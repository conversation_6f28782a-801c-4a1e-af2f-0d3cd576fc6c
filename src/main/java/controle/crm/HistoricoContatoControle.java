package controle.crm;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.SuperControle;
import controle.arquitetura.SuperControleCRM;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import controle.basico.FuncionalidadeControle;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.*;
import negocio.comuns.plano.MarcadorVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.ajax4jsf.event.AjaxEvent;
import org.richfaces.component.html.HtmlDataTable;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.regex.Pattern;

import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.sms.Message;
import servicos.integracao.sms.SmsController;

public class HistoricoContatoControle extends SuperControleCRM {

    protected ContratoVO contratoVigente = new ContratoVO();
    private AgendaVO agendaVO;
    private ClienteVO clienteVO;
    private MalaDiretaVO malaDiretaVO;
    private MalaDiretaEnviadaVO malaDiretaEnviadaVO;
    private HistoricoContatoVO historicoContatoVO;
    private UsuarioVO colaboradorConsultar;
    private Date dataInicio;
    private Date dataTermino;
    private Date dataInicioAgendamento;
    private Date dataTerminoAgendamento;
    private Boolean filtroOperacaoReagendamento;
    private Boolean filtroOperacaoConfirmacao;
    private Boolean filtroOperacaoComparecimento;
    private Boolean filtroOperacaoPessoal;
    private Boolean filtroOperacaoTelefone;
    private Boolean filtroOperacaoWhatsApp;
    private Boolean filtroOperacaoLigacaoSemContato;
    private Boolean filtroOperacaoEmail;
    private Boolean filtroOperacaoSMS;
    private Boolean gerouAgendamento;
    private Boolean filtroResultadoSimplesRegistro;
    private Boolean filtroResultadoObjecao;
    private Boolean filtroResultadoAgLigacao;
    private Boolean filtroResultadoAgVisita;
    private Boolean filtroResultadoAgAula;
    private Boolean filtroResultadoOutros;
    private Date dataConsulta;
    private String consultaSituacao;
    private Integer consultaCategoria;
    private Boolean apresentarDadosTelefonar;
    private String campoConsultarModeloMensagem;
    private String valorConsultarModeloMensagem;
    private List listaConsultarModeloMensagem;
    private List listaEmail;
    private Boolean manterAbertoRichModal;
    private Boolean cancelarRichModal;
    private Boolean cancelarRichModalObjecao;
    private Boolean cancelarRichModalSimplesRegistro;
    private List listaConsultaObjecao;
    private ObjecaoVO objecaoVO;
    private List listaTelefoneIndicado;
    private List listaTelefoneClientePotencial;
    private List listaHistoricoContatoPassivo;
    private List listaHistoricoContatoIndicado;
    private List listaHistoricoContatoCliente;
    private Boolean vindoTelaMetaPerda;
    private boolean verHistoricoContato;
    private List<TelefoneVO> telefonesSMSPassivo;
    private List<HistoricoContatoVO> listaHistoricoContato;
    private String onCompleteBotoes = "";
    private String manterAbertoRichModalPanelAgenda;
    private String paramTipoConsulta;
    private MeioEnvio meioEnvio;
    private boolean tabelaIndicacao;
    private boolean tabelaPassivo;
    private FasesCRMEnum fase;
    private Boolean abrirRealizarContato;
    private Boolean agendamentoVisita;
    private Integer codigoTextoPadrao;
    private HtmlDataTable dataTable;
    private List<SelectItem> listaTextoPadrao;
    private int paginaAtualConsulta = 0;
    private String fasesDoContato = "";
    private Integer tipoMensagemApp = TipoPerguntaEnum.SIMPLES.getCodigo();
    private CamposGenericosTO campos = new CamposGenericosTO();
    private List<SelectItem> listaSelectTiposPerguntas;
    private boolean temUsuarioMovel = false;
    private boolean consultarOrdenacao = true;

    private static final String LISTA_CONTATOS = "LISTA_CONTATOS";
    private static final Integer LISTA_PAGINADA_LIMIT = 10;
    private ListaPaginadaTO listaClienteHistoricoContato;
    private TextoPadraoVO textoPadrao = new TextoPadraoVO();
    private String colunaOrdenar;
    private List<SelectItem> listColunasOrdenar;
    private String direcaoOrdenar;
    private List<SelectItem> listDirecaoOrdenar;
    private String[] faseSelecionadas;
    private List<SelectItem> listaFasesSelecionar;
    private List<SelectItem> listaObjecoesSelecionarDefinitiva;
    private List<SelectItem> listaObjecoesSelecionarDesistencia;
    private List<SelectItem> listaObjecoesSelecionarNaoDefinitiva;
    private Integer[] objecoesSelecionadas;
    private Integer[] objecoesSelecionadasDefinitiva;
    private Integer[] objecoesSelecionadasDesistencia;
    private List<SelectItem> filtroEmpresas = new ArrayList<SelectItem>();
    private int filtroEmpresa;
    private boolean permiteConsultarTodasEmpresas;
    private String onCompleteEdicaoSimplesRegistro = "";
    private HistoricoContatoVO historicoContatoVOEdicao = new HistoricoContatoVO();
    private HistoricoContatoVO historicoContatoVOAntesEdicao = new HistoricoContatoVO();

    /**
     * Interface <code>AgendaInterfaceFacade</code> Responsável pela
     * interconex?o da camada de controle com a camada de neg?cio. Criando uma
     * independ?ncia da camada de controle com rela??o a tenologia de
     * persist?ncia dos dados (DesignPatter: Fa?ade).
     */
    public HistoricoContatoControle() throws Exception {
        try {
            setHistoricoContatoVO(new HistoricoContatoVO());
            setControleConsulta(new ControleConsulta());
            obterUsuarioLogado();
            montarListasOrdenacao();
            inicializarFacades();
        } catch (Exception e) {
            Uteis.logar("Error Historico Contato: " + e, HistoricoContatoControle.class);
        }
    }

    /**
     * Rotina Responsável por disponibilizar um novo objeto da classe
     * <code>Agenda</code> para edi??o pelo Usuário da aplica??o.
     *
     * @throws Exception
     */
    public String novo() throws Exception {
        setControleConsulta(new ControleConsulta());
        setDataConsulta(Calendario.hoje());
        setApresentarDadosTelefonar(false);
        setAgendaVO(new AgendaVO());
        setHistoricoContatoVO(new HistoricoContatoVO());
        setMalaDiretaVO(new MalaDiretaVO());
        setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
        setCampoConsultarModeloMensagem("");
        setValorConsultarModeloMensagem("");
        setManterAbertoRichModal(true);
        setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setCancelarRichModal(false);
        setListaConsultarModeloMensagem(new ArrayList());
        setListaConsultaObjecao(new ArrayList());
        setListaTelefoneClientePotencial(new ArrayList());
        setListaTelefoneIndicado(new ArrayList());
        setListaEmail(new ArrayList());
        setObjecaoVO(new ObjecaoVO());
        inicializarFiltroEmpresas();
        setMsgAlert("");
        setSucesso(true);
        setErro(false);
        setListaTextoPadrao(null);
        setMensagemID("msg_entre_prmconsulta");
        setMensagemDetalhada("");
        return "editar";
    }

    public void inicializarFiltroEmpresas(){
        try {
            if(permissao("ConsultarInfoTodasEmpresas")){
                setPermiteConsultarTodasEmpresas(true);
                setFiltroEmpresa(0);
                setFiltroEmpresas(new ArrayList<SelectItem>());
                getFiltroEmpresas().add(new SelectItem(0, "TODAS"));
                for (EmpresaVO empresa: getFacade().getEmpresa().consultarEmpresas()) {
                    getFiltroEmpresas().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
                }
            }else{
                setFiltroEmpresa(getEmpresaLogado().getCodigo());
                setPermiteConsultarTodasEmpresas(false);
            }
        } catch (Exception e) {
            montarErroComLog(e);
        }
    }

    /**
     * M?todo responsavel por inicializar Dados antes de realizar Contato, seNão causa erro no HistoricoContatoCons
     *
     * <AUTHOR>
     */
    public void inicializarDadosRealizarContato() {
        setListaConsulta(new ArrayList());
        setControleConsulta(new ControleConsulta());
        setDataConsulta(Calendario.hoje());
        setApresentarDadosTelefonar(false);
        setAgendaVO(new AgendaVO());
        setHistoricoContatoVO(new HistoricoContatoVO());
        setMalaDiretaVO(new MalaDiretaVO());
        setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
        setCampoConsultarModeloMensagem("");
        setValorConsultarModeloMensagem("");
        setManterAbertoRichModal(true);
        setCancelarRichModal(false);
        setListaConsultarModeloMensagem(new ArrayList());
        setListaConsultaObjecao(new ArrayList());
        setListaTelefoneClientePotencial(new ArrayList());
        setListaTelefoneIndicado(new ArrayList());
        setListaEmail(new ArrayList());
        setObjecaoVO(new ObjecaoVO());
        setMsgAlert("");
        setMensagemDetalhada("");
        setSucesso(true);
        setErro(false);
        setListaHistoricoContato(new ArrayList<HistoricoContatoVO>());
        setMensagemID("msg_entre_prmconsulta");
        setFaseSelecionadas(new String[FasesCRMEnum.values().length]);
        inicializarFiltroEmpresas();
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            if (getUsuarioLogado().getCodigo() == null || getUsuarioLogado().getCodigo() == 0) {
                throw new ConsistirException("É necessário realizar abertura de meta para realizar esse cadastro.");
            }
            if (getColaboradorResponsavel() == null || getColaboradorResponsavel().getCodigo() == 0) {
                throw new ConsistirException("É necessário realizar abertura de meta para realizar esse cadastro.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Rotina Responsável por disponibilizar os dados de um objeto da classe
     * <code>Agenda</code> para altera??o. O objeto desta classe ?
     * disponibilizado na session da p?gina (request) para que o JSP
     * correspondente possa disponibiliz?-lo para edi??o.
     */
    public String editar() {
        AgendaVO obj = (AgendaVO) context().getExternalContext().getRequestMap().get("agenda");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(new Boolean(false));
        setAgendaVO(obj);
        setMensagemID("msg_dados_editar");
        setMsgAlert("");
        return "editar";
    }

    /**
     * M?todo Responsável inicializar objetos relacionados a classe
     * <code>AgendaVO</code>. Esta inicializa??o ? necess?ria por exig?ncia da
     * tecnologia JSF, que Não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(AgendaVO obj) {
        if (obj.getPassivo() == null) {
            obj.setPassivo(new PassivoVO());
        }
        if (obj.getIndicado() == null) {
            obj.setIndicado(new IndicadoVO());
        }
        if (obj.getColaboradorResponsavel() == null) {
            obj.setColaboradorResponsavel(new UsuarioVO());
        }
        if (obj.getCliente() == null) {
            obj.setCliente(new ClienteVO());
        }
        if (obj.getResponsavelCadastro() == null) {
            obj.setResponsavelCadastro(new UsuarioVO());
        }
    }

    public void inicializarDadosSimplesRegistro() throws Exception {
        if (validarMetaNaoAberta()) {
            setCancelarRichModalSimplesRegistro(false);
            setOnCompleteBotoes("alert('Para realizar contato abra primeiramente a meta de hoje.');");
        } else {
            setCancelarRichModalSimplesRegistro(true);
            setOnCompleteBotoes("Richfaces.showModalPanel('panelSimplesRegistro');");
        }
    }

    public void inicializarDadosAgendamento() throws Exception {
        if (validarMetaNaoAberta()) {
            setOnCompleteBotoes("alert('Para realizar contato abra primeiramente a meta de hoje.');");
        } else {
            setOnCompleteBotoes("Richfaces.showModalPanel('panelAgenda');");

        }
    }

    /**
     * M?todo Responsável por gravar a obje??o depois da consulta
     */
    public void gravarObjecaoHistoricoContato() {
        try {
            ObjecaoVO obj = (ObjecaoVO) context().getExternalContext().getRequestMap().get("objecao");
            getHistoricoContatoVO().setObjecaoVO(obj);
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj().booleanValue()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            getFacade().getHistoricoContato().gravarHistoricoContato("OB", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados_objecao");
            montarMsgAlert(getMensagem());
            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                    obj.setObjetoVOAntesAlteracao(new ObjecaoVO());
                    obj.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo().intValue(), "HISTORICOCONTATO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
                registrarLogObjetoVO(obj, obj.getCodigo().intValue(), "HISTORICOCONTATO - OBJECAO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    /**
     * M?todo Responsável por gravar a obje??o depois da consulta
     */
    public void gravarAgendaHistoricoContato() {
        try {
            setManterAbertoRichModalPanelAgenda("");
            ObjecaoVO obj = (ObjecaoVO) context().getExternalContext().getRequestMap().get("objecao");
            getHistoricoContatoVO().setObjecaoVO(obj);
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Date dataLimite = getFacade().getConfiguracaoSistemaCRM().obterDataCalculadaDiasUteis(Calendario.hoje(), false,
                    configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturo(), getEmpresaLogado());
            if (Calendario.maior(agendaVO.getDataAgendamento(), dataLimite) && (agendaVO.getTipoAgendamento().equals("AE") || agendaVO.getTipoAgendamento().equals("VI"))) {
                throw new ConsistirException("Agendamento com data superior a " + configuracaoSistemaCRMVO.getNrDiasLimiteAgendamentoFuturoDescricao()
                        + " sá pode ser do tipo Ligação. Veja campo 'Número de dias limite para agendamento futuro' nas configurações do CRM.");
            }
            if(!agendaVO.getTipoAgendamento().equals("AE")){
                agendaVO.setModalidade(new ModalidadeVO());
            }
            //LOG - FIM
            if (getEmpresaLogado() != null) {
                agendaVO.setEmpresa(getEmpresaLogado().getCodigo());
            }
            getFacade().getHistoricoContato().gravarHistoricoContato("AG", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
            setManterAbertoRichModal(false);
            setMensagemID("msg_dados_gravados_agenda");
            setSucesso(true);
            setErro(false);
            montarMsgAlert(getMensagem());
            setManterAbertoRichModalPanelAgenda("Richfaces.hideModalPanel('panelAgenda');" + getMsgAlert() + ";recarregarMetas();");
            agendaVO = new AgendaVO();
            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo().intValue(), "HISTORICOCONTATO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            setManterAbertoRichModalPanelAgenda("");
        }

    }

    public void gravarSimplesRegistroHistoricoContato() {
        try {
            if (!getHistoricoContatoVO().getContatoAvulso() && validarMetaNaoAberta()) {
                setMsgAlert("alert('Para realizar contato abra primeiramente a meta de hoje.');");
                return;

            }
            //string numero para o envio de sms
            String numero = null;
            //caso o contato seja realizado via sms
            if (getHistoricoContatoVO().getTipoContato().equals("CS")) {
                getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
                if (getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()) {
                    String mensagem = Uteis.retirarAcentuacaoRegex(getHistoricoContatoVO().getObservacao()).toUpperCase();
                    String verificarTermosSpamNoTitulo = MalaDiretaVO.verificar(getConfiguracaoSistemaCRMVO(), mensagem);
                    if (!verificarTermosSpamNoTitulo.isEmpty()) {
                        setMensagemID("msg_mail_termobloqueado");
                        String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                        limparMsg();
                        throw new Exception(msg);
                    }
                }
                //iterar nos telefones procurando o selecionado
                FOR:
                for (Object obj : getHistoricoContatoVO().getListaTelefoneClientePorTipoContato()) {
                    TelefoneVO tel = (TelefoneVO) obj;
                    //armazenar o selecionado na string
                    if (tel.getSelecionado()) {
                        numero = tel.getNumero();
                        break FOR;
                    }
                }
                //PROCURAR TELEFONE DO PASSIVO
                if (UteisValidacao.emptyString(numero) && !UteisValidacao.emptyNumber(getHistoricoContatoVO().getPassivoVO().getCodigo())) {
                    //iterar nos telefones procurando o selecionado
                    FOR:
                    for (TelefoneVO tel : getTelefonesPassivo()) {
                        //armazenar o selecionado na string
                        if (tel.getSelecionado()) {
                            numero = tel.getNumero();
                            break FOR;
                        }
                    }
                }
                //PROCURAR TELEFONE DO INDICADO
                if (UteisValidacao.emptyString(numero) && !UteisValidacao.emptyNumber(getHistoricoContatoVO().getIndicadoVO().getCodigo())) {
                    //iterar nos telefones procurando o selecionado
                    FOR:
                    for (Object obj : getListaTelefoneIndicado()) {
                        TelefoneVO tel = (TelefoneVO) obj;
                        //armazenar o selecionado na string
                        if (tel.getSelecionado()) {
                            numero = tel.getNumero();
                            break FOR;
                        }
                    }
                }
                //caso nenhum telefone tenha sido escolhido, parar a opera??o.
                if (UteisValidacao.emptyString(numero)) {
                    throw new ValidacaoException("SMS não foi enviado, nenhum celular selecionado");
                }
            }
            ObjecaoVO obj = (ObjecaoVO) context().getExternalContext().getRequestMap().get("objecao");
            getHistoricoContatoVO().setObjecaoVO(obj);

            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            setSucesso(false);
            setErro(true);
            String retorno = "SMS não foi enviado, nenhum celular selecionado";
            String obs = getHistoricoContatoVO().getObservacao().replaceAll(Pattern.quote("<") + "p" + Pattern.quote(">"), "").replaceAll(Pattern.quote("</") + "p" + Pattern.quote(">"), "");

            if (getHistoricoContatoVO().getTipoContato().equals("CS")) {
                if (obs.trim().isEmpty()) {
                    setSucesso(false);
                    setErro(true);
                    throw new Exception("Para o envio de SMS, por favor preencha o campo Observação e tente novamente.");
                }
                montarMailing();
                if (obs.length() > Uteis.TAMANHO_MSG_SMS) {
                    throw new Exception("A mensagem não pode ter mais que 140 caracteres");
                }
                // aqui Não ? necess?rio fazer a verifica??o do n?mero pois j? foi feita acima

                SmsController smsController = new SmsController(getHistoricoContatoVO().getClienteVO().getEmpresa().getTokenSMS()
                        , getKey(), TimeZone.getTimeZone(getHistoricoContatoVO().getPassivoVO().getEmpresaVO().getTimeZoneDefault()));

                if(!UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getCodigo())){
                    retorno = smsController.sendMessage(null, new Message().setNumero(numero).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getClienteVO().getPessoa().getNome())));
                } else if(!UteisValidacao.emptyNumber(historicoContatoVO.getIndicadoVO().getCodigo())){
                    retorno = smsController.sendMessage(null, new Message().setNumero(numero).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getIndicadoVO().getNomeIndicado())));
                } else {
                    retorno = smsController.sendMessage(null, new Message().setNumero(numero).setMsg(ModeloMensagemVO.personalizarTagNomePessoa(obs, historicoContatoVO.getPassivoVO().getNome())));
                }
                if (retorno.toUpperCase().contains("STATUS: OK")) {
                    //LOG - FIM
                    montarMailingAgendamento();
                    malaDiretaVO.setUsuarioVO(getUsuarioLogado());
                    montarHistoricoMailing(malaDiretaVO,retorno);
                    getFacade().getHistoricoContato().gravarHistoricoContato("SR",historicoContatoVO,agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
                    setSucesso(true);
                    setErro(false);
                    setMensagemID("msg_dados_gravados_envioSMS");

                } else {
                    setMensagemID("");
                    setMensagem(retorno);
                }

            } else {
                if (historicoContatoVO.getResponsavelCadastro() == null || UteisValidacao.emptyNumber(historicoContatoVO.getResponsavelCadastro().getCodigo())) {
                    historicoContatoVO.setResponsavelCadastro(getUsuarioLogado());
                }
                getFacade().getHistoricoContato().gravarHistoricoContato("SR", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
                setSucesso(true);
                setErro(false);
                setMensagemID("msg_dados_gravados_simplesRegistro");
            }
            montarMsgAlert(getMensagem());
            if (getMensagem().toUpperCase().contains("SUCESSO")) {
                setMsgAlert(getMsgAlert() + "window.close();");
            }
            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo(), "HISTORICOCONTATO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_naoGravados", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            setMsgAlert(getMsgAlert()
                    + "document.getElementById('form:btnEnviarSMS').style.visibility = 'visible';");
        }

    }
    public void montarMailing() throws Exception{
        MalaDiretaEnviadaVO enviado = new MalaDiretaEnviadaVO();
        enviado.setClienteVO(historicoContatoVO.getClienteVO());
        malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
        malaDiretaVO.getMalaDiretaEnviadaVOs().add(enviado);
        malaDiretaVO.setMensagem(historicoContatoVO.getObservacao().replaceFirst(mensagem, mensagem));
        malaDiretaVO.setUsuarioVO(getUsuarioLogado());
        malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
        malaDiretaVO.setTitulo(Uteis.retirarPontosGraficos("SMS - "+historicoContatoVO.getClienteVO().getPessoa().getNome()));
        malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.SMS);
        malaDiretaVO.setEmpresa(getEmpresaLogado());
        malaDiretaVO.setDataCriacao(Calendario.hoje());
        malaDiretaVO.setDataEnvio(Calendario.hoje());
        malaDiretaVO.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
        getFacade().getMalaDireta().incluir(malaDiretaVO);
    }
    public void montarMailingAgendamento() throws Exception{
        MailingAgendamentoVO agendamentoVO = new MailingAgendamentoVO();
        agendamentoVO.setOcorrencia(OcorrenciaEnum.INSTANTANEO);
        agendamentoVO.setDataInicial(Calendario.hoje());
        agendamentoVO.setMalaDireta(malaDiretaVO.getCodigo());
        agendamentoVO.setUltimaExecucao(Calendario.hoje());
        agendamentoVO.montarCron();
        getFacade().getMailingAgendamento().incluir(agendamentoVO,agendamentoVO.getMalaDireta());
    }
    public void montarHistoricoMailing(MalaDiretaVO historico,String retorno) throws Exception{
        String dadosRetorno[] = retorno.split("-");
        MailingHistoricoVO mailingHistorico = new MailingHistoricoVO();
        mailingHistorico.setDataFim(Calendario.hoje());
        mailingHistorico.setDataInicio(Calendario.hoje());
        mailingHistorico.setFiltro("");
        mailingHistorico.setMalaDireta(historico.getCodigo());
        mailingHistorico.setNrNaoEnviados(0);
        Integer enviados = Integer.parseInt(dadosRetorno[1].replace("enviados:", "").trim());
        Integer saldo = Integer.parseInt(dadosRetorno[2].replace("saldo:", "").trim());
        mailingHistorico.setNrEnviados(enviados);
        mailingHistorico.setSaldo(saldo);
        mailingHistorico.setLog(retorno);
        mailingHistorico.setPessoasAfetadas(1);
        mailingHistorico.setStatus(StatusEnvioMailingEnum.CONCLUIDO);
        getFacade().getMailingHistorico().incluir(mailingHistorico);
    }

    public void gravarEnviandoEmail() {
        try {
            if (validarMetaNaoAberta()) {
                setMsgAlert("alert('Para realizar contato abra primeiramente a meta de hoje.');");
                return;

            }
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
            if (!UteisValidacao.emptyNumber(historicoContatoVO.getClienteVO().getPessoa().getCodigo())) {
                Boolean emailSelecionado = false;
                for (Object obj : historicoContatoVO.getClienteVO().getPessoa().getEmailVOs()) {
                    EmailVO email = (EmailVO) obj;
                    if (email.getEscolhido()) {
                        emailSelecionado = true;
                        break;
                    }
                }
                if (!emailSelecionado) {
                    throw new ValidacaoException("Nenhum e-mail selecionado.");
                }
            }
            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            if (getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()) {
                String verificarTermosSpamNoTitulo = getMalaDiretaVO().verificarTermosSpam(getConfiguracaoSistemaCRMVO());
                if (!verificarTermosSpamNoTitulo.isEmpty()) {
                    setMensagemID("msg_mail_termobloqueado");
                    String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                    limparMsg();
                    throw new Exception(msg);
                }
            }

            getFacade().getHistoricoContato().gravarHistoricoContato("", historicoContatoVO, agendaVO, malaDiretaVO, getEmpresaLogado().getCodigo());
            updateJenkinsService(malaDiretaVO, getConfiguracaoSistemaCRMVO());
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_email_enviado");
            montarMsgAlert(getMensagem());

            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo(), "HISTORICOCONTATO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_enviar_emailErro");
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }

    }
    public void consultaPaginadoOrdenacao(AjaxEvent ajaxEvent){
        //setConfPaginacao(new ConfPaginacao());
        //getConfPaginacao().setPaginaAtual(0);
        // getConfPaginacao().setItensPorPagina(10);
        // getConfPaginacao().setPagNavegacao("pagiInicial");
        //     this.setParamTipoConsulta("detalhada");
        if(consultarOrdenacao) {
            consultarPaginado();
        }else{
            consultarOrdenacao = true;
        }
    }
    public void consultarHistorico() {
        try {
            if (getColaboradorConsultar().getNome().equals("")) {
                colaboradorConsultar = new UsuarioVO();
            }
            setListaHistoricoContato(getFacade().getHistoricoContato().consultarPorFiltrosTelaConsulta(colaboradorConsultar, dataInicio, dataTermino, Arrays.asList(faseSelecionadas), filtroOperacaoReagendamento, filtroOperacaoConfirmacao, filtroOperacaoComparecimento, filtroOperacaoPessoal, filtroOperacaoTelefone, filtroOperacaoLigacaoSemContato, filtroOperacaoEmail, filtroOperacaoSMS, false, Uteis.NIVELMONTARDADOS_TELACONSULTA, getEmpresaLogado()));
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * Metodo listener que obtem os parametros necessarios para realizar
     * a paginacao e filtros da tela
     * <p/>
     * * Autora: Carla
     * Criado em 14/01/2011
     */
    public void consultarPaginadoListenerHistoricoContato(ActionEvent evt) {
        //==================================================================================================================================


        //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao());
            }
        }

        //Obtendo qual pagina dever? ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        //==================================================================================================================================

        Object compTipoConsulta = evt.getComponent().getAttributes().get("tipoConsulta");
        if (compTipoConsulta != null && !"".equals(compTipoConsulta.toString())) {
            if (!compTipoConsulta.toString().equals(this.paramTipoConsulta)) {
                setConfPaginacao(new ConfPaginacao());
            }
            this.setParamTipoConsulta(compTipoConsulta.toString());
        }
        consultarPaginado();
    }

    public void validarDados() throws Exception {
        //validar intervalo de datas
        //sem datas
        if (dataInicio == null && dataTermino == null && dataInicioAgendamento == null && dataTerminoAgendamento == null) {
            throw new Exception("Informe pelo menos uma data");
        }
        //sem data de inicio
        if (dataInicio != null && dataTermino == null) {
            throw new Exception("Informe a data de término");
        }
        //sem data de termino
        if (dataTermino != null && dataInicio == null) {
            throw new Exception("Informe a data de início");
        }
        //data de inicio maior que data de termino
        if (dataInicio != null && dataTermino.before(dataInicio)) {
            throw new Exception("A data de início deve ser menor que a data de término");
        }
        //sem data de inicio agendamento
        if (dataInicioAgendamento != null && dataTerminoAgendamento == null) {
            throw new Exception("Informe a data de término agendamento");
        }
        //sem data de termino agendamento
        if (dataTerminoAgendamento != null && dataInicioAgendamento == null) {
            throw new Exception("Informe a data de início agendamento");
        }
        //data de inicio maior que data de termino agendamento
        if (dataInicioAgendamento != null && dataTerminoAgendamento.before(dataInicioAgendamento)) {
            throw new Exception("A data de início do agendamento deve ser menor que a data de término do agendamento");
        }
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco
     * <p/>
     * * Autora: Carla
     * Criado em 14/01/2011
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginado() {
        try {
            validarDados();
            super.consultar();
            List objs = new ArrayList();
            HistoricoContatoFiltroVO filtro = getHistoricoContatoFiltroVO();

            objs = getFacade().getHistoricoContato().consultarPaginado(filtro, getConfPaginacao());
            setListaHistoricoContato(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);

            this.getConfPaginacao().definirVisibilidadeLinksNavegacao();

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public Long getTamanhoListaConsulta() {
        return (long) getListaHistoricoContato().size();
    }

    public void consultarModeloMensagem() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarModeloMensagem().equals("codigo")) {
                if (getValorConsultarModeloMensagem().equals("")) {
                    setValorConsultarModeloMensagem("0");
                }
                Integer valorInt = Integer.parseInt(getValorConsultarModeloMensagem());
                objs = getFacade().getModeloMensagem().consultarPorCodigo(valorInt, meioEnvio, false, Uteis.NIVELMONTARDADOS_TODOS);
            } else if (getCampoConsultarModeloMensagem().equals("titulo")) {
                objs = getFacade().getModeloMensagem().consultarPorTitulo(getValorConsultarModeloMensagem(), meioEnvio, false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarModeloMensagem(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarModeloMensagem(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarObjecao() {
        try {
            if (validarMetaNaoAberta()) {
                setOnCompleteBotoes("alert('Para realizar contato abra primeiramente a meta de hoje.');");
            } else {
                super.consultar();
                setListaConsultaObjecao(getFacade().getObjecao().consultarObjecao(getVindoTelaMetaPerda()));
                setCancelarRichModalObjecao(true);
                setMensagemID("msg_dados_consultados");
                setOnCompleteBotoes("Richfaces.showModalPanel('panelObjecao')");
            }
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setOnCompleteBotoes("alert('" + e.getMessage() + "');");
        }
    }

    private boolean filtraSituacao(ClienteVO cli) {
        if (getConsultaSituacao() == null) {
            return true;
        }
        if (getConsultaSituacao().trim().isEmpty()) {
            return true;
        }
        Iterator i = cli.getClienteSituacaoVOs().iterator();
        while (i.hasNext()) {
            ClienteSituacaoVO situacao = (ClienteSituacaoVO) i.next();
            if (situacao.getSituacao().equals(getConsultaSituacao()) || situacao.getSubordinadaSituacao().equals(getConsultaSituacao())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Opera??o Responsável por processar a exclus?o um objeto da classe
     * <code>AgendaVO</code> Ap?s a exclus?o ela automaticamente aciona a rotina
     * para uma nova inclus?o.
     */
    public String excluir() {
        try {
            getFacade().getAgenda().excluir(agendaVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }
    /*
     * M?todo usado para inicializar o cliente no ZillyonWeb
     */

    public void inicializarContatoAvulso() throws Exception {
        inicializarCliente(true);
    }

    public void inicializarCliente(boolean contatoAvulso) throws Exception {
        try{
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setAbrirRealizarContato(true);
            setVindoTelaMetaPerda(null);
            novo();
            getHistoricoContatoVO().setContatoAvulso(contatoAvulso);
            Date data = Calendario.hoje();
            ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
            }
            clienteControle.pegarClienteTelaCliente();
            clienteVO = clienteControle.getClienteVO();
            inicializarCliente(data, clienteVO);
            selecionarObjetivoFase();
        } catch (Exception e) {
            setAbrirRealizarContato(false);
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void abrirContatoDoEstudio(ActionEvent event) {
        try {
            setMsgAlert("");
            novo();
            Date data = Calendario.hoje();
            Integer codigoCliente = Integer.valueOf(event.getComponent().getAttributes().get("codigoCliente").toString());
            if (UteisValidacao.emptyNumber(codigoCliente)) {
                montarMsgAlert("Não foi possível encontrar o cliente.");
            } else {
                ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_TODOS);
                inicializarCliente(data, cliente);
                setMsgAlert("abrirPopup('" + JSFUtilities.getRequest().getContextPath() + "/faces/realizarContatoForm.jsp', 'RealizarContatoform', 630, 630);");
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }

    }

    /**
     * <AUTHOR> Alcides
     * 21/06/2013
     */
    public void inicializarCliente(Date data, ClienteVO cliente) throws Exception {
        getHistoricoContatoVO().setClienteVO(cliente);
        if (!isVerHistoricoContato()) {
            if (!getHistoricoContatoVO().getContatoAvulso() && validarMetaNaoAberta()) { // contato avulso Não necessita de meta
                setSucesso(false);
                setErro(true);
                setAbrirRealizarContato(false);
                montarMsgAlert("Para realizar contato abra primeiramente a meta de hoje.");
                setMensagemDetalhada("msg_erro", "Para realizar contato abra primeiramente a meta de hoje.");
                throw new Exception("Para realizar contato abra primeiramente a meta de hoje.");
            }
        }
        setAberturaMeta(getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), data, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getHistoricoContatoVO().setResponsavelCadastro(getUsuarioLogado());
        getHistoricoContatoVO().setColaboradorResponsavel(getUsuarioLogado());
        getHistoricoContatoVO().setAberturaMetaEstaEmAberta(getAberturaMeta().getMetaEmAberto());
        getHistoricoContatoVO().setDiaAbertura(getAberturaMeta().getDia());
        getHistoricoContatoVO().setTipoContato("TE");
        getHistoricoContatoVO().setCodigoFecharMetaDetalhado(getFacade().getFecharMetaDetalhado().consultarPorCodigoClienteComBaseEmHistoriContato(cliente.getCodigo(), false));
        getFacade().getHistoricoContato().inicializarDadosHistoricoContatoCliente(getHistoricoContatoVO());
        getMalaDiretaVO().setRemetente(getUsuarioLogado());
        getMalaDiretaEnviadaVO().setClienteVO(cliente);
        setContratoVigente(getFacade().getContrato().consultarContratoVigentePorPessoa(
                cliente.getPessoa().getCodigo(), false,false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        try {
            setAgendaVO(getFacade().getAgenda().consultarPorCodigoCliente(cliente.getCodigo(), data, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            setAgendaVO(new AgendaVO());
        }

        setClienteVO(cliente);
        setListaEmail(cliente.getPessoa().getEmailVOs());
        setSucesso(false);
        setErro(false);
        setMensagemID("");
        setMensagem("");
    }

    private boolean validarMetaNaoAberta() throws Exception {
        return validarCfgValidacaoContato() &&
                !getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(),
                        getEmpresaLogado().getCodigo(), Calendario.hoje());
    }

    private boolean validarCfgValidacaoContato() throws Exception {
        return getFacade().getConfiguracaoSistema().verificarValidacaoContatoMeta();
    }

    public void inicializarHistoricoContato() throws Exception {
        //flag usado para nao mostrar mensagens referentes a realizar contato

        setVerHistoricoContato(true);
        inicializarCliente(false);
        //j? seta valor falso caso desejar abrir realizar contato
        setVerHistoricoContato(false);
        consultarHistoricoPassivoIndicadoCliente();
    }

    public void inicializarNovaLigacao() throws Exception {
        inicializarContatoAvulso();
        this.getHistoricoContatoVO().setTipoContato("TE");
    }

    private String situacaoConsulta(String situacao) {
        if (situacao == null) {
            return "";
        }
        if (situacao.equals("DE")) {
            return "IN";
        }
        if (situacao.equals("CA")) {
            return "IN";
        }
        if (situacao.equals("AV")) {
            return "AT";
        }
        if (situacao.equals("VE")) {
            return "IN";
        } else {
            return situacao;
        }
    }

    public void obterSituacaoClientePorCLienteEspecifico(ClienteVO obj) throws Exception {
        try {
            obj.setClienteSituacaoVOs(new ArrayList());
            List listaContrato = getFacade().getContrato().consultarPorCodigoPessoa(obj.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (obj.getSituacao().equals("VI")) {
                validarSituacaoVisitanteEspecifico(obj);
                return;
            }
            if (obj.getSituacao().equals("AT")) {
                validarSituacaoAtivoEspecifico(obj, listaContrato);
                return;
            }
            if (obj.getSituacao().equals("TR")) {
                validarSituacaoTrancadoEspecifico(obj, listaContrato);
                return;
            }
            if (obj.getSituacao().equals("IN")) {
                validarSituacaoInativoEspecifico(obj, listaContrato);
                return;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoVisitanteEspecifico(ClienteVO obj) throws Exception {
        try {
            String situacao = obj.obterSituacaoClienteVisitante(Conexao.getFromSession());
            if (situacao.equals("VI")) {
                preencherSituacaoClienteEspecifico(obj, "VI", "");
                return;
            } else if (!situacao.equals("")) {
                preencherSituacaoClienteEspecifico(obj, "VI", situacao);
                return;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoAtivoEspecifico(ClienteVO obj, List listaContrato) throws Exception {
        try {
            Iterator i = listaContrato.iterator();
            while (i.hasNext()) {
                ContratoVO contrato = (ContratoVO) i.next();
                String situacao = getFacade().getZWFacade().obterSituacaoClienteAtivo(contrato, obj);
                if (situacao.equals("NO")) {
                    preencherSituacaoClienteEspecifico(obj, "AT", "NO");
                    return;
                } else if (!situacao.equals("")) {
                    preencherSituacaoClienteEspecifico(obj, "AT", situacao);
                    return;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSituacaoTrancadoEspecifico(ClienteVO obj, List listaContrato) throws Exception {
        try {
            Iterator i = listaContrato.iterator();
            while (i.hasNext()) {
                ContratoVO contrato = (ContratoVO) i.next();
                // if ((Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(),
                // contrato.getVigenciaDe()) >= 0) &&
                // (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(),
                // contrato.getVigenciaAteAjustada()) <= 0)) {
                String situacao = getFacade().getZWFacade().obterSituacaoClienteTrancado(contrato, obj);
                if (situacao.equals("TR")) {
                    preencherSituacaoClienteEspecifico(obj, "TR", "");
                    return;
                } else if (!situacao.equals("")) {
                    preencherSituacaoClienteEspecifico(obj, "TR", situacao);
                    return;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void preencherSituacaoClienteEspecifico(ClienteVO obj, String situacao, String subordinadaSituacao) {
        ClienteSituacaoVO situacaoCliente = new ClienteSituacaoVO();
        situacaoCliente.setClienteVO(obj);
        situacaoCliente.setSituacao(situacao);
        situacaoCliente.setSubordinadaSituacao(subordinadaSituacao);
        obj.getClienteSituacaoVOs().add(situacaoCliente);
    }

    public void validarSituacaoInativoEspecifico(ClienteVO obj, List listaContrato) throws Exception {
        try {

            Iterator i = listaContrato.iterator();
            while (i.hasNext()) {
                ContratoVO contrato = (ContratoVO) i.next();
                // if ((Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(),
                // contrato.getVigenciaDe()) >= 0) &&
                // (Uteis.getCompareData(negocio.comuns.utilitarias.Calendario.hoje(),
                // contrato.getVigenciaAteAjustada()) <= 0)) {
                String situacao = getFacade().getZWFacade().obterSituacaoClienteInativo(contrato, obj);
                if (situacao.equals("")) {
                    preencherSituacaoClienteEspecifico(obj, "IN", "");
                    return;
                } else if (!situacao.equals("")) {
                    preencherSituacaoClienteEspecifico(obj, "IN", situacao);
                    return;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public List getTipoConsultaCombo() throws Exception {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("cfp", "CPF"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        // itens.add(new SelectItem("grauInstrucao", "Grau de Instru?ao"));
        itens.add(new SelectItem("situacao", "Situação"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("nomeCategoria", "Categoria"));
        if (getUsuarioLogado().getAdministrador().equals(true)) {
            itens.add(new SelectItem("empresa", "Empresa"));
        }
        // itens.add(new SelectItem("codAcesso", "Código de Acesso"));

        return itens;
    }

    public List getTipoConsultaComboObjecao() throws Exception {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("grupo", "Grupo"));
        return itens;
    }

    /**
     * M?todo Responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoAgendamento</code>
     *
     * @return
     * @throws Exception
     */
    public List getListaSelectItemTipoAgendamentoAgenda() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoAgendamentos = (Hashtable) Dominios.getTipoAgendamento();
        Enumeration keys = tipoAgendamentos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoAgendamentos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemTipoAgendamentoReagendamento() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoAgendamentos = (Hashtable) Dominios.getTipoAgendamento();
        Enumeration keys = tipoAgendamentos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoAgendamentos.get(value);
            if(!value.equals("LI")){
                objs.add(new SelectItem(value, label));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /*
     * M?todo Responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>faseAtual</code>
     */
    public List<SelectItem> getListaSelectItemFaseAtual() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (FasesCRMEnum fase : FasesCRMEnum.values()) {
            String value = fase.getSigla();
            String label = fase.getDescricao();
            String objetivo = fase.getObjetivo();
            if (!objetivo.isEmpty()) {
                objs.add(new SelectItem(value, label));
            }
        }
        return objs;
    }

    /*
     * M?todo Responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo Horas Esse m?todo cria uma lista de
     * hora em hora para o usuario selecionar a hora que ele quer.
     */
    public List getListaSelectItemHoras() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable horas = (Hashtable) Dominios.getHoras();
        Enumeration keys = horas.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) horas.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List<SelectItem> getListaGrauSatisfacao() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (GrauSatisfacaoEnum grau : GrauSatisfacaoEnum.values()) {
            objs.add(new SelectItem(grau.getCodigo(), grau.getDescricao()));
        }
        return objs;
    }

    /*
     * M?todo Responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo Minutos Esse m?todo cria uma lista de
     * minuto em minuto para o usuario selecionar o minuto que ele quer.
     */
    public List getListaSelectItemMinutos() throws Exception {
        List objs = new ArrayList();
        Hashtable minutos = (Hashtable) Dominios.getMinutos();
        Enumeration keys = minutos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) minutos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * M?todo Responsável por limpar os campos do AgendaVO e a observa??o do
     * historicoContatoVO no momento em que a pessoa alterar a op??o da combo
     * Situação Contato
     */
    public void limparCamposAgendaEHistoricoContato() {
        setAgendaVO(new AgendaVO());
        historicoContatoVO.setObservacao("");
        historicoContatoVO.setObjecaoVO(new ObjecaoVO());
        historicoContatoVO.setAgendaVO(new AgendaVO());
    }

    public void limparCampoResponsavelCadastro() {
        this.getAgendaVO().setResponsavelCadastro(new UsuarioVO());
    }

    /**
     * Rotina Responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboResponsavelCadastro() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomePessoa", "Pessoa"));
        itens.add(new SelectItem("situacao", "Situação"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("nomeCategoria", "Categoria"));
        itens.add(new SelectItem("codAcesso", "Código de Acesso"));
        itens.add(new SelectItem("banco", "Banco"));
        itens.add(new SelectItem("agencia", "Agência"));
        itens.add(new SelectItem("conta", "Conta"));
        return itens;
    }

    /**
     * Rotina Responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultaComboCliente() throws Exception {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("cpf", "CPF"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        // itens.add(new SelectItem("grauInstrucao", "Grau de Instru?ao"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("empresa", "Empresa"));
        itens.add(new SelectItem("codPessoa", "Cod.Pessoa"));
        itens.add(new SelectItem("indicacao", "Indica??o"));
        itens.add(new SelectItem("passivo", "Passivo"));
        itens.add(new SelectItem("email", "Email"));
        // itens.add(new SelectItem("codAcesso", "Código de Acesso"));
        return itens;
    }

    public List getTipoConsultarComboModeloMensagem() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Codigo"));
        itens.add(new SelectItem("titulo", "Título"));
        return itens;
    }

    public void limparCampoColaborador() {
        this.getAgendaVO().setColaboradorResponsavel(new UsuarioVO());
    }

    public void limparCampoColaboradorSuggestion() {
        this.setColaboradorConsultar(new UsuarioVO());
        this.getColaboradorConsultar().setNome("");
    }

    /**
     * Rotina Responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboColaboradorResponsavel() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomePessoa", "Pessoa"));
        itens.add(new SelectItem("situacao", "Situação"));
        itens.add(new SelectItem("codAcesso", "Código Acesso"));
        return itens;
    }

    /**
     * Rotina Responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboIndicado() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomeIndicado", "Nome Indicado"));
        itens.add(new SelectItem("codigo", "Código"));
        // itens.add(new SelectItem("dia", "Dia"));
        itens.add(new SelectItem("situacaoCliente", "Cliente"));
        return itens;
    }

    public void limparCampoPassivo() {
        this.getAgendaVO().setPassivo(new PassivoVO());
    }

    /**
     * Rotina Responsável por preencher a combo de consulta dos RichModal da
     * telas.
     */
    public List getTipoConsultarComboPassivo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("situacaoColaborador", "Usuário Responsável Cadastro"));
        itens.add(new SelectItem("dia", "Dia"));
        itens.add(new SelectItem("situacaoColaborador", "Colaborador"));
        return itens;
    }

    /**
     * Rotina Responsável por atribui um javascript com o m?todo de mascara para
     * campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina Responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaComboAgenda() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("dia", "Dia"));
        itens.add(new SelectItem("nomePassivo", "Cliente Potencial"));
        itens.add(new SelectItem("nomeIndicadoIndicacao", "Cliente Indicado"));
        itens.add(new SelectItem("situacaoColaborador", "Colaborador Responsável"));
        itens.add(new SelectItem("situacaoCliente", "Cliente"));
        itens.add(new SelectItem("tipoAgendamento", "Tipo Agendamento"));
        itens.add(new SelectItem("modalidade", "Modalidade"));
        itens.add(new SelectItem("responsavelCadastro", "Responsável Cadastro"));
        return itens;
    }

    public List getListaSelectItemSituacaoCliente() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoClientes = (Hashtable) Dominios.getSituacaoCliente();
        Enumeration keys = situacaoClientes.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoClientes.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Rotina Responsável por organizar a pagina??o entre as p?ginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("msg_entre_prmconsulta");
        return "editar";
    }

    /**
     * Opera??o que libera todos os recursos (atributos, listas, objetos) do
     * backing bean. Garantindo uma melhor atua??o do Garbage Coletor do Java. A
     * mesma ? automaticamente quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        agendaVO = null;
    }

    /**
     * M?todo responsavel por criar o autocomplete do suggestionBox no campo
     * Modalidade
     *
     * @return
     */
    public List<ModalidadeVO> autocompleteModalidade(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ModalidadeVO> result = new ArrayList<ModalidadeVO>();
        try {
            result = (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarPorNomeModalidadeComLimite(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception ex) {
            result = (new ArrayList<ModalidadeVO>());
        }
        return result;
    }

    /**
     * Opera??o que inicializa as Interfaces Fa?ades com os respectivos objetos
     * de persist?ncia dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    /**
     * M?todo Responsável por criar o dominio TipoContato(Telefone / Email)
     *
     * @return
     * @throws Exception
     */
    public List getListaSelectItemTipoContato() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoContato = (Hashtable) Dominios.getTipoContatoHistoricoContato();
        Enumeration keys = tipoContato.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoContato.get(value);
            objs.add(new SelectItem(value, label));
        }
        return objs;
    }

    public List getListaSelectItemTipoContatoReagendamento() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoContato = (Hashtable) Dominios.getTipoContatoHistoricoContato();
        Enumeration keys = tipoContato.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoContato.get(value);
            if(!value.equals("EM") && !value.equals("CS") ){
                objs.add(new SelectItem(value, label));
            }
        }
        return objs;
    }


    /**
     * M?todo Responsável por criar o dominio TipoContato(Telefone / Email)
     *
     * @return
     * @throws Exception
     */
    public List getListaSelectItemTipoOperacao() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoOperacao = (Hashtable) Dominios.getTipoOperacaoHistoricoContato();
        Enumeration keys = tipoOperacao.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoOperacao.get(value);
            objs.add(new SelectItem(value, label));
        }
        return objs;
    }

    /**
     * M?todo Responsável por fechar o RichModal no momento em que ele clicar no
     * bot?o cancelar do rich:Modal= PanelAgenda
     *
     * @return
     */
    public String getFecharRichModalPanelAgenda() {
        if (getCancelarRichModal()) {
            return "Richfaces.hideModalPanel('panelAgenda')";
        }
        return "";
    }

    public String getFecharRichModalPanelObjecao() {
        if (getCancelarRichModalObjecao()) {
            return "Richfaces.hideModalPanel('panelObjecao');" + getMsgAlert();
        }
        return "";
    }

    public String getFecharRichModalPanelSimplesRegistro() {
        if (getCancelarRichModalSimplesRegistro()) {
            return "Richfaces.hideModalPanel('panelSimplesRegistro');" + getMsgAlert();
        }
        return "";
    }

    public Boolean getApresentarCalendarDia() {
        return getControleConsulta().getCampoConsulta().equals("dia");
    }

    public Boolean getApresentarBotaoObjecao() {
        return !historicoContatoVO.getTipoOperacao().equals("RE")
                && !getHistoricoContatoVO().getTipoContato().equals("AP")
                && !getHistoricoContatoVO().getTipoContato().equals("CS");
    }

    public Boolean getApresentarBotaoSimplesRegistro() {
        return !historicoContatoVO.getTipoOperacao().equals("RE")
                && getHistoricoContatoVO().getPassivoVO().getCodigo() == 0
                && !getHistoricoContatoVO().getTipoContato().equals("AP")
                && !getHistoricoContatoVO().getTipoContato().equals("CS");
    }

    public Boolean getApresentarSMS() {
        return !historicoContatoVO.getTipoOperacao().equals("RE") && getHistoricoContatoVO().getTipoContato().equals("CS");
    }

    public Boolean getApresentarAPP() {
        return !historicoContatoVO.getTipoOperacao().equals("RE") && getHistoricoContatoVO().getTipoContato().equals("AP");
    }

    /*
     * M?todo responsavel por apresentar o input do tipoAgendamento se o
     * tipoAgendamento for Aula Experimental apresentar o input na tela
     */
    public Boolean getApresentarInputTextAulaExperimental() {
        return getAgendaVO().getTipoAgendamento().equals("AE");
    }

    /**
     * M?todo responsavel por definir qual tela sera mostrada: se a tela de
     * agendamento por telefone ou a tela de agendamento de Email. Nesse caso se
     * for por Email ele ira apresentar a tela de enviar Email
     *
     * @return
     */
    public Boolean getApresentarEmail() {
        return getHistoricoContatoVO().getTipoContato().equals("EM") && !getHistoricoContatoVO().getClienteVO().getPessoa().getEmailVOs().isEmpty();
    }

    /**
     * M?todo responsavel por definir qual tela sera mostrada: se a tela de
     * agendamento por telefone ou a tela de agendamento de Email. Nesse caso se
     * for por telefone ele ira apresentar a tela de agendamento por telefone
     *
     * @return
     */
    public Boolean getApresentarTelefoneCliente() {
        return (historicoContatoVO.getTipoContato().equals("TE")
                || historicoContatoVO.getTipoContato().equals("RE")
                || historicoContatoVO.getTipoContato().equals("CO")
                || historicoContatoVO.getTipoContato().equals("LC")
                || historicoContatoVO.getTipoContato().equals("CS"))
                && historicoContatoVO.getClienteVO().getCodigo() != 0;
    }

    public Boolean getApresentarComentarioTelefoneCliente() {
        return historicoContatoVO.getTipoContato().equals("TE")
                && historicoContatoVO.getClienteVO().getCodigo() != 0
                && historicoContatoVO.getClienteVO().getPessoa().getTelefoneVOs().isEmpty();
    }

    public Boolean getApresentarDadosCabecalhoCliente() {
        return getHistoricoContatoVO() != null && getHistoricoContatoVO().getClienteVO().getCodigo() != 0;
    }

    public Boolean getApresentarComentarioNaoPossueEmail() {
        if (historicoContatoVO.getTipoContato().equals("EM")) {
            if (historicoContatoVO.getClienteVO().getCodigo() != 0 && historicoContatoVO.getClienteVO().getPessoa().getEmailVOs().isEmpty()) {
                return true;
            } else if (historicoContatoVO.getPassivoVO().getCodigo() != 0 && historicoContatoVO.getPassivoVO().getEmail().equals("")) {
                return true;
            } else if (historicoContatoVO.getIndicadoVO().getCodigo() != 0 && historicoContatoVO.getIndicadoVO().getEmail().equals("")) {
                return true;
            }
        }
        return false;
    }

    // ********************** Controle de Tela para o Passivo na tela de
    // Realizar Contato **********************
    public Boolean getApresentarDadosCabecalhoPassivo() {
        return getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && getHistoricoContatoVO().getIndicadoVO().getCodigo() == 0;
    }

    public Boolean getApresentarTelefonePassivo() {
        return getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && !getListaTelefoneClientePotencial().isEmpty() && !historicoContatoVO.getTipoContato().equals("EM") && !historicoContatoVO.getTipoContato().equals("PE");
    }

    public Boolean getApresentarComentarioTelefonePassivo() {
        return getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && getListaTelefoneClientePotencial().isEmpty() && historicoContatoVO.getTipoContato().equals("TE");
    }

    public Boolean getApresentarEmailPassivo() {
        return getHistoricoContatoVO().getTipoContato().equals("EM") && !getHistoricoContatoVO().getPassivoVO().getEmail().equals("") && getHistoricoContatoVO().getClienteVO().getCodigo() == 0;
    }

    // ********************** Controle de Tela para o Indicado na tela de
    // Realizar Contato **********************
    public String getValidarQualPopUpAbrirHistoricoContato() {
        if (getHistoricoContatoVO().getPassivoVO().getCodigo() != 0) {
            return "abrirPopup('faces/historicoContatoPassivoForm.jsp', 'HistoricoContatoPassivo', 512, 530);";
        } else if (getHistoricoContatoVO().getIndicadoVO().getCodigo() != 0) {
            return "abrirPopup('faces/historicoContatoIndicadoForm.jsp', 'HistoricoContatoIndicado', 512, 530);";
        } else {
            return "abrirPopup('faces/historicoContatoClienteForm.jsp', 'HistoricoContatoCliente', 512, 530);";
        }
    }

    /**
     * <AUTHOR>
     * 11/10/2011
     */
    public void abrirPopUp() throws Exception {
        consultarOrdenacao = false;
        novo();
        HistoricoContatoVO obj = (HistoricoContatoVO) context().getExternalContext().getRequestMap().get("historicoContato");
        setAberturaMeta(getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), Calendario.hoje(),
                getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setHistoricoContatoVO(getFacade().getHistoricoContato().inicializarDadosPessoasHistoricoContatoRealizacaoContato(obj, agendaVO,
                getUsuarioLogado(), getColaboradorResponsavel(), getMalaDiretaVO(), getMalaDiretaEnviadaVO(),
                getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false, getAberturaMeta()));
        consultarHistoricoContato();
    }

    public void abrirPopUpHistorico() throws Exception {
        setHistoricoContatoVO(new HistoricoContatoVO());
        ClienteControle clienteControle = (ClienteControle) JSFUtilities.getFromSession(ClienteControle.class.getSimpleName());
        getHistoricoContatoVO().setClienteVO(clienteControle.getClienteVO());
        inicializarHistoricoContato();
        consultarHistoricoLigacoes();
    }

    /**
     * <AUTHOR>
     * 11/10/2011
     */
    public String getAbrirPopUpAdequada() throws Exception {
        if (getHistoricoContatoVO().getPassivoVO().getCodigo() != 0) {
            return "abrirPopup('faces/historicoContatoPassivoForm.jsp', 'HistoricoContatoPassivo', 512, 530);";
        } else if (getHistoricoContatoVO().getIndicadoVO().getCodigo() != 0) {
            return "abrirPopup('faces/historicoContatoIndicadoForm.jsp', 'HistoricoContatoIndicado', 512, 530);";
        } else if (getHistoricoContatoVO().getClienteVO().getCodigo() != 0) {
            return "abrirPopup('faces/historicoContatoClienteForm.jsp', 'HistoricoContatoCliente', 512, 530);";
        }
        return "";
    }

    public Boolean getApresentarDadosCabecalhoIndicado() {
        return getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && getHistoricoContatoVO().getPassivoVO().getCodigo() == 0;
    }

    public Boolean getApresentarTelefoneIndicado() {
        return getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && getHistoricoContatoVO().getPassivoVO().getCodigo() == 0 && !getListaTelefoneIndicado().isEmpty() && !historicoContatoVO.getTipoContato().equals("EM") && !historicoContatoVO.getTipoContato().equals("PE");
    }

    public Boolean getApresentarComentarioTelefoneIndicado() {
        return getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && getHistoricoContatoVO().getPassivoVO().getCodigo() == 0 && getListaTelefoneIndicado().isEmpty() && historicoContatoVO.getTipoContato().equals("TE");
    }

    public Boolean getApresentarEmailIndicado() {
        return getHistoricoContatoVO().getTipoContato().equals("EM") && !getHistoricoContatoVO().getIndicadoVO().getEmail().equals("") && getHistoricoContatoVO().getClienteVO().getCodigo() == 0 && getHistoricoContatoVO().getPassivoVO().getCodigo() == 0;
    }

    /**
     * M?todo responsavel por cancelar o agendamento se a pessoa Não quizer mais
     * agendar alguem ele vai cancelar limpando assim o objeto AgendaVO
     */
    public void cancelarAgendamentoPanelAgenda() {
        setAgendaVO(new AgendaVO());
        historicoContatoVO.setAgendaVO(new AgendaVO());
        setCancelarRichModal(true);
        setMensagemID("msg_erro_naoGravados");
        setMensagemDetalhada("");
        setMensagem("");
    }

    public void cancelarObjecaoPanelObjecao() {
        setMsgAlert(null);
        setCancelarRichModalObjecao(true);
        setMensagemID("msg_erro_naoGravados");
        setMensagemDetalhada("");
        setMensagem("");
    }

    public void cancelarSimplesRegistroPanelSimplesRegistro() {
        setMsgAlert(null);
        setCancelarRichModalSimplesRegistro(true);
        setMensagemID("msg_erro_naoGravados");
        setMensagemDetalhada("");
        setMensagem("");
    }

    public List<UsuarioVO> autocompleteRemetente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
        }
        return result;
    }

    public List<PessoaVO> autocompletePessoa(Object suggest) {
        String pref = (String) suggest;
        ArrayList<PessoaVO> result = new ArrayList<PessoaVO>();
        try {
            result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPorNomePessoaComLimite(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
        }
        return result;
    }

    public void limparCampoModeloMensagem() {
        this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        this.getMalaDiretaVO().setMensagem("");
    }

    public void adicionarMalaDiretaEnviada() throws Exception {
        try {
            if (!malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome().equals("")) {
                malaDiretaEnviadaVO.getClienteVO().setPessoa(getFacade().getPessoa().consultarPorNomePessoa(malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
            }
            if (!getMalaDiretaVO().getCodigo().equals(new Integer(0))) {
                malaDiretaEnviadaVO.setMalaDiretaVO(getMalaDiretaVO());
            }
            if (getMalaDiretaEnviadaVO().getClienteVO().getPessoa().getCodigo().intValue() != 0) {
                Integer campoConsulta = getMalaDiretaEnviadaVO().getClienteVO().getPessoa().getCodigo();
                PessoaVO pessoa = getFacade().getPessoa().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getMalaDiretaEnviadaVO().getClienteVO().setPessoa(pessoa);
            }
            getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
            this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarModeloMensagem() throws Exception {
        ModeloMensagemVO obj = (ModeloMensagemVO) context().getExternalContext().getRequestMap().get("modeloMensagem");

        if (obj.getMeioDeEnvio().equals(MeioEnvio.SMS.getCodigo()) || obj.getMeioDeEnvio().equals(MeioEnvio.APP.getCodigo())) {
            this.getMalaDiretaVO().setModeloMensagem(obj);
            getHistoricoContatoVO().setObservacao(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
        } else {
            obj.verificarSeExisteImagemModelo(false, getKey());
            if (getMensagemDetalhada().equals("")) {
                this.getMalaDiretaVO().setModeloMensagem(obj);
                this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
            }
        }

        Uteis.liberarListaMemoria(this.getListaConsultarModeloMensagem());
        this.setValorConsultarModeloMensagem(null);
        this.setCampoConsultarModeloMensagem(null);
        this.setListaConsultarModeloMensagem(new ArrayList());
    }

    /**
     * M?todo responsavel por selecionar o Cliente depois da consulta para o
     * agendamento. * @return
     *
     * @throws Exception
     */
    public void selecionarClienteParaAgendamento() throws Exception {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            historicoContatoVO.setContatoAvulso(true);
            ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
            obj.setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            obj.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            historicoContatoVO.setResponsavelCadastro(getUsuarioLogado());
            historicoContatoVO.setColaboradorResponsavel(getColaboradorResponsavel());
            historicoContatoVO.setAberturaMetaEstaEmAberta(getAberturaMeta().getMetaEmAberto());
            historicoContatoVO.setDiaAbertura(getAberturaMeta().getDia());
            historicoContatoVO.setClienteVO(obj);
            historicoContatoVO.setTipoContato("TE");
            historicoContatoVO.setCodigoFecharMetaDetalhado(getFacade().getFecharMetaDetalhado().consultarPorCodigoClienteComBaseEmHistoriContato(historicoContatoVO.getClienteVO().getCodigo(), false));
            getFacade().getHistoricoContato().inicializarDadosHistoricoContatoCliente(historicoContatoVO);
            malaDiretaVO.setRemetente(getUsuarioLogado());
            malaDiretaEnviadaVO.getClienteVO().getPessoa().setNome(historicoContatoVO.getClienteVO().getPessoa().getNome());
            setListaEmail(obj.getPessoa().getEmailVOs());
            setContratoVigente(getFacade().getContrato().consultarContratoVigentePorPessoa(obj.getPessoa().getCodigo(), false,false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            selecionarObjetivoFase();
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    public void selecionarIndicacaoParaAgendamento() throws Exception {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            IndicadoVO indicacao = (IndicadoVO) context().getExternalContext().getRequestMap().get("indicacao");
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();
            fecharMetaDetalhadoVO.setIndicado(indicacao);
            getFacade().getHistoricoContato().inicializarDadosIndicadoRealizacaoContato(historicoContatoVO, agendaVO, fecharMetaDetalhadoVO, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, new ArrayList<TelefoneVO>(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    public void selecionarPassivoParaAgendamento() throws Exception {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            PassivoVO passivo = (PassivoVO) context().getExternalContext().getRequestMap().get("passivo");
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO fecharMetaDetalhadoVO = new FecharMetaDetalhadoVO();
            fecharMetaDetalhadoVO.setPassivo(passivo);
            getFacade().getHistoricoContato().inicializarDadosPassivoRealizacaoContato(historicoContatoVO, agendaVO, fecharMetaDetalhadoVO, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, new ArrayList<TelefoneVO>(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o passivo para realizar um agendamento
     * e chamar o metodo que preenche os dados do passivo para apresentar na
     * tela de realizarContatoForm.
     */
    public void selecionarPassivoRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            setListaHistoricoContatoPassivo(new ArrayList());
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosPassivoRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o indicado para realizar um agendamento
     * e chamar o metodo que preenche os dados do indicado para apresentar na
     * tela de realizarContatoForm.
     */
    public void selecionarIndicadoRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosIndicadoRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o passivo para realizar um agendamento
     * e chamar o metodo que preenche os dados do passivo para apresentar na
     * tela de realizarContatoForm.
     */
    public void selecionarAgendadosRealizacaoContato(ActionEvent evt) {
        try {
            String fase =  (String) evt.getComponent().getAttributes().get("faseCRM");
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosAgendadosRealizacaoContato(historicoContatoVO, obj.getAgenda(), obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            historicoContatoVO.setFase(fase);
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o passivo para realizar um agendamento
     * e chamar o metodo que preenche os dados do passivo para apresentar na
     * tela de realizarContatoForm.
     */
    public void executarSelecaoPessoasRealizacaoContatoVindoTelaHistoricoContatoCons() {
        try {
            novo();
            HistoricoContatoVO obj = (HistoricoContatoVO) context().getExternalContext().getRequestMap().get("historicoContato");
            setHistoricoContatoVO(getFacade().getHistoricoContato().inicializarDadosPessoasHistoricoContatoRealizacaoContato(obj, agendaVO, getUsuarioLogado(), getColaboradorResponsavel(), getMalaDiretaVO(), getMalaDiretaEnviadaVO(), getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false, getAberturaMeta()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o vinte Quatro Horas para realizar um
     * agendamento e chamar o metodo que preenche os dados do vinte Quatro Horas
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarVinteQuatroHorasRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosVinteQuatroHorasRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o vinte Quatro Horas para realizar um
     * agendamento e chamar o metodo que preenche os dados do vinte Quatro Horas
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarVendasRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosVendasRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o Faturamento para realizar um
     * agendamento e chamar o metodo que preenche os dados do Faturamento
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarFaturamentoRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosFaturamentoRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o Faltas para realizar um
     * agendamento e chamar o metodo que preenche os dados do vinte Quatro Horas
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarFaltasRealizacaoContato() {
        try {
            setMsgAlert("");
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);

            getFacade().getHistoricoContato().consultarTotalLigacaoEDiasUltAcesso(obj.getHistoricoContatoVO());

            AcessoClienteVO ultimoAcesso = getFacade().getAcessoCliente().consultarUltimoAcesso(obj.getCliente(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (getFacade().getFecharMeta().metaAbertaFechamentaDetalhado(obj.getCodigo()) && Uteis.nrDiasEntreDatas(ultimoAcesso.getDataHoraEntrada(), Calendario.hoje()) == 0) {
                getFacade().getFecharMetaDetalhado().excluir(obj);
                FecharMetaVO meta = getFacade().getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.FALTOSOS.getSigla(), Calendario.hoje(), getColaboradorResponsavel().getCodigo(), false, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_ABERTURAMETA);
                meta.setMeta((double) meta.getFecharMetaDetalhadoVOs().size());
                meta.setFecharMetaDetalhadoVOs(new ArrayList<FecharMetaDetalhadoVO>());
                getFacade().getFecharMeta().alteraSemSubordinada(meta);
                throw new Exception("O aluno teve acesso hoje na academia. Sua meta será recalculada, por favor atualize.");
            } else {
                validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
                setAbrirRealizarContato(true);
                getFacade().getHistoricoContato().inicializarDadosFaltasRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
                setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
                selecionarObjetivoFase();
            }
        } catch (Exception e) {
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
            setMsgAlert(getMsgAlert() + ";recarregarMetas();");
        }
    }

    /**
     * M?todo responsavel por selecionar o vinte Quatro Horas para realizar um
     * agendamento e chamar o metodo que preenche os dados do vinte Quatro Horas
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarPerdaRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosPerdaRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
            if(getFase().equals(FasesCRMEnum.DESISTENTES)){
                setVindoTelaMetaPerda(true);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    public void selecionarVencidosRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(true);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosVencidosRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar o GrupoRisco para realizar um
     * agendamento e chamar o metodo que preenche os dados do GrupoRisco
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarGrupoRiscoRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosGrupoRiscoRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar a meta Pos Vendapara realizar um
     * agendamento e chamar o metodo que preenche os dados do PosVenda
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarPosVendaRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosPosVendaRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setAbrirRealizarContato(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * M?todo responsavel por selecionar a meta Renovacao para realizar um
     * agendamento e chamar o metodo que preenche os dados da Renovaca
     * para apresentar na tela de realizarContatoForm.
     */
    public void selecionarRenovacaoRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosRenovacaoRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    public void selecionarAniversarianteRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            setVindoTelaMetaPerda(false);
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosAniversarianteRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    private void preencherFasesDoContato(FecharMetaDetalhadoVO obj) throws Exception {
        StringBuilder sb = new StringBuilder("");
        setFasesDoContato("");
        List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarPorClienteAberturaMeta(obj.getCliente().getCodigo(), obj.getFecharMeta().getAberturaMetaVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (FecharMetaDetalhadoVO fmd : lista) {
            try {
                sb.append(fmd.getFecharMeta().getFase().getDescricao()).append(", ");
            }catch(Exception erro){

            }
        }
        if (sb.length() > 1) {
            sb.deleteCharAt(sb.length() - 1);
            sb.deleteCharAt(sb.length() - 1);
        }

        if (lista.size() > 1) {
            setFasesDoContato(sb.toString());
        }
    }

    public boolean isApresentarFasesDoContato() {
        return !getFasesDoContato().isEmpty();
    }

    public void executarReagendamento(ActionEvent evt) {
        try {
            String fase =  (String) evt.getComponent().getAttributes().get("faseCRM");
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            novo();
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            agendamentoVisita = false;
            if(obj.getAgenda().getTipoAgendamento().equals("LI")){
                agendamentoVisita = true;
            }
            setHistoricoContatoVO(getFacade().getHistoricoContato().inicializarDadosAgendadosRealizacaoContato(getHistoricoContatoVO(), getAgendaVO(), obj, getUsuarioLogado(), getColaboradorResponsavel(), getMalaDiretaVO(), getMalaDiretaEnviadaVO(), getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), true));
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            getHistoricoContatoVO().setFase(fase);

            setMsgAlert("Richfaces.showModalPanel('panelAgenda')");
        } catch (Exception e) {
            setMensagemDetalhada("", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * Metodo responsavel por preencher a lista do suggestion box do usuario
     * colaborador
     *
     * @param suggest
     * @return
     */
    public List<UsuarioVO> autocompleteUsuarioColaborador(Object suggest) {
        String pref = (String) suggest;
        try {
            return (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioSomenteColaborador(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        } catch (Exception ex) {
            return new ArrayList<UsuarioVO>();
        }
    }

    public void setarColaboradorEscolhido() {
        try {
            UsuarioVO usuarioVO = (UsuarioVO) context().getExternalContext().getRequestMap().get("result");
            setColaboradorConsultar(usuarioVO);
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        getHistoricoContatoVO().getClienteVO().getPessoa().setFoto(
                getFacade().getPessoa().obterFoto(getKey(),
                        getHistoricoContatoVO().getClienteVO().getPessoa().getCodigo()));
        SuperControle.paintFoto(out, getHistoricoContatoVO().getClienteVO().getPessoa().getFoto());
    }

    public String getPaintFotoDaNuvem() {
        return getPaintFotoDaNuvem(getHistoricoContatoVO().getClienteVO().getPessoa().getFotoKey());
    }

    /**
     * ************ M?todos do HistoricoContatoForm *************
     */
    public void consultarHistoricoPassivoIndicadoCliente() {
        try {
            inicializarPaginacao();
            if (getHistoricoContatoVO().getPassivoVO().getCodigo() != 0) {
                setListaHistoricoContatoPassivo(getFacade().getHistoricoContato().consultarHistoricoContatoPassivo(getHistoricoContatoVO().getPassivoVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
            } else if (getHistoricoContatoVO().getIndicadoVO().getCodigo() != 0) {
                setListaHistoricoContatoIndicado(getFacade().getHistoricoContato().consultarHistoricoContatoIndicado(getHistoricoContatoVO().getIndicadoVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            } else if(getHistoricoContatoVO().getClienteVO().getCodigo() != 0) {
                setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(getHistoricoContatoVO().getClienteVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE,
                        listaClienteHistoricoContato.getLimit(),listaClienteHistoricoContato.getOffset()));
                listaClienteHistoricoContato.setCount(getFacade().getHistoricoContato().countHistoricoContatoClient(getHistoricoContatoVO().getClienteVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE));
                //System.out.println("Tamanho da Listaas: " + getListaHistoricoContatoCliente().size());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*
    ***************** Metodo Criado para demonstrar o historio das
    * pessoas que receberam liga??o da academia, demonstrando itens com mais
    * detalhes do cliente.
    * */
    public void consultarHistoricoContato() {
        try {
            inicializarPaginacao();
            if (getHistoricoContatoVO().getPassivoVO().getCodigo() != 0) {
                setListaHistoricoContatoPassivo(getFacade().getHistoricoContato().consultarHistoricoContatoPassivo(getHistoricoContatoVO().getPassivoVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
            } else if (getHistoricoContatoVO().getIndicadoVO().getCodigo() != 0) {
                setListaHistoricoContatoIndicado(getFacade().getHistoricoContato().consultarHistoricoContatoIndicado(getHistoricoContatoVO().getIndicadoVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            }
            //Na verdade o cliente pode ter  historico de indicado e passivo também, então o histórico como cliente deve ser exibido independentemente
            if (!UteisValidacao.emptyNumber(getHistoricoContatoVO().getClienteVO().getCodigo())) {
                consultarHistoricoLigacoes();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void consultarHistoricoLigacoes() {
        try {
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarLigacoesCliente(getHistoricoContatoVO().getClienteVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Passivo no momento que clicar na aba
     * M?s de Passivo
     */
    public void consultarHistoricoPassivoVindoTelaMetaPassivoDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoPassivo(getFacade().getHistoricoContato().consultarHistoricoContatoPassivo(obj.getPassivo().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
            if (!getListaHistoricoContatoPassivo().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoPassivo().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosPassivo(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setPassivoVO(obj.getPassivo());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Agendados no momento que clicar na aba
     * M?s de Agendados
     */
    public void consultarHistoricoAgendadosVindoTelaMetaAgendadosDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            if (obj.getCliente().getCodigo() != 0) {
                setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
                if (!getListaHistoricoContatoCliente().isEmpty()) {
                    setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                    getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
                }
                getHistoricoContatoVO().setClienteVO(obj.getCliente());
            } else if (obj.getPassivo().getCodigo() != 0) {
                consultarHistoricoPassivoVindoTelaMetaPassivoDetalhada();
            } else if (obj.getIndicado().getCodigo() != 0) {
                consultarHistoricoIndicadoVindoTelaMetaIndicadoDetalhada();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Indicado no momento que clicar na aba
     * M?s de Indicado
     */
    public void consultarHistoricoIndicadoVindoTelaMetaIndicadoDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoIndicado(getFacade().getHistoricoContato().consultarHistoricoContatoIndicado(obj.getIndicado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoIndicado().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoIndicado().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosIndicado(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setIndicadoVO(obj.getIndicado());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de VinteQuatroHoras no momento que clicar na aba
     * M?s de VinteQuatroHoras
     */
    public void consultarHistoricoVinteQuatroHorasVindoTelaMetaVinteQuatroHorasDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de VinteQuatroHoras no momento que clicar na aba
     * M?s de VinteQuatroHoras
     */
    public void consultarHistoricoVendasVindoTelaMetaVendasDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Faturamento no momento que clicar na aba
     * M?s de Faturamento
     */
    public void consultarHistoricoFaturamentoVindoTelaMetaFaturamentoDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Faltas no momento que clicar na aba
     * M?s de VinteQuatroHoras
     */
    public void consultarHistoricoFaltasVindoTelaMetaFaltasDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Perda no momento que clicar na aba
     * M?s de Perda
     */
    public void consultarHistoricoPerdaVindoTelaMetaPerdaDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de GrupoRisco no momento que clicar na aba
     * M?s de GrupoRisco
     */
    public void consultarHistoricoGrupoRiscoVindoTelaMetaGrupoRiscoDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de PosVenda no momento que clicar na aba
     * M?s de PosVenda
     */
    public void consultarHistoricoPosVendaVindoTelaMetaPosVendaDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Renova??o no momento que clicar na aba
     * M?s de Renova??o
     */
    public void consultarHistoricoRenovacaoVindoTelaMetaRenovacaoDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * M?todo responsavel por consultar a meta de Aniversariante no momento que clicar na aba
     * M?s de Aniversariante
     */
    public void consultarHistoricoAniversarianteVindoTelaMetaAniversarianteDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarInsercaoTag() {
        try {
            MarcadorVO marcador = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorEmail");
            getMalaDiretaVO().setMensagem(MalaDireta.executarInsercaoTag(getMalaDiretaVO().getMensagem(), marcador.getTag(), false));
            setMensagemID("msg_dados_gravados");
            setMensagemDetalhada("");
            setMensagem("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    // ********************************* REGRAS E-MAIL COLETIVO
    // *************************************
    public List<MarcadorVO> getListaSelectItemMarcadoEmail() throws Exception {
        List<MarcadorVO> objs = new ArrayList<MarcadorVO>();
        MarcadorVO marcador = new MarcadorVO();
        for (MarcadoresEmailEnum mEE : MarcadoresEmailEnum.values()) {
            marcador.setTag(mEE.getTag());
            marcador.setNome(mEE.getDescricao());
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        return objs;
    }

    public AgendaVO getAgendaVO() {
        if (agendaVO == null) {
            agendaVO = new AgendaVO();
        }
        return agendaVO;
    }

    public void setAgendaVO(AgendaVO agendaVO) {
        this.agendaVO = agendaVO;
    }

    /**
     * @return the dataConsulta
     */
    public Date getDataConsulta() {
        if (dataConsulta == null) {
            dataConsulta = Calendario.hoje();
        }
        return dataConsulta;
    }

    /**
     * @param dataConsulta the dataConsulta to set
     */
    public void setDataConsulta(Date dataConsulta) {
        this.dataConsulta = dataConsulta;
    }

    /**
     * @return the consultaSituacao
     */
    public String getConsultaSituacao() {
        if (consultaSituacao == null) {
            consultaSituacao = "";
        }
        return consultaSituacao;
    }

    /**
     * @param consultaSituacao the consultaSituacao to set
     */
    public void setConsultaSituacao(String consultaSituacao) {
        this.consultaSituacao = consultaSituacao;
    }

    /**
     * @return the consultaCategoria
     */
    public Integer getConsultaCategoria() {
        if (consultaCategoria == null) {
            consultaCategoria = new Integer(0);
        }
        return consultaCategoria;
    }

    /**
     * @param consultaCategoria the consultaCategoria to set
     */
    public void setConsultaCategoria(Integer consultaCategoria) {
        this.consultaCategoria = consultaCategoria;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the apresentarDadosTelefonar
     */
    public Boolean getApresentarDadosTelefonar() {
        if (apresentarDadosTelefonar == null) {
            apresentarDadosTelefonar = new Boolean(false);
        }
        return apresentarDadosTelefonar;
    }

    /**
     * @param apresentarDadosTelefonar the apresentarDadosTelefonar to set
     */
    public void setApresentarDadosTelefonar(Boolean apresentarDadosTelefonar) {
        this.apresentarDadosTelefonar = apresentarDadosTelefonar;
    }

    /**
     * @return the historicoContatoVO
     */
    public HistoricoContatoVO getHistoricoContatoVO() {
        if (historicoContatoVO == null) {
            historicoContatoVO = new HistoricoContatoVO();
        }
        return historicoContatoVO;
    }
    public String getRotuloResponsavel(){
        int size = getListaResponsaveis().size();
        if(size > 1){
            return "Responsáveis pelo Aluno";
        }else if(size == 1){
            return "Responsável pelo Aluno";
        }
        return "";
    }
    public List<String> getListaResponsaveis(){
        List<String> lista = new ArrayList<String>();
        if(historicoContatoVO.getClienteVO().getPessoa()!=null) {

            PessoaVO pessoa = historicoContatoVO.getClienteVO().getPessoa();

            if (!pessoa.getNomePai().equals("")) {
                lista.add(pessoa.getNomePai());
            }
            if (!pessoa.getNomeMae().equals("")) {
                lista.add(pessoa.getNomeMae());
            }
        }
        return lista;
    }
    public Boolean getAlunoMenorIdade(){

        return historicoContatoVO.getIdade() < 18
                && (!historicoContatoVO.getClienteVO().getPessoa().getNomePai().equals("")
                || !historicoContatoVO.getClienteVO().getPessoa().getNomeMae().equals(""));


    }

    /**
     * @param historicoContatoVO the historicoContatoVO to set
     */
    public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
        this.historicoContatoVO = historicoContatoVO;
    }

    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public MalaDiretaEnviadaVO getMalaDiretaEnviadaVO() {
        if (malaDiretaEnviadaVO == null) {
            malaDiretaEnviadaVO = new MalaDiretaEnviadaVO();
        }
        return malaDiretaEnviadaVO;
    }

    public void setMalaDiretaEnviadaVO(MalaDiretaEnviadaVO malaDiretaEnviadaVO) {
        this.malaDiretaEnviadaVO = malaDiretaEnviadaVO;
    }

    /**
     * @return the colaboradorConsultar
     */
    public UsuarioVO getColaboradorConsultar() {
        if (colaboradorConsultar == null) {
            colaboradorConsultar = new UsuarioVO();
        }
        return colaboradorConsultar;
    }

    /**
     * @param colaboradorConsultar the colaboradorConsultar to set
     */
    public void setColaboradorConsultar(UsuarioVO colaboradorConsultar) {
        this.colaboradorConsultar = colaboradorConsultar;
    }

    public String getCampoConsultarModeloMensagem() {
        if (campoConsultarModeloMensagem == null) {
            campoConsultarModeloMensagem = "";
        }
        return campoConsultarModeloMensagem;
    }

    public void setCampoConsultarModeloMensagem(String campoConsultarModeloMensagem) {
        this.campoConsultarModeloMensagem = campoConsultarModeloMensagem;
    }

    public String getValorConsultarModeloMensagem() {
        if (valorConsultarModeloMensagem == null) {
            valorConsultarModeloMensagem = "";
        }
        return valorConsultarModeloMensagem;
    }

    public void setValorConsultarModeloMensagem(String valorConsultarModeloMensagem) {
        this.valorConsultarModeloMensagem = valorConsultarModeloMensagem;
    }

    public List getListaConsultarModeloMensagem() {
        if (listaConsultarModeloMensagem == null) {
            listaConsultarModeloMensagem = new ArrayList();
        }
        return listaConsultarModeloMensagem;
    }

    public void setListaConsultarModeloMensagem(List listaConsultarModeloMensagem) {
        this.listaConsultarModeloMensagem = listaConsultarModeloMensagem;
    }

    public List getListaEmail() {
        if (listaEmail == null) {
            listaEmail = new ArrayList();
        }
        return listaEmail;
    }

    public void setListaEmail(List listaEmail) {
        this.listaEmail = listaEmail;
    }

    public Boolean getManterAbertoRichModal() {
        if (manterAbertoRichModal == null) {
            manterAbertoRichModal = new Boolean(false);
        }
        return manterAbertoRichModal;
    }

    public void setManterAbertoRichModal(Boolean manterAbertoRichModal) {
        this.manterAbertoRichModal = manterAbertoRichModal;
    }

    public Boolean getCancelarRichModal() {
        if (cancelarRichModal == null) {
            cancelarRichModal = new Boolean(false);
        }
        return cancelarRichModal;
    }

    public void setCancelarRichModal(Boolean cancelarRichModal) {
        this.cancelarRichModal = cancelarRichModal;
    }

    public List getListaConsultaObjecao() {
        if (listaConsultaObjecao == null) {
            listaConsultaObjecao = new ArrayList();
        }
        return listaConsultaObjecao;
    }

    public void setListaConsultaObjecao(List listaConsultaObjecao) {
        this.listaConsultaObjecao = listaConsultaObjecao;
    }

    public ObjecaoVO getObjecaoVO() {
        if (objecaoVO == null) {
            objecaoVO = new ObjecaoVO();
        }
        return objecaoVO;
    }

    public void setObjecaoVO(ObjecaoVO objecaoVO) {
        this.objecaoVO = objecaoVO;
    }

    public List getListaTelefoneClientePotencial() {
        if (listaTelefoneClientePotencial == null) {
            listaTelefoneClientePotencial = new ArrayList();
        }
        return listaTelefoneClientePotencial;
    }

    public void setListaTelefoneClientePotencial(List listaTelefoneClientePotencial) {
        this.listaTelefoneClientePotencial = listaTelefoneClientePotencial;
    }

    public List getListaHistoricoContatoPassivo() {
        if (listaHistoricoContatoPassivo == null) {
            listaHistoricoContatoPassivo = new ArrayList();
        }
        return listaHistoricoContatoPassivo;
    }

    public void setListaHistoricoContatoPassivo(List listaHistoricoContatoPassivo) {
        this.listaHistoricoContatoPassivo = listaHistoricoContatoPassivo;
    }

    /**
     * @return the filtroOperacaoReagendamento
     */
    public Boolean getFiltroOperacaoReagendamento() {
        if (filtroOperacaoReagendamento == null) {
            filtroOperacaoReagendamento = false;
        }
        return filtroOperacaoReagendamento;
    }

    /**
     * @param filtroOperacaoReagendamento the filtroOperacaoReagendamento to set
     */
    public void setFiltroOperacaoReagendamento(Boolean filtroOperacaoReagendamento) {
        this.filtroOperacaoReagendamento = filtroOperacaoReagendamento;
    }

    /**
     * @return the filtroOperacaoConfirmacao
     */
    public Boolean getFiltroOperacaoConfirmacao() {
        if (filtroOperacaoConfirmacao == null) {
            filtroOperacaoConfirmacao = false;
        }
        return filtroOperacaoConfirmacao;
    }

    /**
     * @param filtroOperacaoConfirmacao the filtroOperacaoConfirmacao to set
     */
    public void setFiltroOperacaoConfirmacao(Boolean filtroOperacaoConfirmacao) {
        this.filtroOperacaoConfirmacao = filtroOperacaoConfirmacao;
    }

    /**
     * @return the filtroOperacaoComparecimento
     */
    public Boolean getFiltroOperacaoComparecimento() {
        if (filtroOperacaoComparecimento == null) {
            filtroOperacaoComparecimento = false;
        }
        return filtroOperacaoComparecimento;
    }

    /**
     * @param filtroOperacaoComparecimento the filtroOperacaoComparecimento to set
     */
    public void setFiltroOperacaoComparecimento(Boolean filtroOperacaoComparecimento) {
        this.filtroOperacaoComparecimento = filtroOperacaoComparecimento;
    }

    /**
     * @return the filtroOperacaoPessoa
     */
    public Boolean getFiltroOperacaoPessoal() {
        if (filtroOperacaoPessoal == null) {
            filtroOperacaoPessoal = false;
        }
        return filtroOperacaoPessoal;
    }

    /**
     * @param filtroOperacaoPessoal the filtroOperacaoPessoa to set
     */
    public void setFiltroOperacaoPessoal(Boolean filtroOperacaoPessoal) {
        this.filtroOperacaoPessoal = filtroOperacaoPessoal;
    }

    /**
     * @return the filtroOperacaoTelefone
     */
    public Boolean getFiltroOperacaoTelefone() {
        if (filtroOperacaoTelefone == null) {
            filtroOperacaoTelefone = false;
        }
        return filtroOperacaoTelefone;
    }

    /**
     * @param filtroOperacaoTelefone the filtroOperacaoTelefone to set
     */
    public void setFiltroOperacaoTelefone(Boolean filtroOperacaoTelefone) {
        this.filtroOperacaoTelefone = filtroOperacaoTelefone;
    }

    /**
     * @return the filtroOperacaoWhatsApp
     */
    public Boolean getFiltroOperacaoWhatsApp() {
        if (filtroOperacaoWhatsApp == null) {
            filtroOperacaoWhatsApp = false;
        }
        return filtroOperacaoWhatsApp;
    }

    /**
     * @param filtroOperacaoWhatsApp the filtroOperacaoWhatsApp to set
     */
    public void setFiltroOperacaoWhatsApp(Boolean filtroOperacaoWhatsApp) {
        this.filtroOperacaoWhatsApp = filtroOperacaoWhatsApp;
    }

    /**
     * @return the filtroOperacaoLigacaoSemContato
     */
    public Boolean getFiltroOperacaoLigacaoSemContato() {
        if (filtroOperacaoLigacaoSemContato == null) {
            filtroOperacaoLigacaoSemContato = false;
        }
        return filtroOperacaoLigacaoSemContato;
    }

    /**
     * @param filtroOperacaoLigacaoSemContato the filtroOperacaoLigacaoSemContato to set
     */
    public void setFiltroOperacaoLigacaoSemContato(Boolean filtroOperacaoLigacaoSemContato) {
        this.filtroOperacaoLigacaoSemContato = filtroOperacaoLigacaoSemContato;
    }

    /**
     * @return the filtroOperacaoEmail
     */
    public Boolean getFiltroOperacaoEmail() {
        if (filtroOperacaoEmail == null) {
            filtroOperacaoEmail = false;
        }
        return filtroOperacaoEmail;
    }

    /**
     * @param filtroOperacaoEmail the filtroOperacaoEmail to set
     */
    public void setFiltroOperacaoEmail(Boolean filtroOperacaoEmail) {
        this.filtroOperacaoEmail = filtroOperacaoEmail;
    }

    /**
     * @return the filtroOperacaoSMS
     */
    public Boolean getFiltroOperacaoSMS() {
        if (filtroOperacaoSMS == null) {
            filtroOperacaoSMS = false;
        }
        return filtroOperacaoSMS;
    }

    /**
     * @param filtroOperacaoSMS the filtroOperacaoSMS to set
     */
    public void setFiltroOperacaoSMS(Boolean filtroOperacaoSMS) {
        this.filtroOperacaoSMS = filtroOperacaoSMS;
    }

    /**
     * @return the dataInicio
     */
    public Date getDataInicio() {
        return dataInicio;
    }

    /**
     * @param dataInicio the dataInicio to set
     */
    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    /**
     * @return the dataTermino
     */
    public Date getDataTermino() {
        return dataTermino;
    }

    /**
     * @param dataTermino the dataTermino to set
     */
    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public List getListaHistoricoContatoIndicado() {
        if (listaHistoricoContatoIndicado == null) {
            listaHistoricoContatoIndicado = new ArrayList();
        }
        return listaHistoricoContatoIndicado;
    }

    public void setListaHistoricoContatoIndicado(List listaHistoricoContatoIndicado) {
        this.listaHistoricoContatoIndicado = listaHistoricoContatoIndicado;
    }

    public List getListaTelefoneIndicado() {
        if (listaTelefoneIndicado == null) {
            listaTelefoneIndicado = new ArrayList();
        }
        return listaTelefoneIndicado;
    }

    public void setListaTelefoneIndicado(List listaTelefoneIndicado) {
        this.listaTelefoneIndicado = listaTelefoneIndicado;
    }

    public List getListaHistoricoContatoCliente() {
        if (listaHistoricoContatoCliente == null) {
            listaHistoricoContatoCliente = new ArrayList();
        }
        return listaHistoricoContatoCliente;
    }

    public void setListaHistoricoContatoCliente(List listaHistoricoContatoCliente) {
        this.listaHistoricoContatoCliente = listaHistoricoContatoCliente;
    }

    // carlos
    public Boolean getCancelarRichModalObjecao() {
        if (cancelarRichModalObjecao == null) {
            cancelarRichModalObjecao = new Boolean(false);
        }
        return cancelarRichModalObjecao;
    }

    public void setCancelarRichModalObjecao(Boolean cancelarRichModalObjecao) {
        this.cancelarRichModalObjecao = cancelarRichModalObjecao;
    }

    public Boolean getCancelarRichModalSimplesRegistro() {
        if (cancelarRichModalSimplesRegistro == null) {
            cancelarRichModalSimplesRegistro = new Boolean(false);
        }
        return cancelarRichModalSimplesRegistro;
    }

    public void setCancelarRichModalSimplesRegistro(Boolean cancelarRichModalSimplesRegistro) {
        this.cancelarRichModalSimplesRegistro = cancelarRichModalSimplesRegistro;
    }

    public Boolean getVindoTelaMetaPerda() {
        return vindoTelaMetaPerda;
    }

    public void setVindoTelaMetaPerda(Boolean vindoTelaMetaPerda) {
        this.vindoTelaMetaPerda = vindoTelaMetaPerda;
    }

    public void adicionarMensagemErro() {
        setMensagemDetalhada("msg_erro_naoGravados");
    }
    // pedro

    public void addURLParaRenovacaoRematricula() throws Exception {
        String url = Uteis.getURLAplicacaoRequest() + "/temp.jsp?";
        String parametros = "validade=" + Uteis.somarDias(Calendario.hoje(), 5).getTime()
                + "&contrato=" + getContratoVigente().getCodigo();
        parametros = Criptografia.encrypt(parametros, SuperControle.Crypt_KEY,
                SuperControle.Crypt_ALGORITM);

        getMalaDiretaVO().setMensagem(MalaDireta.executarInsercaoTag(
                getMalaDiretaVO().getMensagem(), url + parametros, false));


    }

    public ContratoVO getContratoVigente() {
        return contratoVigente;
    }

    public void setContratoVigente(ContratoVO contratoVigente) {
        this.contratoVigente = contratoVigente;
    }

    /**
     * @return the verHistoricoContato
     */
    public boolean isVerHistoricoContato() {
        return verHistoricoContato;
    }

    /**
     * @param verHistoricoContato the verHistoricoContato to set
     */
    public void setVerHistoricoContato(boolean verHistoricoContato) {
        this.verHistoricoContato = verHistoricoContato;
    }

    /**
     * Metodo listener responsavel por setar na lista da view o resultado
     * de uma consulta paginada em banco de acordo com os filtros passados
     * <p/>
     * Autor: Carla Pereira
     * Criado em 03/03/2011
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginadoListener(ActionEvent evt) {
        try {
            //Obtendo qual pagina dever? ser exibida
            Object component = evt.getComponent().getAttributes().get("pagNavegacao");
            if (component != null && !"".equals(component.toString())) {
                getConfPaginacao().setPagNavegacao(component.toString());
            }

            Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
            if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
                if (compPaginaInicial.toString().equals("paginaInicial")) {
                    setConfPaginacao(new ConfPaginacao());
                }
            }
            super.consultar();
            List objs = new ArrayList();
            ClienteFiltroVO filtro = new ClienteFiltroVO();

            if (getConsultaSituacao() == null) {
                setConsultaSituacao("");
            }

            //============================================================================================

            filtro.setControlarAcesso(true);
            filtro.setNivelMontarDados(Uteis.NIVELMONTARDADOS_TELACONSULTA);
            String situacao = getConsultaSituacao() == null ? "" : getConsultaSituacao();

            filtro.setEmpresaVO(new EmpresaVO());
            filtro.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {

                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setCodigo(valorInt);
                filtro.getClienteVO().setSituacao(situacao);
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");

            } else if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {

                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome(getControleConsulta().getValorConsulta().trim());
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");

            } else if (getControleConsulta().getCampoConsulta().equals("codPessoa")) {

                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setCodigo(valorInt);
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");


            } else if (getControleConsulta().getCampoConsulta().equals("empresa")) {

                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.getEmpresaVO().setNome(this.getControleConsulta().getValorConsulta().trim());

            } else if (getControleConsulta().getCampoConsulta().equals("cpf")) {

                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setCfp(this.getControleConsulta().getValorConsulta().trim());
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");

            } else if (getControleConsulta().getCampoConsulta().equals("descricaoProfissao")) {

                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                filtro.setProfissaoVO(new ProfissaoVO());
                filtro.getProfissaoVO().setDescricao(getControleConsulta().getValorConsulta().trim());
                filtro.setPessoaVO(new PessoaVO());
                filtro.getPessoaVO().setNome("sem valor");
                filtro.getPessoaVO().setCfp("sem valor");
                filtro.getPessoaVO().setRg("sem valor");
                filtro.getPessoaVO().setRne("sem valor");
                filtro.getPessoaVO().setPassaporte("sem valor");

            } else if (getControleConsulta().getCampoConsulta().equals("matricula")) {

                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.getClienteVO().setSituacao(situacao);
                int valorInt = 0;
                if (!getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta().trim());
                }
                filtro.getClienteVO().setCodigoMatricula(valorInt);

            } else if (getControleConsulta().getCampoConsulta().equals(
                    "email")) {
                filtro.setSituacaoClienteSinteticoDW(new SituacaoClienteSinteticoDW());
                filtro.setClienteVO(new ClienteVO());
                filtro.setContratoVO(null);
                filtro.setEmailVO(new EmailVO());

                String endEmail = getControleConsulta().getValorConsulta().trim();


                filtro.getEmailVO().setEmail(endEmail);

                filtro.getClienteVO().setSituacao(situacao);
                filtro.getClienteVO().setCategoria(new CategoriaVO());
                filtro.getClienteVO().getCategoria().setCodigo(
                        getConsultaCategoria());
                filtro.setPessoaVO(null);
            }


            if (isTabelaPassivo()) {
                objs = getFacade().getPassivo().consultarPorNome(getControleConsulta().getValorConsulta(), false, getEmpresaLogado(), Uteis.NIVELMONTARDADOS_TODOS);
            } else if (isTabelaIndicacao()) {
                objs = getFacade().getIndicado().consultarPorNomeIndicado(getControleConsulta().getValorConsulta(), false, getEmpresaLogado(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                // Foi encapsulado o m?todo de consulta em um ?nico, que suportara todos os filtros utilizados atualmente
                objs = getFacade().getCliente().consultarPaginado(filtro, getConfPaginacao());
            }

            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            FuncionalidadeControle funcionalidade = new FuncionalidadeControle();
            funcionalidade.prepararFuncionalidade(evt);

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * AgendaCons.jsp. Define o tipo de consulta a ser executada, por meio de
     * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
     * resultado, disponibiliza um List com os objetos selecionados na sessao da
     * pagina.
     */
    public String consultarCliente() {
        try {
            super.consultar();
            inicializarUsuarioLogado();
            String situacao;
            List objs = new ArrayList();
            // a Situação informada pode ser Situação secund?ria este m?todo
            // pega a Situação principal para consulta
            situacao = situacaoConsulta(getConsultaSituacao());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                int valorInt;
                if (getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = 0;
                } else {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                }
                objs = getFacade().getCliente().consultarPorCodigo(
                        new Integer(valorInt), getEmpresaLogado().getCodigo(),
                        getConsultaCategoria(), situacao, null, null, 0, 0, 0, 0,
                        true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(
                        getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(),
                        getConsultaCategoria(), situacao, null, null, 0, 0, 0, 0,
                        true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            if (getControleConsulta().getCampoConsulta().equals("codPessoa")) {
                int valorInt;
                if (getControleConsulta().getValorConsulta().trim().isEmpty()) {
                    valorInt = 0;
                } else {
                    valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                }
                objs.add(getFacade().getCliente().consultarPorCodigoPessoa(
                        new Integer(valorInt), getEmpresaLogado().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
            }

            if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                objs = getFacade().getCliente().consultarPorNomeEmpresa(
                        getControleConsulta().getValorConsulta(), getConsultaCategoria(),
                        situacao, null, null, 0, 0, 0, 0, true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            if (getControleConsulta().getCampoConsulta().equals("cpf")) {
                objs = getFacade().getCliente().consultarPorCfp(
                        getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(),
                        getConsultaCategoria(), situacao, null, null, 0, 0, 0, 0, true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            if (getControleConsulta().getCampoConsulta().equals("descricaoProfissao")) {
                objs = getFacade().getCliente().consultarPorDescricaoProfissao(
                        getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(),
                        getConsultaCategoria(), situacao, null, null, 0, 0, 0, 0, true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            if (getControleConsulta().getCampoConsulta().equals("descricaoGrauInstrucao")) {
                objs = getFacade().getCliente().consultarPorDescricaoGrauInstrucao(
                        getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(),
                        getConsultaCategoria(), situacao, null, null, 0, true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            if (getControleConsulta().getCampoConsulta().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(
                        getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(),
                        getConsultaCategoria(), situacao, null, null, 0, 0, 0, 0, true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            setListaConsulta(objs);
            Iterator i = objs.iterator();
            while (i.hasNext()) {
                ClienteVO cli = (ClienteVO) i.next();
                obterSituacaoClientePorCLienteEspecifico(cli);
                if (!filtraSituacao(cli)) {
                    i.remove();
                }
            }
            setMensagemID("msg_dados_consultados");
            return "editar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }

    }

    /**
     * @return the telefonesSMSPassivo
     */
    public List<TelefoneVO> getTelefonesPassivo() {
        telefonesSMSPassivo = new ArrayList<TelefoneVO>();
        if (historicoContatoVO.getTipoContato().endsWith("CS")) {
            for (Object obj : getListaTelefoneClientePotencial()) {
                TelefoneVO telefone = (TelefoneVO) obj;
                if (telefone.getTipoTelefone().equals("CE")) {
                    telefonesSMSPassivo.add(telefone);
                }
            }
            return telefonesSMSPassivo;
        } else {
            return getListaTelefoneClientePotencial();
        }
    }

    /**
     * @return the listaHistoricoContato
     */
    public List<HistoricoContatoVO> getListaHistoricoContato() {
        if (listaHistoricoContato == null) {
            listaHistoricoContato = new ArrayList<HistoricoContatoVO>();
        }
        return listaHistoricoContato;
    }

    /**
     * @param listaHistoricoContato the listaHistoricoContato to set
     */
    public void setListaHistoricoContato(List<HistoricoContatoVO> listaHistoricoContato) {
        this.listaHistoricoContato = listaHistoricoContato;
    }

    /**
     * @return the onCompleteBotoes
     */
    public String getOnCompleteBotoes() {
        return onCompleteBotoes;
    }

    /**
     * @param onCompleteBotoes the onCompleteBotoes to set
     */
    public void setOnCompleteBotoes(String onCompleteBotoes) {
        this.onCompleteBotoes = onCompleteBotoes;
    }

    public String getManterAbertoRichModalPanelAgenda() {
        return manterAbertoRichModalPanelAgenda;
    }

    public void setManterAbertoRichModalPanelAgenda(
            String manterAbertoRichModalPanelAgenda) {
        this.manterAbertoRichModalPanelAgenda = manterAbertoRichModalPanelAgenda;
    }

    /**
     * @return the paramTipoConsulta
     */
    public String getParamTipoConsulta() {
        return paramTipoConsulta;
    }

    /**
     * @param paramTipoConsulta the paramTipoConsulta to set
     */
    public void setParamTipoConsulta(String paramTipoConsulta) {
        this.paramTipoConsulta = paramTipoConsulta;
    }


    public List preencherListaOperacoes() {
        List listaOperacoesConsulta = new ArrayList();
        if (getFiltroOperacaoEmail()) {
            listaOperacoesConsulta.add("EM");
        }
        if (getFiltroOperacaoLigacaoSemContato()) {
            listaOperacoesConsulta.add("LC");
        }
        if (getFiltroOperacaoPessoal()) {
            listaOperacoesConsulta.add("PE");
        }
        if (getFiltroOperacaoSMS()) {
            listaOperacoesConsulta.add("CS");
        }
        if (getFiltroOperacaoTelefone()) {
            listaOperacoesConsulta.add("TE");
        }
        if (getFiltroOperacaoWhatsApp()) {
            listaOperacoesConsulta.add("WA");
        }
        return listaOperacoesConsulta;
    }

    public Object alterarMeioEnvio() {
        getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        temUsuarioMovel = false;
        tipoMensagemApp = TipoPerguntaEnum.SIMPLES.getCodigo();
        campos = new CamposGenericosTO();
        if (getHistoricoContatoVO().getTipoContato().equals("CS")) {
            meioEnvio = MeioEnvio.SMS;
        } else if (getHistoricoContatoVO().getTipoContato().equals("EM")) {
            meioEnvio = MeioEnvio.EMAIL;
        } else if(getHistoricoContatoVO().getTipoContato().equals("AP")){
            meioEnvio = MeioEnvio.APP;
            try {
                temUsuarioMovel = getFacade().getUsuarioMovel().temUsuarioMovel(historicoContatoVO.getClienteVO().getCodigo());
            } catch (Exception e) {
                Uteis.logar(e, HistoricoContatoControle.class);
            }
        }
        return meioEnvio;
    }

    public Object decidirTabela() {
        setListaConsulta(new ArrayList());
        if (getControleConsulta().getCampoConsulta().equals("indicacao")) {
            setTabelaIndicacao(true);
            setTabelaPassivo(false);
        } else if (getControleConsulta().getCampoConsulta().equals("passivo")) {
            setTabelaPassivo(true);
            setTabelaIndicacao(false);
        } else {
            setTabelaPassivo(false);
            setTabelaIndicacao(false);
        }
        return null;
    }

    public boolean isTabelaIndicacao() {
        return tabelaIndicacao;
    }

    public void setTabelaIndicacao(boolean tabelaIndicacao) {
        this.tabelaIndicacao = tabelaIndicacao;
    }

    public boolean isTabelaPassivo() {
        return tabelaPassivo;
    }

    public void setTabelaPassivo(boolean tabelaPassivo) {
        this.tabelaPassivo = tabelaPassivo;
    }

    public boolean getApresentarResultadoConsulta() {
        return (this.getListaConsulta() != null) && !isTabelaIndicacao() && !isTabelaPassivo();
    }

    public void selecionarRealizacaoContato() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato",  getUsuarioLogado());
            setAbrirRealizarContato(true);
            novo();
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            preencherFasesDoContato(obj);
            getFacade().getHistoricoContato().inicializarDadosRealizacaoContato(historicoContatoVO, agendaVO, obj, getUsuarioLogado(), getColaboradorResponsavel(), malaDiretaVO, malaDiretaEnviadaVO, getListaTelefoneClientePotencial(), getListaTelefoneIndicado(), false);
            setListaEmail(historicoContatoVO.getClienteVO().getPessoa().getEmailVOs());
            selecionarObjetivoFase();
            setVindoTelaMetaPerda(getFase().equals(FasesCRMEnum.DESISTENTES) || getFase().equals(FasesCRMEnum.VENCIDOS) || getFase().equals(FasesCRMEnum.EX_ALUNOS));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRealizarContato(false);
            montarMsgAlert(e.getMessage());
        }
    }

    public void consultarHistoricoVindoTelaMetaVendasDetalhada() {
        try {
            FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
            setListaHistoricoContatoCliente(getFacade().getHistoricoContato().consultarHistoricoContatoCliente(obj.getCliente().getCodigo(), false, Uteis.NIVELMONTARDADOS_HISTORICOINDICADO));
            if (!getListaHistoricoContatoCliente().isEmpty()) {
                setHistoricoContatoVO((HistoricoContatoVO) getListaHistoricoContatoCliente().get(0));
                getFacade().getHistoricoContato().preencherHistoricoContatoComDadosCliente(getHistoricoContatoVO());
            }
            getHistoricoContatoVO().setClienteVO(obj.getCliente());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void atualizarObjetivo(ActionEvent event) {
        selecionarObjetivoFase();
    }

    public void selecionarObjetivoFase() {
        setFase(FasesCRMEnum.getFasePorSigla(getHistoricoContatoVO().getFase()));

        try {
            setTextoPadrao(getFacade().getTextoPadrao().consultarPorFase(getFase().getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (getTextoPadrao() == null) {
                setTextoPadrao(new TextoPadraoVO());
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public FasesCRMEnum getFase() {
        return fase;
    }

    public void setFase(FasesCRMEnum fase) {
        this.fase = fase;
    }

    public void inserirTextoPadrao() throws Exception {
        FasesCRMEnum faseCRM = FasesCRMEnum.getFasePorSigla(getHistoricoContatoVO().getFase());
        TextoPadraoVO textoPadraoVO = getFacade().getTextoPadrao().consultarPorFase(faseCRM.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (textoPadraoVO != null) {
            getHistoricoContatoVO().setObservacao(textoPadraoVO.getMensagemPadrao());
        }
    }

    public void setarMensagemTextoPadrao() throws Exception{
        if (getCodigoTextoPadrao() != null && getCodigoTextoPadrao() != 0){
            TextoPadraoVO textoPadraoVO = getFacade().getTextoPadrao().consultarPorChavePrimaria(getCodigoTextoPadrao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (textoPadraoVO != null) {
                getHistoricoContatoVO().setObservacao(textoPadraoVO.getMensagemPadrao());
            }
        }else {
            getHistoricoContatoVO().setObservacao(null);
        }

    }


    public void exportar(ActionEvent evt) throws Exception {

        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

        ConfPaginacao confPaginacao = new ConfPaginacao();
        confPaginacao.setPaginarBanco(false);

        HistoricoContatoFiltroVO filtro = getHistoricoContatoFiltroVO();
        List listaParaImpressao = getFacade().getHistoricoContato().consultarPaginado(filtro, confPaginacao);
        exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
    }

    private HistoricoContatoFiltroVO getHistoricoContatoFiltroVO() throws Exception {
        HistoricoContatoFiltroVO filtro = new HistoricoContatoFiltroVO();
        filtro.setNivelMontarDados(Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

        if ("detalhada".equals(this.getParamTipoConsulta())) {
            if (!isPermiteConsultarTodasEmpresas()) {
                filtro.setEmpresaVO(new EmpresaVO());
                filtro.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
            }else{
                filtro.setEmpresaVO(new EmpresaVO());
                filtro.getEmpresaVO().setCodigo(getFiltroEmpresa());
            }

            if (colaboradorConsultar.getCodigo() != 0) {
                filtro.setCodigoColaborador(colaboradorConsultar.getCodigo());
            }
            if (dataInicio != null && dataTermino != null) {
                filtro.setDataInicio(dataInicio);
                filtro.setDataTermino(dataTermino);
            }
            if (dataInicioAgendamento != null && dataTerminoAgendamento != null) {
                filtro.setDataInicioAgendamento(dataInicioAgendamento);
                filtro.setDataTerminoAgendameto(dataTerminoAgendamento);
            }
            montarDefinirFiltro(filtro);
            filtro.setListaFases(Arrays.asList(faseSelecionadas));
            filtro.setGerouAgendamento(getGerouAgendamento());
            filtro.setListaOperacoes(preencherListaOperacoes());
            filtro.setListaObjecoes(Arrays.asList(getAllObjecoes()));
            filtro.setResultadoAgAula(getFiltroResultadoAgAula());
            filtro.setResultadoAgLigacao(getFiltroResultadoAgLigacao());
            filtro.setResultadoAgVisita(getFiltroResultadoAgVisita());
            filtro.setResultadoObjecao(getFiltroResultadoObjecao());
            filtro.setResultadoSimplesRegistro(getFiltroResultadoSimplesRegistro());
            filtro.setResultadoOutros(getFiltroResultadoOutros());
        }

        return filtro;
    }
    public void montarDefinirFiltro(HistoricoContatoFiltroVO filtro){

        String i = "" ;
        if(!UteisValidacao.emptyString(getColunaOrdenar())) {
            i += getColunaOrdenar();
            if(!UteisValidacao.emptyString(getDirecaoOrdenar())) {
                i += " " + getDirecaoOrdenar();
            }
        }
        filtro.setOrdenacao(i);

    }
    public String nomeColunaPorId(String nome){
        if(nome.equals("matricula"))
            return "cliente.matricula";
        else if(nome.equals("nome"))
            return "OrdenacaoNome";
        else if(nome.equals("Resultado"))
            return "historico.resultado";
        else if(nome.equals("tipoContato"))
            return "historico.tipoContato";
        else if(nome.equals("dataContato"))
            return "historico.dia";
        else if(nome.equals("responsavelCadastro"))
            return "nomeResponsavelCadastro";
        else return "";
    }
    public Boolean getAbrirRealizarContato() {
        if (abrirRealizarContato == null) {
            abrirRealizarContato = Boolean.TRUE;
        }
        return abrirRealizarContato;
    }

    public void setAbrirRealizarContato(Boolean abrirRealizarContato) {
        this.abrirRealizarContato = abrirRealizarContato;
    }

    /**
     * M?todo responsavel por abrir popup que enviara os emails para as pessoas
     * selecionadas.
     *
     * @return
     */
    public String getExecutarAberturaPopupRealizarContato() {
        if (getAbrirRealizarContato()) {
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            return "abrirPopup('newRealizarContatoForm.jsp', 'RealizarContatoform', 850, 700);";
        }
        return getMsgAlert();
    }


    public Boolean getGerouAgendamento() {
        if (gerouAgendamento == null) {
            gerouAgendamento = false;
        }
        return gerouAgendamento;
    }

    public HtmlDataTable getDataTable() {
        return dataTable;
    }

    public void setDataTable(HtmlDataTable dataTable) {
        this.dataTable = dataTable;
    }

    public void setGerouAgendamento(Boolean gerouAgendamento) {
        this.gerouAgendamento = gerouAgendamento;
    }

    public Boolean getAgendamentoVisita() {
        return agendamentoVisita;
    }

    public void setAgendamentoVisita(Boolean agendamentoVisita) {
        this.agendamentoVisita = agendamentoVisita;
    }

    public TextoPadraoVO getTextoPadrao() {
        return textoPadrao;
    }

    public void setTextoPadrao(TextoPadraoVO textoPadrao) {
        this.textoPadrao = textoPadrao;
    }

    public boolean isApresentarLinkExternoTextoPadrao() {
        return !getHistoricoContatoVO().getContatoAvulso()
                && getTextoPadrao().getLinkDocs() != null
                && !getTextoPadrao().getLinkDocs().trim().isEmpty();
    }

    public Date getDataInicioAgendamento() {
        return dataInicioAgendamento;
    }

    public void setDataInicioAgendamento(Date dataInicioAgendamento) {
        this.dataInicioAgendamento = dataInicioAgendamento;
    }

    public Date getDataTerminoAgendamento() {
        return dataTerminoAgendamento;
    }

    public void setDataTerminoAgendamento(Date dataTerminoAgendamento) {
        this.dataTerminoAgendamento = dataTerminoAgendamento;
    }

    public void limparData() {
        setDataInicio(null);
        setDataTermino(null);
    }

    public void limparDataAgendamento() {
        setDataInicioAgendamento(null);
        setDataTerminoAgendamento(null);
    }

    public String getFasesDoContato() {
        return fasesDoContato;
    }

    public void setFasesDoContato(String fasesDoContato) {
        this.fasesDoContato = fasesDoContato;
    }


    public Integer getCodigoTextoPadrao() {
        return codigoTextoPadrao;
    }

    public void setCodigoTextoPadrao(Integer codigoTextoPadrao) {
        this.codigoTextoPadrao = codigoTextoPadrao;
    }



    public void setListaTextoPadrao(List<SelectItem> listaTextoPadrao) {
        this.listaTextoPadrao = listaTextoPadrao;
    }


    public List<SelectItem> getListaTextoPadrao() throws Exception {
        if (listaTextoPadrao == null){
            FasesCRMEnum faseCRM = FasesCRMEnum.getFasePorSigla(getHistoricoContatoVO().getFase());
            listaTextoPadrao = getFacade().getTextoPadrao().consultarPorFaseVariosParaMontarCombo(faseCRM.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        return listaTextoPadrao;
    }

    public Boolean getListaComMaisDeUm() throws Exception{
        if (getListaTextoPadrao() != null && getListaTextoPadrao().size() > 2){
            return true;
        }
        return false;
    }

    public boolean getBotoesApp(){
        return tipoMensagemApp != null && tipoMensagemApp.equals(TipoPerguntaEnum.OBJETIVA.getCodigo())
                && getApresentarAPP();
    }

    public Integer getTipoMensagemApp() {
        return tipoMensagemApp;
    }

    public void setTipoMensagemApp(Integer tipoMensagemApp) {
        this.tipoMensagemApp = tipoMensagemApp;
    }

    public CamposGenericosTO getCampos() {
        return campos;
    }

    public void setCampos(CamposGenericosTO campos) {
        this.campos = campos;
    }

    public List<SelectItem> getListaSelectTiposPerguntas() {
        if(listaSelectTiposPerguntas == null){
            listaSelectTiposPerguntas = new ArrayList<SelectItem>();
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.SIMPLES.getCodigo(), TipoPerguntaEnum.SIMPLES.getDescricao()));
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.DISSERTATIVA.getCodigo(), TipoPerguntaEnum.DISSERTATIVA.getDescricao()));
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.OBJETIVA.getCodigo(), TipoPerguntaEnum.OBJETIVA.getDescricao()));
        }
        return listaSelectTiposPerguntas;
    }

    public void gravarEnviandoContatoAPP() {
        try {
            if (validarMetaNaoAberta()) {
                setMsgAlert("alert('Para realizar contato abra primeiramente a meta de hoje.');");
                return;
            }
            //LOG - INICIO
            boolean historicoContatoVONovoObj = true;
            try {
                if (!historicoContatoVO.isNovoObj()) {
                    historicoContatoVONovoObj = false;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
            malaDiretaVO.setTipoPergunta(tipoMensagemApp);
            if(getBotoesApp()){
                malaDiretaVO.setOpcoes(campos.toString());
            }else if(tipoMensagemApp == TipoPerguntaEnum.DISSERTATIVA.getCodigo()){
                malaDiretaVO.setOpcoes("Confirmar;TEXTO");
            }else{
                campos = new CamposGenericosTO();
                malaDiretaVO.setOpcoes("");
            }
            MalaDiretaEnviadaVO enviado = new MalaDiretaEnviadaVO();
            enviado.setClienteVO(historicoContatoVO.getClienteVO());
            malaDiretaVO.setmalaDiretaEnviadaVOs(new ArrayList<MalaDiretaEnviadaVO>());
            malaDiretaVO.getMalaDiretaEnviadaVOs().add(enviado);
            malaDiretaVO.setMensagem(historicoContatoVO.getObservacao().replaceFirst(mensagem, mensagem));
            malaDiretaVO.setUsuarioVO(getUsuarioLogado());
            malaDiretaVO.setContatoAvulso(historicoContatoVO.getContatoAvulso());
            malaDiretaVO.setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));
            malaDiretaVO.setMeioDeEnvioEnum(MeioEnvio.APP);
            malaDiretaVO.setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
            getFacade().getMalaDireta().incluir(malaDiretaVO);
            String retornoGerarNotificacoes = TreinoWSConsumer.gerarNotificacoes(getKey(),
                    malaDiretaVO, historicoContatoVO.getClienteVO().getCodigo().toString(),malaDiretaVO.getMensagem());
            if (UteisValidacao.emptyString(retornoGerarNotificacoes) || retornoGerarNotificacoes.toUpperCase().contains("ERRO")) {
                throw new Exception("Não foi possível enviar notifica??o. Tente novamente. " + retornoGerarNotificacoes);
            }
            String[] itens = retornoGerarNotificacoes.split(";");
            for (String item : itens) {
                String[] dados = item.split(",");
                getFacade().getHistoricoContato().executarGravacaoVindoEmail(malaDiretaVO, 0, Integer.valueOf(dados[0]), 0,
                        Integer.valueOf(dados[1]), false);
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_app_enviado");
            montarMsgAlert(getMensagem());
            //LOG - INICIO
            try {
                if (historicoContatoVONovoObj) {
                    historicoContatoVO.setObjetoVOAntesAlteracao(new HistoricoContatoVO());
                    historicoContatoVO.setNovoObj(true);
                }
                registrarLogObjetoVO(historicoContatoVO, historicoContatoVO.getCodigo(), "HISTORICOCONTATO", historicoContatoVO.getClienteVO().getPessoa().getCodigo());
            } catch (Exception e) {
                e.printStackTrace();
            }
            //LOG - FIM
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_enviar_emailErro");
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }

    }
    public void montarListasOrdenacao(){
        colunaOrdenar = "";
        direcaoOrdenar = "";
        setListColunasOrdenar(new ArrayList<SelectItem>());
        getListColunasOrdenar().add(new SelectItem("matricula","Matrícula"));
        getListColunasOrdenar().add(new SelectItem("OrdenacaoNome","Nome"));
        getListColunasOrdenar().add(new SelectItem("historico.tipoContato","Tipo Contato"));
        getListColunasOrdenar().add(new SelectItem("historico.dia","Data Contato"));
        getListColunasOrdenar().add(new SelectItem("historico.fase","Fase"));
        getListColunasOrdenar().add(new SelectItem("nomeResponsavelCadastro","Responsável Cadastro"));

        setListDirecaoOrdenar(new ArrayList<SelectItem>());
        getListDirecaoOrdenar().add(new SelectItem("asc" , "Crescente"));
        getListDirecaoOrdenar().add(new SelectItem("desc" , "Decrescente"));

    }
    public List<SelectItem> getListDirecaoOrdenar() {
        return listDirecaoOrdenar;
    }

    public void setListDirecaoOrdenar(List<SelectItem> listDirecaoOrdenar) {
        this.listDirecaoOrdenar = listDirecaoOrdenar;
    }

    public String getDirecaoOrdenar() {
        return direcaoOrdenar;
    }

    public void setDirecaoOrdenar(String direcaoOrdenar) {
        this.direcaoOrdenar = direcaoOrdenar;
    }

    public List<SelectItem> getListColunasOrdenar() {
        return listColunasOrdenar;
    }

    public void setListColunasOrdenar(List<SelectItem> listColunasOrdenar) {
        this.listColunasOrdenar = listColunasOrdenar;
    }

    public String getColunaOrdenar() {
        return colunaOrdenar;
    }

    public void setColunaOrdenar(String colunaOrdenar) {
        this.colunaOrdenar = colunaOrdenar;
    }

    public boolean isTemUsuarioMovel() {
        return temUsuarioMovel;
    }

    public void setTemUsuarioMovel(boolean temUsuarioMovel) {
        this.temUsuarioMovel = temUsuarioMovel;
    }

    public String[] getFaseSelecionadas() {
        return faseSelecionadas;
    }

    public void setFaseSelecionadas(String[] faseSelecionadas) {
        this.faseSelecionadas = faseSelecionadas;
    }

    public List<SelectItem> montarFasesFiltro() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        try{
            for (FasesCRMEnum fase : FasesCRMEnum.values()) {
                if (!fase.equals(FasesCRMEnum.CONVERSAO_AGENDADOS)
                        && !fase.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS)
                        && !fase.equals(FasesCRMEnum.CONVERSAO_INDICADOS)
                        && !fase.equals(FasesCRMEnum.CONVERSAO_PASSIVO)
                        && !fase.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS)
                        && !fase.equals(FasesCRMEnum.CONVERSAO_DESISTENTES)
                ) {
                    itens.add(new SelectItem(fase.getSigla(), fase.getDescricao()));
                }
            }
        }catch(Exception ignored){
        }
        return itens;
    }

    public Integer[] getAllObjecoes(){
        Integer c[] = new Integer[getObjecoesSelecionadas().length + getObjecoesSelecionadasDefinitiva().length + getObjecoesSelecionadasDesistencia().length];

        System.arraycopy(getObjecoesSelecionadas(), 0, c, 0, getObjecoesSelecionadas().length);
        System.arraycopy(getObjecoesSelecionadasDefinitiva(), 0, c, getObjecoesSelecionadas().length, getObjecoesSelecionadasDefinitiva().length);
        System.arraycopy(getObjecoesSelecionadasDesistencia(), 0, c, getObjecoesSelecionadas().length + getObjecoesSelecionadasDefinitiva().length, getObjecoesSelecionadasDesistencia().length);

        return c;
    }

    public Integer[] getObjecoesSelecionadas() {
        if(null == objecoesSelecionadas){
            objecoesSelecionadas = new Integer[0];
        }
        return objecoesSelecionadas;
    }

    public void setObjecoesSelecionadas(Integer[] objecoesSelecionadas) {
        this.objecoesSelecionadas = objecoesSelecionadas;
    }

    public Integer[] getObjecoesSelecionadasDefinitiva() {
        if(null == objecoesSelecionadasDefinitiva){
            objecoesSelecionadasDefinitiva = new Integer[0];
        }
        return objecoesSelecionadasDefinitiva;
    }

    public void setObjecoesSelecionadasDefinitiva(Integer[] objecoesSelecionadasDefinitiva) {
        this.objecoesSelecionadasDefinitiva = objecoesSelecionadasDefinitiva;
    }

    public Integer[] getObjecoesSelecionadasDesistencia() {
        if(null == objecoesSelecionadasDesistencia){
            objecoesSelecionadasDesistencia = new Integer[0];
        }
        return objecoesSelecionadasDesistencia;
    }

    public void setObjecoesSelecionadasDesistencia(Integer[] objecoesSelecionadasDesistencia) {
        this.objecoesSelecionadasDesistencia = objecoesSelecionadasDesistencia;
    }

    public Map<String, List<SelectItem>> montarObjecoesFiltro() {
        Map<String, List<SelectItem>> itens = new HashMap<String, List<SelectItem>>();
        itens.put("OD", new ArrayList<SelectItem>());
        itens.put("OB", new ArrayList<SelectItem>());
        itens.put("MD", new ArrayList<SelectItem>());

        try{
            List<ObjecaoVO> objecaoVOS = getFacade().getObjecao().consultarTodas(Uteis.NIVELMONTARDADOS_TODOS);

            for(ObjecaoVO ov : objecaoVOS){
                itens.get(ov.getTipoGrupo()).add(new SelectItem(ov.getCodigo(), ov.getDescricao()));
            }

        }catch(Exception ignored){
        }
        return itens;
    }

    public List<SelectItem> getListaFasesSelecionar() {
        if(listaFasesSelecionar == null){
            listaFasesSelecionar = montarFasesFiltro();
        }
        return listaFasesSelecionar;
    }

    public void setListaFasesSelecionar(List<SelectItem> listaFasesSelecionar) {
        this.listaFasesSelecionar = listaFasesSelecionar;
    }

    public List<SelectItem> getListaObjecoesSelecionarDefinitiva() {
            Map<String, List<SelectItem>> map = montarObjecoesFiltro();
            listaObjecoesSelecionarDefinitiva = map.get("OD");
            listaObjecoesSelecionarNaoDefinitiva = map.get("OB");
            listaObjecoesSelecionarDesistencia = map.get("MD");

        return listaObjecoesSelecionarDefinitiva;
    }

    public void setListaObjecoesSelecionarDefinitiva(List<SelectItem> listaOjecoesSelecionarDefinitiva) {

        this.listaObjecoesSelecionarDefinitiva = listaOjecoesSelecionarDefinitiva;
    }
    public List<SelectItem> getListaObjecoesSelecionarNaoDefinitiva() {


            Map<String, List<SelectItem>> map = montarObjecoesFiltro();
            listaObjecoesSelecionarDefinitiva = map.get("OD");
            listaObjecoesSelecionarNaoDefinitiva = map.get("OB");
            listaObjecoesSelecionarDesistencia = map.get("MD");

        return listaObjecoesSelecionarNaoDefinitiva;
    }

    public void setListaObjecoesSelecionarNaoDefinitiva(List<SelectItem> listaOjecoesSelecionarNaoDefinitiva) {
        this.listaObjecoesSelecionarNaoDefinitiva = listaOjecoesSelecionarNaoDefinitiva;
    }

    public List<SelectItem> getListaObjecoesSelecionarDesistencia() {
            Map<String, List<SelectItem>> map = montarObjecoesFiltro();
            listaObjecoesSelecionarDefinitiva = map.get("OD");
            listaObjecoesSelecionarNaoDefinitiva = map.get("OB");
            listaObjecoesSelecionarDesistencia = map.get("MD");

        return listaObjecoesSelecionarDesistencia;
    }

    /*Metodos usados para pagina??o*/

    public void inicializarPaginacao() {
        listaClienteHistoricoContato = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
    }

    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) throws Exception {
        if (codigo.equals(LISTA_CONTATOS)) {
            return listaClienteHistoricoContato;
        }
        return null;
    }

    private void carregarListaPaginacao(ListaPaginadaTO paginacao,String codigo) throws Exception {
        if (codigo.equals(LISTA_CONTATOS)) {
            consultarListaContato(paginacao);
        }
    }

    public void proximaPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(paginacao,codigo);
    }
    public void paginaAnterior(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(paginacao,codigo);
    }
    public void ultimaPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(paginacao,codigo);
    }
    public void primeiraPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(paginacao,codigo);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception{
        String codigo = (String)evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(paginacao,codigo);
    }

    private void consultarListaContato(ListaPaginadaTO paginacao) throws Exception{
        // listaHistoricoProduto = getFacade().getMovProduto().consultarTelaCliente(cliente.getPessoa().getCodigo(), contratoSelecionado == null ? null : contratoSelecionado.getCodigo(), paginacao.getLimit(),paginacao.getOffset());
        listaHistoricoContatoCliente = getFacade().getHistoricoContato().consultarHistoricoContatoCliente(getHistoricoContatoVO().getClienteVO().getCodigo().intValue(),false,Uteis.NIVELMONTARDADOS_HISTORICOCLIENTE,paginacao.getLimit(),paginacao.getOffset());
        //setListaHistoricoContatoCliente
    }

    /*Fim m?todos usados para pagina??o*/

    public void setListaObjecoesSelecionarDesistencia(List<SelectItem> listaObjecoesSelecionarDesistencia) {
        this.listaObjecoesSelecionarDesistencia = listaObjecoesSelecionarDesistencia;
    }

    public Boolean getFiltroResultadoSimplesRegistro() {
        return null == filtroResultadoSimplesRegistro ? false : filtroResultadoSimplesRegistro;
    }

    public void setFiltroResultadoSimplesRegistro(Boolean filtroResultadoSimplesRegistro) {
        this.filtroResultadoSimplesRegistro = filtroResultadoSimplesRegistro;
    }

    public Boolean getFiltroResultadoObjecao() {
        return null == filtroResultadoObjecao ? false : filtroResultadoObjecao;
    }

    public void setFiltroResultadoObjecao(Boolean filtroResultadoObjecao) {
        this.filtroResultadoObjecao = filtroResultadoObjecao;
    }

    public Boolean getFiltroResultadoAgLigacao() {
        return null == filtroResultadoAgLigacao ? false : filtroResultadoAgLigacao;
    }

    public void setFiltroResultadoAgLigacao(Boolean filtroResultadoAgLigacao) {
        this.filtroResultadoAgLigacao = filtroResultadoAgLigacao;
    }

    public Boolean getFiltroResultadoAgVisita() {
        return null == filtroResultadoAgVisita ? false : filtroResultadoAgVisita;
    }

    public void setFiltroResultadoAgVisita(Boolean filtroResultadoAgVisita) {
        this.filtroResultadoAgVisita = filtroResultadoAgVisita;
    }

    public Boolean getFiltroResultadoAgAula() {
        return null == filtroResultadoAgAula ? false : filtroResultadoAgAula;
    }

    public void setFiltroResultadoAgAula(Boolean filtroResultadoAgAula) {
        this.filtroResultadoAgAula = filtroResultadoAgAula;
    }

    public Boolean getFiltroResultadoOutros() {
        return null == filtroResultadoOutros ? false : filtroResultadoOutros;     }

    public void setFiltroResultadoOutros(Boolean filtroResultadoOutros) {
        this.filtroResultadoOutros = filtroResultadoOutros;
    }

    public List<SelectItem> getFiltroEmpresas() {
        return filtroEmpresas;
    }

    public void setFiltroEmpresas(List<SelectItem> filtroEmpresas) {
        this.filtroEmpresas = filtroEmpresas;
    }

    public int getFiltroEmpresa() {
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(int filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public boolean isPermiteConsultarTodasEmpresas() {
        return permiteConsultarTodasEmpresas;
    }

    public void setPermiteConsultarTodasEmpresas(boolean permiteConsultarTodasEmpresas) {
        this.permiteConsultarTodasEmpresas = permiteConsultarTodasEmpresas;
    }

    public ListaPaginadaTO getListaClienteHistoricoContato() {
        return listaClienteHistoricoContato;
    }

    public void setListaClienteHistoricoContato(ListaPaginadaTO listaClienteHistoricoContato) {
        this.listaClienteHistoricoContato = listaClienteHistoricoContato;
    }

    public void salvarSimplesRegistro() {
        try {
            limparMsg();
            if (getHistoricoContatoVOEdicao() != null) {
                getFacade().getHistoricoContato().alterar(getHistoricoContatoVOEdicao());

                getHistoricoContatoVOEdicao().setObjetoVOAntesAlteracao(getHistoricoContatoVOAntesEdicao().getClone(true));
                getHistoricoContatoVOEdicao().setNovoObj(false);
                registrarLogObjetoVOGeralAlterandoResponsavel(getHistoricoContatoVOEdicao(), getHistoricoContatoVOEdicao().getCodigo(), "HISTORICOCONTATO", getClienteVO().getPessoa().getCodigo(), false, getUsuarioLogado().getNome());

                inicializarHistoricoContato();
            }
        } catch (Exception e) {
            e.printStackTrace();
            montarErro(e);
        }

    }

    public void prepararEdicaoSimplesRegistro() {
        try {
            limparMsg();
            HistoricoContatoVO obj = (HistoricoContatoVO) context().getExternalContext().getRequestMap().get("contatos");
            if (obj != null) {
                setHistoricoContatoVOEdicao((HistoricoContatoVO) obj.getClone(true));
                setHistoricoContatoVOAntesEdicao((HistoricoContatoVO) obj.getClone(true));
                setOnCompleteEdicaoSimplesRegistro("Richfaces.showModalPanel('mdlEditarSimplesRegistro');");
            }
        } catch (Exception e) {
            setOnCompleteEdicaoSimplesRegistro("");
            e.printStackTrace();
            montarErro(e);
        }

    }

    public String getOnCompleteEdicaoSimplesRegistro() {
        return onCompleteEdicaoSimplesRegistro;
    }

    public void setOnCompleteEdicaoSimplesRegistro(String onCompleteEdicaoSimplesRegistro) {
        this.onCompleteEdicaoSimplesRegistro = onCompleteEdicaoSimplesRegistro;
    }

    public HistoricoContatoVO getHistoricoContatoVOEdicao() {
        return historicoContatoVOEdicao;
    }

    public void setHistoricoContatoVOEdicao(HistoricoContatoVO historicoContatoVOEdicao) {
        this.historicoContatoVOEdicao = historicoContatoVOEdicao;
    }

    public HistoricoContatoVO getHistoricoContatoVOAntesEdicao() {
        return historicoContatoVOAntesEdicao;
    }

    public void setHistoricoContatoVOAntesEdicao(HistoricoContatoVO historicoContatoVOAntesEdicao) {
        this.historicoContatoVOAntesEdicao = historicoContatoVOAntesEdicao;
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        List<String> nomesClasse = new ArrayList<String>();
        nomesClasse.add("HISTORICOCONTATO");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Agenda_SimplesRegistro"));
        loginControle.consultarLogEntidadesSelecionado(nomesClasse, 0, getClienteVO().getPessoa().getCodigo());
    }
}

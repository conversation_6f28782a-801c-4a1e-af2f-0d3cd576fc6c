package controle.crm;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.produtoPacto.ComprarProdutoPactoDTO;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;

public class AdquiraSMSControle  extends SuperControleRelatorio {


    private UsuarioVO usuarioVO = new UsuarioVO();
    private EmpresaVO empresaVO = new EmpresaVO();
    private Boolean podeAdquirirModulo = false;
    private Boolean comprandoAvulso = false;
    private Integer idProdutoPacto = 0;
    private String descProdutoPacto = "";
    private String valorAdesaoProdutoPacto = "";
    private String valorMensalidadeProdutoPacto = "";
    private Integer quantidadeSMS;
    private String onCompleteGravar;
    private String valorSMSApresentar;
    private Integer limiteUnico;
    private Double valorSMS = 0.0;
    private Integer tipoServico;
    private String nomeClasse = "AdquiraSMS";

    public AdquiraSMSControle() {
        init();
    }

    public void init() {
        try {
            setLimiteUnico(5000);
            setValorSMS(0.0);
            setTipoServico(1);
            setIdProdutoPacto(0);
            setDescProdutoPacto("ATIVAÇÃO SMS");
            setValorAdesaoProdutoPacto("R$ 0,00");
            setValorMensalidadeProdutoPacto("R$ 0,00");
            setPodeAdquirirModulo(false);
            setUsuarioVO(getUsuarioLogado() != null ? getUsuarioLogado() : new UsuarioVO());
            setEmpresaVO(getEmpresaLogado() != null ? getEmpresaLogado() : new EmpresaVO());
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            Uteis.logar(e, AdquiraSMSControle.class);
        }
    }

    public void comprarMilSMS(){
        setMsgAlert("");
        setMensagemID("");
        setMensagem("");
        setMensagemDetalhada("");
        setQuantidadeSMS(1000);
        validarCompraSMS();
    }

    public void comprarQuinhetosSMS(){
        setMsgAlert("");
        setMensagemID("");
        setMensagem("");
        setMensagemDetalhada("");
        setQuantidadeSMS(500);
        validarCompraSMS();
    }

    public void comprarSMS() {
        setMsgAlert("");
        setMensagemID("");
        setMensagem("");
        setMensagemDetalhada("");

        if (tipoServico == 1) {
            comprarMilSMS();
            comprandoAvulso = false;
        } else if (tipoServico == 2) {
            comprarQuinhetosSMS();
            setComprandoAvulso(false);
        } else if (comprandoAvulso && tipoServico == 3) {
            if (getLimiteUnico() < 5000) {
                montarErro("O limite mínimo para compra é 5000 SMS");
            } else {
                setQuantidadeSMS(getLimiteUnico());
                comprandoAvulso = true;
                validarCompraSMS();
            }
        }
    }

    /*public void dadosNegociacao(Integer quantidadeSMS) throws Exception {
        setMsgAlert("");
        setMensagemID("");
        setMensagem("");
        setMensagemDetalhada("");
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = "http://localhost:8040/oamd/prest/produto/consultar?tipoProduto=21&ativo=true";
        JSONArray jsonArray;
        try {
            String response = executeRequestHttpService.executeHttpRequest(url, "", new HashMap<>(), ExecuteRequestHttpService.METODO_POST, "UTF-8" );
            jsonArray = new JSONObject(response).getJSONArray("return");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = (JSONObject) jsonArray.get(i);

                if(obj.getInt("qtd") == quantidadeSMS && !getComprandoAvulso()) {
                    setValorMensalidadeProdutoPacto(obj.getString("mensalidadeApresentar"));
                    setIdProdutoPacto(obj.getInt("id"));
                    setDescProdutoPacto(obj.getString("descricao"));
                    setValorAdesaoProdutoPacto(obj.getString("adesaoApresentar"));
                }else if(getComprandoAvulso() && obj.getInt("qtd") == 0){
                    setValorMensalidadeProdutoPacto(obj.getString("mensalidadeApresentar"));
                    setIdProdutoPacto(obj.getInt("id"));
                    setDescProdutoPacto(obj.getString("descricao"));
                    setValorAdesaoProdutoPacto(obj.getString("adesaoApresentar"));
                }
            }
        } catch (Exception e) {
            montarErro("Produto não disponivel para ativação rápida, contate o comercial pacto.");
            throw new Exception(e);
        }
    }*/

    public void verificarPermissao() throws Exception {
        try {
            validarPermissao("BoletosSistema", "4.31 - Emitir boleto para pagamento de contrato mensal para Pacto Software e Gestão", getUsuarioVO());
            for (Object o : getUsuarioVO().getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                if (usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("ADMINISTRADOR") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("ADMIN") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("ADM") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("MASTER") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("DONO") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("CEO")) ;
                setPodeAdquirirModulo(true);
                break;
            }

            if (getPodeAdquirirModulo()) {
                comprarProdutoPacto();
            }
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            getEmpresaLogado().setTokenSMS(getEmpresaVO().getTokenSMS());
            setMsgAlert("Richfaces.hideModalPanel('modalComprarSMS');Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');Richfaces.showModalPanel('modalSMSVendido')");
        }catch (Exception e){
            montarErro(e.getMessage());
            setMensagemDetalhada("msg_erro", e.getMessage());
            throw new Exception(e.getMessage());
        }
    }

    public void comprarProdutoPacto() throws Exception {
        setUsuarioVO(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        validarDados();

        ComprarProdutoPactoDTO compraDTO = new ComprarProdutoPactoDTO();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url;
        if (!comprandoAvulso) {
            url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/produto/comprarSMS?isAvulso=false&qtdavulso=0&limitediario=" + getQuantidadeSMS() +
                    "&valorAssinatura=" + valorSMS + "&vigenciaSMS=" + 365 + "&produto=" + tipoServico;
        } else {
            url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/produto/comprarSMS?isAvulso=true&qtdavulso=" + getLimiteUnico() + "&limitediario=0&vigenciaSMS=" + 365
                    + "&produto=" + tipoServico;
        }


        //DADOS CLIENTE
        compraDTO.setResponsavel(getUsuarioVO().getNome());
        compraDTO.setCpf(getUsuarioVO().getColaboradorVO().getPessoa().getCfp());
        compraDTO.setEmail(getUsuarioVO().getColaboradorVO().getPessoa().getEmail());
        compraDTO.setCelular(getUsuarioVO().getColaboradorVO().getPessoa().getTelefonesCelular());

        //DADOS EMPRESA
        compraDTO.setChave(getKey());
        compraDTO.setCep(getEmpresaVO().getCEP());
        compraDTO.setEmpresa(getEmpresaVO().getCodigo());
        compraDTO.setCodFinanceiro(getEmpresaVO().getCodEmpresaFinanceiro());
        compraDTO.setNomeFantasia(getEmpresaVO().getNome());
        compraDTO.setRazaoSocial(getEmpresaVO().getNome());
        compraDTO.setCnpj(getEmpresaVO().getCNPJ());

        //DADOS DA COMPRA
        compraDTO.setSistemaOrigem("ZW CRM");
        String response = executeRequestHttpService.executeRequestCupomDesconto(url, new JSONObject(compraDTO).toString(), new HashMap<>());
        try {
            setMensagemDetalhada(new JSONObject(response).get("return").toString());
            notificarRecursoEmpresa(RecursoSistema.COMPROU_SMS);
            inserirLog();
        }catch (Exception e){
            montarErro(e);
            throw new Exception(new JSONObject(response).get("erro").toString());
        }
    }

    private void validarDados() throws Exception{
        if(UteisValidacao.emptyString(getUsuarioVO().getNome())){
            throw new Exception("O nome do usuario responsavel pela operação está vazio.");
        }
       if(UteisValidacao.emptyString(getUsuarioVO().getColaboradorVO().getPessoa().getCfp())){
            throw new Exception("O cpf do usuario responsavel pela operação está vazio. Atualize o cpf no cadastro de colaborador e tente novamente.");
        }
        if(UteisValidacao.emptyString(getUsuarioVO().getColaboradorVO().getPessoa().getEmail())){
            throw new Exception("O e-mail do usuario responsavel pela operação está vazio. Atualize o e-mail no cadastro de colaborador e tente novamente.");
        }
        if(UteisValidacao.emptyString(getUsuarioVO().getColaboradorVO().getPessoa().getTelefonesCelular())){
            throw new Exception("O telefone do usuario responsavel pela operação está vazio. Atualize o telefone no cadastro de colaborador e tente novamente.");
        }
        if(UteisValidacao.emptyString(getEmpresaVO().getCNPJ())){
            throw new Exception("O cnpj da empresa está vazio.");
        }
    }

    public void validarCompraSMS() {
        setOnCompleteGravar("");
        setMsgAlert("");
        setMsgAlertAuxiliar("");
        final AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                verificarPermissao();
                auto.setRenderComponents("panelAutorizacaoFuncionalidade,form:panelMensagem,rateioGrid,panelCampos");
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Comprar Serviço de SMS", "BoletosSistema",
                "Você precisa da permissão \"4.31 - Boletos do Sistema\" para executar essa ação.",
                "form:panelMensagem", listener);
    }

    public void notificarRecurso() {
        notificarRecursoEmpresa(RecursoSistema.CLICOU_COMPRAR_SMS);
    }

    public void calcularValorSMS() {
        if (tipoServico == 1) {
            setComprandoAvulso(false);
            setValorSMS(3574.00);
        }

        if (tipoServico == 2) {
            setComprandoAvulso(false);
            setValorSMS(1787.00);
        }

        if (tipoServico == 3) {
            setComprandoAvulso(true);
            setValorSMS(0.12 * getLimiteUnico());
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog(nomeClasse));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, 0);
    }

    public void inserirLog() throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setNomeEntidade("AdquiraSMS");
            obj.setNomeEntidadeDescricao("AdquiraSMS");
            obj.setOperacao("COMPRA");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("Compra de : " + getQuantidadeSMS() + " SMS ");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            registrarLogObjetoVO(obj, getUsuarioVO().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("AdquiraSMS", getUsuarioVO().getCodigo(), "ERRO AO COMPRAR SMS", getUsuarioVO().getNome(), this.getUsuarioVO().getUserOamd());
            e.printStackTrace();
        }
    }

    public String getOnComplete() {
        return getMsgAlert();
    }

    private void setPodeAdquirirModulo(Boolean podeAdquirirModulo) {
        this.podeAdquirirModulo = podeAdquirirModulo;
    }

    public Boolean getPodeAdquirirModulo() {
        return podeAdquirirModulo;
    }

    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    private void setValorMensalidadeProdutoPacto(String valorMensalidadeProdutoPacto) {
        this.valorMensalidadeProdutoPacto = valorMensalidadeProdutoPacto;
    }

    public String getValorMensalidadeProdutoPacto() {
        return valorMensalidadeProdutoPacto;
    }

    private void setValorAdesaoProdutoPacto(String valorAdesaoProdutoPacto) {
        this.valorAdesaoProdutoPacto = valorAdesaoProdutoPacto;
    }

    public String getValorAdesaoProdutoPacto() {
        return valorAdesaoProdutoPacto;
    }

    private void setDescProdutoPacto(String descProdutoPacto) {
        this.descProdutoPacto = descProdutoPacto;
    }

    public String getDescProdutoPacto() {
        return descProdutoPacto;
    }

    private void setIdProdutoPacto(Integer idProdutoPacto) {
        this.idProdutoPacto = idProdutoPacto;
    }

    public Integer getIdProdutoPacto() {
        return idProdutoPacto;
    }

    public Integer getQuantidadeSMS() {
        return quantidadeSMS;
    }

    public void setQuantidadeSMS(Integer quantidadeSMS) {
        this.quantidadeSMS = quantidadeSMS;
    }

    public String getOnCompleteGravar() {
        return onCompleteGravar;
    }

    public void setOnCompleteGravar(String onCompleteGravar) {
        this.onCompleteGravar = onCompleteGravar;
    }

    public Integer getLimiteUnico() {
        return limiteUnico;
    }

    public void setLimiteUnico(Integer limiteUnico) {
        this.limiteUnico = limiteUnico;
    }

    public Double getValorSMS() {
        return valorSMS;
    }

    public String getValorSMSApresentar() {
        calcularValorSMS();
        return valorSMSApresentar = Formatador.formatarValorMonetario(valorSMS);
    }

    public Integer getTipoServico() {
        return tipoServico;
    }

    public void setTipoServico(Integer tipoServico){
        this.tipoServico = tipoServico;
    }

    public void setValorSMS(Double valorSMS) {
        this.valorSMS = valorSMS;
    }

    public Boolean getComprandoAvulso() {
        return comprandoAvulso;
    }

    public void setComprandoAvulso(Boolean comprandoAvulso) {
        this.comprandoAvulso = comprandoAvulso;
    }
}

package controle.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.crm.Passivo;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas passivoForm.jsp passivoCons.jsp) com as funcionalidades da classe
 * <code>Passivo</code>. Implemtação da camada controle (Backing Bean).
 * 
 * @see SuperControle
 * @see Passivo
 * @see PassivoVO
 */
public class PassivoControle extends SuperControle {

	private PassivoVO passivoVO;
	private String campoConsultarResponsavelCadastro;
	private String observacao;
	private String valorConsultarResponsavelCadastro;
	private List listaConsultarResponsavelCadastro;
	private String campoConsultarColaboradorResponsavel;
	private String valorConsultarColaboradorResponsavel;
	private List listaConsultarColaboradorResponsavel;
	private List listaConsultaObjecao;
	private Boolean manterAbertoRichModal;
	private Boolean manterAbertoRichModalPanelObjecao;
	private Boolean somenteEditarPassivo;
	private Date dataConsulta;
	private HistoricoContatoVO historicoContatoVO;
	private String abrirModalAgendamento;
	private String abrirModalObjecao;
	private List<SelectItem> listaSelectItemEmpresa;
	private Date dataInicioConsulta;
	private Date dataFimConsulta;
	private String filtroConsulta;
	private String msgAlert;
	private boolean apresentarConvertidos;

	//Filto
	private PassivoFiltro passivoFiltro = new PassivoFiltro();
	private List<PassivoVO> passivoVOList = new ArrayList<PassivoVO>();

	/**
	 * Interface <code>PassivoInterfaceFacade</code> responsável pela
	 * interconexão da camada de controle com a camada de negócio. Criando uma
	 * independência da camada de controle com relação a tenologia de
	 * persistência dos dados (DesignPatter: Façade).
	 */

	public PassivoControle() throws Exception {
		obterUsuarioLogado();
		setControleConsulta(new ControleConsulta());
		setDataConsulta(negocio.comuns.utilitarias.Calendario.hoje());
		realizarlimpezaCamposMensagem();
		setMensagemID("msg_entre_prmconsulta");
	}
	public void realizarlimpezaCamposMensagem(){
		try {
			setDataInicioConsulta(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(Calendario.hoje())));
			setDataFimConsulta(Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje()));
		} catch (Exception e) {
			setMensagemID("msg_erro");
			setMensagemDetalhada(e);
		}


		setMensagemID("msg_entre_dados");
		setMensagem("");
		setMensagemDetalhada("");
		setSucesso(true);
		setErro(false);
	}

	public void inicializarUsuarioLogado() throws Exception {
		try {
						
			if (getUsuarioLogado().getCodigo() != null || getUsuarioLogado().getCodigo() != 0) {
				getPassivoVO().getResponsavelCadastro().setCodigo(getUsuarioLogado().getCodigo());
				getPassivoVO().getResponsavelCadastro().setNome(getUsuarioLogado().getNome());
				getPassivoVO().getResponsavelCadastro().setAdministrador(getUsuarioLogado().getAdministrador());
			}
			if (getColaboradorResponsavel() != null && getColaboradorResponsavel().getCodigo() != 0 ) {
				getPassivoVO().getColaboradorResponsavel().setCodigo(getColaboradorResponsavel().getCodigo());
				getPassivoVO().getColaboradorResponsavel().setNome(getColaboradorResponsavel().getNome());
				getPassivoVO().getColaboradorResponsavel().setAdministrador(getColaboradorResponsavel().getAdministrador());
				getHistoricoContatoVO().setDiaAbertura(Calendario.hoje());
				
			}
		} catch (Exception e) {
			throw e;
		}
	}

	/**
	 * Rotina responsável por disponibilizar um novo objeto da classe
	 * <code>Passivo</code> para edição pelo usuário da aplicação.
	 */
	public String novo() {
		try {
			setPassivoVO(new PassivoVO());
			setHistoricoContatoVO(new HistoricoContatoVO());
			getHistoricoContatoVO().setFase("CP");
			
			inicializarUsuarioLogado();
			setMsgAlert("");
			setSomenteEditarPassivo(false);
			setSucesso(true);
			setErro(false);
			setMensagemID("msg_entre_dados");
			return "editar";
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
			setSucesso(false);
			setErro(true);
			return "";
		}

	}

	/**
	 * Rotina responsável por disponibilizar os dados de um objeto da classe
	 * <code>Passivo</code> para alteração. O objeto desta classe é
	 * disponibilizado na session da página (request) para que o JSP
	 * correspondente possa disponibilizá-lo para edição.
	 */
	public String editar() throws Exception {
		Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
		try {
			PassivoVO obj = getFacade().getPassivo().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
			setHistoricoContatoVO(getFacade().getHistoricoContato().consultarHistoricoContatoPorCodigoPassivo(codigoConsulta, false, Uteis.NIVELMONTARDADOS_HISTORICOPASSIVO));
			inicializarAtributosRelacionados(obj);
			if(UteisValidacao.emptyString(obj.getObservacao()) && !UteisValidacao.emptyString(getHistoricoContatoVO().getObservacao())) {
				obj.setObservacao(getHistoricoContatoVO().getObservacao());
			}
			obj.setDescricaoEvento(obj.getEvento().getDescricao());
			obj.setNovoObj(false);
			setSomenteEditarPassivo(true);
			obj.registrarObjetoVOAntesDaAlteracao();
			setPassivoVO(obj);
		} catch (Exception e) {
			setSucesso(true);
			setErro(false);
			setMsgAlert("");
			setMensagemID("msg_dados_editar");
		}
		return "editar";
	}
	public void editarPassivoListaFecharMetaDetalhado() throws Exception {
		FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
		setPassivoVO(getFacade().getPassivo().consultarPorChavePrimaria(obj.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
		setSomenteEditarPassivo(true);
	}

	/**
	 * Método responsável inicializar objetos relacionados a classe
	 * <code>PassivoVO</code>. Esta inicialização é necessária por exigência da
	 * tecnologia JSF, que não trabalha com valores nulos para estes atributos.
	 */
	public void inicializarAtributosRelacionados(PassivoVO obj) {
		if (obj.getResponsavelCadastro() == null) {
			obj.setResponsavelCadastro(new UsuarioVO());
		}
		if (obj.getColaboradorResponsavel() == null) {
			obj.setColaboradorResponsavel(new UsuarioVO());
		}
	}

	public void realizarConsultaLogObjetoSelecionado() {
		LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
		String nomeClasse = getPassivoVO().getClass().getSimpleName();
		nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
		loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
		loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
				getPassivoVO().getCodigo(), 0);
	}

	public void realizarConsultaLogObjetoGeral() {
		passivoVO = new PassivoVO();
		realizarConsultaLogObjetoSelecionado();
	}

	public void cancelarAgendamentoPanelAgenda() {
		getHistoricoContatoVO().setAgendaVO(new AgendaVO());
	}

	/**
	 * Rotina responsável por gravar no BD os dados editados de um novo objeto
	 * da classe <code>Passivo</code>. Caso o objeto seja novo (ainda não
	 * gravado no BD) é acionado a operação <code>incluir()</code>. Caso
	 * contrário é acionado o <code>alterar()</code>. Se houver alguma
	 * inconsistência o objeto não é gravado, sendo re-apresentado para o
	 * usuário juntamente com uma mensagem de erro.
	 */
	public void gravar() {
		try {
			if(!getUsuarioLogado().getAdministrador()) {
				getPassivoVO().setEmpresaVO(getEmpresaLogado());
			}
			getFacade().getPassivo().alterarSomentePassivo(getPassivoVO());
                        if(UteisValidacao.emptyNumber(getHistoricoContatoVO().getCodigo())){
                            if(!UteisValidacao.emptyString(getHistoricoContatoVO().getObservacao())){
                                getHistoricoContatoVO().setPassivoVO(getPassivoVO());
                                getFacade().getHistoricoContato().incluir(getHistoricoContatoVO());
                            }
                        } else {
							getHistoricoContatoVO().setObservacao(getPassivoVO().getObservacao());
							getFacade().getHistoricoContato().alterarSemCommit(getHistoricoContatoVO());
                        }
			if(UteisValidacao.emptyString(getPassivoVO().getObservacao()) && !UteisValidacao.emptyString(getHistoricoContatoVO().getObservacao())) {
				getPassivoVO().setObservacao(getHistoricoContatoVO().getObservacao());
			}
			getPassivoVO().setDescricaoEvento(getPassivoVO().getEvento().getDescricao());
			getPassivoVO().formatarObservacao();
			incluirLogAlteracao();
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setManterAbertoRichModal(false);
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setManterAbertoRichModal(true);
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}
	public void incluirLogAlteracao() throws Exception {
		try {
			registrarLogObjetoVO(passivoVO, passivoVO.getCodigo(), "PASSIVO", 0);
		} catch (Exception e) {
			registrarLogErroObjetoVO("PASSIVO", passivoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE RECEPTIVO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
			e.printStackTrace();
		}
		passivoVO.registrarObjetoVOAntesDaAlteracao();
	}

	/**
	 * Rotina responsável por gravar no BD os dados editados de um novo objeto
	 * da classe <code>Passivo</code>. Caso o objeto seja novo (ainda não
	 * gravado no BD) é acionado a operação <code>incluir()</code>. Caso
	 * contrário é acionado o <code>alterar()</code>. Se houver alguma
	 * inconsistência o objeto não é gravado, sendo re-apresentado para o
	 * usuário juntamente com uma mensagem de erro.
	 */
	public void gravarPassivoAgendamento() {
		try {
			if(!getUsuarioLogado().getAdministrador())
				getPassivoVO().setEmpresaVO(getEmpresaLogado());
			getHistoricoContatoVO().setObjecaoVO(new ObjecaoVO());
			getFacade().getPassivo().salvar(getPassivoVO(), getHistoricoContatoVO(), "AG", getEmpresaLogado().getCodigo(), null, null, null, null, null);
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setManterAbertoRichModal(false);
			setSucesso(true);
			setErro(false);
			getPassivoVO().setTipoAgendamentoAnterior(getHistoricoContatoVO().getAgendaVO().getTipoAgendamento());
		} catch (Exception e) {
			setManterAbertoRichModal(true);
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}
	public void validarDadosAgendar(){
		try {
			limparMsg();
			setAbrirModalAgendamento("");
			if(getUsuarioLogado().getAdministrador() || !verificarAberturaMeta()){
				throw new ConsistirException("É necessário fazer a abertura da meta para realizar um agendamento.");
			}
			PassivoVO.validarDados(getPassivoVO(),false);
			setAbrirModalAgendamento("Richfaces.showModalPanel('panelAgenda')");
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}
	
	private boolean verificarAberturaMeta() throws Exception{
        return getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo(), Calendario.hoje());
    }

	/**
	 * Rotina responsável por gravar no BD os dados editados de um novo objeto
	 * da classe <code>Passivo</code>. Caso o objeto seja novo (ainda não
	 * gravado no BD) é acionado a operação <code>incluir()</code>. Caso
	 * contrário é acionado o <code>alterar()</code>. Se houver alguma
	 * inconsistência o objeto não é gravado, sendo re-apresentado para o
	 * usuário juntamente com uma mensagem de erro.
	 */
	public void gravarPassivoObjecao() {
		try {
			if(!getUsuarioLogado().getAdministrador())
				getPassivoVO().setEmpresaVO(getEmpresaLogado());
			ObjecaoVO obj = (ObjecaoVO) context().getExternalContext().getRequestMap().get("objecao");
			getHistoricoContatoVO().setObjecaoVO(obj);
			getFacade().getPassivo().salvar(getPassivoVO(), getHistoricoContatoVO(), "OB",getEmpresaLogado().getCodigo(), null, null, null, null, null);
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setManterAbertoRichModalPanelObjecao(false);
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setManterAbertoRichModalPanelObjecao(true);
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}

	/**
	 * Método responsavel por abrir e fechar o RichModal PanelAgendamento
	 */
	public String getManterAbertoRichModalPanelAgenda() {
		if (!getManterAbertoRichModal()) {
			return "Richfaces.hideModalPanel('panelAgenda');" + getMsgAlert();
		}
		return "";
	}

	/**
	 * Método responsavel por abrir e fechar o RichModal PanelAgendamento
	 */
	public String getFecharRichModalPanelObjecao() {
		if (!getManterAbertoRichModalPanelObjecao()) {
			return "Richfaces.hideModalPanel('panelObjecao');" + getMsgAlert();
		}
		return "";
	}

	public void consultarObjecao() {
		try {
			limparMsg();
			setAbrirModalObjecao("");
			PassivoVO.validarDados(getPassivoVO(),false);
			List objs = getFacade().getObjecao().consultarObjecao(true, "OB", Uteis.NIVELMONTARDADOS_TODOS);
			setListaConsultaObjecao(objs);
			setAbrirModalObjecao("Richfaces.showModalPanel('panelObjecao')");
			setMensagemID("msg_dados_consultados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setListaConsultaObjecao(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	/**
	 * Rotina responsavel por executar as consultas disponiveis no JSP
	 * PassivoCons.jsp. Define o tipo de consulta a ser executada, por meio de
	 * ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como
	 * resultado, disponibiliza um List com os objetos selecionados na sessao da
	 * pagina.
	 */
	public String consultar() {
		try {
			super.consultar();
			List objs = new ArrayList();
			if (getControleConsulta().getCampoConsulta().equals("codigo")) {
				if (getControleConsulta().getValorConsulta().equals("")) {
					getControleConsulta().setValorConsulta("0");
				}
				int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
				objs = getFacade().getPassivo().consultarPorCodigo(valorInt, true, getEmpresaLogado(), Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getControleConsulta().getCampoConsulta().equals("nome")) {
				objs = getFacade().getPassivo().consultarPorNome(getControleConsulta().getValorConsulta(), true,getEmpresaLogado(), Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getControleConsulta().getCampoConsulta().equals("dia")) {
				objs = getFacade().getPassivo().consultarPorDia(getDataConsulta(), getDataConsulta(), true,getEmpresaLogado(), Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getControleConsulta().getCampoConsulta().equals("responsavelCadastro")) {
				objs = getFacade().getPassivo().consultarPorUsuarioResponsavelCadastro(getControleConsulta().getValorConsulta(),getEmpresaLogado(), Uteis.NIVELMONTARDADOS_TODOS);
			}
			setListaConsulta(objs);
			setMensagemID("msg_dados_consultados");
			setSucesso(true);
			setErro(false);
			return "consultar";
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setListaConsulta(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "consultar";
		}
	}

	/**
	 * Operação responsável por processar a exclusão um objeto da classe
	 * <code>PassivoVO</code> Após a exclusão ela automaticamente aciona a
	 * rotina para uma nova inclusão.
	 */
	public String excluir() {
		try {
			getFacade().getPassivo().excluir(passivoVO);
			incluirLogExlusao();
			novo();
			setSucesso(true);
			setErro(false);
			setMensagemID("msg_dados_excluidos");
			return "consultar";
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"passivo\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"passivo\" violates foreign key")){
				setMensagemDetalhada("Este passivo não pode ser excluído!");
			}else {
				setMensagemDetalhada("msg_erro", e.getMessage());
			}
			return "editar";
		}
	}

	/**
	 * Método responsável por processar a consulta na entidade
	 * <code>Colaborador</code> por meio dos parametros informados no richmodal.
	 * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
	 * realizam busca pelos parâmentros informados no richModal montando
	 * automaticamente o resultado da consulta para apresentação.
	 */
	public void consultarColaborador() {
		try {
			List objs = new ArrayList();
			if (getCampoConsultarColaboradorResponsavel().equals("codigo")) {
				if (getValorConsultarColaboradorResponsavel().equals("")) {
					setValorConsultarColaboradorResponsavel("0");
				}
				Integer valorInt = Integer.parseInt(getValorConsultarColaboradorResponsavel());
				objs = getFacade().getColaborador().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarColaboradorResponsavel().equals("nomePessoa")) {
				objs = getFacade().getColaborador().consultarPorNomePessoa(getValorConsultarColaboradorResponsavel(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarColaboradorResponsavel().equals("situacao")) {
				objs = getFacade().getColaborador().consultarPorSituacao(getValorConsultarColaboradorResponsavel(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarColaboradorResponsavel().equals("codAcesso")) {
				objs = getFacade().getColaborador().consultarPorCodAcesso(getValorConsultarColaboradorResponsavel(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			setListaConsultarColaboradorResponsavel(objs);
			setSucesso(true);
			setErro(false);
			setMensagemID("msg_dados_consultados");
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setListaConsultarColaboradorResponsavel(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	public void selecionarColaborador() throws Exception {
		UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("colaborador");
		if (getMensagemDetalhada().equals("")) {
			this.getPassivoVO().setColaboradorResponsavel(obj);
		}
		Uteis.liberarListaMemoria(this.getListaConsultarColaboradorResponsavel());
		this.setValorConsultarColaboradorResponsavel(null);
		this.setCampoConsultarColaboradorResponsavel(null);
	}

	public void limparCampoColaborador() {
		this.getPassivoVO().setColaboradorResponsavel(new UsuarioVO());
	}

	/**
	 * Rotina responsável por preencher a combo de consulta dos RichModal da
	 * telas.
	 */
	public List getTipoConsultarComboColaboradorResponsavel() {
		List itens = new ArrayList();
		itens.add(new SelectItem("codigo", "Código"));
		itens.add(new SelectItem("nomePessoa", "Pessoa"));
		itens.add(new SelectItem("situacao", "Situação"));
		itens.add(new SelectItem("codAcesso", "Código Acesso"));
		return itens;
	}

	/**
	 * Método responsável por processar a consulta na entidade
	 * <code>Colaborador</code> por meio dos parametros informados no richmodal.
	 * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
	 * realizam busca pelos parâmentros informados no richModal montando
	 * automaticamente o resultado da consulta para apresentação.
	 */
	public void consultarResponsavelCadastro() {
		try {
			List objs = new ArrayList();
			if (getCampoConsultarResponsavelCadastro().equals("codigo")) {
				if (getValorConsultarResponsavelCadastro().equals("")) {
					setValorConsultarResponsavelCadastro("0");
				}
				Integer valorInt = Integer.parseInt(getValorConsultarResponsavelCadastro());
				objs = getFacade().getColaborador().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarResponsavelCadastro().equals("nomePessoa")) {
				objs = getFacade().getColaborador().consultarPorNomePessoa(getValorConsultarResponsavelCadastro(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarResponsavelCadastro().equals("situacao")) {
				objs = getFacade().getColaborador().consultarPorSituacao(getValorConsultarResponsavelCadastro(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarResponsavelCadastro().equals("codAcesso")) {
				objs = getFacade().getColaborador().consultarPorCodAcesso(getValorConsultarResponsavelCadastro(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			setListaConsultarResponsavelCadastro(objs);
			setMensagemID("msg_dados_consultados");
		} catch (Exception e) {
			setListaConsultarResponsavelCadastro(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	public void selecionarResponsavelCadastro() throws Exception {
		UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("colaborador");
		if (getMensagemDetalhada().equals("")) {
			this.getPassivoVO().setResponsavelCadastro(obj);
		}
		Uteis.liberarListaMemoria(this.getListaConsultarResponsavelCadastro());
		this.setValorConsultarResponsavelCadastro(null);
		this.setCampoConsultarResponsavelCadastro(null);
	}

	public void limparCampoResponsavelCadastro() {
		this.getPassivoVO().setResponsavelCadastro(new UsuarioVO());
	}

	/**
	 * Rotina responsável por preencher a combo de consulta dos RichModal da
	 * telas.
	 */
	public List getTipoConsultarComboResponsavelCadastro() {
		List itens = new ArrayList();
		itens.add(new SelectItem("codigo", "Código"));
		itens.add(new SelectItem("nomePessoa", "Pessoa"));
		itens.add(new SelectItem("situacao", "Situação"));
		itens.add(new SelectItem("codAcesso", "Código Acesso"));
		return itens;
	}

	/**
	 * Rotina responsável por atribui um javascript com o método de mascara para
	 * campos do tipo Data, CPF, CNPJ, etc.
	 */
	public String getMascaraConsulta() {
		return "";
	}

	/**
	 * Rotina responsável por preencher a combo de consulta da telas.
	 */
	public List getTipoConsultaCombo() {
		List itens = new ArrayList();
		itens.add(new SelectItem("nome", "Nome"));
		itens.add(new SelectItem("codigo", "Código"));
		itens.add(new SelectItem("responsavelCadastro", "Responsável Cadastro"));
		itens.add(new SelectItem("dia", "Dia"));
		return itens;
	}

	public Boolean getApresentarCalendarDia() {
        return getControleConsulta().getCampoConsulta().equals("dia");
    }

	/*
	 * Método responsavel por apresentar o input do tipoAgendamento se o
	 * tipoAgendamento for Aula Experimental apresentar o input na tela
	 */
	public Boolean getApresentarInputTextAulaExperimental() {
        return historicoContatoVO.getAgendaVO().getTipoAgendamento().equals("AE");
    }

	/**
	 * Rotina responsável por organizar a paginação entre as páginas resultantes
	 * de uma consulta.
	 */
	public String inicializarConsultar() {
		setListaConsulta(new ArrayList());
		setMensagemID("msg_entre_prmconsulta");
		return "consultar";
	}

	/**
	 * Operação que libera todos os recursos (atributos, listas, objetos) do
	 * backing bean. Garantindo uma melhor atuação do Garbage Coletor do Java. A
	 * mesma é automaticamente quando realiza o logout.
	 */
	protected void limparRecursosMemoria() {
		super.limparRecursosMemoria();
		passivoVO = null;
	}

	/**
	 * Operação que inicializa as Interfaces Façades com os respectivos objetos
	 * de persistência dos dados no banco de dados.
	 */
	protected boolean inicializarFacades() {
		try {
			super.inicializarFacades();

			return true;
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
			return false;
		}
	}


	public List<ColaboradorVO> executarAutocompleteColaborador(Object suggest) {
		String pref = (String) suggest;
		ArrayList<ColaboradorVO> result;
		try {
			if (pref.equals("%")) {
				result = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarTodosColaboradorComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
			} else {
				result = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarPorNomeColaboradorComLimite(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
			}
		} catch (Exception ex) {
			result = (new ArrayList<ColaboradorVO>());
		}
		return result;
	}

	public List<EventoVO> autocompleteEvento(Object suggest) {
		String pref = (String) suggest;
		ArrayList<EventoVO> result;
		try {
			if (pref.equals("%")) {
				result = (ArrayList<EventoVO>) getFacade().getEvento().consultarTodosEventosComLimite(false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
			}else{result = (ArrayList<EventoVO>) getFacade().getEvento().consultarPorNomeEventoComLimite(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
			}
		} catch (Exception ex) {
			result = (new ArrayList<EventoVO>());
		}
		return result;
	}

	/**
	 * Método responsavel por criar o autocomplete do suggestionBox no campo
	 * Modalidade
	 */
	public List<ModalidadeVO> autocompleteModalidade(Object suggest) {
		String pref = (String) suggest;
		ArrayList<ModalidadeVO> result = new ArrayList<ModalidadeVO>();
		try {
			result = (ArrayList<ModalidadeVO>) getFacade().getModalidade().consultarPorNomeModalidadeComLimite(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
		} catch (Exception ex) {
			result = (new ArrayList<ModalidadeVO>());
		}
		return result;
	}

	public String getCampoConsultarColaboradorResponsavel() {
		return campoConsultarColaboradorResponsavel;
	}

	public void setCampoConsultarColaboradorResponsavel(String campoConsultarColaboradorResponsavel) {
		this.campoConsultarColaboradorResponsavel = campoConsultarColaboradorResponsavel;
	}

	public String getValorConsultarColaboradorResponsavel() {
		return valorConsultarColaboradorResponsavel;
	}

	public void setValorConsultarColaboradorResponsavel(String valorConsultarColaboradorResponsavel) {
		this.valorConsultarColaboradorResponsavel = valorConsultarColaboradorResponsavel;
	}

	public List getListaConsultarColaboradorResponsavel() {
		return listaConsultarColaboradorResponsavel;
	}

	public void setListaConsultarColaboradorResponsavel(List listaConsultarColaboradorResponsavel) {
		this.listaConsultarColaboradorResponsavel = listaConsultarColaboradorResponsavel;
	}

	public String getCampoConsultarResponsavelCadastro() {
		return campoConsultarResponsavelCadastro;
	}

	public void setCampoConsultarResponsavelCadastro(String campoConsultarResponsavelCadastro) {
		this.campoConsultarResponsavelCadastro = campoConsultarResponsavelCadastro;
	}

	public String getValorConsultarResponsavelCadastro() {
		return valorConsultarResponsavelCadastro;
	}

	public void setValorConsultarResponsavelCadastro(String valorConsultarResponsavelCadastro) {
		this.valorConsultarResponsavelCadastro = valorConsultarResponsavelCadastro;
	}

	public List getListaConsultarResponsavelCadastro() {
		return listaConsultarResponsavelCadastro;
	}

	public void setListaConsultarResponsavelCadastro(List listaConsultarResponsavelCadastro) {
		this.listaConsultarResponsavelCadastro = listaConsultarResponsavelCadastro;
	}

	public PassivoVO getPassivoVO() {
		return passivoVO;
	}

	public void setPassivoVO(PassivoVO passivoVO) {
		this.passivoVO = passivoVO;
	}

	/**
	 * @return the dataConsulta
	 */
	public Date getDataConsulta() {
		return dataConsulta;
	}

	/**
	 * @param dataConsulta
	 *            the dataConsulta to set
	 */
	public void setDataConsulta(Date dataConsulta) {
		this.dataConsulta = dataConsulta;
	}

	/**
	 * @param manterAbertoRichModal
	 *            the manterAbertoRichModal to set
	 */
	public void setManterAbertoRichModal(Boolean manterAbertoRichModal) {
		this.manterAbertoRichModal = manterAbertoRichModal;
	}

	/**
	 * @return the manterAbertoRichModal
	 */
	public Boolean getManterAbertoRichModal() {
		if (manterAbertoRichModal == null) {
			manterAbertoRichModal = false;
		}
		return manterAbertoRichModal;
	}

	public void setManterAbertoRichModalPanelObjecao(Boolean manterAbertoRichModalPanelObjecao) {
		this.manterAbertoRichModalPanelObjecao = manterAbertoRichModalPanelObjecao;
	}

	/**
	 * @return the manterAbertoRichModal
	 */
	public Boolean getManterAbertoRichModalPanelObjecao() {
		if (manterAbertoRichModalPanelObjecao == null) {
			manterAbertoRichModalPanelObjecao = false;
		}
		return manterAbertoRichModalPanelObjecao;
	}

	public HistoricoContatoVO getHistoricoContatoVO() {
		if (historicoContatoVO == null) {
			historicoContatoVO = new HistoricoContatoVO();
		}
		return historicoContatoVO;
	}

	public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
		this.historicoContatoVO = historicoContatoVO;
	}

	/**
	 * @param listaConsultaObjecao
	 *            the listaConsultaObjecao to set
	 */
	public void setListaConsultaObjecao(List listaConsultaObjecao) {
		this.listaConsultaObjecao = listaConsultaObjecao;
	}

	/**
	 * @return the listaConsultaObjecao
	 */
	public List getListaConsultaObjecao() {
		if (listaConsultaObjecao == null) {
			listaConsultaObjecao = new ArrayList();
		}
		return listaConsultaObjecao;
	}

	/**
	 * @param somenteEditarPassivo
	 *            the somenteEditarPassivo to set
	 */
	public void setSomenteEditarPassivo(Boolean somenteEditarPassivo) {
		this.somenteEditarPassivo = somenteEditarPassivo;
	}

	/**
	 * @return the somenteEditarPassivo
	 */
	public Boolean getSomenteEditarPassivo() {
		if (somenteEditarPassivo == null) {
			somenteEditarPassivo = false;
		}
		return somenteEditarPassivo;
	}
	/**
	 * @param abrirModalObjecao the abrirModalObjecao to set
	 */
	public void setAbrirModalObjecao(String abrirModalObjecao) {
		this.abrirModalObjecao = abrirModalObjecao;
	}
	/**
	 * @return the abrirModalObjecao
	 */
	public String getAbrirModalObjecao() {
		return abrirModalObjecao;
	}
	/**
	 * @param abrirModalAgendamento the abrirModalAgendamento to set
	 */
	public void setAbrirModalAgendamento(String abrirModalAgendamento) {
		this.abrirModalAgendamento = abrirModalAgendamento;
	}

	public Date getDataInicioConsulta() {
		return dataInicioConsulta;
	}

	public void setDataInicioConsulta(Date dataInicioConsulta) {
		this.dataInicioConsulta = dataInicioConsulta;
	}

	public Date getDataFimConsulta() {
		return dataFimConsulta;
	}

	public void setDataFimConsulta(Date dataFimConsulta) {
		this.dataFimConsulta = dataFimConsulta;
	}

	/**
	 * @return the abrirModalAgendamento
	 */
	public String getAbrirModalAgendamento() {
		return abrirModalAgendamento;
	}
	/**
	 * @param listaSelectItemEmpresa the listaSelectItemEmpresa to set
	 */
	public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
		this.listaSelectItemEmpresa = listaSelectItemEmpresa;
	}

	/**
	 * @return the listaSelectItemEmpresa
	 * @throws Exception 
	 */
	public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
		if(listaSelectItemEmpresa == null){
			montarListaSelectItemEmpresa();
		}
		return listaSelectItemEmpresa;
	}
	@SuppressWarnings("unchecked")
	public void montarListaSelectItemEmpresa() throws Exception {
		this.setListaSelectItemEmpresa(new ArrayList<SelectItem>());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for(EmpresaVO empresa : empresas){
        	this.getListaSelectItemEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
	}

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getPassivo().consultarParaImpressao(getEmpresaLogado().getCodigo(),dataInicioConsulta,dataFimConsulta, filtroConsulta, ordem, campoOrdenacao);
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

	public String getFiltroConsulta() {
		if (filtroConsulta == null) {
			filtroConsulta = "";
		}
		return filtroConsulta;
	}

	public void setFiltroConsulta(String filtroConsulta) {
		this.filtroConsulta = filtroConsulta;
	}

	public void setMsgAlert(String msgAlert) {
		this.msgAlert = msgAlert;
	}

	public String getMsgAlert() {
		if (msgAlert == null) {
			return "";
		}
		return msgAlert;
	}

	public String getObservacao() {
		if (observacao == null) {
			observacao = "";
		}
		return observacao;
	}

	public void setObservacao(String observacao) {
		this.observacao = observacao;
	}

	public boolean isApresentarConvertidos() {
		return apresentarConvertidos;
	}

	public void setApresentarConvertidos(boolean apresentarConvertidos) {
		this.apresentarConvertidos = apresentarConvertidos;
	}

	public void confirmarExcluir(){
		MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
		control.setMensagemDetalhada("", "");
		setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
		control.init("Exclusão de Receptivo",
				"Deseja excluir o Receptivo?",
				this, "excluir", "", "", "", "grupoBtnExcluir");
	}

	public void incluirLogExlusao() throws Exception {
		//LOG - INICIO
		try {
			passivoVO.setObjetoVOAntesAlteracao(new PassivoVO());
			passivoVO.setNovoObj(true);
			registrarLogExclusaoTodosDadosObjetoVO(passivoVO, passivoVO.getCodigo(), "PASSIVO", 0);
		} catch (Exception e) {
			registrarLogErroObjetoVO("PASSIVO", passivoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CIDADE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
			e.printStackTrace();
		}
		//LOG - FIM
	}

	//<editor-fold defaultstate="collapsed" desc="Codigo para criacao de filtro para consulta">

	public void consultarPassivosByFiltro() {
		try {

			setMensagemDetalhada("", "");
			setSucesso(true);
			setErro(false);

			setPassivoVOList(new ArrayList<PassivoVO>());
			if (null != passivoFiltro) {
				UsuarioVO usuarioColaborador = new UsuarioVO();
				if (!UteisValidacao.emptyNumber(passivoFiltro.colaboradorIndicou.getCodigo())) {
					usuarioColaborador = getFacade().getUsuario().consultarPorColaboradorEmpresa(passivoFiltro.colaboradorIndicou.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
				}
				setPassivoVOList(getFacade().getPassivo()
						.consultarPorPeriodoResponsavel(getEmpresaLogado().getCodigo(), passivoFiltro.inicio,
								passivoFiltro.fim,
								usuarioColaborador.getCodigo(),
								false,
								Uteis.NIVELMONTARDADOS_TODOS));
			}
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro", e.getMessage());
			setSucesso(false);
			setErro(true);
		}
	}


	public List<PassivoVO> getPassivoVOList() {
		return passivoVOList;
	}

	public void setPassivoVOList(List<PassivoVO> passivoVOList) {
		this.passivoVOList = passivoVOList;
	}

	public PassivoFiltro getPassivoFiltro() {
		return passivoFiltro;
	}

	public void setPassivoFiltro(PassivoFiltro passivoFiltro) {
		this.passivoFiltro = passivoFiltro;
	}

	public void limparPeriodo() {
		passivoFiltro = new PassivoFiltro();
	}

	public static class PassivoFiltro {
		private Date inicio;
		private Date fim;
		private ColaboradorVO colaboradorIndicou;

		private HttpServletRequest request() {
			return (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
		}


		public void selectColaboradorSuggestionBox() throws Exception {
			ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("colaborador");
			if (colaboradorVO != null) {
				setColaboradorIndicou(colaboradorVO);
			}

		}

		public Date getInicio() {
			return inicio;
		}

		public void setInicio(Date inicio) {
			this.inicio = inicio;
		}

		public Date getFim() {
			return fim;
		}

		public void setFim(Date fim) {
			this.fim = fim;
		}

		public ColaboradorVO getColaboradorIndicou() {
			if (null == colaboradorIndicou) {
				colaboradorIndicou = new ColaboradorVO();
			}
			return colaboradorIndicou;
		}

		public void setColaboradorIndicou(ColaboradorVO colaboradorIndicou) {
			this.colaboradorIndicou = colaboradorIndicou;
		}

	}

	//</editor-fold>
}

package controle.crm;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.basico.*;
import negocio.comuns.crm.MesMetaIndicacaoTO;
import org.json.JSONArray;
import org.json.JSONObject;
import controle.basico.GrupoTelaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.crm.ObjecaoBICRMTO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.GrupoTipoColaboradorVO;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Felipe
 */

public class BusinessIntelligenceCRMControle extends SuperControleRelatorio {

    private Date dataInicio;
    private Date dataFinal;
    private List<UsuarioVO> listaUsuariosSelecionados;
    private String jsonBIResultado;
    private List<FecharMetaVO> totais;
    private List<FecharMetaVO> fases;
    private List<FecharMetaVO> fasesRenovacao;
    private List<FecharMetaVO> fasesRelacionamento;
    private List<FecharMetaVO> fasesStudio;
    private List<FecharMetaVO> fasesCRMExtra;
    private List<FecharMetaVO> totaisResultado;
    private FecharMetaVO metaRenovacao;
    private FecharMetaVO metaFidelizacao;
    private FecharMetaVO metaStudio;
    private FecharMetaVO metaCRMExtra;
    private String jsonBIDesempenhoConsultores;
    private String jsonBIDesempenhoConsultoresLegenda;
    private List<FecharMetaDetalhadoVO> listaClientesMeta;
    private String jsonBIMetaVendas;
    private String jsonBIMetaFidelizacao;
    private String jsonBIMetaStudio;
    private String jsonBIMetaCRMExtra;
    private boolean graficoResultado = false;
    private boolean graficoVendas = false;
    private boolean graficoFidelizacao = false;
    private boolean graficoStudio = false;
    private boolean graficoCRMExtra = false;
    private Integer indAgendaLigacoesPendentes;
    private Integer indIndicacoesSemContato;
    private Integer indContatoReceptivo;
    private List<FecharMetaVO> listaMetas;
    private List<PassivoVO> listaContatoReceptivo;
    private List<FecharMetaVO> listaFecharMetaAgendadosPeriodo;
    private List<FecharMetaVO> listaFecharMetaIndicacoesPeriodo;
    private Integer indObjecoes;
    private List<ObjecaoBICRMTO> listaIndObjecoes;
    private List<ObjecaoBICRMTO> listaIndObjecoesTotais;
    private String metaBuscarDetalhadaVendas;
    private String jsonDetalhadoVendas;
    private String metaBuscarDetalhadaFidelizacao;
    private String jsonDetalhadoFidelizacao;
    private String metaBuscarDetalhadaStudio;
    private String jsonDetalhadoStudio;
    private String metaBuscarDetalhadaCRMExtra;
    private String jsonDetalhadoCRMExtra;
    private String metaBuscarDetalhadaResultado;
    private String jsonDetalhadoResultado;
    private String modalMensagemGenerica;
    private Date dataInicioBIDesempenho;
    private Date dataFinalBIDesempenho;
    private String totalUsuariosSelecionados;
    private String jsonBIObjecoesPorQuantidade;
    private String jsonBIObjecoesPorFase;
    private List<Integer> objecoesOutros;
    private Integer tipoBIObjecao;
    private List<ObjecaoBICRMTO> listaBIObjecoesPorQuantidade;
    private List<ObjecaoBICRMTO> listaBIObjecoesPorFase;
    private Integer indClientesObjecaoDefinitiva;
    private List<ItemRelatorioTO> listaClientesObjecaoDefinitiva;

    private List<GrupoTipoColaboradorVO> listaGrupoTipoColaborador;
    private boolean somenteAtivos = true;

    private List<GrupoColaboradorVO> listaGrupoColaborador;
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;

    private Integer codigoObjecao = 0;
    private String tituloObjecao = "";
    private boolean porFase = false;
    
    private List<ItemRelatorioTO> listaClientePorObjecao;

    private Integer quantidadeClientePorObjecao = 0;
    
    private boolean mostrarTodosFiltros = true;

    private String oncompleteRetorno = "";
    private String faseSelecionada = null;
    
    private String codigosMetaExtraIndividual;
    
    private String codigosMetaExtra;
    
    private String codigosMetaTodos;
    
    private Integer quantidadeConsultorSelecionado;

    private boolean metaExtra;

    private String tipoColaborador = "";
    private List<SelectItem> listaSelectItemTipoColaborador;
    
    public BusinessIntelligenceCRMControle() {
        novo();
    }

    public final void novo() {
        try {
            setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            mesAtual();
            preencherDatasBIDesempenho();
            preencherListaSelectItemTipoColaboradores();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void preencherListaSelectItemTipoColaboradores() {
        List<SelectItem> listaSelectItemTipoColaboradorEnum = new ArrayList<SelectItem>();
        for (TipoColaboradorEnum tipoColaboradorEnum : TipoColaboradorEnum.values()) {
            listaSelectItemTipoColaboradorEnum.add(new SelectItem(tipoColaboradorEnum.getSigla(), tipoColaboradorEnum.getDescricao()));
        }
        Ordenacao.ordenarLista(listaSelectItemTipoColaboradorEnum, "label");

        List<SelectItem> listaSelectItemTipoColaborador = new ArrayList<SelectItem>();
        listaSelectItemTipoColaborador.add(new SelectItem("", "TODOS"));
        listaSelectItemTipoColaborador.addAll(listaSelectItemTipoColaboradorEnum);
        setListaSelectItemTipoColaborador(listaSelectItemTipoColaborador);
    }


    public String carregarBusinessIntelligence() {
        try {
            getFuncionalidadeControle().setarFuncionalidade();
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

            MetaCRMControle metaCRMControle = (MetaCRMControle) context().getExternalContext().getSessionMap().get(MetaCRMControle.class.getSimpleName());
            if (metaCRMControle != null) {
                metaCRMControle.setApresentarModalObjecaoDefinitiva(false);
            }

            novo();

            if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
            montarListaUsuariosCRM();

            //SELECIONAR TODOS DO GRUPO DE CONSULTORES
            for (GrupoTipoColaboradorVO grupo : getListaGrupoTipoColaborador()) {
                if (grupo.getTipoColaborador() != null && grupo.getTipoColaborador().equals(TipoColaboradorEnum.CONSULTOR)) {
                    for (UsuarioVO usuarioVO : grupo.getUsuarios()) {
                        usuarioVO.setSelecionado(true);
                        }
                    }
                }
            } else  {
                if (UteisValidacao.emptyList(getListaUsuariosSelecionados()) && !getUsuarioLogado().getAdministrador()) {
                    List estaEmGrupo = getFacade().getGrupoColaborador().consultarGrupoColaboradorPorCodigoColaborador(getUsuarioLogado().getColaboradorVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0);
                    if (!UteisValidacao.emptyList(estaEmGrupo)) {
                        List<UsuarioVO> usuarioVOList = new ArrayList<UsuarioVO>();
                        usuarioVOList.add(getUsuarioLogado());
                        setListaUsuariosSelecionados(usuarioVOList);
                    }
                }

                montarListaGrupoColaborador();
                validarColaboradorLogado();

                Integer contador = 0;
                //SELECIONAR TODOS OS CONSULTORES
                List<UsuarioVO> listaUsuariosConsultores = new ArrayList<UsuarioVO>();
                if (!UteisValidacao.emptyList(getListaGrupoColaborador())) {
                    for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
                        if (grupo.getTipoGrupo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
                            for (GrupoColaboradorParticipanteVO participanteVO : grupo.getGrupoColaboradorParticipanteVOs()) {
                                listaUsuariosConsultores.add(participanteVO.getUsuarioParticipante());
                                participanteVO.setGrupoColaboradorParticipanteEscolhido(true);
                                contador++;
                            }
                        }
                    }
                    setListaUsuariosSelecionados(listaUsuariosConsultores);
                }

                setQuantidadeConsultorSelecionado(contador);
                
                Integer total = getListaUsuariosSelecionados().size();
                setTotalUsuariosSelecionados(total.toString());
            }

            notificarRecursoEmpresa(RecursoSistema.CRM_BI);
            processarDados();
            montarSucesso("");

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            System.out.println("carregarBusinessIntelligence: " + (d2.getTime() - d1.getTime()));

            return "telaBICRM";
        } catch (Exception e) {
            montarErro(e);
        }
        return null;
    }

    public void validarDados() throws Exception {
        if (UteisValidacao.emptyList(getListaUsuariosSelecionados())) {
            throw new Exception("Selecione pelo menos um usuário.");
        }

        if (Calendario.menor(dataFinal, dataInicio)) {
            throw new Exception("A DATA FINAL da pesquisa deve ser maior que a DATA DE INÍCIO");
        }
    }

    public void mesAtual() throws Exception {
        setDataInicio(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
        setDataFinal(Uteis.obterUltimoDiaMes(Calendario.hoje()));
    }

    public void preencherDatasBIDesempenho() throws Exception {
        Date mesInicial = Uteis.obterDataAnterior(6);
        setDataInicioBIDesempenho(Uteis.obterPrimeiroDiaMes(mesInicial));
        setDataFinalBIDesempenho(Uteis.obterUltimoDiaMes(Calendario.hoje()));
    }

    public void mesAnterior() throws Exception {
        Date mesAnterior = Uteis.obterDataAnterior(1);
        setDataInicio(Uteis.obterPrimeiroDiaMes(mesAnterior));
        setDataFinal(Uteis.obterUltimoDiaMes(mesAnterior));
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<UsuarioVO> getListaUsuariosSelecionados() {
        if (listaUsuariosSelecionados == null) {
            listaUsuariosSelecionados = new ArrayList<UsuarioVO>();
        }
        return listaUsuariosSelecionados;
    }

    public void setListaUsuariosSelecionados(List<UsuarioVO> listaUsuariosSelecionados) {
        this.listaUsuariosSelecionados = listaUsuariosSelecionados;
    }

    public String getJsonBIResultado() {
        if (jsonBIResultado == null) {
            jsonBIResultado = "[]";
        }
        return jsonBIResultado;
    }

    public void setJsonBIResultado(String jsonBIResultado) {
        this.jsonBIResultado = jsonBIResultado;
    }

    public void processarDados() {
        try {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

            if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
                adicionarUsuariosCRMListaSelecionados(false);
            } else {
                adicionarUsuarioListaSelecionados();
            }

            setModalMensagemGenerica("");

            validarDados();

            totalizarBIsVendasFidelizacaoStudioCRMExtra();
            montarJSONResultado();
            montarJSONMetaVendas();
            montarJSONMetaFidelizacao();
            montarJSONMetaStudio();
            montarJSONMetaCRMExtra();
            montarJSONDesempenhoConsultores();
            montarJSONObjecoes();
            montarIndicadores();
            setModalMensagemGenerica("");

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            System.out.println("Tempo Total Processar BI: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Business Intelligence", e.getMessage(), true, null, null, "bicrm, indicadoresBICRM");
        }
    }

    public void montarListaUsuariosCRM() {
        try {
            setListaGrupoTipoColaborador(new ArrayList<GrupoTipoColaboradorVO>());
            List<UsuarioVO> usuarios = getFacade().getUsuario().consultarListaUsuariosCRM(getEmpresaLogado().getCodigo(), !getConfiguracaoSistemaCRMVO().isApresentarColaboradoresInativos(), false);
            Map<TipoColaboradorEnum, List<UsuarioVO>> map = new HashMap<TipoColaboradorEnum, List<UsuarioVO>>();
            for (UsuarioVO usuarioVO : usuarios) {
                if (!map.containsKey(usuarioVO.getTipoColaboradorEnum())) {
                    List<UsuarioVO> lista = new ArrayList<UsuarioVO>();
                    lista.add(usuarioVO);
                    map.put(usuarioVO.getTipoColaboradorEnum(), lista);
                } else {
                    List<UsuarioVO> lista = map.get(usuarioVO.getTipoColaboradorEnum());
                    boolean contem = false;
                    for (UsuarioVO usu : lista) {
                        if (usu.getCodigo().equals(usuarioVO.getCodigo())) {
                         contem = true;
                        }
                    }
                    if (!contem) {
                        lista.add(usuarioVO);
                    }
                    map.put(usuarioVO.getTipoColaboradorEnum(), lista);
                }
            }

            List<GrupoTipoColaboradorVO> listaGrupos = new ArrayList<GrupoTipoColaboradorVO>();
            for (Map.Entry<TipoColaboradorEnum, List<UsuarioVO>> mapa : map.entrySet()) {
                GrupoTipoColaboradorVO grupo = new GrupoTipoColaboradorVO();
                grupo.setTipoColaborador(mapa.getKey());
                grupo.setUsuarios(mapa.getValue());
                listaGrupos.add(grupo);
            }
            Ordenacao.ordenarLista(listaGrupos, "tipoColaborador_Apresentar");
            setListaGrupoTipoColaborador(listaGrupos);
        } catch (Exception e) {
            setListaGrupoTipoColaborador(new ArrayList<>());
        }
    }

    public void adicionarUsuariosCRMListaSelecionados(boolean selecionarUsuarioLogado) throws Exception {
        List<UsuarioVO> novaListaUsuario = new ArrayList<>();
        for (GrupoTipoColaboradorVO grupoTipoColaboradorVO : getListaGrupoTipoColaborador()) {
            for (UsuarioVO usuarioVO : grupoTipoColaboradorVO.getUsuarios()) {
                if (usuarioVO.isSelecionado() || (selecionarUsuarioLogado && usuarioVO.getCodigo().equals(getUsuarioLogado().getCodigo()))) {
                    novaListaUsuario.add(usuarioVO);
                }
            }
        }
        setListaUsuariosSelecionados(novaListaUsuario);
        int total = getListaUsuariosSelecionados().size();
        setTotalUsuariosSelecionados(String.valueOf(total));
    }

    public void totalizarBIsVendasFidelizacaoStudioCRMExtra() {
        Integer empresa = null;
        try {
            empresa = getEmpresaLogado().getCodigo();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        totalizarBIsVendasFidelizacaoStudioCRMExtra(getDataInicio(), getDataFinal(),empresa, getListaUsuariosSelecionados());
    }
    
    public void totalizarBIsVendasFidelizacaoStudioCRMExtra(Date inicio, Date fim,  Integer empresa, List<UsuarioVO> usuariosSelecionados) {
        try {
            totais = new ArrayList<>();
            List<FecharMetaVO> lista = getFacade().getFecharMeta().consultarPorPeriodoColaboradoresResponsaveisBI(
                    inicio, fim, 
                    usuariosSelecionados, false, false, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            NAO É MAIS UTILIZADO NÃO TEM O INDICADOR DE INDICACAO NOS RESULTADOS
//            double qtdDeColaboradores = getFacade().getFecharMeta().contarColaboradoresNasMetasPorPeriodoColaboradoresResponsaveisBI(getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), false, true, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //LISTA SERÁ UTILIZADA PARA MONTAR OS INDICADORES
            setListaMetas(lista);

            // percorre a lista consultada
            for (FecharMetaVO meta : lista) {
                FecharMetaVO alterar = null;
                // verifica se a meta ja esta na lista final
                for (FecharMetaVO aux : totais) {
                    if (meta.getIdentificadorMeta().equals(aux.getIdentificadorMeta())) {
                        if (meta.getFase().getTipoFase().equals(TipoFaseCRM.CRMEXTRA) && !meta.getMalaDiretaCRMExtra().getCodigo().equals(aux.getMalaDiretaCRMExtra().getCodigo())) {
                            continue;
                        }
                        alterar = aux;
                        break;
                    }
                }
                // se nao encontrou a meta
                if (alterar == null) {
                    if (meta.getMeta().intValue() > 0) {
                        meta.setPorcentagem((meta.getMetaAtingida() + meta.getRepescagem()) * 100 / meta.getMeta());
                    }
                    meta.setPerc(Formatador.formatarValorMonetarioSemMoeda(meta.getPorcentagem()) + "%");
                    // adiciona na lista final
                    totais.add(meta);
                } else {
                    // senao altera os valores ja presentes na lista
                    alterar.setMeta(meta.getMeta() + alterar.getMeta());
                    if (meta.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                        alterar.setMetaAtingida(getFacade().getFecharMeta().consultarTotalIndicadoBI(
                                inicio, fim,
                                usuariosSelecionados, false, false, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }else {
                        alterar.setMetaAtingida(meta.getMetaAtingida() + alterar.getMetaAtingida());
                    }
                    alterar.setRepescagem(meta.getRepescagem() + alterar.getRepescagem());
                    if (alterar.getMeta() > 0) {
                        alterar.setPorcentagem((alterar.getMetaAtingida() + alterar.getRepescagem()) * 100 / alterar.getMeta());
                    }
                    alterar.setPerc(Formatador.formatarValorMonetarioSemMoeda(alterar.getPorcentagem()) + "%");
                }
            }

            calcularTotalizadorMetaIndicacao(inicio, fim, usuariosSelecionados);


            totaisResultado = new ArrayList<>();
            fases = new ArrayList<>();

            FecharMetaVO total = new FecharMetaVO();
            metaRenovacao = new FecharMetaVO();
            metaFidelizacao = new FecharMetaVO();
            metaStudio = new FecharMetaVO();
            metaCRMExtra = new FecharMetaVO();
            total.setIdentificadorMeta("TO");
            for (FecharMetaVO auxTotal : totais) {
                if (auxTotal.getOrdemTotalizador() > 0) {
                    if (auxTotal.isFaseConversao()) {
                        calcularTotalConversao(auxTotal, new ArrayList<>(totais));
                        totaisResultado.add(auxTotal);
                    } else {
                        fases.add(auxTotal);
                        total.setMeta(total.getMeta() + auxTotal.getMeta());
                        total.setMetaAtingida(total.getMetaAtingida() + auxTotal.getMetaAtingida());
                        total.setRepescagem(total.getRepescagem() + auxTotal.getRepescagem());
                        if (total.getMeta() > 0) {
                            total.setPorcentagem((total.getMetaAtingida() + total.getRepescagem()) * 100 / total.getMeta());
                        }
                        total.setPerc(Formatador.formatarValorMonetarioSemMoeda(total.getPorcentagem()) + "%");
                    }
                }
            }


            //CONVERSÃO RECEPTIVO - PASSIVO
            Integer totalPassivo = getFacade().getPassivo().contarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo());
            Integer totalPassivoConvertivo = getFacade().getPassivo().contarPassivoConvertidosPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo());
            inserirTotalizadorConversao(FasesCRMEnum.CONVERSAO_PASSIVO, totalPassivo, totalPassivoConvertivo);

            //CONVERSÃO LEADS
            Integer totalLeads = getFacade().getLead().contarLeadPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo(), true, false);
            Integer totalLeadConvertivo = getFacade().getLead().contarLeadPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo(), true, true);
            inserirTotalizadorConversao(FasesCRMEnum.CONVERSAO_LEAD, totalLeads, totalLeadConvertivo);

            Ordenacao.ordenarLista(fases, "ordemTotalizador");
            Ordenacao.ordenarLista(totaisResultado, "ordemTotalizador");
            //Usado para divir metas Relacionamento // Renovação // Studio // CRMExtra
            montarMetasRenovacaoRelacionamento();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void calcularTotalizadorMetaIndicacao(Date inicio, Date fim,  List<UsuarioVO> usuariosSelecionados) throws Exception {
        for (FecharMetaVO meta : totais) {
            if (meta.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                boolean abertoSabado = getConfiguracaoSistemaCRMVO().getAbertoSabado();
                boolean abertoDomingo = getConfiguracaoSistemaCRMVO().getAbertoDomingo();
                int qtdMetaConfig = getConfiguracaoSistemaCRMVO().getIndicacoesMes();

                double metaPorConsultor = 0.0;
                double quantidadeUsuariosConsultor = 0.0;

                List<Date> mesesEntre = Uteis.getMesesEntreDatas(inicio, fim);
                if (mesesEntre != null) {
                    for (Date mesAnalisar : mesesEntre) {
                        MesMetaIndicacaoTO mesMetaIndicacaoTO = new MesMetaIndicacaoTO(mesAnalisar, qtdMetaConfig, abertoSabado, abertoDomingo);
                        mesMetaIndicacaoTO.calcular(inicio, fim);
                        metaPorConsultor += mesMetaIndicacaoTO.getMetaMesAnalisado();
                    }
                }

                for (GrupoColaboradorVO grupoColaborador : getListaGrupoColaborador()) {
                    if (grupoColaborador.getTipoGrupo() != null && grupoColaborador.getTipoGrupo().equals("CO")) {
                        quantidadeUsuariosConsultor += grupoColaborador.getGrupoColaboradorParticipanteVOs().stream()
                                .filter(GrupoColaboradorParticipanteVO::getGrupoColaboradorParticipanteEscolhido).count();
                    }
                }
                for(UsuarioVO usuarioVO : usuariosSelecionados ){
                    if(usuarioVO.getTipoColaboradorEnum() != null && usuarioVO.getTipoColaboradorEnum().getSigla().equals("CO")){
                        quantidadeUsuariosConsultor +=1;
                    }
                }

                meta.setMeta(metaPorConsultor * quantidadeUsuariosConsultor);
                if (meta.getMeta() > 0) {
                    meta.setPorcentagem((meta.getMetaAtingida() + meta.getRepescagem()) * 100 / meta.getMeta());
                }
                meta.setPerc(Formatador.formatarValorMonetarioSemMoeda(meta.getPorcentagem()) + "%");
            }
        }
    }

    private void inserirTotalizadorConversao(FasesCRMEnum fase, Integer total, Integer totalConvertivo) {
        FecharMetaVO conversaoPassivo = new FecharMetaVO();
        conversaoPassivo.setFase(fase);
        conversaoPassivo.setIdentificadorMeta(fase.getSigla());
        conversaoPassivo.setMeta(Double.parseDouble(total.toString()));
        conversaoPassivo.setMetaAtingida(Double.parseDouble(totalConvertivo.toString()));
        conversaoPassivo.setRepescagem(0.0);
        conversaoPassivo.calcularPorcentagem();
        totaisResultado.add(conversaoPassivo);
    }

    public void montarMetasRenovacaoRelacionamento() {
        
        StringBuffer codigosMetaExtra = new StringBuffer("");
        StringBuilder codigosMetaExtraIndividual = new StringBuilder("");
        StringBuffer codigosMetaTodos = new StringBuffer("");
        getFasesRelacionamento().clear();
        getFasesRenovacao().clear();
        getFasesStudio().clear();
        getFasesCRMExtra().clear();
        HashMap<Integer,FecharMetaVO> mapaMetaExtraIndividual = new HashMap<Integer, FecharMetaVO>();
        for (FecharMetaVO fecharMetaVO : fases) {
            if (fecharMetaVO.getFase().getTipoFase() == TipoFaseCRM.VENDAS) {
                getFasesRenovacao().add(fecharMetaVO);
                //INDICACOES NÃO SOMA NO TOTAL
                if (!fecharMetaVO.getFase().equals(FasesCRMEnum.INDICACOES)) {
                    getMetaRenovacao().setMeta(getMetaRenovacao().getMeta() + fecharMetaVO.getMeta());
                    getMetaRenovacao().setMetaAtingida(getMetaRenovacao().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                    getMetaRenovacao().setRepescagem(getMetaRenovacao().getRepescagem() + fecharMetaVO.getRepescagem());
                }
            } else if (fecharMetaVO.getFase().getTipoFase() == TipoFaseCRM.RETENCAO) {
                getFasesRelacionamento().add(fecharMetaVO);
                getMetaFidelizacao().setMeta(getMetaFidelizacao().getMeta() + fecharMetaVO.getMeta());
                getMetaFidelizacao().setMetaAtingida(getMetaFidelizacao().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                getMetaFidelizacao().setRepescagem(getMetaFidelizacao().getRepescagem() + fecharMetaVO.getRepescagem());
            } else if (fecharMetaVO.getFase().getTipoFase() == TipoFaseCRM.ESTUDIO) {
                getFasesStudio().add(fecharMetaVO);
                getMetaStudio().setMeta(getMetaStudio().getMeta() + fecharMetaVO.getMeta());
                getMetaStudio().setMetaAtingida(getMetaStudio().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                getMetaStudio().setRepescagem(getMetaStudio().getRepescagem() + fecharMetaVO.getRepescagem());
            } else if (fecharMetaVO.getFase().getTipoFase() == TipoFaseCRM.CRMEXTRA) {
                if (codigosMetaTodos.toString().isEmpty()) {
                    codigosMetaTodos.append(fecharMetaVO.getCodigo());
                }else{
                    codigosMetaTodos.append(",").append(fecharMetaVO.getCodigo());
                }
                boolean adicionarQuantidade = true;
                //Verifica se a meta e Individual.
                if(fecharMetaVO.getMalaDiretaCRMExtra().getMetaExtraIndividual()) {
                    if (codigosMetaExtra.toString().isEmpty()) {
                        codigosMetaExtra.append(fecharMetaVO.getCodigo());
                    } else {
                        codigosMetaExtra.append(",").append(fecharMetaVO.getCodigo());
                    }
                    //Verifica se tem mais de um usuatio selecionado.
                    if (getQuantidadeConsultorSelecionado() > 1) {
                        if (codigosMetaExtraIndividual.toString().isEmpty()) {
                            codigosMetaExtraIndividual.append(fecharMetaVO.getCodigo());
                        }else{
                            codigosMetaExtraIndividual.append(",").append(fecharMetaVO.getCodigo());
                        }
                        
                        //Verifica se a lista não esta fazia
                        if (!getFasesCRMExtra().isEmpty()) {
                            //Verifica se a meta esta na lista
                            boolean jaEstaNaLista = false;
                            for (FecharMetaVO fecharMetaVO1 : getFasesCRMExtra()) {
                                if (fecharMetaVO1.getNomeMetaCRMExtra_Apresentar().equals(fecharMetaVO.getNomeMetaCRMExtra_Apresentar())) {
                                    jaEstaNaLista = true;
                                    break;
                                }
                            }
                            if (jaEstaNaLista) {
                                //Se a meta individual estiver na meta ele soma as metas.
                                adicionarQuantidade = false;
                                for (FecharMetaVO fecharMetaVO1 : getFasesCRMExtra()) {
                                    FecharMetaVO fecharMetaTotal = new FecharMetaVO();
                                    if (fecharMetaVO1.getMalaDiretaCRMExtra().getMetaExtraIndividual() && fecharMetaVO.getMeta() > 0.0 && fecharMetaVO1.getNomeMeta().equals(fecharMetaVO.getNomeMeta()) ) {
                                        fecharMetaTotal.setMeta(fecharMetaVO1.getMeta() + fecharMetaVO.getMeta());
                                        fecharMetaTotal.setMetaAtingida(fecharMetaVO1.getMetaAtingida()+ fecharMetaVO.getMetaAtingida());
                                        fecharMetaVO1.setMeta(fecharMetaTotal.getMeta());
                                        fecharMetaVO1.setMetaAtingida(fecharMetaTotal.getMetaAtingida());
                                        fecharMetaVO1.calcularPorcentagem();
                                        StringBuilder codigo = new StringBuilder("");
                                        if (fecharMetaVO1.getCodigoMetaExtraSomada().isEmpty()) {
                                            codigo.append(fecharMetaVO.getCodigo());
                                            codigo.append(",").append(fecharMetaVO1.getCodigo());
                                        }else{
                                            codigo.append(fecharMetaVO1.getCodigoMetaExtraSomada()).append(",").append(fecharMetaVO.getCodigo());
                                        }
                                        
                                        fecharMetaVO1.setCodigoMetaExtraSomada(codigo.toString());
                                        mapaMetaExtraIndividual.put(fecharMetaVO.getMalaDiretaCRMExtra().getCodigo(),fecharMetaVO);

                                        getMetaCRMExtra().setMeta(getMetaCRMExtra().getMeta() + fecharMetaVO.getMeta());
                                        getMetaCRMExtra().setMetaAtingida(getMetaCRMExtra().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                                        getMetaCRMExtra().setRepescagem(getMetaCRMExtra().getRepescagem() + fecharMetaVO.getRepescagem());
                                        getMetaCRMExtra().calcularPorcentagem();
                                    }
                                }
                            }else{
                                //Se a lista estiver fazia ele adiciona o primeiro
                                getFasesCRMExtra().add(fecharMetaVO);
                                mapaMetaExtraIndividual.put(fecharMetaVO.getMalaDiretaCRMExtra().getCodigo(),fecharMetaVO);
                            }
                        } else {
                            adicionarQuantidade = false;
                            getFasesCRMExtra().add(fecharMetaVO);
                            mapaMetaExtraIndividual.put(fecharMetaVO.getMalaDiretaCRMExtra().getCodigo(),fecharMetaVO);
                        }
                    }else{
                        if (mapaMetaExtraIndividual.get(fecharMetaVO.getMalaDiretaCRMExtra().getCodigo()) == null) {
                            getFasesCRMExtra().add(fecharMetaVO);
                            mapaMetaExtraIndividual.put(fecharMetaVO.getMalaDiretaCRMExtra().getCodigo(),fecharMetaVO);
                            
                            if (codigosMetaExtraIndividual.toString().isEmpty()) {
                                codigosMetaExtraIndividual.append(fecharMetaVO.getCodigo());
                            }else{
                                codigosMetaExtraIndividual.append(",").append(fecharMetaVO.getCodigo());
                            }
                        } else {
                            adicionarQuantidade = false;
                        }
                    }
                } else {
                    //Vai colocar a Meta Extra não sendo individual e validar se naõ tem mais de um consultor selecionado.
                    if (getQuantidadeConsultorSelecionado() > 1) {
                        if (!getFasesCRMExtra().isEmpty()) {
                            boolean adicionar = true;
                            for (FecharMetaVO fecharMetaVO1 : getFasesCRMExtra()) {
                                if (fecharMetaVO1.getNomeMetaCRMExtra_Apresentar().equals(fecharMetaVO.getNomeMetaCRMExtra_Apresentar())) {
                                    fecharMetaVO1.setMeta(fecharMetaVO1.getMeta()+ fecharMetaVO.getMeta());
                                    fecharMetaVO1.setMetaAtingida(fecharMetaVO1.getMetaAtingida()+ fecharMetaVO.getMetaAtingida());
                                    fecharMetaVO1.setRepescagem(fecharMetaVO1.getRepescagem()+ fecharMetaVO.getRepescagem());
                                    fecharMetaVO1.calcularPorcentagem();
                                    StringBuilder codigo = new StringBuilder("");
                                    if (fecharMetaVO1.getCodigoMetaExtraSomada().isEmpty()) {
                                        codigo.append(fecharMetaVO.getCodigo());
                                        codigo.append(",").append(fecharMetaVO1.getCodigo());
                                    }else{
                                        codigo.append(fecharMetaVO1.getCodigoMetaExtraSomada()).append(",").append(fecharMetaVO.getCodigo());
                                    }
                                    
                                    fecharMetaVO1.setCodigoMetaExtraSomada(codigo.toString());
                                    getMetaCRMExtra().setMeta(getMetaCRMExtra().getMeta() + fecharMetaVO.getMeta());
                                    getMetaCRMExtra().setMetaAtingida(getMetaCRMExtra().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                                    getMetaCRMExtra().setRepescagem(getMetaCRMExtra().getRepescagem() + fecharMetaVO.getRepescagem());
                                    getMetaCRMExtra().calcularPorcentagem();

                                    adicionar = false;
                                    adicionarQuantidade = false;
                                    break;
                                }
                            }
                            if (adicionar) {
                                getFasesCRMExtra().add(fecharMetaVO);

                                if (codigosMetaExtra.toString().isEmpty()) {
                                    codigosMetaExtra.append(fecharMetaVO.getCodigo());
                                } else {
                                    codigosMetaExtra.append(",").append(fecharMetaVO.getCodigo());
                                }
                            }
                        }else{
                            getFasesCRMExtra().add(fecharMetaVO);

                            if (codigosMetaExtra.toString().isEmpty()) {
                                codigosMetaExtra.append(fecharMetaVO.getCodigo());
                            } else {
                                codigosMetaExtra.append(",").append(fecharMetaVO.getCodigo());
                            }
                        }
                    }else{
                        //Esse parte veirifica se a lista esta vazia.
                        if (getFasesCRMExtra().isEmpty()) {
                            getFasesCRMExtra().add(fecharMetaVO);

                            getMetaCRMExtra().setMeta(getMetaCRMExtra().getMeta() + fecharMetaVO.getMeta());
                            getMetaCRMExtra().setMetaAtingida(getMetaCRMExtra().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                            getMetaCRMExtra().setRepescagem(getMetaCRMExtra().getRepescagem() + fecharMetaVO.getRepescagem());
                            getMetaCRMExtra().calcularPorcentagem();

                            if (codigosMetaExtra.toString().isEmpty()) {
                                codigosMetaExtra.append(fecharMetaVO.getCodigo());
                            } else {
                                codigosMetaExtra.append(",").append(fecharMetaVO.getCodigo());
                            }
                        }else{
                            //Se a meta ja esta na lista não adiciona
                            boolean jaEstaNaLista = false;
                            for (FecharMetaVO fecharMetaVO1 : getFasesCRMExtra()) {
                                if (fecharMetaVO1.getNomeMetaCRMExtra_Apresentar().equals(fecharMetaVO.getNomeMetaCRMExtra_Apresentar())) {
                                    jaEstaNaLista = true;
                                    break;
                                }
                            }
                            if (!jaEstaNaLista) {
                                getFasesCRMExtra().add(fecharMetaVO);

                                getMetaCRMExtra().setMeta(getMetaCRMExtra().getMeta() + fecharMetaVO.getMeta());
                                getMetaCRMExtra().setMetaAtingida(getMetaCRMExtra().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                                getMetaCRMExtra().setRepescagem(getMetaCRMExtra().getRepescagem() + fecharMetaVO.getRepescagem());
                                getMetaCRMExtra().calcularPorcentagem();

                                if (codigosMetaExtra.toString().isEmpty()) {
                                    codigosMetaExtra.append(fecharMetaVO.getCodigo());
                                } else {
                                    codigosMetaExtra.append(",").append(fecharMetaVO.getCodigo());
                                }
                            }
                        }
                        
                        adicionarQuantidade = false;
                    }
                }

                if (adicionarQuantidade) {
                    getMetaCRMExtra().setMeta(getMetaCRMExtra().getMeta() + fecharMetaVO.getMeta());
                    getMetaCRMExtra().setMetaAtingida(getMetaCRMExtra().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
                    getMetaCRMExtra().setRepescagem(getMetaCRMExtra().getRepescagem() + fecharMetaVO.getRepescagem());
                    getMetaCRMExtra().calcularPorcentagem();
                }
            }
            
        }

        setCodigosMetaExtraIndividual(codigosMetaExtraIndividual.toString());
        setCodigosMetaExtra(codigosMetaExtra.toString());
        setCodigosMetaTodos(codigosMetaTodos.toString());
        if (getMetaFidelizacao().getMeta().intValue() != 0) {
            getMetaFidelizacao().setPorcentagem(((getMetaFidelizacao().getMetaAtingida() + getMetaFidelizacao().getRepescagem()) * 100) / getMetaFidelizacao().getMeta());
        }
        if (getMetaRenovacao().getMeta().intValue() != 0) {
            getMetaRenovacao().setPorcentagem(((getMetaRenovacao().getMetaAtingida() + getMetaRenovacao().getRepescagem()) * 100) / getMetaRenovacao().getMeta());
        }
        if (getMetaStudio().getMeta().intValue() != 0) {
            getMetaStudio().setPorcentagem(((getMetaStudio().getMetaAtingida() + getMetaStudio().getRepescagem()) * 100) / getMetaStudio().getMeta());
        }
        if (getMetaCRMExtra().getMeta().intValue() != 0) {
            getMetaCRMExtra().setPorcentagem(((getMetaCRMExtra().getMetaAtingida() + getMetaCRMExtra().getRepescagem()) * 100) / getMetaCRMExtra().getMeta());
        }

        getMetaFidelizacao().setPerc(Formatador.formatarValorMonetarioSemMoeda(getMetaFidelizacao().getPorcentagem()) + "%");
        getMetaRenovacao().setPerc(Formatador.formatarValorMonetarioSemMoeda(getMetaRenovacao().getPorcentagem()) + "%");
        getMetaStudio().setPerc(Formatador.formatarValorMonetarioSemMoeda(getMetaStudio().getPorcentagem()) + "%");
        getMetaCRMExtra().setPerc(Formatador.formatarValorMonetarioSemMoeda(getMetaCRMExtra().getPorcentagem()) + "%");
    }

    private boolean verificarMetaNaLista(FecharMetaVO fecharMetaVO){
        boolean valor = true;
       for (FecharMetaVO fecharMetaVO1 : getFasesCRMExtra()) {
            if (fecharMetaVO1.getNomeMetaCRMExtra_Apresentar().equals(fecharMetaVO.getNomeMetaCRMExtra_Apresentar())) {
                valor = true;
                break;
            }
        }
        return valor;
    }
    
    private void calcularTotalConversao(FecharMetaVO aux, ArrayList<FecharMetaVO> fecharMetaVOs) {
        double meta = 0.0;
        for (FecharMetaVO fm : fecharMetaVOs) {
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
        }
        aux.setMeta(meta);
        aux.calcularPorcentagem();
    }

    public List<FecharMetaVO> getFasesRelacionamento() {
        if (fasesRelacionamento == null)
            fasesRelacionamento = new ArrayList<FecharMetaVO>();
        return fasesRelacionamento;
    }

    public void setFasesRelacionamento(List<FecharMetaVO> fasesRelacionamento) {
        this.fasesRelacionamento = fasesRelacionamento;
    }

    public List<FecharMetaVO> getFasesRenovacao() {
        if (fasesRenovacao == null)
            fasesRenovacao = new ArrayList<FecharMetaVO>();
        return fasesRenovacao;
    }


    public void setFasesRenovacao(List<FecharMetaVO> fasesRenovacao) {
        this.fasesRenovacao = fasesRenovacao;
    }

    public FecharMetaVO getMetaRenovacao() {
        if (metaRenovacao == null)
            metaRenovacao = new FecharMetaVO();
        return metaRenovacao;
    }

    public void setMetaRenovacao(FecharMetaVO metaRenovacao) {
        this.metaRenovacao = metaRenovacao;
    }

    public FecharMetaVO getMetaFidelizacao() {
        if (metaFidelizacao == null)
            metaFidelizacao = new FecharMetaVO();
        return metaFidelizacao;
    }

    public void setMetaFidelizacao(FecharMetaVO metaFidelizacao) {
        this.metaFidelizacao = metaFidelizacao;
    }

    public List<FecharMetaVO> getFases() {
        return fases;
    }

    public void setFases(List<FecharMetaVO> fases) {
        this.fases = fases;
    }

    public List<FecharMetaVO> getTotais() {
        return totais;
    }

    public void setTotais(List<FecharMetaVO> totais) {
        this.totais = totais;
    }

    public List<FecharMetaVO> getTotaisResultado() {
        if (totaisResultado == null) {
            totaisResultado = new ArrayList<FecharMetaVO>();
        }
        return totaisResultado;
    }

    public void setTotaisResultado(List<FecharMetaVO> totaisResultado) {
        this.totaisResultado = totaisResultado;
    }

    public FecharMetaVO getMetaStudio() {
        if (metaStudio == null) {
            metaStudio = new FecharMetaVO();
        }
        return metaStudio;
    }

    public void setMetaStudio(FecharMetaVO metaStudio) {
        this.metaStudio = metaStudio;
    }

    public FecharMetaVO getMetaCRMExtra() {
        if (metaCRMExtra == null) {
            metaCRMExtra = new FecharMetaVO();
        }
        return metaCRMExtra;
    }

    public void setMetaCRMExtra(FecharMetaVO metaCRMExtra) {
        this.metaCRMExtra = metaCRMExtra;
    }

    public List<FecharMetaVO> getFasesStudio() {
        if (fasesStudio == null) {
            fasesStudio = new ArrayList<FecharMetaVO>();
        }
        return fasesStudio;
    }

    public void setFasesStudio(List<FecharMetaVO> fasesStudio) {
        this.fasesStudio = fasesStudio;
    }

    public List<FecharMetaVO> getFasesCRMExtra() {
        if (fasesCRMExtra == null) {
            fasesCRMExtra = new ArrayList<FecharMetaVO>();
        }
        return fasesCRMExtra;
    }

    public void setFasesCRMExtra(List<FecharMetaVO> fasesCRMExtra) {
        this.fasesCRMExtra = fasesCRMExtra;
    }

    public String getJsonBIDesempenhoConsultores() {
        if (jsonBIDesempenhoConsultores == null) {
            jsonBIDesempenhoConsultores = "[]";
        }
        return jsonBIDesempenhoConsultores;
    }

    public void setJsonBIDesempenhoConsultores(String jsonBIDesempenhoConsultores) {
        this.jsonBIDesempenhoConsultores = jsonBIDesempenhoConsultores;
    }

    public void montarJSONResultado() {
        try {
            JSONArray biResultado = new JSONArray();

            for (FecharMetaVO fecharMetaVO : getTotaisResultado()) {
                JSONObject objMeta = new JSONObject();
                objMeta.put("fase", fecharMetaVO.getFase().getDescricaoCurtaBI());
                objMeta.put("meta", fecharMetaVO.getMeta());
                objMeta.put("metaatingida", fecharMetaVO.getMetaAtingida());
                objMeta.put("porcentagem", Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem()));

                biResultado.put(objMeta);
            }

            setJsonBIResultado(biResultado.toString());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirListaMeta() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<>());
            FecharMetaVO fecharMetaVO = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
            String identificadorMeta = fecharMetaVO.getFase().getSigla();
            setFaseSelecionada(identificadorMeta);
            List<FecharMetaVO> listaFecharMeta = new ArrayList<>();
            listaFecharMeta.add(fecharMetaVO);
            listaFecharMeta.addAll(fecharMetaVO.getListaFecharMetaRelacionado());
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, false, false, false, false, listaFecharMeta,fecharMetaVO.getMalaDiretaCRMExtra().getMetaExtraIndividual(),getCodigosMetaExtraIndividual(),true,getQuantidadeConsultorSelecionado(),fecharMetaVO.getCodigoMetaExtraSomada(), isMetaExtra());
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            Ordenacao.ordenarLista(lista, "ordenacao");
            setListaClientesMeta(lista);
            metaExtra = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaAtingida() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            FecharMetaVO fecharMetaVO = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
            String identificadorMeta = fecharMetaVO.getFase().getSigla();
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            listaFecharMeta.add(fecharMetaVO);
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, true, false, false, false, listaFecharMeta,fecharMetaVO.getMalaDiretaCRMExtra().getMetaExtraIndividual(),getCodigosMetaExtraIndividual(),false,getQuantidadeConsultorSelecionado(),fecharMetaVO.getCodigoMetaExtraSomada(), isMetaExtra());
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
            metaExtra = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaRepescagem() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            FecharMetaVO fecharMetaVO = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
            String identificadorMeta = fecharMetaVO.getFase().getSigla();
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            listaFecharMeta.add(fecharMetaVO);
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, false, true, false, false, listaFecharMeta);
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaTotalVendas() throws Exception {
        try {
            abrirListaMetaTotal(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaAtingidaTotalVendas() throws Exception {
        try {
            abrirListaMetaAtingidaTotal(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaRepescagemTotalVendas() throws Exception {
        try {
            abrirListaMetaRepescagemTotal(FasesCRMEnum.VINTE_QUATRO_HORAS.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaTotalFidelizacao() throws Exception {
        try {
            abrirListaMetaTotal(FasesCRMEnum.GRUPO_RISCO.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaAtingidaTotalFidelizacao() throws Exception {
        try {
            abrirListaMetaAtingidaTotal(FasesCRMEnum.GRUPO_RISCO.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaRepescagemTotalFidelizacao() throws Exception {
        try {
            abrirListaMetaRepescagemTotal(FasesCRMEnum.GRUPO_RISCO.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaTotalStudio() throws Exception {
        try {
            abrirListaMetaTotal(FasesCRMEnum.ULTIMAS_SESSOES.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaAtingidaTotalStudio() throws Exception {
        try {
            abrirListaMetaAtingidaTotal(FasesCRMEnum.ULTIMAS_SESSOES.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaRepescagemTotalStudio() throws Exception {
        try {
            abrirListaMetaRepescagemTotal(FasesCRMEnum.ULTIMAS_SESSOES.getSigla());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaTotalCRMExtra() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            String identificadorMeta = FasesCRMEnum.CRM_EXTRA.getSigla();
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRMEspecifico(getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, false, false, true, false, getFasesCRMExtra(),getCodigosMetaExtra(),true,getQuantidadeConsultorSelecionado());
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaAtingidaTotalCRMExtra() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            String identificadorMeta = FasesCRMEnum.CRM_EXTRA.getSigla();
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRMEspecifico(getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, true, false, true, false, getFasesCRMExtra(),getCodigosMetaTodos(),false,getQuantidadeConsultorSelecionado());
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaRepescagemTotalCRMExtra() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            String identificadorMeta = FasesCRMEnum.CRM_EXTRA.getSigla();
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, false, true, true, false, getFasesCRMExtra());
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaTotal(String identificadorMeta) throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, false, false, true, false, listaFecharMeta);
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaAtingidaTotal(String identificadorMeta) throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, true, false, true, false, listaFecharMeta);
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaMetaRepescagemTotal(String identificadorMeta) throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, false, true, true, false, listaFecharMeta);
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public List<FecharMetaDetalhadoVO> getListaClientesMeta() {
        if (listaClientesMeta == null) {
            listaClientesMeta = new ArrayList<FecharMetaDetalhadoVO>();
        }
        return listaClientesMeta;
    }

    public void setListaClientesMeta(List<FecharMetaDetalhadoVO> listaClientesMeta) {
        this.listaClientesMeta = listaClientesMeta;
    }

    public String getTotalListaClientesMeta() {
        return "Total " + getListaClientesMeta().size();
    }

    public Integer getSizeListaUsuariosSelecionados() {
        return getListaUsuariosSelecionados().size();
    }

    public void montarJSONMetaVendas() {
        try {

            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (FecharMetaVO fecharMetaVO : getFasesRenovacao()) {
                dados = true;

                json.append("{").append("\"fase\":").append("\"").append(fecharMetaVO.getFase().getDescricaoCurtaBI()).append("\",");
                json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
                json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
                json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
                json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");

            }

            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");

            setJsonBIMetaVendas(json.toString());

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarJSONMetaStudio() {
        try {

            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (FecharMetaVO fecharMetaVO : getFasesStudio()) {
                dados = true;

                json.append("{").append("\"fase\":").append("\"").append(fecharMetaVO.getFase().getDescricaoCurtaBI()).append("\",");
                json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
                json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
                json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
                json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");

            }

            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");

            setJsonBIMetaStudio(json.toString());

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarJSONMetaCRMExtra() {
        try {

            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (FecharMetaVO fecharMetaVO : getFasesCRMExtra()) {
                dados = true;

                json.append("{").append("\"fase\":").append("\"").append(fecharMetaVO.getNomeMetaCRMExtra_Apresentar()).append("\",");
                json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
                json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
                json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
                json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");

            }

            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");

            setJsonBIMetaCRMExtra(json.toString());

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getJsonBIMetaVendas() {
        if (jsonBIMetaVendas == null) {
            jsonBIMetaVendas = "[]";
        }
        return jsonBIMetaVendas;
    }

    public void setJsonBIMetaVendas(String jsonBIMetaVendas) {
        this.jsonBIMetaVendas = jsonBIMetaVendas;
    }

    public String getJsonBIMetaFidelizacao() {
        if (jsonBIMetaFidelizacao == null) {
            jsonBIMetaFidelizacao = "[]";
        }
        return jsonBIMetaFidelizacao;
    }

    public void setJsonBIMetaFidelizacao(String jsonBIMetaFidelizacao) {
        this.jsonBIMetaFidelizacao = jsonBIMetaFidelizacao;
    }

    public void montarJSONMetaFidelizacao() {
        try {

            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (FecharMetaVO fecharMetaVO : getFasesRelacionamento()) {
                dados = true;

                json.append("{").append("\"fase\":").append("\"").append(fecharMetaVO.getFase().getDescricaoCurtaBI()).append("\",");
                json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
                json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
                json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
                json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");

            }

            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");

            setJsonBIMetaFidelizacao(json.toString());

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getJsonBIMetaStudio() {
        if (jsonBIMetaStudio == null) {
            jsonBIMetaStudio = "[]";
        }
        return jsonBIMetaStudio;
    }

    public void setJsonBIMetaStudio(String jsonBIMetaStudio) {
        this.jsonBIMetaStudio = jsonBIMetaStudio;
    }

    public String getJsonBIMetaCRMExtra() {
        if (jsonBIMetaCRMExtra == null) {
            jsonBIMetaCRMExtra = "[]";
        }
        return jsonBIMetaCRMExtra;
    }

    public void setJsonBIMetaCRMExtra(String jsonBIMetaCRMExtra) {
        this.jsonBIMetaCRMExtra = jsonBIMetaCRMExtra;
    }

    public boolean isGraficoResultado() {
        return graficoResultado;
    }

    public void setGraficoResultado(boolean graficoResultado) {
        this.graficoResultado = graficoResultado;
    }

    public boolean isGraficoVendas() {
        return graficoVendas;
    }

    public void setGraficoVendas(boolean graficoVendas) {
        this.graficoVendas = graficoVendas;
    }

    public boolean isGraficoFidelizacao() {
        return graficoFidelizacao;
    }

    public void setGraficoFidelizacao(boolean graficoFidelizacao) {
        this.graficoFidelizacao = graficoFidelizacao;
    }

    public boolean isGraficoStudio() {
        return graficoStudio;
    }

    public void setGraficoStudio(boolean graficoStudio) {
        this.graficoStudio = graficoStudio;
    }

    public boolean isGraficoCRMExtra() {
        return graficoCRMExtra;
    }

    public void setGraficoCRMExtra(boolean graficoCRMExtra) {
        this.graficoCRMExtra = graficoCRMExtra;
    }

    public void apresentarGraficoResultado() {
        if (isGraficoResultado()) {
            setGraficoResultado(false);
        } else {
            setGraficoResultado(true);
        }
    }

    public void apresentarGraficoVendas() {
        if (isGraficoVendas()) {
            setGraficoVendas(false);
        } else {
            setGraficoVendas(true);
        }
    }

    public void apresentarGraficoFidelizacao() {
        if (isGraficoFidelizacao()) {
            setGraficoFidelizacao(false);
        } else {
            setGraficoFidelizacao(true);
        }
    }

    public void apresentarGraficoStudio() {
        if (isGraficoStudio()) {
            setGraficoStudio(false);
        } else {
            setGraficoStudio(true);
        }
    }

    public void apresentarGraficoCRMExtra() {
        if (isGraficoCRMExtra()) {
            setGraficoCRMExtra(false);
        } else {
            setGraficoCRMExtra(true);
        }
    }

    public void montarJSONDesempenhoConsultores() {
        try {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

            if (getConfiguracaoSistemaCRMVO().isApresentarColaboradoresPorTipoColaborador()) {
                adicionarUsuariosCRMListaSelecionados(false);
            } else {
                adicionarUsuarioListaSelecionados();
            }


            setJsonBIDesempenhoConsultores(getFacade().getFecharMeta().consultarGraficoResultadoBICRM(getDataInicioBIDesempenho(), getDataFinalBIDesempenho(), getListaUsuariosSelecionados()));
            setJsonBIDesempenhoConsultoresLegenda(getFacade().getFecharMeta().consultarGraficoResultadoBICRMLegenda(getDataInicioBIDesempenho(), getDataFinalBIDesempenho(), getListaUsuariosSelecionados()));

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            System.out.println("Montar BI Desempenho: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getJsonBIDesempenhoConsultoresLegenda() {
        if (jsonBIDesempenhoConsultoresLegenda == null) {
            jsonBIDesempenhoConsultoresLegenda = "[]";
        }
        return jsonBIDesempenhoConsultoresLegenda;
    }

    public void setJsonBIDesempenhoConsultoresLegenda(String jsonBIDesempenhoConsultoresLegenda) {
        this.jsonBIDesempenhoConsultoresLegenda = jsonBIDesempenhoConsultoresLegenda;
    }

    public Integer getIndAgendaLigacoesPendentes() {
        if (indAgendaLigacoesPendentes == null) {
            indAgendaLigacoesPendentes = 0;
        }
        return indAgendaLigacoesPendentes;
    }

    public void setIndAgendaLigacoesPendentes(Integer indAgendaLigacoesPendentes) {
        this.indAgendaLigacoesPendentes = indAgendaLigacoesPendentes;
    }

    public Integer getIndIndicacoesSemContato() {
        if (indIndicacoesSemContato == null) {
            indIndicacoesSemContato = 0;
        }
        return indIndicacoesSemContato;
    }

    public void setIndIndicacoesSemContato(Integer indIndicacoesSemContato) {
        this.indIndicacoesSemContato = indIndicacoesSemContato;
    }

    public Integer getIndContatoReceptivo() {
        if (indContatoReceptivo == null) {
            indContatoReceptivo = 0;
        }
        return indContatoReceptivo;
    }

    public void setIndContatoReceptivo(Integer indContatoReceptivo) {
        this.indContatoReceptivo = indContatoReceptivo;
    }

    public List<FecharMetaVO> getListaMetas() {
        if (listaMetas == null) {
            listaMetas = new ArrayList<>();
        }
        return listaMetas;
    }

    public void setListaMetas(List<FecharMetaVO> listaMetas) {
        this.listaMetas = listaMetas;
    }

    public void montarIndicadores() throws Exception {
        try {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

            Integer empresa = null;
            try {
                empresa = getEmpresaLogado().getCodigo();
            } catch (Exception ignored) {
            }

            List<FecharMetaVO> listaAgendados = new ArrayList<>();
            List<FecharMetaVO> listaIndicados = new ArrayList<>();
            for (FecharMetaVO fecharMetaVO : getListaMetas()) {
                if (fecharMetaVO.getFase().equals(FasesCRMEnum.AGENDAMENTO)) {
                    listaAgendados.add(fecharMetaVO);
                } else if (fecharMetaVO.getFase().equals(FasesCRMEnum.INDICACOES)) {
                    listaIndicados.add(fecharMetaVO);
                }
            }
            setListaFecharMetaAgendadosPeriodo(listaAgendados);
            setListaFecharMetaIndicacoesPeriodo(listaIndicados);

            //AGENDAMENTO DE LIGAÇÃO PENDENTE
            if (!UteisValidacao.emptyList(getListaFecharMetaAgendadosPeriodo())) {
                setIndAgendaLigacoesPendentes(getFacade().getFecharMetaDetalhado().contarIndicadores(getListaFecharMetaAgendadosPeriodo(), true, false, true));
            }

            //INDICAÇÕES SEM CONTATO
            if (!UteisValidacao.emptyList(getListaFecharMetaIndicacoesPeriodo())) {
                setIndIndicacoesSemContato(getFacade().getFecharMetaDetalhado().contarIndicadores(getListaFecharMetaIndicacoesPeriodo(), false, true, false));
            }

            //CONTATO RECEPTIVO
            setIndContatoReceptivo(getFacade().getPassivo().contarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo()));

            //CONSULTAR ALUNOS COM OBJECOES DEFINITIVAS
            setIndClientesObjecaoDefinitiva(getFacade().getCliente().contarClientesComObjecaoDefinitiva(empresa));

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            System.out.println("Montar Indicadores BI: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean getExisteMetaCRMExtra() {
        return !UteisValidacao.emptyList(getFasesCRMExtra());
    }

    public List<PassivoVO> getListaContatoReceptivo() {
        if (listaContatoReceptivo == null) {
            listaContatoReceptivo = new ArrayList<PassivoVO>();
        }
        return listaContatoReceptivo;
    }

    public void setListaContatoReceptivo(List<PassivoVO> listaContatoReceptivo) {
        this.listaContatoReceptivo = listaContatoReceptivo;
    }

    public void abrirListaAgendamentoLigacaoPendente() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarIndicadores(getListaFecharMetaAgendadosPeriodo(), true, false, true, 1000);
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaIndicacoesSemContato() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            List<FecharMetaDetalhadoVO> lista = getFacade().getFecharMetaDetalhado().consultarIndicadores(getListaFecharMetaIndicacoesPeriodo(), false, true, false, 1000);
            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaContatoReceptivo() throws Exception {
        try {
            setListaContatoReceptivo(getFacade().getPassivo().consultarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            List<FecharMetaDetalhadoVO> passivos = new ArrayList<FecharMetaDetalhadoVO>();
            for (PassivoVO passivoVO : getListaContatoReceptivo()) {
                FecharMetaDetalhadoVO novo = new FecharMetaDetalhadoVO();
                novo.setPassivo(passivoVO);
                novo.getFecharMeta().setDataRegistro(passivoVO.getDia());
                preencherEmailTelefone(novo);
                passivos.add(novo);
            }
            setListaClientesMeta(passivos);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaBIResultado() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            FecharMetaVO fecharMetaVO = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
            String identificadorMeta = fecharMetaVO.getFase().getSigla();
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            List<FecharMetaDetalhadoVO> lista = new ArrayList<FecharMetaDetalhadoVO>();

            if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_PASSIVO.getSigla())) {
                List<PassivoVO> listaPassivos = getFacade().getPassivo().consultarPassivoPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                List<FecharMetaDetalhadoVO> passivos = new ArrayList<FecharMetaDetalhadoVO>();
                for (PassivoVO passivoVO : listaPassivos) {
                    FecharMetaDetalhadoVO novo = new FecharMetaDetalhadoVO();
                    novo.getFecharMeta().setIdentificadorMeta(FasesCRMEnum.CONVERSAO_PASSIVO.getSigla());
                    novo.setPassivo(passivoVO);
                    novo.getFecharMeta().setDataRegistro(passivoVO.getDia());
                    preencherEmailTelefone(novo);
                    passivos.add(novo);
                }
                setListaClientesMeta(passivos);
                return;
            } else if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_LEAD.getSigla())) {
                List<LeadVO> listaLeads = getFacade().getLead().consultarLeadsConvertidosPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(),
                        getDataFinal(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false);
                List<FecharMetaDetalhadoVO> leads = new ArrayList<>();
                for (LeadVO leadVO : listaLeads) {
                    FecharMetaDetalhadoVO novo = new FecharMetaDetalhadoVO();
                    novo.getFecharMeta().setIdentificadorMeta(FasesCRMEnum.CONVERSAO_LEAD.getSigla());
                    if((leadVO.getPassivo() != null && !UteisValidacao.emptyNumber(leadVO.getPassivo().getCodigo()))){
                        novo.setPassivo(leadVO.getPassivo());
                    }else{
                        novo.setIndicado(leadVO.getIndicado());
                    }
                    novo.getFecharMeta().setDataRegistro(
                            (leadVO.getPassivo() != null && !UteisValidacao.emptyNumber(leadVO.getPassivo().getCodigo())) ? leadVO.getPassivo().getDia() : leadVO.getIndicado().getDataLancamento());
                    preencherEmailTelefone(novo);
                    leads.add(novo);
                }
                setListaClientesMeta(leads);
                return;
            }  else {
                lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, true, false, false, false, listaFecharMeta);
            }

            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrirListaBIResultadoMetaAtingida() throws Exception {
        try {
            setListaClientesMeta(new ArrayList<FecharMetaDetalhadoVO>());
            FecharMetaVO fecharMetaVO = (FecharMetaVO) context().getExternalContext().getRequestMap().get("fecharMeta");
            String identificadorMeta = fecharMetaVO.getFase().getSigla();
            List<FecharMetaVO> listaFecharMeta = new ArrayList<FecharMetaVO>();
            List<FecharMetaDetalhadoVO> lista = new ArrayList<FecharMetaDetalhadoVO>();

            if (identificadorMeta.equals(FasesCRMEnum.CONVERSAO_PASSIVO.getSigla())) {
                List<PassivoVO> listaPassivos = getFacade().getPassivo().consultarPassivoConvertidosPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
                List<FecharMetaDetalhadoVO> passivos = new ArrayList<FecharMetaDetalhadoVO>();
                for (PassivoVO passivoVO : listaPassivos) {
                    FecharMetaDetalhadoVO novo = new FecharMetaDetalhadoVO();
                    novo.getFecharMeta().setIdentificadorMeta(FasesCRMEnum.CONVERSAO_PASSIVO.getSigla());
                    novo.setCliente(passivoVO.getClienteVO());
                    novo.getFecharMeta().setDataRegistro(passivoVO.getDia());
                    preencherEmailTelefone(novo);
                    passivos.add(novo);
                }
                setListaClientesMeta(passivos);
                return;
            }else if(identificadorMeta.equals(FasesCRMEnum.CONVERSAO_LEAD.getSigla())) {
                List<LeadVO> listaLeads = getFacade().getLead().consultarLeadsConvertidosPorPeriodoResponsavelCadastro(getListaUsuariosSelecionados(), getDataInicio(), getDataFinal(),
                        getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, true);
                List<FecharMetaDetalhadoVO> leads = new ArrayList<>();
                for (LeadVO leadVO : listaLeads) {
                    FecharMetaDetalhadoVO novo = new FecharMetaDetalhadoVO();
                    novo.getFecharMeta().setIdentificadorMeta(FasesCRMEnum.CONVERSAO_LEAD.getSigla());
                    novo.setCliente(leadVO.getCliente());
                    novo.getFecharMeta().setDataRegistro(leadVO.getPassivo() != null ? leadVO.getPassivo().getDia() : leadVO.getIndicado().getDataLancamento());
                    preencherEmailTelefone(novo);
                    leads.add(novo);
                }
                setListaClientesMeta(leads);
                return;
            }else {
                lista = getFacade().getFecharMetaDetalhado().consultarListaBICRM(getEmpresaLogado(), getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), identificadorMeta, true, false, false, true, listaFecharMeta);
            }

            for (FecharMetaDetalhadoVO metaDetalhadoVO : lista) {
                preencherEmailTelefone(metaDetalhadoVO);
            }
            setListaClientesMeta(lista);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public Integer getIndObjecoes() {
        if (indObjecoes == null) {
            indObjecoes = 0;
        }
        return indObjecoes;
    }

    public void setIndObjecoes(Integer indObjecoes) {
        this.indObjecoes = indObjecoes;
    }

    public List<ObjecaoBICRMTO> getListaIndObjecoes() {
        if (listaIndObjecoes == null) {
            listaIndObjecoes = new ArrayList<ObjecaoBICRMTO>();
        }
        return listaIndObjecoes;
    }

    public void setListaIndObjecoes(List<ObjecaoBICRMTO> listaIndObjecoes) {
        this.listaIndObjecoes = listaIndObjecoes;
    }

    public List<FecharMetaVO> getListaFecharMetaAgendadosPeriodo() {
        if (listaFecharMetaAgendadosPeriodo == null) {
            listaFecharMetaAgendadosPeriodo = new ArrayList<FecharMetaVO>();
        }
        return listaFecharMetaAgendadosPeriodo;
    }

    public void setListaFecharMetaAgendadosPeriodo(List<FecharMetaVO> listaFecharMetaAgendadosPeriodo) {
        this.listaFecharMetaAgendadosPeriodo = listaFecharMetaAgendadosPeriodo;
    }

    public List<FecharMetaVO> getListaFecharMetaIndicacoesPeriodo() {
        if (listaFecharMetaIndicacoesPeriodo == null) {
            listaFecharMetaIndicacoesPeriodo = new ArrayList<FecharMetaVO>();
        }
        return listaFecharMetaIndicacoesPeriodo;
    }

    public void setListaFecharMetaIndicacoesPeriodo(List<FecharMetaVO> listaFecharMetaIndicacoesPeriodo) {
        this.listaFecharMetaIndicacoesPeriodo = listaFecharMetaIndicacoesPeriodo;
    }

    public String getMetaBuscarDetalhadaVendas() {
        if (metaBuscarDetalhadaVendas == null) {
            metaBuscarDetalhadaVendas = "[]";
        }
        return metaBuscarDetalhadaVendas;
    }

    public void setMetaBuscarDetalhadaVendas(String metaBuscarDetalhadaVendas) {
        this.metaBuscarDetalhadaVendas = metaBuscarDetalhadaVendas;
    }

    public String getJsonDetalhadoVendas() {
        if (jsonDetalhadoVendas == null) {
            jsonDetalhadoVendas = "[]";
        }
        return jsonDetalhadoVendas;
    }

    public void setJsonDetalhadoVendas(String jsonDetalhadoVendas) {
        this.jsonDetalhadoVendas = jsonDetalhadoVendas;
    }

    public String getMetaBuscarDetalhadaFidelizacao() {
        if (metaBuscarDetalhadaFidelizacao == null) {
            metaBuscarDetalhadaFidelizacao = "[]";
        }
        return metaBuscarDetalhadaFidelizacao;
    }

    public void setMetaBuscarDetalhadaFidelizacao(String metaBuscarDetalhadaFidelizacao) {
        this.metaBuscarDetalhadaFidelizacao = metaBuscarDetalhadaFidelizacao;
    }

    public String getJsonDetalhadoFidelizacao() {
        if (jsonDetalhadoFidelizacao == null) {
            jsonDetalhadoFidelizacao = "[]";
        }
        return jsonDetalhadoFidelizacao;
    }

    public void setJsonDetalhadoFidelizacao(String jsonDetalhadoFidelizacao) {
        this.jsonDetalhadoFidelizacao = jsonDetalhadoFidelizacao;
    }

    public String getMetaBuscarDetalhadaStudio() {
        if (metaBuscarDetalhadaStudio == null) {
            metaBuscarDetalhadaStudio = "[]";
        }
        return metaBuscarDetalhadaStudio;
    }

    public void setMetaBuscarDetalhadaStudio(String metaBuscarDetalhadaStudio) {
        this.metaBuscarDetalhadaStudio = metaBuscarDetalhadaStudio;
    }

    public String getJsonDetalhadoStudio() {
        if (jsonDetalhadoStudio == null) {
            jsonDetalhadoStudio = "[]";
        }
        return jsonDetalhadoStudio;
    }

    public void setJsonDetalhadoStudio(String jsonDetalhadoStudio) {
        this.jsonDetalhadoStudio = jsonDetalhadoStudio;
    }

    public String getMetaBuscarDetalhadaCRMExtra() {
        if (metaBuscarDetalhadaCRMExtra == null) {
            metaBuscarDetalhadaCRMExtra = "[]";
        }
        return metaBuscarDetalhadaCRMExtra;
    }

    public void setMetaBuscarDetalhadaCRMExtra(String metaBuscarDetalhadaCRMExtra) {
        this.metaBuscarDetalhadaCRMExtra = metaBuscarDetalhadaCRMExtra;
    }

    public String getJsonDetalhadoCRMExtra() {
        if (jsonDetalhadoCRMExtra == null) {
            jsonDetalhadoCRMExtra = "[]";
        }
        return jsonDetalhadoCRMExtra;
    }

    public void setJsonDetalhadoCRMExtra(String jsonDetalhadoCRMExtra) {
        this.jsonDetalhadoCRMExtra = jsonDetalhadoCRMExtra;
    }

    public String getMetaBuscarDetalhadaResultado() {
        if (metaBuscarDetalhadaResultado == null) {
            metaBuscarDetalhadaResultado = "[]";
        }
        return metaBuscarDetalhadaResultado;
    }

    public void setMetaBuscarDetalhadaResultado(String metaBuscarDetalhadaResultado) {
        this.metaBuscarDetalhadaResultado = metaBuscarDetalhadaResultado;
    }

    public String getJsonDetalhadoResultado() {
        if (jsonDetalhadoResultado == null) {
            jsonDetalhadoResultado = "[]";
        }
        return jsonDetalhadoResultado;
    }

    public void setJsonDetalhadoResultado(String jsonDetalhadoResultado) {
        this.jsonDetalhadoResultado = jsonDetalhadoResultado;
    }

    public String montarGraficoDetalhado(FasesCRMEnum fasesCRMEnum) throws Exception {
        String retorno = "";
        List<FecharMetaVO> fecharMetaVOList = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), fasesCRMEnum.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());

        StringBuilder json = new StringBuilder();
        json.append("[");
        boolean dados = false;
        for (FecharMetaVO fecharMetaVO : fecharMetaVOList) {
            dados = true;
            json.append("{").append("\"usuario\":").append("\"").append(fecharMetaVO.getUsuarioVO().getNomeAbreviado()).append("\",");
            json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
            json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
            json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
            json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]");

        return json.toString();
    }

    public void montarGraficoDetalhadoVendas() {
        try {
            String descricaoCurta = getMetaBuscarDetalhadaVendas();
            FasesCRMEnum fase = FasesCRMEnum.getFasePorDescricaoBI(descricaoCurta);
            setJsonDetalhadoVendas(montarGraficoDetalhado(fase));
            setMetaBuscarDetalhadaVendas(fase.getDescricao());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarGraficoDetalhadoFidelizacao() {
        try {
            String descricaoCurta = getMetaBuscarDetalhadaFidelizacao();
            FasesCRMEnum fase = FasesCRMEnum.getFasePorDescricaoBI(descricaoCurta);
            setJsonDetalhadoFidelizacao(montarGraficoDetalhado(fase));
            setMetaBuscarDetalhadaFidelizacao(fase.getDescricao());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarGraficoDetalhadoResultado() {
        try {
            String descricaoCurta = getMetaBuscarDetalhadaResultado();
            FasesCRMEnum faseApresentar = FasesCRMEnum.getFasePorDescricaoBI(descricaoCurta);
            FasesCRMEnum faseBuscar = faseApresentar;
            List<FecharMetaVO> fecharMetaVOList = new ArrayList<FecharMetaVO>();

            if (faseBuscar.equals(FasesCRMEnum.CONVERSAO_INDICADOS)) {
                faseBuscar = FasesCRMEnum.INDICACOES;
                ConfiguracaoSistemaCRMVO confCRM = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                Integer qtdConfiguracao = confCRM.getIndicacoesMes();
                long dias = Uteis.nrDiasEntreDatas(getDataInicio(), Uteis.somarDias(getDataFinal(), 1));
                double valor = (qtdConfiguracao * dias) / Uteis.obterNumeroDiasDoMes(getDataInicio());
                List<FecharMetaVO> indicacoes = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), faseBuscar.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                for (FecharMetaVO fecharMetaVO : indicacoes) {
                    fecharMetaVO.setMeta(Math.ceil(1 * valor));
                    fecharMetaVO.calcularPorcentagem();
                }
                fecharMetaVOList = indicacoes;
            } else if (faseBuscar.equals(FasesCRMEnum.CONVERSAO_AGENDADOS)) {
                FasesCRMEnum agendamento = FasesCRMEnum.AGENDAMENTO;
                List<FecharMetaVO> totais = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), agendamento.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                List<FecharMetaVO> conversao = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), faseBuscar.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                for (FecharMetaVO total : totais) {
                    for (FecharMetaVO conv : conversao) {
                        if (conv.getUsuarioVO().getCodigo().equals(total.getUsuarioVO().getCodigo())) {
                            conv.setMeta(total.getMetaAtingida() + total.getRepescagem());
                            conv.calcularPorcentagem();
                        }
                    }
                }
                fecharMetaVOList = conversao;
            } else if (faseBuscar.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS)) {
                FasesCRMEnum exaluno = FasesCRMEnum.EX_ALUNOS;
                List<FecharMetaVO> totais = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), exaluno.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                List<FecharMetaVO> conversao = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), faseBuscar.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                for (FecharMetaVO total : totais) {
                    for (FecharMetaVO conv : conversao) {
                        if (conv.getUsuarioVO().getCodigo().equals(total.getUsuarioVO().getCodigo())) {
                            conv.setMeta(total.getMetaAtingida() + total.getRepescagem());
                            conv.calcularPorcentagem();
                        }
                    }
                }
                fecharMetaVOList = conversao;
            } else if (faseBuscar.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS)) {
                FasesCRMEnum visi = FasesCRMEnum.VISITANTES_ANTIGOS;
                List<FecharMetaVO> totais = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), visi.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                List<FecharMetaVO> conversao = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), faseBuscar.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                for (FecharMetaVO total : totais) {
                    for (FecharMetaVO conv : conversao) {
                        if (conv.getUsuarioVO().getCodigo().equals(total.getUsuarioVO().getCodigo())) {
                            conv.setMeta(total.getMetaAtingida() + total.getRepescagem());
                            conv.calcularPorcentagem();
                        }
                    }
                }
                fecharMetaVOList = conversao;
            } else if (faseBuscar.equals(FasesCRMEnum.CONVERSAO_DESISTENTES)) {
                FasesCRMEnum desistentes = FasesCRMEnum.DESISTENTES;
                List<FecharMetaVO> totais = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), desistentes.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                List<FecharMetaVO> conversao = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), faseBuscar.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());
                for (FecharMetaVO total : totais) {
                    for (FecharMetaVO conv : conversao) {
                        if (conv.getUsuarioVO().getCodigo().equals(total.getUsuarioVO().getCodigo())) {
                            conv.setMeta(total.getMetaAtingida() + total.getRepescagem());
                            conv.calcularPorcentagem();
                        }
                    }
                }
                fecharMetaVOList = conversao;
            } else if (faseBuscar.equals(FasesCRMEnum.CONVERSAO_PASSIVO)) {
                List<FecharMetaVO> conversaoPassivo = new ArrayList<FecharMetaVO>();
                for (UsuarioVO usuarioVO : getListaUsuariosSelecionados()) {
                    List<UsuarioVO> listaUsuarios = new ArrayList<UsuarioVO>();
                    listaUsuarios.add(usuarioVO);

                    Integer totalPassivoUsuario = getFacade().getPassivo().contarPassivoPorPeriodoResponsavelCadastro(listaUsuarios, getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo());
                    Integer totalPassivoUsuarioConvertivo = getFacade().getPassivo().contarPassivoConvertidosPorPeriodoResponsavelCadastro(listaUsuarios, getDataInicio(), getDataFinal(), getEmpresaLogado().getCodigo());

                    FecharMetaVO fecharMetaVO = new FecharMetaVO();
                    fecharMetaVO.setUsuarioVO(usuarioVO);
                    fecharMetaVO.setMeta(Double.parseDouble(totalPassivoUsuario.toString()));
                    fecharMetaVO.setMetaAtingida(Double.parseDouble(totalPassivoUsuarioConvertivo.toString()));
                    fecharMetaVO.setRepescagem(0.0);
                    fecharMetaVO.calcularPorcentagem();
                    conversaoPassivo.add(fecharMetaVO);
                }
                fecharMetaVOList = conversaoPassivo;
            }

            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (FecharMetaVO fecharMetaVO : fecharMetaVOList) {
                dados = true;
                json.append("{").append("\"usuario\":").append("\"").append(fecharMetaVO.getUsuarioVO().getNomeAbreviado()).append("\",");
                json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
                json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
                json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
                json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");
            }
            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");

            setJsonDetalhadoResultado(json.toString());
            setMetaBuscarDetalhadaResultado(faseApresentar.getDescricao());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarGraficoDetalhadoStudio() {
        try {
            String descricaoCurta = getMetaBuscarDetalhadaStudio();
            FasesCRMEnum fase = FasesCRMEnum.getFasePorDescricaoBI(descricaoCurta);
            setJsonDetalhadoStudio(montarGraficoDetalhado(fase));
            setMetaBuscarDetalhadaStudio(fase.getDescricao());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarGraficoDetalhadoCRMExtra() {
        try {
            String descricaoCurta = getMetaBuscarDetalhadaCRMExtra();
            FasesCRMEnum fase = FasesCRMEnum.CRM_EXTRA;

            String retorno = "";
            List<FecharMetaVO> fecharMetaVOList = getFacade().getFecharMeta().consultarBIDetalhado(getDataInicio(), getDataFinal(), fase.getSigla(), getListaUsuariosSelecionados(), getEmpresaLogado().getCodigo());

            StringBuilder json = new StringBuilder();
            json.append("[");
            boolean dados = false;
            for (FecharMetaVO fecharMetaVO : fecharMetaVOList) {
                dados = true;
                json.append("{").append("\"usuario\":").append("\"").append(fecharMetaVO.getUsuarioVO().getNomeAbreviado()).append("\",");
                json.append("\"meta\":").append("").append(fecharMetaVO.getMeta()).append(",");
                json.append("\"metaatingida\":").append("").append(fecharMetaVO.getMetaAtingida()).append(",");
                json.append("\"repescagem\":").append("").append(fecharMetaVO.getRepescagem()).append(",");
                json.append("\"porcentagem\":").append("").append(Uteis.arredondarForcando2CasasDecimais(fecharMetaVO.getPorcentagem())).append("},");
            }
            if (dados) {
                json.deleteCharAt(json.toString().length() - 1);
            }
            json.append("]");

            setJsonDetalhadoCRMExtra(json.toString());
            setMetaBuscarDetalhadaCRMExtra(fase.getDescricao());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getModalMensagemGenerica() {
        if (modalMensagemGenerica == null) {
            modalMensagemGenerica = "";
        }
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public void montarMsgGenerica(String titulo, String msg, boolean msgInformacao, String botaoSim, String botaoNao, String reRender) {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica')");

        if (msgInformacao) {
            control.init(titulo, msg, this, "Fechar", "", reRender + ",mdlMensagemGenerica");
        } else {
            control.init(titulo, msg, this, botaoSim, "", botaoNao, "", reRender + ",mdlMensagemGenerica");
        }
    }

    public String getColaboradoresFiltro() {
        StringBuilder ret = new StringBuilder();
        ret.append(" Colaboradores Responsáveis: ");
        for (UsuarioVO aux : getListaUsuariosSelecionados()) {
            ret.append(aux.getNome()).append(", ");
        }
        if (ret.toString().isEmpty()) {
            return ret.toString();
        } else {
            return ret.toString().substring(0, ret.toString().length() - 2);
        }
    }

    private String getDescricaoFiltros() throws Exception {
        StringBuilder aux = new StringBuilder();
        aux.append(getPeriodoFiltro() + " / ");
        aux.append(getColaboradoresFiltro());
        return aux.toString();
    }

    private String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "crm" + File.separator + "BICRMRel.jrxml");
    }

    public String getPeriodoFiltro() {
        return " Período de " + Uteis.getData(dataInicio) + " até " + Uteis.getData(dataFinal);
    }

    public void imprimirRelatorio() {
        try {
            JRDataSource jr1 = new JRBeanArrayDataSource(totaisResultado.toArray(), false);
            setRelatorio("sim");
            setListaRelatorio(fases);
            Map<String, Object> parametros = new HashMap<String, Object>();
            parametros.put("tipoRelatorio", getTipoRelatorio());
            parametros.put("nomeRelatorio", "BICRMRel");
            parametros.put("nomeEmpresa", getEmpresaLogado().getNome());
            parametros.put("mensagemRel", "");
            parametros.put("imagemLogo", "");
            parametros.put("tituloRelatorio", "Business Intelligence CRM");
            parametros.put("caminhoParserXML", "/Totalizador/registros");
            parametros.put("nomeDesignIReport", getDesignIReportRelatorio());
            parametros.put("usuario", getUsuarioLogado().getNome());
            parametros.put("listaObjetos", getListaRelatorio());
            parametros.put("tipoImplementacao", "OBJETO");
            parametros.put("listaTotais", jr1);
            parametros.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "crm" + File.separator);
            String filtros = getDescricaoFiltros();
            if (filtros.equals("")) {
                filtros = "nenhum";
            }
            parametros.put("filtros", filtros);

            parametros.put("agendLig", getIndAgendaLigacoesPendentes().toString());
            parametros.put("indSemCont", getIndIndicacoesSemContato().toString());
            parametros.put("contRecep", getIndContatoReceptivo().toString());
            parametros.put("objecoes", getIndObjecoes().toString());

            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<ObjecaoBICRMTO> getListaIndObjecoesTotais() {
        if (listaIndObjecoesTotais == null) {
            listaIndObjecoesTotais = new ArrayList<ObjecaoBICRMTO>();
        }
        return listaIndObjecoesTotais;
    }

    public void setListaIndObjecoesTotais(List<ObjecaoBICRMTO> listaIndObjecoesTotais) {
        this.listaIndObjecoesTotais = listaIndObjecoesTotais;
    }

    public Date getDataInicioBIDesempenho() {
        return dataInicioBIDesempenho;
    }

    public void setDataInicioBIDesempenho(Date dataInicioBIDesempenho) {
        this.dataInicioBIDesempenho = dataInicioBIDesempenho;
    }

    public Date getDataFinalBIDesempenho() {
        return dataFinalBIDesempenho;
    }

    public void setDataFinalBIDesempenho(Date dataFinalBIDesempenho) {
        this.dataFinalBIDesempenho = dataFinalBIDesempenho;
    }

    public void preencherEmailTelefone(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) throws Exception {
        String emails = "";
        String telefones = "";
        String urlRD = "";
        if (!fecharMetaDetalhadoVO.getCliente().getCodigo().equals(0)) {
            List<EmailVO> emailVOList = getFacade().getEmail().consultarEmails(fecharMetaDetalhadoVO.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (EmailVO emailVO : emailVOList) {
                emails += "; " + emailVO.getEmail();
            }
            List<TelefoneVO> telefoneVOList = getFacade().getTelefone().consultarTelefones(fecharMetaDetalhadoVO.getCliente().getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (TelefoneVO telefoneVO : telefoneVOList) {
                telefones += "; " + telefoneVO.getNumero();
            }
            LeadVO lead = getFacade().getLead().consultarPorCliente(fecharMetaDetalhadoVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            urlRD = lead.getUrlRD();
        } else if (!fecharMetaDetalhadoVO.getPassivo().getCodigo().equals(0)) {
            PassivoVO passivoVO = getFacade().getPassivo().consultarPorChavePrimaria(fecharMetaDetalhadoVO.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            emails = passivoVO.getEmail();
            telefones = passivoVO.getTelefones();
            LeadVO lead = getFacade().getLead().consultarPorPassivo(fecharMetaDetalhadoVO.getPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            urlRD = lead.getUrlRD();
        } else if (!fecharMetaDetalhadoVO.getIndicado().getCodigo().equals(0)) {
            IndicadoVO indicadoVO = getFacade().getIndicado().consultarPorChavePrimaria(fecharMetaDetalhadoVO.getIndicado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            emails = indicadoVO.getEmail();
            telefones = indicadoVO.getTelefones();
        }
        fecharMetaDetalhadoVO.setEmails(emails);
        fecharMetaDetalhadoVO.setTelefones(telefones);
        fecharMetaDetalhadoVO.setUrlRD(urlRD);
    }


    public String getTotalUsuariosSelecionados() {
        if (totalUsuariosSelecionados == null) {
            totalUsuariosSelecionados = "0";
        }
        return totalUsuariosSelecionados;
    }

    public void setTotalUsuariosSelecionados(String totalUsuariosSelecionados) {
        this.totalUsuariosSelecionados = totalUsuariosSelecionados;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        List lista = getFacade().getFecharMeta().consultarImpressaoGraficoDesempenhoResultadoBICRM(getDataInicioBIDesempenho(), getDataFinalBIDesempenho(), getListaUsuariosSelecionados());
        exportadorListaControle.exportar(evt, lista, "", null);
    }

    public void montarJSONObjecoes() {
        try {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
            setObjecoesOutros(new ArrayList<Integer>());

            setListaBIObjecoesPorQuantidade(getFacade().getObjecao().consultarObjecoesBICRM(getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), false));
            setListaBIObjecoesPorFase(getFacade().getObjecao().consultarObjecoesBICRM(getDataInicio(), getDataFinal(), getListaUsuariosSelecionados(), true));

            Integer totalObjecoes = 0;
            JSONArray listaJsonPorQtd = new JSONArray();
            boolean listaGrande = false;
            if (getListaBIObjecoesPorQuantidade().size() > 9) {
                Ordenacao.ordenarLista(getListaBIObjecoesPorQuantidade(), "quantidade");
                Collections.reverse(getListaBIObjecoesPorQuantidade());
                listaGrande = true;
            }

            int i = 0;
            int qtdOutros = 0;
            for (ObjecaoBICRMTO ob : getListaBIObjecoesPorQuantidade()) {
                if (!listaGrande || i <= 9) {
                    JSONObject json = new JSONObject();
                    json.put("title", Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(ob.getObjecao()));
                    json.put("value", ob.getQuantidade());
                    json.put("codigo", ob.getCodigo());
                    listaJsonPorQtd.put(json);
                } else {
                    getObjecoesOutros().add(ob.getCodigo());
                    qtdOutros += ob.getQuantidade();
                }
                totalObjecoes += ob.getQuantidade();
                i++;
            }
            setIndObjecoes(totalObjecoes);
            if (listaGrande && qtdOutros > 0) {
                JSONObject json = new JSONObject();
                json.put("title", Uteis.obterNomeComApenasPrimeiraLetraMaiuscula("Outras objeções"));
                json.put("value", qtdOutros);
                listaJsonPorQtd.put(json);
            }
            setJsonBIObjecoesPorQuantidade(listaJsonPorQtd.toString());

            JSONArray listaJsonPorFase = new JSONArray();
            for (ObjecaoBICRMTO ob : getListaBIObjecoesPorFase()) {
                JSONObject json = new JSONObject();
                json.put("title", ob.getFase().getDescricao());
                json.put("value", ob.getQuantidade());
                listaJsonPorFase.put(json);
            }
            setJsonBIObjecoesPorFase(listaJsonPorFase.toString());

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            System.out.println("Montar BI Objecoes: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<SelectItem> getTiposBIObjecoes(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(1, "Por Quantidade"));
        lista.add(new SelectItem(2, "Por Fase"));
        return lista;
    }

    public String getJsonBIObjecoesPorQuantidade() {
        if (jsonBIObjecoesPorQuantidade == null) {
            jsonBIObjecoesPorQuantidade = "[]";
        }
        return jsonBIObjecoesPorQuantidade;
    }

    public void setJsonBIObjecoesPorQuantidade(String jsonBIObjecoesPorQuantidade) {
        this.jsonBIObjecoesPorQuantidade = jsonBIObjecoesPorQuantidade;
    }

    public String getJsonBIObjecoesPorFase() {
        if (jsonBIObjecoesPorFase == null) {
            jsonBIObjecoesPorFase = "[]";
        }
        return jsonBIObjecoesPorFase;
    }

    public void setJsonBIObjecoesPorFase(String jsonBIObjecoesPorFase) {
        this.jsonBIObjecoesPorFase = jsonBIObjecoesPorFase;
    }

    public Integer getTipoBIObjecao() {
        if (tipoBIObjecao == null) {
            tipoBIObjecao = 1;
        }
        return tipoBIObjecao;
    }

    public void setTipoBIObjecao(Integer tipoBIObjecao) {
        this.tipoBIObjecao = tipoBIObjecao;
    }

    public List<ObjecaoBICRMTO> getListaBIObjecoesPorFase() {
        if (listaBIObjecoesPorFase == null) {
            listaBIObjecoesPorFase = new ArrayList<ObjecaoBICRMTO>();
        }
        return listaBIObjecoesPorFase;
    }

    public void setListaBIObjecoesPorFase(List<ObjecaoBICRMTO> listaBIObjecoesPorFase) {
        this.listaBIObjecoesPorFase = listaBIObjecoesPorFase;
    }

    public List<ObjecaoBICRMTO> getListaBIObjecoesPorQuantidade() {
        if (listaBIObjecoesPorQuantidade == null) {
            listaBIObjecoesPorQuantidade = new ArrayList<ObjecaoBICRMTO>();
        }
        return listaBIObjecoesPorQuantidade;
    }

    public void setListaBIObjecoesPorQuantidade(List<ObjecaoBICRMTO> listaBIObjecoesPorQuantidade) {
        this.listaBIObjecoesPorQuantidade = listaBIObjecoesPorQuantidade;
    }

    public Integer getIndClientesObjecaoDefinitiva() {
        if (indClientesObjecaoDefinitiva == null) {
            indClientesObjecaoDefinitiva = 0;
}
        return indClientesObjecaoDefinitiva;
    }

    public void setIndClientesObjecaoDefinitiva(Integer indClientesObjecaoDefinitiva) {
        this.indClientesObjecaoDefinitiva = indClientesObjecaoDefinitiva;
    }

    public void abrirListaClientesObjecoesDefinitiva() throws Exception {
        try {
            Integer empresa = null;
            try {
                empresa = getEmpresaLogado().getCodigo();
            } catch (Exception ignored) {
            }

            setListaClientesObjecaoDefinitiva(getFacade().getCliente().listaClientesComObjecaoDefinitiva(empresa));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public List<ItemRelatorioTO> getListaClientesObjecaoDefinitiva() {
        if (listaClientesObjecaoDefinitiva == null) {
            listaClientesObjecaoDefinitiva = new ArrayList<ItemRelatorioTO>();
        }
        return listaClientesObjecaoDefinitiva;
    }

    public void setListaClientesObjecaoDefinitiva(List<ItemRelatorioTO> listaClientesObjecaoDefinitiva) {
        this.listaClientesObjecaoDefinitiva = listaClientesObjecaoDefinitiva;
    }

    public List<GrupoTipoColaboradorVO> getListaGrupoTipoColaborador() {
        if (listaGrupoTipoColaborador == null) {
            listaGrupoTipoColaborador = new ArrayList<GrupoTipoColaboradorVO>();
}
        return listaGrupoTipoColaborador;
    }

    public void setListaGrupoTipoColaborador(List<GrupoTipoColaboradorVO> listaGrupoTipoColaborador) {
        this.listaGrupoTipoColaborador = listaGrupoTipoColaborador;
    }

    public boolean isSomenteAtivos() {
        return somenteAtivos;
    }

    public void setSomenteAtivos(boolean somenteAtivos) {
        this.somenteAtivos = somenteAtivos;
    }

    public List<GrupoColaboradorVO> getListaGrupoColaborador() {
        if (listaGrupoColaborador == null) {
            listaGrupoColaborador = new ArrayList<GrupoColaboradorVO>();
}
        return listaGrupoColaborador;
    }

    public void setListaGrupoColaborador(List<GrupoColaboradorVO> listaGrupoColaborador) {
        this.listaGrupoColaborador = listaGrupoColaborador;
    }

    public void selecionarTodosGrupoParticipante(ActionEvent evt) throws Exception {
        Integer codigoGrupoParticipante = (Integer) evt.getComponent().getAttributes().get("codigoGrupoParticipante");

        List<UsuarioVO> novaListaUsuario = new ArrayList<UsuarioVO>();
        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
            if (!grupoColaboradorVO.getCodigo().equals(codigoGrupoParticipante)) {
                for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                    if (participanteVO.getGrupoColaboradorParticipanteEscolhido()) {
                        novaListaUsuario.add(participanteVO.getUsuarioParticipante());
                    }
                }
            } else {
                for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                    if (grupoColaboradorVO.getTodosParticipantesSelecionados()) {
                        participanteVO.setGrupoColaboradorParticipanteEscolhido(true);
                        novaListaUsuario.add(participanteVO.getUsuarioParticipante());
                    } else {
                        participanteVO.setGrupoColaboradorParticipanteEscolhido(false);
                    }
                }
            }
        }
        setListaUsuariosSelecionados(novaListaUsuario);
    }

    public void adicionarUsuarioListaSelecionados() throws Exception {
        Integer contador = 0;
        List<UsuarioVO> novaListaUsuario = new ArrayList<>();
        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
            for (GrupoColaboradorParticipanteVO participanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (participanteVO.getGrupoColaboradorParticipanteEscolhido()) {
                    UsuarioVO usuarioVO = getFacade().getUsuario().consultarPorCodigoPessoa(participanteVO.getColaboradorParticipante().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                    boolean adicionar = false;
                    if (UteisValidacao.emptyString(getTipoColaborador())) {
                        adicionar = true;
                    } else {
                        ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoUsuario(usuarioVO.getCodigo(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (colaboradorVO != null) {
                            for (TipoColaboradorVO tipoColaboradorVO : colaboradorVO.getListaTipoColaboradorVOs()) {
                                if (tipoColaboradorVO.getDescricao().equals(getTipoColaborador())) {
                                    adicionar = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (adicionar) {
                        novaListaUsuario.add(usuarioVO);
                    }
                }

                if (!participanteVO.getGrupoColaboradorParticipanteEscolhido() && grupoColaboradorVO.getTodosParticipantesSelecionados()) {
                    grupoColaboradorVO.setTodosParticipantesSelecionados(false);
                }
            }
        }
        
        //Veirificar quantos consultores foram selecionados.
        if (!UteisValidacao.emptyList(getListaGrupoColaborador())) {
            for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
                if (grupo.getTipoGrupo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
                    for (GrupoColaboradorParticipanteVO participanteVO : grupo.getGrupoColaboradorParticipanteVOs()) {
                        if (participanteVO.getGrupoColaboradorParticipanteEscolhido()) {
                            contador++;
                        }
                    }
                }
            }
        }
        
        setQuantidadeConsultorSelecionado(contador);
        setListaUsuariosSelecionados(novaListaUsuario);

        int total = getListaUsuariosSelecionados().size();
        setTotalUsuariosSelecionados(String.valueOf(total));
    }

    public void montarListaGrupoColaborador() throws Exception {
        montarListaGrupoColaborador(null, null);
    }

    public void montarListaGrupoColaborador(UsuarioVO usuarioVO, EmpresaVO empresaVO) throws Exception {
        try {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

            GrupoTelaControle grupoTelaControle;
            if(context() == null){
                grupoTelaControle = new GrupoTelaControle(usuarioVO, empresaVO);
            }else{
                grupoTelaControle = (GrupoTelaControle) context().getExternalContext().getSessionMap().get("GrupoTelaControle");
            }
            if (grupoTelaControle == null) {
                grupoTelaControle = new GrupoTelaControle();
            }
            List<GrupoColaboradorVO> lista = grupoTelaControle.getListaGrupos();
            Ordenacao.ordenarLista(lista, "descricaoOrdenacao");


            String[] codigosUsuariosLista = Uteis.retornarCodigos(getListaUsuariosSelecionados()).split(",");

            for (String codigoUsuario : codigosUsuariosLista) {
                for (GrupoColaboradorVO grupoColaboradorVO : lista) {
                    for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                        if (grupoColaboradorParticipanteVO.getUsuarioParticipante().getCodigo() != 0) {
                            if (grupoColaboradorParticipanteVO.getUsuarioParticipante().getCodigo().toString().equals(codigoUsuario)) {
                                grupoColaboradorParticipanteVO.setGrupoColaboradorParticipanteEscolhido(true);
                            }
                        }
                    }
                    Ordenacao.ordenarLista(grupoColaboradorVO.getGrupoColaboradorParticipanteVOs(), "nomeColaboradorParticipante");
                }
            }
            setListaGrupoColaborador(lista);

            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            System.out.println("Montar Lista de Colaboradores: " + (d2.getTime() - d1.getTime()));
        } catch (Exception e) {
            setListaGrupoColaborador(new ArrayList<GrupoColaboradorVO>());
            setMensagemDetalhada("", e.getMessage());
            montarMsgAlert(e.getMessage());
        }
    }

    private Boolean validarColaboradorLogado() throws Exception {
        try {
            // marca ou desmarca todos os iguais na lista de grupos
            boolean visualizarTudo = false;
            for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
                if (grupo.isSemGrupo() && getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                    visualizarTudo = true;
                    continue;
                }
                for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                    if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(participante.getColaboradorParticipante().getCodigo())) {
                        participante.setGrupoColaboradorParticipanteEscolhido(true);
                    }
                }
            }

            if (visualizarTudo) {
                for (GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
                    for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                        participante.setGrupoColaboradorParticipanteEscolhido(true);
                    }
                }
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public void montarTabelaPorObjecao() throws Exception{
        this.listaClientePorObjecao = new ArrayList<ItemRelatorioTO>();

        String[] objecoes = new String[]{};
        if (isPorFase()) {
            objecoes = new String[]{getTituloObjecao()};
        } else {
            if (getTituloObjecao().equals("Outras Objeções")) {
                List<String> objs = new ArrayList<String>();
                for (Integer codObjecao : getObjecoesOutros()) {
                    objs.add(codObjecao.toString());
                }
                objecoes = objs.toArray(objecoes);
            } else {
                objecoes = new String[]{getCodigoObjecao().toString()};
            }
        }

        setListaClientePorObjecao(getFacade().getCliente().listaClientePorObjecaoDefinida(getDataInicio(), getDataFinal(), isPorFase(), getListaUsuariosSelecionados(), objecoes));
        quantidadeClientePorObjecao = getListaClientePorObjecao().size();
        for (ItemRelatorioTO itemRelatorioTO : getListaClientePorObjecao()) {
             setMostrarTodosFiltros(itemRelatorioTO.getTipoCliente().equals("Cliente"));
        }
        if (isMostrarTodosFiltros()) {
            setOncompleteRetorno("Richfaces.showModalPanel('modalListaPorObjecoesClientes')");
        }else{
            setOncompleteRetorno("Richfaces.showModalPanel('modalListaPorObjecoesClientesReduzido')");
        }
    }
    
    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
        return configuracaoSistemaCRMVO;
    }

    public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
    }

    public void setTituloObjecao(String tituloObjecao) {
        this.tituloObjecao = tituloObjecao;
    }

    public String getTituloObjecao() {
        return tituloObjecao;
    }

    public void setPorFase(boolean porFase) {
        this.porFase = porFase;
    }

    public boolean isPorFase() {
        return porFase;
    }

    public void setListaClientePorObjecao(List<ItemRelatorioTO> listaClientePorObjecao) {
        this.listaClientePorObjecao = listaClientePorObjecao;
    }

    public List<ItemRelatorioTO> getListaClientePorObjecao() {
        return listaClientePorObjecao;
    }

    public void setQuantidadeClientePorObjecao(Integer quantidadeClientePorObjecao) {
        this.quantidadeClientePorObjecao = quantidadeClientePorObjecao;
    }

    public Integer getQuantidadeClientePorObjecao() {
        return quantidadeClientePorObjecao;
    }

    public void setMostrarTodosFiltros(boolean mostrarTodosFiltros) {
        this.mostrarTodosFiltros = mostrarTodosFiltros;
    }

    public boolean isMostrarTodosFiltros() {
        return mostrarTodosFiltros;
    }

    public void setOncompleteRetorno(String oncompleteRetorno) {
        this.oncompleteRetorno = oncompleteRetorno;
    }

    public String getOncompleteRetorno() {
        return oncompleteRetorno;
    }

    public String getFaseSelecionada() {
        if (faseSelecionada == null) {
            faseSelecionada = "";
        }
        return faseSelecionada;
    }

    public void setFaseSelecionada(String faseSelecionada) {
        this.faseSelecionada = faseSelecionada;
    }

    public String getAtributosExportacao() {
        if (FasesCRMEnum.VENCIDOS.getSigla().equals(getFaseSelecionada())) {
            return "matricula=Matricula,nomePessoaRel=Nome,dataRegistro_ApresentarBI=Data Cadastro,emails=Emails,telefones=Telefones,faseMeta=Fase,dataMeta=Data Meta,situacaoCliente=Situação,situacaoContrato=Situação Contrato,idadePessoa=Idade,estadoCivilPessoa=Estado Civil,tipoAgendamento=Tipo Agendamento,dataAgendamento=Data Agendamento,horaAgendamento=Hora Agendamento";
        }
        return "matricula=Matricula,nomePessoaRel=Nome,dataRegistro_ApresentarBI=Data Cadastro,emails=Emails,telefones=Telefones,faseMeta=Fase,dataMeta=Data Meta,situacaoCliente=Situação,situacaoContrato=Situação Contrato,idadePessoa=Idade,estadoCivilPessoa=Estado Civil,tipoAgendamento=Tipo Agendamento,dataAgendamento=Data Agendamento,horaAgendamento=Hora Agendamento";
    }

    public List<Integer> getObjecoesOutros() {
        if (objecoesOutros == null) {
            objecoesOutros = new ArrayList<Integer>();
        }
        return objecoesOutros;
    }

    public void setObjecoesOutros(List<Integer> objecoesOutros) {
        this.objecoesOutros = objecoesOutros;
    }

    public Integer getCodigoObjecao() {
        if (codigoObjecao == null) {
            codigoObjecao = 0;
        }
        return codigoObjecao;
    }

    public void setCodigoObjecao(Integer codigoObjecao) {
        this.codigoObjecao = codigoObjecao;
    }
    
    public String getCodigosMetaExtraIndividual() {
        if (codigosMetaExtraIndividual == null) {
            codigosMetaExtraIndividual = "";
        }
        return codigosMetaExtraIndividual;
    }

    public void setCodigosMetaExtraIndividual(String codigosMetaExtraIndividual) {
        this.codigosMetaExtraIndividual = codigosMetaExtraIndividual;
    }
    
    public String getCodigosMetaExtra() {
        if (codigosMetaExtra == null) {
            codigosMetaExtra = "";
        }
        return codigosMetaExtra;
    }

    public void setCodigosMetaExtra(String codigosMetaExtra) {
        this.codigosMetaExtra = codigosMetaExtra;
    }

    public String getCodigosMetaTodos() {
        if (codigosMetaTodos == null) {
            codigosMetaTodos = "";
        }
        return codigosMetaTodos;
    }

    public void setCodigosMetaTodos(String codigosMetaTodos) {
        this.codigosMetaTodos = codigosMetaTodos;
    }

    public Integer getQuantidadeConsultorSelecionado() {
        if (quantidadeConsultorSelecionado == null) {
            quantidadeConsultorSelecionado = 0;
        }
        return quantidadeConsultorSelecionado;
    }

    public void setQuantidadeConsultorSelecionado(Integer quantidadeConsultorSelecionado) {
        this.quantidadeConsultorSelecionado = quantidadeConsultorSelecionado;
    }

    public boolean isMetaExtra() {
        return metaExtra;
    }

    public void setMetaExtra(boolean metaExtra) {
        this.metaExtra = metaExtra;
    }
    public String getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(String tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public List<SelectItem> getListaSelectItemTipoColaborador() {
        return listaSelectItemTipoColaborador;
    }

    public void setListaSelectItemTipoColaborador(List<SelectItem> listaSelectItemTipoColaborador) {
        this.listaSelectItemTipoColaborador = listaSelectItemTipoColaborador;
    }

}

package controle.crm;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.basico.ColaboradorVO;
import java.util.Iterator;
import java.util.Date;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.comuns.utilitarias.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.FecharMetaVO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * fecharMetaForm.jsp fecharMetaCons.jsp) com as funcionalidades da classe <code>FecharMeta</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see FecharMeta
 * @see FecharMetaVO
*/
public class FecharMetaControle extends SuperControle {
    private FecharMetaVO fecharMetaVO;
    protected List listaSelectItemColaborador;
    /**
    * Interface <code>FecharMetaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */
    private FecharMetaDetalhadoVO fecharMetaDetalhadoVO;
    private String campoConsultarCliente;
    private String valorConsultarCliente;
    private List listaConsultarCliente;
    //abaixo são atributos que não serão gravados no banco
    private List listaConsultaClientePotencial;


    public FecharMetaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>FecharMeta</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setFecharMetaVO(new FecharMetaVO());
        inicializarListasSelectItemTodosComboBox();
        setFecharMetaDetalhadoVO(new FecharMetaDetalhadoVO());
        setListaConsultaClientePotencial(new ArrayList());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>FecharMeta</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        FecharMetaVO obj = (FecharMetaVO)context().getExternalContext().getRequestMap().get("fecharMeta");
        inicializarAtributosRelacionados(obj);
        obj.setNovoObj(new Boolean(false));
        setFecharMetaVO(obj);
        inicializarListasSelectItemTodosComboBox();
        setFecharMetaDetalhadoVO(new FecharMetaDetalhadoVO());
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Método responsável inicializar objetos relacionados a classe <code>FecharMetaVO</code>.
    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
    */
    public void inicializarAtributosRelacionados(FecharMetaVO obj) {     
    }

    public void gravar() {
        try {
            if (fecharMetaVO.isNovoObj()) {
                getFacade().getFecharMeta().incluir(fecharMetaVO);
            } else {
                getFacade().getFecharMeta().alterar(fecharMetaVO);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(true);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP FecharMetaCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getFecharMeta().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataRegistro")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getFecharMeta().consultarPorDataRegistro(Uteis.getDateTime(valorData,0,0,0), Uteis.getDateTime(valorData,23,59,59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getFecharMeta().consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("identificadorMeta")) {
                objs = getFacade().getFecharMeta().consultarPorIdentificadorMeta(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>FecharMetaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getFecharMeta().excluir(fecharMetaVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>FecharMetaDetalhado</code>
     * para o objeto <code>fecharMetaVO</code> da classe <code>FecharMeta</code>
     */ 
    public String adicionarFecharMetaDetalhado() throws Exception {
        try {
            if (!getFecharMetaVO().getCodigo().equals(new Integer(0))) {
                fecharMetaDetalhadoVO.setFecharMeta(getFecharMetaVO());
            }
            if (getFecharMetaDetalhadoVO().getCliente().getCodigo().intValue() != 0){
                Integer campoConsulta = getFecharMetaDetalhadoVO().getCliente().getCodigo();
                ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
                getFecharMetaDetalhadoVO().setCliente(cliente);
            }
            getFecharMetaVO().adicionarObjFecharMetaDetalhadoVOs( getFecharMetaDetalhadoVO());
            this.setFecharMetaDetalhadoVO(new FecharMetaDetalhadoVO());
            setMensagemID("msg_dados_adicionados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>FecharMetaDetalhado</code>
     * para edição pelo usuário.
     */ 
    public String editarFecharMetaDetalhado() throws Exception {
        FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO)context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
        setFecharMetaDetalhadoVO(obj);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>FecharMetaDetalhado</code>
     * do objeto <code>fecharMetaVO</code> da classe <code>FecharMeta</code>
     */ 
    public String removerFecharMetaDetalhado() throws Exception {
        FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO)context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
        getFecharMetaVO().excluirObjFecharMetaDetalhadoVOs(obj.getCliente().getCodigo());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Cliente</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados no richModal
     * montando automaticamente o resultado da consulta para apresentação.
    */
    public void consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("codigo")) {
                if (getValorConsultarCliente().equals("")) {
                    setValorConsultarCliente("0");
                }
                Integer valorInt = Integer.parseInt(getValorConsultarCliente());
                objs = getFacade().getCliente().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("nomePessoa")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, null);
            }
            if (getCampoConsultarCliente().equals("situacao")) {
                objs = getFacade().getCliente().consultarPorSituacao(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                objs = getFacade().getCliente().consultarPorMatricula(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("nomeCategoria")) {
                objs = getFacade().getCliente().consultarPorNomeCategoria(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("codAcesso")) {
                objs = getFacade().getCliente().consultarPorCodAcesso(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("banco")) {
                objs = getFacade().getCliente().consultarPorBanco(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("agencia")) {
                objs = getFacade().getCliente().consultarPorAgencia(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarCliente().equals("conta")) {
                objs = getFacade().getCliente().consultarPorConta(getValorConsultarCliente(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarCliente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    
   
    
    
    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        if (getMensagemDetalhada().equals("")) {
            this.getFecharMetaDetalhadoVO().setCliente(obj);
        }
        Uteis.liberarListaMemoria(this.getListaConsultarCliente());
        this.setValorConsultarCliente(null);
        this.setCampoConsultarCliente(null);
    }
     
    public void limparCampoCliente() {
        this.getFecharMetaDetalhadoVO().setCliente( new ClienteVO());
    }

    /**
    * Rotina responsável por preencher a combo de consulta dos RichModal da telas.
    */
    public List getTipoConsultarComboCliente() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomePessoa", "Pessoa"));
        itens.add(new SelectItem("situacao", "Situação"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        itens.add(new SelectItem("nomeCategoria", "Categoria"));
        itens.add(new SelectItem("codAcesso", "Código de Acesso"));
        itens.add(new SelectItem("banco", "Banco"));
        itens.add(new SelectItem("agencia", "Agência"));
        itens.add(new SelectItem("conta", "Conta"));
        return itens;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Colaborador</code>.
    */
    public void montarListaSelectItemColaborador(String prm) throws Exception {
        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ColaboradorVO obj = (ColaboradorVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
        }
        Uteis.liberarListaMemoria(resultadoConsulta);
        setListaSelectItemColaborador(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Colaborador</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemColaborador() {
        try {
            montarListaSelectItemColaborador("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarColaboradorPorSituacao(String situacaoPrm) throws Exception {
        List lista = getFacade().getColaborador().consultarPorSituacao(situacaoPrm, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
    */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemColaborador();
    }

    /**
    * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
    */
    public String getMascaraConsulta() {
        return "";
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("dataRegistro", "Data Registro"));
        itens.add(new SelectItem("situacaoColaborador", "Colaborador"));
        itens.add(new SelectItem("identificadorMeta", "Identificador Meta"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
    * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente 
    * quando realiza o logout.
    */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        fecharMetaVO = null;
        Uteis.liberarListaMemoria(listaSelectItemColaborador);
        fecharMetaDetalhadoVO = null;
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String getCampoConsultarCliente() {
        return campoConsultarCliente;
    }
     
    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }
     
    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }
     
    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }
     
    public List getListaConsultarCliente() {
        return listaConsultarCliente;
    }
     
    public void setListaConsultarCliente(List listaConsultarCliente) {
        this.listaConsultarCliente = listaConsultarCliente;
    }

    public FecharMetaDetalhadoVO getFecharMetaDetalhadoVO() {
        return fecharMetaDetalhadoVO;
    }
     
    public void setFecharMetaDetalhadoVO(FecharMetaDetalhadoVO fecharMetaDetalhadoVO) {
        this.fecharMetaDetalhadoVO = fecharMetaDetalhadoVO;
    }

    public List getListaSelectItemColaborador() {
        if (listaSelectItemColaborador == null) {
            listaSelectItemColaborador = new ArrayList();
        }
        return (listaSelectItemColaborador);
    }
     
    public void setListaSelectItemColaborador( List listaSelectItemColaborador ) {
        this.listaSelectItemColaborador = listaSelectItemColaborador;
    }

    public FecharMetaVO getFecharMetaVO() {
        return fecharMetaVO;
    }
     
    public void setFecharMetaVO(FecharMetaVO fecharMetaVO) {
        this.fecharMetaVO = fecharMetaVO;
    }

	public List getListaConsultaClientePotencial() {
		if(listaConsultaClientePotencial == null){
			listaConsultaClientePotencial = new ArrayList();
		}
		return listaConsultaClientePotencial;
	}

	public void setListaConsultaClientePotencial(List listaConsultaClientePotencial) {
		this.listaConsultaClientePotencial = listaConsultaClientePotencial;
	}
}
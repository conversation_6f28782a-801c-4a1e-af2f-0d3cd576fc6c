package controle.crm;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import controle.basico.GrupoTelaControle;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.FecharMeta;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Lhoji Shiozawa
 */
public class TotalizadorMetaControle extends SuperControleRelatorio {
    private Date dataInicio;
    private Date dataTermino;
    private String filtros;
    private boolean metaEmAberto;
    private List<FecharMetaVO> totais;
    private List<FecharMetaVO> fases;
    private List<FecharMetaVO> fasesRenovacao;
    private List<FecharMetaVO> fasesRelacionamento;
    private List<FecharMetaVO> totaisResultado;
    private FecharMetaVO metaTotal;
    private FecharMetaVO metaRenovacao;
    private FecharMetaVO metaFidelizacao;
    private List<ColaboradorVO> listaColaboradorVOs;
    private List<GrupoColaboradorVO> listaGrupoColaborador;
    private Integer codigoEmpresaConsulta;
	private List<SelectItem> listaSelectItemEmpresa;

    public TotalizadorMetaControle() {
        novo();
    }

    public final void novo() {
        try {
            GrupoTelaControle gtc = (GrupoTelaControle)context().getExternalContext().getSessionMap().get("GrupoTelaControle");
            if(gtc == null)
                gtc = new GrupoTelaControle();
            listaGrupoColaborador = gtc.getListaGrupos();
            listaColaboradorVOs = gtc.getListaColaboradores();
            mesAtual();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public int getTamanhoListaColaborador() {
        int x = getListaColaboradorVOs().size();
        return (x == 0 ? 1 : x);
    }

    public void selecionarGrupoColaboradorParticipante() throws Exception {
        try {
            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
            obj.setAbrirSimpleTooglePanelPassivo(!obj.getAbrirSimpleTooglePanelPassivo());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarParticipante() {
        GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("participante");
        boolean selecionado = obj.getGrupoColaboradorParticipanteEscolhido();
        // marca ou desmarca todos os iguais na lista de grupos
        for(GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
            for(GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if(obj.getColaboradorParticipante().equals(participante.getColaboradorParticipante())) {
                    participante.setGrupoColaboradorParticipanteEscolhido(selecionado);
                }
            }
        }
        // marca ou desmarca todos os iguais na lista de nao repetidos
        for(ColaboradorVO colaborador : getListaColaboradorVOs()) {
            if(obj.getColaboradorParticipante().equals(colaborador))
                colaborador.setColaboradorEscolhido(selecionado);
        }
    }
    public void validarDados() throws Exception{
        if(Calendario.menor(dataTermino, dataInicio)){
            throw new Exception("A DATA DE TÉRMINO da pesquisa deve ser maior que a DATA DE INÍCIO");
        }
    }
    @SuppressWarnings("unchecked")
	public void montarListaSelectItemEmpresa() throws Exception {
		this.setListaSelectItemEmpresa(new ArrayList<SelectItem>());
        List<EmpresaVO> empresas = new ArrayList<EmpresaVO>();
        empresas = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for(EmpresaVO empresa : empresas){
        	this.getListaSelectItemEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
	}

    public String totalizarMeta() {
        try {
        	Integer empresa = null;
        	if(!getUsuarioLogado().getAdministrador()){
        		empresa = getEmpresaLogado().getCodigo();
        	}else{
        		empresa = getCodigoEmpresaConsulta();
        	}
            validarDados();
            totais = new ArrayList<FecharMetaVO>();
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<FecharMetaVO> lista = getFacade().getFecharMeta().consultarPorPeriodoColaboradoresResponsaveis(dataInicio, dataTermino, listaColaboradorVOs, metaEmAberto, true, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            double qtdDeColaboradores = getFacade().getFecharMeta().contarColaboradoresNasMetasPorPeriodoColaboradoresResponsaveis(dataInicio, dataTermino, listaColaboradorVOs, metaEmAberto, true, empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            // percorre a lista consultada
            for (FecharMetaVO meta : lista) {
                FecharMetaVO alterar = null;
                // verifica se a meta ja esta na lista final
                for (FecharMetaVO aux : totais){
                    if (meta.getIdentificadorMeta().equals(aux.getIdentificadorMeta())) {
                        alterar = aux;
                        break;
                    }
                }
                // se nao encontrou a meta
                if (alterar == null) {
                    if (meta.getMeta().intValue() > 0) {
                        meta.setPorcentagem((meta.getMetaAtingida() + meta.getRepescagem()) * 100 / meta.getMeta());
                    }
                    meta.setPerc(Formatador.formatarValorMonetarioSemMoeda(meta.getPorcentagem()) + "%");
                    // adiciona na lista final
                    totais.add(meta);
                } else {
                    // senao altera os valores ja presentes na lista
                    alterar.setMeta(meta.getMeta() + alterar.getMeta());
                    alterar.setMetaAtingida(meta.getMetaAtingida() + alterar.getMetaAtingida());
                    alterar.setRepescagem(meta.getRepescagem() + alterar.getRepescagem());
                    if (alterar.getMeta() > 0)
                        alterar.setPorcentagem((alterar.getMetaAtingida() + alterar.getRepescagem()) * 100 / alterar.getMeta());
                    alterar.setPerc(Formatador.formatarValorMonetarioSemMoeda(alterar.getPorcentagem()) + "%");
                }
            }
            totaisResultado = new ArrayList<FecharMetaVO>();
            fases = new ArrayList<FecharMetaVO>();
            FecharMetaVO total = new FecharMetaVO();
            metaRenovacao = new FecharMetaVO();
            metaFidelizacao = new FecharMetaVO();
            total.setIdentificadorMeta("TO");
            for (FecharMetaVO aux : totais) {
                if (aux.getOrdemTotalizador() > 0) {
                    if (aux.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                        aux = calcularTotalMeta(configuracaoSistemaCRMVO.getIndicacoesMes(), qtdDeColaboradores, aux);
                        totaisResultado.add(aux);
                    } else if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())
                            || aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())
                            || aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())
                            || aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())
                            || aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                        aux = calcularTotalConversao(aux, new ArrayList<FecharMetaVO>(totais));

                        totaisResultado.add(aux);
                    } else {
                        fases.add(aux);
                        total.setMeta(total.getMeta() + aux.getMeta());
                        total.setMetaAtingida(total.getMetaAtingida() + aux.getMetaAtingida());
                        total.setRepescagem(total.getRepescagem() + aux.getRepescagem());
                        if (total.getMeta() > 0)
                            total.setPorcentagem((total.getMetaAtingida() + total.getRepescagem()) * 100 / total.getMeta());
                        total.setPerc(Formatador.formatarValorMonetarioSemMoeda(total.getPorcentagem()) + "%");
                    }
                }
            }
            Ordenacao.ordenarLista(fases, "ordemTotalizador");
            Ordenacao.ordenarLista(totaisResultado, "ordemTotalizador");
            //Usado para divir metas Relacionamento e Renovação
            setMetaTotal(total);
            montarMetasRenovacaoRelacionamento();
            //   fases.add(total);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }
    public void atribuirMetaFidelizacao(FecharMetaVO fecharMetaVO){

        getMetaFidelizacao().setMeta(getMetaFidelizacao().getMeta()+fecharMetaVO.getMeta());
        getMetaFidelizacao().setMetaAtingida(getMetaFidelizacao().getMetaAtingida()+fecharMetaVO.getMetaAtingida());
        getMetaFidelizacao().setRepescagem(getMetaFidelizacao().getRepescagem()+fecharMetaVO.getRepescagem());
    }
    public void atribuirMetaRenovacao(FecharMetaVO fecharMetaVO){

        getMetaRenovacao().setMeta(getMetaRenovacao().getMeta() + fecharMetaVO.getMeta());
        getMetaRenovacao().setMetaAtingida(getMetaRenovacao().getMetaAtingida() + fecharMetaVO.getMetaAtingida());
        getMetaRenovacao().setRepescagem(getMetaRenovacao().getRepescagem() + fecharMetaVO.getRepescagem());
    }
    public void montarMetasRenovacaoRelacionamento(){
        getFasesRelacionamento().clear();
        getFasesRenovacao().clear();
        for(FecharMetaVO fecharMetaVO : fases){
            if(fecharMetaVO.getFase().getTipoFase() == TipoFaseCRM.VENDAS){
                getFasesRenovacao().add(fecharMetaVO);

                atribuirMetaRenovacao(fecharMetaVO);
            }else if(fecharMetaVO.getFase().getTipoFase() == TipoFaseCRM.RETENCAO){
                getFasesRelacionamento().add(fecharMetaVO);
                atribuirMetaFidelizacao(fecharMetaVO);
            }

        }
        getMetaFidelizacao().setPorcentagem(((getMetaFidelizacao().getMetaAtingida()+ getMetaFidelizacao().getRepescagem())*100)/getMetaFidelizacao().getMeta());
        getMetaFidelizacao().setPerc(Formatador.formatarValorMonetarioSemMoeda(getMetaFidelizacao().getPorcentagem()) + "%");
        getMetaRenovacao().setPorcentagem(((getMetaRenovacao().getMetaAtingida()+getMetaRenovacao().getRepescagem())*100)/getMetaRenovacao().getMeta());
        getMetaRenovacao().setPerc(Formatador.formatarValorMonetarioSemMoeda(getMetaRenovacao().getPorcentagem()) + "%");
    }
    private FecharMetaVO calcularTotalConversao(FecharMetaVO aux, ArrayList<FecharMetaVO> fecharMetaVOs) {
        Double meta = 0.0;
        for (FecharMetaVO fm : fecharMetaVOs) {
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_DESISTENTES.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.DESISTENTES.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_INDICADOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.INDICACOES.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.VISITANTES_ANTIGOS.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }

            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.VISITA_RECORRENTE.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.EX_ALUNOS.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
            if (aux.getIdentificadorMeta().equals(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla())) {
                if (fm.getIdentificadorMeta().equals(FasesCRMEnum.AGENDAMENTO.getSigla())) {
                    meta = fm.getMetaAtingida() + fm.getRepescagem();
                }
            }
        }
        aux.setMeta(meta);
        aux.calcularPorcentagem();
        return aux;
    }

    private FecharMetaVO calcularTotalMeta(Integer qtdConfiguracao, double qtdDeColaboradores, FecharMetaVO aux) {
        long dias = Uteis.nrDiasEntreDatas(dataInicio, Uteis.somarDias(dataTermino, 1));
        double valor = (qtdConfiguracao * dias) / Uteis.obterNumeroDiasDoMes(dataInicio);
        aux.setMeta(Math.ceil(qtdDeColaboradores * valor));

        aux.calcularPorcentagem();
        return aux;
    }

    public void imprimirRelatorio() {
        try {
        	JRDataSource jr1 = new JRBeanArrayDataSource(totaisResultado.toArray(), false);
            setRelatorio("sim");
            setListaRelatorio(fases);
            Map<String, Object> parametros = new HashMap<String, Object>();
            parametros.put("tipoRelatorio", getTipoRelatorio());
            parametros.put("nomeRelatorio", "TotalizadorMetaRel");
            parametros.put("nomeEmpresa", getEmpresaLogado().getNome());
            parametros.put("mensagemRel", "");
            parametros.put("imagemLogo", "");
            parametros.put("tituloRelatorio", "Totalizador de Metas");
            parametros.put("caminhoParserXML", "/Totalizador/registros");
            parametros.put("nomeDesignIReport", getDesignIReportRelatorio());
            parametros.put("usuario", getUsuarioLogado().getNome());
            parametros.put("listaObjetos", getListaRelatorio());
            parametros.put("tipoImplementacao", "OBJETO");
            parametros.put("listaTotais", jr1);
            parametros.put("SUBREPORT_DIR", "relatorio"+ File.separator +"designRelatorio"+ File.separator +"crm"+ File.separator);
            String filtros = getDescricaoFiltros();
            if (filtros.equals("")) {
                filtros = "nenhum";
            }
            parametros.put("filtros", filtros);
            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void mesAtual() throws Exception {
        dataInicio = Uteis.obterPrimeiroDiaMes(negocio.comuns.utilitarias.Calendario.hoje());
        dataTermino = Uteis.obterUltimoDiaMes(negocio.comuns.utilitarias.Calendario.hoje());
    }

    public void mesAnterior() throws Exception {
        Date mesAnterior = Uteis.obterDataAnterior(1);
        dataInicio = Uteis.obterPrimeiroDiaMes(mesAnterior);
        dataTermino = Uteis.obterUltimoDiaMes(mesAnterior);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public boolean isMetaEmAberto() {
        return metaEmAberto;
    }

    public void setMetaEmAberto(boolean metaEmAberto) {
        this.metaEmAberto = metaEmAberto;
    }

    public List<FecharMetaVO> getTotais() {
        return totais;
    }

    public void setTotais(List<FecharMetaVO> totais) {
        this.totais = totais;
    }

    public List<ColaboradorVO> getListaColaboradorVOs() {
        return listaColaboradorVOs;
    }

    public void setListaColaboradorVOs(List<ColaboradorVO> listaColaboradorVOs) {
        this.listaColaboradorVOs = listaColaboradorVOs;
    }

    public FecharMetaVO getMetaRenovacao() {
        if(metaRenovacao==null)
            metaRenovacao = new FecharMetaVO();
        return metaRenovacao;
    }

    public void setMetaRenovacao(FecharMetaVO metaRenovacao) {
        this.metaRenovacao = metaRenovacao;
    }

    public FecharMetaVO getMetaFidelizacao() {
        if(metaFidelizacao==null)
            metaFidelizacao = new FecharMetaVO();
        return metaFidelizacao;
    }

    public void setMetaFidelizacao(FecharMetaVO metaFidelizacao) {
        this.metaFidelizacao = metaFidelizacao;
    }

    public List<GrupoColaboradorVO> getListaGrupoColaborador() {
        return listaGrupoColaborador;
    }

    public void setListaGrupoColaborador(List<GrupoColaboradorVO> listaGrupoColaborador) {
        this.listaGrupoColaborador = listaGrupoColaborador;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }
    
    private String getDescricaoFiltros() throws Exception {
        StringBuilder aux = new StringBuilder();
        aux.append(getPeriodoFiltro()+" / ");
        aux.append(getMetaFiltro()+" / ");
        aux.append(getColaboradoresFiltro());
        return aux.toString();
    }

    private String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "crm" + File.separator + "TotalizadorMetaRel.jrxml");
    }

    public String getPeriodoFiltro() {
        return " Período de " + Uteis.getData(dataInicio) + " Até " + Uteis.getData(dataTermino);
    }

    public String getMetaFiltro() {
        return (metaEmAberto ? " Somente Metas em Aberto" : " Somente Metas Fechadas");
    }

    public String getColaboradoresFiltro() {
        StringBuilder ret = new StringBuilder();
        // uso o iterator para controlar a existencia de mais registros
        Iterator i = listaColaboradorVOs.iterator();
        while(i.hasNext()) {
            ColaboradorVO aux = (ColaboradorVO)i.next();
            if(aux.getColaboradorEscolhido()) {
                if(ret.toString().isEmpty())
                    ret.append(" Colaboradores Responsáveis: ");
                ret.append(aux.getPessoa().getNome()+ ", ");
            }
        }
        if(ret.toString().isEmpty())
            return ret.toString();
        else
            return ret.toString().substring(0, ret.toString().length()-2);
    }
    /**
     * Rotina responsável por navegar para a pagina de consulta
     */
    public String inicializarConsultar() {
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(true);
        setErro(false);
        return "consultar";
    }

	/**
     * @return the codigoEmpresaConsulta
     */
    public Integer getCodigoEmpresaConsulta() {
        return codigoEmpresaConsulta;
    }

	/**
     * @param codigoEmpresaConsulta the codigoEmpresaConsulta to set
     */
    public void setCodigoEmpresaConsulta(Integer codigoEmpresaConsulta) {
        this.codigoEmpresaConsulta = codigoEmpresaConsulta;
    }

    /**
	 * @return the listaSelectItemEmpresa
	 * @throws Exception 
	 */
	public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
		if(listaSelectItemEmpresa == null){
			montarListaSelectItemEmpresa();
		}
		return listaSelectItemEmpresa;
	}

	public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
		this.listaSelectItemEmpresa = listaSelectItemEmpresa;
		
	}

    public List<FecharMetaVO> getFasesRenovacao() {
        if(fasesRenovacao== null)
        fasesRenovacao = new ArrayList<FecharMetaVO>();
        return fasesRenovacao;
    }


    public void setFasesRenovacao(List<FecharMetaVO> fasesRenovacao) {
        this.fasesRenovacao = fasesRenovacao;
    }

    public List<FecharMetaVO> getFasesRelacionamento() {
        if(fasesRelacionamento == null)
        fasesRelacionamento = new ArrayList<FecharMetaVO>();
        return fasesRelacionamento;
    }

    public void setFasesRelacionamento(List<FecharMetaVO> fasesRelacionamento) {
        this.fasesRelacionamento = fasesRelacionamento;
    }

    public List<FecharMetaVO> getFases() {
        return fases;
    }

    public FecharMetaVO getMetaTotal() {
        return metaTotal;
    }

    public void setMetaTotal(FecharMetaVO metaTotal) {
        this.metaTotal = metaTotal;
    }

    public void setFases(List<FecharMetaVO> fases) {
        this.fases = fases;
    }

    public List<FecharMetaVO> getTotaisResultado() {
        return totaisResultado;
    }

    public void setTotaisResultado(List<FecharMetaVO> totaisResultado) {
        this.totaisResultado = totaisResultado;
    }
}

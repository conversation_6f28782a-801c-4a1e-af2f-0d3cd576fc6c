package controle.crm;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.faces.event.ActionEvent;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.DiasDaSemana;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import controle.arquitetura.SuperControle;
import controle.basico.GrupoControle;
import controle.basico.GrupoTelaControle;

/**
 * <AUTHOR>
 * 12/05/2011
 * 
 */
public class PendenciasCRMControle  extends SuperControle  {
	
	private static final int METAS_ABERTAS = 1;
	private static final int METAS_FECHADAS = 2;
	private static final int METAS_NAO_ABERTAS = 3;
	
	private List<GrupoColaboradorVO> listaGrupoColaborador;
	private List<ColaboradorVO> listaColaboradorVOs;
	private Integer totalMetasFechadas;
	private Integer totalMetasAbertasNaoFechadas;
	private Integer totalMetasNaoAbertas;
	private List<AberturaMetaVO> metas;
	private List<AberturaMetaVO> listaExibicaoMetas;
	private List<Date> feriados;
	private Date dataPesquisa;
	private Boolean exibirMetas;
    private boolean marcarUsuarioAcompanhamento;
    private boolean mostrarGruposAcompanhamento = false;

    public PendenciasCRMControle(){
		try {
			inicializarGruposColaboradores();
			consultarPendencias();
		} catch (Exception e) {
			setMensagem(e.getMessage());
		}
		
	}
	
	
	/**
	 * Responsável por montar os totais e as listas de pendencias
	 * <AUTHOR>
	 * 12/05/2011
	 */
	public void consultarPendencias(){
		 try {
			 zerarTotais();
			 montarMetas();
			 setarTotais();
		} catch (Exception e) {
			setMensagem(e.getMessage());
		}
	}
	
	/**
	 * listener de controle da exibição de metas 
	 * <AUTHOR>
	 * 13/05/2011
	 */
	public void exibicaoListener(final ActionEvent event) {
		Integer tipo = Integer.valueOf(event.getComponent().getAttributes().get("codigoExibicao").toString());
		exibirMetas(tipo);
	}
		
	/**
	 * Responsável por pesquisar em banco as metas existentes abertas ou não  
	 * <AUTHOR>
	 * 12/05/2011
	 */
	private void montarMetas() throws Exception{
		this.setMetas(new ArrayList<AberturaMetaVO>());
				
		//obter primeiro e ultimo dias do mes
		Date inicio = Uteis.obterPrimeiroDiaMes(getDataPesquisa());
		Date fim = Uteis.getMesData(Calendario.hoje()) == Uteis.getMesData(this.getDataPesquisa()) ? getDataPesquisa() : Uteis.obterUltimoDiaMes(getDataPesquisa());
		//obter feriados
		this.setFeriados(getFacade().getFeriado().consultarDiasFeriados(inicio, fim, getEmpresaLogado()));
		//consultar as metas do colaborador
			this.getMetas().addAll(getFacade().getAberturaMeta().consultarPorResponsavelPeriodo( null,this.getCodigosSelecionados(), 
					inicio, fim, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
			//preencher dias vazios da lista de metas mensais
			montarMetasNaoAbertas(inicio, fim);
		
	}
	
	/**
	 * Responsável por preencher dias vazios da lista de metas mensais
	 * <AUTHOR>
	 * 12/05/2011
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	private void montarMetasNaoAbertas(Date inicio, Date fim) throws Exception{
		//obter configurações do sistema
		ConfiguracaoSistemaCRMVO config = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS);
		
		for(ColaboradorVO colaborador : getListaColaboradorVOs()) {
            if(colaborador.getColaboradorEscolhidoPendencia()){
            	
            	UsuarioVO user = getFacade().getUsuario().consultarPorColaborador(colaborador.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            	if(user == null || UteisValidacao.emptyNumber(user.getCodigo())){
            		continue;
            	}
            	Date recipiente = Calendario.getDataComHoraZerada(inicio);
        		fim = Calendario.getDataComHoraZerada(fim);
        		
        		//enquanto a data recipiente for menor ou igual ao ultimo dia do mes
        		while (Calendario.menorOuIgual(recipiente, fim)){
        			AberturaMetaVO meta = new AberturaMetaVO();
        			meta.getColaboradorResponsavel().setCodigo(user.getCodigo());
        			meta.setDia(recipiente);
                    meta.setMetaEmAberto(null);
        			if(!user.getAdministrador()){
        				meta.getColaboradorResponsavel().setNome(user.getNome());	
        			}
        			//caso esse dia não possua meta e academia seja aberta no dia , adicionar meta vazia
        			if(!this.getMetas().contains(meta) && academiaAberta(recipiente, config)){
        				this.getMetas().add(meta);
        			}
        			//somar um dia na data recipiente
        			recipiente = Uteis.somarCampoData(recipiente, Calendar.DAY_OF_MONTH, 1);
        		}    	
            }
        }  
		
		
		//ordenar a lista pela data
		Collections.sort(getMetas(),  
		        new Comparator() {  
		            public int compare( Object esquerda, Object direita) {
		            	AberturaMetaVO metaE = (AberturaMetaVO) esquerda;
		            	AberturaMetaVO metaD = (AberturaMetaVO) direita;
		            	return metaE.getDia().compareTo(metaD.getDia());  
		            }  
		        });
	}
	
	private Boolean academiaAberta(Date dia, ConfiguracaoSistemaCRMVO config) throws Exception{
		return (!this.getFeriados().contains(Uteis.getDataComHoraZerada(dia)) &&
				!((Uteis.getDiaDaSemana(dia, DiasDaSemana.SABADO) 
		        		&& !config.getAbertoSabado()) 
		        		|| (Uteis.getDiaDaSemana(dia, DiasDaSemana.DOMINGO) 
		        		&& !config.getAbertoDomingo())));
	}
	
	/**
	 * Responsável por zerar os indicadores
	 * <AUTHOR>
	 * 13/05/2011
	 */
	private void zerarTotais(){
		this.setTotalMetasAbertasNaoFechadas(0);
		this.setTotalMetasNaoAbertas(0);
		this.setTotalMetasFechadas(0);
	}
	/**
	 * Responsável por  setar os indicadores
	 * <AUTHOR>
	 * 13/05/2011
	 */
	private void setarTotais(){
		for(AberturaMetaVO meta : this.getMetas()){
            if (meta.getMetaEmAberto() == null) {
                this.setTotalMetasNaoAbertas(this.getTotalMetasNaoAbertas() + 1);
            } else if (!meta.getMetaEmAberto()) {
                this.setTotalMetasFechadas(this.getTotalMetasFechadas() + 1);
            } else {
                this.setTotalMetasAbertasNaoFechadas(this.getTotalMetasAbertasNaoFechadas() + 1);
            }
		}
	}

	/**
	 * Responsável por controlar a exibicao das metas 
	 * <AUTHOR>
	 * 13/05/2011
	 */
	public void exibirMetas(Integer tipo){
		this.setListaExibicaoMetas(new ArrayList<AberturaMetaVO>());
		switch (tipo) {
		case METAS_ABERTAS:
			for(AberturaMetaVO meta : this.getMetas()){
				if(!UteisValidacao.emptyNumber(meta.getCodigo()) && meta.getMetaEmAberto()){
					this.getListaExibicaoMetas().add(meta.getClone());
				}
			}
			break;
		case METAS_FECHADAS:
			for(AberturaMetaVO meta : this.getMetas()){
				if(!UteisValidacao.emptyNumber(meta.getCodigo())&& !meta.getMetaEmAberto()){
					this.getListaExibicaoMetas().add(meta.getClone());
				}
			}
			break;
		case METAS_NAO_ABERTAS:
			for(AberturaMetaVO meta : this.getMetas()){
				if(UteisValidacao.emptyNumber(meta.getCodigo())){
					this.getListaExibicaoMetas().add(meta.getClone());
				}
			}
			break;
		default:
			for(AberturaMetaVO meta : this.getMetas()){
				this.getListaExibicaoMetas().add(meta.getClone());
			}
			break;
		}
		
	}
	 public void inicializarGruposColaboradores() throws Exception {
	        GrupoTelaControle grupoTelaControle = (GrupoTelaControle) context().getExternalContext().getSessionMap().get("GrupoTelaControle");
	        List<GrupoColaboradorVO> grupos = new ArrayList<GrupoColaboradorVO>();
	        if (grupoTelaControle == null)
	            grupoTelaControle = new GrupoTelaControle();
	        if(!UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())){
	        	Iterator<GrupoColaboradorVO> iterator = grupoTelaControle.getListaGrupos().iterator();
	        	while(iterator.hasNext()){
	        		GrupoColaboradorVO grupo = iterator.next();
	        		if(grupo.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())){
	        			grupo.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferenteMontandoUsuario(grupo.getCodigo(), "", Uteis.NIVELMONTARDADOS_TODOS));
	        			grupos.add(grupo);
	        		}
	        	}
	        }
	        setListaGrupoColaborador(grupos);
	        setListaColaboradorVOs(new ArrayList<ColaboradorVO>());
	        for(GrupoColaboradorVO grupo : grupos){
	        	for(GrupoColaboradorParticipanteVO part : grupo.getGrupoColaboradorParticipanteVOs())
	        		getListaColaboradorVOs().add(part.getColaboradorParticipante());
	        }
	        
	        verificarUsuarioLogado();
	 }
	 public void verificarUsuarioLogado() throws Exception{
		 UsuarioVO user = getUsuarioLogado();
		 if(!user.getAdministrador()){
			 for(GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
		            for(GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
		                if(user.getColaboradorVO().getCodigo().equals(participante.getColaboradorParticipante().getCodigo())) {
		                    participante.setGrupoColaboradorParticipanteEscolhido(true);
		                }
		            }
		        }
			 for(ColaboradorVO colaborador : getListaColaboradorVOs()) {
		            if(getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaborador.getCodigo()))
		                colaborador.setColaboradorEscolhidoPendencia(true);
		        }	 
		 }
	 }
	 
	 public void selecionarGrupoColaboradorParticipante() throws Exception {
	        try {
	            GrupoColaboradorVO obj = (GrupoColaboradorVO) context().getExternalContext().getRequestMap().get("grupoColaborador");
	            obj.setAbrirSimpleTooglePanelPassivo(!obj.getAbrirSimpleTooglePanelPassivo());
	        } catch (Exception e) {
	            setMensagemDetalhada("", e.getMessage());
	        }
	    }
	 public void selecionarParticipante() throws Exception {
	        GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("participante");
         if (obj == null) {
             obj = obterColaboradorParticipanteVOLogadoAcompanhamento();
             obj.setGrupoColaboradorParticipanteEscolhido(!obj.getGrupoColaboradorParticipanteEscolhido());
         }
	        boolean selecionado = obj.getGrupoColaboradorParticipanteEscolhido();
	        // marca ou desmarca todos os iguais na lista de grupos
	        for(GrupoColaboradorVO grupo : getListaGrupoColaborador()) {
	            for(GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
	                if(obj.getColaboradorParticipante().equals(participante.getColaboradorParticipante())) {
	                    participante.setGrupoColaboradorParticipanteEscolhido(selecionado);
	                }
	            }
	        }
	        // marca ou desmarca todos os iguais na lista de nao repetidos
	        for(ColaboradorVO colaborador : getListaColaboradorVOs()) {
	            if(obj.getColaboradorParticipante().equals(colaborador))
	                colaborador.setColaboradorEscolhidoPendencia(selecionado);
	        }
	    zerarTotais();
	    montarMetas();
	    setarTotais();
	    }
	// ----------------------------------- GETTERS AND SETTERS -----------------------------------------------
	/**
	 * @param totalMetasNaoAbertas the totalMetasNaoAbertas to set
	 */
	public void setTotalMetasNaoAbertas(Integer totalMetasNaoAbertas) {
		this.totalMetasNaoAbertas = totalMetasNaoAbertas;
	}
	/**
	 * @return the totalMetasNaoAbertas
	 */
	public Integer getTotalMetasNaoAbertas() {
		if(totalMetasNaoAbertas == null){
			totalMetasNaoAbertas = new Integer(0);
		}
		return totalMetasNaoAbertas;
	}
	/**
	 * @param totalMetasAbertasNaoFechadas the totalMetasAbertasNaoFechadas to set
	 */
	public void setTotalMetasAbertasNaoFechadas(Integer totalMetasAbertasNaoFechadas) {
		this.totalMetasAbertasNaoFechadas = totalMetasAbertasNaoFechadas;
	}
	/**
	 * @return the totalMetasAbertasNaoFechadas
	 */
	public Integer getTotalMetasAbertasNaoFechadas() {
		if(totalMetasAbertasNaoFechadas == null){
			totalMetasAbertasNaoFechadas = new Integer(0);
		}
		return totalMetasAbertasNaoFechadas;
	}
	/**
	 * @param totalMetasFechadas the totalMetasFechadas to set
	 */
	public void setTotalMetasFechadas(Integer totalMetasFechadas) {
		this.totalMetasFechadas = totalMetasFechadas;
	}
	/**
	 * @return the totalMetasFechadas
	 */
	public Integer getTotalMetasFechadas() {
		if(totalMetasFechadas == null){
			totalMetasFechadas = new Integer(0);
		}
		return totalMetasFechadas;
	}

	/**
	 * @param metas the metas to set
	 */
	public void setMetas(List<AberturaMetaVO> metas) {
		this.metas = metas;
	}

	/**
	 * @return the metas
	 */
	public List<AberturaMetaVO> getMetas() {
		if(metas == null){
			metas = new ArrayList<AberturaMetaVO>();
		}
		return metas;
	}

	/**
	 * @param dataPesquisa the dataPesquisa to set
	 */
	public void setDataPesquisa(Date dataPesquisa) {
		this.dataPesquisa = dataPesquisa;
	}

	/**
	 * @return the dataPesquisa
	 */
	public Date getDataPesquisa() {
		if(dataPesquisa == null){
			dataPesquisa = Calendario.hoje();
		}
		return dataPesquisa;
	}


	/**
	 * @param exibirMetas the exibirMetas to set
	 */
	public void setExibirMetas(Boolean exibirMetas) {
		this.exibirMetas = exibirMetas;
	}


	/**
	 * @return the exibirMetas
	 */
	public Boolean getExibirMetas() {
		if(exibirMetas == null){
			exibirMetas = Boolean.FALSE;
		}
		return exibirMetas;
	}

	public void setListaExibicaoMetas(List<AberturaMetaVO> listaExibicaoMetas) {
		this.listaExibicaoMetas = listaExibicaoMetas;
	}


	/**
	 * @return the listaCloneMetas
	 */
	public List<AberturaMetaVO> getListaExibicaoMetas() {
		if(listaExibicaoMetas == null){
			listaExibicaoMetas = new ArrayList<AberturaMetaVO>();
		}
		return listaExibicaoMetas;
	}


	/**
	 * @param feriados the feriados to set
	 */
	public void setFeriados(List<Date> feriados) {
		this.feriados = feriados;
	}


	/**
	 * @return the feriados
	 */
	public List<Date> getFeriados() {
		if(feriados == null){
			feriados = new ArrayList<Date>();
		}
		return feriados;
	}


	
	public List<Integer> getCodigosSelecionados() throws Exception{
		List<Integer> codigos = new ArrayList<Integer>();
		for(ColaboradorVO colaborador : this.getListaColaboradorVOs()){
			if(colaborador.getColaboradorEscolhidoPendencia())
				codigos.add(colaborador.getCodigo());	
		}
		
		return codigos;
	}


	/**
	 * @param listaGrupoColaborador the listaGrupoColaborador to set
	 */
	public void setListaGrupoColaborador(List<GrupoColaboradorVO> listaGrupoColaborador) {
		this.listaGrupoColaborador = listaGrupoColaborador;
	}


	/**
	 * @return the listaGrupoColaborador
	 */
	public List<GrupoColaboradorVO> getListaGrupoColaborador() {
		return listaGrupoColaborador;
	}


	/**
	 * @param listaColaboradorVOs the listaColaboradorVOs to set
	 */
	public void setListaColaboradorVOs(List<ColaboradorVO> listaColaboradorVOs) {
		this.listaColaboradorVOs = listaColaboradorVOs;
	}


	/**
	 * @return the listaColaboradorVOs
	 */
	public List<ColaboradorVO> getListaColaboradorVOs() {
		if(listaColaboradorVOs == null){
			listaColaboradorVOs =  new ArrayList<ColaboradorVO>();
		}
		return listaColaboradorVOs;
	}
	
	public Integer getExibicaoMetasTotal(){
		return getListaExibicaoMetas().size();
	}
	
	public String getMensagemTitulo(){
		String titulo = "No mês de "+Uteis.getMesNomeReferencia(this.getDataPesquisa());
		if(Uteis.getMesData(Calendario.hoje()) == Uteis.getMesData(this.getDataPesquisa())){
			titulo += ", até o dia "+Uteis.getData(this.getDataPesquisa())+", você tem:";
		}else
			if(Uteis.getMesData(Calendario.hoje()) < Uteis.getMesData(this.getDataPesquisa())){
				titulo += " você terá:";
			}else
				titulo +=" você teve: ";
		return titulo;
	}

    public boolean isMostrarGruposAcompanhamento() {
        return mostrarGruposAcompanhamento;
    }

    public void setMostrarGruposAcompanhamento(boolean mostrarGruposAcompanhamento) {
        this.mostrarGruposAcompanhamento = mostrarGruposAcompanhamento;
    }

    public void toggleMostrarGruposAcompanhamento() {
        setMostrarGruposAcompanhamento(!isMostrarGruposAcompanhamento());
    }

    public boolean isMostrarCheckboxAcompanhamento() throws Exception {
        return  !getUsuarioLogado().getAdministrador() && obterColaboradorParticipanteVOLogadoAcompanhamento() != null;
    }

    public void setMarcarUsuarioAcompanhamento(Boolean marcarUsuarioAcompanhamento) {
        this.marcarUsuarioAcompanhamento = marcarUsuarioAcompanhamento;
    }

    public Boolean getMarcarUsuarioAcompanhamento() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        GrupoColaboradorParticipanteVO logado = obterColaboradorParticipanteVOLogadoAcompanhamento();
        if (logado == null) {
            return false;
        }
        return logado.getGrupoColaboradorParticipanteEscolhido();
    }

    private GrupoColaboradorParticipanteVO obterColaboradorParticipanteVOLogadoAcompanhamento() throws Exception {
        for (GrupoColaboradorVO grupoColaboradorVO : getListaGrupoColaborador()) {
            for (GrupoColaboradorParticipanteVO colaboradorParticipanteVO : grupoColaboradorVO.getGrupoColaboradorParticipanteVOs()) {
                if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(colaboradorParticipanteVO.getColaboradorParticipante().getCodigo())) {
                    return colaboradorParticipanteVO;
                }
            }
        }
        return null;
    }
}

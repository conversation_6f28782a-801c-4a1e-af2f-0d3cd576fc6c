package controle.crm;

import br.com.pactosolucoes.ce.comuns.to.BinarioTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.MarcadoresEmailEnum;
import br.com.pactosolucoes.enumeradores.TagsEmailBoasVindasEnum;
import br.com.pactosolucoes.enumeradores.TagsEmailRemessaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.crm.ModeloMensagemVO;
import negocio.comuns.plano.MarcadorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.crm.ModeloMensagem;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas
 * modeloMensagemForm.jsp modeloMensagemCons.jsp) com as funcionalidades da
 * classe <code>ModeloMensagem</code>.
 * Implemtação da camada controle (Backing Bean).
 * 
 * @see SuperControle
 * @see ModeloMensagem
 * @see ModeloMensagemVO
 */
public class ModeloMensagemControle extends SuperControle {

    private ModeloMensagemVO modeloMensagemVO;
    private String arquivoUpLoad;
    private ConfiguracaoSistemaCRMVO configCRM;
    private String msgAlert;

    /**
     * Interface <code>ModeloMensagemInterfaceFacade</code> responsável pela
     * interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia
     * de persistência dos dados (DesignPatter: Façade).
     */
    public ModeloMensagemControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        deleteDir(new File(Uteis.obterCaminhoWeb() + "/imagensCRM/email/tmp/"));
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>ModeloMensagem</code> para edição pelo usuário da aplicação.
     */
    public String novo() {
        try {
            setModeloMensagemVO(new ModeloMensagemVO(true));
            getModeloMensagemVO().setMeioDeEnvioEnum(MeioEnvio.EMAIL);
            setMensagemID("msg_entre_dados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe
     * <code>ModeloMensagem</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request)
     * para que o JSP correspondente possa disponibilizá-lo para edição.
     * @throws IOException
     */
    public String editar() throws IOException {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        return editar(codigoConsulta);
    }
    public String editar(Integer codigoConsulta) throws IOException {
        try {
            ModeloMensagemVO obj = getFacade().getModeloMensagem().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);

            if (!obj.getNomeImagem().isEmpty() || obj.getImagemModelo() != null) {
                obj.setTemImagem(true);
            } else {
                obj.setTemImagem(false);
            }
            obj.verificarSeExisteImagemModelo(false, getKey());
            obj.setNovoObj(false);
            obj.setarLogoEmpresa(getEmpresaLogado());
            setModeloMensagemVO(obj);
            setMensagemID("msg_dados_editar");
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemID("msg_erro_obterModeloMensagem");
            return "consultar";
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto
     * da classe <code>ModeloMensagem</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação
     * <code>incluir()</code>. Caso contrário é acionado o
     * <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo
     * re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public void gravar() {
        try {
            setMsgAlert("");
            if (modeloMensagemVO.getMeioDeEnvio().equals(MeioEnvio.SMS.getCodigo()) && modeloMensagemVO.getMensagem().length() > Uteis.TAMANHO_MSG_SMS){
                throw new Exception("A mensagem não pode ter mais que 140 caracteres.");
            }
            modeloMensagemVO.setMensagem(modeloMensagemVO.getMensagem().replace("src=\"../imagensCRM/", "src=\"imagensCRM/"));
            consultarTipoModeloMensagemConfirmacao();
            if (modeloMensagemVO.isNovoObj()) {
                getFacade().getModeloMensagem().incluir(modeloMensagemVO);
            } else {
                getFacade().getModeloMensagem().alterar(modeloMensagemVO);
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP
     * ModeloMensagemCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox
     * denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na
     * sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getModeloMensagem().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            } else if (getControleConsulta().getCampoConsulta().equals("titulo")) {
                objs = getFacade().getModeloMensagem().consultarPorTitulo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe
     * <code>ModeloMensagemVO</code> Após a exclusão ela automaticamente aciona
     * a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getModeloMensagem().excluir(modeloMensagemVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"modelomensagem\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"objecao\" violates foreign key")){
                setMensagemDetalhada("Este modelo de mensagem não pode ser excluído, pois já foi utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public List getListaSelectItemTipoMensagem() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        Hashtable TipoMensagem = (Hashtable) Dominios.getTipoMensagem();
        Enumeration keys = TipoMensagem.keys();
        objs.add(new SelectItem("",""));
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) TipoMensagem.get(value);
            objs.add(new SelectItem(value, label));
        }
        return objs;
    }

    public List getListaSelectItemMeioDeEnvio() throws Exception {
        List objs = new ArrayList();
        MeioEnvio[] listaMeioEnvio = MeioEnvio.values();
        for (int i = 0; i < listaMeioEnvio.length; i++) {
            if (listaMeioEnvio[i].getCodigo() != 4) {
                int codigo = listaMeioEnvio[i].getCodigo();
                String descricao = listaMeioEnvio[i].getDescricao();
                objs.add(new SelectItem(codigo, descricao));
            }
        }
        return objs;
    }

    /**
     * Metodo que obtem a lista de modelo de mensagens
     *
     * Autor: Pedro Y. Saito
     * Criado em 03/01/2011
     */
    @SuppressWarnings("unchecked")
    public List<SelectItem> getListaSelectItemModeloMensagem() {
        List<SelectItem> objs = new ArrayList<>();

        List<BinarioTO> lista = new ArrayList<>();
        if (modeloMensagemVO.getMeioDeEnvio() == 1) {
            lista = Dominios.getModeloMensagemEmail();
        } else if (modeloMensagemVO.getMeioDeEnvio() == 2) {
            lista = Dominios.getModeloMensagemSMS();
        }

        for (BinarioTO option : lista) {
            if (option != null && !"".equals(option.getCodigo())) {
                objs.add(new SelectItem(option.getCodigo().toString(), option.getDescricao().toString()));
            }
        }

        return objs;
    }

    /**
     * Obtem o modelo de mensagem selecionado
     *
     * Autor: Pedro Y. Saito
     * Criado em 04/01/2011
     */
    public void obterModelo(ActionEvent evt) throws Exception {
        this.arquivoUpLoad = null;

        this.getModeloMensagemVO().setModeloMensagem(modeloMensagemVO.getTipoModeloMensagem(), this.getEmpresaLogado(), modeloMensagemVO.getMeioDeEnvioEnum(), getKey());
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para
     * campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("titulo", "Título"));
        itens.add(new SelectItem("codigo", "Codigo"));
        return itens;
    }

    public void executarInsercaoTag() {
        try {
            MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorEmail");
            getModeloMensagemVO().setMensagem(MalaDireta.executarInsercaoTag(getModeloMensagemVO().getMensagem(), obj.getTag(), true));
        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    public void executarInsercaoTagNome() {
        executarInsercaoTag("TAG_NOME");
    }

    public void executarInsercaoTagPNome() {
        executarInsercaoTag("TAG_PNOME");
    }

    public void executarInsercaoTagBoleto() {
        executarInsercaoTag("TAG_BOLETO");
    }

    private void executarInsercaoTag(String tag) {
        try {
            if (getModeloMensagemVO().getEmail()) {
                executarInsercaoTagEmail(tag);
            } else {
                getModeloMensagemVO().setMensagem(getModeloMensagemVO().getMensagem() + tag);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void executarInsercaoTagEmail(String tag) {
        try {
            String[] mensagem = getModeloMensagemVO().getMensagem().split("</body>");
            if (mensagem.length > 1) {
                getModeloMensagemVO().setMensagem(mensagem[0].trim() + tag + "</body>" + mensagem[1]);
            } else {
                getModeloMensagemVO().setMensagem(getModeloMensagemVO().getMensagem() + tag);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void executarInsercaoTagUsuarioVendasOnline() {
        executarInsercaoTagEmail("@USUARIO_VENDAS_ONLINE@");
    }

    public void executarInsercaoTagSenhaVendasOnline() {
        executarInsercaoTagEmail("@SENHA_VENDAS_ONLINE@");
    }

    public void executarInsercaoTagLink(){
        executarInsercaoTagEmail("LINK_URL");
    }
    public void executarInsercaoTagPlano(){
        executarInsercaoTagEmail("PLANO");
    }
    public void executarInsercaoTagNrParcelas(){
        executarInsercaoTagEmail("NR_PARCELAS");
    }
    public void executarInsercaoTagNomeCliente(){
        executarInsercaoTagEmail("NOME_CLIENTE");
    }

    public void limparCampos(){
        getModeloMensagemVO().setMensagem("");
        getModeloMensagemVO().setTipoMensagem("");
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoValor</code>
     */
    public List<MarcadorVO> getListaSelectItemMarcadoEmail() throws Exception {
        List<MarcadorVO> objs = new ArrayList<MarcadorVO>();
        MarcadorVO marcador = new MarcadorVO();
        MensagemBuilderControle mensagemBuilderControl = (MensagemBuilderControle) context().getExternalContext().getSessionMap().get("MensagemBuilderControle");
        boolean adicionarLogoEmpresa = !mensagemBuilderControl.getMeioSms() && !mensagemBuilderControl.getMeioApp();
        for (MarcadoresEmailEnum mEE : MarcadoresEmailEnum.values()) {
            if (mEE.isModeloMensagem()) {
                if(mEE.equals(MarcadoresEmailEnum.LOGO_EMPRESA_TAG) && !adicionarLogoEmpresa){
                    continue;
                }
                marcador.setTag(mEE.getTag());
                marcador.setNome(mEE.getDescricao());
                objs.add(marcador);
                marcador = new MarcadorVO();
            }
        }
        return objs;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes
     * de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("");
        return "consultar";
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do
     * backing bean.
     * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é
     * automaticamente
     * quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        modeloMensagemVO = null;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            configCRM = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ModeloMensagemVO getModeloMensagemVO() {
        if (modeloMensagemVO == null) {
            modeloMensagemVO = new ModeloMensagemVO();
        }
        return modeloMensagemVO;
    }

    public void setModeloMensagemVO(ModeloMensagemVO modeloMensagemVO) {
        this.modeloMensagemVO = modeloMensagemVO;
    }



    /**
     * Metodo que e executado quando e realizado o upload de alguma imagem,
     * ele ira criar o HTML da imagem que de upload para ser exibida no
     * editor
     *
     * Autor: Pedro Y. Saito
     * Criado em 11/04/2011
     */
    public void definirModeloUpLoad(ActionEvent evt) throws Exception {
        Object arq = evt.getComponent().getAttributes().get("arquivo");
        if (arq != null && !"".equals(arq.toString())) {
            this.setArquivoUpLoad(arq.toString());
            String path = getUrl();
            String caminho = path + "/imagensCRM/email/tmp/" + this.getArquivoUpLoad();
            this.getModeloMensagemVO().criarMensagemUploadImagem(caminho, this.getArquivoUpLoad());
        }
    }

    /**
     * Metodo auxiliar para limpar a mensagem de um imagem de upload
     *
     * Autor: Pedro Y. Saito
     * Criado em 11/04/2011
     */
    public void removerImagemUpload() throws Exception {
        this.getModeloMensagemVO().setMensagem("");
        this.getModeloMensagemVO().setNomeImagem("");
        this.getModeloMensagemVO().initImagemModelo();
        this.arquivoUpLoad = null;
    }

    /**
     * Metodo que realiza o Upload do arquivo selecionado e salva a imagem
     * na pasta temporaria /imagensCRM/email/tmp/
     *
     * Autor: Pedro Y. Saito
     * Criado em 25/03/2011
     */
    public void upload(UploadEvent upload) throws Exception {
        limparMsg();

        //Obtendo a imagem que foi feita upload
        UploadItem item = upload.getUploadItem();
        //Criando um apontamento para o arquivo de upload
        File item1 = item.getFile();

        if (item1.length() > 358400) {
            setMensagemDetalhada("Tamanho máximo do imagem excedido.");
            return;
        }

        //Criando um buffer da imagem que foi feita upload
        BufferedImage outImage = ImageIO.read(item1);

        try {
            //Validando o buffer
            if (outImage != null) {
                //Obtendo o caminha da aplicacao para salvar a imagem
                String caminho = Uteis.obterCaminhoWeb() + "/imagensCRM/email/tmp/";

                String nomeImagem = item.getFileName();
                String nomeEmpresa = getEmpresaLogado().getNome();
                if(nomeEmpresa.length() > 30){
                	nomeEmpresa = nomeEmpresa.substring(0, 30);
                }
                nomeImagem = Uteis.retirarAcentuacaoRegex(nomeEmpresa + nomeImagem).replace(" ", "_").replace("@","");;
                if (nomeImagem.length() > 150) {
                    nomeImagem = nomeImagem.substring(0, 150);
                }
                if(getFacade().getModeloMensagem().validaNomeArquivo(nomeImagem)){
                	throw new ConsistirException("Já existe uma imagem com este nome cadastrada, altere o nome da imagem e tente novamente.");
                }
                //Criando um apontamento para o diretorio
                File arq = new File(caminho);
                //Verificando se o diretorio existe
                if (!arq.exists()) {
                    //Criando o diretorio
                    arq.mkdirs();
                }

                caminho = caminho.replaceAll("\\\\", "/");

                //Verificando se ja existe algum arquivo que foi feito upload
                if (this.getArquivoUpLoad() != null && !"".equals(this.getArquivoUpLoad())) {
                    File arqExcluir = new File(caminho + this.getArquivoUpLoad());
                    if (arqExcluir.exists()) {
                        arqExcluir.delete();
                    }
                }

                //Criando um array para obter a extensao do arquivo
                String[] extensao = nomeImagem.split("\\.");
                arq = new File(caminho + nomeImagem);
                if ("png".equalsIgnoreCase(extensao[extensao.length - 1])) {
                    ImageIO.write(outImage, "PNG", arq);
                } else {
                    ImageIO.write(outImage, "JPEG", arq);
                }

                //=====================================================================

                ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
                byte buffer[] = new byte[4096];
                int bytesRead = 0;
                FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
                while ((bytesRead = fi.read(buffer)) != -1) {
                    arrayOutputStream.write(buffer, 0, bytesRead);
                }

                this.getModeloMensagemVO().setImagemModelo(arrayOutputStream.toByteArray());
                this.getModeloMensagemVO().setNomeImagem(nomeImagem);
                this.setArquivoUpLoad(nomeImagem);

                arrayOutputStream.close();
                fi.close();

                //=====================================================================

                setMensagemID("msg_upload_arquivo");
                getModeloMensagemVO().setFeitoUploadAlgumaFoto(true);
            }
        } catch (ConsistirException c) {
            setMensagemDetalhada(c);
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("Erro ao fazer upload de arquivo");
        }
    }

    /**
     * @return O campo arquivoUpLoad.
     */
    public String getArquivoUpLoad() {
        return this.arquivoUpLoad;
    }

    /**
     * @param arquivoUpLoad O novo valor de arquivoUpLoad.
     */
    public void setArquivoUpLoad(String arquivoUpLoad) {
        this.arquivoUpLoad = arquivoUpLoad;
    }

    /**
     * Metodo que deleta todos os subdiretorios e arquivos
     *
     * Autor: Pedro Y. Saito
     * Criado em 25/03/2011
     */
    private void deleteDir(File dir) {
        try {
            if (dir.isDirectory()) {
                String[] children = dir.list();
                for (int i = 0; i < children.length; i++) {
                    deleteDir(new File(dir, children[i]));
                }
            }

//	        // Agora o diretório está vazio, restando apenas deletá-lo.  
//	        dir.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isApresentarTags() {
        return getModeloMensagemVO().isApresentarTags(configCRM);
    }

    public ConfiguracaoSistemaCRMVO getConfigCRM() {
        return configCRM;
    }

    public void setConfigCRM(ConfiguracaoSistemaCRMVO configCRM) {
        this.configCRM = configCRM;
    }

    public void consultarTipoModeloMensagemConfirmacao()throws Exception{

            limparMsg();

            if(getModeloMensagemVO().getTipoMensagem().equals("EC")) {
                ModeloMensagemVO validacao = getFacade().getModeloMensagem().consultarPorTipo("EC", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (validacao != null && !validacao.getCodigo().equals(getModeloMensagemVO().getCodigo())) {
                    throw new Exception("Já existe um modelo de mensagem cadastrado do tipo \'Confirmação de compra por email\' e não é possível cadastrar mais de um, portanto, acesse-o e altere-o conforme desejado.");
                }
            }

    }

    public List<MarcadorVO> getListaTagsEmailRemessa() {
        return TagsEmailRemessaEnum.getListaTagsEmailRemessa();
    }

    public List<MarcadorVO> getListaTagsBoasVindas() {
        return TagsEmailBoasVindasEnum.getListaTags();
    }

    public void inserirTagRemessa() {
        try {
            MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("tagRemessa");
            getModeloMensagemVO().setMensagem(MalaDireta.executarInsercaoTag(getModeloMensagemVO().getMensagem(), obj.getTag(), true));
        } catch (Exception ignored) {
        }
    }

    public void incluirTagPesquisa() {
        executarInsercaoTag("TAG_PESQUISA");
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Modelo de Mensagem",
                "Deseja excluir o Modelo de Mensagem?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

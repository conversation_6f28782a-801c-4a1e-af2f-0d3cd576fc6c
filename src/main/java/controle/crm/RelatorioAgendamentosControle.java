/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.crm;

import br.com.pactosolucoes.bi.dto.DadosAulaExperimentalDTO;
import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendaEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.basico.AgendaProfessor;
import controle.basico.ClienteControle;

import java.util.*;
import javax.faces.model.SelectItem;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.RelatorioAgendamentoTO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import relatorio.controle.basico.BIControle;
import relatorio.controle.sad.RelControleOperacoesControle;
import relatorio.negocio.comuns.basico.ControleOperacoesRelVO;
import relatorio.negocio.comuns.basico.PendenciaResumoPessoaRelVO;

/**
 * <AUTHOR>
 */
public class RelatorioAgendamentosControle extends BIControle {

    private Date inicio;
    private Date fim;
    private int nrDiasContarResultado;
    private Date iniciolancamento;
    private Date fimlancamento;
    private String tipo = null;
    private RelatorioAgendamentoTO relatorioAgendamentoTO = new RelatorioAgendamentoTO();
    private RelatorioAgendamentoTO relatorioBI = new RelatorioAgendamentoTO();
    private Integer codigoEmpresa = null;
    private Integer codigoModalidade = null;
    private List<SelectItem> tipos = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemModalidades;
    private Boolean somenteConvertidos = false;
    private Boolean somenteExecutadas = false;
    private Boolean aconteceram = false;
    private Boolean acontecer = false;
    private UsuarioVO responsavel = new UsuarioVO();
    private ColaboradorVO colaborador = new ColaboradorVO();
    private Boolean gymPass = false;
    private DadosAulaExperimentalDTO dto = new DadosAulaExperimentalDTO();

    private ItemExportacaoEnum itemExportacao;

    public RelatorioAgendamentosControle() {

    }

    public void novo() {
        try {
            codigoEmpresa = getUsuarioLogado().getAdministrador() ? null : getEmpresaLogado().getCodigo();
            inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            fim = Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje());
            nrDiasContarResultado = getFacade().getConfiguracaoSistemaCRM().obterNrDiasContarResultado();
            consultarAgendamentos();
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    private FiltroDTO getFiltroDTO(Boolean listas){
        FiltroDTO filtroDTO = new FiltroDTO();
        filtroDTO.setNome(BIEnum.AULA_EXPERIMENTAL.name());
        JSONObject filtros = new JSONObject();
        filtros.put("inicio", inicio.getTime());
        filtros.put("fim", fim.getTime());
        filtros.put("responsavel", responsavel.getCodigo());
        filtros.put("empresa", codigoEmpresa);
        filtros.put("nrDiasContarResultado", nrDiasContarResultado);
        filtros.put("listas", listas);
        filtroDTO.setFiltros(filtros.toString());
        return filtroDTO;
    }

    public void novoBITela() {
        gravarHistoricoAcessoBI(BIEnum.AULA_EXPERIMENTAL);
        novoBI(true);
    }

    public void novoBI() {
        novoBI(false);
    }
    public void novoBI(boolean atualizar) {
        try {
            codigoEmpresa =  getEmpresaFiltro().getCodigo();
            inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(getDataBaseFiltroBI()));
            fim = Uteis.obterUltimoDiaMesUltimaHora(getDataBaseFiltroBI());
            nrDiasContarResultado = getFacade().getConfiguracaoSistemaCRM().obterNrDiasContarResultado();
            FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.AULA_EXPERIMENTAL, getFiltroDTO(false), atualizar);
            dto = new DadosAulaExperimentalDTO(new JSONObject(filtroDTO.getJsonDados()));
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public void consultarAgendamentos() {
        relatorioAgendamentoTO = new RelatorioAgendamentoTO();
        setMsgAlert("");
        limparMsg();
        if(fimlancamento == null&& iniciolancamento == null && inicio == null && fim==null && !somenteConvertidos &&
                !somenteExecutadas && !aconteceram &&  !acontecer && !gymPass && UteisValidacao.emptyString(tipo) &&
                 colaborador.getCodigo() == 0 && UteisValidacao.emptyString(responsavel.getNome())) {
            montarAviso("Informe um campo para a consulta");
            setMsgAlert(getMensagemNotificar());
        } else {
            try {
                limparMsg();
                relatorioAgendamentoTO = getFacade().getAgenda().consultarAgendamentos(inicio, fim, iniciolancamento, fimlancamento, tipo, responsavel, (UteisValidacao.emptyNumber(codigoEmpresa) ? getEmpresaLogado().getCodigo() : codigoEmpresa),
                        getFacade().getConfiguracaoSistemaCRM().obterNrDiasContarResultado(), codigoModalidade,
                        somenteConvertidos, somenteExecutadas, aconteceram, acontecer, gymPass, colaborador, null);
            } catch (Exception e) {
                setMensagemID("msg_erro");
                setMensagemDetalhada(e);
                montarErro(getMensagemDetalhada());
                setMsgAlert(getMensagemNotificar());
            }

        }
    }

    public List<SelectItem> getTipos() {
        if (tipos == null || tipos.isEmpty()) {
            tipos = new ArrayList<SelectItem>();
            tipos.add(new SelectItem("", ""));
            tipos.add(new SelectItem(TipoAgendaEnum.AULA_EXPERIMENTAL.getId(), TipoAgendaEnum.AULA_EXPERIMENTAL.getDescricao()));
            tipos.add(new SelectItem(TipoAgendaEnum.LIGACAO.getId(), TipoAgendaEnum.LIGACAO.getDescricao()));
            tipos.add(new SelectItem(TipoAgendaEnum.VISITA.getId(), TipoAgendaEnum.VISITA.getDescricao()));
            tipos.add(new SelectItem(TipoAgendaEnum.AULA_DIARIA.getId(), TipoAgendaEnum.AULA_DIARIA.getDescricao()));
        }
        return tipos;
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public void limparPeriodo() {
        inicio = null;
        fim = null;
    }


    public void limparPeriodolancamento() {
        iniciolancamento = null;
        fimlancamento = null;
    }


    public void validarDataLancamento() throws Exception {
        setMsgAlert("");
            if (iniciolancamento != null && fimlancamento != null && Calendario.maior(iniciolancamento, fimlancamento)) {
                    montarAviso("Periodo de Lancamento data Fim menor que Data início");
                    setMsgAlert(getMensagemNotificar());
                    limparPeriodolancamento();
            }
            else{
                if(iniciolancamento != null && fimlancamento != null && Uteis.getMesesEntreDatas(iniciolancamento,fimlancamento).size()>6){
                    montarAviso("O intervalo de meses deve ser menor igual a 6");
                    setMsgAlert(getMensagemNotificar());
                    limparPeriodolancamento();

                }
            }
    }



    public void validarDataAgendamento() throws Exception {
        setMsgAlert("");
            if (inicio != null && fim != null && Calendario.maior(inicio, fim)) {
                    montarAviso("Periodo de Agendamento data Fim menor que Data início");
                    setMsgAlert(getMensagemNotificar());
                    limparPeriodo();
            }
            else{
                if(inicio != null && fim != null && Uteis.getMesesEntreDatas(inicio,fim).size()>6){
                    montarAviso("O intervalo de meses deve ser menor igual a 6");
                    setMsgAlert(getMensagemNotificar());
                    limparPeriodo();
                }
            }
    }


    public List<SelectItem> getListaSelectItemModalidades() throws Exception {
        if (listaSelectItemModalidades == null) {
            montarListaSelectItemModalidade();
        }
        return listaSelectItemModalidades;
    }

    public void setListaSelectItemModalidades(List<SelectItem> listaSelectItemModalidades) {
        this.listaSelectItemModalidades = listaSelectItemModalidades;
    }

    public void montarListaSelectItemModalidade() throws Exception {
        this.setListaSelectItemModalidades(new ArrayList<SelectItem>());
        List<ModalidadeVO> modalidades = getFacade().getModalidade().consultarPorNome("%", codigoEmpresa == null ? 0 : codigoEmpresa,
                true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        this.getListaSelectItemModalidades().add(new SelectItem(null, ""));
        for (ModalidadeVO mod : modalidades) {
            this.getListaSelectItemModalidades().add(new SelectItem(mod.getCodigo(), mod.getNome()));
        }
    }


    public void irParaTelaCliente() throws Exception {
        setMsgAlert("");
        AgendaVO amostra = (AgendaVO) context().getExternalContext().getRequestMap().get("agendamento");
        if (UteisValidacao.emptyString(amostra.getCliente().getMatricula())) {
            return;
        }
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                JSFUtilities.storeOnSession(ClienteControle.class.getSimpleName(), clienteControle);
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorMatricula(amostra.getCliente().getMatricula(), false, Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            if (UteisValidacao.emptyNumber(clienteControle.getClienteVO().getCodigo())) {
                montarMsgAlert("Cliente não encontrado.");
            } else {
                setMsgAlert("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1000, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<UsuarioVO> executarAutocompleteConsultaUsuario(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            result = (ArrayList<UsuarioVO>) getFacade().getUsuario().
                    consultarPorNomeUsuarioComLimite(pref, codigoEmpresa, false, Uteis.NIVELMONTARDADOS_ROBO);

            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarUsuarioSuggestionBox() throws Exception {
        UsuarioVO usuarioVO = (UsuarioVO) request().getAttribute("result");
        if (usuarioVO != null) {
            setResponsavel(usuarioVO);
        }
    }

    public List<ColaboradorVO> executarAutocompleteColaboradores(Object nome) {
        List<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
        try {
            colaboradores.addAll(getFacade().getColaborador().consultarPorNomePessoa((String) nome, codigoEmpresa, Uteis.NIVELMONTARDADOS_MINIMOS));
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return colaboradores;
    }

    public void selecionarColaborador() throws Exception {
        ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("colaborador");
        if (colaboradorVO == null) {
            this.colaborador = new ColaboradorVO();
        } else {
            this.colaborador = colaboradorVO;
        }
    }

    public boolean getMostrarVisitas() {
        return UteisValidacao.emptyString(tipo) || tipo.equals(TipoAgendaEnum.VISITA.getId());
    }

    public boolean getMostrarDiarias() {
        return UteisValidacao.emptyString(tipo) || tipo.equals(TipoAgendaEnum.AULA_DIARIA.getId());
    }

    public boolean getMostrarLigacoes() {
        return UteisValidacao.emptyString(tipo) || tipo.equals(TipoAgendaEnum.LIGACAO.getId());
    }

    public boolean getMostrarAulasExperimentais() {
        return UteisValidacao.emptyString(tipo) || tipo.equals(TipoAgendaEnum.AULA_EXPERIMENTAL.getId());
    }

    public void abrirAulasExperimentaisAcontecer() {
        somenteExecutadas = false;
        somenteConvertidos = false;
        acontecer = true;
        aconteceram = false;
        tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
        consultarAgendamentos();
    }

    public void abrirAulasExperimentais() {
        somenteExecutadas = false;
        somenteConvertidos = false;
        acontecer = false;
        aconteceram = false;
        gymPass = false;
        tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
        consultarAgendamentos();
    }

    public void abrirAulasExperimentaisAconteceram() {
        somenteExecutadas = false;
        somenteConvertidos = false;
        acontecer = false;
        aconteceram = true;
        tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
        consultarAgendamentos();
    }

    public void abrirAulasExperimentaisExecutadas() {
        tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
        somenteExecutadas = true;
        somenteConvertidos = false;
        acontecer = false;
        aconteceram = false;
        consultarAgendamentos();
    }

    public void abrirAulasExperimentaisConvertidas() {
        tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
        somenteExecutadas = false;
        somenteConvertidos = true;
        acontecer = false;
        aconteceram = false;
        consultarAgendamentos();
    }

    public String getFiltros() throws Exception {
        String filtros = "";
        if (inicio != null) {
            filtros += " | Início: " + Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy");
        }
        if (inicio != null) {
            filtros += " | Fim: " + Uteis.getDataAplicandoFormatacao(fim, "dd/MM/yyyy");
        }
        if (!UteisValidacao.emptyString(tipo) && TipoAgendaEnum.getFromId(tipo) != null) {
            filtros += " | Tipo: " + TipoAgendaEnum.getFromId(tipo).getDescricao();
        }
        if (!UteisValidacao.emptyString(responsavel.getNome())) {
            filtros += " | Resp. Agendamento: " + responsavel.getNome();
        }
        if (somenteExecutadas) {
            filtros += " | Somente executados ";
        }
        if (somenteConvertidos) {
            filtros += " | Somente convertidos ";
        }

        return filtros.replaceFirst("[|]", "");
    }

    public Boolean getSomenteExecutadas() {
        return somenteExecutadas;
    }

    public void setSomenteExecutadas(Boolean somenteExecutadas) {
        this.somenteExecutadas = somenteExecutadas;
    }

    public RelatorioAgendamentoTO getRelatorioBI() {
        return relatorioBI;
    }

    public void setRelatorioBI(RelatorioAgendamentoTO relatorioBI) {
        this.relatorioBI = relatorioBI;
    }

    public Boolean getAconteceram() {
        return aconteceram;
    }

    public void setAconteceram(Boolean aconteceram) {
        this.aconteceram = aconteceram;
    }

    public Boolean getAcontecer() {
        return acontecer;
    }

    public void setAcontecer(Boolean acontecer) {
        this.acontecer = acontecer;
    }

    public boolean isGymPass() {
        return gymPass;
    }

    public void setGymPass(boolean gymPass) {
        this.gymPass = gymPass;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public int getNrDiasContarResultado() {
        return nrDiasContarResultado;
    }

    public void setNrDiasContarResultado(int nrDiasContarResultado) {
        this.nrDiasContarResultado = nrDiasContarResultado;
    }

    public void setIniciolancamento(Date iniciolancamento) {
        this.iniciolancamento = iniciolancamento;
    }

    public void setFimlancamento(Date fimlancamento) {
        this.fimlancamento = fimlancamento;
    }

    public Date getIniciolancamento() {
        return iniciolancamento;
    }

    public Date getFimlancamento() {
        return fimlancamento;
    }





    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public void setSomenteConvertidos(Boolean somenteConvertidos) {
        this.somenteConvertidos = somenteConvertidos;
    }

    public Boolean getSomenteConvertidos() {
        return somenteConvertidos;
    }

    public RelatorioAgendamentoTO getRelatorioAgendamentoTO() {
        return relatorioAgendamentoTO;
    }

    public void setRelatorioAgendamentoTO(RelatorioAgendamentoTO relatorioAgendamentoTO) {
        this.relatorioAgendamentoTO = relatorioAgendamentoTO;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public void selecionarAlunosComAgendamento() {
        try {
            tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
            setItemExportacao(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_AGENDADOS);
            RelatorioAgendamentoTO dados = clientes("alunosAgendados");
            listarAlunos(dados.getClientesComAgendamento());
        }catch (Exception e){
            setMensagemDetalhada(e);
        }
    }

    public void selecionarProfessores() {
        try {
            tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
            setItemExportacao(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_ICV);
            RelatorioAgendamentoTO dados = clientes("alunosAgendados");
            listarProfessores(dados.getLista());
        }catch (Exception e){
            setMensagemDetalhada(e);
        }

    }

    public void selecionarAlunosQueExecutaramAgendamento() {
        try {
            tipo = TipoAgendaEnum.AULA_EXPERIMENTAL.getId();
            setItemExportacao(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_EXECUTADOS);
            RelatorioAgendamentoTO dados = clientes("executaram");
            listarAlunos(dados.getClientesQueExecutaramAgendamento());
        }catch (Exception e){
            setMensagemDetalhada(e);
        }
    }

    private RelatorioAgendamentoTO clientes(String nomeLista) throws Exception{
        FiltroDTO filtroDTO = getFacade().getBiMSService().obterBI(getKey(), BIEnum.AULA_EXPERIMENTAL, getFiltroDTO(true), false);
        RelatorioAgendamentoTO dados = getFacade().getAgenda().consultarAgendamentos(inicio, fim, iniciolancamento,
                fimlancamento, tipo, responsavel, codigoEmpresa, nrDiasContarResultado, codigoModalidade,
                somenteConvertidos, somenteExecutadas, aconteceram, acontecer, gymPass, colaborador, Uteis.arrayJsonToList(
                        new JSONObject(filtroDTO.getJsonDados()).getJSONArray(nomeLista)));
        return dados;
    }

    public void selecionarAlunosQueConvertidos() {
        try {
            setItemExportacao(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_CONVERTIDOS);
            RelatorioAgendamentoTO dados = clientes("convertidos");
            listarAlunos(dados.getClientesConvertidos());
        }catch (Exception e){
            setMensagemDetalhada(e);
        }
    }

    private void listarAlunos(List<ClienteVO> alunos) {
        try {

            List<PendenciaResumoPessoaRelVO> lista = new ArrayList<PendenciaResumoPessoaRelVO>();
            if (alunos != null && !alunos.isEmpty()) {
                StringBuilder codigoClientesComAgendamento = new StringBuilder();
                Map<Integer, String> professores = new HashMap<>();
                for (ClienteVO cliente : alunos) {
                    professores.put(cliente.getCodigo(), cliente.getProfessor());
                    codigoClientesComAgendamento.append("," + cliente.getCodigo());
                }
                String condicao = "cli.codigo IN (" + codigoClientesComAgendamento.toString().replaceFirst(",", "") + ")";
                List<ClienteVO> clientes = getFacade().getCliente().consultarProfessor(condicao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (ClienteVO cliente : clientes) {
                    PendenciaResumoPessoaRelVO pendenciaResumoPessoaRelVO = new PendenciaResumoPessoaRelVO();
                    cliente.setProfessor(professores.get(cliente.getCodigo()));
                    pendenciaResumoPessoaRelVO.setClienteVO(cliente);
                    lista.add(pendenciaResumoPessoaRelVO);
                }
            }

            ControleOperacoesRelVO controleOperacoesRelVO = new ControleOperacoesRelVO();
            controleOperacoesRelVO.setListaPendenciaResumoPessoaRelVOs(lista);
            if(itemExportacao != null) {
                controleOperacoesRelVO.setApresentarAlunosComAgendamento(itemExportacao.equals(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_AGENDADOS));
                controleOperacoesRelVO.setApresentarAlunosAgendamentoConvertido(itemExportacao.equals(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_CONVERTIDOS));
                controleOperacoesRelVO.setApresentarAlunosAgendamentoExecutado(itemExportacao.equals(ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_EXECUTADOS));
            }
            getControlador(RelControleOperacoesControle.class).setControleOperacoesRelVO(controleOperacoesRelVO);
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private String calculoProfessoresIcv(int visitante, int convertido){
        Double indice = 0.0;

        if (convertido > 0) {
            indice = Double.valueOf(100 / ((visitante + convertido) / Double.valueOf(convertido)));
        }

        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(indice);
    }

    private void listarProfessores(List<AgendaVO> listaAgenda) {
        try {
            if (listaAgenda == null || listaAgenda.isEmpty()) {
                return;
            }

            List<AgendaProfessor> listaAgendaProfessor = new ArrayList<>();

            for (AgendaVO agenda : listaAgenda) {
                if (UteisValidacao.emptyNumber(agenda.getCodigoProfessor())) {
                    continue;
                }

                boolean adicionar = true;
                for (AgendaProfessor agendaLst : listaAgendaProfessor) {
                    if (agendaLst.getCodigo().equals(agenda.getCodigoProfessor())) {
                        if (agenda.isConvertido()) {
                            agendaLst.setConvertido(agendaLst.getConvertido() + 1);
                        } else {
                            agendaLst.setVisitante(agendaLst.getVisitante() + 1);
                        }
                        agendaLst.setIcv(calculoProfessoresIcv(agendaLst.getVisitante(), agendaLst.getConvertido()));
                        adicionar = false;
                        break;
                    }
                }

                if (adicionar) {
                    AgendaProfessor agendaProfessor = new AgendaProfessor();
                    agendaProfessor.setCodigo(agenda.getCodigoProfessor());
                    agendaProfessor.setTipo(agenda.getTipoProfessor());
                    agendaProfessor.setNome((agenda.getProfessorCrm() == null ? agenda.getNomeProfessor() : agenda.getProfessorCrm()));
                    if (agenda.isConvertido()) {
                        agendaProfessor.setConvertido(agendaProfessor.getConvertido() + 1);
                    } else {
                        agendaProfessor.setVisitante(agendaProfessor.getVisitante() + 1);
                    }
                    agendaProfessor.setIcv(calculoProfessoresIcv(agendaProfessor.getVisitante(), agendaProfessor.getConvertido()));
                    listaAgendaProfessor.add(agendaProfessor);
                }

            }

            ControleOperacoesRelVO controleOperacoesRelVO = new ControleOperacoesRelVO();
            controleOperacoesRelVO.setListaAgendaProfessorRel(listaAgendaProfessor);
            controleOperacoesRelVO.setApresentarQtdProfessores(true);
            getControlador(RelControleOperacoesControle.class).setControleOperacoesRelVO(controleOperacoesRelVO);
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public DadosAulaExperimentalDTO getDto() {
        return dto;
    }

    public void setDto(DadosAulaExperimentalDTO dto) {
        this.dto = dto;
    }

    public ItemExportacaoEnum getItemExportacao() {
        return itemExportacao;
    }

    public void setItemExportacao(ItemExportacaoEnum itemExportacao) {
        this.itemExportacao = itemExportacao;
    }

}

package controle.crm;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.vendasonline.GestaoVendasOnlineControle;
import negocio.comuns.crm.QuarentenaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class QuarentenaControle extends SuperControleRelatorio {

    private static final String MSG_EXCEPTION = "Já existe uma paralisação nesse período. Motivo: %s";
    private QuarentenaVO quarentenaVO;

    public QuarentenaControle() {
        inicializar();
    }

    private void inicializar() {
        try {
            montarQuarentena();
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            Uteis.logar(e, GestaoVendasOnlineControle.class);
        }
    }

    private void montarQuarentena() throws Exception {
        setQuarentenaVO(getFacade().getQuarentena().obterAtiva(getEmpresaLogado().getCodigo()));
        if (getQuarentenaVO().getCodigo() == null) {
            setQuarentenaVO(new QuarentenaVO());
            getQuarentenaVO().setEmpresa(getEmpresaLogado());
            getQuarentenaVO().setUsuarioIniciou(getUsuarioLogado());
            getQuarentenaVO().setMotivo("");
        }
    }

    public boolean getApresentarIniciarEncerrar() {
        return quarentenaVO.getCodigo() != null && quarentenaVO.isAtiva();
    }

    public void iniciarParalisacao() {
        try {
            limparMsg();
            setMsgAlert("");
            if (quarentenaVO.getMotivo() == null || quarentenaVO.getMotivo().isEmpty()) {
                throw new ConsistirException("Descreva um motivo.");
            }
            if (quarentenaVO.getCodigo() != null) {
                montarSucessoGrowl("Paralisação já iniciada.");
            } else {
                getFacade().getQuarentena().iniciar(getQuarentenaVO());
                montarQuarentena();
                notificarRecursoEmpresa(RecursoSistema.INICIOU_PARALISACAO);
                montarSucessoGrowl("Paralisação iniciada com sucesso.");
            }
        } catch (Exception e) {
            montarErro(e);
            Uteis.logar(e.getMessage(), QuarentenaControle.class);
        }
    }

    public void encerrarParalisacao() {
        try {
            limparMsg();
            setMsgAlert("");
            if (quarentenaVO.getCodigo() != null) {
                quarentenaVO.setUsuarioEncerrou(getUsuarioLogado());
                getFacade().getQuarentena().encerrar(quarentenaVO);
                montarQuarentena();
                notificarRecursoEmpresa(RecursoSistema.FINALIZOU_PARALISACAO);
                montarSucessoGrowl("Paralisação encerrada com sucesso.");
            } else {
                montarSucessoGrowl("Já encerramos a paralisação.");
            }
        } catch (Exception e) {
            montarErro(e);
            Uteis.logar(e.getMessage(), CarteirasControle.class);
        }
    }

    public void paralisacaoRetroativa() {
        try {
            limparMsg();
            setMsgAlert("");
            validarDados();
            getQuarentenaVO().setEmpresa(getEmpresaLogado());
            getQuarentenaVO().setUsuarioIniciou(getUsuarioLogado());
            verificarParalisacaoPorPeriodo();
            getFacade().getQuarentena().iniciarRetroativa(getQuarentenaVO());
            getFacade().getQuarentena().alterarDadosGeradosNaParalisacao(getQuarentenaVO().getInicioQuarentena(), getQuarentenaVO().getFimQuarentena());
            setQuarentenaVO(new QuarentenaVO());
            notificarRecursoEmpresa(RecursoSistema.PARALISACAO_RETROATIVA);
            montarSucessoGrowl("Paralisação lançada com sucesso.");
        } catch (Exception e) {
            Uteis.logar(e.getMessage(), CarteirasControle.class);
        }
    }

    public void validarDados() throws Exception {
        try {
            if (getQuarentenaVO().getInicioQuarentena() == null) {
                throw new ConsistirException("Selecione a data inicial");
            }
            if (getQuarentenaVO().getFimQuarentena() == null) {
                throw new ConsistirException("Selecione a data final");
            }
            SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
            Date dia16 = formato.parse("16/03/2020");
            if (getQuarentenaVO().getInicioQuarentena().before(dia16)) {
                throw new ConsistirException("A data de inicio não pode ser antes de 16 de março de 2020");
            }
            if (getQuarentenaVO().getInicioQuarentena().after(getHoje())) {
                throw new ConsistirException("A data de inicio não pode ser maior que a data atual");
            }
            if (getQuarentenaVO().getFimQuarentena().after(getHoje())) {
                throw new ConsistirException("A data final da paralisação deve ser igual ou anterior à data atual pois paralisações retroativas não podem incluir datas futuras.");
            }
            if (getQuarentenaVO().getInicioQuarentena().after(getQuarentenaVO().getFimQuarentena())) {
                throw new ConsistirException("A data final não pode ser antes da data inicial");
            }
            if (UteisValidacao.emptyString(getQuarentenaVO().getMotivo())) {
                throw new ConsistirException("Descreva um motivo");
            }
        } catch (Exception e) {
            montarErro(e);
            Uteis.logar(e.getMessage(), QuarentenaControle.class);
            throw new Exception(e);
        }
    }

    public void verificarParalisacaoPorPeriodo() throws Exception {
        try {
            List<QuarentenaVO> quarentenaVOS = getFacade().getQuarentena().obterTodas(getEmpresaLogado().getCodigo());
            for (QuarentenaVO quarentena : quarentenaVOS) {
                if (getQuarentenaVO().getFimQuarentena().compareTo(quarentena.getFimQuarentena()) == 0) {
                    throw new ConsistirException(String.format(MSG_EXCEPTION, quarentena.getMotivo()));
                }
                if (getQuarentenaVO().getInicioQuarentena().compareTo(quarentena.getInicioQuarentena()) == 0) {
                    throw new ConsistirException(String.format(MSG_EXCEPTION, quarentena.getMotivo()));
                }
                if (getQuarentenaVO().getInicioQuarentena().after(quarentena.getInicioQuarentena()) && getQuarentenaVO().getInicioQuarentena().before(quarentena.getFimQuarentena())
                        || getQuarentenaVO().getFimQuarentena().after(quarentena.getInicioQuarentena()) && getQuarentenaVO().getFimQuarentena().before(quarentena.getFimQuarentena())) {
                    throw new ConsistirException(String.format(MSG_EXCEPTION, quarentena.getMotivo()));
                }
                if (getQuarentenaVO().getInicioQuarentena().before(quarentena.getInicioQuarentena()) && getQuarentenaVO().getFimQuarentena().after(quarentena.getFimQuarentena())) {
                    throw new ConsistirException(String.format(MSG_EXCEPTION, quarentena.getMotivo()));
                }
            }
        } catch (Exception e) {
            montarErro(e);
            Uteis.logar(e.getMessage(), QuarentenaControle.class);
            throw new Exception(e);
        }
    }

    public String getAlturaPadraoModalEncerrar() {
        if (getApresentarIniciarEncerrar()) {
            return "475";
        }
        return "334";
    }

    public QuarentenaVO getQuarentenaVO() {
        return quarentenaVO;
    }

    public void setQuarentenaVO(QuarentenaVO quarentenaVO) {
        this.quarentenaVO = quarentenaVO;
    }
}

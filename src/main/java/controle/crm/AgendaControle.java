package controle.crm;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

import javax.faces.model.SelectItem;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MalaDiretaEnviadaVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.crm.Agenda;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas agendaForm.jsp agendaCons.jsp) com as funcionalidades da classe
 * <code>Agenda</code>. Implemtação da camada controle (Backing Bean).
 * 
 * @see SuperControle
 * @see Agenda
 * @see AgendaVO
 */
public class AgendaControle extends SuperControle {

	private AgendaVO agendaVO;
	private ClienteVO clienteVO;
	private MalaDiretaVO malaDiretaVO;
	private MalaDiretaEnviadaVO malaDiretaEnviadaVO;
	private HistoricoContatoVO historicoContatoVO;
	private Date dataConsulta;
	private String consultaSituacao;
	private Integer consultaCategoria;	
	private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
	private String valorConsultarNomeComparecimento;
	private Date dataInicio;
	private Date dataTermino;
	private Boolean apresentarFiltroPesquisaHistorico;

	/**
	 * Interface <code>AgendaInterfaceFacade</code> responsável pela
	 * interconexão da camada de controle com a camada de negócio. Criando uma
	 * independência da camada de controle com relação a tenologia de
	 * persistência dos dados (DesignPatter: Façade).
	 */
	public AgendaControle() throws Exception {
		obterUsuarioLogado();
		inicializarFacades();
		setControleConsulta(new ControleConsulta());
		
		setMensagemID("msg_entre_prmconsulta");
	}

	/**
	 * Rotina responsável por disponibilizar um novo objeto da classe
	 * <code>Agenda</code> para edição pelo usuário da aplicação.
	 */
	public void novo() {
		setListaConsulta(new ArrayList());	
		setDataConsulta(negocio.comuns.utilitarias.Calendario.hoje());
		setHistoricoContatoVO(new HistoricoContatoVO());
		setMalaDiretaVO(new MalaDiretaVO());
		setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
		setAgendaVO(new AgendaVO());
		consultarAgendadosHojeConfirmacaoComparecimentoHoje();
		notificarRecursoEmpresa(RecursoSistema.MARCAR_COMPARECIMENTO);
		
	}

	

	/**
	 * Rotina responsável por disponibilizar os dados de um objeto da classe
	 * <code>Agenda</code> para alteração. O objeto desta classe é
	 * disponibilizado na session da página (request) para que o JSP
	 * correspondente possa disponibilizá-lo para edição.
	 */
	public String editar() {
		AgendaVO obj = (AgendaVO) context().getExternalContext().getRequestMap().get("agenda");
		inicializarAtributosRelacionados(obj);
		obj.setNovoObj(new Boolean(false));
		setAgendaVO(obj);
		setMensagemID("msg_dados_editar");
		setMsgAlert("");
		return "editar";
	}
	

	/**
	 * Método responsável inicializar objetos relacionados a classe
	 * <code>AgendaVO</code>. Esta inicialização é necessária por exigência da
	 * tecnologia JSF, que não trabalha com valores nulos para estes atributos.
	 */
	public void inicializarAtributosRelacionados(AgendaVO obj) {
		if (obj.getPassivo() == null) {
			obj.setPassivo(new PassivoVO());
		}
		if (obj.getIndicado() == null) {
			obj.setIndicado(new IndicadoVO());
		}
		if (obj.getColaboradorResponsavel() == null) {
			obj.setColaboradorResponsavel(new UsuarioVO());
		}
		if (obj.getCliente() == null) {
			obj.setCliente(new ClienteVO());
		}
		if (obj.getResponsavelCadastro() == null) {
			obj.setResponsavelCadastro(new UsuarioVO());
		}
	}

	/**
	 * Rotina responsável por gravar no BD os dados editados de um novo objeto
	 * da classe <code>Agenda</code>. Caso o objeto seja novo (ainda não gravado
	 * no BD) é acionado a operação <code>incluir()</code>. Caso contrário é
	 * acionado o <code>alterar()</code>. Se houver alguma inconsistência o
	 * objeto não é gravado, sendo re-apresentado para o usuário juntamente com
	 * uma mensagem de erro.
	 */
	public void gravar() {
		try {
			if(getEmpresaLogado() != null){
				agendaVO.setEmpresa(getEmpresaLogado().getCodigo());
			}
			if (agendaVO.isNovoObj().booleanValue()) {
				getFacade().getAgenda().incluir(agendaVO);
			} else {
				getFacade().getAgenda().alterar(agendaVO);
			}
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}
	
	public void gravarAgendadosComparecidosVindaTelaMetaAgendadosDetalhadosForm(){		
			try {				
				FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
				if(!UteisValidacao.emptyNumber(obj.getAgenda().getModalidade().getCodigo())){
					obj.getAgenda().setModalidade(getFacade().getModalidade().consultarPorChavePrimaria(
							obj.getAgenda().getModalidade().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));	
				}
				getFacade().getAgenda().alterarAgendadosComparecido(obj.getAgenda(), getUsuarioLogado(), true);
				setMensagemID("msg_dados_gravados");
				montarMsgAlert(getMensagem());
				setSucesso(true);
				setErro(false);
			} catch (Exception e) {
				setSucesso(false);
				setErro(true);
				setMensagemDetalhada("msg_erro", e.getMessage());
				montarMsgAlert(getMensagemDetalhada());
			}
	}
	
	public void cancelarAgendadosComparecidosVindaTelaMetaAgendadosDetalhadosForm() {
		try {
			FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");			
			getFacade().getAgenda().cancelarAgendadosComparecido(obj.getAgenda());
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}

	public void gravarAgendadosComparecidos() {
		try {
			AgendaVO obj = (AgendaVO) context().getExternalContext().getRequestMap().get("agendados");
			getFacade().getAgenda().alterarAgendadosComparecido(obj, getUsuarioLogado(), true);
			getFacade().getAgenda().executarSomaTotalizador(agendaVO);
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}

	public void cancelarAgendadosComparecidos() {
		try {
			AgendaVO obj = (AgendaVO) context().getExternalContext().getRequestMap().get("agendados");
			getFacade().getAgenda().cancelarAgendadosComparecido(obj);
			getFacade().getAgenda().executarSubtracaoTotalizador(agendaVO);
			setMensagemID("msg_dados_gravados");
			montarMsgAlert(getMensagem());
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			montarMsgAlert(getMensagemDetalhada());
		}
	}

	

	

	/**
	 * Operação responsável por processar a exclusão um objeto da classe
	 * <code>AgendaVO</code> Após a exclusão ela automaticamente aciona a rotina
	 * para uma nova inclusão.
	 */
	public String excluir() {
		try {
			getFacade().getAgenda().excluir(agendaVO);
			novo();
			setMensagemID("msg_dados_excluidos");
			setSucesso(true);
			setErro(false);
			return "editar";
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "editar";
		}
	}

	

	public List getTipoConsultaComboObjecao() throws Exception {
		List itens = new ArrayList();
		itens.add(new SelectItem("descricao", "Descrição"));
		itens.add(new SelectItem("codigo", "Código"));
		itens.add(new SelectItem("grupo", "Grupo"));
		return itens;
	}

	/*
	 * Método responsável por inicializar List<SelectItem> de valores do
	 * ComboBox correspondente ao atributo <code>tipoAgendamento</code>
	 */
	public List getListaSelectItemTipoAgendamentoAgenda() throws Exception {
		List objs = new ArrayList();
		Hashtable tipoAgendamentos = (Hashtable) Dominios.getTipoAgendamento();
		Enumeration keys = tipoAgendamentos.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) tipoAgendamentos.get(value);
			objs.add(new SelectItem(value, label));
		}
		SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
		Collections.sort((List) objs, ordenador);
		return objs;
	}

	/*
	 * Método responsável por inicializar List<SelectItem> de valores do
	 * ComboBox correspondente ao atributo Horas Esse método cria uma lista de
	 * hora em hora para o usuario selecionar a hora que ele quer.
	 */
	public List getListaSelectItemHoras() throws Exception {
		List objs = new ArrayList();
		objs.add(new SelectItem("", ""));
		Hashtable horas = (Hashtable) Dominios.getHoras();
		Enumeration keys = horas.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) horas.get(value);
			objs.add(new SelectItem(value, label));
		}
		SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
		Collections.sort((List) objs, ordenador);
		return objs;
	}

	/*
	 * Método responsável por inicializar List<SelectItem> de valores do
	 * ComboBox correspondente ao atributo Minutos Esse método cria uma lista de
	 * minuto em minuto para o usuario selecionar o minuto que ele quer.
	 */

	public List getListaSelectItemMinutos() throws Exception {
		List objs = new ArrayList();
		Hashtable minutos = (Hashtable) Dominios.getMinutos();
		Enumeration keys = minutos.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) minutos.get(value);
			objs.add(new SelectItem(value, label));
		}
		SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
		Collections.sort((List) objs, ordenador);
		return objs;
	}

	/**
	 * Método Responsável por limpar os campos do AgendaVO e a observação do
	 * historicoContatoVO no momento em que a pessoa alterar a opção da combo
	 * Situação Contato
	 */
	public void limparCamposAgendaEHistoricoContato() {
		setAgendaVO(new AgendaVO());
		historicoContatoVO.setObservacao("");
		historicoContatoVO.setObjecaoVO(new ObjecaoVO());
		historicoContatoVO.setAgendaVO(new AgendaVO());
	}

//	/**
//	 * Método responsável por validar se os campos do agendamento estão vazios,
//	 * chamando o agendaVO para fazer essa validação
//	 * 
//	 * @throws Exception
//	 * 
//	 * 
//	 */
//	public void validarStatusTipoAgendamento() throws Exception {
//		try {
//			agendaVO.validarDados(agendaVO);
//			setManterAbertoRichModal(false);
//		} catch (Exception e) {
//			setMensagemDetalhada("msg_erro", e.getMessage());
//			setManterAbertoRichModal(true);
//		}
//	}
//
//	/**
//	 * Método responsavel por abrir e fechar o RichModal PanelAgendamento
//	 * 
//	 * @return
//	 */
//	public String getManterAbertoRichModalPanelAgenda() {
//		if (!getManterAbertoRichModal()) {
//			return "Richfaces.hideModalPanel('panelAgenda')";
//		}
//		return "";
//	}

	

	


	public List getListaSelectItemSituacaoCliente() throws Exception {
		List objs = new ArrayList();
		objs.add(new SelectItem("", ""));
		Hashtable situacaoClientes = (Hashtable) Dominios.getSituacaoCliente();
		Enumeration keys = situacaoClientes.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) situacaoClientes.get(value);
			objs.add(new SelectItem(value, label));
		}
		objs.add(new SelectItem("DE", "Desistentes"));
		objs.add(new SelectItem("VE", "Vencidos"));
		objs.add(new SelectItem("AV", "A Vencer"));
		objs.add(new SelectItem("CA", "Cancelados"));
		SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
		Collections.sort((List) objs, ordenador);
		return objs;
	}

	/**
	 * Rotina responsável por organizar a paginação entre as páginas resultantes
	 * de uma consulta.
	 */
	public String inicializarConsultar() {
		setListaConsulta(new ArrayList());
		setMensagemID("msg_entre_prmconsulta");
		return "editar";
	}

	/**
	 * Operação que libera todos os recursos (atributos, listas, objetos) do
	 * backing bean. Garantindo uma melhor atuação do Garbage Coletor do Java. A
	 * mesma é automaticamente quando realiza o logout.
	 */
	protected void limparRecursosMemoria() {
		super.limparRecursosMemoria();
		agendaVO = null;
	}

	public Boolean getApresentarCalendarDia() {
		if (getControleConsulta().getCampoConsulta().equals("dia")) {
			return true;
		}
		return false;
	}

	public void adicionarMalaDiretaEnviada() throws Exception {
		try {
			if (!malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome().equals("")) {
				malaDiretaEnviadaVO.getClienteVO().setPessoa(getFacade().getPessoa().consultarPorNomePessoa(malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
			}
			if (!getMalaDiretaVO().getCodigo().equals(new Integer(0))) {
				malaDiretaEnviadaVO.setMalaDiretaVO(getMalaDiretaVO());
			}
			if (getMalaDiretaEnviadaVO().getClienteVO().getPessoa().getCodigo().intValue() != 0) {
				Integer campoConsulta = getMalaDiretaEnviadaVO().getClienteVO().getPessoa().getCodigo();
				PessoaVO pessoa = getFacade().getPessoa().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
				getMalaDiretaEnviadaVO().getClienteVO().setPessoa(pessoa);
			}
			getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
			this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
			setMensagemID("msg_dados_adicionados");
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	/**
	 * Método que faz a consulta de todos agendados do dia para confirmar o
	 * comparecimento de cada um.
	 */
	public void consultarAgendadosHojeConfirmacaoComparecimentoHoje() {
		try {
			inicializarAgendadosComparecidosHoje();
			if (agendaVO.getListaConfirmacaoComparecimentoAgendamentoHoje().isEmpty()) {
				Integer empresa = null;
				if(getEmpresaLogado() != null){
					empresa = getEmpresaLogado().getCodigo(); 
				}
				agendaVO.setListaConfirmacaoComparecimentoAgendamentoHoje(getFacade().getAgenda().consultarAgendadosConfirmacaoComparecimentoHoje(Calendario.hoje(), empresa, Uteis.NIVELMONTARDADOS_TODOS));
			}
			agendaVO.setTotalComparecidos(getFacade().getAgenda().inicializarTotalComparecidos(agendaVO.getListaConfirmacaoComparecimentoAgendamentoHoje()));
		} catch (Exception e) {
			setMensagemDetalhada("", e.getMessage());
		}
	}

	/**
	 * Método que faz a consulta de todos agendados do mês para confirmar o
	 * comparecimento de cada um.
	 */
	public void consultarAgendadosMesConfirmacaoComparecimentoMes() {
		try {
			inicializarAgendadosComparecidosMes();
			if (agendaVO.getListaConfirmacaoComparecimentoAgendamentoMes().isEmpty()) {
				Integer empresa = null;
				if(getEmpresaLogado() != null){
					empresa = getEmpresaLogado().getCodigo(); 
				}
				agendaVO.setListaConfirmacaoComparecimentoAgendamentoMes(getFacade().getAgenda().consultarAgendadosConfirmacaoComparecimentoMes(empresa, Uteis.NIVELMONTARDADOS_TODOS));
			}
			agendaVO.setTotalComparecidos(getFacade().getAgenda().inicializarTotalComparecidosMes(agendaVO.getListaConfirmacaoComparecimentoAgendamentoMes()));			
		} catch (Exception e) {
			setMensagemDetalhada("", e.getMessage());
		}
	}

	/**
	 * Método que faz a consulta do Historico de todos agendados para confirmar
	 * o comparecimento de cada um.
	 */
	public void consultarAgendadosHistoricoConfirmacaoComparecimentoHistorico() {
		try {   
                        Integer empresa = null;
                        if(getEmpresaLogado() != null){
                                empresa = getEmpresaLogado().getCodigo(); 
                        }
			agendaVO.setListaConfirmacaoComparecimentoAgendamentoHistorico(getFacade().getAgenda().consultarAgendadosConfirmacaoComparecimentoHistorico(empresa, getValorConsultarNomeComparecimento(), getDataInicio(), getDataTermino(), Uteis.NIVELMONTARDADOS_TODOS));
			agendaVO.setTotalComparecidos(getFacade().getAgenda().inicializarTotalComparecidosHistorico(agendaVO.getListaConfirmacaoComparecimentoAgendamentoHistorico()));
		} catch (Exception e) {
			setMensagemDetalhada("", e.getMessage());
		}
	}

	/**
	 * Método responsavel por apresentar o filtro de pesquisa na tela
	 * metaDetalhadaForm e setar na aba selecionada os caracteres
	 * correspondentes.
	 */
	public void inicializarDadosFiltroPesquisa() {
		try {
			agendaVO.setAbaSelecionada("HI");
			setApresentarFiltroPesquisaHistorico(true);
			agendaVO.setTotalComparecidos(getFacade().getAgenda().inicializarTotalComparecidosHistorico(agendaVO.getListaConfirmacaoComparecimentoAgendamentoHistorico()));
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}
	
	/**
	 * Método responsavel por inicializar o nome do Professor ou Instrutor 
	 * na tela confirmaComparecimentoAgendadoForm.
	 * @return
	 * @throws Exception
	 */
	public String getInicializarNomeConsultorProfessor() throws Exception{
		return getUsuarioLogado().getNome();
	}
	
	/**
	 * Método responsavel por inicializar a aba selecinada 
	 * e controlar a apresentação do filtro de pesquisa.
	 */
	public void inicializarAgendadosComparecidosHoje(){
		try {
			agendaVO.setAbaSelecionada("HJ");
			setApresentarFiltroPesquisaHistorico(false);
			agendaVO.setTotalComparecidos(getFacade().getAgenda().inicializarTotalComparecidos(agendaVO.getListaConfirmacaoComparecimentoAgendamentoHoje()));
		} catch (Exception e) {
			setMensagemDetalhada("", e.getMessage());
		}
	}

	/**
	 * Método responsavel por inicializar a aba selecinada 
	 * e controlar a apresentação do filtro de pesquisa.
	 */
	public void inicializarAgendadosComparecidosMes(){
		agendaVO.setAbaSelecionada("ME");
		setApresentarFiltroPesquisaHistorico(false);
	}
	
	
	
	public AgendaVO getAgendaVO() {
		return agendaVO;
	}

	public void setAgendaVO(AgendaVO agendaVO) {
		this.agendaVO = agendaVO;
	}

	/**
	 * @return the dataConsulta
	 */
	public Date getDataConsulta() {
		return dataConsulta;
	}

	/**
	 * @param dataConsulta
	 *            the dataConsulta to set
	 */
	public void setDataConsulta(Date dataConsulta) {
		this.dataConsulta = dataConsulta;
	}

	/**
	 * @return the consultaSituacao
	 */
	public String getConsultaSituacao() {
		return consultaSituacao;
	}

	/**
	 * @param consultaSituacao
	 *            the consultaSituacao to set
	 */
	public void setConsultaSituacao(String consultaSituacao) {
		this.consultaSituacao = consultaSituacao;
	}

	/**
	 * @return the consultaCategoria
	 */
	public Integer getConsultaCategoria() {
		return consultaCategoria;
	}

	/**
	 * @param consultaCategoria
	 *            the consultaCategoria to set
	 */
	public void setConsultaCategoria(Integer consultaCategoria) {
		this.consultaCategoria = consultaCategoria;
	}

	/**
	 * @return the clienteVO
	 */
	public ClienteVO getClienteVO() {
		return clienteVO;
	}

	/**
	 * @param clienteVO
	 *            the clienteVO to set
	 */
	public void setClienteVO(ClienteVO clienteVO) {
		this.clienteVO = clienteVO;
	}	

	/**
	 * @return the historicoContatoVO
	 */
	public HistoricoContatoVO getHistoricoContatoVO() {
		return historicoContatoVO;
	}

	/**
	 * @param historicoContatoVO
	 *            the historicoContatoVO to set
	 */
	public void setHistoricoContatoVO(HistoricoContatoVO historicoContatoVO) {
		this.historicoContatoVO = historicoContatoVO;
	}

	public MalaDiretaVO getMalaDiretaVO() {
		return malaDiretaVO;
	}

	public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
		this.malaDiretaVO = malaDiretaVO;
	}

	public MalaDiretaEnviadaVO getMalaDiretaEnviadaVO() {
		return malaDiretaEnviadaVO;
	}

	public void setMalaDiretaEnviadaVO(MalaDiretaEnviadaVO malaDiretaEnviadaVO) {
		this.malaDiretaEnviadaVO = malaDiretaEnviadaVO;
	}

	
	public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
		return configuracaoSistemaCRMVO;
	}

	public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
		this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
	}

	

	public String getValorConsultarNomeComparecimento() {
		if (valorConsultarNomeComparecimento == null) {
			valorConsultarNomeComparecimento = "";
		}
		return valorConsultarNomeComparecimento;
	}

	public void setValorConsultarNomeComparecimento(String valorConsultarNomeComparecimento) {
		this.valorConsultarNomeComparecimento = valorConsultarNomeComparecimento;
	}

	public Date getDataInicio() {
		if (dataInicio == null) {
			dataInicio = negocio.comuns.utilitarias.Calendario.hoje();
		}
		return dataInicio;
	}

	public void setDataInicio(Date dataInicio) {
		this.dataInicio = dataInicio;
	}

	public Date getDataTermino() {
		if (dataTermino == null) {
			dataTermino = negocio.comuns.utilitarias.Calendario.hoje();
		}
		return dataTermino;
	}

	public void setDataTermino(Date dataTermino) {
		this.dataTermino = dataTermino;
	}

	public Boolean getApresentarFiltroPesquisaHistorico() {
		if (apresentarFiltroPesquisaHistorico == null) {
			apresentarFiltroPesquisaHistorico = new Boolean(false);
		}
		return apresentarFiltroPesquisaHistorico;
	}

	public void setApresentarFiltroPesquisaHistorico(Boolean apresentarFiltroPesquisaHistorico) {
		this.apresentarFiltroPesquisaHistorico = apresentarFiltroPesquisaHistorico;
	}

}

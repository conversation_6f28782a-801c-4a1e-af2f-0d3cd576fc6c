package controle.crm;

import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.GrupoColaborador;
import negocio.facade.jdbc.crm.GrupoColaboradorParticipante;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas grupoColaboradorForm.jsp grupoColaboradorCons.jsp) com as
 * funcionalidades da classe <code>GrupoColaborador</code>. Implemtação da
 * camada controle (Backing Bean).
 * 
 * @see SuperControle
 * @see GrupoColaborador
 * @see GrupoColaboradorVO
 */
public class GrupoColaboradorControle extends SuperControle {

	private GrupoColaboradorVO grupoColaboradorVO;
	/**
	 * Interface <code>GrupoColaboradorInterfaceFacade</code> responsável pela
	 * interconexão da camada de controle com a camada de negócio. Criando uma
	 * independência da camada de controle com relação a tenologia de
	 * persistência dos dados (DesignPatter: Façade).
	 */
	private GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO;
	private String campoConsultarColaborador;
	private String valorConsultarColaborador;
	private List listaConsultarColaborador;
	private String valorConsultarGerente;
	private List listaConsultarGerente;
	private String campoConsultarGerente;
	private GrupoColaboradorParticipante grupoColaboradorParticipante;
	private Boolean apresentarPanelEdicaoColaborador;
	private Boolean apresentarBotaoParticipante = false;
	private Boolean sucesso = false;
	private Boolean erro = false;
	private List<SelectItem> listaSelectItemEmpresa;
	private String msgAlert;

	public GrupoColaboradorControle() throws Exception {
		obterUsuarioLogado();
		inicializarFacades();
		setControleConsulta(new ControleConsulta());
		setMensagemID("");
	}

	/**
	 * Rotina responsável por disponibilizar um novo objeto da classe
	 * <code>GrupoColaborador</code> para edição pelo usuário da aplicação.
	 */
	public String novo() {
		try{
			setGrupoColaboradorVO(new GrupoColaboradorVO());
			getGrupoColaboradorVO().setEmpresa(getEmpresaLogado());
			setGrupoColaboradorParticipanteVO(new GrupoColaboradorParticipanteVO());
			setApresentarBotaoParticipante(false);
			setMsgAlert("");
			setMensagemID("msg_entre_dados");
			setApresentarPanelEdicaoColaborador(false);
			setSucesso(true);
			setErro(false);
		}catch (Exception e){
			montarErro(e);
		}
		return "editar";
	}

	@SuppressWarnings("unchecked")
	public void montarListaSelectItemEmpresa() throws Exception {
		this.setListaSelectItemEmpresa(new ArrayList<SelectItem>());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for(EmpresaVO empresa : empresas){
        	this.getListaSelectItemEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
	}
	
	/**
	 * Rotina responsável por disponibilizar os dados de um objeto da classe
	 * <code>GrupoColaborador</code> para alteração. O objeto desta classe é
	 * disponibilizado na session da página (request) para que o JSP
	 * correspondente possa disponibilizá-lo para edição.
	 */
	public String editar() throws Exception {
		Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
		try {
			GrupoColaboradorVO obj = getFacade().getGrupoColaborador().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS, getEmpresaLogado().getCodigo());
			obj.setNovoObj(false);
			obj = getFacade().getGrupoColaborador().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, getEmpresaLogado().getCodigo());
			setGrupoColaboradorVO(obj);
			getGrupoColaboradorVO().colocarTipoColaboradorIgualTipoGrupo();
			setApresentarPanelEdicaoColaborador(false);
			setApresentarBotaoParticipante(true);
			setGrupoColaboradorParticipanteVO(new GrupoColaboradorParticipanteVO());
			setMsgAlert("");
			setSucesso(true);
			setErro(false);
			setMensagemID("msg_dados_editar");
		} catch (Exception e) {
			setSucesso(true);
			setErro(false);
			setMsgAlert("");
			setMensagemID("msg_dados_editar");
			return "consultar";
		}
		return "editar";
	}

	/**
	 * Rotina responsável por gravar no BD os dados editados de um novo objeto
	 * da classe <code>GrupoColaborador</code>. Caso o objeto seja novo (ainda
	 * não gravado no BD) é acionado a operação <code>incluir()</code>. Caso
	 * contrário é acionado o <code>alterar()</code>. Se houver alguma
	 * inconsistência o objeto não é gravado, sendo re-apresentado para o
	 * usuário juntamente com uma mensagem de erro.
	 */
	public void gravar() {
		try {
			setMsgAlert("");
			if(!getUsuarioLogado().getAdministrador()){
				grupoColaboradorVO.setEmpresa(getEmpresaLogado());
			}
			if (!grupoColaboradorVO.getGerente().getNome().equals("")) {
				grupoColaboradorVO.setGerente(getFacade().getUsuario().consultarPorNomeUsuario(grupoColaboradorVO.getGerente().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
			}
			if (grupoColaboradorVO.isNovoObj()) {
				getFacade().getGrupoColaborador().incluir(grupoColaboradorVO);
			} else {
				getFacade().getGrupoColaborador().alterar(grupoColaboradorVO);
			}
			setMensagemID("msg_dados_gravados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	/**
	 * Rotina responsavel por executar as consultas disponiveis no JSP
	 * GrupoColaboradorCons.jsp. Define o tipo de consulta a ser executada, por
	 * meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
	 * Como resultado, disponibiliza um List com os objetos selecionados na
	 * sessao da pagina.
	 */
	public String consultar() {
		try {
			Integer empresa = null;
			if(!getUsuarioLogado().getAdministrador()){
				empresa = getEmpresaLogado().getCodigo();
			}
			super.consultar();
			List objs = new ArrayList();
			if (getControleConsulta().getCampoConsulta().equals("codigo")) {
				if (getControleConsulta().getValorConsulta().equals("")) {
					getControleConsulta().setValorConsulta("0");
				}
				int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
				objs = getFacade().getGrupoColaborador().consultarPorCodigo(valorInt, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
			}
			if (getControleConsulta().getCampoConsulta().equals("descricaoGrupo")) {
				objs = getFacade().getGrupoColaborador().consultarPorDescricaoGrupo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
			}
			if (getControleConsulta().getCampoConsulta().equals("tipoGrupo")) {

				if (getControleConsulta().getValorConsulta().length() > 2) {
					objs = getFacade().getGrupoColaborador().consultarPorTipoGrupo(getControleConsulta().getValorConsulta().substring(0, 2), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
				} else {
					objs = getFacade().getGrupoColaborador().consultarPorTipoGrupo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
				}

			}
			
			if (getControleConsulta().getCampoConsulta().equals("responsavelGrupo")) {
				objs = getFacade().getGrupoColaborador().consultarPorResponsavelGrupo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
			}
			if (getControleConsulta().getCampoConsulta().equals("participanteGrupo")) {
				objs = getFacade().getGrupoColaborador().consultarPorParticipanteGrupo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, empresa);
			}

			setListaConsulta(objs);
			setMensagemID("msg_dados_consultados");
			setSucesso(true);
			setErro(false);
			return "consultar";
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setListaConsulta(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "consultar";
		}
	}

	/**
	 * Operação responsável por processar a exclusão um objeto da classe
	 * <code>GrupoColaboradorVO</code> Após a exclusão ela automaticamente
	 * aciona a rotina para uma nova inclusão.
	 */
	public String excluir() {
		try {
			getFacade().getGrupoColaborador().excluir(grupoColaboradorVO);
			novo();
			setMensagemID("msg_dados_excluidos");
			setSucesso(true);
			setErro(false);
			return "consultar";
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"grupocolaborador\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"grupocolaborador\" violates foreign key")){
				setMensagemDetalhada("Este grupo colaborador não pode ser excluído, pois já foi utilizado!");
			}else {
				setMensagemDetalhada("msg_erro", e.getMessage());
			}
			return "editar";
		}
	}

	/*
	 * Método responsável por adicionar um novo objeto da classe
	 * <code>GrupoColaboradorParticipante</code> para o objeto
	 * <code>grupoColaboradorVO</code> da classe <code>GrupoColaborador</code>
	 */
	public String adicionarGrupoColaboradorParticipante() throws Exception {
		try {
			if (!getGrupoColaboradorVO().getCodigo().equals(new Integer(0))) {
				grupoColaboradorParticipanteVO.setGrupoColaborador(getGrupoColaboradorVO());
			}
			if (getGrupoColaboradorParticipanteVO().getColaboradorParticipante().getCodigo() != 0) {
				Integer campoConsulta = getGrupoColaboradorParticipanteVO().getColaboradorParticipante().getCodigo();
				ColaboradorVO colaborador = getFacade().getColaborador().consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
				getGrupoColaboradorParticipanteVO().setColaboradorParticipante(colaborador);
			}
			getGrupoColaboradorVO().adicionarObjGrupoColaboradorParticipanteVOs(getGrupoColaboradorParticipanteVO());
			setApresentarPanelEdicaoColaborador(false);
			this.setGrupoColaboradorParticipanteVO(new GrupoColaboradorParticipanteVO());
			setMensagemID("msg_dados_adicionados");
			setSucesso(true);
			setErro(false);
			return "editar";
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "editar";
		}
	}

	public void adicionarGrupoColaboradorParticipanteLista() throws Exception {
		try {
			setMsgAlert("");
			for (Iterator it = listaConsultarColaborador.iterator(); it.hasNext();) {
				GrupoColaboradorParticipanteVO participante = (GrupoColaboradorParticipanteVO) it.next();
				if (participante.getColaboradorParticipante().getColaboradorEscolhidoIndiceConversao()) {
                    if (!getUsuarioLogado().getAdministrador()) {
                        if (!participante.getColaboradorParticipante().getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                            throw new Exception("Um dos colaboradores selecionados não pertence à empresa deste grupo.");
                        }
                    } else {
                        if (!getGrupoColaboradorVO().getEmpresa().getCodigo().equals(participante.getColaboradorParticipante().getEmpresa().getCodigo())) {
                            throw new Exception("Um dos colaboradores selecionados não pertence à empresa deste grupo.");
                        }
                    }
                    getGrupoColaboradorVO().adicionarObjGrupoColaboradorParticipanteVOs(participante);
					participante.getColaboradorParticipante().setColaboradorEscolhidoIndiceConversao(false);
				}
			}
			setMsgAlert("Richfaces.hideModalPanel('panelColaborador')");
			setMensagemID("msg_dados_adicionados");
			setErro(false);
			setSucesso(true);
		} catch (Exception e) {
			setErro(true);
			setSucesso(false);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	/*
	 * Método responsável por disponibilizar dados de um objeto da classe
	 * <code>GrupoColaboradorParticipante</code> para edição pelo usuário.
	 */
	public String editarGrupoColaboradorParticipante() throws Exception {
		GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("grupoColaboradorParticipante");
		setGrupoColaboradorParticipanteVO(obj);
		setApresentarPanelEdicaoColaborador(true);
		return "editar";
	}

	/*
	 * Método responsável por remover um novo objeto da classe
	 * <code>GrupoColaboradorParticipante</code> do objeto
	 * <code>grupoColaboradorVO</code> da classe <code>GrupoColaborador</code>
	 */
	public String removerGrupoColaboradorParticipante() throws Exception {
		GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) context().getExternalContext().getRequestMap().get("grupoColaboradorParticipante");
		getGrupoColaboradorVO().excluirObjGrupoColaboradorParticipanteVOs(obj.getColaboradorParticipante().getCodigo());
		setApresentarPanelEdicaoColaborador(false);
		setMensagemID("msg_dados_excluidos");
		return "editar";
	}

	/**
	 * Método responsável por processar a consulta na entidade
	 * <code>Colaborador</code> por meio dos parametros informados no richmodal.
	 * Esta rotina é utilizada fundamentalmente por requisições Ajax, que
	 * realizam busca pelos parâmentros informados no richModal montando
	 * automaticamente o resultado da consulta para apresentação.
	 */
	public void consultarColaborador() {
		try {
			if ((getUsuarioLogado().getAdministrador()) &&
			((grupoColaboradorVO.getEmpresa().getCodigo() == null) || (grupoColaboradorVO.getEmpresa().getCodigo() <=0))){
				throw new ConsistirException("É necessário selecionar a empresa antes de consultar os colaboradores");
			}
			super.consultar();
			String sql = retornarColaboradoresNaoSelecionados();
			List objs = new ArrayList();
			if (getCampoConsultarColaborador().equals("codigo")) {
				if (getValorConsultarColaborador().equals("")) {
					setValorConsultarColaborador("0");
				}
				int valorInt = Integer.parseInt(getValorConsultarColaborador());
				objs = getFacade().getGrupoColaboradorParticipante().consultarPorCodigoColaboradorSomenteAtivo
                                        (valorInt, getGrupoColaboradorVO().getTipoGrupo(),
											grupoColaboradorVO.getEmpresa().getCodigo(),
                                        true, Uteis.NIVELMONTARDADOS_DADOSBASICOS, sql);
			}
			if (getCampoConsultarColaborador().equals("nome")) {
				objs = getFacade().getGrupoColaboradorParticipante().consultarPorNomePessoaColaboradorSomenteAtivo(
                                        getValorConsultarColaborador(), getGrupoColaboradorVO().getTipoGrupo(),
										grupoColaboradorVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS, sql);
			}
		/*	if (getCampoConsultarColaborador().equals("tipoColaborador")) {
				if (getValorConsultarColaborador().length() > 2) {
					objs = getFacade().getGrupoColaboradorParticipante().consultarPorTipoColaboradorSomenteAtivo(
                                                getValorConsultarColaborador().substring(0, 2), getGrupoColaboradorVO().getTipoGrupo(),
                                                grupoColaboradorVO.getEmpresa().getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS, sql);
				} else {
					objs = getFacade().getGrupoColaboradorParticipante().consultarPorTipoColaboradorSomenteAtivo(
                                                getValorConsultarColaborador(), getGrupoColaboradorVO().getTipoGrupo(),
												grupoColaboradorVO.getEmpresa().getCodigo(),true, Uteis.NIVELMONTARDADOS_DADOSBASICOS, sql);
				}

			}*/
			setListaConsultarColaborador(objs);
			setMensagemID("msg_dados_consultados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setListaConsultarColaborador(new ArrayList());
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	public String retornarColaboradoresNaoSelecionados() {
		String sql = "";
		String sql1 = "( ";
		Iterator it = getGrupoColaboradorVO().getGrupoColaboradorParticipanteVOs().iterator();
		while (it.hasNext()) {
			GrupoColaboradorParticipanteVO obj = (GrupoColaboradorParticipanteVO) it.next();
			sql += "colaborador.codigo <> " + obj.getColaboradorParticipante().getCodigo() + "  and ";
		}
		if (!sql.equals("")) {
			int tamanho = sql.length();
			sql1 += sql.substring(0, (tamanho - 4));
			sql1 += " ) ";
		} else {
			sql1 = "";
		}
		return sql1;
	}

	public void consultarColaboradorGerente() {
		try {
			super.consultar();
			List objs = new ArrayList();
			if (getCampoConsultarGerente().equals("codigo")) {
				if (getValorConsultarGerente().equals("")) {
					setValorConsultarGerente("0");
				}
				int valorInt = Integer.parseInt(getValorConsultarGerente());
				objs = getFacade().getUsuario().consultarPorCodigo(valorInt, false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			if (getCampoConsultarGerente().equals("nome")) {
				objs = getFacade().getUsuario().consultarPorNome(getValorConsultarGerente(), false, Uteis.NIVELMONTARDADOS_TODOS);
			}
			// if (getCampoConsultarGerente().equals("cpf")) {
			// objs =
			// colaboradorFacade.consultarPorCfp(getValorConsultarGerente(),
			// getEmpresaLogado().getCodigo().intValue(), true,
			// Uteis.NIVELMONTARDADOS_TODOS);
			// }
			setListaConsultarGerente(objs);
			setMensagemID("msg_dados_consultados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setListaConsultarGerente(new ArrayList());
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	public void zerarListaUsuarioColaborador() {
		setListaConsultarColaborador(new ArrayList());
	}

	public void selecionarColaborador() throws Exception {
		ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
		this.getGrupoColaboradorParticipanteVO().setColaboradorParticipante(obj);
		Uteis.liberarListaMemoria(this.getListaConsultarColaborador());
		this.setValorConsultarColaborador(null);
		this.setCampoConsultarColaborador(null);
	}

	public void selecionarColaboradorGerente() throws Exception {
		UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("usuario");
		this.getGrupoColaboradorVO().setGerente(obj);
		Uteis.liberarListaMemoria(this.getListaConsultarGerente());
		this.setValorConsultarGerente(null);
		this.setCampoConsultarGerente(null);
	}

	public void limparCampoColaborador() {
		this.getGrupoColaboradorParticipanteVO().setColaboradorParticipante(new ColaboradorVO());
	}

	public void limparCampoColaboradorGerente() {
		this.getGrupoColaboradorVO().setGerente(new UsuarioVO());
	}

	/**
	 * Rotina responsável por preencher a combo de consulta dos RichModal da
	 * telas.
	 */
	public List getTipoConsultarComboColaborador() {
		List itens = new ArrayList();
		itens.add(new SelectItem("nome", "Nome"));
		itens.add(new SelectItem("codigo", "Código"));
		return itens;
	}

	public List getTipoConsultarComboGerente() {
		List itens = new ArrayList();
		itens.add(new SelectItem("nome", "Nome"));
		itens.add(new SelectItem("codigo", "Código"));
		return itens;
	}

	public List getListaSelectItemTipoVisao() throws Exception {
		List objs = new ArrayList();
		Hashtable TipoVisao = (Hashtable) Dominios.getTipoVisao();
		Enumeration keys = TipoVisao.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) TipoVisao.get(value);
			objs.add(new SelectItem(value, label));
		}
		return objs;
	}

	public List getListaSelectItemSituacaoGrupo() throws Exception {
		List objs = new ArrayList();
		Hashtable situacaoGrupo = (Hashtable) Dominios.getTipoStatus();
		Enumeration keys = situacaoGrupo.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) situacaoGrupo.get(value);
			objs.add(new SelectItem(value, label));
		}
		return objs;
	}

	/**
	 * Rotina responsável por atribui um javascript com o método de mascara para
	 * campos do tipo Data, CPF, CNPJ, etc.
	 */
	public String getMascaraConsulta() {
		return "";
	}

	/**
	 * Rotina responsável por preencher a combo de consulta da telas.
	 */
	public List getTipoConsultaCombo() {
		List itens = new ArrayList();
		itens.add(new SelectItem("descricaoGrupo", "Descrição Grupo"));
		itens.add(new SelectItem("tipoGrupo", "Tipo Grupo"));
		itens.add(new SelectItem("codigo", "Código"));
		itens.add(new SelectItem("responsavelGrupo", "Responsável Grupo"));
		itens.add(new SelectItem("participanteGrupo", "Participante Grupo"));
		return itens;
	}

	/**
	 * Rotina responsável por organizar a paginação entre as páginas resultantes
	 * de uma consulta.
	 */
	public String inicializarConsultar() {
		setListaConsulta(new ArrayList());
		setMensagemID("");
		return "consultar";
	}

	/**
	 * Operação que libera todos os recursos (atributos, listas, objetos) do
	 * backing bean. Garantindo uma melhor atuação do Garbage Coletor do Java. A
	 * mesma é automaticamente quando realiza o logout.
	 */
	protected void limparRecursosMemoria() {
		super.limparRecursosMemoria();
		grupoColaboradorVO = null;
		grupoColaboradorParticipanteVO = null;
	}

	public List<UsuarioVO> executarAutocompleteResponsavel(Object suggest) {
		String pref = (String) suggest;
		ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
		try {
			if (pref.equals("%")) {
				result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarTodosUsuarioComLimite(
                                        false,getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
			} else {
				result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(
                                        pref,getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
			}
		} catch (Exception ex) {
			result = (new ArrayList<UsuarioVO>());
		}
		return result;
	}

	public List getListaSelectItemTipoColaborador() throws Exception {
		List objs = new ArrayList();
		objs.add(new SelectItem("", ""));
		Hashtable tipoGrupo = (Hashtable) Dominios.getTipoGrupo();
		Enumeration keys = tipoGrupo.keys();
		while (keys.hasMoreElements()) {

			String value = (String) keys.nextElement();
			String label = (String) tipoGrupo.get(value);
			objs.add(new SelectItem(value, label));
		}
		SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
		Collections.sort((List) objs, ordenador);
		return objs;
	}

	public void apresentarBotaoColaboradorParticipante() {
		try {
			if (!getGrupoColaboradorVO().getTipoGrupo().equals("")) {
				setApresentarBotaoParticipante(true);
				getFacade().getGrupoColaborador().executarValidacaoSeExisterParticipanteDiferenteDoTipoGrupo(getGrupoColaboradorVO());
			} else {
				throw new ConsistirException("O campo TIPO GRUPO deve ser informado!");
			}
			setMensagem("");
			setMensagemID("");
			setMensagemDetalhada("");
			setSucesso(false);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	/**
	 * Operação que inicializa as Interfaces Façades com os respectivos objetos
	 * de persistência dos dados no banco de dados.
	 */
	protected boolean inicializarFacades() {
		try {
			super.inicializarFacades();

			return true;
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
			return false;
		}
	}

	public String getCampoConsultarColaborador() {
		return campoConsultarColaborador;
	}

	public void setCampoConsultarColaborador(String campoConsultarColaborador) {
		this.campoConsultarColaborador = campoConsultarColaborador;
	}

	public String getValorConsultarColaborador() {
		return valorConsultarColaborador;
	}

	public void setValorConsultarColaborador(String valorConsultarColaborador) {
		this.valorConsultarColaborador = valorConsultarColaborador;
	}

	public List getListaConsultarColaborador() {
		if (listaConsultarColaborador == null) {
			listaConsultarColaborador = new ArrayList();
		}
		return listaConsultarColaborador;
	}

	public void setListaConsultarColaborador(List listaConsultarColaborador) {
		this.listaConsultarColaborador = listaConsultarColaborador;
	}

	public GrupoColaboradorParticipanteVO getGrupoColaboradorParticipanteVO() {
		return grupoColaboradorParticipanteVO;
	}

	public void setGrupoColaboradorParticipanteVO(GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO) {
		this.grupoColaboradorParticipanteVO = grupoColaboradorParticipanteVO;
	}

	public GrupoColaboradorVO getGrupoColaboradorVO() {
		return grupoColaboradorVO;
	}

	public void setGrupoColaboradorVO(GrupoColaboradorVO grupoColaboradorVO) {
		this.grupoColaboradorVO = grupoColaboradorVO;
	}

	/**
	 * @return the valorConsultarGerente
	 */
	public String getValorConsultarGerente() {
		return valorConsultarGerente;
	}

	/**
	 * @param valorConsultarGerente
	 *            the valorConsultarGerente to set
	 */
	public void setValorConsultarGerente(String valorConsultarGerente) {
		this.valorConsultarGerente = valorConsultarGerente;
	}

	/**
	 * @return the listaConsultarGerente
	 */
	public List getListaConsultarGerente() {
		return listaConsultarGerente;
	}

	/**
	 * @param listaConsultarGerente
	 *            the listaConsultarGerente to set
	 */
	public void setListaConsultarGerente(List listaConsultarGerente) {
		this.listaConsultarGerente = listaConsultarGerente;
	}

	/**
	 * @return the campoConsultarGerente
	 */
	public String getCampoConsultarGerente() {
		return campoConsultarGerente;
	}

	/**
	 * @param campoConsultarGerente
	 *            the campoConsultarGerente to set
	 */
	public void setCampoConsultarGerente(String campoConsultarGerente) {
		this.campoConsultarGerente = campoConsultarGerente;
	}

	/**
	 * @return the grupoColaboradorParticipante
	 */
	public GrupoColaboradorParticipante getGrupoColaboradorParticipante() {
		return grupoColaboradorParticipante;
	}

	/**
	 * @param grupoColaboradorParticipante
	 *            the grupoColaboradorParticipante to set
	 */
	public void setGrupoColaboradorParticipante(GrupoColaboradorParticipante grupoColaboradorParticipante) {
		this.grupoColaboradorParticipante = grupoColaboradorParticipante;
	}

	/**
	 * @return the apresentarPanelEdicaoColaborador
	 */
	public Boolean getApresentarPanelEdicaoColaborador() {
		return apresentarPanelEdicaoColaborador;
	}

	/**
	 * @param apresentarPanelEdicaoColaborador
	 *            the apresentarPanelEdicaoColaborador to set
	 */
	public void setApresentarPanelEdicaoColaborador(Boolean apresentarPanelEdicaoColaborador) {
		this.apresentarPanelEdicaoColaborador = apresentarPanelEdicaoColaborador;
	}

	/**
	 * @return the sucesso
	 */
	public Boolean getSucesso() {
		return sucesso;
	}

	/**
	 * @param sucesso
	 *            the sucesso to set
	 */
	public void setSucesso(Boolean sucesso) {
		this.sucesso = sucesso;
	}

	/**
	 * @return the erro
	 */
	public Boolean getErro() {
		return erro;
	}

	/**
	 * @param erro
	 *            the erro to set
	 */
	public void setErro(Boolean erro) {
		this.erro = erro;
	}

	/**
	 * @return the apresentarBotaoParticipante
	 */
	public Boolean getApresentarBotaoParticipante() {
		return apresentarBotaoParticipante;
	}

	/**
	 * @param apresentarBotaoParticipante
	 *            the apresentarBotaoParticipante to set
	 */
	public void setApresentarBotaoParticipante(Boolean apresentarBotaoParticipante) {
		this.apresentarBotaoParticipante = apresentarBotaoParticipante;
	}

	/**
	 * @param listaSelectItemEmpresa the listaSelectItemEmpresa to set
	 */
	public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
		this.listaSelectItemEmpresa = listaSelectItemEmpresa;
	}

	/**
	 * @return the listaSelectItemEmpresa
	 * @throws Exception 
	 */
	public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
		if(listaSelectItemEmpresa == null){
			montarListaSelectItemEmpresa();
		}
		return listaSelectItemEmpresa;
	}

	public void setMsgAlert(String msgAlert) {
		this.msgAlert = msgAlert;
	}

	public String getMsgAlert() {
		if (msgAlert == null) {
			return "";
		}
		return msgAlert;
	}


	public void confirmarExcluir(){
		MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
		control.setMensagemDetalhada("", "");
		setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
		control.init("Exclusão de Grupo Colaborador",
				"Deseja excluir o Grupo Colaborador?",
				this, "excluir", "", "", "", "grupoBtnExcluir");
	}

}

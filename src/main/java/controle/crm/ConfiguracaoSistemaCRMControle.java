package controle.crm;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.ConfiguracaoEmailEnum;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoEmailFechamentoMetaVO;
import negocio.comuns.crm.ConfiguracaoDiasMetasTO;
import negocio.comuns.crm.ConfiguracaoDiasPosVendaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.FaixaHorarioAcessoClienteVO;
import negocio.comuns.crm.TiposVinculosFaseVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import servicos.integracao.sendy.services.SendyImpl;
import servicos.integracao.sendy.to.BrandTO;
import servicos.propriedades.PropsService;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas configuracaoSistemaCRMForm.jsp configuracaoSistemaCRMCons.jsp) com as
 * funcionalidades da classe <code>ConfiguracaoSistemaCRM</code>. Implemtação da
 * camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see ConfiguracaoSistemaCRM
 * @see ConfiguracaoSistemaCRMVO
 */
public class ConfiguracaoSistemaCRMControle extends SuperControle {

	private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
	private FaixaHorarioAcessoClienteVO faixaAcesso;
	private List<FaixaHorarioAcessoClienteVO> faixasPeriodoAcesso;
	private List<ConfiguracaoEmailFechamentoMetaVO> emailsFechamento;
	private boolean configuracaoAvancada = false;
	private ConfiguracaoEmailFechamentoMetaVO emailFechamento;
	private ConfiguracaoDiasMetasTO configuracaoDiasMetas = new ConfiguracaoDiasMetasTO();
	private String senhaEmail;
	private List<TiposVinculosFaseVO> tiposVinculosFase = new ArrayList<>();
	private String termoSpam = "";
    private String emailTeste;
    private Integer faseEstudioCodigo = FasesCRMEnum.ULTIMAS_SESSOES.getCodigo();
    private ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO;
    private ProdutoVO produto = new ProdutoVO();
	private List<FasesCRMEnum> listaOrdenacaoMetas;
	private String msgAlert;
	private List<SelectItem> selectTipoColaboradorPosVenda;
	private String jsonHealthCheckSendy;
	private boolean permiteHealthCheck = false;

	public ConfiguracaoSistemaCRMControle() throws Exception {
		obterUsuarioLogado();
		inicializarFacades();
		setConfiguracaoSistemaCRMVO(new ConfiguracaoSistemaCRMVO());
		setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVendaVO());
		setControleConsulta(new ControleConsulta());
		consultarConfiguracoes();
		montarListaEmpresas();
		setEmailsFechamento(getFacade().getConfiguracaoSistemaCRM().consultarEmailsEnviarFechamento(null));
		setFaixasPeriodoAcesso(getFacade().getFaixaHorarioAcessoCliente().consultarFaixas());
		setMensagemID("msg_entre_prmconsulta");
		getMontarSelectTipoColaboradorPosVenda();
		validaPermissaoAcessoHealthCheck();
		if (configuracaoSistemaCRMVO.getDividirFase()) montarTabelaFases();
	}

	private void validaPermissaoAcessoHealthCheck() {
		try {
			setPermiteHealthCheck(getUsuarioLogado().getUsername().equalsIgnoreCase("admin"));
		} catch (Exception e) {
			setPermiteHealthCheck(false);
		}
	}

	public List<SelectItem> getFaseEstudioItens(){
		List<SelectItem> lista = new ArrayList<>();
		lista.add(new SelectItem(FasesCRMEnum.ULTIMAS_SESSOES.getCodigo(), FasesCRMEnum.ULTIMAS_SESSOES.getDescricao()));
		lista.add(new SelectItem(FasesCRMEnum.SEM_AGENDAMENTO.getCodigo(), FasesCRMEnum.SEM_AGENDAMENTO.getDescricao()));
		return lista;
	}

	public String removerConfiguracaoDiasSemAgendamento() {
		ConfiguracaoDiasMetasTO obj = (ConfiguracaoDiasMetasTO) context().getExternalContext().getRequestMap().get("configuracaoDiasMetas");
		getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSemAgendamento().remove(obj);
		setMensagemID("msg_dados_excluidos");
		setSucesso(true);
		setErro(false);
		return "editar";
	}

	public String removerConfiguracaoDiasSessoesFinais() {
		ConfiguracaoDiasMetasTO obj = (ConfiguracaoDiasMetasTO) context().getExternalContext().getRequestMap().get("configuracaoDiasMetas");
		getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSessoesFinais().remove(obj);
		setMensagemID("msg_dados_excluidos");
		setSucesso(true);
		setErro(false);
		return "editar";
	}


    public void adicionarConfiguracaoSemAgendamento(){
        try {
            setMsgAlert("");
            getConfiguracaoSistemaCRMVO().adicionarObjConfiguracaoDiasMetasSemAgendamento(getConfiguracaoDiasMetas());
            getConfiguracaoSistemaCRMVO().setConfiguracaoDiasMetasSemAgendamento(Ordenacao.ordenarLista(getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSemAgendamento(), "nrDia"));
            setConfiguracaoDiasMetas(new ConfiguracaoDiasMetasTO());
            setProduto(new ProdutoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarMsgAlert(e.getMessage());
        }
    }
    public void adicionarConfiguracaoSessoesFinais(){
           try {
            setMsgAlert("");
            getConfiguracaoSistemaCRMVO().adicionarObjConfiguracaoDiasMetasSessoesFinais(getConfiguracaoDiasMetas());
            getConfiguracaoSistemaCRMVO().setConfiguracaoDiasMetasSessoesFinais(Ordenacao.ordenarLista(getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSessoesFinais(), "nrDia"));
            setConfiguracaoDiasMetas(new ConfiguracaoDiasMetasTO());
            setProduto(new ProdutoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarMsgAlert(e.getMessage());
        }
    }

	public String removerConfiguracaoDiasUltimoAcessoGymp() {
		ConfiguracaoDiasMetasTO obj = (ConfiguracaoDiasMetasTO) context().getExternalContext().getRequestMap().get("configuracaoDiasMetas");
		getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasUltimoAcessoGymp().remove(obj);
		setMensagemID("msg_dados_excluidos");
		setSucesso(true);
		setErro(false);
		return "editar";
	}

    public String removerConfiguracaoDiasExAlunos() {
        ConfiguracaoDiasMetasTO obj = (ConfiguracaoDiasMetasTO) context().getExternalContext().getRequestMap().get("configuracaoDiasMetas");
        getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasExAlunos().remove(obj);
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public String removerConfiguracaoDiasVisitantesAntigos() {
        ConfiguracaoDiasMetasTO obj = (ConfiguracaoDiasMetasTO) context().getExternalContext().getRequestMap().get("configuracaoDiasMetas");
        getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasVisitantesAntigos().remove(obj);
        setMensagemID("msg_dados_excluidos");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public void adicionarConfiguracaoExAlunos(){
        try {
            setMsgAlert("");
            getConfiguracaoSistemaCRMVO().adicionarObjConfiguracaoDiasExAlunos(getConfiguracaoDiasMetas());
            getConfiguracaoSistemaCRMVO().setConfiguracaoDiasMetasExAlunos(Ordenacao.ordenarLista(getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasExAlunos(), "nrDia"));
            setConfiguracaoDiasMetas(new ConfiguracaoDiasMetasTO());
            setProduto(new ProdutoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarMsgAlert(e.getMessage());
        }
    }


	public void adicionarConfiguracaoUltimoAcssoGym(){
		try {
			setMsgAlert("");
			getConfiguracaoSistemaCRMVO().adicionarObjConfiguracaoDiasUltimosAcessosGymp(getConfiguracaoDiasMetas());
			getConfiguracaoSistemaCRMVO().setConfiguracaoDiasMetasUltimoAcessoGymp(Ordenacao.ordenarLista(getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasUltimoAcessoGymp(), "nrDia"));
			setConfiguracaoDiasMetas(new ConfiguracaoDiasMetasTO());
			setProduto(new ProdutoVO());
			setMensagemID("msg_dados_adicionados");
			setSucesso(true);
			setErro(false);

		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			montarMsgAlert(e.getMessage());
		}
	}
    public void adicionarConfiguracaoVisitantesAntigos(){
         try {
            setMsgAlert("");
            getConfiguracaoSistemaCRMVO().adicionarObjConfiguracaoDiasMetasVisitantesAntigos(getConfiguracaoDiasMetas());
            getConfiguracaoSistemaCRMVO().setConfiguracaoDiasMetasVisitantesAntigos(Ordenacao.ordenarLista(getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasVisitantesAntigos(), "nrDia"));
            setConfiguracaoDiasMetas(new ConfiguracaoDiasMetasTO());
            setProduto(new ProdutoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            montarMsgAlert(e.getMessage());
        }
    }

	public boolean getSessoesFinais(){
		return faseEstudioCodigo.equals(FasesCRMEnum.ULTIMAS_SESSOES.getCodigo());
	}

	public boolean getSemAgendamento(){
		return faseEstudioCodigo.equals(FasesCRMEnum.SEM_AGENDAMENTO.getCodigo());
	}

	private void consultarConfiguracoes() throws Exception {
		List listaResultado = getFacade().getConfiguracaoSistemaCRM().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_TODOS);
		if (listaResultado.equals(new ArrayList())) {
			novo();
		} else {
			editar(listaResultado);
			setSenhaEmail(getConfiguracaoSistemaCRMVO().getSenha());
			montarListaFasesCRM();
		}
	}

    public void iniciar() throws Exception{
    	consultarConfiguracoes();
    	iniciarFaixa();
        setEmailsFechamento(getFacade().getConfiguracaoSistemaCRM().consultarEmailsEnviarFechamento(null));
    	setFaixasPeriodoAcesso(getFacade().getFaixaHorarioAcessoCliente().consultarFaixas());
        montarTabelaFases();
    	faseEstudioCodigo = FasesCRMEnum.ULTIMAS_SESSOES.getCodigo();
        getConfiguracaoSistemaCRMVO().registrarObjetoVOAntesDaAlteracao();
		montarListaEmpresas();
	}

    public void sugerirDivisao(){
    	setTiposVinculosFase(TiposVinculosFaseVO.sugestaoMetodo());
    }

	/**
	 * author: alcides
	 * 21/10/2011
	 */
	public void iniciarFaixa() {
		this.setFaixaAcesso(new FaixaHorarioAcessoClienteVO());
		for(FaixaHorarioAcessoClienteVO faixa : getFaixasPeriodoAcesso()){
			faixa.setEdicao(Boolean.FALSE);
		}
		setErro(false);
		setSucesso(false);
    	limparMsg();
	}

	public void montarTabelaFases(){
		try {
			if(getConfiguracaoSistemaCRMVO().getDividirFase())
				this.setTiposVinculosFase(getFacade().getTiposVinculosFase().consultarTodos());
			else
				this.setTiposVinculosFase(new ArrayList<>());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void gravarTiposVinculosFase() throws Exception{
		getFacade().getTiposVinculosFase().incluir(getTiposVinculosFase());
	}


	/**
	 * Rotina responsável por disponibilizar um novo objeto da classe
	 * <code>ConfiguracaoSistemaCRM</code> para edição pelo usuário da
	 * aplicação.
	 */
	public String novo() {
		setConfiguracaoSistemaCRMVO(new ConfiguracaoSistemaCRMVO());
		setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVendaVO());
		setSucesso(true);
		setErro(false);
		montarTabelaFases();
		setMensagemID("msg_entre_dados");
		return "editar";
	}

	/**
	 * Rotina responsável por disponibilizar os dados de um objeto da classe
	 * <code>ConfiguracaoSistemaCRM</code> para alteração. O objeto desta classe
	 * é disponibilizado na session da página (request) para que o JSP
	 * correspondente possa disponibilizá-lo para edição.
	 */
	public String editar(List listaResultado) {
		Iterator i = listaResultado.iterator();
		while (i.hasNext()) {
			ConfiguracaoSistemaCRMVO obj = (ConfiguracaoSistemaCRMVO) i.next();
			obj.setNovoObj(false);
			obj.registrarObjetoVOAntesDaAlteracao();
			setConfiguracaoSistemaCRMVO(obj);
		}
		setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVendaVO());
		setMensagemID("msg_dados_editar");
		setSucesso(true);
		setErro(false);
		return "editar";
	}
	/**
	 * Responsável por adicionar o tipo de colaborador escolhido a fase correspondente
	 * author: alcides
	 * 09/11/2011
	 */
	public void adicionarTipoAFase(){
		TiposVinculosFaseVO fase = (TiposVinculosFaseVO)context().getExternalContext().getRequestMap().get("fase");
		if(!UteisValidacao.emptyString(fase.getTipoSelecionado())){
			TipoColaboradorEnum tipoCol = TipoColaboradorEnum.getTipo(fase.getTipoSelecionado());
			if(!fase.getTiposColaborador().contains(tipoCol)){
				fase.getTiposColaborador().add(tipoCol);
			}
			fase.setTipoSelecionado("");
		}

	}
	/**
	 * Responsável por remover o tipo de colaborador escolhido a fase correspondente
	 * author:  alcides
	 * 09/11/2011
	 */
	public void removerTipoAFase(){
		TiposVinculosFaseVO fase = (TiposVinculosFaseVO)context().getExternalContext().getRequestMap().get("fase");
		TipoColaboradorEnum tipoCol = (TipoColaboradorEnum)context().getExternalContext().getRequestMap().get("tipo");
		fase.getTiposColaborador().remove(tipoCol);
	}
	public static void validarEmail( ConfiguracaoSistemaCRMVO configuracaoSistema) throws ValidacaoException {

		if (configuracaoSistema.getEmailPadrao().isEmpty()) {
			throw new ValidacaoException(new String[]{"emailid"}, "O campo EMAIL-Padrão deve ser informado.");
		}

		if (configuracaoSistema.getEmailPadrao().matches("^((.)*(\\s)+(.)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O EMAIL-Padrão não pode conter espaços em branco");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^(([a-zA-Z0-9]|\\.|\\_|\\-)*@(.)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O endereço de EMAIL-Padrão não pode conter caracteres especiais além de ponto (.), hífen (-) e underline (_)");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.)*@([a-zA-Z0-9]|\\.|\\-)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O domínio do EMAIL-Padrão não pode conter caracteres especiais além de ponto (.) e hífen (-)");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.){2,}@(.){2,})$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O endereço e o domínio do EMAIL-Padrão devem possuir ao menos dois caracteres cada e estarem separados por arroba (@)");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^([a-zA-Z0-9](.)*@(.)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O endereço de EMAIL-Padrão deve começar com uma letra");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.)*@[a-zA-Z0-9](.)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O domínio do EMAIL-Padrão deve começar com uma letra ou número");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.)*@(.)*[a-zA-Z0-9])$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O domínio do EMAIL-Padrão deve terminar com uma letra ou número");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.)*@[^\\.\\-]*([\\.|\\-][^\\.\\-]+)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "O domínio do EMAIL-Padrão não pode conter dois caracteres especiais seguidos");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.)*@[^\\.]{1,26}(\\.[^\\.]{2,26})+)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "Cada domínio do EMAIL-Padrão deve possuir no máximo vinte e seis caracteres e estarem separados por ponto \'.\'");
		}
		if (!configuracaoSistema.getEmailPadrao().matches("^((.)*@[^\\.]*[a-zA-Z][^\\.]*(\\.[^\\.]*[a-zA-Z][^\\.]*)*)$")) {
			throw new ValidacaoException(new String[]{"emailid"}, "Cada domínio do EMAIL-Padrão deve possuir ao menos uma letra");
		}
	}
	public void criaBrandSendy() throws Exception {
		if (configuracaoSistemaCRMVO.getIntegracaoPacto()) {
			SendyImpl sendyImpl = new SendyImpl();
			validarEmail( configuracaoSistemaCRMVO);
			String user = StringUtilities.doRemoverAcentos( configuracaoSistemaCRMVO.getEmailPadrao().trim().toLowerCase());
			user = user.split("@").length > 0 ?  user.split("@")[0] : user;
			user = StringUtilities.doRemoverAcentos(user.trim().toLowerCase());
			user = user.replace(" ", "");

			BrandTO brandTO = new BrandTO();
			brandTO.setAppName(getNomeEmpresaLogada().toUpperCase());

			String usersmtp = user + "@" + Uteis.getDomainMail();
			brandTO.setUserSmtp(usersmtp);
			brandTO.setDomain(Uteis.getDomainMail());
			if(Uteis.isAmbienteDesenvolvimentoTeste()){
				user = PropsService.asString(PropsService.postmasterMailgun);
				brandTO.setFromEmail(PropsService.asString(PropsService.postmasterFrom));
				brandTO.setAppName(PropsService.asString(PropsService.brandChuckNorris));
				brandTO.setUserSmtp(user);
				brandTO.setDomain(Uteis.getDomainMail());
			}
			brandTO.setFromEmail(usersmtp);
			brandTO.setFromName(configuracaoSistemaCRMVO.getRemetentePadrao());
			brandTO.setReplyTo(configuracaoSistemaCRMVO.getEmailPadrao());
			brandTO.setQuota(configuracaoSistemaCRMVO.getLimiteMensalPacto().toString());
			brandTO.setApiKeyMailing(Uteis.getTokenMailgun());
			brandTO.setHostSmtp(ConfiguracaoEmailEnum.MAILGUN.getSmtpPadrao());
			brandTO.setSmtpPort(ConfiguracaoEmailEnum.MAILGUN.getPortaPadrao()[0]);
			brandTO.setSmtpTsl("tls");


			try {
				if (Uteis.isAmbienteDesenvolvimentoTeste()){
					brandTO.setAppKey(Uteis.getChaveEmpresaSwarmParaSendy());
				} else {
					brandTO.setAppKey(JSFUtilities.getFromSession("key").toString());
				}
				sendyImpl.createBrand(brandTO);
			} catch (Exception e) {
			}
		}
	}

	/**
	 * Rotina responsável por gravar no BD os dados editados de um novo objeto
	 * da classe <code>ConfiguracaoSistemaCRM</code>. Caso o objeto seja novo
	 * (ainda não gravado no BD) é acionado a operação <code>incluir()</code>.
	 * Caso contrário é acionado o <code>alterar()</code>. Se houver alguma
	 * inconsistência o objeto não é gravado, sendo re-apresentado para o
	 * usuário juntamente com uma mensagem de erro.
	 */
    public void gravar() {
        try {
			if (configuracaoSistemaCRMVO.getIntegracaoPacto() && !UteisValidacao.emptyNumber(configuracaoSistemaCRMVO.getLimiteDiarioEmails())) {
					if (configuracaoSistemaCRMVO.getLimiteDiarioEmails() > configuracaoSistemaCRMVO.getLimiteMensalPacto()) {
						throw new Exception("O pacote Wagi contratado é de " + configuracaoSistemaCRMVO.getLimiteMensalPacto() + " envios mensais. O limite diário não pode ser maior que o pacote mensal.");
					}
			}

            List<ConfiguracaoEmailFechamentoMetaVO> emailsFechamentoant = getFacade().getConfiguracaoSistemaCRM().consultarEmailsEnviarFechamento(null);
            List<TiposVinculosFaseVO> tiposVinculosFaseAnt = getFacade().getTiposVinculosFase().consultarTodos();
            List<FaixaHorarioAcessoClienteVO> faixasPeriodoAcessoAnt = getFacade().getFaixaHorarioAcessoCliente().consultarFaixas();
            List<ConfiguracaoDiasPosVendaVO> configuracaoDiasPosVendaAnt = getFacade().getConfiguracaoDiasPosVenda().consultarPorCodigoConfiguracaoSistemaCRM(configuracaoSistemaCRMVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasExAlunosAnt = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.EX_ALUNOS);
            List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSessoesFinaisAnt = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.ULTIMAS_SESSOES);
            List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSemAgendamentoAnt = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.SEM_AGENDAMENTO);
            List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasVisitantesAntigosAnt = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.VISITANTES_ANTIGOS);
			List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasVisitantesRecorrentes = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoDiasMetasPorFase(FasesCRMEnum.VISITA_RECORRENTE);

            if (UteisValidacao.emptyString(getConfiguracaoSistemaCRMVO().getSenha())) {
                getConfiguracaoSistemaCRMVO().setSenha(getSenhaEmail());
            } else {
                setSenhaEmail(getConfiguracaoSistemaCRMVO().getSenha());
            }
			if(!configuracaoSistemaCRMVO.getMailServer().equals("") || configuracaoSistemaCRMVO.getIntegracaoPacto() )  validaUsuario();
            getFacade().getFaixaHorarioAcessoCliente().incluir(getFaixasPeriodoAcesso());
            getFacade().getConfiguracaoSistemaCRM().salvarEmailsFechamento(getEmailsFechamento());
            gravarOrdenacaoMetas();
            if (configuracaoSistemaCRMVO.isNovoObj()) {
                getFacade().getConfiguracaoSistemaCRM().incluir(configuracaoSistemaCRMVO);
            } else {
                getFacade().getConfiguracaoSistemaCRM().alterar(configuracaoSistemaCRMVO);
            }
			if (configuracaoSistemaCRMVO.getIntegracaoPacto())
				criaBrandSendy();
            iniciarFaixa();
            gravarTiposVinculosFase();
            registrarLogCRM();
            registraLogAlteracaoListasConfiguracao(emailsFechamentoant,tiposVinculosFaseAnt,faixasPeriodoAcessoAnt,
                    configuracaoDiasPosVendaAnt,configuracaoDiasMetasExAlunosAnt,configuracaoDiasMetasSessoesFinaisAnt,configuracaoDiasMetasSemAgendamentoAnt,configuracaoDiasMetasVisitantesAntigosAnt);
            notificarOuvintes("updateConfiguracaoSistemaWS");
			montarSucessoDadosGravados();
        } catch (Exception e) {
            montarErro(e);
        }
		setMsgAlert(getMensagemNotificar());
    }

    private void registrarLogCRM() throws Exception {
        try {
            registrarLogObjetoVO(configuracaoSistemaCRMVO, configuracaoSistemaCRMVO.getCodigo(), "CONFIGURACAOSISTEMACRM", 0);
            getConfiguracaoSistemaCRMVO().registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONFIGURACAOSISTEMACRM", configuracaoSistemaCRMVO.getCodigo(), "ERRO AO GERAR LOG DE CONFIGURACAO CRM", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
	 * Rotina responsavel por executar as consultas disponiveis no JSP
	 * ConfiguracaoSistemaCRMCons.jsp. Define o tipo de consulta a ser
	 * executada, por meio de ComboBox denominado campoConsulta, disponivel
	 * neste mesmo JSP. Como resultado, disponibiliza um List com os objetos
	 * selecionados na sessao da pagina.
	 */
	public String consultar() {
		try {
			super.consultar();
			List objs = new ArrayList();
			if (getControleConsulta().getCampoConsulta().equals("codigo")) {
				if (getControleConsulta().getValorConsulta().equals("")) {
					getControleConsulta().setValorConsulta("0");
				}
				int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
				objs = getFacade().getConfiguracaoSistemaCRM().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
			}
			setListaConsulta(objs);
			setMensagemID("msg_dados_consultados");
			return "consultar";
		} catch (Exception e) {
			setListaConsulta(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "consultar";
		}
	}

	/**
	 * Operação responsável por processar a exclusão um objeto da classe
	 * <code>ConfiguracaoSistemaCRMVO</code> Após a exclusão ela automaticamente
	 * aciona a rotina para uma nova inclusão.
	 */
	public String excluir() {
		try {
			getFacade().getConfiguracaoSistemaCRM().excluir(configuracaoSistemaCRMVO);
			novo();
			setMensagemID("msg_dados_excluidos");
			return "editar";
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "editar";
		}
	}
	/*
	 * Método responsável por adicionar um novo objeto da classe
	 * <code>ConfiguracaoDiasPosVenda</code> para o objeto
	 * <code>configuracaoSistemaCRMVO</code> da classe
	 * <code>ConfiguracaoSistemaCRM</code>
	 */
	public void adicionarConfiguracaoDiasPosVenda() throws Exception {
		try {
                    setMsgAlert("");
			if (!getConfiguracaoSistemaCRMVO().getCodigo().equals(new Integer(0))) {
				configuracaoDiasPosVendaVO.setConfiguracaoSistemaCRM(getConfiguracaoSistemaCRMVO());
			}
			getConfiguracaoSistemaCRMVO().adicionarObjConfiguracaoDiasPosVendaVOs(getConfiguracaoDiasPosVendaVO());
			getConfiguracaoSistemaCRMVO().setConfiguracaoDiasPosVendaVOs(Ordenacao.ordenarLista(getConfiguracaoSistemaCRMVO().getConfiguracaoDiasPosVendaVOs(), "nrDia"));
			this.setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVendaVO());
			setMensagemID("msg_dados_adicionados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
                        montarMsgAlert(e.getMessage());
                        this.setConfiguracaoDiasPosVendaVO(new ConfiguracaoDiasPosVendaVO());
		}
	}
	public void adicionarFaixa() throws Exception {
		try {
			setMsgAlert("");
			validarAdicaoFaixa(getFaixaAcesso());
			adicionarObjFaixaHorario(getFaixaAcesso());
			getFaixaAcesso().setEdicao(Boolean.FALSE);
			setFaixaAcesso(new FaixaHorarioAcessoClienteVO());
			setMensagemID("msg_dados_adicionados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setMensagem("msg_erro");
			montarMsgAlert(e.getMessage());
		}
	}

	public void adicionarEmail() throws Exception {
		try {
			if(!UteisValidacao.validaEmail(getEmailFechamento().getEmail())){
				throw new ConsistirException("E-mail inválido.");
			}
			if (UteisValidacao.emptyNumber(getEmailFechamento().getEmpresa().getCodigo())) {
				throw new ConsistirException("Selecione uma empresa.");
			}
			getEmailFechamento().setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(getEmailFechamento().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
			getEmailsFechamento().add(getEmailFechamento());
			setEmailFechamento(new ConfiguracaoEmailFechamentoMetaVO());
			setMensagemID("msg_dados_adicionados");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	public void adicionarTermoSpam() {
		try {
			limparMsg();
			setSucesso(false);
			setErro(false);
			if(!UteisValidacao.emptyString(termoSpam) && !getConfiguracaoSistemaCRMVO().getTermosSpam().contains(termoSpam)){
				getConfiguracaoSistemaCRMVO().getTermosSpam().add(termoSpam);
				setTermoSpam("");
				setMensagemID("msg_dados_adicionados");
				setSucesso(true);
				setErro(false);
			}
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

	 /**
	 * author: alcides
	 * 05/10/2011
	 */
	public void adicionarObjFaixaHorario(FaixaHorarioAcessoClienteVO obj) throws Exception {
		int index = 0;
		if(obj.getEdicao()){
			Iterator<FaixaHorarioAcessoClienteVO> i = this.getFaixasPeriodoAcesso().iterator();
			while (i.hasNext()) {
				FaixaHorarioAcessoClienteVO objExistente = i.next();
				if (objExistente.getEdicao()) {
					obj.setEdicao(Boolean.FALSE);
					getFaixasPeriodoAcesso().set(index, obj);
					return;
				}
				index++;
			}
		}
		if(obj.getCodigo().equals(0)){
			getFaixasPeriodoAcesso().add((FaixaHorarioAcessoClienteVO) obj.getClone(true));
			return;
		}
		Iterator<FaixaHorarioAcessoClienteVO> i = this.getFaixasPeriodoAcesso().iterator();
		while (i.hasNext()) {
			FaixaHorarioAcessoClienteVO objExistente = i.next();
			if (objExistente.getCodigo().equals(obj.getCodigo())) {
				getFaixasPeriodoAcesso().set(index, obj);
				return;
			}
			index++;
		}
		getFaixasPeriodoAcesso().add(obj);
	}

	private void validarAdicaoFaixa(FaixaHorarioAcessoClienteVO faixa) throws Exception {

		try{
			UteisValidacao.validarHoraMinutos(faixa.getHoraInicial(), "Horário Inicial inválido.");
			UteisValidacao.validarHoraMinutos(faixa.getHoraFinal(), "Horário Final inválido.");
		}
		catch (Exception e){
			throw new ConsistirException(e.getMessage());
		}

		Date horaInicial = Formatador.obterHorario(faixa.getHoraInicial());
		Date horaFinal = Formatador.obterHorario(faixa.getHoraFinal());

		for (FaixaHorarioAcessoClienteVO obj : getFaixasPeriodoAcesso()) {
			if (!obj.getEdicao()) {
                                if (obj.getNomePeriodo().equals(faixa.getNomePeriodo())){
                                    throw new ConsistirException("Já existe período com esse nome!");
                                }
				Date objHoraInicial = Formatador.obterHorario(obj.getHoraInicial());
				Date objHoraFinal = Formatador.obterHorario(obj.getHoraFinal());

				Date auxHoraInicial = null;
				Date auxHoraFinal = null;

				if (horaInicial.equals(objHoraInicial) || horaInicial.equals(objHoraFinal) || horaFinal.equals(objHoraInicial)
						|| horaFinal.equals(objHoraFinal)) {
					throw new ConsistirException("Não é permitido a adição de faixas de horário concomitantes.");
				}

				if (objHoraFinal.before(objHoraInicial)) {
					objHoraFinal = Formatador.obterHorario("23:59");
					auxHoraInicial = Formatador.obterHorario("00:00");
					auxHoraFinal = Formatador.obterHorario(obj.getHoraFinal());

					if ((horaInicial.after(objHoraInicial) && horaInicial.before(objHoraFinal))
							|| (horaFinal.after(objHoraInicial) && horaFinal.before(objHoraFinal))
							|| (horaInicial.after(auxHoraInicial) && horaInicial.before(auxHoraFinal))
							|| (horaFinal.after(auxHoraInicial) && horaFinal.before(auxHoraFinal))) {
						throw new ConsistirException("Não é permitido a adição de faixas de horário concomitantes.");
					}
					if (horaFinal.before(horaInicial)){
						horaFinal = Uteis.somarCampoData(horaFinal, Calendar.DAY_OF_MONTH, 1);
					}
					auxHoraFinal = Uteis.somarCampoData(auxHoraFinal, Calendar.DAY_OF_MONTH, 1);
					if(objHoraInicial.after(horaInicial) && auxHoraFinal.before(horaFinal)){
						throw new ConsistirException("Não é permitido a adição de faixas de horário concomitantes.");
					}
				} else

				if ((horaInicial.after(objHoraInicial) && horaInicial.before(objHoraFinal))
						|| (horaFinal.after(objHoraInicial) && horaFinal.before(objHoraFinal))
						|| (objHoraInicial.after(horaInicial) && objHoraFinal.before(horaFinal))) {
					throw new ConsistirException("Não é permitido a adição de faixas de horário concomitantes.");
				}
			}
		}
	}


	/*
	 * Método responsável por disponibilizar dados de um objeto da classe
	 * <code>ConfiguracaoDiasPosVenda</code> para edição pelo usuário.
	 */
	public String editarConfiguracaoDiasPosVenda() {
		ConfiguracaoDiasPosVendaVO obj = (ConfiguracaoDiasPosVendaVO) context().getExternalContext().getRequestMap().get("configuracaoDiasPosVenda");
		setConfiguracaoDiasPosVendaVO(obj);
		limparMsg();
		setSucesso(true);
		setErro(false);
		return "editar";
	}

    public String editarFaixa() {
		FaixaHorarioAcessoClienteVO obj = (FaixaHorarioAcessoClienteVO) context().getExternalContext().getRequestMap().get("faixa");
		setFaixaAcesso(new FaixaHorarioAcessoClienteVO());
		getFaixaAcesso().setCodigo(obj.getCodigo());
		getFaixaAcesso().setNomePeriodo(obj.getNomePeriodo());
		getFaixaAcesso().setHoraFinal(obj.getHoraFinal());
		getFaixaAcesso().setHoraInicial(obj.getHoraInicial());
		getFaixaAcesso().setEdicao(Boolean.TRUE);
        getFaixaAcesso().registrarObjetoVOAntesDaAlteracao();
        getFaixaAcesso().setNovoObj(false);
		obj.setEdicao(Boolean.TRUE);
		limparMsg();
		setMensagemID("msg_dados_editar");
		setSucesso(true);
		setErro(false);
		return "editar";
	}

	/*
	 * Método responsável por remover um novo objeto da classe
	 * <code>ConfiguracaoDiasPosVenda</code> do objeto
	 * <code>configuracaoSistemaCRMVO</code> da classe
	 * <code>ConfiguracaoSistemaCRM</code>
	 */
	public String removerConfiguracaoDiasPosVenda() throws Exception {
		try {
			ConfiguracaoDiasPosVendaVO obj = (ConfiguracaoDiasPosVendaVO) context().getExternalContext().getRequestMap().get("configuracaoDiasPosVenda");
			if (!getFacade().getFecharMetaDetalhado().existeMetaConfiguracaoDiasPosVenda(obj.getCodigo())) {
				getConfiguracaoSistemaCRMVO().excluirObjConfiguracaoDiasPosVendaVOs(obj.getNrDia());
			} else {
				throw new ConsistirException("Configuração já está associada a alguma meta gerada, por isso não pode ser excluída, apenas inativada!");
			}
			setMensagemID("msg_dados_excluidos");
			setSucesso(true);
			setErro(false);
		} catch (Exception e) {
			montarErro(e);
			setMensagemDetalhada("msg_erro", e.getMessage());
			setSucesso(false);
			setErro(true);
		}
		return "editar";
	}
	public String removerFaixas() {
		FaixaHorarioAcessoClienteVO obj = (FaixaHorarioAcessoClienteVO) context().getExternalContext().getRequestMap().get("faixa");
		getFaixasPeriodoAcesso().remove(obj);
		setMensagemID("msg_dados_adicionados");
		setSucesso(true);
		setErro(false);
		return "editar";
	}

	public String removerEmail() {
		ConfiguracaoEmailFechamentoMetaVO obj = (ConfiguracaoEmailFechamentoMetaVO) context().getExternalContext().getRequestMap().get("email");
		getEmailsFechamento().remove(obj);
		setMensagemID("msg_dados_adicionados");
		setSucesso(true);
		setErro(false);
		return "editar";
	}

	public void removerTermo() {
		String obj = (String) context().getExternalContext().getRequestMap().get("termo");
		getConfiguracaoSistemaCRMVO().getTermosSpam().remove(obj);
		setMensagemID("msg_dados_adicionados");
		setSucesso(true);
		setErro(false);
	}

	public void testarEnvioEmail() {
		try {
			UteisEmail uteis = new UteisEmail();
			uteis.novo(getNomeEmpresaLogada() , getConfiguracaoSistemaCRMVO());
			uteis.enviarEmail(getEmailTeste(), "Teste de Email", "Este é um teste de email pelo ZillyonWeb.", getNomeEmpresaLogada());
			setMensagemID("msg_email_enviado");
			setSucesso(true);
			setErro(false);
        } catch (Exception e) {
            if (e.getMessage().toLowerCase().contains("daily message limit")) {
                setMensagemDetalhada("msg_erro", "Infelizmente seu limite de mensagens diário foi atingido, por favor troque de conta ou espere até amanhã.");
            } else {
                setMensagemDetalhada("msg_erro", "Erro no Envio de Email. Verifique as Configurações e Tente Novamente: " + e.getMessage());
            }
        }
    }

	public boolean isConfigAvancadas() {
		return configuracaoAvancada;
	}

	public void setConfiguracaoAvancada(boolean configuracaoAvancada) {
		this.configuracaoAvancada = configuracaoAvancada;
	}

	public void healtsCheckSendy() {
		SendyImpl sendyImpl = new SendyImpl();
		setJsonHealthCheckSendy(sendyImpl.healthCheck()
				.replaceAll(",", ",\n")
				.replaceAll("Retornado:", "Retornado:\n"));
	}

    public void configurarEmailAtigo() {
		try {
			setMsgAlert("");
			limparMsg();

			if (!UteisValidacao.validaEmail(getConfiguracaoSistemaCRMVO().getEmailPadrao().trim())) {
				throw new Exception("Informe um email padrão válido.");
			}
			if (!UteisValidacao.validaEmail(getEmailTeste().trim())) {
				throw new Exception("Informe um email válido para teste.");
			}

			if (configuracaoSistemaCRMVO.getIntegracaoPacto()) {
				criaBrandSendy();
			}
			UteisEmail uteis = new UteisEmail();
			uteis.novo(getNomeEmpresaLogada(), getConfiguracaoSistemaCRMVO());
			uteis.configurarMail(getEmailTeste(), "Este é um teste de email pelo ZillyonWeb.", configuracaoSistemaCRMVO.getRemetentePadrao(), configuracaoSistemaCRMVO.getIntegracaoPacto(), configuracaoSistemaCRMVO.preparaEnvioSendy());
			getConfiguracaoSistemaCRMVO().setMailServer(uteis.smtpPadrao);
			getConfiguracaoSistemaCRMVO().setIniciarTLS(uteis.iniciarTLS);
			getConfiguracaoSistemaCRMVO().setConexaoSegura(uteis.conexaoSegura);
			getConfiguracaoSistemaCRMVO().setUsaSMTPS(uteis.usaSmtps);
			getConfiguracaoSistemaCRMVO().setPortaServer(uteis.portaPadrao);
			getConfiguracaoSistemaCRMVO().setLogin(uteis.loginServidorSmtp);
			getConfiguracaoSistemaCRMVO().setSenha(uteis.senhaServidorSmtp);
			getFacade().getConfiguracaoSistemaCRM().incluirConfiguracoesEmail(configuracaoSistemaCRMVO);
			montarSucesso("msg_email_enviado");
		} catch (Exception e) {
			montarErro(e);
			if (e.getMessage().toLowerCase().contains("daily message limit")) {
				setMensagemDetalhada("msg_erro", "Infelizmente seu limite de mensagens diário foi atingido, por favor troque de conta ou espere até amanhã.");
			} else if (!e.getMessage().contains("Informe um email")){
				setMensagemDetalhada("msg_erro", "Erro no Envio de Email. Verifique as Configurações Avançadas e Tente Novamente: " + e.getMessage());
			}
		}
		setMsgAlert(getMensagemNotificar(true));
    }

	public void configurarEmail() {
		try {
			setMsgAlert("");
			limparMsg();

			if (!UteisValidacao.validaEmail(getConfiguracaoSistemaCRMVO().getEmailPadrao().trim())) {
				throw new Exception("Informe um email padrão válido.");
			}
			if (!UteisValidacao.validaEmail(getEmailTeste().trim())) {
				throw new Exception("Informe um email válido para teste.");
			}
			validaUsuario();
			if (configuracaoSistemaCRMVO.getIntegracaoPacto()) {
				criaBrandSendy();
			}

			if(getConfiguracaoSistemaCRMVO().getUsarRemetentePadraoGeral()){
				getConfiguracaoSistemaCRMVO().setRemetentePadrao(getConfiguracaoSistemaCRMVO().getRemetentePadrao());
			}
			else{
				getConfiguracaoSistemaCRMVO().setRemetentePadrao(getUsuarioLogado().getNome());
			}
			ConfiguracaoSistemaCRMVO configEmailVO = getFacade().getConfiguracaoSistemaCRM().obterConfiguracaoEmailAutomatico(getEmailTeste(), getNomeEmpresaLogada(), getConfiguracaoSistemaCRMVO());
			if (configEmailVO == null) {
				throw new Exception("Não foi possível obter as configurações. Verifique as Configurações Avançadas e Tente Novamente");
			}
			getConfiguracaoSistemaCRMVO().setMailServer(configEmailVO.getMailServer());
			getConfiguracaoSistemaCRMVO().setIniciarTLS(configEmailVO.isIniciarTLS());
			getConfiguracaoSistemaCRMVO().setConexaoSegura(configEmailVO.isConexaoSegura());
			getConfiguracaoSistemaCRMVO().setUsaSMTPS(configEmailVO.getUsaSMTPS());
			getConfiguracaoSistemaCRMVO().setUsaConfiguracaoEmailManual(configEmailVO.getUsaConfiguracaoEmailManual());
			getConfiguracaoSistemaCRMVO().setPortaServer(configEmailVO.getPortaServer());
			getConfiguracaoSistemaCRMVO().setLogin(configEmailVO.getLogin());
			getConfiguracaoSistemaCRMVO().setSenha(configEmailVO.getSenha());


			getFacade().getConfiguracaoSistemaCRM().incluirConfiguracoesEmail(configuracaoSistemaCRMVO);
			montarSucessoGrowl("Configuração realizada. Email enviado com sucesso.");
		} catch (Exception e) {
			montarErro(e);
			if (e.getMessage().toLowerCase().contains("daily message limit")) {
				setMensagemDetalhada("msg_erro", "Infelizmente seu limite de mensagens diário foi atingido, por favor troque de conta ou espere até amanhã.");
			} else if (!e.getMessage().contains("Informe um email")){
				setMensagemDetalhada("msg_erro", "Erro no Envio de Email. Verifique as Configurações Avançadas e Tente Novamente: " + e.getMessage());
			}
		}
		setMsgAlert(getMensagemNotificar(true));
	}


	public void validaUsuario() throws Exception {
		ArrayList<UsuarioVO> result = (new ArrayList<UsuarioVO>());
		if(!configuracaoSistemaCRMVO.getRemetentePadrao().equals("")) {
			result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(configuracaoSistemaCRMVO.getRemetentePadrao(), getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
		}
		if(result.size() == 0)  {
			configuracaoSistemaCRMVO.setRemetentePadrao("");
			throw new Exception("Remetente padrão não encontrado!");
		}
	}

	public List<UsuarioVO> executarAutocompleteRemetente(Object suggest) {
		String pref = (String) suggest;
		ArrayList<UsuarioVO> result;
		try {
			if (pref.equals("%")) {
				result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarTodosUsuarioComLimite(false, Uteis.NIVELMONTARDADOS_TELACONSULTA, getEmpresaLogado().getCodigo());
			} else {
				result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
			}

		} catch (Exception ex) {
			result = (new ArrayList<UsuarioVO>());
		}
		return result;
	}

	public void setarRemetente() {
		UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("result");
		if (obj != null) {
			getConfiguracaoSistemaCRMVO ().setRemetentePadrao(obj.getNome());
		}
	}

	public void setarRemetentePadrao() throws Exception {
		 if(!configuracaoSistemaCRMVO.getUsarRemetentePadraoGeral()){
			getConfiguracaoSistemaCRMVO ().setRemetentePadrao(getUsuarioLogado().getNome());
		 }else {
			 getConfiguracaoSistemaCRMVO ().setRemetentePadrao("");
		 }
	}

    /**
	 * Rotina responsável por atribui um javascript com o método de mascara para
	 * campos do tipo Data, CPF, CNPJ, etc.
	 */
	public String getMascaraConsulta() {
		return "";
	}

	/**
	 * Rotina responsável por preencher a combo de consulta da telas.
	 */
	public List getTipoConsultaCombo() {
		List<SelectItem> itens = new ArrayList<>();
		itens.add(new SelectItem("codigo", "Código"));
		return itens;
	}

	/**
	 * Rotina responsável por organizar a paginação entre as páginas resultantes
	 * de uma consulta.
	 */
	public String inicializarConsultar() {
		setListaConsulta(new ArrayList());
		setMensagemID("msg_entre_prmconsulta");
		return "consultar";
	}

	/**
	 * Operação que libera todos os recursos (atributos, listas, objetos) do
	 * backing bean. Garantindo uma melhor atuação do Garbage Coletor do Java. A
	 * mesma é automaticamente quando realiza o logout.
	 */
	protected void limparRecursosMemoria() {
		super.limparRecursosMemoria();
		configuracaoSistemaCRMVO = null;
		configuracaoDiasPosVendaVO = null;
	}

	/**
	 * Operação que inicializa as Interfaces Façades com os respectivos objetos
	 * de persistência dos dados no banco de dados.
	 */
	protected boolean inicializarFacades() {
		try {
			super.inicializarFacades();

			return true;
		} catch (Exception e) {
			setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
			return false;
		}
	}

    public List executarAutocompletePesqProduto(Object suggest) {
        List produtoVOs = null;
        try {
            String nomePesq = (String) suggest;
            produtoVOs = getFacade().getProduto().consultarPorDescricaoTipoProduto(nomePesq, "SS", false, Uteis.NIVELMONTARDADOS_MINIMOS);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return produtoVOs;
    }

    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");
        setProduto(obj);
        getConfiguracaoDiasMetas().setProduto(getProduto());
    }

	public ConfiguracaoDiasPosVendaVO getConfiguracaoDiasPosVendaVO() {
		return configuracaoDiasPosVendaVO;
	}

	public void setConfiguracaoDiasPosVendaVO(ConfiguracaoDiasPosVendaVO configuracaoDiasPosVendaVO) {
		this.configuracaoDiasPosVendaVO = configuracaoDiasPosVendaVO;
	}

	public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
		return configuracaoSistemaCRMVO;
	}

	public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
		this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
	}

    public String getEmailTeste() {
		if (emailTeste == null) {
			emailTeste = "";
		}
        return emailTeste;
    }

    public void setEmailTeste(String emailTeste) {
        this.emailTeste = emailTeste;
    }

	public void setFaixaAcesso(FaixaHorarioAcessoClienteVO faixaAcesso) {
		this.faixaAcesso = faixaAcesso;
	}

	public FaixaHorarioAcessoClienteVO getFaixaAcesso() {
		if(faixaAcesso == null){
			faixaAcesso = new FaixaHorarioAcessoClienteVO();
		}
		return faixaAcesso;
	}

	public void setFaixasPeriodoAcesso(List<FaixaHorarioAcessoClienteVO> faixasPeriodoAcesso) {
		this.faixasPeriodoAcesso = faixasPeriodoAcesso;
	}

	public List<FaixaHorarioAcessoClienteVO> getFaixasPeriodoAcesso() {
		if(faixasPeriodoAcesso == null){
			faixasPeriodoAcesso = new ArrayList<>();
		}
		return faixasPeriodoAcesso;
	}

	public void setEmailsFechamento(List<ConfiguracaoEmailFechamentoMetaVO> emailsFechamento) {
		this.emailsFechamento = emailsFechamento;
	}

	public List<ConfiguracaoEmailFechamentoMetaVO> getEmailsFechamento() {
		if(emailsFechamento == null){
			emailsFechamento = new ArrayList<>();
		}
		return emailsFechamento;
	}

	public void setEmailFechamento(ConfiguracaoEmailFechamentoMetaVO emailFechamento) {
		this.emailFechamento = emailFechamento;
	}

	public ConfiguracaoEmailFechamentoMetaVO getEmailFechamento() {
		if(emailFechamento == null){
			emailFechamento = new ConfiguracaoEmailFechamentoMetaVO();
		}
		return emailFechamento;
	}

	public void setSenhaEmail(String senhaEmail) {
		this.senhaEmail = senhaEmail;
	}

	public String getSenhaEmail() {
		return senhaEmail;
	}

	public void setTiposVinculosFase(List<TiposVinculosFaseVO> tiposVinculosFase) {
		this.tiposVinculosFase = tiposVinculosFase;
	}

	public List<TiposVinculosFaseVO> getTiposVinculosFase() {
		return tiposVinculosFase;
	}

	public void setTermoSpam(String termoSpam) {
		this.termoSpam = termoSpam;
	}

	public String getTermoSpam() {
		return termoSpam;
	}

	public void setFaseEstudioCodigo(Integer faseEstudioCodigo) {
		this.faseEstudioCodigo = faseEstudioCodigo;
	}

	public Integer getFaseEstudioCodigo() {
		return faseEstudioCodigo;
	}

	public void setConfiguracaoDiasMetas(ConfiguracaoDiasMetasTO configuracaoDiasMetas) {
		this.configuracaoDiasMetas = configuracaoDiasMetas;
	}

	public ConfiguracaoDiasMetasTO getConfiguracaoDiasMetas() {
		return configuracaoDiasMetas;
	}

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public void realizarConsultaLogCRM() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = configuracaoSistemaCRMVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
				configuracaoSistemaCRMVO.getCodigo(), 0);
    }

	public void montarListaFasesCRM() {
		String[] codOrdenacaoMetas = getConfiguracaoSistemaCRMVO().getOrdenacaoMetas().split(",");
		List<FasesCRMEnum> objs = new ArrayList<>();
		int posicao = 0;
		List<String> lista = new ArrayList<>();
		boolean estudio = false;
		LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
		List<ModuloAberto> modulosHabilitados = loginControle.getModulosHabilitados();
		for (ModuloAberto modulo : modulosHabilitados){
			if(modulo.getSigla().equals("EST")){
				estudio = true;
			}
		}
		for(String sigla : codOrdenacaoMetas){
			if(!sigla.equals("CI")
					&& !sigla.equals("CV")
					&& !sigla.equals("CE")
					&& !sigla.equals("CA")
					&& !sigla.equals("CD")
					&& !sigla.equals("CT")
					&& !sigla.equals("CL")){
				if(!estudio){
					if(!sigla.equals("SF") && !sigla.equals("SA")){
						lista.add(sigla);
					}
				}
				else {
					lista.add(sigla);
				}
			}
		}

		for (String fase : lista) {
			FasesCRMEnum faseMeta = FasesCRMEnum.getFasePorSigla(fase);
			faseMeta.setPosicao(posicao);
			objs.add(faseMeta);
			posicao++;
		}
		setListaOrdenacaoMetas(Ordenacao.ordenarLista(objs, "posicao"));
	}

	public void moverMetaParaCima() {
		try {
			FasesCRMEnum obj = (FasesCRMEnum) context().getExternalContext().getRequestMap().get("ordenacaoMetas");
			for (FasesCRMEnum fase : FasesCRMEnum.values()) {
				if (!fase.equals(FasesCRMEnum.CONVERSAO_INDICADOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_AGENDADOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_PASSIVO)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_DESISTENTES)
						&& !fase.equals(FasesCRMEnum.INDICACOES_SEM_CONTATO)
						&& !fase.equals(FasesCRMEnum.PASSIVO)) {
					if (obj.getPosicao() == fase.getPosicao() + 1) {
						fase.setPosicao(fase.getPosicao() + 1);
						obj.setPosicao(obj.getPosicao() - 1);
						break;
					}
				}
			}
			setListaOrdenacaoMetas(Ordenacao.ordenarLista(getListaOrdenacaoMetas(), "posicao"));
		} catch (Exception e) {
			montarErro("Erro ao ordenar meta");
		}
	}

	public void moverMetaParaBaixo() {
		try {
			FasesCRMEnum obj = (FasesCRMEnum) context().getExternalContext().getRequestMap().get("ordenacaoMetas");
			for (FasesCRMEnum fase : FasesCRMEnum.values()) {
				if (!fase.equals(FasesCRMEnum.CONVERSAO_INDICADOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_AGENDADOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_PASSIVO)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS)
						&& !fase.equals(FasesCRMEnum.CONVERSAO_DESISTENTES)
						&& !fase.equals(FasesCRMEnum.INDICACOES_SEM_CONTATO)
						&& !fase.equals(FasesCRMEnum.PASSIVO)) {
					System.out.println(fase.getDescricao());
					if (fase.getPosicao() == obj.getPosicao() + 1) {
						fase.setPosicao(fase.getPosicao() - 1);
						obj.setPosicao(obj.getPosicao() + 1);
						break;
					}
				}
			}
			setListaOrdenacaoMetas(Ordenacao.ordenarLista(getListaOrdenacaoMetas(), "posicao"));
		} catch (Exception e) {
			montarErro("Erro ao ordenar meta");
		}
	}

	public void gravarOrdenacaoMetas() {
		List<FasesCRMEnum> fasesCRMEnums = Ordenacao.ordenarLista(getListaOrdenacaoMetas(), "posicao");
		StringBuilder ordenacaoNova = new StringBuilder();
		for (FasesCRMEnum fase : fasesCRMEnums) {
			ordenacaoNova.append("," + fase.getSigla());
		}
		String ordenacaoNovaStr = ordenacaoNova.toString().replaceFirst(",", "");
		configuracaoSistemaCRMVO.setOrdenacaoMetas(ordenacaoNovaStr);
	}

	public void restaurarPadraoOrdenacaoMetas() {
		configuracaoSistemaCRMVO.setOrdenacaoMetas("AG,AL,LA,HO,RE,PE,VA,EX,IN,RI,VE,PV,FA,AN,SF,SA,CR,UG");
		montarListaFasesCRM();
	}


    private void registraLogAlteracaoListasConfiguracao(List<ConfiguracaoEmailFechamentoMetaVO> emailsFechamentoant, List<TiposVinculosFaseVO> tiposVinculosFaseAnt, List<FaixaHorarioAcessoClienteVO> faixasPeriodoAcessoAnt, List<ConfiguracaoDiasPosVendaVO> configuracaoDiasPosVendaAnt, List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasExAlunosAnt, List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSessoesFinaisAnt, List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasSemAgendamentoAnt, List<ConfiguracaoDiasMetasTO> configuracaoDiasMetasVisitantesAntigosAnt) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(getConfiguracaoSistemaCRMVO().getCodigo().toString());
            obj.setNomeEntidade("CONFIGURACAOSISTEMACRM");
            obj.setNomeEntidadeDescricao("CONFIGURACAOSISTEMACRM - CONFIGURACAOSISTEMACRMVO");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
            obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");
            obj.setValorCampoAnterior("");


            List<String> alterados = new ArrayList<>();
            List<String> excluidos = new ArrayList<>();
            List<String> adicionados = new ArrayList<>();

            for (ConfiguracaoEmailFechamentoMetaVO emailAnt : emailsFechamentoant) {
                boolean existe = false;

                for (ConfiguracaoEmailFechamentoMetaVO email : getEmailsFechamento()) {
                    if (emailAnt.getEmpresa().getCodigo().equals(email.getEmpresa().getCodigo()) && emailAnt.getEmail().equals(email.getEmail())) {
                        email.setVerificado(true);
                        existe =true;
                        break;
                    }
                }

                if (!existe) {
                    excluidos.add(emailAnt.getEmpresa().getNome() + " -- " + emailAnt.getEmail());
                }
            }

            for (ConfiguracaoEmailFechamentoMetaVO email : getEmailsFechamento()) {
                if (!email.getVerificado()) {
                    adicionados.add(email.getEmpresa().getNome() + " -- " + email.getEmail());
                }
            }

            String str = "";
            String alteradas = "";
            String excluidas = listaAlteracaoLog(excluidos);
            String adicionadas = listaAlteracaoLog(adicionados);

            if (!excluidas.isEmpty()) {
                str += "EMAILS EXCLUIDOS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()) {
                str += "EMAILS ADICIONADOS: \n" + adicionadas + "\n";
            }

            //REGISTRANDO OS HORARIOS DE ACESSO AO SISTEMA

            if(getConfiguracaoSistemaCRMVO().getDividirFase()){
                excluidos = new ArrayList<>();
                adicionados = new ArrayList<>();

                for (TiposVinculosFaseVO tipoFaseAnt : tiposVinculosFaseAnt) {
                    boolean existe = false;

                    for (TiposVinculosFaseVO tipoFase : getTiposVinculosFase()) {
                        if (tipoFaseAnt.getFase().equals(tipoFase.getFase())) {
                            existe = true;
                            tipoFase.setVerificado(true);
                            for(TipoColaboradorEnum tipoColaboradorAnt: tipoFaseAnt.getTiposColaborador()){
                                boolean existeTipo = false;
                                for(TipoColaboradorEnum tipoColaborador: tipoFase.getTiposColaborador()){
                                    if (tipoColaboradorAnt.getSigla().equals(tipoColaborador.getSigla())) {
                                        existeTipo = true;
                                        break;
                                    }
                                }
                                if (!existeTipo) {
                                    excluidos.add(tipoFaseAnt.getFase().getDescricao() + " -- " + tipoColaboradorAnt.getDescricao());
                                }
                            }
                            for(TipoColaboradorEnum tipoColaborador: tipoFase.getTiposColaborador()){
                                boolean existeTipo = false;
                                for(TipoColaboradorEnum tipoColaboradorAnt: tipoFaseAnt.getTiposColaborador()){
                                    if (tipoColaboradorAnt.getSigla().equals(tipoColaborador.getSigla())) {
                                        existeTipo = true;
                                        break;
                                    }
                                }
                                if (!existeTipo) {
                                    adicionados.add(tipoFase.getFase().getDescricao() + " -- " + tipoColaborador.getDescricao());
                                }
                            }

                        }
                    }

                    if (!existe) {
                        for(TipoColaboradorEnum tipoColaboradorAnt: tipoFaseAnt.getTiposColaborador()){
                            excluidos.add(tipoFaseAnt.getFase().getDescricao() + " -- " + tipoColaboradorAnt.getDescricao());
                        }
                    }
                }

                for (TiposVinculosFaseVO tipoFase : getTiposVinculosFase()) {
                    if (!tipoFase.isVerificado()) {
                        for(TipoColaboradorEnum tipoColaborador: tipoFase.getTiposColaborador()){
                            adicionados.add(tipoFase.getFase().getDescricao() + " -- " + tipoColaborador.getDescricao());
                        }
                    }
                }

                excluidas = listaAlteracaoLog(excluidos);
                adicionadas = listaAlteracaoLog(adicionados);

               if (!excluidas.isEmpty()) {
                    str += "RESPONSÁVEL FASE EXCLUÍDOS: \n" + excluidas + "\n";
                }
                if (!adicionadas.isEmpty()) {
                    str += "RESPONSÁVEL FASE ADICIONADOS: \n" + adicionadas + "\n";
                }
            }

           // faixas de acesso

            alterados = new ArrayList<>();
            excluidos = new ArrayList<>();
            adicionados = new ArrayList<>();
           for (FaixaHorarioAcessoClienteVO horarioAnt : faixasPeriodoAcessoAnt) {
                boolean existe = false;

                for (FaixaHorarioAcessoClienteVO horario : getFaixasPeriodoAcesso()) {
                    if (horarioAnt.getNomePeriodo().equals(horario.getNomePeriodo())) {
                        existe = true;
                        horario.setVerificado(true);
                        if (!horarioAnt.getHoraInicial().equals(horario.getHoraInicial())) {
                            alterados.add(horario.getNomePeriodo() + " - Hora Inicial de " + horarioAnt.getHoraInicial() + " para " + horario.getHoraInicial());

                        }
                        if (!horarioAnt.getHoraFinal().equals(horario.getHoraFinal())) {
                            alterados.add(horario.getNomePeriodo() + " - Hora Final de " + horarioAnt.getHoraFinal()+ " - " + horario.getHoraFinal());

                        }
                        break;

                    }
                }

                if (!existe) {
                    excluidos.add(horarioAnt.getNomePeriodo() + "  " + horarioAnt.getHoraInicial() + " - " + horarioAnt.getHoraFinal());
                }
            }

            for (FaixaHorarioAcessoClienteVO  horarioAtu : getFaixasPeriodoAcesso()) {
                if (!horarioAtu.isVerificado()) {
                    adicionados.add(horarioAtu.getNomePeriodo() + "  " + horarioAtu.getHoraInicial() + " - " + horarioAtu.getHoraFinal());
                }
            }

            alteradas = listaAlteracaoLog(alterados);
            excluidas = listaAlteracaoLog(excluidos);
            adicionadas = listaAlteracaoLog(adicionados);


            if (!alteradas.isEmpty()){
                str += "FAIXAS DE HORÁRIO ALTERADOS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "FAIXAS DE HORÁRIO EXCLUÍDAS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "FAIXAS DE HORÁRIO ADICIONADAS: \n" + adicionadas + "\n";
            }

            // dias pós venda 

            alterados = new ArrayList<>();
            excluidos = new ArrayList<>();
            adicionados = new ArrayList<>();
           for (ConfiguracaoDiasPosVendaVO posVendaAnt : configuracaoDiasPosVendaAnt) {
                boolean existe = false;

                for (ConfiguracaoDiasPosVendaVO posVenda : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasPosVendaVOs()) {
                    if (posVendaAnt.getNrDia().equals(posVenda.getNrDia())) {
                        existe = true;
                        posVenda.setVerificado(true);
                        if (!posVendaAnt.getDescricao().equals(posVenda.getDescricao())) {
                            alterados.add(posVenda.getDescricao()+ " - Descrição de " + posVendaAnt.getDescricao() + " para " + posVenda.getDescricao());

                        }
                        if ((posVendaAnt.isAtivo() ^ posVenda.isAtivo())) {
                            alterados.add(posVenda.getDescricao()+ " - Situação de " + (posVendaAnt.isAtivo() ? "ativo" : "inativo") + " para " + (posVenda.isAtivo() ? "ativo" : "inativo"));
                        }
						if (!posVendaAnt.getSiglaResponsavelPeloContato().equals(posVenda.getSiglaResponsavelPeloContato())){
							alterados.add(posVenda.getDescricao()+ " - Responsável pelo contato de " + posVendaAnt.getSiglaResponsavelPeloContato() + " para " + posVenda.getSiglaResponsavelPeloContato());
						}
                        break;
                    }
                }

                if (!existe) {
                    excluidos.add(posVendaAnt.getDescricao()+ " - " + posVendaAnt.getNrDia() + " dias - " + (posVendaAnt.isAtivo() ? "ativo" : "inativo"));
                }
            }

            for (ConfiguracaoDiasPosVendaVO posVenda : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasPosVendaVOs()) {
                if (!posVenda.isVerificado()) {
                    adicionados.add(posVenda.getDescricao()+ " - " + posVenda.getNrDia() + " dias - " + (posVenda.isAtivo() ? "ativo" : "inativo"));
                }
            }

            alteradas = listaAlteracaoLog(alterados);
            excluidas = listaAlteracaoLog(excluidos);
            adicionadas = listaAlteracaoLog(adicionados);


            if (!alteradas.isEmpty()){
                str += "PÓS VENDAS ALTERADOS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "PÓS VENDAS EXCLUÍDOS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "PÓS VENDAS ADICIONADOS: \n" + adicionadas + "\n";
            }

            // dias visitantes Antigos

            alterados = new ArrayList<>();
            excluidos = new ArrayList<>();
            adicionados = new ArrayList<>();
           for (ConfiguracaoDiasMetasTO visitantesAnt : configuracaoDiasMetasVisitantesAntigosAnt) {
                boolean existe = false;

                for (ConfiguracaoDiasMetasTO visitantes : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasVisitantesAntigos()) {
                    if (visitantes.getNrDia().equals(visitantesAnt.getNrDia())) {
                        existe = true;
                        visitantes.setVerificado(true);
                        if (!visitantesAnt.getDescricao().equals(visitantes.getDescricao())) {
                            alterados.add(visitantes.getNrDia()+ " Dia(s) - Descricao de " + visitantesAnt.getDescricao() + " para "+ visitantes.getDescricao());
                        }
                        break;
                    }
                }

                if (!existe) {
                    excluidos.add(visitantesAnt.getDescricao()+ " - " + visitantesAnt.getNrDia() + " dias");
                }
            }

            for (ConfiguracaoDiasMetasTO visitantes : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasVisitantesAntigos()) {
                if (!visitantes.isVerificado()) {
                    adicionados.add(visitantes.getDescricao()+ " - " + visitantes.getNrDia() + " dias");
                }
            }

            alteradas = listaAlteracaoLog(alterados);
            excluidas = listaAlteracaoLog(excluidos);
            adicionadas = listaAlteracaoLog(adicionados);


            if (!alteradas.isEmpty()){
                str += "VISITANTES ANTIGOS ALTERADOS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "VISITANTES ANTIGOS EXCLUÍDOS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "VISITANTES ANTIGOS ADICIONADOS: \n" + adicionadas + "\n";
            }

                        // dias ex-alunos

            alterados = new ArrayList<>();
            excluidos = new ArrayList<>();
            adicionados = new ArrayList<>();
           for (ConfiguracaoDiasMetasTO exAlunosAnt : configuracaoDiasMetasExAlunosAnt) {
                boolean existe = false;

                for (ConfiguracaoDiasMetasTO exAlunos : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasExAlunos()) {
                    if (exAlunosAnt.getNrDia().equals(exAlunos.getNrDia())) {
                        existe = true;
                        exAlunos.setVerificado(true);
                        if (!exAlunosAnt.getDescricao().equals(exAlunos.getDescricao())) {
                            alterados.add(exAlunos.getNrDia()+ " Dia(s) - Descricao de " + exAlunosAnt.getDescricao() + " para "+ exAlunos.getDescricao());
                        }
                        break;
                    }
                }

                if (!existe) {
                    excluidos.add(exAlunosAnt.getDescricao()+ " - " + exAlunosAnt.getNrDia() + " dias");
                }
            }

            for (ConfiguracaoDiasMetasTO exAlunos : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasExAlunos()) {
                if (!exAlunos.isVerificado()) {
                    adicionados.add(exAlunos.getDescricao()+ " - " + exAlunos.getNrDia() + " dias");
                }
            }

            alteradas = listaAlteracaoLog(alterados);
            excluidas = listaAlteracaoLog(excluidos);
            adicionadas = listaAlteracaoLog(adicionados);


            if (!alteradas.isEmpty()){
                str += "EX-ALUNOS ALTERADOS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "EX-ALUNOS EXCLUÍDOS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "EX-ALUNOS ADICIONADOS: \n" + adicionadas + "\n";
            }

           // dias ultimas sessoes

            alterados = new ArrayList<>();
            excluidos = new ArrayList<>();
            adicionados = new ArrayList<>();
           for (ConfiguracaoDiasMetasTO sessoesAnt : configuracaoDiasMetasSessoesFinaisAnt) {
                boolean existe = false;

                for (ConfiguracaoDiasMetasTO sessoes : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSessoesFinais()) {
                    if (sessoesAnt.getNrDia().equals(sessoes.getNrDia())) {
                        existe = true;
                        sessoes.setVerificado(true);
                        if (!sessoesAnt.getDescricao().equals(sessoes.getDescricao())) {
                            alterados.add(sessoes.getNrDia()+ " Dia(s) - Descricao de " + sessoesAnt.getDescricao() + " para "+ sessoes.getDescricao());
                        }
                        break;
                    }
                }

                if (!existe) {
                    excluidos.add(sessoesAnt.getDescricao()+ " - " + sessoesAnt.getNrDia() + " dias");
                }
            }

            for (ConfiguracaoDiasMetasTO sessoes : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSessoesFinais()) {
                if (!sessoes.isVerificado()) {
                    adicionados.add(sessoes.getDescricao()+ " - " + sessoes.getNrDia() + " dias");
                }
            }

            alteradas = listaAlteracaoLog(alterados);
            excluidas = listaAlteracaoLog(excluidos);
            adicionadas = listaAlteracaoLog(adicionados);


            if (!alteradas.isEmpty()){
                str += "ÚLTIMAS SESSÕES ALTERADAS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "ÚLTIMAS SESSÕES  EXCLUÍDAS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "ÚLTIMAS SESSÕES ADICIONADAS: \n" + adicionadas + "\n";
            }


            // dias sem agendamento

            alterados = new ArrayList<>();
            excluidos = new ArrayList<>();
            adicionados = new ArrayList<>();
           for (ConfiguracaoDiasMetasTO agendamentoAnt : configuracaoDiasMetasSemAgendamentoAnt) {
                boolean existe = false;

                for (ConfiguracaoDiasMetasTO agendamento : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSemAgendamento()) {
                    if (agendamentoAnt.getProduto().getCodigo().equals(agendamento.getProduto().getCodigo())) {
                        existe = true;
                        agendamento.setVerificado(true);
                        if (!agendamentoAnt.getDescricao().equals(agendamento.getDescricao())) {
                            alterados.add(agendamento.getProduto().getDescricao()+ " - Descricão de " + agendamentoAnt.getDescricao() + " para " +  agendamento.getDescricao());
                        }
                        if (!agendamentoAnt.getNrDia().equals(agendamento.getNrDia())) {
                            alterados.add(agendamento.getProduto().getDescricao()+ " - Dia(s) de " + agendamentoAnt.getNrDia() + " para " +  agendamento.getNrDia());
                        }
                         break;
                    }
                }

                if (!existe) {
                    excluidos.add(agendamentoAnt.getDescricao()+ " - " + agendamentoAnt.getNrDia() + " dias - " +  agendamentoAnt.getProduto().getDescricao());
                }
            }

            for (ConfiguracaoDiasMetasTO agendamento : getConfiguracaoSistemaCRMVO().getConfiguracaoDiasMetasSemAgendamento()) {
                if (!agendamento.isVerificado()) {
                    adicionados.add(agendamento.getDescricao()+ " - " + agendamento.getNrDia() + " dias - " +  agendamento.getProduto().getDescricao());
                }
            }

            alteradas = listaAlteracaoLog(alterados);
            excluidas = listaAlteracaoLog(excluidos);
            adicionadas = listaAlteracaoLog(adicionados);


            if (!alteradas.isEmpty()){
                str += "SEM AGENDAMENTO ALTERADAS: \n" + alteradas + "\n";
            }
            if (!excluidas.isEmpty()){
                str += "SEM AGENDAMENTO EXCLUÍDAS: \n" + excluidas + "\n";
            }
            if (!adicionadas.isEmpty()){
                str += "SEM AGENDAMENTO ADICIONADAS: \n" + adicionadas + "\n";
            }
//            //REGISTRANDO OS VALORES ANTERIORES
//            List<String> perfilAcessoAnterior = new ArrayList<String>();
//            List<String> horarioAcesoAnterior = new ArrayList<String>();
//
            String strAnt = " ";
//
//            for (UsuarioPerfilAcessoVO perfilAnterior : perfilAcessoUsuarioAnt) {
//                perfilAcessoAnterior.add(perfilAnterior.getEmpresa().getNome() + " -- " + perfilAnterior.getPerfilAcesso().getNome());
//            }
//            for (HorarioAcessoSistemaVO horarioAnterior : horariosAcessoUsuarioAnt) {
//                horarioAcesoAnterior.add(horarioAnterior.getDiaSemana().toString() + "  " + horarioAnterior.getHoraInicial() + " - " + horarioAnterior.getHoraFinal());
//            }
//
//            String perfilAnterior = listaAlteracaoLog(perfilAcessoAnterior);
//            String horarioAnterior = listaAlteracaoLog(horarioAcesoAnterior);
//
//            if (!perfilAnterior.isEmpty() && str.contains("PERFIS")) {
//                strAnt += "PERFIS ANTERIORES: \n" + perfilAnterior + "\n";
//            }
//            if (!horarioAnterior.isEmpty() && str.contains("HORÁRIOS")) {
//                strAnt += "HORARIOS ANTERIORES: \n" + horarioAnterior + "\n";
//            }

            obj.setValorCampoAnterior(strAnt);
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAlterado(str);
            if (!str.isEmpty()) {
                registrarLogObjetoVO(obj, getConfiguracaoSistemaCRMVO().getCodigo());
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO("USUARIO", getConfiguracaoSistemaCRMVO().getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE USUARIO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

     public String listaAlteracaoLog(List<String> lista) {
        StringBuilder str = new StringBuilder();

        for (String mensagem : lista) {
            str.append(mensagem).append("\n");
        }
        return str.toString();
    }

	public List<FasesCRMEnum> getListaOrdenacaoMetas() {
		return listaOrdenacaoMetas;
	}

	public void setListaOrdenacaoMetas(List<FasesCRMEnum> listaOrdenacaoMetas) {
		this.listaOrdenacaoMetas = listaOrdenacaoMetas;
	}

	public List<UsuarioVO> autoCompleteRemetentePadraoMailing(Object suggest) {
		String pref = (String) suggest;
		List<UsuarioVO> result;
		try {
			if (pref.equals("%")) {
				result = getFacade().getUsuario().consultarTodosUsuarioComLimite(false, getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
			} else {
				result = getFacade().getUsuario().consultarPorNome(pref, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
			}
			for (UsuarioVO usuarioVO : result) {
				if (usuarioVO.getUsername().equals("RECOR") ||
						usuarioVO.getUsername().equals("admin") ||
						usuarioVO.getUsername().equals("PACTOBR")) {
					result.remove(usuarioVO);
				}
			}
		} catch (Exception ex) {
			result = new ArrayList<>();
		}
		return result;
	}

	public void selecionarRemetentePadrao() {
		try {
			UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("result");
			getConfiguracaoSistemaCRMVO().setRemetentePadraoMailing(obj);

			setSucesso(true);
			setErro(false);
			setMensagemDetalhada("", "");
		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}
        
        public void removerRemetentePadraoMailing() {
            configuracaoSistemaCRMVO.setRemetentePadraoMailing(new UsuarioVO());
			configuracaoSistemaCRMVO.setRemetentePadrao("");
        }

	public void setMsgAlert(String msgAlert) {
		this.msgAlert = msgAlert;
	}

	public String getMsgAlert() {
		if (msgAlert == null) {
			return "";
		}
		return msgAlert;
	}

	public void confirmarExcluir(){
		MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
		control.setMensagemDetalhada("", "");
		setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
		control.init("Gerar Padrão",
				"Deseja usar a sugestão do Método de Gestão?",
				this, "sugerirDivisao", "", "", "", "grupoBtnExcluir,fases");
	}

	public List<SelectItem> getSelectTipoColaboradorPosVenda() {
		if(selectTipoColaboradorPosVenda == null){
			selectTipoColaboradorPosVenda = new ArrayList<>();
		}
		return selectTipoColaboradorPosVenda;
	}

	public void setSelectTipoColaboradorPosVenda(List<SelectItem> selectTipoColaboradorPosVenda) {
		this.selectTipoColaboradorPosVenda = selectTipoColaboradorPosVenda;
	}

	public List<SelectItem> getMontarSelectTipoColaboradorPosVenda(){
		setSelectTipoColaboradorPosVenda(new ArrayList<>());
		getSelectTipoColaboradorPosVenda().add(new SelectItem("RPF", "Responsáveis pelas fases"));
		try{
			for(TipoColaboradorEnum tipo : TipoColaboradorEnum.values()){
				getSelectTipoColaboradorPosVenda().add(new SelectItem(tipo.getSigla(), tipo.getDescricao()));
			}
		}catch(Exception e){
			e.getStackTrace();
		}
		return getSelectTipoColaboradorPosVenda();
	}

	public String getJsonHealthCheckSendy() {
		return jsonHealthCheckSendy;
	}

	public void setJsonHealthCheckSendy(String jsonHealthCheckSendy) {
		this.jsonHealthCheckSendy = jsonHealthCheckSendy;
	}

	public boolean isPermiteHealthCheck() {
		return permiteHealthCheck;
	}

	public void setPermiteHealthCheck(boolean permiteHealthCheck) {
		this.permiteHealthCheck = permiteHealthCheck;
	}
}

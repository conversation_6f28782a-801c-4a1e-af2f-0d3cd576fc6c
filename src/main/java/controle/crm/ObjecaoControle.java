package controle.crm;
import java.util.*;
import javax.faces.model.SelectItem;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.crm.Objecao;
import negocio.interfaces.crm.ObjecaoInterfaceFacade;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * objecaoForm.jsp objecaoCons.jsp) com as funcionalidades da classe <code>Objecao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Objecao
 * @see ObjecaoVO
*/
public class ObjecaoControle extends SuperControle {
    private ObjecaoVO objecaoVO;
    private boolean objecaoDesistencia = false;
    private Integer objecaoTransferir;
    private boolean sucessoTransferencia = false;
    private String msgAlert;


    /**
    * Interface <code>ObjecaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */
    private ObjecaoInterfaceFacade objecaoFacade = null;

    public ObjecaoControle() throws Exception {
        setSucessoTransferencia(false);
        setObjecaoDesistencia(false);
        setObjecaoTransferir(0);
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>Objecao</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setObjecaoVO(new ObjecaoVO());
        setMensagemID("msg_entre_dados");
        setSucessoTransferencia(false);
        setObjecaoDesistencia(false);
        setObjecaoTransferir(0);
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Objecao</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        setSucessoTransferencia(false);
        setObjecaoDesistencia(false);
        setObjecaoTransferir(0);
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ObjecaoVO obj = getFacade().getObjecao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(Boolean.FALSE);
            setObjecaoVO(obj);
            if (getObjecaoVO().getTipoGrupo().toUpperCase().equals("MD")) {
                setObjecaoDesistencia(true);
            }
            obj.registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_editar");
        } catch (Exception e) {
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
            setMensagemID("msg_dados_editar");
            return "consultar";
        }
        return "editar";
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Objecao</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public void gravar() {
        try {
            setMsgAlert("");
            if (objecaoVO.isNovoObj()) {
                objecaoFacade.incluir(objecaoVO);
                super.criarLogInclusao(objecaoVO);
            } else {
                objecaoFacade.alterar(objecaoVO);
                super.criarLogAlteracao(objecaoVO);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
        	setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void realizarConsultaLogs() {
        super.consultaLogs(objecaoVO);
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP ObjecaoCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getObjecao().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
            	objs = getFacade().getObjecao().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("grupo")) {
            	objs = getFacade().getObjecao().consultarPorGrupo(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ObjecaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            objecaoFacade.excluir(objecaoVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"objecao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"objecao\" violates foreign key")){
                setMensagemDetalhada("Esta objeção não pode ser excluída, pois está sendo utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    /**
    * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
    */
    public String getMascaraConsulta() {
        return "";
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao","Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("grupo","Grupo"));
        return itens;
    }
    
    /**
	 * Método responsável por inicializar List<SelectItem> de valores do
	 * ComboBox correspondente ao atributo <code>tipoAgendamento</code>
	 * @return
	 * @throws Exception
	 */
	public List getListaSelectItemTipoGrupo() throws Exception {
		List objs = new ArrayList();
		Hashtable tipoGrupo = (Hashtable) Dominios.getTipoGrupoObjecao();
		Enumeration keys = tipoGrupo.keys();
		while (keys.hasMoreElements()) {
			String value = (String) keys.nextElement();
			String label = (String) tipoGrupo.get(value);
			if (!value.equals("MD")) { //ESSA OBJEÇÃO FOI REMOVIDA NO TICKET #4927 - NÃO FOI REMOVIDA DO DOMINÍOS PARA NÃO AFETAR OS CLIENTES QUE JA UTILIZAM
			objs.add(new SelectItem(value, label));
		}
		}
		SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
		Collections.sort((List) objs, ordenador);
		return objs;
	}

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        limparMsg();
        setListaConsulta(new ArrayList());
        return "consultar";
    }

    /**
    * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
    * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente 
    * quando realiza o logout.
    */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        objecaoVO = null;
        objecaoFacade = null;
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            objecaoFacade = new Objecao();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ObjecaoVO getObjecaoVO() {
        if(objecaoVO == null){
            objecaoVO = new ObjecaoVO();
        }
        return objecaoVO;
    }
     
    public void setObjecaoVO(ObjecaoVO objecaoVO) {
        this.objecaoVO = objecaoVO;
    }

    public boolean isObjecaoDesistencia() {
        return objecaoDesistencia;
    }

    public void setObjecaoDesistencia(boolean objecaoDesistencia) {
        this.objecaoDesistencia = objecaoDesistencia;
    }

    public List getListaObjecao() throws Exception {
        List resultadoConsulta = getFacade().getObjecao().consultarObjecao(false, "OB", Uteis.NIVELMONTARDADOS_TODOS);
        Ordenacao.ordenarLista(resultadoConsulta, "descricao");
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> listaObjecao = new ArrayList<SelectItem>();
        listaObjecao.add(new SelectItem(0, "Selecione"));
        while (i.hasNext()) {
            ObjecaoVO obj = (ObjecaoVO) i.next();
            String tipo = "";
            if (obj.getTipoGrupo().equals("MD")) {
                tipo = " - DESISTÊNCIA";
            } else if (obj.getTipoGrupo().equals("OD")) {
                tipo = " - DEFINITIVA";
            }
            listaObjecao.add(new SelectItem(obj.getCodigo(), obj.getDescricao() + " - " + obj.getGrupo() + tipo));
        }
        return listaObjecao;
    }

    public Integer getObjecaoTransferir() {
        if (objecaoTransferir == null) {
            objecaoTransferir = 0;
        }
        return objecaoTransferir;
    }

    public void setObjecaoTransferir(Integer objecaoTransferir) {
        this.objecaoTransferir = objecaoTransferir;
    }

    public String transferirObjecoesExcluir() {
        try {

            if (UteisValidacao.emptyNumber(getObjecaoTransferir())) {
                throw new Exception("Selecione a objeção que irá receber a transferência!");
            }
            String objecaoExcluida = getObjecaoVO().getDescricao();

            getFacade().getHistoricoContato().alterarTodasObjecoes(getObjecaoVO().getCodigo(), getObjecaoTransferir());
            getFacade().getObjecao().excluir(getObjecaoVO());
            setSucessoTransferencia(true);
            setMensagemDetalhada("Os vínculos com a objeção: \"" + objecaoExcluida + "\" foram transferidos e a objeção foi excluída com sucesso.");
            return "";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            return "editar";
        }
    }

    public boolean isSucessoTransferencia() {
        return sucessoTransferencia;
    }

    public void setSucessoTransferencia(boolean sucessoTransferencia) {
        this.sucessoTransferencia = sucessoTransferencia;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Objeção",
                "Deseja excluir a Objeção?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}
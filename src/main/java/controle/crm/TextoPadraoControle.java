package controle.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.crm.TextoPadraoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by glauco on 15/01/14.
 */
public class TextoPadraoControle extends SuperControle {

    private TextoPadraoVO textoPadraoVO = new TextoPadraoVO();
    private List<SelectItem> listaSelectItemFaseCRM = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemTipoContato = new ArrayList<SelectItem>();
    private String msgAlert;

    public TextoPadraoControle() throws Exception {
        inicializarListaSelectItemFaseCRM();
        inicializarListaSelectItemTipoContato();
    }

    public Boolean getRenderizarTextoFormatado(){
        String tipoContato = textoPadraoVO.getTipoContato();
        return !tipoContato.equals("CS") && !tipoContato.equals("AP");
    }

    public Boolean getTipoContatoAplicativo(){
        String tipoContato = textoPadraoVO.getTipoContato();
        return tipoContato != null && tipoContato.equals("AP");
    }

    public Boolean getTipoContatoSMS(){
        String tipoContato = textoPadraoVO.getTipoContato();
        return tipoContato != null && tipoContato.equals("CS");
    }

    private void inicializarListaSelectItemTipoContato() {
        Map<String, String> tiposContato = Dominios.getTipoContatoHistoricoContato();
        for(String k : tiposContato.keySet()){
            this.listaSelectItemTipoContato.add(new SelectItem(k, tiposContato.get(k)));
        }
    }

    public void alterouFase(ValueChangeEvent valueChangeEvent) {
        getTextoPadraoVO().setFaseCRM(FasesCRMEnum.getFase((Integer) valueChangeEvent.getNewValue()));
    }

    public void alterouTipoContato(ActionEvent event){
        getTextoPadraoVO().setMensagemPadrao("");
    }

    public Object novo() {
        setTextoPadraoVO(new TextoPadraoVO());
        setMensagemID("msg_entre_dados");
        setMensagemDetalhada("", "");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    public Object gravar() {
        try {
            setMsgAlert("");
            if (!getUsuarioLogado().getAdministrador()) {
                getTextoPadraoVO().setEmpresa(getEmpresaLogado());
            }

            if (!getTextoPadraoVO().getLinkDocs().trim().isEmpty()) {
                if (!getTextoPadraoVO().getLinkDocs().contains("docs.google")) {
                    throw new ConsistirException("Só é permitido link do Google Docs");
                }
            }

            if (getTextoPadraoVO().isNovoObj()) {
//                if (!validarFase()) {
//                    if (!getTextoPadraoVO().getFaseCRM().getSigla().equals(FasesCRMEnum.POS_VENDA.getSigla())){
//                        throw new ConsistirException("Já existe Texto Padrão para esta fase.");
//                    }
//                }
                getFacade().getTextoPadrao().incluir(getTextoPadraoVO());
            } else {
                getFacade().getTextoPadrao().alterar(getTextoPadraoVO());
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    private boolean validarFase() throws Exception {
        TextoPadraoVO textoPadraoVO = getFacade().getTextoPadrao().consultarPorFase(getTextoPadraoVO().getFaseCRM().getCodigo(), getTextoPadraoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return textoPadraoVO == null;
    }

    public Object editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            TextoPadraoVO obj = getFacade().getTextoPadrao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            setTextoPadraoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String excluir() {
        try {
            getFacade().getTextoPadrao().excluir(textoPadraoVO);
            setTextoPadraoVO(new TextoPadraoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"planotextpadrao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"planotextpadrao\" violates foreign key")){
                setMensagemDetalhada("Este Script não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    private void inicializarListaSelectItemFaseCRM() throws Exception {
        boolean estudio = isUsarEstudio();
        listaSelectItemFaseCRM = new ArrayList<SelectItem>();
        for (FasesCRMEnum fase : FasesCRMEnum.values()) {
            if ((estudio && (fase.equals(FasesCRMEnum.ULTIMAS_SESSOES) || fase.equals(FasesCRMEnum.SEM_AGENDAMENTO)))
                    || (!fase.equals(FasesCRMEnum.ULTIMAS_SESSOES) && !fase.equals(FasesCRMEnum.SEM_AGENDAMENTO))
                    && !fase.equals(FasesCRMEnum.CONVERSAO_PASSIVO) && !fase.equals(FasesCRMEnum.CONVERSAO_AGENDADOS) && !fase.equals(FasesCRMEnum.CONVERSAO_DESISTENTES)
                    && !fase.equals(FasesCRMEnum.CONVERSAO_EX_ALUNOS) && !fase.equals(FasesCRMEnum.CONVERSAO_INDICADOS) && !fase.equals(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS)) {
                listaSelectItemFaseCRM.add(new SelectItem(fase.getCodigo(), fase.getDescricao()));
            } 
        }
        Ordenacao.ordenarLista(listaSelectItemFaseCRM, "label");
    }

    public TextoPadraoVO getTextoPadraoVO() {
        return textoPadraoVO;
    }

    public void setTextoPadraoVO(TextoPadraoVO textoPadraoVO) {
        this.textoPadraoVO = textoPadraoVO;
    }

    public List<SelectItem> getListaSelectItemFaseCRM() {
        return listaSelectItemFaseCRM;
    }

    public void setListaSelectItemFaseCRM(List<SelectItem> listaSelectItemFaseCRM) {
        this.listaSelectItemFaseCRM = listaSelectItemFaseCRM;
    }

    public List<SelectItem> getListaSelectItemTipoContato() {
        return listaSelectItemTipoContato;
    }

    public void setListaSelectItemTipoContato(List<SelectItem> listaSelectItemTipoContato) {
        this.listaSelectItemTipoContato = listaSelectItemTipoContato;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Script",
                "Deseja excluir o Script?",
                this, "excluir", "", "", "", "grupoBtnExcluir,tblTextoPadrao_info");
    }

}

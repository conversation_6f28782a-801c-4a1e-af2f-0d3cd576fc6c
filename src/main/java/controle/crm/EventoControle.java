package controle.crm;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

import javax.faces.model.SelectItem;

import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.crm.Evento;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * eventoForm.jsp eventoCons.jsp) com as funcionalidades da classe <code>Evento</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Evento
 * @see EventoVO
 */
public class EventoControle extends SuperControle {

    private EventoVO eventoVO;
    private String msgAlert;

    /**
     * Interface <code>EventoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public EventoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Evento</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setMensagemDetalhada("");
        setEventoVO(new EventoVO());
        setMensagemID("msg_entre_dados");
        //redireciona para a pagina eventoForm.jsp
        return "editar";
    }
    /**
     * Usado para abrir o popup de edicao de evento e limpar o eventoVO
     */
    public void novoEvento(){
        setEventoVO(new EventoVO());
        setMensagemID("msg_entre_dados");
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Evento</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            EventoVO obj = getFacade().getEvento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(new Boolean(false));
            obj.registrarObjetoVOAntesDaAlteracao();
            setEventoVO(obj);
            setMensagemID("msg_dados_editar");
        } catch (Exception e) {
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
            setMensagemID("msg_dados_editar");
            return "consultar";
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Evento</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public void gravar() {
        try {
            setMsgAlert("");
            if (eventoVO.isNovoObj().booleanValue()) {
                getFacade().getEvento().incluir(eventoVO);
                incluirLogInclusao();
            } else {
                getFacade().getEvento().alterar(eventoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP EventoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getEvento().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getEvento().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>EventoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getEvento().excluir(eventoVO);
            incluirLogExlusao();
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"evento\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"evento\" violates foreign key")){
                setMensagemDetalhada("Este evento não pode ser excluído, pois já foi utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("status", "Status"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        setMensagemID("");
        return "consultar";
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
     * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente
     * quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        eventoVO = null;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemTipoStatus() throws Exception {
        List objs = new ArrayList();
        Hashtable tipoStatus = (Hashtable) Dominios.getTipoStatus();
        Enumeration keys = tipoStatus.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoStatus.get(value);
            objs.add(new SelectItem(value, label));
        }
        return objs;
    }

    public EventoVO getEventoVO() {
        return eventoVO;
    }

    public void setEventoVO(EventoVO eventoVO) {
        this.eventoVO = eventoVO;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Evento",
                "Deseja excluir o Evento?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public void realizarConsultaLogObjetoSelecionado() throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.consultarLogObjetoSelecionado("EVENTO", this.eventoVO.getCodigo(), null);
    }

    public void incluirLogInclusao() throws Exception {
        try {
            eventoVO.setObjetoVOAntesAlteracao(new EventoVO());
            eventoVO.setNovoObj(true);
            registrarLogObjetoVO(eventoVO, eventoVO.getCodigo(), "EVENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("EVENTO", eventoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE EVENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        eventoVO.setNovoObj(false);
        eventoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExlusao() throws Exception {
        try {
            eventoVO.setObjetoVOAntesAlteracao(new EventoVO());
            eventoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(eventoVO, eventoVO.getCodigo(), "EVENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("EVENTO", eventoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE EVENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(eventoVO, eventoVO.getCodigo(), "EVENTO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("EVENTO", eventoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE EVENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        eventoVO.registrarObjetoVOAntesDaAlteracao();
    }

}

package controle.crm;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.*;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.SuperControleCRM;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.ReplicarRedeEmpresaCallable;
import controle.arquitetura.security.UsuarioControle;
import controle.basico.ClienteControle;
import controle.basico.EmailOptinControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.plano.PlanoControle;
import importador.LeitorExcel2010;
import importador.UteisImportacaoExcel;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.crm.*;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.plano.MarcadorVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.oamd.RedeEmpresaVO;
import org.ajax4jsf.event.AjaxEvent;
import org.apache.http.HttpHeaders;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.basico.MailingFiltrosTO;
import servicos.discovery.RedeDTO;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.microsservice.cadaux.CadAuxMsService;
import servicos.integracao.sms.Balance;
import servicos.integracao.sms.SmsController;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * malaDiretaForm.jsp malaDiretaCons.jsp) com as funcionalidades da classe <code>MalaDireta</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see MalaDireta
 * @see MalaDiretaVO
 */
public class MalaDiretaControle extends SuperControleCRM {

    private Balance saldo;
    private Balance saldoMarketing;

    private ControleConsulta controleConsulta;
    private List<MetaExtraImportadoDTO> listaItensMetaExtraImportada = new ArrayList<>();
    private Boolean enviarEmailIndividualmente;
    private boolean contatoInstantaneo = false;
    private List<SelectItem> listSelectItemTipoConsultor;
    private boolean permissaoConsultaTodasEmpresas = false;

    private Integer filtroEmpresa;
    private boolean permiteCriarMailingTodasEmpresas = false;
    private File arquivo;
    private boolean consultarSaldoSMS = true;
    private Boolean apresentarOpcoesSMS = false;
    private Boolean botaoAmostraCliente = true;
    private boolean simEnviarSomenteSaldoRestanteDia = false;
    private boolean simEnviarSomenteSaldoRestanteMes = false;
    private boolean existeConvenioCobrancaPix = false;

    private boolean exibirReplicarRedeEmpresa = false;

    private boolean exibirDetalhesOptin = false;


    private List<AmostraClienteTO> listaClienteSemResposta;

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");

            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
            if (getMalaDiretaVO() != null) {
                getMalaDiretaVO().setEmpresa(getEmpresaLogado());
            }
            verificaPodeTodasEmpresas();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void verificaPodeTodasEmpresas() {
        try {
            setPermiteCriarMailingTodasEmpresas(false);

            if (!permissaoConsultaTodasEmpresas) {
                setPermiteCriarMailingTodasEmpresas(false);
                return;
            }

            if (getMalaDiretaVO() == null) {
                return;
            }

            List<EmpresaVO> empresas = getFacade().getEmpresa().consultarTodas(null, Uteis.NIVELMONTARDADOS_MINIMOS);
            if (empresas.size() > 1) {
                setPermiteCriarMailingTodasEmpresas(true);
                getMalaDiretaVO().setTodasEmpresas(false);
            } else {
                setPermiteCriarMailingTodasEmpresas(false);
                getMalaDiretaVO().setTodasEmpresas(false);
            }
        } catch (Exception e) {
            setPermiteCriarMailingTodasEmpresas(false);
            if (getMalaDiretaVO() != null) {
                getMalaDiretaVO().setTodasEmpresas(false);
            }
        }
    }

    public void setSaldo(Balance saldo) {
        this.saldo = saldo;
    }

    public Balance getSaldo() {
        return saldo;
    }

    public String getSaldoApresentar() {
        if (getSaldo() != null) {
            setApresentarOpcoesSMS(false);
            return String.format("Seu saldo de SMS para a data de hoje é de: %s, período de validade de envios é de %s até %s."
                    , getSaldo().getBalance(), Calendario.getData(getSaldo().getValidityStart(), "dd/MM/yyyy")
                    , Calendario.getData(getSaldo().getValidityEnd(), "dd/MM/yyyy"));
        } else {
            setApresentarOpcoesSMS(true);
            return "Seu saldo de SMS para a data de hoje é de: 0";
        }
    }

    public void setApresentarOpcoesSMS(Boolean apresentarOpcoesSMS){
        this.apresentarOpcoesSMS = apresentarOpcoesSMS;
    }

    public boolean isApresentarOpcoesSMS(){
        return apresentarOpcoesSMS;
    }

    public String getModalMensagemGenerica() {
        if (modalMensagemGenerica == null) {
            modalMensagemGenerica = "";
        }
        return modalMensagemGenerica;
    }

    public void setModalMensagemGenerica(String modalMensagemGenerica) {
        this.modalMensagemGenerica = modalMensagemGenerica;
    }

    public Integer getQtdDias() {
        if (qtdDias == null) {
            qtdDias = 0;
        }
        return qtdDias;
    }

    public void setQtdDias(Integer qtdDias) {
        this.qtdDias = qtdDias;
    }

    public boolean isContatoInstantaneo() {
        return contatoInstantaneo;
    }

    public void setContatoInstantaneo(boolean contatoInstantaneo) {
        this.contatoInstantaneo = contatoInstantaneo;
    }

    public boolean isExibirFiltros() {
        return !getMalaDiretaVO().getCfgEvento().getIndicados() &&
                !getMalaDiretaVO().getImportarLista() &&
                !getMalaDiretaVO().getContatoDireto() &&
                !isContatoInstantaneo() &&
                (!getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.VENDA_CONTRATO.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_MATRICULA.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_RENOVACAO.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_REMATRICULA.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_CANCELAMENTO.getCodigo()));
    }

    public boolean isExibirBotaoAmostra() {
        return !getMalaDiretaVO().getContatoDireto() &&
                !getMalaDiretaVO().isCrmExtra() &&
                !isContatoInstantaneo() &&
                (!getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.VENDA_CONTRATO.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_MATRICULA.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_RENOVACAO.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_REMATRICULA.getCodigo()) &&
                        !getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_CANCELAMENTO.getCodigo()));
    }

    public boolean isUsarHorarioEnvio() {
        return usarHorarioEnvio;
    }

    public void setUsarHorarioEnvio(boolean usarHorarioEnvio) {
        this.usarHorarioEnvio = usarHorarioEnvio;
    }

    public boolean isPermiteCriarMailingTodasEmpresas() {
        return permiteCriarMailingTodasEmpresas;
    }

    public void setPermiteCriarMailingTodasEmpresas(boolean permiteCriarMailingTodasEmpresas) {
        this.permiteCriarMailingTodasEmpresas = permiteCriarMailingTodasEmpresas;
    }

    public Boolean getPesquisa() {
        return pesquisa;
    }

    public void setPesquisa(Boolean pesquisa) {
        this.pesquisa = pesquisa;
    }

    public boolean isExisteConvenioCobrancaPix() {
        return existeConvenioCobrancaPix;
    }

    public void setExisteConvenioCobrancaPix(boolean existeConvenioCobrancaPix) {
        this.existeConvenioCobrancaPix = existeConvenioCobrancaPix;
    }

    public List<ConvenioCobrancaVO> getConvenioCobrancaVO() {
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(List<ConvenioCobrancaVO> convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public Integer getTotalAceito() {
        return totalAceito;
    }

    public void setTotalAceito(Integer totalAceito) {
        this.totalAceito = totalAceito;
    }

    public Integer getTotalnaoAceito() {
        return totalnaoAceito;
    }

    public void setTotalnaoAceito(Integer totalnaoAceito) {
        this.totalnaoAceito = totalnaoAceito;
    }

    public Integer getTotalSemResposta() {
        return totalSemResposta;
    }

    public void setTotalSemResposta(Integer totalSemResposta) {
        this.totalSemResposta = totalSemResposta;
    }

    public List<AmostraClienteTO> getListaClienteSemResposta() {
        return listaClienteSemResposta;
    }

    public void setListaClienteSemResposta(List<AmostraClienteTO> listaClienteSemResposta) {
        this.listaClienteSemResposta = listaClienteSemResposta;
    }

    public boolean isExibirDetalhesOptin() {
        return exibirDetalhesOptin;
    }

    public void setExibirDetalhesOptin(boolean exibirDetalhesOptin) {
        this.exibirDetalhesOptin = exibirDetalhesOptin;
    }

    private enum MODE_MODAL {
        DESTINATARIOS, ENVIADOS, NAO_ENVIADOS
    }

    private MODE_MODAL modo = MODE_MODAL.DESTINATARIOS;
    private MalaDiretaVO malaDiretaVO;
    private String campoConsultarRemetente;
    private String valorConsultarRemetente;
    private List listaConsultarRemetente;
    private String campoConsultarModeloMensagem;
    private String valorConsultarModeloMensagem;
    private List listaConsultarModeloMensagem;
    private MalaDiretaEnviadaVO malaDiretaEnviadaVO;
    private String campoConsultarPessoa;
    private String valorConsultarPessoa;
    private List listaConsultarPessoa;
    private List listaEmail;
    private Date dataConsulta;
    private Date dataInicial;
    private Date dataFinal;
    private Date dataInicialCriacao;
    private Date dataFinalCriacao;
    private List<SelectItem> listaSelectItemEventos;
    private List<SelectItem> listaSelectItemTipoAgendamento;
    private List<SelectItem> listaSelectItemOcorrencia;
    private Integer ocorrenciaSelecionada;
    private boolean semanalmente = false;
    private boolean mensalmente = false;
    private boolean umavez = true;
    private boolean diariamente = false;
    private String consultarDescricao = "";
    private String consultarRemetente = "";
    private Integer consultarMeioEnvio = MeioEnvio.EMAIL.getCodigo();
    private ConfiguracaoSistemaCRMVO configCRM = null;
    private Integer codigoMailing;
    private boolean criarEditarMailing = false;
    private boolean usarAgendamento = false;
    private String abaAtual = "";
    private List<ClienteVO> clientes;
    private String labelModal = "";
    private String logSql = "";
    private ConfPaginacao confPaginacaoHistorico = new ConfPaginacao();
    private ConfPaginacao confPaginacaoClientes = new ConfPaginacao();
    private Integer codigoTipoAgendamento = TipoAgendamentoEnum.TODOS.getCodigo();
    private boolean mostrarCategorias = false;
    private boolean mostrarSituacoes = false;
    private boolean mostrarColaboradores = false;
    private boolean mostrarConsultores = false;
    private boolean mostrarProfessores = false;
    private boolean mostrarModalidades = false;
    private boolean mostrarDadosCadastrais = false;
    private boolean mostrarDadosPlanos = false;
    private List<CategoriaVO> categoriaVOs = new ArrayList<>();
    private List<SituacaoClienteTO> situacaoClienteTOs = new ArrayList<>();
    private List<ModalidadeVO> modalidadeVOs = new ArrayList<>();
    private List<ColaboradorVO> consultoresVOs = new ArrayList<>();
    private List<ColaboradorVO> professoresVOs = new ArrayList<>();
    private List<PlanoVO> planoVOs = new ArrayList<>();
    private List<ContratoDuracaoVO> contratoDuracaoVOs = new ArrayList<>();
    private String filtrosDescricao = "";
    private Boolean somenteRecorrencia = Boolean.FALSE;

    private Boolean categoria = Boolean.FALSE;
    private Boolean colaboradores = Boolean.FALSE;
    private Boolean dadosCadastrais = Boolean.FALSE;
    private Boolean dadosPlanos = Boolean.FALSE;
    private Boolean modalidades = Boolean.FALSE;
    private Boolean situacao = Boolean.FALSE;
    private Boolean extra = Boolean.FALSE;
    private Boolean pesquisa = Boolean.FALSE;

    private String nomeClasse = "MalaDireta";
    private List<SelectItem> listaSelectTiposPerguntas;
    private List<SelectItem> listaSelectItemPesquisa;
    private Integer tipoMensagemApp = TipoPerguntaEnum.SIMPLES.getCodigo();
    private CamposGenericosTO campos = new CamposGenericosTO();

    LogVO log = new LogVO();

    private List<SelectItem> tiposAgendamento;
    private List<SelectItem> tiposVigencia;

    private TipoVigenciaConsultaEnum tipoVigencia = TipoVigenciaConsultaEnum.TODOS;

    private List<AmostraClienteTO> listaAmostra = new ArrayList<>();
    private org.richfaces.component.html.HtmlDataTable htmlDataTable;


    private String campoOrdenar;
    private String tipoOrdernacao;
    private List<UsuarioVO> listaUsuariosMetaCRMExtra;
    private String modalMensagemGenerica;

    private Integer qtdDias;
    private boolean usarHorarioEnvio = true;

    private List<MalaDiretaRedeEmpresaVO> listaMalaDiretaRedeEmpresa;

    /**
     * Apenas para o CRM Extra
     */
    private Integer inicioDiasSemAparecer = null;
    private Integer fimDiasSemAparecer = null;
    private Date inicioVencimento;
    private Date fimVencimento;
    private List<ConvenioCobrancaVO> convenioCobrancaVO = new ArrayList<>();

    private Integer totalAceito;
    private Integer totalnaoAceito;
    private Integer totalSemResposta;

    public MalaDiretaControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        getControleConsulta().setNumeroPaginaScroll(5);
        setMensagemID("");

    }

    public void limparTodos() throws Exception{
        inicializarListas();
        malaDiretaVO.setMailingFiltros(new MailingFiltrosTO());
        malaDiretaVO.setCfgEvento(new ConfigEventoMailingTO());
    }

    private void inicializarListas() throws Exception {
        setSomenteRecorrencia(false);
        Integer codEmpresa = getMalaDiretaVO().getEmpresa().getCodigo();

       setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        setCategoriaVOs(getFacade().getCategoria().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_TODOS));
        setSituacaoClienteTOs(new ArrayList<>());
        for (SituacaoClienteEnum situacaoClienteEnum : SituacaoClienteEnum.values()) {
            if(!situacaoClienteEnum.equals(SituacaoClienteEnum.INATIVO)){
                SituacaoClienteTO situacaoClienteTO = new SituacaoClienteTO();
                situacaoClienteTO.setSituacaoClienteEnum(situacaoClienteEnum);
                getSituacaoClienteTOs().add(situacaoClienteTO);
            }

        }
        Ordenacao.ordenarLista(planoVOs, "descricao");
        setModalidadeVOs(getFacade().getModalidade().consultarPorNome("", codEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        setConsultoresVOs(getFacade().getColaborador().consultarPorNomeTipoColaborador("", codEmpresa, true, false, Uteis.NIVELMONTARDADOS_MINIMOS, TipoColaboradorEnum.CONSULTOR));
        setProfessoresVOs(getFacade().getColaborador().consultarPorNomeTipoColaborador("", codEmpresa, true, false, Uteis.NIVELMONTARDADOS_MINIMOS, TipoColaboradorEnum.PROFESSOR));
        setPlanoVOs(getFacade().getPlano().consultarPorDescricao("", codEmpresa, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        Ordenacao.ordenarLista(planoVOs, "descricao");
        setContratoDuracaoVOs(getFacade().getContratoDuracao().consultarNumeroMeses());
        setListaUsuariosMetaCRMExtra(getFacade().getUsuario().consultarUsuarioAberturaMeta(codEmpresa, false, Uteis.NIVELMONTARDADOS_MINIMOS));
        Ordenacao.ordenarLista(getListaUsuariosMetaCRMExtra(), "nome");
        setListSelectItemTipoConsultor(montarListaVinculo());
        montarListaPesquisa();
    }

    private List montarListaVinculo(){
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable situacaoColaborador = Dominios.getVinculos();
        Enumeration keys = situacaoColaborador.keys();

        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) situacaoColaborador.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort(objs, ordenador);

        return objs;
    }

    public void marcaDesmarcaRecorrencia() throws Exception {
        setPlanoVOs(new ArrayList<>());
        List<PlanoVO> planos = getFacade().getPlano().consultarPorDescricao("", getMalaDiretaVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (somenteRecorrencia) {
            for (PlanoVO plano : planos) {
                if (plano.getRecorrencia() || (plano.getRegimeRecorrencia() != null && plano.getRegimeRecorrencia())) {
                    getPlanoVOs().add(plano);
                }
            }
        } else {
            getPlanoVOs().addAll(planos);
        }
    }


    public void setarOcorrencia() {
        if(getEventoEscolhido()){
           getMalaDiretaVO().getAgendamento().setOcorrencia(OcorrenciaEnum.getOcorrencia(getMalaDiretaVO().getCfgEvento().getOcorrencia()));
        }else{
           getMalaDiretaVO().getAgendamento().setOcorrencia(OcorrenciaEnum.getOcorrencia(ocorrenciaSelecionada));
        }

    }

    public List<SelectItem> getListaSelectItemMeioDeEnvio() throws Exception {
        List<SelectItem> objs = new ArrayList<>();
        MeioEnvio[] listaMeioEnvio = MeioEnvio.values();
        for (MeioEnvio aListaMeioEnvio : listaMeioEnvio) {
            if (!aListaMeioEnvio.equals(MeioEnvio.CRM_EXTRA)) {
                int codigo = aListaMeioEnvio.getCodigo();
                String descricao = aListaMeioEnvio.getDescricao();
                objs.add(new SelectItem(codigo, descricao));
            }
        }
        return objs;
    }

    public void limparCampos() throws Exception  {
        getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        getMalaDiretaVO().setMensagem("");
        getMalaDiretaVO().setTitulo("");
        setListaConsultarModeloMensagem(new ArrayList());
        inicializarEventosPreDefinidos();
    }

    public void incluirTagNome() {
        executarInsercaoTag("TAG_NOME");
    }

    public void incluirTagSaldoPontos() {
        executarInsercaoTag("TAG_SALDOPONTOS");
    }

    public void incluirTagPNome() {
        executarInsercaoTag("TAG_PNOME");
    }

    public void incluirTagBoleto() {
        executarInsercaoTag("TAG_BOLETO");
    }

    public void incluirTagPix() {
        executarInsercaoTag("TAG_PIX");
    }

    public void incluirTagParcelasCobranca() {
        executarInsercaoTag("TAG_PARCELAS_COBRANCA");
    }

    public void executarInsercaoTag(String tag) {
        try {
            String[] mensagem = getMalaDiretaVO().getMensagem().split("</body>");
            if (mensagem.length > 1) {
                getMalaDiretaVO().setMensagem(mensagem[0].trim() + tag + "</body>" + mensagem[1]);
            } else {
                getMalaDiretaVO().setMensagem(getMalaDiretaVO().getMensagem() + tag);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public List<ClienteVO> getClientes() {
        if (clientes == null) {
            clientes = new ArrayList<>();
        }
        return clientes;
    }

    public void setClientes(List<ClienteVO> clientes) {
        this.clientes = clientes;
    }

    public void abrirMailing() {
        try {
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.MAILING.toString()));
            novo();
            consultarInicial();
            setCriarEditarMailing(false);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public void initContatoPessoal(){
        abrirMailing();
    }

    public List<SelectItem> getListaDuracoesContratos() throws Exception {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, ""));
        List<ContratoDuracaoVO> listaNumerosMeses = getFacade().getContratoDuracao().consultarNumeroMeses();
        for (ContratoDuracaoVO duracao : listaNumerosMeses) {
            lista.add(new SelectItem(duracao.getNumeroMeses(), duracao.getNumeroMeses().toString()));
        }
        return lista;
    }

    public List<SelectItem> getListaEventos() throws Exception {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, ""));
        List<EventoVO> listaEventos = getFacade().getEvento().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (EventoVO evento : listaEventos) {
            lista.add(new SelectItem(evento.getCodigo(), evento.getDescricao()));
        }
        return lista;
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Vinculo</code> por meio dos parametros informados na tela de mailing.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados na tela de mailing
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public List<VinculoVO> executarAutocompleteConsultaVinculoCarteira(Object suggest) {
        String pref = (String) suggest;
        getMalaDiretaVO().getMailingFiltros().setFiltroVinculoCarteira(new VinculoVO());
        ArrayList<VinculoVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<VinculoVO>) getFacade().getVinculo().
                        consultarTodosVinculosCarteiraComLimite(getMalaDiretaVO().getEmpresa().getCodigo());
            } else {
                result = (ArrayList<VinculoVO>) getFacade().getVinculo().
                        consultarPorNomeColaboradorVinculoCarteiraComLimite(getMalaDiretaVO().getEmpresa().getCodigo(), pref);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<VinculoVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarVinculoCarteiraSuggestionBox() throws Exception {
        VinculoVO vinculo = (VinculoVO) request().getAttribute("result");
        if (vinculo != null) {
            getMalaDiretaVO().getMailingFiltros().setFiltroVinculoCarteira(vinculo);
        }
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Modalidade</code> por meio dos parametros informados na tela de mailing.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados na tela de mailing
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public List<ModalidadeVO> executarAutocompleteConsultaModalidade(Object suggest) {
        String pref = (String) suggest;
        List<ModalidadeVO> result;
        try {
            if (pref.equals("%")) {
                result = getFacade().getModalidade().consultarTodasModalidadesComLimite(getMalaDiretaVO().getEmpresa().getCodigo(), 50);
            } else {
                result = (List<ModalidadeVO>) getFacade().getModalidade().
                        consultarPorNomeModalidadeComLimite(getMalaDiretaVO().getEmpresa().getCodigo(), pref);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ModalidadeVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarModalidadeSuggestionBox() throws Exception {
        ModalidadeVO modalidadeVO = (ModalidadeVO) request().getAttribute("result");
        if (modalidadeVO != null) {
            getMalaDiretaVO().getMailingFiltros().setFiltroModalidade(modalidadeVO);
        }
    }

    public Integer getTotalClientesConsultados() {
        return this.getClientes().size();
    }

    public Integer getTotalClientesAdicionados() {
        return this.getMalaDiretaVO().getMalaDiretaEnviadaVOs().size();
    }

    public void consultarEnviados(){
        try {
            setMsgAlert("");
            modo = MODE_MODAL.ENVIADOS;
            if(!getMalaDiretaVO().getContatoDireto()){
                MailingHistoricoVO historico = (MailingHistoricoVO) context().getExternalContext().getRequestMap().get("historico");
                if(historico != null){
                   setListaAmostra(getFacade().getCliente().consultarEnviadosMailing(historico.getClientesEnviados(),(
                           !UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getEventoCodigo()) &&
                           getMalaDiretaVO().getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.INDICADOS.getCodigo())), false));

                    getSession().setAttribute("modeloAmostra", getMalaDiretaVO().getSqlClientesAmostra().replace("distinct cli.*", "  cli.*")
                            + (getMalaDiretaVO().getCfgEvento().getIndicados() ? " ORDER BY nome " :  " ORDER BY sw.nomecliente"));

                    setTotalAceito(totalAceito());
                    setTotalnaoAceito(totalNaoAceito());
                    setTotalSemResposta(totalSemResposta());

    //               setListaAmostra(getFacade().getCliente().consultarNaoEnviadosMailing(historico.getClientesNaoEnviados()));
                   setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
                }
            } else {
                StringBuffer clientes = new StringBuffer();
                StringBuffer indicados = new StringBuffer();
                StringBuffer passivos = new StringBuffer();
                for(MalaDiretaEnviadaVO dest : getMalaDiretaVO().getMalaDiretaEnviadaVOs()){
                    if(dest.getClienteVO() != null && !UteisValidacao.emptyNumber(dest.getClienteVO().getCodigo())){
                        clientes.append(dest.getClienteVO().getCodigo()).append(",");
                    }
                    if(dest.getIndicadoVO() != null && !UteisValidacao.emptyNumber(dest.getIndicadoVO().getCodigo())){
                        indicados.append(dest.getIndicadoVO().getCodigo()).append(",");
                    }
                    if(dest.getPassivoVO() != null && !UteisValidacao.emptyNumber(dest.getPassivoVO().getCodigo())){
                        passivos.append(dest.getPassivoVO().getCodigo()).append(",");
                    }
                }
                setListaAmostra(new ArrayList<>());
                if(!clientes.toString().equals("")){
                    getListaAmostra().addAll(getFacade().getCliente().consultarEnviadosMailing(clientes.deleteCharAt(clientes.lastIndexOf(",")).toString(), false, false));
                }
                if(!indicados.toString().equals("")){
                    getListaAmostra().addAll(getFacade().getCliente().consultarEnviadosMailing(indicados.deleteCharAt(indicados.lastIndexOf(",")).toString(), true, false));
                }
                if(!passivos.toString().equals("")){
                    getListaAmostra().addAll(getFacade().getCliente().consultarEnviadosMailing(passivos.deleteCharAt(passivos.lastIndexOf(",")).toString(), false, true));
                }
                setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
            }

        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada().replaceAll("'", ""));
        }
    }

        public void consultarNaoEnviados(){
        try {
            setMsgAlert("");
            modo = MODE_MODAL.NAO_ENVIADOS;
            MailingHistoricoVO historico = (MailingHistoricoVO) context().getExternalContext().getRequestMap().get("historico");
            if(historico != null){
//               setListaAmostra(getFacade().getCliente().consultarEnviadosMailing(historico.getClientesEnviados()));
               setListaAmostra(getFacade().getCliente().consultarNaoEnviadosMailing(historico.getClientesNaoEnviados(),(
                       !UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getEventoCodigo()) &&
                       getMalaDiretaVO().getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.INDICADOS.getCodigo()))));
               setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
            }

        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada().replaceAll("'", ""));
        }
    }

    public void consultarClientes() {
        try {
            setExibirDetalhesOptin(getMalaDiretaVO().getMeioDeEnvioEnum().getCodigo() == MeioEnvio.EMAIL.getCodigo());
            limparMsg();
            setMsgAlert("");
            setModalMensagemGenerica("");
            if(getMalaDiretaVO().getImportarLista()){
                if(!getMalaDiretaVO().getListaImportada()) {
                    setListaAmostra(new ArrayList<AmostraClienteTO>() {{
                        for (MetaExtraImportadoDTO meta : listaItensMetaExtraImportada) {
                            add(new AmostraClienteTO(meta));
                        }
                    }});
                }else{

                    List<AmostraClienteTO> listaAmostraSql = new ArrayList<>();
                    listaAmostraSql.addAll(getFacade().getCliente().consultarAmostra("select p.nome, cli.matricula, cli.situacao, empresa.nome from maladiretacrmextracliente m\n" +
                            "inner join cliente cli on cli.codigo = m.cliente \n" +
                            "inner join pessoa p on p.codigo = cli.pessoa\n" +
                            "inner join empresa on empresa.codigo = cli.empresa where maladireta = " + getMalaDiretaVO().getCodigo() + " ORDER BY m.codigo ", null));
                    listaAmostraSql.addAll(getFacade().getCliente().consultarAmostra("select p.nome, empresa.nome from maladiretacrmextracliente m \n" +
                            "inner join passivo p on p.codigo = m.passivo \n" +
                            "inner join empresa on empresa.codigo = p.empresa where maladireta = " + getMalaDiretaVO().getCodigo() + " ORDER BY m.codigo ", null));

                    setListaAmostra(listaAmostraSql);
                }
                setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
                setModalMensagemGenerica("Richfaces.showModalPanel('panelAmostraClientes');");
                return;
            }
            if (getMalaDiretaVO().isCrmExtra() && (getMalaDiretaVO().getEmpresa() == null
                    || getMalaDiretaVO().getEmpresa().getCodigo() == 0)) {
                throw new ConsistirException("Selecione uma empresa para a meta!");
            }

            if (getMalaDiretaVO().getCfgEvento().getEventoCodigo() != null) {
                if (getMalaDiretaVO().getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.VENCIMENTO_PRODUTO.getCodigo())) {
                    if (getMalaDiretaVO().getAgendamento().getOcorrencia() == OcorrenciaEnum.INSTANTANEO) {
                        if ((getMalaDiretaVO().getCfgEvento().getInicio() == null) || (getMalaDiretaVO().getCfgEvento().getFim() == null)) {
                            throw new ConsistirException("Para enviar o email instantâneo é necessário informar o período 'vencendo entre'.");
                        }
                        if (Calendario.maior(getMalaDiretaVO().getCfgEvento().getInicio(), getMalaDiretaVO().getCfgEvento().getFim())) {
                            throw new ConsistirException("Favor corrigir o período 'vencendo entre', pois a data inicial está maior que a data final.");
                        }
                        if (Uteis.getDataComUltimaHora(getMalaDiretaVO().getCfgEvento().getInicio()).compareTo(Uteis.getDataComUltimaHora(Calendario.hoje())) < 0) {
                            throw new ConsistirException("Data Início não pode ser menor que hoje!");
                        }
                    }

                }

                if (getMalaDiretaVO().getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.PRODUTOS_VENCIDOS.getCodigo())) {
                    getMalaDiretaVO().getCfgEvento().setCodigosProdutosVencidos(Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getListaProdutosVOs(), "Selecionado", "Codigo", true));
                    if (getMalaDiretaVO().getAgendamento().getOcorrencia() == OcorrenciaEnum.INSTANTANEO) {
                        if (Uteis.getDataComUltimaHora(getMalaDiretaVO().getCfgEvento().getFim()).compareTo(Uteis.getDataComUltimaHora(Calendario.hoje())) > 0) {
                            throw new ConsistirException("Data fim não pode ser maior que hoje!");
                        }
                    }
                }

                if (getMalaDiretaVO().getCfgEvento().getEventoCodigo().equals(TipoEventoEnum.PARCELAS_VENCIDAS.getCodigo())) {
                    if(UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getDiasParcelasVencidasInicial())){
                        throw new ConsistirException("Faixa inicial de dias deve ser maior que zero.");
                    }else{
                        if(!UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getDiasParcelasVencidasFinal()) &&
                                getMalaDiretaVO().getCfgEvento().getDiasParcelasVencidasInicial() > getMalaDiretaVO().getCfgEvento().getDiasParcelasVencidasFinal()){
                            throw new ConsistirException("A faixa inicial de dias deve ser menor que a final.");
                        }
                    }
                }
            }

            modo = MODE_MODAL.DESTINATARIOS;
            if(!UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getEventoCodigo())
                && UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getOcorrencia()))
            {
                throw new ConsistirException("Informe a periodicidade!");

            }
            if(getMalaDiretaVO().getCfgEvento().getApresentarDatas() &&
                    (getMalaDiretaVO().getCfgEvento().getInicio() == null
                    || getMalaDiretaVO().getCfgEvento().getFim() == null)){
                throw new ConsistirException("Informe corretamente o período da consulta!");

            }
            malaDiretaVO.setConfiguracaoSistemaCRMVO(getConfiguracaoSistemaCRMVO());
            montarDescricaoFiltros();
            setMsgAlert("");
            setModalMensagemGenerica("");
            setListaAmostra(new ArrayList<>());
            getFacade().getMailingAgendamento().montarSql(getMalaDiretaVO(), getMalaDiretaVO().getEmpresa().getCodigo(),
                    categoriaVOs, situacaoClienteTOs, modalidadeVOs,
                    consultoresVOs, professoresVOs, planoVOs, contratoDuracaoVOs, convenioCobrancaVO, inicioVencimento, fimVencimento, inicioDiasSemAparecer, fimDiasSemAparecer, false);
            if(getMalaDiretaVO() != null && getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.SMS)) {
                List<AmostraClienteTO> listaFull = getFacade().getCliente().consultarAmostra(getMalaDiretaVO().getSqlClientesAmostra()
                        + (getMalaDiretaVO().getCfgEvento().getIndicados() ? " ORDER BY nome " : " ORDER BY sw.nomecliente"), null);
                for (AmostraClienteTO amostra : listaFull) {
                    amostra.setTelefones(Uteis.limparTelefonesInvalidos(amostra.getTelefones()));
                    String[] tels = amostra.getTelefones().split("<br/>");
                    if (tels.length == 1) {
                        if (Uteis.telefoneValido(amostra.getTelefones())) {
                            getListaAmostra().add(amostra);
                        }
                    } else {
                        getListaAmostra().add(amostra);
                    }
                }
            }else{
                setListaAmostra(getFacade().getCliente().consultarAmostra(getMalaDiretaVO().getSqlClientesAmostra()
                        + (getMalaDiretaVO().getCfgEvento().getIndicados() ? " ORDER BY nome " :  " ORDER BY sw.nomecliente"), null));

                getSession().setAttribute("modeloAmostra", getMalaDiretaVO().getSqlClientesAmostra()
                        + (getMalaDiretaVO().getCfgEvento().getIndicados() ? " ORDER BY nome " :  " ORDER BY sw.nomecliente"));


                setTotalAceito(totalAceito());
                setTotalnaoAceito(totalNaoAceito());
                setTotalSemResposta(totalSemResposta());
            }
            setMsgAlert("Richfaces.showModalPanel('panelAmostraClientes');");
            setModalMensagemGenerica("Richfaces.showModalPanel('panelAmostraClientes');");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar(true));
        }
    }

    public Integer totalAceito () throws Exception {
         String sqlModelo = (String) getSession().getAttribute("modeloAmostra");
         return getFacade().getCliente().consultarAmostra(sqlModelo, null).size();
    }

    public Integer totalNaoAceito () throws Exception {
        String sqlModelo = (String) getSession().getAttribute("modeloAmostra");

        sqlModelo = sqlModelo.replace("(o.bloqueadobounce = false or o.bloqueadobounce is null)",
                "(o.bloqueadobounce = false ) and dataexclusao is not null ");

        sqlModelo = sqlModelo.replace("(o.bloqueadobounce = false and dataexclusao is null  or o.bloqueadobounce is null)",
                "(o.bloqueadobounce = false ) and dataexclusao is not null ");

        sqlModelo = sqlModelo.replace("e.bloqueadobounce IS FALSE", "e.bloqueadobounce IS TRUE");

        return getFacade().getCliente().consultarAmostra(sqlModelo, null).size();
    }

    public Integer totalSemResposta () throws Exception {
        String sqlModelo = (String) getSession().getAttribute("modeloAmostra");

        sqlModelo = sqlModelo.replace("(o.bloqueadobounce = false or o.bloqueadobounce is null)",
                "( o.bloqueadobounce = true ) and  datainscricao is null ");

        sqlModelo = sqlModelo.replace("(o.bloqueadobounce = false and dataexclusao is null  or o.bloqueadobounce is null)",
                "( o.bloqueadobounce = true ) and  datainscricao is null ");
        setListaClienteSemResposta(getFacade().getCliente().consultarAmostra(sqlModelo, null));
        return getFacade().getCliente().consultarAmostra(sqlModelo, null).size();
    }

    public void enviarOptin(){
        ClienteVO clienteVO  = new ClienteVO();
        try {
            for (int i = 0; i <= getListaClienteSemResposta().size() - 1; i++) {
                if (!getListaClienteSemResposta().get(i).getEmails().isEmpty()) {
                    String [] emails = getListaClienteSemResposta().get(i).getEmails().split("<br/>");
                    EmailOptinControle emailOptinControle;
                    if( emails.length > 0 ) {
                        for(int y=0; y<= emails.length-1; y++){
                            emailOptinControle  = new EmailOptinControle();
                            clienteVO.getPessoa().setEmail(emails[y]);
                            clienteVO.setCodigo(getListaClienteSemResposta().get(i).getCodigoCliente());
                            emailOptinControle.renviarEmailOptin(clienteVO, getKey(), getMalaDiretaVO().getEmpresa().getCodigo());
                        }
                    }
                }
            }
        }
        catch (Exception e){
            montarErro(e);
        }
    }

    public void limparDias() throws IOException {
        getMalaDiretaVO().getCfgEvento().setNrFaltas(0);
        getMalaDiretaVO().getCfgEvento().setDiaMais(0);
        getMalaDiretaVO().getCfgEvento().setDiaMenos(0);
        getMalaDiretaVO().getCfgEvento().setMinValorInt(0);
        getMalaDiretaVO().getCfgEvento().setMaxValorInt(0);
        getMalaDiretaVO().getCfgEvento().setModeloPadraoBoleto(false);
        if (getMalaDiretaVO().isCrmExtra()) {
            getMalaDiretaVO().getCfgEvento().setOcorrencia(OcorrenciaEnum.INSTANTANEO.getCodigo());
        }
        if(getMalaDiretaVO().getCfgEvento().getPosVenda()){
            for (SituacaoClienteTO situacaoClienteTO : getSituacaoClienteTOs()) {
                if (situacaoClienteTO.getSelecionado()) {
                    situacaoClienteTO.setSelecionado(false);
                }
            }
        }
    }

    private void montarDescricaoFiltros() throws Exception{
        filtrosDescricao = "";
        boolean algumSelecionado = false;
        String nomesCategoria = Uteis.getListaEscolhidos(categoriaVOs, "Selecionado", "Nome", false);
        String nomesModalidade = Uteis.getListaEscolhidos(modalidadeVOs, "Selecionado", "Nome", false);
        String nomesConsultores = Uteis.getListaEscolhidos(consultoresVOs, "Selecionado", "Pessoa_Apresentar", false);
        String nomesProfessores = Uteis.getListaEscolhidos(professoresVOs, "Selecionado", "Pessoa_Apresentar", false);
        String nomesPlano = Uteis.getListaEscolhidos(planoVOs, "Selecionado", "Descricao", false);
        String nomesContratoDuracao = Uteis.getListaEscolhidos(contratoDuracaoVOs, "Selecionado", "NumeroMeses", true);
        String situacoes = Uteis.getListaEscolhidos(situacaoClienteTOs, "Selecionado", "Descricao", false);
        String convenios = Uteis.getListaEscolhidos(convenioCobrancaVO, "Selecionado", "Descricao", false);

        //NOMES CATEGORIAS
        if(!UteisValidacao.emptyString(nomesCategoria)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>de categoria:</b> "+nomesCategoria;
        }

        //SITUACOES
        if(!UteisValidacao.emptyString(situacoes)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>de situação:</b> "+situacoes;
        }

        //MODALIDADES
        if(!UteisValidacao.emptyString(nomesModalidade)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com modalidade:</b> "+nomesModalidade;
        }

        //CONSULTORES
        if(!UteisValidacao.emptyString(nomesConsultores)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com vínculo de consultor com:</b> "+nomesConsultores;
        }

        //PROFESSORES
        if(!UteisValidacao.emptyString(nomesProfessores)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com vínculo de professor com:</b> "+nomesProfessores;
        }

        //PLANOS
        if(!UteisValidacao.emptyString(nomesPlano)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com plano:</b> "+nomesPlano;
        }

        //DURACAO
        if(!UteisValidacao.emptyString(nomesContratoDuracao)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com duração de contrato:</b> "+nomesContratoDuracao+" meses";
        }
        if(!UteisValidacao.emptyString(convenios)){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com convenio de cobrança:</b> "+convenios;
        }

        //DATA DE CADASTRO
        if(getMalaDiretaVO().getMailingFiltros().getMostrarDataCadastro()){
            algumSelecionado = true;
            filtrosDescricao += "; <b>com data de cadastro </b>";
            if(getMalaDiretaVO().getMailingFiltros().getDataCadastroMin() != null){
                filtrosDescricao += " <b> maior ou igual a </b>"+Uteis.getData(getMalaDiretaVO().getMailingFiltros().getDataCadastroMin());
            }
            if(getMalaDiretaVO().getMailingFiltros().getDataCadastroMax() != null){
                filtrosDescricao += " <b>"
                                    +(getMalaDiretaVO().getMailingFiltros().getDataCadastroMin() == null ? "" :" e" )
                                    +" menor ou igual a </b>"+Uteis.getData(getMalaDiretaVO().getMailingFiltros().getDataCadastroMax());
            }
        }

        //IDADE
        if(getMalaDiretaVO().getMailingFiltros().getMostrarIdade()){
            algumSelecionado = true;
            filtrosDescricao +="; <b>com idade </b>";
            if(!UteisValidacao.emptyNumber(getMalaDiretaVO().getMailingFiltros().getIdadeMin())){
                filtrosDescricao += " <b>maior ou igual a </b>"+getMalaDiretaVO().getMailingFiltros().getIdadeMin()+ " anos";
            }
            if(!UteisValidacao.emptyNumber(getMalaDiretaVO().getMailingFiltros().getIdadeMax())){
                filtrosDescricao += " <b>"
                                    +(UteisValidacao.emptyNumber(getMalaDiretaVO().getMailingFiltros().getIdadeMin()) ? "" :" e" )
                                    +" menor ou igual a </b>"+getMalaDiretaVO().getMailingFiltros().getIdadeMax() + " anos";
            }
        }

        if(getMalaDiretaVO().getMailingFiltros().isMasculino()){
            algumSelecionado = true;
            filtrosDescricao += getMalaDiretaVO().getMailingFiltros().isFeminino() ? "; <b>de ambos os sexos</b>" : "<b>do sexo masculino</b>";
        }
        //SEXO
        if(getMalaDiretaVO().getMailingFiltros().isFeminino()){
            algumSelecionado = true;
        }

        if (getMalaDiretaVO().getCfgEvento().getEvento() != null) {
            algumSelecionado = true;
            switch (getMalaDiretaVO().getCfgEvento().getEvento()) {
                case EVENTO_ANIVERSARIANTES:
                    filtrosDescricao += "; que fazem <b>aniversário</b>";
                    switch (getMalaDiretaVO().getAgendamento().getOcorrencia()) {
                        case INSTANTANEO:
                            filtrosDescricao += " entre " + Uteis.getDataAplicandoFormatacao(getMalaDiretaVO().getCfgEvento().getInicio(), "dd/MM")
                                    + " e " + Uteis.getDataAplicandoFormatacao(getMalaDiretaVO().getCfgEvento().getFim(), "dd/MM");
                            break;
                        case MENSALMENTE:
                            filtrosDescricao += " este mês";
                            break;
                        default:
                            filtrosDescricao += " hoje";
                    }
                    break;
                //------------------------------------------------------------------
                case EVENTO_MATRICULADOS:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b>matriculados</b>");
                    break;
                //------------------------------------------------------------------
                case EVENTO_REMATRICULADOS:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b>rematriculados</b>");
                    break;
                //------------------------------------------------------------------
                case EVENTO_RENOVADOS:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b>renovados</b>");
                    break;
                case EVENTO_DESISTENTES:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b>cujo situação do cliente é INATIVO e contrato venceu</b>");
                    break;
                //------------------------------------------------------------------
                case VISITANTES:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b>visitantes</b> cadastrados");
                    break;
                case VINTE_QUATRO_HORAS:
                    filtrosDescricao += "; <b>que visitaram a academia no dia de ontem "
                            + "e não fecharam um contrato, apenas foram cadastrados como visitantes</b>";
                    break;
                //------------------------------------------------------------------
                case RENOVAR:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b>com contrato vencendo</b>");
                    break;
                //----------------------------------------------------
                case GRUPO_RISCO:
                    String listaEscolhidos = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getRiscos(), "Selecionado", "Label", false);
                    if (UteisValidacao.emptyString(listaEscolhidos)) {
                        throw new ConsistirException("Selecione pelo menos um nível de risco!");
                    }
                    filtrosDescricao += "; <b>com risco:</b> " + listaEscolhidos;
                    break;
                case FALTOSOS:
                    filtrosDescricao += "; que estão com " + getMalaDiretaVO().getCfgEvento().getNrFaltas()
                            + (getMalaDiretaVO().getCfgEvento().getNrFaltasMaior() ? " ou mais " : " ") +
                            "dias de <b> falta</b>";
                    break;
                case SALDO_PONTOS:
                    filtrosDescricao += "Quantidade mínima de pontos: " + getMalaDiretaVO().getCfgEvento().getNrFaltas();
                    break;
                case DEBITO:
                    filtrosDescricao += "; <b> com débito </b> de no mínimo "
                            + Formatador.formatarValorMonetario(getMalaDiretaVO().getCfgEvento().getMinValor())
                            + " e no máximo " + Formatador.formatarValorMonetario(getMalaDiretaVO().getCfgEvento().getMaxValor())
                            + ", a mais de " + getMalaDiretaVO().getCfgEvento().getMinimoDiasVencido()
                            + (getMalaDiretaVO().getCfgEvento().getMinimoDiasVencido() > 1 ? " dias" : " dia");
                    break;
                case PENDENCIAS:
                    filtrosDescricao += "; <b> com pendências de cadastros</b>";
                    String pendencias = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getPendencias(), "Selecionado", "Label", false);
                    if (UteisValidacao.emptyString(pendencias)) {
                        throw new ConsistirException("Selecione pelo menos uma pendência!");
                    }
                    filtrosDescricao += pendencias.isEmpty() ? "" : " nos campos: " + pendencias;
                    break;
                case INDICADOS:
                    filtrosDescricao = "<b>Indicados </b>" + (getMalaDiretaVO().getCfgEvento().getNaoClientes() ? "que ainda não se tornaram alunos" : "");
                    return;
                case VENCIMENTO_PRODUTO:
                    filtrosDescricao += montarDescricaoEventoGenerico("; <b> com produtos vencendo</b>");
                    break;
                case COMPRA_PRODUTO:
                    String tipos = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getTipoProdutos(), "Selecionado", "Label", false);
                    if (UteisValidacao.emptyString(tipos)) {
                        throw new ConsistirException("Selecione pelo menos um tipo de produto!");
                    }
                    filtrosDescricao += montarDescricaoEventoGenerico("; que <b>compraram um produto do tipo: </b>" + tipos);
                    break;
                case AGENDAMENTOS_PRODUTO:
                    String produtosSessao = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getProdutosSessao(), "Selecionado", "Label", false);
                    filtrosDescricao += montarDescricaoEventoGenerico("; com <b>agendamentos de sessão</b>"
                            + (produtosSessao.isEmpty() ? "" : " dos produtos: " + produtosSessao));
                    break;
                case AGENDAMENTOS_AMBIENTE:
                    String ambientes = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getAmbientes(), "Selecionado", "Label", false);
                    filtrosDescricao += montarDescricaoEventoGenerico("; com <b>agendamentos de sessão</b>"
                            + (ambientes.isEmpty() ? "" : " nos ambientes: " + ambientes));
                    break;
                case AGENDAMENTOS_PERIODO:
                    filtrosDescricao += montarDescricaoEventoGenerico("; com <b>agendamentos de sessão</b>");
                    break;
                case PRODUTOS_VENDIDOS:
                    String produtosVendidos = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getProdutosSessao(), "Selecionado", "Label", false);
                    filtrosDescricao += montarDescricaoEventoGenerico("; que <b>já compraram um produto do tipo sessão</b>"
                            + (produtosVendidos.isEmpty() ? "" : " dos produtos: " + produtosVendidos));
                    break;
                case A_AGENDAR:
                    String produtosAgendar = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getProdutosSessao(), "Selecionado", "Label", false);
                    filtrosDescricao += "; que <b>tem produtos a agendar</b>" + (produtosAgendar.isEmpty() ? "" : " dos produtos: " + produtosAgendar);
                    break;
                case A_FATURAR:
                    String produtosFaturar = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getProdutosSessao(), "Selecionado", "Label", false);
                    filtrosDescricao += "; que <b>tem produtos a agendar</b>" + (produtosFaturar.isEmpty() ? "" : " dos produtos: " + produtosFaturar);
                    break;
                case AGENDAMENTOS_PROFISSIONAL:
                    String profissionais = Uteis.getListaEscolhidos(getMalaDiretaVO().getCfgEvento().getColaboradores(), "Selecionado", "Label", false);
                    filtrosDescricao += montarDescricaoEventoGenerico("; com <b>agendamentos de sessão</b>"
                            + (profissionais.isEmpty() ? "" : " para os profissionais: " + profissionais));
                    break;
                case POS_VENDA:
                    filtrosDescricao += "Ativos onde a quantidade de dias de ativos é: " + getMalaDiretaVO().getDiasPosVenda();
                    break;
                case FREEPASS:
                    filtrosDescricao += "; que <b>iniciaram o Freepass</b> há " + getMalaDiretaVO().getCfgEvento().getNrDiasInicioFreePass()
                            + " dias";
                    break;
            }
        }

        filtrosDescricao = algumSelecionado ?
                          ((getMalaDiretaVO().getMailingFiltros().isFeminino()
                            && !getMalaDiretaVO().getMailingFiltros().isMasculino()) ? "Todas as alunas " : "Todos os alunos ")
                           +filtrosDescricao.replaceFirst(";", "")+"." : "";
        if (!getMalaDiretaVO().isCrmExtra() && getMalaDiretaVO().getMeioDeEnvioEnum() != null) {
            switch (getMalaDiretaVO().getMeioDeEnvioEnum()) {
                case EMAIL:
                    filtrosDescricao += " (Alunos com e-mail para correspondência)";
                    break;
                case SMS:
                    filtrosDescricao += " (Alunos com número do celular informado)";
                    break;
            }
        }

    }

    public String montarDescricaoEventoGenerico(String evento) {
        String filtro = evento;
        switch (getMalaDiretaVO().getAgendamento().getOcorrencia()) {
            case INSTANTANEO:
                filtro += " entre " + Uteis.getData(getMalaDiretaVO().getCfgEvento().getInicio())
                        + " e " + Uteis.getData(getMalaDiretaVO().getCfgEvento().getFim());
                break;
            case MENSALMENTE:
                filtro += " este mês";
                break;
            case DIARIAMENTE:
                if(getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.EVENTO_DESISTENTES)) {
                    Calendar dataCRM = Calendar.getInstance();
                    dataCRM.add(Calendar.DAY_OF_MONTH, -1 - getConfigCRM().getNrDiasParaClientePrevePerda());
                    filtro += " no dia " + Uteis.getData(dataCRM.getTime());;
                }
                else if(getMalaDiretaVO().getCfgEvento().getAprDmais()
                        && (getMalaDiretaVO().getCfgEvento().getDiaMais()> 0)){
                    filtro += " há "+getMalaDiretaVO().getCfgEvento().getDiaMais()
                            +(getMalaDiretaVO().getCfgEvento().getDiaMais() > 1 ? " dias": " dia");
                }else if(getMalaDiretaVO().getCfgEvento().getAprDMenos()
                        && (getMalaDiretaVO().getCfgEvento().getDiaMenos()> 0)){
                    filtro += " daqui a "+getMalaDiretaVO().getCfgEvento().getDiaMenos()
                            +(getMalaDiretaVO().getCfgEvento().getDiaMenos() > 1 ? " dias": " dia");
                }else{
                    filtro += " hoje";
                }
                break;
            case SEMANALMENTE:
                Calendar dataInicial = Calendar.getInstance();
                dataInicial.setTime(getHoje());
                dataInicial.add(Calendar.DAY_OF_MONTH, -7 - getConfigCRM().getNrDiasParaClientePrevePerda());
                Calendar dataFinal = Calendar.getInstance();
                dataFinal.setTime(getHoje());
                dataFinal.add(Calendar.DAY_OF_MONTH, -1 - getConfigCRM().getNrDiasParaClientePrevePerda());
                filtro += " entre " + Uteis.getData(dataInicial.getTime())
                        + " e " + Uteis.getData(dataFinal.getTime());
                break;
            default:
                filtro += " hoje";
        }
        return filtro;
    }

    public void limparFiltros() {
        getMalaDiretaVO().setMailingFiltros(new MailingFiltrosTO());
    }

    public void adicionarClientesSelecionados() {
        try {
            //Limpando as variaveis de mensagem
            limparMsg();
            if (getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL)) {
                //Variavel auxiliar de verificacao das pessoas que possuem e-mail
                boolean pessoaSemEmail = false;

                for (ClienteVO cliente : this.getClientes()) {
                    if (cliente.getSelecionado()) {
                        this.malaDiretaEnviadaVO.setClienteVO(cliente);
                        //Verificando se o cliente selecionado possui e-mail
                        if ((malaDiretaEnviadaVO.getEmails() == null) || ("".equals(malaDiretaEnviadaVO.getEmails()))) {
                            pessoaSemEmail = true;
                        } else {
                            this.getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
                            this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
                        }
                    }
                }

                //Verificando se o cliente selecionado possui e-mail
                if (pessoaSemEmail) {
                    setMensagemDetalhada("Atenção - Alguma(s) pessoa(s) não possue(m) e-mail cadastrado, só foi adicionado as pessoas que possuem e-mail");
                    return;
                }
            } else if (getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.SMS)) {
                //Variavel auxiliar de verificacao das pessoas que possuem telefone celular
                boolean pessoaSemTelefone = false;

                for (ClienteVO cliente : this.getClientes()) {
                    if (cliente.getSelecionado()) {
                        this.malaDiretaEnviadaVO.setClienteVO(cliente);
                        //Verificando se o cliente selecionado possui e-mail
                        if ((malaDiretaEnviadaVO.getTelefones() == null) || ("".equals(malaDiretaEnviadaVO.getTelefones()))) {
                            pessoaSemTelefone = true;
                        } else {
                            this.getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
                            this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
                        }
                    }
                }

                //Verificando se o cliente selecionado possui telefone celular
                if (pessoaSemTelefone) {
                    setMensagemDetalhada("Atenção - Alguma(s) pessoa(s) não possue(m) telefone celular cadastrado, só foi adicionado as pessoas que possuem telefone celular");
                    return;
                }
            }
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarTodosClientes() {
        try {
            //Limpando as variaveis de mensagem
            limparMsg();
            if (getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL)) {
                //Variavel auxiliar de verificacao das pessoas que possuem e-mail
                boolean pessoaSemEmail = false;

                for (ClienteVO cliente : this.getClientes()) {
                    cliente.setSelecionado(Boolean.TRUE);
                    this.malaDiretaEnviadaVO.setClienteVO(cliente);
                    //Verificando se o cliente selecionado possui e-mail
                    if ((malaDiretaEnviadaVO.getEmails() == null) || ("".equals(malaDiretaEnviadaVO.getEmails()))) {
                        pessoaSemEmail = true;
                    } else {
                        this.getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
                        this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
                    }
                }

                //Verificando se o cliente selecionado possui e-mail
                if (pessoaSemEmail) {
                    setMensagemDetalhada("Atenção - Alguma(s) pessoa(s) não possue(m) e-mail cadastrado, só foi adicionado as pessoas que possuem e-mail");
                    return;
                }
            } else if (getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.SMS)) {
                //Variavel auxiliar de verificacao das pessoas que possuem telefone celular
                boolean pessoaSemTelefone = false;

                for (ClienteVO cliente : this.getClientes()) {
                    cliente.setSelecionado(Boolean.TRUE);
                    this.malaDiretaEnviadaVO.setClienteVO(cliente);
                    //Verificando se o cliente selecionado possui telefone celular
                    if ((malaDiretaEnviadaVO.getTelefones() == null) || ("".equals(malaDiretaEnviadaVO.getTelefones()))) {
                        pessoaSemTelefone = true;
                    } else {
                        this.getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
                        this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
                    }
                }

                //Verificando se o cliente selecionado possui telefone celular
                if (pessoaSemTelefone) {
                    setMensagemDetalhada("Atenção - Alguma(s) pessoa(s) não possue(m) telefone celular cadastrado, só foi adicionado as pessoas que possuem telefone celular");
                    return;
                }

            }
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void excluirMalaDiretaEnviada() {
        MalaDiretaEnviadaVO malaDiretaEnviada = (MalaDiretaEnviadaVO) JSFUtilities.getRequestAttribute("malaDiretaEnviada");
        this.getMalaDiretaVO().getMalaDiretaEnviadaVOs().remove(malaDiretaEnviada);
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>MalaDireta</code>
     * para edição pelo usuário da aplicação.
     *
     * @throws Exception
     */
    public String novo() throws Exception {
        setExibirReplicarRedeEmpresa(false);

        setMalaDiretaVO(new MalaDiretaVO());
        montarListaSelectItemEmpresa();
        setContatoInstantaneo(false);
        fecharTodos();
        inicializarListas();
        limparMetaExtra();
        mapaLog = new HashMap<>();
        inicializarLog(true);
        getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.EMAIL);
        getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
        setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());

        setCampoConsultarRemetente("");
        setValorConsultarRemetente("");
        setListaConsultarRemetente(new ArrayList<>());

        setCampoConsultarModeloMensagem("");
        setValorConsultarModeloMensagem("");
        setListaConsultarModeloMensagem(new ArrayList<>());

        setCampoConsultarPessoa("");
        setValorConsultarPessoa("");
        setListaConsultarPessoa(new ArrayList<>());

        setListaEmail(new ArrayList<>());
        setEnviarEmailIndividualmente(false);
        super.novo();
        setListaConsulta(new ArrayList<>());
        this.setClientes(null);
        setMensagemID("msg_entre_dados");
        setUsarAgendamento(false);
        montarAgendamento();
        setCriarEditarMailing(true);
        inicializarEventosPreDefinidos();

        if (consultarSaldoSMS && (!getKey().equals("teste"))) {
            consultarSaldo();
            consultarSaldoSMS = false;
        }
        montarConvenioPix();
        return "editar";
    }

    private void limparMetaExtra() {
        inicioDiasSemAparecer = null;
        fimDiasSemAparecer = null;
        inicioVencimento = null;
        fimVencimento = null;
    }


    public void consultarSaldo() {
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                SmsController smsController = new SmsController(getEmpresaLogado().getTokenSMS(), getKey(), TimeZone.getTimeZone(getEmpresaLogado().getTimeZoneDefault()));
                setSaldo(smsController.getSaldoRemanecente());

                if(!UteisValidacao.emptyString(getEmpresaLogado().getTokenSMSShortCode())){
                    SmsController smsControllerShort = new SmsController(getEmpresaLogado().getTokenSMSShortCode(), getKey(), TimeZone.getTimeZone(getEmpresaLogado().getTimeZoneDefault()));
                    setSaldoMarketing(smsControllerShort .getSaldoRemanecente());
                }

                setApresentarOpcoesSMS((getSaldo() == null || UteisValidacao.emptyNumber(getSaldo().getBalance()))
                    && (getSaldoMarketing() == null || UteisValidacao.emptyNumber(getSaldoMarketing().getBalance())));
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void inicializarEventosPreDefinidos() throws Exception{
        ConfiguracaoSistemaVO cfgSistema = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
        cfgSistema.inicializarConfiguracaoSistemaCliente();
        List<GenericoTO> sessoes = getFacade().getMailingAgendamento().consultarSimples("codigo", "descricao", "produto", "tipoproduto", "SS");
        List<GenericoTO> ambientes = getFacade().getMailingAgendamento().consultarSimples("codigo", "descricao", "ambiente", "tipomodulo", "SS");
        List<GenericoTO> profissionais = getFacade().getColaborador().consultarSimplesPorTipo(TipoColaboradorEnum.ESTUDIO);
        ConfigEventoMailingTO cfgEvt = new ConfigEventoMailingTO(getMalaDiretaVO().getMeioDeEnvioEnum(), cfgSistema,
                sessoes, ambientes, profissionais);
        getMalaDiretaVO().setCfgEvento(getFacade().getMalaDireta().consultarConfigEvento(getMalaDiretaVO().getCodigo(), true, cfgEvt));
    }

    private void montarAgendamento() throws Exception{
        getMalaDiretaVO().setAgendamento(new MailingAgendamentoVO());
        montarListaOcorrencia();
        montarTipos(true);
        ocorrenciaSelecionada = OcorrenciaEnum.INSTANTANEO.getCodigo();
        listaSelectItemEventos = TipoEventoEnum.getListaCombo(isUsarEstudio());
        listaSelectItemTipoAgendamento = TipoAgendamentoEnum.getListaCombo();

    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>MalaDireta</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() throws Exception {
        MalaDiretaVO obj = (MalaDiretaVO) context().getExternalContext().getRequestMap().get("malaDireta");
        return editar(obj);
    }

    public String editar(MalaDiretaVO obj) throws Exception {
        setMsgAlert("");
        setConfPaginacaoHistorico(new ConfPaginacao());

        montarListaSelectItemEmpresa();
        setMalaDiretaVO(getFacade().getMalaDireta().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, false));
        inicializarAtributosRelacionados(obj);

        validarExibirReplicarRedeEmpresa();
        setContatoInstantaneo(!getMalaDiretaVO().getContatoDireto() && getMalaDiretaVO().getTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO));
        getMalaDiretaVO().setChaveAntiga(String.format("%s_%s_%s", getKey(),
                getMalaDiretaVO().getMeioDeEnvioEnum().getDescricao(),
                getMalaDiretaVO().getTipoAgendamento().name()));
        setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
        this.setClientes(null);
        consultarHistorico();
        setEnviarEmailIndividualmente(malaPossuiTag());
        setMensagemID("msg_dados_editar");
        ocorrenciaSelecionada = getMalaDiretaVO().getAgendamento().getOcorrencia().getCodigo();
        inicializarEventosPreDefinidos();
        alterarTipo();
        getMalaDiretaVO().setNovoObj(false);
        setCriarEditarMailing(true);
        montarFiltrosSelecionados();
        fecharTodos();
        inicializarLog(false);
        if (isExibirReplicarRedeEmpresa()) {
            prepararListaReplicarEmpresa();
        }
        return "editar";
    }
    
    public void inicializarLog(Boolean novo){
        mapaLog = povoarMapaValoresParaLog(novo);
    }
    
    
    public Map<String, String> povoarMapaValoresParaLog(Boolean novo){
        Map<String, String> map = new HashMap<>();
        if(!novo){
            map.put("remetente", getMalaDiretaVO().getRemetente().getNome());
            map.put("titulo", getMalaDiretaVO().getTitulo());
            map.put("periodo_de", getMalaDiretaVO().getDataEnvio() == null ? "" : Uteis.getData(getMalaDiretaVO().getDataEnvio()));
            map.put("periodo_ate", getMalaDiretaVO().getVigenteAte() == null ? "" : Uteis.getData(getMalaDiretaVO().getVigenteAte()));
            map.put("horaInicio", getMalaDiretaVO().getAgendamento().getHoraInicio() == null ? "" : getMalaDiretaVO().getAgendamento().getHoraInicio().toString());
            map.put("horaFim", getMalaDiretaVO().getAgendamento().getHoraFim() == null ? "" : getMalaDiretaVO().getAgendamento().getHoraFim().toString());
            map.put("minValor", String.valueOf(getMalaDiretaVO().getCfgEvento().getMinValor()));
            map.put("maxValor", String.valueOf(getMalaDiretaVO().getCfgEvento().getMaxValor()));
            map.put("selecionarRecorrencia", String.valueOf(getSomenteRecorrencia()));
        }

        map.put("questionario", getMalaDiretaVO().getQuestionario().toString());
        map.put("cfgevento", getMalaDiretaVO().getCfgEvento().getEvento() == null ? "" : getMalaDiretaVO().getCfgEvento().getEvento().getDescricao());
        map.put("ocorrencia", getMalaDiretaVO().getAgendamento().getOcorrencia().getDescricao());
        map.put("modelo", getMalaDiretaVO().getModeloMensagemApresentar());
        map.put("categorias", obterValoresLista(categoriaVOs, "nome", "selecionado", "codigo"));
        map.put("modalidades", obterValoresLista(modalidadeVOs, "nome", "selecionado", "codigo"));
        map.put("planos", obterValoresLista(planoVOs, "descricao", "selecionado", "codigo"));
        map.put("duracoes", obterValoresLista(contratoDuracaoVOs, "numeroMeses", "selecionado", "NumeroMeses"));
        map.put("consultores", obterValoresLista(consultoresVOs, "pessoa_Apresentar", "selecionado", "codigo"));
        map.put("professores", obterValoresLista(professoresVOs, "pessoa_Apresentar", "selecionado", "codigo"));
        map.put("sexo", (getMalaDiretaVO().getMailingFiltros().isMasculino() ? "Masculino" : "") +
                (getMalaDiretaVO().getMailingFiltros().isFeminino() && getMalaDiretaVO().getMailingFiltros().isFeminino() ? " - " : "") +
                (getMalaDiretaVO().getMailingFiltros().isFeminino() ? "Feminino" : ""));
        map.put("idade_minima", getMalaDiretaVO().getMailingFiltros().getIdadeMinStr());
        map.put("idade_maxima", getMalaDiretaVO().getMailingFiltros().getIdadeMaxStr());
        map.put("cadastro_minimo", getMalaDiretaVO().getMailingFiltros().getDataCadastroMin() == null ? "" : Uteis.getData(getMalaDiretaVO().getMailingFiltros().getDataCadastroMin()));
        map.put("cadastro_maximo", getMalaDiretaVO().getMailingFiltros().getDataCadastroMax() == null ? "" : Uteis.getData(getMalaDiretaVO().getMailingFiltros().getDataCadastroMax()));

        map.put("config_inicio", getMalaDiretaVO().getCfgEvento().getInicio() == null ? "" : Uteis.getData(getMalaDiretaVO().getCfgEvento().getInicio()));
        map.put("config_fim", getMalaDiretaVO().getCfgEvento().getFim() == null ? "" : Uteis.getData(getMalaDiretaVO().getCfgEvento().getFim()));
        map.put("config_fim", getMalaDiretaVO().getCfgEvento().getFim() == null ? "" : Uteis.getData(getMalaDiretaVO().getCfgEvento().getFim()));
        map.put("riscos", obterValoresLista(getMalaDiretaVO().getCfgEvento().getRiscos(), "label", "selecionado", "codigo"));
        map.put("nrFaltasMaior", String.valueOf(getMalaDiretaVO().getCfgEvento().getNrFaltasMaior()));
        map.put("nrFaltas", String.valueOf(getMalaDiretaVO().getCfgEvento().getNrFaltas()));
        map.put("minimoDiasVencido", String.valueOf(getMalaDiretaVO().getCfgEvento().getMinimoDiasVencido()));

        map.put("qtdMinParcelasVencidas", String.valueOf(getMalaDiretaVO().getCfgEvento().getQtdMinParcelasVencidas()));
        map.put("boletoParcelasVencendo", String.valueOf(getMalaDiretaVO().getCfgEvento().isBoletoParcelasVencendo()));
        map.put("pendencias", obterValoresLista(getMalaDiretaVO().getCfgEvento().getPendencias(), "label", "selecionado", "codigo"));
        map.put("naoClientes", String.valueOf(getMalaDiretaVO().getCfgEvento().getNaoClientes()));
        map.put("tipoProdutos", obterValoresLista(getMalaDiretaVO().getCfgEvento().getTipoProdutos(), "label", "selecionado", "codigo"));
        map.put("produtosSessao", obterValoresLista(getMalaDiretaVO().getCfgEvento().getProdutosSessao(), "label", "selecionado", "codigo"));
        map.put("ambientes", obterValoresLista(getMalaDiretaVO().getCfgEvento().getAmbientes(), "label", "selecionado", "codigo"));
        map.put("colaboradores", obterValoresLista(getMalaDiretaVO().getCfgEvento().getColaboradores(), "label", "selecionado", "codigo"));
        map.put("produtos", obterValoresLista(getMalaDiretaVO().getCfgEvento().getListaProdutosVOs(), "descricao", "selecionado", "codigo", true));
        map.put("codigoErroRemessa", getMalaDiretaVO().getCfgEvento().getCodigoErroRemessa());
        map.put("diasParcelasVencidasInicial", String.valueOf(getMalaDiretaVO().getCfgEvento().getDiasParcelasVencidasInicial()));
        map.put("diasParcelasVencidasFinal", String.valueOf(getMalaDiretaVO().getCfgEvento().getDiasParcelasVencidasFinal()));
        map.put("nrDiasInicioFreePass", String.valueOf(getMalaDiretaVO().getCfgEvento().getNrDiasInicioFreePass()));
        map.put("situacao", obterValoresLista(situacaoClienteTOs, "situacaoClienteEnum", "selecionado", "codigo"));

        map.put("todosDiasSemana", String.valueOf(getMalaDiretaVO().getAgendamento().getTodosDiasSemana()));
        map.put("diasSemana", obterValoresLista(getMalaDiretaVO().getAgendamento().getDiasSemana(), "label", "selecionado", "codigo"));

        return map;
    }

    public String obterValoresLista(List lista, String nome, String selecao, String ordenadoPor){
        return obterValoresLista(lista, nome, selecao, ordenadoPor, false);
    }
    public String obterValoresLista(List lista, String nome, String selecao, String ordenadoPor, Boolean todos){
        if(lista == null){
            return "";
        }
        String vs = "";

        lista = Ordenacao.ordenarLista(lista, ordenadoPor);
        for(Object v : lista){
            if(todos || Boolean.parseBoolean((String)UtilReflection.getValor(v, selecao))){
                vs += ","+ UtilReflection.getValor(v, nome);
            }
        }
        return vs.replaceFirst(",", "");
    }

    public Boolean malaPossuiTag(){
        String msg = null;
        if(getMalaDiretaVO() != null && getMalaDiretaVO().getMensagem() != null){
            msg = getMalaDiretaVO().getMensagem();
        }
        Boolean possuiTag = false;
        if(msg != null){
            for(String tag: MalaDiretaVO.TAGS) {
                possuiTag = msg.contains(tag);
                if(possuiTag){
                    break;
                }
            }
        }
        return possuiTag;
    }

    private void montarFiltrosSelecionados() throws Exception{
        inicializarListas();
        Uteis.marcarValoresBaseadoString(getCategoriaVOs(), "Selecionado", "Codigo", true, getMalaDiretaVO().getMailingFiltros().getCodigosCategoria());
        Uteis.marcarValoresBaseadoString(situacaoClienteTOs, "Selecionado", "Codigo", false, getMalaDiretaVO().getMailingFiltros().getListaSituacoes());
        Uteis.marcarValoresBaseadoString(getModalidadeVOs(), "Selecionado", "Codigo", true, getMalaDiretaVO().getMailingFiltros().getCodigosModalidades());
        Uteis.marcarValoresBaseadoString(consultoresVOs, "Selecionado", "Codigo", true, getMalaDiretaVO().getMailingFiltros().getCodigosConsultores());
        Uteis.marcarValoresBaseadoString(professoresVOs, "Selecionado", "Codigo", true, getMalaDiretaVO().getMailingFiltros().getCodigosProfessores());
        Uteis.marcarValoresBaseadoString(planoVOs, "Selecionado", "Codigo", true, getMalaDiretaVO().getMailingFiltros().getCodigosPlanos());
        Uteis.marcarValoresBaseadoString(contratoDuracaoVOs, "Selecionado", "NumeroMeses", true, getMalaDiretaVO().getMailingFiltros().getCodigoContratoDuracao());

        somenteRecorrencia = getMalaDiretaVO().getMailingFiltros().getSomenteRecorrencia();
        //Validando recorrencia checkada
        if (somenteRecorrencia){
            marcaDesmarcaRecorrencia();
        }

        inicioVencimento = getMalaDiretaVO().getMailingFiltros().getVencimentoContratoMin();
        fimVencimento = getMalaDiretaVO().getMailingFiltros().getVencimentoContratoMax();
        inicioDiasSemAparecer = getMalaDiretaVO().getMailingFiltros().getDiasSemComparecerMin();
        fimDiasSemAparecer = getMalaDiretaVO().getMailingFiltros().getDiasSemComparecerMax();


    }
    private void setarFiltrosSelecionados() throws Exception{
        getMalaDiretaVO().getMailingFiltros().setCodigosCategoria(Uteis.getListaEscolhidos(categoriaVOs, "Selecionado", "Codigo", true));
        getMalaDiretaVO().getMailingFiltros().setCodigosModalidades(Uteis.getListaEscolhidos(modalidadeVOs, "Selecionado", "Codigo", true));
        getMalaDiretaVO().getMailingFiltros().setCodigosConsultores(Uteis.getListaEscolhidos(consultoresVOs, "Selecionado", "Codigo", true));
        getMalaDiretaVO().getMailingFiltros().setCodigosProfessores(Uteis.getListaEscolhidos(professoresVOs, "Selecionado", "Codigo", true));
        getMalaDiretaVO().getMailingFiltros().setCodigosPlanos(Uteis.getListaEscolhidos(planoVOs, "Selecionado", "Codigo", true));
        getMalaDiretaVO().getMailingFiltros().setCodigoContratoDuracao(Uteis.getListaEscolhidos(contratoDuracaoVOs, "Selecionado", "NumeroMeses", true));
        getMalaDiretaVO().getMailingFiltros().setListaSituacoes(Uteis.getListaEscolhidos(situacaoClienteTOs, "Selecionado", "Codigo", false));
        getMalaDiretaVO().getMailingFiltros().setSomenteRecorrencia(somenteRecorrencia);
        getMalaDiretaVO().getMailingFiltros().setVencimentoContratoMin(inicioVencimento);
        getMalaDiretaVO().getMailingFiltros().setVencimentoContratoMin(inicioVencimento);
        getMalaDiretaVO().getMailingFiltros().setVencimentoContratoMax(fimVencimento);
        getMalaDiretaVO().getMailingFiltros().setDiasSemComparecerMin(inicioDiasSemAparecer);
        getMalaDiretaVO().getMailingFiltros().setDiasSemComparecerMax(fimDiasSemAparecer);

    }

    public void mostrarLog() throws Exception {
        MailingHistoricoVO obj = (MailingHistoricoVO) context().getExternalContext().getRequestMap().get("historico");
        labelModal = "Log";
        logSql = obj.getLog();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = malaDiretaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse("Contato em grupo");
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), malaDiretaVO.getCodigo(), 0);
    }

    public void mostrarSql() throws Exception {
        MailingHistoricoVO obj = (MailingHistoricoVO) context().getExternalContext().getRequestMap().get("historico");
        labelModal = "SQL";
        logSql = obj.getFiltro();
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>MalaDiretaVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(MalaDiretaVO obj) {
        if (obj.getRemetente() == null) {
            obj.setRemetente(new UsuarioVO());
        }
        if (obj.getModeloMensagem() == null) {
            obj.setModeloMensagem(new ModeloMensagemVO());
        }
    }

    public void setarRemetente() {
        UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("result");
        if (obj != null) {
            getMalaDiretaVO().setRemetente(obj);
        }
    }

    public int getQtdEmailsRestandoEnviarDia() {
        try {
            return (getConfiguracaoSistemaCRMVO().getLimiteDiarioEmails() - getFacade().getMalaDireta().countEmails(Calendario.hoje(), true, getConfiguracaoSistemaCRMVO().getIntegracaoPacto()));
        } catch (Exception e) {
            return 0;
        }
    }

    public int getQtdEmailsRestandoEnviarMensal() {
        try {
            return (getConfiguracaoSistemaCRMVO().getLimiteMensalPacto() - getFacade().getMalaDireta().countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, getConfiguracaoSistemaCRMVO().getIntegracaoPacto()));
        } catch (Exception e) {
            return 0;
        }
    }

    public String gravar() {
        try {
            setMsgAlert("");
            int qtdEmailsEnviar = 0;
            for(AmostraClienteTO amostraCliente : getListaAmostra()){
                qtdEmailsEnviar += amostraCliente.getEmails().split("<br/>").length;
            }
            if(getMalaDiretaVO().getMensagem().equals("") && getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT)){
                getMalaDiretaVO().setMensagem(MeioEnvio.GYMBOT.getDescricao());
            }

            if(getMalaDiretaVO().getMensagem().equals("") && getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT_PRO)){
                getMalaDiretaVO().setMensagem(MeioEnvio.GYMBOT_PRO.getDescricao());
            }

            if ((getMalaDiretaVO().getUrlwebhoobotconversa().equals("") || getMalaDiretaVO().getUrlwebhoobotconversa() == null)
                    && getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.GYMBOT)) {
                throw new ConsistirException("Para envio de um fluxo para o GymBot é obrigatório selecionar um fluxo!");
            }

            if(getMalaDiretaVO().getEmail() && (!UteisValidacao.emptyNumber(getConfiguracaoSistemaCRMVO().getLimiteDiarioEmails()) && qtdEmailsEnviar > (getConfiguracaoSistemaCRMVO().getLimiteDiarioEmails()
                            - getFacade().getMalaDireta().countEmails(Calendario.hoje(), true, getConfiguracaoSistemaCRMVO().getIntegracaoPacto()))) && !isSimEnviarSomenteSaldoRestanteDia()){
                return "LIMITE-DIARIO-ATINGIDO";
            }
            if(getMalaDiretaVO().getEmail() && (getConfiguracaoSistemaCRMVO().getIntegracaoPacto() &&
                    qtdEmailsEnviar > (getConfiguracaoSistemaCRMVO().getLimiteMensalPacto() - getFacade().getMalaDireta().countEmails(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), false, getConfiguracaoSistemaCRMVO().getIntegracaoPacto()))) && !isSimEnviarSomenteSaldoRestanteMes()){
                return "LIMITE-MENSAL-ATINGIDO";
            }
            setarFiltrosSelecionados();
            if (UteisValidacao.emptyNumber(getMalaDiretaVO().getEmpresa().getCodigo())) {
                throw new ConsistirException("Selecione uma empresa!");
            }

            if(getMalaDiretaVO().getCfgEvento().getApresentarDatas() &&
                    (getMalaDiretaVO().getCfgEvento().getInicio() == null
                    || getMalaDiretaVO().getCfgEvento().getFim() == null)){
                throw new ConsistirException("Informe corretamente o período da consulta!");
            }

            if (getMalaDiretaVO().getCfgEvento().isModeloPadraoBoleto()) {
                getMalaDiretaVO().setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                if (malaDiretaVO.getMensagem().contains("TAG_BOLETO")){
                    if (!getMalaDiretaVO().getConfiguracaoSistemaCRMVO().isEnviarEmailIndividualmente()) {
                        throw new ConsistirException("Para usar o envio de boletos por email (TAG PADRÃO BOLETO), a configuração \"Envio Individual de E-mail\" nas configurações do CRM (aba email) deve estar marcada.");
                    }
                }
            }

            if (getMalaDiretaVO().getEmail() &&
                    getMalaDiretaVO().getMensagem().contains("TAG_PESQUISA") &&
                    UteisValidacao.emptyNumber(getMalaDiretaVO().getQuestionario())) {
                throw new ConsistirException("A \"TAG_PESQUISA\" foi adicionada selecione uma pesquisa!");
            }

            if (getMalaDiretaVO().getEmail() &&
                    !getMalaDiretaVO().getMensagem().contains("TAG_PESQUISA") &&
                    !UteisValidacao.emptyNumber(getMalaDiretaVO().getQuestionario())) {
                throw new ConsistirException("Uma pesquisa foi selecionada, por favor adicione a \"TAG_PESQUISA\"!");
            }

            if(getMalaDiretaVO().getApp()){
                getMalaDiretaVO().setTipoPergunta(tipoMensagemApp);
            }
            if(getBotoesApp()){
                getMalaDiretaVO().setOpcoes(getCampos().toString());
            }else if(tipoMensagemApp.equals(TipoPerguntaEnum.DISSERTATIVA.getCodigo())){
                getMalaDiretaVO().setOpcoes("Confirma;TEXTO");
            }else{
                campos = new CamposGenericosTO();
                getMalaDiretaVO().setOpcoes("");
            }
            if (!isUsarAgendamento()) {
                if(getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.INCLUSAO_VISITANTE.getCodigo()) ||
                        getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.VENDA_CONTRATO.getCodigo()) ||
                        getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_MATRICULA.getCodigo()) ||
                        getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_REMATRICULA.getCodigo()) ||
                        getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_RENOVACAO.getCodigo()) ||
                        getMalaDiretaVO().getCfgEvento().getOcorrencia().equals(OcorrenciaEnum.APOS_CANCELAMENTO.getCodigo())
                        ){
                    getMalaDiretaVO().getAgendamento().setHoraInicio(0);
                    getMalaDiretaVO().getAgendamento().setHoraFim(23);
                }else{
                    getMalaDiretaVO().getAgendamento().setHoraInicio(getMalaDiretaVO().getHoraInicio());
                    getMalaDiretaVO().getAgendamento().setHoraFim(23);
                    getMalaDiretaVO().setVigenteAte(null);
                }
            } else if ((getMalaDiretaVO().getAgendamento().getHoraFim() - getMalaDiretaVO().getAgendamento().getHoraInicio()) < 4) {
                throw new ConsistirException("O Período do Horário de Envio deve ser superior a 4 horas");
            }

            if (getMalaDiretaVO().getAgendamentoPrevisto()) {
                if (getMalaDiretaVO().getDataEnvio() == null) {
                    throw new ConsistirException("Informe a data inicial do período de execução!");
                }
                if (getMalaDiretaVO().getVigenteAte() == null) {
                    throw new ConsistirException("Informe a data final do período de execução!");
                }
                if (getMalaDiretaVO().getVigenteAte() != null && !Calendario.menorOuIgual(getMalaDiretaVO().getDataEnvio(), getMalaDiretaVO().getVigenteAte())) {
                    throw new ConsistirException("Informe corretamente o período de execução!");
                }
            }
            if(getMalaDiretaVO().getAgendamento() != null){
                getMalaDiretaVO().getAgendamento().montarCron();
            }
            getFacade().getMailingAgendamento().montarSql(getMalaDiretaVO(), getMalaDiretaVO().getEmpresa().getCodigo(),
                    categoriaVOs, situacaoClienteTOs, modalidadeVOs,
                    consultoresVOs, professoresVOs, planoVOs, contratoDuracaoVOs, convenioCobrancaVO,  null, null, null, null,true);

            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo()));

            //bloquear termos spam
            if(getConfiguracaoSistemaCRMVO().getBloquearTermoSpam()){
                String verificarTermosSpamNoTitulo = getMalaDiretaVO().verificarTermosSpam(getConfiguracaoSistemaCRMVO());
                if (!verificarTermosSpamNoTitulo.isEmpty()) {
                    setMensagemID("msg_mail_termobloqueado");
                    String msg = getMensagem() + " " + verificarTermosSpamNoTitulo + ".";
                    limparMsg();
                    throw new Exception(msg);
                }
            }
            //verificar remetente ou setar remetente como usuario logado
            if (getMalaDiretaVO().getRemetente().getNome().equals("")) {
                if (getConfiguracaoSistemaCRMVO().getRemetentePadraoMailing() != null && getConfiguracaoSistemaCRMVO().getRemetentePadraoMailing().getCodigo() != 0) {
                    getMalaDiretaVO().setRemetente(getFacade().getUsuario().consultarPorChavePrimaria(getConfiguracaoSistemaCRMVO().getRemetentePadraoMailing().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
                } else {
                    throw new ConsistirException("Por favor insira um remetente padrão para o Mailing");
                }
            } else {
                getMalaDiretaVO().setRemetente(
                        UteisValidacao.emptyNumber(malaDiretaVO.getRemetente().getCodigo()) ?
                        getFacade().getUsuario().consultarPorNomeUsuario(malaDiretaVO.getRemetente().getNome(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA) :
                        getFacade().getUsuario().consultarPorChavePrimaria(malaDiretaVO.getRemetente().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA)
                        );
            }

            if(!UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getEventoCodigo())
                    && UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getOcorrencia())) {
                throw new ConsistirException("Informe corretamente a ocorrência do mailing!");

            }

            if(getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.SMS)){
                if(!( getMalaDiretaVO().getMensagem().contains("TAG_NOME") || getMalaDiretaVO().getMensagem().contains("TAG_PNOME")) ){
                    throw new ConsistirException("Para envio de SMS é obrigatório incluir uma das TAGS [TAG_PNOME ou TAG_NOME], para que o envio não seja bloqueado pela política de anti-spam das operadoras!");
                }
            }

            getMalaDiretaVO().setUsuarioVO(getUsuarioLogado());
            if(getMalaDiretaVO().getEnvioHabilitado()){
                getFacade().getMalaDireta().agendarEnvio(getMalaDiretaVO(), Calendario.hoje(), getMalaDiretaVO().getEmpresa());
                updateJenkinsService(getMalaDiretaVO(), configCRM);
                getMalaDiretaVO().setNovoObj(false);
            }else{
                if(getMalaDiretaVO().getNovoObj()){
                    getFacade().getMalaDireta().incluir(getMalaDiretaVO());
                    getMalaDiretaVO().setNovoObj(false);
                }else{
                    getFacade().getMalaDireta().alterar(getMalaDiretaVO());
                }
            }
            gravarLog();
            setMensagemID("msg_enviar_email");
            setSucesso(true);
            setErro(false);
            montarMsgAlert(getMensagem());
            alterar();
            if (isExibirReplicarRedeEmpresa()) {
                prepararListaReplicarEmpresa();
                replicarAutomaticoTodas();
            }
            return "OK";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemID("msg_enviar_emailErro");
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            return getMensagemDetalhada();
        }
    }

    public void habilitarEnvio(MalaDiretaVO obj) throws Exception{
        getFacade().getMalaDireta().habilitarEnvio(obj);
        getFacade().getMalaDireta().agendarEnvio(obj, Calendario.hoje(), obj.getEmpresa());
        updateJenkinsService(obj, configCRM);
    }

    private void gravarLog() throws Exception {
        Map<String, String> novosValores = povoarMapaValoresParaLog(false);
        for(String k : novosValores.keySet()){
            if(((!UteisValidacao.emptyString(novosValores.get(k)) && (mapaLog.get(k) == null || mapaLog.get(k).isEmpty()))
                    || (!UteisValidacao.emptyString(mapaLog.get(k)) && UteisValidacao.emptyString(novosValores.get(k)))
                    || !novosValores.get(k).equals(mapaLog.get(k)))
                    && !((novosValores.get(k).equals("null") || novosValores.get(k).equals("0.0")) && (mapaLog.get(k) == null || mapaLog.get(k).isEmpty()))){
                LogVO logAlt = new LogVO();
                logAlt.setNomeEntidade("MALADIRETA");
                logAlt.setNomeEntidadeDescricao("Mailing");
                logAlt.setNomeCampo(k);
                logAlt.setChavePrimaria(malaDiretaVO.getCodigo().toString());
                logAlt.setDataAlteracao(Calendario.hoje());
                logAlt.setResponsavelAlteracao(getUsuarioLogado().getNome());
                logAlt.setUserOAMD(getUsuarioLogado().getUserOamd());
                logAlt.setValorCampoAnterior(mapaLog.get(k));
                logAlt.setValorCampoAlterado(novosValores.get(k));
                if (getMalaDiretaVO().getNovoObj()) {
                    logAlt.setOperacao("INCLUSÃO");
                    logAlt.setValorCampoAnterior("");
                } else {
                    logAlt.setOperacao("ALTERAÇÃO");
                }
                if(!UteisValidacao.emptyString(logAlt.getValorCampoAlterado())
                        || !UteisValidacao.emptyString(logAlt.getValorCampoAnterior())){
                    getFacade().getLog().incluir(logAlt);
                }

            }
        }
        inicializarLog(false);
    }

    public void consultarPaginadoInit() {
        try {
            super.consultar();
            setConfPaginacao(new ConfPaginacao());
            getConfPaginacao().setPaginarBanco(true);
            consultarPaginado();
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP malaDiretaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public void consultarPaginado() {
        try {
            super.consultar();
            List<MalaDiretaVO> objs = getFacade().getMalaDireta().consultarPaginado(codigoMailing, getConfPaginacao(), dataInicial, dataFinal,
                    consultarDescricao, dataInicialCriacao, dataFinalCriacao, consultarRemetente,
                    consultarMeioEnvio, codigoTipoAgendamento,
                    getAgendamentoOuTodos() ? tipoVigencia : null,
                    getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_TELACONSULTA);

           Ordenacao.ordenarLista(objs, "ultimaExecucao");
            Collections.reverse(objs);
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }


    public void consultarMalaDireta() {
        try {

            List<MalaDiretaVO> objs = getFacade().getMalaDireta().consultar(codigoMailing,  dataInicial, dataFinal,
                    consultarDescricao, dataInicialCriacao, dataFinalCriacao, consultarRemetente,
                    consultarMeioEnvio, codigoTipoAgendamento,
                    getAgendamentoOuTodos() ? tipoVigencia : null,
                    getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_TELACONSULTA,getControleConsulta().getLimitePorPagina(),getControleConsulta().getOffset(),getCampoOrdenar(),getTipoOrdernacao());

            int totalRegistros = getFacade().getMalaDireta().totalizadorMalaDireta(codigoMailing, dataInicial, dataFinal,
                    consultarDescricao, dataInicialCriacao, dataFinalCriacao, consultarRemetente,
                    consultarMeioEnvio, codigoTipoAgendamento,getAgendamentoOuTodos() ? tipoVigencia : null, getFiltroEmpresa());

            setListaConsulta(objs);
            getControleConsulta().setTotalRegistrosEncontrados(totalRegistros);

            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsulta(new ArrayList(0));
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }




    public List<MalaDiretaVO> consultarTodos() throws Exception {
        ConfPaginacao confPaginacaoTodos = new ConfPaginacao();
        confPaginacaoTodos.setPaginarBanco(false);

        return getFacade().getMalaDireta().consultarPaginado(codigoMailing, confPaginacaoTodos, dataInicial, dataFinal,
                consultarDescricao, dataInicialCriacao, dataFinalCriacao, consultarRemetente,
                consultarMeioEnvio, codigoTipoAgendamento, tipoVigencia, getFiltroEmpresa(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }


    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>malaDiretaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getMalaDireta().excluir(malaDiretaVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>malaDiretaEnviadaVO</code>
     * para o objeto <code>malaDiretaVO</code> da classe <code>malaDireta</code>
     */
    public void adicionarMalaDiretaEnviada() throws Exception {
        try {
            //Limpando as variaveis de mensagem
            limparMsg();

            if (!malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome().equals("")) {
                malaDiretaEnviadaVO.setClienteVO(getFacade().getCliente().consultarPorNomeCliente(malaDiretaEnviadaVO.getClienteVO().getPessoa().getNome(), false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA));
            }
            //se for por meio de envio EMAIL
            if (malaDiretaVO.getMeioDeEnvioEnum() == MeioEnvio.EMAIL) {
                //Verificando se o cliente selecionado possui e-mail
                if ((malaDiretaEnviadaVO.getEmails() == null) || ("".equals(malaDiretaEnviadaVO.getEmails()))) {
                    throw new Exception("Pessoa não possui e-mail cadastrado !");
                }

                //se for por meio de envio SMS
            } else if (malaDiretaVO.getMeioDeEnvioEnum() == MeioEnvio.SMS) {
                if ((malaDiretaEnviadaVO.getTelefones() == null) || ("".equals(malaDiretaEnviadaVO.getTelefones()))) {
                    throw new Exception("Pessoa não possui telefone celular cadastrado !");
                }
            }

            if (!getMalaDiretaVO().getCodigo().equals(new Integer(0))) {
                malaDiretaEnviadaVO.setMalaDiretaVO(getMalaDiretaVO());
            }
            getMalaDiretaVO().adicionarObjMalaDiretaEnviadaVOs(getMalaDiretaEnviadaVO());
            this.setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void listarEmailPessoa() {
        MalaDiretaEnviadaVO obj = (MalaDiretaEnviadaVO) context().getExternalContext().getRequestMap().get("malaDiretaEnviada");
        setListaEmail(obj.getClienteVO().getPessoa().getEmailVOs());

    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>MalaDiretaEnviadaVO</code>
     * para edição pelo usuário.
     */
    public String editarMalaDiretaEnviada() throws Exception {
        MalaDiretaEnviadaVO obj = (MalaDiretaEnviadaVO) context().getExternalContext().getRequestMap().get("malaDiretaEnviada");
        setMalaDiretaEnviadaVO(obj);
        return "editar";
    }

    /* Método responsável por remover um novo objeto da classe <code>MalaDiretaEnviadaVO</code>
     * do objeto <code>malaDiretaVO</code> da classe <code>malaDireta</code>
     */
    public String removerMalaDiretaEnviada() throws Exception {
        MalaDiretaEnviadaVO obj = (MalaDiretaEnviadaVO) context().getExternalContext().getRequestMap().get("malaDiretaEnviada");
        getMalaDiretaVO().excluirObjMalaDiretaEnviadaVOs(obj.getClienteVO().getPessoa().getCodigo());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Pessoa</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados no richModal
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public void consultarPessoa() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarPessoa().equals("codigo")) {
                if (getValorConsultarPessoa().equals("")) {
                    setValorConsultarPessoa("0");
                }
                Integer valorInt = Integer.parseInt(getValorConsultarPessoa());
                objs = getFacade().getPessoa().consultarPorCodigo(valorInt, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            if (getCampoConsultarPessoa().equals("descricaoProfissao")) {
                objs = getFacade().getPessoa().consultarPorDescricaoProfissao(getValorConsultarPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (getCampoConsultarPessoa().equals("nome")) {
                objs = getFacade().getPessoa().consultarPorNome(getValorConsultarPessoa(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (getCampoConsultarPessoa().equals("nomeCidade")) {
                objs = getFacade().getPessoa().consultarPorNomeCidade(getValorConsultarPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            setListaConsultarPessoa(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarPessoa(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarPessoa() throws Exception {
        PessoaVO obj = (PessoaVO) context().getExternalContext().getRequestMap().get("pessoa");
        if (getMensagemDetalhada().equals("")) {
            this.getMalaDiretaEnviadaVO().getClienteVO().setPessoa(obj);
        }
        Uteis.liberarListaMemoria(this.getListaConsultarPessoa());
        this.setValorConsultarPessoa(null);
        this.setCampoConsultarPessoa(null);
    }

    public void limparCampoPessoa() {
        this.getMalaDiretaEnviadaVO().getClienteVO().setPessoa(new PessoaVO());
    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da telas.
     */
    public List getTipoConsultarComboPessoa() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        itens.add(new SelectItem("dataCadastro", "Data de Cadastro"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("dataNasc", "Data de Nascimento"));
        itens.add(new SelectItem("cpf", "Cpf"));
        itens.add(new SelectItem("nomeCidade", "Cidade"));
        return itens;
    }

    /**
     * Método responsável por processar a consulta na entidade <code>ModeloMensagem</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados no richModal
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public void consultarModeloMensagem() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarModeloMensagem().equals("codigo")) {
                if (getValorConsultarModeloMensagem().equals("")) {
                    setValorConsultarModeloMensagem("0");
                }
                Integer valorInt = Integer.parseInt(getValorConsultarModeloMensagem());
                objs = getFacade().getModeloMensagem().consultarPorCodigo(valorInt, getMalaDiretaVO().getMeioDeEnvioEnum(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultarModeloMensagem().equals("titulo")) {
                objs = getFacade().getModeloMensagem().consultarPorTitulo(getValorConsultarModeloMensagem(), getMalaDiretaVO().getMeioDeEnvioEnum(), false, Uteis.NIVELMONTARDADOS_TODOS);
            }
            setListaConsultarModeloMensagem(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarModeloMensagem(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarModeloMensagem() throws Exception {
        ModeloMensagemVO obj = (ModeloMensagemVO) context().getExternalContext().getRequestMap().get("modeloMensagem");
        //caso exista imagem de upload
        if (!UteisValidacao.emptyString(obj.getNomeImagem())) {
            String nomeImagem = obj.getNomeImagem();
            //retirar acentos e espaços
            nomeImagem = Uteis.retirarAcentuacaoRegex(nomeImagem.replaceAll(" ", ""));
            //modificar o nome da imagem no caminho da mensagem 
            if (!nomeImagem.equals(obj.getNomeImagem())) {
                //obter extensao da imagem
                String[] extensao = nomeImagem.split("\\.");
                Pattern verificaMensagem = Pattern.compile("imagensCRM/email/tmp/.*?." + extensao[extensao.length - 1]);
                Matcher m = verificaMensagem.matcher(obj.getMensagem());
                // enquanto o Matcher encontrar o pattern na String fornecida:
                while (m.find()) {
                    obj.setMensagem(obj.getMensagem().replaceAll(m.group(), "imagensCRM/email/tmp/" + nomeImagem));
                    obj.setNomeImagem(nomeImagem);
                }
            }
        }

        obj.verificarSeExisteImagemModelo(false, getKey());

        if (getMensagemDetalhada().equals("")) {
            EmpresaVO empresaVO = new EmpresaVO();
            if (UteisValidacao.emptyNumber(getMalaDiretaVO().getEmpresa().getCodigo())) {
                try {
                    empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getMalaDiretaVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
                } catch (Exception e) {
                    Uteis.logar(e, MalaDiretaControle.class);
                }
            }
            this.getMalaDiretaVO().setModeloMensagem(obj);
            this.getMalaDiretaVO().getModeloMensagem().setarLogoEmpresa(empresaVO);
            this.getMalaDiretaVO().setMensagem(this.getMalaDiretaVO().getModeloMensagem().getMensagem());
        }
        Uteis.liberarListaMemoria(this.getListaConsultarModeloMensagem());
        this.setValorConsultarModeloMensagem(null);
        this.setCampoConsultarModeloMensagem(null);
    }

    public void limparCampoModeloMensagem() {
        this.getMalaDiretaVO().setModeloMensagem(new ModeloMensagemVO());
        this.getMalaDiretaVO().setMensagem("");
    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da telas.
     */
    public List getTipoConsultarComboModeloMensagem() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Codigo"));
        itens.add(new SelectItem("titulo", "Título"));
        return itens;
    }

    /**
     * Método responsável por processar a consulta na entidade <code>Colaborador</code> por meio dos parametros informados no richmodal.
     * Esta rotina é utilizada fundamentalmente por requisições Ajax, que realizam busca pelos parâmentros informados no richModal
     * montando automaticamente o resultado da consulta para apresentação.
     */
    public void consultarRemetente() {
        try {
            List objs = new ArrayList();
            setListaConsultarRemetente(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsultarRemetente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarRemetente() throws Exception {
        UsuarioVO obj = (UsuarioVO) context().getExternalContext().getRequestMap().get("colaborador");
        if (getMensagemDetalhada().equals("")) {
            this.getMalaDiretaVO().setRemetente(obj);
        }
        Uteis.liberarListaMemoria(this.getListaConsultarRemetente());
        this.setValorConsultarRemetente(null);
        this.setCampoConsultarRemetente(null);
    }

    public List<UsuarioVO> executarAutocompleteRemetente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarTodosUsuarioComLimite(false, Uteis.NIVELMONTARDADOS_TELACONSULTA, getMalaDiretaVO().getEmpresa().getCodigo());
            } else {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNomeUsuarioComLimite(pref, getMalaDiretaVO().getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
        }
        return result;
    }

    public List<PessoaVO> executarAutocompletePessoa(Object suggest) {
        String pref = (String) suggest;
        ArrayList<PessoaVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarTodosPessoaComLimite(false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPorNomePessoaComLimite(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
        }
        return result;
    }

    public void limparCampoRemetente() {
        this.getMalaDiretaVO().setRemetente(new UsuarioVO());
    }

    /**
     * Rotina responsável por preencher a combo de consulta dos RichModal da telas.
     */
    public List getTipoConsultarComboRemetente() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nomePessoa", "Pessoa"));
        itens.add(new SelectItem("situacao", "Situação"));
        itens.add(new SelectItem("codAcesso", "Código Acesso"));
        return itens;
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        return TipoAgendamentoEnum.getListaCombo();
    }

    public void executarInsercaoTag() {
        try {
            MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("marcadorEmail");
            getMalaDiretaVO().setMensagem(MalaDireta.executarInsercaoTag(getMalaDiretaVO().getMensagem(), obj.getTag(), true));
        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoValor</code>
     */
    public List<MarcadorVO> getListaSelectItemMarcadoEmail() throws Exception {
        List<MarcadorVO> objs = new ArrayList<>();
        MarcadorVO marcador = new MarcadorVO();
        for (MarcadoresEmailEnum mEE : MarcadoresEmailEnum.values()) {
            marcador.setTag(mEE.getTag());
            marcador.setNome(mEE.getDescricao());
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        return objs;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() throws Exception {
        consultarInicial();
        setMensagemID("");
        return "consultar";
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides
     * 07/05/2013
     */
    public void consultarInicial() throws Exception {
        setConfPaginacao(new ConfPaginacao());
        consultarMalaDireta();
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
     * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente
     * quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        malaDiretaVO = null;
        malaDiretaEnviadaVO = null;
        clientes = null;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            configCRM = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setConfiguracaoSistemaCRMVO(configCRM);
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String getCampoConsultarPessoa() {
        return campoConsultarPessoa;
    }

    public void setCampoConsultarPessoa(String campoConsultarPessoa) {
        this.campoConsultarPessoa = campoConsultarPessoa;
    }

    public String getValorConsultarPessoa() {
        return valorConsultarPessoa;
    }

    public void setValorConsultarPessoa(String valorConsultarPessoa) {
        this.valorConsultarPessoa = valorConsultarPessoa;
    }

    public List getListaConsultarPessoa() {
        return listaConsultarPessoa;
    }

    public void setListaConsultarPessoa(List listaConsultarPessoa) {
        this.listaConsultarPessoa = listaConsultarPessoa;
    }

    public MalaDiretaEnviadaVO getMalaDiretaEnviadaVO() {
        return malaDiretaEnviadaVO;
    }

    public void setMalaDiretaEnviadaVO(MalaDiretaEnviadaVO malaDiretaEnviadaVO) {
        this.malaDiretaEnviadaVO = malaDiretaEnviadaVO;
    }

    public String getCampoConsultarModeloMensagem() {
        return campoConsultarModeloMensagem;
    }

    public void setCampoConsultarModeloMensagem(String campoConsultarModeloMensagem) {
        this.campoConsultarModeloMensagem = campoConsultarModeloMensagem;
    }

    public String getValorConsultarModeloMensagem() {
        return valorConsultarModeloMensagem;
    }

    public void setValorConsultarModeloMensagem(String valorConsultarModeloMensagem) {
        this.valorConsultarModeloMensagem = valorConsultarModeloMensagem;
    }

    public List getListaConsultarModeloMensagem() {
        return listaConsultarModeloMensagem;
    }

    public void setListaConsultarModeloMensagem(List listaConsultarModeloMensagem) {
        this.listaConsultarModeloMensagem = listaConsultarModeloMensagem;
    }

    public String getCampoConsultarRemetente() {
        return campoConsultarRemetente;
    }

    public void setCampoConsultarRemetente(String campoConsultarRemetente) {
        this.campoConsultarRemetente = campoConsultarRemetente;
    }

    public String getValorConsultarRemetente() {
        return valorConsultarRemetente;
    }

    public void setValorConsultarRemetente(String valorConsultarRemetente) {
        this.valorConsultarRemetente = valorConsultarRemetente;
    }

    public List getListaConsultarRemetente() {
        return listaConsultarRemetente;
    }

    public void setListaConsultarRemetente(List listaConsultarRemetente) {
        this.listaConsultarRemetente = listaConsultarRemetente;
    }

    public MalaDiretaVO getMalaDiretaVO() {
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    /**
     * @return the listaEmail
     */
    public List getListaEmail() {
        return listaEmail;
    }

    /**
     * @param listaEmail the listaEmail to set
     */
    public void setListaEmail(List listaEmail) {
        this.listaEmail = listaEmail;
    }

    public Boolean getApresentarCalendarDia() {
        return getControleConsulta().getCampoConsulta().equals("dataenvio");
    }

    /**
     * @return the dataConsulta
     */
    public Date getDataConsulta() {
        if (dataConsulta == null) {
            return negocio.comuns.utilitarias.Calendario.hoje();
        }
        return dataConsulta;
    }

    /**
     * @param dataConsulta the dataConsulta to set
     */
    public void setDataConsulta(Date dataConsulta) {
        this.dataConsulta = dataConsulta;
    }

    /**
     * @return the dataInicial
     */
    public Date getDataInicial() {
        return dataInicial;
    }

    /**
     * @param dataInicial the dataInicial to set
     */
    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    /**
     * @return the dataFinal
     */
    public Date getDataFinal() {
        return dataFinal;
    }

    /**
     * @param dataFinal the dataFinal to set
     */
    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public List<SelectItem> getListaSelectItemOcorrencia() {
        listaSelectItemOcorrencia = listaSelectItemOcorrencia == null ? new ArrayList<>() : listaSelectItemOcorrencia;
        return listaSelectItemOcorrencia;
    }

    public void setListaSelectItemOcorrencia(List<SelectItem> listaSelectItemOcorrencia) {
        this.listaSelectItemOcorrencia = listaSelectItemOcorrencia;
    }

    private void montarListaOcorrencia() {
        this.listaSelectItemOcorrencia = new ArrayList<>();
        for (OcorrenciaEnum ocorrencia : OcorrenciaEnum.values()) {
            if (ocorrencia.isApresentar()) {
                this.listaSelectItemOcorrencia.add(new SelectItem(ocorrencia.getCodigo(), ocorrencia.getDescricao()));
            }
        }
    }

    private void montarTipos(boolean iniciar) {
        semanalmente = false;
        mensalmente = false;
        umavez = iniciar;
        diariamente = false;
    }

    public void alterarTipo() throws IOException {
        montarTipos(false);
        setarOcorrencia();
        if (getMalaDiretaVO().getAgendamento().getOcorrencia() == null) {
            return;
        }

        if (getMalaDiretaVO().isCrmExtra()) {
            getMalaDiretaVO().getAgendamento().setOcorrencia(OcorrenciaEnum.INSTANTANEO);
        }

        switch (getMalaDiretaVO().getAgendamento().getOcorrencia()) {
            case INSTANTANEO:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO);
                umavez = true;
                setUsarAgendamento(false);
                break;
            case DIARIAMENTE:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                diariamente = true;
                setUsarAgendamento(true);
                break;
            case MENSALMENTE:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                mensalmente = true;
                setUsarAgendamento(true);
                break;
            case SEMANALMENTE:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                semanalmente = true;
                setUsarAgendamento(true);
                break;
            case INCLUSAO_VISITANTE:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                setUsarHorarioEnvio(false);
                setUsarAgendamento(true);
                break;
            case VENDA_CONTRATO:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                setUsarHorarioEnvio(false);
                setUsarAgendamento(true);
                break;
            case APOS_MATRICULA:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                setUsarHorarioEnvio(false);
                setUsarAgendamento(true);
                break;
            case APOS_REMATRICULA:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                setUsarHorarioEnvio(false);
                setUsarAgendamento(true);
                break;
            case APOS_RENOVACAO:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                setUsarHorarioEnvio(false);
                setUsarAgendamento(true);
                break;
            case APOS_CANCELAMENTO:
                getMalaDiretaVO().setTipoAgendamento(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO);
                setUsarHorarioEnvio(false);
                setUsarAgendamento(true);
                break;
        }
    }

    public Integer getOcorrenciaSelecionada() {
        return ocorrenciaSelecionada;
    }

    public void setOcorrenciaSelecionada(Integer ocorrenciaSelecionada) {
        this.ocorrenciaSelecionada = ocorrenciaSelecionada;
    }

    public boolean getMensalmente() {
        return mensalmente;
    }

    public void setMensalmente(boolean mensalmente) {
        this.mensalmente = mensalmente;
    }

    public boolean getSemanalmente() {
        return semanalmente;
    }

    public void setSemanalmente(boolean semanalmente) {
        this.semanalmente = semanalmente;
    }

    public boolean getDiariamente() {
        return diariamente;
    }

    public void setDiariamente(boolean diariamente) {
        this.diariamente = diariamente;
    }

    public boolean getUmavez() {
        return umavez;
    }

    public void setUmavez(boolean umavez) {
        this.umavez = umavez;
    }

    public List<SelectItem> getListaSelectItemEventos() {
        if (listaSelectItemEventos == null) {
            listaSelectItemEventos  = new ArrayList<>();
        }

        return listaSelectItemEventos;
    }

    public void setListaSelectItemEventos(List<SelectItem> listaSelectItemEventos) {
        this.listaSelectItemEventos = listaSelectItemEventos;
    }

    public List<SelectItem> getListaSelectItemTipoAgendamento() {
        return listaSelectItemTipoAgendamento;
    }

    public void setListaSelectItemTipoAgendamento(List<SelectItem> listaSelectItemTipoAgendamento) {
        this.listaSelectItemTipoAgendamento = listaSelectItemTipoAgendamento;
    }

    public boolean contem(FiltroMailingEnum filtro) {
        return UteisValidacao.emptyNumber(getMalaDiretaVO().getEvento()) || filtro.getContem(TipoEventoEnum.obter(getMalaDiretaVO().getEvento()));
    }

    public boolean getMostrarSituacao() {
        return contem(FiltroMailingEnum.SITUACAO);
    }

    public boolean getMostrarCategoria() {
        return contem(FiltroMailingEnum.CATEGORIA);
    }

    public boolean getMostrarVinculo() {
        return contem(FiltroMailingEnum.VINCULO);
    }

    public boolean getMostrarModalidade() {
        return contem(FiltroMailingEnum.MODALIDADE);
    }

    public boolean getMostrarDuracao() {
        return contem(FiltroMailingEnum.DURACAO);
    }

    public boolean getMostrarEvento() {
        return contem(FiltroMailingEnum.CATEGORIA);
    }

    public String getLabelModal() {
        return labelModal;
    }

    public void setLabelModal(String labelModal) {
        this.labelModal = labelModal;
    }

    public String getLogSql() {
        return logSql;
    }

    public void setLogSql(String logSql) {
        this.logSql = logSql;
    }

    public void consultarPaginadoHistorico() {
        try {
            super.consultar();
            getMalaDiretaVO().setHistorico(getFacade().getMailingHistorico().consultarPaginado(getMalaDiretaVO().getCodigo(), confPaginacaoHistorico));
            this.getConfPaginacaoHistorico().definirVisibilidadeLinksNavegacao();
        } catch (Exception e) {
            getMalaDiretaVO().setHistorico(new ArrayList<>());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void consultarHistorico() {
        try {
            super.consultar();
            getMalaDiretaVO().setHistorico(getFacade().getMailingHistorico().consultar(getMalaDiretaVO().getCodigo()));
        } catch (Exception e) {
            getMalaDiretaVO().setHistorico(new ArrayList<>());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void consultarPaginadoListenerHistorico(ActionEvent evt) {
        //==================================================================================================================================


        //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacaoHistorico(new ConfPaginacao());
            }
        }

        //Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacaoHistorico().setPagNavegacao(component.toString());
        }

        //==================================================================================================================================
        consultarPaginadoHistorico();
    }

    public String getLabelData() {
        return "Última Execução";
    }

    public Object alterar() throws Exception {
        setCriarEditarMailing(!isCriarEditarMailing());
        return isCriarEditarMailing();
    }

    public Object limparPeriodoExecucao() {
        setDataInicial(null);
        setDataFinal(null);
        return true;
    }

    public Object limparPeriodoCriacao() {
        setDataInicialCriacao(null);
        setDataFinalCriacao(null);
        return true;
    }

    public Object limparPeriodoExecucaoForm() {
        getMalaDiretaVO().setDataEnvio(null);
        getMalaDiretaVO().setVigenteAte(null);
        return true;
    }

    public ConfPaginacao getConfPaginacaoHistorico() {
        return confPaginacaoHistorico;
    }

    public void setConfPaginacaoHistorico(ConfPaginacao confPaginacaoHistorico) {
        this.confPaginacaoHistorico = confPaginacaoHistorico;
    }

    public Integer getCodigoTipoAgendamento() {
        if(codigoTipoAgendamento == null){
            codigoTipoAgendamento = TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo();
        }
        return codigoTipoAgendamento;
    }

    public void setCodigoTipoAgendamento(Integer codigoTipoAgendamento) {
        this.codigoTipoAgendamento = codigoTipoAgendamento;
    }

    public boolean getConsultaAgendamentos() {
        return getCodigoTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo());
    }

    public boolean getConsultaAvulsos() {
        return getCodigoTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo());
    }

    public String getConsultarDescricao() {
        return consultarDescricao;
    }

    public void setConsultarDescricao(String consultarDescricao) {
        this.consultarDescricao = consultarDescricao;
    }

    public String getConsultarRemetente() {
        return consultarRemetente;
    }

    public void setConsultarRemetente(String consultarRemetente) {
        this.consultarRemetente = consultarRemetente;
    }

    public Integer getConsultarMeioEnvio() {
        return consultarMeioEnvio;
    }

    public void setConsultarMeioEnvio(Integer consultarMeioEnvio) {
        this.consultarMeioEnvio = consultarMeioEnvio;
    }

    public ConfPaginacao getConfPaginacaoClientes() {
        return confPaginacaoClientes;
    }

    public void setConfPaginacaoClientes(ConfPaginacao confPaginacaoClientes) {
        this.confPaginacaoClientes = confPaginacaoClientes;
    }

    public Integer getCodigoMailing() {
        return codigoMailing;
    }

    public void setCodigoMailing(Integer codigoMailing) {
        this.codigoMailing = codigoMailing;
    }

    public boolean isCriarEditarMailing() {
        return criarEditarMailing;
    }

    public void setCriarEditarMailing(boolean criarEditarMailing) {
        this.criarEditarMailing = criarEditarMailing;
    }

    public boolean isUsarAgendamento() {
        return usarAgendamento;
    }

    public void setUsarAgendamento(boolean usarAgendamento) {
        this.usarAgendamento = usarAgendamento;
    }

    public String getAbaAtual() {
        return abaAtual;
    }

    public void setAbaAtual(String abaAtual) {
        this.abaAtual = abaAtual;
    }

    public Date getDataInicialCriacao() {
        return dataInicialCriacao;
    }

    public void setDataInicialCriacao(Date dataInicialCriacao) {
        this.dataInicialCriacao = dataInicialCriacao;
    }

    public Date getDataFinalCriacao() {
        return dataFinalCriacao;
    }

    public void setDataFinalCriacao(Date dataFinalCriacao) {
        this.dataFinalCriacao = dataFinalCriacao;
    }

    public boolean isMostrarCategorias() {
        return mostrarCategorias;
    }

    public void setMostrarCategorias(boolean mostrarCategorias) {
        this.mostrarCategorias = mostrarCategorias;
    }

    public void toggleCategorias() {
        setMostrarCategorias(!isMostrarCategorias());
    }

    public boolean isMostrarSituacoes() {
        return mostrarSituacoes;
    }

    public void setMostrarSituacoes(boolean mostrarSituacoes) {
        this.mostrarSituacoes = mostrarSituacoes;
    }

    public void toggleSituacoes() {
        setMostrarSituacoes(!isMostrarSituacoes());
    }

    public boolean isMostrarColaboradores() {
        return mostrarColaboradores;
    }

    public void setMostrarColaboradores(boolean mostrarColaboradores) {
        this.mostrarColaboradores = mostrarColaboradores;
    }

    public void toggleColaboradores() {
        setMostrarColaboradores(!isMostrarColaboradores());
    }

    public boolean isMostrarModalidades() {
        return mostrarModalidades;
    }

    public void setMostrarModalidades(boolean mostrarModalidades) {
        this.mostrarModalidades = mostrarModalidades;
    }

    public void toggleModalidades() {
        setMostrarModalidades(!isMostrarModalidades());
    }

    public boolean isMostrarConsultores() {
        return mostrarConsultores;
    }

    public void setMostrarConsultores(boolean mostrarConsultores) {
        this.mostrarConsultores = mostrarConsultores;
    }

    public void toggleConsultores() {
        setMostrarConsultores(!isMostrarConsultores());
    }

    public boolean isMostrarProfessores() {
        return mostrarProfessores;
    }

    public void setMostrarProfessores(boolean mostrarProfessores) {
        this.mostrarProfessores = mostrarProfessores;
    }

    public void toggleProfessores() {
        setMostrarProfessores(!isMostrarProfessores());
    }

    public List<CategoriaVO> getCategoriaVOs() {
        return categoriaVOs;
    }

    public void setCategoriaVOs(List<CategoriaVO> categoriaVOList) {
        this.categoriaVOs = categoriaVOList;
    }

    public List<SituacaoClienteTO> getSituacaoClienteTOs() {
        return situacaoClienteTOs;
    }

    public void setSituacaoClienteTOs(List<SituacaoClienteTO> situacaoClienteTOs) {
        this.situacaoClienteTOs = situacaoClienteTOs;
    }

    public List<ModalidadeVO> getModalidadeVOs() {
        return modalidadeVOs;
    }

    public void setModalidadeVOs(List<ModalidadeVO> modalidadeVOs) {
        this.modalidadeVOs = modalidadeVOs;
    }

    public List<ColaboradorVO> getConsultoresVOs() {
        return consultoresVOs;
    }

    public void setConsultoresVOs(List<ColaboradorVO> consultoresVOs) {
        this.consultoresVOs = consultoresVOs;
    }

    public List<ColaboradorVO> getProfessoresVOs() {
        return professoresVOs;
    }

    public void setProfessoresVOs(List<ColaboradorVO> professoresVOs) {
        this.professoresVOs = professoresVOs;
    }

    public boolean isMostrarDadosCadastrais() {
        return mostrarDadosCadastrais;
    }

    public void setMostrarDadosCadastrais(boolean mostrarDadosCadastrais) {
        this.mostrarDadosCadastrais = mostrarDadosCadastrais;
    }

    public void toggleDadosCadastrais() {
        setMostrarDadosCadastrais(!isMostrarDadosCadastrais());
    }

    public boolean isMostrarDadosPlanos() {
        return mostrarDadosPlanos;
    }

    public void setMostrarDadosPlanos(boolean mostrarDadosPlanos) {
        this.mostrarDadosPlanos = mostrarDadosPlanos;
    }

    public void toggleDadosPlanos() {
        setMostrarDadosPlanos(!isMostrarDadosPlanos());
    }

    public List<PlanoVO> getPlanoVOs() {
        return planoVOs;
    }

    public void setPlanoVOs(List<PlanoVO> planoVOs) {
        this.planoVOs = planoVOs;
    }

    public List<ContratoDuracaoVO> getContratoDuracaoVOs() {
        return contratoDuracaoVOs;
    }

    public void setContratoDuracaoVOs(List<ContratoDuracaoVO> contratoDuracaoVOs) {
        this.contratoDuracaoVOs = contratoDuracaoVOs;
    }

    public List<AmostraClienteTO> getListaAmostra() {
        return listaAmostra;
    }

    public void setListaAmostra(List<AmostraClienteTO> listaAmostra) {
        this.listaAmostra = listaAmostra;
    }

    private String msgNrTotais(){
        if(getMalaDiretaVO().getSms()){
            int tels = 0;
            try {
                for(AmostraClienteTO amostraCliente : getListaAmostra()){
                    tels += UteisValidacao.emptyString(amostraCliente.getTelefones()) ? 0 :  (amostraCliente.getTelefones().split("br").length);
                }
                if(tels > 1){
                    return "(" + tels + " números de telefone receberão o contato)";
                } else if(tels == 1){
                    return " (Um número de telefone receberá o contato)";
                }
            }catch (Exception e){
                Uteis.logar(e, MalaDiretaControle.class);
            }
        } else if(getMalaDiretaVO().getEmail()){
            int emails = 0;
            try {
                for(AmostraClienteTO amostraCliente : getListaAmostra()){

                    emails += UteisValidacao.emptyString(amostraCliente.getEmails()) ? 0 :  (Uteis.contarSequenciaCaracterString(amostraCliente.getEmails(), "@"));
                }
                if(emails > 1){
                    return "(" + emails + " endereços de e-mail receberão o contato)";
                } else if(emails == 1){
                    return " (Um endereço de e-mail receberá o contato)";
                }
            }catch (Exception e){
                Uteis.logar(e, MalaDiretaControle.class);
            }
        }

        return "";
    }

    public String getNrTotalAmostra() {

        try {
            switch (modo) {
                case DESTINATARIOS:
                    String infoTelefones = msgNrTotais();
                    String nomeEntidade = getMalaDiretaVO().getCfgEvento().getIndicados() ? "indicado" : "aluno";
                    return (getListaAmostra().size() > 1 ? getListaAmostra().size()
                            + " " + nomeEntidade + "s filtrados " : getListaAmostra().size()+" " + nomeEntidade + " filtrado") + infoTelefones;
                case ENVIADOS:
                    return "Alunos que receberam o mailing";
                case NAO_ENVIADOS:
                    return "Alunos que não receberam o mailing";
                default:
                    return "";
            }

        } catch (Exception e) {
            return "";
        }

    }

    public boolean getEmail(){
        return getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.EMAIL);
    }

    public boolean getSMS(){
        return getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.SMS);
    }

    public String getFiltrosDescricao() {
        return filtrosDescricao;
    }

    public void setFiltrosDescricao(String filtrosDescricao) {
        this.filtrosDescricao = filtrosDescricao;
    }

    public boolean getApresentarFiltros(){
        return !filtrosDescricao.isEmpty();
    }

    public ConfiguracaoSistemaCRMVO getConfigCRM() {
        return configCRM;
    }

    public void setConfigCRM(ConfiguracaoSistemaCRMVO configCRM) {
        this.configCRM = configCRM;
    }

    public void irParaTelaCliente() throws Exception {
        setMsgAlert("");
        AmostraClienteTO amostra = (AmostraClienteTO) context().getExternalContext().getRequestMap().get("amostra");
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                JSFUtilities.storeOnSession(ClienteControle.class.getSimpleName(), clienteControle);
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorMatricula(amostra.getMatricula(), false, Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            if(UteisValidacao.emptyNumber(clienteControle.getClienteVO().getCodigo())){
                montarMsgAlert("Cliente não encontrado.");
            }else{
                setMsgAlert("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1000, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Boolean getSomenteRecorrencia() {
        return somenteRecorrencia;
    }

    public void setSomenteRecorrencia(Boolean somenteRecorrencia) {
        if (getMalaDiretaVO() != null && getMalaDiretaVO().getMailingFiltros() != null) {
            getMalaDiretaVO().getMailingFiltros().setSomenteRecorrencia(somenteRecorrencia);
        }
        this.somenteRecorrencia = somenteRecorrencia;
    }

    public boolean getEventoEscolhido(){
        return !UteisValidacao.emptyNumber(getMalaDiretaVO().getCfgEvento().getEventoCodigo());
    }
    public Integer getNrColunasEvento(){
        return getEventoEscolhido() ? 2 : 1;
    }

    public boolean getModoDestinatarios(){
        return modo.equals(MODE_MODAL.DESTINATARIOS);
    }
    public boolean getModoEnviados(){
        return modo.equals(MODE_MODAL.ENVIADOS);
    }
    public boolean getModoNaoEnviados(){
        return modo.equals(MODE_MODAL.NAO_ENVIADOS);
    }

    public void consultarPaginadoListener(ActionEvent evt) {
        //==================================================================================================================================


        //VERIFICACAO NECESSARIA POR CAUSA DOS FILTROS
        Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
        if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
            if (compPaginaInicial.toString().equals("paginaInicial")) {
                setConfPaginacao(new ConfPaginacao());
            }
        }

        //Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        //==================================================================================================================================
        consultarPaginado();
    }

    public Boolean getCategoria() {
        return categoria;
    }

    public void setCategoria(Boolean categoria) {
        this.categoria = categoria;
    }

    public Boolean getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(Boolean colaboradores) {
        this.colaboradores = colaboradores;
    }

    public Boolean getDadosCadastrais() {
        return dadosCadastrais;
    }

    public void setDadosCadastrais(Boolean dadosCadastrais) {
        this.dadosCadastrais = dadosCadastrais;
    }

    public Boolean getDadosPlanos() {
        return dadosPlanos;
    }

    public void setDadosPlanos(Boolean dadosPlanos) {
        this.dadosPlanos = dadosPlanos;
    }

    public Boolean getModalidades() {
        return modalidades;
    }

    public void setModalidades(Boolean modalidades) {
        this.modalidades = modalidades;
    }

    public MODE_MODAL getModo() {
        return modo;
    }

    public void setModo(MODE_MODAL modo) {
        this.modo = modo;
    }

    public Boolean getSituacao() {
        return situacao;
    }

    public void setSituacao(Boolean situacao) {
        this.situacao = situacao;
    }

    public Boolean getExtra() {
        return extra;
    }

    public void setExtra(Boolean extra) {
        this.extra = extra;
    }

    public void fecharTodos() {
        categoria = Boolean.FALSE;
        colaboradores = Boolean.FALSE;
        dadosCadastrais = Boolean.FALSE;
        dadosPlanos = Boolean.FALSE;
        modalidades = Boolean.FALSE;
        situacao = Boolean.FALSE;
        extra = Boolean.FALSE;
        pesquisa = Boolean.FALSE;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

        List listaParaImpressao = consultarTodos();
        exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
    }


    public void exportarAmostra(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

        List listaParaImpressao = getListaAmostra();
        for (Object obj : listaParaImpressao) {
            AmostraClienteTO amostraCliente = (AmostraClienteTO) obj;
            amostraCliente.setEmails(amostraCliente.getEmails().replaceAll("<br/>", ";"));
        }
        exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
    }

    public List<SelectItem> getTiposAgendamento() {
        if(tiposAgendamento == null || tiposAgendamento.isEmpty()){
            tiposAgendamento = new ArrayList<>();
            tiposAgendamento.add(new SelectItem(TipoAgendamentoEnum.TODOS.getCodigo(), TipoAgendamentoEnum.TODOS.getDescricao()));
            tiposAgendamento.add(new SelectItem(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo(), TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getDescricao()));
            tiposAgendamento.add(new SelectItem(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getCodigo(), TipoAgendamentoEnum.AGENDAMENTO_PREVISTO.getDescricao()));
        }
        return tiposAgendamento;
    }

    public void setTiposAgendamento(List<SelectItem> tiposAgendamento) {
        this.tiposAgendamento = tiposAgendamento;
    }

    public List<SelectItem> getTiposVigencia() {
        if(tiposVigencia == null || tiposVigencia.isEmpty()){
            tiposVigencia = JSFUtilities.getSelectItemListFromEnum(TipoVigenciaConsultaEnum.class, "descricao", false);
        }
        return tiposVigencia;
    }

    public void setTiposVigencia(List<SelectItem> tiposVigencia) {
        this.tiposVigencia = tiposVigencia;
    }

    public TipoVigenciaConsultaEnum getTipoVigencia() {
        return tipoVigencia;
    }

    public void setTipoVigencia(TipoVigenciaConsultaEnum tipoVigencia) {
        this.tipoVigencia = tipoVigencia;
    }

    public boolean getAgendamentoOuTodos(){
        return codigoTipoAgendamento == null
                || !codigoTipoAgendamento.equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo());
    }

    public void realizarConsultaLog() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = malaDiretaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),0, 0);
    }


    @Override
    public ControleConsulta getControleConsulta() {
        if (controleConsulta == null){
            controleConsulta = new ControleConsulta();
        }
        return controleConsulta;
    }

    @Override
    public void setControleConsulta(ControleConsulta controleConsulta) {
        this.controleConsulta = controleConsulta;
    }


    public void campoOrdenacao(AjaxEvent aE) {
        String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(getHtmlDataTable());
        if (!colunaOrdenacao.isEmpty()) {
            String[] params = colunaOrdenacao.split(":");
            if (params[0].equals("ml_titulo") || params[0].equals("u_nome") || params[0].equals("mm_titulo")){
                setCampoOrdenar("TRIM("+params[0].replace("_", ".")+")");
            }else {
                setCampoOrdenar(params[0].replace("_", "."));
            }
            setTipoOrdernacao(params[1]);
            ControleConsulta.inicializar(getControleConsulta());
            consultarMalaDireta();
        }
    }

    public org.richfaces.component.html.HtmlDataTable getHtmlDataTable() {
        return htmlDataTable;
    }

    public void setHtmlDataTable(org.richfaces.component.html.HtmlDataTable htmlDataTable) {
        this.htmlDataTable = htmlDataTable;
    }

    public String getTipoOrdernacao() {
        if (tipoOrdernacao == null){
            tipoOrdernacao = "DESC";
        }
        return tipoOrdernacao;
    }

    public void setTipoOrdernacao(String tipoOrdernacao) {
        this.tipoOrdernacao = tipoOrdernacao;
    }

    public String getCampoOrdenar() {
        if (campoOrdenar == null){
            campoOrdenar = "ml.codigo";
        }
        return campoOrdenar;
    }

    public void setCampoOrdenar(String campoOrdenar) {
        this.campoOrdenar = campoOrdenar;
    }


    public List<ProdutoVO> executarAutocompleteConsultaProduto(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ProdutoVO> result;
        try {
            Integer codigoEmpresa = getMalaDiretaVO().getEmpresa().getCodigo();
            result = (ArrayList<ProdutoVO>)  getFacade().getProduto().consultarPorProdutoComVigencia(pref, codigoEmpresa, false, Uteis.NIVELMONTARDADOS_ROBO);
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ProdutoVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarServicoSuggestionBox() throws Exception {
        ProdutoVO produto = (ProdutoVO) request().getAttribute("result");
        produto.setSelecionado(true);
        getMalaDiretaVO().getCfgEvento().adicionarProdutoVencidos(produto);
        getMalaDiretaVO().getCfgEvento().setProdutoVO(new ProdutoVO());
    }

    public void removerProdutoVencidos() throws Exception {
        ProdutoVO produto = (ProdutoVO) request().getAttribute("produto");
        getMalaDiretaVO().getCfgEvento().removerProdutoVencidos(produto);
    }

    public String getIrPara() {
        if (getMalaDiretaVO().getCfgEvento().getProdutosVencidos()){
            return "itemProdutos";
        }
        return null;
    }

    public List<SelectItem> getListaSelectTiposPerguntas() {
        if(listaSelectTiposPerguntas == null){
            listaSelectTiposPerguntas = new ArrayList<>();
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.SIMPLES.getCodigo(), TipoPerguntaEnum.SIMPLES.getDescricao()));
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.DISSERTATIVA.getCodigo(), TipoPerguntaEnum.DISSERTATIVA.getDescricao()));
            listaSelectTiposPerguntas.add(new SelectItem(TipoPerguntaEnum.OBJETIVA.getCodigo(), TipoPerguntaEnum.OBJETIVA.getDescricao()));
        }
        return listaSelectTiposPerguntas;
    }

    public void setListaSelectTiposPerguntas(List<SelectItem> listaSelectTiposPerguntas) {
        this.listaSelectTiposPerguntas = listaSelectTiposPerguntas;
    }

    public Integer getTipoMensagemApp() {
        return tipoMensagemApp;
    }

    public void setTipoMensagemApp(Integer tipoMensagemApp) {
        this.tipoMensagemApp = tipoMensagemApp;
    }

    public CamposGenericosTO getCampos() {
        return campos;
    }

    public void setCampos(CamposGenericosTO campos) {
        this.campos = campos;
    }

    public boolean getBotoesApp(){
        return tipoMensagemApp != null && tipoMensagemApp.equals(TipoPerguntaEnum.OBJETIVA.getCodigo())
                && getMalaDiretaVO().getMeioDeEnvioEnum().equals(MeioEnvio.APP);
    }

    public List<UsuarioVO> getListaUsuariosMetaCRMExtra() {
        return listaUsuariosMetaCRMExtra;
    }

    public void setListaUsuariosMetaCRMExtra(List<UsuarioVO> listaUsuariosMetaCRMExtra) {
        this.listaUsuariosMetaCRMExtra = listaUsuariosMetaCRMExtra;
    }

    public String gravarCRMExtra() throws Throwable {
        try {
            setModalMensagemGenerica("");
            setMsgAlert("");
            setarFiltrosSelecionados();
            getMalaDiretaVO().setCrmExtra(true);

            if (getMalaDiretaVO().getEmpresa() == null
                    || getMalaDiretaVO().getEmpresa().getCodigo() == 0) {
                throw new ConsistirException("Selecione uma empresa para a meta!");
            }
            if (!getMalaDiretaVO().isNovoObj() && getFacade().getFecharMeta().metaCRMExtraUtilizada(getMalaDiretaVO().getCodigo())) {
                throw new ConsistirException("Essa meta já foi utilizada por isso não pode ser alterada!");
            }

            if (UteisValidacao.emptyString(getMalaDiretaVO().getTitulo())) {
                throw new ConsistirException("Informe um nome para a meta!");
            }

            if (getMalaDiretaVO().getDataEnvio() == null || getMalaDiretaVO().getVigenteAte() == null) {
                throw new ConsistirException("Informe corretamente o período para realizar a meta!");
            }

            if (getMalaDiretaVO().isNovoObj() && Calendario.menor(Uteis.getDataComHoraZerada(getMalaDiretaVO().getDataEnvio()), Uteis.getDataComHoraZerada(Calendario.hoje()))) {
                throw new ConsistirException("A data inicial da meta não pode ser inferior a data atual!");
            }

            if (Calendario.menor(getMalaDiretaVO().getVigenteAte(), getMalaDiretaVO().getDataEnvio())) {
                throw new ConsistirException("A data final da meta não pode ser inferior a data inicial!");
            }

            //Verificar os Usuarios que foram selecionados para a meta
            List<UsuarioVO> usuarioSelecionados = new ArrayList<>();
            if(!getMalaDiretaVO().getMetaExtraIndividual()) {
                for (UsuarioVO usuarioVO : getListaUsuariosMetaCRMExtra()) {
                    if (usuarioVO.getUsuarioEscolhido()) {
                        usuarioSelecionados.add(usuarioVO);
                    }
                }
            }

            if (!getMalaDiretaVO().getMetaExtraIndividual() && UteisValidacao.emptyList(usuarioSelecionados)) {
                throw new ConsistirException("Selecione pelo menos um usuário para realizar a meta!");
            }

            if (getMalaDiretaVO().getCfgEvento().getApresentarDatas() &&
                    (getMalaDiretaVO().getCfgEvento().getInicio() == null
                            || getMalaDiretaVO().getCfgEvento().getFim() == null)) {
                throw new ConsistirException("Informe corretamente o período da consulta!");
            }

            campos = new CamposGenericosTO();
            getMalaDiretaVO().setOpcoes("");

            getFacade().getMailingAgendamento().montarSql(getMalaDiretaVO(), getMalaDiretaVO().getEmpresa().getCodigo(),
                    categoriaVOs, situacaoClienteTOs, modalidadeVOs,
                    consultoresVOs, professoresVOs, planoVOs, contratoDuracaoVOs,
                    convenioCobrancaVO, inicioVencimento, fimVencimento, inicioDiasSemAparecer, fimDiasSemAparecer, false);

            getMalaDiretaVO().setTitulo(Uteis.retirarPontosGraficos(getMalaDiretaVO().getTitulo().toUpperCase()));

            //REMETENTE NO CRM-EXTRA É QUEM CADASTROU A META --- USUÁRIO LOGADO
            getMalaDiretaVO().setRemetente(getUsuarioLogado());
            getMalaDiretaVO().setUsuarioVO(getUsuarioLogado());
            getMalaDiretaVO().setEmpresa(getMalaDiretaVO().getEmpresa());
            List<MalaDiretaCRMExtraClienteVO> malaDiretaCRMExtraClienteVO = new ArrayList<>();

            if (getMalaDiretaVO().getImportarLista()) {
                try{
                    malaDiretaCRMExtraClienteVO = getFacade().getMalaDiretaCRMExtraCliente().incluirListaImportados(getUsuarioLogado(), getEmpresaLogado() == null ? null : getEmpresaLogado().getCodigo(),
                            getMalaDiretaVO(),
                            listaItensMetaExtraImportada);
                }catch (Exception e){
                    throw e;
                }
            }

            MalaDiretaVO.validarDados(getMalaDiretaVO());

            //lista que será utilizada para povoar maladiretacrmextracliente
            setListaAmostra(getFacade().getCliente().consultarAmostra(getMalaDiretaVO().getSqlClientesAmostra() + " ORDER BY sw.nomecliente", null));

            getFacade().getMalaDireta().gravarCRMExtra(getMalaDiretaVO());

            if (getMalaDiretaVO().getImportarLista()) {
                getFacade().getMalaDiretaCRMExtraCliente().alterarMalaDireta(malaDiretaCRMExtraClienteVO, getMalaDiretaVO());

            } else {
                getFacade().getMalaDiretaCRMExtraCliente().incluirListaCliente(getMalaDiretaVO(), getListaAmostra());
            }
            getFacade().getMalaDiretaCRMExtraColaborador().incluirListaColaborador(getMalaDiretaVO(), usuarioSelecionados);

            gravarLogCRMExtra();
//            getMalaDiretaVO().setObjetoVOAntesAlteracao(new MalaDiretaVO());
//            getMalaDiretaVO().setNovoObj(true);
//            registrarLogObjetoVO(getMalaDiretaVO(), getMalaDiretaVO().getCodigo(), "MalaDireta", 0);

            getMalaDiretaVO().setNovoObj(false);
            setSucesso(true);
            setErro(false);
            setMensagem("");
            setMensagemDetalhada("Meta criada com sucesso!");
            montarMsgGenerica("Meta Extra", "Meta criada com sucesso!", "Fechar", "", "formF");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(e);
            montarMsgGenerica("Meta Extra", e.getMessage(), "Fechar", "", "formF");
            return "";
        }
        return "consultarCRMExtra";
    }

    public void realizarConsultaLogObjetoSelecionadoTurma() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = getMalaDiretaVO().getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                getMalaDiretaVO().getCodigo(), 0);
    }

    private void gravarLogExcluirMetaExtra() throws Exception {
        log = new LogVO();
        log.setNomeEntidade(MalaDiretaVO.class.getSimpleName());
        log.setNomeEntidadeDescricao(MalaDiretaVO.class.getSimpleName());
        log.setNomeCampo(" ");
        log.setChavePrimaria(getMalaDiretaVO().getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        log.setOperacao("ALTERAÇÃO");
        log.setValorCampoAnterior("SITUAÇÃO: ATIVA");
        log.setValorCampoAlterado("SITUAÇÃO: EXCLUIDA");
        getFacade().getLog().incluir(log);
    }

    private void gravarLogCRMExtra() throws Exception {
        log = new LogVO();
        log.setNomeEntidade(MalaDiretaVO.class.getSimpleName());
        log.setNomeEntidadeDescricao(MalaDiretaVO.class.getSimpleName());
        log.setNomeCampo(" ");
        log.setChavePrimaria(getMalaDiretaVO().getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getUsuarioLogado().getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());

        StringBuilder logAlteracao = new StringBuilder();
        logAlteracao.append("TÍTULO META: ").append(getMalaDiretaVO().getTitulo()).append(", ");
        logAlteracao.append("\nPERÍODO META: ").append(Uteis.getData(getMalaDiretaVO().getDataEnvio())).append(" até ").append(Uteis.getData(getMalaDiretaVO().getVigenteAte())).append(", ");
        logAlteracao.append("\n\rTOTAL CLIENTES: ").append(getListaAmostra().size()).append(", ");
        logAlteracao.append("\n\rSITUAÇÃO: ").append("ATIVA").append(", ");
        logAlteracao.append("\n\rUSUÁRIOS SELECIONADOS: ");
        for (UsuarioVO usuarioVO : getListaUsuariosMetaCRMExtra()) {
            if (usuarioVO.getUsuarioEscolhido()) {
                logAlteracao.append("\n").append(usuarioVO.getNomeAbreviado()).append(", ");
            }
        }

        if (getMalaDiretaVO().getCfgEvento().getEvento() != null) {
            logAlteracao.append("\nEVENTO PRÉ-DEFINIDO: ").append(getMalaDiretaVO().getCfgEvento().getEvento().getDescricao());
        }

        if (getMalaDiretaVO().getNovoObj()) {
            log.setOperacao("INCLUSÃO");
            log.setValorCampoAnterior("");
        } else {
            log.setOperacao("ALTERAÇÃO");
        }
        log.setValorCampoAlterado(logAlteracao.toString());
        if (!getMalaDiretaVO().getNovoObj()) {
            log.setValorCampoAlterado(logAlteracao.toString());
        }
        getFacade().getLog().incluir(log);
    }

    public String novoCRMExtra() {
        try {
            novo();
            getMalaDiretaVO().setCrmExtra(true);
            getMalaDiretaVO().setMeioDeEnvio(MeioEnvio.CRM_EXTRA.getCodigo());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.CRM_EXTRA);
            getMalaDiretaVO().setDataEnvio(Calendario.hoje());
            getMalaDiretaVO().setVigenteAte(Calendario.hoje());
            setCriarEditarMailing(true);
            listaSelectItemEventos = TipoEventoEnum.getListaComboCRMExtra(isUsarEstudio());
            limparMsg();
            listaItensMetaExtraImportada = new ArrayList<>();
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
            return "consultarCRMExtra";
        }
        return "editarCRMExtra";
    }

    public String editarCRMExtra() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            setModalMensagemGenerica("");
            listaItensMetaExtraImportada = new ArrayList<>();
            limparMsg();
            setMensagemID("msg_dados_editar");
            setMalaDiretaVO(getFacade().getMalaDireta().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS, false));
            inicializarAtributosRelacionados(getMalaDiretaVO());
            setConfPaginacaoHistorico(new ConfPaginacao());
            setMsgAlert("");
            setMalaDiretaVO(getFacade().getMalaDireta().consultarPorChavePrimaria(getMalaDiretaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, false));
            getMalaDiretaVO().setChaveAntiga(String.format("%s_%s_%s", getKey(),
                    getMalaDiretaVO().getMeioDeEnvioEnum().getDescricao(),
                    getMalaDiretaVO().getTipoAgendamento().name()));
            setMalaDiretaEnviadaVO(new MalaDiretaEnviadaVO());
            this.setClientes(null);
            consultarHistorico();
            setMensagemID("msg_dados_editar");
            ocorrenciaSelecionada = getMalaDiretaVO().getAgendamento().getOcorrencia().getCodigo();
            inicializarEventosPreDefinidos();
            alterarTipo();
            getMalaDiretaVO().setNovoObj(false);
            setCriarEditarMailing(true);
            montarFiltrosSelecionados();

            StringBuilder logAlteracao = new StringBuilder();
            logAlteracao.append("TÍTULO: ").append(getMalaDiretaVO().getTitulo());
            if (getMalaDiretaVO().getCfgEvento().getEvento() != null) {
                logAlteracao.append("\n\r EVENTO PRÉ-DEFINIDO: ").append(getMalaDiretaVO().getCfgEvento().getEvento().getDescricao());
            }
            logAlteracao.append("\n\r OCORRÊNCIA: ").append(getMalaDiretaVO().getAgendamento().getOcorrencia());
            logAlteracao.append("\n\r REMETENTE: ").append(getMalaDiretaVO().getRemetente().getNome());
            logAlteracao.append("\n\r MODELO MENSAGEM: ").append(getMalaDiretaVO().getModeloMensagemApresentar());
            log.setValorCampoAnterior(logAlteracao.toString());

            fecharTodos();
            getMalaDiretaVO().setCrmExtra(true);
            getMalaDiretaVO().setMeioDeEnvio(MeioEnvio.CRM_EXTRA.getCodigo());
            getMalaDiretaVO().setMeioDeEnvioEnum(MeioEnvio.CRM_EXTRA);
            listaSelectItemEventos = TipoEventoEnum.getListaComboCRMExtra(isUsarEstudio());
            montarFiltrosUsuariosSelecionados();
        } catch (Exception e) {
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
            setMensagemID("msg_dados_editar");
            return "consultarCRMExtra";
        }
        return "editarCRMExtra";
    }

    public String voltarCRMExtra() throws Exception {
        limparMsg();
        return "consultarCRMExtra";
    }

    private void montarFiltrosUsuariosSelecionados() throws Exception {
        List<MalaDiretaCRMExtraColaboradorVO> colaboradoresMeta = getFacade().getMalaDiretaCRMExtraColaborador().consultarPorMalaDireta(getMalaDiretaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (UsuarioVO usuarioVO : getListaUsuariosMetaCRMExtra()) {
            for (MalaDiretaCRMExtraColaboradorVO colaboradorVO : colaboradoresMeta) {
                if (usuarioVO.getCodigo().equals(colaboradorVO.getUsuarioVO().getCodigo())) {
                    usuarioVO.setUsuarioEscolhido(true);
                }
            }
        }
    }

    public boolean getPermiteExcluirCRMExtra() {
        boolean retorno;
        try {
            retorno = !getMalaDiretaVO().getExcluida() && !getMalaDiretaVO().isNovoObj();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
        return retorno;
    }

    public void confirmarExcluirMetaExtra() {
        try {
            setMsgAlert("");
            limparMsg();
            montarMsgGenericaPergunta("Excluir Meta Extra", "Ao fazer a exclusão dessa Meta Extra, a mesma não poderá ser reaberta. Deseja realmente excluir ?", "excluirCRMExtra", "Richfaces.showModalPanel('mdlMensagemGenerica');return false;", null, null, "panelGridLeft,  panelGridCenter, panelGridRight");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Meta Extra", e.getMessage(), "Fechar", "", "formF");
        }
    }

    public void montarMsgGenericaPergunta(String titulo, String msgInformacao, String metodoInvocarAoClicarBotaoSim, String onCompleteBotaoSim, String metodoInvocarAoClicarBotaoNao, String onCompleteBotaoNao, String reRender) {
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init(titulo, msgInformacao, this, metodoInvocarAoClicarBotaoSim, onCompleteBotaoSim, metodoInvocarAoClicarBotaoNao, onCompleteBotaoNao, reRender);
    }

    public String excluirCRMExtra() {
        try {
            setModalMensagemGenerica("");

//            List<FecharMetaVO> metaExtras = getFacade().getFecharMeta().consultarFecharMetaPorMalaDiretaMetaExtra(getMalaDiretaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
//
//            if (!UteisValidacao.emptyList(metaExtras)) {
//                for (FecharMetaVO metaVO: metaExtras) {
//                    getFacade().getFecharMeta().excluir(metaVO);
//                }
//            }
            getFacade().getMalaDireta().alterarSituacaoExclusao(getMalaDiretaVO(), true);

            gravarLogExcluirMetaExtra();

            novoCRMExtra();
            setMensagemID("msg_dados_excluidos");
            montarMsgGenerica("Meta Extra", "Meta Extra excluída com Sucesso.", "Fechar", "", "formF");
            return "consultarCRMExtra";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgGenerica("Meta Extra", e.getMessage(), "Fechar", "", "formF");
            return "";
        }
    }

    public void montarMsgGenerica(String titulo, String msg, String botaoFechar, String onCompleteFechar, String reRender) {
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setModalMensagemGenerica("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init( titulo, msg, this, botaoFechar, onCompleteFechar, reRender + ",mdlMensagemGenerica");
    }

    public void acaoSelecionarEnviarIndividualmente(){
        if(!getEnviarEmailIndividualmente()){
            limparTags();
        }
    }

    /**
     * Limpa as tags adicionadas no texto.
     */
    private void limparTags() {
        if(getMalaDiretaVO().getMensagem() != null){
            String msg = getMalaDiretaVO().getMensagem();
            for(String tag: MalaDiretaVO.TAGS){
                msg = msg.replaceAll(tag, "");
            }
            getMalaDiretaVO().setMensagem(msg);
        }
    }



    public boolean isApresentarTags() {
        return getMalaDiretaVO().isApresentarTags(configCRM);
    }

    public boolean isApresentarTagParcelaCobranca() {
        return getMalaDiretaVO().isApresentarTags(configCRM) && (getMalaDiretaVO().getCfgEvento() != null && getMalaDiretaVO().getCfgEvento().getEvento() != null && (getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS) || getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO)));
    }

    public boolean isApresentarTagBoleto() {
        return getMalaDiretaVO() != null
                && getMalaDiretaVO().isApresentarTags(configCRM)
                && (getMalaDiretaVO().getCfgEvento() != null
                && getMalaDiretaVO().getCfgEvento().getEvento() != null
                && ((getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS) || getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO))
                && getMalaDiretaVO().getCfgEvento().isBoletoParcelasVencendo()));
    }

    public boolean isApresentarTagPix() {
        return getMalaDiretaVO() != null
                && getMalaDiretaVO().isApresentarTags(configCRM)
                && (getMalaDiretaVO().getCfgEvento() != null
                && getMalaDiretaVO().getCfgEvento().getEvento() != null
                && (getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS)
                || (getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO)
                && this.isExisteConvenioCobrancaPix())));
    }

    public boolean isApresentarModeloEmailPadraoBoleto() {
        return getMalaDiretaVO() != null
                && getMalaDiretaVO().isApresentarTags(configCRM)
                && getMalaDiretaVO().getCfgEvento() != null
                && getMalaDiretaVO().getCfgEvento().getEvento() != null
                && (getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELA_VENCENDO) || getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.PARCELAS_VENCIDAS))
                && getMalaDiretaVO().getCfgEvento().isBoletoParcelasVencendo()
                && getMalaDiretaVO().getCfgEvento().isModeloPadraoBoleto();
    }

    public boolean getEnviarEmailIndividualmente() {
        return enviarEmailIndividualmente;
}

    public void setEnviarEmailIndividualmente(boolean enviarEmailIndividualmente) {
        this.enviarEmailIndividualmente = enviarEmailIndividualmente;
    }


    public Integer getInicioDiasSemAparecer() {
        return inicioDiasSemAparecer;
    }

    public void setInicioDiasSemAparecer(Integer inicioDiasSemAparecer) {
        this.inicioDiasSemAparecer = inicioDiasSemAparecer;
    }

    public Integer getFimDiasSemAparecer() {
        return fimDiasSemAparecer;
    }

    public void setFimDiasSemAparecer(Integer fimDiasSemAparecer) {
        this.fimDiasSemAparecer = fimDiasSemAparecer;
    }

    public Date getInicioVencimento() {
        return inicioVencimento;
    }

    public void setInicioVencimento(Date inicioVencimento) {
        this.inicioVencimento = inicioVencimento;
    }

    public Date getFimVencimento() {
        return fimVencimento;
    }

    public void setFimVencimento(Date fimVencimento) {
        this.fimVencimento = fimVencimento;
    }

    public String limparVencimento() {
        inicioVencimento = null;
        fimVencimento = null;
        return "";
    }

    public List<SelectItem> getListSelectItemTipoConsultor() {
        return listSelectItemTipoConsultor;
    }

    public void setListSelectItemTipoConsultor(List<SelectItem> listSelectItemTipoConsultor) {
        this.listSelectItemTipoConsultor = listSelectItemTipoConsultor;
    }

    public boolean isConfiguracaoFtpValida(){
        try{
            configCRM = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }catch (Exception e){
            Uteis.logar(e, MalaDireta.class);
        }
//        http://pactosolucoes.com.br/blog?utm_source=sistema-zw&utm_medium=banner-interno-zw&utm_campaign=banner-respiracao
        return configCRM.getMailingFtpServer() != null && configCRM.getMailingFtpServer().length() > 0 &&
                configCRM.getMailingFtpUser() != null && configCRM.getMailingFtpUser().length() > 0 &&
                configCRM.getMailingFtpPass() != null && configCRM.getMailingFtpPass().length() > 0 &&
                configCRM.getMailingFtpType() != null && configCRM.getMailingFtpType().length() > 0 &&
                configCRM.getMailingFtpPort() != null && configCRM.getMailingFtpPort() > 0;
    }

    public boolean isApresentarTagsRemessa() {
        return getMalaDiretaVO().getEmail() &&
                getMalaDiretaVO().isApresentarTags(configCRM) &&
                (getMalaDiretaVO().getCfgEvento() != null && getMalaDiretaVO().getCfgEvento().getEvento() != null && getMalaDiretaVO().getCfgEvento().getEvento().equals(TipoEventoEnum.ITENS_NAO_APROVADOS_REMESSA));
    }

    public List<MarcadorVO> getListaTagsEmailRemessa() {
        return TagsEmailRemessaEnum.getListaTagsEmailRemessa();
    }

    public void inserirTagRemessa() {
        try {
            MarcadorVO obj = (MarcadorVO) context().getExternalContext().getRequestMap().get("tagRemessa");
            getMalaDiretaVO().setMensagem(MalaDireta.executarInsercaoTag(getMalaDiretaVO().getMensagem(), obj.getTag(), true));
        } catch (Exception ignored) {
        }
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public void incluirTagPesquisa() {
        executarInsercaoTag("TAG_PESQUISA");
    }

    public void montarListaPesquisa() {
        try {
            setListaSelectItemPesquisa(new ArrayList<>());
            List<QuestionarioVO> lista = getFacade().getQuestionario().consultarQuestionariosPorTipo(TipoServicoEnum.PESQUISA, true, Uteis.NIVELMONTARDADOS_MINIMOS);
            getListaSelectItemPesquisa().add(new SelectItem(0, "Selecione"));
            for (QuestionarioVO questionarioVO : lista) {
             getListaSelectItemPesquisa().add(new SelectItem(questionarioVO.getCodigo(), questionarioVO.getNomeInterno()));
            }
        } catch (Exception ex) {
            setListaSelectItemPesquisa(new ArrayList<>());
        }
    }

    public List<SelectItem> getListaSelectItemPesquisa() {
        if (listaSelectItemPesquisa == null) {
            listaSelectItemPesquisa = new ArrayList<>();
        }
        return listaSelectItemPesquisa;
    }

    public void setListaSelectItemPesquisa(List<SelectItem> listaSelectItemPesquisa) {
        this.listaSelectItemPesquisa = listaSelectItemPesquisa;
    }


    public void uploadArquivoListener(final UploadEvent event) throws Exception {
        setMsgAlert("");
        setModalMensagemGenerica("");
        setMensagem("");
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        // cria um novo arquivo de imagem
        arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
        // caso exista o arquivo ele é deletado
        arquivo.delete();
        final FileOutputStream out = new FileOutputStream(arquivo);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        // Limpa a memória assim que o arquivo e carregado
        out.flush();
        out.close();
        processarArquivoExcel();
        setSucesso(true);
        montarSucessoGrowl("Arquivo importado com sucesso. Vá em 'Ver amostra de clientes' para ver o resultado.");
    }

    public void processarArquivoExcel() {
        try {
            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(arquivo.getPath());
            UteisImportacaoExcel uteisExcel = new UteisImportacaoExcel();
            listaItensMetaExtraImportada = uteisExcel.obterListaMetaExtraExcel(linhas);
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLS ou .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public Boolean getBotaoAmostraCliente() {
        return botaoAmostraCliente;
    }

    public void setBotaoAmostraCliente(Boolean botaoAmostraCliente) {
        this.botaoAmostraCliente = botaoAmostraCliente;
    }

    public void limparLista(){
        listaItensMetaExtraImportada = new ArrayList<>();
    }

    public String getModeloPadraoBoleto() {
        try {
            if (!getMalaDiretaVO().getCfgEvento().isModeloPadraoBoleto()) {
                return "";
            }

            BoletoEmailTO boletoEmailTO = new BoletoEmailTO();
            boletoEmailTO.setEmpresaVO(getEmpresaLogado());
            boletoEmailTO.setPessoaVO(getUsuarioLogado().getColaboradorVO().getPessoa());
            boletoEmailTO.setLinhaDigitavel("846800000016350002962024002150100002002569647429");
            boletoEmailTO.setValor(150.00);
            boletoEmailTO.setDataVencimento(Uteis.somarDias(Calendario.hoje(), 5));

            getFacade().getBoleto().gerarHTMLModeloPadraoBoleto(getKey(), boletoEmailTO);
            return boletoEmailTO.getHtmlEmail();
        } catch (Exception ex) {
            return "";
        }
    }

    private void obterColunaOrdenacao() throws Exception {
        String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(htmlDataTable);
        if(colunaOrdenacao.equals(""))
            return;
        else  if (!colunaOrdenacao.isEmpty()) {
            String[] params = colunaOrdenacao.split(":");
            getConfPaginacao().setOrdernar(true);
            getConfPaginacao().setColunaOrdenacao(params[0]);
            getConfPaginacao().setDirecaoOrdenacao(params[1]);
        }
    }

    public void consultaPaginadoOrdenacao(AjaxEvent ajaxEvent) {
        try {
            limparMsg();
            obterColunaOrdenacao();
            consultarPaginado();
        } catch (Exception err) {
            montarErro(err);
        }
    }

    public Balance getSaldoMarketing() {
        return saldoMarketing;
    }

    public void setSaldoMarketing(Balance saldoMarketing) {
        this.saldoMarketing = saldoMarketing;
    }

    public boolean isSimEnviarSomenteSaldoRestanteDia() {
        return simEnviarSomenteSaldoRestanteDia;
    }

    public void setSimEnviarSomenteSaldoRestanteDia(boolean simEnviarSomenteSaldoRestanteDia) {
        this.simEnviarSomenteSaldoRestanteDia = simEnviarSomenteSaldoRestanteDia;
    }

    public boolean isSimEnviarSomenteSaldoRestanteMes() {
        return simEnviarSomenteSaldoRestanteMes;
    }

    public void setSimEnviarSomenteSaldoRestanteMes(boolean simEnviarSomenteSaldoRestanteMes) {
        this.simEnviarSomenteSaldoRestanteMes = simEnviarSomenteSaldoRestanteMes;
    }

    public void montarConvenioPix() {
        try {
            this.setExisteConvenioCobrancaPix(false);
            this.setExisteConvenioCobrancaPix(getFacade().getConvenioCobranca().existeConvenioCobrancaPorEmpresaSituacaoTipoCobranca(getMalaDiretaVO().getEmpresa().getCodigo(),
                    TipoCobrancaEnum.PIX, SituacaoConvenioCobranca.ATIVO));
        } catch (Exception ex) {
            ex.printStackTrace();
            this.setExisteConvenioCobrancaPix(false);
        }
    }

    public boolean existeConfiguracaoGupshupWhatsApp() {
        try {
            String key = (String) JSFUtilities.getFromSession("key");

            RequestHttpService service = new RequestHttpService();
            Map<String, String> params = new HashMap<>();
            params.put("empresaChave", key);
            params.put("empresaId", getEmpresaLogado().getCodigo().toString());
            params.put("SISTEMA_ZW", "true");
            params.put("Accept-Language", "pt-BR");

            params.put(HttpHeaders.CONTENT_TYPE, "application/json");
            String urlWhatsApp = (getUrlMidiaSocial() + "/v1/empresa");
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlWhatsApp, params, null, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());

            if (json.has("result") && json.getJSONArray("result").length() > 0) {
                JSONObject config = json.getJSONArray("result").getJSONObject(0).getJSONObject("configuracaoWhatsApp");
                if (!config.optString("apiKey").isEmpty()
                        && !config.optString("sourceId").isEmpty()
                        && !config.optString("appId").isEmpty()
                        && !config.optString("usuarioParceiro").isEmpty()
                        && !config.optString("senhaParceiro").isEmpty()
                ) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            montarErro(e);
        }
        return false;
    }

    public List<MalaDiretaRedeEmpresaVO> getListaMalaDiretaRedeEmpresa() {
        if(listaMalaDiretaRedeEmpresa == null){
            listaMalaDiretaRedeEmpresa = new ArrayList<>();
        }
        return listaMalaDiretaRedeEmpresa;
    }

    public void setListaMalaDiretaRedeEmpresa(List<MalaDiretaRedeEmpresaVO> listaMalaDiretaRedeEmpresa) {
        this.listaMalaDiretaRedeEmpresa = listaMalaDiretaRedeEmpresa;
    }

    public void prepararListaReplicarEmpresa() {
        try {
            getListaMalaDiretaRedeEmpresa().clear();

            Map<String, MalaDiretaRedeEmpresaVO> mapaUnidadesPorChave = new HashMap<>();

            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            for (RedeDTO redeDTO : redeEmpresaDataDTO.getRedeEmpresas()) {
                if (redeDTO.getChave().toLowerCase().trim().equals(getKey().toLowerCase().trim())) {
                    continue;
                }
                MalaDiretaRedeEmpresaVO malaDiretaRedeEmpresaVO = getFacade().getMalaDiretaRedeEmpresa().consultarPorChaveEmpresaMalaDireta(redeDTO.getChave(), redeDTO.getEmpresaZw(), malaDiretaVO.getCodigo());
                if (malaDiretaRedeEmpresaVO == null) {
                    malaDiretaRedeEmpresaVO = new MalaDiretaRedeEmpresaVO();
                    malaDiretaRedeEmpresaVO.setMensagemSituacao("AGUARDANDO REPLICAR MAILING");
                }
                malaDiretaRedeEmpresaVO.setChaveDestino(redeDTO.getChave().toLowerCase().trim());
                malaDiretaRedeEmpresaVO.setNomeUnidade(redeDTO.getNomeFantasia());
                malaDiretaRedeEmpresaVO.setRedeDTO(redeDTO);
                mapaUnidadesPorChave.put(malaDiretaRedeEmpresaVO.getChaveDestino(), malaDiretaRedeEmpresaVO);
            }
            getListaMalaDiretaRedeEmpresa().addAll(mapaUnidadesPorChave.values());

            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            Logger.getLogger(PlanoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public Integer getListaMalaDiretaRedeEmpresaSincronizado() {
        Integer cont = 0;
        for (MalaDiretaRedeEmpresaVO unid : getListaMalaDiretaRedeEmpresa()) {
            if (unid.getDataAtualizacaoInformada()) {
                cont++;
            }
        }
        return cont;
    }

    public Integer getListaMalaDiretaRedeEmpresaSize() {
        return getListaMalaDiretaRedeEmpresa().size();
    }

    public void validarExibirReplicarRedeEmpresa() {
        boolean integranteFranqueadoraRedeEmpresa = false;
        boolean usuarioAdministrador = false;

        try {
            for (UsuarioPerfilAcessoVO userPerfAcess : getControladorTipado(LoginControle.class).getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
                if (userPerfAcess.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR")) {
                    usuarioAdministrador = true;
                }
            }
        } catch (Exception e) {
            usuarioAdministrador = false;
        }

        try {
            RedeEmpresaVO rede = ((LoginControle) getControlador("LoginControle")).getRedeEmpresaVO();
            integranteFranqueadoraRedeEmpresa = rede != null && !UteisValidacao.emptyNumber(rede.getId()) && rede.getChaveFranqueadora().equals(getKey());
        } catch (Exception e) {
            e.printStackTrace();
        }

        boolean usuarioAdminPacto = false;
        try {
            usuarioAdminPacto = getUsuarioLogado().getUsuarioAdminPACTO();
            if (!usuarioAdminPacto) {
                usuarioAdminPacto = getUsuarioLogado().getUsuarioPACTOBR();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        exibirReplicarRedeEmpresa = integranteFranqueadoraRedeEmpresa && !malaDiretaVO.isNovoObj() && (usuarioAdminPacto || usuarioAdministrador);
    }

    public boolean isExibirReplicarRedeEmpresa() {
        return exibirReplicarRedeEmpresa;
    }

    public void setExibirReplicarRedeEmpresa(boolean exibirReplicarRedeEmpresa) {
        this.exibirReplicarRedeEmpresa = exibirReplicarRedeEmpresa;
    }

    public void replicarAutomaticoTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (MalaDiretaRedeEmpresaVO obj : getListaMalaDiretaRedeEmpresa()) {
                if (obj.getDataAtualizacaoInformada()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.MALA_DIRETA, null, null, null, obj, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarTodas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (MalaDiretaRedeEmpresaVO obj : getListaMalaDiretaRedeEmpresa()) {
                if (!obj.getDataAtualizacaoInformada()) {
                    obj.setSelecionado(true);
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.MALA_DIRETA, null, null, null, obj, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void replicarSelecionadas() {
        try {
            int qtdThreads = 0;
            List<ReplicarRedeEmpresaCallable> callableTasks = new ArrayList<>();
            for (MalaDiretaRedeEmpresaVO obj : getListaMalaDiretaRedeEmpresa()) {
                if (obj.isSelecionado()) {
                    callableTasks.add(new ReplicarRedeEmpresaCallable(JSFUtilities.getRequest(),
                            JSFUtilities.getResponse(), ReplicarRedeEmpresaEnum.MALA_DIRETA, null, null, null, obj, null, null, null, null));
                    qtdThreads++;
                }
            }
            final ExecutorService executorService = Executors.newFixedThreadPool(qtdThreads);
            executorService.invokeAll(callableTasks);
            executorService.shutdown();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void limparReplicar() {
        for(MalaDiretaRedeEmpresaVO obj : getListaMalaDiretaRedeEmpresa()){
            obj.setSelecionado(false);
        }
    }

    public void replicarMalaDiretaRedeEmpresaGeral() {
        MalaDiretaRedeEmpresaVO obj = (MalaDiretaRedeEmpresaVO) context().getExternalContext().getRequestMap().get("malaDiretaRedeEmpresaReplicacao");
        replicarMalaDiretaRedeEmpresaUnica(obj);
    }

    public void retirarVinculoReplicacao() {
        limparMsg();
        MalaDiretaRedeEmpresaVO obj = (MalaDiretaRedeEmpresaVO) context().getExternalContext().getRequestMap().get("malaDiretaRedeEmpresaReplicacao");
        try {
            obj.setDataatualizacao(null);
            obj.setMensagemSituacao("AGUARDANDO REPLICAR MAILING");
            getFacade().getMalaDiretaRedeEmpresa().limparDataAtualizacao(malaDiretaVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getMalaDiretaRedeEmpresa().alterarMensagemSituacao(malaDiretaVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());

        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
        }
    }

    public void replicarMalaDiretaRedeEmpresaUnica(MalaDiretaRedeEmpresaVO obj) {
        try {
            replicarMalaDiretaRedeEmpresa(obj);
            limparMsg();
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(ex);
            obj.setMensagemSituacao("ERRO: " + ex.getMessage());
            try {
                getFacade().getUsuarioRedeEmpresa().alterarMensagemSituacao(malaDiretaVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), obj.getRedeDTO().getEmpresaZw());
            } catch (Exception e) {
            }
            Logger.getLogger(UsuarioControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void replicarMalaDiretaRedeEmpresa(MalaDiretaRedeEmpresaVO obj) throws Exception {
        MalaDiretaRedeEmpresaVO malaDiretaRedeEmpresaVO = getFacade().getMalaDiretaRedeEmpresa().consultarPorChaveEmpresaMalaDireta(obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw(), malaDiretaVO.getCodigo());
        if (malaDiretaRedeEmpresaVO == null) {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String cadastroAuxiliarUrl = redeEmpresaDataDTO.getServiceUrls().getCadastroAuxiliarUrl();

            JSONObject cloneMalaDiretaOrigem = CadAuxMsService.clonar(malaDiretaVO.getCodigo(), cadastroAuxiliarUrl, "malaDireta", getKey());
            malaDiretaRedeEmpresaVO = new MalaDiretaRedeEmpresaVO(malaDiretaVO.getCodigo(), getKey(), obj.getChaveDestino(), obj.getRedeDTO().getEmpresaZw());
            getFacade().getMalaDiretaRedeEmpresa().inserir(malaDiretaRedeEmpresaVO);
            // Perfil não tem na outra academia da rede, então inclui
            JSONObject malaDiretaReplicado = CadAuxMsService.replicar(cloneMalaDiretaOrigem, obj.getRedeDTO().getUrlCadAuxMs(), "malaDireta", obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString());
            malaDiretaRedeEmpresaVO.setChaveDestino(obj.getRedeDTO().getChave());
            malaDiretaRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
            malaDiretaRedeEmpresaVO.setDataatualizacao(new Date());
            malaDiretaRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(malaDiretaRedeEmpresaVO.getDataatualizacao()));
            malaDiretaRedeEmpresaVO.setMalaDiretaReplicado(malaDiretaReplicado.getInt("codigo"));
            obj.setChaveDestino(obj.getRedeDTO().getChave());
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getMalaDiretaRedeEmpresa().alterarDataAtualizacao(malaDiretaVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), malaDiretaReplicado.getInt("codigo"), obj.getRedeDTO().getEmpresaZw());
        } else {
            RedeEmpresaDataDTO redeEmpresaDataDTO = OamdMsService.urlsChaveRedeEmpresa(getKey());
            String cadastroAuxiliarUrl = redeEmpresaDataDTO.getServiceUrls().getCadastroAuxiliarUrl();

            JSONObject cloneMalaDiretaOrigem = CadAuxMsService.clonar(malaDiretaVO.getCodigo(), cadastroAuxiliarUrl, "malaDireta", getKey());
            JSONObject malaDiretaReplicado = CadAuxMsService.replicar(cloneMalaDiretaOrigem, obj.getRedeDTO().getUrlCadAuxMs(), "malaDireta", obj.getRedeDTO().getChave(), obj.getRedeDTO().getEmpresaZw().toString());
            malaDiretaRedeEmpresaVO.setChaveDestino(obj.getRedeDTO().getChave());
            malaDiretaRedeEmpresaVO.setEmpresaDestino(obj.getRedeDTO().getEmpresaZw());
            malaDiretaRedeEmpresaVO.setDataatualizacao(new Date());
            malaDiretaRedeEmpresaVO.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(malaDiretaRedeEmpresaVO.getDataatualizacao()));
            malaDiretaRedeEmpresaVO.setMalaDiretaReplicado(malaDiretaReplicado.getInt("codigo"));
            obj.setChaveDestino(obj.getRedeDTO().getChave());
            obj.setDataatualizacao(new Date());
            obj.setMensagemSituacao("REPLICADO EM " + Uteis.getDataComHora(obj.getDataatualizacao()));
            getFacade().getMalaDiretaRedeEmpresa().alterarDataAtualizacao(malaDiretaVO.getCodigo(), getKey(), obj.getRedeDTO().getChave(), obj.getMensagemSituacao(), malaDiretaReplicado.getInt("codigo"), obj.getRedeDTO().getEmpresaZw());
        }
    }
}


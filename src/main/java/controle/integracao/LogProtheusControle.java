package controle.integracao;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LogProtheusControle extends SuperControle {

    private List<LogEnvio> logsErros;
    private List<LogEnvio> logsSucesso;
    private List<LogEnvio> logsAguardando;
    private Boolean ativo = true;
    private Integer erros = 0;
    private Integer aguardando = 0;
    private Integer bordero = 0;
    private Integer pendentesImportados = 0;
    private Integer pendentesPagos = 0;
    private Integer pendentesAguardando = 0;
    private Integer borderoBaixados = 0;
    private Integer borderoAbertos = 0;
    private Integer borderoProcessando = 0;
    private Integer enviosHoje = 0;
    private String chave = "";

    public LogProtheusControle(){
        inicializar();
    }

    public void inicializar(){
        try {
            if (request() != null) {
                if (request().getParameter("chave") != null) {
                    chave = request().getParameter("chave");
                    Conexao.initSession(chave);
                    JSFUtilities.storeOnSession("key", chave);
                }
            }
            bordero = getFacade().getReciboPagamento().totalEmBordero();
            aguardando = getFacade().getReciboPagamento().totalAguardando();

            Map<String, Object> protheus = getFacade().getReciboPagamento().errosProtheus();
            enviosHoje = (Integer) protheus.get("hoje");
            logsErros = (List<LogEnvio>) protheus.get("erros");
            pendentesImportados = getFacade().getMovParcela().numeroPendenciasImportadas(false, false);
            pendentesPagos = getFacade().getMovParcela().numeroPendenciasImportadas(true, false);
            pendentesAguardando = getFacade().getMovParcela().numeroPendenciasImportadas(false, true);
            logsSucesso = (List<LogEnvio>) protheus.get("sucessos");
            logsAguardando = getFacade().getReciboPagamento().montarLogsAguardando();
            erros = logsErros.size();
        }catch (Exception e){
            ativo = false;
            montarErro(e);
        }
    }


    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Integer getErros() {
        return erros;
    }

    public void setErros(Integer erros) {
        this.erros = erros;
    }

    public Integer getAguardando() {
        return aguardando;
    }

    public void setAguardando(Integer aguardando) {
        this.aguardando = aguardando;
    }

    public Integer getBordero() {
        return bordero;
    }

    public void setBordero(Integer bordero) {
        this.bordero = bordero;
    }

    public Integer getBorderoBaixados() {
        return borderoBaixados;
    }

    public void setBorderoBaixados(Integer borderoBaixados) {
        this.borderoBaixados = borderoBaixados;
    }

    public Integer getBorderoAbertos() {
        return borderoAbertos;
    }

    public void setBorderoAbertos(Integer borderoAbertos) {
        this.borderoAbertos = borderoAbertos;
    }

    public Integer getBorderoProcessando() {
        return borderoProcessando;
    }

    public void setBorderoProcessando(Integer borderoProcessando) {
        this.borderoProcessando = borderoProcessando;
    }

    public List<LogEnvio> getLogsErros() {
        return logsErros;
    }

    public void setLogsErros(List<LogEnvio> logsErros) {
        this.logsErros = logsErros;
    }

    public List<LogEnvio> getLogsSucesso() {
        return logsSucesso;
    }

    public void setLogsSucesso(List<LogEnvio> logsSucesso) {
        this.logsSucesso = logsSucesso;
    }

    public void downloadArquivoBordero() {
        try {
            String textoSalvar = getFacade().getReciboPagamento().emBordero();
            Uteis.salvarArquivo("embordero.txt", textoSalvar, request().getRealPath("relatorio")+ File.separator);
        }catch (Exception ignorado){}
    }

    public void downloadArquivoPendencias() {
        try {
            String textoSalvar = getFacade().getMovParcela().pendenciasImportadas(false, false);
            Uteis.salvarArquivo("pendentesImportados.txt", textoSalvar, request().getRealPath("relatorio")+ File.separator);
        }catch (Exception ignorado){}
    }

    public void downloadArquivoPendenciasPagas() {
        try {
            String textoSalvar = getFacade().getMovParcela().pendenciasImportadas(true, false);
            Uteis.salvarArquivo("pendentesImportadosPagos.txt", textoSalvar, request().getRealPath("relatorio")+ File.separator);
        }catch (Exception ignorado){}
    }

    public void downloadArquivoPendenciasAguardando() {
        try {
            String textoSalvar = getFacade().getMovParcela().pendenciasImportadas(false, true);
            Uteis.salvarArquivo("pendentesImportadosAguardando.txt", textoSalvar, request().getRealPath("relatorio")+ File.separator);
        }catch (Exception ignorado){}
    }

    public List<LogEnvio> getLogsAguardando() {
        return logsAguardando;
    }

    public void setLogsAguardando(List<LogEnvio> logsAguardando) {
        this.logsAguardando = logsAguardando;
    }

    public Integer getPendentesImportados() {
        return pendentesImportados;
    }

    public void setPendentesImportados(Integer pendentesImportados) {
        this.pendentesImportados = pendentesImportados;
    }

    public Integer getPendentesPagos() {
        return pendentesPagos;
    }

    public void setPendentesPagos(Integer pendentesPagos) {
        this.pendentesPagos = pendentesPagos;
    }

    public Integer getPendentesAguardando() {
        return pendentesAguardando;
    }

    public void setPendentesAguardando(Integer pendentesAguardando) {
        this.pendentesAguardando = pendentesAguardando;
    }

    public Integer getEnviosHoje() {
        return enviosHoje;
    }

    public void setEnviosHoje(Integer enviosHoje) {
        this.enviosHoje = enviosHoje;
    }
}

package controle.integracao;

import br.com.pactosolucoes.comuns.util.Formatador;
import org.json.JSONObject;
import negocio.comuns.utilitarias.FormatadorNumerico;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class LogEnvio {

    private Long ultimaTentativa;
    private Integer recibo;
    private String matricula;
    private String nome;
    private String status;
    private String logerro;
    private String envio;

    public LogEnvio(){

    }
    public LogEnvio(JSONObject json) throws Exception{
        ultimaTentativa = json.getLong("ultimaTentativa");
        recibo = json.getInt("recibo");
        status = json.getString("status");
        logerro = json.getString("logerro");
        envio = json.getString("envio");
    }

    public String getHora(){
        try {
            return Uteis.getDataAplicandoFormatacao(new Date(ultimaTentativa), "dd/MM HH:mm");
        }catch (Exception e){
            return "";
        }

    }


    public String getValor(){
        try {
            double valor = new JSONObject(envio).getJSONObject("cobranca").getDouble("valor");
            return Formatador.formatarValorMonetarioSemMoeda(valor);
        }catch (Exception e){
            return "";
        }
    }

    public String getDescricao(){
        try {
            return new JSONObject(envio).getJSONObject("cobranca").getString("descricaoOperadora");
        }catch (Exception e){
            return "";
        }
    }

    public String getTaxaBase(){
        try {
            double valor = new JSONObject(envio).getJSONObject("cobranca").getDouble("taxaBase");
            return Formatador.formatarValorMonetarioSemMoeda(valor) + "%";
        }catch (Exception e){
            return "";
        }
    }

    public String getTaxaValor(){
        try {
            double valor = new JSONObject(envio).getJSONObject("cobranca").getDouble("taxa");
            return Formatador.formatarValorMonetarioSemMoeda(valor);
        }catch (Exception e){
            return "";
        }
    }

    public Long getUltimaTentativa() {
        return ultimaTentativa;
    }

    public void setUltimaTentativa(Long ultimaTentativa) {
        this.ultimaTentativa = ultimaTentativa;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLogerro() {
        return logerro;
    }

    public void setLogerro(String logerro) {
        this.logerro = logerro;
    }

    public String getEnvio() {
        return envio;
    }

    public void setEnvio(String envio) {
        this.envio = envio;
    }

    public Integer getRecibo() {
        return recibo;
    }

    public void setRecibo(Integer recibo) {
        this.recibo = recibo;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
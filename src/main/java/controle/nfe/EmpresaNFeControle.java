package controle.nfe;

import controle.arquitetura.SuperControle;
import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.nfe.enumerador.CRTEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.model.NFeEmpresaDiretorioExecucaoPOJO;
import org.apache.commons.lang.StringUtils;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;

import javax.faces.model.SelectItem;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class EmpresaNFeControle extends SuperControle {

    private EmpresaNFeVO empresaNFeVO;
    private List<SelectItem> listaCRT = new ArrayList<SelectItem>();
    private List<SelectItem> listaExecutaveisDelphi = new ArrayList<SelectItem>();

    public EmpresaNFeControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        montarListaCRT();
        setMensagemID("msg_entre_prmconsulta");
    }

    private void montarListaCRT() {
        setListaCRT(new ArrayList<SelectItem>());
        for (CRTEnum crt : CRTEnum.values()) {
            getListaCRT().add(new SelectItem(crt.getCodigo(), crt.getDescricao()));
        }
    }

    private void montarListaExecutaveisDelphi() {
        setListaExecutaveisDelphi(new ArrayList<SelectItem>());
        try {
            final List<NFeEmpresaDiretorioExecucaoPOJO> empresas = getFacade().getNotaFiscalConsumidorEletronica().getExecutaveisDelphi();
            Collections.sort(empresas, new Comparator<NFeEmpresaDiretorioExecucaoPOJO>() {
                @Override
                public int compare(NFeEmpresaDiretorioExecucaoPOJO e1, NFeEmpresaDiretorioExecucaoPOJO e2) {
                    return e1.getExecutavel().compareTo(e2.getExecutavel());
                }
            });

            for (final NFeEmpresaDiretorioExecucaoPOJO nfeEmpresaDiretorioExecucaoPOJO : empresas) {
                getListaExecutaveisDelphi().add(new SelectItem(
                        nfeEmpresaDiretorioExecucaoPOJO.getIdentificador(),
                        "[" + nfeEmpresaDiretorioExecucaoPOJO.getQtdEmpresas() + "] - " + nfeEmpresaDiretorioExecucaoPOJO.getExecutavel()));
            }
        } catch (Exception e) {
            Uteis.logar(e, EmpresaNFeControle.class);
        }
    }

    public String novo() {
        montarListaExecutaveisDelphi();
        setEmpresaNFeVO(new EmpresaNFeVO());
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public String editar() {
        EmpresaNFeVO obj = (EmpresaNFeVO) context().getExternalContext().getRequestMap().get("empresa");
        obj.setNovoObj(false);
        obj.registrarObjetoVOAntesDaAlteracao();
        setEmpresaNFeVO(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public String gravar() {
        try {
            if (empresaNFeVO.isNovoObj()) {
                getFacade().getEmpresaNFe().incluir(empresaNFeVO);
                if (StringUtils.isNotBlank(empresaNFeVO.getIdentificadorExecutavel())) {
                    adicionarEmpresaReiniciarExecutavelDelphi(empresaNFeVO.getIdentificadorExecutavel());
                }
            } else {
                if (empresaNFeVO.manteveSenha()) {
                    empresaNFeVO.setSenhaCertificado(empresaNFeVO.getSenhaCertificadoDescripto());
                }
                if (empresaNFeVO.manteveSenhaInscricaoMunicipal()) {
                    empresaNFeVO.setSenhaInscricaoMunicipal(empresaNFeVO.getSenhaInscricaoMunicipalDescripto());
                }

                EmpresaNFeVO objAntesDaAlteracao = (EmpresaNFeVO) empresaNFeVO.getObjetoVOAntesAlteracao();
                if(objAntesDaAlteracao.isAtiva() && !empresaNFeVO.isAtiva()){
                    removerEmpresaReiniciarExecutavelDelphi(empresaNFeVO.getIdentificadorExecutavel());
                }

                getFacade().getEmpresaNFe().alterar(empresaNFeVO);
            }

            if (empresaNFeVO.getCertificado() != null && empresaNFeVO.getCertificado().length() > 0) {
                boolean empresaTemCertificado = getFacade().getEmpresaNFe().temCerficadoCadastrado(empresaNFeVO);
                if (!empresaTemCertificado) {
                    getFacade().getEmpresaNFe().incluirCertificado(empresaNFeVO);
                } else {
                    getFacade().getEmpresaNFe().atualizarCertificado(empresaNFeVO);
                }

                if (StringUtils.isBlank(empresaNFeVO.getIdentificadorExecutavel())) {
                    getFacade().getNotaFiscalConsumidorEletronica().reiniciarTodosExecutaveisDelphi();
                } else {
                    getFacade().getNotaFiscalConsumidorEletronica().reiniciarExecutavelDelphi(empresaNFeVO.getIdentificadorExecutavel());
                }
            }

            empresaNFeVO.setSenhaCertificado(Uteis.encriptarNFe(empresaNFeVO.getSenhaCertificado()));
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");

            getControleConsulta().setValorConsulta("");
            empresaNFeVO.setNovoObj(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    private void adicionarEmpresaReiniciarExecutavelDelphi(String identificadorExecutavel) throws Exception {
        getFacade().getNotaFiscalConsumidorEletronica().adicionarEmpresa(identificadorExecutavel, empresaNFeVO.getId_Empresa());
        getFacade().getNotaFiscalConsumidorEletronica().reiniciarExecutavelDelphi(identificadorExecutavel);
    }

    private void removerEmpresaReiniciarExecutavelDelphi(String identificadorExecutavel) throws Exception {
        getFacade().getNotaFiscalConsumidorEletronica().removerEmpresa(identificadorExecutavel, empresaNFeVO.getId_Empresa());
        getFacade().getNotaFiscalConsumidorEletronica().reiniciarExecutavelDelphi(identificadorExecutavel);
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }

                try {
                    Integer.parseInt(getControleConsulta().getValorConsulta());
                } catch (Exception e) {
                    throw new Exception("Informe somente números");
                }

                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getEmpresaNFe().consultarPorCodigo(valorInt,getControleConsulta().getCampoConsulta2());
            }

            if (UteisValidacao.emptyString(getControleConsulta().getValorConsulta())) {
                throw new Exception("Informe a descrição para consultar");
            }

            if (getControleConsulta().getCampoConsulta().equals("razaoSocial")) {
                objs = getFacade().getEmpresaNFe().consultarPorRazaoSocial(getControleConsulta().getValorConsulta(),getControleConsulta().getCampoConsulta2());
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeFantasia")) {
                objs = getFacade().getEmpresaNFe().consultarPorNomeFantasia(getControleConsulta().getValorConsulta(),getControleConsulta().getCampoConsulta2());
            }

            if (getControleConsulta().getCampoConsulta().equals("cidade")) {
                objs = getFacade().getEmpresaNFe().consultarPorCidade(getControleConsulta().getValorConsulta(),getControleConsulta().getCampoConsulta2());
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";

        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        try {
            getFacade().getEmpresaNFe().excluir(empresaNFeVO);
            setEmpresaNFeVO(new EmpresaNFeVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getTipoConsultaCombo() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("nomeFantasia", "Nome Fantasia"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("razaoSocial", "Razão Social"));
        itens.add(new SelectItem("cidade", "Cidade"));
        return itens;
    }

    public List getListaSelectMunicipiosHomologados() throws Exception {
        return getFacade().getMunicipioNFe().obtenhaMunicipiosHomologados();
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void uploadLayoutListener(final UploadEvent event) throws IOException {
        UploadItem item = event.getUploadItem();
        File modelUploaded = item.getFile();

        empresaNFeVO.setCertificado(modelUploaded);
    }

    public EmpresaNFeVO getEmpresaNFeVO() {
        return empresaNFeVO;
    }

    public void setEmpresaNFeVO(EmpresaNFeVO empresaNFeVO) {
        this.empresaNFeVO = empresaNFeVO;
    }

    public List getListaSelectItemStatus() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        //itens.add(new SelectItem("", ""));
        itens.add(new SelectItem(true, "Ativo"));
        itens.add(new SelectItem(false, "Inativo"));
        return itens;
    }

    public List getSituacaoEmpresa() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("todos", "Todos"));
        itens.add(new SelectItem(true, "Ativo"));
        itens.add(new SelectItem(false, "Inativo"));
        return itens;
    }

    public void upload(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        File item1 = item.getFile();
        try {
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead;
            FileInputStream fi = new FileInputStream(item1.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            getEmpresaNFeVO().setFoto(arrayOutputStream.toByteArray());
            arrayOutputStream.close();
            fi.close();

            setMensagemID("msg_upload_arquivo");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            if (isCMYK(item1)) {
                setMensagemDetalhada("msg_erro", "Essa imagem está no formato inadequado. Por favor converta para RGB.");
                setSucesso(false);
                setErro(true);
                throw new ConsistirException("A Imagem está no formato inadequado. Converter para RGB");
            }
        }
    }



    public void paintFotoSemPesquisarNoBanco(OutputStream out, Object data) throws Exception {
        paintFotoEmpresa(out, getEmpresaNFeVO().getFoto());
    }

    public List<SelectItem> getListaCRT() {
        return listaCRT;
    }

    public void setListaCRT(List<SelectItem> listaCRT) {
        this.listaCRT = listaCRT;
    }

    public List<SelectItem> getListaExecutaveisDelphi() {
        return listaExecutaveisDelphi;
    }

    public void setListaExecutaveisDelphi(List<SelectItem> listaExecutaveisDelphi) {
        this.listaExecutaveisDelphi = listaExecutaveisDelphi;
    }
}

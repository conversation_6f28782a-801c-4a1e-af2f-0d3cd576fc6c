package controle.nfe;

import controle.arquitetura.SuperControle;
import negocio.comuns.nfe.ItemRPSNFeVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;

import java.util.ArrayList;
import java.util.List;

public class ItemNFSeControle extends SuperControle {

    private ItemRPSNFeVO itemVO;
    private List<ItemRPSNFeVO> listaDeItens;

    public void getItensDaNota() {
        try {
            NotaFiscalDeServicoVO nota = (NotaFiscalDeServicoVO) context().getExternalContext().getRequestMap().get("nota");
            List<ItemRPSNFeVO> objs = getFacade().getItemNFSe().listarItensDaNota(nota);
            setListaDeItens(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public List<ItemRPSNFeVO> getListaDeItens() {
        return listaDeItens;
    }

    public void setListaDeItens(List<ItemRPSNFeVO> listaDeItens) {
        this.listaDeItens = listaDeItens;
    }
}

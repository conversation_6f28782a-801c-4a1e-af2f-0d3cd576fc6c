package controle.nfe;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class UsuarioNFeControle extends SuperControle {

    private UsuarioNFeVO usuarioNFeVO;
    private UsuarioNFeVO usuarioLogado;
    private String senhaAtual = "";
    private String senha1 = "";
    private String senha2 = "";
    private String javaScriptFecharPopUpAlteracaoSenha = "";
    private EmpresaNFeVO empresaVO = new EmpresaNFeVO();
    private List<SelectItem> listaDePerfis = new ArrayList<SelectItem>();
    private boolean editar = false;

    public UsuarioNFeControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public void alterarSenhaUsuarioNFe() {
        try {
            validarDados();
            String senhaVelha = Uteis.desencriptarNFe(getUsuarioLogado().getSenha());

            if (!getSenhaAtual().equalsIgnoreCase(senhaVelha)) {
                throw new Exception(""); // a senha atual, é diferente da senha digitada
            }
            if (!getSenha1().equalsIgnoreCase(getSenha2())) {
                throw new Exception("");// a nova senha é diferente da confirmacao da senha ?
            }

            usuarioLogado.setSenha(getSenha2());
            getFacade().getUsuarioNFe().alterar(usuarioLogado);
            setMensagemID("msg_senhaAlterada_cli");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
            setJavaScriptFecharPopUpAlteracaoSenha("window.close();fireElementFromParent('form:btnAtualizaPagina');");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro_senha_cli", e.getMessage());
            setJavaScriptFecharPopUpAlteracaoSenha("");
        }
    }

    public void validarDados() throws Exception {
        if (!getUsuarioLogado().getPermiteAlterarPropriaSenha()) {
            throw new Exception("Usuário não possui permissão para alterar sua própria senha");
        }
        if (getUsuarioLogado().getNome().equals("")) {
            throw new Exception("O campo NOME USUÁRIO (Usuário) deve ser informado");
        }
        if (getUsuarioLogado().getUsuario().equals("")) {
            throw new Exception("O campo USER NAME (Usuário) deve ser informado");
        }
        if (getUsuarioLogado().getSenha().equals("")) {
            throw new Exception("O campo SENHA ATUAL (Usuário) deve ser informado");
        }
        if (getSenha1().equals("")) {
            throw new Exception("O campo NOVA SENHA (Usuário) deve ser informado");
        }
        if (getSenha2().equals("")) {
            throw new Exception("O campo CONFIRMAR SENHA (Usuário) deve ser informado");
        }
    }

    public String novo() {
        setUsuarioNFeVO(new UsuarioNFeVO());
        setSucesso(false);
        setErro(false);
        setEditar(false);
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public String editar() throws Exception {
        UsuarioNFeVO obj = (UsuarioNFeVO) context().getExternalContext().getRequestMap().get("usuario");

        if (usuarioLogadoEhAdmin()) {
            empresaVO.setId_Empresa(obj.getPerfilUsuarioNFe().getEmpresa().getId_Empresa());
            montarListaDePerfis();
        }

        obj.setNovoObj(false);
        obj.registrarObjetoVOAntesDaAlteracao();
        setUsuarioNFeVO(obj);
        setErro(false);
        setSucesso(true);
        setEditar(true);
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    public String gravar() {
        try {
            //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
            if (!Uteis.removerEspacosInicioFimString(usuarioNFeVO.getSenha()).equals(usuarioNFeVO.getSenha())) {
                throw new Exception("A senha não pode conter espaços no início ou no final");
            }
            //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
            if (!Uteis.removerEspacosInicioFimString(usuarioNFeVO.getUsuario()).equals(usuarioNFeVO.getUsuario())) {
                throw new Exception("O nome de usuário não pode conter espaços no início ou no final");
            }

            if (usuarioNFeVO.isNovoObj()) {
                getUsuarioNFeVO().getPerfilUsuarioNFe().setEmpresa(empresaVO);
                getFacade().getUsuarioNFe().incluir(usuarioNFeVO);
            } else {
                getUsuarioNFeVO().getPerfilUsuarioNFe().setEmpresa(empresaVO);
                if (usuarioNFeVO.manteveSenha()) {
                    usuarioNFeVO.setSenha(usuarioNFeVO.getSenhaDescriptografada());
                }
                getFacade().getUsuarioNFe().alterar(usuarioNFeVO);
            }

            usuarioNFeVO.setSenha(Uteis.encriptarNFe(usuarioNFeVO.getSenha()));
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");

            getControleConsulta().setValorConsulta("");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }

                try {
                    Integer.parseInt(getControleConsulta().getValorConsulta());
                } catch (Exception e) {
                    throw new Exception("Informe somente números");
                }


                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getUsuarioNFe().consultarPorCodigo(valorInt);
                } else {
                    int idEmpresa = getUsuarioLogado().getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
                    objs = getFacade().getUsuarioNFe().consultarPorCodigoEEmpresa(valorInt, idEmpresa);
                }
            }

            if (UteisValidacao.emptyString(getControleConsulta().getValorConsulta())) {
                throw new Exception("Informe a descrição para consultar");
            }

            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                String consulta = getControleConsulta().getValorConsulta();
                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getUsuarioNFe().consultarPorNome(consulta);
                } else {
                    int idEmpresa = getUsuarioLogado().getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
                    objs = getFacade().getUsuarioNFe().consultarPorNomeEEmpresa(consulta, idEmpresa);
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("usuario")) {
                String consulta = getControleConsulta().getValorConsulta();
                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getUsuarioNFe().consultarPorUsername(consulta);
                } else {
                    int idEmpresa = getUsuarioLogado().getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
                    objs = getFacade().getUsuarioNFe().consultarPorUsernameEEmpresa(consulta, idEmpresa);
                }
            }

            if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                String consulta = getControleConsulta().getValorConsulta();
                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getUsuarioNFe().consultarPorNomeEmpresa(consulta, 0);
                } else {
                    int idEmpresa = getUsuarioLogado().getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
                    objs = getFacade().getUsuarioNFe().consultarPorNomeEmpresa(consulta, idEmpresa);
                }
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        try {
            getFacade().getUsuarioNFe().excluir(usuarioNFeVO);
            setUsuarioNFeVO(new UsuarioNFeVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getTipoConsultaCombo() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("empresa", "Empresa"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("usuario", "Usuario"));
        return itens;
    }

    public List getListaDeEmpresas() throws Exception {
        return getFacade().getEmpresaNFe().obtenhaEmpresasCadastradas();
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public List<SelectItem> montarListaDePerfis() throws Exception {
        if (usuarioLogadoEhAdmin()) {
            setListaDePerfis(getFacade().getPerfilUsuarioNFe().obtenhaPerfisDaEmpresa(getEmpresaVO()));
        } else {
            UsuarioNFeVO usuario = this.getUsuarioLogado();
            EmpresaNFeVO empresa = usuario.getPerfilUsuarioNFe().getEmpresa();
            setListaDePerfis(getFacade().getPerfilUsuarioNFe().obtenhaPerfisDaEmpresa(empresa));
        }
        return listaDePerfis;
    }

    private boolean usuarioLogadoEhAdmin() throws Exception {
        return getUsuarioLogado().isAdministrador();
    }

    public UsuarioNFeVO getUsuarioNFeVO() {
        return usuarioNFeVO;
    }

    public void setUsuarioNFeVO(UsuarioNFeVO usuarioNFeVO) {
        this.usuarioNFeVO = usuarioNFeVO;
    }

    public UsuarioNFeVO getUsuarioLogado() throws Exception {
        usuarioLogado = (UsuarioNFeVO) super.getUsuarioLogado();
        return usuarioLogado;
    }

    public void setUsuarioLogado(UsuarioNFeVO usuarioLogado) {
        this.usuarioLogado = usuarioLogado;
    }

    public String getSenha1() {
        return senha1;
    }

    public void setSenha1(String senha1) {
        this.senha1 = senha1;
    }

    public String getSenha2() {
        return senha2;
    }

    public void setSenha2(String senha2) {
        this.senha2 = senha2;
    }

    public String getSenhaAtual() throws Exception {
        if (senhaAtual == null) {
            senhaAtual = "";
        }
        return senhaAtual;
    }

    public void setSenhaAtual(String senhaAtual) {
        this.senhaAtual = senhaAtual;
    }

    public String getJavaScriptFecharPopUpAlteracaoSenha() {
        return javaScriptFecharPopUpAlteracaoSenha;
    }

    public void setJavaScriptFecharPopUpAlteracaoSenha(String javaScriptFecharPopUpAlteracaoSenha) {
        this.javaScriptFecharPopUpAlteracaoSenha = javaScriptFecharPopUpAlteracaoSenha;
    }

    public EmpresaNFeVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaNFeVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public List<SelectItem> getListaDePerfis() throws Exception {
        if (listaDePerfis.isEmpty()) {
            montarListaDePerfis();
        }
        return listaDePerfis;
    }

    public void setListaDePerfis(List<SelectItem> listaDePerfis) {
        this.listaDePerfis = listaDePerfis;
    }

    public List getListaSelectItemStatus() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("", ""));
        itens.add(new SelectItem(true, "Ativo"));
        itens.add(new SelectItem(false, "Inativo"));
        return itens;
    }

    public boolean isEditar() {
        return editar;
    }

    public void setEditar(boolean editar) {
        this.editar = editar;
    }

    public boolean isPermitirEditarUsuarioSenha() throws Exception {
        return !isEditar() || usuarioLogadoEhAdmin();
    }
}

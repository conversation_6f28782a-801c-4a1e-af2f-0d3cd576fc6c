package controle.nfe;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.PerfilUsuarioNFeVO;
import negocio.comuns.arquitetura.UsuarioNFeVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class PerfilNFeControle extends SuperControle {

    private PerfilUsuarioNFeVO perfilVO;

    public PerfilNFeControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public String novo() throws Exception {
        PerfilUsuarioNFeVO perfil = new PerfilUsuarioNFeVO();

        UsuarioNFeVO usuarioLogado = (UsuarioNFeVO) getUsuarioLogado();
        PerfilUsuarioNFeVO perfilLogado = usuarioLogado.getPerfilUsuarioNFe();
        perfil.getEmpresa().setId_Empresa(perfilLogado.getEmpresa().getId_Empresa());

        setPerfilVO(perfil);
        setSucesso(false);
        setErro(false);
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public String editar() {
        PerfilUsuarioNFeVO obj = (PerfilUsuarioNFeVO) context().getExternalContext().getRequestMap().get("perfil");
        obj.setNovoObj(false);
        obj.registrarObjetoVOAntesDaAlteracao();
        setPerfilVO(obj);
        setMensagemID("msg_dados_editar");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public String gravar() {
        try {
            if (perfilVO.isNovoObj()) {
                getFacade().getPerfilUsuarioNFe().incluir(perfilVO);
            } else {
                getFacade().getPerfilUsuarioNFe().alterar(perfilVO);
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");

            getControleConsulta().setValorConsulta("");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            int idEmpresa = obtenhaIdDaEmpresaDoUsuarioLogado();

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }

                try {
                    Integer.parseInt(getControleConsulta().getValorConsulta());
                } catch (Exception e) {
                    throw new Exception("Informe somente números");
                }

                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());

                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getPerfilUsuarioNFe().consultarPorCodigo(valorInt);
                } else {
                    objs = getFacade().getPerfilUsuarioNFe().consultarPorCodigoEEmpresa(valorInt, idEmpresa);
                }
            }

            if (UteisValidacao.emptyString(getControleConsulta().getValorConsulta())) {
                throw new Exception("Informe a descrição para consultar");
            }

            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                String valorDaConsulta = getControleConsulta().getValorConsulta();

                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getPerfilUsuarioNFe().consultarPorNome(valorDaConsulta);
                } else {
                    objs = getFacade().getPerfilUsuarioNFe().consultarPorNomeEEmpresa(valorDaConsulta, idEmpresa);
                }
            }

            if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                String valorDaConsulta = getControleConsulta().getValorConsulta();

                if (usuarioLogadoEhAdmin()) {
                    objs = getFacade().getPerfilUsuarioNFe().consultarPorNomeEmpresa(valorDaConsulta, 0);
                } else {
                    objs = getFacade().getPerfilUsuarioNFe().consultarPorNomeEmpresa(valorDaConsulta, idEmpresa);
                }
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String excluir() {
        try {
            getFacade().getPerfilUsuarioNFe().excluir(perfilVO);
            setPerfilVO(new PerfilUsuarioNFeVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public List getListaSelectEmpresasCadastradas() throws Exception {
        return getFacade().getEmpresaNFe().obtenhaEmpresasCadastradas();
    }

    public List getTipoConsultaCombo() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("empresa", "Empresa"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    private boolean usuarioLogadoEhAdmin() throws Exception {
        return ((UsuarioNFeVO) getUsuarioLogado()).isAdministrador();
    }

    private int obtenhaIdDaEmpresaDoUsuarioLogado() throws Exception {
        return ((UsuarioNFeVO) getUsuarioLogado()).getPerfilUsuarioNFe().getEmpresa().getId_Empresa();
    }

    public PerfilUsuarioNFeVO getPerfilVO() {
        return perfilVO;
    }

    public void setPerfilVO(PerfilUsuarioNFeVO perfilVO) {
        this.perfilVO = perfilVO;
    }
}

package controle.notaFiscal;

import br.com.pactosolucoes.bi.dto.KeyCodFormJson;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import com.amazonaws.util.IOUtils;
import controle.arquitetura.security.UsuarioControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.notaFiscal.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SolicitacaoVO;
import negocio.comuns.utilitarias.StatusSolicitacaoEnum;
import negocio.comuns.utilitarias.TipoSolicitacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.notaFiscal.NotaFiscalHistorico;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

public class NotaFiscalControle extends UsuarioControle {

    private Integer filtroEmpresa;
    private List<NotaFiscalVO> listaNotas;
    private FiltroNotaFiscalTO filtro;
    private boolean marcarTodos = false;
    private NotaFiscalVO notaSelecionada;
    private List<ConfiguracaoNotaFiscalVO> listaConfiguracaoNotaFiscal;
    private boolean permissaoConsultaTodasEmpresas = false;
    private String justificativa;
    private boolean inicializado = false;
    private String onComplete;
    private int scrollerPage;
    private List<TotalizadorNotaFiscalTO> listaTotalizador;
    private List<ObjetoGenerico> listaParametrosSelecionado;
    private List<NotaFiscalHistoricoVO> listaNotaFiscalHistorico;
    private boolean apresentarFiltroTipoNota = false;
    private boolean apresentarFiltroEmpresaEnotas = false;
    private List<NotaFiscalVO> listaNotasCancelar;
    private List<NotaFiscalVO> listaNotasInutilizar;
    private List<NotaFiscalVO> listaNotasIgnoradas;
    private String emailEnviar;
    private NotaEnotasTO notaEnotasTO;
    private String periodoDtEmissao;
    private String periodoDtAutorizacao;
    private JSONObject jsonEnvio;
    private boolean apresentarInutilizarMarcadas = false;
    private boolean apresentarCancelarMarcadas = false;
    private boolean apresentarRPS = false;
    private NotaFiscalVO notaFiscalVOReenviar;
    private Date dataEmissaoReenvio;

    private List<NotaFiscalOperacaoVO> faixasInutilizadas;
    private String idEmpresaEnotas;
    private Integer numInicialInutilizar;
    private Integer numFinalInutilizar;
    private boolean apresentarInutilizarPorFaixa = false;
    private List<InutilizacaoNotaFiscalTO> inutilizacaoNotaFiscalTOList;
    private InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO;
    private Integer codigoConfigNota;
    private ListaPaginadaTO listaNotasPaginada;
    private static final Integer LISTA_PAGINADA_LIMIT = 10;
    private Set<Integer> notasSelecionadas;
    private Set<Integer> notasGeral;

    private List<SolicitacaoVO> listaSolicitacaoVOS = new ArrayList();

    private List<NotaFiscalVO> listaNotasAlterarStatus;

    private String motivoAlteracaoStatus;

    private String statusAlteracaoNotaFiscal;


    public NotaFiscalControle() throws Exception {
        obterUsuarioLogado();
        inicializar();
    }

    public void inicializar() {
        try {
            montarListaSelectItemEmpresa();
            carregarListaConfiguracoesNota();
            montarValidadeCertificado();
            if (!inicializado) {
                inicializarPaginacao();
                getFiltro().setConfiguracaoNotaFiscal(getListaEmpresasEnotas().size() > 0 ? (Integer) getListaEmpresasEnotas().get(0).getValue() : null);
                getFiltro().setDataRegistroInicio(Uteis.obterPrimeiroEUltimoDiaSemana(true));
                getFiltro().setDataRegistroFim(Calendario.hoje());
                consultarNotasFiscais();
                inicializado = true;
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    private void inicializarPaginacao() {
        listaNotasPaginada = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
    }

    public void montarValidadeCertificado() throws Exception {
        if(!UteisValidacao.emptyNumber(getEmpresaLogado().getConfiguracaoNotaFiscalNFSe().getCodigo())) {
            setCodigoConfigNota(getEmpresaLogado().getConfiguracaoNotaFiscalNFSe().getCodigo());
        } else if (!UteisValidacao.emptyNumber(getEmpresaLogado().getConfiguracaoNotaFiscalNFCe().getCodigo())) {
            setCodigoConfigNota(getEmpresaLogado().getConfiguracaoNotaFiscalNFCe().getCodigo());
        }
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<NotaFiscalVO> getListaNotas() {
        if (listaNotas == null) {
            listaNotas = new ArrayList<>();
        }
        return listaNotas;
    }

    public Integer getQtdListaNotas() {
        return getListaNotas().size();
    }

    public void setListaNotas(List<NotaFiscalVO> listaNotas) {
        this.listaNotas = listaNotas;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public NotaFiscalVO getNotaSelecionada() {
        if (notaSelecionada == null) {
            notaSelecionada = new NotaFiscalVO();
        }
        return notaSelecionada;
    }

    public void setNotaSelecionada(NotaFiscalVO notaSelecionada) {
        this.notaSelecionada = notaSelecionada;
    }

    public List<SelectItem> getListaTipoNotaFiscal() {
        return TipoNotaFiscalEnum.getListaCombo();
    }

    public List<SelectItem> getListaStatusNotaFiscal() {
        return StatusEnotasEnum.getListaCombo();
    }

    public List<SelectItem> getListaEmpresasEnotas() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        if (getListaConfiguracaoNotaFiscal().size() > 1) {
            lista.add(new SelectItem(0, "TODOS"));
        }

        for (ConfiguracaoNotaFiscalVO obj : getListaConfiguracaoNotaFiscal()) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        return lista;
    }

    public List<SelectItem> getListaEmpresasPorIdEnotas() {
        List<SelectItem> lista = new ArrayList<>();

        for (ConfiguracaoNotaFiscalVO obj : getListaConfiguracaoNotaFiscal()) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        return lista;
    }

    public FiltroNotaFiscalTO getFiltro() {
        if (filtro == null) {
            filtro = new FiltroNotaFiscalTO();
        }
        return filtro;
    }

    public void setFiltro(FiltroNotaFiscalTO filtro) {
        this.filtro = filtro;
    }

    public List<ConfiguracaoNotaFiscalVO> getListaConfiguracaoNotaFiscal() {
        if (listaConfiguracaoNotaFiscal == null) {
            listaConfiguracaoNotaFiscal = new ArrayList<>();
        }
        return listaConfiguracaoNotaFiscal;
    }

    public void setListaConfiguracaoNotaFiscal(List<ConfiguracaoNotaFiscalVO> listaConfiguracaoNotaFiscal) {
        this.listaConfiguracaoNotaFiscal = listaConfiguracaoNotaFiscal;
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public boolean isInicializado() {
        return inicializado;
    }

    public void setInicializado(boolean inicializado) {
        this.inicializado = inicializado;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public List<TotalizadorNotaFiscalTO> getListaTotalizador() {
        if (listaTotalizador == null) {
            listaTotalizador = new ArrayList<>();
        }
        return listaTotalizador;
    }

    public void setListaTotalizador(List<TotalizadorNotaFiscalTO> listaTotalizador) {
        this.listaTotalizador = listaTotalizador;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        if (listaParametrosSelecionado == null) {
            listaParametrosSelecionado = new ArrayList<>();
        }
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public List<NotaFiscalHistoricoVO> getListaNotaFiscalHistorico() {
        if (listaNotaFiscalHistorico == null) {
            listaNotaFiscalHistorico = new ArrayList<>();
        }
        return listaNotaFiscalHistorico;
    }

    public void setListaNotaFiscalHistorico(List<NotaFiscalHistoricoVO> listaNotaFiscalHistorico) {
        this.listaNotaFiscalHistorico = listaNotaFiscalHistorico;
    }

    public boolean isApresentarFiltroTipoNota() {
        return apresentarFiltroTipoNota;
    }

    public void setApresentarFiltroTipoNota(boolean apresentarFiltroTipoNota) {
        this.apresentarFiltroTipoNota = apresentarFiltroTipoNota;
    }

    public boolean isApresentarFiltroEmpresaEnotas() {
        return apresentarFiltroEmpresaEnotas;
    }

    public void setApresentarFiltroEmpresaEnotas(boolean apresentarFiltroEmpresaEnotas) {
        this.apresentarFiltroEmpresaEnotas = apresentarFiltroEmpresaEnotas;
    }

    public Integer getQtdNotasCancelar() {
        return getListaNotasCancelar().size();
    }

    public Integer getQtdNotasInutilizar() {
        return getListaNotasInutilizar().size();
    }

    public Integer getQtdNotasIgnoradas() {
        return getListaNotasIgnoradas().size();
    }

    public Integer getQtdNotasAlterarStatus() {
        return getListaNotasAlterarStatus().size();
    }

    public List<NotaFiscalVO> getListaNotasCancelar() {
        if (listaNotasCancelar == null) {
            listaNotasCancelar = new ArrayList<>();
        }
        return listaNotasCancelar;
    }

    public void setListaNotasCancelar(List<NotaFiscalVO> listaNotasCancelar) {
        this.listaNotasCancelar = listaNotasCancelar;
    }

    public List<NotaFiscalVO> getListaNotasInutilizar() {
        if (listaNotasInutilizar == null) {
            listaNotasInutilizar = new ArrayList<>();
        }
        return listaNotasInutilizar;
    }

    public void setListaNotasInutilizar(List<NotaFiscalVO> listaNotasInutilizar) {
        this.listaNotasInutilizar = listaNotasInutilizar;
    }

    public List<NotaFiscalVO> getListaNotasIgnoradas() {
        if (listaNotasIgnoradas == null) {
            listaNotasIgnoradas = new ArrayList<>();
        }
        return listaNotasIgnoradas;
    }

    public void setListaNotasIgnoradas(List<NotaFiscalVO> listaNotasIgnoradas) {
        this.listaNotasIgnoradas = listaNotasIgnoradas;
    }

    public String getEmailEnviar() {
        if (emailEnviar == null) {
            emailEnviar = "";
        }
        return emailEnviar;
    }

    public void setEmailEnviar(String emailEnviar) {
        this.emailEnviar = emailEnviar;
    }

    public NotaEnotasTO getNotaEnotasTO() {
        if (notaEnotasTO == null) {
            notaEnotasTO = new NotaEnotasTO();
        }
        return notaEnotasTO;
    }

    public void setNotaEnotasTO(NotaEnotasTO notaEnotasTO) {
        this.notaEnotasTO = notaEnotasTO;
    }

    public String marcarTodosItens() {
        if (isMarcarTodos()) {
            try {
                this.setNotasSelecionadas(this.getNotasGeral());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        } else {
            this.setNotasSelecionadas(new HashSet<>());
        }

        for (NotaFiscalVO notaFiscalVO : getListaNotas()) {
            notaFiscalVO.setSelecionado(isMarcarTodos());
        }
        return "";
    }

    public void selecionarNota() {
        NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
        setNotaSelecionada(obj);
    }

    public void inutilizarNota() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(getJustificativa())) {
                throw new Exception("Informe uma justificativa");
            }

            getFacade().getNotaFiscalOperacao().gerarSolicitacaoOperacao(getKey(), getJustificativa(), getNotaSelecionada(), getUsuarioLogado(), OperacaoNotaFiscalEnum.INUTILIZAR);
            atualizarNotaNaLista(getNotaSelecionada(), true);

            montarSucessoGrowl("Solicitação de inutilização enviada.");
            setOnComplete("Richfaces.hideModalPanel('modalInutilizar')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void cancelarNota() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(getJustificativa())) {
                throw new Exception("Informe uma justificativa");
            }

            getFacade().getNotaFiscalOperacao().gerarSolicitacaoOperacao(getKey(), getJustificativa(), getNotaSelecionada(), getUsuarioLogado(), OperacaoNotaFiscalEnum.CANCELAR);
            atualizarNotaNaLista(getNotaSelecionada(), true);

            montarSucessoGrowl("Solicitação de cancelamento enviada.");
            setOnComplete("Richfaces.hideModalPanel('modalCancelar')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararInutilizarNotasSelecionadas() {
        try {
            limparMsg();
            setOnComplete("");
            setJustificativa("");
            setListaNotasCancelar(new ArrayList<>());
            setListaNotasInutilizar(new ArrayList<>());
            setListaNotasIgnoradas(new ArrayList<>());

            List<NotaFiscalVO> listaNotasSelecionadas = obterListaGeralNotasSelecionadas();
            for (NotaFiscalVO obj : listaNotasSelecionadas) {
                if (this.getNotasSelecionadas().contains(obj.getCodigo())) {
                    if (obj.isPodeInutilizar()) {
                        getListaNotasInutilizar().add(obj);
                    } else {
                        getListaNotasIgnoradas().add(obj);
                    }
                }
            }

            setApresentarInutilizarPorFaixa(false);
            setOnComplete("Richfaces.showModalPanel('modalInutilizarNotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void inutilizarNotasSelecionadas() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(getJustificativa())) {
                throw new Exception("Informe uma justificativa");
            }

            if (isApresentarInutilizarPorFaixa()) {
                if (UteisValidacao.emptyNumber(getNumInicialInutilizar())) {
                    throw new Exception("Informe uma numeração inicial para inutilizar");
                }

                if (UteisValidacao.emptyNumber(getNumFinalInutilizar())) {
                    throw new Exception("Informe uma numeração final para inutilizar");
                }

                if (getNumInicialInutilizar() > getNumFinalInutilizar()) {
                    throw new Exception("A numeração final deverá ser maior ou igual a inicial");
                }

                ConfiguracaoNotaFiscalVO configNota = buscaConfigNotaPorCodigo(getCodigoConfigNota());

                AmbienteEmissaoNotaFiscalEnum ambienteSelecionado = AmbienteEmissaoNotaFiscalEnum.obterPorCodigo(configNota.getAmbienteEmissao().getCodigo());
                String serie = configNota.getConfigProducaoVO().getSerieNFe();
                if(ambienteSelecionado.equals(AmbienteEmissaoNotaFiscalEnum.HOMOLOGACAO)) {
                    serie = configNota.getConfigHomologacaoVO().getSerieNFe();
                }

                getFacade().getNotaFiscalOperacao().gerarSolicitacaoInutilizacaoPorFaixa(getKey(), configNota.getIdEnotas(), getJustificativa(), getNumInicialInutilizar(), getNumFinalInutilizar(),
                        getUsuarioLogado(), ambienteSelecionado.getDescricaoSemAcentuacao(), serie, configNota.getTipoNotaFiscal().getCodigo());
            } else {
                for (NotaFiscalVO obj : getListaNotasInutilizar()) {
                    getFacade().getNotaFiscalOperacao().gerarSolicitacaoOperacao(getKey(), getJustificativa(), obj, getUsuarioLogado(), OperacaoNotaFiscalEnum.INUTILIZAR);
                    atualizarNotaNaLista(obj, true);
                }
            }

            montarSucessoGrowl("Soliticação enviada");
            setOnComplete("Richfaces.hideModalPanel('modalInutilizarNotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void consultarHistoricoInutilizacao() {
        try {
            limparMsg();
            setOnComplete("");
            ConfiguracaoNotaFiscalVO configNota = buscaConfigNotaPorCodigo(getCodigoConfigNota());
            List<NotaFiscalOperacaoVO> operacoes = getFacade().getNotaFiscalOperacao().obterInutilizacoesRealizadas(configNota.getIdEnotas());
            setFaixasInutilizadas(operacoes);

            List<InutilizacaoNotaFiscalTO> listaInutilizacao = new ArrayList<InutilizacaoNotaFiscalTO>();
            for(NotaFiscalOperacaoVO notaFiscalOperacaoVO : getFaixasInutilizadas()) {
                try {
                    InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = getFacade().getNotaFiscalOperacao().consultarInutilizacao(notaFiscalOperacaoVO);
                    inutilizacaoNotaFiscalTO.setDataRegistro(notaFiscalOperacaoVO.getDataRegistro());
                    if(inutilizacaoNotaFiscalTO != null) {
                        listaInutilizacao.add(inutilizacaoNotaFiscalTO);
                    }
                } catch (Exception ignored) {
                }
            }
            setInutilizacaoNotaFiscalTOList(listaInutilizacao);

            setOnComplete("Richfaces.showModalPanel('modalHistoricoInutilizacao');Richfaces.hideModalPanel('modalInutilizarNotas')");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void prepararCancelarNotasSelecionadas() {
        try {
            limparMsg();
            setOnComplete("");
            setJustificativa("");
            setListaNotasCancelar(new ArrayList<>());
            setListaNotasInutilizar(new ArrayList<>());
            setListaNotasIgnoradas(new ArrayList<>());

            List<NotaFiscalVO> listaNotasSelecionadas = obterListaGeralNotasSelecionadas();
            for (NotaFiscalVO obj : listaNotasSelecionadas) {
                if (this.getNotasSelecionadas().contains(obj.getCodigo())) {
                    if (obj.isPodeCancelar()) {
                        getListaNotasCancelar().add(obj);
                    } else {
                        getListaNotasIgnoradas().add(obj);
                    }
                }
            }

            if (UteisValidacao.emptyList(getListaNotasCancelar()) && UteisValidacao.emptyList(getListaNotasIgnoradas())) {
                throw new Exception("Nenhuma nota foi selecionada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalCancelarNotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void cancelarNotasSelecionadas() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(getJustificativa())) {
                throw new Exception("Informe uma justificativa");
            }

            for (NotaFiscalVO obj : getListaNotasCancelar()) {
                getFacade().getNotaFiscalOperacao().gerarSolicitacaoOperacao(getKey(), getJustificativa(), obj, getUsuarioLogado(), OperacaoNotaFiscalEnum.CANCELAR);
                atualizarNotaNaLista(obj, true);
            }

            montarSucessoGrowl("Soliticação enviada");
            setOnComplete("Richfaces.hideModalPanel('modalCancelarNotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void prepararEnviarEmail() {
        try {
            limparMsg();
            setOnComplete("");

            NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
            setNotaSelecionada(obj);
            setEmailEnviar(obj.getClienteEmail());

            setOnComplete("Richfaces.showModalPanel('modalEnviarEmailNotaFiscal')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void enviarEmail() {
        try {
            limparMsg();
            setOnComplete("");
            montarSucessoGrowl(getFacade().getNotaFiscal().enviarEmailNotaFiscal(getEmailEnviar(), getNotaSelecionada(), getUsuarioLogado(), ""));
            setOnComplete("Richfaces.hideModalPanel('modalEnviarEmailNotaFiscal')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void visualizarHistoricoNotaFiscal() {
        try {
            limparMsg();
            setOnComplete("");
            setListaNotaFiscalHistorico(new ArrayList<>());

            NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
            setNotaSelecionada(obj);

            setListaNotaFiscalHistorico(getFacade().getNotaFiscalHistorico().consultar(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            setOnComplete("Richfaces.showModalPanel('modalHistoricoNotaFiscal');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void exibirParams(ActionEvent evt) {
        try {
            setOnComplete("");
            String params = (String) evt.getComponent().getAttributes().get("params");
            NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
            listaParametrosSelecionado = null;
            if (params != null && obj != null) {
                setNotaSelecionada(obj);
                if (params.equals("envio")) {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(obj.getJsonEnvio());
                    setOnComplete("Richfaces.showModalPanel('modalParametrosNota');");
                } else if (params.equals("retorno")) {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(obj.getJsonRetorno());
                    setOnComplete("Richfaces.showModalPanel('modalParametrosNota');");
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void totalizar() {
        setListaTotalizador(new ArrayList<>());
        Set<Integer> tiposNota = new HashSet<>();
        setApresentarInutilizarMarcadas(false);
        setApresentarCancelarMarcadas(false);
        setApresentarRPS(false);

        Map<String, TotalizadorNotaFiscalTO> map = new HashMap<>();
        for (NotaFiscalVO obj : getListaNotas()) {
            if (obj.isPodeCancelar()) {
                setApresentarCancelarMarcadas(true);
            }
            if (obj.getTipo().equals(TipoNotaFiscalEnum.NFSE)) {
                setApresentarRPS(true);
            }

            tiposNota.add(obj.getTipo().getCodigo());
            TotalizadorNotaFiscalTO totalVO = map.get(obj.getStatusNotaApresentar().toUpperCase());
            if (totalVO == null) {
                totalVO = new TotalizadorNotaFiscalTO();
                totalVO.setDescricao(obj.getStatusNotaApresentar().toUpperCase());
                totalVO.setCss(obj.getStatusNotaEnum().getCss());
                totalVO.setTitle(obj.getStatusNotaEnum().getHint());
            }

            totalVO.setQuantidade(totalVO.getQuantidade() + 1);
            totalVO.setValor(totalVO.getValor() + obj.getValor());
            map.put(obj.getStatusNotaApresentar().toUpperCase(), totalVO);
        }

        setApresentarInutilizarMarcadas(getListaNotas().size() > 0);

        setApresentarFiltroTipoNota(tiposNota.size() > 1);

        Set<String> keys = map.keySet();
        for (String sta : keys) {
            getListaTotalizador().add(map.get(sta));
        }
    }

    public void abrirCadastroClienteColaborador() {
        NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
        try {
            if (obj != null && !UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {

                ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(obj.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
                if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    irParaTelaCliente(clienteVO);
                    setOnComplete("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
                    return;
                }

                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(obj.getPessoaVO().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS);
                if (!UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
                    irParaTelaColaborador(colaboradorVO);
                    setOnComplete("abrirPopup('colaboradorForm.jsp', 'Colaborador', 1024, 700);");
                    return;
                }

                throw new Exception("Cliente ou Colaborador não Encontrado.");

            } else {
                throw new Exception("Cliente ou Colaborador não Encontrado.");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void carregarListaConfiguracoesNota() {
        try {
            List<ConfiguracaoNotaFiscalVO> listaConfigsNotaFiscal = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getFiltroEmpresa(), null, null, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for(ConfiguracaoNotaFiscalVO configNota: listaConfigsNotaFiscal){
                if(!configNota.isAtivo()){
                    configNota.setDescricao(configNota.getDescricao() + " - INATIVA");
                }
            }
            listaConfigsNotaFiscal.sort(Comparator.comparing(ConfiguracaoNotaFiscalVO::isAtivo).reversed());
            setListaConfiguracaoNotaFiscal(listaConfigsNotaFiscal);
            if (getListaConfiguracaoNotaFiscal().size() == 1) {
                setApresentarFiltroEmpresaEnotas(false);
            } else {
                setApresentarFiltroEmpresaEnotas(true);
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public void limparFiltros() {
        try {
            limparMsg();
            getFiltro().setDataRegistroInicio(null);
            getFiltro().setDataRegistroFim(null);
            getFiltro().setDataEmissaoInicio(null);
            getFiltro().setDataEmissaoFim(null);
            getFiltro().setDataAutorizacaoInicio(null);
            getFiltro().setDataAutorizacaoFim(null);
            getFiltro().setCodNotaFiscal(null);
            getFiltro().setNumeroNota("");
            getFiltro().setRazaoSocial("");
            getFiltro().setCpfCnpj("");
            getFiltro().setChaveAcesso("");
            getFiltro().setStatusNota("");
            setPeriodoDtEmissao("");
            setPeriodoDtAutorizacao("");
            getFiltro().setTipoNota(TipoNotaFiscalEnum.TODAS.getCodigo());
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public void consultarNotasFiscais() {
        try {
            limparMsg();
            setMarcarTodos(false);
            setListaNotas(new ArrayList<>());
            setListaTotalizador(new ArrayList<>());
            setApresentarInutilizarMarcadas(false);
            setApresentarCancelarMarcadas(false);
            setApresentarRPS(false);
            setApresentarFiltroTipoNota(false);
            setNotasSelecionadas(new HashSet<>());
            setNotasGeral(new HashSet<>());
            this.getListaNotasPaginada().setCount(0);

            if(!UteisValidacao.emptyNumber(getFiltroEmpresa())){
                getFiltro().setEmpresa(getFiltroEmpresa());
            }else{
                getFiltro().setEmpresa(0);
            }

            setListaNotas(getFacade().getNotaFiscal().consultarNotasFiscais(getFiltro(), LISTA_PAGINADA_LIMIT, 0));
            setNotasGeral(getFacade().getNotaFiscal().codigosNotas(getFiltro()));
            setListaTotalizador(getFacade().getNotaFiscal().consultarNotasFiscaisTotalizador(getFiltro()));

            Integer totalNotas = 0;
            for (TotalizadorNotaFiscalTO dto : getListaTotalizador()) {
                totalNotas+= dto.getQuantidade();
            }
            this.getListaNotasPaginada().setCount(totalNotas);

            LayoutTelaNotaFiscalTO layoutTO = getFacade().getNotaFiscal().obterLayoutNotaFiscal(getFiltro());
            setApresentarInutilizarMarcadas(layoutTO.isApresentarInutilizar());
            setApresentarCancelarMarcadas(layoutTO.isApresentarCancelar());
            setApresentarRPS(layoutTO.isApresentarRPS());
            setApresentarFiltroTipoNota(layoutTO.isApresentarFiltroTipoNota());

            if (UteisValidacao.emptyList(getListaNotas())) {
                montarMsgAlert("Nenhuma nota encontrada.");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    private InutilizacaoNotaFiscalTO prepararStatusInutilizacao(NotaFiscalVO notaFiscalVO, NotaEnotasTO enotasTO) {
        InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = null;
        try {
            if (notaFiscalVO.getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZACAOSOLICITADO) ||
                    notaFiscalVO.getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZACAONEGADA) ||
                    notaFiscalVO.getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZADA)
            ) {
                inutilizacaoNotaFiscalTO = getFacade().getNotaFiscal().consultarInutilizacaoNotaEnotas(notaFiscalVO);

                if (inutilizacaoNotaFiscalTO.getStatus().equals(StatusEnotasEnum.NEGADA.getDescricaoEnotas()) &&
                        inutilizacaoNotaFiscalTO.getMotivoStatus().contains("Uma NF-e da faixa já está inutilizada na Base de dados da SEFAZ")) {
                    enotasTO.setStatus(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                    notaFiscalVO.setStatusNota(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                } else if (inutilizacaoNotaFiscalTO.getStatus().equals(StatusEnotasEnum.NEGADA.getDescricaoEnotas())) {
                    enotasTO.setStatus(StatusEnotasEnum.INUTILIZACAONEGADA.getDescricaoEnotas());
                    notaFiscalVO.setStatusNota(StatusEnotasEnum.INUTILIZACAONEGADA.getDescricaoEnotas());
                }

                if (inutilizacaoNotaFiscalTO.getStatus().equals(StatusEnotasEnum.AUTORIZADA.getDescricaoEnotas())) {
                    enotasTO.setStatus(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                    notaFiscalVO.setStatusNota(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                }

                return inutilizacaoNotaFiscalTO;
            }
        } catch (Exception ex) {
            Uteis.logar("Erro ao consultar inutilização de notas");
            Uteis.logar(ex, NotaFiscalControle.class);
        }

        return inutilizacaoNotaFiscalTO;
    }

    public void consultarNotaEnotas() {
        try {
            limparMsg();
            setOnComplete("");

            NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
            setNotaSelecionada(obj);

            if (UteisValidacao.emptyString(getNotaSelecionada().getIdExterno()) && UteisValidacao.emptyString(getNotaSelecionada().getIdPacto())) {
                throw new Exception("Nota sem referência");
            }

            NotaEnotasTO enotasTO = getFacade().getNotaFiscal().consultarNotaEnotas(getNotaSelecionada());

            InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = prepararStatusInutilizacao(obj, enotasTO);
            setInutilizacaoNotaFiscalTO(inutilizacaoNotaFiscalTO);
            setNotaEnotasTO(enotasTO);

            obj.setStatusNota(enotasTO.getStatus());
            atualizarNotaNaLista(obj, false);

            setOnComplete("Richfaces.showModalPanel('modalNotaEnotas')");
        } catch (Exception ex) {
            if(ex.getMessage().contains("A Nota fiscal não foi encontrada.")){
                Uteis.logar("A nota fiscal foi enviada para o serviço de notas, mais ainda não foi processada.");
                montarSucessoGrowl("A nota está sendo processada. Consulte novamente em alguns instantes.");
            }else{
                Uteis.logar("Erro ao consultar a nota no serviço de notas");
                montarErro("Erro ao consultar status da nota, favor tentar novamente em alguns instantes.");
            }

            Uteis.logar(ex, NotaFiscalControle.class);
        }
    }

    public void atualizarInformacoesComDadosEnotas() {
        try {
            limparMsg();
            setOnComplete("");

            getFacade().getNotaFiscal().atualizarDadosNotaFiscalEnotas(getNotaSelecionada(), getNotaEnotasTO(), getUsuarioLogado());

            montarSucessoGrowl("Dados sincronizados.");
            setOnComplete("Richfaces.hideModalPanel('modalNotaEnotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<SelectItem> getListaPeriodos() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("", "Filtro rápido de período"));
        objs.add(new SelectItem("HJ", "Hoje"));
        objs.add(new SelectItem("ON", "Ontem"));
        objs.add(new SelectItem("SE", "Esta Semana"));
        objs.add(new SelectItem("ME", "Mês"));
        objs.add(new SelectItem("SEP", "Semana Passada"));
        objs.add(new SelectItem("MEP", "Mês Passado"));
        return objs;
    }

    public void alterarPeriodoDataEmissao() throws Exception {
        if (UteisValidacao.emptyString(getPeriodoDtEmissao())) {
            getFiltro().setDataEmissaoInicio(null);
            getFiltro().setDataEmissaoFim(null);
        } else if (getPeriodoDtEmissao().equals("HJ")) {
            getFiltro().setDataEmissaoInicio(Calendario.hoje());
            getFiltro().setDataEmissaoFim(Calendario.hoje());
        } else if (getPeriodoDtEmissao().equals("ON")) {
            getFiltro().setDataEmissaoInicio(Uteis.obterDataAnterior(Calendario.hoje(), 1));
            getFiltro().setDataEmissaoFim(Uteis.obterDataAnterior(Calendario.hoje(), 1));
        } else if (getPeriodoDtEmissao().equals("SE")) {
            getFiltro().setDataEmissaoInicio(Uteis.obterPrimeiroEUltimoDiaSemana(true));
            getFiltro().setDataEmissaoFim(Uteis.obterPrimeiroEUltimoDiaSemana(false));
        } else if (getPeriodoDtEmissao().equals("ME")) {
            getFiltro().setDataEmissaoInicio(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            getFiltro().setDataEmissaoFim(Uteis.obterUltimoDiaMes(Calendario.hoje()));
        } else if (getPeriodoDtEmissao().equals("SEP")) {
            getFiltro().setDataEmissaoInicio(Uteis.obterPrimeiroEUltimoDiaSemana(true, Uteis.obterDataAnterior(Calendario.hoje(), 7)));
            getFiltro().setDataEmissaoFim(Uteis.obterPrimeiroEUltimoDiaSemana(false, Uteis.obterDataAnterior(Calendario.hoje(), 7)));
        } else if (getPeriodoDtEmissao().equals("MEP")) {
            getFiltro().setDataEmissaoInicio(Uteis.obterPrimeiroDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30)));
            getFiltro().setDataEmissaoFim(Uteis.obterUltimoDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30)));
        }
    }

    public void alterarPeriodoDtAutorizacao() throws Exception {
        if (UteisValidacao.emptyString(getPeriodoDtAutorizacao())) {
            getFiltro().setDataAutorizacaoInicio(null);
            getFiltro().setDataAutorizacaoFim(null);
        } else if (getPeriodoDtAutorizacao().equals("HJ")) {
            getFiltro().setDataAutorizacaoInicio(Calendario.hoje());
            getFiltro().setDataAutorizacaoFim(Calendario.hoje());
        } else if (getPeriodoDtAutorizacao().equals("ON")) {
            getFiltro().setDataAutorizacaoInicio(Uteis.obterDataAnterior(Calendario.hoje(), 1));
            getFiltro().setDataAutorizacaoFim(Uteis.obterDataAnterior(Calendario.hoje(), 1));
        } else if (getPeriodoDtAutorizacao().equals("SE")) {
            getFiltro().setDataAutorizacaoInicio(Uteis.obterPrimeiroEUltimoDiaSemana(true));
            getFiltro().setDataAutorizacaoFim(Uteis.obterPrimeiroEUltimoDiaSemana(false));
        } else if (getPeriodoDtAutorizacao().equals("ME")) {
            getFiltro().setDataAutorizacaoInicio(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            getFiltro().setDataAutorizacaoFim(Uteis.obterUltimoDiaMes(Calendario.hoje()));
        } else if (getPeriodoDtAutorizacao().equals("SEP")) {
            getFiltro().setDataAutorizacaoInicio(Uteis.obterPrimeiroEUltimoDiaSemana(true, Uteis.obterDataAnterior(Calendario.hoje(), 7)));
            getFiltro().setDataAutorizacaoFim(Uteis.obterPrimeiroEUltimoDiaSemana(false, Uteis.obterDataAnterior(Calendario.hoje(), 7)));
        } else if (getPeriodoDtAutorizacao().equals("MEP")) {
            getFiltro().setDataAutorizacaoInicio(Uteis.obterPrimeiroDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30)));
            getFiltro().setDataAutorizacaoFim(Uteis.obterUltimoDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30)));
        }
    }

    public void downloadPDFSelecionadas() {
        downloadArquivos("pdf");
    }

    public void downloadXMLSelecionadas() {
        downloadArquivos("xml");
    }

    private void downloadArquivos(String formato) {
        try {
            setOnComplete("");
            limparMsg();
            List<byte[]> arquivos = new ArrayList<>();
            List<String> nomes = new ArrayList<>();

            List<NotaFiscalVO> listaNotasSelecionadas = obterListaGeralNotasSelecionadas();
            int minimoNotasSolicitacao = 15;
            try {
                minimoNotasSolicitacao = Integer.parseInt(PropsService.getPropertyValue(PropsService.minimoNotasSolicitacao));
            }catch (Exception ignore){
            }
            if(listaNotasSelecionadas.size() >= minimoNotasSolicitacao){
                SolicitacaoVO novaSolicitacao = new SolicitacaoVO();
                novaSolicitacao.setUsuarioSolicitante(getUsuarioLogado());
                novaSolicitacao.getEmpresa().setCodigo(filtro.getEmpresa());
                if(formato.equalsIgnoreCase("PDF")){
                    novaSolicitacao.setTipo(TipoSolicitacaoEnum.NOTAS_PDF);
                } else if(formato.equalsIgnoreCase("XML")){
                    novaSolicitacao.setTipo(TipoSolicitacaoEnum.NOTAS_XML);
                }
                JSONObject dadosSolicitacao = new JSONObject();
                Integer[] arrayOrdernado = getNotasSelecionadas().toArray(new Integer[0]);
                Arrays.sort(arrayOrdernado);
                dadosSolicitacao.put("codigoNotas", new JSONArray(Arrays.asList(arrayOrdernado)));
                String urlEnvioEmail = getUrl();
                dadosSolicitacao.put("urlEnvioEmail", urlEnvioEmail);
                novaSolicitacao.setDadosSolicitacao(dadosSolicitacao.toString());
                if(!validarSolicitacaoExistente(novaSolicitacao)) {
                    getFacade().getSolicitacao().incluir(novaSolicitacao);
                    enviarSolicitacao(novaSolicitacao);
                    exibirSolicitacoes();
                    montarSucessoGrowl("Sua solicitação está sendo processada, dentro de instantes  o link para download será disponibilizado no seu email, ou menu Solicitações de Download");
                }
            } else {
                for (NotaFiscalVO obj : listaNotasSelecionadas) {
                    try {
                        if (this.getNotasSelecionadas().contains(obj.getCodigo())) {
                            if ((formato.equalsIgnoreCase("PDF") && obj.isApresentaPDF()) ||
                                    (formato.equalsIgnoreCase("XML") && obj.isApresentaXML())) {

                                String nomeArquivo = "";
                                byte[] arquivo = null;

                                if (obj.getTipo().equals(TipoNotaFiscalEnum.NFSE) && !UteisValidacao.emptyString(obj.getNumeroNota())) {
                                    nomeArquivo = obj.getNumeroNota();
                                } else {
                                    nomeArquivo = obj.getCodigo().toString();
                                }
                                if (formato.equalsIgnoreCase("PDF")) {

                                    nomeArquivo = nomeArquivo + "-PDF." + formato.toLowerCase();
                                    arquivo = gerarArquivo(obj.getLinkPDF());

                                } else if (formato.equalsIgnoreCase("XML")) {

                                    nomeArquivo = nomeArquivo + "-XML." + formato.toLowerCase();
                                    arquivo = gerarArquivoXmlNotas(obj.getLinkXML());

                                }

                                if (arquivo != null && arquivo.length > 0) {
                                    arquivos.add(arquivo);
                                    nomes.add(nomeArquivo);
                                }
                            }
                        }
                    } catch (Exception ignored) {
                    }
                }

                if (arquivos.size() > 0) {
                    String absolutePath = this.getServletContext().getRealPath("relatorio") + File.separator;
                    String nomeArquivo = formato.toUpperCase() + System.currentTimeMillis() + ".zip";

                    String nomeArquivoGerado = absolutePath + nomeArquivo;
                    boolean gerou = Uteis.zip(nomeArquivoGerado, arquivos, nomes);
                    if (gerou) {
                        String onComplete = "abrirPopup('UpdateServlet?op=downloadfile&file=" + nomeArquivo + "&mimetype=application/zip','Notas', 800,200);";
                        setOnComplete(onComplete);
                    } else {
                        throw new ConsistirException("Erro ao gerar aquivo.");
                    }
                } else {
                    throw new ConsistirException("Não foi selecionado nenhuma nota fiscal.");
                }
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private boolean validarSolicitacaoExistente(SolicitacaoVO novaSolicitacao) throws Exception {
        SolicitacaoVO existente = getFacade().getSolicitacao().existeSolicitacaoComMesmoDados(novaSolicitacao);
        if(UteisValidacao.notEmptyNumber(existente.getCodigo())){

            if(existente.getStatus().equals(StatusSolicitacaoEnum.CONCLUIDA)){
                imprimirArquivoSolicitacao(existente);
            } else {
                StringBuilder retorno = new StringBuilder();
                retorno.append("Existe uma solicitação com status "+existente.getStatus().getDescricao()+" com os mesmos dados solicidados.");
                retorno.append(" Dentro de alguns instantes consulte suas solicitações para verificar se ela foi concluída");
                montarErro(retorno.toString());
                exibirSolicitacoes();
            }
            return  true;
        }
       return false;
    }

    public void imprimirArquivoSolicitacao() {
        try {
            SolicitacaoVO solicitacaoVO = (SolicitacaoVO) context().getExternalContext().getRequestMap().get("item");
            if(solicitacaoVO == null){
                throw new Exception("Erro ao solicitar download, nenhuma solicitação selecionada");
            }
            imprimirArquivoSolicitacao(solicitacaoVO);
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void imprimirArquivoSolicitacao(SolicitacaoVO solicitacaoVO) throws Exception {
        try {
            setOnComplete("");
            limparMsg();
            String pathRelatorio = this.getServletContext().getRealPath("relatorio");
            String nomeArquivo = String.format("%s-%s-%s.zip", new Object[]{
                    "nota",
                    solicitacaoVO.getCodigo(),
                    new Date().getTime()
            });
            byte[] dadosArquivo = gerarArquivo(solicitacaoVO.getLinkArquivoNotas());
            File file = new File(pathRelatorio + File.separator + nomeArquivo);
            try (FileOutputStream fos = new FileOutputStream(file);) {
                fos.write(dadosArquivo);
                String onComplete = "abrirPopup('UpdateServlet?op=downloadfile&file=" + nomeArquivo + "&mimetype=application/zip','Notas', 800,200);";
                setOnComplete(onComplete);
            }
        } catch (Exception e ){
            throw e;
        }
    }

    private List<NotaFiscalVO> obterListaGeralNotasSelecionadas() throws Exception {
        List<NotaFiscalVO> listaGeral = new ArrayList<>();
        Set<Integer> notasSelecionadas = new HashSet<>();
        for (Integer notaSele : this.getNotasSelecionadas()) {
            boolean encontrou = false;
            for (NotaFiscalVO obj : getListaNotas()) {
                if (notaSele.equals(obj.getCodigo())) {
                    listaGeral.add(obj);
                    encontrou = true;
                    break;
                }
            }
            if (!encontrou) {
                notasSelecionadas.add(notaSele);
            }
        }
        if (!UteisValidacao.emptyList(notasSelecionadas)) {
            listaGeral.addAll(getFacade().getNotaFiscal().consultarNotasFiscais(this.getFiltro(), notasSelecionadas, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        }
        Ordenacao.ordenarLista(listaGeral, "codigo");
        return listaGeral;
    }

    private byte[] gerarArquivo(String urlDownload) throws IOException {
        URL url = new URL(urlDownload);
        URLConnection urlConn = url.openConnection();
        urlConn.setConnectTimeout(10000);
        urlConn.setReadTimeout(10000);
        urlConn.setAllowUserInteraction(false);
        urlConn.setDoOutput(true);
        InputStream inStream = urlConn.getInputStream();
        return IOUtils.toByteArray(inStream);
    }

    public static byte[] gerarArquivoXmlNotas(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);

        try (ReadableByteChannel channel = Channels.newChannel(url.openStream())) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = channel.read(ByteBuffer.wrap(buffer))) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return outputStream.toByteArray();
        }
    }

    public void realizarEnvioOperacoesServicoNota() {
        try {
            limparMsg();
            setOnComplete("");

            String retorno = getFacade().getNotaFiscalOperacao().realizarEnvioOperacoesServicoNota();
            if (retorno.toUpperCase().contains("ERRO")) {
                throw new Exception(retorno);
            }

            montarSucessoGrowl(retorno);
            setOnComplete("Richfaces.hideModalPanel('modalInutilizarNFCE')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void atualizarNotaNaLista(NotaFiscalVO notaAtualizar, boolean consultar) {
        if (consultar) {
            try {
                notaAtualizar = getFacade().getNotaFiscal().consultarPorChavePrimaria(notaAtualizar.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception ignored) {
            }
        }

        NotaFiscalVO notaAnterior = null;
        for (NotaFiscalVO obj : getListaNotas()) {
            if (notaAtualizar.getCodigo().equals(obj.getCodigo())) {
                notaAnterior = obj;
                break;
            }
        }

        if (notaAnterior != null) {
            int posicao = getListaNotas().indexOf(notaAnterior);
            getListaNotas().remove(notaAnterior);
            getListaNotas().add(posicao, notaAtualizar);
        }
    }

    public String getPeriodoDtAutorizacao() {
        if (periodoDtAutorizacao == null) {
            periodoDtAutorizacao = "";
        }
        return periodoDtAutorizacao;
    }

    public void setPeriodoDtAutorizacao(String periodoDtAutorizacao) {
        this.periodoDtAutorizacao = periodoDtAutorizacao;
    }

    public JSONObject getJsonEnvio() {
        return jsonEnvio;
    }

    public void setJsonEnvio(JSONObject jsonEnvio) {
        this.jsonEnvio = jsonEnvio;
    }

    public void visualizarJsonEnvio() {
        try {
            limparMsg();
            setOnComplete("");
            NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
            JSONObject json = new JSONObject(obj.getJsonEnvio());
            JSONObject jsonNota;
            try {
                jsonNota = json.getJSONObject("jsonNota");
            } catch (Exception ex) {
                jsonNota = new JSONObject(json.getString("jsonNota"));
            }
            setJsonEnvio(jsonNota);
            setOnComplete("Richfaces.showModalPanel('modalJsonEnvio')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String getAtributosExcel() {
        return "codigo=Cod.,tipo.descricao=Tipo,serie=Série,rps=RPS,numeroNota=NR. Nota,cpfCnpjApresentar=CPF/CNPJ," +
                "razaoSocial=Razão Social,nomeCliente=Nome Cliente,statusNotaApresentar=Status,statusNotaHint=Status - Hint,valor=Valor," +
                "dataEmissaoApresentar=Dt. Emissão,dataAutorizacaoApresentar=Dt. Autorização,linkPDF=PDF,linkXML=XML,contrato=Contrato,matricula=Matricula,dataCompetenciaApresentar=Dt. Competência";
    }

    public String getPeriodoDtEmissao() {
        if (periodoDtEmissao == null) {
            periodoDtEmissao = "";
        }
        return periodoDtEmissao;
    }

    public void setPeriodoDtEmissao(String periodoDtEmissao) {
        this.periodoDtEmissao = periodoDtEmissao;
    }

    public boolean isApresentarInutilizarMarcadas() {
        return apresentarInutilizarMarcadas;
    }

    public void setApresentarInutilizarMarcadas(boolean apresentarInutilizarMarcadas) {
        this.apresentarInutilizarMarcadas = apresentarInutilizarMarcadas;
    }

    public boolean isApresentarCancelarMarcadas() {
        return apresentarCancelarMarcadas;
    }

    public void setApresentarCancelarMarcadas(boolean apresentarCancelarMarcadas) {
        this.apresentarCancelarMarcadas = apresentarCancelarMarcadas;
    }

    public boolean isApresentarRPS() {
        return apresentarRPS;
    }

    public void setApresentarRPS(boolean apresentarRPS) {
        this.apresentarRPS = apresentarRPS;
    }

    public NotaFiscalVO getNotaFiscalVOReenviar() {
        return notaFiscalVOReenviar;
    }

    public void setNotaFiscalVOReenviar(NotaFiscalVO notaFiscalVOReenviar) {
        this.notaFiscalVOReenviar = notaFiscalVOReenviar;
    }

    public Date getDataEmissaoReenvio() {
        return dataEmissaoReenvio;
    }

    public void setDataEmissaoReenvio(Date dataEmissaoReenvio) {
        this.dataEmissaoReenvio = dataEmissaoReenvio;
    }

    public List<NotaFiscalOperacaoVO> getFaixasInutilizadas() {
        if (faixasInutilizadas == null) {
            faixasInutilizadas = new ArrayList<>();
        }
        return faixasInutilizadas;
    }

    public void setFaixasInutilizadas(List<NotaFiscalOperacaoVO> faixasInutilizadas) {
        this.faixasInutilizadas = faixasInutilizadas;
    }

    public String getIdEmpresaEnotas() {
        if (idEmpresaEnotas == null) {
            idEmpresaEnotas = "";
        }
        return idEmpresaEnotas;
    }

    public void setIdEmpresaEnotas(String idEmpresaEnotas) {
        this.idEmpresaEnotas = idEmpresaEnotas;
    }

    public Integer getCodigoConfigNota() {
        if(codigoConfigNota == null) {
            codigoConfigNota = 0;
        }
        return codigoConfigNota;
    }

    public void setCodigoConfigNota(Integer codigoConfigNota) {
        this.codigoConfigNota = codigoConfigNota;
    }

    public boolean isApresentarInutilizarPorFaixa() {
        return apresentarInutilizarPorFaixa;
    }

    public void setApresentarInutilizarPorFaixa(boolean apresentarInutilizarPorFaixa) {
        this.apresentarInutilizarPorFaixa = apresentarInutilizarPorFaixa;
    }

    public Integer getNumInicialInutilizar() {
        return numInicialInutilizar;
    }

    public void setNumInicialInutilizar(Integer numInicialInutilizar) {
        this.numInicialInutilizar = numInicialInutilizar;
    }

    public Integer getNumFinalInutilizar() {
        return numFinalInutilizar;
    }

    public void setNumFinalInutilizar(Integer numFinalInutilizar) {
        this.numFinalInutilizar = numFinalInutilizar;
    }

    public List<InutilizacaoNotaFiscalTO> getInutilizacaoNotaFiscalTOList() {
        return inutilizacaoNotaFiscalTOList;
    }

    public void setInutilizacaoNotaFiscalTOList(List<InutilizacaoNotaFiscalTO> inutilizacaoNotaFiscalTOList) {
        this.inutilizacaoNotaFiscalTOList = inutilizacaoNotaFiscalTOList;
    }

    public void selecionarNotaReenviar() {
        NotaFiscalVO obj = (NotaFiscalVO) context().getExternalContext().getRequestMap().get("nota");
        setNotaFiscalVOReenviar(obj);
        setDataEmissaoReenvio(obj.getDataEmissao());
        setOnComplete("Richfaces.showModalPanel('modalReenviar')");
    }

    public void reenviarNotaFiscal() {
        try {
            limparMsg();
            setOnComplete("");

            getFacade().getNotaFiscal().reenviarNotaNFSe(getNotaFiscalVOReenviar(), getKey(), getDataEmissaoReenvio(), getUsuarioLogado());
            atualizarNotaNaLista(getNotaFiscalVOReenviar(), true);
            montarSucessoGrowl("Nota Reenviada");
            setOnComplete("Richfaces.hideModalPanel('modalReenviar')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void sincronizarTodasNotasComEnotas() {
        try {
            limparMsg();
            setOnComplete("");

            List<NotaFiscalVO> listaNotasAtualizar = getFacade().getNotaFiscal().consultarNotasFiscaisSincronizar(this.getFiltro());

            if (UteisValidacao.emptyList(listaNotasAtualizar)) {
                throw new Exception("Nenhuma nota para ser sincronizada.");
            }

            for (NotaFiscalVO notaFiscalVO : listaNotasAtualizar) {
                try {
                    if(!notaFiscalVO.getStatusNota().equals(StatusNotaEnum.AUTORIZADO.getDescricao())){
                        NotaEnotasTO notaEnotasTO = getFacade().getNotaFiscal().consultarNotaEnotas(notaFiscalVO);
                        if (notaEnotasTO != null && !notaEnotasTO.getStatus().equalsIgnoreCase(notaFiscalVO.getStatusNota())) {
                            prepararStatusInutilizacao(notaFiscalVO, notaEnotasTO);
                            getFacade().getNotaFiscal().atualizarDadosNotaFiscalEnotas(notaFiscalVO, notaEnotasTO, getUsuarioLogado());
                        }
                    }
                } catch (Exception ignored) {
                }
            }

            montarSucessoGrowl("Notas sincronizadas, por favor consulte novamente.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public ConfiguracaoNotaFiscalVO buscaConfigNotaPorCodigo(Integer codigoConfigNota) {
        ConfiguracaoNotaFiscalVO cfn = new ConfiguracaoNotaFiscalVO();
        for (ConfiguracaoNotaFiscalVO obj : getListaConfiguracaoNotaFiscal()) {
            if(obj.getCodigo().equals(codigoConfigNota)) {
                cfn = obj;
                break;
            }
        }
        return cfn;
    }

    public String getDataCertificado_Apresentar() {
        String dataCertificado = buscaConfigNotaPorCodigo(getCodigoConfigNota()).getDataCertificado_Apresentar();
        if(!UteisValidacao.emptyString(dataCertificado)) {
            return "Validade Certificado: " + dataCertificado;
        }
        return "";
    }

    public InutilizacaoNotaFiscalTO getInutilizacaoNotaFiscalTO() {
        return inutilizacaoNotaFiscalTO;
    }

    public void setInutilizacaoNotaFiscalTO(InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO) {
        this.inutilizacaoNotaFiscalTO = inutilizacaoNotaFiscalTO;
    }

    public boolean isApresentarDadosInutilizacao(){
        return getInutilizacaoNotaFiscalTO() == null ? false : true;
    }

    public ListaPaginadaTO getListaNotasPaginada() {
        if (listaNotasPaginada == null) {
            listaNotasPaginada = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        }
        return listaNotasPaginada;
    }

    public void setListaNotasPaginada(ListaPaginadaTO listaNotasPaginada) {
        this.listaNotasPaginada = listaNotasPaginada;
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        ListaPaginadaTO paginacao = this.getListaNotasPaginada();
        paginacao.proximaPagina();
        consultarListaNotas(paginacao);
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        ListaPaginadaTO paginacao = this.getListaNotasPaginada();
        paginacao.paginaAnterior();
        consultarListaNotas(paginacao);
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        ListaPaginadaTO paginacao = this.getListaNotasPaginada();
        paginacao.ultimaPagina();
        consultarListaNotas(paginacao);
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        ListaPaginadaTO paginacao = this.getListaNotasPaginada();
        paginacao.primeiraPagina();
        consultarListaNotas(paginacao);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        ListaPaginadaTO paginacao = this.getListaNotasPaginada();
        paginacao.setOffset(0);
        consultarListaNotas(paginacao);
    }

    private void consultarListaNotas(ListaPaginadaTO paginacao) throws Exception {
        try {
            listaNotas = getFacade().getNotaFiscal().consultarNotasFiscais(getFiltro(), paginacao.getLimit(), paginacao.getOffset());
            preencherNotasSelecionadas();
        } catch (Exception ex) {
            montarErro(ex);
            throw ex;
        }
    }

    public Set<Integer> getNotasSelecionadas() {
        if (notasSelecionadas == null) {
            notasSelecionadas = new HashSet<>();
        }
        return notasSelecionadas;
    }

    public void setNotasSelecionadas(Set<Integer> notasSelecionadas) {
        this.notasSelecionadas = notasSelecionadas;
    }

    public void limparNotasSelecionadas() {
        this.setNotasSelecionadas(new HashSet<>());
        this.setMarcarTodos(false);
        preencherNotasSelecionadas();
    }

    private void preencherNotasSelecionadas() {
        for (NotaFiscalVO nota : getListaNotas()) {
            nota.setSelecionado(getNotasSelecionadas().contains(nota.getCodigo()));
        }
    }

    public void selecionarNotaCheckBox(ActionEvent evt) throws Exception {
        Integer codigo = (Integer) evt.getComponent().getAttributes().get("codNota");
        if (this.getNotasSelecionadas().contains(codigo)) {
            this.getNotasSelecionadas().remove(codigo);
        } else {
            this.getNotasSelecionadas().add(codigo);
        }
        this.setMarcarTodos(this.getListaNotasPaginada().getCount().equals(this.getNotasSelecionadas().size()));
    }

    public Set<Integer> getNotasGeral() {
        if (notasGeral == null) {
            notasGeral = new HashSet<>();
        }
        return notasGeral;
    }

    public void setNotasGeral(Set<Integer> notasGeral) {
        this.notasGeral = notasGeral;
    }

    public void exportarExcel(ActionEvent evt) {
        try {
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            List listaParaImpressao = getFacade().getNotaFiscal().consultarNotasFiscais(this.getFiltro(), this.getNotasGeral(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public List<SolicitacaoVO> getListaSolicitacaoVOS() {
        if(UteisValidacao.emptyList(listaSolicitacaoVOS)){
            listaSolicitacaoVOS = new ArrayList<>();
        }
        return listaSolicitacaoVOS;
    }

    public void setListaSolicitacaoVOS(List<SolicitacaoVO> listaSolicitacaoVOS) {
        this.listaSolicitacaoVOS = listaSolicitacaoVOS;
    }

    public void exibirSolicitacoes() {
        setOnComplete("");
        limparMsg();
        try {
           listaSolicitacaoVOS = getFacade().getSolicitacao().consultarListaSolicitacoes(null,null,null, null, null,Uteis.NIVELMONTARDADOS_TODOS, getFiltroEmpresa());
            setOnComplete("Richfaces.showModalPanel('modalSolicitacoes');");
        } catch (Exception ex) {
            Logger.getLogger(NotaFiscalControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        }

    }

    public String enviarSolicitacao(SolicitacaoVO solicitacaoVO) throws Exception {
        KeyCodFormJson keyCod = new KeyCodFormJson(getKey(),solicitacaoVO.getCodigo(),solicitacaoVO.getEmpresa().getCodigo());
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + "solicitacao/adicionarSolicitacao";
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Authorization", getKey());
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL Donwload Nota: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse enviar solicitacao: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has("content") && !UteisValidacao.emptyString(json.optString("content"))) {
            return json.optString("content");
        } else {
            throw new Exception(responseBody);
        }
    }

    public List<NotaFiscalVO> getListaNotasAlterarStatus() {
        if(listaNotasAlterarStatus == null){
            listaNotasAlterarStatus = new ArrayList<>();
        }
        return listaNotasAlterarStatus;
    }

    public void setListaNotasAlterarStatus(List<NotaFiscalVO> listaNotasAlterarStatus) {
        this.listaNotasAlterarStatus = listaNotasAlterarStatus;
    }

    public String getMotivoAlteracaoStatus() {
        if(motivoAlteracaoStatus == null){
            motivoAlteracaoStatus = "";
        }
        return motivoAlteracaoStatus;
    }

    public void setMotivoAlteracaoStatus(String motivoAlteracaoStatus) {
        this.motivoAlteracaoStatus = motivoAlteracaoStatus;
    }

    public String getStatusAlteracaoNotaFiscal() {
        return statusAlteracaoNotaFiscal;
    }

    public void setStatusAlteracaoNotaFiscal(String statusAlteracaoNotaFiscal) {
        this.statusAlteracaoNotaFiscal = statusAlteracaoNotaFiscal;
    }

    public void prepararAlterarStatusNotasSelecionadas() {
        try {
            limparMsg();
            setOnComplete("");
            setMotivoAlteracaoStatus("");
            setListaNotasCancelar(new ArrayList<>());
            setListaNotasInutilizar(new ArrayList<>());
            setListaNotasIgnoradas(new ArrayList<>());
            setListaNotasAlterarStatus(new ArrayList<>());

            getListaNotasAlterarStatus().addAll(obterListaGeralNotasSelecionadas());

            if (UteisValidacao.emptyList( getListaNotasAlterarStatus())) {
                throw new Exception("Nenhuma nota foi selecionada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalAlterarStatusNotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public List<SelectItem> getListaStatusAlterarNotaFiscal() {
        return StatusAlteracaoNotaFiscalEnum.getListaCombo();
    }

    public void alterarStatusNotasSelecionadas() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyString(getMotivoAlteracaoStatus())) {
                throw new Exception("Informe o motivo da alteração do status");
            }
            getListaNotasAlterarStatus().stream().forEach(notaFiscalVO -> atualizarStatusNotaManualmente(notaFiscalVO));
            montarSucessoGrowl("Status alterado com sucesso, por favor consulte novamente.");
            setOnComplete("Richfaces.hideModalPanel('modalAlterarStatusNotas')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void atualizarStatusNotaManualmente(NotaFiscalVO notaFiscalVO) {
        try {
            getFacade().getNotaFiscal().atualizarStatusNotaManualmente(StatusEnotasEnum.obterPorDescricaoEnotas(getStatusAlteracaoNotaFiscal()), notaFiscalVO,getMotivoAlteracaoStatus());
        } catch (Exception ex) {
            montarErro(ex);;
        }
    }

}

package controle.pix;


import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.Pix;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PixRelatorioController extends SuperControle {

    private List<PixVO> pixVOS;
    private Date dataInicial;
    private Date dataFinal;
    private Pix pix;
    private String mensagemResultado;

    private PixVO pixVOSelecionado;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private boolean exibirModalParametros = false;
    private boolean exibirPix = false;

    public void consultarTransacoesPix(){
        try {
            pixVOS = getPix().consultarPorPeriodo(dataInicial, dataFinal);
            if(pixVOS.size() == 0){
                mensagemResultado = "Não existem transações pix no período informado.";
            }else{
                mensagemResultado = null;
            }
        }catch (Exception e){
            Uteis.logar("Falha ao consultar relatório do pix");
            e.printStackTrace();
        }
    }

    public Pix getPix() throws Exception {
        if(pix == null){
            pix =  new Pix();
        }
        return pix;
    }

    public void setPix(Pix pix) {
        this.pix = pix;
    }

    public List<PixVO> getPixVOS() {
        return pixVOS;
    }

    public void setPixVOS(List<PixVO> pixVOS) {
        this.pixVOS = pixVOS;
    }

    public Date getDataInicial() {
        if(dataInicial == null){
            setDataInicial(Calendario.primeiroDiaMes());
        }
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial =  Calendario.primeiraHoraDia(dataInicial);
    }

    public Date getDataFinal() {
        if( dataFinal == null){
            setDataFinal(Calendario.ultimoDiaMes());
        }
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = Calendario.ultimaHoraDia(dataFinal);
    }

    public String getMensagemResultado() {
        return mensagemResultado;
    }

    public void setMensagemResultado(String mensagemResultado) {
        this.mensagemResultado = mensagemResultado;
    }

    public PixVO getPixVOSelecionado() {
        if (pixVOSelecionado == null) {
            pixVOSelecionado = new PixVO();
        }
        return pixVOSelecionado;
    }

    public void setPixVOSelecionado(PixVO pixVOSelecionado) {
        this.pixVOSelecionado = pixVOSelecionado;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }
    public void fecharPanelDadosParametros() {
        this.setExibirModalParametros(false);
        this.setExibirPix(false);
    }

    public boolean isExibirPix() {
        return exibirPix;
    }

    public void setExibirPix(boolean exibirPix) {
        this.exibirPix = exibirPix;
    }

    public void exibirParams(ActionEvent evt) {
        String params = (String) evt.getComponent().getAttributes().get("params");
        PixVO pix = (PixVO) evt.getComponent().getAttributes().get("pix");
        pixVOSelecionado = null;
        listaParametrosSelecionado = null;
        if (params != null && pix != null) {
            pixVOSelecionado = pix;
            setExibirModalParametros(true);
            setMensagemDetalhada("");
            setMensagem("");
            if (params.equals("envio")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(pix.getParamsEnvio());
                } catch (Exception e) {
                    setMensagem("Erro ao obter detalhes do envio!");
                    setMensagemDetalhada(e.getMessage());
                }
            } else if (params.equals("resposta")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(pix.getParamsResposta());
                }catch (Exception e) {
                    setMensagem("Erro ao obter resposta da administradora, necessário reenviar esta transação!");
                    setMensagemDetalhada(e.getMessage());
                }
            }
        }
    }
}

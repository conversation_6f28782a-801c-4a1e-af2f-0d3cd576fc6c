package controle.pix;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Pix;
import org.json.JSONObject;

import javax.faces.context.FacesContext;
import java.sql.Connection;

public class PixController {

    private PixVO pixVO;
    private EmpresaVO empresaVO;
    private boolean tudoCerto;

    public PixController() {
        try {
            String token = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("tk");
            String codigoPix = "";
            String chave = "";
            if (!UteisValidacao.emptyString(token)) {
                JSONObject json = new JSONObject(Uteis.desencriptar(token, "TKP4cT0PiX"));
                chave = json.optString("chave");
                codigoPix = String.valueOf(json.optInt("pix"));
            } else {
                //antigos
                codigoPix = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("codigo");
                chave = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("chave");
            }

            Connection con = null;
            try {
                con = new DAO().obterConexaoEspecifica(chave);
                init(Integer.parseInt(codigoPix), con, chave);
            } finally {
                if (con != null) {
                    con.close();
                }
            }
            tudoCerto = true;
        } catch (Exception e) {
            tudoCerto = false;
            e.printStackTrace();
        }
    }

    private void init(Integer codigo, Connection con, String chave) throws Exception {
        initPixVO(codigo, con, chave);
        initEmpresaVO(pixVO.getEmpresa(), con);
    }

    private void initPixVO(Integer codigo, Connection con, String chave) throws Exception {
        Pix pixDAO = new Pix(con);
        pixVO = pixDAO.consultarPorCodigo(codigo, false);
        pixVO.setChaveBanco(chave);
        pixDAO = null;
    }

    private void initEmpresaVO(Integer codigoEmpresa, Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        empresaVO = empresaDAO.consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;
    }

    public PixVO getPixVO() {
        return pixVO;
    }

    public void setPixVO(PixVO pixVO) {
        this.pixVO = pixVO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isTudoCerto() {
        return tudoCerto;
    }

    public void setTudoCerto(boolean tudoCerto) {
        this.tudoCerto = tudoCerto;
    }

}

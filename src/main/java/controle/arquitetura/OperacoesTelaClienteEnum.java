package controle.arquitetura;

import br.com.pactosolucoes.estudio.controle.CancelamentoSessaoControle;
import controle.contrato.AfastamentoContratoControle;
import controle.contrato.AlterarHorarioContratoControle;
import controle.contrato.BonusContratoControle;
import controle.contrato.EstornoContratoControle;
import controle.contrato.ManutencaoModalidadeControle;
import controle.contrato.RetornoAtestadoContratoControle;
import controle.contrato.RetornoCarenciaContratoControle;
import controle.contrato.RetornoTrancamentoContratoControle;
import controle.financeiro.EdicaoPagamentoControle;
import controle.financeiro.EstornoMovProdutoControle;
import controle.financeiro.EstornoReciboControle;
import controle.plano.ConsultarTurmaControle;
import controle.plano.OrcamentoControle;

public enum OperacoesTelaClienteEnum {

    AFASTAMENTO("afastamentoContratoForm.jsp", AfastamentoContratoControle.class, "novo", true),
    ESTORNO("estornoContratoForm.jsp", EstornoContratoControle.class, "novo"),
    ALTERAR_HORARIO("alterarHorarioContrato.jsp", AlterarHorarioContratoControle.class, "novo"),
    BONUS("bonusForm.jsp", BonusContratoControle.class, "novo"),
    MANUTENCAO_MODALIDADE("manutencaoModalidade.jsp", ManutencaoModalidadeControle.class, "novo"),
    CONSULTA_RECIBO("estornoReciboForm.jsp", EstornoReciboControle.class),
    EDICAO_FORMA_PAGAMENTO("pages/finan/edicaoPagamento.jsp", EdicaoPagamentoControle.class),
    NOVO_CONTRATO_VENDA_RAPIDA("inclusaoAlunoVenda.jsp?cliente="),
    NOVO_CONTRATO("tela4.jsp"),
    REMATRICULAR_RENOVAR_CONTRATO("negociacaoContrato.jsp"),
    RETORNO_ATESTADO("retornoAtestadoContrato.jsp", RetornoAtestadoContratoControle.class, "novo"),
    RETORNO_TRANCAMENTO("retornoTrancamentoContratoForm.jsp", RetornoTrancamentoContratoControle.class, "novo"),
    RETORNO_FERIAS("retornoCarenciaContrato.jsp", RetornoCarenciaContratoControle.class, "novo"),
    LISTA_ALUNO_TURMA("listaAlunosTurma.jsp", ConsultarTurmaControle.class),
    ESTORNAR_PRODUTO("estornoMovProdutoForm.jsp", EstornoMovProdutoControle.class),
    CANCELAR_SESSAO("cancelamentoSessaoForm.jsp", CancelamentoSessaoControle.class),
    REALIZAR_ORCAMENTO("realizarOrcamento.jsp", OrcamentoControle.class)
    ;

    private String url;
    private Class<?> controle;
    private String method;
    private Boolean adicionarCodigoContratoUrl;
    private Boolean adicionarMatriculaUrl;
    private Boolean adicionarCodigoClienteUrl;

    OperacoesTelaClienteEnum(String url) {
        this(url, null);
    }

    OperacoesTelaClienteEnum(String url, Class<?> controle) {
        this(url, controle, null);
    }
    OperacoesTelaClienteEnum(String url, Class<?> controle, String method) {
        this(url, controle, method, false);
    }

    OperacoesTelaClienteEnum(String url, Class<?> controle, String method, Boolean adicionarCodigoContratoUrl) {
        this(url, controle, method, adicionarCodigoContratoUrl, false);
    }

    OperacoesTelaClienteEnum(String url, Class<?> controle, String method, Boolean adicionarCodigoContratoUrl, Boolean adicionarMatriculaUrl) {
        this(url, controle, method, adicionarCodigoContratoUrl, adicionarMatriculaUrl, false);
    }

    OperacoesTelaClienteEnum(String url, Class<?> controle, String method, Boolean adicionarCodigoContratoUrl, Boolean adicionarMatriculaUrl, Boolean adicionarCodigoClienteUrl) {
        this.url = url;
        this.controle = controle;
        this.method = method;
        this.adicionarCodigoContratoUrl = adicionarCodigoContratoUrl;
        this.adicionarMatriculaUrl = adicionarMatriculaUrl;
        this.adicionarCodigoClienteUrl = adicionarCodigoClienteUrl;
    }

    public String getUrl() {
        return url;
    }

    public Class<?> getControle() {
        return controle;
    }

    public String getMethod() {
        return method;
    }

    public Boolean getAdicionarCodigoContratoUrl() {
        return adicionarCodigoContratoUrl;
    }

    public Boolean getAdicionarMatriculaUrl() {
        return adicionarMatriculaUrl;
    }

    public Boolean getAdicionarCodigoClienteUrl() {
        return adicionarCodigoClienteUrl;
    }
}

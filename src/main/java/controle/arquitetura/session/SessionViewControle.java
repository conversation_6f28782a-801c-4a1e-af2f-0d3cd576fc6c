/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.session;

import controle.arquitetura.SuperControle;
import controle.arquitetura.session.listener.SessionState;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.event.ActionEvent;
import negocio.comuns.financeiro.ObjetoGenerico;
import servicos.util.ExecuteRequestHttpService;

/**
 *
 * <AUTHOR>
 */
public class SessionViewControle extends SuperControle {

    private static final long serialVersionUID = 7941342247579782118L;
    private List<SessionTO> sessions;
    private boolean updateAuto = false;

    public List<SessionTO> getSessions() {
        return sessions;
    }

    public void setSessions(List<SessionTO> sessions) {
        this.sessions = sessions;
    }

    public boolean isUpdateAuto() {
        return updateAuto;
    }

    public void setUpdateAuto(boolean updateAuto) {
        this.updateAuto = updateAuto;
    }

    public void atualizar() {
        sessions = SessionState.updateList();
        super.setListaManipulavel((ArrayList) sessions);
    }

    public void expandirTudo() {
        for (SessionTO sessionTO : sessions) {
            sessionTO.setExpandido(true);
        }
    }

    public void retrairTudo() {
        for (SessionTO sessionTO : sessions) {
            sessionTO.setExpandido(false);
        }
    }

    public void actionSession(ActionEvent evt) {
        SessionTO to = (SessionTO) evt.getComponent().getAttributes().get("obj");
        String op = (String) evt.getComponent().getAttributes().get("op");
        if (op != null) {
            if (op.equals("exp")) {
                to.setExpandido(!to.isExpandido());
            }
        }
    }

    public void actionAttributes(ActionEvent evt) {
        SessionTO to = (SessionTO) evt.getComponent().getAttributes().get("obj");
        ObjetoGenerico o = (ObjetoGenerico) evt.getComponent().getAttributes().get("objGen");
        String op = (String) evt.getComponent().getAttributes().get("op");
        if (op != null) {
            if (op.equals("del")) {
                to.getSession().removeAttribute(o.getAtributo());
                atualizar();
            }
        }
    }

    public void atualizarAutomaticamente(ActionEvent evt) {
        Boolean b = Boolean.valueOf(evt.getComponent().getAttributes().get("valor").toString());
        setUpdateAuto(!b);
    }

    public void consultarWhois(ActionEvent evt) {
        SessionTO s = (SessionTO) evt.getComponent().getAttributes().get("session");
        if (s != null && s.getWhois().isEmpty()) {
            try {
                String aux = ExecuteRequestHttpService.executeRequest("https://registro.br/cgi-bin/whois/?qr=" + s.getIp() + "&#lresp", null);
                if (!aux.isEmpty()) {
                    s.setWhois(aux.substring(aux.indexOf("Copyright (c) Nic.br"), aux.indexOf("whois.registro.br aceita somente consultas diretas.")));
                    s.setWhois("<pre>" + s.getWhois() + "</pre>");
                }
            } catch (IOException ex) {
                Logger.getLogger(SessionState.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (s != null && !s.getWhois().isEmpty()) {
            s.setWhois("");
        }
    }

    public void invalidateAllIdles() {
        for (SessionTO sess : sessions) {
            if ((sess.getEmpresa() == null || sess.getEmpresa().isEmpty())
                    && (sess.getUsuario() == null || sess.getUsuario().isEmpty())) {
                sess.getSession().invalidate();
            }
        }
        atualizar();
    }

    public void invalidate(ActionEvent evt) {
        SessionTO to = (SessionTO) evt.getComponent().getAttributes().get("obj");
        to.getSession().invalidate();
        atualizar();
    }

    public void invalidateTeste(ActionEvent evt) {
        SessionTO to = (SessionTO) evt.getComponent().getAttributes().get("obj");
        to.getSession().removeAttribute("ReposicaoControle");
        to.getSession().removeAttribute("AutorizacaoFuncionalidadeControle");
        to.getSession().removeAttribute("ComboBoxEmpresaControle");
        to.getSession().removeAttribute("DataScrollerControle");
        to.getSession().removeAttribute("GestaoRemessasControle");
        to.getSession().removeAttribute("GestaoTransacoesControle");
        to.getSession().removeAttribute("HistoricoVinculoControle");
        to.getSession().removeAttribute("InicioControle");
        to.getSession().removeAttribute("LogoutControle");
//        to.getSession().removeAttribute("MovPagamentoControle");
//        to.getSession().removeAttribute("MovProdutoControle");

//        to.getSession().removeAttribute("PagamentoCartaoCreditoControle");
//        to.getSession().removeAttribute("SkinControle");
//        to.getSession().removeAttribute("SocialMailingControle");
//        to.getSession().removeAttribute("SuperControle");
//        to.getSession().removeAttribute("SuporteControle");
//        to.getSession().removeAttribute("TrocarCartaoRecorrenciaControle");
//        to.getSession().removeAttribute("RecuperacaoSenhaControle");
//        to.getSession().removeAttribute("CaptchaControle");

        atualizar();
    }
}

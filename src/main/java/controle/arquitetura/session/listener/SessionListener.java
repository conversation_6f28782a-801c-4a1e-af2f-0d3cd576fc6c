/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.session.listener;

import controle.arquitetura.session.SessionTO;
import negocio.facade.jdbc.utilitarias.ConnectionSerializable;

import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;
import java.sql.SQLException;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class SessionListener implements HttpSessionListener {

    @Override
    public void sessionCreated(HttpSessionEvent se) {
        Date agora = new Date();
        System.out.println(String.format("Sessão %1$s criada em %2$s (%3$s)", new Object[]{
                se.getSession().getId(),
                agora,
                se.getSession().getServletContext().getContextPath()
        }));

        SessionState.getInstance().put(se.getSession().getId(), new SessionTO(se.getSession().getId(), agora, se.getSession()));
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent se) {
        ConnectionSerializable c = (ConnectionSerializable) se.getSession().getAttribute("con");
        try {
            if (c != null && c.getCon() != null && (!c.getCon().isClosed())) {
                c.getCon().close();
            }
        } catch (SQLException ex) {
            Logger.getLogger(SessionListener.class.getName()).log(Level.SEVERE, null, ex);
        }
        SessionState.getInstance().remove(se.getSession().getId());
        System.out.println(String.format("Sessão %1$s destruída em %2$s", new Object[]{se.getSession().getId(), new Date()}));
    }
}

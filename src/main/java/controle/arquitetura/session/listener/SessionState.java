/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.session.listener;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.session.SessionTO;
import java.sql.Connection;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.ConnectionSerializable;
import org.postgresql.jdbc.PgConnection;

/**
 *
 * <AUTHOR>
 */
public class SessionState {

    public static final String LOGADO = "logado";
    public static final String IP = "ip";
    public static final String CON = "con";
    public static final String LOGIN_CONTROLE = "LoginControle";
    public static final String BROWSER = "browser";
     public static final String SCREEN_H = "screenH";
    public static final String SCREEN_W = "screenW";
    public static final String ATRIBUTO = "atributo";
    private static Map<String, SessionTO> sessionState = new ConcurrentHashMap<>();

    public static Map<String, SessionTO> getInstance() {
        return SessionState.sessionState;
    }

    protected static List<ObjetoGenerico> getObjectsFromSession(final String id,
            Map<String, SessionTO> mapa) {
        SessionTO to = mapa.get(id);
        List<ObjetoGenerico> lista = new ArrayList<ObjetoGenerico>();
        try {
            final Enumeration enums = to.getSession().getAttributeNames();
            while (enums.hasMoreElements()) {
                final String name = enums.nextElement().toString();
                final Object v = to.getSession().getAttribute(name);
                if (name.equals(LOGADO)) {
                    to.setUsuario(v.toString());
                }
                if (name.equals(LOGIN_CONTROLE)) {
                    EmpresaVO emp = ((LoginControle) v).getEmpresa();
                    if (emp != null && !UteisValidacao.emptyNumber(emp.getCodigo())) {
                        to.setEmpresa(emp.getNome());
                        to.setCodigoEmpresa(emp.getCodigo());
                        to.setCnpj(emp.getCNPJ());
                        to.setUf(emp.getEstado().getSigla());
                    }
                }
                if (name.equals(JSFUtilities.KEY)) {
                    to.setChave((String) v);
                }
                if (name.equals(IP)) {
                    to.setIp((String) v);
                }
                if (name.equals(BROWSER)) {
                    to.setBrowser(v.toString());
                }
                if (name.equals(SCREEN_H)) {
                    to.setScreenH(v.toString());
                }
                if (name.equals(SCREEN_W)) {
                    to.setScreenW(v.toString());
                }
                if (name.equals(CON)) {
                    try {
                        Connection con = ((ConnectionSerializable) v).getCon();
                        PgConnection pgConnection = (PgConnection) con;
                        to.setProcId(pgConnection.getBackendPID());
                    } catch (Exception ex) {
                        to.setProcId(0);
                    }
                }
                lista.add(new ObjetoGenerico(name, v != null ? v.toString() : ""));
            }
            Ordenacao.ordenarLista(lista, ATRIBUTO);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return lista;

    }

    public static List<SessionTO> updateList() {
        try {
            List<SessionTO> lista = new ArrayList<>();
            Map<String, SessionTO> mapSync = new ConcurrentHashMap<>(sessionState);
            Set<String> s = mapSync.keySet();
            for (Iterator<String> it = s.iterator(); it.hasNext();) {
                try {
                    String id = it.next();
                    SessionTO to = mapSync.get(id);
                    to.setObjetos(SessionState.getObjectsFromSession(id, mapSync));
                    lista.add(to);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }
}

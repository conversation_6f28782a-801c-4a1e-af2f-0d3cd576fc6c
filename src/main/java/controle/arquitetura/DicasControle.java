/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.arquitetura;

import java.util.ArrayList;
import java.util.List;

import negocio.comuns.basico.DicasTO;
import negocio.comuns.utilitarias.Uteis;

/**
 * <AUTHOR>
 */
public class DicasControle extends SuperControle {

    private String nomeFuncionalidade = "";
    private String dados = "";
    private List<DicasTO> nomesImagens = new ArrayList<DicasTO>();
    private boolean naoMostrarMais = false;

    public List<DicasTO> getMontarLista() throws Exception {
        String pathInfo = request().getPathInfo();
        pathInfo = pathInfo == null ? request().getRequestURI() : pathInfo;
        nomesImagens = new ArrayList<DicasTO>();
        if (!getExibirDica()) {
            return new ArrayList<DicasTO>();
        }
//        if (pathInfo.contains("telaInicialCRM.jsp")) {
//            montarDicasTelaInicialCRM();
//        }
//        if(pathInfo.contains("tela1.jsp")){
//            montarDicasTelaInicialZW();
//        }
        //if(pathInfo.contains("telaInicialFinan.jsp")){
        //    montarDicasTelaInicialFW();
        //}
        return nomesImagens;
    }


    public void marcarEsconder() {
        try {
            String pathInfo = request().getPathInfo();
            getFacade().getUsuario().marcarNaoAparecerMais(getUsuarioLogado().getCodigo(), pathInfo, naoMostrarMais);

            if (naoMostrarMais) {
                getUsuarioLogado().getDicasEsconder().add(pathInfo);
            } else {
                getUsuarioLogado().getDicasEsconder().remove(pathInfo);
            }

        } catch (Exception e) {
            setMensagemDetalhada(e);
        }

    }

    public void montarDicasTelaInicialCRM() {
        nomesImagens.add(new DicasTO("Novidades no CRM!", "Conheça as evoluções do ZW CRM e como sua nova interface traz uma usabilidade mais simples e limpa para fazer do seu trabalho cada vez mais eficaz. Assista video-aula.", null, null,
                "<object width=\"640\" height=\"351\"><param name=\"allowfullscreen\" value=\"true\" /><param name=\"allowscriptaccess\" value=\"always\" /><param name=\"movie\" value=\"https://vimeo.com/moogaloop.swf?clip_id=157343926&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=0&amp;loop=0\" /><embed src=\"https://vimeo.com/moogaloop.swf?clip_id=157343926&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=0&amp;loop=0\" type=\"application/x-shockwave-flash\" allowfullscreen=\"true\" allowscriptaccess=\"always\" width=\"640\" height=\"351\"></embed></object>"));
    }

    public void montarDicasTelaInicialZW() {
        nomesImagens.add(new DicasTO("Novidades para você!", "Novo Relatório de Comissão. Assista ao vídeo!", null, null,
                "<object width=\"640\" height=\"351\"><param name=\"allowfullscreen\" value=\"true\" /><param name=\"allowscriptaccess\" value=\"always\" /><param name=\"movie\" value=\"https://vimeo.com/moogaloop.swf?clip_id=86498771&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=0&amp;loop=0\" /><embed src=\"https://vimeo.com/moogaloop.swf?clip_id=86498771&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=0&amp;loop=0\" type=\"application/x-shockwave-flash\" allowfullscreen=\"true\" allowscriptaccess=\"always\" width=\"500\" height=\"281\"></embed></object>"));
    }

    public void montarDicasTelaInicialFW() {
        nomesImagens.add(new DicasTO("Novo BI Financeiro!", "Informações de fácil acesso para acompanhar seus resultados. Assista video-aula.", null, null,
                "<object width=\"640\" height=\"351\"><param name=\"allowfullscreen\" value=\"true\" /><param name=\"allowscriptaccess\" value=\"always\" /><param name=\"movie\" value=\"https://vimeo.com/moogaloop.swf?clip_id=80890557&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=0&amp;loop=0\" /><embed src=\"https://vimeo.com/moogaloop.swf?clip_id=80890557&amp;force_embed=1&amp;server=vimeo.com&amp;show_title=1&amp;show_byline=1&amp;show_portrait=1&amp;color=00adef&amp;fullscreen=1&amp;autoplay=0&amp;loop=0\" type=\"application/x-shockwave-flash\" allowfullscreen=\"true\" allowscriptaccess=\"always\" width=\"640\" height=\"351\"></embed></object>"));
    }

    private String nomeUsuario() {
        try {
            return Uteis.obterNomeComApenasPrimeiraLetraMaiuscula(Uteis.getPrimeiroNome(getUsuarioLogado().getNome()));
        } catch (Exception e) {
            return "Usuário";
        }

    }
    public boolean getExibirDicaTutorial() throws Exception {
        try {
            String pathInfo = request().getPathInfo();
            pathInfo = pathInfo == null ? request().getRequestURI() : pathInfo;
            return !getUsuarioLogado().getDicasEsconder().contains(pathInfo);
        } catch (Exception e) {
            return true;
        }
    }
    public boolean getExibirDica() throws Exception {
        try {
            String pathInfo = request().getPathInfo();
            pathInfo = pathInfo == null ? request().getRequestURI() : pathInfo;
//            return !getUsuarioLogado().getDicasEsconder().contains(pathInfo);
            return false;
        } catch (Exception e) {
            return true;
        }
    }


    public String getNomeFuncionalidade() {
        return nomeFuncionalidade;
    }

    public void setNomeFuncionalidade(String nomeFuncionalidade) {
        this.nomeFuncionalidade = nomeFuncionalidade;
    }

    public List<DicasTO> getNomesImagens() {
        return nomesImagens;
    }

    public void setNomesImagens(List<DicasTO> nomesImagens) {
        this.nomesImagens = nomesImagens;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public boolean isNaoMostrarMais() {
        return naoMostrarMais;
    }

    public void setNaoMostrarMais(boolean naoMostrarMais) {
        this.naoMostrarMais = naoMostrarMais;
    }


}

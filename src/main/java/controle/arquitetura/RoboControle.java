package controle.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.RoboTransientObjectsVO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.HistoricoContrato;

/**
 *
 * <AUTHOR>
 */
public class RoboControle extends SuperControle {

    protected RoboVO robo;
    protected RoboVO roboPontuacao;
    private String matriculas;
    private String contratos;
    private Date dataSimulada = Calendario.hoje();
    private boolean apagarHistorioContratoAntes = true;

    public String getContratos() {
        return contratos;
    }

    public void setContratos(String contratos) {
        this.contratos = contratos;
    }

    public String getMatriculas() {
        return matriculas;
    }

    public void setMatriculas(String matriculas) {
        this.matriculas = matriculas;
    }

    public Date getDataSimulada() {
        return dataSimulada;
    }

    public void setDataSimulada(Date dataSimulada) {
        this.dataSimulada = dataSimulada;
    }

    public boolean isApagarHistorioContratoAntes() {
        return apagarHistorioContratoAntes;
    }

    public void setApagarHistorioContratoAntes(boolean apagarHistorioContratoAntes) {
        this.apagarHistorioContratoAntes = apagarHistorioContratoAntes;
    }

    public RoboControle() throws Exception {
        inicializarFacades();
        setRobo(new RoboVO());
    }

    private void inicializarConfiguracaoSistema(RoboVO roboVO) throws Exception {
        roboVO.setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_ROBO));
    }

    private void inicializarUsuarioAdministrador(RoboVO roboVO) throws Exception {
        roboVO.setUsuarioVO(getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
        if (roboVO.getUsuarioVO() != null && context() != null) {
            JSFUtilities.setManagedBeanValue("LoginControle.usuario", getRobo().getUsuarioVO());
        }

    }

    public void inicializarRobo() throws Exception {
        setRobo(new RoboVO());
        if (getRobo().isUsarRobo()) {
            inicializarConfiguracaoSistema(getRobo());
            inicializarUsuarioAdministrador(getRobo());
            getRobo().setListaEmpresa(getFacade().getEmpresa().consultarTodas(null, Uteis.NIVELMONTARDADOS_ROBO));
        }
    }

    public void start(String chave) throws Exception {
        try {
            setRobo(new RoboVO());
            if (getRobo().isUsarRobo()) {
                inicializarConfiguracaoSistema(getRobo());
                inicializarUsuarioAdministrador(getRobo());
                getRobo().setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0,null, false, Uteis.NIVELMONTARDADOS_ROBO));
                getFacade().getRobo().start(getRobo(), Uteis.getUrlOamd(), chave);
            }
            //new Robo().start(robo);
        } catch (Exception e) {
            getRobo().setRotinaProcessada(false);
            getRobo().setTexto("Erro: " + e.getMessage());
            if (!tentarGravarErro()) { }
            throw e;
        }
    }

    public void startPontuacao(String chave) throws Exception {
        try {
            setRoboPontuacao(new RoboVO());
            if (getRoboPontuacao().isUsarRobo()) {
                inicializarConfiguracaoSistema(getRoboPontuacao());
                //inicializarUsuarioAdministrador(getRoboPontuacao());
                getRoboPontuacao().setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0,null, false, Uteis.NIVELMONTARDADOS_ROBO));
                getFacade().getRobo().startPontos(getRoboPontuacao(), Uteis.getUrlOamd(), chave, getDataSimulada());
            }
        } catch (Exception e) {
            getRobo().setRotinaProcessada(false);
            throw e;
        }
    }

    public void startRoboDesatualizado(Date data, String chave) throws Exception {
        try {
            if (getRobo().isUsarRobo()) {
                inicializarUsuarioAdministrador(getRobo());
                inicializarConfiguracaoSistema(getRobo());
                getRobo().setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0,null, false, Uteis.NIVELMONTARDADOS_ROBO));
                getRobo().setDia(data);
                getFacade().getRobo().start(getRobo(), Uteis.getUrlOamd(), chave);
            }
        } catch (Exception e) {
            getRobo().setTexto("Erro: " + e.getMessage());
            if (!tentarGravarErro()) { }
            throw e;
        }
    }

    public boolean tentarGravarErro() {
        try {
            getFacade().getRobo().incluir(robo);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public RoboVO getRobo() {
        return robo;
    }

    public void setRobo(RoboVO robo) {
        this.robo = robo;
    }

    public RoboVO getRoboPontuacao() {
        return roboPontuacao;
    }

    public void setRoboPontuacao(RoboVO roboPontuacao) {
        this.roboPontuacao = roboPontuacao;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public String obterMsgResumo() {
        return String.format("Matrículas: %s e Contratos: %s processados com sucesso. Dia: %s",
                matriculas, contratos, Uteis.getData(dataSimulada));
    }

    public void processarLote() {
        setMensagemDetalhada("");
        setMensagem("");
        String[] vMat = matriculas.isEmpty() ? new String[]{} : matriculas.split(",");
        String[] vCont = contratos.isEmpty() ? new String[]{} : contratos.split(",");

        List<ClienteVO> listaClientes = new ArrayList();
        List<ContratoVO> listaContratos = new ArrayList();

        try {
            for (int i = 0; i < vCont.length; i++) {
                int codContrato = Integer.valueOf(vCont[i]);
                ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(codContrato, Uteis.NIVELMONTARDADOS_ROBO);
                if (contrato != null && contrato.getCodigo().intValue() != 0) {
                    listaContratos.add(contrato);
                }
            }

            for (int i = 0; i < vMat.length; i++) {
                int codMatricula = Integer.valueOf(vMat[i]);
                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoMatricula(codMatricula, 0, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                if (cliente != null && cliente.getCodigo().intValue() != 0) {
                    listaClientes.add(cliente);
                }
            }
            if(JSFUtilities.isJSFContext()){

                RoboTransientObjectsVO rTransVO = new RoboTransientObjectsVO();
                rTransVO.setListaClientes(listaClientes);
                rTransVO.setListaContratos(listaContratos);
                rTransVO.setDia(dataSimulada);
                rTransVO.validar();
                 JSFUtilities.storeOnSession(RoboTransientObjectsVO.class.getSimpleName(), rTransVO);
                 JSFUtilities.setRequestAttribute("dataBase", dataSimulada);
            } else {
                robo.setListaClientes(listaClientes);
                robo.setListaContratos(listaContratos);
                robo.setDia(dataSimulada);
            }
            Date hoje = Calendario.hoje();


            inicializarUsuarioAdministrador(getRobo());
            inicializarConfiguracaoSistema(getRobo());
            getRobo().setListaEmpresa(getFacade().getEmpresa().consultarPorCodigo(0,null,
                    false, Uteis.NIVELMONTARDADOS_ROBO));

            robo.setForcarGeracaoRetroativa(true);

            if (apagarHistorioContratoAntes) {
                for (ContratoVO contratoVO : listaContratos) {
                    HistoricoContrato.executarConsulta("delete from historicocontrato h "
                            + "where contrato = " + contratoVO.getCodigo()
                            + " and tipohistorico not in ('MA', 'RE', 'RN', 'AT', 'RA', 'CA', 'CR', 'RC','RT','TR', 'CT') and not exists (select codigo from historicocontrato  where contrato  = h.contrato and datainiciosituacao > h.datafinalsituacao and tipohistorico in ('AT', 'RA', 'CA', 'CR', 'RC','RT','TR'))",
                            getFacade().getContrato().getCon());
                }
            }

            
            long dias = Uteis.nrDiasEntreDatas(hoje, dataSimulada);
            if (dias > 1) {
                Date data = (Date) hoje.clone();
                robo.setDeveGerarSintetico(false);
                for (int i = 0; i < dias - 1; i++) {
                    robo.setDia(data);
                    getFacade().getRobo().start(robo, Uteis.getUrlOamd(), getKey());
                    data = Calendario.proximo(Calendar.DAY_OF_MONTH, data);
                }
            }

            robo.setDeveGerarSintetico(true);
            robo.setDia(dataSimulada);
            getFacade().getRobo().start(robo, Uteis.getUrlOamd(), getKey());

            setMensagem(obterMsgResumo());
        } catch (Exception ex) {
            setMensagemDetalhada(ex.getMessage());
            Logger.getLogger(RoboControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if(JSFUtilities.isJSFContext()){
                JSFUtilities.removeFromSession(RoboTransientObjectsVO.class);
            }
        }
    }
}

package controle.arquitetura;

import controle.arquitetura.SuperControle;

/**
 * Created by rafaelc on 16/06/2017.
 */
public class MensagemExceptionControle extends SuperControle {
    private Exception exception;
    private String mensagem;
    private boolean show = false;

    public MensagemExceptionControle() {
    }
    public void init(Exception ex){
        this.exception = ex;
        this.show = true;
    }

    public Exception getException() {
        return exception;
    }
    public boolean isShow() {
        return show;
    }
}

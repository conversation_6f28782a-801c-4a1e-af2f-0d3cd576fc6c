package controle.arquitetura;

import br.com.pactosolucoes.enumeradores.Modulo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public enum MenuFuncionalidadeEnum {
    ADM_BI("ADM-BI","/menuRelatorio.jsp", "/indexRelatorio.jsp"),
    ADM_INICIO("ADM-INICIO",
            "/tela1.jsp",
            "/clientes.jsp",
            "/preCadastro.jsp",
            "/menuRelatorio.jsp",
            "/indexRelatorio.jsp",
            "/telaModulo.jsp",
            "/biClubeVantagens.jsp",
            "listasRelatoriosAlunosCancelados.jsp",
            "listasRelatoriosAlunosAtestado.jsp",
            "listasRelatoriosAlunosBonus.jsp",
            "listasRelatoriosAlunosTrancados.jsp",
            "listasRelatorios.jsp",
            "listaClientesDadosBasicos.jsp",
            "gestaoArmario.jsp",
            "gestaoPersonal.jsp",
            "gestaoRemessas.jsp",
            "gestaoBoletosOnline.jsp",
            "gestaoTurma.jsp",
            "gestaoVendasOnline.jsp",
            "detalheBI.jsp",
            "tela8.jsp",
            "diariaForm.jsp",
            "freePassForm.jsp",
            "realizarOrcamento.jsp",
            "vendaAvulsaForm.jsp",
            "inclusaoAlunoVenda.jsp",
            "clubeVantagens.jsp",
            "indicadores.jsp"),
    ADM_CADASTROS("ADM-CADASTROS", "/telaModulo.jsp"),
    ADM_CADASTROS_ACESSO_SISTEMA( "ADM-CADASTROS_ACESSO_SISTEMA", "/indexArquitetura.jsp", "/telaGeradorConsultas.jsp"),
    ADM_CADASTROS_AUXILIARES("ADM-CADASTROS_AUXILIARES", "/indexBasico.jsp"),
    ADM_CADASTROS_CONFIG_CONTRATO("ADM-CADASTROS_CONFIG_CONTRATO", "/indexContrato.jsp"),
    ADM_CADASTROS_CONFIG_FINANCEIRAS("ADM-CADASTROS_CONFIG_FINANCEIRAS", "/indexFinanceiro.jsp"),
    ADM_CADASTROS_CONTROLE_ESTOQUE("ADM-CADASTROS_CONTROLE_ESTOQUE","/indexControleEstoque.jsp"),
    ADM_CADASTROS_PRODUTOS_PLANOS_TURMAS("ADM-CADASTROS_PRODUTOS_PLANOS_TURMAS", "/indexPlano.jsp"),
    ADM_CLUBE_VANTAGENS("ADM-CLUBE_VANTAGENS","/biClubeVantagens.jsp", "/clubeVantagens.jsp", "/indicadores.jsp"),
    CRM_BI("CRM-BI", "/telaBICRM.jsp"),
    CRM_INICIO("CRM-INICIO", "/telaInicialCRM.jsp"),
    EST_INICIO("EST-INICIO",true,  "/pages/estudio/indexEstudio.jsp", "/pages/estudio/includes/include_box_menulateral_sc.jsp"),
    FIN_BI("FIN-BI", "/pages/finan/relatoriosMobile.jsp", "/pages/finan/includes/include_menu_relatorios.jsp"),
    FIN_CADASTROS("FIN-CADASTROS", "/pages/finan/cadastros/telaCadastro.jsp"),
    FIN_CADASTROS_AUXILIARES("FIN-CADASTROS-AUXILIARES", "/pages/finan/cadastros/telaCadastroAuxiliares.jsp"),
    FIN_CONFIG_FINANCEIRAS("FIN-CONFIG-FINANCEIRAS", "/pages/finan/cadastros/telaCadastroConfigFinanceira.jsp", "/pages/finan/cadastros/includes/include_box_menulatcadastrosfinan.jsp"),
    FIN_INICIO("FIN-INICIO", "/pages/finan/telaInicialFinan.jsp",
            "/pages/finan/lancamentoFinanceiroRapidoForm.jsp",
            "/pages/finan/includes/include_box_menulateral.jsp",
            "/pages/finan/relatorios.jsp",
            "/pages/finan/bloqueiocaixa.jsp",
            "/pages/finan/gestaoLotesCons.jsp",
            "/pages/finan/lancamentoFinanceiroRapidoForm.jsp",
            "/pages/finan/telaLancamentosForm.jsp",
            "/pages/finan/gestaoRecebiveis.jsp",
            "/pages/finan/telaCaixa.jsp",
            "/pages/finan/telaLancamentosCons.jsp",
            "/pages/finan/dre.jsp",
            "/pages/finan/demonstrativoFinanceiro.jsp",
            "/pages/finan/fluxoCaixa.jsp",
            "/pages/finan/resumoContas.jsp",
            "/pages/finan/centroCustoCons.jsp",
            "/pages/finan/planoContaCons.jsp",
            "/pages/finan/rateioIntegracaoCons.jsp"),
    NOTAS_INICIO("NOTAS-INICIO", "/notaFiscal.jsp"),
    PESSOAS_INICIO("PESSOAS-INICIO", "/clientes.jsp", "preCadastro.jsp"),
    ;

    private final String name;
    private final String[] uri;
    private boolean flagModuloEstudio = false;

    private MenuFuncionalidadeEnum(String name, String... uri) {
        this.name = name;
        this.uri = uri;
    }

    private MenuFuncionalidadeEnum(String name, boolean flagModuloEstudio, String... uri) {
        this.name = name;
        this.uri = uri;
        this.flagModuloEstudio = flagModuloEstudio;

    }

    public String getName() {
        return name;
    }

    public String[] getUri() {
        return uri;
    }

    public boolean isFlagModuloEstudio() {
        return flagModuloEstudio;
    }

    public static Optional<List<String>> getMenusFromURI(final String uri) {
        return Optional.of(Arrays.stream(
                MenuFuncionalidadeEnum.values())
                .filter(e -> e.getUri() != null && e.getUri().length > 0 && Arrays.stream(e.getUri()).anyMatch(a -> uri.contains(a)))
                .map(b -> b.getName())
                .collect(Collectors.toList()));
    }

    public static Optional<List<String>> getMenusZwUiFromURI(ModuloAberto moduloAberto) {
        ArrayList<String> menus = new ArrayList<>();
        if(moduloAberto.equals(ModuloAberto.ZILLYONWEB)){
            menus.add(ADM_INICIO.getName());
        }

        if(moduloAberto.equals(ModuloAberto.CRMWEB)){
            menus.add(CRM_INICIO.getName());
        }

        if(moduloAberto.equals(ModuloAberto.FINAN)){
            menus.add(FIN_INICIO.getName());
        }

        if(moduloAberto.equals(ModuloAberto.GESTAOSTUDIO)){
            menus.add(EST_INICIO.getName());
        }

        if(moduloAberto.equals(ModuloAberto.NOTAS)){
            menus.add(NOTAS_INICIO.getName());
        }

        if(moduloAberto.equals(ModuloAberto.PESSOAS)){
            menus.add(PESSOAS_INICIO.getName());
        }

        return Optional.of(menus);
    }


    public static void main(String[] args) {
        //System.out.println(Arrays.asList(MenuFuncionalidadeEnum.ADM_BI.getUri()).toString());
        System.out.println(getMenusFromURI("/ZillyonWeb/faces/tela1.jsp"));
    }

}

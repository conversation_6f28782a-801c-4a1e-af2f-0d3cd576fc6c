/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.feed.FeedGestaoTelaTO;
import negocio.comuns.feed.FeedGestaoVO;
import negocio.comuns.feed.IndicadorEnum;
import negocio.comuns.feed.PaginaInicialFeedVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.feed.FeedGestaoService;

import javax.faces.event.ActionEvent;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class FeedGestaoControle extends SuperControle {

    private List<FeedGestaoVO> feeds;
    private List<FeedGestaoVO> feedsBI;
    private List<FeedGestaoTelaTO> feedsApresentar;
    private boolean temNovo = false;
    private Integer nrMsgsNaoLidas = 0;
    private boolean capa = true;
    private IndicadorEnum indicadorSelecionado;
    private Map<IndicadorEnum, List<FeedGestaoTelaTO>> listas = new HashMap<IndicadorEnum, List<FeedGestaoTelaTO>>();
    private Map<IndicadorEnum, Integer> naoLidas = new HashMap<IndicadorEnum, Integer>();
    private Map<IndicadorEnum, Integer> totais = new HashMap<IndicadorEnum, Integer>();
    private Map<Integer, String> positions;
    private PaginaInicialFeedVO inicial = new PaginaInicialFeedVO();
    private FeedGestaoService service;
    private Boolean exibirFeed = Boolean.FALSE;

    public void abrirFeedExemplo() {
        try {
            FeedGestaoService service = new FeedGestaoService(Conexao.getFromSession());

//            feeds = service.obterTodos();
            inicial = service.obterCapaFeedsExemplo();
            abrirFeed();
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoControle.class);
        }
    }

    public void abrirFeed() {
        try {
            exibirFeed = true;
            notificarRecursoEmpresa(RecursoSistema.ASSISTENTE_GESTAO);
            capa = true;
            nrMsgsNaoLidas = 0;
            verificarTemNovaPorIndicador();
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoControle.class);
        }

    }

    public void verificarTemNovaPorIndicador() {
        naoLidas = new HashMap<IndicadorEnum, Integer>();
        setFeeds(Ordenacao.ordenarLista(getFeeds(), "dataVista"));
        listas = new HashMap<IndicadorEnum, List<FeedGestaoTelaTO>>();
        Collections.reverse(getFeeds());
        for (IndicadorEnum indicador : IndicadorEnum.values()) {
            listas.put(indicador, separarDicasIndicadores(indicador));
            naoLidas.put(indicador, 0);
            totais.put(indicador, 0);
        }
        for (FeedGestaoVO feed : getFeeds()) {
            marcarNaoLidas(feed);
        }
    }

    private void marcarNaoLidas(FeedGestaoVO feed) {
        if (feed.getIndicadorGrupo() != null) {
            totais.put(feed.getIndicadorGrupo(), totais.get(feed.getIndicadorGrupo()) + 1);
            if (feed.getDataVista() != null) {
                return;
            }
            naoLidas.put(feed.getIndicadorGrupo(), naoLidas.get(feed.getIndicadorGrupo()) + 1);
        }
    }

    public void verificarTemNova() {
        temNovo = false;
        nrMsgsNaoLidas = 0;
        for (FeedGestaoVO feed : getFeeds()) {
            if (feed.getDataVista() == null && !UteisValidacao.emptyNumber(feed.getCodigoHistorico())) {
                nrMsgsNaoLidas++;
            }
        }
    }

    public void proxima(ActionEvent evt) {
        try {
            Integer index = (Integer) evt.getComponent().getAttributes().get("index");
            FeedGestaoTelaTO proximo = getFeedsApresentar().get(index);
            marcarLida(proximo.getFeed1(), Calendario.hoje());
            marcarLida(proximo.getFeed2(), Calendario.hoje());
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoControle.class);
        }
    }

    public void marcarLida(FeedGestaoVO feedLido, Date data) throws Exception {
        if (feedLido != null && feedLido.getDataVista() == null && !UteisValidacao.emptyNumber(feedLido.getCodigoHistorico())) {
            getFacade().getFeedGestao().marcarLida(feedLido.getCodigoHistorico(), getUsuarioLogado().getCodigo());
            feedLido.setDataVista(data);
        }
    }

    public void ultima() {
    }

    public void like(ActionEvent evt) throws Exception {
        marcarAvaliacao(evt, true, false);
    }

    public void marcarAvaliacao(ActionEvent evt, boolean liked, boolean disliked) throws Exception {
        FeedGestaoVO feedSelecionado = (FeedGestaoVO) evt.getComponent().getAttributes().get("feedSelecionado");

        getFacade().getFeedGestao().marcarAvaliacao(feedSelecionado.getCodigoHistorico(), getUsuarioLogado().getCodigo(), liked, disliked);
        feedSelecionado.setLiked(liked);
        feedSelecionado.setDisliked(disliked);
    }

    public void dislike(ActionEvent evt) throws Exception {
        marcarAvaliacao(evt, false, true);
    }

    public FeedGestaoControle() {
        try {
//            inicial = getFacade().getPaginaInicialFeed().consultarPaginaInicial();
            inicial = new PaginaInicialFeedVO();
            if (getUsuarioLogado().getAdministrador()) {
//                setFeeds(getFacade().getFeedGestao().obterTodos(null, getUsuarioLogado(), null));
                setFeeds(new ArrayList<>());
            } else {
                LoginControle loginControle = (LoginControle) getControlador(LoginControle.class);
                if (loginControle.getPerfilAcesso() != null && loginControle.getPerfilAcesso().getTipo() != null) {
//                    setFeeds(getFacade().getFeedGestao().obterTodos(getEmpresaLogado().getCodigo(), getUsuarioLogado(),
//                            loginControle.getPerfilAcesso().getTipo().getId()));
                    setFeeds(new ArrayList<>());
                }
            }
            if (!getUsuarioLogado().getAdministrador()) {
//                setFeeds(getFacade().getFeedGestao().obterFeedsTelaBI(getEmpresaLogado().getCodigo()));
                setFeeds(new ArrayList<>());
            }
            verificarTemNova();
        } catch (Exception e) {
            Logger.getLogger(FeedGestaoControle.class.getName()).log(Level.WARNING, e.getMessage(), e);
        }
    }

    public List<FeedGestaoVO> getFeeds() {
        if(feeds == null){
            feeds = new ArrayList<>();
        }
        return feeds;
    }

    public void setFeeds(List<FeedGestaoVO> feeds) {
        this.feeds = feeds;
    }

    public boolean isTemNovo() {
        return temNovo;
    }

    public void setTemNovo(boolean temNovo) {
        this.temNovo = temNovo;
    }

    public Integer getNrMsgsNaoLidas() {
        return nrMsgsNaoLidas;
    }

    public void setNrMsgsNaoLidas(Integer nrMsgsNaoLidas) {
        this.nrMsgsNaoLidas = nrMsgsNaoLidas;
    }

    public boolean isCapa() {
        return capa;
    }

    public void setCapa(boolean capa) {
        this.capa = capa;
    }

    public void mudarIndicador(ActionEvent act) {
        capa = false;
        IndicadorEnum selec = (IndicadorEnum) act.getComponent().getAttributes().get("selec");
        if (selec != null) {
            selecionarIndicador(selec);
        }
    }

    public void selecionarIndicador(IndicadorEnum indicador) {
        try {
            this.indicadorSelecionado = indicador;
            setFeedsApresentar(listas.get(indicador));
            if (!getFeedsApresentar().isEmpty()) {
                getFeedsApresentar().get(getFeedsApresentar().size() - 1).setUltima(true);
                marcarLida(getFeedsApresentar().get(0).getFeed1(), Calendario.hoje());
                marcarLida(getFeedsApresentar().get(0).getFeed2(), Calendario.hoje());
            }
            zerar(indicador);
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoControle.class);
        }
    }

    private List<FeedGestaoTelaTO> separarDicasIndicadores(IndicadorEnum indicador) {
        List<FeedGestaoTelaTO> lista = new ArrayList<FeedGestaoTelaTO>();
        try {
            if (!getFeeds().isEmpty()) {
                FeedGestaoTelaTO tela = new FeedGestaoTelaTO();
                tela.setPrimeira(true);
                int index = 0;
                tela.setIndex(++index);
                tela.setIndicador(indicador);
                for (int i = 0; i < getFeeds().size(); i++) {
                    if (getFeeds().get(i).getIndicadorGrupo() == null
                            || !getFeeds().get(i).getIndicadorGrupo().equals(indicador)) {
                        continue;
                    }
                    if (tela.getFeed1() == null) {
                        tela.setFeed1(getFeeds().get(i));
                        lista.add(tela);
                        continue;
                    }
                    if (tela.getFeed2() == null) {
                        tela.setFeed2(getFeeds().get(i));
                        tela = new FeedGestaoTelaTO();
                        tela.setIndex(++index);
                        tela.setIndicador(indicador);
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoControle.class);
        }
        return lista;
    }

    private void zerar(IndicadorEnum indicador) {
        naoLidas.put(indicador, 0);
    }

    public void voltarCapa() {
        capa = true;
    }

    public IndicadorEnum getIndicadorSelecionado() {
        return indicadorSelecionado;
    }

    public void setIndicadorSelecionado(IndicadorEnum indicadorSelecionado) {
        this.indicadorSelecionado = indicadorSelecionado;
    }

    public List<FeedGestaoTelaTO> getFeedsApresentar() {
        if(feedsApresentar == null){
            feedsApresentar = new ArrayList<>();
        }
        return feedsApresentar;
    }

    public void setFeedsApresentar(List<FeedGestaoTelaTO> feedsApresentar) {
        this.feedsApresentar = feedsApresentar;
    }

    public Map<IndicadorEnum, List<FeedGestaoTelaTO>> getListas() {
        return listas;
    }

    public void setListas(Map<IndicadorEnum, List<FeedGestaoTelaTO>> listas) {
        this.listas = listas;
    }

    public Map<Integer, String> getPositions() {
        if (positions == null || positions.isEmpty()) {
            positions = new HashMap<Integer, String>();
            positions.put(0, "top: 120px;");
            positions.put(1, "top: 205px;");
            positions.put(2, "top: 290px;");
            positions.put(3, "top: 372px;");
            positions.put(4, "top: 455px;");
        }
        return positions;
    }

    public void setPositions(Map<Integer, String> positions) {
        this.positions = positions;
    }

    public List<IndicadorEnum> getIndicadores() {
        List<IndicadorEnum> lista = new ArrayList<IndicadorEnum>();
        Set<IndicadorEnum> keySet = listas.keySet();
        for (IndicadorEnum ind : keySet) {
            if (listas.get(ind) != null && !listas.get(ind).isEmpty()) {
                lista.add(ind);
            }
        }
        Ordenacao.ordenarLista(lista, "id");
        return lista;
    }

    public Map<IndicadorEnum, Integer> getNaoLidas() {
        return naoLidas;
    }

    public void setNaoLidas(Map<IndicadorEnum, Integer> naoLidas) {
        this.naoLidas = naoLidas;
    }

    public Map<IndicadorEnum, Integer> getTotais() {
        return totais;
    }

    public void setTotais(Map<IndicadorEnum, Integer> totais) {
        this.totais = totais;
    }

    public PaginaInicialFeedVO getInicial() {
        return inicial;
    }

    public void setInicial(PaginaInicialFeedVO inicial) {
        this.inicial = inicial;
    }

    public List<FeedGestaoVO> getFeedsBI() {
        if(feedsBI == null){
            feedsBI = new ArrayList<>();
        }
        return feedsBI;
    }

    public Boolean getMostrarAssis(){
        return feeds != null && !feeds.isEmpty();
    }

    public void setFeedsBI(List<FeedGestaoVO> feedsBI) {
        this.feedsBI = feedsBI;
    }

    public Boolean getExibirFeed() {
        return exibirFeed;
    }

    public void setExibirFeed(Boolean exibirFeed) {
        this.exibirFeed = exibirFeed;
    }
}

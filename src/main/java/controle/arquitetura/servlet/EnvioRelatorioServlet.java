/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.json.RelatorioJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONObject;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.context.FacesContext;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.bi.JasperGenerics;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

/**
 *
 * <AUTHOR>
 */
public class EnvioRelatorioServlet extends HttpServlet {
    
    private void print(PrintWriter printer, final String msg) {
        final String tmp = msg.contains("<td>") ? "" : "</br>";
        printer.println(msg + tmp);
    }

    private void print(StringBuilder printer, final String msg) {
        final String tmp = msg.contains("<td>") ? "" : "</br>";
        printer.append(msg).append(tmp);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=ISO-8859-1");
        PrintWriter out = response.getWriter();
        try {
            String chave = request.getParameter("chave");
            String matricula = request.getParameter("matricula");
            if (chave != null && matricula != null && !matricula.isEmpty()) {
                try {
                    out.println(enviarEmailRelatorio(chave, matricula));
                } catch (Exception ex) {
                    print(out, String.format("ERRO - chave: %s - ID: %s - %s ", chave, matricula, ex.getMessage()));
                    Logger.getLogger(MailingServlet.class.getName()).log(Level.SEVERE, null, ex);
                } 
            } else {
                print(out, String.format("Matrícula %s da chave %s Não Encontrada!", matricula, chave));
            }
        } finally {
            out.close();
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    public String getServletInfo() {
        return "Short description";
    }
    
    public String enviarEmailRelatorio(String key,String matricula) throws Exception {
        Connection c =new DAO().obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(key, c);
        Cliente clienteDao = new Cliente(c);
        String url = PropsService.getPropertyValue(key, PropsService.urlTreino);
        String dados = ExecuteRequestHttpService.executeRequest(url+"/prest/aula/"+key+"/relatorio?matricula="+matricula, null);
        JSONObject json = new JSONObject(dados);
        List<RelatorioJSON> lista = JSONMapper.getList(json.getJSONArray("aulas"), RelatorioJSON.class);
        Integer pontos = 0;
        for(RelatorioJSON item : lista){
            pontos += item.getBonus();
        }
        
        ClienteVO cliente = clienteDao.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        if(cliente.getPessoa().getEmailsString().isEmpty()){
            return "Você não tem email cadastrado, procure a recepção!";
        }
        JasperGenerics obj = new JasperGenerics(lista);
        obj.setTitulo("Relatório de participação de aulas");
        obj.setUsuario("Retira Ficha");
        obj.setLogo(obterLogo(key,cliente.getEmpresa()));
        obj.setEmpresa(cliente.getEmpresa());
        obj.setFiltro("Cliente:"+cliente.getPessoa().getNome()+". Você tem "+pontos+" pontos de bônus no período.");
        
        obj.putLabel("data", "Dia");
//        private String data; 
        obj.putLabel("diaSemana", "Dia semana");
//    private String diaSemana; 
        obj.putLabel("modalidade", "Modalidade");
//    private String modalidade; 
        obj.putLabel("horario", "Horário");
//    private String horario; 
        obj.putLabel("professor", "Professor");
//    private String professor; 
        obj.putLabel("bonus", "Bônus");
//    private Integer bonus;
        
        obj.setFileNameDefault(false);
        String prefixoArquivo = String.format("%s-%s-%s", new Object[]{
            "Relatorio", Uteis.retirarAcentuacaoRegex(Uteis.getPrimeiroNome(cliente.getPessoa().getNome())), new Date().getTime()});
        obj.setPath(this.getServletContext().getRealPath("relatorio")
                        + File.separator);
        obj.setPrefixoArquivo(prefixoArquivo);
        obj.prepare(true, true);
        obj.pdf();

        File file = new File(obj.getPathPrefixo()+".pdf");
        String[] array = new String[cliente.getPessoa().getEmailsString().size()];
        DaoAuxiliar.retornarAcessoControle(key).enviarEmail(
                cliente.getPessoa().getEmailsString().toArray(array), "Relatório de aulas",
                new StringBuilder(), file);
        return "ok";
    }
    
    private InputStream obterLogo(String key, EmpresaVO empresa) throws Exception {
        InputStream logo;
        empresa.setFotoRelatorio(DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().obterFoto(key, empresa.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        if (empresa.getFotoRelatorio() == null || empresa.getFotoRelatorio().length == 0) {
            logo = getImagem();
        } else {
            InputStream fs = new ByteArrayInputStream(empresa.getFotoRelatorio());
            logo = fs;
        }
        return logo;
    }

    public InputStream getImagem() throws Exception {
        String caminhoApp = obterCaminhoWebAplicacaoFoto();
        String caminho = caminhoApp + File.separator + "fotos" + File.separator + "logoPadraoRelatorio.jpg";
        File imagem = new File(caminho);
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;

    }
    

    public String obterCaminhoWebAplicacaoFoto() throws Exception {
        return ((ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext()).getRealPath("");
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONObject;
import negocio.comuns.nfe.*;
import negocio.comuns.utilitarias.QRCode;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import net.glxn.qrgen.image.ImageType;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
public class NFCeServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getParameter("nfce") != null) {

            final String nfce = request.getParameter("nfce");
            Connection con = null;
            DAO dao;
            try {
                dao = new DAO();
                con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);

                //REQUISIÇÃO PARA GERAR O PDF DAS NOTAS!

                List<NFCeImprimirTO> listaNotasNFCe = nfseDao.consultarNFCeParaGeracaoPDF(nfce);

                if (listaNotasNFCe.size() > 1) {
                    //GERAR ARQUIVO ZIP COM VÁRIOS

                    List<byte[]> arquivos = new ArrayList<byte[]>();
                    List<String> nomes = new ArrayList<String>();

                    for (NFCeImprimirTO nota : listaNotasNFCe) {
                        try {
                            String arquivo = gerarArquivoPDFNotaFiscal(nota, request);
                            File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);

                            byte[] b = new byte[(int) pdfFile.length()];
                            FileInputStream fileInputStream = new FileInputStream(pdfFile);
                            fileInputStream.read(b);

                            arquivos.add(b);
                            nomes.add(nota.getNomeArquivoPDF() + ".pdf");
                        } catch (Exception ignored) {
                        }
                    }

                    String arquivo = "PDF-NFCe-" + System.currentTimeMillis() + ".zip";

                    String nomeArquivoGerado = request.getRealPath("relatorio") + File.separator + arquivo;
                    boolean gerou = Uteis.zip(nomeArquivoGerado, arquivos, nomes);

                    if (!gerou) {
                        throw new Exception("Erro ao gerar arquivo");
                    }

                    response.setContentType("application/json");

                    JSONObject objReturn = new JSONObject();
                    objReturn.put("arquivo", arquivo);
                    objReturn.put("url", "DownloadSV?mimeType=application/zip&relatorio="+arquivo);
                    ServletOutputStream op = response.getOutputStream();
                    op.print(objReturn.toString());
                    op.flush();

                } else if (listaNotasNFCe.size() == 1) {
                    //GERAR SOMENTE DE 1 ARQUIVO

                    String arquivo = gerarArquivoPDFNotaFiscal(listaNotasNFCe.get(0), request);
                    File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
                    String nomeDownload = listaNotasNFCe.get(0).getNomeArquivoPDF() + ".pdf";

                    response.setContentType("application/pdf");
                    response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
                    response.setContentLength((int) pdfFile.length());

                    FileInputStream fileInputStream = new FileInputStream(pdfFile);
                    OutputStream responseOutputStream = response.getOutputStream();
                    int bytes;
                    while ((bytes = fileInputStream.read()) != -1) {
                        responseOutputStream.write(bytes);
                    }
                }
            } catch (Exception ex) {
                response.getOutputStream().print(ex.getMessage());
                Logger.getLogger(NFCeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

        } else if (request.getParameter("status") != null) {

            JSONObject jsonObject = new JSONObject();

            String idNFCe = request.getParameter("status");

            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);
                NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO = nfseDao.consultarStatusGestaoNFCe(Integer.parseInt(idNFCe));
                if (UteisValidacao.emptyNumber(notaFiscalConsumidorNFCeVO.getId_NFCe())) {
                    throw new Exception("NFC-e não encontrado!");
                }
                jsonObject.put("return", notaFiscalConsumidorNFCeVO.getStatusEnum());

            } catch (Exception ex) {
                try {
                    JSONObject objErro = new JSONObject();
                    objErro.put("msg", ex.getMessage());
                    jsonObject.put("error", objErro);
                } catch (Exception ignored) {
                }
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

            response.setContentType("application/json");
            response.getOutputStream().print(jsonObject.toString());
        }
    }

    private String gerarArquivoPDFNotaFiscal(NFCeImprimirTO nota, HttpServletRequest request) throws Exception {

        processarQRCode(nota);

        List<NFCeImprimirTO> lista = new ArrayList<NFCeImprimirTO>();
        lista.add(nota);

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFCe.jrxml");
        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
        params.put("tipoRelatorio", "PDF");
        params.put("tipoImplementacao", "OBJETO");
        params.put("nomeRelatorio", "NFCE");
        params.put("nomeEmpresa", "");
        params.put("listaObjetos", lista);
        return new SuperControleRelatorio().imprimirNotaFiscal(request, params);
    }

    private void processarQRCode(NFCeImprimirTO nota) throws IOException {

        String textoQRCode = nota.getUrlQRCode();

        ByteArrayOutputStream out = QRCode.from(textoQRCode).to(ImageType.PNG).withSize(325, 325).stream();
        byte[] a = (out.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        out.close();
        nota.setQrCode(fs);
        fs.close();
    }


    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

package controle.arquitetura.servlet;

import controle.arquitetura.servico.google.UploadObject;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.IOUtils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * Servlet implementation class DownloadSV
 */
public class DownloadServlet extends HttpServlet {

    /**
     * @see HttpServlet#HttpServlet()
     */
    public DownloadServlet() {
        super();

    }

//	public void processRequest(HttpServletRequest request, HttpServletResponse response) {
//		try {
//			String arquivo = request.getParameter("relatorio");
//			byte[] buffer;
//			String caminhoaArquivo = getServletContext().getRealPath("relatorio");
//			response.addHeader("Content-Disposition:", "attachment; filename=" + arquivo);
//			response.setContentType("application/cbl");
//
//			File file = new File(caminhoaArquivo + File.separator + arquivo);
//			FileInputStream stream = new FileInputStream(file);
//			buffer = new byte[(int) file.length()];
//			stream.read(buffer);
//			OutputStream out = response.getOutputStream();
//
//			out.write(buffer);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
    private void processRequest(HttpServletRequest request, HttpServletResponse response) {
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            String mimeType = request.getParameter("mimeType");
            String arquivo = request.getParameter("relatorio");
            String diretorio = request.getParameter("diretorio");
            String bucketName = request.getParameter("bkname");
            String projectId = request.getParameter("projectId");

            String caminhoaArquivo = getServletContext().getRealPath("relatorio");
            if (!UteisValidacao.emptyString(diretorio)) {
                caminhoaArquivo = getServletContext().getRealPath(diretorio);
            }
            if (caminhoaArquivo == null && !UteisValidacao.emptyString(diretorio)) {
                File tmp = new File(diretorio);
                if (tmp.exists() && tmp.isDirectory())
                    caminhoaArquivo = diretorio;
            }
            File file = new File(caminhoaArquivo + File.separator + arquivo);

            if (bucketName != null) {
                UploadObject.uploadObject(projectId, bucketName, file.getName(), file.getAbsolutePath());
            }
            response.setHeader("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"");
            response.setContentType(mimeType);
            response.setContentLength((int) file.length());

            IOUtils.copy(new FileInputStream(file), out);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                out.flush();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
     *      response)
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
     *      response)
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }
}

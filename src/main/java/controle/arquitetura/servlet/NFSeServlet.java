/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.api.client.json.Json;
import com.google.api.client.json.JsonObjectParser;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import negocio.comuns.utilitarias.UteisJSON;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.financeiro.NotasFiscaisTO;
import negocio.comuns.nfe.EmpresaNFeVO;
import negocio.comuns.nfe.NFSeImprimirTO;
import negocio.comuns.nfe.NFeImprimirTO;
import negocio.comuns.nfe.NotaFiscalConsumidorNFCeVO;
import negocio.comuns.nfe.NotaFiscalDeServicoVO;
import negocio.comuns.nfe.NotasImprimirTO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.nfe.EmpresaNFe;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Felipe
 */
public class NFSeServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getParameter("rps") != null || request.getParameter("nfce") != null) {

            final String rps = request.getParameter("rps");
            final String nfce = request.getParameter("nfce");
            final String consulta = request.getParameter("consulta");
            final String enviarEmail = request.getParameter("enviarEmail");
            final String usuario = request.getParameter("usuario");
            Connection con = null;
            DAO dao;
            try {
                dao = new DAO();
                con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);

                //REQUISIÇÃO ENVIADA PELO ZW PARA ENVIAR O EMAIL PARA O ALUNO
                if (enviarEmail != null) {

                    response.setContentType("application/json");

                    NotaFiscalDeServicoVO objNota = nfseDao.consultarPorChavePrimaria(Integer.parseInt(rps), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    objNota.setEmailConsParaEnvio(enviarEmail);
                    nfseDao.enviarEmail(objNota, 1, "Solicitado pelo ZW - Usuário: " + usuario);

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("retorno", "Solicitado envio por email com sucesso.");
                    response.getOutputStream().print(jsonObject.toString());

                } else if (consulta != null) { //REQUISIÇÃO SOLICITADA PELO ZW PARA MONSTRAR AS TELAS NA TELA DO ALUNO
                    response.setContentType("application/json");
                    JSONArray listaJSON = new JSONArray();
                    List<NotasFiscaisTO> listaNotasFiscais = new ArrayList<NotasFiscaisTO>();


                    List<NotaFiscalDeServicoVO> listaNFSe = nfseDao.consultarNFSe(rps);
                    for (NotaFiscalDeServicoVO nota : listaNFSe) {
                        listaNotasFiscais.add(nota.toTO());
                    }

                    List<NotaFiscalConsumidorNFCeVO> listaNFCe = nfseDao.consultarNFCe(nfce);
                    for (NotaFiscalConsumidorNFCeVO nota : listaNFCe) {
                        listaNotasFiscais.add(nota.toTO());
                    }

                    Ordenacao.ordenarListaReverse(listaNotasFiscais, "dataEmissaoOrdenar");
                    for (NotasFiscaisTO notasFiscaisTO : listaNotasFiscais) {
                        listaJSON.put(notasFiscaisTO.toJSON());
                    }

                    response.getOutputStream().print(listaJSON.toString());

                } else {

                    //REQUISIÇÃO PARA GERAR O PDF DAS NOTAS!

                    List<NotasImprimirTO> listaNotasGeral = nfseDao.consultarNotasParaGeracaoPDF(rps);

                    if (listaNotasGeral.size() > 1) {
                        //GERAR ARQUIVO ZIP COM VÁRIOS

                        List<String> nomes = new ArrayList<String>();
                        List<String> absolutePathToZip = new ArrayList<String>();

                        for (NotasImprimirTO nota : listaNotasGeral) {
                            try {
                                String arquivo = gerarArquivoPDFNotaFiscal(nota, request);
                                File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
                                byte[] b = new byte[(int) pdfFile.length()];
                                FileInputStream fileInputStream = new FileInputStream(pdfFile);
                                fileInputStream.read(b);

                                nomes.add(nota.getNomeArquivo() + ".pdf");
                                absolutePathToZip.add(pdfFile.getAbsolutePath());
                            } catch (Exception ignored) {
                            }
                        }

                        String arquivo = "PDF" + System.currentTimeMillis() + ".zip";

                        String nomeArquivoGerado = request.getRealPath("relatorio") + File.separator + arquivo;
                        boolean gerou = Uteis.zipFiles(nomeArquivoGerado, absolutePathToZip, nomes);

                        if (!gerou) {
                            throw new Exception("Erro ao gerar arquivo");
                        }

                        response.setContentType("application/json");

                        JSONObject objReturn = new JSONObject();
                        objReturn.put("arquivo", arquivo);
                        objReturn.put("url", "DownloadSV?mimeType=application/zip&relatorio="+arquivo);
                        ServletOutputStream op = response.getOutputStream();
                        op.print(objReturn.toString());
                        op.flush();


                    } else if (listaNotasGeral.size() == 1) {
                        //GERAR SOMENTE DE 1 ARQUIVO

                        String arquivo = gerarArquivoPDFNotaFiscal(listaNotasGeral.get(0), request);
                        File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
                        String nomeDownload = listaNotasGeral.get(0).getNomeArquivo() + ".pdf";

                        response.setContentType("application/pdf");
                        response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
                        response.setContentLength((int) pdfFile.length());

                        FileInputStream fileInputStream = new FileInputStream(pdfFile);
                        OutputStream responseOutputStream = response.getOutputStream();
                        int bytes;
                        while ((bytes = fileInputStream.read()) != -1) {
                            responseOutputStream.write(bytes);
                        }
                    }
                }
            } catch (Exception ex) {
                response.getOutputStream().print(ex.getMessage());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (request.getParameter("dash") != null) {

            String chave = request.getParameter("dash");

            JSONObject jsonObject = new JSONObject();

            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica("");

                if (chave.equals("TODOS")) {
                    chave = "";
                } else {
                    EmpresaNFe empresaNFeDao = new EmpresaNFe(con);
                    EmpresaNFeVO empresaNFeVO = empresaNFeDao.consultarPorChave(chave);
                    if (UteisValidacao.emptyNumber(empresaNFeVO.getId_Empresa())) {
                        throw new Exception("Chave não encontrada no módulo NFSe.");
                    }
                }

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);
                JSONObject retorno = nfseDao.consultarDash(chave);

                jsonObject.put("return", retorno);

                response.setContentType("application/json");
                response.getOutputStream().print(jsonObject.toString());
            } catch (Exception ex) {
                try {
                    JSONObject objErro = new JSONObject();
                    objErro.put("msg", ex.getMessage());
                    jsonObject.put("error", objErro);
                } catch (Exception ignored) {
                }
                response.getOutputStream().print(jsonObject.toString());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (request.getParameter("gestaoNotas") != null) {

            String chave = request.getParameter("gestaoNotas");
            String mes = request.getParameter("mes");

            JSONObject jsonObject = new JSONObject();

            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);
                JSONArray retorno = nfseDao.consultarGestaoDeNotas(chave, mes);

                jsonObject.put("return", retorno);

                response.setContentType("application/json");
                response.getOutputStream().print(jsonObject.toString());
            } catch (Exception ex) {
                try {
                    JSONObject objErro = new JSONObject();
                    objErro.put("msg", ex.getMessage());
                    jsonObject.put("error", objErro);
                } catch (Exception ignored) {
                }
                response.getOutputStream().print(jsonObject.toString());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

        } else if (request.getParameter("desvincular") != null) {

            JSONObject jsonObject = new JSONObject();

            String idLote = request.getParameter("desvincular");

            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);
                List<NotaFiscalDeServicoVO> notas = nfseDao.consultarPorId_Lote(idLote);
                if (UteisValidacao.emptyList(notas)) {
                    throw new Exception("RPS não encontrado!");
                } else {
                    for (NotaFiscalDeServicoVO obj : notas) {
                        nfseDao.limparIdReferenciaParaReenvio(obj);
                    }
                }
                jsonObject.put("return", "sucesso");

            } catch (Exception ex) {
                try {
                    JSONObject objErro = new JSONObject();
                    objErro.put("msg", ex.getMessage());
                    jsonObject.put("error", objErro);
                } catch (Exception ignored) {
                }
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

            response.setContentType("application/json");
            response.getOutputStream().print(jsonObject.toString());
        } else if (request.getParameter("atualizarEmpresa") != null) {
            JSONObject jsonObject = new JSONObject();

            String chave = request.getParameter("chave");
            String params = request.getParameter("params");

            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica("");

                EmpresaNFe empresaNFeDao = new EmpresaNFe(con);
                EmpresaNFeVO empresaNFeVO = empresaNFeDao.consultarPorChave(chave);

                empresaNFeDao.atualizarParametros(empresaNFeVO, params);

                jsonObject.put("return", "sucesso");
            } catch (Exception ex) {
                try {
                    JSONObject objErro = new JSONObject();
                    objErro.put("msg", ex.getMessage());
                    jsonObject.put("error", objErro);
                } catch (Exception ignored) {
                }
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

            response.setContentType("application/json");
            response.getOutputStream().print(jsonObject.toString());

        } else if (request.getParameter("corrigirLoteZero") != null) {

            Connection con = null;
            DAO dao;
            final String listaRps = request.getReader().lines().collect(Collectors.joining());
            final String chaveEmpresa = request.getParameter("chaveEmpresa");

            try {
                dao = new DAO();
                con = dao.obterConexaoEspecifica("");

                NotaFiscalDeServico nfseDao = new NotaFiscalDeServico(con);
                List<HashMap<String, String>> notas = nfseDao.consultarNFSePorRPS(listaRps, chaveEmpresa);
                response.setContentType("application/json");
                response.getOutputStream().print(new Gson().toJson(notas));

            } catch (Exception ex) {
                response.getOutputStream().print(ex.getMessage());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

        }
    }

    private String gerarArquivoPDFNotaFiscal(NotasImprimirTO nota, HttpServletRequest request) throws Exception {

        verificaImagemPadrao(nota, request);

        if (nota.getNFSe()) {

            List<NFSeImprimirTO> lista = new ArrayList<NFSeImprimirTO>();
            lista.add(nota.getNotaNFSe());

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFSe.jrxml");
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
            params.put("tipoRelatorio", "PDF");
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeRelatorio", "NFSE");
            params.put("nomeEmpresa", "");
            params.put("listaObjetos", lista);
            return new SuperControleRelatorio().imprimirNotaFiscal(request, params);

        } else {

            List<NFeImprimirTO> lista = new ArrayList<NFeImprimirTO>();
            lista.add(nota.getNotaNFe());

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator + "NFe.jrxml");
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "nfse" + File.separator);
            params.put("tipoRelatorio", "PDF");
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeRelatorio", "NFe");
            params.put("nomeEmpresa", "");
            params.put("listaObjetos", lista);
            return new SuperControleRelatorio().imprimirNotaFiscal(request, params);
        }
    }

    private void verificaImagemPadrao(NotasImprimirTO nota, HttpServletRequest request) throws Exception {
        InputStream imagemPadrao = getImagemPadrao(request);

        if (nota.getNFSe() && nota.getNotaNFSe().getLogomarcaPrefeitura() == null) {
            nota.getNotaNFSe().setLogomarcaPrefeitura(imagemPadrao);
        }

        processarImagemLogomarcaPrestador(nota, request, imagemPadrao);
    }

    private InputStream getImagemPadrao(HttpServletRequest request) throws Exception {
        String caminho = request.getRealPath("images") + File.separator + "logo_nfse.jpg";
        File imagem = new File(caminho);
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        byte buffer[] = new byte[4096];
        int bytesRead = 0;
        FileInputStream fi = new FileInputStream(imagem.getAbsolutePath());
        while ((bytesRead = fi.read(buffer)) != -1) {
            arrayOutputStream.write(buffer, 0, bytesRead);
        }
        byte[] a = (arrayOutputStream.toByteArray());
        InputStream fs = new ByteArrayInputStream(a);
        arrayOutputStream.close();
        fi.close();
        return fs;
    }

    private void processarImagemLogomarcaPrestador(NotasImprimirTO nota, HttpServletRequest request, InputStream imagemPadrao) {
        try {
            if (nota.getLogomarcaPrestadorByte().length == 0) {
                throw new Exception("Sem imagem");
            }

            String identificador = "NFSE_LOGOMARCA" + "_" + nota.getIdEmpresa();
            String caminhoBase = File.separator + "imgTempFeed" + File.separator + identificador + ".jpg";
            String caminhoSalvar = request.getRealPath("imagens") + caminhoBase;

            File arqGerado = FileUtilities.saveToFileReturnFile(nota.getLogomarcaPrestadorByte(), caminhoSalvar);
            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
            byte buffer[] = new byte[4096];
            int bytesRead = 0;
            FileInputStream fi = new FileInputStream(arqGerado.getAbsolutePath());
            while ((bytesRead = fi.read(buffer)) != -1) {
                arrayOutputStream.write(buffer, 0, bytesRead);
            }
            byte[] a = (arrayOutputStream.toByteArray());
            InputStream fs = new ByteArrayInputStream(a);
            arrayOutputStream.close();
            fi.close();
            nota.setLogomarcaPrestador(fs);
        } catch (Exception e) {
            nota.setLogomarcaPrestador(imagemPadrao);
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

package controle.arquitetura.servlet;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

// chaves de busca: sincronizarvinculos atualizarvinculos
public class VerificarVinculosAlunoProfessorTW {

    public static void main(String ... args) throws Exception {
        Map<String, Map<String, String>> unidades = new HashMap<String, Map<String, String>>();
        Map<String, String> item = new HashMap<String, String>();
        item.put("zw", "*****************************************************");
        item.put("tr", "**************************************************");
        unidades.put("item", item);

        for (String u : unidades.keySet()) {
            Connection conzw = DriverManager.getConnection(unidades.get(u).get("zw"), "postgres", "pactodb");
            Connection contr = DriverManager.getConnection(unidades.get(u).get("tr"), "postgres", "pactodb");
            start(contr, conzw, true);
        }
        System.out.println("\n\nPROCESSO CONCLUÍDO!!! *****************************************");
    }

    public static void start(Connection conBDTW, Connection conBDZW, boolean atualizar) throws Exception {
        ResultSet rsColaboradoresZW = SuperFacadeJDBC.criarConsulta(
                "select c.codigo, p.nome " +
                    "from colaborador c " +
                    "inner join pessoa p on p.codigo = c.pessoa ",
                conBDZW
        );

        while (rsColaboradoresZW.next()) {
            if (isQtVinculosDiferentes(conBDZW, conBDTW, rsColaboradoresZW.getInt("codigo"), rsColaboradoresZW.getString("nome"))) {
                if (atualizar) {
                    atualizarVinculosProfessorBDTW(conBDZW, conBDTW, rsColaboradoresZW.getInt("codigo"), rsColaboradoresZW.getString("nome"));
                }
            } else {
                System.out.println("Os vínculos do(a) professor(a) " + rsColaboradoresZW.getString("nome").toUpperCase() + " estão atualizados");
            }
        }
    }

    public static boolean isQtVinculosDiferentes(Connection conBDZW, Connection conBDTW, int colaboradorId, String nomeProfessor) throws Exception {
        ResultSet rsVinculosProfessorZW = SuperFacadeJDBC.criarConsulta(
                "select count(*) as qtvinculosZW " +
                    "from vinculo v " +
                    "where v.tipovinculo = 'TW' and v.colaborador = " + colaboradorId,
                conBDZW
        );

        if (rsVinculosProfessorZW.next()) {
            int countZW = rsVinculosProfessorZW.getInt("qtvinculosZW");
            if (countZW > 0) {
                ResultSet rsVinculosProfessorTW = SuperFacadeJDBC.criarConsulta(
                        "select count(*) qtvinculosTW " +
                            "from clientesintetico c " +
                            "inner join professorsintetico p on p.codigo = c.professorsintetico_codigo " +
                            "where p.codigocolaborador = " + colaboradorId,
                        conBDTW
                );

                if (rsVinculosProfessorTW.next()) {
                    int countTw = rsVinculosProfessorTW.getInt("qtvinculosTW");
                    if (countZW != countTw) {
                        System.out.println("\nATENÇÃO!!! Existem " + (countZW - countTw) + " alunos com vínculo desatualizado para o(a) professor(a): " + nomeProfessor.toUpperCase());
                    }

                    return countZW != countTw;
                }
            }
        }

        return false;
    }

    public static void atualizarVinculosProfessorBDTW(Connection conBDZW, Connection conBDTW, int colaboradorId, String nomeProfessor) throws Exception {
        int codProfessorTW = obterCodProfessorBDTW(conBDTW, colaboradorId);
        if (codProfessorTW > 0) {
            ResultSet rsVinculosProfessorZW = SuperFacadeJDBC.criarConsulta(
                    "select c.codigomatricula, c.codigo, p.nome " +
                        "from vinculo v " +
                        "inner join cliente c on c.codigo = v.cliente " +
                        "inner join pessoa p on p.codigo = c.pessoa " +
                        "where v.tipovinculo = 'TW' and v.colaborador = " + colaboradorId,
                    conBDZW
            );
            int count = 0;
            while (rsVinculosProfessorZW.next()) {
                ResultSet aluno = SuperFacadeJDBC.criarConsulta(
                        "select codigo, matricula, nome, professorsintetico_codigo " +
                            "from clientesintetico " +
                            "where matricula = " + rsVinculosProfessorZW.getInt("codigomatricula"),
                        conBDTW
                );
                if (aluno.next()) {
                    if (aluno.getInt("professorsintetico_codigo") != codProfessorTW) {
                        SuperFacadeJDBC.executarConsulta(
                                "update clientesintetico " +
                                    "set professorsintetico_codigo = " + codProfessorTW + " " +
                                    "where matricula = " + rsVinculosProfessorZW.getInt("codigomatricula"),
                                conBDTW
                        );
                        count++;
                    }
                } else {
                    System.out.println("..... Este aluno não foi localizado no treino: " + rsVinculosProfessorZW.getString("nome").toUpperCase() + " MAT: " + rsVinculosProfessorZW.getInt("codigomatricula"));
                }
            }
            System.out.println("..... SUCESSO: Foram atualizados " + count + " vinculos do(a) professor(a) " + nomeProfessor.toUpperCase() + " no banco do treino! \n");
        } else {
            System.out.println("\n--> TEMOS UM PROBLEMINHA HEHE: Não foi localizado o código do(a) professor(a)" + nomeProfessor.toUpperCase() + " no banco do treino, a atualização não pode prosseguir!\n");
        }
    }

    public static int obterCodProfessorBDTW(Connection conBDTW, int colaboradorId) throws Exception {
        ResultSet rsProfessorTW = SuperFacadeJDBC.criarConsulta(
                "select p.codigo " +
                    "from professorsintetico p " +
                    "where p.codigocolaborador = " + colaboradorId,
                conBDTW
        );

        if (rsProfessorTW.next()) {
            return rsProfessorTW.getInt("codigo");
        }

        return 0;
    }

}

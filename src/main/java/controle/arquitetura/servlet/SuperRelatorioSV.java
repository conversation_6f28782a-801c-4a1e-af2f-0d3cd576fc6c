/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.arquitetura.servlet;

import controle.arquitetura.security.LoginControle;
import java.io.IOException;
import javax.faces.context.FacesContext;
import javax.servlet.RequestDispatcher;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
public class SuperRelatorioSV extends HttpServlet {
    
    public FacesContext context() {
        return FacesContext.getCurrentInstance();
    }
    
    public void apresentarRelatorio( HttpServletRequest request,
                                     HttpServletResponse response,
                                     String nomeRelatorio,
                                     String xml, 
                                     String tituloRelatorio, 
                                     String nomeEmpresa,
                                     String mensagemRel,
                                     String tipoRelatorio,
                                     String parserBuscaTags,
                                     String designIReport,
                                     String nomeUsuario,
                                     String filtros,
                                     String parametro1,
                                     String parametro2,
                                     String parametro3) throws Exception {
        request.setAttribute("xmlRelatorio", xml);
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        request.setAttribute("parametro1", parametro1);
        request.setAttribute("parametro2", parametro2);
        request.setAttribute("parametro3", parametro3);
        
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        RequestDispatcher dispatcher = getServletContext().getRequestDispatcher("/VisualizadorRelatorio");
        dispatcher.forward(request, response);        
    }    
    
    public void apresentarRelatorio( HttpServletRequest request,
                                     HttpServletResponse response,
                                     String nomeRelatorio,
                                     String xml, 
                                     String tituloRelatorio, 
                                     String nomeEmpresa,
                                     String mensagemRel,
                                     String tipoRelatorio,
                                     String parserBuscaTags,
                                     String designIReport,
                                     String nomeUsuario,
                                     String filtros) throws Exception {
        request.setAttribute("xmlRelatorio", xml);
        request.setAttribute("tipoRelatorio", tipoRelatorio);
        request.setAttribute("nomeRelatorio", nomeRelatorio);
        request.setAttribute("nomeEmpresa", nomeEmpresa);
        request.setAttribute("mensagemRel", mensagemRel);
        request.setAttribute("tituloRelatorio", tituloRelatorio);
        request.setAttribute("caminhoParserXML", parserBuscaTags);
        request.setAttribute("nomeDesignIReport", designIReport);
        request.setAttribute("nomeUsuario", nomeUsuario);
        if (filtros.equals("")) {
            filtros = "nenhum";
        }
        request.setAttribute("filtros", filtros);
        RequestDispatcher dispatcher = getServletContext().getRequestDispatcher("/VisualizadorRelatorio");
        dispatcher.forward(request, response);        
    }       
    
    public String getNomeUsuario() {   
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getUsuario().getNome();
        } catch (Exception e) {
            return "";
        }
    }    

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
    throws ServletException, IOException {
    } 

    // <editor-fold defaultstate="collapsed" desc="Métodos HttpServlet. Clique no sinal de + à esquerda para editar o código.">
    /** 
    * Handles the HTTP <code>GET</code> method.
    * @param request servlet request
    * @param response servlet response
    */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
    throws ServletException, IOException {
        processRequest(request, response);
    } 

    /** 
    * Handles the HTTP <code>POST</code> method.
    * @param request servlet request
    * @param response servlet response
    */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
    throws ServletException, IOException {
        processRequest(request, response);
    }

    /** 
    * Returns a short description of the servlet.
    */
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>


}

package controle.arquitetura.servlet;

import java.io.IOException;
import java.io.OutputStream;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CentralEventosFacade;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Servlet simples para exibição de imagens.
 *
 * Caminho do diretório é 'hard coded' nesta classe.
 *
 * O nome do arquivo é passado como parâmetro.
 *
 * Responde requisições do tipo get
 *
 * <AUTHOR>
 * <AUTHOR>
 *
 */
public class ImgServlet extends HttpServlet {

    /**
     * Serial gerado automáticamente
     */
    private static final long serialVersionUID = 458704839020430050L;

    /**
     * Obtém a imagem requisitada.
     *
     * @param req <code>HttpServletRequest</code> - Solicitação do cliente
     * @param resp <code>HttpServletResponse</code> - Resposta do servidor
     */
    @Override
    public void doGet(final HttpServletRequest req, final HttpServletResponse resp) throws IOException {
        CentralEventosFacade centralEventosFacade = null;
        try {
            centralEventosFacade = FacadeManager.getFacade().getCentralEventosFacade();
            String key = (String) req.getSession().getAttribute("key");
            DAO dao = new DAO();
            centralEventosFacade.setCon(dao.obterConexaoEspecifica(key == null ? "" : key));
            dao = null;

            // Contexto do servlet (a aplicação)
            ServletContext sc = this.getServletContext();

            // obtenção do arquivo de imagem
            String nomeImagem = req.getParameter("arquivo");
            Integer codigoModelo = Integer.valueOf(req.getParameter("modelo"));
            Integer tipoDocumento = Integer.valueOf(req.getParameter("q"));

            byte[] imagem = tipoDocumento.equals(1) ? centralEventosFacade.consultarImagemPorModeloContratoENome(codigoModelo, nomeImagem)
                    : centralEventosFacade.consultarImagemPorModeloOrcamentoENome(codigoModelo, nomeImagem);

            resp.setContentType(sc.getMimeType(nomeImagem));

            // tamanho da resposta
            resp.setContentLength(imagem.length);

            // fluxo de saída da resposta
            OutputStream out = resp.getOutputStream();

            // escreve o arquivo na resposta
            out.write(imagem);

            // fecha o fluxo
            out.close();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (centralEventosFacade != null) {
                try {
                    centralEventosFacade.getCon().close();
                } catch (Exception ex) {
                    Logger.getLogger(ImgServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }
}

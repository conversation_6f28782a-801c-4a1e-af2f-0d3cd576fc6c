/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.integracao.IntegracaoCadastrosWSConsumer;

/**
 *
 * <AUTHOR>
 */
public class VerificarAlunosTreino {

    public static void procurarAluno(String nome) {
        try {
            Connection oamd = DriverManager.getConnection("jdbc:postgresql://********:5432/OAMD", "postgres", "pactodb");
//            Connection oamd = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            ResultSet rsZW = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa", oamd);
            while (rsZW.next()) {
                try {
                    System.out.println("BD: " + rsZW.getString("nomeBD"));
                    String url = rsZW.getString("urlintegracaows");
                    if (UteisValidacao.emptyString(url)) {
                        url = rsZW.getString("robocontrole");
                    }
                    String chave = rsZW.getString("chave");
                    List<ClienteVO> listaClientes = IntegracaoCadastrosWSConsumer.getListaClientes(url, chave, 0, nome);
                    for (ClienteVO cli : listaClientes) {
                        System.out.println("ACHEI:" + cli.getMatricula() + " -------------------------------------------");
                    }
                } catch (Exception e) {
                    System.err.println("erro " + e.getMessage());
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void procurarAlunosNaoExistemZW() {
        try {
            Connection oamd = DriverManager.getConnection("jdbc:postgresql://********:5432/OAMD", "postgres", "pactodb");
            Connection oamd5 = DriverManager.getConnection("*******************************/OAMD2", "postgres", "pactodb");
            Connection oamd12 = DriverManager.getConnection("**************************************", "postgres", "pactodb");
            ResultSet rsOAMD = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa", oamd);
            while (rsOAMD.next()) {
                try {
                    String nomeBD = rsOAMD.getString("nomeBD");
                    System.out.println("BD: " + nomeBD);
                    Connection conBancoTreino = null;
                    ResultSet rsOamdTreino = SuperFacadeJDBC.criarConsulta("SELECT nomeBD FROM empresa WHERE chave = '"
                            + nomeBD + "'", oamd5);
                    if (rsOamdTreino.next()) {
                        conBancoTreino = DriverManager.getConnection("*******************************/" + rsOamdTreino.getString("nomeBD"),
                                "zillyonweb", "pactodb");
                    } else {
                        rsOamdTreino = SuperFacadeJDBC.criarConsulta("SELECT nomeBD FROM empresa WHERE chave = '"
                                + nomeBD + "'", oamd5);
                        if (rsOamdTreino.next()) {
                            conBancoTreino = DriverManager.getConnection("*******************************/" + rsOAMD.getString("nomeBD"),
                                    "zillyonweb", "pactodb");
                        } else {
                            throw new Exception("Não encontrei o banco do treino desse aluno");
                        }
                    }
                    String url = rsOAMD.getString("urlintegracaows");
                    if (UteisValidacao.emptyString(url)) {
                        url = rsOAMD.getString("robocontrole");
                    }
                    String chave = rsOAMD.getString("chave");
                    List<ClienteVO> listaClientes = IntegracaoCadastrosWSConsumer.getListaClientes(url, chave, 0, "");
                    for (ClienteVO cli : listaClientes) {
                        System.out.println("ACHEI:" + cli.getMatricula() + " -------------------------------------------");
                    }
                } catch (Exception e) {
                    System.err.println("erro " + e.getMessage());
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String... args) {
        procurarAluno("YESLEY");
//        procurarAluno("AA_primei");
    }
}

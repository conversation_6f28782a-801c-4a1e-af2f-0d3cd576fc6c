/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.plano.Modalidade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class AvatarServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            if ((request.getParameter("av_pes") != null || request.getParameter("av_personal") != null
                    || request.getParameter("midia_entidade") != null)  && request.getParameter("key") != null) {
                response.setContentType("image/jpeg");
                final String id_pessoa = request.getParameter("av_pes");
                final String id_personal = request.getParameter("av_personal");
                final String key = request.getParameter("key");
                final MidiaEntidadeEnum midiaEntidade = MidiaEntidadeEnum.fromValue(request.getParameter("midia_entidade"));
                //se existir o parametro empty na request deve caso a pessoa ñ tenha foto, deve retornar output vazio
                final String forceEmptyToGasper = request.getParameter("forceEmptyToGasper");
                DAO dao = new DAO();
                try (Connection c = dao.obterConexaoEspecifica(key)) {
                    byte[] foto = null;

                    switch (midiaEntidade) {
                        case FOTO_MODADLIDADE:
                            foto = new Modalidade(c).obterFoto(key, Integer.valueOf(id_pessoa));
                            SuperControle.paintFotoInterno(response.getOutputStream(), foto,
                                    "/br/com/pactosolucoes/comuns/util/resources/fotoPadrao.jpg", null, null);
                            break;

                        default:
                            if (PropsService.asString(PropsService.typeMidiasService).equals("AWS_S3")) {
                                if (id_pessoa != null) {
                                    String urlFoto;
                                    final String fotokey = new Pessoa(c).obterFotoKey(Integer.valueOf(id_pessoa));
                                    if (fotokey != null) {
                                        urlFoto = String.format("%s/%s", PropsService.asString(PropsService.urlFotosNuvem),
                                                fotokey);
                                    } else {//fotoPadrao.jpg
                                        urlFoto = String.format("%s/fotoPadrao.jpg", PropsService.asString(PropsService.urlFotosNuvem),
                                                fotokey);
                                    }
                                    if (urlFoto != null) {
                                        response.sendRedirect(urlFoto);
                                        return;
                                    }
                                } else {
                                    foto = new Colaborador(c).obterFotoPersonal(Integer.valueOf(id_personal));
                                }

                            } else {
                                if (id_pessoa != null) {
                                    foto = new Pessoa(c).obterFoto(key, Integer.valueOf(id_pessoa));
                                } else {
                                    foto = new Colaborador(c).obterFotoPersonal(Integer.valueOf(id_personal));
                                }
                            }
                            break;
                    }

                    if (forceEmptyToGasper != null) {
                        if (foto != null && foto.length > 1096) {
                            SuperControle.paintFotoInterno(response.getOutputStream(), foto,
                                    "/br/com/pactosolucoes/comuns/util/resources/fotoPadrao.jpg", 96, 96);
                        }
                    } else {
                        SuperControle.paintFotoInterno(response.getOutputStream(), foto,
                                "/br/com/pactosolucoes/comuns/util/resources/fotoPadrao.jpg", 96, 96);
                    }
                    foto = null;
                } catch (Exception ex) {
                    Logger.getLogger(AvatarServlet.class.getName()).log(Level.SEVERE, null, ex);
                    try {
                        if (forceEmptyToGasper == null) {
                            SuperControle.paintFotoInterno(response.getOutputStream(), null, "/br/com/pactosolucoes/comuns/util/resources/fotoPadrao.jpg", 96, 96);
                        }
                    } catch (Exception ex1) {
                        Logger.getLogger(AvatarServlet.class.getName()).log(Level.SEVERE, null, ex1);
                    }
                } finally {
                    dao = null;
                }
            } else if (request.getParameter("get_persons") != null && request.getParameter("key") != null) {
                response.setContentType("application/json");
                final String key = request.getParameter("key");
                DAO dao = new DAO();
                try (Connection c = dao.obterConexaoEspecifica(key)) {
                    JSONArray json = new JSONArray();
                    ResultSet rs = SuperFacadeJDBC.criarConsulta("select p.codigo, p.fotoKey from pessoa p\n"
                            + "where ((p.codigo in (select pessoa from cliente where codigo in (select coalesce(cliente,0) from usuariomovel)))\n"
                            + "or (p.codigo in (select pessoa from colaborador where codigo in (select coalesce(colaborador,0) from usuariomovel))))\n"
                            + "and p.fotoKey is not null\n"
                            + "order by p.codigo", c);
                    while (rs.next()) {
                        JSONObject obj = new JSONObject();
                        obj.put("id_pessoa", rs.getInt("codigo"));
                        obj.put("fotokey", rs.getString("fotoKey"));
                        json.put(obj);
                    }
                    response.getOutputStream().print(json.toString());
                } catch (Exception ex) {
                    Logger.getLogger(AvatarServlet.class.getName()).log(Level.SEVERE, null, ex);
                } finally {
                    dao = null;
                }

            }
        } finally {
//            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.UsuarioControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 * <AUTHOR>
 */
public class SincronizarUsuarioServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */

    private static String ERROR = "error";
    private static String RETURN = "return";
    private static String RETURN_SUCESSO = "ok";

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        JSONObject jsonResponse = new JSONObject();

        try {
            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("KEY NÃO INFORMADA");
            }

            String username = request.getParameter("username");
            String codUsuario = request.getParameter("codusuario");

            Integer codigoUsuario = 0;
            try {
                codigoUsuario = Integer.parseInt(codUsuario);
            }catch (Exception ex) {
                ex.printStackTrace();
            }

            if (UteisValidacao.emptyString(username) && UteisValidacao.emptyNumber(codigoUsuario)) {
                throw new Exception("USUÁRIO NÃO INFORMADO");
            }

            con = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(key, con);

            Usuario usuarioDAO = new Usuario(con);
            UsuarioVO usuarioVO = new UsuarioVO();
            if (!UteisValidacao.emptyString(username)) {
                usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else if (!UteisValidacao.emptyNumber(codigoUsuario)) {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(codigoUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            usuarioDAO = null;

            if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("USUÁRIO NÃO ENCONTRADO");
            }

            UsuarioControle usuarioControle = new UsuarioControle();
            usuarioControle.setKey(key);
            usuarioControle.setUsuarioVO(null);
            usuarioControle.editarParametro(usuarioVO);
            usuarioControle.preencherUsuarioEmail();
            usuarioControle.sincronizarUsuarioMovel(true, key);

            jsonResponse.put(RETURN, "Usuario " + usuarioVO.getUsername() + " sincronizado com sucesso!");
        } catch (Exception ex) {
            Uteis.logar(null, "ERRO - REQUISIÇÃO NotaFiscalServlet: " + ex.getMessage());
            ex.printStackTrace();
            jsonResponse.put(ERROR, ex.getMessage().toUpperCase());
        } finally {
            try {
                if (con != null) {
                    con.close();
                }
            } catch (Exception ignored) {
            }
        }
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        response.getOutputStream().print(jsonResponse.toString());
    }
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

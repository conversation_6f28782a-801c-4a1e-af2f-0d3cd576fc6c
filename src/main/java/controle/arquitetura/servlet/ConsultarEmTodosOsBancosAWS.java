/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class ConsultarEmTodosOsBancosAWS {

    public static void main(String... args) {
        try {
            Connection oamd2 = DriverManager.getConnection("***************************************************************************/OAMD2", "oamd", "pactotreino");
            Connection oamd = DriverManager.getConnection("***************************************************", "postgres", "pactodb");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa where chave = 'df30051aa71e45c0070e15b8e7340854'", oamd);
            Map<String,String> mapaOamd = new HashMap<String, String>();
            while(rs.next()){
                mapaOamd.put(rs.getString("chave"),rs.getString("nomeBD"));
            }
            ResultSet rs2 = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa where chave = 'df30051aa71e45c0070e15b8e7340854'", oamd2);
            while (rs2.next()) {
                System.out.println(rs2.getString("nomeBD"));
                validarSituacaoAlunos(rs2.getString("nomeBD"),rs2.getString("chave"), mapaOamd);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    static void validarSituacaoAlunos(String nomeBD, String chave, Map<String,String> mapaOamd){
        try {
            Connection conBancoZW = DriverManager.getConnection("**********************************************/" + mapaOamd.get(chave), "postgres", "pactodb");
            ResultSet rsZW = SuperFacadeJDBC.criarConsulta("select codigocliente, situacao, situacaocontrato from situacaoclientesinteticodw ", conBancoZW);
            Map<Integer, Map<String, String>> situacoes = new HashMap<Integer, Map<String, String>>();
            while(rsZW.next()){
                Map<String,String> situacao = new HashMap<String, String>();
                situacao.put("situacao", rsZW.getString("situacao"));
                situacao.put("situacaocontrato", rsZW.getString("situacaocontrato"));
                situacoes.put(rsZW.getInt("codigocliente"), situacao);
            }
            String select = "select matricula, codigocliente, situacao, situacaocontrato from clientesintetico";
            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            
            ResultSet rsClienteSintetico = SuperFacadeJDBC.criarConsulta(select, conBanco);
            while(rsClienteSintetico.next()){
                Integer codigoCliente = rsClienteSintetico.getInt("codigocliente");
                if(rsClienteSintetico.getString("situacao") == null
                        || situacoes.get(codigoCliente) == null
                        || situacoes.get(codigoCliente).get("situacao") == null){
                    continue;
                }
                if(!rsClienteSintetico.getString("situacao").equals(situacoes.get(codigoCliente).get("situacao"))){
                    System.out.println("Atualizando a situacao do cliente "+rsClienteSintetico.getString("matricula"));
                    String update = "UPDATE clientesintetico SET situacao = '"+situacoes.get(codigoCliente).get("situacao")+"'";
                    if(situacoes.get(codigoCliente).get("situacaocontrato") != null){
                        update += ", situacaocontrato = '" + situacoes.get(codigoCliente).get("situacaocontrato")+"'";
                    }
                    update += " where codigocliente = "+codigoCliente;
                    SuperFacadeJDBC.executarConsulta(update, conBanco);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    
    static void imprimir(Map<String, List<String>> mapa, String entidade){
        System.out.println(entidade+"________________________________");
            for (String key : mapa.keySet()) {
                if (mapa.get(key).isEmpty()) {
                    continue;
                }
                System.out.println(key);
                for (String codigo : mapa.get(key)) {
                    System.out.println(" - " + codigo);
                }
                System.out.println("_____________________________________");
            }
    }

    public static List<String> verDuplicidadesUsuario(String nomeBD) {
            StringBuilder sql = new StringBuilder("select cliente_codigo,professor_codigo, count(codigo) from usuario  where cliente_codigo is not null");
            sql.append(" GROUP BY cliente_codigo,professor_codigo HAVING COUNT(codigo) >1 ");
        try {
            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            ResultSet rsusuarios = SuperFacadeJDBC.criarConsulta(sql.toString(), conBanco);
            List<String> lista = new ArrayList<String>();
            while (rsusuarios.next()) {
                if(rsusuarios.getInt("cliente_codigo") == 0){
                    lista.add("professor. "+String.valueOf(rsusuarios.getInt("professor_codigo")));    
                }else{
                    lista.add("cliente. "+String.valueOf(rsusuarios.getInt("cliente_codigo")));    
                }
                
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<String>();
    }
    
    public static void ajustarNrTreinoRealizados(String nomeBD) {
            StringBuilder sql = new StringBuilder("UPDATE clientesintetico cs SET nrtreinosprevistos = "
                    + "(select totalaulasprevistas from programatreino where cliente_codigo = cs.codigo "
                    + "and datainicio < '2015-04-03' order by codigo desc limit 1)\n" +
                        "where nrtreinosprevistos is not null and nrtreinosprevistos <> "
                    + "(select totalaulasprevistas from programatreino where cliente_codigo = cs.codigo and datainicio < '2015-04-03' "
                    + "order by codigo desc limit 1)");
        try {
            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            SuperFacadeJDBC.executarConsulta(sql.toString(), conBanco);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static List<String> verDuplicidadesTreinoAndamento(String nomeBD) {
            StringBuilder sql = new StringBuilder("SELECT programa_codigo FROM ProgramaTreinoAndamento ");
            sql.append(" GROUP BY programa_codigo ");
            sql.append(" HAVING COUNT(codigo) > 1 ");
        try {
            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            ResultSet rsusuarios = SuperFacadeJDBC.criarConsulta(sql.toString(), conBanco);
            List<String> lista = new ArrayList<String>();
            while (rsusuarios.next()) {
                lista.add(String.valueOf(rsusuarios.getInt("programa_codigo")));
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<String>();
    }

    public static List<String> verDuplicidadesCliente(String nomeBD) {
        StringBuilder sql = new StringBuilder("select matricula, count(codigo) from clientesintetico  ");
        sql.append(" GROUP BY matricula HAVING COUNT(codigo) > 1 ");
        try {
            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            ResultSet rsusuarios = SuperFacadeJDBC.criarConsulta(sql.toString(), conBanco);
            List<String> lista = new ArrayList<String>();
            while (rsusuarios.next()) {
                lista.add(String.valueOf(rsusuarios.getInt("matricula")));
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<String>();
    }

    public static List<String> verDuplicidadesTreinoRealizado(String nomeBD) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT cast(datainicio as date) as data, cliente_codigo, programatreinoficha_codigo FROM treinorealizado");
            sql.append(" \nGROUP BY cast(datainicio as date), cliente_codigo , programatreinoficha_codigo ");
            sql.append(" \nHAVING COUNT(codigo) > 1 ");

            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            ResultSet rsusuarios = SuperFacadeJDBC.criarConsulta(sql.toString(), conBanco);
            List<String> lista = new ArrayList<String>();
            while (rsusuarios.next()) {
                lista.add(String.valueOf(rsusuarios.getInt("cliente_codigo"))
                        + ";"+String.valueOf(rsusuarios.getInt("programatreinoficha_codigo"))
                        + ";"+String.valueOf(rsusuarios.getDate("data")));
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<String>();
    }
    
    public static List<String> verDuplicidadesProfessor(String nomeBD) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" select codigocolaborador, count(codigo) from professorsintetico \n");
            sql.append(" GROUP BY codigocolaborador \n");
            sql.append(" HAVING COUNT(codigo) > 1");

            Connection conBanco = DriverManager.getConnection("***************************************************************************/" + nomeBD, "zillyonweb", "pactotreino");
            ResultSet rsusuarios = SuperFacadeJDBC.criarConsulta(sql.toString(), conBanco);
            List<String> lista = new ArrayList<String>();
            while (rsusuarios.next()) {
                lista.add(String.valueOf(rsusuarios.getInt("codigocolaborador")));
            }
            return lista;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<String>();
    }
    
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <PERSON>an<PERSON>
 */
public class AtualizaContratoServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

            try {
                Integer contrato = Integer.parseInt(request.getParameter("contrato"));
                String chave = request.getParameter("chave");
                Integer codigoPessoa =  Integer.parseInt(request.getParameter("codigoPessoa"));

                try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                    Conexao.guardarConexaoForJ2SE(chave, con);

                    atualizarContrato(contrato,codigoPessoa, con, request, response);
                }
            } catch (Exception ex) {
                response.getOutputStream().print(ex.getMessage());
                Logger.getLogger(NFCeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

    }



    private void atualizarContrato(Integer contrato, Integer pessoa, Connection con, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ContratoTextoPadrao contratoTextoPadraoDao = new ContratoTextoPadrao(con);
        String htmlContrato = contratoTextoPadraoDao.atualizarHtmlContrato(contrato,pessoa);
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        out.println(htmlContrato);
    }



    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

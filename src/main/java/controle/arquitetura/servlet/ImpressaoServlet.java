/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoImpressaoTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.ContratoTextoPadrao;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
public class ImpressaoServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getParameter("info") != null) {
            try {

                JSONObject json = new JSONObject(Uteis.desencriptar(request.getParameter("info"), Uteis.getChaveCriptoImpressaoServlet()));
                String chave = json.getString("chave");

                try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                    Conexao.guardarConexaoForJ2SE(chave, con);

                    if (json.has("transacao")) {
                        imprimirTransacao(json, con, request, response);
                    } else if (json.has("contrato")) {
                        imprimirContrato(json, con, request, response);
                    } else if (json.has("recibo")) {
                        imprimirRecibo(chave, json.optInt("recibo"), json.optInt("usuario"), request, response, con);
                    }
                }
            } catch (Exception ex) {
                response.getOutputStream().print(ex.getMessage());
                Logger.getLogger(NFCeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private void imprimirTransacao(JSONObject json, Connection con, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Integer transacao = json.getInt("transacao");
        Transacao transacaoDAO = new Transacao(con);
        TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(transacao);
        transacaoDAO = null;

        Empresa empresaDAO = new Empresa(con);
        transacaoVO.setEmpresaVO(empresaDAO.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        empresaDAO = null;

        TransacaoImpressaoTO transacaoImpressaoTO = new TransacaoImpressaoTO(transacaoVO);

        escolherTidOuCodigoExternoImpressao(transacaoImpressaoTO);

        String arquivo = gerarArquivoPDFEstornoTransacao(transacaoImpressaoTO, request);
        File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
        String nomeDownload = "COMPROVANTE-" + transacaoImpressaoTO.getCodigoExterno() + ".pdf";

        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
        response.setContentLength((int) pdfFile.length());

        FileInputStream fileInputStream = new FileInputStream(pdfFile);
        OutputStream responseOutputStream = response.getOutputStream();
        int bytes;
        while ((bytes = fileInputStream.read()) != -1) {
            responseOutputStream.write(bytes);
        }
    }

    private void escolherTidOuCodigoExternoImpressao(TransacaoImpressaoTO transacaoImpressaoTO) {
        if (transacaoImpressaoTO.getTipoTransacaoEnum().equals(TipoTransacaoEnum.DCC_CAIXA_ONLINE)) {
            transacaoImpressaoTO.setTid(transacaoImpressaoTO.getCodigoExterno());
        }
    }

    private void imprimirContrato(JSONObject json, Connection con, HttpServletRequest request, HttpServletResponse response) throws Exception {
        Integer contrato = json.getInt("contrato");
        ContratoTextoPadrao contratoTextoPadraoDao = new ContratoTextoPadrao(con);
        String htmlContrato = contratoTextoPadraoDao.consultarHtmlContrato(contrato,true);
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        out.println(htmlContrato);
    }

    private String gerarArquivoPDFEstornoTransacao(TransacaoImpressaoTO transacaoImpressaoTO, HttpServletRequest request) {

        List<TransacaoImpressaoTO> lista = new ArrayList<TransacaoImpressaoTO>();
        lista.add(transacaoImpressaoTO);

        Map<String, Object> params = new HashMap<String, Object>();
        params.put("nomeDesignIReport", "relatorio" + File.separator + "designRelatorio" + File.separator + "transacao" + File.separator + "ComprovanteCancelamentoTransacao.jrxml");
        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "transacao" + File.separator);
        params.put("tipoRelatorio", "PDF");
        params.put("tipoImplementacao", "OBJETO");
        params.put("nomeRelatorio", "COMPROVANTE");
        params.put("nomeEmpresa", "");
        params.put("listaObjetos", lista);
        return new SuperControleRelatorio().imprimirRetornaNomeArquivo(request, params);
    }

    private void imprimirRecibo(String chave,
                                Integer recibo, Integer usuario,
                                HttpServletRequest request,
                                HttpServletResponse response,
                                Connection con) throws Exception {
        String url = imprimirReciboGeral(recibo, usuario, chave, request, con);
        String arquivo = url.split("/")[url.split("/").length - 1];

        File pdfFile = new File(request.getRealPath("relatorio") + File.separator + arquivo);
        String nomeDownload = "RECIBO-" + recibo + "-" + chave + ".pdf";

        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=" + nomeDownload);
        response.setContentLength((int) pdfFile.length());

        FileInputStream fileInputStream = new FileInputStream(pdfFile);
        OutputStream responseOutputStream = response.getOutputStream();
        int bytes;
        while ((bytes = fileInputStream.read()) != -1) {
            responseOutputStream.write(bytes);
        }
    }

    private String imprimirReciboGeral(Integer recibo, Integer usuario, String chave,
                                       HttpServletRequest request, Connection con) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        Usuario usuarioDAO;
        try {
            reciboPagamentoDAO = new ReciboPagamento(con);
            usuarioDAO = new Usuario(con);

            ReciboPagamentoVO reciboVO = reciboPagamentoDAO.consultarPorChavePrimaria(recibo, Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO;
            if (UteisValidacao.emptyNumber(usuario)) {
                usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            } else {
                usuarioVO = usuarioDAO.consultarPorChavePrimaria(usuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            request.getSession().setAttribute("key", chave);

            return reciboPagamentoDAO.imprimirReciboPDF(Boolean.FALSE,
                    reciboVO.getEmpresa().isReciboParaImpressoraTermica(), reciboVO.getEmpresa(),
                    reciboVO.getEmpresa(), usuarioVO, reciboVO, null, "PDF", request);
        } finally {
            reciboPagamentoDAO = null;
            usuarioDAO = null;
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

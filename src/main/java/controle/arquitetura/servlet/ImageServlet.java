package controle.arquitetura.servlet;
import java.io.*;
import java.util.Map;

import javax.servlet.*;
import javax.servlet.http.*;

/**
 * Classe padrão do JasperReport utilizada para preparar as imagens a serem apresentadas
 * durante a exibição dos relatórios.
 */
public class ImageServlet extends HttpServlet {
    
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
    throws ServletException, IOException {
        Map imagesMap = (Map)request.getSession().getAttribute("IMAGES_MAP");
        
        if (imagesMap != null) {
            String imageName = request.getParameter("image");
            if (imageName != null) {
                byte[] imageData = (byte[])imagesMap.get(imageName);
                
                response.setContentLength(imageData.length);
                ServletOutputStream ouputStream = response.getOutputStream();
                ouputStream.write(imageData, 0, imageData.length);
                ouputStream.flush();
                ouputStream.close();
            }
        }
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /** Handles the HTTP <code>GET</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
    throws ServletException, IOException {
        processRequest(request, response);
    }
    
    /** Handles the HTTP <code>POST</code> method.
     * @param request servlet request
     * @param response servlet response
     */
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
    throws ServletException, IOException {
        processRequest(request, response);
    }
    
    /** Returns a short description of the servlet.
     */
    public String getServletInfo() {
        return "Short description";
    }
    // </editor-fold>
}

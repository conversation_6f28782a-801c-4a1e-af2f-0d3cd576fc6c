/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet.update;

import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.UsuarioOAMDCacheTO;
import controle.arquitetura.servico.UpdateServico;
import controle.arquitetura.session.SessionTO;
import controle.arquitetura.session.listener.SessionState;
import controle.basico.SuporteControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisServlet;
import negocio.comuns.utilitarias.UteisServletExportData;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.bi.VerificadorDeInconsitencias;
import servicos.discovery.DiscoveryMsService;
import servicos.integracao.OamdWSConsumer;
import servicos.notificador.NotificacaoTO;
import servicos.notificador.NotificadorServiceControle;
import servicos.oamd.RedeEmpresaService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class UpdateServlet extends HttpServlet {

    List<Map> listaOAMD = null;
    private boolean exibirTitulo = true;
    private int seq = 1;
    private UteisServlet uteisServlet = new UteisServlet();

    public static final Map<String, UsuarioOAMDCacheTO> MAPA_USUARIOS = new HashMap<>();


    private final static int MAX_THREAD_POOL_SIZE = 20;

    public static String lerConfig(final String attr) {
        return Uteis.getPropriedade(
                "/servicos/propriedades/SuperControle.properties",
                attr);
    }

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String operacao = request.getParameter("op");
        final String format = request.getParameter("format");
        PrintWriter out = null;
        if (request.getParameter("email") != null && operacao.contains("exportData")){
            response.setContentType("text/html;charset=ISO-8859-1");
            out = response.getWriter();
        }
        Enumeration<String> parameterNames = request.getParameterNames();

        while (parameterNames.hasMoreElements()) {

            String paramName = parameterNames.nextElement();

            String[] paramValues = request.getParameterValues(paramName);
            for (int i = 0; i < paramValues.length; i++) {
                String paramValue = paramValues[i];
                Uteis.logar(String.format("############# UpdateServlet => param: %s value %s ###########", paramName, paramValue));
            }
        }

        if (request.getParameter("mimetype") != null
                && (format != null && format.startsWith("csv"))){
            response.setContentType("text/csv");
            out = response.getWriter();
        } else if (operacao.equals("selectParquet") || operacao.contains("SchemaParquet")){
            response.setContentType("application/json");
            out = response.getWriter();
        } else if (request.getParameter("mimetype") != null
                || (format != null && (format.equals("excel")  || format.startsWith("csv")))
                || (operacao.equals("dc")
                || (operacao.equals("dcatalog")) || (operacao.equals("purgecatalog"))
                || operacao.equals("errosFull"))) {
            //
        } else {
            response.setContentType("text/html;charset=ISO-8859-1");
            out = response.getWriter();
        }

        seq = 1;
        exibirTitulo = request.getParameter("title") != null
                && request.getParameter("title").equalsIgnoreCase("s");

        if (operacao == null || operacao.isEmpty()) {
            return;
        }

        OperacaoUpdateServletEnum operacaoEnum = OperacaoUpdateServletEnum.fromString(operacao);
        if (operacaoEnum == null) {
            return;
        }

        try {
            switch (operacaoEnum) {
                case UPDATE_PG:
                    atualizarTodosBancosDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            out);
                    break;
                case UPDATE_PG_ONE:
                    atualizaUmBanco(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            out);
                    break;
                case UPDATE_ALL:
                    executarComandoUpdateTodosBancosDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("sql"),
                            request.getParameter("except"),
                            request.getParameter("prefixoBanco"),
                            request.getParameter("lgn"),
                            out);
                    break;
                case UPDATE_ONE:
                    executarComandoUpdate(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("sql"),
                            request.getParameter("lgn"),
                            out);
                    break;
                case FINANCEIRO_PACTO:
                    executarChamadasFinanceiro(
                            request.getParameter("chave"),
                            request.getParameter("empresa"),
                            request.getParameter("data"),
                            out, response);
                    break;
                case SELECT_FULL_XML:
                    executarConsultaFull_XML(
                            request.getParameter("sql"),
                            request.getParameter("chave"),
                            request.getParameter("except"),
                            request.getParameter("prefixoBanco"),
                            request.getParameter("chavesOnly"),
                            out, request, response);
                    break;
                case SELECT_ALL:
                    executarConsultaSQLTodosBancosDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("sql"),
                            request.getParameter("except"),
                            request.getParameter("format"),
                            request.getParameter("prefixoArquivo"),
                            request.getParameter("prefixoBanco"),
                            request.getParameter("chavesOnly"),
                            request.getParameter("lgn"),
                            out, response);


                    break;
                case SELECT_PARQUET:
                    executarConsultaSQLTodosBancosDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("sql"),
                            request.getParameter("except"),
                            "parquet",
                            request.getParameter("prefixoArquivo"),
                            request.getParameter("prefixoBanco"),
                            request.getParameter("chavesOnly"),
                            request.getParameter("lgn"),
                            out, response);

                    break;
                case EXPORT_DATA:
                    exportarDadosBancoDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("tables"),
                            request.getParameter("format"),
                            request.getParameter("email"),
                            request.getParameter("lgn"),
                            out, request);

                    break;
                case EXPORT_DATA_SYNC:
                    exportarDadosBancoDadosSincrono(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("tables"),
                            request.getParameter("format"),
                            request.getParameter("email"),
                            request.getParameter("lgn"),
                            out, request);

                    break;
                case SELECT_ONE:
                    executarConsultaSQL(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("sql"),
                            request.getParameter("format"),
                            request.getParameter("prefixoArquivo"),
                            request.getParameter("lgn"),
                            request.getParameter("appName"),
                            out, response);
                    break;
                case CHANGE_OWNER_ALL:
                    alterarOwnerTodosBancosDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("newOwner"),
                            request.getParameter("except"),
                            out);
                    break;
                case CHANGE_OWNER:
                    alterarOwnerBancosDados(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("newOwner"),
                            out);
                    break;
                case CREATE_NOTF:
                    criarNotificacao(
                            request.getParameter("desc"),
                            request.getParameter("dti"),
                            request.getParameter("dtf"),
                            request.getParameter("tipoNOTF"),
                            request.getParameter("chave"),
                            request.getParameter("localAcesso"),
                            request.getParameter("propagable"),
                            request.getRequestURL().toString(),
                            request.getParameter("timeZone"));
                    break;
                case LIST_NOTF:
                    listNotificacoes(out);
                    break;
                case CLEAR_NOTF:
                    NotificadorServiceControle.limpar();
                    break;
                case DOWNLOAD_FILE:
                    if (request.getParameter("mimetype") != null) {
                        uteisServlet.doDownloadFile(
                                request.getParameter("file"),
                                request.getParameter("relatorio"),
                                request.getParameter("mimetype"),
                                response, this.getServletContext());
                    } else {
                        doDownloadFile(request.getParameter("file"), out);
                    }

                    break;
                case DOWNLOAD_FILE_DIRETORIO_ARQUIVOS:
                    uteisServlet.doDownloadFileDiretorioArquivos(request.getParameter("file"),
                            request.getParameter("mimetype"),
                            response, this.getServletContext());
                    break;
                case TROCA_EMPRESA:
                    String urlLoginRedirect = (String) request.getSession().getAttribute("urlLoginRedirect");
                    String urlRedirectModulo = (String) request.getSession().getAttribute("urlRedirectModulo");
                    request.getSession(false).invalidate();
                    if (!UteisValidacao.emptyString(urlRedirectModulo)) {
                        response.sendRedirect(urlRedirectModulo);
                    } else {
                        response.sendRedirect(urlLoginRedirect);
                    }
                    break;
                case LOGOUT:
                    Object vindoLogin = request.getSession().getAttribute("vindoLogin");
                    String urlOamd = (String) request.getSession().getAttribute("urlOamd");
                    String urlLoginDeslogar = (String) request.getSession().getAttribute("urlLoginDeslogar");
                    request.getSession(false).invalidate();
                    if (!UteisValidacao.emptyString(urlOamd)) {
                        response.sendRedirect(urlOamd);
                    } else if (vindoLogin == null) {
                        String url = Uteis.desencriptar(request.getParameter("ru"), "URLs:ZW");
                        url = url.isEmpty() ? "" : url.substring(0, url.indexOf("("));
                        response.sendRedirect(request.getContextPath() + "/faces/se.jsp?ru=" + url);
                    } else {
                        response.sendRedirect(urlLoginDeslogar);
                    }
                    break;
                case PROCESS_ONE:
                    executarProcesso(
                            request.getParameter("method"),
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("callable"),
                            request.getParameter("lgn"),
                            out);
                    break;
                case PROCESS_ALL:
                    executarProcessoTodosBancosDados(
                            request.getParameter("method"),
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("callable"),
                            request.getParameter("lgn"),
                            out);

                    break;
                case URL_BANNERS:
                    doGetBanners(out, request.getParameter("modulo"));
                    response.addHeader("Access-Control-Allow-Origin", "*");
                    break;
                case REFRESH_CONFIG:
                    doRefreshConfig(out, request.getParameter("propagable"), request);
                    break;
                case SET_CONFIG:
                    //configName=nome_config&configValue=valor_config
                    doSetConfig(out, request.getParameter("propagable"), request);
                    break;
                case INVALIDATE_IDLE_SESSIONS:
                    doInvalidateIdleSessions(out, request.getParameter("propagable"), request);
                    break;
                case INVALIDATE_IDLE_SESSIONS_KEY:
                    doInvalidateIdleSessionsKey(out, request.getParameter("propagable"), request,
                            request.getParameter("chave"),
                            request.getParameter("empresa"));
                    break;
                case LIST_SESSIONS:
                    doListSessions(out, response, request.getParameter("propagable"), request);
                    break;
                case DC:
                    doDownloadCache(out, response, request);
                    break;
                case ERROS_FULL:
                    doDownloadCache(out, response, request);
                    break;
                case DCATALOG:
                    doDownloadCatalog(out, response, request);
                    break;
                case UPDATE_LGN_CATALOG:
                    doDownloadCatalog(out, response, request);
                    break;
                case PURGE_CATALOG:
                    doPurgeCatalog(out, response, request);
                    break;
                case VERIFICADOR:
                    VerificadorDeInconsitencias.executar(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("urlUS"),
                            request.getParameter("bd"),
                            request.getParameter("emailsPara"),
                            request.getParameter("equipe"));
                    break;
                case GET_LAST_ACTION_TIME:
                    Long valor = (Long) request.getSession().getAttribute(JSFUtilities.LAST_ACTION_TIMESTAMP);
                    response.setContentType("text/plain");
                    if (valor != null) {
                        response.getWriter().print(valor);
                    } else {
                        response.getWriter().print(0L);
                    }
                    break;
                case SET_LAST_ACTION_TIME:
                    Long lastAct = Calendario.getDateInTimeZone(new Date(), "GMT").getTime();
                    request.getSession().setAttribute(JSFUtilities.LAST_ACTION_TIMESTAMP, lastAct);
                    response.setContentType("text/plain");
                    response.getWriter().print(lastAct);
                    break;
                case ENABLE_DEBUG:
                    Uteis.debug = Boolean.parseBoolean(request.getParameter("value"));
                    String propagable = request.getParameter("propagable");
                    if (propagable != null && propagable.equals("s")) {
                        propagarRequestInner(operacao, out, request.getParameterMap(), request.getRequestURL().toString());
                    }
                    break;
                case UPDATE_BOUNCE:
                    UteisEmail.forcarPovoarEmailsEmBounce();
                    propagarRequestInner(operacao, out, request.getParameterMap(), request.getRequestURL().toString());
                    break;
                case UPDATE_LOGOS:
                    Uteis.updateOamdImagemTodasEmpresas(request.getParameter("key"));
                    break;
                case GENERATE_CRYPTO:
                    final String result = Uteis.encriptar(request.getParameter("key"));
                    response.setContentType("text/plain");
                    response.getWriter().print(result.trim());
                    break;
                case LIMPAR_MAPA_REDE_EMPRESA:
                    RedeEmpresaService.limparMapaDeRedes();
                    break;
                case PREENCHER_SCHEMA_PARQUET:
                    new UpdateServico().preencherSchemaParquet(
                            request.getParameter("sql"),
                            request.getParameter("prefixoBanco"),
                            request.getParameter("chave"),
                            request.getParameter("lgn"),
                            request, out);
                    break;
                case EXTRACT_SCHEMA_PARQUET:
                    extractSchemaParquet(
                            request.getParameter("hostPG"),
                            request.getParameter("portaPG"),
                            request.getParameter("userPG"),
                            request.getParameter("pwdPG"),
                            request.getParameter("bd"),
                            request.getParameter("sql"),
                            request.getParameter("lgn"),
                            out);
                    break;
                case CLEAR_USER_CACHE_MAP:
                    clearUsuariosMap();
                    break;
            }
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
    
    public void atualizaUmBanco(final String nomeHostPG, final String portaPG, 
            final String superUserPG, final String senhaPG, final String nomeBanco, 
            PrintWriter out) {
         Connection conAtual = null;
         try {
            conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco);
            SuperControle superControle = new SuperControle();
            Conexao.guardarConexaoForJ2SE(conAtual);
            AtualizadorBD atualizador = new AtualizadorBD(conAtual);
            atualizador.atualizar(superControle.getVersaoBD(), 1);
            uteisServlet.print(out, conAtual.getMetaData().getURL() + ": Atualização executada com sucesso.");
            Uteis.logar(null, conAtual.getMetaData().getURL() + ": Atualização executada com sucesso.");
            //
        } catch (Exception e) {
             try {
                 if (conAtual != null) uteisServlet.print(out, conAtual.getMetaData().getURL() + ": " + e.getMessage());
             } catch (SQLException ex) {
                 Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
                 out.println(ex.getMessage());
             }
             out.println(e.getMessage());
        } finally {
             try {
                 if (conAtual != null) conAtual.close();
             } catch (SQLException ex) {
                 Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
                 out.println(ex.getMessage());
             }
        }
    }

    private void atualizarTodosBancosDados(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            PrintWriter out) {
        try {
            String sql = "";
            Connection con = null;
            boolean consultaOamd = true;
            if (nomeHostPG.equals("********")) {
                con = uteisServlet.obterConexao("********", portaPG, "postgres", senhaPG, "OAMD");
                sql = "SELECT * FROM empresa WHERE ativa IS TRUE AND \"usarBDLocal\" IS TRUE  AND \"nomeBD\" LIKE ('bdzillyon%')";
            } else {
                con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "postgres");
                sql = "select * from pg_database where datname like('bdzillyon%') order by datname";
                consultaOamd = false;
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                String nomeBanco = "";
                if (consultaOamd) {
                    nomeBanco = rs.getString("nomeBD");
                    nomeHostPG = rs.getString("hostBD");
                    portaPG = rs.getString("porta");
                    superUserPG = rs.getString("userBD");
                    senhaPG = rs.getString("passwordBD");

                } else {
                    nomeBanco = rs.getString("datname");

                }
                atualizaUmBanco(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco, out);
            }
            con.close();
        } catch (Exception ex) {
            uteisServlet.print(out, ex.getMessage());
        }
    }

    private void executarProcessoTodosBancosDados(final String metodo,
            String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String callable,
            String lgn,
            PrintWriter out) {
        ExecutorService executor = null;
        Connection con = null;
        try {
            String sql = "";
            boolean oamd = true;
            final String prefixo = "bdzillyon";
            if (nomeHostPG.equals("********") || nomeHostPG.equals("********")) {
                con = uteisServlet.obterConexao("********", portaPG, "postgres", senhaPG, "OAMD");
                sql = "SELECT * FROM empresa WHERE ativa IS TRUE AND \"usarBDLocal\" IS TRUE  AND \"nomeBD\" LIKE ('" + prefixo + "%')";
            } else {
                con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "postgres");
                sql = "select * from pg_database where datname like('" + prefixo + "%') order by datname";
                oamd = false;
            }
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            String nomeBanco = "";

            if (callable != null && callable.equals("s")) {
                executor = Executors.newFixedThreadPool(MAX_THREAD_POOL_SIZE);
            }
            Connection conAtual = null;
            while (rs.next()) {
                if (oamd) {
                    nomeBanco = rs.getString("nomeBD");
                    nomeHostPG = rs.getString("hostBD");
                    portaPG = rs.getString("porta");
                    superUserPG = rs.getString("userBD");
                    senhaPG = rs.getString("passwordBD");
                } else {
                    nomeBanco = rs.getString("datname");
                }

                try {
                    //
                    conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco);
                    uteisServlet.print(out, new Date().toString() + " " + conAtual.getMetaData().getURL() + ": Iniciando Processo " + metodo);
                    if (callable != null && callable.equals("s") && executor != null) {
                        Runnable runnable = AtualizadorBD.executarUmProcessoQualquerRunnable(metodo, conAtual);
                        if (runnable != null) {
                            executor.submit(runnable);
                        }
                    } else {
                        AtualizadorBD.executarUmProcessoQualquer(metodo, conAtual);
                    }
                    uteisServlet.print(out, new Date().toString() + " " + conAtual.getMetaData().getURL() + ": Processo " + metodo + " executado com sucesso.");
                    Uteis.logar(null, conAtual.getMetaData().getURL() + ": Processo " + metodo + " executado com sucesso.");
                    //
                } catch (Exception e) {
                    uteisServlet.print(out, conAtual.getMetaData().getURL() + ": " + e.getMessage());
                } finally {
                    if (executor == null) {
                        conAtual.close();
                    }
                }
            }
        } catch (Exception ex) {
            uteisServlet.print(out, ex.getMessage());
        } finally {
            try {
                con.close();
            } catch (Exception ex) {
                Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
            if (executor != null) {
                executor.shutdown();
            }
        }
    }

    private void executarProcesso(final String metodo,
            String nomeHostPG, String portaPG, String superUserPG, String senhaPG,
            String nomeBanco, String callable, final String lgn, PrintWriter out) {
        if (validarToken(lgn)) {
            ExecutorService executor = null;
            try {
                Connection con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG,
                        nomeBanco);

                if (callable != null && callable.equals("s")) {
                    executor = Executors.newFixedThreadPool(MAX_THREAD_POOL_SIZE);
                }

                try {
                    Conexao.guardarConexaoForJ2SE(con);
                    uteisServlet.print(out, new Date().toString() + " " + con.getMetaData().getURL() + ": Iniciando Processo " + metodo);
                    if (callable != null && callable.equals("s") && executor != null) {
                        Runnable runnable = AtualizadorBD.executarUmProcessoQualquerRunnable(metodo, con);
                        if (runnable != null) {
                            executor.submit(runnable);
                        }
                    } else {
                        AtualizadorBD.executarUmProcessoQualquer(metodo, con);
                    }
                    uteisServlet.print(out, new Date().toString() + " " + con.getMetaData().getURL() + ": Processo " + metodo + " executado com sucesso.");
                    Uteis.logar(null, con.getMetaData().getURL() + ": Processo " + metodo + " executado com sucesso.");
                } catch (Exception e) {
                    uteisServlet.print(out, con.getMetaData().getURL() + ": " + e.getMessage());
                } finally {
                    if (executor == null) {
                        con.close();
                    }
                }
            } catch (Exception ex) {
                uteisServlet.print(out, ex.getMessage());
            } finally {
                if (executor != null) {
                    executor.shutdown();
                }
            }
        }
    }

    private void alterarOwnerBancoDadosPostgreSQL(Connection conOrigem,
            String nomeBanco, String novoOwner, PrintWriter out) throws Exception {

        String sql = "ALTER DATABASE \"" + nomeBanco + "\" OWNER TO " + novoOwner;

        SuperFacadeJDBC.executarConsulta(sql, conOrigem);

        uteisServlet.print(out, "Alterou owner do Banco de Dados " + nomeBanco.toUpperCase() + " para " + novoOwner);

        sql = "select * from pg_tables where schemaname in ('public', 'sch_estudio') order by tablename";

        ResultSet rsTables = SuperFacadeJDBC.criarConsulta(sql, conOrigem);
        while (rsTables.next()) {

            String nomeSchema = rsTables.getString("schemaname");
            String nomeTabela = rsTables.getString("tablename");

            SuperFacadeJDBC.executarConsulta(
                    "alter table " + nomeSchema + "." + nomeTabela + " OWNER TO " + novoOwner, conOrigem);

            uteisServlet.print(out, "Alterou owner da tabela " + nomeTabela.toUpperCase() + " para " + novoOwner);


        }

        sql = "select * from pg_views  where schemaname = 'sch_estudio' order by viewname";

        ResultSet rsViews = SuperFacadeJDBC.criarConsulta(sql, conOrigem);
        while (rsViews.next()) {

            String nomeSchema = rsViews.getString("schemaname");
            String nomeView = rsViews.getString("viewname");


            SuperFacadeJDBC.executarConsulta(
                    "alter table " + nomeSchema + "." + nomeView + " OWNER TO " + novoOwner, conOrigem);

            uteisServlet.print(out, "Alterou owner da view " + nomeView.toUpperCase() + " para " + novoOwner);


        }

        String alteraFuncaoEstudio = "ALTER FUNCTION sch_estudio.fc_disponibilidade("
                + "character varying, character varying, character varying, "
                + "timestamp without time zone, timestamp without time zone, integer) "
                + "OWNER TO " + novoOwner;

        SuperFacadeJDBC.executarConsulta(alteraFuncaoEstudio, conOrigem);
        uteisServlet.print(out, "Alterou owner da funcao sch_estudio.fc_disponibilidade");

        String alteraOwnerSchemaStudio = "ALTER SCHEMA sch_estudio OWNER TO " + novoOwner;

        SuperFacadeJDBC.executarConsulta(alteraOwnerSchemaStudio, conOrigem);
        uteisServlet.print(out, "Alterou owner do sch_estudio");

    }

    private void alterarOwnerBancosDados(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG, String nomeBanco,
            String novoOwner, PrintWriter out) {
        try {
            Connection con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG,
                    nomeBanco);

            try {
                alterarOwnerBancoDadosPostgreSQL(con, nomeBanco, novoOwner, out);
            } catch (Exception e) {
                uteisServlet.print(out, con.getMetaData().getURL() + ": " + e.getMessage());
            }

            con.close();
        } catch (Exception ex) {
            uteisServlet.print(out, ex.getMessage());
        }
    }

    private void alterarOwnerTodosBancosDados(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String novoOwner, String except, PrintWriter out) {
        try {
            String sql = "select * from pg_database where datname like('bdzillyon%') order by datname";
            Connection con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "postgres");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            while (rs.next()) {
                String nomeBanco = rs.getString("datname");

                if (uteisServlet.isExcept(nomeBanco, except)) {
                    continue;
                }
                Connection conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG,
                        superUserPG, senhaPG, nomeBanco);
                try {
                    //
                    alterarOwnerBancoDadosPostgreSQL(conAtual, nomeBanco, novoOwner, out);
                    //
                } catch (Exception e) {
                    uteisServlet.print(out, conAtual.getMetaData().getURL() + ": " + e.getMessage());
                } finally {
                    conAtual.close();
                }
            }
            con.close();
        } catch (Exception ex) {
            uteisServlet.print(out, ex.getMessage());
        }
    }

    private boolean validarToken(final String lgn) {
        UsuarioOAMDCacheTO cacheUsuarioOAMD = MAPA_USUARIOS.get(lgn);

        if (cacheUsuarioOAMD != null) {
            Boolean validationReturn = cacheUsuarioOAMD.getValidation();
            if (validationReturn != null) {
                return validationReturn;
            }
        }

        if (lgn == null) {
            return false;
        }

        JSONObject o = new JSONObject(Uteis.desencriptar(lgn, "chave_login_unificado"));
        String userName = o.optString("userName");

        if (userName != null && !userName.isEmpty()) {
            String retorno = OamdWSConsumer.validarUsuarioOamd(userName, o.optString("pwd"));
            Uteis.logarDebug(String.format("Validado LGN-TOKEN for %s", userName));
            boolean validationReturn = "sucesso".equals(retorno);

            cacheUsuarioOAMD = new UsuarioOAMDCacheTO(validationReturn);
            MAPA_USUARIOS.put(lgn, cacheUsuarioOAMD);

            return validationReturn;
        }

        return false;
    }


    private void executarComandoUpdateTodosBancosDados(String nomeHostPG,
                                                       String portaPG, String superUserPG, String senhaPG,
                                                       String comandoSQL, String except, final String prefixBanco, final String lgn, PrintWriter out) {
        if (validarToken(lgn)) {
            Connection conBancos = null;
            try {
                String sql = "";
                boolean oamd = true;
                Connection con = null;
                final String prefixo = prefixBanco != null ? prefixBanco : "bdzillyon";
                if (nomeHostPG.equals("********") || nomeHostPG.equals("********")) {
                    con = uteisServlet.obterConexao("********", portaPG, "postgres", senhaPG, "OAMD");
                    sql = "SELECT * FROM empresa WHERE ativa IS TRUE AND \"usarBDLocal\" IS TRUE  AND \"nomeBD\" LIKE ('" + prefixo + "%')";
                } else {
                    con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "postgres");
                    sql = "select * from pg_database where datname like('" + prefixo + "%') order by datname";
                    oamd = false;
                }
                ResultSet bancos = SuperFacadeJDBC.criarConsulta(sql, con);
                String _nomeBD = "";
                String _hostPG = "";
                String _portaPG = "";
                String _userPG = "";
                String _pwdPG = "";
                while (bancos.next()) {
                    if (oamd) {
                        _nomeBD = bancos.getString("nomeBD");
                        _hostPG = bancos.getString("hostBD");
                        _portaPG = bancos.getString("porta");
                        _userPG = bancos.getString("userBD");
                        _pwdPG = bancos.getString("passwordBD");
                    } else {
                        _nomeBD = bancos.getString("datname");
                        _userPG = superUserPG;
                        _hostPG = nomeHostPG;
                        _portaPG = portaPG;
                        _pwdPG = senhaPG;
                    }
                    if (uteisServlet.isExcept(_nomeBD, except)) {
                        continue;
                    }
                    executarComandoUpdate(_hostPG, _portaPG, _userPG, _pwdPG, _nomeBD, comandoSQL, lgn, out);
                }
            } catch (Exception ex) {
                uteisServlet.print(out, ex.getMessage());
            } finally {
                try {
                    conBancos.close();
                } catch (SQLException ex) {
                    Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }

    private void executarComandoUpdate(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String nomeBanco, String comandoSQL, String lgn, PrintWriter out) {
        if (validarToken(lgn)) {
            try {
                Connection conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG,
                        senhaPG, nomeBanco);
                //
                try {
                    SuperFacadeJDBC.executarConsulta(comandoSQL, conAtual);
                    uteisServlet.print(out, conAtual.getMetaData().getURL() + ": Comando executado com sucesso: " + comandoSQL);
                    Uteis.logarDebug(conAtual.getMetaData().getURL() + ": Comando executado com sucesso: " + comandoSQL);
                } catch (Exception e) {
                    uteisServlet.print(out, conAtual.getMetaData().getURL() + ": " + e.getMessage());
                } finally {
                    conAtual.close();
                }
            } catch (Exception ex) {
                uteisServlet.print(out, ex.getMessage());
            }
        }
    }

    @Deprecated
    private void executarChamadasFinanceiro(String chave, String codEmpresa, String data, PrintWriter out, HttpServletResponse response) throws Exception {
        UpdateServico updateServico = new UpdateServico();
        String nomeHostPG = "localhost";
        String portaPG = "5432";
        String superUserPG = "postgres";
        String senhaPG = "pactodb";
        String formato = "json";

        Conexao conexao = Conexao.getInstance();
        if (!conexao.getUrlOAMD().isEmpty()) {
            String[] urlConexao = conexao.getUrlOAMD().split("/");
            String[] servidor = urlConexao[2].split(":");

            nomeHostPG = servidor[0];
            portaPG = servidor[1];
        }

        Date dataPequisa = Calendario.hoje();
        if (data != null) {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd", Calendario.getDefaultLocale());
            dataPequisa = df.parse(data);
        }

        String comandoSQL = "SELECT\n"
                + "  sum(qtd) AS ativos,\n"
                + "  emp.nome,\n"
                + "  emp.codigo\n"
                + "FROM (SELECT\n"
                + "        count(*) AS qtd,\n"
                + "        c.empresa,\n"
                + "        c.codigo\n"
                + "      FROM contrato c\n"
                + "      WHERE current_date BETWEEN c.vigenciaDe AND c.vigenciaAteAjustada\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('CA', 'DE')\n"
                + "                       AND current_date BETWEEN h.datainiciosituacao AND h.datafinalsituacao)\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   o.contrato\n"
                + "                 FROM contratooperacao o\n"
                + "                 WHERE o.tipooperacao IN ('TR', 'TV')\n"
                + "                       AND current_date >= o.datainicioefetivacaooperacao\n"
                + "                       AND current_date <= o.datafimefetivacaooperacao)\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   contratobaseadorenovacao\n"
                + "                 FROM contrato ct\n"
                + "                 WHERE ct.pessoa = c.pessoa\n"
                + "                       AND current_date BETWEEN ct.vigenciaDe AND ct.vigenciaAteAjustada)\n"
                + "      GROUP BY c.empresa, c.codigo\n"
                + "      UNION\n"
                + "      SELECT\n"
                + "        count(*) AS qtd,\n"
                + "        c.empresa,\n"
                + "        c.codigo\n"
                + "      FROM contrato c\n"
                + "      WHERE c.contratoresponsavelrenovacaomatricula = 0\n"
                + "            AND c.codigo IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('VE')\n"
                + "                       AND current_date BETWEEN h.datainiciosituacao AND h.datafinalsituacao)\n"
                + "            AND c.codigo NOT IN\n"
                + "                (SELECT\n"
                + "                   h.contrato\n"
                + "                 FROM historicocontrato h\n"
                + "                 WHERE h.tipohistorico IN ('DE')\n"
                + "                       AND h.datainiciosituacao BETWEEN '" + Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(dataPequisa)) + "' AND current_date)\n"
                + "      GROUP BY c.empresa, c.codigo) AS foo\n"
                + "  INNER JOIN empresa emp ON foo.empresa = emp.codigo &&&\n"
                + "GROUP BY emp.nome, emp.codigo;";
        try {
            // Validar parâmetros para prevenir SQL injection
            if (codEmpresa != null && !Uteis.isValidInteger(codEmpresa)) {
                throw new SecurityException("Código da empresa inválido: " + codEmpresa);
            }
            if (data != null && !Uteis.isValidDate(data)) {
                throw new SecurityException("Data inválida: " + data);
            }

            if (codEmpresa == null) {
                comandoSQL = comandoSQL.replaceAll("&&&", "");
            } else {
                // Usar parâmetro validado de forma segura
                comandoSQL = comandoSQL.replaceAll("&&&", "AND emp.codigo = " + Integer.parseInt(codEmpresa));
            }

            if (data != null) {
                // Escapar aspas simples na data para prevenir SQL injection
                String dataEscapada = data.replace("'", "''");
                comandoSQL = comandoSQL.replaceAll("current_date", "'" + dataEscapada + "'");
            }

            updateServico.executarConsultaSQLTodosBancosDadosFull(nomeHostPG,
                    portaPG, superUserPG, senhaPG, comandoSQL, null, formato, null, chave,
                    null, out, response, this.getServletContext(), exibirTitulo, seq);
        } catch (JSONException e) {
            e.printStackTrace();
        } finally {
            updateServico = null;
        }
    }

    private void executarConsultaSQLTodosBancosDadosFull(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String comandoSQL, String except, String formato, String prefixoArquivo,
            final String prefixoBanco,
            PrintWriter out, HttpServletResponse response) {
        UpdateServico updateServico = new UpdateServico();
        try {
            if (formato == null) {
                formato = "html";
            }
            updateServico.executarConsultaSQLTodosBancosDadosFull(nomeHostPG,
                    portaPG, superUserPG, senhaPG, comandoSQL, except, formato, null,
                    prefixoArquivo, prefixoBanco, out, response,
                    this.getServletContext(), exibirTitulo, seq);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            updateServico = null;
        }
    }

    private void executarConsultaFull_XML(final String comandoSQL, final String chave,
            final String except, final String prefixoBanco, final String chavesOnly,
            PrintWriter out, HttpServletRequest request, HttpServletResponse response) {
        UpdateServico updateServico = new UpdateServico();
        try {
            if (validarToken(request.getParameter("lgn"))) {
                updateServico.executarConsultaFull_XML(comandoSQL, except, chave,
                        prefixoBanco, chavesOnly, out, request, response);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            updateServico = null;
        }
    }

    private void executarConsultaSQLTodosBancosDados(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String comandoSQL, String except, String formato, String prefixoArquivo,
            final String prefixoBanco, final String chavesOnly, final String lgn,
            PrintWriter out, HttpServletResponse response) {
        UpdateServico updateServico = new UpdateServico();
        try {
            if (formato == null) {
                formato = "html";
            }
            if (validarToken(lgn)) {
                updateServico.executarConsultaSQLTodosBancosDados(nomeHostPG,
                        portaPG, superUserPG, senhaPG, comandoSQL, except, formato,
                        prefixoArquivo, prefixoBanco, chavesOnly, out, response,
                        this.getServletContext(), exibirTitulo, seq);
            }
        } finally {
            updateServico = null;
        }
    }

    private void doDownloadDat(final String fileName, HttpServletResponse response) throws Exception {
        uteisServlet.doDownloadFile(fileName, null, "text/plain", response, this.getServletContext());
    }

    private void doDownloadFile(String fileName, PrintWriter out) {
        uteisServlet.print(out, String.format("<body onload=\"document.location.href='%s';\"></body>", "relatorio/" + fileName));
    }

    private void executarConsultaSQL(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String nomeBanco, String comandoSQL, String formato, String prefixoArquivo, String lgn,
            String appName, PrintWriter out, HttpServletResponse response) {
        UpdateServico updateServico = new UpdateServico();
        try {
            if (validarToken(lgn)) {
                updateServico.executarConsultaSQL(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco, comandoSQL, formato, prefixoArquivo, appName, out, response, this.getServletContext(), exibirTitulo, seq);
            }
        } finally {
            updateServico = null;
        }
    }

    public static String getFullURL(HttpServletRequest request) {
        StringBuffer requestURL = request.getRequestURL();
        String queryString = request.getQueryString();

        if (queryString == null) {
            return requestURL.toString();
        } else {
            return requestURL.append('?').append(queryString).toString();
        }
    }

    private void exportarDadosBancoDados(String nomeHostPG,
            String portaPG, String superUserPG, String senhaPG,
            String nomeBanco, String tables, String formato, final String emailDest, final String lgn,
            PrintWriter out, HttpServletRequest request) {
        if (validarToken(lgn)) {
            if (!UteisValidacao.emptyString(emailDest)) {
                uteisServlet.print(out, String.format("SUCESSO! Você receberá um e-mail em %s com a URL para download quando o arquivo terminar de ser gerado!",
                        emailDest));
                new UteisServletExportData(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco, tables, formato,
                        exibirTitulo, emailDest, getServletContext().getRealPath("relatorio"),
                        getFullURL(request)).start();
            } else {
                uteisServlet.print(out, "ERRO: E-mail de destino do Arquivo a ser exportado deve ser informado!");
            }
        }
    }

    private void exportarDadosBancoDadosSincrono(String nomeHostPG,
                                         String portaPG, String superUserPG, String senhaPG,
                                         String nomeBanco, String tables, String formato, final String emailDest, final String lgn,
                                         PrintWriter out, HttpServletRequest request) {
        if (validarToken(lgn)) {
            UteisServletExportData u =
                new UteisServletExportData(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco, tables, formato,
                        exibirTitulo, emailDest, getServletContext().getRealPath("relatorio"),
                        getFullURL(request));
            u.run();
            out.print(u.getFileNameFull());
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception ex) {
            Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            processRequest(request, response);
        } catch (Exception ex) {
            Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

    private void criarNotificacao(String desc, String dtInicio, String dtFim,
            String tipo, String chave, String localAcesso, final String propagable,
            final String urlRequest, String timeZone) {
        NotificacaoTO notf;
        tipo = tipo == null ? "ZW" : tipo;
        try {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método criarNotificacao ");
            if (tipo.equals("ZW")) {
                NotificadorServiceControle.limpar("ZW");
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss", Calendario.getDefaultLocale());

            TimeZone tz = null;
            if (!UteisValidacao.emptyString(timeZone)) {
                tz = TimeZone.getTimeZone(timeZone);
            }

            notf = new NotificacaoTO(df.parse(dtInicio).getTime(), df.parse(dtFim).getTime(), desc, tipo, chave, localAcesso, tz);
            
            NotificadorServiceControle.adicionarNotificacao(notf);

            if (propagable != null && propagable.equals("s")) {
                propagarNotificacao(desc, dtInicio, dtFim, tipo, chave, localAcesso, urlRequest, timeZone);
            }
        } catch (Exception ex) {
            Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    private void propagarNotificacao(final String desc, final String dtInicio,
                                     final String dtFim, final String tipo, final String chave,
                                     final String localAcesso, final String urlRequest, final String timeZone) {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Entrou no método propagarNotificacao");
        try {
            Map<String, String> mapa = new HashMap();
            mapa.put("op", "createNOTF");
            mapa.put("desc", desc);
            mapa.put("dti", dtInicio);
            mapa.put("dtf", dtFim);
            mapa.put("tipoNOTF", tipo);
            mapa.put("chave", chave);
            mapa.put("localAcesso", localAcesso);
            mapa.put("timeZone", timeZone);

            String[] hostsPortas = lerConfig(PropsService.instanciasNotificar).split(",");
            try {
                URL u = new URL(urlRequest);
                for (String hostPorta : hostsPortas) {
                    final String host = hostPorta.substring(0, hostPorta.indexOf(":"));
                    final String porta = hostPorta.substring(hostPorta.indexOf(":") + 1);
                    try {
                        if ((u.getPort() != Integer.valueOf(porta)) || (!u.getHost().equals(host))) {
                            String protocolToUse = porta.startsWith("280") ? "http" : u.getProtocol();
                            StringBuilder s = new StringBuilder(protocolToUse);
                            s.append("://").append(hostPorta).append(u.getPath());
                            Uteis.logar(null, "NOTIFICANDO ====>> " + s);
                            ExecuteRequestHttpService.executeRequest(s.toString(), mapa);
                        }
                    } catch (Exception ex) {
//                    Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            } catch (MalformedURLException ex) {
                Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        } catch (Exception ex) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Erro no método propagarNotificacao - " + ex.getMessage());
            throw ex;
        } finally {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Finalizou o método propagarNotificacao");
        }
    }

    private void propagarRequest(final Map requestParameMap, final String urlRequest) {
        String[] hostsPortas = lerConfig(PropsService.instanciasNotificar).split(",");
        try {
            URL u = new URL(urlRequest);
            Map tmp = new HashMap(requestParameMap);
            tmp.remove("propagable");
            for (String hostPorta : hostsPortas) {
                final String host = hostPorta.substring(0, hostPorta.indexOf(":"));
                final String porta = hostPorta.substring(hostPorta.indexOf(":") + 1);
                try {
                    if ((u.getPort() != Integer.valueOf(porta)) || (!u.getHost().equals(host))) {
                        StringBuilder s = new StringBuilder(u.getProtocol());
                        s.append("://").append(hostPorta).append(u.getPath());
                        Uteis.logar(null, "PROPAGANDO REQUEST PARA ====>> " + s);
                        Uteis.logar(null, "CONTEUDO ====>> " + requestParameMap.toString());
                        ExecuteRequestHttpService.executeRequest(s.toString(), requestParameMap);
                    }
                } catch (Exception ex) {
//                    Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    private void listNotificacoes(PrintWriter out) {
        List<NotificacaoTO> lista = (List<NotificacaoTO>) ((ArrayList) NotificadorServiceControle.notificacoesAtuaisZW).clone();
        lista.addAll((List<NotificacaoTO>) ((ArrayList) NotificadorServiceControle.notificacoesAtuaisWS).clone());
        StringBuffer sb = new StringBuffer();
        for (NotificacaoTO notificacaoTO : lista) {
            uteisServlet.print(sb, notificacaoTO.toString(), false);
        }
        uteisServlet.print(out, sb.toString());
    }

    private void doGetBanners(PrintWriter out, final String modulo) {
        out.print(SuporteControle.carregarURLBanners(modulo));
    }

    private void doRefreshConfig(PrintWriter out, final String propagable, final HttpServletRequest request) {
        PropsService.refresh(true);
        if (request.getParameter("key") != null) {
            try {
                new DAO().reloadProps(request.getParameter("key"));
            } catch (Exception ex) {
                Logger.getLogger(UpdateServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        if (propagable != null && propagable.equals("s")) {
            propagarRequest(request.getParameterMap(), request.getRequestURL().toString());
        }
        DiscoveryMsService.clearnCache();
        Uteis.clearAuthZwSecret();
        out.print(UpdateServlet.class.getSimpleName() + " -> PropsService reloaded!!!");
    }
    
    private void doSetConfig(PrintWriter out, final String propagable, final HttpServletRequest request) {
        PropsService.refresh(true);
        PropsService.setPropertyValue(request.getParameter("configName"), request.getParameter("configValue"));
        PropsService.save(true);
        if (propagable != null && propagable.equals("s")) {
            propagarRequest(request.getParameterMap(), request.getRequestURL().toString());
        }
        out.print(String.format("UpdateServlet.class.getSimpleName() => Property %s with Value: %s, setting was succssufully!",
                request.getParameter("configName"),
                request.getParameter("configValue")));
    }

    private void doInvalidateIdleSessions(PrintWriter out, final String propagable, final HttpServletRequest request) {
        List<SessionTO> sessions = SessionState.updateList();
        StringBuilder sb = new StringBuilder();
        for (SessionTO sess : sessions) {
            if (sess.isInvalid()) {
                long estimateInactiveTime = Uteis.somarCampoData(sess.getUltAcesso(), Calendar.MINUTE, 120).getTime();
                long systemCurrentTimeMilis = System.currentTimeMillis();

                sb.append("sess.getEmpresa(): ").append(sess.getEmpresa()).append("\n");
                sb.append("sess.getUsuario(): ").append(sess.getUsuario()).append("\n");
                sb.append("estimateInactiveTime: ").append(estimateInactiveTime).append("\n");
                sb.append("systemCurrentTimeMilis: ").append(systemCurrentTimeMilis).append("\n");
                sb.append("Sessão invalidada com sucesso: ").append(sess.getId()).append(sess.getEmpresa());

                Uteis.logar(null, sb.toString());
                sess.getSession().invalidate();
            }
        }
        if (propagable != null && propagable.equals("s")) {
            propagarRequest(request.getParameterMap(), request.getRequestURL().toString());
        }
        out.print(UpdateServlet.class.getSimpleName() + " -> " + sb);
    }


    private void doInvalidateIdleSessionsKey(PrintWriter out, final String propagable, final HttpServletRequest request, String chave, String empresa) {
        List<SessionTO> sessions = SessionState.updateList();
        StringBuilder sb = new StringBuilder();
        Integer empresacod = Integer.valueOf(empresa);
        for (SessionTO sess : sessions) {
            Integer empresaSessao = null;
            try {
                empresaSessao = ((LoginControle) sess.getSession().getAttribute("LoginControle")).getEmpresa().getCodigo();
            }catch (Exception e){}

            if (sess.getSession().getAttribute("key") != null &&
                    sess.getSession().getAttribute("key").equals(chave)
                    && empresaSessao != null && empresaSessao.equals(empresacod)) {
                sb.append("Sessão da chave ")
                .append(chave).append(" empresa ").append(empresa)
                        .append(" invalidada com sucesso: ").append(sess.getId()).append(sess.getEmpresa());
                Uteis.logar(null, sb.toString());
                sess.getSession().invalidate();
            }
        }
        if (propagable != null && propagable.equals("s")) {
            propagarRequest(request.getParameterMap(), request.getRequestURL().toString());
        }
        out.print(UpdateServlet.class.getSimpleName() + " -> " + sb.toString());
    }


    private void doListSessions(PrintWriter out, final HttpServletResponse response, final String propagable,
            final HttpServletRequest request) {
        out.append("[\n");
        UpdateServico updateServico = new UpdateServico();
        try {
            updateServico.listSessions(out, response, request);
            if (propagable != null && propagable.equals("s")) {
                propagarRequestInner("listSessions", out, request.getParameterMap(), request.getRequestURL().toString());
            }
        } finally {
            updateServico = null;
            out.append("]");
        }
    }

    private void doDownloadCache(PrintWriter out, final HttpServletResponse response,
            final HttpServletRequest request) {
        UpdateServico updateServico = new UpdateServico();
        try {
            updateServico.redirecXML(request, response);
        } finally {
            updateServico = null;
        }
    }

    private void doDownloadCatalog(PrintWriter out, final HttpServletResponse response,
                                 final HttpServletRequest request) {
        if (validarToken(request.getParameter("lgn"))) {
            UpdateServico updateServico = new UpdateServico();
            response.setContentType("text/xml;charset=ISO-8859-1");
            updateServico.catalogXML(request, response);
        }
    }

    private void doPurgeCatalog(PrintWriter out, final HttpServletResponse response,
                                   final HttpServletRequest request) {
        if (validarToken(request.getParameter("lgn"))) {
            UpdateServico updateServico = new UpdateServico();
            response.setContentType("text/html;charset=ISO-8859-1");
            updateServico.purgeCatalog(response, Integer.valueOf(request.getParameter("limit")));
        }
    }

    public static void propagarRequestInner(String op, PrintWriter out, final Map requestParameMap, final String urlRequest) {
        try {
            String[] hostsPortas = lerConfig(PropsService.instanciasNotificar).split(",");
            ExecuteRequestHttpService serviceRequest = new ExecuteRequestHttpService();

            URL u = new URL(urlRequest);
            Map tmp = new HashMap(requestParameMap);
            tmp.put("op", op);
            tmp.remove("propagable");
            for (String hostPorta : hostsPortas) {

                final String porta = hostPorta.substring(hostPorta.indexOf(":") + 1);
                try {
                    if (u.getPort() != Integer.valueOf(porta)) {
                        StringBuilder s = new StringBuilder(u.getProtocol());
                        s.append("://").append(hostPorta).append(u.getPath());
                        Uteis.logar(null, "PROPAGANDO REQUEST PARA ==>> " + s);
                        StringBuilder retorno = new StringBuilder("");
                        retorno.append(",");
                        try {
                            StringBuilder retornoServer = new StringBuilder(serviceRequest.executeRequestInner(s.toString(), tmp));
                            retornoServer.deleteCharAt(0);
                            retorno.append(retornoServer.toString());
                        } catch (Exception ignored) {
                            out.append(retorno.deleteCharAt(0));
                        }
                        out.append(retorno.toString());
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (MalformedURLException ex) {
            Logger.getLogger(UpdateServico.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void extractSchemaParquet(String nomeHostPG, String portaPG, String superUserPG, String senhaPG,
                                      String nomeBD, String comandoSQL, final String lgn, PrintWriter out) {
        UpdateServico updateServico = new UpdateServico();
        try {
            if (validarToken(lgn)) {
                updateServico.getParquetSchema(nomeHostPG,
                        portaPG, superUserPG, senhaPG, nomeBD, comandoSQL, out);
            }
        } finally {
            updateServico = null;
        }
    }

    private void clearUsuariosMap() {
        MAPA_USUARIOS.clear();
        Uteis.logarDebug("Cache de usuários (UPDATE SERVLET) resetada!");
    }


}

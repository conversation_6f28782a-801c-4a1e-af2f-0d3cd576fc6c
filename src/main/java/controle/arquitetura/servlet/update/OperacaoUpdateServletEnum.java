/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet.update;

/**
 * <AUTHOR>
 */
public enum OperacaoUpdateServletEnum {
    UPDATE_PG("updatePG"),
    UPDATE_PG_ONE("updatePGONE"),
    UPDATE_ALL("updateALL"),
    UPDATE_ONE("updateONE"),
    FINANCEIRO_PACTO("financeiroPacto"),
    SELECT_FULL_XML("selectFULL_XML"),
    SELECT_ALL("selectALL"),
    SELECT_PARQUET("selectParquet"),
    EXPORT_DATA("exportData"),
    EXPORT_DATA_SYNC("exportDataSync"),
    SELECT_ONE("selectONE"),
    CHANGE_OWNER_ALL("changeOWNERALL"),
    CHANGE_OWNER("changeOWNER"),
    CREATE_NOTF("createNOTF"),
    LIST_NOTF("listNOTF"),
    <PERSON><PERSON>AR_NOTF("clearNOTF"),
    DOWNLOAD_FILE("downloadfile"),
    DOWNLOAD_FILE_DIRETORIO_ARQUIVOS("downloadfilediretorioarquivos"),
    TROCA_EMPRESA("trocaEmpresa"),
    LOGOUT("logout"),
    PROCESS_ONE("processONE"),
    PROCESS_ALL("processALL"),
    URL_BANNERS("urlBanners"),
    REFRESH_CONFIG("refreshConfig"),
    SET_CONFIG("setConfig"),
    INVALIDATE_IDLE_SESSIONS("invalidateIdleSessions"),
    INVALIDATE_IDLE_SESSIONS_KEY("invalidateIdleSessionsKey"),
    LIST_SESSIONS("listSessions"),
    DC("dc"),
    ERROS_FULL("errosFull"),
    DCATALOG("dcatalog"),
    UPDATE_LGN_CATALOG("updatelgncatalog"),
    PURGE_CATALOG("purgecatalog"),
    VERIFICADOR("verificador"),
    GET_LAST_ACTION_TIME("getLastActionTime"),
    SET_LAST_ACTION_TIME("setLastActionTime"),
    ENABLE_DEBUG("enableDebug"),
    UPDATE_BOUNCE("updateBounce"),
    UPDATE_LOGOS("updateLogos"),
    GENERATE_CRYPTO("generateCrypto"),
    LIMPAR_MAPA_REDE_EMPRESA("limparMapaRedeEmpresa"),
    PREENCHER_SCHEMA_PARQUET("preencherSchemaParquet"),
    EXTRACT_SCHEMA_PARQUET("extractSchemaParquet"),
    CLEAR_USER_CACHE_MAP("CLEAR_USER_CACHE_MAP")
    ;

    private final String operacaoString;

    OperacaoUpdateServletEnum(String operacaoString) {
        this.operacaoString = operacaoString;
    }

    public static OperacaoUpdateServletEnum fromString(String operacaoString) {
        for (OperacaoUpdateServletEnum operacaoEnum : OperacaoUpdateServletEnum.values()) {
            if (operacaoEnum.getOperacaoString().equalsIgnoreCase(operacaoString)) {
                return operacaoEnum;
            }
        }
        return null;
    }

    public String getOperacaoString() {
        return operacaoString;
    }
}


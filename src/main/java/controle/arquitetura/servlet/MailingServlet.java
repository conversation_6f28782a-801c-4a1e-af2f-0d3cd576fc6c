package controle.arquitetura.servlet;

import br.com.pactosolucoes.enumeradores.OcorrenciaEnum;
import servicos.operacoes.MailingService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class MailingServlet extends HttpServlet {

    private void print(PrintWriter printer, final String msg) {
        final String tmp = msg.contains("<td>") ? "" : "</br>";
        printer.println(msg + tmp);
    }

    private void print(StringBuilder printer, final String msg) {
        final String tmp = msg.contains("<td>") ? "" : "</br>";
        printer.append(msg).append(tmp);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=ISO-8859-1");
        PrintWriter out = response.getWriter();
        try {

            String chave = request.getParameter("chave");
            String id_mailing = request.getParameter("id_mailing");
            String cliente = request.getParameter("cliente");
            String ocorrencia = request.getParameter("ocorrencia");


            if (chave != null) {

                try (MailingService ms = new MailingService(chave)) {
                    if (id_mailing != null && !id_mailing.isEmpty()) {
                        ms.enviarMalaDireta(Integer.valueOf(id_mailing));
                        print(out, String.format("Iniciando processo do Mailing do ZW... Chave: %s Mailing: %s", chave, id_mailing));
                    }

                    if (cliente != null && !cliente.isEmpty() &&
                            ocorrencia != null && !ocorrencia.isEmpty()) {

                        OcorrenciaEnum ocorrenciaEnum = OcorrenciaEnum.getOcorrencia(Integer.valueOf(ocorrencia));
                        ms.enviarMalaDiretaComOcorrencia(ocorrenciaEnum, Integer.valueOf(cliente));
                        print(out, String.format("Iniciando processo do Mailing do ZW... Chave: %s Cliente: %s Ocorrencia: %s", chave, cliente, ocorrenciaEnum.getDescricao()));
                    }

                } catch (Exception ex) {
                    print(out, String.format("ERRO - chave: %s - ID: %s - %s ", chave, id_mailing, ex.getMessage()));
                    Logger.getLogger(MailingServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
            } else {
                print(out, String.format("MAILING %s da chave %s Não Encontrado!", id_mailing, chave));
            }
        } finally {
            out.close();
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    public String getServletInfo() {
        return "Short description";
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.NFSeEmitida;
import negocio.facade.jdbc.financeiro.NFSeEmitidaHistorico;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
public class WebHookNotaServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        if (request.getParameter("key") != null &&
                request.getParameter("nfseEmitida") != null &&
                request.getParameter("idLote") != null) {

            System.out.println("REQUISIÇÃO WebHookNota: " + request.toString());

            final String chave = request.getParameter("key");
            final String nfseEmitida = request.getParameter("nfseEmitida");
            final String idLote = request.getParameter("idLote");

            JSONObject jsonObject = new JSONObject();

            try {

                if (UteisValidacao.emptyString(chave)) {
                    throw new Exception("key não informada!");
                }

                if (UteisValidacao.emptyString(nfseEmitida)) {
                    throw new Exception("nfseEmitida não informada!");
                }

                if (UteisValidacao.emptyString(idLote)) {
                    throw new Exception("idLote não informada!");
                }

                Connection con = new DAO().obterConexaoEspecifica(chave);

                NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                NFSeEmitidaHistorico nfSeEmitidaHistoricoDAO = new NFSeEmitidaHistorico(con);
                MovConta movContaDAO = new MovConta(con);
                NFSeEmitidaVO nfSeEmitidaVO = nfSeEmitidaDAO.consultarPorChavePrimaria(Integer.parseInt(nfseEmitida));

                if (nfSeEmitidaVO != null) {
                    if (idLote.toLowerCase().equals("excluir")) {
                        nfSeEmitidaDAO.excluir(nfSeEmitidaVO);
                        nfSeEmitidaHistoricoDAO.excluirPorNFSeEmitida(nfSeEmitidaVO);
                        if (nfSeEmitidaVO.getMovConta() != null && nfSeEmitidaVO.getMovConta().getCodigo() > 0) {
                            movContaDAO.desmarcarNFSEEmitida(nfSeEmitidaVO.getMovConta().getCodigo());
                        }
                        System.out.println("REQUISIÇÃO WebHookNota: NFSeEmitida: " + nfSeEmitidaVO.getCodigo() + " EXCLUIDA!");

                    } else if (nfSeEmitidaVO.isNotaFamilia()){ //ATUALIZAR NOTA FAMÍLIA

                        nfSeEmitidaDAO.atualizarSituacaoIdRpsNotaFamilia(SituacaoNotaFiscalEnum.PROCESSADA, Integer.parseInt(idLote), nfSeEmitidaVO.getIdReferencia());
                        System.out.println("REQUISIÇÃO WebHookNota: NOTA EM GRUPO - NFSeEmitida: " + nfSeEmitidaVO.getCodigo() + " ATUALIZADA! IdLote: " + idLote);

                    } else {
                        nfSeEmitidaVO.setIdRps(Integer.parseInt(idLote));
                        nfSeEmitidaVO.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.PROCESSADA);
                        nfSeEmitidaDAO.atualizarSituacaoIdRps(nfSeEmitidaVO);
                        System.out.println("REQUISIÇÃO WebHookNota: NFSeEmitida: " + nfSeEmitidaVO.getCodigo() + " ATUALIZADA! IdLote: " + idLote);
                    }
                } else {
                    throw new Exception("NFSeEmitidaVO não encontrada com o código: " + Integer.parseInt(nfseEmitida));
                }
                jsonObject.put("return", "sucesso");
            } catch (Exception ex) {
                System.out.println("REQUISIÇÃO WebHookNota: ERRO: " + ex.getMessage());
                jsonObject.put("error", ex.getMessage());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }

            response.setContentType("application/json");
            response.getOutputStream().print(jsonObject.toString());
        } else if (request.getParameter("obterCliente") != null) {
            final String chave = request.getParameter("key");
            final String nfseEmitida = request.getParameter("nfseEmitida");

            JSONObject jsonObject = new JSONObject();
            try {
                Connection con = new DAO().obterConexaoEspecifica(chave);
                NFSeEmitida nfseEmitidaDAO = new NFSeEmitida(con);
                NFSeEmitidaVO nfSeEmitidaVO =  nfseEmitidaDAO.consultarPorChavePrimaria(Integer.parseInt(nfseEmitida));

                Pessoa pessoaDAO = new Pessoa(con);
                PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getPessoa() , Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                JSONObject object = new JSONObject();
                object.put("nome", pessoaVO.getNome());
                object.put("cpf", pessoaVO.getCfp());

                if (pessoaVO.getEnderecoVOs().size() > 0) {
                    EnderecoVO enderecoVO = pessoaVO.getEnderecoVOs().get(0);
                    object.put("cep", enderecoVO.getCep());
                    object.put("endereco", enderecoVO.getEndereco());
                    object.put("complemento", enderecoVO.getComplemento());
                    object.put("numero", enderecoVO.getNumero());
                    object.put("bairro", enderecoVO.getBairro());
                }

                if (pessoaVO.getEmailVOs().size() > 0) {
                    EmailVO emailVO = pessoaVO.getEmailVOs().get(0);
                    object.put("email", emailVO.getEmail());
                }

                if (pessoaVO.getTelefoneVOs().size() > 0) {
                    TelefoneVO telefoneVO = pessoaVO.getTelefoneVOs().get(0);
                    object.put("telefone", telefoneVO.getNumero());
                }

                jsonObject.put("return", object);
            } catch (Exception ex) {
                System.out.println("REQUISIÇÃO WebHookNota (Obter Dados): ERRO: " + ex.getMessage());
                jsonObject.put("error", ex.getMessage());
                Logger.getLogger(NFSeServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
            response.setContentType("application/json");
            response.getOutputStream().print(jsonObject.toString());
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}

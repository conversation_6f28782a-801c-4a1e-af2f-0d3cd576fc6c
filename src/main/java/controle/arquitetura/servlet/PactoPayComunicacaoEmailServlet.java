package controle.arquitetura.servlet;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.pactoPay.PactoPayComunicacaoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Lu<PERSON>
 * Date: 25/08/2024
 */
public class PactoPayComunicacaoEmailServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String chave = request.getParameter("chave");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }

            Integer id_mailing = UteisValidacao.converterInteiro(request.getParameter("id_mailing"));
            if (UteisValidacao.emptyNumber(id_mailing)) {
                throw new Exception("id_mailing não informado");
            }

            EnvelopeRespostaDTO envelopeRespostaDTO;
            PactoPayComunicacaoService service;
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                service = new PactoPayComunicacaoService(con);
                service.enviarPactoPayEnvioEmail(id_mailing);
                envelopeRespostaDTO = EnvelopeRespostaDTO.of("sucesso");
            } finally {
                service = null;
            }

            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(envelopeRespostaDTO).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            Logger.getLogger(PactoPayComunicacaoEmailServlet.class.getName()).log(Level.SEVERE, null, ex);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.erro("Erro", ex.getMessage())).toString());
        }
    }
}

package controle.arquitetura.servlet;

import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

public class VerificarAulasDesmarcadasDuplicadas {

    public static Integer totalDuplicados = 0;
    public static Integer totalCodContratoIncorreto = 0;

    public static void main(String ... args) throws Exception {
        try {
            Connection con = DriverManager.getConnection("************************************************************", "postgres", "pactodb");
            String dataInicio = "2022-11-01"; // Ajustar a data para a data de início a ser tratada
            start(con, dataInicio);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void start(Connection con, String dataInicio) throws Exception {
        System.out.println("############## PROCESSO INICIADO ##############\n");
        listarAlunosComAulasDesmarcadasPorPeriodo(con, dataInicio);
        System.out.println("\nTOTAL DUPLICADOS: " + totalDuplicados);
        System.out.println("TOTAL COM CODIGO CONTRATO INCORRETO: " + totalCodContratoIncorreto);
        System.out.println("TOTAL DE AULAS DESMARCADAS COM DIVERGÊNCIA: " + (totalDuplicados + totalCodContratoIncorreto));
        System.out.println("\n############## PROCESSO FINALIZADO ##############");
    }

    public static void listarAlunosComAulasDesmarcadasPorPeriodo(Connection con, String dataInicio) throws Exception {
        String sql =
                "select distinct (c.codigo) as codCliente, p.nome, c.pessoa, c.codigomatricula \n" +
                "from auladesmarcada a \n" +
                "inner join cliente c on c.codigo = a.cliente \n" +
                "inner join pessoa p on p.codigo = c.pessoa \n" +
                "where dataorigem >= '" + dataInicio + "' \n" +
                "order by p.nome \n";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        while (rs.next()) {
            listarAulasDesmarcadasAluno(con, dataInicio, rs.getInt("codCliente"), rs.getString("nome"), rs.getInt("codigomatricula"), rs.getInt("pessoa"));
        }
    }

    public static void listarAulasDesmarcadasAluno(Connection con, String dataInicio, Integer codCliente, String nome, Integer matricula, Integer pessoa) throws Exception {
        // utiliza origemsistema = 4 pois era a origem do app que estava salvando o código do contrato incorreto quando era contrato concomitante
        String sql =
                "select * from auladesmarcada a \n" +
                "where cliente = " + codCliente + " \n" +
                "and datareposicao is null \n" +
                "and dataorigem >= '" + dataInicio + "' \n" +
                "and origemsistema = 4";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        Map<String, Map<String, String>> mapaParaAjuste = new HashMap<String, Map<String, String>>();
        while (rs.next()) {
            String dataOrigem = Calendario.getData(rs.getDate("dataorigem"), "yyyy-MM-dd");
            if (isPeriodoContratoConcomitante(con, dataOrigem, pessoa)) {
                verificarAulasDesmarcadasIncorretas(con, mapaParaAjuste, dataOrigem, codCliente, rs.getInt("turma"),
                        rs.getInt("horarioturma"), nome, matricula, rs.getInt("codigo"), rs.getInt("contrato"), pessoa, rs.getInt("origemsistema"));
            }
        }
        if (mapaParaAjuste.size() > 0) {
            System.out.println("\n## " + mapaParaAjuste.size() + " aulas desmarcadas com divergências." + "    Nome: " + nome + " - MAT: " + matricula + " - Pessoa: " + pessoa + " - Cliente: " + codCliente);
            for (String item : mapaParaAjuste.keySet()) {
                if (mapaParaAjuste.get(item).get("tipoDivergencia").equals("duplicado")) {
                    System.out.println("#### DUPLICADO: Codigo:" + mapaParaAjuste.get(item).get("codAulaDesmarcada") + "; Data_Origem: " + mapaParaAjuste.get(item).get("dataOrigem") + "; Turma:" + mapaParaAjuste.get(item).get("turma") + "; Horario_Turma:" + mapaParaAjuste.get(item).get("horarioturma"));
                    totalDuplicados++;

                    SuperFacadeJDBC.executarConsulta("DELETE FROM auladesmarcada WHERE codigo = " + mapaParaAjuste.get(item).get("codAulaDesmarcada"), con);
                    System.out.println("####### REMOVIDO!");
                } else if (mapaParaAjuste.get(item).get("tipoDivergencia").equals("cod_contrato_incorreto")) {
                    System.out.println("#### COD_CONTRATO_INCORRETO: Codigo:" + mapaParaAjuste.get(item).get("codAulaDesmarcada") + "; Data_Origem: " + mapaParaAjuste.get(item).get("dataOrigem") + "; Turma:" + mapaParaAjuste.get(item).get("turma") + "; Horario_Turma:" + mapaParaAjuste.get(item).get("horarioturma") + "; Contrato_Incorreto: " + mapaParaAjuste.get(item).get("contrato_incorreto") + "; Contrato_Correto: " + mapaParaAjuste.get(item).get("contrato_correto"));
                    totalCodContratoIncorreto++;

                    Integer codContratoCorreto = Integer.valueOf(mapaParaAjuste.get(item).get("contrato_correto"));
                    Integer codAulaDesmarcada = Integer.valueOf(mapaParaAjuste.get(item).get("codAulaDesmarcada"));
                    if (codContratoCorreto != null && !codContratoCorreto.equals(0)) {
                        SuperFacadeJDBC.executarConsulta("UPDATE auladesmarcada SET contrato = " + codContratoCorreto + " where codigo = " + codAulaDesmarcada, con);
                        System.out.println("####### ATUALIZADO!");
                    }
                }
            }
        }
    }

    public static void verificarAulasDesmarcadasIncorretas(Connection con, Map<String, Map<String, String>> mapaParaAjuste,
                                                           String dataOrigem, Integer cliente, Integer turma, Integer horarioTurma,
                                                           String nome, Integer matricula, Integer codigoAulaDesmarcada, Integer contrato, Integer pessoa, Integer origem) throws Exception {
        if (isDuplicado(con, dataOrigem, cliente, turma, horarioTurma)) {
            String sql =
                "select ad.* from auladesmarcada ad \n" +
                "where ad.cliente = " + cliente + " \n" +
                "and ad.datareposicao is null \n" +
                "and ad.dataorigem = '" + dataOrigem + "' \n" +
                "and ad.turma = " + turma + " \n" +
                "and ad.horarioturma = " + horarioTurma + " \n" +
                "and ad.origemsistema = 4 \n" +
                "and ad.codigo not in ( \n" +
                "   select ad2.codigo from auladesmarcada ad2 \n" +
                "   inner join contrato ct on ct.codigo = ad2.contrato \n" +
                "   inner join contratomodalidade cm on cm.contrato = ct.codigo \n" +
                "   inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo and cmt.turma = " + turma + " \n" +
                "   where ad2.dataorigem = '" + dataOrigem + "' \n" +
                "   and ad2.datareposicao is null \n" +
                "   and ad2.turma = " + turma + " \n" +
                "   and ad2.horarioturma = " + horarioTurma + " \n" +
                "   and ad2.cliente = " + cliente + " \n" +
                ")";

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

            while (rs.next()) {
                Map<String, String> item = new HashMap<String, String>();
                item.put("codAulaDesmarcada", rs.getInt("codigo") + "");
                item.put("dataOrigem", rs.getDate("dataorigem") + "");
                item.put("contrato", rs.getInt("contrato") + "");
                item.put("turma", rs.getInt("turma") + "");
                item.put("horarioturma", rs.getInt("horarioturma") + "");
                item.put("aluno", nome);
                item.put("matricula", matricula.toString());
                item.put("tipoDivergencia", "duplicado");
                item.put("origem", rs.getInt("origemsistema") + "");
                mapaParaAjuste.put(cliente + "_" +rs.getInt("codigo"), item);
            }
        } else if (isCodContratoIncorreto(con, dataOrigem, cliente, turma, horarioTurma)) {
            Integer codContratoCorreto = obterCodContratoCorreto(con, dataOrigem, turma, pessoa);
            if (codContratoCorreto != null && codContratoCorreto > 0) {
                Map<String, String> item = new HashMap<String, String>();
                item.put("codAulaDesmarcada", codigoAulaDesmarcada.toString());
                item.put("dataOrigem", dataOrigem);
                item.put("contrato_incorreto", contrato.toString());
                item.put("contrato_correto", codContratoCorreto.toString());
                item.put("turma", turma.toString());
                item.put("horarioturma", horarioTurma.toString());
                item.put("aluno", nome);
                item.put("matricula", matricula.toString());
                item.put("tipoDivergencia", "cod_contrato_incorreto");
                item.put("origem", origem.toString());
                mapaParaAjuste.put(cliente + "_" + codigoAulaDesmarcada, item);
            }
        }
    }

    public static Boolean isDuplicado(Connection con, String dataOrigem, Integer cliente, Integer turma, Integer horarioTurma) throws Exception {
        String sqlDuplicado =
                "select count(ad.*) as qtd from auladesmarcada ad \n" +
                "where ad.cliente = " + cliente + " \n" +
                "and ad.datareposicao is null \n" +
                "and ad.dataorigem = '" + dataOrigem + "' \n" +
                "and ad.turma = " + turma + " \n" +
                "and ad.horarioturma = " + horarioTurma + " \n";

        ResultSet rsDuplicado = SuperFacadeJDBC.criarConsulta(sqlDuplicado, con);

        if (rsDuplicado.next()) {
            if (rsDuplicado.getInt("qtd") >= 2) {
                return true;
            }
        }

        return false;
    }

    public static Boolean isCodContratoIncorreto(Connection con, String dataOrigem, Integer cliente, Integer turma, Integer horarioTurma) throws Exception {
        String sqlCodContratoIncorreto =
                "select count(ad2.*) as qtd from auladesmarcada ad2 \n" +
                "inner join contrato ct on ct.codigo = ad2.contrato \n" +
                "inner join contratomodalidade cm on cm.contrato = ct.codigo \n" +
                "inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo and cmt.turma = " + turma + " \n" +
                "where ad2.dataorigem = '" + dataOrigem + "' \n" +
                "and ad2.datareposicao is null \n" +
                "and ad2.turma = " + turma + " \n" +
                "and ad2.horarioturma = " + horarioTurma + " \n" +
                "and ad2.cliente = " + cliente + " \n" +
                "and ad2.origemsistema = 4";

        ResultSet rsCodContratoIncorreto = SuperFacadeJDBC.criarConsulta(sqlCodContratoIncorreto, con);

        if (rsCodContratoIncorreto.next()) {
            if (rsCodContratoIncorreto.getInt("qtd") == 0) {
                return true;
            }
        }

        return false;
    }

    public static Integer obterCodContratoCorreto(Connection con, String dataOrigem, Integer turma, Integer pessoa) throws Exception {
        String sqlCodContratoIncorreto =
                "select ct.codigo from contrato ct \n" +
                "inner join contratomodalidade cm on cm.contrato = ct.codigo \n" +
                "inner join contratomodalidadeturma cmt on cmt.contratomodalidade = cm.codigo \n" +
                "inner join contratomodalidadehorarioturma cmht on cmht.contratomodalidadeturma = cmt.codigo \n" +
                "where ct.pessoa = " + pessoa + " \n" +
                "and cmt.turma = " + turma + " \n" +
                "and '" + dataOrigem + "' between ct.vigenciade and ct.vigenciaateajustada ";

        ResultSet rsCodContratoCorreto = SuperFacadeJDBC.criarConsulta(sqlCodContratoIncorreto, con);

        if (rsCodContratoCorreto.next()) {
            return rsCodContratoCorreto.getInt("codigo");
        }

        return 0;
    }

    public static Boolean isPeriodoContratoConcomitante(Connection con, String dataOrigem, Integer pessoa) throws Exception {
        String sql =
                "select exists ( \n" +
                "   select pessoa, count(pessoa) from contrato ct \n" +
                "   inner join contratomodalidade cm on cm.contrato = ct.codigo \n" +
                "   where pessoa = " + pessoa + " \n" +
                "   and '" + dataOrigem + "' between vigenciade and vigenciaateajustada \n" +
                "   group by pessoa having count(pessoa) > 1 \n" +
                ")";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        if (rs.next()) {
            return rs.getBoolean("exists");
        }

        return false;
    }

}

package controle.arquitetura.security;

/**
 * Descrição: Contrato a ser implementado por controladores que façam uso de {@link AutorizacaoFuncionalidadeControle}
 * Projeto: ZillyonWeb-T
 *
 * <AUTHOR> em 20/fev/2018 às 11:49
 * Pacto Soluções - Todos os direitos reservados
 */
public abstract class AutorizacaoFuncionalidadeListener {

    //Objeto passado para o NavigationHandler após a autorização com sucesso. <br>
    // https://docs.jboss.org/richfaces/latest/vdldoc/a4j/commandButton.html
    private String paginaDestino = null;


    //Instruções executadas ao completar a autorização
    private String executarAoCompletar = "";

    /**
    * Método executado quando a autorização é feita com sucesso
    */
    public abstract void onAutorizacaoComSucesso() throws Exception;

    /**
     * Executado quando há algum erro durante a validação da autenticação.<br>
     *     Veja {@link AutorizacaoFuncionalidadeControle#invoke}
     */
    public void onAutorizacaoComErro(Exception e){
        //
    }

    /**
     * Executado quando o usuário clica no botão para fechar o modal
     */
    public void onFecharModalAutorizacao() {
        //
    }

    public String getPaginaDestino() {
        return paginaDestino;
    }

    public void setPaginaDestino(String paginaDestino) {
        this.paginaDestino = paginaDestino;
    }

    public String getExecutarAoCompletar() {
        return executarAoCompletar;
    }

    public void setExecutarAoCompletar(String executarAoCompletar) {
        this.executarAoCompletar = executarAoCompletar;
    }
}

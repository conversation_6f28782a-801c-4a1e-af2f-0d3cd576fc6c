/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.security;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpSession;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
public class LogoutControle extends SuperControle {

    private boolean deveEfetuarLogout = false;
    private String url;

    public String getURL() throws Exception {
        if (url == null) {
            String kSession = (String) JSFUtilities.getFromSession("key");
            String urlLogin = (String) JSFUtilities.getFromSession("urlLogin");
            String path = request().getContextPath() + "/";
            String retorno;
            Object vindoLogin = JSFUtilities.getFromSession("vindoLogin");
            if (urlLogin != null && !urlLogin.equals("") &&
                    (urlLogin.toLowerCase().contains("oamd") || urlLogin.toLowerCase().contains("apoio"))) {
                boolean origemApoio = urlLogin.toLowerCase().contains("apoio");
                String URL_OAMD_EMPRESAS = (String) JSFUtilities.getFromSession("URL_OAMD_EMPRESAS");

                JSFUtilities.storeOnSession("urlOamd", UteisValidacao.emptyString(URL_OAMD_EMPRESAS) ?
                        getUrlOamdEmpresas() :
                        (URL_OAMD_EMPRESAS + (origemApoio ? "" : "/empresas")));
            }
            if (vindoLogin != null) {
//                String urlLoginDeslogar = (urlLogin + "/usuario/deslogar?k=" + kSession);
                String urlLoginDeslogar = (urlLogin + "/logout");
                if (getUsuarioLogado() != null && (getIntegracaoNovoLogin() && isNotBlank(getUsuarioLogado().getUsuarioGeral()))) {
                    try {
                        urlLoginDeslogar = (urlLogin + "/logout");
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
                JSFUtilities.storeOnSession("urlLoginDeslogar", urlLoginDeslogar);
            }
            if (kSession != null && !kSession.isEmpty()) {
                String sufixo = path + "faces/inicio.jsp?key=" + kSession;
                retorno = String.format(path + "UpdateServlet?op=logout&ru=%s",
                        Uteis.encriptar(sufixo + "(" + Uteis.getData(Calendario.hoje(), "ddMMyyyy"), "URLs:ZW"));
            } else {
                String sufixo = path + "faces/inicio.jsp";
                retorno = String.format(path + "UpdateServlet?op=logout&ru=%s",
                        Uteis.encriptar(sufixo + "(" + Uteis.getData(Calendario.hoje(), "ddMMyyyy"), "URLs:ZW"));
            }
            url = retorno;

        }
        return url;
    }

    public String getRedirectLogout() {
        String retorno = "";
        try {
            retorno = getURL();
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }

        return retorno;
    }

    public boolean isDeveEfetuarLogout() {
        return deveEfetuarLogout;
    }

    public void setDeveEfetuarLogout(boolean deveEfetuarLogout) {
        this.deveEfetuarLogout = deveEfetuarLogout;
    }

    public void updateCookiesLogado(){
        JSFUtilities.addCookie(new Cookie(JSFUtilities.COOKIE_LOGADO, "false"));
    }

    public void doLogout() {
        HttpSession httpSession = (HttpSession) context().getExternalContext().getSession(false);
        httpSession.invalidate();
        JSFUtilities.removeCookie(JSFUtilities.COOKIE_CREDENTIALS_FAILOVER);
        JSFUtilities.recuperarCookie(JSFUtilities.COOKIE_KEY);
    }
}

package controle.arquitetura.security;

public class UsuarioOAMDCacheTO {

    private static final long HORAS_ADICIONAR = 24L * 3600000;
    private final long expirationDate;
    private final Boolean validation;

    public UsuarioOAMDCacheTO(boolean validation) {
        this.validation = validation;
        this.expirationDate = System.currentTimeMillis() + HORAS_ADICIONAR;
    }

    public Boolean getValidation() {
        if (System.currentTimeMillis() < expirationDate) {
            return validation;
        }
        return null;
    }
}

package controle.arquitetura.security;

import org.json.JSONObject;

public class UsuarioTreinoDTO {
    private Integer id;
    private String username;
    private String nome;
    private PerfilUsuarioTreinoDTO perfilUsuario;

    public UsuarioTreinoDTO(JSONObject json) {
        JSONObject user = json.getJSONObject("user");
        this.id = user.getInt("id");
        this.username = user.getString("username");
        this.nome = user.getString("nome");
        this.perfilUsuario = new PerfilUsuarioTreinoDTO(json.getJSONObject("perfilUsuario"));
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public PerfilUsuarioTreinoDTO getPerfilUsuario() {
        return perfilUsuario;
    }

    public void setPerfilUsuario(PerfilUsuarioTreinoDTO perfilUsuario) {
        this.perfilUsuario = perfilUsuario;
    }
}

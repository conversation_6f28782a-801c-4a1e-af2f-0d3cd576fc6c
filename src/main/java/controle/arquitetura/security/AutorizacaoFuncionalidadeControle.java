/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.security;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Controlador genérico responsável pela autorização de permissões na aplicação.<br>
 *     Seu principal método é o {@link AutorizacaoFuncionalidadeControle#autorizar(String, String, String, String, AutorizacaoFuncionalidadeListener)}
 * <AUTHOR>
 */
public class AutorizacaoFuncionalidadeControle extends SuperControle {

    private String titulo;
    private String nomePermissao;
    private String mensagemUsuario;

    // Componentes a serem re-renderizados em resposta do clique nos botões fechar ou autorizar
    private String renderComponents;
    private String onComplete;
    private boolean finalizarAutorizacaoSemExibirModal;

    // Propriedade que define se o modal deve ser exibido ou não
    private boolean pedirPermissao = false;
    private UsuarioVO usuario;
    private AutorizacaoFuncionalidadeListener listener;
    private boolean forcarValidacaoUsuarioDiferenteLogado = false;

    /**
     * Principal método do controlador. Reponsável por receber parâmetros para configuração do modal e execução da autorização.
     *
     * @param titulo Título a ser apresentado no modal
     * @param nomePermissao Permissão requerida pela aplicação
     * @param mensagemUsuario Mensagem com a descrição da permissão solicitada
     * @param renderComponents Componentes que devem ser re-renderizados em resposta do clique nos botões 'Fechar' e 'Gravar'
     * @param listener Objeto que ouve as ações executadas pelo modal: 'Autorização com Sucesso', 'Autorização com Erro', etc...
     * @return String que define a página de destino após a autorização com sucesso. Veja {@link 'AutorizacaoFuncionalidadeListener#paginaDestino'}
     */
    public String autorizar(final String titulo, final String nomePermissao, final String mensagemUsuario,
                            final String renderComponents, AutorizacaoFuncionalidadeListener listener) {
        return autorizar(titulo, nomePermissao, mensagemUsuario, renderComponents, false, listener);
    }

    public String autorizar(final String titulo, final String nomePermissao, final String mensagemUsuario,
                          final String renderComponents, boolean escaparValidacao, AutorizacaoFuncionalidadeListener listener) {
        this.titulo = titulo;
        this.nomePermissao = nomePermissao;
        this.mensagemUsuario = mensagemUsuario;
        this.renderComponents = renderComponents + ", panelAutorizacaoFuncionalidade";
        this.listener = listener;
        this.finalizarAutorizacaoSemExibirModal = false;
        this.onComplete = "";

        setMensagemID(null);
        //Verificar se existe a permissão::: existindo, chama o invoke direto
        try {
            this.usuario = (UsuarioVO) super.getUsuarioLogado().getClone(false);
            setForcarValidacaoUsuarioDiferenteLogado(false);
            if (escaparValidacao || !getUsuario().isPedirSenhaFuncionalidade()) {
                return invoke(false, escaparValidacao);
            } else {
                this.pedirPermissao = true;// Ativa a renderização do modal para solicitar a senha
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
            montarErro(e);
        }
        return null;
    }

    /**
     * Executado quando o usuário clica no botão 'Fechar' do modal
     * @return
     * @throws Exception
     */
    public String fechar() {
        pedirPermissao = false;
        setMensagemDetalhada("", "");
        try {
            listener.onFecharModalAutorizacao();
        } catch (Exception ignored) {}
        return "";
    }

    public void consultarUsuario() {
        try {
            setUsuario(getFacade().getUsuario().consultarPorChavePrimaria(
                    getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getUsuario().setUserOamd(getUsuarioLogado().getUserOamd());
            setForcarValidacaoUsuarioDiferenteLogado(UteisValidacao.notEmptyNumber(getUsuario().getCodigo()) && !getUsuario().getCodigo().equals(getUsuarioLogado().getCodigo()));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarUsuarioPorUserName() {
        try {
            setUsuario(getFacade().getUsuario().consultarPorUsername(
                    getUsuario().getUsername(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setForcarValidacaoUsuarioDiferenteLogado(UteisValidacao.notEmptyNumber(getUsuario().getCodigo()) && !getUsuario().getCodigo().equals(getUsuarioLogado().getCodigo()));
            getUsuario().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarPermissao() throws Exception {
        UsuarioVO usuarioVO;
        if (getUsuario().isPedirSenhaFuncionalidade() || !UteisValidacao.emptyString(getUsuario().getSenha().toUpperCase()) || forcarValidacaoUsuarioDiferenteLogado) {
            usuarioVO = getFacade().getControleAcesso().verificarLoginUsuario(
                    getUsuario().getCodigo(),
                    getUsuario().getSenha().toUpperCase());
        } else {
            usuarioVO = getUsuario();
            usuarioVO.setUsuarioPerfilAcessoVOs(getFacade().getUsuarioPerfilAcesso().consultarUsuarioPerfilAcessoEmpresaAtiva(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
        }
        permissaoFuncionalidade(usuarioVO, nomePermissao, mensagemUsuario);
        getUsuario().setPorcetagemDescontoContrato(usuarioVO.getPorcetagemDescontoContrato());
    }
    public String invoke() {
        return invoke(true, false);
    }

    public String invoke(boolean exibiuModal, boolean escaparValidacao) {
        setMensagemID(null);
        try {
            if (!escaparValidacao) {
                validarPermissao();
            }
            pedirPermissao = false;
            listener.onAutorizacaoComSucesso();
            finalizarAutorizacaoSemExibirModal = !exibiuModal;
            this.onComplete = listener.getExecutarAoCompletar();
            return listener.getPaginaDestino();
        } catch (Exception ex) {
            pedirPermissao = true;
            finalizarAutorizacaoSemExibirModal = false;
            Logger.getLogger(AutorizacaoFuncionalidadeControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
            try {
                listener.onAutorizacaoComErro(ex);
            }catch (Exception e){
                Logger.getLogger(AutorizacaoFuncionalidadeControle.class.getName()).log(Level.SEVERE, null, e);
            }

        }
        return null;
    }

    public UsuarioVO getUsuario() throws Exception {
        if (usuario == null) {
            usuario = (UsuarioVO) super.getUsuarioLogado().getClone(false);
        }
        return usuario;
    }

    public void pedirPermissaoFalso(){
        pedirPermissao = false;
    }

    public String getMensagemUsuario() {
        return mensagemUsuario;
    }

    public void setMensagemUsuario(String mensagemUsuario) {
        this.mensagemUsuario = mensagemUsuario;
    }

    public String getNomePermissao() {
        return nomePermissao;
    }

    public void setNomePermissao(String nomePermissao) {
        this.nomePermissao = nomePermissao;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getRenderComponents() {
        return renderComponents;
    }

    public void setRenderComponents(String renderComponents) {
        this.renderComponents = renderComponents;
    }

    public boolean isPedirPermissao() {
        return pedirPermissao;
    }

    public void setPedirPermissao(boolean pedirPermissao) {
        this.pedirPermissao = pedirPermissao;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public String finalizarAutorizacao(){
        this.finalizarAutorizacaoSemExibirModal = false;
        return this.listener.getPaginaDestino();
    }

    public boolean isFinalizarAutorizacaoSemExibirModal(){
        return finalizarAutorizacaoSemExibirModal;
    }

    public String getOnComplete() {
        return onComplete == null ? "" : onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public boolean isForcarValidacaoUsuarioDiferenteLogado() {
        return forcarValidacaoUsuarioDiferenteLogado;
    }

    public void setForcarValidacaoUsuarioDiferenteLogado(boolean forcarValidacaoUsuarioDiferenteLogado) {
        this.forcarValidacaoUsuarioDiferenteLogado = forcarValidacaoUsuarioDiferenteLogado;
    }
}

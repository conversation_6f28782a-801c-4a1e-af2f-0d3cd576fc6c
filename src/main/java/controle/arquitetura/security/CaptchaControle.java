package controle.arquitetura.security;

import br.com.pactosolucoes.comuns.util.MediaData;
import controle.arquitetura.SuperControle;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Random;

public class CaptchaControle extends SuperControle {

    private String valor = "";
    private String palavraSecreta = "";

    public final static int NUM_TENTATIVAS_INVALIDAS = 3;
    public final static String MSG_ERRO_CODIGO_SEGURANCA = "Código de segurança não confere";

    private MediaData mediaData;


    public CaptchaControle() throws Exception {
    }

    public boolean validarCodigo() {
        return getPalavraSecreta().equalsIgnoreCase(valor);
    }

    public void paintCaptcha(OutputStream out, Object data) throws IOException {
        if (data instanceof MediaData) {

            MediaData paintData = (MediaData) data;
            BufferedImage img = new BufferedImage(paintData.getWidth(), paintData.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D graphics2D = img.createGraphics();
            graphics2D.setBackground(paintData.getBackground());
            graphics2D.setColor(paintData.getDrawColor());
            graphics2D.clearRect(0, 0, paintData.getWidth(), paintData.getHeight());
            graphics2D.drawLine(0, 0, paintData.getWidth(), paintData.getHeight());
            graphics2D.drawLine(paintData.getWidth(), (paintData.getHeight() / 3), (paintData.getWidth() / 3), paintData.getHeight());

            gerarPalavraSecreta();
            graphics2D.drawChars(getPalavraSecreta().toCharArray(), 0, 4, 35, 25);
            ImageIO.write(img, "jpeg", out);
        }
    }

    public void gerarPalavraSecreta() {
        String palavraSecreta = "";

        for (int i = 0; i < 4; i++) {
            int numero = new Random().nextInt(10);
            palavraSecreta += numero;
        }

        setPalavraSecreta(palavraSecreta);
    }

    public int getNumTentativasInvalidas() {
        return NUM_TENTATIVAS_INVALIDAS;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public MediaData getMediaData() {
        if (mediaData == null) {
            mediaData = new MediaData();
        }
        return mediaData;
    }

    public void setMediaData(MediaData mediaData) {
        this.mediaData = mediaData;
    }

    public String getPalavraSecreta() {
        return palavraSecreta;
    }

    public void setPalavraSecreta(String palavraSecreta) {
        this.palavraSecreta = palavraSecreta;
    }
}

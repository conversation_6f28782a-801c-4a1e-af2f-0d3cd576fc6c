package controle.arquitetura.security;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class RecursoTreinoDTO {
    private String recurso;
    private List<String> tipoPermissoes;

    public RecursoTreinoDTO(JSONObject json) {
        this.recurso = json.getString("recurso");
        this.tipoPermissoes = new ArrayList<>();
        JSONArray jsonArray = json.getJSONArray("tipoPermissoes");
        for (int i = 0; i < jsonArray.length(); i++) {
            this.tipoPermissoes.add(jsonArray.getString(i));
        }
    }

    public static List<RecursoTreinoDTO> fromJsonArray(JSONArray json) {
        List<RecursoTreinoDTO> recursoTreinoDTOS = new ArrayList<>();
        for (int i = 0; i < json.length(); i++) {
            recursoTreinoDTOS.add(new RecursoTreinoDTO(json.getJSONObject(i)));
        }
        return recursoTreinoDTOS;
    }

    public String getRecurso() {
        return recurso;
    }

    public void setRecurso(String recurso) {
        this.recurso = recurso;
    }

    public List<String> getTipoPermissoes() {
        return tipoPermissoes;
    }

    public void setTipoPermissoes(List<String> tipoPermissoes) {
        this.tipoPermissoes = tipoPermissoes;
    }
}

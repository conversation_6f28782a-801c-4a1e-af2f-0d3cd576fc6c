/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.security;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.oamd.controle.basico.InicioControle;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Classe usada especificamente para recuperar a senha utilizando uma conexão específica
 *
 * <AUTHOR>
 */
public class RecuperacaoSenhaControle extends SuperControle {

    private boolean abrirModalRecuperacaoSenha = false;
    private PessoaVO pesRecuperacaoSenhaVO;
    private String mensagemRecuperaSenha;

    public RecuperacaoSenhaControle() {
        setPesRecuperacaoSenhaVO(new PessoaVO());
    }

    /**
     * Método que abre o modal de recuperação de senha
     */
    public void abrirModalRecuperacaoSenha() {
        setAbrirModalRecuperacaoSenha(true);
        setMensagemID("msg_dados_usuario");
        setPesRecuperacaoSenhaVO(new PessoaVO());
        setSucesso(false);
        setErro(false);
        setAtencao(true);
        setMensagemRecuperaSenha("");
    }

    public void validarDadosRecuperacaoSenha() throws Exception {
        //validar CPF e data de nascimento
        if ("".equals(pesRecuperacaoSenhaVO.getCfp())) {
            throw new Exception("Informe o cpf");
        }
        if (pesRecuperacaoSenhaVO.getDataNasc() == null) {
            throw new Exception("Informe a data de nascimento");
        }
        if (pesRecuperacaoSenhaVO.getDataNasc().after(Calendario.hoje())) {
            throw new Exception("A data de nascimento deve ser menor que a data atual");
        }
        if (!SuperVO.verificaCPF(pesRecuperacaoSenhaVO.getCfp())) {
            throw new Exception("Informe um cpf válido");
        }
    }

    public boolean validarSenhaExpiradaParaEnvioEmail(UsuarioVO usuario, Connection conEspecifica) throws Exception {
        if (usuario.getDataUltimaAlteracaoSenha() != null) {
            //consulta a quantidade de dias limite da configuração do sistema
            ConfiguracaoSistema confFacade = new ConfiguracaoSistema(conEspecifica);
            Integer qtdDiasExpirarSenha = confFacade.buscarQtdDiasParaExpirarSenha();
            //analisa a data que irá expirar a senha
            Date dataExpiracao = Uteis.obterDataFutura2(usuario.getDataUltimaAlteracaoSenha(), qtdDiasExpirarSenha);
            //seta a quantidade de dias que falta para expirar
            long qtdDiasFaltaExpirar = Uteis.nrDiasEntreDatas(Calendario.hoje(), dataExpiracao);
            if (qtdDiasFaltaExpirar <= 0) {
                return true;
            }
        } else {
            throw new Exception("A data da última alteração de senha desse usuário deve ser informada.");
        }
        return false;
    }

    /**
     * Recurso criado para enviar email com nova senha ao usuário
     * OBS.: não usar getFacade() neste método devido a conexão que deve ser usada
     * somente neste e depois fechada
     *
     * @throws SQLException
     */
    public void enviarEmailRecuperacaoSenha() throws SQLException {
        Connection conEspecifica = null;
        Email emailFacade = null;
        Usuario usFacade = null;
        Pessoa pessoaFacade = null;

        CaptchaControle captchaControle = (CaptchaControle) getControlador(CaptchaControle.class);
        try {
            if (!captchaControle.validarCodigo()) {
                throw new Exception(CaptchaControle.MSG_ERRO_CODIGO_SEGURANCA);
            }
            //validar dados informados
            validarDadosRecuperacaoSenha();
            //cria uma conexão a partir da chave
            InicioControle inicioControle = (InicioControle) context().getExternalContext().getSessionMap().get(InicioControle.class.getSimpleName());
            if (inicioControle == null) {
                inicioControle = new InicioControle();
            }
            String uKey = inicioControle.getUserKey();
            DAO dao = new DAO();
            conEspecifica = dao.obterConexaoEspecifica(uKey);

            //Tentar localizar uma pessoa com os dados informados
            pessoaFacade = new Pessoa(conEspecifica);
            List<Integer> lista = pessoaFacade.consultarPorDataNascECPF(pesRecuperacaoSenhaVO.getCfp(),
                    Uteis.getDataJDBC(pesRecuperacaoSenhaVO.getDataNasc()));
            if (lista.isEmpty()) {
                throw new Exception("Não foi encontrada pessoa com os dados informados.");
            } else if (lista.size() > 1) {
                throw new Exception("Email não enviado. Há mais de uma pessoa com Data de Nascimento e CPF informados.");
            } else {
                //Validar se essa pessoa encontrada possui um e-mail válido
                emailFacade = new Email(conEspecifica);
                List<EmailVO> listaEmails = emailFacade.consultarEmails(lista.get(0), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (listaEmails.isEmpty()) {
                    throw new Exception("Nenhum e-mail encontrado para esse usuário.");
                } else {
                    String listaEmailsValidos = "";
                    boolean possuiEmailsValidos = false;
                    for (EmailVO email : listaEmails) {
                        if (UteisValidacao.validaEmail(email.getEmail())) {
                            possuiEmailsValidos = true;
                        }
                    }
                    if (!possuiEmailsValidos) {
                        throw new Exception("Email(s) inválido(s)");
                    } else {
                        usFacade = new Usuario(conEspecifica);
                        UsuarioVO usuarioVO = usFacade.consultarPorCodigoPessoa(lista.get(0), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (usuarioVO.getCodigo().intValue() != 0) {
                            //Validar se a senha do usuário já expirou
                            if (!usuarioVO.getAdministrador()) {
                                if (validarSenhaExpiradaParaEnvioEmail(usuarioVO, conEspecifica)) {
                                    throw new Exception("A senha está expirada. Não será possível recuperar a senha.");
                                }
                            }
                            if (usuarioVO.getColaboradorVO().getSituacao().equals("NA")) {
                                throw new Exception("Usuário desativado. Não será possível recuperar sua senha.");
                            }
                            //Se encontrado o usuário, gerar uma senha numérica randômica e enviar para o e-mail cadastrado
                            Integer senhaNova = UteisValidacao.gerarNumeroRandomico(10000, 50000);
                            for (EmailVO email : listaEmails) {
                                if (UteisValidacao.validaEmail(email.getEmail())) {
                                    UteisValidacao.enviarEmail(montarMensagemEmailRecuperacaoSenha(usuarioVO.getUsername(), senhaNova),
                                            email.getEmail(), usuarioVO.getNome(),
                                            "Recuperação de Senha - ZillyonWeb");
                                    listaEmailsValidos += listaEmailsValidos.isEmpty() ? "" + email.getEmail() : ", " + email.getEmail();
                                }
                            }
                            usuarioVO.setDataUltimaAlteracaoSenha(Calendario.hoje());
                            usuarioVO.setSenha(Integer.toString(senhaNova));
                            usFacade.alterarSenhaUsuario(usuarioVO, true);
                            //Informar ao usuário que uma senha nova foi gerada e esta foi enviada para o seu e-mail cadastrado
                            setMensagemID("msg_enviar_email");
                            setMensagemRecuperaSenha("Uma nova senha foi gerada. E-mail(s) enviado(s) para: " + listaEmailsValidos);
                        } else {
                            throw new Exception("Usuário não encontrado!");
                        }
                    }
                }
                setAtencao(false);
                setSucesso(true);
                setErro(false);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            setAtencao(false);
        } finally {
            if (conEspecifica != null && !conEspecifica.isClosed()) {
                conEspecifica.close();
                conEspecifica = null;
                //limpando os facades
                pessoaFacade = null;
                usFacade = null;
                emailFacade = null;
                Conexao.storeOnSession(null);
            }
        }
    }

    public StringBuffer montarMensagemEmailRecuperacaoSenha(String nomeDestinatario, Integer novaSenha) {
        StringBuffer texto = new StringBuffer();
        texto.append("<p><center><b>Solicitação de Recuperação de Senha</b></center></p>");
        texto.append("Olá ").append(nomeDestinatario).append(", <br/><br/>");
        texto.append("Foi solicitada a recuperação de senha, através do ZillyonWeb.<br/>");
        texto.append("Abaixo está sua nova senha.<br>");
        texto.append("Caso não tenha sido você, alguém pode estar tentanto fraudar sua conta de usuário.<br>");

        texto.append("<br><br><b>Nova Senha: </b>");
        texto.append(novaSenha).append("<br><br>");
        texto.append("<br><br><b>IP Solicitante: </b>").append(this.getIpCliente()).append("<br>");

        return texto;
    }

    /**
     * Método de fechar modal de recuperação de senha
     */
    public void fecharPanelRecuperacaoSenha() {
        setAbrirModalRecuperacaoSenha(false);
        setMensagemID("msg_entre_prmlogin");
        setAtencao(false);
        setSucesso(false);
        setErro(false);
        setMensagemRecuperaSenha("");
    }

    /**
     * @return the abrirModalRecuperacaoSenha
     */
    public boolean isAbrirModalRecuperacaoSenha() {
        return abrirModalRecuperacaoSenha;
    }

    /**
     * @param abrirModalRecuperacaoSenha the abrirModalRecuperacaoSenha to set
     */
    public void setAbrirModalRecuperacaoSenha(boolean abrirModalRecuperacaoSenha) {
        this.abrirModalRecuperacaoSenha = abrirModalRecuperacaoSenha;
    }

    /**
     * @return the pesRecuperacaoSenhaVO
     */
    public PessoaVO getPesRecuperacaoSenhaVO() {
        return pesRecuperacaoSenhaVO;
    }

    /**
     * @param pesRecuperacaoSenhaVO the pesRecuperacaoSenhaVO to set
     */
    public void setPesRecuperacaoSenhaVO(PessoaVO pesRecuperacaoSenhaVO) {
        this.pesRecuperacaoSenhaVO = pesRecuperacaoSenhaVO;
    }

    /**
     * @return the mensagemRecuperaSenha
     */
    public String getMensagemRecuperaSenha() {
        return mensagemRecuperaSenha;
    }

    /**
     * @param mensagemRecuperaSenha the mensagemRecuperaSenha to set
     */
    public void setMensagemRecuperaSenha(String mensagemRecuperaSenha) {
        this.mensagemRecuperaSenha = mensagemRecuperaSenha;
    }
}

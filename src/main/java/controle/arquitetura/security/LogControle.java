package controle.arquitetura.security;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.SuperControle;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * usuarioForm.jsp usuarioCons.jsp) com as funcionalidades da classe <code>Usuario</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Usuario
 * @see UsuarioVO
 */
public class LogControle extends SuperControle {

    protected LogVO logVO;
    protected String nomeEntidade;
    protected Date dataInicio;
    protected Date dataFim;
    private List<LogVO> listaCampos;
    /**
     * Interface <code>UsuarioInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public LogControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        setNomeEntidade("");
        getControleConsulta().setCampoConsulta("nomeEntidade");
    }

    /**
     * Metodo Listener que controla a paginacao via banco implementadada no
     * metodo consultar da DAO "Log.java"
     *
     * Autor: Pedro Y. Saito
     * Criado em 29/12/2010
     *
     */
    @SuppressWarnings("unchecked")
	public void consultarPaginadoListener(ActionEvent evt) {
		try {
//			//Obtendo qual pagina deverá ser exibida
//	    	Object component = evt.getComponent().getAttributes().get("pagNavegacao");
//	    	if (component != null && !"".equals(component.toString())){
//	    		getConfPaginacao().setPagNavegacao(component.toString());
//	    	}
//
//	    	Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
//			if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())){
//				if (compPaginaInicial.toString().equals("paginaInicial")){
//					setConfPaginacao(new ConfPaginacao());
//				}
//	    	}
	    	
			consultarPaginado();
		} catch (Exception e) {
			setListaConsulta(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
    }

	private void consultarPaginado() throws Exception {
        try{
         //   super.consultar();

            List objs = getFacade().getLog().consultarSemLimitacao(getControleConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");

        }catch (Exception e){
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
	}
    
	/**
	 * Rotina responsavel por executar as consultas disponiveis no JSP UsuarioCons.jsp. Define o tipo de consulta a ser executada, por meio
	 * de ComboBox denominado campoConsulta, disponivel neste mesmo JSP. Como resultado, disponibiliza um List com os objetos selecionados
	 * na sessao da pagina.
	 */
	@SuppressWarnings("unchecked")
	public String consultar() {
		try {
			super.consultar();
			List objs = new ArrayList();

			// Inicio - Versao anterior
			// if (getControleConsulta().getCampoConsulta().equals("nomeEntidade")) {
			// objs = getFacade().getLog().consultarPorNomeEntidade(getControleConsulta().getValorConsulta(), false,
			// Uteis.NIVELMONTARDADOS_TODOS);
			// }
			// if (getControleConsulta().getCampoConsulta().equals("responsavel")) {
			// objs = getFacade().getLog().consultarPorResponsavel(getControleConsulta().getValorConsulta(), false,
			// Uteis.NIVELMONTARDADOS_TODOS);
			// }
			// if (getControleConsulta().getCampoConsulta().equals("operacao")) {
			// objs = getFacade().getLog().consultarPorOpercao(getControleConsulta().getValorConsulta(), false,
			// Uteis.NIVELMONTARDADOS_TODOS);
			// }
			// if (getControleConsulta().getCampoConsulta().equals("conteudoLog")) {
			// objs = getFacade().getLog().consultarPorConteudoLog(getControleConsulta().getValorConsulta(), false,
			// Uteis.NIVELMONTARDADOS_TODOS);
			// }
			// Fim - Versao anterior

			//------------------------------------------------------------------------------------------------------------------------------
			
			objs = getFacade().getLog().consultar(getControleConsulta(), false, Uteis.NIVELMONTARDADOS_TODOS);
						
			//------------------------------------------------------------------------------------------------------------------------------
			 
			objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
			definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
			
			setListaConsulta(objs);
			setMensagemID("msg_dados_consultados");
			return "consultar";
		} catch (Exception e) {
			setListaConsulta(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
			return "consultar";
		}
	}

    public void visualizarLog() throws Exception {
        LogVO obj = (LogVO) context().getExternalContext().getRequestMap().get("controleLog");
        setLogVO(obj);

        setListaCampos(getFacade().getLog().consultarApartirDoResponsavelDataAlteracao(obj.getResponsavelAlteracao(), obj.getDataHoraAlteracao_Apresentar().replace(" ás ", " "), Uteis.NIVELMONTARDADOS_TODOS));
    }

    public void visualizarLog2(){
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            LogVO obj = getFacade().getLog().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setLogVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro",e.getMessage());
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>UsuarioVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getLog().excluir(getLogVO());
            setLogVO(new LogVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomeEntidade", "Nome Entidade"));
        itens.add(new SelectItem("responsavel", "Responsável"));
        itens.add(new SelectItem("operacao", "Operação"));
        itens.add(new SelectItem("conteudoLog", "Conteúdo do Log"));
        return itens;
    }

    /**

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    /**
     * @return the logVO
     */
    public LogVO getLogVO() {
        return logVO;
    }

    /**
     * @param logVO the logVO to set
     */
    public void setLogVO(LogVO logVO) {
        this.logVO = logVO;
    }

    /**
     * @return the nomeEntidade
     */
    public String getNomeEntidade() {
        return nomeEntidade;
    }

    /**
     * @param nomeEntidade the nomeEntidade to set
     */
    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    /**
     * @return the dataInicio
     */
    public Date getDataInicio() {
        return dataInicio;
    }

    /**
     * @param dataInicio the dataInicio to set
     */
    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    /**
     * @return the dataFim
     */
    public Date getDataFim() {
        return dataFim;
    }

    /**
     * @param dataFim the dataFim to set
     */
    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }
    /**
	 * @param showLog the showLog to set
	 */
	public void setShowLog(Boolean showLog) {
		this.showLog = showLog;
	}

	/**
	 * @return the showLog
	 */
	public Boolean getShowLog() {
		if(showLog == null){
			showLog = Boolean.FALSE;
		}
		return showLog;
	}
	/**
	 * @param nrTotalConsulta the nrTotalConsulta to set
	 */
	public void setNrTotalConsulta(Integer nrTotalConsulta) {
		this.nrTotalConsulta = nrTotalConsulta;
	}

	/**
	 * @return the nrTotalConsulta
	 */
	public Integer getNrTotalConsulta() {
		return nrTotalConsulta;
	}

	/**
	 * @param nrPagina the nrPagina to set
	 */
	public void setNrPagina(Integer nrPagina) {
		this.nrPagina = nrPagina;
	}

	/**
	 * @return the nrPagina
	 */
	public Integer getNrPagina() {
		return nrPagina;
	}
    
    //--------------------------------------- CENTRAL DE EVENTOS ---------------------------------------------//
    
    
	

	private Integer nrPagina;
	private Integer nrTotalConsulta;
	private Boolean showLog;
    
    /**
     * Responsável por 
     * <AUTHOR>
     * 25/03/2011
     * @param evt
     * @throws Exception 
     */
    public void entidadeListener(ActionEvent evt) throws Exception {
    	String nomeEntidade;
    	Object entidade = evt.getComponent().getAttributes().get("nomeEntidade");
    	if(entidade == null){
    		nomeEntidade = "";
    	}else
    		nomeEntidade = entidade.toString();
    	this.setListaConsulta(getFacade().getLog().consultarPorNomeCodigoEntidadeAgrupado(
    			nomeEntidade, 0, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, true));
    	autorizacao(evt);
    	limparMsg();
    	try{
    		this.verificarAutorizacao();
    		this.setShowLog(Boolean.TRUE);
    		this.setMensagemID("log.sucesso");
    		
    	}catch (Exception e) {
    		this.setMensagemID("msg_erro");
			this.setMensagemDetalhada(e.getMessage());
			this.setShowLog(Boolean.FALSE);
		}
    }
    
    /**
     * Responsável por efetuar a consulta de logs sem parametros, para a visualizacao
     * <AUTHOR>
     * 31/03/2011
     * @return
     */
    public String exibirLogCE(){
    	setConfPaginacao(new ConfPaginacao());
    	setControleConsulta(new ControleConsulta());
    	try {
			consultarPaginado();
		} catch (Exception e) {
			setListaConsulta(new ArrayList());
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	return "visualizarLog";
    	
    }


    public List<LogVO> getListaCampos() {
        if (listaCampos == null){
            listaCampos = new ArrayList<LogVO>(0);
        }
        return listaCampos;
    }

    public void setListaCampos(List<LogVO> listaCampos) {
        this.listaCampos = listaCampos;
    }


}
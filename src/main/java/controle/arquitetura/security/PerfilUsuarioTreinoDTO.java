package controle.arquitetura.security;

import org.json.JSONObject;

import java.util.List;

public class PerfilUsuarioTreinoDTO {
    private String nome;
    private List<RecursoTreinoDTO> recursos;
    private List<FuncionalidadeTreinoDTO> funcionalidades;

    public PerfilUsuarioTreinoDTO(JSONObject json) {
        this.nome = json.getString("nome");
        this.recursos = RecursoTreinoDTO.fromJsonArray(json.getJSONArray("recursos"));
        this.funcionalidades = FuncionalidadeTreinoDTO.fromJsonArray(json.getJSONArray("funcionalidades"));
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<RecursoTreinoDTO> getRecursos() {
        return recursos;
    }

    public void setRecursos(List<RecursoTreinoDTO> recursos) {
        this.recursos = recursos;
    }

    public List<FuncionalidadeTreinoDTO> getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(List<FuncionalidadeTreinoDTO> funcionalidades) {
        this.funcionalidades = funcionalidades;
    }
}

package controle.arquitetura;

import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AvisoInternoDTO;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MenuAvisosControle extends SuperControle {

    private List<SelectItem> itensAcesso = new ArrayList<>();
    private List<SelectItem> itensPerfil = new ArrayList<>();
    private Integer codigo;
    private Integer itemAcesso = 1;
    private Integer itemPerfil = 0;
    private String aviso  = "";
    private String nomeUsuario  = "";
    private Date avisoAte = null;
    private List<UsuarioVO> usuarios = new ArrayList<>();
    private List<Integer> usuariosSelecionados = new ArrayList<>();
    private List<PerfilAcessoVO> perfisSelecionados = new ArrayList<>();

    public MenuAvisosControle(){

    }

    public List<SelectItem> getItensAcesso() {
        if(itensAcesso.isEmpty()){
            itensAcesso.add(new SelectItem(1, "Todos da empresa"));
            itensAcesso.add(new SelectItem(2, "Pessoas específicas"));
            itensAcesso.add(new SelectItem(3, "Perfil de acesso específico"));
        }
        return itensAcesso;
    }

    public List<UsuarioVO> executarAutocompleteUsuario(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);

            } else {
                result = (ArrayList<UsuarioVO>) getFacade().getUsuario().consultarPorNome(pref, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            }
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        for(UsuarioVO user : new ArrayList<>(result)){
            if(usuariosSelecionados.contains(user.getCodigo())){
                result.remove(user);
            }
        }
        return result;
    }

    public void novoAviso(){
        try {
            this.codigo = null;
            this.aviso = "";
            this.avisoAte = null;
            this.itemAcesso = 1;
            this.usuarios = new ArrayList<>();
            this.perfisSelecionados = new ArrayList<>();
            montarPerfis();
            this.nomeUsuario = "";
            this.usuariosSelecionados = new ArrayList<>();
        }catch (Exception e){
            Uteis.logar(e, MenuAvisosControle.class);
        }
    }

    private void montarPerfis() throws Exception {
        this.itemPerfil = 0;
        this.itensPerfil = new ArrayList<>();
        List<PerfilAcessoVO> perfis = getFacade().getPerfilAcesso().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        itensPerfil.add(new SelectItem(0, ""));
        INNER: for (PerfilAcessoVO perfil : perfis){
            for(PerfilAcessoVO perfilSelecionado : perfisSelecionados){
                if(perfil.getCodigo().equals(perfilSelecionado.getCodigo())){
                    continue INNER;
                }
            }
            itensPerfil.add(new SelectItem(perfil.getCodigo(), perfil.getNome()));
        }
    }

    private void obterAvisos() {
        LoginControle loginControle = (LoginControle) getControlador(LoginControle.class.getSimpleName());
        loginControle.obterAvisosInternos();
    }

    public void gravarAviso(){
        try {
            setMsgAlert("");
            if(codigo == null){
                getFacade().getPerfilAcesso().inserirAvisoInterno(getEmpresaLogado().getCodigo(),
                        aviso, getUsuarioLogado().getCodigo(), getAvisoAte(), perfisSelecionados, usuarios);
            } else {
                AvisoInternoDTO avisoDTO = new AvisoInternoDTO();
                avisoDTO.setCodigo(codigo);
                avisoDTO.setAviso(aviso);
                avisoDTO.setDataAte(getAvisoAte());
                getFacade().getPerfilAcesso().atualizarAvisoInterno(avisoDTO, perfisSelecionados, usuarios);
            }
            this.obterAvisos();
            montarSucesso("");
            setMensagem("Aviso salvo com sucesso!");
            setMsgAlert(getMensagemNotificar(true) + "; Richfaces.hideModalPanel('modalGravarAviso');");
        }catch (Exception e){
            montarErro(e.getMessage());
        }
    }

    public void selecionarUsuario() throws Exception {
        UsuarioVO user = (UsuarioVO) request().getAttribute("result");
        usuariosSelecionados.add(user.getCodigo());
        usuarios.add(user);
        nomeUsuario = "";
    }

    public void removerUsuario() throws Exception {
        UsuarioVO user = (UsuarioVO) request().getAttribute("usu");
        usuariosSelecionados.remove(user.getCodigo());
        for(UsuarioVO usuario : new ArrayList<>(usuarios)){
            if(usuario.getCodigo().equals(user.getCodigo())){
                usuarios.remove(usuario);
            }
        }
    }

    public void removerPerfil() throws Exception {
        PerfilAcessoVO per = (PerfilAcessoVO) request().getAttribute("per");
        for(PerfilAcessoVO perfil : new ArrayList<>(perfisSelecionados)){
            if(perfil.getCodigo().equals(per.getCodigo())){
                perfisSelecionados.remove(perfil);
            }
        }
        montarPerfis();
    }

    public void selecionarPerfil() throws Exception {
        if(itemPerfil > 0){
            PerfilAcessoVO perfil = getFacade().getPerfilAcesso().consultarPorChavePrimaria(itemPerfil, Uteis.NIVELMONTARDADOS_MINIMOS);
            perfisSelecionados.add(perfil);
            montarPerfis();
        }
    }

    public void editarAviso() throws Exception {
        setCodigoByRequest();
        AvisoInternoDTO aviso = getFacade().getPerfilAcesso().getAvisoInternoPorCodigo(codigo);
        this.codigo = aviso.getCodigo();
        this.aviso = aviso.getAviso();
        this.avisoAte = aviso.getDataAte();
        if(aviso.getVisivelParaTodos() == true) {
            this.itemAcesso = 1;
        } else if(aviso.getVisivelParaTodos() == false && !aviso.getUsuarios().isEmpty()) {
            this.itemAcesso = 2;
            this.usuarios.clear();
            for(Integer usuario : aviso.getUsuarios()){
                UsuarioVO usu = getFacade().getUsuario().consultarPorCodigoUsuario(usuario, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (usu != null) {
                    this.usuarios.add(usu);
                }
            }

        } else if (aviso.getVisivelParaTodos() == false && !aviso.getPerfis().isEmpty()) {
            this.itemAcesso = 3;
            this.perfisSelecionados.clear();
            for(Integer perfilAcesso : aviso.getPerfis()){
                PerfilAcessoVO perfil = getFacade().getPerfilAcesso().consultarPorChavePrimaria(perfilAcesso, Uteis.NIVELMONTARDADOS_MINIMOS);
                if(perfil != null){
                    this.perfisSelecionados.add(perfil);
                }
            }
        }
        this.obterAvisos();
        montarPerfis();
    }

    public void deletarAviso(){
        try {
            setCodigoByRequest();
            getFacade().getPerfilAcesso().deletarAvisoInterno(codigo);
            montarSucesso("");
            setMensagem("Aviso deletado com sucesso!");
        }catch (Exception e){
            montarErro("");
            setMensagem("Erro ao remover aviso!");
        }
        this.obterAvisos();
        setMsgAlert(getMensagemNotificar(true));
    }

    private void setCodigoByRequest() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        this.codigo = Integer.valueOf(request.getParameter("codigo"));
    }

    public void setItensAcesso(List<SelectItem> itensAcesso) {
        this.itensAcesso = itensAcesso;
    }

    public Integer getItemAcesso() {
        return itemAcesso;
    }

    public void setItemAcesso(Integer itemAcesso) {
        this.itemAcesso = itemAcesso;
    }

    public String getAviso() {
        return aviso;
    }

    public void setAviso(String aviso) {
        this.aviso = aviso;
    }

    public Date getAvisoAte() {
        return avisoAte;
    }

    public void setAvisoAte(Date avisoAte) {
        this.avisoAte = avisoAte;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public List<UsuarioVO> getUsuarios() {
        return usuarios;
    }

    public void setUsuarios(List<UsuarioVO> usuarios) {
        this.usuarios = usuarios;
    }

    public List<SelectItem> getItensPerfil() {
        return itensPerfil;
    }

    public void setItensPerfil(List<SelectItem> itensPerfil) {
        this.itensPerfil = itensPerfil;
    }

    public Integer getItemPerfil() {
        return itemPerfil;
    }

    public void setItemPerfil(Integer itemPerfil) {
        this.itemPerfil = itemPerfil;
    }

    public List<Integer> getUsuariosSelecionados() {
        return usuariosSelecionados;
    }

    public void setUsuariosSelecionados(List<Integer> usuariosSelecionados) {
        this.usuariosSelecionados = usuariosSelecionados;
    }

    public List<PerfilAcessoVO> getPerfisSelecionados() {
        return perfisSelecionados;
    }

    public void setPerfisSelecionados(List<PerfilAcessoVO> perfisSelecionados) {
        this.perfisSelecionados = perfisSelecionados;
    }
}

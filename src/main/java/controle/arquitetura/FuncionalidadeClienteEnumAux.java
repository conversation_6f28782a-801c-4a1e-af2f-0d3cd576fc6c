package controle.arquitetura;

public class FuncionalidadeClienteEnumAux implements Cloneable {

    private FuncionalidadeClienteEnum funcionalidadeClienteEnum;
    private String descricao;
    private String url;
    private String expressaoRenderizar;
    private String[] palavrasChaves;

    @Override
    public FuncionalidadeClienteEnumAux clone(){
        try {
            return (FuncionalidadeClienteEnumAux) super.clone();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public FuncionalidadeClienteEnum getFuncionalidadeClienteEnum() {
        return funcionalidadeClienteEnum;
    }

    public void setFuncionalidadeClienteEnum(FuncionalidadeClienteEnum funcionalidadeClienteEnum) {
        this.funcionalidadeClienteEnum = funcionalidadeClienteEnum;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getUrl() {
        if((url == null || url.trim().length() == 0) && getFuncionalidadeClienteEnum() != null){
            url = getFuncionalidadeClienteEnum().getUrl();
        }
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getExpressaoRenderizar() {
        return expressaoRenderizar;
    }

    public void setExpressaoRenderizar(String expressaoRenderizar) {
        this.expressaoRenderizar = expressaoRenderizar;
    }

    public String[] getPalavrasChaves() {
        return palavrasChaves;
    }

    public void setPalavrasChaves(String[] palavrasChaves) {
        this.palavrasChaves = palavrasChaves;
    }
}

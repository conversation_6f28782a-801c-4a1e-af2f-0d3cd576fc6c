/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura;

import java.io.Serializable;
import javax.faces.event.ActionEvent;

/**
 *
 * <AUTHOR>
 */
public class SkinControle implements Serializable{

    private String skin;

    public SkinControle(){
        this.skin = "glassX";
    }

    public String getSkin() {
        return skin;
    }

    public void setSkin(String skin) {
        this.skin = skin;
    }

    public void definirSkinCrm(ActionEvent evt){
        setSkin("glassX");        
    }

    public void definirSkinFinanceiro(ActionEvent evt){
        setSkin("glassX");        
    }
    public void definirSkinZillyon(ActionEvent evt){
        setSkin("glassX");         
    }
}

package controle.arquitetura;

import servicos.propriedades.PropsService;

public class PrintScreenControle extends SuperControle {

    private boolean gerarPrintsAcoesUsuario;
    private int limitePrintsAcoesUsuario;

    public boolean isGerarPrintsAcoesUsuario() {
        String propGerarPrints = PropsService.getPropertyValue(PropsService.GERAR_PRINTS_ACOES_USUARIO);
        if(propGerarPrints != null && propGerarPrints.toLowerCase().trim().equals("true")){
            this.gerarPrintsAcoesUsuario = true;
        }else{
            this.gerarPrintsAcoesUsuario = false;
        }

        return this.gerarPrintsAcoesUsuario;
    }

    public int getLimitePrintsAcoesUsuario(){
        try{
            this.limitePrintsAcoesUsuario = Integer.parseInt(PropsService.getPropertyValue(PropsService.LIMITE_PRINTS_ACOES_USUARIO));
        }catch (NumberFormatException e){
            this.limitePrintsAcoesUsuario = 0;
        }

        return this.limitePrintsAcoesUsuario;
    }

    public void setGerarPrintsAcoesUsuario(boolean gerarPrintsAcoesUsuario) {
        this.gerarPrintsAcoesUsuario = gerarPrintsAcoesUsuario;
    }

    public void setLimitePrintsAcoesUsuario(int limitePrintsAcoesUsuario) {
        this.limitePrintsAcoesUsuario = limitePrintsAcoesUsuario;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servico;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class VerificarProfessoresInativadosTreino {
    

    

    public static void procurarPorProfessoresInconsistentes(Connection conBancoTreino, Connection conBancoZW) throws Exception {
        ResultSet rsProfessorSint = SuperFacadeJDBC.criarConsulta("select codigocolaborador, ativo, nome from professorsintetico", conBancoTreino);
        while(rsProfessorSint.next()){
            ResultSet rsColaborador = SuperFacadeJDBC.criarConsulta(" SELECT situacao FROM colaborador " +
                    "where codigo = "+rsProfessorSint.getInt("codigocolaborador"), conBancoZW);
            if(rsColaborador.next()){
                if(rsProfessorSint.getBoolean("ativo") && !rsColaborador.getString("situacao").equals("AT")){
                    System.out.println(rsProfessorSint.getString("nome") + " - tr:"+rsProfessorSint.getBoolean("ativo") 
                            + " - zw:"+rsColaborador.getString("situacao"));
                    SuperFacadeJDBC.executarConsulta("update professorsintetico set ativo = false "
                            + " where codigocolaborador = "+rsProfessorSint.getInt("codigocolaborador"), conBancoTreino);
                }
            }
        }
    }

    public static void main(String ... args) throws Exception {
        Map<String, Map<String, String>> unidades = new HashMap<String, Map<String, String>>();
        Map<String, String> quads = new HashMap<String, String>();
        quads.put("zw", "***********************************************");
        quads.put("tr", "********************************************");
        unidades.put("quads", quads);
        

        for(String u : unidades.keySet()){
            System.out.println(u);
            Connection conzw = DriverManager.getConnection(unidades.get(u).get("zw"), "postgres", "pactodb");
            Connection contr = DriverManager.getConnection(unidades.get(u).get("tr"), "postgres", "pactodb");
            procurarPorProfessoresInconsistentes(contr, conzw);
        }
    }
    
}

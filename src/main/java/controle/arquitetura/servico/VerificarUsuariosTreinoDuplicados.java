/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servico;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class VerificarUsuariosTreinoDuplicados {
    
    private static String chave = "sportmg";
    private static String servidor = "localhost";
    
    public static void main(String ... args){
        try {
            Connection oamd = DriverManager.getConnection("jdbc:postgresql://"+servidor+":5432/OAMD2", "postgres", "pactodb");
            ResultSet rsoamd2 = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa "+(UteisValidacao.emptyString(chave) ? "" : ("where chave = '"+chave+"'")), oamd);
            while(rsoamd2.next()){
                 Connection conTR = DriverManager.getConnection("jdbc:postgresql://"+rsoamd2.getString("hostBD")+":"+
                         rsoamd2.getString("porta")+"/"+rsoamd2.getString("nomeBD"), "postgres", "pactodb");
                 verificarDuplicados(conTR);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static void verificarDuplicados(Connection contr){
        try {
            ResultSet rsDuplicados = SuperFacadeJDBC.criarConsulta("select username from usuario u\n" +
                                              "GROUP BY username\n" +
                                              "having count(codigo) > 1", contr);
            int cont = 0;
            while(rsDuplicados.next()){
                ResultSet rsCodUser = SuperFacadeJDBC.criarConsulta("select u.codigo, cli.matricula, cli.nome from usuario u"
                        + " inner join clientesintetico cli on cli.codigo = u.cliente_codigo"
                        + " where u.nome = '"
                        +rsDuplicados.getString("username")+"'", contr);
                while(rsCodUser.next()){
                    ResultSet rsAud = SuperFacadeJDBC.criarConsulta("select * from usuario_aud  "
                                                      + " where codigo = "+rsCodUser.getInt("codigo")
                                                      + " and nome <> '"+rsDuplicados.getString("username")
                                                      + "' order by rev desc limit 1", contr);
                    if(rsAud.next()){
                        String update = "UPDATE usuario SET codigoexterno = ?, nome = ?, senha = ?, "
                                + "username = ? where codigo = ?";
                        PreparedStatement stm = contr.prepareStatement(update);
                        int i = 1;
                        stm.setString(i++, rsAud.getString("codigoexterno"));
                        stm.setString(i++, rsAud.getString("nome"));
                        stm.setString(i++, rsAud.getString("senha"));
                        stm.setString(i++, rsAud.getString("username"));
                        stm.setInt(i++, rsAud.getInt("codigo"));
                        stm.execute();
                        
                        System.out.println(++cont+" - O usuário do aluno "+rsCodUser.getString("matricula")+" - "+rsCodUser.getString("nome")+" foi alterado para "
                                +rsAud.getString("username"));
                    }else{
                        SuperFacadeJDBC.executarConsulta("UPDATE usuario u SET username = (select matricula from clientesintetico where codigo = u.cliente_codigo),\n"
                                + "nome = (select matricula from clientesintetico where codigo = u.cliente_codigo) where u.codigo = "+rsCodUser.getInt("codigo"), contr);
                        System.out.println(++cont+" - O usuário do aluno "+rsCodUser.getString("matricula")+" - "+rsCodUser.getString("nome")+" foi alterado para sua própria MATRÍCULA.");
                    }
                }    
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
}

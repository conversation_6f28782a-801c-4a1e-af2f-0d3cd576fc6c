package controle.arquitetura.servico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class RecursoMsService {

    public static HashMap<String, CacheEmpresaDTO> cacheFuncionalidadesInativas = new HashMap<>();
    private static String url;
    private static String personaToken = null;
    private static Long dtProximoPersonaToken = null;

    public static String baseUrl() throws Exception {
        if (url == null) {
            try {
                url = DiscoveryMsService.urls().getServiceUrls().getRecursoMsUrl();
                if(url == null || url.isEmpty()){
                    Uteis.logar("Falha ao obter url do serviço recurso ms do discovery");
                }
            } catch (Exception e) {
                Uteis.logarDebug("Falha ao obter url do serviço recurso ms do discovery " + e.getMessage());
                throw e;
            }

        }

        return url;
    }

    public static List<FuncionalidadeSistemaEnum> consultarFuncionalidadesInativas(String chave, Integer empresa) {
        if (cacheFuncionalidadesInativas.containsKey(cacheKey(chave, empresa))) {
            CacheEmpresaDTO cacheEmpresaDTO = cacheFuncionalidadesInativas.get(cacheKey(chave, empresa));
            long diferencaEmMinutos = Calendario.diferencaEmMinutos(cacheEmpresaDTO.getData(), Calendario.hoje());

            if (diferencaEmMinutos < Uteis.validadeCacheNichoEmMinutos()) {
                return cacheEmpresaDTO.getFuncionalidadeSistemaEnums();
            }
        }

        initPersonaToken();
        if (personaToken == null || "erroAoGerar".equals(personaToken)) {
            return new ArrayList<>();
        }

        List<FuncionalidadeSistemaEnum> funcionalidades = new ArrayList<>();

        findNichoByKeyAndCompanyId(chave, empresa, funcionalidades, personaToken);

        CacheEmpresaDTO cacheEmpresaDTO = new CacheEmpresaDTO(chave, empresa, funcionalidades);
        cacheFuncionalidadesInativas.put(cacheKey(chave, empresa), cacheEmpresaDTO);
        return funcionalidades;
    }

    private static void initPersonaToken() {
        try {
            if (dtProximoPersonaToken == null || (System.currentTimeMillis() > dtProximoPersonaToken)) {
                personaToken = AutenticacaoMsService.personaToken();
                dtProximoPersonaToken = System.currentTimeMillis() + (60 * 60 * 1000);
            }
        } catch (Exception ex) {
            personaToken = "erroAoGerar";
            dtProximoPersonaToken = System.currentTimeMillis() + (5 * 60 * 1000);
            ex.printStackTrace();
        }
    }

    private static void findNichoByKeyAndCompanyId(String chave, Integer empresa,
                                                   List<FuncionalidadeSistemaEnum> funcionalidades,
                                                   String personaToken) {
        String response;
        try {
            String url = baseUrl() + "/v1/funcionalidade/inativas?empresa=" + empresa + "&chave=" + chave;
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", personaToken);
            response = ExecuteRequestHttpService.get(url, headers);

            JSONObject jsonResponse = new JSONObject(response);
            JSONArray content = jsonResponse.optJSONArray("content");

            if (content != null) {
                for (int i = 0; i < content.length(); i++) {
                    JSONObject funcionalidade = content.getJSONObject(i);
                    String name = funcionalidade.optString("name");
                    if (name != null) {
                        FuncionalidadeSistemaEnum funcionalidadeSistemaEnum = FuncionalidadeSistemaEnum.obterPorNome(name);
                        if (funcionalidadeSistemaEnum != null) {
                            funcionalidades.add(FuncionalidadeSistemaEnum.obterPorNome(name));
                        }
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logarDebug("Falha ao obter funcionalidades inativas do recurso ms " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static String token() throws Exception {
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if (loginControle != null && loginControle.getTokenNZW() != null) {
            return loginControle.getTokenNZW();
        } else {
            throw new Exception("Token não encontrado");
        }
    }

    private static String cacheKey(String chave, Integer empresa) {
        return "chave:" + chave + ":empresa:" + empresa;
    }

    public static String cacheKey(String chave) {
        return "chave:" + chave;
    }

    public static void setCacheFuncionalidadesInativas(String chave, Integer empresa) {
        List<FuncionalidadeSistemaEnum> funcionalidadeSistemaEnums = RecursoMsService.consultarFuncionalidadesInativas(chave, empresa);
        CacheEmpresaDTO cacheEmpresaDTO = new CacheEmpresaDTO(chave, empresa, funcionalidadeSistemaEnums);
        cacheFuncionalidadesInativas.put(cacheKey(chave, empresa), cacheEmpresaDTO);
    }

    public static List<String> invalidarCache(String chave) {
        if (chave != null && !chave.trim().isEmpty()) {
            Set<String> cacheKeys = cacheFuncionalidadesInativas.keySet();
            List<String> cacheKeysPorChave = cacheKeys.stream().filter(k -> k.startsWith(cacheKey(chave))).collect(Collectors.toList());
            cacheKeysPorChave.forEach(k -> cacheFuncionalidadesInativas.remove(k));
            return cacheKeysPorChave;
        } else {
            List<String> cacheKeys = new ArrayList<>(cacheFuncionalidadesInativas.keySet());
            cacheFuncionalidadesInativas = new HashMap<>();
            return cacheKeys;
        }
    }
}

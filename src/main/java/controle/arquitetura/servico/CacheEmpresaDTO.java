package controle.arquitetura.servico;

import controle.arquitetura.FuncionalidadeSistemaEnum;
import java.util.Date;
import java.util.List;

public class CacheEmpresaDTO {
    private String chave;
    private Integer empresa;
    private Date data;
    private List<FuncionalidadeSistemaEnum> funcionalidadeSistemaEnums;

    public CacheEmpresaDTO() {
    }

    public CacheEmpresaDTO(String chave, Integer empresa, List<FuncionalidadeSistemaEnum> funcionalidadeSistemaEnums) {
        this.chave = chave;
        this.empresa = empresa;
        this.funcionalidadeSistemaEnums = funcionalidadeSistemaEnums;
        this.data = new Date();
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public List<FuncionalidadeSistemaEnum> getFuncionalidadeSistemaEnums() {
        return funcionalidadeSistemaEnums;
    }

    public void setFuncionalidadeSistemaEnums(List<FuncionalidadeSistemaEnum> funcionalidadeSistemaEnums) {
        this.funcionalidadeSistemaEnums = funcionalidadeSistemaEnums;
    }
}

package controle.arquitetura.servico.interfaces;

import controle.arquitetura.exceptions.SecretException;
import controle.arquitetura.exceptions.TokenExpiradoException;
import controle.arquitetura.exceptions.TokenInvalidoException;
import controle.arquitetura.servico.impl.PersonaDTO;

/**
 * <PERSON>tra<PERSON> para as regras de negócio de um token
 *
 */
public interface LeituraTokenServico {


    /**
     * Através do TOKEN informado, recupera as informações do usuário
     *
     * @param token TOKEN gerado previamente pela API
     * @throws controle.arquitetura.exceptions.TokenInvalidoException Caso ocorra algum problema na validação ou na recuperação
     */
    PersonaDTO validarRecuperandoPersona(String token) throws SecretException,TokenInvalidoException, TokenExpiradoException;
}

package controle.arquitetura.servico;

import controle.arquitetura.SuperControle;
import controle.arquitetura.servico.google.UploadObject;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisServlet;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.avro.AvroParquetWriter;
import org.apache.parquet.hadoop.ParquetWriter;
import org.apache.parquet.hadoop.metadata.CompressionCodecName;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class ThreadUpdateServico extends Thread implements Runnable {
    private final SuperControle superControle = new SuperControle();
    private UteisServlet uteisServlet = new UteisServlet();
    private String chaveOAMD;
    private String nomeBD;
    private String hostPG;
    private String portaPG;
    private String userPG;
    private String pwdPG;
    private UpdateServicoParams params;
    private boolean terminouExecucao;
    private String comandoSQL;
    private String operacao;
    private String msgErro = "";
    private String formato;
    private StringBuffer resultado;
    private boolean existeResultado = false;
    private boolean exibirTitulo;
    private boolean appendVirgula;
    private String colunas;
    private String parquetFileName;
    private String csvFileName;

    /**
     *
     * @param params
     * @param chaveOAMD
     * @param nomeBD
     * @param userPG
     * @param portaPG
     * @param userPG
     * @param pwdPG
     */
    public ThreadUpdateServico(UpdateServicoParams params, final String chaveOAMD,final String hostPG, final String nomeBD,
            final String userPG,final String portaPG, final String pwdPG, final String comandoSQL, final String operacao,
                               final String format,final boolean exibirTitulo,boolean appendVirgula){
        this.params = params;
        this.chaveOAMD = chaveOAMD;
        this.hostPG = hostPG;
        this.nomeBD = nomeBD;
        this.userPG = userPG;
        this.portaPG = portaPG;
        this.pwdPG = pwdPG;
        this.comandoSQL = comandoSQL;
        this.operacao = operacao;
        this.terminouExecucao = false;
        this.formato =  format;
        this.resultado = new StringBuffer();
        this.exibirTitulo = exibirTitulo;
        this.appendVirgula = appendVirgula;
    }

    @Override
    public void run() {
        try {
            uteisServlet.logar(String.format("%s - %s - %s ", this.getClass().getSimpleName(), chaveOAMD, nomeBD));
            if (operacao.equals("select")) {
                if (formato != null && formato.equals("parquet"))
                    executarConsultaBancoParquet();
                else if (formato != null && formato.startsWith("csv"))
                    executarConsultaBancoCSV();
                else
                    executarConsultaBanco();
            }
        } catch (Exception e) {
            this.msgErro = "Erro execução Thread : " + this.nomeBD + ".\n" +
                    "Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage();
            Uteis.logar(null, msgErro);
        } finally {
            this.terminouExecucao = true;
        }
    }


    public String getChaveOAMD() {
        return chaveOAMD;
    }

    public void setChaveOAMD(String chaveOAMD) {
        this.chaveOAMD = chaveOAMD;
    }

    public String getNomeBD() {
        return nomeBD;
    }

    public void setNomeBD(String nomeBD) {
        this.nomeBD = nomeBD;
    }

    public String getHostPG() {
        return hostPG;
    }

    public void setHostPG(String hostPG) {
        this.hostPG = hostPG;
    }

    public String getPortaPG() {
        return portaPG;
    }

    public void setPortaPG(String portaPG) {
        this.portaPG = portaPG;
    }

    public String getUserPG() {
        return userPG;
    }

    public void setUserPG(String userPG) {
        this.userPG = userPG;
    }

    public String getPwdPG() {
        return pwdPG;
    }

    public void setPwdPG(String pwdPG) {
        this.pwdPG = pwdPG;
    }

    public UpdateServicoParams getParams() {
        return params;
    }

    public void setParams(UpdateServicoParams params) {
        this.params = params;
    }

    public boolean isTerminouExecucao() {
        return terminouExecucao;
    }

    public void setTerminouExecucao(boolean terminouExecucao) {
        this.terminouExecucao = terminouExecucao;
    }

    public String getComandoSQL() {
        return comandoSQL;
    }

    public void setComandoSQL(String comandoSQL) {
        this.comandoSQL = comandoSQL;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String Operacao) {
        this.operacao = Operacao;
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }

    private void executarConsultaBanco() {
        try {
            Connection  conAtual = uteisServlet.obterConexao(hostPG, portaPG, userPG, pwdPG, nomeBD);
            try {
                int seq = 0; 

                ResultSet rsConsulta = SuperFacadeJDBC.criarConsulta(comandoSQL, conAtual);
                uteisServlet.logar("Consultou " + conAtual.getMetaData().getURL());
                // metodo doConsultaSQL usa um do/while para complementar uso do next no if e percorrer corretamente o ResultSet
                if (rsConsulta.next()) {
                    CopyOnWriteArrayList<String> colunas = uteisServlet.obterColunas(rsConsulta);
                    this.colunas = String.join(UteisServlet.getDelimiter(formato), colunas) +
                            (!UteisValidacao.emptyString(chaveOAMD) ? UteisServlet.getDelimiter(formato) + UteisServlet.COL_CHAVE : "");

                    StringBuffer resultGrupo = uteisServlet.doConsultaSQL(conAtual, rsConsulta, colunas, formato, exibirTitulo, seq, chaveOAMD);
                    if (!formato.equals("json")) {
                        resultado.append(resultGrupo);
                    } else {
                        JSONArray arrayResult = new JSONArray(resultGrupo.toString());

                        JSONObject obj = new JSONObject();
                        obj.put("_chave", chaveOAMD);
                        obj.put("nomeBanco", nomeBD);
                        obj.put("host", InetAddress.getLocalHost().getHostName());
                        obj.put("versaoBD", superControle.getVersaoBD());
                        obj.put("versao", superControle.getVersaoSistema());
                        obj.put("result", arrayResult);
                        resultado.append(obj.toString());
                        if (appendVirgula) {
                            resultado.append(",");
                        }
                    }
                    existeResultado = true;
                }
            } catch (Exception e) {
                Uteis.logarDebug(String.format("Erro ao consultar: %s com o erro: %s", nomeBD, e.getMessage()));
                if (formato.equals("json")) {

                    JSONObject obj = new JSONObject();
                    obj.put("_chave", chaveOAMD);
                    obj.put("nomeBanco", nomeBD);
                    obj.put("host", InetAddress.getLocalHost().getHostName());
                    obj.put("versaoBD", superControle.getVersaoBD());
                    obj.put("versao", superControle.getVersaoSistema());
                    obj.put("result", e.getMessage());
                    resultado.append(obj.toString());
                    if (appendVirgula) {
                       resultado.append(",");
                    }
                } else if (!formato.startsWith("csv")) {
                    uteisServlet.print(resultado, conAtual.getMetaData().getURL() + ": " + e.getMessage(), false);
                }
                existeResultado = true;
            } finally {
                conAtual.close();
            }
        } catch (Exception ex) {
            if (formato.equals("json")) {
                JSONObject obj = new JSONObject();
                obj.put("nomeBanco", nomeBD);
                obj.put("versaoBD", superControle.getVersaoBD());
                obj.put("versao", superControle.getVersaoSistema());
                try {
                    obj.put("host", InetAddress.getLocalHost().getHostName());
                } catch (UnknownHostException e) {
                    obj.put("host", "N/C");
                    e.printStackTrace();
                }
                obj.put("result", "erro");
                resultado.append(obj.toString());
                if (appendVirgula) {
                    resultado.append(",");
                }
            } else if (!formato.startsWith("csv")) {
                uteisServlet.print(resultado, "\"" + ex.getMessage() + "\"", false);
            }
        }
            
    }

    private void executarConsultaBancoCSV() {
        try {
            Connection conAtual = null;
            String msgErro = null;
            try {
                conAtual = uteisServlet.obterConexao(hostPG, portaPG, userPG, pwdPG, nomeBD);
            } catch (Exception e) {
                msgErro = e.getMessage();
                uteisServlet.logar(String.format("Falhou conexão para CSV em %s:%s/%s - com erro %s tentar mais 3x...", hostPG, portaPG, nomeBD, msgErro));

                int i = 1;
                while (i <= 3) {
                    Thread.sleep(5000);
                    try {
                        conAtual = uteisServlet.obterConexao(hostPG, portaPG, userPG, pwdPG, nomeBD);
                        uteisServlet.logar(String.format("Conexão recuperada em tentativa %s/3 %s %s %s - estava com o erro %s...", i, hostPG, portaPG, nomeBD, e.getMessage()));
                    } catch (Exception ex) {
                        uteisServlet.logar(String.format("Tentativa %s/3 falhou %s %s %s - %s...", i, hostPG, portaPG, nomeBD, e.getMessage()));
                        msgErro = e.getMessage();
                    }
                    i++;
                }
            }

            if (conAtual != null && !conAtual.isClosed()) {

                try {
                    int seq = 0;
                    CopyOnWriteArrayList colunas = null;
                    conAtual.setAutoCommit(false);
                    Statement stm = conAtual.createStatement();
                    stm.setFetchSize(1000);
                    ResultSet rsConsulta = stm.executeQuery(comandoSQL);

                    uteisServlet.logar(String.format("Consultando para CSV %s...", conAtual.getMetaData().getURL()));

                    // metodo doConsultaSQL usa um do/while para complementar uso do next no if e percorrer corretamente o ResultSet
                    try {
                        if (rsConsulta.next()) {
                            if (seq == 0) {
                                colunas = uteisServlet.obterColunas(rsConsulta);
                                this.colunas = String.join(UteisServlet.getDelimiter(formato), colunas) +
                                        (!UteisValidacao.emptyString(chaveOAMD) ? UteisServlet.getDelimiter(formato) + UteisServlet.COL_CHAVE : "");
                            }
                            seq++;
                            csvFileName = uteisServlet.obterResultadoCSV(colunas, rsConsulta, formato, exibirTitulo, seq, chaveOAMD);
                            uteisServlet.logar(String.format("Consultou para CSV %s - %s",  conAtual.getMetaData().getURL(), csvFileName));
                            //uteisServlet.logar(String.format("Consultou Colunas %s para CSV %s %s - %s", this.colunas, conAtual, conAtual.getMetaData().getURL(), csvFileName));
                            existeResultado = csvFileName != null;
                        }
                    } finally {
                        rsConsulta.close();
                        stm.close();
                    }
                } catch (Exception e) {
                    Uteis.logarDebug(String.format("Erro ao consultar 2: %s com o erro: %s", nomeBD, e.getMessage()));
                    e.printStackTrace();
                } finally {
                    conAtual.close();
                }
            } else {
                Uteis.logarDebug(String.format("Erro ao consultar 3: %s com o erro: conexão falhou anteriormente com a mensagem: %s", nomeBD, msgErro));
            }
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("Falhou ao obter conexão do banco de dados %s:%s %s %s", hostPG, portaPG, userPG, nomeBD) );
            ex.printStackTrace();
        } finally {
            terminouExecucao = true;
        }

    }


    private void executarConsultaBancoParquet() {
        try {
            Connection  conAtual = uteisServlet.obterConexao(hostPG, portaPG, userPG, pwdPG, nomeBD);
            final String host = InetAddress.getLocalHost().getHostName();
            try {
                ResultSet rsConsulta = SuperFacadeJDBC.criarConsulta(comandoSQL, conAtual);
                uteisServlet.logar("Consultou Parquet " + conAtual.getMetaData().getURL());
                String tableFrom = "undefined";

                if (formato.split(",").length > 1) {
                    tableFrom = formato.split(",")[1];
                } else {

                    String regex = "(?<=from|join)(\\s+\\w+\\b)";
                    Matcher m = Pattern.compile(regex, Pattern.CASE_INSENSITIVE).matcher(comandoSQL);

                    if (m.find())
                        tableFrom = m.group().trim().replace(" ", "-");
                }

                try {
                    final long createdAt = System.currentTimeMillis();
                    final String filePrefix = String.format("%s_%s_%s.%s_%s_%s.parquet", host, hostPG, nomeBD, tableFrom, chaveOAMD, createdAt);
                    this.parquetFileName = filePrefix;
                    if (formato.equals("parquet")) {
                        Path path = new Path(String.format("%s%s%s",
                                Uteis.TEMP_DIR,
                                File.separator,
                                filePrefix));

                        List<String> colunas = uteisServlet.obterColunas(rsConsulta);
                        Schema schema = uteisServlet.obterSchemaParquet(rsConsulta);

                        try (ParquetWriter<GenericData.Record> parquetWriter = AvroParquetWriter.<GenericData.Record>builder(path)
                                .withSchema(schema)
                                .withCompressionCodec(CompressionCodecName.SNAPPY)
                                .withRowGroupSize(ParquetWriter.DEFAULT_BLOCK_SIZE)
                                .withPageSize(ParquetWriter.DEFAULT_PAGE_SIZE)
                                .withConf(new Configuration())
                                .withValidation(false)
                                .withDictionaryEncoding(false)
                                .build()) {

                            JSONObject meta = new JSONObject();
                            meta.put("_chave", chaveOAMD);
                            meta.put("_nomeBanco", nomeBD);
                            meta.put("_host", host);
                            meta.put("_versaoBD", superControle.getVersaoBD());
                            meta.put("_versao", superControle.getVersaoSistema());
                            meta.put("_createdAt", createdAt);

                            while (rsConsulta.next()) {
                                GenericData.Record record = uteisServlet.obterParquetData(colunas, schema, rsConsulta, meta);
                                parquetWriter.write(record);
                            }
                        }

                        File f = new File(path.toString());
                        uteisServlet.logar(String.format("%s To Parquet %s", conAtual.getMetaData().getURL(), f.toString()));
                        if (f.exists() && f.length() > 256) {
                            UploadObject.uploadObject("pacto-datalake-dev", "land_zone_db_data",
                                    f.getName(), f.getAbsolutePath());
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace(System.out);
                } finally {
                    terminouExecucao = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                if (formato.equals("json")) {

                    JSONObject obj = new JSONObject();
                    obj.put("_chave", chaveOAMD);
                    obj.put("nomeBanco", nomeBD);
                    obj.put("host", InetAddress.getLocalHost().getHostName());
                    obj.put("versaoBD", superControle.getVersaoBD());
                    obj.put("versao", superControle.getVersaoSistema());
                    obj.put("result", e.getMessage());
                    resultado.append(obj.toString());
                    if (appendVirgula) {
                        resultado.append(",");
                    }
                } else if (!formato.startsWith("csv")) {
                    uteisServlet.print(resultado, conAtual.getMetaData().getURL() + ": " + e.getMessage(), false);
                }
                existeResultado = true;
            } finally {
                conAtual.close();
            }
        } catch (Exception ex) {
            if (formato.equals("json")) {
                JSONObject obj = new JSONObject();
                obj.put("nomeBanco", nomeBD);
                obj.put("versaoBD", superControle.getVersaoBD());
                obj.put("versao", superControle.getVersaoSistema());
                try {
                    obj.put("host", InetAddress.getLocalHost().getHostName());
                } catch (UnknownHostException e) {
                    obj.put("host", "N/C");
                    e.printStackTrace();
                }
                obj.put("result", "erro");
                resultado.append(obj.toString());
                if (appendVirgula) {
                    resultado.append(",");
                }
            } else if (!formato.startsWith("csv")) {
                uteisServlet.print(resultado, "\"" + ex.getMessage() + "\"", false);
            }
        } finally {
            terminouExecucao = true;
        }

    }

    public UteisServlet getUteisServlet() {
        return uteisServlet;
    }

    public void setUteisServlet(UteisServlet uteisServlet) {
        this.uteisServlet = uteisServlet;
    }

    public String getFormato() {
        return formato;
    }

    public void setFormato(String formato) {
        this.formato = formato;
    }

    public StringBuffer getResultado() {
        return resultado;
    }

    public void setResultado(StringBuffer resultado) {
        this.resultado = resultado;
    }

    public boolean isExisteResultado() {
        return existeResultado;
    }

    public void setExisteResultado(boolean existeResultado) {
        this.existeResultado = existeResultado;
    }

    public boolean isExibirTitulo() {
        return exibirTitulo;
    }

    public void setExibirTitulo(boolean exibirTitulo) {
        this.exibirTitulo = exibirTitulo;
    }

    public boolean isAppendVirgula() {
        return appendVirgula;
    }

    public void setAppendVirgula(boolean appendVirgula) {
        this.appendVirgula = appendVirgula;
    }

    public String getColunas() {
        return colunas;
    }

    public String getParquetFileName() {
        return parquetFileName;
    }

    public String getCsvFileName() {
        return csvFileName;
    }
}

package controle.arquitetura.servico.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import controle.arquitetura.exceptions.SecretException;
import controle.arquitetura.exceptions.TokenExpiradoException;
import controle.arquitetura.exceptions.TokenInvalidoException;
import controle.arquitetura.servico.interfaces.LeituraTokenServico;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

public class LeituraTokenServiceImpl implements LeituraTokenServico {

    private final String EMITENTE = "aut_pacto";
    private final String CLAIM_CONTENT = "content";
    private JWTVerifier verificador;
    private Algorithm algoritimo;
    private static String authSecret = null;
    private static String authSecretPersona = null;

    public String authSecret() throws SecretException{
        try {
            if (authSecret == null) {
                String path_id_persona = PropsService.getPropertyValue("AUTH_SECRET_PATH");
                authSecret = Uteis.readLineByLineJava8(path_id_persona).replace("\n", "").replace("\r", "");
            }
            if(UteisValidacao.emptyString(authSecret)){
                System.out.println("auth secret vazia");
                throw new Exception("auth secret vazia");
            }
            return authSecret;
        }catch (Exception e){
            throw new SecretException(e);
        }

    }

    @Override
    public PersonaDTO validarRecuperandoPersona(String token) throws SecretException, TokenInvalidoException, TokenExpiradoException {
        this.algoritimo = Algorithm.HMAC256(authSecret());
        this.verificador = JWT.require(algoritimo).withIssuer(EMITENTE).build();

        DecodedJWT decodedJWT = validarToken(token);
        return new PersonaDTO(new JSONObject(getClaim(String.class, decodedJWT, CLAIM_CONTENT)));
    }

    private DecodedJWT validarToken(String token) throws TokenInvalidoException, TokenExpiradoException {
        try {
            return verificador.verify(token);
        } catch (TokenExpiredException e) {
            throw new TokenExpiradoException(e);
        } catch (JWTVerificationException e) {
            throw new TokenInvalidoException(e);
        }
    }


    public <T> T getClaim(Class<T> tipo, DecodedJWT decodedJWT, String claimCode){
        Claim claim = decodedJWT.getClaims().get(claimCode);
        return claim == null ? null : claim.as(tipo);
    }
}

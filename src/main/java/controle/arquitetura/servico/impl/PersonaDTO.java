package controle.arquitetura.servico.impl;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PersonaDTO {


    private String descricao;
    private String permissoes;

    public PersonaDTO(JSONObject json) {
        this.descricao = json.optString("d");
        this.permissoes = json.optString("p");
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<String> getPermissoes() {
        return UteisValidacao.emptyString(permissoes) ? new ArrayList<>() : Arrays.asList(permissoes.split(","));
    }

    public void setPermissoes(String permissoes) {
        this.permissoes = permissoes;
    }
}

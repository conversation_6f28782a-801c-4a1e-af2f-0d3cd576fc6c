/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servico;

/**
 *
 * <AUTHOR>
 */
public class UpdateServicoParams {

    /**
     * request.getParameter("hostPG"), request.getParameter("portaPG"),
     * request.getParameter("userPG"), request.getParameter("pwdPG"),
     * request.getParameter("bd"), equest.getParameter("sql")
     */
    private String prefixoBanco;
    private String chavesOnly;
    private String hostPG;
    private String portaPG;
    private String userPG;
    private String pwdPG;
    private String bd;
    private String roboControle;
    private String sql;
    private String nomeBDFull;
    private String except;
    private String lgn;

    public UpdateServicoParams(String prefixoBanco, String chavesOnly, String hostPG,
            String portaPG, String userPG, String pwdPG, String bd, String roboControle,
            String sql, String nomeBDFull, String except, String lgn) {

        this.prefixoBanco = prefixoBanco;
        this.hostPG = hostPG;
        this.portaPG = portaPG;
        this.userPG = userPG;
        this.pwdPG = pwdPG;
        this.bd = bd;
        this.roboControle = roboControle;
        this.sql = sql;
        this.nomeBDFull = nomeBDFull;
        this.except = except;
        this.lgn = lgn;
        this.chavesOnly = chavesOnly;
    }

    public String getPrefixoBanco() {
        return prefixoBanco;
    }

    public void setPrefixoBanco(String prefixoBanco) {
        this.prefixoBanco = prefixoBanco;
    }

    public String getHostPG() {
        return hostPG;
    }

    public void setHostPG(String hostPG) {
        this.hostPG = hostPG;
    }

    public String getPortaPG() {
        return portaPG;
    }

    public void setPortaPG(String portaPG) {
        this.portaPG = portaPG;
    }

    public String getUserPG() {
        return userPG;
    }

    public void setUserPG(String userPG) {
        this.userPG = userPG;
    }

    public String getPwdPG() {
        return pwdPG;
    }

    public void setPwdPG(String pwdPG) {
        this.pwdPG = pwdPG;
    }

    public String getBd() {
        return bd;
    }

    public void setBd(String bd) {
        this.bd = bd;
    }

    public String getRoboControle() {
        return roboControle;
    }

    public void setRoboControle(String roboControle) {
        this.roboControle = roboControle;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getNomeBDFull() {
        return nomeBDFull;
    }

    public void setNomeBDFull(String nomeBDFull) {
        this.nomeBDFull = nomeBDFull;
    }

    public String getExcept() {
        return except;
    }

    public void setExcept(String except) {
        this.except = except;
    }

    public String getLgn() {
        return lgn;
    }

    public void setLgn(String lgn) {
        this.lgn = lgn;
    }

    public String getChavesOnly() {
        return chavesOnly;
    }

    public void setChavesOnly(String chavesOnly) {
        this.chavesOnly = chavesOnly;
    }
}

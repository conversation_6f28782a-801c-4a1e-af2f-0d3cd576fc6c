package controle.arquitetura.servico;

import com.sun.org.apache.xml.internal.serialize.XMLSerializer;
import controle.arquitetura.session.SessionTO;
import controle.arquitetura.session.listener.SessionState;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisServlet;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.io.FileUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.XML;
import org.w3c.dom.Document;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Connection;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UpdateServico {

    private static final String id = "id";
    private static final String dtCriacao = "dtCriacao";
    private static final String ultAcesso = "ultAcesso";
    private static final String lastURI = "lastURI";
    private static final String empresa = "empresa";
    private static final String usuario = "usuario";
    private static final String ip = "ip";
    private static final String instancia = "instancia";
    private static final String instancia_prop = "com.sun.aas.instanceName";
    private static final String procId = "procId";
    private static final String browser = "browser";
    private static final String codigoEmpresa = "codigoEmpresa";
    private static final String empty = "";
    private static final String cnpj = "cnpj";
    private static final String uf = "uf";
    private static final String screenH = "screenH";
    private static final String screenW = "screenW";
    private static final String chave = "chave";
    private static final String virgula = ",";
    private static final String contentType = "application/json";
    private static final String dateTimePattern = "yyyy-MM-dd'T'HH:mm:ss";
    private static final String UTC = "UTC";
    private static final String GMT_3 = "GMT-3";
    private static final SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    private static final String ULT_EXEC = "ultimaExecucao";
    private static final String ERROS = "erros";
    private static final String PWDPG = "pwdPG";
    private static final String LGN = "lgn";
    private static final String ID = "id";
    private static final String CHAVE = "chave";
    private static final String ROBOCONTROLE = "roboControle";
    private static final String CONTAINS = "contains";

    private UteisServlet uteisServlet = new UteisServlet();
    private static final String CHARSET = "UTF-8";
    public List<ThreadUpdateServico> listaThreads;

    Map<String, ExecutorService> executorServicesPerZone = new HashMap<>();

    public void executarConsultaSQL(String nomeHostPG,
                                    String portaPG, String superUserPG, String senhaPG,
                                    String nomeBanco, String comandoSQL, String formato, String prefixoArquivo,
                                    String appName,
                                    PrintWriter out, HttpServletResponse response, ServletContext servletContext, boolean exibirTitulo, int seq) {
        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");
            if (formato == null) {
                formato = "html";
            }
            Connection conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco, appName);
            try {
                ResultSet rsConsulta = SuperFacadeJDBC.criarConsulta(comandoSQL, conAtual);
                if (formato.equals("excel")) {
                    List<Connection> conexoes = new ArrayList<Connection>();
                    conexoes.add(conAtual);
                    uteisServlet.doJasperGenerics(conexoes, comandoSQL, prefixoArquivo, response, servletContext);
                } else if (rsConsulta.next()) {  // metodo uteisServlet.obterResultado usa um do/while para complementar uso do next no if es percorrer corretamente o ResultSet
                    if (formato.equals("html")) {
                        uteisServlet.print(out, conAtual.getMetaData().getURL());
                    }

                    final String tmp = uteisServlet.obterResultado(uteisServlet.obterColunas(rsConsulta),
                            rsConsulta, formato, exibirTitulo, seq, null).toString();
                    if (formato.equals("json")) {
                        JSONArray arrayResult = new JSONArray(tmp);

                        JSONObject obj = new JSONObject();
                        obj.put("nomeBanco", nomeBanco);
                        obj.put("result", arrayResult);

                        final String saidaTmp = obj.toString();
                        uteisServlet.print(out, saidaTmp);
                    } else {
                        uteisServlet.print(out, tmp);
                    }
                } else {
                    uteisServlet.print(out, UteisServlet.MSG_SEMDADOS);
                }

            } catch (Exception e) {
                uteisServlet.print(out, conAtual.getMetaData().getURL() + ": " + e.getMessage());
            } finally {
                conAtual.close();
            }
        } catch (Exception ex) {
            uteisServlet.print(out, ex.getMessage());
        }
    }

    public void getParquetSchema(String nomeHostPG,
                                    String portaPG, String superUserPG, String senhaPG,
                                    String nomeBanco, String comandoSQL,
                                    PrintWriter out) {
        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");

            Connection conAtual = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, nomeBanco, "GetParquetSchema");

            try {
                try (ResultSet rsConsulta = SuperFacadeJDBC.criarConsulta(comandoSQL, conAtual)) {
                    if (rsConsulta.next())
                        uteisServlet.printLine(out, uteisServlet.obterSchemaJsonParquet(rsConsulta));
                }
            } catch (Exception e) {
                uteisServlet.print(out, conAtual.getMetaData().getURL() + ": " + e.getMessage());
                e.printStackTrace();
            } finally {
                conAtual.close();
            }
        } catch (Exception ex) {
            uteisServlet.print(out, ex.getMessage());
        }
    }

    private void validarComando(final String comando, final String... termosBloquear) throws Exception {
        for (int i = 0; i < termosBloquear.length; i++) {
            String termo = termosBloquear[i];
            if (comando.toLowerCase().contains(termo.toLowerCase() + " ") || comando.toLowerCase().contains(" " + termo.toLowerCase() + " ")) {
                throw new Exception(String.format("Termo \"%s\" não é permitido por esta operação", termo));
            }
        }
    }

    @Deprecated
    /**
     * @see UpdateServico.executarConsultaFull_XML
     */
    public void executarConsultaSQLTodosBancosDadosFull(String nomeHostPG, String portaPG, String superUserPG, String senhaPG,
                                                        String comandoSQL, String except, String formato, String prefixoArquivo,
                                                        String chave, final String prefixBanco,
                                                        PrintWriter out, HttpServletResponse response,
                                                        ServletContext servletContext, boolean exibirTitulo, int seq) throws Exception {
        String _hostPG;

        ConsultaServlet consulta = new ConsultaServlet(out);

        Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");
            response.addHeader("Access-Control-Allow-Origin", "http://homologacao.pactosolucoes.com.br");
            if (formato != null) {
                if (formato.equalsIgnoreCase("json")) {
                    response.setContentType("application/json;charset=UTF-8");
                } else if (formato.equalsIgnoreCase("excel")) {
                    response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                }
            }
            //
            final String prefixo = prefixBanco != null ? prefixBanco : "bdzillyon";

            //Bancos do OAMD
            StringBuilder consultaEmpresas = new StringBuilder("SELECT * FROM empresa WHERE ativa IS TRUE AND \"nomeBD\" LIKE ('" + prefixo + "%')\n");

            if (chave != null) {
                consultaEmpresas.append("AND chave = '").append(chave).append("'");

            }
            ResultSet bancos = SuperFacadeJDBC.criarConsulta(consultaEmpresas.toString(), conOAMD);
            ArrayList<String> servidoresProcessados = new ArrayList<String>();

            ExecuteRequestHttpService serviceRequest = new ExecuteRequestHttpService();
            serviceRequest.connectTimeout = 3000;
            JSONArray arrayJSON = new JSONArray();
            String _op = (chave == null) ? "selectALL" : "selectONE";
            while (bancos.next()) {
                String chaveOAMD = bancos.getString("chave");
                if (chave == null || chave.equals(chaveOAMD)) {

                    String roboControle = bancos.getString("robocontrole");
                    if (roboControle == null
                            || roboControle.trim().isEmpty()
                            || roboControle.contains("homologacao.pactosolucoes")
                            || roboControle.contains("192.168")) {
                        continue;
                    }
                    String chaveServidor = roboControle.replace("http://", "").
                            replace("https://", "");
                    if (chaveServidor.indexOf("/") != -1) {
                        chaveServidor = chaveServidor.substring(0, chaveServidor.indexOf("/"));
                    }
                    if (servidoresProcessados.contains(chaveServidor)) {
                        continue;
                    }
                    String url = roboControle.trim() + "/UpdateServlet";

                    _hostPG = bancos.getString("hostBD");
                    Integer _portaPG = bancos.getInt("porta");
                    String _pwdPG = bancos.getString("passwordBD");

                    Map<String, String> params = new HashMap<String, String>();
                    params.put("op", _op);
                    params.put("sql", comandoSQL);
                    params.put("format", "json");
                    params.put("hostPG", _hostPG);
                    params.put("portaPG", _portaPG.toString());
                    params.put("userPG", superUserPG);
                    params.put("pwdPG", _pwdPG);
                    params.put("prefixoBanco", prefixo);
                    if (chave != null) {
                        params.put("bd", bancos.getString("nomeBD"));
                    }
                    if (!servidoresProcessados.contains(roboControle)) {
                        uteisServlet.logar("Consultou " + roboControle);
                        JSONObject jsonObj = new JSONObject();
                        jsonObj.put("nomeservidor", roboControle);
                        try {
                            if (roboControle.contains("pactosolucoes.com.br") || roboControle.contains(".amazonaws.com")) {
                                serviceRequest.readTimeout = 300000;// 5 minutos
                            } else {
                                serviceRequest.readTimeout = 30000;
                            }
                            String requisicao = serviceRequest.executeRequestInner(url, params);
                            if (_op.equals("selectONE")) {
                                JSONObject jsonRequisicao = new JSONObject(requisicao);
                                jsonObj.put("servidor", jsonRequisicao);
                            } else {
                                JSONArray jsonRequisicao = new JSONArray(requisicao);
                                jsonObj.put("servidor", jsonRequisicao);
                            }
                        } catch (Exception ex) {
                            JSONObject jsonEx = new JSONObject();
                            jsonEx.put("excecao", ex.getMessage() == null ? "NullpointerException" : ex.getMessage());
                            JSONArray arrayEx = new JSONArray();
                            arrayEx.put(jsonEx);
                            jsonObj.put("servidor", jsonEx);
                        }
                        servidoresProcessados.add(chaveServidor);
                        arrayJSON.put(jsonObj);
                    }
                }
            }
            consulta.getOut().append(arrayJSON.toString());
            conOAMD.close();
        } catch (Exception ex) {
            out.append("[");
//            uteisServlet.print(out, ex.getMessage());
            out.append("]}");
            uteisServlet.logar("Erro em  executarConsultaSQLTodosBancosDadosFull: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            conOAMD.close();
        }
    }

    public void redirecXML(HttpServletRequest request, HttpServletResponse response) {
        response.addHeader("Access-Control-Allow-Origin", "http://homologacao.pactosolucoes.com.br");
        //
        final String docid = request.getParameter("docid");
        final String docParams = docid.substring(0, docid.indexOf(".")) + ".json";

        response.setContentType("text/xml;charset=" + CHARSET);
        try {
            File xml = new File(String.format("%s/%s",
                    PropsService.getPropertyValue(PropsService.diretorioArquivos),
                    request.getParameter("docid")));


            File jsonParams = new File(String.format("%s/%s",
                    PropsService.getPropertyValue(PropsService.diretorioArquivos),
                    docParams));
            if (xml.exists() && request.getParameter("op").equals("errosFull")) {
                uteisServlet.logar("Redirect XML: " + xml.getAbsolutePath());

                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

                try {
                    DocumentBuilder builder = factory.newDocumentBuilder();
                    Document document = builder.parse(xml);
                    XMLSerializer serializer = new XMLSerializer();
                    serializer.setOutputByteStream(response.getOutputStream());
                    serializer.serialize(document);
                } catch (ParserConfigurationException pce) {
                    // do something appropriate
                }
            } else if (xml.exists() && jsonParams.exists()) {
                JSONObject p = new JSONObject(FileUtils.readFileToString(jsonParams));
                if (!docid.endsWith(".json")) {
                    String dataCache;
                    Date dateCache = null;
                    try {
                        dataCache = p.getString("dataCache");
                        dateCache = Calendario.getDate("dd/MM/yyyy HH:mm:ss", dataCache);
                    } catch (Exception e) {
                    }
                    int minutosIntervalo = 60;//minutos
                    boolean validarCache = true;
                    try {
                        final String intervalo = p.getString("intervalo");
                        minutosIntervalo = intervalo == null || intervalo.isEmpty() ? minutosIntervalo : Integer.valueOf(intervalo);
                    } catch (Exception e) {
                    }
                    try {
                        validarCache = p.getString("validacache").equals("sim") || (!UteisValidacao.emptyString(request.getParameter("vlcache")) && request.getParameter("vlcache").equals("sim"));
                    } catch (Exception e) {
                    }
                    if (validarCache && (dateCache == null || minutosIntervalo == -1 || Calendario.diferencaEmMinutos(dateCache,
                            Calendario.hoje()) > minutosIntervalo)) {// se já existir os dados no cache, recarrega depois de 5 minutos da última execução
                        Set<String> s = p.keySet();
                        for (String paramJSON : s) {
                            request.setAttribute(paramJSON, p.get(paramJSON));
                        }
                        preencherArquivoXML(xml,
                                p.getString("comando"), p.getString("prefixo"),
                                p.has("chavesOnly")  ? p.getString("chavesOnly") : null,
                                p.getString("chave"), p.getString("except"), p.getString("lgn"), request);
                        //
                        p.put("dataCache", Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                        FileUtils.writeStringToFile(jsonParams, p.toString(), CHARSET);
                    }
                    //
                    uteisServlet.logar("Redirect XML: " + xml.getAbsolutePath());

                    DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();

                    try {
                        DocumentBuilder builder = factory.newDocumentBuilder();
                        Document document = builder.parse(xml);
                        XMLSerializer serializer = new XMLSerializer();
                        serializer.setOutputByteStream(response.getOutputStream());
                        serializer.serialize(document);
                        p.put("ultimaExecucao", Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                        p.put("intervalo", String.valueOf(minutosIntervalo));
                        FileUtils.writeStringToFile(jsonParams, p.toString(), CHARSET);
                    } catch (ParserConfigurationException pce) {
                        // do something appropriate
                    }
                } else {
                    response.setContentType("application/json;charset=" + CHARSET);
                    response.getOutputStream().println(p.toString());
                }
            } else {
                throw new Exception("Arquivo com este docid não foi encontrado: " + docid);
            }
        } catch (Exception ex) {
            Logger.getLogger(UpdateServico.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void wf(OutputStream os, final String content) throws IOException {
        os.write(content.getBytes(CHARSET));
    }

    private static boolean deveTrocarHttps(URL u) {
        return u.getProtocol().equals("https") && (String.valueOf(u.getPort()).startsWith("90") && !u.getHost().contains("dyndns"));
    }

    private static String prepararUrlRoboControle(final String url) throws MalformedURLException {
        URL u = new URL(url);
        final String roboControle = url;
        if (u.getPort() == -1) {
            return roboControle.replaceAll("https", "http");
        } else if (deveTrocarHttps(u)) {
            if (roboControle.contains("https://zw90.pactosolucoes.com.br:9090")) {
                return roboControle.replace("https://zw90.pactosolucoes.com.br:9090",
                        "http://zw90.pactosolucoes.com.br:8090");
            } else if (roboControle.contains("https://unique.pactosolucoes.com.br:9090")) {
                return roboControle.replace("https://unique.pactosolucoes.com.br:9090",
                        "http://unique.pactosolucoes.com.br:8090");
            } else {
                return roboControle.replaceAll("https", "http").replace("90", "80");
            }
        } else {
            return roboControle;
        }
    }

    private List<UpdateServicoParams> obterParamsUpdateServlet(final String comandoSQL, final String prefixBanco, final String lgn,
                                                               final String chavesOnly, final String chave,
                                                               HttpServletRequest request, boolean incluirTestes) throws Exception {

        final String prefixo = prefixBanco == null || prefixBanco.isEmpty() ? "bdzillyon" : prefixBanco;

        List<UpdateServicoParams> params = new ArrayList();
        StringBuilder consultaEmpresas = new StringBuilder("SELECT * FROM empresa WHERE ativa IS TRUE and usoteste IS FALSE and usointerno IS FALSE ");
        /*if (!UteisValidacao.emptyString(prefixo) && !prefixo.contains("bdmusc")) {
            consultaEmpresas.append("AND \"nomeBD\" LIKE ('").append(prefixo).append("%')\n");
        }*/
        if (incluirTestes) {
            consultaEmpresas = new StringBuilder("SELECT * FROM empresa WHERE ativa IS TRUE ");
        }

        consultaEmpresas.append("AND robocontrole <> '' \n");
        //
        if (chavesOnly != null && !chavesOnly.isEmpty()) {
            consultaEmpresas.append(" AND chave in (").append(Uteis.splitFromArray(chavesOnly.split(","), true)).append(") \n");
        }
        else if (chave != null && !chave.isEmpty()) {
            consultaEmpresas.append(" AND chave = '").append(chave).append("' \n");
        }
        consultaEmpresas.append("ORDER BY robocontrole");
        if (request.getParameter("bd") != null && !request.getParameter("bd").isEmpty()) {
            final String nomeBD = request.getParameter("bd");
            final String hostPG = request.getParameter("hostPG");
            final String portaPG = request.getParameter("portaPG");
            final String userPG = request.getParameter("userPG");
            final String pwdPG = request.getParameter("pwdPG");
            final String except = request.getParameter("except");
            final String roboControle = prepararUrlRoboControle(request.getRequestURL().toString()).replace("/UpdateServlet", "");
            UpdateServicoParams p = new UpdateServicoParams(prefixo, chavesOnly, hostPG,
                    portaPG, userPG, pwdPG, nomeBD, roboControle, comandoSQL, nomeBD, except, lgn);
            params.add(p);
        } else if (request.getAttribute("bd") != null && !request.getAttribute("bd").toString().isEmpty()) {
            final String nomeBD = (String) request.getAttribute("bd");
            final String hostPG = (String) request.getAttribute("hostPG");
            final String portaPG = (String) request.getAttribute("portaPG");
            final String userPG = (String) request.getAttribute("userPG");
            final String pwdPG = (String) request.getAttribute("pwdPG");
            final String roboControle = prepararUrlRoboControle(request.getRequestURL().toString());
            final String except = request.getParameter("except");
            UpdateServicoParams p = new UpdateServicoParams(prefixo, chavesOnly, hostPG, portaPG, userPG, pwdPG, nomeBD, roboControle, comandoSQL, nomeBD, except, lgn);
            params.add(p);
        } else {
            Connection conOAMD = Conexao.obterConexaoBancoEmpresas();
            try {
                ResultSet bancos = SuperFacadeJDBC.criarConsulta(consultaEmpresas.toString(), conOAMD);
                while (bancos.next()) {
                    final String chaveOAMD = bancos.getString("chave");
                    final Integer infra = bancos.getInt("infoinfra");
                    String roboControle = bancos.getString("roboControle");
                    try {
                        if (infra.intValue() == 6
                                || infra.intValue() == 9
                                || infra.intValue() == 10
                                || infra.intValue() == 12
                                || infra.intValue() == 13
                        ) {
                            roboControle = "http://app4.pactosolucoes.com.br/app";
                        }
                    } catch (Exception e) {
                    }
                    roboControle = prepararUrlRoboControle(roboControle);
                    final String hostPG = bancos.getString("hostBD");
                    final Integer portaPG = bancos.getInt("porta");
                    final String pwdPG = bancos.getString("passwordBD");
                    final String userPG = bancos.getString("userBD");
                    final String nomeBDFull = bancos.getString("nomeBD");
                    final String except = request.getParameter("except");
                    UpdateServicoParams p = new UpdateServicoParams(
                            prefixo, chavesOnly, hostPG, portaPG.toString(), userPG, pwdPG,
                            null, roboControle, comandoSQL, nomeBDFull, except, lgn);
                    params.add(p);
                }
            } finally {
                conOAMD.close();
            }
        }
        return params;
    }

    private void preencherArquivoXML(final File arquivoOut,
                                     final String comandoSQL, final String prefixBanco, final String chavesOnly, final String chave,
                                     final String except, final String lgn, HttpServletRequest request) throws Exception {
        List<String> erros = new ArrayList();
        FileOutputStream fos = new FileOutputStream(arquivoOut);
        //
        List<UpdateServicoParams> paramsServico = obterParamsUpdateServlet(comandoSQL,
                prefixBanco, lgn, chavesOnly, chave, request, false);
        File arqErros = new File(String.format("%s/%s",
                PropsService.getPropertyValue(PropsService.diretorioArquivos),
                "errosfull.xml"));
        FileOutputStream fosErros = new FileOutputStream(arqErros);
        wf(fosErros, "<?xml version=\"1.0\" encoding=\"" + CHARSET + "\"?>");

        wf(fosErros, "<root>");
        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");
            //
            wf(fos, "<?xml version=\"1.0\" encoding=\"" + CHARSET + "\"?>");

            wf(fos, "<root>");
            //
            Map<String, String> servidoresProcessados = new HashMap<String, String>();
            //
            ExecuteRequestHttpService serviceRequest = new ExecuteRequestHttpService();
            serviceRequest.connectTimeout = 3000;
            serviceRequest.readTimeout = 30000;
            for (UpdateServicoParams p : paramsServico) {

                String _op = "selectALL";
                if (chave != null && !chave.isEmpty()) {
                    _op = "selectONE";
                } else if (p.getBd() != null && !p.getBd().isEmpty()) {
                    _op = "selectONE";
                }
                JSONArray arrayJSON = new JSONArray();
                final String nomeBD = p.getBd();
                if (p.getExcept() != null && uteisServlet.isExcept(nomeBD, p.getExcept())) {
                    continue;
                }

                final String roboControle = p.getRoboControle();

                if (roboControle == null
                        || roboControle.trim().isEmpty()
                        || roboControle.contains("homologacao.pactosolucoes")
                        || roboControle.contains("devi9.pactosolucoes")
                        || roboControle.contains("devapp.pactosolucoes")
                        || roboControle.contains("devdesk.pactosolucoes")
                        || roboControle.contains("192.168")) {
                    continue;
                }

                URL u = new URL(roboControle);
                final String chaveServidor = u.getHost();
                if (servidoresProcessados.containsKey(chaveServidor)) {
                    continue;
                }

                if ((chaveServidor.contains("zw90.pactosolucoes.com.br")
                        && servidoresProcessados.containsKey("zw90.pactosolucoes.com.br"))
                        || (chaveServidor.contains("unique.pactosolucoes.com.br")
                        && servidoresProcessados.containsKey("unique.pactosolucoes.com.br"))) {
                    continue;
                }

                if (roboControle.contains("pactosolucoes.com.br") || roboControle.contains(".amazonaws.com")) {
                    serviceRequest.readTimeout = 300000;// 5 minutos
                } else {
                    serviceRequest.readTimeout = 30000;
                }

                final String url = !roboControle.contains("UpdateServlet") ? roboControle.trim() + "/UpdateServlet" : roboControle;

                Map<String, String> params = new HashMap<String, String>();
                params.put("op", _op);
                params.put("sql", comandoSQL);
                params.put("format", "json");
                params.put("hostPG", p.getHostPG());
                params.put("portaPG", p.getPortaPG());
                params.put("userPG", p.getUserPG());
                params.put("pwdPG", p.getPwdPG());
                params.put("prefixoBanco", p.getPrefixoBanco());
                params.put("chavesOnly", p.getChavesOnly());
                params.put("bd", nomeBD);
                params.put("except", except);
                params.put("lgn", lgn);
                uteisServlet.logar(String.format("Consultando: %s - params: %s... ", roboControle, params));
                try {
                    String resposta = serviceRequest.executeRequestInner(url, params);
                    if (_op.equals("selectONE")) {
                        JSONObject jsonResposta = new JSONObject(resposta);
                        arrayJSON.put(jsonResposta);
                    } else {
                        JSONArray jsonResposta = new JSONArray(resposta);
                        arrayJSON.put(jsonResposta);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    erros.add(String.format("URL: %s - Params: %s - mensagem: %s", roboControle, params, ex.getMessage()));
                    wf(fosErros, "<array><result><servidor>" + roboControle + "</servidor><nomebanco>" + p.getNomeBDFull() + "</nomebanco><erro>" + ex.getMessage() + "</erro><xmlconsulta>" + arquivoOut.getName() + "</xmlconsulta><dataOcorrencia>" + Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss") + "</dataOcorrencia></result></array>");
                }
                /*servidoresProcessados.add(chaveServidor);
                servidoresProcessados.add(u.getHost());*/
                servidoresProcessados.put(chaveServidor, chaveServidor);
                wf(fos, XML.toString(arrayJSON));
                uteisServlet.logar("Consultou " + roboControle + "!");
                fos.flush();

            }

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            File fParams = new File(String.format("%s/%s.json",
                    PropsService.getPropertyValue(PropsService.diretorioArquivos),
                    arquivoOut.getName().substring(0, arquivoOut.getName().indexOf("."))));
            JSONObject p;
            if (!fParams.exists()) {//não existe ainda
                p = new JSONObject();
                if (!paramsServico.isEmpty()) {
                    UpdateServicoParams ps = paramsServico.get(0);
                    p.put("bd", ps.getBd());
                    p.put("hostPG", ps.getHostPG());
                    p.put("portaPG", ps.getPortaPG());
                    p.put("prefixo", ps.getPrefixoBanco());
                    p.put("pwdPG", ps.getPwdPG());
                    p.put("roboControle", ps.getRoboControle());
                    p.put("userPG", ps.getUserPG());
                }
                p.put("comando", comandoSQL);
                p.put("prefixo", prefixBanco);
                p.put("chavesOnly", chavesOnly);
                p.put("chave", chave);
                p.put("except", except);
                p.put("erros", erros);
                p.put("criacao", Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                p.put("dataCache", Calendario.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                p.put("intervalo", request.getParameter("intervalo"));
                p.put("lgn", request.getParameter("lgn"));
                if (UteisValidacao.emptyString(request.getParameter("intervalo"))) {
                    p.put("validacache", "nao");
                } else {
                    p.put("validacache", "sim");
                }
                FileUtils.writeStringToFile(fParams, p.toString(), CHARSET);
            }

            wf(fos, "</root>");
            fos.close();
            wf(fosErros, "</root>");
            fosErros.close();
        }
    }

    public void executarConsultaFull_XML(String comandoSQL,
                                         final String except, final String chave, final String prefixBanco, String chavesOnly,
                                         PrintWriter out, HttpServletRequest request, HttpServletResponse response) throws Exception {
        response.addHeader("Access-Control-Allow-Origin", "http://homologacao.pactosolucoes.com.br");
        //
        File arqCache = new File(String.format("%s/%s.xml",
                PropsService.getPropertyValue(PropsService.diretorioArquivos),
                UUID.randomUUID()));
        //
        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");
            preencherArquivoXML(arqCache, comandoSQL, prefixBanco, chavesOnly, chave, except, request.getParameter("lgn"), request);
            response.setContentType("text/xml;charset=" + CHARSET);
            //REDIRECT
            response.sendRedirect(String.format("%s/UpdateServlet?op=dc&docid=%s", request.getContextPath(),
                    arqCache.getName()));
            //
        } catch (Exception ex) {
            out.println(ex.getMessage());
            ex.printStackTrace();
        }
    }

    public void executarConsultaSQLTodosBancosDados(String nomeHostPG, String portaPG, String superUserPG, String senhaPG,
                                                    String comandoSQL, String except, String formato, String prefixoArquivo, final String prefixBanco,
                                                    final String chavesOnly,
                                                    PrintWriter out, HttpServletResponse response, ServletContext servletContext, boolean exibirTitulo, int seq) {
        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");
            List<Connection> conexoes = new ArrayList<>();
            ConsultaServlet consulta = new ConsultaServlet(out);
            if (formato.equals("json")) {
                consulta.getOut().append("[");
            }

            processSelectAllThread(nomeHostPG, portaPG, superUserPG, senhaPG, comandoSQL, except, formato, prefixBanco,
                    chavesOnly, exibirTitulo, seq, conexoes, consulta);

            if (!consulta.getExisteResultado() && !formato.equals("json") && !formato.equals("parquet") && !formato.contains("csv")) {
                uteisServlet.print(out, UteisServlet.MSG_SEMDADOS);
            }

            if (formato.equals("json")) {
                out.append("]");
            }

            if (formato.equals("excel")) {
                uteisServlet.doJasperGenerics(conexoes, comandoSQL, prefixoArquivo, response, servletContext);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            uteisServlet.print(out, ex.getMessage());
        }
    }

    public void listSessions(PrintWriter out, HttpServletResponse response, HttpServletRequest request) throws JSONException {
        ConsultaServlet consulta = new ConsultaServlet(out);
        response.setContentType(contentType);

        StringBuilder sessoes = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat(dateTimePattern);
        dateFormat.setTimeZone(TimeZone.getTimeZone(GMT_3));

        List<SessionTO> sessions = null;
        try {
            sessions = new ArrayList<>(SessionState.updateList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (sessions != null) {
            for (SessionTO sess : sessions) {
                try {
                    JSONObject obj = new JSONObject();
                    obj.put(id, sess.getId());
                    obj.put(dtCriacao, dateFormat.format(sess.getDataCriacao()));
                    obj.put(ultAcesso, dateFormat.format(sess.getUltAcesso()));
                    obj.put(lastURI, sess.getLastURI());
                    obj.put(empresa, (sess.getEmpresa() != null ? sess.getEmpresa() : empty));
                    obj.put(usuario, (sess.getUsuario() != null ? sess.getUsuario() : empty));
                    obj.put(ip, (sess.getIp() != null ? sess.getIp() : empty));
                    obj.put(instancia, System.getProperty(instancia_prop, empty));
                    obj.put(procId, sess.getProcId());
                    obj.put(browser, sess.getBrowser());
                    obj.put(codigoEmpresa, (sess.getCodigoEmpresa() != null ? sess.getCodigoEmpresa() : 0));
                    obj.put(cnpj, (sess.getCnpj() != null ? sess.getCnpj() : empty));
                    obj.put(chave, (sess.getChave() != null ? sess.getChave() : empty));
                    obj.put(uf, (sess.getUf() != null ? sess.getUf() : empty));
                    obj.put(screenH, sess.getScreenH());
                    obj.put(screenW, sess.getScreenW());
                    sessoes.append(obj.toString());
                    sessoes.append(virgula);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }

            if (sessoes.length() > 0) {
                sessoes.deleteCharAt(sessoes.length() - 1);
            }
            consulta.getOut().append(sessoes);
        }
    }


    private void processSelectAllThread(String nomeHostPG, String portaPG, String superUserPG, String senhaPG,
                                        String comandoSQL, String except, String formato, final String prefixBanco,
                                        final String chavesOnly, boolean exibirTitulo, int seq,
                                        List<Connection> conexoes, ConsultaServlet consultaServlet) throws Exception {
        String sql = "";
        String sqlcount = "";
        boolean oamd = true;
        Connection con = null;
        Connection conOAMD = null;
        Connection conOAMD2 = null;
        try {
            conOAMD = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "OAMD");
            conOAMD2 = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "OAMD2");
        } catch (Exception e) {
            e.printStackTrace();
        }
        final String prefixo = prefixBanco != null ? prefixBanco : "bdzillyon";
        if (chavesOnly != null && !chavesOnly.isEmpty()) {
            con = uteisServlet.obterConexao(nomeHostPG, portaPG, "postgres", senhaPG, "OAMD");
            sql = "SELECT * FROM empresa WHERE ativa IS TRUE AND chave in (" + Uteis.splitFromArray(chavesOnly.split(","), true) + ")";
            sqlcount = "SELECT count(*) as foo FROM empresa WHERE ativa IS TRUE AND chave in (" + Uteis.splitFromArray(chavesOnly.split(","), true) + ")";
        } else if (prefixo != null && (prefixo.equalsIgnoreCase("bdzillyon") || prefixo.equalsIgnoreCase("bdmusc"))) {
            con = uteisServlet.obterConexao(nomeHostPG, portaPG, "postgres", senhaPG, prefixo.equalsIgnoreCase("bdzillyon") ? "OAMD" : "OAMD2");
            sql = "SELECT * FROM empresa WHERE ativa IS TRUE ";
            sqlcount = "SELECT count(*) as foo FROM empresa WHERE ativa IS TRUE ";
        } else {
            con = uteisServlet.obterConexao(nomeHostPG, portaPG, superUserPG, senhaPG, "postgres");
            sqlcount = "select count(*) from (select * from pg_database where datname like('" + prefixo + "%') order by datname) as foo";
            sql = "select * from pg_database where datname like('" + prefixo + "%') order by datname";
            oamd = false;
        }

        //Bancos do OAMD
        try {
            int cont = 0;
            int qtdBancos = SuperFacadeJDBC.contar(sqlcount, con);
            ResultSet bancos = SuperFacadeJDBC.criarConsulta(sql, con);
            String chaveOAMD = "";
            String _nomeBD = "";
            String _hostPG = "";
            String _portaPG = "";
            String _userPG = "";
            String _pwdPG = "";
            listaThreads = new ArrayList<>();

            while (bancos.next()) {
                if (oamd) {
                    chaveOAMD = bancos.getString("chave");
                    _nomeBD = bancos.getString("nomeBD");
                    _hostPG = bancos.getString("hostBD");
                    _portaPG = bancos.getString("porta");
                    _userPG = bancos.getString("userBD");
                    _pwdPG = bancos.getString("passwordBD");
                } else {
                    _nomeBD = bancos.getString("datname");
                    _userPG = superUserPG;
                    _hostPG = nomeHostPG;
                    _portaPG = portaPG;
                    _pwdPG = senhaPG;
                }

                if (!UteisValidacao.emptyString(prefixBanco) && prefixBanco.contains("bdmusc"))
                    _nomeBD = _nomeBD.replace("bdzillyon", "bdmusc");

                if (uteisServlet.isExcept(_nomeBD, except)) {
                    continue;
                }

                if (conOAMD  != null && UteisValidacao.emptyString(chaveOAMD)) {
                    try (ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format("select chave from empresa where \"nomeBD\" = '%s'", _nomeBD), conOAMD)) {
                        if (rs.next())
                            chaveOAMD = rs.getString(1);
                    }
                }

                if (conOAMD2 != null && UteisValidacao.emptyString(chaveOAMD)) {
                    try (ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format("select chave from empresa where \"nomeBD\" = '%s'", _nomeBD), conOAMD2)) {
                        if (rs.next())
                            chaveOAMD = rs.getString(1);
                    }
                }

                try {
                    if (formato.equals("excel")) {
                        Connection conAtual = uteisServlet.obterConexao(_hostPG, _portaPG, _userPG, _pwdPG, _nomeBD);
                        conexoes.add(conAtual);
                    } else {
                        listaThreads.add(new ThreadUpdateServico(null, chaveOAMD, _hostPG, _nomeBD, _userPG, _portaPG,
                                _pwdPG, comandoSQL, "select", formato, exibirTitulo, (cont < (qtdBancos - 1))));
                    }
                } catch (Exception ex) {
                    uteisServlet.print(consultaServlet.getOut(), "\"" + ex.getMessage() + "\"");
                }
                cont++;
            }
        } finally {
            con.close();
            if (conOAMD != null)
                conOAMD.close();
            if (conOAMD2 != null) {
                conOAMD2.close();
            }
        }

        if (!formato.equals("excel")) {
            try {
                executarThreads();
                loopVerificarTerminoDasThreads();
                boolean printedColunas = false;
                for (ThreadUpdateServico thread : listaThreads) {
                    if (!printedColunas && formato.startsWith("csv")){
                        if (thread.getColunas() != null && !thread.getColunas().equals("null")) {
                            consultaServlet.getOut().append(thread.getColunas()).append("\n");
                            //uteisServlet.logar(String.format("Adicionado Colunas %s para CSV %s", thread.getColunas(), thread.getCsvFileName()));
                            printedColunas = true;
                        }
                    }
                    if (thread.getParquetFileName() != null) {
                        consultaServlet.setExisteResultado(true);
                        consultaServlet.getOut().append(thread.getParquetFileName().concat("\n"));
                    } else if (thread.isExisteResultado()) {

                        final String fileName = thread.getCsvFileName();

                        consultaServlet.setExisteResultado(true);

                        if (fileName != null) {
                            try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
                                String line;
                                int ind = 0;
                                // Read the file line by line and write to the PrintWriter
                                while ((line = reader.readLine()) != null) {
                                    consultaServlet.getOut().println(line);
                                    ind++;
                                    if (ind == 10000) {
                                        consultaServlet.getOut().flush();
                                        ind = 0;
                                    }
                                }
                                Uteis.logarDebug(String.format("File %s copied successfully!", fileName));
                            } catch (Exception e) {
                                e.printStackTrace();
                            } finally {
                                consultaServlet.getOut().flush();
                                File file = new File(fileName);
                                if (file.delete()) {
                                    Uteis.logarDebug(String.format("File %s deleted with successfully!", fileName));
                                } else {
                                    Uteis.logarDebug(String.format("Failed to delete file %s!", fileName));
                                }
                            }
                        } else {
                            consultaServlet.getOut().append(thread.getResultado().toString());
                        }
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    public void executarThreads() {
        // iniciar as threads
        try {
            for (ThreadUpdateServico thread : listaThreads) {
                ExecutorService executorService = executorServicesPerZone.get(thread.getHostPG());
                if (executorService == null) {
                    int totalCores = Runtime.getRuntime().availableProcessors();
                    // calculate half of the total cores
                    int halfCores = totalCores / 2;
                    executorService = Executors.newFixedThreadPool(halfCores);
                    executorServicesPerZone.put(thread.getHostPG(), executorService);
                }
                executorService.submit(thread);
                Uteis.logarDebug(String.format("Acionado o ExecutorService %s for chave %s", thread.getHostPG(), thread.getChaveOAMD()));
            }
        } finally {
            for (Map.Entry<String, ExecutorService> entry : executorServicesPerZone.entrySet()) {
                if (entry.getValue() != null) {
                    entry.getValue().shutdown();
                }
            }
        }
    }

    public void loopVerificarTerminoDasThreads() throws Exception {
        int totalThreads = listaThreads.size();
        int totalThreadsTerminadas = 0;
        while (totalThreadsTerminadas < totalThreads) {
            for (ThreadUpdateServico thread : listaThreads) {
                if (thread.isTerminouExecucao()) {
                    totalThreadsTerminadas++;
                } else {
                    break;
                }
                // Verifica se houve erro na execução da thread
                if (!thread.getMsgErro().isEmpty()) {
                    Uteis.logarDebug("método loopVerificarTerminoDasThreads achou erro nas threads.");
                    throw new Exception(thread.getMsgErro());
                }
            }
            if (totalThreadsTerminadas == totalThreads)
                break;
            totalThreadsTerminadas = 0;
            Thread.sleep(1000);
        }
    }

    public void catalogXML(HttpServletRequest request, HttpServletResponse response) {
        //
        response.setContentType("text/xml;charset=" + CHARSET);
        try {
            OutputStream os = response.getOutputStream();
            wf(os, "<?xml version=\"1.0\" encoding=\"" + CHARSET + "\"?>");
            wf(os, "<root>");
            wf(os, XML.toString(readCatalog(request.getParameter(CONTAINS), request)));
            wf(os, "</root>");
        } catch (Exception ex) {
            Logger.getLogger(UpdateServico.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public JSONArray readCatalog(final String contains, HttpServletRequest req) {
        File dirJSON = new File(String.format("%s",
                PropsService.getPropertyValue(PropsService.diretorioArquivos)));
        JSONArray arr = new JSONArray();
        if (dirJSON.isDirectory()) {
            File[] files = dirJSON.listFiles((dir, name) -> name.toLowerCase().endsWith(".json"));
            for (File f : files) {
                try {
                    final String json = FileUtils.readFileToString(f, "UTF-8");
                    try {
                        if (json != null && !json.isEmpty() && json.contains("{") && json.contains("}")) {
                            JSONObject o = new JSONObject(json);
                            o.put(ID, f.getName().replace(".json", ""));
                            if (o.has(ULT_EXEC)) {
                                o.remove(ERROS);
                                o.remove(PWDPG);
                                o.remove(CHAVE);
                                o.remove(ROBOCONTROLE);
                                if (contains != null && contains.length() > 0 && !json.toLowerCase().contains(contains)) {
                                    continue;
                                }
                                if (o.has(LGN)) {
                                    String lgn = o.getString(LGN);
                                    if (lgn != null && !lgn.isEmpty() && lgn.length() > 10)
                                        o.put(LGN, lgn.substring(0, 10));
                                }
                                arr.put(o);
                            }
                            if (!UteisValidacao.emptyString(req.getParameter("changelgn"))
                                    && req.getParameter("changelgn").equals("true")) {

                                JSONObject jsonExist = new JSONObject(json);

                                if (jsonExist.has(LGN)) {
                                    JSONObject obUserExist = new JSONObject(Uteis.desencriptar(jsonExist.getString(LGN),"chave_login_unificado"));

                                    String lgnRequest = req.getParameter(LGN);
                                    JSONObject obUserNew = new JSONObject(Uteis.desencriptar(lgnRequest,"chave_login_unificado"));

                                    if (obUserExist.getString("userName") != null && !obUserExist.getString("userName").isEmpty()
                                            && obUserExist.getString("userName").equals(obUserNew.getString("userName"))) {
                                        Uteis.logarDebug(String.format("Atualizado LGN-TOKEN for %s para %s",
                                                jsonExist.getString("userName"), req.getParameter(LGN)));
                                        jsonExist.put(LGN, req.getParameter(LGN));
                                    }
                                }
                            }
                        }
                    } catch (JSONException e) {
                        System.out.println("Erro ao ler JSON " + f.getName() + ": " + e.getMessage());
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return arr;
    }

    public void purgeCatalog(HttpServletResponse response, final Integer limiteDias) {
        File dirJSON = new File(String.format("%s",
                PropsService.getPropertyValue(PropsService.diretorioArquivos)));
        int cont = 0;
        if (dirJSON.isDirectory()) {
            File[] files = dirJSON.listFiles((dir, name) -> name.toLowerCase().endsWith(".json"));
            for (File f : files) {
                try {
                    final String json = FileUtils.readFileToString(f, "UTF-8");

                    try {
                        if (json != null && !json.isEmpty() && json.contains("{") && json.contains("}")) {
                            JSONObject o = new JSONObject(json);
                            if (o.has(ULT_EXEC)) {
                                try {
                                    Date d = sdf.parse(o.getString(ULT_EXEC));
                                    if (Uteis.nrDiasEntreDatas(d, Calendario.hoje()) > limiteDias) {
                                        File xml = new File(f.getParent() + File.separator + f.getName().replace(".json", ".xml"));
                                        if (xml.exists())
                                            xml.delete();
                                        f.delete();
                                        response.getOutputStream().println(String.format("Removido arquivo %s ", f.getPath()));
                                        cont++;
                                    }
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }
                            } else {
                                f.delete();
                            }
                        }
                    } catch (JSONException e) {
                        System.out.println("Erro ao ler JSON " + f.getName() + ": " + e.getMessage());
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        try {
            response.getOutputStream().println(String.format("Removidos %s arquivos", cont));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void preencherSchemaParquet(final String comandoSQL, final String prefixBanco, final String chave,
                                       final String lgn, HttpServletRequest request, PrintWriter out) throws Exception {
        List<String> erros = new ArrayList();
        //
        List<UpdateServicoParams> paramsServico = obterParamsUpdateServlet(comandoSQL,
                prefixBanco, lgn, null, chave, request, true);

        try {
            validarComando(comandoSQL, "update", "delete", "alter", "create");
            //
            //
            Map<String, String> servidoresProcessados = new HashMap<String, String>();
            //
            ExecuteRequestHttpService serviceRequest = new ExecuteRequestHttpService();
            serviceRequest.connectTimeout = 3000;
            serviceRequest.readTimeout = 30000;
            for (UpdateServicoParams p : paramsServico) {

                String _op = "extractSchemaParquet";

                final String roboControle = p.getRoboControle();

                if (roboControle == null
                        || roboControle.trim().isEmpty()
                        || roboControle.contains("homologacao.pactosolucoes")
                        || roboControle.contains("devi9.pactosolucoes")
                        || roboControle.contains("devapp.pactosolucoes")
                        || roboControle.contains("devdesk.pactosolucoes")) {
                    continue;
                }

                URL u = new URL(roboControle);
                final String chaveServidor = u.getHost();
                if (servidoresProcessados.containsKey(chaveServidor)) {
                    continue;
                }

                if ((chaveServidor.contains("zw90.pactosolucoes.com.br")
                        && servidoresProcessados.containsKey("zw90.pactosolucoes.com.br"))
                        || (chaveServidor.contains("unique.pactosolucoes.com.br")
                        && servidoresProcessados.containsKey("unique.pactosolucoes.com.br"))) {
                    continue;
                }

                serviceRequest.readTimeout = 300000;// 5 minutos

                final String url = !roboControle.contains("UpdateServlet") ? roboControle.trim() + "/UpdateServlet" : roboControle;

                Map<String, String> params = new HashMap<String, String>();
                params.put("op", _op);
                params.put("sql", comandoSQL);
                params.put("format", "json");
                params.put("hostPG", p.getHostPG());
                params.put("portaPG", p.getPortaPG());
                params.put("userPG", p.getUserPG());
                params.put("pwdPG", p.getPwdPG());
                if (!UteisValidacao.emptyString(chave) && !UteisValidacao.emptyString(p.getNomeBDFull())){
                    params.put("bd", p.getNomeBDFull());
                    if (prefixBanco.equals("bdmusc")){
                        p.setNomeBDFull(p.getNomeBDFull().replace("bdzillyon", "bdmusc"));
                        params.put("bd", p.getNomeBDFull());
                    }
                }

                params.put("lgn", lgn);
                uteisServlet.logar(String.format("Consultando Schema JSON Parquet: %s - params: %s... ", roboControle, params));
                try {
                    String resposta = serviceRequest.executeRequestInner(url, params);
                    Uteis.logarDebug("PARQUET JSON SCHEMA: " + resposta);
                    uteisServlet.printLine(out, resposta);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                servidoresProcessados.put(chaveServidor, chaveServidor);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /*public static void main(String[] args) {
        try {
            System.out.println(new UpdateServico().readCatalog());
        } catch (Exception ex) {
            Logger.getLogger(UpdateServico.class.getName()).log(Level.SEVERE, null, ex);
        }
    }*/
}

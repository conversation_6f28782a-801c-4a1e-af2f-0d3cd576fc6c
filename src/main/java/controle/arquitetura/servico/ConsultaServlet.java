package controle.arquitetura.servico;

import java.io.PrintWriter;

/**
 * Created by glauco on 15/07/2014.
 */
public class ConsultaServlet {

    private Boolean existeResultado = false;
    private PrintWriter out;

    ConsultaServlet(PrintWriter out) {
        this.out = out;
    }

    public Boolean getExisteResultado() {
        return existeResultado;
    }

    public void setExisteResultado(Boolean existeResultado) {
        this.existeResultado = existeResultado;
    }

    public PrintWriter getOut() {
        return out;
    }

    public void setOut(PrintWriter out) {
        this.out = out;
    }

}

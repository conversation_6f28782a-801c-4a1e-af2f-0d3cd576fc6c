package controle.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estudio.controle.CancelamentoSessaoControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ClienteControle;
import controle.basico.TelaClienteControle;
import controle.contrato.ContratoControle;
import controle.financeiro.EdicaoPagamentoControle;
import controle.financeiro.EstornoMovProdutoControle;
import controle.financeiro.EstornoReciboControle;
import controle.plano.ConsultarTurmaControle;
import controle.plano.OrcamentoControle;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.Date;

public class OperacoesClienteService {
    private final String codCliente;
    private OperacoesTelaClienteEnum operacaoTelaCliente;
    private Connection con;
    private String chave;

    public OperacoesClienteService(String codCliente, OperacoesTelaClienteEnum operacaoTelaCliente, String chave) {
        this.codCliente = codCliente;
        this.operacaoTelaCliente = operacaoTelaCliente;
        this.chave = chave;
    }

    public String abrirOperacaoCliente(HttpServletRequest request) throws Exception {
        switch (this.operacaoTelaCliente) {
            case CONSULTA_RECIBO:
                abrirTelaConsultaRecibo(request, this.operacaoTelaCliente);
                break;
            case EDICAO_FORMA_PAGAMENTO:
                abrirTelaEdicaoPagamento(request);
                break;
            case NOVO_CONTRATO_VENDA_RAPIDA:
                return request.getContextPath() + "/faces/" + this.operacaoTelaCliente.getUrl() + request.getParameter("codCliente");
            case REMATRICULAR_RENOVAR_CONTRATO:
            case NOVO_CONTRATO:
                abrirTelaContrato(request);
                break;
            case LISTA_ALUNO_TURMA:
                abrirListaAlunoTurma(request);
                break;
            case ESTORNAR_PRODUTO:
                abrirEstonarProduto(request);
                break;
            case CANCELAR_SESSAO:
                abrirCancelarSessao(request);
                break;
            case REALIZAR_ORCAMENTO:
                abrirRealizarOrcamento(request);
                break;
            default:
                if (this.operacaoTelaCliente.getControle() != null) {
                    abrirTelaByController(request, this.operacaoTelaCliente);
                }
        }
        return request.getContextPath() + "/faces/" + this.operacaoTelaCliente.getUrl();
    }

    private void abrirRealizarOrcamento(HttpServletRequest request) throws Exception {
        OrcamentoControle controle = (OrcamentoControle) getControleOrCreate(request, operacaoTelaCliente);
        controle.preencherCliente(Integer.parseInt(request.getParameter("codCliente")));
    }

    private void abrirCancelarSessao(HttpServletRequest request) {
        Integer codMovProduto = Integer.parseInt(request.getParameter("codMovProduto"));
        CancelamentoSessaoControle estornoMovProdutoControle = (CancelamentoSessaoControle) getControleOrCreate(request, OperacoesTelaClienteEnum.CANCELAR_SESSAO);
        estornoMovProdutoControle.novo(codMovProduto);
    }

    private void abrirEstonarProduto(HttpServletRequest request) {
        Integer codMovProduto = Integer.parseInt(request.getParameter("codMovProduto"));
        EstornoMovProdutoControle estornoMovProdutoControle = (EstornoMovProdutoControle) getControleOrCreate(request, OperacoesTelaClienteEnum.ESTORNAR_PRODUTO);
        estornoMovProdutoControle.novo(codMovProduto);
    }

    private void abrirTelaContrato(HttpServletRequest request) throws Exception {
        ClienteControle clienteControle = (ClienteControle) getControleOrCreate(request, ClienteControle.class);
        TelaClienteControle telaControle = (TelaClienteControle) getControleOrCreate(request, TelaClienteControle.class);
        ContratoControle contratoControle = (ContratoControle) getControleOrCreate(request, ContratoControle.class);
        JSFUtilities.storeOnSession(ContratoVO.KEY_NEGOCIACAO_EM_ANDAMENTO, contratoControle.toString());
        telaControle.voltarContratos();
        clienteControle.setContratoVO(new ContratoVO());
        clienteControle.excluirFotoBVTemporario();

        String retorno;

        if (Boolean.parseBoolean(request.getParameter("contratoConcomitante"))) {
            clienteControle.validarNovoContrato();
            contratoControle.adicionarDadosNovoContratoSessao();
            retorno = clienteControle.validarQuestionarioSemLimparContratoControle();
        } else {
            retorno = clienteControle.validarQuestionarioSemLimparContratoControle();
            contratoControle.inicializarDadosNovoContrato();
        }

        if (retorno.equals("contrato")) {
            operacaoTelaCliente = OperacoesTelaClienteEnum.REMATRICULAR_RENOVAR_CONTRATO;
        }

        if (Boolean.parseBoolean(request.getParameter("renovar")) || Boolean.parseBoolean(request.getParameter("rematricular"))) {
            retorno = clienteControle.gerarOutroContratoApartirDoContratoMatricula(Integer.parseInt(request.getParameter("contratoRenovar")));
            if (retorno.equals("questionario")) {
                operacaoTelaCliente = OperacoesTelaClienteEnum.NOVO_CONTRATO;
            }
        }

    }

    private void abrirTelaEdicaoPagamento(HttpServletRequest request) throws Exception {
        EdicaoPagamentoControle edicaoPagamentoControle = (EdicaoPagamentoControle) getControleOrCreate(request, OperacoesTelaClienteEnum.EDICAO_FORMA_PAGAMENTO);
        edicaoPagamentoControle.prepareRecibo(Integer.parseInt(request.getParameter("codigoPagamento")));
        ClienteControle clienteControle = (ClienteControle) getControleOrCreate(request, ClienteControle.class);
        clienteControle.autorizarEdicaoPagamento(Integer.parseInt(request.getParameter("codigoUsuario")));
    }

    private void abrirTelaConsultaRecibo(HttpServletRequest request, OperacoesTelaClienteEnum operacaoTelaCliente) throws Exception {
        EstornoReciboControle estornoReciboControle = (EstornoReciboControle) getControleOrCreate(request, operacaoTelaCliente);
        estornoReciboControle.preparaRecibo(Integer.parseInt(request.getParameter("codigoRecibo")));
        estornoReciboControle.preencherRecibo();
    }

    private void abrirListaAlunoTurma(HttpServletRequest request) throws Exception {
        ConsultarTurmaControle consultarTurmaControle = (ConsultarTurmaControle) getControleOrCreate(request, OperacoesTelaClienteEnum.LISTA_ALUNO_TURMA);
        Integer codHorarioTurma = Integer.parseInt(request.getParameter("codHorarioTurma"));
        String diaSemana = request.getParameter("diaSemana");
        Date dataBase = Calendario.getDate("yyyy-MM-dd'T'HH:mm:ss", request.getParameter("dataBase"));

        consultarTurmaControle.mostrarDadosTurma(
                codHorarioTurma,
                null,
                null,
                diaSemana,
                dataBase
        );

    }

    private void abrirTelaByController(HttpServletRequest request, OperacoesTelaClienteEnum operacaoTelaCliente) throws Exception {
        Object controle = getControleOrCreate(request, operacaoTelaCliente);
        Method method = controle.getClass().getMethod(operacaoTelaCliente.getMethod());
        method.setAccessible(true);
        method.invoke(controle);
        method.setAccessible(false);
    }

    private Object getControleOrCreate(HttpServletRequest request, OperacoesTelaClienteEnum operacaoTelaCliente) {
        Object controle = getControleBySession(request, operacaoTelaCliente);

        if (controle == null) {
            controle = JSFUtilities.getManagedBean(operacaoTelaCliente.getControle().getSimpleName());
        }

        return controle;
    }

    private Object getControleBySession(HttpServletRequest request, OperacoesTelaClienteEnum operacaoTelaCliente) {
        return request.getSession().getAttribute(operacaoTelaCliente.getControle().getSimpleName());
    }

    private Object getControleOrCreate(HttpServletRequest request, Class<?> controleClass) {
        Object controle = getControleBySession(request, controleClass);

        if (controle == null) {
            controle = JSFUtilities.getManagedBean(controleClass.getSimpleName());
        }

        return controle;
    }

    private Object getControleBySession(HttpServletRequest request, Class<?> controleClass) {
        return request.getSession().getAttribute(controleClass.getSimpleName());
    }

    private void obterConexao() throws Exception {
        con = Conexao.getFromSession();
        if (con != null) {
            if (con.isClosed()) {
                if (!UteisValidacao.emptyString(chave)) {
                    con = new DAO().obterConexaoEspecifica(chave.trim());
                } else {
                    con = new DAO().obterConexaoEspecifica(chave);
                }
                Conexao.storeOnSession(con);
            }
        } else {
            con = Conexao.initSession(chave);
        }
    }

}

package controle.arquitetura;

public enum FuncionalidadeClienteEnum {

    NOVO_ATESTADO("Novo Atestado","abrirPopup('[contexto]atestadoAptidaoFisica.jsp', 'AptidaoFisica', 880, 650);","true",new String[]{"A"}),
    NOVO_CONTRATO("Novo Contrato","semValor","ClienteControle.menuContrato",new String[]{"B"}),
    VINCULAR_CARTEIRA("Vincular Carteira","abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595);","true",new String[]{"C"}),
    ADICIONAR_CLASSIFICACAO("Adicionar classificação","abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595);","true",new String[]{"D"}),
    ASSOCIAR_GRUPOS("Associar a Grupos","abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595);","true",new String[]{"E"}),
    ULTIMO_BOLETO_VISITA("Ver o Último Boletim de Visita","abrirPopup('questionarioClienteCRMForm.jsp', 'Questionario', 780, 595);","true",new String[]{"F"}),
    HISTORICO_BOLETINS("Ver Histórico de Boletins","abrirPopup('questionarioClienteForm.jsp', 'Questionario', 780, 595);","true",new String[]{"G"}),
    REALIZAR_CONTATO("Realizar Contato","abrirPopup('questionarioClienteForm.jsp', 'Questionario', 780, 595);","true",new String[]{"H"}),
    HISTORICO_CONTATOS("Histórico Contato","abrirPopup('historicoContatoClienteForm.jsp', 'HistoricoContatoCliente', 512, 530); ","true",new String[]{"I"}),
    HISTORICO_INDICACOES("Histórico de Indicações","abrirPopup('[contexto]historicoIndicacao.jsp', 'HistoricoIndicacao', 512, 530);","true",new String[]{"J"}),
    LANCAR_MENSAGEM_CATRACA("Lançar Mensagem para Catraca","Richfaces.showModalPanel('panelClienteMensagem');","true",new String[]{"K"}),
    LANCAR_AVISO_CONSULTOR("Lançar Aviso ao Consultor","Richfaces.showModalPanel('panelClienteMensagem');","true",new String[]{"L"}),
    LANCAR_AVISO_MEDICO("Lançar Aviso Médico","Richfaces.showModalPanel('panelClienteMensagem');","true",new String[]{"M"}),
    LANCAR_OBJETIVO("Lançar Objetivo do Aluno Academia","Richfaces.showModalPanel('panelClienteMensagem');","true",new String[]{"N"}),
    PRODUTO_SERVICO("Produto ou Serviço","semValor","true",new String[]{"O"}),
    DIARIA("Diária","semValor","true",new String[]{"P"});



    private String descricao;
    private String url;
    private String expressaoRenderizar;
    private String[] palavrasChaves;

    FuncionalidadeClienteEnum(String descricao, String url, String expressaoRenderizar, String[] palavrasChaves){
        this.descricao = descricao;
        this.url = url;
        this.expressaoRenderizar = expressaoRenderizar;
        this.palavrasChaves = palavrasChaves;
    }

    public static FuncionalidadeClienteEnum obterPorNome(final String nome) {
        for (FuncionalidadeClienteEnum i : FuncionalidadeClienteEnum.values()) {
            if (i.name().equals(nome)) {
                return i;
            }
        }
        return null;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getUrl() {
        return url;
    }

    public String getExpressaoRenderizar() {
        return expressaoRenderizar;
    }

    public String[] getPalavrasChaves() {
        return palavrasChaves;
    }
}

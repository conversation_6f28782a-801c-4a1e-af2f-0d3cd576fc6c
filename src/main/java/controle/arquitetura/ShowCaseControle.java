package controle.arquitetura;

import controle.basico.FuncionalidadeControle;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by <PERSON> on 20/06/2016.
 */
public class ShowCaseControle extends SuperControle {


    private List<ObjetoExemplo> listaExemplo;
    private String paginaExibir = "cor.jsp";
    private Boolean check1 = Boolean.FALSE;
    private Boolean check2 = Boolean.FALSE;
    private Boolean radio1 = Boolean.FALSE;
    private Boolean radio2 = Boolean.FALSE;
    public void preparaAbrirConteudo(ActionEvent evt){
        try{
            String nomePagina = (String)evt.getComponent().getAttributes().get("pagina");
            setPaginaExibir(nomePagina);
        }catch (Exception ex){
            Uteis.logar(ex,FuncionalidadeControle.class);
        }
    }
    public String getPaginaExibir() {
        return paginaExibir;
    }

    public void setPaginaExibir(String paginaExibir) {
        this.paginaExibir = paginaExibir;
    }

    public Boolean getCheck1() {
        return check1;
    }

    public void setCheck1(Boolean check1) {
        this.check1 = check1;
    }

    public Boolean getCheck2() {
        return check2;
    }

    public void setCheck2(Boolean check2) {
        this.check2 = check2;
    }

    public Boolean getRadio1() {
        return radio1;
    }

    public void setRadio1(Boolean radio1) {
        this.radio1 = radio1;
    }

    public Boolean getRadio2() {
        return radio2;
    }

    public void setRadio2(Boolean radio2) {
        this.radio2 = radio2;
    }

    public void abrirNotificacaoErro(){
        try{
            throw new Exception("Campos importantes não foram preenchidos");
        }catch(Exception e){
            montarErro(e);
        }
    }

    public void abrirNotificacaoSucesso(){
        limparMsg();
        montarSucessoGrowl("Notificacão apresentada!");
    }

    public void abrirNotificacaoInformativa(){
        limparMsg();
        montarInfo("ALGUNS CAMPOS NÃO FORAM PREENCHIDOS", "Cuide das informações da sua academia, quando mais, melhor!");

    }

    public void abrirNotificacaoAlerta(){
        limparMsg();
        montarAviso("As informações não foram salvas como deviam");
    }


    public List<ObjetoExemplo> getListaExemplo(){
        List<ObjetoExemplo> listObjExemplo = new ArrayList<ObjetoExemplo>();
        ObjetoExemplo objExemplo = new ObjetoExemplo("0001", "13/02/2015", new Double(100.00));
        listObjExemplo.add(objExemplo);
        objExemplo = new ObjetoExemplo("0002", "16/03/2015", new Double(50.00));
        listObjExemplo.add(objExemplo);
        objExemplo = new ObjetoExemplo("0003", "20/05/2015", new Double(73.00));
        listObjExemplo.add(objExemplo);
        objExemplo = new ObjetoExemplo("0004", "16/07/2015", new Double(85.00));
        listObjExemplo.add(objExemplo);
        return listObjExemplo;
    }

    public void setListaExemplo(List<ObjetoExemplo> objExemplo){
        this.listaExemplo = objExemplo;
    }

    public String redirectShowCaseComponentes(){
        return "showcaseComponentes";
    }

    public class ObjetoExemplo {

        private String codigo;
        private String data;
        private Double valor;

        public ObjetoExemplo(String codigo, String data, Double valor){
            this.codigo = codigo;
            this.data = data;
            this.valor = valor;
        }
        public void setCodigo(String codigo){
            this.codigo = codigo;
        }
        public String getCodigo(){
            return this.codigo;
        }
        public void setData(String data){
            this.data = data;
        }
        public String getData(){
            return this.data;
        }
        public void setValor(Double valor){
            this.valor = valor;
        }
        public Double getValor(){
            return this.valor;
        }
    }
}

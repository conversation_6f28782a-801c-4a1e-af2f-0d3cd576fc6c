package controle.arquitetura;


import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;

import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.JenkinsService;

import java.util.Calendar;
import java.util.TimeZone;

public class SuperControleCRM extends SuperControleRelatorio {
    private int tamanhoToolTip = 60;
    private String termosFiscalizados = "";
    private ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO;
    
    public String novo() throws Exception{
    	setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    	getConfiguracaoSistemaCRMVO().setUrlJenkins(getPropertyValue("urlJenkins"));
		getConfiguracaoSistemaCRMVO().setUrlMailing(getPropertyValue("urlMailing"));
    	return "";
    }
    
    public void termosFiscalizados(int tamanho) throws Exception{
    	termosFiscalizados = " ";
    	if(getConfiguracaoSistemaCRMVO() == null){
    		setConfiguracaoSistemaCRMVO(getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
			getConfiguracaoSistemaCRMVO().setUrlJenkins(getPropertyValue("urlJenkins"));
			getConfiguracaoSistemaCRMVO().setUrlMailing(getPropertyValue("urlMailing"));
		}
    	if(getConfiguracaoSistemaCRMVO().getTermosSpam().isEmpty()){
    		setTamanhoToolTip(tamanho);
    		 return;
    	}
    	setMensagemID("msg_tip_tituloMailTermos");
    	termosFiscalizados += getMensagem();
    	limparMsg();
    	int cont = 1;
    	for(String termo : getConfiguracaoSistemaCRMVO().getTermosSpam()){
    		cont++;
    		termosFiscalizados += ", "+termo;
    	}
    	//fazer esta conta para evitar que o tool tip fique quebrado
    	setTamanhoToolTip((tamanho + 50) + 10*(cont/4));
    	termosFiscalizados =  termosFiscalizados.replaceFirst(",", "")+".<br/>";
    }
    
    public String getTermosFiscalizados(){
    	return termosFiscalizados;
    }
	

    /**
     * @return the configuracaoSistemaCRMVO
     */
    public ConfiguracaoSistemaCRMVO getConfiguracaoSistemaCRMVO() {
        return configuracaoSistemaCRMVO;
    }

    /**
     * @param configuracaoSistemaCRMVO the configuracaoSistemaCRMVO to set
     */
    public void setConfiguracaoSistemaCRMVO(ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) {
        this.configuracaoSistemaCRMVO = configuracaoSistemaCRMVO;
    }


	public void setTamanhoToolTip(int tamanhoToolTip) {
		this.tamanhoToolTip = tamanhoToolTip;
	}

	public String getTamanhoToolTip() throws Exception {
		termosFiscalizados(70);
		return tamanhoToolTip+"px";
	}
	public String getTamanhoToolTipSMS() throws Exception {
		termosFiscalizados(0);
		return tamanhoToolTip+"px";
	}
	public boolean getToolTipSMS() throws Exception{
    	if(getConfiguracaoSistemaCRMVO() == null){
    		novo();
    	}
		return !getConfiguracaoSistemaCRMVO().getTermosSpam().isEmpty();
	}

    public void updateJenkinsService(MalaDiretaVO malaDireta, ConfiguracaoSistemaCRMVO confCrm) throws Exception {
        final String chave = String.format("%s_%s_%s", getKey(),
                malaDireta.getMeioDeEnvioEnum().getDescricao(),
                malaDireta.getTipoAgendamento().name());

        if (malaDireta.getTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO)) {
            JenkinsService.createTaskMailing(chave, malaDireta.getCodigo().toString(), null, confCrm.getUrlJenkins(), confCrm.getUrlMailing(),malaDireta.getChaveAntiga());
            JenkinsService.buildTask(chave, malaDireta.getCodigo().toString(), confCrm.getUrlJenkins());
        } else if (malaDireta.getTipoAgendamento().equals(TipoAgendamentoEnum.AGENDAMENTO_PREVISTO)) {
			ajustaHorarioCron(malaDireta);
			JenkinsService.createTaskMailing(chave, malaDireta.getCodigo().toString(),
                    malaDireta.getAgendamento().getCron(),
                    confCrm.getUrlJenkins(), confCrm.getUrlMailing(),malaDireta.getChaveAntiga());
        }
    }

	private void ajustaHorarioCron(MalaDiretaVO malaDireta) throws ConsistirException {
		TimeZone tzEmpresa = TimeZone.getTimeZone(malaDireta.getEmpresa().getTimeZoneDefault());
		TimeZone tzBrasilia = TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId());

		Calendar dEmpresa = Calendario.hojeCalendar(tzEmpresa);
		Calendar dBrasilia = Calendario.hojeCalendar(tzBrasilia);
		Integer diferencaHoras = Calendario.diferencaEmHoras(dEmpresa.getTime(), dBrasilia.getTime()).intValue();

		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, malaDireta.getAgendamento().getHoraInicio());
		calendar.add(Calendar.HOUR_OF_DAY, diferencaHoras);
		malaDireta.getAgendamento().setHoraInicio(calendar.get(Calendar.HOUR_OF_DAY));

		calendar.set(Calendar.HOUR_OF_DAY, malaDireta.getAgendamento().getHoraFim());
		calendar.add(Calendar.HOUR_OF_DAY, diferencaHoras);
		malaDireta.getAgendamento().setHoraFim(calendar.get(Calendar.HOUR_OF_DAY));

		malaDireta.getAgendamento().montarCron();
	}

}

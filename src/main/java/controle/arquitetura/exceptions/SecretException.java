package controle.arquitetura.exceptions;


public class SecretException extends ServiceException {

    private static final String CHAVE_EXCECAO = "problema_secret";
    private static final String MENSAGEM_EXCECAO = "Problemas ao obter a secret";

    public SecretException() {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO);
    }

    public SecretException(Throwable causa) {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO, causa);
    }

}

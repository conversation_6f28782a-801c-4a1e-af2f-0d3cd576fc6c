package controle.arquitetura.exceptions;


public class TokenExpiradoException extends ServiceException {

    private static final String CHAVE_EXCECAO = "token_expirado";
    private static final String MENSAGEM_EXCECAO = "O token informado esta expirado";

    public TokenExpiradoException() {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO);
    }

    public TokenExpiradoException(Throwable causa) {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO, causa);
    }

}

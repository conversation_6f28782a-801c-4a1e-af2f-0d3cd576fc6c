package controle.arquitetura.exceptions;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 */
@SuppressWarnings("serial")
public class CobrancaException extends Exception {

    private CodigoRetornoPactoEnum codigoRetornoPactoEnum;
    private String mensagemComplementar;

    public CobrancaException(CodigoRetornoPactoEnum codigoRetornoPactoEnum) {
        this.codigoRetornoPactoEnum = codigoRetornoPactoEnum;
    }

    public CobrancaException(CodigoRetornoPactoEnum codigoRetornoPactoEnum, String mensagemComplementar) {
        this.codigoRetornoPactoEnum = codigoRetornoPactoEnum;
        this.mensagemComplementar = mensagemComplementar;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPactoEnum() {
        if (codigoRetornoPactoEnum == null) {
            codigoRetornoPactoEnum = CodigoRetornoPactoEnum.OUTRO;
        }
        return codigoRetornoPactoEnum;
    }

    public void setCodigoRetornoPactoEnum(CodigoRetornoPactoEnum codigoRetornoPactoEnum) {
        this.codigoRetornoPactoEnum = codigoRetornoPactoEnum;
    }

    public String getMensagemComplementar() {
        if (mensagemComplementar == null) {
            mensagemComplementar = "";
        }
        return mensagemComplementar;
    }

    public void setMensagemComplementar(String mensagemComplementar) {
        this.mensagemComplementar = mensagemComplementar;
    }
}

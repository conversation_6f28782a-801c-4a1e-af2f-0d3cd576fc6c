/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.arquitetura;

/**
 *
 * <AUTHOR>
 */
public enum ModuloAberto {

    ZILLYONWEB(1,"adm","ZW",1, false, "../imagens/banners/adm.png"),
    CRMWEB(2,"crm","CRM",2, false, "../imagens/banners/crm.png"),
    CE(3,"Central de Eventos","CE",4, false),
    FINAN(4,"financeiro","FIN",3, false, "../../imagens/banners/fin.png"),
    GESTAOSTUDIO(5,"studio","EST",4, false),
    TREINO_WEB(6,"treino","TR",5, false),
    AULA_CHEIA(7,"aula cheia","SLC",6, true),
    NFSE(8,"nota fiscal","NFSE",7, true),
    UCP(9,"Universidade Corporativa Pacto","UCP",8, true),
    COBRANCA(10,"Cobrança","CB",9, true),
    NOTAS(11,"Nota Fiscal","NOTAS",11, false),
    CANAL_CLIENTE(12,"Canal do Cliente","CANAL", 12, false),
    NOVO_TREINO(13,"treino","NTR",13, true),
    AVALIACAO_FISICA(14,"Avaliação Física","NAV",14, true),
    PACTO_PAY(15,"Pacto Pay","PAY",15, true),
    CROSS(16,"Cross","NCR",16, true),
    GRADUACAO(17,"Graduação","GRD",17, true),
    AGENDA(17,"Agenda","AGE",18, true),
    PESSOAS(18,"Pessoas","PES",19, true);

    private int codigo;
    private String descricao;
    private String sigla;
    private int ordem;
    private String imgAppGestor;
    private String imgAssinaturaDigital;
    private String imgCartaoVacina;
    private boolean zwUi;
    private String bannerHomePath;

    private ModuloAberto(int codigo, String descricao, String sigla, int ordem, boolean zwUi){
        this(codigo, descricao, sigla, ordem, zwUi, null);
    }

    private ModuloAberto(int codigo, String descricao, String sigla, int ordem, boolean zwUi, String bannerHomePath){
        setZwUi(zwUi);
        setCodigo(codigo);
        setDescricao(descricao);
        setSigla(sigla);
        this.bannerHomePath = bannerHomePath;

        if (sigla.equals("FIN") || sigla.equals("CE")) {
            imgAppGestor = "../../imagens_flat/pct-icone-fundo-app.svg";
            imgAssinaturaDigital = "../../imagens/assinaturadigital.png";
            imgCartaoVacina = "../../imagens/cartaodevacina.png";
        } else {
            imgAppGestor = "../imagens_flat/pct-icone-fundo-app.svg";
            imgAssinaturaDigital = "../imagens/assinaturadigital.png";
            imgCartaoVacina = "../imagens/cartaodevacina.png";

        }
    }

    /**
     * @return the codigo
     */
    public int getCodigo() {
        return codigo;
    }

    /**
     * @param codigo the codigo to set
     */
    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return descricao;
    }

    /**
     * @param descricao the descricao to set
     */
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getImgAppGestor() {
        return imgAppGestor;
    }

    public String getImgAssinaturaDigital() {
        return imgAssinaturaDigital;
    }

    public String getImgCartaoVacina() {
        return imgCartaoVacina;
    }

    public String getIconeModulo() {
        return "gente" + getSigla() + ".svg";
    }

    public String getLogoModulo(){
        return "logo-"+getSigla()+".svg";
    }

    public int getOrdem() {
        return ordem;
    }
    public static ModuloAberto obterPorSigla(String sigla){
        for(ModuloAberto modulo : ModuloAberto.values()){
            if(modulo.getSigla().equals(sigla)){
                return modulo;
            }
        }
        return null;
    }

    public void setOrdem(int ordem) {
        this.ordem = ordem;
    }

    public boolean isZwUi() {
        return zwUi;
    }

    public void setZwUi(boolean zwUi) {
        this.zwUi = zwUi;
    }

    public String getBannerHomePath() {
        return bannerHomePath;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.filter;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.InicioControle;
import controle.arquitetura.security.LogoutControle;
import controle.arquitetura.view.ViewPhaseListener;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.facade.jdbc.utilitarias.ConnectionSerializable;
import org.json.JSONObject;
import servicos.erro.ErroService;
import servicos.propriedades.PropsService;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class SessionFilter implements Filter {

    private final String paginaInicio = "faces/inicio.jsp";
    private final String paginaLoginZW = "faces/login.jsp";
    private final String paginaTimeout = "faces/se.jsp";
    private final String paginaPesquisa = "faces/pesquisa.jsp";
    private static final String HEALTH_PAGE = "faces/health.jsp";
    public static final String COOKIE_ZWKEY = "zwkey";
    private ServletContext context;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        context = filterConfig.getServletContext();
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;

        if ((request instanceof HttpServletRequest) && (response instanceof HttpServletResponse)) {

            httpServletRequest.getSession().setAttribute("lastURI", httpServletRequest.getRequestURI());

            Cookie cookieInicial = JSFUtilities.recuperarCookie(JSFUtilities.COOKIE_KEY, httpServletRequest);

            //Verifica se para este recurso que se deve controlar, páginas diferentes de 'login'
            //serão redirecionadas para a própria tela de login novamente
            if (isPossuiControleParaEsteRecurso(httpServletRequest)) {
                // sessão inválida?
                boolean sessaoInvalida = isSessaoInvalida(httpServletRequest);
                 //tratar cookie de FailOver (reconectar automaticamente)
                JSONObject json = null;

                if (sessaoInvalida &&  PropsService.isTrue(PropsService.cookieFailover)) {
                    Cookie[] cookies = httpServletRequest.getCookies();
                    if (cookies != null) {//cookies podem estar desabilitados no navegador :)
                        Cookie cFailOver = null;
                        for (int i = 0; i < cookies.length; i++) {
                            Cookie c = cookies[i];
                            if (c.getName().equals(JSFUtilities.COOKIE_CREDENTIALS_FAILOVER)) {
                                cFailOver = c;
                                break;
                            }
                        }
                        if (cFailOver != null) {
                            json = JSFUtilities.recuperarSessaoFailOver(cFailOver, httpServletRequest, httpServletResponse);
                        }
                    }
                }
                //fim do cookie FailOver
                if (sessaoInvalida && json == null) {
                    String timeoutUrl = httpServletRequest.getContextPath() + "/" + paginaInicio;
                    timeoutUrl = getUrlRedirect(cookieInicial, timeoutUrl);
                    httpServletResponse.sendRedirect(timeoutUrl);
                    return;
                } else if (isAcessandoRaizContexto(httpServletRequest)) {
                    String urlLogin = httpServletRequest.getContextPath() + "/" + paginaLoginZW;
                    urlLogin = getUrlRedirect(cookieInicial, urlLogin);
                    httpServletResponse.sendRedirect(urlLogin);
                    return;
                }
            } else {
                if (httpServletRequest.getRequestURI().contains(paginaInicio)) {
                    if (isJaExisteUmaConexaoIniciada(httpServletRequest) && (!isModalEmpresasAberto(httpServletRequest))) {
                        String loginURL = httpServletRequest.getContextPath() + "/" + paginaLoginZW;
                        loginURL = getUrlRedirect(cookieInicial, loginURL);
                        httpServletResponse.sendRedirect(loginURL);
                        return;
                    }
                }
            }
        }
        try {
            ViewPhaseListener.filterMenu(httpServletRequest);
            chain.doFilter(request, response);
        } catch (ServletException e) {
            if (!PropsService.isEmpty(PropsService.GCLOUD_API_KEY))
                ErroService.registrarErroGoogleStackDriver(e, httpServletRequest);
            throw e;
        } catch (IOException e) {
            if (!PropsService.isEmpty(PropsService.GCLOUD_API_KEY))
                ErroService.registrarErroGoogleStackDriver(e, httpServletRequest);
            throw e;
        }
    }

    private String getUrlRedirect(Cookie cookieInicial, String urlLogin) {
        if (cookieInicial != null && !UteisValidacao.emptyString(cookieInicial.getValue())) {
            try {
                String str = PropsService.getPropertyValue(PropsService.urlLogin) + "/" + cookieInicial.getValue();
                if (!UteisValidacao.emptyString(str) && str.contains("http")) {
                    URL url = new URL(str);
                    urlLogin = url.toString();
                }
            } catch (MalformedURLException ex) {
                Uteis.logar(ex, SessionFilter.class);
            }
        }
        return urlLogin;
    }

    private boolean isPossuiControleParaEsteRecurso(HttpServletRequest httpServletRequest) {
        String requestPath = httpServletRequest.getRequestURI();
        boolean requerControle = (!requestPath.contains(paginaInicio))
                && (!requestPath.contains(paginaTimeout))
                && (!requestPath.contains("logprotheus"))
                && (!requestPath.contains("pix/qrcode.jsp"))
                && (!requestPath.contains("assinaturaDigital.jsp"))
                && (!requestPath.contains(paginaPesquisa))
                && (!requestPath.contains("migracao_"))
                && (!requestPath.contains("doUpdateServlet"))
                && (!(requestPath.endsWith("descubraSeuFatorZW.jsp") && liberarAcessoFatorZW()))
                && (!(requestPath.endsWith(HEALTH_PAGE)));
        return requerControle;
    }

    private boolean liberarAcessoFatorZW() {
        return Boolean.parseBoolean(PropsService.getPropertyValue(PropsService.liberarAcessoFatorZW));
    }

    private boolean isAcessandoRaizContexto(HttpServletRequest httpServletRequest) {
        String paginaReq = httpServletRequest.getRequestURI();
        if (paginaReq.length() > 1) {
            paginaReq = paginaReq.substring(0, paginaReq.length() - 1);
        }
        return httpServletRequest.getContextPath().equals(paginaReq);
    }

    private boolean isJaExisteUmaConexaoIniciada(HttpServletRequest httpServletRequest) {
        ConnectionSerializable con = (ConnectionSerializable) httpServletRequest.getSession().getAttribute("con");
        boolean existe = false;
        try {
            existe = (con != null) && (con.getCon() != null) && (!con.getCon().isClosed());
        } catch (SQLException ex) {
            Logger.getLogger(SessionFilter.class.getName()).log(Level.SEVERE, null, ex);
        }

        return existe;
    }

    private boolean isSessaoInvalida(HttpServletRequest request) throws ServletException {
        final ConnectionSerializable c = (ConnectionSerializable) request.getSession().getAttribute("con");
        boolean sessaoInvalida = (request.getRequestedSessionId() == null)
                || (!request.isRequestedSessionIdValid()
                || c == null);
        if (sessaoInvalida && c == null) {
            final String key = (String) request.getSession().getAttribute("key");

            if (key != null && !key.isEmpty()) {
                try {
                    Uteis.logar(null, "VOU RECUPERAR CONEXÃO DA CHAVE " + key.toUpperCase());
                    Conexao.initSession(key);
                    Uteis.logar(null, "CONEXÃO DA CHAVE " + key.toUpperCase() + " RECUPERADA COM SUCESSO!");
                    sessaoInvalida = false;
                } catch (Exception ex) {
                    Logger.getLogger(SessionFilter.class.getName()).log(Level.SEVERE, null, ex);
                    throw new ServletException("Erro ao tentar RECUPERAR CONEXÃO DA CHAVE " + key);
                }
            }
        }
        return sessaoInvalida;
    }

    public boolean isModalEmpresasAberto(HttpServletRequest httpServletRequest) {
        InicioControle inicio = (InicioControle) httpServletRequest.getSession().getAttribute(InicioControle.class.getSimpleName());
        return inicio != null && inicio.isApresentarPanelEscolhaEmpresa();
    }

    public boolean isDeveEfetuarLogout(HttpServletRequest httpServletRequest) {
        LogoutControle logout = (LogoutControle) httpServletRequest.getSession().getAttribute(LogoutControle.class.getSimpleName());
        return logout != null && logout.isDeveEfetuarLogout();
    }

    @Override
    public void destroy() {
    }
}

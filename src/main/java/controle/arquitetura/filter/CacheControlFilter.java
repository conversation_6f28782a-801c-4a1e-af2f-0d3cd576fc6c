package controle.arquitetura.filter;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Calendar;

public class CacheControlFilter implements Filter {
    Calendar umMes = null;

    public void init(FilterConfig config) throws ServletException {
        umMes = Calendar.getInstance();
        umMes.add(Calendar.MONTH, 1);
    }

    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        long expiry = umMes.getTimeInMillis();

        HttpServletResponse httpResponse = (HttpServletResponse)response;
        httpResponse.setDateHeader("Expires", expiry);
        httpResponse.setHeader("Cache-Control", "max-age=" + (expiry/1000));

        chain.doFilter(request, response);
    }
}

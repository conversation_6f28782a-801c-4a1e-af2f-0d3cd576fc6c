/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.view;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.FacesException;
import javax.faces.event.ActionEvent;

import br.com.pactosolucoes.sms.SMSControle;
import negocio.comuns.utilitarias.Uteis;
import org.richfaces.component.html.HtmlTree;
import org.richfaces.event.NodeSelectedEvent;
import org.richfaces.model.ListRowKey;
import org.richfaces.model.TreeNode;
import org.richfaces.model.TreeNodeImpl;

/**
 *
 * <AUTHOR>
 * 
 * Controlador genérico para TreeViews
 * 
 * Utilize objeto Properties passado para o atributo "props" contendo uma estrutura como se segue:
 * 1=<PERSON>
1.1=<PERSON>
1.1.1=Start In Life
1.1.2=Slavery And Escape
1.1.3=Wrecked On A Desert Island
1.1.4=First Weeks On The Island
1.1.5=Builds A House - The Journal
1.1.6=Ill And Conscience-Stricken
1.1.7=Agricultural Experience
1.1.8=Surveys His Position
1.1.9=A Boat
1.1.10=Tames Goats
1.1.11=Finds Print Of Man's Foot On The Sand
1.1.12=A Cave Retreat
1.1.13=Wreck Of A Spanish Ship
1.1.14=A Dream Realised
1.1.15=Friday's Education
1.1.16=Rescue Of Prisoners From Cannibals
1.1.17=Visit Of Mutineers
1.1.18=The Ship Recovered
1.1.19=Return To England
1.1.20=Fight Between Friday And A Bear
2=Alleck Balduin
2.1=Plays
2.1.1=Politian
2.2=Short stories
2.2.1=The Assignation
 *      ...
 * E assim sucessivamente...
 */
public class TreeViewControle implements Serializable{

    private TreeNode rootNode = null;
    private List<TreeViewNode> selectedNodeChildren = new ArrayList<TreeViewNode>();
    private List<TreeViewNode> nodesMarcados = new ArrayList<TreeViewNode>();
    private TreeViewNode nodeSelecionado;
    private Map<String, TreeViewNode> props;
    private boolean exibirListaNodesMarcados = true;

    private boolean marcarTodos = false;

    private void addNodes(String path, TreeNode node, Map<String, TreeViewNode> mapaObjetos) throws
            Exception {
        try {
            boolean end = false;
            int counter = 1;

            while (!end) {
                String key = path != null ? path + '.' + counter : String.valueOf(counter);
                TreeViewNode obj = mapaObjetos.get(key);

                if (obj != null) {
                    TreeNodeImpl nodeImpl = new TreeNodeImpl();
                    nodeImpl.setData(obj);
                    node.addChild(new Integer(counter), nodeImpl);
                    addNodes(key, nodeImpl, mapaObjetos);
                    counter++;
                } else {
                    end = true;
                }
            }
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro addNodes - TreeViewControle " + ex.getMessage());
            throw ex;
        }
    }

    private void loadTree() {
        try {
            if (props != null) {
                rootNode = new TreeNodeImpl();
                addNodes(null, rootNode, props);
            }

        } catch (Exception e) {
            throw new FacesException(e.getMessage(), e);
        } finally {
        }
    }

    public void atualizarSelecionados() {
        nodesMarcados.clear();
        updateMarcadosRecursive(rootNode);
    }

    private void updateMarcadosRecursive(TreeNode nodePai) {
        try {
            Iterator<Map.Entry<TreeViewNode, TreeNode>> it = nodePai.getChildren();

            while (nodePai != null && it != null && it.hasNext()) {
                Map.Entry<TreeViewNode, TreeNode> entry = it.next();
                TreeViewNode nodExit = (TreeViewNode) entry.getValue().getData();
                if (nodExit.isMarcado()) {
                    nodesMarcados.add(nodExit);
                }
                updateMarcadosRecursive(entry.getValue());
            }
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro updateMarcadosRecursive - TreeViewControle " + ex.getMessage());
            throw ex;
        }
    }

    public void processSelection(ActionEvent event) {
        /*
         * Atenção: A composição do artefato include_arvore_grupo_colaboradores.jsp pode gerar NPE,
         * caso a composição dos componentes de rich:treeNode sejam alterados.
         * Fazendo com que a sequência event.getComponent().getParent().getParent().getParent().getParent(),
         * pare de funcionar.
         *
         */
        try {
            HtmlTree tree = (HtmlTree) event.getComponent().getParent().getParent().getParent().getParent();

            if (tree != null) {
                nodeSelecionado = (TreeViewNode) event.getComponent().getAttributes().get("nodeSelecionado");
                nodesMarcados.clear();
                boolean marcarOuDesmarcar = !nodeSelecionado.isMarcado();
                nodeSelecionado.setMarcado(marcarOuDesmarcar && nodeSelecionado.getObjeto() != null);

                //
                TreeNode currentNode = tree.getModelTreeNode(tree.getRowKey());
                if (currentNode.isLeaf()) {
                    TreeViewNode node = (TreeViewNode) currentNode.getData();
                    node.setMarcado(marcarOuDesmarcar);
                    atualizarSelecionados();
                    selectedNodeChildren.add(node);

                } else {
                    Iterator<Map.Entry<TreeViewNode, TreeNode>> it = currentNode.getChildren();
                    while (it != null && it.hasNext()) {
                        Map.Entry<TreeViewNode, TreeNode> entry = it.next();
                        TreeViewNode node = (TreeViewNode) entry.getValue().getData();
                        node.setMarcado(!node.isMarcado());
                        atualizarSelecionados();
                        selectedNodeChildren.add(node);
                    }
                }
            }
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro processSelection - TreeViewControle " + ex.getMessage());
            throw ex;
        }
    }

    public void processSelectionMeta(ActionEvent event) {
        try {
            HtmlTree tree = (HtmlTree) event.getComponent().getParent().getParent().getParent().getParent();
            nodeSelecionado = (TreeViewNode) event.getComponent().getAttributes().get("nodeSelecionado");
            processSelectionNodeTree(tree, nodeSelecionado);
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro processSelectionNodeAction - TreeViewControle " + ex.getMessage());
            throw ex;
        }
    }

    public void processSelectionMetaTree(NodeSelectedEvent event) {
        try {
            HtmlTree tree = (HtmlTree) event.getComponent();
            processSelectionNodeTree(tree, null);
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro processSelectionNode - TreeViewControle " + ex.getMessage());
            throw ex;
        }
    }

    private void processSelectionNodeTree(HtmlTree tree, TreeViewNode nodeSel) {
        if (tree == null) {
            return;
        }
        nodeSelecionado = nodeSel;
        if (nodeSelecionado == null) {
            nodeSelecionado = (TreeViewNode) tree.getModelTreeNode().getData();
        }
        if (nodeSelecionado == null || nodeSelecionado.getObjeto() == null) {
            return;
        }
        nodesMarcados.clear();
        boolean marcarOuDesmarcar = !nodeSelecionado.isMarcado();
        nodeSelecionado.setMarcado(marcarOuDesmarcar && nodeSelecionado.getObjeto() != null);

        TreeNode currentNode = tree.getModelTreeNode(tree.getRowKey());
        if (currentNode.isLeaf()) {
            TreeViewNode node = (TreeViewNode) currentNode.getData();
            node.setMarcado(marcarOuDesmarcar);
            atualizarSelecionados();
            selectedNodeChildren.add(node);

        } else {
            Iterator<Map.Entry<TreeViewNode, TreeNode>> it = currentNode.getChildren();
            while (it != null && it.hasNext()) {
                Map.Entry<TreeViewNode, TreeNode> entry = it.next();
                TreeViewNode node = (TreeViewNode) entry.getValue().getData();
                node.setMarcado(!node.isMarcado());
                atualizarSelecionados();
                selectedNodeChildren.add(node);
            }
        }
    }

    public TreeNode getTreeNode() {
        if (rootNode == null) {
            loadTree();
        }

        return rootNode;
    }

    public TreeViewNode getNodeSelecionado() {
        return nodeSelecionado;
    }

    public void setNodeSelecionado(TreeViewNode nodeSelecionado) {
        this.nodeSelecionado = nodeSelecionado;
    }

    public Map<String, TreeViewNode> getProps() {
        return props;
    }

    public void setProps(Map<String, TreeViewNode> props) {
        this.props = props;
    }

    public List<TreeViewNode> getNodesMarcados() {
        return nodesMarcados;
    }

    public void setNodesMarcados(List<TreeViewNode> nodesMarcados) {
        this.nodesMarcados = nodesMarcados;
    }    

    public boolean isExibirListaNodesMarcados() {
        return exibirListaNodesMarcados;
    }

    public void setExibirListaNodesMarcados(boolean exibirListaNodesMarcados) {
        this.exibirListaNodesMarcados = exibirListaNodesMarcados;
    }

    public void marcarNenhumOuTodosListener(ActionEvent evt) {
        try {
            Boolean opcao = (Boolean) evt.getComponent().getAttributes().get("opcao");
//        Uteis.logar(null, "Inicio");
            if (opcao != null) {
                setMarcarTodos(opcao);
                marcarOuDesmarcarTodos(opcao, rootNode);
                atualizarSelecionados();
            }
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro marcarNenhumOuTodosListener - TreeViewControle " + ex.getMessage());
            throw ex;
        }
//        Uteis.logar(null, "Fim");
    }

    private void marcarOuDesmarcarTodos(boolean opcao, TreeNode nodePai) {
        try {
            Iterator<Map.Entry<TreeViewNode, TreeNode>> it = nodePai.getChildren();
            while (nodePai != null && it != null && it.hasNext()) {
                Map.Entry<TreeViewNode, TreeNode> entry = it.next();
                TreeViewNode nodExit = (TreeViewNode) entry.getValue().getData();
                nodExit.setMarcado(opcao && nodExit.getObjeto() != null);
                marcarOuDesmarcarTodos(opcao, entry.getValue());
            }
        } catch (Exception ex){
            Logger.getLogger(TreeViewControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            Uteis.logar("Erro marcarOuDesmarcarTodos - TreeViewControle " + ex.getMessage());
            throw ex;
        }
    }

    public void limpar(){
        rootNode = null;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }
}

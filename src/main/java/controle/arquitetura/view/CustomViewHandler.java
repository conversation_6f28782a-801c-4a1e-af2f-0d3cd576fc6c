/*
 * To change super template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.view;

import com.sun.faces.application.ViewHandlerImpl;
import java.io.IOException;
import java.util.Locale;
import javax.faces.FacesException;
import javax.faces.component.UIViewRoot;
import javax.faces.context.FacesContext;

/**
 *
 * <AUTHOR>
 */
public class CustomViewHandler extends ViewHandlerImpl {

    @Override
    public UIViewRoot restoreView(FacesContext fc, String viewId) {
        UIViewRoot root = null;

        root = super.restoreView(fc, viewId);

        if (root == null) {

            root = super.createView(fc, viewId);

        }

        return root;
    }

    @Override
    public Locale calculateLocale(FacesContext context) {
        return super.calculateLocale(context);
    }

    @Override
    public String calculateRenderKitId(FacesContext context) {
        return super.calculateRenderKitId(context);
    }

    @Override
    public UIViewRoot createView(FacesContext context, String viewId) {
        return super.createView(context, viewId);
    }

    @Override
    public String getActionURL(FacesContext context, String viewId) {
        return super.getActionURL(context, viewId);
    }

    @Override
    public String getResourceURL(FacesContext context, String path) {
        return super.getResourceURL(context, path);
    }

    @Override
    public void renderView(FacesContext context, UIViewRoot viewToRender) throws IOException, FacesException {
        super.renderView(context, viewToRender);
    }

    @Override
    public void writeState(FacesContext context) throws IOException {
        super.writeState(context);
    }
}

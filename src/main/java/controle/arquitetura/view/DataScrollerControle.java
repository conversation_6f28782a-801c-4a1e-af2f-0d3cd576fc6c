/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.view;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import org.richfaces.component.UIDatascroller;

import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public class DataScrollerControle implements Serializable{

    private transient UIDatascroller dataScroller = null;
    private Integer nrRows = 10;
    private Integer[] itensScroll;

    private enum RegistrosPorPagina {

        QUANTIDADE200(0, new Integer[]{10, 15, 25, 50, 100, 200}),
        QUANTIDADE500(1, new Integer[]{10, 15, 25, 50, 100, 200, 500});

        private Integer codigo;
        private Integer[] descricao;

        RegistrosPorPagina(Integer codigo, Integer[] descricao) {
            this.codigo = codigo;
            this.descricao = descricao;
        }

        public Integer getValor() {
            return this.codigo;
        }

        public Integer[] getDescricao() {
            return descricao;
        }
    }

    public void resetDatascroller() {

        if (dataScroller != null) {
            getDataScroller().setPage(1);
            getDataScroller().getDataTable().setFirst(0);
        }
    }

    public List<SelectItem> getItensScroll() {
        List<SelectItem> list = new ArrayList<SelectItem>(itensScroll.length);
        for (Integer i : itensScroll) {
            list.add(new SelectItem(i, i.toString()));
        }
        return list;
    }

    public List<SelectItem> getItensScroll200() {
        this.itensScroll = RegistrosPorPagina.QUANTIDADE200.getDescricao();
        return getItensScroll();
    }

    public DataScrollerControle() {
        this.itensScroll = RegistrosPorPagina.QUANTIDADE500.getDescricao();
    }

    public UIDatascroller getDataScroller() {
        return dataScroller;
    }

    public void setDataScroller(UIDatascroller dataSroller) {
        this.dataScroller = dataSroller;
    }

    public Integer getNrRows() {
        return nrRows;
    }

    public void setNrRows(Integer nrRows) {
        this.nrRows = nrRows;
    }
}
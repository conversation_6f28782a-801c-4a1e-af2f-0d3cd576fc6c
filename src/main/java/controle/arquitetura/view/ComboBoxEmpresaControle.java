/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.view;

import controle.arquitetura.SuperControle;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class ComboBoxEmpresaControle extends SuperControle {

    private EmpresaVO empresaVO = new EmpresaVO();
    private List listaSelectItemEmpresa = new ArrayList();
    private String gatilhosFuncoes;

    public ComboBoxEmpresaControle() {
        setMensagemDetalhada("");
        try {
            inicializarEmpresaLogado();
            montarListaSelectItemEmpresa();
        } catch (Exception ex) {
            setMensagemDetalhada(ex.getMessage());
            Logger.getLogger(ComboBoxEmpresaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (getEmpresaVO().getCodigo().intValue() == 0) {
                List objs = new ArrayList();
                objs.add(new SelectItem(new Integer(0), ""));
                List resultadoConsulta = getFacade().getEmpresa().consultarPorCodigo(0,true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Iterator i = resultadoConsulta.iterator();
                while (i.hasNext()) {
                    EmpresaVO obj = (EmpresaVO) i.next();
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
                setListaSelectItemEmpresa(objs);
            }
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList());
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void inicializarEmpresaLogado() throws Exception {
        if (getEmpresaLogado() != null && getEmpresaLogado().getCodigo().intValue() != 0) {
            getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
            getEmpresaVO().setNome(getEmpresaLogado().getNome());
        }
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public void selecionarEmpresa() {
        //faz nada mesmo!
    }

    public String getGatilhosFuncoes() {
        return gatilhosFuncoes;
    }
  
    /**
     *
     * @param gatilhos: os gatilhos são nomes de funções (a4j:jsFunction)
     * que são disparados no evento 'oncomplete' de um componente A4J.
     * Eles deverão ser registrados separados por ';' (ponto-e-virgula).
     */
    public void registrarGatilhos(String... gatilhos){
        String resultado = "";
        for (int i = 0; i < gatilhos.length; i++) {
            String gat = gatilhos[i];
            resultado += gat + "();";
        }
        this.gatilhosFuncoes = resultado;
    }
}

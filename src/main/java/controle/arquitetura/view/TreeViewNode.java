/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.view;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class TreeViewNode implements Serializable {

    private boolean marcado = false;
    private String css = "";
    private Object objeto;
    private String descricao = "";

    public TreeViewNode(Object objNegocio, String descricao) {
        this.objeto = objNegocio;
        this.descricao = descricao;
    }

    public boolean isMarcado() {
        return marcado;
    }

    public void setMarcado(boolean marcado) {
        this.marcado = marcado;
    }

    public String getCss() {
        return css;
    }

    public void setCss(String css) {
        this.css = css;
    }

    public Object getObjeto() {
        return objeto;
    }

    public void setObjeto(Object objeto) {
        this.objeto = objeto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

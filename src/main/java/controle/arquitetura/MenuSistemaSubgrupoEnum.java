package controle.arquitetura;

public enum MenuSistemaSubgrupoEnum {

    ADM_OPERACOES_ESTOQUE("Operações de Estoque"),
    ADM_OPERACOES_GESTAO("Operações de Gestão"),
    ADM_OPERACOES_VENDA("Operações de Venda"),
    ADM_OUTRAS_OPERACOES("Outras Operações"),
    ADM_RELATORIOS_ACESSO("Relatórios de Acessos"),
    ADM_RELATORIOS_CLIENTES("Relatórios de Clientes"),
    ADM_RELATORIOS_COMISSAO("Relatórios de Comissão"),
    ADM_RELATORIOS_ESTATISTICO("Relatórios Estatístico"),
    ADM_RELATORIOS_FINANCEIRO("Relatórios Financeiro"),
    ADM_RELATORIOS_TURMAS("Relatórios de Turmas"),
    ADM_OUTROS_RELATORIOS("Outros Relatórios"),
    ADM_CADASTROS_ACESSO("Cadastros de Acesso"),
    ADM_CADASTROS_CLUBE_DE_VANTAGENS("Cadastros Clube de Vantagens"),
    ADM_CADASTROS_CONTRATO("Cadastros de Contrato"),
    ADM_CADASTROS_FINANCEIROS("Cadastros Financeiros"),
    ADM_CADASTROS_PLANOS("Cadastros de Planos"),
    ADM_CADASTROS_PRODUTOS("Cadastros de Produtos"),
    ADM_CADASTROS_TURMAS("Cadastros de Turmas"),
    ADM_OUTROS_CADASROS("Outros Cadastros"),
    FIN_OPERACOES_CAIXA("Caixa"),
    FIN_OPERACOES_CONTA("Conta"),
    FIN_OPERACOES_RECEBIVEIS("Recebíveis"),
    TREINO_OPERACOES_GESTAO_PERSONAL("Gestão de Personal"),
    PACTOPAY_REGUA_COBRANCA("Regua de cobrança"),
    ;

    private String descricao;


    MenuSistemaSubgrupoEnum(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }


}

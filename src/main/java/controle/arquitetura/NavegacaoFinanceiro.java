package controle.arquitetura;

import controle.financeiro.MetaFinanceiroBIControle;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.util.Iterator;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.utilitarias.Uteis;

/**
 * Classe que contém os metodos para abrir as telas de navegação
 * <AUTHOR>
 */
public class NavegacaoFinanceiro extends SuperControle {

    /**
     * Abre a tela CE
     * @return loginCE
     */
    public String abrirTelaInicial() {
        return "financeiro";
    }

    public String abrirTelaCadastro() {
        return "cadastrosFinan";
    }


    public String abrirTelaBI() {
    	try {
    		 MetaFinanceiroBIControle metaFinanceiroBIControle = (MetaFinanceiroBIControle) JSFUtilities.getFromSession(MetaFinanceiroBIControle.class);
    		 if(metaFinanceiroBIControle == null){
    			 metaFinanceiroBIControle = new MetaFinanceiroBIControle();
    		 }
    		 metaFinanceiroBIControle.inicializarDados();
    		 JSFUtilities.storeOnSession(MetaFinanceiroBIControle.class.getSimpleName(),
    				 metaFinanceiroBIControle);
		} catch (Exception e) {
			montarMsgAlert(e.getMessage());
		}
        return "metaFinanceiro";
    }
    
  
    public String inicializarRelatorios() throws Exception {
        if (validarPermissoes()) {
            return "relatoriosFinan";
        }
        return "";
    }

    public boolean validarPermissoes() {
        try {
            validarPermissaoVisualizarTodosRelatorios();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * Valida a permissão do usuário logado visualizar todos os relatórios
     * @throws Exception
     */
    public void validarPermissaoVisualizarTodosRelatorios() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "VisualizarRelatorios", "9.14 - Visualizar Relatórios");
            }
        }
    }
}

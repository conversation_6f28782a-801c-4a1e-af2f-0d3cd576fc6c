/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.threads;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;

/**
 *
 * <AUTHOR>
 */
public class ThreadFechamentoCaixaAutomatico{

    private Connection con;
    private Usuario userDao;
    private String chave;
    
    public static void main(String ... args) throws Exception{
        Connection con = DriverManager.getConnection("********************************************", "postgres", "pactodb");
        ThreadFechamentoCaixaAutomatico t = new ThreadFechamentoCaixaAutomatico(con, "metabolismo");
        t.fecharCaixas();
    }

    public ThreadFechamentoCaixaAutomatico(Connection con, String chave) throws Exception {
        this.con = con;
        this.chave = chave;
        userDao = new Usuario(con);
    }
    
    public void fecharCaixas() {
        try {
            ResultSet rsConf = SuperFacadeJDBC.criarConsulta("select fecharcaixaautomaticamente from configuracaofinanceiro ", con);
            if (rsConf.next() && rsConf.getBoolean("fecharcaixaautomaticamente")) {
                UsuarioVO usuarioRecor = userDao.getUsuarioRecorrencia();
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from caixa  where datafechamento  is null", con);
                while (rs.next()) {
                    System.out.println("VOU FECHAR O CAIXA "+rs.getInt("codigo")+" da chave "+chave);
                    SuperFacadeJDBC.executarConsulta("update caixa set datafechamento = '"
                            + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss")
                            + "', responsavelfechamento = " + usuarioRecor.getCodigo() + " where codigo = " + rs.getInt("codigo"), con);
                }
            }

        } catch (Exception e) {
            Uteis.logar(e, ThreadFechamentoCaixaAutomatico.class);
        }
    }
}

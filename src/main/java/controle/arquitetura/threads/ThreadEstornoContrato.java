package controle.arquitetura.threads;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.oamd.OAMDService;

import java.sql.Connection;

public class ThreadEstornoContrato extends Thread {

    private final ContratoVO contratoVO;
    private final ClienteVO clienteVO;
    private final UsuarioVO usuarioVO;
    private final String chaveZW;
    private final Connection con;
    private String msg = "";

    public ThreadEstornoContrato(Connection con, ContratoVO contratoVO, ClienteVO clienteVO, UsuarioVO usuarioVO, String msg, String chaveZW) throws Exception {
        this.con = con;
        this.contratoVO = contratoVO;
        this.clienteVO = clienteVO;
        this.usuarioVO = usuarioVO;
        this.msg = msg;
        this.chaveZW = chaveZW;
    }

    @Override
    public void run() {
        Contrato contratoDAO = null;
        Cliente clienteDAO = null;
        ZillyonWebFacade zwDAO = null;
        Log logDAO = null;
        try (Connection connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            contratoDAO = new Contrato(connection);
            clienteDAO = new Cliente(connection);
            zwDAO = new ZillyonWebFacade(connection);
            logDAO = new Log(connection);

            Uteis.logarDebug("Iniciando Thread Estorno de Contratos: " + Uteis.getDataComHora(Calendario.hoje()));

            //ESTORNAR CONTRATO
            contratoDAO.estornoContrato(this.contratoVO, this.clienteVO, null, "Estorno Automático - Vendas Online");

            zwDAO.atualizarSintetico(clienteDAO.consultarPorCodigo(this.clienteVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            logDAO.incluirLogEstornoContratoSite(this.clienteVO.getCodigo(), this.usuarioVO, this.msg);
            if (!UteisValidacao.emptyString(this.contratoVO.getNumeroCupomDesconto())) {
                OAMDService oamdService = new OAMDService();
                oamdService.cancelarUtilizacaoCupomDesconto(this.contratoVO.getNumeroCupomDesconto(), this.chaveZW);
                oamdService.informarContratoEstornadoHistoricoUtilizacaoCupom(this.contratoVO.getCodigo(), this.chaveZW);
                oamdService = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            try {
                throw e;
            } catch (Exception ex) {
                throw new RuntimeException(ex);
            }
        } finally {
            Uteis.logarDebug("Terminando Thread Estorno de Contratos: " + Uteis.getDataComHora(Calendario.hoje()));
            Thread.currentThread().interrupt();
        }
    }
}


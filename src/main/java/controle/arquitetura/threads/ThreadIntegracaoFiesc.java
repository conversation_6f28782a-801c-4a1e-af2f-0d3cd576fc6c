package controle.arquitetura.threads;

import br.com.pactosolucoes.bi.dto.FiltroDTO;
import br.com.pactosolucoes.bi.dto.KeyCodFormJson;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

public class ThreadIntegracaoFiesc implements Runnable {
    private String key;
    private String msgErro = "";
    private int codigoPessoa;

    private int codigoEntidade;
    private int codEmpresa;
    private String data;
    private boolean terminouExecucao = false;
    private String operacao = "ADD";

    private String notaBase64;

    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_ACCEPT = "Accept";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String CONTENT = "content";
    private List<Integer> listaCodigosEntidades = new ArrayList<Integer>();

    public ThreadIntegracaoFiesc(String key, int codigoPessoa) {
        this.key = key;
        this.codigoPessoa = codigoPessoa;
        this.terminouExecucao = false;
    }

    public ThreadIntegracaoFiesc(String key, int codEmpresa, String data, String acao) {
        this.key = key;
        this.codEmpresa = codEmpresa;
        this.data = data;
        this.operacao = acao;
        this.terminouExecucao = false;
    }

    public ThreadIntegracaoFiesc(String key, int codEmpresa, List<Integer> listaCodigosEntidades, String acao){
        this.key = key;
        this.listaCodigosEntidades = listaCodigosEntidades;
        this.operacao = acao;
        this.codEmpresa = codEmpresa;
    }

    public ThreadIntegracaoFiesc(String key, int codigoEntidade, int codEmpresa,  String acao){
        this.key = key;
        this.codigoEntidade = codigoEntidade;
        this.operacao = acao;
        this.codEmpresa = codEmpresa;
    }


    public void run() {
        try {
            Uteis.logarDebug(getClass().getName() + " operação Sesi: " + operacao);
            switch (operacao) {
                case "ADD":
                    executarRequestIntegracaoFiesc(this.key, codigoPessoa, "integracao-sesi/pessoa", ExecuteRequestHttpService.METODO_POST);
                    break;
                case "gerarNotaFiscal":
                    executarRequestIntegracaoFiescGeraNotaFiscal(this.key, codEmpresa, data, "integracao-sesi/emitirNotaFiscal", ExecuteRequestHttpService.METODO_POST);
                    break;
                case "incluirPix":
                    executarRequestIntegracaoFiescListaCodigos(this.key, codEmpresa, listaCodigosEntidades, "integracao-sesi/integracaoPix");
                    break;
                case "integrarTesourariaDinheiro":
                    executarRequestIntegracaoFiescData(this.key, codEmpresa, data, "integracao-sesi/integracaoTesourariaDinheiro");
                    break;
                case "consultaIntegracaoPix":
                    executarRequestIntegracaoFiescConsultaIntegracao(this.key, codEmpresa, null, "integracao-sesi/consultaIntegracaoPix");
                    break;
                case "consultaIntegracaoTesourariaDinheiro":
                    executarRequestIntegracaoFiescConsultaIntegracao(this.key, codEmpresa, null, "integracao-sesi/consultaIntegracaoTesouraria");
                    break;
                case "consultarNotaFiscal":
                    executarRequestIntegracaoFiescConsultaNotas(this.key, codEmpresa, null, "integracao-sesi/consultaNotas");
                    break;
                case "incluirCartao":
                    executarRequestIntegracaoFiescListaCodigos(this.key, codEmpresa, listaCodigosEntidades, "integracao-sesi/integracaoCartao");
                    break;
                case "incluirDeposito":
                    executarRequestIntegracaoFiescListaCodigos(this.key, codEmpresa, listaCodigosEntidades,"integracao-sesi/integracaoDeposito");
                    break;
                case "consultaIntegracaoDeposito":
                    executarRequestIntegracaoFiescConsultaIntegracao(this.key, codEmpresa, null, "integracao-sesi/consultaIntegracaoDeposito");
                    break;
                case "imprimirNota":
                    executarRequestIntegracaoSesi(this.key, codigoEntidade, codEmpresa, "integracao-sesi/imprimirNota");
                    break;
                case "errosCartao":
                    executarRequestIntegracaoFiescData(this.key, codEmpresa, data, "integracao-sesi/reprocessarCartoesComProblema");
                    break;
                case "errosPix":
                    executarRequestIntegracaoFiescData(this.key, codEmpresa, data, "integracao-sesi/reprocessarPixComProblema");
                    break;
                case "errosDeposito":
                    executarRequestIntegracaoFiescData(this.key, codEmpresa, data, "integracao-sesi/reprocessarDepositoComProblema");
                    break;
                case "errosTesouraria":
                    executarRequestIntegracaoFiescData(this.key, codEmpresa, data, "integracao-sesi/reprocessarTesourariaComProblema");
                    break;
            }

    } catch (Exception e) {
            Uteis.logar(e,ThreadIntegracaoFiesc.class);
        }
    }

    private FiltroDTO executarRequestIntegracaoFiesc(String key, Integer cod, String metodo, String metodoHTTP) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        return getConnectionSesi(key, cod, path);
    }

    private FiltroDTO executarRequestIntegracaoFiescGeraNotaFiscal(String key, int codEmpresa, String data ,String metodo, String metodoHTTP) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        return getConnectionSesiGeraNotaFiscal(key,codEmpresa, data, path);
    }

    private FiltroDTO executarRequestIntegracaoFiescData(String key, int codEmpresa, String data , String metodo) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        return getConnectionSesiIntegrarData(key,codEmpresa, data, path);
    }

    private FiltroDTO executarRequestIntegracaoFiescListaCodigos(String key, int codEmpresa, List<Integer> listaCodigos , String metodo) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        return getConnectionSesiIntegracaoListaCodigos(key, codEmpresa,  listaCodigos, path);
    }

    private FiltroDTO executarRequestIntegracaoFiescConsultaIntegracao(String key, int codEmpresa, String data ,String metodo) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        return getConnectionSesiConsultaIntegracao(key,codEmpresa, data, path);
    }

    private FiltroDTO executarRequestIntegracaoFiescConsultaNotas(String key, int codEmpresa, String data ,String metodo) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        return getConnectionSesiConsultaNotas(key,codEmpresa, data, path);
    }

    public FiltroDTO getConnectionSesi(String key, Integer cod, String path) throws Exception {
        KeyCodFormJson keyCod = new KeyCodFormJson(key, cod);
        JSONObject jObject = new JSONObject();
        jObject.put("key", key);
        jObject.put("codigo", cod);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }


    public FiltroDTO executarRequestIntegracaoSesi(String key, Integer codigoEntidade, Integer codigoEmpresa, String metodo) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesMs)  + "/" + metodo;
        KeyCodFormJson keyCod = new KeyCodFormJson(key, codigoEntidade, codigoEmpresa);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            if(metodo.contains("imprimirNota")){
                this.setNotaBase64(json.getString(CONTENT));
                return null;
            }
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }

    public FiltroDTO getConnectionSesiGeraNotaFiscal(String key, int codEmpresa, String data, String path) throws Exception {
        String[] dataSplint =data.split("-");
        Integer mes = Integer.valueOf(dataSplint[1]);
        Integer ano = Integer.valueOf(dataSplint[0]);

        KeyCodFormJson keyCod = new KeyCodFormJson(key, codEmpresa, mes, ano);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }

    public FiltroDTO getConnectionSesiIntegracaoListaCodigos(String key, int codEmpresa , List<Integer> listaCodigos, String path) throws Exception {
        KeyCodFormJson keyCod = new KeyCodFormJson(key,codEmpresa, listaCodigos);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);

        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }


    public FiltroDTO getConnectionSesiIntegrarData(String key, int codEmpresa, String data, String path) throws Exception {
        KeyCodFormJson keyCod = new KeyCodFormJson(key, codEmpresa, data);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }

    public FiltroDTO getConnectionSesiConsultaIntegracao(String key, int codEmpresa , String data, String path) throws Exception {
        KeyCodFormJson keyCod = new KeyCodFormJson(key,codEmpresa,data);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }



    public FiltroDTO getConnectionSesiConsultaNotas(String key, int codEmpresa , String data, String path) throws Exception {
        KeyCodFormJson keyCod = new KeyCodFormJson(key,codEmpresa,data);
        StringEntity entity = new StringEntity(new JSONObject(keyCod).toString(), ContentType.APPLICATION_JSON);
        URL url = new URL(path);
        HttpPost httpPost = new HttpPost(path);
        httpPost.setEntity(entity);
        httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
        httpPost.setHeader(HEADER_AUTHORIZATION, key);
        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        Uteis.logarDebug(getClass().getName() + "URL operação Sesi: " + url +"\nStatusCode: " + statusCode+
                "\nbody: " + new JSONObject(keyCod).toString() +"\nResponse operação Sesi: " + responseBody);
        JSONObject json = new JSONObject(responseBody);
        if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
            return JSONMapper.getObject(json.getJSONObject(CONTENT), FiltroDTO.class);
        } else {
            throw new Exception(responseBody);
        }
    }

    public String getNotaBase64() {
        return notaBase64;
    }

    public void setNotaBase64(String notaBase64) {
        this.notaBase64 = notaBase64;
    }
}

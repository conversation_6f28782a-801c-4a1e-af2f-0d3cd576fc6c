package controle.arquitetura.threads;

import br.com.pactosolucoes.enumeradores.TipoVigenciaMyWellnessGymPassEnum;
import com.google.api.client.json.Json;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.json.JSONObject;
import servicos.integracao.mywellness.json.IntegracaoMyWellnessJSON;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

public class ThreadMyWellnes extends Thread {

    private transient Connection con;
    private String key;
    private String msgErro = "";
    private String origem;
    private int codigoEntidade;
    private boolean terminouExecucao = false;
    private boolean acesso = false;
    private boolean forcarAtualizacao = false;
    private boolean enviarContratoAnterior = false;
    private String operacao = "ADD";

    public ThreadMyWellnes(Connection con, String key, String origem, int codigoEntidade, boolean acesso) {
        this.con = con;
        this.key = key;
        this.origem = origem;
        this.codigoEntidade = codigoEntidade;
        this.terminouExecucao = terminouExecucao;
        this.acesso = acesso;
    }

    public ThreadMyWellnes(Connection con, String key, String origem, int codigoEntidade, boolean acesso, String operacao) {
        this.con = con;
        this.key = key;
        this.origem = origem;
        this.codigoEntidade = codigoEntidade;
        this.terminouExecucao = terminouExecucao;
        this.acesso = acesso;
        this.operacao = operacao;
    }

    public ThreadMyWellnes(Connection con, String key, String origem, int codigoEntidade, boolean acesso, String operacao, boolean forcarAtualizacao , boolean enviarContratoAnterior) {
        this.con = con;
        this.key = key;
        this.origem = origem;
        this.codigoEntidade = codigoEntidade;
        this.terminouExecucao = terminouExecucao;
        this.acesso = acesso;
        this.operacao = operacao;
        this.forcarAtualizacao = forcarAtualizacao;
        this.enviarContratoAnterior= enviarContratoAnterior;
    }

    public void run() {
        Date dataTentativa = Calendario.hoje();
        Date dataSicronizacao = null;
        String erroServico ="";
        List<IntegracaoMyWellnessJSON> listaIntegracaoJson = new ArrayList<>();


        try {
            if (origem == "CL") {
                listaIntegracaoJson = montarDadosCliente();
            } else {
                listaIntegracaoJson = montarDadosColaborador();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return;
        }
        for (IntegracaoMyWellnessJSON integracaoJson : listaIntegracaoJson) {
            try {
                if(operacao.equals("ADD")) {
                    if(!forcarAtualizacao && origem == "CL" && jasincronizado(integracaoJson.toJSON().toString())){
                        return;
                    }

                    String retorno = executarRequestIntegracaoMyWellness(integracaoJson.toJSON().toString(), "mywellnessservice", ExecuteRequestHttpService.METODO_POST);
                    JSONObject jsonRetorno = new JSONObject(retorno);
                    if(jsonRetorno.optString("status", "success").equalsIgnoreCase("success") || !UteisValidacao.emptyString(jsonRetorno.optString("permanentToken"))){
                        persistirUserIdExterno(integracaoJson, jsonRetorno);
                    }
                    if (jsonRetorno.optString("status", "success").equalsIgnoreCase("success")) {
                        dataSicronizacao = Calendario.hoje();
                    } else {
                        erroServico = jsonRetorno.getString("erros");
                    }

                    incluirLogIntegracao(integracaoJson, dataTentativa, dataSicronizacao, false, erroServico,jsonRetorno);
                } else {
                    deletarPessoa(integracaoJson);
                }
            } catch (Exception e) {
                try {
                    incluirLogIntegracao(integracaoJson,dataTentativa, null, true, "Exceção ZW: "+ e.getMessage(), new JSONObject());
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        }

    }

    private boolean jasincronizado(String json) {
        String sql = "select codigo from (select * from integracaomywellness  where codigoentidade =  ? and origem = 'CL' order by codigo desc limit 1) as foo where foo.dtsincronizacaoapi  is not null and json = ? ";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoEntidade);
            sqlConsultar.setString(2, json);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return true;
                }
                return false;
            }
        } catch (SQLException e) {
            return false;
        }

    }

    private void deletarPessoa(IntegracaoMyWellnessJSON integracaoJson) throws Exception {
        integracaoJson.setOperacao("DELETE");
        String errosServico ="";
        Date dataSicronizacao  = null;
        String retorno = executarRequestIntegracaoMyWellness(integracaoJson.toJSON().toString(), "mywellnessservice", ExecuteRequestHttpService.METODO_DELETE);
        JSONObject jsonRetorno = new JSONObject(retorno);
        if (jsonRetorno.optString("status", "success").equalsIgnoreCase("success")) {
            dataSicronizacao = Calendario.hoje();
            deletarTokenIntegracao(integracaoJson);
        } else {
            errosServico = jsonRetorno.getString("erros");
        }

        incluirLogIntegracao(integracaoJson, Calendario.hoje() , dataSicronizacao, false, errosServico, jsonRetorno);
    }

    private void deletarTokenIntegracao(IntegracaoMyWellnessJSON integracaoJson) throws SQLException {
        String sqlExcluir = "delete from integracaomywellnessusertoken  where ";
        if(origem == "CL") {
            sqlExcluir += " pessoa = "+integracaoJson.getPessoa().getCodPessoa()+" and facilityurl='"+ integracaoJson.getFacilityUrl()+"';";
        } else {
            sqlExcluir += " colaborador = "+integracaoJson.getPessoa().getCodColaborador()+" and facilityurl='"+ integracaoJson.getFacilityUrl()+"';";
        }
        SuperFacadeJDBC.executarUpdate(sqlExcluir,con);
    }

    private void persistirUserIdExterno(IntegracaoMyWellnessJSON integracaoJson, JSONObject jsonRetorno) throws SQLException {
        String sqlPersistir = "";
        if(origem == "CL") {
            if(UteisValidacao.emptyString(integracaoJson.getTokenPessoa())){
                sqlPersistir = "INSERT INTO integracaomywellnessusertoken(pessoa, token, facilityurl) VALUES ("+
                integracaoJson.getPessoa().getCodPessoa() +",'" +jsonRetorno.getString("permanentToken") +"','"+ integracaoJson.getFacilityUrl()+"');";
            } else if(jsonRetorno.optBoolean("trocarPermanentToken", false)) {
                sqlPersistir = "UPDATE integracaomywellnessusertoken SET  " +
                        "token='"+jsonRetorno.getString("permanentToken") +"' where pessoa = "+integracaoJson.getPessoa().getCodPessoa()+" and facilityurl='"+ integracaoJson.getFacilityUrl()+"';";
            }
        } else {
            if(UteisValidacao.emptyString(integracaoJson.getTokenPessoa())){
                sqlPersistir = "INSERT INTO integracaomywellnessusertoken(colaborador, staffid, facilityurl) VALUES ("+
                        integracaoJson.getPessoa().getCodColaborador() +",'" +jsonRetorno.getString("staffId") +"','"+ integracaoJson.getFacilityUrl()+"');";
            } else if (!jsonRetorno.optString("staffId", "").equals("") ){
                sqlPersistir = "UPDATE integracaomywellnessusertoken SET  " +
                        "staffid='"+jsonRetorno.getString("staffId") +"' where colaborador = "+integracaoJson.getPessoa().getCodColaborador()+" and facilityurl='"+ integracaoJson.getFacilityUrl()+"';";
            }
        }
        SuperFacadeJDBC.executarUpdate(sqlPersistir,con);
    }

    private void incluirLogIntegracao(IntegracaoMyWellnessJSON integracaoJson, Date dataTentativa, Date dataSincronizacao, boolean erroZW, String msgErro, JSONObject retorno) throws SQLException {
        StringBuilder sql = new StringBuilder("INSERT INTO integracaomywellness(dttentativa,codigoentidade,origem, errosincronizacaoapi, json, retorno\n" );
        if(!erroZW) {
            sql.append(",dtsincronizacaoapi\n");
        }
        sql.append(")VALUES (?, ?, ?, ?, ?, ?\n");
        if(!erroZW) {
            sql.append(", ?\n");
        }
        sql.append(");\n");
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            int i = 1;
            sqlInserir.setTimestamp(i++,Uteis.getDataJDBCTimestamp(dataTentativa));
            sqlInserir.setInt(i++,codigoEntidade);
            sqlInserir.setString(i++, origem);
            sqlInserir.setString(i++,msgErro);
            sqlInserir.setString(i++, integracaoJson.toJSON().toString());
            sqlInserir.setString(i++, retorno.toString());
            if(!erroZW){
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(dataSincronizacao));

            }
            sqlInserir.execute();

        }
    }

    private List<IntegracaoMyWellnessJSON>  montarDadosColaborador() throws Exception {
        List<IntegracaoMyWellnessJSON> listaIntegracaoJson = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT col.codigo,pes.fotokey,itk.staffid as tokenpessoa, pes.nome,  pes.sexo, pes.datanasc,  cid.nome as cidade, pes.cfp, col.empresa,\n");
        sql.append("integracaoMyWellnessUser,integracaoMyWellnessPassword,integracaMyWellneApiKey, integracaoMyWellneHabilitada, integracaoMyWellnessFacilityUrl,\n");
        sql.append("est.sigla as estado, \n");
        sql.append("(SELECT email FROM email ema WHERE ema.pessoa = pes.codigo LIMIT 1) as email,\n");
        sql.append("(SELECT TRIM(CONCAT(tipoendereco, ' ', bairro, ' ', numero, ' ', complemento, ' ', endereco)) FROM endereco ede where ede.pessoa = pes.codigo LIMIT 1) as endereco,\n");
        sql.append("(SELECT cep FROM endereco ede WHERE ede.pessoa = pes.codigo LIMIT 1) as cep,\n");
        sql.append("(SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT tpc.descricao from tipocolaborador tpc where tpc.colaborador = col.codigo) foo ) as a) as tipocolaborador,\n");
        sql.append("(SELECT tipoTelefone FROM telefone tel WHERE tel.pessoa = pes.codigo and tipotelefone <> 'CE' LIMIT 1)as telefone,\n");
        sql.append("(SELECT numero FROM telefone tel WHERE tel.pessoa = pes.codigo and tipotelefone = 'CE' limit 1) as celular\n");
        sql.append(" FROM pessoa pes\n");
        sql.append(" inner join colaborador col on col.pessoa = pes.codigo\n");
        sql.append("inner join empresa e on e.codigo = col.empresa\n");
        sql.append("left join integracaomywellnessusertoken itk on itk.colaborador = col.codigo and itk.facilityUrl = e.integracaoMyWellnessFacilityUrl\n");
        sql.append(" LEFT JOIN cidade cid ON pes.cidade = cid.codigo\n");
        sql.append(" LEFT JOIN estado est ON pes.estado = est.codigo\n");
        sql.append("  WHERE col.codigo = ").append(codigoEntidade);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)){
            if(rs.next()){
                IntegracaoMyWellnessJSON integracaoJson = new IntegracaoMyWellnessJSON();
                integracaoJson.setChave(key);
                integracaoJson.setOrigem(origem);
                integracaoJson.setApiKey(rs.getString("integracaMyWellneApiKey"));
                integracaoJson.setFacilityUrl(rs.getString("integracaoMyWellnessFacilityUrl"));
                integracaoJson.setPassword(rs.getString("integracaoMyWellnessPassword"));
                integracaoJson.setUser(rs.getString("integracaoMyWellnessUser"));
                integracaoJson.setTokenPessoa(rs.getString("tokenpessoa"));
                integracaoJson.getPessoa().setCodColaborador(rs.getInt("codigo"));
                integracaoJson.getPessoa().setCelular(rs.getString("celular"));
                integracaoJson.getPessoa().setCep(rs.getString("cep"));
                integracaoJson.getPessoa().setCidade(rs.getString("cidade"));
                integracaoJson.getPessoa().setCpf(rs.getString("cfp"));
                integracaoJson.getPessoa().setDataNascimento(rs.getDate("datanasc") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("datanasc")));
                integracaoJson.getPessoa().setEmail(rs.getString("email"));
                integracaoJson.getPessoa().setEmpresa(rs.getInt("empresa"));
                integracaoJson.getPessoa().setCodPessoa(rs.getInt("codigo"));
                integracaoJson.getPessoa().setEndereco(rs.getString("endereco"));
                integracaoJson.getPessoa().setEstado(rs.getString("estado"));
                integracaoJson.getPessoa().setGenero(rs.getString("sexo"));
                integracaoJson.getPessoa().setNome(rs.getString("nome"));
                integracaoJson.getPessoa().setTelefone(rs.getString("telefone"));
                integracaoJson.getPessoa().setTipocolaborador(rs.getString("tipocolaborador"));
                integracaoJson.getPessoa().setUrlFoto(UteisValidacao.emptyString(rs.getString("fotokey")) ? "" : Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                listaIntegracaoJson.add(integracaoJson);
            } else {
                throw new Exception("dados não encontratos para a pessoa :"+codigoEntidade);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return listaIntegracaoJson;
    }

    private List<IntegracaoMyWellnessJSON>  montarDadosCliente() throws Exception {
        List<IntegracaoMyWellnessJSON> listaIntegracaoJson = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT pes.codigo,pes.fotokey, itk.token as tokenpessoa, pes.nome,  pes.sexo, pes.datanasc,  cid.nome as cidade, pes.cfp, cli.empresa,\n");
        sql.append("integracaoMyWellnessUser,integracaoMyWellnessPassword,integracaoMyWellnessEnviarGrupos,integracaMyWellneApiKey, integracaoMyWellneHabilitada, integracaoMyWellnessFacilityUrl,tipovigenciamywellnessgympass,nrdiasvigenciamywellnessgympass,\n");
        sql.append("est.sigla as estado,   sdw.nomeplano,sdw.datamatricula ,  sdw.datalancamentocontrato, sdw.datavigenciade, sdw.datavigenciaateajustada, sdw.datainicioperiodoacesso, sdw.datafimperiodoacesso, cdu.numeromeses,\n");
        sql.append("(SELECT email FROM email ema WHERE ema.pessoa = pes.codigo LIMIT 1) as email,\n");
        sql.append("(SELECT TRIM(CONCAT(tipoendereco, ' ', bairro, ' ', numero, ' ', complemento, ' ', endereco)) FROM endereco ede where ede.pessoa = pes.codigo LIMIT 1) as endereco,\n");
        sql.append("(SELECT cep FROM endereco ede WHERE ede.pessoa = pes.codigo LIMIT 1) as cep,\n");
        sql.append("Case when integracaoMyWellnessEnviarVinculos then (SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT CONCAT(staffid, '|', tipovinculo) from (select distinct tipovinculo, (select colaborador from vinculo where codigo = max(v.codigo)) as colaborador from vinculo v where cliente =cli.codigo group by 1 ) as vinculo inner join integracaomywellnessusertoken itkc on itkc.colaborador = vinculo.colaborador) foo ) as a) ELSE '' END as vinculos,\n ");
        sql.append("(SELECT tipoTelefone FROM telefone tel WHERE tel.pessoa = pes.codigo and tipotelefone <> 'CE' LIMIT 1)as telefone,\n");
        sql.append("(SELECT numero FROM telefone tel WHERE tel.pessoa = pes.codigo and tipotelefone = 'CE' limit 1) as celular, con.situacaocontrato\n");
        if(enviarContratoAnterior){
            sql.append(", anterior.vigenciade as vigenciadeanterior, anterior.vigenciaateajustada as vigenciaateanterior \n");
        }
        sql.append(", (select datainicioacesso::date||';'||datafinalacesso::date from periodoacessocliente  where pessoa = pes.codigo and  tokengympass  <> '' order by datafinalacesso desc limit 1) as acessogynpass \n" );
        sql.append(", (select max(a.dthrentrada) from acessocliente a inner join localacesso l on a.localacesso = l.codigo where a.cliente = cli.codigo and l.empresa = cli.empresa)   as ultimoAcesso \n" );
        sql.append(", (select min(datainicioacesso) from periodoacessocliente  where pessoa = pes.codigo ) as primeiroperiodoacesso, e.timezonedefault \n" );
        sql.append(" FROM pessoa pes\n");
        sql.append(" inner join cliente cli on cli.pessoa = pes.codigo\n");
        sql.append("inner join empresa e on e.codigo = cli.empresa\n");
        if(acesso) { // só quem foi enviado vai ter dados de acesso enviado
            sql.append(" inner ");
        } else {
            sql.append(" left ");
        }
        sql.append(" join integracaomywellnessusertoken itk on itk.pessoa = pes.codigo and itk.facilityUrl = e.integracaoMyWellnessFacilityUrl\n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw sdw ON sdw.codigopessoa = pes.codigo\n");
        sql.append(" LEFT JOIN cidade cid ON pes.cidade = cid.codigo\n");
        sql.append(" LEFT JOIN estado est ON pes.estado = est.codigo\n");
        sql.append(" LEFT JOIN acessocliente acc on acc.codigo = cli.uacodigo\n");
        sql.append("  LEFT JOIN contrato con on con.codigo = sdw.codigocontrato\n");
        sql.append("  LEFT JOIN contratoduracao cdu on cdu.contrato = sdw.codigocontrato\n");
        if(enviarContratoAnterior){
            sql.append(" left join contrato anterior on anterior.codigo = con.contratobaseadorematricula or anterior.codigo = con.contratobaseadorenovacao \n");
        }
        sql.append("  WHERE cli.codigo = ").append(codigoEntidade);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)){
            if(rs.next()){
                IntegracaoMyWellnessJSON integracaoJson = new IntegracaoMyWellnessJSON();
                integracaoJson.setChave(key);
                integracaoJson.setOrigem(origem);
                integracaoJson.setApiKey(rs.getString("integracaMyWellneApiKey"));
                integracaoJson.setFacilityUrl(rs.getString("integracaoMyWellnessFacilityUrl"));
                integracaoJson.setPassword(UteisValidacao.emptyString(rs.getString("integracaoMyWellnessPassword")) ? "Pac03092020@" : rs.getString("integracaoMyWellnessPassword") );
                integracaoJson.setUser(UteisValidacao.emptyString(rs.getString("integracaoMyWellnessUser"))  ? "<EMAIL>" : rs.getString("integracaoMyWellnessUser") );
                integracaoJson.setTokenPessoa(rs.getString("tokenpessoa"));
                integracaoJson.getPessoa().setEnviarGrupos(rs.getBoolean("integracaoMyWellnessEnviarGrupos"));
                integracaoJson.getPessoa().setCelular(rs.getString("celular"));
                integracaoJson.getPessoa().setCep(rs.getString("cep"));
                integracaoJson.getPessoa().setCidade(rs.getString("cidade"));
                integracaoJson.getPessoa().setCodPessoa(rs.getInt("codigo"));
                integracaoJson.getPessoa().setVinculos(rs.getString("vinculos"));
                integracaoJson.getPessoa().setCpf(rs.getString("cfp"));
                integracaoJson.getPessoa().setDatalancamentocontrato(rs.getDate("datamatricula") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("datamatricula")));
                integracaoJson.getPessoa().setDataNascimento(rs.getDate("datanasc") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("datanasc")));
                Date datafinal = rs.getDate("datavigenciaateajustada");
                Date dataInicio = rs.getDate("datavigenciade");
                if(rs.getDate("datafimperiodoacesso") != null && ((datafinal == null) || Calendario.maior(rs.getDate("datafimperiodoacesso"), datafinal))){
                    datafinal = rs.getDate("datafimperiodoacesso");
                }
                if(rs.getDate("datainicioperiodoacesso") != null && (dataInicio == null)){
                    dataInicio = rs.getDate("datainicioperiodoacesso");
                }
                boolean gympass = false;
                if(!UteisValidacao.emptyString(rs.getString("acessogynpass"))){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    String[] datas = rs.getString("acessogynpass").split(";");
                    Date dataInicioGymPass = sdf.parse(datas[0]);
                    Date dataFinalGymPass = sdf.parse(datas[1]);
                    if(datafinal == null || Calendario.maior(dataFinalGymPass,datafinal)){
                        dataInicio = dataInicioGymPass;

                        TipoVigenciaMyWellnessGymPassEnum tipoVigenciaMyWellnessGymPass = TipoVigenciaMyWellnessGymPassEnum.getFromId(rs.getInt("tipovigenciamywellnessgympass"));
                        if(tipoVigenciaMyWellnessGymPass.equals(TipoVigenciaMyWellnessGymPassEnum.QUANTIDADE_INFORMADA)) {
                            Integer diasVigencia = rs.getInt("nrdiasvigenciamywellnessgympass");
                            datafinal = Uteis.somarDias(dataInicioGymPass, (diasVigencia - 1));
                        } else if(tipoVigenciaMyWellnessGymPass.equals(TipoVigenciaMyWellnessGymPassEnum.SEM_DATA_FINAL)){
                            datafinal = null;
                        } else {
                            datafinal = dataFinalGymPass;
                        }

                        if(rs.getDate("datamatricula") == null){
                            integracaoJson.getPessoa().setDatalancamentocontrato(rs.getDate("primeiroperiodoacesso") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("primeiroperiodoacesso")));
                        }
                        gympass = true;
                    }
                }
                integracaoJson.getPessoa().setDatavigenciaate(datafinal == null ? "" : Uteis.getDataFormatoBD(datafinal));
                integracaoJson.getPessoa().setDatavigenciade(dataInicio == null ? "" : Uteis.getDataFormatoBD(dataInicio));
                if (rs.getDate("ultimoAcesso") != null) {
                    try {
                        //enviar com data/hora e timezone | Formato de acordo com a doc: aaaa-MM-dd hh:mm:ss +(-)hh:mm
                        String timezoneString = rs.getString("timezonedefault");
                        integracaoJson.getPessoa().setDthrentrada(
                                String.format("%s %s", Calendario.getDataAplicandoFormatacao(rs.getTimestamp("ultimoAcesso"), "yyyy-MM-dd HH:mm:ss"),
                                        Calendario.timeZoneToGMT(timezoneString)));
                    } catch (Exception exception) {
                        //enviar somente data data
                        integracaoJson.getPessoa().setDthrentrada(Uteis.getDataFormatoBD(rs.getDate("ultimoAcesso")));
                    }
                } else {
                    integracaoJson.getPessoa().setDthrentrada("");
                }
                integracaoJson.getPessoa().setSituacaoContrato(rs.getString("situacaocontrato"));
                integracaoJson.getPessoa().setEmail(rs.getString("email"));
                integracaoJson.getPessoa().setEmail(rs.getString("email"));
                integracaoJson.getPessoa().setEmpresa(rs.getInt("empresa"));
                integracaoJson.getPessoa().setCodPessoa(rs.getInt("codigo"));
                integracaoJson.getPessoa().setEndereco(rs.getString("endereco"));
                integracaoJson.getPessoa().setEstado(rs.getString("estado"));
                integracaoJson.getPessoa().setGenero(rs.getString("sexo"));
                integracaoJson.getPessoa().setNome(rs.getString("nome"));
                integracaoJson.getPessoa().setNomeplano(gympass ? "GymPass" : rs.getString("nomeplano"));
                integracaoJson.getPessoa().setNumeromesescontrato(rs.getInt("numeromeses"));
                integracaoJson.getPessoa().setTelefone(rs.getString("telefone"));
                integracaoJson.getPessoa().setUrlFoto(UteisValidacao.emptyString(rs.getString("fotokey")) ? "" : Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
                if(enviarContratoAnterior){
                    integracaoJson.getPessoa().setDatavigenciadeanterior(rs.getDate("vigenciadeanterior") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("vigenciadeanterior")));
                    integracaoJson.getPessoa().setDatavigenciaateanterior(rs.getDate("vigenciaateanterior") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("vigenciaateanterior")));
                }
                listaIntegracaoJson.add(integracaoJson);
            } else {
                throw new Exception("dados não encontratos para a pessoa :"+codigoEntidade);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        consultarContratosDeOutraUnidade(listaIntegracaoJson);
        Collections.reverse(listaIntegracaoJson);
        return listaIntegracaoJson;
    }

    private void consultarContratosDeOutraUnidade(List<IntegracaoMyWellnessJSON> listaIntegracaoJson) throws Exception {

        try{
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT pes.codigo,pes.fotokey, itk.token as tokenpessoa, pes.nome,  pes.sexo, pes.datanasc,  cid.nome as cidade, pes.cfp, con.empresa,\n");
            sql.append("integracaoMyWellnessUser,integracaoMyWellnessPassword,integracaoMyWellnessEnviarGrupos,integracaMyWellneApiKey, integracaoMyWellneHabilitada, integracaoMyWellnessFacilityUrl,tipovigenciamywellnessgympass,nrdiasvigenciamywellnessgympass,\n");
            sql.append("est.sigla as estado,   pla.descricao as nomeplano, con.datamatricula ,  con.datalancamento as datalancamentocontrato, con.vigenciade as datavigenciade, con.vigenciaateajustada as datavigenciaateajustada,  cdu.numeromeses,\n");
            sql.append("(SELECT email FROM email ema WHERE ema.pessoa = pes.codigo LIMIT 1) as email,\n");
            sql.append("(SELECT TRIM(CONCAT(tipoendereco, ' ', bairro, ' ', numero, ' ', complemento, ' ', endereco)) FROM endereco ede where ede.pessoa = pes.codigo LIMIT 1) as endereco,\n");
            sql.append("(SELECT cep FROM endereco ede WHERE ede.pessoa = pes.codigo LIMIT 1) as cep,\n");
            sql.append("Case when integracaoMyWellnessEnviarVinculos then (SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT CONCAT(staffid, '|', tipovinculo) from (select distinct tipovinculo, (select colaborador from vinculo where codigo = max(v.codigo)) as colaborador from vinculo v where cliente =cli.codigo group by 1 ) as vinculo inner join integracaomywellnessusertoken itkc on itkc.colaborador = vinculo.colaborador) foo ) as a) ELSE '' END as vinculos,\n ");
            sql.append("(SELECT tipoTelefone FROM telefone tel WHERE tel.pessoa = pes.codigo and tipotelefone <> 'CE' LIMIT 1)as telefone,\n");
            sql.append("(SELECT numero FROM telefone tel WHERE tel.pessoa = pes.codigo and tipotelefone = 'CE' limit 1) as celular, con.situacaocontrato\n");
            sql.append(", (select max(a.dthrentrada) from acessocliente a inner join localacesso l on a.localacesso = l.codigo where a.cliente = cli.codigo and l.empresa = con.empresa)   as ultimoAcesso \n" );
            sql.append(", (select min(datainicioacesso) from periodoacessocliente  where pessoa = pes.codigo ) as primeiroperiodoacesso, e.timezonedefault \n" );
            sql.append(" FROM pessoa pes\n");
            sql.append(" inner join cliente cli on cli.pessoa = pes.codigo\n");
            sql.append("  inner join contrato con on con.pessoa = cli.pessoa   and cli.empresa <> con.empresa \n");
            sql.append("  inner join plano pla on con.plano = pla.codigo    \n");
            sql.append(" inner join empresa e on e.codigo = con.empresa\n");
            if(acesso) { // só quem foi enviado vai ter dados de acesso enviado
                sql.append(" inner ");
            } else {
                sql.append(" left ");
            }
            sql.append(" join integracaomywellnessusertoken itk on itk.pessoa = pes.codigo and itk.facilityUrl = e.integracaoMyWellnessFacilityUrl\n");
            sql.append(" LEFT JOIN situacaoclientesinteticodw sdw ON sdw.codigopessoa = pes.codigo\n");
            sql.append(" LEFT JOIN cidade cid ON pes.cidade = cid.codigo\n");
            sql.append(" LEFT JOIN estado est ON pes.estado = est.codigo\n");

            sql.append("  LEFT JOIN contratoduracao cdu on cdu.contrato = con.codigo\n");

            sql.append("  WHERE cli.codigo = ").append(codigoEntidade);
            sql.append(" and con.vigenciade <= current_date order by con.vigenciaateajustada desc");
            List<Integer> empresasAdicionadas = new ArrayList<>();
            empresasAdicionadas.add(listaIntegracaoJson.get(0).getPessoa().getEmpresa());
            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)){
                while(rs.next()){
                    if(empresasAdicionadas.contains(rs.getInt("empresa"))) {
                        continue;
                    }
                    IntegracaoMyWellnessJSON integracaoJson = new IntegracaoMyWellnessJSON();
                    integracaoJson.setChave(key);
                    integracaoJson.setOrigem(origem);
                    integracaoJson.setApiKey(rs.getString("integracaMyWellneApiKey"));
                    integracaoJson.setFacilityUrl(rs.getString("integracaoMyWellnessFacilityUrl"));
                    integracaoJson.setPassword(UteisValidacao.emptyString(rs.getString("integracaoMyWellnessPassword")) ? "Pac03092020@" : rs.getString("integracaoMyWellnessPassword") );
                    integracaoJson.setUser(UteisValidacao.emptyString(rs.getString("integracaoMyWellnessUser"))  ? "<EMAIL>" : rs.getString("integracaoMyWellnessUser") );
                    integracaoJson.setTokenPessoa(rs.getString("tokenpessoa"));
                    integracaoJson.getPessoa().setEnviarGrupos(rs.getBoolean("integracaoMyWellnessEnviarGrupos"));
                    integracaoJson.getPessoa().setCelular(rs.getString("celular"));
                    integracaoJson.getPessoa().setCep(rs.getString("cep"));
                    integracaoJson.getPessoa().setCidade(rs.getString("cidade"));
                    integracaoJson.getPessoa().setCodPessoa(rs.getInt("codigo"));
                    integracaoJson.getPessoa().setVinculos(rs.getString("vinculos"));
                    integracaoJson.getPessoa().setCpf(rs.getString("cfp"));
                    integracaoJson.getPessoa().setDatalancamentocontrato(rs.getDate("datamatricula") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("datamatricula")));
                    integracaoJson.getPessoa().setDataNascimento(rs.getDate("datanasc") == null ? "" : Uteis.getDataFormatoBD(rs.getDate("datanasc")));
                    Date datafinal = rs.getDate("datavigenciaateajustada");
                    Date dataInicio = rs.getDate("datavigenciade");


                    integracaoJson.getPessoa().setDatavigenciaate(datafinal == null ? "" : Uteis.getDataFormatoBD(datafinal));
                    integracaoJson.getPessoa().setDatavigenciade(dataInicio == null ? "" : Uteis.getDataFormatoBD(dataInicio));
                    if (rs.getDate("ultimoAcesso") != null) {
                        try {
                            //enviar com data/hora e timezone | Formato de acordo com a doc: aaaa-MM-dd hh:mm:ss +(-)hh:mm
                            String timezoneString = rs.getString("timezonedefault");
                            integracaoJson.getPessoa().setDthrentrada(
                                    String.format("%s %s", Calendario.getDataAplicandoFormatacao(rs.getTimestamp("ultimoAcesso"), "yyyy-MM-dd HH:mm:ss"),
                                            Calendario.timeZoneToGMT(timezoneString)));
                        } catch (Exception exception) {
                            //enviar somente data data
                            integracaoJson.getPessoa().setDthrentrada(Uteis.getDataFormatoBD(rs.getDate("ultimoAcesso")));
                        }
                    } else {
                        integracaoJson.getPessoa().setDthrentrada("");
                    }
                    integracaoJson.getPessoa().setSituacaoContrato(rs.getString("situacaocontrato"));
                    integracaoJson.getPessoa().setEmail(rs.getString("email"));
                    integracaoJson.getPessoa().setEmail(rs.getString("email"));
                    integracaoJson.getPessoa().setEmpresa(rs.getInt("empresa"));
                    integracaoJson.getPessoa().setCodPessoa(rs.getInt("codigo"));
                    integracaoJson.getPessoa().setEndereco(rs.getString("endereco"));
                    integracaoJson.getPessoa().setEstado(rs.getString("estado"));
                    integracaoJson.getPessoa().setGenero(rs.getString("sexo"));
                    integracaoJson.getPessoa().setNome(rs.getString("nome"));
                    integracaoJson.getPessoa().setNomeplano( rs.getString("nomeplano"));
                    integracaoJson.getPessoa().setNumeromesescontrato(rs.getInt("numeromeses"));
                    integracaoJson.getPessoa().setTelefone(rs.getString("telefone"));
                    integracaoJson.getPessoa().setUrlFoto(UteisValidacao.emptyString(rs.getString("fotokey")) ? "" : Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));

                    listaIntegracaoJson.add(integracaoJson);
                    empresasAdicionadas.add(rs.getInt("empresa"));
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private String executarRequestIntegracaoMyWellness(String parametros, String metodo, String metodoHTTP) throws Exception {

        String path = PropsService.getPropertyValue(PropsService.urlIntegracoesService)  + "/" + metodo;
        Map<String, String> maps = new HashMap<String, String>();
        maps.put("Content-Type", "application/json");

        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, maps, metodoHTTP, "UTF-8");
    }


}


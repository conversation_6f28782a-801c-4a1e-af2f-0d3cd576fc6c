package controle.arquitetura.threads;

import negocio.comuns.arquitetura.PushNotificacaoAppTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.PushMobileRunnableAppAluno;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 */
public class ThreadPushNotificacaoApp extends Thread {

    private static final BlockingQueue<PushNotificacaoAppTO> fila = new LinkedBlockingQueue<>();
    private final PushMobileRunnableAppAluno pushMobileRunnableAppAluno;

    public ThreadPushNotificacaoApp(PushMobileRunnableAppAluno pushMobileRunnableAppAluno) {
        this.pushMobileRunnableAppAluno = pushMobileRunnableAppAluno;
    }

    public ThreadPushNotificacaoApp() {
        this(new PushMobileRunnableAppAluno());
    }

    public static void enfileirarNotificacao(final String horario, final String ctx, final String titulo, final String message, final String usuario, final String path) {
        PushNotificacaoAppTO dados = new PushNotificacaoAppTO(horario, ctx, titulo, message, usuario, path);
        fila.add(dados);
    }

    public void run() {
        while (isAlive()) {
            try {
                PushNotificacaoAppTO dados = fila.take();

                Uteis.logarDebug("Consumindo notificação");
                pushMobileRunnableAppAluno.enviarNotificacaoPushAppAluno(dados.getHorario(), dados.getCtx(), dados.getTitulo(), dados.getMessage(), dados.getUsuario(), dados.getPath());
            } catch (InterruptedException e) {
                break;
            } catch (Exception e) {
                Uteis.logarDebug("Erro ao consumir notificação");
                throw new RuntimeException(e);
            }
        }
    }

}

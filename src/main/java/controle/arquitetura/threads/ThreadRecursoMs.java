package controle.arquitetura.threads;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.servico.RecursoMsService;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ThreadRecursoMs extends Thread {
    private static final Integer SLEEP_MINUTOS = 3;

    @Override
    public void run() {
        try {
            Thread.sleep(SLEEP_MINUTOS * 60 * 1000);
            this.processar();
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao processar recursos em ThreadRecursoMs " + e.getMessage());
        }
    }

    private void processar() throws SQLException, ClassNotFoundException {
        Uteis.logarDebug("Iniciando consultar de recursos inativos por empresa");
        try (Connection connOAMD = Conexao.obterConexaoBancoEmpresas()) {

            try (PreparedStatement ps = connOAMD.prepareStatement("SELECT chave FROM empresa where ativa is true AND modulo like '%NIC%'");
                 ResultSet rs = ps.executeQuery()) {

                while (rs.next()) {
                    String chave = rs.getString("chave");
                    try (Connection connBbZillyon = new DAO().obterConexaoEspecifica(chave);
                         PreparedStatement psZw = connBbZillyon.prepareStatement("SELECT codigo FROM empresa where ativa");
                         ResultSet rsZw = psZw.executeQuery()) {

                        while (rsZw.next()) {
                            Integer empresa = rsZw.getInt("codigo");
                            RecursoMsService.consultarFuncionalidadesInativas(chave, empresa);
                            Uteis.logarDebug("Funcionalidades inativas do nicho para chave " + chave + ", empresa " + empresa + " foram definidas em cache.");
                        }

                    } catch (Exception e) {
                        Uteis.logarDebug("Erro ao consultar dados da empresa da chave " + chave + ". Erro: " + e.getMessage());
                    }
                }

            } catch (Exception e) {
                Uteis.logarDebug("Erro ao consultar chaves do banco OAMD " + e.getMessage());
            }

        }

        Uteis.logarDebug("Finalizados recursos inativos por empresa. Validade do cache em minutos: " + Uteis.validadeCacheNichoEmMinutos());
    }
}

package controle.arquitetura.threads;

import br.com.pactosolucoes.comuns.notificacao.AbstractNotificadorRecursoSistema;
import org.json.JSONObject;
import negocio.comuns.arquitetura.RecursoEmpresaTO;
import negocio.comuns.utilitarias.Uteis;
import servicos.notificador.NotificadorRecursoEmpresaServiceControle;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ThreadRecursoEmpresa extends Thread {

    private static final Integer SLEEP_MINUTOS = 5;

    public void run() {
        try {
            Uteis.logar(null, "Iniciei ThreadRecursoEmpresa...");
            while (true) {
                processar();
                Thread.sleep(SLEEP_MINUTOS * 60 * 1000);
                Uteis.logar(null, "Finalizei ThreadRecursoEmpresa...");
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ThreadRecursoEmpresa.class);
        }
    }


    public void processar() {
        try {
            List<RecursoEmpresaTO> notificacoes = new ArrayList<RecursoEmpresaTO>(NotificadorRecursoEmpresaServiceControle.notificacoes);
            for (RecursoEmpresaTO notf : notificacoes) {
                Uteis.logar(null, String.format("Iniciando Sicronização no OAMD do Recurso Empresa -> (%s)  ....", notf.getRecurso()));
                String response = AbstractNotificadorRecursoSistema.enviarNotificacaoOAMD(notf);

                if (new JSONObject(response).has("sucesso")) {
                    NotificadorRecursoEmpresaServiceControle.removerItem(notf);
                }
            }
            Uteis.logar(null, "Rodada de sincronização dos recursos de empresa com OAMD finalizada ....");
        } catch (Exception e) {
            Uteis.logar(e, ThreadRecursoEmpresa.class);
        }
    }
}

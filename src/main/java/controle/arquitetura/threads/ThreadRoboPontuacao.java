package controle.arquitetura.threads;

import controle.arquitetura.RoboControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Robo;

import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ThreadRoboPontuacao extends Thread {

    public ThreadRoboPontuacao() throws Exception {
        roboControle = new RoboControle();
        roboFacade = new Robo();
    }

    private RoboControle roboControle;
    private Robo roboFacade;
    private String chave = "";
    private Date diaProcessar;
    private boolean finalizado =false;

    @Override
    public void run() {
        try {
            try {
                finalizado= false;
                Date dataUltimoProcessamento = roboFacade.consultaParaObterUltimoDiaprocessado();
                if (dataUltimoProcessamento.before(Calendario.hoje()))
                    dataUltimoProcessamento = Calendario.hoje();
                while (dataUltimoProcessamento.after(getDiaProcessar())) {
                    roboControle.setDataSimulada(getDiaProcessar());
                    roboControle.startPontuacao(chave);
                    setDiaProcessar(Calendario.somarDias(getDiaProcessar(), 1));
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                Uteis.logar(null, "Terminando Thread Pontos Retroativos: " + Uteis.getDataComHora(Calendario.hoje()));
                Thread.currentThread().interrupt();
                this.finalizado = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void setRoboControle(RoboControle roboControle) {
        this.roboControle = roboControle;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getDiaProcessar() {
        return diaProcessar;
    }

    public void setDiaProcessar(Date diaProcessar) {
        this.diaProcessar = diaProcessar;
    }

    public boolean isFinalizado() {
        return finalizado;
    }
}

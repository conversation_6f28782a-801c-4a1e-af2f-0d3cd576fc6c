package controle.arquitetura.threads;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.RoboControle;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.Connection;
import java.util.*;

import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.DiaRoboVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.arquitetura.Robo;
import org.postgresql.util.PSQLException;
import servicos.propriedades.PropsService;

public class ThreadRobo extends Thread {

    public ThreadRobo() throws Exception {
        realizar = false;
        realizouRotina = false;
        roboControle = new RoboControle();
        roboFacade = new Robo();
    }
    private Boolean realizar;
    private Boolean realizouRotina;
    private RoboControle roboControle;
    private Robo roboFacade;
    private List<DiaRoboVO> diasDesatualizados = new ArrayList<>();
    private String diaEmProcessamento = "";
    private boolean ativarPoll = false;
    private String ultimaVerificacao = "";
    private boolean rodarEmLoop = true;
    private boolean validarEstatisticas = false;
    private String chave = "";

    @Override
    public void run() {
        try {
            if (rodarEmLoop) {
                while (true) {
                    synchronized (this) {
                        try {
                            diaEmProcessamento = "";
                            ultimaVerificacao = String.format("Última verificação: %1$s",
                                    Uteis.getDataComHora(Calendario.hoje()));
                            Date date = Calendario.hoje();
                            Integer horaAtual = Uteis.gethoraHH(date);

                            if ((!diasDesatualizados.isEmpty())
                                    || (getRealizar() && (horaAtual >= 0 && horaAtual <= 1))) {

                                ativarPoll = !diasDesatualizados.isEmpty();

                                try {
                                    Date dataUltimoProcessamento = roboFacade.consultaParaObterUltimoDiaprocessado();
                                    if (dataUltimoProcessamento == null) {
                                        Calendar cal = Calendario.getInstance();
                                        cal.add(GregorianCalendar.DATE, -1);
                                        dataUltimoProcessamento = cal.getTime();
                                    }
                                    dataUltimoProcessamento = Uteis.obterDataFutura2(dataUltimoProcessamento, 1);
                                    
                                    if (Uteis.getCompareData(date, dataUltimoProcessamento) >= 0) {
                                        if (diasDesatualizados.isEmpty()) {

                                            roboControle.start(chave);

                                            //dormir por 1 hora já que o processamento noturno foi efetuado com sucesso
                                            //ThreadRobo.sleep(3600000);
                                        } else {

                                            for (Iterator<DiaRoboVO> it = diasDesatualizados.iterator(); it.hasNext();) {
                                                DiaRoboVO diaRobo = it.next();

                                                if (it.hasNext()) {//para gerar sintetico so no ultimo dia a ser processado
                                                    roboControle.getRobo().setDeveGerarSintetico(false);
                                                } else {
                                                    roboControle.getRobo().setDeveGerarSintetico(true);
                                                }
                                                diaEmProcessamento = Uteis.getData(diaRobo.getDia());
                                                try {
                                                    //Criar Thread para gerar os Agendamentos do Módulo Financeiro.
                                                    if (roboControle.getRobo().isDeveGerarSintetico()) {
                                                        criarThreadAgendamentoFinanceiro(diaRobo.getDia(), chave);
                                                    }
                                                    roboControle.startRoboDesatualizado(diaRobo.getDia(), chave);


                                                } catch (Exception e) {
                                                    Logger.getLogger(this.getClass().getName()).log(
                                                            Level.SEVERE, "Exceção: {0} ( em processando dias desatualizados) - {1}",
                                                            new Object[]{
                                                                e.getMessage(),
                                                                diasDesatualizados.size()});
                                                }

                                            }
                                        }
                                    }


                                } finally {
                                    diasDesatualizados.clear();
                                }
                            }

                            //ThreadRobo.sleep(3600000); // intervalo multiplicado por 1
                            ThreadRobo.sleep(10000); // intervalo multiplicado por 1
                            // minuto(60000 3600000Lmilisegundos).

                        } catch (Exception e) {
                            diasDesatualizados.clear();
                            try {
                                if (e.getClass() == PSQLException.class) {
                                    roboFacade = null;
                                    roboFacade = new Robo();
                                }
                            } catch (Exception ex) {
                                this.setRealizar(false);
                                this.interrupt();
                            }
                        }

                    }

                }
            } else {//deve rodar uma vez e morrer

                synchronized (this) {
                    try {
                        diaEmProcessamento = "";
                        ultimaVerificacao = String.format("Última verificação: %1$s",
                                Uteis.getDataComHora(Calendario.hoje()));
                        Date date = Calendario.hoje();
                        Integer horaAtual = Uteis.gethoraHH(date);

                        if (!diasDesatualizados.isEmpty()
                                || getRealizar()) {
                            roboFacade.inicializarDadosAcessoSintetico(Calendario.hoje());
                            ativarPoll = !diasDesatualizados.isEmpty();
                            

                            try {
                                Date dataUltimoProcessamento = roboFacade.consultaParaObterUltimoDiaprocessado();
                                if (dataUltimoProcessamento == null) {
                                    Calendar cal = Calendario.getInstance();
                                    cal.add(GregorianCalendar.DATE, -1);
                                    dataUltimoProcessamento = cal.getTime();
                                }
                                dataUltimoProcessamento = Uteis.obterDataFutura2(dataUltimoProcessamento, 1);

                                if (Uteis.getCompareData(date, dataUltimoProcessamento) >= 0) {
                                    if (diasDesatualizados.isEmpty()) {

                                        roboControle.start(chave);

                                    } else {

                                        for (Iterator<DiaRoboVO> it = diasDesatualizados.iterator(); it.hasNext();) {
                                            DiaRoboVO diaRobo = it.next();

                                            if (it.hasNext()) {//para gerar sintetico so no ultimo dia a ser processado
                                                roboControle.getRobo().setDeveGerarSintetico(false);
                                            } else {
                                                roboControle.getRobo().setDeveGerarSintetico(true);
                                            }
                                            diaEmProcessamento = Uteis.getData(diaRobo.getDia());
                                            try {
                                                roboControle.startRoboDesatualizado(diaRobo.getDia(), chave);
                                                if (roboControle.getRobo().isDeveGerarSintetico()
                                                        && this.isValidarEstatisticas()) {

                                                    /** WM 02/04/2015:
                                                     * Deixando apenas para o Serviço que enviará sequencialmente as remessas
                                                     * para o Servidor de Processamento.
                                                     * NewRemessaService remessEDICielo = new NewRemessaService();
                                                    remessEDICielo.setKey(chave);
                                                    remessEDICielo.processarRemessa(diaRobo.getDia());
                                                    remessEDICielo = null;
                                                    * */

                                                    roboControle.getRobo().processarFeedsGestao();
                                                    //Processa renovação Automatica de Armários
                                                    roboControle.getRobo().processarArmarioGestao();
                                                }

                                            } catch (Exception e) {
                                                Logger.getLogger(this.getClass().getName()).log(
                                                        Level.SEVERE, "Exceção: {0} ( em processando dias desatualizados) - {1}",
                                                        new Object[]{
                                                    e.getMessage(),
                                                    diasDesatualizados.size()});
                                                StringWriter errors = new StringWriter();
                                                e.printStackTrace(new PrintWriter(errors));
                                                StringBuffer sb = new StringBuffer();
                                                sb.append(Uteis.getDataComHHMM(Calendario.hoje()));
                                                sb.append(" THREAD: ");
                                                sb.append(this.getName());
                                                sb.append(" - Erro ao processar dias desatualizados: ");
                                                sb.append(diasDesatualizados.size());
                                                sb.append(" - Exceção que ocorreu: ");
                                                sb.append(e.getMessage());
                                                sb.append("\n").append(errors.toString());
                                                MsgTO m = new MsgTO(chave, "RobotRunner - Erro ao processar dias " + chave, sb);
                                                // acrescentar destinatario para abertura automatizada de ticket de ERRO
                                                if (!PropsService.isEmpty(PropsService.emailSquadAdmAbrirTicket)) {
                                                    List<String> l = m.getDestinatarios() != null
                                                            ? Arrays.asList(m.getDestinatarios())
                                                            : new ArrayList<>();
                                                    l.add(PropsService.getPropertyValue(PropsService.emailSquadAdmAbrirTicket));
                                                    m.setDestinatarios(l.toArray(new String[l.size()]));
                                                }

                                                UteisValidacao.enfileirarEmail(m);
                                            }

                                            try{
                                                //Criar Thread para gerar os Agendamentos do Módulo Financeiro.
                                                criarThreadAgendamentoFinanceiro(diaRobo.getDia(), chave);
                                                criarThreadFechamentoCaixa(diaRobo.getDia(),  chave);
                                            }catch (Exception e){
                                                throw new Exception(e);
                                            }

                                        }
                                    }
                                }


                            } finally {
                                diasDesatualizados.clear();
                            }
                        }

                    } catch (Exception e) {
                        diasDesatualizados.clear();
                        try {
                            if (e.getClass() == PSQLException.class) {
                                roboFacade = null;
                                roboFacade = new Robo();
                            }
                        } catch (Exception ex) {
                            this.setRealizar(false);
                            this.interrupt();
                        }
                    }

                }


            }
        } catch (Exception e) {
            Logger.getLogger(this.getName()).log(
                    Level.SEVERE, "Exceção: {0} ocorreu enquanto verificava a execução do Robô",
                    new Object[]{
                        e.getMessage()});
            this.setRealizar(false);
            this.interrupt();
        } finally {
            ThreadEnviarEmail.finalmente();
        }
    }

    private void criarThreadAgendamentoFinanceiro(Date dataProcessamento, String chave) throws Exception {
        DAO dao = new DAO();
        Connection conexaoAgendamento = dao.obterConexaoEspecifica(chave);
        ThreadAgendamentoFinanceiro.gerarParcelasParaAgendamento(conexaoAgendamento, dataProcessamento);

    }

    private void criarThreadFechamentoCaixa(Date dataProcessamento, String chave) throws Exception {
        DAO dao = new DAO();
        Connection conexaoFechamento = dao.obterConexaoEspecifica(chave);
        ThreadFechamentoCaixaAutomatico tf = new ThreadFechamentoCaixaAutomatico(conexaoFechamento, chave);
        tf.fecharCaixas();

    }

    /**
     * @return the realizar
     */
    public Boolean getRealizar() {
        return realizar;
    }

    /**
     * @param realizar
     *            the realizar to set
     */
    public void setRealizar(Boolean realizar) {
        this.realizar = realizar;
    }

    public RoboControle getRoboControle() {
        return roboControle;
    }

    public void setRoboControle(RoboControle roboControle) {
        this.roboControle = roboControle;
    }

    public Boolean getRealizouRotina() {
        return realizouRotina;
    }

    public void setRealizouRotina(Boolean realizouRotina) {
        this.realizouRotina = realizouRotina;
    }

    public String getDiaEmProcessamento() {
        return diaEmProcessamento;
    }

    public boolean isAtivarPoll() {
        return ativarPoll;
    }

    public void verificaFimPoll() {
        if (diasDesatualizados.isEmpty()) {
            ativarPoll = false;
        }
    }

    public String getUltimaVerificacao() {
        return ultimaVerificacao;
    }

    public void setDiasDesatualizados(List<DiaRoboVO> diasDesatualizados) {
        this.diasDesatualizados = diasDesatualizados;
    }

    public List<DiaRoboVO> getDiasDesatualizados() {
        return diasDesatualizados;
    }

    public boolean isRodarEmLoop() {
        return rodarEmLoop;
    }

    public void setRodarEmLoop(boolean rodarEmLoop) {
        this.rodarEmLoop = rodarEmLoop;
    }

    public boolean isValidarEstatisticas() {
        return validarEstatisticas;
    }

    public void setValidarEstatisticas(boolean validarEstatisticas) {
        this.validarEstatisticas = validarEstatisticas;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }
}

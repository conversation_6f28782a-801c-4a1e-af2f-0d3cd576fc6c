package controle.arquitetura.threads;

import br.com.pactosolucoes.atualizadb.processo.ajustebd.AdicionarModalidadeEmPlano;
import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.UteisImportacao;
import importador.alunosTurma.ImportarAlunosTurma;
import importador.alunosTurma.ImportarTurma;
import importador.cliente.ImportarCliente;
import importador.colaborador.ImportarColaborador;
import importador.contrato.ImportarContrato;
import importador.financeiro.ImportarConta;
import importador.financeiro.ImportarFornecedor;
import importador.financeiro.ImportarPagamento;
import importador.json.*;
import importador.produtos.ImportarProduto;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.acesso.IntegracaoMemberService;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

/**
 * Created by Luiz Felipe on 20/12/2019.
 */
public class ThreadImportacao extends Thread {

    private Connection con;
    private String chave;
    private ImportacaoConfigTO configTO;
    private ImportacaoCache cache;

    private ProcessoImportacao processoDAO;
    private ProcessoImportacaoLog processoLogDAO;
    private ProcessoImportacaoItem processoItemDAO;

    private List<ClienteImportacaoJSON> listaClientesJSON;
    private List<ContratoImportacaoJSON> listaContratosJSON;
    private List<ParcelaPagamentoJSON> listaParcelasPagamentosJSON;
    private List<ProdutoImportacaoJSON> listaProdutosJSON;
    private List<ContaImportacaoJSON> listaContaJSON;
    private List<FornecedorImportacaoJSON> listaFornecedorJSON;
    private List<ColaboradorImportacaoJSON> listaColaboradorJSON;
    private List<AlunoTurmaImportacaoJSON> listaAlunoTurmaJson;
    private List<TurmaImportacaoJSON> listaTurmaJson;
    private List<TreinoAtividadeJSON> listaTreinoAtividadesJson;
    private List<ProgramaFichaJSON> listaTreinoProgramasJson;
    private List<AtividadeFichaJSON> listaTreinoAtividadeFichaJson;
    private ConfigExcelImportacaoTO configExcelImportacaoTO;

    public ThreadImportacao(ImportacaoConfigTO configTO,
                            ConfigExcelImportacaoTO configExcelImportacaoTO,
                            Connection con,
                            List<ClienteImportacaoJSON> listaClientesJSON,
                            List<ContratoImportacaoJSON> listaContratosJSON,
                            List<ProdutoImportacaoJSON> listaProdutosJSON,
                            List<ContaImportacaoJSON> listaContaJSON,
                            List<FornecedorImportacaoJSON> listaFornecedorJSON,
                            List<ColaboradorImportacaoJSON> listaColaboradorJSON,
                            List<AlunoTurmaImportacaoJSON> listaAlunoTurmaJson,
                            List<TurmaImportacaoJSON> listaTurmaJson,
                            List<ParcelaPagamentoJSON> listaParcelasPagamentosJSON,
                            String chave,
                            List<TreinoAtividadeJSON> listaTreinoAtividadesJSON,
                            List<ProgramaFichaJSON> listaTreinoProgramasJson,
                            List<AtividadeFichaJSON> listaTreinoAtividadeFichaJson
                            ) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        this.chave = chave;

        validarThread(configTO);

        this.processoDAO = new ProcessoImportacao(this.con);
        this.processoLogDAO = new ProcessoImportacaoLog(this.con);
        this.processoItemDAO = new ProcessoImportacaoItem(this.con);
        this.listaClientesJSON = listaClientesJSON;
        this.listaContratosJSON = listaContratosJSON;
        this.listaParcelasPagamentosJSON = listaParcelasPagamentosJSON;
        this.listaProdutosJSON = listaProdutosJSON;
        this.listaContaJSON = listaContaJSON;
        this.listaFornecedorJSON = listaFornecedorJSON;
        this.listaColaboradorJSON = listaColaboradorJSON;
        this.listaAlunoTurmaJson = listaAlunoTurmaJson;
        this.listaTurmaJson = listaTurmaJson;
        this.configExcelImportacaoTO = configExcelImportacaoTO;
        this.listaTreinoAtividadesJson = listaTreinoAtividadesJSON;
        this.listaTreinoProgramasJson = listaTreinoProgramasJson;
        this.listaTreinoAtividadeFichaJson = listaTreinoAtividadeFichaJson;
    }

    private void validarThread(ImportacaoConfigTO configTO) throws Exception {
        this.configTO = configTO;

        if (UteisValidacao.emptyString(getConfigTO().getChave())) {
            throw new Exception("Necessário a chave.");
        }
        if (getConfigTO().getTipoImportacaoEnum() == null) {
            throw new Exception("Necessário o tipo de importacão.");
        }
        if (UteisValidacao.emptyNumber(getConfigTO().getUsuarioResponsavelImportacao())) {
            throw new Exception("Necessário o usuário responsável pela importação.");
        }

        this.setCache(new ImportacaoCache(this.con, getConfigTO().getUsuarioResponsavelImportacao()));
        this.setName(getNameThread());
    }

    private String getNameThread() {
        return ("IMPORTACAO_" + getConfigTO().getChave() + "_" + getConfigTO().getTipoImportacaoEnum().name());
    }

    @Override
    public void run() {
        try {
            synchronized (this) {
                try {
                    Uteis.logar(null, "Iniciei ThreadImportacao | Chave " + getConfigTO().getChave());
                    switch (configTO.getTipoImportacaoEnum()) {
                        case CLIENTE:
                            iniciarImportacaoClientes();
                            break;
                        case CONTRATO:
                            iniciarImportacaoContratos();
                            break;
                        case COLABORADOR:
                            iniciarImportacaoColaborador();
                            break;
                        case FORNECEDOR:
                            iniciarImportacaoFornecedor();
                            break;
                        case CONTA_FINANCEIRO:
                            iniciarImportacaoContas();
                            break;
                        case PRODUTO:
                            iniciarImportacaoProdutos();
                            break;
                        case MEMBERS_EVO:
                            iniciarImportacaoMembersEvo();
                            break;
                        case PARCELAS_PAGAMENTOS:
                            iniciarImportacaoParcelasPagamentos();
                        case ALUNOS_TURMAS:
                            iniciarImportacaoAlunosTumas();
                            break;
                        case TURMAS:
                            iniciarImportacaoTumas();
                            break;
                        case TREINO_ATIVIDADES:
                            iniciarImportacaoTreinoAtividades();
                            break;
                        case TREINO_PROGRAMAS:
                            iniciarImportacaoTreinoProgramas();
                            break;
                        case TREINO_ATIVIDADE_FICHA:
                            iniciarImportacaoTreinoAtividadeFicha();
                            break;
                    }
                    Uteis.logar(null, "Terminando ThreadImportacao | Chave " + getConfigTO().getChave());
                } catch (Exception ex) {
                    Uteis.logar(null, "ERRO ThreadImportacao | Chave " + getConfigTO().getChave() + " | " + ex.getMessage());
                    ex.printStackTrace();
                } finally {
                    Uteis.logar(null, "Finalizei ThreadImportacao | Chave " + getConfigTO().getChave());
                    if (getCon() != null) {
                        getCon().close();
                    }
                    this.interrupt();
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, ThreadImportacao.class);
        }
    }

    private void iniciarImportacaoParcelasPagamentos() throws Exception {
        Integer total = getListaParcelasPagamentosJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;


        for (ParcelaPagamentoJSON parcelaPagamentoJSON : getListaParcelasPagamentosJSON()) {

            String msgSucessoImportacao = "Parcela de ID Externo: " + parcelaPagamentoJSON.getIdExternoParcela()
                    + " foi importada com sucesso. (ID Ext. Contrato: " + parcelaPagamentoJSON.getIdExternoContrato() + "). ";
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                processarImportacaoParcelaPagamento(parcelaPagamentoJSON);

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, msgSucessoImportacao));
                ++sucesso;

            } catch (Exception e) {
                e.printStackTrace();
                ++falha;
                String msgErroImportacao = "ERRO importando Parcela (ID Ext.: " + parcelaPagamentoJSON.getIdExternoParcela() + ") - " + e.getMessage();
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msgErroImportacao));

            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(parcelaPagamentoJSON).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }

        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoTreinoAtividades() throws Exception {
        Integer total = getListaTreinoAtividadesJson().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);
        try {
            String urlTreino = PropsService.getPropertyValue(getChave(), PropsService.urlTreino);
            String path = (urlTreino + "/prest/atividades/" + getChave() + "/importar-atividades-by-dto");
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, new HashMap<>(), new JSONArray(getListaTreinoAtividadesJson()).toString(), MetodoHttpEnum.POST);
            service = null;
            JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
            processoVO.atualizarJSONStatus(total, Integer.valueOf(jsonResp.getString("sucesso").split(" ")[0]),
                    Integer.valueOf(jsonResp.getString("sucesso").split(" ")[7]), total);
            processoDAO.atualizarStatus(processoVO);
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, jsonResp.getString("sucesso")));
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgErroImportacao = "ERRO importando treino atividades - " + ex.getMessage();
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msgErroImportacao));
        }
    }

    private void iniciarImportacaoTreinoProgramas() throws Exception {
        Integer total = getListaTreinoProgramasJson().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);
        try {
            String urlTreino = PropsService.getPropertyValue(getChave(), PropsService.urlTreino);
            String path = (urlTreino + "/prest/programa/" + getChave() + "/importar-programas-by-dto");
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, new HashMap<>(), new JSONArray(getListaTreinoProgramasJson()).toString(), MetodoHttpEnum.POST);
            service = null;
            JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
            processoVO.atualizarJSONStatus(total, Integer.valueOf(jsonResp.getString("sucesso").split(" ")[0]),
                    Integer.valueOf(jsonResp.getString("sucesso").split(" ")[7]), total);
            processoDAO.atualizarStatus(processoVO);
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, jsonResp.getString("sucesso")));
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgErroImportacao = "ERRO importando treino atividades - " + ex.getMessage();
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msgErroImportacao));
        }
    }

    private void iniciarImportacaoTreinoAtividadeFicha() throws Exception {
        Integer total = getListaTreinoAtividadeFichaJson().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);
        try {
            String urlTreino = PropsService.getPropertyValue(getChave(), PropsService.urlTreino);
            String path = (urlTreino + "/prest/atividades/" + getChave() + "/importar-atividades-ficha-series-by-dto");
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, new HashMap<>(), new JSONArray(getListaTreinoAtividadeFichaJson()).toString(), MetodoHttpEnum.POST);
            service = null;
            JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
            processoVO.atualizarJSONStatus(total, Integer.valueOf(jsonResp.getString("sucesso").split(" ")[0]),
                    Integer.valueOf(jsonResp.getString("sucesso").split(" ")[7]), total);
            processoDAO.atualizarStatus(processoVO);
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, jsonResp.getString("sucesso")));
        } catch (Exception ex) {
            ex.printStackTrace();
            String msgErroImportacao = "ERRO importando treino atividades fichas - " + ex.getMessage();
            processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msgErroImportacao));
        }
    }

    private void processarImportacaoParcelaPagamento(ParcelaPagamentoJSON parcelaPagamentoJSON) throws Exception {
        //inicializando daos
        Cliente clienteDao = new Cliente(con);
        Contrato contratoDao = new Contrato(con);
        MovParcela movParcelaDao = new MovParcela(con);
        FormaPagamento formaPagamentoDao = new FormaPagamento(con);

        ClienteVO clienteVO = clienteDao.consultarPorMatriculaExterna(
                parcelaPagamentoJSON.getMatriculaExterna(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if(clienteVO == null) {
            throw new Exception("Cliente de Matricula Externa " + parcelaPagamentoJSON.getMatriculaExterna() + " não encontrado!");
        }

        //Encontrar contrato por id externo
        ContratoVO contratoVO = contratoDao.consultarPorIdExterno(
                parcelaPagamentoJSON.getIdExternoContrato().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);


        if(contratoVO == null) {
            throw new Exception("Contrato de Id Externo " + parcelaPagamentoJSON.getIdExternoContrato() + " não encontrado!");
        }

        String errosParcelaSufixo = "A Parcela de ID Externo " + parcelaPagamentoJSON.getIdExternoParcela();

        //validando situacao
        String situacaoParcelaFormatado = formatarSituacaoParcela(parcelaPagamentoJSON.getSituacaoParcela());
        if(UteisValidacao.emptyString(situacaoParcelaFormatado)) {
            throw new Exception(errosParcelaSufixo + " não foi importada: coluna SITUACAO na planilha está vazia ou com valor inválido.");

        }

        //valida forma de pagamento
        FormaPagamentoVO formaPagamentoVO = null;
        if(situacaoParcelaFormatado.equalsIgnoreCase("PG")) {
            formaPagamentoVO = formaPagamentoDao.consultarPorChavePrimaria(parcelaPagamentoJSON.getCodigoFormaPagamento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(formaPagamentoVO == null) {
                throw new Exception(errosParcelaSufixo + " não foi importada pois a forma de pagamento " + parcelaPagamentoJSON.getFormaPagamento() + " (Cod.: " + parcelaPagamentoJSON.getCodigoFormaPagamento() + ") não foi encontrada no sistema");
            } else {
                if (formaPagamentoVO.getTipoFormaPagamento().equalsIgnoreCase("CA") && parcelaPagamentoJSON.getQtdParcelasCartao() == 0) {
                    if (parcelaPagamentoJSON.getFormaPagamento().endsWith("x")) {
                        int length = parcelaPagamentoJSON.getFormaPagamento().length();
                        String nrParcela = parcelaPagamentoJSON.getFormaPagamento().substring(length - 3, length - 1).trim();
                        if (nrParcela.matches("\\d+")) {
                            parcelaPagamentoJSON.setQtdParcelasCartao(Integer.parseInt(nrParcela));
                        }
                    }
                    if (parcelaPagamentoJSON.getQtdParcelasCartao() == 0) {
                        throw new Exception(errosParcelaSufixo + " não foi importada: é necessário informar a qtd de parcelas do cartao de crédito");
                    }
                }
            }
        }

        //Validar se parcela com esse id externo ja existe
        if( movParcelaDao.consultarPorIdExterno(
                parcelaPagamentoJSON.getIdExternoParcela(), Uteis.NIVELMONTARDADOS_DADOSBASICOS) != null) {
            throw new Exception(errosParcelaSufixo + " já existe no sistema.");
        }

        //encontrar parcela a ser substituida no contrato
        MovParcelaVO parcelaEncontrada =  movParcelaDao.consultarPorNumeroParcelaNoContrato(
                parcelaPagamentoJSON.getNumeroParcela(), contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if(parcelaEncontrada == null) {
            throw new Exception("A parcela de número " + parcelaPagamentoJSON.getNumeroParcela() + " nao foi encontrada no contrato");
        }

        //renegociar parcela paga SE valor for diferente
        int comparacaoValorParcelas = parcelaPagamentoJSON.getValorParcela().compareTo(parcelaEncontrada.getValorParcela());
        if(comparacaoValorParcelas != 0) {
            IntegracaoImportacao integracaoImportacao = new IntegracaoImportacao(con);
            MovParcelaVO parcelaRenegociada = integracaoImportacao.renegociarParcela(
                    parcelaEncontrada,
                    parcelaPagamentoJSON.getValorParcela(),
                    getCache().getUsuarioVOImportacao()
            );
            parcelaEncontrada = parcelaRenegociada;
        } else {
            parcelaEncontrada.setValorParcela(parcelaPagamentoJSON.getValorParcela());
        }

        parcelaEncontrada.setIdExterno(parcelaPagamentoJSON.getIdExternoParcela().intValue());
        if (situacaoParcelaFormatado.equals("CA") && parcelaEncontrada.getSituacao().equals("PG")) {
            throw new Exception(errosParcelaSufixo + " está paga e não pode ser cancelada");
        }
        parcelaEncontrada.setSituacao(situacaoParcelaFormatado);
        parcelaEncontrada.setDataVencimento(parcelaPagamentoJSON.getDtVencimento());
        Date dataPagamento = null;
        if (situacaoParcelaFormatado.equalsIgnoreCase("pg")) {
            Date hoje = new Date();

            if (parcelaPagamentoJSON.getDtPagamento() == null || parcelaPagamentoJSON.getDtPagamento().after(hoje)) {
                dataPagamento = hoje;
            } else {
                dataPagamento = parcelaPagamentoJSON.getDtPagamento();
            }

            parcelaEncontrada.setDataPagamento(dataPagamento);

            movParcelaDao.alterarSemCommit(parcelaEncontrada);

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setValor(parcelaPagamentoJSON.getValorParcela());
            movPagamentoVO.setValorTotal(parcelaPagamentoJSON.getValorParcela());
            movPagamentoVO.setDataPagamento(dataPagamento);
            movPagamentoVO.setDataQuitacao(dataPagamento);
            movPagamentoVO.setDataLancamento(dataPagamento);
            movPagamentoVO.setResponsavelPagamento(contratoVO.getResponsavelContrato());
            movPagamentoVO.setPessoa(contratoVO.getPessoa());
            movPagamentoVO.setEmpresa(contratoVO.getEmpresa());
            movPagamentoVO.setNomePagador(contratoVO.getPessoa().getNome());
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setObservacao("PAGAMENTO IMPORTADO");

            movPagamentoVO.setFormaPagamento(formaPagamentoVO);
            boolean isFormaPagamentoCartaoCredito = formaPagamentoVO.getTipoFormaPagamento().equalsIgnoreCase("CA");
            if(isFormaPagamentoCartaoCredito) {
                movPagamentoVO.setNrParcelaCartaoCredito(parcelaPagamentoJSON.getQtdParcelasCartao());
                movPagamentoVO.setNsu(parcelaPagamentoJSON.getNumeroNsu().toString());
                Double valorParcelaCartao = movPagamentoVO.getValor() / movPagamentoVO.getNrParcelaCartaoCredito();
                Double residuo = movPagamentoVO.getValor() % movPagamentoVO.getNrParcelaCartaoCredito();

                for (int i = 1; i <= parcelaPagamentoJSON.getQtdParcelasCartao(); i++) {
                    CartaoCreditoVO novocc = new CartaoCreditoVO();
                    novocc.setDataCompensacao(Uteis.somarMeses(dataPagamento, i));
                    novocc.setValor(valorParcelaCartao);
                    if (residuo > 0.0) {
                        novocc.setValor(novocc.getValor() + residuo);
                        residuo = 0.0;
                    }
                    novocc.setValorTotal(novocc.getValor());
                    novocc.setSituacao("EA");
                    novocc.setOperadora(cache.obterOperadora("importacao", true));
                    novocc.setNrParcela(i);
                    movPagamentoVO.getCartaoCreditoVOs().add(novocc);
                }
            }

            PagamentoMovParcelaVO pagamentoMovParcelaVO = new PagamentoMovParcelaVO();
            pagamentoMovParcelaVO.setMovParcela(parcelaEncontrada);
            pagamentoMovParcelaVO.setValorPago(parcelaPagamentoJSON.getValorParcela());
            movPagamentoVO.setPagamentoMovParcelaVOs(new ArrayList<>());
            movPagamentoVO.getPagamentoMovParcelaVOs().add(pagamentoMovParcelaVO);

            ImportarPagamento importarPagamento = new ImportarPagamento(con);
            importarPagamento.incluirPagamento(movPagamentoVO, contratoVO.getCodigo());
        } else {
            movParcelaDao.alterarSemCommit(parcelaEncontrada);
        }
    }

    private String formatarSituacaoParcela(String situacaoParcelaExcel) {
        if (situacaoParcelaExcel == null || situacaoParcelaExcel.trim().isEmpty()) {
            return null;
        }
        String situacaoNormalizada = situacaoParcelaExcel.trim().toLowerCase();
        switch (situacaoNormalizada) {
            case "em aberto":
            case "em-aberto":
            case "ea":
            case "aberto":
                return "EA";
            case "pago":
            case "pg":
                return "PG";
            case "cancelado":
            case "cancelada":
            case "ca":
                return "CA";
            default:
                return null;
        }
    }


    private void iniciarImportacaoMembersEvo() throws Exception {
        if (UteisValidacao.emptyString(this.configTO.getIdsMembers())) {
            throw new Exception("Matriculas não informadas!");
        }

        IntegracaoMemberService integracaoMemberService = new IntegracaoMemberService(con, getCache(), configTO);
        List<Integer> idsMembers = new ArrayList<>();
        if (!UteisValidacao.emptyString(this.configTO.getIdsMembers())) {
            idsMembers.addAll(integracaoMemberService.obterListaIds(this.configTO.getIdsMembers(),0));
        }

        switch (getConfigTO().getTipoOperacaoIntegracaoMembersEnum()) {
            case MEMBERS:
                integracaoMemberService.processarMembers(this.configTO.getIntegracaoMemberVO(), idsMembers, true, true);
                break;
            case RECEBIMENTOS_PARCELAS:
                integracaoMemberService.processarRecebimentosParcelasPorIdExternoMovParcela(this.configTO.getIntegracaoMemberVO(), idsMembers);
                break;
            default:
                break;
        }
    }

    private void iniciarImportacaoAlunosTumas() throws Exception {
        Integer total = getListaAlunoTurmaJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);
        ImportarAlunosTurma importarAlunosTurmaDao = new ImportarAlunosTurma(con, getConfigTO());

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        for (AlunoTurmaImportacaoJSON json : getListaAlunoTurmaJSON()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                if (UteisValidacao.emptyNumber(json.getCodigoDeContrato()) && !UteisValidacao.emptyNumber(json.getIdExterno())) {
                    String sql = "select codigo from contrato where id_externo = " + json.getIdExterno() + " and empresa = " + json.getEmpresa();
                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
                    if (rs.next()) {
                        json.setCodigoDeContrato(rs.getLong("codigo"));
                    } else {
                        throw new Exception("Contrato não encontrado com idExterno: " + json.getIdExterno());
                    }
                }

                ContratoModalidade cmDAO = new ContratoModalidade(con);
                List<ContratoModalidadeVO> contratoModalidadeVOS = cmDAO.consultarContratoModalidades(json.getCodigoDeContrato().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                cmDAO = null;

                boolean possuiModalidade = contratoModalidadeVOS.stream().anyMatch(cm -> cm.getModalidade().getCodigo().intValue() == (json.getCodigoDaModalidade().intValue()));
                if (!possuiModalidade) {
                    ModalidadeVO modalidadeVO = cache.obterModalidadeVO(json.getCodigoDaModalidade().intValue());
                    AdicionarModalidadeEmPlano adicionarModalidadeEmPlano = new AdicionarModalidadeEmPlano(con);
                    ContratoVO contratoVO = new ContratoVO();
                    contratoVO.setCodigo(json.getCodigoDeContrato().intValue());
                    adicionarModalidadeEmPlano.adicionarModalidadeContrato(contratoVO, modalidadeVO, configExcelImportacaoTO.getUsuarioResponsavelImportacao());
                }

                importarAlunosTurmaDao.importarAlunoTurma(json);

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getCodigoDeContrato() + " - Importado com sucesso."));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, ex.getMessage()));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoTumas() throws Exception {
        Integer total = getListaTurmaJson().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);
        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        ImportarTurma importarTurma = new ImportarTurma(con, getConfigTO());

        for (TurmaImportacaoJSON turmaImportacaoJSON : getListaTurmaJson()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                importarTurma.importarTurma(turmaImportacaoJSON);
                String msg = String.format("%d - turma \"%s\" importada com sucesso.", turmaImportacaoJSON.getIdExterno(), turmaImportacaoJSON.getTurma());
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, msg));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, ex.getMessage()));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(turmaImportacaoJSON).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoClientes() throws Exception {
        Integer total = getListaClientesJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        for (ClienteImportacaoJSON json : getListaClientesJSON()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                ImportarCliente importarClienteDAO = new ImportarCliente(getConfigTO(), con);
                importarClienteDAO.importarCliente(json, configExcelImportacaoTO, getCache());
                importarClienteDAO = null;

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getNome() + " - Importado com sucesso."));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                String msgErro = "ERRO importando aluno " + json.getNome() + " (CPF: " + json.getCpf() + ") - " + ex.getMessage();
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, msgErro));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoContratos() throws Exception {
        Conexao.guardarConexaoForJ2SE(getConfigTO().getChave(), con);

        Integer total = getListaContratosJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        getConfigTO().setTipoImportacaoEnum(TipoImportacaoEnum.CONTRATO);
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        ContratoVO contratoVO = null;
        for (ContratoImportacaoJSON json : getListaContratosJSON()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                String[] horariosTurma = null;
                if(json.getIdExternoTurma() != null) {
                    horariosTurma = UteisValidacao.validarHorarioTurmaPlanilha(json.getHorarioTurma());
                }

                ImportarContrato importarContrato = new ImportarContrato(getConfigTO(), con);
                contratoVO = importarContrato.importarContrato(json, getCache());

                if(horariosTurma != null && horariosTurma.length > 0) {
                    importarContrato.importarVinculoTurma(horariosTurma, contratoVO, json, con);
                }

                importarContrato = null;

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getIdExterno() + " - Importado com sucesso."));
                ++sucesso;
            } catch (Exception ex) {
                if (contratoVO != null && contratoVO.getCodigo().intValue() > 0) {
                    estornarContrato(contratoVO);
                }
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, "Erro ao importar contrato de id externo " + json.getIdExterno() + " - " +  ex.getMessage()));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }

        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void estornarContrato(ContratoVO contratoVO) {
        try {
            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(this.con);
            MovPagamento movPagamentoDAO = new MovPagamento(this.con);
            MovParcela movParcelaDAO = new MovParcela(this.con);
            Cliente clienteDAO = new Cliente(con);
            Contrato contratoDAO = new Contrato(con);

            ClienteVO cliente = null;
            contratoVO.setUsuarioVO(cache.obterUsuarioVO(1));

            List<ReciboPagamentoVO> listaReciboPagamento = reciboPagamentoDAO.consularReciboPagamParcelaPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            reciboPagamentoDAO = null;

            if (!listaReciboPagamento.isEmpty()) {
                contratoVO.setMovParcelaVOs(new ArrayList<>());
                contratoVO.setListaEstornoRecibo(new ArrayList<>());
                for (ReciboPagamentoVO recibo : listaReciboPagamento) {
                    EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                    estornoRecibo.setReciboPagamentoVO(recibo);
                    estornoRecibo.setListaMovPagamento(movPagamentoDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                    estornoRecibo.setListaMovParcela(movParcelaDAO.consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));

                    //transações de cartão de crédito
                    contratoVO.montarListaTransacoes(estornoRecibo.getListaMovParcela(), con);
                    contratoVO.getListaEstornoRecibo().add(estornoRecibo);
                }

            } else {
                contratoVO.setMovParcelaVOs(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

                //transações de cartão de crédito
                contratoVO.montarListaTransacoes(contratoVO.getMovParcelaVOs(), con);
            }

            contratoVO.setPrecisaEstornarTransacoes(false);

            contratoVO.montarListaItensRemessa(movParcelaDAO.consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), con);

            cliente = clienteDAO.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDAO.estornoContrato(contratoVO, cliente, null, null);
            ZillyonWebFacade zawDAO = new ZillyonWebFacade(con);
            zawDAO.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        } catch (Exception e) {
            String msg = String.format("Erro ao estornar contrato -> %d | Exceção -> %s",
                    contratoVO.getCodigo(), e.getMessage());
            Uteis.logarDebug(msg);
        }

    }

    private void iniciarImportacaoColaborador() throws Exception {
        Integer total = getListaColaboradorJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        for (ColaboradorImportacaoJSON json : getListaColaboradorJSON()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                ImportarColaborador importarColaborador = new ImportarColaborador(getConfigTO(), con);
                importarColaborador.importarColaborador(json, configExcelImportacaoTO, getCache());
                importarColaborador = null;

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getIdExterno() + " - Importado com sucesso."));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, ex.getMessage()));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }

        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoProdutos() throws Exception {
        Integer total = getListaProdutosJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        for (ProdutoImportacaoJSON json : getListaProdutosJSON()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                ImportarProduto importarProduto = new ImportarProduto(getConfigTO(), con);
                importarProduto.importarProduto(json, configExcelImportacaoTO, getCache());
                importarProduto = null;

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getIdExterno() + " - " + json.getDescricao() + " - Importado com sucesso."));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, ex.getMessage()));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }

        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoFornecedor() throws Exception {
        Integer total = getListaFornecedorJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;
        for (FornecedorImportacaoJSON json : getListaFornecedorJSON()) {
            try {
                processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);

                ImportarFornecedor importarFornecedor = new ImportarFornecedor(getConfigTO(), con);
                importarFornecedor.importarFornecedor(json, configExcelImportacaoTO, getCache());
                importarFornecedor = null;

                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getNome() + " - Importado com sucesso."));
                ++sucesso;
            } catch (Exception ex) {
                ex.printStackTrace();
                ++falha;
                processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, ex.getMessage()));
            } finally {
                processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                processoDAO.atualizarStatus(processoVO);
                processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                if (Thread.currentThread().isInterrupted()) {
                    JSONObject statusJson = new JSONObject(processoVO.getStatus());
                    statusJson.put("parado", true);
                    processoVO.setStatus(statusJson.toString());
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                    break;
                }
            }
        }
        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    private void iniciarImportacaoContas() throws Exception {
        Integer total = getListaContaJSON().size();
        if (UteisValidacao.emptyNumber(total)) {
            return;
        }
        ProcessoImportacaoVO processoVO = processoDAO.iniciarProcessoImportacaoVO(getConfigTO(), total);

        int atual = 0;
        Integer sucesso = 0;
        Integer falha = 0;

        EmpresaVO empresaVO = getCache().obterEmpresaVO(getListaContaJSON().get(0).getEmpresa());

        Caixa caixaDAO = new Caixa(con);
        CaixaVO caixaVO = new CaixaVO();
        try {
            caixaVO.setUsuarioVo(getCache().getUsuarioVOImportacao());
            caixaVO.setDataAbertura(Calendario.hoje());
            caixaVO.setDataTrabalho(Calendario.hoje());
            caixaVO.setEmpresaVo(empresaVO);
            caixaDAO.incluir(caixaVO);

            for (ContaImportacaoJSON json : getListaContaJSON()) {
                try {
                    processoVO.atualizarJSONStatus(++atual, sucesso, falha, total);
                    processoDAO.atualizarStatus(processoVO);

                    ImportarConta importarConta = new ImportarConta(getConfigTO(), con);
                    importarConta.importarConta(json, configExcelImportacaoTO, getCache(), empresaVO, caixaVO);
                    importarConta = null;

                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, json.getDescricao() + " - Importada com sucesso."));
                    ++sucesso;
                } catch (Exception ex) {
                    ex.printStackTrace();
                    ++falha;
                    processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.ERRO, ex.getMessage()));
                } finally {
                    processoVO.atualizarJSONStatus(atual, sucesso, falha, total);
                    processoDAO.atualizarStatus(processoVO);
                    processoItemDAO.incluir(new ProcessoImportacaoItemVO(processoVO, new JSONObject(json).toString()));
                    if (Thread.currentThread().isInterrupted()) {
                        JSONObject statusJson = new JSONObject(processoVO.getStatus());
                        statusJson.put("parado", true);
                        processoVO.setStatus(statusJson.toString());
                        processoLogDAO.incluir(new ProcessoImportacaoLogVO(processoVO, TipoLogEnum.SUCESSO, "Parado manualmente"));
                        break;
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception(ex);
        } finally {
            caixaVO.setResponsavelFechamento(getCache().getUsuarioVOImportacao());
            caixaVO.setDataFechamento(Calendario.hoje());
            caixaDAO.alterar(caixaVO);
            caixaDAO = null;
        }

        processoDAO.finalizarProcessoImportacaoVO(processoVO, getConfigTO());
    }

    public List<ParcelaPagamentoJSON> getListaParcelasPagamentosJSON() {
        if (listaParcelasPagamentosJSON == null) {
            listaParcelasPagamentosJSON = new ArrayList<>();
        }
        return listaParcelasPagamentosJSON;
    }

    public void setListaParcelasPagamentosJSON(List<ParcelaPagamentoJSON> listaParcelasPagamentosJSON) {
        this.listaParcelasPagamentosJSON = listaParcelasPagamentosJSON;
    }

    public List<ProdutoImportacaoJSON> getListaProdutosJSON() {
        if (listaProdutosJSON == null) {
            listaProdutosJSON = new ArrayList<>();
        }
        return listaProdutosJSON;
    }

    public void setListaProdutosJSON(List<ProdutoImportacaoJSON> listaProdutosJSON) {
        this.listaProdutosJSON = listaProdutosJSON;
    }

    public List<FornecedorImportacaoJSON> getListaFornecedorJSON() {
        if (listaFornecedorJSON == null) {
            listaFornecedorJSON = new ArrayList<>();
        }
        return listaFornecedorJSON;
    }

    public void setListaFornecedorJSON(List<FornecedorImportacaoJSON> listaFornecedorJSON) {
        this.listaFornecedorJSON = listaFornecedorJSON;
    }

    public List<ColaboradorImportacaoJSON> getListaColaboradorJSON() {
        if (listaColaboradorJSON == null) {
            listaColaboradorJSON = new ArrayList<>();
        }
        return listaColaboradorJSON;
    }

    public void setListaColaboradorJSON(List<ColaboradorImportacaoJSON> listaColaboradorJSON) {
        this.listaColaboradorJSON = listaColaboradorJSON;
    }

    public List<ContaImportacaoJSON> getListaContaJSON() {
        if (listaContaJSON == null) {
            listaContaJSON = new ArrayList<>();
        }
        return listaContaJSON;
    }

    public void setListaContaJSON(List<ContaImportacaoJSON> listaContaJSON) {
        this.listaContaJSON = listaContaJSON;
    }

    public ImportacaoConfigTO getConfigTO() {
        if (configTO == null) {
            configTO = new ImportacaoConfigTO();
        }
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public List<ClienteImportacaoJSON> getListaClientesJSON() {
        if (listaClientesJSON == null) {
            listaClientesJSON = new ArrayList<>();
        }
        return listaClientesJSON;
    }

    public List<AlunoTurmaImportacaoJSON> getListaAlunoTurmaJSON() {
        if (listaAlunoTurmaJson == null) {
            listaAlunoTurmaJson = new ArrayList<>();
        }
        return listaAlunoTurmaJson;
    }

    public List<TurmaImportacaoJSON> getListaTurmaJson() {
        if (listaTurmaJson == null) {
            listaTurmaJson = new ArrayList<>();
        }
        return listaTurmaJson;
    }

    public void setListaTurmaJson(List<TurmaImportacaoJSON> listaTurmaJson) {
        this.listaTurmaJson = listaTurmaJson;
    }

    public void setListaClientesJSON(List<ClienteImportacaoJSON> listaClientesJSON) {
        this.listaClientesJSON = listaClientesJSON;
    }

    public ImportacaoCache getCache() {
        return cache;
    }

    public void setCache(ImportacaoCache cache) {
        this.cache = cache;
    }

    public List<ContratoImportacaoJSON> getListaContratosJSON() {
        if (listaContratosJSON == null) {
            listaContratosJSON = new ArrayList<>();
        }
        return listaContratosJSON;
    }

    public void setListaContratosJSON(List<ContratoImportacaoJSON> listaContratosJSON) {
        this.listaContratosJSON = listaContratosJSON;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public List<TreinoAtividadeJSON> getListaTreinoAtividadesJson() {
        return listaTreinoAtividadesJson;
    }

    public void setListaTreinoAtividadesJson(List<TreinoAtividadeJSON> listaTreinoAtividadesJson) {
        this.listaTreinoAtividadesJson = listaTreinoAtividadesJson;
    }

    public List<ProgramaFichaJSON> getListaTreinoProgramasJson() {
        return listaTreinoProgramasJson;
    }

    public void setListaTreinoProgramasJson(List<ProgramaFichaJSON> listaTreinoProgramasJson) {
        this.listaTreinoProgramasJson = listaTreinoProgramasJson;
    }

    public List<AtividadeFichaJSON> getListaTreinoAtividadeFichaJson() {
        return listaTreinoAtividadeFichaJson;
    }

    public void setListaTreinoAtividadeFichaJson(List<AtividadeFichaJSON> listaTreinoAtividadeFichaJson) {
        this.listaTreinoAtividadeFichaJson = listaTreinoAtividadeFichaJson;
    }
}

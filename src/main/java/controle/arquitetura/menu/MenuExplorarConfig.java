package controle.arquitetura.menu;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.MenuSistemaGrupoEnum;
import controle.arquitetura.MenuSistemaSubgrupoEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;

public class MenuExplorarConfig {
    private MenuSistemaGrupoEnum grupo;
    private MenuSistemaSubgrupoEnum subgrupoEnum;
    private MenuExplorarConfig[] subgrupos;
    private List<FuncionalidadeSistemaEnum> funcionalidades = new ArrayList<>();
    private List<FuncionalidadeSistemaEnum> funcionalidadesOld;
    private List<FuncionalidadeSistemaEnumTO> funcionalidadesFavorito = new ArrayList<>();


    public MenuExplorarConfig(MenuSistemaGrupoEnum grupo, FuncionalidadeSistemaEnum[] funcionalidades) {
        this.grupo = grupo;
        validateFuncionalidades(funcionalidades);
    }

    public MenuExplorarConfig(MenuSistemaSubgrupoEnum subgrupoEnum, FuncionalidadeSistemaEnum[] funcionalidades) {
        this.subgrupoEnum = subgrupoEnum;
        validateFuncionalidades(funcionalidades);
    }

    public MenuExplorarConfig(MenuSistemaGrupoEnum grupo, FuncionalidadeSistemaEnum[] funcionalidades, MenuExplorarConfig[] subgrupos) {
        this.grupo = grupo;
        validateFuncionalidades(funcionalidades);
        this.subgrupos = subgrupos;
        validateFuncionalidadesSubGrupo();
    }

    public MenuExplorarConfig(MenuSistemaGrupoEnum grupo, MenuExplorarConfig[] subgrupos) {
        this.grupo = grupo;
        this.subgrupos = subgrupos;
    }

    public MenuSistemaGrupoEnum getGrupo() {
        return grupo;
    }

    public void setGrupo(MenuSistemaGrupoEnum grupo) {
        this.grupo = grupo;
    }

    public MenuSistemaSubgrupoEnum getSubgrupoEnum() {
        return subgrupoEnum;
    }

    public void setSubgrupoEnum(MenuSistemaSubgrupoEnum subgrupoEnum) {
        this.subgrupoEnum = subgrupoEnum;
    }

    public MenuExplorarConfig[] getSubgrupos() {
        return subgrupos;
    }

    public void setSubgrupos(MenuExplorarConfig[] subgrupos) {
        this.subgrupos = subgrupos;
    }

    public List<FuncionalidadeSistemaEnum> getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(List<FuncionalidadeSistemaEnum> funcionalidades) {
        this.funcionalidades = funcionalidades;
    }

    public List<FuncionalidadeSistemaEnum> getFuncionalidadesOld() {
        if (funcionalidadesOld == null) {
            funcionalidadesOld = new ArrayList<>();
        }
        return funcionalidadesOld;
    }

    public void setFuncionalidadesOld(List<FuncionalidadeSistemaEnum> funcionalidadesOld) {
        this.funcionalidadesOld = funcionalidadesOld;
    }

    private void validateFuncionalidades(FuncionalidadeSistemaEnum[] funcionalidades) {
        if (funcionalidades != null) {
            for (FuncionalidadeSistemaEnum funcionalidade : funcionalidades) {
                long millisInicioProcessamentoMenu = System.currentTimeMillis();
                boolean funcionalidadeAtiva = isFuncionalidadeAtiva(funcionalidade);
                boolean  permiteRenderizar = isPermiteRenderizar(funcionalidade);
                boolean funcionalidadFavorito = isFuncionalidadeFavorito(funcionalidade);
                if (permiteRenderizar && funcionalidadeAtiva) {
                    this.funcionalidades.add(funcionalidade);
                    this.funcionalidadesFavorito.add(new FuncionalidadeSistemaEnumTO(funcionalidade, funcionalidadFavorito));
                }
                long millisProcessamentoMenu = System.currentTimeMillis() - millisInicioProcessamentoMenu;
                if (millisProcessamentoMenu > 1) {
                    Uteis.logarDebug("Processamento funcionalidade " + funcionalidade.getName() + " com resultado "+permiteRenderizar+ " demorou " + millisProcessamentoMenu + " milissegundos.");
                }
            }
        }
    }

    private boolean isFuncionalidadeAtiva(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum){
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if(loginControle != null){
            return loginControle.isFuncionalidadeAtiva(funcionalidadeSistemaEnum);
        }

        return false;
    }

    private boolean isFuncionalidadeFavorito(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum){
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if(loginControle != null){
            return loginControle.isFuncionalidadeFavorito(funcionalidadeSistemaEnum);
        }
        return false;
    }

    private boolean isPermiteRenderizar(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum) {
        boolean renderizarModulo = false;
        try {
            renderizarModulo = getManagedBeanValue(funcionalidadeSistemaEnum.getExpressaoRenderizar());
        } catch (Exception e) {
            Uteis.logarDebug("Falha ao processar exibição da funcionalidade " + funcionalidadeSistemaEnum.name());
            e.printStackTrace();
        }
        return renderizarModulo;
    }

    private boolean getManagedBeanValue(final String param) {
        return JSFUtilities.getManagedBeanValue(param) != null && (Boolean) JSFUtilities.getManagedBeanValue(param);
    }

    public List<FuncionalidadeSistemaEnumTO> getFuncionalidadesFavorito() {
        if (funcionalidadesFavorito == null) {
            funcionalidadesFavorito = new ArrayList<>();
        }
        return funcionalidadesFavorito;
    }

    public void setFuncionalidadesFavorito(List<FuncionalidadeSistemaEnumTO> funcionalidadesFavorito) {
        this.funcionalidadesFavorito = funcionalidadesFavorito;
    }

    private void validateFuncionalidadesSubGrupo() {
        if (this.subgrupos != null) {
            for (MenuExplorarConfig menuExplorarConfig : this.subgrupos) {
                for (FuncionalidadeSistemaEnum funcionalidade : menuExplorarConfig.funcionalidades) {
//                    menuExplorarConfig.getFuncionalidadesFavorito().add(new FuncionalidadeSistemaEnumTO(funcionalidade, funcionalidadFavorito))
//                    long millisInicioProcessamentoMenu = System.currentTimeMillis();
//                    boolean funcionalidadeAtiva = isFuncionalidadeAtiva(funcionalidade);
//                    boolean  permiteRenderizar = isPermiteRenderizar(funcionalidade);
//                    boolean funcionalidadFavorito = isFuncionalidadeFavorito(funcionalidade);
//                    if (permiteRenderizar && funcionalidadeAtiva) {
//
//                        this.funcionalidades.add(funcionalidade);
//                        this.funcionalidadesFavorito.add();
//                    }
//                    long millisProcessamentoMenu = System.currentTimeMillis() - millisInicioProcessamentoMenu;
//                    if (millisProcessamentoMenu > 1) {
//                        Uteis.logarDebug("Processamento funcionalidade " + funcionalidade.getName() + " com resultado "+permiteRenderizar+ " demorou " + millisProcessamentoMenu + " milissegundos.");
//                    }
                }
            }
        }
    }
}

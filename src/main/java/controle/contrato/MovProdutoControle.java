package controle.contrato;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.MovProduto;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Classe responsável por implementar a intera??o entre os componentes JSF das p?ginas
 * movProdutoForm.jsp movProdutoCons.jsp) com as funcionalidades da classe <code>MovProduto</code>.
 * Implemta??o da camada controle (Backing Bean).
 * @see SuperControle
 * @see MovProduto
 * @see MovProdutoVO
 */
public class MovProdutoControle extends SuperControle {

    private MovProdutoVO movProdutoVO;
//    protected List listaSelectItemProduto;
//    protected List listaSelectItemContrato;
//    protected List listaSelectItemPessoa;
    protected List listaSelectItemEmpresa;
//    protected List listaSelectItemResponsavelLancamento;
    /**
     * Interface <code>MovProdutoInterfaceFacade</code> responsável pela interconex?o da camada de controle com a camada de neg?cio.
     * Criando uma independ?ncia da camada de controle com rela??o a tenologia de persist?ncia dos dados (DesignPatter: Fa?ade).
     */
    private MovProdutoParcelaVO movProdutoParcelaVO;
//    protected List listaSelectItemMovParcela;
//    protected List listaSelectItemMovProduto;
    protected String campoConsultaPessoa;
    protected String valorConsultaPessoa;
//    protected List listaConsultaPessoa;
    protected String campoConsultaProduto;
    protected String valorConsultaProduto;
//    protected List listaConsultaProduto;
    protected String acaoAjaxGravarFinalVigencia;
    private Boolean modalEditarVigenciaFinalProdutoCliente = false;
    private Date dtInicio;
    private Date dtFim;
    private String filtroData;

    public MovProdutoControle() throws Exception {
        setDtInicio(Calendario.primeiroDiaMes());
        setDtFim(Calendario.ultimoDiaMes());
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        setSucesso(false);
        setErro(false);
    }

    public void inicializarUsuarioLogado() {
        try {

            movProdutoVO.setUsuarioVO(getUsuarioLogado());
        } catch (Exception exception) {
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>MovProduto</code>
     * para edi??o pelo usu?rio da aplica??o.
     */
    public String novo() {
        setMovProdutoVO(new MovProdutoVO());
        getMovProdutoVO().setQuantidade(1);
        // setListaConsultaPessoa(new ArrayList());
        //  setListaSelectItemPessoa(new ArrayList());
        setCampoConsultaPessoa(new String());
        setValorConsultaPessoa(new String());
        //   inicializarListasSelectItemTodosComboBox();
        inicializarUsuarioLogado();
        getMovProdutoVO().setResponsavelLancamento(getMovProdutoVO().getUsuarioVO());
        setMovProdutoParcelaVO(new MovProdutoParcelaVO());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>MovProduto</code> para altera??o.
     * O objeto desta classe ? disponibilizado na session da p?gina (request) para que o JSP correspondente possa disponibiliz?-lo para edi??o.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            MovProdutoVO obj = getFacade().getMovProduto().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setMovProdutoParcelaVO(new MovProdutoParcelaVO());
            setMovProdutoVO(new MovProdutoVO());
            setMovProdutoVO(obj);
            //getMovProdutoVO().setResponsavelLancamento(obj.getContrato().getResponsavelContrato());
            montarListaSelectItemEmpresa();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>MovProdutoVO</code>.
     * Esta inicialização ? necessária por exigência da tecnologia JSF, que n?o trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(MovProdutoVO obj) {
        if (obj.getProduto() == null) {
            obj.setProduto(new ProdutoVO());
        }
        if (obj.getContrato() == null) {
            obj.setContrato(new ContratoVO());
        }
        if (obj.getPessoa() == null) {
            obj.setPessoa(new PessoaVO());
        }
        if (obj.getEmpresa() == null) {
            obj.setEmpresa(new EmpresaVO());
        }
//        if (obj.getResponsavelLancamento() == null) {
//            obj.setResponsavelLancamento(new ColaboradorVO());
//        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>MovProduto</code>.
     * Caso o objeto seja novo (ainda n?o gravado no BD) ? acionado a opera??o <code>incluir()</code>. Caso contr?rio ? acionado o <code>alterar()</code>.
     * Se houver alguma inconsist?ncia o objeto n?o ? gravado, sendo re-apresentado para o usu?rio juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (movProdutoVO.isNovoObj().booleanValue()) {
                getFacade().getMovProduto().incluir(movProdutoVO);
            } else {
                getFacade().getMovProduto().alterar(movProdutoVO);
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void gravarEdicaoDataFinalVigencia() {
        try {
            MovProdutoVO.validarDataFinalVigencia(movProdutoVO);
            gravar();
            ((ClienteControle) getControlador(ClienteControle.class.getSimpleName())).consultarClienteMensagem();
            fecharModalEditarVigenciaFinalProduto();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public Object fecharModalEditarVigenciaFinalProduto() {
        setModalEditarVigenciaFinalProdutoCliente(false);
        return "";
    }

    public Integer obterEmpresaLogadoSistema() throws Exception {
        EmpresaVO emp = getEmpresaLogado();
        if (emp == null || emp.getCodigo() == 0) {
            return (0);
        } else {
            return emp.getCodigo();
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP MovProdutoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            Integer empresa = obterEmpresaLogadoSistema();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovProduto().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS, empresa);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoProduto")) {
                objs = getFacade().getMovProduto().consultarPorDescricaoProduto(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS, empresa);
            }
            if (getControleConsulta().getCampoConsulta().equals("codigoContrato")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovProduto().consultarPorCodigoContrato(new Integer(valorInt), Uteis.NIVELMONTARDADOS_TODOS, empresa);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                objs = getFacade().getMovProduto().consultarPorNomePessoa(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, empresa);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeEmpresa")) {
                objs = getFacade().getMovProduto().consultarPorNomeEmpresa(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getMovProduto().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS, empresa);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoColaborador")) {
                objs = getFacade().getMovProduto().consultarPorSituacaoColaborador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS, empresa);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    public void consultarPessoa() {
        try {
            super.consultar();
            List objs = new ArrayList();

            if (getCampoConsultaPessoa().equals("codigo")) {
                if (getValorConsultaPessoa().equals("")) {
                    setValorConsultaPessoa("0");
                }

                int valorInt = Integer.parseInt(getValorConsultaPessoa());
                objs = getFacade().getPessoa().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (getCampoConsultaPessoa().equals("nomePessoa")) {
                objs = getFacade().getPessoa().consultarPorNome(getValorConsultaPessoa(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            if (getCampoConsultaPessoa().equals("empresa")) {
                objs = getFacade().getPessoa().consultarPorNomeEmpresa(getValorConsultaPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            if (getCampoConsultaPessoa().equals("cpf")) {
                objs = getFacade().getPessoa().consultarPorCfp(getValorConsultaPessoa(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (getCampoConsultaPessoa().equals("descricaoProfissao")) {
                objs = getFacade().getPessoa().consultarPorDescricaoProfissao(getValorConsultaPessoa(), Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            if (getCampoConsultaPessoa().equals("matricula")) {
                objs = getFacade().getPessoa().consultarPorMatricula(getValorConsultaPessoa(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
            }

            //       setListaSelectItemPessoa(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            //        setListaSelectItemPessoa(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void selecionarPessoa() throws Exception {
        PessoaVO obj = (PessoaVO) context().getExternalContext().getRequestMap().get("pessoa");
        getMovProdutoVO().getPessoa().setCodigo(obj.getCodigo().intValue());
        getMovProdutoVO().getPessoa().setNome(obj.getNome());
        //   setListaSelectItemPessoa(new ArrayList());
        obj = null;
        setValorConsultaPessoa("");
        setCampoConsultaPessoa("");
        montarListaSelectItemContrato();
    }

    public void consultarProduto() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getCampoConsultaProduto().equals("codigo")) {
                if (getValorConsultaProduto().equals("")) {
                    setValorConsultaProduto("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaProduto());
                objs = getFacade().getProduto().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultaProduto().equals("descricaoCategoriaProduto")) {
                objs = getFacade().getProduto().consultarPorDescricaoCategoriaProduto(getValorConsultaProduto(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultaProduto().equals("descricao")) {
                objs = getFacade().getProduto().consultarPorDescricao(getValorConsultaProduto(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getCampoConsultaProduto().equals("tipoVigencia")) {
                objs = getFacade().getProduto().consultarPorTipoVigencia(getValorConsultaProduto(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            //    setListaConsultaProduto(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            //    setListaConsultaProduto(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        getMovProdutoVO().getProduto().setCodigo(obj.getCodigo().intValue());
        getMovProdutoVO().getProduto().setDescricao(obj.getDescricao());
        getMovProdutoVO().setPrecoUnitario(obj.getValorFinal());
        getMovProdutoVO().setDataInicioVigencia(obj.getDataInicioVigencia());
        getMovProdutoVO().setDataFinalVigencia(obj.getDataFinalVigencia());
        // setListaSelectItemProduto(new ArrayList());
        obj = null;
        setValorConsultaProduto("");
        setCampoConsultaProduto("");
        calculaTotalFinal();
    }

    /**
     * Opera??o responsável por processar a exclus?o um objeto da classe <code>MovProdutoVO</code>
     * Ap?s a exclus?o ela automaticamente aciona a rotina para uma nova inclus?o.
     */
    public String excluir() {
        try {
            getFacade().getMovProduto().excluir(movProdutoVO);
            setMovProdutoVO(new MovProdutoVO());

            setMovProdutoParcelaVO(new MovProdutoParcelaVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>MovProdutoParcela</code>
     * para o objeto <code>movProdutoVO</code> da classe <code>MovProduto</code>
     */
//    public String adicionarMovProdutoParcela() throws Exception {
//        try {
//            if (!getMovProdutoVO().getCodigo().equals(new Integer(0))) {
//                movProdutoParcelaVO.setMovProduto(getMovProdutoVO().getCodigo());
//            }
//            if (getMovProdutoParcelaVO().getMovProduto().getCodigo().intValue() != 0){
//                Integer campoConsulta = getMovProdutoParcelaVO().getMovProduto().getCodigo();
//                MovProdutoVO movProduto = movProdutoFacade.consultarPorChavePrimaria(campoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
//                getMovProdutoParcelaVO().setMovProduto(movProduto);
//            }
//            if (getMovProdutoParcelaVO().getMovParcela().getCodigo().intValue() != 0){
//                Integer campoMovParcela = getMovProdutoParcelaVO().getMovParcela().getCodigo();
//                MovParcelaVO movParcela = movParcelaFacade.consultarPorChavePrimaria(campoMovParcela, Uteis.NIVELMONTARDADOS_TODOS);
//                getMovProdutoParcelaVO().setMovParcela(movParcela);
//            }
//            getMovProdutoVO().adicionarObjMovProdutoParcelaVOs( getMovProdutoParcelaVO());
//            this.setMovProdutoParcelaVO(new MovProdutoParcelaVO());
//            setMensagemID("msg_dados_adicionados");
//            return "editar";
//        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            return "editar";
//        }
//    }
//
//    /* Método responsável por disponibilizar dados de um objeto da classe <code>MovProdutoParcela</code>
//     * para edi??o pelo usu?rio.
//     */
//    public String editarMovProdutoParcela() throws Exception {
//        MovProdutoParcelaVO obj = (MovProdutoParcelaVO)context().getExternalContext().getRequestMap().get("movProdutoParcela");
//        setMovProdutoParcelaVO(obj);
//        return "editar";
//    }
//
//    /* Método responsável por remover um novo objeto da classe <code>MovProdutoParcela</code>
//     * do objeto <code>movProdutoVO</code> da classe <code>MovProduto</code>
//     */
//    public String removerMovProdutoParcela() throws Exception {
//        MovProdutoParcelaVO obj = (MovProdutoParcelaVO)context().getExternalContext().getRequestMap().get("movProdutoParcela");
//        getMovProdutoVO().excluirObjMovProdutoParcelaVOs(obj.getMovProduto().getCodigo());
//        setMensagemID("msg_dados_excluidos");
//        return "editar";
//    }
    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>MovProduto</code>.
     */
    public void montarListaSelectItemMovProduto(String prm) throws Exception {
        List resultadoConsulta = consultarMovProdutoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            MovProdutoVO obj = (MovProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        // setListaSelectItemMovProduto(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>MovProduto</code>.
     * Buscando todos os objetos correspondentes a entidade <code>MovProduto</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemMovProduto() {
        try {
            montarListaSelectItemMovProduto("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarMovProdutoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getMovProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS, obterEmpresaLogadoSistema());
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>MovParcela</code>.
     */
    public void montarListaSelectItemMovParcela(Integer prm) throws Exception {
        List resultadoConsulta = consultarMovParcelaPorCodigo(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            MovParcelaVO obj = (MovParcelaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo().toString()));
        }
        //  setListaSelectItemMovParcela(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>MovParcela</code>.
     * Buscando todos os objetos correspondentes a entidade <code>MovParcela</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemMovParcela() {
        try {
            montarListaSelectItemMovParcela(new Integer(0));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>codigo</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarMovParcelaPorCodigo(Integer codigoPrm) throws Exception {
        List lista = getFacade().getMovParcela().consultarPorCodigo(codigoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>ResponsavelLancamento</code>.
     */
    public void montarListaSelectItemResponsavelLancamento(String prm) throws Exception {
//        List resultadoConsulta = consultarColaboradorPorSituacao(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            ColaboradorVO obj = (ColaboradorVO)i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
//        }
//        setListaSelectItemResponsavelLancamento(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>ResponsavelLancamento</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Colaborador</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemResponsavelLancamento() {
        try {
            montarListaSelectItemResponsavelLancamento("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>situacao</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
//    public List consultarColaboradorPorSituacao(String situacaoPrm) throws Exception {
//        List lista = colaboradorFacade.consultarPorSituacao(situacaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
//        return lista;
//    }
    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Empresa</code>.
     */
    public void montarListaSelectItemEmpresa(String prm) throws Exception {
        Integer empresa = 0;
        if (!getEmpresaLogado().getCodigo().equals(new Integer(0)) && (!getEmpresaLogado().getCodigo().equals(null))) {
            empresa = getEmpresaLogado().getCodigo().intValue();
        }
        List resultadoConsulta = consultarEmpresaPorNome(prm, empresa);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemEmpresa(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Empresa</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Empresa</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarEmpresaPorNome(String nomePrm, Integer empresa) throws Exception {
        List lista = getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Pessoa</code>.
     */
    public void montarListaSelectItemPessoa(String prm) throws Exception {
        List resultadoConsulta = consultarPessoaPorNome(prm);
//        Iterator i = resultadoConsulta.iterator();
//        List objs = new ArrayList();
//        //objs.add(new SelectItem(new Integer(0), ""));
//        while (i.hasNext()) {
//            PessoaVO obj = (PessoaVO) i.next();
//            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
//        }
        //  setListaSelectItemPessoa(resultadoConsulta);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Pessoa</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Pessoa</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemPessoa() {
        try {
            montarListaSelectItemPessoa("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarPessoaPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getPessoa().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Contrato</code>.
     */
    public void montarListaSelectItemContrato(Integer prm) throws Exception {
        List resultadoConsulta = consultarContratoPorCodigo(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ContratoVO obj = (ContratoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getCodigo().toString()));
        }
        // setListaSelectItemContrato(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Contrato</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Contrato</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemContrato() {
        try {
            montarListaSelectItemContrato(getMovProdutoVO().getPessoa().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>codigo</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarContratoPorCodigo(Integer codigoPrm) throws Exception {
        List lista = getFacade().getContrato().consultarPorCodigo(codigoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Produto</code>.
     */
    public void montarListaSelectItemProduto(String prm) throws Exception {
        List resultadoConsulta = consultarProdutoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
        //     setListaSelectItemProduto(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Produto</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Produto</code>. Esta rotina n?o recebe par?metros para filtragem de dados, isto ?
     * importante para a inicialização dos dados da tela para o acionamento por meio requisi??es Ajax.
     */
    public void montarListaSelectItemProduto() {
        try {
            montarListaSelectItemProduto("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>descricao</code>
     * Este atributo ? uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarProdutoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getProduto().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemProduto();
        montarListaSelectItemContrato();
        montarListaSelectItemPessoa();
        montarListaSelectItemEmpresa();
        montarListaSelectItemResponsavelLancamento();
        montarListaSelectItemMovParcela();
        montarListaSelectItemMovProduto();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome Pessoa"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricaoProduto", "Nome Produto"));
        itens.add(new SelectItem("codigoContrato", "Numero Contrato"));
        itens.add(new SelectItem("nomeEmpresa", "Nome Empresa"));
        itens.add(new SelectItem("descricao", "Descrição"));
        //itens.add(new SelectItem("situacaoColaborador", "responsável Lan?amento"));
        return itens;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaComboPessoa() throws Exception {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("cfp", "CPF"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        itens.add(new SelectItem("matricula", "Matrícula"));
        if (getUsuarioLogado().getAdministrador().equals(true)) {
            itens.add(new SelectItem("empresa", "Empresa"));
        }
        //     itens.add(new SelectItem("codAcesso", "C?digo de Acesso"));
        return itens;
    }

    public List getTipoConsultaComboProduto() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricaoCategoriaProduto", "Categoria de Produto"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("tipoVigencia", "Tipo de Vigência"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a pagina??o entre as p?ginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Opera??o que inicializa as Interfaces Fa?ades com os respectivos objetos de
     * persist?ncia dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public void calculaTotalFinal() {
        getMovProdutoVO().setTotalFinal((getMovProdutoVO().getPrecoUnitario() * getMovProdutoVO().getQuantidade()) - getMovProdutoVO().getValorDesconto());

    }

//    public List getListaSelectItemMovProduto() {
//        return (listaSelectItemMovProduto);
//    }
//
//    public void setListaSelectItemMovProduto(List listaSelectItemMovProduto) {
//        this.listaSelectItemMovProduto = listaSelectItemMovProduto;
//    }
//
//    public List getListaSelectItemMovParcela() {
//        return (listaSelectItemMovParcela);
//    }
//
//    public void setListaSelectItemMovParcela(List listaSelectItemMovParcela) {
//        this.listaSelectItemMovParcela = listaSelectItemMovParcela;
//    }
    public MovProdutoParcelaVO getMovProdutoParcelaVO() {
        return movProdutoParcelaVO;
    }

    public void setMovProdutoParcelaVO(MovProdutoParcelaVO movProdutoParcelaVO) {
        this.movProdutoParcelaVO = movProdutoParcelaVO;
    }

//    public List getListaSelectItemResponsavelLancamento() {
//        return (listaSelectItemResponsavelLancamento);
//    }
//
//    public void setListaSelectItemResponsavelLancamento(List listaSelectItemResponsavelLancamento) {
//        this.listaSelectItemResponsavelLancamento = listaSelectItemResponsavelLancamento;
//    }
    public List getListaSelectItemEmpresa() {
        return (listaSelectItemEmpresa);
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

//    public List getListaSelectItemPessoa() {
//        return (listaSelectItemPessoa);
//    }
//
//    public void setListaSelectItemPessoa(List listaSelectItemPessoa) {
//        this.listaSelectItemPessoa = listaSelectItemPessoa;
//    }
//
//    public List getListaSelectItemContrato() {
//        return (listaSelectItemContrato);
//    }
//
//    public void setListaSelectItemContrato(List listaSelectItemContrato) {
//        this.listaSelectItemContrato = listaSelectItemContrato;
//    }
//
//    public List getListaSelectItemProduto() {
//        return (listaSelectItemProduto);
//    }
//
//    public void setListaSelectItemProduto(List listaSelectItemProduto) {
//        this.listaSelectItemProduto = listaSelectItemProduto;
//    }
    public MovProdutoVO getMovProdutoVO() {
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public String getCampoConsultaPessoa() {
        return campoConsultaPessoa;
    }

    public void setCampoConsultaPessoa(String campoConsultaPessoa) {
        this.campoConsultaPessoa = campoConsultaPessoa;
    }

//    public List getListaConsultaPessoa() {
//        return listaConsultaPessoa;
//    }
//
//    public void setListaConsultaPessoa(List listaConsultaPessoa) {
//        this.listaConsultaPessoa = listaConsultaPessoa;
//    }
    public String getValorConsultaPessoa() {
        return valorConsultaPessoa;
    }

    public void setValorConsultaPessoa(String valorConsultaPessoa) {
        this.valorConsultaPessoa = valorConsultaPessoa;
    }

    public String getCampoConsultaProduto() {
        return campoConsultaProduto;
    }

    public void setCampoConsultaProduto(String campoConsultaProduto) {
        this.campoConsultaProduto = campoConsultaProduto;
    }

//    public List getListaConsultaProduto() {
//        return listaConsultaProduto;
//    }
//
//    public void setListaConsultaProduto(List listaConsultaProduto) {
//        this.listaConsultaProduto = listaConsultaProduto;
//    }
    public String getValorConsultaProduto() {
        return valorConsultaProduto;
    }

    public void setValorConsultaProduto(String valorConsultaProduto) {
        this.valorConsultaProduto = valorConsultaProduto;
    }

    /**
     * Metodo listener responsavel por setar na lista da view o resultado
     * de uma consulta paginada em banco de acordo com os filtros passados
     *
     * Autor: Carla Pereira
     * Criado em 25/02/2011
     */
    @SuppressWarnings("unchecked")
    public void consultarPaginadoListener(ActionEvent evt) {
        try {
            //Obtendo qual pagina dever? ser exibida
            Object component = evt.getComponent().getAttributes().get("pagNavegacao");
            if (component != null && !"".equals(component.toString())) {
                getConfPaginacao().setPagNavegacao(component.toString());
            }

            Object compPaginaInicial = evt.getComponent().getAttributes().get("paginaInicial");
            if (compPaginaInicial != null && !"".equals(compPaginaInicial.toString())) {
                if (compPaginaInicial.toString().equals("paginaInicial")) {
                    setConfPaginacao(new ConfPaginacao());
                }
            }

            super.consultar();
            List objs = new ArrayList();

            // Foi encapsulado o Método de consulta em um ?nico, que suportara todos os filtros utilizados atualmente
            objs = getFacade().getMovProduto().consultarPaginado(getControleConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, getConfPaginacao());

            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarMovProdutoListener(ActionEvent evt) {
        movProdutoVO = (MovProdutoVO) evt.getComponent().getAttributes().get("objMovProdutoEdicao");
        setMensagemDetalhada("", "");
        setMensagem("");
        setErro(false);
    }

    public String getAcaoAjaxGravarFinalVigencia() {
        if (getSucesso()) {
            return "Richfaces.hideModalPanel('modalEditarVigenciaFinalProdutoCliente');";
        }
        return "";
    }
     public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        java.sql.Date dataInicial = null;
        try{ dataInicial = new java.sql.Date(this.dtInicio.getTime());}catch (Exception e){ dataInicial = null; }
        java.sql.Date dataFinal = null;
        try{ dataFinal = new java.sql.Date(this.dtFim.getTime());}catch (Exception e){ dataFinal = null; }
        List listaParaImpressao = getFacade().getMovProduto().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), dataInicial, dataFinal, this.filtroData);
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void confirmarAlteracaoDataValidade() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        setModalEditarVigenciaFinalProdutoCliente(false);
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setMsgAlert("Richfaces.hideModalPanel('panelAutorizacaoFuncionalidade');");
                setModalEditarVigenciaFinalProdutoCliente(true);
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Alterar Vigência de produto - Autorizar", "AlterarDataVigenciaValidade_Autorizar",
                "Alterar Data de Vigência de produto com validade",
                "form:panelRecarregar, modalEditarVigenciaFinalProdutoCliente", listener);
    }
    public String getOnComplete() {
        return getMsgAlert();
    }

    public Boolean getModalEditarVigenciaFinalProdutoCliente() {
        return modalEditarVigenciaFinalProdutoCliente;
    }

    public void setModalEditarVigenciaFinalProdutoCliente(Boolean modalEditarVigenciaFinalProdutoCliente) {
        this.modalEditarVigenciaFinalProdutoCliente = modalEditarVigenciaFinalProdutoCliente;
    }

    public Date getDtInicio() {
        return dtInicio;
    }

    public void setDtInicio(Date dtInicio) {
        this.dtInicio = dtInicio;
    }

    public Date getDtFim() {
        return dtFim;
    }

    public void setDtFim(Date dtFim) {
        this.dtFim = dtFim;
    }

    public String getFiltroData() {
        return filtroData;
    }

    public void setFiltroData(String filtroData) {
        this.filtroData = filtroData;
    }
}

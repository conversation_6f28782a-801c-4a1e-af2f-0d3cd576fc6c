package controle.contrato;
import java.util.Date;
import negocio.facade.jdbc.contrato.Convenio;
import negocio.comuns.utilitarias.*;
import negocio.comuns.contrato.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * convenioForm.jsp convenioCons.jsp) com as funcionalidades da classe <code>Convenio</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Convenio
 * @see ConvenioVO
*/
public class ConvenioControle extends SuperControle {
    private ConvenioVO convenioVO;
    /**
    * Interface <code>ConvenioInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */

    public ConvenioControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>Convenio</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setConvenioVO(new ConvenioVO());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Convenio</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        ConvenioVO obj = (ConvenioVO)context().getExternalContext().getRequestMap().get("convenio");
        obj.setNovoObj(new Boolean(false));
        setConvenioVO(obj);
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Convenio</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (convenioVO.isNovoObj().booleanValue()) {
                getFacade().getConvenio().incluir(convenioVO);
            } else {
                getFacade().getConvenio().alterar(convenioVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP ConvenioCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenio().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getConvenio().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataAssinatura")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenio().consultarPorDataAssinatura(Uteis.getDateTime(valorData,0,0,0), Uteis.getDateTime(valorData,23,59,59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ConvenioVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getConvenio().excluir(convenioVO);
            setConvenioVO( new ConvenioVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("dataAssinatura", "Data de Assinatura"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ConvenioVO getConvenioVO() {
        return convenioVO;
    }
     
    public void setConvenioVO(ConvenioVO convenioVO) {
        this.convenioVO = convenioVO;
    }
}
package controle.contrato;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import kong.unirest.Client;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas contratoOperacaoForm.jsp contratoOperacaoCons.jsp) com as
 * funcionalidades da classe
 * <code>ContratoOperacao</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see ContratoOperacao
 * @see ContratoOperacaoVO
 */
public class TransferirDireitoUsoContratoControle extends SuperControle {

    private ContratoVO contratoVO;
    private ClienteVO clienteVO;
    private ClienteVO clienteDestinoVO;

    public TransferirDireitoUsoContratoControle() {

    }

    public void novoTransferirDireitoUso() {
        try {
            setMensagemDetalhada("");
            setContratoVO(new ContratoVO());
            setClienteVO(new ClienteVO());
            setErro(false);
            setSucesso(true);

            prepararDados();

            setMsgAlert("Richfaces.showModalPanel('pnlTransferirDireitoUsoContrato');");

            setMensagem("");
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void novoRecuperarDireitoUso() {
        try {
            setMensagemDetalhada("");
            setContratoVO(new ContratoVO());
            setClienteVO(new ClienteVO());
            setErro(false);
            setSucesso(true);

            prepararDados();

            setMsgAlert("Richfaces.showModalPanel('pnlRecuperarDireitoUsoContrato');");

            setMensagem("");
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<ClienteVO> consultarClientes(Object suggest) {
        String nomeOuMatricula = (String) suggest;
        List<ClienteVO> clientes = new ArrayList<>();
        try {
            clientes = getFacade().getCliente().consultarPorNomePessoaSituacaoDiferente(nomeOuMatricula, "AT", getClienteVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, 50);
        } catch (Exception e) {
            Uteis.logar(e, TransferirDireitoUsoContratoControle.class);
        }

        return clientes;
    }

    public void selecionarClienteDestino() {
        ClienteVO cliente = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        setClienteDestinoVO(cliente);
    }

    public void prepararDados() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            setErro(false);

            clienteControle.pegarClienteTelaCliente();

            setClienteVO(clienteControle.getClienteVO());
            setContratoVO(clienteControle.getContratoVO());
        }
    }

    public void transferirDireitoDeUsoContrato() {
        try {
            limparMsg();

            getFacade().getZWFacade().transferirDireitoDeUsoContrato(getClienteVO(), getClienteDestinoVO(), getContratoVO(), getUsuarioLogado(), getKey());

            montarSucessoGrowl("Transferência realizada com sucesso.");
        } catch (Exception ex) {
            montarErro(ex);
            ex.printStackTrace();
        }
    }

    public void recuperarDireitoDeUsoContrato() {
        try {
            limparMsg();

            ClienteVO clienteAtual = getFacade().getCliente().consultarPorCodigoPessoa(getContratoVO().getPessoa().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS);

            ClienteVO clienteOriginal = getFacade().getCliente().consultarPorCodigoPessoa(getContratoVO().getPessoaOriginal().getCodigo(), 0, Uteis.NIVELMONTARDADOS_MINIMOS);
            setClienteDestinoVO(clienteOriginal);

            getFacade().getZWFacade().recuperarDireitoDeUsoContrato(clienteAtual, getClienteDestinoVO(), getContratoVO(), getUsuarioLogado(), getKey());

            montarSucessoGrowl("Recuperação dos direitos de uso realizada com sucesso.");
        } catch (Exception ex) {
            montarErro(ex);
            ex.printStackTrace();
        }
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ClienteVO getClienteDestinoVO() {
        return clienteDestinoVO;
    }

    public void setClienteDestinoVO(ClienteVO clienteDestinoVO) {
        this.clienteDestinoVO = clienteDestinoVO;
    }

}

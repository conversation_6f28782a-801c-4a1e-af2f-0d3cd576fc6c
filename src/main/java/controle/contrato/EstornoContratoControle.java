package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.financeiro.CaixaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoEmpresaBitrixVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.oamd.OAMDService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.impl.IntegracaoLeadGenericaServiceImpl;

import javax.faces.model.SelectItem;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas contratoOperacaoForm.jsp contratoOperacaoCons.jsp) com as
 * funcionalidades da classe
 * <code>ContratoOperacao</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see ContratoOperacao
 * @see ContratoOperacaoVO
 */
public class EstornoContratoControle extends SuperControle {

    protected ContratoVO contratoVO;
    protected ClienteVO clienteVO;
    protected Boolean abrirRichConfirmacaoEstorno = false;
    protected Boolean apresentarBotaoEstorno = false;
    protected Boolean exiteOutroContratoPagouMinhaParcela = false;
    protected List listaPessoaVOs;
    private boolean existeTransacoesJaProcessadas = false;
    private boolean existeTransacoesQuePodemSerCanceladas = false;
    private List<TransacaoVO> listaTransacoes = new ArrayList<>();
    private List<RemessaItemVO> listaItensRemessa = new ArrayList<>();
    private Boolean temChequeComLote = Boolean.FALSE;
    private String justificativaEstorno = "";
    private CaixaVO caixaAberto = null;
    private String estornarTransacoes = "";

    private boolean permissaoEstronarTransacao;
    private boolean permiteEstornarContratoSemPermissao = false;

    public EstornoContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
    }

    public void inicializarUsuarioLogado() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
            getContratoVO().setUsuarioVO(new UsuarioVO());
            getContratoVO().getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
            getContratoVO().getUsuarioVO().setUsername(getUsuarioLogado().getUsername());
            getContratoVO().getUsuarioVO().setNome(getUsuarioLogado().getNome());
            getContratoVO().getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
            getContratoVO().getUsuarioVO().setUserOamd(getUsuarioLogado().getUserOamd());
        } else {
            throw new Exception("Não ha usuario logado.");
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe
     * <code>ContratoOperacao</code> para edição pelo usuário da aplicação.
     */
    public void novo() {
        notificarRecursoEmpresa(RecursoSistema.ESTORNO_CONTRATO_CLIENTE);
        try {
            setMensagemDetalhada("");
            temChequeComLote = Boolean.FALSE;
            setContratoVO(new ContratoVO());
            setClienteVO(new ClienteVO());
            setErro(false);
            setSucesso(true);
            setExiteOutroContratoPagouMinhaParcela(false);
            setAbrirRichConfirmacaoEstorno(true);
            setEstornarTransacoes("");
            setListaPessoaVOs(new ArrayList());
            montarClienteEContratoEstorno();
            setPermiteEstornarContratoSemPermissao(validarEstornarContratoSemPermissao());
            setApresentarBotaoEstorno(permissao("EstornoContrato_Autorizar") || isPermiteEstornarContratoSemPermissao());
            inicializarUsuarioLogado();
            setMensagem("");
            validaPermissaoVisuaalizarBotao();
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            montarErro(e);
        }

    }

    private boolean validarEstornarContratoSemPermissao() throws Exception {
        try {
            // Se possui alguma permissão de estorno, não valida a configuração do sistema.
            if (!permissao("EstornoContrato")) {
                if (getFacade().getConfiguracaoSistema().isPermiteEstornarContrato30MinAposLancamento()) {
                    if (getContratoVO().getDataAlteracaoManual() == null) {
                        if (getUsuarioLogado().getCodigo() == getContratoVO().getResponsavelContrato().getCodigo()) {
                            if (Calendario.diferencaEmMinutos(getContratoVO().getDataLancamento(), Calendario.hoje()) < 30) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private void validaPermissaoVisuaalizarBotao() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle.validarPermisaoUsuario("GestaoTransacoes", "4.20 - Gestão Transações")) {
            setPermissaoEstronarTransacao(true);
        } else {
            setPermissaoEstronarTransacao(false);
        }
    }

    public void montarClienteEContratoEstorno() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            clienteControle.setUrlPopup("");
            clienteControle.pegarClienteTelaCliente();
            setErro(false);
            setClienteVO(clienteControle.getClienteVO());
            setContratoVO(clienteControle.getContratoVO());
            validarSeExisteReciboContrato();

            List<MovParcelaVO> parcelasEmRemessas = new ArrayList<>();
            for (MovParcelaVO parcelaVO : getContratoComParcelasEnvioDeRemessa()) {
                RemessaItemVO itemRemessa = getFacade().getRemessaItem().consultarPorParcelaPorCodigoRemessa(parcelaVO.getCodigo(), parcelaVO.getRemessa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!itemRemessa.getProps().containsKey(DCCAttEnum.StatusVenda.name())) {
                    parcelasEmRemessas.add(parcelaVO);
                }
            }
            clienteControle.setListaParcelasRemessa(parcelasEmRemessas);

            if (clienteControle.getListaParcelasRemessa().isEmpty()) {
                clienteControle.setUrlPopup("popup('estornoContratoForm.jsp', 'EstornoContratoControle', 830, 690);");
            } else {
                clienteControle.setUrlPopup("Richfaces.showModalPanel('panelParcelasRemessas')");

            }
        }
    }

    public void validarSeExisteReciboContrato() throws Exception {
        getFacade().getContrato().montarListasParaEstorno(contratoVO);

        ConfiguracaoFinanceiroVO confFinan = getFacade().getConfiguracaoFinanceiro().consultar();
        for (EstornoReciboVO estornoReciboVO : contratoVO.getListaEstornoRecibo()) {
            if (confFinan.getUsarMovimentacaoContas()) {
                for (MovPagamentoVO movPag : estornoReciboVO.getListaMovPagamento()) {
                    if (!movPag.getCredito()) {
                        if (getFacade().getCheque().verificarContemLote(movPag.getCodigo())
                                || getFacade().getCartaoCredito().verificarContemLote(movPag.getCodigo())) {
                            temChequeComLote = Boolean.TRUE;
                            break;
                        }
                    }
                }
            }
            for (MovParcelaVO parcela : estornoReciboVO.getListaMovParcela()) {
                if (!parcela.getContrato().getCodigo().equals(contratoVO.getCodigo())) {
                    setExiteOutroContratoPagouMinhaParcela(true);
                    adicionarObjContratoVO(parcela.getContrato());
                }
            }
        }

        contratoVO.montarListaItensRemessa(getFacade().getMovParcela().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS), getFacade().getZWFacade().getCon());

        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", contratoVO.getListaEstornoTransacoes());
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.consultarParaExportar", false);
        JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacao.count", contratoVO.getListaEstornoTransacoes().size());
        JSFUtilities.setManagedBeanValue("GestaoRemessasControle.itensRemessaEstorno", contratoVO.getListaItensRemessa());
        setListaTransacoes(contratoVO.getListaEstornoTransacoes());
        contratoVO.setListaEstornoTransacoes(getListaTransacoes());
    }

    public void estornoContrato() {
        try {
            limparMsg();
            setMensagemDetalhada("", "");

            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            this.contratoVO = clienteControle.getContratoVO();
            this.clienteVO = clienteControle.getClienteVO();

            if(!UteisValidacao.emptyList(getContratoVO().getListaEstornoRecibo())){
                try {
                    permissaoFuncionalidade(getContratoVO().getUsuarioVO(), "EstornoRecibo", "4.12 - Estornar recibo");
                } catch (Exception e){
                    throw new ConsistirException("Contrato tem parcela(s) paga(s). Usuário precisa ter a permissão \"4.12 - Estornar recibo\" para realizar o estorno desse contrato");
                }
            }

            if (!UteisValidacao.emptyList(getListaTransacoes()) && UteisValidacao.emptyString(getEstornarTransacoes())) {
                throw new Exception("Deve ser selecionado se deseja estornar as transações");
            }
            getContratoVO().setPrecisaEstornarTransacoes(getEstornarTransacoes().equalsIgnoreCase("SIM"));

            for (RemessaItemVO remessaItemVO : getContratoVO().getListaItensRemessa()) {
                if (remessaItemVO.getCodigoStatus().equals("00") && !remessaItemVO.isItemCancelado()) {
                    throw new Exception("Não é possível realizar o estorno, pois existem parcelas pagas por uma remessa. Por favor, cancele o contrato.");
                }
                if (remessaItemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.GERADA) ||
                        remessaItemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA)) {

                    boolean exception = true;
                    if (remessaItemVO.getRemessa().getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO)) {
                        exception = false;
                        if (remessaItemVO.getMovPagamento() != null &&
                                UteisValidacao.emptyNumber(remessaItemVO.getMovPagamento().getCodigo())) {
                            for (RemessaItemMovParcelaVO itemMovParcelaVO : remessaItemVO.getMovParcelas()) {
                                if (itemMovParcelaVO.getMovParcelaVO() != null &&
                                        itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                                    exception = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (exception) {
                        throw new Exception("Não é possível realizar o estorno, pois existe(m) remessa(s) aguardando retorno");
                    }
                }
            }
            if (contratoVO.isPossuiRemessaBoleto()) {

                for (RemessaItemVO remessaItemVO : getContratoVO().getListaItensRemessa()) {
                    if (remessaItemVO.getMovParcela().getSituacao().equals("PG") &&
                            !remessaItemVO.getAutorizacao().isEmpty() &&
                            !remessaItemVO.getAutorizacao().startsWith("00")) {
                        throw new Exception("Já existe um boleto pago.");
                    }
                }
                RemessaVO remessa = getContratoVO().getListaItensRemessa().isEmpty() ? getContratoVO().getListaItensRemessa().get(0).getRemessa() : new RemessaVO();
                if (!UteisValidacao.emptyNumber(remessa.getCodigo())) {
                    if (remessa.getSituacaoRemessa().equals(SituacaoRemessaEnum.GERADA) ||
                            remessa.getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA)) {
                        throw new Exception("A remessa se encontra gerada e aguardando retorno.");
                    }
                }
            }

            getFacade().getContrato().estornoContrato(getContratoVO(), getClienteVO(), getCaixaAberto(), getJustificativaEstorno());
            estornarPontosContrato();
            if (!UteisValidacao.emptyString(getContratoVO().getNumeroCupomDesconto())) {
                OAMDService oamdService = new OAMDService();
                oamdService.cancelarUtilizacaoCupomDesconto(getContratoVO().getNumeroCupomDesconto(), getKey());
                oamdService.informarContratoEstornadoHistoricoUtilizacaoCupom(getContratoVO().getCodigo(), getKey());
                oamdService = null;
            }

            // altera a situação do cliente
            processarCliente();

            atualizarSinteticoContratoAntigoQueFoiRenovado(getContratoVO());

            setMensagemID("msg_EstornoSucesso");
            if (!getContratoVO().getListaEstornoTransacoes().isEmpty() || !getContratoVO().getListaItensRemessa().isEmpty()) {
                List<TransacaoVO> transacoes = getContratoVO().getListaEstornoTransacoes();
                String msgTransacoesEstornarCielo = "";
                for (TransacaoVO transacaoVO : transacoes) {
                    if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)
                            && !transacaoVO.getValorAtributoResposta(APF.CodigoAutorizacao).isEmpty()) {
                        msgTransacoesEstornarCielo += transacaoVO.getCodigoExterno() + ", ";
                    }
                }
                List<RemessaItemVO> itens = getContratoVO().getListaItensRemessa();
                for (RemessaItemVO item : itens) {
                    if (!item.getAutorizacao().equals("000000")) {
                        msgTransacoesEstornarCielo += item.getAutorizacao() + ", ";
                    }
                }
                if (!msgTransacoesEstornarCielo.isEmpty()) {
                    setMensagemDetalhada(String.format("Atenção! "
                                    + "O Estorno foi concluído no ZillyonWeb, "
                                    + "mas as transações/autorizações %s precisam "
                                    + "ser Estornadas junto à Administradora.",
                            new Object[]{msgTransacoesEstornarCielo}));
                }
            }
            getClienteVO().setDeveAtualizarDependentesSintetico(true);
            getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            extornaBitrix();
            montarSucessoGrowl("Estorno realizado com sucesso.");
            setApresentarBotaoEstorno(false);
            setAbrirRichConfirmacaoEstorno(false);
        } catch (Exception ex) {
            setAbrirRichConfirmacaoEstorno(true);
            montarErro(ex);
            ex.printStackTrace();
        }
    }

    private void extornaBitrix() {
        try {
            //Notifica bitrix se o cliente originou da integração
            ClienteVO clienteVO1s = getFacade().getCliente().consultarPorCodigoPessoa(getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            LeadVO lead = getFacade().getLead().consultarPorCliente(clienteVO1s.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lead.getTipo().equals(TipoLeadEnum.BITIRX24)) {
                String chave = (String) JSFUtilities.getFromSession("key");
                List<ConfiguracaoEmpresaBitrixVO> listbitrix = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoEmpresaBitrix24(chave);
                ConfiguracaoEmpresaBitrixVO config = listbitrix.get(0);
                IntegracaoLeadGenericaServiceImpl bitrix = new IntegracaoLeadGenericaServiceImpl(getFacade().getConfiguracaoSistemaCRM().getCon());

                String jsonString = config.getAcao();
                // Remova as aspas simples e substitua por aspas duplas para tornar a string válida no formato JSON
                jsonString = jsonString.replace("'", "\"");
                // Carrega a lista de campos customizados para recuperar o nome do objeto pelo label
                String jsonFields = bitrix.leadFieldBitrix(config.getUrl(),"l");
                JSONObject jsonObject = new JSONObject(jsonString);
                JSONObject json = Uteis.extractField(jsonFields, "Status Pacto");

                JSONObject fields = new JSONObject();
                JSONObject statuspacto = Uteis.extractField(jsonFields, "Status Pacto");
                fields.put(statuspacto.get("title").toString(), "INTEGRADO");
                fields.put("STATUS_ID", "NEW");
                bitrix.updateStausBitrix(config.getUrl() + jsonObject.getString("updatelead"), (int) lead.getIdLead(), fields);

                String title = "'Lead #"+ lead.getIdLead() +"'";
                fields =  new JSONObject();
                fields.put("filter",  new JSONObject( "{ \"TITLE\": "+ title +"}"));
                fields.put("select",  new JSONArray( "[ \"ID\", \"TITLE\", \"STAGE_ID\", \"PROBABILITY\", \"OPPORTUNITY\", \"CURRENCY_ID\" ]"));
                Object jsonNegocio = bitrix.listNegociacaoBitrix(config.getUrl() + "/crm.deal.list", fields);
                JSONObject JSONObject = new JSONObject(jsonNegocio.toString());

                JSONArray jsonArray = (JSONArray)JSONObject.get("result");
                if(!UteisValidacao.emptyNumber( Integer.parseInt(JSONObject.get("total").toString()))) {
                    JSONObject jsOb = new JSONObject(jsonArray.get(0).toString());
                    estornaBitrix(bitrix, config, jsOb.get("ID").toString());
                }
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void  estornaBitrix( IntegracaoLeadGenericaServiceImpl bitrix, ConfiguracaoEmpresaBitrixVO config, String id )  throws Exception {
        if(!UteisValidacao.emptyString(id)){
            bitrix.extornaNegociacaoBitrix(config.getUrl() + "/crm.deal.delete?id=" + id);
        }

    }

    private void estornarPontosContrato() throws Exception {
        if (getEmpresaLogado().isTrabalharComPontuacao()) {
            List<HistoricoPontosVO> lstHistoricoPontosPesquisado = getFacade().getHistoricoPontos().
                    obterHistoricoPorContrato(TipoItemCampanhaEnum.PLANO, getClienteVO().getCodigo(), getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (HistoricoPontosVO historicoPontosVO : lstHistoricoPontosPesquisado) {
                if (historicoPontosVO.getPontos() > 0) {
                    criarHistoricoPontosEstorno(TipoItemCampanhaEnum.ESTORNO_PLANO, historicoPontosVO);
                }
            }
        }
    }


    private void criarHistoricoPontosEstorno(TipoItemCampanhaEnum tipoEstorno, HistoricoPontosVO pesquisado) throws Exception {
        HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
        if (tipoEstorno.equals(TipoItemCampanhaEnum.ESTORNO_PLANO))
            historicoPontos.setDescricao("Estorno do Contrato: " + getContratoVO().getCodigo() + " utilizando o plano: " + getContratoVO().getPlano().getDescricao());
        else
            historicoPontos.setDescricao("Estorno do Contrato: " + getContratoVO().getCodigo() + " utilizando o plano Duracao: " + getContratoVO().getPlanoDuracao().getDescricaoDuracao());
        historicoPontos.setEntrada(false);
        historicoPontos.setDataConfirmacao(Calendario.hoje());
        historicoPontos.setDataaula(Calendario.hoje());
        historicoPontos.setCliente(getClienteVO());
        historicoPontos.setTipoPonto(tipoEstorno);
        Integer valor = (pesquisado.getPontos() * -1);
        historicoPontos.setPontos(valor);
        getFacade().getHistoricoPontos().incluir(historicoPontos);
    }

    private void atualizarSinteticoContratoAntigoQueFoiRenovado(ContratoVO contratoVO) throws Exception {
        getFacade().getSituacaoClienteSinteticoDW().atualizarInformacoesCreditoTreino(contratoVO.getPessoa().getCodigo(), Calendario.hoje());
        getFacade().getSituacaoClienteSinteticoDW().atualizarBaseOffLineZillyonAcesso((String) JSFUtilities.getFromSession(JSFUtilities.KEY), contratoVO.getPessoa().getCodigo());
    }

    public void processarCliente() throws Exception {
        // pega o ultimo contrato vigente da pessoa
        ContratoVO contrato = getFacade().getContrato().consultarContratoVigentePorPessoa(getClienteVO().getPessoa().getCodigo(), false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        // se contrato encontrado
        if (contrato.getCodigo() != 0) {
            // altera o historico se necessario
            Contrato.gerarHistoricoTemporalUmContrato(contrato.getCodigo());
            // exclui a situacao sintetica do cliente, que ainda é relativa ao contrato estornado
            getFacade().getSituacaoClienteSinteticoDW().excluir(getClienteVO().getCodigo());
        }
        // atualiza a situacao do cliente
        getFacade().getZWFacade().atualizarSintetico(getClienteVO(), Calendario.hoje(),
                SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
    }

    public void consultarResponsavelEstornoContrato() {
        try {
            getContratoVO().setUsuarioVO(getFacade().getUsuario().consultarPorChavePrimaria(getContratoVO().getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getContratoVO().getUsuarioVO().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void adicionarObjContratoVO(ContratoVO obj) {
        int index = 0;
        Iterator i = getListaPessoaVOs().iterator();
        while (i.hasNext()) {
            ContratoVO objExistente = (ContratoVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo())) {
                getListaPessoaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getListaPessoaVOs().add(obj);

    }

    public String getFecharRichModalPanelConfirmacao() {
        if (getAbrirRichConfirmacaoEstorno()) {
            if (getContratoVO().isMostrarMsgExcluirNFse()) {
                return "Richfaces.hideModalPanel('panelConfirmacaoEstornar');Richfaces.showModalPanel('modalConfirmacaoEstornarComNota');";
            } else {
                return "Richfaces.showModalPanel('panelConfirmacaoEstornar');" + getMensagemNotificar();
            }
        } else {
            if (getContratoVO().isExcluirNFSe()) {
                return "Richfaces.hideModalPanel('modalConfirmacaoEstornarComNota');fireElementFromParent('form:btnAtualizaCliente');Notifier.cleanAll();" + getMensagemNotificar();
            } else {
                return "Richfaces.hideModalPanel('panelConfirmacaoEstornar');fireElementFromParent('form:btnAtualizaCliente');Notifier.cleanAll();" + getMensagemNotificar();
            }
        }
    }

    public void estornoContratoExcluindoNFSe() {
        getContratoVO().setExcluirNFSe(true);
        getContratoVO().setMostrarMsgExcluirNFse(false);
        validarPermissaoEstornarContrato();
    }

    public boolean getDesenharColunaNomeContrato() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && obj.getContrato() != null && obj.getContrato().getCodigo() != 0;

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomeVendaAvulsa() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && obj.getVendaAvulsaVO() != null && obj.getVendaAvulsaVO().getCodigo() != 0;

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomeAulaAvusa() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && obj.getAulaAvulsaDiariaVO() != null && obj.getAulaAvulsaDiariaVO().getCodigo() != 0;

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            setMensagemID("");
            return false;
        }
    }

    public void validarCaixaAbrirModal() throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            if (getUsuarioLogado().getAdministrador()) {
                throw new Exception("Está operação não pode se realizada com usuário 'ADMINISTRADOR'");
            } else {

                if (!UteisValidacao.emptyList(getListaTransacoes()) && UteisValidacao.emptyString(getEstornarTransacoes())) {
                    throw new Exception("Deve ser selecionado se deseja estornar as transações");
                }

                if (getContratoVO().isMostrarMsgExcluirNFse()) {
                    setMsgAlert("Richfaces.showModalPanel('modalConfirmacaoEstornarComNota')");
                } else {

                    getContratoVO().setMostrarMsgExcluirNFse(false);
                    getContratoVO().setExcluirNFSe(false);
                    boolean podeNecessitarAbrirCaixa = false;
                    EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!empresaVO.isPermitirEstornarContratoComParcelasPG()) {

                        for (EstornoReciboVO obj : getContratoVO().getListaEstornoRecibo()) {
                            for (MovParcelaVO movParcelaVO : obj.getListaMovParcela()) {
                                if (movParcelaVO.getSituacao().equals("PG") && movParcelaVO.getValorParcela() != null && movParcelaVO.getValorParcela() > BigDecimal.ZERO.doubleValue()) {
                                    throw new Exception("Não é possível estornar este contrato pois o mesmo já possui parcela(s) paga(s).");
                                }
                            }
                        }
                    }
                    for (EstornoReciboVO estorno : getContratoVO().getListaEstornoRecibo()) {
                        for (MovPagamentoVO mp : estorno.getListaMovPagamento()) {
                            if (!UteisValidacao.emptyNumber(mp.getMovconta())) {
                                podeNecessitarAbrirCaixa = true;
                            }
                        }
                    }

                    if (podeNecessitarAbrirCaixa) {
                        ConfiguracaoFinanceiroVO cFinan = getFacade().getConfiguracaoFinanceiro().consultar();
                        if (cFinan.getUsarMovimentacaoContas()) {
                            CaixaControle caixaControle = (CaixaControle) JSFUtilities.getFromSession(CaixaControle.class.getSimpleName());
                            setCaixaAberto(caixaControle.getCaixaVoEmAberto());
                            if (getCaixaAberto() == null || UteisValidacao.emptyNumber(getCaixaAberto().getCodigo())) {
                                caixaControle.abrirCaixa();
                                if (!caixaControle.getMensagemDetalhada().equals("Não existem contas ativas para abrir caixa")) {
                                    setMsgAlert("if(!confirm('Você precisa ter um caixa aberto no financeiro. Deseja abrir?')){return false;};Richfaces.showModalPanel('modalAbrirCaixa')");
                                    return;
                                }
                            }
                        }
                    }

                    if (getExiteOutroContratoPagouMinhaParcela()) {
                        setMsgAlert("Richfaces.showModalPanel('panelMensagemOutroContrato')");
                    } else {
                        validarPermissaoEstornarContrato();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void validarPermissaoEstornarContrato() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                getContratoVO().setUsuarioVO(auto.getUsuario());
                StringBuilder onComplete = new StringBuilder("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {};");
                estornoContrato();
                if (!getErro()) {
                    onComplete.append(" executePostMessage({backToProfile: true});");
                }
                onComplete.append(getMensagemNotificar());
                setExecutarAoCompletar(onComplete.toString());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

        };

        limparMsg();
        if (isPermiteEstornarContratoSemPermissao()){
            auto.autorizar("Confirmação para Estornar Contrato", "EstornoContrato",
                    "Você precisa da permissão \"4.13 -  Estornar contrato\"",
                    "form,panelMensagem", isPermiteEstornarContratoSemPermissao(), listener);
        } else {
            auto.autorizar("Confirmação para Estornar Contrato", "EstornoContrato",
                    "Você precisa da permissão \"4.13 -  Estornar contrato\"",
                    "form,panelMensagem", listener);
        }


    }

    /**
     * @return the contratoVO
     */
    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    /**
     * @param contratoVO the contratoVO to set
     */
    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Boolean getApresentarListaPagamento() {
        return !getContratoVO().getListaEstornoRecibo().isEmpty();
    }

    /**
     * @return the abrirRichConfirmacaoEstorno
     */
    public Boolean getAbrirRichConfirmacaoEstorno() {
        return abrirRichConfirmacaoEstorno;
    }

    /**
     * @param abrirRichConfirmacaoEstorno the abrirRichConfirmacaoEstorno to set
     */
    public void setAbrirRichConfirmacaoEstorno(Boolean abrirRichConfirmacaoEstorno) {
        this.abrirRichConfirmacaoEstorno = abrirRichConfirmacaoEstorno;
    }

    /**
     * @return the apresentarBotaoEstorno
     */
    public Boolean getApresentarBotaoEstorno() {
        return apresentarBotaoEstorno;
    }

    /**
     * @param apresentarBotaoEstorno the apresentarBotaoEstorno to set
     */
    public void setApresentarBotaoEstorno(Boolean apresentarBotaoEstorno) {
        this.apresentarBotaoEstorno = apresentarBotaoEstorno;
    }

    /**
     * @return the exiteOutroContratoPagouMinhaParcela
     */
    public Boolean getExiteOutroContratoPagouMinhaParcela() {
        return exiteOutroContratoPagouMinhaParcela;
    }

    /**
     * @param exiteOutroContratoPagouMinhaParcela the
     *                                            exiteOutroContratoPagouMinhaParcela to set
     */
    public void setExiteOutroContratoPagouMinhaParcela(Boolean exiteOutroContratoPagouMinhaParcela) {
        this.exiteOutroContratoPagouMinhaParcela = exiteOutroContratoPagouMinhaParcela;
    }

    /**
     * @return the listaPessoaVOs
     */
    public List getListaPessoaVOs() {
        return listaPessoaVOs;
    }

    /**
     * @param listaPessoaVOs the listaPessoaVOs to set
     */
    public void setListaPessoaVOs(List listaPessoaVOs) {
        this.listaPessoaVOs = listaPessoaVOs;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();

        contratoVO = null;
        clienteVO = null;
        abrirRichConfirmacaoEstorno = null;
        apresentarBotaoEstorno = null;
        exiteOutroContratoPagouMinhaParcela = null;
        listaPessoaVOs = new ArrayList();
    }

    public boolean isExisteTransacoesJaProcessadas() {
        return existeTransacoesJaProcessadas;
    }

    public void setExisteTransacoesJaProcessadas(boolean existeTransacoesJaProcessadas) {
        this.existeTransacoesJaProcessadas = existeTransacoesJaProcessadas;
    }

    public boolean isExisteTransacoesQuePodemSerCanceladas() {
        return existeTransacoesQuePodemSerCanceladas;
    }

    public void setExisteTransacoesQuePodemSerCanceladas(boolean existeTransacoesQuePodemSerCanceladas) {
        this.existeTransacoesQuePodemSerCanceladas = existeTransacoesQuePodemSerCanceladas;
    }

    public List<TransacaoVO> getListaTransacoes() {
        return listaTransacoes;
    }

    public void setListaTransacoes(List<TransacaoVO> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public List<RemessaItemVO> getListaItensRemessa() {
        return listaItensRemessa;
    }

    public void setListaItensRemessa(List<RemessaItemVO> listaItensRemessa) {
        this.listaItensRemessa = listaItensRemessa;
    }

    public void setTemChequeComLote(Boolean temChequeComLote) {
        this.temChequeComLote = temChequeComLote;
    }

    public Boolean getTemChequeComLote() {
        return temChequeComLote;
    }

    public String getJustificativaEstorno() {
        return justificativaEstorno;
    }

    public void setJustificativaEstorno(String justificativaEstorno) {
        this.justificativaEstorno = justificativaEstorno;
    }

    public CaixaVO getCaixaAberto() {
        return caixaAberto;
    }

    public void setCaixaAberto(CaixaVO caixaAberto) {
        this.caixaAberto = caixaAberto;
    }


    public List<MovParcelaVO> getContratoComParcelasEnvioDeRemessa() throws Exception {
        return getFacade().getMovParcela().consultarPorContratoParcelasEmRemessa(getContratoVO().getCodigo().toString());
    }

    public void consultarUsuarioPorUserName() {
        try {
            setUsuario(getFacade().getUsuario().consultarPorChavePrimaria(
                    getUsuario().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getUsuario().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getEstornarTransacoes() {
        return estornarTransacoes;
    }

    public void setEstornarTransacoes(String estornarTransacoes) {
        this.estornarTransacoes = estornarTransacoes;
    }

    public List<SelectItem> getListaSelectItemEstornarTransacoes() {
        List<SelectItem> lista = new ArrayList<>();
        if (!isPermissaoEstronarTransacao()) {
            lista.add(new SelectItem("NAO", "NÃO"));
        }
        else {
            lista.add(new SelectItem("", ""));
            lista.add(new SelectItem("SIM", "SIM"));
            lista.add(new SelectItem("NAO", "NÃO"));
        }
        return lista;
    }

    public boolean isPermissaoEstronarTransacao() {
        return permissaoEstronarTransacao;
    }

    public void setPermissaoEstronarTransacao(boolean permissaoEstronarTransacao) {
        this.permissaoEstronarTransacao = permissaoEstronarTransacao;
    }

    public boolean isPermiteEstornarContratoSemPermissao() {
        return permiteEstornarContratoSemPermissao;
    }

    public void setPermiteEstornarContratoSemPermissao(boolean permiteEstornarContratoSemPermissao) {
        this.permiteEstornarContratoSemPermissao = permiteEstornarContratoSemPermissao;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import negocio.comuns.utilitarias.ContadorTempo;

/**
 *
 * <AUTHOR>
 */
public class AfastamentoContratoControle extends SuperControle {

    public AfastamentoContratoControle() throws Exception {
    }

    public void novo() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_CONTRATO);
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.pegarClienteTelaCliente();
            }
            setMensagemID("msg_entre_dados");
            setS<PERSON>sso(false);
            setErro(false);
            setAtencao(false);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
}

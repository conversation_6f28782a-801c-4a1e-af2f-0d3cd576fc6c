package controle.contrato;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.FamiliaTO;
import negocio.comuns.basico.FamiliarTO;
import negocio.comuns.contrato.BonusContratoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NegociacaoFamiliaresControle extends SuperControle {

    private List<FamiliarTO> familiares = new ArrayList<FamiliarTO>();
    private Map<Integer, ContratoVO> contratos = new HashMap<Integer, ContratoVO>();

    public void inicializarFamilia(String matricula){
        try {
            familiares = getFacade().getFamiliar().consultarFamiliaresMatricula(matricula, true);
        }catch (Exception e){
            Uteis.logar(e, NegociacaoFamiliaresControle.class);
        }
    }

    public void editarIndividualmente(){
        try {
            setMsgAlert("");
            ContratoControle contratoControle = (ContratoControle) JSFUtilities.getFromSession(ContratoControle.class);
            FamiliarTO f = (FamiliarTO) FacesContext.getCurrentInstance().getExternalContext()
                    .getRequestMap().get("f");

            if(!contratos.keySet().contains(contratoControle.getContratoVO().getPessoa().getCodigo())){
                for(FamiliarTO fa : familiares){
                    if(fa.getCodigoPessoa().equals(contratoControle.getContratoVO().getPessoa().getCodigo())){
                        fa.setSelecionado(true);
                    }
                }
                contratos.put(contratoControle.getContratoVO().getPessoa().getCodigo(), contratoControle.getContratoVO());
            }

            if(!contratos.keySet().contains(f.getCodigoPessoa())){
                ContratoVO clone = (ContratoVO) contratoControle.getContratoVO().getClone(false);
                ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(f.getCodigoCliente(), Uteis.NIVELMONTARDADOS_TODOS);
                clone.setPessoa(clienteVO.getPessoa());
                clone.setCliente(clienteVO);
                contratos.put(f.getCodigoPessoa(), clone);
            }
            contratoControle.setContratoVO(contratos.get(f.getCodigoPessoa()));

        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }




    }

    public boolean verificarNegociacaoFamilia() throws Exception{
        if(UteisValidacao.emptyList(getFamiliares())){
            return false;
        }
        for(FamiliarTO familiar : getFamiliares()){
            if(familiar.getSelecionado()){
            }
        }

        return true;

    }


    public void gravarContratosSimilares(ContratoVO contratoVO, BonusContratoVO bonus, HttpServletRequest httpServletRequest, String chaveSessao) throws Exception{
        List<Integer> gravados = new ArrayList<Integer>();
        gravados.add(contratoVO.getPessoa().getCodigo());
        for(FamiliarTO f : familiares){
            if(f.getSelecionado() && !gravados.contains(f.getCodigoPessoa())){
                contratoVO.setCodigo(0);
                ClienteVO cliente = getFacade().getCliente().consultarPorCodigo(f.getCodigoCliente(), false, Uteis.NIVELMONTARDADOS_TODOS);
                contratoVO.setPessoa(cliente.getPessoa());
                contratoVO.setCliente(cliente);
                getFacade().getZWFacade().incluirComBonus(contratoVO, bonus, httpServletRequest,chaveSessao);
            }
        }
    }

    public List<FamiliarTO> getFamiliares() {
        return familiares;
    }

    public void setFamiliares(List<FamiliarTO> familiares) {
        this.familiares = familiares;
    }

    public Map<Integer, ContratoVO> getContratos() {
        return contratos;
    }

    public void setContratos(Map<Integer, ContratoVO> contratos) {
        this.contratos = contratos;
    }
}

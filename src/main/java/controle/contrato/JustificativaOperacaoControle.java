package controle.contrato;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.JustificativaOperacao;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * justificativaOperacaoForm.jsp justificativaOperacaoCons.jsp) com as funcionalidades da classe <code>JustificativaOperacao</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see JustificativaOperacao
 * @see JustificativaOperacaoVO
 */
public class JustificativaOperacaoControle extends SuperControle {

    private JustificativaOperacaoVO justificativaOperacaoVO;
    private String msgAlert;

    /**
     * Interface <code>JustificativaOperacaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public JustificativaOperacaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    public void inicializarUsuarioLogado() {
        try {
            justificativaOperacaoVO.setUsuarioVO(new UsuarioVO());
            justificativaOperacaoVO.getUsuarioVO().setCodigo(getUsuarioLogado().getCodigo());
            justificativaOperacaoVO.getUsuarioVO().setAdministrador(getUsuarioLogado().getAdministrador());
        } catch (Exception ignored) {
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>JustificativaOperacao</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        reset();
        return "editar";
    }

    public void reset() {
        setJustificativaOperacaoVO(new JustificativaOperacaoVO());
        inicializarUsuarioLogado();
        inicializarListasSelectItemTodosComboBox();
        limparMsg();
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>JustificativaOperacao</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            JustificativaOperacaoVO obj = getFacade().getJustificativaOperacao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setJustificativaOperacaoVO(new JustificativaOperacaoVO());
            setJustificativaOperacaoVO(obj);
            justificativaOperacaoVO.registrarObjetoVOAntesDaAlteracao();
            inicializarListasSelectItemTodosComboBox();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>JustificativaOperacaoVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(JustificativaOperacaoVO obj) {
        if (obj.getEmpresa() == null) {
            obj.setEmpresa(new EmpresaVO());
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>JustificativaOperacao</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (justificativaOperacaoVO.getDescricao().length() > 50) {
                throw new Exception(getMensagemInternalizacao("justificativa_valor_muito_longo"));
            }

            if (justificativaOperacaoVO.getApresentarTodasEmpresas()) {
                justificativaOperacaoVO.setEmpresa(null);
            } else {
                justificativaOperacaoVO.setEmpresa(getEmpresaLogado());
            }

            if (justificativaOperacaoVO.isNovoObj()) {
                getFacade().getJustificativaOperacao().incluir(justificativaOperacaoVO);
                incluirLogInclusao();
            } else {
                getFacade().getJustificativaOperacao().alterar(justificativaOperacaoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP JustificativaOperacaoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List<JustificativaOperacaoVO> objs = new ArrayList<>();
            switch (getControleConsulta().getCampoConsulta()) {
                case "codigo":
                    if (getControleConsulta().getValorConsulta().equals("")) {
                        getControleConsulta().setValorConsulta("0");
                    }
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    objs = getFacade().getJustificativaOperacao().consultarPorCodigo(valorInt, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
                    break;
                case "descricao":
                    objs = getFacade().getJustificativaOperacao().consultarPorDescricao(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
                    break;
                case "tipoOp":
                    objs = getFacade().getJustificativaOperacao().consultarPorTipoOperacao(getControleConsulta().getValorConsulta(), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
                    break;
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setListaConsulta(new ArrayList<>());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>JustificativaOperacaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            if (getFacade().getJustificativaOperacao().isUtiliza(getEmpresaLogado().getCodigo(), justificativaOperacaoVO.getCodigo())) {
                throw new ValidacaoException("Existe contratos de operação utilizando a justificativa");
            }
            getFacade().getJustificativaOperacao().excluir(justificativaOperacaoVO);
            incluirLogExclusao();
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"justificativaoperacao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"justificativaoperacao\" violates foreign key")) {
                setMensagemDetalhada("Este Horário não pode ser excluído, pois está sendo utilizado!");
            } else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>tipoOperacao</code>
     */
    public List<SelectItem> getListaSelectItemTipoOperacaoJustificativaOperacao() throws Exception {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("", ""));
        Hashtable<String, String> tipoJustificativaOperacaos = Dominios.getTipoJustificativaOperacao();
        Enumeration<String> keys = tipoJustificativaOperacaos.keys();
        while (keys.hasMoreElements()) {
            String value = keys.nextElement();
            String label = tipoJustificativaOperacaos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        try {
            montarListaEmpresas();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<>();
        itens.add(new SelectItem("codigo", "Código"));
        // itens.add(new SelectItem("nomeEmpresa", "Empresa"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("tipoOp", "Tipo Operação"));
        return itens;
    }

    public String inicializarConsultar() {
        setListaConsulta(new ArrayList<>());
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
     * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente
     * quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        justificativaOperacaoVO = null;

    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public JustificativaOperacaoVO getJustificativaOperacaoVO() {
        if (justificativaOperacaoVO == null) {
            justificativaOperacaoVO = new JustificativaOperacaoVO();
        }
        return justificativaOperacaoVO;
    }

    public void setJustificativaOperacaoVO(JustificativaOperacaoVO justificativaOperacaoVO) {
        this.justificativaOperacaoVO = justificativaOperacaoVO;
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getJustificativaOperacao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void incluirLogInclusao() throws Exception {
        try {
            justificativaOperacaoVO.setObjetoVOAntesAlteracao(new JustificativaOperacaoVO());
            justificativaOperacaoVO.setNovoObj(true);
            registrarLogObjetoVO(justificativaOperacaoVO, justificativaOperacaoVO.getCodigo(), "JUSTIFICATIVAOPERACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("JUSTIFICATIVAOPERACAO", justificativaOperacaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE JUSTIFICATIVAOPERACAO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        justificativaOperacaoVO.setNovoObj(false);
        justificativaOperacaoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            justificativaOperacaoVO.setObjetoVOAntesAlteracao(new JustificativaOperacaoVO());
            justificativaOperacaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(justificativaOperacaoVO, justificativaOperacaoVO.getCodigo(), "JUSTIFICATIVAOPERACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("JUSTIFICATIVAOPERACAO", justificativaOperacaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE JUSTIFICATIVAOPERACAO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     *
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(justificativaOperacaoVO, justificativaOperacaoVO.getCodigo(), "JUSTIFICATIVAOPERACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("JUSTIFICATIVAOPERACAO", justificativaOperacaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE JUSTIFICATIVAOPERACAO ", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        justificativaOperacaoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = justificativaOperacaoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), justificativaOperacaoVO.getCodigo(), 0);
    }

    public void realizarConsultaLogObjetoGeral() {
        justificativaOperacaoVO = new JustificativaOperacaoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public void confirmarExcluir() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Justificativa de Operação",
                "Deseja excluir a Justificativa de Operação?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

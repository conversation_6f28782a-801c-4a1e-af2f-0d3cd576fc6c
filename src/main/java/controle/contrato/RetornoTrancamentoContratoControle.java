/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import edu.emory.mathcs.backport.java.util.Collections;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.contrato.TrancamentoContratoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.plano.Produto;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class RetornoTrancamentoContratoControle extends SuperControle {

    protected TrancamentoContratoVO trancamentoContratoVO;
    protected List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs;
    protected List<ContratoVO> listaContratoVOs;
    protected List<ProdutoVO> listaProdutoTrancamentoVOs;
    protected List<TrancamentoContratoVO> listaTrancamentoVOs;
    protected Boolean abrirRichModalDeConfirmacao;
    protected Boolean apresentarBotoes;
    protected String visualizacaoObservacao;
    protected Date dataRetornoAuxiliar;
    private Boolean necessitaManutencao;
    

    public RetornoTrancamentoContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
    }

    public void inicializarUsuarioLogado() {
        try {
            getTrancamentoContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getTrancamentoContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void inicializarEmpresaLogado() throws Exception {
        try {
            getTrancamentoContratoVO().setEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            throw e;
        }
    }

    public void tratarHorariosTurmasOcupacao() {
        try {
            trancamentoContratoVO.setMatriculaVigentes(getFacade().getMatriculaAlunoHorarioTurma()
                .consultarMatriculaAtiva(trancamentoContratoVO.getContratoVO().getCodigo(),Uteis.obterDataAnterior(Calendario.hoje(), 1)));
            necessitaManutencao = false;
            boolean estaNaTurma = false;
            boolean adicionarNova = true;
            for(ContratoModalidadeVO cm : trancamentoContratoVO.getContratoVO().getContratoModalidadeVOs()){
                for(Object cmt : cm.getContratoModalidadeTurmaVOs()){
                    for (Iterator it = ((ContratoModalidadeTurmaVO)cmt).getContratoModalidadeHorarioTurmaVOs().iterator(); it.hasNext();) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) it.next();
                        for (MatriculaAlunoHorarioTurmaVO matTurma : trancamentoContratoVO.getMatriculaVigentes()){
                            if(matTurma.getHorarioTurma().getCodigo().equals(cmht.getHorarioTurma().getCodigo())){
                                adicionarNova = false;
                                if(Calendario.maiorOuIgual(matTurma.getDataFim(), Calendario.hoje())){//a consulta das vigentes busca a data de ontem, para que a matricula tenha continuidade, mas a ocupação deve ser verificada hoje.
                                    estaNaTurma = true;
                                }
                                break;
                            }
                        }
                        if((cmht.getHorarioTurma().estahLotada(estaNaTurma) && ((ContratoModalidadeTurmaVO)cmt).getTurma().isBloquearMatriculasAcimaLimite()) || cmht.getHorarioTurma().getSituacao().equals("IN") ){
                             necessitaManutencao = true;
                        }
                        if(adicionarNova){
                            MatriculaAlunoHorarioTurmaVO mat = new MatriculaAlunoHorarioTurmaVO();
                            mat.setDataInicio(Calendario.hoje());
                            mat.setHorarioTurma(cmht.getHorarioTurma());
                            mat.setContrato(trancamentoContratoVO.getContratoVO());
                            mat.setPessoa(trancamentoContratoVO.getContratoVO().getPessoa());
                            mat.setEmpresa(trancamentoContratoVO.getContratoVO().getEmpresa().getCodigo());
                            trancamentoContratoVO.getMatriculaVigentes().add(mat);
                        }
                        estaNaTurma = false;
                        adicionarNova = true;
                    }
                }
            }
        } catch (Exception ex) {
            setApresentarBotoes(true);
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void criaObjetoNovoRetorno() {
        try {
            setProcessandoOperacao(false);
            setTrancamentoContratoVO(new TrancamentoContratoVO());
            setListaContratoVOs(new ArrayList<ContratoVO>());
            montarDadosContratoParaTrancamento();
            obterUltimoTracamentoContrato();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void novo() {
        novoRetornoEvent(null);
    }

    public void novoRetornoEvent(ActionEvent event) {
        setMensagemDetalhada("","");
        setMensagem("");
        criaObjetoNovoRetorno();
        tratarHorariosTurmasOcupacao();
    }

    public void gravarRetornoSemInteracaoComUsuario() throws  Exception {
        getFacade().getTrancamentoContrato().incluirRetorno(trancamentoContratoVO);
        logGravacaoRetorno();
        if(!trancamentoContratoVO.getContratoVO().getSituacao().equals("TR") && !trancamentoContratoVO.getContratoVencido() && necessitaManutencao){
            Map<String, Object> mapaDeSessao =context().getExternalContext().getSessionMap();
            if (mapaDeSessao.get("ManutencaoModalidadeControle") == null) {
                mapaDeSessao.put("ManutencaoModalidadeControle", new ManutencaoModalidadeControle());
            }
            ((ManutencaoModalidadeControle)mapaDeSessao.get("ManutencaoModalidadeControle")).novo();
            redirect("/faces/manutencaoModalidade.jsp");
        }
    }

    private void logGravacaoRetorno() {
        try {
            trancamentoContratoVO.setObjetoVOAntesAlteracao(new TrancamentoContratoVO());
            trancamentoContratoVO.setNovoObj(true);
            registrarLogObjetoVO(trancamentoContratoVO, trancamentoContratoVO.getCodigo(), "TRANCAMENTOCONTRATO", trancamentoContratoVO.getContratoVO().getPessoa().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosContratoParaTrancamento() throws Exception {
        ClienteControle clienteControle = (ClienteControle) obtenhaObjetoSessionMap("ClienteControle");
        if (clienteControle != null) {
            clienteControle.pegarClienteTelaCliente();
            getTrancamentoContratoVO().setContratoVO(clienteControle.getContratoVO());
            getListaContratoVOs().add(clienteControle.getContratoVO());
        } else {
            throw new Exception("Não foi possível inicializar os dados do Contrato.");
        }
    }

    public void validarRetorno() {
        try {
            TrancamentoContratoVO.validarDadosRetorno(trancamentoContratoVO);
            setErro(false);
            trancamentoContratoVO.setMensagemErro(false);
            setAbrirRichModalDeConfirmacao(true);
            if(necessitaManutencao && !trancamentoContratoVO.getTaxaDiasExcedidos() && !trancamentoContratoVO.getContratoVencido()){
                 trancamentoContratoVO.setMensagemManutencao(true);
            }
            setMensagemDetalhada("");
            abrirRichConfimacaoDeTrancamento();
        } catch (Exception e) {
            setAbrirRichModalDeConfirmacao(false);
            setErro(true);
            montarErro(e);
        }
    }
    
     public void fecharMenssagemManutencao() {
         trancamentoContratoVO.setMensagemManutencao(false);
         abrirRichConfimacaoDeTrancamento();
     }

    public String abrirRichConfimacaoDeTrancamento() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                trancamentoContratoVO.setResponsavelOperacao(auto.getUsuario());

                setProcessandoOperacao(true);
                gravarRetornoSemInteracaoComUsuario();

                montarSucessoGrowl("");
                setApresentarBotoes(false);

                setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente');} catch(e) {}; fecharJanela(); executePostMessage({reloadContractPage: true});");

                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                setProcessandoOperacao(false);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        try {
            /**
             * Preparar o método para autorização
             */
            if (getAbrirRichModalDeConfirmacao() && !trancamentoContratoVO.getMensagemManutencao()) {
            auto.autorizar("Confirmação de Retorno de Trancamento", "Trancamento_Autorizar",
                    "Você precisa da permissão \"3.10 - Trancamento do  Contrato - Autorizar\"",
                    "gridMensagens", listener);
            }

        } catch (Exception e) {
            montarErro(e);
        }

        return "";
    }

    public void consultarResponsavel() {
        try {
            trancamentoContratoVO.setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(trancamentoContratoVO.getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarTrancamentosDataPrimeiroTrancamento() throws Exception {
        trancamentoContratoVO.setDataPrimeiroTrancamentoSeq((Date)trancamentoContratoVO.getDataTrancamento().clone());
        setListaTrancamentoVOs(getFacade().getTrancamentoContrato()
                    .consultarPorCodigoContrato(trancamentoContratoVO.getContratoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS));
        Ordenacao.ordenarListaReverse(listaTrancamentoVOs, "dataTrancamento");
        for (TrancamentoContratoVO trancamento : listaTrancamentoVOs ){
            if(trancamentoContratoVO.getCodigo().equals(trancamento.getCodigo())){
                continue;
            } else if(Calendario.igual(trancamentoContratoVO.getDataPrimeiroTrancamentoSeq(),Uteis.somarDias(trancamento.getDataFimTrancamento(),1))){
                trancamentoContratoVO.setDataPrimeiroTrancamentoSeq(trancamento.getDataTrancamento());
            } else {
                break;
            }
        }
        Collections.reverse(listaTrancamentoVOs);
    }

    public void obterUltimoTracamentoContrato() throws Exception {
        setTrancamentoContratoVO(getFacade().getTrancamentoContrato().obterUltimoDiaRetornoContrato(trancamentoContratoVO.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        if (getTrancamentoContratoVO() != null) {
            if (Uteis.getCompareData(getTrancamentoContratoVO().getDataRetorno(), negocio.comuns.utilitarias.Calendario.hoje()) >= 0) {
                trancamentoContratoVO.setApresentarPanelClienteRetornoNoPrazo(true);
                trancamentoContratoVO.setApresentarPanelClienteRetornoForaPrazo(false);
            } else {
                trancamentoContratoVO.setApresentarPanelClienteRetornoForaPrazo(true);
                trancamentoContratoVO.setApresentarPanelClienteRetornoNoPrazo(false);
            }
            setDataRetornoAuxiliar(trancamentoContratoVO.getDataRetorno());
            trancamentoContratoVO.setObservacao("");
            setListaTrancamentoVOs(new ArrayList<TrancamentoContratoVO>());
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            consultarTrancamentosDataPrimeiroTrancamento();
            setAbrirRichModalDeConfirmacao(false);
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(true);
            setVisualizacaoObservacao("");
        } else {
            setTrancamentoContratoVO(new TrancamentoContratoVO());
        }
    }

    public void descarmarTaxaExcedida() {
        try {
            trancamentoContratoVO.setTaxaDiasExcedidos(false);
            trancamentoContratoVO.setProdutoTrancamento(new ProdutoVO());
            trancamentoContratoVO.setTipoJustificativa(0);
            trancamentoContratoVO.setDataTrancamento(negocio.comuns.utilitarias.Calendario.hoje());
            trancamentoContratoVO.setDataFimTrancamento(negocio.comuns.utilitarias.Calendario.hoje());
            trancamentoContratoVO.setDataRetorno(getDataRetornoAuxiliar());
            Long a = Uteis.nrDiasEntreDatas(getDataRetornoAuxiliar(), negocio.comuns.utilitarias.Calendario.hoje());
            trancamentoContratoVO.setNumeroDiasDescontar(a.intValue());
            if (trancamentoContratoVO.getNumeroDiasDescontar() > trancamentoContratoVO.getNrDiasCongelado()){
                trancamentoContratoVO.setContratoVencido(true);
            }
            setMensagem("");
            setMensagemDetalhada("");
            setMensagemID("");
            setErro(false);
        } catch (Exception e) {
            setErro(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void descarmarDescontoExcedida() {
        trancamentoContratoVO.setDataTrancamento(getDataRetornoAuxiliar());
        trancamentoContratoVO.getProdutoTrancamento().setCodigo(0);
        trancamentoContratoVO.setTipoJustificativa(0);
        trancamentoContratoVO.setDescontoDiasExcedidos(false);
        trancamentoContratoVO.setApresentarPeriodoTrancamento(false);
        montarDadosListaProdutoTrancamentoVOs();
        montarDadosListaJustificativaOperacaoVOs();
        setMensagem("");
        setMensagemDetalhada("");
        setMensagemID("");
        setErro(false);
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Cidade</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public void obterObservacaoTrancamento() {
        TrancamentoContratoVO obj = (TrancamentoContratoVO) obtenhaObjetoRequestMap("trancamento");
        setVisualizacaoObservacao(obj.getObservacao());
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs() {
        try {
            montarDadosListaJustificativaOperacaoVOs("TR");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs(String prm) throws Exception {
        List resultadoConsulta = consultarTipoJustificativaOperacaoPorTipo(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            JustificativaOperacaoVO obj = (JustificativaOperacaoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaJustificativaOperacaoVOs(objs);
    }

    public List consultarTipoJustificativaOperacaoPorTipo(String prm) throws Exception {
        return getFacade().getJustificativaOperacao().consultarPorTipoOperacao(prm, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void montarDadosListaProdutoTrancamentoVOs() {
        try {
            montarDadosListaProtudoTrancamentoVOs("TR");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaProtudoTrancamentoVOs(String prm) throws Exception {
        List<ProdutoVO> resultadoConsulta = consultarTipoProdutoTrancamento(prm);
        Iterator<ProdutoVO> i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            ProdutoVO obj = i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaProdutoTrancamentoVOs(objs);
    }

    public List<ProdutoVO> consultarTipoProdutoTrancamento(String prm) throws Exception {
        return getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", prm, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void obterValorProduto() {
        try {
            if (getTrancamentoContratoVO().getProdutoTrancamento().getCodigo() != 0) {
                ProdutoVO obj = new Produto().consultarPorChavePrimaria(getTrancamentoContratoVO().getProdutoTrancamento().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                getTrancamentoContratoVO().setValorTrancamento(obj.getValorFinal());
                getTrancamentoContratoVO().setProdutoTrancamento(obj);
                getTrancamentoContratoVO().setApresentarPeriodoTrancamento(true);
                getTrancamentoContratoVO().setDataFimTrancamento(Uteis.obterDataFutura2(getTrancamentoContratoVO().getDataTrancamento(), (obj.getNrDiasVigencia() - 1)));
                getTrancamentoContratoVO().setDataRetorno(Uteis.obterDataFutura2(getTrancamentoContratoVO().getDataTrancamento(), obj.getNrDiasVigencia()));
            } else {
                getTrancamentoContratoVO().setValorTrancamento((double) 0);
                getTrancamentoContratoVO().setApresentarPeriodoTrancamento(false);
                getTrancamentoContratoVO().setDataRetorno(negocio.comuns.utilitarias.Calendario.hoje());
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            setErro(false);
        } catch (Exception e) {
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void limparMensagem() {
        setMensagemDetalhada("");
        setMensagem("");
        setMensagemID("");
        setErro(false);
    }

    public List<JustificativaOperacaoVO> getListaJustificativaOperacaoVOs() {
        return listaJustificativaOperacaoVOs;
    }

    public void setListaJustificativaOperacaoVOs(List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs) {
        this.listaJustificativaOperacaoVOs = listaJustificativaOperacaoVOs;
    }

    public List<ProdutoVO> getListaProdutoTrancamentoVOs() {
        return listaProdutoTrancamentoVOs;
    }

    public void setListaProdutoTrancamentoVOs(List<ProdutoVO> listaProdutoTrancamentoVOs) {
        this.listaProdutoTrancamentoVOs = listaProdutoTrancamentoVOs;
    }

    public TrancamentoContratoVO getTrancamentoContratoVO() {
        return trancamentoContratoVO;
    }

    public void setTrancamentoContratoVO(TrancamentoContratoVO trancamentoContratoVO) {
        this.trancamentoContratoVO = trancamentoContratoVO;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public Boolean getAbrirRichModalDeConfirmacao() {
        return abrirRichModalDeConfirmacao;
    }

    public void setAbrirRichModalDeConfirmacao(Boolean abrirRichModalDeConfirmacao) {
        this.abrirRichModalDeConfirmacao = abrirRichModalDeConfirmacao;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public List<TrancamentoContratoVO> getListaTrancamentoVOs() {
        return listaTrancamentoVOs;
    }

    public void setListaTrancamentoVOs(List<TrancamentoContratoVO> listaTrancamentoVOs) {
        this.listaTrancamentoVOs = listaTrancamentoVOs;
    }

    public String getVisualizacaoObservacao() {
        if (visualizacaoObservacao == null) {
            visualizacaoObservacao = "";
        }
        return visualizacaoObservacao;
    }

    public void setVisualizacaoObservacao(String visualizacaoObservacao) {
        this.visualizacaoObservacao = visualizacaoObservacao;
    }

    public Date getDataRetornoAuxiliar() {
        return dataRetornoAuxiliar;
    }

    public void setDataRetornoAuxiliar(Date dataRetornoAuxiliar) {
        this.dataRetornoAuxiliar = dataRetornoAuxiliar;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        trancamentoContratoVO = null;
        listaJustificativaOperacaoVOs = new ArrayList<JustificativaOperacaoVO>();
        listaContratoVOs = new ArrayList<ContratoVO>();
        listaProdutoTrancamentoVOs = new ArrayList<ProdutoVO>();
        listaTrancamentoVOs = new ArrayList<TrancamentoContratoVO>();
        abrirRichModalDeConfirmacao = null;
        apresentarBotoes = null;
        visualizacaoObservacao = null;
    }

    public Boolean getNecessitaManutencao() {
        if (necessitaManutencao == null) {
            necessitaManutencao = false;
        }
        return necessitaManutencao;
    }

    public void setNecessitaManutencao(Boolean necessitaManutencao) {
        this.necessitaManutencao = necessitaManutencao;
    }
}

package controle.contrato;

/**
 * Created by <PERSON> on 19/07/2016.
 */
public class ContratoComponenteID {

    private String VALORES_CONTRATO;
    private String CONDICAO_PAGAMENTO;

    public ContratoComponenteID(){

         StringBuilder aux = new StringBuilder();
         aux.append("containerValoresContrato");
         aux.append(",total1");
         aux.append(",panelProdutoParcela");
         setVALORES_CONTRATO(aux.toString());

         aux.append(",panelVencimentoCartao");
         aux.append(",containerCondicaoPagamento");
         aux.append(",panelAutorizacoesCobrancaCliente");
         setCONDICAO_PAGAMENTO(aux.toString());

    }

    public String getValoresContrato() {
        return VALORES_CONTRATO;
    }

    public void setVALORES_CONTRATO(String VALORES_CONTRATO) {
        this.VALORES_CONTRATO = VALORES_CONTRATO;
    }

    public String getCondicaoPagamento() {
        return CONDICAO_PAGAMENTO;
    }

    public void setCONDICAO_PAGAMENTO(String CONDICAO_PAGAMENTO) {
        this.CONDICAO_PAGAMENTO = CONDICAO_PAGAMENTO;
    }
}

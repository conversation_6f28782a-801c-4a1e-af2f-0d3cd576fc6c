/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.contrato.CarenciaContratoVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RetornoCarenciaContratoControle extends SuperControle {

    private CarenciaContratoVO carenciaContratoVO;
    private Boolean apresentarBotoes;
    private List<ContratoVO> listaContratoVOs;
    private boolean existeOperacaoFutura = false;

    public RetornoCarenciaContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        novo();
    }

    public void inicializarUsuarioLogado() {
        try {
            getCarenciaContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getCarenciaContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
        } catch (Exception ignored) {
        }
    }

    public void novo() throws Exception {
        try {
            setMsgAlert("");
            setProcessandoOperacao(false);
            setCarenciaContratoVO(new CarenciaContratoVO());
            setListaContratoVOs(new ArrayList<>());
            inicializarUsuarioLogado();
            montarDadosContratoParaAtestado();
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(true);            
        } catch (Exception e) {
            setApresentarBotoes(false);
            montarErro(e);
        }
    }

    public void montarDadosContratoParaAtestado() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.pegarClienteTelaCliente();
                getCarenciaContratoVO().setContratoVO(clienteControle.getContratoVO());
                getCarenciaContratoVO().setEmpresa(clienteControle.getClienteVO().getEmpresa().getCodigo());

                ContratoOperacaoVO contratoOperacaoVO = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato( clienteControle.getContratoVO().getCodigo(), "CR" , false, Uteis.NIVELMONTARDADOS_ROBO);
                getCarenciaContratoVO().setDataInicio(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
                getCarenciaContratoVO().setDataTermino(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
                this.existeOperacaoFutura = ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetornoBoolean(getCarenciaContratoVO().getContratoVO());
                if(existeOperacaoFutura){
                     throw new Exception("Existe Operação futura que impede esse retorno");
                }
                getListaContratoVOs().add(clienteControle.getContratoVO());
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }

        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void consultarResponsavel() {
        try {
            getCarenciaContratoVO().setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(getCarenciaContratoVO().getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void gravar() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);

                if (auto.getUsuario() != null && !UteisValidacao.emptyNumber(auto.getUsuario().getCodigo())) {
                    getCarenciaContratoVO().setResponsavelOperacao(auto.getUsuario());
                } else {
                    getCarenciaContratoVO().setResponsavelOperacao(getUsuarioLogado());
                }
                getFacade().getContratoOperacao().incluirOperacaoRetornoCarencia(carenciaContratoVO);

                setApresentarBotoes(false);
                montarSucessoDadosGravados();

                setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});" + getMensagemNotificar());

                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setProcessandoOperacao(false);
                setApresentarBotoes(true);
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        try {
            auto.autorizar("Confirmação de retorno das Férias", "Carencia_Autorizar",
                    "Você precisa da permissão \"3.13 - Férias para Contrato - Autorizar\"",
                    "form", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public CarenciaContratoVO getCarenciaContratoVO() {
        return carenciaContratoVO;
    }

    public void setCarenciaContratoVO(CarenciaContratoVO carenciaContratoVO) {
        this.carenciaContratoVO = carenciaContratoVO;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        carenciaContratoVO = null;
        listaContratoVOs.clear();
        apresentarBotoes = null;

    }

    public boolean isExisteOperacaoFutura() {
        return existeOperacaoFutura;
    }

    public void setExisteOperacaoFutura(boolean existeOperacaoFutura) {
        this.existeOperacaoFutura = existeOperacaoFutura;
    }
    
    
}

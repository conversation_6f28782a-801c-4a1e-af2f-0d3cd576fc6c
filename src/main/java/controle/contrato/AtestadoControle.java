package controle.contrato;

import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.basico.TelaClienteControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ArquivoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.AtestadoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import servicos.integracao.TreinoWSConsumer;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;

/**
 * Created by glauco on 04/06/2014
 */
public class AtestadoControle extends SuperControle {

    private Date dataInicio = new Date();
    private Integer nrDias = 0;
    private Date dataFinal = new Date();
    private String observacao = "";
    private ProdutoVO atestado = new ProdutoVO();
    private ClienteVO cliente = new ClienteVO();
    private Boolean parqPositivo = false;
    private File arquivoAtestado;
    private String extensaoArquivoAtestado = "";
    private Boolean apresentarBotoes = true;
    private boolean edicaoDeAtestado = false;
    private boolean existeArquivo = false;
    private AtestadoVO atestadoVO = new AtestadoVO();
    private List<SelectItem> produtosAtestados = new ArrayList<SelectItem>();
    private MovProdutoVO movProdutoExclusao = new MovProdutoVO();
    private String onCompleteImprimir;

    public AtestadoControle() throws Exception {
        consultarProdutosAtestados();
    }

    public void prepararAtestado(ActionEvent evt) {
        setEdicaoDeAtestado(false);
        setApresentarBotoes(true);
        setAtestadoVO(new AtestadoVO());
        setAtestado(new ProdutoVO());
        setDataInicio(new Date());
        setNrDias(0);
        setDataFinal(new Date());
        setParqPositivo(false);
        setObservacao("");
        setArquivoAtestado(null);

        setSucesso(false);
        setErro(false);
        setMensagem("");
        setMensagemDetalhada("", "");

        setCliente((ClienteVO) evt.getComponent().getAttributes().get("cliente"));
    }

    public void editarAtestado() {
        setEdicaoDeAtestado(true);
        setApresentarBotoes(false);
    }

    private void consultarProdutosAtestados() throws Exception {
        List<ProdutoVO> produtos = getFacade().getProduto().consultarProdutosPorTipoProduto(TipoProduto.ATESTADO.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        List<SelectItem> produtosSelec = new ArrayList<SelectItem>();
        for (ProdutoVO prod : produtos) {
            produtosSelec.add(new SelectItem(prod.getCodigo(), prod.getDescricao()));
        }
        setProdutosAtestados(produtosSelec);
    }

    public void gerarPeriodoRetornoAtestado() {
        try {
            limparMsg();
            if (getDataInicio() != null && getNrDias() != 0) {
                Date novaData = Uteis.somarDias(getDataInicio(), getNrDias());
                setDataFinal(novaData);
            }
            if (getDataInicio() != null && getDataFinal() != null) {
                if (Calendario.maior(getDataInicio(), getDataFinal())) {
                    throw new Exception("O campo ATÉ não pode ser antes do campo INÍCIO");
                }

                setNrDias(((Long) Uteis.nrDiasEntreDatas(getDataInicio(), getDataFinal())).intValue());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validar() throws Exception {
        if (getDataInicio() == null || getDataFinal() == null) {
            throw new Exception("Informe todas as datas!");
        }
        if (Calendario.maior(getDataInicio(), getDataFinal())) {
            throw new Exception("O campo ATÉ não pode ser antes do campo INÍCIO!");
        }
    }

    public void gerarLog(MovProdutoVO movProdutoVO,
                         VendaAvulsaVO vendaAvulsaVO) throws Exception {
        try {
            if (movProdutoVO == null) {
                if (vendaAvulsaVO.getMovProdutoVOs() != null && !vendaAvulsaVO.getMovProdutoVOs().isEmpty()) {
                    movProdutoVO = vendaAvulsaVO.getMovProdutoVOs().get(0);
                    movProdutoVO.setObjetoVOAntesAlteracao(new MovProdutoVO());
                } else {
                    movProdutoVO = new MovProdutoVO();
                }
                movProdutoVO.setObjetoVOAntesAlteracao(new MovProdutoVO());
            }
            registrarLogObjetoVO(movProdutoVO, movProdutoVO.getCodigo(), "MOVPRODUTO", getCliente().getPessoa().getCodigo());
        } catch (Exception e) {
            registrarLogErroObjetoVO("MOVPRODUTO", movProdutoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE MOVPRODUTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
        }
    }

    public void acaoLancarAtestado() {
        limparMsg();
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setErro(false);
                setSucesso(false);
                if (!isEdicaoDeAtestado()) {
                    // Valida se o produto foi informado na tela de Atestado de Aptidão Física
                    if (UteisValidacao.emptyNumber(getAtestado().getCodigo())) {
                        throw new Exception(getMensagemInternalizacao("produto_atestado_aptidao_fisica_nao_informado"));
                    }
                    setAtestado(getFacade().getProduto().consultarPorChavePrimaria(getAtestado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    UsuarioVO usuarioResponsavel = auto.getUsuario();
                    usuarioResponsavel.setUserOamd(getUsuarioLogado().getUserOamd());
                    VendaAvulsaVO vendaAvulsaVO = getFacade().getVendaAvulsa().gerarVendaAtestado(getCliente(), getDataInicio(), getDataFinal(), getAtestado(), getAtestado().getValorFinal(), usuarioResponsavel, getEmpresaLogado());
                    ArquivoVO arquivoVO = gerarArquivo();
                    AtestadoVO atestadoVO = getFacade().getAtestado().incluirAtestado(vendaAvulsaVO, arquivoVO, getObservacao(), getParqPositivo(), 0);
                    setAtestadoVO(atestadoVO);
                    setEdicaoDeAtestado(true);
                    setApresentarBotoes(false);
                    gerarLog(null, vendaAvulsaVO);
                    // LOG - FIM
                } else {
                    MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(getAtestadoVO().getMovProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    movProdutoVO.setObjetoVOAntesAlteracao(movProdutoVO.getClone(true));
                    movProdutoVO.setDataInicioVigencia(getDataInicio());
                    movProdutoVO.setDataFinalVigencia(getDataFinal());
                    getFacade().getMovProduto().alterar(movProdutoVO);
                    getAtestadoVO().setObservacao(getObservacao());
                    getAtestadoVO().setParqPositivo(getParqPositivo());
                    getFacade().getAtestado().alterar(getAtestadoVO());
                    gerarLog(movProdutoVO, null);
                }
                setExisteArquivo(getAtestadoVO().getArquivo().getCodigo() != null && getAtestadoVO().getArquivo().getCodigo() != 0);

                getCliente().setParqPositivo(getParqPositivo());
                getFacade().getCliente().alterarParqCliente(getCliente());

                setSucesso(true);
                TreinoWSConsumer.atualizarParq(getKey(), getCliente().getCodigo(), getCliente().isParqPositivo());
                setMensagemID("msg_dados_gravados");
                setMsgAlert("fireElementFromParent('form:btnAtualizaCliente');");
                ((ClienteControle) getControlador(ClienteControle.class)).obterListaProdutoComValidadeCliente();

                getFacade().getClienteMensagem().processarProdutoAtestado(getCliente().getCodigo(), getAtestado(),
                        getDataFinal(), getUsuario());
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        try {
            validar();
            auto.autorizar("Lançamento de atestado de aptidão física", "LancamentoAtestadoAptidaoFisica",
                    "Você precisa da permissão \"Lançamento de atestado de aptidão física\"",
                    "panelMensagem,pnlUpload,panelBotoes", listener);
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada(ex);
            setSucesso(false);
        }
    }

    private ArquivoVO gerarArquivo() throws Exception {
        if (getArquivoAtestado() != null) {
            ArquivoVO arquivoVO = new ArquivoVO();
            arquivoVO.setPessoa(getCliente().getPessoa());
            arquivoVO.setExtensao(getExtensaoArquivoAtestado());
            arquivoVO.setTipo("APTIDÃO FÍSICA");

            getFacade().getArquivo().incluir(arquivoVO);

            MidiaService.getInstance().uploadObjectWithExtension(getKey(),
                    MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO,
                    arquivoVO.getCodigo().toString(),
                    getArquivoAtestado(),
                    getExtensaoArquivoAtestado());

            System.out.println("Arquivo copiado com sucesso!");
            setMensagemID("msg_upload_arquivo");
            return arquivoVO;
        } else {
            return new ArquivoVO();
        }
    }

    public void upload(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        if (item.getFile().length() > 512000) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 512KB");
            throw new ConsistirException("Tamanho Superior a 512KB");
        }

        setArquivoAtestado(item.getFile());
        String[] partes = item.getFileName().split("\\.");
        setExtensaoArquivoAtestado("." + partes[partes.length - 1]);

        setSucesso(true);
        setErro(false);
        setMensagem("Arquivo enviado com sucesso");
        setMensagemDetalhada("", "");
    }

    public void prepararEdicaoAtestado(ActionEvent actionEvent) throws Exception {
        setSucesso(false);
        setErro(false);
        setMensagem("");
        setMensagemDetalhada("", "");

        MovProdutoVO movProduto = (MovProdutoVO) actionEvent.getComponent().getAttributes().get("movProdutoVO");
        movProduto.setObjetoVOAntesAlteracao(movProduto.getClone(true));
        setDataInicio(movProduto.getDataInicioVigencia());
        setDataFinal(movProduto.getDataFinalVigencia());
        setNrDias((int) Uteis.nrDiasEntreDatasSemHoraZerada(getDataInicio(), getDataFinal()));

        setCliente((ClienteVO) actionEvent.getComponent().getAttributes().get("clienteVO"));

        setAtestadoVO(getFacade().getAtestado().consultarPorMovProduto(movProduto.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        setExisteArquivo(getAtestadoVO().getArquivo().getCodigo() != null && getAtestadoVO().getArquivo().getCodigo() != 0);


        setObservacao(atestadoVO.getObservacao());
        setParqPositivo(atestadoVO.getParqPositivo());
        setAtestado(movProduto.getProduto());
    }

    public void prepararExcluirAtestado() throws Exception {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        setMsgAlert("");
        auto.setMensagemDetalhada("", "");
        MovProdutoVO mp = (MovProdutoVO) context().getExternalContext().getRequestMap().get("movProduto");
        if (mp != null) {
            movProdutoExclusao = mp;
        }
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                EstornoMovProdutoVO estornoMovProdutoVO = new EstornoMovProdutoVO();
                estornoMovProdutoVO.setMovProdutoVO(movProdutoExclusao);
                estornoMovProdutoVO.getListaMovProduto().add(movProdutoExclusao);
                if (estornoMovProdutoVO.getMovProdutoVO().getCodigo() > 0) {
                    MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorMovProduto(estornoMovProdutoVO.getMovProdutoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    getFacade().getMovProduto().estornarMovProduto(estornoMovProdutoVO, movParcela, getKey());
                }
                TelaClienteControle telaClienteControle = (TelaClienteControle) getControlador(TelaClienteControle.class);
                ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
                clienteControle.pegarClienteTelaCliente();
                setCliente(clienteControle.getClienteVO());
                AtestadoVO atestadoT = getFacade().getAtestado().consultarUltimoAtestado(getCliente().getPessoa());
                getCliente().setParqPositivo(atestadoT.getParqPositivo());
                getFacade().getCliente().alterarParqCliente(getCliente());
                getFacade().getClienteMensagem().processarProdutoAtestado(getCliente().getCodigo(),
                        movProdutoExclusao.getProduto(), null, null);
                movProdutoExclusao = new MovProdutoVO();
                montarSucessoGrowl("Produto excluído com sucesso!");
                setMsgAlert(getMensagemNotificar());
                telaClienteControle.setCarregarFinanceiro(true);

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Exclusão de atestado de aptidão física", "ExclusaoAtestadoAptidaoFisica",
                "Você precisa da permissão \"Exclusão de atestado de aptidão física\"",
                "panelAutorizacaoFuncionalidade,form", listener);

    }

    public Boolean getApresentarUploadArquivo() {
        return (!isEdicaoDeAtestado() || !isExisteArquivo()) && getApresentarBotoes();
    }

    public String getDownloadAtestado() {
        return getAtestadoVO().getArquivo().getNomeArquivoGerado();
    }

    public void downloadAtestadoListener(ActionEvent actionEvent) {
        try {
            byte[] b = MidiaService.getInstance().downloadObjectWithExtensionAsByteArray(getKey(), MidiaEntidadeEnum.ANEXO_ATESTADO_APTIDAO, getAtestadoVO().getArquivo().getCodigo().toString(), getAtestadoVO().getArquivo().getExtensao(), null);
            if (b == null || b.length == 0) {
                throw new ConsistirException("Não foi possível realizar o download do arquivo");
            }
            HttpServletResponse res = (HttpServletResponse) context().getExternalContext().getResponse();
            ServletOutputStream out = res.getOutputStream();

            res.setHeader("Content-disposition", "attachment;filename=\"" + getAtestadoVO().getArquivo().getNomeArquivoGerado() + "\"");
            res.setContentLength(b.length);
            res.setContentType("application/octet-stream");

            out.write(b);
            out.flush();
            out.close();

            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Integer getNrDias() {
        if(nrDias == null){
            nrDias = 0;
        }
        return nrDias;
    }

    public void setNrDias(Integer nrDias) {
        this.nrDias = nrDias;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public ProdutoVO getAtestado() {
        return atestado;
    }

    public void setAtestado(ProdutoVO atestado) {
        this.atestado = atestado;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public List<SelectItem> getProdutosAtestados() {
        return produtosAtestados;
    }

    public void setProdutosAtestados(List<SelectItem> produtosAtestados) {
        this.produtosAtestados = produtosAtestados;
    }

    public Boolean getParqPositivo() {
        return parqPositivo;
    }

    public void setParqPositivo(Boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public File getArquivoAtestado() {
        return arquivoAtestado;
    }

    public void setArquivoAtestado(File arquivoAtestado) {
        this.arquivoAtestado = arquivoAtestado;
    }

    public String getExtensaoArquivoAtestado() {
        return extensaoArquivoAtestado;
    }

    public void setExtensaoArquivoAtestado(String extensaoArquivoAtestado) {
        this.extensaoArquivoAtestado = extensaoArquivoAtestado;
    }

    public AtestadoVO getAtestadoVO() {
        return atestadoVO;
    }

    public void setAtestadoVO(AtestadoVO atestadoVO) {
        this.atestadoVO = atestadoVO;
    }

    public boolean isEdicaoDeAtestado() {
        return edicaoDeAtestado;
    }

    public void setEdicaoDeAtestado(boolean edicaoDeAtestado) {
        this.edicaoDeAtestado = edicaoDeAtestado;
    }

    public boolean isExisteArquivo() {
        return existeArquivo;
    }

    public void setExisteArquivo(boolean existeArquivo) {
        this.existeArquivo = existeArquivo;
    }

    public String getOnComplete() {
        return getMsgAlert();
    }

    public String getOnCompleteImprimir() {
        if (onCompleteImprimir == null) {
            onCompleteImprimir = "";
        }
        return onCompleteImprimir;
    }

    public void setOnCompleteImprimir(String onCompleteImprimir) {
        this.onCompleteImprimir = onCompleteImprimir;
    }

    public void imprimirParQ() {
        try {
            setOnCompleteImprimir("");
            String assinaturaDigital = getFacade().getPessoa().obterAssinaturaBiometriaDigital(getCliente().getPessoa().getCodigo());
            String url = getUrlTreino() + "/prest/avaliacao/" + getKey() + "/imprimirParq?avaliacao=" + getAtestadoVO().getAvaliacaoFisicaTW() + "&assinaturaDigital=" + assinaturaDigital;
            String resposta = ExecuteRequestHttpService.executeRequestGET(url, new HashMap<String, String>());
            String pdf = new JSONObject(resposta).getString("sucesso");
            setOnCompleteImprimir("window.open('" + getUrlTreino() + "/" + pdf + "', '_blank');");
        } catch (Exception ignored) {
            setOnCompleteImprimir("");
        }

    }

}

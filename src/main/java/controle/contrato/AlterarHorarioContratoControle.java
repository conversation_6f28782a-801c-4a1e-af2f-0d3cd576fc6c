package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import servicos.impl.dcc.base.DCCAttEnum;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AlterarHorarioContratoControle extends SuperControle {

    private ContratoVO contratoAntigo;
    private ContratoVO contratoNovo;
    private ContratoOperacaoVO operacaoAlterarHorario;
    private PlanoVO novoPlanoHorario;
    private UsuarioVO responsavelOperacao;
    private UsuarioVO responsavelLiberacao;
    private Boolean planoVencido;
    private Boolean apresentarBotao;
    private Boolean apresentarDadosNovoHorario;
    private Boolean mostrarRichModalPanelConfirmacaoAlterarHorario;
    private Boolean abrirRichConfirmacaoLiberacao;
    private Double valorAlteracaoHorarioNovo;
    private Double valorDiaHorarioNovo;
    private Double valorHorarioNovo;
    private Double valorTotalAlteracaoHorario;
    private Double valorTotalAlteracaoValor;
    private Integer nrDiasContrato;
    private Integer nrDiasContratoUtilizado;
    private Integer restanteDiasContrato;
    protected List<ContratoVO> listaContrato;
    private boolean alterarValor;
    private boolean alteracaoAplicada;

    private boolean apresentarDataOperacao = false;
    private boolean apresentarDataOperacaoSelecionado = false;
    private Date dataOperacao = Calendario.hoje();
    private boolean jaUtilizouTodosOsDiasUteis = false;


    public AlterarHorarioContratoControle() {
        inicializarFacades();
        novo();
    }

    public void inicializarUsuarioLogado() {
        try {
            if (getUsuarioLogado().getCodigo() != 0) {
                getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
                getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
                getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
                getResponsavelLiberacao().setCodigo(getUsuarioLogado().getCodigo());
                getResponsavelLiberacao().setUsername(getUsuarioLogado().getUsername());
                getResponsavelLiberacao().setUserOamd(getUsuarioLogado().getUserOamd());
            }
        } catch (Exception e) {
            getResponsavelOperacao().setCodigo(0);
            getResponsavelOperacao().setUsername("");
        }
    }

    public void novo() {
        notificarRecursoEmpresa(RecursoSistema.ALTERAR_HORARIO_CLIENTE);
        try {
            setProcessandoOperacao(false);
            setListaContrato(new ArrayList<>());
            setPlanoVencido(false);
            setApresentarDadosNovoHorario(false);
            setMostrarRichModalPanelConfirmacaoAlterarHorario(false);
            setApresentarBotao(true);
            setAbrirRichConfirmacaoLiberacao(false);
            setValorAlteracaoHorarioNovo(0.0);
            setValorDiaHorarioNovo(0.0);
            setValorHorarioNovo(0.0);
            setValorTotalAlteracaoHorario(0.0);
            setNrDiasContrato(0);
            setNrDiasContratoUtilizado(0);
            setRestanteDiasContrato(0);
            setResponsavelOperacao(new UsuarioVO());
            setResponsavelLiberacao(new UsuarioVO());
            setEmpresa(new EmpresaVO());
            obterCliente();
            montarNovoPlanoHorario();
            inicializarUsuarioLogado();
            alteracaoAplicada = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        if (this.contratoNovo != null && this.contratoNovo.isVendaCreditoTreino()){
            MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            control.init("Alterar Horário","Operação não permitida para planos 'Venda Crédito de Treino'.",this, "OK", "", "");
            setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        }else{
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            setMsgAlert(clienteControle.getUrlPopup());
        }
    }

    public List<MovParcelaVO> getContratoComParcelasEnvioDeRemessa() throws Exception {
        StringBuilder contratosRemessa = new StringBuilder();
        try {
            if (getListaContrato().size() > 1) {
                for (int pos = 0; pos < getListaContrato().size(); pos++) {
                    contratosRemessa.append(getListaContrato().get(pos).getCodigo());
                    if (pos < (getListaContrato().size() - 1)) {
                        contratosRemessa.append(",");
                    }
                }
            } else {
                contratosRemessa.append(getListaContrato().get(0).getCodigo());
            }
            return getFacade().getMovParcela().consultarPorContratoParcelasEmRemessa(contratosRemessa.toString());
        } finally {
            contratosRemessa = null;
        }
    }


    public void gravar() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);

        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                setProcessandoOperacao(true);
                inicializarDadosOperacaoAlterarHorario();
                operacaoAlterarHorario.setResponsavelLiberacao(auto.getUsuario());
                getFacade().getContratoOperacao().incluirOperacaoAlterarHorario(operacaoAlterarHorario, contratoAntigo, contratoNovo, getValorTotalAlteracaoHorario(), false);

                setMensagemDetalhada("msg_dados_gravados", "");
                setApresentarBotao(false);
                setErro(false);
                setSucesso(true);
                setAbrirRichConfirmacaoLiberacao(false);
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMostrarRichModalPanelConfirmacaoAlterarHorario(true);
                setApresentarBotao(true);
                setErro(true);
                setSucesso(false);
                setMensagemDetalhada("msg_erro", e.getMessage());
                setProcessandoOperacao(false);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };
        listener.setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});");

        limparMsg();
        try {
            auto.autorizar("Confirmação de Alteração de Horário", "AlterarHorario_Autorizar",
                    "Você precisa da permissão \"3.15 - Alterar Horário - Autorizar\"",
                    "form", listener);

        } catch (Exception e) {
            montarErro(e);
        }

    }

    public void autorizarApresentarDataOperacao(){
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setApresentarDataOperacao(true);
                setApresentarDataOperacaoSelecionado(true);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setApresentarDataOperacao(false);
                setMensagemDetalhada(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                setApresentarDataOperacao(false);
            }
        };
        limparMsg();
        try {
            setDataOperacao(Calendario.hoje());
            calcularAlteracaoDataOperacao();
            setApresentarDataOperacaoSelecionado(false);
            if (isApresentarDataOperacao()) {
                auto.autorizar("Autorização Alterar Data da Operação do Horário", "AlterarHorario_AlterarDatadaOperacao",
                        "Você precisa da permissão \"3.33 - Alterar Horário - Alterar Data da Operação\"",
                        "form", listener);
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void alterarDataOperacao(ActionEvent actionEvent) {
        try {
            calcularAlteracaoDataOperacao();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void calcularAlteracaoDataOperacao() throws Exception {
        setNrDiasContrato(getFacade().getZWFacade().obterNrDiasContrato(getContratoAntigo()));
        setNrDiasContratoUtilizado(getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(getContratoAntigo(), getDataOperacao()));
        setRestanteDiasContrato(getNrDiasContrato() - getNrDiasContratoUtilizado());
        setJaUtilizouTodosOsDiasUteis(this.restanteDiasContrato <= 0);

        for (PlanoHorarioVO planoHorarioVO : novoPlanoHorario.getPlanoHorarioVOs()) {
            if (planoHorarioVO.getHorario().getHorarioEscolhida()) {
                request().setAttribute("planoHorario", planoHorarioVO);
                selecionarHorario();
            }
        }
    }

    public void inicializarDadosOperacaoAlterarHorario() {
        setOperacaoAlterarHorario(new ContratoOperacaoVO());
        getOperacaoAlterarHorario().setContrato(getContratoNovo().getCodigo());
        getOperacaoAlterarHorario().setDataInicioEfetivacaoOperacao(getDataOperacao());
        getOperacaoAlterarHorario().setDataFimEfetivacaoOperacao(getDataOperacao());
        getOperacaoAlterarHorario().setDataOperacao(getDataOperacao());
        getOperacaoAlterarHorario().setDescricaoCalculo("Alteração de Horário \n\r" +
                "O aluno tinha para esse contrato o horário : " + contratoAntigo.getContratoHorario().getHorario().getDescricao() + "\r\n " +
                "e mudou para o horário: " + contratoNovo.getPlanoHorario().getHorario().getDescricao());
        
        if(alteracaoAplicada){
        	getOperacaoAlterarHorario().setDescricaoCalculo(getOperacaoAlterarHorario().getDescricaoCalculo() + "\n (VALOR ALTERADO MANUALMENTE)");
        }
        getOperacaoAlterarHorario().setObservacao("");
        getOperacaoAlterarHorario().setTipoOperacao("AH");
        if (getOperacaoAlterarHorario().getResponsavelLiberacao().getCodigo() != 0) {
            getOperacaoAlterarHorario().setResponsavel(getResponsavelLiberacao());
        } else {
            getOperacaoAlterarHorario().setResponsavel(getResponsavelOperacao());
        }
        if (getApresentarDepositoEmConta()) {
            getOperacaoAlterarHorario().setOperacaoPaga(false);


        } else {
            getOperacaoAlterarHorario().setOperacaoPaga(true);
        }
    }

    public void obterCliente() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle != null) {
            clienteControle.pegarClienteTelaCliente();
            setContratoAntigo(getFacade().getContrato().consultarPorChavePrimaria(clienteControle.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            getListaContrato().add(contratoAntigo);
            setContratoNovo((ContratoVO) getContratoAntigo().getClone(true));
            contratoNovo.inicializarDadosPlanoCondicaoPagamento();
            contratoNovo.inicializarDadosPlanoDuracao();
            contratoNovo.setPlanoHorario(new PlanoHorarioVO());
            setNrDiasContrato(getFacade().getZWFacade().obterNrDiasContrato(getContratoAntigo()));
            setNrDiasContratoUtilizado(getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(getContratoAntigo(), getDataOperacao()));
            setRestanteDiasContrato(getNrDiasContrato() - getNrDiasContratoUtilizado());
            setJaUtilizouTodosOsDiasUteis(this.restanteDiasContrato <= 0);

            List<MovParcelaVO> parcelasEmRemessas = new ArrayList<MovParcelaVO>();
            for (MovParcelaVO parcelaVO : getContratoComParcelasEnvioDeRemessa()) {
                RemessaItemVO itemRemessa = getFacade().getRemessaItem().consultarPorParcelaPorCodigoRemessa(parcelaVO.getCodigo(), parcelaVO.getRemessa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!itemRemessa.getProps().containsKey(DCCAttEnum.StatusVenda.name())) {
                    parcelasEmRemessas.add(parcelaVO);
                }
            }
            clienteControle.setListaParcelasRemessa(parcelasEmRemessas);

            if (clienteControle.getListaParcelasRemessa().isEmpty()){
                clienteControle.setUrlPopup("abrirPopup('alterarHorarioContrato.jsp', 'AlterarHorarioContratoControle', 830, 690);");
            }else {
                clienteControle.setUrlPopup("Richfaces.showModalPanel('panelParcelasRemessas')");

            }
        } else {
            throw new Exception("Não foi possível inicializar os dados do Contrato.");
        }
    }

    public void montarNovoPlanoHorario() {
        setNovoPlanoHorario(getContratoAntigo().getPlano());
        apresentarHorarioAntigoEscolhido();
        setPlanoVencido(false);
    }

    public void selecionarHorario() throws Exception {

        PlanoHorarioVO obj = (PlanoHorarioVO) context().getExternalContext().getRequestMap().get("planoHorario");
        for (PlanoHorarioVO planoHorario : getNovoPlanoHorario().getPlanoHorarioVOs()) {
            if (!planoHorario.getCodigo().equals(obj.getCodigo())) {
                planoHorario.getHorario().setHorarioEscolhida(false);
            } else {
                if (planoHorario.getHorario().getHorarioEscolhida()) {
                    setApresentarDadosNovoHorario(true);
                    contratoNovo.setPlanoHorario(obj);
                    getFacade().getContrato().calcularValorContrato(contratoNovo, true, true);
                    setValorAlteracaoHorarioNovo(Uteis.arredondarForcando2CasasDecimais(contratoNovo.getValorBaseCalculo() - contratoAntigo.getValorBaseCalculo()));
                    setValorDiaHorarioNovo(Uteis.arredondarForcando2CasasDecimais(getValorAlteracaoHorarioNovo() / getNrDiasContrato()));
                    if(isJaUtilizouTodosOsDiasUteis()) {
                        setValorHorarioNovo(0.0);
                        setValorTotalAlteracaoHorario(0.0);
                    } else {
                        setValorHorarioNovo(getValorDiaHorarioNovo() * getRestanteDiasContrato());
                        setValorTotalAlteracaoHorario(getValorHorarioNovo());
                    }
                    //colocando valores negativos
                    if (contratoNovo.getValorBaseCalculo() < contratoAntigo.getValorBaseCalculo()) {
                        setValorDiaHorarioNovo(getValorDiaHorarioNovo() * -1);
                        setValorHorarioNovo(getValorHorarioNovo() * -1);
                    }
                } else {
                    setValorAlteracaoHorarioNovo(0.0);
                    setValorDiaHorarioNovo(0.0);
                    setValorHorarioNovo(0.0);
                    setValorTotalAlteracaoHorario(0.0);
                    setApresentarDadosNovoHorario(false);
                    contratoNovo.setPlanoHorario(new PlanoHorarioVO());
                }
            }
        }
        setErro(false);
        setSucesso(false);
        setMensagemDetalhada("");
        setMensagemID("");
        setMensagem("");
    }

    public void autorizarLiberacao(){
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                setProcessandoOperacao(true);
                inicializarDadosOperacaoAlterarHorario();
                operacaoAlterarHorario.setResponsavelLiberacao(auto.getUsuario());
                getFacade().getContratoOperacao().incluirOperacaoAlterarHorario(operacaoAlterarHorario, contratoAntigo, contratoNovo, getValorTotalAlteracaoHorario(), true);

                setMensagemDetalhada("msg_dados_gravados", "");
                setApresentarBotao(false);
                setErro(false);
                setSucesso(true);
                setAbrirRichConfirmacaoLiberacao(false);
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setAbrirRichConfirmacaoLiberacao(true);
                setApresentarBotao(true);
                setErro(true);
                setSucesso(false);
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public void onFecharModalAutorizacao() {
                setAbrirRichConfirmacaoLiberacao(false);
            }
        };

        limparMsg();
        try {
            if (!getApresentarLiberacao()) {
                valorHorarioNovo = 0.00;
                alterarValor = true;
            }
            if (alterarValor) {
                valorTotalAlteracaoHorario = valorHorarioNovo;
                alterarValor = false;
            }
            if (!getAbrirRichConfirmacaoLiberacao()) {
                setResponsavelLiberacao(new UsuarioVO());
                if (getUsuarioLogado().getCodigo() != 0) {
                    getResponsavelLiberacao().setCodigo(getUsuarioLogado().getCodigo());
                    getResponsavelLiberacao().setUsername(getUsuarioLogado().getUsername());
                    getResponsavelLiberacao().setUserOamd(getUsuarioLogado().getUserOamd());
                }
            } else {
                if (contratoNovo.getPlanoHorario().getCodigo() == 0) {
                    setAbrirRichConfirmacaoLiberacao(false);
                    throw new Exception("Por favor escolha um novo horário");
                }
            }

            if (getAbrirRichConfirmacaoLiberacao()) {
                auto.autorizar("Confirmação de Liberação Alterar Horário", "AlterarHorario_Autorizar",
                        "Você precisa da permissão \"3.15 - Alterar Horário - Autorizar\"",
                        "form", listener);
            }

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void alterarValor(){
    	limparMsg();
    	if(alterarValor){
    		if (contratoNovo.getPlanoHorario().getCodigo() == 0) {
                setMensagemDetalhada("msg_erro", "Por favor escolha um novo horário");
                setErro(true);
                setSucesso(false);
                setAbrirRichConfirmacaoLiberacao(false);
                alterarValor = false;
            }else{
           	 abrirRichConfirmacaoLiberacao = false;	 
            }	
    	}else{
    		alteracaoAplicada = true;
    		valorTotalAlteracaoHorario = valorHorarioNovo;
    	}
    }

    public void aplicarAlterarValor() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        final Double valorTotalAlteracaoValorSelecionado = valorTotalAlteracaoValor;
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                alteracaoAplicada = true;
                valorTotalAlteracaoHorario = valorTotalAlteracaoValorSelecionado;
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                alteracaoAplicada = false;
                valorTotalAlteracaoValor = 0.0;
                setMensagemDetalhada(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                alteracaoAplicada = false;
                valorTotalAlteracaoValor = 0.0;
            }
        };
        limparMsg();
        try {
            if (UteisValidacao.emptyNumber(valorTotalAlteracaoValor)) {
                throw new ConsistirException("Para valor 0,00 utilize a Liberação!");
            }
            auto.autorizar("Autorização Alterar Horário", "AlterarHorario_Autorizar",
                    "Você precisa da permissão \"Alterar Horário - Autorizar\"",
                    "form", listener);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }


    public Boolean getApresentarLiberacao() {
        return !getApresentarDepositoEmConta();
    }

    public void consultarResponsavel() {
        try {
            setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void apresentarHorarioAntigoEscolhido() {
        Iterator i = novoPlanoHorario.getPlanoHorarioVOs().iterator();
        while (i.hasNext()) {
            PlanoHorarioVO obj = (PlanoHorarioVO) i.next();
            if (obj.getHorario().getCodigo().equals(contratoAntigo.getContratoHorario().getHorario().getCodigo())) {
                obj.setApresentarValor(true);
            }
        }
    }

    public ContratoVO getContratoAntigo() {
        return contratoAntigo;
    }

    public void setContratoAntigo(ContratoVO contratoAntigo) {
        this.contratoAntigo = contratoAntigo;
    }

    public ContratoVO getContratoNovo() {
        return contratoNovo;
    }

    public void setContratoNovo(ContratoVO contratoNovo) {
        this.contratoNovo = contratoNovo;
    }

    public UsuarioVO getResponsavelOperacao() {
        return responsavelOperacao;
    }

    public void setResponsavelOperacao(UsuarioVO responsavelOperacao) {
        this.responsavelOperacao = responsavelOperacao;
    }

    public PlanoVO getNovoPlanoHorario() {
        return novoPlanoHorario;
    }

    public void setNovoPlanoHorario(PlanoVO novoPlanoHorario) {
        this.novoPlanoHorario = novoPlanoHorario;
    }


    public Double getValorAlteracaoHorarioNovo() {
        return valorAlteracaoHorarioNovo;
    }

    public void setValorAlteracaoHorarioNovo(Double valorAlteracaoHorarioNovo) {
        this.valorAlteracaoHorarioNovo = valorAlteracaoHorarioNovo;
    }

    public Integer getNrDiasContrato() {
        return nrDiasContrato;
    }

    public void setNrDiasContrato(Integer nrDiasContrato) {
        this.nrDiasContrato = nrDiasContrato;
    }

    public Integer getNrDiasContratoUtilizado() {
        return nrDiasContratoUtilizado;
    }

    public void setNrDiasContratoUtilizado(Integer nrDiasContratoUtilizado) {
        this.nrDiasContratoUtilizado = nrDiasContratoUtilizado;
    }

    public Integer getRestanteDiasContrato() {
        return restanteDiasContrato;
    }

    public void setRestanteDiasContrato(Integer restanteDiasContrato) {
        this.restanteDiasContrato = restanteDiasContrato;
    }

    public Boolean getApresentarBotao() {
        return apresentarBotao;
    }

    public void setApresentarBotao(Boolean apresentarBotao) {
        this.apresentarBotao = apresentarBotao;
    }

    public List<ContratoVO> getListaContrato() {
        return listaContrato;
    }

    public void setListaContrato(List<ContratoVO> listaContrato) {
        this.listaContrato = listaContrato;
    }

    public Double getValorDiaHorarioNovo() {
        return valorDiaHorarioNovo;
    }

    public void setValorDiaHorarioNovo(Double valorDiaHorarioNovo) {
        this.valorDiaHorarioNovo = valorDiaHorarioNovo;
    }

    public Double getValorHorarioNovo() {
        return valorHorarioNovo;
    }

    public void setValorHorarioNovo(Double valorHorarioNovo) {
        this.valorHorarioNovo = valorHorarioNovo;
    }

    public Double getValorTotalAlteracaoHorario() {
        return valorTotalAlteracaoHorario;
    }

    public void setValorTotalAlteracaoHorario(Double valorTotalAlteracaoHorario) {
        this.valorTotalAlteracaoHorario = valorTotalAlteracaoHorario;
    }

    public Boolean getApresentarDadosNovoHorario() {
        return apresentarDadosNovoHorario;
    }

    public void setApresentarDadosNovoHorario(Boolean apresentarDadosNovoHorario) {
        this.apresentarDadosNovoHorario = apresentarDadosNovoHorario;
    }

    public Boolean getMostrarRichModalPanelConfirmacaoAlterarHorario() {
        return mostrarRichModalPanelConfirmacaoAlterarHorario;
    }

    public void setMostrarRichModalPanelConfirmacaoAlterarHorario(Boolean mostrarRichModalPanelConfirmacaoAlterarHorario) {
        this.mostrarRichModalPanelConfirmacaoAlterarHorario = mostrarRichModalPanelConfirmacaoAlterarHorario;
    }

    public Boolean getPlanoVencido() {
        return planoVencido;
    }

    public void setPlanoVencido(Boolean planoVencido) {
        this.planoVencido = planoVencido;
    }

    public Boolean getApresentarDepositoEmConta() {
        return contratoAntigo.getValorBaseCalculo() > contratoNovo.getValorBaseCalculo() && !jaUtilizouTodosOsDiasUteis;
    }

    public Boolean getApresentarParcela() {
        return contratoNovo.getValorBaseCalculo() >= contratoAntigo.getValorBaseCalculo() || jaUtilizouTodosOsDiasUteis ;
    }

    public ContratoOperacaoVO getOperacaoAlterarHorario() {
        return operacaoAlterarHorario;
    }

    public void setOperacaoAlterarHorario(ContratoOperacaoVO operacaoAlterarHorario) {
        this.operacaoAlterarHorario = operacaoAlterarHorario;
    }

    public UsuarioVO getResponsavelLiberacao() {
        return responsavelLiberacao;
    }

    public void setResponsavelLiberacao(UsuarioVO responsavelLiberacao) {
        this.responsavelLiberacao = responsavelLiberacao;
    }

    public Boolean getAbrirRichConfirmacaoLiberacao() {
        return abrirRichConfirmacaoLiberacao;
    }

    public void setAbrirRichConfirmacaoLiberacao(Boolean abrirRichConfirmacaoLiberacao) {
        this.abrirRichConfirmacaoLiberacao = abrirRichConfirmacaoLiberacao;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        contratoAntigo = null;
        contratoNovo = null;
        novoPlanoHorario = null;
        responsavelOperacao = null;
        planoVencido = null;
        operacaoAlterarHorario = null;
        apresentarBotao = null;
        apresentarDadosNovoHorario = null;
        mostrarRichModalPanelConfirmacaoAlterarHorario = null;
        valorAlteracaoHorarioNovo = null;
        valorDiaHorarioNovo = null;
        valorHorarioNovo = null;
        valorTotalAlteracaoHorario = null;
        nrDiasContrato = null;
        nrDiasContratoUtilizado = null;
        restanteDiasContrato = null;
        listaContrato = new ArrayList<>();
    }

	public void setAlterarValor(boolean alterarValor) {
		this.alterarValor = alterarValor;
	}

	public boolean getAlterarValor() {
		return alterarValor;
	}

	public void setValorTotalAlteracaoValor(Double valorTotalAlteracaoValor) {
		this.valorTotalAlteracaoValor = valorTotalAlteracaoValor;
	}

	public Double getValorTotalAlteracaoValor() {
		return valorTotalAlteracaoValor;
	}
	
    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public boolean isApresentarDataOperacao() {
        return apresentarDataOperacao;
    }

    public void setApresentarDataOperacao(boolean apresentarDataOperacao) {
        this.apresentarDataOperacao = apresentarDataOperacao;
    }

    public boolean isApresentarDataOperacaoSelecionado() {
        return apresentarDataOperacaoSelecionado;
    }

    public void setApresentarDataOperacaoSelecionado(boolean apresentarDataOperacaoSelecionado) {
        this.apresentarDataOperacaoSelecionado = apresentarDataOperacaoSelecionado;
    }

    public boolean isJaUtilizouTodosOsDiasUteis() {
        return jaUtilizouTodosOsDiasUteis;
    }

    public void setJaUtilizouTodosOsDiasUteis(boolean jaUtilizouTodosOsDiasUteis) {
        this.jaUtilizouTodosOsDiasUteis = jaUtilizouTodosOsDiasUteis;
    }
}

package controle.contrato;

import java.util.Date;
import negocio.facade.jdbc.contrato.ContratoOperacao;
import negocio.comuns.utilitarias.*;
import negocio.comuns.contrato.*;

import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;

import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.SuperControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * contratoOperacaoForm.jsp contratoOperacaoCons.jsp) com as funcionalidades da classe <code>ContratoOperacao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see ContratoOperacao
 * @see ContratoOperacaoVO
 */
public class ContratoOperacaoControle extends SuperControle {

    private ContratoOperacaoVO contratoOperacaoVO;
    /**
     * Interface <code>ContratoOperacaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public ContratoOperacaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>ContratoOperacao</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setContratoOperacaoVO(new ContratoOperacaoVO());
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>ContratoOperacao</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        ContratoOperacaoVO obj = (ContratoOperacaoVO) context().getExternalContext().getRequestMap().get("contratoOperacao");
        obj.setNovoObj(new Boolean(false));
        setContratoOperacaoVO(obj);
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>ContratoOperacao</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (contratoOperacaoVO.isNovoObj().booleanValue()) {
                getFacade().getContratoOperacao().incluir(contratoOperacaoVO);
            } else {
                getFacade().getContratoOperacao().alterar(contratoOperacaoVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ContratoOperacaoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoOperacao().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("contrato")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoOperacao().consultarPorContrato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoOperacao")) {
                objs = getFacade().getContratoOperacao().consultarPorTipoOperacao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataOperacao")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoOperacao().consultarPorDataOperacao(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("responsavel")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContratoOperacao().consultarPorResponsavel(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

   /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ContratoOperacaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getContratoOperacao().excluir(contratoOperacaoVO);
            setContratoOperacaoVO(new ContratoOperacaoVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("contrato", "Contrato"));
        itens.add(new SelectItem("tipoOperacao", "Tipo Operação"));
        itens.add(new SelectItem("dataOperacao", "Data Operação"));
        itens.add(new SelectItem("responsavel", "Responsável"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }
}
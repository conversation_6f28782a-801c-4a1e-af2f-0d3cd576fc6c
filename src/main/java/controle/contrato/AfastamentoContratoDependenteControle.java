/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ContratoDependenteVO;
import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;


public class AfastamentoContratoDependenteControle extends SuperControle {

    private AfastamentoContratoDependenteVO afastamentoVO;

    private List<ContratoVO> listaContratoVOs;
    private ContratoVO contratoOriginalVO;

    private Boolean apresentarBotoes;
    private Boolean abrirRichModalDeConfirmacao;
    private Integer carencia;
    private String aviso;
    private String mensagemCarrencia;
    private String nomeArquivoComprovanteOperacao;

    private ContratoDependenteVO contratoDependenteVO;
    private List<SelectItem> listSelectItemTipoAfastamento;
    private List<SelectItem> listSelectItemJustificativa;

    private List<AfastamentoContratoDependenteVO> listaAfastamentos;

    public AfastamentoContratoDependenteControle() {
        consultarCarencia();
    }


    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }


    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "DIASCARENCIA";
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("DIASCARENCIA"));
        loginControle.consultarLogObjetoSelecionado("DIASCARENCIA", afastamentoVO.getContratoVO().getCodigo(), 0);
    }

    public void novo() throws Exception {
        try {
            setAfastamentoVO(new AfastamentoContratoDependenteVO());

            setProcessandoOperacao(false);
            setAviso("");
            setMensagemCarrencia(null);
            limparMsg();
            setMsgAlert("");

            setListaContratoVOs(new ArrayList<>());
            setListaAfastamentos(new ArrayList<>());

            montarDadosContratoDependente();
            setAbrirRichModalDeConfirmacao(false);

            afastamentoVO.validarRegrasAfastamento();

            List<ContratoDependenteVO> cdVOS = getFacade().getContratoDependente().findAllByCliente(afastamentoVO.getContratoDependenteVO().getCliente(), 999);
            Ordenacao.ordenarListaReverse(cdVOS, "dataFinalAjustada");
            if (cdVOS.size() > 0 && !contratoDependenteVO.getCodigo().equals(cdVOS.get(0).getCodigo())) {
                throw new ConsistirException("Só é possível lançar um afastamento para o contrato mais recente do cliente.");
            }

            setMsgAlert("Richfaces.showModalPanel('pnlAfastamentoDependente');");

            setMensagemID("msg_entre_dados");

            setApresentarBotoes(true);
            setMensagemDetalhada("");
        } catch (Exception e) {
            montarErro(e);
            setApresentarBotoes(false);
        }
    }

    public void listarAfastamentos() {
        try {
            setAviso("");
            limparMsg();
            setMsgAlert("");

            if (contratoDependenteVO == null) {
                throw new ConsistirException("Não foi possível inicializar os dados do Contrato.");
            }

            setListaAfastamentos(getFacade()
                    .getAfastamentoContratoDependente()
                    .consultarPorContratoDependente(contratoDependenteVO.getCodigo(), null, Uteis.NIVELMONTARDADOS_TODOS));
            Ordenacao.ordenarListaReverse(getListaAfastamentos(), "dataTermino");

            setMsgAlert("Richfaces.showModalPanel('pnlListaAfastamentos');");

            setApresentarBotoes(true);
            setMensagemDetalhada("");
        } catch (ConsistirException c) {
            setMensagemCarrencia(c.getMessage());
            setApresentarBotoes(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setApresentarBotoes(false);
            montarErro(e);
        }
    }

    public void montarDadosContratoDependente() throws Exception {
        if (contratoDependenteVO == null) {
            throw new ConsistirException("Não foi possível inicializar os dados do Contrato.");

        }
        ContratoVO contratoOriginal = getFacade().getContrato().consultarPorChavePrimaria(contratoDependenteVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        getListaContratoVOs().add(contratoOriginal);

        List<AfastamentoContratoDependenteVO> afastamentos = getFacade().getAfastamentoContratoDependente().consultarPorContratoDependente(contratoDependenteVO.getCodigo(), "CR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        int nrDiasUsados = 0;
        for (AfastamentoContratoDependenteVO afastamento : afastamentos) {
            nrDiasUsados += afastamento.getNrDias();
        }

        afastamentoVO.setContratoDependenteVO(contratoDependenteVO);
        afastamentoVO.setContratoVO(contratoOriginal);
        afastamentoVO.setNrDiasFeriasPermitido(contratoOriginal.getContratoDuracao().getCarencia());
        afastamentoVO.setNrDiasUsados(nrDiasUsados);
        afastamentoVO.setNrDiasRestam(afastamentoVO.getNrDiasFeriasPermitido() - afastamentoVO.getNrDiasUsados());

        contratoOriginalVO = contratoOriginal;
    }

    public void montarListaJustificativaOperacao() {
        try {
            montarDadosListaJustificativaOperacaoVOs(afastamentoVO.getTipoAfastamento());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void montarDadosListaJustificativaOperacaoVOs(String prm) throws Exception {
        List<JustificativaOperacaoVO> justificativas = consultarTipoJustificativaOperacaoPorTipo(prm);

        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem(0, ""));
        for (JustificativaOperacaoVO obj : justificativas) {
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListSelectItemJustificativa(objs);
    }

    public List<JustificativaOperacaoVO> consultarTipoJustificativaOperacaoPorTipo(String prm) throws Exception {
        return getFacade().getJustificativaOperacao().consultarPorTipoOperacao(prm, contratoOriginalVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public void validarDadosCarencia() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);

                if (auto.getUsuario() != null && UteisValidacao.emptyNumber(auto.getUsuario().getCodigo())) {
                    afastamentoVO.setResponsavelOperacao(auto.getUsuario());
                } else {
                    afastamentoVO.setResponsavelOperacao(getUsuarioLogado());
                }

                ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(contratoDependenteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                afastamentoVO.getContratoDependenteVO().setCliente(cliente);

                List<AfastamentoContratoDependenteVO> afastamentosLancados = getFacade().getAfastamentoContratoDependente().consultarPorContratoDependente(contratoDependenteVO.getCodigo(), "", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                afastamentoVO.validarPeriodoCarencia(afastamentosLancados);

                afastamentoVO.validarRegrasAfastamento();

                getFacade().getContratoDependente().incluirAfastamento(afastamentoVO);

                setMensagemDetalhada("");
                setMensagem("");
                setMensagemID("msg_dados_gravados");
                setApresentarBotoes(false);
                setErro(false);
                setSucesso(true);
                setExecutarAoCompletar(getMensagemNotificar() + ";Richfaces.hideModalPanel('pnlAfastamentoDependente');fireElement('form:btnAtualizaCliente');");

                afastamentoVO.setMensagemErro(false);
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                auto.setPedirPermissao(false);
                setApresentarBotoes(true);
                afastamentoVO.setMensagemErro(true);
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
            }
        };

        limparMsg();
        try {
            validarPeriodosAfastamento();
            validarPermissaoRetroativo();

            Date dataAnteriorOperacao = Uteis.obterDataAnterior(afastamentoVO.getDataInicio(), 1);

            Integer nrDiasContratos = (int) Uteis.nrDiasEntreDatas(contratoDependenteVO.getDataInicio(), contratoDependenteVO.getDataFinalAjustada());
            Integer diasUtilizados = (int) Uteis.nrDiasEntreDatas(contratoDependenteVO.getDataInicio(), dataAnteriorOperacao);
            int diasRestante = nrDiasContratos - diasUtilizados;

            if (diasRestante <= 0) {
                throw new ConsistirException(String.format("Não é possível lançar "
                        + "um afastamento nesse período, pois o número de dias "
                        + "utilizados (%s) é maior ou igual ao número de dias do contrato (%s).",
                        diasUtilizados, nrDiasContratos));
            }

            afastamentoVO.validarPeriodoCarencia(getListaAfastamentos());
            auto.autorizar("Confirmação de afastamento",
                    "LancarAfastamentoContratoDependente",
                    "Você precisa da permissão \"2.84 - Lançar afastamento para clientes que são dependentes\"",
                    "formAfastamentoDependente",
                    listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void validarPermissaoRetroativo() throws ConsistirException {
        if (Uteis.nrDiasEntreDatas(Calendario.hoje(), afastamentoVO.getDataInicio()) < 0) {
            try {
                permissaoFuncionalidade(getUsuarioLogado(), "LancamentoCarenciaRetroativa", "9.59 - Lançamento de Carência Retroativa para o contrato do cliente");
            } catch (Exception e) {
                throw new ConsistirException("Para fazer um lançamento de férias retroativa é necessária a permissão: \"9.59 - Lançamento de Carência Retroativa para o contrato do cliente\"");
            }
        }
    }


    private void validarPeriodosAfastamento() throws ConsistirException {
        if (afastamentoVO.getDataInicio() == null || afastamentoVO.getDataTermino() == null) {
            throw new ConsistirException("O período do Afastamento deve ser informado");
        }

        if (afastamentoVO.isFerias()) {
            long diferenca = Uteis.nrDiasEntreDatas(afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino()) + 1;
            afastamentoVO.setPeriodoCarencia(diferenca);
            if (afastamentoVO.getPeriodoCarencia().intValue() < getCarencia()) {
                throw new ConsistirException("O período do afastamento deve ser maior do que a quantidade mínima de férias");
            }
        }
    }

    public void gerarPeriodoRetornoCarencia() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                montarSucessoGrowl("");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                // limpa a data termino para obrigar o usuario a informar de novo
                afastamentoVO.setDataTermino(null);
            }
        };

        limparMsg();
        try {
            if (afastamentoVO.getDataInicio() != null && afastamentoVO.getNrDiasSomar() != 0) {
                Date novaData = Uteis.somarDias(afastamentoVO.getDataInicio(), (afastamentoVO.getNrDiasSomar() - 1)); // -1 porque o primeiro dia dev
                afastamentoVO.setDataTermino(novaData);
            }

            if (afastamentoVO.getDataInicio() != null && afastamentoVO.getDataTermino() != null) {
                afastamentoVO.validarDatasAfastamento();

                Date dataAnteriorOperacao = Uteis.obterDataAnterior(afastamentoVO.getDataInicio(), 1);

                long nrDiasTotal = Uteis.nrDiasEntreDatas(contratoDependenteVO.getDataInicio(), contratoDependenteVO.getDataFinalAjustada());
                long nrDiasUsados = Uteis.nrDiasEntreDatas(contratoDependenteVO.getDataInicio(), dataAnteriorOperacao);
                long nrDiasRestantes = nrDiasTotal - nrDiasUsados;

                afastamentoVO.setNrDiasContrato((int) nrDiasTotal);
                afastamentoVO.setNrDiasUsadoContrato((int) nrDiasUsados);
                afastamentoVO.setNrDiasRestanteContrato((int) nrDiasRestantes);

                if (afastamentoVO.getNrDiasRestanteContrato() <= 0) {
                    throw new ConsistirException(String.format("Não é possível lançar "
                                    + "um afastamento nesse período, pois o número de dias "
                                    + "utilizados (%s) é maior ou igual ao número de dias do contrato (%s).",
                            nrDiasUsados, nrDiasTotal));
                }

                validarPeriodosAfastamento();
                long diasAfastamento = Uteis.nrDiasEntreDatas(afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino()) + 1;

                afastamentoVO.setDataInicioRetorno(Uteis.obterDataFutura2(afastamentoVO.getDataTermino(), 1));

                //diasRestante pode ser negativo, portanto utilizar os dias da Carência e não o restante do contrato
                if (Calendario.maior(afastamentoVO.getDataTermino(), contratoDependenteVO.getDataFinalAjustada())) {
                    afastamentoVO.setQtdDiasCarenciaMaiorQueContrato(true);
                    afastamentoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(afastamentoVO.getDataTermino(), afastamentoVO.getNrDiasRestanteContrato()));
                    afastamentoVO.setNrDias(afastamentoVO.getNrDiasRestanteContrato());
                } else {
                    afastamentoVO.setQtdDiasCarenciaMaiorQueContrato(false);
                    if (afastamentoVO.getNrDiasRestanteContrato() < (int) diasAfastamento) { // contrato em periodo de bonus
                        afastamentoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(afastamentoVO.getContratoVO().getVigenciaAteAjustada(), afastamentoVO.getNrDiasRestanteContrato()));
                        afastamentoVO.setNrDias(afastamentoVO.getNrDiasRestanteContrato());
                    } else {
                        afastamentoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(afastamentoVO.getContratoVO().getVigenciaAteAjustada(), (int) diasAfastamento));
                        afastamentoVO.setNrDias((int) diasAfastamento);
                    }
                }

                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(contratoDependenteVO.getCliente().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                int acessos = getFacade().getAcessoCliente().consultarQtdAcessosEntreDatas(cliente, afastamentoVO.getDataInicio(), afastamentoVO.getDataTermino(), false);
                // se cliente teve algum acesso neste periodo
                if (acessos > 0) {
                    auto.autorizar("Confirmação de Férias com Acesso", "AtestadoCarencia_Autorizar", "O Aluno possui " + acessos + " acessos no período informado. Para continuar você precisa da permissão \"3.18 - Atestado e Férias com Frequência - Autorizar\"", "form", listener);
                }
                if (getMensagemID().equals("msg_operacao_sucesso")) {
                    limparMsg();
                }
                afastamentoVO.setNrDiasSomar((int) diasAfastamento);
                afastamentoVO.setMensagemErro(false);

            }
        } catch (Exception e) {
            afastamentoVO.setMensagemErro(true);
            montarErro(e);
        }
    }

    private void consultarCarencia() {
        try {
            if (getEmpresaLogado().getCodigo() != 0) {
                setCarencia(new Empresa().obterCarenciaEmpresa(getEmpresaLogado().getCodigo()));
                if (getCarencia() != null && getCarencia() == 0) {
                    setCarencia(new ConfiguracaoSistema().obterCarenciaConfiguracao());
                }
            } else {
                setCarencia(new ConfiguracaoSistema().obterCarenciaConfiguracao());
            }
            if (getCarencia() == null) {
                setCarencia(0);
            }
        } catch (Exception ex) {
            Logger.getLogger(AfastamentoContratoDependenteControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void excluirAfastamento() {
        try {
            validarPermissao("EstornoOperacaoContrato_Autorizar", "3.24 - Estorno de Operação de Contrato - Autorizar", getUsuarioLogado());


            ClienteVO cliente = getFacade().getCliente().consultarPorChavePrimaria(contratoDependenteVO.getCliente().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            afastamentoVO.getContratoDependenteVO().setCliente(cliente);

            getFacade().getContratoDependente().excluirAfastamento(afastamentoVO, getUsuarioLogado());

            setMsgAlert("fireElement('form:btnAtualizaCliente');");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        afastamentoVO = null;
        listaContratoVOs = new ArrayList<>();
        abrirRichModalDeConfirmacao = null;
        apresentarBotoes = null;
        listaAfastamentos = new ArrayList<>();
    }

    public void imprimirComprovanteOperacao() {
        try {
//            if (getCarenciaContratoVO().getContratoOperacaoVO().getCodigo() != 0) {
//                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getCarenciaContratoVO().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getCarenciaContratoVO().getContratoOperacaoVO(), empresaVO));
//            } else {
            throw new Exception("Não foi possível imprimir o comprovante da operação.");
//            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Boolean getAbrirRichModalDeConfirmacao() {
        return abrirRichModalDeConfirmacao;
    }

    public void setAbrirRichModalDeConfirmacao(Boolean abrirRichModalDeConfirmacao) {
        this.abrirRichModalDeConfirmacao = abrirRichModalDeConfirmacao;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public AfastamentoContratoDependenteVO getAfastamentoVO() {
        if (afastamentoVO == null) {
            afastamentoVO = new AfastamentoContratoDependenteVO();
        }
        return afastamentoVO;
    }

    public void setAfastamentoVO(AfastamentoContratoDependenteVO afastamentoVO) {
        this.afastamentoVO = afastamentoVO;
    }

    public List<AfastamentoContratoDependenteVO> getListaAfastamentos() {
        if (listaAfastamentos == null) {
            listaAfastamentos = new ArrayList<>();
        }
        return listaAfastamentos;
    }

    public void setListaAfastamentos(List<AfastamentoContratoDependenteVO> listaAfastamentos) {
        this.listaAfastamentos = listaAfastamentos;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public Integer getCarencia() {
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }

    public String getAviso() {
        return aviso;
    }

    public void setAviso(String aviso) {
        this.aviso = aviso;
    }

    public String getMensagemCarrencia() {
        return mensagemCarrencia;
    }

    public void setMensagemCarrencia(String mensagemCarrencia) {
        this.mensagemCarrencia = mensagemCarrencia;
    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

    public ContratoDependenteVO getContratoDependenteVO() {
        if (contratoDependenteVO == null) {
            contratoDependenteVO = new ContratoDependenteVO();
        }
        return contratoDependenteVO;
    }

    public void setContratoDependenteVO(ContratoDependenteVO contratoDependenteVO) {
        this.contratoDependenteVO = contratoDependenteVO;
    }

    public List<SelectItem> getListSelectItemTipoAfastamento() {
        if (listSelectItemTipoAfastamento == null) {
            listSelectItemTipoAfastamento = new ArrayList<>();
            listSelectItemTipoAfastamento.add(new SelectItem("", ""));
            listSelectItemTipoAfastamento.add(new SelectItem("CR", "Férias"));
            listSelectItemTipoAfastamento.add(new SelectItem("AT", "Atestado"));
        }
        return listSelectItemTipoAfastamento;
    }

    public void setListSelectItemTipoAfastamento(List<SelectItem> listSelectItemTipoAfastamento) {
        this.listSelectItemTipoAfastamento = listSelectItemTipoAfastamento;
    }

    public List<SelectItem> getListSelectItemJustificativa() {
        if (listSelectItemJustificativa == null) {
            listSelectItemJustificativa = new ArrayList<>();
        }
        return listSelectItemJustificativa;
    }

    public void setListSelectItemJustificativa(List<SelectItem> listSelectItemJustificativa) {
        this.listSelectItemJustificativa = listSelectItemJustificativa;
    }
}

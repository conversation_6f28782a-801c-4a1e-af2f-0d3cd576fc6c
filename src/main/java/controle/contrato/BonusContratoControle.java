/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.SituacaoAtualContrato;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.BonusContratoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BonusContratoControle extends SuperControle {

    protected BonusContratoVO bonusContratoVO;
    protected List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs;
    protected List<ContratoVO> listaContratoVOs;
    protected Boolean apresentarBotoes;
    protected Boolean abrirRichModalDeConfirmacao;
    private String nomeArquivoComprovanteOperacao;

    public BonusContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        novo();
    }

    public void inicializarUsuarioLogado() {
        try {
            getBonusContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getBonusContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
            getBonusContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
        }
    }

    public void novo() throws Exception {
        try {
            notificarRecursoEmpresa(RecursoSistema.BONUS_CLIENTE);
            setProcessandoOperacao(false);
            setBonusContratoVO(new BonusContratoVO());
            setListaContratoVOs(new ArrayList<ContratoVO>());
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            //validar se o contrato possui um trancamento sem retorno
            ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(bonusContratoVO.getContratoVO());
            obterNomeCliente();
            setAbrirRichModalDeConfirmacao(false);
            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarDataRetroativaBonus(BonusContratoVO bonusContratoVO) throws Exception {

        if (bonusContratoVO.getNrDias() != null && bonusContratoVO.getNrDias().intValue() > 0 && bonusContratoVO.getAcrescentarDiaContrato() != null
                && bonusContratoVO.getAcrescentarDiaContrato().equals("AC") && bonusContratoVO.getContratoVO().getSituacao().equals(SituacaoAtualContrato.ATIVO.getCodigo())) {
            if (Calendario.maiorComHora(bonusContratoVO.getContratoVO().getVigenciaAteAjustada(), bonusContratoVO.getDataTermino())) {
                throw new Exception("O sistema identificou alguns dados inconsistentes nessa operação. Favor consultar o cliente e tente realizar a operação novamente");
            }
        }
    }

    public void validarDadosBonus() {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);

                bonusContratoVO.setResponsavelOperacao(auto.getUsuario());
                ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(bonusContratoVO.getContratoVO());
                validarDataRetroativaBonus(bonusContratoVO);
                getFacade().getContratoOperacao().incluirOperacaoBonus(bonusContratoVO, false, null, true, null);

                setErro(false);
                montarSucessoGrowl("");
                setApresentarBotoes(false);
                setProcessandoOperacao(false);
                bonusContratoVO.setMensagemErro(new Boolean(false));

                setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});");

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setApresentarBotoes(true);
                setErro(true);
                setSucesso(false);
                montarErro(e);
                bonusContratoVO.setMensagemErro(new Boolean(true));
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };
        limparMsg();
        try {
            BonusContratoVO.validarDados(bonusContratoVO);
            auto.autorizar("Confirmação de Lançamento de Bônus", "Bonus_Autorizar",
                    "Você precisa da permissão \"3.11 - Bonus para Contrato - Autorizar\"",
                    "form", listener);

            montarSucessoGrowl("");
            setApresentarBotoes(false);
            setProcessandoOperacao(false);
            bonusContratoVO.setMensagemErro(false);
            setErro(false);
        } catch (Exception e) {
            setApresentarBotoes(true);
            montarErro(e);
            bonusContratoVO.setMensagemErro(true);
        }
    }

    public void consultarResponsavel() {
        try {
            getBonusContratoVO().setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(getBonusContratoVO().getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getBonusContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void limparNumeroDias() {
        limparMensagem();
        getBonusContratoVO().setNrDias(new Integer(0));
        getBonusContratoVO().setApresentarPeriodoBonus(false);
        getBonusContratoVO().setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        getBonusContratoVO().setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
    }

    public void limparMensagem() {
        setMensagem("");
        setMensagemID("");
        setMensagemDetalhada("");
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void inicializarListasSelectItemTodosComboBox() throws Exception {
        try {
            montarDadosContratoParaBonus();
            montarDadosListaJustificativaOperacaoVOs();
        } catch (Exception e) {
            throw e;
        }
    }

    public void obterNomeCliente() {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.pegarClienteTelaCliente();
                getBonusContratoVO().getContratoVO().getPessoa().setNome(clienteControle.getClienteVO().getPessoa().getNome());
                getBonusContratoVO().setEmpresa(clienteControle.getClienteVO().getEmpresa().getCodigo());
            } else {
                throw new Exception("Não foi possível inicializar o nome do cliente.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarDadosContratoParaBonus() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.pegarClienteTelaCliente();
                getBonusContratoVO().setContratoVO(clienteControle.getContratoVO());
                getListaContratoVOs().add(clienteControle.getContratoVO());
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs() {
        try {
            montarDadosListaJustificativaOperacaoVOs("BO");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs(String prm) throws Exception {
        try {
            List resultadoConsulta = consultarTipoJustificativaOperacaoPorTipo(prm);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                JustificativaOperacaoVO obj = (JustificativaOperacaoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
            }
            setListaJustificativaOperacaoVOs(objs);
        } catch (Exception e) {
            throw e;
        }

    }

    public List consultarTipoJustificativaOperacaoPorTipo(String prm) throws Exception {
        List lista = new ArrayList();
        try {
            lista = getFacade().getJustificativaOperacao().consultarPorTipoOperacao(prm, getEmpresaLogado().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    public List getTipoBonus() throws Exception {
        List itens = new ArrayList();
        //   if (bonusContratoVO.getContratoVO() != null
        //           && Calendario.menor(Calendario.hoje(),
        //           bonusContratoVO.getContratoVO().getVigenciaAteAjustada())) {
        itens.add(new SelectItem("AC", "Acrescentar dias no Contrato "));
        itens.add(new SelectItem("RE", "Retirar dias do Contrato"));
        //  } else {
        //      itens.add(new SelectItem("AC", "Acrescentar dias no contrato"));
        //  }
        return itens;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public BonusContratoVO getBonusContratoVO() {
        return bonusContratoVO;
    }

    public void setBonusContratoVO(BonusContratoVO bonusContratoVO) {
        this.bonusContratoVO = bonusContratoVO;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public List<JustificativaOperacaoVO> getListaJustificativaOperacaoVOs() {
        return listaJustificativaOperacaoVOs;
    }

    public void setListaJustificativaOperacaoVOs(List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs) {
        this.listaJustificativaOperacaoVOs = listaJustificativaOperacaoVOs;
    }

    public Boolean getAbrirRichModalDeConfirmacao() {
        return abrirRichModalDeConfirmacao;
    }

    public void setAbrirRichModalDeConfirmacao(Boolean abrirRichModalDeConfirmacao) {
        this.abrirRichModalDeConfirmacao = abrirRichModalDeConfirmacao;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        bonusContratoVO = null;
        listaJustificativaOperacaoVOs = new ArrayList<JustificativaOperacaoVO>();
        listaContratoVOs = new ArrayList<ContratoVO>();
        abrirRichModalDeConfirmacao = null;
        apresentarBotoes = null;

    }


    public void obterDataInicioTermino() throws Exception {
        try {
            limparMensagem();

            if (getBonusContratoVO().getNrDias() != null && getBonusContratoVO().getNrDias() != 0) {
                if (getBonusContratoVO().getAcrescentarDiaContrato().equals("AC")) {
                    if (Calendario.maior(Calendario.hoje(), getBonusContratoVO().getContratoVO().getVigenciaAteAjustada())) {
                        getBonusContratoVO().setDataInicio(Calendario.getDataComHoraZerada(Calendario.hoje()));
                    } else {
                        getBonusContratoVO().setDataInicio(Uteis.obterDataFutura2(getBonusContratoVO().getContratoVO().getVigenciaAteAjustada(), 1));
                    }
                    getBonusContratoVO().setDataTermino(Uteis.obterDataFutura2(getBonusContratoVO().getDataInicio(), (getBonusContratoVO().getNrDias() - 1)));
                } else {
                    if (getBonusContratoVO().getContratoVO().getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())) {
                        getBonusContratoVO().setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
                        getBonusContratoVO().setDataTermino(Uteis.obterDataAnterior(getBonusContratoVO().getContratoVO().getVigenciaAteAjustada(), (getBonusContratoVO().getNrDias())));
                    } else {
                        getBonusContratoVO().setPeriodoAcessoClienteVO(getFacade().getPeriodoAcessoCliente().obterUltimoDiaPeriodoAcessoContrato(getBonusContratoVO().getContratoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        if (getBonusContratoVO().getPeriodoAcessoClienteVO() == null || !getBonusContratoVO().getPeriodoAcessoClienteVO().getTipoAcesso().equals("BO") || Calendario.menor(getBonusContratoVO().getPeriodoAcessoClienteVO().getDataFinalAcesso(), Calendario.hoje())) {
                            throw new ConsistirException("Não existe Bônus lançado para fazer retirada de dias para Período de Acesso! (Contrato Inativo)");
                        } else {
                            if (Calendario.maior(Calendario.hoje(), Uteis.somarDias(getBonusContratoVO().getPeriodoAcessoClienteVO().getDataFinalAcesso(), (getBonusContratoVO().getNrDias() - 1) * -1))) {
                                throw new ConsistirException("Retirada de dias com quantidade de dias acima do restante de dias do Bônus lançado: " + (Uteis.nrDiasEntreDatas(Calendario.hoje(), getBonusContratoVO().getPeriodoAcessoClienteVO().getDataFinalAcesso()) + 1) + " Dias restantes");
                            }
                            getBonusContratoVO().setDataInicio(Uteis.obterDataAnterior(getBonusContratoVO().getPeriodoAcessoClienteVO().getDataFinalAcesso(), (getBonusContratoVO().getNrDias())));
                            getBonusContratoVO().setDataTermino(Uteis.obterDataAnterior(getBonusContratoVO().getPeriodoAcessoClienteVO().getDataFinalAcesso(), (getBonusContratoVO().getNrDias())));
                        }
                    }
                }
                getBonusContratoVO().setApresentarPeriodoBonus(true);
            } else {
                getBonusContratoVO().setApresentarPeriodoBonus(false);
            }


        } catch (Exception e) {
            getBonusContratoVO().setApresentarPeriodoBonus(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }


    public String getMensagemDataTermino() {
        if (getBonusContratoVO().getContratoVO().getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())) {
            return "A data de Vencimento do seu contrato será de: ";
        } else {
            return "A data de Vencimento do seu período de acesso será de: ";
        }

    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

    public void imprimirComprovanteOperacao() {
        try {
            if (getBonusContratoVO().getContratoOperacaoVO().getCodigo() != 0) {
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getBonusContratoVO().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getBonusContratoVO().getContratoOperacaoVO(), empresaVO));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante da operação.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

}

package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.plano.ConsultarTurmaControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoComposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ConsultarTurmaTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoDuracaoCreditoTreinoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoModalidadeVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.enumerador.ReferenciaValorModalidadeEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import org.apache.commons.collections.Predicate;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Refactoring em 08/11/2011
 *
 * <AUTHOR> Lhoji Shiozawa
 */
public class ManutencaoModalidadeControle extends SuperControle {

    private UsuarioVO responsavel = new UsuarioVO();
    private UsuarioVO responsavelLiberacao = new UsuarioVO();
    private ContratoVO contratoNovo = null;
    private ContratoVO contratoAntigo = null;
    private ContratoModalidadeVO contratoModalidade = new ContratoModalidadeVO();
    private List<ContratoVO> contratos = new ArrayList<ContratoVO>();
    private List<PlanoModalidadeVO> modalidadesPlano = new ArrayList<PlanoModalidadeVO>();
    private List<ContratoOperacaoVO> listaContratoOperacao = new ArrayList<ContratoOperacaoVO>();
    private List<ContratoModalidadeVO> listaModalidadeAdicionada = new ArrayList<ContratoModalidadeVO>();
    private List<ContratoModalidadeVO> listaModalidadeAlterada = new ArrayList<ContratoModalidadeVO>();
    private List<ContratoModalidadeVO> listaModalidadeExcluida = new ArrayList<ContratoModalidadeVO>();
    private List<PlanoModalidadeVezesSemanaVO> listaVezesSemana = new ArrayList<PlanoModalidadeVezesSemanaVO>();
    private boolean mostrarEditorModalidade = false;
    private boolean consultaTurma = true;
    private boolean apresentarBotao = false;
    private boolean apresentarDadosNovoModalidade = false;
    private boolean mostrarPanelConfirmacaoAlterar = false;
    private boolean abrirConfirmacaoLiberacao = false;
    private boolean periodoBonus = false;
    private double valorAlteracaoModalidadeNovo = 0.0;
    private double valorDiaModalidadeNovo = 0.0;
    private double valorModalidadeNovo = 0.0;
    private double valorTotalAlteracaoModalidade = 0.0;
    private int nrDiasContrato;
    private int nrDiasContratoUtilizado;
    private int restanteDiasContrato;
    private boolean apresentarValorFinalModificado = false;
    private boolean mostrarTiposDesconto = false;
    private double valorFinal = 0.0;
    private double desconto = 0.0;
    private String opcaoAlterarValor = "Manter Valor";
    private String tipoDesconto = "";
    private Double valorAlterado = 0.0;
    private boolean mostrarCampoAlterarValor = false;
    private String opcaoUsada = "Manter Valor";

    private Boolean contratoPossuiParcelaComRemessa;
    private Integer diferencaQuantidadeCreditoTreino = 0;
    private double valorUnitarioCreditoTreino = 0;
    private Integer nrVezesPorSemanaEscolhido;
    private Integer saldoCreditoTreino =0;
    private Integer totalCreditoPossivelUtilizar;
    private boolean alterouVezesSemanaContratoCreditoTreino = false;
    private boolean alterouHorarioTurmaCreditoTreino = false;
    private List<HorarioTurmaVO>listaTodosHorarioTurmaCreditoTreino;
    private boolean apresentarMsgLiberacao = false;

    private List<MovParcelaVO> listaParcelasRemessa;
    List<Integer> codigosHorariosNovos; // horários que serão incluídos e devem ser verificados , com relação a ocupação com as reposições futuras
    private List<MovParcelaVO> parcelasEmAberto;

    public boolean getMostrarAlterarValor() {
        return opcaoAlterarValor.equals("Alterar Valor");
    }

    public void aplicarValorNovo() {
        try {
            if (getApresentarLiberacao() && UteisValidacao.emptyNumber(valorAlterado)) {
                throw new ConsistirException("Para valor 0,00 utilize a Liberação!");
            }
            valorFinal = valorAlterado;
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }

    }



    public void novo() {
        ContadorTempo.limparCronometro();
        ContadorTempo.iniciarContagem();
        parcelasEmAberto = new ArrayList<MovParcelaVO>();
        notificarRecursoEmpresa(RecursoSistema.MANUTENCAO_MODALIDADE);
        try {
            setProcessandoOperacao(false);
            inicializarDados();
            inicializarUsuarioLogado();
            inicializarContratoSelecionado();
            inicializarListaModalidades();
            inicializarVezesSemanaContratoAntigo();
            ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getContratoAntigo());
            validarContratoCreditoTreino();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarContratoCreditoTreino() throws Exception{
        if (this.contratoNovo.isVendaCreditoTreino() && !this.contratoNovo.isVendaCreditoSessao()){
            if (this.contratoNovo.getContratoHorario().getHorario().isLivre()){
                throw new ConsistirException("Operação não permitida para contrato 'Crédito de Treino' com horário livre.");
            }
        }
    }

    public boolean isPermiteAlterarModalidade(){
        return !this.contratoNovo.isVendaCreditoTreino() || !this.contratoNovo.getContratoHorario().getHorario().isLivre() || this.contratoNovo.isVendaCreditoSessao();
    }

    public List<MovParcelaVO> getContratoComParcelasEnvioDeRemessa() throws Exception{
        StringBuilder contratosRemessa = new StringBuilder("");
        if (contratos.size() > 1){
            for (int pos=0; pos<contratos.size(); pos++){
                contratosRemessa.append(contratos.get(pos).getCodigo());
                if (pos < (contratos.size()-1)){
                    contratosRemessa.append(",");
                }
            }
        }else {
            contratosRemessa.append(contratos.get(0).getCodigo());
        }

        return getFacade().getMovParcela().consultarPorContratoParcelasEmRemessa(contratosRemessa.toString());
    }

    public void inicializarDados() throws Exception {
        contratoModalidade = new ContratoModalidadeVO();
        contratoModalidade.setCalculoManutencao(true);
        mostrarEditorModalidade = false;
        apresentarDadosNovoModalidade = false;
        alterouVezesSemanaContratoCreditoTreino = false;
        apresentarMsgLiberacao = false;
        setMensagemDetalhada("", "");
    }

    public void inicializarUsuarioLogado() {
        try {
            if (getUsuarioLogado().getCodigo() != 0) {
                getResponsavel().setCodigo(getUsuarioLogado().getCodigo());
                getResponsavel().setUsername(getUsuarioLogado().getUsername());
                getResponsavel().setUserOamd(getUsuarioLogado().getUserOamd());
                getResponsavel().setNome(getUsuarioLogado().getNome());
                getResponsavelLiberacao().setCodigo(getUsuarioLogado().getCodigo());
                getResponsavelLiberacao().setUsername(getUsuarioLogado().getUsername());
                getResponsavelLiberacao().setNome(getUsuarioLogado().getNome());
                 getResponsavelLiberacao().setUserOamd(getUsuarioLogado().getUserOamd());
            }
        } catch (Exception e) {
            getResponsavel().setCodigo(0);
            getResponsavel().setUsername("");
        }
    }

    public void inicializarContratoSelecionado() throws Exception {
        contratos = new ArrayList<ContratoVO>();
        ClienteControle cc = (ClienteControle) obtenhaObjetoSessionMap("ClienteControle");
        if (cc != null) {
            try {
                cc.pegarClienteTelaCliente();
                // pega o contrato escolhido na tela do cliente
                contratoAntigo = getFacade().getContrato().consultarPorChavePrimaria(cc.getContratoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                contratoAntigo.getPlano().setPlanoExcecaoVOs(getFacade().getPlanoExcecao().consultarPorPlano(contratoAntigo.getPlano().getCodigo()));
                contratoAntigo.getPessoa().setNome(cc.getClienteVO().getPessoa().getNome());
                contratoAntigo.setMovProdutoVOs(getFacade().getMovProduto().consultarPorCodigoContrato(contratoAntigo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                validarParcelaAbertaComBoletoPendente(contratoAntigo);

                contratoNovo = (ContratoVO) contratoAntigo.getClone(true);
                contratoNovo.setEmpresa(contratoAntigo.getEmpresa());
                contratos.add(contratoAntigo);
                // separa informações relevantes do contrato
                nrDiasContrato = getFacade().getZWFacade().obterNrDiasContrato(contratoAntigo);
                nrDiasContratoUtilizado = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(getContratoAntigo(), negocio.comuns.utilitarias.Calendario.hoje());
                periodoBonus = getFacade().getZWFacade().isContratoEmPeriodoBonusOuTransferencia(contratoAntigo, negocio.comuns.utilitarias.Calendario.hoje(), nrDiasContrato);
                restanteDiasContrato = (periodoBonus ? 0 : nrDiasContrato - nrDiasContratoUtilizado);
                validarOperacoesFuturas();
                cc.setUrlPopup("abrirPopup('manutencaoModalidade.jsp', 'ManutencaoModalidadeControle', 920, 700);");
                inicializarValoresModalidadesAtuais();
            } catch (Exception e){
                cc.montarErro(e);
                cc.setUrlPopup("");
                throw e;
            }
        }
    }

    public void inicializarListaModalidades() {
        try {
            modalidadesPlano = getFacade().getPlanoModalidade().consultarPlanoModalidades(contratoNovo.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (ContratoModalidadeVO cm : contratoNovo.getContratoModalidadeVOs()) {
                cm.setCalculoManutencao(true);
                PlanoModalidadeVO plano;
                try {
                    plano = getFacade().getPlanoModalidade().consultarPorPlanoModalidade(contratoNovo.getPlano().getCodigo(),
                            cm.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } catch (Exception ex) {
                    throw new ConsistirException("A modalidade " + cm.getModalidade().getNome() + " não está mais cadastrada para o plano " + contratoNovo.getPlano().getDescricao());
                }
                // pega a lista de vezes por semana de uma modalidade
                List<PlanoModalidadeVezesSemanaVO> lista = getFacade().getPlanoModalidadeVezesSemana().consultarPorCodigoPlanoModalidade(plano.getCodigo());
                for (PlanoModalidadeVezesSemanaVO planoVezesSemana : lista) {
                    // se a quantidade escolhida existir
                    if (planoVezesSemana.getNrVezes().equals(cm.getNrVezesSemana())) {
                        planoVezesSemana.setVezeSemanaEscolhida(true);
                        cm.setPlanoVezesSemanaVO(planoVezesSemana);
                        cm.setNrVezesSemana(planoVezesSemana.getNrVezes());

                    }
                }
                validarOcupacao(cm);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarVezesSemanaContratoAntigo(){
        try {
            List<ContratoModalidadeVO> listContratoModalidade = getFacade().getContratoModalidade().consultarPorCodigoContrato(contratoAntigo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            for (ContratoModalidadeVO cmVO : listContratoModalidade) {
                for (ContratoModalidadeVO cm : contratoAntigo.getContratoModalidadeVOs()) {
                    if(cmVO.getModalidade().getCodigo().equals(cm.getModalidade().getCodigo())){
                        cm.setNrVezesSemana(cmVO.getNrVezesSemana());
                    }
                }
            }
        } catch (Exception e) {
        setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarVezesPorSemana() {
        try {
            // consulta o plano modalidade
            PlanoModalidadeVO plano;
            contratoModalidade.setPlanoModalidade( plano = getFacade().getPlanoModalidade().consultarPorPlanoModalidade(contratoNovo.getPlano().getCodigo(),
                    contratoModalidade.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            // pega a lista de vezes por semana de uma modalidade
            listaVezesSemana = getFacade().getPlanoModalidadeVezesSemana().consultarPorCodigoPlanoModalidade(plano.getCodigo());
            contratoModalidade.getPlanoModalidade().setPlanoModalidadeVezesSemanaVOs(listaVezesSemana);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

// * consulta de turmas e horarios *********************************************
    public void abrirConsultaTurma() {
        setErro(false);
        setMensagemDetalhada("", "");
        try {
            inicializarVezesPorSemana();
            // prepara os dados para consulta
            consultaTurma = true;
            ConsultarTurmaTO consulta = new ConsultarTurmaTO();
            consulta.setEmpresa(contratoAntigo.getEmpresa());
            consulta.setModalidade(contratoModalidade.getModalidade());

            // transfere os dados pro controlador da consulta
            ConsultarTurmaControle consultarTurmaControle = (ConsultarTurmaControle) getControlador(ConsultarTurmaControle.class.getSimpleName());
            consultarTurmaControle.setRenovacaoContrato(false);
            consultarTurmaControle.setEmpresaPermiteRenovarContratosEmTurmasLotadas(false);
            int origem;
            if(context().getViewRoot().getViewId().contains("manutencaoModalidade.jsp")){
                origem = ConsultarTurmaControle.ORIGEM_MANUTENCAO_MODALIDADE;
            }
            else  {
                origem = ConsultarTurmaControle.ORIGEM_MANUTENCAO_MODALIDADE;
            }
            consulta.setPessoaOperacao(getContratoNovo().getPessoa().getCodigo());
            if(getContratoNovo().getPlano().isVendaCreditoTreino() && !getContratoNovo().getPlano().isCreditoSessao()) {
                consultarTurmaControle.setPlanoCreditoTreino(true);
                contratoNovo.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana();
            }
            consultarTurmaControle.preparaConsulta(consulta, contratoNovo.getPessoa().getDataNasc(), consultaTurma, origem, contratoModalidade, null);

            /*setMensagemDetalhada("msg_dados_consultados", "");*/
        } catch (Exception e) {
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirConsultaHorario() {
        setErro(false);
        setMensagemDetalhada("", "");
        try {
            // prepara os dados para consulta
            inicializarVezesPorSemana();
            consultaTurma = false;
            ConsultarTurmaTO consulta = new ConsultarTurmaTO();
            consulta.setEmpresa(getEmpresaLogado());
            consulta.setModalidade(contratoModalidade.getModalidade());
            ContratoModalidadeTurmaVO obj = (ContratoModalidadeTurmaVO) obtenhaObjetoRequestMap("turma");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar a turma corretamente.");
            }
            consulta.setTurma(obj.getTurma());
            consulta.setPessoaOperacao(getContratoNovo().getPessoa().getCodigo());
            ContratoModalidadeVO contratoModalidadeConsulta = (ContratoModalidadeVO) contratoModalidade.getClone(true);

            // transfere os dados pro controlador da consulta
            ConsultarTurmaControle consultarTurmaControle = (ConsultarTurmaControle) getControlador(ConsultarTurmaControle.class.getSimpleName());
            consultarTurmaControle.setRenovacaoContrato(false);
            consultarTurmaControle.setEmpresaPermiteRenovarContratosEmTurmasLotadas(false);
            consultarTurmaControle.preparaConsulta(consulta, contratoNovo.getPessoa().getDataNasc(), consultaTurma, 2, contratoModalidadeConsulta, null);

            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getAbrirPanelConsulta() {
        if (getErro()) {
            return "";
        }
        return "Richfaces.showModalPanel('panelConsultarTurma')";
    }

// * funcoes de tela *********************************************************
    public void adicionarModalidade() {
        contratoModalidade = new ContratoModalidadeVO();
        mostrarEditorModalidade = true;
    }

    public void editarModalidade() {
        setMensagemDetalhada("", "");
        try {
            ContratoModalidadeVO obj = (ContratoModalidadeVO) obtenhaObjetoRequestMap("cm");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar a modalidade corretamente.");
            }
            // seleciona a modalidade
            contratoModalidade = (ContratoModalidadeVO) obj.getClone(true);
            mostrarEditorModalidade = true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            mostrarEditorModalidade = false;
        }
    }

    public void excluirModalidade() {
        setMensagemDetalhada("", "");
        try {
            ContratoModalidadeVO obj = (ContratoModalidadeVO) obtenhaObjetoRequestMap("cm");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar a modalidade corretamente.");
            }
            if (contratoNovo.getContratoModalidadeVOs().size() == 1) {
                throw new Exception("Não é Possível Excluir todas as Modalidades de um Contrato. Mantenha Pelo Menos Uma Modalidade.");
            }

            // o uso do iterator é melhor neste caso pois remover o elemento se torna mais eficiente
            Iterator i = contratoNovo.getContratoModalidadeVOs().iterator();
            while (i.hasNext()) {
                ContratoModalidadeVO cm = (ContratoModalidadeVO) i.next();
                if (cm.getCodigo().equals(obj.getCodigo())) {
                    i.remove();
                    break;
                }
            }
            if (this.contratoNovo.isVendaCreditoTreino() && !this.contratoNovo.isVendaCreditoSessao()){
                gravarModalidadeVezesCreditoTreino(0);
            }
            if (!contratoNovo.getContratoModalidadeVOs().isEmpty()) {
                calcularContrato();
            }
            contratoModalidade = new ContratoModalidadeVO();
            mostrarEditorModalidade = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void cancelarEdicaoModalidade() {
        try {
            inicializarDados();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        setMensagemDetalhada("", "");
    }

    public void selecionarModalidade() {
        setMensagemDetalhada("", "");
        try {
            PlanoModalidadeVO obj = (PlanoModalidadeVO) obtenhaObjetoRequestMap("modalidade");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar a modalidade corretamente.");
            }

            // percorre a lista de modalidades dos contrato
            for (ContratoModalidadeVO cm : contratoNovo.getContratoModalidadeVOs()) {
                // se esta modalidade ja existe no contrato
                if (cm.getModalidade().getCodigo().intValue() == obj.getModalidade().getCodigo()) {
                    contratoModalidade = (ContratoModalidadeVO) cm.getClone(true);
                    return;
                }
            }
            // percorre a lista de modalidades dos contrato antigo
            for (ContratoModalidadeVO cm : contratoAntigo.getContratoModalidadeVOs()) {
                // se esta modalidade ja existe no contrato
                if (cm.getModalidade().getCodigo().intValue() == obj.getModalidade().getCodigo()) {
                    contratoModalidade = (ContratoModalidadeVO) cm.getClone(true);
                    setMensagemDetalhada("msg_erro", "Esta modalidade já estava presente no contrato. Os dados desta modalidade foram recuperados!");
                    return;
                }
            }
            ModalidadeVO mod = obj.getModalidade();
            mod.setModalidadeEscolhida(true);
            // inclui a modalidade
            contratoModalidade = new ContratoModalidadeVO();
            contratoModalidade.setContrato(contratoNovo.getCodigo());
            contratoModalidade.setValorModalidade(mod.getValorMensal());
            contratoModalidade.setValorFinalModalidade(mod.getValorMensal());
            contratoModalidade.setModalidade(mod);
            contratoModalidade.setNrVezesSemana(mod.getNrVezes());
            // verifica qual o planovezesSemana correto e coloca no contrato modalidade
            for (Object o : obj.getPlanoModalidadeVezesSemanaVOs()) {
                PlanoModalidadeVezesSemanaVO pm = (PlanoModalidadeVezesSemanaVO) o;
                if (mod.getNrVezes().intValue() == pm.getNrVezes()) {
                    contratoModalidade.setPlanoVezesSemanaVO(pm);
                    break;
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarVezesSemana() {
        setMensagemDetalhada("", "");
        try {
            PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO) obtenhaObjetoRequestMap("vezesSemana");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar a quantidade corretamente.");
            }
            obj.setVezeSemanaEscolhida(true);
            contratoModalidade.setPlanoVezesSemanaVO(obj);
            contratoModalidade.setNrVezesSemana(obj.getNrVezes());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public void gravarModalidade() {
        gravarModalidadeVezes(false); 
    }

    private Integer contarNrVezesSemanaCreditoTreino(int nrVezesPorSemanaAdicionado)throws Exception{
        // contar as vezes por semana já adicionado no contrato
        List<ContratoModalidadeVO> listaContratoModalidade = contratoNovo.getContratoModalidadeVOs();
        Integer nrVezes = 0;
        for (ContratoModalidadeVO contratoModalidadeVO: listaContratoModalidade){
            if ((nrVezesPorSemanaAdicionado > 0) && (contratoModalidadeVO.getModalidade().getCodigo().equals(contratoModalidade.getModalidade().getCodigo()))){
                continue;
            }
            for (Object obj: contratoModalidadeVO.getContratoModalidadeTurmaVOs()){
                ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = (ContratoModalidadeTurmaVO)obj;
                nrVezes = nrVezes + contratoModalidadeTurmaVO.getContratoModalidadeHorarioTurmaVOs().size();
            }
        }
        return nrVezes + nrVezesPorSemanaAdicionado;
    }

    private List montarTodosContratoModalidadeTurmaCreditoTreino()throws Exception{
        List lista = new ArrayList();
        lista.addAll(contratoModalidade.getContratoModalidadeTurmaVOs());
        List<ContratoModalidadeVO> listaContratoModalidade = contratoNovo.getContratoModalidadeVOs();
        for (ContratoModalidadeVO contratoModalidadeVO: listaContratoModalidade){
            if (contratoModalidadeVO.getModalidade().getCodigo().equals(contratoModalidade.getModalidade().getCodigo())){
                continue;
            }
            lista.addAll(contratoModalidadeVO.getContratoModalidadeTurmaVOs());
        }
        return lista;
    }

    public void montarTodosHorarioTurmaCreditoTreino(List listaContratoModalidadeTurma )throws Exception{
        this.listaTodosHorarioTurmaCreditoTreino = new ArrayList<HorarioTurmaVO>();
        for (Object obj : listaContratoModalidadeTurma) {
            ContratoModalidadeTurmaVO contratoModalidadeTurmaVO = (ContratoModalidadeTurmaVO) obj;
            if (contratoModalidadeTurmaVO.getTurma().getTurmaEscolhida()) {
                for (Object objModTurmaHorario : contratoModalidadeTurmaVO.getContratoModalidadeHorarioTurmaVOs()) {
                    ContratoModalidadeHorarioTurmaVO contratoModalidadeHorarioTurmaVO = (ContratoModalidadeHorarioTurmaVO) objModTurmaHorario;
                    if (contratoModalidadeHorarioTurmaVO.getHorarioTurma().getHorarioTurmaEscolhida()) {
                        this.listaTodosHorarioTurmaCreditoTreino.add(contratoModalidadeHorarioTurmaVO.getHorarioTurma());
                    }
                }
            }
        }
    }

    private void gravarModalidadeVezesCreditoTreino(int nrVezesPorSemana)throws Exception{
        if (this.contratoNovo.isVendaCreditoTreino() && !this.contratoNovo.isVendaCreditoSessao()){
            this.nrVezesPorSemanaEscolhido = contarNrVezesSemanaCreditoTreino(nrVezesPorSemana);
            alterouVezesSemanaContratoCreditoTreino = this.nrVezesPorSemanaEscolhido != this.contratoNovo.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana();
            mostrarEditorModalidade = false;

            this.saldoCreditoTreino = ControleCreditoTreino.consultarSaldoCredito(getFacade().getCliente().getCon(), this.contratoNovo.getCodigo());
            Integer aulasDesmarcasPassadasARepor = getFacade().getAulaDesmarcada().contarAulasDesmarcadasPorPeriodo(contratoNovo.getCodigo(), 0,null,Calendario.hoje()); //aulas que já passaram e não foram repostas e não consumiram crédito
            List listaContratoModalidadeTurma = montarTodosContratoModalidadeTurmaCreditoTreino();
            Map<Integer, Date> mapaDia = this.contratoNovo.verificarTotalCreditoPossivelUtilizar(Calendario.hoje(), listaContratoModalidadeTurma, contratoNovo.getVigenciaAteAjustada(),(saldoCreditoTreino - aulasDesmarcasPassadasARepor));
            Set<Map.Entry<Integer, Date>> set = mapaDia.entrySet();
            Date novaDataFimOcupacaoTurma = null;
            for (Map.Entry<Integer, Date> ent : set) {
                this.totalCreditoPossivelUtilizar = ent.getKey();
                novaDataFimOcupacaoTurma = ent.getValue();
            }
            this.contratoNovo.setVigenciaTurmaCreditoTreinoAte(novaDataFimOcupacaoTurma);
            montarTodosHorarioTurmaCreditoTreino(listaContratoModalidadeTurma);
            this.alterouHorarioTurmaCreditoTreino = getFacade().getContratoModalidadeHorarioTurma().houveAlteracaoDeHorarioTurma(contratoNovo.getCodigo(),this.listaTodosHorarioTurmaCreditoTreino);

            this.diferencaQuantidadeCreditoTreino = this.totalCreditoPossivelUtilizar - this.saldoCreditoTreino;
            PlanoDuracaoVO planoDuracaoVO = getFacade().getPlanoDuracao().consultarPorNumeroMesesPlano(this.contratoNovo.getContratoDuracao().getNumeroMeses(),this.contratoNovo.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Integer nrVezesPesquisar = this.contratoNovo.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana();
            if (this.diferencaQuantidadeCreditoTreino > 0){
                nrVezesPesquisar = this.nrVezesPorSemanaEscolhido;
            }
            PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoVO = getFacade().getPlanoDuracaoCreditoTreino().consultar(planoDuracaoVO.getCodigo(), TipoHorarioCreditoTreinoEnum.HORARIO_TURMA.getCodigo(), nrVezesPesquisar,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (planoDuracaoCreditoTreinoVO == null){
                StringBuilder msg = new StringBuilder();
                msg.append("Operação não permitida. Não foi configurado no plano '").append(this.contratoNovo.getPlano().getDescricao()).append("' ");
                msg.append("os valores referentes a ").append(this.nrVezesPorSemanaEscolhido).append("x por semana para a duração ");
                if (this.contratoNovo.getContratoDuracao().getNumeroMeses() == 1){
                    msg.append(this.contratoNovo.getContratoDuracao().getNumeroMeses()).append(" mês.");
                }else{
                    msg.append(this.contratoNovo.getContratoDuracao().getNumeroMeses()).append(" meses.");
                }
                throw new ConsistirException(msg.toString());
            }
            this.valorUnitarioCreditoTreino = planoDuracaoCreditoTreinoVO.getValorUnitario();

            if (this.alterouHorarioTurmaCreditoTreino){
                apresentarDadosNovoModalidade = true;
            }
            if (alterouVezesSemanaContratoCreditoTreino){
                this.apresentarDadosNovoModalidade = true;
                valorModalidadeNovo = (Math.abs(this.diferencaQuantidadeCreditoTreino)) * this.valorUnitarioCreditoTreino;
                valorTotalAlteracaoModalidade = valorModalidadeNovo;
                valorFinal = valorTotalAlteracaoModalidade;
                if (this.diferencaQuantidadeCreditoTreino > 0){
                    contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo() + valorTotalAlteracaoModalidade);
                }else{
                    contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo() - valorTotalAlteracaoModalidade);
                }
                contratoNovo.setDiferencaQtdeCreditoTreinoManutencaoModalidade(this.diferencaQuantidadeCreditoTreino);
                contratoNovo.setQtdeVezesSemanaAposManutencaoModalidade(this.nrVezesPorSemanaEscolhido);
            }else{
                contratoNovo.setQtdeVezesSemanaAposManutencaoModalidade(null);
                contratoNovo.setDiferencaQtdeCreditoTreinoManutencaoModalidade(0);
                valorModalidadeNovo = 0;
                valorTotalAlteracaoModalidade = 0;
                valorFinal = 0;
                contratoNovo.setValorBaseCalculo(contratoAntigo.getValorBaseCalculo());
            }
        }
    }

    private boolean isMgbNiveisHorariosIguais() {
        // se integrado com mgb, verificar se todos os níveis dos horários selecionados são iguais, pois o MGB possui como regra
        // que o aluno obrigatoriamente deve possuir somente um nível atual;
        // se o nivelCodigoMgb do horario selecionado estiver vazio indica que não possui vínculo com um nível mgb, e pode ser selecionado
        try {
            String primeiroPublicIdNivelMgb = "";
            for (Object o : contratoModalidade.getContratoModalidadeTurmaVOs()) {
                ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) o;
                for (Object item : cmt.getContratoModalidadeHorarioTurmaVOs()) {
                    try {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) item;
                        if (UteisValidacao.emptyString(primeiroPublicIdNivelMgb)) {
                            primeiroPublicIdNivelMgb = cmht.getHorarioTurma().getNivelTurma().getCodigoMgb() != null
                                    ? cmht.getHorarioTurma().getNivelTurma().getCodigoMgb()
                                    : "";
                        }
                        if (!UteisValidacao.emptyString(primeiroPublicIdNivelMgb)
                                && !UteisValidacao.emptyString(cmht.getHorarioTurma().getNivelTurma().getCodigoMgb())
                                && !primeiroPublicIdNivelMgb.equals(cmht.getHorarioTurma().getNivelTurma().getCodigoMgb())) {
                            return false;
                        }
                    } catch (Exception e) {
                        Uteis.logar(e, ManutencaoModalidadeControle.class);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            Uteis.logar(e, ManutencaoModalidadeControle.class);
        }
        return false;
    }

    public void gravarModalidadeVezes(boolean modalidadeInicial) {
        setMensagemDetalhada("", "");
        try {
            if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo()) && !isMgbNiveisHorariosIguais()) {
                // se integrado com mgb, verificar se todos os níveis dos horários selecionados são iguais, pois o MGB possui como regra
                // que o aluno obrigatoriamente deve possuir somente um nível atual;
                throw new Exception("A integração com o MGB está ativa, obrigatoriamente os horários selecionados devem possuir o mesmo nível vinculado ao nível MGB");
            }
            if (contratoModalidade.getModalidade().getCodigo() == 0) {
                throw new Exception("Nenhuma Modalidade foi informada. Não é possível gravar.");
            }
            if (!this.contratoNovo.isVendaCreditoTreino() || this.contratoNovo.isVendaCreditoSessao()){
                if (contratoModalidade.getModalidade().isUtilizarTurma() && contratoModalidade.getContratoModalidadeTurmaVOs().isEmpty()) {
                    throw new Exception("Nenhuma Turma foi informada para esta modalidade.");
                }
            }
            int nrVezesPorSemana = 0;
            boolean incluirModalidade = false;
            // se modalidade usa turmas
            if (contratoModalidade.getModalidade().isUtilizarTurma()) {
                // percorre as turmas da modalidade
                List<Integer> turmasExcluir = new ArrayList<Integer>();
                for (Object o : contratoModalidade.getContratoModalidadeTurmaVOs()) {
                    ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) o;
                    // conta qtos horarios tem em cada turma
                    if(cmt.getContratoModalidadeHorarioTurmaVOs().size() == 0){
                        turmasExcluir.add(cmt.getTurma().getCodigo());
                        continue;
                    }
                    nrVezesPorSemana += cmt.getContratoModalidadeHorarioTurmaVOs().size();
                }
                for(Integer codigoTurma: turmasExcluir){
                    contratoModalidade.excluirObjContratoModalidadeTurmaVOs(codigoTurma);
                }
                // se modalidade nao usa turmas
            } else {
                if (contratoModalidade.getNrVezesSemana() > 0) {
                    nrVezesPorSemana = contratoModalidade.getNrVezesSemana();
                }
            }
            PlanoModalidadeVO plano;
            try {
                plano = getFacade().getPlanoModalidade().consultarPorPlanoModalidade(contratoNovo.getPlano().getCodigo(),
                        contratoModalidade.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception ex) {
                throw new ConsistirException("A modalidade " + contratoModalidade.getModalidade().getNome() + " não está mais cadastrada para o plano " + contratoNovo.getPlano().getDescricao());
            }

            gravarModalidadeVezesCreditoTreino(nrVezesPorSemana);
            // pega a lista de vezes por semana de uma modalidade
            List<PlanoModalidadeVezesSemanaVO> lista = getFacade().getPlanoModalidadeVezesSemana().consultarPorCodigoPlanoModalidade(plano.getCodigo());
            PlanoModalidadeVezesSemanaVO planoVezesReferencia = (PlanoModalidadeVezesSemanaVO) ColecaoUtils.find(
                    lista, new Predicate() {
                        @Override
                        public boolean evaluate(Object o) {
                            PlanoModalidadeVezesSemanaVO vezesSemana = (PlanoModalidadeVezesSemanaVO) o;
                            return vezesSemana.isReferencia();
                        }
                    });

            if (planoVezesReferencia != null) {
                if (!contratoModalidade.getModalidade().getUtilizarTurma()) {
                    contratoModalidade.setPlanoVezesSemanaVO(planoVezesReferencia);
                    contratoModalidade.setNrVezesSemana(planoVezesReferencia.getNrVezes());
                }
                contratoModalidade.getModalidade().setValorOriginal(planoVezesReferencia.getValorEspecifico());
                contratoModalidade.getModalidade().setValorMensal(planoVezesReferencia.getValorEspecifico());
                contratoModalidade.getModalidade().setValorOriginal(planoVezesReferencia.getValorEspecifico());
                contratoModalidade.getModalidade().setNrVezes(planoVezesReferencia.getNrVezes());
                planoVezesReferencia.setReferencia(true);
                planoVezesReferencia.setOrigem(ReferenciaValorModalidadeEnum.PLANO);
                planoVezesReferencia.setValorReferencia(planoVezesReferencia.getValorEspecifico());
            }

            // percorre a lista de vezes por semana da modalidade
            for (PlanoModalidadeVezesSemanaVO planoVezesSemana : lista) {
                // se a quantidade escolhida existir
                boolean flag = (this.contratoNovo.getPlano().isVendaCreditoTreino() && !this.contratoNovo.isVendaCreditoSessao()) ? alterouHorarioTurmaCreditoTreino: (planoVezesSemana.getNrVezes() == nrVezesPorSemana);
                if (flag) {
                    if (this.contratoNovo.getPlano().isVendaCreditoTreino() && !this.contratoNovo.isVendaCreditoSessao()){
                        // Na venda de crédito de treino, o aluno pode escolher quantas vezes deseja treinar em cada modalidade.
                        planoVezesSemana.setNrVezes(nrVezesPorSemana);
                    }
                    planoVezesSemana.setVezeSemanaEscolhida(true);
                    contratoModalidade.setPlanoVezesSemanaVO(planoVezesSemana);
                    contratoModalidade.setNrVezesSemana(planoVezesSemana.getNrVezes());
                    incluirModalidade = true;
                }
            }
            
            if (incluirModalidade) {

                ContratoComposicaoVO contratoComposicaoVO = getFacade().getContratoComposicao().consultaPorCodigoContrato(contratoNovo.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                Iterator i = contratoNovo.getContratoModalidadeVOs().iterator();
                // percorre a lista de modalidades dos contrato
                while (i.hasNext()) {
                    ContratoModalidadeVO cm = (ContratoModalidadeVO) i.next();

                    if (contratoComposicaoVO != null) {
                        if (cm.getValorFinalModalidade() < cm.getModalidade().getValorMensal()) {
                            cm.getModalidade().setValorMensal(cm.getValorFinalModalidade());
                        }
                    } 

                    // se esta modalidade ja existe no contrato
                    if ((cm.getCodigo().intValue() != 0 && cm.getCodigo().intValue() == contratoModalidade.getCodigo()) || cm.getModalidade().getCodigo().intValue() == contratoModalidade.getModalidade().getCodigo()) {
                        // retira ela para colocar a nova
                        i.remove();

                    }
                }
                // adiciona a nova modalidade ao contrato
                validarOcupacao(contratoModalidade);
                contratoNovo.getContratoModalidadeVOs().add((ContratoModalidadeVO) contratoModalidade.getClone(true));
                // esconde a tela de edicao
                if(!modalidadeInicial){
                    mostrarEditorModalidade = false;
                    calcularContrato();
                }
            } else {
                if (!this.contratoNovo.isVendaCreditoTreino() || this.contratoNovo.isVendaCreditoSessao()){
                    contratoModalidade.setPlanoVezesSemanaVO(new PlanoModalidadeVezesSemanaVO());
                    contratoModalidade.setNrVezesSemana(0);
                    throw new Exception("O Número de vezes por semana para Modalidade "
                            + contratoModalidade.getModalidade().getNome() + " não está Disponivel.");
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerTurma() {
        setMensagemDetalhada("", "");
        try {
            ContratoModalidadeTurmaVO obj = (ContratoModalidadeTurmaVO) obtenhaObjetoRequestMap("turma");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar a turma corretamente.");
            }
            // o uso do iterator é melhor neste caso pois remover o elemento se torna mais eficiente
            Iterator i = contratoModalidade.getContratoModalidadeTurmaVOs().iterator();
            while (i.hasNext()) {
                ContratoModalidadeTurmaVO cm = (ContratoModalidadeTurmaVO) i.next();
                if (cm.getCodigo().equals(obj.getCodigo())) {
                    i.remove();
                    break;
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void removerHorario() {
        setMensagemDetalhada("", "");
        try {
            ContratoModalidadeHorarioTurmaVO obj = (ContratoModalidadeHorarioTurmaVO) obtenhaObjetoRequestMap("horario");
            if (obj == null) {
                throw new Exception("Não foi possível posicionar o horário corretamente.");
            }
            contratoModalidade.setAdvertenciaMatriculasFuturas(false);
            for (Object obj1 : contratoModalidade.getContratoModalidadeTurmaVOs()) {
                ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) obj1;
                if (cmt.getTurma().getCodigo().intValue() == obj.getHorarioTurma().getTurma()) {
                    // o uso do iterator é melhor neste caso pois remover o elemento se torna mais eficiente
                    Iterator j = cmt.getContratoModalidadeHorarioTurmaVOs().iterator();
                    while (j.hasNext()) {
                        ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) j.next();
                        if (cmht.getHorarioTurma().getCodigo().intValue() == obj.getHorarioTurma().getCodigo()) {
                            j.remove();
                            return;
                        }
                    }
                }
            }
            for (Object obj1 : contratoModalidade.getContratoModalidadeTurmaVOs()) {
                ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) obj1;
                    // o uso do iterator é melhor neste caso pois remover o elemento se torna mais eficiente
                Iterator j = cmt.getContratoModalidadeHorarioTurmaVOs().iterator();
                while (j.hasNext()) {
                    ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) j.next();
                     if (!UteisValidacao.emptyString(cmht.getHorarioTurma().getMsgMatriculasFuturas())){
                         contratoModalidade.setAdvertenciaMatriculasFuturas(true);
                     }
                }
            }
            
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String proximo() {
        limparMsg();
        setMsgAlert("");
        String retorno = "";
        try {
             for (ContratoModalidadeVO cm : contratoNovo.getContratoModalidadeVOs()) {
                 if(cm.isObstrucaoConclusaoManutencao()){
                     throw new Exception("A modalidade "+cm.getModalidade().getNome()+" contem turmas que estão acima do limite ou inativas. Verifique as marcações e resolva as pendências antes de prossegir.");
                 }
             }
            
            processarNovoContrato();
            StringBuilder sbValida = new StringBuilder();
            if(!UteisValidacao.emptyList(codigosHorariosNovos)){
                try {
                    sbValida = getFacade().getReposicao().validarConflitoComReposicoes(contratoNovo, codigosHorariosNovos);
                }catch (Exception e){
                    setMsgAlert("");
                    throw e;
                }
            }
            if(sbValida.length() > 0){
                sbValida.append("<br><br>Mesmo assim deseja continuar?");
                MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
                control.setMensagemDetalhada("", "");
                setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
                control.init("Manutenção com divergência(s)",
                        sbValida.toString(),
                        this, "continuarManutecao", "", "alterarHorariosComReposicao", "", "form");
            }else {
                retorno = prosseguirManutencao();
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
        return retorno;
    }

    public String continuarManutecao(){
        String retorno = "";
        try {
            retorno = prosseguirManutencao();
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
        return retorno;
    }
    public void alterarHorariosComReposicao(){
        limparMsg();
    }

    public String prosseguirManutencao() throws Exception{
        Ordenacao.ordenarLista(getListaModalidadeAdicionada(), "nomeModalidade_Apresentar");
        Ordenacao.ordenarLista(getListaModalidadeAlterada(), "nomeModalidade_Apresentar");
        Ordenacao.ordenarLista(getListaModalidadeExcluida(), "nomeModalidade_Apresentar");
        if (getListaModalidadeAdicionada().isEmpty()
                && getListaModalidadeAlterada().isEmpty()
                && getListaModalidadeExcluida().isEmpty()) {
            setApresentarBotao(false);
            setMostrarTiposDesconto(false);
            //desfazerAlteracoes();
            setMensagemDetalhada("Não houve nenhuma manutenção em modalidade");
        } else {
            opcaoAlterarValor = "Manter Valor";
            alterarOpcaoAlterarValor();
            setApresentarBotao(true);
            setMostrarTiposDesconto(true);

        }

        return "proximo";
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 29/04/2013
     */
    private void autorizarAcao(AutorizacaoFuncionalidadeControle auto, AutorizacaoFuncionalidadeListener listener) {
        //preparar controle de Permissão Especial
        auto.autorizar("Manutenção Modalidade", "ManutencaoModalidade_Autorizar", "Você precisa da permissão \"3.14 - Lançamento de manutenção de modalidade\"", "form", listener);
    }

    private void processarNovoContrato() throws Exception {
        setListaModalidadeAdicionada(new ArrayList<ContratoModalidadeVO>());
        setListaModalidadeAlterada(new ArrayList<ContratoModalidadeVO>());
        setListaModalidadeExcluida(new ArrayList<ContratoModalidadeVO>());
        codigosHorariosNovos = new ArrayList<Integer>();
        processarModalidades();
    }

    public void processarModalidades() throws Exception {
        // se excluiu todas as modalidades e nao adicionou nenhuma
        if (contratoNovo.getContratoModalidadeVOs().isEmpty()) {
            throw new Exception("Não é Possível Excluir todas as Modalidades de um Contrato. Mantenha Pelo Menos Uma Modalidade.");
        }
        // lista que conterá as modalidades do contrato antigo que possuem equivalente no novo
        // note que as modalidades que nao estiverem nesta lista é que serão excluidas
        List<ContratoModalidadeVO> exclusao = new ArrayList<ContratoModalidadeVO>();
        // percorre as modalidades do novo contrato
        for (ContratoModalidadeVO cmNovo : contratoNovo.getContratoModalidadeVOs()) {
            // indica se sera necessario incluir a modalidade escolhida
            boolean inclusao = true;
            // percorre as modalidades do contrato antigo
            for (ContratoModalidadeVO cmAntigo : contratoAntigo.getContratoModalidadeVOs()) {
                // se modalidade do contrato novo e modalidade do contrato antigo são iguais
                if (cmNovo.getModalidade().getCodigo().intValue() == cmAntigo.getModalidade().getCodigo().intValue()) {
                    // todas as modalidades que estiverem presentes nesta lista nao serao incluidas nas mensagens de exclusao de turma
                    exclusao.add(cmAntigo);
                    inclusao = false;
                    // processa turmas
                    processarTurmas(cmAntigo, cmNovo);
                    break;
                }
            }
            if (inclusao) {
                // inclui a modalidade
                codigosHorariosNovos.addAll(cmNovo.gerarTextoInclusao(cmNovo));
                adicionarModalidades(getListaModalidadeAdicionada(), cmNovo);
            }
        }
        // se a quantidade de modalidades do contrato antigo for diferente da quantidade atualizada
        if (exclusao.size() != contratoAntigo.getContratoModalidadeVOs().size()) {
            // percorre todas as modalidades do contrato antigo
            for (ContratoModalidadeVO cm : contratoAntigo.getContratoModalidadeVOs()) {
                // se a modalidade nao teve manutencao realizada
                if (!exclusao.contains(cm)) {
                    // exclui a modalidade nao escolhida
                    adicionarModalidades(getListaModalidadeExcluida(), cm);
                    cm.gerarTextoExclusao(cm);
                }
            }
        }
    }

    public void processarTurmas(ContratoModalidadeVO cmAntigo, ContratoModalidadeVO cmNovo) throws Exception {
        // se a modalidade antiga e a nova não possuem turma
        if (cmNovo.getContratoModalidadeTurmaVOs().isEmpty() && cmAntigo.getContratoModalidadeTurmaVOs().isEmpty()) {
            // se a qtde de vezes por semana do novo e do antigo forem iguais
            if (cmNovo.getNrVezesSemana().intValue() != cmAntigo.getNrVezesSemana().intValue()) {
                // altera a modalidade escolhida
                adicionarModalidades(getListaModalidadeAlterada(), cmNovo);
                cmNovo.gerarTextoAlteracao(cmNovo, cmAntigo, null, null);
            }
        } else {
            // se a modalidade antiga nao possui turma e a modalidade nova possui
            if (cmNovo.getContratoModalidadeTurmaVOs().isEmpty() != cmAntigo.getContratoModalidadeTurmaVOs().isEmpty()) {
                // altera a modalidade escolhida
                adicionarModalidades(getListaModalidadeAlterada(), cmNovo);
                cmNovo.gerarTextoAlteracao(cmNovo, cmAntigo, null, null);
            } else {
                Iterator i = cmNovo.getContratoModalidadeTurmaVOs().iterator();
                boolean incluir, alterar;
                // percorre as turmas do contrato novo
                while (i.hasNext()) {
                    ContratoModalidadeTurmaVO cmtNovo = (ContratoModalidadeTurmaVO) i.next();
                    // variavel que controla inclusao de turmas das modalidades
                    incluir = true;
                    // percorre as turmas do contrato antigo
                    for (Object o : cmAntigo.getContratoModalidadeTurmaVOs()) {
                        ContratoModalidadeTurmaVO cmtAntigo = (ContratoModalidadeTurmaVO) o;
                        //se as turmas sao iguais
                        if (cmtNovo.getTurma().getCodigo().intValue() == cmtAntigo.getTurma().getCodigo().intValue()) {
                            processarHorarios(cmtNovo, cmtAntigo, cmNovo, cmAntigo);
                            // nao precisara incluir
                            incluir = false;
                            break;
                        }
                    }
                    // se turma do novo nao esta no antigo
                    if (incluir) {
                        // coloca a modalidade na lista de alterados
                        adicionarModalidades(getListaModalidadeAlterada(), cmNovo);
                        cmNovo.setTextoAlteracao(cmNovo.getTextoAlteracao() + cmNovo.gerarTextoInclusaoTurma(cmNovo, cmtNovo, codigosHorariosNovos));
                    }
                }
                // percorre todas as turmas do contrato antigo
                for (Object o : cmAntigo.getContratoModalidadeTurmaVOs()) {
                    ContratoModalidadeTurmaVO cmtAntigo = (ContratoModalidadeTurmaVO) o;
                    i = cmNovo.getContratoModalidadeTurmaVOs().iterator();
                    alterar = true;
                    // percorre todas as turmas do novo contrato
                    while (i.hasNext()) {
                        ContratoModalidadeTurmaVO cmtNovo = (ContratoModalidadeTurmaVO) i.next();
                        // se turma do antigo esta no contrato novo
                        if (cmtAntigo.getTurma().getCodigo().intValue() == cmtNovo.getTurma().getCodigo().intValue()) {
                            // nao precisara registrar alteracao
                            alterar = false;
                            break;
                        }
                    }
                    // se turma do antigo nao esta no contrato novo
                    if (alterar) {
                        // desmarca os horarios para nao afetar o final do processo
                        i = cmtAntigo.getContratoModalidadeHorarioTurmaVOs().iterator();
                        while (i.hasNext()) {
                            ContratoModalidadeHorarioTurmaVO aux = (ContratoModalidadeHorarioTurmaVO) i.next();
                            aux.getHorarioTurma().setHorarioTurmaEscolhida(false);
                        }
                        cmtAntigo.getTurma().setTurmaEscolhida(false);
                        // adiciona na lista de horarioturma desta turma
                        cmNovo.getContratoModalidadeTurmaVOs().add(cmtAntigo.getClone(true));
                        adicionarModalidades(getListaModalidadeAlterada(), cmNovo);
                        cmNovo.gerarTextoAlteracao(cmNovo, cmAntigo, cmtAntigo, null);
                    }
                }
            }
        }
    }

    public void processarHorarios(ContratoModalidadeTurmaVO cmtNovo, ContratoModalidadeTurmaVO cmtAntigo, ContratoModalidadeVO cmNovo, ContratoModalidadeVO cmAntigo) throws Exception {
        // percorre todos os horario-turmas do novo contrato
        for (Object o : cmtNovo.getContratoModalidadeHorarioTurmaVOs()) {
            ContratoModalidadeHorarioTurmaVO cmhtNovo = (ContratoModalidadeHorarioTurmaVO) o;
                // se ate os horarios forem iguais
            if (!cmtAntigo.existeHorarioTurma(cmhtNovo.getHorarioTurma().getCodigo())) {
                // prepara para alteracao
                adicionarModalidades(getListaModalidadeAlterada(), cmNovo);
                codigosHorariosNovos.add(cmhtNovo.getHorarioTurma().getCodigo());
                cmNovo.gerarTextoInclusaoDeHorario(cmNovo, null, null, cmhtNovo, cmhtNovo);
            }
        }
        boolean altera;
        // percorre todos os horario-turmas do contrato antigo
        for (Object o : cmtAntigo.getContratoModalidadeHorarioTurmaVOs()) {
            ContratoModalidadeHorarioTurmaVO cmhtAntigo = (ContratoModalidadeHorarioTurmaVO) o;
            Iterator i = cmtNovo.getContratoModalidadeHorarioTurmaVOs().iterator();
            altera = true;
            // percorre todos os horario-turmas do novo contrato
            while (i.hasNext()) {
                ContratoModalidadeHorarioTurmaVO cmhtNovo = (ContratoModalidadeHorarioTurmaVO) i.next();
                // se horario do antigo esta no contrato novo
                if (cmhtAntigo.getHorarioTurma().getCodigo().intValue() == cmhtNovo.getHorarioTurma().getCodigo().intValue()) {
                    // nao precisara registrar alteracao
                    altera = false;
                    break;
                }
            }
            // se horario do antigo nao esta no contrato novo
            if (altera) {
                // desmarca o horario para nao afetar o final do processo
                cmhtAntigo.getHorarioTurma().setHorarioTurmaEscolhida(false);
                // adiciona na lista de horarioturma desta turma
                cmtNovo.getContratoModalidadeHorarioTurmaVOs().add(cmhtAntigo);
                adicionarModalidades(getListaModalidadeAlterada(), cmNovo);
                cmNovo.gerarTextoExclusaoDeHorario(cmNovo, null, null, cmhtAntigo);
            }
        }
    }

    public ContratoModalidadeVO adicionarModalidades(List aux, ContratoModalidadeVO obj) {
        for (Object anAux : aux) {
            ContratoModalidadeVO objExistente = (ContratoModalidadeVO) anAux;
            if (objExistente.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())) {
                return objExistente;
            }
        }
        aux.add(obj);
        return null;
    }

    private void calcularContrato() {
        try {
            if (this.contratoNovo.isVendaCreditoTreino() && !this.contratoNovo.isVendaCreditoSessao()){
                return;
            }
            Double valorMensalAntigo = getFacade().getContrato().calcularValorContrato((ContratoVO) contratoAntigo.getClone(true), false, true);
            getFacade().getContrato().calcularValorContrato(contratoNovo, true, true);
            if (contratoNovo.getDiaVencimentoProrata() > 0) {
                double valorProrata = 0.0;
                int nrProdutosPlano = 0;
                int nrProdutosProRata = 0;
                double valorProdMensal = 0.0;
                for (Object obj : contratoNovo.getMovProdutoVOs()) {
                    MovProdutoVO movProd = (MovProdutoVO) obj;
                    // se é pro-rata
                    if (movProd.getDescricao().contains("PRO-RATA")) {
                        valorProrata = movProd.getTotalFinal();
                        nrProdutosProRata++;
                    } else if (movProd.getProduto().getTipoProduto().equals("PM")) {
                        nrProdutosPlano++;
                        valorProdMensal = movProd.getTotalFinal();
                    }
                }
                if (nrProdutosPlano == 0 && nrProdutosProRata > 0) {
                    contratoNovo.setValorBaseCalculo(contratoNovo.getValorBaseCalculo() - valorMensalAntigo + valorProrata);
                } else if (nrProdutosPlano < contratoAntigo.getContratoDuracao().getNumeroMeses()) {
                    contratoNovo.setValorBaseCalculo(contratoNovo.getValorBaseCalculo() - valorProdMensal);
                    contratoNovo.setValorFinal(contratoNovo.getValorFinal() - valorProdMensal);
                }
                // valor calculado na função anterior não considera o valor do pro-rata
                // por isso este valor é somado aqui
                if (nrProdutosPlano > 0) {
                    contratoNovo.setValorBaseCalculo(contratoNovo.getValorBaseCalculo() + valorProrata);
                    contratoNovo.setValorFinal(contratoNovo.getValorFinal() + valorProrata);
                }
            }
            valorAlteracaoModalidadeNovo = Uteis.arredondarForcando2CasasDecimais((contratoNovo.getValorBaseCalculo() - contratoAntigo.getValorBaseCalculo()));
            valorDiaModalidadeNovo = Uteis.arredondarForcando2CasasDecimais(valorAlteracaoModalidadeNovo / nrDiasContrato);
            valorModalidadeNovo = valorDiaModalidadeNovo * restanteDiasContrato;
            if(valorModalidadeNovo > valorAlteracaoModalidadeNovo){ // pode acontecer por causa de arredondamento
                valorModalidadeNovo = valorAlteracaoModalidadeNovo;
            }
            valorTotalAlteracaoModalidade = valorModalidadeNovo;
            valorFinal = valorTotalAlteracaoModalidade;
            apresentarDadosNovoModalidade = true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void desfazerAlteracoes() {
        try {
            novo();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String voltar() {
        setMensagemDetalhada("", "");
        return "voltar";
    }

    public void obterLiberacao() throws Exception {
        try {
            if (!abrirConfirmacaoLiberacao) {
                responsavelLiberacao = new UsuarioVO();
                if (getUsuarioLogado().getCodigo() != 0) {
                    responsavelLiberacao.setCodigo(getUsuarioLogado().getCodigo());
                    responsavelLiberacao.setUsername(getUsuarioLogado().getUsername());
                    responsavelLiberacao.setUserOamd(getUsuarioLogado().getUserOamd());
                    getResponsavelLiberacao().setNome(getUsuarioLogado().getNome());
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarResponsavel() {
        try {
            responsavel = getFacade().getUsuario().consultarPorChavePrimaria(responsavel.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getResponsavel().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarUsuarioSenhaResponsavelLiberacao() {
        try {
            responsavelLiberacao = getFacade().getUsuario().consultarPorChavePrimaria(responsavelLiberacao.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getResponsavelLiberacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String gravar() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                validarOperacoesFuturas();
                inicializarDadosOperacaoAlterarModalidade();
                ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getContratoAntigo());
                List<MovParcelaVO> listaParcelas = processarParcelasManutencao();
                getFacade().getContratoOperacao().incluirOperacaoManutencaoModalidade(listaModalidadeAdicionada, listaModalidadeExcluida,
                        listaModalidadeAlterada, listaContratoOperacao, contratoNovo, contratoAntigo, valorTotalAlteracaoModalidade,
                        valorFinal, getApresentarParcela(), false, responsavel, restanteDiasContrato, (getMostrarAlterarValor() || isApresentarTiposDesconto()),
                        listaTodosHorarioTurmaCreditoTreino, listaParcelas);
                getFacade().getSituacaoClienteSinteticoDW().atualizarInformacoesCrossfit(getKey(), true, contratoNovo.getPessoa().getCodigo());

                resolverMovProdutoModalidade();
                setMensagemDetalhada("msg_dados_gravados", "");
                setApresentarBotao(false);
                setMostrarPanelConfirmacaoAlterar(false);
                setMostrarTiposDesconto(false);
                setMostrarCampoAlterarValor(false);
                setMsgAlert("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({close: true});");
                notificarRecursoEmpresa(RecursoSistema.MANUTENCAO_MODALIDADE_SUCESSO,ContadorTempo.encerraContagem());

                try {
                    if (getFacade().getMgbService().integradoMgb(getEmpresaLogado().getCodigo())) {
                        getFacade().getMgbService().syncAlunoMgb(getEmpresaLogado().getCodigo(), null, contratoNovo.getPessoa().getCodigo());
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ManutencaoModalidadeControle.class);
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMostrarPanelConfirmacaoAlterar(true);
                setApresentarBotao(true);
                setMensagemDetalhada("msg_erro", e.getMessage());
                setProcessandoOperacao(false);
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        autorizarAcao(auto, listener);
        setProcessandoOperacao(false);
        return "";
    }

    private List<MovParcelaVO> processarParcelasManutencao() throws Exception {
        parcelasEmAberto = getFacade().getMovParcela().consultarEmAbertoPorContrato(contratoAntigo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(contratoAntigo.getPlano().getDividirManutencaoParcelasEA() && !UteisValidacao.emptyList(parcelasEmAberto)){
            List<MovParcelaVO> parcelasEmAbertoSemAlteracao = getFacade().getMovParcela().consultarParcelasFuturasPMContrato(contratoAntigo.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Double valorPorParcela = Uteis.arredondarForcando2CasasDecimais(valorFinal/ parcelasEmAberto.size());
            Double diferenca = Uteis.arredondarForcando2CasasDecimais( valorFinal - (valorPorParcela  * parcelasEmAberto.size()));
            for (MovParcelaVO parcela : parcelasEmAbertoSemAlteracao){
                parcela.setValorBaseCalculo(diferenca + valorPorParcela);
                parcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela() + parcela.getValorBaseCalculo()));
                diferenca = 0.00;
            }
            Ordenacao.ordenarLista(parcelasEmAbertoSemAlteracao, "dataVencimento");
            return parcelasEmAbertoSemAlteracao;
        }
        return null;
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 15/05/2013
     */
    private void resolverMovProdutoModalidade() throws Exception, InstantiationException {
        if (getFacade().getMovProdutoModalidade().validarExisteMovProdModalidade(contratoAntigo.getCodigo())) {
            if(listaModalidadeAdicionada.isEmpty() && listaModalidadeExcluida.isEmpty()){
                return;
            }
            
            List<ContratoModalidadeVO> modalidadesAlteradasAdicionar = new ArrayList<ContratoModalidadeVO>();
            List<ContratoModalidadeVO> modalidadesAlteradasRetirar = new ArrayList<ContratoModalidadeVO>();

            for (ContratoModalidadeVO contMod : listaModalidadeAlterada) {
                modalidadesAlteradasRetirar.add(contMod);
                ContratoModalidadeVO modFakeAdicionar = (ContratoModalidadeVO) contMod.getClone(true);
                modFakeAdicionar.setValorFinalModalidade(contMod.getValorFinalModalidade());
                modalidadesAlteradasAdicionar.add(modFakeAdicionar);
            }
            modalidadesAlteradasAdicionar.addAll(listaModalidadeAdicionada);
            modalidadesAlteradasRetirar.addAll(listaModalidadeExcluida);
            getFacade().getMovProdutoModalidade().realizarManutencaoMovProdutoModalidade(modalidadesAlteradasAdicionar, modalidadesAlteradasRetirar, contratoNovo, contratoAntigo);
        }
    }

    public void gravarComResponsavelLiberacao() {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                validarOperacoesFuturas();
                validarPermissaoLiberacaoAlterarModalidade(auto.getUsuario());
                setOpcaoAlterarValor(getOpcaoUsada());
                inicializarDadosOperacaoAlterarModalidade();
                    getFacade().getContratoOperacao().incluirOperacaoManutencaoModalidade(listaModalidadeAdicionada, listaModalidadeExcluida,
                            listaModalidadeAlterada, listaContratoOperacao, contratoNovo, contratoAntigo, valorTotalAlteracaoModalidade, valorFinal,
                            true, true, responsavelLiberacao, restanteDiasContrato, getMostrarAlterarValor(), listaTodosHorarioTurmaCreditoTreino, null);
                getFacade().getSituacaoClienteSinteticoDW().atualizarInformacoesCrossfit(getKey(), true, contratoNovo.getPessoa().getCodigo());

                resolverMovProdutoModalidade();

                setMensagemDetalhada("msg_dados_gravados", "");
                setMostrarTiposDesconto(false);
                setApresentarBotao(false);
                setMostrarPanelConfirmacaoAlterar(false);
                setAbrirConfirmacaoLiberacao(false);
                setMostrarCampoAlterarValor(getMostrarAlterarValor());
                apresentarMsgLiberacao= true;
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMostrarPanelConfirmacaoAlterar(false);
                setAbrirConfirmacaoLiberacao(true);
                setApresentarBotao(true);
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public void onFecharModalAutorizacao() {
                fecharLiberarValor();
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Liberar Manutenção Modalidade", "LiberarManutencaoModalidade", "Você precisa da permissão \"3.17 - Lançamento de liberação da manutenção de modalidade de contrato\"", "form", listener);
        setProcessandoOperacao(false);
    }

    public void gravarAlteracaoValor() {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                validarAlteracaoValorManutencaoModalidade(auto.getUsuario());
                setOpcaoAlterarValor(getOpcaoUsada());
                setMostrarCampoAlterarValor(getMostrarAlterarValor());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMostrarPanelConfirmacaoAlterar(false);
                setAbrirConfirmacaoLiberacao(true);
                setApresentarBotao(true);
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };

        setMostrarPanelConfirmacaoAlterar(true);
        setOpcaoAlterarValor("Manter Valor");
        auto.autorizar("Alterar valor e Aplicar Desconto Manutenção Modalidade", "AlterarValorManutencaoModalidade",
                "Você precisa da permissão \"3.31 - Alterar e dar desconto na manutenção de modalidade\"",
                "form", listener);
    }

    public void validarPermissaoAlterarModalidade(UsuarioVO usuario) throws Exception {
        setMensagemDetalhada("", "");
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não possui perfil de acesso.");
        }
        if (contratoNovo.getEmpresa().getCodigo() == 0) {
            throw new Exception("Não há nenhuma empresa logada.");
        }
        for (Object o : usuario.getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (contratoNovo.getEmpresa().getCodigo().intValue() == usuarioPerfilAcesso.getEmpresa().getCodigo()) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                        usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        usuario, "ManutencaoModalidade_Autorizar", "3.14 - Manutenção Modalidade - Autorizar");
            }
        }
    }

    public void validarPermissaoLiberacaoAlterarModalidade(UsuarioVO usuario) throws Exception {
        setMensagemDetalhada("", "");
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        if (contratoNovo.getEmpresa().getCodigo() == 0) {
            throw new Exception("Não há nenhuma empresa logada.");
        }
        for (Object o : usuario.getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (contratoNovo.getEmpresa().getCodigo().intValue() == usuarioPerfilAcesso.getEmpresa().getCodigo()) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                        usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(
                        usuarioPerfilAcesso.getPerfilAcesso(), usuario,
                        "LiberarManutencaoModalidade", "3.17 - Liberar Manutenção Modalidade");
            }
        }
    }

    public void validarAlteracaoValorManutencaoModalidade(UsuarioVO usuario) throws Exception {
        setMensagemDetalhada("", "");
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        if (contratoNovo.getEmpresa().getCodigo() == 0) {
            throw new Exception("Não há nenhuma empresa logada.");
        }
        for (Object o : usuario.getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (contratoNovo.getEmpresa().getCodigo().intValue() == usuarioPerfilAcesso.getEmpresa().getCodigo()) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                        usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(
                        usuarioPerfilAcesso.getPerfilAcesso(), usuario,
                        "AlterarValorManutencaoModalidade", "3.31 - Alterar valor e Aplicar Desconto Manutenção Modalidade - Autorizar");
            }
        }
    }

    public void inicializarDadosOperacaoAlterarModalidade() {
        listaContratoOperacao = new ArrayList<ContratoOperacaoVO>();
        if (!getListaModalidadeAdicionada().isEmpty()) {
            operacaoAdicionarModalidade();
        }
        if (!getListaModalidadeAlterada().isEmpty()) {
            operacaoAlterarModalidade();
        }
        if (!getListaModalidadeExcluida().isEmpty()) {
            operacaoExcluirModalidade();
        }
    }

    public void operacaoAdicionarModalidade() {
        for (ContratoModalidadeVO obj : listaModalidadeAdicionada) {
            ContratoOperacaoVO ope = new ContratoOperacaoVO();
            ope.setContrato(contratoNovo.getCodigo());
            ope.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDescricaoCalculo(obj.getTextoInclusao());
            ope.setObservacao("");
            ope.setTipoOperacao("IM");
            if (getResponsavelLiberacao().getCodigo() != 0) {
                ope.setResponsavel(responsavelLiberacao);
            } else {
                ope.setResponsavel(responsavel);
            }
            if (getApresentarDepositoEmConta()) {
                ope.setOperacaoPaga(false);
            } else {
                ope.setOperacaoPaga(true);
            }
            listaContratoOperacao.add(ope);
        }
    }

    public void operacaoAlterarModalidade() {
        for (ContratoModalidadeVO obj : listaModalidadeAlterada) {
            ContratoOperacaoVO ope = new ContratoOperacaoVO();
            ope.setContrato(contratoNovo.getCodigo());
            ope.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDescricaoCalculo(obj.getTextoAlteracao());
            ope.setObservacao("");
            ope.setTipoOperacao("AM");
            if (getResponsavelLiberacao().getCodigo() != 0) {
                ope.setResponsavel(responsavelLiberacao);
            } else {
                ope.setResponsavel(responsavel);
            }
            if (getApresentarDepositoEmConta() && responsavelLiberacao == null) {
                ope.setOperacaoPaga(false);
            } else {
                ope.setOperacaoPaga(true);
            }
            listaContratoOperacao.add(ope);
        }
    }

    public void operacaoExcluirModalidade() {
        for (ContratoModalidadeVO obj : listaModalidadeExcluida) {
            ContratoOperacaoVO ope = new ContratoOperacaoVO();
            ope.setContrato(contratoNovo.getCodigo());
            ope.setDataFimEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDataInicioEfetivacaoOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDataOperacao(negocio.comuns.utilitarias.Calendario.hoje());
            ope.setDescricaoCalculo(obj.getTextoExclusao());
            ope.setObservacao("");
            ope.setTipoOperacao("EM");
            if (getResponsavelLiberacao().getCodigo() != 0) {
                ope.setResponsavel(responsavelLiberacao);
            } else {
                ope.setResponsavel(responsavel);
            }
            if (getApresentarDepositoEmConta() && responsavelLiberacao == null) {
                ope.setOperacaoPaga(false);
            } else {
                ope.setOperacaoPaga(true);
            }
            listaContratoOperacao.add(ope);
        }
    }

// ********* gets and sets *****************************************************
    public String getAbrirPanelConfirmacaoAlterar() {
        if (mostrarPanelConfirmacaoAlterar) {
            return "Richfaces.showModalPanel('panelConfirmacaoAlterarModalidade');fireElementFromParent('form:btnAtualizaCliente');";
        } else {
            return "Richfaces.hideModalPanel('panelConfirmacaoAlterarModalidade');fireElementFromParent('form:btnAtualizaCliente');";
        }
    }

    public String getFecharPanelConfirmacaoLiberacao() {
        if (abrirConfirmacaoLiberacao) {
            return "Richfaces.showModalPanel('panelConfirmacaoLiberacaoPagamento');fireElementFromParent('form:btnAtualizaCliente');";
        } else {
            return "Richfaces.hideModalPanel('panelConfirmacaoLiberacaoPagamento');fireElementFromParent('form:btnAtualizaCliente');";
        }
    }

    public Boolean getApresentarLiberacao() {
        return (getApresentarParcela() && getValorTotalAlteracaoModalidade() > 0);
    }

    public Boolean getApresentarDepositoEmConta() {
        return (contratoAntigo.getValorBaseCalculo() > contratoNovo.getValorBaseCalculo());
    }

    public Boolean getApresentarParcela() {
        return (contratoNovo.getValorBaseCalculo() >= contratoAntigo.getValorBaseCalculo());
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public UsuarioVO getResponsavelLiberacao() {
        return responsavelLiberacao;
    }

    public void setResponsavelLiberacao(UsuarioVO responsavelLiberacao) {
        this.responsavelLiberacao = responsavelLiberacao;
    }

    public ContratoVO getContratoNovo() {
        return contratoNovo;
    }

    public void setContratoNovo(ContratoVO contratoNovo) {
        this.contratoNovo = contratoNovo;
    }

    public ContratoVO getContratoAntigo() {
        return contratoAntigo;
    }

    public void setContratoAntigo(ContratoVO contratoAntigo) {
        this.contratoAntigo = contratoAntigo;
    }

    public ContratoModalidadeVO getContratoModalidade() {
        return contratoModalidade;
    }

    public void setContratoModalidade(ContratoModalidadeVO contratoModalidade) {
        this.contratoModalidade = contratoModalidade;
    }

    public List<ContratoVO> getContratos() {
        return contratos;
    }

    public void setContratos(List<ContratoVO> contratos) {
        this.contratos = contratos;
    }

    public List<PlanoModalidadeVO> getModalidadesPlano() {
        return modalidadesPlano;
    }

    public void setModalidadesPlano(List<PlanoModalidadeVO> modalidadesPlano) {
        this.modalidadesPlano = modalidadesPlano;
    }

    public boolean isMostrarEditorModalidade() {
        return mostrarEditorModalidade;
    }

    public void setMostrarEditorModalidade(boolean mostrarEditorModalidade) {
        this.mostrarEditorModalidade = mostrarEditorModalidade;
    }

    public double getValorAlteracaoModalidadeNovo() {
        return valorAlteracaoModalidadeNovo;
    }

    public void setValorAlteracaoModalidadeNovo(double valorAlteracaoModalidadeNovo) {
        this.valorAlteracaoModalidadeNovo = valorAlteracaoModalidadeNovo;
    }

    public double getValorDiaModalidadeNovo() {
        return valorDiaModalidadeNovo;
    }

    public void setValorDiaModalidadeNovo(double valorDiaModalidadeNovo) {
        this.valorDiaModalidadeNovo = valorDiaModalidadeNovo;
    }

    public double getValorModalidadeNovo() {
        return valorModalidadeNovo;
    }

    public void setValorModalidadeNovo(double valorModalidadeNovo) {
        this.valorModalidadeNovo = valorModalidadeNovo;
    }

    public double getValorTotalAlteracaoModalidade() {
        return valorTotalAlteracaoModalidade;
    }

    public void setValorTotalAlteracaoModalidade(double valorTotalAlteracaoModalidade) {
        this.valorTotalAlteracaoModalidade = valorTotalAlteracaoModalidade;
    }

    public int getNrDiasContrato() {
        return nrDiasContrato;
    }

    public void setNrDiasContrato(int nrDiasContrato) {
        this.nrDiasContrato = nrDiasContrato;
    }

    public int getNrDiasContratoUtilizado() {
        return nrDiasContratoUtilizado;
    }

    public void setNrDiasContratoUtilizado(int nrDiasContratoUtilizado) {
        this.nrDiasContratoUtilizado = nrDiasContratoUtilizado;
    }

    public int getRestanteDiasContrato() {
        return restanteDiasContrato;
    }

    public void setRestanteDiasContrato(int restanteDiasContrato) {
        this.restanteDiasContrato = restanteDiasContrato;
    }

    public boolean isConsultaTurma() {
        return consultaTurma;
    }

    public void setConsultaTurma(boolean consultaTurma) {
        this.consultaTurma = consultaTurma;
    }

    public List<ContratoModalidadeVO> getListaModalidadeAdicionada() {
        return listaModalidadeAdicionada;
    }

    public void setListaModalidadeAdicionada(List<ContratoModalidadeVO> listaModalidadeAdicionada) {
        this.listaModalidadeAdicionada = listaModalidadeAdicionada;
    }

    public List<ContratoModalidadeVO> getListaModalidadeAlterada() {
        return listaModalidadeAlterada;
    }

    public void setListaModalidadeAlterada(List<ContratoModalidadeVO> listaModalidadeAlterada) {
        this.listaModalidadeAlterada = listaModalidadeAlterada;
    }

    public List<ContratoModalidadeVO> getListaModalidadeExcluida() {
        return listaModalidadeExcluida;
    }

    public void setListaModalidadeExcluida(List<ContratoModalidadeVO> listaModalidadeExcluida) {
        this.listaModalidadeExcluida = listaModalidadeExcluida;
    }

    public boolean isApresentarBotao() {
        return apresentarBotao;
    }

    public void setApresentarBotao(boolean apresentarBotao) {
        this.apresentarBotao = apresentarBotao;
    }

    public boolean isApresentarDadosNovoModalidade() {
        return apresentarDadosNovoModalidade;
    }

    public void setApresentarDadosNovoModalidade(boolean apresentarDadosNovoModalidade) {
        this.apresentarDadosNovoModalidade = apresentarDadosNovoModalidade;
    }

    public boolean isMostrarPanelConfirmacaoAlterar() {
        return mostrarPanelConfirmacaoAlterar;
    }

    public void setMostrarPanelConfirmacaoAlterar(boolean mostrarPanelConfirmacaoAlterar) {
        this.mostrarPanelConfirmacaoAlterar = mostrarPanelConfirmacaoAlterar;
    }

    public boolean isAbrirConfirmacaoLiberacao() {
        return abrirConfirmacaoLiberacao;
    }

    public void setAbrirConfirmacaoLiberacao(boolean abrirConfirmacaoLiberacao) {
        this.abrirConfirmacaoLiberacao = abrirConfirmacaoLiberacao;
    }

    public List<PlanoModalidadeVezesSemanaVO> getListaVezesSemana() {
        return listaVezesSemana;
    }

    public void setListaVezesSemana(List<PlanoModalidadeVezesSemanaVO> listaVezesSemana) {
        this.listaVezesSemana = listaVezesSemana;
    }

    public boolean isPeriodoBonus() {
        return periodoBonus;
    }

    public void setPeriodoBonus(boolean periodoBonus) {
        this.periodoBonus = periodoBonus;
    }

    public List<SelectItem> getOpcoesAlterarValor() {
        List<SelectItem> opcoes = new ArrayList<SelectItem>();
        opcoes.add(new SelectItem("Manter Valor", "Manter Valor"));
        opcoes.add(new SelectItem("Alterar Valor", "Alterar Valor"));
        opcoes.add(new SelectItem("Liberar", "Liberar"));
        if (getApresentarLiberacao()) {
            opcoes.add(new SelectItem("Aplicar desconto", "Aplicar desconto"));
        }

        return opcoes;
    }

    public String getOpcaoAlterarValor() {
        return opcaoAlterarValor;
    }

    public void setOpcaoAlterarValor(String opcaoAlterarValor) {
        this.opcaoAlterarValor = opcaoAlterarValor;
    }

    public String getTipoDesconto() {
        return tipoDesconto;
    }

    public void setTipoDesconto(String tipoDesconto) {
        this.tipoDesconto = tipoDesconto;
    }

    public boolean isApresentarTiposDesconto() {
        return getOpcaoAlterarValor().equals("Aplicar desconto") && isMostrarTiposDesconto();
    }

    public List<SelectItem> getOpcoesDesconto() {
        List<SelectItem> opcoes = new ArrayList<SelectItem>();
        opcoes.add(new SelectItem("Valor", "Valor"));
        opcoes.add(new SelectItem("Porcentagem", "Porcentagem"));

        return opcoes;
    }

    public boolean isApresentarCampoValorDesconto() {
        return !UteisValidacao.emptyString(getTipoDesconto());
    }


    public void fecharLiberarValor() {
        try {
            opcaoAlterarValor = "Manter Valor";
            alterarOpcaoAlterarValor();
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    public Object alterarOpcaoAlterarValor() throws Exception {
        setDesconto(0);
        calcularValorFinal();
        valorAlterado = valorFinal;
        setMostrarCampoAlterarValor(false);
        setApresentarValorFinalModificado(false);
        setAbrirConfirmacaoLiberacao(false);
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        if (getOpcaoAlterarValor().equals("Liberar")) {
            opcaoUsada = "Aplicar desconto";
            gravarComResponsavelLiberacao();
            return true;
        } /*else if (getOpcaoAlterarValor().equals("Aplicar desconto")) {
         setApresentarValorFinalModificado(true);
         }*/

        if (getOpcaoAlterarValor().equals("Aplicar desconto")) {
            opcaoUsada = "Aplicar desconto";
            setApresentarValorFinalModificado(true);
            gravarAlteracaoValor();
            return true;
        }

        if (getOpcaoAlterarValor().equals("Alterar Valor")) {
            opcaoUsada = "Alterar Valor";
            setMostrarPanelConfirmacaoAlterar(true);
            gravarAlteracaoValor();
            return true;
        }

        auto.setPedirPermissao(false);
        return true;
    }

    public Object calcularValorFinal() {
        setMensagemDetalhada("");
        if (!UteisValidacao.emptyString(getTipoDesconto()) && getTipoDesconto().equals("Valor")) {
            if (getDesconto() > getValorTotalAlteracaoModalidade()) {
                setMensagemDetalhada("O valor do desconto é superior ao da manutenção");
            } else if (getDesconto() == getValorTotalAlteracaoModalidade()) {
                setMensagemDetalhada("Para o desconto de 100% utilize a Liberação!");
            } else {
                setApresentarValorFinalModificado(true);
                setValorFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorTotalAlteracaoModalidade() - getDesconto()));
            }
        } else if (!UteisValidacao.emptyString(getTipoDesconto()) && getTipoDesconto().equals("Porcentagem")) {
            if (getDesconto() > 100) {
                setMensagemDetalhada("O valor do desconto é superior ao da manutenção.");
            } else if (getDesconto() == 100) {
                setMensagemDetalhada("Para o desconto de 100% utilize a Liberação!");
            } else {
                setApresentarValorFinalModificado(true);
                setValorFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorTotalAlteracaoModalidade() * (1 - (getDesconto() / 100))));
            }
        } else {
            setApresentarValorFinalModificado(false);
            setValorFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorTotalAlteracaoModalidade()));
        }

        return true;
    }

    public double getDesconto() {
        return desconto;
    }

    public void setDesconto(double desconto) {
        this.desconto = desconto;
    }

    public double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public Object alterarOpcaoDesconto() {
        setDesconto(0);
        calcularValorFinal();
        return true;
    }

    public boolean isApresentarValorFinalModificado() {
        return apresentarValorFinalModificado;
    }

    public void setApresentarValorFinalModificado(boolean apresentarValorFinalModificado) {
        this.apresentarValorFinalModificado = apresentarValorFinalModificado;
    }

    public boolean isMostrarTiposDesconto() {
        return mostrarTiposDesconto;
    }

    public void setMostrarTiposDesconto(boolean mostrarTiposDesconto) {
        this.mostrarTiposDesconto = mostrarTiposDesconto;
    }

    public void setValorAlterado(Double valorAlterado) {
        this.valorAlterado = valorAlterado;
    }

    public Double getValorAlterado() {
        return valorAlterado;
    }

    public void setMostrarCampoAlterarValor(boolean mostrarCampoAlterarValor) {
        this.mostrarCampoAlterarValor = mostrarCampoAlterarValor;
    }

    public boolean getMostrarCampoAlterarValor() {
        return mostrarCampoAlterarValor;
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public String getOpcaoUsada() {
        return opcaoUsada;
    }

    public void setOpcaoUsada(String opcaoUsada) {
        this.opcaoUsada = opcaoUsada;
    }

    public Boolean getContratoPossuiParcelaComRemessa() {
        if (contratoPossuiParcelaComRemessa == null){
            contratoPossuiParcelaComRemessa  = false;
        }
        return contratoPossuiParcelaComRemessa;
    }

    public void setContratoPossuiParcelaComRemessa(Boolean contratoPossuiParcelaComRemessa) {
        this.contratoPossuiParcelaComRemessa = contratoPossuiParcelaComRemessa;
    }

    public List<MovParcelaVO> getListaParcelasRemessa() {
        if (listaParcelasRemessa == null){
            listaParcelasRemessa = new ArrayList<MovParcelaVO>(0);
        }
        return listaParcelasRemessa;
    }

    public void setListaParcelasRemessa(List<MovParcelaVO> listaParcelasRemessa) {
        this.listaParcelasRemessa = listaParcelasRemessa;
    }

    private void inicializarValoresModalidadesAtuais() {
        for (ContratoModalidadeVO contMod: getContratoAntigo().getContratoModalidadeVOs()){
            setContratoModalidade(contMod);
            gravarModalidadeVezes(true);
        }
        setContratoModalidade(new ContratoModalidadeVO());
    }

    public double getValorUnitarioCreditoTreino() {
        return valorUnitarioCreditoTreino;
    }

    public void setValorUnitarioCreditoTreino(double valorUnitarioCreditoTreino) {
        this.valorUnitarioCreditoTreino = valorUnitarioCreditoTreino;
    }

    public Integer getDiferencaQuantidadeCreditoTreino() {
        return diferencaQuantidadeCreditoTreino;
    }

    public void setDiferencaQuantidadeCreditoTreino(Integer diferencaQuantidadeCreditoTreino) {
        this.diferencaQuantidadeCreditoTreino = diferencaQuantidadeCreditoTreino;
    }

    public Integer getNrVezesPorSemanaEscolhido() {
        return nrVezesPorSemanaEscolhido;
    }

    public void setNrVezesPorSemanaEscolhido(Integer nrVezesPorSemanaEscolhido) {
        this.nrVezesPorSemanaEscolhido = nrVezesPorSemanaEscolhido;
    }

    public String getLabelValorUnitarioCreditoTreino(){
        Integer vezesSemana = this.nrVezesPorSemanaEscolhido;
        if (contratoNovo.getDiferencaQtdeCreditoTreinoManutencaoModalidade() <= 0){
            vezesSemana = this.contratoNovo.getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana();
        }
        return "Valor unitário " + vezesSemana + "x por semana:";
    }

    public String getLabelValorDiferencaCreditoTreino(){
        Integer vezesSemana = this.nrVezesPorSemanaEscolhido;
        if (contratoNovo.getDiferencaQtdeCreditoTreinoManutencaoModalidade() <= 0){
           return "Quantidade de crédito a devolver: ";
        }else{
            return "Quantidade de crédito a comprar: ";
        }
    }

    public String getValorDiferencaQuantidadeCreditoTreino(){
        if (this.diferencaQuantidadeCreditoTreino > 0){
            return  this.diferencaQuantidadeCreditoTreino.toString();
        }else{
            return String.valueOf(this.diferencaQuantidadeCreditoTreino * -1);
        }
    }

    public String getLabelQuantidadeCreditoTreinoNecessario(){
        return " Quantidade crédito treino necessário para " + this.nrVezesPorSemanaEscolhido + "x por semana " +
                "para a vigência " + Uteis.getData(Calendario.hoje()) + " até " + Uteis.getData(this.contratoNovo.getVigenciaAteAjustada()) + ": ";
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public Integer getTotalCreditoPossivelUtilizar() {
        return totalCreditoPossivelUtilizar;
    }

    public void setTotalCreditoPossivelUtilizar(Integer totalCreditoPossivelUtilizar) {
        this.totalCreditoPossivelUtilizar = totalCreditoPossivelUtilizar;
    }

    public boolean isAlterouVezesSemanaContratoCreditoTreino() {
        return alterouVezesSemanaContratoCreditoTreino;
    }

    public void setAlterouVezesSemanaContratoCreditoTreino(boolean alterouVezesSemanaContratoCreditoTreino) {
        this.alterouVezesSemanaContratoCreditoTreino = alterouVezesSemanaContratoCreditoTreino;
    }

    public List<HorarioTurmaVO> getListaTodosHorarioTurmaCreditoTreino() {
        return listaTodosHorarioTurmaCreditoTreino;
    }

    public void setListaTodosHorarioTurmaCreditoTreino(List<HorarioTurmaVO> listaTodosHorarioTurmaCreditoTreino) {
        this.listaTodosHorarioTurmaCreditoTreino = listaTodosHorarioTurmaCreditoTreino;
    }

    public boolean isAlterouHorarioTurmaCreditoTreino() {
        return alterouHorarioTurmaCreditoTreino;
    }

    public void setAlterouHorarioTurmaCreditoTreino(boolean alterouHorarioTurmaCreditoTreino) {
        this.alterouHorarioTurmaCreditoTreino = alterouHorarioTurmaCreditoTreino;
    }

    public void validarOcupacao(ContratoModalidadeVO cm) {
        cm.setAdvertenciaAcimaLimite(false);
        cm.setObstrucaoConclusaoManutencao(false);
        //verifica os atuais com os antigos para evitar erros de avaliacao com dados editados
        List<ContratoModalidadeHorarioTurmaVO> cmhtVOSAntigo = new ArrayList<ContratoModalidadeHorarioTurmaVO>();
        for (ContratoModalidadeVO obj : contratoAntigo.getContratoModalidadeVOs()) {
            if (obj.getModalidade().getCodigo().equals(cm.getModalidade().getCodigo())) {
                cmhtVOSAntigo = obj.getListaContratoModalidadesHorarioTurmaVOs();
                break;
            }
        }
        for (Object cmt : cm.getContratoModalidadeTurmaVOs()) {
            for (Iterator it = ((ContratoModalidadeTurmaVO) cmt).getContratoModalidadeHorarioTurmaVOs().iterator(); it.hasNext();) {
                ContratoModalidadeHorarioTurmaVO cmht = (ContratoModalidadeHorarioTurmaVO) it.next();
                boolean alunoNaTurma = false;
                cmht.getHorarioTurma().setObstrucaoConclusaoManutencao(false);
                cmht.getHorarioTurma().setAdvertenciaAcimaLimite(false);
                for (ContratoModalidadeHorarioTurmaVO antigo : cmhtVOSAntigo) {
                    if (antigo.getHorarioTurma().getCodigo().equals(cmht.getHorarioTurma().getCodigo())) {
                        alunoNaTurma = true;
                        break;
                    }
                }
                if ((cmht.getHorarioTurma().estahLotada(alunoNaTurma) && ((ContratoModalidadeTurmaVO) cmt).getTurma().isBloquearMatriculasAcimaLimite()) || cmht.getHorarioTurma().getSituacao().equals("IN")) {
                    cm.setObstrucaoConclusaoManutencao(true);
                    cmht.getHorarioTurma().setObstrucaoConclusaoManutencao(true);
                } else if (cmht.getHorarioTurma().estahLotada(alunoNaTurma)) {
                    cmht.getHorarioTurma().setAdvertenciaAcimaLimite(true);
                    cm.setAdvertenciaAcimaLimite(true);
                }
            }
        }
    }

    private void validarOperacoesFuturas() throws Exception{
        if (getFacade().getContratoOperacao().existeOperacaoLancadaFuturaAEstaData(getContratoAntigo().getCodigo(), "'IM','AM','EM'", Calendario.hoje())){
             throw new ConsistirException("Existe(m) operação(ões) de manutenção de modalidade futuras. Isso impede a realização dessa operação");
        }
        if(getFacade().getContratoOperacao().existeOperacaoPendenteDeRetorno(getContratoAntigo().getCodigo(), Calendario.hoje())){
            //essas operações desmarcam as aulas do periodo e se forem substituídas, os novos horários não avaliam o periodo de afastamento futuro, deixando o aluno em turmas mesmo estando afastado#18191
             throw new ConsistirException("Existe operação de afastamento vigente ou futura. Essa operação pode ter desmarcado aulas futuras, impedindo a realização dessa operação. Dê retorno dessa operação(vigente) ou estorne(futura), caso queira fazer uma manutenção de modalidade.");
        }
    }

    public void validarParcelaAbertaComBoletoPendente(ContratoVO contrato) throws Exception {
        boolean existeBoletoPendenteDeParcela = false;
        List<MovParcelaVO> listaParcelasContrato = getFacade().getMovParcela().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        for (MovParcelaVO movParcelaVO : listaParcelasContrato) {
            existeBoletoPendenteDeParcela = getFacade().getBoleto().existeBoletoPendentePorMovParcela(movParcelaVO.getCodigo(), true);
            if (existeBoletoPendenteDeParcela){
                throw new ConsistirException("Existem um ou mais boletos pendentes para este contrato. Caso queira fazer uma manutenção de modalidade é necessário cancelar todos os boletos pendentes das parcelas deste contrato antes de prosseguir.");
            }
        }

    }

    public boolean isApresentarMsgLiberacao() {
        return apresentarMsgLiberacao;
    }

    public void setApresentarMsgLiberacao(boolean apresentarMsgLiberacao) {
        this.apresentarMsgLiberacao = apresentarMsgLiberacao;
    }

    public List<Integer> getCodigosHorariosNovos() {
        return codigosHorariosNovos;
    }

    public void setCodigosHorariosNovos(List<Integer> codigosHorariosNovos) {
        this.codigosHorariosNovos = codigosHorariosNovos;
    }
}



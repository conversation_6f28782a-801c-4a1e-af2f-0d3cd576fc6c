package controle.contrato;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.Hashtable;
import java.util.Enumeration;
import java.util.Collections;
import java.util.Date;

import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.contrato.ConvenioDesconto;
import negocio.comuns.utilitarias.*;
import negocio.comuns.contrato.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import javax.faces.event.ActionEvent;
import negocio.comuns.arquitetura.LogVO;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * convenioDescontoForm.jsp convenioDescontoCons.jsp) com as funcionalidades da classe <code>ConvenioDesconto</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see ConvenioDesconto
 * @see ConvenioDescontoVO
 */
public class ConvenioDescontoControle extends SuperControle {

    private String NOME_ENTIDADE = "CONVENIO_DESCONTO";
    private ConvenioDescontoVO convenioDescontoVO;
    private String msgAlert;
    /**
     * Interface <code>ConvenioDescontoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    
    private ConvenioDescontoConfiguracaoVO convenioDescontoConfiguracaoVO;
    private List<SelectItem> selectItemEmpresas = new ArrayList<SelectItem>();

    public ConvenioDescontoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        montarSelectItemEmpresas();
        setMensagemID("");
    }

    private void montarSelectItemEmpresas() throws Exception {
        super.montarListaEmpresas();
        setSelectItemEmpresas(super.getListaEmpresas());
    }

    public void inicializarResponsavel() throws Exception{
        convenioDescontoVO.setResponsavelAutorizacao(getUsuarioLogado());
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>ConvenioDesconto</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() throws Exception {
        setConvenioDescontoVO(new ConvenioDescontoVO());     
        inicializarResponsavel();
        setConvenioDescontoConfiguracaoVO(new ConvenioDescontoConfiguracaoVO());
        convenioDescontoVO.registrarObjetoVOAntesDaAlteracao();
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>ConvenioDesconto</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ConvenioDescontoVO obj = getFacade().getConvenioDesconto().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            obj.registrarConfiguracaoVOsAntesDaAlteracao();
            setConvenioDescontoConfiguracaoVO(new ConvenioDescontoConfiguracaoVO());
            setConvenioDescontoVO(new ConvenioDescontoVO());
            setConvenioDescontoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>ConvenioDesconto</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (!getUsuarioLogado().getAdministrador()) {
                convenioDescontoVO.setEmpresa(getEmpresaLogado());
            }

            if (convenioDescontoVO.getEmpresa().getCodigo() <= 0) {
                throw new ConsistirException("Informe a empresa");
            }

            if (UteisValidacao.emptyNumber(convenioDescontoVO.getCodigo())) {
                getFacade().getConvenioDesconto().incluir(convenioDescontoVO);
                incluirLogInclusao();
            } else {
                getFacade().getConvenioDesconto().alterar(convenioDescontoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = convenioDescontoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), null);
    }
    
    public void realizarConsultaLogObjetoGeral() {
       convenioDescontoVO = new ConvenioDescontoVO();
       realizarConsultaLogObjetoSelecionado();
    }


    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ConvenioDescontoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorCodigo(valorInt, 0, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getConvenioDesconto().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataAssinatura")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorDataAssinatura(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataInicioVigencia")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorDataInicioVigencia(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataFinalVigencia")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorDataFinalVigencia(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descontoParcela")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                double valorDouble = Double.parseDouble(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorDescontoParcela(valorDouble, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("responsavelAutorizacao")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorResponsavelAutorizacao(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("dataAutorizacao")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getConvenioDesconto().consultarPorDataAutorizacao(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }
    
    public Boolean getDesenhaValorEspecifico() {         
        if (convenioDescontoConfiguracaoVO.getTipoDesconto() == null) {
            return new Boolean(false);
        }
        if (convenioDescontoConfiguracaoVO.getTipoDesconto().equals("VE")) {
            convenioDescontoConfiguracaoVO.setPorcentagemDesconto(new Double(0));
            return new Boolean(true);
        }
        return new Boolean(false);
    }

    public Boolean getDesenhaPercentualDesconto() {       
        if (convenioDescontoConfiguracaoVO.getTipoDesconto() == null) {
            return new Boolean(false);
        }
        if (convenioDescontoConfiguracaoVO.getTipoDesconto().equals("PD")) {
            convenioDescontoConfiguracaoVO.setValorDesconto(new Double(0));
            return new Boolean(true);
        }
        return new Boolean(false);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ConvenioDescontoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getConvenioDesconto().excluir(convenioDescontoVO);
            incluirLogExclusao();
            setConvenioDescontoVO(new ConvenioDescontoVO());

            setConvenioDescontoConfiguracaoVO(new ConvenioDescontoConfiguracaoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
                if (e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"conveniodesconto\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"conveniodesconto\" violates foreign key")){
                setMensagemDetalhada("Este convênio de desconto não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /* Método responsável por adicionar um novo objeto da classe <code>ConvenioDescontoConfiguracao</code>
     * para o objeto <code>convenioDescontoVO</code> da classe <code>ConvenioDesconto</code>
     */
    public void adicionarConvenioDescontoConfiguracao() throws Exception {
        try {
            if (!getConvenioDescontoVO().getCodigo().equals(new Integer(0))) {
                convenioDescontoConfiguracaoVO.setConvenioDesconto(getConvenioDescontoVO().getCodigo());
            }           
            getConvenioDescontoVO().adicionarObjConvenioDescontoConfiguracaoVOs(getConvenioDescontoConfiguracaoVO());
            this.setConvenioDescontoConfiguracaoVO(new ConvenioDescontoConfiguracaoVO());
            setMensagemID("msg_dados_adicionados");
            
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());            
        }
    }

    /* Método responsável por disponibilizar dados de um objeto da classe <code>ConvenioDescontoConfiguracao</code>
     * para edição pelo usuário.
     */
    public void editarConvenioDescontoConfiguracao() throws Exception {
        ConvenioDescontoConfiguracaoVO obj = (ConvenioDescontoConfiguracaoVO) context().getExternalContext().getRequestMap().get("convenioDescontoConfiguracao");
        setConvenioDescontoConfiguracaoVO(obj);

    }

    /* Método responsável por remover um novo objeto da classe <code>ConvenioDescontoConfiguracao</code>
     * do objeto <code>convenioDescontoVO</code> da classe <code>ConvenioDesconto</code>
     */
    public void removerConvenioDescontoConfiguracao() throws Exception {
        ConvenioDescontoConfiguracaoVO obj = (ConvenioDescontoConfiguracaoVO) context().getExternalContext().getRequestMap().get("convenioDescontoConfiguracao");
        getConvenioDescontoVO().excluirObjConvenioDescontoConfiguracaoVOs(obj.getDuracao());
        setMensagemID("msg_dados_excluidos");
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do 
     * ComboBox correspondente ao atributo <code>tipoDesconto</code>
     */
    public List getListaSelectItemTipoDescontoConvenioDescontoConfiguracao() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable tipoValors = (Hashtable) Dominios.getTipoValor();
        Enumeration keys = tipoValors.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoValors.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("dataAssinatura", "Data de Assinatura"));
        itens.add(new SelectItem("dataInicioVigencia", "Data Inicio de Vigencia"));
        itens.add(new SelectItem("dataFinalVigencia", "Data Final de Vigencia"));
        itens.add(new SelectItem("descontoParcela", "Desconto nas Parcela"));
        itens.add(new SelectItem("responsavelAutorizacao", "Responsável Autorização"));
        itens.add(new SelectItem("dataAutorizacao", "Data Autorização"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }
  
    public ConvenioDescontoConfiguracaoVO getConvenioDescontoConfiguracaoVO() {
        return convenioDescontoConfiguracaoVO;
    }

    public void setConvenioDescontoConfiguracaoVO(ConvenioDescontoConfiguracaoVO convenioDescontoConfiguracaoVO) {
        this.convenioDescontoConfiguracaoVO = convenioDescontoConfiguracaoVO;
    }

    public ConvenioDescontoVO getConvenioDescontoVO() {
        return convenioDescontoVO;
    }

    public void setConvenioDescontoVO(ConvenioDescontoVO convenioDescontoVO) {
        this.convenioDescontoVO = convenioDescontoVO;
    }
     public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getConvenioDesconto().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public void setSelectItemEmpresas(List<SelectItem> selectItemEmpresas) {
        this.selectItemEmpresas = selectItemEmpresas;
    }

    public List<SelectItem> getSelectItemEmpresas() {
        return selectItemEmpresas;
    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            convenioDescontoVO.setObjetoVOAntesAlteracao(new ConvenioDescontoVO());
            convenioDescontoVO.setNovoObj(true);
            registrarLogObjetoVO(convenioDescontoVO, convenioDescontoVO.getCodigo(), NOME_ENTIDADE, 0);
            incluirLogAlteracoesConfiguracaoVOs();
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        convenioDescontoVO.setNovoObj(new Boolean(false));
        convenioDescontoVO.registrarObjetoVOAntesDaAlteracao();
        convenioDescontoVO.registrarConfiguracaoVOsAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            convenioDescontoVO.setObjetoVOAntesAlteracao(new ConvenioDescontoVO());
            convenioDescontoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(convenioDescontoVO, convenioDescontoVO.getCodigo(), NOME_ENTIDADE, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(convenioDescontoVO, convenioDescontoVO.getCodigo(), NOME_ENTIDADE, 0);
            incluirLogAlteracoesConfiguracaoVOs();
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        convenioDescontoVO.registrarObjetoVOAntesDaAlteracao();
        convenioDescontoVO.registrarConfiguracaoVOsAntesDaAlteracao();
    }
    
    
    private void incluirLogAlteracoesConfiguracaoVOs() throws Exception {
        for(Object obj : convenioDescontoVO.getConvenioDescontoConfiguracaoVOs()){
            ConvenioDescontoConfiguracaoVO atual = (ConvenioDescontoConfiguracaoVO) obj;
            boolean nova = true;
            for(ConvenioDescontoConfiguracaoVO anterior : convenioDescontoVO.getConvenioDescontoConfiguracaoVOsAntesAlteracao()){
                if(anterior.getDuracao().equals(atual.getDuracao())){
                    if(!anterior.getTipoDesconto().equals(atual.getTipoDesconto()) 
                            || Uteis.arredondarForcando2CasasDecimais(anterior.getPorcentagemDesconto()) != Uteis.arredondarForcando2CasasDecimais(atual.getPorcentagemDesconto())
                            || Uteis.arredondarForcando2CasasDecimais(anterior.getValorDesconto()) != Uteis.arredondarForcando2CasasDecimais(atual.getValorDesconto())){
                        incluirLogAlteracaoConfiguracaoVO(anterior ,atual);
                    }
                    nova = false;
                    break;
                }
            }
            if(nova){
                incluirLogInclusaoConfiguracaoVO(atual);
            }
        }
        for(ConvenioDescontoConfiguracaoVO anterior : convenioDescontoVO.getConvenioDescontoConfiguracaoVOsAntesAlteracao()){
            boolean excluida = true;
            for(Object obj : convenioDescontoVO.getConvenioDescontoConfiguracaoVOs()){
                ConvenioDescontoConfiguracaoVO atual = (ConvenioDescontoConfiguracaoVO) obj;
                if(anterior.getDuracao().equals(atual.getDuracao())){
                     excluida = false;
                     break;
                 }
            }
            if(excluida){
                incluirLogExclusaoConfiguracaoVO(anterior);
            }
        }
    }

    private void incluirLogAlteracaoConfiguracaoVO(ConvenioDescontoConfiguracaoVO anterior, ConvenioDescontoConfiguracaoVO atual) throws Exception {
         try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(convenioDescontoVO.getCodigo()));
            logVO.setNomeEntidade(NOME_ENTIDADE);
            logVO.setNomeEntidadeDescricao("ConvenioDesconto - ConvenioDescontoConfiguracao/Configuração do Convênio de Desconto");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("Duração do Plano: "+anterior.getDuracao() +"\n  Valor de Desconto= "+anterior.getValorDesconto()+"\n  Porcentagem de Desconto= "+anterior.getPorcentagemDesconto() + "\n  Tipo de Desconto= "+anterior.getTipoDesconto());
            logVO.setValorCampoAlterado("Duração do Plano: "+atual.getDuracao() +"\n  Valor de Desconto= "+atual.getValorDesconto()+"\n  Porcentagem de Desconto= "+atual.getPorcentagemDesconto() + "\n  Tipo de Desconto= "+atual.getTipoDesconto());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CONFIGURAÇÃO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogInclusaoConfiguracaoVO(ConvenioDescontoConfiguracaoVO atual) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("INCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(convenioDescontoVO.getCodigo()));
            logVO.setNomeEntidade(NOME_ENTIDADE);
            logVO.setNomeEntidadeDescricao("ConvenioDesconto - ConvenioDescontoConfiguracao/Configuração do Convênio de Desconto");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Duração do Plano: "+atual.getDuracao() +"\n  Valor de Desconto= "+atual.getValorDesconto()+"\n  Porcentagem de Desconto= "+atual.getPorcentagemDesconto() + "\n  Tipo de Desconto= "+atual.getTipoDesconto());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONFIGURAÇÃO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void incluirLogExclusaoConfiguracaoVO(ConvenioDescontoConfiguracaoVO anterior) throws Exception {
        try{
            LogVO logVO = new LogVO();
            logVO.setOperacao("EXCLUSÃO");
            logVO.setChavePrimaria(String.valueOf(convenioDescontoVO.getCodigo()));
            logVO.setNomeEntidade(NOME_ENTIDADE);
            logVO.setNomeEntidadeDescricao("ConvenioDesconto - ConvenioDescontoConfiguracao/Configuração do Convênio de Desconto");
            logVO.setResponsavelAlteracao(getUsuarioLogado().getNome());
            logVO.setUserOAMD(getUsuarioLogado().getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado("Duração do Plano: "+anterior.getDuracao() +"\n  Valor de Desconto= "+anterior.getValorDesconto()+"\n  Porcentagem de Desconto= "+anterior.getPorcentagemDesconto() + "\n  Tipo de Desconto= "+anterior.getTipoDesconto());

            if (logVO != null) {
                logVO.setPessoa(0);
                getFacade().getLog().incluirSemCommit(logVO);
            }
         } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, convenioDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONFIGURAÇÃO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Convênio de Desconto",
                "Deseja excluir o Convênio de Desconto?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.contrato.AtestadoContratoVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RetornoAtestadoContratoControle extends SuperControle {

    private AtestadoContratoVO atestadoContratoVO;
    private Boolean apresentarBotoes;
    private List<ContratoVO> listaContratoVOs;
    private List<ContratoOperacaoVO> listaContratoOperacaoVOs;

    public RetornoAtestadoContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        novo();
    }

    public void inicializarUsuarioLogado() {
        try {
            getAtestadoContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getAtestadoContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
        } catch (Exception e) {
        }
    }

    public void novo() throws Exception {
        try {
            setProcessandoOperacao(false);
            setAtestadoContratoVO(new AtestadoContratoVO());
            setListaContratoVOs(new ArrayList<ContratoVO>());
            inicializarUsuarioLogado();
            montarDadosContratoParaAtestado();
            setSucesso(false);
            setErro(false);
            setApresentarBotoes(true);            
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarDadosContratoParaAtestado() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.pegarClienteTelaCliente();
                getAtestadoContratoVO().setContratoVO(clienteControle.getContratoVO());
                getAtestadoContratoVO().setEmpresa(clienteControle.getClienteVO().getEmpresa().getCodigo());

                ContratoOperacaoVO contratoOperacaoVO = getFacade().getContratoOperacao().consultarPorTipoOperacaoCodigoContrato(clienteControle.getContratoVO().getCodigo(), "AT", false, Uteis.NIVELMONTARDADOS_ROBO);
                getAtestadoContratoVO().setDataInicio(contratoOperacaoVO.getDataInicioEfetivacaoOperacao());
                getAtestadoContratoVO().setDataTermino(contratoOperacaoVO.getDataFimEfetivacaoOperacao());
                getAtestadoContratoVO().setTipoJustificativa(contratoOperacaoVO.getTipoJustificativa().getCodigo());

                getListaContratoVOs().add(clienteControle.getContratoVO());
                setListaContratoOperacaoVOs(clienteControle.getListaSelectItemContratoOperacao());
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void consultarResponsavel() {
        try {
            getAtestadoContratoVO().setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(getAtestadoContratoVO().getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarDadosAtestado() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                getFacade().getContratoOperacao().incluirOperacaoRetornoAtestado(atestadoContratoVO, getUsuarioLogado());
                montarSucessoGrowl("");
                setApresentarBotoes(false);
                setProcessandoOperacao(false);
                setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});" + getMensagemNotificar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setApresentarBotoes(true);
                setErro(true);
                setSucesso(false);
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        auto.autorizar("Confirmação de Retorno de Atestado", "Atestado_Autorizar",
                "Você precisa da permissão \"3.12 - Atestado para Contrato - Autorizar\"",
                "panelBotoes", listener);

    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public AtestadoContratoVO getAtestadoContratoVO() {
        return atestadoContratoVO;
    }

    public void setAtestadoContratoVO(AtestadoContratoVO atestadoContratoVO) {
        this.atestadoContratoVO = atestadoContratoVO;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public List<ContratoOperacaoVO> getListaContratoOperacaoVOs() {
        return listaContratoOperacaoVOs;
    }

    public void setListaContratoOperacaoVOs(List<ContratoOperacaoVO> listaContratoOperacaoVOs) {
        this.listaContratoOperacaoVOs = listaContratoOperacaoVOs;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        atestadoContratoVO = null;
        listaContratoVOs.clear();
        apresentarBotoes = null;
    }
}

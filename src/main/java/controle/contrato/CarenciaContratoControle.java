/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.CarenciaContratoVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import negocio.facade.jdbc.arquitetura.Permissao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class CarenciaContratoControle extends SuperControle {

    protected CarenciaContratoVO carenciaContratoVO;
    protected List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs;
    protected List<ContratoVO> listaContratoVOs;
    protected List<ContratoOperacaoVO> listaContratoOperacaoVOs;
    protected Boolean apresentarBotoes;
    protected Boolean abrirRichModalDeConfirmacao;
    protected Boolean apresentarPeriodoCarencia;
    protected Integer carencia;
    private UsuarioVO autorizacaoAcesso;
    private boolean apresentarAcesso;
    private String aviso;
    private String mensagemCarrencia;
    private String nomeArquivoComprovanteOperacao;
    private Integer novoDiasCarencia = null;
    private boolean editando = false;
    private UsuarioVO responsavelAlteracaoDias = null;

    public CarenciaContratoControle() throws Exception {
        consultarCarencia();
        inicializarFacades();
        //novo();
    }

    public void inicializarEmpresaLogado() throws Exception {
        try {
            if (getEmpresaLogado().getCodigo().intValue() != 0) {
                getCarenciaContratoVO().setEmpresa(getEmpresaLogado().getCodigo());
            } else {
                getCarenciaContratoVO().setEmpresa(new Integer(0));
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            getCarenciaContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getCarenciaContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
            getCarenciaContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
            getAutorizacaoAcesso().setCodigo(getUsuarioLogado().getCodigo());
            getAutorizacaoAcesso().setUsername(getUsuarioLogado().getUsername());
            getAutorizacaoAcesso().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            throw e;
        }
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }


    public void confirmarAlteracao() {
        try {
            if (getCarenciaContratoVO().getNrDiasPermitido() == novoDiasCarencia) {
                setEditando(false);
                novoDiasCarencia = null;
                return;
            }
            getFacade().getContratoDuracao().alterarDiasCarenciaContrato(getCarenciaContratoVO().getContratoVO(), getCarenciaContratoVO().getNrDiasPermitido(),
                    novoDiasCarencia, responsavelAlteracaoDias);
            setarNovosDiasCarenciaContrato();
            montarSucessoDadosGravados();
            setEditando(false);
            novoDiasCarencia = null;
            novo();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "DIASCARENCIA";
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("DIASCARENCIA"));
        loginControle.consultarLogObjetoSelecionado("DIASCARENCIA", getCarenciaContratoVO().getContratoVO().getCodigo(), 0);
    }

    public void alterarDias() {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                editando = true;
                novoDiasCarencia = getCarenciaContratoVO().getNrDiasPermitido();
                responsavelAlteracaoDias = auto.getUsuario();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        auto.autorizar("Alterar manualmente a quantidade de dias de férias permitidos", "AlterarManualmenteCarencia",
                "Você precisa da permissão \"3.36 - Alterar manualmente a quantidade de dias de férias permitidos\"",
                "panelCarencia", listener);
    }


    public String novo() throws Exception {
        try {
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_CARENCIA);
            setProcessandoOperacao(false);
            setEditando(false);
            novoDiasCarencia = null;
            setAviso("");
            setMensagemCarrencia(null);
            setApresentarAcesso(false);
            setAutorizacaoAcesso(new UsuarioVO());
            setCarenciaContratoVO(new CarenciaContratoVO());
            setListaContratoVOs(new ArrayList<ContratoVO>());
            setListaContratoOperacaoVOs(new ArrayList<ContratoOperacaoVO>());
            inicializarUsuarioLogado();
            inicializarEmpresaLogado();
            inicializarListasSelectItemTodosComboBox();
            obterNomeCliente();
            montarDadosContratoParaCarencia();
            //validar se o contrato possui um trancamento sem retorno
            ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getCarenciaContratoVO().getContratoVO());
            setAbrirRichModalDeConfirmacao(false);
            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setErro(false);
            setAtencao(false);
            setApresentarPeriodoCarencia(false);
            setApresentarBotoes(true);
            setMensagemDetalhada("");
            return "carencia";
        } catch (ConsistirException c) {
            setMensagemCarrencia(c.getMessage());
            setApresentarBotoes(false);
            return "erroCarencia";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setApresentarBotoes(false);
            return "erroCarencia";
        }
    }

    public void inicializarListasSelectItemTodosComboBox() throws Exception {
        try {
            montarDadosListaJustificativaOperacaoVOs();
        } catch (Exception e) {
            throw e;
        }
    }

    public void obterNomeCliente() {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                getCarenciaContratoVO().getContratoVO().getPessoa().setNome(clienteControle.getClienteVO().getPessoa().getNome());
            } else {
                throw new Exception("Não foi possível inicializar o nome do cliente.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void setarNovosDiasCarenciaContrato() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.getContratoVO().getContratoDuracao().setCarencia(novoDiasCarencia);
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDadosContratoParaCarencia() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                getCarenciaContratoVO().setContratoVO(clienteControle.getContratoVO());
                //getCarenciaContratoVO().setEmpresa(clienteControle.getClienteVO().getEmpresa().getCodigo().intValue());
                getListaContratoVOs().add(clienteControle.getContratoVO());
                setListaContratoOperacaoVOs(clienteControle.getListaSelectItemContratoOperacao());
                getCarenciaContratoVO().validarDiasUtilizados(getListaContratoOperacaoVOs());
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs() {
        try {
            montarDadosListaJustificativaOperacaoVOs("CR");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs(String prm) throws Exception {
        try {
            List resultadoConsulta = consultarTipoJustificativaOperacaoPorTipo(prm);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                JustificativaOperacaoVO obj = (JustificativaOperacaoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
            }
            setListaJustificativaOperacaoVOs(objs);
        } catch (Exception e) {
            throw e;
        }
    }

    public List consultarTipoJustificativaOperacaoPorTipo(String prm) throws Exception {
        List lista = new ArrayList();
        try {
            lista = getFacade().getJustificativaOperacao().consultarPorTipoOperacao(prm, getCarenciaContratoVO().getEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarDadosCarencia() {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                setProcessandoOperacao(true);
                //validar se o contrato possui um trancamento sem retorno
                ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getCarenciaContratoVO().getContratoVO());
                carenciaContratoVO.validarPeriodoCarencia(getFacade().getContratoOperacao().
                        consultarPorContrato(getCarenciaContratoVO().getContratoVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (auto.getUsuario() != null && UteisValidacao.emptyNumber(auto.getUsuario().getCodigo())){
                    getCarenciaContratoVO().setResponsavelOperacao(auto.getUsuario());
                } else {
                    getCarenciaContratoVO().setResponsavelOperacao(getUsuarioLogado());
                }
                getFacade().getContratoOperacao().incluirOperacaoCarencia(carenciaContratoVO, getUsuarioLogado());
                setMensagemDetalhada("");
                setMensagem("");
                setMensagemID("msg_dados_gravados");
                setApresentarBotoes(false);
                setErro(false);
                setSucesso(true);
                notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_CARENCIA_SUCESSO, ContadorTempo.encerraContagem());
                setExecutarAoCompletar(getMensagemNotificar()+";try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true})");
                getCarenciaContratoVO().setMensagemErro(false);
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setApresentarBotoes(true);
                getCarenciaContratoVO().setMensagemErro(true);
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
            }
        };

        limparMsg();
        try {
            validarCarencia();
            validarPermissaoRetroativo();
            if (!carenciaContratoVO.getContratoVO().isVendaCreditoTreino()) {
                Date dataAnterior = Uteis.obterDataAnterior(carenciaContratoVO.getDataInicio(), 1);
                Integer nrDiasContratos = getFacade().getZWFacade().obterNrDiasContrato(carenciaContratoVO.getContratoVO());
                Integer diasUtilizados = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(carenciaContratoVO.getContratoVO(), dataAnterior);
                Integer diasRestante = carenciaContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(nrDiasContratos, diasUtilizados);
                if (diasRestante <= 0) {
                    throw new Exception(String.format("Não é possível lançar "
                                    + "um carencia nesse período, pois o número de dias "
                                    + "utilizados (%s) é maior ou igual ao número de dias do contrato (%s).",
                            new Object[]{
                                    diasUtilizados,
                                    nrDiasContratos

                            }));
                }
            }
            getCarenciaContratoVO().validarPeriodoCarencia(getListaContratoOperacaoVOs());
            auto.autorizar("Confirmação de Férias", "Carencia_Autorizar",
                    "Você precisa da permissão \"3.13 - Férias  para Contrato - Autorizar\"",
                    "form", listener);


        } catch (Exception e) {
            montarErro(e);
        }
        if (Uteis.nrDiasEntreDatas(Calendario.hoje(), carenciaContratoVO.getDataInicio()) < 0) {


        }

    }

    public void validarPermissaoRetroativo() throws Exception {
        if (Uteis.nrDiasEntreDatas(Calendario.hoje(), carenciaContratoVO.getDataInicio()) < 0) {
            try {
                permissaoFuncionalidade(getUsuarioLogado(),
                        "LancamentoCarenciaRetroativa",
                        "9.59 - Lançamento de Carência Retroativa para o contrato do cliente");
            } catch (Exception e) {
                throw new Exception("Para fazer um lançamento de férias retroativa é necessária a permissão: \"9.59 - Lançamento de Carência Retroativa para o contrato do cliente\"");
            }
        }

    }


    public void validarCarencia() throws Exception {
        if (carenciaContratoVO.getDataInicio() == null || carenciaContratoVO.getDataTermino() == null) {
            throw new Exception("O período das Férias deve ser informado");
        }
        Long diferenca = Uteis.nrDiasEntreDatas(carenciaContratoVO.getDataInicio(), carenciaContratoVO.getDataTermino());
        getCarenciaContratoVO().setPeriodoCarencia(diferenca + 1);
        if (getCarenciaContratoVO().getPeriodoCarencia().intValue() < getCarencia().intValue()) {
            throw new Exception("O Período das Férias Deve Ser Maior do que a Quantidade Mínima de Férias");
        }
    }

    public void gerarPeriodoRetornoCarencia() {


        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                // limpa a data termino para obrigar o usuario a informar de novo
                carenciaContratoVO.setDataTermino(null);
            }
        };

        limparMsg();
        try {
            if (carenciaContratoVO.getDataInicio() != null && carenciaContratoVO.getDataTermino() != null) {
                validarDataInicioEDataTermino();
                carenciaContratoVO.setNrDiasContrato(getFacade().getZWFacade().obterNrDiasContrato(carenciaContratoVO.getContratoVO()));
                Date dataAnterior = Uteis.obterDataAnterior(carenciaContratoVO.getDataInicio(), 1);
                carenciaContratoVO.setNrDiasUsadoContrato(getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(carenciaContratoVO.getContratoVO(), dataAnterior));
                if (carenciaContratoVO.getContratoVO().isVendaCreditoTreino()) {
                    carenciaContratoVO.setNrDiasRestanteContrato(((Long) Uteis.nrDiasEntreDatas(carenciaContratoVO.getDataInicio(), carenciaContratoVO.getContratoVO().getVigenciaAteAjustada())).intValue() + 1);
                } else {
                    carenciaContratoVO.setNrDiasRestanteContrato(carenciaContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(carenciaContratoVO.getNrDiasContrato(), carenciaContratoVO.getNrDiasUsadoContrato()));
                    if (carenciaContratoVO.getNrDiasRestanteContrato().intValue() <= 0) {
                        throw new Exception(String.format("Não é possível lançar uma férias nesse período, pois o contrato do cliente está vigente por meio de bônus e o número de dias utilizados" +
                                        " (%s) é maior ou igual ao número de dias do contrato (%s).",
                                new Object[]{
                                        carenciaContratoVO.getNrDiasUsadoContrato(),
                                        carenciaContratoVO.getNrDiasContrato()

                                }));
                    }
                }
                validarCarencia();
                Long dias = Uteis.nrDiasEntreDatas(carenciaContratoVO.getDataInicio(), carenciaContratoVO.getDataTermino());
                dias++;
                carenciaContratoVO.setDataInicioRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getDataTermino(), 1));

                //diasRestante pode ser negativo, portanto utilizar os dias da Carência e não o restante do contrato
                if (Uteis.getCompareData(carenciaContratoVO.getDataTermino(), carenciaContratoVO.getContratoVO().getVigenciaAteAjustada()) > 0) {
                    carenciaContratoVO.setQtdDiasCarenciaMaiorQueContrato(true);
                    carenciaContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getDataTermino(), carenciaContratoVO.getNrDiasRestanteContrato().intValue()));
                    carenciaContratoVO.setNrDias(carenciaContratoVO.getNrDiasRestanteContrato().intValue());
                } else {
                    carenciaContratoVO.setQtdDiasCarenciaMaiorQueContrato(false);
                    if (carenciaContratoVO.getNrDiasRestanteContrato().intValue() < dias.intValue()) { // contrato em periodo de bonus
                        carenciaContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getContratoVO().getVigenciaAteAjustada(), carenciaContratoVO.getNrDiasRestanteContrato().intValue()));
                        carenciaContratoVO.setNrDias(carenciaContratoVO.getNrDiasRestanteContrato().intValue());
                    } else {
                        carenciaContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(carenciaContratoVO.getContratoVO().getVigenciaAteAjustada(), dias.intValue()));
                        carenciaContratoVO.setNrDias(dias.intValue());
                    }
                }
                setApresentarPeriodoCarencia(true);

                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(carenciaContratoVO.getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                int acessos = getFacade().getAcessoCliente().consultarQtdAcessosEntreDatas(
                        cliente, carenciaContratoVO.getDataInicio(),
                        carenciaContratoVO.getDataTermino(), false);
                // se cliente teve algum acesso neste periodo
                if (acessos > 0) {
                    auto.autorizar("Confirmação de Férias com Acesso", "AtestadoCarencia_Autorizar",
                            "O Aluno possui " + acessos + " acessos no período informado. Para continuar você precisa da permissão \"3.18 - Atestado e Férias com Frequência - Autorizar\"",
                            "form", listener);
                }

                montarSucessoGrowl("");
                carenciaContratoVO.setMensagemErro(false);

            }
        } catch (Exception e) {
            setApresentarPeriodoCarencia(false);
            carenciaContratoVO.setMensagemErro(true);
            montarErro(e);
        }
    }

    public void fecharConfirmacaoAcesso() {
        setApresentarAcesso(false);
        // limpa a data termino para obrigar o usuario a informar de novo
        carenciaContratoVO.setDataTermino(null);
    }

    public void validarDataInicioEDataTermino() throws Exception {
        try {
            if (Uteis.getCompareData(carenciaContratoVO.getDataInicio(), carenciaContratoVO.getDataTermino()) > 0) {
                throw new Exception("O campo ATÉ não pode ser antes do campo INÍCIO");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void limparMensagem() {
        setMensagem("");
        setMensagemID("");
        setMensagemDetalhada("");
    }

    public void consultarResponsavel() {
        try {
            getCarenciaContratoVO().setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(getCarenciaContratoVO().getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getCarenciaContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
            limparMensagem();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarResponsavelAcesso() {
        try {
            setAutorizacaoAcesso(new Usuario().consultarPorChavePrimaria(getAutorizacaoAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getAutorizacaoAcesso().setUserOamd(getUsuarioLogado().getUserOamd());
            limparMensagem();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getAbrirRichConfimacaoDeCarencia() {
        if (getAbrirRichModalDeConfirmacao()) {
            return "Richfaces.showModalPanel('panelConfirmarCarencia');"
                    + "setFocus(formConfirmarAtestado,'formConfirmarAtestado:username');";
        } else {
            return "";
        }
    }

    private void consultarCarencia() {
        try {
            if (getEmpresaLogado().getCodigo() != 0) {
                setCarencia(new Empresa().obterCarenciaEmpresa(getEmpresaLogado().getCodigo()));
                if (getCarencia() != null && getCarencia() == 0) {
                    setCarencia(new ConfiguracaoSistema().obterCarenciaConfiguracao());
                }
            } else {
                setCarencia(new ConfiguracaoSistema().obterCarenciaConfiguracao());
            }
            if (getCarencia() == null) {
                setCarencia(new Integer(0));
            }
        } catch (Exception ex) {
            Logger.getLogger(CarenciaContratoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean getAbrirRichModalDeConfirmacao() {
        return abrirRichModalDeConfirmacao;
    }

    public void setAbrirRichModalDeConfirmacao(Boolean abrirRichModalDeConfirmacao) {
        this.abrirRichModalDeConfirmacao = abrirRichModalDeConfirmacao;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public Boolean getApresentarPeriodoCarencia() {
        return apresentarPeriodoCarencia;
    }

    public void setApresentarPeriodoCarencia(Boolean apresentarPeriodoCarencia) {
        this.apresentarPeriodoCarencia = apresentarPeriodoCarencia;
    }

    public CarenciaContratoVO getCarenciaContratoVO() {
        return carenciaContratoVO;
    }

    public void setCarenciaContratoVO(CarenciaContratoVO carenciaContratoVO) {
        this.carenciaContratoVO = carenciaContratoVO;
    }

    public List<ContratoOperacaoVO> getListaContratoOperacaoVOs() {
        return listaContratoOperacaoVOs;
    }

    public void setListaContratoOperacaoVOs(List<ContratoOperacaoVO> listaContratoOperacaoVOs) {
        this.listaContratoOperacaoVOs = listaContratoOperacaoVOs;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public List<JustificativaOperacaoVO> getListaJustificativaOperacaoVOs() {
        return listaJustificativaOperacaoVOs;
    }

    public void setListaJustificativaOperacaoVOs(List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs) {
        this.listaJustificativaOperacaoVOs = listaJustificativaOperacaoVOs;
    }

    public Integer getCarencia() {
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        carenciaContratoVO = null;
        listaContratoVOs = new ArrayList<ContratoVO>();
        abrirRichModalDeConfirmacao = null;
        apresentarBotoes = null;
        apresentarPeriodoCarencia = null;
        listaJustificativaOperacaoVOs = new ArrayList<JustificativaOperacaoVO>();
        listaContratoOperacaoVOs = new ArrayList<ContratoOperacaoVO>();

    }

    public UsuarioVO getAutorizacaoAcesso() {
        return autorizacaoAcesso;
    }

    public void setAutorizacaoAcesso(UsuarioVO autorizacaoAcesso) {
        this.autorizacaoAcesso = autorizacaoAcesso;
    }

    public boolean isApresentarAcesso() {
        return apresentarAcesso;
    }

    public void setApresentarAcesso(boolean apresentarAcesso) {
        this.apresentarAcesso = apresentarAcesso;
    }

    public String getAviso() {
        return aviso;
    }

    public void setAviso(String aviso) {
        this.aviso = aviso;
    }

    public String getMensagemCarrencia() {
        return mensagemCarrencia;
    }

    public void setMensagemCarrencia(String mensagemCarrencia) {
        this.mensagemCarrencia = mensagemCarrencia;
    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

    public void imprimirComprovanteOperacao() {
        try {
            if (getCarenciaContratoVO().getContratoOperacaoVO().getCodigo() != 0) {
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getCarenciaContratoVO().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getCarenciaContratoVO().getContratoOperacaoVO(), empresaVO));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante da operação.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Integer getNovoDiasCarencia() {
        return novoDiasCarencia;
    }

    public void setNovoDiasCarencia(Integer novoDiasCarencia) {
        this.novoDiasCarencia = novoDiasCarencia;
    }

    public boolean isEditando() {
        return editando;
    }

    public void setEditando(boolean editando) {
        this.editando = editando;
    }

    public UsuarioVO getResponsavelAlteracaoDias() {
        return responsavelAlteracaoDias;
    }

    public void setResponsavelAlteracaoDias(UsuarioVO responsavelAlteracaoDias) {
        this.responsavelAlteracaoDias = responsavelAlteracaoDias;
    }
}

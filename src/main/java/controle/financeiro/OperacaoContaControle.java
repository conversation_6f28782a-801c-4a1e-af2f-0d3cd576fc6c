package controle.financeiro;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.openbanking.stone.TransferenciaStoneVO;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import servicos.impl.devolucaocheque.DevolucaoChequeServiceImpl;
import servicos.interfaces.devolucaocheque.DevolucaoChequeService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

public class OperacaoContaControle extends SuperControleRelatorio {

    private String onCompleteModalOperacao = "";
    private LoteVO lote = new LoteVO();
    private TipoOperacaoLancamento tipoOperacao;
    private TipoFormaPagto tipoForma;
    private boolean apresentarLote = false;
    private ContaVO contaOrigem = new ContaVO();
    private Date dataDeposito = Calendario.hoje();
    private boolean apresentarNoCaixa = true;

    private final String urlTratativaGestaoRecebiveis = "/ZillyonWeb/faces/pages/finan/gestaoRecebiveis.jsp";

    /**
     * <AUTHOR> Alcides 14/11/2012
     */
    public String transferirChequesCartoes(List<ChequeTO> cheques, List<CartaoCreditoTO> cartoes, ContaVO conta) throws Exception {
        LoteVO lote = new LoteVO();
        lote.setDataDeposito(Calendario.hoje());
        lote.setEmpresa(getEmpresaLogado());
        lote.setUsuarioResponsavel(getUsuarioLogado());
        lote.setDataLancamento(Calendario.hoje());
        //    	preencher listas
        if (cheques == null) {
            lote.setCartoes(new ArrayList<CartaoCreditoVO>());
            for (CartaoCreditoTO cartao : cartoes) {
                if (cartao.isCartaoEscolhido()) {
                    lote.adicionarCartao(getFacade().getCartaoCredito().consultarPorChavePrimaria(cartao.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
            return abrirModal(lote, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.ENTRADA,
                    TipoFormaPagto.CARTAOCREDITO, lote.getValor(), true, conta, false, false, false);
        } else {
            lote.setCheques(new ArrayList<ChequeVO>());
            for (ChequeTO cheque : cheques) {
                if (cheque.isChequeEscolhido()) {
                    lote.adicionarCheque(getFacade().getCheque().consultarPorChavePrimaria(cheque.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
            return abrirModal(lote, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.ENTRADA,
                    TipoFormaPagto.CHEQUE, lote.getValor(), true, conta, false, false, false);
        }

    }

    public String abrirModal(LoteVO lote, TipoOperacaoLancamento tipoOperacao, TipoES tipo,
                             TipoFormaPagto tipoForma, Double valor, boolean apresentarLote, ContaVO contaOrigem,
                             boolean transferirDinheiro, boolean habilitarValor, boolean recebidoZW,
                             FormaPagamentoVO formaPagamentoVO, Double taxaJuros, Double valorLiquidoCalculado, Integer qtdeTotalVista, Double taxaAntecipacao, Date dataTrabalhoSugeridaParaMovimentacaoConciliacao) throws Exception {
        return abrirModal(lote, tipoOperacao, tipo, tipoForma, valor, apresentarLote, contaOrigem, transferirDinheiro, habilitarValor, recebidoZW, formaPagamentoVO, taxaJuros,
                valorLiquidoCalculado, false, false , qtdeTotalVista, taxaAntecipacao, dataTrabalhoSugeridaParaMovimentacaoConciliacao);
    }

    public String abrirModal(LoteVO lote, TipoOperacaoLancamento tipoOperacao, TipoES tipo,
                             TipoFormaPagto tipoForma, Double valor, boolean apresentarLote, ContaVO contaOrigem,
                             boolean transferirDinheiro, boolean habilitarValor, boolean recebidoZW,
                             FormaPagamentoVO formaPagamentoVO, Double taxaJuros, Double valorLiquidoCalculado, Integer qtdeTotalVista) throws Exception {
        return abrirModal(lote, tipoOperacao, tipo, tipoForma, valor, apresentarLote, contaOrigem, transferirDinheiro, habilitarValor, recebidoZW, formaPagamentoVO, taxaJuros, valorLiquidoCalculado, false, false , qtdeTotalVista, 0.0);
    }

    public String abrirModal(LoteVO lote, TipoOperacaoLancamento tipoOperacao, TipoES tipo,
                             TipoFormaPagto tipoForma, Double valor, boolean apresentarLote, ContaVO contaOrigem,
                             boolean transferirDinheiro, boolean habilitarValor, boolean recebidoZW,
                             FormaPagamentoVO formaPagamentoVO, Double taxaJuros, Double valorLiquidoCalculado, boolean openBankMesmoBanco, boolean openBankOutrosBancos, Integer qtdeTotalVista, Double taxaAntecipacao) throws Exception {
        return abrirModal(lote, tipoOperacao, tipo, tipoForma, valor, apresentarLote, contaOrigem, transferirDinheiro, habilitarValor, recebidoZW,
        formaPagamentoVO, taxaJuros, valorLiquidoCalculado, openBankMesmoBanco, openBankOutrosBancos, qtdeTotalVista, taxaAntecipacao, null);
    }

    public String abrirModal(LoteVO lote, TipoOperacaoLancamento tipoOperacao, TipoES tipo,
            TipoFormaPagto tipoForma, Double valor, boolean apresentarLote, ContaVO contaOrigem,
            boolean transferirDinheiro, boolean habilitarValor, boolean recebidoZW,
            FormaPagamentoVO formaPagamentoVO, Double taxaJuros, Double valorLiquidoCalculado, boolean openBankMesmoBanco, boolean openBankOutrosBancos, Integer qtdeTotalVista,
                             Double taxaAntecipacao, Date dataTrabalhoSugeridaParaMovimentacaoConciliacao) throws Exception {
        limparMsg();
        this.apresentarNoCaixa = recebidoZW;
        this.tipoForma = tipoForma;
        this.tipoOperacao = tipoOperacao;
        this.apresentarLote = apresentarLote;
        this.contaOrigem = contaOrigem;
        try {
            if (valor == 0.0 && !transferirDinheiro) {
                setMensagemID("msg_selecione_lancamento");
                throw new Exception(getMensagem());
            }
            CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
            MovContaControle movContaControle = getMovContaControle();
            if (((caixaControle.getCaixaVoEmAberto() == null) || (caixaControle.getCaixaVoEmAberto().getCodigo() == 0))
                    && getUsarMovimentacao()) {
                setMensagemID("msg_naoPossuiCaixaAberto");
                throw new Exception(getMensagem());
            } else if (!getUsarMovimentacao()) {
                caixaControle.setCaixaVoEmAberto(new CaixaVO());
                if (dataTrabalhoSugeridaParaMovimentacaoConciliacao == null) {
                    caixaControle.getCaixaVoEmAberto().setDataTrabalho(Calendario.hoje());
                } else {
                    caixaControle.getCaixaVoEmAberto().setDataTrabalho(dataTrabalhoSugeridaParaMovimentacaoConciliacao);
                }
            }

            String wiki = "Recebimento";
            if (TipoES.SAIDA.equals(tipo)) {
                wiki = "Pagamento";
            }

            getMovContaControle().novo(false, false, tipoOperacao, "", "*Favorecido:", tipo, tipoForma, contaOrigem, habilitarValor, wiki, openBankMesmoBanco , qtdeTotalVista);
            movContaControle.getMovContaVO().setValor(valor);
            movContaControle.getMovContaVO().setValorPago(valor);
            if (dataTrabalhoSugeridaParaMovimentacaoConciliacao == null) {
                dataDeposito = caixaControle.getCaixaVoEmAberto().getDataTrabalho() == null ? null : caixaControle.getCaixaVoEmAberto().getDataTrabalho();
            } else {
                dataDeposito = dataTrabalhoSugeridaParaMovimentacaoConciliacao;
            }

            movContaControle.getMovContaVO().setDataCompetencia(caixaControle.getCaixaVoEmAberto().getDataTrabalho() == null ? Calendario.hoje() : caixaControle.getCaixaVoEmAberto().getDataTrabalho());
            movContaControle.getMovContaVO().setDataLancamento(Calendario.hoje());
            movContaControle.getMovContaVO().setDataQuitacao(caixaControle.getCaixaVoEmAberto().getDataTrabalho());
            movContaControle.getMovContaVO().setDataVencimento(caixaControle.getCaixaVoEmAberto().getDataTrabalho() == null ? Calendario.hoje() : caixaControle.getCaixaVoEmAberto().getDataTrabalho());
            if (formaPagamentoVO != null) {
                movContaControle.getMovContaRateioVO().setFormaPagamentoVO(formaPagamentoVO);
            }
            if (taxaJuros != null) {
                movContaControle.setTaxaMediaCartao(taxaJuros);
            }
            if (taxaAntecipacao != null) {
                movContaControle.setTaxaAntecipacaoCartao(taxaAntecipacao);
            }
            if (valorLiquidoCalculado != null) {
                movContaControle.setValorLiquidoCalculado(valorLiquidoCalculado);
            }

            this.setLote(lote);
            movContaControle.getMovContaVO().setLote(lote);
            if (this.getLote() != null) {
                this.getLote().setDataDeposito(caixaControle.getCaixaVoEmAberto().getDataTrabalho() == null ? Calendario.hoje() : caixaControle.getCaixaVoEmAberto().getDataTrabalho());
                this.getLote().setDataLancamento(caixaControle.getCaixaVoEmAberto().getDataTrabalho() == null || (caixaControle.getCaixaVoEmAberto().getDataTrabalho() != null && Calendario.igual(caixaControle.getCaixaVoEmAberto().getDataTrabalho(), Calendario.hoje()))   ? Calendario.hoje() : caixaControle.getCaixaVoEmAberto().getDataTrabalho());
            }
            if(openBankMesmoBanco){
                return "Richfaces.showModalPanel('panelTransferenciaMesmoBanco')";
            }else if(openBankOutrosBancos){
                return "Richfaces.showModalPanel('panelTransferenciaOutrosBancos')";
            }else {
                return "Richfaces.showModalPanel('panelDeposito')";
            }
        } catch (Exception e) {
            throw e;
        }

    }

    public String abrirModal(LoteVO lote, TipoOperacaoLancamento tipoOperacao, TipoES tipo,
                             TipoFormaPagto tipoForma, Double valor, boolean apresentarLote, ContaVO contaOrigem,
                             boolean transferirDinheiro, boolean habilitarValor, boolean recebidoZW, boolean openBankMesmoBanco, boolean openBankOutrosBancos) throws Exception {
        getMovContaControle().setValorLiquidoCalculado(0);
        return abrirModal(lote, tipoOperacao, tipo, tipoForma, valor, apresentarLote, contaOrigem, transferirDinheiro, habilitarValor, recebidoZW, null, null, null, openBankMesmoBanco, openBankOutrosBancos, 0,0.0);
    }

    public String abrirModal(LoteVO lote, TipoOperacaoLancamento tipoOperacao, TipoES tipo,
            TipoFormaPagto tipoForma, Double valor, boolean apresentarLote, ContaVO contaOrigem,
            boolean transferirDinheiro, boolean habilitarValor, boolean recebidoZW) throws Exception {
        getMovContaControle().setValorLiquidoCalculado(0);
        return abrirModal(lote, tipoOperacao, tipo, tipoForma, valor, apresentarLote, contaOrigem, transferirDinheiro, habilitarValor, recebidoZW, null, null, null,0);
    }

    public void gravarOperacaoConta() {
        FacesContext context = FacesContext.getCurrentInstance();
        HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
        String uri = request.getRequestURI();

        gravarOperacaoContaGeral(false,false,false, uri);
    }

    public void gravarOperacaoContaOpenBankOutrosBancos() {
        gravarOperacaoContaGeral(true,false, true, "");
    }

    public void gravarOperacaoContaOpenBankMesmoBanco() {
        gravarOperacaoContaGeral(true, true, false, "");
    }

    public void gravarOperacaoContaGeral(boolean transferindoOpenBank, boolean openBankMesmoBanco, boolean openBankOutrosBancos, String uri) {
        try {
            setOnCompleteModalOperacao("");

            if(transferindoOpenBank){
                getMovContaControle().getMovContaVO().setEmpresaVO(getEmpresaLogado());
                getMovContaControle().getMovContaVO().setPessoaVO(getUsuarioLogado().getColaboradorVO().getPessoa());
                getMovContaControle().getMovContaVO().setDescricao("Transferência online - ZillyonWeb FIN | Pacto Software Gestão");
            }

            getMovContaControle().getMovContaVO().setDescricao(apresentarLote ? getLote().getDescricao()
                    : getMovContaControle().getMovContaVO().getDescricao());

            if (!getUsarMovimentacao()) {
                getMovContaControle().getMovContaVO().setContaVO(
                        getFacade().getFinanceiro().getConta().consultarOuCriarContaBancoPadrao(
                        getMovContaControle().getMovContaVO().getEmpresaVO()));
                for (SelectItem si : getMovContaControle().getListaSelectItemFormaPagamento()) {
                    Integer codigo = (Integer) si.getValue();
                    if (!UteisValidacao.emptyNumber(codigo)) {
                        getMovContaControle().getMovContaRateioVO().getFormaPagamentoVO().setCodigo(codigo);
                        break;
                    }

                }
            }
            
            boolean transferencia = contaOrigem != null && tipoOperacao.equals(TipoOperacaoLancamento.TRANSFERENCIA);

            if (transferencia) {
                if (contaOrigem.getCodigo() == getMovContaControle().getMovContaVO().getContaVO().getCodigo()) {
                    throw new Exception("A conta de destino não pode ser a mesma conta de origem.");
                }
                GerenciadorContaControle gerenciadorContaControle = JSFUtilities.getControlador(GerenciadorContaControle.class);
                if (gerenciadorContaControle != null && gerenciadorContaControle.isTransferindoSaldoResumoContas()) {
                    //Este tratamento aqui é necessário pois o cliente no momento da transaferência pode copiar e colar a descrição da conta de origem, o que faz com que no momentod e gravar o rateio o item esteja como entrada,
                    // com descrição que contenha "saída" e vai mudar o item incorretamente para saída, quando deve permanecer como entrada
                    getMovContaControle().getMovContaVO().setDescricao(getMovContaControle().getMovContaVO().getDescricao().replaceAll("-", ""));
                }
            }

            if(!transferindoOpenBank) {
                MovContaVO.validarDadosOperacao(getMovContaControle().getMovContaVO());
            }

            ComportamentoConta comportamentoConta = getFacade().getFinanceiro().getTipoConta().consultarComportamento(
                    getMovContaControle().getMovContaVO().getContaVO().getCodigo());
            comportamentoConta = comportamentoConta == null ? ComportamentoConta.CAIXA : comportamentoConta;
            //Validar se já está em uma conta do tipo devolução antes de jogar em outra conta do mesmo tipo devolução.
            if(comportamentoConta.equals(ComportamentoConta.DEVOLUCOES)) {
                for (ChequeTO cheque : getLote() != null ? getLote().getChequesTO() : new ArrayList<ChequeTO>()) {
                    if (cheque.getCodigoContaContido() != 0) {
                        ContaVO contaDev = getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(cheque.getCodigoContaContido(), Uteis.NIVELMONTARDADOS_TODOS);
                        if (contaDev.getTipoConta().getComportamento().equals(ComportamentoConta.DEVOLUCOES)) {
                            throw new Exception("O cheque de número " + cheque.getNumero() + ", do pagador " + cheque.getNomePagador() + ", já foi movimentado para uma conta do tipo devolução.");
                        }
                    }
                }
            }

            if (comportamentoConta == null) {
                throw new Exception("O tipo de conta da conta selecionada não possui comportamento.");
            }

            if (comportamentoConta != null && comportamentoConta.equals(ComportamentoConta.BANCO)
                    && tipoOperacao.equals(TipoOperacaoLancamento.CUSTODIA)) {
                tipoOperacao = TipoOperacaoLancamento.DEPOSITO;
                getMovContaControle().getMovContaVO().setTipoOperacaoLancamento(TipoOperacaoLancamento.DEPOSITO);
            }

            if (tipoOperacao != null && comportamentoConta != null && getMovContaControle().getMovContaVO().getTipoForma() != null &&
                    tipoOperacao.equals(TipoOperacaoLancamento.DEPOSITO) && UteisValidacao.emptyNumber(getMovContaControle().getMovContaVO().getValorLiquido()) &&
                    comportamentoConta.equals(ComportamentoConta.BANCO) &&
                    (getMovContaControle().getMovContaVO().getTipoForma().equals(TipoFormaPagto.CARTAOCREDITO) ||
                            getMovContaControle().getMovContaVO().getTipoForma().equals(TipoFormaPagto.CARTAODEBITO))) {
                throw new Exception("Operações de Depósito para conta tipo Banco, deve ter Valor Líquido em um formato válido maior que 0,00.");
            }

            if (getLote() != null) {
                getLote().setDataDeposito(dataDeposito);
            }

            if (apresentarLote && getLote().getDataDeposito() == null) {
                throw new Exception("Informe a DATA DE DEPÓSITO do lote.");
            }

            getMovContaControle().prepararAtributosParaOperacao();

            //Aconteceu o caso de após usar a tela de Lançamento Rápido, os dados de plano de contas e centro de custo continuarem no objeto em outras telas copmo a de recebíveis,
            //consequentemente esses dados eram gravados na movcontarateio, fazendo com que no fechamento de caixa os recebíveis movimentados no dia as vezes se apresentassem como despesas
            //Para evitar, criada tratativa para limpar esses dados caso a requisição esteja vindo de: /ZillyonWeb/faces/pages/finan/gestaoRecebiveis.jsp
            if(uri.equalsIgnoreCase(urlTratativaGestaoRecebiveis) && getMovContaControle().getMovContaVO().getMovContaRateios() != null) {
                for(MovContaRateioVO movContaRateioVO : getMovContaControle().getMovContaVO().getMovContaRateios()) {
                    movContaRateioVO.setPlanoContaVO(new PlanoContaTO());
                    movContaRateioVO.setCentroCustoVO(new CentroCustoTO());
                }
            }

            //Aconteceram alguma situações em que comportamentoConta/tipoOperacao de Custódia está ficando com o MovContaVO.movContaRateio de saída, o que não deveria acontecer.
            //Desconfiamos que é residuo de alguma operação anterior que ficou no controlador, mas não conseguimos simular para identificar
            //Para evitar que isso aconteça, vamos forçar que o rateio seja de entrada.
            if (!UteisValidacao.emptyString(uri) && comportamentoConta.equals(ComportamentoConta.CUSTODIA)) {
                getMovContaControle().getMovContaRateioVO().setTipoES(TipoES.ENTRADA);
                for (MovContaRateioVO movContaRateioVO: getMovContaControle().getMovContaVO().getMovContaRateios()) {
                    movContaRateioVO.setTipoES(TipoES.ENTRADA);
                }
            }

            getMovContaControle().getMovContaVO().setTemLote(apresentarLote);
            if (apresentarLote) {
                if (UteisValidacao.emptyNumber(lote.getEmpresa().getCodigo())) {
                    lote.setEmpresa(getEmpresaLogado());
                }
                if ((getLote().getCheques() != null && !getLote().getCheques().isEmpty()) || (getLote().getCartoes() != null && !getLote().getCartoes().isEmpty())) {
                    getMovContaControle().getMovContaVO().setLote(getLote());
                } else {
                    getMovContaControle().getMovContaVO().setLote(null);
                }
            }
            CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
            StringBuilder txtLog = new StringBuilder();

            if (transferencia || (contaOrigem != null && UteisValidacao.emptyNumber(contaOrigem.getCodigo()))) {
                getMovContaControle().getMovContaVO().setContaOrigem(contaOrigem.getCodigo());
            }

            if (getMovContaControle().getMovContaVO().getTipoForma() != null &&
                    getMovContaControle().getMovContaVO().getTipoForma().equals(TipoFormaPagto.PIX)) {
                getMovContaControle().getMovContaVO().setMovimentandoTipoFormaPix(true);
            }

            getMovContaControle().getMovContaVO().setTipoForma(tipoForma);
            getMovContaControle().getMovContaVO().setValorPago(getMovContaControle().getMovContaVO().getValor());
            getMovContaControle().getMovContaVO().setLiquido(getMovContaControle().getMovContaVO().getTipoForma().equals(TipoFormaPagto.CARTAOCREDITO));
            getMovContaControle().getMovContaVO().setApresentarNoCaixa(apresentarNoCaixa);

            Calendar instance = Calendario.getInstance();
            getMovContaControle().getMovContaVO().setDataCompetencia(dataDeposito);
            getMovContaControle().getMovContaVO().setDataVencimento(dataDeposito);
            getMovContaControle().getMovContaVO().setDataQuitacao(
                    Uteis.getDateTime(dataDeposito, instance.get(Calendar.HOUR_OF_DAY), instance.get(Calendar.MINUTE), instance.get(Calendar.SECOND)));

            validarDadosConsultadosAntesOperacao();

            boolean transferindoSaldoResumoContas = false;
            try {
                GerenciadorContaControle gerenciadorContaControle = JSFUtilities.getControlador(GerenciadorContaControle.class);
                transferindoSaldoResumoContas = gerenciadorContaControle.isTransferindoSaldoResumoContas();
            } catch (Exception ex) {
            }

            //se estiver transferindo saldo entre contas, nunca deve ter plano de contas e centro de custos.
            //As vezes acontecia de gravar com plano de contas e centro de custos essas transferências pois compartilham o mesmo controlar dor com o gestão de recebíveis.
            if (transferindoSaldoResumoContas || (
                    getMovContaControle().getMovContaVO().getTipoOperacaoLancamento() != null &&
                    getMovContaControle().getMovContaVO().getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.DEPOSITO))) {
                //limpar controladores para evitar qualquer atribuição posterior lá na frente
                limparControladoresPlanoECentro();

                //limpar plano e centro da movconta
                if (!UteisValidacao.emptyList(getMovContaControle().getMovContaVO().getMovContaRateios())) {
                    getMovContaControle().getMovContaVO().getMovContaRateios().get(0).setPlanoContaVO(new PlanoContaTO());
                    getMovContaControle().getMovContaVO().getMovContaRateios().get(0).setCentroCustoVO(new CentroCustoTO());
                }
            }

            int codMovContaSaidaTransferenciaOnline = 0;
            getFacade().getFinanceiro().getMovConta().incluir(
                    getMovContaControle().getMovContaVO(), caixaControle.getCaixaVoEmAberto().getCodigo(), false, comportamentoConta);
            caixaControle.atualizarCaixa();

            boolean gravarSaidaDevolucao = comportamentoConta.equals(ComportamentoConta.DEVOLUCOES)
                    && lote != null
                    && tipoForma.equals(TipoFormaPagto.CHEQUE);
            //caso tenha saida de devolução, não gravar a saida normal
            if ((transferencia || (contaOrigem != null && !UteisValidacao.emptyNumber(contaOrigem.getCodigo())))
                    && !gravarSaidaDevolucao) {
                codMovContaSaidaTransferenciaOnline = gravarSaida(getMovContaControle().getMovContaVO(), caixaControle.getCaixaVoEmAberto().getCodigo(), contaOrigem, transferencia, transferindoOpenBank);
            }
            boolean devolucaoCheque = false;
            if(gravarSaidaDevolucao){
                devolucaoCheque = getFacade().getFinanceiro().getDevolucaoServico().processarMovimentacaoCheques(
                        getKey(),
                        getPropertyValue("urlNotificacaoAcesso"),
                        getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo(),
                        lote.getCheques(),
                        getMovContaControle().getMovContaVO(),
                        getCfg(),
                        caixaControle.getCaixaVoEmAberto().getCodigo(),
                        getUsuarioLogado(),
                        contaOrigem
                        );
            }

            if ((lote != null && !UteisValidacao.emptyNumber(lote.getCodigo()))
                    && (tipoOperacao.equals(TipoOperacaoLancamento.CUSTODIA)
                    || tipoOperacao.equals(TipoOperacaoLancamento.DEPOSITO))
                    && tipoForma.equals(TipoFormaPagto.CHEQUE)) {
                for (ChequeVO cheque : lote.getCheques()) {
                    getFacade().getFinanceiro().getHistoricoCheque().inicializarHistorico(cheque, getMovContaControle().getMovContaVO().getCodigo(), getLote(),
                            devolucaoCheque ? TipoOperacaoLancamento.DEVOLUCAO_CHEQUE : tipoOperacao);
                }
            }
            if(tipoForma.equals(TipoFormaPagto.CHEQUE) && contaOrigem != null){
                getFacade().getFinanceiro().getDevolucaoServico().voltarChequeEstadoNatural(
                        getKey(),
                        getPropertyValue("urlNotificacaoAcesso"),
                        getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo(),
                        lote.getCheques(),
                        getCfg(),
                        getUsuarioLogado(),
                        contaOrigem.getCodigo());
            }
            if ((lote != null && !UteisValidacao.emptyNumber(lote.getCodigo()))
                    && (tipoOperacao.equals(TipoOperacaoLancamento.CUSTODIA) || tipoOperacao.equals(TipoOperacaoLancamento.DEPOSITO))
                    && tipoForma.equals(TipoFormaPagto.CARTAOCREDITO)) {
                for (CartaoCreditoVO cartao : lote.getCartoes()) {
                    getFacade().getFinanceiro().getHistoricoCartao().inicializarHistorico(cartao, getMovContaControle().getMovContaVO().getCodigo(), getLote());
                }
            }
            if(!transferindoOpenBank) {
                getMovContaControle().getMovContaVO().setContaVO(
                        getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(getMovContaControle().getMovContaVO().getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            GestaoRecebiveisControle gestaoRecebiveisControle = (GestaoRecebiveisControle) getControlador(GestaoRecebiveisControle.class.getSimpleName());

            if (lote == null || UteisValidacao.emptyNumber(lote.getCodigo())) {
                Boolean pagamentoAV = false;
                 if (!transferencia) {
                    for (MovPagamentoVO pagamento : gestaoRecebiveisControle.getListaOutros()) {
                        if (pagamento.getMovPagamentoEscolhidaFinan()) {
                            getFacade().getMovPagamento().alterarSomenteEntrouNoCaixa(pagamento, getMovContaControle().getMovContaVO().getCodigo());
                            pagamento.setMovconta(getMovContaControle().getMovContaVO().getCodigo());
                            pagamento.setDataMovimento(getMovContaControle().getMovContaVO().getDataQuitacao());
                            pagamento.setContaFinanceiro(getMovContaControle().getMovContaVO().getContaVO().getDescricao());
                            if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("AV")){
                                pagamentoAV  = true;
                            }
                            txtLog.append(" Código: ").append(pagamento.getCodigo());
                            txtLog.append(" Nome: ").append(pagamento.getNomePagador())
                                    .append(" - Valor: ").append(pagamento.getValorApresentar()).append(" - Conta: ").append(pagamento.getContaFinanceiro());
                            if (!pagamento.getAutorizacaoCartao().isEmpty()) {
                                txtLog.append(" - Autorização: ").append(pagamento.getAutorizacaoCartao());
                            }
                            if (!UteisValidacao.emptyString(uri)) {
                                txtLog.append(" - Tela Operação: ").append(uri);
                            }
                            gerarLog(txtLog, getMovContaControle().getMovContaVO().getCodigo());
                            txtLog = new StringBuilder();
                        }
                    }
                    if (pagamentoAV && comportamentoConta.equals(ComportamentoConta.BANCO) && getFacade().getConfiguracaoSistema().realizarEnvioSesiSC()){
                        List<Integer> listaMovConta = new ArrayList<>();
                        listaMovConta.add(getMovContaControle().getMovContaVO().getCodigo());
                         getFacade().getZWFacade().startThreadIntegracaoFiesc(getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo(), listaMovConta, "incluirDeposito");
                    }
                 }
            } else {
                for (CartaoCreditoTO ca : lote.getCartoesTO()) {
                    ca.setNumeroLote(lote.getCodigo());
                    ca.setContaContido(getMovContaControle().getMovContaVO().getContaVO().getDescricao());
                    ca.setCartaoEscolhido(false);
                    ca.setCodigoContaContido(getMovContaControle().getMovContaVO().getContaVO().getCodigo());
                    if (tipoOperacao.equals(TipoOperacaoLancamento.DEPOSITO)) {
                        ca.setDataCompensacao(lote.getDataDeposito());
                        txtLog.append(" Depósito: ").append(ca.getDataCompensacaoApresentar());
                    }
                    txtLog.append(" Código: ").append(ca.getCodigo());
                    txtLog.append(" Nome: ").append(ca.getNomePagador()).append(" - Operadora: ").append(ca.getOperadora());
                    txtLog.append(" - Valor: ").append(ca.getValor());
                    txtLog.append(" - Conta: ").append(ca.getContaContido());
                    if (!UteisValidacao.emptyString(uri)) {
                        txtLog.append(" - Tela Operação: ").append(uri);
                    }
                    gerarLog(txtLog, getMovContaControle().getMovContaVO().getCodigo());
                    txtLog = new StringBuilder();
                }

                for (ChequeTO ch : lote.getChequesTO()) {
                    ch.setContaContido(getMovContaControle().getMovContaVO().getContaVO().getDescricao());
                    ch.setCodigoContaContido(getMovContaControle().getMovContaVO().getContaVO().getCodigo());
                    ch.setNumeroLote(lote.getCodigo());
                    ch.setChequeEscolhido(false);
                    if (tipoOperacao.equals(TipoOperacaoLancamento.DEPOSITO)) {
                        ch.setDataCompensacao(lote.getDataDeposito());
                        txtLog.append(" Depósito: ").append(ch.getDataCompensacaoApresentar());
                    }
                    txtLog.append(" Código: ").append(ch.getCodigo());
                    txtLog.append(" Nome: ").append(ch.getNomePagador());
                    txtLog.append(" - Valor: ").append(ch.getValor());
                    txtLog.append(" - Conta: ").append(ch.getContaContido());
                    if (!UteisValidacao.emptyString(uri)) {
                        txtLog.append(" - Tela Operação: ").append(uri);
                    }
                    gerarLog(txtLog, getMovContaControle().getMovContaVO().getCodigo());
                    txtLog = new StringBuilder();
                }
            }

            if(transferindoOpenBank){
                if(openBankMesmoBanco) {
                    transferirMesmoBancoStoneOpenBank(codMovContaSaidaTransferenciaOnline);
                }else if(openBankOutrosBancos){
                    transferirOutrosBancosStoneOpenBank(codMovContaSaidaTransferenciaOnline);
                }
            }

            if (devolucaoCheque) {
                if (getCfg().getPlanoContasDevolucao() == null || UteisValidacao.emptyNumber(getCfg().getPlanoContasDevolucao().getCodigo())) {
                    montarSucesso("msg_cheques_devolvidos_finan_configure");
                } else {
                    montarSucesso("msg_cheques_devolvidos_finan");
                }
            } else {
                montarSucesso("msg_dados_gravados");
            }

            setOnCompleteModalOperacao(getMensagemNotificar() + ";" +
                    "Richfaces.hideModalPanel('panelDeposito');atualizar();");
            gestaoRecebiveisControle.setQtdeTotal(0);
            gestaoRecebiveisControle.setQtdeTotalCartoes(0);
            gestaoRecebiveisControle.setQtdeTotalCheques(0);
            gestaoRecebiveisControle.setQtdeTotalVista(0);
            gestaoRecebiveisControle.setTotalCartoes(0.0);
            gestaoRecebiveisControle.setTotalCheques(0.0);
            gestaoRecebiveisControle.setTotalVista(0.0);

            if (comportamentoConta.equals(ComportamentoConta.BANCO) && tipoForma != null && tipoForma.equals(TipoFormaPagto.CARTAODEBITO)) {
                gestaoRecebiveisControle.setDataNovaCompensacaoCartoes(getDataDeposito());
                gestaoRecebiveisControle.setAlterarCredito(false);
                gestaoRecebiveisControle.alterarDataCompensacaoCartao();

            }
            gestaoRecebiveisControle.desmarcarTudo();
            if(gestaoRecebiveisControle.isVisaoConciliacao()){
                gestaoRecebiveisControle.consultarConciliacao();
            }

            if (lote != null) {
                JSFUtilities.storeOnSession("codigoLoteSessao", lote.getCodigo());
            }

            setErro(false);
            setSucesso(true);
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
            setSucesso(false);
            setErro(true);
        }
    }

    public void limparControladoresPlanoECentro() {
        try {
            PlanoContasControle controlPlanoContas = (PlanoContasControle) JSFUtilities.getFromSession(PlanoContasControle.class.getSimpleName());
            CentroCustosControle controlCentro = (CentroCustosControle) JSFUtilities.getFromSession(CentroCustosControle.class.getSimpleName());

            if (controlPlanoContas == null) {
                controlPlanoContas = new PlanoContasControle();
            }
            if (controlCentro == null) {
                controlCentro = new CentroCustosControle();
            }

            controlPlanoContas.limpar();
            controlCentro.limpar();
        } catch (Exception ex) {
        }
    }

    public void gerarLog(StringBuilder txtLog, String valorCampoAnterior, Integer chavePrimaria) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("MOVIMENTACAORECEBIVEIS");
        log.setNomeEntidadeDescricao("Movimentação");
        log.setChavePrimaria(chavePrimaria.toString());
        log.setNomeCampo("Movimentações");
        log.setValorCampoAlterado(txtLog.toString());
        log.setValorCampoAnterior(valorCampoAnterior);
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getUsuarioLogado().getNome().toUpperCase());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        log.setOperacao("ALTERAÇÃO");
        getFacade().getLog().incluir(log);
    }


    public void gerarLog(StringBuilder txtLog, Integer chavePrimaria) throws Exception {
        gerarLog(txtLog, null, chavePrimaria);
    }

    public void verificarSaida(MovContaVO movConta, int caixaAberto) throws Exception {
        if (tipoForma.equals(TipoFormaPagto.CHEQUE)) {
            Map<Integer, Double> contas = getFacade().getCheque().consultarValorSaidaParaContas(movConta.getLote().getCheques());
            Set<Integer> keySet = contas.keySet();
            for (Integer conta : keySet) {
                if (UteisValidacao.emptyNumber(contas.get(conta))) {
                    continue;
                }
                MovContaVO clone = (MovContaVO) movConta.getClone(true);
                clone.setValor(contas.get(conta));
                ContaVO contaVO = new ContaVO(conta);
                gravarSaida(clone, caixaAberto, contaVO, false, false);
            }
        }
        if (tipoForma.equals(TipoFormaPagto.CARTAOCREDITO)) {
            Map<Integer, Double> contas = getFacade().getCartaoCredito().consultarValorSaidaParaContas(movConta.getLote().getCartoes(), movConta.getLote().getCodigo());
            Set<Integer> keySet = contas.keySet();
            for (Integer conta : keySet) {
                if (UteisValidacao.emptyNumber(contas.get(conta))) {
                    continue;
                }
                MovContaVO clone = (MovContaVO) movConta.getClone(true);
                clone.setValor(contas.get(conta));
                ContaVO contaVO = new ContaVO(conta);
                gravarSaida(clone, caixaAberto, contaVO, false, false);
            }
        }
    }

    /**
     * <AUTHOR> Alcides 07/11/2012
     */
    public int gravarSaida(MovContaVO entrada, int caixaAberto, ContaVO origem, boolean gravarHistorico, boolean transferindoOpenBank) throws Exception {
        MovContaVO movConta = (MovContaVO) entrada.getClone(true);
        movConta.setTemLote(false);
        ContaVO conta = null;
        try {
            conta = getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(entrada.getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }catch (Exception ex){
            if(!transferindoOpenBank){
                throw new Exception(ex);
            }
        }
        movConta.setDescricao("Saída de valor - CONTA DESTINO : " + ((transferindoOpenBank && conta == null) ? movConta.getContaDestinoOpenBank().getBanco().getNomeByCodigoBanco() : conta.getDescricao()));
        movConta.setContaOrigem(null);
        movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.TRANSFERENCIA);
        movConta.setApresentarNoCaixa(false);
        movConta.setMovContaRateios(new ArrayList<MovContaRateioVO>());
        movConta.setContaVO(origem);
        movConta.setLiquido(false);
        movConta.setValorLiquido(0.0);

        MovContaRateioVO saida = new MovContaRateioVO();
        if (!entrada.getMovContaRateios().isEmpty()) {
            saida.setFormaPagamentoVO(entrada.getMovContaRateios().get(0).getFormaPagamentoVO());
        }
        saida.setTipoES(TipoES.SAIDA);
        saida.setDescricao(movConta.getDescricao());
        saida.setValor(movConta.getValor());
        movConta.getMovContaRateios().add(saida);
        //Joao Alcides: ao gravar a saida não precisa validar a forma de pagamento, caso seja uma operação que necessite dessa validação, a inserção da entrada é que validará os dados
        getFacade().getFinanceiro().getMovConta().incluir(movConta, caixaAberto, false, null);

        StringBuilder txtLog = new StringBuilder();
        txtLog.append("Transferência' entre contas. 'Conta origem: ").append(origem.getDescricao());
        txtLog.append(" para a Conta destino: ").append((transferindoOpenBank && conta == null) ? movConta.getContaDestinoOpenBank().getBanco().getNomeByCodigoBanco() : conta.getDescricao());

        if(movConta != null && UteisValidacao.emptyNumber(movConta.getCodigo())) {
            gerarLog(txtLog, Formatador.formatarValorMonetario(movConta.getValor()), null);
        } else if(movConta != null) {
            gerarLog(txtLog, Formatador.formatarValorMonetario(movConta.getValor()), movConta.getCodigo());
        }

        if (gravarHistorico && apresentarLote && tipoForma.equals(TipoFormaPagto.CHEQUE)) {
            for (ChequeVO cheque : lote.getCheques()) {
                HistoricoChequeVO histCheque = new HistoricoChequeVO();
                histCheque.setCheque(cheque);
                histCheque.setStatus(StatusCheque.TRANSFERENCIA);
                histCheque.setDataInicio(Calendario.hoje());
                histCheque.setLote(movConta.getLote());
                histCheque.setMovConta(entrada);
//				getFacade().getLote().retirarChequeDoLote(cheque, movConta.getLote().getCodigo());

                getFacade().getFinanceiro().getHistoricoCheque().incluir(histCheque);
            }
        }else
        if (gravarHistorico && apresentarLote && tipoForma.equals(TipoFormaPagto.CARTAOCREDITO)) {
            for (CartaoCreditoVO cartao : lote.getCartoes()) {
                HistoricoCartaoVO histCartao = new HistoricoCartaoVO();
                histCartao.setCartao(cartao);
                histCartao.setDataInicio(Calendario.hoje());
                histCartao.setLote(movConta.getLote());
                histCartao.setMovConta(entrada);
//				getFacade().getLote().retirarChequeDoLote(cheque, movConta.getLote().getCodigo());

                getFacade().getFinanceiro().getHistoricoCartao().incluir(histCartao);
            }
        }
        if (apresentarLote) {
            JSFUtilities.storeOnSession("codigoLoteSessao", lote.getCodigo());
        }

        return movConta.getCodigo();
    }

    public void imprimirComprovanteListener(ActionEvent evt) throws Exception {
        Integer movConta = (Integer) evt.getComponent().getAttributes().get("movconta");
        MovContaVO movContaVO = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(movConta, Uteis.NIVELMONTARDADOS_TODOS);


        imprimirComprovante(movContaVO);


    }

    public void imprimirComprovante(MovContaVO movConta) throws Exception {
        List<ChequeVO> cheques = new ArrayList<ChequeVO>();
        List<CartaoCreditoVO> cartoes = new ArrayList<CartaoCreditoVO>();
        cartoes.addAll(movConta.getCartoes());
        cheques.addAll(movConta.getCheques());

        if (movConta.getLote() != null) {
            cartoes.addAll(getFacade().getCartaoCredito().consultarPorLote(movConta.getLote().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            cheques.addAll(getFacade().getCheque().consultarPorLote(movConta.getLote().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        }

        ComprovanteOperacaoTO comp = new ComprovanteOperacaoTO();
        comp.setCodigo(movConta.getCodigo().toString());
        comp.setDataLancamento(movConta.getDataLancamento());
        comp.setDataQuitacao(movConta.getDataQuitacao());
        comp.setContaDestino(movConta.getContaVO().getDescricao());
        comp.setLote(movConta.getLote().getDescricao());
        comp.setDescricao(movConta.getDescricao());
        comp.setFornecedor(movConta.getFavorecido());
        comp.setObservacoes(movConta.getObservacoes());
        TipoES tipoes = null;
        for (MovContaRateioVO movContaRateio : movConta.getMovContaRateios()) {
            comp.setFormaPagamento(movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.AJUSTESALDO)
                    ? ("CONCILIAÇÃO DE SALDO : " + movContaRateio.getValorFormatado())
                    : comp.getFormaPagamento() + " \n"
                    + movContaRateio.getDescricao() + " - "
                    + movContaRateio.getFormaPagamentoVO().getDescricao() + " : " + movContaRateio.getValorFormatado());
            tipoes = movContaRateio.getTipoES();
        }

        comp.setValor(movConta.getValor());
        if (movConta.getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.TRANSFERENCIA) && tipoes.equals(TipoES.ENTRADA)) {
            ContaVO contaOrigem = getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(movConta.getContaOrigem(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            comp.setTipoOperacao(movConta.getTipoOperacaoLancamento().getLabel() + " - " + contaOrigem.getDescricao());
        } else {
            comp.setTipoOperacao(movConta.getTipoOperacaoLancamento().getLabel());
        }
        Map<String, Object> params = new HashMap<String, Object>();
        List lista = new ArrayList();
        lista.add(comp);

        params.put("nomeRelatorio", "ComprovanteOperacao");
        params.put("nomeEmpresa", movConta.getEmpresaVO().getNome());
        params.put("tituloRelatorio", "Comprovante Operação");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", lista);
        params.put("enderecoEmpresa", movConta.getEmpresaVO().getEndereco());
        params.put("cidadeEmpresa", movConta.getEmpresaVO().getCidade().getNome());
        params.put("tipoOperacao", comp.getTipoOperacao());
        params.put("exibirCheques", !cheques.isEmpty());
        params.put("exibirCartoes", !cartoes.isEmpty());
        params.put("fornecedor", movConta.getFavorecido());

        params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
        JRDataSource jr1 = new JRBeanArrayDataSource(cheques.toArray());
        params.put("listaCheques", jr1);

        JRDataSource jr2 = new JRBeanArrayDataSource(cartoes.toArray());
        params.put("listaCartoes", jr2);

        apresentarRelatorioObjetos(params);
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "ComprovanteOperacao.jrxml");
    }

    public MovContaControle getMovContaControle() {
        return (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
    }

    public void setOnCompleteModalOperacao(String onCompleteModalOperacao) {
        this.onCompleteModalOperacao = onCompleteModalOperacao;
    }

    public String getOnCompleteModalOperacao() {
        return onCompleteModalOperacao;
    }

    public void setLote(LoteVO lote) {
        this.lote = lote;
    }

    public LoteVO getLote() {
        return lote;
    }

    public void setApresentarLote(boolean apresentarLote) {
        this.apresentarLote = apresentarLote;
    }

    public boolean getApresentarLote() {
        return apresentarLote;
    }

    public void setContaOrigem(ContaVO contaOrigem) {
        this.contaOrigem = contaOrigem;
    }

    public ContaVO getContaOrigem() {
        return contaOrigem;
    }

    public TipoOperacaoLancamento getTipoOperacao() {
        return tipoOperacao;
    }

    public String getLabelModal() {
        if (getTipoOperacao() == null) {
            return "Operação";
        }
        return (getTipoOperacao().equals(TipoOperacaoLancamento.CUSTODIA)
                || getTipoOperacao().equals(TipoOperacaoLancamento.DEPOSITO))
                ? "Operação com recebíveis" : getTipoOperacao().getLabel();
    }

    public void setDataDeposito(Date dataDeposito) {
        this.dataDeposito = dataDeposito;
    }

    public Date getDataDeposito() {
        return dataDeposito;
    }

    public void setApresentarNoCaixa(boolean apresentarNoCaixa) {
        this.apresentarNoCaixa = apresentarNoCaixa;
    }

    public boolean isApresentarNoCaixa() {
        return apresentarNoCaixa;
    }

    public String editarLancamento() throws Exception {
        MovContaRateioVO obj = (MovContaRateioVO) context().getExternalContext().getRequestMap().get("movContaRateio");
        MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
        return prepararEdicao(obj, movContaControle);
    }

    /**
     * <AUTHOR> Alcides 20/05/2013
     */
    private String prepararEdicao(MovContaRateioVO obj, MovContaControle controle) throws Exception {
        MovContaVO movConta = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(obj.getMovContaVO(), Uteis.NIVELMONTARDADOS_TODOS);
        controle.setLancamentoDemonstrativo(Boolean.TRUE);
        controle.setOrigemConsulta("resumoContas");
        return controle.preparaEdicao(movConta);
    }

    public void editarLancamentoPopUp() throws Exception {
        MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
        MovContaRateioVO obj = (MovContaRateioVO) context().getExternalContext().getRequestMap().get("movContaRateio");
        movContaControle.setOrigemConsulta("resumoContas");
        prepararEdicao(obj, movContaControle);
        movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
        // numero de documento pode sempre ser exibido nessa tela de edição do resumo de contas
        movContaControle.setExibirNumeroDocumeto(true);
    }

    public Boolean getUsarMovimentacao() {
        try {
            return getCfg().getUsarMovimentacaoContas();
        } catch (Exception e) {
            return true;
        }
    }

    public ConfiguracaoFinanceiroVO getCfg() {
        try {
            ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class.getSimpleName());
            return cfg.getConfFinanceiro();
        } catch (Exception e) {
            return new ConfiguracaoFinanceiroVO();
        }
    }

    private void validarDadosConsultadosAntesOperacao() throws Exception {
        GestaoRecebiveisControle gestaoRecebiveisControle = (GestaoRecebiveisControle) getControlador(GestaoRecebiveisControle.class.getSimpleName());
         if (gestaoRecebiveisControle.getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
             if (lote != null) {
                 for (ChequeTO ch : lote.getChequesTO()) {
                     ChequeTO aux = new ChequeTO();
                     aux.setCodigo(ch.getCodigo());
                     aux.setCodigosComposicao(ch.getCodigosComposicao());
                     getFacade().getFinanceiro().getHistoricoCheque().getContaLoteCheque(aux, true);
                     if ((contaOrigem == null && aux.getCodigoContaContido() != 0) || (contaOrigem != null && aux.getCodigoContaContido() != contaOrigem.getCodigo())) {
                         throw new Exception("O(s) cheque(s) que você está tentando movimentar parece(m) ter sido alterado(s) por outra operação ápos o inicio dessa operação. Para evitar movimentações indevidas, consulte novamente os dados e tente refazer a operação!");
                     }
                 }
             }
        } else if (gestaoRecebiveisControle.getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
             if (lote != null) {
                 for (CartaoCreditoTO ca : lote.getCartoesTO()) {
                     CartaoCreditoTO aux = new CartaoCreditoTO();
                     aux.setCodigo(ca.getCodigo());
                     aux.setCodigosComposicao(ca.getCodigosComposicao());
                     getFacade().getFinanceiro().getHistoricoCartao().getContaLoteCartao(aux, true);
                     if ((contaOrigem == null && aux.getCodigoContaContido() != 0) || (contaOrigem != null && aux.getCodigoContaContido() != 0 && aux.getCodigoContaContido() != contaOrigem.getCodigo())) {
                         throw new Exception("O(s) cartão(ões) que você está tentando movimentar parece(m) ter sido alterado(s) por outra operação ápos o inicio dessa operação. Para evitar movimentações indevidas, consulte novamente os dados e tente refazer a operação!");
                     }
                 }
             }
        } else if (gestaoRecebiveisControle.getExibirAutorizacao()) {
            String codigosMovConta = "";
            for (MovPagamentoVO movPag : gestaoRecebiveisControle.getListaOutros()) {
                if (movPag.getMovPagamentoEscolhidaFinan()) {
                    Integer movContaAtual = getFacade().getMovPagamento().obterMovContaPagamento(movPag.getCodigo());
                    codigosMovConta += ","+movContaAtual;
                }
            }
            if (!UteisValidacao.emptyString(codigosMovConta)) {
                List<ContaVO> contas = getFacade().getFinanceiro().getConta().consultarContasPorMovContas(codigosMovConta.replaceFirst(",", ""));
                if ((!UteisValidacao.emptyList(contas) && ((contas.size() > 1 || contaOrigem == null) || (!contas.get(0).getCodigo().equals(contaOrigem.getCodigo()))))
                        || (UteisValidacao.emptyList(contas) && contaOrigem != null)) {
                    throw new Exception("O(s) pagamento(s) que você está tentando movimentar parece(m) ter sido alterado(s) por outra operação ápos o inicio dessa operação. Para evitar movimentações indevidas, consulte novamente os dados e tente refazer a operação!");
                }
            }
        }
    }

    public String transferirMesmoBancoStoneOpenBank(int codMovContaTransferenciaOnline) {
        try {
            String url = Uteis.getUrlDiscovery("openBankingMs") + "/stonebank/transferirMesmoBanco";
            HashMap<String, String> headers = new HashMap<>();
            headers.put("chaveZW", getKey());
            headers.put("account_id", getFacade().getContaStone().buscarAccountIdContaStone(getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo()));
            headers.put("descricao", getMovContaControle().getMovContaVO().getDescricao());
            headers.put("dataTransferencia", Calendario.getDataAplicandoFormatacao(getDataDeposito(), "yyyy-MM-dd"));
            headers.put("contaTransferir", getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getNumero() + getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getNumeroDV());
            Integer valorTransferencia = (int) (getMovContaControle().getMovContaVO().getValor() * 100);
            headers.put("valor", valorTransferencia.toString());
            headers.put("producao", PropsService.getPropertyValue(PropsService.stoneOpenBankProducao));
            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            TransferenciaStoneVO transferenciaStone = getFacade().getTransferenciaStone().montarTransferencia(new JSONObject(response).getString("content"), codMovContaTransferenciaOnline,
                    getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo(), getMovContaControle().getMovContaVO().getCodigo());
            transferenciaStone.setOperation_type("internal_transfer");
            getFacade().getTransferenciaStone().incluir(transferenciaStone);
            return "";
        } catch (Exception ignore) {
            // Retorna saldo zerado sem impactar.
        }
        return "";
    }

    public String transferirOutrosBancosStoneOpenBank(int codMovContaTransferenciaOnline) {
        try {
            String url = Uteis.getUrlDiscovery("openBankingMs") + "/stonebank/transferirOutrosBancos";
            HashMap<String, String> headers = new HashMap<>();
            headers.put("chaveZW", getKey());
            headers.put("account_id", getFacade().getContaStone().buscarAccountIdContaStone(getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo()));

            Integer valorTransferencia = (int) (getMovContaControle().getMovContaVO().getValor() * 100);
            JSONObject body = new JSONObject()
                    .put("dataTransferencia", Calendario.getDataAplicandoFormatacao(getDataDeposito(), "yyyy-MM-dd"))
                    .put("producao", PropsService.getPropertyValue(PropsService.stoneOpenBankProducao))
                    .put("agenciaTransferir", getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getAgencia() + getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getAgenciaDV())
                    .put("contaTransferir", getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getNumero() + getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getNumeroDV())
                    .put("codigoBancoTransferir", getMovContaControle().getMovContaVO().getContaDestinoOpenBank().getBanco().getCodigoBanco().toString())
                    .put("tipoContaTransferir", getMovContaControle().getTipoContaDestinoOpenBank())
                    .put("nomeContaTransferir", getMovContaControle().getNomeDestinoOpenBank())
                    .put("tipoDocumentoContaTransferir", getMovContaControle().getTipoDocumentoDestinoOpenBank())
                    .put("documentoContaTransferir", Uteis.removerMascara(getMovContaControle().getDocumentoDestionoOpenBank()))
                    .put("valorTransferir", valorTransferencia);

            String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, body.toString(), headers);
            TransferenciaStoneVO transferenciaStone = getFacade().getTransferenciaStone().montarTransferencia(new JSONObject(response).getString("content"), codMovContaTransferenciaOnline,
                    getMovContaControle().getMovContaVO().getEmpresaVO().getCodigo(), getMovContaControle().getMovContaVO().getCodigo());
            transferenciaStone.setOperation_type("external_transfer");
            getFacade().getTransferenciaStone().incluir(transferenciaStone);
            return "";
        } catch (Exception ignore) {
            // Retorna saldo zerado sem impactar.
        }
        return "";
    }
}

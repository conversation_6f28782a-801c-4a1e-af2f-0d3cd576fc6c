package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ConvenioVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.EstornoMovProdutoVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;

import java.util.Calendar;

import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.HistoricoPontos;

import javax.faces.model.SelectItem;

public class EstornoMovProdutoControle extends SuperControle {

    protected EstornoMovProdutoVO estornoMovProdutoVO;
    protected Boolean abrirRichConfirmacaoEstorno;
    protected Boolean apresentarBotaoEstorno;
    protected Boolean temChequeOuCartaoEmlote = false;
    private Boolean mostrarNomeMatriculaAluno;
    protected List listaPessoaVOs;
    private CaixaVO caixaAberto = null;
    private String estornarTransacoes = "";

    public EstornoMovProdutoControle() {
        setControleConsulta(new ControleConsulta());
        setMostrarNomeMatriculaAluno(true);
        setSucesso(false);
        setErro(false);
    }

    public void inicializarUsuarioLogado() throws Exception {
        if (getUsuarioLogado() != null && getUsuarioLogado().getCodigo() != 0) {
            getEstornoMovProdutoVO().getResponsavelEstorno().setCodigo(getUsuarioLogado().getCodigo());
            getEstornoMovProdutoVO().getResponsavelEstorno().setUsername(getUsuarioLogado().getUsername());
            getEstornoMovProdutoVO().getResponsavelEstorno().setNome(getUsuarioLogado().getNome());
            getEstornoMovProdutoVO().getResponsavelEstorno().setAdministrador(getUsuarioLogado().getAdministrador());
            getEstornoMovProdutoVO().getResponsavelEstorno().setUserOamd(getUsuarioLogado().getUserOamd());
        } else {
            throw new Exception("Não há usuário logado.");
        }
    }

    public void novo() {
        novo(null);
    }

    /**
     * Método usado para inicializar atributos e para montar estorno de produtos de clientes
     */
    public void novo(Integer codMovProduto) {
        try {
            setEstornoMovProdutoVO(new EstornoMovProdutoVO());
            setAbrirRichConfirmacaoEstorno(true);
            setApresentarBotaoEstorno(true);
            setListaPessoaVOs(new ArrayList());
            inicializarFacades();
            setMensagemID("msg_entre_prmconsulta");
            MovProdutoVO obj = null;
            if (codMovProduto == null) {
                obj = (MovProdutoVO) context().getExternalContext().getRequestMap().get("historicoCompras");
            } else {
                obj = getFacade().getMovProduto().consultarPorChavePrimaria(codMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            getEstornoMovProdutoVO().setMovProdutoVO(obj);
            montarClienteEMovProdutoEstorno();
            inicializarUsuarioLogado();
            setMensagemID("msg_dados_prontos_exclusao");
            setErro(false);
            setSucesso(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setErro(true);
            setSucesso(false);
        }

    }

    public void recarregarInformacoes(MovProdutoVO produto) throws Exception {
        setEstornoMovProdutoVO(new EstornoMovProdutoVO());
        setAbrirRichConfirmacaoEstorno(true);
        setApresentarBotaoEstorno(true);
        setListaPessoaVOs(new ArrayList());
        getEstornoMovProdutoVO().setMovProdutoVO(produto);
        montarClienteEMovProdutoEstorno();
    }

    /**
     * Método usado para inicializar atributos e para montar estorno de produtos de colaboradores
     */
    public void novoParaColaborador() {
        try {
            setEstornoMovProdutoVO(new EstornoMovProdutoVO());
            setAbrirRichConfirmacaoEstorno(true);
            setApresentarBotaoEstorno(true);
            setListaPessoaVOs(new ArrayList());
            inicializarFacades();
            setMensagemID("msg_entre_prmconsulta");
            MovProdutoVO obj = (MovProdutoVO) context().getExternalContext().getRequestMap().get("historicoCompras");
            getEstornoMovProdutoVO().setMovProdutoVO(obj);
            montarColaboradorEMovProdutoEstorno();
            inicializarUsuarioLogado();
            setMensagemID("msg_entre_prmconsulta");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método usado para inicializar atributos e para chamar o método de montar estorno de produtos de consumidores
     */
    public void novoParaConsumidor() {
        try {
            setEstornoMovProdutoVO(new EstornoMovProdutoVO());
            setAbrirRichConfirmacaoEstorno(true);
            setApresentarBotaoEstorno(true);
            setListaPessoaVOs(new ArrayList());
            inicializarFacades();
            setMensagemID("msg_entre_prmconsulta");
            MovProdutoVO obj = (MovProdutoVO) context().getExternalContext().getRequestMap().get("historicoCompras");
            getEstornoMovProdutoVO().setMovProdutoVO(obj);
            montarConsumidorEMovProdutoEstorno();
            inicializarUsuarioLogado();
            setMensagemID("msg_entre_prmconsulta");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    /**
     * Método usado para montar estorno de produtos de clientes
     */
    public void montarClienteEMovProdutoEstorno() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                clienteControle.pegarClienteTelaCliente();
                getEstornoMovProdutoVO().setClienteVO(clienteControle.getClienteVO());
                if (clienteControle.getClienteVO().getCodigo() == 0 & (clienteControle.getClienteVO().getMatricula().trim().isEmpty()
                        | clienteControle.getClienteVO().getPessoa().getNome().trim().isEmpty())) {
                    setMostrarNomeMatriculaAluno(false);
                } else {
                    setMostrarNomeMatriculaAluno(true);
                }
                getEstornoMovProdutoVO().setParcela(getFacade().getMovParcela().consultarPorMovProduto(
                        getEstornoMovProdutoVO().getMovProdutoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS));
                getEstornoMovProdutoVO().setListaMovParcelaVinculadas(consultarMovParcelasVinculadas());

                validarSeExisteReciboMovProduto();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método usado para montar estorno de produtos de colaboradores
     */
    public void montarColaboradorEMovProdutoEstorno() throws Exception {
        try {
            ColaboradorControle colaboradorControle = (ColaboradorControle) context().getExternalContext().getSessionMap().get("ColaboradorControle");
            if (colaboradorControle != null) {
                getEstornoMovProdutoVO().setClienteVO(colaboradorControle.getClienteVO());
                if (colaboradorControle.getClienteVO().getCodigo() == 0 & (colaboradorControle.getClienteVO().getMatricula().trim().isEmpty()
                        | colaboradorControle.getClienteVO().getPessoa().getNome().trim().isEmpty())) {
                    setMostrarNomeMatriculaAluno(false);
                } else {
                    setMostrarNomeMatriculaAluno(true);
                }
                getEstornoMovProdutoVO().setParcela(getFacade().getMovParcela().consultarPorMovProduto(
                        getEstornoMovProdutoVO().getMovProdutoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS));
                getEstornoMovProdutoVO().setListaMovParcelaVinculadas(consultarMovParcelasVinculadas());

                validarSeExisteReciboMovProduto();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método usado para inicializar atributos e para montar estorno de produtos de consumidores
     */
    public void montarConsumidorEMovProdutoEstorno() throws Exception {
        try {
            VendaConsumidorControle vendaAvulsa = (VendaConsumidorControle) context().getExternalContext().getSessionMap().get("VendaConsumidorControle");
            if (vendaAvulsa != null) {
                getEstornoMovProdutoVO().setClienteVO(vendaAvulsa.getClienteVO());
                if (vendaAvulsa.getClienteVO().getMatricula().trim().isEmpty()) {
                    setMostrarNomeMatriculaAluno(false);
                } else {
                    setMostrarNomeMatriculaAluno(true);
                }

                getEstornoMovProdutoVO().setParcela(getFacade().getMovParcela().consultarPorMovProduto(
                        getEstornoMovProdutoVO().getMovProdutoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_MINIMOS));
                getEstornoMovProdutoVO().setListaMovParcelaVinculadas(consultarMovParcelasVinculadas());

                validarSeExisteReciboMovProduto();

            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void validarSeExisteReciboMovProduto() throws Exception {
        try {
            ConfiguracaoFinanceiroVO confFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            temChequeOuCartaoEmlote = Boolean.FALSE;
            ReciboPagamento reciboFacade = new ReciboPagamento();
            List listaReciboPagamento = new ArrayList();
            for (MovParcelaVO parcelaVin : estornoMovProdutoVO.getListaMovParcelaVinculadas()) {
                if (!estornoMovProdutoVO.getListaMovParcela().contains(parcelaVin)) {
                    ReciboPagamentoVO recibo = reciboFacade.consultarPorParcela(parcelaVin.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyNumber(recibo.getCodigo())) {
                        Iterator k = listaReciboPagamento.iterator();
                        EstornoReciboVO estornoRecibo = new EstornoReciboVO();
                        estornoRecibo.setReciboPagamentoVO(recibo);
                        estornoRecibo.setListaMovPagamento(getFacade().getMovPagamento().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
                        if (confFinan.getUsarMovimentacaoContas()) {
                            for (MovPagamentoVO movPag : estornoRecibo.getListaMovPagamento()) {
                                if (!movPag.getCredito()) {
                                    if (getFacade().getCheque().verificarContemLote(movPag.getCodigo())
                                            || getFacade().getCartaoCredito().verificarContemLote(movPag.getCodigo())) {
                                        temChequeOuCartaoEmlote = Boolean.TRUE;
                                        break;
                                    }
                                }
                            }
                        }
                        estornoRecibo.setListaMovParcela(getFacade().getMovParcela().consultarPorCodigoRecibo(recibo.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
                        estornoRecibo.setResponsavelEstornoRecivo(getEstornoMovProdutoVO().getResponsavelEstorno());

                        Iterator e = estornoRecibo.getListaMovParcela().iterator();
                        while (e.hasNext()) {
                            MovParcelaVO parcela = (MovParcelaVO) e.next();
                            if (parcela.getContrato().getCodigo() != 0 && !parcela.getContrato().getCodigo().equals(getEstornoMovProdutoVO().getContrato())) {
                                getEstornoMovProdutoVO().setExiteOutroContratoPagouMinhaParcela(true);
                                adicionarObjContratoVO(parcela.getContrato().getPessoa());
                                continue;
                            } else if (parcela.getVendaAvulsaVO().getCodigo() != 0 && !parcela.getVendaAvulsaVO().getCodigo().equals(getEstornoMovProdutoVO().getVendaAvulsa())) {
                                getEstornoMovProdutoVO().setExiteOutroContratoPagouMinhaParcela(true);
                                adicionarObjContratoVO(parcela.getVendaAvulsaVO().getCliente().getPessoa());
                                continue;
                            } else if (parcela.getAulaAvulsaDiariaVO().getCodigo() != 0 && !parcela.getAulaAvulsaDiariaVO().getCodigo().equals(getEstornoMovProdutoVO().getAulaAvulsaDiaria())) {
                                getEstornoMovProdutoVO().setExiteOutroContratoPagouMinhaParcela(true);
                                adicionarObjContratoVO(parcela.getAulaAvulsaDiariaVO().getCliente().getPessoa());
                                continue;
                            } else if (parcela.getPersonal().getCodigo() != 0 && !parcela.getPersonal().getCodigo().equals(getEstornoMovProdutoVO().getPersonal())) {
                                getEstornoMovProdutoVO().setExiteOutroContratoPagouMinhaParcela(true);
                                adicionarObjContratoVO(parcela.getPersonal().getPersonal().getPessoa());
                                continue;
                            } else {
                                getEstornoMovProdutoVO().getListaMovParcela().add(parcela);
                                List lista = getFacade().getMovProduto().consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                adicionarMovprodutos(lista);
                                List listaItens = getFacade().getRemessaItem().consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                getEstornoMovProdutoVO().getListaItensRemessa().addAll(listaItens);
                            }
                        }
                        //
                        getEstornoMovProdutoVO().getListaEstornoRecibo().add(estornoRecibo);
                    } else {
                        getEstornoMovProdutoVO().getListaMovParcela().add(parcelaVin);
                        List lista = getFacade().getMovProduto().consultarPorCodigoParcela(parcelaVin.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        adicionarMovprodutos(lista);
                        List listaItens = getFacade().getRemessaItem().consultarPorCodigoParcela(parcelaVin.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        getEstornoMovProdutoVO().getListaItensRemessa().addAll(listaItens);
                    }
                }
            }
            //preparar transações de cartão de crédito
            getEstornoMovProdutoVO().montarListaTransacoes(getEstornoMovProdutoVO().getListaMovParcela(), getFacade().getZWFacade().getCon());
            JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.listaTransacoes", getEstornoMovProdutoVO().getListaTransacoes());
            JSFUtilities.setManagedBeanValue("GestaoTransacoesControle.consultarParaExportar", false);
        } catch (Exception e) {
            throw e;
        }
    }

    public void adicionarObjContratoVO(PessoaVO obj) {
        int index = 0;
        Iterator i = getListaPessoaVOs().iterator();
        while (i.hasNext()) {
            PessoaVO objExistente = (PessoaVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo())) {
                getListaPessoaVOs().set(index, obj);
                return;
            }
            index++;
        }
        getListaPessoaVOs().add(obj);

    }

    public void consultarResponsavelEstorno() {
        try {
            getEstornoMovProdutoVO().setResponsavelEstorno(getFacade().getUsuario().
                    consultarPorChavePrimaria(getEstornoMovProdutoVO().getResponsavelEstorno().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getEstornoMovProdutoVO().getResponsavelEstorno().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagem("");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public String gravar(){
        return gravar(true);
    }

    /**
     * Método usado para gravar o estorno de produtos
     */
    public String gravar(boolean controlarTransacao) {
        try {
            getEstornoMovProdutoVO().setCaixa(getCaixaAberto());

            if (!UteisValidacao.emptyList(getEstornoMovProdutoVO().getListaTransacoes()) && UteisValidacao.emptyString(getEstornarTransacoes())) {
                throw new Exception("Deve ser selecionado se deseja estornar as transações");
            }
            getEstornoMovProdutoVO().setEstornarOperadora(getEstornarTransacoes().equalsIgnoreCase("SIM"));


            for (RemessaItemVO remessaItemVO : getEstornoMovProdutoVO().getListaItensRemessa()) {
                if (remessaItemVO.getCodigoStatus().equals("00")) {
                    throw new Exception("Não é possível realizar o estorno, pois existem parcelas pagas por uma remessa.");
                }
                if (remessaItemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.GERADA) ||
                        remessaItemVO.getRemessa().getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA)) {
                    throw new Exception("Não é possível realizar o estorno, pois existe(m) remessa(s) aguardando retorno");
                }
            }
            //verifica se o estorno é referente a produtos de consumidores e exclui venda avulsa do mesmo, caso encontre.
            if (getEstornoMovProdutoVO().getMovProdutoVO().getCodigo() > 0) {
                if (getEstornoMovProdutoVO().getExiteOutroContratoPagouMinhaParcela()) {
                    getFacade().getMovProduto().estornarMovProduto(getEstornoMovProdutoVO(), getEstornoMovProdutoVO().getParcela(), getKey(), controlarTransacao);
                    recarregarInformacoes(getEstornoMovProdutoVO().getMovProdutoVO());
                }
                getFacade().getMovProduto().estornarMovProduto(getEstornoMovProdutoVO(), getEstornoMovProdutoVO().getParcela(), getKey(), controlarTransacao);
                gravarEstornoPonto();
                setAbrirRichConfirmacaoEstorno(false);
                setMensagemID("msg_dados_gravados");
                setMensagem("");
                setMensagemDetalhada("");
                setSucesso(true);
                setErro(false);
                setApresentarBotaoEstorno(false);
            }

        } catch (ConsistirException ex) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setErro(true);
            setAbrirRichConfirmacaoEstorno(true);
            setMensagemDetalhada("msg_erro", "Para estornar este produto, é necessário primeiro estornar o recibo do mesmo.");
        } catch (Exception e) {
            setApresentarBotaoEstorno(true);
            setSucesso(false);
            setErro(true);
            setAbrirRichConfirmacaoEstorno(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public void gravarEstornoPonto() throws Exception {
        if (getEmpresaLogado().isTrabalharComPontuacao()) {
            for (MovProdutoVO movProdutoVO : getEstornoMovProdutoVO().getListaMovProduto()) {
                ProdutoVO produto = getFacade().getProduto().consultarPorChavePrimaria(movProdutoVO.getProduto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Integer codigoVenda = 0;
                if (getEstornoMovProdutoVO().getVendaAvulsa() > 0) {
                    codigoVenda = getEstornoMovProdutoVO().getVendaAvulsa();
                } else if (getEstornoMovProdutoVO().getAulaAvulsaDiaria() > 0) {
                    codigoVenda = getEstornoMovProdutoVO().getAulaAvulsaDiaria();
                }

                HistoricoPontosVO historicoPontosPesquisado = getFacade().
                        getHistoricoPontos().obterHistoricoPorProduto(getEstornoMovProdutoVO().getClienteVO().getCodigo(), produto.getCodigo(), codigoVenda, Uteis.NIVELMONTARDADOS_TODOS);
                if (historicoPontosPesquisado.getPontos() > 0) {
                    HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
                    historicoPontos.setCliente(getEstornoMovProdutoVO().getClienteVO());
                    if (produto.getTipoProduto().equals("DI")) {
                        historicoPontos.setDescricao("Estorno (Diária) - " + produto.getDescricao());
                    } else {
                        historicoPontos.setDescricao("Estorno (Produto) - " + produto.getDescricao());
                    }
                    historicoPontos.setEntrada(false);
                    historicoPontos.setDataConfirmacao(Calendario.hoje());
                    historicoPontos.setDataaula(Calendario.hoje());
                    Integer valor = (historicoPontosPesquisado.getPontos() * -1);
                    historicoPontos.setPontos(valor);
                    historicoPontos.setTipoPonto(TipoItemCampanhaEnum.ESTORNO_PRODUTO);
                    historicoPontos.setProduto(produto.getCodigo());
                    getFacade().getHistoricoPontos().incluir(historicoPontos);
                }
            }
        }
    }

    public void validarQualModalAbrir() {
        try {
            if (!UteisValidacao.emptyList(getEstornoMovProdutoVO().getListaTransacoes()) && UteisValidacao.emptyString(getEstornarTransacoes())) {
                throw new Exception("Deve ser selecionado se deseja estornar as transações");
            }
            getEstornoMovProdutoVO().setEstornarOperadora(getEstornarTransacoes().equalsIgnoreCase("SIM"));

            if (getEstornoMovProdutoVO().getExiteOutroContratoPagouMinhaParcela()) {
                setMsgAlert("Richfaces.showModalPanel('panelMensagemOutroProduto')");
            } else {
                abrirModalConfirmacaoEstorno();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public boolean getDesenharColunaNomeContrato() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoMovProdutoVO().getApresentarNomePorContrato(obj);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomeVendaAvulsa() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoMovProdutoVO().getApresentarNomePorVendaAvulsa(obj);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomeAulaAvusa() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoMovProdutoVO().getApresentarNomePorAulaAvulsaDiaria(obj);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public boolean getDesenharColunaNomePersonal() {
        try {
            MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("historicoParcela");
            return obj != null && getEstornoMovProdutoVO().getApresentarNomePorTaxaPersonal(obj);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return false;
        }
    }

    public String getFecharRichModalPanelConfirmacao() {
        if (getAbrirRichConfirmacaoEstorno()) {
            return "Richfaces.showModalPanel('panelConfirmacaoEstornar')";
        } else {
            return "Richfaces.hideModalPanel('panelConfirmacaoEstornar')";
        }
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos
     * de persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }

    }

    /**
     * @return the abrirRichConfirmacaoEstorno
     */
    public Boolean getAbrirRichConfirmacaoEstorno() {
        return abrirRichConfirmacaoEstorno;
    }

    /**
     * @param abrirRichConfirmacaoEstorno the abrirRichConfirmacaoEstorno to set
     */
    public void setAbrirRichConfirmacaoEstorno(Boolean abrirRichConfirmacaoEstorno) {
        this.abrirRichConfirmacaoEstorno = abrirRichConfirmacaoEstorno;
    }

    /**
     * @return the apresentarBotaoEstorno
     */
    public Boolean getApresentarBotaoEstorno() {
        return apresentarBotaoEstorno;
    }

    /**
     * @param apresentarBotaoEstorno the apresentarBotaoEstorno to set
     */
    public void setApresentarBotaoEstorno(Boolean apresentarBotaoEstorno) {
        this.apresentarBotaoEstorno = apresentarBotaoEstorno;
    }

    /**
     * @return the estornoMovProdutoVO
     */
    public EstornoMovProdutoVO getEstornoMovProdutoVO() {
        return estornoMovProdutoVO;
    }

    /**
     * @param estornoMovProdutoVO the estornoMovProdutoVO to set
     */
    public void setEstornoMovProdutoVO(EstornoMovProdutoVO estornoMovProdutoVO) {
        this.estornoMovProdutoVO = estornoMovProdutoVO;
    }

    /**
     * @return the listaPessoaVOs
     */
    public List getListaPessoaVOs() {
        return listaPessoaVOs;
    }

    /**
     * @param listaPessoaVOs the listaPessoaVOs to set
     */
    public void setListaPessoaVOs(List listaPessoaVOs) {
        this.listaPessoaVOs = listaPessoaVOs;
    }

    public Boolean getApresentarListaPagamento() {
        return getEstornoMovProdutoVO().getListaEstornoRecibo().size() != 0;

    }

    /**
     * @return the naoMostrarNomeMatriculaAluno
     */
    public Boolean getMostrarNomeMatriculaAluno() {
        return mostrarNomeMatriculaAluno;
    }

    /**
     * @param mostrarNomeMatriculaAluno the mostrarNomeMatriculaAluno to set
     */
    public void setMostrarNomeMatriculaAluno(Boolean mostrarNomeMatriculaAluno) {
        this.mostrarNomeMatriculaAluno = mostrarNomeMatriculaAluno;
    }

    public CaixaVO getCaixaAberto() {
        return caixaAberto;
    }

    public void setCaixaAberto(CaixaVO caixaAberto) {
        this.caixaAberto = caixaAberto;
    }

    public void validarCaixaAbrirModal() throws Exception {
        setMsgAlert("");
        boolean podeNecessitarAbrirCaixa = false;
        for (EstornoReciboVO estorno : getEstornoMovProdutoVO().getListaEstornoRecibo()) {
            for (MovPagamentoVO mp : estorno.getListaMovPagamento()) {
                if (!UteisValidacao.emptyNumber(mp.getMovconta())) {
                    podeNecessitarAbrirCaixa = true;
                }
            }
        }
        if (podeNecessitarAbrirCaixa) {
            ConfiguracaoFinanceiroVO cFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            if (cFinan.getUsarMovimentacaoContas()) {
                CaixaControle caixaControle = (CaixaControle) JSFUtilities.getFromSession(CaixaControle.class.getSimpleName());
                setCaixaAberto(caixaControle.getCaixaVoEmAberto());
                if (getCaixaAberto() == null || UteisValidacao.emptyNumber(getCaixaAberto().getCodigo())) {
                    caixaControle.abrirCaixa();
                    if (!caixaControle.getMensagemDetalhada().equals("Não existem contas ativas para abrir caixa")) {
                        setMsgAlert("if(!confirm('Você precisa ter um caixa aberto no financeiro. Deseja abrir?')){return false;};Richfaces.showModalPanel('modalAbrirCaixa')");
                        return;
                    }
                }
            }
        }
//        setMsgAlert(getAbrirRichModal());
    }

    public void abrirModalConfirmacaoEstorno() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                UsuarioVO responsavelEstorno = auto.getUsuario();
                getEstornoMovProdutoVO().setResponsavelEstorno(responsavelEstorno);
                gravar();
                if (getMensagemID().equals("msg_erro")) {
                    setExecutarAoCompletar("Richfaces.hideModalPanel('panelMensagemOutroProduto');fireElementFromParent('form:btnAtualizaCliente');" + getMensagemNotificar());
                } else {
                    setExecutarAoCompletar("Richfaces.hideModalPanel('panelMensagemOutroProduto');try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {console.error(e)}; executePostMessage({reload: true});" + getMensagemNotificar());
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                limparMsg();
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }

        };

        limparMsg();
        try {
            validarCaixaAbrirModal();
            auto.autorizar("Confirmação do Estorno do Produto", "EstornoMovProduto",
                    "Você precisa da permissão \"4.14 - Estorno de Movimentação Produto\"",
                    "form, panelMensagem", listener);


        } catch (Exception e) {
            montarErro(e);
        }
    }

    private List<MovParcelaVO> consultarMovParcelasVinculadas() throws Exception {
        if (!UteisValidacao.emptyNumber(getEstornoMovProdutoVO().getContrato())) {
            return getFacade().getMovParcela().consultarPorContrato(getEstornoMovProdutoVO().getContrato(), Uteis.NIVELMONTARDADOS_TODOS);
        } else if (!UteisValidacao.emptyNumber(getEstornoMovProdutoVO().getVendaAvulsa())) {
            return getFacade().getMovParcela().consultarPorCodigoVendaAvulsaLista(getEstornoMovProdutoVO().getVendaAvulsa(), null, null, "", false, Uteis.NIVELMONTARDADOS_TODOS);
        } else if (!UteisValidacao.emptyNumber(getEstornoMovProdutoVO().getAulaAvulsaDiaria())) {
            return getFacade().getMovParcela().consultarPorCodigoAulaAvulsaDiariaLista(getEstornoMovProdutoVO().getAulaAvulsaDiaria(), "", false, null, null, Uteis.NIVELMONTARDADOS_TODOS);
        } else if (!UteisValidacao.emptyNumber(getEstornoMovProdutoVO().getPersonal())) {
            return getFacade().getMovParcela().consultarPorCodigoPersonalLista(getEstornoMovProdutoVO().getPersonal(), "", false, null, null, Uteis.NIVELMONTARDADOS_TODOS);
        }
        List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();
        lista.add(estornoMovProdutoVO.getParcela());
        return lista;
    }

    private void adicionarMovprodutos(List<MovProdutoVO> lista) {
        for (MovProdutoVO obj : lista) {
            int index = 0;
            boolean adicionado = false;
            Iterator i = getEstornoMovProdutoVO().getListaMovProduto().iterator();
            while (i.hasNext()) {
                MovProdutoVO objExistente = (MovProdutoVO) i.next();
                if (objExistente.getCodigo().equals(obj.getCodigo())) {
                    getEstornoMovProdutoVO().getListaMovProduto().set(index, obj);
                    adicionado = true;
                    break;
                }
                index++;
            }
            if (!adicionado) {
                getEstornoMovProdutoVO().getListaMovProduto().add(obj);
            }
        }

    }

    public String excluirMovProduto() throws Exception {
        try {
            MovProdutoVO obj = (MovProdutoVO) context().getExternalContext().getRequestMap().get("historicoCompras");
            getFacade().getMovProduto().excluir(obj);
            montarSucesso("msg_dados_excluidos");

            ColaboradorControle colaboradorControle = (ColaboradorControle) context().getExternalContext().getSessionMap().get("ColaboradorControle");
            if (colaboradorControle != null) {
                colaboradorControle.getClienteVO().getListaHistoricoProduto().remove(obj);
            }
        } catch (Exception e) {
            montarErroComLog(e);
        }
        
        return "";
    }

    public Boolean getTemChequeOuCartaoEmlote() {
        return temChequeOuCartaoEmlote;
    }

    public void setTemChequeOuCartaoEmlote(Boolean temChequeOuCartaoEmlote) {
        this.temChequeOuCartaoEmlote = temChequeOuCartaoEmlote;
    }

    public String getEstornarTransacoes() {
        return estornarTransacoes;
    }

    public void setEstornarTransacoes(String estornarTransacoes) {
        this.estornarTransacoes = estornarTransacoes;
    }

    public List<SelectItem> getListaSelectItemEstornarTransacoes() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem("", ""));
        lista.add(new SelectItem("SIM", "SIM"));
        lista.add(new SelectItem("NAO", "NÃO"));
        return lista;
    }
}

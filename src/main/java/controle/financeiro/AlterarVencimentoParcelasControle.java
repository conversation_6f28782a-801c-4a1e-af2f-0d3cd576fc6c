/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.crm.HistoricoContatoControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.event.ActionEvent;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AlterarVencimentoParcelasControle extends SuperControle {

    private boolean exibirModal = false;
    private boolean gestaoRemessas = false;
    private List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> listaParcelasOriginais = new ArrayList<MovParcelaVO>();
    private int diaVencimento = 0;
    private ContratoVO contrato = null;
    private ContratoRecorrenciaVO contratoRecorrencia = null;
    private ClienteVO cliente = null;
    private Date dataTodos;

    public int getDiaVencimento() {
        return diaVencimento;
    }

    public void setDiaVencimento(int diaVencimento) {
        this.diaVencimento = diaVencimento;
    }

    public boolean isExibirModal() {
        return exibirModal;
    }

    public void setExibirModal(boolean exibirModal) {
        this.exibirModal = exibirModal;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public List<MovParcelaVO> getListaParcelasOriginais() {
        return listaParcelasOriginais;
    }

    public void setListaParcelasOriginais(List<MovParcelaVO> listaParcelasOriginais) {
        this.listaParcelasOriginais = listaParcelasOriginais;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public void validarPermissaoAlterarDataVencimento() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "AlterarDataVencimentoParcela", "2.41 - Permissão para alterar a data de vencimento de parcelas");
            }
        }
    }

    public void alterarParcelaEspecifica() {
        MovParcelaVO parcela = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parc");
        alterarParcelas(false, parcela);
    }

    public void alterarParcelas() {
        alterarParcelas(true, null);
    }

    public void alterarParcelas(Boolean emMassa, MovParcelaVO parcela) {
        try {
            dataTodos = Calendario.hoje();
            setGestaoRemessas(true);
            contratoRecorrencia = null;
            cliente = null;
            validarPermissaoAlterarDataVencimento();
            listaParcelas = new ArrayList<MovParcelaVO>();
            setMsgAlert("");
            limparMsg();
            if (emMassa) {
                GestaoRemessasControle gestaoRem = (GestaoRemessasControle) getControlador(GestaoRemessasControle.class.getSimpleName());
                for (MovParcelaVO movParcelaVO : gestaoRem.getAgrupamentoParcelas().getParcelasReenvio()) {
                    if (movParcelaVO.getParcelaEscolhida() && !getFacade().getRemessaItem().existeParcelaEmRemessaGeradaouAguardando(movParcelaVO.getCodigo())) {
                        listaParcelas.add((MovParcelaVO) movParcelaVO.getClone(true));
                    }
                }
            } else {
                cliente = getFacade().getCliente().consultarPorCodigoPessoa(parcela.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                diaVencimento = Uteis.getDiaMesData(parcela.getDataVencimento());
                if(!getFacade().getRemessaItem().existeParcelaEmRemessaGeradaouAguardando(parcela.getCodigo())){
                    listaParcelas.add(parcela);
                }
            }
            listaParcelasOriginais = new ArrayList<MovParcelaVO>();
            for (MovParcelaVO movParcelaVO : listaParcelas) {
                listaParcelasOriginais.add((MovParcelaVO) movParcelaVO.getClone(true));
            }
            Ordenacao.ordenarLista(listaParcelas, "dataVencimento");
            if (listaParcelas.isEmpty()) {
                setMensagemDetalhada("Não há parcelas para serem exibidas.");
            }
            setMsgAlert("Richfaces.showModalPanel('modalAlterarVencimento');");
        } catch (Exception e) {
            Logger.getLogger(AlterarVencimentoParcelasControle.class.getName()).log(Level.SEVERE, null, e);
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }

    }

    public void preparar(ActionEvent evt) throws Exception {
        notificarRecursoEmpresa(RecursoSistema.IMPRESSAO_CONTRATO_CLIENTE);
        setGestaoRemessas(false);
        setMensagemID("");
        setMensagemDetalhada("", "");
        try {
            Boolean ehRecorrencia = (Boolean) evt.getComponent().getAttributes().get("ehRecorrencia");
            contrato = (ContratoVO) evt.getComponent().getAttributes().get("contrato");
            contratoRecorrencia = (ContratoRecorrenciaVO) evt.getComponent().getAttributes().get("contratoRecorrencia");
            cliente = (ClienteVO) evt.getComponent().getAttributes().get("cliente");
            
            if (contrato != null &&  !UteisValidacao.emptyNumber(contrato.getCodigo())){
                 contrato = getFacade().getContrato().consultarPorCodigo(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (contrato != null && cliente != null && contratoRecorrencia == null && (ehRecorrencia != null && ehRecorrencia)) {
                contratoRecorrencia = getFacade().getContratoRecorrencia().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                cliente = null;
            }

            if ((contrato != null && contrato.getCodigo() != 0 && contratoRecorrencia != null && contratoRecorrencia.getCodigo() != 0)
                    || (cliente != null && cliente.getCodigo() != 0)) {

                validarPermissaoAlterarDataVencimento();

                if (cliente == null) {
                    diaVencimento = contratoRecorrencia.getDiaVencimentoCartao();
                }

                listaParcelas = new ArrayList<MovParcelaVO>();
                this.exibirModal = true;

                List<MovParcelaVO> listaTemp;
                if (contrato != null) {
                    listaTemp = getFacade().getZWFacade().getMovParcela().
                            consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    listaTemp = getFacade().getZWFacade().getMovParcela().
                            consultarPorCodigoPessoa(cliente.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                }
                for (MovParcelaVO movParcelaVO : listaTemp) {
                    if(!getFacade().getRemessaItem().existeParcelaEmRemessaGeradaouAguardando(movParcelaVO.getCodigo())){
                        if (movParcelaVO.getSituacao().equals("EA")) {
                            if (cliente != null && !movParcelaVO.getRegimeRecorrencia()) {
                                listaParcelas.add(movParcelaVO);
                            }

                            if (cliente == null && movParcelaVO.getRegimeRecorrencia()) {
                                listaParcelas.add(movParcelaVO);
                            }
                        }
                    }
                }
                listaParcelasOriginais = new ArrayList<MovParcelaVO>();
                for (MovParcelaVO movParcelaVO : listaParcelas) {
                    listaParcelasOriginais.add((MovParcelaVO) movParcelaVO.getClone(true));
                }

                Ordenacao.ordenarLista(listaParcelas, "codigo");

                if (listaParcelas.isEmpty()) {
                    setMensagemDetalhada("Não há parcelas que não sejam de recorrência para serem alteradas!");
                }

            }
        } catch (Exception ex) {
            Logger.getLogger(AlterarVencimentoParcelasControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex);
        }
        JSFUtilities.setManagedBeanValue(this.getClass().getSimpleName() + ".exibirModal", exibirModal);
    }

    public void fecharPanelDadosParametros() {
        this.exibirModal = false;
    }

    private void validarDiaVencimento() throws Exception {
        if (diaVencimento <= 0 || diaVencimento >= 31) {
            throw new Exception("O dia para vencimento deve estar entre 1 e 30.");
        }
    }

    public void aplicarDataTodos() throws Exception {
        for(MovParcelaVO parcela : listaParcelas){
            if(gestaoRemessas){
                parcela.setDataCobranca(dataTodos);
            }else{
                parcela.setDataVencimento(dataTodos);
            }
            
        }
    }
    public void aplicarNovaDataVencimento() throws Exception {
        ArrayList<MovParcelaVO> temp = new ArrayList<MovParcelaVO>(listaParcelasOriginais);
        List<MovParcelaVO> listaMutavel = new ArrayList<MovParcelaVO>();

        for (MovParcelaVO mp : temp) {
            MovParcelaVO nova = (MovParcelaVO) mp.getClone(true);
            listaMutavel.add(nova);
        }

        setMensagemDetalhada("");
        try {
            validarDiaVencimento();
            contratoRecorrencia.setDiaVencimentoCartao(getDiaVencimento());
            for (MovParcelaVO movParcelaVO : listaMutavel) {
                Date dataAnterior = movParcelaVO.getDataVencimento();
                Date novaData = Calendario.getInstance(
                        Uteis.getAnoData(dataAnterior),
                        Uteis.getMesData(dataAnterior),
                        diaVencimento).getTime();
                movParcelaVO.setDataVencimento(novaData);
            }

            listaParcelas = new ArrayList<MovParcelaVO>(listaMutavel);


        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }

    }

    public void gerarDatas() {
        try {
            limparMsg();

            Iterator a = getListaParcelas().iterator();
            MovParcelaVO novaParcela = (MovParcelaVO) a.next();
            Date novaData = novaParcela.getDataVencimento();
            while (a.hasNext()) {
                novaParcela = (MovParcelaVO) a.next();
                novaData = Uteis.obterDataFutura3(novaData, 1);
                novaParcela.setDataVencimento(novaData);
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gravar() throws Exception {
        limparMsg();
        setMensagemDetalhada("", "");
        Connection con = null;
        try {
            con = getFacade().getZWFacade().getCon();
            con.setAutoCommit(false);

            if (!isGestaoRemessas()) {
                for (MovParcelaVO movParcelaVO : listaParcelas) {
                    if (!movParcelaVO.getSituacao().equalsIgnoreCase("EA")) {
                        throw new Exception("Para alterar o vencimento das parcelas todas precisam estar em aberto.");
                    }
                    if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                        throw new Exception("A data de vencimento da(s) parcela(s) não poder ser menor que "+Uteis.getData(Calendario.hoje())+".");
                    }

                    MovParcelaVO parcelaVOBancoDados = getFacade().getMovParcela().consultarPorChavePrimaria(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (parcelaVOBancoDados.getDataVencimento().equals(movParcelaVO.getDataVencimento())) {
                        //Como não ouve alteração na data de vencimento, não é necessário validar se existe boleto aguardando pagamento
                        continue;
                    }

                    if (existeBoletoAguardandoPagamento(movParcelaVO)) {
                        throw new Exception("A(s) parcela(s) não podem ser alteradas por ter boletos aguardando pagamento. Cancele os boletos antes de editar o vencimento das parcelas.");
                    }
                }
            }

            if (cliente == null && contratoRecorrencia != null) {
                validarDiaVencimento();
                getFacade().getZWFacade().getContratoRecorrencia().alterar(contratoRecorrencia);
            }

            getFacade().getZWFacade().getMovParcela().alterarVencimentoListaParcelas(listaParcelas, listaParcelasOriginais, gestaoRemessas,
                    getUsuarioLogado().getNome(), "AlterarVencimentoParcelasControle", true, true);
            con.commit();
            
            setMensagem("Alteração realizada com sucesso!");
            if(gestaoRemessas){
                GestaoRemessasControle gestaoRemessas = (GestaoRemessasControle)getControlador(GestaoRemessasControle.class.getSimpleName());
                for(MovParcelaVO parcela : listaParcelas){
                    int indexOf = gestaoRemessas.getAgrupamentoParcelas().getParcelasReenvio().indexOf(parcela);
                    if(indexOf >= 0){
                        gestaoRemessas.getAgrupamentoParcelas().getParcelasReenvio().get(indexOf).setDataCobranca(parcela.getDataCobranca());
                    }
                }
                setGestaoRemessas(false);
            }
            listaParcelas.clear();
            montarSucessoGrowl("Alteração realizada com sucesso!");
        } catch (Exception ex) {
            listaParcelas = listaParcelasOriginais;
            con.rollback();
            setMensagemDetalhada(ex.getMessage());
            Logger.getLogger(AlterarVencimentoParcelasControle.class.getName()).log(Level.SEVERE, null, ex);
            montarErro(ex);
        } finally {
            con.setAutoCommit(true);
        }
    }

    private boolean existeBoletoAguardandoPagamento(MovParcelaVO movParcelaVO) {
        boolean existeBoletoAguardandoPagamento = false;
        try {
            existeBoletoAguardandoPagamento = getFacade().getBoleto().existeBoletoPendentePorMovParcela(movParcelaVO.getCodigo(), true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return existeBoletoAguardandoPagamento;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public boolean isGestaoRemessas() {
        return gestaoRemessas;
    }

    public void setGestaoRemessas(boolean gestaoRemessas) {
        this.gestaoRemessas = gestaoRemessas;
    }

    public Date getDataTodos() {
        return dataTodos;
    }

    public void setDataTodos(Date dataTodos) {
        this.dataTodos = dataTodos;
    }

    public void abrirContatoCliente(){
        try{
            HistoricoContatoControle histContatoControle = (HistoricoContatoControle) getControlador(HistoricoContatoControle.class.getSimpleName());
            histContatoControle.novo();
            ClienteVO cli = getFacade().getCliente().consultarPorChavePrimaria(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            histContatoControle.inicializarCliente(Calendario.hoje(), cli);
            setMsgAlert("abrirPopup('realizarContatoForm.jsp', 'RealizarContatoform', 664, 635);");
        }catch(Exception e){
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("");
        }
    }

    public void gravarComPermissao() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                gravar();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemDetalhada("msg_erro", e.getMessage());
                montarMsgAlert(getMensagemDetalhada());
            }
            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        setMsgAlert("");
        auto.autorizar("2.41 - Permissão para alterar a data de vencimento de parcelas", "AlterarDataVencimentoParcela",
                "Você precisa da permissão \"2.41 - Permissão para alterar a data de vencimento de parcelas\"",
                "formAlterar:panelConteudo,form:panelConteudo", listener);
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

}

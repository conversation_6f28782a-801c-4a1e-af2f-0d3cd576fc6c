/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BloqueioCaixaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BloqueioCaixaControle extends SuperControleRelatorio {

    private List<BloqueioCaixaVO> bloqueios;
    private BloqueioCaixaVO bloqueio;
    private BloqueioCaixaVO bloqueioAtual;
    private Integer empresaSelecionada;
    private boolean historico = false;
    private boolean mostrarCampoEmpresa = false;
    private List<SelectItem> empresas;

    public BloqueioCaixaControle() {
        iniciar();
    }

    public void abrirHistorico() {
        try {
            bloqueios = getFacade().getFinanceiro().getBloqueioCaixa().consultar(empresaSelecionada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            historico = true;
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void sairHistorico() {
        historico = false;
    }

    public void iniciar() {
        try {
            if (getEmpresaLogado() != null) {
                empresaSelecionada = getEmpresaLogado().getCodigo();
                bloqueioAtual = getFacade().getFinanceiro().getBloqueioCaixa().consultarBloqueioAtual(empresaSelecionada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            inicializarEmpresa();
        } catch (Exception e) {
            montarErro(e.getMessage());
        }
    }

    public void mudarEmpresa() {
        try {
            bloqueioAtual = getFacade().getFinanceiro().getBloqueioCaixa().consultarBloqueioAtual(empresaSelecionada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (historico) {
                bloqueios = getFacade().getFinanceiro().getBloqueioCaixa().consultar(empresaSelecionada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }
        
    
    public final void inicializarEmpresa() throws Exception {
        mostrarCampoEmpresa = validarPermissaoEmpresas();
        empresas = new ArrayList<SelectItem>();
        if(mostrarCampoEmpresa){
            List<EmpresaVO> emps = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            mostrarCampoEmpresa = emps.size() > 1;
            for(EmpresaVO e : emps){
                empresas.add(new SelectItem(e.getCodigo(), e.getNome()));
            }
        }
    }

    public void limpar() {
        bloqueio = null;
    }

    public void novo() {
        try {
            bloqueio = new BloqueioCaixaVO();
            bloqueio.setUsuarioResponsavel(getUsuarioLogado());
            if (!mostrarCampoEmpresa && getEmpresaLogado() != null) {
                bloqueio.setEmpresa(getEmpresaLogado());
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void gravar() {
        try {
            bloqueio.setUsuarioResponsavel(getUsuarioLogado());
            bloqueio.setLancamento(Calendario.hoje());
            bloqueio.setEmpresa(new EmpresaVO());
            bloqueio.getEmpresa().setCodigo(empresaSelecionada);
            bloqueio.setUserOamd(getUsuarioLogado().getUserOamd());
            getFacade().getFinanceiro().getBloqueioCaixa().incluir(bloqueio);
            montarSucessoDadosGravados();
            setMsgAlert(getMensagemNotificar());
            bloqueioAtual = getFacade().getFinanceiro().getBloqueioCaixa().consultarBloqueioAtual(empresaSelecionada, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            bloqueio = null;
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public BloqueioCaixaVO getBloqueio() {
        return bloqueio;
    }

    public void setBloqueio(BloqueioCaixaVO bloqueio) {
        this.bloqueio = bloqueio;
    }

    public Integer getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(Integer empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public BloqueioCaixaVO getBloqueioAtual() {
        return bloqueioAtual;
    }

    public void setBloqueioAtual(BloqueioCaixaVO bloqueioAtual) {
        this.bloqueioAtual = bloqueioAtual;
    }

    public boolean isHistorico() {
        return historico;
    }

    public void setHistorico(boolean historico) {
        this.historico = historico;
    }

    public List<BloqueioCaixaVO> getBloqueios() {
        return bloqueios;
    }

    public void setBloqueios(List<BloqueioCaixaVO> bloqueios) {
        this.bloqueios = bloqueios;
    }

    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    public List<SelectItem> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<SelectItem> empresas) {
        this.empresas = empresas;
    }

    
    
}

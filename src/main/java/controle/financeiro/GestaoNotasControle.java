package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoGeracaoNotaFiscal;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoNFSeEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.financeiro.GestaoNotas;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.impl.nfe.LayoutRPSLoteMaraba;
import servicos.impl.nfe.LayoutRPSLoteSaoPaulo;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

public class GestaoNotasControle extends SuperControle {

    private List<ItemGestaoNotasTO> listaItens = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> listaItensApresentar = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> listaItensApresentarGeral;
    private List<ItemGestaoNotasTO> totalizadores = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> totalFormaPg = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> totalTipoFormaPg = new ArrayList<ItemGestaoNotasTO>();
    private Date dataInicio = new Date();
    private Date dataFim = new Date();
    private Double valorParaSelecionar = 0.0;
    private List<SelectItem> selectItemsFormaDePagamento = new ArrayList<SelectItem>();
    private Integer formaPagamentoSelecionado = 0;
    private Integer codpessoa;
    private Boolean formasInativas = Boolean.FALSE;

    private EmpresaVO empresaVO = null;

    private ItemGestaoNotasTO selecionado = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO emitido = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO periodo = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO emitir = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO emitidoGeral = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO emitidoDeOutraForma = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO aguardandoEnvio = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO notasExcluidas = new ItemGestaoNotasTO();

    private String nomeClasse = "GestaoNotas";
    private int scrollerPage;
    private String onComplete;
    private Date dataEmissao;
    private Integer formaPagamentoSelecionadoFamilia = 0;
    private HashMap<String, ItemGestaoNotaFamiliaTO> notasTitular = new HashMap<String, ItemGestaoNotaFamiliaTO>();
    private List<ItemGestaoNotaFamiliaTO> listaFamilia;
    private TipoRelatorioDF tipoRelatorioDF;
    private String numeroNotaManual;
    private String nomeArquivoRelatorioNotaManual;
    private boolean apresentarBtnExcluirNotas = false;
    private String msgExcluirNotas;
    private String tituloRelatorio;
    private List<SituacaoNFSeEnum> situacaoNFSe;
    private boolean marcarTodos = false;
    private Date dataConsultaTotalizadorNFSe;
    private List<ItemGestaoNotasTO> totalizadoresModuloNFSe;
    private int totalizadorNotasEnviadas = 0;
    private int totalizadorNotasAEnviar = 0;
    private boolean loadEnviandoNotas = false;
    private String msgEnviandoNotas = "Aguarde, estamos iniciando o envio de notas...";
    private List<ItemGestaoNotasTO> listaItensDiferencaValores = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> listaItensExcluidos = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> listaItensOutrosMeses = new ArrayList<ItemGestaoNotasTO>();
    private String tipoVisualizacao;
    private List<PlanoVO> planos;
    private String qtdPlanosSelecionados;
    private boolean selecionarTodosPlanos = false;
    private ConfiguracaoNotaFiscalVO configNotaFiscalFamilia;
    private List<SelectItem> listaConfiguracaoNotaFamilia;
    List<ItemGestaoNotasTO> lista;
    private boolean marcarTodosExcluir = false;
    private boolean existeNotaDelphi = false;

    public GestaoNotasControle() throws Exception {
        carregarListSelectItemsFormaDePagamento();
        montarListaEmpresas();
        prepararTela();
    }

    public void prepararTela() {
        try {
            setTotalizadorNotasEnviadas(20);
            setDataConsultaTotalizadorNFSe(Calendario.hoje());
            situacaoNFSe = JSFUtilities.getListFromEnum(SituacaoNFSeEnum.class);
            setCodPessoa(null);
            setListaItens(new ArrayList<ItemGestaoNotasTO>());
            setListaItensApresentar(new ArrayList<ItemGestaoNotasTO>());
            this.listaFamilia = new ArrayList<ItemGestaoNotaFamiliaTO>();
            setEmpresaVO(getEmpresaLogado());
            if (!UtilReflection.objetoMaiorQueZero(this.empresaVO, "getCodigo()")) {
                setEmpresaVO(new EmpresaVO());
            } else {
                tipoRelatorioDF = TipoRelatorioDF.getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe());
            }
            totalizar();
            carregarListaPlanos();
            montarListaConfiguracaoNotaFamilia();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String getMsgEnviandoNotas() {
        return msgEnviandoNotas;
    }

    public void setMsgEnviandoNotas(String msgEnviandoNotas) {
        this.msgEnviandoNotas = msgEnviandoNotas;
    }

    public int getTotalizadorNotasAEnviar() {
        return totalizadorNotasAEnviar;
    }

    public void setTotalizadorNotasAEnviar(int totalizadorNotasAEnviar) {
        this.totalizadorNotasAEnviar = totalizadorNotasAEnviar;
    }

    public int getTotalizadorNotasEnviadas() {
        return totalizadorNotasEnviadas;
    }

    public void setTotalizadorNotasEnviadas(int totalizadorNotasEnviadas) {
        this.totalizadorNotasEnviadas = totalizadorNotasEnviadas;
    }

    public boolean isLoadEnviandoNotas() {
        return loadEnviandoNotas;
    }

    public void setLoadEnviandoNotas(boolean loadEnviandoNotas) {
        this.loadEnviandoNotas = loadEnviandoNotas;
    }

    public boolean isEmissaoPorCompetencia() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA.getCodigo();
        }
        return false;
    }

    public boolean isEmissaoPorCompetenciaIndepenteQuitacao() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO.getCodigo();
        }
        return false;
    }

    public boolean isEmissaoPorFaturamento() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO.getCodigo();
        }
        return false;
    }

    public boolean isEmissaoPorReceita() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.RECEITA.getCodigo();
        }
        return false;
    }

    public boolean isEmissaoPorFaturamentoRecebido() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo();
        }
        return false;
    }

    public boolean isEmissaoPorCompetenciaIndependenteQuitacao() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().getTipoGestaoNFSe() == TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO.getCodigo();
        }
        return false;
    }

    private void processarNotasValoresDiferentes() {
        setListaItensDiferencaValores(new ArrayList<ItemGestaoNotasTO>());
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!UteisValidacao.emptyNumber(item.getValorEmitido()) && !item.getValor().equals(item.getValorEmitido())) {
                getListaItensDiferencaValores().add(item);
            }
        }
    }

    private void inicializarTotalizadorEmitir() {
        setEmitir(new ItemGestaoNotasTO());
        getEmitir().setNome("Disponível");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!item.getNfseemitida()) {
                getEmitir().setQuantidade(getEmitir().getQuantidade() + 1);
                getEmitir().setValor(getEmitir().getValor() + item.getValor());
            }
        }
    }

    private void inicializarTotalizadorAguardandoEnvio() {
        setAguardandoEnvio(new ItemGestaoNotasTO());
        getAguardandoEnvio().setNome("Aguardando Envio");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.getNfseemitida() && item.getSituacaoNotaFiscal() != null && item.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA)) {
                getAguardandoEnvio().setQuantidade(getAguardandoEnvio().getQuantidade() + 1);
                getAguardandoEnvio().setValor(getAguardandoEnvio().getValor() + item.getValor());
            }
        }
    }

    private void inicializarTotalizadorPeriodo() {
        setPeriodo(new ItemGestaoNotasTO());
        getPeriodo().setNome("Total do Período");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!UteisValidacao.emptyNumber(item.getRps())) {
                existeNotaDelphi = true;
            }
            getPeriodo().setQuantidade(getPeriodo().getQuantidade() + 1);
            getPeriodo().setValor(getPeriodo().getValor() + item.getValor());
        }
    }

    private void inicializarTotalizadorEmitido() {
        setListaItensOutrosMeses(new ArrayList<ItemGestaoNotasTO>());
        setEmitido(new ItemGestaoNotasTO());
        setEmitidoGeral(new ItemGestaoNotasTO());

        Map<String, ItemGestaoNotasTO> mapa = new HashMap<String, ItemGestaoNotasTO>();

        Integer qtdOutrosMeses = 0;
        Double valorOutrosMeses = 0.0;
        Integer qtdEmitidoDeOutraForma = 0;
        Double valorEmitidoDeOutraForma = 0.0;

        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.getNfseemitida() && item.getSituacaoNotaFiscal() != null && item.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA)) {
                continue;
            }

            if(item.isEmitidoDeOutraForma()){
                qtdEmitidoDeOutraForma++;
                valorEmitidoDeOutraForma += item.getValor();
                continue;
            }

            if (item.getNfseemitida() && item.getDataReferenciaItem() != null && (item.getDataEmissao() == null || Calendario.dataNoMesmoMesAno(item.getDataReferenciaItem(), item.getDataEmissao()))) {
                qtdOutrosMeses = qtdOutrosMeses + 1;
                valorOutrosMeses = valorOutrosMeses + item.getValor();

                getEmitido().setQuantidade(getEmitido().getQuantidade() + 1);
                getEmitido().setValor(getEmitido().getValor() + item.getValorEmitido());
            } else if (item.getNfseemitida() && item.getDataReferenciaItem() != null && item.getDataEmissao() != null && !Calendario.dataNoMesmoMesAno(item.getDataReferenciaItem(), item.getDataEmissao())) {
                qtdOutrosMeses = qtdOutrosMeses + 1;
                valorOutrosMeses = valorOutrosMeses + item.getValorEmitido();

                String mesAno = Uteis.getDataMesAnoConcatenado(item.getDataEmissao());
                if (mapa.containsKey(mesAno)) {
                    ItemGestaoNotasTO novoItem = mapa.get(mesAno);
                    novoItem.setQuantidade(novoItem.getQuantidade() + 1);
                    novoItem.setValor(novoItem.getValor() + item.getValor());
                    mapa.put(mesAno, novoItem);
                } else {
                    ItemGestaoNotasTO novoItem = new ItemGestaoNotasTO();
                    novoItem.setNome("Enviada(s) - Mês Emissão " + mesAno);
                    novoItem.setQuantidade(1);
                    novoItem.setValor(item.getValor());
                    mapa.put(mesAno, novoItem);
                }

            }
        }

        getListaItensOutrosMeses().addAll(mapa.values());
        if (UteisValidacao.emptyList(getListaItensOutrosMeses())) {
            getEmitido().setNome("Enviada(s)");
        } else {
            getEmitidoGeral().setNome("Enviada(s) - Total Geral");
            getEmitidoGeral().setQuantidade(qtdOutrosMeses);
            getEmitidoGeral().setValor(valorOutrosMeses);

            getEmitido().setNome("Enviada(s) - Dentro do Mês");
        }

        getEmitidoDeOutraForma().setNome("Emitidas por outra forma de emissão");
        getEmitidoDeOutraForma().setQuantidade(qtdEmitidoDeOutraForma);
        getEmitidoDeOutraForma().setValor(valorEmitidoDeOutraForma);
    }

    private void inicializarTotalizadorSelecionado() {
        setSelecionado(new ItemGestaoNotasTO());
        getSelecionado().setNome("Selecionado");
        calcularValorSelecionado();
    }

    public void totalizar() throws Exception {
        processarNotasValoresDiferentes();

        inicializarTotalizadorSelecionado();
        inicializarTotalizadorEmitido();
        inicializarTotalizadorPeriodo();
        inicializarTotalizadorEmitir();
        inicializarTotalizadorAguardandoEnvio();

        totalizadores = new ArrayList<ItemGestaoNotasTO>();
        totalizadores.add(getPeriodo());
        totalizadores.add(getEmitir());

        if (!UteisValidacao.emptyNumber(getAguardandoEnvio().getQuantidade())) {
            totalizadores.add(getAguardandoEnvio());
        }

        totalizadores.add(getSelecionado());
        if (!UteisValidacao.emptyList(getListaItensOutrosMeses())) {
            totalizadores.add(getEmitidoGeral());
        }

        totalizadores.add(getEmitidoDeOutraForma());
        totalizadores.add(getEmitido());
        totalizadores.addAll(getListaItensOutrosMeses());

        verificarExisteNotasExcluidas(totalizadores);

        totalizarFormaPagamento();
        totalizarTipoFormaPagamento();
    }

    private void verificarExisteNotasExcluidas(List<ItemGestaoNotasTO> totalizadores) {
        try {

            Date inicioPesquisar = getDataInicio();
            Date finalPesquisar = getDataFim();
            if (isEmissaoPorCompetencia()) {
                inicioPesquisar = Uteis.obterPrimeiroDiaMes(getDataInicio());
                finalPesquisar = Uteis.obterUltimoDiaMes(getDataInicio());
            }

            setListaItensExcluidos(getFacade().getNfSeEmitidaHistorico().consultarNotasExcluidasApresentar(getEmpresaVO().getCodigo(), inicioPesquisar, finalPesquisar));

            if (!UteisValidacao.emptyList(getListaItensExcluidos())) {
                setNotasExcluidas(new ItemGestaoNotasTO());
                getNotasExcluidas().setClick(true);
                getNotasExcluidas().setNome("Notas Excluídas e Contratos Cancelados");
                for (ItemGestaoNotasTO item : getListaItensExcluidos()) {
                    getNotasExcluidas().setQuantidade(getNotasExcluidas().getQuantidade() + 1);
                    getNotasExcluidas().setValor(getNotasExcluidas().getValor() + item.getValor());
                }
                totalizadores.add(getNotasExcluidas());
            }
        } catch (Exception ignored) {
        }
    }

    private void totalizarFormaPagamento() throws Exception {
        List<FormaPagamentoVO> formasDePagamento = getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoGestaoDeNotas("", false, false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        setTotalFormaPg(new ArrayList<ItemGestaoNotasTO>());
        for (FormaPagamentoVO formaPagamento : formasDePagamento) {
            ItemGestaoNotasTO itemTotal = new ItemGestaoNotasTO();
            itemTotal.setNome(formaPagamento.getDescricao());
            itemTotal.getFormasPagamento().add(formaPagamento.getCodigo());
            getTotalFormaPg().add(itemTotal);
        }

        for (ItemGestaoNotasTO item : getListaItensApresentar()) {

            boolean emitidas = getTipoVisualizacao().equals("EM") || UteisValidacao.emptyString(getTipoVisualizacao());
            boolean naoEmitidas = getTipoVisualizacao().equals("NE") || UteisValidacao.emptyString(getTipoVisualizacao());

            if (emitidas && item.getNfseemitida()) {
                for (Map.Entry<Integer, Double> entrySet : item.getValoresEFormas().entrySet()) {
                    ItemGestaoNotasTO itemFormaPG = obtenhaItemPorFormaPG(entrySet.getKey());
                    if (itemFormaPG != null) {
                        itemFormaPG.setValor(itemFormaPG.getValor() + entrySet.getValue());
                    }
                }
            } else if (naoEmitidas && !item.getNfseemitida()) {
                for (Map.Entry<Integer, Double> entrySet : item.getValoresEFormas().entrySet()) {
                    ItemGestaoNotasTO itemFormaPG = obtenhaItemPorFormaPG(entrySet.getKey());
                    if (itemFormaPG != null) {
                        itemFormaPG.setValorNaoEmitido(itemFormaPG.getValorNaoEmitido() + entrySet.getValue());
                    }
                }
            }
        }

        List<ItemGestaoNotasTO> listaApresentar = new ArrayList<ItemGestaoNotasTO>();
        for (ItemGestaoNotasTO forma : getTotalFormaPg()) {
            if (!UteisValidacao.emptyNumber(forma.getValor()) || !UteisValidacao.emptyNumber(forma.getValorNaoEmitido())) {
                listaApresentar.add(forma);
            }
        }
        setTotalFormaPg(listaApresentar);
    }

    private void totalizarTipoFormaPagamento() throws Exception {
        setTotalTipoFormaPg(new ArrayList<ItemGestaoNotasTO>());

        Map<String, ItemGestaoNotasTO> map = new HashMap<String, ItemGestaoNotasTO>();
        for (ItemGestaoNotasTO item : getTotalFormaPg()) {
            FormaPagamentoVO formaPagamentoVO = getFacade().getFormaPagamento().consultarPorChavePrimaria(item.getFormasPagamento().get(0), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (map.containsKey(formaPagamentoVO.getTipoFormaPagamento())) {
                ItemGestaoNotasTO itemGestaoNotasTO = map.get(formaPagamentoVO.getTipoFormaPagamento());
                itemGestaoNotasTO.setValor(itemGestaoNotasTO.getValor() + item.getValor());
                itemGestaoNotasTO.setValorNaoEmitido(itemGestaoNotasTO.getValorNaoEmitido() + item.getValorNaoEmitido());
                map.put(formaPagamentoVO.getTipoFormaPagamento(), itemGestaoNotasTO);
            } else {
                ItemGestaoNotasTO novo = new ItemGestaoNotasTO();
                novo.setNome(formaPagamentoVO.getTipoFormaPagamento());
                novo.setValor(item.getValor());
                novo.setValorNaoEmitido(item.getValorNaoEmitido());
                map.put(formaPagamentoVO.getTipoFormaPagamento(), novo);
            }
        }

        List<ItemGestaoNotasTO> listaApresentar = new ArrayList<ItemGestaoNotasTO>();
        for (String key : map.keySet()) {
            ItemGestaoNotasTO novo = map.get(key);
            novo.setNome(obterDescricaoTipoFormaPagamento(key));
            listaApresentar.add(novo);
        }
        setTotalTipoFormaPg(listaApresentar);
    }

    private ItemGestaoNotasTO obtenhaItemPorFormaPG(Integer codFormaPagamento) {
        for (ItemGestaoNotasTO itemGestaoNotasTO : getTotalFormaPg()) {
            if (codFormaPagamento.equals(itemGestaoNotasTO.getFormasPagamento().get(0))) {
                return itemGestaoNotasTO;
            }
        }
        return null;
    }

    private void carregarListSelectItemsFormaDePagamento() throws Exception {
        List<FormaPagamentoVO> formasDePagamento = getFacade().getFormaPagamento().consultarPorTipoFormaPagamento("", false, false,
                false, formasInativas, Uteis.NIVELMONTARDADOS_MINIMOS);
        Ordenacao.ordenarLista(formasDePagamento, "descricao");
        setSelectItemsFormaDePagamento(new ArrayList<SelectItem>());
        getSelectItemsFormaDePagamento().add(new SelectItem(0, "TODAS AS FORMAS DE PAGAMENTO"));
        for (FormaPagamentoVO formaPagamento : formasDePagamento) {
            getSelectItemsFormaDePagamento().add(new SelectItem(formaPagamento.getCodigo(), formaPagamento.getDescricao()));
        }
    }

    public void consultarItens() {
        setMarcarTodos(false);
        setMarcarTodosExcluir(false);
        consultarNFSe(false);
    }


    public void consultarDadosEmpresa() {
        try {
            if (UtilReflection.objetoMaiorQueZero(getEmpresaVO(), "getCodigo()")) {
                setEmpresaVO(getFacade().getEmpresa().consultarPorCodigo(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                tipoRelatorioDF = TipoRelatorioDF.getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe());
            } else {
                setEmpresaVO(new EmpresaVO());
                tipoRelatorioDF = null;
            }
            carregarListaPlanos();
            montarListaConfiguracaoNotaFamilia();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void consultarNFSe(boolean familia) {
        try {
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            limparMsg();
            setListaItens(new ArrayList<>());
            setListaItensApresentar(new ArrayList<>());
            setListaFamilia(new ArrayList<>());
            setListaItensApresentar(new ArrayList<>());

            if (!UtilReflection.objetoMaiorQueZero(this.getEmpresaVO(), "getCodigo()")) {
                throw new ConsistirException("Selecione a empresa.");
            }

            if ((isEmissaoPorCompetencia() || isEmissaoPorCompetenciaIndependenteQuitacao() )  && !this.getEmpresaVO().isMostrarNotaPorDiaCompetencia()) {

                if (getDataInicio() == null) {
                    throw new Exception("A data deve ser informada");
                }

            } else {

                if (getDataInicio() == null) {
                    throw new Exception("A data inicial deve ser informada");
                }
                if (getDataFim() == null) {
                    throw new Exception("A data final deve ser informada");
                }

                if (Calendario.maior(getDataInicio(), getDataFim())) {
                    throw new Exception("A data inicial e não pode ser maior que a final");
                }

                if (!Calendario.dataNoMesmoMesAno(getDataInicio(), getDataFim()) && UteisValidacao.emptyNumber(getCodPessoa())) {
                    throw new Exception("A data inicial e final tem que ser no mesmo mês");
                }
            }

            tipoRelatorioDF = TipoRelatorioDF.getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe());

            if (getFormaPagamentoSelecionado() == 0) {
                lista = getFacade().getGestaoNotas().obterDados(
                        tipoRelatorioDF, empresaVO.getCodigo(), getFormaPagamentoSelecionado(),
                        getDataInicio(), getDataFim(), getCodPessoa(), familia, getEmpresaLogado().isMostrarNotaPorDiaCompetencia(), false, obterListaPlanosSelecionados(familia));
                switch (tipoRelatorioDF.getCodigo()) {
                    case 1:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_RECEITA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 2:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_COMPETENCIA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 3:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_FATURAMENTO_DE_CAIXA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 4:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_FATURAMENTO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 5:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_QUITADA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 6:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_NAO_QUITADA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 7:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_RECEITAPROVISAO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 8:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_TODAS_FORMAS_PGTO_COMPETENCIA_INDEPENDENTE_QUITACAO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    default:
                        break;
                }
            } else {
                lista = getFacade().getGestaoNotas().obterDados(
                        tipoRelatorioDF, empresaVO.getCodigo(), getFormaPagamentoSelecionado(),
                        getDataInicio(), getDataFim(), getCodPessoa(), familia, getEmpresaLogado().isMostrarNotaPorDiaCompetencia(), false, obterListaPlanosSelecionados(familia));
                switch (tipoRelatorioDF.getCodigo()) {
                    case 1:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_RECEITA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 2:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 3:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_FATURAMENTO_DE_CAIXA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 4:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_FATURAMENTO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 5:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA_QUITADA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 6:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_COMPETENCIA_NAO_QUITADA, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    case 7:
                        notificarRecursoEmpresa(RecursoSistema.NOTA_OUTRAS_FORMAS_PGTO_RECEITAPROVISAO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1);
                        break;
                    default:
                        break;
                }

            }
            if (UteisValidacao.emptyList(lista)) {
                throw new Exception("Nenhum item encontrado.");
            }

            if (familia) {
                montarMapaFamilia(lista);
            } else {
                setListaItens(lista);
                if (isEmissaoPorFaturamento() || isEmissaoPorCompetencia() || isEmissaoPorCompetenciaIndependenteQuitacao()) {
                    montarListaApresentar();
                } else if (isEmissaoPorFaturamentoRecebido()) {
                    montarListaApresentarFaturamentoRecebido();
                } else {
                    setListaItensApresentar(getListaItens());
                }
                setListaItensApresentarGeral(getListaItensApresentar());
                processarTipoVisualizacao();
            }
            totalizar();
        } catch (Exception err) {
            setListaItensApresentar(new ArrayList<ItemGestaoNotasTO>());
            setListaItens(new ArrayList<ItemGestaoNotasTO>());
            montarErro(err);
            Uteis.logar(err, GestaoNotas.class);
        }
    }

    public void consultarItensFamilia() {
        consultarNFSe(true);
    }

    public void montarMapaFamilia(List<ItemGestaoNotasTO> itens) throws Exception {

        notasTitular.clear();
        for (ItemGestaoNotasTO item : itens) {
            ItemGestaoNotaFamiliaTO notaFamilia = null;
            if (!UteisValidacao.emptyNumber(item.getRps())) {
                notaFamilia = notasTitular.get(item.getRps().toString());
            } else {
                notaFamilia = notasTitular.get(item.getClienteTitular().toString());
            }
            if (notaFamilia == null) {
                notaFamilia = new ItemGestaoNotaFamiliaTO();
                ClienteTitularDependenteTO titularFamilia = getFacade().getCliente().consultarTitularPorDependente(item.getCodCliente(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                ClienteVO titularCliente = titularFamilia.getClienteDependente();
                notaFamilia.setCnpj(titularCliente.getPessoa().getCnpj());
                notaFamilia.setNfseemitida(item.getNfseemitida());
                notaFamilia.setFormasPagamento(item.getFormasPagamento());
                notaFamilia.setProdutosPagos(item.getProdutosPagos());
                notaFamilia.setCodCliente(titularCliente.getCodigo());
                notaFamilia.setNome(titularCliente.getNome_Apresentar());
                notaFamilia.setClientePagador(titularCliente);
                notaFamilia.setSelecionado(false);
                notaFamilia.setParcialmenteemitida(item.getParcialmenteemitida());
                notaFamilia.getReciboPagamentoVOs().add(item);
                notaFamilia.setValor(item.getValor());
                notaFamilia.setSequencialFamilia(item.getSequencialFamilia());
                notaFamilia.setNotaFiscalVO(item.getNotaFiscalVO());
                notaFamilia.setCodNotaFiscal(item.getCodNotaFiscal());

                if (!UteisValidacao.emptyNumber(item.getRps())) {
                    notasTitular.put(item.getRps().toString(), notaFamilia);
                } else {
                    notasTitular.put(item.getClienteTitular().toString(), notaFamilia);
                }
            } else {

                notaFamilia.setValor(notaFamilia.getValor() + item.getValor());
                notaFamilia.getReciboPagamentoVOs().add(item);
                if (!UteisValidacao.emptyNumber(item.getRps())) {
                    notasTitular.put(item.getRps().toString(), notaFamilia);
                } else {
                    notasTitular.put(item.getClienteTitular().toString(), notaFamilia);
                }

            }
        }
        listaFamilia = new ArrayList<ItemGestaoNotaFamiliaTO>(notasTitular.values());
        for (ItemGestaoNotaFamiliaTO itemGestaoNotaFamiliaTO : listaFamilia) {
            int totalEmitida = 0;
            for (ItemGestaoNotasTO itemGestaoNotasTO : itemGestaoNotaFamiliaTO.getReciboPagamentoVOs()) {
                if (itemGestaoNotasTO.getNfseemitida()) {
                    totalEmitida++;
                }
            }
            itemGestaoNotaFamiliaTO.setNfseemitida(totalEmitida == itemGestaoNotaFamiliaTO.getReciboPagamentoVOs().size());
        }
    }

    public void consultarItensAutomatico(EmpresaVO empresaVO, Integer formaPagamento, Date dataInicio,
                                         Date dataFim, boolean processoAutomatico) throws Exception {
        setEmpresaVO(empresaVO);

        TipoRelatorioDF tipoRelatorioDF = TipoRelatorioDF.getTipoRelatorioDF(empresaVO.getTipoGestaoNFSe());

        List<ItemGestaoNotasTO> itensConsulta = getFacade().getGestaoNotas().obterDados(
                tipoRelatorioDF, empresaVO.getCodigo(), formaPagamento, dataInicio, dataFim, null, false,
                empresaVO.isMostrarNotaPorDiaCompetencia(), processoAutomatico, null);

        setListaItens(itensConsulta);
        if (isEmissaoPorFaturamento() || isEmissaoPorCompetencia()) {
            montarListaApresentar();
        } else {
            setListaItensApresentar(getListaItens());
        }
    }

    public List<ItemGestaoNotaFamiliaTO> getListaFamilia() {
        if (listaFamilia == null) {
            listaFamilia = new ArrayList<ItemGestaoNotaFamiliaTO>();
        }
        return listaFamilia;
    }

    public void setListaFamilia(List<ItemGestaoNotaFamiliaTO> listaFamilia) {
        this.listaFamilia = listaFamilia;
    }

    public void selecionarTodosItens() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
//            Logger.getLogger(GestaoNotasControle.class.getName()).log(Level.INFO, "RPS: " + item.getRps() + " - Status: " + item.getSituacaoNotaFiscal() + " - Selecionar: " + (!item.getNfseemitida() && !item.isSelecionado()) + ";", new Object[]{});
            if (!item.getNfseemitida() && !item.isSelecionado()) {
                item.setSelecionado(true);
            }
        }
    }

    private void montarListaApresentarFaturamentoRecebido() throws Exception {
        EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        String[] tipoProdutoEmissaoNFSe = empresa.getTipoProdutoEmissaoNFSe().split("\\|");

        for (ItemGestaoNotasTO item : getListaItens()) {
            String[] produtosPagos = item.getProdutosPagos().split("\\|");
            StringBuilder descricaoProdutosPagos = new StringBuilder();
            for (String produtoPago : produtosPagos) {
                if (!produtoPago.isEmpty()) {
                    String[] produtosPagosSplit = produtoPago.split(",");
                    String codigoProduto = produtosPagosSplit[0];
                    String tipoProduto = produtosPagosSplit[1];
                    Integer codProduto = Integer.parseInt(codigoProduto);
                    MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(codProduto, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    for (String produtoTipo : tipoProdutoEmissaoNFSe) {
                        if (produtoTipo.equals(tipoProduto)) {
                            descricaoProdutosPagos.append(movProdutoVO.getDescricao()).append(" <br>");
                        }
                    }
                }
            }
            item.setDescricaoProdutosPagos(descricaoProdutosPagos.toString());
        }
        setListaItensApresentar(getListaItens());
    }

    private void montarListaApresentar() throws Exception {
        List<ItemGestaoNotasTO> itensApresentar = new ArrayList<ItemGestaoNotasTO>();

        for (ItemGestaoNotasTO item : getListaItens()) {
            ItemGestaoNotasTO itemEncontrado = null;
            for (ItemGestaoNotasTO itemAuxiliar : itensApresentar) {
                if (item.getNome().equals(itemAuxiliar.getNome())
                        && item.getMovProdutoVO().getCodigo().equals(itemAuxiliar.getMovProdutoVO().getCodigo())) {
                    itemEncontrado = itemAuxiliar;
                }
            }

            if (itemEncontrado != null) {
                if (!itemEncontrado.getNfseemitida()) {
                    itemEncontrado.setValor(itemEncontrado.getValor() + item.getValor());
                }
            } else {
                ItemGestaoNotasTO itemNovo = new ItemGestaoNotasTO();
                itemNovo.setNome(item.getNome());
                itemNovo.setMatricula(item.getMatricula());
                itemNovo.setCpf(item.getCpf());
                itemNovo.setValor(item.getValor());
                itemNovo.setNfseemitida(item.getNfseemitida());
                itemNovo.setRps(item.getRps());
                itemNovo.setNrNotaManual(item.getNrNotaManual());
                itemNovo.setParcialmenteemitida(item.getParcialmenteemitida());
                itemNovo.setPessoaVO(item.getPessoaVO());
                itemNovo.setCodCliente(item.getCodCliente());

                itemNovo.setCodNFSeEmitida(item.getCodNFSeEmitida());
                itemNovo.setCodNFCeEmitida(item.getCodNFCeEmitida());
                itemNovo.setCodNotaFiscal(item.getCodNotaFiscal());
                itemNovo.setNotaFiscalVO(item.getNotaFiscalVO());

                itemNovo.setChequeVO(item.getChequeVO());
                itemNovo.setMovProdutoVO(item.getMovProdutoVO());
                itemNovo.setMovPagamentoVO(item.getMovPagamentoVO());
                itemNovo.setReciboPagamentoVO(item.getReciboPagamentoVO());

                if (item.getCodCliente() == 0) {
                    ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(item.getMovProdutoVO().getPessoa().getCodigo(), item.getMovProdutoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    itemNovo.setNome(colaboradorVO.getPessoa_Apresentar());
                    itemNovo.setMatricula("COLABORADOR");
                    itemNovo.setCpf(colaboradorVO.getPessoa().getCfp());
                    itemNovo.setCodColaborador(colaboradorVO.getCodigo());
                }

                itemNovo.setDataEmissao(item.getDataEmissao());
                itemNovo.setDataReferenciaItem(item.getDataReferenciaItem());
                itemNovo.setSituacaoNotaFiscal(item.getSituacaoNotaFiscal());
                itemNovo.setEmitidoDeOutraForma(item.isEmitidoDeOutraForma());
                itemNovo.setDataEmissao(item.getDataEmissao());
                itemNovo.setValorEmitido(item.getValorEmitido());
                itemNovo.setNomeResponsavel(item.getNomeResponsavel());
                itemNovo.setValoresEFormas(item.getValoresEFormas());

                itensApresentar.add(itemNovo);
            }

        }
        setListaItensApresentar(itensApresentar);
    }

    public void calcularValorSelecionado() {
        double valorSelecionado = 0.0;
        Integer selecionado = 0;
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.isSelecionado()) {
                selecionado++;
                valorSelecionado += item.getValor();
            }
        }
        getSelecionado().setQuantidade(selecionado);
        getSelecionado().setValor(valorSelecionado);
    }

    public void calcularValorSelecionadoFamilia() {
        double valorSelecionado = 0.0;
        for (ItemGestaoNotaFamiliaTO item : getListaFamilia()) {
            //if (item.isSelecionado()) {
            for (ItemGestaoNotasTO itemNota : item.getReciboPagamentoVOs()) {
                if (itemNota.isSelecionado()) {
                    valorSelecionado += itemNota.getValor();
                }
            }
            //}
        }
        getSelecionado().setValor(valorSelecionado);
    }

    public Object selecionarValor() throws Exception {
        Double valorSelecionado = 0.0;
        Double valorParaSelecionar = getValorParaSelecionar();
        boolean empresaPermiteGerarNotaManual = this.empresaVO.isPermiteGerarNotaManual();
        List<ItemGestaoNotasTO> itensParcialmenteEmitidos = new ArrayList<ItemGestaoNotasTO>();
        List<ItemGestaoNotasTO> itensEmitir = new ArrayList<ItemGestaoNotasTO>();

        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!item.getNfseemitida()) {
                item.setSelecionado(false);
                if(item.getParcialmenteemitida()) {
                    itensParcialmenteEmitidos.add(item);
                } else {
                    itensEmitir.add(item);
                }
            }
        }

        //ordenação das notas para priorizar na seleção notas parcialmente emitidas
        Ordenacao.ordenarLista(itensParcialmenteEmitidos, "valor");
        Ordenacao.ordenarLista(itensEmitir, "valor");
        itensParcialmenteEmitidos.addAll(itensEmitir);

        for (ItemGestaoNotasTO item : itensParcialmenteEmitidos ) {
            boolean cpfVazio = UteisValidacao.emptyString(item.getCpf());
            if(valorSelecionado < valorParaSelecionar) {
                if (!cpfVazio || empresaPermiteGerarNotaManual) {
                    item.setSelecionado(true);
                    valorSelecionado += item.getValor();
                }
            } else {
                break;
            }
        }

        totalizar();
        return true;
    }

    public void enviarTodasNotas() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            item.setRetorno("");
            if (!item.getNfseemitida()) {
                item.setSelecionado(true);
            }
        }
        enviarNotas();
    }

    public void enviarNotasAutomatico(EmpresaVO empresaVO, String chave) {
        enviarNotasGeral(empresaVO, chave);
    }

    public void enviarNotas() {
        enviarNotasGeral(empresaVO, getKey());
    }

    private void enviarNotasGeral(EmpresaVO empresaVO, String chave) {
        try {
            setLoadEnviandoNotas(true);
            setMsgEnviandoNotas("Aguarde, estamos iniciando o envio das notas...");

//            Logger.getLogger(GestaoNotasControle.class.getName()).log(Level.INFO, "Certificado Vencido:  " + certificadoVencido(empresaVO), new Object[]{});
//            if (certificadoVencido(empresaVO)) {
//                setLoadEnviandoNotas(false);
//                throw new Exception("Certificado vencido! Solicite suporte após a atualização.");
//            }

            processarNotas(TipoGeracaoNotaFiscal.ENVIAR_NOTA, chave);
            setLoadEnviandoNotas(false);
            montarSucessoGrowl("Nota processadas com sucesso!");
        } catch (Exception e) {
            setLoadEnviandoNotas(false);
            montarErro(e);
        }
    }

    private boolean certificadoVencido(EmpresaVO empresaVO) throws Exception {
//        empresaVO = empresaVO == null ? getEmpresaLogado() : empresaVO;
//        if (empresaVO.isValidarCertificado()) {
//            return getFacade().getNotaFiscalConsumidorEletronica().isCertificadoVencido(empresaVO.getCNPJ());
//        }
        return false;
    }

    public void gerarArquivoLoteRPS() {
        try {
            processarNotas(TipoGeracaoNotaFiscal.GERAR_XML, getKey());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gerarArquivoLoteRPSTodas() {
        try {
            for (ItemGestaoNotasTO item : getListaItensApresentar()) {
                item.setRetorno("");
                if (!item.getNfseemitida()) {
                    item.setSelecionado(true);
                }
            }
            processarNotas(TipoGeracaoNotaFiscal.GERAR_XML, getKey());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void processarNotas(TipoGeracaoNotaFiscal tipoGeracaoNotaFiscal, String chave) throws Exception {
        limparMsg();

        if (Calendario.maior(getDataEmissao(), Calendario.hoje())) {
            throw new ConsistirException("A data de emissão não pode ser superior a data atual.");
        }

        List<ItemGestaoNotasTO> notasEmitir = new ArrayList<>();
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            item.setRetorno("");
            if (item.isSelecionado()) {
                notasEmitir.add(item);
            }
        }

        retirarMovProdutoDuplicado(notasEmitir);

        Uteis.logar(null, "Quantidade de itens para enviar: " + notasEmitir.size());

        if (UteisValidacao.emptyList(notasEmitir)) {
            getFacade().getNotaFiscal().enviarNotasAguardando();
            throw new Exception("Nenhuma nota selecionada");
        }

        setTotalizadorNotasEnviadas(0);
        setTotalizadorNotasAEnviar(notasEmitir.size());
        setMsgEnviandoNotas("Enviando notas...");

        UsuarioVO usuarioVO = null;
        if (JSFUtilities.isJSFContext()) {
            usuarioVO = getUsuarioLogado();
        } else {
            usuarioVO = getFacade().getUsuario().getUsuarioRecorrencia();
        }

        if (tipoGeracaoNotaFiscal.equals(TipoGeracaoNotaFiscal.GERAR_XML)
                || tipoGeracaoNotaFiscal.equals(TipoGeracaoNotaFiscal.GERAR_XML_MARABA)) {

            gerarArquivoLoteRPS(notasEmitir, tipoGeracaoNotaFiscal, getDataEmissao());
            totalizar();

        } else if (tipoGeracaoNotaFiscal.equals(TipoGeracaoNotaFiscal.NOTA_MANUAL)) {

            getFacade().getNotaFiscal().processarItensIndividual(chave, TipoNotaFiscalEnum.NFSE, usuarioVO, notasEmitir, getDataEmissao(), getNumeroNotaManual(), empresaVO.getTipoGestaoNFSeEnum());

            totalizar();
            montarLog(notasEmitir);

        } else {

            getFacade().getNotaFiscal().processarItensIndividual(chave, TipoNotaFiscalEnum.NFSE, usuarioVO, notasEmitir, getDataEmissao(), null, empresaVO.getTipoGestaoNFSeEnum());

            ajustarStatusMovProdutoDuplicado(notasEmitir);
            totalizar();
            montarLog(notasEmitir);
        }

        setTotalizadorNotasEnviadas(0);
        setTotalizadorNotasAEnviar(0);
        setMarcarTodos(false);
        setMarcarTodosExcluir(false);
    }

    private void retirarMovProdutoDuplicado(List<ItemGestaoNotasTO> notasEmitir) {
        if(empresaVO.getTipoGestaoNFSeEnum().equals(TipoRelatorioDF.COMPETENCIA)){
            Set<Integer> codigosDuplicados = notasEmitir.stream()
                    .collect(Collectors.groupingBy(
                            obj -> obj.getMovProdutoVO().getCodigo(),
                            Collectors.counting()))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());

            List<ItemGestaoNotasTO> itensDuplicadosRemover = notasEmitir.stream()
                    .filter(obj -> codigosDuplicados.contains(obj.getMovProdutoVO().getCodigo()))
                    .filter(obj -> !obj.getNome().equals(obj.getMovProdutoVO().getPessoa().getNome()))
                    .collect(Collectors.toList());

            notasEmitir.removeAll(itensDuplicadosRemover);
        }
    }

    private void ajustarStatusMovProdutoDuplicado(List<ItemGestaoNotasTO> notasEmitir) {
        if(empresaVO.getTipoGestaoNFSeEnum().equals(TipoRelatorioDF.COMPETENCIA)){

            Set<Integer> codigosDuplicados = this.listaItensApresentar.stream()
                    .collect(Collectors.groupingBy(
                            obj -> obj.getMovProdutoVO().getCodigo(),
                            Collectors.counting()))
                    .entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());

            if(!UteisValidacao.emptyList(codigosDuplicados)){
                for(ItemGestaoNotasTO item: notasEmitir){
                    if(codigosDuplicados.contains(item.getMovProdutoVO().getCodigo())){
                        for(ItemGestaoNotasTO itemApresentar: this.listaItensApresentar){
                            if(itemApresentar.getMovProdutoVO().getCodigo().equals(item.getMovProdutoVO().getCodigo())){
                                itemApresentar.setNfseemitida(item.getNfseemitida());
                                itemApresentar.setRetorno(item.getRetorno());
                                itemApresentar.setSelecionado((item.isSelecionado()));
                            }
                        }
                    }
                }
            }
        }
    }

    private Boolean existeItemSelecionado(ItemGestaoNotaFamiliaTO item) {
        boolean existe = false;
        for (ItemGestaoNotasTO itemNota : item.getReciboPagamentoVOs()) {
            if (itemNota.isSelecionado()) {
                existe = true;
                break;
            }
        }
        return existe;
    }

    public void enviarNotasFamilia() {
        enviarNotasFamilia(null);
    }

    public void enviarNotasFamilia(EmpresaVO empresaVO) {
        try {
            limparMsg();

            if (certificadoVencido(empresaVO)) {
                throw new Exception("Certificado vencido! Solicite suporte após a atualização.");
            }

            if (UteisValidacao.emptyNumber(getConfigNotaFiscalFamilia().getCodigo())) {
                throw new Exception("Selecione uma configuração de emissão de notas.");
            }

            List<ItemGestaoNotaFamiliaTO> notasEmitir = new ArrayList<ItemGestaoNotaFamiliaTO>();
            for (ItemGestaoNotaFamiliaTO it : getNotasTitular().values()) {
                it.setRetorno("");
                if (existeItemSelecionado(it)) {
                    notasEmitir.add(it);
                }
            }

            getFacade().getNotaFiscal().processarItensFamilia(getConfigNotaFiscalFamilia(),
                    notasEmitir,
                    this.tipoRelatorioDF,
                    getEmpresaVO(),
                    getUsuarioLogado(),
                    getDataEmissao(), getKey());

            totalizar();
            montarLogFamilia(notasEmitir);
        } catch (Exception erro) {
            montarErro(erro);
        }
    }

    public void irParaTelaClienteColaborador() {
        ItemGestaoNotasTO obj = (ItemGestaoNotasTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente ou Colaborador não Encontrado.");
            } else if (obj.getCodCliente() == 0) {
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(obj.getCodColaborador(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                irParaTelaColaborador(colaboradorVO);
                setOnComplete("abrirPopup('colaboradorForm.jsp', 'Colaborador', 1024, 700);");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCodCliente());
                irParaTelaCliente(clienteVO);
                setOnComplete("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog(nomeClasse));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, 0);
    }

    private void gravarLog(double valorEmitido, double valorNaoEmitido, double valorAguardando, StringBuilder enviadas, StringBuilder naoEnviadas, StringBuilder aguardando,
                           JSONArray notasEnviadasJSON, JSONArray notasNaoEnviadasJSON, JSONArray notasAguardandoJSON, boolean notaFamilia) throws Exception {
        gravarLog(valorEmitido, valorNaoEmitido, valorAguardando, enviadas, naoEnviadas, aguardando,
                notasEnviadasJSON, notasNaoEnviadasJSON, notasAguardandoJSON, notaFamilia, false);
    }

    private void gravarLog(double valorEmitido, double valorNaoEmitido, double valorAguardando, StringBuilder enviadas, StringBuilder naoEnviadas, StringBuilder aguardando,
                           JSONArray notasEnviadasJSON, JSONArray notasNaoEnviadasJSON, JSONArray notasAguardandoJSON, boolean notaFamilia, boolean desvincular) throws Exception {
        try {
            String valorTotalEmitido = Formatador.formatarValorMonetario(valorEmitido + valorNaoEmitido + valorAguardando);
            String valorEmitidoString = Formatador.formatarValorMonetario(valorEmitido);
            String valorNaoEmitidoString = Formatador.formatarValorMonetario(valorNaoEmitido);
            String valorAguardandoString = Formatador.formatarValorMonetario(valorAguardando);


            JSONObject jsonObject = new JSONObject();
            jsonObject.put("ValorTotal", valorTotalEmitido);
            jsonObject.put("ValorEmitido", valorEmitidoString);
            jsonObject.put("ValorNaoEmitido", valorNaoEmitidoString);
            jsonObject.put("ValorAguardando", valorAguardandoString);
            jsonObject.put("AlunosEnviados", notasEnviadasJSON);
            jsonObject.put("AlunosNaoEnviados", notasNaoEnviadasJSON);
            jsonObject.put("AlunosAguardando", notasAguardandoJSON);
            jsonObject.put("NotaFamilia", notaFamilia);

            StringBuilder notasEnviadas = new StringBuilder();
            notasEnviadas.append("NOTAS ENVIADAS \r\n");
            notasEnviadas.append(enviadas);
            StringBuilder notasNaoEnviadas = new StringBuilder();
            notasNaoEnviadas.append("NOTAS NÃO ENVIADAS \r\n");
            notasNaoEnviadas.append(naoEnviadas);
            StringBuilder notasAguardando = new StringBuilder();
            notasAguardando.append("NOTAS AGUARDANDO ENVIO \r\n");
            notasAguardando.append(aguardando);

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setNomeEntidade(nomeClasse.toUpperCase());
            obj.setNomeEntidadeDescricao(nomeClasse.toUpperCase());
            if (desvincular) {
                obj.setOperacao("DESVÍNCULO DE NOTAS NO VALOR TOTAL DE " + valorTotalEmitido);
            } else {
                obj.setOperacao("EMISSÃO DE NOTAS NO VALOR TOTAL DE " + valorTotalEmitido);
            }
            String usuario = "";
            String usuarioOAMD = "";
            try {
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());

                usuario = getUsuarioLogado().getNome();
                usuarioOAMD = getUsuarioLogado().getUserOamd();
            } catch (Exception ex) {
                usuario = "PROCESSO DIÁRIO";
                obj.setResponsavelAlteracao(usuario);
            }
            jsonObject.put("Usuario", usuario);
            jsonObject.put("UsuarioOAMD", usuarioOAMD);

            obj.setNomeCampo("NOTAS");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");

            StringBuilder alterado = new StringBuilder();
            if (notaFamilia) {
                alterado.append("ENVIO DE NOTA NOTA EM GRUPO\r\n");
            }
            alterado.append("VALOR ENVIADO = ").append(valorEmitidoString);
            alterado.append("\r\nVALOR NÃO ENVIADO = ").append(valorNaoEmitidoString);
            alterado.append("\r\nVALOR AGUARDANDO ENVIO = ").append(valorAguardandoString);

            Integer codFormaPagamento = 0;
            String formaPagamentoDescricao = "";
            if (notaFamilia && !UteisValidacao.emptyNumber(getFormaPagamentoSelecionadoFamilia())) {
                codFormaPagamento = getFormaPagamentoSelecionadoFamilia();
                alterado.append("\r\nFORMA PAGAMENTO = ").append(getFormaPagamentoSelecionadoFamilia());

                FormaPagamentoVO formaPagamentoVO = getFacade().getFormaPagamento().consultarPorChavePrimaria(getFormaPagamentoSelecionadoFamilia(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (formaPagamentoVO != null) {
                    formaPagamentoDescricao = formaPagamentoVO.getDescricao();
                    alterado.append(" - ").append(formaPagamentoVO.getDescricao());
                }
            } else if (!notaFamilia && !UteisValidacao.emptyNumber(getFormaPagamentoSelecionado())) {
                codFormaPagamento = getFormaPagamentoSelecionado();
                alterado.append("\r\nFORMA PAGAMENTO = ").append(getFormaPagamentoSelecionado());

                FormaPagamentoVO formaPagamentoVO = getFacade().getFormaPagamento().consultarPorChavePrimaria(getFormaPagamentoSelecionado(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (formaPagamentoVO != null) {
                    formaPagamentoDescricao = formaPagamentoVO.getDescricao();
                    alterado.append(" - ").append(formaPagamentoVO.getDescricao());
                }
            }
            jsonObject.put("FormaPagamentoFiltro", codFormaPagamento);
            jsonObject.put("FormaPagamentoFiltroDescricao", formaPagamentoDescricao);

            Integer pessoaFiltro = 0;
            String pessoaNome = "";
            if (!UteisValidacao.emptyNumber(getCodPessoa())) {
                pessoaFiltro = getCodPessoa();
                alterado.append("\r\nPESSOA = ").append(getCodPessoa());
                try {
                    PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(getCodPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    if (pessoaVO != null) {
                        pessoaNome = pessoaVO.getNome();
                        alterado.append(" - ").append(pessoaVO.getNome());
                    }
                } catch (Exception ignored) {
                }
            }
            jsonObject.put("PessoaFiltro", pessoaFiltro);
            jsonObject.put("PessoaFiltroNome", pessoaNome);
            jsonObject.put("DataInicio", Uteis.getData(getDataInicio()));
            jsonObject.put("DataFinal", Uteis.getData(getDataFim()));
            jsonObject.put("DataEmissao", Uteis.getData(getDataEmissao()));
            jsonObject.put("Empresa", getEmpresaVO().getCodigo());

            alterado.append("\r\nDATA INICIAL = ").append(Uteis.getData(getDataInicio()));
            alterado.append("\r\nDATA FINAL = ").append(Uteis.getData(getDataFim()));
            alterado.append("\r\nDATA EMISSÃO = ").append(Uteis.getData(getDataEmissao()));
            alterado.append("\r\n\r\n").append(notasEnviadas).append(" \r\n").append(notasNaoEnviadas).append(" \r\n").append(notasAguardando);

            try {
                getFacade().getNotaFiscal().incluirLogEmisao(jsonObject.toString(), getEmpresaVO().getCodigo());
            } catch (Exception ignored) {
            }

            obj.setValorCampoAlterado(alterado.toString());
            registrarLogObjetoVO(obj, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nomeClasse.toUpperCase(), 0, "ERRO AO GERAR LOG DA GESTÃO DE NOTAS" + e.getMessage(), this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void montarLogFamilia(List<ItemGestaoNotaFamiliaTO> notas) throws Exception {
        double valorEmitido = 0.0;
        double valorNaoEmitido = 0.0;
        double valorAguardando = 0.0;
        StringBuilder notasEnviadas = new StringBuilder();
        StringBuilder notasNaoEnviadas = new StringBuilder();
        StringBuilder notasAguardando = new StringBuilder();

        JSONArray notasEnviadasJSON = new JSONArray();
        JSONArray notasNaoEnviadasJSON = new JSONArray();
        JSONArray notasAguardandoJSON = new JSONArray();
        for (ItemGestaoNotaFamiliaTO itemGestaoNotaFamiliaTO : notas) {
            int totalEmitida = 0;
            for (ItemGestaoNotasTO itemGestaoNotasTO : itemGestaoNotaFamiliaTO.getReciboPagamentoVOs()) {
                itemGestaoNotasTO.setSituacaoNotaFiscal(itemGestaoNotaFamiliaTO.getSituacaoNotaFiscal());
                if (itemGestaoNotasTO.getNfseemitida() && (itemGestaoNotasTO.getSituacaoNotaFiscal() != null && itemGestaoNotasTO.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.ENVIADA))) {
                    valorEmitido += itemGestaoNotaFamiliaTO.getValor();
//                    notasEnviadas.append("NFSEEMITIDA: ").append(itemGestaoNotasTO.getNfSeEmitidaVO().getCodigo()).append(" || ").append(itemGestaoNotasTO.getNome()).append(" || VALOR: ").append(itemGestaoNotasTO.getValor_apresentar()).append(" || RESULTADO: ").append(itemGestaoNotasTO.getRetorno()).append("\r\n");
                    totalEmitida++;

                    JSONObject json = new JSONObject();
//                    json.put("NFSeEmitida", itemGestaoNotasTO.getNfSeEmitidaVO().getCodigo());
                    json.put("Nome", itemGestaoNotasTO.getNome());
                    json.put("Valor", itemGestaoNotasTO.getValor_apresentar());
                    json.put("Resultado", itemGestaoNotasTO.getRetorno());
                    notasEnviadasJSON.put(json);


                } else if (itemGestaoNotasTO.getNfseemitida() && (itemGestaoNotasTO.getSituacaoNotaFiscal() != null && itemGestaoNotasTO.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA))) {
                    valorAguardando += itemGestaoNotaFamiliaTO.getValor();
//                    notasAguardando.append("NFSEEMITIDA: ").append(itemGestaoNotasTO.getNfSeEmitidaVO().getCodigo()).append(" || ").append(itemGestaoNotasTO.getNome()).append(" || VALOR: ").append(itemGestaoNotasTO.getValor_apresentar()).append(" || RESULTADO: ").append(itemGestaoNotasTO.getRetorno()).append("\r\n");
                    totalEmitida++;

                    JSONObject json = new JSONObject();
//                    json.put("NFSeEmitida", itemGestaoNotasTO.getNfSeEmitidaVO().getCodigo());
                    json.put("Nome", itemGestaoNotasTO.getNome());
                    json.put("Valor", itemGestaoNotasTO.getValor_apresentar());
                    json.put("Resultado", itemGestaoNotasTO.getRetorno());
                    notasAguardandoJSON.put(json);

                } else if (!itemGestaoNotasTO.getNfseemitida()) {
                    valorNaoEmitido += itemGestaoNotaFamiliaTO.getValor();
                    notasNaoEnviadas.append(itemGestaoNotasTO.getNome()).append(" || VALOR: ").append(itemGestaoNotasTO.getValor_apresentar()).append(" || RESULTADO:  ").append(itemGestaoNotasTO.getRetorno()).append("\r\n");

                    JSONObject json = new JSONObject();
                    json.put("Nome", itemGestaoNotasTO.getNome());
                    json.put("Valor", itemGestaoNotasTO.getValor_apresentar());
                    json.put("Resultado", itemGestaoNotasTO.getRetorno());
                    notasNaoEnviadasJSON.put(json);
                }
            }
            itemGestaoNotaFamiliaTO.setNfseemitida(totalEmitida == itemGestaoNotaFamiliaTO.getReciboPagamentoVOs().size());
        }
        gravarLog(valorEmitido, valorNaoEmitido, valorAguardando, notasEnviadas, notasNaoEnviadas, notasAguardando, notasEnviadasJSON, notasNaoEnviadasJSON, notasAguardandoJSON, true);

    }

    private void montarLogAoDesvincularNota(List<ItemGestaoNotasTO> notas) throws Exception {

        double valorEmitido = 0.0;
        double valorNaoEmitido = 0.0;
        double valorAguardando = 0.0;
        StringBuilder notasEnviadas = new StringBuilder();
        StringBuilder notasNaoEnviadas = new StringBuilder();
        StringBuilder notasAguardando = new StringBuilder();

        JSONArray notasEnviadasJSON = new JSONArray();
        JSONArray notasNaoEnviadasJSON = new JSONArray();
        JSONArray notasAguardandoJSON = new JSONArray();
        for (ItemGestaoNotasTO item : notas) {
            if (item.isSelecionadoExcluir()) {
                if (item.getNfseemitida() && item.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.ENVIADA)) {
                    valorEmitido += item.getValor();
//                notasEnviadas.append("NFSEEMITIDA: ").append(item.getNfSeEmitidaVO().getCodigo()).append(" || ").append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");

                    JSONObject json = new JSONObject();
//                json.put("NFSeEmitida", item.getNfSeEmitidaVO().getCodigo());
                    json.put("Nome", item.getNome());
                    json.put("Valor", item.getValor_apresentar());
                    json.put("Resultado", item.getRetorno());
                    notasEnviadasJSON.put(json);

                } else if (item.getNfseemitida() && item.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA)) {
                    valorAguardando += item.getValor();
//                notasAguardando.append("NFSEEMITIDA: ").append(item.getNfSeEmitidaVO().getCodigo()).append(" || ").append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");

                    JSONObject json = new JSONObject();
//                json.put("NFSeEmitida", item.getNfSeEmitidaVO().getCodigo());
                    json.put("Nome", item.getNome());
                    json.put("Valor", item.getValor_apresentar());
                    json.put("Resultado", item.getRetorno());
                    notasAguardandoJSON.put(json);

                } else if (!item.getNfseemitida()) {
                    valorNaoEmitido += item.getValor();
                    notasNaoEnviadas.append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");

                    JSONObject json = new JSONObject();
                    json.put("Nome", item.getNome());
                    json.put("Valor", item.getValor_apresentar());
                    json.put("Resultado", item.getRetorno());
                    notasNaoEnviadasJSON.put(json);
                }
            }
        }
        gravarLog(valorEmitido, valorNaoEmitido, valorAguardando, notasEnviadas, notasNaoEnviadas, notasAguardando, notasEnviadasJSON, notasNaoEnviadasJSON, notasAguardandoJSON, false, true);
    }

    private void montarLog(List<ItemGestaoNotasTO> notas) throws Exception {

        double valorEmitido = 0.0;
        double valorNaoEmitido = 0.0;
        double valorAguardando = 0.0;
        StringBuilder notasEnviadas = new StringBuilder();
        StringBuilder notasNaoEnviadas = new StringBuilder();
        StringBuilder notasAguardando = new StringBuilder();

        JSONArray notasEnviadasJSON = new JSONArray();
        JSONArray notasNaoEnviadasJSON = new JSONArray();
        JSONArray notasAguardandoJSON = new JSONArray();
        for (ItemGestaoNotasTO item : notas) {
            if (item.getNfseemitida() && item.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.ENVIADA)) {
                valorEmitido += item.getValor();
//                notasEnviadas.append("NFSEEMITIDA: ").append(item.getNfSeEmitidaVO().getCodigo()).append(" || ").append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");

                JSONObject json = new JSONObject();
//                json.put("NFSeEmitida", item.getNfSeEmitidaVO().getCodigo());
                json.put("Nome", item.getNome());
                json.put("Valor", item.getValor_apresentar());
                json.put("Resultado", item.getRetorno());
                notasEnviadasJSON.put(json);

            } else if (item.getNfseemitida() && item.getSituacaoNotaFiscal().equals(SituacaoNotaFiscalEnum.GERADA)) {
                valorAguardando += item.getValor();
//                notasAguardando.append("NFSEEMITIDA: ").append(item.getNfSeEmitidaVO().getCodigo()).append(" || ").append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");

                JSONObject json = new JSONObject();
//                json.put("NFSeEmitida", item.getNfSeEmitidaVO().getCodigo());
                json.put("Nome", item.getNome());
                json.put("Valor", item.getValor_apresentar());
                json.put("Resultado", item.getRetorno());
                notasAguardandoJSON.put(json);

            } else if (!item.getNfseemitida()) {
                valorNaoEmitido += item.getValor();
                notasNaoEnviadas.append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");

                JSONObject json = new JSONObject();
                json.put("Nome", item.getNome());
                json.put("Valor", item.getValor_apresentar());
                json.put("Resultado", item.getRetorno());
                notasNaoEnviadasJSON.put(json);
            }
        }
        gravarLog(valorEmitido, valorNaoEmitido, valorAguardando, notasEnviadas, notasNaoEnviadas, notasAguardando, notasEnviadasJSON, notasNaoEnviadasJSON, notasAguardandoJSON, false);
    }

    public List<PessoaVO> executarAutocompleteConsultaPessoa(Object suggest) {
        String pref = (String) suggest;
        ArrayList<PessoaVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPessoaComLimite(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPessoaPorNomeComLimite(getEmpresaLogado().getCodigo(), pref, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public List<ClienteVO> executarAutocompleteConsultaClienteFamilia(Object suggest) {
        String pref = (String) suggest;
        List<ClienteVO> result;
        try {
            result = getFacade().getCliente().consultarPorNomeClienteFamilia(getEmpresaLogado().getCodigo(), pref, 50, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }


    public void selecionarPessoaSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        codpessoa = (pessoaVO.getCodigo());
        limparListas();
    }

    public void selecionarClienteSuggestionBoxFamilia() throws Exception {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("result");
        codpessoa = clienteVO.getPessoa().getCodigo();
    }

    public List<ItemGestaoNotasTO> getTotalizadores() {
        return totalizadores;
    }

    public void setTotalizadores(List<ItemGestaoNotasTO> totalizadores) {
        this.totalizadores = totalizadores;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Double getValorParaSelecionar() {
        return valorParaSelecionar;
    }

    public void setValorParaSelecionar(Double valorParaSelecionar) {
        this.valorParaSelecionar = valorParaSelecionar;
    }

    public List<SelectItem> getSelectItemsFormaDePagamento() {
        return selectItemsFormaDePagamento;
    }

    public void setSelectItemsFormaDePagamento(List<SelectItem> selectItemsFormaDePagamento) {
        this.selectItemsFormaDePagamento = selectItemsFormaDePagamento;
    }

    public Integer getFormaPagamentoSelecionado() {
        return formaPagamentoSelecionado;
    }

    public void setFormaPagamentoSelecionado(Integer formaPagamentoSelecionado) {
        this.formaPagamentoSelecionado = formaPagamentoSelecionado;
    }

    public List<ItemGestaoNotasTO> getListaItens() {
        return listaItens;
    }

    public void setListaItens(List<ItemGestaoNotasTO> listaItens) {
        this.listaItens = listaItens;
    }

    public ItemGestaoNotasTO getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(ItemGestaoNotasTO selecionado) {
        this.selecionado = selecionado;
    }

    public ItemGestaoNotasTO getEmitido() {
        return emitido;
    }

    public void setEmitido(ItemGestaoNotasTO emitido) {
        this.emitido = emitido;
    }

    public ItemGestaoNotasTO getPeriodo() {
        return periodo;
    }

    public void setPeriodo(ItemGestaoNotasTO periodo) {
        this.periodo = periodo;
    }

    public ItemGestaoNotasTO getEmitir() {
        return emitir;
    }

    public void setEmitir(ItemGestaoNotasTO emitir) {
        this.emitir = emitir;
    }

    public List<ItemGestaoNotasTO> getTotalFormaPg() {
        return totalFormaPg;
    }

    public void setTotalFormaPg(List<ItemGestaoNotasTO> totalFormaPg) {
        this.totalFormaPg = totalFormaPg;
    }

    public List<ItemGestaoNotasTO> getListaItensApresentar() {
        return listaItensApresentar;
    }

    public void setListaItensApresentar(List<ItemGestaoNotasTO> listaItensApresentar) {
        this.listaItensApresentar = listaItensApresentar;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public Integer getCodPessoa() {
        return codpessoa;
    }

    public void setCodPessoa(Integer codpessoa) {
        this.codpessoa = codpessoa;
    }

    public void limparAluno() throws Exception {
        limparListas();
        setCodPessoa(null);
    }

    public void limparAlunoFamilia() {
        setCodPessoa(null);
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public Date getDataEmissao() {
        if (dataEmissao == null) {
            dataEmissao = new Date();
        }
        return dataEmissao;
    }

    public void setDataEmissao(Date dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public Integer getFormaPagamentoSelecionadoFamilia() {
        return formaPagamentoSelecionadoFamilia;
    }

    public void setFormaPagamentoSelecionadoFamilia(Integer formaPagamentoSelecionadoFamilia) {
        this.formaPagamentoSelecionadoFamilia = formaPagamentoSelecionadoFamilia;
    }


    public HashMap<String, ItemGestaoNotaFamiliaTO> getNotasTitular() {
        return notasTitular;
    }

    public void setNotasTitular(HashMap<String, ItemGestaoNotaFamiliaTO> notasTitular) {
        this.notasTitular = notasTitular;
    }

    public boolean isPermiteAlterarDataEmissaoNFSe() {
        if (getEmpresaVO() != null) {
            return getEmpresaVO().isPermiteAlterarDataEmissaoNFSe();
        }
        return false;
    }

    public TipoRelatorioDF getTipoRelatorioDF() {
        return tipoRelatorioDF;
    }

    public void setTipoRelatorioDF(TipoRelatorioDF tipoRelatorioDF) {
        this.tipoRelatorioDF = tipoRelatorioDF;
    }

    public String getLabelPeriodo() {
        if (this.tipoRelatorioDF == null) {
            return "Período";
        } else if (this.tipoRelatorioDF.equals(TipoRelatorioDF.RECEITA)) {
            return "Por Receita";
        } else if (this.tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA)) {
            return "Por Competência";
        } else if (this.tipoRelatorioDF.equals(TipoRelatorioDF.FATURAMENTO_DE_CAIXA)) {
            return "Por Faturamento Recebido";
        } else if (this.tipoRelatorioDF.equals(TipoRelatorioDF.FATURAMENTO)) {
            // PARA FATURAMENTO A DESCRIÇÃO É DIFERENTE --- POIS O FATURAMENTO DO NFSE É DIFERENTE DO ZILLYONWEB -- TICKET #3493
            return "Por Faturamento Recebido Por Parcela";
        } else if (this.tipoRelatorioDF.equals(TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO)) {
            return "Por Competência Independente da Quitação";
        } else {
            return "Período " + this.tipoRelatorioDF.getDescricao();
        }
    }

    private void gerarArquivoLoteRPS(List<ItemGestaoNotasTO> notasEmitir, TipoGeracaoNotaFiscal tipoGeracaoNotaFiscal, Date dataEmissao) {
        try {
            setMensagemDetalhada("", "");
            setOnComplete("");
            Integer sequencialInicial = this.getEmpresaVO().getSequencialLoteRPS();
            RemessaVO remessaVO = preparaListaRemessaItemVO(notasEmitir);
            StringBuilder sb = new StringBuilder();
            if (tipoGeracaoNotaFiscal.equals(TipoGeracaoNotaFiscal.GERAR_XML_MARABA)) {
                ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                boolean usarNomeResponsavelNota = configuracaoSistemaVO.isUsarNomeResponsavelNota();
                Map<String, PessoaTO> pessoasResponsaveis = prepararPessoasResponsaveis(usarNomeResponsavelNota, notasEmitir);
                sb = LayoutRPSLoteMaraba.preencherArquivoLote(notasEmitir, this.getEmpresaVO(), pessoasResponsaveis, dataEmissao, getFacade().getZWFacade().getCon());
            } else if (tipoGeracaoNotaFiscal.equals(TipoGeracaoNotaFiscal.GERAR_XML)) {
                ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO = getFacade().getConfiguracaoNotaFiscal().consultarPorChavePrimaria(this.getEmpresaVO().getConfiguracaoNotaFiscalNFSe().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.getEmpresaVO().setConfiguracaoNotaFiscalNFSe(configuracaoNotaFiscalVO);
                LayoutRPSLoteSaoPaulo.preencherArquivoLote(remessaVO, this.getEmpresaVO());
                sb = LayoutRPSLoteSaoPaulo.prepareFile(remessaVO);
            }
            String path = this.getServletContext().getRealPath("relatorio") + File.separator + remessaVO.getNomeArquivoDownload();
            StringUtilities.saveToFile(sb, path);
            setOnComplete("abrirPopup('UpdateServlet?op=downloadfile&file=" + remessaVO.getNomeArquivoDownload() + "&mimetype=txt','LoteRPS', 640,480);");
            getFacade().getEmpresa().atualizarSequencialLoteRPS(this.getEmpresaVO().getSequencialLoteRPS(), this.getEmpresaVO().getCodigo());
            Integer sequencialFinal = this.getEmpresaVO().getSequencialLoteRPS();
            gravarLogGeracaoArquivoLoteRPS(sequencialInicial, sequencialFinal);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private RemessaVO preparaListaRemessaItemVO(List<ItemGestaoNotasTO> notasEmitir) throws Exception {
        RemessaVO remessaVO = new RemessaVO();

        remessaVO.setDataInicio(getDataInicio());
        remessaVO.setDataFim(getDataFim());

        remessaVO.setNomeArquivoDownload("RPSLote_" + StringUtilities.formatarCampoData(Calendario.hoje(), "yyyyMMdd") + Calendario.hoje().getTime() + ".txt");

        List<RemessaItemVO> listaRemessaItem = new ArrayList<RemessaItemVO>();

        for (ItemGestaoNotasTO nota : notasEmitir) {
            RemessaItemVO remessaItemVO = new RemessaItemVO();

            remessaItemVO.setValorRPS(nota.getValor());
            remessaItemVO.setDataEmissaoRPS(getDataEmissao());

            preencherInformacoesCadastroLoteRPS(nota, remessaItemVO);

            //DESCRIÇÃO
            String[] produtosPagos = nota.getProdutosPagos().split("\\|");
            String descricaoProdutosPagos = "";
            for (String produtoPago : produtosPagos) {
                if (!produtoPago.isEmpty()) {
                    String[] produtosPagosSplit = produtoPago.split(",");
                    String codigoProduto = produtosPagosSplit[0];
                    Integer codProduto = Integer.parseInt(codigoProduto);
                    MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(codProduto, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    descricaoProdutosPagos = movProdutoVO.getDescricao();
                    break;
                }
            }
            remessaItemVO.setDescricaoRPS(descricaoProdutosPagos);

            listaRemessaItem.add(remessaItemVO);
        }

        remessaVO.setListaItens(listaRemessaItem);

        return remessaVO;
    }

    private void preencherInformacoesCadastroLoteRPS(ItemGestaoNotasTO nota, RemessaItemVO remessaItemVO) throws Exception {
        ConfiguracaoSistemaVO configuracaoSistemaVO = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        boolean usarNomeResponsavelNota = configuracaoSistemaVO.isUsarNomeResponsavelNota();

        PessoaVO pessoaVO;
        if (!UteisValidacao.emptyNumber(nota.getCodCliente())) {
            ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(nota.getCodCliente(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        } else {
            ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(nota.getCodColaborador(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(colaboradorVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        }

        remessaItemVO.setPessoa(pessoaVO);
        remessaItemVO.setCpfCnpjRPS(Formatador.removerMascara(pessoaVO.getCfp()));

        if (pessoaVO.getPessoaJuridica()) {
            remessaItemVO.setCpfCnpjRPS(Formatador.removerMascara(pessoaVO.getCnpj()));

        } else if (pessoaVO.getDataNasc() != null && usarNomeResponsavelNota) {
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if ((Uteis.nrDiasEntreDatas(pessoaVO.getDataNasc(), Calendario.hoje()) < 18 * 365 + 4)) {
                if (clienteVO.getPessoaResponsavel() != null && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                    remessaItemVO.getPessoa().setNome(clienteVO.getPessoaResponsavel().getNome());
                    remessaItemVO.setCpfCnpjRPS(Formatador.removerMascara(clienteVO.getPessoaResponsavel().getCfp()));
                } else if (!clienteVO.getPessoa().getCfp().isEmpty() && !clienteVO.getPessoa().getNomeMae().isEmpty()) {
                    remessaItemVO.getPessoa().setNome(clienteVO.getPessoa().getNomeMae());
                    if (!UteisValidacao.emptyString(clienteVO.getPessoa().getCpfMae())) {
                        remessaItemVO.setCpfCnpjRPS(Formatador.removerMascara(clienteVO.getPessoa().getCpfMae()));
                    }
                }
            }

            if (clienteVO.getPessoa().isEmitirNomeTerceiro()) {
                remessaItemVO.getPessoa().setNome(clienteVO.getPessoa().getNomeTerceiro());
                remessaItemVO.setCpfCnpjRPS(Formatador.removerMascara(clienteVO.getPessoa().getCpfCNPJTerceiro()));
            }
        }
    }

    public boolean getPermiteGerarLoteRPSSaoPaulo() {
        if ((this.getEmpresaVO() != null && !UteisValidacao.emptyString(this.getEmpresaVO().getCidade().getNome())) && this.getEmpresaVO().isPermiteGerarArquivoLoteRPS()) {
            return this.getEmpresaVO().getCidade().getNomeSemAcento().equalsIgnoreCase("SAO PAULO");
        } else {
            return false;
        }
    }

    private void gravarLogGeracaoArquivoLoteRPS(Integer sequencialInicial, Integer sequencialFinal) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setNomeEntidade(nomeClasse.toUpperCase());
            obj.setNomeEntidadeDescricao(nomeClasse.toUpperCase());
            obj.setOperacao("GERAÇÃO DE ARQUIVO LOTE RPS");
            try {
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("PROCESSO DIÁRIO");
            }
            obj.setNomeCampo("NOTAS");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("SEQUENCIA RPS INICIO = " + sequencialInicial +
                    "\r\nSEQUENCIA RPS FINAL  = " + sequencialFinal +
                    "\r\nDATA INICIAL = " + Uteis.getData(getDataInicio()) +
                    "\r\nDATA FINAL = " + Uteis.getData(getDataFim()) +
                    "\r\nDATA EMISSÃO = " + Uteis.getData(getDataEmissao()));
            registrarLogObjetoVO(obj, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nomeClasse.toUpperCase(), 0, "ERRO AO GERAR LOG DA GESTÃO DE NOTAS - ARQUIVO LOTE RPS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public String getNumeroNotaManual() {
        /*if (numeroNotaManual == null) {
            numeroNotaManual = "";
        }*/
        return numeroNotaManual;
    }

    public void setNumeroNotaManual(String numeroNotaManual) {
        this.numeroNotaManual = numeroNotaManual;
    }

    public void preparaNotaManual() {
        setOnComplete("");
        limparMsg();
        setNumeroNotaManual("");
        setOnComplete("Richfaces.showModalPanel('modalNotaManual');");
    }

    public void preparaNotaManualTodas() {
        setOnComplete("");
        limparMsg();
        setNumeroNotaManual("");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            item.setRetorno("");
            if (!item.getNfseemitida()) {
                item.setSelecionado(true);
            }
        }
        calcularValorSelecionado();
        setOnComplete("Richfaces.showModalPanel('modalNotaManual');");
    }

    public void gerarNotaManual() {
        try {
            setOnComplete("");
            limparMsg();

            if (UteisValidacao.emptyString(getNumeroNotaManual())) {
                throw new Exception("Informe o número da nota manual.");
            }

            processarNotas(TipoGeracaoNotaFiscal.NOTA_MANUAL, getKey());

            setOnComplete("Richfaces.hideModalPanel('modalNotaManual');");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getNomeArquivoRelatorioNotaManual() {
        if (nomeArquivoRelatorioNotaManual == null) {
            nomeArquivoRelatorioNotaManual = "";
        }
        return nomeArquivoRelatorioNotaManual;
    }

    public void setNomeArquivoRelatorioNotaManual(String nomeArquivoRelatorioNotaManual) {
        this.nomeArquivoRelatorioNotaManual = nomeArquivoRelatorioNotaManual;
    }

    public void imprimirRelatorioNotasManuais() {
        try {
            Double valorTotal = 0.0;
            List<ItemGestaoNotasTO> listaImprimir = new ArrayList<ItemGestaoNotasTO>();
            for (ItemGestaoNotasTO item : getListaItensApresentar()) {
                if (!UteisValidacao.emptyString(item.getNrNotaManual())) {
                    listaImprimir.add(item);
                    valorTotal += item.getValor();
                }
            }

            if (UteisValidacao.emptyList(getListaItensApresentar())) {
                throw new Exception("Nenhuma Nota Manual emitida.");
            }

            boolean competencia = isEmissaoPorCompetencia();
            boolean faturamento = isEmissaoPorFaturamento();
            boolean faturamentoRecebido = isEmissaoPorFaturamentoRecebido();

            StringBuilder filtro = new StringBuilder();
            if (competencia) {
                filtro.append("Competência do mês: ").append(Uteis.getDataMesAnoConcatenado(getDataInicio()));
            } else if (faturamento) {
                filtro.append("Período Por Faturamento Recebido: ").append(Uteis.getData(getDataInicio())).append(" até ").append(Uteis.getData(getDataFim()));
            } else if (faturamentoRecebido) {
                filtro.append("Por Faturamento Recebido Por Parcela: ").append(Uteis.getData(getDataInicio())).append(" até ").append(Uteis.getData(getDataFim()));
            } else {
                filtro.append("Período de Receita: ").append(Uteis.getData(getDataInicio())).append(" até ").append(Uteis.getData(getDataFim()));
            }

            String design = "relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "NotaManual.jrxml";

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("valorTotalApresentar", Formatador.formatarValorMonetario(valorTotal));
            params.put("tipoRelatorio", "PDF");
            params.put("nomeRelatorio", "relatorioNotasManuais");
            params.put("tituloRelatorio", getTituloRelatorio());
            params.put("tipoImplementacao", "OBJETO");
            params.put("nomeDesignIReport", design);
            params.put("empresaVO", this.getEmpresaVO());
            params.put("nomeEmpresa", this.getEmpresaVO().getNome());
            params.put("SUBREPORT_DIR", "relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
            params.put("listaObjetos", listaImprimir);
            params.put("filtros", filtro.toString());
            params.put("nomeEmpresa", this.getEmpresaVO().getNome());
            params.put("tipoRelatorio", "PDF");
            params.put("usuario", getUsuarioLogado().getNome());
            params.put("enderecoEmpresa", this.getEmpresaVO().getEndereco());
            params.put("cidadeEmpresa", this.getEmpresaVO().getCidade().getNome());

            String arquivo = new SuperControleRelatorio().imprimirRelatorioNotasManuais(params);

            setNomeArquivoRelatorioNotaManual("abrirPopupPDFImpressao('relatorio/" + arquivo + "','', 800, 600);");
        } catch (Exception e) {
            setNomeArquivoRelatorioNotaManual("");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void preparaExcluirNotaManual() {
        setApresentarBtnExcluirNotas(false);
        setMsgExcluirNotas("");
        setOnComplete("");
        limparMsg();
        setNumeroNotaManual("");
        setOnComplete("Richfaces.showModalPanel('modalNotaManualExcluir');");
    }

    public void excluirNotaManual() {
        try {
            setOnComplete("");
            limparMsg();

            if (UteisValidacao.emptyString(getNumeroNotaManual())) {
                throw new Exception("Informe o número da nota manual para ser excluída.");
            }

            getFacade().getNFSeEmitida().excluirPorNrNotaManual(getNumeroNotaManual());

            setMsgExcluirNotas("Todas as notas foram excluídas!");
            setApresentarBtnExcluirNotas(false);
            setNumeroNotaManual("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarNotasExcluir() {
        try {
            setApresentarBtnExcluirNotas(false);
            setOnComplete("");
            limparMsg();

            if (UteisValidacao.emptyString(getNumeroNotaManual())) {
                throw new Exception("Informe o número da nota manual para ser excluída.");
            }

            Integer totalNotasExcluir = getFacade().getNFSeEmitida().consultaPorNrNotaManual(getNumeroNotaManual());

            if (UteisValidacao.emptyNumber(totalNotasExcluir)) {
                setMsgExcluirNotas("Nenhuma nota foi encontrada com o número \"" + getNumeroNotaManual() + "\"");
                setApresentarBtnExcluirNotas(false);
            } else {
                setMsgExcluirNotas("Foram encontradas " + totalNotasExcluir + " nota(s) com o número \"" + getNumeroNotaManual() + "\"");
                setApresentarBtnExcluirNotas(true);
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isApresentarBtnExcluirNotas() {
        return apresentarBtnExcluirNotas;
    }

    public void setApresentarBtnExcluirNotas(boolean apresentarBtnExcluirNotas) {
        this.apresentarBtnExcluirNotas = apresentarBtnExcluirNotas;
    }

    public String getMsgExcluirNotas() {
        if (msgExcluirNotas == null) {
            msgExcluirNotas = "";
        }
        return msgExcluirNotas;
    }

    public void setMsgExcluirNotas(String msgExcluirNotas) {
        this.msgExcluirNotas = msgExcluirNotas;
    }

    public String getTituloRelatorio() {
        if (tituloRelatorio == null) {
            tituloRelatorio = "";
        }
        return tituloRelatorio;
    }

    public void setTituloRelatorio(String tituloRelatorio) {
        this.tituloRelatorio = tituloRelatorio;
    }

    public void preparaImprimirRelatorioEmitidas() {
        setOnComplete("");
        limparMsg();
        setTituloRelatorio("Relatório de Notas Manuais Emitidas");
        setOnComplete("Richfaces.showModalPanel('modalNotaManualTitulo');");
    }

    public List<SituacaoNFSeEnum> getSituacaoNFSe() {
        if (situacaoNFSe == null) {
            situacaoNFSe = new ArrayList<SituacaoNFSeEnum>();
        }
        return situacaoNFSe;
    }

    public void setSituacaoNFSe(List<SituacaoNFSeEnum> situacaoNFSe) {
        this.situacaoNFSe = situacaoNFSe;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public void acaoMarcarTodos() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (isMarcarTodos()) {
                if (!item.isSelecionado() && !item.getNfseemitida()) {
                    item.setSelecionado(true);
                }
            } else {
                if (item.isSelecionado() && !item.getNfseemitida()) {
                    item.setSelecionado(false);
                }
            }

        }
        calcularValorSelecionado();
    }

    public Date getDataConsultaTotalizadorNFSe() {
        return dataConsultaTotalizadorNFSe;
    }

    public void setDataConsultaTotalizadorNFSe(Date dataConsultaTotalizadorNFSe) {
        this.dataConsultaTotalizadorNFSe = dataConsultaTotalizadorNFSe;
    }

    public List<ItemGestaoNotasTO> getTotalizadoresModuloNFSe() {
        if (totalizadoresModuloNFSe == null) {
            totalizadoresModuloNFSe = new ArrayList<ItemGestaoNotasTO>();
        }
        return totalizadoresModuloNFSe;
    }

    public void setTotalizadoresModuloNFSe(List<ItemGestaoNotasTO> totalizadoresModuloNFSe) {
        this.totalizadoresModuloNFSe = totalizadoresModuloNFSe;
    }

    public void consultarTotalizadoresNFSe() {
        try {
            limparMsg();
            setTotalizadoresModuloNFSe(new ArrayList<ItemGestaoNotasTO>());

            if (UteisValidacao.emptyNumber(this.getEmpresaVO().getCodigo())) {
                throw new Exception("Selecione a empresa.");
            }

            if (UteisValidacao.emptyString(empresaVO.getChaveNFSe())) {
                return;
            }

            if (dataConsultaTotalizadorNFSe == null) {
                throw new Exception("Informe uma data para consultar os totais do Módulo NFSe.");
            }

            String dataReferencia = Uteis.getDataAplicandoFormatacao(getDataConsultaTotalizadorNFSe(), "dd-MM-yyyy");
            String chaveNFSe = empresaVO.getChaveNFSe();

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");

            Map<String, String> corpo = new HashMap<String, String>();
            corpo.put("gestaoNotas", chaveNFSe);
            corpo.put("mes", dataReferencia);

            String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota";
            String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");

            JSONObject jsonObject = new JSONObject(executeRequest);
            JSONArray lista = new JSONArray(jsonObject.get("return").toString());
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                ItemGestaoNotasTO item = new ItemGestaoNotasTO();
                item.setNome(obj.getString("status"));
                item.setQuantidade(obj.getInt("qtdNotas"));
                item.setValor(obj.getDouble("valor"));
                getTotalizadoresModuloNFSe().add(item);
            }
        } catch (Exception e) {
            setTotalizadoresModuloNFSe(new ArrayList<ItemGestaoNotasTO>());
            montarErro(e);
        }
    }

    public void desvincularNota() {
        try {
            ItemGestaoNotasTO obj = (ItemGestaoNotasTO) request().getAttribute("item");
            StatusNotaEnum statusNotaEnum = getFacade().getLoteNFSe().retornarStatus(obj.getRps());
            if (statusNotaEnum.isPodeEstornar()) {

                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Content-Type", "application/x-www-form-urlencoded");

                Map<String, String> corpo = new HashMap<String, String>();
                corpo.put("desvincular", obj.getRps().toString());

                String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota";
                String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
                JSONObject retornoJSON = new JSONObject(executeRequest);
                if (!retornoJSON.has("return")) {
                    throw new Exception("Erro ao tentar desvincular nota.");
                }

                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorRPS(obj.getRps());
                if (nfSeEmitidaVO != null) {
                    getFacade().getNFSeEmitida().excluirComLog(nfSeEmitidaVO, getUsuarioLogado(), ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL);
                }
                montarSucesso("Nota desvinculada. Status: " + statusNotaEnum.getDescricao().toUpperCase());
                obj.setRps(null);
                obj.setNfseemitida(false);
            } else {
                throw new Exception("Nota não pode ser desvinculada, nota está com status: " + statusNotaEnum.getDescricao().toUpperCase());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public ItemGestaoNotasTO getAguardandoEnvio() {
        return aguardandoEnvio;
    }

    public void setAguardandoEnvio(ItemGestaoNotasTO aguardandoEnvio) {
        this.aguardandoEnvio = aguardandoEnvio;
    }

    public List<ItemGestaoNotasTO> getListaItensDiferencaValores() {
        return listaItensDiferencaValores;
    }

    public void setListaItensDiferencaValores(List<ItemGestaoNotasTO> listaItensDiferencaValores) {
        this.listaItensDiferencaValores = listaItensDiferencaValores;
    }

    public List<ItemGestaoNotasTO> getListaItensExcluidos() {
        if (listaItensExcluidos == null) {
            listaItensExcluidos = new ArrayList<ItemGestaoNotasTO>();
        }
        return listaItensExcluidos;
    }

    public void setListaItensExcluidos(List<ItemGestaoNotasTO> listaItensExcluidos) {
        this.listaItensExcluidos = listaItensExcluidos;
    }

    public ItemGestaoNotasTO getNotasExcluidas() {
        return notasExcluidas;
    }

    public void setNotasExcluidas(ItemGestaoNotasTO notasExcluidas) {
        this.notasExcluidas = notasExcluidas;
    }

    public List<ItemGestaoNotasTO> getTotalTipoFormaPg() {
        return totalTipoFormaPg;
    }

    public void setTotalTipoFormaPg(List<ItemGestaoNotasTO> totalTipoFormaPg) {
        this.totalTipoFormaPg = totalTipoFormaPg;
    }

    private String obterDescricaoTipoFormaPagamento(String sigla) {
        if (sigla == null) {
            return "";
        }

        if (sigla.equals("AV")) {
            return "Dinheiro";
        }

        if (sigla.equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
            //DEVE SER AJUSTADO QUANDO ESTIVER OUTRO PARCEIRO DEVE SER ALTERADO FUTURAMENTE
            return TipoParceiroEnum.DOTZ.getNome();
        }

        TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla(sigla);
        if (tipoFormaPagto != null) {
            return tipoFormaPagto.getDescricao();
        } else {
            return "";
        }
    }

    public List<ItemGestaoNotasTO> getListaItensOutrosMeses() {
        if (listaItensOutrosMeses == null) {
            listaItensOutrosMeses = new ArrayList<ItemGestaoNotasTO>();
        }
        return listaItensOutrosMeses;
    }

    public void setListaItensOutrosMeses(List<ItemGestaoNotasTO> listaItensOutrosMeses) {
        this.listaItensOutrosMeses = listaItensOutrosMeses;
    }

    public ItemGestaoNotasTO getEmitidoDeOutraForma() {
        return emitidoDeOutraForma;
    }

    public void setEmitidoDeOutraForma(ItemGestaoNotasTO emitidoDeOutraForma) {
        this.emitidoDeOutraForma = emitidoDeOutraForma;
    }

    public ItemGestaoNotasTO getEmitidoGeral() {
        return emitidoGeral;
    }

    public void setEmitidoGeral(ItemGestaoNotasTO emitidoGeral) {
        this.emitidoGeral = emitidoGeral;
    }

    public List<ItemGestaoNotasTO> getListaItensApresentarGeral() {
        if (listaItensApresentarGeral == null) {
            listaItensApresentarGeral = new ArrayList<ItemGestaoNotasTO>();
        }
        return listaItensApresentarGeral;
    }

    public void setListaItensApresentarGeral(List<ItemGestaoNotasTO> listaItensApresentarGeral) {
        this.listaItensApresentarGeral = listaItensApresentarGeral;
    }

    public void processarTipoVisualizacao() throws Exception {
        if (UteisValidacao.emptyString(getTipoVisualizacao())) {
            setListaItensApresentar(getListaItensApresentarGeral());
        } else {

            boolean emitidas = getTipoVisualizacao().equals("EM");
            boolean naoEmitidas = getTipoVisualizacao().equals("NE");

            List<ItemGestaoNotasTO> listaFiltrada = new ArrayList<ItemGestaoNotasTO>();
            for (ItemGestaoNotasTO item : getListaItensApresentarGeral()) {
                if (emitidas && item.getNfseemitida()) {
                    listaFiltrada.add(item);
                } else if (naoEmitidas && !item.getNfseemitida()) {
                    listaFiltrada.add(item);
                }
            }
            setListaItensApresentar(listaFiltrada);
        }
        totalizar();
    }

    public List<SelectItem> getSelectItemsTipoVisualizacao() {
        List<SelectItem> retorno = new ArrayList<SelectItem>();
        retorno.add(new SelectItem("", "TODAS"));
        retorno.add(new SelectItem("EM", "EMITIDAS"));
        retorno.add(new SelectItem("NE", "NÃO EMITIDAS"));
        return retorno;
    }

    public String getTipoVisualizacao() {
        if (tipoVisualizacao == null) {
            tipoVisualizacao = "";
        }
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(String tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }

    public void limparListas() throws Exception {
        setListaItens(new ArrayList<ItemGestaoNotasTO>());
        setListaItensApresentar(new ArrayList<ItemGestaoNotasTO>());
        setListaItensApresentarGeral(new ArrayList<ItemGestaoNotasTO>());
        totalizar();
    }

    public List<PlanoVO> getPlanos() {
        if (planos == null) {
            planos = new ArrayList<PlanoVO>();
        }
        return planos;
    }

    public void setPlanos(List<PlanoVO> planos) {
        this.planos = planos;
    }

    public String getQtdPlanosSelecionados() {
        if (qtdPlanosSelecionados == null) {
            qtdPlanosSelecionados = "";
        }
        return qtdPlanosSelecionados;
    }

    public void setQtdPlanosSelecionados(String qtdPlanosSelecionados) {
        this.qtdPlanosSelecionados = qtdPlanosSelecionados;
    }

    public void carregarListaPlanos() {
        try {
            setQtdPlanosSelecionados("");
            setSelecionarTodosPlanos(false);
            setPlanos(new ArrayList<>());
            if (getEmpresaVO() != null && !UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
                setPlanos(getFacade().getPlano().consultarPorCodigoEmpresa(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAO_NOTAS));
            } else {
                setPlanos(getFacade().getPlano().consultarTodos(Calendario.hoje(), Uteis.NIVELMONTARDADOS_GESTAO_NOTAS));
            }
            Ordenacao.ordenarLista(getPlanos(), "descricao");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void calcularPlanosSelecionados() {
        try {
            setQtdPlanosSelecionados("");
            Integer qtd = 0;
            for (PlanoVO planoVO : getPlanos()) {
                if (planoVO.isSelecionado()) {
                    qtd++;
                }
            }

            if (qtd == getPlanos().size()) {
                setSelecionarTodosPlanos(true);
            } else {
                setSelecionarTodosPlanos(false);
            }

            if (UteisValidacao.emptyNumber(qtd)) {
                setQtdPlanosSelecionados(" (Nenhum plano selecionado.)");
            } else if (qtd > 1) {
                setQtdPlanosSelecionados(" (" + qtd + " planos selecionados)");
            } else {
                setQtdPlanosSelecionados(" (" + qtd + " plano selecionado)");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void marcarTodosPlanos() {
        for (PlanoVO planoVO : getPlanos()) {
            planoVO.setSelecionado(isSelecionarTodosPlanos());
        }
        calcularPlanosSelecionados();
    }

    private List<PlanoVO> obterListaPlanosSelecionados(boolean familia) {

        if (familia) { //n?o utilizado para plano familia
            return new ArrayList<PlanoVO>();
        }

        List<PlanoVO> retorno = new ArrayList<PlanoVO>();
        for (PlanoVO planoVO : getPlanos()) {
            if (planoVO.isSelecionado()) {
                retorno.add(planoVO);
            }
        }
        return retorno;
    }

    public boolean isSelecionarTodosPlanos() {
        return selecionarTodosPlanos;
    }

    public void setSelecionarTodosPlanos(boolean selecionarTodosPlanos) {
        this.selecionarTodosPlanos = selecionarTodosPlanos;
    }

    public ConfiguracaoNotaFiscalVO getConfigNotaFiscalFamilia() {
        if (configNotaFiscalFamilia == null) {
            configNotaFiscalFamilia = new ConfiguracaoNotaFiscalVO();
        }
        return configNotaFiscalFamilia;
    }

    public void setConfigNotaFiscalFamilia(ConfiguracaoNotaFiscalVO configNotaFiscalFamilia) {
        this.configNotaFiscalFamilia = configNotaFiscalFamilia;
    }

    public List<SelectItem> getListaConfiguracaoNotaFamilia() {
        if (listaConfiguracaoNotaFamilia == null) {
            listaConfiguracaoNotaFamilia = new ArrayList<SelectItem>();
        }
        return listaConfiguracaoNotaFamilia;
    }

    public void setListaConfiguracaoNotaFamilia(List<SelectItem> listaConfiguracaoNotaFamilia) {
        this.listaConfiguracaoNotaFamilia = listaConfiguracaoNotaFamilia;
    }

    public void montarListaConfiguracaoNotaFamilia() {
        try {
            setConfigNotaFiscalFamilia(new ConfiguracaoNotaFiscalVO());
            setListaConfiguracaoNotaFamilia(new ArrayList<SelectItem>());
            List<ConfiguracaoNotaFiscalVO> lista = getFacade().getConfiguracaoNotaFiscal().consultarConfiguracaoNotaFiscal(getEmpresaVO().getCodigo(),
                    new Integer[]{TipoNotaFiscalEnum.NFSE.getCodigo()}, true, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (lista.size() > 1) {
                getListaConfiguracaoNotaFamilia().add(new SelectItem(0, ""));
            }
            for (ConfiguracaoNotaFiscalVO config : lista) {
                getListaConfiguracaoNotaFamilia().add(new SelectItem(config.getCodigo(), config.getDescricao()));
            }
            if (lista.size() == 1) {
                setConfigNotaFiscalFamilia(lista.get(0));
            }
        } catch (Exception ex) {
            setConfigNotaFiscalFamilia(new ConfiguracaoNotaFiscalVO());
            setListaConfiguracaoNotaFamilia(new ArrayList<SelectItem>());
        }
    }

    public void excluirNotaFiscalFamilia() {
        try {
            ItemGestaoNotaFamiliaTO obj = (ItemGestaoNotaFamiliaTO) request().getAttribute("reciboFamilia");
            if (!UteisValidacao.emptyNumber(obj.getSequencialFamilia()) && !UteisValidacao.emptyNumber(obj.getCodNotaFiscal())) {

                getFacade().getNFSeEmitida().excluirComLogFamiliaEnotas(obj.getCodNotaFiscal(), obj.getSequencialFamilia(), getUsuarioLogado());

                for (ItemGestaoNotasTO item : obj.getReciboPagamentoVOs()) {
                    item.setSequencialFamilia(null);
                    item.setNfseemitida(false);
                    item.setCodNotaFiscal(0);
                    item.setNotaFiscalVO(new NotaFiscalVO());
                }

                obj.setSequencialFamilia(null);
                obj.setNfseemitida(false);
                obj.setCodNotaFiscal(0);
                obj.setNotaFiscalVO(new NotaFiscalVO());
                montarSucessoGrowl("Nota excluída com sucesso.");
            } else {
                throw new Exception("Nota não pode ser excluida.");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public Boolean getFormasInativas() {
        return formasInativas;
    }

    public void setFormasInativas(Boolean formasInativas) {
        this.formasInativas = formasInativas;
    }

    public void mudarIncluirInativas(){
        try {
            formasInativas = !formasInativas;
            carregarListSelectItemsFormaDePagamento();
        }catch (Exception e) {
            Uteis.logar(e, GestaoNotasControle.class);
        }

    }

    public void selecionarItensNotaFiscalFamilia(ActionEvent evt) {
        try {
            ItemGestaoNotaFamiliaTO obj = (ItemGestaoNotaFamiliaTO) request().getAttribute("reciboFamilia");
            for (ItemGestaoNotasTO itemNota : obj.getReciboPagamentoVOs()) {
                itemNota.setSelecionado(obj.isSelecionado());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void acaoMarcarTodosExcluir() {
        for (ItemGestaoNotasTO obj : getListaItensApresentar()) {
            if (!UteisValidacao.emptyNumber(obj.getCodNFSeEmitida()) && !UteisValidacao.emptyNumber(obj.getCodNotaFiscal()) && obj.isApresentarExcluirNotaFiscalNFSe()) {
                obj.setSelecionadoExcluir(isMarcarTodosExcluir());
            }
        }
    }

    public void excluirNotasFiscais() {
        try {

            boolean notaSelecionada = false;
            for (ItemGestaoNotasTO obj : getListaItensApresentar()) {
                if (obj.isSelecionadoExcluir()) {
                    notaSelecionada = true;
                    break;
                }
            }

            if (!notaSelecionada) {
                throw new Exception("Nenhuma nota selecionada para excluir.");
            }


            int qtdExcluiu = 0;
            for (ItemGestaoNotasTO obj : getListaItensApresentar()) {
                if (obj.isSelecionadoExcluir() &&
                        !UteisValidacao.emptyNumber(obj.getCodNFSeEmitida()) &&
                        !UteisValidacao.emptyNumber(obj.getCodNotaFiscal()) &&
                        obj.isApresentarExcluirNotaFiscalNFSe()) {

                    getFacade().getNFSeEmitida().excluirComLogEnotas(obj.getCodNotaFiscal(), new NFSeEmitidaVO(obj.getCodNFSeEmitida()), getUsuarioLogado());

                    obj.setRps(null);
                    obj.setNfseemitida(false);
                    obj.setCodNotaFiscal(0);
                    obj.setNotaFiscalVO(new NotaFiscalVO());
                    qtdExcluiu++;
                }
            }


            if (UteisValidacao.emptyNumber(qtdExcluiu)) {
                throw new Exception("Nenhuma nota excluída.");
            }

            montarLogAoDesvincularNota(getListaItensApresentar());
            montarSucessoGrowl("Notas excluídas com sucesso. Total de " + qtdExcluiu + " nota(s) excluídas.");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isMarcarTodosExcluir() {
        return marcarTodosExcluir;
    }

    public void setMarcarTodosExcluir(boolean marcarTodosExcluir) {
        this.marcarTodosExcluir = marcarTodosExcluir;
    }

    public void gerarArquivoLoteRPSMaraba() {
        try {
            processarNotas(TipoGeracaoNotaFiscal.GERAR_XML_MARABA, getKey());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void gerarArquivoLoteRPSTodasMaraba() {
        try {
            for (ItemGestaoNotasTO item : getListaItensApresentar()) {
                item.setRetorno("");
                if (!item.getNfseemitida()) {
                    item.setSelecionado(true);
                }
            }
            processarNotas(TipoGeracaoNotaFiscal.GERAR_XML_MARABA, getKey());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private HashMap<String, PessoaTO> prepararPessoasResponsaveis(boolean usarNomeResponsavelNaNota, List<ItemGestaoNotasTO> notasEmitir) throws Exception {
        HashMap<String, PessoaTO> responsaveis = new HashMap<String, PessoaTO>();

        for (ItemGestaoNotasTO nota : notasEmitir) {
            String chave;
            PessoaVO pessoaVO;
            if (!UteisValidacao.emptyNumber(nota.getCodCliente())) {
                chave = "CLI" + nota.getCodCliente();
                PessoaTO pessoaTO = responsaveis.get(chave);
                if (pessoaTO != null) {
                    continue;
                }
                ClienteVO clienteVO = getFacade().getCliente().consultarPorChavePrimaria(nota.getCodCliente(), Uteis.NIVELMONTARDADOS_MINIMOS);
                pessoaVO = clienteVO.getPessoa();
            } else {
                chave = "COL" + nota.getCodColaborador();
                PessoaTO pessoaTO = responsaveis.get(chave);
                if (pessoaTO != null) {
                    continue;
                }
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(nota.getCodColaborador(), Uteis.NIVELMONTARDADOS_MINIMOS);
                pessoaVO = colaboradorVO.getPessoa();
            }

            pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            PessoaTO pessoaTO = PessoaTO.preencheComPessoaVO(pessoaVO);
            pessoaTO.setCpf(Formatador.removerMascara(pessoaVO.getCfp()));

            if (pessoaVO.getPessoaJuridica()) {
                pessoaTO.setCpf(Formatador.removerMascara(pessoaVO.getCnpj()));
            } else if (pessoaVO.getDataNasc() != null && usarNomeResponsavelNaNota && (Uteis.nrDiasEntreDatas(pessoaVO.getDataNasc(), Calendario.hoje()) < 18 * 365 + 4)) {
                ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                if (clienteVO.getPessoaResponsavel() != null && !clienteVO.getPessoaResponsavel().getCodigo().equals(0)) {
                    pessoaTO.setNomeCompleto(clienteVO.getPessoaResponsavel().getNome());
                    pessoaTO.setCpf(Formatador.removerMascara(clienteVO.getPessoaResponsavel().getCfp()));
                } else if (!clienteVO.getPessoa().getCfp().isEmpty() && !clienteVO.getPessoa().getNomeMae().isEmpty()) {
                    pessoaTO.setNomeCompleto(clienteVO.getPessoa().getNomeMae());
                    pessoaTO.setCpf(Formatador.removerMascara(clienteVO.getPessoa().getCfp()));
                }
            }

            responsaveis.put(chave, pessoaTO);
        }
        return responsaveis;
    }

    public boolean getPermiteGerarLoteRPS() {
        return getPermiteGerarLoteRPSSaoPaulo() || getPermiteGerarLoteRPSMaraba();
    }

    public boolean getPermiteGerarLoteRPSMaraba() {
        if ((this.getEmpresaVO() != null && !UteisValidacao.emptyString(this.getEmpresaVO().getCidade().getNome())) && this.getEmpresaVO().isPermiteGerarArquivoLoteRPS()) {
            return this.getEmpresaVO().getCidade().getNomeSemAcento().equalsIgnoreCase("MARABA");
        } else {
            return false;
        }
    }

    public boolean isExisteNotaDelphi() {
        return existeNotaDelphi;
    }

    public void setExisteNotaDelphi(boolean existeNotaDelphi) {
        this.existeNotaDelphi = existeNotaDelphi;
    }
}

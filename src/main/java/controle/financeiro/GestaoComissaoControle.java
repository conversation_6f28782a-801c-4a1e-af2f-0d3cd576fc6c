/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.DiaSemana;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.view.DataScrollerControle;
import controle.basico.ClienteControle;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.financeiro.ComissaoLinearVO;
import negocio.comuns.financeiro.ComissaoTree;
import negocio.comuns.financeiro.ComissaoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.arquitetura.GeradorRelatorio;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.DetalhamentoLancamentoDF_VO;
import relatorio.negocio.jdbc.financeiro.ContratoModalidadePercentual;
import relatorio.negocio.jdbc.financeiro.GestaoComissaoRel;

import javax.faces.model.SelectItem;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Array;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

/**
 * <AUTHOR>
 */
public class GestaoComissaoControle extends SuperControleRelatorio {

    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private Integer codContrato = 0;
    private List<ComissaoVO> listaObjetos = new ArrayList<ComissaoVO>();
    private GestaoComissaoRel servicoComissao;
    private boolean somenteComTurmas = true;
    private List<ComissaoTree> listaTree = new ArrayList<ComissaoTree>();
    private List<ComissaoTree> listaTreeBkp = new ArrayList<ComissaoTree>();
    private List listaSintenticoProfessor = new ArrayList();
    private List<ColaboradorVO> filtros = new ArrayList<ColaboradorVO>();
    private Integer cliente = 0;
    private boolean ehCompetencia = false;
    private Boolean openConsulta = true;
    private Boolean openDatas = false;
    private Boolean openFiltros = false;
    private String formatoDoCalendario = "dd/MM/yyyy";
    private String tipoConsulta = "";
    private String colaboradoresSel = "";
    private String somenteAlunosTurma = "SIM";
    private String nomeConsulta = "";

    private Integer professorSelecionado = 0;
    private List<SelectItem> listaProfessores = new ArrayList<SelectItem>();
    private List<ComissaoVO> listaObjetosAuxiliar;

    public GestaoComissaoControle() {
        try {
            servicoComissao = new GestaoComissaoRel();
            consultarProfessores();
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoComissaoControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void consultarProfessores() throws Exception {
        List<ColaboradorVO> professores = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(
                TipoColaboradorEnum.PROFESSOR, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
        listaProfessores = new ArrayList<SelectItem>();

        listaProfessores.add(new SelectItem(0, ""));
        for (ColaboradorVO colaborador : professores) {
            listaProfessores.add(new SelectItem(colaborador.getCodigo(), colaborador.getPessoa_Apresentar()));
        }

        Ordenacao.ordenarLista(listaProfessores, "label");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public void filtrarPorColaborador() {
        listaTree = new ArrayList<ComissaoTree>();
        List<Integer> codigosSelecionados = obterCodigoColaboradoresSelecionados();
        if (codigosSelecionados.isEmpty()) {
            listaTree.addAll(listaTreeBkp);
        } else {
            for (ComissaoTree ct : listaTreeBkp) {
                if (codigosSelecionados.contains(ct.getChavePrimariaColaborador())) {
                    listaTree.add(ct);
                }
            }
            Ordenacao.ordenarLista(listaTree, "codigoAgrupador");
        }
    }

    public void montarTreeView() {

        int cont = 1;
        int contMod = 1;
        int contTur = 1;
        listaTree = new ArrayList<ComissaoTree>();
        listaTreeBkp = new ArrayList<ComissaoTree>();
        filtros = new ArrayList<ColaboradorVO>();
        //iterar na lista de objetos gerada pela consulta
        for (ComissaoVO comissao : listaObjetos) {
            //iterar na lista de lançamentos do objeto
            for (DetalhamentoLancamentoDF_VO detalhamento : comissao.getListaDetalheLancamento()) {
                //iterar nas modalidades do lançamento
                for (ContratoModalidadePercentual conMod : detalhamento.getListaContratoModalidade()) {
                    //ordenar a lista de turmas pelo horario da turma
                    Ordenacao.ordenarLista(conMod.getMatriculasDesteContratoModalidade(), "hrInicio");
                    //verificar o quanto vale cada vez na semana que o aluno frequenta
                    for (MatriculaAlunoHorarioTurmaVO maht : conMod.getMatriculasDesteContratoModalidade()) {
                        if (getProfessorSelecionado() != 0) {
                            if (!getProfessorSelecionado().equals(maht.getHorarioTurma().getProfessor().getCodigo())) {
                                continue;
                            }
                        }
                        //professor
                        Double valor = (maht.getValor() > detalhamento.getTotalPagoPlano()) && (!ehCompetencia)  ? 
                            detalhamento.getTotalPagoPlano() : maht.getValor() ;
                        
                        cont++;
                        ComissaoTree professor = inserirNo(cont, ComissaoTree.COLABORADOR, "",
                                maht.getHorarioTurma().getProfessor().getPessoa().getNome(),
                                maht.getHorarioTurma().getProfessor().getCodigo(), maht.getHorarioTurma().getProfessor().getCodigo(),
                                valor, detalhamento, comissao, maht);
                        //modalidade
                        ComissaoTree modalidade = inserirNo(contMod, ComissaoTree.MODALIDADE, professor.getCodigoAgrupador(),
                                conMod.getNomeModalidade(),
                                conMod.getCodigoModalidade(), maht.getHorarioTurma().getProfessor().getCodigo(), valor,
                                detalhamento, comissao, maht);
                        contMod++;

                        //turma
                        ComissaoTree turma = inserirNo(contTur, ComissaoTree.TURMA, modalidade.getCodigoAgrupador(),
                                maht.getHorarioTurma().getIdentificadorTurma(),
                                (maht.getHorarioTurma().getIdentificadorTurma()+conMod.getNomeModalidade()).hashCode(),
                                maht.getHorarioTurma().getProfessor().getCodigo(), valor,
                                detalhamento, comissao, maht);

                        //horario
                        ComissaoTree horario = inserirNo(contTur, ComissaoTree.HORARIO, turma.getCodigoAgrupador(),
                                maht.getHorarioTurma().getHoraInicial() + "/" + maht.getHorarioTurma().getHoraFinal(),
                                (maht.getHorarioTurma().getHoraInicial() + "/" + maht.getHorarioTurma().getHoraFinal()).hashCode() + turma.getChavePrimaria(),
                                maht.getHorarioTurma().getProfessor().getCodigo(), valor,
                                detalhamento, comissao, maht);

                        //pessoa
                        ComissaoTree pessoa = inserirNo(contTur, ComissaoTree.PESSOA, horario.getCodigoAgrupador(),
                                maht.getPessoa().getNome() + " - " + comissao.getCliente().getSituacao_Apresentar() + " / " + comissao.getCliente().getSituacaoContrato_Apresentar(), (maht.getHorarioTurma().getHoraInicial() + "/" + maht.getHorarioTurma().getHoraFinal() + maht.getPessoa().getCodigo()+maht.getHorarioTurma().getTurma()).hashCode(),
                                maht.getHorarioTurma().getProfessor().getCodigo(), valor,
                                detalhamento, comissao, maht);
                        pessoa.setCliente(comissao.getCliente().getCodigo());
                        //vezes na semana será o maior número de vezes na semana da lista de alunos
                        contTur++;

                        DiaSemana dia = DiaSemana.getDiaSemana(maht.getHorarioTurma().getDiaSemana());
                        addDiaSemana(professor, dia);
                        addDiaSemana(modalidade, dia);
                        addDiaSemana(turma, dia);
                        addDiaSemana(horario, dia);
                        addDiaSemana(pessoa, dia);

                        if (pessoa.getDiasSemana().size() == 1) {
                            professor.setQtdAluno(1 + professor.getQtdAluno());
                            modalidade.setQtdAluno(1 + modalidade.getQtdAluno());
                            turma.setQtdAluno(1 + turma.getQtdAluno());
                            horario.setQtdAluno(1 + horario.getQtdAluno());
                            pessoa.setQtdAluno(1);
                        }

                        atualizarValorPorcComissao(professor, modalidade, turma, horario, pessoa, maht, maht.getValor());
                    }
                }
            }
        }
        Ordenacao.ordenarLista(listaTree, "codigoAgrupador");
        listaTreeBkp.addAll(listaTree);
        listaTree = new ArrayList<ComissaoTree>();
        Map<String, String> nodes = new HashMap<String, String>();

        listaTree.addAll(reOrdenarCodigos(nodes, getFromNivel(listaTreeBkp, ComissaoTree.COLABORADOR)));
        listaTree.addAll(reOrdenarCodigos(nodes, getFromNivel(listaTreeBkp, ComissaoTree.MODALIDADE)));
        listaTree.addAll(reOrdenarCodigos(nodes, getFromNivel(listaTreeBkp, ComissaoTree.TURMA)));
        listaTree.addAll(reOrdenarCodigos(nodes, getFromNivel(listaTreeBkp, ComissaoTree.HORARIO)));
        listaTree.addAll(reOrdenarCodigos(nodes, getFromNivel(listaTreeBkp, ComissaoTree.PESSOA)));

        Ordenacao.ordenarLista(listaTree, "codigoAgrupador");
    }

    private List<ComissaoTree> getFromNivel(List<ComissaoTree> cts, int nivel) {
        List<ComissaoTree> rec = new ArrayList<ComissaoTree>();
        for (ComissaoTree ct : cts) {
            if (nivel == ct.getTipoNo()) {
                rec.add(ct);
            }
        }
        Ordenacao.ordenarLista(rec, "nome");
        return rec;
    }

    private List<ComissaoTree> reOrdenarCodigos(Map<String, String> nodes, List<ComissaoTree> cts) {
        for (ComissaoTree ct : cts) {
            nodes.put(ct.getCodigoAgrupador(), gerarCodigoNo(nodes.get(ct.getCodigoPai()), (cts.indexOf(ct) + 1)));
            ct.setCodigoAgrupador(nodes.get(ct.getCodigoAgrupador()));
        }
        return cts;
    }

    private void addDiaSemana(ComissaoTree noComissao, DiaSemana dia) {
        if (dia != null && !noComissao.getDiasSemana().contains(dia)) {
            noComissao.getDiasSemana().add(dia);
        }
    }

    public ComissaoTree inserirNo(int cont, int tipoNo, String agregador, String nome, int chave, int chaveColaborador,
                                  double valorPago, DetalhamentoLancamentoDF_VO detalhePagamento, ComissaoVO comissao, MatriculaAlunoHorarioTurmaVO maht) {
        ComissaoTree noComissao = new ComissaoTree();
        noComissao.setCodigoAgrupador(gerarCodigoNo(agregador, cont));

        noComissao.setNome(nome);
        noComissao.setChavePrimaria(chave);
        noComissao.setChavePrimariaColaborador(chaveColaborador);
        noComissao.setTipoNo(tipoNo);
        noComissao.setMovPagamento(detalhePagamento.getMovPagamento());

        if (listaTree.contains(noComissao)) {
            noComissao = listaTree.get(listaTree.indexOf(noComissao));
        } else {
            listaTree.add(noComissao);
            if (tipoNo == ComissaoTree.COLABORADOR) {
                ColaboradorVO colaborador = new ColaboradorVO();
                colaborador.setCodigo(chave);
                colaborador.getPessoa().setNome(nome);
                filtros.add(colaborador);
            }

            if( tipoNo == ComissaoTree.PESSOA){
                noComissao.setMovPagamento(detalhePagamento.getMovPagamento());
                noComissao.setClienteVO(comissao.getCliente());
                noComissao.setMatriculaAluno(maht);
            }
        }
        noComissao.setValorPago(valorPago + noComissao.getValorPago());
        return noComissao;
    }

    /**
     * Verifica o valor a ser pago de comissão considerando que há porcentagem em cadastro de colaborador
     * para modalidade, turma e aluno.
     * Deve ser considerado o valor do percentual predominante considerado o preenchimento de cada porcentagem
     * Se estiver preenchido aluno considerar aluno, se não se estiver preenchido turma considerar turma,
     * se não se estiver preenchido modalidade considerar modalidade, se não considerar porcentagem do professor.
     *
     * @param professor
     * @param modalidade
     * @param turma
     * @param horarioTurma
     * @param pessoa
     * @param maht
     * @param valorPago
     */
    public void atualizarValorPorcComissao(ComissaoTree professor, ComissaoTree modalidade, ComissaoTree turma,
            ComissaoTree horarioTurma, ComissaoTree pessoa,
            MatriculaAlunoHorarioTurmaVO maht, Double valorPago) {
        //por padrão deixar apresentando o valor da porcentagem de comissão do professor mesmo sendo zerado
        if (!UteisValidacao.emptyNumber(maht.getHorarioTurma().getProfessor().getPorcComissao())) {
            professor.setTipoComissao("%");
            professor.setComissao(maht.getHorarioTurma().getProfessor().getPorcComissao());
        } else if (!UteisValidacao.emptyNumber(maht.getHorarioTurma().getProfessor().getValorComissao())) {
            professor.setTipoComissao("R$");
            professor.setComissao(maht.getHorarioTurma().getProfessor().getValorComissao());
        } else {
            professor.setTipoComissao("%");
            professor.setComissao(maht.getHorarioTurma().getProfessor().getPorcComissao());
        }

        //quando considera o percentual do aluno
        for (AlunoComissaoColaboradorVO alunoComissao : maht.getHorarioTurma().getProfessor().getListaAlunoComissaoColaboradorVOs()) {
            if (maht.getPessoa().getCodigo().intValue() == alunoComissao.getPessoaVO().getCodigo().intValue()) {
                if (!UteisValidacao.emptyNumber(alunoComissao.getPorcComissao())) {
                    atualizarValorComissaoComPorcPessoa(alunoComissao, valorPago, pessoa, horarioTurma, turma, modalidade, professor);
                } else if (!UteisValidacao.emptyNumber(alunoComissao.getValorComissao())) {
                    atualizarValorComissaoComValorPessoa(alunoComissao, valorPago, pessoa, horarioTurma, turma, modalidade, professor);
                }
                return;
            }
        }
        //quando considera o percentual da turma
        for (TurmaComissaoColaboradorVO turmaComissao : maht.getHorarioTurma().getProfessor().getListaTurmaComissaoColaboradorVO()) {
            if (maht.getHorarioTurma().getTurma().intValue() == turmaComissao.getTurma().getCodigo().intValue()) {
                if (!UteisValidacao.emptyNumber(turmaComissao.getPorcComissao())) {
                    atualizarValorComissaoPorPorcTurma(turmaComissao, valorPago, pessoa, horarioTurma, turma, modalidade, professor);
                } else if (!UteisValidacao.emptyNumber(turmaComissao.getValorComissao())) {
                    atualizarValorComissaoPorValorTurma(turmaComissao, valorPago, pessoa, horarioTurma, turma, modalidade, professor);
                }
                return;
            }
        }
        //quando considera o percentual da modalidade
        for (ModalidadeComissaoColaboradorVO modalidadeComissao : maht.getHorarioTurma().getProfessor().getListaModalidadesComissaoColaboradorVO()) {
            if (modalidade.getChavePrimaria() == modalidadeComissao.getModalidade().getCodigo()) {
                if (!UteisValidacao.emptyNumber(modalidadeComissao.getPorcComissao())) {
                    atualizarValorComissaoPorPorcModalidade(modalidadeComissao, valorPago, pessoa, horarioTurma, turma, modalidade, professor);
                } else if (!UteisValidacao.emptyNumber(modalidadeComissao.getValorComissao())) {
                    atualizarValorComissaoPorValorModalidade(modalidadeComissao, valorPago, pessoa, horarioTurma, turma, modalidade, professor);
                }
                return;
            }
        }
        //se não considerar o percentual do professor
        if (!UteisValidacao.emptyNumber(maht.getHorarioTurma().getProfessor().getPorcComissao())) {
            atualizarValorComissaoPorPorcProfessor(valorPago, pessoa, horarioTurma, turma, modalidade, professor, maht);
        } else if (!UteisValidacao.emptyNumber(maht.getHorarioTurma().getProfessor().getValorComissao())) {
            atualizarValorComissaoPorValorProfessor(valorPago, pessoa, horarioTurma, turma, modalidade, professor, maht);
        } else {
            atualizarValorComissaoPorPorcProfessor(valorPago, pessoa, horarioTurma, turma, modalidade, professor, maht);
        }
    }

    public void atualizarValorComissaoComPorcPessoa(AlunoComissaoColaboradorVO alunoComissao,
            Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
            ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor) {
        //o valor da comissão será o percentual do aluno vezes o valor pago
        Double valorComissao = alunoComissao.getPorcComissao() > 0.0 ? valorPago * (alunoComissao.getPorcComissao() / 100) : 0.0;
        //atualizando valor da comissão do professor com o aluno
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o horário da turma
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a turma
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a modalidade
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o professor
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
        //considerar o valor da comissão do aluno
        pessoa.setComissao(alunoComissao.getPorcComissao());
    }

    public void atualizarValorComissaoComValorPessoa(AlunoComissaoColaboradorVO alunoComissao,
                                                    Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
                                                    ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor) {
        //o valor da comissão será o percentual do aluno vezes o valor pago
        Double valorComissao = alunoComissao.getValorComissao();
        //atualizando valor da comissão do professor com o aluno
//        pessoa.setTipoComissao("R$");
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o horário da turma
//        horarioTurma.setTipoComissao("R$");
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a turma
//        turma.setTipoComissao("R$");
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a modalidade
//        modalidade.setTipoComissao("R$");
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o professor
        professor.setTipoComissao("R$");
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
        //considerar o valor da comissão do aluno
        pessoa.setTipoComissao("R$");
        pessoa.setComissao(alunoComissao.getValorComissao());
    }

    public void atualizarValorComissaoPorPorcTurma(TurmaComissaoColaboradorVO turmaComissao,
            Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
            ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor) {
        //o valor da comissão será o percentual da turma vezes o valor pago
        Double valorComissao = turmaComissao.getPorcComissao() > 0.0 ? valorPago * (turmaComissao.getPorcComissao() / 100) : 0.0;
        //atualizando o valor da comissão do professor em relação ao aluno
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o horário da turma
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a turma
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a modalidade
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o professor
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
        //considerar o valor da comissão da turma
        turma.setComissao(turmaComissao.getPorcComissao());
    }

    public void atualizarValorComissaoPorValorTurma(TurmaComissaoColaboradorVO turmaComissao,
                                                   Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
                                                   ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor) {
        //o valor da comissão será o percentual da turma vezes o valor pago
        Double valorComissao = turmaComissao.getValorComissao();
        //atualizando o valor da comissão do professor em relação ao aluno
//        pessoa.setTipoComissao("R$");
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o horário da turma
//        horarioTurma.setTipoComissao("R$");
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a turma
//        turma.setTipoComissao("R$");
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a modalidade
//        modalidade.setTipoComissao("R$");
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o professor
        professor.setTipoComissao("R$");
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
        //considerar o valor da comissão da turma
        turma.setTipoComissao("R$");
        turma.setComissao(turmaComissao.getValorComissao());
    }

    public void atualizarValorComissaoPorPorcModalidade(ModalidadeComissaoColaboradorVO modalidadeComissao,
            Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
            ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor) {
        //o valor da comissão será o percentual da modalidade vezes o valor pago
        Double valorComissao = modalidadeComissao.getPorcComissao() > 0.0 ? valorPago * (modalidadeComissao.getPorcComissao() / 100) : 0.0;

        //atualizando o valor da comissão do professor em relação ao aluno
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o horário da turma
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a turma
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a modalidade
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o professor
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
        //considerar o valor da comissão da turma
        modalidade.setComissao(modalidadeComissao.getPorcComissao());
    }

    public void atualizarValorComissaoPorValorModalidade(ModalidadeComissaoColaboradorVO modalidadeComissao,
                                                        Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
                                                        ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor) {
        //o valor da comissão será o percentual da modalidade vezes o valor pago
        Double valorComissao = modalidadeComissao.getValorComissao();

        //atualizando o valor da comissão do professor em relação ao aluno
//        pessoa.setTipoComissao("R$");
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o horário da turma
//        horarioTurma.setTipoComissao("R$");
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a turma
//        turma.setTipoComissao("R$");
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para a modalidade
//        modalidade.setTipoComissao("R$");
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //somando os valores das comissões anteriores para o professor
        professor.setTipoComissao("R$");
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
        //considerar o valor da comissão da turma
        modalidade.setTipoComissao("R$");
        modalidade.setComissao(modalidadeComissao.getValorComissao());
    }

    public void atualizarValorComissaoPorPorcProfessor(Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
            ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor, MatriculaAlunoHorarioTurmaVO maht) {
        //o valor da comissão será o percentual do professor vezes o valor pago
        Double valorComissao = (maht.getHorarioTurma().getProfessor().getPorcComissao() > 0.0
                ? valorPago * (maht.getHorarioTurma().getProfessor().getPorcComissao() / 100) : 0.0);
        //horário deve ser a soma dos horários de turma.
        horarioTurma.setValorComissao(horarioTurma.getValorComissao()
                + valorComissao);
        //valor da turma deve ser a soma do valor das turmas
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //modalidade deve ser a soma do valor total das modalidades
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //pessoa deve ser a soma total de pessoas
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //deve ser a soma dos valores da comissão do professor
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
    }

    public void atualizarValorComissaoPorValorProfessor(Double valorPago, ComissaoTree pessoa, ComissaoTree horarioTurma,
                                                       ComissaoTree turma, ComissaoTree modalidade, ComissaoTree professor, MatriculaAlunoHorarioTurmaVO maht) {
        //o valor da comissão será o percentual do professor vezes o valor pago
        Double valorComissao = maht.getHorarioTurma().getProfessor().getValorComissao();
        //horário deve ser a soma dos horários de turma.
//        horarioTurma.setTipoComissao("R$");
        horarioTurma.setValorComissao(horarioTurma.getValorComissao() + valorComissao);
        //valor da turma deve ser a soma do valor das turmas
//        turma.setTipoComissao("R$");
        turma.setValorComissao(turma.getValorComissao() + valorComissao);
        //modalidade deve ser a soma do valor total das modalidades
//        modalidade.setTipoComissao("R$");
        modalidade.setValorComissao(modalidade.getValorComissao() + valorComissao);
        //pessoa deve ser a soma total de pessoas
        pessoa.setTipoComissao("R$");
        pessoa.setValorComissao(pessoa.getValorComissao() + valorComissao);
        //deve ser a soma dos valores da comissão do professor
        professor.setTipoComissao("R$");
        professor.setValorComissao(professor.getValorComissao() + valorComissao);
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getDataFimApresentar() {
        if (ehCompetencia) {
            return Uteis.getDataAplicandoFormatacao(dataFim, "MM/yyyy");
        }
        return Uteis.getData(dataFim);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicioApresentar() {
        if (ehCompetencia) {
            return Uteis.getDataAplicandoFormatacao(dataInicio, "MM/yyyy");
        }
        return Uteis.getData(dataInicio);
    }

    public Integer getCodContrato() {
        return codContrato;
    }

    public void setCodContrato(Integer codContrato) {
        this.codContrato = codContrato;
    }

    public List<ComissaoVO> getListaObjetos() {
        return listaObjetos;
    }

    public void setListaObjetos(List<ComissaoVO> listaObjetos) {
        this.listaObjetos = listaObjetos;
    }

    public GestaoComissaoRel getServicoComissao() {
        return servicoComissao;
    }

    public void setServicoComissao(GestaoComissaoRel servicoComissao) {
        this.servicoComissao = servicoComissao;
    }

    public boolean getSomenteComTurmas() {
        return somenteComTurmas;
    }

    public List getListaSintenticoProfessor() {
        return listaSintenticoProfessor;
    }

    public void setListaSintenticoProfessor(List listaSintenticoProfessor) {
        this.listaSintenticoProfessor = listaSintenticoProfessor;
    }

    public void setSomenteComTurmas(boolean somenteComTurmas) {
        this.somenteComTurmas = somenteComTurmas;
    }

    public void consultarInformacoes() {
        try {
            limparMsg();
            if (dataInicio == null || dataFim == null) {
                throw new Exception("Os campos de data devem ser preenchidos.");
            }

            setListaObjetosAuxiliar(null);
            setMensagemDetalhada("", "");
            StringBuilder debug = new StringBuilder(String.format("Início Gestão Comissão por %s período de %s à %s",
                    new Object[]{ehCompetencia ? "Competência" : "Receita", Uteis.getData(dataInicio), Uteis.getData(dataFim)}));
            Uteis.logar(null, debug.toString());
            long ti = System.currentTimeMillis();
            colaboradoresSel = "";
            somenteAlunosTurma = (this.somenteComTurmas ? "Apenas Alunos em Turmas" : "");
            if (ehCompetencia) {
                tipoConsulta = "Por Competência";
                dataInicio = Uteis.obterPrimeiroDiaMes(dataInicio);
                dataFim = Uteis.obterUltimoDiaMes(dataFim);
                listaObjetos = servicoComissao.consultarPorCompetencia(getEmpresaLogado(), dataInicio, dataFim, this.somenteComTurmas);

            } else {
                tipoConsulta = "Por Compensação";
                listaObjetos = servicoComissao.consultar(getEmpresaLogado(), dataInicio, dataFim, this.somenteComTurmas);
            }
            long tf = System.currentTimeMillis();
            debug = new StringBuilder(String.format("Fim Gestão Comissão por %s período de %s à %s. Tempo Total: %s ms",
                    new Object[]{ehCompetencia ? "Competência" : "Receita", Uteis.getData(dataInicio), Uteis.getData(dataFim), tf - ti}));
            Uteis.logar(null, debug.toString());
            DataScrollerControle dataScroller = (DataScrollerControle) getControlador(DataScrollerControle.class.getSimpleName());
            dataScroller.setNrRows(10);

            if (listaObjetos.isEmpty()) {
                setMensagemDetalhada("Nenhum registro encontrado no período.");
            }
            super.setListaManipulavel((ArrayList) listaObjetos);

            montarTreeView();
            montarSinteticoPorProfessor();
        } catch (Exception ex) {
            ex.printStackTrace();
            setMensagemDetalhada(ex);
        }

    }


    public void montarSinteticoPorProfessor(){
        listaSintenticoProfessor = new ArrayList();
        List<ComissaoTree> professores =  getFromNivel(listaTreeBkp, ComissaoTree.COLABORADOR);
        List<ComissaoTree> modalidades = getFromNivel(listaTreeBkp, ComissaoTree.MODALIDADE);
        List<ComissaoTree> turmas = getFromNivel(listaTreeBkp, ComissaoTree.TURMA);
        List<ComissaoTree> horarios = getFromNivel(listaTreeBkp, ComissaoTree.HORARIO);
        List<ComissaoTree> alunos = getFromNivel(listaTreeBkp, ComissaoTree.PESSOA);

        /**
         * @todo extatir para metodo recursivo
         */
        for (ComissaoTree prof: professores) {
            for (ComissaoTree mod: modalidades){
                if(mod.getCodigoPai().equals(prof.getCodigoAgrupador())){
                    for (ComissaoTree turma:turmas) {
                        if(turma.getCodigoPai().equals(mod.getCodigoAgrupador())){
                            for (ComissaoTree horario:horarios) {
                                if(horario.getCodigoPai().equals(turma.getCodigoAgrupador())){
                                    for(ComissaoTree aluno: alunos){
                                        if(aluno.getCodigoPai().equals(horario.getCodigoAgrupador())){
                                            horario.getAlunos().add(aluno);
                                        }
                                    }
                                    turma.getHorarios().add(horario);
                                }
                            }
                            mod.getTurmas().add(turma);
                        }
                    }
                    prof.getModalidades().add(mod);
                }
            }
            listaSintenticoProfessor.add(prof);
        }
    }


    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        ArrayList listaClonada = new ArrayList<>();
        try {
            listaClonada.addAll(getListaManipulavel());

            if (getProfessorSelecionado() != 0 ){
                verificarFiltroProfessorTrazerFiltrado();
                listaClonada.clear();
                listaClonada.addAll(getListaObjetos());
                getListaManipulavel().clear();
                getListaManipulavel().addAll(getListaObjetos());
            }

            params.put("nomeRelatorio", "GestaoComissao");
            params.put("nomeEmpresa", empre.getNome());

            params.put("tituloRelatorio", "Relatório Comissão para Professor");
            params.put("nomeDesignIReport", getDesignIReportRelatorio());
            params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
            params.put("listaObjetos", listaClonada);

            params.put("filtros", getFiltrosDescricao());
            if (ehCompetencia) {
                params.put("dataIni", Uteis.getDataAplicandoFormatacao(dataInicio, "MM/yyyy"));
                params.put("dataFim", Uteis.getDataAplicandoFormatacao(dataFim, "MM/yyyy"));
            } else {
                params.put("dataIni", Uteis.getData(dataInicio));
                params.put("dataFim", Uteis.getData(dataFim));
            }
            params.put("enderecoEmpresa", empre.getEndereco());
            params.put("cidadeEmpresa", empre.getCidade().getNome());
            /*JRDataSource jr1 = new JRBeanArrayDataSource(totalQuantidadeAgrupadoPorSituacao.toArray(), false);
            JRDataSource jr2 = new JRBeanArrayDataSource(totalValorAgrupadoPorSituacao.toArray(), false);
            JRDataSource jr3 = new JRBeanArrayDataSource(totalParcelasAgrupadoPorSituacao.toArray(), false);*/

            /*params.put("dadosTotalPorSituacao", jr1);
            params.put("dadosValoresPorSituacao", jr2);
            params.put("dadosValoresParcelas", jr3);*/

            params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        } finally {
            listaClonada = null;
        }
    }

    public void imprimirRelatorio() {
        try {
            limparMsg();
            setMsgAlert("");
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_COMISSAO_PROFESSOR_ANALITICO, getListaObjetos().size(), getFiltrosDescricao(), "pdf", "", "");
            Map<String, Object> parametros = new HashMap<String, Object>();

            prepareParams(parametros);
            apresentarRelatorioObjetos(parametros);
            setMsgAlert("abrirPopupPDFImpressao('relatorio/"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void verificarFiltroProfessorTrazerFiltrado() throws Exception{
        try {

            if (getProfessorSelecionado() != null && getProfessorSelecionado() != 0){
                List<ComissaoVO> listaAuxiliar = new ArrayList<ComissaoVO>(0);
                for (ComissaoVO comissaoVO: getListaObjetosAuxiliar()){
                    boolean temProfessor = false;
                    for (DetalhamentoLancamentoDF_VO detalhamentoLancamentoDFVO: comissaoVO.getListaDetalheLancamento()){
                        for (ContratoModalidadePercentual contratoModalidadePercentual: detalhamentoLancamentoDFVO.getListaContratoModalidade()){
                            for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO: contratoModalidadePercentual.getMatriculasDesteContratoModalidade()){
                                if (matriculaAlunoHorarioTurmaVO.getHorarioTurma().getProfessor().getCodigo().equals(getProfessorSelecionado())){
                                    temProfessor = true;
                                    break;
                                }
                            }
                            if (temProfessor){
                                break;
                            }
                        }
                        if (temProfessor){
                            break;
                        }
                    }
                    if (temProfessor){
                        listaAuxiliar.add(comissaoVO);
                    }
                }

                setListaObjetos(listaAuxiliar);
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }


    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "GestaoComissaoRel.jrxml");
    }

    public String getDesignIReportRelatorioExcel() {
        return ("designRelatorio" + File.separator + "outros" + File.separator + "GestaoComissaoRelExcel");
    }

    public String getFiltrosDescricao() {
        String filtros = "Consulta:\t" + getTipoConsulta();
        if (this.somenteComTurmas) {
            filtros += "\nObservação:\t" + getSomenteAlunosTurma();
        }
        if (ehCompetencia) {
            filtros += "\nIntervalo de Data: \t " + Uteis.getDataAplicandoFormatacao(this.getDataInicio(), "MM/yyyy") + " até "
                    + Uteis.getDataAplicandoFormatacao(this.getDataFim(), "MM/yyyy");
        } else {
            filtros += "\nIntervalo de Data: \t " + Uteis.getData(this.getDataInicio()) + " até "
                    + Uteis.getData(this.getDataFim());
        }
        return filtros;
    }

    @Override
    public void filtrarPorTexto() {
//        super.filtrarPorTexto();
        if (getNomeConsulta().isEmpty()) {
            setListaManipulavel(getListaOriginal() != null ? (ArrayList) getListaOriginal().clone() : new ArrayList());
        } else {
            setListaManipulavel(getListaOriginal() != null ? (ArrayList) getListaOriginal().clone() : new ArrayList());
            List listaAssociada = new ArrayList(getListaManipulavel());
            for (Object object : listaAssociada) {
                ComissaoVO comissao = (ComissaoVO) object;
                if (!comissao.getNome().toLowerCase().contains(getNomeConsulta().toLowerCase())) {
                    getListaManipulavel().remove(object);
                }
            }
        }
        DataScrollerControle dataScroller = (DataScrollerControle) getControlador(DataScrollerControle.class.getSimpleName());
        dataScroller.getDataScroller().getDataTable().setRows(100);
        dataScroller.getDataScroller().getDataTable().setValue(getListaManipulavel());
    }

    public List converterListaObjetos() {
        List<ComissaoLinearVO> listaRetornar = new ArrayList();
        List<ComissaoVO> lista = (List<ComissaoVO>) getListaManipulavel().clone();
        for (ComissaoVO c : lista) {

            List<DetalhamentoLancamentoDF_VO> listaDetalhe = c.getListaDetalheLancamento();
            for (DetalhamentoLancamentoDF_VO detalhe : listaDetalhe) {

                List<ContratoModalidadePercentual> listaCM = detalhe.getListaContratoModalidade();

                for (ContratoModalidadePercentual cm : listaCM) {

                    List<MatriculaAlunoHorarioTurmaVO> matriculas = cm.getMatriculasDesteContratoModalidade();
                    if (matriculas.isEmpty()) {
                        ComissaoLinearVO cl = new ComissaoLinearVO();
                        cl.setCliente(c.getNome());
                        cl.setMatricula(c.getCliente().getMatricula());
                        cl.setSituacao(c.getCliente().getSituacao());
                        //
                        cl.setValorpago(detalhe.getTotalPagoPlano_Apresentar());
                        cl.setIdpagamento(String.valueOf(detalhe.getMovPagamento()));
                        //
                        cl.setModalidade(cm.getNomeModalidade());
                        cl.setFracaopagoporcent(cm.getPercentagem_Apresentar());
                        cl.setFracaopagovalor(cm.getValorPago_Apresentar());
                        //
                        listaRetornar.add(cl);
                    } else {
                        for (MatriculaAlunoHorarioTurmaVO mat : matriculas) {
                            ComissaoLinearVO cl = new ComissaoLinearVO();
                            cl.setCliente(c.getNome());
                            cl.setMatricula(c.getCliente().getMatricula());
                            cl.setSituacao(c.getCliente().getSituacao());
                            //
                            cl.setValorpago(detalhe.getTotalPagoPlano_Apresentar());
                            cl.setIdpagamento(String.valueOf(detalhe.getMovPagamento()));
                            //
                            cl.setModalidade(cm.getNomeModalidade());
                            cl.setFracaopagoporcent(cm.getPercentagem_Apresentar());
                            cl.setFracaopagovalor(cm.getValorPago_Apresentar());
                            //

                            cl.setContrato(mat.getContrato().getCodigo().toString());
                            cl.setDia(mat.getHorarioTurma().getDiaSemana());
                            cl.setHorario(mat.getHorarioTurma().getHoraInicial() + "/"
                                    + mat.getHorarioTurma().getHoraFinal());
                            cl.setIdentificadorturma(mat.getHorarioTurma().getIdentificadorTurma());
                            cl.setProfessor(mat.getHorarioTurma().getProfessor().getPessoa().getNome());
                            if (!UteisValidacao.emptyNumber(mat.getHorarioTurma().getProfessor().getPorcComissao())) {
                                cl.setComissao(mat.getHorarioTurma().getProfessor().getPorcComissao_Apresentar());
                            } else if (!UteisValidacao.emptyNumber(mat.getHorarioTurma().getProfessor().getValorComissao())) {
                                cl.setComissao(mat.getHorarioTurma().getProfessor().getValorComissao_Apresentar());
                            }
                            cl.setIniciomatricula(mat.getDataInicio_Apresentar());
                            cl.setFimmatricula(mat.getDataFim_Apresentar());
                            cl.setValorcomissao(mat.getValorComissao_Apresentar());
                            listaRetornar.add(cl);
                        }
                    }

                }

            }

        }
        return listaRetornar;
    }

    public void imprimirExcel() {
        try {
            limparMsg();
            setMsgAlert("");
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_COMISSAO_PROFESSOR_ANALITICO, getListaObjetos().size(), getFiltrosDescricao(), "xlsx", "", "");
            GeradorRelatorio obj = new GeradorRelatorio();
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            obj.montarRelatorio("OBJETO", "EXCEL", "GestaoComissaoRelExcel",
                    getDesignIReportRelatorioExcel(), converterListaObjetos(), parametros);
            setMsgAlert("location.href='DownloadSV?mimeType=application/vnd.ms-excel&relatorio=GestaoComissaoRelExcel.xlsx'");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirTelaCliente() throws Exception {
        ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
        clienteControle.setarCliente(getFacade().getCliente().consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_TELACONSULTA));
        clienteControle.acaoAjax();
    }

    private String gerarCodigoNo(String agregado, Integer cont) {
        String codigo = cont.toString();
        while (codigo.length() < 3) {
            codigo = "0" + codigo;
        }
        if (!UteisValidacao.emptyString(agregado)) {
            codigo = agregado + "." + codigo;
        }
        return codigo;
    }

    private List<Integer> obterCodigoColaboradoresSelecionados() {
        List<Integer> lista = new ArrayList<Integer>();
        colaboradoresSel = "";
        for (ColaboradorVO col : filtros) {
            if (col.getColaboradorEscolhido()) {
                lista.add(col.getCodigo());
                if (colaboradoresSel.equals("")) {
                    colaboradoresSel = col.getPessoa().getNome();
                } else {
                    colaboradoresSel += ", " + col.getPessoa().getNome();
                }
            }
        }
        return lista;
    }

    public List getTiposRelatorios() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem(true, "Por Competência"));
        itens.add(new SelectItem(false, "Por Compensação"));
        return itens;
    }

    public List<ComissaoTree> getListaTree() {
        return listaTree;
    }

    public void setListaTree(List<ComissaoTree> listaTree) {
        this.listaTree = listaTree;
    }

    public List<ColaboradorVO> getFiltros() {
        return filtros;
    }

    public void setFiltros(List<ColaboradorVO> filtros) {
        this.filtros = filtros;
    }

    public List<ComissaoTree> getListaTreeBkp() {
        return listaTreeBkp;
    }

    public void setListaTreeBkp(List<ComissaoTree> listaTreeBkp) {
        this.listaTreeBkp = listaTreeBkp;
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        getEmpresaLogado().setFoto(getFacade().getEmpresa().obterFoto(getKey(), 
                getEmpresaLogado().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA));
        SuperControle.paintFoto(out, getEmpresaLogado().getFoto());
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer codigoCliente) {
        this.cliente = codigoCliente;
    }

    public boolean isEhCompetencia() {
        return ehCompetencia;
    }

    public void setEhCompetencia(boolean ehCompetencia) {
        this.ehCompetencia = ehCompetencia;
    }

    public Boolean getOpenConsulta() {
        return openConsulta;
    }

    public void setOpenConsulta(Boolean openConsulta) {
        this.openConsulta = openConsulta;
    }

    public Boolean getOpenDatas() {
        return openDatas;
    }

    public void setOpenDatas(Boolean openDatas) {
        this.openDatas = openDatas;
    }

    public Boolean getOpenFiltros() {
        return openFiltros;
    }

    public void setOpenFiltros(Boolean openFiltros) {
        this.openFiltros = openFiltros;
    }

    public String getFormatoDoCalendario() {
        return formatoDoCalendario;
    }

    public void setFormatoDoCalendario(String formatoDoCalendario) {
        this.formatoDoCalendario = formatoDoCalendario;
    }

    public Object alterarFormatoDoCalendario() {
        setDataFim(null);
        setDataInicio(null);
        if (isEhCompetencia()) {
            setFormatoDoCalendario("MM/yyyy");
        } else {
            setFormatoDoCalendario("dd/MM/yyyy");
        }

        return true;
    }

    /**
     * @param tipoConsulta the tipoConsulta to set
     */
    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    /**
     * @return the tipoConsulta
     */
    public String getTipoConsulta() {
        return tipoConsulta;
    }

    /**
     * @param colaboradoresSel the colaboradoresSel to set
     */
    public void setColaboradoresSel(String colaboradoresSel) {
        this.colaboradoresSel = colaboradoresSel;
    }

    /**
     * @return the colaboradoresSel
     */
    public String getColaboradoresSel() {
        return colaboradoresSel;
    }

    /**
     * @param somenteAlunosTurma the somenteAlunosTurma to set
     */
    public void setSomenteAlunosTurma(String somenteAlunosTurma) {
        this.somenteAlunosTurma = somenteAlunosTurma;
    }

    /**
     * @return the somenteAlunosTurma
     */
    public String getSomenteAlunosTurma() {
        return somenteAlunosTurma;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public List<SelectItem> getListaProfessores() {
        return listaProfessores;
    }

    public void setListaProfessores(List<SelectItem> listaProfessores) {
        this.listaProfessores = listaProfessores;
    }

    public Integer getProfessorSelecionado() {
        return professorSelecionado;
    }

    public void setProfessorSelecionado(Integer professorSelecionado) {
        this.professorSelecionado = professorSelecionado;
    }


    public List<ComissaoVO> getListaObjetosAuxiliar() {
        if (listaObjetosAuxiliar == null){
            listaObjetosAuxiliar = getListaObjetos();
        }
        return listaObjetosAuxiliar;
    }

    public void setListaObjetosAuxiliar(List<ComissaoVO> listaObjetosAuxiliar) {
        this.listaObjetosAuxiliar = listaObjetosAuxiliar;
    }

    public void validarPermissaoExportacao(){
        try {
            limparMsg();
            setMsgAlert("");
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_COMISSAO_PROFESSOR_SINTETICO, listaTree.size(), "","html", "", "");
            setMsgAlert("abrirPopup('relatorioComissao.jsp', 'GestaoComissao', 780, 595);");
        } catch (Exception e){
            montarErro(e);
        }
    }
}

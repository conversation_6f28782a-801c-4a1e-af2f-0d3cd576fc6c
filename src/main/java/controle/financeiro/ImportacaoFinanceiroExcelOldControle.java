package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.financeiro.PlanoContasControle.TipoConsulta;
import importador.LeitorExcel;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.PlanoConta;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.sql.SQLException;
import java.util.*;

public class ImportacaoFinanceiroExcelOldControle extends SuperControle {

    private File arquivo;
    private List<XSSFRow> hssfRows;
    private List<Integer> idxs = new ArrayList<Integer>();
    private Map<Integer, Object> cabecalho = new HashMap<Integer, Object>();
    private List<Map<Integer, Object>> linhas = new ArrayList<Map<Integer, Object>>();

    public void abrirPopUp() {
        try {
            setMsgAlert("abrirPopup('" + request().getContextPath() +
                    "/faces/importadorExcelFinanceiro.jsp', 'Importador', 1000, 670);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }

    }

    public void uploadArquivoListener(final UploadEvent event) throws Exception {
        try {
            linhas = new ArrayList<Map<Integer, Object>>();
            final UploadItem item = event.getUploadItem();
            final File arquivoUploaded = item.getFile();
            arquivo = new File(item.getFile().getParent() + File.separator + item.getFileName());
            arquivo.delete();
            final FileOutputStream out = new FileOutputStream(arquivo);
            out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
            // Limpa a memória assim que o arquivo e carregado
            out.flush();
            out.close();
            XSSFWorkbook sheets = new XSSFWorkbook(new FileInputStream(item.getFile().getParent() + File.separator + item.getFileName()));

            XSSFRow row = sheets.getSheetAt(0).getRow(0);
            int cc = 0;
            idxs = new ArrayList<Integer>();
            Iterator<Cell> itc = row.cellIterator();
            cabecalho = new HashMap<Integer, Object>();
            while (itc.hasNext()){
                idxs.add(cc);
                Cell c = itc.next();
                cabecalho.put(cc++, c.getStringCellValue());
            }

            hssfRows = LeitorExcel2010.lerLinhas(new FileInputStream(item.getFile().getParent() + File.separator + item.getFileName()));
            for(XSSFRow linha : hssfRows){
                Map<Integer, Object> mp = new HashMap<Integer, Object>();
                int i = 0;
                Iterator<Cell> it = linha.cellIterator();
                while (it.hasNext()){
                    Cell c = it.next();
                    CellType cellTypeEnum = c.getCellTypeEnum();
                    switch (cellTypeEnum){
                        case STRING:
                            mp.put(i++, c.getStringCellValue());
                            break;
                        case NUMERIC:
                            mp.put(i++, c.getNumericCellValue());
                            break;
                        case BOOLEAN:
                            mp.put(i++, c.getBooleanCellValue());
                            break;
                        default:
                            mp.put(i++, c.getDateCellValue());
                            break;
                    }

                }
                linhas.add(mp);
            }
            System.out.println(linhas);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    public void removerArquivo() {
        arquivo = null;
    }

    public File getArquivo() {
        return arquivo;
    }

    public void setArquivo(File arquivo) {
        this.arquivo = arquivo;
    }

    public List<XSSFRow> getHssfRows() {
        return hssfRows;
    }

    public void setHssfRows(List<XSSFRow> hssfRows) {
        this.hssfRows = hssfRows;
    }

    public List<Integer> getIdxs() {
        return idxs;
    }

    public void setIdxs(List<Integer> idxs) {
        this.idxs = idxs;
    }

    public Map<Integer, Object> getCabecalho() {
        return cabecalho;
    }

    public void setCabecalho(Map<Integer, Object> cabecalho) {
        this.cabecalho = cabecalho;
    }

    public List<Map<Integer, Object>> getLinhas() {
        return linhas;
    }

    public void setLinhas(List<Map<Integer, Object>> linhas) {
        this.linhas = linhas;
    }
}

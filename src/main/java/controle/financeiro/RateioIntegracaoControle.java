package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoProdutoLocacao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.EntidadeRateioEnum;
import br.com.pactosolucoes.enumeradores.TipoRateioEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.financeiro.CategoriaRateioTO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ModalidadeRateioTO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.RateioTreeTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.PlanoConta;
import negocio.facade.jdbc.financeiro.RateioIntegracao;

import javax.faces.model.SelectItem;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class RateioIntegracaoControle extends SuperControle {

    private List<CategoriaRateioTO> rateios;
    private List<ModalidadeRateioTO> rateiosModalidade;
    private Boolean mostrarEmpresa = null;
    // codigos para edição
    private Integer codigoProduto;
    private Integer codigoCategoria;
    private Integer codigoModalidade;
    // codigos para inclusão de rateios
    private RateioIntegracaoTO rateio;
    // rateios para serem adicionados na edição de rateios
    private List<RateioIntegracaoTO> rateiosEdicao;
    private RateioIntegracaoTO rateioTemp;
    private String codigoComboCentro;
    private PlanoContaTO planoSuggestion;
    // controle de navegação
    private boolean fecharmodal;
    private boolean adicaoRateio;
    private boolean edicaoRateio;
    public String descricaoInclusaoRateio;
    private boolean ativarPool = false;
    private List<CategoriaProdutoVO> listaCategoriaProdutos;
    private List<ProdutoVO> listaProdutos;
    private List<ModalidadeVO> listaModalidades;
    private List<RateioTreeTO> listaRateiosProdutos;
    private List<RateioTreeTO> listaRateiosModalidades;
    private List<RateioTreeTO> listaRateiosCentralEventos;
    private int idEntidadeSelecionada;
    private int idTipoSelecionado;
    private String nomeSelecionado;
    private Integer cont = new Integer(0);
    private String abaSelecionada;
    private boolean permiteCentroCustos = true;
    private boolean abrirModalRateio = false;
    private ConfiguracaoFinanceiroVO confFinanceiro = new ConfiguracaoFinanceiroVO();

    public enum ValorRateioOneRadio {

        TODOS, SomentePlanoConta, SomenteCentroCusto
    };
    //por padrao o valor de radio é todos
    public static ValorRateioOneRadio valorOneRadio = ValorRateioOneRadio.TODOS;
    private boolean gravarPlanoContasRateio = false;

    public void montarRateios() throws Exception {
        cont = 1;
        montarProdutos();
        montarModalidades();
        montarCE();
    }

    private String gerarCodigoNo(String agregado, Integer cont) {
        String codigo = cont.toString();
        while (codigo.length() < 3) {
            codigo = "0" + codigo;
        }
        if (!agregado.isEmpty()) {
            codigo = agregado + "." + codigo;
        }
        return codigo;
    }

    private void montarCE() throws Exception {
        setListaRateiosCentralEventos(new ArrayList<RateioTreeTO>());
        //obter descricao e codigo das categorias
        RateioTreeTO rateioAmbientes = new RateioTreeTO();
        rateioAmbientes.setCodigoAgrupador(gerarCodigoNo("", cont));
        rateioAmbientes.setNome("Ambientes");
        rateioAmbientes.setEntidadeRateio(EntidadeRateioEnum.AMBIENTES.getTipo());
        getListaRateiosCentralEventos().add(rateioAmbientes);
        carregarRateios(EntidadeRateioEnum.AMBIENTES, rateioAmbientes);
        cont++;

        montarTreeCE(getFacade().getAmbiente().consultarAmbientesCESimplificado(), rateioAmbientes, EntidadeRateioEnum.AMBIENTE);

        RateioTreeTO rateioServicos = new RateioTreeTO();
        rateioServicos.setCodigoAgrupador(gerarCodigoNo("", cont));
        rateioServicos.setNome("Serviços");
        rateioServicos.setEntidadeRateio(EntidadeRateioEnum.SERVICOS.getTipo());
        getListaRateiosCentralEventos().add(rateioServicos);
        carregarRateios(EntidadeRateioEnum.SERVICOS, rateioServicos);
        cont++;
        montarTreeCE(getFacade().getCentralEventosFacade().getServico().consultarServicoSimplificado(), rateioServicos, EntidadeRateioEnum.SERVICO);

        montarProdutos(TipoProdutoLocacao.BENS_DE_CONSUMO, EntidadeRateioEnum.BENS_CONSUMO);
        montarProdutos(TipoProdutoLocacao.UTENSILIOS, EntidadeRateioEnum.UTENSILIOS);
        montarProdutos(TipoProdutoLocacao.BRINQUEDOS, EntidadeRateioEnum.BRINQUEDOS);


        RateioTreeTO rateioCreditos = new RateioTreeTO();
        rateioCreditos.setCodigoAgrupador(gerarCodigoNo("", cont));
        rateioCreditos.setNome("Crédito");
        rateioCreditos.setEntidadeRateio(EntidadeRateioEnum.CREDITO.getTipo());
        getListaRateiosCentralEventos().add(rateioCreditos);

        carregarRateios(EntidadeRateioEnum.CREDITO, rateioCreditos);
        cont++;

        RateioTreeTO rateioDevolucaoCreditos = new RateioTreeTO();
        rateioDevolucaoCreditos.setCodigoAgrupador(gerarCodigoNo("", cont));
        rateioDevolucaoCreditos.setNome("Devolução de crédito");
        rateioDevolucaoCreditos.setEntidadeRateio(EntidadeRateioEnum.DEVOLUCAO_CREDITO.getTipo());
        getListaRateiosCentralEventos().add(rateioDevolucaoCreditos);
        carregarRateios(EntidadeRateioEnum.DEVOLUCAO_CREDITO, rateioDevolucaoCreditos);
        cont++;


    }

    private void carregarRateios(EntidadeRateioEnum entidade, RateioTreeTO rateio) throws Exception {
        RateioTreeTO detalheTreeProd = new RateioTreeTO();
        detalheTreeProd.setCodigoAgrupador(gerarCodigoNo(rateio.getCodigoAgrupador(), 1));
        detalheTreeProd.setDetalhamento(true);
        detalheTreeProd.setRateios(getFacade().getFinanceiro().getRateioIntegracao().consultar(entidade, rateio.getCodigoEntidade()));
        if (!detalheTreeProd.getRateios().isEmpty()) {
            getListaRateiosCentralEventos().add(detalheTreeProd);
        }


    }

    private void montarTreeCE(Map<Integer, String> map, RateioTreeTO pai, EntidadeRateioEnum entidade) throws Exception {
        Set<Integer> keySetAmb = map.keySet();
        pai.setPrevia(keySetAmb.size());
        for (Integer keyAmb : keySetAmb) {
            RateioTreeTO rateio = new RateioTreeTO();
            rateio.setCodigoAgrupador(gerarCodigoNo(pai.getCodigoAgrupador(), cont));
            rateio.setCodigoEntidade(keyAmb);
            rateio.setNome(map.get(keyAmb));
            rateio.setEntidadeRateio(entidade.getTipo());
            getListaRateiosCentralEventos().add(rateio);
            cont++;
            carregarRateios(entidade, rateio);
        }
    }

    private void montarProdutos(TipoProdutoLocacao tipo, EntidadeRateioEnum entidade) throws Exception {
        RateioTreeTO rateio = new RateioTreeTO();
        rateio.setCodigoAgrupador(gerarCodigoNo("", cont));
        rateio.setNome(tipo.getDescricao());
        rateio.setCodigoEntidade(entidade.getTipo());
        rateio.setTipoRateio(entidade.getTipo());
        rateio.setEntidadeRateio(entidade.getTipo());
        getListaRateiosCentralEventos().add(rateio);
        cont++;
        carregarRateios(entidade, rateio);
        montarTreeCE(getFacade().getCentralEventosFacade().getProdutoLocacao().consultarSimplificado(tipo), rateio, EntidadeRateioEnum.PRODUTO_CE);
    }

    /**
     * <AUTHOR> 12/08/2011
     */
    private void montarProdutos() throws Exception {
        List<Integer> produtosComRateio = getFacade().getFinanceiro().getRateioIntegracao().obterProdutosComRateio();
        setListaRateiosProdutos(new ArrayList<RateioTreeTO>());

        //obter descricao e codigo das categorias
        Map<Integer, String> mapCategorias = getFacade().getCategoriaProduto().consultarCategoriaSimplificado();
        Set<Integer> keySet = mapCategorias.keySet();

        for (Integer key : keySet) {
            //obter produtos desta categoria
            Map<Integer, String> produtos = getFacade().getProduto().consultarProdutosPorCategoriaSimplificado(key, "AT");
            //caso a categoria não possua produtos ou todos os produtos estejam desativados
            if (produtos.size() == 0) {
                continue;
            }

            RateioTreeTO rateioTree = new RateioTreeTO();
            rateioTree.setNome(mapCategorias.get(key));
            rateioTree.setCodigoAgrupador(gerarCodigoNo("", cont));
            rateioTree.setCodigoEntidade(key);
            rateioTree.setEntidadeRateio(EntidadeRateioEnum.CATEGORIA_PRODUTO.getTipo());
            rateioTree.setPrevia(produtos.size());
            //detalhar rateios da categoria
            List<RateioIntegracaoTO> rateios = getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.CATEGORIA_PRODUTO, key);
            if (rateios != null && !rateios.isEmpty()) {
                rateioTree.setPossuiRateio(true);
            }

            getListaRateiosProdutos().add(rateioTree);

            if (!rateios.isEmpty()) {
                RateioTreeTO rateio = new RateioTreeTO();
                rateio.setCodigoAgrupador(gerarCodigoNo(rateioTree.getCodigoAgrupador(), 1));
                rateio.setNome("Rateio da categoria");
                rateio.setExibirPrevia(false);

                RateioTreeTO detalheTree = new RateioTreeTO();
                detalheTree.setCodigoAgrupador(gerarCodigoNo(rateio.getCodigoAgrupador(), 1));
                detalheTree.setRateios(rateios);
                detalheTree.setDetalhamento(true);
                getListaRateiosProdutos().add(rateio);
                getListaRateiosProdutos().add(detalheTree);
            }
            int contProd = 1;
            int totalProd = 0;
            Set<Integer> keySetProd = produtos.keySet();

            //adicionar produtos que já possuem rateio
            RateioTreeTO prodComRateio = new RateioTreeTO();
            prodComRateio.setCodigoAgrupador(gerarCodigoNo(rateioTree.getCodigoAgrupador(), 2));
            prodComRateio.setNome("Produtos com rateio específico");
            getListaRateiosProdutos().add(prodComRateio);
            for (Integer keyProd : keySetProd) {
                if (produtosComRateio.contains(keyProd)) {
                    RateioTreeTO rateioTreeProd = new RateioTreeTO();
                    rateioTreeProd.setNome(produtos.get(keyProd));
                    rateioTreeProd.setCodigoAgrupador(gerarCodigoNo(prodComRateio.getCodigoAgrupador(), contProd));
                    rateioTreeProd.setCodigoEntidade(keyProd);
                    rateioTreeProd.setEntidadeRateio(EntidadeRateioEnum.PRODUTO.getTipo());

                    RateioTreeTO detalheTreeProd = new RateioTreeTO();
                    detalheTreeProd.setCodigoAgrupador(gerarCodigoNo(rateioTreeProd.getCodigoAgrupador(), 1));
                    detalheTreeProd.setRateios(getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.PRODUTO, keyProd));
                    detalheTreeProd.setDetalhamento(true);

                    getListaRateiosProdutos().add(rateioTreeProd);
                    getListaRateiosProdutos().add(detalheTreeProd);
                    contProd++;
                    totalProd++;
                }
            }
            prodComRateio.setPrevia(totalProd);

            totalProd = 0;
            //adicionar produtos que não possuem rateio
            RateioTreeTO prodSemRateio = new RateioTreeTO();
            prodSemRateio.setCodigoAgrupador(gerarCodigoNo(rateioTree.getCodigoAgrupador(), 3));
            prodSemRateio.setNome("Produtos com rateio padrão da categoria");
            getListaRateiosProdutos().add(prodSemRateio);
            for (Integer keyProd : keySetProd) {
                if (!produtosComRateio.contains(keyProd)) {
                    RateioTreeTO rateioTreeProd = new RateioTreeTO();
                    rateioTreeProd.setNome(produtos.get(keyProd));
                    rateioTreeProd.setCodigoAgrupador(gerarCodigoNo(prodSemRateio.getCodigoAgrupador(), contProd));
                    rateioTreeProd.setCodigoEntidade(keyProd);
                    rateioTreeProd.setEntidadeRateio(EntidadeRateioEnum.PRODUTO.getTipo());
                    getListaRateiosProdutos().add(rateioTreeProd);
                    contProd++;
                    totalProd++;
                }
            }
            prodSemRateio.setPrevia(totalProd);
            cont++;
        }
    }

    /**
     * <AUTHOR> 12/08/2011
     * @throws Exception
     */
    public void selecionarRateio() throws Exception {
        montarListaEmpresas();
        this.setPermiteCentroCustos(true);
        this.limparDados();
        limparMsg();
        this.abrirModalRateio = true;
        this.setAdicaoRateio(true);
        this.setEdicaoRateio(false);
        this.setRateiosEdicao(new ArrayList<RateioIntegracaoTO>());
        if (this.getIdTipoSelecionado() == EntidadeRateioEnum.PRODUTO.getTipo()) {
            String tipo = getFacade().getProduto().obterTipoProduto(this.getIdEntidadeSelecionada());
            if (tipo.equals("VI")) {
                this.setPermiteCentroCustos(false);
            }
            this.setRateiosEdicao(getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.PRODUTO, this.getIdEntidadeSelecionada()));
            this.getRateio().setDescricao(EntidadeRateioEnum.PRODUTO.getDescricao() + ": " + this.getNomeSelecionado());
        } else {
            EntidadeRateioEnum rateioEnum = EntidadeRateioEnum.getEntidadeRateioEnum(this.getIdTipoSelecionado());
            this.setRateiosEdicao(getFacade().getFinanceiro().getRateioIntegracao().consultar(rateioEnum, this.getIdEntidadeSelecionada()));
            this.getRateio().setDescricao(rateioEnum.getDescricao() +(UteisValidacao.emptyString(this.getNomeSelecionado()) ? "" : (" : "+this.getNomeSelecionado())));
        }


    }

    public boolean isTipoOrdemCompra() {
        try {
            return getFacade().getProduto().obterTipoProduto(this.getIdEntidadeSelecionada()).equals(TipoProduto.ORDEM_COMPRA.getCodigo());
        } catch (Exception ex) {
            return false;
        }
    }

    public void fecharModalRateio() {
        this.abrirModalRateio = false;
    }

    /**
     * <AUTHOR> 12/08/2011
     */
    private void montarModalidades() throws Exception {
        setListaRateiosModalidades(new ArrayList<RateioTreeTO>());

        //obter descricao e codigo das modalidades
        Map<Integer, String> mapModalidades = getFacade().getModalidade().consultarModalidadesSimplificado();
        Map<Integer, String> empresasModalidade = getFacade().getModalidade().consultarEmpresasModalidadesSimplificado();
        //ordenar por nome
        List<String> nomes = new ArrayList<String>(mapModalidades.values());
        Collections.sort(nomes);
        List<Integer> keySet = new ArrayList<Integer>();
        FORNOME: for(String nome : nomes){
            for(Integer k : mapModalidades.keySet()){
                if(mapModalidades.get(k).equals(nome) && !keySet.contains(k)){
                    keySet.add(k);
                    continue FORNOME;
                }
            }
        }
        List<Integer> modalidesComRateio = getFacade().getFinanceiro().getRateioIntegracao().obterModalidesComRateio();
        //adicionar modalidades que já possuem rateio
        RateioTreeTO rateioGeralModalidade = new RateioTreeTO();
        rateioGeralModalidade.setCodigoAgrupador(gerarCodigoNo("", cont));
        rateioGeralModalidade.setNome("Rateio geral das modalidades");
        rateioGeralModalidade.setCodigoEntidade(-1);
        rateioGeralModalidade.setEntidadeRateio(EntidadeRateioEnum.GERAL_MODALIDADES.getTipo());
        getListaRateiosModalidades().add(rateioGeralModalidade);
        cont++;
        RateioTreeTO detalheTreeGeral = new RateioTreeTO();
        detalheTreeGeral.setCodigoAgrupador(gerarCodigoNo(rateioGeralModalidade.getCodigoAgrupador(), 1));
        detalheTreeGeral.setRateios(getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.GERAL_MODALIDADES, 0));
        detalheTreeGeral.setDetalhamento(true);
        getListaRateiosModalidades().add(detalheTreeGeral);
        cont++;
        //adicionar modalidades que já possuem rateio
        RateioTreeTO modComRateio = new RateioTreeTO();
        modComRateio.setCodigoAgrupador(gerarCodigoNo("", cont));
        modComRateio.setNome("Modalidades com rateio");
        getListaRateiosModalidades().add(modComRateio);
        cont++;
        int totalMod = 0;
        for (Integer key : keySet) {
            if (modalidesComRateio.contains(key)) {
                RateioTreeTO rateioTree = new RateioTreeTO();
                rateioTree.setNome(mapModalidades.get(key));
                rateioTree.setCodigoAgrupador(gerarCodigoNo(modComRateio.getCodigoAgrupador(), cont));
                rateioTree.setCodigoEntidade(key);
                rateioTree.setEntidadeRateio(EntidadeRateioEnum.MODALIDADE.getTipo());
                rateioTree.setEmpresas(empresasModalidade.get(key));
                RateioTreeTO detalheTree = new RateioTreeTO();
                detalheTree.setCodigoAgrupador(gerarCodigoNo(rateioTree.getCodigoAgrupador(), 1));
                detalheTree.setRateios(getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.MODALIDADE, key));
                detalheTree.setDetalhamento(true);

                getListaRateiosModalidades().add(rateioTree);
                getListaRateiosModalidades().add(detalheTree);
                cont++;
                totalMod++;
            }
        }
        modComRateio.setPrevia(totalMod);

        //adicionar modalidades que não possuem rateio
        RateioTreeTO modSemRateio = new RateioTreeTO();
        modSemRateio.setCodigoAgrupador(gerarCodigoNo("", cont));
        modSemRateio.setNome("Modalidades sem rateio");
        getListaRateiosModalidades().add(modSemRateio);
        cont++;
        totalMod = 0;
        for (Integer key : keySet) {
            if (!modalidesComRateio.contains(key)) {
                RateioTreeTO rateioTree = new RateioTreeTO();
                rateioTree.setNome(mapModalidades.get(key));
                rateioTree.setCodigoAgrupador(gerarCodigoNo(modSemRateio.getCodigoAgrupador(), cont));
                rateioTree.setCodigoEntidade(key);
                rateioTree.setEmpresas(empresasModalidade.get(key));
                rateioTree.setEntidadeRateio(EntidadeRateioEnum.MODALIDADE.getTipo());
                getListaRateiosModalidades().add(rateioTree);
                cont++;
                totalMod++;
            }
        }
        modSemRateio.setPrevia(totalMod);
    }

    public List<CategoriaRateioTO> getRateios() {
        if (rateios == null) {
            this.carregar();
        }
        return rateios;
    }

    public String abrirTelaRateioIntegracao() {
        try {
            setAbaSelecionada("abaCategoria");
            valorOneRadio = ValorRateioOneRadio.TODOS;
            setGravarPlanoContasRateio(false);
            limparMsg();
            limparDados();
            try {
                setConfFinanceiro(getFacade().getConfiguracaoFinanceiro().consultar());
                validarPermissaoRateioIntegracao();
                montarRateios();
            } catch (Exception e) {
                montarMsgAlert(e.getMessage());
                return "";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "rateioIntegracao";
    }

    /**
     * Valida a permissão do usuário logado para a entidade RateioIntegracao que
     * usa a permissão "9.03 - Rateio de Integração"
     *
     * @throws Exception
     */
    public void validarPermissaoRateioIntegracao() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "RateioIntegracao", "9.03 - Rateio de Integração");
            }
        }
    }

    public List<ModalidadeRateioTO> getRateiosModalidade() {
        if (rateiosModalidade == null) {
            this.carregarModalidade();
        }
        return rateiosModalidade;
    }

    public void editarRateiosCategoria() {
        try {
            this.setDescricaoInclusaoRateio("Edição de Rateio da Categoria");
            Integer cdgCat = this.getCodigoCategoria();
            this.setCodigoProduto(0);
            this.rateio.setCodigoCategoria(cdgCat);
            this.rateio.setCodigoProduto(0);
            this.rateio.setCodigoModalidade(0);
            this.rateio.setDescricao("Rateio de Categoria");

            // consultar os rateios já cadastrados
            List<RateioIntegracaoTO> rateiosCategoria = getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.CATEGORIA_PRODUTO, cdgCat);
            this.setRateiosEdicao(rateiosCategoria);
            this.setAdicaoRateio(true);
            this.setEdicaoRateio(false);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public void editarRateiosModalidade() {
        try {
            this.setDescricaoInclusaoRateio("Edição de Rateio da Modalidade");
            Integer cdgMod = this.getCodigoModalidade();
            this.setCodigoProduto(0);
            this.setCodigoCategoria(0);
            this.rateio.setCodigoCategoria(0);
            this.rateio.setCodigoProduto(0);
            this.rateio.setCodigoModalidade(cdgMod);
            this.rateio.setDescricao("Rateio de Modalidade");

            // consultar os rateios já cadastrados
            List<RateioIntegracaoTO> rateiosModalidade = getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.MODALIDADE, cdgMod);
            this.setRateiosEdicao(rateiosModalidade);
            this.setAdicaoRateio(true);
            this.setEdicaoRateio(false);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public void editarRateiosProduto() {
        try {
            this.setDescricaoInclusaoRateio("Edição de Rateio de Produto");
            Integer cdgProd = this.getCodigoProduto();
            this.setCodigoCategoria(0);
            this.rateio.setCodigoCategoria(0);
            this.rateio.setCodigoProduto(cdgProd);
            this.rateio.setCodigoModalidade(0);
            this.rateio.setDescricao("Rateio de Produto");

            // consultar os rateios já cadastrados
            List<RateioIntegracaoTO> rateiosProdutos = getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.PRODUTO, cdgProd);
            this.setRateiosEdicao(rateiosProdutos);
            this.setAdicaoRateio(true);
            this.setEdicaoRateio(false);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public List<SelectItem> getPlanos() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        PlanoConta pj = new PlanoConta();
        List<PlanoContaTO> consultar = pj.consultar(null, null,null, PlanoContasControle.tipoConsulta.TODOS.name());

        for (PlanoContaTO objeto : consultar) {
            itens.add(new SelectItem(objeto.getCodigo() + ","
                    + objeto.getCodigoPlano() + "," + objeto.getDescricao(),
                    objeto.getDescricaoDetalhada()));
        }
        return itens;
    }

    /**
     * <AUTHOR> 23/08/2011
     */
    public List<SelectItem> getTipos() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        if (valorOneRadio == ValorRateioOneRadio.TODOS) {
            for (TipoRateioEnum tipoRateio : TipoRateioEnum.values()) {
                itens.add(new SelectItem(tipoRateio.getCodigo(), tipoRateio.getDescricao()));
            }
        } else if (valorOneRadio == ValorRateioOneRadio.SomenteCentroCusto) {
            itens.add(new SelectItem(TipoRateioEnum.CENTRO_CUSTOS.getCodigo(), TipoRateioEnum.CENTRO_CUSTOS.getDescricao()));
        } else if (valorOneRadio == ValorRateioOneRadio.SomentePlanoConta) {
            itens.add(new SelectItem(TipoRateioEnum.PLANO_CONTAS.getCodigo(), TipoRateioEnum.PLANO_CONTAS.getDescricao()));
        }
        return itens;
    }
    public Boolean mostrarComboEmpresa(){
        try {

            if(getUsuarioLogado().getUsuarioPerfilAcessoVOs().size() == 1
                    && !getUsuarioLogado().getSenha().equals("88f2f8d383d38e72184852b185825965a566b30c8a167e5bf13774bdeaaa0718")){

                UsuarioPerfilAcessoVO perfil = (UsuarioPerfilAcessoVO)getUsuarioLogado().getUsuarioPerfilAcessoVOs().get(0);
                for(PermissaoVO permissaoVO : perfil.getPerfilAcesso().getPermissaoVOs()){
                  if(permissaoVO.getNomeEntidade().equals("LancarVisualizarLancamentosEmpresas")){


                      return true;

                  }
                }
                rateio.setEmpresa(getEmpresaLogado().getCodigo());
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
       return true;
    }
    public List<SelectItem> getCentros() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        CentroCusto cc = new CentroCusto();
        List<CentroCustoTO> consultar = cc.consultar(null, null, null);

        for (CentroCustoTO objeto : consultar) {
            itens.add(new SelectItem(objeto.getCodigo() + ","
                    + objeto.getCodigoCentro() + "," + objeto.getDescricao(),
                    objeto.getDescricaoCurta()));
        }
        return itens;
    }

    private void carregar() {
        try {
            RateioIntegracao f = new RateioIntegracao();
            rateios = f.consultarRateiosCategorias();
        } catch (SQLException e) {
            tratarEx(e);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    private void carregarModalidade() {
        try {
            rateiosModalidade = getFacade().getFinanceiro().getRateioIntegracao().consultar();
        } catch (SQLException e) {
            tratarEx(e);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Integer getCodigoCategoria() {
        return codigoCategoria;
    }

    public void setCodigoCategoria(Integer codigoCategoria) {
        this.codigoCategoria = codigoCategoria;
    }

    public RateioIntegracaoTO getRateio() {
        if (rateio == null) {
            rateio = new RateioIntegracaoTO();
        }
        return rateio;
    }

    public void setRateio(RateioIntegracaoTO rateio) {
        this.rateio = rateio;
    }

    public void salvarRateioEdicao() {

        try {
            String descricao = rateio.getDescricao();
            Integer tipoRateio = rateio.getTipoRateio();
            // navegação
            this.setMensagemID("");
            this.setFecharmodal(false);
            preencherRateio();
            
            // valida se já existe um rateio para aquele mesmo
            // centro de custo e plano de conta
            if (rateiosEdicao.contains(rateio)) {
                rateiosEdicao.remove(rateio);
            }

            rateiosEdicao.add(rateio);

            limparDados();
            rateio.setDescricao(descricao);
            rateio.setTipoRateio(tipoRateio);

            this.setEdicaoRateio(false);
            this.setAdicaoRateio(true);

            limparMsg();
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public void adicionarRateio() {
        try {
            String descricao = rateio.getDescricao();
            Integer tipoRateio = rateio.getTipoRateio();
            // navegação
            this.setMensagemID("");
            this.setFecharmodal(false);
            preencherRateio();
            if (!UteisValidacao.emptyNumber(rateio.getEmpresa())) {
                for(SelectItem si : getListaEmpresas()){
                    if(si.getValue().equals(rateio.getEmpresa())){
                        rateio.setNomeEmpresa(si.getLabel());
                    }
                }
            }
            // valida se já existe um rateio para aquele mesmo
            // centro de custo e plano de conta
            for (RateioIntegracaoTO rateioObj : rateiosEdicao) {
                if (rateioObj.equals(rateio)) {
                    throw new Exception(
                            "Você não pode adicionar o mesmo rateio vários vezes!");
                }
            }

            this.getRateiosEdicao().add(rateio);
            RateioIntegracaoTO rateio2 = new RateioIntegracaoTO();
            rateio2.setCodigoProduto(this.getRateio().getCodigoProduto());
            rateio2.setCodigoModalidade(this.getRateio().getCodigoModalidade());
            rateio2.setCodigoCategoria(this.getRateio().getCodigoCategoria());
            this.setCodigoComboCentro("");
            rateio2.setPercentagemDesc("");
            this.setRateio(rateio2);

            limparDados();
            rateio.setDescricao(descricao);
            rateio.setTipoRateio(tipoRateio);

        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public void adicionarPlanoContasRateio() {
        try {
            String descricao = rateio.getDescricao();
            Integer tipoRateio = rateio.getTipoRateio();
            // navegação
            this.setMensagemID("");
            this.setFecharmodal(false);
            //
            preencherRateio();
            // valida se já existe um rateio para aquele mesmo
            // centro de custo e plano de conta
            for (RateioIntegracaoTO rateioObj : rateiosEdicao) {
                if (rateioObj.equals(rateio)) {
                    throw new Exception(
                            "Você não pode adicionar o mesmo rateio vários vezes!");
                }
            }
            this.rateio.setCodigoPlano("");
            this.rateio.setPlanoContasRateio(this.rateio.getCodigoPlanoContas());
            this.getRateiosEdicao().add(rateio);
            limparDados();
            rateio.setDescricao(descricao);
            rateio.setTipoRateio(tipoRateio);

        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     *
     * <AUTHOR> 15/08/2011
     */
    private void limparDados() {
        rateio = new RateioIntegracaoTO();
        limparCentroEPlano();
    }

    /**
     * <AUTHOR> 17/08/2011
     */
    private void limparCentroEPlano() {
        PlanoContasControle controlPlano = (PlanoContasControle) JSFUtilities.getFromSession(PlanoContasControle.class.getSimpleName());
        if (controlPlano != null) {
            controlPlano.setPlanoEscolhido(new PlanoContaTO());
            controlPlano.setPlanoNome("");
            JSFUtilities.storeOnSession(PlanoContasControle.class.getSimpleName(), controlPlano);
        }
        CentroCustosControle controlCentro = (CentroCustosControle) JSFUtilities.getFromSession(CentroCustosControle.class.getSimpleName());
        if (controlCentro != null) {
            controlCentro.setCentroEscolhido(new CentroCustoTO());
            controlCentro.setCentroNome("");
            JSFUtilities.storeOnSession(CentroCustosControle.class.getSimpleName(), controlCentro);
        }
    }

    private void tratarEx(Exception e) {
        this.setMensagemDetalhada("msg_erro", e.getMessage());
        this.setErro(true);
    }

    private void preencherRateio() throws Exception {

        PlanoContasControle controlPlano = (PlanoContasControle) JSFUtilities.getFromSession(PlanoContasControle.class.getSimpleName());
        PlanoContaTO plano = controlPlano.getPlanoEscolhido();


        CentroCustosControle controlCentro = (CentroCustosControle) JSFUtilities.getFromSession(CentroCustosControle.class.getSimpleName());
        CentroCustoTO centro = controlCentro.getCentroEscolhido();


        if ((plano == null || UteisValidacao.emptyNumber(plano.getCodigo()))
                && (centro == null || UteisValidacao.emptyNumber(centro.getCodigo()))) {
            throw new Exception("Informe um Plano de Conta ou um Centro de Custos.");
        }
        if (UteisValidacao.emptyString(this.rateio.getPercentagemDesc())) {
            throw new Exception("A percentagem deve ser informada.");
        }
        Double perc = Formatador.obterValorNumerico(rateio.getPercentagemDesc());
        rateio.setPercentagem(perc);
        if (UteisValidacao.emptyNumber(this.rateio.getPercentagem())
                || this.rateio.getPercentagem() < 0.0
                || this.rateio.getPercentagem() > 100.0) {
            throw new Exception(
                    "A percentagem deve ser superior a 0 e inferior a 100.");
        }
        if (rateio.getTipoRateio().equals(TipoRateioEnum.PLANO_CONTAS.getCodigo())) {
            // preenche os valores do plano
            rateio.setCodigoPlanoContas(plano.getCodigo());
            rateio.setCodigoPlano(plano.getCodigoPlano());
            rateio.setNomePlano(plano.getDescricao());
            rateio.setTipoES(plano.getTipoPadrao());
            rateio.setCodigoCentroCustos(0);
            rateio.setCodigoCentro("");
            rateio.setNomeCentro("");
        } else {
            // preenche os valores do centro
            rateio.setCodigoCentroCustos(centro.getCodigo());
            rateio.setCodigoCentro(centro.getCodigoCentro());
            rateio.setNomeCentro(centro.getDescricao());
            rateio.setCodigoPlanoContas(0);
            rateio.setCodigoPlano("");
            rateio.setNomePlano("");
        }
    }

    public void salvarRateios() {
        try {
            if (!isTipoOrdemCompra()) {
                this.validarRateios(rateiosEdicao, TipoRateioEnum.CENTRO_CUSTOS, null);
                this.validarRateios(rateiosEdicao, TipoRateioEnum.PLANO_CONTAS, null);
            }else{
                if(rateiosEdicao != null && rateiosEdicao.size() > 3){
                    throw new Exception("Para produtos do tipo ordem de compra só é tolerado 1 plano do tipo entrada," +
                            " 1 plano do tipo saida e 1 centro de custo, todos com porcentagem de 100%");
                }
                this.validarRateios(rateiosEdicao, TipoRateioEnum.PLANO_CONTAS, TipoES.ENTRADA);
                this.validarRateios(rateiosEdicao, TipoRateioEnum.PLANO_CONTAS, TipoES.SAIDA);
            }

            if (UteisValidacao.emptyNumber(this.getIdTipoSelecionado())) {
                PlanoContasControle controlPlano = (PlanoContasControle) JSFUtilities.getFromSession(PlanoContasControle.class.getSimpleName());
                if (controlPlano == null) {
                    controlPlano = new PlanoContasControle();
                }
                getFacade().getFinanceiro().getRateioIntegracao().excluirRateiosCentroCusto(controlPlano.getPlano().getCodigo());
                for (RateioIntegracaoTO rateio : rateiosEdicao) {
                    rateio.setPlanoContasRateio(controlPlano.getPlano().getCodigo());
                    rateio.setCodigoPlanoContas(0);
                }
                getFacade().getFinanceiro().getRateioIntegracao().incluir(rateiosEdicao, 0, 0);
            } else {
                getFacade().getFinanceiro().getRateioIntegracao().excluir(this.getIdEntidadeSelecionada(), this.getIdTipoSelecionado());
                getFacade().getFinanceiro().getRateioIntegracao().incluir(rateiosEdicao, this.getIdEntidadeSelecionada(), this.getIdTipoSelecionado());
            }
            this.carregar();
            this.carregarModalidade();
            this.setFecharmodal(true);
            this.setAbrirModalRateio(false);
            montarRateios();
            limparMsg();
        } catch (Exception e) {
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            this.setFecharmodal(false);
            this.setAbrirModalRateio(true);
            this.setErro(true);
        }
    }

    public void validarRateios(List<RateioIntegracaoTO> rateiosEdicao2, TipoRateioEnum tipoRateio, TipoES tipoES)
            throws Exception {

        Map<Integer, Double> valores = new HashMap<Integer, Double>();

        for (RateioIntegracaoTO obj : rateiosEdicao2) {
            if (obj.getTipoRateio().equals(tipoRateio.getCodigo()) && (tipoES == null || obj.getTipoES().equals(tipoES))) {
                Double percentagem = valores.get(obj.getEmpresa() == null ? 0 : obj.getEmpresa());
                if (percentagem == null) {
                    percentagem = 0.0;
                }
                percentagem += obj.getPercentagem();
                valores.put(obj.getEmpresa() == null ? 0 : obj.getEmpresa(), percentagem);
            }
        }
        Set<Integer> keySet = valores.keySet();
        for (Integer key : keySet) {
            Double percentagem = valores.get(key);
            if (percentagem != 100 && percentagem != 0) {
                throw new Exception(
                        "O total da soma das percentagens do tipo " + tipoRateio.getDescricao() +
                                ((isTipoOrdemCompra() && tipoES != null) ? " - " + tipoES.getDescricao() : "") + " deve ser igual a 100%.");
            }
        }
    }

    public String getDescricaoInclusaoRateio() {
        return descricaoInclusaoRateio;
    }

    public void setDescricaoInclusaoRateio(String descricaoInclusaoRateio) {
        this.descricaoInclusaoRateio = descricaoInclusaoRateio;
    }

    public List<RateioIntegracaoTO> getRateiosEdicao() {
        if (rateiosEdicao == null) {
            rateiosEdicao = new ArrayList<RateioIntegracaoTO>();
        }
        return rateiosEdicao;
    }

    public void setRateiosEdicao(List<RateioIntegracaoTO> rateiosEdicao) {
        this.rateiosEdicao = rateiosEdicao;
    }

    public void editarRateioEdicao() {
        limparMsg();
        RateioIntegracaoTO obj = (RateioIntegracaoTO) context().getExternalContext().getRequestMap().get("rateio");



        if (UteisValidacao.emptyNumber(obj.getCodigoPlanoContas())) {
            this.getRateio().setTipoRateio(TipoRateioEnum.CENTRO_CUSTOS.getCodigo());
        } else {
            this.getRateio().setTipoRateio(TipoRateioEnum.PLANO_CONTAS.getCodigo());
        }

        String descricao = this.getRateio().getDescricao();
        this.setRateio(obj);
        this.setRateioTemp(obj);
        this.getRateio().setDescricao(descricao);


        String percentagemDesc = Formatador.formatarValorMonetarioSemMoeda(obj.getPercentagem());
        this.getRateio().setPercentagemDesc(percentagemDesc);

        PlanoContaTO plano = new PlanoContaTO();
        plano.setCodigo(obj.getCodigoPlanoContas());
        plano.setCodigoPlano(obj.getCodigoPlano());
        plano.setDescricao(obj.getNomePlano());
        plano.setTipoPadrao(obj.getTipoES());
        JSFUtilities.setManagedBeanValue("PlanoContasControle.planoEscolhido", plano);
        JSFUtilities.setManagedBeanValue("PlanoContasControle.planoNome", plano.getDescricaoCurta());


        CentroCustoTO centro = new CentroCustoTO();
        centro.setCodigo(obj.getCodigoCentroCustos());
        centro.setCodigoCentro(obj.getCodigoCentro());
        centro.setDescricao(obj.getNomeCentro());
        JSFUtilities.setManagedBeanValue("CentroCustosControle.centroEscolhido", centro);
        JSFUtilities.setManagedBeanValue("CentroCustosControle.centroNome", centro.getDescricaoCurta());

        this.setAdicaoRateio(false);
        this.setEdicaoRateio(true);
        this.setFecharmodal(false);
    }

    public void removerRateioEdicao() {
        try {
            RateioIntegracaoTO obj = (RateioIntegracaoTO) context().getExternalContext().getRequestMap().get("rateio");
            int index = 0;
            Iterator<RateioIntegracaoTO> i = rateiosEdicao.iterator();
            while (i.hasNext()) {
                RateioIntegracaoTO objExistente = i.next();
                if (objExistente.equals(obj)) {
                    rateiosEdicao.remove(index);
                    return;
                }
                index++;
            }
        } catch (Exception e){
            montarErro(e);
        }
    }

    public void removerRateioCentroCustoEdicao() {
        try{
            RateioIntegracaoTO obj = (RateioIntegracaoTO) context().getExternalContext().getRequestMap().get("rateio");
            int index = 0;
            Iterator<RateioIntegracaoTO> i = rateiosEdicao.iterator();
            while (i.hasNext()) {
                RateioIntegracaoTO objExistente = i.next();
                if (objExistente.getCodigoCentroCustos().equals(obj.getCodigoCentroCustos())) {
                    rateiosEdicao.remove(index);
                    return;
                }
                index++;
            }
        } catch (Exception e){
            montarErro(e);
        }
    }

    public Boolean getMostrarEmpresa() {
          if(mostrarEmpresa == null)
            mostrarEmpresa = mostrarComboEmpresa();

        return mostrarEmpresa;
    }

    public void setMostrarEmpresa(Boolean mostrarEmpresa) {
        this.mostrarEmpresa = mostrarEmpresa;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public RateioIntegracaoTO getRateioTemp() {
        return rateioTemp;
    }

    public void setRateioTemp(RateioIntegracaoTO rateioTemp) {
        this.rateioTemp = rateioTemp;
    }

    public boolean isAdicaoRateio() {
        return adicaoRateio;
    }

    public void setAdicaoRateio(boolean adicaoRateio) {
        this.adicaoRateio = adicaoRateio;
    }

    public boolean isEdicaoRateio() {
        return edicaoRateio;
    }

    public void setEdicaoRateio(boolean edicaoRateio) {
        this.edicaoRateio = edicaoRateio;
    }

    public PlanoContaTO getPlanoSuggestion() {
        return planoSuggestion;
    }

    public void setPlanoSuggestion(PlanoContaTO planoSuggestion) {
        this.planoSuggestion = planoSuggestion;
    }

    public boolean isFecharmodal() {
        return fecharmodal;
    }

    public void setFecharmodal(boolean fecharmodal) {
        this.fecharmodal = fecharmodal;
    }

    public String getCodigoComboCentro() {
        return codigoComboCentro;
    }

    public void setCodigoComboCentro(String codigoComboCentro) {
        this.codigoComboCentro = codigoComboCentro;
    }

    //--------------------------- GETTERS AND SETTERS --------------------------------//
    /**
     * @param listaProdutos the listaProdutos to set
     */
    public void setListaProdutos(List<ProdutoVO> listaProdutos) {
        this.listaProdutos = listaProdutos;
    }

    /**
     * @return the listaProdutos
     */
    public List<ProdutoVO> getListaProdutos() {
        if (listaProdutos == null) {
            listaProdutos = new ArrayList<ProdutoVO>();
        }
        return listaProdutos;
    }

    /**
     * @param listaModalidades the listaModalidades to set
     */
    public void setListaModalidades(List<ModalidadeVO> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    /**
     * @return the listaModalidades
     */
    public List<ModalidadeVO> getListaModalidades() {
        if (listaModalidades == null) {
            listaModalidades = new ArrayList<ModalidadeVO>();
        }
        return listaModalidades;
    }

    /**
     * @param listaRateiosTree the listaRateiosTree to set
     */
    public void setListaRateiosProdutos(List<RateioTreeTO> listaRateiosTree) {
        this.listaRateiosProdutos = listaRateiosTree;
    }

    /**
     * @return the listaRateiosTree
     */
    public List<RateioTreeTO> getListaRateiosProdutos() {
        if (listaRateiosProdutos == null) {
            listaRateiosProdutos = new ArrayList<RateioTreeTO>();
        }
        return listaRateiosProdutos;
    }

    /**
     * @param listaRateiosModalidades the listaRateiosModalidades to set
     */
    public void setListaRateiosModalidades(List<RateioTreeTO> listaRateiosModalidades) {
        this.listaRateiosModalidades = listaRateiosModalidades;
    }

    /**
     * @return the listaRateiosModalidades
     */
    public List<RateioTreeTO> getListaRateiosModalidades() {
        if (listaRateiosModalidades == null) {
            listaRateiosModalidades = new ArrayList<RateioTreeTO>();
        }
        return listaRateiosModalidades;
    }

    /**
     * @param listaCategoriaProdutos the listaCategoriaProdutos to set
     */
    public void setListaCategoriaProdutos(List<CategoriaProdutoVO> listaCategoriaProdutos) {
        this.listaCategoriaProdutos = listaCategoriaProdutos;
    }

    /**
     * @return the listaCategoriaProdutos
     */
    public List<CategoriaProdutoVO> getListaCategoriaProdutos() {
        return listaCategoriaProdutos;
    }

    /**
     * @param ativarPool the ativarPool to set
     */
    public void setAtivarPool(boolean ativarPool) {
        this.ativarPool = ativarPool;
    }

    /**
     * @return the ativarPool
     */
    public boolean getAtivarPool() {
        return ativarPool;
    }

    /**
     * @param idEntidadeSelecionada the idEntidadeSelecionada to set
     */
    public void setIdEntidadeSelecionada(int idEntidadeSelecionada) {
        this.idEntidadeSelecionada = idEntidadeSelecionada;
    }

    /**
     * @return the idEntidadeSelecionada
     */
    public int getIdEntidadeSelecionada() {
        return idEntidadeSelecionada;
    }

    /**
     * @return the fecharmodal
     */
    public boolean getFecharmodal() {
        return fecharmodal;
    }

    /**
     * @param idTipoSelecionado the idTipoSelecionado to set
     */
    public void setIdTipoSelecionado(int idTipoSelecionado) {
        this.idTipoSelecionado = idTipoSelecionado;
    }

    /**
     * @return the idTipoSelecionado
     */
    public int getIdTipoSelecionado() {
        return idTipoSelecionado;
    }

    /**
     * @param nomeSelecionado the nomeSelecionado to set
     */
    public void setNomeSelecionado(String nomeSelecionado) {
        this.nomeSelecionado = nomeSelecionado;
    }

    /**
     * @return the nomeSelecionado
     */
    public String getNomeSelecionado() {
        return nomeSelecionado;
    }

    /**
     * @param abaSelecionada the abaSelecionada to set
     */
    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    /**
     * @return the abaSelecionada
     */
    public String getAbaSelecionada() {
        if (abaSelecionada == null) {
            abaSelecionada = "abaCategoria";
        }
        return abaSelecionada;
    }

    /**
     * <AUTHOR> 23/08/2011
     */
    public boolean getTipoCentro() {
        if (this.getRateio().getTipoRateio().equals(TipoRateioEnum.CENTRO_CUSTOS.getCodigo())) {
            return true;
        }
        return false;
    }

    /**
     * <AUTHOR> 23/08/2011
     */
    public boolean getTipoPlano() {
        if (this.getRateio().getTipoRateio().equals(TipoRateioEnum.PLANO_CONTAS.getCodigo())) {
            return true;
        }
        return false;
    }

    public void alternarTipo() {
        this.setFecharmodal(false);
    }

    /**
     * @param permiteCentroCustos the permiteCentroCustos to set
     */
    public void setPermiteCentroCustos(boolean permiteCentroCustos) {
        this.permiteCentroCustos = permiteCentroCustos;
    }

    /**
     * @return the permiteCentroCustos
     */
    public boolean getPermiteCentroCustos() {
        return permiteCentroCustos;
    }

    /**
     * @return the gravarPlanoContasRateio
     */
    public boolean isGravarPlanoContasRateio() {
        return gravarPlanoContasRateio;
    }

    /**
     * @param gravarPlanoContasRateio the gravarPlanoContasRateio to set
     */
    public void setGravarPlanoContasRateio(boolean gravarPlanoContasRateio) {
        this.gravarPlanoContasRateio = gravarPlanoContasRateio;
    }

    /**
     * @return the abrirModalRateio
     */
    public boolean isAbrirModalRateio() {
        return abrirModalRateio;
    }

    /**
     * @param abrirModalRateio the abrirModalRateio to set
     */
    public void setAbrirModalRateio(boolean abrirModalRateio) {
        this.abrirModalRateio = abrirModalRateio;
    }

    public void setListaRateiosCentralEventos(List<RateioTreeTO> listaRateiosCentralEventos) {
        this.listaRateiosCentralEventos = listaRateiosCentralEventos;
    }

    public List<RateioTreeTO> getListaRateiosCentralEventos() {
        return listaRateiosCentralEventos;
    }

    public void setConfFinanceiro(ConfiguracaoFinanceiroVO confFinanceiro) {
        this.confFinanceiro = confFinanceiro;
    }

    public ConfiguracaoFinanceiroVO getConfFinanceiro() {
        return confFinanceiro;
    }
}

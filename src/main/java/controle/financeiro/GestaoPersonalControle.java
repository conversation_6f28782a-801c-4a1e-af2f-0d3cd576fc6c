package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.basico.ItemRelatorioGestaoPersonalTO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.ItemTaxaPersonalVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.DescontoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoDesconto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR> Lhoji Shiozawa
 */
public class GestaoPersonalControle extends SuperControle {

    private List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();
    private List<ColaboradorVO> listaPersonal = new ArrayList<ColaboradorVO>();
    private List<DescontoVO> listaDescontos = new ArrayList<DescontoVO>();
    private List<ProdutoVO> listaProdutos = new ArrayList<ProdutoVO>();
    private MovParcelaVO parcela = new MovParcelaVO();
    private ItemTaxaPersonalVO aluno = new ItemTaxaPersonalVO();
    private EmpresaVO empresa = new EmpresaVO();
    private String valorConsultarPersonal = "";
    private String valorConsultarProduto = "";
    private String valorConsultarCliente = "";
    private Date mesReferencia = Calendario.hoje();
    private boolean abrirRichModalVinculo;
    private boolean abrirRichModalCliente;
    private UsuarioVO usuarioResponsavel;
    private boolean todosMarcados = false;
    private boolean negociada = true;
    private boolean paga = true;
    private boolean vencida = true;
    private boolean livre = true;
    private ItemRelatorioGestaoPersonalTO itemRelatorio = new ItemRelatorioGestaoPersonalTO();
    private List<ItemRelatorioGestaoPersonalTO> relatorioPersonais = new ArrayList<ItemRelatorioGestaoPersonalTO>();
    private int diaConfiguracaoSistema = 0;
    private ItemTaxaPersonalVO taxaDescontoPersonal;

    private Date dataLancamentoParcela = Calendario.hoje();
    private boolean apresentarDtLancamento = false;


    public GestaoPersonalControle() throws Exception {
        inicializarEmpresa();
        obterUsuarioLogado();
    }

    private void acaoNovo() throws Exception {
        obterDiaVencimentoConfiguracao();
        montarListaEmpresas();
        limparDados();
        inicializarEmpresa();
        inicializarUsuarioLogado();
        try {
            validarPermissao("DataLancamentoGestaoPersonal", "Permissão para alterar a data de lançamento da parcela em Gestão de Personal", getUsuarioLogado());
            setApresentarDtLancamento(true);
        } catch (Exception ex) {
            setApresentarDtLancamento(false);
        }
        setMensagemID("msg_entre_dados");
    }

    public String novo() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.GESTAO_PERSONAL.toString()));

        acaoNovo();
        return "gestaoPersonal";
    }

    public String novoRelatorio() throws Exception {
        acaoNovo();
        return "relatorioPersonal";
    }

    public void limparDados() {
        setAbrirRichModalVinculo(false);
        setAbrirRichModalCliente(false);
        setPersonal(new ColaboradorVO());
        setListaPersonal(new ArrayList<ColaboradorVO>());
        setListaAlunos(new ArrayList<ItemTaxaPersonalVO>());
        setListaDescontos(new ArrayList<DescontoVO>());
        setListaProdutos(new ArrayList<ProdutoVO>());
        setListaClientes(new ArrayList<ClienteVO>());
        setListaParcelas(new ArrayList<MovParcelaVO>());
        setParcela(new MovParcelaVO());
        setAluno(new ItemTaxaPersonalVO());
        setValorConsultarPersonal("");
        setValorConsultarProduto("");
        setValorConsultarCliente("");
        setValorTotal(0.0);
        setValorPago(0.0);
        setMesReferencia(Calendario.hoje());
        setRelatorioPersonais(new ArrayList<ItemRelatorioGestaoPersonalTO>());
        setDataLancamentoParcela(Calendario.hoje());
    }

    private void validarEntradaDados(boolean relatorio) throws Exception {
        if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
            throw new Exception("Informe a Empresa a Consultar.");
        }
        if (!relatorio && (getPersonal() == null || getPersonal().getCodigo() == 0)) {
            throw new Exception("Informe o Personal a Consultar.");
        }
    }

    public void consultarAlunos() throws Exception {
        try {
            setValorTotal(0.0);
            setListaAlunos(new ArrayList<>());
            setListaParcelas(new ArrayList<>());
            setAluno(new ItemTaxaPersonalVO());
            setParcela(new MovParcelaVO());
            validarEntradaDados(false);
            // busca o que ja foi processado no mes (Parcelas)
            consultaHistorico(getItemRelatorio());
            // busca todos os alunos do vinculo deste personal (Vínculos
            consultarVinculos(getItemRelatorio(), false);
            Ordenacao.ordenarLista(getItemRelatorio().getListaAlunos(), "nomeAluno_Apresentar");
            setMensagemDetalhada("", "");
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void consultarRelatorio() throws Exception {
        try {
            validarEntradaDados(true);
            setValorTotal(0.0);

            List<ColaboradorVO> personais = getFacade().getColaborador().consultarPorNomeTipoColaborador(getValorConsultarPersonal().trim(), getEmpresa().getCodigo(), false, true, Uteis.NIVELMONTARDADOS_MINIMOS,
                    TipoColaboradorEnum.PERSONAL_TRAINER, TipoColaboradorEnum.PERSONAL_INTERNO, TipoColaboradorEnum.PERSONAL_EXTERNO);

            setRelatorioPersonais(new ArrayList<ItemRelatorioGestaoPersonalTO>());

            for (ColaboradorVO colaborador : personais) {
                ItemRelatorioGestaoPersonalTO itemRelatorio = new ItemRelatorioGestaoPersonalTO();
                itemRelatorio.setPersonal(colaborador);
                consultaHistorico(itemRelatorio);
                consultarVinculos(itemRelatorio, true);
                getRelatorioPersonais().add(itemRelatorio);
            }

            filtrarRelatorioPersonais();

            calcularTotalRelatorio();

            setMensagemDetalhada("", "");
            notificarRecursoEmpresa(RecursoSistema.RELATORIO_DE_PERSONAL_CONSULTOU);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    private void filtrarRelatorioPersonais() {
        List<ItemRelatorioGestaoPersonalTO> listaFiltrada = new ArrayList<ItemRelatorioGestaoPersonalTO>();

        for (ItemRelatorioGestaoPersonalTO itemRelatorio : getRelatorioPersonais()) {
            boolean adicionarLista = false;
            List<ItemTaxaPersonalVO> listaAlunosTemp = new ArrayList<ItemTaxaPersonalVO>();
            for (ItemTaxaPersonalVO aluno : itemRelatorio.getListaAlunos()) {
                if ((isVencida() && aluno.isVencido())
                        || (isNegociada() && aluno.isGerado())
                        || (isLivre() && aluno.isLivre())
                        || (isPaga() && aluno.isPago())
                        || (!isVencida() && !isNegociada() && !isLivre() && !isPaga())) {
                    listaAlunosTemp.add(aluno);
                    adicionarLista = true;
                }
            }
            if (adicionarLista) {
                itemRelatorio.setListaAlunos(listaAlunosTemp);
                listaFiltrada.add(itemRelatorio);
            }
        }

        setRelatorioPersonais(listaFiltrada);
    }

    public void calcularTotalRelatorio() {
        setValorTotal(0.0);
        setValorPago(0.0);
        // percorre todos os alunos do personal
        for (ItemRelatorioGestaoPersonalTO itemRelatorio : getRelatorioPersonais()) {
            for (ItemTaxaPersonalVO aluno : itemRelatorio.getListaAlunos()) {

                setValorTotal(getValorTotal() + aluno.getValorFinal());
                if (aluno.isPago()) {
                    setValorPago(getValorPago() + aluno.getValorFinal());
                }
            }
        }
    }

    private void consultaHistorico(ItemRelatorioGestaoPersonalTO itemRelatorio) throws Exception {
        // busca o que ja foi processado no mes
        itemRelatorio.setListaParcelas(getFacade().getMovParcela().consultarPorMesReferenciaPersonal(
                getEmpresa().getCodigo(), itemRelatorio.getPersonal().getCodigo(),
                Uteis.getMesReferencia(mesReferencia) + "/" + Uteis.getAnoData(mesReferencia),
                Uteis.NIVELMONTARDADOS_TODOS));
        // percorre a lista de parcelas
        for (MovParcelaVO parc : itemRelatorio.getListaParcelas()) {
            // pega os alunos de cada parcela para incluir na lista da tela
            for (ItemTaxaPersonalVO item : parc.getPersonal().getAlunos()) {
                if (item.getDesconto().getCodigo().equals(0) && item.getDescontoEspecifico() > 0.00) {
                    item.getDesconto().setTipoDesconto(TipoDesconto.VA);
                    item.getDesconto().setValor(item.getDescontoEspecifico());
                }
                item.getProduto().setDesconto(item.getDesconto());
                ItemTaxaPersonalVO aux = itemRelatorio.adicionarAluno(item.getAluno(), item.getProduto());
                // deixa o checkbox marcado
                aux.setMarcado(true);
                // coloca a situacao do aluno
                if (parc.getSituacao().equals("PG")) {
                    aux.setSituacao(ItemTaxaPersonalVO.PAGO);
                } else {
                    if (Calendario.getDataComHoraZerada(Calendario.hoje()).after(Calendario.getDataComHoraZerada(parc.getDataVencimento()))) {
                        aux.setSituacao(ItemTaxaPersonalVO.VENCIDO);
                    } else {
                        aux.setSituacao(ItemTaxaPersonalVO.GERADO);
                    }
                }
            }
        }
    }

    public void consultarVinculos(ItemRelatorioGestaoPersonalTO itemRelatorio, boolean relatorio) throws Exception {
        // consulta todos os vinculos deste personal
        List<HistoricoVinculoVO> vinculos = getFacade().getHistoricoVinculo().consultarVinculoNaData(
                Uteis.obterUltimoDiaMes(getMesReferencia()), itemRelatorio.getPersonal().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL,
                TipoColaboradorEnum.PERSONAL_TRAINER, TipoColaboradorEnum.PERSONAL_INTERNO, TipoColaboradorEnum.PERSONAL_EXTERNO);
        // se nao possui nenhum aluno
        if (vinculos.isEmpty()) {
            setMensagemDetalhada("msg_dados_consultados_vazio", "Personal Não Possui Alunos Vinculados a Ele(a).");
        } else {
            // processa todos os vinculos encontrados
            for (HistoricoVinculoVO vinculo : vinculos) {
                // verifica se algum vinculo é valido
                if (vinculo.getTipoColaborador().equals(TipoColaboradorEnum.PERSONAL_TRAINER.getSigla())
                        || vinculo.getTipoColaborador().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                        || vinculo.getTipoColaborador().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())) {
                    itemRelatorio.adicionarAluno(vinculo.getCliente(), null, vinculo.getCliente().getEmpresa().getCodigo().intValue() == getEmpresa().getCodigo().intValue());
                }
            }
            if (!relatorio) {
                // se nenhum vinculo foi considerado valido
                if (itemRelatorio.getListaAlunos().isEmpty()) {
                    setMensagemDetalhada("msg_dados_consultados_vazio", "Personal Não Possui Alunos Vinculados Nesta Empresa.");
                }
            }
        }
    }

    public void validarPermissaoVinculo() throws Exception {
        try {
            setMensagemDetalhada("", "");
            setUsuarioResponsavel(getFacade().getControleAcesso().verificarLoginUsuario(getUsuarioResponsavel().getCodigo(), getUsuarioResponsavel().getSenha().toUpperCase()));
            if (getUsuarioResponsavel().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioResponsavel().getAdministrador()) {
                    setAbrirRichModalCliente(true);
                    return;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioResponsavel().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresa().getCodigo().intValue() == usuarioPerfilAcesso.getEmpresa().getCodigo().intValue()) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), getUsuarioResponsavel(), "Vinculo", "2.29 - Incluir Vínculos Cliente/Colaborador");
                }
            }
            setAbrirRichModalCliente(true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
            setAbrirRichModalCliente(false);
        }
    }

    public void confirmarAdicionarVinculos() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                setUsuarioResponsavel(auto.getUsuario());
                setExecutarAoCompletar("Richfaces.showModalPanel('panelCliente');");
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();

        auto.autorizar("Confirmar Adicionar Vínculo", "Vinculo",
                "Você precisa da permissão \"2.29 - Incluir Vínculos Cliente/Colaborador\"",
                "listaAlunos", listener);
    }

    public void consultarResponsavelVinculo() {
        try {
            setUsuarioResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void escolherAluno() {
        // pega os dados do produto a ser consultado
        ItemTaxaPersonalVO obj = (ItemTaxaPersonalVO) context().getExternalContext().getRequestMap().get("aluno");
        // separa para consulta
        setAluno(obj);
        setListaProdutos(new ArrayList<ProdutoVO>());
        setListaDescontos(new ArrayList<DescontoVO>());
    }

    public void consultarPersonal() {
        try {
            // consulta todos os colaborador que possuem o tipo PERSONAL TRAINER, interno ou externo
            setListaPersonal(getFacade().getColaborador().consultarPorNomeTipoColaborador(getValorConsultarPersonal().trim(), getEmpresa().getCodigo(), true, false, Uteis.NIVELMONTARDADOS_MINIMOS,
                    TipoColaboradorEnum.PERSONAL_TRAINER, TipoColaboradorEnum.PERSONAL_INTERNO, TipoColaboradorEnum.PERSONAL_EXTERNO));
            // se nao encontrou personal
            if (getListaPersonal().isEmpty()) {
                setMensagemDetalhada("msg_dados_consultados_vazio", "");
            }
        } catch (Exception e) {
            setListaPersonal(new ArrayList<ColaboradorVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarPersonal() throws Exception {
        try {
            ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("Personal");
            // coloca o colaborador na tela principal
            if (obj.getProdutoDefault() != null && obj.getProdutoDefault().getCodigo() > 0) {
                try {
                    obj.setProdutoDefault(getFacade().getProduto().consultarPorChavePrimaria(obj.getProdutoDefault().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                } catch (ConsistirException e) {
                    obj.setProdutoDefault(new ProdutoVO());
                }
            }
            setPersonal(obj);
            consultarAlunos();
            setMensagemDetalhada("", "");
            setValorConsultarPersonal("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarDesconto() {
        try {
            setListaDescontos(new ArrayList<DescontoVO>());
            // pega os dados do produto a ser consultado
            ItemTaxaPersonalVO obj = (ItemTaxaPersonalVO) context().getExternalContext().getRequestMap().get("aluno");
            // separa para consulta
            setAluno(obj);
            if (obj.getProduto().getCodigo() != 0) // consulta os descontos relativos ao produto
            {
                setListaDescontos(getFacade().getDesconto().consultarPorTipoProdutoPorEmpresa(obj.getProduto().getTipoProduto(), true, getEmpresaLogado().getCodigo()));
            }
            // se nao encontrou descontos
            if (getListaDescontos().isEmpty()) {
                setMensagemDetalhada("msg_dados_consultados_vazio", "");
            }
        } catch (Exception e) {
            setListaDescontos(new ArrayList<DescontoVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarDesconto() throws Exception {
        DescontoVO desconto = (DescontoVO) context().getExternalContext().getRequestMap().get("desconto");
        // pega o desconto selecionado e atribui ao produto escolhido anteriormente
        getAluno().getProduto().setDesconto(desconto);
        getAluno().setDesconto(desconto);
        setMensagemDetalhada("", "");
        calcularTotal();
    }

    public void limparCampoDesconto() {
        ItemTaxaPersonalVO aluno = (ItemTaxaPersonalVO) context().getExternalContext().getRequestMap().get("aluno");
        // tira o desconto dado anteriormente
        aluno.getProduto().setDesconto(new DescontoVO());
        aluno.setDesconto(new DescontoVO());
        calcularTotal();
    }

    public void consultarProduto() {
        try {
            // consulta todos os produtos do tipo taxa personal
            setListaProdutos(getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(getValorConsultarProduto(), "TP", false, Uteis.NIVELMONTARDADOS_TODOS));
            // se nao encontrou produtos
            if (getListaProdutos().isEmpty()) {
                setMensagemDetalhada("msg_dados_consultados_vazio", "");
            }
        } catch (Exception e) {
            setListaProdutos(new ArrayList<ProdutoVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarProduto() throws Exception {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
        // coloca o produto no aluno escolhido
        getAluno().setProduto(obj);
        getAluno().setDesconto(new DescontoVO());
        setMensagemDetalhada("", "");
        setValorConsultarProduto("");
        calcularTotal();
    }

    public void aplicarProdutoAlunosMarcados() throws Exception {
        for (ItemTaxaPersonalVO aluno : getItemRelatorio().getListaAlunos()) {
            ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("produto");
            if (aluno.isMarcado() && aluno.isLivre()) {
                aluno.setProduto((ProdutoVO) obj.getClone(true));
                aluno.setDesconto(new DescontoVO());
                setMensagemDetalhada("", "");
                setValorConsultarProduto("");
                calcularTotal();
            }
        }

    }

    public void editarDescontoProduto() throws Exception {
        ItemTaxaPersonalVO obj = (ItemTaxaPersonalVO) context().getExternalContext().getRequestMap().get("aluno");
        obj.getDesconto().setTipoDesconto(TipoDesconto.VA);
        obj.getProduto().setDesconto(obj.getDesconto());
        obj.setDesconto(obj.getDesconto());
        setMensagemDetalhada("", "");
        calcularTotal();
    }

    public void editarCampoDescontoProduto() throws Exception {
        if (taxaDescontoPersonal.getProduto().getValorFinal() > 0) {
            taxaDescontoPersonal.setMostrarDesconto(true);
        }
    }

    //LUCAS
    public void validarPermissaoDescontoProduto() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                editarCampoDescontoProduto();
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        ItemTaxaPersonalVO obj = (ItemTaxaPersonalVO) context().getExternalContext().getRequestMap().get("aluno");
        if (obj != null) {
            taxaDescontoPersonal = obj;
        }
        auto.autorizar("Autorização para lançar desconto Produto", "DescontoGestãodePersonal",
                "Você precisa da permissão \"5.59 - Desconto Gestão de Personal\"",
                "listaAlunos, tablepreviewtotal", listener);
    }

    public void consultarClientes() {
        try {
            // consulta os clientes pelo nome
            setListaClientes(getFacade().getCliente().consultarPorNomeCliente(getValorConsultarCliente().trim(),
                    getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS, 50));
            // se nao encontrou clientes com o nome informado
            if (getListaClientes().isEmpty()) {
                setMensagemDetalhada("msg_dados_consultados_vazio", "");
            }
        } catch (Exception e) {
            setListaClientes(new ArrayList<ClienteVO>());
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        try {
            limparMsg();
            // adiciona um vinculo entre o personal e o aluno se necessario
            verificarVinculo(obj, false);
            // coloca o aluno selecionado na lista do personal
            getItemRelatorio().adicionarAluno(obj, null);
            setMensagemDetalhada("", "");
            setValorConsultarCliente("");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void verificarVinculo(ClienteVO cliente, boolean excluir) throws Exception {
        // se esta no mes atual ou futuro
        if (Uteis.obterPrimeiroDiaMes(mesReferencia).compareTo(Uteis.obterPrimeiroDiaMes(Calendario.hoje())) >= 0) {
            // verifica se o cliente tem vinculo com o personal
            VinculoVO vinculo = getFacade().getVinculo().consultarVinculoPorCodigoColaboradorClienteTipoVinculo(
                    getPersonal().getCodigo(), cliente.getCodigo(), TipoColaboradorEnum.PERSONAL_TRAINER.getSigla(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            // se nao encontrou
            if (vinculo.getCodigo() == 0) {
                vinculo = getFacade().getVinculo().consultarVinculoPorCodigoColaboradorClienteTipoVinculo(
                        getPersonal().getCodigo(), cliente.getCodigo(), TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            }
            // se nao encontrou
            if (vinculo.getCodigo() == 0) {
                vinculo = getFacade().getVinculo().consultarVinculoPorCodigoColaboradorClienteTipoVinculo(
                        getPersonal().getCodigo(), cliente.getCodigo(), TipoColaboradorEnum.PERSONAL_INTERNO.getSigla(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            }

            // se nao existe nenhum tipo de vinculo do tipo personal entre eles
            if (vinculo.getCodigo() == 0 && !excluir) {
                // adiciona o vinculo
                vinculo.setCliente(cliente);
                vinculo.setColaborador(getItemRelatorio().getPersonal());
                vinculo.setTipoVinculo(getItemRelatorio().getPersonal().getTipoColaborador());
                if (Calendario.getMesAno(Calendario.hoje()).equals(Calendario.getMesAno(getMesReferencia()))) {
                    // Se o mês atual é igual ao mês de referência
                    getFacade().getVinculo().incluir(vinculo, null, "GESTÃO PERSONAL", false, getUsuarioResponsavel(), null);
                } else {
                    getFacade().getVinculo().incluir(vinculo, getMesReferencia(), "GESTÃO PERSONAL", false, getUsuarioResponsavel(), null);
                }
            }

            if (vinculo.getCodigo() != 0 && excluir) {
                List<VinculoVO> vinculoVOs = new ArrayList<VinculoVO>();
                vinculoVOs.add(vinculo);

                if (Calendario.getMesAno(Calendario.hoje()).equals(Calendario.getMesAno(getMesReferencia()))) {
                    // Se o mês atual é igual ao mês de referência
                    getFacade().getVinculo().removerVinculos(vinculoVOs, "GESTÃO PERSONAL", getUsuarioResponsavel(), true);
                }else {
                    getFacade().getVinculo().removerVinculos(vinculoVOs, "GESTÃO PERSONAL", getUsuarioResponsavel(), true, Calendario.hoje());
                }
            } else if (excluir) {
                getFacade().getHistoricoVinculo().incluirHistoricoVinculoSaidaGestaoPersonal(Uteis.obterUltimoDiaMes(getMesReferencia()), getPersonal().getCodigo(), cliente.getCodigo(), getUsuarioResponsavel());
            }
        }
    }

    private String tipoVinculoPersonal(ColaboradorVO personal) {
        // verifica todos os tipo do colaborador
        for (TipoColaboradorVO tipo : personal.getListaTipoColaboradorVOs()) {
            // pega o primeiro tipo de personal que encontrar
            if (tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_TRAINER.getSigla())
                    || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_INTERNO.getSigla())
                    || tipo.getDescricao().equals(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla())) {
                return tipo.getDescricao();
            }
        }
        // nao deve chegar aqui
        return "";
    }

    public void confirmarRetirarVinculos() {
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        limparMsg();
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                retirarCliente();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };
        auto.autorizar("Confirmar Remover Vínculo", "Vinculo",
                "Você precisa da permissão \"Vinculo\"",
                "listaAlunos, mensagem, totalLancado, mensagem2",
                listener);
    }

    public String getOnComplete() {
        return "";
    }

    public void retirarCliente() throws Exception {
        int j = 0;
        // necessario usar iterator para exclusao pois for each apresenta problema
        Iterator i = getItemRelatorio().getListaAlunos().iterator();
        // percorre todos os alunos
        while (i.hasNext()) {
            ItemTaxaPersonalVO gp = (ItemTaxaPersonalVO) i.next();
            // exclui os alunos selecionados e que nao possuem parcela gerada pra eles
            if (gp.isMarcado() && gp.isLivre()) {
                verificarVinculo(gp.getAluno(), true);
                i.remove();
                j++;
            }
        }
        setValorTotal(0.0);
        setMensagemDetalhada("", "");
        // se nao houve exclusao realizada
        if (j == 0) {
            setMensagemDetalhada("", "Selecione os Alunos que Deseja Retirar na Lista. Alunos Com Parcelas Geradas não Serão Considerados Para Exclusão.");
        }
    }

    public void verLista() {
        MovParcelaVO obj = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parcela");
        // seleciona a parcela para apresentar os produtos relacionados
        setParcela(obj);
    }

    public void marcarTodos() {
        for (ItemTaxaPersonalVO aluno : getItemRelatorio().getListaAlunos()) {
            if (aluno.isLivre()) {
                aluno.setMarcado(isTodosMarcados());
            }
        }
        calcularTotal();
    }

    public void todosMarcadosLista() {
        for (ItemTaxaPersonalVO aluno : getItemRelatorio().getListaAlunos()) {
            if (!aluno.isMarcado() && aluno.isLivre()) {
                setTodosMarcados(false);
                return;
            }
            setTodosMarcados(true);
        }
    }

    public void calcularTotal() {
        todosMarcadosLista();
        setValorTotal(0.0);
        // percorre todos os alunos do personal
        for (ItemTaxaPersonalVO aluno : getItemRelatorio().getListaAlunos()) {
            // soma somente os produtos marcados
            if (aluno.isMarcado() && aluno.isLivre()) {
                setValorTotal(getValorTotal() + aluno.getValorFinal());
            }
        }
    }

    public String fecharNegociacao() throws Exception {

        getFacade().getRisco().getCon().setAutoCommit(false);
        try {
            if (!Calendario.maiorOuIgual(pegarDiaVencimento(),dataLancamentoParcela)) {
                throw new ConsistirException("A data de lançamento não pode ser maior do que a data de vencimento da parcela.");
            }


            // gera uma parcela para todos os alunos selecionados validos
            MovParcelaVO parc = geraParcela(geraControle(geraListaAlunos()));
            // salva todas as informacoes (parcela, produto e historico)
            getFacade().getZWFacade().incluirMovParcelaSemValidar(parc);
            // prepara a tela de caixa em aberto com os dados do personal
            try {
                LogVO obj = new LogVO();
                ColaboradorVO colaborador = getFacade().getColaborador().consultarPorCodigoPessoa(parc.getPessoa().getCodigo(), parc.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                obj.setChavePrimaria(colaborador.getCodigo().toString());
                obj.setNomeEntidade("COLABORADOR");
                obj.setNomeEntidadeDescricao("Fechar Negociação");
                obj.setOperacao("INCLUSÃO");
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
                obj.setNomeCampo("TAXA DE PERSONAL");
                obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
                obj.setValorCampoAnterior("");
                obj.setValorCampoAlterado("INCLUIR TAXA DE GESTÃO DE PERSONAL \r\n Valor: " + Formatador.formatarValorMonetarioSemMoeda(parc.getValorParcela())
                        + "\nPERSONAL: " + getPersonal().getPessoa().getNome()
                        + "\nALUNO: " + getAluno().getNomeAluno());
                registrarLogObjetoVO(obj, colaborador.getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("Colaborador", parc.getCodigo(), "ERRO AO INCLUIR TAXA DE GESTÃO DE PERSONAL", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            getFacade().getRisco().getCon().commit();
            inicializarCaixa();
            setMensagemDetalhada("", "");
            return "tela8";
        } catch (Exception e) {
            getFacade().getRisco().getCon().rollback();
            montarErro(e);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }finally {
            getFacade().getRisco().getCon().setAutoCommit(true);
        }
        return "";
    }

    public String pagarParcela() throws Exception {
        inicializarCaixa();
        setMensagemDetalhada("", "");
        return "tela8";
    }

    private void inicializarCaixa() throws Exception {
        MovParcelaControle mpc = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        if (mpc == null) {
            mpc = new MovParcelaControle(getPersonal().getPessoa().getNome());
        } else {
            mpc.liberarBackingBeanMemoria("MovParcelaControle");
        }
        mpc.liberarBackingBeanMemoria("ContratoControle");
        mpc.setMatricula("");
        mpc.setValorConsulta(getPersonal().getPessoa().getNome());
        mpc.novo(false, true);
        context().getExternalContext().getSessionMap().put("MovParcelaControle", mpc);
    }

    private ControleTaxaPersonalVO geraControle(List<ItemTaxaPersonalVO> alunos) throws Exception {
        ControleTaxaPersonalVO ctrl = new ControleTaxaPersonalVO(0);
        ctrl.setEmpresa(getEmpresa());
        ctrl.setDataRegistro(getDataLancamentoParcela());
        ctrl.setPersonal(getPersonal());
        ctrl.setResponsavel(getUsuarioLogado());
        ctrl.setAlunos(alunos);
        return ctrl;
    }

    private MovParcelaVO geraParcela(ControleTaxaPersonalVO controle) throws Exception {
        MovParcelaVO par;
        calcularTotal();

        // prepara a parcela com os dados conhecidos
        par = new MovParcelaVO();
        par.setDataRegistro(controle.getDataRegistro());
        par.setDataVencimento(pegarDiaVencimento());
        par.setDescricao("PERSONAL TRAINER");
        par.setResponsavel(getUsuarioLogado());
        par.setSituacao("EA");
        par.setEmpresa(empresa);
        par.setPessoa(getPersonal().getPessoa());
        par.setValorParcela(getValorTotal());
        par.getContrato().setCodigo(0);
        par.setPersonal(controle);
        if (getValorTotal() == 0.0) {
            par.setSituacao("PG");
        }
        return par;
    }

    private void obterDiaVencimentoConfiguracao() throws Exception {
        ConfiguracaoSistemaVO config = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (config == null || config.getCodigo() == 0) {
            setDiaConfiguracaoSistema(1);
        } else {
            setDiaConfiguracaoSistema(config.getVencimentoColaborador());
        }
    }

    private Date pegarDiaVencimento() throws Exception {
        // pega o dia do vencimento configurado no personal
        int dia = getItemRelatorio().getPersonal().getDiaVencimento();
        int mes = Uteis.getMesData(mesReferencia) - 1; // subtrai um pois o metodo do Uteis adiciona 1, mas o calendar trabalha de 0 a 11
        int ano = Uteis.getAnoData(mesReferencia);
        int mesAtual = Uteis.getMesData(Calendario.hoje()) - 1; // subtrai um pois o metodo do Uteis adiciona 1, mas o calendar trabalha de 0 a 11
        Calendar diaVencimento = Calendario.getInstance();
        // a data do vencimento tem que ser relativa ao mes de referencia escolhido
        diaVencimento.set(Calendar.MONTH, mes);
        diaVencimento.set(Calendar.YEAR, ano);
        // se dia nao foi informado pega o dia default do sistema
        if (dia == 0) {
            // pega o codigo da configuracao do sistema

            ConfiguracaoSistemaVO config;
            // se nao encontrou considera o dia default = 1

            // busca a configuracao do sistema
            config = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            // se nao encontrou considera o dia default = 1
            if (config == null || config.getCodigo() == 0) {
                dia = 1;
            } else // pega o dia default
            {
                dia = config.getVencimentoColaborador();
            }

        }
        // coloca o dia do vencimento na data
        diaVencimento.set(Calendar.DAY_OF_MONTH, dia);
        // se o mes de referencia é o corrente e hoje é um dia posterior ao dia do vencimento
        if (mes == mesAtual && Calendario.hoje().after(diaVencimento.getTime())) // soma ao dia de hoje mais 3 dias para o vencimento da parcela
        {
            return Uteis.obterDataFutura2(Calendario.hoje(), 3);
        }
        // retorna o dia do vencimento
        return diaVencimento.getTime();
    }

    private MovProdutoVO geraProduto(ItemTaxaPersonalVO aux) throws Exception {
        // prepara um produto com os dados conhecidos
        MovProdutoVO produto = new MovProdutoVO();
        produto.setAnoReferencia(Uteis.getAnoData(mesReferencia));
        produto.setDataLancamento(dataLancamentoParcela);
        produto.setDescricao(aux.getProduto().getDescricao());
        produto.setEmpresa(getEmpresa());
        produto.setMesReferencia(Uteis.getMesReferencia(mesReferencia) + "/" + produto.getAnoReferencia());
        produto.setPessoa(getItemRelatorio().getPersonal().getPessoa());
        produto.setPrecoUnitario(aux.getProduto().getValorFinal());
        produto.setProduto(aux.getProduto());
        produto.setQuantidade(1);
        produto.setQuitado(false);
        produto.setResponsavelLancamento(getUsuario());
        produto.setSituacao("EA");
        produto.setTotalFinal(aux.getValorFinal());
        produto.setValorDesconto(aux.getProduto().getValorFinal() - aux.getValorFinal());

        return produto;
    }

    private List<ItemTaxaPersonalVO> geraListaAlunos() throws Exception {
        List<ItemTaxaPersonalVO> listaHistorico = new ArrayList<ItemTaxaPersonalVO>();
        // a partir da lista de alunos
        for (ItemTaxaPersonalVO aux : getItemRelatorio().getListaAlunos()) {
            // se aluno nao possui historico inclui
            if (aux.isLivre() && aux.isMarcado()) {
                if (aux.getProduto() == null || aux.getProduto().getCodigo() == 0) {
                    throw new Exception("O aluno " + aux.getAluno().getPessoa().getNome() + " não possui produto associado. Informe um produto antes de finalizar a negociação");
                }
                aux.setMovProduto(geraProduto(aux));
                listaHistorico.add(aux);
            }
        }
        // se nenhum historico foi gerado 
        if (listaHistorico.isEmpty()) {
            throw new Exception("Não Foi Possível Gerar Produtos Para Parcela. Verifique se há Algum Aluno Selecionado.");
        }
        return listaHistorico;
    }

    public final void inicializarEmpresa() throws Exception {
        if (isApresentarEmpresa()) {
            return;
        }
        empresa = getEmpresaLogado();
        if (empresa == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        setUsuarioResponsavel(new UsuarioVO());
        getUsuarioResponsavel().setCodigo(getUsuarioLogado().getCodigo());
        getUsuarioResponsavel().setUsername(getUsuarioLogado().getUsername());
    }

    public boolean isApresentarEmpresa() throws Exception {
        return getUsuarioLogado().getAdministrador();
    }

    public ColaboradorVO getPersonal() {
        return getItemRelatorio().getPersonal();
    }

    public void setPersonal(ColaboradorVO personal) {
        this.getItemRelatorio().setPersonal(personal);
    }

    @Override
    public EmpresaVO getEmpresa() {
        return empresa;
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getValorConsultarPersonal() {
        return valorConsultarPersonal;
    }

    public void setValorConsultarPersonal(String valorConsultarPersonal) {
        this.valorConsultarPersonal = valorConsultarPersonal;
    }

    public List<ColaboradorVO> getListaPersonal() {
        return listaPersonal;
    }

    public void setListaPersonal(List<ColaboradorVO> listaPersonal) {
        this.listaPersonal = listaPersonal;
    }

    public Date getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(Date mesReferencia) {
        if (mesReferencia != null)
            this.mesReferencia = mesReferencia;
    }

    public String getApresentarMesReferencia() {
        return Uteis.getMesNomeReferencia(mesReferencia) + "/" + Uteis.getAnoData(mesReferencia);
    }

    public double getValorTotal() {
        return getItemRelatorio().getValorTotal();
    }

    public void setValorTotal(double valorTotal) {
        this.getItemRelatorio().setValorTotal(valorTotal);
    }

    public double getValorPago() {
        return getItemRelatorio().getValorPago();
    }

    public void setValorPago(double valorPago) {
        this.getItemRelatorio().setValorPago(valorPago);
    }

    public List<ItemTaxaPersonalVO> getListaAlunos() {
        return getItemRelatorio().getListaAlunos();
    }

    public void setListaAlunos(List<ItemTaxaPersonalVO> listaAlunos) {
        this.getItemRelatorio().setListaAlunos(listaAlunos);
    }

    public List<DescontoVO> getListaDescontos() {
        return listaDescontos;
    }

    public void setListaDescontos(List<DescontoVO> listaDescontos) {
        this.listaDescontos = listaDescontos;
    }

    public String getValorConsultarProduto() {
        return valorConsultarProduto;
    }

    public void setValorConsultarProduto(String valorConsultarProduto) {
        this.valorConsultarProduto = valorConsultarProduto;
    }

    public List<ProdutoVO> getListaProdutos() {
        return listaProdutos;
    }

    public void setListaProdutos(List<ProdutoVO> listaProdutos) {
        this.listaProdutos = listaProdutos;
    }

    public ItemTaxaPersonalVO getAluno() {
        return aluno;
    }

    public void setAluno(ItemTaxaPersonalVO aluno) {
        this.aluno = aluno;
    }

    public String getValorConsultarCliente() {
        return valorConsultarCliente;
    }

    public void setValorConsultarCliente(String valorConsultarCliente) {
        this.valorConsultarCliente = valorConsultarCliente;
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaCliente) {
        this.listaClientes = listaCliente;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return getItemRelatorio().getListaParcelas();
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.getItemRelatorio().setListaParcelas(listaParcelas);
    }

    public MovParcelaVO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaVO parcela) {
        this.parcela = parcela;
    }

    public boolean isAbrirRichModalVinculo() {
        return abrirRichModalVinculo;
    }

    public void setAbrirRichModalVinculo(boolean abrirRichModalVinculo) {
        this.abrirRichModalVinculo = abrirRichModalVinculo;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public boolean isAbrirRichModalCliente() {
        return abrirRichModalCliente;
    }

    public void setAbrirRichModalCliente(boolean abrirRichModalCliente) {
        this.abrirRichModalCliente = abrirRichModalCliente;
    }

    public boolean isTodosMarcados() {
        return todosMarcados;
    }

    public void setTodosMarcados(boolean todosMarcados) {
        this.todosMarcados = todosMarcados;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        for (ItemTaxaPersonalVO item : getListaAlunos()) {
            item.setMesReferencia(getApresentarMesReferencia());
            item.setPersonal(getPersonal().getPessoa_Apresentar());
        }
        List listaParaImpressao = getListaAlunos();
        if(!(listaParaImpressao.size() == 0)) { // nada para ser impresso.
            exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
        }
    }

    public void exportarRelatorio(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        for (ItemRelatorioGestaoPersonalTO itemRelatorio : getRelatorioPersonais()) {
            for (ItemTaxaPersonalVO aluno : itemRelatorio.getListaAlunos()) {
                aluno.setMesReferencia(getApresentarMesReferencia());
                aluno.setPersonal(itemRelatorio.getPersonal().getPessoa_Apresentar());
            }
        }

        List listaParaImpressao = new ArrayList();

        for (ItemRelatorioGestaoPersonalTO itemRelatorio : getRelatorioPersonais()) {
            listaParaImpressao.addAll(itemRelatorio.getListaAlunos());
        }

        if(!(listaParaImpressao.size() == 0)){ // nada para ser impresso.
            exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
        }
    }

    public ItemRelatorioGestaoPersonalTO getItemRelatorio() {
        return itemRelatorio;
    }

    public void setItemRelatorio(ItemRelatorioGestaoPersonalTO itemRelatorio) {
        this.itemRelatorio = itemRelatorio;
    }

    public List<ItemRelatorioGestaoPersonalTO> getRelatorioPersonais() {
        return relatorioPersonais;
    }

    public void setRelatorioPersonais(List<ItemRelatorioGestaoPersonalTO> relatorioPersonais) {
        this.relatorioPersonais = relatorioPersonais;
    }

    public boolean isNegociada() {
        return negociada;
    }

    public void setNegociada(boolean negociada) {
        this.negociada = negociada;
    }

    public boolean isPaga() {
        return paga;
    }

    public void setPaga(boolean paga) {
        this.paga = paga;
    }

    public boolean isVencida() {
        return vencida;
    }

    public void setVencida(boolean vencida) {
        this.vencida = vencida;
    }

    public boolean isLivre() {
        return livre;
    }

    public void setLivre(boolean livre) {
        this.livre = livre;
    }

    public int getDiaConfiguracaoSistema() {
        return diaConfiguracaoSistema;
    }

    public void setDiaConfiguracaoSistema(int diaConfiguracaoSistema) {
        this.diaConfiguracaoSistema = diaConfiguracaoSistema;
    }

    public Date getDataLancamentoParcela() {
        return dataLancamentoParcela;
    }

    public void setDataLancamentoParcela(Date dataLancamentoParcela) {
        this.dataLancamentoParcela = dataLancamentoParcela;
    }

    public void setApresentarDtLancamento(boolean apresentarDtLancamento) {
        this.apresentarDtLancamento = apresentarDtLancamento;
    }

    public boolean isApresentarDtLancamento() {
        return apresentarDtLancamento;
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "Colaborador";
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), getPersonal().getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
        realizarConsultaLogObjetoSelecionado();
    }

    public boolean isPersonalSelecionado() {
        return getPersonal() != null && getPersonal().getCodigo() > 0;
    }
}

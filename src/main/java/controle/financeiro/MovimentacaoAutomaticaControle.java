package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.ConfiguracaoMovimentacaoAutomaticaVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class MovimentacaoAutomaticaControle extends SuperControleRelatorio{

    public EmpresaVO empresaVO = new EmpresaVO();
    public ConfiguracaoMovimentacaoAutomaticaVO nova = new ConfiguracaoMovimentacaoAutomaticaVO();
    public List<ConfiguracaoMovimentacaoAutomaticaVO> configs = new ArrayList<ConfiguracaoMovimentacaoAutomaticaVO>();
    public List<SelectItem> tipos;
    public List<SelectItem> formas;
    public List<SelectItem> contas;
    public List<SelectItem> adquirentes;
    public List<SelectItem> convenios;

    public MovimentacaoAutomaticaControle(){
        try {
            montarListas();
        }catch (Exception e){
            montarErro(e);
        }

    }

    public void montarListas() throws Exception{
        tipos = new ArrayList<SelectItem>();
        tipos.add(new SelectItem(null, ""));
        for(TipoFormaPagto tfp : TipoFormaPagto.values()){
            tipos.add(new SelectItem(tfp, tfp.getDescricao()));
        }

        formas = new ArrayList<SelectItem>();
        formas.add(new SelectItem(null, ""));
        formas.addAll(getFacade().getFormaPagamento().opcoesFormaEmpresa(empresaVO.getCodigo(), null));

        contas = new ArrayList<SelectItem>();
        contas.add(new SelectItem(null, ""));
        List<ContaVO> contaVOS = getFacade().getFinanceiro().getConta().consultarContasSimples(null, true);
        for(ContaVO c : contaVOS){
            contas.add(new SelectItem(c.getCodigo(), c.getDescricao()));
        }

        adquirentes = new ArrayList<SelectItem>();
        adquirentes.add(new SelectItem(null, ""));
        List<AdquirenteVO> adquirenteVOS = getFacade().getAdquirente().consultarTodos(true);
        for(AdquirenteVO a : adquirenteVOS){
            adquirentes.add(new SelectItem(a.getCodigo(), a.getNome()));
        }

        convenios = new ArrayList<SelectItem>();
        convenios.add(new SelectItem(null, ""));
        List<ConvenioCobrancaVO> convenioCobrancaVOS = getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for(ConvenioCobrancaVO cc : convenioCobrancaVOS){
            convenios.add(new SelectItem(cc.getCodigo(), cc.getDescricao()));
        }

    }

    public void abrirConfiguracoes(){
        try {
            if(UteisValidacao.emptyNumber(empresaVO.getCodigo())){
                GestaoRecebiveisControle controleGR = (GestaoRecebiveisControle) JSFUtilities.getManagedBean(GestaoRecebiveisControle.class.getSimpleName());
                empresaVO = controleGR.getEmpresaRel();
            }
            configs = getFacade().getMovimentacaoAutomatica().obterConfiguracoes(empresaVO.getCodigo());
            montarListas();
            setMsgAlert("Richfaces.showModalPanel('modalPanelConfigGestaoRecebiveis');");
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void adicionarNova(){
        try {
            setMsgAlert("");
            nova.setUsuario(getUsuarioLogado());
            nova.setEmpresa(empresaVO);
            if(UteisValidacao.emptyNumber(nova.getFormaPagamento().getCodigo())
                && UteisValidacao.emptyNumber(nova.getAdquirente().getCodigo())
                && UteisValidacao.emptyNumber(nova.getConvenio().getCodigo())
                && nova.getTipoFormaPagto() == null
            ){
                throw new Exception("Pelo menos uma origem deve ser informada.");
            }

            if(UteisValidacao.emptyNumber(nova.getConta().getCodigo())){
                throw new Exception("A conta de destino deve ser informada.");
            }

            getFacade().getMovimentacaoAutomatica().incluir(nova);
            configs = getFacade().getMovimentacaoAutomatica().obterConfiguracoes(0);
            nova = new ConfiguracaoMovimentacaoAutomaticaVO();
            montarSucessoDadosGravados();
        }catch (Exception e){
            montarErro(e);
        }
        setMsgAlert(getMensagemNotificar());
    }

    public void remover(){
        try {
            setMsgAlert("");
            ConfiguracaoMovimentacaoAutomaticaVO cfg = (ConfiguracaoMovimentacaoAutomaticaVO) context().getExternalContext().getRequestMap().get("cfgma");
            getFacade().getMovimentacaoAutomatica().remover(cfg);
            configs = getFacade().getMovimentacaoAutomatica().obterConfiguracoes(0);
            montarSucesso("msg_dados_excluidos");
        }catch (Exception e){
            montarErro(e);
        }
        setMsgAlert(getMensagemNotificar());
    }

    public List<ConfiguracaoMovimentacaoAutomaticaVO> getConfigs() {
        return configs;
    }

    public void setConfigs(List<ConfiguracaoMovimentacaoAutomaticaVO> configs) {
        this.configs = configs;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public ConfiguracaoMovimentacaoAutomaticaVO getNova() {
        return nova;
    }

    public void setNova(ConfiguracaoMovimentacaoAutomaticaVO nova) {
        this.nova = nova;
    }

    public List<SelectItem> getTipos() {
        return tipos;
    }

    public void setTipos(List<SelectItem> tipos) {
        this.tipos = tipos;
    }

    public List<SelectItem> getFormas() {
        return formas;
    }

    public void setFormas(List<SelectItem> formas) {
        this.formas = formas;
    }

    public List<SelectItem> getContas() {
        return contas;
    }

    public void setContas(List<SelectItem> contas) {
        this.contas = contas;
    }

    public List<SelectItem> getAdquirentes() {
        return adquirentes;
    }

    public void setAdquirentes(List<SelectItem> adquirentes) {
        this.adquirentes = adquirentes;
    }

    public List<SelectItem> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<SelectItem> convenios) {
        this.convenios = convenios;
    }
}

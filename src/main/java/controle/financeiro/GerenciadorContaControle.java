package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.filtros.FiltroResumoContaTO;
import negocio.comuns.financeiro.openbanking.stone.ContaStoneVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import servicos.pactobank.dto.ExtratoMovimentoZWDTO;

import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class GerenciadorContaControle extends SuperControleRelatorio {

    private ContaVO contaSelecionada = new ContaVO();
    private List<ContaVO> listaContas = new ArrayList<ContaVO>();
    private List<MovContaRateioVO> listaMovimentacoes = new ArrayList<MovContaRateioVO>();
    private Date inicio;
    private Date fim;
    private String periodo = "";
    private String saldoFinal = "";
    private String saldoAnterior = "";
    private String onCompleteNovaOperacao = "";
    private FormaPagamentoVO formaPagamento = null;
    private Double valorConciliar = 0.0;
    private UsuarioVO responsavelConciliar = new UsuarioVO();
    private double entradaTotal = 0;
    private double saidaTotal = 0;
    private Date dataConciliar = Calendario.hoje();
    private Double valorSaldoAtualConciliar = 0.0;
    private String observacaoConciliar = "";
    private boolean verMovimentacoes = false;
    private boolean vindoDoBI = false;
    private boolean saldoFinalPositivo = true;
    private boolean marcouItemResumoDeContas = false;
    private List<ExtratoMovimentoZWDTO> listaMovimentacoesOpenBank = new ArrayList<>();
    private boolean transferindoSaldoResumoContas = false;

    public boolean getVindoDoBI() {
        return vindoDoBI;
    }

    public void setVindoDoBI(boolean vindoDoBI) {
        this.vindoDoBI = vindoDoBI;
    }

    public void imprimir() throws Exception {
        try {
            setMsgAlert("");
            Map<String, Object> params = new HashMap<String, Object>();
            prepareParams(params);
            apresentarRelatorioObjetos(params);
            setMsgAlert("abrirPopupPDFImpressao('../../relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        String contaSelecionadaFormatada = Uteis.retirarAcentuacao(Uteis.removerMascara(contaSelecionada.getDescricao().replaceAll(" ", "_")));
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());

        params.put("tituloRelatorio", "Extrato da Conta - " + contaSelecionadaFormatada);
        params.put("nomeRelatorio", "Extrato da Conta - " + contaSelecionadaFormatada);
        params.put("nomeEmpresa", empre.getNome());

        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", listaMovimentacoes);
        params.put("periodo", getPeriodo());
        params.put("valorInicial", getSaldoAnterior());
        params.put("valorFinal", getSaldoFinal());
        params.put("totalEntrada", getEntradaApresentar());
        params.put("totalSaida", getSaidaApresentar());


        params.put("enderecoEmpresa", empre.getEndereco());
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "Movimentacoes.jrxml");
    }

    public String getValorSaldoAtualConciliarFormatado() {
        return Formatador.formatarValorNumerico(this.valorSaldoAtualConciliar, Formatador.FRMT_NUM_PADRAO);
    }

    public void confirmarConciliacaoConta() throws Exception {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                Double valor = valorConciliar - valorSaldoAtualConciliar;
                if (UteisValidacao.emptyNumber(valor)) {
                    throw new Exception("Não existe diferença entre o saldo atual da conta e o valor informado.");
                }

                TipoES tipoES = valor < 0.0 ? TipoES.SAIDA : TipoES.ENTRADA;
                valor = valor < 0.0 ? (valor * -1) : valor;
                MovContaVO movConta = new MovContaVO();
                movConta.setEmpresaVO(getContaSelecionada().getEmpresa());
                movConta.setUsuarioVO(auto.getUsuario());
                movConta.setPessoaVO(getFacade().getPessoa().consultarPessoaEmpresaFinan(getContaSelecionada().getEmpresa()));
                dataConciliar = Uteis.getDateTime(dataConciliar, 23, 59, 59);

                movConta.setDataCompetencia(dataConciliar);
                movConta.setDataQuitacao(dataConciliar);
                movConta.setDataVencimento(dataConciliar);
                movConta.setDataLancamento(Calendario.hoje());
                movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.AJUSTESALDO);
                movConta.setValor(valor);
                movConta.setDescricao("CONCILIAÇÃO DE SALDO" + (UteisValidacao.emptyString(observacaoConciliar) ? "" : ": " + observacaoConciliar));
                movConta.setContaVO(getContaSelecionada());
                movConta.setObservacoes(observacaoConciliar);

                MovContaRateioVO movContaRateio = new MovContaRateioVO();
                movContaRateio.setValor(valor);
                movContaRateio.setTipoES(tipoES);
                movContaRateio.setDescricao(movConta.getDescricao());

                movConta.getMovContaRateios().add(movContaRateio);

                CaixaControle controladorCaixa = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
                getFacade().getFinanceiro().getMovConta().incluir(movConta, controladorCaixa.getCaixaVoEmAberto().getCodigo(), false, null);

                OperacaoContaControle operacaoContaControle = new OperacaoContaControle();
                StringBuilder txtLog = new StringBuilder();
                txtLog.append("Conciliação'. 'Valor a Conciliar: ").append(Formatador.formatarValorMonetario(valorConciliar));
                txtLog.append("', 'Saldo Anterior: ").append(Formatador.formatarValorMonetario(valorSaldoAtualConciliar));

                if(movConta != null && UteisValidacao.emptyNumber(movConta.getCodigo())) {
                    operacaoContaControle.gerarLog(txtLog, Formatador.formatarValorMonetario(valorConciliar), null);
                } else if(movConta != null) {
                    operacaoContaControle.gerarLog(txtLog, Formatador.formatarValorMonetario(valorConciliar), movConta.getCodigo());
                }

                setMensagemDetalhada("msg_conciliacaoSucesso");
                setExecutarAoCompletar("Richfaces.hideModalPanel('modalConciliar'); atualizar();");
                limparMsg();

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        try {

            if (observacaoConciliar.length() > 77) {
                throw new Exception("A observação não pode conter mais que 77 caracteres.");
            }
            if (dataConciliar == null) {
                throw new Exception("Informe a data da conciliação.");
            }

            auto.autorizar("Confirmação de conciliar Saldo", "ConciliarSaldo",
                    "Você precisa da permissão \"9.20 - Conciliar Saldo\"",
                    "form", listener);

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarResponsavel() {
        try {
            responsavelConciliar = new Usuario().consultarPorChavePrimaria(responsavelConciliar.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void abrirConfirmacao() throws Exception {
        responsavelConciliar.setCodigo(getUsuarioLogado().getCodigo());
        consultarResponsavel();
    }

    public void validarPermissaoConciliarSaldo(UsuarioVO usuario) throws Exception {
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator<UsuarioPerfilAcessoVO> i = usuario.getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        usuario, "ConciliarSaldo", "9.20 - Conciliar Saldo");
            }
        }
    }

    public String resumoContas() {
        try {
            transferindoSaldoResumoContas = false; // limpar variavel
            verMovimentacoes = Boolean.FALSE;
            autorizarResumoContas();
            ContaVO filtro = new ContaVO();
            
            ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class.getSimpleName());
            if (!getUsuarioLogado().getAdministrador() 
                    && !(cfg.getConfFinanceiro().isPermitirContaOutraUnidade()
                    && validarPermissaoAbrirConsultarCaixaParaTodasEmpresa())) {
                filtro.setEmpresa(getEmpresaLogado());
            }
            consultarContas(filtro, getKey());
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
        return "resumoContas";
    }

    public void autorizarResumoContas() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "ResumoContas", "9.26 - Resumo de Contas");
            }
        }
    }

    public void exibirDetalhes() {
        try {
            setMsgAlert("");
            DemonstrativoFinanceiroControle dfControl = (DemonstrativoFinanceiroControle) getControlador(DemonstrativoFinanceiroControle.class.getSimpleName());
            dfControl.setListaLancamentosDF(new ArrayList<LancamentoDF>());
            List<Integer> movContas = new ArrayList<Integer>();
            Double total = 0.0;
            Double saida = 0.0;
            Double entrada = 0.0;

            boolean contem = false;

            for (MovContaRateioVO mcRateio : listaMovimentacoes) {
                if (mcRateio.getEscolhido() && !movContas.contains(mcRateio.getMovContaVO())) {
                    LancamentoDF lancamento = new LancamentoDF();
                    lancamento.setMovConta(mcRateio.getMovContaVO());
                    lancamento.setNomePessoa(mcRateio.getMovConta().getPessoaVO().getNome());
                    lancamento.setValorLancamento(mcRateio.getTipoES().equals(TipoES.ENTRADA) ? mcRateio.getMovConta().getValor() : (mcRateio.getMovConta().getValor() * -1));
                    lancamento.setDescricaoLancamento(mcRateio.getDescricao());
                    lancamento.setTipoES(mcRateio.getTipoES());
                    movContas.add(mcRateio.getMovContaVO());
                    total = total + lancamento.getValorLancamento();
                    if (mcRateio.getTipoES().equals(TipoES.ENTRADA)) {
                        entrada = entrada + mcRateio.getMovConta().getValor();
                    } else {
                        saida = saida + mcRateio.getMovConta().getValor();
                    }

                    dfControl.getListaLancamentosDF().add(lancamento);
                    contem = true;
                }
            }
            if (!contem) {
                setMsgAlert("alert('Selecione algum lançamento para ser exibido.');");
                return;
            }
            dfControl.setResumoContas(true);
            dfControl.setNomeAgrupadorSelecionado(getContaSelecionada().getDescricao());
            dfControl.setTotalRegistros(dfControl.getListaLancamentosDF().size());

            dfControl.setTotalSelecionadoSaida(saida);
            dfControl.setTotalSelecionadoEntrada(Formatador.formatarValorMonetarioSemMoeda(entrada));
            dfControl.setTotalSelecionado(total);
            Calendar inicio = Calendar.getInstance();
            dfControl.totalizadorMesSelecionado = new TotalizadorMesDF();
            dfControl.totalizadorMesSelecionado.setMesProcessar(new MesProcessar());
            dfControl.totalizadorMesSelecionado.getMesProcessar().setDataIni(inicio);
            Calendar fim = Calendar.getInstance();
            dfControl.totalizadorMesSelecionado.getMesProcessar().setDataFim(fim);
            setMsgAlert("Richfaces.showModalPanel('modalLancamentos');");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    /**
     * <AUTHOR> Alcides 19/11/2012
     */
    public void consultarContas() throws Exception {
        listaContas = getFacade().getFinanceiro().getConta().consultar(new ContaVO(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA, "");
    }

    public void consultarContas(ContaVO filtro, String chaveZW) throws Exception {
        listaContas = getFacade().getFinanceiro().getConta().consultar(filtro, true, Uteis.NIVELMONTARDADOS_TELACONSULTA, chaveZW);
    }

    public String getDtInicioApresentar(){
        return Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM");
    }

    private void determinarSemanaAtual() {
        Calendar calendar = Calendario.getInstance();
        calendar.setTime(Calendario.hoje());
        int difEntreSegunda = Calendar.MONDAY - calendar.get(Calendar.DAY_OF_WEEK);
        calendar.add(Calendar.DAY_OF_WEEK, difEntreSegunda);
        setInicio(Calendario.getDataComHoraZerada(calendar.getTime()));
        calendar.add(Calendar.DAY_OF_WEEK, 6);
        setFim(Calendario.getDataComHora(calendar.getTime(), "23:59:59"));
        restaurarFiltros();
    }

    public void selecionarConta() {
        try {
            transferindoSaldoResumoContas = false; //selecionou uma conta, preencher a variável
            notificarRecursoEmpresa(RecursoSistema.RESUMO_CONTAS_MOVIMENTACAO);
            vindoDoBI = false;
            selecionarContaSemTratarEx((ContaVO) context().getExternalContext().getRequestMap().get("conta"));
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    private void selecionarContaSemTratarEx(ContaVO conta) throws Exception {
        contaSelecionada = conta;
        verMovimentacoes = Boolean.TRUE;
        determinarSemanaAtual();
        atualizar();
    }

    public String visualizarContaBI() {
        try {
            notificarRecursoEmpresa(RecursoSistema.RESUMO_CONTAS_MOVIMENTACAO);
            vindoDoBI = true;
            GenericoTO generico = (GenericoTO) context().getExternalContext().getRequestMap().get("conta");
            ContaVO conta = getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(generico.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            selecionarContaSemTratarEx(conta);
            return "resumoContas";
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public String abrirLancamentoRapido() {
        try {
            MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
            movContaControle.iniciarNovoLancamentoRapido();
            return "lancamentoFinanceiroRapido";
        }catch (Exception e){
            limparMsg();
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public void voltarContas() {
        verMovimentacoes = Boolean.FALSE;
    }

    public void conciliarSaldo() {
        try {
            setDataConciliar(Calendario.hoje());
            setValorSaldoAtualConciliar(getContaSelecionada().getSaldoAtual());
            setObservacaoConciliar("");
            setValorConciliar(null);
            CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
            caixaControle.validarCaixaAbertoConta(getContaSelecionada().getCodigo(), false);
            setMsgAlert("Richfaces.showModalPanel('modalConciliar');");
        } catch (Exception e) {
            limparMsg();
            montarMsgAlert(e.getMessage());
        }
    }

    public void transferirOpenBankMesmaConta() {
        novaOperacaoGeral(true, false);
    }

    public void transferirOpenBankOutrosBancos() {
        novaOperacaoGeral(false, true);
    }

    public void novaOperacao() {
        novaOperacaoGeral(false, false);
    }

    public void novaOperacaoGeral(boolean openBankMesmaConta, boolean openBankOutrosBancos) {
        try {
            setTransferindoSaldoResumoContas(true);
            if(!getContaSelecionada().isContaIntegracaoOpenBank()) {
                CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
                caixaControle.validarCaixaAbertoConta(getContaSelecionada().getCodigo(), false);
            }

            setOnCompleteNovaOperacao("");
            Set<Integer> selecionados = new HashSet<Integer>();
            formaPagamento = null;
            Double valorNovaOperacao = validarEscolhidos(selecionados);

            OperacaoContaControle operacaoContaControle = (OperacaoContaControle) getControlador(OperacaoContaControle.class.getSimpleName());
            if (selecionados.isEmpty()) {
                if(getContaSelecionada().isContaIntegracaoOpenBank()) {
                    setOnCompleteNovaOperacao(operacaoContaControle.abrirModal(null, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.SAIDA,
                            TipoFormaPagto.AVISTA, valorNovaOperacao, false, contaSelecionada, true, true, false, openBankMesmaConta, openBankOutrosBancos));
                } else {
                    setOnCompleteNovaOperacao(operacaoContaControle.abrirModal(null, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.ENTRADA,
                            TipoFormaPagto.AVISTA, valorNovaOperacao, false, contaSelecionada, true, true, false));
                }
            } else {

                LoteVO novoLote = new LoteVO();
                novoLote.setDataDeposito(Calendario.hoje());
                novoLote.setDataLancamento(Calendario.hoje());
                novoLote.setUsuarioResponsavel(getUsuarioLogado());
                novoLote.setEmpresa(getEmpresaLogado());
                novoLote.setValor(valorNovaOperacao);

                if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
                    //transferencia de lancamentos em cheque
                    novoLote.setCheques(new ArrayList<ChequeVO>());
                    for (Integer movConta : selecionados) {
                        novoLote.getCheques().addAll(getFacade().getCheque().consultarPorMovContaLote(movConta, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }
                    setOnCompleteNovaOperacao(operacaoContaControle.abrirModal(novoLote, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.ENTRADA,
                            TipoFormaPagto.CHEQUE, valorNovaOperacao, true, contaSelecionada, false, false, false, formaPagamento, null, null,0));
                } else //transferencia de lancamentos em cartao
                    if (formaPagamento.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                        novoLote.setCartoes(new ArrayList<CartaoCreditoVO>());
                        for (Integer movConta : selecionados) {
                            novoLote.getCartoes().addAll(
                                    getFacade().getCartaoCredito().consultarPorMovContaLote(movConta, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        }
                        setOnCompleteNovaOperacao(operacaoContaControle.abrirModal(novoLote, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.ENTRADA,
                                TipoFormaPagto.CARTAOCREDITO, valorNovaOperacao, true, contaSelecionada, false, false, false, formaPagamento, null, null,0));
                    } //transferencia de lancamentos em dinheiro
                    else {
                        setOnCompleteNovaOperacao(operacaoContaControle.abrirModal(null, TipoOperacaoLancamento.TRANSFERENCIA, TipoES.ENTRADA,
                                TipoFormaPagto.AVISTA, valorNovaOperacao, false, contaSelecionada, false, false, false, formaPagamento, null, null,0));
                    }
            }
        } catch (Exception e) {
            setOnCompleteNovaOperacao("alert('" + e.getMessage() + "')");

        }

    }

    /**
     * <AUTHOR> Alcides 06/11/2012
     */
    private Double validarEscolhidos(Set<Integer> selecionados) throws ConsistirException {
        Double valorSelecionados = 0.0;
        // iterar movs
        for (MovContaRateioVO mov : listaMovimentacoes) {
            if (mov.getEscolhido()) {
                if (mov.getTipoES().equals(TipoES.SAIDA)) {
                    throw new ConsistirException("Lançamentos de saída não podem ser transferidos.");
                }
                if (mov.getFormaPagamentoVO().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
                    throw new ConsistirException("Transferências de cheques só poderão ser realizadas na Gestão de Recebíveis.");
                }
                valorSelecionados += mov.getValor();
                selecionados.add(mov.getMovContaVO());
                if (formaPagamento == null) {
                    formaPagamento = mov.getFormaPagamentoVO();
                } else {
                    if (!formaPagamento.getTipoFormaPagamento().equals(mov.getFormaPagamentoVO().getTipoFormaPagamento())) {
                        throw new ConsistirException("Selecione lançamentos com mesma forma de pagamento.");
                    }
                }

            }
        }
        return valorSelecionados;
    }

    /**
     * <AUTHOR> Alcides 30/10/2012
     */
    public void atualizar(){

        try{
            setMensagemDetalhada("");
            if(inicio == null){
                throw new Exception("A data de início da pesquisa deve ser informada!");
            }
            if(fim == null){
                throw new Exception("A data final da pesquisa deve ser informada!");
            }
            if(Calendario.maior(inicio, fim)){
                throw new Exception("A data de inicio da pesquisa deve ser menor ou igual a data final!");
            }
            entradaTotal = 0;
            saidaTotal = 0;
            periodo = Uteis.getData(inicio) + " - " + Uteis.getData(fim);
            salvarFiltroSessao();
            if (contaSelecionada.isContaIntegracaoOpenBank() && !UteisValidacao.emptyString(getKey())) {
                listaMovimentacoesOpenBank = getFacade().getFinanceiro().getConta().consultarExtratoContaStoneOpenBank(getKey(), contaSelecionada.getEmpresa().getCodigo(),
                        inicio, fim, 100, 1, 2);
                tratarSaldosOpenBank(listaMovimentacoesOpenBank);
            } else {
                listaMovimentacoes = getFacade().getFinanceiro().getMovContaRateio().consultarPorConta(contaSelecionada.getCodigo(), inicio, fim);
                Double saldoInicial = (getFacade().getFinanceiro().getConta().saldoAte(contaSelecionada.getCodigo(), Uteis.somarDias(inicio, -1)));
                saldoAnterior = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(saldoInicial);
                preencherSaldo(saldoInicial, listaMovimentacoes);
                contaSelecionada.setSaldoAtual(getFacade().getFinanceiro().getConta().saldoAte(contaSelecionada.getCodigo(), fim));
                saldoFinal = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(contaSelecionada.getSaldoAtual());
                calcularTotais();
            }
        } catch(Exception e){
            montarErro(e);
        }
    }

    private void tratarSaldosOpenBank(List<ExtratoMovimentoZWDTO> listaMovimentacoesOpenBank) {
        if(listaMovimentacoesOpenBank.size() != 0) {
            Ordenacao.ordenarLista(listaMovimentacoesOpenBank, "dateCreate_TimesTamp");
            int tamanhoLista = listaMovimentacoesOpenBank.size();
            ExtratoMovimentoZWDTO primeiroExtrato = listaMovimentacoesOpenBank.get(0);
            ExtratoMovimentoZWDTO ultimoExtrato = listaMovimentacoesOpenBank.get(tamanhoLista - 1);
            saldoAnterior = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(Double.valueOf((float) primeiroExtrato.getBalance_before()/100));
            saldoFinal = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(Double.valueOf((float) ultimoExtrato.getBalance_after()/100));
        } else {
            saldoAnterior = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(0.0);
            saldoFinal = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(0.0);
        }
    }


    public void realizarConsultaLogOperacaoConta() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");

        loginControle.setNomeClasse("MOVIMENTACAORECEBIVEIS");
        loginControle.consultarLogObjetoSelecionado("MOVIMENTACAORECEBIVEIS", 0, 0,false);
    }

    private void preencherSaldo(Double saldo, List<MovContaRateioVO> listaMovimentacoes) {
        saldo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(saldo);
        for (MovContaRateioVO mRateio : listaMovimentacoes) {
            saldo = mRateio.getTipoES().equals(TipoES.ENTRADA) ? saldo + Uteis.arredondarForcando2CasasDecimais(mRateio.getValor()) : saldo - Uteis.arredondarForcando2CasasDecimais(mRateio.getValor());
            mRateio.setSaldo(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(saldo));
        }
    }

    public void obterValorSaldoDataRetroativaConciliar() {
        try {
            valorSaldoAtualConciliar = getFacade().getFinanceiro().getConta().saldoAte(contaSelecionada.getCodigo(), dataConciliar);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    private void calcularTotais() {
        for (MovContaRateioVO movContaRateioVO : listaMovimentacoes) {
            if (movContaRateioVO.getTipoES().equals(TipoES.ENTRADA)) {
                entradaTotal += movContaRateioVO.getValor();
            } else if (movContaRateioVO.getTipoES().equals(TipoES.SAIDA)) {
                saidaTotal += movContaRateioVO.getValor();
            }
        }
    }

    public void setContaSelecionada(ContaVO contaSelecionada) {
        this.contaSelecionada = contaSelecionada;
    }

    public ContaVO getContaSelecionada() {
        return contaSelecionada;
    }

    public void setListaContas(List<ContaVO> listaContas) {
        this.listaContas = listaContas;
    }

    public List<ContaVO> getListaContas() {
        return listaContas;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Date getFim() {
        return fim;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public String getPeriodo() {
        return periodo;
    }

    public void setListaMovimentacoes(List<MovContaRateioVO> listaMovimentacoes) {
        this.listaMovimentacoes = listaMovimentacoes;
    }

    public List<MovContaRateioVO> getListaMovimentacoes() {
        return listaMovimentacoes;
    }

    public void setSaldoFinal(String saldoAtual) {
        this.saldoFinal = saldoAtual;
    }

    public String getSaldoFinal() {
        return saldoFinal;
    }

    public void setSaldoAnterior(String saldoAnterior) {
        this.saldoAnterior = saldoAnterior;
    }

    public String getSaldoAnterior() {
        return saldoAnterior;
    }


    public void setOnCompleteNovaOperacao(String onCompleteNovaOperacao) {
        this.onCompleteNovaOperacao = onCompleteNovaOperacao;
    }

    public String getOnCompleteNovaOperacao() {
        return onCompleteNovaOperacao;
    }

    public void setValorConciliar(Double valorConciliar) {
        this.valorConciliar = valorConciliar;
    }

    public Double getValorConciliar() {
        return valorConciliar;
    }

    public void setResponsavelConciliar(UsuarioVO responsavelConciliar) {
        this.responsavelConciliar = responsavelConciliar;
    }

    public UsuarioVO getResponsavelConciliar() {
        return responsavelConciliar;
    }

    public String getEntradaApresentar() {
        List<MovContaRateioVO> listaMovimentacoesSelecionadas = getListaMovimentacoesSelecionadas();

        if (listaMovimentacoesSelecionadas.isEmpty()) {
            return Formatador.formatarValorMonetarioSemMoeda(getEntradaTotal());
        }
        return Formatador.formatarValorMonetarioSemMoeda(getTotalValorMovimentacoesSelecionadas(true));
    }

    public String getSaidaApresentar() {
        List<MovContaRateioVO> listaMovimentacoesSelecionadas = getListaMovimentacoesSelecionadas();

        if (listaMovimentacoesSelecionadas.isEmpty()) {
            return Formatador.formatarValorMonetarioSemMoeda(getSaidaTotal());
        }

        return Formatador.formatarValorMonetarioSemMoeda(getTotalValorMovimentacoesSelecionadas(false));
    }

    public double getEntradaTotal() {
        return entradaTotal;
    }

    public void setEntradaTotal(double entradaTotal) {
        this.entradaTotal = entradaTotal;
    }

    public double getSaidaTotal() {
        return saidaTotal;
    }

    public void setSaidaTotal(double saidaTotal) {
        this.saidaTotal = saidaTotal;
    }

    public void setDataConciliar(Date dataConciliar) {
        this.dataConciliar = dataConciliar;
    }

    public Date getDataConciliar() {
        return dataConciliar;
    }

    public void setObservacaoConciliar(String observacaoConciliar) {
        this.observacaoConciliar = observacaoConciliar;
    }

    public String getObservacaoConciliar() {
        return observacaoConciliar;
    }

    public void setValorSaldoAtualConciliar(Double valorSaldoAtualConciliar) {
        this.valorSaldoAtualConciliar = valorSaldoAtualConciliar;
    }

    public Double getValorSaldoAtualConciliar() {
        return valorSaldoAtualConciliar;
    }

    public void setVerMovimentacoes(boolean verMovimentacoes) {
        this.verMovimentacoes = verMovimentacoes;
    }

    public boolean getVerMovimentacoes() {
        return verMovimentacoes;
    }

    public boolean validarPermissaoAbrirConsultarCaixaParaTodasEmpresa() throws Exception {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "AbrirConsultarHistCaixaAdmTodasEmpresas", "9.25 - Abrir/Consultar Caixa-Administrativo para todas as Empresas");
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * Obtém uma lista de movimentações onde estiver selecionado.<br>
     *
     * @return
     */
    public List<MovContaRateioVO> getListaMovimentacoesSelecionadas() {
        List<MovContaRateioVO> movimentacoesSelecionadas = new ArrayList<MovContaRateioVO>();
        for (MovContaRateioVO movContaRateioVO : listaMovimentacoes) {
            if (Boolean.TRUE == movContaRateioVO.getEscolhido()) {//null-safe
                movimentacoesSelecionadas.add(movContaRateioVO);
            }
        }
        return movimentacoesSelecionadas;
    }


    /**
     * Obtém o total de entrada ou saídas das movimentações selecionadas
     * @param entrada
     * @return
     */
    public double getTotalValorMovimentacoesSelecionadas(boolean entrada) {
        List<MovContaRateioVO> listaMovimentacoesSelecionadas = getListaMovimentacoesSelecionadas();
        double totalMovimentacoesSelecionadas = 0.0D;
        for (MovContaRateioVO movContaRateioVO : listaMovimentacoesSelecionadas) {
            if ((entrada && movContaRateioVO.getTipoES() == TipoES.ENTRADA)
                    || !entrada && movContaRateioVO.getTipoES() == TipoES.SAIDA) {
                totalMovimentacoesSelecionadas += movContaRateioVO.getValor();
            }
        }
        return totalMovimentacoesSelecionadas;
    }

    /**
     * Obtém um mapa totalizador dos valores das movimentações selecionadas agrupadas por tipo de operação de lançamento
     * @return
     */
    public List<SelectItem> getTotalMovimentacoesSelecionadasPorTipoOperacao(){

        setMarcouItemResumoDeContas(false);
        Map<TipoOperacaoLancamento, Double> movimentacoesPorTipoOperacao = new HashMap<TipoOperacaoLancamento, Double>();
        for (MovContaRateioVO movimentacaoSelecionada : getListaMovimentacoesSelecionadas()){
            TipoOperacaoLancamento tipoOperacaoLancamento = movimentacaoSelecionada.getMovConta().getTipoOperacaoLancamento();
            Double totalMovimentacaoPorTipoOperacao = movimentacoesPorTipoOperacao.get(tipoOperacaoLancamento);
            if (totalMovimentacaoPorTipoOperacao != null) {
                totalMovimentacaoPorTipoOperacao += movimentacaoSelecionada.getValor();
            } else {
                totalMovimentacaoPorTipoOperacao = movimentacaoSelecionada.getValor();
            }
            movimentacoesPorTipoOperacao.put(tipoOperacaoLancamento, totalMovimentacaoPorTipoOperacao);
        }
              Map<TipoOperacaoLancamento, String> totalizadoresParaApresentacao = new HashMap<TipoOperacaoLancamento, String>();
        List<SelectItem> itensSelecionados = new ArrayList<SelectItem>();

        for (Map.Entry<TipoOperacaoLancamento, Double> entry : movimentacoesPorTipoOperacao.entrySet()) {
            String valor = Formatador.formatarValorMonetario(entry.getValue());
            String label = entry.getKey().getDescricao();
            itensSelecionados.add(new SelectItem(valor, label));
            totalizadoresParaApresentacao.put(entry.getKey(), valor);
            setMarcouItemResumoDeContas(true);
        }
        return itensSelecionados;
    }

    public String getSaldoApresentacao() {
        setSaldoFinalPositivo(true);
        List<MovContaRateioVO> movimentacoesSelecionadas = getListaMovimentacoesSelecionadas();
        if (movimentacoesSelecionadas.isEmpty()) {
            return saldoFinal;
        }
        double totalEntrada = getTotalValorMovimentacoesSelecionadas(true);
        double totalSaida = getTotalValorMovimentacoesSelecionadas(false);
        if (totalEntrada - totalSaida < 0.0) { //saldo negativo, exibir cor vermelha
            setSaldoFinalPositivo(false);
        }
        return Uteis.formataDuasCasasDecimaisEPontoCasasMilenioMantendoSinal(totalEntrada - totalSaida);

    }

    public String getCorExibirSaldoFinalResumoContas() {
        //Marcou algum item
        if (isMarcouItemResumoDeContas()) {
            if (isSaldoFinalPositivo()) {
                return "#008000"; //verde
            }
            return "#da2128"; //vermelho
        } else if (getSaldoFinal().contains("-")){ //Nenhum item marcado
            return "#da2128"; //vermelho
        }
        return "#008000"; //verde
    }

    public boolean isAlgumaMovimentacaoNaoSelecionada() {
        return getListaMovimentacoesSelecionadas().size() != listaMovimentacoes.size();
    }

    /**
     * Responde à ação de selecionar/desmarcar todas as movimentações
     */
    public void selecionarOuDesmarcarTodasMovimentacoes() {
        boolean selecionarTodas = isAlgumaMovimentacaoNaoSelecionada();
        for (MovContaRateioVO movContaRateioVO : listaMovimentacoes) {
            movContaRateioVO.setEscolhido(selecionarTodas);
        }
    }
    public void salvarFiltroSessao() {
        FiltroResumoContaTO filtroResumoContaTO = new FiltroResumoContaTO();
        filtroResumoContaTO.setDataInicioConsulta(this.inicio);
        filtroResumoContaTO.setDataFimConsulta(this.fim);
        JSFUtilities.storeOnSession(FiltroResumoContaTO.class.getName()+"_"+contaSelecionada.getCodigo(), filtroResumoContaTO);
    }

    public void restaurarFiltros(){
        FiltroResumoContaTO filtroSessao = (FiltroResumoContaTO) JSFUtilities.getFromSession(FiltroResumoContaTO.class.getName()+"_"+contaSelecionada.getCodigo());
        if (filtroSessao != null ){
            inicio = filtroSessao.getDataInicioConsulta();
            fim = filtroSessao.getDataFimConsulta();
        }
    }

    public List<ExtratoMovimentoZWDTO> getListaMovimentacoesOpenBank() {
        return listaMovimentacoesOpenBank;
    }

    public void setListaMovimentacoesOpenBank(List<ExtratoMovimentoZWDTO> listaMovimentacoesOpenBank) {
        this.listaMovimentacoesOpenBank = listaMovimentacoesOpenBank;
    }

    public boolean isContaStoneIncluida(){
        try {
            return !UteisValidacao.emptyString(getFacade().getContaStone().buscarAccountIdContaStone(getEmpresaLogado().getCodigo()));
        }catch (Exception e){
            return false;
        }
    }

    public String getContaStoneDetalhes(){
        try {
            ContaStoneVO contaStone = getFacade().getContaStone().buscarContaStone(getEmpresaLogado().getCodigo());
            return "Nome: "+contaStone.getOwner_name()+", "+"Agencia: "+contaStone.getBranch_code()+", "+"Conta: "+contaStone.getAccount_code()+".";
        }catch (Exception e){
            return "Falha ao obter dados da conta";
        }
    }

    public boolean isSaldoFinalPositivo() {
        return saldoFinalPositivo;
    }

    public void setSaldoFinalPositivo(boolean saldoFinalPositivo) {
        this.saldoFinalPositivo = saldoFinalPositivo;
    }

    public boolean isMarcouItemResumoDeContas() {
        return marcouItemResumoDeContas;
    }

    public void setMarcouItemResumoDeContas(boolean marcouItemResumoDeContas) {
        this.marcouItemResumoDeContas = marcouItemResumoDeContas;
    }

    public boolean isTransferindoSaldoResumoContas() {
        return transferindoSaldoResumoContas;
    }

    public void setTransferindoSaldoResumoContas(boolean transferindoSaldoResumoContas) {
        this.transferindoSaldoResumoContas = transferindoSaldoResumoContas;
    }
}

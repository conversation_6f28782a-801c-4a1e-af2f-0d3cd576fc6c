package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe controladora dos cadastros de conta
 * 
 * <AUTHOR>
 */
public class ContaControle extends SuperControle {
    // objeto do formulário de cadastro

    private ContaVO contaVO;
    // combo de tipos de conta (filtro da consulta)
    private boolean apresentarComboTipo;
    private boolean apresentarComboEmpresa;
    private boolean apresentarComboBanco;
    // consulta dos dados
    private List listaConsulta;

    public ContaControle() throws Exception {
        setContaVO(new ContaVO());
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        realizarConsultaLogObjetoSelecionado();
    }

    /**
     * Chamado para criar uma nova conta
     *
     * @return navigation-case
     */
    public String novo() {
        setContaVO(new ContaVO());
        setMensagemID("msg_entre_dados");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    public void abrirConta() {
        try {
            validarPermissaoConta();
            String context = request().getRequestURL().toString().split("faces")[0];
            setMsgAlert("abrirPopup('"+context+"faces/finanContaCons.jsp?modulo=financeiroWeb', 'Contas', 800, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    /**
     * Valida a permissão do usuário logado para a entidade Conta
     * que usa a permissão "Conta"
     * @throws Exception
     */
    public void validarPermissaoConta() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "Conta", "9.06 - Conta");
            }
        }
    }

    public String editar() throws Exception{
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));

        setContaVO(getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(codigoConsulta,Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS));
        getContaVO().setTipoDeConta(getContaVO().getTipoConta().getCodigo().toString());
        contaVO.registrarObjetoVOAntesDaAlteracao();
        return "editar";
    }

    // /CONSULTA PAGINADA
    public String consultar() {
        try {
            super.consultar();
            List objs;

            if (getControleConsulta().getCampoConsulta().equals("tipoConta")){
                contaVO.setEmpresa(new EmpresaVO());
                contaVO.setBanco(new BancoVO());
            } else if (getControleConsulta().getCampoConsulta().equals("banco")) {
                contaVO.setTipoConta(new TipoContaVO());
                contaVO.setEmpresa(new EmpresaVO());
            } else if (getControleConsulta().getCampoConsulta().equals("empresa")) {
                contaVO.setBanco(new BancoVO());
                contaVO.setTipoConta(new TipoContaVO());
            }

            objs = getFacade().getFinanceiro().getConta().consultar(contaVO, false, Uteis.NIVELMONTARDADOS_TELACONSULTA, "");
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Grava a conta
     *
     * @return
     */
    public String gravar() {
        try {
            if (contaVO.isNovoObj().booleanValue()) {
                contaVO.setObjetoVOAntesAlteracao( new ContaVO());
                getFacade().getFinanceiro().getConta().incluir(contaVO);
                getContaVO().setTipoDeConta(getContaVO().getTipoConta().getCodigo().toString());
                contaVO.formatarObservacao();
                //LOG - INICIO
                inicializarLog();
                //LOG - FIM
            } else {
                getFacade().getFinanceiro().getConta().alterar(contaVO);
                getContaVO().setTipoDeConta(getContaVO().getTipoConta().getCodigo().toString());
                contaVO.formatarObservacao();
                //LOG - INICIO
                incluirLogAlteracao();
                //LOG - FIM
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(contaVO, contaVO.getCodigo(), "CONTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTA", contaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE CONTA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        contaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void inicializarLog() throws Exception{
        try {
            contaVO.setNovoObj(true);
            registrarLogObjetoVO(contaVO, contaVO.getCodigo(), "CONTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTA", contaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE CONTA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        contaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "CONTA";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                getContaVO().getCodigo(), 0);
    }



    public String consultarFiltroPadrao() {
        getControleConsulta().setCampoConsulta("empresa");
        habilitarComboTipos();
        return consultar();
    }

    /**
     * Acionado na tela de edição, exclui a conta
     *
     * @return navigation-case
     */
    public String excluir() {
        try {
            getFacade().getFinanceiro().getConta().excluir(contaVO);
            incluirLogExclusao();
            setContaVO(new ContaVO());
            consultarFiltroPadrao();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("empresa", "Empresa"));
        itens.add(new SelectItem("banco", "Banco"));
        itens.add(new SelectItem("tipoConta", "Tipo de Conta"));
        return itens;
    }

    /**
     * Combo de Empresas
     *
     * @return List - SelectItem
     */
    public List getEmpresaCombo() {
        List itens = new ArrayList();
        try {
            itens.add(new SelectItem("", ""));
            List consultarTodas = getFacade().getEmpresa().consultarTodas(true,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Iterator iterator = consultarTodas.iterator(); iterator.hasNext();) {
                EmpresaVO obj = (EmpresaVO) iterator.next();
                itens.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return itens;
    }

    /**
     * Combo de tipos de conta
     *
     * @return List - SelectItem
     */
    public List getTipoContaCombo() {

        List itens = new ArrayList();
        try {
            itens.add(new SelectItem("", ""));
            List consultarTodas = getFacade().getFinanceiro().getTipoConta().consultar(new TipoContaVO());
            for (Iterator iterator = consultarTodas.iterator(); iterator.hasNext();) {
                TipoContaVO obj = (TipoContaVO) iterator.next();
                itens.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return itens;
    }

    /**
     * Combo de bancos
     *
     * @return List - SelectItem
     */
    public List getBancoCombo() {

        List itens = new ArrayList();
        try {
            itens.add(new SelectItem("", ""));
            List consultarTodas = getFacade().getBanco().consultarTodos(
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Iterator iterator = consultarTodas.iterator(); iterator.hasNext();) {
                BancoVO obj = (BancoVO) iterator.next();
                itens.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return itens;
    }

    /**
     * Decide se será exibido a combo de tipos de conta
     */
    public void habilitarComboTipos() {
        if (getControleConsulta().getCampoConsulta().equals("tipoConta")) {
            apresentarComboTipo = true;
            apresentarComboEmpresa = false;
            apresentarComboBanco = false;
            getContaVO().getEmpresa().setCodigo(0);
            getContaVO().getBanco().setCodigo(0);
        } else if (getControleConsulta().getCampoConsulta().equals("banco")) {
            apresentarComboTipo = false;
            apresentarComboEmpresa = false;
            apresentarComboBanco = true;
            getContaVO().getEmpresa().setCodigo(0);
            getContaVO().getTipoConta().setCodigo(0);
        } else {
            apresentarComboTipo = false;
            apresentarComboEmpresa = true;
            apresentarComboBanco = false;
            getContaVO().getTipoConta().setCodigo(0);
            getContaVO().getBanco().setCodigo(0);
        }

    }

    // GETTERS AND SETTERS
    public List getListaConsulta() {
        return listaConsulta;
    }

    public void setListaConsulta(List listaConsulta) {
        this.listaConsulta = listaConsulta;
    }

    public ContaVO getContaVO() {
        return contaVO;
    }

    public void setContaVO(ContaVO contaVO) {
        this.contaVO = contaVO;
    }

    public boolean isApresentarComboTipo() {
        return apresentarComboTipo;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getFinanceiro().getConta().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public void setApresentarComboTipo(boolean apresentarComboTipo) {
        this.apresentarComboTipo = apresentarComboTipo;
    }

    public boolean isApresentarComboEmpresa() {
        //caso nenhuma das combos seja exibida, exibir a de empresa
        if (!apresentarComboBanco && !apresentarComboTipo) {
            apresentarComboEmpresa = true;
        }
        return apresentarComboEmpresa;
    }

    public void setApresentarComboEmpresa(boolean apresentarComboEmpresa) {
        this.apresentarComboEmpresa = apresentarComboEmpresa;
    }

    public boolean isApresentarComboBanco() {
        return apresentarComboBanco;
    }

    public void setApresentarComboBanco(boolean apresentarComboBanco) {
        this.apresentarComboBanco = apresentarComboBanco;
    }

    public void incluirLogExclusao() throws Exception {
        try {
            contaVO.setObjetoVOAntesAlteracao(new ContaVO());
            contaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(contaVO, contaVO.getCodigo(), "CONTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTA", contaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONTA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();

        }
    }
}

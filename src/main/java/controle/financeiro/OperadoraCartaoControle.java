package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum;
import negocio.comuns.financeiro.enumerador.*;
import negocio.interfaces.financeiro.OperadoraCartaoInterfaceFacade;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import servicos.impl.geoitd.enums.BandeirasGeoitdEnum;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * operadoraCartaoForm.jsp operadoraCartaoCons.jsp) com as funcionalidades da classe <code>OperadoraCartao</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see negocio.facade.jdbc.financeiro.OperadoraCartao
 * @see OperadoraCartaoVO
 */
public class OperadoraCartaoControle extends SuperControle {

    private OperadoraCartaoVO operadoraCartaoVO;
    private String msgCartoesNaoAceitos = "";
    private List<SelectItem> listaBandeirasGeoitd = new ArrayList<SelectItem>();
    private ConfiguracaoSistemaVO configuracaoSistema;
    /**
     * Interface <code>OperadoraCartaoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private OperadoraCartaoInterfaceFacade operadoraCartaoFacade = null;
    private String situacaoFiltro;
    private String msgAlert;

    public OperadoraCartaoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        inicializarConfiguracaoSistema();
        setMensagemID("");
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }
    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>OperadoraCartao</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        limparMsg();
        setOperadoraCartaoVO(new OperadoraCartaoVO());
        getOperadoraCartaoVO().setAtivo(true);
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>OperadoraCartao</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            OperadoraCartaoVO obj = getFacade().getOperadoraCartao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setOperadoraCartaoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    public String gravar() {
        return gravar(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    public String gravarCE() {
        return gravar(true);
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>OperadoraCartao</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (operadoraCartaoVO.isNovoObj()) {
                    operadoraCartaoFacade.incluir(operadoraCartaoVO, true);
                    //LOG - INICIO
                    try {
                        operadoraCartaoVO.setObjetoVOAntesAlteracao(new OperadoraCartaoVO());
                        operadoraCartaoVO.setNovoObj(true);
                        registrarLogObjetoVO(operadoraCartaoVO, operadoraCartaoVO.getCodigo(), "OPERADORA CARTAO CE", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("OPERADORA CARTAO CE", operadoraCartaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE OPERADORA CARTAO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                } else {
                    operadoraCartaoFacade.alterar(operadoraCartaoVO, true);
                    //LOG - INICIO
                    try {
                        registrarLogObjetoVO(operadoraCartaoVO, operadoraCartaoVO.getCodigo(), "OPERADORA CARTAO CE", 0);
                    } catch (Exception e) {
                        registrarLogErroObjetoVO("OPERADORA CARTAO CE", operadoraCartaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE OPERADORA CARTAO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                        e.printStackTrace();
                    }
                    //LOG - FIM
                }
            } else {
                if (operadoraCartaoVO.isNovoObj()) {
//                    if(isUruguay()){
//                        operadoraCartaoVO.setGeoitd(isUruguay());
//                        operadoraCartaoVO.setCodBandeiraGeoitd(operadoraCartaoVO.getBandeirasGeoitd().getCodcIssuerGeoitd());
//                        operadoraCartaoVO.setCodigoOperadora(Integer.valueOf(operadoraCartaoVO.getBandeirasGeoitd().getCodAcquirerGeoitd()));
//                    }

                    boolean exiteOperadoraCartaoComEsseCodigoIntegracao = operadoraCartaoFacade.existeOperadoraComCodigoIntegracao(operadoraCartaoVO);
                    if (exiteOperadoraCartaoComEsseCodigoIntegracao) {
                        throw new Exception("Já existe uma operadora de cartão com esse código de integração. Identificar e Editar a operadora de cartão existente.");
                    }

                    operadoraCartaoFacade.incluir(operadoraCartaoVO);
                    incluirLogInclusao();
                } else {
                  //  operadoraCartaoVO.setGeoitd(isUruguay());
                    operadoraCartaoFacade.alterar(operadoraCartaoVO);
                    incluirLogAlteracao();
                }
            }
            montarSucesso("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP OperadoraCartaoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = operadoraCartaoFacade.consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("codigoOperadora")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = operadoraCartaoFacade.consultarPorCodigoOperadora(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = operadoraCartaoFacade.consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    public void metodoVazio() {
        setListaConsulta(getListaConsulta());
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    public String excluirCE() {
        return excluir(true);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    public String excluir() {
        return excluir(false);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>OperadoraCartaoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                operadoraCartaoFacade.excluir(operadoraCartaoVO, true);

                //LOG - INICIO
                try {
                    registrarLogExclusaoObjetoVO(operadoraCartaoVO, operadoraCartaoVO.getCodigo(), "OPERADORA CARTAO CE", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("OPERADORA CARTAO CE", operadoraCartaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE OPERADORA CARTAO CE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM

            } else {
                operadoraCartaoFacade.excluir(operadoraCartaoVO);
                incluirLogExclusao();
            }
            novo();
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"operadoracartao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"operadoracartao\" violates foreign key")){
                setMensagemDetalhada("Esta Operadora de Cartão não pode ser excluída, pois está sendo utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    /**
     * Rotina responsável por atribui um javascript com o método de mascara para campos do tipo Data, CPF, CNPJ, etc.
     */
    public String getMascaraConsulta() {
        return "";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("codigoOperadora", "Código Operadora"));
        itens.add(new SelectItem("descricao", "Descrição"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setListaConsulta(new ArrayList());
        limparMsg();
        return "consultar";
    }

    /**
     * Operação que libera todos os recursos (atributos, listas, objetos) do backing bean.
     * Garantindo uma melhor atuação do Garbage Coletor do Java. A mesma é automaticamente
     * quando realiza o logout.
     */
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        operadoraCartaoVO = null;
        operadoraCartaoFacade = null;
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            operadoraCartaoFacade = getFacade().getOperadoraCartao();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public OperadoraCartaoVO getOperadoraCartaoVO() {
        return operadoraCartaoVO;
    }

    public void setOperadoraCartaoVO(OperadoraCartaoVO operadoraCartaoVO) {
        this.operadoraCartaoVO = operadoraCartaoVO;
    }

    public List getListaOperadorasExternas() {
        return OperadorasExternasPagamentoDigitalEnum.getSelectListTipo();
    }

    public List getListaOperadorasExternasAprovaFacil() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipo();
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getOperadoraCartao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(),getSituacaoFiltro());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public String getMsgCartoesNaoAceitos() {
        return msgCartoesNaoAceitos;
    }

    public void setMsgCartoesNaoAceitos(String msgCartoesNaoAceitos) {
        this.msgCartoesNaoAceitos = msgCartoesNaoAceitos;
    }
    
    public void incluirLogInclusao() throws Exception {
        try {
            operadoraCartaoVO.setObjetoVOAntesAlteracao(new OperadoraCartaoVO());
            operadoraCartaoVO.setNovoObj(true);
            registrarLogObjetoVO(operadoraCartaoVO, operadoraCartaoVO.getCodigo(), "OPERADORACARTAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("OPERADORACARTAO", operadoraCartaoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE OPERADORACARTAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        operadoraCartaoVO.setNovoObj(new Boolean(false));
        operadoraCartaoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            operadoraCartaoVO.setObjetoVOAntesAlteracao(new OperadoraCartaoVO());
            operadoraCartaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(operadoraCartaoVO, operadoraCartaoVO.getCodigo(), "OPERADORACARTAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("OPERADORACARTAO", operadoraCartaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE OPERADORACARTAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVOGeral(operadoraCartaoVO, operadoraCartaoVO.getCodigo(), "OPERADORACARTAO", 0, true);
        } catch (Exception e) {
            registrarLogErroObjetoVO("OPERADORACARTAO", operadoraCartaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE OPERADORACARTAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        operadoraCartaoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = operadoraCartaoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), operadoraCartaoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       operadoraCartaoVO = new OperadoraCartaoVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public List getListaTipoDebitoOnline() {
        return TipoDebitoOnlineEnum.getSelectListTipo();
    }
    
    
     public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro ="AT";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public List getListaOperadorasGetNet() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE);
    }

    public List getListaBandeirasCappta() {
        return BandeirasCapptaEnum.getSelectListTipo();
    }

    public List getListaBandeirasStone() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE);
    }

    public List getListaBandeirasStone_V5() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5);
    }

    public List getListaBandeirasMundiPagg() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_MUNDIPAGG);
    }

    public List getListaBandeirasPagarMe() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_PAGAR_ME);
    }

    public List getListaBandeirasPagBank() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_PAGBANK);
    }

    public List getListaBandeirasStripe() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_STRIPE);
    }

    public List getListaBandeirasPinBank() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_PINBANK);
    }

    public List getListaBandeirasFacilitePay() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_FACILITEPAY);
    }

    public List getListaBandeirasDCCCaixaOnline() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_CAIXA_ONLINE);
    }

    public List getListaBandeirasPagolivre() {
        return OperadorasExternasAprovaFacilEnum.getSelectListTipoPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE);
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Operadora de Cartão",
                "Deseja excluir a Operadora de Cartão?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public List carregarOperadorasGeoitd(){
        setListaBandeirasGeoitd(JSFUtilities.getSelectItemListFromEnum(BandeirasGeoitdEnum.class, "descIssuerGeoitd", false));
        return listaBandeirasGeoitd;
    }

    public Boolean isUruguay() throws Exception {
        if(getConfiguracaoSistema().isUsarSistemaInternacional() && getEmpresaLogado().getPais().getNome().equals(IdentificadorInternacionalEnum.URUGUAY.getNomePais())) {
            carregarOperadorasGeoitd();
            return true;
        }
        return false;
    }

    public java.util.List<SelectItem> getListaBandeirasGeoitd() {
        return listaBandeirasGeoitd;
    }

    public void setListaBandeirasGeoitd(java.util.List<SelectItem> listaBandeirasGeoitd) {
        this.listaBandeirasGeoitd = listaBandeirasGeoitd;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public String getMensagemAjusteBandeiras() {
        return "Estamos padronizando as bandeiras de cartão de crédito. Por isso os campos estão bloqueados para edição. " +
                "Se precisa criar novas para um Convênio de Cartão de Crédito Online, use a opção no Convênio de Cobrança para gerar o vínculo.";
    }

}

package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.utilitarias.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;
import negocio.comuns.financeiro.BancoVO;
import negocio.facade.jdbc.financeiro.Banco;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * bancoForm.jsp bancoCons.jsp) com as funcionalidades da classe <code>Banco</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Banco
 * @see BancoVO
 */
public class BancoControle extends SuperControle {

    private BancoVO bancoVO;
    /**
     * Interface <code>BancoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public BancoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Banco</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setBancoVO(new BancoVO());
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Banco</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            BancoVO obj = getFacade().getBanco().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            setBancoVO(obj);
            bancoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Banco</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            BancoVO banco = getFacade().getBanco().consultarCodigoBanco(bancoVO.getCodigoBanco(), true, Uteis.NIVELMONTARDADOS_TODOS);
        	if(banco.getCodigo().equals(0) || (bancoVO.getCodigo() != null && bancoVO.getCodigo().equals(banco.getCodigo()))) {
        		if (bancoVO.isNovoObj().booleanValue()) {
        			getFacade().getBanco().incluir(bancoVO);
                                incluirLogInclusao();
        		} else {
        			getFacade().getBanco().alterar(bancoVO);
                                incluirLogAlteracao();
        		}
        	} else {
        		throw new Exception("Já existe um banco cadastrado com este código!");
        	}
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP BancoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();      
           
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getBanco().consultarPorCodigoBanco(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getBanco().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>BancoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getBanco().excluir(bancoVO);
            incluirLogExclusao();
            setBancoVO(new BancoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
        setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
        setErro(true);
            return "editar";
        }
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Banco",
                "Deseja excluir o banco?",
                this, "excluir", "", "", "", "");
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código do Banco"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public BancoVO getBancoVO() {
        return bancoVO;
    }

    public void setBancoVO(BancoVO bancoVO) {
        this.bancoVO = bancoVO;
    }
     public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getBanco().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
     
    public void incluirLogInclusao() throws Exception {
        try {
            bancoVO.setObjetoVOAntesAlteracao(new BancoVO());
            bancoVO.setNovoObj(true);
            registrarLogObjetoVO(bancoVO, bancoVO.getCodigo(), "BANCO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("BANCO", bancoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE BANCO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        bancoVO.setNovoObj(new Boolean(false));
        bancoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            bancoVO.setObjetoVOAntesAlteracao(new BancoVO());
            bancoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(bancoVO, bancoVO.getCodigo(), "BANCO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("BANCO", bancoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE BANCO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(bancoVO, bancoVO.getCodigo(), "BANCO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("BANCO", bancoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE BANCO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        bancoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = bancoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), bancoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       bancoVO = new BancoVO();
       realizarConsultaLogObjetoSelecionado();
    }
}

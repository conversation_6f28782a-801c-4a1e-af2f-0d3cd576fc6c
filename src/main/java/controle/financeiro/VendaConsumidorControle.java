/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import java.util.ArrayList;
import java.util.List;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class VendaConsumidorControle extends SuperControle {

    private VendaAvulsaVO vendaAvulsaVO;
    private ClienteVO clienteVO;
    private boolean apresentarOpcaoEstornoProduto;
    private Integer nrPaginaMovProduto;
    private Integer nrPaginaMovPagamento;
    private Integer nrPaginaMovParcela;
    private Integer codigoVenda = 0;

    /**
     * Interface <code>BancoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    public VendaConsumidorControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        inicializarDados();
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    private void inicializarDados() {
        setClienteVO(new ClienteVO());
        setNrPaginaMovPagamento(new Integer(5));
        setNrPaginaMovParcela(new Integer(5));
        setNrPaginaMovProduto(new Integer(5));
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP vendaConsumidorCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getValorConsulta().trim().isEmpty()) {
                //consulta somente por tipos consumidores
                objs = getFacade().getVendaAvulsa().consultarPorTipoComprador("CN", true, Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                if (getControleConsulta().getCampoConsulta().equals("nomeCons")) {
                    //consulta por nome de consumidor passado na consulta
                    objs = getFacade().getVendaAvulsa().consultarConsumidorPorNomeComprador(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
                }
                if (getControleConsulta().getCampoConsulta().equals("valorCompra")) {
                    //consulta por valor da compra aceitando valor com virgula, inteiro e com ponto
                    double valorDouble = Uteis.getObterDoubleDeValorReal(getControleConsulta().getValorConsulta());
                    objs = getFacade().getVendaAvulsa().consultarConsumidorPorValorCompra(valorDouble, true, Uteis.NIVELMONTARDADOS_TODOS);
                }
                if (getControleConsulta().getCampoConsulta().equals("codVenda")) {
                    //consulta por codigo de venda avulsa
                    Integer valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    objs = getFacade().getVendaAvulsa().consultarConsumidorPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
                }
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }
    
    public void montarListaHistoricoCompras(Integer vendaAvulsa) throws Exception {
         setCodigoVenda(vendaAvulsa);
         montarListaHistoricoCompras();
         setCodigoVenda(0);
    }
    

    public String montarListaHistoricoCompras() throws Exception {
        Integer codigoConsulta = getCodigoVenda();
        if(UteisValidacao.emptyNumber(codigoConsulta)){
            codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        }
        try {
            VendaAvulsaVO vendaConsumidor = getFacade().getVendaAvulsa().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            getClienteVO().getPessoa().setNome(vendaConsumidor.getNomeComprador());
            setApresentarOpcaoEstornoProduto(false);
            getClienteVO().setListaHistoricoPagamento(new ArrayList<MovPagamentoVO>());
            getClienteVO().setListaHistoricoProduto(new ArrayList<MovProdutoVO>());
            getClienteVO().setListaParcelas(new ArrayList<MovParcelaVO>());
            getClienteVO().setListaHistoricoProduto(getFacade().getMovProduto().consultarPorNomeConsumidorParaHistoricoCompras(getClienteVO().getPessoa().getNome(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getClienteVO().setListaHistoricoPagamento(getFacade().getMovPagamento().consultarPorConsumidorParaHistoricoPagamento(getClienteVO().getPessoa().getNome(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR));
            getClienteVO().setListaParcelas(getFacade().getMovParcela().consultarPorNomeConsumidor(getClienteVO().getPessoa().getNome(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            for (MovProdutoVO mov : getClienteVO().getListaHistoricoProduto()) {
                if (mov.getProduto().getTipoProduto().equals("PE") || mov.getProduto().getTipoProduto().equals("SE") || mov.getProduto().getTipoProduto().equals("DI") || mov.getProduto().getTipoProduto().equals("AA")) {
                    setApresentarOpcaoEstornoProduto(true);
                    break;
                }
                if (mov.getSituacao().equalsIgnoreCase("EA")) {
                    mov.setMudarCorSituacaoEmAberto("red");
                }
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "buscar";
        }
        return "editar";
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomeCons", "Nome do Consumidor"));
        itens.add(new SelectItem("valorCompra", "Valor da Compra"));
        itens.add(new SelectItem("codVenda", "Código"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void liberarBackingBeanMemoria(String nomeBackingBean) {
        try {
            this.limparRecursosMemoria();
            removerManagedBean(nomeBackingBean);
            System.gc();
            System.out.println("BACKING....: " + this.getClass().getSimpleName() + " REMOVIDO DA MEMÓRIA.");
        } catch (Exception e) {
            System.out.println("Nao conseguimos remover o Backing da Memória (" + e.getMessage() + ") " + this.getClass().getSimpleName());
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    /**
     * @return the apresentarOpcaoEstornoProduto
     */
    public boolean isApresentarOpcaoEstornoProduto() {
        return apresentarOpcaoEstornoProduto;
    }

    /**
     * @param apresentarOpcaoEstornoProduto the apresentarOpcaoEstornoProduto to set
     */
    public void setApresentarOpcaoEstornoProduto(boolean apresentarOpcaoEstornoProduto) {
        this.apresentarOpcaoEstornoProduto = apresentarOpcaoEstornoProduto;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the nrPaginaMovProduto
     */
    public Integer getNrPaginaMovProduto() {
        return nrPaginaMovProduto;
    }

    /**
     * @param nrPaginaMovProduto the nrPaginaMovProduto to set
     */
    public void setNrPaginaMovProduto(Integer nrPaginaMovProduto) {
        this.nrPaginaMovProduto = nrPaginaMovProduto;
    }

    /**
     * @return the nrPaginaMovPagamento
     */
    public Integer getNrPaginaMovPagamento() {
        return nrPaginaMovPagamento;
    }

    /**
     * @param nrPaginaMovPagamento the nrPaginaMovPagamento to set
     */
    public void setNrPaginaMovPagamento(Integer nrPaginaMovPagamento) {
        this.nrPaginaMovPagamento = nrPaginaMovPagamento;
    }

    /**
     * @return the nrPaginaMovParcela
     */
    public Integer getNrPaginaMovParcela() {
        return nrPaginaMovParcela;
    }

    /**
     * @param nrPaginaMovParcela the nrPaginaMovParcela to set
     */
    public void setNrPaginaMovParcela(Integer nrPaginaMovParcela) {
        this.nrPaginaMovParcela = nrPaginaMovParcela;
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

            String[] split = paramsTableFiltrada.split(",");
            String campoOrdenacao = split[0].replace("[", "");
            String ordem = split[1];
            String filtro = split[2].replace("''", "");
            filtro = filtro.replace("]", "");
            List listaParaImpressao = getFacade().getVendaAvulsa().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, ItemExportacaoEnum.VENDAS_CONSUMIDOR);
            if(exportadorListaControle.getErro()){
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            String tipo = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            if(tipo.equals("pdf")){
                setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/pdf','Transacoes', 800,200);");
            } else {
                setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file=" + exportadorListaControle.getFileName() + "&mimetype=application/vnd.ms-excel','Transacoes', 800,200);");
            }
        } catch (Exception e){
            montarErro(e);
        }

    }

    public Integer getCodigoVenda() {
        return codigoVenda;
    }

    public void setCodigoVenda(Integer codigoVenda) {
        this.codigoVenda = codigoVenda;
    }
    
    
}

package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.FiltrosEnum;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.FluxoCaixaTO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import relatorio.arquitetura.VisualizadorRelatorio;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.impl.fluxocaixa.FluxoCaixaServiceImpl;
import servicos.interfaces.FluxoCaixaServiceInterface;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/*
 * <AUTHOR> Felipe
 */
public class FluxoCaixaControle extends SuperControleRelatorio {

    private Date dataInicialPrevisto;
    private Date dataFinalPrevisto;
    private List<SelectItem> listaSelectItemEmpresa;
    private Date dataInicialRealizado;
    private Date dataFinalRealizado;
    private String saldoAlterado;
    private List<GenericoTO> tipos;
    private List<GenericoTO> mesesPrevisto = null;
    private List<GenericoTO> mesesRealizado = null;
    private String mesPrevisto;
    private boolean mostrarCampoEmpresa = false;
    private String mesRealizado;
    private List<FluxoCaixaTO> listaPrevisto;
    private List<FluxoCaixaTO> listaRealizado;
    private FluxoCaixaServiceInterface servico;
    private Double saldoInicialRealizado = 0.0;
    private Double saldoInicialPrevisto = 0.0;
    private Double saldoSimulado = 0.0;
    private Integer codigoMovConta;
    private Integer codigoPessoa;
    private String diaAplicar;
    private String diaMostrarSaldo;
    private FluxoCaixaTO totalRealizado;
    private FluxoCaixaTO totalPrevisto;
    private boolean visualizarSaldo = false;
    private boolean datasVinculadas = false;
    private boolean modoGrafico = false;
    private boolean filtrandoPrevisto = true;
    private JSONArray grafico;
    private String contasSaldoInicial;
    private String contasPrevisto;
    private String contasRealizado;
    private Map<String, List<ContaVO>> contasFiltro;
    private Date hojeZ = Calendario.getDataComHoraZerada(Calendario.hoje());
    private boolean incluirParcelasRecorrenciaListaPrevisto;
    private boolean incluirParcelasEmAbertoListaPrevisto = false;
    private boolean saldoFixado=false;
    private String fixarSaldoAlterado;
    private SimulacaoSaldoVO simulacaoSaldoVO = new SimulacaoSaldoVO();
    private Boolean alterarSaldoInicial = false;
    private String saldoInicialAlterado = "0,00";
    private Double saldoInicial = 0.0;
    private Boolean filtrandoRealizado = false;

    private Integer filtroParcelasEmAberto = 0;

    public FluxoCaixaControle() throws Exception {
        servico = new FluxoCaixaServiceImpl(getFacade().getFinanceiro().getCon());
    }

    public void vincularDatas() {
        if( filtrandoRealizado != null && !filtrandoRealizado) {
            if (!Calendario.igual(dataInicialPrevisto, dataInicialRealizado)
                    || !Calendario.igual(dataFinalPrevisto, dataFinalRealizado)) {
                dataInicialRealizado = new Date(dataInicialPrevisto.getTime());
                dataFinalRealizado = new Date(dataFinalPrevisto.getTime());
                consultarRealizado();
            }
        }else {
            if (!Calendario.igual(dataInicialPrevisto, dataInicialRealizado)
                    || !Calendario.igual(dataFinalPrevisto, dataFinalRealizado)) {
                dataInicialPrevisto = new Date(dataInicialRealizado.getTime());
                dataFinalPrevisto = new Date(dataFinalRealizado.getTime());
                consultarPrevisto();
            }
        }
        datasVinculadas = true;
    }

    public void sairGrafico() {
        modoGrafico = false;
    }

    public void abrirGrafico() {
        try {
            grafico = servico.montarGrafico(listaPrevisto, listaRealizado);
            modoGrafico = true;
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void irParaTelaCliente() throws Exception {
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
        clienteControle.prepararTelaCliente(cliente, true);
    }

    public String getDadosGrafico() {
        return grafico == null ? new JSONArray().toString() : grafico.toString();
    }

    public void desvincularDatas() {
        datasVinculadas = false;
    }

    public void aplicarSaldoAlterado() {
        Double saldoAplicar = 0.0;
        try {
            saldoAplicar = Double.valueOf(saldoAlterado.replace(".", "").replace(",", "."));
        } catch (Exception e) {
            saldoAplicar = 0.0;
        }
            servico.aplicarSaldo(listaPrevisto, saldoAplicar, totalPrevisto, diaAplicar);
            servico.aplicarSaldo(listaRealizado, saldoAplicar, totalRealizado, diaAplicar);
    }

    public void fixarSaldoAlterado(){
        salvarFixacaoSaldo(Double.valueOf(saldoAlterado.replace(".", "").replace(",", ".")), diaAplicar, false);
        notificarRecursoEmpresa(RecursoSistema.FIXOU_SALDO_FLUXO_CAIXA);
        montarSucessoGrowl("Saldo fixado com sucesso!");
    }

    public void fixarSaldoInicialAlterado(){
        aplicarSaldoInicialAlterado();
        String dia = Uteis.getDataAplicandoFormatacao(dataInicialPrevisto, "dd/MM");
        salvarFixacaoSaldo(saldoInicialPrevisto, dia, true);
        alterarSaldoInicial = false;
        saldoFixado = true;
        datasVinculadas = true;
        notificarRecursoEmpresa(RecursoSistema.FIXOU_SALDO_INICIAL_FLUXO_CAIXA);
        montarSucessoGrowl("Saldo fixado com sucesso!");
    }

    public void salvarFixacaoSaldo(Double saldoAplicar, String dia, Boolean saldoInicial){
        simulacaoSaldoVO.registrarObjetoVOAntesDaAlteracao();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataInicialPrevisto);
        int ano = calendar.get(Calendar.YEAR);
        int mes = calendar.get(Calendar.MONTH);
        try {
            if(saldoFixado){
                incluirLogExclusao();
                servico.exluirSimulacaoSaldo(mes, ano);
                saldoFixado = false;
            }

            simulacaoSaldoVO.setDataLancamento(getDataAtual());
            simulacaoSaldoVO.setEmpresa(getEmpresaLogado().getCodigo());
            simulacaoSaldoVO.setUsuarioLancamento(getUsuarioLogado().getCodigo());
            simulacaoSaldoVO.setSaldo(saldoAplicar);
            simulacaoSaldoVO.setDia(dia);
            simulacaoSaldoVO.setAno(ano);
            simulacaoSaldoVO.setMes(mes);
            simulacaoSaldoVO.setSaldoInicial(saldoInicial);

            servico.incluirSimulacaoSaldo(simulacaoSaldoVO);
            incluirLogAlteracao();
            saldoFixado = true;
            datasVinculadas = true;

        }catch(Exception e){
            montarErro(e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
    }

    public void aplicarSaldoRealizado() {
        dataInicialRealizado = new Date(dataInicialPrevisto.getTime());
        dataFinalRealizado = new Date(dataFinalPrevisto.getTime());
        consultarRealizado(true);
        saldoSimulado = 0.0;

        for (FluxoCaixaTO f : listaRealizado) {
            if (f.getDiaApresentar().equals(diaAplicar)) {
                saldoSimulado = f.getSaldo();
                break;
            }
        }
        saldoAlterado = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(saldoSimulado);
        servico.aplicarSaldo(listaPrevisto, saldoSimulado, totalPrevisto, diaAplicar);
    }

    public void consultarGrafico() {
        try {
            consultarPrevisto(false);
            dataInicialRealizado = new Date(dataInicialPrevisto.getTime());
            dataFinalRealizado = new Date(dataFinalPrevisto.getTime());
            consultarRealizado(true);
            abrirGrafico();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void desfazerSaldoRealizado() {
        if(simulacaoSaldoVO.getSaldoInicial() || alterarSaldoInicial ){
            alterarSaldoInicial = false;
            saldoInicialRealizado = 0.0;
            saldoInicialPrevisto = 0.0;
            saldoInicialAlterado = "0,00";
        }
        servico.aplicarSaldo(listaPrevisto, saldoInicialPrevisto, totalPrevisto, null);
        servico.aplicarSaldo(listaRealizado, saldoInicialRealizado, totalRealizado, null);
        diaAplicar = null;
        saldoSimulado = null;
        saldoAlterado = null;
        try {
            if(saldoFixado) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(dataInicialPrevisto);
                int ano = calendar.get(Calendar.YEAR);
                int mes = calendar.get(Calendar.MONTH);

                incluirLogExclusao();
                servico.exluirSimulacaoSaldo(mes, ano);
                saldoFixado = false;
            }
            datasVinculadas = false;
            montarSucessoGrowl("Saldo desfixado com sucesso!");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void consultarPrevisto() {
        saldoInicialPrevisto = 0.0;
        diaAplicar = null;
        consultarPrevisto(true);
        notificarRecursoEmpresa(RecursoSistema.FLUXO_DE_CAIXA);
    }

    public void consultarPrevisto(boolean testarCompatibilidade) {
        try {
            limparMsg();
            listaPrevisto = new ArrayList<FluxoCaixaTO>();
            totalPrevisto = new FluxoCaixaTO();
            processarFiltroParcelasEmAberto();
            if (datasVinculadas && saldoFixado) {
                dataInicialRealizado = new Date(dataInicialPrevisto.getTime());
                dataFinalRealizado = new Date(dataFinalPrevisto.getTime());
                consultarRealizado();
            }
            if(UteisValidacao.emptyString(contasSaldoInicial)){
                saldoInicialPrevisto = 0.0;
            }else {
                saldoInicialPrevisto = getFacade().getFinanceiro().getConta().saldoAte(null,
                        Uteis.somarDias(dataInicialPrevisto,-1), false,
                        null, contasSaldoInicial);
            }

            if(dataInicialPrevisto.getMonth() != dataFinalPrevisto.getMonth()){
                dataFinalPrevisto = Uteis.obterUltimoDiaMes(dataInicialPrevisto);
            }

            mesPrevisto = "prev" + Uteis.getDataAplicandoFormatacao(dataInicialPrevisto, "MM/yyyy");
            listaPrevisto = servico.processarFluxoCaixa(
                    getEmpresa().getCodigo(),
                    dataInicialPrevisto,
                    dataFinalPrevisto,
                    true,
                    saldoInicialPrevisto,
                    totalPrevisto,
                    contasPrevisto,
                    isIncluirParcelasRecorrenciaListaPrevisto(),
                    isIncluirParcelasEmAbertoListaPrevisto());
            if (testarCompatibilidade) {
                verificarCompatibilidade();
            }
            if (getMesesPrevisto().get(getMesesPrevisto().size() - 1).getCodigoString().equals(mesPrevisto)
                    || getMesesPrevisto().get(0).getCodigoString().equals(mesPrevisto)) {
                mesesPrevisto = montarMeses("prev", dataInicialPrevisto);
            }
            if (incluirParcelasRecorrenciaListaPrevisto) {
                notificarRecursoEmpresa(RecursoSistema.FLUXO_DE_CAIXA_FILTRO_PARCELAS_RECORRENCIA);
            }

            verificarSimulacaoFixada(false);
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public final void inicializarEmpresa() throws Exception {
        mostrarCampoEmpresa = validarPermissaoEmpresas();
        if (mostrarCampoEmpresa) {
            setEmpresa((EmpresaVO) getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0));
        } else {
            setEmpresa((EmpresaVO) getEmpresaLogado().getClone(true));
        }
        if (getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void usarSaldoPrevistoOlhandoProSaldoPrevisto() throws Exception {
        filtrandoRealizado = false;
        saldoPrevistoInicialSaldoFinalMesPassado(dataInicialPrevisto);
    }

    public void usarSaldoRealizadoOlhandoProSaldoPrevisto() throws Exception {
        filtrandoRealizado = false;
        saldoRealizadoInicialSaldoFinalMesPassado(dataInicialPrevisto);
    }

    public void usarSaldoPrevistoOlhandoProSaldoRealizado() throws Exception {
        filtrandoRealizado = true;
        saldoPrevistoInicialSaldoFinalMesPassado(dataInicialRealizado);
    }

    public void usarSaldoRealizadoOlhandoProSaldoRealizado() throws Exception {
        filtrandoRealizado = true;
        saldoRealizadoInicialSaldoFinalMesPassado(dataInicialRealizado);
    }

    public void saldoPrevistoInicialSaldoFinalMesPassado(Date data) throws Exception {
        Date primeiroDiaMesAterior = Uteis.somarCampoData(data, Calendar.MONTH, -1);
        Date ultimoDiaMesAnterior = Uteis.obterUltimoDiaMes(primeiroDiaMesAterior);
        vincularDatas();
        processarFiltroParcelasEmAberto();

        totalPrevisto = new FluxoCaixaTO();

        servico.processarFluxoCaixa(
                getEmpresa().getCodigo(),
                primeiroDiaMesAterior,
                ultimoDiaMesAnterior,
                true,
                saldoInicialPrevisto,
                totalPrevisto,
                contasPrevisto,
                isIncluirParcelasRecorrenciaListaPrevisto(),
                isIncluirParcelasEmAbertoListaPrevisto());

        int ano;
        int mes;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(primeiroDiaMesAterior);
        ano = calendar.get(Calendar.YEAR);
        mes = calendar.get(Calendar.MONTH);

        saldoInicialRealizado = totalPrevisto.getTotal();
        saldoInicialPrevisto = totalPrevisto.getTotal();
        saldoInicialAlterado = totalPrevisto.getTotal_Apresentar();

        simulacaoSaldoVO = servico.buscarSimulacaoSaldo(ano, mes);

        if(simulacaoSaldoVO.getSaldoInicial()){
            saldoInicialRealizado += simulacaoSaldoVO.getSaldo();
            saldoInicialPrevisto += simulacaoSaldoVO.getSaldo();
            saldoInicialAlterado = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(saldoInicialPrevisto);
        }

        servico.aplicarSaldo(listaPrevisto, saldoInicialPrevisto, totalPrevisto, null);
        servico.aplicarSaldo(listaRealizado, saldoInicialRealizado, totalRealizado, null);
        datasVinculadas = true;
        alterarSaldoInicial = true;
    }

    public void saldoRealizadoInicialSaldoFinalMesPassado(Date data) throws Exception {
        Date primeiroDiaMesAterior = Uteis.somarCampoData(data, Calendar.MONTH, -1);
        Date ultimoDiaMesAnterior = Uteis.obterUltimoDiaMes(primeiroDiaMesAterior);
        vincularDatas();
        totalRealizado = new FluxoCaixaTO();

        servico.processarFluxoCaixa(getEmpresa().getCodigo(),
                primeiroDiaMesAterior,
                ultimoDiaMesAnterior,
                false,
                saldoInicialRealizado,
                totalRealizado,
                contasRealizado);

        int ano;
        int mes;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(primeiroDiaMesAterior);
        ano = calendar.get(Calendar.YEAR);
        mes = calendar.get(Calendar.MONTH);

        saldoInicialRealizado = totalRealizado.getTotal();
        saldoInicialPrevisto = totalRealizado.getTotal();
        saldoInicialAlterado = totalRealizado.getTotal_Apresentar();

        simulacaoSaldoVO = servico.buscarSimulacaoSaldo(ano, mes);

        if(simulacaoSaldoVO.getSaldoInicial()){
            saldoInicialPrevisto += simulacaoSaldoVO.getSaldo();
            saldoInicialRealizado += simulacaoSaldoVO.getSaldo();
            saldoInicialAlterado = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(saldoInicialRealizado);
        }

        servico.aplicarSaldo(listaPrevisto, saldoInicialPrevisto, totalPrevisto, null);
        servico.aplicarSaldo(listaRealizado, saldoInicialRealizado, totalRealizado, null);
        alterarSaldoInicial = true;
        datasVinculadas = true;
    }

    public void consultarPrevistoMes() throws Exception {
        GenericoTO mp = (GenericoTO) context().getExternalContext().getRequestMap().get("mp");
        dataInicialPrevisto = Uteis.getDate("01/" + mp.getCodigoString().replace("prev", ""));
        dataFinalPrevisto = Uteis.obterUltimoDiaMes(dataInicialPrevisto);
        dataFinalRealizado = Uteis.obterUltimoDiaMes(dataInicialRealizado);
        consultarPrevisto();
        consultarRealizado();
    }

    public void consultarRealizadoMes() throws Exception {
        GenericoTO mp = (GenericoTO) context().getExternalContext().getRequestMap().get("mr");
        dataInicialRealizado = Uteis.getDate("01/" + mp.getCodigoString().replace("real", ""));
        dataFinalRealizado = Uteis.obterUltimoDiaMes(dataInicialRealizado);
        dataFinalPrevisto = Uteis.obterUltimoDiaMes(dataInicialPrevisto);
        consultarPrevisto();
        consultarRealizado();
    }

    public void consultarRealizado() {
        saldoInicialRealizado = 0.0;
        diaAplicar = null;
        if(datasVinculadas){
            dataInicialRealizado = dataInicialPrevisto;
        }
        consultarRealizado(true);
    }

    public void consultarRealizado(boolean testarCompatibilidade) {
        try {
            limparMsg();
            listaRealizado = new ArrayList<FluxoCaixaTO>();
            mesRealizado = "real" + Uteis.getDataAplicandoFormatacao(dataInicialRealizado, "MM/yyyy");
            totalRealizado = new FluxoCaixaTO();
            if(UteisValidacao.emptyString(contasSaldoInicial)){
                saldoInicialRealizado = 0.0;
            }else{
                saldoInicialRealizado = getFacade().getFinanceiro().getConta().saldoAte(null,
                        Uteis.somarDias(dataInicialRealizado, -1),
                        false, null, contasSaldoInicial);
            }

            if(dataInicialRealizado.getMonth() != dataFinalRealizado.getMonth()){
                dataFinalRealizado = Uteis.obterUltimoDiaMes(dataInicialRealizado);
            }

            listaRealizado = servico.processarFluxoCaixa(getEmpresa().getCodigo(),
                    dataInicialRealizado,
                    dataFinalRealizado,
                    false,
                    saldoInicialRealizado,
                    totalRealizado,
                    contasRealizado);
            if (testarCompatibilidade) {
                verificarCompatibilidade();
            }
            if(getMesesRealizado().get(getMesesRealizado().size() - 1).getCodigoString().equals(mesRealizado)
                    || getMesesRealizado().get(0).getCodigoString().equals(mesRealizado)){
                mesesRealizado = montarMeses("real", dataInicialRealizado);
            }
            verificarSimulacaoFixada(true);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<GenericoTO> montarMeses(String prfx, Date dataBase) throws Exception {
        List<GenericoTO> meses = new ArrayList<GenericoTO>();
        Date inicio = Uteis.somarCampoData(dataBase, Calendar.MONTH, -5);
        Date fim = Uteis.somarCampoData(dataBase, Calendar.MONTH, 6);
        List<Date> mesesEntreDatas = Uteis.getMesesEntreDatas(inicio, fim);
        for (Date mes : mesesEntreDatas) {
            GenericoTO g = new GenericoTO();
            g.setLabel(Uteis.getDataAplicandoFormatacao(mes, "MMM/yy"));
            g.setCodigoString(prfx + Uteis.getDataAplicandoFormatacao(mes, "MM/yyyy"));
            meses.add(g);
        }
        return meses;
    }

    public void verificarCompatibilidade() {
        if (listaRealizado == null || listaPrevisto == null) {
            return;
        }
        for (FluxoCaixaTO fp : listaPrevisto) {
            int i = listaRealizado.indexOf(fp);
            if (i >= 0) {
                FluxoCaixaTO fr = listaRealizado.get(i);
                fr.setEntradaDiferente(!fr.getEntradas().equals(fp.getEntradas()));
                fp.setEntradaDiferente(fr.isEntradaDiferente());

                fr.setSaidaDiferente(!fr.getSaidas().equals(fp.getSaidas()));
                fp.setSaidaDiferente(fr.isSaidaDiferente());
            }
        }
    }

    /**
     * <AUTHOR> Alcides 20/05/2013
     */
    private String prepararEdicao(Integer codigo, MovContaControle controle) throws Exception {
        MovContaVO movConta = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(codigo, Uteis.NIVELMONTARDADOS_TODOS);
        controle.setLancamentoDemonstrativo(Boolean.TRUE);
        controle.setOrigemConsulta("fluxoCaixa");
        return controle.preparaEdicao(movConta);
    }

    public void editarLancamentoPopUp() throws Exception {
        MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
        movContaControle.setOrigemConsulta("fluxoCaixa");
        prepararEdicao(codigoMovConta, movContaControle);
        movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
    }

    public void abrirFiltroPrevisto() throws Exception {
        abrirFiltro(true);
    }

    public void abrirFiltroRealizado() throws Exception {
        abrirFiltro(false);
    }

    public void fecharFiltroContas() {
        try {
            String f = "";
            for (String k : contasFiltro.keySet()) {
                for (ContaVO c : contasFiltro.get(k)) {
                    if (c.getContaEscolhida()) {
                        f += "," + c.getCodigo();
                    }
                }
            }

            f = f.replaceFirst("\\,", "");
            if(visualizarSaldo){
                contasSaldoInicial = f;
                //gravarFiltro(FiltrosEnum.FLUXO_CAIXA_CONTAS_PREVISTO_SALDO_INICIAL, contasSaldoInicial);
                //consultarPrevisto();
                //consultarRealizado();
            }else if (filtrandoPrevisto) {
                contasPrevisto = f;
                //gravarFiltro(FiltrosEnum.FLUXO_CAIXA_CONTAS_PREVISTO, contasPrevisto);
               // consultarPrevisto();
            } else {
                contasRealizado = f;
                //gravarFiltro(FiltrosEnum.FLUXO_CAIXA_CONTAS_REALIZADO, contasRealizado);
                //consultarRealizado();
            }
            if(!f.equals("")){
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(dataInicialPrevisto);
                int ano = calendar.get(Calendar.YEAR);
                int mes = calendar.get(Calendar.MONTH);

                incluirLogExclusao();
                servico.exluirSimulacaoSaldo(mes, ano);
                saldoFixado = false;
                datasVinculadas = true;
                if( filtrandoRealizado != null && !filtrandoRealizado) {
                    if (!Calendario.igual(dataInicialPrevisto, dataInicialRealizado)
                            || !Calendario.igual(dataFinalPrevisto, dataFinalRealizado)) {
                        dataInicialRealizado = new Date(dataInicialPrevisto.getTime());
                        dataFinalRealizado = new Date(dataFinalPrevisto.getTime());
                    }
                }else {
                    if (!Calendario.igual(dataInicialPrevisto, dataInicialRealizado)
                            || !Calendario.igual(dataFinalPrevisto, dataFinalRealizado)) {
                        dataInicialPrevisto = new Date(dataInicialRealizado.getTime());
                        dataFinalPrevisto = new Date(dataFinalRealizado.getTime());
                    }
                }
                consultarRealizado();
                consultarPrevisto();
                saldoInicialAlterado = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(saldoInicialRealizado);
                alterarSaldoInicial = true;
                aplicarSaldoInicialAlterado();
                contasSaldoInicial = "";
            }
        } catch (Exception e) {
            montarErro(e);
        }

    }

    public String getDtInicioApresentar(){
        return Uteis.getDataAplicandoFormatacao(Uteis.somarDias(filtrandoPrevisto ? dataInicialPrevisto : dataInicialRealizado, -1), "dd/MM");
    }

    public void abrirSaldoPrevisto() {
        try {
            filtrandoPrevisto = true;
            visualizarSaldo = true;
            filtrandoRealizado = true;
            abrirSaldoContas(Boolean.TRUE);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void alterarRenderizarSaldoInicial() {
        try {
            vincularDatas();
            alterarSaldoInicial = true;
            datasVinculadas = true;
            aplicarSaldoInicialAlterado();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirSaldoRealizado() {
        try {
            filtrandoPrevisto = false;
            filtrandoRealizado = true;
            visualizarSaldo = true;
            abrirSaldoContas(Boolean.FALSE);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirSaldoContasRealizado() throws Exception {
        visualizarSaldo = true;
        abrirSaldoContas(false);
        Map<Integer, Double> mpcnts = new HashMap<Integer, Double>();
        for (FluxoCaixaTO f : listaRealizado) {
            for (Integer c : f.getContas().keySet()) {
                mpcnts.put(c, mpcnts.get(c) == null ? f.getContas().get(c) : (f.getContas().get(c) + mpcnts.get(c)));
            }
            if (f.getDiaApresentar().equals(diaMostrarSaldo)) {
                break;
            }
        }

        for (List<ContaVO> lista : contasFiltro.values()) {
            for (ContaVO c : lista) {
                if (mpcnts.get(c.getCodigo()) != null) {
                    c.setSaldoAtual(mpcnts.get(c.getCodigo()) + c.getSaldoAtual());
                }

            }
        }


    }

    public void abrirSaldoContas(Boolean previsto) throws Exception {
        String[] split;

        if(visualizarSaldo){
            split = contasSaldoInicial == null ? new String[]{} : contasSaldoInicial.split("\\,");
        }else {
            split = previsto ? (contasPrevisto == null ? new String[]{} : contasPrevisto.split("\\,"))
                    : (contasRealizado == null ? new String[]{} : contasRealizado.split("\\,"));
        }

        List<Integer> selecionados = new ArrayList<Integer>();
        for (String s : split) {
            if (!UteisValidacao.emptyString(s)) {
                selecionados.add(Integer.valueOf(s));
            }

        }
        verificarMarcados(selecionados, previsto);
    }

    public void abrirFiltro(boolean previsto) throws Exception{
        filtrandoPrevisto = previsto;
        visualizarSaldo = false;
        List<Integer> selecionados = new ArrayList<Integer>();
        String[] split = previsto ? (contasPrevisto == null ? new String[]{} : contasPrevisto.split("\\,"))
                : (contasRealizado == null ? new String[]{} : contasRealizado.split("\\,"));
        for (String s : split) {
            if (!UteisValidacao.emptyString(s)) {
                selecionados.add(Integer.valueOf(s));
            }

        }
        verificarMarcados(selecionados, previsto);
    }

    private void verificarMarcados(List<Integer> selecionados, boolean previsto) throws Exception{
        Map<String, Boolean> mpBl = new HashMap<String, Boolean>();
        for (String k : contasFiltro.keySet()) {
            if(UteisValidacao.emptyList(contasFiltro.get(k))){
                mpBl.put(k,Boolean.FALSE);
            }
            Boolean todos = Boolean.TRUE;
            for (ContaVO c : contasFiltro.get(k)) {
                c.setContaEscolhida(selecionados.contains(c.getCodigo()));
                if(visualizarSaldo){
                    c.setSaldoAtual(getFacade().getConta().saldoAte(c.getCodigo(),
                            Uteis.somarDias(previsto ? dataInicialPrevisto : dataInicialRealizado, -1), false,
                            null, null));
                }
                if(!c.getContaEscolhida()){
                    todos = Boolean.FALSE;
                }
            }
            mpBl.put(k,todos);
        }

        for(GenericoTO g : tipos){
            g.setSelecionado(mpBl.get(g.getCodigoString()));
        }
    }


    public void marcarTodas(String t, boolean m) {
        for (String k : contasFiltro.keySet()) {
            if (t != null && !k.equals(t)) {
                continue;
            }
            for (ContaVO c : contasFiltro.get(k)) {
                c.setContaEscolhida(m);
            }
        }
        for(GenericoTO g : tipos){
            g.setSelecionado(m);
        }
    }

    public void obterFiltros() throws Exception {
        contasSaldoInicial = obterFiltro(FiltrosEnum.FLUXO_CAIXA_CONTAS_PREVISTO_SALDO_INICIAL);
        contasPrevisto = obterFiltro(FiltrosEnum.FLUXO_CAIXA_CONTAS_PREVISTO);
        contasRealizado = obterFiltro(FiltrosEnum.FLUXO_CAIXA_CONTAS_REALIZADO);
    }

    public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList<SelectItem>();
            listaSelectItemEmpresa.add(new SelectItem(0, ""));
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                listaSelectItemEmpresa.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }

        }
        return listaSelectItemEmpresa;
    }

    public String getNomeEmpresaFiltro() throws Exception{
        if(mostrarCampoEmpresa){
            for(SelectItem se : listaSelectItemEmpresa){
                if(getEmpresa() != null && getEmpresa().getCodigo() != null && getEmpresa().getCodigo().equals(se.getValue())){
                    return se.getLabel();
                }
            }
            return "";
        }else{
            return getEmpresaLogado() == null ? "" : getEmpresaLogado().getNome();
        }
    }


    public void obterEmpresaEscolhida() {
        try {
            inicializar();
            consultarPrevisto(false);
            consultarRealizado(false);
            verificarCompatibilidade();
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }


    public String abrirFluxoDeCaixa() {
        try {
            validarPermissaoFluxoaixa();
            inicializarEmpresa();
            inicializar();

        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
        return "fluxoCaixa";
    }


    public void validarPermissaoFluxoaixa() throws Exception {
        validarPermissao("FluxoCaixa", "9.58 - Permitir consultar fluxo de caixa", getUsuarioLogado());
    }

    public void validarPermissao(String permissao, String descricao, UsuarioVO usuario) throws Exception {
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                limparMsg();
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = usuario.getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                        usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        usuario, permissao, descricao);
            }
        }
    }

    public void inicializar() throws Exception{
        List<ContaVO> contaVOS = getFacade().getFinanceiro().getConta().consultarContasSimples(getEmpresa().getCodigo(), true);
        setContasFiltro(new HashMap<String, List<ContaVO>>());
        tipos = new ArrayList<GenericoTO>();
        for (ContaVO c : contaVOS) {
            List<ContaVO> contaVOSTipo = getContasFiltro().get(c.getTipoConta().getDescricao() == null ? "" :  c.getTipoConta().getDescricao().replaceAll(" ", "_"));
            if (contaVOSTipo == null) {
                contaVOSTipo = new ArrayList<ContaVO>();
                getContasFiltro().put(c.getTipoConta().getDescricao() == null ? "" :  c.getTipoConta().getDescricao().replaceAll(" ", "_"), contaVOSTipo);
                tipos.add(new GenericoTO(
                        c.getTipoConta().getDescricao() == null ? "" :  c.getTipoConta().getDescricao().replaceAll(" ", "_"),
                        c.getTipoConta().getDescricao()));
            }
            contaVOSTipo.add(c);
        }
        Ordenacao.ordenarLista(tipos, "label");
        tipos.add(new GenericoTO("naomov", ""));
        getContasFiltro().put("naomov", Arrays.asList(new ContaVO[]{new ContaVO(-1)}));
        //obterFiltros();

            dataInicialPrevisto = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataFinalPrevisto = Uteis.obterUltimoDiaMes(Calendario.hoje());
            consultarPrevisto(false);

            dataInicialRealizado = new Date(dataInicialPrevisto.getTime());
            dataFinalRealizado = new Date(dataFinalPrevisto.getTime());
            consultarRealizado(false);

        verificarCompatibilidade();

    }

    public void marcarTodas() {
        String t = null;
        try {
            GenericoTO tipo = (GenericoTO) context().getExternalContext().getRequestMap().get("t");
            t = tipo.getCodigoString();
        } catch (Exception e) {
        }
        marcarTodas(t, true);
    }


    public void desmarcarTodas() {
        String t = null;
        try {
            GenericoTO tipo = (GenericoTO) context().getExternalContext().getRequestMap().get("t");
            t = tipo.getCodigoString();
        } catch (Exception e) {
        }
        marcarTodas(t, false);
    }

    public Date getDataInicialPrevisto() {
        if (dataInicialPrevisto == null) {
            dataInicialPrevisto = Calendario.hoje();
        }
        return dataInicialPrevisto;
    }

    public void setDataInicialPrevisto(Date dataInicialPrevisto) {
        this.dataInicialPrevisto = dataInicialPrevisto;
    }

    public Date getDataFinalPrevisto() {
        if (dataFinalPrevisto == null) {
            dataFinalPrevisto = Calendario.hoje();
        }
        return dataFinalPrevisto;
    }

    public void setDataFinalPrevisto(Date dataFinalPrevisto) {
        this.dataFinalPrevisto = dataFinalPrevisto;
    }

    public Date getDataInicialRealizado() {
        if (dataInicialRealizado == null) {
            dataInicialRealizado = Calendario.hoje();
        }
        return dataInicialRealizado;
    }

    public void setDataInicialRealizado(Date dataInicialRealizado) {
        this.dataInicialRealizado = dataInicialRealizado;
    }

    public Date getDataFinalRealizado() {
        if (dataFinalRealizado == null) {
            dataFinalRealizado = Calendario.hoje();
        }
        return dataFinalRealizado;
    }

    public void setDataFinalRealizado(Date dataFinalRealizado) {
        this.dataFinalRealizado = dataFinalRealizado;
    }

    public List<FluxoCaixaTO> getListaPrevisto() {
        if (listaPrevisto == null) {
            listaPrevisto = new ArrayList<FluxoCaixaTO>();
        }
        return listaPrevisto;
    }

    public void setListaPrevisto(List<FluxoCaixaTO> listaPrevisto) {
        this.listaPrevisto = listaPrevisto;
    }

    public List<FluxoCaixaTO> getListaRealizado() {
        if (listaRealizado == null) {
            listaRealizado = new ArrayList<FluxoCaixaTO>();
        }
        return listaRealizado;
    }

    public void setListaRealizado(List<FluxoCaixaTO> listaRealizado) {
        this.listaRealizado = listaRealizado;
    }

    public Double getSaldoInicialRealizado() {
        return saldoInicialRealizado;
    }

    public void setSaldoInicialRealizado(Double saldoInicialRealizado) {
        this.saldoInicialRealizado = saldoInicialRealizado;
    }

    public String getSaldoInicialRealizadoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaldoInicialRealizado());
    }

    public String getSaldoInicialPrevistoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaldoInicialPrevisto());
    }

    public String getSaldoSimuladoApresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaldoSimulado());
    }

    public Double getSaldoInicialPrevisto() {
        return saldoInicialPrevisto;
    }

    public void setSaldoInicialPrevisto(Double saldoInicialPrevisto) {
        this.saldoInicialPrevisto = saldoInicialPrevisto;
    }

    public Integer getCodigoMovConta() {
        return codigoMovConta;
    }

    public void setCodigoMovConta(Integer codigoMovConta) {
        this.codigoMovConta = codigoMovConta;
    }

    public FluxoCaixaServiceInterface getServico() {
        return servico;
    }

    public void setServico(FluxoCaixaServiceInterface servico) {
        this.servico = servico;
    }

    public FluxoCaixaTO getTotalRealizado() {
        return totalRealizado;
    }

    public void setTotalRealizado(FluxoCaixaTO totalRealizado) {
        this.totalRealizado = totalRealizado;
    }

    public FluxoCaixaTO getTotalPrevisto() {
        return totalPrevisto;
    }

    public void setTotalPrevisto(FluxoCaixaTO totalPrevisto) {
        this.totalPrevisto = totalPrevisto;
    }

    public boolean isDatasVinculadas() {
        return datasVinculadas;
    }

    public void setDatasVinculadas(boolean datasVinculadas) {
        this.datasVinculadas = datasVinculadas;
    }

    public boolean isModoGrafico() {
        return modoGrafico;
    }

    public void setModoGrafico(boolean modoGrafico) {
        this.modoGrafico = modoGrafico;
    }

    public String getDiaAplicar() {
        return diaAplicar;
    }

    public void setDiaAplicar(String diaAplicar) {
        this.diaAplicar = diaAplicar;
    }

    public Boolean getAlterarSaldoInicial() {
        return alterarSaldoInicial;
    }

    public void setAlterarSaldoInicial(Boolean alterarSaldoInicial) {
        this.alterarSaldoInicial = alterarSaldoInicial;
    }

    public Double getSaldoSimulado() {
        return saldoSimulado;
    }

    public void setSaldoSimulado(Double saldoSimulado) {
        this.saldoSimulado = saldoSimulado;
    }

    public JSONArray getGrafico() {
        return grafico;
    }

    public void setGrafico(JSONArray grafico) {
        this.grafico = grafico;
    }

    public String getContasPrevisto() {
        return contasPrevisto;
    }

    public void setContasPrevisto(String contasPrevisto) {
        this.contasPrevisto = contasPrevisto;
    }

    public String getContasRealizado() {
        return contasRealizado;
    }

    public void setContasRealizado(String contasRealizado) {
        this.contasRealizado = contasRealizado;
    }

    public Map<String, List<ContaVO>> getContasFiltro() {
        return contasFiltro;
    }

    public void setContasFiltro(Map<String, List<ContaVO>> contasFiltro) {
        this.contasFiltro = contasFiltro;
    }

    public boolean isFiltrandoPrevisto() {
        return filtrandoPrevisto;
    }

    public void setFiltrandoPrevisto(boolean filtrandoPrevisto) {
        this.filtrandoPrevisto = filtrandoPrevisto;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Date getHojeZ() {
        return hojeZ;
    }

    public void setHojeZ(Date hojeZ) {
        this.hojeZ = hojeZ;
    }

    public List<GenericoTO> getTipos() {
        return tipos;
    }

    public void setTipos(List<GenericoTO> tipos) {
        this.tipos = tipos;
    }

    public boolean isVisualizarSaldo() {
        return visualizarSaldo;
    }

    public void setVisualizarSaldo(boolean visualizarSaldo) {
        this.visualizarSaldo = visualizarSaldo;
    }

    public List<GenericoTO> getMesesPrevisto() throws Exception {
        if (mesesPrevisto == null) {
            mesesPrevisto = montarMeses("prev", Calendario.hoje());
        }
        return mesesPrevisto;
    }

    public void setMesesPrevisto(List<GenericoTO> mesesPrevisto) {
        this.mesesPrevisto = mesesPrevisto;
    }

    public List<GenericoTO> getMesesRealizado() throws Exception {
        if (mesesRealizado == null) {
            mesesRealizado = montarMeses("real", Calendario.hoje());
        }
        return mesesRealizado;
    }

    public void setMesesRealizado(List<GenericoTO> mesesRealizado) {
        this.mesesRealizado = mesesRealizado;
    }

    public String getMesPrevisto() {
        return mesPrevisto;
    }

    public void setMesPrevisto(String mesPrevisto) {
        this.mesPrevisto = mesPrevisto;
    }

    public String getMesRealizado() {
        return mesRealizado;
    }

    public void setMesRealizado(String mesRealizado) {
        this.mesRealizado = mesRealizado;
    }

    public String getSaldoAlterado() {
        return saldoAlterado;
    }

    public void setSaldoAlterado(String saldoAlterado) {
        this.saldoAlterado = saldoAlterado;
    }

    public String getFixarSaldoAlterado() {
        return fixarSaldoAlterado;
    }

    public void setFixarSaldoAlterado(String fixarSaldoAlterado) {
        this.fixarSaldoAlterado = fixarSaldoAlterado;
    }

    public String getDiaMostrarSaldo() {
        return diaMostrarSaldo;
    }

    public void setDiaMostrarSaldo(String diaMostrarSaldo) {
        this.diaMostrarSaldo = diaMostrarSaldo;
    }

    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    public boolean isIncluirParcelasRecorrenciaListaPrevisto() {
        return incluirParcelasRecorrenciaListaPrevisto;
    }

    public void setIncluirParcelasRecorrenciaListaPrevisto(boolean incluirParcelasRecorrenciaListaPrevisto) {
        this.incluirParcelasRecorrenciaListaPrevisto = incluirParcelasRecorrenciaListaPrevisto;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public String getContasSaldoInicial() {
        return contasSaldoInicial;
    }

    public void setContasSaldoInicial(String contasSaldoInicial) {
        this.contasSaldoInicial = contasSaldoInicial;
    }

    public void verificarSimulacaoFixada(Boolean realizado) throws Exception {
        int ano;
        int mes;
        Calendar calendar = Calendar.getInstance();
        if(!realizado){
            calendar.setTime(dataInicialPrevisto);
            dataFinalPrevisto = Uteis.obterUltimoDiaMes(dataInicialPrevisto);
        }
        else {
            calendar.setTime(dataInicialRealizado);
            dataFinalRealizado = Uteis.obterUltimoDiaMes(dataInicialRealizado);
        }
        ano = calendar.get(Calendar.YEAR);
        mes = calendar.get(Calendar.MONTH);
        saldoFixado = false;
        alterarSaldoInicial = false;
        saldoInicialAlterado = "0.0";

       simulacaoSaldoVO = servico.buscarSimulacaoSaldo(ano, mes);

       if(simulacaoSaldoVO.getSaldo()!=null && !simulacaoSaldoVO.getSaldoInicial()){
           saldoAlterado = simulacaoSaldoVO.getSaldo().toString();
           diaAplicar = simulacaoSaldoVO.getDia();
           saldoFixado = true;
           datasVinculadas = true;
           aplicarSaldoAlterado();
       }else if(simulacaoSaldoVO.getSaldo()!=null && simulacaoSaldoVO.getSaldoInicial()){
           if(realizado){
               dataInicialPrevisto = dataInicialRealizado;
               dataFinalPrevisto = dataFinalRealizado;
               consultarPrevisto();
           }
           saldoInicial = simulacaoSaldoVO.getSaldo();
           saldoInicialAlterado = Formatador.formatarValorMonetarioSemMoedaMantendoSinal(simulacaoSaldoVO.getSaldo());
           saldoFixado = true;
           datasVinculadas = true;
           aplicarSaldoInicialAlterado();
           montarInfo("O saldo inicial foi fixado manualmente");
       }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = simulacaoSaldoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),0, 0);
    }

    public void incluirLogExclusao() throws Exception {
        try {
            simulacaoSaldoVO.setObjetoVOAntesAlteracao(new SimulacaoSaldoVO());
            simulacaoSaldoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(simulacaoSaldoVO, simulacaoSaldoVO.getCodigo(), "SIMULACAOSALDO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("MODALIDADE", simulacaoSaldoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE SIMULACAOSALDO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(simulacaoSaldoVO, simulacaoSaldoVO.getCodigo(), "SIMULACAOSALDO", 0);
            simulacaoSaldoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("SIMULACAOSALDO", simulacaoSaldoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE SIMULACAOSALDO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        simulacaoSaldoVO.registrarObjetoVOAntesDaAlteracao();
    }

    public String getSaldoInicialAlterado() {
        return saldoInicialAlterado;
    }

    public void setSaldoInicialAlterado(String saldoInicialAlterado) {
        this.saldoInicialAlterado = saldoInicialAlterado;
    }

    public void aplicarSaldoInicialAlterado(){
        Double saldoInicialAplicar;
        try {
            saldoInicialAplicar = Double.valueOf(saldoInicialAlterado.replace(".", "").replace(",", ".").replace("-", ""));
            if(saldoInicialAlterado.contains("-")){
                saldoInicialAplicar = - saldoInicialAplicar;
            }
        } catch (Exception e) {
            saldoInicialAplicar = 0.0;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dataInicialPrevisto);

        saldoInicialPrevisto = saldoInicialRealizado = saldoInicialAplicar;
        servico.aplicarSaldo(listaPrevisto, saldoInicialAplicar, totalPrevisto, null, true);
        servico.aplicarSaldo(listaRealizado, saldoInicialAplicar, totalRealizado, null, true);
    }

    public boolean isSaldoFixado() {
        return saldoFixado;
    }

    public void setSaldoFixado(boolean saldoFixado) {
        this.saldoFixado = saldoFixado;
    }

    public void exportarRealizado(ActionEvent evt) throws Exception {
        try {
            setMsgAlert("");

            Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
            EmpresaVO empre = new EmpresaVO();
            if (emp != 0) {
                empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            String caminhoSubRelatorio = "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator;
            String caminhoRelatorio = caminhoSubRelatorio + "FluxoCaixa.jrxml";

            String tipoRelatorio = (String) JSFUtilities.getFromActionEvent("tipo", evt);

            HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
            //Parametros obrigatorios
            if (tipoRelatorio.equals("xls")) {
                request.setAttribute("tipoRelatorio", "EXCEL");
            } else {
                request.setAttribute("tipoRelatorio", "PDF");
            }
            request.setAttribute("nomeEmpresa", empre.getNome());
            request.setAttribute("nomeRelatorio", "FluxoCaixa");
            request.setAttribute("tituloRelatorio", "Fluxo de Caixa Realizado");
            request.setAttribute("nomeDesignIReport", caminhoRelatorio);
            request.setAttribute("SUBREPORT_DIR", caminhoSubRelatorio);
            request.setAttribute("listaObjetos", this.listaRealizado);
            request.setAttribute("tipoImplementacao", "OBJETO");
            //Parametros opicionais
            Map<String, Object> params = new HashMap<>();
            params.put("saldoInicial", UteisValidacao.emptyNumber(this.getSaldoInicialRealizado()) ? "0,00" : Formatador.formatarValorMonetarioSemMoedaMantendoSinal(this.getSaldoInicialRealizado()));
            params.put("totaisEntradas", this.getTotalRealizado().getEntrada_Apresentar());
            params.put("totaisSaidas", this.getTotalRealizado().getSaida_Apresentar());
            params.put("totaisTotal", this.getTotalRealizado().getTotal_Apresentar());
            params.put("totaisSaldo", this.getTotalRealizado().getSaldo_Apresentar());
            request.setAttribute("parametrosRelatorio", params);

            VisualizadorRelatorio visualizador = new VisualizadorRelatorio();
            visualizador.processRequest(request, null);
            visualizador = null;

            setMsgAlert("abrirPopupPDFImpressao('../../relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void exportarPrevisto(ActionEvent evt) throws Exception {
        try {
            setMsgAlert("");

            Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
            EmpresaVO empre = new EmpresaVO();
            if (emp != 0) {
                empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            String caminhoSubRelatorio = "relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator;
            String caminhoRelatorio = caminhoSubRelatorio + "FluxoCaixa.jrxml";

            String tipoRelatorio = (String) JSFUtilities.getFromActionEvent("tipo", evt);

            HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
            //Parametros obrigatorios
            if (tipoRelatorio.equals("xls")) {
                request.setAttribute("tipoRelatorio", "EXCEL");
            } else {
                request.setAttribute("tipoRelatorio", "PDF");
            }
            request.setAttribute("nomeEmpresa", empre.getNome());
            request.setAttribute("nomeRelatorio", "FluxoCaixa");
            request.setAttribute("tituloRelatorio", "Fluxo de Caixa Previsto");
            request.setAttribute("nomeDesignIReport", caminhoRelatorio);
            request.setAttribute("SUBREPORT_DIR", caminhoSubRelatorio);
            request.setAttribute("listaObjetos", this.listaPrevisto);
            request.setAttribute("tipoImplementacao", "OBJETO");
            //Parametros opicionais
            Map<String, Object> params = new HashMap<>();
            params.put("saldoInicial", UteisValidacao.emptyNumber(this.getSaldoInicialPrevisto()) ? "0,00" : Formatador.formatarValorMonetarioSemMoedaMantendoSinal(this.getSaldoInicialPrevisto()));
            params.put("totaisEntradas", this.getTotalPrevisto().getEntrada_Apresentar());
            params.put("totaisSaidas", this.getTotalPrevisto().getSaida_Apresentar());
            params.put("totaisTotal", this.getTotalPrevisto().getTotal_Apresentar());
            params.put("totaisSaldo", this.getTotalPrevisto().getSaldo_Apresentar());
            request.setAttribute("parametrosRelatorio", params);

            VisualizadorRelatorio visualizador = new VisualizadorRelatorio();
            visualizador.processRequest(request, null);
            visualizador = null;

            setMsgAlert("abrirPopupPDFImpressao('../../relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public boolean isIncluirParcelasEmAbertoListaPrevisto() {
        return incluirParcelasEmAbertoListaPrevisto;
    }

    public void setIncluirParcelasEmAbertoListaPrevisto(boolean incluirParcelasEmAbertoListaPrevisto) {
        this.incluirParcelasEmAbertoListaPrevisto = incluirParcelasEmAbertoListaPrevisto;
    }

    public Integer getFiltroParcelasEmAberto() {
        if (filtroParcelasEmAberto == null) {
            filtroParcelasEmAberto = 0;
        }
        return filtroParcelasEmAberto;
    }

    public void setFiltroParcelasEmAberto(Integer filtroParcelasEmAberto) {
        this.filtroParcelasEmAberto = filtroParcelasEmAberto;
    }

    public List<SelectItem> getListaFiltroParcelasEmAberto() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, ""));
        lista.add(new SelectItem(1, "Adicionar parcelas em aberto"));
        lista.add(new SelectItem(2, "Adicionar parcelas em aberto com autorização de cobrança ou contrato regime recorrência"));
        return lista;
    }

    public void processarFiltroParcelasEmAberto() {
        if (this.getFiltroParcelasEmAberto().equals(0)) {
            this.setIncluirParcelasRecorrenciaListaPrevisto(false);
            this.setIncluirParcelasEmAbertoListaPrevisto(false);
        } else if (this.getFiltroParcelasEmAberto().equals(1)) {
            this.setIncluirParcelasRecorrenciaListaPrevisto(false);
            this.setIncluirParcelasEmAbertoListaPrevisto(true);
        } else if (this.getFiltroParcelasEmAberto().equals(2)) {
            this.setIncluirParcelasRecorrenciaListaPrevisto(true);
            this.setIncluirParcelasEmAbertoListaPrevisto(false);
        }
    }
}

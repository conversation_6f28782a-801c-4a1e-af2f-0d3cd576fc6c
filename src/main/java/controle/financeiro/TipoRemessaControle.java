package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.Iterator;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.facade.jdbc.financeiro.TipoRemessa;
import negocio.comuns.utilitarias.*;
import negocio.comuns.financeiro.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * tipoRemessaForm.jsp tipoRemessaCons.jsp) com as funcionalidades da classe <code>TipoRemessa</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see TipoRemessa
 * @see TipoRemessaVO
 */
public class TipoRemessaControle extends SuperControle {

    private TipoRemessaVO tipoRemessaVO;
    private List listaSelectItemTipoRetorno;
    private List<SelectItem> listaSelectItemArquivoLayoutRemessa;
    private String msgAlert;

    /**
     * Interface <code>TipoRemessaInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public TipoRemessaControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>TipoRemessa</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setTipoRemessaVO(new TipoRemessaVO());
        inicializarListasSelectItemTodosComboBox();
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>TipoRemessa</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            TipoRemessaVO obj = getFacade().getTipoRemessa().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setTipoRemessaVO(obj);
            tipoRemessaVO.registrarObjetoVOAntesDaAlteracao();
            inicializarListasSelectItemTodosComboBox();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>TipoRemessaVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(TipoRemessaVO obj) {
        if (obj.getTipoRetorno() == null) {
            obj.setTipoRetorno(new TipoRetornoVO());
        }
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>TipoRemessa</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (tipoRemessaVO.isNovoObj()) {
                getFacade().getTipoRemessa().incluir(tipoRemessaVO);
                incluirLogInclusao();
            } else {
                getFacade().getTipoRemessa().alterar(tipoRemessaVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP TipoRemessaCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getTipoRemessa().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getTipoRemessa().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricaoTipoRetorno")) {
                objs = getFacade().getTipoRemessa().consultarPorDescricaoTipoRetorno(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("arquivoLayoutRemessa")) {
                objs = getFacade().getTipoRemessa().consultarPorArquivoLayoutRemessa(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("tipoRemessa")) {
                objs = getFacade().getTipoRemessa().consultarPorTipoRemessa(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>TipoRemessaVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getTipoRemessa().excluir(tipoRemessaVO);
            incluirLogExclusao();
            setTipoRemessaVO(new TipoRemessaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"tiporemessa\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"tiporemessa\" violates foreign key")){
                setMensagemDetalhada("Este tipo de remessa não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>TipoRetorno</code>.
     */
    public void montarListaSelectItemTipoRetorno(String prm) throws Exception {
        List resultadoConsulta = consultarTipoRetornoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        while (i.hasNext()) {
            TipoRetornoVO obj = (TipoRetornoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        setListaSelectItemTipoRetorno(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>TipoRetorno</code>.
     * Buscando todos os objetos correspondentes a entidade <code>TipoRetorno</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemTipoRetorno() {
        try {
            montarListaSelectItemTipoRetorno("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void montarListaSelectItemArquivoLayout() {
        List<SelectItem> selectItems = new ArrayList<SelectItem>();
        for (ArquivoLayoutRemessaEnum arquivoEnum : ArquivoLayoutRemessaEnum.values()) {
            SelectItem item = new SelectItem();
            item.setValue(arquivoEnum);
            item.setLabel(arquivoEnum.getDescricao());
            selectItems.add(item);
        }
        setListaSelectItemArquivoLayoutRemessa(selectItems);
    }

    private List consultarTipoRetornoPorDescricao(String descricaoPrm) throws Exception {
        return getFacade().getTipoRetorno().consultarPorDescricao(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemTipoRetorno();
        montarListaSelectItemArquivoLayout();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricaoTipoRetorno", "Tipo de Retorno"));
        itens.add(new SelectItem("arquivoLayoutRemessa", "Arquivo de Layout da Remessa"));
        itens.add(new SelectItem("tipoRemessa", "Tipo de Remessa"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public List getListaSelectItemTipoRetorno() {
        return (listaSelectItemTipoRetorno);
    }

    public void setListaSelectItemTipoRetorno(List listaSelectItemTipoRetorno) {
        this.listaSelectItemTipoRetorno = listaSelectItemTipoRetorno;
    }

    public TipoRemessaVO getTipoRemessaVO() {
        return tipoRemessaVO;
    }

    public void setTipoRemessaVO(TipoRemessaVO tipoRemessaVO) {
        this.tipoRemessaVO = tipoRemessaVO;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getTipoRemessa().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public void setListaSelectItemArquivoLayoutRemessa(List<SelectItem> listaSelectItemArquivoLayoutRemessa) {
        this.listaSelectItemArquivoLayoutRemessa = listaSelectItemArquivoLayoutRemessa;
    }

    public List<SelectItem> getListaSelectItemArquivoLayoutRemessa() {
        return listaSelectItemArquivoLayoutRemessa;
    }
    
    
    public void incluirLogInclusao() throws Exception {
        try {
            tipoRemessaVO.setObjetoVOAntesAlteracao(new TipoRemessaVO());
            tipoRemessaVO.setNovoObj(true);
            registrarLogObjetoVO(tipoRemessaVO, tipoRemessaVO.getCodigo(), "TIPOREMESSA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOREMESSA", tipoRemessaVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TIPOREMESSA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoRemessaVO.setNovoObj(new Boolean(false));
        tipoRemessaVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            tipoRemessaVO.setObjetoVOAntesAlteracao(new TipoRemessaVO());
            tipoRemessaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(tipoRemessaVO, tipoRemessaVO.getCodigo(), "TIPOREMESSA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOREMESSA", tipoRemessaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TIPOREMESSA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(tipoRemessaVO, tipoRemessaVO.getCodigo(), "TIPOREMESSA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOREMESSA", tipoRemessaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TIPOREMESSA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoRemessaVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = tipoRemessaVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), tipoRemessaVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       tipoRemessaVO = new TipoRemessaVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Tipo de Remessa",
                "Deseja excluir o Tipo de Remessa?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }
}

package controle.financeiro;

import negocio.comuns.arquitetura.SuperVO;

public class SimulacaoSaldoVO extends SuperVO {

    private Integer codigo;
    private Double saldo;
    private String dia;
    private String dataLancamento;
    private Integer empresa;
    private Integer usuarioLancamento;
    private Integer ano;
    private Integer mes;
    private Boolean saldoInicial = false;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getSaldo() {
        return saldo;
    }

    public void setSaldo(Double saldo) {
        this.saldo = saldo;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getUsuarioLancamento() {
        return usuarioLancamento;
    }

    public void setUsuarioLancamento(Integer usuarioLancamento) {
        this.usuarioLancamento = usuarioLancamento;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Boolean getSaldoInicial() {
        return saldoInicial;
    }

    public void setSaldoInicial(Boolean saldoInicial) {
        this.saldoInicial = saldoInicial;
    }
}

package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.financeiro.enumerador.SituacaoNFCeEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.nfe.enumerador.StatusNFCeEnum;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.*;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.model.SelectItem;
import java.util.*;

public class GestaoNFCeControle extends SuperControle {

    private List<ItemGestaoNotasTO> listaItens = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> listaItensApresentar = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> totalizadores = new ArrayList<ItemGestaoNotasTO>();
    private List<ItemGestaoNotasTO> totalFormaPg = new ArrayList<ItemGestaoNotasTO>();
    private Date dataInicio = new Date();
    private Date dataFim = new Date();
    private Double valorParaSelecionar = 0.0;
    private List<SelectItem> selectItemsFormaDePagamento = new ArrayList<SelectItem>();
    private Integer formaPagamentoSelecionado = 0;
    private Integer codpessoa;

    private EmpresaVO empresaVO = null;

    private ItemGestaoNotasTO selecionado = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO emitido = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO periodo = new ItemGestaoNotasTO();
    private ItemGestaoNotasTO emitir = new ItemGestaoNotasTO();

    private String nomeClasse = "GestaoNFCeControle";
    private int scrollerPage;
    private String onComplete;
    private TipoRelatorioDF tipoRelatorioDF = TipoRelatorioDF.FATURAMENTO_DE_CAIXA;
    private String tituloRelatorio;
    private List<SituacaoNFCeEnum> situacaoNFCe;
    private boolean marcarTodos = false;
    private int totalizadorNotasEnviadas = 0;
    private int totalizadorNotasAEnviar = 0;
    private boolean loadEnviandoNotas = true;
    private String msgEnviandoNotas = "Aguarde, estamos iniciando o envio de notas...";
    private List<PlanoVO> planos;
    private String qtdPlanosSelecionados;
    private boolean selecionarTodosPlanos = false;
    private boolean excluirTodas = false;
    private boolean desvincularTodas = false;

    public GestaoNFCeControle() throws Exception {
        carregarListSelectItemsFormaDePagamento();
        montarListaEmpresas();
        prepararTela();
    }

    public void prepararTela() {
        try {
            setTotalizadorNotasEnviadas(0);
            situacaoNFCe = JSFUtilities.getListFromEnum(SituacaoNFCeEnum.class);
            setCodPessoa(null);
            setListaItens(new ArrayList<ItemGestaoNotasTO>());
            setListaItensApresentar(new ArrayList<ItemGestaoNotasTO>());
            setEmpresaVO(getEmpresaLogado());
            if (!UtilReflection.objetoMaiorQueZero(this.empresaVO, "getCodigo()")) {
                setEmpresaVO(new EmpresaVO());
            }
            totalizar();
            carregarListaPlanos();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void inicializarTotalizadorEmitir() {
        setEmitir(new ItemGestaoNotasTO());
        getEmitir().setNome("A Enviar");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!item.getNfseemitida()) {
                getEmitir().setQuantidade(getEmitir().getQuantidade() + 1);
                getEmitir().setValor(getEmitir().getValor() + item.getValor());
            }
        }
    }

    private void inicializarTotalizadorPeriodo() {
        setPeriodo(new ItemGestaoNotasTO());
        getPeriodo().setNome("Total do Período");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            getPeriodo().setQuantidade(getPeriodo().getQuantidade() + 1);
            getPeriodo().setValor(getPeriodo().getValor() + item.getValor());
        }
    }

    private void inicializarTotalizadorEmitido() {
        setEmitido(new ItemGestaoNotasTO());
        getEmitido().setNome("Enviada(s)");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.getNfseemitida()) {
                getEmitido().setQuantidade(getEmitido().getQuantidade() + 1);
                getEmitido().setValor(getEmitido().getValor() + item.getValor());
            }
        }
    }

    private void inicializarTotalizadorSelecionado() {
        setSelecionado(new ItemGestaoNotasTO());
        getSelecionado().setNome("Selecionado");
        calcularValorSelecionado();
    }

    public void totalizar() throws Exception {
        inicializarTotalizadorSelecionado();
        inicializarTotalizadorEmitido();
        inicializarTotalizadorPeriodo();
        inicializarTotalizadorEmitir();

        totalizadores = new ArrayList<ItemGestaoNotasTO>();
        totalizadores.add(getSelecionado());
        totalizadores.add(getEmitido());
        totalizadores.add(getPeriodo());
        totalizadores.add(getEmitir());

        totalizarFormaPagamento();
    }

    private void totalizarFormaPagamento() throws Exception {
        List<FormaPagamentoVO> formasDePagamento = getFacade().getFormaPagamento().consultarPorTipoFormaPagamento("", false, false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        setTotalFormaPg(new ArrayList<ItemGestaoNotasTO>());
        for (FormaPagamentoVO formaPagamento : formasDePagamento) {
            ItemGestaoNotasTO itemTotal = new ItemGestaoNotasTO();
            itemTotal.setNome(formaPagamento.getDescricao());
            itemTotal.getFormasPagamento().add(formaPagamento.getCodigo());
            getTotalFormaPg().add(itemTotal);
        }

        for (ItemGestaoNotasTO item : getListaItens()) {
            for (Map.Entry<Integer, Double> entrySet : item.getValoresEFormas().entrySet()) {
                ItemGestaoNotasTO itemFormaPG = obtenhaItemPorFormaPG(entrySet.getKey());
                if (itemFormaPG != null) {
                    itemFormaPG.setValor(itemFormaPG.getValor() + entrySet.getValue());
                }
            }
        }

        List<ItemGestaoNotasTO> listaApresentar = new ArrayList<ItemGestaoNotasTO>();
        for (ItemGestaoNotasTO forma : getTotalFormaPg()) {
            if (!UteisValidacao.emptyNumber(forma.getValor())) {
                listaApresentar.add(forma);
            }
        }
        setTotalFormaPg(listaApresentar);
    }

    private ItemGestaoNotasTO obtenhaItemPorFormaPG(Integer codFormaPagamento) {
        for (ItemGestaoNotasTO itemGestaoNotasTO : getTotalFormaPg()) {
            if (codFormaPagamento.equals(itemGestaoNotasTO.getFormasPagamento().get(0))) {
                return itemGestaoNotasTO;
            }
        }
        return null;
    }

    private void carregarListSelectItemsFormaDePagamento() throws Exception {
        List<FormaPagamentoVO> formasDePagamento = getFacade().getFormaPagamento().consultarPorTipoFormaPagamento("", false, false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        setSelectItemsFormaDePagamento(new ArrayList<SelectItem>());
        getSelectItemsFormaDePagamento().add(new SelectItem(0, "TODAS AS FORMAS DE PAGAMENTO"));
        for (FormaPagamentoVO formaPagamento : formasDePagamento) {
            getSelectItemsFormaDePagamento().add(new SelectItem(formaPagamento.getCodigo(), formaPagamento.getDescricao()));
        }
    }

    public void consultarDadosEmpresa() {
        try {
            if (UtilReflection.objetoMaiorQueZero(getEmpresaVO(), "getCodigo()")) {
                setEmpresaVO(getFacade().getEmpresa().consultarPorCodigo(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            } else {
                setEmpresaVO(new EmpresaVO());
            }
            carregarListaPlanos();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void consultarItens() {
        try {
            if (!UtilReflection.objetoMaiorQueZero(this.getEmpresaVO(), "getCodigo()")) {
                throw new ConsistirException("Selecione a empresa.");
            }

            List<ItemGestaoNotasTO> lista = getFacade().getGestaoNotas().obterDadosNFCe(
                    tipoRelatorioDF, empresaVO.getCodigo(), getFormaPagamentoSelecionado(),
                    getDataInicio(), getDataFim(), getCodPessoa(), false, obterListaPlanosSelecionados(false));

            if (UteisValidacao.emptyList(lista)) {
                throw new Exception("Nenhum item encontrado.");
            }

            setListaItens(lista);
            montarListaApresentarFaturamentoRecebido();
            totalizar();
            montarSucesso("msg_dados_consultados");
        } catch (Exception err) {
            montarErro(err);
        }
    }

    public void consultarItensAutomatico(EmpresaVO empresaVO, Integer formaPagamento, Date dataInicio, Date dataFim, boolean processoAutomatico) throws Exception {
        setEmpresaVO(empresaVO);

        List<ItemGestaoNotasTO> itensConsulta = getFacade().getGestaoNotas().obterDadosNFCe(
                tipoRelatorioDF, empresaVO.getCodigo(), formaPagamento, dataInicio, dataFim, getCodPessoa(), processoAutomatico, null);

        setListaItens(itensConsulta);
        setListaItensApresentar(getListaItens());
    }

    public void selecionarTodosItens() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!item.getNfseemitida() && !item.isSelecionado()) {
                item.setSelecionado(true);
            }
        }
    }

    private void montarListaApresentarFaturamentoRecebido() throws Exception {

        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(this.empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        String tipoProdutoEmissaoNFCe = empresaVO.getTipoProdutoEmissaoNFCe();

        for (ItemGestaoNotasTO item : getListaItens()) {
            String[] produtosPagos = item.getProdutosPagos().split("\\|");
            Double valorTotalProdutosPagos = 0.0;
            StringBuilder descricaoProdutosPagos = new StringBuilder();
            for (String produtoPago : produtosPagos) {
                if (!produtoPago.isEmpty()) {
                    String[] produtosPagosSplit = produtoPago.split(",");
                    if (!UteisValidacao.emptyString(tipoProdutoEmissaoNFCe) && !tipoProdutoEmissaoNFCe.contains(produtosPagosSplit[1])) {
                        continue;
                    }
                    String codigoProduto = produtosPagosSplit[0];
                    Double valorPagoProduto = Double.valueOf(produtosPagosSplit[3]);
                    Integer codProduto = Integer.parseInt(codigoProduto);
                    MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(codProduto, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    descricaoProdutosPagos.append(movProdutoVO.getDescricao()).append(" <br>");
                    valorTotalProdutosPagos = (valorTotalProdutosPagos + valorPagoProduto);
                }
            }
            item.setDescricaoProdutosPagos(descricaoProdutosPagos.toString());
            item.setValor(valorTotalProdutosPagos);
        }
        setListaItensApresentar(getListaItens());
    }

    public void calcularValorSelecionado() {
        double valorSelecionado = 0.0;
        Integer selecionado = 0;
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.isSelecionado()) {
                selecionado++;
                valorSelecionado += item.getValor();
            }
        }
        getSelecionado().setQuantidade(selecionado);
        getSelecionado().setValor(valorSelecionado);
    }

    public Object selecionarValor() throws Exception {
        List<ItemGestaoNotasTO> itensEmitir = new ArrayList<ItemGestaoNotasTO>();
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (!item.getNfseemitida()) {
                item.setSelecionado(false);
                itensEmitir.add(item);
            }
        }

        Double valorSelecionado = 0.0;

        Ordenacao.ordenarLista(itensEmitir, "parcialmenteemitida");
        Collections.reverse(itensEmitir);

        for (ItemGestaoNotasTO item : itensEmitir) {
            if (valorSelecionado < getValorParaSelecionar() && item.getParcialmenteemitida() && (this.empresaVO.isPermiteGerarNotaManual() || !UteisValidacao.emptyString(item.getCpf()))) {
                item.setSelecionado(true);
                valorSelecionado += item.getValor();
            }
        }

        Ordenacao.ordenarLista(itensEmitir, "valor");
        for (ItemGestaoNotasTO item : itensEmitir) {
            if (valorSelecionado < getValorParaSelecionar() && !item.isSelecionado() && (this.empresaVO.isPermiteGerarNotaManual() || !UteisValidacao.emptyString(item.getCpf()))) {
                item.setSelecionado(true);
                valorSelecionado += item.getValor();
            }
        }

        totalizar();
        return true;
    }

    public boolean enviarNotas() {
        try {
            limparMsg();
            return enviarNotas(null, getKey());
        } catch (Exception ex){
            montarErro(ex);
            return false;
        }
    }

    public boolean enviarNotas(EmpresaVO empresaVO, String chave) throws Exception {
        setTotalizadorNotasEnviadas(0);
        setTotalizadorNotasAEnviar(0);
        setLoadEnviandoNotas(true);
        setMsgEnviandoNotas("Aguarde, estamos iniciando o envio das notas...");

//        if (certificadoVencido(empresaVO)) {
//            setMsgEnviandoNotas("Certificado vencido! Solicite suporte após a atualização.");
//            Thread.sleep(5000);
//            return true;
//        }

        List<ItemGestaoNotasTO> notasEmitir = new ArrayList<ItemGestaoNotasTO>();
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            item.setRetorno("");
            if (item.isSelecionado()) {
                notasEmitir.add(item);
            }
        }

        setTotalizadorNotasAEnviar(notasEmitir.size());
        setMsgEnviandoNotas("Enviando notas...");

        UsuarioVO usuarioVO = null;
        if (JSFUtilities.isJSFContext()) {
            usuarioVO = getUsuarioLogado();
        } else {
            usuarioVO = getFacade().getUsuario().getUsuarioRecorrencia();
        }

        if(this.empresaVO.getConfiguracaoNotaFiscalNFCe().getTipoNotaFiscal().equals(TipoNotaFiscalEnum.NFE)){
            getFacade().getNotaFiscal().processarItensIndividual(chave, TipoNotaFiscalEnum.NFE, usuarioVO, notasEmitir, null, null, TipoRelatorioDF.FATURAMENTO_DE_CAIXA);
        } else {
            getFacade().getNotaFiscal().processarItensIndividual(chave, TipoNotaFiscalEnum.NFCE, usuarioVO, notasEmitir, null, null, TipoRelatorioDF.FATURAMENTO_DE_CAIXA);
        }


        setTotalizadorNotasEnviadas(0);
        setTotalizadorNotasAEnviar(0);

        totalizar();
        montarLog(notasEmitir);
        setLoadEnviandoNotas(false);
        return true;
    }

    private boolean certificadoVencido(EmpresaVO empresaVO) throws Exception {
//        empresaVO = empresaVO == null ? getEmpresaLogado() : empresaVO;
//        if (empresaVO.isValidarCertificado()) {
//            return getFacade().getNotaFiscalConsumidorEletronica().isCertificadoVencido(empresaVO.getCNPJ());
//        }
        return false;
    }

    public void irParaTelaClienteColaborador() {
        ItemGestaoNotasTO obj = (ItemGestaoNotasTO) context().getExternalContext().getRequestMap().get("item");
        try {
            if (obj == null) {
                throw new Exception("Cliente ou Colaborador Não Encontrado.");
            } else if (obj.getCodCliente() == 0) {
                ColaboradorVO colaboradorVO = getFacade().getColaborador().consultarPorChavePrimaria(obj.getCodColaborador(), Uteis.NIVELMONTARDADOS_MINIMOS);
                irParaTelaColaborador(colaboradorVO);
                setOnComplete("abrirPopup('colaboradorForm.jsp', 'Colaborador', 1024, 700);");
            } else {
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCodCliente());
                irParaTelaCliente(clienteVO);
                setOnComplete("abrirPopup('clienteNav.jsp?page=viewCliente', 'Cliente', 1024, 700);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog(nomeClasse));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, 0);
    }

    private void gravarLog(double valorEmitido, double valorNaoEmitido, String resultadoNotas) throws Exception {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setNomeEntidade(nomeClasse.toUpperCase());
            obj.setNomeEntidadeDescricao(nomeClasse.toUpperCase());
            obj.setOperacao("EMISSÃO DE NFCe NO VALOR DE " + Formatador.formatarValorMonetario(valorEmitido + valorNaoEmitido));
            try {
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("PROCESSO DIÁRIO - NFCe");
            }
            obj.setNomeCampo("NFCe");
            obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("VALOR ENVIADO = " + Formatador.formatarValorMonetario(valorEmitido) +
                    "\r\nVALOR NÃO ENVIADO = " + Formatador.formatarValorMonetario(valorNaoEmitido) +
                    "\r\nDATA INICIAL = " + Uteis.getData(getDataInicio()) +
                    "\r\nDATA FINAL = " + Uteis.getData(getDataFim()) +
                    "\r\n\r\n" + resultadoNotas);
            registrarLogObjetoVO(obj, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(nomeClasse.toUpperCase(), 0, "ERRO AO GERAR LOG DA GESTÃO DE NOTAS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    private void montarLog(List<ItemGestaoNotasTO> notas) throws Exception {

        double valorEmitido = 0.0;
        double valorNaoEmitido = 0.0;
        StringBuilder notasEnviadas = new StringBuilder();
        notasEnviadas.append("NFCe ENVIADAS \r\n");
        StringBuilder notasNaoEnviadas = new StringBuilder();
        notasNaoEnviadas.append("NFCe NÃO ENVIADAS \r\n");
        for (ItemGestaoNotasTO item : notas) {
            if (item.getNfseemitida()) {
                valorEmitido += item.getValor();
                notasEnviadas.append("ID_NFCe: ").append(item.getId_NFCe()).append(" || ").append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO: ").append(item.getRetorno()).append("\r\n");
            } else {
                valorNaoEmitido += item.getValor();
                notasNaoEnviadas.append(item.getNome()).append(" || VALOR: ").append(item.getValor_apresentar()).append(" || RESULTADO:  ").append(item.getRetorno()).append("\r\n");
            }
        }
        gravarLog(valorEmitido, valorNaoEmitido, notasEnviadas + " \r\n" + notasNaoEnviadas);
    }

    public List<PessoaVO> executarAutocompleteConsultaPessoa(Object suggest) {
        String pref = (String) suggest;
        ArrayList<PessoaVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPessoaComLimite(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().consultarPessoaPorNomeComLimite(getEmpresaLogado().getCodigo(), pref, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPessoaSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        codpessoa = (pessoaVO.getCodigo());
    }

    public List<ItemGestaoNotasTO> getTotalizadores() {
        return totalizadores;
    }

    public void setTotalizadores(List<ItemGestaoNotasTO> totalizadores) {
        this.totalizadores = totalizadores;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Double getValorParaSelecionar() {
        return valorParaSelecionar;
    }

    public void setValorParaSelecionar(Double valorParaSelecionar) {
        this.valorParaSelecionar = valorParaSelecionar;
    }

    public List<SelectItem> getSelectItemsFormaDePagamento() {
        return selectItemsFormaDePagamento;
    }

    public void setSelectItemsFormaDePagamento(List<SelectItem> selectItemsFormaDePagamento) {
        this.selectItemsFormaDePagamento = selectItemsFormaDePagamento;
    }

    public Integer getFormaPagamentoSelecionado() {
        return formaPagamentoSelecionado;
    }

    public void setFormaPagamentoSelecionado(Integer formaPagamentoSelecionado) {
        this.formaPagamentoSelecionado = formaPagamentoSelecionado;
    }

    public List<ItemGestaoNotasTO> getListaItens() {
        return listaItens;
    }

    public void setListaItens(List<ItemGestaoNotasTO> listaItens) {
        this.listaItens = listaItens;
    }

    public ItemGestaoNotasTO getSelecionado() {
        return selecionado;
    }

    public void setSelecionado(ItemGestaoNotasTO selecionado) {
        this.selecionado = selecionado;
    }

    public ItemGestaoNotasTO getEmitido() {
        return emitido;
    }

    public void setEmitido(ItemGestaoNotasTO emitido) {
        this.emitido = emitido;
    }

    public ItemGestaoNotasTO getPeriodo() {
        return periodo;
    }

    public void setPeriodo(ItemGestaoNotasTO periodo) {
        this.periodo = periodo;
    }

    public ItemGestaoNotasTO getEmitir() {
        return emitir;
    }

    public void setEmitir(ItemGestaoNotasTO emitir) {
        this.emitir = emitir;
    }

    public List<ItemGestaoNotasTO> getTotalFormaPg() {
        return totalFormaPg;
    }

    public void setTotalFormaPg(List<ItemGestaoNotasTO> totalFormaPg) {
        this.totalFormaPg = totalFormaPg;
    }

    public List<ItemGestaoNotasTO> getListaItensApresentar() {
        return listaItensApresentar;
    }

    public void setListaItensApresentar(List<ItemGestaoNotasTO> listaItensApresentar) {
        this.listaItensApresentar = listaItensApresentar;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public Integer getCodPessoa() {
        return codpessoa;
    }

    public void setCodPessoa(Integer codpessoa) {
        this.codpessoa = codpessoa;
    }

    public void limparAluno() {
        setCodPessoa(null);
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public TipoRelatorioDF getTipoRelatorioDF() {
        return tipoRelatorioDF;
    }

    public void setTipoRelatorioDF(TipoRelatorioDF tipoRelatorioDF) {
        this.tipoRelatorioDF = tipoRelatorioDF;
    }

    public String getLabelPeriodo() {
        return "Período " + this.tipoRelatorioDF.getDescricao();
    }

    public String getTituloRelatorio() {
        if (tituloRelatorio == null) {
            tituloRelatorio = "";
        }
        return tituloRelatorio;
    }

    public void setTituloRelatorio(String tituloRelatorio) {
        this.tituloRelatorio = tituloRelatorio;
    }

    public List<SituacaoNFCeEnum> getSituacaoNFCe() {
        if (situacaoNFCe == null) {
            situacaoNFCe = new ArrayList<SituacaoNFCeEnum>();
        }
        return situacaoNFCe;
    }

    public void setSituacaoNFCe(List<SituacaoNFCeEnum> situacaoNFCe) {
        this.situacaoNFCe = situacaoNFCe;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public void acaoMarcarTodos() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (isMarcarTodos()) {
                if (!item.isSelecionado() && !item.getNfseemitida()) {
                    item.setSelecionado(true);
                }
            } else {
                if (item.isSelecionado() && !item.getNfseemitida()) {
                    item.setSelecionado(false);
                }
            }

        }
        calcularValorSelecionado();
    }

    public void acaoExcluirTodas() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (isExcluirTodas()) {
                if (!item.isSelecionadoExcluir() && loginControle.getPermissaoAcessoMenuVO().getPermiteExcluirNotaFiscal() && item.isApresentarExcluirNotaFiscalNFCe()) {
                    item.setSelecionadoExcluir(true);
                }
            } else {
                if (item.isSelecionadoExcluir() && loginControle.getPermissaoAcessoMenuVO().getPermiteExcluirNotaFiscal() && item.isApresentarExcluirNotaFiscalNFCe()) {
                    item.setSelecionadoExcluir(false);
                }
            }

        }
        //calcularValorSelecionado();
    }

    public void acaoDesvincularTodas() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (isDesvincularTodas()) {
                if (!item.isSelecionadoDesvincular() && item.getApresentarDesvincularNFCe()) {
                    item.setSelecionadoDesvincular(true);
                }
            } else {
                if (item.isSelecionadoDesvincular() && item.getApresentarDesvincularNFCe()) {
                    item.setSelecionadoDesvincular(false);
                }
            }

        }
        //calcularValorSelecionado();
    }

    public String getMsgEnviandoNotas() {
        return msgEnviandoNotas;
    }

    public void setMsgEnviandoNotas(String msgEnviandoNotas) {
        this.msgEnviandoNotas = msgEnviandoNotas;
    }

    public int getTotalizadorNotasAEnviar() {
        return totalizadorNotasAEnviar;
    }

    public void setTotalizadorNotasAEnviar(int totalizadorNotasAEnviar) {
        this.totalizadorNotasAEnviar = totalizadorNotasAEnviar;
    }

    public int getTotalizadorNotasEnviadas() {
        return totalizadorNotasEnviadas;
    }

    public void setTotalizadorNotasEnviadas(int totalizadorNotasEnviadas) {
        this.totalizadorNotasEnviadas = totalizadorNotasEnviadas;
    }

    public boolean isLoadEnviandoNotas() {
        return loadEnviandoNotas;
    }

    public void setLoadEnviandoNotas(boolean loadEnviandoNotas) {
        this.loadEnviandoNotas = loadEnviandoNotas;
    }

    public void desvincularSelecionados() {
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.isSelecionadoDesvincular() && item.getApresentarDesvincularNFCe()) {
                desvincularNota(item);
            }
        }
    }

    public void desvincularNota() {
        desvincularNota(null);
    }

    public void desvincularNota(ItemGestaoNotasTO itemGestaoNotasTO) {
        try {
            ItemGestaoNotasTO obj = null == itemGestaoNotasTO ? (ItemGestaoNotasTO) request().getAttribute("item") : itemGestaoNotasTO;

            boolean permiteExcluir = false;
            StatusNFCeEnum statusNFCeEnum;

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");

            Map<String, String> corpo = new HashMap<String, String>();
            if (obj.getNotaFiscalVO().getTipo().equals(TipoNotaFiscalEnum.NFCE)){
                corpo.put("status", obj.getId_NFCe().toString());

                String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nfce";
                String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
                JSONObject retornoJSON = new JSONObject(executeRequest);
                if (!retornoJSON.has("return")) {
                    throw new Exception("Erro ao tentar desvincular nota.");
                } else {
                    statusNFCeEnum = StatusNFCeEnum.valueOf(retornoJSON.getString("return"));
                    if (statusNFCeEnum.equals(StatusNFCeEnum.NAO_AUTORIZADO) || statusNFCeEnum.equals(StatusNFCeEnum.CANCELADO)) {
                        permiteExcluir = true;
                    }
                }

                if (permiteExcluir) {

                    NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO = getFacade().getNotaFiscalConsumidorEletronica().consultarPorIdNFCe(obj.getId_NFCe(), Uteis.NIVELMONTARDADOS_TODOS);
                    if (!UteisValidacao.emptyNumber(notaFiscalConsumidorEletronicaVO.getCodigo())) {
                        getFacade().getNotaFiscalConsumidorEletronica().excluirComLog(notaFiscalConsumidorEletronicaVO, getUsuarioLogado(), ProcessoAjusteGeralEnum.EXCLUIR_NFCe);
                    }
                    montarSucessoGrowl("Nota desvinculada. Status: " + statusNFCeEnum.getDescricao().toUpperCase());
                    obj.setId_NFCe(null);
                    obj.setNfseemitida(false);
                } else {
                    throw new Exception("Nota não pode ser desvinculada, nota está com status: " + statusNFCeEnum.getDescricao().toUpperCase());
                }
            } else {
               // corpo.put("desvincular", obj.getRps().toString());

              //  String urlConsultar = PropsService.getPropertyValue(PropsService.urlModuloNFSe) + "/nota";
             //   String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(urlConsultar, headers, corpo, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", "ISO-8859-1");
              //  JSONObject retornoJSON = new JSONObject(executeRequest);
              //  if (!retornoJSON.has("return")) {
              //      throw new Exception("Erro ao tentar desvincular nota.");
             //   }

                NFSeEmitidaVO nfSeEmitidaVO = getFacade().getNFSeEmitida().consultaPorCodigo(obj.getCodNFSeEmitida());
                if (nfSeEmitidaVO != null) {
                    getFacade().getNFSeEmitida().excluirComLog(nfSeEmitidaVO, getUsuarioLogado(), ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL);
                }

                StatusNotaEnum statusNotaEnum = getFacade().getLoteNFSe().retornarStatus(obj.getRps());
                montarSucessoGrowl("Nota desvinculada. Status: " + statusNotaEnum.getDescricao().toUpperCase());
                obj.setRps(null);
                obj.setNfseemitida(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<PlanoVO> getPlanos() {
        if (planos == null) {
            planos = new ArrayList<PlanoVO>();
        }
        return planos;
    }

    public void setPlanos(List<PlanoVO> planos) {
        this.planos = planos;
    }

    public String getQtdPlanosSelecionados() {
        if (qtdPlanosSelecionados == null) {
            qtdPlanosSelecionados = "";
        }
        return qtdPlanosSelecionados;
    }

    public void setQtdPlanosSelecionados(String qtdPlanosSelecionados) {
        this.qtdPlanosSelecionados = qtdPlanosSelecionados;
    }

    public void carregarListaPlanos() {
        try {
            setQtdPlanosSelecionados("");
            setSelecionarTodosPlanos(false);
            setPlanos(new ArrayList<PlanoVO>());
            if (getEmpresaVO() != null && !UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
                setPlanos(getFacade().getPlano().consultarPorCodigoEmpresa(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            } else {
                setPlanos(getFacade().getPlano().consultarTodos(Calendario.hoje(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            Ordenacao.ordenarLista(getPlanos(), "descricao");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void calcularPlanosSelecionados() {
        try {
            setQtdPlanosSelecionados("");
            Integer qtd = 0;
            for (PlanoVO planoVO : getPlanos()) {
                if (planoVO.isSelecionado()) {
                    qtd++;
                }
            }

            if (qtd == getPlanos().size()) {
                setSelecionarTodosPlanos(true);
            } else {
                setSelecionarTodosPlanos(false);
            }

            if (UteisValidacao.emptyNumber(qtd)) {
                setQtdPlanosSelecionados(" (Nenhum plano selecionado.)");
            } else if (qtd > 1){
                setQtdPlanosSelecionados(" ("+qtd + " planos selecionados)");
            } else {
                setQtdPlanosSelecionados(" ("+ qtd + " plano selecionado)");
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void marcarTodosPlanos() {
        for (PlanoVO planoVO : getPlanos()) {
            planoVO.setSelecionado(isSelecionarTodosPlanos());
        }
        calcularPlanosSelecionados();
    }

    private List<PlanoVO> obterListaPlanosSelecionados(boolean familia) {

        if (familia) { //não utilizado para plano familia
            return new ArrayList<PlanoVO>();
        }

        List<PlanoVO> retorno = new ArrayList<PlanoVO>();
        for (PlanoVO planoVO : getPlanos()) {
            if (planoVO.isSelecionado()) {
                retorno.add(planoVO);
            }
        }
        return retorno;
    }

    public boolean isSelecionarTodosPlanos() {
        return selecionarTodosPlanos;
    }

    public void setSelecionarTodosPlanos(boolean selecionarTodosPlanos) {
        this.selecionarTodosPlanos = selecionarTodosPlanos;
    }

    public boolean isExcluirTodas() {
        return excluirTodas;
    }

    public void setExcluirTodas(boolean excluirTodas) {
        this.excluirTodas = excluirTodas;
    }

    public boolean isDesvincularTodas() {
        return desvincularTodas;
    }

    public void setDesvincularTodas(boolean desvincularTodas) {
        this.desvincularTodas = desvincularTodas;
    }

    public void excluirSelecionados() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        for (ItemGestaoNotasTO item : getListaItensApresentar()) {
            if (item.isSelecionadoExcluir() && loginControle.getPermissaoAcessoMenuVO().getPermiteExcluirNotaFiscal() && item.isApresentarExcluirNotaFiscalNFCe()) {
                excluirNotaFiscal(item);
            }
        }
    }

    public void excluirNotaFiscal() {
        excluirNotaFiscal(null);
    }

    public void excluirNotaFiscal(ItemGestaoNotasTO itemGestaoNotasTO) {
        try {
            ItemGestaoNotasTO obj = null == itemGestaoNotasTO ?  (ItemGestaoNotasTO) request().getAttribute("item") : itemGestaoNotasTO;
            if (!UteisValidacao.emptyNumber(obj.getCodNFCeEmitida()) && !UteisValidacao.emptyNumber(obj.getCodNotaFiscal())) {

                getFacade().getNotaFiscalConsumidorEletronica().excluirComLogEnotas(obj.getCodNotaFiscal(), new NotaFiscalConsumidorEletronicaVO(obj.getCodNFCeEmitida()), getUsuarioLogado());

                obj.setRps(null);
                obj.setId_NFCe(null);
                obj.setNfseemitida(false);
                obj.setCodNotaFiscal(0);
                obj.setNotaFiscalVO(new NotaFiscalVO());
                montarSucessoGrowl("Nota excluída com sucesso.");
            } else {
                throw new Exception("Nota não pode ser excluida.");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }
}

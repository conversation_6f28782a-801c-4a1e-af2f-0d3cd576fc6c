package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RelatorioOrcamentarioConfigVO;
import negocio.comuns.financeiro.RelatorioOrcamentarioValoresPrevisaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.bi.exportador.Exportador;
import servicos.bi.exportador.RelatorioBuilder;

import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class RelatorioOrcamentarioConfigControle extends SuperControleRelatorio {

    private EmpresaVO empresa = new EmpresaVO();
    private Mes mes = Mes.VAZIO;
    private int ano = Uteis.getAnoData(Calendario.hoje());
    private String descricao = "";
    private List<RelatorioOrcamentarioConfigVO> previsoes = new ArrayList<RelatorioOrcamentarioConfigVO>();
    private RelatorioOrcamentarioConfigVO previsao = new RelatorioOrcamentarioConfigVO();
    private Date periodoDe = Calendario.hoje();
    private Date periodoAte = Calendario.hoje();
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String urlPlanilhaRelOrcamentarioConfig;
    private File filePlanilhaConfiguracao = null;
    private String onComplete;

    public RelatorioOrcamentarioConfigControle() throws Exception {
        inicializarEmpresa();
        inicializarDados(true);
    }

    private void inicializarEmpresa() throws Exception {
        setEmpresa((EmpresaVO) getEmpresaLogado().getClone(true));
    }

    protected void inicializarDados(boolean limparEmpresa) throws Exception {
        if (limparEmpresa) {
            empresa = new EmpresaVO();
        }
        mes = Mes.VAZIO;
        ano = Uteis.getAnoData(Calendario.hoje());
        descricao = "";
        previsoes = new ArrayList<RelatorioOrcamentarioConfigVO>();

        periodoDe = Uteis.getDate("01/01/" + Calendario.getInstance().get(Calendar.YEAR));
        periodoAte = Uteis.getDate("31/12/" + (Calendario.getInstance().get(Calendar.YEAR) + 1));
        filePlanilhaConfiguracao = null;
        urlPlanilhaRelOrcamentarioConfig = "";
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e){
            throw e;
        }
    }

    public String novo() throws Exception {
        inicializarMeta();
        return "editarRelatorioOrcamentarioConf";
    }

    private void inicializarMeta() throws Exception {
        previsao = new RelatorioOrcamentarioConfigVO();
        previsao.setAno(Uteis.getAnoData(Calendario.hoje()));
        inicializarValores();
    }

    private void inicializarValores() throws Exception {
        List<PlanoContaTO> planoDeContas = getFacade().getPlanoConta().consultarTodos();
        List<RelatorioOrcamentarioValoresPrevisaoVO> listaRetorno = new ArrayList<>();
        for (PlanoContaTO item: planoDeContas){
            RelatorioOrcamentarioValoresPrevisaoVO valor = new RelatorioOrcamentarioValoresPrevisaoVO();
            valor.setPlanoContaTO(item);
            listaRetorno.add(valor);
        }
        previsao.setValores(listaRetorno);
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        try {
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
        return objs;
    }

    public List<SelectItem> getListaSelectItemMeses() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        try {
            for (Mes obj : Mes.values()) {
                objs.add(new SelectItem(obj, obj.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
        return objs;
    }

    public String editarMeta() {
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            RelatorioOrcamentarioConfigVO obj = getFacade().getRelatorioOrcamentarioConfig().consultarPorChavePrimaria(codigoConsulta);
            previsao = (RelatorioOrcamentarioConfigVO) obj.getClone(true);
            previsao.setNovoObj(false);
            previsao.registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_editar");
            setSucesso(true);
            setErro(false);
            return "editarRelatorioOrcamentarioConf";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void autorizarMetasFinanceiro() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CadastroMetas", "9.15 - Cadastro de Metas");
            }
        }
    }

    public void gravar() {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            if (!loginControle.getPermissaoAcessoMenuVO().getVisualizarMetasFinanceirasTodasEmpresas()) {
                previsao.setEmpresa(getEmpresaLogado());
            }

            if (previsao.getNovoObj()) {
                getFacade().getRelatorioOrcamentarioConfig().incluir(previsao);
            } else {
                getFacade().getRelatorioOrcamentarioConfig().alterar(previsao);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    @SuppressWarnings("CallToThreadDumpStack")
    public String excluir() {
        try {
            getFacade().getRelatorioOrcamentarioConfig().excluir(previsao);
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "relatorioOrcamentarioConf";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public void gerarPlanilhaExcelPlanoDeContas(){
        try{
            String nomeArquivo = "modelo-relatorio-orcamentario-config.xlsx";
            File arquivo = new File(Uteis.obterCaminhoWeb() + File.separator + "modelo" + File.separator + nomeArquivo);
            if (arquivo.exists()) {
                arquivo.delete();
            } else {
                arquivo.mkdirs();
            }
            arquivo.createNewFile();

            List<PlanoContaTO> planoDeContas = getFacade().getPlanoConta().consultarTodos();
            RelatorioBuilder relatorio = new RelatorioBuilder();
            relatorio.addColuna("Código Plano","codigoPlano");
            relatorio.addColuna("Descrição", "descricao");
            relatorio.addColuna("Valor Previsão", "");
            relatorio.dado(planoDeContas);
            Exportador.exportarExcel(relatorio, arquivo);

            this.urlPlanilhaRelOrcamentarioConfig = "DownloadSV?mimeType=application/vnd.ms-excel&diretorio=modelo&relatorio=" + nomeArquivo;
        }catch (Exception ex){
            montarErro(ex);
            ex.printStackTrace();
        }
    }

    public void uploadPlanilhaConfiguracao(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        filePlanilhaConfiguracao = new File(item.getFile().getParent() + File.separator + item.getFileName());
        if (filePlanilhaConfiguracao.exists()) {
            filePlanilhaConfiguracao.delete();
        }
        final FileOutputStream out = new FileOutputStream(filePlanilhaConfiguracao);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void processarPlanilhaConfiguracao() throws Exception {
        limparMsg();

        Map<String, RelatorioOrcamentarioValoresPrevisaoVO> mapaValores = new HashMap<>();
        previsao.getValores().forEach(v -> mapaValores.put(v.getPlanoContaTO().getCodigoPlano(), v));

        List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getFilePlanilhaConfiguracao().getPath());
        for (XSSFRow linha : linhas) {
            try {
                String codigoPlano = LeitorExcel2010.obterString(linha, 0);
                String descricao = LeitorExcel2010.obterString(linha, 1);
                Double valorPrevisao = LeitorExcel2010.obterNumero(linha, 2).doubleValue();
                RelatorioOrcamentarioValoresPrevisaoVO relatorioOrcamentarioConfigVO = mapaValores.get(codigoPlano);
                if (relatorioOrcamentarioConfigVO != null) {
                    relatorioOrcamentarioConfigVO.setValor(valorPrevisao);
                } else {
                    throw new Exception("Plano de Conta não encontrado: " + codigoPlano + " - " + descricao);
                }
            } catch (Exception e) {
                montarErro("Falha ao processar planilha: " + e.getMessage());
                return;
            }
        }
        setFilePlanilhaConfiguracao(null);
        setUrlPlanilhaRelOrcamentarioConfig("");
        montarSucessoGrowl("Os valores da planilha foram aplicados com sucesso!");
    }

    public String voltarTelaConsulta() {
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(true);
        setErro(false);
        return "relatorioOrcamentarioConf";
    }

    public Date obterDataMesAno() throws Exception {
        return Uteis.getDate("01/" + previsao.getMes().getCodigo() + "/" + previsao.getAno());
    }

    public String getApresentarPeriodoDe() {
        return Uteis.getMesNomeReferencia(periodoDe) + "/" + Uteis.getAnoData(periodoDe);
    }

    public String getApresentarPeriodoAte() {
        return Uteis.getMesNomeReferencia(periodoAte) + "/" + Uteis.getAnoData(periodoAte);
    }

// * Gets e Sets ***************************************************************
    @Override
    public EmpresaVO getEmpresa() {
        return empresa;
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Mes getMes() {
        return mes;
    }

    public void setMes(Mes mes) {
        this.mes = mes;
    }

    public int getAno() {
        return ano;
    }

    public void setAno(int ano) {
        this.ano = ano;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<RelatorioOrcamentarioConfigVO> getPrevisoes() {
        return previsoes;
    }

    public void setPrevisoes(List<RelatorioOrcamentarioConfigVO> previsoes) {
        this.previsoes = previsoes;
    }

    public RelatorioOrcamentarioConfigVO getPrevisao() {
        if (previsao == null) {
            previsao = new RelatorioOrcamentarioConfigVO();
        }
        return previsao;
    }

    public void setMeta(RelatorioOrcamentarioConfigVO meta) {
        this.previsao = previsao;
    }

    public Date getPeriodoDe() {
        return periodoDe;
    }

    public void setPeriodoDe(Date periodoDe) {
        this.periodoDe = periodoDe;
    }

    public Date getPeriodoAte() {
        return periodoAte;
    }

    public void setPeriodoAte(Date periodoAte) {
        this.periodoAte = periodoAte;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public void setPrevisao(RelatorioOrcamentarioConfigVO previsao) {
        this.previsao = previsao;
    }

    public String getUrlPlanilhaRelOrcamentarioConfig() {
        return urlPlanilhaRelOrcamentarioConfig;
    }

    public void setUrlPlanilhaRelOrcamentarioConfig(String urlPlanilhaRelOrcamentarioConfig) {
        this.urlPlanilhaRelOrcamentarioConfig = urlPlanilhaRelOrcamentarioConfig;
    }

    public void removerPlanihaConfiguracao(){
        filePlanilhaConfiguracao = null;
    }

    public boolean isApresetarBotaoLerPlanilhaConfiguracao() {
        return filePlanilhaConfiguracao != null;
    }

    public File getFilePlanilhaConfiguracao() {
        return filePlanilhaConfiguracao;
    }

    public void setFilePlanilhaConfiguracao(File filePlanilhaConfiguracao) {
        this.filePlanilhaConfiguracao = filePlanilhaConfiguracao;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }
}

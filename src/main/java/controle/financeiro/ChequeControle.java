package controle.financeiro;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;

import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import br.com.pactosolucoes.comuns.util.JSFUtilities;

import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.HistoricoChequeVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.StatusCheque;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import controle.arquitetura.SuperControle;

public class ChequeControle extends SuperControle {

    private List<HistoricoChequeVO> historicoCheque = new ArrayList<HistoricoChequeVO>();
    private ChequeTO chequeTO = new ChequeTO();
    private List<SelectItem> itensStatus = new ArrayList<SelectItem>();
    private HistoricoChequeVO novoHistorico = new HistoricoChequeVO();
    private ChequeVO cheque = new ChequeVO();
    private boolean financeiro = false;
    private String banco = "";
    private String variavelCpfCnpj;
    private boolean apresentarCampoCPF;
    private boolean apresentarCampoCNPJ;
    private int qtdeCheques = 0;
    private List<SelectItem> listaSelectItemBanco;
    private BancoVO bancoSelected = null;
    private List<SelectItem> listaSelectStatus;
    private List<SelectItem> listaSelectItemCpfCnpj;
    private int novoStatus;
    private ConfiguracaoSistemaVO configuracaoSistemaVO;
    private String[] displayIdentificadorFront;

    public void prepareReciboGestaoRecebiveis(ActionEvent evt) throws Exception {
        Integer ch = (Integer) evt.getComponent().getAttributes().get("codigoCheque");
        if (ch != null) {
            GestaoRecebiveisControle controle = (GestaoRecebiveisControle) JSFUtilities.getManagedBeanValue(GestaoRecebiveisControle.class);
            for (ChequeTO chTO : controle.getListaCheques()) {
                if (chTO.getCodigo() == ch) {
                    evt.getComponent().getAttributes().put("chequeTO", chTO);
                    break;
                }
            }
        }
        escolherCheque(evt);
    }
    
    public void escolherCheque(ActionEvent evt) throws Exception {
        ChequeTO chTO = (ChequeTO) evt.getComponent().getAttributes().get("chequeTO");
        if (chTO != null) {
            chequeTO = chTO;
        }
        historicoCheque = getFacade().getFinanceiro().getHistoricoCheque().consultarPorChequeComposicao(chequeTO.getObterTodosChequesComposicao());
    }

    public void visualizarOperacao() {
        try {
            setMensagemID("msg_dados_editar");
            HistoricoChequeVO historico = (HistoricoChequeVO) context().getExternalContext().getRequestMap().get("historico");
            MovContaVO movContaVo = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(historico.getMovConta().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            MovContaControle movContaControle = (MovContaControle) JSFUtilities.getManagedBean(MovContaControle.class.getSimpleName());

            movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
            movContaVo.setMovContaRateios(getFacade().getFinanceiro().getMovContaRateio().consultarPorMovConta(movContaVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            movContaControle.setarMovConta(movContaVo, true, true);
            movContaControle.setLoteUsado(getFacade().getLote().consultarPorPagaMovConta(movContaVo, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Erro ao visualizar Lançamento do Financeiro. Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage());
        }
    }

    public void inicializarConfiguracaoSistema() {
        try {
            setConfiguracaoSistemaVO((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public ChequeControle() {
        novo();
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional();
    }

    public void novo() {
        montarListaSelectItemBanco();
        historicoCheque = new ArrayList<HistoricoChequeVO>();
        setVariavelCpfCnpj("");
        limparMsg();
        chequeTO = new ChequeTO();
        cheque = new ChequeVO();
        banco = "";
        bancoSelected = null;
        novoHistorico = new HistoricoChequeVO();
        qtdeCheques = 0;
    }

    public void novoFinanceiro() {
        financeiro = true;
        novo();
        qtdeCheques = 1;
    }

    public static void incrementaNumeroChequeLista(List<ChequeVO> listaCheques) throws Exception {
        String numeroCheque = "";
        for (int contador = 0; contador < listaCheques.size(); contador++) {
            ChequeVO ch = listaCheques.get(contador);
            if (contador == 0) {
                numeroCheque = ch.getNumero();
            } else {
                ch.setNumero(Uteis.getIncrementarNumeroCheque(numeroCheque));
                numeroCheque = ch.getNumero();
            }
        }
    }

    public void mostrarCampoCPF() {

        if (financeiro) {
            getCheque().setCnpj("");
            getCheque().setCpf("");
        } else {
            MovPagamentoControle controlador = (MovPagamentoControle) getControlador(MovPagamentoControle.class.getSimpleName());
            getCheque().setCnpj("");
            getCheque().setCpf(controlador.getMovPagamentoVO().getPessoa().getCfp());
        }

        if (getVariavelCpfCnpj() == null) {
            setVariavelCpfCnpj("");
        }
        if (getVariavelCpfCnpj().equals("")) {
            setApresentarCampoCPF(false);
            setApresentarCampoCNPJ(false);
        } else if (getVariavelCpfCnpj().equals("cpf")) {
            setApresentarCampoCPF(true);
            setApresentarCampoCNPJ(false);
        } else if (getVariavelCpfCnpj().equals("cnpj")) {
            setApresentarCampoCPF(false);
            setApresentarCampoCNPJ(true);
        }
    }

    public void consultarBancoPorCodigoBanco() {
        try {

            if (getCheque().getBanco().getCodigoBanco() != null && getCheque().getBanco().getCodigoBanco() != 0) {
                BancoVO obj = getFacade().getBanco().consultarCodigoBanco(getCheque().getBanco().getCodigoBanco(), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (obj != null && obj.getCodigo() != 0) {
                    getCheque().setBanco(new BancoVO());
                    getCheque().setBanco(obj);
                    bancoSelected = obj;
                    setMensagemDetalhada("", "");
                } else {
                    setMensagemDetalhada("Banco não encontrado.");
                }
            } else {
                setMensagemDetalhada("Banco não encontrado.");
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void alterarStatus() {
        try {
            setMsgAlert("");
            if (UteisValidacao.emptyNumber(novoStatus)) {
                setMensagemID("msg_selecione_novo_status");
            } else {
                getFacade().getFinanceiro().getHistoricoCheque().mudarStatus(StatusCheque.getStatusCheque(novoStatus), chequeTO.getObterTodosChequesComposicao());
                historicoCheque = getFacade().getFinanceiro().getHistoricoCheque().consultarPorChequeComposicao(chequeTO.getObterTodosChequesComposicao());
                setMensagemID("msg_sucesso_novo_status");
            }
            montarMsgAlert(getMensagem());
            limparMsg();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void consultarBancoPorCodigo() {
        try {
            if(null != bancoSelected){
                getCheque().setBanco(bancoSelected);
                setBanco(bancoSelected.getCodigoBanco().toString());
                setMensagemDetalhada("");
                setMensagem("");
                setMensagemID("");
            }else {
                banco = "";
                setMensagemDetalhada("Banco não encontrado.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<SelectItem> getListaSelectItemCpfCnpj() {
        if (listaSelectItemCpfCnpj == null) {
            listaSelectItemCpfCnpj = new ArrayList<SelectItem>();
            listaSelectItemCpfCnpj.add(new SelectItem("cpf", displayIdentificadorFront[0]));
            listaSelectItemCpfCnpj.add(new SelectItem("cnpj", displayIdentificadorFront[2]));
            try {
                SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
                Collections.sort((List<SelectItem>) listaSelectItemCpfCnpj, ordenador);
            } catch (Exception e) {}
        }
        return listaSelectItemCpfCnpj;
    }

    public void montarListaSelectItemBanco() {
        try {
            montarListaSelectItemBanco("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Nao foi Possível Montar a lista de Bancos.");
            setListaSelectItemBanco(new ArrayList<SelectItem>());
        }
    }

    public void montarListaSelectItemBanco(String prm) throws Exception {
        List resultadoConsulta = consultarBancoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(null, "SELECIONE UM BANCO"));
        while (i.hasNext()) {
            BancoVO obj = (BancoVO) i.next();
            objs.add(new SelectItem(obj, obj.getNome().toString()));
        }
        setListaSelectItemBanco(objs);
    }

    public List consultarBancoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getBanco().consultarPorNome(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    //--------------------------------------------------------------------\\-------------------------------------------------//
    public String getVariavelCpfCnpj() {
        return variavelCpfCnpj;
    }

    public void setVariavelCpfCnpj(String variavelCpfCnpj) {
        this.variavelCpfCnpj = variavelCpfCnpj;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        if(!UteisValidacao.emptyString(banco)) {
            ChequeVO aux = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            if (aux != null) {
                aux.getBanco().setCodigoBanco(Integer.valueOf(banco));
            } else {
                getCheque().getBanco().setCodigoBanco(Integer.valueOf(banco));
            }
        }
        this.banco = banco;
    }

    public void setHistoricoCheque(List<HistoricoChequeVO> historicoCheque) {
        this.historicoCheque = historicoCheque;
    }

    public List<HistoricoChequeVO> getHistoricoCheque() {
        return historicoCheque;
    }

    public void setChequeTO(ChequeTO cheque) {
        this.chequeTO = cheque;
    }

    public ChequeTO getChequeTO() {
        return chequeTO;
    }

    public List<SelectItem> getItensStatus() {
        if (itensStatus == null) {
            itensStatus = StatusCheque.getListaSelectItem();
        }
        return itensStatus;
    }

    public void setNovoHistorico(HistoricoChequeVO novoHistorico) {
        this.novoHistorico = novoHistorico;
    }

    public HistoricoChequeVO getNovoHistorico() {
        return novoHistorico;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public ChequeVO getCheque() {
        return cheque;
    }

    public void setFinanceiro(boolean financeiro) {
        this.financeiro = financeiro;
    }

    public boolean isFinanceiro() {
        return financeiro;
    }

    public void setApresentarCampoCPF(boolean apresentarCampoCPF) {
        this.apresentarCampoCPF = apresentarCampoCPF;
    }

    public boolean getApresentarCampoCPF() {
        return apresentarCampoCPF;
    }

    public void setApresentarCampoCNPJ(boolean apresentarCampoCNPJ) {
        this.apresentarCampoCNPJ = apresentarCampoCNPJ;
    }

    public boolean getApresentarCampoCNPJ() {
        return apresentarCampoCNPJ;
    }

    public void setQtdeCheques(int qtdeCheques) {
        this.qtdeCheques = qtdeCheques;
    }

    public int getQtdeCheques() {
        return qtdeCheques;
    }

    public List getListaSelectItemBanco() {
        return listaSelectItemBanco;
    }

    public void setListaSelectItemBanco(List listaSelectItemBanco) {
        this.listaSelectItemBanco = listaSelectItemBanco;
    }

    public void setListaSelectItemCpfCnpj(List listaSelectItemCpfCnpj) {
        this.listaSelectItemCpfCnpj = listaSelectItemCpfCnpj;
    }

    public void setListaSelectStatus(List<SelectItem> listaSelectStatus) {
        this.listaSelectStatus = listaSelectStatus;
    }

    public List<SelectItem> getListaSelectStatus() {
        if (listaSelectStatus == null) {
            listaSelectStatus = StatusCheque.getListaSelectItem();
        }
        return listaSelectStatus;
    }

    public void setNovoStatus(int novoStatus) {
        this.novoStatus = novoStatus;
    }

    public int getNovoStatus() {
        return novoStatus;
    }

    public BancoVO getBancoSelected() {
        return bancoSelected;
    }

    public void setBancoSelected(BancoVO bancoSelected) {
        this.bancoSelected = bancoSelected;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistemaVO() {
        return configuracaoSistemaVO;
    }

    public void setConfiguracaoSistemaVO(ConfiguracaoSistemaVO configuracaoSistemaVO) {
        this.configuracaoSistemaVO = configuracaoSistemaVO;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public String[] identificacaoPessoalInternacional() {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(getEmpresaLogado().getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;
    }
}

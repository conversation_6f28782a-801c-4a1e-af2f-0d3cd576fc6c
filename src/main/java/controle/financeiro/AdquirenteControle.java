package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import servicos.impl.geoitd.enums.AcquirerGeoitd;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class AdquirenteControle extends SuperControle {

    private AdquirenteVO adquirenteVO;
    private List<SelectItem> listaSelectItemTipoConvenio = new ArrayList<SelectItem>();
    private List<SelectItem> listaAdquirenteGeoitd = new ArrayList<SelectItem>();
    private String situacaoFiltro;
    private String msgAlert;
    private ConfiguracaoSistemaVO configuracaoSistema;



    public AdquirenteControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        inicializarConfiguracaoSistema();
        setMensagemID("");
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }
    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Pais</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        limparMsg();
        reset();
        montarListaSelectItemTipoConvenio();
        return "editar";
    }

    public void reset() {
        limparMsg();
        setAdquirenteVO(new AdquirenteVO());
        setSucesso(false);
        setErro(false);
    }

    public String editar() {
        montarListaSelectItemTipoConvenio();
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            AdquirenteVO obj = getFacade().getAdquirente().consultarPorCodigo(codigoConsulta);
            obj.setNovoObj(new Boolean(false));
            obj.registrarObjetoVOAntesDaAlteracao();
            setAdquirenteVO(obj);
        } catch (Exception e) {
            montarErro(e);
        }
        return "editar";
    }

    public String gravar() {
        try {
            if (adquirenteVO.isNovoObj().booleanValue()) {
                getFacade().getAdquirente().incluir(adquirenteVO);
                incluirLogInclusao();
            } else {
                getFacade().getAdquirente().alterar(adquirenteVO);
                incluirLogAlteracao();
            }

            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            //limparMsg();
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada(e.getMessage());
            return "editar";
        }
    }

    /**
     * Inclui o log de inclusão de categoria
     * @throws Exception
     */
    public void incluirLogInclusao() throws Exception {
        try {
            adquirenteVO.setObjetoVOAntesAlteracao(new AdquirenteVO());
            adquirenteVO.setNovoObj(true);
            registrarLogObjetoVO(adquirenteVO, adquirenteVO.getCodigo(), "ADQUIRENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("ADQUIRENTE", adquirenteVO.getCodigo(),
                    "ERRO AO GERAR LOG DE INCLUSÃO DE ADQUIRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        adquirenteVO.setNovoObj(false);
        adquirenteVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            adquirenteVO.setObjetoVOAntesAlteracao(new AdquirenteVO());
            adquirenteVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(adquirenteVO, adquirenteVO.getCodigo(), "ADQUIRENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("ADQUIRENTE", adquirenteVO.getCodigo(),
                    "ERRO AO GERAR LOG DE EXCLUSÃO DE ADQUIRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de país
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(adquirenteVO, adquirenteVO.getCodigo(), "ADQUIRENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("ADQUIRENTE", adquirenteVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE ADQUIRENTE",
                    this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        adquirenteVO.registrarObjetoVOAntesDaAlteracao();
    }


    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP PaisCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getPais().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    AdquirenteVO adquirente = getFacade().getAdquirente().consultarPorCodigo(new Integer(valorInt));
                    if(adquirente != null) {
                        objs.add(adquirente);
                    }
                }
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            montarErro(e);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>PaisVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getAdquirente().excluir(adquirenteVO);
            incluirLogExclusao();
            setAdquirenteVO(new AdquirenteVO());

            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"adquirente\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"adquirente\" violates foreign key")){
                setMensagemDetalhada("Este adquirente não pode ser excluída, pois já foi utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("nome", "Nome"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        limparMsg();
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }


    public void montarListaSelectItemTipoConvenio() {
        setListaSelectItemTipoConvenio(JSFUtilities.getSelectItemListFromEnum(TipoConvenioCobrancaEnum.class, "descricao", false));
    }


    /**
     * Consulta de logs de país
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("País");
        loginControle.consultarLogObjetoSelecionado("ADQUIRENTE", adquirenteVO.getCodigo(), null);
    }

    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        adquirenteVO = new AdquirenteVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getAdquirente().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(),getSituacaoFiltro());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public AdquirenteVO getAdquirenteVO() {
        return adquirenteVO;
    }

    public void setAdquirenteVO(AdquirenteVO adquirenteVO) {
        this.adquirenteVO = adquirenteVO;
    }

    public List<SelectItem> getListaSelectItemTipoConvenio() {
        return listaSelectItemTipoConvenio;
    }

    public void setListaSelectItemTipoConvenio(List<SelectItem> listaSelectItemTipoConvenio) {
        this.listaSelectItemTipoConvenio = listaSelectItemTipoConvenio;
    }

    public List<SelectItem> getListaSelectItemSituacao() throws Exception {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("AT", "Ativo"));
        objs.add(new SelectItem("NA", "Inativo"));
        objs.add(new SelectItem("TD", "Todos"));
        return objs;
    }

    public String getSituacaoFiltro() {
        if (situacaoFiltro == null) {
            situacaoFiltro ="AT";
        }
        return situacaoFiltro;
    }

    public void setSituacaoFiltro(String situacaoFiltro) {
        this.situacaoFiltro = situacaoFiltro;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Adquirente",
                "Deseja excluir a Adquirente?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

    public List<SelectItem> getListaAdquirenteGeoitd() {
        return listaAdquirenteGeoitd;
    }

    public void setListaAdquirenteGeoitd(List<SelectItem> listaAdquirenteGeoitd) {
        this.listaAdquirenteGeoitd = listaAdquirenteGeoitd;
    }

    public List carregarAdquirenteGeoitd(){
        setListaAdquirenteGeoitd(JSFUtilities.getSelectItemListFromEnum(AcquirerGeoitd.class, "desc", false));
        return listaAdquirenteGeoitd;
    }

    public Boolean isUruguay() throws Exception {
        if(getConfiguracaoSistema().isUsarSistemaInternacional() && getEmpresaLogado().getPais().getNome().equals(IdentificadorInternacionalEnum.URUGUAY.getNomePais())) {
     //       carregarAdquirenteGeoitd();
            return true;
        }
        return false;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }
}

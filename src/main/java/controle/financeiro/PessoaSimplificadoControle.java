/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.FornecedorVO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import controle.arquitetura.SuperControle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Colaborador;

/**
 *
 * <AUTHOR>
 */
public class PessoaSimplificadoControle extends SuperControle {

    private boolean mostrarCampoEmpresa = false;
    private PessoaVO pessoaVO;
    private EmpresaVO empresaVO;
    private FornecedorVO fornecedorVO;
    private TelefoneVO telefoneVO;
    private EnderecoVO enderecoVO;
    private CepVO cepVO;
    private ClienteVO clienteVO;
    private List<SelectItem> listaSelectItemCidade;
    private List<SelectItem> listaSelectItemPais = new ArrayList<SelectItem>();
    private boolean usarAutosizedNoModal;

    public PessoaSimplificadoControle() throws Exception {
        obterUsuarioLogado();
        //inicializa os dados 
        novo();
        setMensagemID("msg_entre_dados");
        setSucesso(false);
        setErro(false);
    }

    public void novo() throws Exception {
        validarPermissaoEmpresa();
        //para setar a pessoa é necessário da pessoa
        //de outro controlador passando a pessoa
        if (pessoaVO == null) {
            setPessoaVO(new PessoaVO());
        }
        if (empresaVO == null) {
            setEmpresaVO(new EmpresaVO());
        }
        if (!isMostrarCampoEmpresa()) {
            setEmpresaVO(getEmpresaLogado());
        }
        setFornecedorVO(new FornecedorVO());
        getFornecedorVO().setCodigo(0);
        setTelefoneVO(new TelefoneVO());
        setEnderecoVO(new EnderecoVO());
        //por padrão quando o lançamento for recebimento setar o  Cliente como tipo de pessoa
        // e quando for pagamento setar o Fornecedor como tipo de pessoa
        MovContaControle control = (MovContaControle) JSFUtilities.getFromSession(MovContaControle.class.getSimpleName());
        if(control == null){
            control = new MovContaControle();
        }
        if (control.getMovContaVO() != null && control.getMovContaVO().getTipoOperacaoLancamento() != null) {
            if (control.getMovContaVO().getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.RECEBIMENTO)) {
                getPessoaVO().setTipoPessoa("CL");
            } else if (control.getMovContaVO().getTipoOperacaoLancamento().equals(TipoOperacaoLancamento.PAGAMENTO)) {
                getPessoaVO().setTipoPessoa("FO");
            }
        } else {
            getPessoaVO().setTipoPessoa("FO");
        }
        setCepVO(new CepVO());
        setClienteVO(new ClienteVO());
        getPessoaVO().setPais(new PaisVO());
        setListaSelectItemCidade(new ArrayList<SelectItem>());
    }

    /**
     * Monta a lista de empresa da comboBox
     * @return
     */
    public List<SelectItem> getListaSelectItemEmpresa() {
        List objs = new ArrayList();
        try {
            objs.add(new SelectItem(new Integer(0), ""));
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
        return objs;
    }

    /**
     * Monta a lista de tipo de pessoa do SelectOneRadio
     * @return
     */
    public List<SelectItem> getListaSelectItemTipoPessoa() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("FO", "Fornecedor"));
        objs.add(new SelectItem("CL", "Pessoa"));
        return objs;
    }

    /**
     * Método que faz validação do CPF digitado
     * @throws ConsistirException
     * @throws Exception
     */
    public void validarCPF() throws ConsistirException, Exception {
        try {
            if (!pessoaVO.getCfp().isEmpty()) {
                PessoaVO.validarCPF(pessoaVO, getEmpresaVO().getNome());
            }
            setMensagemDetalhada("msg_entre_dados", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Método que faz validação do CNPJ digitado
     * @throws ConsistirException
     * @throws Exception
     */
    public void validarCNPJ() throws ConsistirException, Exception {
        try {
            if (!fornecedorVO.getCnpj().isEmpty()) {
                FornecedorVO.validarCNPJ(fornecedorVO, getEmpresaVO().getNome());
            }
            setMensagemDetalhada("msg_entre_dados", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    /*
     * Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>sexo</code>
     */

    public List getListaSelectItemSexoPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable sexos = (Hashtable) Dominios.getSexo();
        Enumeration keys = sexos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) sexos.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public List getListaSelectItemGeneroPessoa() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable generos = (Hashtable) Dominios.getGenero();
        Enumeration keys = generos.keys();

        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) generos.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);

        return objs;
    }


    /*
     * Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>UF</code>
     */
    public List getListaSelectItemEstado() throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem("", ""));
        Hashtable estados = (Hashtable) Dominios.getEstado();
        Enumeration keys = estados.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) estados.get(value);
            objs.add(new SelectItem(value, label));
        }

        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    /**
     * Verifica permissão para mostrar empresa para o usuário
     */
    public void validarPermissaoEmpresa() {
        if (validarPermissaoLancarVisualizarLancamentosParaTodasAsEmpresas()) {
            setMostrarCampoEmpresa(true);
        } else {
            setMostrarCampoEmpresa(false);
        }
    }

    public void gravarCidade() {
        try {
            getPessoaVO().getCidade().setPais(getPessoaVO().getPais());
            getPessoaVO().getCidade().setEstado(getFacade().getEstado().consultarPorSiglaUf(cepVO.getUfSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getFacade().getCidade().incluir(pessoaVO.getCidade());
            montarListaSelectItemCidade();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Monta lista de SelectItem de Cidade
     * @throws Exception
     */
    public void montarListaSelectItemCidade() throws Exception {
        getPessoaVO().setEstadoVO(getFacade().getEstado().consultarPorSiglaUf(this.cepVO.getUfSigla(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        getPessoaVO().getCidade().setEstado(this.getPessoaVO().getEstadoVO());
        if (getPessoaVO().getEstadoVO().getCodigo() == null) {
            getPessoaVO().getEstadoVO().setCodigo(0);
        }
        List resultadoConsulta = getFacade().getCidade().consultarPorCodigoEstado(this.getPessoaVO().getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            CidadeVO obj = (CidadeVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemCidade(objs);
    }

    /**
     * Grava os dados de pessoa, de cliente ou de fornecedor
     * @throws Exception
     */
    public void gravar() {
        try {
            preparaDadosPessoaParaGravar();
            validarDadosSimplificado(pessoaVO, empresaVO);
            if (getPessoaVO().getTipoPessoa().equals("CL")) {
                if (getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor() &&
                        UteisValidacao.emptyString(pessoaVO.getCfp())) {
                    throw new ConsistirException("O campo CPF deve ser informado!");
                }
                if(getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isCnpjObrigatorioFornecedor() && !(Uteis.formatarCpfCnpj(pessoaVO.getCfp(), true).length() == 11)) {
                    throw new ConsistirException("Quantidade de digitos para o campo CPF deve ser 11.");
                }
                this.gravarPessoa();
            }
            if (getPessoaVO().getTipoPessoa().equals("FO")) {
                this.gravarFornecedor();
            }
            setMensagemID("msg_dados_gravados");
            setMsgAlert("Richfaces.hideModalPanel('modalPanelCadastrarPessoaSimplificada');");
            // fechando completamente o modalPanel de pessoa
            MovContaControle movContaControle = (MovContaControle) context().getExternalContext().getSessionMap().get(MovContaControle.class.getSimpleName());
            if (movContaControle == null) {
                movContaControle = new MovContaControle();
                JSFUtilities.storeOnSession(MovContaControle.class.getSimpleName(), movContaControle);
            }
            movContaControle.setAbrirRichModalCadastroNovaPessoa(false);
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            setMsgAlert("Richfaces.showModalPanel('modalPanelCadastrarPessoaSimplificada');");
        }
    }

    /**
     * Prepara os atributos para gravar os dados 
     */
    public void preparaDadosPessoaParaGravar() {
        if (!enderecoVO.getEndereco().isEmpty()) {
            List<EnderecoVO> listaEnderecoVOs = new ArrayList<EnderecoVO>();
            enderecoVO.setTipoEndereco("NE");
            listaEnderecoVOs.add(enderecoVO);
            getPessoaVO().setEnderecoVOs(listaEnderecoVOs);
        }
        if (!telefoneVO.getNumero().isEmpty()) {
            List<TelefoneVO> listaTelefoneVOs = new ArrayList<TelefoneVO>();
            telefoneVO.setTipoTelefone("CO");
            listaTelefoneVOs.add(telefoneVO);
            getPessoaVO().setTelefoneVOs(listaTelefoneVOs);

        }
        if (!getPessoaVO().getEmail().isEmpty()) {
            List<EmailVO> listaEmailVOs = new ArrayList<EmailVO>();
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(getPessoaVO().getEmail());
            emailVO.setEmailCorrespondencia(false);
            listaEmailVOs.add(emailVO);
            getPessoaVO().setEmailVOs(listaEmailVOs);
        }
    }

    /**
     * Grava os dados de tipo de Pessoa CLIENTE
     * @throws Exception
     */
    public void gravarPessoa() throws Exception {
        //incluir novo cliente
    	getFacade().getPessoa().incluirPessoaSimplificado(pessoaVO);
    }

    public void alterarPessoa() throws Exception {
        getFacade().getPessoa().alterar(pessoaVO);
    }
    /**
     * Grava os dados do tipo de pessoa FORNECEDOR
     * @throws Exception
     */
    public void gravarFornecedor() throws Exception {
        //incluir novo fornecedor
        getFornecedorVO().setEmpresaVO(empresaVO);
        fornecedorVO.setDescricao(pessoaVO.getNome());
        fornecedorVO.validarDados(fornecedorVO);
        getFacade().getPessoa().incluirPessoaSimplificado(pessoaVO);
        getFornecedorVO().setPessoa(pessoaVO);
        getFacade().getFornecedor().incluir(fornecedorVO);
    }

    /**
     * Valida os dados de pessoa 
     * @param pessoaVO
     * @param empresaVO
     * @throws ConsistirException
     */
    public void validarDadosSimplificado(PessoaVO pessoaVO, EmpresaVO empresaVO) throws ConsistirException, Exception {
        if (pessoaVO.getTipoPessoa() == null || pessoaVO.getTipoPessoa().isEmpty()) {
            throw new ConsistirException("Informe o TIPO de pessoa");
        }
        if (empresaVO == null || empresaVO.getCodigo() == 0) {
            throw new ConsistirException("Informe a EMPRESA");
        }
        if (pessoaVO.getNome() == null || pessoaVO.getNome().isEmpty()) {
            throw new ConsistirException("Informe o NOME da pessoa");
        }
        validarCPF();
        validarCNPJ();
        
    }

    /**
     * Ao clicar em cancelar na tela de cadastro de pessoa limpa o campo de nome de pessoa
     * @throws Exception
     */
    public void cancelar() throws Exception {
        MovContaControle movContaControle = (MovContaControle) context().getExternalContext().getSessionMap().get(MovContaControle.class.getSimpleName());
        if (movContaControle == null) {
            movContaControle = new MovContaControle();
            JSFUtilities.storeOnSession(MovContaControle.class.getSimpleName(), movContaControle);
        } else {
            movContaControle.getMovContaVO().setPessoaVO(new PessoaVO());
        }
        movContaControle.setAbrirRichModalCadastroNovaPessoa(false);
    }

    /**
     * Valida a permissão do usuário logado para lançar e visualizar lancamentos para todas as empresas
     * que usa a permissão "Financeiro - 9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas"
     * @throws Exception
     */
    public boolean validarPermissaoLancarVisualizarLancamentosParaTodasAsEmpresas() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setSucesso(true);
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "LancarVisualizarLancamentosEmpresas", "9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas");
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * @return the mostrarCampoEmpresa
     */
    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    /**
     * @param mostrarCampoEmpresa the mostrarCampoEmpresa to set
     */
    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    /**
     * @return the pessoaVO
     */
    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    /**
     * @param pessoaVO the pessoaVO to set
     */
    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    /**
     * @return the empresaVO
     */
    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    /**
     * @param empresaVO the empresaVO to set
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return the fornecedorVO
     */
    public FornecedorVO getFornecedorVO() {
        return fornecedorVO;
    }

    /**
     * @param fornecedorVO the fornecedorVO to set
     */
    public void setFornecedorVO(FornecedorVO fornecedorVO) {
        this.fornecedorVO = fornecedorVO;
    }

    /**
     * @return the telefoneVO
     */
    public TelefoneVO getTelefoneVO() {
        return telefoneVO;
    }

    /**
     * @param telefoneVO the telefoneVO to set
     */
    public void setTelefoneVO(TelefoneVO telefoneVO) {
        this.telefoneVO = telefoneVO;
    }

    /**
     * @return the enderecoVO
     */
    public EnderecoVO getEnderecoVO() {
        return enderecoVO;
    }

    /**
     * @param enderecoVO the enderecoVO to set
     */
    public void setEnderecoVO(EnderecoVO enderecoVO) {
        this.enderecoVO = enderecoVO;
    }

    /**
     * @return the cepVO
     */
    public CepVO getCepVO() {
        return cepVO;
    }

    /**
     * @param cepVO the cepVO to set
     */
    public void setCepVO(CepVO cepVO) {
        this.cepVO = cepVO;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the listaSelectItemCidade
     */
    public List<SelectItem> getListaSelectItemCidade() {
        return listaSelectItemCidade;
    }

    /**
     * @param listaSelectItemCidade the listaSelectItemCidade to set
     */
    public void setListaSelectItemCidade(List<SelectItem> listaSelectItemCidade) {
        this.listaSelectItemCidade = listaSelectItemCidade;
    }

	public void setListaSelectItemPais(List<SelectItem> listaSelectItemPais) {
		this.listaSelectItemPais = listaSelectItemPais;
	}

	public List<SelectItem> getListaSelectItemPais() throws Exception {
		if(listaSelectItemPais.isEmpty()){
			List<PaisVO> consultarPorNome = getFacade().getPais().consultarPorNome("",false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
			for(PaisVO pais : consultarPorNome){
				listaSelectItemPais.add(new SelectItem(pais.getCodigo(), pais.getNome()));
			}
		}
		return listaSelectItemPais;
	}

    public String inicializarConsultar() {
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public String novoPessoaSimplificado() {
        try {
            validarPermissaoEmpresa();
            setPessoaVO(new PessoaVO());
            if (empresaVO == null) {
                setEmpresaVO(new EmpresaVO());
            }
            empresaVO.setCodigo(getEmpresaLogado().getCodigo().intValue());
            getPessoaVO().setNovoObj(true);
            setTelefoneVO(new TelefoneVO());
            setEnderecoVO(new EnderecoVO());
            getPessoaVO().setTipoPessoa("CL");
            setCepVO(new CepVO());
            setClienteVO(new ClienteVO());
            getPessoaVO().setPais(new PaisVO());
            setListaSelectItemCidade(new ArrayList<SelectItem>());
        } catch (ConsistirException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
        return "editar";
    }

    public void gravarPessoaSimplificada() {
        try {
            preparaDadosPessoaParaGravar();
            validarDadosSimplificado(pessoaVO, empresaVO);
            if (getPessoaVO().getNovoObj()) {
                this.gravarPessoa();
            } else {
                this.alterarPessoa();
            }
            setMensagem("");
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            setMsgAlert("");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagem(e.getMessage());
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }

    }

    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        PessoaVO obj = getFacade().getPessoa().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getPessoaVO().setNovoObj(false);
        empresaVO.setCodigo(getEmpresaLogado().getCodigo().intValue());
        setPessoaVO(obj);
        getPessoaVO().setTipoPessoa("CL");
        if (obj.getEnderecoVOs().size() > 0) {
            setEnderecoVO((EnderecoVO) obj.getEnderecoVOs().get(0));
        }
        if (obj.getEmailVOs().size() > 0) {
            getPessoaVO().setEmail(((EmailVO) obj.getEmailVOs().get(0)).getEmail());
        }
        if (obj.getTelefoneVOs().size() > 0) {
            setTelefoneVO(obj.getTelefoneVOs().get(0));
        }
        getListaSelectItemEstado();
        getListaSelectItemCidade();

        setMensagemID("msg_dados_editar");
        return "editar";
    }

    public void inicializarAtributosRelacionados(PessoaVO obj) {
        if (obj.getCidade() == null) {
            obj.setCidade(new CidadeVO());
        }
        if (obj.getPais() == null) {
            obj.setPais(new PaisVO());
        }
    }

    public void abrirCadastroSimplificado() {
        try {
            setMsgAlert("abrirPopup('../../../faces/pessoaCons.jsp','Pessoa', 800, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public String excluir() {
        try {
            getFacade().getPessoa().excluir(pessoaVO);
            setPessoaVO( new PessoaVO());

            setTelefoneVO(new TelefoneVO());

            setEnderecoVO(new EnderecoVO());
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");

        control.init("Exclusão de Pessoa",
                "Deseja excluir a Pessoa?",
                this, "excluir", "", "", "", "grupoBtnExcluir,mensagens");

    }

    public boolean isUsarAutosizedNoModal() {
        return usarAutosizedNoModal;
    }

    public void setUsarAutosizedNoModal(boolean usarAutosizedNoModal) {
        this.usarAutosizedNoModal = usarAutosizedNoModal;
    }
}

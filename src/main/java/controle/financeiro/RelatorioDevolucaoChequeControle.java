package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.RelatorioDevolucaoChequeTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Created by ulisses on 16/11/2017.
 */
public class RelatorioDevolucaoChequeControle extends SuperControle {
    private Date inicio = Calendario.hoje();
    private Date fim = Calendario.hoje();
    private boolean mostrarCampoEmpresa = false;
    private EmpresaVO empresaVO = new EmpresaVO();
    private boolean mostrarRelatorio = false;
    private List<RelatorioDevolucaoChequeTO> listaRelatorio;
    private double totalRelatorio = 0;
    private List<SelectItem> listaSelectItemEmpresa;
    private PessoaVO pessoaVO = new PessoaVO();
    private String numeroCheque;
    private String tipoConsulta;
    private String agencia = "";
    private String conta = "";
    private Integer banco = 0;
    private List<SelectItem> listaSelectItemBanco;
    private List<SelectItem> listaSelectTipos;
    private boolean consultarSomenteChequesNaoRecebidos = false;
    private String nomeNoCheque = "";


    public void abrirTelaRelatorioDevolucaoCheque() {
        try {
            limparFiltros();
            inicializarEmpresa();
            montarListaBanco();
            montarListaTipos();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/pages/finan/relatorioDevolucaoCheque.jsp?modulo=financeiroWeb', 'relatorioDevolucaoCheque', 880, 595);");
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMsgAlert(getMensagemDetalhada());
        }
    }

    public void limparFiltros(){
        inicio = Calendario.hoje();
        fim = Calendario.hoje();
        mostrarRelatorio =false;
        listaRelatorio = new ArrayList<RelatorioDevolucaoChequeTO>();
        totalRelatorio = 0;
        pessoaVO = new PessoaVO();
        numeroCheque = "";
        agencia = "";
        conta = "";
        banco = 0;
        this.consultarSomenteChequesNaoRecebidos = false;
    }

    private void montarListaBanco()throws Exception{
        this.listaSelectItemBanco = new ArrayList<SelectItem>();
        this.listaSelectItemBanco.add(new SelectItem("0", ""));
        List<BancoVO> lista = getFacade().getBanco().consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (BancoVO bancoVO: lista){
            this.listaSelectItemBanco.add(new SelectItem(bancoVO.getCodigo(), bancoVO.getNome()));
        }
    }

    private void montarListaTipos()throws Exception{
        this.listaSelectTipos = new ArrayList<SelectItem>();
        this.listaSelectTipos.add(new SelectItem("", "Todos"));
        this.listaSelectTipos.add(new SelectItem("TROCA", "Apenas trocados"));
        this.listaSelectTipos.add(new SelectItem("DEVOLVIDOS", "Apenas devolvidos pelo banco"));

    }

    public void inicializarEmpresa() throws Exception {
        mostrarCampoEmpresa = validarPermissaoEmpresas();
        if (mostrarCampoEmpresa) {
            List<EmpresaVO> listaEmpresa = getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((listaEmpresa != null) && (listaEmpresa.size() == 1)){
                this.empresaVO = listaEmpresa.get(0);
                this.mostrarCampoEmpresa = false;
            }
        } else {
            setEmpresa((EmpresaVO) getEmpresaLogado().getClone(true));
            this.mostrarCampoEmpresa = false;
        }
        if (this.empresaVO == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void exportarRelatorio(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = ((ExportadorListaControle) getControlador(ExportadorListaControle.class));
        StringBuilder filtro = new StringBuilder();
        if ((this.empresaVO.getCodigo() != null) && (this.empresaVO.getCodigo() > 0)){
            EmpresaVO objEmpresa = getFacade().getEmpresa().consultarPorChavePrimaria(this.empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            filtro.append("Empresa:").append(objEmpresa.getNome());
        }
        filtro.append("  Período: ").append(Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy")).append(" até ").append(Uteis.getDataAplicandoFormatacao(fim, "dd/MM/yyyy")).append(" 23:59");
        if (!this.numeroCheque.equals("")){
            filtro.append(" Número do Cheque:").append(this.numeroCheque);
        }if(!this.nomeNoCheque.equals("")){
            filtro.append(" Nome no Cheque:").append(this.nomeNoCheque);
        }
        if (!this.agencia.equals("")){
            filtro.append(" Agência:").append(this.agencia);
        }
        if (!this.conta.equals("")){
            filtro.append(" Conta:").append(this.conta);
        }
        if ((this.banco != null) && (banco > 0)){
            BancoVO bancoVO = getFacade().getBanco().consultarPorChavePrimaria(this.banco, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            filtro.append(" Banco:").append(bancoVO.getNome());
        }
        if (!this.pessoaVO.getNome().equals("")){
            filtro.append(" Cliente:").append(this.pessoaVO.getNome());
        }
        List<RelatorioDevolucaoChequeTO> listaImprimir = new ArrayList<RelatorioDevolucaoChequeTO>();
        listaImprimir.addAll(this.listaRelatorio);
        RelatorioDevolucaoChequeTO totalizador = new RelatorioDevolucaoChequeTO();
        totalizador.setRegistroTotalizador(true);
        totalizador.setValorCheque(this.totalRelatorio);
        listaImprimir.add(totalizador);
        exportadorListaControle.exportar(evt, listaImprimir, filtro.toString(), null);
    }

    public void consultarRelatorio() throws Exception {
        try{
            setMostrarRelatorio(true);
            Integer codigoPessoa = this.pessoaVO.getCodigo();
            if (this.pessoaVO.getNome().trim().equals("")){
                codigoPessoa = null;
            }
            listaRelatorio = getFacade().getCheque().consultarDevolucaoCheque(this.empresaVO.getCodigo(), this.inicio, this.fim,
                    codigoPessoa,this.numeroCheque, this.agencia, this.conta,this.banco, this.consultarSomenteChequesNaoRecebidos, tipoConsulta);
            totalRelatorio = 0;
            for (RelatorioDevolucaoChequeTO obj: listaRelatorio){
                totalRelatorio = totalRelatorio + obj.getValorCheque();
            }
            limparMsg();
        }catch (ValidacaoException ex){
            limparMsg();
            montarAviso(ex.getMessage());
            setMensagemDetalhada(ex.getMessage());
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (mostrarCampoEmpresa || getEmpresaVO().getCodigo() == 0) {
                List<SelectItem> objs = new ArrayList<SelectItem>();
                objs.add(new SelectItem(0, ""));
                List<EmpresaVO> resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                Iterator i = resultadoConsulta.iterator();
                while (i.hasNext()) {
                    EmpresaVO obj = (EmpresaVO) i.next();
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
                setListaSelectItemEmpresa(objs);
            }
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList());
            e.printStackTrace();
        }
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Date getFim() {
        return fim;
    }

    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isMostrarRelatorio() {
        return mostrarRelatorio;
    }

    public void setMostrarRelatorio(boolean mostrarRelatorio) {
        this.mostrarRelatorio = mostrarRelatorio;
    }

    public List<RelatorioDevolucaoChequeTO> getListaRelatorio() {
        return listaRelatorio;
    }

    public void setListaRelatorio(List<RelatorioDevolucaoChequeTO> listaRelatorio) {
        this.listaRelatorio = listaRelatorio;
    }

    public double getTotalRelatorio() {
        return totalRelatorio;
    }

    public void setTotalRelatorio(double totalRelatorio) {
        this.totalRelatorio = totalRelatorio;
    }

    public String getTotalRelatorio_apresentar(){
        return Formatador.formatarValorMonetario(this.totalRelatorio);
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            montarListaSelectItemEmpresa();
        }
        return listaSelectItemEmpresa;
    }

    public List<PessoaVO> executarAutocompleteConsultaPessoa(Object suggest) {
        String pref = (String) suggest;
        List<PessoaVO> result = new ArrayList<PessoaVO>();
        boolean somenteNumero = UteisValidacao.somenteNumeros(pref);
        try {
            //if (UteisValidacao.emptyNumber(this.empresaVO.getCodigo().intValue())) {
            //    throw new Exception("Informe a empresa primeiro");
           // }
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                        consultarClientes(this.empresaVO.getCodigo().intValue(), "", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                        consultarClientes(this.empresaVO.getCodigo().intValue(), pref, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPessoaSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        if (pessoaVO != null) {
            this.pessoaVO = pessoaVO;
        }

    }



    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getNumeroCheque() {
        return numeroCheque;
    }

    public void setNumeroCheque(String numeroCheque) {
        this.numeroCheque = numeroCheque;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public Integer getBanco() {
        return banco;
    }

    public void setBanco(Integer banco) {
        this.banco = banco;
    }

    public List<SelectItem> getListaSelectItemBanco() {
        return listaSelectItemBanco;
    }

    public void setListaSelectItemBanco(List<SelectItem> listaSelectItemBanco) {
        this.listaSelectItemBanco = listaSelectItemBanco;
    }

    public boolean isConsultarSomenteChequesNaoRecebidos() {
        return consultarSomenteChequesNaoRecebidos;
    }

    public void setConsultarSomenteChequesNaoRecebidos(boolean consultarSomenteChequesNaoRecebidos) {
        this.consultarSomenteChequesNaoRecebidos = consultarSomenteChequesNaoRecebidos;
    }

    public String getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(String tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public List<SelectItem> getListaSelectTipos() {
        return listaSelectTipos;
    }

    public void setListaSelectTipos(List<SelectItem> listaSelectTipos) {
        this.listaSelectTipos = listaSelectTipos;
    }

    public String getNomeNoCheque() {
        return nomeNoCheque;
    }

    public void setNomeNoCheque(String nomeNoCheque) {
        this.nomeNoCheque = nomeNoCheque;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoConsultaParcelasEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.encrypt.gpg.PgpEncryption;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.integracao.pactopay.PactoPayService;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.view.DataScrollerControle;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.crm.HistoricoContatoControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.AcoesStatusRemessaVO;
import negocio.comuns.basico.AgrupamentoParcelasTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemGenericoTO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.AcoesRemessasEnum;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.AgrupadorRemessaTO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ItemRetornoRemessaTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.RetornoRemessaVO;
import negocio.comuns.financeiro.TotalizadorRemessaTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.oamd.EmpresaFinanceiroVO;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ItemRemessaTO;
import servicos.impl.boleto.LayoutBoletoPadrao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.bb.LayoutRemessaBBDCO;
import servicos.impl.dcc.bradesco.LayoutRemessaBradescoCNAB240;
import servicos.impl.dcc.cielo.LayoutRemessaCieloDCC;
import servicos.impl.dcc.itau.BBCnab400ItauStatusEnum;
import servicos.impl.dcc.itau.LayoutRemessaItauDCO;
import servicos.impl.dco.febraban.LayoutRemessaFebrabanDCO;
import servicos.impl.oamd.OAMDService;
import servicos.remessa.to.ProcessoRetornoRemessaTO;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.math.BigDecimal;
import java.security.Security;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class GestaoRemessasControle extends SuperControleRelatorio {

    public static final String ABA_REMESSA = "abaRemessa";
    public static final String ABA_CANCELAMENTO = "abaCancelamento";
    private List<RemessaVO> listaRemessas;
    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private Date dataInicioCobranca = null;
    private Date dataFimCobranca = null;
    private RemessaVO remessaVO = new RemessaVO();
    //Repescagem
    private boolean exibirModalParametros = false;
    private boolean exibirRemessa = false;
    private boolean exibirRemessaNovo = false;
    private boolean exibirRemessaBoleto = false;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList<ObjetoGenerico>();
    private boolean exibirControles = true;
    private List<String> listaColunas;
    private RemessaService service;
    private List<SituacaoRemessaEnum> situacoesRemessa = new ArrayList<SituacaoRemessaEnum>();
    private String abaSelecionada = ABA_REMESSA;
    private List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> listaParcelasGeral;
    private List<SelectItem> tiposConsulta = new ArrayList<SelectItem>();
    private List<SelectItem> tiposConsultaStatus;
    private String[] tiposConsultaEscolhido = new String[2];
    private String[] tiposConsultaStatusEscolhido = new String[6];
    private List<SelectItem> convenios = new ArrayList<SelectItem>();
    private ConvenioCobrancaVO convenio = new ConvenioCobrancaVO();
    private Integer qtdItensTotal = 0;
    private Double valorBrutoTotal = 0.0;
    private Double valorAceitoTotal = 0.0;
    private Double valorLiquidoTotal = 0.0;
    private double somaItensRepescagem = 0.0;
    private double somaItensRepescagemSelecao = 0.0;
    private double somaItensEmAberto = 0.0;
    private double somaItensEmAbertoSelecao = 0.0;
    private int qtdItensRepescagem = 0;
    private int qtdItensRepescagemSelecao = 0;
    private int qtdItensEmAberto = 0;
    private int qtdItensEmAbertoSelecao = 0;
    private boolean marcarTodos = false;
    private boolean exibirAutorizacoes = false;
    private boolean exibirModalUpload = false;
    StringBuilder headRetorno = null;
    private Integer escolhaGerarRemessaOuProcessarRetorno = 0;//0 ou 1
    private List<RemessaItemVO> itensRemessaEstorno;
    private List<RemessaItemVO> itensRemessaBoleto;
    private List<SelectItem> acoes;
    private AcoesStatusRemessaVO acao;
    private List<AcoesStatusRemessaVO> listaAcoes;
    private AgrupamentoParcelasTO agrupamentoParcelas;
    private boolean agrupar = true;
    private boolean mostrarParcelasComBoleto = false;
    private MovParcelaVO movParcela;
    private List<TotalizadorRemessaTO> totalizador = new ArrayList<TotalizadorRemessaTO>();
    private boolean marcarTodosTrocarCartao = false;
    private boolean marcarTodosContato = false;
    private boolean marcarTodosOutros = false;
    private boolean marcarTodosReenvio = false;
    private boolean marcarTodosCielo = false;
    private boolean marcarTodosSemAcao = false;
    private String numeroEstabelecimento = "";
    private Date dataRetorno;
    private Date dataEnvio;
    private String numeroCielo;
    private boolean remessasDiferentes = false;
    private int creditoDCC = 0;
    private TipoCobrancaPactoEnum tipoCobrancaPacto;
    private Date dataExpiracaoCreditoDCC = null;
    private List<RemessaItemVO> itensProcessar;
    private List<BoletoVO> itensProcessarBoleto;
    private RemessaVO remessaRetorno;
    private RemessaVO retornoBoleto;
    private String nomeArquivo;
    private PlanoVO planoFiltro = new PlanoVO();
    private boolean apresentarColunaRetornoManual = false;
    private RemessaItemVO itemRetornar = new RemessaItemVO();
    private List<ItemRetornoRemessaTO> registrosNaoEncontrados;
    private Map<EmpresaFinanceiroVO, String> mapaItemEmpresa;

    //Campos informativos do Retorno
    private Date dataPrevistaCredito;
    private int quantidadeDeItens = 0;
    private Double valorTotalDoArquivo = 0.0;
    private int quantidadeItensReconhecidos = 0;
    private Double valorTotalASerBaixado = 0.0;
    private int quantidadeItensBaixaEmDuplicidade = 0;
    private int quantidadeItensBaixaValorMenor = 0;
    private int quantidadeItensBaixaValorMaior = 0;
    private int quantidadeItensBaixaNormal = 0;
    private int quantidadeItensBaixaManual = 0;
    private int quantidadeItensNaoReconhecido = 0;
    private int quantidadeItensRegistrados = 0;
    private Double valorTotalNaoReconhecido = 0.0;

    private int quantidadeAutorizacoes = 0;
    private int quantidadeConfirmacoes = 0;

    private RetornoRemessaVO retornoRemessaVO;
    private boolean apresentarItensRetornoBoleto = false;
    private boolean apresentarBotaoProcessar = false;
    private Integer nrPaginasItensRemessaEstorno = 10;
    private Integer nrPaginasItensRemessaBoleto = 10;
    private boolean baixarRemessa = true; //para validar se será feito o dowload da remessa ou da retorno apos validacao da senha
    private boolean envioAutomatico = true;
    private boolean exigirValidacao = true;
    private String senhaDownloadRemessa = "";
    private String criptogrado = "";
    private boolean impressao = false;
    private String onCompleteDetalhes = "";
    //Cancelamento
    private List<RemessaCancelamentoItemVO> itensAutorizados;
    private List<RemessaCancelamentoItemVO> remessaCancelamentoItensApresentar;

    //Agrupamento Boleto
    private boolean agruparRemessas = false;
    private List<AgrupadorRemessaTO> agrupamentoRemessas;
    private String identificadorEmpresaFinaceiro = "";
    private EmpresaFinanceiroVO empresaFinaceiro = new EmpresaFinanceiroVO();
    private List<EmpresaFinanceiroVO> empresaFinanceiroVOs = new ArrayList<EmpresaFinanceiroVO>();
    List<GenericoTO> itensOutrasEmpresa = new ArrayList<GenericoTO>();

    private List<String> listaDescricaoParcelasAutorizadas;
    private String filtroParcelaAutorizada;
    private List<RemessaCancelamentoItemVO> itensAutorizadosFiltrada;
    private ClienteVO clienteVOFiltrar;
    private List<MovParcelaVO> listaRemessaComErro;

    private List<SelectItem> listaModalidades;
    private ModalidadeVO modalidade;
    private List<SelectItem> listaSelectItemEmpresa;
    private String empresaSelecionada;
    private EmpresaVO empresaVO;
    private boolean buscarTodasEmpresas = false;
    private List<ItemGenericoTO> listaStatus;
    private boolean consultarInfoTodasEmpresas = false;
    private List<ConvenioCobrancaVO> listaConveniosCompleta;
    private String codigoGenericoConvenio;
    private ProcessoRetornoRemessaTO processoRetornoRemessaTO;
    private List<MovParcelaTransacaoTO> movParcelaTransacaoTO;
    private boolean perfilAcessoBotaoErro99;
    private String codAutorizacao;
    private Integer codigoItem;
    private Date dataCompensacao = Calendario.hoje();
    private String msgErro;
    private Integer qtdItemDebito = 0;
    private boolean filtroRemessasCancelamento = false;
    private boolean apresentarAlunosBloqueioCobranca = true;
    private boolean inicializarEmpresa = true;
    private boolean apresentarConvenioInativo = false;
    private String onCompleteBoleto;
    private List<String> errosGeracao;
    private boolean sucessoReciboItemRemessa;

    public GestaoRemessasControle() throws Exception {
        situacoesRemessa = JSFUtilities.getListFromEnum(SituacaoRemessaEnum.class);
        if (!situacoesRemessa.isEmpty()) {
            situacoesRemessa.remove(0);
        }
        tiposConsulta = JSFUtilities.getSelectItemListFromEnum(TipoConsultaParcelasEnum.class, "descricao", false);
        tiposConsultaStatus = JSFUtilities.getSelectItemListFromEnum(SituacaoRemessaEnum.class, "descricao", false);
        tiposConsultaStatus.remove(6);
        tiposConsultaStatus.remove(5);
        tiposConsultaStatus.remove(0);

        obterEmpresa();

        montarAcoes();
        numeroCielo = getFacade().getConfiguracaoSistema().obterNumeroCielo();
        envioAutomatico = getFacade().getConfiguracaoSistema().isEnviarRemessasRemotamente();
        obterIdentificadorEmpresaFinanceiro(false);

        validaPerfilAcessoBotaoErro99();
    }

    public void inicializarEmpresa() throws Exception {
        montarListaSelectItemEmpresa();
        try {
            //selecionar a empresa logada
            setEmpresaVO(getEmpresaLogado());
            setEmpresaSelecionada(getEmpresaLogado().getCodigo().toString());
            selecionouEmpresaCarregarConvenios();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        setInicializarEmpresa(false);
    }

    public void montarAcoes() {
        acoes = new ArrayList<SelectItem>();
        acoes.add(new SelectItem(null, ""));
        for (AcoesRemessasEnum acaoEnum : AcoesRemessasEnum.values()) {
            acoes.add(new SelectItem(acaoEnum.getCodigo(), getMensagemInternalizacao(acaoEnum.name())));
        }
    }

    private void montarListaModalidades() {
        try {
            listaModalidades = new ArrayList<SelectItem>();
            if (getConvenio().isBoleto()) {
                List<ModalidadeVO> modalidades = getFacade().getModalidade().consultarTodasModalidadesComLimite(getConvenio().getEmpresa().getCodigo(), true, null);
                listaModalidades.add(new SelectItem("", ""));
                for (ModalidadeVO mod : modalidades) {
                    listaModalidades.add(new SelectItem(mod.getCodigo(), mod.getNome()));
                }
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }

    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Ações Status Remessa");

        Integer codConsultar = 0;
        if (acao != null && !acao.getCodigoStatus().isEmpty()) {
            try {
                codConsultar = Integer.parseInt(acao.getCodigoStatus());
            } catch (Exception ex) {
                codConsultar = 0;
                Uteis.logar(ex, this.getClass());
            }
        }

        loginControle.consultarLogObjetoSelecionado("AcoesStatusRemessa".toUpperCase(), 0, codConsultar);
    }

    public void abrirConfigAcao() {
        acao = new AcoesStatusRemessaVO();
        try {
            listaAcoes = getFacade().getAcoesStatus().consultarPorTodos();
            Ordenacao.ordenarLista(listaAcoes, "codigoStatus_Ordenacao");
        } catch (Exception e) {
            listaAcoes = new ArrayList<AcoesStatusRemessaVO>();
        }
    }

    public void adicionarAcao() {
        try {
            setMsgAlert("");
            if (acao.getCodigoStatus() == null
                    || acao.getAcao() == null
                    || acao.getAcao().getCodigo() == null) {
                throw new Exception(getMensagemInternalizacao("msg_preencha_todos_dados_acao"));
            }
            getFacade().getAcoesStatus().salvar(acao);
            AcoesStatusRemessaVO antes = (AcoesStatusRemessaVO) acao.getObjetoVOAntesAlteracao();
            if (antes == null) {
                acao.setObjetoVOAntesAlteracao(new AcoesStatusRemessaVO());
                antes = new AcoesStatusRemessaVO();
            }
            antes.setCodigoStatus("");
            registrarLogObjetoVO(acao, Integer.parseInt(acao.getCodigoStatus()));
            abrirConfigAcao();
            montarMsgAlert(getMensagemInternalizacao("msg_dados_gravados"));
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("");
        }
    }

    public void editar() {
        try {
            acao = (AcoesStatusRemessaVO) context().getExternalContext().getRequestMap().get("acaoLinha");
            acao.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("", "");
        }
    }

    public void excluir() {
        try {
            setMsgAlert("");
            AcoesStatusRemessaVO acaoLinha = (AcoesStatusRemessaVO) context().getExternalContext().getRequestMap().get("acaoLinha");
            getFacade().getAcoesStatus().excluir(acaoLinha);
            abrirConfigAcao();
            montarMsgAlert(getMensagemInternalizacao("msg_dados_excluidos"));
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("");
        }
    }

    public boolean isGerarRemessa() {
        return escolhaGerarRemessaOuProcessarRetorno == 0;
    }

    public boolean isProcessarRetorno() {
        return escolhaGerarRemessaOuProcessarRetorno == 1;
    }

    public RemessaVO getRemessaVO() {
        return remessaVO;
    }

    public void setRemessaVO(RemessaVO remessaVO) {
        this.remessaVO = remessaVO;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public List<RemessaVO> getListaRemessas() {
        if (listaRemessas == null) {
            listaRemessas = new ArrayList<RemessaVO>();
        }
        return listaRemessas;
    }

    public void setListaRemessas(List<RemessaVO> listaRemessas) {
        this.listaRemessas = listaRemessas;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public boolean isExibirRemessa() {
        return exibirRemessa;
    }

    public void setExibirRemessa(boolean exibirTransacao) {
        this.exibirRemessa = exibirTransacao;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public List<String> getListaColunas() {
        if (listaColunas == null) {
            listaColunas = new ArrayList<>();
        }
        return listaColunas;
    }

    public void setListaColunas(List<String> listaColunas) {
        this.listaColunas = listaColunas;
    }

    public List<SituacaoRemessaEnum> getSituacoesRemessa() {
        return situacoesRemessa;
    }

    public void setSituacoesRemessa(List<SituacaoRemessaEnum> situacoesRemessa) {
        this.situacoesRemessa = situacoesRemessa;
    }

    public String getAbaSelecionada() {
        return abaSelecionada;
    }

    public void setAbaSelecionada(String abaSelecionada) {
        this.abaSelecionada = abaSelecionada;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public List<SelectItem> getTiposConsulta() {
        return tiposConsulta;
    }

    public void setTiposConsulta(List<SelectItem> tiposConsulta) {
        this.tiposConsulta = tiposConsulta;
    }

    public String[] getTiposConsultaEscolhido() {
        return tiposConsultaEscolhido;
    }

    public void setTiposConsultaEscolhido(String[] tiposConsultaEscolhido) {
        this.tiposConsultaEscolhido = tiposConsultaEscolhido;
    }

    public List<SelectItem> getTiposConsultaStatus() {
        return tiposConsultaStatus;
    }

    public void setTiposConsultaStatus(List<SelectItem> tiposConsultaStatus) {
        this.tiposConsultaStatus = tiposConsultaStatus;
    }

    public String[] getTiposConsultaStatusEscolhido() {
        return tiposConsultaStatusEscolhido;
    }

    public void setTiposConsultaStatusEscolhido(String[] tiposConsultaStatusEscolhido) {
        this.tiposConsultaStatusEscolhido = tiposConsultaStatusEscolhido;
    }

    public ConvenioCobrancaVO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public List<SelectItem> getConvenios() {
        if (convenios == null) {
            convenios = new ArrayList<>();
        }
        return convenios;
    }

    public void setConvenios(List<SelectItem> convenios) {
        this.convenios = convenios;
    }

    public int getQtdItensEmAberto() {
        return qtdItensEmAberto;
    }

    public void setQtdItensEmAberto(int qtdItensEmAberto) {
        this.qtdItensEmAberto = qtdItensEmAberto;
    }

    public int getQtdItensRepescagem() {
        return qtdItensRepescagem;
    }

    public void setQtdItensRepescagem(int qtdItensRepescagem) {
        this.qtdItensRepescagem = qtdItensRepescagem;
    }

    public double getSomaItensEmAberto() {
        return somaItensEmAberto;
    }

    public void setSomaItensEmAberto(double somaItensEmAberto) {
        this.somaItensEmAberto = somaItensEmAberto;
    }

    public double getSomaItensRepescagem() {
        return somaItensRepescagem;
    }

    public void setSomaItensRepescagem(double somaItensRepescagem) {
        this.somaItensRepescagem = somaItensRepescagem;
    }

    public boolean isMarcarTodos() {
        return marcarTodos;
    }

    public void setMarcarTodos(boolean marcarTodos) {
        this.marcarTodos = marcarTodos;
    }

    public int getQtdItensEmAbertoSelecao() {
        return qtdItensEmAbertoSelecao;
    }

    public void setQtdItensEmAbertoSelecao(int qtdItensEmAbertoSelecao) {
        this.qtdItensEmAbertoSelecao = qtdItensEmAbertoSelecao;
    }

    public int getQtdItensRepescagemSelecao() {
        return qtdItensRepescagemSelecao;
    }

    public void setQtdItensRepescagemSelecao(int qtdItensRepescagemSelecao) {
        this.qtdItensRepescagemSelecao = qtdItensRepescagemSelecao;
    }

    public double getSomaItensEmAbertoSelecao() {
        return somaItensEmAbertoSelecao;
    }

    public void setSomaItensEmAbertoSelecao(double somaItensEmAbertoSelecao) {
        this.somaItensEmAbertoSelecao = somaItensEmAbertoSelecao;
    }

    public double getSomaItensRepescagemSelecao() {
        return somaItensRepescagemSelecao;
    }

    public void setSomaItensRepescagemSelecao(double somaItensRepescagemSelecao) {
        this.somaItensRepescagemSelecao = somaItensRepescagemSelecao;
    }

    public int getQtdItensTotal() {
        return qtdItensTotal;
    }

    public void setQtdItensTotal(int qtdItensTotal) {
        this.qtdItensTotal = qtdItensTotal;
    }

    public double getValorBrutoTotal() {
        return valorBrutoTotal;
    }

    public void setValorBrutoTotal(double valorBrutoTotal) {
        this.valorBrutoTotal = valorBrutoTotal;
    }

    public double getValorAceitoTotal() {
        return valorAceitoTotal;
    }

    public void setValorAceitoTotal(double valorAceitoTotal) {
        this.valorAceitoTotal = valorAceitoTotal;
    }

    public double getValorLiquidoTotal() {
        return valorLiquidoTotal;
    }

    public void setValorLiquidoTotal(double valorLiquidoTotal) {
        this.valorLiquidoTotal = valorLiquidoTotal;
    }

    public String getSomaTotalSelecao() {
        return Formatador.formatarValorMonetario(somaItensEmAbertoSelecao + somaItensRepescagemSelecao);
    }

    public String getValorBrutoTotal_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorBrutoTotal);
    }

    public String getValorAceitoTotal_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorAceitoTotal);
    }

    public String getValorLiquidoTotal_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valorLiquidoTotal);
    }

    public int getQtdTotalSelecao() {
        return qtdItensEmAbertoSelecao + qtdItensRepescagemSelecao;
    }

    public boolean isExibirAutorizacoes() {
        return exibirAutorizacoes;
    }

    public void setExibirAutorizacoes(boolean exibirAutorizacoes) {
        this.exibirAutorizacoes = exibirAutorizacoes;
    }

    public boolean isExibirModalUpload() {
        return exibirModalUpload;
    }

    public void setExibirModalUpload(boolean exibirModalUpload) {
        this.exibirModalUpload = exibirModalUpload;
    }

    public Integer getEscolhaGerarRemessaOuProcessarRetorno() {
        return escolhaGerarRemessaOuProcessarRetorno;
    }

    public void setEscolhaGerarRemessaOuProcessarRetorno(Integer escolhaGerarRemessaOuProcessarRetorno) {
        this.escolhaGerarRemessaOuProcessarRetorno = escolhaGerarRemessaOuProcessarRetorno;
    }

    public void selecionarItemCancelado() {
        try {
            RemessaItemVO itemCancelado = (RemessaItemVO) JSFUtilities.getFromRequest("item");
            if (itemCancelado != null) {
                setRemessaCancelamentoItensApresentar(getFacade().getRemessaCancelamentoItem().consultarPorCodigoRemessaItem(itemCancelado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    private Boolean validaPerfilAcesso() {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            UsuarioVO usuarioLogado = loginControle.getUsuario();

            if (usuarioLogado.getUsuarioPerfilAcessoVOs().isEmpty()) {
                return false;
            } else {
                Iterator i = usuarioLogado.getUsuarioPerfilAcessoVOs().iterator();
                while (i.hasNext()) {
                    UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                    if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                        usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                                usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                                usuarioLogado, "ReciboTransacaoErro99Cielo", "Criação de recibo remessa erro 99 (CIELO)");
                    }
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void validaPerfilAcessoBotaoErro99(){
        this.perfilAcessoBotaoErro99 = validaPerfilAcesso();
    }

    public void gerarRecibo() {
        try{
            limparMsg();
            setOnCompleteDetalhes("");
            if (UteisValidacao.emptyString(this.codAutorizacao)){
               throw new Exception("Código de autorização deve ser informado");
            }

            if (this.dataCompensacao == null){
                throw new Exception("Data de compensação deve ser informada");
            }

            getFacade().getRemessaItem().gerarReciboItemRemessa(this.codigoItem, this.codAutorizacao, this.dataCompensacao, this.getUsuarioLogado());
            this.codAutorizacao = null;
            montarSucessoGrowl("Recibo gerado com sucesso!");
            setOnCompleteDetalhes("Richfaces.hideModalPanel('panelGerarRecibo');Richfaces.showModalPanel('modalSucessoRecibo')");;
            setSucessoReciboItemRemessa(true);
        } catch (Exception e){
            montarErro(e);
            setSucessoReciboItemRemessa(false);
        }
    }

    public void redirecionarTelaAluno() throws IOException {
        ExternalContext ec = FacesContext.getCurrentInstance().getExternalContext();
        ec.redirect(((HttpServletRequest) ec.getRequest()).getRequestURI());
    }

    public void remessa(ActionEvent evt) throws Exception {
        this.codAutorizacao = null;
        this.msgErro = null;
        this.codigoItem = (Integer)evt.getComponent().getAttributes().get("codigoItem");
    }

    public void selecionarRemessaListener(ActionEvent evt) {
        RemessaVO remessa = (RemessaVO) evt.getComponent().getAttributes().get("remessa");
        if (remessa != null) {
            try {
                remessaVO = remessa;
                LayoutRemessaBase.preencherArquivoRemessa(remessa);
            } catch (Exception ex) {
                setMensagemDetalhada(ex.getMessage());
            }
        }
    }

    public void exibirParams(ActionEvent evt) {
        String params = (String) evt.getComponent().getAttributes().get("params");
        RemessaVO remessa = (RemessaVO) evt.getComponent().getAttributes().get("remessa");
        remessaVO = null;
        listaColunas = null;
        listaParametrosSelecionado = null;
        if (params != null && remessa != null) {
            remessaVO = remessa;
            setExibirModalParametros(true);

            listaColunas = new ArrayList<String>();
            listaParametrosSelecionado = new ArrayList<ObjetoGenerico>();

            listaColunas.addAll(Uteis.getTituloAtributosFromStringBuffer(remessa.getHead()));
            listaColunas.add("");
            listaColunas.addAll(Uteis.getTituloAtributosFromStringBuffer(remessa.getTrailer()));
            listaColunas.add("Usuario");

            listaParametrosSelecionado.addAll(Uteis.getAtributosFromStringBuffer(remessa.getHead()));
            listaParametrosSelecionado.add(new ObjetoGenerico("", ""));
            listaParametrosSelecionado.addAll(Uteis.getAtributosFromStringBuffer(remessa.getTrailer()));

            ObjetoGenerico obj = new ObjetoGenerico("");

            try {
                UsuarioVO usuarioVO = new UsuarioVO();
                usuarioVO = getFacade().getUsuario().consultarPorChavePrimaria(remessa.getUsuarioFechamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                listaParametrosSelecionado.add(new ObjetoGenerico("FechouRemessa", usuarioVO.getNomeAbreviado()));
            } catch (Exception e) {
                e.printStackTrace();
            }


            if (!UteisValidacao.emptyString(remessaVO.getDescricaoCodigoRetornoRemessa())) {
                for (ObjetoGenerico objetoGenerico : listaParametrosSelecionado) {
                    if (DCCAttEnum.CodigoRetorno.name().equals(objetoGenerico.getAtributo())) {
                        objetoGenerico.setValor(remessaVO.getDescricaoCodigoRetornoRemessa());
                    }
                }
            }
        }
    }

    public void downloadRemessa(ActionEvent evt) {
        try {
            limparMsg();
            if ((getUsuarioLogado().getUsername().equals("admin") || getUsuarioLogado().getUsername().equals("PACTOBR")) && isExigirValidacao()) {
                setRemessaVO((RemessaVO) evt.getComponent().getAttributes().get("remessa"));
                setBaixarRemessa(true);
                setCriptogrado((String) evt.getComponent().getAttributes().get("criptografado"));
                setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmarDownload');");
            } else {
                if (!getUsuarioLogado().getUsername().equals("admin") && !getUsuarioLogado().getUsername().equals("PACTOBR")) {
                    setRemessaVO((RemessaVO) evt.getComponent().getAttributes().get("remessa"));
                    setCriptogrado((String) evt.getComponent().getAttributes().get("criptografado"));
                }
                setMensagemDetalhada("", "");
                if (getRemessaVO() != null) {

                    if (getRemessaVO().getCodigo() != 0) {

                        if (!getRemessaVO().getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO) &&
                                !getRemessaVO().getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) &&
                                !Uteis.isAmbienteDesenvolvimentoTeste()) {
                            throw new Exception("Download indisponível. Entre em contato com a Pacto Soluções.");
                        }

                        setExibirRemessa(false);
                        setExibirRemessaNovo(false);
                        setExibirRemessaBoleto(false);
                        getRemessaVO().setConvenioCobranca(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(getRemessaVO().getConvenioCobranca().getCodigo(), getRemessaVO().getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS));
                        List<RemessaItemVO> lista = new ArrayList<RemessaItemVO>();
                        if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)
                                || getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU)
                                || getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)) {
                            lista = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(getRemessaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);
                        } else {
                            lista = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(getRemessaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        }
                        List<RemessaCancelamentoItemVO> listaCancelamento = getFacade().getZWFacade().getRemessaCancelamentoItem().consultarPorCodigoRemessa(getRemessaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                            for (RemessaItemVO ri : lista) {
                                ri.setMesesAbertos(getFacade().getRemessaItem().consultarMesesEmAberto(ri));
                            }
                            Ordenacao.ordenarLista(lista, "cepSacado");
                        } else if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)
                                || getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                            for (RemessaItemVO ri : lista) {
                                ri.setAutorizacaoCobrancaVO(getFacade().getAutorizacaoCobrancaCliente().consultar(ri.getClienteVO().getCodigo(), TipoAutorizacaoCobrancaEnum.DEBITOCONTA));
                            }
                        }

                        getRemessaVO().setListaItens(lista);
                        getRemessaVO().setListaItensCancelamento(listaCancelamento);
                        if (getRemessaVO().isCancelamento()) {
                            LayoutRemessaBase.preencherArquivoRemessaCancelamento(getRemessaVO());
                        } else {
                            LayoutRemessaBase.preencherArquivoRemessa(getRemessaVO());
                        }
                        StringBuilder sb;
                        String encode = "UTF-8";
                        if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) {
                            sb = LayoutRemessaItauDCO.prepareFile(getRemessaVO());
                        } else if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                            sb = LayoutRemessaBradescoCNAB240.prepareFile(getRemessaVO());
                        } else if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                            obterIdentificadorEmpresaFinanceiro(true);
                            sb = LayoutBoletoPadrao.prepareFile(getRemessaVO(), getIdentificadorEmpresaFinaceiro());
                            encode = LayoutBoletoPadrao.obterEncodeArquivo(getRemessaVO());
                        } else if (getRemessaVO().getConvenioCobranca().isLayoutFebrabanDCO()) {
                            if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BB)) {
                                sb = LayoutRemessaBBDCO.prepareFile(getRemessaVO());
                            } else {
                                sb = LayoutRemessaFebrabanDCO.prepareFile(getRemessaVO());
                            }
                        } else {
                            sb = LayoutRemessaCieloDCC.prepareFile(getRemessaVO());
                        }
                        String path = this.getServletContext().getRealPath("relatorio") + File.separator + getRemessaVO().getNomeArquivoDownload();
                        if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                                && getCriptogrado() != null && getCriptogrado().equals("true")) {
                            PgpEncryption.salvarCriptografado(sb.toString(), path, getRemessaVO().getConvenioCobranca().getChaveGETNET());
                        } else {
                            StringUtilities.saveToFile(sb, path, encode);
                        }
                        //remessaVO = remessa;
                    }
                }
                setMsgAlertAuxiliar("Richfaces.hideModalPanel('modalConfirmarDownload');abrirPopup('UpdateServlet?op=downloadfile&file=" + getRemessaVO().getNomeArquivoDownload() + "&mimetype=txt','Remessa', 640,480);");
                setExigirValidacao(true);
                setRemessaVO(null);
            }
        } catch (Exception ex) {
            Uteis.logar(ex, GestaoRemessasControle.class);
            setExigirValidacao(true);
            setMensagemDetalhada(ex);
        }
    }

    public void exibirItensRemessa(ActionEvent evt) {
        RemessaVO remessa = (RemessaVO) evt.getComponent().getAttributes().get("remessa");
        setMensagemDetalhada("", "");
        remessaVO = null;
        if (remessa != null) {
            try {
                if (remessa.getCodigo() != 0) {
                    definirRemessaExibir(remessa);

                    povoarItensRemessaPorTipo(remessa);
                    remessaVO = remessa;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                setMensagemDetalhada(ex);
            }
        }
    }

    private void definirRemessaExibir(RemessaVO remessa) {
        if (remessa.getTipo().equals(TipoRemessaEnum.BOLETO)
                || remessa.getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)
                || remessa.getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)) {
            setExibirRemessaBoleto(true);
        } else if (remessa.isNovoFormato()) {
            setExibirRemessaNovo(true);
        } else {
            setExibirRemessa(true);
        }
    }

    private void povoarItensRemessaPorTipo(RemessaVO remessa) throws Exception {
        try {
            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);
            CacheControl.toggleCache(Remessa.class, true);

//            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();

            if (!remessa.isCancelamento()) {
                List<RemessaItemVO> lista = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240) || remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                    for (RemessaItemVO ri : lista) {
                        ri.setAutorizacaoCobrancaVO(getFacade().getAutorizacaoCobrancaCliente().consultar(ri.getClienteVO().getCodigo(), TipoAutorizacaoCobrancaEnum.DEBITOCONTA));
                    }
                }
                remessa.setListaItens(lista);
                LayoutRemessaBase.preencherArquivoRemessa(remessa, true);
            } else {
                List<RemessaCancelamentoItemVO> lista = getFacade().getZWFacade().getRemessaCancelamentoItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                remessa.setListaItensCancelamento(lista);
                LayoutRemessaBase.preencherArquivoRemessaCancelamento(remessa, true);
            }

//            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
//            System.out.println("Tempo povoarItensRemessaPorTipo: " + (d2.getTime() - d1.getTime()));
        } finally {
            CacheControl.clear();
        }
    }

    public Integer qtdItensAutorizarDebito(RemessaVO vo) throws Exception {
        vo.setQtdItemDebito(0);
        if (vo.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
            vo.setQtdItemDebito(getFacade().getZWFacade().getRemessaItem().consultarQtdPorCodigoRemessa(vo.getCodigo()));
        }
        return vo.getQtdItemDebito();
    }

    public void imprimirBoletosRemessa(ActionEvent evt) {
        limparMsg();
        setMsgAlert("");
        RemessaVO remessa = (RemessaVO) evt.getComponent().getAttributes().get("remessa");
        if (remessa != null) {
            BoletoBancarioControle boletoBancarioControle = (BoletoBancarioControle) context().getExternalContext().getSessionMap().get("BoletoBancarioControle");
            try {
                if (remessa.getCodigo() != 0) {
                    RemessaVO remessaVO = getFacade().getZWFacade().getRemessa().consultarPorChavePrimaria(remessa.getCodigo());
                    boletoBancarioControle.imprimirBoletosRemessa(remessaVO);

                }
                setSucesso(true);
                setMsgAlert("abrirPopupPDFImpressao('relatorio/" + boletoBancarioControle.getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
                setErro(false);

            } catch (Exception ex) {
                montarErro("Não foi possível imprimir o boleto! Favor verifique todas as configurações no convênio de cobrança, no tipo de remessa e também na conta corrente. Todas devem estar com o mesmo código do banco.");
                ex.printStackTrace();
            }
        }
    }

    public void exibirItensRemessa() {
        setMensagemDetalhada("", "");
        if (remessaVO != null) {
            try {
                if (remessaVO.getCodigo() != 0) {
                    povoarItensRemessaPorTipo(remessaVO);
                    if (remessaVO.isBoleto()) {
                        setExibirRemessaBoleto(true);
                    } else if (remessaVO.isNovoFormato()) {
                        setExibirRemessaNovo(true);
                    } else {
                        setExibirRemessa(true);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                setMensagemDetalhada(ex);
            }
        }
    }

    public void downloadRetorno(ActionEvent evt) {
        try {
            limparMsg();
            if ((getUsuarioLogado().getUsername().equals("admin") || getUsuarioLogado().getUsername().equals("PACTOBR")) && isExigirValidacao()) {
                setRemessaVO((RemessaVO) evt.getComponent().getAttributes().get("retorno"));
                setBaixarRemessa(false);
                setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmarDownload');");
            } else {
                if (!getUsuarioLogado().getUsername().equals("admin") && !getUsuarioLogado().getUsername().equals("PACTOBR")) {
                    setRemessaVO((RemessaVO) evt.getComponent().getAttributes().get("retorno"));
                }
                setMensagemDetalhada("", "");
                if (getRemessaVO() != null) {
                    if (getRemessaVO().getCodigo() != 0) {

                        if (!getRemessaVO().getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO) &&
                                !getRemessaVO().getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) &&
                                !Uteis.isAmbienteDesenvolvimentoTeste()) {
                            throw new Exception("Download indisponível. Entre em contato com a Pacto Soluções.");
                        }

                        setExibirRemessa(false);
                        setExibirRemessaNovo(false);
                        setExibirRemessaBoleto(false);
                        StringBuilder sb = getRemessaVO().getRetorno();
                        String path = this.getServletContext().getRealPath("relatorio") + File.separator + getRemessaVO().getIdentificador() + ".ret";
                        StringUtilities.saveToFile(sb, path);
                    }
                }
                setMsgAlertAuxiliar("Richfaces.hideModalPanel('modalConfirmarDownload');abrirPopup('UpdateServlet?op=downloadfile&file=" + getRemessaVO().getIdentificador() + ".ret&mimetype=txt','Retorno', 640,480);");
                setExigirValidacao(true);
                setRemessaVO(null);
            }
        } catch (Exception ex) {
            setExigirValidacao(true);
            setMensagemDetalhada(ex);
        }
    }

    private void obterEmpresa() {
        try {
            boolean temPermissao = permissao("ConsultarInfoTodasEmpresas");
            consultarInfoTodasEmpresas = temPermissao;

            if (getUsuarioLogado().getAdministrador() || temPermissao) {
                setEmpresaSelecionada("");
                setEmpresaVO(new EmpresaVO());
                setBuscarTodasEmpresas(true);
            } else {
                setEmpresaVO(getEmpresaLogado());
            }
        } catch (Exception ex) {
            setEmpresaVO(new EmpresaVO());
        }
    }

    public void selecionouEmpresaCarregarConvenios() throws Exception {
        selecionouEmpresa();
        carregarConvenios();
        atualizarSaldo();
    }

    public void selecionouEmpresa() {
        try {
            setBuscarTodasEmpresas(false);
            setEmpresaVO(new EmpresaVO());

            if (getEmpresaSelecionada().equals("TD")) {
                setEmpresaVO(new EmpresaVO());
                setBuscarTodasEmpresas(true);
            } else if (getEmpresaSelecionada().equals("0")) {
                setEmpresaVO(new EmpresaVO());
            } else {
                setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(Integer.parseInt(getEmpresaSelecionada()), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ex) {
            setBuscarTodasEmpresas(false);
            setEmpresaVO(new EmpresaVO());
        }
    }

    private void atualizarSaldo() throws Exception {

        if (TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.equals(TipoCobrancaPactoEnum.getConsultarPorCodigo(getEmpresaLogado().getTipoCobrancaPacto()))) {
            try {
                JSONObject jsonObject = getFacade().getEmpresa().obterInfoRedeDCC(Uteis.getUrlOamd(), getKey());
                setCreditoDCC(jsonObject.getInt("creditos"));
                setDataExpiracaoCreditoDCC(empresaVO.getDataExpiracaoCreditoDCC());
                setTipoCobrancaPacto(TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto()));
            } catch (Exception e) {
            }
        } else {
            EmpresaVO empresaVO = new EmpresaVO();
            if (getEmpresaVO().getCodigo() > 0) {
                empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            setCreditoDCC(empresaVO.getCreditoDCC());
            setDataExpiracaoCreditoDCC(empresaVO.getDataExpiracaoCreditoDCC());
            setTipoCobrancaPacto(TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto()));
        }
    }

    public void carregarConveniosTela() throws Exception {
        try {
            limparMsg();
            carregarConvenios();
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void carregarConvenios() throws Exception {
        setConvenios(new ArrayList<>());
        setListaConveniosCompleta(new ArrayList<>());

        if (isBuscarTodasEmpresas()) {
            setListaConveniosCompleta(getFacade().getConvenioCobranca().consultarTodosGeral(false, false, Uteis.NIVELMONTARDADOS_TODOS));
        } else {
            setListaConveniosCompleta(getFacade().getConvenioCobranca().consultarPorEmpresa(getEmpresaVO().getCodigo(),
                    this.isApresentarConvenioInativo() ? null : SituacaoConvenioCobranca.ATIVO, false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        Ordenacao.ordenarLista(getListaConveniosCompleta(), "descricao");
        for (ConvenioCobrancaVO convenioVO : getListaConveniosCompleta()) {
            convenioVO.setCodigoGenerico(convenioVO.getCodigo() + "|" + convenioVO.getEmpresa().getCodigo());
            if (!convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                    !convenioVO.getTipo().isTransacaoOnline() && !convenioVO.getTipo().isPix()) {
                if (isBuscarTodasEmpresas()) {
                    getConvenios().add(new SelectItem(convenioVO.getCodigoGenerico(), convenioVO.isGerarArquivoUnico() ? convenioVO.getDescricao() : (convenioVO.getDescricao() + " - " + convenioVO.getEmpresa_Apresentar())));
                } else {
                    getConvenios().add(new SelectItem(convenioVO.getCodigoGenerico(), convenioVO.getDescricao()));
                }
            }
        }

        if (!UteisValidacao.emptyList(getConvenios())) {
            getConvenios().add(0, new SelectItem("0|0", "TODOS"));
        }

        for (SelectItem itemUnico : getConvenios()) {
            if (itemUnico.getValue() != null && !itemUnico.getValue().equals("0|0")) {
                setCodigoGenericoConvenio((String) itemUnico.getValue());
                selecionarConvenio();
                break;
            }
        }
        agrupamentoParcelas = new AgrupamentoParcelasTO();
    }

    public void consultarInformacoes() {
        try {
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            setFiltroParcelaAutorizada("TODOS");
            setClienteVOFiltrar(new ClienteVO());
            clsMessages();
            atualizarSaldo();
            if (abaSelecionada.equals(ABA_REMESSA)) {
                consultarRemessas();
                if (isAgruparRemessas()) {
                    agruparRemessas();
                }
                notificarRecursoEmpresa(RecursoSistema.GESTAO_DE_REMESSAS_CONSULTAR_REMESSAS, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1));
            } else if (abaSelecionada.equals("abaParcelas")) {
                if (tiposConsultaEscolhido.length == 0) {
                    throw new ConsistirException("Selecione pelo menos um tipo de parcela.");
                }
                consultarParcelas();
                notificarRecursoEmpresa(RecursoSistema.GESTAO_DE_REMESSAS_CONSULTAR_PARCELAS, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1));
            } else if (abaSelecionada.equals(ABA_CANCELAMENTO)) {
                consultarItensPagos();
                notificarRecursoEmpresa(RecursoSistema.GESTAO_DE_REMESSAS_CONSULTAR_REMESSAS_CANCELAMENTO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim()) + 1));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage() == null ? e.getClass().getName() : e.getMessage());
        } finally {
            totalizar();
        }
    }

    public void confirmarGerarNovaRemessa() {
        try {
            limparMsg();
            setErrosGeracao(new ArrayList<>());
            setMsgAlert("");

            if (convenio == null || convenio.getCodigo() == 0) {
                throw new ConsistirException("Selecione um Convênio de Cobrança. Para esta operação, não pode ser todos.");
            }

            if (UteisValidacao.emptyNumber(getConvenio().getEmpresa().getCodigo())) {
                throw new ConsistirException("Nenhuma empresa no convênio de cobrança.");
            }

            Integer codModalidade = null;
            if (getConvenio().isBoleto() && getModalidade().getCodigo() > 0) {
                codModalidade = getModalidade().getCodigo();
            }

            int qtdItens = getFacade().getMovParcela().consultarParcelasDCCCount(getConvenio(), dataInicio, dataFim, dataInicioCobranca, dataFimCobranca, getConvenio().getEmpresa().getCodigo(), getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), !isMostrarParcelasComBoleto(), codModalidade, isApresentarAlunosBloqueioCobranca());
            if (qtdItens <= 0) {
                throw new ConsistirException("Não existe parcela em aberto para gerar remessa no periodo informado.");
            }
            double valorTotal = getFacade().getMovParcela().consultarParcelasDCCValor(getConvenio(), dataInicio, dataFim, dataInicioCobranca, dataFimCobranca, getConvenio().getEmpresa().getCodigo(), getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), !isMostrarParcelasComBoleto(), codModalidade, isApresentarAlunosBloqueioCobranca());

            if (valorTotal <= 0) {
                throw new ConsistirException("Existe "+qtdItens+" parcela em aberto com valor ZERADO não e possivel gerar remessa no periodo informado.");
            }
            String valorTotal_Apresentar = Formatador.formatarValorMonetarioSemMoeda(valorTotal);

            MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
            control.setMensagemDetalhada("", "");
            setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
            limparMsg();

            String vencimentoDe = Uteis.getData(getDataInicio(), "br");
            String vencimentoAte = Uteis.getData(getDataFim(), "br");

            StringBuilder msgModal = new StringBuilder();
            msgModal.append("Criar remessa com parcelas cujo vencimento é de ").append(vencimentoDe).append(" até ").append(vencimentoAte).append("?");
            msgModal.append("<br/>");
            msgModal.append("Total de itens: <b>").append(qtdItens).append(" </b> no valor de <b> " + getEmpresaLogado().getMoeda()).append(valorTotal_Apresentar).append("</b>.");

            control.init("Criar nova remessa", msgModal.toString(), this, "criarRemessaParcelasEmAberto", "", null, "", "panelConteudo");

        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void criarRemessaParcelasEmAberto() {
        try {
            setErrosGeracao(new ArrayList<>());

            convenio = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio.getCodigo(), convenio.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            List<RemessaVO> remessas;
            List<String> msgErro = new ArrayList<>();
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                Integer codModalidade = null;
                if (getConvenio().isBoleto() && getModalidade().getCodigo() > 0) {
                    codModalidade = getModalidade().getCodigo();
                }

                remessas = getService().preencherRemessaSemListaBoleto(getConvenio(), dataInicio, dataFim, dataInicioCobranca, dataFimCobranca, getConvenio().getEmpresa().getCodigo(),
                        getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), getUsuarioLogado(), !isMostrarParcelasComBoleto(), codModalidade);
            } else {
                List<MovParcelaVO> parcelas = getFacade().getMovParcela().consultarParcelasDCC(convenio, dataInicio, dataFim, dataInicioCobranca, dataFimCobranca, obterListaEmpresaBaseadoFiltros(),
                        getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), false, false, false,
                        null, true, isApresentarAlunosBloqueioCobranca(), null);
                if (parcelas.isEmpty()) {
                    throw new ConsistirException("Não existe parcela em aberto para gerar remessa no periodo informado.");
                }
                remessas = getService().preencherRemessas(parcelas, convenio, convenio.getEmpresa(), true, getUsuarioLogado(), msgErro);

            }

            if (getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO) &&
                    remessas != null) {

                boolean existeItem = false;
                for (RemessaVO remessaVO : remessas) {
                    if (!existeItem &&
                            !UteisValidacao.emptyList(remessaVO.getListaItens())) {
                        existeItem = true;
                    }
                    getErrosGeracao().addAll(remessaVO.getErrosGeracao());
                }
                if (!existeItem && getErrosGeracao().size() > 0) {
                    throw new ConsistirException("Verifique os erros na geração da remessa");
                }
            }

            for (RemessaVO remessa : remessas) {
                getService().gravarRemessa(remessa, convenio.getEmpresa());
            }

            //enviar remessas remoto
            String msgEnvioRemessa = getService().processoEnvioRemessasGestaoRemessas(convenio, getUsuarioLogado());

            montarMsgAlert(String.format("Remessa(s) \"%s\" criada(s) com sucesso! " + msgEnvioRemessa, remessas.toString()));
            abaSelecionada = ABA_REMESSA;

            if (getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO)) {
                this.setDataInicio(Calendario.hoje());
                this.setDataFim(Calendario.hoje());
            }

            consultarInformacoes();
            atualizarSaldo();
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
    }

    private void consultarParcelas() throws Exception {
        marcarTodos = false;
        marcarTodosTrocarCartao = false;
        marcarTodosContato = false;
        marcarTodosOutros = false;
        marcarTodosReenvio = false;
        marcarTodosCielo = false;
        marcarTodosSemAcao = false;
        List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();
        String[] tipos = tiposConsultaEscolhido;

        if (UteisValidacao.emptyNumber(getConvenio().getCodigo())) {
            throw new Exception("Selecione um convênio para realizar a consulta.");
        }

        if (!getConvenio().getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
            throw new Exception("Selecione um convênio ativo para realizar a consulta.");
        }

        for (String name : tipos) {
            TipoConsultaParcelasEnum t = TipoConsultaParcelasEnum.valueOf(name);
            if (t.equals(TipoConsultaParcelasEnum.PARCELAS_EM_ABERTO_AUTORIZADAS)) {
                List<MovParcelaVO> tmp = getFacade().getMovParcela().consultarParcelasDCC(getConvenio(), dataInicio, dataFim, dataInicioCobranca, dataFimCobranca,
                        obterListaEmpresaBaseadoFiltros(), getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), false, false,
                        !isMostrarParcelasComBoleto(), null, true, isApresentarAlunosBloqueioCobranca(), null);
                lista.addAll(tmp);
            } else if (t.equals(TipoConsultaParcelasEnum.PARCELAS_REPESCAGEM)) {
                List<MovParcelaVO> tmp = getFacade().getMovParcela().consultarParcelasDCC(getConvenio(), dataInicio, dataFim, dataInicioCobranca, dataFimCobranca,
                        obterListaEmpresaBaseadoFiltros(), getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), true, false,
                        !isMostrarParcelasComBoleto(), null, true, isApresentarAlunosBloqueioCobranca(), null);
                lista.addAll(tmp);
            }
        }
        setListaParcelas(Ordenacao.ordenarLista(lista, "pessoa_Apresentar"));
        getFacade().getMovParcela().montarMultaJurosParcelaVencida(getConvenio().getEmpresa(), getConvenio().getTipo().getTipoCobranca(), getListaParcelas(), Calendario.hoje());
        setListaParcelasGeral(getListaParcelas());
        montarFiltroStatus();
        agruparParcelas();
    }

    private void consultarItensPagos() throws Exception {
        List<RemessaCancelamentoItemVO> lista = getFacade().getRemessaCancelamentoItem().consultarItensAutorizados(convenio, dataInicio, dataFim);
        setItensAutorizados(lista);
        setItensAutorizadosFiltrada(getItensAutorizados());
        montarListaTipoParcela();
    }

    public void consultarParcelasBI( Integer codigoEmpresa, List<Integer> convenios, List<Integer> colaboradores, Date filtroBase) throws Exception {
        setMensagem("");
        setMensagemDetalhada("", "");
        atualizarSaldo();
        List<MovParcelaTransacaoTO> lista = new ArrayList<>();
        setMovParcelaTransacaoTO(new ArrayList<>());
        List<Integer> listaEmpresas = new ArrayList<Integer>();
        if(UteisValidacao.emptyList(convenios)){
            listaEmpresas.add(codigoEmpresa);
        } else {
            listaEmpresas = getFacade().getConvenioCobrancaEmpresa().obterEmpresasConvenio(convenios);
        }
        List<MovParcelaTransacaoTO> tmp = getFacade().getMovParcela().consultarParcelasDCCBIParcelasEmAbertoResumido(convenios, colaboradores, dataInicio, filtroBase, dataInicioCobranca, dataFimCobranca, listaEmpresas, getPlanoFiltro().getCodigo() == null ? 0 : planoFiltro.getCodigo(), true, false, false, null, false, true);
        lista.addAll(Ordenacao.ordenarLista(tmp, "nome"));

        setMovParcelaTransacaoTO(lista);
        // setListaParcelasGeral(getListaParcelas());
        //agruparParcelas();
    }

    public void agruparParcelas() {
        try {
            desmarcarTodos();
            Map<String, Integer> statusAcao = getFacade().getAcoesStatus().consultarPorTodosMapa();
            Map<Integer, List<MovParcelaVO>> acaoParcelas = new HashMap<Integer, List<MovParcelaVO>>();
            acaoParcelas.put(9999, new ArrayList<MovParcelaVO>());
            for (MovParcelaVO movParcela : getListaParcelas()) {
                Integer acaoCod = 9999;
                if (movParcela.getItemRemessa() != null && agrupar) {
                    try {
                        acaoCod = statusAcao.get(movParcela.getItemRemessa().getCodigoStatus());
                    } catch (Exception e) {
                    }
                    acaoCod = acaoCod == null ? 9999 : acaoCod;
                }
                List<MovParcelaVO> parcelas = acaoParcelas.get(acaoCod);
                if (parcelas == null) {
                    parcelas = new ArrayList<MovParcelaVO>();
                    acaoParcelas.put(acaoCod, parcelas);
                }
                movParcela.setAcao(AcoesRemessasEnum.getFromId(acaoCod));
                parcelas.add(movParcela);
            }
            agrupamentoParcelas = new AgrupamentoParcelasTO();
            Set<Integer> keySet = acaoParcelas.keySet();
            for (Integer key : keySet) {
                if (key == 9999) {
                    agrupamentoParcelas.setParcelasSemAcao(acaoParcelas.get(key));
                } else {
                    AcoesRemessasEnum acaoEsc = AcoesRemessasEnum.getFromId(key);
                    switch (acaoEsc) {
                        case CONTATO:
                            agrupamentoParcelas.setParcelasContato(acaoParcelas.get(key));
                            break;
                        case OUTROS:
                            agrupamentoParcelas.setParcelasOutros(acaoParcelas.get(key));
                            break;
                        case REALIZAR_CONTATO_CIELO:
                            agrupamentoParcelas.setParcelasContatoCielo(acaoParcelas.get(key));
                            break;
                        case REALIZAR_REENVIO:
                            agrupamentoParcelas.setParcelasReenvio(acaoParcelas.get(key));
                            break;
                        case TROCAR_CARTAO:
                            agrupamentoParcelas.setParcelasTrocarCartao(acaoParcelas.get(key));
                            getFacade().getClienteMensagem().verificarBloqueados(agrupamentoParcelas.getParcelasTrocarCartao());
                            break;
                    }
                }
            }
            agrupamentoParcelas.totalizar();
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void desmarcarTodos() {
        for (MovParcelaVO parcela : listaParcelas) {
            parcela.setParcelaEscolhida(false);
        }
    }

    private void consultarRemessas() throws Exception {
        try {
            if (dataInicio == null || dataFim == null) {
                setMensagemDetalhada("", "Informe as datas de início e fim para a consulta.");
                return;
            }

            String[] tipos = tiposConsultaStatusEscolhido;
            List<SituacaoRemessaEnum> situacaoRemessaEnums = new ArrayList<>();
            for (String name : tipos) {
                if (!UteisValidacao.emptyString(name)) {
                    situacaoRemessaEnums.add(SituacaoRemessaEnum.valueOf(name));
                }
            }

            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);

            List<Integer> empresas = new ArrayList<>();
            List<Integer> convenios = new ArrayList<>();
            if (!UteisValidacao.emptyNumber(convenio.getCodigo())) {
                convenios.add(convenio.getCodigo());
            }

            if (isBuscarTodasEmpresas()) {
                listaRemessas = getFacade().getZWFacade().getRemessa().consultar(dataInicio, dataFim, empresas, convenios, situacaoRemessaEnums, isFiltroRemessasCancelamento(), null);
            } else {
                if (!UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
                    empresas.add(getEmpresaVO().getCodigo());
                }
                listaRemessas = getFacade().getZWFacade().getRemessa().consultar(dataInicio, dataFim, empresas, convenios, situacaoRemessaEnums, isFiltroRemessasCancelamento(), null);
            }
            if (listaRemessas.isEmpty()) {
                setMensagemDetalhada("", "Nenhum registro encontrado no período.");
            } else {
                qtdItensTotal = 0;
                qtdItemDebito = 0;
                valorBrutoTotal = 0.0;
                valorAceitoTotal = 0.0;
                valorLiquidoTotal = 0.0;
                for (RemessaVO remessa : listaRemessas) {
                    getService().getL().lerHeaderETrailerRemessa(remessa);
                    LayoutRemessaBase.lerRetorno(remessa);
                    remessa.setValorAceito(getFacade().getZWFacade().getRemessaItem().consultarPorCodigoValorRemessaAceito(remessa));
                    qtdItensTotal += remessa.getQtdRegistros();
                    qtdItemDebito = qtdItensAutorizarDebito(remessa);
                    valorBrutoTotal += remessa.getValorBruto();
                    valorAceitoTotal += remessa.getValorAceito();
                    valorLiquidoTotal += remessa.getValorLiquido();
                }
            }
        } finally {
            CacheControl.clear();
        }
    }

    public void agruparRemessas() {
        setAgruparRemessas(true);
        Map<Date, AgrupadorRemessaTO> mapaAgrupamento = new HashMap<Date, AgrupadorRemessaTO>();
        for (RemessaVO remessa : getListaRemessas()) {
            Date dataReferencia = Calendario.getDataComHoraZerada(remessa.getDataRegistro());
            AgrupadorRemessaTO agrupamento = mapaAgrupamento.get(dataReferencia);
            if (agrupamento == null) {
                agrupamento = new AgrupadorRemessaTO();
                agrupamento.setDiaAgrupado(Calendario.getDataComHoraZerada(remessa.getDataRegistro()));
                mapaAgrupamento.put(dataReferencia, agrupamento);
            }
            agrupamento.adicionarItem(remessa);
        }

        setAgrupamentoRemessas(new ArrayList<AgrupadorRemessaTO>());
        for (Date dataAgrupada : mapaAgrupamento.keySet()) {
            getAgrupamentoRemessas().add(mapaAgrupamento.get(dataAgrupada));
        }
        Ordenacao.ordenarLista(getAgrupamentoRemessas(), "diaAgrupado");
        Collections.reverse(getAgrupamentoRemessas());
    }

    public String desagruparRemessas() {
        setAgruparRemessas(false);
        return "";
    }

    public void fecharPanelDadosParametros() throws Exception {
        itensProcessar = new ArrayList<RemessaItemVO>();
        remessaRetorno = new RemessaVO();
        retornoBoleto = new RemessaVO();
        escolhaGerarRemessaOuProcessarRetorno = 0;
        this.setExibirControles(true);
        this.setExibirModalParametros(false);
        this.setExibirRemessa(false);
        this.setExibirRemessaNovo(false);
        this.setExibirRemessaBoleto(false);
        limparMsg();
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
    }

    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaVO().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        List<TransacaoVO> listaClonada = new ArrayList();
        try {
            listaClonada.addAll(getListaManipulavel());
            Ordenacao.ordenarLista(listaClonada, "nomePessoa");

            params.put("nomeRelatorio", "GestaoRemessas");
            params.put("nomeEmpresa", empre.getNome());

            params.put("tituloRelatorio", "Relatório Gestão de Remessas");
            params.put("nomeDesignIReport", getDesignIReportRelatorio());
            params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
            params.put("listaObjetos", listaClonada);

            params.put("filtros", getFiltrosDescricao(true, null));
            params.put("dataIni", Uteis.getData(dataInicio));
            params.put("dataFim", Uteis.getData(dataFim));
            params.put("enderecoEmpresa", empre.getEndereco());
            params.put("cidadeEmpresa", empre.getCidade().getNome());
            params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        } finally {
            listaClonada = null;
        }
    }

    public void imprimirRelatorio() {
        try {
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            apresentarRelatorioObjetos(parametros);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "GestaoTransacoes.jrxml");
    }

    public boolean isExibirControles() {
        return exibirControles;
    }

    public void setExibirControles(boolean exibirControles) {
        this.exibirControles = exibirControles;
    }

    @Override
    public void filtrarPorTexto() {
        super.filtrarPorTexto();
        DataScrollerControle dataScroller = (DataScrollerControle) getControlador(DataScrollerControle.class.getSimpleName());
        dataScroller.getDataScroller().getDataTable().setValue(getListaManipulavel());
    }

    public void uploadListener(UploadEvent event) {
        setOnCompleteBoleto("");
        UploadItem item = event.getUploadItem();
        limparMsg();
        limparInformacoesRetornoBoleto();
        try {
            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);
            TipoConvenioCobrancaEnum tipo = getConvenio().getTipo();
            FileInputStream is = new FileInputStream(item.getFile().getPath());
            StringBuilder head = Uteis.convertStreamToStringBuffer(is);
            if (tipo != null && tipo.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                Security.addProvider(new BouncyCastleProvider());
                if (!UteisValidacao.emptyString(getConvenio().getNossaChave())) {
                    byte[] arquivo = FileUtilities.obterBytesArquivo(item.getFile());
                    InputStream chave = new ByteArrayInputStream(getConvenio().getNossaChave().getBytes());
                    byte[] decrypt = PgpEncryption.decrypt(arquivo, chave, getConvenio().getNossaSenha(), false);
                    head = new StringBuilder(new String(decrypt, "UTF-8"));
                }
            }

            nomeArquivo = item.getFileName();

            processoRetornoRemessaTO = new ProcessoRetornoRemessaTO();
            processoRetornoRemessaTO.setNomeArquivo(nomeArquivo);
            processoRetornoRemessaTO.setIdentificadorEmpresaFinaceiro(getIdentificadorEmpresaFinaceiro());
            processoRetornoRemessaTO.setEmpresaFinaceiro(empresaFinaceiro);
            processoRetornoRemessaTO.setEmpresaFinanceiroVOs(getEmpresaFinanceiroVOs());
            processoRetornoRemessaTO.setMapaItemEmpresa(getMapaItemEmpresa());

            if (head != null && !head.toString().equals("")) {
                headRetorno = new StringBuilder(head);
                processoRetornoRemessaTO.setHeadRetorno(headRetorno);
                getService().preencherRemessaDoRetorno(processoRetornoRemessaTO, getConvenio(), getEmpresaVO());
                preencherControle();
            }
            if (isConvenioTipoBoleto() || convenio.isBoleto()) {
                retornoRemessaVO = obterRetornoRemessa(item.getFile());
            }
            setApresentarBotaoProcessar(true);
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    private void limparInformacoesRetornoBoleto() {
        setDataPrevistaCredito(null);
        setQuantidadeDeItens(0);
        setValorTotalDoArquivo(0.0);
        setQuantidadeItensReconhecidos(0);
        setValorTotalASerBaixado(0.0);
        setQuantidadeItensBaixaEmDuplicidade(0);
        setQuantidadeItensBaixaValorMenor(0);
        setQuantidadeItensBaixaValorMaior(0);
        setQuantidadeItensBaixaNormal(0);
        setQuantidadeItensBaixaManual(0);
        setQuantidadeItensNaoReconhecido(0);
        setValorTotalNaoReconhecido(0.0);
        setQuantidadeItensRegistrados(0);
    }

    public String getHeadRetornoString() {
        return headRetorno != null && headRetorno.length() > 0 ? headRetorno.toString().substring(0, headRetorno.toString().indexOf("\n")) : "";
    }

    private void preencherControle() throws Exception {
        nomeArquivo = processoRetornoRemessaTO.getNomeArquivo();
        itensProcessar = processoRetornoRemessaTO.getItensProcessar();
        itensProcessarBoleto = processoRetornoRemessaTO.getItensProcessarBoleto();
        remessaRetorno = processoRetornoRemessaTO.getRemessaRetorno();
        retornoBoleto = processoRetornoRemessaTO.getRetornoBoleto();
        remessaVO = processoRetornoRemessaTO.getRemessaVO();
        headRetorno = processoRetornoRemessaTO.getHeadRetorno();
        quantidadeAutorizacoes = processoRetornoRemessaTO.getQuantidadeAutorizacoes();
        quantidadeConfirmacoes = processoRetornoRemessaTO.getQuantidadeConfirmacoes();
        quantidadeItensReconhecidos = processoRetornoRemessaTO.getQuantidadeItensReconhecidos();
        quantidadeDeItens = processoRetornoRemessaTO.getQuantidadeDeItens();
        identificadorEmpresaFinaceiro = processoRetornoRemessaTO.getIdentificadorEmpresaFinaceiro();
        empresaFinanceiroVOs = processoRetornoRemessaTO.getEmpresaFinanceiroVOs();
        itensOutrasEmpresa = processoRetornoRemessaTO.getItensOutrasEmpresa();
        mapaItemEmpresa = processoRetornoRemessaTO.getMapaItemEmpresa();
        empresaFinaceiro = processoRetornoRemessaTO.getEmpresaFinaceiro();
        dataPrevistaCredito = processoRetornoRemessaTO.getDataPrevistaCredito();
        valorTotalDoArquivo = processoRetornoRemessaTO.getValorTotalDoArquivo();
        quantidadeItensNaoReconhecido = processoRetornoRemessaTO.getQuantidadeItensNaoReconhecido();
        valorTotalNaoReconhecido = processoRetornoRemessaTO.getValorTotalNaoReconhecido();
        valorTotalASerBaixado = processoRetornoRemessaTO.getValorTotalASerBaixado();
        quantidadeItensRegistrados = processoRetornoRemessaTO.getQuantidadeItensRegistrados();
        quantidadeItensBaixaValorMenor = processoRetornoRemessaTO.getQuantidadeItensBaixaValorMenor();
        quantidadeItensBaixaValorMaior = processoRetornoRemessaTO.getQuantidadeItensBaixaValorMaior();
        quantidadeItensBaixaNormal = processoRetornoRemessaTO.getQuantidadeItensBaixaNormal();
        quantidadeItensBaixaEmDuplicidade = processoRetornoRemessaTO.getQuantidadeItensBaixaEmDuplicidade();
    }

    public void confirmarProcessarBoletoOnline() {
        setOnCompleteBoleto("");
        setMensagem("");
        setMensagemDetalhada("", "");
        String processamentoOutrasEmpresas = "";
        try {
            String retornoProcessamento = "";
            if (!UteisValidacao.emptyList(this.getItensProcessarBoleto())) {
                getService().setUsuarioVO(getUsuarioLogado());

                Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();
                Map<Long, BoletoVO> mapaItens = new HashMap<>();
                for (BoletoVO item : itensProcessarBoleto) {
                    ConvenioCobrancaVO convenioVO = mapConve.get(item.getConvenioCobrancaVO().getCodigo());
                    if (convenioVO == null) {
                        convenioVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(item.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        mapConve.put(convenioVO.getCodigo(), convenioVO);
                    }
                    item.setConvenioCobrancaVO(convenioVO);
                    mapaItens.put(Long.valueOf(item.getCodigo()), item);
                }
                retornoBoleto.setEmpresa(getEmpresaVO().getCodigo());
                retornoRemessaVO = getFacade().getRetornoRemessa().consultarPorCodigo(retornoRemessaVO.getCodigo());

                if (retornoRemessaVO.getDataProcessamento() == null) {
                    retornoRemessaVO.setDataProcessamento(Calendario.hoje());
                }
                retornoRemessaVO.setDataUltimoProcessamento(Calendario.hoje());
                retornoRemessaVO.limpar();

//                Map<Integer, EmpresaVO> mapaEmpresas = getFacade().getZWFacade().getEmpresa().obterMapaEmpresas();
                retornoProcessamento = getService().processarRetornoBoletoOnline(retornoBoleto, mapaItens, retornoRemessaVO, getIdentificadorEmpresaFinaceiro());
            }

//            if (mapaItemEmpresa != null && !mapaItemEmpresa.isEmpty()) {
//                ConvenioCobrancaVO cobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
//                processamentoOutrasEmpresas = doProcessamentoRetornoServlet(mapaItemEmpresa, getNomeArquivo(), retornoString, cobrancaVO);
//            }

            //Arquivo nomeArquivo Processado às DataHotaMinutoSegundo!
            String msgRetorno = "Arquivo " + getNomeArquivo() + " Processado às " + Uteis.getDataComHora(retornoRemessaVO.getDataUltimoProcessamento() == null ? Calendario.hoje() : retornoRemessaVO.getDataUltimoProcessamento()) + "\n" + processamentoOutrasEmpresas;
            setMensagem(msgRetorno + " \n " + retornoProcessamento);
            remessaVO.setRetorno(new StringBuilder());

            setApresentarBotaoProcessar(false);
            montarSucessoGrowl(retornoProcessamento);
        } catch (Exception ex) {
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex);
            montarErro(ex);
        }
    }

    public void confirmarProcessarBoleto() {
        setMensagem("");
        setMensagemDetalhada("", "");
        String processamentoOutrasEmpresas = "";
        try {
            String retornoString = retornoRemessaVO.getArquivoRetorno();
            if (itensProcessar != null && !itensProcessar.isEmpty()) {
                getService().setUsuarioVO(getUsuarioLogado());
                Map<Long, RemessaItemVO> mapaItens = new HashMap<Long, RemessaItemVO>();
                for (RemessaItemVO item : itensProcessar) {
                    if (item.getRemessa().getConvenioCobranca().isUsarIdentificador() || !getIdentificadorEmpresaFinaceiro().equals("")) {
                        mapaItens.put(Long.valueOf(item.getIdentificador()), item);
                    } else {
                        mapaItens.put(Long.valueOf(item.getCodigo()), item);
                    }
                }
                retornoBoleto.setEmpresa(getEmpresaVO().getCodigo());
                retornoRemessaVO = getFacade().getRetornoRemessa().consultarPorCodigo(retornoRemessaVO.getCodigo());

                if (retornoRemessaVO.getDataProcessamento() == null) {
                    retornoRemessaVO.setDataProcessamento(Calendario.hoje());
                }
                retornoRemessaVO.setDataUltimoProcessamento(Calendario.hoje());
                retornoRemessaVO.limpar();

                Map<Integer, EmpresaVO> mapaEmpresas = getFacade().getZWFacade().getEmpresa().obterMapaEmpresas();
                Set<Integer> remessas = getService().processarRetornoBoleto(retornoBoleto, mapaItens, retornoRemessaVO, mapaEmpresas, getIdentificadorEmpresaFinaceiro());
            }

            if (mapaItemEmpresa != null && !mapaItemEmpresa.isEmpty()) {
                ConvenioCobrancaVO cobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
                processamentoOutrasEmpresas = doProcessamentoRetornoServlet(mapaItemEmpresa, getNomeArquivo(), retornoString, cobrancaVO);
            }
            //Arquivo nomeArquivo Processado às DataHotaMinutoSegundo!
            setMensagem("Arquivo " + getNomeArquivo() + " Processado às " + Uteis.getDataComHora(retornoRemessaVO.getDataUltimoProcessamento() == null ? Calendario.hoje() : retornoRemessaVO.getDataUltimoProcessamento()) + "\n" + processamentoOutrasEmpresas);
            remessaVO.setRetorno(new StringBuilder());

            setApresentarBotaoProcessar(false);

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private RetornoRemessaVO obterRetornoRemessa(File arquivoRetorno) throws Exception {
        RetornoRemessaVO retornoRemessaVO = new RetornoRemessaVO();
        retornoRemessaVO.setNomeArquivo(getNomeArquivo());
        retornoRemessaVO = getFacade().getRetornoRemessa().consultarPorNomeArquivo(retornoRemessaVO.getNomeArquivo());
        StringBuilder arq = FileUtilities.readContentFile(arquivoRetorno.getAbsolutePath());
        if (retornoRemessaVO == null || retornoRemessaVO.getCodigo() == null || retornoRemessaVO.getCodigo() <= 0) {
            retornoRemessaVO = new RetornoRemessaVO();
            retornoRemessaVO.setNomeArquivo(getNomeArquivo());
            retornoRemessaVO.setUsuarioVO(getUsuarioLogado());
            retornoRemessaVO.setArquivoRetorno(arq.toString());
            retornoRemessaVO.setDataPrevistaCredito(getDataPrevistaCredito());
            retornoRemessaVO.setQuantidadeDeItens(getQuantidadeDeItens());
            retornoRemessaVO.setValorTotalDoArquivo(getValorTotalDoArquivo());
            getFacade().getRetornoRemessa().incluir(retornoRemessaVO);
        } else {
            if (arq.length() > 0 && (retornoRemessaVO.getArquivoRetorno() == null || retornoRemessaVO.getArquivoRetorno().length() == 0)) {
                retornoRemessaVO.setArquivoRetorno(arq.toString());
                getFacade().getRetornoRemessa().alterarSomenteArquivo(retornoRemessaVO);
            }
        }

        return retornoRemessaVO;
    }

    public void confirmarProcessarItau() {
        setMensagem("");
        setMensagemDetalhada("", "");
        try {
            getService().setUsuarioVO(getUsuarioLogado());

            ConvenioCobrancaVO conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio.getCodigo(), convenio.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Map<Integer, RemessaItemVO> mapaItens = new HashMap<>();
            for (RemessaItemVO item : itensProcessar) {
                if (conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)
                        || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                    mapaItens.put(item.getCodigo(), item);
                } else {
                    mapaItens.put(item.getMovParcela().getCodigo(), item);
                }
            }
            remessaRetorno.setEmpresa(convenio.getEmpresa().getCodigo());

            Set<Integer> remessas = new HashSet<Integer>();
            List<ItemRetornoRemessaTO> registrosNaoEncontrados = new ArrayList<ItemRetornoRemessaTO>();
            if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_SANTANDER)) {
                remessaRetorno.setListaItens(itensProcessar);
                getService().processarRetorno(remessaRetorno, getUsuarioLogado());
            } else if (conv.getTipo() != null && (conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU) || conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL))) {
                remessas = getService().processarRetornoItauBoleto(remessaRetorno, mapaItens, getUsuarioLogado(), registrosNaoEncontrados);
            } else {
                remessas = getService().processarRetornoItau(remessaRetorno, mapaItens);
            }

            String remessasProcessadas = "";
            String remessasNaoProcessadas = "";
            for (Integer codigo : remessas) {
                if (conv.getTipo() != null
                        && (conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU)
                        || conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL))) {
                    if (getFacade().getZWFacade().getRemessa().processarSituacaoRemessaItauBoleto(codigo)) {
                        remessasProcessadas += ", " + codigo;
                    } else {
                        remessasNaoProcessadas += ", " + codigo;
                    }
                } else {
                    if (getFacade().getZWFacade().getRemessa().processarSituacaoRemessa(codigo)) {
                        remessasProcessadas += ", " + codigo;
                    } else {
                        remessasNaoProcessadas += ", " + codigo;
                    }
                }
            }
            setMensagem((remessasProcessadas.isEmpty() ? "" : ("Remessas com retorno totalmente processado: " + remessasProcessadas.replaceFirst(", ", "") + "<br/>"))
                    + (remessasNaoProcessadas.isEmpty() ? "" : ("Remessas com itens a serem processados ainda: "
                    + remessasNaoProcessadas.replaceFirst(", ", ""))));
            remessaVO.setRetorno(new StringBuilder());

            setRegistrosNaoEncontrados(registrosNaoEncontrados);
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void confirmarProcessarRetorno() {
        setMensagem("");
        setMensagemDetalhada("", "");
        try {
            getService().preencherRemessaDoRetorno(getProcessoRetornoRemessaTO(), convenio, convenio.getEmpresa());
            remessaVO.setResultadoProcessamentoRetorno("");
            if (remessaVO.getRetorno().length() > 250 ||
                    (TipoRemessaEnum.DCC_BIN.equals(remessaVO.getTipo()) && remessaVO.getRetorno().length() == 201)) {
                remessaVO.setConvenioCobranca(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(remessaVO.getConvenioCobranca().getCodigo(), remessaVO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS));
                getService().setUsuarioVO(getUsuarioLogado());
                getService().processarRetorno(remessaVO, getUsuarioLogado());
                remessaVO.setResultadoProcessamentoRetorno("Processamento concluído. Clique no botão resultado para maiores detalhes.");
                setMensagem(remessaVO.getResultadoProcessamentoRetorno());
                remessaVO.setRetorno(new StringBuilder());
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void confirmarProcessarFebraban() {
        setMensagem("");
        setMensagemDetalhada("", "");
        Set<Integer> remessas;
        List<String> dadosInvalidos = new ArrayList<String>();
        try {
            ConvenioCobrancaVO conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio.getCodigo(), convenio.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            getService().setUsuarioVO(getUsuarioLogado());
            remessaRetorno.setEmpresa(convenio.getEmpresa().getCodigo());
            remessaRetorno.setListaItens(itensProcessar);
            remessaRetorno.setConvenioCobranca(conv);
            remessas = getService().processarRetornoFebraban(remessaRetorno, dadosInvalidos, null, convenio);

            String remessasProcessadas = "";
            String remessasNaoProcessadas = "";
            for (Integer codigo : remessas) {
                if (getFacade().getZWFacade().getRemessa().processarSituacaoRemessa(codigo)) {
                    remessasProcessadas += ", " + codigo;
                } else {
                    remessasNaoProcessadas += ", " + codigo;
                }
            }
            String cpfs = "";
            for (String cpf : dadosInvalidos) {
                cpfs += ", " + cpf;
            }

            setMensagem((remessasProcessadas.isEmpty() ? "" : ("Remessas com retorno totalmente processado: " + remessasProcessadas.replaceFirst(", ", "") + "<br/>"))
                    + (remessasNaoProcessadas.isEmpty() ? "" : ("Remessas com itens a serem processados ainda: " + remessasNaoProcessadas.replaceFirst(", ", "") + "<br/>"))
                    + (cpfs.isEmpty() ? "" : ("DadosInválidos: " + cpfs.replaceFirst(", ", ""))));
            remessaRetorno.setRetorno(new StringBuilder());
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void imprimirDetalhe() {
        try {
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            CacheControl.clear();
        }
    }

    public void marcarOuDesmarcarTodos() {
        marcarDesmarcarCielo();
        marcarDesmarcarOutros();
        marcarDesmarcarTrocarCartao();
        marcarDesmarcarReenvio();
        marcarDesmarcarSemAcao();
        marcarDesmarcarContato();
        totalizar();
    }

    public void totalizar() {
        if (agrupamentoParcelas != null) {
            agrupamentoParcelas.totalizarSelecionadas();
        }

        List<MovParcelaVO> lista = listaParcelas;
        //Totais Geral
        somaItensEmAberto = 0.0;
        qtdItensEmAberto = 0;
        somaItensRepescagem = 0.0;
        qtdItensRepescagem = 0;
        //Seleção
        qtdItensEmAbertoSelecao = 0;
        somaItensEmAbertoSelecao = 0.0;
        qtdItensRepescagemSelecao = 0;
        somaItensRepescagemSelecao = 0.0;
        remessasDiferentes = false;
        int remessa = 0;
        for (MovParcelaVO mp : lista) {
            Double valorComMultaJuros = mp.getValorParcela() + mp.getValorMultaJuros();
            if (mp.getNrTentativas() > 0) {
                somaItensRepescagem += valorComMultaJuros;
                qtdItensRepescagem += 1;
                if (mp.getParcelaEscolhida()) {
                    qtdItensRepescagemSelecao += 1;
                    somaItensRepescagemSelecao += valorComMultaJuros;
                    if (mp.getItemRemessa() != null && mp.getItemRemessa().getRemessa() != null) {
                        if (remessa > 0 && remessa != mp.getItemRemessa().getRemessa().getCodigo().intValue()) {
                            remessasDiferentes = true;
                        }
                        remessa = mp.getItemRemessa().getRemessa().getCodigo().intValue();
                    }
                }
            } else {
                somaItensEmAberto += valorComMultaJuros;
                qtdItensEmAberto += 1;
                if (mp.getParcelaEscolhida()) {
                    qtdItensEmAbertoSelecao += 1;
                    somaItensEmAbertoSelecao += valorComMultaJuros;
                    if (mp.getItemRemessa() != null && mp.getItemRemessa().getRemessa() != null) {
                        if (remessa > 0 && remessa != mp.getItemRemessa().getRemessa().getCodigo().intValue()) {
                            remessasDiferentes = true;
                        }
                        remessa = mp.getItemRemessa().getRemessa().getCodigo().intValue();
                    }
                }
            }
        }
    }

    public void criarRemessa() {
        criarRemessa(listaParcelas);
    }

    public void criarRemessaContatoCielo() {
        criarRemessa(agrupamentoParcelas.getParcelasContatoCielo());
    }

    public void criarRemessaReenvio() {
        criarRemessa(agrupamentoParcelas.getParcelasReenvio());
    }

    public void criarRemessa(List<MovParcelaVO> parcelas) {
        listaRemessaComErro = new ArrayList<MovParcelaVO>();
        Set<Integer> quitadas = new HashSet<Integer>();
        clsMessages();
        setMsgAlert("");
        try {
            if (convenio == null || convenio.getCodigo() == 0) {
                throw new ConsistirException("Selecione um Convênio de Cobrança. Para esta operação, não pode ser todos.");
            }
            if (convenio.getSituacao().equals(SituacaoConvenioCobranca.INATIVO)) {
                throw new ConsistirException("Não e possivel gerar uma remessa para um convênio inativo");
            }

            if (UteisValidacao.emptyNumber(convenio.getEmpresa().getCodigo()) && (convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && convenio.isGerarArquivoUnico())) {
                throw new ConsistirException("Selecione uma empresa que será utilizada para informações do DCO.");
            }

            if (UteisValidacao.emptyNumber(convenio.getEmpresa().getCodigo())) {
                throw new ConsistirException("Selecione uma empresa para poder criar a remessa.");
            }

            List<MovParcelaVO> novaLista = new ArrayList<MovParcelaVO>();
            for (MovParcelaVO mp : parcelas) {
                if (mp.getParcelaEscolhida()) {
                    if (getFacade().getMovParcela().parcelaEmAberto(mp) && !getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(mp)) {
                        novaLista.add(mp);
                    } else {
                        quitadas.add(mp.getCodigo());
                    }
                }
            }
            if (!novaLista.isEmpty()) {
                novaLista = Ordenacao.ordenarLista(novaLista, "dataVencimento");
                convenio = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio.getCodigo(), convenio.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                List<RemessaVO> remessas;
                List<String> msgErro = new ArrayList<>();
                if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                    remessas = getService().preencherRemessaBoleto(novaLista, convenio, true, getUsuarioLogado());
                } else {
                    remessas = getService().preencherRemessas(novaLista, convenio, convenio.getEmpresa(), true, getUsuarioLogado(), msgErro);
                }

                StringBuilder erros = new StringBuilder();
                for (String erro : msgErro) {
                    erros.append(erro).append(" \n");
                }

                if (UteisValidacao.emptyList(remessas)) {
                    throw new Exception("Nenhuma remessa gerada. \n" + erros.toString());
                }

                if (!UteisValidacao.emptyList(msgErro)) {
                    montarAviso(erros.toString());
                }

                for (RemessaVO remessa : remessas) {
                    getService().gravarRemessa(remessa, convenio.getEmpresa());
                }

                //enviar remessas remoto
                String msgEnvioRemessa = getService().processoEnvioRemessasGestaoRemessas(convenio, getUsuarioLogado());

                abaSelecionada = ABA_REMESSA;
                consultarInformacoes();
                marcarTodos = false;
                marcarOuDesmarcarTodos();
                parcelas.removeAll(novaLista);
                consultarParcelas();
                montarSucessoGrowl("Remessa(s) " + remessas.toString() + " criada(s) com sucesso! " + msgEnvioRemessa);
            }

            atualizarSaldo();
            if (!quitadas.isEmpty()) {
                montarAviso("As parcelas com os códigos " + quitadas.toString() + " já foram quitadas no caixa ou já estão dentro de uma remessa aguardando retorno!");
            }
            setMsgAlert("Richfaces.hideModalPanel('mdlGeracaoRemessa');Richfaces.hideModalPanel('mdlCriarRemessaCielo');Richfaces.hideModalPanel('mdlCriarRemessaReenvio');" + getMensagemNotificar() + ";");
        } catch (Exception e) {
            montarErro(e);
            if (getListaRemessaComErro().isEmpty()) {
                setMsgAlert("Richfaces.hideModalPanel('mdlGeracaoRemessa');Richfaces.hideModalPanel('mdlCriarRemessaCielo');Richfaces.hideModalPanel('mdlCriarRemessaReenvio');" + getMensagemNotificar() + ";");
            } else {
                setMsgAlert("Richfaces.hideModalPanel('mdlGeracaoRemessa');Richfaces.hideModalPanel('mdlCriarRemessaCielo');Richfaces.hideModalPanel('mdlCriarRemessaReenvio');Richfaces.showModalPanel('modalRemesaComErro');" + getMensagemNotificar() + ";");
            }
            //montarMsgAlert(e.getMessage() == null ? e.getClass().getName() : e.getMessage());
        }
    }

    public void criarRemessaCancelamento() {
        try {
            if (convenio == null || convenio.getCodigo() == 0) {
                throw new ConsistirException("Selecione um Convênio de Cobrança. Para esta operação, não pode ser todos.");
            }

            List<RemessaCancelamentoItemVO> itensParaCancelar = new ArrayList<RemessaCancelamentoItemVO>();
            for (RemessaCancelamentoItemVO item : getItensAutorizadosFiltrada()) {
                if (item.getSelecionado() && itensParaCancelar
                        .stream()
                        .noneMatch(p -> p.getItemRemessaCancelar().getMovPagamento().getCodigo().equals(item.getItemRemessaCancelar().getMovPagamento().getCodigo()))) {
                        itensParaCancelar.add(item);
                }
            }

            convenio = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio.getCodigo(), convenio.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            List<RemessaVO> remessasCancelamento = getService().preencherRemessasCancelamento(itensParaCancelar, convenio, convenio.getEmpresa(), getUsuarioLogado());
            for (RemessaVO remessa : remessasCancelamento) {
                getService().gravarRemessa(remessa, convenio.getEmpresa());
            }

            //enviar remessas remoto
            String msgEnvioRemessa = getService().processoEnvioRemessasGestaoRemessas(convenio, getUsuarioLogado());

            consultarItensPagos();
            atualizarSaldo();
            montarMsgAlert("Remessa de cancelamento gerada com sucesso! " + msgEnvioRemessa);
        } catch (Exception e) {
            montarMsgAlert(e.getMessage() == null ? e.getClass().getName() : e.getMessage());
        }
    }

    public void abrirModalAutorizacoes() {
        this.exibirAutorizacoes = true;
    }

    public void fecharModalAutorizacoes() {
        this.exibirAutorizacoes = false;
    }

    public void abrirModalRetornoUpload(ActionEvent evt) {
        remessaVO = null;
        this.exibirModalUpload = true;
    }

    public void fecharModalRetornoUpload() {
        remessaVO = null;
        this.exibirModalUpload = false;
    }

    public List<RemessaItemVO> getItensRemessaEstorno() {
        return itensRemessaEstorno;
    }

    public void setItensRemessaEstorno(List<RemessaItemVO> itensRemessaEstorno) {
        this.itensRemessaEstorno = itensRemessaEstorno;
    }

    public List<TotalizadorRemessaTO> getTotalizador() {
        return totalizador;
    }

    public void setTotalizador(List<TotalizadorRemessaTO> totalizador) {
        this.totalizador = totalizador;
    }

    public List<SelectItem> getAcoes() {
        return acoes;
    }

    public void setAcoes(List<SelectItem> acoes) {
        this.acoes = acoes;
    }

    public AcoesStatusRemessaVO getAcao() {
        if (acao == null) {
            acao = new AcoesStatusRemessaVO();
        }
        return acao;
    }

    public void setAcao(AcoesStatusRemessaVO acao) {
        this.acao = acao;
    }

    public List<AcoesStatusRemessaVO> getListaAcoes() {
        if (listaAcoes == null) {
            listaAcoes = new ArrayList<AcoesStatusRemessaVO>();
        }
        return listaAcoes;
    }

    public void setListaAcoes(List<AcoesStatusRemessaVO> listaAcoes) {
        this.listaAcoes = listaAcoes;
    }

    public boolean getApresentarLista() {
        return !UteisValidacao.emptyList(listaAcoes);
    }

    public AgrupamentoParcelasTO getAgrupamentoParcelas() {
        return agrupamentoParcelas;
    }

    public void setAgrupamentoParcelas(AgrupamentoParcelasTO agrupamentoParcelas) {
        this.agrupamentoParcelas = agrupamentoParcelas;
    }

    public boolean isAgrupar() {
        return agrupar;
    }

    public void setAgrupar(boolean agrupar) {
        this.agrupar = agrupar;
    }

    public void abrirContatoCliente() {
        try {
            validarPermissao("RealizarContato", "7.23 - Realizar Contato", getUsuarioLogado());
            MovParcelaVO parcela = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parc");
            HistoricoContatoControle histContatoControle = (HistoricoContatoControle) getControlador(HistoricoContatoControle.class
                    .getSimpleName());
            histContatoControle.novo();

            histContatoControle.getHistoricoContatoVO()
                    .setContatoAvulso(true);
            ClienteVO cli = getFacade().getCliente().consultarPorCodigoPessoa(parcela.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            histContatoControle.inicializarCliente(Calendario.hoje(), cli);

            setMsgAlert(
                    "abrirPopup('realizarContatoForm.jsp', 'RealizarContatoform', 664, 635);");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("");
        }
    }

    public void abrirContatoCielo() {
        try {
            MovParcelaVO parcela = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parc");
            ConvenioCobrancaVO convenio = getFacade().getConvenioCobranca().consultarPorMovParcela(parcela.getCodigo(), parcela.getCodigoEmpresa());
            numeroEstabelecimento = convenio.getNumeroContrato();
            RemessaVO remessa = getFacade().getZWFacade().getRemessa().consultarPorMovParcela(parcela.getCodigo());
            dataEnvio = remessa.getDataRegistro();
            dataRetorno = remessa.getDataRetorno();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
            setMensagemDetalhada("");
        }
    }

    public void gerenciarAcesso(final boolean emMassa, final boolean bloqueio, String metodo) {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        setMsgAlert("");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                //para bloqueio em massa
                if (emMassa && bloqueio) {
                    List<Integer> jaBloqueados = new ArrayList<Integer>();
                    for (MovParcelaVO parcela : agrupamentoParcelas.getParcelasTrocarCartao()) {
                        if (parcela.getParcelaEscolhida()
                                && !parcela.isAcessoBloqueado()
                                && !jaBloqueados.contains(parcela.getPessoa().getCodigo())) {
                            getFacade().getClienteMensagem().lancarBloqueioCatraca(getMensagemInternalizacao("TROCAR_CARTAO"),
                                    parcela.getPessoa().getCodigo(),
                                    auto.getUsuario());
                            jaBloqueados.add(parcela.getPessoa().getCodigo());
                        }
                    }
                    montarMsgAlertID("msg_bloqueios_lancado");
                    //para desbloqueio em massa
                } else if (emMassa && !bloqueio) {
                    for (MovParcelaVO parcela : agrupamentoParcelas.getParcelasTrocarCartao()) {
                        if (parcela.getParcelaEscolhida() && parcela.isAcessoBloqueado()) {
                            getFacade().getClienteMensagem().lancarDesbloqueioCatraca(parcela.getPessoa().getCodigo());
                        }
                    }
                    montarMsgAlertID("msg_desbloqueios_lancado");
                } else {
                    if (movParcela == null) {
                        montarMsgAlertID("msg_tente_outra_vez");
                        //para bloqueio simples
                    } else if (bloqueio) {
                        getFacade().getClienteMensagem().lancarBloqueioCatraca(getMensagemInternalizacao("TROCAR_CARTAO"), movParcela.getPessoa().getCodigo(), auto.getUsuario());
                        montarMsgAlertID("msg_bloqueio_lancado");

                        //para desbloqueio simples
                    } else {
                        getFacade().getClienteMensagem().lancarDesbloqueioCatraca(movParcela.getPessoa().getCodigo());
                        montarMsgAlertID("msg_desbloqueio_lancado");
                    }
                }
                getFacade().getClienteMensagem().verificarBloqueados(agrupamentoParcelas.getParcelasTrocarCartao());
                marcarTodosTrocarCartao = false;
                marcarDesmarcarTrocarCartao();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarMsgAlert(getMensagemDetalhada());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        movParcela = emMassa ? null : (MovParcelaVO) context().getExternalContext().getRequestMap().get("parc");
        auto.autorizar("2.31 - Lançar ou excluir mensagem para a catraca", "LancarMensagemCatraca",
                "2.31 - Lançar ou excluir mensagem para a catraca",
                "panelConteudo", listener);
    }

    private void verificarSelecionados(List<MovParcelaVO> lista) throws Exception {
        for (MovParcelaVO parcela : lista) {
            if (parcela.getParcelaEscolhida()) {
                return;
            }
        }
        throw new Exception("msg_finan_nenhum_item_selecionado");
    }

    private void marcarDesmarcarTodos(List<MovParcelaVO> lista, boolean marcar) {
        for (MovParcelaVO parcela : lista) {
            parcela.setParcelaEscolhida(marcar);
        }
    }

    public void marcarDesmarcarOutros() {
        marcarDesmarcarTodos(agrupamentoParcelas.getParcelasOutros(), marcarTodosOutros);
        totalizar();
    }

    public void marcarDesmarcarReenvio() {
        marcarDesmarcarTodos(agrupamentoParcelas.getParcelasReenvio(), marcarTodosReenvio);
        totalizar();
    }

    public void marcarDesmarcarContato() {
        marcarDesmarcarTodos(agrupamentoParcelas.getParcelasContato(), marcarTodosContato);
        totalizar();
    }

    public void marcarDesmarcarCielo() {
        marcarDesmarcarTodos(agrupamentoParcelas.getParcelasContatoCielo(), marcarTodosCielo);
        totalizar();
    }

    public void marcarDesmarcarTrocarCartao() {
        marcarDesmarcarTodos(agrupamentoParcelas.getParcelasTrocarCartao(), marcarTodosTrocarCartao);
        totalizar();
    }

    public void marcarDesmarcarSemAcao() {
        marcarDesmarcarTodos(agrupamentoParcelas.getParcelasSemAcao(), marcarTodosSemAcao);
        totalizar();
    }

    public void lancarBloqueios() {
        try {
            verificarSelecionados(agrupamentoParcelas.getParcelasTrocarCartao());
            gerenciarAcesso(true, true, "lancarBloqueios");
        } catch (Exception ex) {
            montarMsgAlertID(ex.getMessage());
        }

    }

    public void lancarBloqueio() {
        gerenciarAcesso(false, true, "lancarBloqueio");
    }

    public void lancarDesbloqueios() {
        try {
            verificarSelecionados(agrupamentoParcelas.getParcelasTrocarCartao());
            gerenciarAcesso(true, false, "lancarDesbloqueios");
        } catch (Exception ex) {
            montarMsgAlertID(ex.getMessage());
        }
    }

    public void lancarDesbloqueio() {
        gerenciarAcesso(false, false, "lancarDesbloqueio");
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

    public boolean isMarcarTodosCielo() {
        return marcarTodosCielo;
    }

    public void setMarcarTodosCielo(boolean marcarTodosCielo) {
        this.marcarTodosCielo = marcarTodosCielo;
    }

    public boolean isMarcarTodosContato() {
        return marcarTodosContato;
    }

    public void setMarcarTodosContato(boolean marcarTodosContato) {
        this.marcarTodosContato = marcarTodosContato;
    }

    public boolean isMarcarTodosOutros() {
        return marcarTodosOutros;
    }

    public void setMarcarTodosOutros(boolean marcarTodosOutros) {
        this.marcarTodosOutros = marcarTodosOutros;
    }

    public boolean isMarcarTodosReenvio() {
        return marcarTodosReenvio;
    }

    public void setMarcarTodosReenvio(boolean marcarTodosReenvio) {
        this.marcarTodosReenvio = marcarTodosReenvio;
    }

    public boolean isMarcarTodosSemAcao() {
        return marcarTodosSemAcao;
    }

    public void setMarcarTodosSemAcao(boolean marcarTodosSemAcao) {
        this.marcarTodosSemAcao = marcarTodosSemAcao;
    }

    public boolean isMarcarTodosTrocarCartao() {
        return marcarTodosTrocarCartao;
    }

    public void setMarcarTodosTrocarCartao(boolean marcarTodosTrocarCartao) {
        this.marcarTodosTrocarCartao = marcarTodosTrocarCartao;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public Date getDataRetorno() {
        return dataRetorno;
    }

    public void setDataRetorno(Date dataRetorno) {
        this.dataRetorno = dataRetorno;
    }

    public String getNumeroEstabelecimento() {
        return numeroEstabelecimento;
    }

    public void setNumeroEstabelecimento(String numeroEstabelecimento) {
        this.numeroEstabelecimento = numeroEstabelecimento;
    }

    public String getNumeroCielo() {
        return numeroCielo;
    }

    public void setNumeroCielo(String numeroCielo) {
        this.numeroCielo = numeroCielo;
    }

    public String getDataEnvioApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataEnvio, "dd/MM/yyyy HH:mm");
    }

    public String getDataRetornoApresentar() {
        return Uteis.getDataAplicandoFormatacao(dataRetorno, "dd/MM/yyyy HH:mm");
    }

    public boolean isRemessasDiferentes() {
        return remessasDiferentes;
    }

    public void setRemessasDiferentes(boolean remessasDiferentes) {
        this.remessasDiferentes = remessasDiferentes;
    }

    public String getTextoValidacao() {
        return remessasDiferentes ? getMensagemInternalizacao("validar_remessas_diferentes") : "";
    }

    public Date getDataFimCobranca() {
        return dataFimCobranca;
    }

    public void setDataFimCobranca(Date dataFimCobranca) {
        this.dataFimCobranca = dataFimCobranca;
    }

    public Date getDataInicioCobranca() {
        return dataInicioCobranca;
    }

    public void setDataInicioCobranca(Date dataInicioCobranca) {
        this.dataInicioCobranca = dataInicioCobranca;
    }

    public int getCreditoDCC() {
        return creditoDCC;
    }

    public void setCreditoDCC(int creditoDCC) {
        this.creditoDCC = creditoDCC;
    }

    public String getSituacaoCreditoDCC() {
        if (UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
            return "Selecione uma empresa para verificar o saldo.";
        } else if (getCreditoDCC() >= RemessaService.PADRAO_CREDITO_DCC) {
            return "Seu saldo é de " + getCreditoDCC() + " crédito(s).";
        } else if (getCreditoDCC() >= RemessaService.INICIO_LIMITE_EMERGENCIAL_DCC) {
            return "Seu saldo está no fim! Restam apenas " + getCreditoDCC() + " crédito(s).";
        } else if (getCreditoDCC() > RemessaService.LIMITE_EMERGENCIAL_DCC) {
            return "O limite emergêncial de DCC foi ativado. Você já consumiu " + (-1 * getCreditoDCC()) + " de " + (-1 * RemessaService.LIMITE_EMERGENCIAL_DCC) + " crédito(s).";
        } else {
            return "Você não possui saldo DCC.";
        }
    }

    public String getStyleSituacaoCreditoDCC() {
        if (UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
            return "color: #474747;";
        } else if (getCreditoDCC() >= RemessaService.PADRAO_CREDITO_DCC) {
            return "color: green;";
        } else if (getCreditoDCC() >= RemessaService.INICIO_LIMITE_EMERGENCIAL_DCC) {
            return "color: olive;";
        } else if (getCreditoDCC() > RemessaService.LIMITE_EMERGENCIAL_DCC) {
            return "color: red;";
        } else {
            return "color: red;";
        }
    }

    public String getSituacaoDataCreditoDCC() {
        if (getDataExpiracaoCreditoDCC() == null) {
            return "";
        } else if (Calendario.maior(getDataExpiracaoCreditoDCC(), Calendario.hoje())) {
            return "Seus créditos serão bloqueados no dia " + Uteis.getData(getDataExpiracaoCreditoDCC()) + ".";
        } else {
            return "Seus créditos estão bloqueados.";
        }
    }

    public String getStyleSituacaoDataCreditoDCC() {
        if (getDataExpiracaoCreditoDCC() == null) {
            return "color: green;";
        } else if (Calendario.maior(getDataExpiracaoCreditoDCC(), Calendario.hoje())) {
            return "color: olive;";
        } else {
            return "color: red;";
        }
    }

    public boolean getItauNovo() {
        return convenio.getTipo().equals(TipoConvenioCobrancaEnum.ITAU);
    }
    public boolean getItau() {
        return !convenio.isLayoutFebrabanDCO() && ((itensProcessar != null && !itensProcessar.isEmpty()) || (mapaItemEmpresa != null && !mapaItemEmpresa.isEmpty()));
    }

    public List<RemessaItemVO> getItensProcessar() {
        return itensProcessar;
    }

    public void setItensProcessar(List<RemessaItemVO> itensProcessar) {
        this.itensProcessar = itensProcessar;
    }

    public RemessaVO getRemessaRetorno() {
        return remessaRetorno;
    }

    public void setRemessaRetorno(RemessaVO remessaRetorno) {
        this.remessaRetorno = remessaRetorno;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFiltrosDescricao(boolean remessas, String agrupamento) {
        String filtros = "";
        if (getConvenio() != null && !UteisValidacao.emptyNumber(getConvenio().getCodigo())) {
            try {
                ConvenioCobrancaVO conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(getConvenio().getCodigo(), getConvenio().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                filtros += " - " + conv.getDescricao();
            } catch (Exception ignored) {
            }
        }
        if (this.getDataInicio() != null && this.getDataFim() != null) {
            filtros += (remessas ? " - Período:" : " - Vencimento: ") + Uteis.getData(this.getDataInicio()) + "-"
                    + Uteis.getData(this.getDataFim());
        }
        if (remessas) {
            return filtros.replaceFirst(" - ", "");
        }
        if (this.getDataInicioCobranca() != null && this.getDataFimCobranca() != null) {
            filtros += " - Cobrança: " + Uteis.getData(this.getDataInicioCobranca()) + "-"
                    + Uteis.getData(this.getDataFimCobranca());
        }
        String[] tipos = tiposConsultaEscolhido;
        for (String name : tipos) {
            if (!UteisValidacao.emptyString(name)) {
                TipoConsultaParcelasEnum t = TipoConsultaParcelasEnum.valueOf(name);
                filtros += " - " + t.getDescricao();
            }
        }
        if (planoFiltro != null && !UteisValidacao.emptyString(planoFiltro.getDescricao())) {
            filtros += " - Plano: " + planoFiltro.getDescricao();
        }
        if (agrupamento != null) {
            filtros += " - " + agrupamento;
        }
        return filtros.replaceFirst(" - ", "");
    }

    public void exportarContato(ActionEvent evt) {
        montarCategorias(getAgrupamentoParcelas().getParcelasContato());
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getAgrupamentoParcelas().getParcelasContato(), "pessoa_Apresentar"), getFiltrosDescricao(false, "Contato com o cliente"), null);
    }

    public void exportarParcelasVencidas(ActionEvent evt) {
        try {
            limparMsg();
            setMsgAlert("");
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getManagedBean(ExportadorListaControle.class.getSimpleName());
            exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getMovParcelaTransacaoTO(), "nome"), getFiltrosDescricao(false, "Parcelas Vencidas em Aberto"), ItemExportacaoEnum.BI_DCC_PARCELASVENCIDAS_EMABERTO);
            if(exportadorListaControle.getErro()){
                throw new Exception(exportadorListaControle.getMensagemDetalhada());
            }
            String tipo = (String) JSFUtilities.getFromActionEvent("tipo", evt);
            setMsgAlert("abrirPopup('UpdateServlet?op=downloadfile&file="+exportadorListaControle.getFileName()+"&mimetype="+(tipo.equals("pdf")?"application/pdf":"application/vnd.ms-excel")+"','Transacoes', 800,200);");
        } catch (Exception e ){
            montarErro(e);
        }

    }

    public void exportarOutros(ActionEvent evt) {
        montarCategorias(getAgrupamentoParcelas().getParcelasOutros());
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getAgrupamentoParcelas().getParcelasOutros(), "pessoa_Apresentar"), getFiltrosDescricao(false, "Outros"),null );
    }

    public void exportarReenvio(ActionEvent evt) {
        montarCategorias(getAgrupamentoParcelas().getParcelasReenvio());
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getAgrupamentoParcelas().getParcelasReenvio(), "pessoa_Apresentar"), getFiltrosDescricao(false, "Realizar reenvio"), null);
    }

    public void exportarContatoCielo(ActionEvent evt) {
        montarCategorias(getAgrupamentoParcelas().getParcelasContatoCielo());
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getAgrupamentoParcelas().getParcelasContatoCielo(), "pessoa_Apresentar"), getFiltrosDescricao(false, "Contato cielo"), null);
    }

    public void exportarTrocar(ActionEvent evt) {
        montarCategorias(getAgrupamentoParcelas().getParcelasTrocarCartao());
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getAgrupamentoParcelas().getParcelasTrocarCartao(), "pessoa_Apresentar"), getFiltrosDescricao(false, "Trocar cartão"), null);
    }

    public void exportar(ActionEvent evt) {
        montarCategorias(getAgrupamentoParcelas().getParcelasSemAcao());
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        try {
            getFacade().getMovParcela().preencherCamposEspeciais(getAgrupamentoParcelas().getParcelasSemAcao());
        } catch (Exception e) {
            e.printStackTrace();
        }
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getAgrupamentoParcelas().getParcelasSemAcao(), "pessoa_Apresentar"), getFiltrosDescricao(false, null), null);
    }

    public void exportarItensNaoEncontrados(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, getRegistrosNaoEncontrados(), "Registros Não Encontrados", null);
    }

    public void exportarItensCancelar(ActionEvent evt) {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, Ordenacao.ordenarLista(getItensAutorizadosFiltrada(), "pessoa_Apresentar"), getFiltrosDescricao(false, "Itens Para Cancelar"), null);
    }

    public void exportarItensRemessa(ActionEvent evt) {
        try {
            limparMsg();
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            if (remessaVO.getListaItens().size() > 0) {
                List<RemessaItemVO> listaAux = new ArrayList<>();
                for (RemessaItemVO remessaItem : remessaVO.getListaItens()) {
                    for (RemessaItemMovParcelaVO remessaItemMovParcelaVO : remessaItem.getMovParcelas()) {
                        RemessaItemVO remessaItemAux = (RemessaItemVO) remessaItem.getClone(true);
                        remessaItemAux.setMovParcela(remessaItemMovParcelaVO.getMovParcelaVO());
                        listaAux.add(remessaItemAux);
                    }
                }
                exportadorListaControle.exportar(evt, listaAux, getFiltrosDescricao(false, "Itens da Remessa"), null);
            } else if (remessaVO.getListaItensCancelamento().size() > 0){
                List<RemessaCancelamentoItemVO> listaAuxCancelamento = new ArrayList<>();
                for (RemessaCancelamentoItemVO remessaCancelamentoItemVO : remessaVO.getListaItensCancelamento()) {
                    PessoaVO pessoaVO = remessaCancelamentoItemVO.getItemRemessaCancelar().getPessoa();
                    for (RemessaItemMovParcelaVO remessaItemMovParcelaVO : remessaCancelamentoItemVO.getItemRemessaCancelar().getMovParcelas()) {
                        remessaItemMovParcelaVO.getMovParcelaVO().setPessoa(pessoaVO);
                        RemessaCancelamentoItemVO remessaItemAux = (RemessaCancelamentoItemVO) remessaCancelamentoItemVO.getClone(true);
                        remessaItemAux.setMovParcela(remessaItemMovParcelaVO.getMovParcelaVO());
                        listaAuxCancelamento.add(remessaItemAux);
                    }
                }
                exportadorListaControle.exportar(evt, listaAuxCancelamento, getFiltrosDescricao(false, "Itens da Remessa"), null);
            }
        } catch (Exception e) {
            montarErro(e);
            e.printStackTrace();
        }
    }

    public void exportarItensRemessaBoleto(ActionEvent evt) {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, montarListaRelatorio(remessaVO.getListaItens()), getFiltrosDescricao(false, "Itens da Remessa"), null);
    }

    private List<ItemRemessaTO> montarListaRelatorio(List<RemessaItemVO> itens) {
        List<ItemRemessaTO> lista = new ArrayList<ItemRemessaTO>();
        int i = 1;
        // cada remessa sera transformado em um item para excel ou pdf
        for (RemessaItemVO item : itens) {
            // se remessa nao possui parcelas
            if (item.getMovParcelas().isEmpty()) {
                lista.add(criaItemBoleto(i, item));
            } else {
                // cada item da parcela completa as informacoes da linha
                for (RemessaItemMovParcelaVO itemAux : item.getMovParcelas()) {
                    ItemRemessaTO aux = criaItemBoleto(i, item);
                    aux.setCodigoParcela(itemAux.getMovParcelaVO().getContrato().getCodigo());
                    aux.setDescricaoParcela(itemAux.getMovParcelaVO().getDescricao());
                    aux.setSituacaoParcela(itemAux.getMovParcelaVO().getSituacao_Apresentar());
                    aux.setValorParcela(itemAux.getMovParcelaVO().getValorParcela());
                    lista.add(aux);
                }
            }
        }
        return lista;
    }

    private ItemRemessaTO criaItemBoleto(int i, RemessaItemVO item) {
        ItemRemessaTO aux = new ItemRemessaTO();
        aux.setSequencia(i);
        aux.setCodigo(item.getCodigo());
        aux.setIdentificador(item.getIdentificador());
        aux.setPessoa(item.getPessoa().getNome());
        aux.setStatus(item.getCodigoStatus());
        aux.setDataVencimento(item.getDataVencimentoBoleto());
        aux.setValorTitulo(item.getValorItemRemessa());
        aux.setCodigoPagamento(verificaPagamento(item.getMovPagamento().getCodigo(), item.isTodasParcelasPagas()));
        return aux;
    }

    private String verificaPagamento(int codigo, boolean pago) {
        return codigo == 0 && pago ? "Pgt. OUTRO" : codigo != 0 ? "Pgt. " + codigo : "";
    }

    public void montarCategorias(List<MovParcelaVO> parcelas) {
        try {
            getFacade().getCategoria().montarCategoriasParcelas(parcelas);
        } catch (Exception e) {
            Uteis.logar(e, null);
        }
    }

    public PlanoVO getPlanoFiltro() {
        if (planoFiltro == null) {
            planoFiltro = new PlanoVO();
        }
        return planoFiltro;
    }

    public void setPlanoFiltro(PlanoVO planoFiltro) {
        this.planoFiltro = planoFiltro;
    }

    @SuppressWarnings("unchecked")
    public List<PlanoVO> executarAutocompletePlano(Object suggest) throws Exception {
        List<PlanoVO> result = new ArrayList<PlanoVO>();
        try {
            String pref = (String) suggest;
            result = getFacade().getPlano().consultarPorDescricao(pref,
                    getEmpresaVO().getCodigo() == null ? 0 : getEmpresaVO().getCodigo(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception ex) {
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void limparPlano() throws Exception {
        setPlanoFiltro(new PlanoVO());

    }

    public void selecionarPlanoSuggestionBox() throws Exception {
        PlanoVO plano = (PlanoVO) request().getAttribute("result");
        if (plano != null) {
            setPlanoFiltro(plano);
        }
    }

    public Date getDataExpiracaoCreditoDCC() {
        return dataExpiracaoCreditoDCC;
    }

    public void setDataExpiracaoCreditoDCC(Date dataExpiracaoCreditoDCC) {
        this.dataExpiracaoCreditoDCC = dataExpiracaoCreditoDCC;
    }

    public void validarApresentacaoDaColunaRetornoManual() {
        setApresentarColunaRetornoManual(false);
        for (RemessaItemVO itemRemessa : itensRemessaEstorno) {
            if (itemRemessa.getRemessa().getTipo().equals(TipoRemessaEnum.ITAU_BOLETO)
                    || itemRemessa.getRemessa().getTipo().equals(TipoRemessaEnum.BOLETO)) {
                setApresentarColunaRetornoManual(true);
                break;
            }
        }
    }

    public void prepararRetornoManual(ActionEvent actionEvent) {
        RemessaItemVO item = (RemessaItemVO) JSFUtilities.getFromRequest("item");
        if (item != null) {
            setItemRetornar(item);
        }
    }

    public void retornarParcelaManualmente() {
        final AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setMensagemDetalhada("", "");
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {
            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                retornarItemManualmente(auto.getUsuario());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setMensagemID("msg_erro");
                setMensagemDetalhada(e);
                montarMsgAlert(getMensagemDetalhada());
            }

            @Override
            public String getExecutarAoCompletar() {
                return getOnComplete();
            }
        };
        setMsgAlert("");
        limparMsg();
        auto.autorizar("Autorização para retornar item manualmente", "RetornoManualItensItau",
                "Você precisa da permissão \"4.32 - Permissão para retornar um item, de forma manual, que está em remessa de boleto Itaú CNAB 400\"",
                "panelAutorizacaoFuncionalidade, panelMensagemErro, tblItensRemessa", listener);

    }

    private void retornarItemManualmente(UsuarioVO usuarioVO) {
        try {
            getItemRetornar().put(DCCAttEnum.StatusVenda.name(), BBCnab400ItauStatusEnum.Status9999.getId());
            getItemRetornar().put(DCCAttEnum.CodUsuarioRetorno.name(), usuarioVO.getCodigo().toString());
            getItemRetornar().put(DCCAttEnum.NomeUsuarioRetorno.name(), usuarioVO.getNome());
            getItemRetornar().put(DCCAttEnum.DataHoraRetorno.name(), Calendario.getData("dd/MM/yyyy"));

            getFacade().getRemessaItem().alterar(getItemRetornar());
        } catch (Exception ignored) {
        }
    }

    public boolean isApresentarColunaRetornoManual() {
        return apresentarColunaRetornoManual;
    }

    public void setApresentarColunaRetornoManual(boolean apresentarColunaRetornoManual) {
        this.apresentarColunaRetornoManual = apresentarColunaRetornoManual;
    }

    public RemessaItemVO getItemRetornar() {
        return itemRetornar;
    }

    public void setItemRetornar(RemessaItemVO itemRetornar) {
        this.itemRetornar = itemRetornar;
    }

    public void setRegistrosNaoEncontrados(List<ItemRetornoRemessaTO> registrosNaoEncontrados) {
        this.registrosNaoEncontrados = registrosNaoEncontrados;
    }

    public List<ItemRetornoRemessaTO> getRegistrosNaoEncontrados() {
        return registrosNaoEncontrados;
    }

    public boolean isExibirRemessaBoleto() {
        return exibirRemessaBoleto;
    }

    public void setExibirRemessaBoleto(boolean exibirRemessaBoleto) {
        this.exibirRemessaBoleto = exibirRemessaBoleto;
    }

    public Object selecionarConvenio() {
        try {

            String[] conv = getCodigoGenericoConvenio().split("\\|");
            Integer convenio = Integer.parseInt(conv[0]);
            Integer empresa = Integer.parseInt(conv[1]);

            if (!UteisValidacao.emptyNumber(convenio) && !UteisValidacao.emptyNumber(empresa)) {
                setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio, empresa, Uteis.NIVELMONTARDADOS_TODOS));
            } else if (!UteisValidacao.emptyNumber(convenio) && UteisValidacao.emptyNumber(empresa)) {
                setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(convenio, Uteis.NIVELMONTARDADOS_TODOS));
            } else {
                setConvenio(new ConvenioCobrancaVO());
            }

            if (!getConvenio().isBoleto()) {
                desagruparRemessas();
                if (getAbaSelecionada().equals(ABA_CANCELAMENTO)) {
                    setAbaSelecionada(ABA_REMESSA);
                }
            }

            if (getConvenio().isBoleto()) {
                montarListaModalidades();
            }
            agrupamentoParcelas = new AgrupamentoParcelasTO();
            agrupamentoParcelas.totalizar();
            itensProcessar = new ArrayList<RemessaItemVO>();
            remessaRetorno = new RemessaVO();
            retornoBoleto = new RemessaVO();
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
        }
        return "";
    }

    public boolean isConvenioTipoBoleto() {
        return getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)
                || getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)
                || getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE);
    }

    public List<RemessaItemVO> getItensRemessaBoleto() {
        return itensRemessaBoleto;
    }

    public void setItensRemessaBoleto(List<RemessaItemVO> itensRemessaBoleto) {
        this.itensRemessaBoleto = itensRemessaBoleto;
    }

    public RemessaVO getRetornoBoleto() {
        return retornoBoleto;
    }

    public void setRetornoBoleto(RemessaVO retornoBoleto) {
        this.retornoBoleto = retornoBoleto;
    }

    public Date getDataPrevistaCredito() {
        return dataPrevistaCredito;
    }

    public void setDataPrevistaCredito(Date dataPrevistaCredito) {
        this.dataPrevistaCredito = dataPrevistaCredito;
    }

    public String getDataPrevistaCredito_Apresentar() {
        if (getDataPrevistaCredito() == null) {
            return "Não informado";
        }
        return Uteis.getData(getDataPrevistaCredito());
    }

    public int getQuantidadeDeItens() {
        return quantidadeDeItens;
    }

    public void setQuantidadeDeItens(int quantidadeDeItens) {
        this.quantidadeDeItens = quantidadeDeItens;
    }

    public Double getValorTotalDoArquivo() {
        return valorTotalDoArquivo;
    }

    public String getValorTotalDoArquivo_Apresentar() {
        if (valorTotalDoArquivo == null) {
            valorTotalDoArquivo = 0.0;
        }
        return Formatador.formatarValorMonetario(valorTotalDoArquivo);
    }

    public void setValorTotalDoArquivo(Double valorTotalDoArquivo) {
        this.valorTotalDoArquivo = valorTotalDoArquivo;
    }

    public int getQuantidadeItensReconhecidos() {
        return quantidadeItensReconhecidos;
    }

    public void setQuantidadeItensReconhecidos(int quantidadeItensReconhecidos) {
        this.quantidadeItensReconhecidos = quantidadeItensReconhecidos;
    }

    public Double getValorTotalASerBaixado() {
        return valorTotalASerBaixado;
    }

    public void setValorTotalASerBaixado(Double valorTotalASerBaixado) {
        this.valorTotalASerBaixado = valorTotalASerBaixado;
    }

    public String getValorTotalASerBaixado_Apresentar() {
        if (valorTotalASerBaixado == null) {
            valorTotalASerBaixado = 0.0;
        }
        return Formatador.formatarValorMonetario(valorTotalASerBaixado);
    }

    public int getQuantidadeItensBaixaEmDuplicidade() {
        return quantidadeItensBaixaEmDuplicidade;
    }

    public void setQuantidadeItensBaixaEmDuplicidade(int quantidadeItensBaixaEmDuplicidade) {
        this.quantidadeItensBaixaEmDuplicidade = quantidadeItensBaixaEmDuplicidade;
    }

    public int getQuantidadeItensBaixaValorMenor() {
        return quantidadeItensBaixaValorMenor;
    }

    public void setQuantidadeItensBaixaValorMenor(int quantidadeItensBaixaValorMenor) {
        this.quantidadeItensBaixaValorMenor = quantidadeItensBaixaValorMenor;
    }

    public int getQuantidadeItensBaixaValorMaior() {
        return quantidadeItensBaixaValorMaior;
    }

    public void setQuantidadeItensBaixaValorMaior(int quantidadeItensBaixaValorMaior) {
        this.quantidadeItensBaixaValorMaior = quantidadeItensBaixaValorMaior;
    }

    public int getQuantidadeItensBaixaNormal() {
        return quantidadeItensBaixaNormal;
    }

    public void setQuantidadeItensBaixaNormal(int quantidadeItensBaixaNormal) {
        this.quantidadeItensBaixaNormal = quantidadeItensBaixaNormal;
    }

    public int getQuantidadeItensBaixaManual() {
        return quantidadeItensBaixaManual;
    }

    public void setQuantidadeItensBaixaManual(int quantidadeItensBaixaManual) {
        this.quantidadeItensBaixaManual = quantidadeItensBaixaManual;
    }

    public int getQuantidadeItensNaoReconhecido() {
        return quantidadeItensNaoReconhecido;
    }

    public void setQuantidadeItensNaoReconhecido(int quantidadeItensNaoReconhecido) {
        this.quantidadeItensNaoReconhecido = quantidadeItensNaoReconhecido;
    }

    public Double getValorTotalNaoReconhecido() {
        return valorTotalNaoReconhecido;
    }

    public void setValorTotalNaoReconhecido(Double valorTotalNaoReconhecido) {
        this.valorTotalNaoReconhecido = valorTotalNaoReconhecido;
    }

    public String getValorTotalNaoReconhecido_Apresentar() {
        if (valorTotalNaoReconhecido == null) {
            valorTotalNaoReconhecido = 0.0;
        }
        return Formatador.formatarValorMonetario(valorTotalNaoReconhecido);
    }

    public boolean isApresentarTabelaBoleto() {
        return getItensProcessar().size() < 500;
    }

    public RetornoRemessaVO getRetornoRemessaVO() {
        return retornoRemessaVO;
    }

    public void setRetornoRemessaVO(RetornoRemessaVO retornoRemessaVO) {
        this.retornoRemessaVO = retornoRemessaVO;
    }

    public boolean isApresentarItensRetornoBoleto() {
        return apresentarItensRetornoBoleto;
    }

    public void setApresentarItensRetornoBoleto(boolean apresentarItensRetornoBoleto) {
        this.apresentarItensRetornoBoleto = apresentarItensRetornoBoleto;
    }

    public boolean isApresentarBotaoProcessar() {
        return apresentarBotaoProcessar;
    }

    public void setApresentarBotaoProcessar(boolean apresentarBotaoProcessar) {
        this.apresentarBotaoProcessar = apresentarBotaoProcessar;
    }

    public Integer getNrPaginasItensRemessaEstorno() {
        return nrPaginasItensRemessaEstorno;
    }

    public void setNrPaginasItensRemessaEstorno(Integer nrPaginasItensRemessaEstorno) {
        this.nrPaginasItensRemessaEstorno = nrPaginasItensRemessaEstorno;
    }

    public Integer getNrPaginasItensRemessaBoleto() {
        return nrPaginasItensRemessaBoleto;
    }

    public void setNrPaginasItensRemessaBoleto(Integer nrPaginasItensRemessaBoleto) {
        this.nrPaginasItensRemessaBoleto = nrPaginasItensRemessaBoleto;
    }

    public String receberParcela() {
        MovParcelaControle mpc = (MovParcelaControle) getControlador(MovParcelaControle.class);
        MovParcelaVO mpvo = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parc");
        try {
            mpc.novo();
            mpvo = getFacade().getMovParcela().consultarPorChavePrimaria(mpvo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            mpc.setMovParcelaVO(mpvo);
            mpc.setEmpresa(mpvo.getEmpresa());
            mpc.setMovParcelaVO(mpvo);
            mpc.getListaParcelasPagar().add(mpvo);
            mpc.setValorTotalParcela(mpvo.getValorParcela());
            if (mpc.validarBasico()) {
                MovPagamentoControle mpgtoc = (MovPagamentoControle) getControlador(MovPagamentoControle.class);
                mpgtoc.novo();
                mpgtoc.inicializarListasSelectItemTodosComboBox(false);
                return "pagamento";
            } else {
                throw new ConsistirException("Não foi possível inicializar tela de pagamento");
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";

    }

    public boolean isEnvioAutomatico() {
        return envioAutomatico;
    }

    public void setEnvioAutomatico(boolean envioAutomatico) {
        this.envioAutomatico = envioAutomatico;
    }

    public boolean isExigirValidacao() {
        return exigirValidacao;
    }

    public void setExigirValidacao(boolean exigirValidacao) {
        this.exigirValidacao = exigirValidacao;
    }

    public String getSenhaDownloadRemessa() {
        return senhaDownloadRemessa;
    }

    public void setSenhaDownloadRemessa(String senhaDownloadRemessa) {
        this.senhaDownloadRemessa = senhaDownloadRemessa;
    }

    public boolean isBaixarRemessa() {
        return baixarRemessa;
    }

    public void setBaixarRemessa(boolean baixarRemessa) {
        this.baixarRemessa = baixarRemessa;
    }

    public void validarSenhaDownload() throws Exception {
        if (Uteis.encriptar(getSenhaDownloadRemessa().toUpperCase()).equals("59d1e5c9b93e7d3b78d40392e18220c50416e4842f3cf97a3ecf790beaa9ad2a")) {
            setExigirValidacao(false);
            if (isBaixarRemessa()) {
                downloadRemessa(null);
            } else {
                downloadRetorno(null);
            }
        } else {
            setMsgAlertAuxiliar("Richfaces.showModalPanel('modalConfirmarDownload');");
            setMensagemDetalhada("senha incorreta!");
        }
    }

    public String getCriptogrado() {
        return criptogrado;
    }

    public void setCriptogrado(String criptogrado) {
        this.criptogrado = criptogrado;
    }

    public void cancelarDownload() {
        setRemessaVO(null);
    }

    public int getQuantidadeAutorizacoes() {
        return quantidadeAutorizacoes;
    }

    public void setQuantidadeAutorizacoes(int quantidadeAutorizacoes) {
        this.quantidadeAutorizacoes = quantidadeAutorizacoes;
    }

    public int getQuantidadeConfirmacoes() {
        return quantidadeConfirmacoes;
    }

    public void setQuantidadeConfirmacoes(int quantidadeConfirmacoes) {
        this.quantidadeConfirmacoes = quantidadeConfirmacoes;
    }

    public boolean isApresentarBotaoProcessarRetorno() {
        return (!getItau() && !isConvenioTipoBoleto() && getConvenio().isLayoutFebrabanDCO() && (getQuantidadeAutorizacoes() > 0 || getQuantidadeItensReconhecidos() > 0));
    }

    public boolean isImpressao() {
        return impressao;
    }

    public void setImpressao(boolean impressao) {
        this.impressao = impressao;
    }

    public String getOnCompleteDetalhes() {
        return onCompleteDetalhes;
    }

    public void setOnCompleteDetalhes(String onCompleteDetalhes) {
        this.onCompleteDetalhes = onCompleteDetalhes;
    }

    public void abrirTelaClienteColaborador(ActionEvent evt) throws Exception {
        ClienteControle cliControle = (ClienteControle) getControlador(ClienteControle.class);
        ColaboradorControle colaboradorControle = (ColaboradorControle) getControlador(ColaboradorControle.class);

        PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get("pessoa");
        Boolean impressao = Boolean.valueOf((String) evt.getComponent().getAttributes().get("impressao"));

        setImpressao(impressao);

        if (pessoa != null) {
            ClienteVO c = getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (c.getCodigo() != 0) {
                cliControle.setarCliente(c);
                cliControle.acaoAjax();
                if (impressao) {
                    setOnCompleteDetalhes("abrirPopup('../../clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);");
                } else {
                    setOnCompleteDetalhes("abrirPopup('clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700)");
                }
            } else {
                ColaboradorVO col = getFacade().getColaborador().consultarPorCodigoPessoa(pessoa.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                colaboradorControle.prepararEditarColaborador(col);
                if (impressao) {
                    setOnCompleteDetalhes("abrirPopup('../../colaboradorForm.jsp', 'ColaboradorGestaoRemessa', 1024, 700);");
                } else {
                    setOnCompleteDetalhes("abrirPopup('colaboradorForm.jsp', 'ColaboradorGestaoRemessa', 1024, 700)");
                }
            }
        }
    }

    public String getTotalizadorAcoes() {
        int total = getListaAcoes().size();
        int qtdRepescagem = 0;
        for (AcoesStatusRemessaVO acao : getListaAcoes()) {
            if (acao.getAcao().equals(AcoesRemessasEnum.REALIZAR_REENVIO) && acao.getReagendarAutomaticamente()) {
                qtdRepescagem++;
            }
        }
        return "Total: " + total + " - Repescagem: " + qtdRepescagem;
    }

    public List<RemessaCancelamentoItemVO> getItensAutorizados() {
        if (itensAutorizados == null) {
            itensAutorizados = new ArrayList<RemessaCancelamentoItemVO>();
        }
        return itensAutorizados;
    }

    public void setItensAutorizados(List<RemessaCancelamentoItemVO> itensAutorizados) {
        this.itensAutorizados = itensAutorizados;
    }

    public boolean isApresentarAbaCancelamento() {
        return getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC) ||
                getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET);
    }

    public List<RemessaCancelamentoItemVO> getRemessaCancelamentoItensApresentar() {
        if (remessaCancelamentoItensApresentar == null) {
            remessaCancelamentoItensApresentar = new ArrayList<RemessaCancelamentoItemVO>();
        }
        return remessaCancelamentoItensApresentar;
    }

    public void setRemessaCancelamentoItensApresentar(List<RemessaCancelamentoItemVO> remessaCancelamentoItensApresentar) {
        this.remessaCancelamentoItensApresentar = remessaCancelamentoItensApresentar;
    }

    public boolean isAbaParcela() {
        return getAbaSelecionada().equals("abaParcelas") && isGerarRemessa();
    }

    public boolean isAbaRemessa() {
        return getAbaSelecionada().equals(ABA_REMESSA);
    }

    public Boolean getAbaParcelaNaoBoleto() {
        return isAbaParcela() && !isConvenioTipoBoleto();
    }

    public Boolean getAbaParcelaBoleto() {
        return isAbaParcela() && isConvenioTipoBoleto();
    }

    public Boolean getApresentarPainelRetornoRemessa() {
        return getConvenio().getCodigo() != 0 && isProcessarRetorno();
    }

    public String getTextoFieldsetDataConsulta() {
        if (getAbaSelecionada().equals(ABA_REMESSA)) {
            return "Data de Geração da Remessa";
        } else if (getAbaSelecionada().equals("abaParcelas")) {
            return "Vencimento Parcelas";
        } else if (getAbaSelecionada().equals(ABA_CANCELAMENTO)) {
            return "Data de Pagamento das Parcelas";
        } else {
            return "";
        }
    }

    public List<AgrupadorRemessaTO> getAgrupamentoRemessas() {
        if (agrupamentoRemessas == null) {
            agrupamentoRemessas = new ArrayList<AgrupadorRemessaTO>();
        }
        return agrupamentoRemessas;
    }

    public void setAgrupamentoRemessas(List<AgrupadorRemessaTO> agrupamentoRemessas) {
        this.agrupamentoRemessas = agrupamentoRemessas;
    }

    public boolean isAgruparRemessas() {
        return agruparRemessas;
    }

    public void setAgruparRemessas(boolean agruparRemessas) {
        this.agruparRemessas = agruparRemessas;
    }

    public void downloadAgrupadorRemessa(ActionEvent evt) {
        limparMsg();

        setMensagemDetalhada("", "");

        AgrupadorRemessaTO agrupadorRemessaTO = (AgrupadorRemessaTO) evt.getComponent().getAttributes().get("agrupamento");

        setBaixarRemessa(true);
        setExibirRemessa(false);
        setExibirRemessaNovo(false);
        setExibirRemessaBoleto(false);

        List<String> arquivosZipar = new ArrayList<String>();
        try {
            for (RemessaVO remessa : agrupadorRemessaTO.getRemessas()) {
                remessa.setConvenioCobranca(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(remessa.getConvenioCobranca().getCodigo(), remessa.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS));

                if (!remessa.getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO) &&
                        !remessa.getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) &&
                        !Uteis.isAmbienteDesenvolvimentoTeste()) {
                    throw new Exception("Download indisponível. Entre em contato com a Pacto Soluções.");
                }

                List<RemessaItemVO> lista = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<RemessaCancelamentoItemVO> listaCancelamento = getFacade().getZWFacade().getRemessaCancelamentoItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO) &&
                        remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_CLUBE)) {
                    for (RemessaItemVO ri : lista) {
                        ri.setMesesAbertos(getFacade().getRemessaItem().consultarMesesEmAberto(ri));
                    }
                    Ordenacao.ordenarLista(lista, "cepSacado");
                }

                remessa.setListaItens(lista);
                remessa.setListaItensCancelamento(listaCancelamento);

                if (remessa.isCancelamento()) {
                    LayoutRemessaBase.preencherArquivoRemessaCancelamento(remessa);
                } else {
                    LayoutRemessaBase.preencherArquivoRemessa(remessa);
                }

                StringBuilder sb;
                if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) {
                    sb = LayoutRemessaItauDCO.prepareFile(remessa);
                } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                    obterIdentificadorEmpresaFinanceiro(true);
                    sb = LayoutBoletoPadrao.prepareFile(remessa, getIdentificadorEmpresaFinaceiro());
                } else if (remessa.getConvenioCobranca().isLayoutFebrabanDCO()) {
                    if (getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BB)) {
                        sb = LayoutRemessaBBDCO.prepareFile(remessa);
                    } else {
                        sb = LayoutRemessaFebrabanDCO.prepareFile(remessa);
                    }
                } else {
                    sb = LayoutRemessaCieloDCC.prepareFile(remessa);
                }
                String path = this.getServletContext().getRealPath("relatorio") + File.separator + remessa.getNomeArquivoDownload();
                if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                        && getCriptogrado() != null && getCriptogrado().equals("true")) {
                    PgpEncryption.salvarCriptografado(sb.toString(), path, remessa.getConvenioCobranca().getChaveGETNET());
                } else {
                    StringUtilities.saveToFile(sb, path);
                }
                arquivosZipar.add(remessa.getNomeArquivoDownload());
            }

            List<byte[]> arquivos = new ArrayList<byte[]>();
            for (String arquivo : arquivosZipar) {
                File file = new File(this.getServletContext().getRealPath("relatorio") + File.separator + arquivo);
                byte[] b = IOUtils.toByteArray(new FileInputStream(file));
                if (b.length > 0) {
                    arquivos.add(b);
                }
            }

            if (arquivos.size() > 0) {
                String absolutePath = this.getServletContext().getRealPath("relatorio") + File.separator;
                String nomeArquivo = getKey() + System.currentTimeMillis() + ".zip";

                String nomeArquivoGerado = absolutePath + nomeArquivo;
                Uteis.zip(nomeArquivoGerado, arquivos, arquivosZipar);
                setMsgAlertAuxiliar("Richfaces.hideModalPanel('modalConfirmarDownload');abrirPopup('UpdateServlet?op=downloadfile&file=" + nomeArquivo + "&mimetype=application/zip','Remessa', 640,480);");
            } else {
                throw new ConsistirException("Não foi selecionado nenhuma nota.");
            }
        } catch (Exception ex) {
            setExigirValidacao(true);
            setMensagemDetalhada(ex);
        }
    }

    private void obterIdentificadorEmpresaFinanceiro(boolean validarPrenchimento) throws Exception {
        OAMDService oamdService;
        String identificadorEmpresaFin = "";
        try {
            oamdService = new OAMDService();
            EmpresaFinanceiroVO empFin = oamdService.consultarEmpresaFinanceiro((String) JSFUtilities.getFromSession("key"));
            if (empFin != null) {
                if (UteisValidacao.emptyNumber(empFin.getIdentificadorRemessa()) && validarPrenchimento) {
                    throw new ConsistirException("Empresa faz parte de uma rede de empresa e não tem um identificador configurado. É necessário fazer essa configuração");
                }
                oamdService = new OAMDService();
                empresaFinanceiroVOs = oamdService.consultarUnidadesDaRede((String) JSFUtilities.getFromSession("key"));
                if (!UteisValidacao.emptyNumber(empFin.getIdentificadorRemessa())) {
                    identificadorEmpresaFin = StringUtilities.formatarCampo(new BigDecimal(empFin.getIdentificadorRemessa()), 3);
                }
                empresaFinaceiro = empFin;
            }
        } catch (Exception ex) {
            Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        oamdService = null;
        setIdentificadorEmpresaFinaceiro(identificadorEmpresaFin);
    }

    public String getIdentificadorEmpresaFinaceiro() {
        return identificadorEmpresaFinaceiro;
    }

    public void setIdentificadorEmpresaFinaceiro(String identificadorEmpresaFinaceiro) {
        this.identificadorEmpresaFinaceiro = identificadorEmpresaFinaceiro;
    }

    public List<EmpresaFinanceiroVO> getEmpresaFinanceiroVOs() {
        return empresaFinanceiroVOs;
    }

    public void setEmpresaFinanceiroVOs(List<EmpresaFinanceiroVO> empresaFinanceiroVOs) {
        this.empresaFinanceiroVOs = empresaFinanceiroVOs;
    }

    public List<GenericoTO> getItensOutrasEmpresa() {
        return itensOutrasEmpresa;
    }

    public void setItensOutrasEmpresa(List<GenericoTO> itensOutrasEmpresa) {
        this.itensOutrasEmpresa = itensOutrasEmpresa;
    }

    public Map<EmpresaFinanceiroVO, String> getMapaItemEmpresa() {
        return mapaItemEmpresa;
    }

    public void setMapaItemEmpresa(Map<EmpresaFinanceiroVO, String> mapaItemEmpresa) {
        this.mapaItemEmpresa = mapaItemEmpresa;
    }

    private String doProcessamentoRetornoServlet(Map<EmpresaFinanceiroVO, String> empresasItens, final String nomeArquivo, final String arquivo, ConvenioCobrancaVO convenio) throws Exception {
        StringBuffer retorno = new StringBuffer();
        String host = request().getRequestURL().substring(0, request().getRequestURL().indexOf("/faces")).replace("https:", "http");
        ExecuteRequestHttpService serviceRequest = new ExecuteRequestHttpService();
        serviceRequest.connectTimeout = 5000;
        serviceRequest.readTimeout = 500000;
        for (Map.Entry<EmpresaFinanceiroVO, String> pairEmp : mapaItemEmpresa.entrySet()) {
            if (pairEmp.getKey().getCodigo().equals(empresaFinaceiro.getCodigo()) || pairEmp.getKey().getCodigo().equals(0)) {
                continue;
            }

            Map<String, String> p = new HashMap<String, String>();
            p.put("op", "retornoBoletoRede");
            p.put("chave", pairEmp.getKey().getChaveZW());
            p.put("identificador", StringUtilities.formatarCampo(new BigDecimal(pairEmp.getKey().getIdentificadorRemessa()), 3));
            p.put("nomeArquivo", nomeArquivo);
            p.put("arquivo", arquivo);
            p.put("codigoItens", pairEmp.getValue());
            p.put("empresaProcessada", getEmpresaLogado().getNome());
            p.put("Usuario", getUsuarioLogado().getUsername());
            p.put("tipoConvenio", convenio.getTipo().toString());
            p.put("bancoConvenio", convenio.getBanco().getCodigoBanco().toString());
            String result = "";
            try {
                result = serviceRequest.executeRequestInner(host + "/RemessaServlet", p);

            } catch (Exception ex) {
                Logger.getLogger(GestaoRemessasControle.class.getName()).log(Level.SEVERE, null, ex);
                result = ex.getMessage();
            }
            for (GenericoTO generico : getItensOutrasEmpresa()) {
                if (pairEmp.getKey().getCodigo() == generico.getCodigo()) {
                    if (result.contains("sucesso")) {
                        generico.setLabel("itens processados com sucesso\n");
                        generico.setSelecionado(true);
                    } else {
                        generico.setLabel(result);
                        generico.setSelecionado(false);
                    }
                    break;
                }

            }

        }
        return retorno.toString();
    }

    public boolean isMostrarParcelasComBoleto() {
        return mostrarParcelasComBoleto;
    }

    public void setMostrarParcelasComBoleto(boolean mostrarParcelasComBoleto) {
        this.mostrarParcelasComBoleto = mostrarParcelasComBoleto;
    }

    public void limparInformacoesPanelRetorno() {
        limparMsg();
        limparInformacoesRetornoBoleto();
        remessaVO = new RemessaVO();
        setMensagemDetalhada("Arquivo com tipo não aceito. Arquivos de retorno só podem ser do tipo \".ret\", \".txt\", \".cmp\" ou \"crt\".");
    }

    public void confirmarFecharRemessa(ActionEvent evt) {
        RemessaVO remessa = (RemessaVO) evt.getComponent().getAttributes().get("remessa");
        setRemessaVO(remessa);

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        control.setMensagemApresentar("");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Fechar Remessa", "Deseja realmente fechar a remessa?", this,
                "fecharRemessa", null, null, null,
                "tblRemessas,mdlMensagemGenerica");
    }

    public void fecharRemessa() throws Exception {
        try {
            getRemessaVO().registrarObjetoVOAntesDaAlteracao();
            getRemessaVO().setNovoObj(false);

            getRemessaVO().setUsuarioFechamento(getUsuarioLogado());
            getRemessaVO().setDataFechamento(Calendario.hoje());
            getFacade().getRemessa().fecharRemessa(getRemessaVO());

            registrarLogObjetoVO(getRemessaVO(), getRemessaVO().getCodigo(), "REMESSA", 0);
        } catch (Exception ex) {
            registrarLogErroObjetoVO("REMESSA", getRemessaVO().getCodigo(), "ERRO AO GERAR LOG DE FECHAMENTO DE REMESSA", getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
        }
    }

    public List<String> getListaDescricaoParcelasAutorizadas() {
        if (listaDescricaoParcelasAutorizadas == null) {
            listaDescricaoParcelasAutorizadas = new ArrayList<String>();
        }
        return listaDescricaoParcelasAutorizadas;
    }

    public void setListaDescricaoParcelasAutorizadas(List<String> listaDescricaoParcelasAutorizadas) {
        this.listaDescricaoParcelasAutorizadas = listaDescricaoParcelasAutorizadas;
    }

    public String getFiltroParcelaAutorizada() {
        if (filtroParcelaAutorizada == null) {
            filtroParcelaAutorizada = "TODOS";
        }
        return filtroParcelaAutorizada;
    }

    public void setFiltroParcelaAutorizada(String filtroParcelaAutorizada) {
        this.filtroParcelaAutorizada = filtroParcelaAutorizada;
    }

    public List<RemessaCancelamentoItemVO> getItensAutorizadosFiltrada() {
        if (itensAutorizadosFiltrada == null) {
            itensAutorizadosFiltrada = new ArrayList<RemessaCancelamentoItemVO>();
        }
        return itensAutorizadosFiltrada;
    }

    public void setItensAutorizadosFiltrada(List<RemessaCancelamentoItemVO> itensAutorizadosFiltrada) {
        this.itensAutorizadosFiltrada = itensAutorizadosFiltrada;
    }

    public ClienteVO getClienteVOFiltrar() {
        if (clienteVOFiltrar == null) {
            clienteVOFiltrar = new ClienteVO();
        }
        return clienteVOFiltrar;
    }

    public void setClienteVOFiltrar(ClienteVO clienteVOFiltrar) {
        this.clienteVOFiltrar = clienteVOFiltrar;
    }

    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().
                        consultarTodosClienteComLimite(convenio.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().
                        consultarPorNomeClienteComLimite(convenio.getEmpresa().getCodigo(), pref, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void limparAluno() throws Exception {
        setClienteVOFiltrar(new ClienteVO());
        filtrarRemessasParaCancelar();
    }

    private void montarListaTipoParcela() {
        Map<String, String> map = new HashMap<String, String>();
        for (RemessaCancelamentoItemVO remessaCancelamentoItemVO : getItensAutorizados()) {
            map.put(remessaCancelamentoItemVO.getItemRemessaCancelar().getMovParcela().getDescricao(), remessaCancelamentoItemVO.getItemRemessaCancelar().getMovParcela().getDescricao());
        }
        setListaDescricaoParcelasAutorizadas(new ArrayList<String>());
        getListaDescricaoParcelasAutorizadas().addAll(map.values());
    }

    public List<SelectItem> getSelectItemDescricaoParcela() {
        List<SelectItem> listaRetornar = new ArrayList<SelectItem>();
        for (String desc : getListaDescricaoParcelasAutorizadas()) {
            listaRetornar.add(new SelectItem(desc, desc));
        }
        Ordenacao.ordenarLista(listaRetornar, "label");
        listaRetornar.add(0, new SelectItem("TODOS", "TODOS"));
        return listaRetornar;
    }

    public void filtrarPorDescricaoParcela() throws Exception {
        if (!getFiltroParcelaAutorizada().equals("TODOS")) {
            List<RemessaCancelamentoItemVO> listaFiltrada = new ArrayList<RemessaCancelamentoItemVO>();
            for (RemessaCancelamentoItemVO reme : getItensAutorizados()) {
                if (getFiltroParcelaAutorizada().toUpperCase().equals(reme.getItemRemessaCancelar().getMovParcela().getDescricao().toUpperCase())) {
                    listaFiltrada.add(reme);
                }
            }
            setItensAutorizadosFiltrada(listaFiltrada);
        } else {
            setItensAutorizadosFiltrada(getItensAutorizados());
        }
    }

    public void filtrarPorCliente() throws Exception {
        if (!UteisValidacao.emptyNumber(getClienteVOFiltrar().getCodigo())) {
            List<RemessaCancelamentoItemVO> listaFiltrada = new ArrayList<RemessaCancelamentoItemVO>();
            for (RemessaCancelamentoItemVO reme : getItensAutorizadosFiltrada()) {
                if (reme.getItemRemessaCancelar().getMovParcela().getPessoa_Apresentar().toUpperCase().startsWith(getClienteVOFiltrar().getNome_Apresentar().toUpperCase())) {
                    listaFiltrada.add(reme);
                }
            }
            setItensAutorizadosFiltrada(listaFiltrada);
        } else {
            setItensAutorizadosFiltrada(getItensAutorizadosFiltrada());
        }
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("result");
        setClienteVOFiltrar(clienteVO);
        filtrarRemessasParaCancelar();
    }

    public void filtrarRemessasParaCancelar() throws Exception {
        if (getFiltroParcelaAutorizada().equals("TODOS")) {
            setItensAutorizadosFiltrada(getItensAutorizados());
        } else {
            filtrarPorDescricaoParcela();
        }

        if (!UteisValidacao.emptyNumber(getClienteVOFiltrar().getCodigo())) {
            filtrarPorCliente();
        }
    }

    public int getQuantidadeItensRegistrados() {
        return quantidadeItensRegistrados;
    }

    public void setQuantidadeItensRegistrados(int quantidadeItensRegistrados) {
        this.quantidadeItensRegistrados = quantidadeItensRegistrados;
    }

    public List<MovParcelaVO> getListaRemessaComErro() {
        return listaRemessaComErro;
    }

    public void setListaRemessaComErro(List<MovParcelaVO> listaRemessaComErro) {
        this.listaRemessaComErro = listaRemessaComErro;
    }

    public TipoCobrancaPactoEnum getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    public void setTipoCobrancaPacto(TipoCobrancaPactoEnum tipoCobrancaPacto) {
        this.tipoCobrancaPacto = tipoCobrancaPacto;
    }

    public List<SelectItem> getListaModalidades() {
        return listaModalidades;
    }

    public void setListaModalidades(List<SelectItem> listaModalidades) {
        this.listaModalidades = listaModalidades;
    }

    public ModalidadeVO getModalidade() {
        if (modalidade == null) {
            modalidade = new ModalidadeVO();
        }
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            List<SelectItem> objs = new ArrayList<SelectItem>();
            objs.add(new SelectItem("TD", "TODAS"));
            List<EmpresaVO> empresasCadastradas = getFacade().getEmpresa().consultarPorCodigo(0, true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Ordenacao.ordenarLista(empresasCadastradas, "nome");
            for (EmpresaVO obj : empresasCadastradas) {
                objs.add(new SelectItem(obj.getCodigo().toString(), obj.getNome()));
            }
            setListaSelectItemEmpresa(objs);
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList<SelectItem>());
            setMensagemDetalhada(e.getMessage());
        }
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList<SelectItem>();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public String getEmpresaSelecionada() {
        if (empresaSelecionada == null) {
            empresaSelecionada = "";
        }
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(String empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isBuscarTodasEmpresas() {
        return buscarTodasEmpresas;
    }

    public void setBuscarTodasEmpresas(boolean buscarTodasEmpresas) {
        this.buscarTodasEmpresas = buscarTodasEmpresas;
    }

    public List<ItemGenericoTO> getListaStatus() {
        if (listaStatus == null) {
            listaStatus = new ArrayList<ItemGenericoTO>();
        }
        return listaStatus;
    }

    public void setListaStatus(List<ItemGenericoTO> listaStatus) {
        this.listaStatus = listaStatus;
    }

    public List<MovParcelaVO> getListaParcelasGeral() {
        if (listaParcelasGeral == null) {
            listaParcelasGeral = new ArrayList<MovParcelaVO>();
        }
        return listaParcelasGeral;
    }

    public void setListaParcelasGeral(List<MovParcelaVO> listaParcelasGeral) {
        this.listaParcelasGeral = listaParcelasGeral;
    }

    public Boolean getApresentarFiltroStatus() {
        return !UteisValidacao.emptyList(getListaStatus());
    }

    public void processarFiltroStatusRemessa() {
        List<MovParcelaVO> lista = new ArrayList<>();
        boolean selecionou = false;
        for (MovParcelaVO movParcelaVO : getListaParcelasGeral()) {
            for (ItemGenericoTO genericoTO : getListaStatus()) {
                if (genericoTO.isSelecionado()) {
                    selecionou = true;
                }
                if (genericoTO.isSelecionado() && movParcelaVO.getItemRemessa().getCodigoStatus().equals(genericoTO.getCodigo())) {
                    lista.add(movParcelaVO);
                }
            }
        }

        if (!selecionou) {
            setListaParcelas(getListaParcelasGeral());
        } else {
            setListaParcelas(lista);
        }
        agruparParcelas();
    }

    public void processarFiltroStatus() {
        List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();
        boolean selecionou = false;
        for (MovParcelaVO movParcelaVO : getListaParcelasGeral()) {
            for (ItemGenericoTO genericoTO : getListaStatus()) {
                if (genericoTO.isSelecionado()) {
                    selecionou = true;
                }
                if (genericoTO.isSelecionado() && movParcelaVO.getItemRemessa().getCodigoStatus().equals(genericoTO.getCodigo())) {
                    lista.add(movParcelaVO);
                }
            }
        }

        if (!selecionou) {
            setListaParcelas(getListaParcelasGeral());
        } else {
            setListaParcelas(lista);
        }
        agruparParcelas();
    }

    private void montarFiltroStatus() {
        setListaStatus(new ArrayList<ItemGenericoTO>());
        Map<String, String> mapa = new HashMap<String, String>();
        for (MovParcelaVO movParcelaVO : getListaParcelasGeral()) {
            if (!mapa.containsKey(movParcelaVO.getItemRemessa().getCodigoStatus())) {
                mapa.put(movParcelaVO.getItemRemessa().getCodigoStatus(), movParcelaVO.getItemRemessa().getCodigoStatus());
                ItemGenericoTO novo = new ItemGenericoTO();
                novo.setCodigo(movParcelaVO.getItemRemessa().getCodigoStatus());
                novo.setHint(movParcelaVO.getItemRemessa().getDescricaoStatus());

                if (UteisValidacao.emptyString(movParcelaVO.getItemRemessa().getCodigoStatus())) {
                    novo.setDescricao("SEM STATUS");
                } else {
                    novo.setDescricao(movParcelaVO.getItemRemessa().getCodigoStatus());
                }
                getListaStatus().add(novo);
            }
        }
    }

    public boolean isConsultarInfoTodasEmpresas() {
        return consultarInfoTodasEmpresas;
    }

    public void setConsultarInfoTodasEmpresas(boolean consultarInfoTodasEmpresas) {
        this.consultarInfoTodasEmpresas = consultarInfoTodasEmpresas;
    }

    public List<ConvenioCobrancaVO> getListaConveniosCompleta() {
        if (listaConveniosCompleta == null) {
            listaConveniosCompleta = new ArrayList<ConvenioCobrancaVO>();
        }
        return listaConveniosCompleta;
    }

    public void setListaConveniosCompleta(List<ConvenioCobrancaVO> listaConveniosCompleta) {
        this.listaConveniosCompleta = listaConveniosCompleta;
    }

    public String getCodigoGenericoConvenio() {
        if (codigoGenericoConvenio == null) {
            codigoGenericoConvenio = "";
        }
        return codigoGenericoConvenio;
    }

    public void setCodigoGenericoConvenio(String codigoGenericoConvenio) {
        this.codigoGenericoConvenio = codigoGenericoConvenio;
    }

    public ProcessoRetornoRemessaTO getProcessoRetornoRemessaTO() {
        if (processoRetornoRemessaTO == null) {
            processoRetornoRemessaTO = new ProcessoRetornoRemessaTO();
        }
        return processoRetornoRemessaTO;
    }

    public void setProcessoRetornoRemessaTO(ProcessoRetornoRemessaTO processoRetornoRemessaTO) {
        this.processoRetornoRemessaTO = processoRetornoRemessaTO;
    }

    private List<Integer> obterListaEmpresaBaseadoFiltros() throws Exception {

        if (UteisValidacao.emptyNumber(getConvenio().getCodigo())) {
            throw new Exception("Selecione o convênio de cobrança");
        }

        List<Integer> retorno = new ArrayList<Integer>();
        if ((getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && getConvenio().isGerarArquivoUnico()) || isBuscarTodasEmpresas()) {
            List<Integer> convenios = Arrays.asList(getConvenio().getCodigo());
            retorno = getFacade().getConvenioCobrancaEmpresa().obterEmpresasConvenio(convenios);
        } else if (!UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())) {
            retorno.add(getEmpresaVO().getCodigo());
        } else {
            throw new Exception("Selecione uma empresa");
        }
        return retorno;
    }

    public void irParaTelaCliente() {
        MovParcelaTransacaoTO obj = (MovParcelaTransacaoTO) context().getExternalContext().getRequestMap().get("parc");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                if (UteisValidacao.emptyNumber(obj.getCodigoCliente())) {
                    return;
                }
                ClienteVO clienteVO = new ClienteVO();
                clienteVO.setCodigo(obj.getCodigoCliente());
                irParaTelaCliente(clienteVO);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List<MovParcelaTransacaoTO> getMovParcelaTransacaoTO() {
        return movParcelaTransacaoTO;
    }

    public void setMovParcelaTransacaoTO(List<MovParcelaTransacaoTO> movParcelaTransacaoTO) {
        this.movParcelaTransacaoTO = movParcelaTransacaoTO;
    }

    public boolean isPerfilAcessoBotaoErro99() {
        return perfilAcessoBotaoErro99;
    }

    public void setPerfilAcessoBotaoErro99(boolean perfilAcessoBotaoErro99) {
        this.perfilAcessoBotaoErro99 = perfilAcessoBotaoErro99;
    }

    public String getCodAutorizacao() {
        return codAutorizacao;
    }

    public void setCodAutorizacao(String codAutorizacao) {
        this.codAutorizacao = codAutorizacao;
    }

    public Integer getQtdItemDebito() {
        return qtdItemDebito;
    }

    public void setQtdItemDebito(Integer qtdItemDebito) {
        this.qtdItemDebito = qtdItemDebito;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public String getMsgErro() {
        return msgErro;
    }

    public void setMsgErro(String msgErro) {
        this.msgErro = msgErro;
    }

    public boolean isFiltroRemessasCancelamento() {
        return filtroRemessasCancelamento;
    }

    public void setFiltroRemessasCancelamento(boolean filtroRemessasCancelamento) {
        this.filtroRemessasCancelamento = filtroRemessasCancelamento;
    }

    public boolean isApresentarAlunosBloqueioCobranca() {
        return apresentarAlunosBloqueioCobranca;
    }

    public void setApresentarAlunosBloqueioCobranca(boolean apresentarAlunosBloqueioCobranca) {
        this.apresentarAlunosBloqueioCobranca = apresentarAlunosBloqueioCobranca;
    }

    public boolean isExibirRemessaNovo() {
        return exibirRemessaNovo;
    }

    public void setExibirRemessaNovo(boolean exibirRemessaNovo) {
        this.exibirRemessaNovo = exibirRemessaNovo;
    }

    public void selecionarParcela() {
        limparMsg();
        RemessaCancelamentoItemVO parcela = (RemessaCancelamentoItemVO) context().getExternalContext().getRequestMap().get("itemAutorizado");
        List<RemessaCancelamentoItemVO> parcelasMesmoPagamento = itensAutorizadosFiltrada
                .stream()
                .filter(p -> p.getItemRemessaCancelar().getMovPagamento().getCodigo().equals(parcela.getItemRemessaCancelar().getMovPagamento().getCodigo()))
                .collect(Collectors.toList());
        for (RemessaCancelamentoItemVO p : parcelasMesmoPagamento) {
            p.setSelecionado(parcela.getSelecionado());
        }
        if (parcela.getSelecionado() && parcelasMesmoPagamento.size() > 1) {
            montarInfo("Alerta", "Outras parcelas foram selecionadas por terem sido pagas na mesma transação.");
        }
    }

    public void enviarRemessaTestePactoPay(ActionEvent evt) {
        try {
            RemessaVO obj = (RemessaVO) evt.getComponent().getAttributes().get("remessa");

            obj = getFacade().getRemessa().consultarPorChavePrimaria(obj.getCodigo());
            obj.setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            obj.setListaItens(getFacade().getRemessaItem().consultarPorCodigoRemessa(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            obj.setIdPactoPay(PactoPayService.enviarRemessa(obj, getKey(), obj.isCancelamento()));
            getFacade().getRemessa().alterarIdPactoPay(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public RemessaService getService() throws Exception {
        if (service == null) {
            service = new RemessaService();
            service.setKey((String) JSFUtilities.getFromSession("key"));
        }
        return service;
    }

    public boolean isInicializarEmpresa() {
        return inicializarEmpresa;
    }

    public void setInicializarEmpresa(boolean inicializarEmpresa) {
        this.inicializarEmpresa = inicializarEmpresa;
    }

    public boolean isApresentarConvenioInativo() {
        return apresentarConvenioInativo;
    }

    public void setApresentarConvenioInativo(boolean apresentarConvenioInativo) {
        this.apresentarConvenioInativo = apresentarConvenioInativo;
    }

    public List<BoletoVO> getItensProcessarBoleto() {
        if (itensProcessarBoleto == null) {
            itensProcessarBoleto = new ArrayList<>();
        }
        return itensProcessarBoleto;
    }

    public void setItensProcessarBoleto(List<BoletoVO> itensProcessarBoleto) {
        this.itensProcessarBoleto = itensProcessarBoleto;
    }

    public boolean isSucessoReciboItemRemessa() {
        return sucessoReciboItemRemessa;
    }

    public void setSucessoReciboItemRemessa(boolean sucessoReciboItemRemessa) {
        this.sucessoReciboItemRemessa = sucessoReciboItemRemessa;
    }

    public boolean getBoletoOnline() {
        return convenio.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE);
    }

    public String getOnCompleteBoleto() {
        if (onCompleteBoleto == null) {
            onCompleteBoleto = "";
        }
        return onCompleteBoleto;
    }

    public void setOnCompleteBoleto(String onCompleteBoleto) {
        this.onCompleteBoleto = onCompleteBoleto;
    }

    public List<String> getErrosGeracao() {
        if (errosGeracao == null) {
            errosGeracao = new ArrayList<>();
        }
        return errosGeracao;
    }

    public void setErrosGeracao(List<String> errosGeracao) {
        this.errosGeracao = errosGeracao;
    }

    public String getErrosGeracaoApresentar() {
        StringBuilder retorno = new StringBuilder();
        for (String erro : getErrosGeracao()) {
            retorno.append(erro).append(" <br/>");
        }
        return retorno.toString();
    }
}

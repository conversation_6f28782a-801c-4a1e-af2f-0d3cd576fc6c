/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import negocio.comuns.contrato.CancelamentoContratoVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.ChequeTO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class RetiradaAutomaticaControle extends SuperControle {

    private List<ContaVO> contas = new ArrayList<ContaVO>();
    private Map<Integer, ContaVO> mapaConta = new HashMap<Integer, ContaVO>();
    private String botaoClick;
    private CaixaVO caixaAberto;
    private String nomePessoa;

    public boolean preparar(List<ChequeVO> chequesPreparar, List<CartaoCreditoVO> cartoesPreparar, String botaoClick, String nomePessoa) throws Exception{
        this.botaoClick = botaoClick;
        contas = new ArrayList<ContaVO>();
        this.nomePessoa = nomePessoa;
        mapaConta = new HashMap<Integer, ContaVO>();
        if(cartoesPreparar != null){
            prepararListaCartoes(cartoesPreparar);
        }
        if(chequesPreparar != null){
            prepararListaCheques(chequesPreparar);
        }
        contas.addAll(mapaConta.values());
        AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);

        return (chequesPreparar != null &&
                !chequesPreparar.isEmpty()) || (cartoesPreparar != null && !cartoesPreparar.isEmpty());
    }
    //recebe uma lista de cheques e prepara a saida automatica dos mesmos das suas respectivas contas
    public void prepararListaCheques(List<ChequeVO> chequesPreparar) throws Exception{
        contas = new ArrayList<ContaVO>();
        
        for(ChequeVO cheque : chequesPreparar){
            ChequeTO mock = cheque.toTO();
            if(UteisValidacao.emptyString(cheque.getNomeNoCheque())){
                cheque.setNomeNoCheque(nomePessoa);
            }
            getFacade().getFinanceiro().getHistoricoCheque().getContaLoteCheque(mock, true);
            if(mock.getCodigoContaContido() == 0){
                continue;
            }
            setarConta(mapaConta, cheque, null, mock.getCodigoContaContido(), mock.getContaContido());
        }
    }

    public void retirarCancelamento(CancelamentoContratoVO cancelamento) throws Exception{
        
        contas = new ArrayList<ContaVO>();
    }

    public void confirmar() {
        try {
            setMsgAlert("");
            CaixaControle caixaControle = (CaixaControle) JSFUtilities.getFromSession(CaixaControle.class.getSimpleName());
            caixaAberto = caixaControle.getCaixaVoEmAberto();
            if (caixaAberto == null || UteisValidacao.emptyNumber(caixaAberto.getCodigo())) {
                caixaControle.abrirCaixa();
                setMsgAlert("if(!confirm('Você precisa ter um caixa aberto no financeiro. Deseja abrir?')){return false;};Richfaces.showModalPanel('modalAbrirCaixa')");
                return;
            }
            AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    setMsgAlert("document.getElementById('" + botaoClick + "').click();");
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    setMensagemDetalhada(e);
                    montarMsgAlert(getMensagemDetalhada());
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            auto.autorizar("Retirada automática de recebíveis do financeiro", "RetiradaAutomaticaRecebiveisFinanceiro",
                    "Você precisa da permissão \"Retirada automática de recebíveis do financeiro\"",
                    "", listener);
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void setarConta(Map<Integer,ContaVO> mapaConta, ChequeVO cheque, CartaoCreditoVO cartao, Integer codigoConta, String contaContido) throws Exception{
        ContaVO conta = mapaConta.get(codigoConta);
            if(conta == null){
                conta = getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(codigoConta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                conta.setChequesRetirar(new ArrayList<ChequeVO>());
                conta.setCartaoRetirar(new ArrayList<CartaoCreditoVO>());
                mapaConta.put(conta.getCodigo(), conta);
            }
        if(cheque != null){
            conta.getChequesRetirar().add(cheque);
            conta.setValorRetiradaAutomatica(conta.getValorRetiradaAutomatica()+cheque.getValorTotal());
        }
        if(cartao != null){
            conta.getCartaoRetirar().add(cartao);
            if(cartao.isCartaoEscolhido()){
                conta.setValorRetiradaAutomatica(conta.getValorRetiradaAutomatica()+cartao.getValorTotal());
            } else {
                conta.setValorRetiradaAutomatica(conta.getValorRetiradaAutomatica()+cartao.getValorParcialDevolver());
            }
        }

    }
    //recebe uma lista de cartoes e prepara a saida automatica dos mesmos das suas respectivas contas
    public void prepararListaCartoes(List<CartaoCreditoVO> cartoesPreparar) throws Exception{
        contas = new ArrayList<ContaVO>();
        for(CartaoCreditoVO cartao : cartoesPreparar){
            CartaoCreditoTO mock = cartao.toTO();
            if (UteisValidacao.emptyString(mock.getNomePagador())) {
                mock.setNomePagador(nomePessoa);
            }
            getFacade().getCartaoCredito().getContaLoteCartao(mock, mock.getNumeroLote());
            if(mock.getCodigoContaContido() == 0){
                continue;
            }
            setarConta(mapaConta, null, cartao, mock.getCodigoContaContido(), mock.getContaContido());
        }
        
    }

    public List<ContaVO> getContas() {
        return contas;
    }

    public void setContas(List<ContaVO> contas) {
        this.contas = contas;
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }
}

package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.basico.ClienteControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.RepasseTO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import org.richfaces.component.UIDataTable;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RelatorioRepasseControle extends SuperControleRelatorio {

    private static final int RELATORIO_REPASSE = 1;
    private List<PlanoVO> planosDesconsiderar = new ArrayList<PlanoVO>();
    private Double taxaRepasse = 20.0;
    private Boolean openConsulta = true;
    private Boolean openParametros = false;
    private Boolean openPlanos = false;
    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private List<RepasseTO> listaRepasse = new ArrayList<RepasseTO>();
    private Integer tipoRelatorioRep = RELATORIO_REPASSE;
    private Double totalValorPago = 0.0;
    private Double totalDescontado = 0.0;
    private Double totalCompensado = 0.0;
    private Double totalRepasse = 0.0;
    private EmpresaVO empresaVO;
    private Boolean desmarcarTodos = false;
    private static final int RECEITA = 1;
    private static final int COMPETENCIA = 2;
    private static final int COMPETENCIA_PAGA = 3;
    private List<SelectItem> tipo = null;
    private int tipoEscolhido = RECEITA;

    public void povoarComboEmpresa() {
        try {
            montarListaEmpresas();
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public static int getRelatorioRepasse() {
        return RELATORIO_REPASSE;

    }

    public RelatorioRepasseControle() throws Exception {
        empresaVO = getEmpresaLogado();
        obterUsuarioLogado();
        povoarComboEmpresa();
        montarPlanosDesconsiderar();
    }

    public Boolean getDesmarcarTodos() {
        if (desmarcarTodos == null){
            desmarcarTodos = true;
        }
        return desmarcarTodos;
    }


    public List consultarPorNomeEmpresa(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }
    public void irParaTelaCliente() {

        RepasseTO obj = (RepasseTO) context().getExternalContext().getRequestMap().get("repasse");
        try {
            if (obj == null)
                throw new Exception("Cliente Não Encontrado.");
            else {
                ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class);
                clienteControle.setarCliente(getFacade().getCliente().consultarPorMatricula(obj.getMatricula(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
                clienteControle.acaoAjax();
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void imprimirPDF() {
        try {
            limparMsg();
            setMsgAlert("");
            if (!getListaRepasse().isEmpty()) {
                UIDataTable dataTable = (UIDataTable) context().getViewRoot().findComponent("form:listaRepasse");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(getListaRepasse(), params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(getListaRepasse());
                        }
                    }
                }
            }
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.REL_REPASSE, getListaRepasse().size(), getFiltrosDescricao(), "pdf", "", "");
            Map<String, Object> params = new HashMap<String, Object>();
            prepareParams(params);
            apresentarRelatorioObjetos(params);
            setMsgAlert("abrirPopupPDFImpressao('relatorio/"+getNomeArquivoRelatorioGeradoAgora()+"','', 780, 595);");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void prepareParams(Map<String, Object> params) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }


        params.put("nomeRelatorio", "GestaoComissao");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", empre.getNome());
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", listaRepasse);

        params.put("filtros", getFiltrosDescricao());
        params.put("totalNr", String.valueOf(listaRepasse.size()));
        params.put("periodo", "Período: "+(getCompetencia() ? (Uteis.getMesReferenciaData(dataInicio)) + " até " + (Uteis.getMesReferenciaData(dataFim)) : (Uteis.getData(dataInicio) + " até "+Uteis.getData(dataFim))));
        params.put("dataIni", Uteis.getData(dataInicio));
        params.put("dataFim", Uteis.getData(dataFim));
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());

        params.put("totalValorPago", getTotalValorPago());
        params.put("totalDescontado", getTotalDescontado());
        params.put("totalCompensado", getTotalCompensado());
        params.put("totalRepasse", getTotalRepasse());


    }

    public String getFiltrosDescricao() {
        return "RELATÓRIO DE REPASSE ("+taxaRepasse+"%)";
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "RelatorioRepasse.jrxml");
    }

    public void totalizar() {
        totalValorPago = 0.0;
        totalDescontado = 0.0;
        totalCompensado = 0.0;
        totalRepasse = 0.0;

        for (RepasseTO repasse : listaRepasse) {
            totalValorPago += repasse.getValor();
            totalDescontado += repasse.getTotalDescontar();
            totalCompensado += repasse.getValorCompensado();
            totalRepasse += repasse.getValorRepasse();
        }
    }

    public void montarFiltros() throws Exception {
        if (getPlanosDesconsiderar() == null || getPlanosDesconsiderar().isEmpty())
            setPlanosDesconsiderar(getFacade().getPlano().consultarPorDescricao("",0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }
    public void montarPlanosDesconsiderar() throws Exception {
           setDesmarcarTodos(false);
               if(empresaVO != null)
              setPlanosDesconsiderar(getFacade().getPlano().consultarPorDescricao("",empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }
    public List<Integer> planosDesconsiderar() {
        List<Integer> cods = new ArrayList<Integer>();
        for (PlanoVO plano : planosDesconsiderar) {
            if (plano.getEscolhido())
                cods.add(plano.getCodigo());
        }
        return cods;
    }

    public void consultarInformacoes() {
        try {
            if (getDataInicio() == null || getDataFim() == null) {
                throw new Exception("Os campos de data devem ser preenchidos.");
            }
            setMsgAlert("");
            if (tipoEscolhido == COMPETENCIA) {
                listaRepasse = getFacade().getRelatorioRepasseRel().consultarPorCompetencia(planosDesconsiderar(), tipoRelatorioRep == RELATORIO_REPASSE, dataInicio, dataFim, taxaRepasse, getEmpresaLogado().getCodigo(), false);
            } else if (tipoEscolhido == COMPETENCIA_PAGA) {
                listaRepasse = getFacade().getRelatorioRepasseRel().consultarPorCompetencia(planosDesconsiderar(), tipoRelatorioRep == RELATORIO_REPASSE, dataInicio, dataFim, taxaRepasse, getEmpresaLogado().getCodigo(), true);
            } else {
                listaRepasse = getFacade().getRelatorioRepasseRel().consultarPorReceita(planosDesconsiderar(), tipoRelatorioRep == RELATORIO_REPASSE, dataInicio, dataFim, taxaRepasse, getEmpresaLogado().getCodigo());
            }
            totalizar();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }

    }

    public List<PlanoVO> getPlanosDesconsiderar() {
        return planosDesconsiderar;
    }
    public void selecionaTodosPlanos()
    {

        if (getDesmarcarTodos()){
            setDesmarcarTodos(false);
        }else {
            setDesmarcarTodos(true);
        }

        if (!getPlanosDesconsiderar().isEmpty()){
            for (PlanoVO obj: getPlanosDesconsiderar()){
                obj.setEscolhido(desmarcarTodos);
            }
        }
    }

    public Boolean setDesmarcarTodos(Boolean desmarcarTodos) {
        this.desmarcarTodos = desmarcarTodos;
        return desmarcarTodos;
    }
    public String getRotulo() {
        if (getDesmarcarTodos()){
            return "Desmarcar Todos";
        }else {
            return "Marcar Todos";
        }
    }

    public void setPlanosDesconsiderar(List<PlanoVO> planosDesconsiderar) {
        this.planosDesconsiderar = planosDesconsiderar;
    }

    public Double getTaxaRepasse() {
        return taxaRepasse;
    }

    public void setTaxaRepasse(Double taxaRepasse) {
        this.taxaRepasse = taxaRepasse;
    }

    public Boolean getOpenConsulta() {
        return openConsulta;
    }

    public void setOpenConsulta(Boolean openConsulta) {
        this.openConsulta = openConsulta;
    }

    public Boolean getOpenParametros() {
        return openParametros;
    }

    public void setOpenParametros(Boolean openParametros) {
        this.openParametros = openParametros;
    }

    public Boolean getOpenPlanos() {
        return openPlanos;
    }

    public void setOpenPlanos(Boolean openPlanos) {
        this.openPlanos = openPlanos;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public List<RepasseTO> getListaRepasse() {
        return listaRepasse;
    }

    public void setListaRepasse(List<RepasseTO> listaRepasse) {
        this.listaRepasse = listaRepasse;
    }

    public Integer getTipoRelatorioRep() {
        return tipoRelatorioRep;
    }

    public void setTipoRelatorioRep(Integer tipoRelatorioRep) {
        this.tipoRelatorioRep = tipoRelatorioRep;
    }

    public String getTotalValorPago() {
        return Formatador.formatarValorMonetarioSemMoeda(totalValorPago);
    }

    public void setTotalValorPago(Double totalValorPago) {
        this.totalValorPago = totalValorPago;
    }

    public String getTotalDescontado() {
        return Formatador.formatarValorMonetarioSemMoeda(totalDescontado);
    }

    public void setTotalDescontado(Double totalDescontado) {
        this.totalDescontado = totalDescontado;
    }

    public String getTotalCompensado() {
        return Formatador.formatarValorMonetarioSemMoeda(totalCompensado);
    }

    public void setTotalCompensado(Double totalCompensado) {
        this.totalCompensado = totalCompensado;
    }

    public String getTotalRepasse() {
        return Formatador.formatarValorMonetarioSemMoeda(totalRepasse);
    }

    public void setTotalRepasse(Double totalRepasse) {
        this.totalRepasse = totalRepasse;
    }

    public List<SelectItem> getTipo() {
        if(tipo == null || tipo.isEmpty()){
            tipo = new ArrayList<SelectItem>();
            tipo.add(new SelectItem(RECEITA, "Receita"));
            tipo.add(new SelectItem(COMPETENCIA, "Competência"));
            tipo.add(new SelectItem(COMPETENCIA_PAGA, "Competência Paga"));
        }
        return tipo;
    }

    public void setTipo(List<SelectItem> tipoRelatorio) {
        this.tipo = tipoRelatorio;
    }

    public int getTipoEscolhido() {
        return tipoEscolhido;
    }

    public void setTipoEscolhido(int tipoEscolhido) {
        this.tipoEscolhido = tipoEscolhido;
    }
    
    public String getDatePattern() {
        if (this.tipoEscolhido == COMPETENCIA || this.tipoEscolhido == COMPETENCIA_PAGA) {
            return "MM/yyyy";
        } else {
            return "dd/MM/yyyy";
        }
    }
    
    public boolean getCompetencia(){
        return tipoEscolhido == COMPETENCIA || tipoEscolhido == COMPETENCIA_PAGA;
    }
    
    public String getHeaderColunaCod(){
        return getCompetencia() ? "Código" : "Cod. Pagto";
    }
    
    public String getHeaderColunaData(){
        return getCompetencia() ? "Mês" : "Data Compensação";   
    }
}

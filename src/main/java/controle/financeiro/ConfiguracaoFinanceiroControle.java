package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.openbanking.stone.ContaStoneVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.PlanoConta;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.model.SelectItem;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

public class ConfiguracaoFinanceiroControle extends SuperControle {
	
	private ConfiguracaoFinanceiroVO confFinanceiro = new ConfiguracaoFinanceiroVO();
	private String descricaoProcessoRealizado;
    private String nomePlanoDefaultTaxaPix;
    private String nomeCentroCustoDefaultTaxaPix;
    private String nomePlanoDefaultChequesDevolvidos;
    private String nomeCentroCustoDefaultChequesDevolvidos;
    private boolean permitePlanoPadrao = true;
    private Integer codigoContratoEstornoRecibo;
    private Date compensacaoChequeApartirDe;
    private int qtdDiasAcrescentarCompensacaoCheque;
    private Date dataInicioProcessar;
    private Date dataFimProcessar;
    private Integer empresaSelecionada;
    private List<EmpresaVO> listaEmpresasCadastradas;
    private List<SelectItem> selectItemEmpresas;
    private List<ContaVO> contaVOS;
    private String nomePlanoDefaultLancAutoEntrada;
    private String nomePlanoDefaultLancAutoSaida;
    private String nomeCentroCustoDefaultLancAutoEntrada;
    private String nomeCentroCustoDefaultLancAutoSaida;
    private String oncompleteLog;
    private boolean desmarcouUsarMovimentacaoContas = false;

    private String descricaoMovimentacaoAutomaticaDebito;
    private String descricaoMovimentacaoAutomaticaCredito;

    private ContaVO contaMovimentacaoAutomaticaDebito;
    private ContaVO contaMovimentacaoAutomaticaCredito;
    private PessoaVO favorecidomovimentacaoautomaticaDebito;
    private PessoaVO favorecidomovimentacaoautomaticaCredito;

    private boolean movimentacaoAutomaticaRecebiveisConciliacao = false;

    private List<SelectItem> listaComboConta = new ArrayList<SelectItem>();

    public void estornarReciboDevolucao() {
        try {
            getFacade().getConfiguracaoFinanceiro().estornarReciboCancelamento(codigoContratoEstornoRecibo);
            montarMsgAlert("Devolução estornado com sucesso!");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void realizarAjusteCompensacaoCheque(){
        try{
            limparMsg();
            setMsgAlert("");
            if(qtdDiasAcrescentarCompensacaoCheque > 0){
            getFacade().getConfiguracaoFinanceiro().executarAjusteCompensacaoCheque(qtdDiasAcrescentarCompensacaoCheque, compensacaoChequeApartirDe);
                montarSucessoGrowl("Ajuste realizado com sucesso.");
            }else{
                montarAviso("Informe uma quantidade de dias à ser acrescentado.");
            }
            qtdDiasAcrescentarCompensacaoCheque = 0;
            compensacaoChequeApartirDe = Calendario.hoje();
        }catch(Exception e){
            montarErro(e.getMessage());
        }
    }

    public boolean isUsarFinanceiroAvancado() throws Exception {
        try {
            return getFacade().getConfiguracaoFinanceiro().consultarUsarMovimentacaoContas();
        } catch (Exception e) {
            montarErro(e.getMessage());
            throw new Exception("Não foi possível obter informação de uso do financeiro avançado");
        }
    }

    public void habilitarFinanceiroAvancado() throws Exception {
        try {
            getFacade().getConfiguracaoFinanceiro().habilitarDesabilitarFinanceiroAvancado(true);
            incluirLog(getEmpresaLogado().getCodigo(), getUsuarioLogado(), "Alteração", true);
            montarSucessoGrowl("Financeiro avançado habilitado com sucesso!");
        } catch (Exception e) {
            montarErro(e.getMessage());
            throw new Exception("Não foi possível obter informação de uso do financeiro avançado");
        }
    }

    public void desabilitarFinanceiroAvancado() throws Exception {
        try {
            getFacade().getConfiguracaoFinanceiro().habilitarDesabilitarFinanceiroAvancado(false);
            incluirLog(getEmpresaLogado().getCodigo(), getUsuarioLogado(), "Alteração", false);
            montarSucessoGrowl("Financeiro avançado desabilitado com sucesso!");
        } catch (Exception e) {
            montarErro(e.getMessage());
            throw new Exception("Não foi possível obter informação de uso do financeiro avançado");
        }
    }

    public String getStatusFinanceiroAvancadoExibir() throws Exception {
        try {
            boolean ativado = getFacade().getConfiguracaoFinanceiro().consultarUsarMovimentacaoContas();
            if (ativado) {
                return "Ativado";
            } else {
                return "Desativado";
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
            throw new Exception("Não foi possível obter informação de uso do financeiro avançado");
        }
    }

    public void incluirLog(int codEmpresa, UsuarioVO usuarioVO, String op, boolean habilitando) {
        try {
            LogVO obj = new LogVO();
            obj.setChavePrimaria(String.valueOf(codEmpresa));
            obj.setNomeEntidade("CONFIGURACAOFINANCEIRO");
            obj.setNomeEntidadeDescricao("financeiroAvancado");
            obj.setOperacao(op);
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("usarMovimentacaoContas");
            obj.setDataAlteracao(Calendario.hoje());
            String valorCampoAnterior = habilitando ? "false" : "true";
            obj.setValorCampoAnterior(valorCampoAnterior);
            obj.setValorCampoAlterado(String.valueOf(habilitando));
            getFacade().getLog().incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
	
	public ConfiguracaoFinanceiroControle(){
		try {
			confFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
            nomePlanoDefaultChequesDevolvidos = getConfFinanceiro().getPlanoContasDevolucao().getDescricao();
            nomeCentroCustoDefaultChequesDevolvidos = getConfFinanceiro().getCentroCustoDevolucao().getDescricao();
            montarListaEmpresasComItemTodas();
            selectItemEmpresas = new ArrayList<>(super.getListaEmpresas());
            carregarContas();
		} catch (Exception e) {
			confFinanceiro = new ConfiguracaoFinanceiroVO();
		}
	}

        public void rePovoarPlanoContas(){
            try {
                getFacade().getFinanceiro().getPlanoConta().rePovoarPlanoContas();
                limparMsg();
                setMensagemID("msg_planocontas_repovoados");
            } catch (Exception e) {
                setMensagemDetalhada(e);
                setMensagemID("msg_erro");
            }
        }

	public void preparaEdicao(){
		try{
            setDesmarcouUsarMovimentacaoContas(false);
            notificarRecursoEmpresa(RecursoSistema.CONFIG_FIN);
			setMsgAlert("");
//			autorizarConfiguracoes();
            permitePlanoPadrao = !getFacade().getFinanceiro().getPlanoConta().verificarExisteRelacionamento();
			confFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
            carregarContas();
			PlanoContasControle controlPlanoContas = getControlPlanoContas();
			CentroCustosControle controlCentroCustos = getControlCentroCustos();
			if (controlPlanoContas != null) {
				controlPlanoContas.setPlanoNome(UteisValidacao.emptyNumber(getConfFinanceiro().getPlanoContasTaxa().getCodigo()) ? "" :
						getConfFinanceiro().getPlanoContasTaxa().getCodigoPlano() + " - "+getConfFinanceiro().getPlanoContasTaxa().getDescricao());
				controlPlanoContas.setPlanoEscolhido(getConfFinanceiro().getPlanoContasTaxa());

                controlPlanoContas.setPlanoNomeBoleto(UteisValidacao.emptyNumber(getConfFinanceiro().getPlanoContasTaxaBoleto().getCodigo()) ? "" :
                        getConfFinanceiro().getPlanoContasTaxaBoleto().getCodigoPlano() + " - "+getConfFinanceiro().getPlanoContasTaxaBoleto().getDescricao());
                controlPlanoContas.setPlanoEscolhidoBoleto(getConfFinanceiro().getPlanoContasTaxaBoleto());
	        } 
	        if (controlCentroCustos != null) {
	        	controlCentroCustos.setCentroNome(UteisValidacao.emptyNumber(getConfFinanceiro().getCentroCustosTaxa().getCodigo()) ? "" :
	        			         getConfFinanceiro().getCentroCustosTaxa().getCodigoCentro()+" - "+getConfFinanceiro().getCentroCustosTaxa().getDescricao());
	        	controlCentroCustos.setCentroEscolhido(getConfFinanceiro().getCentroCustosTaxa());

                controlCentroCustos.setCentroNomeBoleto(UteisValidacao.emptyNumber(getConfFinanceiro().getCentroCustosTaxaBoleto().getCodigo()) ? "" :
                        getConfFinanceiro().getCentroCustosTaxaBoleto().getCodigoCentro()+" - "+getConfFinanceiro().getCentroCustosTaxaBoleto().getDescricao());
                controlCentroCustos.setCentroEscolhidoBoleto(getConfFinanceiro().getCentroCustosTaxaBoleto());
	        }
            this.movimentacaoAutomaticaRecebiveisConciliacao = confFinanceiro.isMovimentacaoAutomaticaRecebiveisConciliacao();
	        limparMsg();
	        setMsgAlert("abrirPopup('"+request().getContextPath()+"/faces/pages/finan/finanConfiguracoes.jsp?modulo=financeiroWeb', 'MetasFinanceiro', 780, 595);");
            setSucesso(false);
            setErro(false);
            confFinanceiro.registrarObjetoVOAntesDaAlteracao();
        } catch(Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }

	}

	public void darConsentimentoStoneOpenBank() throws Exception {
        limparMsg();
        setMsgAlert("");
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = Uteis.getUrlDiscovery("openBankingMs") + "/stonebank/consent";
        HashMap<String, String> headers = new HashMap<>();
        headers.put("chaveZW", getKey());
        headers.put("empresaZW", getEmpresaLogado().getCodigo().toString());
        headers.put("producao", PropsService.getPropertyValue(PropsService.stoneOpenBankProducao));
        try{
            String response = executeRequestHttpService.executeRequestCupomDesconto(url, "", headers);
            String urlConsentimento = new JSONObject(response).get("content").toString();
            setMsgAlert("abrirPopupMaximizada('"+urlConsentimento+"','pop')");
        }catch (Exception e){
            montarErro("Não foi possível realizar consentimento agora, tente novamente mais tarde.");
        }
    }
	
	public void gravar() {
		try {
            validarCamposObrigatorios();
                if (getControlPlanoContas() != null) {
                    if (getControlPlanoContas().getPlanoEscolhido() != null) {
                        getConfFinanceiro().setPlanoContasTaxa(getControlPlanoContas().getPlanoEscolhido());
                    }
                    if (getControlPlanoContas().getPlanoEscolhidoBoleto() != null){
                        getConfFinanceiro().setPlanoContasTaxaBoleto(getControlPlanoContas().getPlanoEscolhidoBoleto());
                    }
                }
                if (getControlCentroCustos() != null) {
                    if (getControlCentroCustos().getCentroEscolhido() != null) {
                        getConfFinanceiro().setCentroCustosTaxa(getControlCentroCustos().getCentroEscolhido());
                    }
                    if (getControlCentroCustos().getCentroEscolhidoBoleto() != null){
                        getConfFinanceiro().setCentroCustosTaxaBoleto(getControlCentroCustos().getCentroEscolhidoBoleto());
                    }
                }
                carregarDadosLancamentoAutomatico();

            getFacade().getConfiguracaoFinanceiro().alterar(confFinanceiro);
                registrarLogObjetoVO(confFinanceiro, confFinanceiro.getCodigo(), "CONFIGURACAOFINANCEIRO", 0);
                confFinanceiro.registrarObjetoVOAntesDaAlteracao();
                setMensagemID("msg_dados_gravados");
                setSucesso(true);
                setErro(false);

		} catch (Exception e) {
			setSucesso(false);
			setErro(true);
			setMensagemDetalhada("msg_erro", e.getMessage());
		}
	}

    public void validarCamposObrigatorios() throws Exception {
        if (getConfFinanceiro().isAdicionarDevolucaoRelatorioComissao()) {
            if (getConfFinanceiro().getPlanoContasDevolucao() == null || UteisValidacao.emptyNumber(getConfFinanceiro().getPlanoContasDevolucao().getCodigo())) {
                throw new Exception("O campo 'Plano de contas de cheques devolvidos (Despesa)' na aba 'Devolução de cheques' deve ser informado");
            }
            if (getConfFinanceiro().getCentroCustoDevolucao() == null || UteisValidacao.emptyNumber(getConfFinanceiro().getCentroCustoDevolucao().getCodigo())) {
                throw new Exception("O campo 'Centro de custo de cheques devolvidos (Despesa)' na aba 'Devolução de cheques' deve ser informado");
            }
        }

        if(getConfFinanceiro().isMovimentacaoAutomaticaRecebiveisConciliacao()){
            if (UteisValidacao.emptyString(getConfFinanceiro().getDescricaomovimentacaoautomaticaDebito())){
                throw new Exception("O campo 'Descrição (Débito)' na aba 'Configurações de Conciliação Automática' deve ser informado.");
            }
            if (UteisValidacao.emptyString(getConfFinanceiro().getFavorecidomovimentacaoautomaticaDebito().getNome())){
                throw new Exception("O campo 'Favorecido (Débito)' na aba 'Configurações de Conciliação Automática' deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(getConfFinanceiro().getContamovimentacaoautomaticadebito().getCodigo())){
                throw new Exception("O campo 'Conta Bancária (Débito)' na aba 'Configurações de Conciliação Automática' deve ser informado.");
            }
            if (UteisValidacao.emptyString(getConfFinanceiro().getDescricaomovimentacaoautomaticaCredito())){
                throw new Exception("O campo 'Descrição (Crédito)' na aba 'Configurações de Conciliação Automática' deve ser informado.");
            }
            if (UteisValidacao.emptyString(getConfFinanceiro().getFavorecidomovimentacaoautomaticaCredito().getNome())){
                throw new Exception("O campo 'Favorecido (Crédito)' na aba 'Configurações de Conciliação Automática' deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(getConfFinanceiro().getContamovimentacaoautomaticacredito().getCodigo())){
                throw new Exception("O campo 'Conta Bancária (Crédito)' na aba 'Configurações de Conciliação Automática' deve ser informado.");
            }
        }
    }

    public void setarPlanoPaiVazio() {
        try {
            getConfFinanceiro().setPlanoContasDevolucao(new PlanoContaTO());
            if (this.nomePlanoDefaultChequesDevolvidos == null || this.nomePlanoDefaultChequesDevolvidos.isEmpty()) {
                getConfFinanceiro().setPlanoContasDevolucao(new PlanoContaTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void setarCentroCustoDevolucaoVazio() {
        try {
            getConfFinanceiro().setCentroCustoDevolucao(new CentroCustoTO());
            if (this.nomeCentroCustoDefaultChequesDevolvidos == null || this.nomeCentroCustoDefaultChequesDevolvidos.isEmpty()) {
                getConfFinanceiro().setCentroCustoDevolucao(new CentroCustoTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void setarPlanoPaiVazioTaxaPix() {
        try {
            getConfFinanceiro().setPlanoContasTaxaPix(new PlanoContaTO());
            if (this.nomePlanoDefaultTaxaPix == null || this.nomePlanoDefaultTaxaPix.isEmpty()) {
                getConfFinanceiro().setPlanoContasTaxaPix(new PlanoContaTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void setarCentroCustoDevolucaoVazioTaxaPix() {
        try {
            getConfFinanceiro().setCentroCustoTaxaPix(new CentroCustoTO());
            if (this.nomeCentroCustoDefaultTaxaPix == null || this.nomeCentroCustoDefaultTaxaPix.isEmpty()) {
                getConfFinanceiro().setCentroCustoTaxaPix(new CentroCustoTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarPlanoContas() throws SQLException, Exception {
        selecionarPlano((PlanoContaTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarPlanoContasTaxaPix() throws SQLException, Exception {
        selecionarPlanoPix((PlanoContaTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarPlanoPix(PlanoContaTO obj)throws Exception{
        getConfFinanceiro().setPlanoContasTaxaPix(obj);
    }

    public void selecionarPlano(PlanoContaTO obj)throws Exception{
        getConfFinanceiro().setPlanoContasDevolucao(obj);
    }

    public void selecionarCentroCusto() throws SQLException, Exception {
        selecionarCentroCustoDAO((CentroCustoTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarCentroCustoDAO(CentroCustoTO obj)throws Exception{
        getConfFinanceiro().setCentroCustoDevolucao(obj);
    }

    public void selecionarCentroCustoTaxaPix() throws SQLException, Exception {
        selecionarCentroCustoPix((CentroCustoTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarCentroCustoPix(CentroCustoTO obj)throws Exception{
        getConfFinanceiro().setCentroCustoTaxaPix(obj);
    }

    public Date getDataInicioProcessar() {
        return dataInicioProcessar;
    }

    public void setDataInicioProcessar(Date dataInicioProcessar) {
        this.dataInicioProcessar = dataInicioProcessar;
    }

    public Date getDataFimProcessar() {
        return dataFimProcessar;
    }

    public void setDataFimProcessar(Date dataFimProcessar) {
        this.dataFimProcessar = dataFimProcessar;
    }

    public Integer getEmpresaSelecionada() {
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(Integer empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public List<SelectItem> getSelectItemEmpresas() {
        return selectItemEmpresas;
    }

    public void setSelectItemEmpresas(List<SelectItem> selectItemEmpresas) {
        this.selectItemEmpresas = selectItemEmpresas;
    }

    public enum TipoConsulta {

        TODOS, SOMENTE_ENTRADA, SOMENTE_SAIDA
    };
    public List<PlanoContaTO> executarAutocompletePesqPlanoContas(Object suggest) {
        List<PlanoContaTO> listaPlanoContas = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
            Integer codigo = null;
            if (somenteNumero){
                codigo = Integer.parseInt(nomePesq);
            }
            PlanoConta pj = new PlanoConta();
            if (somenteNumerosEPontos) {
                listaPlanoContas = pj.consultar(nomePesq, null,codigo, TipoConsulta.SOMENTE_SAIDA.name());
            } else {
                listaPlanoContas = pj.consultar(null, nomePesq, codigo, TipoConsulta.SOMENTE_SAIDA.name());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaPlanoContas;
    }

    public List<PlanoContaTO> executarAutocompletePesqPlanoContasEntrada(Object suggest) {
        List<PlanoContaTO> listaPlanoContas = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
            Integer codigo = null;
            if (somenteNumero){
                codigo = Integer.parseInt(nomePesq);
            }
            PlanoConta pj = new PlanoConta();
            if (somenteNumerosEPontos) {
                listaPlanoContas = pj.consultar(nomePesq, null,codigo, TipoConsulta.SOMENTE_ENTRADA.name());
            } else {
                listaPlanoContas = pj.consultar(null, nomePesq, codigo, TipoConsulta.SOMENTE_ENTRADA.name());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaPlanoContas;
    }

    public List<CentroCustoTO> executarAutocompletePesqCentrosCusto(Object suggest) {
        List<CentroCustoTO> listaCentrosCusto = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
//            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
//            Integer codigo = null;
//            if (somenteNumero){
//                codigo = Integer.parseInt(nomePesq);
//            }
            CentroCusto centoCusto = new CentroCusto();
            if (somenteNumerosEPontos) {
                listaCentrosCusto = centoCusto.consultar(nomePesq, null,null);
            } else {
                listaCentrosCusto = centoCusto.consultar(null, nomePesq, null);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaCentrosCusto;
    }

    private void autorizarConfiguracoes() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "ConfiguracaoFinanceiro", "9.08 - Configurações do Financeiro");
            }
        }
    }

	public void setConfFinanceiro(ConfiguracaoFinanceiroVO confFinanceiro) {
		this.confFinanceiro = confFinanceiro;
	}

	public ConfiguracaoFinanceiroVO getConfFinanceiro() {
        if (confFinanceiro == null) {
            confFinanceiro = new ConfiguracaoFinanceiroVO();
        }
		return confFinanceiro;
	}
	
	public PlanoContasControle getControlPlanoContas(){
		return (PlanoContasControle) getControlador(PlanoContasControle.class.getSimpleName()); 
	}
	
	public CentroCustosControle getControlCentroCustos(){
		return (CentroCustosControle) getControlador(CentroCustosControle.class.getSimpleName());
	}

    public String getDescricaoProcessoRealizado() {
        return descricaoProcessoRealizado;
    }

    public void setDescricaoProcessoRealizado(String descricaoProcessoRealizado) {
        this.descricaoProcessoRealizado = descricaoProcessoRealizado;
    }

    public void rodarAtualizacaoTaxaCartao() {
        gravar();
        try {
            LogVO log = getFacade().getFinanceiro().getMovConta().alterarCentroCustoPlanoContaTaxaCartaoBoleto(getConfFinanceiro().getPlanoContasTaxa().getCodigo(), getConfFinanceiro().getCentroCustosTaxa().getCodigo(), getUsuarioLogado(), false);
            getFacade().getLog().incluir(log);
            setDescricaoProcessoRealizado(log.getValorCampoAlterado() + "<br/> Configurações Salvas.");

        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }

    public void rodarAtualizacaoTaxaBoleto() {
        gravar();
        try {
            LogVO log = getFacade().getFinanceiro().getMovConta().alterarCentroCustoPlanoContaTaxaCartaoBoleto(getConfFinanceiro().getPlanoContasTaxaBoleto().getCodigo(), getConfFinanceiro().getCentroCustosTaxaBoleto().getCodigo(), getUsuarioLogado(), true);
            getFacade().getLog().incluir(log);
            setDescricaoProcessoRealizado(log.getValorCampoAlterado() + "<br/> Configurações Salvas.");

        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
    }

    public String getDescricaomovimentacaoautomaticaDebito() {
        return descricaoMovimentacaoAutomaticaDebito;
    }

    public void setDescricaomovimentacaoautomaticaDebito(String descricaomovimentacaoautomaticaDebito) {
        this.descricaoMovimentacaoAutomaticaDebito = descricaomovimentacaoautomaticaDebito;
    }

    public String getDescricaoMovimentacaoAutomaticaCredito() {
        return descricaoMovimentacaoAutomaticaCredito;
    }

    public void setDescricaoMovimentacaoAutomaticaCredito(String descricaoMovimentacaoAutomaticaCredito) {
        this.descricaoMovimentacaoAutomaticaCredito = descricaoMovimentacaoAutomaticaCredito;
    }

    public ContaVO getContaMovimentacaoAutomaticaDebito() {
        return contaMovimentacaoAutomaticaDebito;
    }

    public void setContaMovimentacaoAutomaticaDebito(ContaVO contaMovimentacaoAutomaticaDebito) {
        this.contaMovimentacaoAutomaticaDebito = contaMovimentacaoAutomaticaDebito;
    }

    public ContaVO getContaMovimentacaoAutomaticaCredito() {
        return contaMovimentacaoAutomaticaCredito;
    }

    public void setContaMovimentacaoAutomaticaCredito(ContaVO contaMovimentacaoAutomaticaCredito) {
        this.contaMovimentacaoAutomaticaCredito = contaMovimentacaoAutomaticaCredito;
    }

    public PessoaVO getFavorecidomovimentacaoautomaticaDebito() {
        return favorecidomovimentacaoautomaticaDebito;
    }

    public void setFavorecidomovimentacaoautomaticaDebito(PessoaVO favorecidomovimentacaoautomaticaDebito) {
        this.favorecidomovimentacaoautomaticaDebito = favorecidomovimentacaoautomaticaDebito;
    }

    public PessoaVO getFavorecidomovimentacaoautomaticaCredito() {
        return favorecidomovimentacaoautomaticaCredito;
    }

    public void setFavorecidomovimentacaoautomaticaCredito(PessoaVO favorecidomovimentacaoautomaticaCredito) {
        this.favorecidomovimentacaoautomaticaCredito = favorecidomovimentacaoautomaticaCredito;
    }

    public boolean isMovimentacaoAutomaticaRecebiveisConciliacao() {
        return movimentacaoAutomaticaRecebiveisConciliacao;
    }

    public void setMovimentacaoAutomaticaRecebiveisConciliacao(boolean movimentacaoAutomaticaRecebiveisConciliacao) {
        this.movimentacaoAutomaticaRecebiveisConciliacao = movimentacaoAutomaticaRecebiveisConciliacao;
    }

    public boolean isPermitePlanoPadrao() {
        return permitePlanoPadrao;
    }

    public void setPermitePlanoPadrao(boolean permitePlanoPadrao) {
        this.permitePlanoPadrao = permitePlanoPadrao;
    }

    public Integer getCodigoContratoEstornoRecibo() {
        return codigoContratoEstornoRecibo;
    }

    public void setCodigoContratoEstornoRecibo(Integer codigoContratoEstornoRecibo) {
        this.codigoContratoEstornoRecibo = codigoContratoEstornoRecibo;
    }

    public String getNomePlanoDefaultChequesDevolvidos() {
        return nomePlanoDefaultChequesDevolvidos;
    }

    public void setNomePlanoDefaultChequesDevolvidos(String nomePlanoDefaultChequesDevolvidos) {
        this.nomePlanoDefaultChequesDevolvidos = nomePlanoDefaultChequesDevolvidos;
    }

    public Date getCompensacaoChequeApartirDe() {
        if(compensacaoChequeApartirDe == null){
            compensacaoChequeApartirDe = Calendario.hoje();
        }
        return compensacaoChequeApartirDe;
    }

    public void setCompensacaoChequeApartirDe(Date compensacaoChequeApartirDe) {
        this.compensacaoChequeApartirDe = compensacaoChequeApartirDe;
    }

    public int getQtdDiasAcrescentarCompensacaoCheque() {
        return qtdDiasAcrescentarCompensacaoCheque;
    }

    public void setQtdDiasAcrescentarCompensacaoCheque(int qtdDiasAcrescentarCompensacaoCheque) {
        this.qtdDiasAcrescentarCompensacaoCheque = qtdDiasAcrescentarCompensacaoCheque;
    }

    public String getNomeCentroCustoDefaultChequesDevolvidos() {
        return nomeCentroCustoDefaultChequesDevolvidos;
    }

    public void setNomeCentroCustoDefaultChequesDevolvidos(String nomeCentroCustoDefaultChequesDevolvidos) {
        this.nomeCentroCustoDefaultChequesDevolvidos = nomeCentroCustoDefaultChequesDevolvidos;
    }

    public boolean isContaStoneIncluida(){
        try {
            return !UteisValidacao.emptyString(getFacade().getContaStone().buscarAccountIdContaStone(getEmpresaLogado().getCodigo()));
        }catch (Exception e){
            return false;
        }
    }

    public String getContaStoneDetalhes(){
        try {
            ContaStoneVO contaStone = getFacade().getContaStone().buscarContaStone(getEmpresaLogado().getCodigo());
            return "Nome: "+contaStone.getOwner_name()+", "+"Agencia: "+contaStone.getBranch_code()+", "+"Conta: "+contaStone.getAccount_code()+".";
        }catch (Exception e){
            return "Falha ao obter dados da conta";
        }
    }

    public String getOncompleteLog() {
        if (oncompleteLog == null) {
            oncompleteLog = "";
        }
        return oncompleteLog;
    }

    public void setOncompleteLog(String oncompleteLog) {
        this.oncompleteLog = oncompleteLog;
    }

    public void processarDadosSintetico() {
        try {
            limparMsg();

            if (getDataInicioProcessar() == null) {
                throw new Exception("Informe a data início.");
            }
            if (getDataFimProcessar() == null) {
                throw new Exception("Informe a data fim.");
            }
            if (Calendario.maior(getDataInicioProcessar(), getDataFimProcessar())) {
                throw new Exception("A data início deve ser superior a fim.");
            }

            int nrMeses = Calendario.diferencaEmMeses(getDataInicioProcessar(), getDataFimProcessar());
            if (nrMeses > 6) {
                throw new Exception("O período deve ser de no máximo 6 meses.");
            }

            getFacade().getFinanceiro().getDFSinteticoDW().gerarDadosSinteticoProcesso(getEmpresaSelecionada(), getDataInicioProcessar(), getDataFimProcessar());
            montarSucessoGrowl("Dados processados");
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void iniciarTabDevolucaoCheque(){
        nomePlanoDefaultChequesDevolvidos = getConfFinanceiro().getPlanoContasDevolucao().getDescricao();
        nomeCentroCustoDefaultChequesDevolvidos = getConfFinanceiro().getCentroCustoDevolucao().getDescricao();
    }

    public void iniciarTabTaxaPix(){
        nomePlanoDefaultTaxaPix = getConfFinanceiro().getPlanoContasTaxaPix().getDescricao();
        nomeCentroCustoDefaultTaxaPix = getConfFinanceiro().getCentroCustoTaxaPix().getDescricao();
    }

    public void iniciarTabConfiguracoesConciliacao() throws Exception {
        carregarDadosLancamentoAutomatico();
        this.montarSelectItemListaComboConta();
    }

    public void montarSelectItemListaComboConta() throws Exception {
        try {
            MovContaControle mov = new MovContaControle();

            Boolean visualizarTodasEmpresas = mov.visualizarTodasEmpresas();
            this.listaComboConta = new ArrayList<SelectItem>();
            SelectItem item = new SelectItem(0, " ");
            this.listaComboConta.add(item);
            List<ContaVO> consultar = getFacade().getFinanceiro().getConta().consultarContasCaixaAberto(visualizarTodasEmpresas ? 0 : 0,
                    mov.obterCodigoCaixaEmAberto(), true);

            for (ContaVO obj : consultar) {
                item = new SelectItem(obj.getCodigo(), obj.getDescricao());
                this.listaComboConta.add(item);
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public List<ContaVO> getContaVOS() {
        if (contaVOS == null) {
            contaVOS = new ArrayList<>();
        }
        return contaVOS;
    }

    public void setContaVOS(List<ContaVO> contaVOS) {
        this.contaVOS = contaVOS;
    }

    private void carregarContas() {
        try {
            this.setContaVOS(getFacade().getConta().consultarContasSimples(null, true));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public List<SelectItem> getContas() {
        List<SelectItem> lista = new ArrayList<>();
        for (ContaVO contaVO : this.getContaVOS()) {
            lista.add(new SelectItem(contaVO.getCodigo(), contaVO.getDescricao()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public String getNomePlanoDefaultLancAutoEntrada() {
        if (nomePlanoDefaultLancAutoEntrada == null) {
            nomePlanoDefaultLancAutoEntrada = "";
        }
        return nomePlanoDefaultLancAutoEntrada;
    }

    public void setNomePlanoDefaultLancAutoEntrada(String nomePlanoDefaultLancAutoEntrada) {
        this.nomePlanoDefaultLancAutoEntrada = nomePlanoDefaultLancAutoEntrada;
    }

    public String getNomePlanoDefaultLancAutoSaida() {
        if (nomePlanoDefaultLancAutoSaida == null) {
            nomePlanoDefaultLancAutoSaida = "";
        }
        return nomePlanoDefaultLancAutoSaida;
    }

    public void setNomePlanoDefaultLancAutoSaida(String nomePlanoDefaultLancAutoSaida) {
        this.nomePlanoDefaultLancAutoSaida = nomePlanoDefaultLancAutoSaida;
    }

    public String getNomeCentroCustoDefaultLancAutoEntrada() {
        if (nomeCentroCustoDefaultLancAutoEntrada == null) {
            nomeCentroCustoDefaultLancAutoEntrada = "";
        }
        return nomeCentroCustoDefaultLancAutoEntrada;
    }

    public void setNomeCentroCustoDefaultLancAutoEntrada(String nomeCentroCustoDefaultLancAutoEntrada) {
        this.nomeCentroCustoDefaultLancAutoEntrada = nomeCentroCustoDefaultLancAutoEntrada;
    }

    public String getNomeCentroCustoDefaultLancAutoSaida() {
        if (nomeCentroCustoDefaultLancAutoSaida == null) {
            nomeCentroCustoDefaultLancAutoSaida = "";
        }
        return nomeCentroCustoDefaultLancAutoSaida;
    }

    public void setNomeCentroCustoDefaultLancAutoSaida(String nomeCentroCustoDefaultLancAutoSaida) {
        this.nomeCentroCustoDefaultLancAutoSaida = nomeCentroCustoDefaultLancAutoSaida;
    }

    public void selecionarPlanoContasSaida() {
        getConfFinanceiro().setPlanoContasLancamentoAutomaticoSaida((PlanoContaTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarPlanoContasEntrada() {
        getConfFinanceiro().setPlanoContasLancamentoAutomaticoEntrada((PlanoContaTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarCentroCustoSaida() {
        getConfFinanceiro().setCentroCustoLancamentoAutomaticoSaida((CentroCustoTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void selecionarCentroCustoEntrada() {
        getConfFinanceiro().setCentroCustoLancamentoAutomaticoEntrada((CentroCustoTO) context().getExternalContext().getRequestMap().get("result"));
    }

    public void setarCentroCustoLancamentoAutoEntradaVazio() {
        try {
            getConfFinanceiro().setCentroCustoLancamentoAutomaticoEntrada(new CentroCustoTO());
            if (this.nomeCentroCustoDefaultLancAutoEntrada == null || this.nomeCentroCustoDefaultLancAutoEntrada.isEmpty()) {
                getConfFinanceiro().setCentroCustoLancamentoAutomaticoEntrada(new CentroCustoTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void setarCentroCustoLancamentoAutoSaidaVazio() {
        try {
            getConfFinanceiro().setCentroCustoLancamentoAutomaticoSaida(new CentroCustoTO());
            if (this.nomeCentroCustoDefaultLancAutoSaida == null || this.nomeCentroCustoDefaultLancAutoSaida.isEmpty()) {
                getConfFinanceiro().setCentroCustoLancamentoAutomaticoSaida(new CentroCustoTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void setarPlanoContasLancamentoAutoSaidaVazio() {
        try {
            getConfFinanceiro().setPlanoContasLancamentoAutomaticoSaida(new PlanoContaTO());
            if (this.nomePlanoDefaultLancAutoSaida == null || this.nomePlanoDefaultLancAutoSaida.isEmpty()) {
                getConfFinanceiro().setPlanoContasLancamentoAutomaticoSaida(new PlanoContaTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void setarPlanoContasLancamentoAutoEntradaVazio() {
        try {
            getConfFinanceiro().setPlanoContasLancamentoAutomaticoEntrada(new PlanoContaTO());
            if (this.nomePlanoDefaultLancAutoEntrada == null || this.nomePlanoDefaultLancAutoEntrada.isEmpty()) {
                getConfFinanceiro().setPlanoContasLancamentoAutomaticoEntrada(new PlanoContaTO());
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void carregarDadosLancamentoAutomatico() {
        try {
            nomePlanoDefaultLancAutoSaida = getConfFinanceiro().getPlanoContasLancamentoAutomaticoSaida().getDescricao();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            nomePlanoDefaultLancAutoEntrada = getConfFinanceiro().getPlanoContasLancamentoAutomaticoEntrada().getDescricao();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            nomeCentroCustoDefaultLancAutoEntrada = getConfFinanceiro().getCentroCustoLancamentoAutomaticoEntrada().getDescricao();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            nomeCentroCustoDefaultLancAutoSaida = getConfFinanceiro().getCentroCustoLancamentoAutomaticoSaida().getDescricao();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public Object realizarConsultaLog() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = confFinanceiro.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + "ConfiguracaoSistema" + "_tituloForm"));

        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                confFinanceiro.getCodigo(), null);

        setOncompleteLog("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");

        return true;
    }

    public void registrarMarcarDesmarcarConfiguracaoUsarMovimentacaoContas() {
        if (confFinanceiro.getUsarMovimentacaoContas()) {
            setDesmarcouUsarMovimentacaoContas(false);
        } else {
            setDesmarcouUsarMovimentacaoContas(true);
        }
    }

    public String getNomePlanoDefaultTaxaPix() {
        return nomePlanoDefaultTaxaPix;
    }

    public void setNomePlanoDefaultTaxaPix(String nomePlanoDefaultTaxaPix) {
        this.nomePlanoDefaultTaxaPix = nomePlanoDefaultTaxaPix;
    }

    public String getNomeCentroCustoDefaultTaxaPix() {
        return nomeCentroCustoDefaultTaxaPix;
    }

    public void setNomeCentroCustoDefaultTaxaPix(String nomeCentroCustoDefaultTaxaPix) {
        this.nomeCentroCustoDefaultTaxaPix = nomeCentroCustoDefaultTaxaPix;
    }

    public boolean isDesmarcouUsarMovimentacaoContas() {
        return desmarcouUsarMovimentacaoContas;
    }

    public void setDesmarcouUsarMovimentacaoContas(boolean desmarcouUsarMovimentacaoContas) {
        this.desmarcouUsarMovimentacaoContas = desmarcouUsarMovimentacaoContas;
    }

    public List<SelectItem> getListaComboConta() {
        if (listaComboConta == null) {
            listaComboConta = new ArrayList<SelectItem>();
        }
        return listaComboConta;
    }

    public void setListaComboConta(List<SelectItem> listaComboConta) {
        this.listaComboConta = listaComboConta;
    }

    public List<PessoaVO> executarAutocompleteConsultaFavorecido(Object suggest) throws Exception {
        String pref = (String) suggest;
        ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = getFacade().getConfiguracaoFinanceiro().consultar();
        ArrayList<PessoaVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                        consultarTodosPessoaComLimiteFinanceiro(0, false, Uteis.NIVELMONTARDADOS_TIPOPESSOA);
            } else {
                result = (ArrayList<PessoaVO>) getFacade().getPessoa().
                        consultarPorNomePessoaComLimiteFinanceiro(0, pref, false, false,
                                Uteis.NIVELMONTARDADOS_TIPOPESSOA, configuracaoFinanceiroVO.isContaPagarReceberColabInativo());

            }
        } catch (Exception ex) {
            result = (new ArrayList<PessoaVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarFavorecidoDebitoSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        if (pessoaVO != null) {
            getConfFinanceiro().setFavorecidomovimentacaoautomaticaDebito(pessoaVO);
        }else {
            getConfFinanceiro().setFavorecidomovimentacaoautomaticaDebito(new PessoaVO());
        }
    }

    public void selecionarFavorecidoCreditoSuggestionBox() throws Exception {
        PessoaVO pessoaVO = (PessoaVO) request().getAttribute("result");
        if (pessoaVO != null) {
            getConfFinanceiro().setFavorecidomovimentacaoautomaticaCredito(pessoaVO);
        }
    }

    public void selecionarContaMovimentacaoAutomaticaDebito() throws Exception {
        try {
            if (this.contaMovimentacaoAutomaticaDebito != null) {
                getConfFinanceiro().setContamovimentacaoautomaticadebito(contaMovimentacaoAutomaticaDebito);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarMovimentacaoAutomaticaRecebiveisConciliacao() throws Exception {
        try {
            if(movimentacaoAutomaticaRecebiveisConciliacao){
                getConfFinanceiro().setMovimentacaoAutomaticaRecebiveisConciliacao(movimentacaoAutomaticaRecebiveisConciliacao);
            }else{
                getConfFinanceiro().setMovimentacaoAutomaticaRecebiveisConciliacao(movimentacaoAutomaticaRecebiveisConciliacao);
                getConfFinanceiro().setDescricaomovimentacaoautomaticaDebito("");
                getConfFinanceiro().setDescricaomovimentacaoautomaticaCredito("");
                getConfFinanceiro().setFavorecidomovimentacaoautomaticaDebito(new PessoaVO());
                getConfFinanceiro().setFavorecidomovimentacaoautomaticaCredito(new PessoaVO());
                getConfFinanceiro().setContamovimentacaoautomaticadebito(new ContaVO());
                getConfFinanceiro().setContamovimentacaoautomaticacredito(new ContaVO());
            }

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarContaMovimentacaoAutomaticaCredito() throws Exception {
        try {
            if (this.contaMovimentacaoAutomaticaCredito != null) {
                getConfFinanceiro().setContamovimentacaoautomaticacredito(contaMovimentacaoAutomaticaCredito);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public boolean isApresentarConfigCentralEventos() {
        String modulos = (String) JSFUtilities.getFromSession("modulosHabilitados");
        return modulos != null && modulos.contains(Modulo.CENTRAL_DE_EVENTOS.getSiglaModulo());
    }

}

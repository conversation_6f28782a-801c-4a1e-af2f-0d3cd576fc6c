/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoConsultaVelocimetro;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.DFSinteticoDWVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.ReceitaSinteticoDWVO;
import negocio.comuns.financeiro.ResumoFormaPagamentoTO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.VelocimetroTO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class BIFinanceiroControle  extends SuperControle{

    private static final int QUANTIDADE_MESES_PERIODO_INFERIOR_RF_PADRAO = 5;
    private List<GenericoTO> contasPagar = new ArrayList<GenericoTO>();
    private List<GenericoTO> contasReceber = new ArrayList<GenericoTO>();
    private List<GenericoTO> resumoContas = new ArrayList<GenericoTO>();
    private List<TipoContaVO> resumoContasTela = new ArrayList<TipoContaVO>();
    private String dadosGrafico = "";
    private String dadosGraficoReceita = "";
    private boolean agruparTipoConta = false;
    private int totalReceber = 0;
    private int totalPagar = 0;
    private Double valorReceber = 0.0;
    private Double valorPagar = 0.0;
    private String dataAtualizacaoDados = "";
    private VelocimetroTO velocimetro = new VelocimetroTO();
    private String labelVelocimetro = "";
    private boolean mostrarPagar = true;
    private boolean jaCalculado = false;
    private boolean receberCalculado = false;
    public Date dataReceita = Calendario.hoje();
    public ReceitaSinteticoDWVO receitaSint;
    public boolean metaNaoAberta = false;
    public boolean metaZerada = false;
    public Integer codigoMeta;
    public ConfiguracaoFinanceiroVO configuracaoFinan = null;
    private EmpresaVO empresaReceitaFormas;
    protected List listaSelectItemEmpresaReceitaFormas;
    private Date dataInicioBiRF = Uteis.obterDataAnterior(QUANTIDADE_MESES_PERIODO_INFERIOR_RF_PADRAO);
    private Date dataFinalBiRF = Calendario.hoje();
    private String msgAlert;



    public boolean isJaCalculado() {
        return jaCalculado;
    }

    public void setJaCalculado(boolean jaCalculado) {
        this.jaCalculado = jaCalculado;
    }

    public boolean getMostrarPagar() {
        return mostrarPagar;
    }

    public boolean getMostrarReceber() {
        return !mostrarPagar;
    }

    public void setarMostrar(){
        mostrarPagar = !mostrarPagar;
    }

    public int getIntervalo(){
        return new Double(velocimetro.getValorFinal() / 10.0).intValue();
    }

    public int getIntervaloBarras(){
        return new Double(velocimetro.getValorFinal() / 20.0).intValue();
    }

    public List<SelectItem> getTiposVelocimetro(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for(TipoConsultaVelocimetro tipo : TipoConsultaVelocimetro.values()){
            lista.add(new SelectItem(tipo.getCodigo(), tipo.getLabel()));
        }
        return lista;
    }

    public List<SelectItem> getMeses(){
        List<SelectItem> lista = new ArrayList<SelectItem>();
        for(DFSinteticoDWVO tipo : velocimetro.getValores()){
            Mes mes = Mes.getMesPeloCodigo(tipo.getMes());
            lista.add(new SelectItem(mes.getCodigo(), mes.getDescricao()));
        }
        return lista;
    }

    public void atualizarDadosSinteticos() {
        try {
            setMsgAlert("");
            getFacade().getFinanceiro().getDFSinteticoDW().gerarDadosSintetico(getEmpresaLogado().getCodigo(), false, null);
            inicializarVelocimetro();
            String result = getFacade().getFinanceiro().getDFSinteticoDW().consultarParaGrafico(getEmpresaLogado().getCodigo(), null, null);
            dataAtualizacaoDados = result.substring(0, result.indexOf("/horaAtua/"));
            atribuirDadosGraficoRF(result);
            montarMsgAlert("Dados atualizados com sucesso!");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMsgAlert(getMensagemDetalhada());
        }
    }

    public void atribuirDadosGraficoRF(String result) {
        dadosGrafico = result.substring(result.indexOf("/horaAtua/")).replace("/horaAtua/", "");
    }

    public void atualizarDadosGraficoRF(){
        try {
            setMensagemDetalhada("");
            setMsgAlert("");
            validarDatas();
            String result = getFacade().getFinanceiro().getDFSinteticoDW().consultarParaGrafico(getEmpresaLogado().getCodigo(), getDataInicioBiRF(), getDataFinalBiRF());
            atribuirDadosGraficoRF(result);
        }catch(Exception e){
            setMensagemDetalhada(e);
            setMsgAlert(getMensagemDetalhada());
        }
    }

    private void validarDatas() throws Exception {
        if (getDataInicioBiRF() != null && getDataFinalBiRF() == null) {
            throw new Exception("Informe o mês final.");
        }
        if (getDataInicioBiRF() == null && getDataFinalBiRF() != null) {
            throw new Exception("Informe o mês inicial.");
        }

        if (getDataInicioBiRF() == null && getDataFinalBiRF() == null) {
            throw new Exception("Informe um intervalo de meses");
        }

        if (getDataInicioBiRF() != null && getDataInicioBiRF().after(getDataFinalBiRF())) {
            throw new Exception("Data Inicial não pode ser superior a Data Final.");
        }

        if(Uteis.getMesesEntreDatas(getDataInicioBiRF(), getDataFinalBiRF()).size() <= 2 || Uteis.getMesesEntreDatas(getDataInicioBiRF(), getDataFinalBiRF()).size() > 6){
            throw new Exception("O intervalo entre os meses deve ser maior que 2 e menor igual a 6.");
        }

    }

    public String btnBIFinanceiroAction() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.BI_FINANCEIRO);
        return inicializar();
    }

    public String inicializar() throws Exception{
        try{
            //últimos 6 meses é o padrão de inicialização
            if (dataInicioBiRF == null) {
                dataInicioBiRF =  Uteis.obterPrimeiroDiaMes(Calendario.subtrairMeses(Calendario.hoje(), QUANTIDADE_MESES_PERIODO_INFERIOR_RF_PADRAO));
                dataFinalBiRF = Uteis.obterUltimoDiaMes(Calendario.hoje());
            } else {
                dataInicioBiRF = Uteis.obterPrimeiroDiaMes(dataInicioBiRF);
                dataFinalBiRF = Uteis.obterUltimoDiaMes(dataFinalBiRF);
            }
            notificarRecursoEmpresa(RecursoSistema.FIN_BI);
            getFuncionalidadeControle().setarFuncionalidade();
            atualizarContasPagar();
            atualizarResumoContas();
            if (!jaCalculado) {
                inicializarVelocimetro();
                String result = getFacade().getFinanceiro().getDFSinteticoDW().consultarParaGrafico(getEmpresaLogado().getCodigo(), dataInicioBiRF, dataFinalBiRF);
                dataAtualizacaoDados = result.substring(0, result.indexOf("/horaAtua/"));
                atribuirDadosGraficoRF(result);
                jaCalculado = true;
                gerarReceitaPorFormaPagamentoPrimeiraVez();
                atualizarContasReceberPrimeiraVez();
            }
            if(configuracaoFinan == null){
                configuracaoFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            }
        }catch(Exception e){
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
        }
        
        return "relatoriosFinan";
    }

    public void configurarVelocimetro() {
        try {
            metaNaoAberta = false;
            metaZerada = false;
            labelVelocimetro = "";
            TipoConsultaVelocimetro tipo = TipoConsultaVelocimetro.getPeloCodigo(velocimetro.getTipo());
            if (tipo == null) {
                tipo = TipoConsultaVelocimetro.RECEITA;
                velocimetro.setTipo(TipoConsultaVelocimetro.RECEITA.getCodigo());
            }
            DFSinteticoDWVO valoresMes = getValoresMes();
            List<MetaFinanceiraEmpresaVO> metas = getFacade().getMetaFinanceiraEmpresa().
                    consultarPorEmpresaAnoMesDescricao(getEmpresaLogado().getCodigo(), valoresMes.getAno(), valoresMes.getMes(), "", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (metas.isEmpty()) {
                velocimetro.setValorFinalAmarelo(0.0);
                velocimetro.setValorInicialAmarelo(0.0);
                velocimetro.setValorFinal(0.0);
                velocimetro.setValorPonteiro(0.0);
                metaNaoAberta = true;
                return;
            }
            MetaFinanceiraEmpresaVO meta = metas.get(0);
            codigoMeta = meta.getCodigo();
            switch (tipo) {
                case RECEITA:
                    metaZerada = meta.getReceitaVeloc().equals(0.0);
                    velocimetro.setValorFinalAmarelo(meta.getReceitaVeloc());
                    velocimetro.setValorInicialAmarelo(meta.getReceitaVeloc() - (meta.getReceitaVeloc() * 0.3));
                    if(meta.getReceitaVeloc() * 2 < valoresMes.getReceita()){
                        velocimetro.setValorFinal(Uteis.arredondarForcando2CasasDecimais(Uteis.arredondarMod10(valoresMes.getReceita() + (valoresMes.getReceita() * 0.1))));
                    } else {
                        velocimetro.setValorFinal(Uteis.arredondarMod10(meta.getReceitaVeloc() * 2));
                    }
                    velocimetro.setValorPonteiro(valoresMes.getReceita());
                    velocimetro.setCor1("#ea3838");
                    velocimetro.setCor2("#36CE1B");
                    labelVelocimetro = "Meta: "+Formatador.formatarValorMonetario(meta.getReceitaVeloc())
                            +"\\nAtingido: "+Formatador.formatarValorMonetario(valoresMes.getReceita());
                    break;
                case FATURAMENTO:
                    metaZerada = meta.getFaturamentoVeloc().equals(0.0);
                    velocimetro.setValorFinalAmarelo(meta.getFaturamentoVeloc());
                    velocimetro.setValorInicialAmarelo(meta.getFaturamentoVeloc() - (meta.getFaturamentoVeloc() * 0.3));
                    if(meta.getFaturamentoVeloc() * 2 <= valoresMes.getFaturamento()){
                        velocimetro.setValorFinal(Uteis.arredondarForcando2CasasDecimais(Uteis.arredondarMod10(valoresMes.getFaturamento() + (valoresMes.getFaturamento() * 0.1))));
                    } else {
                        velocimetro.setValorFinal(Uteis.arredondarMod10(meta.getFaturamentoVeloc() * 2));
                    }
                    velocimetro.setValorPonteiro(valoresMes.getFaturamento());
                    velocimetro.setCor1("#ea3838");
                    velocimetro.setCor2("#36CE1B");
                    labelVelocimetro = "Meta: "+Formatador.formatarValorMonetario(meta.getFaturamentoVeloc())
                            +"\\nAtingido: "+Formatador.formatarValorMonetario(valoresMes.getFaturamento());
                    break;
                case DESPESA:
                    metaZerada = meta.getDespesaVeloc().equals(0.0);
                    velocimetro.setValorFinalAmarelo(valoresMes.getReceita()< meta.getDespesaVeloc() ?
                        (meta.getDespesaVeloc() + (meta.getDespesaVeloc() * 0.3)) : valoresMes.getReceita());
                    velocimetro.setValorInicialAmarelo(meta.getDespesaVeloc());
                    velocimetro.setValorPonteiro(valoresMes.getDespesa() < 0.0 ? (valoresMes.getDespesa()*-1) : valoresMes.getDespesa());
                    Double comparar = velocimetro.getValorFinalAmarelo() > velocimetro.getValorPonteiro() ? velocimetro.getValorFinalAmarelo() : velocimetro.getValorPonteiro();
                    if(meta.getDespesaVeloc() * 2 <=  comparar){
                        velocimetro.setValorFinal(Uteis.arredondarForcando2CasasDecimais(Uteis.arredondarMod10(comparar + comparar * 0.1)));
                    } else {
                        velocimetro.setValorFinal(Uteis.arredondarMod10(meta.getDespesaVeloc() * 2));
                    }
                    
                   
                    velocimetro.setCor2("#ea3838");
                    velocimetro.setCor1("#36CE1B");
                    labelVelocimetro = "Meta: "+Formatador.formatarValorMonetario(meta.getDespesaVeloc())
                            +"\\nAtingido: "+Formatador.formatarValorMonetario(
                            (valoresMes.getDespesa() < 0.0 ? (valoresMes.getDespesa()*-1) : valoresMes.getDespesa()));
                    break;
            }

        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
        }
    }


    public DFSinteticoDWVO getValoresMes(){
        for(DFSinteticoDWVO df : velocimetro.getValores()){
            if(velocimetro.getMes() == df.getMes()){
                return df;
            }
        }
        if(!velocimetro.getValores().isEmpty()){
            return velocimetro.getValores().get(0);
        }
        return new DFSinteticoDWVO();
    }
    public void inicializarVelocimetro(){
        velocimetro = new VelocimetroTO();
        velocimetro.setMes(Uteis.getMesData(Calendario.hoje()));
        velocimetro.setTipo(TipoConsultaVelocimetro.RECEITA.getCodigo());
        try {
            velocimetro.setValores(getFacade().getFinanceiro().getDFSinteticoDW().consultar(getEmpresaLogado().getCodigo()));
            configurarVelocimetro();
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
        }
    }

    public void totalizarReceber(){
        valorReceber = 0.0;
        totalReceber = contasReceber.size();
        for(GenericoTO conta : contasReceber){
            valorReceber += conta.getValor();
        }
    }
    
    public void totalizarPagar(){
        valorPagar = 0.0;
        totalPagar = contasPagar.size();
        for(GenericoTO conta : contasPagar){
            valorPagar += conta.getValor();
        }
    }

    public void atualizarContasPagar(){
        contasPagar = atualizarContas(Calendario.hoje(), TipoOperacaoLancamento.PAGAMENTO);
        totalizarPagar();
    }
    public void atualizarContasReceber(){
        contasReceber = atualizarContas(Calendario.hoje(), TipoOperacaoLancamento.RECEBIMENTO);
        totalizarReceber();
    }

    public void atualizarContasReceberPrimeiraVez(){
        if(!receberCalculado){
            atualizarContasReceber();
            receberCalculado = true;
        }
    }

    public List<GenericoTO> atualizarContas(Date data, TipoOperacaoLancamento tipo){
        try{
            limparMsg();
            return getFacade().getFinanceiro().getMovConta().consultarGenerico(tipo,
                    Uteis.obterPrimeiroEUltimoDiaSemana(true, data), Uteis.obterPrimeiroEUltimoDiaSemana(false,data), getEmpresaLogado().getCodigo());
        }catch(Exception e){
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
            return new ArrayList<GenericoTO>();
        }
    }


     public List<GenericoTO> consultarRecebiveis(Date inicio, Date fim, Integer empresa) throws Exception {

        GestaoRecebiveisControle gestaoRecebiveisControle = (GestaoRecebiveisControle) context().getExternalContext().getSessionMap().get(GestaoRecebiveisControle.class.getSimpleName());
        if (gestaoRecebiveisControle == null) {
            gestaoRecebiveisControle = new GestaoRecebiveisControle();
        }
            gestaoRecebiveisControle.novo(false);
            //Lançamento é o mesmo que lançamento no gestão de recebíveis.

            gestaoRecebiveisControle.setDataInicialLancamento(null);
            gestaoRecebiveisControle.setDataFinalLancamento(null);
            gestaoRecebiveisControle.setDataInicialCompensacao(inicio);
            gestaoRecebiveisControle.setDataFinalCompensacao(fim);
            gestaoRecebiveisControle.getEmpresa().setCodigo(empresa);
            gestaoRecebiveisControle.consultarRecebiveis();
            //Data de quitação é o mesmo que compensação na gestão de recebíveis.

            context().getExternalContext().getSessionMap().put(GestaoRecebiveisControle.class.getSimpleName(), gestaoRecebiveisControle);


            return montarMovContaVORecebiveis(gestaoRecebiveisControle.getListaResumo());
    }
    public List<GenericoTO> montarMovContaVORecebiveis(List<ResumoFormaPagamentoTO> lista) {
        List<GenericoTO> marx = new ArrayList<GenericoTO>();
        for (ResumoFormaPagamentoTO rfp : lista) {
            if (adicionarFP(rfp.getTipo1())) {
                marx.add(new GenericoTO(rfp.getDescricao1(), rfp.getDescricao1(), 0, rfp.getTotal1()));
            }
            if (adicionarFP(rfp.getTipo2())) {
                marx.add(new GenericoTO(rfp.getDescricao2(), rfp.getDescricao2(), 0, rfp.getTotal2()));
            }
            if (adicionarFP(rfp.getTipo3())) {
                marx.add(new GenericoTO(rfp.getDescricao3(), rfp.getDescricao3(), 0, rfp.getTotal3()));
            }
            if (adicionarFP(rfp.getTipo4())) {
                marx.add(new GenericoTO(rfp.getDescricao4(), rfp.getDescricao4(), 0, rfp.getTotal4()));
            }
        }
        return Ordenacao.ordenarLista(marx, "label");
    }

     private boolean adicionarFP(String tipo){
         return (tipo.equals(TipoFormaPagto.AVISTA.getSigla())
                 || tipo.equals(TipoFormaPagto.CARTAOCREDITO.getSigla())
                 || tipo.equals(TipoFormaPagto.CARTAODEBITO.getSigla())
                 || tipo.equals(TipoFormaPagto.CHEQUE.getSigla()));
     }

    public void agruparPorTipoConta(){
            resumoContasTela = new ArrayList<TipoContaVO>();
        if(agruparTipoConta){
            Map<String, TipoContaVO> mapa = new HashMap<String, TipoContaVO>();
            for(GenericoTO conta : resumoContas){
                TipoContaVO get = mapa.get(conta.getCodigoString());
                if(get == null){
                    get = new TipoContaVO();
                    get.setDescricao(conta.getCodigoString());
                    get.setValor(conta.getValor());
                    mapa.put(conta.getCodigoString(), get);
                }else{
                    get.setValor(get.getValor() + conta.getValor());
                }
            }
            
            resumoContasTela.addAll(mapa.values());
        }else{
            Map<String, TipoContaVO> mapa = new HashMap<String, TipoContaVO>();
            Ordenacao.ordenarLista(resumoContas, "label");
            for(GenericoTO conta : resumoContas){
                TipoContaVO get = mapa.get(conta.getCodigoString());
                if(get == null){
                    get = new TipoContaVO();
                    get.setDescricao(conta.getCodigoString());
                    get.getGenericos().add(conta);
                    mapa.put(conta.getCodigoString(), get);
                }else{
                    get.getGenericos().add(conta);
                }
            }
            resumoContasTela.addAll(mapa.values());
            
        }
            Ordenacao.ordenarLista(resumoContasTela, "descricao");

    }
    public void atualizarResumoContas(){
        try{
            limparMsg();
            ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class.getSimpleName());
            if(cfg == null){
                cfg = new ConfiguracaoFinanceiroControle();
                JSFUtilities.storeOnSession(ConfiguracaoFinanceiroControle.class.getSimpleName(),cfg);
            }
            Integer empresa;
            if(cfg.getConfFinanceiro().isPermitirContaOutraUnidade() && validarPermissaoAbrirConsultarCaixaParaTodasEmpresa()){
                empresa = null;
            }else{
                empresa = getEmpresaLogado().getCodigo();
            }
            resumoContas = getFacade().getFinanceiro().getConta().consultarGenerico(Calendario.hoje(), empresa);
            agruparPorTipoConta();
        }catch(Exception e){
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
        }
    }

     public void editarLancamentoPopUp() throws Exception{
    	MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
    	GenericoTO obj = (GenericoTO) context().getExternalContext().getRequestMap().get("conta");
    	MovContaVO movConta = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
        movContaControle.setOrigemConsulta("bi");
        movContaControle.preparaEdicao(movConta);
        if (movContaControle.isContasPagar()) {
            notificarRecursoEmpresa(RecursoSistema.NOVA_CONTA_PAGAR);
        } else if (movContaControle.isContasReceber()) {
            notificarRecursoEmpresa(RecursoSistema.NOVA_CONTAS_RECEBER);
        }
    }

    public List<GenericoTO> getContasReceber() {
        return contasReceber;
    }

    public void setContasReceber(List<GenericoTO> contasReceber) {
        this.contasReceber = contasReceber;
    }

    public List<GenericoTO> getContasPagar() {
        return contasPagar;
    }

    public void setContasPagar(List<GenericoTO> contasPagar) {
        this.contasPagar = contasPagar;
    }

    public List<GenericoTO> getResumoContas() {
        return resumoContas;
    }

    public void setResumoContas(List<GenericoTO> resumoContas) {
        this.resumoContas = resumoContas;
    }

    public String getDadosGrafico() {
        return dadosGrafico;
    }

    public void setDadosGrafico(String dadosGrafico) {
        this.dadosGrafico = dadosGrafico;
    }

    public List<TipoContaVO> getResumoContasTela() {
        return resumoContasTela;
    }

    public void setResumoContasTela(List<TipoContaVO> resumoContasTela) {
        this.resumoContasTela = resumoContasTela;
    }

    public boolean isAgruparTipoConta() {
        return agruparTipoConta;
    }

    public void setAgruparTipoConta(boolean agruparTipoConta) {
        this.agruparTipoConta = agruparTipoConta;
    }

    public int getTotalPagar() {
        return totalPagar;
    }

    public void setTotalPagar(int totalPagar) {
        this.totalPagar = totalPagar;
    }

    public int getTotalReceber() {
        return totalReceber;
    }

    public void setTotalReceber(int totalReceber) {
        this.totalReceber = totalReceber;
    }

    public Double getValorPagar() {
        return valorPagar;
    }

    public void setValorPagar(Double valorPagar) {
        this.valorPagar = valorPagar;
    }

    public Double getValorReceber() {
        return valorReceber;
    }

    public void setValorReceber(Double valorReceber) {
        this.valorReceber = valorReceber;
    }

    public String getValorReceber_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorReceber());
    }

    public String getValorPagar_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorPagar());
    }

    public String getDataAtualizacaoDados() {
        return dataAtualizacaoDados;
    }

    public void setDataAtualizacaoDados(String dataAtualizacaoDados) {
        this.dataAtualizacaoDados = dataAtualizacaoDados;
    }

    public VelocimetroTO getVelocimetro() {
        return velocimetro;
    }

    public void setVelocimetro(VelocimetroTO velocimetro) {
        this.velocimetro = velocimetro;
    }

    public String getLabelVelocimetro() {
        return labelVelocimetro;
    }

    public void setLabelVelocimetro(String labelVelocimetro) {
        this.labelVelocimetro = labelVelocimetro;
    }

    public void gerarReceitaPorFormaPagamentoPrimeiraVez() throws Exception{
        setEmpresaReceitaFormas(getEmpresaLogado());
        montarListaSelectItemEmpresa();
        if(UteisValidacao.emptyString(dadosGraficoReceita)){
            ReceitaSinteticoDWVO receita = getFacade().getFinanceiro().getDFSinteticoDW().consultarReceitaPorFormaPagamento(getEmpresaReceitaFormas().getCodigo());
            montarDadosGraficoReceita(receita);
        }
    }

    public void gerarReceitaPorFormaPagamento() throws Exception{
        try{
            ReceitaSinteticoDWVO receita = getFacade().getFinanceiro().getDFSinteticoDW().gerarReceitaPorFormaPagamento(getEmpresaReceitaFormas().getCodigo(), dataReceita);
            montarDadosGraficoReceita(receita);
        }catch(Exception ex){
            montarDadosGraficoReceita(new ReceitaSinteticoDWVO());
        }
    }
    
    public void montarDadosGraficoReceita(ReceitaSinteticoDWVO receita){
        receitaSint = receita;
        dadosGraficoReceita = "var chartData2 = [";
        StringBuilder grafico = new StringBuilder();
        if (UteisValidacao.notEmptyNumber(receita.getCartaoCredito())){
            grafico.append(",{\"tipo\": \"C. crédito\",\"sigla\": \"CA\", \"valor\": \"" + receita.getCartaoCredito() + "\", \"color\": \"#F26F04\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getCartaoDebito())){
            grafico.append(",{\"tipo\": \"C. débito\",\"sigla\": \"CD\", \"valor\": \"" + receita.getCartaoDebito() + "\", \"color\": \"#F2EB04\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getChequePrazo())){
            grafico.append(",{\"tipo\": \"Ch. prazo\",\"sigla\": \"CHP\", \"valor\": \"" + receita.getChequePrazo() + "\", \"color\": \"#1A62CF\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getChequeVista())){
            grafico.append(",{\"tipo\": \"Ch. vista\",\"sigla\": \"CHV\", \"valor\": \"" + receita.getChequeVista() + "\", \"color\": \"#3BB0F3\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getDinheiro())){
            grafico.append(",{\"tipo\": \"Dinheiro\",\"sigla\": \"DI\", \"valor\": \"" + receita.getDinheiro() + "\", \"color\": \"#19FC43\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getPix())){
            grafico.append(",{\"tipo\": \"Pix\",\"sigla\": \"PX\", \"valor\": \"" + receita.getPix() + "\", \"color\": \"#006400\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getTransferenciaBancaria())){
            grafico.append(",{\"tipo\": \"Transferência Bancaria\",\"sigla\": \"TB\", \"valor\": \"" + receita.getTransferenciaBancaria() + "\", \"color\": \"#bdb76b\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getBoleto())){
            grafico.append(",{\"tipo\": \"Boleto\",\"sigla\": \"BB\", \"valor\": \"" + receita.getBoleto() + "\", \"color\": \"#A0522D\"} ");
        }
        if (UteisValidacao.notEmptyNumber(receita.getOutros())){
            grafico.append(",{\"tipo\": \"Outros\",\"sigla\": \"OU\", \"valor\": \"" + receita.getOutros() + "\", \"color\": \"#C40D87\"} ");
        }
        dadosGraficoReceita = dadosGraficoReceita + (grafico.toString().isEmpty() ?
                "{\"tipo\": \"Nenhum pagamento\",\"sigla\": \"Nenhum pagamento\", \"valor\": \"0.0\", \"color\": \"#C40D87\"}" :
                grafico.toString().replaceFirst("\\,\\{", "{")) + " ];";
    }

    public String getDadosGraficoReceita() {
        return dadosGraficoReceita;
    }

    public void setDadosGraficoReceita(String dadosGraficoReceita) {
        this.dadosGraficoReceita = dadosGraficoReceita;
    }

    public Date getDataReceita() {
        return dataReceita;
    }

    public void setDataReceita(Date dataReceita) {
        this.dataReceita = dataReceita;
    }

    public boolean getMostrarDataAtualizacaoReceita(){
        return receitaSint != null && receitaSint.getAno().equals(Uteis.getAnoData(Calendario.hoje()))
                && receitaSint.getMes().equals(Uteis.getMesData(Calendario.hoje()));
    }

    public ReceitaSinteticoDWVO getReceitaSint() {
        return receitaSint;
    }

    public void setReceitaSint(ReceitaSinteticoDWVO receitaSint) {
        this.receitaSint = receitaSint;
    }

    public boolean isReceberCalculado() {
        return receberCalculado;
    }

    public void setReceberCalculado(boolean receberCalculado) {
        this.receberCalculado = receberCalculado;
    }

    public boolean getMetaNaoAberta() {
        return metaNaoAberta;
    }

    public void setMetaNaoAberta(boolean metaNaoAberta) {
        this.metaNaoAberta = metaNaoAberta;
    }

    public boolean getMetaZerada() {
        return metaZerada;
    }

    public void setMetaZerada(boolean metaZerada) {
        this.metaZerada = metaZerada;
    }

    public Integer getCodigoMeta() {
        return codigoMeta;
    }

    public void setCodigoMeta(Integer codigoMeta) {
        this.codigoMeta = codigoMeta;
    }

    public ConfiguracaoFinanceiroVO getConfiguracaoFinan() {
        return configuracaoFinan;
    }

    public void setConfiguracaoFinan(ConfiguracaoFinanceiroVO configuracaoFinan) {
        this.configuracaoFinan = configuracaoFinan;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (getEmpresaReceitaFormas().getCodigo() == 0 || getUsuarioLogado().getAdministrador()) {
                List objs = new ArrayList();
                objs.add(new SelectItem(0, ""));
                List resultadoConsulta = consultarPorNomeEmpresa("");
                Iterator i = resultadoConsulta.iterator();
                while (i.hasNext()) {
                    EmpresaVO obj = (EmpresaVO) i.next();
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
                setListaSelectItemEmpresaReceitaFormas(objs);
            }
        } catch (Exception e) {
            setListaSelectItemEmpresaReceitaFormas(new ArrayList());
            e.printStackTrace();
        }
    }

    public List consultarPorNomeEmpresa(String nomePrm) throws Exception {
        return getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }

    public EmpresaVO getEmpresaReceitaFormas() {
        return empresaReceitaFormas;
    }

    public void setEmpresaReceitaFormas(EmpresaVO empresaReceitaFormas) {
        this.empresaReceitaFormas = empresaReceitaFormas;
    }

    public List getListaSelectItemEmpresaReceitaFormas() {
        return listaSelectItemEmpresaReceitaFormas;
    }

    public void setListaSelectItemEmpresaReceitaFormas(List listaSelectItemEmpresaReceitaFormas) {
        this.listaSelectItemEmpresaReceitaFormas = listaSelectItemEmpresaReceitaFormas;
    }
    
    public boolean validarPermissaoAbrirConsultarCaixaParaTodasEmpresa() throws Exception {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "AbrirConsultarHistCaixaAdmTodasEmpresas", "9.25 - Abrir/Consultar Caixa-Administrativo para todas as Empresas");
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    public Date getDataInicioBiRF() {
        return dataInicioBiRF;
    }

    public void setDataInicioBiRF(Date dataInicioBiRF) {
        this.dataInicioBiRF = dataInicioBiRF;
    }

    public Date getDataFinalBiRF() {
        return dataFinalBiRF;
    }

    public void setDataFinalBiRF(Date dataFinalBiRF) {
        this.dataFinalBiRF = dataFinalBiRF;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Gerar dados Gráfico",
                "Isso pode levar alguns minutos. Deseja continuar?",
                this, "atualizarDadosSinteticos", "", "", "", "grupoBtnExcluir,grupoBtnAtualizarVel,panelBIFinanceiro");
    }

}

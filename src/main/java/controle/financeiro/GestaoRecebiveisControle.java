package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.StatusMovimentacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.ModuloAberto;
import controle.arquitetura.security.LoginControle;
import controle.basico.ClienteControle;
import importador.ExportadorExcel;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCredencialStoneEnum;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.filtros.FiltroGestaoRecebiveisTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryPoolMXBean;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class GestaoRecebiveisControle extends SuperControleRelatorio {

    private UsuarioVO operadorCaixa = new UsuarioVO();
    private String nome = "";
    private String nomeTerceiro = "";
    private EmpresaVO empresaRel = new EmpresaVO();
    private UsuarioVO usuarioResponsavel = null;
    private Date dataInicialCompensacao = null;
    private String horaInicialCompensacao = "";
    private Date dataFinalCompensacao = null;
    private String horaFinalCompensacao = "";
    private Date dataInicialLancamento = Calendario.hoje();
    private String horaInicialLancamento = "";
    private Date dataFinalLancamento = Calendario.hoje();
    private String horaFinalLancamento = "";
    private Date dataInicialConciliacao = Calendario.hoje();
    private Date dataFinalConciliacao = Calendario.hoje();
    private List<ResumoFormaPagamentoTO> listaResumo = new ArrayList<ResumoFormaPagamentoTO>();
    private List<MovPagamentoVO> listaOutros = new ArrayList<MovPagamentoVO>();
    private List<ChequeTO> listaCheques = new ArrayList<ChequeTO>();
    private List<CartaoCreditoTO> listaCartoes = new ArrayList<CartaoCreditoTO>();
    private List<CartaoCreditoTO> listaCartoesOriginal = new ArrayList<CartaoCreditoTO>();
    //lista referente a todos os dados da consulta
    private List<MovPagamentoVO> listaConsultaRecebiveis = new ArrayList<MovPagamentoVO>();
    //lista de cheques que tem mais de um registro para ser representado
    private List<Integer> chequeComposicao = new ArrayList<Integer>();
    //lista de cheques que tem mais de um registro para ser representado
    private List<Integer> cartaoComposicao = new ArrayList<Integer>();
    private String escolha = "closed";
    private double totalCheques = 0.0;
    private double totalSelecionadoCheques = 0.0;
    private double totalCartoes = 0.0;
    private double totalSelecionadoCartoes = 0.0;
    private LoteVO lote = new LoteVO();
    private String tipoLista = "";
    private int qtdeTotal = 0;
    private int qtdeTotalCheques = 0;
    private int qtdeTotalCartoes = 0;
    private double valorTotal = 0.0;
    private boolean mostrarCampoEmpresa = false;
    private UsuarioVO responsavelEdicaoPagamento = null;
    private boolean todosChequesMarcados = false;
    private boolean todosCartoesMarcados = false;
    private boolean todosPagamentosMarcados = false;
    private String filtros;
    private boolean parametro1 = true;
    private boolean parametro2 = true;
    private boolean parametro3 = false;
    private boolean parametro4 = false;
    private ChequeVO chequeVO = new ChequeVO();
    private String cpf;
    private String nomeClienteContrato = "";
    private String periodoCompensacao = "";
    private String periodoLancamento = "HJ";
    //usada somente na tela de lançamentos financeiros que inclui todos os receb?veis (com lotes e sem lotes)
    private Boolean pesquisarRecebiveis = false;
    private Boolean pesquisarRecebiveisSemLote = false;
    private Boolean pesquisarRecebiveisComLote = false;
    private String onCompleteDepositar = "";
    private Boolean exibirAutorizacao = false;
    private Boolean exibirNSU = false;
    private double totalVista = 0.0;
    private int qtdeTotalVista = 0;
    private boolean contaCorrente = false;
    private Date dataNovaCompensacaoCartoes;
    private Date dataNovaCompensacaoBoletos;
    private boolean alterarCredito = false;
    private boolean imprimirDiario = false;
    private RelatorioGestaoRecebiveisTO itemImpressao = new RelatorioGestaoRecebiveisTO();
    private Boolean recebidosZW = Boolean.FALSE;
    private boolean possuiPermissaoConsultarTodasEmpresas;
    List<SelectItem> listaSelectItemsEmpresa = new ArrayList<>();
    private transient org.richfaces.component.html.HtmlDataTable dataTableCheque = null;
    private transient org.richfaces.component.html.HtmlDataTable dataTableOutro = null;
    private transient org.richfaces.component.html.HtmlDataTable dataTableCartao = null;
    private String codAutorizacao = "";
    private String codAutorizacaoedi = "";
    private String ro = "";
    private String nsu = "";
    private String numeroDocumento;
    private Integer operadoraCartao;
    private Integer adquirente;
    private Integer tipoConciliacao = TipoConciliacaoEnum.PAGAMENTOS.getCodigo();
    private List<SelectItem> centroCustos = new ArrayList<SelectItem>();
    private List<SelectItem> operadorasCartao = new ArrayList<SelectItem>();
    private List<SelectItem> adquirentes = new ArrayList<SelectItem>();
    private List<SelectItem> tiposConcilacao = new ArrayList<SelectItem>();
    private List<SelectItem> tiposConvenio = null;
    private String nomeConta = "";
    private static final int LANCAMENTO = 1;
    private int agruparPor = LANCAMENTO;
    //Totais
    private double totalEspecie = 0;
    private double totalBoleto = 0;
    private double totalDevolucoes = 0;
    private double totalNaoFisicamente = 0;
    private String matricula = "";
    private List<MovContaVO> listaDevolucoes = new ArrayList<MovContaVO>();
    private boolean chequeAvista = false;
    private boolean chequeAprazo = false;
    private boolean mostrarCancelados = false;
    private boolean considerarDataCompensacaoOriginal = false;
    private List<SelectItem> listaStatusItens;
    private StatusMovimentacaoEnum statusMovimentacao = StatusMovimentacaoEnum.TODOS;
    private FormaPagamentoVO formaPagamentoSelecionada = new FormaPagamentoVO();
    private List<HistoricoCartaoVO> listaHistoricoCartao = new ArrayList<HistoricoCartaoVO>();
    private String codigoLoteFiltro = "";
    private List<Integer> codigosListaFiltro = new ArrayList<Integer>();
    private HistoricoCartaoVO historicoCartao;
    private Integer nrPaginaRetirarLote = 10;
    //refactor tela nova
    private List<ExtratoDiarioItemVO> extratoDiario;
    private List<ExtratoDiarioItemVO> extratoDiarioCancelamentos;
    private List<ExtratoDiarioItemVO> extratoDiarioBack;
    private Map<String, ExtratoDiarioItemVO> mapaExtratoDiario;
    private boolean ok = true;
    private boolean pendencia = true;
    private boolean nao_encontrado = true;
    private boolean estornado = true;
    private boolean estornadoOperadora = true;
    private Double okValor = 0.0;
    private Double pendenciaValor = 0.0;
    private Double nao_encontradoValor = 0.0;
    private ExtratoDiarioItemVO itemSelecionado;
    private JSONArray arrayGrafico = new JSONArray();
    private Map<String, FormaPagamentoVO> mapaPagamentosParaGrafico = new HashMap<String, FormaPagamentoVO>();
    private Map<String, String> mapaFormasCores = new HashMap<String, String>();
    private List<FormaPagamentoVO> lista;
    private String selecionados = "";
    private String descricaoFp = "";
    private Double totalSelecionado = 0.0;
    private Map<Integer, ExtratoDiarioItemVO> mapa = new HashMap<Integer, ExtratoDiarioItemVO>();
    private boolean tudoZerado = false;
    private boolean mostrarZerados = false;
    private boolean visaoConciliacao = false;
    private List<MovContaVO> devolucoes;
    private ExtratoDiarioItemVO itemOperadora = null;
    private ExtratoDiarioItemVO itemZW = null;
    private TotalizadorConciliacaoDTO totalizadorConciliacaoDTO = new TotalizadorConciliacaoDTO();

    private List<SelectItem> formasPagamentoSI;
    private List<SelectItem> itensMovimentar;
    private List<SelectItem> diasMovimentar;
    private TipoFormaPagto tipoMovimentar = TipoFormaPagto.CARTAOCREDITO;
    private Date dataMovimentarExtrato = null;
    private Integer chequeSelecionado;
    private Integer cartaoSelecionado;
    private Integer pagamentoSelecionado;
    private Integer codigoConvenio;
    private Integer fpSelecionada;
    private Integer nrNaoConciliados;
    private Boolean resetou = true;
    private String ordenacaoListagem;
    private Boolean antecipados = false;
    private Boolean apresentarPagamentosCancelados = false;
    private Boolean detalharRelatorio = true;
    private Boolean exibeRetirarCartaoLotes = false;
    private Boolean exibeRetirarChequeLotes = false;
    private CentroCustoTO centroCustoTO = new CentroCustoTO();
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String[] displayIdentificadorFront;
    private String ordenaColunaPor;
    private String ordemDaColuna;
    private Date dataInicioMovimentacaoInicio = null;
    private String horaInicioMovimentacaoInicio = "00:00:00";
    private Date dataInicioMovimentacaoFim = null;
    private String horaInicioMovimentacaoFim = "23:59:59";
    private Double taxaAntecipacao = 0.0;
    private List<String> empresasSelecionadas = new ArrayList<>();
    private boolean checkboxTodasEmpresasSelecionado = false;
    private List<EmpresaVO> empresas = new ArrayList<>();

    private MovContaVO movContaVOExcluirDeposito;
    private MovContaRateioVO movContaRateioVOExcluirDeposito;
    private List<MovPagamentoVO> listaMovPagamentoExcluirDeposito = new ArrayList();
    private ListaPaginadaTO listaMovPagamentoExcluirDepositoPaginada;
    private static final Integer LISTA_PAGINADA_LIMIT = 10;
    private static final String LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO = "LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO";
    private List<MovContaVO> relacionadoPorLote = new ArrayList<MovContaVO>();
    private String abrirFecharModalExclusaoDeposito = "";
    private String abrirFecharModalPerguntaExclusaoDeposito = "";
    private boolean caixaAtualDiferenteDoCaixaDoDepositoExcluir;
    private int codCaixaAtual;
    private int codCaixaDoDepositoExcluir;

    public String getOrdenaColunaPor() {
        return ordenaColunaPor;
    }

    public void setOrdenaColunaPor(String ordenaColunaPor) {
        this.ordenaColunaPor = ordenaColunaPor;
    }

    public String getOrdemDaColuna() {
        return ordemDaColuna;
    }

    public void setOrdemDaColuna(String ordemDaColuna) {
        this.ordemDaColuna = ordemDaColuna;
    }

    public void limpaOrdemColuna(){
        this.ordenaColunaPor = null;
        this.ordemDaColuna = null;
        this.ordenacaoListagem = null;
    }

    public void ordenaColunaDataTable() {
        if (UteisValidacao.emptyString(this.ordenaColunaPor)) {
            this.ordemDaColuna = "";
        }else {
            this.ordenacaoListagem = this.ordenaColunaPor;

            if (this.listaCartoes != null) {
                if (this.ordenaColunaPor.contains("matricula")) {
                    Ordenacao.ordenarLista(this.listaCartoes, this.ordenaColunaPor, true);
                } else {
                    Ordenacao.ordenarLista(this.listaCartoes, this.ordenaColunaPor);
                }
            }else if (this.listaCheques != null) {
                if (this.ordenaColunaPor.contains("matricula")) {
                    Ordenacao.ordenarLista(this.listaCheques, this.ordenaColunaPor, true);
                } else {
                    Ordenacao.ordenarLista(this.listaCheques, this.ordenaColunaPor);
                }
            }else if (this.listaOutros != null) {
                if (this.ordenaColunaPor.contains("matricula") || this.ordenaColunaPor.contains("dataMovimento")) {
                    ordenarListaRecebiveisOutros();
                } else {
                    Ordenacao.ordenarLista(this.listaOutros, this.ordenaColunaPor);
                }
            }

            if (UteisValidacao.emptyString(this.ordemDaColuna) || this.ordemDaColuna.equals("asc")) {
                this.ordemDaColuna = "asc";
            } else if (this.ordemDaColuna.equals("desc")) {
                this.ordenacaoListagem += ":desc";
                if (this.listaCartoes != null) {
                    Collections.reverse(this.listaCartoes);
                }else if (this.listaCheques != null) {
                    Collections.reverse(this.listaCheques);
                }else if (this.listaOutros!= null) {
                    Collections.reverse(this.listaOutros);
                }
            }
        }
    }

    private void ordenarListaRecebiveisOutros() {
        if (this.ordenaColunaPor.contains("matricula")) {
            Comparator<MovPagamentoVO> comparaPelaMatricula = new Comparator<MovPagamentoVO>() {
                @Override
                public int compare(MovPagamentoVO o1, MovPagamentoVO o2) {
                    String matriculaO1 = o1.getMatriculaPagador() != null ? o1.getMatriculaPagador() : "0";
                    String matriculaO2 = o2.getMatriculaPagador() != null ? o2.getMatriculaPagador() : "0";
                    matriculaO1 = Uteis.adicionarValorEsquerda("0", matriculaO1, 10);
                    matriculaO2 = Uteis.adicionarValorEsquerda("0", matriculaO2, 10);
                    return matriculaO1.compareTo(matriculaO2);
                }
            };
            Collections.sort(this.listaOutros, comparaPelaMatricula);
        } else if (this.ordenaColunaPor.contains("dataMovimento")) {
            Comparator<MovPagamentoVO> comparaPelaDataMovimento = new Comparator<MovPagamentoVO>() {
                @Override
                public int compare(MovPagamentoVO o1, MovPagamentoVO o2) {
                    // Define ano, mês e dia para 01/01/1990 para MovPagamentoVO que a DataMovimento está Null
                    Calendar calendar = new GregorianCalendar();
                    calendar.set(Calendar.YEAR, 1990);
                    calendar.set(Calendar.MONTH, Calendar.JANUARY);
                    calendar.set(Calendar.DAY_OF_MONTH, 1);

                    Date dataO1 = o1.getDataMovimento() != null ? o1.getDataMovimento() : calendar.getTime();
                    Date dataO2 = o2.getDataMovimento() != null ? o2.getDataMovimento() : calendar.getTime();
                    return dataO1.compareTo(dataO2);
                }
            };
            Collections.sort(this.listaOutros, comparaPelaDataMovimento);
        }
    }

    public boolean possuiIntegracaoFacilitePay() throws Exception {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        return loginControle != null && loginControle.isApresentarModuloFacilitePay();
    }

    public void entrarModoConciliacao() {
        try {
            //limpar a variável aqui que guarda a origem do resumo de contas, limpar para false
            limparVariavelResumoContas();

            limparMsg();
            setMsgAlert("");

            if (!getEmpresaLogado().isFacilitePayConciliacaoCartao()) {
                this.setMsgAlert("Richfaces.showModalPanel('modalPropagandaFaciliteConciliacao');");
                return;
            }

            listaCheques = new ArrayList<ChequeTO>();
            listaCartoes = new ArrayList<CartaoCreditoTO>();
            listaOutros = new ArrayList<MovPagamentoVO>();

            visaoConciliacao = true;
            if (UteisValidacao.emptyList(formasPagamentoSI)) {
                formasPagamentoSI = new ArrayList<SelectItem>();
                formasPagamentoSI.add(new SelectItem(null, ""));
                TipoFormaPagto[] tiposFPConsultar = new TipoFormaPagto[]{TipoFormaPagto.CARTAOCREDITO, TipoFormaPagto.CARTAODEBITO};
                List<FormaPagamentoVO> formaPagamentoVOs = getFacade().getFormaPagamento().consultarPorTiposFormaPagamentoSimples(tiposFPConsultar, getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_MINIMOS);
                for (FormaPagamentoVO fp : formaPagamentoVOs) {
                    formasPagamentoSI.add(new SelectItem(fp.getCodigo(), fp.getDescricao()));
                }
            }
            if (UteisValidacao.emptyList(tiposConvenio)) {
                List<ConvenioCobrancaVO> convenioCobrancaVOs = getFacade().getConvenioCobranca().consultarSimplesPorTipos(TipoConvenioCobrancaEnum.DCC,
                        TipoConvenioCobrancaEnum.DCC_BIN, TipoConvenioCobrancaEnum.DCC_E_REDE, TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE,
                        TipoConvenioCobrancaEnum.DCC_PAGOLIVRE, TipoConvenioCobrancaEnum.DCC_FACILITEPAY,
                        TipoConvenioCobrancaEnum.DCC_GETNET, TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE,
                        TipoConvenioCobrancaEnum.DCC_STONE_ONLINE, TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT, TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5);
                tiposConvenio = new ArrayList<SelectItem>();
                Map<Integer, ConvenioCobrancaVO> map = new HashMap<>();
                for (ConvenioCobrancaVO cob : convenioCobrancaVOs) {
                    if (!map.containsKey(cob.getCodigo())) {
                        tiposConvenio.add(new SelectItem(cob.getCodigo(), cob.getDescricao()));
                        map.put(cob.getCodigo(), cob);
                    }
                }

                Ordenacao.ordenarLista(tiposConvenio, "label");
                tiposConvenio.add(0, new SelectItem(null, ""));
            }
            if (UteisValidacao.emptyList(extratoDiario)) {
                dataInicialCompensacao = Calendario.hoje();
                dataFinalCompensacao = Calendario.hoje();
                dataInicialLancamento = null;
                dataFinalLancamento = null;
                consultarConciliacao();
            }
            setFiltros(detalharFiltros());
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    public void sairModoConciliacao() throws Exception{
        visaoConciliacao = false;
        consultarRecebiveis();
    }

    public void mesclarCartoes(){
        try {
            setMsgAlert("");
            limparMsg();
            for(ExtratoDiarioItemVO item : extratoDiario){
                if(item.isItemEscolhido()){
                    itemOperadora = item;
                }else if(item.isCartaoEscolhido()){
                    itemZW = item;
                }
            }
            if((itemOperadora != null && itemZW != null)){
                Double valorZW = itemZW.getCartao() != null ? itemZW.getCartao().getValor() :
                                    itemZW.getMovPagamento() != null ? itemZW.getMovPagamento().getValor() : 0.00;
                if(!itemOperadora.getValorBruto().equals(valorZW)){
                    montarErro("Você não pode mesclar dois itens com valores diferentes.");
                    setMsgAlert(getMensagemNotificar());
                    return;
                }
                setMsgAlert("Richfaces.showModalPanel('modalMesclarCartao');");
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarFormaPagamento() {
        selecionarFormaPagamento((FormaPagamentoVO) context().getExternalContext().getRequestMap().get("forma"));
    }

    public void selecionarFormaPagamentoHidden() {
        limparVariavelResumoContas();
        limpaOrdemColuna();
        for(FormaPagamentoVO fp : lista){
            if(fp.getCodigo().toString().equals(descricaoFp)){
                selecionarFormaPagamento(fp);
                break;
            }
        }
    }

    public void selecionarDevolucoes() {
        formaPagamentoSelecionada = new FormaPagamentoVO();
        formaPagamentoSelecionada.setTipoFormaPagamento("DEVOLUCOES");
    }

    public void selecionarFormaPagamento(FormaPagamentoVO fp) {
        listaCheques = null;
        listaCartoes = null;
        listaOutros = null;
        List list = new ArrayList();
        formaPagamentoSelecionada = fp;
        try {
            list = getFacade().getFinanceiro().getRecebiveis().selecionarListaFormaPagamento(fp,
                    listaConsultaRecebiveis,
                    analisarSePrecisaPesquisarPorLote(),
                    chequeAvista, chequeAprazo, dataInicialCompensacao, dataFinalCompensacao, considerarDataCompensacaoOriginal,
                    getStatusMovimentacao(),
                    nomeConta,
                    codigoLoteFiltro,
                    chequeVO,
                    codigosListaFiltro,
                    mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);
        }catch (Exception e){
            e.printStackTrace();
        }

        if (formaPagamentoSelecionada.getTipoFormaPagamento().equals("CA")) {
            todosCartoesMarcados = false;
            qtdeTotalCartoes = 0;
            totalCartoes = 0.0;
            setListaCartoes(list);
        } else if (formaPagamentoSelecionada.getTipoFormaPagamento().equals("CH")) {
            todosChequesMarcados = false;
            qtdeTotalCheques = 0;
            totalCheques = 0.0;
            setListaCheques(list);
        } else {
            todosPagamentosMarcados = false;
            qtdeTotalVista = 0;
            totalVista = 0.0;
            setListaOutros(list);
        }
//        adicionar devolucoes
        setExibirAutorizacao(getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()));
        setExibirNSU(getFormaPagamentoSelecionada().isApresentarNSU());
        setContaCorrente(getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla()));

    }

    public void abrirModalAlteracaoCompensacaoCredito() {
        setarEscolhidos();
        dataNovaCompensacaoCartoes = Calendario.hoje();
        setMsgAlert("");
        setAlterarCredito(true);
        boolean algumSelecionado = false;
        for (CartaoCreditoTO cc : listaCartoes) {
            if (cc.isCartaoEscolhido()) {
                algumSelecionado = true;
                break;
            }
        }
        if (algumSelecionado) {
            setMsgAlert("Richfaces.showModalPanel('modalAlterarCompensacao');");
        } else {
            montarMsgAlert(getMensagemInternalizacao("msg_nenhumitemselecionado"));
        }
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(paginacao, codigo);
    }

    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) throws Exception {
        if (codigo.equals(LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO)) {
            return listaMovPagamentoExcluirDepositoPaginada;
        }
        return null;
    }

    private void carregarListaPaginacao(ListaPaginadaTO paginacao, String codigo) throws Exception {
        if (codigo.equals(LISTA_MOVPAGAMENTO_EXCLUIR_DEPOSITO)) {
            consultarListaMovPagamentoExcluirDeposito(paginacao);
        }
    }

    private void consultarListaMovPagamentoExcluirDeposito(ListaPaginadaTO paginacao) throws Exception {
        listaMovPagamentoExcluirDeposito = getFacade().getMovPagamento().consultarPorMovConta(movContaVOExcluirDeposito.getCodigo(), paginacao.getLimit(), paginacao.getOffset());
    }

    private MovContaVO loteAvulso = new MovContaVO();

    public GestaoRecebiveisControle() throws Exception {
        inicializarConfiguracaoSistema();
        identificacaoPessoalInternacional(getEmpresaLogado());
        abrirTelaRecebiveis();
    }

    public void limparVariavelResumoContas() {
        try {
            GerenciadorContaControle gerenciadorContaControle = JSFUtilities.getControlador(GerenciadorContaControle.class);
            gerenciadorContaControle.setTransferindoSaldoResumoContas(false);
        } catch (Exception ex) {}
    }

    public GestaoRecebiveisControle(EmpresaVO empresaVO, boolean isTela) throws Exception {
        identificacaoPessoalInternacional(empresaVO);

        if(isTela) {
            inicializarConfiguracaoSistema();
            abrirTelaRecebiveis();
        }
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    public void abrirRetiradaLotes() {
        try {
            setarEscolhidos();
            verificarCaixa();
            GestaoLotesControle gestaoLotesCtrl = (GestaoLotesControle) JSFUtilities.getManagedBean(GestaoLotesControle.class.getSimpleName());
            gestaoLotesCtrl.setChequesARetirar(new ArrayList<ChequeTO>());
            gestaoLotesCtrl.setCartoesARetirar(new ArrayList<CartaoCreditoTO>());
            gestaoLotesCtrl.setObservacaoRetiradaLote("");
            gestaoLotesCtrl.setCodigoLote(0);
            gestaoLotesCtrl.setOrigem("GestaoRecebiveisControle");
            setMsgAlert("");
            if (listaCheques != null) {
                for (ChequeTO ch : listaCheques) {
                    if (ch.isChequeEscolhido()) {
                        if(ch.getNumeroLote() == 0){
                            montarMsgAlert("O cheque " + ch.getNumero() + " não estã em lote, desmarque-o para continuar!");
                            return;
                        }
                        gestaoLotesCtrl.addChequesARetirar(ch);
                        if (gestaoLotesCtrl.getCodigoLote() != 0 && ch.getNumeroLote() != gestaoLotesCtrl.getCodigoLote()) {
                            montarMsgAlert("Todos os cheques devem estar no mesmo lote!");
                            return;
                        } else {
                            gestaoLotesCtrl.setCodigoLote(ch.getNumeroLote());
                        }
                    }
                }
            }
            if (listaCartoes != null) {
                for (CartaoCreditoTO cc : listaCartoes) {
                    if (cc.isCartaoEscolhido()) {
                        if(cc.getNumeroLote() == 0){
                            montarMsgAlert("O cartão com autorização " + cc.getAutorizacao() + " não está em lote, desmarque-o para continuar!");
                            return;
                        }
                        gestaoLotesCtrl.addCartoesARetirar(cc);
                        if (gestaoLotesCtrl.getCodigoLote() != 0 && cc.getNumeroLote() != gestaoLotesCtrl.getCodigoLote()) {
                            montarMsgAlert("Todos os cartões devem estar no mesmo lote!");
                            return;
                        } else {
                            gestaoLotesCtrl.setCodigoLote(cc.getNumeroLote());
                        }
                    }
                }
            }
            gestaoLotesCtrl.setLote(new LoteVO(gestaoLotesCtrl.getCodigoLote()));

            setMsgAlert("Richfaces.showModalPanel('modalRetirarChequeLote')");

        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    private void verificarCaixa() throws Exception {
        CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
        if ((caixaControle.getCaixaVoEmAberto() == null) || (caixaControle.getCaixaVoEmAberto().getCodigo() == 0)) {
            setMensagemID("msg_naoPossuiCaixaAberto");
            throw new Exception(getMensagem());
        }
    }

    public void novo(boolean limparCamposLotes) throws Exception {
        considerarDataCompensacaoOriginal = false;
        limparDados(limparCamposLotes);
        restaurarFiltros();
        setOperadorCaixa(new UsuarioVO());
        inicializarEmpresa();
        inicializarUsuarioLogado();
        preparaFormasPagamento();
        setMensagemID("msg_entre_dados");
    }

    public void abrirModalImpressao() {
        getItemImpressao().setDataFimCompensacao(dataFinalCompensacao);
        getItemImpressao().setDataInicioCompensacao(dataInicialCompensacao);
        getItemImpressao().setDataInicioFaturamento(dataInicialLancamento);
        getItemImpressao().setDataFimFaturamento(dataFinalLancamento);
        if (getItemImpressao().getFormasPagamento() == null || getItemImpressao().getFormasPagamento().isEmpty()) {
            getItemImpressao().setFormasPagamento(new ArrayList<ResumoFormaPagamentoRelatorio>());
            for (FormaPagamentoVO fp : lista) {
                if(fp.getValor() > 0.0){
                    try {
                        List itens = getFacade().getFinanceiro().getRecebiveis().selecionarListaFormaPagamento(fp,
                                listaConsultaRecebiveis,
                                analisarSePrecisaPesquisarPorLote(),
                                chequeAvista, chequeAprazo, dataInicialCompensacao, dataFinalCompensacao, considerarDataCompensacaoOriginal,
                                getStatusMovimentacao(),
                                nomeConta,
                                codigoLoteFiltro,
                                chequeVO,
                                codigosListaFiltro,
                                mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);
                        adicionar(getItemImpressao(), true, fp, fp.getValor(),
                                itens, fp.getTipoFormaPagamento());
                    }catch (Exception e){
                        montarErro(e);
                    }
                }

            }
        }
        ResumoFormaPagamentoRelatorio resumoDevolucao = null;
        for(ResumoFormaPagamentoRelatorio resumo : getItemImpressao().getFormasPagamento()){
            if(resumo.getTipoFormaPagamento().equals("devolucoes")){
                resumoDevolucao = resumo;
            }
        }
        if(resumoDevolucao != null){
            getItemImpressao().getFormasPagamento().remove(resumoDevolucao);
        }
        if(totalDevolucoes > 0.0){
            FormaPagamentoVO fpDevolucoes = new FormaPagamentoVO();
            fpDevolucoes.setDescricao("Devoluções");
            adicionar(getItemImpressao(), true, fpDevolucoes, totalDevolucoes, devolucoes, "devolucoes");
        }
        Ordenacao.ordenarListaReverse(getItemImpressao().getFormasPagamento(), "valor");
    }

    public void imprimir() throws Exception {
        try {
            List<RelatorioGestaoRecebiveisTO> relatorio = new ArrayList<RelatorioGestaoRecebiveisTO>();
            setMsgAlert("");

            processarItemImpressao();
            if (imprimirDiario) {
                relatorio.addAll(agruparDiario());
                Ordenacao.ordenarLista(relatorio, "dataInicioFaturamento");
            } else {
                relatorio.add(getItemImpressao());
                ordenarRelatorio(relatorio);
            }

            Map<String, Object> params = new HashMap<String, Object>();
            prepareParams(params, relatorio);
            apresentarRelatorioObjetos(params);
            setMsgAlert("abrirPopupPDFImpressao('../../relatorio/" + getNomeArquivoRelatorioGeradoAgora() + "','', 780, 595);");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }

    }

    private RecebivelTO converterParaRecebivelTO(Object obj) throws Exception {
        RecebivelTO recebivelTO = new RecebivelTO();
        if (obj instanceof ChequeTO) {
            recebivelTO = ((ChequeTO) obj).getRecebivelTO();
        } else if (obj instanceof CartaoCreditoTO) {
            recebivelTO = ((CartaoCreditoTO) obj).getRecebivelTO();
        } else if (obj instanceof MovPagamentoVO) {
            recebivelTO = ((MovPagamentoVO) obj).getRecebivelTO();
        } else if (obj instanceof MovContaVO) {
            recebivelTO = ((MovContaVO) obj).getRecebivelTO();
        }

        return recebivelTO;
    }

    public void exportarExcelResumido(ActionEvent evt) {
        try {
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
            List listaParaImpressao = new ArrayList();

            for(FormaPagamentoVO fp : lista) {
                if (fp.getValor() > 0.0) {
                    List itens = getFacade().getFinanceiro().getRecebiveis().selecionarListaFormaPagamento(fp,
                            listaConsultaRecebiveis,
                            analisarSePrecisaPesquisarPorLote(),
                            chequeAvista, chequeAprazo, dataInicialCompensacao, dataFinalCompensacao, considerarDataCompensacaoOriginal,
                            getStatusMovimentacao(),
                            nomeConta,
                            codigoLoteFiltro,
                            chequeVO,
                            codigosListaFiltro,
                            mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);
                    for (Object o : itens) {
                        RecebivelTO recebivelTO = converterParaRecebivelTO(o);
                        recebivelTO.setTipoRecebivel(fp.getDescricao());
                        if (UteisValidacao.emptyString(recebivelTO.getMatricula()) &&
                                UteisValidacao.emptyString(recebivelTO.getNomePessoa()) &&
                                !UteisValidacao.emptyString(recebivelTO.getNomePagador())) {
                            recebivelTO.setNomePessoa(recebivelTO.getNomePagador());
                        }
                        listaParaImpressao.add(recebivelTO);
                    }
                    itens = null;
                }
            }

            for (Object obj : listaParaImpressao) {
                RecebivelTO recebivelTO = (RecebivelTO) obj;
                try {
                    preencherDadosColunaAlunosParcelaExportarExcel(recebivelTO);
                    if(
                            recebivelTO.getFormaPagamento() != null && !UteisValidacao.emptyString(recebivelTO.getFormaPagamento().getTipoFormaPagamento())
                                    && (recebivelTO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())
                                    || recebivelTO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()))
                    ){
                        recebivelTO.setDocumentoIntegracaoSesi(
                                preencherDadosColunaDocumentoExportarExcel(recebivelTO.getEmpresa().getCodExternoUnidadeSesi(), recebivelTO.getDataLancamento(), recebivelTO.getNsu(), recebivelTO.getAutorizacao())
                        );
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
            listaParaImpressao = null;
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

            List listaParaImpressao = montarListaExportavel(false);

            //montar nome do pagador quando for consumidor
            for (Object recebivel : listaParaImpressao) {
                RecebivelTO obj = (RecebivelTO) recebivel;
                if (UteisValidacao.emptyString(obj.getMatricula()) &&
                        UteisValidacao.emptyString(obj.getNomePessoa()) &&
                        !UteisValidacao.emptyString(obj.getNomePagador())) {
                    obj.setNomePessoa(obj.getNomePagador());
                }
            }

            exportadorListaControle.exportar(evt, listaParaImpressao, "", null);
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
    }

    private List<RecebivelTO> montarListaExportavel(boolean mesclarParcelas) throws Exception {
        List<RecebivelTO> listaParaImpressao = new ArrayList<>();
        Map<String, RecebivelTO> parcelasMescladas = new HashMap<>();

        for(FormaPagamentoVO fp : lista){
            if(fp.getValor() > 0.0){
                List itens = getFacade().getFinanceiro().getRecebiveis().selecionarListaFormaPagamento(fp,
                        listaConsultaRecebiveis,
                        analisarSePrecisaPesquisarPorLote(),
                        chequeAvista, chequeAprazo, dataInicialCompensacao, dataFinalCompensacao, considerarDataCompensacaoOriginal,
                        getStatusMovimentacao(),
                        nomeConta,
                        codigoLoteFiltro,
                        chequeVO,
                        codigosListaFiltro,
                        mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);
                for(Object o : itens){
                    RecebivelTO recebivelTO = converterParaRecebivelTO(o);
                    recebivelTO.setTipoRecebivel(fp.getDescricao());
                    recebivelTO.setCnpjEmpresa(empresaRel.getCNPJ());
                    recebivelTO.setNomeEmpresa(empresaRel.getNome());

                    if(mesclarParcelas && adicionarMesclagem(recebivelTO, parcelasMescladas)) {
                        listaParaImpressao.add(recebivelTO);
                    } else if(!mesclarParcelas) {
                        listaParaImpressao.add(recebivelTO);
                    }
                }
                itens = null;
            }
        }
        if(devolucoes == null || devolucoes.isEmpty() && !UteisValidacao.emptyList(getEmpresasSelecionadas()) && !UteisValidacao.emptyList(getEmpresas())) {
            for(EmpresaVO empresa: getEmpresas()){
                if(getEmpresasSelecionadas().contains(empresa.getCodigo().toString())){
                    devolucoes.addAll(getFacade().getFinanceiro().getMovConta().consultarDevolucoes(
                            dataInicialLancamento == null ? null : (Calendario.getDataComHora(dataInicialLancamento, (horaInicialLancamento + "00"))),
                            dataFinalLancamento == null ? null : (Calendario.getDataComHora(dataFinalLancamento, (horaFinalLancamento + "59"))),
                            dataInicialCompensacao == null ? null : (Calendario.getDataComHora(dataInicialCompensacao, (horaInicialCompensacao + "00"))),
                            dataFinalCompensacao == null ? null : (Calendario.getDataComHora(dataFinalCompensacao, (horaFinalCompensacao + "59"))),
                            empresa.getCodigo(), nome, getOperadorCaixa().getNome(), cpf, matricula));
                }
            }
        }
        for(MovContaVO movContaVO : devolucoes) {
            RecebivelTO recebivelTO = converterParaRecebivelTO(movContaVO);
            recebivelTO.setTipoRecebivel(movContaVO.getDescricao().toUpperCase());
            recebivelTO.setValor(recebivelTO.getValor() * -1);
            recebivelTO.setCnpjEmpresa(empresaRel.getCNPJ());
            recebivelTO.setNomeEmpresa(empresaRel.getNome());

            if(mesclarParcelas && adicionarMesclagem(recebivelTO, parcelasMescladas)) {
                listaParaImpressao.add(recebivelTO);
            } else if(!mesclarParcelas) {
                listaParaImpressao.add(recebivelTO);
            }
        }

        Map<Integer, ReciboPagamentoVO> mapaReciboUsuario = new HashMap<>();
        for (RecebivelTO obj : listaParaImpressao) {
            try {
                if (!UteisValidacao.emptyNumber(obj.getRecibo())) {
                    ReciboPagamentoVO reciboPagamentoVO = mapaReciboUsuario.get(obj.getRecibo());
                    if (reciboPagamentoVO == null) {
                        reciboPagamentoVO = getFacade().getReciboPagamento().obterReciboExportacaoRecebiveis(obj.getRecibo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                        mapaReciboUsuario.put(obj.getRecibo(), reciboPagamentoVO);
                    }
                    obj.setContratoVO(reciboPagamentoVO.getContrato());
                    obj.setNomeResponsavelRecibo(reciboPagamentoVO.getResponsavelLancamento().getNome());
                }

                preencherDadosColunaAlunosParcelaExportarExcel(obj);
                if(
                        obj.getFormaPagamento() != null
                        && !UteisValidacao.emptyString(obj.getFormaPagamento().getTipoFormaPagamento())
                        && (
                                obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())
                                || obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())
                        )
                ){
                    obj.setDocumentoIntegracaoSesi(
                            preencherDadosColunaDocumentoExportarExcel(obj.getEmpresa().getCodExternoUnidadeSesi(), obj.getDataLancamento(), obj.getNsu(), obj.getAutorizacao())
                    );
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        return listaParaImpressao;
    }

    private void preencherDadosColunaAlunosParcelaExportarExcel(RecebivelTO obj) {
        if(!UteisValidacao.emptyList(obj.getPagamentoMovParcelaVOs())){
            String nomeAlunosParcela = "";
            int cont = 1;
            for (Object objectPagamentoMovParcelaVO : obj.getPagamentoMovParcelaVOs()) {
                PagamentoMovParcelaVO pagamentoMovParcelaVO = (PagamentoMovParcelaVO) objectPagamentoMovParcelaVO;
                if(
                        pagamentoMovParcelaVO.getMovParcela() != null
                        && pagamentoMovParcelaVO.getMovParcela().getPessoa()  != null
                        && !UteisValidacao.emptyString(pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome())
                ){
                    if(cont == 1){
                        nomeAlunosParcela += pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                        cont++;
                    } else {
                        nomeAlunosParcela += ", " + pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                    }
                }
            }
            obj.setNomeAlunosDaParcela(nomeAlunosParcela);
        }
    }

    private String preencherDadosColunaDocumentoExportarExcel(Integer idSesi, Date dataLancamento, String NSU, String Autorizacao) {
        String campoDocumento = "";
        campoDocumento += idSesi.toString();
        campoDocumento += tratarDataSesi(dataLancamento);
        campoDocumento += NSU;
        campoDocumento += "1"; //Falor fixo solicitado pelo Sesi, sempre enviar 1
        campoDocumento += Autorizacao;
        return campoDocumento;
    }

    private String tratarDataSesi(Date data){
        String dataSemFormato = data.toString();
        //Precisa disso porquê o toString não trás os valores de mílesegundo com final 0
        dataSemFormato += (
                dataSemFormato.length() == 22 ? "0" :
                dataSemFormato.length() == 21 ? "00" :
                dataSemFormato.length() == 20 ? "000" : ""
        );
        //Precisa disso porquê o toString não trás os valores de TimeZone
        //Três TimeZone existenes no Brasil. Como o Sesi é nacional, resolvi implementar todos para garantir.
        dataSemFormato += (
                data.getTimezoneOffset() == 180 ? "0300" :
                data.getTimezoneOffset() == 120 ? "0200" :
                data.getTimezoneOffset() == 240 ? "0400" : ""
        );
        //Essa parte tira os carcateres e adiciona o T entre a Data e a Hora, para atender o formato especificado pelo Sesi
        dataSemFormato = dataSemFormato.replace("-","").replace(":","").replace(".","").replace(" ","");
        String parte1 = dataSemFormato.substring(0, 8);
        String parte2 = dataSemFormato.substring(8);
        dataSemFormato = parte1 + "T" + parte2;
        return dataSemFormato;
    }

    private boolean adicionarMesclagem(RecebivelTO recebivelTO, Map<String, RecebivelTO> parcelasMescladas) {
        String identificacao = recebivelTO.getTipoRecebivel()+";"+recebivelTO.getMatricula()+";"+recebivelTO.getRecibo()+";"+recebivelTO.getAutorizacao()+";"+recebivelTO.getModalidade();

        RecebivelTO recebivelTOAux = parcelasMescladas.get(identificacao);

        if(recebivelTOAux != null) {
            if (UteisValidacao.emptyNumber(recebivelTOAux.getParcelasMescladas())) {
                recebivelTOAux.setParcelasMescladas(2);
                recebivelTOAux.setValorTotal(BigDecimal.valueOf(recebivelTOAux.getValor()).add(BigDecimal.valueOf(recebivelTO.getValor())).setScale(2, BigDecimal.ROUND_CEILING).doubleValue());
            } else {
                recebivelTOAux.setParcelasMescladas(recebivelTOAux.getParcelasMescladas() + 1);
                recebivelTOAux.setValorTotal(BigDecimal.valueOf(recebivelTOAux.getValorTotal()).add(BigDecimal.valueOf(recebivelTO.getValor())).setScale(2, BigDecimal.ROUND_CEILING).doubleValue());
            }
            if (UteisValidacao.emptyString(recebivelTOAux.getCodigoUnico()) || recebivelTO.getCodigoUnico().compareTo(recebivelTOAux.getCodigoUnico()) < 0) {
                recebivelTOAux.setCodigoUnico(recebivelTO.getCodigoUnico());
            }
            return false;
        }

        recebivelTO.setValorTotal(null);
        parcelasMescladas.put(identificacao, recebivelTO);
        return true;
    }

    private List<RelatorioGestaoRecebiveisTO> agruparDiario() {
        List<RelatorioGestaoRecebiveisTO> relatorio = new ArrayList<RelatorioGestaoRecebiveisTO>();
        Map<Date, RelatorioGestaoRecebiveisTO> mapa = new HashMap<Date, RelatorioGestaoRecebiveisTO>();
        for (ResumoFormaPagamentoRelatorio rfpr : getItemImpressao().getFormasPagamento()) {

            //cheque
            for (ChequeTO ch : rfpr.getCheques()) {
                RelatorioGestaoRecebiveisTO relatorioGestaoRecebiveisTO = mapa.get(Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? ch.getDataLancamento() : ch.getDataCompensacao()));
                relatorioGestaoRecebiveisTO = novoDia(mapa, Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? ch.getDataLancamento() : ch.getDataCompensacao()), relatorioGestaoRecebiveisTO);
                for (ResumoFormaPagamentoRelatorio rf : relatorioGestaoRecebiveisTO.getFormasPagamento()) {
                    if (rfpr.getFormaPagamento().equals(rf.getFormaPagamento())) {
                        rf.setValor(rf.getValor() + ch.getValor());
                        rf.getCheques().add(ch);
                        relatorioGestaoRecebiveisTO.setValor(relatorioGestaoRecebiveisTO.getValor() + ch.getValor());
                        relatorioGestaoRecebiveisTO.setValorEspecie(relatorioGestaoRecebiveisTO.getValorEspecie()+ ch.getValor());
                    }
                }
            }

            //cartao
            for (CartaoCreditoTO cc : rfpr.getCartoes()) {
                RelatorioGestaoRecebiveisTO relatorioGestaoRecebiveisTO = mapa.get(Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? cc.getDataLancamento() : cc.getDataCompensacao()));
                relatorioGestaoRecebiveisTO = novoDia(mapa, Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? cc.getDataLancamento() : cc.getDataCompensacao()), relatorioGestaoRecebiveisTO);
                for (ResumoFormaPagamentoRelatorio rf : relatorioGestaoRecebiveisTO.getFormasPagamento()) {
                    if (rfpr.getFormaPagamento().equals(rf.getFormaPagamento())) {
                        rf.setValor(rf.getValor() + cc.getValor());
                        rf.getCartoes().add(cc);
                        relatorioGestaoRecebiveisTO.setValor(relatorioGestaoRecebiveisTO.getValor() + cc.getValor());
                        relatorioGestaoRecebiveisTO.setValorEspecie(relatorioGestaoRecebiveisTO.getValorEspecie()+ cc.getValor());
                    }
                }
            }

            //pagamento
            for (MovPagamentoVO mp : rfpr.getPagamentos()) {
                RelatorioGestaoRecebiveisTO relatorioGestaoRecebiveisTO = mapa.get(Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? mp.getDataLancamento() : mp.getDataPagamento()));
                relatorioGestaoRecebiveisTO = novoDia(mapa, Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? mp.getDataLancamento() : mp.getDataPagamento()), relatorioGestaoRecebiveisTO);
                for (ResumoFormaPagamentoRelatorio rf : relatorioGestaoRecebiveisTO.getFormasPagamento()) {
                    if (rfpr.getFormaPagamento().equals(rf.getFormaPagamento())) {
                        rf.getPagamentos().add(mp);
                        if (rf.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
                            relatorioGestaoRecebiveisTO.setValorContaCorrente(relatorioGestaoRecebiveisTO.getValorContaCorrente()+ mp.getValor());

                        } else {
                            if(rf.getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())){
                                relatorioGestaoRecebiveisTO.setValorBoleto(relatorioGestaoRecebiveisTO.getValorBoleto()+ mp.getValor());
                            } else {
                                relatorioGestaoRecebiveisTO.setValorEspecie(relatorioGestaoRecebiveisTO.getValorEspecie()+ mp.getValor());
                            }
                            relatorioGestaoRecebiveisTO.setValor(relatorioGestaoRecebiveisTO.getValor() + mp.getValor());
                        }
                        rf.setValor(rf.getValor() + mp.getValor());
                    }
                }
            }

            //pagamento
            for (MovContaVO mc : rfpr.getListaDevolucoes()) {
                Date dataBase = agruparPor == LANCAMENTO ? mc.getDataLancamento() : mc.getDataQuitacao();
                if(dataBase == null){
                    continue;
                }
                RelatorioGestaoRecebiveisTO relatorioGestaoRecebiveisTO = mapa.get(Calendario.getDataComHoraZerada(dataBase));
                relatorioGestaoRecebiveisTO = novoDia(mapa, Calendario.getDataComHoraZerada(agruparPor == LANCAMENTO ? mc.getDataLancamento() : mc.getDataQuitacao()), relatorioGestaoRecebiveisTO);
                for (ResumoFormaPagamentoRelatorio rf : relatorioGestaoRecebiveisTO.getFormasPagamento()) {
                    if (rfpr.getFormaPagamento().equals(rf.getFormaPagamento())) {
                        rf.getListaDevolucoes().add(mc);
                        relatorioGestaoRecebiveisTO.setValor(relatorioGestaoRecebiveisTO.getValor() - mc.getValor());
                        relatorioGestaoRecebiveisTO.setValorDevolucoes(relatorioGestaoRecebiveisTO.getValorDevolucoes()+ mc.getValor());
                        rf.setValor(rf.getValor() + mc.getValor());
                    }
                }
            }
        }
        relatorio.addAll(mapa.values());
        Ordenacao.ordenarLista(relatorio, "dataInicioCompensacao");
        for (RelatorioGestaoRecebiveisTO relatorioGestaoRecebiveisTO : relatorio) {
            Ordenacao.ordenarListaReverse(relatorioGestaoRecebiveisTO.getFormasPagamento(), "valor");
        }
        return relatorio;

    }

    /**
     *
     */
    private RelatorioGestaoRecebiveisTO novoDia(Map<Date, RelatorioGestaoRecebiveisTO> mapa, Date data,
            RelatorioGestaoRecebiveisTO relatorioGestaoRecebiveisTO) {
        if (relatorioGestaoRecebiveisTO == null) {
            relatorioGestaoRecebiveisTO = new RelatorioGestaoRecebiveisTO();
            relatorioGestaoRecebiveisTO.getFormasPagamento().addAll(getItemImpressao().clonarFormasSemDados());
            if(agruparPor == LANCAMENTO){
                relatorioGestaoRecebiveisTO.setDataInicioFaturamento(data);
                relatorioGestaoRecebiveisTO.setDataFimFaturamento(data);
            }else{
                relatorioGestaoRecebiveisTO.setDataInicioCompensacao(data);
                relatorioGestaoRecebiveisTO.setDataFimCompensacao(data);
            }
            relatorioGestaoRecebiveisTO.setDetalhar(false);
            mapa.put(data, relatorioGestaoRecebiveisTO);
        }
        return relatorioGestaoRecebiveisTO;
    }

    private void prepareParams(Map<String, Object> params, List<RelatorioGestaoRecebiveisTO> relatorio) throws Exception {
        Integer emp = getUsuarioLogado().getAdministrador() ? 0 : getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        params.put("nomeRelatorio", "GestaoRecebiveis");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Gestão de Recebíveis");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("usuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", relatorio);

        params.put("filtros", getFiltros());
        params.put("dataIni", Uteis.getData(dataInicialCompensacao));
        params.put("dataFim", Uteis.getData(dataFinalCompensacao));
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        params.put("formas", getItemImpressao().getFormas());
        params.put("diario", getImprimirDiario());

        params.put("faturamento", (dataInicialLancamento != null || dataFinalLancamento != null));
        params.put("compensacao", (dataInicialCompensacao != null || dataFinalCompensacao != null));
        params.put("inicioFaturamento", Uteis.getData(dataInicialLancamento));
        params.put("fimFaturamento", Uteis.getData(dataFinalLancamento));
        params.put("inicioCompensacao", Uteis.getData(dataInicialCompensacao));
        params.put("fimCompensacao", Uteis.getData(dataFinalCompensacao));
        params.put("valorTotalGR", getItemImpressao().getValorApresentar());
        params.put("valorBoletoGR", getItemImpressao().getValorBoletoApresentar());
        params.put("valorDevolucoesGR", getItemImpressao().getValorDevolucoesApresentar());
        params.put("valorEspecieGR", getItemImpressao().getValorEspecieApresentar());
        params.put("valorContaCorrenteGR", getItemImpressao().getValorContaCorrenteApresentar());
        params.put("considerarCompensacaoOriginal", considerarDataCompensacaoOriginal);
        params.put("moeda", getEmpresaLogado().getMoeda());
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public static String getDesignIReportRelatorioExcel() {
        return ("designRelatorio" + File.separator + "financeiro" + File.separator + "GestaoRecebiveisExcel");
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "GestaoRecebiveis.jrxml");
    }

    private void adicionar(RelatorioGestaoRecebiveisTO item, boolean adicionar,
            FormaPagamentoVO formaPagamento, double valor, List lista, String tipo) {
        if (adicionar) {
            ResumoFormaPagamentoRelatorio rfpr = new ResumoFormaPagamentoRelatorio();
            rfpr.setFormaPagamento(formaPagamento.getDescricao());
            rfpr.setValor(valor);
            rfpr.setTipoFormaPagamento(tipo);
            if (tipo.equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                rfpr.setCartoes(lista);
                rfpr.setCartao(true);
            } else if (tipo.equals(TipoFormaPagto.CHEQUE.getSigla())) {
                rfpr.setCheques(lista);
                rfpr.setCheque(true);
            } else if (tipo.equals("devolucoes")) {
                rfpr.setDevolucao(true);
                rfpr.setListaDevolucoes(lista);
            } else {
                rfpr.setExibirAutorizacao(tipo.equals(TipoFormaPagto.CARTAODEBITO.getSigla()));
                rfpr.setExibirNSU(formaPagamento.isApresentarNSU());
                rfpr.setPagamentos(lista);
            }


            if(tipo.equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())){
                item.setValorContaCorrente(item.getValorContaCorrente()+ valor);
            } else {
                if(tipo.equals("devolucoes")){
                    item.setValorDevolucoes(item.getValorDevolucoes()+ valor);
                    item.setValor(item.getValor() - valor);
                } else {
                    if(tipo.equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())){
                        item.setValorBoleto(item.getValorBoleto()+ valor);
                    } else {
                        item.setValorEspecie(item.getValorEspecie()+ valor);
                    }
                    item.setValor(item.getValor() + valor);
                }
            }
            item.getFormasPagamento().add(rfpr);
        }
    }

    private List ordenarLista(org.richfaces.component.html.HtmlDataTable dataTable, List lista) {
        if (!lista.isEmpty()) {
            String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
            if (!colunaOrdenacao.isEmpty()) {
                String[] params = colunaOrdenacao.split(":");
                Ordenacao.ordenarLista(lista, params[0]);
                if (params[1].equals("DESC")) {
                    Collections.reverse(lista);
                }
            }
        }
        return lista;
    }

    public void ordenarRelatorio(List<RelatorioGestaoRecebiveisTO> list){
        if(!UteisValidacao.emptyString(this.ordenacaoListagem)){
            String[] ordenacao = this.ordenacaoListagem.split(":");
            for(RelatorioGestaoRecebiveisTO rel : list){
                for(ResumoFormaPagamentoRelatorio formaPagamento : rel.getFormasPagamento()){
                    List<?> listaOrdenar = null;
                    if(formaPagamento.getTipoFormaPagamento().equals("CA")){
                        listaOrdenar = formaPagamento.getCartoes();
                    }else if(formaPagamento.getTipoFormaPagamento().equals("CH")){
                        listaOrdenar = formaPagamento.getCheques();
                    }else{
                        listaOrdenar = formaPagamento.getPagamentos();
                    }
                    if(listaOrdenar != null && !listaOrdenar.isEmpty()){
                        String[] camposOrdenacao = ordenacao[0].split("@");
                        for(String ordenador : camposOrdenacao){
                            try {
                                if(ordenador.startsWith("+")){
                                    Ordenacao.ordenarLista(listaOrdenar, ordenador.substring(1), true);
                                }else{
                                    Ordenacao.ordenarLista(listaOrdenar, ordenador);
                                }
                                if (ordenacao[1].equals("desc")) {
                                    Collections.reverse(listaOrdenar);
                                }
                                break;
                            }catch (Exception e){}
                        }
                    }
                }
            }
        }
    }

    public void limparDados(boolean limparCamposLotes) {
        setNome("");
        setEmpresa(new EmpresaVO());
        setUsuarioResponsavel(null);
        setDataInicialCompensacao(null);
        setDataFinalCompensacao(null);
        setDataInicialLancamento(Uteis.somarDias(Calendario.hoje(), -1));
        setDataFinalLancamento(Uteis.somarDias(Calendario.hoje(), -1));
        setListaResumo(new ArrayList<ResumoFormaPagamentoTO>());
        limparDadosConsultados();
        todosChequesMarcados = false;
        todosCartoesMarcados = false;
        setPeriodoLancamento("ON");
        setPeriodoCompensacao("");
        if (limparCamposLotes) {
            setPesquisarRecebiveis(false);
            setPesquisarRecebiveisSemLote(false);
            setPesquisarRecebiveisComLote(false);
            setCodigoLoteFiltro(null);
        }
        chequeAprazo = false;
        chequeAvista = false;
    }

    public void limparFiltros() {
        setNome("");
        setDataInicialCompensacao(null);
        setDataFinalCompensacao(null);
        setDataInicialLancamento(null);
        setDataFinalLancamento(null);
        setHoraInicialLancamento("");
        setHoraFinalLancamento("");
        setHoraInicialCompensacao("");
        setHoraFinalCompensacao("");
        setOperadorCaixa(new UsuarioVO());
        limparDadosConsultados();
        todosChequesMarcados = false;
        todosCartoesMarcados = false;
        setCpf("");
        setNomeClienteContrato("");
        setChequeVO(new ChequeVO());
        setPeriodoCompensacao("");
        setPeriodoLancamento("");
        setPesquisarRecebiveis(false);
        setPesquisarRecebiveisComLote(false);
        setPesquisarRecebiveisSemLote(false);
        setCodAutorizacao("");
        setOperadoraCartao(null);
        setMatricula("");
        setNsu("");
        setConsiderarDataCompensacaoOriginal(false);
        setMostrarCancelados(false);
        setRecebidosZW(false);
        setStatusMovimentacao(null);
        setNomeConta(null);
        setPesquisarRecebiveisComLote(false);
        setPesquisarRecebiveisSemLote(false);
        setCodigoLoteFiltro(null);
        setNomeTerceiro("");
        setChequeAvista(false);
        setChequeAprazo(false);
        setCentroCustoTO(new CentroCustoTO());
        setNumeroDocumento("");
    }

    public void limparOperador() {
        setOperadorCaixa(new UsuarioVO());
    }

    public void limparDadosConsultados() {
        setListaOutros(new ArrayList<MovPagamentoVO>());
        setListaCheques(new ArrayList<ChequeTO>());
        setListaCartoes(new ArrayList<CartaoCreditoTO>());
        setTotalCheques(0.0);
        setTotalCartoes(0.0);
        setQtdeTotal(0);
        setQtdeTotalCartoes(0);
        setQtdeTotalCheques(0);
        setValorTotal(0.0);
        setLote(new LoteVO());
        setEscolha("closed");
        setTipoLista("");
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        getEmpresas().clear();
        try {
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                getEmpresas().add(obj);
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
        return objs;
    }

    public void preencherListaSelectItemsEmpresa() throws Exception {
        validarPermissaoAcessoRelatorioTodasEmpresas();

        listaSelectItemsEmpresa = new ArrayList<>();
        getEmpresas().clear();
        try {
            if (possuiPermissaoConsultarTodasEmpresas) {
                List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                Iterator i = resultadoConsulta.iterator();
                while (i.hasNext()) {
                    EmpresaVO obj = (EmpresaVO) i.next();
                    listaSelectItemsEmpresa.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                    getEmpresas().add(obj);
                }
            } else {
                EmpresaVO obj = getFacade().getEmpresa().consultarPorCodigo(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                listaSelectItemsEmpresa.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                getEmpresas().add(obj);
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void abrirModalAlteracaoCompensacaoDebito() {
        try {
            setMsgAlert("");
            setarEscolhidos();
            dataNovaCompensacaoCartoes = Calendario.hoje();
            setAlterarCredito(false);
            boolean algumSelecionado = false;
            for (MovPagamentoVO cd : listaOutros) {
                if (cd.getMovPagamentoEscolhidaFinan()) {
                    algumSelecionado = true;
                    break;
                }
            }
            if (algumSelecionado) {
                setMsgAlert("Richfaces.showModalPanel('modalAlterarCompensacao');");
            } else {
                throw new Exception(getMensagemInternalizacao("msg_nenhumitemselecionado"));
            }
        }catch (Exception e){
            setErro(true);
            setMensagemDetalhada(e);
            setMsgAlert(getMensagemNotificar());
            limparMsg();
        }

    }

    public void alterarDataCompensacaoCartao() throws Exception {
        try {
            setMsgAlert("");
            StringBuilder txtLog = new StringBuilder();
            txtLog.append("Alteração Data Compensação Cart?es ");
            if (isAlterarCredito()) {
                txtLog.append("Crédito \n");
                for (CartaoCreditoTO cc : listaCartoes) {
                    if (cc.isCartaoEscolhido()) {
                        String codigos = "" + cc.getCodigo();
                        if (cc.getCodigosComposicao() != null && !cc.getCodigosComposicao().equals("")) {
                            codigos += "," + cc.getCodigosComposicao();
                        }
                        txtLog.append("\n -" + cc.getNomePagador() + ", Códigos:" + codigos + ", Data Original:" + cc.getDataCompensacaoApresentar() + ", Data Nova:" + Formatador.formatarDataPadrao(getDataNovaCompensacaoCartoes()));
                        getFacade().getCartaoCredito().alterarDataCompensacao(codigos, getDataNovaCompensacaoCartoes());
                        cc.setDataCompensacao(getDataNovaCompensacaoCartoes());
                    }
                }
            } else {
                txtLog.append("Débito \n");
                for (MovPagamentoVO pag : listaOutros) {
                    if (pag.getMovPagamentoEscolhidaFinan()) {
                        //Adicionado if porquê Conta Corrente tem de mudar todos os MovPagamentos ligados a ele e pagamento não Conta Correte não precisa, pois ele é único
                        if (UteisValidacao.emptyNumber(pag.getMovPagamentoOrigemCredito())){
                            txtLog.append("\n -" + pag.getNomePagador() + ", Códigos:" + pag.getCodigo() + ", Data Original:" + pag.getDataPagamento_Apresentar() + ", Data Nova:" + Formatador.formatarDataPadrao(getDataNovaCompensacaoCartoes()));
                            getFacade().getMovPagamento().alterarDataCompensacao(pag.getCodigo(), getDataNovaCompensacaoCartoes());
                        } else {
                            txtLog.append("\n -" + pag.getNomePagador() + ", Códigos:" + pag.getMovPagamentoOrigemCredito() + ", Data Original:" + pag.getDataPagamento_Apresentar() + ", Data Nova:" + Formatador.formatarDataPadrao(getDataNovaCompensacaoCartoes()));
                            getFacade().getMovPagamento().alterarDataCompensacaoOrigemCredito(pag.getMovPagamentoOrigemCredito(), getDataNovaCompensacaoCartoes());
                        }
                        pag.setDataPagamento(getDataNovaCompensacaoCartoes());
                    }
                }
            }
            LogVO log = new LogVO();
            log.setNomeEntidade("ALTERACAOCARTAOCREDITO");
            log.setNomeEntidadeDescricao("Cartão Crédito");
            log.setNomeCampo("Data de Compensação");
            log.setValorCampoAlterado(txtLog.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao(getUsuarioLogado().getNome().toUpperCase());
            log.setUserOAMD(getUsuarioLogado().getUserOamd());
            log.setOperacao("ALTERAÇÃO");
            getFacade().getLog().incluir(log);
            setMensagemDetalhada("Data de Compensação alterada com sucesso!");
            setSucesso(true);
            setMsgAlert(getMensagemNotificar()+"Richfaces.hideModalPanel('modalAlterarCompensacao');");
            limparMsg();
            setSelecionados("");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }

    }

    /**
     * Consulta de logs de lan?amentos
     */
    public void realizarConsultaLogObjetoSelecionado() {
        setMsgAlert("");
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "ALTERACAOCARTAOCREDITO";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Finan_AlteracaoDataCompensacao"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), 0, null);
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");

    }

    /**
     * Consulta de logs de movimenta??es
     */
    public void realizarConsultaLogMovimentacao() {
        setMsgAlert("");
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        List<String> nomesClasse = new ArrayList<String>();
        nomesClasse.add("MOVIMENTACAORECEBIVEIS");
        nomesClasse.add("ALTERACAOCARTAOCREDITO");
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Finan_AlteracaoDataCompensacao"));
        loginControle.consultarLogEntidadesSelecionado(nomesClasse, 0, null);
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");
    }


    public final void inicializarEmpresa() throws Exception {
        mostrarCampoEmpresa = validarPermissaoEmpresas();
        if (mostrarCampoEmpresa) {
            setEmpresaRel((EmpresaVO) getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_MINIMOS).get(0));
            getEmpresasSelecionadas().add(getEmpresaRel().getCodigo().toString());
        } else {
            setEmpresaRel((EmpresaVO) getEmpresaLogado().getClone(true));
            getEmpresasSelecionadas().add(getEmpresaRel().getCodigo().toString());
        }
        if (getEmpresaRel() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        setUsuarioResponsavel(new UsuarioVO());
        getUsuarioResponsavel().setCodigo(getUsuarioLogado().getCodigo());
        getUsuarioResponsavel().setUsername(getUsuarioLogado().getUsername());
        getUsuarioResponsavel().setAdministrador(getUsuarioLogado().getAdministrador());
        setResponsavelEdicaoPagamento(new UsuarioVO());
        getResponsavelEdicaoPagamento().setCodigo(getUsuarioLogado().getCodigo());
        getResponsavelEdicaoPagamento().setUsername(getUsuarioLogado().getUsername());
    }

    public List<UsuarioVO> executarAutocompleteConsultaUsuario(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            result = (ArrayList<UsuarioVO>) getFacade().getUsuario().
                    consultarPorNome(pref, false, Uteis.NIVELMONTARDADOS_ROBO);
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void autorizarGestaoRecebiveis() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "GestaoRecebiveis", "9.01 - Gestão de Recebíveis");
            }
        }
    }

    public String abrirTelaRecebiveis() {
        try {
            preencherListaSelectItemsEmpresa();
            recebidosZW = Boolean.FALSE;
            if(UteisValidacao.emptyList(listaConsultaRecebiveis)){
                novo(true);
            }
//            restaurarFiltros();
            autorizarGestaoRecebiveis();
            if (getEmpresaLogado().getCodigo() != 0) {
                getEmpresasSelecionadas().clear();
                empresaRel = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                getEmpresasSelecionadas().add(empresaRel.getCodigo().toString());
            }
            //setFiltros(detalharFiltros());
            setFiltros("");
            //setVisaoConciliacao(false);
//            consultarRecebiveis();
            //resetou = true;
            return "gestaoRecebiveis";
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    public void consultarResponsavel() {
        try {
            setUsuarioResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemDetalhada("msg_dados_consultados", "");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarDados() throws Exception {
        if (JSFUtilities.isJSFContext()) {
            if (empresasSelecionadas.isEmpty()) {
                throw new Exception("Informe a Empresa para consultar.");
            }
        }
        // se uma data de um periodo foi informada a outra tbm deve ser
        if (dataInicialCompensacao != null && dataFinalCompensacao == null) {
            throw new Exception("Informe a data final de compensação.");
        }
        if (dataInicialCompensacao == null && dataFinalCompensacao != null) {
            throw new Exception("Informe a data inicial de compensação.");
        }
        if (dataInicialLancamento != null && dataFinalLancamento == null) {
            throw new Exception("Informe a data final de compensação.");
        }
        if (dataInicialLancamento == null && dataFinalLancamento != null) {
            throw new Exception("Informe a data inicial de compensação.");
        }
        // se nenhum filtro foi informado
        if (nome.trim().isEmpty()
                && getOperadorCaixa().getNome().trim().isEmpty()
                && dataInicialCompensacao == null && dataFinalCompensacao == null
                && dataInicialLancamento == null && dataFinalLancamento == null
                && UteisValidacao.emptyString(codAutorizacao)
                && UteisValidacao.emptyString(nsu)
                && UteisValidacao.emptyString(cpf)
                && UteisValidacao.emptyString(getNumeroDocumento())
                && UteisValidacao.emptyString(nomeClienteContrato)
                && UteisValidacao.emptyString(matricula)
                && UteisValidacao.emptyString(getChequeVO().getAgencia())
                && UteisValidacao.emptyString(getChequeVO().getNumero())
                && UteisValidacao.emptyString(getChequeVO().getConta())
                && UteisValidacao.emptyString(nomeTerceiro)
                && codigoLoteFiltro.trim().isEmpty()) {
            throw new Exception("Informe pelo menos um filtro para consultar (Nome Pagador, " + getEmpresaLogado().getMoeda() + ", Matrícula, Data de Compensação, Data de Lançamento, Agência, Número do cheque, Conta, Nome do terceiro ou Lote).");
        }
        if (dataInicialCompensacao != null && dataInicialCompensacao.after(dataFinalCompensacao)) {
            throw new Exception("Data Final de Compensação não pode ser menor que a Data Inicial de Compensação.");
        }
        if (dataInicialLancamento != null && dataInicialLancamento.after(dataFinalLancamento)) {
            throw new Exception("Data Final de Lançamento não pode ser menor que a Data Inicial de Lançamento.");
        }
    }

    public void validarRecebiveisMarcadoComLote() {
        setPesquisarRecebiveisSemLote(false);
        setPesquisarRecebiveis(false);
    }

    public void validarRecebiveisMarcadoSemLote() {
        setPesquisarRecebiveisComLote(false);
        setPesquisarRecebiveis(false);
    }

    public void validarRecebiveisMarcadoRecebiveis() {
        setPesquisarRecebiveisComLote(false);
        setPesquisarRecebiveisSemLote(false);
    }

    public void selecionarUsuarioSuggestionBox() throws Exception {
        UsuarioVO usuarioVO = (UsuarioVO) request().getAttribute("result");
        if (usuarioVO != null) {
            setOperadorCaixa(usuarioVO);
        }
    }

    private void preparaHoraCompensacao() {
        if (getDataInicialCompensacao() != null && (getHoraInicialCompensacao() == null || getHoraInicialCompensacao().trim().isEmpty())) {
            setHoraInicialCompensacao("00:00:00");
        }
        if (getDataFinalCompensacao() != null && (getHoraFinalCompensacao() == null || getHoraFinalCompensacao().trim().isEmpty())) {
            setHoraFinalCompensacao("23:59:59");
        }
    }

    private void preparaHoraLancamento() {
        if (getDataInicialLancamento() != null && (getHoraInicialLancamento() == null || getHoraInicialLancamento().trim().isEmpty())) {
            setHoraInicialLancamento("00:00:00");
        }
        if (getDataFinalLancamento() != null && (getHoraFinalLancamento() == null || getHoraFinalLancamento().trim().isEmpty())) {
            setHoraFinalLancamento("23:59:59");
        }
    }

    public void consultarConciliacao(){
        try {
            setMsgAlert("");
            limparMsg();
            notificarRecursoEmpresa(RecursoSistema.CONCILIACAO);
            if ((TipoConciliacaoEnum.VENDAS.getCodigo().equals(tipoConciliacao) || TipoConciliacaoEnum.PAGAMENTOS.getCodigo().equals(tipoConciliacao)) &&
                    (dataInicialConciliacao != null && dataFinalConciliacao == null) || (dataInicialConciliacao == null && dataFinalConciliacao != null)) {
                montarAviso("Informe um período correto.");
                return;
            }

            if(dataInicialConciliacao == null
                    && dataFinalConciliacao == null
                    && UteisValidacao.emptyString(ro)
                    && UteisValidacao.emptyString(codAutorizacaoedi)
                    && UteisValidacao.emptyString(nsu)
                    && !(tipoConciliacao == 6)){
                montarAviso("Informe pelo menos um filtro valido.");
                return;
            }

            limparMsg();
            extratoDiarioBack = getFacade().getExtratoDiarioItem().consultarExtratoParaConciliacao(
                    dataInicialConciliacao, dataFinalConciliacao, ro, codAutorizacaoedi, tipoConciliacao, empresaRel.getCodigo(),
                    codigoConvenio, fpSelecionada, apresentarPagamentosCancelados, operadoraCartao, adquirente, nsu);
            if(extratoDiarioBack.isEmpty()){
                montarAviso("Nenhum extrato foi encontrado no período.");
            }
            filtrarLista();
            setFiltros(detalharFiltros());
            if(!UteisValidacao.emptyNumber(fpSelecionada)){
                tipoMovimentar = getFacade().getFormaPagamento().consultarTipoFormasPagamento(fpSelecionada);
            }
            setMsgAlert("Richfaces.hideModalPanel('modalPanelFiltrosGestaoRecebiveis')");
        } catch (Exception e) {
            e.printStackTrace();
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }


    private boolean validaFiltrosConsulta() {
        //verifica se os filtros est?o preenchidos e soma 1 cada vez que encontra um filtro preenchido na vari?vel contadora de filtros preenchidos
        try {
            int contafiltrosPreenchidos = (analisarSePrecisaPesquisarPorLote() != null && analisarSePrecisaPesquisarPorLote() && validarTextoMinimo(codigoLoteFiltro, "Lote", 1) ? 1 : 0)
                    + (validarTextoMinimo(nome, "Nome do cliente", 3) ? 1 : 0)
                    + (validarTextoMinimo(nomeTerceiro, "Nome terceiro", 3) ? 1 : 0)
                    + (validarTextoMinimo(cpf, displayIdentificadorFront[0], 3) ? 1 : 0)
                    + (validarTextoMinimo(matricula, "Matrícula", 1) ? 1 : 0)
                    + (validarTextoMinimo(nomeClienteContrato, "Nome do Cliente", 3) ? 1 : 0)
                    + (validarTextoMinimo(codAutorizacao, "Cód. Autorização", 2) ? 1 : 0)
                    + (validarTextoMinimo(nsu, "NSU", 2) ? 1 : 0)
                    + (operadorCaixa != null && operadorCaixa.getCodigo() > 0 ? 1 : 0)
                    + (codigosListaFiltro != null && !codigosListaFiltro.isEmpty() ? 1 : 0)
                    + (operadoraCartao != null && operadoraCartao > 0 ? 1 : 0)
                    + (chequeAvista ? 1 : 0)
                    + (chequeAprazo ? 1 : 0);
            if (contafiltrosPreenchidos > 0) {
                return true;
            } else {
                //verifica se as datas de faturamento est?o preenchidas e se a diferen?a entre elas ? menor que 31 dias
                boolean datasFaturamentoPreenchidas = Calendario.validaDataPreenchida(dataInicialLancamento) && Calendario.validaDataPreenchida(dataFinalLancamento);
                //verifica se as datas de compensa??o est?o preenchidas e se a diferen?a entre elas ? menor que 31 dias
                boolean datasCompensacaoPreenchidas = Calendario.validaDataPreenchida(dataInicialCompensacao) && Calendario.validaDataPreenchida(dataFinalCompensacao);

                boolean dataFaturamentoValida = true;
                if (datasFaturamentoPreenchidas) {
                    dataFaturamentoValida = Calendario.diferencaEmDias(dataInicialLancamento, dataFinalLancamento) <= 93;
                }

                boolean dataCompensacaoValida = true;
                if (datasCompensacaoPreenchidas) {
                    dataCompensacaoValida = Calendario.diferencaEmDias(dataInicialCompensacao, dataFinalCompensacao) <= 93;
                }

                if (!dataFaturamentoValida || !dataCompensacaoValida) {
                    montarErro("Para pesquisar um período superior à 93 dias, selecione mais um filtro ");
                    return false;
                }
                return true;
            }
        } catch (Exception ce) {
            montarErro(ce.getMessage());
            return false;
        }
    }

    private boolean validarTextoMinimo(String texto, String nomeFiltro, int tamanhoMinimo) throws ConsistirException {
        if (UteisValidacao.emptyString(texto)) {
            return false;
        }
        if (texto.length() < tamanhoMinimo) {
            throw new ConsistirException("O filtro " + nomeFiltro + " deve ter pelo menos " + tamanhoMinimo + " caracteres");
        }

        return true;
    }

    public void consultarRecebiveis() {
        if(validaFiltrosConsulta()){
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            limparConciliacao();
            limparDevolucoes();
            setMensagemDetalhada("", "");
            setMensagemID("");
            Integer codigo = 0;
            try {
                if (considerarDataCompensacaoOriginal && (dataInicialCompensacao == null || dataFinalCompensacao == null)) {
                    throw  new Exception("Para utilizar o filtro Considerar Compensação Original, precisa preencher os campos Período de Compensação.");
                }
                consultarDados();

                MemoryPoolMXBean iter2 = ManagementFactory.getMemoryPoolMXBeans().get(1);
                System.out.println("Final Usage: " + iter2.getUsage());

                setarGrafico();
                if ((listaConsultaRecebiveis == null || listaConsultaRecebiveis.isEmpty())
                        && (devolucoes == null || devolucoes.isEmpty())) {
                    setMensagem("Recebível não encontrado");
                    montarAviso("Nenhum recebível foi encontrado no período.");
                }
                resetou = false;
            } catch (Exception e) {
                montarErro(e.getMessage() + (UteisValidacao.emptyNumber(codigo) ? "" : codigo.toString()));
            } finally {
                if (getDataInicialLancamento() != null && getDataFinalLancamento() != null && getDataInicialCompensacao() != null && getDataFinalCompensacao() != null) {
                    notificarRecursoEmpresa(RecursoSistema.GESTAO_RECEBIVEIS_FATURAMENTO_RECEBIDO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicialLancamento(), getDataFinalLancamento()) + 1));
                    notificarRecursoEmpresa(RecursoSistema.GESTAO_RECEBIVEIS_PERIODO_COMPENSACAO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicialCompensacao(), getDataFinalCompensacao()) + 1));
                } else if (getDataInicialLancamento() != null && getDataFinalLancamento() != null) {
                    notificarRecursoEmpresa(RecursoSistema.GESTAO_RECEBIVEIS_FATURAMENTO_RECEBIDO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicialLancamento(), getDataFinalLancamento()) + 1));
                } else if (getDataInicialCompensacao() != null && getDataFinalCompensacao() != null) {
                    notificarRecursoEmpresa(RecursoSistema.GESTAO_RECEBIVEIS_PERIODO_COMPENSACAO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicialCompensacao(), getDataFinalCompensacao()) + 1));
                }
            }
        }
    }

    private void consultarDados() throws Exception {
        preparaFormasPagamento();
        setFiltros(detalharFiltros());
        chequeComposicao = new ArrayList<Integer>();
        cartaoComposicao = new ArrayList<Integer>();
        qtdeTotal = 0;
        valorTotal = 0.0;
        totalNaoFisicamente = 0;
        totalEspecie = 0;
        totalBoleto = 0;
        arrayGrafico = new JSONArray();
        formaPagamentoSelecionada = new FormaPagamentoVO();
        validarDados();
        zerarFormasPagamento();

        preparaHoraCompensacao();
        preparaHoraLancamento();

        codigosListaFiltro.clear();
        try {
            codigoLoteFiltro = codigoLoteFiltro.replace(" ", "");
            for (String c : Arrays.asList(codigoLoteFiltro.split(Pattern.quote(",")))) {
                if (!c.trim().isEmpty())
                    codigosListaFiltro.add(Integer.valueOf(c));
            }
        } catch (Exception e) {
            throw  new Exception("Por favor entre com os números separados por vírgula. Ex 50,51,52.");
        }
        listaCartoes = null;
        listaCartoesOriginal = null;
        listaCheques = null;
        listaConsultaRecebiveis = null;
        MemoryPoolMXBean iter1 = ManagementFactory.getMemoryPoolMXBeans().get(1);
        System.out.println("Initial Usage: " + iter1.getUsage());
        // consulta todos os pagamentos de acordo com os filtros
        setListaConsultaRecebiveis(getFacade().getMovPagamento().consultarComFiltros(getEmpresasSelecionadas(),
                nome,
                nomeTerceiro,
                dataInicialLancamento,
                dataFinalLancamento,
                horaInicialLancamento,
                horaFinalLancamento,
                dataInicialCompensacao,
                dataFinalCompensacao,
                horaInicialCompensacao,
                horaFinalCompensacao,
                "",
                cpf,
                matricula,
                nomeClienteContrato,
                chequeVO,
                analisarSePrecisaPesquisarPorLote(),
                getOperadorCaixa(),
                codAutorizacao,
                operadoraCartao,
                nsu,
                chequeAvista,
                chequeAprazo,
                considerarDataCompensacaoOriginal,
                codigosListaFiltro,
                mostrarCancelados,
                Uteis.NIVELMONTARDADOS_PARCELA,
                centroCustoTO.getCodigo(),
                antecipados,
                statusMovimentacao, adquirente, this.getNumeroDocumento()));

        removerPossiveisPgtosDuplicadosDaLista(getListaConsultaRecebiveis());
        Map<Integer, String> mapaCPF = new HashMap<>();
        for (MovPagamentoVO item: getListaConsultaRecebiveis()) {
            if (!UteisValidacao.emptyNumber(item.getPessoa().getCodigo()) && (item.getCpfPagador() == null || UteisValidacao.emptyString(item.getCpfPagador()))) {
                if (!mapaCPF.containsKey(item.getPessoa().getCodigo())) {
                    String cpf = getFacade().getPessoa().consultarPorCodigo(item.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).getCfp();
                    mapaCPF.put(item.getPessoa().getCodigo(), cpf);
                }
                item.setCpfPagador(mapaCPF.get(item.getPessoa().getCodigo()));
            }
            item.setNomePagador(item.getNomePagador());
            item.setResponsavelPagador(item.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla()) ||
                    item.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla()) &&
                            item.getPessoa().isMenorIdade() ? item.getPessoa().getNomeResponsaveis() : "");
        }

        listaCheques = new ArrayList<ChequeTO>();
        listaCartoes = new ArrayList<CartaoCreditoTO>();
        listaOutros = new ArrayList<MovPagamentoVO>();

        if (UteisValidacao.emptyString(getOperadorCaixa().getNome()) && UteisValidacao.emptyString(nomeClienteContrato) && codigoLoteFiltro.trim().isEmpty()
                && !UteisValidacao.emptyList(getEmpresasSelecionadas()) && !UteisValidacao.emptyList(getEmpresas())) {
            for(EmpresaVO empresa: getEmpresas()){
                if(getEmpresasSelecionadas().contains(empresa.getCodigo().toString())){
                    getListaConsultaRecebiveis().addAll(getFacade().
                            getRecebivelAvulso().consultaParaGestaoRecebiveis(dataInicialCompensacao, dataFinalCompensacao,
                            dataInicialLancamento, dataFinalLancamento, empresa, matricula,
                            nome, nomeTerceiro, chequeVO, codAutorizacao, nsu, operadoraCartao, analisarSePrecisaPesquisarPorLote(), cpf));
                }
            }
        }
        salvarFiltroSessao();
        for (FormaPagamentoVO fp : lista) {
            fp.setQuantidade(0);
            fp.setValor(0.0);
        }
        lista = getFacade().getFinanceiro().getRecebiveis().montarTotais(lista, listaConsultaRecebiveis, analisarSePrecisaPesquisarPorLote(),
                chequeAvista, chequeAprazo, dataInicialCompensacao, dataFinalCompensacao, considerarDataCompensacaoOriginal,
                statusMovimentacao, chequeVO, nomeConta, mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);
        if (codigoLoteFiltro.trim().isEmpty() && UteisValidacao.emptyString(nsu)
                && UteisValidacao.emptyString(codAutorizacao)
                && UteisValidacao.emptyString(getChequeVO().getConta())
                && UteisValidacao.emptyString(getChequeVO().getNumero())
                && UteisValidacao.emptyString(getChequeVO().getAgencia())
                && UteisValidacao.emptyString(nomeTerceiro)) {
            consultarDevolucoes();
        }
        setItemImpressao(new RelatorioGestaoRecebiveisTO());
    }

    public void removerPossiveisPgtosDuplicadosDaLista(List<MovPagamentoVO> listaMovPagamentos) throws Exception {
        try {
            if (!UteisValidacao.emptyList(listaMovPagamentos)) {
                Map<String, MovPagamentoVO> mapaPagamentos = new HashMap<>();
                List<MovPagamentoVO> novaListaAposTratamento = new ArrayList<>();

                // Identificar duplicados e armazenar o correto
                for (MovPagamentoVO mov : listaMovPagamentos) {
                    String codigo = String.valueOf(mov.getCodigo());

                    if (!mapaPagamentos.containsKey(codigo)) {
                        mapaPagamentos.put(codigo, mov);
                    } else {
                        // Já existe um pagamento com o mesmo código, substituí-lo pelo correto
                        MovPagamentoVO movpCorreto = getFacade().getMovPagamento()
                                .consultarPorChavePrimaria(mov.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        movpCorreto.setMatriculaPagador(String.valueOf(getFacade().getCliente()
                                .consultarPorCodigoPessoa(movpCorreto.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS)
                                .getCodigoMatricula()));
                        movpCorreto.setCpfPagador(movpCorreto.getPessoa().getCfp());
                        mapaPagamentos.put(codigo, movpCorreto);
                    }
                }

                // Adicionar todos os pagamentos únicos (ou corrigidos) à nova lista
                novaListaAposTratamento.addAll(mapaPagamentos.values());

                // Atualizar a lista de consulta com a nova lista tratada
                setListaConsultaRecebiveis(novaListaAposTratamento);
            }
        } catch (Exception ex) {
            setListaConsultaRecebiveis(listaMovPagamentos);
        }
    }

    public void trocarMostrarZerados() throws Exception{
        mostrarZerados = !mostrarZerados;
        setarGrafico();
    }
    public void setarGrafico() throws JSONException {
        for(FormaPagamentoVO fp : lista){
            qtdeTotal += fp.getQuantidade();
            if (fp.getTipoFormaPagamento().equals("CC")) {
                totalNaoFisicamente = Uteis.arredondarForcando2CasasDecimais(totalNaoFisicamente + fp.getValor());
            } else {
                qtdeTotal++;
                valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal + fp.getValor());
                if (fp.getTipoFormaPagamento().equals("BB")) {
                    totalBoleto = Uteis.arredondarForcando2CasasDecimais(totalBoleto + fp.getValor());
                } else {
                    totalEspecie = Uteis.arredondarForcando2CasasDecimais(totalEspecie + fp.getValor());
                }
            }
        }

        arrayGrafico = new JSONArray();
        List<String> keyList = new ArrayList<String>(mapaPagamentosParaGrafico.keySet());
        Collections.sort(keyList);
        lista = Ordenacao.ordenarLista(lista, "valor");
        Collections.reverse(lista);
        for (FormaPagamentoVO fp : lista) {
            fp.setPercentual((totalNaoFisicamente+totalEspecie+totalBoleto) == 0.0 ?  0.0 : Uteis.arredondarForcando2CasasDecimais((fp.getValor()*100)/(totalNaoFisicamente+totalEspecie+totalBoleto)));
            if (fp.getValor() > 0.0 || mostrarZerados) {
                JSONObject obj = new JSONObject();
                obj.put("forma", fp.getDescricao().replaceFirst(" ", "\n"));
                obj.put("cor", mapaFormasCores.get(fp.getDescricao()));
                obj.put("codigo", fp.getCodigo());
                obj.put("tipo", fp.getTipoFormaPagamento());
                obj.put("percent", fp.getPercentual());
                obj.put("valor", Uteis.arredondarForcando2CasasDecimais(fp.getValor()));
                obj.put("sigla", fp.getSigla());
                arrayGrafico.put(obj);
            }
        }
    }


    private void limparDevolucoes(){
        setTotalDevolucoes(0.0);
        devolucoes = new ArrayList<MovContaVO>();
    }
    private void consultarDevolucoes() throws Exception {
        setTotalDevolucoes(0.0);

        if(!UteisValidacao.emptyList(getEmpresasSelecionadas()) && !UteisValidacao.emptyList(getEmpresas())) {
            for(EmpresaVO empresa: getEmpresas()){
                if(getEmpresasSelecionadas().contains(empresa.getCodigo().toString())){
                    devolucoes.addAll(getFacade().getFinanceiro().getMovConta().consultarDevolucoes(
                            dataInicialLancamento == null ? null : (Calendario.getDataComHora(dataInicialLancamento, (horaInicialLancamento + "00"))),
                            dataFinalLancamento == null ? null : (Calendario.getDataComHora(dataFinalLancamento, (horaFinalLancamento + "59"))),
                            dataInicialCompensacao == null ? null : (Calendario.getDataComHora(dataInicialCompensacao, (horaInicialCompensacao + "00"))),
                            dataFinalCompensacao == null ? null : (Calendario.getDataComHora(dataFinalCompensacao, (horaFinalCompensacao + "59"))),
                            empresa.getCodigo(), nome, getOperadorCaixa().getNome(), cpf, matricula)
                    );
                }
            }
        }

        if (devolucoes != null && !devolucoes.isEmpty()) {
            int tamanho = listaResumo.size();
            if (tamanho > 0) {
                Double valor = 0.0;
                for (MovContaVO dev : devolucoes) {
                    valor += dev.getValor();
                }
                totalDevolucoes = valor;
            }
        }
    }

    private Boolean analisarSePrecisaPesquisarPorLote() {
        //analisar se pesquisa precisa ser feita somente com lote ou somente sem lote
        Boolean pesquisarComLote = null;
        if (pesquisarRecebiveisComLote) {
            pesquisarComLote = true;
        } else if (pesquisarRecebiveisSemLote) {
            pesquisarComLote = false;
        }
        return pesquisarComLote;
    }

    private void zerarFormasPagamento() {
        // percorre a lista de formas de pagamento
        for (ResumoFormaPagamentoTO rfp : getListaResumo()) {
            rfp.setTotal1(0.0);
            rfp.setLista1(new ArrayList());
            rfp.setTotal2(0.0);
            rfp.setLista2(new ArrayList());
            rfp.setTotal3(0.0);
            rfp.setLista3(new ArrayList());
            rfp.setTotal4(0.0);
            rfp.setLista4(new ArrayList());
        }
        setFormaPagamentoSelecionada(new FormaPagamentoVO());
    }

    private void limparConciliacao(){
        pendencia = true;
        nao_encontrado = true;
        ok = true;
        estornado = true;
        ro = "";
        codAutorizacaoedi = "";
        extratoDiario = new ArrayList<ExtratoDiarioItemVO>();
        extratoDiarioBack = new ArrayList<ExtratoDiarioItemVO>();
    }

    private double adicionaNaLista(String tipo, List lista, MovPagamentoVO mp) throws Exception {
        FormaPagamentoVO valorFp = mapaPagamentosParaGrafico.get(mp.getFormaPagamento().getDescricao());

        double total = 0.0;
        List temp = new ArrayList();
        // se cheque
        if (tipo.equals("CH")) {
            if (dataInicialCompensacao == null) {
                temp = getFacade().getCheque().consultarPagamentoCheques(mp.getCodigo(), analisarSePrecisaPesquisarPorLote(),
                        mp.isPagamentoAvulso(),
                        chequeAvista, chequeAprazo, Uteis.NIVELMONTARDADOS_GESTAORECEBIVEIS);
            } else {
                temp = getFacade().getCheque().consultarPorPagamentoCompensacao(mp.getCodigo(), dataInicialCompensacao,
                        dataFinalCompensacao, analisarSePrecisaPesquisarPorLote(), mp.isPagamentoAvulso(),
                        chequeAvista, chequeAprazo, considerarDataCompensacaoOriginal, Uteis.NIVELMONTARDADOS_GESTAORECEBIVEIS);
            }
            // totaliza os cheques
            Iterator i = temp.iterator();
            while (i.hasNext()) {
                ChequeVO aux = (ChequeVO) i.next();
                //desconsiderar cheques cancelados
                if (((aux.getSituacao().equals("CA") || aux.getSituacao().equals("DV")) && !mostrarCancelados)) {
                    continue;
                }
                if(chequeVO != null && !UteisValidacao.emptyString(chequeVO.getNumero())){
                    if(aux.getNumero() != null && !aux.getNumero().startsWith(chequeVO.getNumero())){
                        continue;
                    }
                }
                if (aux.getComposicao() != null && !aux.getComposicao().equals("")) {
                    Boolean presente = false;	// condi??o para verificar se algum cheque da composicao j? est? na lista
                    for (Integer codigo : chequeComposicao) {
                        if (aux.getCodigo().intValue() == codigo) {
                            presente = true; //cheque j? est? na lista
                            break;
                        }

                    }
                    if (!presente) { // se cheque não est? presente, adicionar na lista os codigos dos cheques que não podem entrar na lista
                        String[] codigos = aux.getComposicao().split(",");
                        for (String codComposicao : codigos) {
                            chequeComposicao.add(Integer.parseInt(codComposicao));
                        }
                    } else {
                        continue; // cheque j? presente na lista, não adicionar
                    }

                }
                // objeto a ser utilizado na tela
                ChequeTO novo = new ChequeTO();
                novo.setMatricula(mp.getMatriculaPagador());
                novo.setCodigo(aux.getCodigo());
                novo.setNomePagador(mp.getNomePagador());
                novo.setCpfPagador(mp.getCpfPagador());
                novo.setNumeroBanco(aux.getBanco().getCodigoBanco().toString());
                novo.setAgencia(aux.getAgencia());
                novo.setConta(aux.getConta());
                novo.setNumero(aux.getNumero());
                novo.setDataLancamento(mp.getDataLancamento());
                novo.setDataCompensacao(aux.getDataCompensacao());
                novo.setDataOriginal(aux.getDataOriginal());
                novo.setValor(aux.getValorTotal());
                novo.setRecibo(mp.getReciboPagamento().getCodigo());
                novo.setMovConta(aux.getMovConta().getCodigo());
                novo.setCodigosComposicao(aux.getComposicao());
                novo.setNomeNoCheque(aux.getNomeNoCheque());
                novo.setCodigoPessoa(mp.getPessoa().getCodigo());
                novo.setMovPagamentoVO(mp);
                novo.setEmpresa(mp.getEmpresa());
                novo.setCodigosParcelas(mp.getCodigosParcelas());
                novo.setVencimentosParcelas(mp.getVencimentosParcelas());
                novo.setNumerosParcelas(mp.getNumerosParcelas());

                if (aux.getSituacao().equals("CA")) {
                    novo.setAtivo(false);
                }
                if (aux.getSituacao().equals("DV")) {
                    novo.setDevolvido(true);
                }
                getFacade().getFinanceiro().getHistoricoCheque().getContaLoteCheque(novo, false);
                novo.setLoteAvulso(getFacade().getLote().consultarLoteAvulso(novo.getCodigo()));
                if (UteisValidacao.emptyNumber(novo.getNumeroLote())) {
                    novo.setNumeroLote(getFacade().getLote().consultarPorCheque(novo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
                }

                if (novo.getDataFim() != null && novo.getLoteAvulso() == 0) {
                    novo.setNumeroLote(0);
                    novo.setContaContido("");
                    novo.setCodigoContaContido(0);
                    if (aux.getSituacao().equals("DV") && getFacade().getMovProduto().existeProdutoPagoDevolucaoCheque(novo.getCodigo()) ){
                        novo.setAtivo(false);
                    }
                }

                if ((getStatusMovimentacao() != null
                        && getStatusMovimentacao().equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                        && !UteisValidacao.emptyString(novo.getContaContido()))
                        || (getStatusMovimentacao() != null
                        && getStatusMovimentacao().equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                        && UteisValidacao.emptyString(novo.getContaContido()))) {
                    continue;
                }

                if (!codigoLoteFiltro.trim().isEmpty()
                        && !codigosListaFiltro.contains(Integer.valueOf(novo.getLoteAvulso()))
                        && !codigosListaFiltro.contains(Integer.valueOf(novo.getNumeroLote()))) {
                    continue;
                }

                if (!UteisValidacao.emptyString(nomeConta) && !novo.getContaContido().toLowerCase().contains(nomeConta.toLowerCase())) {
                    continue;
                }
                lista.add(novo);
                valorFp.setQuantidade(valorFp.getQuantidade()+1);
                qtdeTotal++;
                valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal + aux.getValorTotal());
                totalEspecie = Uteis.arredondarForcando2CasasDecimais(totalEspecie + aux.getValorTotal());
                total = Uteis.arredondarForcando2CasasDecimais(total + aux.getValorTotal());
                aux = null;
            }
            // se cartao de credito
        } else if (tipo.equals("CA")) {
            if (dataInicialCompensacao == null) {
                temp = getFacade().getCartaoCredito().consultarPorMovPagamento(mp.getCodigo(), analisarSePrecisaPesquisarPorLote(), mp.isPagamentoAvulso(),
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            } else {
                temp = getFacade().getCartaoCredito().consultarPorPeriodoCompensacao(mp.getCodigo(), dataInicialCompensacao,
                        dataFinalCompensacao, analisarSePrecisaPesquisarPorLote(), mp.isPagamentoAvulso(),
                        Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, considerarDataCompensacaoOriginal);
            }
            // totaliza os cartoes de credito
            Iterator i = temp.iterator();
            while (i.hasNext()) {
                CartaoCreditoVO aux = (CartaoCreditoVO) i.next();
                if (aux.getSituacao().equals("CA") && !mostrarCancelados) {
                    continue;
                }

                if (aux.getComposicao() != null && !aux.getComposicao().equals("")) {
                    Boolean presente = false;
                    for (Integer codigo : cartaoComposicao) {
                        if (aux.getCodigo().intValue() == codigo) {
                            presente = true;
                            break;
                        }

                    }
                    if (!presente) {
                        String[] codigos = aux.getComposicao().split(",");
                        for (String codComposicao : codigos) {
                            cartaoComposicao.add(Integer.parseInt(codComposicao));
                        }
                    } else {
                        continue;
                    }

                }


                // objeto a ser utilizado na tela
                CartaoCreditoTO novo = new CartaoCreditoTO();
                novo.setMatricula(mp.getMatriculaPagador());
                novo.setCodigo(aux.getCodigo());
                novo.setAdquirente(mp.getAdquirenteVO().getNome());
                novo.setAdquirenteCod(mp.getAdquirenteVO().getCodigo());
                novo.setOperadora(aux.getOperadora().getDescricao());
                novo.setOperadoraCodigo(aux.getOperadora().getCodigo());

                novo.setNomePagador(mp.getNomePagador());
                novo.setCpfPagador(mp.getCpfPagador());
                novo.setDataLancamento(mp.getDataLancamento());
                novo.setDataCompensacao(aux.getDataCompensacao());
                novo.setDataOriginal(aux.getDataOriginal());
                novo.setValor(aux.getValorTotal());
                novo.setRecibo(mp.getReciboPagamento().getCodigo());
                novo.setAutorizacao(mp.getAutorizacaoCartao());
                novo.setNsu(mp.getNsu());
                novo.setNumeroLote(aux.getLote().getCodigo());
                if (!codigoLoteFiltro.trim().isEmpty()
                        && !codigosListaFiltro.contains(Integer.valueOf(novo.getNumeroLote()))) {
                    continue;
                }
                novo.setCodigosComposicao(aux.getComposicao());
                novo.setCodigoPessoa(mp.getPessoa().getCodigo());
                novo.setMovConta(aux.getMovConta().getCodigo());
                novo.setNrVezes(aux.getMovpagamento().getQtdVezes());
                novo.setMovPagamentoVO(mp);
                if (aux.getSituacao().equals("CA")) {
                    novo.setAtivo(false);
                }
                getFacade().getCartaoCredito().getContaLoteCartao(novo, novo.getNumeroLote());
                novo.setEmpresa(mp.getEmpresa());
                novo.setCodigosParcelas(mp.getCodigosParcelas());
                novo.setVencimentosParcelas(mp.getVencimentosParcelas());
                novo.setNumerosParcelas(mp.getNumerosParcelas());

                if ((getStatusMovimentacao() != null
                        && getStatusMovimentacao().equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                        && !UteisValidacao.emptyString(novo.getContaContido()))
                        || (getStatusMovimentacao() != null
                        && getStatusMovimentacao().equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                        && UteisValidacao.emptyString(novo.getContaContido()))) {
                    continue;
                }

                if (!UteisValidacao.emptyString(nomeConta) && !novo.getContaContido().toLowerCase().contains(nomeConta.toLowerCase())) {
                    continue;
                }

                lista.add(novo);
                valorFp.setQuantidade(valorFp.getQuantidade()+1);
                qtdeTotal++;
                valorTotal = valorTotal + Uteis.arredondarForcando2CasasDecimais(aux.getValorTotal());
                totalEspecie = totalEspecie + Uteis.arredondarForcando2CasasDecimais(aux.getValorTotal());
                total = total + Uteis.arredondarForcando2CasasDecimais(aux.getValorTotal());
                aux = null;
            }
        } else {
            if ((getStatusMovimentacao() != null
                    && getStatusMovimentacao().equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                    && !UteisValidacao.emptyString(mp.getContaFinanceiro()))
                    || (getStatusMovimentacao() != null
                    && getStatusMovimentacao().equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                    && UteisValidacao.emptyString(mp.getContaFinanceiro()))) {
                return total;
            }
            if (!UteisValidacao.emptyString(nomeConta) && !mp.getContaFinanceiro().toLowerCase().contains(nomeConta.toLowerCase())) {
                return total;
            }
            if (!UteisValidacao.emptyNumber(mp.getValor()) || tipo.equals("CC")) {
                if(tipo.equals("CD")){
                    mp.setDataAux(Uteis.getData(considerarDataCompensacaoOriginal ? mp.getDataPagamento() : mp.getDataPagamentoOriginal()));

                }
                lista.add(mp);
                valorFp.setQuantidade(valorFp.getQuantidade()+1);
                if (tipo.equals("CC")) {
                    totalNaoFisicamente = Uteis.arredondarForcando2CasasDecimais(totalNaoFisicamente + mp.getValorTotal());
                } else {
                    qtdeTotal++;
                    valorTotal = Uteis.arredondarForcando2CasasDecimais(valorTotal + mp.getValorTotal());
                    if (tipo.equals("BB")) {
                        totalBoleto = Uteis.arredondarForcando2CasasDecimais(totalBoleto + mp.getValorTotal());
                    } else {
                        totalEspecie = Uteis.arredondarForcando2CasasDecimais(totalEspecie + mp.getValorTotal());
                    }
                }
                total = Uteis.arredondarForcando2CasasDecimais(mp.getValorTotal());
            }
        }

        valorFp.setValor(valorFp.getValor()+total);
        return total;
    }

    private void preparaFormasPagamento() throws Exception {
        // limpa a lista anterior
        mapaFormasCores = new HashMap<String, String>();
        setListaResumo(new ArrayList<ResumoFormaPagamentoTO>());
        // consulta as formas de pagamento existente no sistema
        lista = getFacade().getFormaPagamento().consultarPorDescricaoTipoFormaPagamento("", false, false, false, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        int indicador = 0;
        ResumoFormaPagamentoTO rfp = new ResumoFormaPagamentoTO();
        // percorre as formas de pagamento cadastradas para montar os totalizadores
        for (FormaPagamentoVO fpvo : lista) {
            if (UteisValidacao.emptyString(fpvo.getCor())) {
                fpvo.setCor(fpvo.getCorRandom());
                getFacade().getFormaPagamento().atualizarCorFormaPagamento(fpvo.getCodigo(), fpvo.getCor());
            }
            mapaFormasCores.put(fpvo.getDescricao(), fpvo.getCor());
            mapaPagamentosParaGrafico.put(fpvo.getDescricao(), fpvo);
            // pega o tipo de lista que deve ser apresentada na tela
            String tipoLista = (fpvo.getTipoFormaPagamento().isEmpty() ? "closed"
                    : (fpvo.getTipoFormaPagamento().equals("CH") ? "cheque"
                    : (fpvo.getTipoFormaPagamento().equals("CA") ? "cartao"
                    : (fpvo.getTipoFormaPagamento().equals("CD") ? "debito"
                    : "outros"))));
            // cada linha da tela pode mostrar 4 formas de pagamento
            switch (indicador) {
                case 0:
                    // cria um objeto para ser usado na tela
                    rfp = new ResumoFormaPagamentoTO();
                    rfp.setDescricao1(fpvo.getDescricao());
                    rfp.setTotal1(0.0);
                    rfp.setTipo1(fpvo.getTipoFormaPagamento());
                    rfp.setMostrar1(true);
                    rfp.setLista1(new ArrayList());
                    rfp.setTipoLista1(tipoLista);
                    rfp.setFormaPagamento1(fpvo);
                    break;
                case 1:
                    rfp.setDescricao2(fpvo.getDescricao());
                    rfp.setTotal2(0.0);
                    rfp.setTipo2(fpvo.getTipoFormaPagamento());
                    rfp.setMostrar2(true);
                    rfp.setLista2(new ArrayList());
                    rfp.setTipoLista2(tipoLista);
                    rfp.setFormaPagamento2(fpvo);
                    break;
                case 2:
                    rfp.setDescricao3(fpvo.getDescricao());
                    rfp.setTotal3(0.0);
                    rfp.setTipo3(fpvo.getTipoFormaPagamento());
                    rfp.setMostrar3(true);
                    rfp.setLista3(new ArrayList());
                    rfp.setTipoLista3(tipoLista);
                    rfp.setFormaPagamento3(fpvo);
                    break;
                case 3:
                    indicador = -1;
                    rfp.setDescricao4(fpvo.getDescricao());
                    rfp.setTotal4(0.0);
                    rfp.setTipo4(fpvo.getTipoFormaPagamento());
                    rfp.setMostrar4(true);
                    rfp.setLista4(new ArrayList());
                    rfp.setTipoLista4(tipoLista);
                    rfp.setFormaPagamento4(fpvo);
                    getListaResumo().add(rfp);
                    break;
            }
            // variavel que indica qual coluna esta sendo preenchida
            indicador++;
        }
        // o loop salva apenas quando o indicador ? igual a 3,
        // por isso se for menor que 3 deve salvar o objeto na lista
        if (indicador > 0) {
            getListaResumo().add(rfp);
        }
    }

    public void escolheLista1() {
        escolheLista(1);
    }

    public void escolheLista2() {
        escolheLista(2);
    }

    public void escolheLista3() {
        escolheLista(3);
    }

    public void escolheLista4() {
        escolheLista(4);
    }

    private void escolheLista(int opcao) {
        desmarcarSelecionados();
        limparMsg();
        try {
            // pega o objeto da tela
            ResumoFormaPagamentoTO obj = (ResumoFormaPagamentoTO) context().getExternalContext().getRequestMap().get("fp");
            // se nao foi encontrado o objeto selecionado
            if (obj == null) {
                throw new Exception("Erro ao posicionar a forma de pagamento escolhida. Contate o suporte técnico.");
            }
            setarListaRecebiveis(opcao, obj);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void desmarcarSelecionados() {
        todosCartoesMarcados = false;
        todosChequesMarcados = false;
        todosPagamentosMarcados = false;
        setQtdeTotalCartoes(0);
        setQtdeTotalCheques(0);
        setQtdeTotalVista(0);
        setTotalCheques(0.0);
        setTotalCartoes(0.0);
        setTotalVista(0.0);
        for (ChequeTO cheque : listaCheques) {
            cheque.setChequeEscolhido(false);
        }
        for (CartaoCreditoTO cartao : listaCartoes) {
            cartao.setCartaoEscolhido(false);
        }
        for (MovPagamentoVO pagamento : listaOutros) {
            pagamento.setMovPagamentoEscolhidaFinan(false);
        }
    }

    public void setarListaRecebiveis(int opcao, ResumoFormaPagamentoTO obj) {
        List aux = (opcao == 1 ? obj.getLista1()
                : (opcao == 2 ? obj.getLista2()
                : (opcao == 3 ? obj.getLista3()
                : obj.getLista4())));
        setEscolha(opcao == 1 ? obj.getTipoLista1()
                : (opcao == 2 ? obj.getTipoLista2()
                : (opcao == 3 ? obj.getTipoLista3()
                : obj.getTipoLista4())));
        setFormaPagamentoSelecionada(opcao == 1 ? obj.getFormaPagamento1()
                : (opcao == 2 ? obj.getFormaPagamento2()
                : (opcao == 3 ? obj.getFormaPagamento3()
                : obj.getFormaPagamento4())));

        if (getEscolha().equals("debito")) {
            setExibirAutorizacao(true);
        } else {
            setExibirAutorizacao(false);
        }

        setExibirNSU(getFormaPagamentoSelecionada().isApresentarNSU());

        setEscolha(aux.isEmpty() ? "closed" : (getEscolha().equals("debito") ? "outros" : getEscolha()));
        if (getEscolha().equals("cheque")) {
            setListaCheques(aux);
        } else if (getEscolha().equals("cartao")) {
            setListaCartoes(aux);
        } else if (getEscolha().equals("devolucoes")) {
            setListaDevolucoes(aux);
        } else {
            try {
                MovPagamentoVO pagamento = (MovPagamentoVO) aux.get(0);
                if (pagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
                    setContaCorrente(true);
                } else {
                    setContaCorrente(false);
                }
            } catch (Exception e) {
                setContaCorrente(false);
            }

            setListaOutros(aux);
        }
    }

    public void calcularTotalVista() {
        setTotalVista(0.0);
        qtdeTotalVista = 0;
        // percorre todos os pagamentos da lista
        Iterator<MovPagamentoVO> i = getListaOutros().iterator();
        while (i.hasNext()) {
            MovPagamentoVO mp = i.next();
            // soma somente os pagamentos marcados
            if (mp.getMovPagamentoEscolhidaFinan()) {
                totalVista += mp.getValor();
                qtdeTotalVista++;
            }
        }
    }

    public Integer montarLote() throws Exception {
        lote = new LoteVO();
        lote.setCodigo(0);
        lote.setDescricao("");
        lote.setValor(0.0);
        lote.setCartoes(new ArrayList<CartaoCreditoVO>());
        lote.setCheques(new ArrayList<ChequeVO>());

        lote.setCartoesTO(new ArrayList<CartaoCreditoTO>());
        lote.setChequesTO(new ArrayList<ChequeTO>());
        // prepara o lote
        lote.setEmpresa(empresaRel);
        lote.setDataLancamento(Calendario.hoje());
        lote.setUsuarioResponsavel(usuarioResponsavel);

        Integer conta = null;

        // verifica qual a lista de deposito na tela
        if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
            lote.setCheques(new ArrayList<ChequeVO>());
            // percorre a lista verificando quais elementos estao marcados
            for (ChequeTO ch : listaCheques) {
                if (ch.isChequeEscolhido()) {
                    ChequeVO aux = new ChequeVO();

                    if (conta == null) {
                        conta = UteisValidacao.emptyNumber(ch.getCodigoContaContido()) ? 0 : ch.getCodigoContaContido();
                    } else {
                        if (!conta.equals(ch.getCodigoContaContido())) {
                            throw new Exception("Os cheques devem estar na mesma conta, ou em nenhuma, para serem movimentados juntos.");
                        }
                    }
                    aux.setCodigo(ch.getCodigo());
                    aux.setDataCompensacao(ch.getDataCompensacao());
                    aux.setComposicao(ch.getCodigosComposicao());
                    aux.setDataOriginal(ch.getDataOriginal());
                    aux.setValor(ch.getValor());
                    lote.adicionarCheque(aux);
                    lote.getChequesTO().add(ch);
                }
            }
        } else if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            lote.setCartoes(new ArrayList<CartaoCreditoVO>());
            // percorre a lista verificando quais elementos estao marcados
            for (CartaoCreditoTO ca : listaCartoes) {
                if (ca.isCartaoEscolhido()) {
                    if (conta == null) {
                        conta = UteisValidacao.emptyNumber(ca.getCodigoContaContido()) ? 0 : ca.getCodigoContaContido();
                    } else {
                        if (!conta.equals(ca.getCodigoContaContido())) {
                            throw new Exception("Os cartões devem estar na mesma conta, ou em nenhuma, para serem movimentados juntos.");
                        }
                    }

                    CartaoCreditoVO aux = new CartaoCreditoVO();
                    aux.setCodigo(ca.getCodigo());
                    aux.setComposicao(ca.getCodigosComposicao());
                    aux.setDataCompensacao(ca.getDataCompensacao());
                    aux.setDataOriginal(ca.getDataOriginal());
                    aux.setValor(ca.getValor());
                    lote.adicionarCartao(aux);
                    lote.getCartoesTO().add(ca);
                }
            }
        } else if (getExibirAutorizacao() || (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla()))) {
            String codigosMovConta = "";
            boolean aindaNaoMovimentado = false;
            for (MovPagamentoVO movPag : listaOutros) {
                if (movPag.getMovPagamentoEscolhidaFinan()) {
                    if(movPag != null && movPag.getMovconta() != null && movPag.getMovconta().equals(0)){
                        aindaNaoMovimentado = true;
                    } else {
                        codigosMovConta += "," + movPag.getMovconta();
                    }
                     if(aindaNaoMovimentado && !codigosMovConta.equals("")){
                        throw new Exception("Os pagamentos devem estar na mesma conta, ou em nenhuma, para serem movimentados juntos.");
                     }
                }
            }
            if(!aindaNaoMovimentado && !UteisValidacao.emptyString(codigosMovConta)){
                List<ContaVO> contas = getFacade().getFinanceiro().getConta().consultarContasPorMovContas(codigosMovConta.replaceFirst(",", ""));
                if (!UteisValidacao.emptyList(contas) && contas.size() > 1) {
                    throw new Exception("Os pagamentos devem estar na mesma conta, ou em nenhuma, para serem movimentados juntos.");
                } else if (!UteisValidacao.emptyList(contas)) {
                    conta = contas.get(0).getCodigo();
                }
            }
        }
        return conta;
    }

    public void consultarResponsavelEdicaoPagamento() {
        try {
            setResponsavelEdicaoPagamento(getFacade().getUsuario().consultarPorChavePrimaria(getResponsavelEdicaoPagamento().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }


    private void setarModuloZW(){
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if(loginControle != null){
            loginControle.setModuloAberto(ModuloAberto.ZILLYONWEB);
        }
    }

    private void setarModuloFINAN(){
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if(loginControle != null){
            loginControle.setModuloAberto(ModuloAberto.FINAN);
        }
    }

    public String autorizarEdicaoPagamento() throws Exception {
        try {
            setErro(false);
            setMensagemDetalhada("", "");
            setResponsavelEdicaoPagamento(getFacade().getControleAcesso().verificarLoginUsuario(getResponsavelEdicaoPagamento().getCodigo().intValue(), getResponsavelEdicaoPagamento().getSenha().toUpperCase()));
            if (getResponsavelEdicaoPagamento().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getResponsavelEdicaoPagamento().getAdministrador()) {
                    setarModuloZW();
                    return "editarPagamento";
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getResponsavelEdicaoPagamento().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().intValue() == usuarioPerfilAcesso.getEmpresa().getCodigo().intValue()) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), getResponsavelEdicaoPagamento(), "EdicaoPagamento", "4.22 - Edição de Pagamento");
                }
            }
            setarModuloZW();
            return "editarPagamento";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setAtencao(false);
            setErro(true);
        }
        return "";
    }

    public void irParaTelaClienteCh() throws Exception {
        boolean novoCliente = false;
        setMensagemDetalhada("", "");
        setMsgAlert("");
        try {
            ChequeTO ch = (ChequeTO) context().getExternalContext().getRequestMap().get("cheq");
            if (ch == null) {
                throw new Exception("Cliente não foi posicionado corretamente. Contate o suporte técnico.");
            }
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                novoCliente = true;
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorCodigoPessoa(ch.getCodigoPessoa(), Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            if (UteisValidacao.emptyNumber(clienteControle.getClienteVO().getCodigo())) {
                montarMsgAlert("Cliente não encontrado.");
            } else {
                setMsgAlert("abrirPopup('../../tela6.jsp', 'Cliente', 1000, 700);");
            }
            if (novoCliente) {
                context().getExternalContext().getSessionMap().put("ClienteControle", clienteControle);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaClienteCc() throws Exception {
        boolean novoCliente = false;
        setMensagemDetalhada("", "");
        try {
            CartaoCreditoTO cc = (CartaoCreditoTO) context().getExternalContext().getRequestMap().get("cart");
            if (cc == null) {
                throw new Exception("Cliente não foi posicionado corretamente. Contate o suporte técnico.");
            }
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                novoCliente = true;
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorCodigoPessoa(cc.getCodigoPessoa(), Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            if (novoCliente) {
                context().getExternalContext().getSessionMap().put("ClienteControle", clienteControle);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaClienteMp() throws Exception {
        boolean novoCliente = false;
        setMensagemDetalhada("", "");
        try {
            Integer codigoPessoa = null;

            MovPagamentoVO mp = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movp");
            if (mp == null) {
                MovContaVO movConta = (MovContaVO) context().getExternalContext().getRequestMap().get("movConta");
                if (movConta == null) {
                    throw new Exception("Cliente não foi posicionado corretamente. Contate o suporte técnico.");
                }
                codigoPessoa = movConta.getPessoaVO().getCodigo();
            } else {
                codigoPessoa = mp.getPessoa().getCodigo();
            }

            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                novoCliente = true;
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorCodigoPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            if (novoCliente) {
                context().getExternalContext().getSessionMap().put("ClienteControle", clienteControle);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void irParaTelaClienteEx() throws Exception {
        boolean novoCliente = false;
        setMensagemDetalhada("", "");
        try {
            ExtratoDiarioItemVO ex = (ExtratoDiarioItemVO) context().getExternalContext().getRequestMap().get("item");
            if (ex == null) {
                throw new Exception("Cliente não foi posicionado corretamente. Contate o suporte técnico.");
            }
            Integer codigoPessoa = getFacade().getExtratoDiarioItem().pessoaExtrato(ex);

            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                novoCliente = true;
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorCodigoPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            if (novoCliente) {
                context().getExternalContext().getSessionMap().put("ClienteControle", clienteControle);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void limparPeriodoC() {
        dataInicialCompensacao = null;
        dataFinalCompensacao = null;
    }

    public void limparPeriodoL() {
        dataInicialLancamento = null;
        dataFinalLancamento = null;
    }

    private boolean setarEscolhidos() {
        if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
            for (ChequeTO chequeTO : listaCheques) {
                if (chequeTO.isChequeEscolhido()) {
                    return true;
                }
            }
        } else if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            for (CartaoCreditoTO ccTO : listaCartoes) {
                if (ccTO.isCartaoEscolhido()) {
                    return true;
                }
            }
        } else {
            for (MovPagamentoVO mp : listaOutros) {
                if (mp.getMovPagamentoEscolhidaFinan()) {
                    return true;
                }
            }
        }
        return false;
    }

    private void limparEscolhidos() {
        for (ChequeTO chequeTO : listaCheques) {
            chequeTO.setChequeEscolhido(false);
        }
        for (CartaoCreditoTO ccTO : listaCartoes) {
            ccTO.setCartaoEscolhido(false);
        }
        for (MovPagamentoVO mp : listaOutros) {
            mp.setMovPagamentoEscolhidaFinan(false);
        }
    }
    public void decidirMovimentacaoConciliacao(){
        nrNaoConciliados = 0;
        setOnCompleteDepositar("");
        boolean temAlguemConciliado = false;
        for(ExtratoDiarioItemVO ei : extratoDiario){
            if(!SituacaoItemExtratoEnum.OK.equals(ei.getSituacao()) && !SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.equals(ei.getSituacao()) ){
                nrNaoConciliados++;
            }
            if(!temAlguemConciliado && (SituacaoItemExtratoEnum.OK.equals(ei.getSituacao()) || SituacaoItemExtratoEnum.PENDENCIAS.equals(ei.getSituacao()))){
                temAlguemConciliado = true;
            }
        }

        if(!temAlguemConciliado){
            setOnCompleteDepositar("try{ Notifier.warning(\"A movimentação pela conciliação só movimenta recebíveis conciliados.\",\"Nada pra movimentar\");} catch(e){}");
            return;
        }

        if(nrNaoConciliados > 0){
            setOnCompleteDepositar("Richfaces.showModalPanel('modalDecidir');");
        }else{
            salvaEscolha();
        }
    }

    private void validarMovimentacao()throws Exception{
        if(getFormaPagamentoSelecionada().getTipoFormaPagamento().equals("CA")
            || getFormaPagamentoSelecionada().getTipoFormaPagamento().equals("CH")){
            return;
        }
        List<String> listaSelecionado = Arrays.asList(getSelecionados().split(";"));
        for (MovPagamentoVO mp : listaOutros) {
            if( (mp.getFormaPagamento().getTipoFormaPagamento().equals("AV")) &&
                    ((mp.getMovconta() != null) && (mp.getMovconta() >0)) ){
                // Formas de pagamento avista s? podem ser movimentadas uma ?nica vez.
                if (listaSelecionado.indexOf("_"+ String.valueOf(mp.getCodigo())) >=0){
                    StringBuilder msg = new StringBuilder();
                    msg.append("Operação não permitida para pagamentos a vista. O valor ").append(mp.getValorTotal_Apresentar()).append(" vinculado ao pagador ").append(mp.getNomePagador());
                    msg.append(" já foi movimentado.");
                    throw new ConsistirException(msg.toString());
                }
            }
        }
    }

    public void salvaEscolha() {
        try {
            limparMsg();
            validarMovimentacao();
            Double valorLiquidoConciliacao = 0.0;
            if(visaoConciliacao){
                listaOutros = new ArrayList<MovPagamentoVO>();
                listaCartoes = new ArrayList<CartaoCreditoTO>();
                setTotalSelecionado(0.0);
                setFormaPagamentoSelecionada(new FormaPagamentoVO());
                getFormaPagamentoSelecionada().setTipoFormaPagamento(tipoMovimentar.getSigla());
                setExibirAutorizacao(true);
                int i = 0;
                for(ExtratoDiarioItemVO ei : extratoDiario){
                    if(ei.getSituacao().equals(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA)){
                        continue;
                    }
                    if(!UteisValidacao.emptyNumber(ei.getCodigoCartaoCredito()) && TipoFormaPagto.CARTAOCREDITO.equals(tipoMovimentar)){
                        if(TipoConciliacaoEnum.VENDAS.getCodigo().equals(ei.getTipoConciliacao())){
                            for(CartaoCreditoTO cartao : ei.getCartoes()){
                                cartao.setCartaoEscolhido(true);
                                setTotalSelecionado(Uteis.arredondarForcando2CasasDecimais(getTotalSelecionado()+cartao.getValor()));
                                listaCartoes.add(cartao);
                            }
                        }else{
                            if(ei.getCartao() != null){
                                ei.getCartao().setCartaoEscolhido(true);
                                listaCartoes.add(ei.getCartao());
                            }
                            setTotalSelecionado(Uteis.arredondarForcando2CasasDecimais(ei.getValorCCNumber() + getTotalSelecionado()));
                        }
                        valorLiquidoConciliacao =  Uteis.arredondarForcando2CasasDecimais(valorLiquidoConciliacao +  (ei.getValorLiquido() == null || ei.getValorLiquido().equals(0.0) ?
                                                    ei.getCartao().getValor(): ei.getValorLiquido()));
                    }else if(!UteisValidacao.emptyNumber(ei.getCodigoMovPagamento())
                            && ei.getMovPagamento() != null
                            && TipoFormaPagto.CARTAODEBITO.equals(tipoMovimentar)
                            && ei.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())){
                        valorLiquidoConciliacao = Uteis.arredondarForcando2CasasDecimais(valorLiquidoConciliacao + (ei.getValorLiquido() == null || ei.getValorLiquido().equals(0.0) ?
                                ei.getMovPagamento().getValor() : ei.getValorLiquido()));
                        List<MovPagamentoVO> pagamentosItemDebito = getFacade().getMovPagamento().consultarPagamentosItemExtrato(ei);
                        for(MovPagamentoVO pag : pagamentosItemDebito){
                            pag.setMovPagamentoEscolhidaFinan(true);
                            setTotalSelecionado(Uteis.arredondarForcando2CasasDecimais(pag.getValor() + getTotalSelecionado()));
                            listaOutros.add(pag);
                        }
                    }
                }
            }else{
                if(!setarEscolhidos()){
                    throw new Exception("Selecione um ou mais recebíveis para movimentar");
                }
            }
            setOnCompleteDepositar("");
            ConfiguracaoFinanceiroVO confFinan = getFacade().getConfiguracaoFinanceiro().consultar();
            Integer codigoContaOrigem = montarLote();
            if (confFinan.getUsarMovimentacaoContas()){
                validarCaixaAbertoConta(codigoContaOrigem);
            }
            OperacaoContaControle operacaoContaControle = (OperacaoContaControle) getControlador(OperacaoContaControle.class.getSimpleName());
            TipoFormaPagto tfp = null;
            double total = 0.0;
            boolean apresentarLote = true;
            TipoOperacaoLancamento tipo = TipoOperacaoLancamento.CUSTODIA;
            TipoFormaPagto tipoFormaPagto = TipoFormaPagto.getTipoFormaPagtoSigla(getFormaPagamentoSelecionada().getTipoFormaPagamento());
            if (visaoConciliacao){
                total = getTotalSelecionado();
            }else if (tipoFormaPagto != null) {
                switch (tipoFormaPagto) {
                    case CARTAOCREDITO:
                        total = getTotalCartoes();
                        break;
                    case CHEQUE:
                        total = getTotalCheques();
                        break;
                    default:
                        total = getTotalVista();
                }
            }

            if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())) {
                tfp = TipoFormaPagto.CHEQUE;
            } else if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                tfp = TipoFormaPagto.CARTAOCREDITO;
            } else if (getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())) {
                tfp = TipoFormaPagto.BOLETOBANCARIO;
            }else {
                tfp = getExibirAutorizacao() ? TipoFormaPagto.CARTAODEBITO : TipoFormaPagto.AVISTA;
                apresentarLote = false;
                tipo = TipoOperacaoLancamento.DEPOSITO;
                lote = null;
            }

            ContaVO contaOrigem = UteisValidacao.emptyNumber(codigoContaOrigem) ? null : new ContaVO(codigoContaOrigem);
            double valorLiquido = getValorLiquido();
            double taxaAntecipacao = getTaxaAntecipacao();
            double taxaFinal = UteisValidacao.emptyNumber(total) ? 0.0 : Uteis.arredondarForcando2CasasDecimais(((total - valorLiquido) / total) * 100) - taxaAntecipacao;
            valorLiquido = Uteis.arredondarForcando2CasasDecimais(valorLiquido);

            Date dataTrabalhoSugeridaParaMovimentacaoConciliacao = null;
            if (visaoConciliacao && getDataInicialCompensacao() != null && getDataFinalCompensacao() != null) {
                boolean pesquisouSomenteUmDiaNaConciliacao = Uteis.datasMesmoDiaMesAno(getDataInicialCompensacao(), getDataFinalCompensacao());
                if (pesquisouSomenteUmDiaNaConciliacao) {
                    dataTrabalhoSugeridaParaMovimentacaoConciliacao = getDataInicialCompensacao();
                }
            }

            setOnCompleteDepositar(operacaoContaControle.abrirModal(lote, tipo, TipoES.ENTRADA, tfp, total, apresentarLote,
                    contaOrigem, false, false, recebidosZW, getFormaPagamentoSelecionada(), taxaFinal, valorLiquido , qtdeTotalVista, taxaAntecipacao, dataTrabalhoSugeridaParaMovimentacaoConciliacao));

            MovContaControle movContaControle = (MovContaControle) getControlador(MovContaControle.class.getSimpleName());
            if(movContaControle != null
                    && empresaRel != null &&
                    !UteisValidacao.emptyNumber(empresaRel.getCodigo())){
                if(movContaControle.isMostrarCampoEmpresa()){
                    movContaControle.getMovContaVO().getEmpresaVO().setCodigo(empresaRel.getCodigo());
                    movContaControle.verificarPreenchimentoCampoEmpresa();
                }
                if(visaoConciliacao && !UteisValidacao.emptyNumber(fpSelecionada)){
                    movContaControle.getMovContaRateioVO().getFormaPagamentoVO().setCodigo(fpSelecionada);
                    movContaControle.selecionarContaCustodia(false);
                }
                movContaControle.setApresentarLabelPix(false);
                movContaControle.setApresentarLabelCartao(false);
                movContaControle.setLancamentoMovimentacaoRecebiveis(true);
            }
        } catch (Exception e) {
            setErro(true);
            setMensagemDetalhada(e);
            setOnCompleteDepositar(getMensagemNotificar());
            limparMsg();
        }
    }

    private double getValorLiquido() throws Exception{
        double valorLiquido = 0.0;
        setTaxaAntecipacao(0.0);
        if(getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())){
            for (CartaoCreditoTO cartaoTO : getListaCartoes()) {
                if (cartaoTO.isCartaoEscolhido()) {
                    double taxa = getFacade().getFormaPagamentoEmpresa().descrobrirTaxaApropriada(
                            cartaoTO.getNrVezes(),
                            cartaoTO.getDataLancamento(),
                            cartaoTO.getAdquirenteCod(),
                            cartaoTO.getOperadoraCodigo(),
                            visaoConciliacao && cartaoTO.getFormaPagamentoVO() != null && !UteisValidacao.emptyNumber(cartaoTO.getFormaPagamentoVO().getCodigo()) ?
                                    cartaoTO.getFormaPagamentoVO() : formaPagamentoSelecionada,
                            empresaRel.getCodigo());
                    if (cartaoTO.isAntecipacao()) {
                        setTaxaAntecipacao(cartaoTO.getTaxaCalculadaAntecipacao());
                        valorLiquido += cartaoTO.getValor() - (((taxa + getTaxaAntecipacao()) / 100) * cartaoTO.getValor());
                    } else {
                        valorLiquido += ((100 - taxa) / 100) * cartaoTO.getValor();
                    }
                }
            }
        } else if(!getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.CHEQUE.getSigla())){
            for (MovPagamentoVO movPagamentoVO : getListaOutros()) {
                if (movPagamentoVO.getMovPagamentoEscolhidaFinan()) {
                    double taxa = getFacade().getFormaPagamentoEmpresa().descrobrirTaxaApropriada(0,
                            movPagamentoVO.getDataLancamento(),
                            movPagamentoVO.getAdquirenteVO().getCodigo(),
                            movPagamentoVO.getOperadoraCartaoVO().getCodigo(),
                            visaoConciliacao ? movPagamentoVO.getFormaPagamento() : formaPagamentoSelecionada,
                            empresaRel.getCodigo()
                    );
                    if (movPagamentoVO.isAntecipacao()) {
                        setTaxaAntecipacao(movPagamentoVO.getTaxaAntecipacao());
                        valorLiquido += movPagamentoVO.getValor() - (((taxa + movPagamentoVO.getTaxaAntecipacao()) / 100) * movPagamentoVO.getValor());
                    } else {
                        valorLiquido += ((100 - taxa) / 100) * movPagamentoVO.getValor();
                    }
                }
            }
        }

        return valorLiquido;
    }

    public void gerarLoteAvulso() {
        setarEscolhidos();
        setMsgAlert("");
        lote = new LoteVO();
        loteAvulso = new MovContaVO();
        boolean algumEscolhido = false;
        try {
            int codigoConta = 0;
            for (ChequeTO ch : listaCheques) {
                if (ch.isChequeEscolhido()) {
                    algumEscolhido = true;
                    if (ch.getCodigoContaContido() == 0) {
                        throw new Exception("Selecione somente cheques que já estão em uma conta.");
                    }
                    if (codigoConta == 0) {
                        codigoConta = ch.getCodigoContaContido();
                    } else if (codigoConta != ch.getCodigoContaContido()) {
                        throw new Exception("Lote avulso deve conter apenas cheques que estão na mesma conta.");
                    }
                }
            }
            if (!algumEscolhido) {
                throw new Exception("Selecione pelo menos um cheque.");
            }
            setTipoLista(getEscolha());
            montarLote();
            loteAvulso = MovContaVO.getMovContaParaLoteAvulso(codigoConta, lote.getValor());

            setMsgAlert("Richfaces.showModalPanel('panelLoteAvulso');");
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void salvarLoteAvulso() throws Exception {
        try {
            setMsgAlert("");
            if (UteisValidacao.emptyString(lote.getDescricao())) {
                throw new Exception("Campo DESCRIÇÃO do lote deve ser informado.");
            }
            getFacade().getFinanceiro().getMovConta().salvarLoteAvulso(loteAvulso, lote);
            setMsgAlert("alert('Lote salvo com sucesso!');Richfaces.hideModalPanel('panelLoteAvulso');");
            for (ChequeTO ch : lote.getChequesTO()) {
                ch.setChequeEscolhido(false);
                ch.setLoteAvulso(lote.getCodigo());
            }
            setQtdeTotalCheques(0);
            setTotalCheques(0.0);
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void marcaDesmarcaVista() {
        for (MovPagamentoVO mp : listaOutros) {
            if (mp.getMovconta() == null || mp.getMovconta() == 0 || exibirAutorizacao) {
                mp.setMovPagamentoEscolhidaFinan(todosPagamentosMarcados);
            }
        }
        calcularTotalVista();
    }

    public void fechar() {
        setEscolha("closed");
    }

// *****************************************************************************
    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Date getDataInicialCompensacao() {
        return dataInicialCompensacao;
    }

    public void setDataInicialCompensacao(Date dataInicialCompensacao) {
        this.dataInicialCompensacao = dataInicialCompensacao;
    }

    public Date getDataFinalCompensacao() {
        return dataFinalCompensacao;
    }

    public void setDataFinalCompensacao(Date dataFinalCompensacao) {
        this.dataFinalCompensacao = dataFinalCompensacao;
    }

    public Date getDataInicialLancamento() {
        return dataInicialLancamento;
    }

    public void setDataInicialLancamento(Date dataInicialLancamento) {
        this.dataInicialLancamento = dataInicialLancamento;
    }

    public Date getDataFinalLancamento() {
        return dataFinalLancamento;
    }

    public void setDataFinalLancamento(Date dataFinalLancamento) {
        this.dataFinalLancamento = dataFinalLancamento;
    }

    public Date getDataInicialConciliacao() {
        return dataInicialConciliacao;
    }

    public void setDataInicialConciliacao(Date dataInicialConciliacao) {
        this.dataInicialConciliacao = dataInicialConciliacao;
    }

    public Date getDataFinalConciliacao() {
        return dataFinalConciliacao;
    }

    public void setDataFinalConciliacao(Date dataFinalConciliacao) {
        this.dataFinalConciliacao = dataFinalConciliacao;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public List<ResumoFormaPagamentoTO> getListaResumo() {
        return listaResumo;
    }

    public JSONArray getArrayGrafico() {
        return arrayGrafico;
    }

    public String getGrafico() {
        try {
            return getArrayGrafico().toString();
        } catch (Exception e) {
            return "";
        }

    }

    public void setArrayGrafico(JSONArray arrayGrafico) {
        this.arrayGrafico = arrayGrafico;
    }

    public void setListaResumo(List<ResumoFormaPagamentoTO> listaResumo) {
        this.listaResumo = listaResumo;
    }

    public String getEscolha() {
        return escolha;
    }

    public void setEscolha(String escolha) {
        this.escolha = escolha;
    }

    public double getTotalCheques() {
        return totalCheques;
    }

    public void setTotalCheques(double totalCheques) {
        this.totalCheques = totalCheques;
    }

    public double getTotalCartoes() {
        return totalCartoes;
    }

    public void setTotalCartoes(double totalCartoes) {
        this.totalCartoes = totalCartoes;
    }

    public double getTotalSelecionadoCheques() {
        return totalSelecionadoCheques;
    }

    public void setTotalSelecionadoCheques(double totalSelecionadoCheques) {
        this.totalSelecionadoCheques = totalSelecionadoCheques;
    }

    public double getTotalSelecionadoCartoes() {
        return totalSelecionadoCartoes;
    }

    public void setTotalSelecionadoCartoes(double totalSelecionadoCartoes) {
        this.totalSelecionadoCartoes = totalSelecionadoCartoes;
    }

    public List<ChequeTO> getListaCheques() {
        return listaCheques;
    }

    public void setListaCheques(List<ChequeTO> listaCheques) {
        this.listaCheques = listaCheques;
    }

    public List<CartaoCreditoTO> getListaCartoes() {
        return listaCartoes;
    }

    public void setListaCartoes(List<CartaoCreditoTO> listaCartoes) {
        this.listaCartoes = listaCartoes;
    }

    public List<MovPagamentoVO> getListaOutros() {
        return listaOutros;
    }

    public void setListaOutros(List<MovPagamentoVO> listaOutros) {
        this.listaOutros = listaOutros;
    }

    public LoteVO getLote() {
        return lote;
    }

    public void setLote(LoteVO lote) {
        this.lote = lote;
    }

    public String getTipoLista() {
        return tipoLista;
    }

    public void setTipoLista(String tipoLista) {
        this.tipoLista = tipoLista;
    }

    public int getQtdeTotal() {
        return qtdeTotal;
    }

    public void setQtdeTotal(int qtdeTotal) {
        this.qtdeTotal = qtdeTotal;
    }

    public double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    public EmpresaVO getEmpresaRel() {
        return empresaRel;
    }

    public void setEmpresaRel(EmpresaVO empresaRel) {
        this.empresaRel = empresaRel;
    }

    public UsuarioVO getResponsavelEdicaoPagamento() {
        return responsavelEdicaoPagamento;
    }

    public void setResponsavelEdicaoPagamento(UsuarioVO responsavelEdicaoPagamento) {
        this.responsavelEdicaoPagamento = responsavelEdicaoPagamento;
    }

    public boolean isTodosChequesMarcados() {
        return todosChequesMarcados;
    }

    public void setTodosChequesMarcados(boolean todosChequesMarcados) {
        this.todosChequesMarcados = todosChequesMarcados;
    }

    public boolean isTodosCartoesMarcados() {
        return todosCartoesMarcados;
    }

    public void setTodosCartoesMarcados(boolean todosCartoesMarcados) {
        this.todosCartoesMarcados = todosCartoesMarcados;
    }

    public int getQtdeTotalCheques() {
        return qtdeTotalCheques;
    }

    public void setQtdeTotalCheques(int qtdeTotalCheques) {
        this.qtdeTotalCheques = qtdeTotalCheques;
    }

    public int getQtdeTotalCartoes() {
        return qtdeTotalCartoes;
    }

    public void setQtdeTotalCartoes(int qtdeTotalCartoes) {
        this.qtdeTotalCartoes = qtdeTotalCartoes;
    }

    public boolean getApresentarFiltros() {
        return !this.getFiltros().isEmpty();
    }

    /**
     * @return the filtros
     */
    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public String detalharFiltros() throws Exception {
        String filtros = ""
                + "";
        if(!UteisValidacao.emptyList(getEmpresasSelecionadas()) && !UteisValidacao.emptyList(getEmpresas())){
            int cont = 1;
            for(EmpresaVO empresa: getEmpresas()){
                if(getEmpresasSelecionadas().contains(empresa.getCodigo().toString())){
                    if(cont == 1){
                        filtros += " | Empresa: " + empresa.getNome();
                        cont++;
                    } else {
                        filtros += ", " + empresa.getNome();
                    }
                }
            }
        }
        if (!"".equals(nome)) {
            filtros += " | Nome Pagador:" + nome;
        }
        if (!"".equals(nomeTerceiro)) {
            filtros += " | Nome Terceiro:" + nomeTerceiro;
        }
        if (!"".equals(getOperadorCaixa().getNome())) {
            filtros += " | Operador de caixa:" + getOperadorCaixa().getNome();
        }
        if (pesquisarRecebiveisComLote) {
            filtros += " | Pesquisar: Com lote";
        }
        if (pesquisarRecebiveisSemLote) {
            filtros += " | Pesquisar: Sem lote";
        }
        if (!codigoLoteFiltro.trim().isEmpty()) {
            filtros += " | Lote:" + codigoLoteFiltro;
        }
        if (!visaoConciliacao && dataInicialCompensacao != null && dataFinalCompensacao != null) {
            filtros += " | Data de Compensação: " + Uteis.getDataAplicandoFormatacao(dataInicialCompensacao, "dd/MM/yyyy");
            filtros += " até " + Uteis.getDataAplicandoFormatacao(dataFinalCompensacao, "dd/MM/yyyy");
        }
        if (!visaoConciliacao && dataInicialLancamento != null && dataFinalLancamento != null) {
            filtros += " | Data de Faturamento: " + Uteis.getDataAplicandoFormatacao(dataInicialLancamento, "dd/MM/yyyy");
            filtros += " até " + Uteis.getDataAplicandoFormatacao(dataFinalLancamento, "dd/MM/yyyy");
        }
        if (visaoConciliacao && dataInicialConciliacao != null && dataFinalConciliacao != null) {
            filtros += tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()) ? " | Data de Compensação: " : " | Data de Faturamento: ";
            filtros += Uteis.getDataAplicandoFormatacao(dataInicialConciliacao, "dd/MM/yyyy");
            filtros += " até " + Uteis.getDataAplicandoFormatacao(dataFinalConciliacao, "dd/MM/yyyy");
        }
        if (!UteisValidacao.emptyString(cpf)) {
            filtros += " | " + displayIdentificadorFront[0] + ": " + cpf;
        }
        if (!UteisValidacao.emptyString(nomeClienteContrato)) {
            filtros += " | Nome Cliente: " + nomeClienteContrato;
        }
        if (!UteisValidacao.emptyString(matricula)) {
            filtros += " | Matrícula: " + matricula;
        }
        if (!UteisValidacao.emptyString(chequeVO.getBanco().getNome())) {
            filtros += " | Banco: " + chequeVO.getBanco().getNome();
        }
        if (!UteisValidacao.emptyString(chequeVO.getAgencia())) {
            filtros += " | Agência: " + chequeVO.getAgencia();
        }
        if (!UteisValidacao.emptyString(chequeVO.getConta())) {
            filtros += " | Conta: " + chequeVO.getConta();
        }
        if (!UteisValidacao.emptyString(chequeVO.getNumero())) {
            filtros += " | Nº Cheque: " + chequeVO.getNumero();
        }
        if (!UteisValidacao.emptyNumber(operadoraCartao)) {
            filtros += " | Operadora de cartão: " + Uteis.retornaLabelObjetoSelecionado(getOperadorasCartao(), operadoraCartao);
        }
        if (!UteisValidacao.emptyNumber(adquirente)) {
            //adquirente 9999 é o filtro 'SEM ADQUIRENTE'
            if (adquirente == 9999){
                filtros += " | Adquirentes: Sem adquirente";
            }else {
                filtros += " | Adquirentes: " + Uteis.retornaLabelObjetoSelecionado(getAdquirentes(), adquirente);
            }
        }
        if (!UteisValidacao.emptyString(codAutorizacao)) {
            filtros += " | Código Autorização: " + codAutorizacao;
        }else if (!UteisValidacao.emptyString(codAutorizacaoedi)) {
            filtros += " | Código Autorização: " + codAutorizacaoedi;
        }
        if (!UteisValidacao.emptyString(nsu)) {
            filtros += " | NSU: " + nsu;
        }
        if (chequeAprazo) {
            filtros += " | Cheques a prazo ";
        }
        if (chequeAvista) {
            filtros += " | Cheques a vista ";
        }
        if (considerarDataCompensacaoOriginal) {
            filtros += " | Considerar data original de compensação";
        }
        if (!visaoConciliacao && getStatusMovimentacao() != null && !(getStatusMovimentacao().equals(StatusMovimentacaoEnum.TODOS) && antecipados)) {
            filtros += " | " + getStatusMovimentacao().getLabel();
        }
        if (recebidosZW) {
            filtros += " | Movimentação financeira é Sim ";
        }

        if (mostrarCancelados) {
            filtros += " | Mostrar cancelados/devolvidos";
        }

        if (antecipados) {
            filtros += " | Recebíveis antecipados";
        }

        if (centroCustoTO.getCodigo() > 0) {
            for(SelectItem si : getCentrosCusto()){
                if(centroCustoTO.getCodigo().equals(si.getValue())){
                    filtros += " | Centro de custos: "+si.getLabel();
                }
            }
        }
        if(visaoConciliacao){
            if(!UteisValidacao.emptyNumber(tipoConciliacao)){
                for(SelectItem si : tiposConcilacao){
                    if(tipoConciliacao.equals(si.getValue())){
                        filtros += " | Tipo de conciliação: "+si.getLabel();
                    }
                }
            }
            if(!UteisValidacao.emptyNumber(fpSelecionada)){
                for(SelectItem si : formasPagamentoSI){
                    if(fpSelecionada.equals(si.getValue())){
                        filtros += " | Forma de pagamento: "+si.getLabel();
                    }
                }
            }
            if(!UteisValidacao.emptyNumber(codigoConvenio)){
                for(SelectItem si : tiposConvenio){
                    if(codigoConvenio.equals(si.getValue())){
                        filtros += " | Convênio de cobrança: "+si.getLabel();
                    }
                }
            }
        }
        return filtros.replaceFirst("[|]", "");
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (empresaRel.getCodigo().intValue() != 0) {
            setEmpresaRel(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaRel().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            setEmpresaRel(new EmpresaVO());
        }
    }

    public List<SelectItem> getListaSelectItemBanco() throws Exception {
        List resultadoConsulta = getFacade().getBanco().consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            BancoVO obj = (BancoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        return objs;
    }

    public void consultarBancoPorCodigo() {
        try {
            if (getChequeVO().getBanco().getCodigo() != null && getChequeVO().getBanco().getCodigo().intValue() != 0) {
                BancoVO obj = getFacade().getBanco().consultarCodigo(getChequeVO().getBanco().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
                if (obj != null && obj.getCodigo().intValue() != 0) {
                    getChequeVO().setBanco(new BancoVO());
                    getChequeVO().setBanco(obj);
                    //setBanco(obj.getCodigoBanco().toString());
                    setMensagemDetalhada("");
                    setMensagem("");
                    setMensagemID("");
                } else {
                    setMensagemDetalhada("Banco não encontrado.");
                }
            } else {
                setMensagemDetalhada("Banco não encontrado.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * @return the parametro1
     */
    public boolean isParametro1() {
        return parametro1;
    }

    /**
     * @param parametro1 the parametro1 to set
     */
    public void setParametro1(boolean parametro1) {
        this.parametro1 = parametro1;
    }

    /**
     * @return the parametro2
     */
    public boolean isParametro2() {
        return parametro2;
    }

    /**
     * @param parametro2 the parametro2 to set
     */
    public void setParametro2(boolean parametro2) {
        this.parametro2 = parametro2;
    }

    /**
     * @return the parametro3
     */
    public boolean isParametro3() {
        return parametro3;
    }

    /**
     * @param parametro3 the parametro3 to set
     */
    public void setParametro3(boolean parametro3) {
        this.parametro3 = parametro3;
    }

    /**
     * @return the parametro4
     */
    public boolean isParametro4() {
        return parametro4;
    }

    /**
     * @param parametro4 the parametro4 to set
     */
    public void setParametro4(boolean parametro4) {
        this.parametro4 = parametro4;
    }

    /**
     * @return the chequeVO
     */
    public ChequeVO getChequeVO() {
        return chequeVO;
    }

    /**
     * @param chequeVO the chequeVO to set
     */
    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    /**
     * @return the cpf
     */
    public String getCpf() {
        return cpf;
    }

    /**
     * @param cpf the cpf to set
     */
    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    /**
     * @return the nomeClienteContrato
     */
    public String getNomeClienteContrato() {
        return nomeClienteContrato;
    }

    /**
     * @param nomeClienteContrato the nomeClienteContrato to set
     */
    public void setNomeClienteContrato(String nomeClienteContrato) {
        this.nomeClienteContrato = nomeClienteContrato;
    }

    public List<SelectItem> getListaPeriodos() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem("HJ", "Hoje"));
        objs.add(new SelectItem("ON", "Ontem"));
        objs.add(new SelectItem("SE", "Esta Semana"));
        objs.add(new SelectItem("ME", "Mês"));
        objs.add(new SelectItem("SEP", "Semana Passada"));
        objs.add(new SelectItem("MEP", "Mês Passado"));
        objs.add(new SelectItem("PME", "Próximo Mês"));
        if (visaoConciliacao) {
            objs.add(new SelectItem("FI", "Informar período"));
        }
        return objs;
    }

    public void consultarCompensadosHoje() throws Exception {
        limparFiltros();
        periodoLancamento = "";
        periodoCompensacao = "HJ";
        alterarPeriodoLancamento();
        alterarPeriodoCompensacao();
        consultarRecebiveis();
    }

    public void alterarPeriodoCompensacao() throws Exception {
        setDataInicialCompensacao(null);
        setDataFinalCompensacao(null);
        if (periodoCompensacao.equals("HJ")) {
            dataInicialCompensacao = Calendario.hoje();
            dataFinalCompensacao = Calendario.hoje();
        } else if (periodoCompensacao.equals("ON")) {
            dataInicialCompensacao = Uteis.obterDataAnterior(Calendario.hoje(), 1);
            dataFinalCompensacao = Uteis.obterDataAnterior(Calendario.hoje(), 1);
        } else if (periodoCompensacao.equals("SE")) {
            dataInicialCompensacao = Uteis.obterPrimeiroEUltimoDiaSemana(true);
            dataFinalCompensacao = Uteis.obterPrimeiroEUltimoDiaSemana(false);
        } else if (periodoCompensacao.equals("ME")) {
            dataInicialCompensacao = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataFinalCompensacao = Uteis.obterUltimoDiaMes(Calendario.hoje());
        } else if (periodoCompensacao.equals("SEP")) {
            dataInicialCompensacao = Uteis.obterPrimeiroEUltimoDiaSemana(true, Uteis.obterDataAnterior(Calendario.hoje(), 7));
            dataFinalCompensacao = Uteis.obterPrimeiroEUltimoDiaSemana(false, Uteis.obterDataAnterior(Calendario.hoje(), 7));
        } else if (periodoCompensacao.equals("MEP")) {
            dataInicialCompensacao = Uteis.obterPrimeiroDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
            dataFinalCompensacao = Uteis.obterUltimoDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
        } else if (periodoCompensacao.equals("PME")) {
            Date mesAnterior = Uteis.obterDataAnterior(-1);
            dataInicialCompensacao = Uteis.obterPrimeiroDiaMes(Uteis.obterPrimeiroDiaMes(mesAnterior));
            dataFinalCompensacao = Uteis.obterUltimoDiaMes(Uteis.obterUltimoDiaMes(mesAnterior));
        }
    }

    public void consultarFaturadosOntem() throws Exception {
        periodoLancamento = "ON";
        periodoCompensacao = "";
        alterarPeriodoLancamento();
        alterarPeriodoCompensacao();
        consultarRecebiveis();
    }

    public void alterarPeriodoLancamento() throws Exception {
        if (periodoLancamento.equals("HJ")) {
            dataInicialLancamento = Calendario.hoje();
            dataFinalLancamento = Calendario.hoje();
        } else if (periodoLancamento.equals("ON")) {
            dataInicialLancamento = Uteis.obterDataAnterior(Calendario.hoje(), 1);
            dataFinalLancamento = Uteis.obterDataAnterior(Calendario.hoje(), 1);
        } else if (periodoLancamento.equals("SE")) {
            dataInicialLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(true);
            dataFinalLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(false);
        } else if (periodoLancamento.equals("ME")) {
            dataInicialLancamento = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataFinalLancamento = Uteis.obterUltimoDiaMes(Calendario.hoje());
        } else if (periodoLancamento.equals("SEP")) {
            dataInicialLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(true, Uteis.obterDataAnterior(Calendario.hoje(), 7));
            dataFinalLancamento = Uteis.obterPrimeiroEUltimoDiaSemana(false, Uteis.obterDataAnterior(Calendario.hoje(), 7));
        } else if (periodoLancamento.equals("MEP")) {
            dataInicialLancamento = Uteis.obterPrimeiroDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
            dataFinalLancamento = Uteis.obterUltimoDiaMes(Uteis.obterDataAnterior(Calendario.hoje(), 30));
        } else if (periodoLancamento.equals("PME")) {
            Date mesAnterior = Uteis.obterDataAnterior(-1);
            dataInicialLancamento = Uteis.obterPrimeiroDiaMes(Uteis.obterPrimeiroDiaMes(mesAnterior));
            dataFinalLancamento = Uteis.obterUltimoDiaMes(Uteis.obterUltimoDiaMes(mesAnterior));
        }
    }

    /**
     * @return the periodoCompensacao
     */
    public String getPeriodoCompensacao() {
        return periodoCompensacao;
    }

    /**
     * @param periodoCompensacao the periodoCompensacao to set
     */
    public void setPeriodoCompensacao(String periodoCompensacao) {
        this.periodoCompensacao = periodoCompensacao;
    }

    /**
     * @return the periodoLancamento
     */
    public String getPeriodoLancamento() {
        return periodoLancamento;
    }

    /**
     * @param periodoLancamento the periodoLancamento to set
     */
    public void setPeriodoLancamento(String periodoLancamento) {
        this.periodoLancamento = periodoLancamento;
    }

    /**
     * @return the listaConsultaRecebiveis
     */
    public List<MovPagamentoVO> getListaConsultaRecebiveis() {
        return listaConsultaRecebiveis;
    }

    /**
     * @param listaConsultaRecebiveis the listaConsultaRecebiveis to set
     */
    public void setListaConsultaRecebiveis(List<MovPagamentoVO> listaConsultaRecebiveis) {
        this.listaConsultaRecebiveis = listaConsultaRecebiveis;
    }

    /**
     * @return the pesquisarRecebiveis
     */
    public Boolean getPesquisarRecebiveis() {
        return pesquisarRecebiveis;
    }

    /**
     * @param pesquisarRecebiveis the pesquisarRecebiveis to set
     */
    public void setPesquisarRecebiveis(Boolean pesquisarRecebiveis) {
        this.pesquisarRecebiveis = pesquisarRecebiveis;
    }

    /**
     * @return the pesquisarRecebiveisSemLote
     */
    public Boolean getPesquisarRecebiveisSemLote() {
        return pesquisarRecebiveisSemLote;
    }

    /**
     * @param pesquisarRecebiveisSemLote the pesquisarRecebiveisSemLote to set
     */
    public void setPesquisarRecebiveisSemLote(Boolean pesquisarRecebiveisSemLote) {
        this.pesquisarRecebiveisSemLote = pesquisarRecebiveisSemLote;
    }

    /**
     * @return the pesquisarRecebiveisComLote
     */
    public Boolean getPesquisarRecebiveisComLote() {
        return pesquisarRecebiveisComLote;
    }

    /**
     * @param pesquisarRecebiveisComLote the pesquisarRecebiveisComLote to set
     */
    public void setPesquisarRecebiveisComLote(Boolean pesquisarRecebiveisComLote) {
        this.pesquisarRecebiveisComLote = pesquisarRecebiveisComLote;
    }

    public void setOnCompleteDepositar(String onCompleteDepositar) {
        this.onCompleteDepositar = onCompleteDepositar;
    }

    public String getOnCompleteDepositar() {
        return onCompleteDepositar;
    }

    public void setTotalVista(double totalVista) {
        this.totalVista = totalVista;
    }

    public double getTotalVista() {
        return totalVista;
    }

    public void setQtdeTotalVista(int qtdeTotalVista) {
        this.qtdeTotalVista = qtdeTotalVista;
    }

    public int getQtdeTotalVista() {
        return qtdeTotalVista;
    }

    public void setTodosPagamentosMarcados(boolean todosPagamentosMarcados) {
        this.todosPagamentosMarcados = todosPagamentosMarcados;
    }

    public boolean getTodosPagamentosMarcados() {
        return todosPagamentosMarcados;
    }

    public void setExibirAutorizacao(Boolean exibirAutorizacao) {
        this.exibirAutorizacao = exibirAutorizacao;
    }

    public Boolean getExibirAutorizacao() {
        return exibirAutorizacao;
    }

    public Boolean getExibirNSU() {
        return exibirNSU;
    }

    public void setExibirNSU(Boolean exibirNSU) {
        this.exibirNSU = exibirNSU;
    }

    public void setContaCorrente(boolean contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public boolean getContaCorrente() {
        return contaCorrente;
    }

    public void setOperadorCaixa(UsuarioVO operadorCaixa) {
        this.operadorCaixa = operadorCaixa;
    }

    public UsuarioVO getOperadorCaixa() {
        return operadorCaixa;
    }

    public void desmarcarTudo() {
        try {
            todosPagamentosMarcados = false;
            todosCartoesMarcados = false;
            todosChequesMarcados = false;
            for (MovPagamentoVO movpag : listaOutros) {
                movpag.setMovPagamentoEscolhidaFinan(false);
            }
        }
        catch (Exception e) {
        }
    }

    public void setLoteAvulso(MovContaVO loteAvulso) {
        this.loteAvulso = loteAvulso;
    }

    public MovContaVO getLoteAvulso() {
        return loteAvulso;
    }

    public void setDataNovaCompensacaoCartoes(Date dataNovaCompensacaoCartoes) {
        this.dataNovaCompensacaoCartoes = dataNovaCompensacaoCartoes;
    }

    public Date getDataNovaCompensacaoCartoes() {
        return dataNovaCompensacaoCartoes;
    }

    public void setAlterarCredito(boolean alterarCredito) {
        this.alterarCredito = alterarCredito;
    }

    public boolean isAlterarCredito() {
        return alterarCredito;
    }

    public void setImprimirDiario(boolean imprimirDiario) {
        this.imprimirDiario = imprimirDiario;
    }

    public boolean getImprimirDiario() {
        return imprimirDiario;
    }

    public void setItemImpressao(RelatorioGestaoRecebiveisTO itemImpressao) {
        this.itemImpressao = itemImpressao;
    }

    public RelatorioGestaoRecebiveisTO getItemImpressao() {
        return itemImpressao;
    }

    public void setAgruparPor(int agruparPor) {
        this.agruparPor = agruparPor;
    }

    public int getAgruparPor() {
        return agruparPor;
    }

    /**
     * @return the nomeTerceiro
     */
    public String getNomeTerceiro() {
        return nomeTerceiro;
    }

    /**
     * @param nomeTerceiro the nomeTerceiro to set
     */
    public void setNomeTerceiro(String nomeTerceiro) {
        this.nomeTerceiro = nomeTerceiro;
    }

    public double getTotalEspecie() {
        return totalEspecie;
    }

    public String getTotalEspecieApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getTotalEspecie());
    }

    private String formatarValorTentandoLocale(double totalEspecie) {
        try {
            return Formatador.formatarValorMonetario(totalEspecie, getEmpresaLogado().getLocale());
        } catch (Exception ignored) {
            return Formatador.formatarValorMonetario(totalEspecie);
        }
    }

    public void setTotalEspecie(double totalEspecie) {
        this.totalEspecie = totalEspecie;
    }
    
    public double getTotalBoleto() {
        return totalBoleto;
    }

    public String getTotalBoletoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getTotalBoleto());
    }

    public void setTotalBoleto(double totalBoleto) {
        this.totalBoleto = totalBoleto;
    }

    public double getTotalNaoFisicamente() {
        return totalNaoFisicamente;
    }

    public String getTotalNaoFisicamenteApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getTotalNaoFisicamente());
    }

    public String getTotalDevolucoesApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getTotalDevolucoes() * -1);
    }

    public void setTotalNaoFisicamente(double totalNaoFisicamente) {
        this.totalNaoFisicamente = totalNaoFisicamente;
    }

    public String getTotalTotalApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getTotalTotal());
    }

    public Double getTotalTotal() {
        return (getTotalEspecie() + getTotalBoleto() - getTotalDevolucoes());
    }

    public List<Integer> getChequeComposicao() {
        return chequeComposicao;
    }

    public void setChequeComposicao(List<Integer> chequeComposicao) {
        this.chequeComposicao = chequeComposicao;
    }

    /**
     * @param cartaoComposicao the cartaoComposicao to set
     */
    public void setCartaoComposicao(List<Integer> cartaoComposicao) {
        this.cartaoComposicao = cartaoComposicao;
    }

    /**
     * @return the cartaoComposicao
     */
    public List<Integer> getCartaoComposicao() {
        return cartaoComposicao;
    }

    public void setRecebidosZW(Boolean recebidosZW) {
        this.recebidosZW = recebidosZW;
    }

    public Boolean getRecebidosZW() {
        return recebidosZW;
    }

    public void setCodAutorizacao(String codAutorizacao) {
        this.codAutorizacao = codAutorizacao;
    }

    public String getCodAutorizacao() {
        return codAutorizacao;
    }

    public void setOperadoraCartao(Integer operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public Integer getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setAdquirente(Integer adquirente) {
        this.adquirente = adquirente;
    }

    public Integer getAdquirente() {
        return adquirente;
    }

    public List<SelectItem> getOperadorasCartao() throws Exception {
        if (UteisValidacao.emptyList(operadorasCartao)) {
            operadorasCartao = new ArrayList<SelectItem>();
            List<OperadoraCartaoVO> consultarPorDescricao = getFacade().getOperadoraCartao().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (OperadoraCartaoVO op : consultarPorDescricao) {
                operadorasCartao.add(new SelectItem(op.getCodigo(), op.getDescricao()));
            }
        }
        return operadorasCartao;
    }
    public List<SelectItem> getAdquirentes() throws Exception {
        if (UteisValidacao.emptyList(adquirentes)) {
            adquirentes = new ArrayList<SelectItem>();
            //Exibia apenas ativos. Cliente pediu para exibir tudo. Se alguém reclamar, fazer reunião do time para definir a regra que deve prevalecer.
            List<AdquirenteVO> consultarPorAdquirente = getFacade().getAdquirente().consultarTodos(false);
            for (AdquirenteVO ad : consultarPorAdquirente) {
                adquirentes.add(new SelectItem(ad.getCodigo(), ad.getNome()));
            }
        }
        return adquirentes;
    }

    public List<SelectItem> getCentrosCusto() throws Exception {
        if (UteisValidacao.emptyList(centroCustos)) {
            centroCustos = new ArrayList<SelectItem>();
            List<CentroCustoTO> listaCentroCusto = getFacade().getCentroCusto().consultarTodos();
            for (CentroCustoTO centroCusto : listaCentroCusto) {
                centroCustos.add(new SelectItem(centroCusto.getCodigo(), centroCusto.getDescricao()));
            }
        }
        return centroCustos;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getMatricula() {
        return matricula;
    }

    public Boolean getUsarMovimentacao() {
        try {
            ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class.getSimpleName());
            return cfg.getConfFinanceiro().getUsarMovimentacaoContas();
        } catch (Exception e) {
            return true;
        }
    }

    public void setNomeConta(String nomeConta) {
        this.nomeConta = nomeConta;
    }

    public String getNomeConta() {
        return nomeConta;
    }

    public String getHoraInicialCompensacao() {
        return horaInicialCompensacao;
    }

    public void setHoraInicialCompensacao(String horaInicialCompensacao) {
        this.horaInicialCompensacao = horaInicialCompensacao;
    }

    public String getHoraFinalCompensacao() {
        return horaFinalCompensacao;
    }

    public void setHoraFinalCompensacao(String horaFinalCompensacao) {
        this.horaFinalCompensacao = horaFinalCompensacao;
    }

    public String getHoraInicialLancamento() {
        return horaInicialLancamento;
    }

    public void setHoraInicialLancamento(String horaInicialLancamento) {
        this.horaInicialLancamento = horaInicialLancamento;
    }

    public String getHoraFinalLancamento() {
        return horaFinalLancamento;
    }

    public void setHoraFinalLancamento(String horaFinalLancamento) {
        this.horaFinalLancamento = horaFinalLancamento;
    }

    public List<MovContaVO> getListaDevolucoes() {
        return listaDevolucoes;
    }

    public void setListaDevolucoes(List<MovContaVO> listaDevolucoes) {
        this.listaDevolucoes = listaDevolucoes;
    }

    public double getTotalDevolucoes() {
        return totalDevolucoes;
    }

    public void setTotalDevolucoes(double totalDevolucoes) {
        this.totalDevolucoes = totalDevolucoes;
    }

    public boolean isChequeAvista() {
        return chequeAvista;
    }

    public void setChequeAvista(boolean chequeAvista) {
        this.chequeAvista = chequeAvista;
    }

    public boolean isChequeAprazo() {
        return chequeAprazo;
    }

    public void setChequeAprazo(boolean chequeAprazo) {
        this.chequeAprazo = chequeAprazo;
    }

    public boolean isMostrarCancelados() {
        return mostrarCancelados;
    }

    public void setMostrarCancelados(boolean mostrarCancelados) {
        this.mostrarCancelados = mostrarCancelados;
    }

    public boolean isConsiderarDataCompensacaoOriginal() {
        return considerarDataCompensacaoOriginal;
    }

    public void setConsiderarDataCompensacaoOriginal(boolean considerarDataCompensacaoOriginal) {
        this.considerarDataCompensacaoOriginal = considerarDataCompensacaoOriginal;
    }

    public List<SelectItem> getListaStatusItens() {
        if (listaStatusItens == null || listaStatusItens.isEmpty()) {
            listaStatusItens = JSFUtilities.getSelectItemListFromEnum(StatusMovimentacaoEnum.class, "label", false);
        }
        return listaStatusItens;
    }

    public void setListaStatusItens(List<SelectItem> listaStatusItens) {
        this.listaStatusItens = listaStatusItens;
    }

    public StatusMovimentacaoEnum getStatusMovimentacao() {
        return statusMovimentacao;
    }

    public void setStatusMovimentacao(StatusMovimentacaoEnum statusMovimentacao) {
        this.statusMovimentacao = statusMovimentacao;
    }

    public FormaPagamentoVO getFormaPagamentoSelecionada() {
        return formaPagamentoSelecionada;
    }

    public void setFormaPagamentoSelecionada(FormaPagamentoVO formaPagamentoSelecionada) {
        this.formaPagamentoSelecionada = formaPagamentoSelecionada;
    }

    public List<HistoricoCartaoVO> getListaHistoricoCartao() {
        return listaHistoricoCartao;
    }

    public void setListaHistoricoCartao(List<HistoricoCartaoVO> listaHistoricoCartao) {
        this.listaHistoricoCartao = listaHistoricoCartao;
    }

    public void selecionarCartaoDebitoHistorico(ActionEvent evt) throws Exception {
        MovPagamentoVO movPag;
        try {
            listaHistoricoCartao = new ArrayList<>();
            movPag = (MovPagamentoVO) evt.getComponent().getAttributes().get("movp");
        } catch (Exception e) {
            Uteis.logar(e, GestaoRecebiveisControle.class);
            return;
        }
        if (movPag == null)
            return;

        historicoCartao = new HistoricoCartaoVO();
        historicoCartao.setDataInicio(movPag.getDataPagamento());
        historicoCartao.setCredito(movPag.getCredito());
        listaHistoricoCartao = getFacade().getFinanceiro().getMovConta().consultarHistoricoCartaoDebito(movPag);
    }

    public void selecionarCartaoCreditoHistorico(ActionEvent evt) throws Exception {
        CartaoCreditoTO cartaoTO;
        try {
            listaHistoricoCartao = new ArrayList<>();
            cartaoTO = (CartaoCreditoTO) evt.getComponent().getAttributes().get("cart");
        } catch (Exception e) {
            Uteis.logar(e, GestaoRecebiveisControle.class);
            return;
        }
        if (cartaoTO == null)
            return;

        historicoCartao = new HistoricoCartaoVO();
        historicoCartao.setDataInicio(cartaoTO.getDataCompensacao());
        historicoCartao.setDataFim(cartaoTO.getDataOriginal());
        listaHistoricoCartao = getFacade().getFinanceiro().getHistoricoCartao().consultarPorCartaoComposicao(cartaoTO.getObterTodosCartoesComposicao());
    }

    public void visualizarOperacaoCartaoCredito() {
        try {
            setMensagemID("msg_dados_editar");
            HistoricoCartaoVO histCart = (HistoricoCartaoVO) context().getExternalContext().getRequestMap().get("historico");
            MovContaVO movContaVo = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(histCart.getMovConta().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            MovContaControle movContaControle = (MovContaControle) JSFUtilities.getManagedBean(MovContaControle.class.getSimpleName());
            movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
            movContaVo.setMovContaRateios(getFacade().getFinanceiro().getMovContaRateio().consultarPorMovConta(movContaVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            movContaControle.setarMovConta(movContaVo, true, true);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Erro ao visualizar Lançamento do Financeiro. Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage());
        }
    }

    public String getNameAtributoRecebiveisOutros() {
        return "atributos";
    }

    public String getValueAtributoRecebiveisOutros() throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append("empresa.nome=Empresa,matriculaPagador=Matrícula,cpfPagador=" + displayIdentificadorFront[0] + " ,nomePagador=Nome Pagador,nomeAlunosDaParcela=Nome do(s) Aluno(s),dataLancamento=Dt.Lançamento,dataPagamento=Dt.Compensação,");
        if (getExibirAutorizacao()) {
            sb.append("documentoIntegracaoSesi=Documento,");
            sb.append("autorizacaoCartao=Autorização,");
        }
        if (getExibirNSU()) {
            sb.append("nsu=NSU,");
        }
        sb.append("nomeOperadorCartaoApresentar=Op.Cartão,valorTotalApresentar=Valor,contaFinanceiro=Conta,dataMovimento_Apresentar=Data Movimentação,usuarioResponsavelApresentar=Resp. Recebimento,reciboPagamentoApresentar=Recibo,contratoReciboApresentar=Contrato");
        return sb.toString();

    }

    public void montarComboDatasMovimentar(Date inicio, Date fim) throws Exception{
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, fim);
        diasMovimentar = new ArrayList<SelectItem>();
        Collections.sort(diasEntreDatas);
        for(Date dia : diasEntreDatas){
            diasMovimentar.add(new SelectItem(dia, Uteis.getData(dia)));
        }
    }


    public String getCodigoLoteFiltro() {
        return codigoLoteFiltro;
    }

    public Integer getNrPaginaRetirarLote() {
        return nrPaginaRetirarLote;
    }

    public void setNrPaginaRetirarLote(Integer nrPaginaRetirarLote) {
        this.nrPaginaRetirarLote = nrPaginaRetirarLote;
    }

    public void setCodigoLoteFiltro(String codigoLoteFiltro) {
        this.codigoLoteFiltro = codigoLoteFiltro == null ? "" : codigoLoteFiltro;
    }

    public HistoricoCartaoVO getHistoricoCartao() {
        return historicoCartao;
    }

    public void setHistoricoCartao(HistoricoCartaoVO historicoCartao) {
        this.historicoCartao = historicoCartao;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public List<ExtratoDiarioItemVO> getExtratoDiario() {
        return extratoDiario;
    }

    public void setExtratoDiario(List<ExtratoDiarioItemVO> extratoDiario) {
        this.extratoDiario = extratoDiario;
    }

    public boolean isOk() {
        return ok;
    }

    public void setOk(boolean ok) {
        this.ok = ok;
    }

    public boolean isPendencia() {
        return pendencia;
    }

    public void setPendencia(boolean pendencia) {
        this.pendencia = pendencia;
    }

    public boolean isNao_encontrado() {
        return nao_encontrado;
    }

    public void setNao_encontrado(boolean nao_encontrado) {
        this.nao_encontrado = nao_encontrado;
    }

    public boolean isEstornado() {
        return estornado;
    }

    public void setEstornado(boolean estornado) {
        this.estornado = estornado;
    }


    private void filtrarLista() throws Exception {
        totalizadorConciliacaoDTO = new TotalizadorConciliacaoDTO();
        okValor = 0.0;
        nao_encontradoValor = 0.0;
        extratoDiario = new ArrayList<>();
        extratoDiarioCancelamentos = new ArrayList<>();
        pendenciaValor = 0.0;
        Set<Integer> compProcessada = new HashSet<>();
        for (ExtratoDiarioItemVO extratoItem : extratoDiarioBack) {
            try {
            if ((extratoItem.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) ||
                    extratoItem.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT) ||
                    extratoItem.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5))
                    && (extratoItem.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE))) {
                if (Calendario.maiorOuIgual(extratoItem.getDataPrevistaPagamento(),
                        Calendario.somarDias(extratoItem.getDataLancamento(), 3))) {
                    extratoItem.setCredito(true);
                }
            }
            if (tratarComposicao(compProcessada, extratoItem)) continue;
            if(estornadoOperadora && extratoItem.getValorBruto() < 0.0 && !extratoItem.getSituacao().equals(SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE)){
                int tipoConciliacaoEnum = (!UteisValidacao.emptyNumber(extratoItem.getTipoConciliacao())
                        && extratoItem.getTipoConciliacao() != TipoConciliacaoEnum.CANCELAMENTO.getCodigo()) ? extratoItem.getTipoConciliacao() : TipoConciliacaoEnum.CANCELAMENTO.getCodigo();
                extratoItem.setTipoConciliacao(tipoConciliacaoEnum);
                extratoItem.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA);
            }
            if ((ok && SituacaoItemExtratoEnum.OK.equals(extratoItem.getSituacao()))
                    || (pendencia && SituacaoItemExtratoEnum.PENDENCIAS.equals(extratoItem.getSituacao()))
                    || (nao_encontrado && SituacaoItemExtratoEnum.AUTORIZACAO_NAO_EXISTE.equals(extratoItem.getSituacao()))
                    || (estornadoOperadora && SituacaoItemExtratoEnum.ESTORNADO_OPERADORA.equals(extratoItem.getSituacao()))
                    || (estornado && SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.equals(extratoItem.getSituacao()))) {

                if(estornadoOperadora && extratoItem.getValorBruto() < 0.0){
                    getExtratoDiarioCancelamentos().add(extratoItem);
                } else {
                    getExtratoDiario().add(extratoItem);
                }
                totalizadorConciliacaoDTO.totalizar(extratoItem);
            }
            } catch (Exception e) {
            }
        }
        extratoDiario = Ordenacao.ordenarLista(extratoDiario, "nomePagador");
        extratoDiarioCancelamentos = Ordenacao.ordenarLista(extratoDiarioCancelamentos, "nomePagador");

    }

    private boolean tratarComposicao(Set<Integer> compProcessada, ExtratoDiarioItemVO extratoItem) {
        if (extratoItem.getComposicao() != 0 && extratoItem.getCartoes() != null && !extratoItem.getCartoes().isEmpty()) {
            for (CartaoCreditoTO cardComp : extratoItem.getCartoes()) {
                if (!compProcessada.contains(cardComp.getCodigo())) {
                    compProcessada.add(extratoItem.getComposicao());
                }
            }

            for (CartaoCreditoTO cardComp2 : extratoItem.getCartoes()) {
                if (compProcessada.contains(cardComp2.getCodigo())) {
                    return true;
                }
            }
        }
        return false;
    }

    public void toggleOk() {
        try {
            ok = !ok;
            filtrarLista();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }    
    }

    public void togglePendencias() {
        try{
            pendencia = !pendencia;
            filtrarLista();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void toggleNE() {
        try{
            nao_encontrado = !nao_encontrado;
            filtrarLista();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }
    
    public void toggleEstornado() {
        try {
            estornado = !estornado;
            filtrarLista();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void toggleEstornadoOperadora() {
        try {
            estornadoOperadora = !estornadoOperadora;
            filtrarLista();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }
    }

    public void novoCartaoAvulso() {
        RecebivelAvulsoControle recebivelAvulsoControle = (RecebivelAvulsoControle) JSFUtilities.getManagedBeanValue(RecebivelAvulsoControle.class);
        if (recebivelAvulsoControle == null) {
            recebivelAvulsoControle = new RecebivelAvulsoControle();
            JSFUtilities.setManagedBeanValue(RecebivelAvulsoControle.class.getSimpleName(), recebivelAvulsoControle);
        }
        recebivelAvulsoControle.novo();
        recebivelAvulsoControle.setTipo(2);
        ExtratoDiarioItemVO item = (ExtratoDiarioItemVO) context().getExternalContext().getRequestMap().get("item");
        recebivelAvulsoControle.getRecebivelAvulso().setLancamento(item.getDataLancamento());
        recebivelAvulsoControle.getRecebivelAvulso().setFaturamento(item.getDataLancamento());
        recebivelAvulsoControle.getRecebivelAvulso().setCompensacao(item.getDataPrevistaPagamento());
        recebivelAvulsoControle.getRecebivelAvulso().setValor(item.getValorBruto());
        recebivelAvulsoControle.getRecebivelAvulso().setCodAutorizacao(item.getAutorizacao());
        recebivelAvulsoControle.getRecebivelAvulso().getCartaoCredito().setNrParcela(item.getNrParcela());
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/recebivelAvulsoForm.jsp', 'RecebivelAvulso', 785, 595);");

    }
    
    public void reprocessarItemConciliacao() {
        try {
            limparMsg();
            ExtratoDiarioItemVO item = (ExtratoDiarioItemVO) context().getExternalContext().getRequestMap().get("item");
            item = getFacade().getExtratoDiarioItem().consultarPorChavePrimaria(item.getCodigo());
            if(!UteisValidacao.emptyNumber(item.getCodigo())){
                if (!UteisValidacao.emptyNumber(item.getConvenio().getCodigo())) {
                    item.setConvenio(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(item.getConvenio().getCodigo(), item.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    item.setTipoConvenioCobrancaEnum(item.getConvenio().getTipo());
                }
                List<ExtratoDiarioItemVO> lista = new ArrayList<ExtratoDiarioItemVO>();
                lista.add(item);
                getFacade().getExtratoDiarioItem().processarListaExtratoDiario(lista, true, item.getConvenio());
                if(!UteisValidacao.emptyNumber(lista.get(0).getCodigoMovPagamento()) ||!UteisValidacao.emptyNumber(lista.get(0).getCodigoMovConta())){
                    consultarConciliacao();
                    montarSucessoGrowl("Item reprocessado com sucesso!");
                } else {
                    montarAviso("Item reprocessado, mas não foi encontrado pagamento/conta para conciliação");
                }
            } else {
                throw new Exception("Item não encontrato para reprocessamento");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e.getMessage());
        }

    }

    public void alterarPagamento() {
        try {
            limparMsg();
            getFacade().getExtratoDiarioItem().alterarDatasPagamento(itemSelecionado);
            if(getIntegraProtheus()){
                getFacade().getReciboPagamento().inserirIntegracaoProtheus(itemSelecionado.getReciboPagamentoVO().getCodigo(), "A");
            }
            consultarConciliacao();
            setSucesso(true);
            setMensagemDetalhada("O lançamento foi alterado com sucesso!");
            setMsgAlert(getMensagemNotificar()+"Richfaces.hideModalPanel('modalPendenciaItemExtrato');");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void estornarRecibo() {
        try {
            limparMsg();
            if(getIntegraProtheus()){
                getFacade().getReciboPagamento().inserirIntegracaoProtheus(itemSelecionado.getReciboPagamentoVO().getCodigo(), "E");
            }
            getFacade().getExtratoDiarioItem().estornarRecibo(itemSelecionado);
            consultarConciliacao();
            setSucesso(true);
            setMensagemDetalhada("Recibo estornado com sucesso!");
            setMsgAlert(getMensagemNotificar()+"Richfaces.hideModalPanel('modalEstornoExtrato');");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void mesclarLancamentos() {
        try {
            limparMsg();
            getFacade().getExtratoDiarioItem().mesclarLancamentos(itemZW, itemOperadora);
            consultarConciliacao();
            setSucesso(true);
            setMensagemDetalhada("O lançamento foi mesclado com sucesso!");
            setMsgAlert(getMensagemNotificar()+"Richfaces.hideModalPanel('modalMesclarCartao');");
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void abrirAvisoData() {
        abrirAlterarPagamento();
        itemSelecionado.setSomenteDatas(true);
    }
    public void abrirAvisoValor() {
        abrirAlterarPagamento();
        itemSelecionado.setSomenteDatas(false);
    }
    public void abrirAlterarPagamento() {
        try {
            itemSelecionado = (ExtratoDiarioItemVO) context().getExternalContext().getRequestMap().get("item");
            itemSelecionado.setSomenteDatas(!itemSelecionado.getValorDiferente());

            ExtratoDiarioItemVO extratoDiarioItemVO = null;
            if (!UteisValidacao.emptyNumber(itemSelecionado.getCodigo())) {
                extratoDiarioItemVO = getFacade().getExtratoDiarioItem().consultarPorChavePrimaria(itemSelecionado.getCodigo());
                itemSelecionado.setObservacao(extratoDiarioItemVO.getObservacao());
            }

            //TRATAMENTOS STONE V5
            if (itemSelecionado.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)
                    && !UteisValidacao.emptyNumber( extratoDiarioItemVO.getConvenio().getCodigo())) {
                TipoCredencialStoneEnum tipoCredencialStoneEnum = getFacade().getConvenioCobranca().obterTipoCredenciamentoStoneByCodigoConvenio(extratoDiarioItemVO.getConvenio().getCodigo());
                if (tipoCredencialStoneEnum != null) {
                    itemSelecionado.setTipoCredencialStoneEnum(tipoCredencialStoneEnum);
                }
            }

            obterParcelasItem();

            //ITEM DE VENDA E PARCELADO
            if (itemSelecionado.getTipoConciliacao() != null &&
                    itemSelecionado.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo()) &&
                    itemSelecionado.getNrTotalParcelas() > 1 &&
                    !UteisValidacao.emptyNumber(itemSelecionado.getCodigoMovPagamento()) &&
                    itemSelecionado.getDataLancamento() != null) {
                double valorLiquidoParcelamentoVendaAgrupado = obterValorLiquidoParcelamentoVendaAgrupadosItem();
                if (!UteisValidacao.emptyNumber(valorLiquidoParcelamentoVendaAgrupado)) {
                    itemSelecionado.setValorLiquidoParcelamentoVendaAgrupado(valorLiquidoParcelamentoVendaAgrupado);
                }
                double valorBrutoDeTaxas = obterValorDescontadoTaxasParcelamentoVendaAgrupadosItem();
                if (!UteisValidacao.emptyNumber(valorBrutoDeTaxas)) {
                    itemSelecionado.setValorDescontadoTaxasParcelamentoVendaAgrupado(valorBrutoDeTaxas);
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirEstornoRecibo() {
        itemSelecionado = (ExtratoDiarioItemVO) context().getExternalContext().getRequestMap().get("item");
    }

    public ExtratoDiarioItemVO getItemSelecionado() {
        return itemSelecionado;
    }

    public void setItemSelecionado(ExtratoDiarioItemVO itemSelecionado) {
        this.itemSelecionado = itemSelecionado;
    }

    public List<FormaPagamentoVO> getLista() {
        return lista;
    }

    public void setLista(List<FormaPagamentoVO> lista) {
        this.lista = lista;
    }

    public Map<String, FormaPagamentoVO> getMapaPagamentosParaGrafico() {
        return mapaPagamentosParaGrafico;
    }

    public void setMapaPagamentosParaGrafico(Map<String, FormaPagamentoVO> mapaPagamentosParaGrafico) {
        this.mapaPagamentosParaGrafico = mapaPagamentosParaGrafico;
    }

    public Map<String, String> getMapaFormasCores() {
        return mapaFormasCores;
    }

    public void setMapaFormasCores(Map<String, String> mapaFormasCores) {
        this.mapaFormasCores = mapaFormasCores;
    }

    public String getSelecionados() {
        return selecionados;
    }

    public void setSelecionados(String selecionados) {
        this.selecionados = selecionados;
    }

    public Double getTotalSelecionado() {
        return totalSelecionado;
    }

    public void setTotalSelecionado(Double totalSelecionado) {
        this.totalSelecionado = totalSelecionado;
    }

    public String getDescricaoFp() {
        return descricaoFp;
    }

    public void setDescricaoFp(String descricaoFp) {
        this.descricaoFp = descricaoFp;
    }

    public Double getOkValor() {
        return okValor;
    }

    public void setOkValor(Double okValor) {
        this.okValor = okValor;
    }

    public Double getPendenciaValor() {
        return pendenciaValor;
    }

    public void setPendenciaValor(Double pendenciaValor) {
        this.pendenciaValor = pendenciaValor;
    }

    public Double getNao_encontradoValor() {
        return nao_encontradoValor;
    }

    public void setNao_encontradoValor(Double nao_encontradoValor) {
        this.nao_encontradoValor = nao_encontradoValor;
    }

    public Map<Integer, ExtratoDiarioItemVO> getMapa() {
        return mapa;
    }

    public void setMapa(Map<Integer, ExtratoDiarioItemVO> mapa) {
        this.mapa = mapa;
    }
    
    public String getOkValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(okValor);
    }
    
    public String getNao_encontradoValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(nao_encontradoValor);
    }
    
    public String getPendenciaValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(pendenciaValor);
    }

    public boolean isTudoZerado() {
        return tudoZerado;
    }

    public void setTudoZerado(boolean tudoZerado) {
        this.tudoZerado = tudoZerado;
    }

    public boolean isMostrarZerados() {
        return mostrarZerados;
    }

    public void setMostrarZerados(boolean mostrarZerados) {
        this.mostrarZerados = mostrarZerados;
    }

    public boolean isVisaoConciliacao() {
        return visaoConciliacao;
    }

    public void setVisaoConciliacao(boolean visaoConciliacao) {
        this.visaoConciliacao = visaoConciliacao;
    }

    public List<CartaoCreditoTO> getListaCartoesOriginal() {
        return listaCartoesOriginal;
    }

    public void setListaCartoesOriginal(List<CartaoCreditoTO> listaCartoesOriginal) {
        this.listaCartoesOriginal = listaCartoesOriginal;
    }

    public List<MovContaVO> getDevolucoes() {
        return devolucoes;
    }

    public void setDevolucoes(List<MovContaVO> devolucoes) {
        this.devolucoes = devolucoes;
    }

    public Map<String, ExtratoDiarioItemVO> getMapaExtratoDiario() {
        return mapaExtratoDiario;
    }

    public void setMapaExtratoDiario(Map<String, ExtratoDiarioItemVO> mapaExtratoDiario) {
        this.mapaExtratoDiario = mapaExtratoDiario;
    }

    public ExtratoDiarioItemVO getItemOperadora() {
        return itemOperadora;
    }

    public void setItemOperadora(ExtratoDiarioItemVO itemOperadora) {
        this.itemOperadora = itemOperadora;
    }

    public ExtratoDiarioItemVO getItemZW() {
        return itemZW;
    }

    public void setItemZW(ExtratoDiarioItemVO itemZW) {
        this.itemZW = itemZW;
    }

    public List<SelectItem> getItensMovimentar() {
        if(itensMovimentar == null){
            itensMovimentar = new ArrayList<SelectItem>();
            itensMovimentar.add(new SelectItem(TipoFormaPagto.CARTAOCREDITO, "Movimentar crédito"));
            itensMovimentar.add(new SelectItem(TipoFormaPagto.CARTAODEBITO, "Movimentar débito"));
        }
        return itensMovimentar;
    }

    public void setItensMovimentar(List<SelectItem> itensMovimentar) {
        this.itensMovimentar = itensMovimentar;
    }

    public List<SelectItem> getDiasMovimentar() {
        if(diasMovimentar == null){
            diasMovimentar = new ArrayList<SelectItem>();
        }
        return diasMovimentar;
    }

    public void setDiasMovimentar(List<SelectItem> diasMovimentar) {
        this.diasMovimentar = diasMovimentar;
    }

    public TipoFormaPagto getTipoMovimentar() {
        return tipoMovimentar;
    }

    public void setTipoMovimentar(TipoFormaPagto tipoMovimentar) {
        this.tipoMovimentar = tipoMovimentar;
    }

    public Date getDataMovimentarExtrato() {
        return dataMovimentarExtrato;
    }

    public void setDataMovimentarExtrato(Date dataMovimentarExtrato) {
        this.dataMovimentarExtrato = dataMovimentarExtrato;
    }

    public Integer getChequeSelecionado() {
        return chequeSelecionado;
    }

    public void setChequeSelecionado(Integer chequeSelecionado) {
        this.chequeSelecionado = chequeSelecionado;
    }

    public Integer getCartaoSelecionado() {
        return cartaoSelecionado;
    }

    public void setCartaoSelecionado(Integer cartaoSelecionado) {
        this.cartaoSelecionado = cartaoSelecionado;
    }

    public Boolean getResetou() {
        return resetou;
    }

    public void setResetou(Boolean resetou) {
        this.resetou = resetou;
    }

    public Integer getPagamentoSelecionado() {
        return pagamentoSelecionado;
    }

    public void setPagamentoSelecionado(Integer pagamentoSelecionado) {
        this.pagamentoSelecionado = pagamentoSelecionado;
    }

    public String getOrdenacaoListagem() {
        return ordenacaoListagem;
    }

    public void setOrdenacaoListagem(String ordenacaoListagem) {
        this.ordenacaoListagem = ordenacaoListagem;
    }

    public String getRo() {
        return ro;
    }

    public void setRo(String ro) {
        this.ro = ro;
    }

    public String getCodAutorizacaoedi() {
        return codAutorizacaoedi;
    }

    public void setCodAutorizacaoedi(String codAutorizacaoedi) {
        this.codAutorizacaoedi = codAutorizacaoedi;
    }

    public List<SelectItem> getTiposConcilacao() {
        if(tiposConcilacao == null || tiposConcilacao.isEmpty()){
            for(TipoConciliacaoEnum t : TipoConciliacaoEnum.values()){
                if (t.equals(TipoConciliacaoEnum.PAGAMENTOS) || t.equals(TipoConciliacaoEnum.VENDAS)){
                    if (t.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                        t.setDescricao("Por Compensação");
                    }
                    if (t.equals(TipoConciliacaoEnum.VENDAS)) {
                        t.setDescricao("Por Faturamento Recebido");
                    }
                    tiposConcilacao.add(new SelectItem(t.getCodigo(), t.getDescricao()));
                }
            }
        }
        return tiposConcilacao;
    }

    public void setTiposConcilacao(List<SelectItem> tiposConcilacao) {
        this.tiposConcilacao = tiposConcilacao;
    }

    public Integer getTipoConciliacao() {
        return tipoConciliacao;
    }

    public void setTipoConciliacao(Integer tipoConciliacao) {
        this.tipoConciliacao = tipoConciliacao;
    }

    public Integer getCodigoConvenio() {
        return codigoConvenio;
    }

    public void setCodigoConvenio(Integer codigoConvenio) {
        this.codigoConvenio = codigoConvenio;
    }

    public List<SelectItem> getTiposConvenio() {
        return tiposConvenio;
    }

    public void setTiposConvenio(List<SelectItem> tiposConvenio) {
        this.tiposConvenio = tiposConvenio;
    }

    public List<SelectItem> getFormasPagamentoSI() {
        return formasPagamentoSI;
    }

    public void setFormasPagamentoSI(List<SelectItem> formasPagamento) {
        this.formasPagamentoSI = formasPagamento;
    }

    public Integer getFpSelecionada() {
        return fpSelecionada;
    }

    public void setFpSelecionada(Integer fpSelecionada) {
        this.fpSelecionada = fpSelecionada;
    }

    public Integer getNrNaoConciliados() {
        return nrNaoConciliados;
    }

    public void setNrNaoConciliados(Integer nrNaoConciliados) {
        this.nrNaoConciliados = nrNaoConciliados;
    }

    public Boolean getDetalharRelatorio() {
        return detalharRelatorio;
    }

    public void setDetalharRelatorio(Boolean detalharRelatorio) {
        this.detalharRelatorio = detalharRelatorio;
    }

    public Boolean getApresentarPagamentosCancelados() {
        return apresentarPagamentosCancelados;
    }

    public void setApresentarPagamentosCancelados(Boolean apresentarPagamentosCancelados) {
        this.apresentarPagamentosCancelados = apresentarPagamentosCancelados;
    }


    private void processarItemImpressao() {
        getItemImpressao().setDetalhar(getDetalharRelatorio());
        getItemImpressao().setValor(0.0);
        getItemImpressao().setValorBoleto(0.0);
        getItemImpressao().setValorContaCorrente(0.0);
        getItemImpressao().setValorDevolucoes(0.0);
        getItemImpressao().setValorEspecie(0.0);
        for (ResumoFormaPagamentoRelatorio formaPagamentoRelatorio : getItemImpressao().getFormasPagamento()) {
            if((imprimirDiario && !UteisValidacao.emptyNumber(formaPagamentoRelatorio.getValor()))){
                formaPagamentoRelatorio.setImprimir(true);
            }
            if (formaPagamentoRelatorio.getImprimir()) {
                if(formaPagamentoRelatorio.getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla())){
                    getItemImpressao().setValorBoleto(getItemImpressao().getValorBoleto() + formaPagamentoRelatorio.getValor());
                } else if(!formaPagamentoRelatorio.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla()) && !formaPagamentoRelatorio.getTipoFormaPagamento().equals("devolucoes")){ 
                     getItemImpressao().setValorEspecie(getItemImpressao().getValorEspecie() + formaPagamentoRelatorio.getValor());
                }
                if(formaPagamentoRelatorio.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())){
                    getItemImpressao().setValorContaCorrente(getItemImpressao().getValorContaCorrente()+ formaPagamentoRelatorio.getValor());
                } else {
                     if(!formaPagamentoRelatorio.getTipoFormaPagamento().equals("devolucoes")){
                        getItemImpressao().setValor(getItemImpressao().getValor() + formaPagamentoRelatorio.getValor());
                     } else {
                         getItemImpressao().setValorDevolucoes(getItemImpressao().getValorDevolucoes()+ formaPagamentoRelatorio.getValor());
                         getItemImpressao().setValor(getItemImpressao().getValor() - formaPagamentoRelatorio.getValor());
                     }
                }
            }
        }
    }

    public void salvarFiltroSessao() {
        FiltroGestaoRecebiveisTO filtroGestaoRecebiveisTO = new FiltroGestaoRecebiveisTO();
        filtroGestaoRecebiveisTO.setCodigoEmpresa(empresaRel.getCodigo());
        filtroGestaoRecebiveisTO.setEmpresasSelecionadas(empresasSelecionadas);
        filtroGestaoRecebiveisTO.setNome(nome);
        filtroGestaoRecebiveisTO.setNomeTerceiro(nomeTerceiro);
        filtroGestaoRecebiveisTO.setDataInicialLancamento(dataInicialLancamento);
        filtroGestaoRecebiveisTO.setDataFinalLancamento(dataFinalLancamento);
        filtroGestaoRecebiveisTO.setHoraInicialLancamento(horaInicialLancamento);
        filtroGestaoRecebiveisTO.setHoraFinalLancamento(horaFinalLancamento);
        filtroGestaoRecebiveisTO.setDataInicialCompensacao(dataInicialCompensacao);
        filtroGestaoRecebiveisTO.setDataFinalCompensacao(dataFinalCompensacao);
        filtroGestaoRecebiveisTO.setHoraInicialCompensacao(horaInicialCompensacao);
        filtroGestaoRecebiveisTO.setHoraFinalCompensacao(horaFinalCompensacao);
        filtroGestaoRecebiveisTO.setCpf(cpf);
        filtroGestaoRecebiveisTO.setMatricula(matricula);
        filtroGestaoRecebiveisTO.setNomeClienteContrato(nomeClienteContrato);
        filtroGestaoRecebiveisTO.setCodAutorizacao(codAutorizacao);
        filtroGestaoRecebiveisTO.setOperadoraCartao(operadoraCartao);
        filtroGestaoRecebiveisTO.setNsu(nsu);
        filtroGestaoRecebiveisTO.setCodigoLoteFiltro(codigoLoteFiltro);

        JSFUtilities.storeOnSession(FiltroGestaoRecebiveisTO.class.getName(), filtroGestaoRecebiveisTO);
    }

    public void restaurarFiltros(){
        FiltroGestaoRecebiveisTO filtroSessao = (FiltroGestaoRecebiveisTO) JSFUtilities.getFromSession(FiltroGestaoRecebiveisTO.class.getName());
        if (filtroSessao != null ){
            empresaRel.setCodigo(filtroSessao.getCodigoEmpresa());
            empresasSelecionadas = (filtroSessao.getEmpresasSelecionadas());
            nome = filtroSessao.getNome();
            nomeTerceiro = filtroSessao.getNomeTerceiro();
            dataInicialLancamento = filtroSessao.getDataInicialLancamento();
            dataFinalLancamento = filtroSessao.getDataFinalLancamento();
            horaInicialLancamento = filtroSessao.getHoraInicialLancamento();
            horaFinalLancamento = filtroSessao.getHoraFinalLancamento();
            dataInicialCompensacao = filtroSessao.getDataInicialCompensacao();
            dataFinalCompensacao = filtroSessao.getDataFinalCompensacao();
            horaInicialCompensacao = filtroSessao.getHoraInicialCompensacao();
            horaFinalCompensacao = filtroSessao.getHoraFinalCompensacao();
            cpf = filtroSessao.getCpf();
            matricula = filtroSessao.getMatricula();
            nomeClienteContrato = filtroSessao.getNomeClienteContrato();
            codAutorizacao = filtroSessao.getCodAutorizacao();
            operadoraCartao =filtroSessao.getOperadoraCartao();
            nsu = filtroSessao.getNsu();
            codigoLoteFiltro = filtroSessao.getCodigoLoteFiltro();


        }
    }

    public CentroCustoTO getCentroCustoTO() {
        return centroCustoTO;
    }

    public void setCentroCustoTO(CentroCustoTO centroCustoTO) {
        this.centroCustoTO = centroCustoTO;
    }

    public Boolean getAntecipados() {
        return antecipados;
    }

    public void setAntecipados(Boolean antecipados) {
        this.antecipados = antecipados;
    }

    public boolean isEstornadoOperadora() {
        return estornadoOperadora;
    }

    public void setEstornadoOperadora(boolean estornadoOperadora) {
        this.estornadoOperadora = estornadoOperadora;
    }

    public boolean isApresentarEstornarTodos() {
        return !ok && !pendencia && !nao_encontrado && !estornado &&  estornadoOperadora;
    }

    public void estornarTodos() {
        try {
            limparMsg();
            for (ExtratoDiarioItemVO cc : extratoDiarioBack) {
                if (cc.getSituacao().equals(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA)) {
                    getFacade().getExtratoDiarioItem().estornarRecibo(cc);
                }
            }
            consultarConciliacao();
            setSucesso(true);
            setMensagemDetalhada("Recibos estornados com sucesso!");
            setMsgAlert(getMensagemNotificar()+"Richfaces.hideModalPanel('modalEstornoExtratoTodos');");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void processarTodosAlertas() {
        try {
            limparMsg();
            for (ExtratoDiarioItemVO item : extratoDiarioBack) {
                if (item.getSituacao() != null && item.getSituacao().equals(SituacaoItemExtratoEnum.PENDENCIAS) && item.getDataDiferente()) {
                    item.setSomenteDatas(!item.getValorDiferente());
                    if (item.getSomenteDatas()) {
                        getFacade().getExtratoDiarioItem().alterarDatasPagamento(item);
                    }
                }
            }
            consultarConciliacao();
            setSucesso(true);
            setMensagemDetalhada("Itens ajustados com sucesso!");
            setMsgAlert(getMensagemNotificar()+"Richfaces.hideModalPanel('modalAlertaTodos');");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    private void obterParcelasItem() throws Exception {
        getFacade().getExtratoDiarioItem().preencherParcelasItem(itemSelecionado);
    }

    private double obterValorLiquidoParcelamentoVendaAgrupadosItem() throws Exception {
        return getFacade().getExtratoDiarioItem().obterValorLiquidoParcelamentoVendaAgrupadosItem(itemSelecionado.getCodigoMovPagamento(), itemSelecionado.getDataLancamento());
    }

    private double obterValorDescontadoTaxasParcelamentoVendaAgrupadosItem() throws Exception {
        return getFacade().getExtratoDiarioItem().obterValorDescontadoTaxasParcelamentoVendaAgrupadosItem(itemSelecionado.getCodigoMovPagamento(), itemSelecionado.getDataLancamento());
    }

    private void validarCaixaAbertoConta(Integer codigoContaOrigem) throws Exception{
        CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
        caixaControle.validarCaixaAbertoConta(codigoContaOrigem, true);
    }

    public void notificarRecursoEmpresaExportarExcelResumido() {
        notificarRecursoEmpresa(RecursoSistema.EXCEL_RESUMIDO_GESTAO_RECEBIVEIS);
    }

    public void calcularTotalCartoes() {
        totalCartoes = 0.0;
        qtdeTotalCartoes = 0;
        // percorre todos os cartoes da lista
        Iterator i = getListaCartoes().iterator();
        while (i.hasNext()) {
            CartaoCreditoTO c = (CartaoCreditoTO) i.next();
            // soma somente os cartoes marcados
            if (c.isCartaoEscolhido()) {
                totalCartoes = totalCartoes + c.getValor();
                qtdeTotalCartoes++;
            }
        }
        if(qtdeTotalCartoes > 0){
            setExibeRetirarCartaoLotes(true);
        }else{
            setExibeRetirarCartaoLotes(false);
        }
    }

    public void marcaDesmarcaCartoes() {
        for (CartaoCreditoTO cc : listaCartoes) {
            if ((getUsarMovimentacao() || cc.getNumeroLote() == 0) && cc.isAtivo()) {
                cc.setCartaoEscolhido(todosCartoesMarcados);
            }

        }
        calcularTotalCartoes();
    }

    public void marcaDesmarcaCheques() {
        for (ChequeTO ch : listaCheques) {
            if (ch.getLoteAvulso() <= 0 && (getUsarMovimentacao() || ch.getNumeroLote() == 0) && ch.isAtivo()) {
                ch.setChequeEscolhido(todosChequesMarcados);
            }

        }
        calcularTotalCheques();
    }

    public void calcularTotalCheques() {
        setMsgAlert("");
        totalCheques = 0.0;
        qtdeTotalCheques = 0;
        // percorre todos os cheques da lista
        Iterator i = getListaCheques().iterator();
        while (i.hasNext()) {
            ChequeTO c = (ChequeTO) i.next();
            // soma somente os cheques marcados
            if (c.isChequeEscolhido()) {
                totalCheques = Uteis.arredondarForcando2CasasDecimais(totalCheques + c.getValor());
                qtdeTotalCheques++;
            }
        }
        if(qtdeTotalCheques > 0){
            setExibeRetirarChequeLotes(true);
        }else{
            setExibeRetirarChequeLotes(false);
        }
    }

    public String[] identificacaoPessoalInternacional(EmpresaVO empresaVO) {
        try {
            displayIdentificadorFront = identificadorPessoaInternacional(empresaVO.getPais().getNome());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return displayIdentificadorFront;

    }
    public static String formatarValorMonetarioSemMoeda(final Double valor) {
        try {
            final NumberFormat numberf = NumberFormat.getCurrencyInstance(Formatador.BRASIL);

            String str = numberf.format(valor);
            str = str.substring(3, str.length());
            return str;
        } catch (Exception e) {
            return null;

        }
    }

    public Boolean getExibeRetirarCartaoLotes() {
        return exibeRetirarCartaoLotes;
    }

    public void setExibeRetirarCartaoLotes(Boolean exibeRetirarCartaoLotes) {
        this.exibeRetirarCartaoLotes = exibeRetirarCartaoLotes;
    }

    public Boolean getExibeRetirarChequeLotes() {
        return exibeRetirarChequeLotes;
    }

    public void setExibeRetirarChequeLotes(Boolean exibeRetirarChequeLotes) {
        this.exibeRetirarChequeLotes = exibeRetirarChequeLotes;
    }

    public String[] getDisplayIdentificadorFront() {
        return displayIdentificadorFront;
    }

    public void setDisplayIdentificadorFront(String[] displayIdentificadorFront) {
        this.displayIdentificadorFront = displayIdentificadorFront;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public TotalizadorConciliacaoDTO getTotalizadorConciliacaoDTO() {
        return totalizadorConciliacaoDTO;
    }

    public void setTotalizadorConciliacaoDTO(TotalizadorConciliacaoDTO totalizadorConciliacaoDTO) {
        this.totalizadorConciliacaoDTO = totalizadorConciliacaoDTO;
    }

    public List<ExtratoDiarioItemVO> getExtratoDiarioCancelamentos() {
        return extratoDiarioCancelamentos;
    }

    public void setExtratoDiarioCancelamentos(List<ExtratoDiarioItemVO> extratoDiarioCancelamentos) {
        this.extratoDiarioCancelamentos = extratoDiarioCancelamentos;
    }

    public List<RecebivelTO> gerarListaExportavel(EmpresaVO empresaVO, String matricula, Date dataInicialLancamento, Date dataFinalLancamento) throws Exception {
        this.empresaRel = empresaVO;
        this.matricula = matricula;
        this.dataInicialLancamento = dataInicialLancamento;
        this.dataFinalLancamento = dataFinalLancamento;

        consultarDados();

        if ((listaConsultaRecebiveis == null || listaConsultaRecebiveis.isEmpty())
                && (devolucoes == null || devolucoes.isEmpty())) {
            return null;
        }

        List<RecebivelTO> listaExportavel = montarListaExportavel(false);

        return listaExportavel;
    }

    public String gerarRelatorioXlsF360PorPeriodo(EmpresaVO empresaVO, Date dataInicio, Date dataTermino) throws Exception {
        empresaRel = empresaVO;
        dataInicialLancamento = dataInicio;
        dataFinalLancamento = dataTermino;

        consultarDados();

        List<RecebivelTO> listaExportavel = new ArrayList<>();
        if (!((listaConsultaRecebiveis == null || listaConsultaRecebiveis.isEmpty())
                && (devolucoes == null || devolucoes.isEmpty()))) {
            listaExportavel = montarListaExportavel(true);
        }

        String atributos = "nomeEmpresa=Empresa,cnpjEmpresa=CNPJ,matricula=Matricula,nomePessoa=Nome do pagador,cpfPagador=CPF,tipoRecebivel=Tipo do Recebível,conta=Conta," +
                "valor=Valor,parcelasMescladas=Parcelas,valorTotal=Valor total,dataLancamento=Data Lançamento,codigoUnico=Documento,recibo=Recibo,operadora=Operadora,autorizacao=Autorização," +
                "nsu=NSU,produtos=produtos,planoContrato=Plano contrato,modalidade=Modalidade";

        return new ExportadorExcel().gerarArquivoExcelPorAtributos("GestaoRecebiveisF360", atributos, listaExportavel).getAbsolutePath();
    }

    public Date getDataInicioMovimentacaoInicio() {
        return dataInicioMovimentacaoInicio;
    }

    public void setDataInicioMovimentacaoInicio(Date dataInicioMovimentacaoInicio) {
        this.dataInicioMovimentacaoInicio = dataInicioMovimentacaoInicio;
    }

    public Date getDataInicioMovimentacaoFim() {
        return dataInicioMovimentacaoFim;
    }

    public void setDataInicioMovimentacaoFim(Date dataInicioMovimentacaoFim) {
        this.dataInicioMovimentacaoFim = dataInicioMovimentacaoFim;
    }

    public String getHoraInicioMovimentacaoInicio() {
        return horaInicioMovimentacaoInicio;
    }

    public void setHoraInicioMovimentacaoInicio(String horaInicioMovimentacaoInicio) {
        this.horaInicioMovimentacaoInicio = horaInicioMovimentacaoInicio;
    }

    public String getHoraInicioMovimentacaoFim() {
        return horaInicioMovimentacaoFim;
    }

    public void setHoraInicioMovimentacaoFim(String horaInicioMovimentacaoFim) {
        this.horaInicioMovimentacaoFim = horaInicioMovimentacaoFim;
    }

    public void prepararExportarRecebiceis(ActionEvent evt) {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

        Map<Integer, ReciboPagamentoVO> mapaReciboUsuario = new HashMap<>();
        if (!UteisValidacao.emptyList(this.listaCartoes)) {
            for (CartaoCreditoTO obj : this.listaCartoes) {
                try {
                    if (!UteisValidacao.emptyNumber(obj.getRecibo())) {
                        ReciboPagamentoVO reciboPagamentoVO = mapaReciboUsuario.get(obj.getRecibo());
                        if (reciboPagamentoVO == null) {
                            reciboPagamentoVO = getFacade().getReciboPagamento().obterReciboExportacaoRecebiveis(obj.getRecibo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                            mapaReciboUsuario.put(obj.getRecibo(), reciboPagamentoVO);
                        }
                        obj.setContratoVO(reciboPagamentoVO.getContrato());
                        obj.setNomeResponsavelRecibo(reciboPagamentoVO.getResponsavelLancamento().getNome());
                    }

                    String nomeAlunosParcela = "";
                    int cont = 1;
                    if(obj.getMovPagamentoVO() != null && !UteisValidacao.emptyList(obj.getMovPagamentoVO().getPagamentoMovParcelaVOs())){
                        for(PagamentoMovParcelaVO pagamentoMovParcelaVO: obj.getMovPagamentoVO().getPagamentoMovParcelaVOs()){
                            if(
                                    pagamentoMovParcelaVO.getMovParcela() != null
                                    && pagamentoMovParcelaVO.getMovParcela().getPessoa() != null
                                    && !UteisValidacao.emptyString(pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome())
                            ){
                                if(cont == 1){
                                    nomeAlunosParcela += pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                                    cont++;
                                } else {
                                    nomeAlunosParcela += ", " + pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                                }
                            }
                        }
                    }
                    obj.setNomeAlunosDaParcela(nomeAlunosParcela);

                    obj.setDocumentoIntegracaoSesi(
                            preencherDadosColunaDocumentoExportarExcel(obj.getEmpresa().getCodExternoUnidadeSesi(), obj.getDataLancamento(), obj.getNsu(), obj.getAutorizacao())
                    );
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }else if (!UteisValidacao.emptyList(this.listaCheques)) {
            for (ChequeTO obj : this.listaCheques) {
                try {
                    if (!UteisValidacao.emptyNumber(obj.getRecibo())) {
                        ReciboPagamentoVO reciboPagamentoVO = mapaReciboUsuario.get(obj.getRecibo());
                        if (reciboPagamentoVO == null) {
                            reciboPagamentoVO = getFacade().getReciboPagamento().obterReciboExportacaoRecebiveis(obj.getRecibo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                            mapaReciboUsuario.put(obj.getRecibo(), reciboPagamentoVO);
                        }
                        obj.setContratoVO(reciboPagamentoVO.getContrato());
                        obj.setNomeResponsavelRecibo(reciboPagamentoVO.getResponsavelLancamento().getNome());
                    }

                    String nomeAlunosParcela = "";
                    int cont = 1;
                    if(obj.getMovPagamentoVO() != null && !UteisValidacao.emptyList(obj.getMovPagamentoVO().getPagamentoMovParcelaVOs())){
                        for(PagamentoMovParcelaVO pagamentoMovParcelaVO: obj.getMovPagamentoVO().getPagamentoMovParcelaVOs()){
                            if(
                                    pagamentoMovParcelaVO.getMovParcela() != null
                                    && pagamentoMovParcelaVO.getMovParcela().getPessoa() != null
                                    && !UteisValidacao.emptyString(pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome())
                            ){
                                if(cont == 1){
                                    nomeAlunosParcela += pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                                    cont++;
                                } else {
                                    nomeAlunosParcela += ", " + pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                                }
                            }
                        }
                    }
                    obj.setNomeAlunosDaParcela(nomeAlunosParcela);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }else if (!UteisValidacao.emptyList(this.listaOutros)) {
            for (MovPagamentoVO obj : this.listaOutros) {
                try {
                    if (obj.getReciboPagamento() != null &&
                            !UteisValidacao.emptyNumber(obj.getReciboPagamento().getCodigo())) {
                        ReciboPagamentoVO reciboPagamentoVO = mapaReciboUsuario.get(obj.getReciboPagamento().getCodigo());
                        if (reciboPagamentoVO == null) {
                            reciboPagamentoVO = getFacade().getReciboPagamento().obterReciboExportacaoRecebiveis(obj.getReciboPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                            mapaReciboUsuario.put(obj.getReciboPagamento().getCodigo(), reciboPagamentoVO);
                        }
                        obj.setContratoVO(reciboPagamentoVO.getContrato());
                        obj.setNomeResponsavelRecibo(reciboPagamentoVO.getResponsavelLancamento().getNome());
                    }

                    String nomeAlunosParcela = "";
                    int cont = 1;
                    for(PagamentoMovParcelaVO pagamentoMovParcelaVO: obj.getPagamentoMovParcelaVOs()){
                        if(
                                pagamentoMovParcelaVO.getMovParcela() != null
                                && pagamentoMovParcelaVO.getMovParcela().getPessoa() != null
                                && !UteisValidacao.emptyString(pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome())
                        ){
                            if(cont == 1){
                                nomeAlunosParcela += pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                                cont++;
                            } else {
                                nomeAlunosParcela += ", " + pagamentoMovParcelaVO.getMovParcela().getPessoa().getNome();
                            }
                        }
                    }
                    obj.setNomeAlunosDaParcela(nomeAlunosParcela);

                    if(obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())){
                        obj.setDocumentoIntegracaoSesi(
                                preencherDadosColunaDocumentoExportarExcel(obj.getEmpresa().getCodExternoUnidadeSesi(), obj.getDataLancamento(), obj.getNsu(), obj.getAutorizacaoCartao())
                        );
                    }

                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }

        exportadorListaControle.exportar(evt);
    }

    public Double getTaxaAntecipacao() {
        if (taxaAntecipacao == null) {
            return 0.0;
        }
        return taxaAntecipacao;
    }

    public void setTaxaAntecipacao(Double taxaAntecipacao) {
        this.taxaAntecipacao = taxaAntecipacao;
    }

    public String getNumeroDocumento() {
        if (numeroDocumento == null) {
            numeroDocumento = "";
        }
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public boolean isExibirModificarCompensacaoBoleto() {
        return this.getFormaPagamentoSelecionada() != null &&
                this.getFormaPagamentoSelecionada().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla());
    }

    public Date getDataNovaCompensacaoBoletos() {
        return dataNovaCompensacaoBoletos;
    }

    public void setDataNovaCompensacaoBoletos(Date dataNovaCompensacaoBoletos) {
        this.dataNovaCompensacaoBoletos = dataNovaCompensacaoBoletos;
    }

    public void abrirModalAlteracaoCompensacaoBoleto() {
        try {
            limparMsg();
            setMsgAlert("");
            dataNovaCompensacaoBoletos = Calendario.hoje();
            boolean algumSelecionado = false;
            for (MovPagamentoVO cd : listaOutros) {
                if (cd.getMovPagamentoEscolhidaFinan()) {
                    algumSelecionado = true;
                    break;
                }
            }
            if (algumSelecionado) {
                setMsgAlert("Richfaces.showModalPanel('modalAlterarCompensacaoBoleto');");
            } else {
                throw new Exception(getMensagemInternalizacao("msg_nenhumitemselecionado"));
            }
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void alterarDataCompensacaoBoleto() {
        try {
            limparMsg();
            setMsgAlert("");

            if (this.getDataNovaCompensacaoBoletos() == null) {
                throw new Exception("Data de compensação não informada");
            }

            StringBuilder txtLog = new StringBuilder();
            txtLog.append("Alteração Data Compensação Boletos \n");
            for (MovPagamentoVO pag : listaOutros) {
                if (pag.getMovPagamentoEscolhidaFinan()) {
                    txtLog.append("\n -").append(pag.getNomePagador()).append(", Códigos:").append(pag.getCodigo()).append(", Data Original:").append(pag.getDataPagamento_Apresentar()).append(", Data Nova:").append(Formatador.formatarDataPadrao(getDataNovaCompensacaoBoletos()));
                    getFacade().getMovPagamento().alterarDataCompensacao(pag.getCodigo(), getDataNovaCompensacaoBoletos());
                    pag.setDataPagamento(getDataNovaCompensacaoBoletos());
                }
            }
            LogVO log = new LogVO();
            log.setNomeEntidade("ALTERACAOBOLETO");
            log.setNomeEntidadeDescricao("Boleto");
            log.setNomeCampo("Data de Compensação");
            log.setValorCampoAlterado(txtLog.toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setResponsavelAlteracao(getUsuarioLogado().getNome().toUpperCase());
            log.setUserOAMD(getUsuarioLogado().getUserOamd());
            log.setOperacao("ALTERAÇÃO");
            getFacade().getLog().incluir(log);
            setMensagemDetalhada("Data de Compensação alterada com sucesso!");
            setSucesso(true);
            setMsgAlert(getMensagemNotificar() + "Richfaces.hideModalPanel('modalAlterarCompensacaoBoleto');");
            limparMsg();
            setSelecionados("");
        } catch (Exception e) {
            montarErro(e.getMessage());
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void abrirModalExcluirLancamentoFinanceiro(ActionEvent evt) throws Exception {
        try {
            limparMsg();

            //validar se tem caixa em aberto
            int codCaixa = obterCodigoCaixaEmAberto();
            if (UteisValidacao.emptyNumber(codCaixa)) {
                throw new ConsistirException("O usuário não possui Caixa aberto. É necessário abrir o Caixa antes de realizar a operação.");
            }

            //validar permissão excluir conta pagar/receber
            validarPermissao("ExcluirContaPagarEReceber", "9.45 - Permitir excluir conta a Pagar ou Receber", getUsuarioLogado());

            setMovContaVOExcluirDeposito(new MovContaVO());
            setMovContaRateioVOExcluirDeposito(new MovContaRateioVO());
            setListaMovPagamentoExcluirDeposito(new ArrayList());
            setListaMovPagamentoExcluirDepositoPaginada(new ListaPaginadaTO(10));
            int codMovConta = Integer.valueOf(evt.getComponent().getAttributes().get("codMovConta").toString());
            MovContaVO movContaVO = getFacade().getMovConta().consultarPorCodigo(codMovConta, Uteis.NIVELMONTARDADOS_TODOS);
            setMovContaVOExcluirDeposito(movContaVO);
            //valida se o lançamento pertence a outro caixa para exibir o ícone de aviso no modal
            validarCaixaDiferente();
            setMovContaRateioVOExcluirDeposito(movContaVO.getMovContaRateios().get(0));
            listaMovPagamentoExcluirDeposito = getFacade().getMovPagamento().consultarPorMovConta(codMovConta, LISTA_PAGINADA_LIMIT, 0);
            listaMovPagamentoExcluirDepositoPaginada.setCount(getFacade().getMovPagamento().consultarPorMovContaCount(codMovConta, LISTA_PAGINADA_LIMIT, 0));
            setMsgAlert("Richfaces.showModalPanel('modalExcluirDepositoFinanceiro');");
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMsgAlert("");
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void validarCaixaDiferente() throws Exception {

        setCaixaAtualDiferenteDoCaixaDoDepositoExcluir(false);
        boolean caixadiferente = getFacade().getCaixaMovConta().validarCaixaAberto(getMovContaVOExcluirDeposito()) != 0;
        int codCaixaAtual = obterCodigoCaixaEmAberto();
        if (caixadiferente) {
            setCaixaAtualDiferenteDoCaixaDoDepositoExcluir(true);
            setCodCaixaAtual(codCaixaAtual);
            setCodCaixaDoDepositoExcluir(getMovContaVOExcluirDeposito().getCaixa());
        } else {
            setCodCaixaAtual(codCaixaAtual);
        }
    }

    public int obterCodigoCaixaEmAberto() {
        int codigoCaixa = 0;
        if (getUsarMovimentacao()) {
            try {
                CaixaControle caixaControle = (CaixaControle) getControlador(CaixaControle.class.getSimpleName());
                codigoCaixa = caixaControle.getCaixaVoEmAberto().getCodigo();
            } catch (Exception e) {
            }
        }
        return codigoCaixa;
    }

    public void reabrirCaixa(CaixaVO caixaReabrir) throws Exception {
        try {
            validarPermissoesReabrirCaixa(caixaReabrir, getUsuarioLogado());
            BloqueioCaixaVO bloqueio = getFacade().getFinanceiro().getBloqueioCaixa().consultarBloqueioAtual(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Date fechamento = Calendario.getDataComHoraZerada(caixaReabrir.getDataFechamento());
            if (bloqueio != null && fechamento.before(Calendario.getDataComHora(bloqueio.getDataBloqueio(), "23:59"))) {
                throw new Exception("Não é permitido fazer a reabertura de caixas com a data de fechamento igual ou inferior a data "+ Uteis.getData(bloqueio.getDataBloqueio()) + " do bloqueio de caixa!");
            } else {
                getFacade().getFinanceiro().getCaixa().reabrirCaixa(caixaReabrir);
                salvarLogCaixa(caixaReabrir, "Reabertura automática de Caixa pela exclusão de depósito", getUsuarioLogado());
            }
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", ex.getMessage());
            throw ex;
        }
    }

    public void fecharCaixa(CaixaVO caixaVOFechamento) throws Exception {
        try {
            caixaVOFechamento.setDataFechamento(Calendario.hoje());
            caixaVOFechamento.setResponsavelFechamento(getUsuarioLogado());
            getFacade().getFinanceiro().getCaixa().alterar(caixaVOFechamento);
            salvarLogCaixa(caixaVOFechamento, "Fechamento automático de Caixa pela exclusão de depósito", getUsuarioLogado());

            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("Depósito excluído com sucesso!");
            montarSucessoGrowl("Depósito excluído com sucesso!");
        } catch (Exception ex) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }

    public void validarPermissoesReabrirCaixa(CaixaVO caixaReabrir, UsuarioVO usuario) throws Exception {
        boolean podeReabrirCaixa = false;
        boolean caixaDeOutroUsuario = !caixaReabrir.getUsuarioVo().getCodigo().equals(getUsuarioLogado().getCodigo());
        boolean podeFecharCaixaOutroUsuario = false;

        //permissão reabrir caixa
        try {
            validarPermissao("ReabrirCaixa", "9.21 - Reabrir e movimentar um caixa administrativo", usuario);
            podeReabrirCaixa = true;
        } catch (Exception ex) {}

        //permissão fechar caixa outro usuário

        if (caixaDeOutroUsuario) {
            try {
                validarPermissao("FecharCaixaOutroUsuario", "9.19 - Fechar Caixa de Outro Usuário", usuario);
                podeFecharCaixaOutroUsuario = true;
            } catch (Exception ex) {
            }
        }

        if (!podeReabrirCaixa) {
            throw new Exception("Para prosseguir com a exclusão, o usuário logado precisa da permissão 9.21 - Reabrir Caixa");
        }

        if (podeReabrirCaixa && (caixaDeOutroUsuario && !podeFecharCaixaOutroUsuario)) {
            throw new Exception("Para prosseguir com a exclusão, o usuário logado precisa da permissão 9.19 - Fechar Caixa de Outro Usuário, pois o caixa pertence a outro usuário");
        }
    }

    public void modalConfirmacaoExclusaoDeposito() throws Exception {
        setAbrirFecharModalPerguntaExclusaoDeposito("Richfaces.showModalPanel('modalPerguntaExcluirDepositoFinanceiro');");
    }

    public void operacaoExclusaoDeposito() {
        try {

            //caixa do lançamento que está tentando excluir
            CaixaVO caixaVODoDepositoAExcluir = getFacade().getCaixa().consultarCaixaPorMovConta(getMovContaVOExcluirDeposito().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            //para caixa diferente do caixa em aberto atual, reabrir automaticamente o caixa
            if (isCaixaAtualDiferenteDoCaixaDoDepositoExcluir()) {
                reabrirCaixa(caixaVODoDepositoAExcluir);
            }

            setMsgAlert("");
            limparMsg();

            getFacade().getFinanceiro().getMovConta().validarDataBloqueio(getMovContaVOExcluirDeposito(), true);

            List<MovContaVO> movContaRelacionados = new ArrayList<MovContaVO>();

            if (UteisValidacao.notEmptyNumber(getMovContaVOExcluirDeposito().getLote().getCodigo())) {
                movContaRelacionados = getFacade().getFinanceiro().getMovConta().consultarMovContaRelacionados(getMovContaVOExcluirDeposito());
                relacionadoPorLote = getFacade().getFinanceiro().getMovConta().consultarMovContaRelacionadoPorLote(getMovContaVOExcluirDeposito());
            }

            if (movContaRelacionados.isEmpty() && relacionadoPorLote.isEmpty()) {
                excluirMovConta();
            } else if (movContaRelacionados.isEmpty()) {
                confirmarExclusaoLotesRelacionados();
            }

            //atualizar objeto na tela
            for (MovPagamentoVO item : getListaOutros()) {
                if (item.getMovconta() != null && item.getMovconta().equals(getMovContaVOExcluirDeposito().getCodigo())) {
                    item.setDataMovimento(null);
                    item.setContaFinanceiro("");
                    item.setMovconta(null);
                }
            }

            //para caixa diferente, após reabrir automaticamente, fehcar também automaticamente
            if (isCaixaAtualDiferenteDoCaixaDoDepositoExcluir()) {
                fecharCaixa(caixaVODoDepositoAExcluir);
            }

            setMovContaVOExcluirDeposito(new MovContaVO());
            setMovContaRateioVOExcluirDeposito(new MovContaRateioVO());

            setSucesso(true);
            setErro(false);
            setMensagemDetalhada("Depósito excluído com sucesso!");
            montarSucessoGrowl("Depósito excluído com sucesso!");
            setAbrirFecharModalPerguntaExclusaoDeposito("Richfaces.hideModalPanel('modalPerguntaExcluirDepositoFinanceiro');");
            setAbrirFecharModalExclusaoDeposito("Richfaces.hideModalPanel('modalExcluirDepositoFinanceiro');");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getTitleCaixaDiferente() {
        StringBuilder title = new StringBuilder();
        if (isCaixaAtualDiferenteDoCaixaDoDepositoExcluir()) {
            title.append("<b>Cód. do caixa do depósito: </b>" + getCodCaixaDoDepositoExcluir() + "</br>");
            title.append("<b>Cód. do caixa aberto atual: </b>" + getCodCaixaAtual() + "</br>");
        } else {
            title.append("<b>Este depósito foi realizado no caixa em aberto atual");
        }
        return title.toString();
    }

    public String getTitleCaixaDiferenteIcon() {
        StringBuilder title = new StringBuilder();
        title.append("Este lançamento foi realizado em outro caixa que já está fechado.</br>");
        title.append("Se você prosseguir com a exclusão, <b>o caixa antigo será reaberto e fechado automaticamente</b>.");
        return title.toString();
    }

    private void confirmarExclusaoLotesRelacionados() {
        StringBuilder movContas = new StringBuilder();
        for (MovContaVO movConta : relacionadoPorLote) {
            movContas.append(", ").append(movConta.getDescricao());
        }

        setMsgAlert("if(confirm('" + getMensagemInternalizacao("msg_movConta_derivado") + movContas.toString().replaceFirst(",", "")
                + "')){document.getElementById('form:excluirDerivados').click(); }");
    }

    private void salvarLogCaixa(CaixaVO caixa, String operacao, UsuarioVO usuario) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("Caixa");
        log.setNomeEntidadeDescricao("Caixa");
        log.setChavePrimaria(caixa.getCodigo().toString());
        log.setNomeCampo("Dia Trabalho:: "+caixa.getDataTrabalho_Apresentar()+" Responsável:: " +usuario.getNome().toUpperCase());
        log.setDescricao("");
        log.setValorCampoAnterior(" -- ");
        log.setDataAlteracao(Calendario.hoje());
        log.setOperacao(operacao.toUpperCase());
        log.setUsuarioVO(usuario);
        log.setResponsavelAlteracao(usuario.getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        String texto = "Valor Entrada: " + Formatador.formatarValorMonetario(caixa.getTotalEntrada()) + " \n";
        texto = texto + "Valor Saída: " + Formatador.formatarValorMonetario(caixa.getTotalSaida()) + " \n";
        log.setValorCampoAlterado(texto);
        getFacade().getLog().incluir(log);
    }

    private void exibirErroMovContasRelacionados(List<MovContaVO> movContaRelacionados) {
        StringBuilder movContas = new StringBuilder();
        Ordenacao.ordenarListaReverse(movContaRelacionados, "codigo");
        int ordem = 1;
        for (MovContaVO movConta : movContaRelacionados) {
            if (!UteisValidacao.emptyString(movConta.getDescricao())) {
                movContas.append(", ").append(ordem + "º: ").append(movConta.getCodigo());
            } else {
                movContas.append(", Lote ").append(movConta.getLote().getCodigo()).append(" - ").append(movConta.getLote().getDescricao());
            }
            ordem++;
        }
        setSucesso(false);
        setErro(false);
        setMensagemDetalhada("msg_erro", getMensagemInternalizacao("msg_movConta_Lotes_Derivados") + movContas.toString().replaceFirst(",", ""));
    }

    private void excluirMovConta() throws Exception {
        getFacade().getMovPagamento().alterarPagamentoRetirandoDoCaixa(getMovContaVOExcluirDeposito().getCodigo(), getEmpresaLogado().getNrDiasCompensacao());
        final AgendamentoFinanceiroVO agendamentoFinanceiroVO = getFacade().getFinanceiro().getAgendamentoFinanceiro().consultarPorMovConta(getMovContaVOExcluirDeposito().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getFacade().getFinanceiro().getMovConta().excluir(getMovContaVOExcluirDeposito());
        registrarLogExcluirParcelaDoAgendamento(agendamentoFinanceiroVO, getMovContaVOExcluirDeposito());
        excluirAgendamentos(getMovContaVOExcluirDeposito());

        MovContaVO movContaVOAntesExcluir = (MovContaVO) getMovContaVOExcluirDeposito().getClone(true);
        setMensagemID("msg_dados_excluidos");

        getControlador(CaixaControle.class).atualizarCaixa();

        gerarLogExclusao(movContaVOAntesExcluir);

    }

    private void excluirAgendamentos(MovContaVO movConta) throws Exception {
        if (movConta.getAgendamentoFinanceiro() <= 0) {
            return;
        }

        List<MovContaVO> lista = getFacade().getFinanceiro().getMovConta().
                consultarLancamentosPeloAgendamento(movConta.getAgendamentoFinanceiro(), null, null,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (lista.isEmpty()) {
            getFacade().getFinanceiro().getAgendamentoFinanceiro().excluir(movConta.getAgendamentoFinanceiro());
        }
    }

    private void registrarLogExcluirParcelaDoAgendamento(AgendamentoFinanceiroVO agendamentoFinanceiroVO, MovContaVO movContaVO) throws Exception {
        if (agendamentoFinanceiroVO == null || agendamentoFinanceiroVO.getCodigo() == null || agendamentoFinanceiroVO.getCodigo() <= 0) {
            return;
        }

        LogVO obj = new LogVO();
        obj.setOperacao("ALTERAÇÃO");
        obj.setChavePrimaria(agendamentoFinanceiroVO.getCodigo().toString());
        obj.setNomeEntidade("AGENDAMENTOFINANCEIRO");
        obj.setNomeEntidadeDescricao("AGENDAMENTO FINANCEIRO");
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
        obj.setUserOAMD(getUsuarioLogado().getUserOamd());
        obj.setNomeCampo("MENSAGEM");

        StringBuilder msg = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        msg.append("Parcela do agendamento que foi excluída: \n");
        msg.append("PARCELA ").append(movContaVO.getCodigo()).append(" - ").append(sdf.format(movContaVO.getDataVencimento())).append(" - ").append(Formatador.formatarValorMonetario(movContaVO.getValor())).append("\n");

        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());

        registrarLogObjetoVO(obj, agendamentoFinanceiroVO.getCodigo());
    }


    private void gerarLogExclusao(MovContaVO movContaVOAntesExcluir) throws Exception {
        try {
            gerarLog(new StringBuilder("Favorecido: ").append(movContaVOAntesExcluir.getPessoaVO().getNome())
                    .append(" - Id: ").append(movContaVOAntesExcluir.getCodigo())
                    .append(" - Descrição: ").append(movContaVOAntesExcluir.getDescricao())
                    .append(" - Conta: ").append(movContaVOAntesExcluir.getContaVO().getDescricao())
                    .append(" - Tipo de operação: ").append(movContaVOAntesExcluir.getTipoOperacaoLancamento_Apresentar())
                    .append(" \n")
                    .append(" - Valor: ").append(movContaVOAntesExcluir.getValor())
                    .append(" - ").append(movContaVOAntesExcluir.getPessoaVO().getNome()).toString());
        } catch (Exception e) {
            registrarLogErroObjetoVO("MOVCONTA", movContaVOAntesExcluir.getPessoaVO().getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÂO DE PARCELA", getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public String getOnComplete() {
        return getMsgAlert();
    }

    private void gerarLog(String txtLog) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("MOVCONTA");
        log.setNomeEntidadeDescricao("MovContaVO");
        log.setNomeCampo("Movimentações");
        log.setValorCampoAlterado(txtLog);
        log.setDataAlteracao(Calendario.hoje());
        log.setResponsavelAlteracao(getUsuarioLogado().getNome().toUpperCase());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        log.setPessoa(0);
        log.setOperacao("EXCLUSÃO");
        getFacade().getLog().incluir(log);
    }

    public List<String> getEmpresasSelecionadas() {
        return empresasSelecionadas;
    }

    public void setEmpresasSelecionadas(List<String> empresasSelecionadas) {
        this.empresasSelecionadas = empresasSelecionadas;
    }

    public boolean getCheckboxTodasEmpresasSelecionado() {
        return this.checkboxTodasEmpresasSelecionado;
    }

    public void setCheckboxTodasEmpresasSelecionado(boolean checkboxTodasEmpresasSelecionado) {
        this.checkboxTodasEmpresasSelecionado = checkboxTodasEmpresasSelecionado;
    }

    public List<EmpresaVO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<EmpresaVO> empresas) {
        this.empresas = empresas;
    }

    public void selecionarTodasEmpresas() {
        if(this.checkboxTodasEmpresasSelecionado){
            for(EmpresaVO empresa: getEmpresas()){
                getEmpresasSelecionadas().add(empresa.getCodigo().toString());
            }
        } else {
            getEmpresasSelecionadas().clear();
        }
    }

    public void selecionarCheckboxEmpresa() {
        if(getListaEmpresas().size() == 1){
            for(EmpresaVO empresa: getEmpresas()){
                if(empresa.getCodigo().toString().equals(getListaEmpresas().get(0))){
                    setEmpresaRel(empresa);
                }
            }
        }
        setCheckboxTodasEmpresasSelecionado(false);
    }

    public void validarPermissaoAcessoRelatorioTodasEmpresas() throws Exception {
        setPossuiPermissaoConsultarTodasEmpresas(false);
        try {
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "ConsultarInfoTodasEmpresas", "9.47 - Consultar relatórios de todas as empresas");
                }
            }
            //chegou até aqui possui permissão;
            setPossuiPermissaoConsultarTodasEmpresas(true);
        } catch (Exception ex) {
            setPossuiPermissaoConsultarTodasEmpresas(false);
        }

    }

    public boolean isPossuiPermissaoConsultarTodasEmpresas() {
        return possuiPermissaoConsultarTodasEmpresas;
    }

    public void setPossuiPermissaoConsultarTodasEmpresas(boolean possuiPermissaoConsultarTodasEmpresas) {
        this.possuiPermissaoConsultarTodasEmpresas = possuiPermissaoConsultarTodasEmpresas;
    }

    public List<SelectItem> getListaSelectItemsEmpresa() {
        return listaSelectItemsEmpresa;
    }

    public void setListaSelectItemsEmpresa(List<SelectItem> listaSelectItemsEmpresa) {
        this.listaSelectItemsEmpresa = listaSelectItemsEmpresa;
    }

    public MovContaVO getMovContaVOExcluirDeposito() {
        if (movContaVOExcluirDeposito == null) {
            return new MovContaVO();
        }
        return movContaVOExcluirDeposito;
    }

    public void setMovContaVOExcluirDeposito(MovContaVO movContaVOExcluirDeposito) {
        this.movContaVOExcluirDeposito = movContaVOExcluirDeposito;
    }

    public MovContaRateioVO getMovContaRateioVOExcluirDeposito() {
        if (movContaRateioVOExcluirDeposito == null) {
            return new MovContaRateioVO();
        }
        return movContaRateioVOExcluirDeposito;
    }

    public void setMovContaRateioVOExcluirDeposito(MovContaRateioVO movContaRateioVOExcluirDeposito) {
        this.movContaRateioVOExcluirDeposito = movContaRateioVOExcluirDeposito;
    }

    public List<MovPagamentoVO> getListaMovPagamentoExcluirDeposito() {
        if (UteisValidacao.emptyList(listaMovPagamentoExcluirDeposito)) {
            return new ArrayList<>();
        }
        return listaMovPagamentoExcluirDeposito;
    }

    public void setListaMovPagamentoExcluirDeposito(List<MovPagamentoVO> listaMovPagamentoExcluirDeposito) {
        this.listaMovPagamentoExcluirDeposito = listaMovPagamentoExcluirDeposito;
    }

    public ListaPaginadaTO getListaMovPagamentoExcluirDepositoPaginada() {
        return listaMovPagamentoExcluirDepositoPaginada;
    }

    public void setListaMovPagamentoExcluirDepositoPaginada(ListaPaginadaTO listaMovPagamentoExcluirDepositoPaginada) {
        this.listaMovPagamentoExcluirDepositoPaginada = listaMovPagamentoExcluirDepositoPaginada;
    }

    public String getAbrirFecharModalExclusaoDeposito() {
        return abrirFecharModalExclusaoDeposito;
    }

    public void setAbrirFecharModalExclusaoDeposito(String abrirFecharModalExclusaoDeposito) {
        this.abrirFecharModalExclusaoDeposito = abrirFecharModalExclusaoDeposito;
    }

    public String getAbrirFecharModalPerguntaExclusaoDeposito() {
        return abrirFecharModalPerguntaExclusaoDeposito;
    }

    public void setAbrirFecharModalPerguntaExclusaoDeposito(String abrirFecharModalPerguntaExclusaoDeposito) {
        this.abrirFecharModalPerguntaExclusaoDeposito = abrirFecharModalPerguntaExclusaoDeposito;
    }

    public boolean isCaixaAtualDiferenteDoCaixaDoDepositoExcluir() {
        return caixaAtualDiferenteDoCaixaDoDepositoExcluir;
    }

    public void setCaixaAtualDiferenteDoCaixaDoDepositoExcluir(boolean caixaAtualDiferenteDoCaixaDoDepositoExcluir) {
        this.caixaAtualDiferenteDoCaixaDoDepositoExcluir = caixaAtualDiferenteDoCaixaDoDepositoExcluir;
    }

    public int getCodCaixaAtual() {
        return codCaixaAtual;
    }

    public void setCodCaixaAtual(int codCaixaAtual) {
        this.codCaixaAtual = codCaixaAtual;
    }

    public int getCodCaixaDoDepositoExcluir() {
        return codCaixaDoDepositoExcluir;
    }

    public void setCodCaixaDoDepositoExcluir(int codCaixaDoDepositoExcluir) {
        this.codCaixaDoDepositoExcluir = codCaixaDoDepositoExcluir;
    }
}

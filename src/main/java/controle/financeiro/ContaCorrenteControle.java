package controle.financeiro;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import java.util.Iterator;

import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.financeiro.ContaCorrente;
import negocio.comuns.utilitarias.*;
import negocio.comuns.financeiro.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;
import controle.arquitetura.security.LoginControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * contaCorrenteForm.jsp contaCorrenteCons.jsp) com as funcionalidades da classe <code>ContaCorrente</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see ContaCorrente
 * @see ContaCorrenteVO
*/
public class ContaCorrenteControle extends SuperControle {
    private ContaCorrenteVO contaCorrenteVO;
    protected List listaSelectItemBanco;
    private String msgAlert;
    /**
    * Interface <code>ContaCorrenteInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
    * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
    */

    public ContaCorrenteControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
    * Rotina responsável por disponibilizar um novo objeto da classe <code>ContaCorrente</code>
    * para edição pelo usuário da aplicação.
    */
    public String novo() {
        setContaCorrenteVO(new ContaCorrenteVO());
        inicializarListasSelectItemTodosComboBox();
        limparMsg();
        return "editar";
    }

    /**
    * Rotina responsável por disponibilizar os dados de um objeto da classe <code>ContaCorrente</code> para alteração.
    * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
    */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ContaCorrenteVO obj = getFacade().getContaCorrente().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(new Boolean(false));
            setContaCorrenteVO(obj);
            contaCorrenteVO.registrarObjetoVOAntesDaAlteracao();
            inicializarListasSelectItemTodosComboBox();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
    * Método responsável inicializar objetos relacionados a classe <code>ContaCorrenteVO</code>.
    * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
    */
    public void inicializarAtributosRelacionados(ContaCorrenteVO obj) {
        if (obj.getBanco() == null) {
            obj.setBanco(new BancoVO());
        }
    }

    /**
    * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>ContaCorrente</code>.
    * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
    * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
    */
    public String gravar() {
        try {
            if (contaCorrenteVO.isNovoObj().booleanValue()) {
                getFacade().getContaCorrente().incluir(contaCorrenteVO);
                incluirLogInclusao();
            } else {
                getFacade().getContaCorrente().alterar(contaCorrenteVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
        setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
        setErro(true);
            return "editar";
        }
    }

    /**
    * Rotina responsavel por executar as consultas disponiveis no JSP ContaCorrenteCons.jsp.
    * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
    * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
    */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getContaCorrente().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("agencia")) {
                objs = getFacade().getContaCorrente().consultarPorAgencia(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("agenciaDV")) {
                objs = getFacade().getContaCorrente().consultarPorAgenciaDV(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("contaCorrente")) {
                objs = getFacade().getContaCorrente().consultarPorContaCorrente(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("contaCorrenteDV")) {
                objs = getFacade().getContaCorrente().consultarPorContaCorrenteDV(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeBanco")) {
                objs = getFacade().getContaCorrente().consultarPorNomeBanco(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
        setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ContaCorrenteVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getContaCorrente().excluir(contaCorrenteVO);
            incluirLogExclusao();
            setContaCorrenteVO( new ContaCorrenteVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
        setErro(false);
            return "consultar";
        } catch (Exception e) {
//            setMensagemDetalhada("msg_erro", e.getMessage());
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"contacorrente\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"contacorrente\" violates foreign key")){
                setMensagemDetalhada("Esta Movimentação não pode ser excluída, pois já está vinculado à alguma empresa!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
        setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Banco</code>.
    */
    public void montarListaSelectItemBanco(String prm) throws Exception {
        List resultadoConsulta = consultarBancoPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            BancoVO obj = (BancoVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemBanco(objs);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Banco</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Banco</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
    */
    public void montarListaSelectItemBanco() {
        try {
            montarListaSelectItemBanco("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
    */
    public List consultarBancoPorNome(String nomePrm) throws Exception {
        List lista = getFacade().getBanco().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
    */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemBanco();
    }

    /**
    * Rotina responsável por preencher a combo de consulta da telas.
    */
    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("agencia", "Agência"));
        itens.add(new SelectItem("agenciaDV", "Dígito verificador da Agência"));
        itens.add(new SelectItem("contaCorrente", "Conta Corrente"));
        itens.add(new SelectItem("contaCorrenteDV", "Dígito Verificador da Conta Corrente"));
        itens.add(new SelectItem("nomeBanco", "Banco"));
        return itens;
    }

    /**
    * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
    */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
    * Operação que inicializa as Interfaces Façades com os respectivos objetos de
    * persistência dos dados no banco de dados. 
    */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemBanco() {
        return (listaSelectItemBanco);
    }
     
    public void setListaSelectItemBanco( List listaSelectItemBanco ) {
        this.listaSelectItemBanco = listaSelectItemBanco;
    }

    public ContaCorrenteVO getContaCorrenteVO() {
        return contaCorrenteVO;
    }
     
    public void setContaCorrenteVO(ContaCorrenteVO contaCorrenteVO) {
        this.contaCorrenteVO = contaCorrenteVO;
    }
 public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getContaCorrente().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
 
    public void verificarBanco() {
        try {
            BancoVO banco = getFacade().getBanco().consultarCodigo(getContaCorrenteVO().getBanco().getCodigo(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            getContaCorrenteVO().setBanco(banco);
        } catch (Exception e) {
        }
    }
    
    public boolean getApresentarCodigoOperacao(){
        try {
            return getContaCorrenteVO().getBanco().getCodigoBanco().equals(104);
        } catch (Exception e) {
            return false;
        }
    }
    
         public void incluirLogInclusao() throws Exception {
        try {
            contaCorrenteVO.setObjetoVOAntesAlteracao(new ContaCorrenteVO());
            contaCorrenteVO.setNovoObj(true);
            registrarLogObjetoVO(contaCorrenteVO, contaCorrenteVO.getCodigo(), "CONTACORRENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTACORRENTE", contaCorrenteVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CONTACORRENTE", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        contaCorrenteVO.setNovoObj(new Boolean(false));
        contaCorrenteVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            contaCorrenteVO.setObjetoVOAntesAlteracao(new ContaCorrenteVO());
            contaCorrenteVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(contaCorrenteVO, contaCorrenteVO.getCodigo(), "CONTACORRENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTACORRENTE", contaCorrenteVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CONTACORRENTE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(contaCorrenteVO, contaCorrenteVO.getCodigo(), "CONTACORRENTE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTACORRENTE", contaCorrenteVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CONTACORRENTE ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        contaCorrenteVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = contaCorrenteVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), contaCorrenteVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       contaCorrenteVO = new ContaCorrenteVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Comissão",
                "Deseja excluir a Comissão?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Classe controladora dos cadastros de tipos de conta
 * 
 * <AUTHOR>
 */
public class TipoContaControle extends SuperControle {
    // objeto do formulário de cadastro

    private TipoContaVO tipoContaVO;
    private String filtroConsulta;
    // consulta dos dados
    private List listaConsulta;
    private List<SelectItem> listaComportamento;

    public TipoContaControle() throws Exception {
        setTipoContaVO(new TipoContaVO());
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Chamado para criar uma nova conta
     *
     * @return navigation-case
     */
    public String novo() {
        setTipoContaVO(new TipoContaVO());
        tipoContaVO.setNovoObj(true);
        tipoContaVO.setObjetoVOAntesAlteracao(new TipoContaVO());
        setMensagemID("");
        return "editar";
    }

    public void abrirTipoConta() {
        try {
            validarPermissaoTipoConta();
            setMsgAlert("abrirPopup('"+request().getContextPath() + "/faces/finanTipoContaCons.jsp?modulo=financeiroWeb', 'TipoContas', 800, 595);");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

     /**
     * Valida a permissão do usuário logado para a entidade TipoConta
     * que usa a permissão "Tipo de Conta"
     * @throws Exception
     */
    public void validarPermissaoTipoConta() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "TipoConta", "9.09 - Tipo de Conta");
            }
        }
    }
    
    public List<SelectItem> getListaComportamento(){
    	if(listaComportamento == null){
    		listaComportamento = ComportamentoConta.getListaSelectItem();
    	}
    	return listaComportamento;
    }
    
    public void setListaComportamento(List<SelectItem> lista){
    	listaComportamento = lista;
    	
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getFinanceiro().getTipoConta().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao,filtro, null);
    }
    public String editar() throws Exception{
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        TipoContaVO obj = getFacade().getFinanceiro().getTipoConta().consultarPorChavePrimaria(codigoConsulta,Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        obj.setNovoObj(new Boolean(false));
        setTipoContaVO(obj);
        tipoContaVO.setNovoObj(false);
        tipoContaVO.registrarObjetoVOAntesDaAlteracao();
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "editar";
    }

    // /CONSULTA PAGINADA
    public String consultar() {
        try {
            super.consultar();
            List<TipoContaVO> objs = new ArrayList<TipoContaVO>();
            tipoContaVO.setDescricao(getControleConsulta().getValorConsulta());
            objs = getFacade().getFinanceiro().getTipoConta().consultar(tipoContaVO);
            setListaConsulta(objs);
            setMensagemID("");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Grava a conta
     *
     * @return
     */
    public String gravar() {
        try {
            if(UteisValidacao.emptyNumber(tipoContaVO.getCodigoComportamento())){
                throw new Exception("Escolha um comportamento para o tipo de conta");
            }
            if (tipoContaVO.isNovoObj().booleanValue()) {
                getFacade().getFinanceiro().getTipoConta().incluir(tipoContaVO);
                inicializarLog();
            } else {
                getFacade().getFinanceiro().getTipoConta().alterar(tipoContaVO);
                incluirLogAlteracao();
            }
          //  consultar();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Acionado na tela de edição, exclui a conta
     *
     * @return navigation-case
     */
    public String excluir() {
        try {
            getFacade().getFinanceiro().getTipoConta().excluir(tipoContaVO);
            incluirLogExclusao();
            setTipoContaVO(new TipoContaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    // GETTERS AND SETTERS
    public TipoContaVO getTipoContaVO() {
        return tipoContaVO;
    }

    public void setTipoContaVO(TipoContaVO tipoContaVO) {
        this.tipoContaVO = tipoContaVO;
    }

    public List getListaConsulta() {
        return listaConsulta;
    }

    public void setListaConsulta(List listaConsulta) {
        this.listaConsulta = listaConsulta;
    }

    public void realizarConsultaLogObjetoGeral() {
        tipoContaVO = new TipoContaVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = "TIPOCONTA";
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(),
                getTipoContaVO().getCodigo(), 0);
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(tipoContaVO, tipoContaVO.getCodigo(), "TIOPOCONTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CONTA", tipoContaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE TIPO DE CONTA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoContaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void inicializarLog() throws Exception{
        try {
            tipoContaVO.setNovoObj(true);
            registrarLogObjetoVO(tipoContaVO, tipoContaVO.getCodigo(), "CONTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOCONTA", tipoContaVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE TIPO DE CONTA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoContaVO.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            tipoContaVO.setObjetoVOAntesAlteracao(new TipoContaVO());
            tipoContaVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(tipoContaVO, tipoContaVO.getCodigo(), "TIPOCONTA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPOCONTA", tipoContaVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TIPO DE TURMA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
}

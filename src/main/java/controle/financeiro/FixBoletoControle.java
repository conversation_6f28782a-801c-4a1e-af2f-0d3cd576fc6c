/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.RetornoRemessaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.santander.LayoutRemessaSantanderCNAB400;

import javax.faces.event.ActionEvent;
import java.io.File;
import java.util.ArrayList;
import java.util.List;


public class FixBoletoControle extends SuperControle {

    private static final String CONSTANTE_PARTIDA = "PARTIDA";
    private static final String CONSTANTE_DESTINO = "DESTINO";

    private Boolean usarCodigoPartida = true;
    private Integer codigoPartida;
    private RemessaItemVO itemPartida;

    private Boolean usarCodigoDestino = true;
    private Integer codigoDestino;
    private RemessaItemVO itemDestino;

    private List<RetornoRemessaVO> retornos;

    private String codigosEmMassa;

    public FixBoletoControle() throws Exception {

    }

    public void limpar() {
        setCodigoDestino(0);
        setItemDestino(new RemessaItemVO());
        setCodigoPartida(0);
        setItemPartida(new RemessaItemVO());
    }

    public void consultarItem(ActionEvent event) {
        try {
            String param = event.getComponent().getAttributes().get("tipoItem").toString();
            if (CONSTANTE_PARTIDA.equals(param)) {
                itemPartida = consultarBoleto(getCodigoPartida(), getUsarCodigoPartida());
            } else if (CONSTANTE_DESTINO.equals(param)) {
                itemDestino = consultarBoleto(getCodigoDestino(), getUsarCodigoDestino());
            } else {
                throw new Exception("Problemas ao consultar Item");
            }
            montarSucesso("Dados consultados com sucesso");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private RemessaItemVO consultarBoleto(Integer codigo, boolean usarCodigo) throws Exception {
        if (usarCodigo) {
            return getFacade().getRemessaItem().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
        } else {
            return getFacade().getRemessaItem().consultarPorIdentificador(codigo, Uteis.NIVELMONTARDADOS_TODOS);
        }
    }

    public void realizarEstornoPagamento() throws Exception {
        try {
            estornarItem(itemPartida);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void estornarItem(RemessaItemVO item) throws Exception {
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setCodigo(1);

        item.getProps().remove(DCCAttEnum.StatusVenda.name());
        getFacade().getRemessaItem().estornarPagamentoBoleto(item, getKey(), usuarioVO);
        montarSucesso("Estorno realizado com sucesso!");
    }

    public void estornarEmMassa() throws Exception {
        StringBuilder sbErros = new StringBuilder();
        if (!UteisValidacao.emptyString(getCodigosEmMassa())) {
            String[] codigos = getCodigosEmMassa().split(",");
            for (String codigo : codigos) {
                try {
                    RemessaItemVO itemEstornar = consultarBoleto(Integer.parseInt(codigo), true);
                    estornarItem(itemEstornar);
                } catch (Exception ex) {
                    sbErros.append(ex.getMessage()).append("<br/>");
                }
            }
        }
        montarErro(sbErros.toString());
    }

    public void alterarArquivoRetorno() {
        try {
            if (UteisValidacao.emptyList(getRetornos())) {
                setRetornos(getFacade().getRetornoRemessa().consultarTodos());
            }

            RemessaVO remessaVO = new RemessaVO();
            remessaVO.setConvenioCobranca(itemDestino.getRemessa().getConvenioCobranca());

            FOR_RETORNOS:
            for (RetornoRemessaVO retornoRemessaVO : getRetornos()) {
                remessaVO.setNomeArquivoDownload(retornoRemessaVO.getNomeArquivo());
                remessaVO.setRetorno(new StringBuilder(retornoRemessaVO.getArquivoRetorno().trim()));

                try {
                    LayoutRemessaSantanderCNAB400.lerRetorno(remessaVO);
                } catch (Exception ex) {
                    ex.getStackTrace();
                    continue;
                }

                String codigoRetorno = remessaVO.getHeaderRetorno().getValue(DCCAttEnum.NumAvisoBancario.name());
                if (codigoRetorno.equals("")) {
                    codigoRetorno = remessaVO.getHeaderRetorno().getValue(DCCAttEnum.DataGeracao.name());
                }
                if (itemPartida.processouRetorno(codigoRetorno)) {
                    boolean encontrou = false;
                    for (RegistroRemessa regRet : remessaVO.getDetailsRetorno()) {
                        String nossoNumeroTmp = regRet.getValue(DCCAttEnum.NossoNumero.name()).trim();
                        if (nossoNumeroTmp.length() > 3) {
                            nossoNumeroTmp = nossoNumeroTmp.substring(3);
                        }
                        Integer nossoNumero = Integer.valueOf(nossoNumeroTmp);
                        if (getCodigoPartida().equals(nossoNumero)) {
                            String statusVenda = regRet.getValue(DCCAttEnum.StatusVenda.name());
                            if (itemPartida.isLiquidado(statusVenda)) {
                                if (itemDestino.getRemessa().getConvenioCobranca().isUsarIdentificador()) {
                                    regRet.set(DCCAttEnum.NossoNumero, itemDestino.getIdentificador().toString());
                                    encontrou = true;
                                } else {
                                    regRet.set(DCCAttEnum.NossoNumero, itemDestino.getCodigo().toString());
                                    encontrou = true;
                                }
                                break;
                            }
                        }
                    }
                    if (encontrou) {
                        break;
                    }
                }
            }

            StringBuilder sb = new StringBuilder();
            sb.append(remessaVO.getHeaderRetorno().toStringBuffer()).append("\r\n");
            List<RegistroRemessa> lista = remessaVO.getDetailsRetorno();
            StringBuilder sbDetail = new StringBuilder();
            for (RegistroRemessa regD : lista) {
                sbDetail.append(regD.toStringBuffer()).append("\r\n");
            }
            sb.append(sbDetail);
            sb.append(remessaVO.getTrailerRetorno().toStringBuffer());

            String path = this.getServletContext().getRealPath("relatorio") + File.separator + remessaVO.getNomeArquivoDownload();
            StringUtilities.saveToFile(sb, path, "UTF-8");

            setMsgAlertAuxiliar("abrirPopup('UpdateServlet?op=downloadfile&file=" + remessaVO.getNomeArquivoDownload() + "&mimetype=txt','FixBoleto', 640,480);");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void limparRetornos() {
        setRetornos(new ArrayList<RetornoRemessaVO>());
    }

    public void downloadNovoArquivoRetorno() {

    }

    public void realizarBaixaNovoItem() {

    }


    public Integer getCodigoPartida() {
        if (codigoPartida == null) {
            codigoPartida = 0;
        }
        return codigoPartida;
    }

    public void setCodigoPartida(Integer codigoPartida) {
        this.codigoPartida = codigoPartida;
    }

    public RemessaItemVO getItemPartida() {
        if (itemPartida == null) {
            itemPartida = new RemessaItemVO();
        }
        return itemPartida;
    }

    public void setItemPartida(RemessaItemVO itemPartida) {
        this.itemPartida = itemPartida;
    }

    public Integer getCodigoDestino() {
        if (codigoDestino == null) {
            codigoDestino = 0;
        }
        return codigoDestino;
    }

    public void setCodigoDestino(Integer codigoDestino) {
        this.codigoDestino = codigoDestino;
    }

    public RemessaItemVO getItemDestino() {
        if (itemDestino == null) {
            itemDestino = new RemessaItemVO();
        }
        return itemDestino;
    }

    public void setItemDestino(RemessaItemVO itemDestino) {
        this.itemDestino = itemDestino;
    }

    public Boolean getUsarCodigoPartida() {
        return usarCodigoPartida;
    }

    public void setUsarCodigoPartida(Boolean usarCodigoPartida) {
        this.usarCodigoPartida = usarCodigoPartida;
    }

    public Boolean getUsarCodigoDestino() {
        return usarCodigoDestino;
    }

    public void setUsarCodigoDestino(Boolean usarCodigoDestino) {
        this.usarCodigoDestino = usarCodigoDestino;
    }

    public List<RetornoRemessaVO> getRetornos() {
        return retornos;
    }

    public void setRetornos(List<RetornoRemessaVO> retornos) {
        this.retornos = retornos;
    }

    public String getCodigosEmMassa() {
        if (codigosEmMassa == null) {
            codigosEmMassa = "";
        }

        return codigosEmMassa;
    }

    public void setCodigosEmMassa(String codigosEmMassa) {
        this.codigosEmMassa = codigosEmMassa;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import annotations.arquitetura.ChaveEstrangeira;
import br.com.pactosolucoes.enumeradores.SituacaoEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.financeiro.FreePassVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @see FreePassVO
 */
public class FreePassControle extends SuperControle {

    private FreePassVO freePassVO;
    @ChaveEstrangeira
    protected ClienteVO clienteVO;
    protected List<FreePassVO> listaSelectItemProdutoFreePass;
    protected List<ClienteVO> listaConsultaDeCliente;
    //protected List<EmpresaVO>listaSelectItemEmpresa;
    protected String campoConsultarCliente;
    protected String valorConsultaCliente;
    protected Boolean abrirRichModalDeConfirmacao;
    protected Boolean apresentarBotoes;
    private boolean deletarFreePass = false;
    private Date dataInicio;
    private Date dataFim;
    private PeriodoAcessoClienteVO periodoAtual;
    private String msgFreePassExistente = "";
    private MensagemGenericaControle mensagemGenericaControle = new MensagemGenericaControle();

    public FreePassControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
    }

    @Override
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            getFreePassVO().setUsuarioResponsavel(new UsuarioVO());
            getFreePassVO().setUsuarioResponsavel(getUsuarioLogado());
            getFreePassVO().getUsuarioResponsavel().setCodigo(getUsuarioLogado().getCodigo());
            getFreePassVO().getUsuarioResponsavel().setUsername(getUsuarioLogado().getUsername());
        } catch (Exception e) {
            throw e;
        }
    }

    public String novoTela() {
        String retorno = "";
        try {
            retorno = novo();
        } catch (Exception e) {
            montarErro(e);
        }
        return retorno;
    }

    public String novo() throws Exception {
        setClienteVO(new ClienteVO());
        setFreePassVO(new FreePassVO());
        setCampoConsultarCliente("");
        setValorConsultaCliente("");
        setListaConsultaDeCliente(new ArrayList<ClienteVO>());
        setListaSelectItemProdutoFreePass(new ArrayList<FreePassVO>());
        inicializarFacades();
        inicializarListasSelectItemTodosComboBoxFreePass();
        inicializarUsuarioLogado();
        setDataInicio(Calendario.hoje());
        setAbrirRichModalDeConfirmacao(false);
        setApresentarBotoes(true);
//        setSucesso(false);
//        setErro(false);
//        setMensagemID("msg_entre_dados");
        return "freePass";
    }

    public void inicializarListasSelectItemTodosComboBoxFreePass() {
        montarSelectItemProdutoFreePass();
    }

    public void montarSelectItemProdutoFreePass() {
        try {
            montarSelectItemProdutoFreePass("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarSelectItemProdutoFreePass(String prm) throws Exception {
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        List resultadoConsulta = consultarProdutoFreePass(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            ProdutoVO obj = (ProdutoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString() + " - " + obj.getNrDiasVigencia() + " Dias"));
        }
        setListaSelectItemProdutoFreePass(objs);
    }

    public List consultarProdutoFreePass(String nomePrm) throws Exception {
        List lista = getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(nomePrm, "FR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return lista;
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        List lista = new Empresa().consultarPorNome(nomePrm, true, true, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public List getTipoConsultaCombo() throws Exception {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("matricula", "Matricula"));
        return itens;
    }

    public String consultarCliente() {
        try {
            List objs = new ArrayList();
            if (getCampoConsultarCliente().equals("codigo")) {
                if (getValorConsultaCliente().equals("")) {
                    setValorConsultaCliente("0");
                }
                int valorInt = Integer.parseInt(getValorConsultaCliente());
                objs = getFacade().getCliente().consultarPorCodigo(new Integer(valorInt), getEmpresaLogado().getCodigo().intValue(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (getCampoConsultarCliente().equals("nome")) {
                objs = getFacade().getCliente().consultarPorNomePessoa(getValorConsultaCliente(), getEmpresaLogado().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, null);
            }
            if (getCampoConsultarCliente().equals("matricula")) {
                if (valorConsultaCliente.equals("")) {
                    setValorConsultaCliente("0");
                } else {
                    objs = getFacade().getCliente().consultarPorMatricula(getValorConsultaCliente(), getEmpresaLogado().getCodigo().intValue(), getFreePassVO().getClienteVO().getCodigo(), null, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
            }
            setListaConsultaDeCliente(objs);
            setMensagemDetalhada("");
            return "";
        } catch (Exception e) {
            setListaConsultaDeCliente(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    public void selecionarCliente() throws Exception {
        ClienteVO obj = (ClienteVO) context().getExternalContext().getRequestMap().get("cliente");
        obj = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        getFreePassVO().setClienteVO(obj);
        getFreePassVO().setClienteFreePass(obj.getPessoa().getNome());

        boolean permitelancarFreePass = UteisValidacao.emptyNumber(getFreePassVO().getClienteVO().getFreePass().getCodigo());
        if (!permitelancarFreePass) {
            PeriodoAcessoClienteVO periodoFreePass = getFacade().getPeriodoAcessoCliente().consultarUltimoPorDataPessoaTipoAcesso(Calendario.hoje(), getFreePassVO().getClienteVO().getPessoa().getCodigo(),
                    SituacaoEnum.FREE_PASS.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);

            if (periodoFreePass != null) {
                setPeriodoAtual(periodoFreePass);
                setMsgFreePassExistente("Existe um Free Pass para este aluno para o período de " + Uteis.getData(getPeriodoAtual().getDataInicioAcesso()) + " até " + Uteis.getData(getPeriodoAtual().getDataFinalAcesso()) + ". É necessário excluir esse Free Pass para lançar outro.");
            } else {
                getFacade().getCliente().removerFreePass(obj);
                getFreePassVO().getClienteVO().setFreePass(new ProdutoVO());
                permitelancarFreePass = true;
            }
        }

        if (permitelancarFreePass) {
            setPeriodoAtual(new PeriodoAcessoClienteVO());
            setMsgFreePassExistente("");
        }
        setMensagemDetalhada("", "");
        setMensagemID("");
        setValorConsultaCliente(null);
        setCampoConsultarCliente(null);
        setErro(false);
        setSucesso(true);
    }

    public void limparCliente() {
        getFreePassVO().setClienteVO(new ClienteVO());
        getFreePassVO().setClienteFreePass("");
    }

    public void limparProduto() {
        getFreePassVO().setProdutoFreePass(new ProdutoVO());
    }

    public void validarDadosFreePass() throws ConsistirException {
        try {
            setDeletarFreePass(false);
            FreePassVO.validarDados(freePassVO);
            setSucesso(false);
            setErro(false);
            setAbrirRichModalDeConfirmacao(true);
        } catch (Exception e) {
            setAbrirRichModalDeConfirmacao(false);
//          setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            montarErro(e.getMessage());
        }
    }

    public void getAbrirRichModalConfirmacaoFreePass() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                validacoesFreepass(auto.getUsuario());
                setExecutarAoCompletar(getMensagemNotificar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                montarAviso(e.getMessage());
                mensagemGenericaControle.setOnCompleteBotaoNao(getMensagemNotificar());
                setApresentarBotoes(true);
            }

            @Override
            public void onFecharModalAutorizacao() {
                limparMsg();
                auto.setRenderComponents("painelAutorizacaoUsuario");
            }

        };

        limparMsg();
        try {
            validarDadosFreePass();
            if (getAbrirRichModalDeConfirmacao()) {
                auto.autorizar("Confirmação do FreePass", "PermissaoFreePass",
                        "Você precisa da permissão \"2.51 - Permissão para lançar Free Pass\"",
                        "form:panelGeral,form:panelCaixaAberto,form:panelBotoesControle, form:totalLancado,botaoLogFreepass", listener);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void getAbrirRichModalConfirmacaoExclusaoFreePass() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                if (isDeletarFreePass()) {
                    deletar();
                }
                setExecutarAoCompletar(getMensagemNotificar());
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                montarAviso(e.getMessage());
                mensagemGenericaControle.setOnCompleteBotaoNao(getMensagemNotificar());
                setApresentarBotoes(true);
            }

            @Override
            public void onFecharModalAutorizacao() {
                limparMsg();
                auto.setRenderComponents("painelAutorizacaoUsuario");
            }

        };

        limparMsg();
        try {
            validarExclusaoFreePass();
            if (getAbrirRichModalDeConfirmacao()) {
                auto.autorizar(
                        "Confirmação do FreePass", "PermissaoFreePass",
                        "Você precisa da permissão \"2.51 - Permissão para lançar Free Pass\"",
                        "form:panelGeral,form:panelCaixaAberto,form:panelBotoesControle, form:totalLancado,form:panelDadosProdutosPeriodo", listener
                );
            }

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void validacoesFreepass(UsuarioVO usuarioVO) throws Exception {
        carregarDadosCliente(false);
        if (Calendario.maior(Calendario.hoje(), dataInicio)) {
            throw new Exception("A data de início do FreePass não pode ser retroativa.");
        }

        ContratoVO contrato = getFacade().getContrato().consultarContratoVigentePorPessoa(getFreePassVO().getClienteVO().getPessoa().getCodigo(), false, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyNumber(contrato.getCodigo()) && !contrato.getSituacao().equals("CA")) {
            throw new Exception("Não é possível lançar FreePass com contrato vigente.");
        }

        dataInicio = dataInicio == null ? Calendario.hoje() : dataInicio;
        dataFim = Uteis.somarDias(dataInicio, (freePassVO.getProdutoFreePass().getNrDiasVigencia() - 1));
        Boolean possuiFreePass = getFacade().getPeriodoAcessoCliente().possuiPeriodoAcesso(getFreePassVO().getClienteVO().getPessoa().getCodigo(), "PL", dataInicio, dataFim, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (possuiFreePass) {
            throw new Exception("Já existe atualmente um FreePass para o cliente nesse período.");
        }

        gravar(usuarioVO);
    }

    public void verificarExisteContratoVigente() throws Exception {


    }

    public void consultarResponsavel() {
        try {
            freePassVO.setUsuarioResponsavel(new Usuario().consultarPorChavePrimaria(freePassVO.getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String editar() {
        return "";
    }

    public void gravar(UsuarioVO usuarioVO) throws Exception {
        MensagemGenericaControle mensagemGenericaControle = getControlador(MensagemGenericaControle.class);
        getFreePassVO().getClienteVO().setResponsavelFreePass(usuarioVO.getCodigo());
        getFreePassVO().getClienteVO().setFreePass(getFreePassVO().getProdutoFreePass());

        getFacade().getCliente().incluirFreePass(getFreePassVO().getClienteVO(), dataInicio, null, null, null);
        gravarPontosCliente();
        montarSucessoGrowl("Free Pass lançado com Sucesso!");
        mensagemGenericaControle.setOnCompleteBotaoSim(getMensagemNotificar());
        incluirLogInclusao();

        getFacade().getZWFacade().atualizarSintetico(getFreePassVO().getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
    }

    public void lancaFreepass(UsuarioVO usuarioVO)  throws Exception {

        dataInicio = dataInicio == null ? Calendario.hoje() : dataInicio;
        dataFim = Uteis.somarDias(dataInicio, (freePassVO.getProdutoFreePass().getNrDiasVigencia() - 1));
        Boolean possuiFreePass = getFacade().getPeriodoAcessoCliente().possuiPeriodoAcesso(getFreePassVO().getClienteVO().getPessoa().getCodigo(), "PL", dataInicio, dataFim, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (possuiFreePass) {
            throw new Exception("Já existe atualmente um FreePass para o cliente nesse período.");
        }

        getFreePassVO().getClienteVO().setResponsavelFreePass(usuarioVO.getCodigo());
        getFreePassVO().getClienteVO().setFreePass(getFreePassVO().getProdutoFreePass());
        getFacade().getCliente().incluirFreePass(getFreePassVO().getClienteVO(), dataInicio, null, null, null);
        gravarPontosCliente();
        incluirLogInclusao();

        getFacade().getZWFacade().atualizarSintetico(getFreePassVO().getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
    }

    //Tem um método similar no FreePassControle
    //Se alterar algo aqui, precisa validar para alterar lá também
    private void gravarPontosCliente() throws Exception {
        CampanhaDuracaoVO maiorCampanhaAtiva = getFacade().getCampanhaDuracao().campanhaVigenteMultiplicador(Calendario.hoje(), TipoItemCampanhaEnum.PRODUTO, freePassVO.getEmpresaVO().getCodigo());
        if (getEmpresaLogado().isTrabalharComPontuacao() && getFreePassVO().getProdutoFreePass().getPontos() > 0 &&
                (!freePassVO.getEmpresaVO().isPontuarApenasCategoriasEmCampanhasAtivas() ||
                        (freePassVO.getEmpresaVO().isPontuarApenasCategoriasEmCampanhasAtivas() && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())))) {
            HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
            historicoPontos.setCliente(getFreePassVO().getClienteVO());
            historicoPontos.setDataConfirmacao(Calendario.hoje());
            historicoPontos.setDataaula(Calendario.hoje());
            historicoPontos.setEntrada(true);
            Integer pontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * freePassVO.getProdutoFreePass().getPontos() : freePassVO.getProdutoFreePass().getPontos());
            historicoPontos.setPontos(pontos);
            historicoPontos.setDescricao("Lançamento (FreePass) - " + getFreePassVO().getProdutoFreePass().getDescricao() + maiorCampanhaAtiva.getTextoCampanhaApresentar());
            historicoPontos.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
            historicoPontos.setTipoPonto(TipoItemCampanhaEnum.PRODUTO);
            historicoPontos.setCodigoVenda(getFreePassVO().getCodigo());
            historicoPontos.setProduto(getFreePassVO().getProdutoFreePass().getCodigo());
            getFacade().getHistoricoPontos().incluir(historicoPontos);
        }
    }


    public String excluir() {
        return "";
    }

    public void deletar() throws Exception {
        MensagemGenericaControle mensagemGenericaControle = getControlador(MensagemGenericaControle.class);

        getFacade().getCliente().alterarDadoFreePass(getFreePassVO().getClienteVO());
        getFacade().getPeriodoAcessoCliente().excluir(getPeriodoAtual());
        getFacade().getConvite().excluir(getFreePassVO());

        incluirLogExclusao();
        setPeriodoAtual(new PeriodoAcessoClienteVO());
        getFreePassVO().setClienteVO(getFacade().getCliente().consultarPorChavePrimaria(getFreePassVO().getClienteVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        montarSucessoGrowl("Free Pass excluído com Sucesso!");
        mensagemGenericaControle.setOnCompleteBotaoSim(getMensagemNotificar());

        getFacade().getZWFacade().atualizarSintetico(getFreePassVO().getClienteVO(), Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public FreePassVO getFreePassVO() {
        return freePassVO;
    }

    public void setFreePassVO(FreePassVO freePassVO) {
        this.freePassVO = freePassVO;
    }

    public List<ClienteVO> getListaConsultaDeCliente() {
        return listaConsultaDeCliente;
    }

    public void setListaConsultaDeCliente(List<ClienteVO> listaConsultaDeCliente) {
        this.listaConsultaDeCliente = listaConsultaDeCliente;
    }

    public List<FreePassVO> getListaSelectItemProdutoFreePass() {
        return listaSelectItemProdutoFreePass;
    }

    public void setListaSelectItemProdutoFreePass(List<FreePassVO> listaSelectItemProdutoFreePass) {
        this.listaSelectItemProdutoFreePass = listaSelectItemProdutoFreePass;
    }

    public String getCampoConsultarCliente() {
        if (campoConsultarCliente == null) {
            campoConsultarCliente = "";
        }
        return (campoConsultarCliente);
    }

    public void setCampoConsultarCliente(String campoConsultarCliente) {
        this.campoConsultarCliente = campoConsultarCliente;
    }

    public String getValorConsultaCliente() {
        if (valorConsultaCliente == null) {
            valorConsultaCliente = "";
        }
        return (valorConsultaCliente);
    }

    public void setValorConsultaCliente(String valorConsultaCliente) {
        this.valorConsultaCliente = valorConsultaCliente;
    }

    //    public List<EmpresaVO> getListaSelectItemEmpresa() {
//        return listaSelectItemEmpresa;
//    }
//
//    public void setListaSelectItemEmpresa(List<EmpresaVO> listaSelectItemEmpresa) {
//        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
//    }
    public Boolean getAbrirRichModalDeConfirmacao() {
        return abrirRichModalDeConfirmacao;
    }

    public void setAbrirRichModalDeConfirmacao(Boolean abrirRichModalDeConfirmacao) {
        this.abrirRichModalDeConfirmacao = abrirRichModalDeConfirmacao;
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        // listaSelectItemEmpresa.clear();
        listaConsultaDeCliente.clear();
        listaSelectItemProdutoFreePass.clear();
        abrirRichModalDeConfirmacao = null;
        campoConsultarCliente = null;
        clienteVO = null;
        freePassVO = null;
        valorConsultaCliente = null;
    }


    public void setDeletarFreePass(boolean deletarFreePass) {
        this.deletarFreePass = deletarFreePass;
    }

    public boolean isDeletarFreePass() {
        return deletarFreePass;
    }

    public void validarExclusaoFreePass() {
        try {
            inicializarUsuarioLogado();
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("", "");
            setSucesso(false);
            setErro(false);
            setAbrirRichModalDeConfirmacao(true);
            setDeletarFreePass(true);
        } catch (Exception e) {
            setDeletarFreePass(false);
            setAbrirRichModalDeConfirmacao(false);
            montarAviso(e.getMessage());
            setSucesso(false);
            setErro(true);
            mensagemGenericaControle.setOnCompleteBotaoNao(getMensagemNotificar());
        }
    }

    //Tem um método similar no VendasOnlineService
    //Se alterar algo aqui, precisa validar para alterar lá também
    public void incluirLogInclusao() throws Exception {
        try {
            LogVO obj = new LogVO();
            String nomeUsuario;
            obj.setPessoa(freePassVO.getClienteVO().getPessoa().getCodigo());
            obj.setNomeEntidade("FREEPASS" + "");
            obj.setChavePrimaria(freePassVO.getClienteVO().getPessoa().getCodigo().toString());
            obj.setNomeEntidadeDescricao("FreePass");
            obj.setOperacao("INCLUSÃO " + freePassVO.getUsuarioResponsavel().getNome());
            try{
                nomeUsuario = getUsuarioLogado().getNome();
            }
            catch (Exception e){
                nomeUsuario = freePassVO.getUsuarioResponsavel().getNome();
            }
            obj.setResponsavelAlteracao(nomeUsuario);
            obj.setUserOAMD(freePassVO.getUsuarioResponsavel().getUserOamd());
            obj.setNomeCampo("FREEPASS");
            obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "\n\rNome do Cliente = " + freePassVO.getClienteVO().getPessoa().getNome() + "\n\rData do inicio do FreePass = " + (Uteis.getData(getDataInicio())) + "\n\rData Final do FreePass= " + (Uteis.getData(dataFim)) + "\n\rProduto = " + freePassVO.getProdutoFreePass().getDescricao() + "\n\r");
            getFacade().getLog().incluirSemCommit(obj);
        } catch (Exception e) {
            registrarLogErroObjetoVO("FREEPASS", freePassVO.getClienteVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE FREEPASS", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
        }

    }

    public void incluirLogExclusao() throws Exception {
        LogVO obj = new LogVO();
        obj.setPessoa(freePassVO.getClienteVO().getPessoa().getCodigo());
        obj.setNomeEntidade("FREEPASS" + "");
        obj.setChavePrimaria(freePassVO.getClienteVO().getPessoa().getCodigo().toString());
        obj.setNomeEntidadeDescricao("FreePass");
        obj.setOperacao("EXCLUSÃO " + freePassVO.getUsuarioResponsavel().getNome());
        obj.setResponsavelAlteracao(getUsuarioLogado().getNome());

        obj.setUserOAMD(freePassVO.getUsuarioResponsavel().getUserOamd());
        //
        obj.setNomeCampo("FREEPASS");
        obj.setValorCampoAlterado(obj.getValorCampoAlterado() + "Nome do Cliente = " + freePassVO.getClienteVO().getPessoa().getNome() + "\n\rData do inicio do FreePass = " + Uteis.getData(getPeriodoAtual().getDataInicioAcesso()) + "\n\rData Final do FreePass= " + (Uteis.getData(getPeriodoAtual().getDataFinalAcesso())));
        getFacade().getLog().incluirSemCommit(obj);

    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("FreePassVO");
        loginControle.consultarLogObjetoSelecionado("FREEPASS", freePassVO.getCodigo(), null);
    }


    public void realizarConsultaLogObjetoGeral() {
        freePassVO = new FreePassVO();
        realizarConsultaLogObjetoSelecionado();
    }


    public List<ClienteVO> executarAutocompleteConsultaCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result;
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarTodosClienteComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeClienteComLimite(getEmpresaLogado().getCodigo(), pref, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
            montarErro(ex.getMessage());
        }
        return result;
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        carregarDadosCliente(true);
    }

    public void carregarDadosCliente(boolean suggestionBox) throws Exception {
        try {
            setMsgAlert("");
            ClienteVO obj = suggestionBox ? (ClienteVO) request().getAttribute("result") : getFreePassVO().getClienteVO();
            obj = getFacade().getCliente().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            getFreePassVO().setClienteVO(obj);
            getFreePassVO().setClienteFreePass(obj.getPessoa().getNome());

            boolean permitelancarFreePass = UteisValidacao.emptyNumber(getFreePassVO().getClienteVO().getFreePass().getCodigo());
            if (!permitelancarFreePass) {
                PeriodoAcessoClienteVO periodoFreePass = getFacade().getPeriodoAcessoCliente().consultarUltimoPorDataPessoaTipoAcesso(Calendario.hoje(), getFreePassVO().getClienteVO().getPessoa().getCodigo(),
                        SituacaoEnum.FREE_PASS.getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);

                if (periodoFreePass != null) {
                    setPeriodoAtual(periodoFreePass);
                    setMsgFreePassExistente("Existe um Free Pass para este aluno para o período de " + Uteis.getData(getPeriodoAtual().getDataInicioAcesso()) + " até " + Uteis.getData(getPeriodoAtual().getDataFinalAcesso()) + ". É necessário excluir esse Free Pass para lançar outro.");
                } else {
                    getFacade().getCliente().removerFreePass(obj);
                    getFreePassVO().getClienteVO().setFreePass(new ProdutoVO());
                    permitelancarFreePass = true;
                }
            }

            if (permitelancarFreePass) {
                setPeriodoAtual(new PeriodoAcessoClienteVO());
                setMsgFreePassExistente("");
            }
            setMensagemDetalhada("", "");
            setMensagemID("");
            setValorConsultaCliente(null);
            setCampoConsultarCliente(null);
            setErro(false);
            setSucesso(true);
        } catch (Exception e) {
            montarErro("Não foi possivel obter o cliente. Tente novamente!");
            setMsgAlert(getMensagemNotificar());

        }
    }


    public List<ProdutoVO> executarAutocompleteConsultaProduto(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ProdutoVO> resultProduto;
        try {
            if (pref.equals("%")) {
                resultProduto = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo("", "FR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                resultProduto = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(pref, "FR", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            setMensagemID("");
        } catch (Exception ex) {
            resultProduto = (new ArrayList<ProdutoVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return resultProduto;
    }

    public void selecionarProdutoSuggestionBox() throws Exception {
        ProdutoVO obj = (ProdutoVO) request().getAttribute("resultProduto");
        obj = getFacade().getProduto().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        getFreePassVO().setProdutoFreePass(obj);
        setMensagemDetalhada("", "");
        setMensagemID("");
        setValorConsultaCliente(null);
        setCampoConsultarCliente(null);
        setErro(false);
        setSucesso(true);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public PeriodoAcessoClienteVO getPeriodoAtual() {
        return periodoAtual;
    }

    public void setPeriodoAtual(PeriodoAcessoClienteVO periodoAtual) {
        this.periodoAtual = periodoAtual;
    }

    public String getMsgFreePassExistente() {
        return msgFreePassExistente;
    }

    public void setMsgFreePassExistente(String msgFreePassExistente) {
        this.msgFreePassExistente = msgFreePassExistente;
    }

}

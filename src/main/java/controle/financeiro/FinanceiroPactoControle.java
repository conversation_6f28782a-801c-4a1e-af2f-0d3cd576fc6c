package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import cfin.wrapper.ParcelaEmAberto;
import cfin.wrapper.TResultadoBoleto;
import cfin.wrapper.TResultadoParcelaConsultada;
import cfin.wrapper.TResultadoParcelasEmAberto;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.FinanceiroPacto;
import negocio.facade.jdbc.utilitarias.ConnectionSerializable;
import ws.TParcelaEmAberto;
import ws.TResultadoTransacaoWS;

import javax.faces.event.ActionEvent;
import java.util.Calendar;

/**
 * Created by glauco on 20/09/2014
 */
public class FinanceiroPactoControle extends SuperControle {

    private transient TResultadoParcelasEmAberto parcelas;
    private transient FinanceiroPacto financeiroPacto;

    public FinanceiroPactoControle() throws Exception {
        this.financeiroPacto = new FinanceiroPacto(((ConnectionSerializable) JSFUtilities.getFromSession("con")).getCon());
        obterParcelasEmAberto();
    }

    public TResultadoParcelasEmAberto getParcelas() {
        return parcelas;
    }

    public void setParcelas(TResultadoParcelasEmAberto parcelas) {
        this.parcelas = parcelas;
    }

    public void limparMensagens() {
        setMensagemDetalhada("", "");
    }

    public void downloadBoleto(ActionEvent actionEvent) {
        limparMensagens();
        setErro(false);
        setMensagemDetalhada("", "");
        try {
            ParcelaEmAberto obj = (ParcelaEmAberto) context().getExternalContext().getRequestMap().get("parcela");

            TResultadoBoleto resultadoBoleto = getFinanceiroPacto().regerarParcela(obj.getCodigoParcela(), isPermiteGerarBoleto(obj));

            String nomeArquivo = "";
            if (resultadoBoleto.getResultado().equals(TResultadoTransacaoWS.rtOK)) {
                obterParcelasEmAberto();
                if (resultadoBoleto.getNomeArquivo().contains("\\")) {
                    nomeArquivo = resultadoBoleto.getNomeArquivo().split("boleto\\\\")[1];
                } else {
                    nomeArquivo = resultadoBoleto.getNomeArquivo();
                }
            }

            if (!nomeArquivo.equals("")) {
                String urlArquivo = Uteis.getUrlBoletosFinanceiroPacto() + nomeArquivo;
                setMsgAlert("abrirPopup('" + urlArquivo + "', 'Boleto', 1000, 670);");
                setSucesso(true);
                String msgDetalhada = "";
                if (!isPermiteGerarBoleto(obj)) {
                    msgDetalhada = "O boleto impresso não foi gerado agora!";
                }
                setMensagemDetalhada("msg_dados_consultados", msgDetalhada);
            } else {
                throw new ConsistirException("Arquivo não encontrado");
            }
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
    }


    public String obterParcelasEmAberto() {
        try {
            limparMensagens();
            String chaveOAMD = (String) JSFUtilities.getFromSession("key");
            setParcelas(new TResultadoParcelasEmAberto(new TParcelaEmAberto[0], TResultadoTransacaoWS.rtOK));
            setParcelas(getFinanceiroPacto().obterParcelasEmAberto(chaveOAMD, getEmpresaLogado().getCodigo()));
            setSucesso(true);
            setMensagemDetalhada("msg_dados_consultados", "Lista de parcelas obtidas com sucesso.");
        } catch (Exception ex) {
            setErro(true);
            setMensagemDetalhada(ex);
            Uteis.logar(ex, this.getClass());
        }
        return "";
    }

    public FinanceiroPacto getFinanceiroPacto() {
        return financeiroPacto;
    }

    public void setFinanceiroPacto(FinanceiroPacto financeiroPacto) {
        this.financeiroPacto = financeiroPacto;
    }

    public boolean isPermiteGerarBoleto(ParcelaEmAberto obj) {
        if (Calendario.maior(obj.getDataVencimento().getTime(), Calendario.hoje())) {
            return false;
        }

        Calendar calendar = Calendario.getInstance();
        int horaDoDia = calendar.get(Calendar.HOUR_OF_DAY);

        return horaDoDia <= 17;
    }
}

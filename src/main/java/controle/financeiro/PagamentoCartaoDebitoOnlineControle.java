package controle.financeiro;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.cieloecommerce.CieloDebitoeCommerceService;
import servicos.impl.redepay.ERedeDebitoService;
import servicos.interfaces.AprovacaoServiceInterface;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/*
 * Created by Luiz Felipe on 31/08/2017.
 */
public class PagamentoCartaoDebitoOnlineControle extends SuperControle {

    private ConfiguracaoSistemaVO configuracaoSistema;
    private List<MovParcelaVO> parcelasPagar;
    private Double valorPagar = 0.0;
    private CartaoCreditoTO dadosPagamento;
    private UsuarioVO responsavelPagamento;
    private MovPagamentoVO movPagamentoSelecionado;
    private Integer operadoraCartao;
    private PessoaVO pessoaPagamento;
    private String onComplete = "";
    private TipoDebitoOnlineEnum tipoDebitoOnline;
    private TransacaoVO transacaoVO;
    private List<SelectItem> listaTipoDebitoDisponiveis;

    public PagamentoCartaoDebitoOnlineControle() throws Exception {
        inicializarTiposDebitoDisponiveis();
    }


    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
    }

    private void inicializarTiposDebitoDisponiveis() throws Exception {
        setListaTipoDebitoDisponiveis(getFacade().getFormaPagamento().consultarTipoDebitoOnlineCadastrados());
    }

    public List<SelectItem> getOperadorasCartaoDebito() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        List<OperadoraCartaoVO> lista = getFacade().getOperadoraCartao().consultarPorTipoDebitoOnline(tipoDebitoOnline, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (OperadoraCartaoVO op : lista) {
            SelectItem item = new SelectItem(op.getCodigo(), "<img src=\"imagens/bandeiras/" + op.getOperadoraIntegracao().getImagem() + ".png\"/>");
            item.setEscape(false);
            itens.add(item);
        }
        if (lista.size() == 1) {
            this.getDadosPagamento().setBand(lista.get(0).getCodigoIntegracaoDebito());
        }
        return itens;
    }

    public void acaoMudarTipoDebitoOnline() {
        this.operadoraCartao = null;
        this.getDadosPagamento().setBand(null);
        this.getDadosPagamento().setBandeira(0);
    }

    /**
     * Listener para receber os dados do pagamento
     *
     * @param event
     */
    public void listaParcelas(final ActionEvent event) {
        //limpar os atributos do pagamento antes de receber lista de parcelas
        limparAtributos();
        try {
            this.prepararControlador((List<MovParcelaVO>) event.getComponent().getAttributes().get("listaParcelas"));
        } catch (Exception ex) {
            Logger.getLogger(PagamentoCartaoDebitoOnlineControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }
    }

    public void preencherParcelas(List<MovParcelaVO> listaParcelas) {
        //limpar os atributos do pagamento antes de receber lista de parcelas
        limparAtributos();
        try {
            this.prepararControlador(listaParcelas);
        } catch (Exception ex) {
            Logger.getLogger(PagamentoCartaoDebitoOnlineControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }
    }

    /**
     * Responsável por chamar a tela de pagamento recorrente em cartão de
     * crédito
     *
     * <AUTHOR> 04/07/2011
     */
    public String abrir() throws Exception {
        String retorno = "";
        try {
            inicializarConfiguracaoSistema();
            //verificar se as configurações com o gateway de pagamento estão configuradas
            validarConfiguracoes();
            //inicializar os atributos do pagamento
            validarAtributos();

            retorno = "pagamentoCartao";
            setSucesso(true);
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID(e.getMessage());
            montarMsgAlert(getMensagem());
            setSucesso(false);
        }
        return retorno;
    }

    public void validar() throws Exception {

        inicializarConfiguracaoSistema();
        //verificar se as configurações com o gateway de pagamento estão configuradas
        validarConfiguracoes();
        //inicializar os atributos do pagamento
        validarAtributos();


    }

    public void novo() {
        try {
            inicializarConfiguracaoSistema();
            //verificar se as configurações com o gateway de pagamento estão configuradas
            validarConfiguracoes();
            //inicializar os atributos do pagamento
            validarAtributos();

            setSucesso(true);
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemID(e.getMessage());
            montarMsgAlert(getMensagem());
            setSucesso(false);
        }
    }

    public void pagar() {
        try {
            setProcessandoOperacao(true);
            if (verificarLoginUsuario()) {
                getMovPagamentoSelecionado().setResponsavelPagamento(this.getResponsavelPagamento());
                dadosPagamento.setUsuarioResponsavel(this.getResponsavelPagamento());
                dadosPagamento.setIdPessoaCartao(this.pessoaPagamento.getCodigo());
                this.getDadosPagamento().setValorDocumento(Formatador.formatarValorMonetario(getMovPagamentoSelecionado().getValorTotal()));
                this.getDadosPagamento().setListaParcelas(this.getParcelasPagar());
                this.getDadosPagamento().setIp("RecorrENtE");
                this.getDadosPagamento().setIpClientePacto(this.getIpCliente());
                this.getDadosPagamento().setValor(getMovPagamentoSelecionado().getValorTotal());
                List<MovParcelaVO> parcelas = dadosPagamento.getListaParcelas();
                for (MovParcelaVO movParcelaVO : parcelas) {
                    if (!movParcelaVO.getSituacao().equals("EA")) {
                        throw new ConsistirException(String.format("Parcela %s - %s não está mais em aberto. Por favor, verifique localizando a parcela e tentando receber novamente.",
                                movParcelaVO.getCodigo(), movParcelaVO.getDescricao()));
                    } else {
                        getFacade().getMovParcela().validarMovParcelaPagar(movParcelaVO);
                    }
                    getFacade().getMovParcela().validarMovParcelaComTransacaoConcluidaOuPendente(movParcelaVO.getCodigo());
                }

                AprovacaoServiceInterface service = getServiceTransacaoOnlineDebito(getMovPagamentoSelecionado());
                new AragornService().povoarCartaoCreditoTO(dadosPagamento);
                setTransacaoVO(service.tentarAprovacao(dadosPagamento));

                if (getTransacaoVO().getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                    throw new Exception(transacaoVO.getResultadoRequisicao());
                }

                setOnComplete("window.location = '" + getTransacaoVO().getUrlTransiente() + "'");
            }
        } catch (Exception e) {
            Uteis.logar(e, PagamentoCartaoDebitoOnlineControle.class);
            setMensagemDetalhada("", e.getMessage());
        } finally {
            setProcessandoOperacao(false);
        }
    }

    public void consultarSituacaoPagamentoDebito() {
        try {
            AprovacaoServiceInterface service = getServiceTransacaoOnlineDebito(getMovPagamentoSelecionado());
            setTransacaoVO(service.confirmarTransacao(getTransacaoVO(), null , null));

            MovPagamentoControle movPagamentoControle = (MovPagamentoControle) JSFUtilities.getFromSession(MovPagamentoControle.class);
            if (getTransacaoVO().getSituacao() == SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) {
                movPagamentoControle.getMovPagamentoVO().getResponsavelPagamento().setUsername(this.getResponsavelPagamento().getUsername());
                movPagamentoControle.getMovPagamentoVO().getResponsavelPagamento().setSenha(this.getResponsavelPagamento().getSenha());
                movPagamentoControle.verificarUsuarioSenhaResponsavelPagamentoCupom();
                getTransacaoVO().setReciboPagamento(movPagamentoControle.getReciboObj().getCodigo());
                getTransacaoVO().setMovPagamento(this.getMovPagamentoSelecionado().getCodigo());
                getFacade().getTransacao().alterar(getTransacaoVO());
                movPagamentoControle.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
                movPagamentoControle.getMovPagamentoVO().setUsarPagamentoDebitoOnline(false);
                JSFUtilities.storeOnSession(MovPagamentoControle.class.getSimpleName(), movPagamentoControle);
            } else {
                throw new Exception(getTransacaoVO().getResultadoRequisicao());
            }
        } catch (Exception e) {
            Uteis.logar(e, PagamentoCartaoDebitoOnlineControle.class);
            setMensagemDetalhada("", e.getMessage());
        }
    }

    private AprovacaoServiceInterface getServiceTransacaoOnlineDebito(MovPagamentoVO movPagamentoVO) throws Exception {
        AprovacaoServiceInterface aprovacaoInterface = null;
        if (movPagamentoVO.getOperadoraCartaoVO().getTipoDebitoOnline().equals(TipoDebitoOnlineEnum.CIELO) && movPagamentoVO.getOperadoraCartaoVO().getCodigoIntegracaoDebito() != null) {
            String url = request().getRequestURL().toString() + "?retornoDebitoOnline=true";
            aprovacaoInterface = new CieloDebitoeCommerceService(Conexao.getFromSession(), getUsuarioLogado(), getMovPagamentoSelecionado(), url);
        } else if (movPagamentoVO.getOperadoraCartaoVO().getTipoDebitoOnline().equals(TipoDebitoOnlineEnum.E_REDE) && movPagamentoVO.getOperadoraCartaoVO().getCodigoIntegracaoDebito() != null) {
            String url = request().getRequestURL().toString() + "?retornoDebitoOnline=true";
            aprovacaoInterface = new ERedeDebitoService(Conexao.getFromSession(), getUsuarioLogado(), getMovPagamentoSelecionado(), url);
        }
        return aprovacaoInterface;
    }

    /**
     * Valida os dados digitados pelo usuario
     *
     * <AUTHOR> 08/07/2011
     */
    public void confirmar() {
        try {
            if (getParcelasPagar() != null) {
                for (MovParcelaVO listaParcela : getParcelasPagar()) {
                    MovPagamentoControle movPagamentoControle = (MovPagamentoControle) getControlador(MovPagamentoControle.class);
                    movPagamentoControle.validarParcelaRemessa(listaParcela);
                }
            }

            if (!this.getParcelasPagar().isEmpty()) {
                dadosPagamento.setEmpresa(this.getParcelasPagar().get(0).getEmpresa().getCodigo());
            }
            CartaoCreditoTO.validarDados(this.getDadosPagamento());
            setSucesso(true);
            setMensagemDetalhada("");
        } catch (Exception e) {
            setSucesso(false);
            setMensagemDetalhada(e.getMessage());
        }
    }

    /**
     * Responsável por inicializar atributos da operação de pagamento de
     * contrato recorrente
     *
     * <AUTHOR> 07/07/2011
     */
    private void validarAtributos() throws Exception {
        if (this.getParcelasPagar().isEmpty()) {
            throw new Exception(getMensagemInternalizacao("msg_erro_nenhumaParcelaRelacionada"));
        }
        for (MovParcelaVO parcela : this.getParcelasPagar()) {

            //calcular o total das parcelas somando-as
            this.setValorPagar(this.getValorPagar() + parcela.getValorParcela());
        }
        this.onComplete = "";
    }

    /**
     * retorna um comando de alerta caso a ação do botão não tenha sido efetuado
     * com sucesso
     *
     * <AUTHOR> 07/07/2011
     */
    public String getAlertValidacao() {
        if (getSucesso()) {
            return "";
        } else {
            return getMsgAlert();
        }
    }

    private void limparAtributos() {
        this.setValorPagar(0.0);
        this.setDadosPagamento(new CartaoCreditoTO());
        this.setParcelasPagar(new ArrayList<MovParcelaVO>());
        this.setResponsavelPagamento(new UsuarioVO());
    }

    /**
     * Responsável pela ação do botão cancelar
     *
     * <AUTHOR> 07/07/2011
     */
    public String cancelar() {
        limparAtributos();
        return "login";
    }

    private void validarConfiguracoes() throws Exception {
//        Boolean estaConfigurado = true;
//        EmpresaVO emp = getFacade().getEmpresa().consultarPorCodigo(this.getDadosPagamento().getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
//        String url = ConfiguracaoSistemaVO.obterURLRecorrencia(emp, this.getConfiguracaoSistema());
//        if (UteisValidacao.emptyString(url)) {
//            estaConfigurado = false;
//        }else{
//            setUrlTeste(url.toLowerCase().contains("//teste.aprovafacil"));
//        }
//        if(!estaConfigurado){
//            estaConfigurado = validarConfiguracaoVindi();
//        }
//        if(!estaConfigurado){
//            estaConfigurado = validarConfiguracaoCielo();
//        }
//        if(!estaConfigurado){
//            throw new Exception(getMensagemInternalizacao("msg_erro_configuracoesRecorrenciaInexistentes"));
//        }
    }

    public String getMostrarConfirmacaoUsuario() {
        if (this.getSucesso()) {
            return "Richfaces.showModalPanel('panelUsuarioSenhaDebito');";
        } else {
            return "";
        }

    }

    /**
     * Responsável por formatar o valor a ser pago para exibição
     *
     * <AUTHOR> 07/07/2011
     */
    public String getValor() {
        return Formatador.formatarValorMonetario(this.getValorPagar());
    }

    /**
     * Consulta o responsável pelo contrato, caso existam alteração
     */
    public void consultarResponsavel() {

        try {
            limparMsg();
            this.setResponsavelPagamento(
                    getFacade().getUsuario().consultarPorChavePrimaria(
                            this.getResponsavelPagamento().getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS));

            this.setMensagemID("msg_dados_consultados");
            getMovPagamentoSelecionado().setResponsavelPagamento(this.getResponsavelPagamento());
        } catch (Exception e) {
            this.setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean verificarLoginUsuario() throws Exception {
        getFacade().getControleAcesso().verificarLoginUsuarioPIN(
                this.getResponsavelPagamento().getUsername(),
                this.getResponsavelPagamento().getSenha().toUpperCase());
        return true;
    }

    public void selecionaOperadora() {
        if (this.operadoraCartao != null) {
            try {
                OperadoraCartaoVO operadoraCartaoVO = getFacade().getOperadoraCartao().consultarPorChavePrimaria(operadoraCartao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                this.getDadosPagamento().setBand(operadoraCartaoVO.getOperadoraIntegracao());
                this.getDadosPagamento().setBandeira(operadoraCartaoVO.getOperadoraIntegracao().getId());
                this.setTipoDebitoOnline(operadoraCartaoVO.getTipoDebitoOnline());
                if (this.getMovPagamentoSelecionado() != null) {//recurso pode não estar sendo utilizado na tela de pagamento (quitação)
                    this.getMovPagamentoSelecionado().setOperadoraCartaoVO(operadoraCartaoVO);

                }
                MovPagamentoControle control = (MovPagamentoControle) JSFUtilities.getManagedBean(MovPagamentoControle.class.getSimpleName());
                if (control != null) {
                    control.montarListaNrParcelasCartao(operadoraCartaoVO);
                }
                limparMsg();
            } catch (Exception ex) {
                Logger.getLogger(PagamentoCartaoDebitoOnlineControle.class.getName()).log(Level.SEVERE, null, ex);
                setMensagemDetalhada("msg_erro", ex.getMessage());
            }
        }

    }

    public void alterarBandeira() {
        dadosPagamento.setBand(null);
        dadosPagamento.setBandeira(0);
    }

    //--------------------------- GETTERS AND SETTERS ----------------------------------//
    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        if (this.configuracaoSistema == null) {
            this.configuracaoSistema = new ConfiguracaoSistemaVO();
        }
        return this.configuracaoSistema;

    }

    /**
     * @param parcelas the pagamentos to set
     */
    public void setParcelasPagar(List<MovParcelaVO> parcelas) {
        this.parcelasPagar = parcelas;
    }

    /**
     * @return the pagamentos
     */
    public List<MovParcelaVO> getParcelasPagar() {
        if (this.parcelasPagar == null) {
            parcelasPagar = new ArrayList<MovParcelaVO>();
        }
        return parcelasPagar;
    }

    /**
     * @param valorPagar the valorPagar to set
     */
    public void setValorPagar(Double valorPagar) {
        this.valorPagar = valorPagar;
    }

    /**
     * @return the valorPagar
     */
    public Double getValorPagar() {
        return valorPagar;
    }

    /**
     * @param responsavelPagamento the responsavelPagamento to set
     */
    public void setResponsavelPagamento(UsuarioVO responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    /**
     * @return the pessoaPagadora
     */
    public UsuarioVO getResponsavelPagamento() {
        if (responsavelPagamento == null) {
            responsavelPagamento = new UsuarioVO();
        }
        return responsavelPagamento;
    }

    /**
     * @param dadosPagamento the dadosPagamento to set
     */
    public void setDadosPagamento(CartaoCreditoTO dadosPagamento) {
        this.dadosPagamento = dadosPagamento;
    }

    /**
     * @return the dadosPagamento
     */
    public CartaoCreditoTO getDadosPagamento() {
        if (dadosPagamento == null) {
            dadosPagamento = new CartaoCreditoTO();
        }
        return dadosPagamento;
    }

    public MovPagamentoVO getMovPagamentoSelecionado() {
        return movPagamentoSelecionado;
    }

    public void setMovPagamentoSelecionado(MovPagamentoVO movPagamentoSelecionado) {
        this.movPagamentoSelecionado = movPagamentoSelecionado;
        setarPropriedadesOperadoraCartao();
    }

    private void setarPropriedadesOperadoraCartao() {
        if (this.movPagamentoSelecionado != null && this.movPagamentoSelecionado.getOperadoraCartaoVO() != null && !UteisValidacao.emptyNumber(this.movPagamentoSelecionado.getOperadoraCartaoVO().getCodigoOperadora())) {
            this.operadoraCartao = this.movPagamentoSelecionado.getOperadoraCartaoVO().getCodigo();
            selecionaOperadora();
        }
    }

    public void prepararControlador(List<MovParcelaVO> listaParcelas) throws Exception {
        limparMsg();
        inicializarConfiguracaoSistema();
        EmpresaVO empresa = getFacade().getEmpresa().obterEmpresaDeUmaListaParcelas(listaParcelas);
        this.setDadosPagamento(new CartaoCreditoTO());
        this.getDadosPagamento().setEmpresa(empresa.getCodigo());
        this.getDadosPagamento().setUrl(ConfiguracaoSistemaVO.obterURLRecorrencia(empresa, this.getConfiguracaoSistema()));
        this.setParcelasPagar(listaParcelas);
    }

    public Integer getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(Integer operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public String getOnComplete() {
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }


    public TipoDebitoOnlineEnum getTipoDebitoOnline() {
        return tipoDebitoOnline;
    }

    public void setTipoDebitoOnline(TipoDebitoOnlineEnum tipoDebitoOnline) {
        this.tipoDebitoOnline = tipoDebitoOnline;
    }

    public PessoaVO getPessoaPagamento() {
        return pessoaPagamento;
    }

    public void setPessoaPagamento(PessoaVO pessoaPagamento) {
        this.pessoaPagamento = pessoaPagamento;
    }

    public TransacaoVO getTransacaoVO() {
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public List<SelectItem> getListaTipoDebitoDisponiveis() {
        if (listaTipoDebitoDisponiveis == null) {
            listaTipoDebitoDisponiveis = new ArrayList<SelectItem>();
        }
        return listaTipoDebitoDisponiveis;
    }

    public void setListaTipoDebitoDisponiveis(List<SelectItem> listaTipoDebitoDisponiveis) {
        this.listaTipoDebitoDisponiveis = listaTipoDebitoDisponiveis;
    }
}

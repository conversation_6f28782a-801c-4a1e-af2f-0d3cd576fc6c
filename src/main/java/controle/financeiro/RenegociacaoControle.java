package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RenegociacaoControle extends SuperControle {

    private CaixaAbertoTO itemParaRenegociar = new CaixaAbertoTO();
    private List<MovParcelaVO> parcelasParaRenegociar = new ArrayList<MovParcelaVO>();
    private List<MovParcelaVO> parcelasRenegociadas = new ArrayList<MovParcelaVO>();
    private MovParcelaVO parcela = new MovParcelaVO();

    private Boolean apresentarTaxaDesconto = false;
    private MovParcelaVO taxa = new MovParcelaVO();
    private MovParcelaVO desconto = new MovParcelaVO();

    private Integer qtdParcelas = 1;
    private Date dataPrimeiraParcela = Calendario.hoje();
    private boolean mostrarBotaoRemover = true;

    private boolean renegociacaoConcluida = false;

    private boolean temParcelaCC = false;
    private boolean incluirProdutosExtra = true;
    private String tipoProdutoExtra = "DE";

    private boolean alteracaoManual = false;

    private long diasAtrasoJuros;
    private double valorJurosAtraso;
    private double valorMulta;
    private String justificativaRenegociacao;


    private Map<CaixaAbertoTO, List<MovParcelaVO>> parcelasPorCaixaParaRenegociar = new HashMap<CaixaAbertoTO, List<MovParcelaVO>>();
    private double valorDesejado = 0.0;
    private double porcentagemDescontoDesejado = 0.0;

    public void limpar() {
        setJustificativaRenegociacao("");
        parcelasRenegociadas = new ArrayList<MovParcelaVO>();
        parcela = new MovParcelaVO();

        setTaxa(new MovParcelaVO());
        setDesconto(new MovParcelaVO());

        setTipoProdutoExtra("DE");

        setIncluirProdutosExtra(true);
        setSucesso(false);
        setErro(false);
        setMensagemDetalhada("", "");
    }


    public Double getResiduo() {
        Double residuo = getTotalParaRenegociar();

        for (MovParcelaVO movParcelaVO : parcelasRenegociadas) {
            residuo -= Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
        }

        return Uteis.arredondarForcando2CasasDecimaisMantendoSinal(residuo);
    }


    public Double getTotalParaRenegociar() {
        Double totalParaRenegociar = 0.0;
        for (MovParcelaVO movParcelaVO : parcelasParaRenegociar) {
            if (movParcelaVO.getDescricao().equals("DESCONTOS")) {
                totalParaRenegociar -= movParcelaVO.getValorParcela();
            } else {
                totalParaRenegociar += movParcelaVO.getValorParcela();
            }
        }

        return Uteis.arredondarForcando2CasasDecimais(totalParaRenegociar);
    }

    public Double getTotalLancado() {
        Double totalLancado = 0.0;
        for (MovParcelaVO movParcelaVO : parcelasRenegociadas) {
            totalLancado += movParcelaVO.getValorParcela();
        }

        return Uteis.arredondarForcando2CasasDecimais(totalLancado);
    }

    public void prepararRenegociacao() throws InstantiationException, IllegalAccessException, ConsistirException {
        Double totalParaRenegociar = getResiduo();
        Double valorPorParcela = totalParaRenegociar / getQtdParcelas();

        if (getParcelasParaRenegociar().isEmpty()) {
            throw new ConsistirException("Selecione pelo menos uma parcela!");
        }

        if (getQtdParcelas() <= 0) {
            throw new ConsistirException("Informe a quantidade de parcelas a serem geradas");
        }

        setApresentarTaxaDesconto(!temParcelaDeCC());

        MovParcelaVO movParcelaVO = getParcelasParaRenegociar().get(0);

        for (int i = 0; i < getQtdParcelas(); i++) {
            Date dataVencimento = Uteis.obterDataFutura3(getDataPrimeiraParcela(), i);
            MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
            novaParcela.setDescricao("PARCELA RENEGOCIADA");
            novaParcela.setValorParcela(valorPorParcela);
            novaParcela.setDataVencimento(dataVencimento);
            novaParcela.setDataRegistro(Calendario.hoje());
            novaParcela.setDataRegistroAnterior(movParcelaVO.getDataRegistro());

            // Evitar erro de renegociar uma parcela parcelada pela operadora e o sistema dividir o valor da parcela no cartão do cliente
            if (getQtdParcelas() > 1 && getQtdParcelas() != getParcelasParaRenegociar().size()) {
                novaParcela.setNumeroParcelasOperadora(0);
            }

            getParcelasRenegociadas().add(novaParcela);
        }

        ajustarTela();
    }

    private boolean temParcelaDeCC() {
        boolean ehParcelaCC = false;
        for (MovParcelaVO parcelaVO : getParcelasParaRenegociar()) {
            if (parcelaVO.getMovPagamentoCC() != null && !parcelaVO.getMovPagamentoCC().equals("")) {
                ehParcelaCC = true;
                break;
            }
        }
        setTemParcelaCC(ehParcelaCC);
        return ehParcelaCC;
    }

    public String adicionarParcela() throws InstantiationException, IllegalAccessException {

        MovParcelaVO movParcelaVO = getParcelasRenegociadas().get(getParcelasRenegociadas().size() - 1);
        MovParcelaVO novaParcela = (MovParcelaVO) movParcelaVO.getClone(true);
        novaParcela.setDescricao("PARCELA RENEGOCIADA");
        novaParcela.setValorParcela(0.0);
        novaParcela.setDataVencimento(Uteis.obterDataFutura3(movParcelaVO.getDataVencimento(), 1));
        novaParcela.setDataRegistro(Calendario.hoje());
        novaParcela.setDataRegistroAnterior(movParcelaVO.getDataRegistro());
        getParcelasRenegociadas().add(novaParcela);

        ajustarTela();
        return "";
    }

    public String adicionarTaxa() throws Exception  {
        try {
            limparMsg();
            MovParcelaVO parcelaTaxa = (MovParcelaVO) getTaxa().getClone(true);
            parcelaTaxa.setDescricao("TAXA/JUROS");
            parcelaTaxa.setDataVencimento(Calendario.hoje());

            if (parcelaTaxa.getValorParcela() < 0) {
                throw new ConsistirException("Valor da taxa/juros não pode ser um valor negativo");
            }

            if (!getApresentarRenegociaValores() && parcelaTaxa.getValorParcela() > 0.01) {
                double vlr = 0.0;
                getDesconto().setValorParcela(vlr);
                parcelaTaxa.setValorParcela(vlr);
                getParcelasParaRenegociar().add(parcelaTaxa);
                setIncluirProdutosExtra(false);
                this.alterarValorParcela(1);
                montarErro("Usuário não tem permissão \"4.44 - Renegociação de Valores (Desconto/Juros) \" para alterar valor da parcela. Foi gerado uma parcela zerada para permitir finalizar a operação.");
                return "";
            }

            getParcelasParaRenegociar().add(parcelaTaxa);
            setIncluirProdutosExtra(false);

            //tipo de adição 2 == Adicionar uma Taxa
            this.alterarValorParcela(2);
        } catch (Exception e) {
            montarErro(e);
        }

        return "";
    }

    public String adicionarDesconto() {
        try {
            limparMsg();
            MovParcelaVO desconto = (MovParcelaVO) getDesconto().getClone(true);
            if (Uteis.arredondarForcando2CasasDecimais(desconto.getValorParcela()) > Uteis.arredondarForcando2CasasDecimais(getTotalParaRenegociar())) {
                throw new ConsistirException("Valor de desconto não pode ser maior que o valor das parcela a renegociar");
            } else if(desconto.getValorParcela() < 0) {
                throw new ConsistirException("Valor de desconto não pode ser um valor negativo");
            }

            desconto.setDescricao("DESCONTOS");
            desconto.setDataVencimento(Calendario.hoje());
            if (!getApresentarRenegociaValores() && desconto.getValorParcela() > 0.01){
                double vlr = 0.0;
                getDesconto().setValorParcela(vlr);
                desconto.setValorParcela(vlr);
                getParcelasParaRenegociar().add(desconto);
                setIncluirProdutosExtra(false);
                this.alterarValorParcela(1);
                throw new ConsistirException("Usuário não tem permissão \"4.44 - Renegociação de Valores (Desconto/Juros) \" para alterar valor da parcela. Foi gerado uma parcela zerada para permitir finalizar a operação.");
            }

            getParcelasParaRenegociar().add(desconto);
            setIncluirProdutosExtra(false);

            //tipo de adição 1 == Adicionar um Desconto
            this.alterarValorParcela(1);
        }catch (Exception e) {
            montarErro(e);
        }

        return "";
    }

    /**
     * Altera o valor da parcela de acordo com a entrada de desconto
     **/
    public void alterarValorParcela(int tipoAdicao) {
        int qtdeParcelasRenegociadas = this.getParcelasRenegociadas().size();
        //Se não for uma alteração manual desconta de todas
        if (!this.isAlteracaoManual()) {
            for (int i = 0; i < qtdeParcelasRenegociadas; i++) {
                Double valorParcela = this.getParcelasRenegociadas().get(i).getValorParcela();
                //No caso de ser Adicionar um Desconto
                if (tipoAdicao == 1) {
                    Double valorDesconto = (desconto.getValorParcela() / qtdeParcelasRenegociadas);
                    this.getParcelasRenegociadas().get(i).setValorParcela(valorParcela - valorDesconto);
                } else {//No caso de ser Adicionar uma Taxa
                    Double valorTaxa = (taxa.getValorParcela() / qtdeParcelasRenegociadas);
                    this.getParcelasRenegociadas().get(i).setValorParcela(valorParcela + valorTaxa);
                }
            }
        }
    }


    /**
     * Remove o desconto que foi adicionado as parcelas,
     * e volta ao valor anterior sem desconto
     **/
    public void removerDescontoValorParcela() {
        int qtdeParcelasRenegociadas = this.getParcelasRenegociadas().size();
        //Se não for uma alteração manual volta todas as parcelas para o
        //original
        if (!this.isAlteracaoManual()) {
            for (int i = 0; i < qtdeParcelasRenegociadas; i++) {
                Double valorParcela = this.getParcelasRenegociadas().get(i).getValorParcela();
                Double valorDesconto = (desconto.getValorParcela() / qtdeParcelasRenegociadas);
                this.getParcelasRenegociadas().get(i).setValorParcela(valorParcela + valorDesconto);
            }
        }
    }

    /**
     * Remove a taxa que foi adicionado as parcelas,
     * e volta ao valor anterior sem taxa
     **/
    public void removerTaxaValorParcela() {
        int qtdeParcelasRenegociadas = this.getParcelasRenegociadas().size();
        //Se não for uma alteração manual volta todas as parcelas para o
        //original
        if (!this.isAlteracaoManual()) {
            for (int i = 0; i < qtdeParcelasRenegociadas; i++) {
                Double valorParcela = this.getParcelasRenegociadas().get(i).getValorParcela();
                Double valorTaxa = (taxa.getValorParcela() / qtdeParcelasRenegociadas);
                this.getParcelasRenegociadas().get(i).setValorParcela(valorParcela - valorTaxa);
            }
        }
    }

    /**
     * Método que verifica se ouve alteração manual
     * nos valores da parcela
     **/
    public boolean verificarAlteracaoManual() {
        this.setAlteracaoManual(true);
        return this.isAlteracaoManual();
    }

    public String removerProdutosExtras() {
        getParcelasParaRenegociar().remove(desconto);
        getParcelasParaRenegociar().remove(taxa);

        this.removerDescontoValorParcela();
        this.removerTaxaValorParcela();

        setIncluirProdutosExtra(true);
        return "";
    }

    public String alterarTipoProdutoExtra() throws Exception {
        setTaxa(new MovParcelaVO());
        setDesconto(new MovParcelaVO());
        return "";
    }

    public String removerParcela() {
        MovParcelaVO movParcelaVO = (MovParcelaVO) context().getExternalContext().getRequestMap().get("parcelaRenegociada");
        getParcelasRenegociadas().remove(movParcelaVO);

        ajustarTela();
        return "";
    }

    private void ajustarTela() {
        setMostrarBotaoRemover(getParcelasRenegociadas().size() > 1);
        setRenegociacaoConcluida(false);
    }

    public String renegociar() throws Exception {
        try {
            try {
                if (Calendario.menor(Calendario.hoje(),getParcelasRenegociadas().get(0).getDataVencimento())) {
                    validarPermissao("AlterarDataRenegociacaoParcela", "4.45 - Alterar Data na Renegociação de Parcelas", getUsuarioLogado());
                }
            }catch (Exception e){
                setMensagemDetalhada(e.getMessage());
                throw new ConsistirException(e.getMessage()+", favor informar a data de hoje na primeira parcela. ");
            }
            if (getResiduo() != 0.0) {
                throw new ConsistirException("Existem resíduos para serem lançados");
            }
            if (getTotalLancado().equals(0.00) && getParcelasRenegociadas().size() > 1) {
                int nrParcelas = getParcelasRenegociadas().size();
                for (int i = nrParcelas - 1; i > 0; i--) {
                    getParcelasRenegociadas().remove(i);
                }
            }

            for (MovParcelaVO parcelaParaRenegociar : getParcelasRenegociadas()) {
                parcelaParaRenegociar.setJustificativaParcelasRenegociadas(getJustificativaRenegociacao());
                if (getParcelasRenegociadas().size() > 1) {
                    parcelaParaRenegociar.setNumeroParcelasOperadora(0);
                }
            }

            boolean existeParcelaDescontoTaxa = false;
            for (MovParcelaVO parcelaParaRenegociar : getParcelasParaRenegociar()) {
                existeParcelaDescontoTaxa = parcelaParaRenegociar.getDescricao().equals("DESCONTOS") || parcelaParaRenegociar.getDescricao().equals("TAXA/JUROS");
                getFacade().getPix().excluirPorParcela(parcelaParaRenegociar.getCodigo(), getUsuarioLogado());
            }

            if (!existeParcelaDescontoTaxa && (apresentarTaxaDesconto && incluirProdutosExtra)) {
                throw new ConsistirException("É necessário adicionar desconto ou Taxa/Juros na renegociação.");
            }

            validardataparcelas();
            processarRenegociacao(false, null, null);
            return voltar(); //Volta para outra tela para impedir que o usuário faça alterações que gerem confusões a ele.
        } catch (Exception ex) {
            setRenegociacaoConcluida(false);
            montarErro(ex);
            return "";
        }
    }

    private MovParcelaControle getMovParcelaControle(){
        return (MovParcelaControle) getControlador(MovParcelaControle.class);
    }

    private void processarRenegociacao(boolean mapaParcelas, MovParcelaVO ultimaParcela, Double valorDesejado) throws Exception {
        //Voltar Para Tela Anterior Com o nome do Cliente que foi renegociado
        //junto com a sua pesquisa
        MovParcelaControle movParcelaControle = (MovParcelaControle) getControlador(MovParcelaControle.class);
        movParcelaControle.setValorConsulta(getItemParaRenegociar().getNomeCliente());

        getFacade().getMovParcela().renegociarParcelas(getParcelasParaRenegociar(), getParcelasRenegociadas(),
                desconto, taxa, tipoProdutoExtra, mapaParcelas, ultimaParcela,
                valorDesejado, getPorcentagemDescontoDesejado(), temParcelaDeCC(), getUsuarioLogado(), false, false, true,
                null, null);

        setRenegociacaoConcluida(true);
        setSucesso(true);
        setErro(false);
        setMensagemDetalhada("msg_dados_gravados", "");
    }

    public String voltar() throws Exception {
        //setMensagemDetalhada("", "");
        setMensagem("");
        setErro(false);

        MovParcelaControle mpc = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        mpc.novo(false, true);


        context().getExternalContext().getSessionMap().put("MovParcelaControle", mpc);

        return "tela8";
    }

    //Método especifico para o botao cancelar
    //Nesse metodo remove a mensagem de erro
    public String cancelar() throws Exception {

        setMensagemDetalhada("", "");

        return this.voltar();
    }

    public CaixaAbertoTO getItemParaRenegociar() {
        return itemParaRenegociar;
    }

    public void setItemParaRenegociar(CaixaAbertoTO itemParaRenegociar) {
        this.itemParaRenegociar = itemParaRenegociar;
    }

    public List<MovParcelaVO> getParcelasParaRenegociar() {
        return parcelasParaRenegociar;
    }

    public MovParcelaVO getPrimeiraParcelaParaRenegociar() {
        if (getMovParcelaControle().getItemParaRenegociar().getParcelas().size() > 0) {
            return getMovParcelaControle().getItemParaRenegociar().getParcelas().get(0);
        } else {
            return null;
        }
    }

    public void setParcelasParaRenegociar(List<MovParcelaVO> parcelasParaRenegociar) {
        this.parcelasParaRenegociar = parcelasParaRenegociar;
    }

    public List<MovParcelaVO> getParcelasRenegociadas() {
        return parcelasRenegociadas;
    }

    public void setParcelasRenegociadas(List<MovParcelaVO> parcelasRenegociadas) {
        this.parcelasRenegociadas = parcelasRenegociadas;
    }

    public MovParcelaVO getParcela() {
        return parcela;
    }

    public void setParcela(MovParcelaVO parcela) {
        this.parcela = parcela;
    }

    public Integer getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public Date getDataPrimeiraParcela() {
        return dataPrimeiraParcela;
    }

    public void setDataPrimeiraParcela(Date dataPrimeiraParcela) {
        this.dataPrimeiraParcela = dataPrimeiraParcela;
    }

    public boolean isMostrarBotaoRemover() {
        return mostrarBotaoRemover;
    }

    public void setMostrarBotaoRemover(boolean mostrarBotaoRemover) {
        this.mostrarBotaoRemover = mostrarBotaoRemover;
    }

    public boolean isRenegociacaoConcluida() {
        return renegociacaoConcluida;
    }

    public void setRenegociacaoConcluida(boolean renegociacaoConcluida) {
        this.renegociacaoConcluida = renegociacaoConcluida;
    }

    public MovParcelaVO getTaxa() {
        return taxa;
    }

    public void setTaxa(MovParcelaVO taxa) {
        this.taxa = taxa;
    }

    public MovParcelaVO getDesconto() {
        return desconto;
    }

    public void setDesconto(MovParcelaVO desconto) {
        this.desconto = desconto;
    }

    public boolean isIncluirProdutosExtra() {
        return incluirProdutosExtra;
    }

    public void setIncluirProdutosExtra(boolean incluirProdutosExtra) {
        this.incluirProdutosExtra = incluirProdutosExtra;
    }

    public String getTipoProdutoExtra() {
        return tipoProdutoExtra;
    }

    public void setTipoProdutoExtra(String tipoProdutoExtra) {
        this.tipoProdutoExtra = tipoProdutoExtra;
    }

    public void validardataprimeiraparcela() {
        setMensagemDetalhada("", "");
        if (getDataPrimeiraParcela() != null && Uteis.getDataComHoraZerada(Calendario.hoje()).compareTo(getDataPrimeiraParcela()) > 0) {

            try{
                validarPermissao("RenegociacaoParcelasRetroativas", "4.39 - Renegociação de Parcelas com data retroativa", getUsuarioLogado());
            }catch (Exception e){
                setMensagemDetalhada(e.getMessage());
                setDataPrimeiraParcela(Calendario.hoje());
            }

            MovParcelaVO primeiraParcela = getPrimeiraParcelaParaRenegociar();
            if( primeiraParcela != null ){
                if ( !Calendario.maiorOuIgual(Uteis.getDataComHoraZerada(getDataPrimeiraParcela()), Uteis.getDataComHoraZerada(getPrimeiraParcelaParaRenegociar().getDataRegistro())) ) {
                    setMensagemDetalhada("A data de vencimento da primeira parcela não pode ser menor do que a data de lançamento das parcelas.");
                    setDataPrimeiraParcela(Calendario.hoje());
                }
            }

        }
    }

    public void validardataparcelas() throws Exception {
        setMensagemDetalhada("", "");
        for (MovParcelaVO parc : getParcelasRenegociadas()) {
            try {
                validarPermissao("AlterarDataRenegociacaoParcela", "4.45 - Alterar Data na Renegociação de Parcelas", getUsuarioLogado());
            }catch (Exception e){
                setMensagemDetalhada(e.getMessage());
                parc.setDataVencimento(Calendario.hoje());
            }

            try {
                if (Uteis.getDataComHoraZerada(Calendario.hoje()).compareTo(parc.getDataVencimento()) > 0) {
                    try{
                        validarPermissao("RenegociacaoParcelasRetroativas", "4.39 - Renegociação de Parcelas com data retroativa", getUsuarioLogado());
                    }catch (Exception e){
                        setMensagemDetalhada(e.getMessage());
                        parc.setDataVencimento(Calendario.hoje());
                    }
                }

                if ( !Calendario.maiorOuIgual(Uteis.getDataComHoraZerada(parc.getDataVencimento()), Uteis.getDataComHoraZerada(parc.getDataRegistroAnterior())) ) {
                    setMensagemDetalhada("A data de vencimento não pode ser menor do que a data de lançamento da parcela.");
                    parc.setDataVencimento(Calendario.hoje());
                } else {
                    if (Calendario.maior(parc.getDataVencimento(), Calendario.hoje())) {
                        parc.setDataRegistro(Calendario.hoje());
                    } else {
                        parc.setDataRegistro(parc.getDataVencimento());
                    }
                }
            } catch (Exception e) {
                setMensagemDetalhada(e.getMessage() + ", por favor informar a data da parcela.");
            }
        }
    }

    public Boolean getApresentarTaxaDesconto() {
        return apresentarTaxaDesconto;
    }

    public void setApresentarTaxaDesconto(Boolean apresentarTaxaDesconto) {
        this.apresentarTaxaDesconto = apresentarTaxaDesconto;
    }

    public boolean isAlteracaoManual() {
        return alteracaoManual;
    }

    public void setAlteracaoManual(boolean alteracaoManual) {
        this.alteracaoManual = alteracaoManual;
    }

    public long getDiasAtrasoJuros() {
        return diasAtrasoJuros;
    }

    public void setDiasAtrasoJuros(long diasAtrasoJuros) {
        this.diasAtrasoJuros = diasAtrasoJuros;
    }

    public double getValorMulta() {
        return valorMulta;
    }

    public void setValorMulta(double valorMulta) {
        this.valorMulta = valorMulta;
    }

    public double getValorJurosAtraso() {
        return valorJurosAtraso;
    }

    public void setValorJurosAtraso(double valorJurosAtraso) {
        this.valorJurosAtraso = valorJurosAtraso;
    }

    public String getValorJurosAtraso_Apresentar() {
        return Formatador.formatarValorMonetario(valorJurosAtraso);
    }

    public String getValorMulta_Apresentar() {
        return Formatador.formatarValorMonetario(valorMulta);
    }

    public Map<CaixaAbertoTO, List<MovParcelaVO>> getParcelasPorCaixaParaRenegociar() {
        return parcelasPorCaixaParaRenegociar;
    }

    public void setParcelasPorCaixaParaRenegociar(Map<CaixaAbertoTO, List<MovParcelaVO>> parcelasPorCaixaParaRenegociar) {
        this.parcelasPorCaixaParaRenegociar = parcelasPorCaixaParaRenegociar;
    }

    public int getQtdParcelasMapaParaRenegociar() {
        int i = 0;
        for (CaixaAbertoTO caixaAbertoTO : getParcelasPorCaixaParaRenegociar().keySet()) {
            i += getParcelasPorCaixaParaRenegociar().get(caixaAbertoTO).size();
        }
        return i;
    }

    public double getValorParcelasMapaParaRenegociar() {
        double valor = 0;
        for (CaixaAbertoTO caixaAbertoTO : getParcelasPorCaixaParaRenegociar().keySet()) {
            List<MovParcelaVO> parcelasDoCaixa = getParcelasPorCaixaParaRenegociar().get(caixaAbertoTO);
            for (MovParcelaVO parcela : parcelasDoCaixa) {
                valor += Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela());
            }
        }
        return valor;
    }

    public double getValorDesejado() {
        return valorDesejado;
    }

    public void setValorDesejado(double valorDesejado) {
        this.valorDesejado = valorDesejado;
    }

    public double getPorcentagemDescontoDesejado() {
        return porcentagemDescontoDesejado;
    }

    public void setPorcentagemDescontoDesejado(double porcentagemDescontoDesejado) {
        this.porcentagemDescontoDesejado = porcentagemDescontoDesejado;
    }

    public void calcularPorcentagemDesconto() {
        try {
            if (getValorDesejado() < 0) {
                throw new ConsistirException("msg_err_vlr_desj_negativo");
            }
            if (getValorDesejado() >= getValorParcelasMapaParaRenegociar()) {
                throw new ConsistirException("msg_err_vlr_desj_maior_vlr_div");
            }
            double porcentagem = Uteis.arredondarForcando2CasasDecimais(100 - ((getValorDesejado() / getValorParcelasMapaParaRenegociar()) * 100));
            setPorcentagemDescontoDesejado(porcentagem);
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void calcularValorDesconto() {
        try {
            if (getPorcentagemDescontoDesejado() > 100) {
                throw new ConsistirException("msg_perct_desc_sup_100");
            }
            if (getPorcentagemDescontoDesejado() < 0) {
                throw new ConsistirException("msg_perct_desc_infr_0");
            }
            double valorDesejado = getValorParcelasMapaParaRenegociar() * (1 - (getPorcentagemDescontoDesejado() / 100));
            setValorDesejado(valorDesejado);
            setSucesso(true);
            setErro(false);
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isTemParcelaCC() {
        return temParcelaCC;
    }

    public void setTemParcelaCC(boolean temParcelaCC) {
        this.temParcelaCC = temParcelaCC;
    }

    public boolean getApresentarRenegociaValores() {
        setMensagemDetalhada("", "");
        try {
            validarPermissao("RenegociacaoParcelaValores", "4.44 - Renegociação de Valores (Desconto/Juros)", getUsuarioLogado());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public String getJustificativaRenegociacao() {
        return justificativaRenegociacao;
    }

    public void setJustificativaRenegociacao(String justificativaRenegociacao) {
        this.justificativaRenegociacao = justificativaRenegociacao;
    }
}

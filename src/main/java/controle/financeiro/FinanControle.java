package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.ObjetoTreeTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import relatorio.negocio.jdbc.financeiro.RelatorioDRE;
import relatorio.negocio.jdbc.financeiro.RelatorioDemonstrativoFinanceiro;

import javax.faces.model.SelectItem;
import java.text.DateFormat;
import java.util.*;

public class FinanControle extends SuperControleRelatorio {

    private int tipoListaLancamentosMostrar = 1;
    private String mesSelecionado = "";
    private String codigoAgrupadorSelecionado = "";
    public TipoRelatorioDF tipoRelatorioDf = TipoRelatorioDF.COMPETENCIA;
    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private EmpresaVO empresaVO;
    public boolean totalMeses=false;
    public boolean mostrarFormaPagamento=false;
    private List<SelectItem> listaSelectItemEmpresa;
    private List<SelectItem> listaFonteDados;
    private TipoFonteDadosDF tipoFonteDadosDF = TipoFonteDadosDF.TODAS;
    private boolean houveErroNoRelatorio = false;
    private TipoVisualizacaoRelatorioDF tipoVisualizacao = TipoVisualizacaoRelatorioDF.PLANOCONTA;
    private String nomeEmpresa;
    private String periodoRelatorio;
    public RelatorioDRE relatorioDRE;
    public RelatorioDemonstrativoFinanceiro relatorioDF;
    public List<DemonstrativoFinanceiro> listaDF; // Esta lista contém todos os processamentos do DF e não será enviada ao Browser
    public List<DemonstrativoFinanceiro> listaDFBrowser; // Esta lista é uma copia "enchuta" da lista "listaDF" que será enviada ao Browser
    private int indiceNaoInformado;
    public List<MesProcessar> listaMesProcessar = new ArrayList<MesProcessar>();
    public List<LancamentoDF> listaLancamentosDF; // Esta lista será utilizada para mostrar os lançamentos, quando ou usuário clicar no total do mês
    public List<DemonstrativoFinanceiro> listaTotalizadoresDF; // Esta lista é uma copia "enchuta" da lista "listaDF" com os totalizadores da cada mês, que será enviada ao Browser
    private String totalSelecionado = "";
    private String totalSelecionadoEntrada = "";
    private String totalSelecionadoSaida = "";
    public TotalizadorMesDF totalizadorMesSelecionado;
    private String labelAgrupadorSelecionado = "";
    private String nomeAgrupadorSelecionado = "";
    private int totalRegistros = 0;
    public DemonstrativoFinanceiro demonstrativoFinanceiroSelecionado;
    private Boolean openConsulta = true;
    private Boolean openFiltrosMemoria = false;
    private Boolean openOpcoes = true;
    private Boolean openIntervalo = true;
    private Boolean mesReferencia = false;
    private ConfiguracaoFinanceiroVO confFinanceiro = new ConfiguracaoFinanceiroVO();
    private Map<String, Double> valoresTotais = new HashMap<String, Double>();
    private boolean mostrarComparativo = true;
    private boolean mostrarCampoEmpresa = false;
    private boolean agruparValorProdutoMMasModalidades = false;
    public String tituloData;
    private List<ObjetoTreeTO> listaCentroCustoTree;
    private List<ContaVO> contasFiltro;
    private String planosSelecionados;
    private String centrosSelecionados;
    private List<Integer> codigosPlanosSelecionados = new ArrayList<Integer>();
    private List<ObjetoTreeTO> listaPlanoContaTree;
    private boolean apresentarDevolucoesRel = true;

    public boolean isMostrarComparativo() {
        return mostrarComparativo;
    }

    public void setMostrarComparativo(boolean mostrarComparativo) {
        this.mostrarComparativo = mostrarComparativo;
    }

    public String getTotalSelecionadoEntrada() {
        return totalSelecionadoEntrada;
    }

    public void setTotalSelecionadoEntrada(String totalSelecionadoEntrada) {

        this.totalSelecionadoEntrada = totalSelecionadoEntrada;
    }

    public String getTotalSelecionadoSaida() {
        return totalSelecionadoSaida;
    }

    public void setTotalSelecionadoSaida(Double totalSelecionadoSaida) {
        if (totalSelecionadoSaida == 0.0) {
            this.totalSelecionadoSaida = Formatador.formatarValorMonetarioSemMoeda(totalSelecionadoSaida);
        } else {
            this.totalSelecionadoSaida = "(" + Formatador.formatarValorMonetarioSemMoeda(totalSelecionadoSaida).trim() + ")";
        }
    }

    public DemonstrativoFinanceiro getDemonstrativoFinanceiroSelecionado() {
        return demonstrativoFinanceiroSelecionado;
    }

    public void setDemonstrativoFinanceiroSelecionado(DemonstrativoFinanceiro demonstrativoFinanceiroSelecionado) {
        this.demonstrativoFinanceiroSelecionado = demonstrativoFinanceiroSelecionado;
    }

    public void abrirModalTodos(){
        setMensagemDetalhada("", "");
        DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
        df.setCodigoAgrupador(this.getCodigoAgrupadorSelecionado());
        int indiceDF = this.listaDF.indexOf(df);
        setListaLancamentosDF(new ArrayList<LancamentoDF>());
        if (indiceDF >= 0) {
            df = this.listaDF.get(indiceDF);
            this.demonstrativoFinanceiroSelecionado = df;
            setLabelAgrupadorSelecionado(this.getTipoVisualizacao().getDescricao());

            TotalizadorMesDF totalizadorMes = new TotalizadorMesDF();
            totalizadorMes.setMesProcessar(new MesProcessar());
            totalizadorMes.getMesProcessar().setMesAno("Total");
            List<LancamentoDF> listaLancamentos = new ArrayList<>();

            Double totalSelecionado = 0.0;
            Double totalEntradaNivel = 0.0;
            Double totalSelecionadoSaida = 0.0;
            String valorLancamentoFormatado="";

            for(TotalizadorMesDF total : df.getListaTotalizadorMeses()){
                totalizadorMesSelecionado = total;
                if (UteisValidacao.emptyList(total.getListaLancamentosNaoAtribuido())) {

                    for(LancamentoDF lancamentoDF : total.getListaLancamentos()){

                        valorLancamentoFormatado = lancamentoDF.getValorLancamentoApresentar().replace(".", "").replace(",", ".");
                        try {
                            if(valorLancamentoFormatado.contains("(")) {
                                totalSelecionadoSaida += Uteis.naoArredondar(lancamentoDF.getValorLancamento());
                            }else {
                                totalEntradaNivel += Uteis.naoArredondar(lancamentoDF.getValorLancamento());
                            }
                        }catch (Exception e){
                            System.out.println(lancamentoDF.getValorLancamento() + "/" + lancamentoDF.getDescricaoLancamento());
                            e.printStackTrace();
                        }
                    }

                    totalSelecionado = totalEntradaNivel - totalSelecionadoSaida;

                    listaLancamentos.addAll(total.getListaLancamentos());
                    setNomeAgrupadorSelecionado(df.getNomeAgrupador());
                    if(tipoRelatorioDf.equals(TipoRelatorioDF.RECEITAPROVISAO) ){
                        if(df.getNomeAgrupador().equals("Despesas")) {
                            setMostrarFormaPagamento(false);
                            setTituloData("Data Vencimento");
                        }else setTituloData("Data Quitação");
                    }
                } else {

                    for(LancamentoDF lancamentoDF : total.getListaLancamentos()){

                        valorLancamentoFormatado = lancamentoDF.getValorLancamentoApresentar().replace(".", "").replace(",", ".");
                        try {
                            if(valorLancamentoFormatado.contains("(")) {
                                totalSelecionadoSaida += Uteis.naoArredondar(lancamentoDF.getValorLancamento());
                            }else {
                                totalEntradaNivel += Uteis.naoArredondar(lancamentoDF.getValorLancamento());
                            }
                        }catch (Exception e){
                            System.out.println(lancamentoDF.getValorLancamento() + "/" + lancamentoDF.getDescricaoLancamento());
                            e.printStackTrace();
                        }
                    }

                    totalSelecionado = totalEntradaNivel - totalSelecionadoSaida;

                    listaLancamentos.addAll(total.getListaLancamentosNaoAtribuido());
                    setNomeAgrupadorSelecionado(df.getNomeAgrupador() + " - Não Atribuido");
                    if(tipoRelatorioDf.equals(TipoRelatorioDF.RECEITAPROVISAO) ){
                        if(df.getNomeAgrupador().equals("Despesa")) {
                            setMostrarFormaPagamento(false);
                            setTituloData("Data Vencimento");
                        }else setTituloData("Data Quitação");
                    }
                }

            }
            setTotalSelecionado(totalSelecionado);
            setTotalSelecionadoEntrada(Formatador.formatarValorMonetarioSemMoeda(totalEntradaNivel));
            setTotalSelecionadoSaida(totalSelecionadoSaida);

            this.totalRegistros = listaLancamentos.size();
            this.listaLancamentosDF.addAll(listaLancamentos);
        }

    }

    public void editarLancamento() {
        setMensagemDetalhada("", "");
            DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
            df.setCodigoAgrupador(this.getCodigoAgrupadorSelecionado());
            int indiceDF = this.listaDF.indexOf(df);
            int indiceLanc = -1;
            setListaLancamentosDF(new ArrayList<LancamentoDF>());
            if (indiceDF >= 0) {
            df = this.listaDF.get(indiceDF);
            this.demonstrativoFinanceiroSelecionado = df;
            setLabelAgrupadorSelecionado(this.getTipoVisualizacao().getDescricao());

                Double totalSelecionado = 0.0;
                Double totalEntradaNivel = 0.0;
                Double totalSelecionadoSaida = 0.0;
                String valorLancamentoFormatado="";

            TotalizadorMesDF totalizadorMes = new TotalizadorMesDF();
            totalizadorMes.setMesProcessar(new MesProcessar());
            totalizadorMes.getMesProcessar().setMesAno(this.getMesSelecionado());
            indiceLanc = df.getListaTotalizadorMeses().indexOf(totalizadorMes);
            if (indiceLanc >= 0) {
                totalizadorMes = df.getListaTotalizadorMeses().get(indiceLanc);
                totalizadorMesSelecionado = totalizadorMes;
                Set<LancamentoDF> listaLancamentos = null;
                if (this.getTipoListaLancamentosMostrar() == 1) {

                    for(LancamentoDF lancamentoDF : totalizadorMes.getListaLancamentos()) {
                        valorLancamentoFormatado = lancamentoDF.getValorLancamentoApresentar().replace(".", "").replace(",", ".");
                        try {
                            if(valorLancamentoFormatado.contains("(")) {
                                totalSelecionadoSaida += Uteis.arredondarForcando2CasasDecimais(lancamentoDF.getValorLancamento());
                            }else {
                                totalEntradaNivel += Uteis.arredondarForcando2CasasDecimais(lancamentoDF.getValorLancamento());
                            }
                        }catch (Exception e){
                            System.out.println(lancamentoDF.getValorLancamento() + "/" + lancamentoDF.getDescricaoLancamento());
                            e.printStackTrace();
                        }
                    }

                    totalSelecionado = totalEntradaNivel - totalSelecionadoSaida;

                    setTotalSelecionado(totalSelecionado);
                    setTotalSelecionadoEntrada(totalEntradaNivel.toString());
                    setTotalSelecionadoSaida(totalSelecionadoSaida);
                    listaLancamentos = totalizadorMes.getListaLancamentos();
                    setNomeAgrupadorSelecionado(df.getNomeAgrupador());
                    if(tipoRelatorioDf.equals(TipoRelatorioDF.RECEITAPROVISAO) ){
                        if(df.getNomeAgrupador().equals("Despesas")) {
                            setMostrarFormaPagamento(false);
                            setTituloData("Data Vencimento");
                        }else setTituloData("Data Quitação");
                    }
                } else {

                    for(LancamentoDF lancamentoDF : totalizadorMes.getListaLancamentosNaoAtribuido()) {
                        valorLancamentoFormatado = lancamentoDF.getValorLancamentoApresentar().replace(".", "").replace(",", ".");
                        try {
                            if(valorLancamentoFormatado.contains("(")) {
                                totalSelecionadoSaida += Uteis.naoArredondar(lancamentoDF.getValorLancamento());
                            }else {
                                totalEntradaNivel += Uteis.naoArredondar(lancamentoDF.getValorLancamento());
                            }
                        }catch (Exception e){
                            System.out.println(lancamentoDF.getValorLancamento() + "/" + lancamentoDF.getDescricaoLancamento());
                            e.printStackTrace();
                        }
                    }

                    totalSelecionado = totalEntradaNivel - totalSelecionadoSaida;

                    listaLancamentos = totalizadorMes.getListaLancamentosNaoAtribuido();
                    setNomeAgrupadorSelecionado(df.getNomeAgrupador() + " - Não Atribuido");
                    if(tipoRelatorioDf.equals(TipoRelatorioDF.RECEITAPROVISAO) ){
                        if(df.getNomeAgrupador().equals("Despesas")) {
                            setMostrarFormaPagamento(false);
                            setTituloData("Data Vencimento");
                        }else setTituloData("Data Quitação");
                    }
                }

                setTotalSelecionado(totalSelecionado);
                setTotalSelecionadoEntrada(Formatador.formatarValorMonetarioSemMoeda(totalEntradaNivel));
                setTotalSelecionadoSaida(totalSelecionadoSaida);

                this.totalRegistros = listaLancamentos.size();
                this.listaLancamentosDF.addAll(listaLancamentos);
            }
        }
        if ((indiceDF < 0) || (indiceLanc < 0)) {
            setMensagemDetalhada("msg_erro", "Erro ao buscar os lançamentos do mês '" + this.getMesSelecionado() + "'"
                    + " indiceDF=" + indiceDF + " indiceLanc=" + indiceLanc);
        }
        //System.out.println("Selecionou o mes: " + this.mesSelecionado );
    }

    public String getTotalSelecionado() {
        return totalSelecionado;
    }

    public void setTotalSelecionado(Double totalSelecionado) {
        if (totalSelecionado < 0.0) {
            this.totalSelecionado = "(" + Formatador.formatarValorMonetarioSemMoeda(totalSelecionado).trim() + ")";
        } else {
            this.totalSelecionado = Formatador.formatarValorMonetarioSemMoeda(totalSelecionado);
        }
    }

    public int getTipoListaLancamentosMostrar() {
        /* Informa o tipo de lista de lançamentos que será visualizada no browser.
         * Se o usuário clicar no valor do totalizador "Não Atribuido", então a lista que será mostrada
         * será somente dos lançamentos deste totalizador. Caso contrário, será mostrado a lista de todos os
         * lançamentos do nivel selecionado.
         *
         */
        //Obs.:
        //  O valor 1 para este campo informa que a lista a ser exibida será de todos os Lançamentos do Nível
        // O valor 2 para este campo informa que a lista a ser exibida será somente dos lançamentos do totalizador "Não Atribuido"
        return tipoListaLancamentosMostrar;
    }

    public void setTipoListaLancamentosMostrar(int tipoListaLancamentosMostrar) {
        this.tipoListaLancamentosMostrar = tipoListaLancamentosMostrar;
    }

    public String getCodigoAgrupadorSelecionado() {
        return codigoAgrupadorSelecionado;
    }

    public void setCodigoAgrupadorSelecionado(String codigoAgrupadorSelecionado) {
        this.codigoAgrupadorSelecionado = codigoAgrupadorSelecionado;
    }

    public String getMesSelecionado() {
        return mesSelecionado;
    }

    public void setMesSelecionado(String mesSelecionado) {
        this.mesSelecionado = mesSelecionado;
    }

    public Boolean getMostrarFormaPagamento(){
        return mostrarFormaPagamento;
    }

    public void setMostrarFormaPagamento(boolean mostrarFormaPagamento){
        this.mostrarFormaPagamento = mostrarFormaPagamento;
    }

    public Boolean getTotalMeses() {
        return totalMeses;
    }

    public void setTotalMeses(Boolean totalMeses) {
        this.totalMeses = totalMeses;

        if(mesSelecionado.isEmpty()){
            this.totalMeses = true;
        }
        else this.totalMeses = false;
    }

    private void totalizarListaTotalizadoresDF(TotalizadorMesDF totalizadorMesDf) {
        DemonstrativoFinanceiro dfEntrada = this.listaTotalizadoresDF.get(0);
        DemonstrativoFinanceiro dfSaida = this.listaTotalizadoresDF.get(1);
        DemonstrativoFinanceiro dfResultado = this.listaTotalizadoresDF.get(2);

        TotalizadorMesDF totMesDF;
        int indice;
        //Somar os valores das Entradas.
        indice = dfEntrada.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        totMesDF = dfEntrada.getListaTotalizadorMeses().get(indice);
        totMesDF.setTotalNivel(totalizadorMesDf.getTotalEntradaNivel());

        //dfEntrada.setTotalTodosMeses(dfEntrada.getTotalTodosMeses() + totMesDF.getTotalNivel());

        //Somar os valores das Saídas.
        indice = dfSaida.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        totMesDF = dfSaida.getListaTotalizadorMeses().get(indice);

        totMesDF.setTotalNivel(totalizadorMesDf.getTotalSaidaNivel());
        //dfSaida.setTotalTodosMeses(dfSaida.getTotalTodosMeses() + totMesDF.getTotalNivel());

        //Calcular o Resultado
        indice = dfResultado.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        totMesDF = dfResultado.getListaTotalizadorMeses().get(indice);
        totMesDF.setTotalNivel(totalizadorMesDf.getTotalEntradaNivel() + totalizadorMesDf.getTotalSaidaNivel());
        //dfResultado.setTotalTodosMeses(dfResultado.getTotalTodosMeses() + totMesDF.getTotalNivel());

    }

    private void criarListaTotalizadoresDF() {
        this.listaTotalizadoresDF = new ArrayList<DemonstrativoFinanceiro>();
        DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Total Entrada(+): ");
        listaTotalizadoresDF.add(df);
        df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Total Saída(-): ");
        listaTotalizadoresDF.add(df);
        df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Resultado(=): ");
        listaTotalizadoresDF.add(df);
        // Criar lista dos meses que serão processados
        List<MesProcessar> listaMesProcessar = new ArrayList<MesProcessar>();
        if (this.listaDF.size() > 0) {
            DemonstrativoFinanceiro dfRel = listaDF.get(0);
            for (TotalizadorMesDF obj : dfRel.getListaTotalizadorMeses()) {
                listaMesProcessar.add(obj.getMesProcessar());
            }
        }
        // Criar a lista de meses para cada totalizador.
        for (DemonstrativoFinanceiro demonstrativo : this.listaTotalizadoresDF) {
            RelatorioDemonstrativoFinanceiro.criarTotalizadoresMeses(demonstrativo, listaMesProcessar);
        }

    }

    public Double valorGeral(TotalizadorMesDF tot, boolean total) {
        Double valor = 0.0;
        for (DemonstrativoFinanceiro df : listaDF) {
            if (!UteisValidacao.emptyString(df.getCodigoAgrupador())
                    && df.getCodigoAgrupador().length() == 3
                    && df.getTipoES() != null
                    && df.getTipoES().equals(TipoES.ENTRADA)) {
                if (total) {
                    for (TotalizadorMesDF t : df.getListaTotalizadorMeses()) {
                        valor += t.getTotalNivel();
                    }

                } else {
                    int indexOfMeses = df.getListaTotalizadorMeses().indexOf(tot);
                    if (indexOfMeses >= 0) {
                        valor += df.getListaTotalizadorMeses().get(indexOfMeses).getTotalNivel();
                    }
                }
            }
        }
        valoresTotais.put(total ? "total" : tot.getMesProcessar().getMesAno(), valor);
        return valor;
    }

    public void criarListaParaEnviarAoBrowser(boolean dre, boolean caixaplanocontas) {
        valoresTotais = new HashMap<String, Double>();
        this.listaDFBrowser = new ArrayList<DemonstrativoFinanceiro>();
        criarListaTotalizadoresDF();

        for (DemonstrativoFinanceiro obj : this.listaDF) {
            DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
            df.setCodigoAgrupador(obj.getCodigoAgrupador());
            df.setNomeAgrupador(obj.getNomeAgrupador());
            df.setHouveNaoAtribuido(obj.isHouveNaoAtribuido());
            df.setTipoES(obj.getTipoES());
            df.setDre(obj.getDre());
            df.setTipoEquivalencia(obj.getTipoEquivalencia());
            df.setMetaPerc(obj.getMetaPerc());
            df.setLink(obj.getLink());
            df.setPercPretendido(obj.getPercPretendido());

            if (caixaplanocontas) {
                df.setTotalPorFormaPagamento(totalizarPorFormaPagamento(obj));
                df.setCodigoPlanoContaTotalFormaPag(obterCodigoPlanoContasTotalizadorFormaPagamento(obj));

                if ((!obj.getNomeAgrupador().equalsIgnoreCase("Não Informado Plano de Conta") &&
                        (obj.getTipoES() != null && !obj.getTipoES().equals(TipoES.ENTRADA))) ||
                        (!obj.getNomeAgrupador().equalsIgnoreCase("Não Informado Plano de Conta") && (df.getTotalPorFormaPagamento() == null || df.getTotalPorFormaPagamento().isEmpty()))) {
                    continue;
                }
            }

            if (dre) {
                df.setListaCentros(obj.getListaCentros());
            }
            for (TotalizadorMesDF totalizadorMesDf : obj.getListaTotalizadorMeses()) {

                Double totalSelecionado = 0.0;
                Double totalEntradaNivel = 0.0;
                Double totalSelecionadoSaida = 0.0;

                for(LancamentoDF lancamentoDF : totalizadorMesDf.getListaLancamentos()){
                    try {
                        totalEntradaNivel +=  Uteis.arredondarForcando2CasasDecimais(lancamentoDF.getValorLancamento());
                    }catch (Exception e){
                        System.out.println(lancamentoDF.getValorLancamento() + "/" + lancamentoDF.getDescricaoLancamento());
                        e.printStackTrace();
                    }
                }

                totalSelecionado = totalEntradaNivel - totalSelecionadoSaida;

                MesProcessar mesProcessar = totalizadorMesDf.getMesProcessar();
                MesProcessar mesp = new MesProcessar();
                mesp.setDataIni(mesProcessar.getDataIni());
                mesp.setDataFim(mesProcessar.getDataFim());
                mesp.setMesAno(mesProcessar.getMesAno());
                mesp.setNomeMes(mesProcessar.getNomeMes());

                if(totalizadorMesDf.getTotalEntradaNivel() != 0.0) {
                    mesp.setEntrada(totalizadorMesDf.getTotalEntradaNivel());
                }
                if(totalizadorMesDf.getTotalSaidaNivel() != 0.0) {
                    mesp.setSaida(totalizadorMesDf.getTotalSaidaNivel());
                }
                if(totalizadorMesDf.getTotalSaidaNivel()!=0.0) {
                    mesp.setTotal(totalizadorMesDf.getTotalNivel());
                }else{
                    mesp.setTotal(totalSelecionado);
                }

                mesp.setTotalNaoAtribuido(totalizadorMesDf.getTotalNaoAtribuido());
                mesp.setMetaPretendido(df.getPercPretendido());

                if (df.getTipoES() != null && df.getTipoES().equals(TipoES.SAIDA) && mostrarComparativo) {
                    Double valor = valoresTotais.get(mesProcessar.getMesAno());
                    if (valor == null) {
                        valor = valorGeral(totalizadorMesDf, false);
                    }
                    mesp.setValorPercGasto(mesp.getTotalPositivo() / (UteisValidacao.emptyNumber(valor) ? 1 : valor));
                    mesp.setValorPercGastoNA((mesp.getTotalNaoAtribuido() < 0.0 ? mesp.getTotalNaoAtribuido() * -1
                            : mesp.getTotalNaoAtribuido()) / (UteisValidacao.emptyNumber(valor) ? 1 : valor));
                }

                df.getListaMeses().add(mesp);

                if(totalizadorMesDf.getTotalSaidaNivel()!=0.0){
                    df.setTotalTodosMeses(df.getTotalTodosMeses() + mesp.getTotal());
                }else{
                    df.setTotalTodosMeses(df.getTotalTodosMeses() + totalSelecionado);
                }
                // Calcular os totalizadores somente para os agrupadores de último nível.
                if (obj.getCodigoAgrupador().length() == 3) {
                    totalizarListaTotalizadoresDF(totalizadorMesDf);
                }
            }

            if (df.getTipoES() != null && df.getTipoES().equals(TipoES.SAIDA) && mostrarComparativo) {
                Double valor = valoresTotais.get("total");
                if (valor == null) {
                    valor = valorGeral(null, true);
                }
                df.setValorPercGasto(df.getTotalTodosMesesPositivo() / (UteisValidacao.emptyNumber(valor) ? 1 : valor));
            }

            listaDFBrowser.add(df);
        }

        if (listaDFBrowser.get(listaDFBrowser.size() - 1).getTotalTodosMeses() == 0 && !dre) {
            this.indiceNaoInformado = -1;
            // Excluir o nível "Não Informado (Plano Contas) ou (Centro Custos)", caso não tenha sido associado nenhum valor a este nível.
            listaDFBrowser.remove(listaDFBrowser.size() - 1);
        } else {
            this.indiceNaoInformado = this.listaDFBrowser.size();
        }

        // Calcular os Totais de todos os meses para a lista de totalizadores
        for (DemonstrativoFinanceiro dfTot : this.listaTotalizadoresDF) {
            for (TotalizadorMesDF totMes : dfTot.getListaTotalizadorMeses()) {
                dfTot.setTotalTodosMeses(dfTot.getTotalTodosMeses() + totMes.getTotalNivel());
            }
        }

        for(DemonstrativoFinanceiro df : this.listaDFBrowser) {
            for(MesProcessar mesProcessar : df.getListaMeses()) {
                df.setTotalEntradaTodosOsMeses(df.getTotalEntradaTodosOsMeses() + mesProcessar.getEntrada());
                df.setTotalSaidaTodosOsMeses(df.getTotalSaidaTodosOsMeses() + mesProcessar.getSaida());
            }
        }

        if (this.listaDFBrowser.size() > 0) {
            // Pegar um objeto da lista dos totalizadores dos Meses para montar o cabeçalho dos meses na treeView
            this.listaMesProcessar = listaDFBrowser.get(0).getListaMeses();
        }

    }

    private String obterCodigoPlanoContasTotalizadorFormaPagamento(DemonstrativoFinanceiro df) {
        if (df.getListaTotalizadorMeses() != null && !df.getListaTotalizadorMeses().isEmpty()) {
            for (TotalizadorMesDF totalizadorMesDF : df.getListaTotalizadorMeses()) {
                if (totalizadorMesDF.getListaLancamentos() != null && !totalizadorMesDF.getListaLancamentos().isEmpty()) {
                    for (LancamentoDF lancamentoDF : totalizadorMesDF.getListaLancamentos()) {
                        if (lancamentoDF.getRateios() != null && !lancamentoDF.getRateios().isEmpty()) {
                            for (RateioIntegracaoTO rateio : lancamentoDF.getRateios()) {
                                if(rateio.getCodigoPlano().equals(df.getCodigoAgrupador())){
                                    return rateio.getCodigoPlano();
                                }
                            }
                        }
                    }
                }
            }
        }
        return "";
    }

    private HashMap<String, Double> totalizarPorFormaPagamento(DemonstrativoFinanceiro df) {
        HashMap<String, Double> totalPorFormaPagamento = new HashMap<>();
        for (TotalizadorMesDF mesDf : df.getListaTotalizadorMeses()) {
            for (LancamentoDF lancamentoDF : mesDf.getListaLancamentos()) {
                if (lancamentoDF.getTipoFormaPagto() != null) {
                    String descFormaPag = (lancamentoDF.getDescricaoFormaPagamento() != null ? lancamentoDF.getDescricaoFormaPagamento() : lancamentoDF.getTipoFormaPagto().getDescricao()).toUpperCase();
                    if (totalPorFormaPagamento.containsKey(descFormaPag)) {
                        totalPorFormaPagamento.put(descFormaPag, totalPorFormaPagamento.get(descFormaPag) + lancamentoDF.getValorLancamento());
                    } else {
                        totalPorFormaPagamento.put(descFormaPag, lancamentoDF.getValorLancamento());
                    }
                }
            }
        }
        return totalPorFormaPagamento;
    }

    public List<DemonstrativoFinanceiro> getListaDFBrowser() {
        if (listaDFBrowser == null) {
            listaDFBrowser = new ArrayList<DemonstrativoFinanceiro>();
        }
        return listaDFBrowser;
    }

    public void setListaDFBrowser(List<DemonstrativoFinanceiro> listaDFBrowser) {
        this.listaDFBrowser = listaDFBrowser;
    }

    public String getPeriodoRelatorio() {
        return periodoRelatorio;
    }

    public void setPeriodoRelatorio(String periodoRelatorio) {
        this.periodoRelatorio = periodoRelatorio;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public TipoVisualizacaoRelatorioDF getTipoVisualizacao() {
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(TipoVisualizacaoRelatorioDF tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }

    public List getTiposVisualizacao() throws Exception {
        List itens = new ArrayList();
        for (TipoVisualizacaoRelatorioDF tipo : TipoVisualizacaoRelatorioDF.values()) {
            itens.add(new SelectItem(TipoVisualizacaoRelatorioDF.getTipoVisualizacaoDF(tipo.getCodigo()), tipo.getDescricao()));
        }
        return itens;
    }

    public TipoRelatorioDF getTipoRelatorioDf() {
        return tipoRelatorioDf;
    }

    public void setTipoRelatorioDf(TipoRelatorioDF tipoRelatorioDf) {
        this.tipoRelatorioDf = tipoRelatorioDf;
    }

    public boolean getHouveErroNoRelatorio() {
        return houveErroNoRelatorio;
    }

    public void setHouveErroNoRelatorio(boolean houveErroNoRelatorio) {
        this.houveErroNoRelatorio = houveErroNoRelatorio;
    }

    /**
     * @param listaSelectItemEmpresa the listaSelectItemEmpresa to set
     */
    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    /**
     * Responsável por montar Lista de Empresas
     *
     * <AUTHOR> 23/08/2011
     */
    @SuppressWarnings("unchecked")
    public void montarListaSelectItemEmpresa() {
        try {
            if (mostrarCampoEmpresa || getEmpresaVO().getCodigo() == 0) {
                List<SelectItem> objs = new ArrayList<SelectItem>();
                objs.add(new SelectItem(0, ""));
                List<EmpresaVO> resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                Iterator i = resultadoConsulta.iterator();
                while (i.hasNext()) {
                    EmpresaVO obj = (EmpresaVO) i.next();
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
                setListaSelectItemEmpresa(objs);
            }
        } catch (Exception e) {
            setListaSelectItemEmpresa(new ArrayList());
            e.printStackTrace();
        }
    }

    public void obterEmpresaEscolhida() throws Exception {
        if (getEmpresaVO().getCodigo() != 0) {
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            setEmpresaVO(new EmpresaVO());
        }
    }

    /**
     * @return the listaSelectItemEmpresa
     */
    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            montarListaSelectItemEmpresa();
        }
        return listaSelectItemEmpresa;
    }

    public List<SelectItem> getListaFonteDados() {
        return listaFonteDados;
    }

    public void setListaFonteDados(List<SelectItem> listaFonteDados) {
        this.listaFonteDados = listaFonteDados;
    }

    public TipoFonteDadosDF getTipoFonteDadosDF() {
        return tipoFonteDadosDF;
    }

    public void setTipoFonteDadosDF(TipoFonteDadosDF tipoFonteDadosDF) {
        this.tipoFonteDadosDF = tipoFonteDadosDF;
    }

    public void criarListaTipoFonteDadosDF() {
        listaFonteDados = new ArrayList<SelectItem>();
        for (TipoFonteDadosDF tipo : TipoFonteDadosDF.values()) {
            if (confFinanceiro.getUsarCentralEventos() || (!tipo.equals(TipoFonteDadosDF.CENTRAL_EVENTOS) && !tipo.equals(TipoFonteDadosDF.TODAS_MENOS_FINANCEIRO))) {
                listaFonteDados.add(new SelectItem(tipo, tipo.getDescricao()));
            }
        }
    }

    /**
     * @param empresaVO the empresaVO to set
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return the empresaVO
     */
    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public Date getDataFim() {
        if (dataFim == null) {
            dataFim = Calendario.hoje();
        }
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        if (dataFim == null) {
            return;
        }
        this.dataFim = dataFim;
    }

    public Date getDataInicio()  {
        if (dataInicio == null) {
            dataInicio = Calendario.hoje();
        }
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        if (dataInicio == null) {
            return;
        }
        this.dataInicio = dataInicio;
    }

    public String getOninputkeypress() {
        if (this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA ||
                this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA_QUITADA ||
                this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) {
            return "return mascara2(this.form, this.id, '99/9999', event);";
        } else {
            return "return mascara2(this.form, this.id, '99/99/9999', event);";
        }
    }

    public String getInputSize() {
        if (this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA ||
                this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA_QUITADA ||
                this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) {
            return "4";
        } else {
            return "10";
        }

    }

    public List<SelectItem> getTiposRelatorios() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();

        for (TipoRelatorioDF tipo : TipoRelatorioDF.values()) {
            if(tipo.equals(TipoRelatorioDF.COMPETENCIA_INDEPENDENTE_QUITACAO)){
                continue;
            }
            if ((!tipo.equals(TipoRelatorioDF.COMPETENCIA_QUITADA) &&
                    !tipo.equals(TipoRelatorioDF.COMPETENCIA_NAO_QUITADA))
                    || confFinanceiro.isEspecificarCompetencia()) {
                itens.add(new SelectItem(TipoRelatorioDF.getTipoRelatorioDF(tipo.getCodigo()), tipo.getDescricaoCompleta()));
            }
        }
        return itens;
    }

    public String getDatePattern() {
        if (this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA ||
                this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA_QUITADA ||
                this.tipoRelatorioDf == TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) {
            return "MM/yyyy";
        } else {
            return "dd/MM/yyyy";
        }
    }

    public int getIndiceNaoInformado() {
        return indiceNaoInformado;
    }

    public void setIndiceNaoInformado(int indiceNaoInformado) {
        this.indiceNaoInformado = indiceNaoInformado;
    }

    public List<MesProcessar> getListaMesProcessar() {
        return listaMesProcessar;
    }

    public void setListaMesProcessar(List<MesProcessar> listaMesProcessar) {
        this.listaMesProcessar = listaMesProcessar;
    }

    public List<LancamentoDF> getListaLancamentosDF() {
        return listaLancamentosDF;
    }

    public void setListaLancamentosDF(List<LancamentoDF> listaLancamentosDF) {
        this.listaLancamentosDF = listaLancamentosDF;
    }

    public List<DemonstrativoFinanceiro> getListaTotalizadoresDF() {
        return listaTotalizadoresDF;
    }

    public void setListaTotalizadoresDF(List<DemonstrativoFinanceiro> listaTotalizadoresDF) {
        this.listaTotalizadoresDF = listaTotalizadoresDF;
    }

    public String getLabelAgrupadorSelecionado() {
        return labelAgrupadorSelecionado;
    }

    public void setLabelAgrupadorSelecionado(String labelAgrupadorSelecionado) {
        this.labelAgrupadorSelecionado = labelAgrupadorSelecionado;
    }

    public String getNomeAgrupadorSelecionado() {
        return nomeAgrupadorSelecionado;
    }

    public void setNomeAgrupadorSelecionado(String nomeAgrupadorSelecionado) {
        this.nomeAgrupadorSelecionado = nomeAgrupadorSelecionado;
    }

    public int getTotalRegistros() {
        return totalRegistros;
    }

    public void setTotalRegistros(int totalRegistros) {
        this.totalRegistros = totalRegistros;
    }

    /**
     * @param openConsulta the openConsulta to set
     */
    public void setOpenConsulta(Boolean openConsulta) {
        this.openConsulta = openConsulta;
    }

    /**
     * @return the openConsulta
     */
    public Boolean getOpenConsulta() {
        if (openConsulta == null) {
            openConsulta = true;
        }
        return openConsulta;
    }

    /**
     * @param openFiltrosMemoria the openFiltrosMemoria to set
     */
    public void setOpenFiltrosMemoria(Boolean openFiltrosMemoria) {
        this.openFiltrosMemoria = openFiltrosMemoria;
    }

    /**
     * @return the openFiltrosMemoria
     */
    public Boolean getOpenFiltrosMemoria() {
        if (openFiltrosMemoria == null) {
            openFiltrosMemoria = false;
        }
        return openFiltrosMemoria;
    }

    /**
     * @param openOpcoes the openOpcoes to set
     */
    public void setOpenOpcoes(Boolean openOpcoes) {
        this.openOpcoes = openOpcoes;
    }

    /**
     * @return the openOpcoes
     */
    public Boolean getOpenOpcoes() {
        if (openOpcoes == null) {
            openOpcoes = true;
        }
        return openOpcoes;
    }

    /**
     * @param openIntervalo the openIntervalo to set
     */
    public void setOpenIntervalo(Boolean openIntervalo) {
        this.openIntervalo = openIntervalo;
    }

    /**
     * @return the openIntervalo
     */
    public Boolean getOpenIntervalo() {
        if (openIntervalo == null) {
            openIntervalo = true;
        }
        return openIntervalo;
    }

    public void setConfFinanceiro(ConfiguracaoFinanceiroVO confFinanceiro) {
        this.confFinanceiro = confFinanceiro;
    }

    public ConfiguracaoFinanceiroVO getConfFinanceiro() {
        return confFinanceiro;
    }

    public boolean isMostrarCampoEmpresa() {
        return mostrarCampoEmpresa;
    }

    public void setMostrarCampoEmpresa(boolean mostrarCampoEmpresa) {
        this.mostrarCampoEmpresa = mostrarCampoEmpresa;
    }

    
    public final void inicializarEmpresa() throws Exception {
        mostrarCampoEmpresa = validarPermissaoEmpresas();
        if (mostrarCampoEmpresa) {
            setEmpresa(getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_DADOSBASICOS).get(0));
        } else {
            setEmpresa((EmpresaVO) getEmpresaLogado().getClone(true));
        }
        if (getEmpresa() == null) {
            throw new Exception("Empresa Não Encontrada. Entre Novamente no Sistema.");
        }
    }

    public boolean validarPermissaoEmpresas() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setMensagemDetalhada("");
                    setSucesso(true);
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "LancarVisualizarLancamentosEmpresas", "9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas");
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public boolean isAgruparValorProdutoMMasModalidades() {
        return agruparValorProdutoMMasModalidades;
    }

    public void setAgruparValorProdutoMMasModalidades(boolean agruparValorProdutoMMasModalidades) {
        this.agruparValorProdutoMMasModalidades = agruparValorProdutoMMasModalidades;
    }


    public static String formatarDataPadrao(final Date data) {
        try {
            final DateFormat datef = DateFormat.getDateInstance(
                    DateFormat.MEDIUM, Formatador.BRASIL);
            return datef.format(data);
        } catch (Exception e) {
            return null;

        }
    }

    public void setTituloData(String tituloData){
        this.tituloData = tituloData;
    }

    public String getTituloData(){
        return tituloData;
    }

    public void setMesReferencia(boolean mesReferencia){
        this.mesReferencia = mesReferencia;
    }

    public boolean getMesReferencia(){
        return mesReferencia;
    }


    public void tipoDatas(){
        int codigo = tipoRelatorioDf.getCodigo();

        switch (codigo){
            case 1:
                tituloData = "Data Quitação";
                setMostrarFormaPagamento(true);
                setMesReferencia(false);
                break;
            case 2:
                tituloData = "Mês Referencia";
                setMostrarFormaPagamento(false);
                setMesReferencia(true);
                break;
            case 3:
                tituloData = "Data Recebimento";
                setMostrarFormaPagamento(true);
                setMesReferencia(false);
                break;
            case 4:
                tituloData = "Data Lançamento";
                setMostrarFormaPagamento(false);
                setMesReferencia(false);
                break;
            case 5:
                tituloData = "Mês Referencia";
                setMesReferencia(true);
                setMostrarFormaPagamento(false);
                break;
            case 6:
                tituloData = "Mês Referencia";
                setMesReferencia(true);
                setMostrarFormaPagamento(false);
                break;
            case 7:
                tituloData = "Mês Referencia";
                setMesReferencia(false);
                setMostrarFormaPagamento(true);
                break;
        }
    }

    public void selecionarTipoRelatorio() {
        try {
            tipoRelatorioDf = (TipoRelatorioDF) ((SelectItem) JSFUtilities.getRequestAttribute("tipoRel")).getValue();
            if(tipoRelatorioDf.getCodigo() != 3){
                setApresentarDevolucoesRel(true);
            }
            tipoDatas();
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }

    }

    public List<ObjetoTreeTO> getListaCentroCustoTree() {
        if (listaCentroCustoTree == null){
            listaCentroCustoTree = new ArrayList<ObjetoTreeTO>();
        }
        return listaCentroCustoTree;
    }

    public void setListaCentroCustoTree(List<ObjetoTreeTO> listaCentroCustoTree) {
        this.listaCentroCustoTree = listaCentroCustoTree;
    }

    public List<ContaVO> getContasFiltro() {
        return contasFiltro;
    }

    public void setContasFiltro(List<ContaVO> contasFiltro) {
        this.contasFiltro = contasFiltro;
    }

    public String getPlanosSelecionados() {
        if (planosSelecionados == null) {
            planosSelecionados = "";
        }
        return planosSelecionados;
    }

    /**
     * @param planosSelecionados the planosSelecionados to set
     */
    public void setPlanosSelecionados(String planosSelecionados) {
        this.planosSelecionados = planosSelecionados;
    }

    /**
     * @return the codigosPlanosSelecionados
     */
    /**
     * @return the centrosSelecionados
     */
    public String getCentrosSelecionados() {
        if (centrosSelecionados == null) {
            centrosSelecionados = "";
        }
        return centrosSelecionados;
    }

    /**
     * @param centrosSelecionados the centrosSelecionados to set
     */
    public void setCentrosSelecionados(String centrosSelecionados) {
        this.centrosSelecionados = centrosSelecionados;
    }

    /**
     * @return the planosSelecionados
     */

    public void listarPlanoContas() throws Exception {

    }
    public void setListaPlanoContaTree(List<ObjetoTreeTO> listaPlanoContaTree) {

    }
    public void ajustarCodigoAgrupador(ObjetoTreeTO obj) {

    }

    public List<Integer> getCodigosPlanosSelecionados() {
        return codigosPlanosSelecionados;
    }

    public void setCodigosPlanosSelecionados(List<Integer> codigosPlanosSelecionados) {
        this.codigosPlanosSelecionados = codigosPlanosSelecionados;
    }

    public List<ObjetoTreeTO> getListaPlanoContaTree() {
        return listaPlanoContaTree;
    }
    public void listarCentroCustos() throws Exception {

    }
    public void montarArvoreFiltros() throws Exception {
        listarPlanoContas();
        listarCentroCustos();
        setContasFiltro(getFacade().getFinanceiro().getConta().consultarContasSimples(getEmpresaLogado().getCodigo(), false));
        getContasFiltro().add(new ContaVO(-1));
    }

    public boolean isApresentarDevolucoesRel() {
        return apresentarDevolucoesRel;
    }

    public void setApresentarDevolucoesRel(boolean apresentarDevolucoesRel) {
        this.apresentarDevolucoesRel = apresentarDevolucoesRel;
    }
}

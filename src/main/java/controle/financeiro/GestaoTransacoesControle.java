/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.to.GrupoParcelasTO;
import br.com.pactosolucoes.comuns.to.GrupoTransacoesErrosTO;
import br.com.pactosolucoes.comuns.to.GrupoTransacoesTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.view.DataScrollerControle;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.facade.jdbc.utilitarias.Conexao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import org.json.JSONObject;
import org.richfaces.component.html.HtmlDataTable;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.interfaces.AprovacaoServiceInterface;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class GestaoTransacoesControle extends SuperControleRelatorio {

    private List<RemessaVO> listaRemessas;
    private ListaPaginadaTO listaTransacao;
    private ListaPaginadaTO listaApresenta;
    private List<TransacaoVO> listaTransacoes;
    private List<TransacaoVO> listaTransacoesVerificacao;
    private List<TransacaoVO> listaApresentar;
    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private RemessaVO remessaVO = new RemessaVO();
    private TransacaoVO transacaoVO = new TransacaoVO();
    private List<GrupoTransacoesTO> totalQuantidadeAgrupadoPorSituacao = new ArrayList();
    private List<GrupoTransacoesTO> totalValorAgrupadoPorSituacao = new ArrayList();
    private List<GrupoParcelasTO> totalParcelasAgrupadoPorSituacao = new ArrayList();
    //Repescagem
    private List<GrupoTransacoesTO> totalRepescagemPorSituacao = new ArrayList();
    private boolean exibirModalParametros = false;
    private boolean exibirTransacao = false;
    private List<ObjetoGenerico> listaParametrosSelecionado = new ArrayList();
    private ContratoRecorrenciaVO contratoRecorrencia;
    private ClienteVO cliente = new ClienteVO();
    private SituacaoTransacaoEnum situacaoSelecionada = SituacaoTransacaoEnum.NENHUMA;
    private ConfiguracaoSistemaVO configSistema;
    private Integer totalCobradas = 0;
    private TipoTransacaoEnum tipoTransacao;
    private CodigoRetornoPactoEnum retornoPacto;
    private EmpresaVO empresaCreditoVO;
    private List<SelectItem> listaSelectItemEmpresa;
    private Integer empresaFiltro;
    private boolean consultarInfoTodasEmpresas = false;
    private String filtro;
    private boolean impressao = false;
    private String onCompleteDetalhes = "";
    private String onComplete;
    private String emailEnviar;

    private static final String LISTA_TRANSACOES = "LISTA_TRANSACOES";
    private static final String LISTA_APRESENTAR = "LISTA_APRESENTAR";
    private static final Integer LISTA_PAGINADA_LIMIT = 10;

    private List<GrupoTransacoesErrosTO> listaErrosTipoTransacao;
    private List<GrupoTransacoesErrosTO> totalizadorTransacoesPorTipo;

    private List<EmpresaVO> empresasCadastradas;
    private Map<Integer, EmpresaVO> mapaEmpresaVO;
    private List<ParcelaTransacaoVO> listaParcelasTransacao = new ArrayList();
    private boolean apresentarCobrancaVerificarCartao = false;
    private Integer convenioCobrancaRetentativa;
    private List<ConvenioCobrancaVO> listaConvenioCobrancaRetentativa;
    private Boolean consultarParaExportar = true;

    private boolean permissaoEstronarTransacao;

    public GestaoTransacoesControle() throws Exception {

        configSistema = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        //service = new AprovaFacilService(Conexao.getFromSession());
        obterEmpresa();
        montarListaSelectItemEmpresa();
        atualizarSaldo();
    }

    private void inicializarPaginacao() throws Exception {
        listaTransacao = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        listaApresenta = new ListaPaginadaTO(LISTA_PAGINADA_LIMIT);
        validaPermissaoVisuaalizarBotao();
    }

    private void obterEmpresa() {
        try {
            this.setConsultarInfoTodasEmpresas(permissao("ConsultarInfoTodasEmpresas"));

            if (getUsuarioLogado().getAdministrador() || isConsultarInfoTodasEmpresas()) {
                setEmpresaFiltro(0);
            } else {
                setEmpresaFiltro(getEmpresaLogado().getCodigo());
            }
        } catch (Exception ex) {
            setEmpresaFiltro(0);
        }
    }

    public RemessaVO getRemessaVO() {
        return remessaVO;
    }

    public void setRemessaVO(RemessaVO remessaVO) {
        this.remessaVO = remessaVO;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public List<RemessaVO> getListaRemessas() {
        return listaRemessas;
    }

    public void setListaRemessas(List<RemessaVO> listaRemessas) {
        this.listaRemessas = listaRemessas;
    }

    public ListaPaginadaTO getListaTransacao() {
        if(listaTransacao == null){
            listaTransacao = new ListaPaginadaTO(10);
        }
        return listaTransacao;
    }

    public ListaPaginadaTO getListaApresenta() {
        return listaApresenta;
    }

    public List<TransacaoVO> getListaTransacoes() {
        if (listaTransacoes == null) {
            listaTransacoes = new ArrayList<>();
        }
        return listaTransacoes;
    }

    public List<TransacaoVO> getListaTransacoesVerificacao() {
        if (listaTransacoesVerificacao == null){
            listaTransacoesVerificacao = new ArrayList<>();
        }

        return listaTransacoesVerificacao;
    }

    public void setListaTransacoesVerificacao(List<TransacaoVO> listaTransacoesVerificacao) {
        this.listaTransacoesVerificacao = listaTransacoesVerificacao;
    }

    public List<TransacaoVO> getListaTransacoesExportar() {
        return this.listaTransacoes;
    }

    public List<TransacaoVO> getListaTransacoesVerificacaoExportar() {
        return this.listaTransacoesVerificacao;
    }
    public void setListaTransacao(ListaPaginadaTO listaTransacao) {
        this.listaTransacao = listaTransacao;
    }

    public void setListaApresenta(ListaPaginadaTO listaApresenta) {
        this.listaApresenta = listaApresenta;
    }

    public void setListaTransacoes(List<TransacaoVO> listaTransacoes) {
        this.listaTransacoes = listaTransacoes;
    }

    public List<GrupoTransacoesTO> getTotalQuantidadeAgrupadoPorSituacao() {
        return totalQuantidadeAgrupadoPorSituacao;
    }

    public List<GrupoTransacoesTO> getTotalValorAgrupadoPorSituacao() {
        return totalValorAgrupadoPorSituacao;
    }

    public boolean isExibirModalParametros() {
        return exibirModalParametros;
    }

    public boolean isExibirTransacao() {
        return exibirTransacao;
    }

    public void setExibirTransacao(boolean exibirTransacao) {
        this.exibirTransacao = exibirTransacao;
    }

    public void setExibirModalParametros(boolean exibirModalParametros) {
        this.exibirModalParametros = exibirModalParametros;
    }

    public TransacaoVO getTransacaoVO() {
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public List<ObjetoGenerico> getListaParametrosSelecionado() {
        return listaParametrosSelecionado;
    }

    public void setListaParametrosSelecionado(List<ObjetoGenerico> listaParametrosSelecionado) {
        this.listaParametrosSelecionado = listaParametrosSelecionado;
    }

    public ContratoRecorrenciaVO getContratoRecorrencia() {
        return contratoRecorrencia;
    }

    public void setContratoRecorrencia(ContratoRecorrenciaVO contratoRecorrencia) {
        this.contratoRecorrencia = contratoRecorrencia;
    }

    public SituacaoTransacaoEnum getSituacaoSelecionada() {
        return situacaoSelecionada;
    }

    public void setSituacaoSelecionada(SituacaoTransacaoEnum situacaoSelecionada) {
        this.situacaoSelecionada = situacaoSelecionada;
    }

    public List<SelectItem> getListaSituacoes() {
        return SituacaoTransacaoEnum.getSelectListSituacaoTransacao();
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public List<GrupoParcelasTO> getTotalParcelasAgrupadoPorSituacao() {
        return totalParcelasAgrupadoPorSituacao;
    }

    public void setTotalParcelasAgrupadoPorSituacao(List<GrupoParcelasTO> totalParcelasAgrupadoPorSituacao) {
        this.totalParcelasAgrupadoPorSituacao = totalParcelasAgrupadoPorSituacao;
    }

    public Integer getTotalCobradas() {
        return totalCobradas;
    }

    public void setTotalCobradas(Integer totalCobradas) {
        this.totalCobradas = totalCobradas;
    }

    public List<GrupoTransacoesTO> getTotalRepescagemPorSituacao() {
        return totalRepescagemPorSituacao;
    }

    public void setTotalRepescagemPorSituacao(List<GrupoTransacoesTO> totalRepescagemPorSituacao) {
        this.totalRepescagemPorSituacao = totalRepescagemPorSituacao;
    }

    public String getSomaValores() {
        List<GrupoTransacoesTO> lista = totalValorAgrupadoPorSituacao;
        double soma = 0.0;
        for (GrupoTransacoesTO g : lista) {
            soma += g.getValor();
        }
        return Formatador.formatarValorMonetario(soma);
    }

    public String getSomaValoresParcelas() {
        List<GrupoParcelasTO> lista = totalParcelasAgrupadoPorSituacao;
        double soma = 0.0;
        for (GrupoParcelasTO g : lista) {
            soma += g.getValor();
        }
        return Formatador.formatarValorMonetario(soma);
    }

    public Integer getQuantidadeTotal_TotalQuantidadeAgrupadoPorSituacao() {
        List<GrupoTransacoesTO> lista = totalQuantidadeAgrupadoPorSituacao;
        int total = 0;
        for (GrupoTransacoesTO g : lista) {
            total += g.getQuantidade();
        }
        return total;
    }

    public String getValorTotal_TotalQuantidadeAgrupadoPorSituacao() {
        List<GrupoTransacoesTO> lista = totalQuantidadeAgrupadoPorSituacao;
        Double total = 0.0;
        for (GrupoTransacoesTO g : lista) {
            total += g.getValor();
        }
        return Formatador.formatarValorMonetario(total);
    }

    public Integer getQuantidadesRepescagem() {
        List<GrupoTransacoesTO> lista = totalRepescagemPorSituacao;
        int total = 0;
        for (GrupoTransacoesTO g : lista) {
            total += g.getQuantidade();
        }
        return total;
    }

    public String getSomaValoresRepescagem() {
        List<GrupoTransacoesTO> lista = totalRepescagemPorSituacao;
        double soma = 0.0;
        for (GrupoTransacoesTO g : lista) {
            soma += g.getValor();
        }
        return Formatador.formatarValorMonetario(soma);
    }

    public void selecionarRemessaListener(ActionEvent evt) {
        RemessaVO remessa = (RemessaVO) evt.getComponent().getAttributes().get("remessa");
        if (remessa != null) {
            try {
                getFacade().getZWFacade().getRemessa().preencherTransacoes(remessaVO);
                remessaVO = remessa;
            } catch (Exception ex) {
                setErro(true);
                setMensagem(ex.getMessage());
            }
        }
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(paginacao, codigo);
    }

    public void exibirParams(ActionEvent evt) {
        String params = (String) evt.getComponent().getAttributes().get("params");
        TransacaoVO transacao = (TransacaoVO) evt.getComponent().getAttributes().get("transacao");
        transacaoVO = null;
        listaParametrosSelecionado = null;
        if (params != null && transacao != null) {
            transacaoVO = transacao;
            setExibirModalParametros(true);
            setMensagemDetalhada("");
            setMensagem("");
            if (params.equals("envio")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(transacao.getParamsEnvio());
                } catch (Exception e) {
                    setMensagem("Erro ao obter detalhes do envio!");
                    setMensagemDetalhada(e.getMessage());
                }
            } else if (params.equals("resposta")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(transacao.getParamsResposta());
                }catch (Exception e) {
                    setMensagem("Erro ao obter resposta da administradora, necessário reenviar esta transação!");
                    setMensagemDetalhada(e.getMessage());
                }
            } else if (params.equals("confirmacao")) {
                try {
                    listaParametrosSelecionado = Uteis.obterListaParametrosValores(transacao.getResultadoCaptura());
                } catch (Exception e) {
                    setMensagem("Erro ao obter confirmação da administradora!");
                    setMensagemDetalhada(e.getMessage());
                }
            }
        }
    }

    public void exibirDetalhesTransacao(ActionEvent evt) {
        TransacaoVO transacao = (TransacaoVO) evt.getComponent().getAttributes().get("transacao");
        setMensagem("");
        setMensagemDetalhada("", "");
        contratoRecorrencia = null;
        cliente = null;
        transacaoVO = null;
        if (transacao != null) {
            try {
                EmpresaVO emp = getFacade().getEmpresa().consultarPorCodigo(transacao.getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                String url = ConfiguracaoSistemaVO.obterURLRecorrencia(emp, configSistema);
                transacao.setUrlTransiente(url);
                transacao.setIpTransiente(this.getIpCliente());
                transacaoVO = transacao;
                if (transacaoVO.getCodigo() != 0) {
                    setExibirTransacao(true);

                    transacaoVO.setListaParcelas(getFacade().getZWFacade().getTransacao().obterParcelasDaTransacao(transacao));
                    for (MovParcelaVO obj : transacaoVO.getListaParcelas()) {
                        if (!UteisValidacao.emptyNumber(obj.getPessoa().getCodigo())) {
                            obj.setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                        }
                        validarParcelaRemessa(obj);
                    }

                    if (!UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                        transacaoVO.setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }

                    contratoRecorrencia = getFacade().getZWFacade().getTransacao().
                            obterContratoRecorrenciaPorTransacao(transacao);

                    cliente = getFacade().getZWFacade().
                            getTransacao().obterClientePorTransacao(transacao);
                }
            } catch (Exception ex) {
                setErro(true);
                setMensagem(ex.getMessage());
            }
        }
    }

    private void validaPermissaoVisuaalizarBotao() throws Exception {
        ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
        if (clienteControle.validarPermisaoUsuario("GestaoTransacoes", "4.20 - Gestão Transações")) {
            setPermissaoEstronarTransacao(true);
        } else {
            setPermissaoEstronarTransacao(false);
        }
    }

    public String getAtributosExportar() {
        StringBuilder atributos = new StringBuilder();
        atributos.append("matricula=Matrícula,nomeCliente=Nome,descricaoParcela=Parcela, situacaoParcela_Apresentar=Situação Parcela,valorParcela=Valor Parcela");
        return atributos.toString();
    }

    public void totalizar() throws Exception {
        totalQuantidadeAgrupadoPorSituacao = new ArrayList<>();
        totalParcelasAgrupadoPorSituacao = new ArrayList<>();
        totalizadorTransacoesPorTipo = new ArrayList<>();

        totalQuantidadeAgrupadoPorSituacao = getFacade().getZWFacade().getTransacao().consultarQuantidadeAgrupandoPorSituacao(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(),
                getTipoTransacao(), getRetornoPacto(), getFiltro(), isApresentarCobrancaVerificarCartao());

        totalParcelasAgrupadoPorSituacao = getFacade().getZWFacade().getTransacao().consultarValoresParcelasAgrupandoPorSituacaoParcela(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(),
                getTipoTransacao(), getRetornoPacto(), getFiltro(), isApresentarCobrancaVerificarCartao());

        totalizadorTransacoesPorTipo = getFacade().getZWFacade().getTransacao().consultarTotalizadorTransacaoPortipo(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(),
                getTipoTransacao(), getRetornoPacto(), getFiltro(), isApresentarCobrancaVerificarCartao());

//        totalCobradas = getFacade().getZWFacade().getTransacao().consultarQuantidadeTransacoesASeremCobradas(dataInicio, dataFim, situacaoSelecionada, getEmpresaVO().getCodigo(), getTipoTransacao(), getRetornoPacto(), getFiltro());
//        /**Totais repescagem:
//         * Deve-se sempre exibir a repescagem apenas de um dia, pois, se o período for maior do que isso,
//         * o Sistema irá mostrar o total de transações superior ao total da dívida, pois a mesma parcela poderá estar
//         * sendo referenciada em mais de um dia e os valores da repescagem é baseado em transações e não em parcelas.
//         *
//         * ATUALIZAÇÃO (Joao Alcides)10/03/2015: Foi solicitado que seja totalizado mesmo quando o período for maior que
//         * um dia, somando as transaçoes mas não os valores de parcelas cobradas mais de uma vez.
//         */
//        totalRepescagemPorSituacao = Transacao.consultarGrupoRepescagem(listaTransacoes);
//        listaErrosTipoTransacao = getFacade().getZWFacade().getTransacao().consultarErrosAgrupandoPorTipoTransacao(dataInicio, dataFim, situacaoSelecionada, getEmpresaVO().getCodigo(), getTipoTransacao(), getRetornoPacto(), getFiltro());
    }

    private void validaConsulta() throws Exception {
        // Periodo deve ser informado, nao permitindo dessa forma consultar todas as transacoes
        if (dataInicio == null) {
            throw new Exception("Informe a data de início da consulta.");
        }
        if (dataFim == null) {
            throw new Exception("Informe a data final da consulta.");
        }

        // Data inicial nao pode ser maior que data final
        if (!Calendario.menorOuIgual(dataInicio, dataFim)) {
            throw new Exception("A data inicial da consulta não pode ser maior que a data final.");
        }

        // Limitar periodo entre as datas (inicial e final). Nao pode ser maior que 31 dias
        int dias = Calendario.diferencaEmDias(dataInicio, dataFim);
        if (dias > 150) {
            throw new Exception("A consulta não pode superior a 150 dias.");
        }
    }

    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) throws Exception {
        if (codigo.equals(LISTA_TRANSACOES)) {
            return listaTransacao;
        } else if (codigo.equals(LISTA_APRESENTAR)) {
            return listaApresenta;
        }
        return null;
    }

    private void carregarListaPaginacao(ListaPaginadaTO paginacao, String codigo) throws Exception {
        if (codigo.equals(LISTA_TRANSACOES) || codigo.equals(LISTA_APRESENTAR)) {
            consultarListaTransacoes(paginacao);
        }
    }

    private void consultarListaTransacoes(ListaPaginadaTO paginacao) throws Exception {
        try {

            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);

            listaTransacoes = getFacade().getZWFacade().getTransacao().consultar(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(), getTipoTransacao(), getRetornoPacto(),
                    getFiltro(), paginacao.getLimit(), paginacao.getOffset(), isApresentarCobrancaVerificarCartao(), Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
        } catch (Exception ex) {
            montarErro(ex);
            throw ex;
        } finally {
            CacheControl.clear();
        }
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(paginacao, codigo);
    }

    public void limparFiltro() {
        limparMsg();
        setMsgAlert("");
        setFiltro("");
        consultarTransacoes();
    }

    public void limparConsulta() {
        listaTransacoes = null;
        listaTransacao = null;
        listaApresentar = null;
        listaApresenta = null;
        totalCobradas = 0;
        totalQuantidadeAgrupadoPorSituacao = new ArrayList<>();
        totalParcelasAgrupadoPorSituacao = new ArrayList<>();
        totalizadorTransacoesPorTipo = new ArrayList<>();
    }

    public void consultarTransacoes() {
        try {
            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);

            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();

            limparConsulta();
            limparMsg();
            setMsgAlert("");
            setOnComplete("");

            validaConsulta();

            inicializarPaginacao();

            listaTransacoes = getFacade().getZWFacade().getTransacao().consultar(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(), getTipoTransacao(), getRetornoPacto(), getFiltro(),
                    LISTA_PAGINADA_LIMIT, 0, isApresentarCobrancaVerificarCartao(), Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            if (UteisValidacao.emptyList(listaTransacoes)) {
                throw new Exception("Nenhum registro encontrado no período.");
            }

            //preencher a empresa
            for (TransacaoVO obj : getListaTransacoes()) {
                preencherEmpresaVO(obj);
            }

            listaTransacao.setCount(getFacade().getZWFacade().getTransacao().obterCountTransacoes(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(),
                    getTipoTransacao(), getRetornoPacto(), getFiltro(), isApresentarCobrancaVerificarCartao()));
            totalizar();

            atualizarSaldo();
            notificarRecursoEmpresa(RecursoSistema.GESTAO_TRANSACAO, ContadorTempo.encerraContagem(), Calendario.diferencaEmDias(dataInicio, dataFim)+1);
            setOnComplete("Notifier.cleanAll()");
        } catch (Exception ex) {
            montarErro(ex);
        } finally {
            CacheControl.clear();
        }
    }

    private void preencherEmpresaVO(TransacaoVO obj) {
        if (!UteisValidacao.emptyNumber(obj.getEmpresa())) {
            EmpresaVO empresaVO = getMapaEmpresaVO().get(obj.getEmpresa());
            if (empresaVO != null) {
                obj.setEmpresaVO(empresaVO);
            }
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        try {

            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);

            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());

            List<TransacaoVO> listaParaImpressao = new ArrayList<>();
            if (this.getConsultarParaExportar() == null || this.getConsultarParaExportar()) {
                listaParaImpressao = getFacade().getZWFacade().getTransacao().consultar(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(), getTipoTransacao(),
                        getRetornoPacto(), getFiltro(), null, null, isApresentarCobrancaVerificarCartao(), Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            } else {
                listaParaImpressao.addAll(this.getListaTransacoes());
            }

            //preencher a empresa
            for (TransacaoVO obj : listaParaImpressao) {
                preencherEmpresaVO(obj);
            }

            exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
            listaParaImpressao = null;
        } catch (Exception ex) {
            montarErro(ex);
            throw ex;
        } finally {
            CacheControl.clear();
        }
    }

    public void novo() {
        this.setExibirModalParametros(false);
        this.setExibirTransacao(false);
        this.setDataInicio(Calendario.hoje());
        this.setDataFim(Calendario.hoje());
        this.setListaManipulavel(new ArrayList<TransacaoVO>());
        this.setListaTransacoes(new ArrayList<>());
        this.setValorPesquisa("");
        this.totalQuantidadeAgrupadoPorSituacao = new ArrayList<>();
        this.totalParcelasAgrupadoPorSituacao = new ArrayList<>();
        this.totalizadorTransacoesPorTipo = new ArrayList<>();
        this.setConsultarParaExportar(true);
    }

    public void fecharPanelDadosParametros() {
        this.setExibirModalParametros(false);
        this.setExibirTransacao(false);
    }

    public void repetirTransacao() {
        try {
            limparMsg();
            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Transação inválida");
            }

            TransacaoVO transacaoAnterior = transacaoVO;
            TransacaoVO transacaoNova = (TransacaoVO) transacaoVO.getClone(true);
            //
            SituacaoTransacaoEnum sitAnterior = transacaoAnterior.getSituacao();
            transacaoNova.setUsuarioResponsavel(getUsuarioLogado());
            getServiceParaTransacao(transacaoNova).retransmitirTransacao(transacaoNova, contratoRecorrencia, cliente);
            SituacaoTransacaoEnum sitNova = transacaoNova.getSituacao();
            //
            if (((sitAnterior != sitNova) && (sitNova == SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO))
                    || transacaoAnterior.getTipo().equals(TipoTransacaoEnum.CIELO_ONLINE)
                    || transacaoAnterior.getTipo().equals(TipoTransacaoEnum.E_REDE)) {
                //
                transacaoAnterior.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
                getFacade().getZWFacade().getTransacao().alterar(transacaoAnterior);
                //
                String msg = "Transação concluída com sucesso! "
                        + "Esta transação foi descartada e gerada uma nova: " + transacaoNova.getCodigoExterno()
                        + " em: " + Uteis.getDataComHora(transacaoNova.getDataProcessamento());
                setMensagem(msg);
                montarSucessoGrowl(msg);
                totalizar();
            } else {
                throw new Exception("Não foi possível concluir a transação: " + transacaoNova.getValorAtributoResposta(APF.ResultSolicAprovacao));
            }
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    private AprovacaoServiceInterface getServiceParaTransacao(TransacaoVO transacaoVO) throws Exception {
        return CobrancaOnlineService.getImplementacaoAprovacaoService(transacaoVO.getTipo(), transacaoVO.getEmpresa(),
                transacaoVO.getConvenioCobrancaVO().getCodigo(), transacaoVO.getConvenioCobrancaVO().isPactoPay(), Conexao.getFromSession());
    }

    public void confirmarTransacao() {
        try {
            limparMsg();

            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Transação inválida");
            }

            TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);
            SituacaoTransacaoEnum sitAnterior = transacaoAnterior.getSituacao();
            getServiceParaTransacao(transacaoVO).confirmarTransacao(transacaoVO, contratoRecorrencia, cliente);
            SituacaoTransacaoEnum sitNova = transacaoVO.getSituacao();
            getFacade().getTransacao().gravarLogTransacao("TRANSACAO-CONFIRMADA", transacaoAnterior, transacaoVO, getUsuarioLogado());
            if ((sitAnterior != sitNova) && (sitNova == SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                String msg ="Transação confirmada com sucesso!";
                setMensagem(msg);
                montarSucessoGrowl(msg);
                totalizar();
            } else {
                throw new Exception("Não foi possível confirmar a transação: " + transacaoVO.getValorAtributoCaptura(APF.ResultSolicConfirmacao));
            }
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    private void carregarDadosTransacao(TransacaoVO transacao) throws Exception {
        contratoRecorrencia = null;
        cliente = null;
        transacaoVO = null;
        if (transacao != null && !UteisValidacao.emptyNumber(transacao.getCodigo())) {
            setTransacaoVO(transacao);

            EmpresaVO emp = getFacade().getEmpresa().consultarPorCodigo(getTransacaoVO().getEmpresa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            getTransacaoVO().setEmpresaVO(emp);
            String url = ConfiguracaoSistemaVO.obterURLRecorrencia(emp, configSistema);
            getTransacaoVO().setUrlTransiente(url);
            getTransacaoVO().setIpTransiente(this.getIpCliente());

            transacaoVO.setListaParcelas(getFacade().getZWFacade().getTransacao().obterParcelasDaTransacao(getTransacaoVO()));
            for (MovParcelaVO obj : transacaoVO.getListaParcelas()) {
                validarParcelaRemessa(obj);
            }
            contratoRecorrencia = getFacade().getZWFacade().getTransacao().obterContratoRecorrenciaPorTransacao(getTransacaoVO());
            cliente = getFacade().getZWFacade().getTransacao().obterClientePorTransacao(getTransacaoVO());
        }
    }

    public void abrirModalEnviarEmailComprovanteCancelamento() {
        try {
            limparMsg();
            setOnComplete("");

            TransacaoVO obj = (TransacaoVO) context().getExternalContext().getRequestMap().get("transacao");
            carregarDadosTransacao(obj);
            setTransacaoVO(obj);

            try {
                List<EmailVO> emailVOList = getFacade().getEmail().consultarEmails(getTransacaoVO().getPessoaPagador().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (EmailVO emailVO : emailVOList) {
                    if (!UteisValidacao.emptyString(emailVO.getEmail())) {
                        setEmailEnviar(emailVO.getEmail());
                        break;
                    }
                }
            } catch (Exception ignored) {
            }

            setOnComplete("Richfaces.showModalPanel('modalEnviarEmailTransacao')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void enviarEmailComprovanteCancelamento() {
        try {
            limparMsg();
            setOnComplete("");

            getFacade().getTransacao().enviarEmailComprovanteCancelamento(getEmailEnviar(), transacaoVO);

            montarSucessoGrowl("E-mail enviado com sucesso!");
            setOnComplete("Richfaces.hideModalPanel('modalEnviarEmailTransacao')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void abrirModalCancelarTransacao() {
        try {
            limparMsg();
            setOnComplete("");

            TransacaoVO obj = (TransacaoVO) context().getExternalContext().getRequestMap().get("transacao");
            carregarDadosTransacao(obj);

            setOnComplete("Richfaces.showModalPanel('modalCancelarTransacao')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void cancelarTransacao() {
        try {
            limparMsg();
            setOnComplete("");

            if (transacaoVO.getClienteVO() == null) {
                transacaoVO.setClienteVO(cliente);
            }
            getFacade().getTransacao().cancelarTransacao(transacaoVO, true, getUsuarioLogado(), getKey());
            atualizarTransacaoNaLista(transacaoVO, true);

            setOnComplete("Richfaces.hideModalPanel('modalCancelarTransacao')");
            montarSucessoGrowl("Transação cancelada com sucesso.");
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    private void atualizarTransacaoNaLista(TransacaoVO transacaoNovaVO, boolean consultar) {
        if (consultar) {
            try {
                transacaoNovaVO = getFacade().getTransacao().consultarPorChavePrimaria(transacaoVO.getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOTRANSACAO);
            } catch (Exception ignored) {
            }
        }
        preencherEmpresaVO(transacaoNovaVO);

        TransacaoVO transacaoAnterior = null;
        for (TransacaoVO obj : getListaTransacoes()) {
            if (transacaoNovaVO.getCodigo().equals(obj.getCodigo())) {
                transacaoAnterior = obj;
                break;
            }
        }

        if (transacaoAnterior != null) {
            int posicao = getListaTransacoes().indexOf(transacaoAnterior);
            getListaTransacoes().remove(transacaoAnterior);
            getListaTransacoes().add(posicao, transacaoNovaVO);
        }
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
    }

    public void descartarTransacao() {
        try {
            limparMsg();
            if (transacaoVO == null || UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                throw new Exception("Transação inválida");
            }

            TransacaoVO transacaoAnterior = (TransacaoVO) transacaoVO.getClone(true);
            SituacaoTransacaoEnum sitAnterior = transacaoAnterior.getSituacao();
            getServiceParaTransacao(transacaoVO).descartarTransacao(transacaoVO);
            SituacaoTransacaoEnum sitNova = transacaoVO.getSituacao();
            getFacade().getTransacao().gravarLogTransacao("TRANSACAO-DESCARTADA", transacaoAnterior, transacaoVO, getUsuarioLogado());
            if ((sitAnterior != sitNova) && (sitNova == SituacaoTransacaoEnum.DESCARTADA)) {
                String msg = "Transação descartada com sucesso!";
                setMensagem(msg);
                montarSucessoGrowl(msg);
                totalizar();
            } else {
                throw new Exception("Não foi possível descartar a transação.");
            }
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    private void prepareParams(Map<String, Object> params) throws Exception {
        EmpresaVO empre = new EmpresaVO();
        if (!UteisValidacao.emptyNumber(getEmpresaFiltro())) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltro(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }

        List<TransacaoVO> listaClonada = new ArrayList();
        try {
            listaClonada.addAll(getListaApresentar());
            Ordenacao.ordenarLista(listaClonada, "nomePessoa");

            params.put("nomeRelatorio", "GestaoTransacoes");
            params.put("nomeEmpresa", empre.getNome());

            params.put("tituloRelatorio", "Relatório Gestão de Transações");
            params.put("nomeDesignIReport", getDesignIReportRelatorio());
            params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
            params.put("listaObjetos", listaClonada);

            params.put("filtros", getFiltrosDescricao());
            params.put("dataIni", Uteis.getData(dataInicio));
            params.put("dataFim", Uteis.getData(dataFim));
            params.put("enderecoEmpresa", empre.getEndereco());
            params.put("cidadeEmpresa", empre.getCidade().getNome());
            JRDataSource jr1 = new JRBeanArrayDataSource(totalQuantidadeAgrupadoPorSituacao.toArray(), false);
            JRDataSource jr2 = new JRBeanArrayDataSource(totalValorAgrupadoPorSituacao.toArray(), false);
            JRDataSource jr3 = new JRBeanArrayDataSource(totalParcelasAgrupadoPorSituacao.toArray(), false);

            params.put("dadosTotalPorSituacao", jr1);
            params.put("dadosValoresPorSituacao", jr2);
            params.put("dadosValoresParcelas", jr3);
            params.put("totalTransacoesCobradas", totalCobradas);
            params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        } finally {
            listaClonada = null;
        }
    }

    public void imprimirRelatorio() {
        try {

            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros);
            apresentarRelatorioObjetos(parametros);
        } catch (Exception e) {
            setErro(true);
            setMensagem(e.getMessage());
        }
    }

    public void teste() {
        try {
            DataScrollerControle dsc = (DataScrollerControle) JSFUtilities.getFromSession(DataScrollerControle.class);
            List lista = JSFUtilities.getListFromDataScroller((HtmlDataTable) dsc.getDataScroller().getDataTable());
            System.out.println(JSFUtilities.printListToTableHTML(lista));
        } catch (Exception e) {
            setErro(true);
            setMensagem(e.getMessage());
        }
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "GestaoTransacoes.jrxml");
    }

    public String getFiltrosDescricao() {
        String filtros = "Intervalo de Data: " + Uteis.getData(this.getDataInicio()) + "-"
                + Uteis.getData(this.getDataFim()) + "<br/>";
        filtros += "Situação: " + (this.getSituacaoSelecionada() == null ? "Todas" : this.getSituacaoSelecionada().getDescricao());
        return filtros;
    }

    @Override
    public void filtrarPorTexto() {
        String valorPesquisa = getFiltro().toUpperCase();
        if (UteisValidacao.emptyString(valorPesquisa)) {
            setListaApresentar(getListaTransacoes());
        } else {
            setListaApresentar(new ArrayList<TransacaoVO>());
            for (Object object : getListaTransacoes()) {
                TransacaoVO transacaoVO = (TransacaoVO) object;
                if (transacaoVO.getCartaoMascarado().toUpperCase().contains(valorPesquisa) ||
                        transacaoVO.getAutorizacao().toUpperCase().contains(valorPesquisa) ||
                        transacaoVO.getReciboPagamento().toString().toUpperCase().contains(valorPesquisa) ||
                        transacaoVO.getBandeira().toUpperCase().contains(valorPesquisa) ||
                        transacaoVO.getUsuario_Apresentar().toUpperCase().contains(valorPesquisa) ||
                        transacaoVO.getNomePessoa().toUpperCase().contains(valorPesquisa)) {
                    getListaApresentar().add(transacaoVO);
                }
            }
        }
        DataScrollerControle dataScroller = (DataScrollerControle) getControlador(DataScrollerControle.class.getSimpleName());
        dataScroller.getDataScroller().getDataTable().setValue(getListaApresentar());
    }

    public void validarParcelaRemessa(MovParcelaVO mp) throws Exception {
        mp.setEmRemessaAindaNaoProcessada(false);
        List<RemessaItemVO> itensRemessa = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoParcela(mp.getCodigo(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!itensRemessa.isEmpty()) {
            for (RemessaItemVO item : itensRemessa) {
                if (item.getRemessa().isAguardandoRetorno() && !item.getProps().containsKey(DCCAttEnum.StatusVenda.name())) {
                    mp.setEmRemessaAindaNaoProcessada(true);
                    break;
                }
            }
        }
    }

    public List<SelectItem> getListaTipoTransacao() {
        return TipoTransacaoEnum.getSelectListTipoTransacao();
    }

    public TipoTransacaoEnum getTipoTransacao() {
        return tipoTransacao;
    }

    public void setTipoTransacao(TipoTransacaoEnum tipoTransacao) {
        this.tipoTransacao = tipoTransacao;
    }

    public CodigoRetornoPactoEnum getRetornoPacto() {
        return retornoPacto;
    }

    public void setRetornoPacto(CodigoRetornoPactoEnum retornoPacto) {
        this.retornoPacto = retornoPacto;
    }

    public List<SelectItem> getListaRetornoPacto() {
        return CodigoRetornoPactoEnum.getSelectListRetornoPactoPagamento();
    }

    public boolean getApresentarFiltroRetornoPacto() {
//        return getTipoTransacao() != null && getTipoTransacao().equals(TipoTransacaoEnum.E_REDE);
        return false;
    }

    private void atualizarSaldo() throws Exception {
        if (!UteisValidacao.emptyNumber(getEmpresaFiltro())) {

            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaFiltro(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            if (TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.equals(TipoCobrancaPactoEnum.getConsultarPorCodigo(empresaVO.getTipoCobrancaPacto()))) {
                try {
                    JSONObject jsonObject = getFacade().getEmpresa().obterInfoRedeDCC(Uteis.getUrlOamd(), getKey());
                    setEmpresaCreditoVO(new EmpresaVO());
                    getEmpresaCreditoVO().setCreditoDCC(jsonObject.getInt("creditos"));
                    getEmpresaCreditoVO().setDataExpiracaoCreditoDCC(getEmpresaLogado().getDataExpiracaoCreditoDCC());
                    getEmpresaCreditoVO().setTipoCobrancaPacto(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                setEmpresaCreditoVO(empresaVO);
            }
        } else {
            setEmpresaCreditoVO(new EmpresaVO());
        }
    }

    public String getSituacaoCreditoPacto() {
        if (UteisValidacao.emptyNumber(getEmpresaFiltro())) {
            return "Selecione uma empresa para verificar o saldo.";
        } else if (getEmpresaCreditoVO().getCreditoDCC() >= RemessaService.PADRAO_CREDITO_DCC) {
            return "Seu saldo é de " + getEmpresaCreditoVO().getCreditoDCC() + " crédito(s).";
        } else if (getEmpresaCreditoVO().getCreditoDCC() >= RemessaService.INICIO_LIMITE_EMERGENCIAL_DCC) {
            return "Seu saldo está no fim! Restam apenas " + getEmpresaCreditoVO().getCreditoDCC() + " crédito(s).";
        } else if (getEmpresaCreditoVO().getCreditoDCC() > RemessaService.LIMITE_EMERGENCIAL_DCC) {
            return "O limite emergencial de crédito foi ativado. Você já consumiu " + (-1 * getEmpresaCreditoVO().getCreditoDCC()) + " de " + (-1 * RemessaService.LIMITE_EMERGENCIAL_DCC) + " crédito(s).";
        } else {
            return "Você não possui saldo de transações.";
        }
    }

    public String getStyleSituacaoCreditoPacto() {
        if (UteisValidacao.emptyNumber(getEmpresaFiltro())) {
            return "color: #474747; font-weight: normal;";
        } else if (getEmpresaCreditoVO().getCreditoDCC() >= RemessaService.PADRAO_CREDITO_DCC) {
            return "color: green; font-weight: normal;";
        } else if (getEmpresaCreditoVO().getCreditoDCC() >= RemessaService.INICIO_LIMITE_EMERGENCIAL_DCC) {
            return "color: olive; font-weight: normal;";
        } else if (getEmpresaCreditoVO().getCreditoDCC() > RemessaService.LIMITE_EMERGENCIAL_DCC) {
            return "color: red; font-weight: normal;";
        } else {
            return "color: red; font-weight: normal;";
        }
    }

    public String getSituacaoDataCreditoPacto() {
        if (getEmpresaCreditoVO().getDataExpiracaoCreditoDCC() == null) {
            return "";
        } else if (Calendario.maior(getEmpresaCreditoVO().getDataExpiracaoCreditoDCC(), Calendario.hoje())) {
            return "Seus créditos serão bloqueados no dia " + Uteis.getData(getEmpresaCreditoVO().getDataExpiracaoCreditoDCC());
        } else {
            return "Seus créditos estão bloqueados.";
        }
    }

    public String getStyleSituacaoDataCreditoPacto() {
        if (getEmpresaCreditoVO().getDataExpiracaoCreditoDCC() == null) {
            return "color: green; font-weight: normal;";
        } else if (Calendario.maior(getEmpresaCreditoVO().getDataExpiracaoCreditoDCC(), Calendario.hoje())) {
            return "color: olive; font-weight: normal;";
        } else {
            return "color: red; font-weight: normal;";
        }
    }

    public EmpresaVO getEmpresaCreditoVO() {
        if (empresaCreditoVO == null) {
            empresaCreditoVO = new EmpresaVO();
        }
        return empresaCreditoVO;
    }

    public void setEmpresaCreditoVO(EmpresaVO empresaCreditoVO) {
        this.empresaCreditoVO = empresaCreditoVO;
    }

    public void montarListaSelectItemEmpresa() {
        try {
            setMapaEmpresaVO(new HashMap<>());

            setEmpresasCadastradas(getFacade().getEmpresa().consultarPorCodigo(0, null, false, Uteis.NIVELMONTARDADOS_MINIMOS));

            List<SelectItem> objs = new ArrayList<SelectItem>();
            for (EmpresaVO obj : getEmpresasCadastradas() ) {
                getMapaEmpresaVO().put(obj.getCodigo(), obj);
                if (obj.isAtiva()){
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                }
            }

            Ordenacao.ordenarLista(objs, "label");
            if (getEmpresasCadastradas().size() > 1) {
                objs.add(0, new SelectItem(null, "TODAS"));
                setEmpresaFiltro(getEmpresaLogado().getCodigo());
            } else {
                setEmpresaFiltro((Integer) objs.get(0).getValue());
            }

            setListaSelectItemEmpresa(objs);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList<SelectItem>();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public boolean isApresentarColunaEmpresa() {
        return UteisValidacao.emptyNumber(getEmpresaFiltro());
    }

    public boolean isApresentarColunaRepescagem() {
        for (TransacaoVO tran : getListaTransacoes()) {
            if (tran.getTipo().equals(TipoTransacaoEnum.AprovaFacilCB)) {
                return true;
            }
        }
        return false;
    }

    public void selecionouEmpresa() {
        try {
            limparConsulta();
            atualizarSaldo();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isConsultarInfoTodasEmpresas() {
        return consultarInfoTodasEmpresas;
    }

    public void setConsultarInfoTodasEmpresas(boolean consultarInfoTodasEmpresas) {
        this.consultarInfoTodasEmpresas = consultarInfoTodasEmpresas;
    }

    public List<TransacaoVO> getListaApresentar() {
        if (listaApresentar == null) {
            listaApresentar = new ArrayList<TransacaoVO>();
        }
        return listaApresentar;
    }

    public void setListaApresentar(List<TransacaoVO> listaApresentar) {
        this.listaApresentar = listaApresentar;
    }

    public String getFiltro() {
        if (filtro == null) {
            filtro = "";
        }
        return filtro.trim();
    }

    public void setFiltro(String filtro) {
        this.filtro = filtro;
    }

    public boolean isImpressao() {
        return impressao;
    }

    public void setImpressao(boolean impressao) {
        this.impressao = impressao;
    }

    public String getOnCompleteDetalhes() {
        return onCompleteDetalhes;
    }

    public void setOnCompleteDetalhes(String onCompleteDetalhes) {
        this.onCompleteDetalhes = onCompleteDetalhes;
    }

    public void abrirTelaClienteColaboradorTransacao(ActionEvent evt) throws Exception {
        ClienteControle cliControle = (ClienteControle) getControlador(ClienteControle.class);
        ColaboradorControle colaboradorControle = (ColaboradorControle) getControlador(ColaboradorControle.class);

        PessoaVO pessoa = (PessoaVO) evt.getComponent().getAttributes().get("pessoa");
        Boolean impressao = Boolean.valueOf((String) evt.getComponent().getAttributes().get("impressao"));

        setImpressao(impressao);

        if (pessoa != null) {
            ClienteVO c = getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (c.getCodigo() != 0) {
                cliControle.setarCliente(c);
                cliControle.acaoAjax();
                if (impressao) {
                    setOnCompleteDetalhes("abrirPopup('../../clienteNav.jsp?page=viewCliente', 'ClienteGestaoTransacao', 1024, 700);");
                } else {
                    setOnCompleteDetalhes("abrirPopup('clienteNav.jsp?page=viewCliente', 'ClienteGestaoTransacao', 1024, 700)");
                }
            } else {
                ColaboradorVO col = getFacade().getColaborador().consultarPorCodigoPessoa(pessoa.getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                colaboradorControle.prepararEditarColaborador(col);
                if (impressao) {
                    setOnCompleteDetalhes("abrirPopup('../../colaboradorForm.jsp', 'ColaboradorGestaoTransacao', 1024, 700);");
                } else {
                    setOnCompleteDetalhes("abrirPopup('colaboradorForm.jsp', 'ColaboradorGestaoTransacao', 1024, 700)");
                }
            }
        }
    }

    public List<GrupoTransacoesErrosTO> getListaErrosTipoTransacao() {
        if (listaErrosTipoTransacao == null) {
            listaErrosTipoTransacao = new ArrayList<>();
        }
        return listaErrosTipoTransacao;
    }

    public void setListaErrosTipoTransacao(List<GrupoTransacoesErrosTO> listaErrosTipoTransacao) {
        this.listaErrosTipoTransacao = listaErrosTipoTransacao;
    }

    public Integer getTotalizadorTransacoesPorTipo_Size() {
        return getTotalizadorTransacoesPorTipo().size();
    }

    public List<GrupoTransacoesErrosTO> getTotalizadorTransacoesPorTipo() {
        if (totalizadorTransacoesPorTipo == null) {
            totalizadorTransacoesPorTipo = new ArrayList<>();
        }
        return totalizadorTransacoesPorTipo;
    }

    public void setTotalizadorTransacoesPorTipo(List<GrupoTransacoesErrosTO> totalizadorTransacoesPorTipo) {
        this.totalizadorTransacoesPorTipo = totalizadorTransacoesPorTipo;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public String getEmailEnviar() {
        if (emailEnviar == null) {
            emailEnviar = "";
        }
        return emailEnviar;
    }

    public void setEmailEnviar(String emailEnviar) {
        this.emailEnviar = emailEnviar;
    }

    public List<EmpresaVO> getEmpresasCadastradas() {
        if (empresasCadastradas == null) {
            empresasCadastradas = new ArrayList<>();
        }
        return empresasCadastradas;
    }

    public void setEmpresasCadastradas(List<EmpresaVO> empresasCadastradas) {
        this.empresasCadastradas = empresasCadastradas;
    }

    public Integer getEmpresaFiltro() {
        return empresaFiltro;
    }

    public void setEmpresaFiltro(Integer empresaFiltro) {
        this.empresaFiltro = empresaFiltro;
    }

    public Map<Integer, EmpresaVO> getMapaEmpresaVO() {
        if (mapaEmpresaVO == null) {
            mapaEmpresaVO = new HashMap<>();
        }
        return mapaEmpresaVO;
    }

    public void setMapaEmpresaVO(Map<Integer, EmpresaVO> mapaEmpresaVO) {
        this.mapaEmpresaVO = mapaEmpresaVO;
    }

    public void abrirModalSincronizarTransacao() {
        try {
            limparMsg();
            setOnComplete("");

            TransacaoVO obj = (TransacaoVO) context().getExternalContext().getRequestMap().get("transacao");
            carregarDadosTransacao(obj);

            setOnComplete("Richfaces.showModalPanel('modalSincronizarTransacao')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void sincronizarTransacao() {
        try {
            limparMsg();
            setOnComplete("");

            transacaoVO.setOrigemSincronizacao("ZW_GESTAO_TRANSACAO");
            String retorno = getFacade().getTransacao().sincronizarTransacao(transacaoVO, getUsuarioLogado(), true);
            atualizarTransacaoNaLista(transacaoVO, true);

            setOnComplete("Richfaces.hideModalPanel('modalSincronizarTransacao')");
            montarSucessoGrowl(retorno);
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    public List<ParcelaTransacaoVO> getListaParcelasTransacao() {
        return listaParcelasTransacao;
    }

    public void setListaParcelasTransacao(List<ParcelaTransacaoVO> listaParcelasTransacao) {
        this.listaParcelasTransacao = listaParcelasTransacao;
    }

    public void irParaTelaCliente() {
        ParcelaTransacaoVO obj = (ParcelaTransacaoVO) context().getExternalContext().getRequestMap().get("parcela");
        try {
            if (obj == null) {
                throw new Exception("Cliente Não Encontrado.");
            } else {
                irParaTelaCliente(obj.getCliente());
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void listarParcelas() {
        try {
            limparMsg();
            listaParcelasTransacao = new ArrayList<>();
            listaParcelasTransacao = getFacade().getZWFacade().getTransacao().consultarParcelasTransacao(dataInicio, dataFim, situacaoSelecionada, getEmpresaFiltro(), getTipoTransacao(), getRetornoPacto(), getFiltro());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirModalRetentativaTransacao() {
        try {
            limparMsg();
            setOnComplete("");
            setListaConvenioCobrancaRetentativa(new ArrayList<>());
            setConvenioCobrancaRetentativa(0);

            TransacaoVO obj = (TransacaoVO) context().getExternalContext().getRequestMap().get("transacao");
            carregarDadosTransacao(obj);

            ConvenioCobrancaVO convenioCobrancaTransacaoVO = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(obj.getConvenioCobrancaVO().getCodigo(), obj.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (convenioCobrancaTransacaoVO != null &&
                    !UteisValidacao.emptyNumber(convenioCobrancaTransacaoVO.getCodigo()) &&
                    convenioCobrancaTransacaoVO.getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
                getListaConvenioCobrancaRetentativa().add(convenioCobrancaTransacaoVO);
            }

            //apresentar na lista tbm os convênios online da empresa
            List<ConvenioCobrancaVO> listaConvenioEmpresa = getFacade().getConvenioCobranca().consultarTodosPorSituacaoEmpresa(obj.getEmpresa(),
                    SituacaoConvenioCobranca.ATIVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (ConvenioCobrancaVO convVO : listaConvenioEmpresa) {
                if (convVO.getTipo().getTipoTransacao().equals(TipoTransacaoEnum.NENHUMA) ||
                        convVO.isSomenteExtrato()) {
                    continue;
                }
                boolean existe = false;
                for (ConvenioCobrancaVO conv : getListaConvenioCobrancaRetentativa()) {
                    if (conv.getCodigo().equals(convVO.getCodigo())) {
                        existe = true;
                        break;
                    }
                }
                if (existe) {
                    continue;
                }
                getListaConvenioCobrancaRetentativa().add(convVO);
            }

            if (UteisValidacao.emptyList(getListaConvenioCobrancaRetentativa())) {
                throw new Exception("Não existe convênio para realizar retentativa");
            }

            //selecionar a autorizacao do cliente
            AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = getFacade().getAutorizacaoCobrancaCliente().obterUltimaAutorizacao(null, obj.getPessoaPagador().getCodigo(),
                    TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            if (autorizacaoCobrancaClienteVO != null &&
                    !UteisValidacao.emptyNumber(autorizacaoCobrancaClienteVO.getCodigo())) {
                Integer convenioCliente = autorizacaoCobrancaClienteVO.getConvenio().getCodigo();
                for (ConvenioCobrancaVO convVO : getListaConvenioCobrancaRetentativa()) {
                    if (!UteisValidacao.emptyNumber(convenioCliente) &&
                                convVO.getCodigo().equals(convenioCliente)) {
                        setConvenioCobrancaRetentativa(convenioCliente);
                        break;
                    }
                }
            }

            setOnComplete("Richfaces.showModalPanel('modalRetentativaTransacao')");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void retentativaTransacao() {
        try {
            limparMsg();
            setOnComplete("");

            if (UteisValidacao.emptyNumber(getConvenioCobrancaRetentativa())) {
                throw new Exception("Selecione o convênio para realizar a retentativa");
            }

            String retorno = getFacade().getTransacao().retentativaTransacao(transacaoVO, getUsuarioLogado(), getConvenioCobrancaRetentativa(), this.getIpCliente(), null);
            setOnComplete("Richfaces.hideModalPanel('modalRetentativaTransacao')");
            montarSucessoGrowl(retorno);
        } catch (Exception ex) {
            setErro(true);
            setMensagem(ex.getMessage());
            montarErro(ex);
        }
    }

    public boolean isApresentarCobrancaVerificarCartao() {
        return apresentarCobrancaVerificarCartao;
    }

    public void setApresentarCobrancaVerificarCartao(boolean apresentarCobrancaVerificarCartao) {
        this.apresentarCobrancaVerificarCartao = apresentarCobrancaVerificarCartao;
    }

    public Integer getConvenioCobrancaRetentativa() {
        if (convenioCobrancaRetentativa == null) {
            convenioCobrancaRetentativa = 0;
        }
        return convenioCobrancaRetentativa;
    }

    public void setConvenioCobrancaRetentativa(Integer convenioCobrancaRetentativa) {
        this.convenioCobrancaRetentativa = convenioCobrancaRetentativa;
    }

    public List<SelectItem> getSelectItemListaConvenioCobrancaRetentativa() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        for (ConvenioCobrancaVO obj : getListaConvenioCobrancaRetentativa()) {
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(objs, "label");
        objs.add(0, new SelectItem(0, ""));
        return objs;
    }

    public List<ConvenioCobrancaVO> getListaConvenioCobrancaRetentativa() {
        if (listaConvenioCobrancaRetentativa == null) {
            listaConvenioCobrancaRetentativa = new ArrayList<>();
        }
        return listaConvenioCobrancaRetentativa;
    }

    public void setListaConvenioCobrancaRetentativa(List<ConvenioCobrancaVO> listaConvenioCobrancaRetentativa) {
        this.listaConvenioCobrancaRetentativa = listaConvenioCobrancaRetentativa;
    }


    public Boolean getConsultarParaExportar() {
        return consultarParaExportar;
    }

    public void setConsultarParaExportar(Boolean consultarParaExportar) {
        this.consultarParaExportar = consultarParaExportar;
    }

    public void abrirPactoPayTelaCartao() {
        try {
            setMsgAlert("");
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession("LoginControle");
            String openWindow = "window.open('" + loginControle.getAbrirPactoPay() + "&redirect=pactopay/transacoes/credito-online'";
            openWindow += ")";
            setMsgAlert(openWindow);
        } catch (Exception exception) {
            montarErro(exception.getMessage());
        }
    }


    public boolean isExibirModalInformativo() {
        try {
            HttpServletRequest path = (HttpServletRequest) context().getExternalContext().getRequest();
            return path.getRequestURL().toString().contains("gestaoTransacoes.jsp");
        } catch (Exception ignored) {
        }
        return false;
    }

    public boolean isPermissaoEstronarTransacao() {
        return permissaoEstronarTransacao;
    }

    public void setPermissaoEstronarTransacao(boolean permissaoEstronarTransacao) {
        this.permissaoEstronarTransacao = permissaoEstronarTransacao;
    }
}

package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.financeiro.filtros.FiltroDemonstrativoFinanceiroTO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.richfaces.component.UIDataTable;
import relatorio.arquitetura.GeradorRelatorio;
import relatorio.negocio.comuns.financeiro.DetalhamentoLancamentoDF_VO;
import relatorio.negocio.jdbc.basico.DemonstrativoFinanceiroRel;
import relatorio.negocio.jdbc.financeiro.*;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.event.ActionEvent;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FechamentoCaixaPlanoContaControle extends FinanControle {

    private Calendar horaInicioProcessamento;
    private Calendar horaFimProcessamento;
    private Date dataInicioRelatorio = Calendario.hoje();
    private Date dataFimRelatorio = Calendario.hoje();
    private String tempoGasto;
    private String filtroContas;
    private List<ContaVO> contasFiltro;
    private boolean dre = false;
    private boolean resumoContas = false;
    private Boolean consultaPaginada = true;
    private Boolean paginada = true;
    private int contAjusteTree = 0;
    private SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss SSSS");
    private List<ObjetoTreeTO> listaPlanoContaTree;
    private List<ObjetoTreeTO> listaCentroCustoTree;
    private List<Integer> codigosCentrosSelecionados = new ArrayList<Integer>();
    private List<Integer> codigosPlanosSelecionados = new ArrayList<Integer>();
    private String planosSelecionados;
    private String centrosSelecionados;
    private DetalhamentoLancamentoDF_VO detalhamentoLancamentoDF_Vo = new DetalhamentoLancamentoDF_VO();
    private List<String> listaCentroCustosSelecionados = new ArrayList<String>();
    private String lista_CC_Selecionado; // Lista dos centro de custos selecionados.
    private DemonstrativoFinanceiroRel demonstrativoFinanceiroRel;
    //jsfChart
    private DefaultPieDataset dataSetPizza = new DefaultPieDataset();
    private DefaultCategoryDataset dataSetBarra = new DefaultCategoryDataset();
    //flex - CHART
    private LinkedHashMap<String, Double[]> data = new LinkedHashMap<String, Double[]>();
    private List<String> colors = new ArrayList<String>();
    private List<String> names = new ArrayList<String>();


    public FechamentoCaixaPlanoContaControle() throws Exception {
        inicializarEmpresa();
        montarListaSelectItemEmpresa();
        tipoDatas();
        demonstrativoFinanceiroRel = new DemonstrativoFinanceiroRel();
    }

    public static String getDesignIReportRelatorioPDF() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "DemonstrativoFinanceiroRel" + ".jrxml");
    }

    public static String getDesignIReportRelatorioExcel() {
        return ("designRelatorio" + File.separator + "outros" + File.separator + "DemonstrativoFinanceiroRelExcel");
    }

    public static void main(String... args) {
        try {
            FechamentoCaixaPlanoContaControle df = new FechamentoCaixaPlanoContaControle();
            Conexao.guardarConexaoForJ2SE(FacadeManager.getFacade().getRisco().getCon());
            df.setUsuario(getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS));
            df.setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS));
            df.setDataInicio(Calendario.getInstance(2012, 11, 1).getTime());
            df.setDataFim(Calendario.getInstance(2012, 11, 30).getTime());
            df.setTipoVisualizacao(TipoVisualizacaoRelatorioDF.PLANOCONTA);
            df.setTipoRelatorioDf(TipoRelatorioDF.RECEITA);
            df.setTipoFonteDadosDF(TipoFonteDadosDF.ZILLYON_WEB);
            df.gerarDemonstrativoSemThread();

        } catch (Exception ex) {
            Logger.getLogger(FechamentoCaixaPlanoContaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void editarLancamento() {
        setConsultaPaginada(true);
        setPaginada(true);
        if (totalMeses == true) {
            super.abrirModalTodos();
        } else {
            super.editarLancamento();
        }
        setDre(false);
    }

    public void mudarPaginacao() {
        setConsultaPaginada(!getConsultaPaginada());
        setPaginada(getConsultaPaginada());
    }

    public LinkedHashMap<String, Double[]> getData() {
        return data;
    }

    public List<String> getColors() {
        return colors;
    }

    public void setColors(List<String> colors) {
        this.colors = colors;
    }

    public List<String> getNames() {
        return names;
    }

    public void setNames(List<String> names) {
        this.names = names;
    }

    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    public void gerarDemonstrativoSemThread() {
        gerarDemonstrativo(this.tipoRelatorioDf, false, true);

    }

    public void gerarDemonstrativoComThread() {
        try {
            setTipoRelatorioDf(TipoRelatorioDF.FATURAMENTO_DE_CAIXA);
            ContadorTempo.limparCronometro();
            ContadorTempo.iniciarContagem();
            validarDados();
            gerarDemonstrativo(this.tipoRelatorioDf, true, this.isApresentarDevolucoesRel());
            montarArvoreFiltros();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Metodo para capturar a foto da empresa.
     *
     * @throws IOException
     * @throws Exception
     */
    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        this.getEmpresa().setFoto(getFacade().getEmpresa().obterFoto(getKey(), this.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        SuperControle.paintFoto(out, this.getEmpresa().getFoto());
    }

    private void consultarCentroCustosSelecionados() throws Exception {
        this.listaCentroCustosSelecionados.clear();
        List<CentroCustoTO> listaCentroCusto;
        this.lista_CC_Selecionado = "";
        listaCentroCusto = getFacade().getFinanceiro().getCentroCusto().consultarTodos();
        for (CentroCustoTO obj : listaCentroCusto) {
            if (codigosCentrosSelecionados.contains(obj.getCodigo())) {
                this.listaCentroCustosSelecionados.add(obj.getDescricao());
                if (lista_CC_Selecionado.equals("")) {
                    lista_CC_Selecionado = obj.getDescricao();
                } else {
                    lista_CC_Selecionado = lista_CC_Selecionado + ", " + obj.getDescricao();
                }
            }
        }
    }

    private void gerarDemonstrativo(TipoRelatorioDF tipoRelatorioDF, boolean gerarRelatorioUsandoThread, boolean apresentarDevolucoesRel) {
        boolean parametrosIncorretos = true;
        try {
            consultarCentroCustosSelecionados();

            setMensagemDetalhada("", "");
            Calendar dataInicialRel = Calendario.getInstance();
            if (tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA) {
                setDataInicioRelatorio(Uteis.obterPrimeiroDiaMes(getDataInicioRelatorio()));
                setDataInicio(getDataInicioRelatorio());
            }
            dataInicialRel.setTime(getDataInicioRelatorio());
            if (dataInicialRel.get(Calendar.YEAR) < 1990) {
                throw new Exception("Data de início do relatório inválida. Informe outra data.");
            }

            Calendar dataFinalRel = Calendario.getInstance();
            if (tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA ||
                    tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA_QUITADA ||
                    tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) {
                setDataFimRelatorio(Uteis.obterUltimoDiaMes(getDataFimRelatorio()));
                setDataFim(getDataFimRelatorio());
            }
            dataFinalRel.setTime(getDataFimRelatorio());
            if (Calendario.diferencaEmDias(getDataInicioRelatorio(), getDataFimRelatorio()) > 365) {
                throw new Exception("Período inválido. Informe um período que não ultrapasse 1 ano.");
            }
            if (!isMostrarCampoEmpresa()) {
                setEmpresaVO(this.getEmpresaLogado() != null ? this.getEmpresaLogado() : getEmpresaVO());
            }
            // Consultar o nome da empresa.
            if(getEmpresaVO() != null && !UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())){
                EmpresaVO objEmpresa = getFacade().getEmpresa().consultarPorCodigo(this.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeEmpresa(objEmpresa.getNome());
            }
            parametrosIncorretos = false;
            setHouveErroNoRelatorio(false);
            this.horaInicioProcessamento = Calendario.getInstance();
            horaInicioProcessamento.setTime(Calendario.hoje());

            // Montar a string do Período do relatório.
            SimpleDateFormat sdf2 = new SimpleDateFormat("dd/MM/yyyy");
            setPeriodoRelatorio(sdf2.format(getDataInicioRelatorio()) + " à " + sdf2.format(getDataFimRelatorio()));

            relatorioDF = new RelatorioDemonstrativoFinanceiro();
            montarFiltroContas();
            listaDF = relatorioDF.gerarDemonstrativo(tipoRelatorioDF,
                    this.getTipoVisualizacao(),
                    dataInicialRel,
                    dataFinalRel,
                    this.getEmpresaVO().getCodigo(),
                    this.codigosCentrosSelecionados,
                    gerarRelatorioUsandoThread,
                    this.getTipoFonteDadosDF(),
                    getConfFinanceiro().getUsarCentralEventos(),
                    isAgruparValorProdutoMMasModalidades(), filtroContas, apresentarDevolucoesRel, null);
            salvarFiltroSessao();
            // Após processar o Demonstrativo, então exibir na tela a Lista do Demonstrativo Financeiro
            criarListaParaEnviarAoBrowser(false, true);
            calcularTempoGastoPeloRelatorio();
            //RelatorioDemonstrativoFinanceiro.retornarListaDRE(listaDF);
        } catch (Exception e) {
            setHouveErroNoRelatorio(true);
            if (parametrosIncorretos) {
                setMensagemDetalhada(e.getMessage());
            } else {
                setMensagemDetalhada("msg_erro", "Não foi possível gerar Demonstrativo Financeiro: " +  e.getMessage());
            }

        }

    }

    private void montarFiltroContas(){
        filtroContas = "";
        for(ContaVO c : contasFiltro){
            if(c.getContaEscolhida()){
                filtroContas += "," + c.getCodigo();
            }
        }
        filtroContas = filtroContas.replaceFirst("\\,", "");
    }

    public void calcularTempoGastoPeloRelatorio() {
        this.horaFimProcessamento = Calendario.getInstance();
        horaFimProcessamento.setTime(Calendario.hoje());
        System.out.println("Fim Geração do Relatorio: " + sdf.format(horaFimProcessamento.getTime()));
        long diferenca = (this.horaFimProcessamento.getTimeInMillis() - this.horaInicioProcessamento.getTimeInMillis());
        System.out.println("Tempo(miliSegundos) para gerar relatorio: " + diferenca);
        double segundos = diferenca / 1000f;
        NumberFormat format = new DecimalFormat("00.000");
        setTempoGasto(format.format(segundos));
        System.out.println("Tempo(segundos) para gerar relatorio: " + format.format(segundos));
        // após mostrar o relatório na tela, informar alterar status das threads para processar novamente.
        setMensagem("Relatório concluído em:" + tempoGasto + " segundos.");

    }

    public List<DemonstrativoFinanceiro> getListaDF() {
        return listaDF;
    }

    public void setListaDF(List<DemonstrativoFinanceiro> listaDF) {
        this.listaDF = listaDF;
    }

    public String getTempoGasto() {
        return tempoGasto;
    }

    public void setTempoGasto(String tempoGasto) {
        this.tempoGasto = tempoGasto;
    }

    /**
     * Responsável por montar as árvores dos filtros
     *
     * <AUTHOR>
     * 19/08/2011
     */
    public void montarArvoreFiltros() throws Exception {
        listarPlanoContas();
        listarCentroCustos();
        setContasFiltro(getFacade().getFinanceiro().getConta().consultarContasSimples(getEmpresaLogado().getCodigo(), false));
        getContasFiltro().add(new ContaVO(-1));
    }

    /**
     * <AUTHOR>
     * 19/08/2011
     */
    private void setarFiltrosSelecionados() {
        this.setCodigosCentrosSelecionados(new ArrayList<Integer>());
        this.setCodigosPlanosSelecionados(new ArrayList<Integer>());
        //obter os códigos dos centros e planos selecionados
        this.setCodigosCentrosSelecionados(obterFiltrosSelecionados(this.getCentrosSelecionados()));
        this.setCodigosPlanosSelecionados(obterFiltrosSelecionados(this.getPlanosSelecionados()));
    }

    /**
     * Percorre a string pegando os códigos dos centros e planos selecionados.
     *
     * <AUTHOR>
     * 19/08/2011
     */
    private List<Integer> obterFiltrosSelecionados(String superString) {
        List<Integer> lista = new ArrayList<Integer>();
        String[] listaParams = superString.split(";");
        for (String param : listaParams) {
            if (!param.equals("")) {
                lista.add(Integer.valueOf(param));
            }
        }
        return lista;
    }

    /**
     * Responsável por ajustar o código agrupador para evitar que mesmo em arvores diferentes
     * haja dois códigos iguais. Isso porque a função jquery que monta a tree view expande/retrai
     * a linha, faz isso pelo id do tr. Portanto se tiver mais de uma arvore na tela e dois ou mais
     * ids iguais, vão ocorrer incosistências nas arvores.
     *
     * <AUTHOR> 19/08/2011
     */
    public void ajustarCodigoAgrupador(ObjetoTreeTO obj) {
        String codigoAgrupador = obj.getCodigoAgrupador();
        //obter codigo pai
        Integer codigoPai = Integer.valueOf(codigoAgrupador.substring(0, 3));
        //verificar  se o código indica objeto filhos
        String filhos = codigoAgrupador.substring(3);
        //se o objeto indicar pai, atualizar o contador do filtro
        if (filhos.isEmpty()) {
            contAjusteTree++;
        }
        //a regra para ajuste do código é o tamanho do demonstrativo + o contador dos filtros + o código pai
        codigoPai = getListaDFBrowser().size() + contAjusteTree;
        codigoAgrupador = codigoPai.toString();
        //adicionar zeros ao código do pai
        while (codigoAgrupador.length() < 3) {
            codigoAgrupador = "0" + codigoAgrupador;
        }
        //adicionar os filhos
        codigoAgrupador += filhos;
        //atualizar o código agrupador
        obj.setCodigoAgrupador(codigoAgrupador);
    }

    /**
     * Monta a arvore dos planos de contas
     *
     * @throws Exception
     * <AUTHOR> 18/08/2011
     */
    public void listarPlanoContas() throws Exception {
        this.setListaPlanoContaTree(new ArrayList<ObjetoTreeTO>());
        //consultar os planos de contas
        List<PlanoContaTO> planos = getFacade().getFinanceiro().getPlanoConta().consultarTodasReceitas(getEmpresa().getCodigo());
        for (PlanoContaTO plano : planos) {
            ObjetoTreeTO obj = new ObjetoTreeTO();
            obj.setCodigoAgrupador(plano.getCodigoPlano());
            ajustarCodigoAgrupador(obj);
            obj.setCodigoEntidade(plano.getCodigo());
            obj.setNome(plano.getDescricao());
            //marcar como selecionado se o código estiver contido na lista de codigos selecionados
            //isto pq a caixa de filtros é renderizada sempre que o botão 'visualizar' é clicado
            if (this.getCodigosPlanosSelecionados().contains(obj.getCodigoEntidade())) {
                obj.setSelecionado(true);
            }
            this.getListaPlanoContaTree().add(obj);
        }
    }

    /**
     * Monta a arvore dos centros de custos
     *
     * @throws Exception
     * <AUTHOR> 18/08/2011
     */
    public void listarCentroCustos() throws Exception {
        this.setListaCentroCustoTree(new ArrayList<ObjetoTreeTO>());
        //consultar os centros de custos
        List<CentroCustoTO> centros = getFacade().getFinanceiro().getCentroCusto().consultar("", "", null);

        for (CentroCustoTO centro : centros) {
            ObjetoTreeTO obj = new ObjetoTreeTO();
            obj.setCodigoAgrupador(centro.getCodigoCentro());
            ajustarCodigoAgrupador(obj);
            obj.setCodigoEntidade(centro.getCodigo());
            obj.setNome(centro.getDescricao());
            //marcar como selecionado se o código estiver contido na lista de codigos selecionados
            //isto pq a caixa de filtros é renderizada sempre que o botão 'visualizar' é clicado
            if (this.getCodigosCentrosSelecionados().contains(obj.getCodigoEntidade())) {
                obj.setSelecionado(true);
            }
            this.getListaCentroCustoTree().add(obj);
        }
    }

    public String abrirDemonstrativoFinanceiro() {
        try {
            montarArvoreFiltros();
            montarListaSelectItemEmpresa();
            setConfFinanceiro(getFacade().getConfiguracaoFinanceiro().consultar());
            setTipoFonteDadosDF(TipoFonteDadosDF.ZILLYON_WEB);
            setAgruparValorProdutoMMasModalidades(false);
            restauraFiltros();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "fechamentoCaixaPlanoConta";
    }

    /**
     * @return the listaCentroCustoTree
     */
    @Override
    public List<ObjetoTreeTO> getListaCentroCustoTree() {
        return listaCentroCustoTree;
    }

    /**
     * @param listaCentroCustoTree the listaCentroCustoTree to set
     */
    @Override
    public void setListaCentroCustoTree(List<ObjetoTreeTO> listaCentroCustoTree) {
        this.listaCentroCustoTree = listaCentroCustoTree;
    }

    /**
     * @return the listaPlanoContaTree
     */
    public List<ObjetoTreeTO> getListaPlanoContaTree() {
        if (listaPlanoContaTree == null) {
            listaPlanoContaTree = new ArrayList<ObjetoTreeTO>();
        }
        return listaPlanoContaTree;
    }

    /**
     * @param listaPlanoContaTree the listaPlanoContaTree to set
     */
    public void setListaPlanoContaTree(List<ObjetoTreeTO> listaPlanoContaTree) {
        this.listaPlanoContaTree = listaPlanoContaTree;
    }

    /**
     * @return the centrosSelecionados
     */
    @Override
    public String getCentrosSelecionados() {
        if (centrosSelecionados == null) {
            centrosSelecionados = "";
        }
        return centrosSelecionados;
    }

    /**
     * @param centrosSelecionados the centrosSelecionados to set
     */
    @Override
    public void setCentrosSelecionados(String centrosSelecionados) {
        this.centrosSelecionados = centrosSelecionados;
    }

    /**
     * @return the planosSelecionados
     */
    @Override
    public String getPlanosSelecionados() {
        return planosSelecionados;
    }

    /**
     * @param planosSelecionados the planosSelecionados to set
     */
    @Override
    public void setPlanosSelecionados(String planosSelecionados) {
        this.planosSelecionados = planosSelecionados;
    }

    /**
     * @return the codigosPlanosSelecionados
     */
    public List<Integer> getCodigosPlanosSelecionados() {
        if (codigosPlanosSelecionados == null) {
            codigosPlanosSelecionados = new ArrayList<Integer>();
        }
        return codigosPlanosSelecionados;
    }

    /**
     * @param codigosPlanosSelecionados the codigosPlanosSelecionados to set
     */
    public void setCodigosPlanosSelecionados(List<Integer> codigosPlanosSelecionados) {
        this.codigosPlanosSelecionados = codigosPlanosSelecionados;
    }

    /**
     * @return the codigosCentrosSelecionados
     */
    public List<Integer> getCodigosCentrosSelecionados() {
        if (codigosCentrosSelecionados == null) {
            codigosCentrosSelecionados = new ArrayList<Integer>();
        }
        return codigosCentrosSelecionados;
    }

    /**
     * @param codigosCentrosSelecionados the codigosCentrosSelecionados to set
     */
    public void setCodigosCentrosSelecionados(List<Integer> codigosCentrosSelecionados) {
        this.codigosCentrosSelecionados = codigosCentrosSelecionados;
    }

    public void visualizarLancamentoFinanceiro() {
        try {
            setMensagemID("msg_dados_editar");
            LancamentoDF lancamento = (LancamentoDF) context().getExternalContext().getRequestMap().get("lancamentoDF");
            MovContaVO movContaVo = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(lancamento.getMovConta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            MovContaControle movContaControle = (MovContaControle) JSFUtilities.getManagedBean("MovContaControle");

            movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
            movContaVo.setMovContaRateios(getFacade().getFinanceiro().getMovContaRateio().consultarPorMovConta(movContaVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            movContaControle.setarMovConta(movContaVo, true, true);
            movContaControle.setChequeDevolvido(true);
            movContaControle.setApresentarBotaoQuitar(true);

            if (UteisValidacao.emptyList(movContaControle.getListaComboBanco()) && movContaControle.getMovContaVO() != null
                    && movContaControle.getMovContaVO().getEmpresaVO() != null && !UteisValidacao.emptyNumber(movContaControle.getMovContaVO().getEmpresaVO().getCodigo())) {
                movContaControle.montarSelectItemListaComboConta(movContaControle.getMovContaVO().getEmpresaVO());
            }

            JSFUtilities.storeOnSession(MovContaControle.class.getSimpleName(), movContaControle);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Erro ao visualizar Lançamento do Financeiro. Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage());
        }
    }

    public void visualizarLancamentoZillyonWeb() {
        try {
            LancamentoDF lancamento = (LancamentoDF) context().getExternalContext().getRequestMap().get("lancamentoDF");
            this.detalhamentoLancamentoDF_Vo = new DetalhamentoLancamentoDF_VO();
            lancamento.setLancamentoEhNaoAtribuido(this.getTipoListaLancamentosMostrar() != 1);
            setDetalhamentoLancamentoDF_Vo(DetalhamentoLancamentoDF.consultarDetalhesDoLancamento(lancamento,
                    this.getTipoVisualizacao(),
                    demonstrativoFinanceiroSelecionado,
                    codigosCentrosSelecionados,
                    this.totalizadorMesSelecionado.getMesProcessar().getDataIni().getTime(),
                    this.totalizadorMesSelecionado.getMesProcessar().getDataFim().getTime(),
                    tipoRelatorioDf, null, false));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Erro ao visualizar Lançamento do ZillyonWeb. Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage());
        }
    }

    public void montarChart() throws Exception {

        class MyMonth implements Serializable {

            private Long mesReferencia;
            private double entradas = 0;
            private double saidas = 0;

            public double getEntradas() {
                return entradas;
            }

            public void setEntradas(double entradas) {
                this.entradas = entradas;
            }

            public Long getMesReferencia() {
                return mesReferencia;
            }

            public void setMesReferencia(Long mesReferencia) {
                this.mesReferencia = mesReferencia;
            }

            public double getSaidas() {
                return saidas;
            }

            public void setSaidas(double saidas) {
                this.saidas = saidas;
            }
        }
        synchronized (this) {
            try {
                Date dtInicio = (Date) Uteis.obterUltimoDiaMes(getDataFim()).clone();
                setDataInicio(Uteis.obterPrimeiroDiaMes(Uteis.somarCampoData(dtInicio,
                        Calendar.MONTH, -3)));
                setDataFim(Uteis.obterUltimoDiaMes(dtInicio));
                this.gerarDemonstrativo(TipoRelatorioDF.RECEITA, true, true);
                data = new LinkedHashMap<String, Double[]>();
                names = new ArrayList<String>();
                names.add("Entradas");
                names.add("Saidas");
                colors = new ArrayList<String>();
                colors.add("#847CDE");
                colors.add("#F21105");
                getDataSetBarra().clear();

                if (getListaDFBrowser() != null) {
                    List<DemonstrativoFinanceiro> lista = getListaDFBrowser();
                    Double tEntrada = 0.0;
                    Double tSaida = 0.0;

                    LinkedHashMap<Long, MyMonth> mapaMeses = new LinkedHashMap<Long, MyMonth>();

                    for (DemonstrativoFinanceiro demonstrativoFinanceiro : lista) {
                        List<MesProcessar> listaMeses = demonstrativoFinanceiro.getListaMeses();

                        if (demonstrativoFinanceiro.getTipoES() != null && !demonstrativoFinanceiro.getCodigoAgrupador().contains(".")) {
                            //preparar lista de meses com totais para o gráfico
                            for (MesProcessar mesProcessar : listaMeses) {
                                Long dataMesAtualInMillis = mesProcessar.getDataIni().getTimeInMillis();

                                MyMonth meuMes = mapaMeses.get(dataMesAtualInMillis);
                                if (meuMes == null) {
                                    meuMes = new MyMonth();
                                    meuMes.setMesReferencia(dataMesAtualInMillis);
                                    mapaMeses.put(dataMesAtualInMillis, meuMes);
                                }
                                if (demonstrativoFinanceiro.getTipoES().equals(TipoES.ENTRADA)) {
                                    meuMes.setEntradas(meuMes.getEntradas() + mesProcessar.getTotal());
                                } else if (demonstrativoFinanceiro.getTipoES().equals(TipoES.SAIDA)) {
                                    meuMes.setSaidas(meuMes.getSaidas() + mesProcessar.getTotal());
                                }

                            }

                        }
                    }

                    Set<Long> meses = Uteis.ordenarMapPorKey(mapaMeses).keySet();
                    for (Long mes : meses) {

                        tEntrada = Uteis.arredondarForcando2CasasDecimais(mapaMeses.get(mes).getEntradas());
                        tSaida = Uteis.arredondarForcando2CasasDecimais(mapaMeses.get(mes).getSaidas());

                        String nomeMes = Uteis.getMesNomeReferencia(new Date(mes));
                        if (nomeMes.contains("ç")) {
                            nomeMes = nomeMes.replace("ç", "&#0231;");
                        }

                        data.put(nomeMes, new Double[]{tEntrada, tSaida});

                        getDataSetBarra().setValue(tEntrada, "Entradas", Uteis.getMesNomeReferencia(new Date(mes)));
                        getDataSetBarra().setValue(tSaida, "Saídas", Uteis.getMesNomeReferencia(new Date(mes)));

                    }

                }
            } catch (Exception e) {
                throw e;
            }
        }
    }

    public DetalhamentoLancamentoDF_VO getDetalhamentoLancamentoDF_Vo() {
        return detalhamentoLancamentoDF_Vo;
    }

    public void setDetalhamentoLancamentoDF_Vo(DetalhamentoLancamentoDF_VO detalhamentoLancamentoDF_Vo) {
        this.detalhamentoLancamentoDF_Vo = detalhamentoLancamentoDF_Vo;
    }

    public List<String> getListaCentroCustosSelecionados() {
        return listaCentroCustosSelecionados;
    }

    public void setListaCentroCustosSelecionados(List<String> listaCentroCustosSelecionados) {
        this.listaCentroCustosSelecionados = listaCentroCustosSelecionados;
    }

    public Boolean getConsultaPaginada() {
        return consultaPaginada;
    }

    public void setConsultaPaginada(Boolean consultaPaginada) {
        this.consultaPaginada = consultaPaginada;
    }

    public Boolean getPaginada() {
        return paginada;
    }

    public void setPaginada(Boolean paginada) {
        this.paginada = paginada;
    }

    public String getLista_CC_Selecionado() {
        return lista_CC_Selecionado;
    }

    public void setLista_CC_Selecionado(String lista_CC_Selecionado) {
        this.lista_CC_Selecionado = lista_CC_Selecionado;
    }

    public void imprimirPDF(Integer codigoTipoRelatorioDF) throws Exception {
        //Essa impressão é um include compartilhado pelo DRE e Demonstrativo Financeiro, por isso precisa guardar e retornar no fim do método para que o DRE não impacte na tela do Demonstrativo Financeiro.
        TipoRelatorioDF tipoRelatorioDFAntesImpressao = this.tipoRelatorioDf;
        if (!UteisValidacao.emptyNumber(codigoTipoRelatorioDF)){
            this.tipoRelatorioDf.getDescricao();
            this.tipoRelatorioDf = TipoRelatorioDF.getTipoRelatorioDF(codigoTipoRelatorioDF);
        }

        try {
        	if (!listaLancamentosDF.isEmpty()) {
                UIDataTable dataTable = (consultaPaginada) ?
                        (UIDataTable) context().getViewRoot().findComponent("formModalLancamentosDF:tableLancamentos") :
                        (UIDataTable) context().getViewRoot().findComponent("formModalLancamentosDF:tableLancamentosNaoPaginada");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(listaLancamentosDF, params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(listaLancamentosDF);
                        }
                    }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

        Map<String, Object> parametros = new HashMap<String, Object>();
        prepareParams(parametros, listaLancamentosDF);
        demonstrativoFinanceiroRel.imprimirRelatorio(parametros);
        this.tipoRelatorioDf = tipoRelatorioDFAntesImpressao;
    }

    public void imprimirExcel() {
        try {
            if (!listaLancamentosDF.isEmpty()) {
                UIDataTable dataTable = (consultaPaginada) ?
                        (UIDataTable) context().getViewRoot().findComponent("formModalLancamentosDF:tableLancamentos") :
                        (UIDataTable) context().getViewRoot().findComponent("formModalLancamentosDF:tableLancamentosNaoPaginada");
                if (dataTable != null) {
                    String colunaOrdenacao = JSFUtilities.getColunaOrdenacao(dataTable);
                    if (!colunaOrdenacao.isEmpty()) {
                        String[] params = colunaOrdenacao.split(":");
                        Ordenacao.ordenarLista(listaLancamentosDF, params[0]);
                        if (params[1].equals("DESC")) {
                            Collections.reverse(listaLancamentosDF);
                        }
                    }
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

        try {
            GeradorRelatorio obj = new GeradorRelatorio();
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParamsExcel(parametros);

            try {
                preencherNomeEmpresa();
            } catch (Exception ex) {}

            obj.montarRelatorio("OBJETO", "EXCEL", "DemonstrativoFinanceiroRelExcel", getDesignIReportRelatorioExcel(), listaLancamentosDF, parametros);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void preencherNomeEmpresa() throws Exception {
        Map<Integer, String> mapaEmpresas = new HashMap<>();
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarEmpresas(Uteis.NIVELMONTARDADOS_MINIMOS);

        if (!UteisValidacao.emptyList(empresas)) {
            for (EmpresaVO empresa : empresas) {
                mapaEmpresas.put(empresa.getCodigo(), empresa.getNome());
            }
        }

        for (LancamentoDF item : listaLancamentosDF) {
            item.setEmpresaApresentar(mapaEmpresas.getOrDefault(item.getEmpresa(), ""));
        }
    }

    private void prepareParams(Map<String, Object> params, List listaObjetos) throws Exception {
        Integer emp = this.getEmpresaLogado().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        params.put("nomeRelatorio", "DemonstrativoFinanceiroRel");
        params.put("nomeEmpresa", empre.getNome());
        params.put("tituloData", getTituloData());

        params.put("tituloRelatorio", "Lançamentos");
        params.put("nomeDesignIReport", getDesignIReportRelatorioPDF());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", listaObjetos);

        params.put("tipoRelatorioDF", tipoRelatorioDf.getCodigo());
        params.put("totalEntrada", getTotalSelecionadoEntrada());
        params.put("totalSaida", getTotalSelecionadoSaida());
        params.put("resultado", getTotalSelecionado());

        params.put("filtros", getFiltros());
        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        
        //Parâmetro para o relatório, para que possa mostrar o plano filho a que um plano
        //de contas pertence
        params.put("planoFilho", this.receberAgrupador());
    }
    
    
    //Método que recebe o agrupador a que um plano pertence
    private String receberAgrupador(){
    	String nomeAgrupador = "";
    	
    	if (!getNomeAgrupadorSelecionado().isEmpty()) {
    		nomeAgrupador = getNomeAgrupadorSelecionado();
        }
    	
    	return nomeAgrupador;
    }

    private void prepareParamsExcel(Map<String, Object> params) throws Exception {
        params.put("tipoRelatorioDF", tipoRelatorioDf.getCodigo());
        params.put("tituloData", getTituloData());
        
        //Parâmetro para o relatório, para que possa mostrar o plano filho a que um plano
        //de contas pertence
        params.put("planoFilho", this.receberAgrupador());
    }

    public String getFiltros() {
        String filtros = "";
        if (getTipoRelatorioDf() != null) {
            filtros += "Tipo Consulta:";
            filtros += getTipoRelatorioDf().getDescricao() + "  ";
        }
        if (getTipoVisualizacao() != null) {
            filtros += "Tipo Visualização:";
            filtros += getTipoVisualizacao().getDescricao() + "  ";
        }
        if (getTipoVisualizacao() == TipoVisualizacaoRelatorioDF.PLANOCONTA) {
            if (!listaCentroCustosSelecionados.isEmpty()) {
                filtros += "Filtros: Centro Custos:" + getLista_CC_Selecionado() + "  ";
            }
        }
        if (getPeriodoRelatorio() != null) {
            filtros += "Período:";
            filtros += getPeriodoRelatorio() + "  ";
        }
        if (this.getEmpresaVO() != null) {
            filtros += "Empresa:" + this.getEmpresaVO().getNome() + "  ";
        }
        if (getTipoFonteDadosDF() != null) {
            filtros += "Fonte de Dados:" + getTipoFonteDadosDF().getDescricao() + "  ";
        }
        if (!getMesSelecionado().isEmpty()) {
            filtros += "Mês Referência:" + getMesSelecionado() + "  ";
        }
        if (!getNomeAgrupadorSelecionado().isEmpty() && !getLabelAgrupadorSelecionado().isEmpty()) {
            filtros += "" + getLabelAgrupadorSelecionado() + ":" + getNomeAgrupadorSelecionado() + "  ";
        }
        return filtros;
    }

    /**
     * @return the demonstrativoFinanceiroRel
     */
    public DemonstrativoFinanceiroRel getDemonstrativoFinanceiroRel() {
        return demonstrativoFinanceiroRel;
    }

    /**
     * @param demonstrativoFinanceiroRel the demonstrativoFinanceiroRel to set
     */
    public void setDemonstrativoFinanceiroRel(DemonstrativoFinanceiroRel demonstrativoFinanceiroRel) {
        this.demonstrativoFinanceiroRel = demonstrativoFinanceiroRel;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public boolean getDre() {
        return dre;
    }

    public void setDre(boolean dre) {
        this.dre = dre;
    }

    public boolean getResumoContas() {
        return resumoContas;
    }

    public void setResumoContas(boolean resumoContas) {
        this.resumoContas = resumoContas;
    }

    public Date getDataInicioRelatorio() {
        return dataInicioRelatorio;
    }

    public void setDataInicioRelatorio(Date dataInicioRelatorio) {
        this.dataInicioRelatorio = dataInicioRelatorio;
    }

    public Date getDataFimRelatorio() {
        return dataFimRelatorio;
    }

    public void setDataFimRelatorio(Date dataFimRelatorio) {
        this.dataFimRelatorio = dataFimRelatorio;
    }
    
    public void exportar(ActionEvent evt) {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        exportadorListaControle.exportar(evt, listaDFBrowser, null, null);
    }

    public String getTxtExportar(){
        String txt = "codigoAgrupador=codigoAgrupador,nomeAgrupador=nomeAgrupador,";
        String meses = "";
        if(listaDFBrowser != null && listaDFBrowser.size() > 0){
            int i = 1;
            DemonstrativoFinanceiro demonsF = listaDFBrowser.get(0);
            List<MesProcessar> lista = new ArrayList<MesProcessar>(demonsF.getListaMeses());
            Collections.reverse(lista);
            for(MesProcessar mes : lista){
                meses = "mes"+(i++)+"="+mes.getNomeMes().replaceAll("/", "-")+","+meses;
            }
        }
        return txt + meses + "totalTodosMesesApresentar=Total";
    }

    private void validarDados() throws Exception {
        if (dataInicioRelatorio  != null && dataFimRelatorio == null) {
            throw new Exception("Informe a data final.");
        }
        if (dataInicioRelatorio == null && dataFimRelatorio != null) {
            throw new Exception("Informe a data inicial.");
        }
        if (dataInicioRelatorio != null && dataInicioRelatorio.after(dataFimRelatorio)) {
            throw new Exception("Data Inicial não pode ser superior a Data Final.");
        }
    }

    public String getFiltroContas() {
        return filtroContas;
    }

    public void setFiltroContas(String filtroContas) {
        this.filtroContas = filtroContas;
    }

    public List<ContaVO> getContasFiltro() {
        return contasFiltro;
    }

    public void setContasFiltro(List<ContaVO> contasFiltro) {
        this.contasFiltro = contasFiltro;
    }

    public void salvarFiltroSessao() {
        FiltroDemonstrativoFinanceiroTO filtroDemonstrativoFinanceiroTO = new FiltroDemonstrativoFinanceiroTO();
        filtroDemonstrativoFinanceiroTO.setTipoRelatorioDF(tipoRelatorioDf);
        filtroDemonstrativoFinanceiroTO.setTipoVisualizacao(this.getTipoVisualizacao());
        filtroDemonstrativoFinanceiroTO.setDataInicioRelatorio(this.dataInicioRelatorio);
        filtroDemonstrativoFinanceiroTO.setDataFimRelatorio(this.dataFimRelatorio);
        filtroDemonstrativoFinanceiroTO.setCodEmpresa(getEmpresaVO().getCodigo());
        filtroDemonstrativoFinanceiroTO.setCodigosCentrosSelecionados(this.codigosCentrosSelecionados);
        filtroDemonstrativoFinanceiroTO.setTipoFonteDadosDF(this.getTipoFonteDadosDF());
        filtroDemonstrativoFinanceiroTO.setAgruparValorProdutoMMasModalidade(isAgruparValorProdutoMMasModalidades());
        filtroDemonstrativoFinanceiroTO.setContasFiltro(this.contasFiltro);
        filtroDemonstrativoFinanceiroTO.setAgruparValorProdutoMMasModalidade(this.isAgruparValorProdutoMMasModalidades());

        JSFUtilities.storeOnSession(FiltroDemonstrativoFinanceiroTO.class.getName(), filtroDemonstrativoFinanceiroTO);
    }

    public void restauraFiltros() {
        FiltroDemonstrativoFinanceiroTO filtroSessao = (FiltroDemonstrativoFinanceiroTO) JSFUtilities.getFromSession(FiltroDemonstrativoFinanceiroTO.class.getName());
        if (filtroSessao != null) {
            this.setTipoRelatorioDf(filtroSessao.getTipoRelatorioDF());
            this.setTipoVisualizacao(filtroSessao.getTipoVisualizacao());
            dataInicioRelatorio = filtroSessao.getDataInicioRelatorio();
            dataFimRelatorio = filtroSessao.getDataFimRelatorio();
            this.getEmpresaVO().setCodigo(filtroSessao.getCodEmpresa());
            codigosCentrosSelecionados = filtroSessao.getCodigosCentrosSelecionados();
            this.setTipoFonteDadosDF(filtroSessao.getTipoFonteDadosDF());
            this.contasFiltro = filtroSessao.getContasFiltro();
            setAgruparValorProdutoMMasModalidades(filtroSessao.isAgruparValorProdutoMMasModalidade());

        }
    }
}

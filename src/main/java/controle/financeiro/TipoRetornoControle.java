package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.financeiro.TipoRetornoVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.TipoRetorno;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * tipoRetornoForm.jsp tipoRetornoCons.jsp) com as funcionalidades da classe <code>TipoRetorno</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see TipoRetorno
 * @see TipoRetornoVO
 */
public class TipoRetornoControle extends SuperControle {

    private TipoRetornoVO tipoRetornoVO;
    private String msgAlert;

    public TipoRetornoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>TipoRetorno</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setTipoRetornoVO(new TipoRetornoVO());
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>TipoRetorno</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            TipoRetornoVO obj = getFacade().getTipoRetorno().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            obj.setNovoObj(false);
            setTipoRetornoVO(obj);
            tipoRetornoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>TipoRetorno</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (tipoRetornoVO.isNovoObj()) {
                getFacade().getTipoRetorno().incluir(tipoRetornoVO);
                incluirLogInclusao();
            } else {
                getFacade().getTipoRetorno().alterar(tipoRetornoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP TipoRetornoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getTipoRetorno().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getTipoRetorno().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("arquivoLayoutRetorno")) {
                objs = getFacade().getTipoRetorno().consultarPorArquivoLayoutRetorno(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>TipoRetornoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getTipoRetorno().excluir(tipoRetornoVO);
            incluirLogExclusao();
            setTipoRetornoVO(new TipoRetornoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"tiporemessa\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"tiporemessa\" violates foreign key")){
                setMensagemDetalhada("Este tipo de retorno não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("arquivoLayoutRetorno", "Arquivo de Layout do Retorno"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public TipoRetornoVO getTipoRetornoVO() {
        return tipoRetornoVO;
    }

    public void setTipoRetornoVO(TipoRetornoVO tipoRetornoVO) {
        this.tipoRetornoVO = tipoRetornoVO;
    }
     public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getTipoRetorno().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
     
         public void incluirLogInclusao() throws Exception {
        try {
            tipoRetornoVO.setObjetoVOAntesAlteracao(new TipoRetornoVO());
            tipoRetornoVO.setNovoObj(true);
            registrarLogObjetoVO(tipoRetornoVO, tipoRetornoVO.getCodigo(), "TIPORETORNO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPORETORNO", tipoRetornoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE TIPORETORNO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoRetornoVO.setNovoObj(new Boolean(false));
        tipoRetornoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            tipoRetornoVO.setObjetoVOAntesAlteracao(new TipoRetornoVO());
            tipoRetornoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(tipoRetornoVO, tipoRetornoVO.getCodigo(), "TIPORETORNO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPORETORNO", tipoRetornoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE TIPORETORNO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(tipoRetornoVO, tipoRetornoVO.getCodigo(), "TIPORETORNO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("TIPORETORNO", tipoRetornoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE TIPORETORNO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        tipoRetornoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = tipoRetornoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), tipoRetornoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       tipoRetornoVO = new TipoRetornoVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Tipo de Retorno",
                "Deseja excluir o Tipo de Retorno?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

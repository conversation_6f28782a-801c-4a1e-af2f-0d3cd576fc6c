package controle.financeiro;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CentroCusto;
import org.richfaces.model.TreeNode;
import org.richfaces.model.TreeNodeImpl;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.model.SelectItem;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

public class CentroCustosControle extends SuperControle {

    private List<CentroCustoTO> lista;
    // navegação dos centros
    private CentroCustoTO centro;
    private CentroCustoTO centroDestino;
    // exibição da tela
    private Boolean btExcluir;
    private Boolean btTrocarCentroPai;
    private Boolean btTrocarCentroPaiMarcado;
    private boolean disabledCampoCentroCustoPai = false;
    // usado na suggestion box
    private String centroNome;
    private String centroNomeBoleto;
    private String centroNomePix;
    private CentroCustoTO centroEscolhido;
    private CentroCustoTO centroEscolhidoPix;
    private CentroCustoTO centroEscolhidoBoleto;
    // codigo do centro de custos para processar a seleção
    private int codigoBancoCentroCustos;
    //atributo da árvore
    private TreeNode rootNode = null;
    private Integer codigoTipoPadrao = 0;
    //atributo usado na consulta de planos especificando o tipo de consulta que será feita
    private String codigoCentroPaiSelecionado;
    private Boolean alterarCodigo;
    private String codigoAntesAlteracao;
    private List<CentroCustoTO> listaCentros;
    private Integer codigoCentro;
    private String msgExcluirTodos;
    private String fecharModalExclusao;
    private EmpresaVO empresaRelatorio;
    private String onCompleteChangeSuggestionBoxCentroCustos = "";
    private Integer tabIndexSuggestionBoxCentroCustos;
    private boolean taxaBoleto = false;

    /**
     * <AUTHOR>
     * 23/11/2011
     */
    public String abrirCentroCusto() {
        try {
            this.setCodigoCentroPaiSelecionado("");
            validarPermissaoCentroCustos();
            montarArvoreCentro();
            setMensagemID("msg_dados_consultados");
            return "centroCusto";

        } catch (Exception e) {
            //montarMsgAlert usado para mostrar alert de permissão
            montarMsgAlert(e.getMessage());
            return "";
        }
    }

    /**
     * <AUTHOR>
     * 23/11/2011
     */
    private void montarArvoreCentro() throws Exception {
        this.setListaCentros(getFacade().getFinanceiro().getCentroCusto().consultarTodos());
    }

    /**
     * Responsável por salvar a Alteracao do Codigo do Plano de contas, fazendo todas as validações necessárias 
     * e alterando em cascata os códigos dos filhos e irmãos 
     * <AUTHOR>
     * 22/11/2011
     */
    private void salvarAlteracaoCodigoCentro() throws Exception {
        String codigoPai = Uteis.obterCodigoPaiEntidadeFinanceiro(this.getCentro().getCodigoCentro());
        String codigoPaiAntesAlteracao = Uteis.obterCodigoPaiEntidadeFinanceiro(this.getCodigoAntesAlteracao());
        UteisValidacao.validaCodigoFinanceiro(this.getCentro().getCodigoCentro());
        //aqui inicializo um inteiro com o novo valor do codigo e com o valor antes da alteração
        int valorCodigoAlterar = Integer.parseInt(this.getCentro().getCodigoCentro().substring(this.getCentro().getCodigoCentro().length() - 3, this.getCentro().getCodigoCentro().length()));
        int valorCodigoAntesAlteracao = Integer.parseInt(this.getCodigoAntesAlteracao().substring(this.getCodigoAntesAlteracao().length() - 3, this.getCodigoAntesAlteracao().length()));

        //verificar se houve mudança de pai
        if (!codigoPai.equals(codigoPaiAntesAlteracao)) {
            valorCodigoAntesAlteracao = valorCodigoAlterar;
            validarMudancaCodigo(codigoPai);
        }
        //verificar se o código novo existe ou está livre, caso esteja livre processar a alteração, alterando também os irmãos
        if (getFacade().getFinanceiro().getCentroCusto().verificarExistenciaPlano(this.getCentro().getCodigoCentro())) {
            //obter irmãos
            Map<Integer, String> codigosIrmaos = getFacade().getFinanceiro().getCentroCusto().obterCodigoCentroIrmaos(codigoPai);
            //filhos dos irmãos
            Map<Integer, List<Integer>> codigosFilhos = new HashMap<Integer, List<Integer>>();
            Set<Integer> keySet = codigosIrmaos.keySet();
            //iterar nos irmãos, montando lista com códigos dos filhos
            for (Integer key : keySet) {
                String codIrmao = codigosIrmaos.get(key);
                codigosFilhos.put(key, getFacade().getFinanceiro().getCentroCusto().obterCodigoFilhos(codIrmao, this.getCodigoAntesAlteracao()));
            }
            //salvar a alteração de código de plano de contas, atualizando os filhos
            getFacade().getFinanceiro().getCentroCusto().atualizarCodigoCentroCustos(this.getCentro().getCodigo(), this.getCentro().getCodigoCentro(),
                    this.getCodigoAntesAlteracao(), getFacade().getFinanceiro().getCentroCusto().obterCodigoFilhos(this.getCodigoAntesAlteracao(), null));
            //iterar nos planos do mesmo nivel, mudando a posição dos mesmos caso necessário
            for (Integer key : keySet) {
                String codIrmao = codigosIrmaos.get(key);
                int valorCodIrmao = Integer.parseInt(codIrmao.substring(codIrmao.length() - 3, codIrmao.length()));
                //se  valor novo é maior do que o valor antigo
                if (valorCodigoAlterar > valorCodigoAntesAlteracao) {
                    if (valorCodIrmao <= valorCodigoAlterar && valorCodIrmao > valorCodigoAntesAlteracao) {
                        //montar o novo código
                        String codigoFolha = Uteis.incrementarNumeroDoTipoString(codIrmao.substring(codIrmao.length() - 3, codIrmao.length()), -1);
                        if (codigoFolha.length() > 3) {
                            codigoFolha = Uteis.removerZeroAEsquerda(codigoFolha, codigoFolha.length() - 3);
                        }
                        String novoCodigo = codIrmao.substring(0, codIrmao.length() - 3) + codigoFolha;
                        //salvar a alteração de código de plano de contas, atualizando os filhos
                        getFacade().getFinanceiro().getCentroCusto().atualizarCodigoCentroCustos(key, novoCodigo, codIrmao, codigosFilhos.get(key));
                    }
                } else {
                    if ((valorCodIrmao >= valorCodigoAlterar && valorCodIrmao < valorCodigoAntesAlteracao)
                            || (valorCodIrmao >= valorCodigoAlterar && valorCodigoAlterar == valorCodigoAntesAlteracao)) {
                        //montar o novo código
                        String codigoFolha = Uteis.incrementarNumeroDoTipoString(codIrmao.substring(codIrmao.length() - 3, codIrmao.length()), 1);
                        if (codigoFolha.length() > 3) {
                            codigoFolha = Uteis.removerZeroAEsquerda(codigoFolha, codigoFolha.length() - 3);
                        }
                        String novoCodigo = codIrmao.substring(0, codIrmao.length() - 3) + codigoFolha;
                        //salvar a alteração de código de plano de contas, atualizando os filhos
                        getFacade().getFinanceiro().getCentroCusto().atualizarCodigoCentroCustos(key, novoCodigo, codIrmao, codigosFilhos.get(key));
                    }
                }
            }
            //caso não, salvar a alteração
        } else {
            getFacade().getFinanceiro().getCentroCusto().atualizarCodigoCentroCustos(this.getCentro().getCodigo(), this.getCentro().getCodigoCentro(),
                    this.getCodigoAntesAlteracao(), getFacade().getFinanceiro().getCentroCusto().obterCodigoFilhos(this.getCodigoAntesAlteracao(), null));
        }
        this.setCodigoAntesAlteracao(this.getCentro().getCodigoCentro());
    }

    /**
     * <AUTHOR>
     * 24/11/2011
     */
    private void validarMudancaCodigo(String pai) throws Exception {
        //verificar a existência do pai
        if (!pai.isEmpty()) {
            if (pai.equals(this.getCodigoAntesAlteracao()) || !getFacade().getFinanceiro().getCentroCusto().verificarExistenciaPlano(pai)) {
                throw new ConsistirException("Código do Centro de Custos não é válido, Centro Pai não existe.");
            }
        }
        //não pode mudar para um filho dele mesmo
        if (this.getCentro().filhoDe(this.getCodigoAntesAlteracao())) {
            throw new ConsistirException("Centro de Custos não pode se tornar um filho dele mesmo.");
        }
    }

    /**
     * <AUTHOR>
     * 23/11/2011
     */
    public void voltar() throws Exception {
        CentroCustoTO novo = new CentroCustoTO();
        this.setCentro(novo);
        montarArvoreCentro();
    }

    /**
     * Valida a permissão do usuário logado para a entidade CentroCusto
     * que usa a permissão "Centro de Custos"
     * @throws Exception
     */
    public void validarPermissaoCentroCustos() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CentroCustos", "9.05 - Centro de Custos");
            }
        }
    }

    /**
     * Preenche a combo de Tipo Padrão - Entrada ou Saida
     * @return
     */
    public List<SelectItem> getListaSelectItemTipoPadrao() {
        List objs = new ArrayList();
        objs.add(new SelectItem(0, ""));
        try {
            TipoES[] listaTipos = TipoES.values();
            for (TipoES tipoES : listaTipos) {
                objs.add(new SelectItem(tipoES.getCodigo(), tipoES.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return objs;
    }

    /**
     * Retorna o código do centro pai
     * @param obj
     * @return
     */
    private String obterCodigoPai(CentroCustoTO obj) {
        int l = obj.getCodigoCentro().lastIndexOf(".");
        if (l == -1) {
            return "";
        } else {
            String codigoPai = obj.getCodigoCentro().substring(0, l);
            return codigoPai;
        }
    }

    /**
     * Consulta a lista referente a árvore
     * @return
     */
    public String consultar() {
        loadTree();
        return "consultar";
    }

    /**
     * Montagem da árvore
     */
    public void loadTree() {
        try {
            lista = getFacade().getFinanceiro().getCentroCusto().consultar(null, null, null);
            if (UteisValidacao.emptyString(getMensagemID())
                    || getMensagemID().equals("msg_dados_consultados")) {
                setMensagemID("msg_dados_consultados");
                setSucesso(true);
                setErro(false);
            }
            rootNode = new TreeNodeImpl();
            addNodes(rootNode, lista);
        } catch (IOException e) {
            tratarEx(e);
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Cancela cadastros 
     * @return
     */
    public void cancelar() {
        incluir();
    }

    /**
     * Método que edita o centro de custo escolhido na árvore
     * @return
     * @throws Exception 
     */
    public void editar() throws Exception {
        this.setAlterarCodigo(Boolean.FALSE);
        CentroCustoTO obj = getFacade().getFinanceiro().getCentroCusto().obter(this.getCodigoCentro());
        String codigoPai = Uteis.obterCodigoPaiEntidadeFinanceiro(obj.getCodigoCentro());
        if (!UteisValidacao.emptyString(codigoPai)) {
            obj.setCentroPai(getFacade().getFinanceiro().getCentroCusto().obter(codigoPai));
        }
        //custoPai só está preenchido caso seja um custo filho
        if (obj.getCentroPai() != null) {
            setCentroEscolhido(obj.getCentroPai());
            //setCentroExibicao(obj.getCentroPai());
            setCentroNome(obj.getCentroPai().getDescricaoCurta());
        } else {
            setCentroEscolhido(new CentroCustoTO());
            //setCentroExibicao(new CentroCustoTO());
            setCentroNome("");
        }
        setCentro(obj);
        setDisabledCampoCentroCustoPai(true);
        setBtExcluir(obj.isLeaf());
        if (obj.isLeaf()) {
            setBtTrocarCentroPai(true);
        } else {
            setBtTrocarCentroPai(false);
        }
        setBtTrocarCentroPaiMarcado(false);
        obj.setNovoObj(false);
        setMensagemID("msg_dados_editar");
        setSucesso(false);
        setErro(false);
        this.setCodigoAntesAlteracao(this.getCentro().getCodigoCentro());
    }

    /**
     * Método que permite habilitar o campo de custo pai
     * quando o usuário marcar o checkbox
     */
    public void habilitarCampoCentroPai() {
        disabledCampoCentroCustoPai = !btTrocarCentroPaiMarcado;
    }

    /**
     * Método necessário para setar vazio para o objeto
     * centro escolhido quando o usuario apagar o campo
     * usado pelo input na pagina include_SuggestionBoxCentroCusto
     */
    public void setarCentroVazio() {
        try {
            if (this.centroNome == null || this.centroNome.isEmpty()) {
                if (UteisValidacao.nenhumNulo(getCentro())) {
                    getCentro().setCodigoCentro("");
                }
                setCentroEscolhido(new CentroCustoTO());
            }
            if (this.centroNomeBoleto == null || this.centroNomeBoleto.isEmpty()) {
                if (UteisValidacao.nenhumNulo(getCentro())) {
                    getCentro().setCodigoCentro("");
                }
                setCentroEscolhidoBoleto(new CentroCustoTO());
            }
            if (this.centroNomePix == null || this.centroNomePix.isEmpty()) {
                if (UteisValidacao.nenhumNulo(getCentro())) {
                    getCentro().setCodigoCentro("");
                }
                setCentroEscolhidoPix(new CentroCustoTO());
            }
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Método que salva (inclui ou altera) custo de contas
     * @return
     */
    public void salvar() {
        try {
            if (!UteisValidacao.emptyString(this.getCodigoAntesAlteracao())
                    && !UteisValidacao.emptyString(this.getCentro().getCodigoCentro())
                    && !this.getCodigoAntesAlteracao().equals(this.getCentro().getCodigoCentro())) {
                salvarAlteracaoCodigoCentro();

            }
            if (centroEscolhido != null) {
                centro.setCentroPai(centroEscolhido);
            }
            if (getCentro().getCodigoCentro().isEmpty()) {
                this.getCentro().setCodigoCentro("" + getFacade().getFinanceiro().getCentroCusto().consultarCodigoPaiContendoSomenteUmNumeroEAdicionaUm());
            }
            if (centro.getNovoObj()) {
                getFacade().getFinanceiro().getCentroCusto().incluir(centro);
            } else {
                getFacade().getFinanceiro().getCentroCusto().alterar(centro);
            }
            this.setErro(false);
            this.setSucesso(true);
            montarArvoreCentro();
            setMensagemID("msg_dados_gravados");
        } catch (Exception e) {
            this.setMensagemDetalhada("msg_erro", e.getMessage());
            this.setErro(true);
            this.setSucesso(false);
        }
    }

    /**
     * Método que exclui o centro de custos escolhido
     * @return
     */
    public void excluir() {
        try {
            this.setFecharModalExclusao("");
            if(getFacade().getFinanceiro().getCentroCusto().verificarExisteRelacionamento(getCentro().getCodigo())){
                //setCentroEscolhido(new CentroCustoTO());
                setFecharModalExclusao("Richfaces.showModalPanel('modalTrocarCentro');");
            }else{
                getFacade().getFinanceiro().getCentroCusto().excluir(this.getCentro());
                setMensagemID("msg_dados_excluidos");
                setSucesso(true);
                setErro(false);
                incluir();
                montarArvoreCentro();
                this.setFecharModalExclusao("Richfaces.hideModalPanel('painelEdicaoCentroCustos');Richfaces.hideModalPanel('modalTrocarCentro');");
            }
            
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    public void excluirTrocando(){
    	try{
    		setMsgAlert("");
    		if(UteisValidacao.emptyNumber(getCentroEscolhido().getCodigo())){
    			throw new ConsistirException("Informe o Centro de Custos.");
    		}
    		getFacade().getFinanceiro().getCentroCusto().trocarCentroCustos(getCentro().getCodigo(), getCentroEscolhido().getCodigo());
    		excluir();
    	}catch(Exception e){
    		montarMsgAlert(e.getMessage());
    	}
    }

    /**
     * Método que exclui todos os centros de custos
     */
    public void excluirTodosCentros() {
        try {
            getFacade().getFinanceiro().getCentroCusto().excluirTodosCentros();
            setMsgExcluirTodos("");
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            this.setCentro(new CentroCustoTO());
            this.setCentroEscolhido(new CentroCustoTO());
            //this.setCentroExibicao(new CentroCustoTO());
            setCentroNome("");
        } catch (Exception e) {
            tratarEx(e);
            setMsgExcluirTodos("alert('Não foi possível excluir pois existem relacionamentos com rateios de integração e de contas.');");
        }
    }

    /**
     * Trata as mensagens usadas 
     * @param e
     */
    private void tratarEx(Exception e) {
        this.setLista(new ArrayList<CentroCustoTO>());
        this.setMensagemDetalhada("msg_erro", e.getMessage());
        setSucesso(false);
        setErro(true);
    }

    /**
     * Método que monta os objetos para adicionar um custo de conta
     * @return
     */
    public void incluir() {
        try {
            this.setCodigoAntesAlteracao("");
            //objetos usados no campo de custo Pai
            this.setCentroNome("");
            this.setCentroEscolhido(new CentroCustoTO());
            //this.setCentroExibicao(new CentroCustoTO());
            //objeto usado no cadastro de custo
            this.setCentro(new CentroCustoTO());
            getCentro().setNovoObj(true);
            //limpando campos
            setCodigoTipoPadrao(0);
            setDisabledCampoCentroCustoPai(false);
            setBtExcluir(false);
            setBtTrocarCentroPai(false);

            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    /**
     * Monta o objeto de centro de custos escolhido no modal de centro de custos
     */
    public void processSelection() {
        try {
            CentroCustoTO obj = getFacade().getFinanceiro().getCentroCusto().obter(this.getCodigoBancoCentroCustos());
            if(isTaxaBoleto()){
                this.setCentroEscolhidoBoleto(obj);
                this.setCentroNomeBoleto(obj.getDescricaoCurta());
            }else {
                this.setCentroEscolhido(obj);
                this.setCentroNome(obj.getDescricaoCurta());
            }
            //possui filho para o pai escolhido
            if (getCentro() != null) {
                String codigoProximoFilho = getFacade().getFinanceiro().getCentroCusto().obterCodigoProximoFilho(
                        obj.getCodigoCentro());
                getCentro().setCodigoCentro(codigoProximoFilho);
            }
            setTaxaBoleto(false);
        } catch (Exception e) {
            tratarEx(e);
        }
    }
    
    public void processSelectionRecebiveisAvulsos() {
        try {
        	RecebivelAvulsoControle recebivelAvulso = (RecebivelAvulsoControle)getControlador(RecebivelAvulsoControle.class.getSimpleName());
            CentroCustoTO obj = getFacade().getFinanceiro().getCentroCusto().obter(this.getCodigoBancoCentroCustos());
            recebivelAvulso.getRecebivelAvulso().setCentroCustos(obj);
            recebivelAvulso.setDescricaoCentro(obj.getCodigoCentro() +" - "+obj.getDescricao());
        } catch (Exception e) {
            tratarEx(e);
        }
    }

    /**
     * Método que consulta centro de custos quando o usuário digita no suggestionBox
     * @param suggest
     * @return
     */
    public List<CentroCustoTO> executarAutocompletePesqCentroCusto(Object suggest) {
        List<CentroCustoTO> listaCentroCustos = null;
        try {
            String nomePesq = (String) suggest;
            boolean somenteNumerosEPontos = UteisValidacao.somenteNumerosEPontos(nomePesq);
            boolean somenteNumero = UteisValidacao.somenteNumeros(nomePesq);
            Integer codigo = null;
            if (somenteNumero){
                codigo = Integer.parseInt(nomePesq);
            }
            CentroCusto pj = new CentroCusto();
            if (somenteNumerosEPontos) {
                listaCentroCustos = pj.consultar(nomePesq, null, codigo);
            } else {
                listaCentroCustos = pj.consultar(null, nomePesq, codigo);
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaCentroCustos;
    }

    /**
     * Método que seleciona o centro de custos escolhido no suggestionBox
     * @throws SQLException
     * @throws Exception
     */
    public void selecionarCentroCusto() throws SQLException, Exception {
        selecionarCentro((CentroCustoTO) context().getExternalContext().getRequestMap().get("result"), false);
    }

    public void selecionarCentroCustoBoleto() throws SQLException, Exception {
        selecionarCentro((CentroCustoTO) context().getExternalContext().getRequestMap().get("resultBoleto"), true);
    }

    public void selecionarCentro(CentroCustoTO obj, boolean boleto)throws Exception{
        if(boleto){
            this.setCentroEscolhidoBoleto(obj);
        }else {
            this.setCentroEscolhido(obj);
        }
        //possui filho para o pai escolhido
        if (getCentro() != null) {
            String codigoProximoFilho = getFacade().getFinanceiro().getCentroCusto().obterCodigoProximoFilho(
                    obj.getCodigoCentro());
            getCentro().setCodigoCentro(codigoProximoFilho);
        }
    }

    public void selecionarCentroCustoPix() throws SQLException, Exception {
        selecionarCentroPix((CentroCustoTO) context().getExternalContext().getRequestMap().get("resultPix"));
    }

    public void selecionarCentroPix(CentroCustoTO obj)throws Exception{
        this.setCentroEscolhidoPix(obj);
        //possui filho para o pai escolhido
        if (getCentro() != null) {
            String codigoProximoFilho = getFacade().getFinanceiro().getCentroCusto().obterCodigoProximoFilho(
                    obj.getCodigoCentro());
            getCentro().setCodigoCentro(codigoProximoFilho);
        }
    }

    /**
     * Método chamado da árvore que retorna
     * a mesma para mostrar na tela
     * @return
     */
    public TreeNode getTreeNode() {
        //loadTree();
        return rootNode;
    }

    /**
     * Método que verifica qual nó será pai e qual será filho
     * e adiciona na árvore
     * @param path
     * @param node
     * @param properties
     */
    private void addNodes(TreeNode node,
            List<CentroCustoTO> properties) {

        TreeNodeImpl temporary = new TreeNodeImpl();
        TreeNodeImpl anterior = new TreeNodeImpl();

        int i = 1;
        for (CentroCustoTO obj : properties) {
            TreeNodeImpl nodeImpl = new TreeNodeImpl();
            nodeImpl.setData(obj);

            // o custo é um dos nos raizes da arvore
            if (obj.getCodigoCentro().length() <= 3) {
                node.addChild(i++, nodeImpl);
                temporary = nodeImpl;
                /*System.out.println("Nó Pai: " + obj.getCodigoCentro() + " "
                + obj.getDescricao());*/
            } else {
                String codigoPai = obterCodigoPai(obj);

                String codigoAnterior = ((CentroCustoTO) anterior.getData()).getCodigoCentro();
                int l2 = codigoAnterior.lastIndexOf(".");
                String codigoPaiAnterior = l2 == -1 ? codigoAnterior.substring(
                        0, 3) : codigoAnterior.substring(0, l2);

                if (codigoAnterior.equals(codigoPai)) {
                    // o custo é um subnó no nó anterior
                    obj.setCentroPai((CentroCustoTO) anterior.getData());
                    adicionarNodos(anterior, nodeImpl, i++);
                    temporary = (TreeNodeImpl) anterior.getParent();
                    /*System.out.println("Subnó do Nó anterior: " + obj.getCodigoCentro() + " "
                    + obj.getDescricao());*/
                } else {
                    // verificar se é irmão do nó anterior
                    if (codigoPaiAnterior.equals(codigoPai)) {
                        /*System.out.println("Subnó do Nó anterior: " + obj.getCodigoCentro() + " "
                        + obj.getDescricao());*/
                        obj.setCentroPai((CentroCustoTO) anterior.getParent().getData());
                        adicionarNodos(anterior.getParent(), nodeImpl, i++);
                        //anterior = nodeImpl;
                    } else {
                        // adicionar a um nó anterior
                        // encontrar o pai.

                        try {
                            ACHAR_PAI:
                            while (true) {
                                String obterCodigoPai = this.obterCodigoPai(obj);
                                CentroCustoTO temporarioCusto = (CentroCustoTO) temporary.getData();
                                if (temporarioCusto != null
                                        && obterCodigoPai.equals(temporarioCusto.getCodigoCentro())) {
                                    break ACHAR_PAI;
                                } else {
                                    temporary = (TreeNodeImpl) temporary.getParent();
                                }
                            }
                            obj.setCentroPai((CentroCustoTO) temporary.getData());
                            adicionarNodos(temporary, nodeImpl, i++);
                            /*System.out.println("Adiciona um nó após mostrar subNó do nó anterior: "
                            + obj.getCodigoCentro() + " "
                            + obj.getDescricao());*/
                        } catch (Exception e) {
                            System.out.println("Erro"
                                    + e.getMessage() + "  " + obj.getCodigoCentro());
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                        }
                    }
                    // não é irmão do nó anterior
                }
            }
            anterior = nodeImpl;
        }
    }

    /**
     * Método que adiciona o nó na árvore na ordem
     * @param pai
     * @param filho
     * @param i
     */
    private void adicionarNodos(TreeNode pai, TreeNode filho, int i) {
        pai.addChild(i, filho);
    }

    public Boolean getBtExcluir() {
        return btExcluir;
    }

    public void setBtExcluir(Boolean btExcluir) {
        this.btExcluir = btExcluir;
    }

    public CentroCustoTO getCentro() {
        return centro;
    }

    public void setCentro(CentroCustoTO centro) {
        this.centro = centro;
    }

    public CentroCustoTO getCentroDestino() {
        return centroDestino;
    }

    public void setCentroDestino(CentroCustoTO centroDestino) {
        this.centroDestino = centroDestino;
    }

    public List<CentroCustoTO> getLista() {
        if (lista == null) {
            loadTree();
        }
        return lista;
    }

    public Integer getCodigoTipoPadrao() {
        if (codigoTipoPadrao == null) {
            codigoTipoPadrao = 0;
        }
        return codigoTipoPadrao;
    }

    public void setCodigoTipoPadrao(Integer codigoTipoPadrao) {
        this.codigoTipoPadrao = codigoTipoPadrao;
    }

    public void setLista(List<CentroCustoTO> lista) {
        this.lista = lista;
    }

    public String getCentroNome() {
        return centroNome;
    }

    public void setCentroNome(String centroNome) {
        this.centroNome = centroNome;
    }

    public CentroCustoTO getCentroEscolhido() {
        if (centroEscolhido == null) {
            centroEscolhido = new CentroCustoTO();
        }
        return centroEscolhido;
    }

    public void setCentroEscolhido(CentroCustoTO centroEscolhido) {
        this.centroEscolhido = centroEscolhido;
    }

    public int getCodigoBancoCentroCustos() {
        return codigoBancoCentroCustos;
    }

    public void setCodigoBancoCentroCustos(int codigoBancoCentroCustos) {
        this.codigoBancoCentroCustos = codigoBancoCentroCustos;
    }

    public Boolean getBtTrocarCentroPai() {
        return btTrocarCentroPai;
    }

    public void setBtTrocarCentroPai(Boolean btTrocarCentroPai) {
        this.btTrocarCentroPai = btTrocarCentroPai;
    }

    public void setBtTrocarCentroPaiMarcado(Boolean btTrocarCentroPaiMarcado) {
        this.btTrocarCentroPaiMarcado = btTrocarCentroPaiMarcado;
    }

    public Boolean getBtTrocarCentroPaiMarcado() {
        return btTrocarCentroPaiMarcado;
    }

    public boolean isDisabledCampoCentroCustoPai() {
        return disabledCampoCentroCustoPai;
    }

    public void setDisabledCampoCentroCustoPai(boolean disabledCampoCentroCustoPai) {
        this.disabledCampoCentroCustoPai = disabledCampoCentroCustoPai;
    }

    /**
     * @param msgExcluirTodos the msgExcluirTodos to set
     */
    public void setMsgExcluirTodos(String msgExcluirTodos) {
        this.msgExcluirTodos = msgExcluirTodos;
    }

    /**
     * @return the msgExcluirTodos
     */
    public String getMsgExcluirTodos() {
        if (msgExcluirTodos == null) {
            msgExcluirTodos = "";
        }
        return msgExcluirTodos;
    }

    /**
     * @param listaCentros the listaCentros to set
     */
    public void setListaCentros(List<CentroCustoTO> listaCentros) {
        this.listaCentros = listaCentros;
    }

    /**
     * @return the listaCentros
     */
    public List<CentroCustoTO> getListaCentros() {
        if (listaCentros == null) {
            listaCentros = new ArrayList<CentroCustoTO>();
        }
        return listaCentros;
    }

    /**
     * @param codigoCentro the codigoCentro to set
     */
    public void setCodigoCentro(Integer codigoCentro) {
        this.codigoCentro = codigoCentro;
    }

    /**
     * @return the codigoCentro
     */
    public Integer getCodigoCentro() {
        return codigoCentro;
    }

    /**
     * @param codigoCentroPaiSelecionado the codigoCentroPaiSelecionado to set
     */
    public void setCodigoCentroPaiSelecionado(String codigoCentroPaiSelecionado) {
        this.codigoCentroPaiSelecionado = codigoCentroPaiSelecionado;
    }

    /**
     * @return the codigoCentroPaiSelecionado
     */
    public String getCodigoCentroPaiSelecionado() {
        if (codigoCentroPaiSelecionado == null) {
            codigoCentroPaiSelecionado = "";
        }
        return codigoCentroPaiSelecionado;
    }

    /**
     * @param alterarCodigo the alterarCodigo to set
     */
    public void setAlterarCodigo(Boolean alterarCodigo) {
        this.alterarCodigo = alterarCodigo;
    }

    /**
     * @return the alterarCodigo
     */
    public Boolean getAlterarCodigo() {
        if (alterarCodigo == null) {
            alterarCodigo = Boolean.FALSE;
        }
        return alterarCodigo;
    }

    /**
     * @param codigoAntesAlteracao the codigoAntesAlteracao to set
     */
    public void setCodigoAntesAlteracao(String codigoAntesAlteracao) {
        this.codigoAntesAlteracao = codigoAntesAlteracao;
    }

    /**
     * @return the codigoAntesAlteracao
     */
    public String getCodigoAntesAlteracao() {
        if (codigoAntesAlteracao == null) {
            codigoAntesAlteracao = "";
        }
        return codigoAntesAlteracao;
    }

    /**
     * @param fecharModalExclusao the fecharModalExclusao to set
     */
    public void setFecharModalExclusao(String fecharModalExclusao) {
        this.fecharModalExclusao = fecharModalExclusao;
    }

    /**
     * @return the fecharModalExclusao
     */
    public String getFecharModalExclusao() {
        return fecharModalExclusao;
    }

    /**
     * Metodo para capturar a foto da empresa para mostrar no relatorio de Plano de Contas
     * @param event
     * @throws java.io.IOException
     * @throws java.lang.Exception
     */
    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        Integer codigoEmpresa = getEmpresaLogado().getCodigo();
        if (codigoEmpresa != 0) {
            setEmpresaRelatorio(getFacade().getEmpresa().consultarPorChavePrimaria(
                    codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getEmpresaRelatorio().setFotoRelatorio(getFacade().getEmpresa().obterFoto(
                    getKey(), getEmpresaRelatorio().getCodigo(), 
                    MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        }
        super.paintFotoRelatorio(out, getEmpresaRelatorio().getFotoRelatorio());
    }

    /**
     * @return the empresaRelatorio
     */
    public EmpresaVO getEmpresaRelatorio() {
        if (empresaRelatorio == null) {
            empresaRelatorio = new EmpresaVO();
        }
        return empresaRelatorio;
    }

    public void limpar() {
        this.setCentroNome("");
        this.setCentroEscolhido(new CentroCustoTO());
        this.setCentro(new CentroCustoTO());
    }

    /**
     * @param empresaRelatorio the empresaRelatorio to set
     */
    public void setEmpresaRelatorio(EmpresaVO empresaRelatorio) {
        this.empresaRelatorio = empresaRelatorio;
    }

      public String getDataAtual() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm",new Locale("pt","BR"));
        return sdf.format(Calendario.hoje());
    }

    public String getNomeUsuarioLogado() {
        String nomeUsuario = "";
        try {
            nomeUsuario = getUsuarioLogado().getNome();
        } catch (Exception e) {
        }
        return nomeUsuario;
    }

    public void editandoTaxaBoleto(){
        setTaxaBoleto(true);
    }

    public Integer getTabIndexSuggestionBoxCentroCustos() {
        return tabIndexSuggestionBoxCentroCustos;
    }

    public void setTabIndexSuggestionBoxCentroCustos(Integer tabIndexSuggestionBoxCentroCustos) {
        this.tabIndexSuggestionBoxCentroCustos = tabIndexSuggestionBoxCentroCustos;
    }

    public String getOnCompleteChangeSuggestionBoxCentroCustos() {
        return onCompleteChangeSuggestionBoxCentroCustos;
    }

    public void setOnCompleteChangeSuggestionBoxCentroCustos(String onCompleteChangeSuggestionBoxCentroCustos) {
        this.onCompleteChangeSuggestionBoxCentroCustos = onCompleteChangeSuggestionBoxCentroCustos;
    }

    public CentroCustoTO getCentroEscolhidoBoleto() {
        return centroEscolhidoBoleto;
    }

    public void setCentroEscolhidoBoleto(CentroCustoTO centroEscolhidoBoleto) {
        this.centroEscolhidoBoleto = centroEscolhidoBoleto;
    }

    public String getCentroNomeBoleto() {
        return centroNomeBoleto;
    }

    public void setCentroNomeBoleto(String centroNomeBoleto) {
        this.centroNomeBoleto = centroNomeBoleto;
    }

    public boolean isTaxaBoleto() {
        return taxaBoleto;
    }

    public void setTaxaBoleto(boolean taxaBoleto) {
        this.taxaBoleto = taxaBoleto;
    }

    public CentroCustoTO getCentroEscolhidoPix() {
        return centroEscolhidoPix;
    }

    public void setCentroEscolhidoPix(CentroCustoTO centroEscolhidoPix) {
        this.centroEscolhidoPix = centroEscolhidoPix;
    }

    public String getCentroNomePix() {
        return centroNomePix;
    }

    public void setCentroNomePix(String centroNomePix) {
        this.centroNomePix = centroNomePix;
    }
}

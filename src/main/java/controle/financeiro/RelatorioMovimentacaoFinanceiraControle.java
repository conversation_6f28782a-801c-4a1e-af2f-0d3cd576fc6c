package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO;
import negocio.comuns.financeiro.RelatorioFechamentoCaixaTO.CaixaAgrupar;
import negocio.comuns.financeiro.filtros.FiltroMovFinanceirasTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RelatorioMovimentacaoFinanceiraControle extends FinanControle {

    private RelatorioFechamentoCaixaTO relatorioFechamentoCaixa = new RelatorioFechamentoCaixaTO();
    private Date inicio = Calendario.hoje();
    private Date fim = Calendario.hoje();
    private List<ContaVO> contas = new ArrayList<ContaVO>();
    private boolean marcadoTodos = true;

    public void abrirFechamento() {
        try {
            inicializarEmpresa();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/pages/finan/fechamentoDiario.jsp?modulo=financeiroWeb', 'fechamentoDiario', 880, 595);");
            if (!isMostrarCampoEmpresa()) {
                setEmpresaVO(getEmpresaLogado());
            }
            setContas(getFacade().getFinanceiro().getConta().consultarContasBI(getEmpresaVO().getCodigo()));
            for (ContaVO cc : contas) {
                cc.setContaEscolhida(Boolean.TRUE);
            }
            marcadoTodos = true;
            restauraFiltro();
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMsgAlert(getMensagemDetalhada());
        }

    }

    @Override
    public void obterEmpresaEscolhida() throws Exception {
        if (getEmpresaVO().getCodigo() != 0) {
            setEmpresaVO(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
        } else {
            setEmpresaVO(new EmpresaVO());
        }
        setContas(getFacade().getFinanceiro().getConta().consultarContasBI(getEmpresaVO().getCodigo()));
        for (ContaVO cc : contas) {
            cc.setContaEscolhida(Boolean.TRUE);
        }
    }

    public void imprimir() throws Exception {
        try {
            if (!isMostrarCampoEmpresa()) {
                setEmpresaVO(this.getEmpresaLogado());
            }
            setMsgAlert("");
            Date faturamentoInicio = Uteis.somarDias(inicio, -1);
            Date faturamentoFim = Uteis.somarDias(fim, -1);
            String contasEscolhidas = Uteis.getListaEscolhidos(contas, "ContaEscolhida", "Codigo", true);

            relatorioFechamentoCaixa.setFormasPagamento(getFacade().getMovPagamento().consultarLancamentosFechamentoCaixa(
                    faturamentoInicio, faturamentoFim, getEmpresaVO().getCodigo()));

            relatorioFechamentoCaixa.setTotaisRecebidos(
                    getFacade().getFinanceiro().getMovConta().consultarRecebidos(inicio, fim, getEmpresaVO().getCodigo(), null, contasEscolhidas));

            relatorioFechamentoCaixa.setContas(getFacade().getFinanceiro().getConta().consultarContasMovimentadas(inicio, fim, getEmpresaVO().getCodigo(), contasEscolhidas));

            relatorioFechamentoCaixa.setTotaisPorForma(
                    getFacade().getFinanceiro().getMovConta().totalizarPorFormasPagamento(inicio, fim, getEmpresaVO().getCodigo(), null, contasEscolhidas));

            relatorioFechamentoCaixa.setCaixas(
                    getFacade().getFinanceiro().getCaixa().consultarCaixasMovimentadas(inicio, fim, getEmpresaVO().getCodigo()));



            for (ContaVO cc : relatorioFechamentoCaixa.getContas()) {
                cc.setListaMovimentacoes(getFacade().getFinanceiro().getMovConta().listarMovimentacoes(inicio, fim, cc.getCodigo(), null, getEmpresaVO().getCodigo(), null));
            }
            boolean todasAsContas = true;
            CONTAS:
            for (ContaVO cc : getContas()) {
                if (cc.getContaEscolhida()) {
                    for (ContaVO conta : relatorioFechamentoCaixa.getContas()) {
                        if (conta.getCodigo().equals(cc.getCodigo())) {
                            continue CONTAS;
                        }
                    }
                    Double saldo = getFacade().getFinanceiro().getConta().saldoAte(cc.getCodigo(), fim);
                    cc.setSaldoInicial(saldo);
                    cc.setSaldoAtual(saldo);
                    relatorioFechamentoCaixa.getContas().add(cc);
                } else {
                    todasAsContas = false;
                }
            }

            for (CaixaAgrupar ca : relatorioFechamentoCaixa.getCaixas()) {
                ca.setListaMovimentacoes(getFacade().getFinanceiro().getMovConta().listarMovimentacoes(inicio, fim, null, ca.getCodigoCaixa(), getEmpresaVO().getCodigo(), contasEscolhidas));
            }

            relatorioFechamentoCaixa.totalizarContas();
            relatorioFechamentoCaixa.totalizarFormas();
            Map<String, Object> params = new HashMap<String, Object>();
            prepareParams(params, relatorioFechamentoCaixa.getTotalRecebidos(), relatorioFechamentoCaixa.getTotalRecebiveis(), faturamentoInicio, faturamentoFim, todasAsContas);
            apresentarRelatorioObjetos(params);
            setMsgAlert(getNomeRefPastaRelatorioRelatorioGeradoAgora());
            salvarFiltroSessao();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }

    }

    public String getNomeRefPastaRelatorioRelatorioGeradoAgora() {
        String hRef = getNomeArquivoRelatorioGeradoAgora();
        if (!hRef.isEmpty()) {
            return "location.href=\"../../relatorio/" + hRef + "\"";
        } else {
            return "";
        }
    }

    private void prepareParams(Map<String, Object> params, Double totalRecebidos,
            Double totalRecebiveis, Date inicioFat, Date fimFat, boolean todasContas) throws Exception {
        Integer emp = getEmpresaVO().getCodigo();
        EmpresaVO empre = new EmpresaVO();
        if (emp != 0) {
            empre = getFacade().getEmpresa().consultarPorChavePrimaria(emp, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (todasContas) {
            params.put("contasSelecionadas", "");
        } else {
            String listaEscolhidos = Uteis.getListaEscolhidos(contas, "ContaEscolhida", "Descricao", false);
            params.put("contasSelecionadas", listaEscolhidos.isEmpty() ? "" : "Filtros: " + listaEscolhidos);
        }



        params.put("nomeRelatorio", "RelatorioFechamentoCaixa");
        params.put("nomeEmpresa", empre.getNome());

        params.put("tituloRelatorio", "Relatório Fechamento Caixa");
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        List<RelatorioFechamentoCaixaTO> lista = new ArrayList<RelatorioFechamentoCaixaTO>();
        lista.add(relatorioFechamentoCaixa);
        params.put("listaObjetos", lista);

        params.put("enderecoEmpresa", empre.getEndereco());
        params.put("cidadeEmpresa", empre.getCidade().getNome());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());

        params.put("dataAberturaApresentar", Uteis.getDataAplicandoFormatacao(inicio, "d MMMM | yyyy"));
        params.put("dataFechamentoApresentar", Uteis.getDataAplicandoFormatacao(fim, "d MMMM | yyyy"));
        params.put("totalRecebidos", Formatador.formatarValorMonetarioSemMoeda(totalRecebidos));
        params.put("totalRecebiveis", Formatador.formatarValorMonetarioSemMoeda(totalRecebiveis));

        params.put("inicioFat", Uteis.getDataAplicandoFormatacao(inicioFat, "dd/MM"));
        params.put("fimFat", Uteis.getDataAplicandoFormatacao(fimFat, "dd/MM"));

        params.put("dataImpressao", Uteis.getDataComHHMM(Calendario.hoje()));
        params.put("usuarioImpressao", getUsuarioLogado().getNome());
        params.put("moeda", getEmpresaLogado().getMoeda());
    }

    public void marcarTodos() {
        marcadoTodos = isMarcadoTodos() ? true : false;

        for (ContaVO item : getContas()) {
            item.setContaEscolhida(marcadoTodos);
        }
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "RelatorioFechamentoCaixa.jrxml");
    }

    public void setRelatorioFechamentoCaixa(RelatorioFechamentoCaixaTO relatorioFechamentoCaixa) {
        this.relatorioFechamentoCaixa = relatorioFechamentoCaixa;
    }

    public RelatorioFechamentoCaixaTO getRelatorioFechamentoCaixa() {
        return relatorioFechamentoCaixa;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Date getFim() {
        return fim;
    }

    public List<ContaVO> getContas() {
        return contas;
    }

    public void setContas(List<ContaVO> contas) {
        this.contas = contas;
    }

    public boolean isMarcadoTodos() {
        return marcadoTodos;
    }

    public void setMarcadoTodos(boolean marcadoTodos) {
        this.marcadoTodos = marcadoTodos;
    }

    public void salvarFiltroSessao() {
        FiltroMovFinanceirasTO filtroMovFinanceirasTO = new FiltroMovFinanceirasTO();
        filtroMovFinanceirasTO.setContas(contas);
        JSFUtilities.storeOnSession(FiltroMovFinanceirasTO.class.getName(), filtroMovFinanceirasTO);

    }

    public void restauraFiltro(){
        FiltroMovFinanceirasTO filtroSessao = (FiltroMovFinanceirasTO) JSFUtilities.getFromSession(FiltroMovFinanceirasTO.class.getName());
        if (filtroSessao !=null) {
            contas = filtroSessao.getContas();
        }
    }
}


package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.ce.controle.CadastroInicialControle;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import br.com.pactosolucoes.integracao.amigoFit.ClienteAmigoFitJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.fasterxml.jackson.databind.ObjectMapper;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.EnvioEmailContratoReciboControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.contrato.ContratoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.basico.ParceiroFidelidadeVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeItemVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeVO;
import negocio.comuns.basico.enumerador.*;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.BandeirasCapptaEnum;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoDebitoOnlineEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoOperacaoContaCorrenteEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.NotaProcessarTO;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.plano.PlanoTextoPadraoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ConstantesAcesso;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.geoitd.DadosGeoitdFinancialPurchaseTO;
import servicos.impl.geoitd.client.ClientGeoitd;
import servicos.impl.geoitd.enums.RespostaPosPinpad;
import servicos.impl.stone.connect.PedidoRetornoDTO;
import servicos.impl.stone.connect.StoneConnectService;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;
import servicos.integracao.impl.parceirofidelidade.dotz.ParceiroFidelidadeAPIDotzImpl;
import servicos.integracao.impl.parceirofidelidade.to.RetornoParceiroTO;
import servicos.pix.PixEmailService;
import servicos.pix.PixPagamentoService;
import servicos.pix.PixStatusEnum;
import servicos.propriedades.PropsService;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.component.html.HtmlSelectBooleanCheckbox;
import javax.faces.component.html.HtmlSelectOneMenu;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Connection;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas movPagamentoForm.jsp movPagamentoCons.jsp) com as funcionalidades da
 * classe
 * <code>MovPagamento</code>. Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see MovPagamento
 * @see MovPagamentoVO
 */
public class MovPagamentoControle extends SuperControleRelatorio {

    private MovPagamentoVO movPagamentoVO;
    private MovParcelaControle parcelaControle;
    private List listaSelectItemFormaPagamento;
    private List<MovPagamentoVO> listaSelectItemMovPagamento;
    private List<SelectItem> listaSelectItemBanco;
    private List listaValidacaoParcela;
    private List listaSelectItemConvenioCobranca;
    private boolean apresentarBotaoRecibo = false;
    private TipoOperacaoContaCorrenteEnum tipoOperacaoContaCorrenteEnum = TipoOperacaoContaCorrenteEnum.TOCC_Nenhum;
    private List listaSelectItemReceberOuDevolver;
    ReciboPagamentoVO reciboObj = null;
    private PagamentoMovParcelaVO pagamentoMovParcelaVO;
    private MovimentoContaCorrenteClienteVO movContaCorrenteCliente;
    private VendaAvulsaVO vendaAvulsaVO;
    private ChequeVO cheque;
    private ChequeVO chequeClone;
    private String campoConsultaPessoa;
    private String valorConsultaPessoa;
    private Double valorResiduo;
    private Double valorResiduoBaseCalculo;
    private Double totalLancado;
    private Double totalDivida;
    private Double saldoInicial;
    private Boolean autorizar;
    private Boolean abrirRichModalContaCorrente;
    private Boolean abrirRichModalCheque;
    private Boolean abrirRichModalMensagem;
    private Boolean abrirRichModalConfirmacaoPagamento;
    private Boolean apresentarCheque;
    private Boolean apresentarBotoesControle;
    private Boolean receberTroco;
    private Boolean apresentarPanelContratoPrestacaoServico;
    private Boolean apresentarBotaoImprimirContrato;
    private Boolean apresentarBotaoImprimirContratoServico;
    private Integer codigoContrato;
    private Integer codigoVendaAvulsa;
    private List<PlanoTextoPadraoVO> listaSelectModelosContratoPrestacao;
    private PlanoTextoPadraoVO textoContratoPrestacao;
    private String mensagemPagamento;
    private String nomePagador;
    private boolean apresentarPagamentoDigital = false;
    private List<MovPagamentoVO> pagamentos;
    private UsuarioVO usuarioLiberacao;
    private boolean incluirCupom;
    private Date dataAuxiliar;
    private Date dataPagto;
    private UsuarioVO responsavelDataPagto;
    private boolean autorizadoDataPagto;
    private boolean apresentarAlteracaoDataBasePagtoContrato = false;
    private List<SelectItem> listaSelectItemNrParcelaCartao = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectDiasMes = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemNrParcelas = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemOperadoraCartaoCredito = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemAdquirente = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemOperadoraCartaoDebito = new ArrayList<SelectItem>();
    private List<SelectItem> listaSelectItemOperadoraCartaoDebitoOnline;
    private int qtdeCheques = 0;
    private boolean consideraParcelas = false;
    private List<ParcelasPorMes> mesesReferencia = new ArrayList<ParcelasPorMes>();
    private EmpresaVO empresa = new EmpresaVO();

    private List<SelectItem> listaSelectItemCobrarNaoCobrarMulta;
    private Boolean opcaoCobrarMulta;
    private Double valorMultaJuros;
    private Boolean apresentarlinkCliente;
    private Date dtLancamentoContrato;
    private boolean permitirCalcularJuros = false;
    private List<MovParcelaVO> parcelasMultasJuros = new ArrayList<MovParcelaVO>();
    private PinpadTO pinpad = new PinpadTO();
    private boolean controlarTransacao = true;
    private Double totalPontosParceiroFidelidade;
    private List<MovPagamentoVO> listaPagamentosParceiroFidelidade;
    private ConfiguracaoSistemaVO configuracaoSistema;
    private String msgGeoitd;
    private String msgGeoitdPos;
    private String onCompletePinpad;
    private boolean confirmarPinpad = false;
    private boolean processandoPinpad = false;
    private PinPadPedidoVO pinPadPedidoVO;
    private List<PinPadVO> listaPinpad;
    private Integer nrParcelasPinpad;
    private PinPadVO pinpadVOSelecionado;
    private Integer pinpadSelecionado;
    private OpcoesPinpadEnum opcoesPinpadSelecionado;
    private boolean apresentarParcelasPinpad = false;

    private Integer codMsgGeoitd;
    private ClientGeoitd clientGeoitd;
    private List<ConvenioCobrancaVO> conveniosPix;
    private ConvenioCobrancaVO convenioPixSelecionado;
    private Integer convenioPix;
    private List<SelectItem> listaSelectItemConvenioCobrancaPix = new ArrayList<SelectItem>();
    private PixVO pixVO;
    private PixPagamentoService pixPagamentoService;
    private boolean pixConfirmado;
    private String telefonePix;
    private String emailPix;
    private boolean apresentarCPFPix = false;
    private String cpfPix;
    private boolean confirmarCobrancaPix = false;
    private String linkSendWhatsPix;
    private String onCompleteJs;
    private boolean pixGerado = false;
    private boolean clicouBotaoPagOnline;
    private List<ConvenioCobrancaVO> listaTodosConvenios;
    private boolean convenioPinPadSucesso = true;
    private boolean consultandoPixAgora = false;
    private int codReciboPagamentoPixImprimirCaixaEmAberto;

    public void erroPagamentoPinpad() {
        gravarLogCappta("erroPagamentoPinpad", "");
    }

    public void inicializarConfiguracaoSistema() {
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e) {
            throw e;
        }
    }

    public void confirmarPagamentoCappta() {
        gravarLogCappta("confirmarPagamentoCappta - " + " INICIO", "");
        MovPagamentoVO mpClone = null;
        for (Object obj : getListaSelectItemMovPagamento()) {
            MovPagamentoVO mPag = (MovPagamentoVO) obj;
            if (mPag.getFormaPagamento().getCodigo().equals(pinpad.getFormaPagamento()) && UteisValidacao.emptyString(mPag.getRespostaRequisicaoPinpad()) &&
                    UteisValidacao.emptyString(mPag.getObservacao()) && UteisValidacao.emptyString(mPag.getNumeroUnicoTransacao())) {
                try {
                    mpClone = (MovPagamentoVO) mPag.getClone(true);
                } catch (Exception e) {
                    gravarLogCappta("ErroGerarCloneMovPagamentoVOCappta - ", e.getMessage());
                }

                mPag.setObservacao(pinpad.getRetorno()); //param1
                mPag.setAutorizacaoCartao(pinpad.getAutorizacao());
                mPag.setNsu(pinpad.getNsu());
                mPag.setNumeroUnicoTransacao(pinpad.getNumeroUnicoTransacao()); //param2
                mPag.setRespostaRequisicaoPinpad(pinpad.getRespostaRequisicao()); //param3

                boolean credito = mPag.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla());
                if (credito && UteisValidacao.emptyNumber(pinpad.getNrParcelas())) {
                    pinpad.setNrParcelas(1);
                    mPag.setNrParcelaCartaoCredito(pinpad.getNrParcelas());
                }

                try {
                    BandeirasCapptaEnum bandeirasCapptaEnum = BandeirasCapptaEnum.obterPorCodigo(pinpad.getCodBandeiraCappta());
                    OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
                    if (bandeirasCapptaEnum != null) {
                        operadoraCartaoVO = getFacade().getOperadoraCartao().consultarOuCriaSeNaoExistirPorCodigoIntegracaoCappta(bandeirasCapptaEnum, credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }

                    if (UteisValidacao.emptyNumber(operadoraCartaoVO.getCodigo())) {
                        if (UteisValidacao.emptyNumber(mPag.getOperadoraCartaoVO().getCodigo())) {
                            List<OperadoraCartaoVO> listaOperadora = getFacade().getOperadoraCartao().consultarTodas(true, credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            if (!UteisValidacao.emptyList(listaOperadora)) {
                                mPag.setOperadoraCartaoVO(listaOperadora.get(0));
                            }
                        }
                    } else {
                        mPag.setOperadoraCartaoVO(operadoraCartaoVO);
                        montarListaNrParcelasCartao(operadoraCartaoVO);
                    }
                } catch (Exception ex) {
                    gravarLogCappta("ErroObterOperadoraCappta - ", ex.getMessage());
                }

                try {
                    JSONObject jsonObject = new JSONObject(pinpad.getRespostaRequisicao());
                    String adquirente = jsonObject.getString("acquirerName");
                    AdquirenteVO adquirenteVO = getFacade().getAdquirente().consultarOuCriaSeNaoExistir(adquirente);
                    mPag.setAdquirenteVO(adquirenteVO);
                } catch (Exception ex) {
                    gravarLogCappta("ErroObterAdquirenteCappta - ", ex.getMessage());
                }
            }
        }

        mpClone.setMovPagamentoEscolhida(false);
        mpClone.setValorTotal(0.0);
        mpClone.setPagamentoAberto(false);
        getListaSelectItemMovPagamento().add(mpClone);

        if (getValorResiduoBaseCalculo() == 0.0) {
            setAbrirRichModalConfirmacaoPagamento(false);
            verificarUsuarioSenhaResponsavelPagamento(false, pinpad.getPinpadEnum());
        } else {
            setMensagemPagamento("Pagamento parcial no pinpad efetuado com sucesso! Agora escolha uma forma de pagamento para receber o restante.");
            setAbrirRichModalConfirmacaoPagamento(true);
        }
        gravarLogCappta("confirmarPagamentoCappta - " + " FIM", "");
    }

    public void abrirPinpadGeoitd() {
        msgGeoitd = "";
        codMsgGeoitd = 0;
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        String jsonFinancialPurchase = DadosGeoitdFinancialPurchaseTO.montarJSON(obj);
        validarDadosPagamento(OpcoesPinpadEnum.GEOITD, true);
        try {
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "MovPagamento", ConstantesAcesso.INCLUIR);
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "VendaAvulsa", ConstantesAcesso.INCLUIR);

            if (clientGeoitd == null) {
                clientGeoitd = new ClientGeoitd();
            }

            JSONObject respostaClientGeoidt = clientGeoitd.retornoEndpointGeoitd(jsonFinancialPurchase);
            codMsgGeoitd = respostaClientGeoidt.getInt("msgCode");
            //msgGeoitd = "Status: " + respostaClientGeoidt.getInt("msgCode") + " - " + respostaClientGeoidt.getString("descCode");
            msgGeoitd = respostaClientGeoidt.getString("descCode");

        } catch (Exception ex) {
            msgGeoitd = ex.getMessage();
            montarErro(ex);
            setMsgAlert(msgGeoitd);
        }
    }

    public void modalGeoitd() {
        abrirPinpadGeoitd();
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlPinpadGeoitd');");
    }

    public void posPinpadGeoitd() throws IOException {
        clientGeoitd.posComunicacaoPinpadGeoitd();
        for (Object obj : getListaSelectItemMovPagamento()) {
            MovPagamentoVO mPag = (MovPagamentoVO) obj;
            mPag.setAutorizacaoCartao(clientGeoitd.posComunicacaoPinpadGeoitd().getString("AuthorizationCode"));
        }

        if (clientGeoitd.posComunicacaoPinpadGeoitd().getString("PosResponseCode").equals(RespostaPosPinpad.RESPONSE00.getCod())) {
            verificarUsuarioSenhaResponsavelPagamento(false, OpcoesPinpadEnum.GEOITD);
        } else {
            msgGeoitdPos = RespostaPosPinpad.consultarPorCod(clientGeoitd.posComunicacaoPinpadGeoitd().getString("PosResponseCode"));
        }
    }

    public void abrirPinpadCappta() {
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        pinpad.setFormaPagamento(obj.getFormaPagamento().getCodigo());
        pinpad.setPinpadCappta(true);
        pinpad.setTipo(obj.getTipoParcelamento());
        pinpad.setValorPinpad(obj.getValorTotal());
        pinpad.setNrParcelas(obj.getNrParcelaCartaoCredito());
        pinpad.setPinpadEnum(OpcoesPinpadEnum.CAPPTA);
        validarDadosAbrirModalCappta();
        try {
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "MovPagamento", ConstantesAcesso.INCLUIR);

            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "VendaAvulsa", ConstantesAcesso.INCLUIR);

            if (getAbrirRichModalConfirmacaoPagamento()) {
                gravarLogCappta("abrirPinpadCappta", "");

                //DADOS DE MOCK PARA TESTE
                //Quando for testar, decomenta as linhas abaixo e comenta as linhas abaixo de CODIGO DE PRODUCAO
//                int numeroPagamentosLinxSelecionados = 0;
//                Iterator i = getListaSelectItemMovPagamento().iterator();
//                while (i.hasNext()) {
//                    MovPagamentoVO mpSelecionado = (MovPagamentoVO) i.next();
//                    if (mpSelecionado.getMovPagamentoEscolhida() && mpSelecionado.getFormaPagamento().getPinpad().isCAPPTA()) {
//                        numeroPagamentosLinxSelecionados += 1;
//                    }
//                }
//                setMsgAlert("mockDadosTesteLinx(" + numeroPagamentosLinxSelecionados + ");");

                //CODIGO DE PRODUCAO
                setMsgAlert(obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())
                        ? "creditPayment();" : "debitPayment();");
            } else {
                setMsgAlert(getMensagemNotificar());
            }
        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public void validarDadosAbrirModalCappta() {
        try {
            limparMsg();
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(2);
            calcularPagamentoInput();

            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
            if (movParcelaControle == null && vendaAvulsaControle == null && parcelaControle == null && aulaAvulsaDiariaControle == null) {
                throw new Exception("Lista de Parcelas não foi gerada.");
            }

            for (Object obj : getListaValidacaoParcela()) {
                MovParcelaVO mp = (MovParcelaVO) obj;
                validarParcelaRemessa(mp);
            }

            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj = (MovPagamentoVO) i.next();
                if (obj.getMovPagamentoEscolhida()) {
                    if ((obj.getOpcaoPagamentoCartaoCredito() || obj.getOpcaoPagamentoCartaoDebito()) && obj.getValorTotal() <= 0 && obj.getFormaPagamento().getPinpad().isCAPPTA()) {
                        throw new Exception("Sem valor para cobrança.");
                    }

                    if (getValorResiduoBaseCalculo() < 0.00) {
                        throw new Exception("O valor pago não pode ser superior ao valor das parcelas.");
                    }
                }
            }
            setApresentarPagamentoDigital(false);
            setAbrirRichModalConfirmacaoPagamento(true);
            setSucesso(false);
            setErro(false);
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            gravarLogCappta("validarDadosPagamento - ERRO | ", e.getMessage());
            setAbrirRichModalConfirmacaoPagamento(false);
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
        }
    }

    public void abrirPinpad() {
        try {
            this.setOnCompletePinpad("");
            this.setConfirmarPinpad(false);
            this.setPinPadPedidoVO(null);
            this.setApresentarParcelasPinpad(false);
            this.setNrParcelasPinpad(null);
            this.setPinpadVOSelecionado(null);
            this.setPinpadSelecionado(null);
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");

            //Stone Connect cobrando valor errado. Quando descobrir de onde vem o problema, apagar esses logs.
            Uteis.logarDebug("Stone Connect - MovPagamentoControle.abrirPinpad - obj.getValorTotal: " + obj.getValorTotal());

            List<PinPadVO> listaPinpad = getFacade().getPinPad().consultarPorFormaEmpresa(obj.getFormaPagamento().getCodigo(), obj.getEmpresa().getCodigo(), null);
            Set<Integer> tiposPinpad = new HashSet<>();
            for (PinPadVO pinPadVO : listaPinpad) {
                tiposPinpad.add(pinPadVO.getOpcoesPinpadEnum().getCodigo());
            }

            pinpad = new PinpadTO();
            pinpad.setFormaPagamento(obj.getFormaPagamento().getCodigo());
            pinpad.setPinpadCappta(false);
            pinpad.setPinpadEnum(tiposPinpad.size() == 1 ? listaPinpad.get(0).getOpcoesPinpadEnum() : OpcoesPinpadEnum.STONE_CONNECT);
            pinpad.setTipo(obj.getTipoParcelamento());
            pinpad.setValorPinpad(obj.getValorTotal());

            validarDadosPagamento(pinpad.getPinpadEnum(), true);
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "MovPagamento", ConstantesAcesso.INCLUIR);
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "VendaAvulsa", ConstantesAcesso.INCLUIR);
            if (obj.getValorTotal() < getTotalDivida()) {
                throw new Exception("O valor total do pagamento deve ser quitado apenas pelo pinpad, não sendo permitido mais de uma forma de pagamento");
            }

            Integer qtdSelecionado = 0;
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj1 = (MovPagamentoVO) i.next();
                if (obj1.getMovPagamentoEscolhida()) {
                    qtdSelecionado++;
                }
            }
            if (qtdSelecionado > 1) {
                throw new Exception("Deve ser selecionado somente uma forma de pagamento");
            }

            //consultar os pinpad
            this.setListaPinpad(listaPinpad);
            this.setApresentarParcelasPinpad(obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()));

            if (this.getListaPinpad().size() == 1) {
                preencherObjPinpadVOSelecionado(this.getListaPinpad().get(0));
                this.setPinpadSelecionado(this.getPinpadVOSelecionado().getCodigo());

                //não precisa apresentar então vamos já gerar o pedido
                if (!this.isApresentarParcelasPinpad()) {
                    criarPedidoPinpad();
                }
            } else {
                limparPinpadSelecionado();
            }

            String onComplete = "Notifier.cleanAll();Richfaces.showModalPanel('modalPinpad');";
            if (this.getPinPadPedidoVO().getPinpad() != null &&
                    this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.GETCARD) &&
                    !UteisValidacao.emptyString(this.getOnCompletePinpad())) {
                onComplete += this.getOnCompletePinpad();
            }
            this.setOnCompletePinpad(onComplete);

            //Stone Connect cobrando valor errado. Quando descobrir de onde vem o problema, apagar esses logs.
            if (pinpad.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                Uteis.logarDebug("Stone Connect - MovPagamentoControle.abrirPinpad - pinpad.valorPinpad: " + pinpad.getValorPinpad());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            limparPinpadSelecionado();
            this.setOnCompletePinpad(getMensagemNotificar());
        }
    }

    private void limparPinpadSelecionado() {
        if (this.getListaPinpad().size() > 1) {
            this.setPinpadSelecionado(null);
            this.setNrParcelasPinpad(0);
        }
    }

    public void selecionarPinPad() throws Exception {
        setConvenioPinPadSucesso(true);
        try {
            this.setOnCompletePinpad("");

            if (UteisValidacao.emptyNumber(this.getPinpadSelecionado())) {
                this.setPinpadVOSelecionado(null);
                return;
            }
            for (PinPadVO pinPadVO1 : this.getListaPinpad()) {
                if (pinPadVO1.getCodigo().equals(this.getPinpadSelecionado())) {
                    preencherObjPinpadVOSelecionado(pinPadVO1);
                    break;
                }
            }
            //pagamento de débito então já gera o pedido
            if (!this.isApresentarParcelasPinpad()) {
                criarPedidoPinpad();
            }
        } catch (Exception ex) {
            setConvenioPinPadSucesso(false);
            ex.printStackTrace();
            montarErro(ex);
            this.setOnCompletePinpad(getMensagemNotificar());
        }
    }

    private void preencherObjPinpadVOSelecionado(PinPadVO pinPadVO1) throws Exception {
        try {
            this.setPinpadVOSelecionado(pinPadVO1);

            if (this.getPinpad() == null) {
                this.setPinpad(new PinpadTO());
            }

            this.getPinpad().setPinpadEnum(this.getPinpadVOSelecionado().getOpcoesPinpadEnum());
            if (!UteisValidacao.emptyNumber(this.getPinpadVOSelecionado().getConvenioCobranca().getCodigo())) {
                List<ConvenioCobrancaVO> convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigo(this.getPinpadVOSelecionado().getConvenioCobranca().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (convenioCobrancaVO != null && !UteisValidacao.emptyList(convenioCobrancaVO) && convenioCobrancaVO.get(0).getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
                    this.getPinpad().setConvenioCobranca(this.getPinpadVOSelecionado().getConvenioCobranca().getCodigo());
                } else {
                    throw new Exception("O Pinpad cadastrado está vinculado com um convênio de cobrança INATIVO. Não será possível prosseguir.");
                }
            } else {
                throw new Exception("O Pinpad cadastrado não tem convênio de cobrança vinculado. Abra o cadastro de \"PinPad\" e revise a configuração.");
            }
        } catch (Exception ex) {
            throw ex;
        }
    }

    public void criarPedidoPinpad() {
        try {

            //Stone Connect cobrando valor errado. Quando descobrir de onde vem o problema, apagar esses logs.
            if (pinpad.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                Uteis.logarDebug("Stone Connect - MovPagamentoControle.criarPedidoPinpad Inicio - pinpad.valorPinpad: " + pinpad.getValorPinpad());
            }

            this.setOnCompletePinpad("");
            this.setConfirmarPinpad(false);
            this.setPinPadPedidoVO(null);

            MovPagamentoVO obj = null;
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj1 = (MovPagamentoVO) i.next();
                if (obj1.getMovPagamentoEscolhida()) {
                    obj = obj1;
                    break;
                }
            }

            if (obj == null) {
                throw new Exception("Por favor, repita a operação");
            }

            validarDadosPagamento(pinpad.getPinpadEnum(), true);
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "MovPagamento", ConstantesAcesso.INCLUIR);
            validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "VendaAvulsa", ConstantesAcesso.INCLUIR);

            //Venda Avulsa Consumidor
            if (UteisValidacao.emptyString(obj.getPessoa().getNome()) && !UteisValidacao.emptyString(obj.getTipoPagador())
                    && obj.getTipoPagador().equals("CN")) {
                tratarPessoaConsumidor(obj);
            }

            if (obj.getValorTotal() < getTotalDivida()) {
                throw new Exception("O valor total do pagamento deve ser quitado apenas pelo pinpad, não sendo permitido mais de uma forma de pagamento");
            }

            if (obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                if (UteisValidacao.emptyNumber(this.getNrParcelasPinpad())) {
                    throw new Exception("Selecione o número de parcelas");
                } else {
                    obj.setNrParcelaCartaoCredito(this.getNrParcelasPinpad());
                    pinpad.setNrParcelas(obj.getNrParcelaCartaoCredito());
                }
            }

            //Stone Connect cobrando valor errado. Quando descobrir de onde vem o problema, apagar esses logs.
            if (pinpad.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                Uteis.logarDebug("Stone Connect - MovPagamentoControle.criarPedidoPinpad Inicio If - pinpad.valorPinpad: " + pinpad.getValorPinpad());
            }

            if (getAbrirRichModalConfirmacaoPagamento()) {
                ///criar pedido stonne connect

                if (pinpad.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                    StoneConnectService.validarPedidoAguardandoEFechar(this.getPinpadVOSelecionado(), Conexao.getFromSession());

                    List<MovParcelaVO> listaParcelas = !UteisValidacao.emptyList(getListaValidacaoParcela()) ? getListaValidacaoParcela() : null;

                    verificarExisteParcelaDuplica(listaParcelas);

                    this.setPinPadPedidoVO(StoneConnectService.gerarPedido(getKey(), obj.getFormaPagamento().getTipoFormaPagamentoEnum(),
                            obj.getPessoa(), obj.getEmpresa(), this.getPinpadVOSelecionado(), pinpad, obj.getFormaPagamento(),
                            OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO, getUsuarioLogado(), listaParcelas, Conexao.getFromSession()));
                    if (this.getPinPadPedidoVO().getStatus() == null ||
                            !this.getPinPadPedidoVO().getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {
                        throw new Exception(this.getPinPadPedidoVO().getMsg());
                    }

                    //confirmar o pedido para habilitar o poll
                    this.setConfirmarPinpad(true);

                } else if (pinpad.getPinpadEnum().equals(OpcoesPinpadEnum.GETCARD)) {
					List<MovParcelaVO> listaParcelas = !UteisValidacao.emptyList(getListaValidacaoParcela()) ? getListaValidacaoParcela() : null;
                    PinPadPedidoVO pedidoVO = getFacade().getPinPadPedido().criarPedido(obj.getPessoa(), pinpad.getValorPinpad(),
                            pinpad.getPinpadEnum(), obj.getEmpresa(), this.getPinpadVOSelecionado().getConvenioCobranca(),
                            this.getPinpadVOSelecionado().getPdvPinpad(), obj.getFormaPagamento(), OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO,
                            getUsuarioLogado(), listaParcelas);
                    this.setPinPadPedidoVO(pedidoVO);
                    this.getPinPadPedidoVO().setParamsEnvio(this.getBodyPinPadGetCard());
                    getFacade().getPinPadPedido().alterar(this.getPinPadPedidoVO());
                    this.setOnCompletePinpad("cobrarGetcard();");

                } else {
                    throw new Exception("Não implementado.");
                }

            } else {
                //erro
                limparPinpadSelecionado();
                this.setOnCompletePinpad(getMensagemNotificar());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            this.setNrParcelasPinpad(0);
            limparPinpadSelecionado();
            this.setOnCompletePinpad(getMensagemNotificar());
        }
    }

    private void verificarExisteParcelaDuplica(List<MovParcelaVO> listaParcelas) throws Exception {
        if (!UteisValidacao.emptyList(listaParcelas) && listaParcelas.size() > 1) {
            Map<Integer, MovParcelaVO> mapParcelasVerificadas = new HashMap<>();

            for (MovParcelaVO parcela : listaParcelas) {
                Integer codigo = parcela.getCodigo();
                if (mapParcelasVerificadas.containsKey(codigo)) {
                    throw new Exception("Parcela duplicada encontrada. Por favor, volte ao Caixa em Aberto e repita a operação.");
                }
                mapParcelasVerificadas.put(codigo, parcela);
            }
        }
    }

    public void tratarPessoaConsumidor(MovPagamentoVO movPagamentoVO) {
        if (!UteisValidacao.emptyString(movPagamentoVO.getNomePagador())) {
            movPagamentoVO.getPessoa().setNome(movPagamentoVO.getNomePagador());
        }
    }

    public void cancelarPinpad() {
        String oncomplete = "";
        try {
            limparMsg();
            setOnCompletePinpad("");

            if (!UteisValidacao.emptyNumber(this.getPinPadPedidoVO().getCodigo())) {
                if (this.getPinpad().getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                    StoneConnectService.fecharPedido(this.getPinPadPedidoVO(), StatusPinpadEnum.FALHA, false, true, Conexao.getFromSession());
                } else if (this.getPinpad().getPinpadEnum().equals(OpcoesPinpadEnum.GETCARD)) {
                    this.getPinPadPedidoVO().setStatus(StatusPinpadEnum.ABORTADO);
                    getFacade().getPinPadPedido().alterarStatus(this.getPinPadPedidoVO());
                    oncomplete = "abortarOperacaoGetcard();";
                }
            }

            limparPinpadSelecionado();
            this.setConfirmarPinpad(false);
            this.setNrParcelasPinpad(null);
            montarSucessoGrowl("Pedido cancelado com sucesso!");
            setOnCompletePinpad(oncomplete + "Richfaces.hideModalPanel('modalPinpad');" + getMensagemNotificar());
        } catch (Exception ex) {
            montarErro(ex);
            setOnCompletePinpad(oncomplete + "Richfaces.hideModalPanel('modalPinpad');" + getMensagemNotificar());
        }
    }

    public void confirmarPagamentoPinpad() {
        try {
            limparMsg();
            this.setOnCompletePinpad("");
            if (this.isConfirmarPinpad() && !this.isProcessandoPinpad()) {
                try {
                    this.setProcessandoPinpad(true);

                    if (UteisValidacao.emptyNumber(this.getPinPadPedidoVO().getCodigo())) {
                        return;
                    }

                    if (this.getPinPadPedidoVO().getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {

                        PedidoRetornoDTO pedidoRetornoDTO = null;
                        if (this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                            pedidoRetornoDTO = getFacade().getPinPadPedido().consultarPedido(this.getPinPadPedidoVO());

                        } else if (this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.GETCARD)) {
                            try {
                                pedidoRetornoDTO = new PedidoRetornoDTO();
                                pedidoRetornoDTO.setDados(this.getPinPadPedidoVO().getParamsResp());

                                JSONObject jsonResp = new JSONObject(this.getPinPadPedidoVO().getParamsResp());
                                JSONObject response = jsonResp.getJSONObject("response");
                                pedidoRetornoDTO.setStatus(response.optBoolean("Sucesso") ? StatusPinpadEnum.PAGO : StatusPinpadEnum.AGUARDANDO);

                                if (response.optBoolean("Sucesso")) {
                                    String cupomReduzido = response.getString("CupomReduzido").replaceAll("\n", " ");

                                    try {
                                        String autorizacao = "";
                                        if (cupomReduzido.contains("AUT=")) {
                                            autorizacao = cupomReduzido.split("AUT=")[1].split(" ")[0];
                                        } else if (cupomReduzido.contains("AUT:")) {
                                            autorizacao = cupomReduzido.split("AUT:")[1].split(" ")[0];
                                        }
                                        pedidoRetornoDTO.setAutorizacao(autorizacao.trim());
                                    } catch (Exception ignored) {
                                    }

                                    try {
                                        String nsu = "";
                                        if (cupomReduzido.contains("DOC=")) {
                                            nsu = cupomReduzido.split("DOC=")[1].split(" ")[0];
                                        } else if (cupomReduzido.contains("NSU:")) {
                                            nsu = cupomReduzido.split("NSU:")[1].split(" ")[0];
                                        } else if (cupomReduzido.contains("NSU=")) {
                                            nsu = cupomReduzido.split("NSU=")[1].split(" ")[0];
                                        }

                                        pedidoRetornoDTO.setNsu(nsu.trim());
                                    } catch (Exception ignored) {
                                    }

                                    try {
                                        String bandeira = cupomReduzido.split(" ")[0];
                                        pedidoRetornoDTO.setBandeira(bandeira.trim());
                                    } catch (Exception ignored) {
                                    }

                                    try {
                                        String cupomCliente = response.getString("CupomCliente");
                                        String adquirente = "";
                                        if (cupomCliente.toUpperCase().contains("CIELO")) {
                                            adquirente = "CIELO";
                                        } else if (cupomCliente.toUpperCase().contains("GETNET") ||
                                                cupomCliente.toUpperCase().contains("GET NET")) {
                                            adquirente = "GETNET";
                                        } else if (cupomCliente.toUpperCase().contains("STONE")) {
                                            adquirente = "STONE";
                                        }
                                        pinpad.setAdquirente(adquirente);
                                    } catch (Exception ignored) {
                                    }
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }

                        if (pedidoRetornoDTO != null &&
                                pedidoRetornoDTO.getStatus() != null &&
                                !pedidoRetornoDTO.getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {

                            //Adicionar no Log para analise de problema de cobrar do aluno, mas não dar baixa no sistema de forma automática
                            if (this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                                gravarLogStoneConnect("confirmarPagamentoPinpad - INICIO", pedidoRetornoDTO.getDados());
                            }

                            setConfirmarPinpad(false);
                            this.getPinPadPedidoVO().setStatus(pedidoRetornoDTO.getStatus());

                            if (this.getPinPadPedidoVO().getStatus().equals(StatusPinpadEnum.PAGO)) {
                                setConfirmarPinpad(false);
                                pinpad.setCodigoAutorizacao(pedidoRetornoDTO.getAutorizacao());
                                pinpad.setCodigoNSU(pedidoRetornoDTO.getNsu());
                                pinpad.setBandeira(pedidoRetornoDTO.getBandeira());
                                pinpad.setRespostaRequisicao(pedidoRetornoDTO.getDados());
                                pinpad.setConvenioCobranca(this.getPinPadPedidoVO().getConvenioCobrancaVO().getCodigo());
                                pinpad.setPinpadPedido(this.getPinPadPedidoVO().getCodigo());
                                gerarPagamentoPinpad();
                                setAbrirRichModalMensagem(false);
                                setSucesso(true);
                                setErro(false);
                                setMensagem("Pagamento Efetuado com Sucesso");
                                setMensagemPagamento("Pagamento Efetuado Com Sucesso.");
                                montarSucessoGrowl("Pagamento Efetuado Com Sucesso");
                                setMensagemDetalhada("");
                                setOnCompletePinpad("try{Richfaces.hideModalPanel('modalPinpad');Notifier.success('Pagamento Efetuado com Sucesso');}catch(e){console.log(e)}");
                                setApresentarBotaoRecibo(true);
                                setApresentarBotoesControle(false);
                                setApresentarAlteracaoDataBasePagtoContrato(false);
                                setValorMultaJuros(null);
                            } else if (this.getPinPadPedidoVO().getStatus().equals(StatusPinpadEnum.FALHA) ||
                                    this.getPinPadPedidoVO().getStatus().equals(StatusPinpadEnum.CANCELADO)) {
                                setConfirmarPinpad(false);
                                montarErro("Pedido cancelado");
                                setOnCompletePinpad("Richfaces.hideModalPanel('modalPinpad');" + getMensagemNotificar());
                            }
                            try {
                                if (this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.GETCARD) || this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                                    getFacade().getPinPadPedido().alterar(this.getPinPadPedidoVO());
                                    if (this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                                        gravarLogStoneConnect("confirmarPagamentoPinpad - FIM", pedidoRetornoDTO.getDados());
                                    }
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                } finally {
                    this.setProcessandoPinpad(false);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
            setOnCompletePinpad(getMensagemNotificar());
            if (this.getPinPadPedidoVO().getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                gravarLogStoneConnect("confirmarPagamentoPinpad - ERRO", ex.getMessage());
            }
        }
    }

    private void gerarPagamentoPinpad() {
        for (Object obj : getListaSelectItemMovPagamento()) {
            MovPagamentoVO mPag = (MovPagamentoVO) obj;
            if (mPag.getMovPagamentoEscolhida() != null &&
                    mPag.getMovPagamentoEscolhida() &&
                    mPag.getFormaPagamento().getCodigo().equals(pinpad.getFormaPagamento())) {
                mPag.setAutorizacaoCartao(pinpad.getAutorizacao());
                mPag.setNsu(pinpad.getNsu());
                mPag.setRespostaRequisicaoPinpad(pinpad.getDadosPinPadGravar());
                mPag.setConvenio(new ConvenioCobrancaVO());
                mPag.getConvenio().setCodigo(pinpad.getConvenioCobranca());

                boolean credito = mPag.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla());
                if (credito) {
                    if (UteisValidacao.emptyNumber(pinpad.getNrParcelas())) {
                        pinpad.setNrParcelas(1);
                    }
                    mPag.setNrParcelaCartaoCredito(pinpad.getNrParcelas());
                }

                try {
                    OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
                    if (pinpad.getOperadorasExternasAprovaFacilEnum() != null) {
                        operadoraCartaoVO = getFacade().getOperadoraCartao().consultarOuCriaSeNaoExistirPinpad(pinpad.getOperadorasExternasAprovaFacilEnum(),
                                pinpad.getPinpadEnum(), credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }

                    if (UteisValidacao.emptyNumber(operadoraCartaoVO.getCodigo())) {
                        if (UteisValidacao.emptyNumber(mPag.getOperadoraCartaoVO().getCodigo())) {
                            List<OperadoraCartaoVO> listaOperadora = getFacade().getOperadoraCartao().consultarTodas(true, credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            if (!UteisValidacao.emptyList(listaOperadora)) {
                                mPag.setOperadoraCartaoVO(listaOperadora.get(0));
                            }
                        }
                    } else {
                        mPag.setOperadoraCartaoVO(operadoraCartaoVO);
                        montarListaNrParcelasCartao(operadoraCartaoVO);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                try {
                    if (!UteisValidacao.emptyString(pinpad.getAdquirenteGravar())) {
                        mPag.setAdquirenteVO(getFacade().getAdquirente().consultarOuCriaSeNaoExistir(pinpad.getAdquirenteGravar()));
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
        validarDadosPagamento(pinpad.getPinpadEnum(), false);
        if (getAbrirRichModalConfirmacaoPagamento()) {
            setAbrirRichModalConfirmacaoPagamento(false);
            verificarUsuarioSenhaResponsavelPagamento(false, pinpad.getPinpadEnum());
        }
        if (getSucesso()) {
            setMensagemPagamento("Pagamento Efetuado Com Sucesso.");
        }
    }

    public void setPagamentos(List<MovPagamentoVO> formasPagamento) {
        this.pagamentos = formasPagamento;
    }

    public List<MovPagamentoVO> getPagamentos() {
        if (pagamentos == null) {
            pagamentos = new ArrayList<MovPagamentoVO>();
        }
        return pagamentos;
    }

    public void setApresentarPagamentoDigital(boolean apresentarPagamentoDigital) {
        this.apresentarPagamentoDigital = apresentarPagamentoDigital;
    }

    public boolean isApresentarPagamentoDigital() {
        return apresentarPagamentoDigital;
    }

    public void fecharPagamentoDigital() {
        setApresentarPagamentoDigital(false);
    }

    public String getAbrirPagamentoDigital() {
        if (isApresentarPagamentoDigital()) {
            return "Richfaces.showModalPanel('panelStatus1'),submitPagamentoDigital();";
        } else {
            return "Richfaces.hideModalPanel('panelStatus1')";
        }
    }

    public MovPagamentoControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        inicializarConfiguracaoSistema();
        setMensagemID("msg_entre_prmconsulta");
        novo();
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            getMovPagamentoVO().getResponsavelPagamento().setCodigo(getUsuarioLogado().getCodigo());
            getMovPagamentoVO().getResponsavelPagamento().setUsername(getUsuarioLogado().getUsername());
            getMovPagamentoVO().getResponsavelPagamento().setUserOamd(getUsuarioLogado().getUserOamd());
            getMovContaCorrenteCliente().getResponsavelAutorizacao().setCodigo(getUsuarioLogado().getCodigo());
            getMovContaCorrenteCliente().getResponsavelAutorizacao().setUsername(getUsuarioLogado().getUsername());
            getMovContaCorrenteCliente().getResponsavelAutorizacao().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception e) {
            throw e;
        }
    }

    public void addNovoCartao() throws Exception {
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        MovPagamentoVO novoCartao = (MovPagamentoVO) obj.getClone(obj);
        novoCartao.setValorTotal(valorResiduoBaseCalculo);
        novoCartao.setAutorizacaoCartao("");
        novoCartao.setAdquirenteVO(new AdquirenteVO());
        novoCartao.getAdquirenteVO().setCodigo(obj.getAdquirenteVO().getCodigo());
        novoCartao.setNsu("");
        novoCartao.setOperadoraCartaoVO(new OperadoraCartaoVO());
        int indexOf = getListaSelectItemMovPagamento().indexOf(obj);
        getListaSelectItemMovPagamento().add(indexOf + 1, novoCartao);
        calcularPagamentoInput();
    }

    public void inicializarMovPagamento() throws Exception {
        try {
            setApresentarPanelContratoPrestacaoServico(false);
            setListaSelectItemFormaPagamento(getFacade().getFormaPagamento().
                    consultarPorDescricaoTipoFormaPagamento("", false, true, true, true,
                            empresa.getCodigo(),
                            Uteis.NIVELMONTARDADOS_TODOS));

            //colocar a forma de pagamento "Conta Corrente" por ultimo, para tela nao ter uma linha escondida no meio de todas as outras formas de pagamento
            List<FormaPagamentoVO> lista = new ArrayList(getListaSelectItemFormaPagamento());
            List<FormaPagamentoVO> listaValidandoPerfilAcesso = new ArrayList<FormaPagamentoVO>();
            for (FormaPagamentoVO formaPagamentoVO : lista) {
                boolean adicionar = getUsuarioLogado().getAdministrador() || formaPagamentoVO.validarPerfilAcesso(getPerfilUsuarioLogado());
                if (adicionar) {
                    listaValidandoPerfilAcesso.add(formaPagamentoVO);
                }
            }
            setListaSelectItemFormaPagamento(listaValidandoPerfilAcesso);
            inserirContaCorrentePorUltimo();

            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            if (movParcelaControle != null) {
                pagamentoDeMovParcela(movParcelaControle);
            }
            ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
            if (contratoControle != null && contratoControle.getContratoVO().getCodigo() != null && contratoControle.getContratoVO().getCodigo().intValue() != 0
                    && movParcelaControle == null) {
                setDtLancamentoContrato(contratoControle.getContratoVO().getDataLancamento());
                setApresentarBotaoImprimirContrato(true);
                setCodigoContrato(contratoControle.getContratoVO().getCodigo());
                parcelaControle = new MovParcelaControle();
                List<MovParcelaVO> listaParcelas = getFacade().getMovParcela().
                        consultarPorContrato(contratoControle.getContratoVO().getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS, "EA");

                //se contrato se refere a plano de recorrência, habilitar para receber apenas a primeira parcela
                //as demais serão cobradas automaticamente pelo serviço de cobrança
                if (contratoControle.getContratoVO().isRegimeRecorrencia() && contratoControle.getContratoVO().getContratoRecorrenciaVO() != null
                        && !UteisValidacao.emptyNumber(contratoControle.getContratoVO().getContratoRecorrenciaVO().getCodigo())) {

                    boolean encontrou = false;
                    for (MovParcelaVO movParcelaVO : listaParcelas) {
                        if (Calendario.igual(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                            parcelaControle.getListaParcelasPagar().add(movParcelaVO);
                            encontrou = true;
                        }
                    }

                    if (!encontrou && listaParcelas.size() > 0) {
                        parcelaControle.getListaParcelasPagar().add(listaParcelas.get(0));
                    }
                } else {
                    parcelaControle.setListaParcelasPagar(listaParcelas);
                }

                Double valorPagar = 0.0;
                for (MovParcelaVO movParcelaVO : parcelaControle.getListaParcelasPagar()) {
                    valorPagar += movParcelaVO.getValorParcela();
                }
                parcelaControle.setValorTotalParcela(valorPagar);

                Iterator i = parcelaControle.getListaParcelasPagar().iterator();
                while (i.hasNext()) {
                    MovParcelaVO m = (MovParcelaVO) i.next();
                    m.setContrato(contratoControle.getContratoVO());
                    parcelaControle.setMovParcelaVO(m);
                    break;
                }
                pagamentoDeMovParcela(parcelaControle);
            } else {
                parcelaControle = null;
            }
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            if (vendaAvulsaControle != null) {
                pagamentoDeVendaAvulsa(vendaAvulsaControle);

            }
            AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
            if (aulaAvulsaDiariaControle != null && aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO() != null
                    && aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCodigo() != null && !aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCodigo().equals(0)) {
                pagamentoDeAulaAvulsaDiaria(aulaAvulsaDiariaControle);
            }

            inicializarPagamentoParcelasContratoRecorrente();

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            throw new Exception(e.getMessage());

        }
    }

    @SuppressWarnings("unchecked")
    private void inserirContaCorrentePorUltimo() {
        List<FormaPagamentoVO> lista = new ArrayList<FormaPagamentoVO>(getListaSelectItemFormaPagamento());
        for (FormaPagamentoVO formaPagamentoVO : lista) {
            if (formaPagamentoVO.getTipoFormaPagamento().equals("CC")) {
                getListaSelectItemFormaPagamento().remove(formaPagamentoVO);
                getListaSelectItemFormaPagamento().add(formaPagamentoVO);
                break;
            }
        }
    }

    public void imprimirContratoServico() {
        try {
            List<MovPagamentoVO> pagamentos = prepararVendaEPagamento();

            if (getVendaAvulsaVO().getCodigo() != 0) {
                getVendaAvulsaVO().getTextoPadrao().substituirTagsTextoPadraoVendaAvulsa(getKey(), Conexao.getFromSession(), getVendaAvulsaVO(), pagamentos, getEmpresaLogado().getDescMoeda());
            } else {
                throw new Exception("Não foi possível emitir o contrato. Dados não encontrados!");
            }

            setMensagemID("");
            setMensagemDetalhada("", "");
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public List<MovPagamentoVO> prepararVendaEPagamento() throws Exception {

        // setar atributos do contrato

        getVendaAvulsaVO().setTextoPadrao(getFacade().getPlanoTextoPadrao().consultarPorChavePrimaria(getTextoContratoPrestacao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        getVendaAvulsaVO().getParcela().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(
                getVendaAvulsaVO().getParcela().getPessoa().getCodigo(),
                Uteis.NIVELMONTARDADOS_TODOS));
        getVendaAvulsaVO().setMovProdutoVOs(getFacade().getMovProduto().consultarPorCodigoParcela(getVendaAvulsaVO().getParcela().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        getVendaAvulsaVO().setEmpresa(
                getFacade().getEmpresa().consultarPorChavePrimaria(
                        getVendaAvulsaVO().getEmpresa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_TODOS));
        getVendaAvulsaVO().setResponsavel(
                getFacade().getUsuario().consultarPorChavePrimaria(
                        getVendaAvulsaVO().getResponsavel().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        List<MovPagamentoVO> pagamentos = getFacade().getMovPagamento().consultarPagamentoDeUmaParcela(
                getVendaAvulsaVO().getParcela().getCodigo().intValue(), false,
                Uteis.NIVELMONTARDADOS_TODOS);


        return pagamentos;

    }

    public void prepararVendaAvulsa() throws Exception {
        setVendaAvulsaVO(getFacade().getVendaAvulsa().consultarPorChavePrimaria(getCodigoVendaAvulsa(), Uteis.NIVELMONTARDADOS_TODOS));
        getVendaAvulsaVO().setParcela(getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(getCodigoVendaAvulsa(), "", false, Uteis.NIVELMONTARDADOS_TODOS));
        setListaSelectModelosContratoPrestacao(montarListaModelosContrato(0));
        textoContratoPrestacao = new PlanoTextoPadraoVO();
        setApresentarPanelContratoPrestacaoServico(true);
    }

    public List<PlanoTextoPadraoVO> montarListaModelosContrato(Integer codigoPrm)
            throws Exception {
        List resultadoConsulta = getFacade().getPlanoTextoPadrao().consultarPorCodigoSituacaoTipo(0, "AT", "SE", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();

        while (i.hasNext()) {
            PlanoTextoPadraoVO obj = (PlanoTextoPadraoVO) i.next();
            setTextoContratoPrestacao(obj);
            objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));

        }
        return objs;

    }

    private void inicializarPagamentoParcelasContratoRecorrente() throws Exception {
        JSFUtilities.removeFromSession(PagamentoCartaoCreditoControle.class.getSimpleName());
        ArrayList<MovParcelaVO> listaTemp = (ArrayList<MovParcelaVO>) getListaValidacaoParcela();
        boolean habilitarApenasCartaoCredito = false;
        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        if (empresaVO.isIrTelaPagamentoCartaoCreditoRecorrente()) {
            for (MovParcelaVO movParcelaVO : listaTemp) {
                if (movParcelaVO.getRegimeRecorrencia()) {
                    habilitarApenasCartaoCredito = true;
                } else {
                    habilitarApenasCartaoCredito = false;
                    break;
                }
            }
        }
        if (habilitarApenasCartaoCredito) {
            try {
                marcarPagamentoCartaoCreditoAprovaFacil(true, getTotalDivida());
                calcularPagamentoInput();
                this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(true);
            } catch (Exception e) {
                if (e.toString().equals("java.lang.NullPointerException")) {
                    setMensagemDetalhada("Configurações incorretas. Por favor verifique as configurações de cobrança (Operadora de Cartão, Convênio ou Autorização de cobrança) antes de tentar cobrar direto no cartão do aluno.");
                } else {
                    Integer tipos[] = {TipoConvenioCobrancaEnum.DCC.getCodigo(), TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(), TipoConvenioCobrancaEnum.DCC_BIN.getCodigo()}; //tipo 2 é referente ao dcc, 8 é getnet
                    List convenioDcc = getFacade().getConvenioCobranca().consultarPorEmpresa(getEmpresa().getCodigo(), consideraParcelas, aasEntidade, tipos);
                    if (convenioDcc.isEmpty()) {
                        setMensagemDetalhada("msg_erro", e.getMessage() + " Também não tem nenhum convênio de cobrança do tipo DCC/GETNET/BIN cadastrado.");
                    }
                }
                setSucesso(false);
                setErro(true);
            }
        }
    }

    /**
     * Responsável por verificar se existe na sessao um atributo boolean
     * trocarCartao, que indica se apos o pagamento haverá uma troca de cartão
     * do regime de recorrencia do contrato
     *
     * <AUTHOR> 04/08/2011
     */
    private Boolean trocaCartao() {
        Boolean troca = (Boolean) JSFUtilities.getFromSession("trocarCartao");
        if (troca == null) {
            troca = Boolean.FALSE;
        } else {
            JSFUtilities.removeFromSession("trocarCartao");
        }
        return troca;
    }

    public void validardatabase() {
        setMensagemDetalhada("");
        if (getDataAuxiliar() == null) {
            setMensagemDetalhada("Data Base não pode ficar vazia.");
            return;
        }
        // percorre a lista de parcelas
        if (Calendario.maior(getDataAuxiliar(), Calendario.hoje())) {
            setMensagemDetalhada("Data Base não pode ser maior que a data atual");
            setDataAuxiliar(Calendario.hoje());
        }
        Iterator i = getListaValidacaoParcela().iterator();
        while (i.hasNext()) {
            MovParcelaVO mp = (MovParcelaVO) i.next();
            // se a parcela possui data de alteração manual
            if (mp.getDataRegistro().compareTo(dataAuxiliar) > 0) {
                setMensagemDetalhada("Data de Pagamento não pode ser anterior a " + mp.getDataRegistro_Apresentar() + ", porque pelo menos uma parcela foi lançada nessa data e você não pode pagar com data anterior ao lançamento.");
                setDataAuxiliar(Calendario.hoje());
                break;
            }
        }
    }

    public void pagamentoDeMovParcela(MovParcelaControle movParcelaControle) throws Exception {
        try {
            setValorMultaJuros(null);
            setTotalLancado(0.0);
            if (getEmpresaLogado().getCobrarAutomaticamenteMultaJuros()) {
                Date dataConsiderarMultaJuros = obterDataMultaJuros();
                movParcelaControle.montarMultaJurosParcelaVencida(dataConsiderarMultaJuros);
                if (movParcelaControle.getValorMultaJuros() != null && movParcelaControle.getValorMultaJuros() != 0.0) {
                    setValorMultaJuros(movParcelaControle.getValorMultaJuros());
                }
            }
            consultaMovContaCorrenteCliente(movParcelaControle.getMovParcelaVO().getContrato().getPessoa().getCodigo());
            setValorResiduo(movParcelaControle.getValorTotalParcela() + getValorMultaJuros());
            setTotalDivida(movParcelaControle.getValorTotalParcela() + getValorMultaJuros());
            setValorResiduoBaseCalculo(movParcelaControle.getValorTotalParcela() + getValorMultaJuros());
            movParcelaControle.setListaParcelasPagar(Ordenacao.ordenarLista(movParcelaControle.getListaParcelasPagar(), "dataVencimento"));
            processarParcelas(movParcelaControle.getListaParcelasPagar());

            getFormaPagamento(movParcelaControle, null);

            if ((movParcelaControle.getMovParcelaVO().getContrato().getCodigo().intValue() != 0)
                    && ((movParcelaControle.getMovParcelaVO().getContrato().getPessoa().getCodigo().intValue() != 0))) {
                getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                        consultarPorChavePrimaria(movParcelaControle.getMovParcelaVO().
                                getContrato().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            } else if (movParcelaControle.getMovParcelaVO().getVendaAvulsaVO().getCodigo().intValue() != 0) {
                if (movParcelaControle.getMovParcelaVO().getVendaAvulsaVO().getTipoComprador().equals("CI")) {
                    getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                            consultarPorChavePrimaria(movParcelaControle.getMovParcelaVO().
                                            getVendaAvulsaVO().getCliente().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_MINIMOS));
                } else if (movParcelaControle.getMovParcelaVO().getVendaAvulsaVO().getTipoComprador().equals("CO")) {
                    getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                            consultarPorChavePrimaria(movParcelaControle.getMovParcelaVO().
                                            getVendaAvulsaVO().getColaborador().getPessoa().getCodigo(),
                                    Uteis.NIVELMONTARDADOS_MINIMOS));
                }
            } else if (movParcelaControle.getMovParcelaVO().getAulaAvulsaDiariaVO().getCodigo().intValue() != 0) {
                getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                        consultarPorChavePrimaria(movParcelaControle.getMovParcelaVO().
                                        getAulaAvulsaDiariaVO().getCliente().getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_MINIMOS));
            }

        } catch (Exception e) {
            throw e;
        }
    }

    public void pagamentoDeVendaAvulsa(VendaAvulsaControle vendaAvulsaControle) throws Exception {
        try {
            setValorResiduo(vendaAvulsaControle.getVendaAvulsaVO().getValorTotal());
            setValorResiduoBaseCalculo(getValorResiduo());
            setTotalDivida(vendaAvulsaControle.getVendaAvulsaVO().getValorTotal());
            MovParcelaVO parcela = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(
                    vendaAvulsaControle.getVendaAvulsaVO().getCodigo(), "EA",
                    false, Uteis.NIVELMONTARDADOS_TODOS);
            getListaValidacaoParcela().add(parcela);
            Iterator j = vendaAvulsaControle.getVendaAvulsaVO().getItemVendaAvulsaVOs().iterator();
            setApresentarBotaoImprimirContratoServico(false);
            while (j.hasNext()) {
                ItemVendaAvulsaVO itemVenda = (ItemVendaAvulsaVO) j.next();
                if (itemVenda.getProduto().getTipoProduto().equals("SS")) {
                    setApresentarBotaoImprimirContratoServico(true);
                    setCodigoVendaAvulsa(vendaAvulsaControle.getVendaAvulsaVO().getCodigo());
                    break;
                }

            }

            getFormaPagamento(null, parcela);

            if (vendaAvulsaControle.getVendaAvulsaVO().getTipoComprador().equals("CI")) {
                getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                        consultarPorChavePrimaria(vendaAvulsaControle.getVendaAvulsaVO().getCliente().getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_MINIMOS));
            } else if (vendaAvulsaControle.getVendaAvulsaVO().getTipoComprador().equals("CO")) {
                getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                        consultarPorChavePrimaria(vendaAvulsaControle.getVendaAvulsaVO().getColaborador().getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_MINIMOS));
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void pagamentoDeAulaAvulsaDiaria(AulaAvulsaDiariaControle aulaAvulsaDiariaControle) throws Exception {
        try {
            setValorResiduo(aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getValor());
            setValorResiduoBaseCalculo(getValorResiduo());
            setTotalDivida(aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getValor());
            MovParcelaVO parcela = getFacade().getMovParcela().consultarPorCodigoAulaAvulsaDiaria(aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS);
            getListaValidacaoParcela().add(parcela);

            getFormaPagamento(null,parcela);

            if (aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCliente().
                    getPessoa().getCodigo().intValue() != 0) {
                getMovPagamentoVO().setPessoa(getFacade().getPessoa().
                        consultarPorChavePrimaria(aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCliente().getPessoa().getCodigo(),
                                Uteis.NIVELMONTARDADOS_MINIMOS));
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarMovPagamentoVO(FormaPagamentoVO formaPagamento, MovParcelaVO movParcela) throws Exception {
        if (movParcela.getContrato().getCodigo() != 0) {
            obterPessoaPorContrato(movParcela);
        } else if (movParcela.getVendaAvulsaVO().getCodigo() != 0) {
            obterPessoaPorVendaAvulsa(movParcela);
        } else if (movParcela.getAulaAvulsaDiariaVO().getCodigo() != 0) {
            obterPessoaPorAulaAvulsaDiaria(movParcela);
        } else if (movParcela.getPersonal().getCodigo() != 0) {
            obterPessoaPorTaxaPersonal(movParcela);
        } else {
            getMovPagamentoVO().setTipoPagador("CN");
            getMovPagamentoVO().setNomePagador(movParcela.getVendaAvulsaVO().getNomeComprador());
            setNomePagador(movParcela.getVendaAvulsaVO().getNomeComprador());
        }
        getMovPagamentoVO().setEmpresa(empresa);
        getMovPagamentoVO().setDataLancamento(Calendario.hoje());
        getMovPagamentoVO().setDataPagamento(Calendario.hoje());
        getMovPagamentoVO().setFormaPagamento(formaPagamento);
        obterFormaPagamento(movParcela);
    }

    public void obterPessoaPorContrato(MovParcelaVO movParcela) {
        PessoaVO pessoaPagadora = movParcela.getContrato().getPessoa();
        if (!UteisValidacao.emptyNumber(movParcela.getContrato().getPessoaOriginal().getCodigo())) {
            pessoaPagadora = movParcela.getContrato().getPessoaOriginal();
        }
        getMovPagamentoVO().setPessoa(pessoaPagadora);
        getMovPagamentoVO().setNomePagador(getMovPagamentoVO().getPessoa().getNome());
        setNomePagador(getMovPagamentoVO().getPessoa().getNome());
        getMovPagamentoVO().setTipoPagador("CI");
        setApresentarlinkCliente(true);
    }

    public void obterPessoaPorVendaAvulsa(MovParcelaVO movParcela) {
        if (movParcela.getVendaAvulsaVO().getTipoComprador().equals("CI")) {
            getMovPagamentoVO().setTipoPagador("CI");
            getMovPagamentoVO().setPessoa(movParcela.getVendaAvulsaVO().getCliente().getPessoa());
            getMovPagamentoVO().setNomePagador(getMovPagamentoVO().getPessoa().getNome());
            setNomePagador(getMovPagamentoVO().getPessoa().getNome());
            setApresentarlinkCliente(true);
            consultaMovContaCorrenteCliente(movParcela.getVendaAvulsaVO().getCliente().getPessoa().getCodigo());
        } else if (movParcela.getVendaAvulsaVO().getTipoComprador().equals("CO")) {
            getMovPagamentoVO().setTipoPagador("CO");
            getMovPagamentoVO().setPessoa(movParcela.getVendaAvulsaVO().getColaborador().getPessoa());
            getMovPagamentoVO().setNomePagador(getMovPagamentoVO().getPessoa().getNome());
            setNomePagador(getMovPagamentoVO().getPessoa().getNome());
            setApresentarlinkCliente(false);
            consultaMovContaCorrenteCliente(movParcela.getVendaAvulsaVO().getColaborador().getPessoa().getCodigo());
        } else {
            getMovPagamentoVO().setNomePagador(movParcela.getVendaAvulsaVO().getNomeComprador());
            setNomePagador(movParcela.getVendaAvulsaVO().getNomeComprador());
            getMovPagamentoVO().setTipoPagador("CN");
            setApresentarlinkCliente(false);
            try {
                if (!UteisValidacao.emptyNumber(movParcela.getVendaAvulsaVO().getPessoaVO().getCodigo())) {
                    PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(movParcela.getVendaAvulsaVO().getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    movParcela.getVendaAvulsaVO().setPessoaVO(pessoaVO);
                    getMovPagamentoVO().setPessoa(pessoaVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public void obterPessoaPorAulaAvulsaDiaria(MovParcelaVO movParcela) {
        getMovPagamentoVO().setTipoPagador("CI");
        getMovPagamentoVO().setPessoa(movParcela.getAulaAvulsaDiariaVO().getCliente().getPessoa());
        getMovPagamentoVO().setNomePagador(getMovPagamentoVO().getPessoa().getNome());
        setNomePagador(getMovPagamentoVO().getPessoa().getNome());
        setApresentarlinkCliente(true);
        consultaMovContaCorrenteCliente(movParcela.getAulaAvulsaDiariaVO().getCliente().getPessoa().getCodigo());
    }

    public void obterPessoaPorTaxaPersonal(MovParcelaVO movParcela) {
        getMovPagamentoVO().setTipoPagador("CO");
        getMovPagamentoVO().setPessoa(movParcela.getPersonal().getPersonal().getPessoa());
        getMovPagamentoVO().setNomePagador(getMovPagamentoVO().getPessoa().getNome());
        setNomePagador(getMovPagamentoVO().getPessoa().getNome());
        setApresentarlinkCliente(false);
        consultaMovContaCorrenteCliente(movParcela.getPersonal().getPersonal().getPessoa().getCodigo());
    }

    public void obterFormaPagamento(MovParcelaVO movParcela) {
        final NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("AV")) {
            getMovPagamentoVO().setOpcaoPagamentoDinheiro(true);
        }
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("BB")) {
            getMovPagamentoVO().setOpcaoPagamentoBoleto(true);
        }
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
            getMovPagamentoVO().setOpcaoPagamentoCheque(true);
        }
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
            getMovPagamentoVO().setOpcaoPagamentoCartaoCredito(true);
        }
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("CD")) {
            getMovPagamentoVO().setOpcaoPagamentoCartaoDebito(true);
        }
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
            getMovPagamentoVO().setUsarParceiroFidelidade(true);
            getMovPagamentoVO().setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.RESGATAR);
            getMovPagamentoVO().setOpcaoPagamentoParceiroFidelidade(true);
        }
        if (getMovPagamentoVO().getFormaPagamento().isGerarPontos() && !getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
            getMovPagamentoVO().setUsarParceiroFidelidade(true);
            getMovPagamentoVO().setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.ACUMULAR);
        }
        if (getMovPagamentoVO().getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
            if (movParcela.getVendaAvulsaVO().getTipoComprador().equals("CO")
                    || movParcela.getVendaAvulsaVO().getTipoComprador().equals("CN")
                    || movParcela.getPersonal().getCodigo().intValue() > 0) {
                getMovPagamentoVO().setOpcaoPagamentoContaCorrenteCliente(false);
                getMovPagamentoVO().setMostraContaCorrenteAcademia(false);
            } else {
                getMovPagamentoVO().setOpcaoPagamentoContaCorrenteCliente(true);
            }
            if (getMovContaCorrenteCliente() != null) {
                if (getMovContaCorrenteCliente().getSaldoAtual() >= 0) {
                    getMovPagamentoVO().setSaldoContaCorrenteCliente("Saldo Disponível de: " + nf.format(Double.valueOf(getMovContaCorrenteCliente().getSaldoAtual())));
                } else {
                    getMovPagamentoVO().setSaldoContaCorrenteCliente("Saldo Negativo de: " + nf.format(Double.valueOf(getMovContaCorrenteCliente().getSaldoAtual())));
                }
            }
        }

    }

    public String novo() throws Exception {
        try {
            setPixGerado(false);
            setMensagemID("msg_entre_dados");
            setMensagemDetalhada("");
            setMovPagamentoVO(new MovPagamentoVO());
            setPagamentoMovParcelaVO(new PagamentoMovParcelaVO());
            setChequeClone(new ChequeVO());
            setCheque(new ChequeVO());
            setListaSelectItemMovPagamento(new ArrayList());
            setListaValidacaoParcela(new ArrayList());
            setMovContaCorrenteCliente(new MovimentoContaCorrenteClienteVO());
            setValorResiduo(0.0);
            setReceberTroco(null);
            setValorResiduoBaseCalculo(0.0);
            setTotalLancado(0.0);
            setAutorizar(false);
            setMensagemPagamento("");
            setAbrirRichModalContaCorrente(false);
            setAbrirRichModalCheque(false);
            setAbrirRichModalMensagem(false);
            setAbrirRichModalConfirmacaoPagamento(false);
            setApresentarBotoesControle(true);
            setApresentarBotaoImprimirContrato(false);
            setApresentarBotaoImprimirContratoServico(false);
            setCodigoContrato(0);
            setTotalDivida(0.0);
            setSaldoInicial(0.0);
            setProcessandoOperacao(false);
            selecionarEmpresa();
            initiateResponsavelDtPgto();
            //campo de alteração da data base de pagamento sera mostrado na tela somente
            //se o campo de alteracaoDataBaseContrato for verdadeiro
            apresentarAlteracaoDataBasePagtoContrato =
                    getFacade().getConfiguracaoSistema().consultarSePermiteAlteracaoDataBaseContrato();
            inicializarListasSelectItemTodosComboBox(true);
            inicializarMovPagamento();
            inicializarUsuarioLogado();
            limparDataPagto();
            setPermitirCalcularJuros(true);
            setParcelasMultasJuros(new ArrayList<MovParcelaVO>());
            setControlarTransacao(true);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMovContaCorrenteCliente(new MovimentoContaCorrenteClienteVO());
            return "editar";
        }
    }

    private void selecionarEmpresa() throws Exception {
        Integer codigoEmpresa = 0;
        MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        if (movParcelaControle != null) {
            codigoEmpresa = movParcelaControle.getListaParcelasPagar().get(0).getEmpresa().getCodigo();
        }
        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        if (UtilReflection.objetoMaiorQueZero(contratoControle, "getContratoVO().getCodigo()")
                && movParcelaControle == null) {
            codigoEmpresa = contratoControle.getContratoVO().getEmpresa().getCodigo();
        }
        VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
        if (vendaAvulsaControle != null) {
            codigoEmpresa = vendaAvulsaControle.getVendaAvulsaVO().getEmpresa().getCodigo();

        }
        AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
        if (UtilReflection.objetoMaiorQueZero(aulaAvulsaDiariaControle, "getAulaAvulsaDiariaVO().getCodigo()")) {
            codigoEmpresa = aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getEmpresa().getCodigo();
        }
        empresa = getFacade().getEmpresa().consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
    }

    private void initiateResponsavelDtPgto() {
        responsavelDataPagto = new UsuarioVO();
    }

    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            MovPagamentoVO obj = getFacade().getMovPagamento().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            setMovPagamentoVO(obj);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            setMovPagamentoVO(obj);
            setPagamentoMovParcelaVO(new PagamentoMovParcelaVO());
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public void inicializarAtributosRelacionados(MovPagamentoVO obj) {
        if (obj.getPessoa() == null) {
            obj.setPessoa(new PessoaVO());
        }
        if (obj.getFormaPagamento() == null) {
            obj.setFormaPagamento(new FormaPagamentoVO());
        }
    }

    public void limparDataPagto() {
        setDataPagto(Calendario.hoje());
        setDataAuxiliar(Calendario.hoje());
        setAutorizadoDataPagto(false);
        setResponsavelDataPagto(new UsuarioVO());
        atualizarDatas();
        try {
            getResponsavelDataPagto().setCodigo(getUsuarioLogado().getCodigo().intValue());
            getResponsavelDataPagto().setUsername(getUsuarioLogado().getUsername());
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Não foi possível posicionar o Usuário automaticamente. Informe o código manualmente.");
        }
    }

    public void limparTelefoneWhatsPix() {
            setTelefonePix("");
    }

    public void autorizarDataPagto() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                setResponsavelDataPagto(auto.getUsuario());
                getResponsavelDataPagto().setUserOamd(auto.getUsuario().getUserOamd());
                setDataPagto(getDataAuxiliar());
                setListaSelectItemMovPagamento(new ArrayList());
                inicializarMovPagamento();
                inicializarUsuarioLogado();

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade");
            }
        };

        limparMsg();
        try {
            auto.autorizar("Confirmação de Alteração de Data de Pagamento", "DataBase",
                    "Você precisa da permissão \"3.06 - Liberação de Data Base\"",
                    "form", listener);

        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void atualizarDatas() {
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getMovPagamentoEscolhida()) {
                movPagamento.setDataLancamento(Calendario.hoje());
                movPagamento.setDataPagamento(Calendario.hoje());
                movPagamento.setDataQuitacao(Calendario.hoje());
                movPagamento.setDataAlteracaoManual(Calendario.hojeSemRequest());
            }
        }
    }

    public void consultarResponsavelDataPagto() {
        try {
            setResponsavelDataPagto(getFacade().getUsuario().consultarPorChavePrimaria(getResponsavelDataPagto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getResponsavelDataPagto().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public boolean isDataPagtoAlterada() throws Exception {
        return getDataPagto() != null && (!Calendario.igual(getDataPagto(), Calendario.hoje()));
    }

    public String gravar() throws Exception {
        return gravar(null);
    }

    public String gravar(OpcoesPinpadEnum pinPad) throws Exception {
        String retorno = "";
        List<MovParcelaVO> listaParcelas = new ArrayList<>();
        try {
            List<MovParcelaVO> listaMovParcelaAcertoCC = null;
            List<MovParcelaVO> listaMovParcelaDeposito = null;
            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
            TipoOperacaoContaCorrenteEnum tipo = getTipoOperacaoContaCorrenteEnum();
            //atualiza o campo de observação para gravar
            for (Object forma : listaSelectItemMovPagamento) {
                MovPagamentoVO mov = (MovPagamentoVO) forma;
                mov.setValor(mov.getValorTotal());
                mov.setObservacao(getMovPagamentoVO().getObservacao()
                        + "\n" + mov.getObservacao());

                if (mov.getMovPagamentoEscolhida() && mov.getValorTotal() == 0.0) {
                    mov.setMovPagamentoEscolhida(false);
                }
            }
            //se houver valor na conta corrente do cliente
            boolean deveCalcular = movPagamentoVO.getValorReceberOuDevolverContaCorrente() != 0;
            // se data de pagamento foi alterada
            if (isDataPagtoAlterada()) {
                request().setAttribute("dataBase", this.getDataPagto().clone());
                // atualiza os pagamentos para corresponder à data escolhida
                atualizarDatas();
            }
            //se houver valor na conta corrente do cliente
            //para o novo processo de recebimento de débito de conta corrente pelo botão de dinheiro o valor da conta
            //é setado com o valor zero, mesmo tendo recebido o debito da conta
            if (deveCalcular && tipo != TipoOperacaoContaCorrenteEnum.TOCC_Devolver) {
                double valor = movPagamentoVO.getValorReceberOuDevolverContaCorrente();

                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(
                        movPagamentoVO.getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                VendaAvulsaVO vendaAvulsaVO = getFacade().getMovimentoContaCorrenteCliente().gerarProdutoPagamentoDebito(valor, cliente, tipo, getUsuarioLogado());

                listaMovParcelaAcertoCC = new ArrayList<MovParcelaVO>();
                MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(),
                        tipo == TipoOperacaoContaCorrenteEnum.TOCC_Receber ? "EA" : "PG",
                        false, Uteis.NIVELMONTARDADOS_MINIMOS);
                movParcela.setSituacao("EA");
                listaMovParcelaAcertoCC.add(movParcela);

            }

            if (getValorResiduoBaseCalculo() != 0.0 && getReceberTroco() != null && !getReceberTroco()) {
                double deposito = getValorResiduoBaseCalculo() * -1;

                ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(
                        movPagamentoVO.getPessoa().getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO;

                VendaAvulsaVO vendaAvulsaVO = getFacade().getMovimentoContaCorrenteCliente().gerarProdutoPagamentoCredito(deposito, clienteVO, null, getUsuarioLogado(), descLancCC);

                listaMovParcelaDeposito = new ArrayList<MovParcelaVO>();
                MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(),
                        "EA",
                        false, Uteis.NIVELMONTARDADOS_MINIMOS);
                movParcela.setSituacao("EA");
                setValorResiduo(Uteis.arredondarForcando2CasasDecimais(getValorResiduo() + movParcela.getValorParcela()));
                setValorResiduoBaseCalculo(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorResiduoBaseCalculo() + movParcela.getValorParcela()));
                listaMovParcelaDeposito.add(movParcela);

                //LOG - INICIO
                try {
                    vendaAvulsaVO.setObjetoVOAntesAlteracao(new VendaAvulsaVO());
                    vendaAvulsaVO.setNovoObj(true);
                    registrarLogObjetoVO(vendaAvulsaVO, vendaAvulsaVO.getCodigo(), "VENDAAVULSA", vendaAvulsaVO.getCliente().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("VENDAAVULSA", vendaAvulsaVO.getCliente().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE VENDA AVULSA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
                //LOG - FIM
            }

            reciboObj = null;

            if (movParcelaControle != null) {
                if (listaMovParcelaAcertoCC != null) {
                    movParcelaControle.getListaParcelasPagar().addAll(listaMovParcelaAcertoCC);
                }
                if (listaMovParcelaDeposito != null) {
                    movParcelaControle.getListaParcelasPagar().addAll(listaMovParcelaDeposito);
                }

                if (!getOpcaoCobrarMulta()) {
                    for (MovParcelaVO movParcelaVO : movParcelaControle.getListaParcelasPagar()) {
                        movParcelaVO.setValorMultaJuros(null);
                    }
                }
                boolean addParcelasJuros = false;
                if (isPermitirCalcularJuros()) {
                    verificarEGerarParcelasDeJuros(movParcelaControle.getListaParcelasPagar());
                    addParcelasJuros = true;
                }

                List<MovParcelaVO> parcelasParaPagar = movParcelaControle.getListaParcelasPagar();
                List<MovPagamentoVO> pagamentosVO = listaSelectItemMovPagamento;
                if (addParcelasJuros) {
                    parcelasParaPagar.addAll(getParcelasMultasJuros());
                }

                listaParcelas = parcelasParaPagar;

                for (Object obj : listaParcelas) {
                    MovParcelaVO mPag = (MovParcelaVO) obj;
                    getFacade().getPix().excluirPorParcela(mPag.getCodigo(), getUsuarioLogado());
                }

                if (!UteisValidacao.emptyList(pagamentosVO)) {
                    for (MovPagamentoVO movPagamento: pagamentosVO) {
                        if (movPagamento.getMovPagamentoEscolhida() != null && movPagamento.getMovPagamentoEscolhida() &&
                            !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad()) && movPagamento.getRespostaRequisicaoPinpad().contains("administrativeCode")) {
                            gravarLogCappta("verificarEGerarParcelasDeJuros", "");
                        }
                    }
                }

                try {
                    registrarLogCapptaServidorGravar(pinPad, parcelasParaPagar, pagamentosVO);
                    validarIntegridadeListaMovParcelasCappta(pinPad, parcelasParaPagar);
                } catch (Exception e){}

                reciboObj = getFacade().getMovPagamento().incluirListaPagamento(
                        pagamentosVO,
                        parcelasParaPagar,
                        (MovimentoContaCorrenteClienteVO) getMovContaCorrenteCliente().getClone(true),
                        movParcelaControle.getMovParcelaVO().getContrato(),
                        getReceberTroco(), getValorResiduoBaseCalculo(), controlarTransacao, null, getTipoOperacaoContaCorrenteEnum());

            } else if (parcelaControle != null) {
                if (listaMovParcelaAcertoCC != null) {
                    parcelaControle.getListaParcelasPagar().addAll(listaMovParcelaAcertoCC);
                }
                if (listaMovParcelaDeposito != null) {
                    parcelaControle.getListaParcelasPagar().addAll(listaMovParcelaDeposito);
                }
                List<MovParcelaVO> parcelasParaPagar = parcelaControle.getListaParcelasPagar();

                listaParcelas = parcelasParaPagar;

                for (Object obj : listaParcelas) {
                    MovParcelaVO mPag = (MovParcelaVO) obj;
                    getFacade().getPix().excluirPorParcela(mPag.getCodigo(), getUsuarioLogado());
                }

                reciboObj = getFacade().getMovPagamento().incluirListaPagamento(listaSelectItemMovPagamento,
                        parcelasParaPagar, (MovimentoContaCorrenteClienteVO) getMovContaCorrenteCliente().getClone(true),
                        parcelaControle.getMovParcelaVO().getContrato(),
                        getReceberTroco(), getValorResiduoBaseCalculo(), controlarTransacao, null);
            } else if (vendaAvulsaControle != null) {
                List lista = new ArrayList();
                MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(
                        vendaAvulsaControle.getVendaAvulsaVO().getCodigo(),
                        "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                lista.add(movParcela);
                if (listaMovParcelaAcertoCC != null) {
                    lista.addAll(listaMovParcelaAcertoCC);
                }
                if (listaMovParcelaDeposito != null) {
                    lista.addAll(listaMovParcelaDeposito);
                }

                listaParcelas = lista;

                for (Object obj : listaParcelas) {
                    MovParcelaVO mPag = (MovParcelaVO) obj;
                    getFacade().getPix().excluirPorParcela(mPag.getCodigo(), getUsuarioLogado());
                }

                //nesse fluxo não há contrato.
                reciboObj = getFacade().getMovPagamento().incluirListaPagamento(
                        listaSelectItemMovPagamento, lista,
                        (MovimentoContaCorrenteClienteVO) getMovContaCorrenteCliente().getClone(true), null, getReceberTroco(),
                        getValorResiduoBaseCalculo(), controlarTransacao, null);
            } else if (aulaAvulsaDiariaControle != null) {
                List lista = new ArrayList();
                MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoAulaAvulsaDiaria(
                        aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCodigo().intValue(),
                        false, Uteis.NIVELMONTARDADOS_MINIMOS);
                lista.add(movParcela);
                if (listaMovParcelaAcertoCC != null) {
                    lista.addAll(listaMovParcelaAcertoCC);
                }
                if (listaMovParcelaDeposito != null) {
                    lista.addAll(listaMovParcelaDeposito);
                }

                listaParcelas = lista;

                for (Object obj : listaParcelas) {
                    MovParcelaVO mPag = (MovParcelaVO) obj;
                    getFacade().getPix().excluirPorParcela(mPag.getCodigo(), getUsuarioLogado());
                }
                //nesse fluxo não há contrato.
                reciboObj = getFacade().getMovPagamento().incluirListaPagamento(
                        listaSelectItemMovPagamento, lista,
                        (MovimentoContaCorrenteClienteVO) getMovContaCorrenteCliente().getClone(true), null, getReceberTroco(),
                        getValorResiduoBaseCalculo(), controlarTransacao, null, getTipoOperacaoContaCorrenteEnum());
            }
            if (!UteisValidacao.emptyString(getEmpresa().getNomeUsuarioAmigoFit()) && !UteisValidacao.emptyString(getEmpresa().getSenhaUsuarioAmigoFit())) {
                try {
                    cadastrarPagamentoAmigoFit(listaParcelas);
                } catch (Exception e) {
                    Uteis.logar(e, MovPagamentoControle.class);
                }
            }
            try {
                request().removeAttribute("dataBase");
                LogVO logVO = new LogVO();
                reciboObj.montarLogReciboPagamento(logVO);
                logVO.setOperacao("INCLUSÃO DE RECIBO");
                registrarLogObjetoVO(logVO, reciboObj.getPessoaPagador().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CONTRATO", reciboObj.getPessoaPagador().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO RECIBO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                Uteis.logar(e, ContratoControle.class);
            }

            try {
                if (getEmpresa().isNotificarWebhook()) {
                    ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(reciboObj.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    getFacade().getZWFacade().notificarPagamento(clienteVO, reciboObj);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            try {
                if (pinPad != null &&
                        !UteisValidacao.emptyNumber(this.getPinPadPedidoVO().getCodigo())) {
                    this.getPinPadPedidoVO().setReciboPagamentoVO(this.reciboObj);
                    if (!UteisValidacao.emptyList(this.reciboObj.getPagamentosDesteRecibo())) {
                        this.getPinPadPedidoVO().setMovPagamentoVO(this.reciboObj.getPagamentosDesteRecibo().get(0));
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (controlarTransacao) { // controle
               retorno = realizarAcoesPagamentoConfirmado();

            }
        } catch (Exception e) {
            if (pinPad != null) {
                gravarLogCappta(pinPad.name() + "|gravar - ERRO", e.getMessage());
                if (!UteisValidacao.emptyString(pinPad.getNome()) && pinPad.getNome().equals("Cappta")) {
                    notificarAPIChatGoogle(e.getMessage(), getMovPagamentoVO().getResponsavelPagamento());
                }
            }
            setApresentarBotoesControle(true);
            setSucesso(false);
            getMovPagamentoVO().getResponsavelPagamento().setSenha("");
            throw e;
        } finally {
            try {
                //atualizar tentativa das parcelas
                getFacade().getMovParcelaResultadoCobranca().processarParcelas(listaParcelas);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return retorno;
    }

    private void validarIntegridadeListaMovParcelasCappta(OpcoesPinpadEnum pinPad, List<MovParcelaVO> parcelasParaPagar) throws Exception {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela
        //Pelos Logs, desconfiamos que algo está fazendo o MovPagamentoControle perder o Contrato/VendaAvulsa/AulaAvulsa/Personal da MovParcela
        //Por isso adiciomos esse método para verificar a integridade da MovParcela
        if (pinPad != null && !UteisValidacao.emptyNumber(pinPad.getCodigo()) && pinPad.getCodigo().equals(OpcoesPinpadEnum.CAPPTA.getCodigo()) && !UteisValidacao.emptyList(parcelasParaPagar)) {
            for (MovParcelaVO movParcelaVO: parcelasParaPagar) {
                if (
                        !UteisValidacao.emptyNumber(movParcelaVO.getContrato().getCodigo()) ||
                        !UteisValidacao.emptyNumber(movParcelaVO.getVendaAvulsaVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(movParcelaVO.getAulaAvulsaDiariaVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(movParcelaVO.getPersonal().getCodigo())
                ) {
                    continue;
                }

                String[] colunas = {"contrato", "vendaavulsa", "aulaavulsadiaria", "personal"};
                for (String coluna : colunas) {
                    Integer codigoEntidadeZW = getFacade().getMovParcela().encontrarEntidadeZWPorMovParcelaParaCappta(coluna, movParcelaVO.getCodigo());
                    if (!UteisValidacao.emptyNumber(codigoEntidadeZW)) {
                        switch (coluna) {
                            case "contrato":
                                Uteis.logarDebug(" CAPPTA - MovPagamentoControle.validarIntegridadeListaMovParcelasCappta - Código do Contrato: " + codigoEntidadeZW);
                                movParcelaVO.getContrato().setCodigo(codigoEntidadeZW);
                                break;
                            case "vendaavulsa":
                                Uteis.logarDebug(" CAPPTA - MovPagamentoControle.validarIntegridadeListaMovParcelasCappta - Código da Venda Avulsa: " + codigoEntidadeZW);
                                movParcelaVO.getVendaAvulsaVO().setCodigo(codigoEntidadeZW);
                                break;
                            case "aulaavulsadiaria":
                                Uteis.logarDebug(" CAPPTA - MovPagamentoControle.validarIntegridadeListaMovParcelasCappta - Código da Diária: " + codigoEntidadeZW);
                                movParcelaVO.getAulaAvulsaDiariaVO().setCodigo(codigoEntidadeZW);
                                break;
                            case "personal":
                                Uteis.logarDebug(" CAPPTA - MovPagamentoControle.validarIntegridadeListaMovParcelasCappta - Código do Personal: " + codigoEntidadeZW);
                                movParcelaVO.getPersonal().setCodigo(codigoEntidadeZW);
                                break;
                        }
                        break;
                    }
                }
            }
        }
    }

    private static void registrarLogCapptaServidorGravar(OpcoesPinpadEnum pinPad, List<MovParcelaVO> parcelasParaPagar, List<MovPagamentoVO> pagamentosVO) {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela, por isso estamos adicionando Logs para tentar encontrar o problema.
        //Pelo tipo de erro no Log do Servidor, imagino que o MovPagamentoVO.contratoVO, está perndendo o código ou ficando null.
        //Quando encontrar o problema, pode retirar esse if
        if (!UteisValidacao.emptyList(pagamentosVO)) {
            for (MovPagamentoVO movPagamento: pagamentosVO) {
                if (
                        movPagamento.getMovPagamentoEscolhida() != null && movPagamento.getMovPagamentoEscolhida() &&
                        !UteisValidacao.emptyString(movPagamento.getRespostaRequisicaoPinpad()) && movPagamento.getRespostaRequisicaoPinpad().contains("administrativeCode")
                ) {
                    Uteis.logarDebug(pinPad.getNome() + " - MovPagamentoControle.gravar - Número do Contrato: " + parcelasParaPagar.get(0).getContrato().getCodigo());
                    Uteis.logarDebug(pinPad.getNome() + " - MovPagamentoControle.gravar - Resposta Requisicao Pinpad: " + movPagamento.getRespostaRequisicaoPinpad());
                }
            }
        }
    }

    private void notificarAPIChatGoogle(String msgErro, UsuarioVO usuarioTentativa) throws Exception {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlApiChatGoogleCappta);

            StringBuilder sb = new StringBuilder();
            sb.append("*Falha na baixa da Parcela pela Cappta* \n");
            sb.append("*Empresa: " + getEmpresa().getNome() + "*\n");
            sb.append("*Chave ZW:* " + getKey() + "\n");
            sb.append("*Horário:* " + Uteis.getDataComHora(Calendario.hoje()) + "\n");
            sb.append("*Usuário Login:* " + usuarioTentativa.getUsername() + "\n");
            sb.append("*Usuário Código:* " + usuarioTentativa.getCodigo() + "\n");
            sb.append("*Motivo:* " + msgErro + ")* \n");

            JSONObject envio = new JSONObject();
            envio.put("text", sb.toString());

            RequestHttpService service = new RequestHttpService();
            Uteis.logarDebug("Vou enviar a notificação de erro Cappta para a API do chat google.");
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, null, null, envio.toString(), MetodoHttpEnum.POST);

            if (respostaHttpDTO.getHttpStatus() == 200) {
                Uteis.logarDebug("Notificação para a API do chat google enviada com SUCESSO");
            } else {
                Uteis.logarDebug("ERRO ao notificar a API do google. | Resposta API Google: " + respostaHttpDTO.getResponse());
            }
        } catch (Exception e) {
        }
    }

    private boolean podeEmitirNFSeAutomatico(ReciboPagamentoVO reciboObj, EmpresaVO empresaVO) {
        return reciboObj.getEmitirNFSeAutomatico()
                && reciboObj.getEmpresa().getUsarNFSe()
                && empresaVO.getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()
                && !empresaVO.isEmitirNotaSomenteRecorrencia()
                && (empresaVO.isNotasAutoPgRetroativo() || !Uteis.getDataComUltimaHora(dataPagto).before(Uteis.getDataComUltimaHora(Calendario.hoje())));
    }

    public Date obterDataMultaJuros() {
        Date dataConsiderarMultaJuros = Calendario.hoje();
        if (getDataPagto() != null) {
            dataConsiderarMultaJuros = getDataPagto();
        }
        return dataConsiderarMultaJuros;
    }

    public void confirmarPermissaoUsuario() {
        setErro(false);
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception{
                setProcessandoOperacao(true);
                Boolean existeSomentePagamentoAvista = true;
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) i.next();
                    if (obj.getMovPagamentoEscolhida() && !obj.getTipoPagador().equals("CI")) {
                        existeSomentePagamentoAvista = false;
                    }
                }
                if (getAbrirRichModalConfirmacaoPagamento() && existeSomentePagamentoAvista && getValorResiduoBaseCalculo() < 0.0) {
                    setExecutarAoCompletar("Richfaces.showModalPanel('panelConfirmacaoPagamentoDepositoNaConta'), setFocus(formConfirmacaoPagamentoDepositoNaConta,'formConfirmacaoPagamentoDepositoNaConta:depositarContar')");
                } else if (getAbrirRichModalConfirmacaoPagamento() /*&& (!pagamentoDigitalMarcado)*/) {
                    LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                    String path = loginControle.getModuloAberto().getDescricao();

                    if (path.equals("centralEventos") || path.equals("Central de Eventos")) {
                        setPaginaDestino(pagamentoParcelaCE(false));

                    } else {
                        if(auto == null){
                            movPagamentoVO.setResponsavelPagamento(getUsuarioLogado());
                        } else {
                            movPagamentoVO.setResponsavelPagamento(auto.getUsuario());
                        }
                        verificarUsuarioSenhaResponsavelPagamento(false, null);
                    }
                }
                setProcessandoOperacao(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

        };

        limparMsg();
        validarDadosPagamento();
        if (!getErro()) {
            auto.autorizar("Confirmação de Liberação de Pagamento", "MovPagamento",
                    "Você precisa da permissão \"4.07 - Movimento de Pagamento\"",
                    "geralnovopagamento, mensagemSup,panelSaldoContaCorrente," +
                            "dataPagto, panelCobrancaMultaJuros, form:panelBotoesControle,form:panelEsconderControles," +
                            "form:panelEsconderControles,form:panelEsconderControles2,form:panelEsconderControles3," +
                            "form:tituloFormasPagamento,residuo", listener);
        } else {
            montarErro(getMensagemDetalhada());
        }
    }

    public void validarDadosPagamento() {
        validarDadosPagamento(null, false);
    }

    public void validarDadosPagamento(OpcoesPinpadEnum opcoesPinpadEnum, boolean isInicioFluxoPinpad) {
        try {
            limparMsg();
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(2);
            calcularPagamentoInput();
            if (getValorResiduoBaseCalculo() > 0.00) {
                throw new Exception("Não foi quitado o valor total do pagamento. "
                        + "Ainda falta ser pago " + getEmpresaLogado().getMoeda() + ": " + (nf.format(Double.valueOf(getValorResiduoBaseCalculo()))));
            }

            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
            if (movParcelaControle == null && vendaAvulsaControle == null && parcelaControle == null && aulaAvulsaDiariaControle == null) {
                throw new Exception("Lista de Parcelas não foi gerada.");
            }

            for (Object obj : getListaValidacaoParcela()) {
                MovParcelaVO mp = (MovParcelaVO) obj;
                validarParcelaRemessa(mp);
            }

            List<String> autorizacaoMesmoRecebimento = new ArrayList<>();
            List<String> nsuMesmoRecebimento = new ArrayList<>();
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj = (MovPagamentoVO) i.next();
                if (obj.getMovPagamentoEscolhida()) {
                    MovPagamentoVO.validarDadosEspecial(obj, getValorResiduoBaseCalculo(), opcoesPinpadEnum);
                    if (obj.getUsarPagamentoDigital()) {
                        this.setApresentarPagamentoDigital(true);
                    }

                    if ((obj.getOpcaoPagamentoCartaoCredito() || obj.getOpcaoPagamentoCartaoDebito()) && obj.getValorTotal() <= 0 && obj.getFormaPagamento().getPinpad().isCAPPTA()) {
                        throw new Exception("Sem valor para cobrança.");
                    }

                    if (opcoesPinpadEnum != null) {
                        if (getValorResiduoBaseCalculo() < 0.00) {
                            throw new Exception("O valor pago não pode ser superior ao valor das parcelas.");
                        }
                    }

                    if ((obj.getOpcaoPagamentoCartaoCredito() || obj.getOpcaoPagamentoCartaoDebito()) && obj.getFormaPagamento().isReceberSomenteViaPinPad() &&
                            opcoesPinpadEnum == null && !obj.getFormaPagamento().getPinpad().isCAPPTA()) {
                        throw new Exception("O recebimento só pode ser realizado via Pinpad.");
                    }

                    if ((obj.getOpcaoPagamentoCartaoCredito() || obj.getOpcaoPagamentoCartaoDebito()) && obj.getFormaPagamento().isReceberSomenteViaPinPad() &&
                            opcoesPinpadEnum == null && obj.getFormaPagamento().getPinpad().isCAPPTA() && UteisValidacao.emptyString(obj.getRespostaRequisicaoPinpad()) &&
                            UteisValidacao.emptyString(obj.getAutorizacaoCartao())) {
                        throw new Exception("A forma de pagamento " + obj.getFormaPagamento().getDescricao() + " deve ser recebida somente via pinpad.");
                    }

                    if (!isInicioFluxoPinpad && obj.getFormaPagamento().isExigeCodAutorizacao() && !clicouBotaoPagOnline) {
                        if (obj.getAutorizacaoCartao().isEmpty() && !obj.isPermitePeloCodigoIntegracaoHabilitado(obj.getNrParcelaCartaoCredito())) {
                            throw new Exception("O código da autorização deve ser informado.");
                        }
                    }

                    if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")
                            && getMovContaCorrenteCliente().getResponsavelAutorizacao().getCodigo() == 0) {
                        throw new Exception("O responsável pela (AUTORIZAÇÃO do  uso da CONTA CORRENTE) deve ser informado.");
                    }
                    if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        getFacade().getCheque().validarChequeExistente(obj.getChequeVOs());
                    }

                    //Adicionado essa validação porquê tem cliente que lança dois Cartões Crédito ou Cartão Débito com a mesma Autorização ou mesmo NSU e depois abri suporte reclamando da Conciliação.
                    if(!UteisValidacao.emptyString(obj.getAutorizacaoCartao()) && !autorizacaoMesmoRecebimento.contains(obj.getAutorizacaoCartao())){
                        autorizacaoMesmoRecebimento.add(obj.getAutorizacaoCartao());
                    } else if (!UteisValidacao.emptyString(obj.getAutorizacaoCartao())) {
                        throw new Exception("Os códigos de autorização precisam ser diferentes para um mesmo pagamento.");
                    }
                    if(!UteisValidacao.emptyString(obj.getNsu()) && !nsuMesmoRecebimento.contains(obj.getNsu())){
                        nsuMesmoRecebimento.add(obj.getNsu());
                    } else if (!UteisValidacao.emptyString(obj.getNsu())) {
                        throw new Exception("Os NSU precisam ser diferentes para um mesmo pagamento.");
                    }

                }
                validarParceiroFidelidade(obj);
                validarConfirmacaoPix(obj);
            }
            if (this.apresentarPagamentoDigital) {
                setApresentarPagamentoDigital(true);
            } else {
                this.setApresentarPagamentoDigital(false);
            }
            setAbrirRichModalConfirmacaoPagamento(true);
            setSucesso(false);
            setErro(false);
            setMensagemID("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            if (opcoesPinpadEnum != null) {
                gravarLogCappta("validarDadosPagamento - ERRO | " + opcoesPinpadEnum.getNome().toUpperCase(), e.getMessage());
            }
            setMensagemPagamento("");
            setAbrirRichModalConfirmacaoPagamento(false);
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarErro(e);
        }
    }

    private void validarConfirmacaoPix(MovPagamentoVO movPagamentoVO) throws Exception {
        if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla())
                && movPagamentoVO.getMovPagamentoEscolhida() && !isExisteConvenioPix()) {
            throw new Exception("Não foi encontrado nenhum convênio do tipo PIX para prosseguir com o pagamento. Você precisa cadastrar um convênio de cobrança PIX");
        }
        if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla())
                && movPagamentoVO.getMovPagamentoEscolhida()) {
            if (!pixVO.getStatus().equals("CONCLUIDA")) {
                throw new Exception("O pagamento do pix ainda não foi confirmado. Aguarde alguns segundos e tente novamente.");
            }
        }
    }

    private void validarParceiroFidelidade(MovPagamentoVO obj) throws Exception {
        if (obj.getMovPagamentoEscolhida() && (obj.isOpcaoPagamentoParceiroFidelidade() || (obj.getFormaPagamento().isGerarPontos() && obj.isUsarParceiroFidelidade()))) {

            if (obj.getFormaPagamento().isGerarPontos()) {
                if (UteisValidacao.emptyNumber(obj.getTabelaParceiroFidelidadeVO().getCodigo())) {
                    throw new Exception("Selecione uma tabela para acumular pontos na forma de pagamento " + obj.getFormaPagamento().getDescricao());
                }
            }
            if (!SuperVO.verificaCPF(obj.getCpfParceiroFidelidade())) {
                throw new Exception("Informe um CPF válido do parceiro fidelidade " + obj.getFormaPagamento().getTipoParceiro().getNome() + ". Forma de pagamento " + obj.getFormaPagamento().getDescricao());
            }
            if (obj.isOpcaoPagamentoParceiroFidelidade()) {

                if (UteisValidacao.emptyString(obj.getCodigoExternoProdutoParceiroFidelidade())) {
                    throw new Exception("Selecione uma tabela para resgatar.");
                }

                if (UteisValidacao.emptyString(obj.getSenhaParceiroFidelidade())) {
                    throw new Exception("Informe a senha do " + obj.getFormaPagamento().getTipoParceiro().getNome() + ".");
                }
            }
        }
    }

    public void limparListaParcelaPaga(MovParcelaControle movParcelaControle, VendaAvulsaControle vendaAvulsaControle, AulaAvulsaDiariaControle aulaAvulsaDiariaControle) throws Exception {
        if (movParcelaControle != null) {
            movParcelaControle.setMovParcelaVO(new MovParcelaVO());
            //movParcelaControle.setMovProdutoParcelaVO(new MovProdutoParcelaVO());
            movParcelaControle.setItensCaixaAberto(new ArrayList<CaixaAbertoTO>());
            movParcelaControle.setListaParcelasPagar(new ArrayList());
            movParcelaControle.setListaResponsavelPagamento(new ArrayList());
            //movParcelaControle.setListaSelectItemConvenioCobranca(new ArrayList());
            movParcelaControle.setValorConsulta("");
            //movParcelaControle.setApresentarParcelaPaga(false);
            movParcelaControle.setNumeroContratoResponsavel(new Integer(0));
        }
        if (vendaAvulsaControle != null) {
            vendaAvulsaControle.setVendaAvulsaVO(new VendaAvulsaVO());
            vendaAvulsaControle.setItemVendaAvulsaVO(new ItemVendaAvulsaVO());
        }
        if (aulaAvulsaDiariaControle != null) {
            aulaAvulsaDiariaControle.setAulaAvulsaDiariaVO(new AulaAvulsaDiariaVO());
        }
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO obj = (MovPagamentoVO) i.next();
            obj.setMovPagamentoEscolhida(false);
            obj.setValorTotal(new Double(0));
        }
        setValorResiduo(0.0);
        setValorResiduoBaseCalculo(0.0);
        setTotalLancado(0.0);
    }

    private void restaurarDivida() {
        setValorResiduo(getTotalDivida());
        setValorResiduoBaseCalculo(getTotalDivida());
        setTotalLancado(0.0);
        try {
            calcularPagamentoInput();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void limparFormasEscolhidas() {
        List<MovPagamentoVO> formas = getListaSelectItemMovPagamento();
        for (MovPagamentoVO movPagamentoVO : formas) {
            movPagamentoVO.setPagamentoAberto(false);
            movPagamentoVO.setMovPagamentoEscolhida(false);
            movPagamentoVO.setValorTotal(0.0);
            movPagamentoVO.setOperadoraCartaoVO(null);
            movPagamentoVO.setNrParcelaCartaoCredito(0);
            movPagamentoVO.setCartaoCreditoVOs(null);
            movPagamentoVO.setChequeVOs(null);
        }
    }


    public void checkAberto() {
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        boolean pagamentoAberto = obj.isPagamentoAberto();
        obj.setPagamentoAberto(!pagamentoAberto);
        if (!obj.getMovPagamentoEscolhida() && !pagamentoAberto) {
            obj.setMovPagamentoEscolhida(true);
            calcularPagamentoSeletcCheckbox();
        }
    }

    private List<MovParcelaVO> obterParcelas() throws Exception {
        List<MovParcelaVO> movParcelas = new ArrayList<>();

        MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");

        if (movParcelaControle != null && movParcelaControle.getListaParcelasPagar() != null && movParcelaControle.getListaParcelasPagar().size() >= 1) {
            // Fluxo de navegação a partir da tela "Caixa em Aberto"
            movParcelas = movParcelaControle.getListaParcelasPagar();
        }
        if((movParcelas == null || movParcelas.size() == 0) && vendaAvulsaControle != null && vendaAvulsaControle.getVendaAvulsaVO() != null && vendaAvulsaControle.getVendaAvulsaVO().getMovParcelaVOs().size() >=1){
            // Fluxo de navegação a partir da tela "Venda avulsta" na opção receber
            movParcelas = vendaAvulsaControle.getVendaAvulsaVO().getMovParcelaVOs();
        }
        if((movParcelas == null || movParcelas.size() == 0 ) && contratoControle != null && contratoControle.getContratoVO() != null && contratoControle.getContratoVO().getMovParcelaVOs().size() >=1){
            // Fluxo de navegação a partir da tela "Negociação" na opção receber
            movParcelas = contratoControle.getContratoVO().getMovParcelaVOs();
        }
        if((movParcelas == null || movParcelas.size() == 0 ) && aulaAvulsaDiariaControle != null && aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO() != null){
            // Fluxo de navegação a partir da tela "Venda Avulsa" na opção receber
            MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoAulaAvulsaDiaria(
                    aulaAvulsaDiariaControle.getAulaAvulsaDiariaVO().getCodigo().intValue(),
                    false, Uteis.NIVELMONTARDADOS_MINIMOS);
            movParcelas.add(movParcela);
        }

        if (movParcelas == null) {
            throw new ValidacaoException("Não foi possível definir as parcelas relacionadas ao pagamento. Por favor, entre em contato com nosso suporte.");
        }

        return movParcelas;
    }

    public void calcularPagamentoSeletcCheckbox() {
        try {
            setApresentarCPFPix(false);
            setCpfPix("");
            calcularTotalPontosParceiroFidelidade();
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            if (obj.getFormaPagamento().isCartaoDebitoOnline()) {
                montarListaSelectItemOperadoraCartaoDebitoOnline(obj);
            }

            if (obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla()) || obj.getFormaPagamento().isGerarPontos()) {
                obj.setListaSelectItemTabelaParceiroFidelidade(new ArrayList<SelectItem>());
                obj.setSelectItemsProdutosParceiroFidelidade(new ArrayList<SelectItem>());
                obj.setPontosParceiroFidelidade(0);
                obj.setProdutosParceiroFidelidade(new ArrayList<ProdutoParceiroFidelidadeVO>());
                if (!UteisValidacao.emptyString(obj.getPessoa().getCfp())) {
                    String cpfpagador = Uteis.aplicarMascara(Uteis.removerMascara(obj.getPessoa().getCfp()), "999.999.999-99");
                    obj.setCpfParceiroFidelidade(cpfpagador);
                }
                if (obj.getFormaPagamento().isGerarPontos()) {
                    obj.setUsarParceiroFidelidade(obj.getPessoa().isUtilizaDotz());
                    montarListaSelectItemTabelaParceiroFidelidade(obj);
                    atualizarPontosParceiroFidelidade(obj);
                }
            }

            obj.setPagamentoAberto(obj.getMovPagamentoEscolhida());

            if ((obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()) ||
                    obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) && obj.getMovPagamentoEscolhida() &&
                    !obj.getFormaPagamento().getPinpad().getPinpad().equals(OpcoesPinpadEnum.CAPPTA.getCodigo())) {

                try {
                    //só tem uma adquirente selecionar automático ... (Primeiro item é vazio)
                    if (!UteisValidacao.emptyList(getListaSelectItemAdquirente()) && getListaSelectItemAdquirente().size() == 2) {
                        obj.getAdquirenteVO().setCodigo((Integer) getListaSelectItemAdquirente().get(1).getValue());
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                boolean credito = obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla());
                obj.setOperadoraCartaoVO(getFacade().getOperadoraCartao().obterOperadoraCartaoPadrao(credito));
                if (credito && !UteisValidacao.emptyNumber(obj.getOperadoraCartaoVO().getCodigo())) {
                    montarListaNrParcelasCartao(obj.getOperadoraCartaoVO());
                    if (obj.getOperadoraCartaoVO().getQtdeMaxParcelas() == 1) {
                        obj.setNrParcelaCartaoCredito(1);
                        if (isPermitePeloCodigoIntegracaoHabilitado(obj, obj.getNrParcelaCartaoCredito())) {
                            //setar como null pois tem transação online.
                            obj.setNrParcelaCartaoCredito(null);
                        }
                    }
                }
            }

            //validar se tem pinpad stone connect configurado
            if (obj.getMovPagamentoEscolhida() &&
                    (obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()) ||
                            obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()))) {
                obj.getFormaPagamento().setStoneConnect(getFacade().getPinPad().existePinpad(OpcoesPinpadEnum.STONE_CONNECT, obj.getFormaPagamento().getCodigo(), obj.getEmpresa().getCodigo()));
                obj.getFormaPagamento().setGetCard(getFacade().getPinPad().existePinpad(OpcoesPinpadEnum.GETCARD, obj.getFormaPagamento().getCodigo(), obj.getEmpresa().getCodigo()));
            }

            setMsgAlert("");
            if (obj.getFormaPagamento().getPinpad().getPinpad().equals(OpcoesPinpadEnum.CAPPTA.getCodigo()) && obj.getMovPagamentoEscolhida()) {
                pinpad = new PinpadTO();
                pinpad.setPinpadCappta(true);
                OperadoraCartaoVO ope = new OperadoraCartaoVO();
                ope.setQtdeMaxParcelas(obj.getFormaPagamento().getNrMaxParcelasPinpad());
                montarListaNrParcelasCartao(ope);
            }

            //validar se pode apresentar opção de pagamento online
            if (obj.getMovPagamentoEscolhida() &&
                    obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {

                Set<Integer> listaEmp = new HashSet<>();

                if (getEmpresaLogado() != null && !UteisValidacao.emptyNumber(getEmpresaLogado().getCodigo())) {
                    listaEmp.add(getEmpresaLogado().getCodigo());
                }

                List<MovParcelaVO> listaParcelas = this.getListaValidacaoParcela();
                for (MovParcelaVO movParcelaVO : listaParcelas) {
                    listaEmp.add(movParcelaVO.getEmpresa().getCodigo());
                }

                if (!UteisValidacao.emptyList(listaEmp)) {
                    obj.setApresentarPagamentoOnline(getFacade().getConvenioCobranca().existeConvenioOnline(listaEmp, SituacaoConvenioCobranca.ATIVO));
                    setClicouBotaoPagOnline(false);
                } else {
                    obj.setApresentarPagamentoOnline(false);
                }
            }

            if (validarValorResiduoBaseCalculo(obj)) {
                return;
            }
            validarMovPagamento(obj);

            //validar pagamento com pix
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PIX.getSigla()) && obj.getMovPagamentoEscolhida()) {
                setPixGerado(false);
                montarConvenioCobrancaPix(obj.getEmpresa(), obj.getFormaPagamento());

                //validar tipo pagador consumidor
                gerarPessoaConsumidor(obj);

                if (conveniosPix == null || conveniosPix.size() == 0) {
                    conveniosPix = getFacade().getConvenioCobranca().consultarPorTipoPixComEmpresa(obj.getEmpresa());
                }
                if (conveniosPix.size() == 1) {
                    //Possui somente 1 convênio Pix na mesma empresa
                    convenioPixSelecionado = conveniosPix.get(0);
                    convenioPixSelecionado.setEmpresa(obj.getEmpresa());
                    setConvenioPix(convenioPixSelecionado.getCodigo());
                    if (convenioPixSelecionado.isPixPjBank()) {
                        convenioPixSelecionado.setPixClientId(convenioPixSelecionado.getCredencialPJBank());
                        convenioPixSelecionado.setPixAppKey(convenioPixSelecionado.getChavePJBank());
                    }
                    gerarObterPix(obj);
                }
            }

            //WM.: reinicializa totais, ajuda evitar lixo nos valores
            if (this.deveRestaurarValorTotalDivida()) {
                restaurarDivida();
            }
            setMensagemDetalhada("");
            setMensagem("");
            setMensagemID("");
            setErro(false);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
    }

    private void gerarPessoaConsumidor(MovPagamentoVO obj) {
        try {
            //tipo pagador consumidor CN
            if (!obj.getMovPagamentoEscolhida() ||
                    obj.getTipoPagador() == null ||
                    !obj.getTipoPagador().equalsIgnoreCase("CN") ||
                    !UteisValidacao.emptyNumber(obj.getPessoa().getCodigo())) {
                return;
            }

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(obj.getNomePagador());
            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            getFacade().getPessoa().incluirPessoaSimplificado(pessoaVO);
            obj.setPessoa(pessoaVO);

            //preencher a pessoa na venda avulsa
            Iterator i = getListaValidacaoParcela().iterator();
            while (i.hasNext()) {
                MovParcelaVO mp = (MovParcelaVO) i.next();

                if (!UteisValidacao.emptyNumber(mp.getVendaAvulsaVO().getCodigo())) {
                    VendaAvulsaVO vendaAvulsaVO = getFacade().getVendaAvulsa().consultarPorChavePrimaria(mp.getVendaAvulsaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (vendaAvulsaVO.getTipoComprador().equalsIgnoreCase("CN") &&
                            UteisValidacao.emptyNumber(vendaAvulsaVO.getPessoaVO().getCodigo())) {
                        getFacade().getVendaAvulsa().alterarPessoa(pessoaVO.getCodigo(), vendaAvulsaVO.getCodigo(), "Gerar pessoa consumidor", getUsuarioLogado(), true);
                        mp.getVendaAvulsaVO().setPessoaVO(pessoaVO);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void alterarCPFPessoa() {
        try {
            limparMsg();
            if (UteisValidacao.emptyString(this.getCpfPix().trim())) {
                throw new Exception("Informe um CPF");
            }
            if (!UteisValidacao.isValidCPF(this.getCpfPix())) {
                throw new Exception("Informe um CPF válido");
            }

            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");

            PessoaVO pessoaVO = getFacade().getPessoa().consultarPorCPF(this.getCpfPix(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo()) && (!obj.getPessoa().getCodigo().equals(pessoaVO.getCodigo()))) {
                throw new Exception("Este CPF já está cadastrado para o(a) aluno(a): " + pessoaVO.getNome() + " | Cód pessoa: " + pessoaVO.getCodigo());
            }

            if (obj.getMovPagamentoEscolhida() &&
                    !UteisValidacao.emptyNumber(obj.getPessoa().getCodigo()) &&
                    UteisValidacao.emptyString(obj.getPessoa().getCfp())) {
                getFacade().getPessoa().alterarCPF(obj.getPessoa().getCodigo(), this.getCpfPix(), "Alterar CPF gerar Pix", getUsuarioLogado(), true);
                obj.getPessoa().setCfp(this.getCpfPix());
                gerarObterPix(obj);
                setApresentarCPFPix(false);
            } else if (!UteisValidacao.emptyString(obj.getPessoa().getCfp())) {
                gerarObterPix(obj);
                setApresentarCPFPix(false);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private void gerarObterPix(MovPagamentoVO obj) throws Exception {
        setPixGerado(false);
        pixVO = null;

        String email = obj.getPessoa().getEmail();
        if ((email == null || email.isEmpty()) && obj.getPessoa().getEmailVOs().size() == 0) {
            obj.getPessoa().setEmailVOs(
                    getFacade().getEmail().consultarEmails(obj.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)
            );
        }
        if (email == null || email.isEmpty()
                && obj.getPessoa().getEmailVOs() != null && obj.getPessoa().getEmailVOs().size() > 0) {
            email = obj.getPessoa().getEmailVOs().get(0).getEmail();
        }
        this.emailPix = email;

        String telefone = obj.getPessoa().getTelefonesCelular();
        if ((telefone == null || telefone.isEmpty()) && obj.getPessoa().getTelefoneVOs().size() == 0) {
            obj.getPessoa().setTelefoneVOs(
                    getFacade().getTelefone().consultarTelefones(obj.getPessoa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)
            );
        }
        if (telefone == null || telefone.isEmpty()
                && obj.getPessoa().getTelefoneVOs() != null && obj.getPessoa().getTelefoneVOs().size() > 0) {
            telefone = obj.getPessoa().getTelefoneVOs().get(0).getNumeroApresentar();
        }
        this.telefonePix = UteisTelefone.removerCaracteresEspeciais(telefone);

        if (convenioPixSelecionado != null && convenioPixSelecionado.getCodigo() != 0) {

            try {
                //validar crédito pacto
                getFacade().getPix().validarCreditosPacto(obj.getEmpresa());
            } catch (Exception ex) {
                obj.setPagamentoAberto(false);
                obj.setMovPagamentoEscolhida(false);
                throw ex;
            }

            if (obj.getTipoPagador() != null &&
                    obj.getTipoPagador().equalsIgnoreCase("CN") &&
                    UteisValidacao.emptyString(obj.getPessoa().getCfp())) {
                setApresentarCPFPix(true);
            }

            //é chamado somente para validar o cpf
            if (obj != null && !UteisValidacao.emptyNumber(obj.getPessoa().getCodigo())) {
                TipoPessoa tipoPessoa = getFacade().getPessoa().consultarTipoPessoa(obj.getPessoa().getCodigo());
                if (tipoPessoa != null && tipoPessoa.equals(TipoPessoa.FISICA)) {
                    getFacade().getPix().obterCPF(obj.getPessoa());
                } else if (tipoPessoa.equals(TipoPessoa.JURIDICA) && UteisValidacao.emptyString(obj.getPessoa().getCnpj())) {
                    throw new Exception("O aluno é uma pessoa Jurídica e não possui CNPJ cadastrado.");
                }
            }

            Double valor = 0.0;
            if (UteisValidacao.emptyNumber(getValorResiduoBaseCalculo())) {
                valor = obj.getValorTotal();
            } else {
                valor = getValorResiduoBaseCalculo();
            }

            validarValoresPagamentoPix(valor);

            if (convenioPixSelecionado.isPixPjBank()) {
                convenioPixSelecionado.setPixClientId(convenioPixSelecionado.getCredencialPJBank());
                convenioPixSelecionado.setPixClientSecret(convenioPixSelecionado.getChavePJBank());
            }

            pixVO = getFacade().getPix().gerarObterPix(getKey(), obj.getPessoa(), convenioPixSelecionado, obj.getEmpresa(),
                    getListaValidacaoParcela(), getUsuarioLogado(), obj.getFormaPagamento().getCodigo(), OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO, getOpcaoCobrarMulta());
            confirmarCobrancaPix = true;
            consultandoPixAgora = false;
            setPixGerado(true);
        }
    }

    public void acaoSelecionarConvenioPix() {
        try {
            setMsgAlert("");
            limparMsg();
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            convenioPixSelecionado = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenioPix, obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            if (convenioPixSelecionado.isPixPjBank()) {
                convenioPixSelecionado.setPixClientId(convenioPixSelecionado.getCredencialPJBank());
                convenioPixSelecionado.setPixClientSecret(convenioPixSelecionado.getChavePJBank());
            }
            convenioPixSelecionado.setEmpresa(obj.getEmpresa());
            gerarObterPix(obj);
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }
    }

    public void prepararPagamentoOnlineDebito() {
        try {
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");

            if (obj.getFormaPagamento().isCartaoDebitoOnline() && !obj.getOperadoraCartaoVO().getCodigo().equals(0)) {
//                validarValoresPagamentoOnLine(obj); //Não validar devido a mudanças no ticket M2-2473 que estamos identificando os impactos
                PagamentoCartaoDebitoOnlineControle pagControl = (PagamentoCartaoDebitoOnlineControle) JSFUtilities.getFromSession(PagamentoCartaoDebitoOnlineControle.class);
                if (pagControl == null) {
                    pagControl = new PagamentoCartaoDebitoOnlineControle();
                }
                pagControl.prepararControlador(this.getListaValidacaoParcela());
                pagControl.getDadosPagamento().setParcelas(1);
                pagControl.setValorPagar(obj.getValorTotal());
                pagControl.setMovPagamentoSelecionado(obj);
                pagControl.setPessoaPagamento(obj.getPessoa());
                pagControl.setResponsavelPagamento(getUsuarioLogado());
                pagControl.validar();
                pagControl.setOperadoraCartao(obj.getOperadoraCartaoVO().getCodigo());
                pagControl.selecionaOperadora();
                pagControl.consultarResponsavel();
                JSFUtilities.storeOnSession(PagamentoCartaoCreditoControle.class.getSimpleName(), pagControl);
                this.getMovPagamentoVO().setUsarPagamentoDebitoOnline(true);
            }
        } catch (Exception e) {
            this.getMovPagamentoVO().setUsarPagamentoDebitoOnline(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private boolean deveRestaurarValorTotalDivida() {
        boolean resultado = true;
        for (Object obj : getListaSelectItemMovPagamento()) {
            MovPagamentoVO mp = (MovPagamentoVO) obj;

            if (mp.getMovPagamentoEscolhida()) {
                resultado = false;
                break;
            }
        }
        return resultado;
    }

    public Boolean validarValorResiduoBaseCalculo(MovPagamentoVO obj) throws ConsistirException {
        if (getValorResiduoBaseCalculo() < 0.0) {
            setMensagemDetalhada("A(s) forma(s) de pagamento já selecionada(s) paga(m) a(s) parcela(s) ");
            if (!obj.getMovPagamentoEscolhida() && obj.getValorTotal().doubleValue() != 0) {
                final NumberFormat nf = NumberFormat.getInstance();
                nf.setMinimumFractionDigits(2);
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && getAutorizar()) {
                    desmontarContaCorrenteCliente(obj, nf);
                }
                calcularPagamentoSeletcCheckboxContaCliente(false);
                setTotalLancado(getTotalLancado() - obj.getValorTotal());
                setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() + obj.getValorTotal());
                obj.setValorTotal(0.0);
                setMensagemDetalhada("");
            }

            obj.setMovPagamentoEscolhida(false);
            return true;
        }
        return false;
    }

    public void validarMovPagamento(MovPagamentoVO obj) throws Exception {
        try {
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(2);

            if (obj.getMovPagamentoEscolhida()) {
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && !getAutorizar()) {
                    calcularPagamentoSeletcCheckboxContaCliente(getUsuarioLogado().isPedirSenhaFuncionalidade());
                    return;
                }
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    calcularPagamentoSeletcCheckboxCheque();
                    return;
                }
                obj.setValorTotal(getValorResiduoBaseCalculo());
                setTotalLancado(getTotalLancado() + obj.getValorTotal());
                setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() - obj.getValorTotal());
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && getAutorizar()) {
                    montarDadosContaCorrenteCliente(obj, nf);
                }
            } else {
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && getAutorizar()) {
                    desmontarContaCorrenteCliente(obj, nf);
                }
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    desmontarCheque(obj);
                    this.setCheque(new ChequeVO());
                }
                setTotalLancado(getTotalLancado() - obj.getValorTotal());
                setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() + obj.getValorTotal());
                obj.setValorTotal(0.0);
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                    calcularPagamentoSeletcCheckboxContaCliente(false);
                    return;
                }

            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void calcularPagamentoSeletcCheckboxCheque() {
        setAbrirRichModalCheque(true);
        setCheque(new ChequeVO());
        setMensagemDetalhada("");
        ChequeControle controlador = (ChequeControle) getControlador(ChequeControle.class.getSimpleName());
        controlador.novo();
        controlador.setQtdeCheques(qtdeCheques);
        controlador.setFinanceiro(false);

    }

    public void calcularPagamentoSeletcCheckboxContaCliente(Boolean abrirRichModal) {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                final NumberFormat nf = NumberFormat.getInstance();
                nf.setMinimumFractionDigits(2);
                getMovContaCorrenteCliente().setResponsavelAutorizacao(auto.getUsuario());

                getMovContaCorrenteCliente().getResponsavelAutorizacao().setUserOamd(auto.getUsuario().getUserOamd());
                /*Como Atributo Valor nao e gravado no banco ele fica com o valor do Saldo anterior caso a pesso desista de  utilizar a forma de pagamento CC*/
                getMovContaCorrenteCliente().setValor(getMovContaCorrenteCliente().getSaldoAtual());
                setAutorizar(true);
                setAbrirRichModalContaCorrente(false);
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) i.next();
                    if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && getAutorizar()) {
                        if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Nenhum) {
                            obj.setValorTotal(getValorResiduoBaseCalculo());
                            setTotalLancado(getTotalLancado() + obj.getValorTotal());
                            setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() - obj.getValorTotal());
                        }
                        montarDadosContaCorrenteCliente(obj, nf);
                    }
                }
                setMensagemDetalhada("");
                setMensagem("");
                setErro(false);
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                auto.setRenderComponents("panelAutorizacaoFuncionalidade,panelMovCC");
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) i.next();
                    if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                        obj.setMovPagamentoEscolhida(false);
                    }
                }
            }
        };

        limparMsg();

        if (abrirRichModal) {
            if (getAbrirRichModalContaCorrente()) {
                auto.autorizar("Autorização Para Utilizar Crédito em Academia", "MovPagamento",
                        "Você precisa da permissão \"4.07 - Movimento de Pagamento\"",
                        "form:geralnovopagamento", listener);
            } else {
                auto.autorizar("Autorização Para Utilizar Dinheiro em Academia", "MovPagamento",
                        "Você precisa da permissão \"4.07 - Movimento de Pagamento\"",
                        "form:geralnovopagamento", listener);
            }
            setAbrirRichModalContaCorrente(true);
            setMensagemDetalhada("");
        } else {
            setAbrirRichModalContaCorrente(false);
            setAbrirRichModalCheque(false);
            verificarUsuarioSenhaResponsavelLiberacaoContaAcademia();
            setMensagemDetalhada("");
        }

    }

    public void verificarAutorizacaoUtilizarCredito() {
        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);

                final NumberFormat nf = NumberFormat.getInstance();
                nf.setMinimumFractionDigits(2);
                if (getUsuarioLogado().isPedirSenhaFuncionalidade()) {
                    getMovContaCorrenteCliente().setResponsavelAutorizacao(auto.getUsuario());
                } else {
                    getMovContaCorrenteCliente().setResponsavelAutorizacao(auto.getUsuario());
                }
                getMovContaCorrenteCliente().getResponsavelAutorizacao().setUserOamd(auto.getUsuario().getUserOamd());
                /*Como Atributo Valor nao e gravado no banco ele fica com o valor do Saldo anterior caso a pesso desista de  utilizar a forma de pagamento CC*/
                getMovContaCorrenteCliente().setValor(getMovContaCorrenteCliente().getSaldoAtual());
                setAutorizar(true);
                setAbrirRichModalContaCorrente(false);
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) i.next();
                    if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && getAutorizar()) {
                        if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Nenhum) {
                            obj.setValorTotal(getValorResiduoBaseCalculo());
                            setTotalLancado(getTotalLancado() + obj.getValorTotal());
                            setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() - obj.getValorTotal());
                        }
                        getMovContaCorrenteCliente().setSaldoAtual(getSaldoInicial());
                        montarDadosContaCorrenteCliente(obj, nf);
                    }
                }
                setMensagemDetalhada("");
                setMensagem("");
                setErro(false);

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {

            }

            @Override
            public void onFecharModalAutorizacao() {
                try {
                    auto.setRenderComponents("panelAutorizacaoFuncionalidade,tabContaCorrente,panelMovCC,containerTotalizadores");
                    setTipoOperacaoContaCorrenteEnum(TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);
                    Iterator i = getListaSelectItemMovPagamento().iterator();
                    while (i.hasNext()) {
                        MovPagamentoVO obj = (MovPagamentoVO) i.next();
                        if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                            obj.setValorTotal(0.0);
                            obj.setMovPagamentoEscolhida(false);
                        }
                    }
                    calcularPagamentoInput();
                } catch (Exception e) {
                    montarErro(e);
                }

            }
        };

        limparMsg();
        try {
            if (getTipoOperacaoContaCorrenteEnum() == tipoOperacaoContaCorrenteEnum.TOCC_Nenhum) {
                getMovContaCorrenteCliente().setSaldoAtual(getSaldoInicial());
            }
            calcularPagamentoInput();
            if (getAbrirRichModalContaCorrente() && getUsuarioLogado().isPedirSenhaFuncionalidade()) {
                auto.autorizar("Autorização Para Utilizar Dinheiro em Academia", "MovPagamento",
                        "Você precisa da permissão \"4.07 - Movimento de Pagamento\"",
                        "panelCobrancaMultaJuros, panelSaldoContaCorrente,mensagem1, " +
                                "escolhaFormaPagamento,escolhaFormaPagamentoCC,mensagemInf, " +
                                "mensagemSup, totalLancado, residuo, panelBotoesControle", listener);
            } else if (getAbrirRichModalContaCorrente()) {
                verificarUsuarioSenhaResponsavelLiberacaoContaAcademia();
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void calcularEncapsulado() {
        try {
            calcularPagamentoInput();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void calcularPagamentoInput() throws Exception {
        final NumberFormat nf = NumberFormat.getInstance();
        nf.setMinimumFractionDigits(2);
        Double soma = 0.0;
        calcularTotalPontosParceiroFidelidade();

        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getMovPagamentoEscolhida()) {
                //Não validar devido a mudanças no ticket M2-2473 que estamos identificando os impactos
//                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")
//                        && movPagamento.isPermitePeloCodigoIntegracaoHabilitado(movPagamento.getNrParcelaCartaoCredito())) {
//                    validarValoresPagamentoOnLine(movPagamento);
//                }
                if (movPagamento.getValorTotal() < 0) {
                    movPagamento.setValorTotal(movPagamento.getValorTotal() * -1);
                }

                if (!movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                        Iterator j = movPagamento.getChequeVOs().iterator();
                        movPagamento.setValorTotal(0.0);
                        while (j.hasNext()) {
                            ChequeVO novoCheque = (ChequeVO) j.next();
                            if (novoCheque.getValor() < 0) {
                                throw new Exception("Cheque não pode ter valor negativo.");
                            }
                            movPagamento.setValorTotal(movPagamento.getValorTotal() + novoCheque.getValor());
                            novoCheque.setValorTotal(novoCheque.getValor());
                        }
                    }
                    soma += movPagamento.getValorTotal();
                }
            }
        }
        if (getTipoOperacaoContaCorrenteEnum() == tipoOperacaoContaCorrenteEnum.TOCC_Devolver) {
            setValorResiduoBaseCalculo(this.valorResiduo - soma);
        } else {
            setValorResiduoBaseCalculo(getValorResiduo() - soma);
        }
        if (getSaldoInicial().doubleValue() != 0
                && !apresentarBotaoRecibo && movContaCorrenteCliente.isApresentarPanelSaldoContaCorrente()
                && !(getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Nenhum)) {
            Iterator j = getListaSelectItemMovPagamento().iterator();
            while (j.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) j.next();
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                    if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Devolver) {
                        getMovContaCorrenteCliente().setSaldoAtual(getSaldoInicial());
                        if (getValorResiduoBaseCalculo().doubleValue() > 0.0) {
                            if (getValorResiduoBaseCalculo() <= getMovContaCorrenteCliente().getSaldoAtual().doubleValue()) {
                                getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(getValorResiduoBaseCalculo());
                            } else {
                                getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(Math.abs(getMovContaCorrenteCliente().getSaldoAtual())));
                            }
                            movPagamento.setMovPagamentoEscolhida(true);
                        } else {
                            getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(0.0);
                            setTipoOperacaoContaCorrenteEnum(TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);
                            movPagamento.setMovPagamentoEscolhida(false);
                            movPagamento.setSaldoContaCorrenteCliente("Saldo Disponível de: " + nf.format(Double.valueOf(getSaldoInicial().toString())));
                        }
                        movPagamento.setValorTotal(new Double(getMovPagamentoVO().getValorReceberOuDevolverContaCorrente()));
                        if (!getAutorizar()) {
                            calcularPagamentoSeletcCheckboxContaCliente(true);
                        }
                        //soma += getMovPagamentoVO().getValorReceberOuDevolverContaCorrente();
                    } else if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Receber) {

                        if (getMovPagamentoVO().getValorReceberOuDevolverContaCorrente().doubleValue() == 0) {
                            if (this.valorResiduo <= getMovContaCorrenteCliente().getSaldoAtual().doubleValue()) {
                                getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(this.valorResiduo);
                            } else {
                                getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(Math.abs(getMovContaCorrenteCliente().getSaldoAtual()));
                            }
                        }
                        if (getMovPagamentoVO().getValorReceberOuDevolverContaCorrente() > Math.abs(getSaldoInicial())) {
                            getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(Math.abs(getSaldoInicial()));
                        }
                        setValorResiduoBaseCalculo(this.valorResiduo - soma + getMovPagamentoVO().getValorReceberOuDevolverContaCorrente());
                        if (!getAutorizar()) {
                            calcularPagamentoSeletcCheckboxContaCliente(true);
                        }
                        if (getValorResiduoBaseCalculo().doubleValue() > 0.0 && movPagamento.getMovPagamentoEscolhida()) {
                            if (getValorResiduoBaseCalculo() < movPagamento.getValorTotal()) {
                                movPagamento.setValorTotal(new Double(getValorResiduoBaseCalculo()));
                            }
                        } else {
                            movPagamento.setMovPagamentoEscolhida(false);
                            movPagamento.setValorTotal(new Double(0.0));
                        }
                    } else {
                        //soma -= getMovPagamentoVO().getValorReceberOuDevolverContaCorrente();
                        getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(0.0);
                        movPagamento.setMovPagamentoEscolhida(false);
                        movPagamento.setValorTotal(getMovPagamentoVO().getValorReceberOuDevolverContaCorrente());
                        context().getExternalContext().getRequestMap().put(
                                "movPagamento", movPagamento);
                        calcularPagamentoSeletcCheckbox();
                    }
                    if (movPagamento.getMovPagamentoEscolhida()) {
                        if (getAutorizar()) {
                            /*o campo valor tem o saldo atual do cliente na academia*/
                            getMovContaCorrenteCliente().setSaldoAtual(getMovContaCorrenteCliente().getSaldoAnterior());
                            // getMovContaCorrenteCliente().setSaldoAtual(getMovContaCorrenteCliente().getValor());
                            montarDadosContaCorrenteCliente(movPagamento, nf);
                        }
                        soma += movPagamento.getValorTotal();
                    }
                }
            }
        } else {
            getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(0.0);
            getMovContaCorrenteCliente().setSaldoAtual(getSaldoInicial());
            Iterator j = getListaSelectItemMovPagamento().iterator();
            while (j.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) j.next();
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                    if (movPagamento.getMovPagamentoEscolhida()) {
                        if (getValorResiduoBaseCalculo().doubleValue() > 0.0) {
                            if (movPagamento.getValorTotal() > getValorResiduoBaseCalculo().doubleValue()) {
                                movPagamento.setValorTotal(new Double(getValorResiduoBaseCalculo().doubleValue()));
                            }
                            if (getAutorizar()) {
                                /*o campo valor tem o saldo atual do cliente na academia*/
                                getMovContaCorrenteCliente().setSaldoAtual(getMovContaCorrenteCliente().getSaldoAnterior());
                                // getMovContaCorrenteCliente().setSaldoAtual(getMovContaCorrenteCliente().getValor());
                                montarDadosContaCorrenteCliente(movPagamento, nf);
                            }
                            soma += movPagamento.getValorTotal();
                        } else {
                            movPagamento.setValorTotal(new Double(0.0));
                            movPagamento.setMovPagamentoEscolhida(false);
                        }
                    }
                }
            }
        }

        soma = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(soma);


        setTotalLancado(soma);
        if (getTipoOperacaoContaCorrenteEnum() == tipoOperacaoContaCorrenteEnum.TOCC_Devolver) {
            setValorResiduoBaseCalculo(this.valorResiduo - soma);
        } else {
            setValorResiduoBaseCalculo(getValorResiduo() - soma);
        }
        setMensagemDetalhada("", "");
        setSucesso(false);
        setErro(false);
    }

    public void gerarDatas() throws Exception {
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getMovPagamentoEscolhida()) {
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    if (movPagamento.getChequeVOs().isEmpty()) {
                        continue;
                    }
                    Iterator j = movPagamento.getChequeVOs().iterator();
                    ChequeVO novoCheque = (ChequeVO) j.next();
                    Date novaData = novoCheque.getDataCompensacao();
                    while (j.hasNext()) {
                        novoCheque = (ChequeVO) j.next();
                        novaData = Uteis.obterDataFutura3(novaData, 1);
                        if (movPagamento.getFormaPagamento().isCompensacaoDiasUteis()) {
                            novoCheque.setDataCompensacao(getFacade().getMovPagamento().obterProximoDiaUtil(novaData, movPagamento.getEmpresa()));
                        } else {
                            novoCheque.setDataCompensacao(novaData);
                        }
                    }
                }
            }
        }
    }

    public void montarDadosContaCorrenteCliente(MovPagamentoVO movPagamento, NumberFormat nf) throws Exception {
        Double saldo = 0.0;
        if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Receber) {
            if (movPagamento.getValorTotal() > 0) {
                saldo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getSaldoInicial() - movPagamento.getValorTotal() + getMovPagamentoVO().getValorReceberOuDevolverContaCorrente());
                movPagamento.setSaldoContaCorrenteCliente("Será retirado da sua conta o VALOR: " + nf.format(Double.valueOf(movPagamento.getValorTotal().toString())) + ". Restando um Saldo de: " + nf.format(Double.valueOf(saldo.toString())));
            } else {
                saldo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getSaldoInicial() + getMovPagamentoVO().getValorReceberOuDevolverContaCorrente());
                movPagamento.setSaldoContaCorrenteCliente("Será adicionado na sua conta o VALOR: " + nf.format(Double.valueOf(getMovPagamentoVO().getValorReceberOuDevolverContaCorrente().toString())) + ". Restando um Saldo de: " + nf.format(Double.valueOf(saldo.toString())));
            }
        } else {
            if (movPagamento.getValorTotal() > getMovContaCorrenteCliente().getSaldoAtual()) {
                saldo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getMovContaCorrenteCliente().getSaldoAtual() - movPagamento.getValorTotal());
                movPagamento.setSaldoContaCorrenteCliente("Será retirado da sua conta o VALOR: " + nf.format(Double.valueOf(movPagamento.getValorTotal().toString())) + ". Restando um Saldo Negativo de: " + nf.format(Double.valueOf(saldo.toString())));
            } else {
                saldo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getMovContaCorrenteCliente().getSaldoAtual() - movPagamento.getValorTotal());
                movPagamento.setSaldoContaCorrenteCliente("Será retirado da sua conta o VALOR: " + nf.format(Double.valueOf(movPagamento.getValorTotal().toString())) + ". Restando um Saldo positivo de: " + nf.format(Double.valueOf(saldo.toString())));

            }
        }
        getMovContaCorrenteCliente().setCodigo(0);
        getMovContaCorrenteCliente().setNovoObj(true);
        getMovContaCorrenteCliente().setDescricao("Pagar Parcelas");
        getMovContaCorrenteCliente().setPessoa(movPagamento.getPessoa());
        getMovContaCorrenteCliente().setDataRegistro(Calendario.hoje());
        getMovContaCorrenteCliente().setTipoMovimentacao("DE");
        /*Como Atributo Valor nao e gravado no banco ele fica com o valor do Saldo anterior caso a pesso desista de  utilizar a forma de pagamento CC*/
        getMovContaCorrenteCliente().setSaldoAnterior(getMovContaCorrenteCliente().getSaldoAtual());
        getMovContaCorrenteCliente().setValor(movPagamento.getValorTotal());
        //getMovContaCorrenteCliente().setValor(getMovContaCorrenteCliente().getSaldoAtual());
        //descomentar
        //getMovContaCorrenteCliente().setValor(movPagamento.getValor());
        getMovContaCorrenteCliente().setSaldoAtual(saldo);
        //movPagamento.setSaldoContaCorrenteCliente("");
    }

    public void desmontarCheque(MovPagamentoVO movPagamento) throws ConsistirException {
        double valorTotalCheque = 0.0;
        Iterator j = movPagamento.getChequeVOs().iterator();
        while (j.hasNext()) {
            ChequeVO novoCheque = (ChequeVO) j.next();
            valorTotalCheque = valorTotalCheque + novoCheque.getValor();
        }
        valorTotalCheque = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorTotalCheque);
        setTotalLancado(getTotalLancado() - valorTotalCheque);
        setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() + valorTotalCheque);
        movPagamento.setValorTotal(0.0);
        movPagamento.setChequeVOs(new ArrayList());
        setAbrirRichModalCheque(false);
        setAbrirRichModalContaCorrente(false);
        setMensagemDetalhada("");
        setCheque(new ChequeVO());
    }

    public void desmontarContaCorrenteCliente(MovPagamentoVO movPagamento, NumberFormat nf) throws ConsistirException {
        getMovContaCorrenteCliente().setSaldoAtual(getMovContaCorrenteCliente().getSaldoAnterior());
        if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Devolver) {
            getMovPagamentoVO().setValorReceberOuDevolverContaCorrente(0.0);
            setTipoOperacaoContaCorrenteEnum(TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);
        }
        if (getSaldoInicial() >= 0) {
            getMovContaCorrenteCliente().setSaldoAtual(getSaldoInicial());
            movPagamento.setSaldoContaCorrenteCliente("Saldo Disponível de: " + nf.format(Double.valueOf(getMovContaCorrenteCliente().getSaldoAtual())));
        } else {
            movPagamento.setSaldoContaCorrenteCliente("Saldo Negativo de: " + nf.format(Double.valueOf(getMovContaCorrenteCliente().getSaldoAtual())));
        }

    }

    @Override
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }

                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovPagamento().consultarPorCodigo(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                objs = getFacade().getMovPagamento().consultarPorNomePessoa(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("dataPagamento")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovPagamento().consultarPorDataPagamento(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("dataLancamento")) {
                Date valorData = Uteis.getDate(getControleConsulta().getValorConsulta());
                objs = getFacade().getMovPagamento().consultarPorDataLancamento(Uteis.getDateTime(valorData, 0, 0, 0), Uteis.getDateTime(valorData, 23, 59, 59), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("descricaoFormaPagamento")) {
                objs = getFacade().getMovPagamento().consultarPorDescricaoFormaPagamento(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }

            if (getControleConsulta().getCampoConsulta().equals("nomePagador")) {
                objs = getFacade().getMovPagamento().consultarPorNomePagador(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("cpfPagador")) {
                objs = getFacade().getMovPagamento().consultarPorCpfPagador(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("numeroCheque")) {
                objs = getFacade().getMovPagamento().consultarPorNumeroCheque(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("agenciaCheque")) {
                objs = getFacade().getMovPagamento().consultarPorAgenciaCheque(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("bancoCheque")) {
                objs = getFacade().getMovPagamento().consultarPorBancoCheque(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("numeroCartao")) {
                objs = getFacade().getMovPagamento().consultarPorNumeroCartao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            if (getControleConsulta().getCampoConsulta().equals("codigoOperacaoCartao")) {
                objs = getFacade().getMovPagamento().consultarPorCodigoOperacaoCartao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "";
        }

    }

    public String excluir() {
        try {
            getFacade().getMovPagamento().excluir(movPagamentoVO);
            setMovPagamentoVO(new MovPagamentoVO());

            setPagamentoMovParcelaVO(new PagamentoMovParcelaVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }

    }

    public void consultaMovContaCorrenteCliente(Integer codigoPessoa) {
        try {
            VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
            setMovContaCorrenteCliente(new MovimentoContaCorrenteCliente().consultarPorCodigoPessoa(codigoPessoa.intValue(),
                    Uteis.NIVELMONTARDADOS_TODOS));
            if (getMovContaCorrenteCliente() == null) {
                setMovContaCorrenteCliente(new MovimentoContaCorrenteClienteVO());
                setSaldoInicial(0.0);
            } else if (getMovContaCorrenteCliente().getSaldoAtual().doubleValue() != 0
                    && (vendaAvulsaControle == null || !vendaAvulsaControle.getVendaAvulsaVO().getProdutoDebitoContaCorrente())) {
                getMovContaCorrenteCliente().setApresentarPanelSaldoContaCorrente(true);
                setSaldoInicial(getMovContaCorrenteCliente().getSaldoAtual());
            } else {
                getMovContaCorrenteCliente().setApresentarPanelSaldoContaCorrente(false);
                setSaldoInicial(0.0);
            }


        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public String adicionarPagamentoMovParcela() throws Exception {
        try {
            if (!getMovPagamentoVO().getCodigo().equals(new Integer(0))) {
                pagamentoMovParcelaVO.setMovPagamento(getMovPagamentoVO().getCodigo());
            }

            if (getPagamentoMovParcelaVO().getMovParcela().getCodigo().intValue() != 0) {
                Integer campoConsulta = getPagamentoMovParcelaVO().getMovParcela().getCodigo();
            }

            getMovPagamentoVO().adicionarObjPagamentoMovParcelaVOs(getPagamentoMovParcelaVO(), getFacade().getMovPagamento().getCon());
            this.setPagamentoMovParcelaVO(new PagamentoMovParcelaVO());
            setMensagemID("msg_dados_adicionados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }

    }

    public String editarPagamentoMovParcela() throws Exception {
        PagamentoMovParcelaVO obj = (PagamentoMovParcelaVO) context().getExternalContext().getRequestMap().get("pagamentoMovParcela");
        setPagamentoMovParcelaVO(obj);
        return "editar";
    }

    public String removerPagamentoMovParcela() throws Exception {
        PagamentoMovParcelaVO obj = (PagamentoMovParcelaVO) context().getExternalContext().getRequestMap().get("pagamentoMovParcela");
        getMovPagamentoVO().excluirObjPagamentoMovParcelaVOs(obj.getMovParcela().getCodigo());
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public void adicionarCheque() throws Exception {
        try {
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            copiarDadosCheque(obj);
            if (obj.getNumero().trim().equals("")) {
                throw new Exception("Não possível adicionar um novo cheque, pois o campo número do cheque deve ser informado");
            }
            getCheque().setNumero(Uteis.getIncrementarNumeroCheque(obj.getNumero()));
            Iterator k = this.getListaSelectItemMovPagamento().iterator();
            while (k.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) k.next();
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    int i = 0;
                    if (movPagamento.getChequeVOs().isEmpty()) {
                        getCheque().setMarcador(i);
                    } else {
                        Iterator j = movPagamento.getChequeVOs().iterator();
                        while (j.hasNext()) {
                            ChequeVO novoCheque = (ChequeVO) j.next();
                            novoCheque.setMarcador(i);
                            i++;
                        }
                        getCheque().setMarcador(i + 1);
                    }
                    movPagamento.getChequeVOs().add(getCheque());
                    this.setCheque(new ChequeVO());
                }
            }
            gerarDatas();
            setMensagem("msg_dados_adicionados");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void copiarDadosCheque(MovPagamentoVO obj) {
        Iterator j = obj.getChequeVOs().iterator();
        while (j.hasNext()) {
            ChequeVO novoCheque = (ChequeVO) j.next();
            getCheque().setAgencia(novoCheque.getAgencia());
            getCheque().setBanco(novoCheque.getBanco());
            getCheque().setCnpj(novoCheque.getCnpj());
            getCheque().setCpf(novoCheque.getCpf());
            getCheque().setConta(novoCheque.getConta());
            getCheque().setDataCompensacao(novoCheque.getDataCompensacao());
            getCheque().setNomeBanco(novoCheque.getNomeBanco());
            getCheque().setNumero(novoCheque.getNumero());
            break;
        }
    }

    public void copiarDadosCheque(ChequeVO novoCheque) {
        getCheque().setAgencia(novoCheque.getAgencia());
        getCheque().setBanco(new BancoVO());
        getCheque().getBanco().setCodigo(novoCheque.getBanco().getCodigo());
        getCheque().getBanco().setCodigoBanco(novoCheque.getBanco().getCodigoBanco());
        getCheque().getBanco().setNome(novoCheque.getBanco().getNome());
        getCheque().setCnpj(novoCheque.getCnpj());
        getCheque().setCpf(novoCheque.getCpf());
        getCheque().setConta(novoCheque.getConta());
        getCheque().setDataCompensacao(novoCheque.getDataCompensacao());
        getCheque().setNomeBanco(novoCheque.getNomeBanco());
        getCheque().setNumero(novoCheque.getNumero());
        getCheque().setNomeNoCheque(novoCheque.getNomeNoCheque());
    }

    @SuppressWarnings("unchecked")
    public void adicionarListaCheque() throws Exception {
        try {

            ChequeControle chequeControle = (ChequeControle) getControlador(ChequeControle.class.getSimpleName());
            qtdeCheques = chequeControle.getQtdeCheques();
            if (chequeControle.getCheque().getNumero().trim().equals("")) {
                throw new Exception("Não possível adicionar o(s) cheque(s), pois o campo número do cheque não foi informado");
            }
            setCheque(chequeControle.getCheque());
            qtdeCheques = (qtdeCheques <= 0 ? 1 : qtdeCheques);
            consideraParcelas = (qtdeCheques == mesesReferencia.size());
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    double somaCheque = 0.0;
                    double conferencia = 0.0;
                    double somaMovPagamento = validarExisteMovPagamentoEscolhido();
                    double valorParaCadaCheque = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorResiduoBaseCalculo() / getQtdeCheques());
                    movPagamento.setChequeVOs(new ArrayList());
                    if (consideraParcelas && getTotalLancado() == 0) {
                        int c = 0;
                        for (ParcelasPorMes ppm : mesesReferencia) {
                            inicializarDadosCheque(movPagamento, c++, ppm.getValor(), getCheque().getNomeNoCheque());
                            somaCheque += ppm.getValor();
                        }
                    } else {
                        for (int k = 0; k < qtdeCheques; k++) {
                            inicializarDadosCheque(movPagamento, k, valorParaCadaCheque, getCheque().getNomeNoCheque());
                            somaCheque += valorParaCadaCheque;
                        }
                    }
                    // o arredondamento pode gerar diferença, por isso deve-se conferir os valores gerados.
                    // soma dos cheques + soma dos outros pagamentos
                    conferencia = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(somaCheque + getTotalLancado());
                    // se a soma de tudo for diferente do total da divida
                    if (totalDivida != conferencia) {
                        conferencia = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(totalDivida - conferencia);
                        // corrige a diferença no primeiro cheque
                        ChequeVO ch = (ChequeVO) movPagamento.getChequeVOs().get(0);
                        ch.setValor(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(ch.getValor() + conferencia));
                        somaCheque = conferencia + somaCheque;
                    }
                    totalLancado = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(totalLancado + somaCheque);
                    setValorResiduoBaseCalculo(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(getValorResiduoBaseCalculo() - somaCheque));
                    movPagamento.setValorTotal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(somaCheque));
                    ChequeControle.incrementaNumeroChequeLista(movPagamento.getChequeVOs());
                }
            }
            gerarDatas();
            setCheque(new ChequeVO());
            setAbrirRichModalCheque(false);
            setSucesso(false);
            setErro(false);
            setMensagemID("");
            setMensagemDetalhada("");
            setMsgAlert("Richfaces.hideModalPanel('panelCheque');");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setMsgAlert("Richfaces.showModalPanel('panelCheque');");
        }
    }

    public void validarParcelaRemessa(final MovParcelaVO mp) throws Exception {
        if (getFacade().getMovParcela().parcelaEstaBloqueadaPorCobranca(mp)) {
            StringBuilder sb = new StringBuilder();
            sb.append("Você está tentando receber a parcela \"%s\" do Cliente \"%s\".");
            sb.append("Porém, ela se encontra em uma Remessa de Cobrança que já foi enviada ao Banco, ou em uma transação pendente. ");
            sb.append("É necessário você esperar o processamento do arquivo de Retorno Bancário ou a resposta da operadora.");
            sb.append("Isso é uma medida de segurança que evita inconsistências como \"Duplicidade de Pagamentos\".");
            if (!UteisValidacao.emptyNumber(mp.getCodRemessaQueEstaVinculada())) {
                sb.append(" Cód. da remessa que a parcela está vinculada: ").append(mp.getCodRemessaQueEstaVinculada());
            }
            throw new ConsistirException(String.format(sb.toString(), mp.getCodigo(),
                    mp.getPessoa().getNome()));
        }
    }

    public void validarParcelaBoletoOnlinePendente(final MovParcelaVO mp) throws Exception {
        if (UteisValidacao.emptyNumber(mp.getCodigo())) {
            throw new ConsistirException("Você está tentando receber uma parcela sem Código.");
        }
        if (getFacade().getBoleto().existeBoletoPendentePorMovParcela(mp.getCodigo(), true)) {
            throw new ConsistirException(String.format(
                    "Você está tentando receber a parcela \"%s\" do Cliente \"%s\". Porém, "
                            + "ela se encontra em um boleto pendente. "
                            + "É necessário aguardar o aluno efetuar o pagamento ou Cancelar o boleto. "
                            + "Isso é uma medida de segurança que evita uma inconsistência do tipo "
                            + "\"Duplicidade de Pagamentos\".", mp.getCodigo(),
                    mp.getPessoa().getNome()));
        }
    }

    private void processarParcelas(List<MovParcelaVO> lista) throws Exception {
        mesesReferencia = new ArrayList<ParcelasPorMes>();
        for (MovParcelaVO mp : lista) {
            validarParcelaRemessa(mp);
            validarParcelaBoletoOnlinePendente(mp);
            String strMesReferencia = Uteis.getMesReferenciaData(mp.getDataVencimento());
            int i = encontraMesReferencia(mesesReferencia, strMesReferencia);
            if (i < 0) {
                ParcelasPorMes ppp = new ParcelasPorMes();
                ppp.setReferencia(strMesReferencia);
                ppp.adicionaParcela(mp);
                mesesReferencia.add(ppp);
            } else {
                ((ParcelasPorMes) mesesReferencia.get(i)).adicionaParcela(mp);
            }
        }
        qtdeCheques = mesesReferencia.size();
    }

    public double validarExisteMovPagamentoEscolhido() {
        double soma = 0.0;
        Iterator e = listaSelectItemMovPagamento.iterator();
        while (e.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) e.next();
            if (movPagamento.getMovPagamentoEscolhida()) {
                if (movPagamento.getMovPagamentoEscolhida() && !movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    soma = (soma + movPagamento.getValor());
                }
            }
        }
        return soma;
    }

    public void inicializarDadosCheque(MovPagamentoVO movPagamento, int marcador, double valorCheque, String nomeNoCheque) throws Exception {
        try {
            getChequeClone().setDataCompensacao(Calendario.hoje());
            getChequeClone().setAgencia(getCheque().getAgencia());
            getChequeClone().setBanco(new BancoVO());
            getChequeClone().getBanco().setCodigo(getCheque().getBanco().getCodigo());
            getChequeClone().getBanco().setCodigoBanco(getCheque().getBanco().getCodigoBanco());
            getChequeClone().getBanco().setNome(getCheque().getBanco().getNome());
            getChequeClone().setConta(getCheque().getConta());
            getChequeClone().setNumero(getCheque().getNumero());
            getChequeClone().setCpf(getCheque().getCpf());
            getChequeClone().setCnpj(getCheque().getCnpj());
            getChequeClone().setSituacao("EA");
            getChequeClone().setMarcador(marcador);
            getChequeClone().setValor(valorCheque);
            getChequeClone().setValorTotal(valorCheque);
            getChequeClone().setNomeNoCheque(nomeNoCheque);
            movPagamento.getChequeVOs().add(getChequeClone());
            setChequeClone(new ChequeVO());
        } catch (Exception e) {
            throw e;
        }
    }

    public String editarCheque() throws Exception {
        ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
        setCheque(obj);
        return "editar";
    }

    public void removerCheque() throws Exception {
        ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
        Iterator i = listaSelectItemMovPagamento.iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                Iterator j = movPagamento.getChequeVOs().iterator();
                while (j.hasNext()) {
                    ChequeVO novoCheque = (ChequeVO) j.next();
                    if (novoCheque.getMarcador().equals(obj.getMarcador())) {
                        movPagamento.excluirObjChequeVOs(obj.getMarcador());
                        calcularPagamentoInput();
                        return;
                    }
                }
            }
        }
    }

    public Boolean getDesenharBotao() {
        try {
            Iterator i = listaSelectItemMovPagamento.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                    if (movPagamento.getChequeVOs().size() == 1) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return true;
        }

    }

    public String getAbrirRichModalConfirmaPagamento() {
        try {
            Boolean existeSomentePagamentoAvista = true;
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj = (MovPagamentoVO) i.next();
                if (obj.getMovPagamentoEscolhida() && !obj.getTipoPagador().equals("CI")) {
                    existeSomentePagamentoAvista = false;
                }
            }
            if (getAbrirRichModalConfirmacaoPagamento() && existeSomentePagamentoAvista && getValorResiduoBaseCalculo() < 0.0) {
                return "Richfaces.showModalPanel('panelConfirmacaoPagamentoDepositoNaConta'), setFocus(formConfirmacaoPagamentoDepositoNaConta,'formConfirmacaoPagamentoDepositoNaConta:depositarContar')";
            } else if (getAbrirRichModalConfirmacaoPagamento() /*&& (!pagamentoDigitalMarcado)*/) {
                return showModalConfirmacaoPagamento();
            } else {
                return "";
            }
        } catch (Exception e) {
            montarErro(e);
            return "";
        }
    }

    public String showModalConfirmacaoPagamento() throws Exception {
        if (getUsuarioLogado().isPedirSenhaFuncionalidade()) {
            return "Richfaces.showModalPanel('panelConfirmacaoPagamento'), setFocus(formConfirmacaoPagamento,'formConfirmacaoPagamento:username')";
        } else {
            this.incluirCupom = true;
            verificarUsuarioSenhaResponsavelPagamento(false, null);
            return getApresentarMensagem();
        }
    }

    public void validarReceberTroco() {
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO obj = (MovPagamentoVO) i.next();
            if (obj.getMovPagamentoEscolhida() && !obj.getTipoPagador().equals("CI")) {
                setReceberTroco(null);
            }
        }
    }

    public void devolverTroco() {
        setReceberTroco(true);
    }

    public void depositarNaConta() {
        setReceberTroco(false);
        validarDadosPagamento();
        verificarUsuarioSenhaResponsavelPagamento(false, null);
        //confirmarPermissaoUsuario();
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void montarListaSelectItemConvenioCobranca() {
        montarListaSelectItemConvenioCobranca(true);
    }

    private void montarListaSelectItemConvenioCobranca(boolean origemTelaFormaPagamento) {
        try {
            List<ConvenioCobrancaVO> resultadoConsulta;
            if (origemTelaFormaPagamento) {
                //origem tela de forma de pagamento montar somente convênios ativos
                resultadoConsulta = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(movPagamentoVO.getEmpresa().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, SituacaoConvenioCobranca.ATIVO);
            } else {
                resultadoConsulta = getFacade().getConvenioCobranca().consultarTodos(Uteis.NIVELMONTARDADOS_TODOS);
            }

            List<SelectItem> listaSelectItem = new ArrayList<>();
            for (ConvenioCobrancaVO obj : resultadoConsulta) {
                listaSelectItem.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            Ordenacao.ordenarLista(listaSelectItem, "label");
            setListaSelectItemConvenioCobranca(listaSelectItem);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Nao foi Possivel Montar a lista de Convênios de Cobrança.");
            setListaSelectItemConvenioCobranca(new ArrayList<SelectItem>());
        }
    }

    public void montarListaTodosConvenios() {
        try {
            setListaTodosConvenios(getFacade().getConvenioCobranca().consultarPorEmpresa(movPagamentoVO.getEmpresa().getCodigo(),
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Não foi Possivel Montar a lista de Convênios de Cobrança.");
        }
    }

    public void montarListaSelectItemOperadoraCartaoCredito() throws Exception {
        setListaSelectItemOperadoraCartaoCredito(new ArrayList<SelectItem>());
        List resultadoConsulta = new ArrayList();

        resultadoConsulta = getFacade().getOperadoraCartao().consultarPorTipo(true, false, Uteis.NIVELMONTARDADOS_TODOS);

        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            OperadoraCartaoVO obj = (OperadoraCartaoVO) i.next();
            getListaSelectItemOperadoraCartaoCredito().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
        }
        Ordenacao.ordenarLista(getListaSelectItemOperadoraCartaoCredito(), "label");
        getListaSelectItemOperadoraCartaoCredito().add(0, new SelectItem(new Integer(0), ""));
    }

    public void montarListaSelectItemAdquirente() throws Exception {
        setListaSelectItemAdquirente(new ArrayList<SelectItem>());
        List<AdquirenteVO> resultadoConsulta = new ArrayList<AdquirenteVO>();
        resultadoConsulta = getFacade().getAdquirente().consultarTodos(true);

        getListaSelectItemAdquirente().add(new SelectItem(new Integer(0), ""));
        for (AdquirenteVO ad : resultadoConsulta) {
            getListaSelectItemAdquirente().add(new SelectItem(ad.getCodigo(), ad.getNome().toString()));
        }
    }

    public void montarListaSelectItemOperadoraCartaoDebito() throws Exception {
        setListaSelectItemOperadoraCartaoDebito(new ArrayList<SelectItem>());
        List<AdquirenteVO> resultadoConsulta = new ArrayList<AdquirenteVO>();
        resultadoConsulta = getFacade().getOperadoraCartao().consultarPorTipo(false, false, Uteis.NIVELMONTARDADOS_TODOS);

        Iterator i = resultadoConsulta.iterator();
        getListaSelectItemOperadoraCartaoDebito().add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            OperadoraCartaoVO obj = (OperadoraCartaoVO) i.next();
            getListaSelectItemOperadoraCartaoDebito().add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
    }

    public void montarListaSelectItemOperadoraCartao() {
        try {
            setListaSelectItemNrParcelaCartao(new ArrayList<SelectItem>());
            montarListaSelectItemOperadoraCartaoCredito();
            montarListaSelectItemOperadoraCartaoDebito();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Nao foi Possivel Montar a lista de Operadoras de Cartao.");
            setListaSelectItemOperadoraCartaoCredito(new ArrayList<SelectItem>());
            setListaSelectItemOperadoraCartaoDebito(new ArrayList<SelectItem>());
        }
    }

    public void inicializarListasSelectItemTodosComboBox(boolean origemFormaPagamento) throws Exception {
        montarListaSelectItemAdquirente();
        montarListaSelectItemOperadoraCartao();
        montarListaSelectItemBanco();
        montarListaSelectItemConvenioCobranca(origemFormaPagamento);
        montarListaTodosConvenios();
        montarListaNrParcelasCartao(movPagamentoVO.getOperadoraCartaoVO());
        montarListaDia();
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nomePessoa", "Pessoa"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("dataPagamento", "Data Pagamento"));
        itens.add(new SelectItem("dataLancamento", "Data Lançamento"));
        itens.add(new SelectItem("descricaoFormaPagamento", "Forma Pagamento"));
        itens.add(new SelectItem("nomePagador", "Nome do Pagador"));
        itens.add(new SelectItem("cpfPagador", "Cpf do Pagador"));
        itens.add(new SelectItem("numeroCheque", "Número Cheque"));
        itens.add(new SelectItem("agenciaCheque", "Agência Cheque"));
        itens.add(new SelectItem("bancoCheque", "Banco Cheque"));
        itens.add(new SelectItem("numeroCartao", "Número Cartão"));
        itens.add(new SelectItem("codigoOperacaoCartao", "Código Operação Cartão"));
        return itens;
    }

    public List getTipoConsultaComboPessoa() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("tipoPessoa", "Tipo Pessoa"));
        itens.add(new SelectItem("descricaoProfissao", "Profissão"));
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("cfp", "Cfp"));
        itens.add(new SelectItem("nomeCidade", "Cidade"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Apresentar a tela de caixa em aberto com o nome da pessoa no campo de
     * pesquisa
     *
     * @return
     * @throws Exception
     */
    public String voltar() throws Exception {
        try {
            MovParcelaControle movParcelaControle = (MovParcelaControle) getControlador(MovParcelaControle.class.getSimpleName());
            permissaoFuncionalidade(getUsuarioLogado(), "CaixaEmAberto", "2.44 - Operação - Caixa em Aberto");
            String nomePessoa = movPagamentoVO.getPessoa().getNome();
            EmpresaVO empresaVO = movParcelaControle.getEmpresa();
            liberarBackingBeanMemoria(MovPagamentoControle.class.getSimpleName());
            liberarBackingBeanMemoria(MovParcelaControle.class.getSimpleName());
            MovParcelaControle movParcela = (MovParcelaControle) getControlador(MovParcelaControle.class.getSimpleName());
            movParcela.setValorConsulta(nomePessoa);
            movParcela.setEmpresa(empresaVO);
            return "tela8";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "erro";
        }
    }

    public String retornaTela3() {
        return "gravar";
    }

    public String getApresentarMensagem() {
        if (isApresentarPagamentoDigital()) {
            String retorno = getAbrirPagamentoDigital();
            setApresentarPagamentoDigital(false);
            return retorno;
        } else {
            return "Richfaces.showModalPanel('panelDadosGravadosComSucesso');";
        }
    }

    public String getAutorizacao() {
        try {
            if (getAbrirRichModalCheque()) {
                return "Richfaces.showModalPanel('panelCheque'), setFocus(formCheque,'formCheque:listaCheque:0:codigoBanco');";
            }
            if (getAbrirRichModalContaCorrente() && getUsuarioLogado().isPedirSenhaFuncionalidade()) {
                return "Richfaces.showModalPanel('panelAutorizacao'), setFocus(formAutorizacao,'formAutorizacao:username')";
            } else if (getAbrirRichModalContaCorrente()) {
                verificarUsuarioSenhaResponsavelLiberacaoContaAcademia();
                return "Richfaces.hideModalPanel('panelAutorizacao')";
            } else {
                return "Richfaces.hideModalPanel('panelAutorizacao')";
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            return "Richfaces.showModalPanel('panelAutorizacao'), setFocus(formAutorizacao,'formAutorizacao:username')";
        }
    }

    public void verificarUsuarioSenhaResponsavelLiberacaoContaAcademia() {
        try {
            final NumberFormat nf = NumberFormat.getInstance();
            nf.setMinimumFractionDigits(2);
            if (getUsuarioLogado().isPedirSenhaFuncionalidade()) {
                getMovContaCorrenteCliente().setResponsavelAutorizacao(getFacade().getControleAcesso().verificarLoginUsuario(getMovContaCorrenteCliente().getResponsavelAutorizacao().getCodigo().intValue(), getMovContaCorrenteCliente().getResponsavelAutorizacao().getSenha().toUpperCase()));
            } else {
                getMovContaCorrenteCliente().setResponsavelAutorizacao(getUsuarioLogado());
            }
            getMovContaCorrenteCliente().getResponsavelAutorizacao().setUserOamd(getUsuarioLogado().getUserOamd());
            /*Como Atributo Valor nao e gravado no banco ele fica com o valor do Saldo anterior caso a pesso desista de  utilizar a forma de pagamento CC*/
            getMovContaCorrenteCliente().setValor(getMovContaCorrenteCliente().getSaldoAtual());
            setAutorizar(true);
            setAbrirRichModalContaCorrente(false);
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj = (MovPagamentoVO) i.next();
                if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC") && getAutorizar() && obj.getMovPagamentoEscolhida()) {
                    if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Nenhum) {
                        obj.setValorTotal(getValorResiduoBaseCalculo());
                        setTotalLancado(getTotalLancado() + obj.getValorTotal());
                        setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() - obj.getValorTotal());
                    }
                    getMovContaCorrenteCliente().setSaldoAtual(getSaldoInicial());
                    montarDadosContaCorrenteCliente(obj, nf);
                }
            }
            setMensagemDetalhada("");
            setMensagem("");
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAutorizar(false);
            setAbrirRichModalContaCorrente(true);
        }

    }

    public void fecharJanelaResponsavelLiberacaoContaAcademia() throws Exception {
        setTipoOperacaoContaCorrenteEnum(TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO obj = (MovPagamentoVO) i.next();
            if (obj.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                obj.setMovPagamentoEscolhida(false);
                obj.setValorTotal(new Double(0));
            }
        }
        calcularPagamentoInput();
        setAbrirRichModalContaCorrente(false);
        setMensagemDetalhada("");
        setMensagem("");
        setMensagemID("");
        setErro(false);
    }

    public void verificarUsuarioSenhaResponsavelPagamentoCupom() {
        this.incluirCupom = true;
        this.verificarUsuarioSenhaResponsavelPagamento();
    }

    public void verificarUsuarioSenhaResponsavelPagamentoSemCupom() {
        this.incluirCupom = false;
        this.verificarUsuarioSenhaResponsavelPagamento();
    }

    public void verificarUsuarioSenhaResponsavelPagamento() {
        verificarUsuarioSenhaResponsavelPagamento(true, null);
    }

    public void verificarUsuarioSenhaResponsavelPagamento(boolean validarUsuarioSenha, OpcoesPinpadEnum pinPad) {
        try {
            limparMsg();
            setProcessandoOperacao(true);
            UsuarioVO usuario = movPagamentoVO.getResponsavelPagamento();
            if (validarUsuarioSenha) {
                usuario = getFacade().getControleAcesso().verificarLoginUsuarioPIN(
                        movPagamentoVO.getResponsavelPagamento().getUsername(),
                        movPagamentoVO.getResponsavelPagamento().getSenha().toUpperCase());
            }
            usuario.setUserOamd(movPagamentoVO.getResponsavelPagamento().getUserOamd());
            this.setUsuarioLiberacao(usuario);
            context().getExternalContext().getRequestMap().put("responsavelOperacao", usuario);
            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            if (movParcelaControle != null) {
                getFacade().getMovPagamento().validarPermissaoDataCheque(usuario, movParcelaControle.getListaParcelasPagar(), listaSelectItemMovPagamento);
                validarPagamentoChequeDevolvido(movParcelaControle.getListaParcelasPagar());
            } else if (parcelaControle != null) {
                getFacade().getMovPagamento().validarPermissaoDataCheque(usuario, parcelaControle.getListaParcelasPagar(), listaSelectItemMovPagamento);
                validarPagamentoChequeDevolvido(parcelaControle.getListaParcelasPagar());
            }
            Iterator i = listaSelectItemMovPagamento.iterator();
            boolean usandoPagamentoDigital = false;
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida()) {
                    movPagamento.setResponsavelPagamento(usuario);
                    usandoPagamentoDigital = movPagamento.getUsarPagamentoDigital();
                }
            }
            validarReceberTroco();
            if (!usandoPagamentoDigital) {
                try {
                    registrarLogCapptaServidorVerificarUsuarioSenhaResponsavelPagamento(pinPad);
                } catch (Exception e){}

                concluirPagamentoComSucesso(pinPad);
            } else {
                setApresentarPagamentoDigital(true);
            }
            setApresentarAlteracaoDataBasePagtoContrato(false);
        } catch (Exception e) {
            if (pinPad != null) {
                gravarLogCappta(pinPad.name() + "|verificarUsuarioSenhaResponsavelPagamento - ERRO", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            setAbrirRichModalMensagem(true);
        } finally {
            setProcessandoOperacao(false);
        }
    }

    private void registrarLogCapptaServidorVerificarUsuarioSenhaResponsavelPagamento(OpcoesPinpadEnum pinPad) {
        //Estamos tendo problemas com a Cappta, onde tem o retorno da Cappta, mas da erro ao Baixar a Parcela, por isso estamos adicionando Logs para tentar encontrar o problema.
        //Pelo tipo de erro no Log do Servidor, imagino que o MovPagamentoVO.contratoVO, está perndendo o código ou ficando null.
        //Quando encontrar o problema, pode retirar esse if
        if (pinPad != null && pinPad.getNome().equals("Cappta")) {
            MovParcelaVO movParcelavo = (MovParcelaVO) getListaValidacaoParcela().get(0);
            Uteis.logarDebug(pinPad.getNome() + " - MovPagamentoControle.verificarUsuarioSenhaResponsavelPagamento - Número do Contrato: " + movParcelavo.getContrato().getCodigo());
        }
    }

    public void setUsuarioLiberacao(UsuarioVO usuario) {
        this.usuarioLiberacao = usuario;
    }

    public void concluirPagamentoComSucesso() {
        concluirPagamentoComSucesso(null);
    }

    public void concluirPagamentoComSucesso(OpcoesPinpadEnum pinPad) {
        try {
            String msggravar = gravar(pinPad);
            if (getEmpresaLogado().getCobrarAutomaticamenteMultaJuros()) {
                setValorMultaJuros(null);
            }
            setAbrirRichModalMensagem(false);
            setSucesso(true);
            setErro(false);
            setMensagem("Pagamento Efetuado com Sucesso");
            setMensagemPagamento("Pagamento Efetuado Com Sucesso. " + msggravar);
            setMensagemDetalhada("");
            setApresentarBotaoRecibo(true);
        } catch (Exception ex) {
            if (pinPad != null) {
                gravarLogCappta(pinPad.name()+"|concluirPagamentoComSucesso - ERRO", ex.getMessage());
            }
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", ex.getMessage());
            setAbrirRichModalMensagem(true);
        }
    }

    public void consultarResponsavelPagamento() {
        try {
            getMovPagamentoVO().setResponsavelPagamento(getFacade().getUsuario().consultarPorChavePrimaria(getMovPagamentoVO().getResponsavelPagamento().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getMovPagamentoVO().getResponsavelPagamento().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("Dados Consultados com Sucesso.");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        //setAbrirRichModalMensagem(true);
    }

    public void consultarResponsavelLiberacao() {
        try {
            getMovContaCorrenteCliente().setResponsavelAutorizacao(getFacade().getUsuario().consultarPorChavePrimaria(getMovContaCorrenteCliente().getResponsavelAutorizacao().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getMovContaCorrenteCliente().getResponsavelAutorizacao().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("Dados Consultados com Sucesso.");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        //setAbrirRichModalContaCorrente(true);
    }

    /**
     * Responsável por pesquisar em banco se já existe pagamento registrado para
     * o contrato do cliente, para informar na impressão do contrato as formas
     * de pagamento. Se não existir, utilizar as formas de pagamento escolhidas
     * na tela
     *
     * @param contrato
     * @throws Exception
     * <AUTHOR> 21/02/2011
     */
    private void montarPagamentos(ContratoVO contrato) throws Exception {
        //pesquisar em banco
        this.setPagamentos(getFacade().getMovPagamento().consultarPagamentoDeUmContrato(contrato.getCodigo().intValue(), false, Uteis.NIVELMONTARDADOS_TODOS));
        //caso o retorno seja vazio, verificar as formas de pagamento escolhidas na tela
        if (this.getPagamentos().isEmpty()) {
            Iterator i = listaSelectItemMovPagamento.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida()) {
                    this.getPagamentos().add(movPagamento);
                }
            }
        }

    }

    public void gerarImpressaoContrato() throws Exception {
        ContratoVO contrato = prepararContratoEPagamento();
        if (contrato.getCodigo() != 0) {
            JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", contrato.getEmpresa());
            String htmlContrato = getFacade().getContratoTextoPadrao().consultarHtmlContrato(contrato.getCodigo(),true);
            HttpServletRequest request = (HttpServletRequest) context().getExternalContext().getRequest();
            request.getSession().setAttribute("textoRelatorio", htmlContrato);
        } else {
            throw new Exception("Não foi possível emitir o relatório. Dados não encontrados!");
        }
    }

    /**
     * Responsável por enviar por email o contrato do cliente
     *
     * <AUTHOR> 06/05/2011
     */
//    public void enviarContrato() {
//        try {
//            ContratoVO contrato = prepararContratoEPagamento();
//            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getFacade().getConfiguracaoSistemaCRM().consultarPorChavePrimaria(1,
//                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            if (UteisValidacao.emptyString(configuracaoSistemaCRMVO.getMailServer())) {
//                throw new Exception("Configure o servidor de envio de e-mail!");
//            }
//            if (contrato.isVendaCreditoTreino()){
//                contrato.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(contrato.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
//            }
//            enviarContrato(contrato, this.getPagamentos(), configuracaoSistemaCRMVO);
//            apresentarSucessoEmail(true);
//            setMensagemDetalhada("");
//            setMensagem("");
//        } catch (Exception e) {
//            apresentarSucessoEmail(false);
//            setMensagemDetalhada("msg_erro", e.getMessage());
//            setSucesso(false);
//            setErro(true);
//        }
//
//    }

    /**
     * Responsável por montar os objetos necessários para impressão/envio do
     * contrato
     *
     * <AUTHOR> 06/05/2011
     */
    private ContratoVO prepararContratoEPagamento() throws Exception {
        //obter contrato
        ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(getCodigoContrato(), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
        //preenccher dados do contrato
        contrato.setContratoTextoPadrao(getFacade().getContratoTextoPadrao().consultarPorCodigoContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contrato.setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(contrato.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contrato.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(contrato.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contrato.setContratoDuracao(getFacade().getContratoDuracao().consultarContratoDuracoes(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contrato.setContratoHorario(getFacade().getContratoHorario().consultarContratoHorarios(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contrato.setResponsavelContrato(getFacade().getUsuario().consultarPorChavePrimaria(contrato.getResponsavelContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        contrato.setContratoModalidadeVOs(getFacade().getContratoModalidade().consultarContratoModalidades(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
        contrato.getPlano().setPlanoTextoPadrao(contrato.getContratoTextoPadrao().getPlanoTextoPadrao());

        //montar as informações das parcelas
        contrato.setMovParcelaVOs(getFacade().getMovParcela().consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        //montar os pagamentos que irão constar no contrato
        montarPagamentos(contrato);
        return contrato;
    }

    public void marcarPagamentoDinheiro() {
        Iterator i = listaSelectItemMovPagamento.iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getFormaPagamento().getDescricao().equals("DINHEIRO")) {
                movPagamento.setMovPagamentoEscolhida(true);
                movPagamento.setValorTotal(getValorResiduoBaseCalculo());
                setTotalLancado(getValorResiduoBaseCalculo());
                setValorResiduoBaseCalculo(new Double(0.0));
            }
        }
    }

    public void marcarPagamentoContaCorrente(boolean marcar) {

        Iterator i = listaSelectItemMovPagamento.iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                movPagamento.setMovPagamentoEscolhida(marcar);
                movPagamento.setValorTotal(marcar ? getMovPagamentoVO().getValorReceberOuDevolverContaCorrente() : 0.0);
                //setTotalLancado(getValorResiduoBaseCalculo());
            }
        }
    }

    public void marcarPagamentoCartaoCreditoAprovaFacil(boolean marcar, double valor) throws Exception {
        Iterator i = listaSelectItemMovPagamento.iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                movPagamento.setMovPagamentoEscolhida(marcar);
                movPagamento.setValorTotal(marcar ? valor : 0.0);
                movPagamento.setUsarPagamentoAprovaFacil(marcar);
                movPagamento.setNrParcelaCartaoCredito(1);
                PagamentoCartaoCreditoControle pagControl =
                        (PagamentoCartaoCreditoControle) JSFUtilities.getFromSession(
                                PagamentoCartaoCreditoControle.class);
                if (pagControl == null) {
                    pagControl = new PagamentoCartaoCreditoControle(this);
                }

                pagControl.prepararControlador(this.getListaValidacaoParcela());
                pagControl.setPessoaPagamento(movPagamento.getPessoa());
                pagControl.obterAutorizacoesCliente();
                pagControl.getDadosPagamento().setParcelas(1);
                if (pagControl != null && pagControl.getAutorizacoes().size() > 0) {
                    pagControl.inicializarCard();
                }
                movPagamento.setNrParcelaCartaoCredito(1);
                try {
                    pagControl.validar();
                } catch (Exception e) {
                    movPagamento.setMovPagamentoEscolhida(false);
                    movPagamento.setValorTotal(0.0);
                    movPagamento.setUsarPagamentoAprovaFacil(false);
                    pagControl = null;
                    throw e;
                }

                pagControl.setMovPagamentoSelecionado(movPagamento);
                pagControl.selecionaOperadoraAuto();

                pagControl.setTrocarCartao(trocaCartao());

                pagControl.setResponsavelPagamento(this.getUsuarioLogado());

                JSFUtilities.storeOnSession(PagamentoCartaoCreditoControle.class.getSimpleName(),
                        pagControl);
                break;
            }
        }
    }

    public PagamentoMovParcelaVO getPagamentoMovParcelaVO() {
        return pagamentoMovParcelaVO;
    }

    public void setPagamentoMovParcelaVO(PagamentoMovParcelaVO pagamentoMovParcelaVO) {
        this.pagamentoMovParcelaVO = pagamentoMovParcelaVO;
    }

    public List getListaSelectItemFormaPagamento() {
        return (listaSelectItemFormaPagamento);
    }

    public void setListaSelectItemFormaPagamento(List listaSelectItemFormaPagamento) {
        this.listaSelectItemFormaPagamento = listaSelectItemFormaPagamento;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public String getCampoConsultaPessoa() {
        return campoConsultaPessoa;
    }

    public void setCampoConsultaPessoa(String campoConsultaPessoa) {
        this.campoConsultaPessoa = campoConsultaPessoa;
    }

    public String getValorConsultaPessoa() {
        return valorConsultaPessoa;
    }

    public void setValorConsultaPessoa(String valorConsultaPessoa) {
        this.valorConsultaPessoa = valorConsultaPessoa;
    }

    public MovimentoContaCorrenteClienteVO getMovContaCorrenteCliente() {
        return movContaCorrenteCliente;
    }

    public void setMovContaCorrenteCliente(MovimentoContaCorrenteClienteVO movContaCorrenteCliente) {
        this.movContaCorrenteCliente = movContaCorrenteCliente;
    }

    public List getListaSelectItemMovPagamento() {
        return listaSelectItemMovPagamento;
    }

    public void setListaSelectItemMovPagamento(List listaSelectItemMovPagamento) {
        this.listaSelectItemMovPagamento = listaSelectItemMovPagamento;
    }

    public Double getTotalLancado() {
        return totalLancado;
    }

    public void setTotalLancado(Double totalLancado) {
        this.totalLancado = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(totalLancado);
    }

    public Double getValorResiduo() {
        if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Devolver) {
            return valorResiduo - getMovPagamentoVO().getValorReceberOuDevolverContaCorrente();
        } else if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Receber) {
            return valorResiduo + getMovPagamentoVO().getValorReceberOuDevolverContaCorrente();
        } else if (getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Nenhum) {
            //se não é pra fazer nada, então voltar o valor original do resíduo de movPagamento
            return valorResiduo;
        }
        return valorResiduo;
    }

    public void setValorResiduo(Double valorResiduo) {
        if (valorResiduo != null) {
            this.valorResiduo = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorResiduo);
        } else {
            this.valorResiduo = valorResiduo;
        }
    }

    public ChequeVO getCheque() {
        return cheque;
    }

    public void setCheque(ChequeVO cheque) {
        this.cheque = cheque;
    }

    public Double getValorResiduoBaseCalculo() {
        return valorResiduoBaseCalculo;
    }

    public void setValorResiduoBaseCalculo(Double valorResiduoBaseCalculo) {
        this.valorResiduoBaseCalculo =
                Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorResiduoBaseCalculo);
    }

    public Boolean getAutorizar() {
        return autorizar;
    }

    public void setAutorizar(Boolean autorizar) {
        this.autorizar = autorizar;
    }

    public Boolean getAbrirRichModalContaCorrente() {
        return abrirRichModalContaCorrente;
    }

    public void setAbrirRichModalContaCorrente(Boolean abrirRichModalContaCorrente) {
        this.abrirRichModalContaCorrente = abrirRichModalContaCorrente;
    }

    public Boolean getAbrirRichModalCheque() {
        return abrirRichModalCheque;
    }

    public void setAbrirRichModalCheque(Boolean abrirRichModalCheque) {
        this.abrirRichModalCheque = abrirRichModalCheque;
    }

    public List getListaValidacaoParcela() {
        return listaValidacaoParcela;
    }

    public void setListaValidacaoParcela(List listaValidacaoParcela) {
        this.listaValidacaoParcela = listaValidacaoParcela;
    }

    public ChequeVO getChequeClone() {
        return chequeClone;
    }

    public void setChequeClone(ChequeVO chequeClone) {
        this.chequeClone = chequeClone;
    }

    public Boolean getAbrirRichModalMensagem() {
        return abrirRichModalMensagem;
    }

    public void setAbrirRichModalMensagem(Boolean abrirRichModalMensagem) {
        this.abrirRichModalMensagem = abrirRichModalMensagem;
    }

    public Boolean getApresentarCheque() {
        return apresentarCheque;
    }

    public void setApresentarCheque(Boolean apresentarCheque) {
        this.apresentarCheque = apresentarCheque;
    }

    public Boolean getAbrirRichModalConfirmacaoPagamento() {
        return abrirRichModalConfirmacaoPagamento;
    }

    public void setAbrirRichModalConfirmacaoPagamento(Boolean abrirRichModalConfirmacaoPagamento) {
        this.abrirRichModalConfirmacaoPagamento = abrirRichModalConfirmacaoPagamento;
    }

    public String getMensagemPagamento() {
        if (mensagemPagamento == null) {
            mensagemPagamento = "";
        }
        return mensagemPagamento;
    }

    public void setMensagemPagamento(String mensagemPagamento) {
        this.mensagemPagamento = mensagemPagamento;
    }

    public Boolean getReceberTroco() {
        return receberTroco;
    }

    public void setReceberTroco(Boolean receberTroco) {
        this.receberTroco = receberTroco;
    }

    public Boolean getApresentarBotoesControle() {
        return apresentarBotoesControle;
    }

    public void setApresentarBotoesControle(Boolean apresentarBotoesControle) {
        this.apresentarBotoesControle = apresentarBotoesControle;
    }

    public Boolean getApresentarBotaoImprimirContrato() {
        return apresentarBotaoImprimirContrato;
    }

    public void setApresentarBotaoImprimirContrato(Boolean apresentarBotaoImprimirContrato) {
        this.apresentarBotaoImprimirContrato = apresentarBotaoImprimirContrato;
    }

    public Boolean getApresentarBotaoImprimirContratoServico() {
        return apresentarBotaoImprimirContratoServico;
    }

    public void setApresentarBotaoImprimirContratoServico(Boolean apresentarBotaoImprimirContratoServico) {
        this.apresentarBotaoImprimirContratoServico = apresentarBotaoImprimirContratoServico;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public String getNomePagador() {
        if (nomePagador == null) {
            nomePagador = "";
        }
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        movPagamentoVO = null;
        listaSelectItemFormaPagamento = new ArrayList();

        listaSelectItemMovPagamento = new ArrayList();
        pagamentoMovParcelaVO = null;
        movContaCorrenteCliente = null;
        cheque = null;
        codigoContrato = null;
        apresentarBotaoImprimirContrato = null;
        campoConsultaPessoa = null;
        valorConsultaPessoa = null;
        valorResiduo = null;
        valorResiduoBaseCalculo = null;
        totalLancado = null;
        autorizar = null;
        /*Essa Lista não pode ser limpada pois ela recebe seu obj do Controle MovParcelaControle
         listaValidacaoParcela.clear();
         */
        chequeClone = null;
        abrirRichModalContaCorrente = null;
        abrirRichModalCheque = null;
        abrirRichModalMensagem = null;
        abrirRichModalConfirmacaoPagamento = null;
        mensagemPagamento = null;
        nomePagador = null;

    }

    public boolean isApresentarBotaoRecibo() {
        return apresentarBotaoRecibo;
    }

    public void setApresentarBotaoRecibo(boolean apresentarBotaoRecibo) {
        this.apresentarBotaoRecibo = apresentarBotaoRecibo;
    }

    public void setListaSelectItemReceberOuDevolver(List listaSelectItemReceberOuDevolver) {
        this.listaSelectItemReceberOuDevolver = listaSelectItemReceberOuDevolver;
    }

    public List getListaSelectItemReceberOuDevolver() {
        if ((listaSelectItemReceberOuDevolver == null)
                || (listaSelectItemReceberOuDevolver.isEmpty())) {
            listaSelectItemReceberOuDevolver = new ArrayList();

            listaSelectItemReceberOuDevolver.add(new SelectItem(
                    TipoOperacaoContaCorrenteEnum.TOCC_Nenhum,
                    TipoOperacaoContaCorrenteEnum.TOCC_Nenhum.getDescricao()));

            if (getMovContaCorrenteCliente().getSaldoAtual() > 0) {
                listaSelectItemReceberOuDevolver.add(new SelectItem(
                        TipoOperacaoContaCorrenteEnum.TOCC_Devolver,
                        TipoOperacaoContaCorrenteEnum.TOCC_Devolver.getDescricao()));
            } else if (getMovContaCorrenteCliente().getSaldoAtual() < 0) {
                listaSelectItemReceberOuDevolver.add(new SelectItem(
                        TipoOperacaoContaCorrenteEnum.TOCC_Receber,
                        TipoOperacaoContaCorrenteEnum.TOCC_Receber.getDescricao()));
            }
        }
        return listaSelectItemReceberOuDevolver;
    }

    public void setTipoOperacaoContaCorrenteEnum(TipoOperacaoContaCorrenteEnum tipoOperacaoContaCorrenteEnum) {
        this.tipoOperacaoContaCorrenteEnum = tipoOperacaoContaCorrenteEnum;
    }

    public TipoOperacaoContaCorrenteEnum getTipoOperacaoContaCorrenteEnum() {
        return tipoOperacaoContaCorrenteEnum;
    }

    public void validarOperacaoContaCorrente(FacesContext context,
                                             UIComponent validate, Object value) {

        if (!(getTipoOperacaoContaCorrenteEnum() == TipoOperacaoContaCorrenteEnum.TOCC_Nenhum)) {
            Double valor = 0.0;
            if (value.getClass() == Double.class) {
                valor = (Double) value;
            }
            if (valor != 0) {
                if (valor > this.valorResiduo.doubleValue()) {
                    //((UIInput) validate).setValid(false);
                    FacesMessage msg = new FacesMessage(
                            "Por favor, o valor nao pode ser superior a " + this.valorResiduo);
                    context.addMessage(validate.getClientId(context), msg);
                    return;
                }

                if ((valor < 0) || (valor > Math.abs(getSaldoInicial()))) {
                    //((UIInput) validate).setValid(false);
                    FacesMessage msg = new FacesMessage(
                            "Por favor, o valor deve estar entre " + 0 + " e "
                                    + Math.abs(getSaldoInicial()));
                    context.addMessage(validate.getClientId(context), msg);
                    return;
                }
            }
        }


    }

    public boolean isApresentarFormaPagamentoContaCorrente() {
        try {
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamentoVO = (MovPagamentoVO) i.next();
                if (movPagamentoVO != null &&
                        movPagamentoVO.getTipoPagador() != null &&
                        (movPagamentoVO.getTipoPagador().equalsIgnoreCase("CN")
                                || movPagamentoVO.getTipoPagador().equalsIgnoreCase("CO"))) {
                    //para colaborador ou consumidor não deve apresentar conta corrente
                    return false;
                }
            }
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return true;
        }
    }

    public boolean isApresentarFormasPagamento() {

        boolean retorno = ((getTipoOperacaoContaCorrenteEnum() != TipoOperacaoContaCorrenteEnum.TOCC_Devolver)
                && (!isApresentarBotaoRecibo()))
                || ((!isApresentarBotaoRecibo()) && (getValorResiduo().doubleValue() != 0.0));

        return retorno && !getMovPagamentoVO().getUsarPagamentoAprovaFacil() && !getMovPagamentoVO().getUsarPagamentoDebitoOnline() && !getProcessandoOperacao();
    }

    public boolean getApresentarMensagens() {
        return getErro() || getSucesso()
                || (!getMensagemDetalhada().isEmpty());
    }

    public void atualizaNrParcelasOperadora() throws Exception {
        // monta a lista de parcelas de acordo com a operadora
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        if (obj != null) {
            montarListaNrParcelasCartao(obj.getOperadoraCartaoVO());
        }
    }

    public void atualizarBancoEvent(ActionEvent evt) {
        try {
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            if (obj != null) {
                obj.setBanco(getFacade().getBanco().consultarCodigoBanco(obj.getBanco().getCodigoBanco(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void atualizarBanco() {
        try {
            ChequeVO obj = (ChequeVO) context().getExternalContext().getRequestMap().get("cheque");
            if (obj != null) {
                obj.setBanco(getFacade().getBanco().consultarCodigo(obj.getBanco().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void atualizarTipoPagamentoStone(ValueChangeEvent evt) throws Exception {
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        if (obj.getMovPagamentoEscolhida() && obj.getOperadoraCartaoVO().getCodigoIntegracaoStoneOnline() != null) {
            PagamentoCartaoCreditoControle pagControl = (PagamentoCartaoCreditoControle) JSFUtilities.getFromSession(PagamentoCartaoCreditoControle.class);

            if (pagControl != null && pagControl.getDadosPagamento() != null && obj.getNrParcelaCartaoCredito() > 0) {
                pagControl.getDadosPagamento().setTipoParcelamentoStone(InstalmentTypeInstlmtTp.fromValue(evt.getNewValue().toString()));
//                validarPagamentoEMostrarTelaConfirmacao(pagControl, obj, obj.getNrParcelaCartaoCredito()); //Não validar devido a mudanças no ticket M2-2473 que estamos identificando os impactos
            }
        }
    }

    public void pagamentoOnline() {
        try {
            if (validarExisteRecebimentoPinpad()) {
                throw new Exception("Não é possível realizar o pagamento online, pois já existe recebimento via Pinpad.");
            }

            setClicouBotaoPagOnline(true);
            limparMsg();

            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            obj.setValidarOperadora(false);
            obj.setValidarNrVezes(false);

            Integer nrParcelas = 0;

            PagamentoCartaoCreditoControle pagControl = (PagamentoCartaoCreditoControle) JSFUtilities.getFromSession(PagamentoCartaoCreditoControle.class);
            validarParceiroFidelidade(obj);
            obj.setNrParcelaCartaoCredito(nrParcelas);
            this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
            this.getMovPagamentoVO().setUsarPagamentoDigital(false);
            validarDadosPagamento();
//            validarValoresPagamentoOnLine(obj); //Não validar devido a mudanças no ticket M2-2473 que estamos identificando os impactos
            pagControl.prepararControlador(this.getListaValidacaoParcela());
            pagControl.getDadosPagamento().setParcelas(nrParcelas);
            pagControl.setValorPagar(obj.getValorTotal());
            pagControl.setMovPagamentoSelecionado(obj);
            pagControl.setPessoaPagamento(obj.getPessoa());
            pagControl.setResponsavelPagamento(getUsuarioLogado());

            Integer cliente = getFacade().getCliente().obterCodigoClientePorPessoa(pagControl.getPessoaPagamento().getCodigo());
            pagControl.setLabelColaborador(UteisValidacao.emptyNumber(cliente));

            pagControl.obterAutorizacoesCliente();

            pagControl.validar();
            JSFUtilities.storeOnSession(PagamentoCartaoCreditoControle.class.getSimpleName(), pagControl);
            this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(true);
            if ((pagControl.getAutorizacoes() != null && pagControl.getAutorizacoes().size() > 0) || (pagControl.getAutorizacoesColaborador() != null && pagControl.getAutorizacoesColaborador().size() > 0)) {
                pagControl.inicializarCard();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            montarErro(ex);
        }
    }

    private boolean validarExisteRecebimentoPinpad() {
        boolean existeRecebimentoPinpad = false;
        if (!UteisValidacao.emptyList(getListaSelectItemMovPagamento())) {
            for (Object obj : getListaSelectItemMovPagamento()) {
                MovPagamentoVO mp = (MovPagamentoVO) obj;
                if (mp.getMovPagamentoEscolhida() != null && mp.getMovPagamentoEscolhida() && !UteisValidacao.emptyString(mp.getObservacao()) &&
                    !UteisValidacao.emptyString(mp.getNumeroUnicoTransacao()) && !UteisValidacao.emptyString(mp.getRespostaRequisicaoPinpad())) {
                    existeRecebimentoPinpad = true;
                    break;
                }
            }
        }
        return existeRecebimentoPinpad;
    }

    public void atualizarOperadoraCartao(ValueChangeEvent evt) {
        setMensagemPagamento("");
        setMensagemDetalhada("");
        limparMsg();
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        if (evt.getComponent().getClass() == HtmlSelectBooleanCheckbox.class) {
            Boolean valor = (Boolean) evt.getNewValue();
            if (evt.getComponent().getId().equals("chkUsarPagamentoDigital")) {
                this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
                this.getMovPagamentoVO().setUsarPagamentoDigital(false);
                this.setApresentarPagamentoDigital(valor);
            }
        } else if (evt.getComponent().getClass() == HtmlSelectOneMenu.class) {
            try {
//                PagamentoCartaoCreditoControle pagControl = (PagamentoCartaoCreditoControle) JSFUtilities.getFromSession(PagamentoCartaoCreditoControle.class);
//                Integer nrParcelas;

                //operadoraCartao quando muda a combo de oepradora
                if (evt.getComponent().getId().equals("operadoraCartao")) {
                    Integer codOper = (Integer) evt.getNewValue();
//                    nrParcelas = obj.getNrParcelaCartaoCredito();
                    obj.setOperadoraCartaoVO(new OperadoraCartaoVO());
                    if (codOper != 0) {
                        obj.setOperadoraCartaoVO(getFacade().getOperadoraCartao().consultarPorChavePrimaria(
                                codOper, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }

                    this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);

//                    //verifica se já possui o numero de parcelas selecionado e já habilita formulário inferior
//                    //para informação dos dados de cartão de crédito (Número, titular, validade, etc);
//                    if (obj.getMovPagamentoEscolhida() && getEmpresa().isIrTelaPagamentoCartaoCreditoFormaPagamento() && isPermitePeloCodigoIntegracaoHabilitado(obj, nrParcelas)
//                            && (obj.getNrParcelaCartaoCredito() > 0)) {
//                        validarPagamentoEMostrarTelaConfirmacao(pagControl, obj, nrParcelas);
//                    } else {
//                        this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
//                    }

                } else if (evt.getComponent().getId().equals("nrParcelaCartao")) { //nrParcelaCartao quando muda a combo de parcelas
                    this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);

//                    nrParcelas = Integer.valueOf(evt.getNewValue().toString());
//                    if (obj.getMovPagamentoEscolhida() && getEmpresa().isIrTelaPagamentoCartaoCreditoFormaPagamento() && isPermitePeloCodigoIntegracaoHabilitado(obj, nrParcelas)) {
//                        validarPagamentoEMostrarTelaConfirmacao(pagControl, obj, nrParcelas);
//                    } else {
//                        this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
//                    }
                } else if (evt.getComponent().getId().equals("nrParcelasAPF") ||
                        evt.getComponent().getId().equals("nrParcelasVindi") ||
                        evt.getComponent().getId().equals("nrParcelasCielo")) {
                    List<MovPagamentoVO> lista = getListaSelectItemMovPagamento();
                    for (MovPagamentoVO mpVO : lista) {
                        if (mpVO.getMovPagamentoEscolhida() && mpVO.getFormaPagamento().getTipoFormaPagamento().equals("CA")) {
                            mpVO.setNrParcelaCartaoCredito(new Integer(evt.getNewValue().toString()));
                            break;
                        }
                    }
                }
            } catch (Exception ex) {
                this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
                setMensagemDetalhada(ex.getMessage());
                if (ex.getMessage() != null && ex.getMessage().contains(" necessario vincular")) {
                    montarAviso(ex.getMessage());
                } else {
                    montarErro(ex.getMessage());
                }
            }
        }
    }

    //Não validar devido a mudanças no ticket M2-2473 que estamos identificando os impactos
//    private void validarPagamentoEMostrarTelaConfirmacao(PagamentoCartaoCreditoControle pagControl, MovPagamentoVO obj, Integer nrParcelas) throws Exception {
//        validarParceiroFidelidade(obj);
//        obj.setNrParcelaCartaoCredito(nrParcelas);
//        this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
//        this.getMovPagamentoVO().setUsarPagamentoDigital(false);
//        validarDadosPagamento();
//        validarValoresPagamentoOnLine(obj);
//        if (nrParcelas > 0) {
//            pagControl.prepararControlador(this.getListaValidacaoParcela());
//            pagControl.getDadosPagamento().setParcelas(nrParcelas);
//            pagControl.setValorPagar(obj.getValorTotal());
//            pagControl.setMovPagamentoSelecionado(obj);
//            pagControl.setPessoaPagamento(obj.getPessoa());
//            pagControl.obterAutorizacoesCliente();
//            pagControl.setResponsavelPagamento(getUsuarioLogado());
//
//            pagControl.validar();
//            JSFUtilities.storeOnSession(PagamentoCartaoCreditoControle.class.getSimpleName(), pagControl);
//            this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(true);
//        }
//    }

    private boolean isPermitePeloCodigoIntegracaoHabilitado(MovPagamentoVO obj, Integer nrParcelas) {
        return obj.isPermitePeloCodigoIntegracaoHabilitado(nrParcelas);
    }

    public void montarListaNrParcelasCartao(OperadoraCartaoVO obj) throws Exception {
        setListaSelectItemNrParcelaCartao(new ArrayList<SelectItem>());
        getListaSelectItemNrParcelaCartao().add(new SelectItem(0, "(Selecionar)"));
        for (int i = 1; i <= obj.getQtdeMaxParcelas(); i++) {
            if (i == 1) {
                getListaSelectItemNrParcelaCartao().add(new SelectItem(i, "À Vista"));
            } else {
                getListaSelectItemNrParcelaCartao().add(new SelectItem(i, i + " vezes"));
            }
        }
    }

    public void montarListaDia() throws Exception {
        setListaSelectDiasMes(new ArrayList<SelectItem>());
        for (int i = 1; i <= 31; i++) {
            getListaSelectDiasMes().add(new SelectItem(i, String.valueOf(i)));
        }
    }

    public String getTokenPagamentoDigital() {
        return super.getValorCampoConfiguracaoSistema("tokencontapagdigital",
                (String) JSFUtilities.getFromSession("key"));
    }

    public String getEmailContaPagamentoDigital() {
        return super.getValorCampoConfiguracaoSistema("emailcontapagdigital",
                (String) JSFUtilities.getFromSession("key"));
    }

    public List getListaSelectItemConvenioCobranca() {
        return listaSelectItemConvenioCobranca;
    }

    public void setCodigoContratoEvento(Integer codigoContratoEvento) {
        this.codigoContratoEvento = codigoContratoEvento;
    }

    public Integer getCodigoContratoEvento() {
        if (this.codigoContratoEvento == null) {
            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
            //criar uma lista que irá manter momentaneamente os objetos movparcela
            List<MovParcelaVO> parcelasSemContrato = new ArrayList<MovParcelaVO>();
            if (movParcelaControle.getListaParcelasPagar() != null) {
                MovParcelaVO parcela = (MovParcelaVO) movParcelaControle.getListaParcelasPagar().get(0);
                setCodigoContratoEvento(parcela.getContrato().getCodigo());
            }
        }
        return codigoContratoEvento;
    }

    public void setListaSelectItemConvenioCobranca(List listaSelectItemConvenioCobranca) {
        this.listaSelectItemConvenioCobranca = listaSelectItemConvenioCobranca;
    }

    public Double getTotalDivida() {
        return totalDivida;
    }

    public void setTotalDivida(Double totalDivida) {
        if (totalDivida != null) {
            this.totalDivida = Uteis.arredondarForcando2CasasDecimaisMantendoSinal(totalDivida);
        } else {
            this.totalDivida = totalDivida;
        }
    }

    //------------------------------------Métodos do Central de Eventos---------------------------------------------//
    private Integer codigoContratoEvento;

    /**
     * Método responsável por fazer o pagamento de parcela de eventos do Central
     * de Eventos.
     *
     * @throws Exception
     * <AUTHOR>
     */
    public String pagamentoParcelaCE(){
        return pagamentoParcelaCE(true);
    }

    public String pagamentoParcelaCE(boolean verificaLoginUsuario) {
        try {
            if(verificaLoginUsuario) {
                getFacade().getControleAcesso().verificarLoginUsuario(
                    this.getMovPagamentoVO().getResponsavelPagamento().getUsername(),
                    this.getMovPagamentoVO().getResponsavelPagamento().getSenha().toUpperCase());
            }
            pagarParcelasEvento();
            CadastroInicialControle cadastro = (CadastroInicialControle) JSFUtilities.getFromSession(CadastroInicialControle.class.getSimpleName());
            if (cadastro == null) {
                cadastro = new CadastroInicialControle();
            }
            cadastro.abrirDetalhamento();
            cadastro.setMensagemID("operacoes.parcelaPaga");
            JSFUtilities.storeOnSession(CadastroInicialControle.class.getSimpleName(), cadastro);
            return "detalhamentoEvento";
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            setErro(true);
            return "pagamentoCE";
        }
    }

    public void listenerPagamentoCE(ActionEvent evt) {
        try {
            super.autorizacao(evt);
            CadastroInicialControle cadastro = (CadastroInicialControle) JSFUtilities.getFromSession(CadastroInicialControle.class.getSimpleName());
            if (cadastro == null) {
                cadastro = new CadastroInicialControle();
            }
            cadastro.selCadastroInicialListener(evt);
            JSFUtilities.storeOnSession(CadastroInicialControle.class.getSimpleName(), cadastro);
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
            setErro(true);
        }
    }

    public void montarListaSelectItemBanco() {
        try {
            montarListaSelectItemBanco("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Nao foi Possivel Montar a lista de Bancos.");
            setListaSelectItemBanco(new ArrayList<SelectItem>());
        }
    }

    public void montarListaSelectItemBanco(String prm) throws Exception {
        List resultadoConsulta = consultarBancoPorDescricao(prm);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            BancoVO obj = (BancoVO) i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemBanco(objs);
    }

    public List consultarBancoPorDescricao(String descricaoPrm) throws Exception {
        List lista = getFacade().getBanco().consultarPorNome(descricaoPrm, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    /**
     * responsavel por encapsular as operaçoes do pagamento de parcelas do
     * evento
     *
     * @throws Exception
     */
    public void pagarParcelasEvento() throws Exception {


        setCodigoContratoEvento(0);
        //pegar na sessão o controle de movparcela
        MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        //criar uma lista que irá manter momentaneamente os objetos movparcela
        List<MovParcelaVO> parcelasSemContrato = new ArrayList<MovParcelaVO>();
        if (movParcelaControle.getListaParcelasPagar() != null) {
            MovParcelaVO parcela = (MovParcelaVO) movParcelaControle.getListaParcelasPagar().get(0);
            setCodigoContratoEvento(parcela.getContrato().getCodigo());
        }

        //percorrer a lista de parcelas a pagar
        for (Object obj : movParcelaControle.getListaParcelasPagar()) {
            MovParcelaVO parcela = (MovParcelaVO) obj;

            //retirar do atributo contrato da parcela o codigo do contrato, para evitar que ele seja gravado na tabela movparcela
            //pois esse código não é um código de contrato e sim de negociacaoeventocontrato
            parcela.getContrato().setCodigo(null);
            //adicionar à lista
            parcelasSemContrato.add(parcela);
        }
        //setar a lista criada neste método como a lista de parcelas a pagar
        movParcelaControle.setListaParcelasPagar(parcelasSemContrato);
        //colocar o objeto pagamentoParcelaCE alterado na sessão
        JSFUtilities.storeOnSession("MovParcelaControle", movParcelaControle);

        //identificar quem vai para o log
        Map<Integer, Object> pagamentosARegistrar = new HashMap<Integer, Object>();
        int codLog = 0;
        Iterator i = listaSelectItemMovPagamento.iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            if (movPagamento.getMovPagamentoEscolhida()) {
                pagamentosARegistrar.put(codLog, movPagamento);
            }
        }
        //realizar as operações de pagamento do zillyon
        verificarUsuarioSenhaResponsavelPagamento(false, null);
        if (getErro()) {
            throw new Exception(getMensagemDetalhada());
        }
        //salvar o relacionamento entre o movpagamento e o contrato do evento
        getFacade().getMovPagamento().salvarPagamentoContratoEvento(getCodigoContratoEvento());
        //salvar o relacionamento entre o recibo e o contrato do evento
        getFacade().getReciboPagamento().salvarPagamentoContratoEvento(getCodigoContratoEvento());

        registrarLogCE(pagamentosARegistrar);
    }

    /**
     * @throws Exception
     * <AUTHOR> 31/03/2011
     */
    private void registrarLogCE(Map<Integer, Object> pagamentosARegistrar) throws Exception {
        Set<Integer> chaves = pagamentosARegistrar.keySet();
        for (Integer key : chaves) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) pagamentosARegistrar.get(key);
            //LOG - INICIO
            try {

                movPagamento.setObjetoVOAntesAlteracao(new MovPagamentoVO());
                movPagamento.setNovoObj(true);
                registrarLogObjetoVO(movPagamento, movPagamento.getCodigo(), "PAGAMENTO CE", movPagamento.getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("PAGAMENTO CE", movPagamento.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PAGAMENTO", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
        }

        //registrar logs

    }

    @Override
    public void fecharConfirmacao() {
        setApresentarSucessoEmail(Boolean.FALSE);
    }

    public Date getDataAuxiliar() {
        return dataAuxiliar;
    }

    public void setDataAuxiliar(Date dataAuxiliar) {
        this.dataAuxiliar = dataAuxiliar;
    }

    public Date getDataPagto() {
        return dataPagto;
    }

    public String getDataPagto_Apresentar() {
        return Uteis.getData(dataPagto);
    }

    public void setDataPagto(Date dataPagto) {
        this.dataPagto = dataPagto;
    }

    public UsuarioVO getResponsavelDataPagto() {
        return responsavelDataPagto;
    }

    public void setResponsavelDataPagto(UsuarioVO responsavelDataPagto) {
        this.responsavelDataPagto = responsavelDataPagto;
    }

    public boolean isAutorizadoDataPagto() {
        return autorizadoDataPagto;
    }

    public void setAutorizadoDataPagto(boolean autorizadoDataPagto) {
        this.autorizadoDataPagto = autorizadoDataPagto;
    }

    public boolean isApresentarAlteracaoDataBasePagtoContrato() {
        return apresentarAlteracaoDataBasePagtoContrato;
    }

    public void setApresentarAlteracaoDataBasePagtoContrato(boolean apresentarAlteracaoDataBasePagtoContrato) {
        this.apresentarAlteracaoDataBasePagtoContrato = apresentarAlteracaoDataBasePagtoContrato;
    }

    public void escolherOutraFormaPagamento() {
        try {
            PagamentoCartaoCreditoControle control = (PagamentoCartaoCreditoControle) JSFUtilities.getFromSession(PagamentoCartaoCreditoControle.class);
            if (control != null && control.getMovPagamentoSelecionado() != null) {
                control.getMovPagamentoSelecionado().setMovPagamentoEscolhida(false);
                control.getMovPagamentoSelecionado().setOperadoraCartaoVO(null);
                control.getMovPagamentoSelecionado().setValorTotal(0.0);
                control.getMovPagamentoSelecionado().setNrParcelaCartaoCredito(0);
                this.limparFormasEscolhidas();
                this.restaurarDivida();
            }
        } catch (Exception ignored) {
        }

        try {
            PagamentoCartaoDebitoOnlineControle control = (PagamentoCartaoDebitoOnlineControle) JSFUtilities.getFromSession(PagamentoCartaoDebitoOnlineControle.class);
            if (control != null && control.getMovPagamentoSelecionado() != null) {
                control.getMovPagamentoSelecionado().setMovPagamentoEscolhida(false);
                control.getMovPagamentoSelecionado().setOperadoraCartaoVO(null);
                control.getMovPagamentoSelecionado().setValorTotal(0.0);
                control.getMovPagamentoSelecionado().setNrParcelaCartaoCredito(0);
                this.limparFormasEscolhidas();
                this.restaurarDivida();
            }
        } catch (Exception ignored) {
        }
        getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
        getMovPagamentoVO().setUsarPagamentoDebitoOnline(false);
    }

    public ReciboPagamentoVO getReciboObj() {
        return reciboObj;
    }

    public void setReciboObj(ReciboPagamentoVO reciboObj) {
        this.reciboObj = reciboObj;
    }

    public List<SelectItem> getListaSelectItemNrParcelaCartaoVindiOuCielo() {
        List<SelectItem> lista = new ArrayList<>();
        for (int i = 1; i <= 12; i++) {
            if (i == 1) {
                lista.add(new SelectItem(i, "À Vista"));
            } else {
                lista.add(new SelectItem(i, i + " vezes"));
            }
        }
        return lista;
    }

    public List<SelectItem> getListaSelectItemNrParcelaCartao() throws Exception {
        return listaSelectItemNrParcelaCartao;
    }

    public List<SelectItem> getNrParcelas() throws Exception {
        if (UteisValidacao.emptyList(listaSelectItemNrParcelas)) {
            listaSelectItemNrParcelas.add(new SelectItem("1", "À vista"));
            for (int i = 2; i <= 12; i++) {
                listaSelectItemNrParcelas.add(new SelectItem(i, new Integer(i).toString()));
            }
        }
        return listaSelectItemNrParcelas;
    }

    public void setListaSelectItemNrParcelaCartao(List<SelectItem> listaSelectItemNrParcelaCartao) {
        this.listaSelectItemNrParcelaCartao = listaSelectItemNrParcelaCartao;
    }

    public List<SelectItem> getListaSelectDiasMes() throws Exception {
        return listaSelectDiasMes;
    }

    public void setListaSelectDiasMes(List<SelectItem> listaSelectDiasMes) throws Exception {
        this.listaSelectDiasMes = listaSelectDiasMes;
    }

    public List<SelectItem> getListaSelectItemOperadoraCartaoCredito() {
        return listaSelectItemOperadoraCartaoCredito;
    }

    public void setListaSelectItemOperadoraCartaoCredito(List<SelectItem> listaSelectItemOperadoraCartaoCredito) {
        this.listaSelectItemOperadoraCartaoCredito = listaSelectItemOperadoraCartaoCredito;
    }

    public List<SelectItem> getListaSelectItemOperadoraCartaoDebito() {
        return listaSelectItemOperadoraCartaoDebito;
    }

    public void setListaSelectItemOperadoraCartaoDebito(List<SelectItem> listaSelectItemOperadoraCartaoDebito) {
        this.listaSelectItemOperadoraCartaoDebito = listaSelectItemOperadoraCartaoDebito;
    }

    public int getQtdeCheques() {
        return qtdeCheques;
    }

    public void setQtdeCheques(int qtdeCheques) {
        this.qtdeCheques = qtdeCheques;
    }

    /**
     * este método é necessário pois o indexOf do arraylist não consegue
     * localizar um objeto a partir de uma string
     */
    private int encontraMesReferencia(List<ParcelasPorMes> mesesReferencia, String strMesReferencia) {
        int ret = 0;
        for (ParcelasPorMes ppp : mesesReferencia) {
            if (ppp.getReferencia().equals(strMesReferencia)) {
                return ret;
            }
            ret++;
        }
        return -1;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public void setPermitirCalcularJuros(boolean permitirCalcularJuros) {
        this.permitirCalcularJuros = permitirCalcularJuros;
    }

    public boolean isPermitirCalcularJuros() {
        return permitirCalcularJuros;
    }

    public List<MovParcelaVO> getParcelasMultasJuros() {
        return parcelasMultasJuros;
    }

    public void setParcelasMultasJuros(List<MovParcelaVO> parcelasMultasJuros) {
        this.parcelasMultasJuros = parcelasMultasJuros;
    }

    public List<SelectItem> getListaSelectItemOperadoraCartaoDebitoOnline() {
        if (listaSelectItemOperadoraCartaoDebitoOnline == null) {
            listaSelectItemOperadoraCartaoDebitoOnline = new ArrayList<SelectItem>();
        }
        return listaSelectItemOperadoraCartaoDebitoOnline;
    }

    public void setListaSelectItemOperadoraCartaoDebitoOnline(List<SelectItem> listaSelectItemOperadoraCartaoDebitoOnline) {
        this.listaSelectItemOperadoraCartaoDebitoOnline = listaSelectItemOperadoraCartaoDebitoOnline;
    }

    public void montarListaSelectItemOperadoraCartaoDebitoOnline(MovPagamentoVO movPagamentoVO) throws Exception {
        if (movPagamentoVO.getFormaPagamento().getTipoDebitoOnline() == null ||
                movPagamentoVO.getFormaPagamento().getTipoDebitoOnline().equals(TipoDebitoOnlineEnum.NENHUM)) {
            return;
        }
        setListaSelectItemOperadoraCartaoDebitoOnline(new ArrayList<SelectItem>());
        List resultadoConsulta = getFacade().getOperadoraCartao().consultarPorTipoDebitoOnline(movPagamentoVO.getFormaPagamento().getTipoDebitoOnline(), Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        getListaSelectItemOperadoraCartaoDebitoOnline().add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            OperadoraCartaoVO obj = (OperadoraCartaoVO) i.next();
            getListaSelectItemOperadoraCartaoDebitoOnline().add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
        }
    }

    private void validarPagamentoChequeDevolvido(List<MovParcelaVO> listaParcelas) throws ConsistirException {
        // o sistema deve proibir o uso da conta corrente do cliente para pagar produtos de devolução de cheques
        if (!getTipoOperacaoContaCorrenteEnum().equals(tipoOperacaoContaCorrenteEnum.TOCC_Devolver)) {
            Iterator<MovPagamentoVO> i = listaSelectItemMovPagamento.iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = i.next();
                if (movPagamento.getMovPagamentoEscolhida() && movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
                    for (MovParcelaVO parcela : listaParcelas) {
                        try {
                            if (getFacade().getMovParcela().parcelaPagaChequeDevolvido(parcela.getCodigo())) {
                                throw new ConsistirException("Não é permitido quitar parcela(s) de cheque(s) devolvido(s) deixando débito na conta corrente do aluno. A indicação é utilizar a renegociação de parcelas, dividindo em um número de parcelas maior e em valores que serão quitados a cada pagamento");
                            }
                        } catch (ConsistirException ce) {
                            throw ce;
                        } catch (Exception ignored) {
                        }
                    }
                }
            }
        }
    }

    //Não validar devido a mudanças no ticket M2-2473 que estamos identificando os impactos
//    private void validarValoresPagamentoOnLine(MovPagamentoVO pagamentoSelecionado) throws Exception {
//        if (pagamentoSelecionado.getValorTotal() < getTotalDivida()) {
//
//            throw new Exception("Operadora de cartão informada está configurada para pagamentos com transações online. O valor total do pagamento deve ser quitado apenas pela transação, não sendo permitido mais de uma forma de pagamento");
//        }
//    }

    private void validarValoresPagamentoPix(Double valor) throws Exception {
        if (valor < getTotalDivida()) {
            throw new Exception("Pagamento com pix não pode ser utilizado com outra forma de pagamento");
        }
    }

    public String getTotalPontosParceiroFidelidade_Apresentar() {
        Integer total = getTotalPontosParceiroFidelidade().intValue();
        return total.toString();
    }

    public Double getTotalPontosParceiroFidelidade() {
        if (totalPontosParceiroFidelidade == null) {
            totalPontosParceiroFidelidade = 0.0;
        }
        return totalPontosParceiroFidelidade;
    }

    public void setTotalPontosParceiroFidelidade(Double totalPontosParceiroFidelidade) {
        this.totalPontosParceiroFidelidade = totalPontosParceiroFidelidade;
    }

    public List<MovPagamentoVO> getListaPagamentosParceiroFidelidade() {
        if (listaPagamentosParceiroFidelidade == null) {
            listaPagamentosParceiroFidelidade = new ArrayList<MovPagamentoVO>();
        }
        return listaPagamentosParceiroFidelidade;
    }

    public void setListaPagamentosParceiroFidelidade(List<MovPagamentoVO> listaPagamentosParceiroFidelidade) {
        this.listaPagamentosParceiroFidelidade = listaPagamentosParceiroFidelidade;
    }

    public void abrirCadastroConvenio() {
        ConvenioCobrancaControle convenioCobrancaControle = getControlador(ConvenioCobrancaControle.class);
        setOnCompleteJs("abrirPopup('convenioCobrancaCons.jsp','ConvenioCobranca',1000,750);");
    }

    public void consultarCobrancaPix() {
        if (isConfirmarCobrancaPix() && !this.consultandoPixAgora) {
            this.consultandoPixAgora = true;
            try {
                if (pixVO == null || UteisValidacao.emptyNumber(pixVO.getCodigo())) {
                    return;
                }
                limparMsg();
                setOnCompleteJs("");

                if (!pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {

                    boolean consultarNoBancoDeDados =
                            pixVO.getConveniocobranca().isPixSantander() ||
                            pixVO.getConveniocobranca().isPixBradesco() ||
                            pixVO.getConveniocobranca().isPixPjBank() ||
                            pixVO.getConveniocobranca().isPixInter() ||
                            pixVO.getConveniocobranca().isPixItau() ||
                            (pixVO.getConveniocobranca().isPixBB() && !isRedePratique());
                    //consultar a situação no banco de dados. O Webhook que muda o Status do pix para PAGO.
                    if (pixVO.getConveniocobranca() != null && consultarNoBancoDeDados) {
                        boolean pixFoiPago = getFacade().getPix().isPixPago(pixVO.getCodigo());
                        if (pixFoiPago) {
                            pixVO.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());
                            try {
                                //O recibo é gerado por webhook e não tem sessão. Por isso necessário inicializar essa variável do controlador do movpagamento.
                                //Quando uma baixa ocorrer aqui dentro do controlador ele irá preencher com o código do recibo para posteriormente ser impresso no caixa em aberto.
                                    setCodReciboPagamentoPixImprimirCaixaEmAberto(0); // LIMPAR
                                    pixVO.setReciboPagamento(getFacade().getPix().consultarCodigoReciboPorCodigoPix(pixVO.getCodigo()));
                                    setCodReciboPagamentoPixImprimirCaixaEmAberto(pixVO.getReciboPagamento());
                            } catch (Exception ex) {
                                ex.printStackTrace();
                                Uteis.logarDebug("Não foi possível obter o recibo do pix: " + ex.getMessage());
                            }
                        }
                    } else {
                        //Aqui ainda está consultando lá na API do banco por enquanto
                        PixVO pixVOConsulta = getPixPagamentoService().processarPixControlandoTransacao(pixVO.getCodigo());

                        pixVO.setStatus(pixVOConsulta.getStatus());

                        if (pixVO.getStatusEnum().equals(PixStatusEnum.EXPIRADA) ||
                                pixVO.getStatusEnum().equals(PixStatusEnum.REMOVIDA_PELO_USUARIO_RECEBEDOR)) {
                            setConfirmarCobrancaPix(false);
                            setPixConfirmado(false);
                        }
                    }
                }

                if (pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                    setConfirmarCobrancaPix(false);
                    setPixConfirmado(true);
                    setAbrirRichModalMensagem(false);
                    setSucesso(true);
                    setErro(false);
                    setMensagem("Pagamento Efetuado com Sucesso");
                    setMensagemPagamento("Pagamento Efetuado Com Sucesso.");
                    montarSucessoGrowl("Pagamento pix confirmado");
                    setMensagemDetalhada("");
                    setOnCompleteJs("try{Notifier.success('Pagamento Efetuado com Sucesso');Richfaces.hideModalPanel('modalPix');Richfaces.hideModalPanel('modalPixTelefone');Richfaces.hideModalPanel('modalPixEmail');}catch(e){console.log(e)}");
                    setApresentarBotaoRecibo(true);
                    setApresentarBotoesControle(false);
                    setApresentarAlteracaoDataBasePagtoContrato(false);
                    setValorMultaJuros(null);
                }
            } catch (Exception e) {
                e.printStackTrace();
                if (e.getMessage().equals("Existe Recibo para esse Pix")){
                    onCompleteJs = "Notifier.error('Já existe Recibo para esse Pix.');";
                }
            } finally {
                this.consultandoPixAgora = false;
            }
        }
    }

    private boolean isRedePratique() {
        //Como todas as unidades da pratique usam a mesma conta bancária, então não é possível configurar o webhook pois o webhook é uma URL por chave pix.
        //Momentaneamente quando for rede pratique continuará sem webhook até que a integração do Pix Inter fique pronta.
        try {
            RedeEmpresaVO rede = (RedeEmpresaVO) JSFUtilities.getFromSession("redeempresa");
            //CHAVEREDE DA PRATIQUE
            if (rede != null && rede.getChaverede().equals("341b908afd7637c1d5b09f248d3498f1")) {
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
        return false;
    }

    public void enviarWhatsPix() {
        if (isExisteTelefone()) {
            linkSendWhatsPix = "window.open('https://web.whatsapp.com/send?phone=" + getTelefoneCodigoPais() + "&text=" + pixVO.obterUrlCompartilharQRcode(getKey()) + "', 'WhatsApp', 800, 500); return false;";
        } else {
            linkSendWhatsPix = "Notifier.error('Informe o número de WhatsApp do aluno com DDD. São 11 dígitos, incluindo o 9. Exemplo: (62)99212-3233');";
        }
    }

    public void enviarEmailPix() {
        try {
            if (isExisteEmail()) {
                PixEmailService.enviarEmail(getEmailPix(),
                        getPixVO(),
                        url(),
                        getEmpresaLogado(),
                        getKey(),
                        getConfiguracaoSMTPNoReply(),
                        getUsuarioLogado());

                onCompleteJs = "Richfaces.hideModalPanel('modalPixEmail');Notifier.success('Email pix enviado');";
            } else {
                onCompleteJs = "Notifier.error('Informe um email válido');";
            }
        } catch (NullPointerException e) {
            onCompleteJs = "Notifier.error('Não foi possível enviar o email. Recarregue a página e tente novamente.');";
            e.printStackTrace();
        } catch (Exception e) {
            onCompleteJs = "Notifier.error('Não foi possível enviar o email. Motivo: " + e.getMessage() + "');";
            e.printStackTrace();
        }
    }

    public String getPaginaConfirmacaoPagamentoPix() {
        if (pixVO != null && pixVO.getStatus().equals(PixStatusEnum.CONCLUIDA.toString())) {
            return "Richfaces.showModalPanel('panelDadosGravadosComSucesso');";
        } else {
            return "";
        }
    }

    public void setOnCompleteJs(String onCompleteJs) {
        this.onCompleteJs = onCompleteJs;
    }

    public String getOnCompleteJs() {
        return onCompleteJs;
    }

    public boolean isPixGerado() {
        return pixGerado;
    }

    public void setPixGerado(boolean pixGerado) {
        this.pixGerado = pixGerado;
    }

    public String getTelefonePix() {
        if (telefonePix == null) {
            telefonePix = "";
        }
        return telefonePix;
    }

    public void setTelefonePix(String telefonePix) {
        this.telefonePix = telefonePix;
    }

    public String getEmailPix() {
        if (emailPix == null) {
            emailPix = "";
        }
        return emailPix;
    }

    public void setEmailPix(String emailPix) {
        this.emailPix = emailPix;
    }

    public String getCpfPix() {
        if (cpfPix == null) {
            cpfPix = "";
        }
        return cpfPix;
    }

    public void setCpfPix(String cpfPix) {
        this.cpfPix = cpfPix;
    }

    public boolean isApresentarCPFPix() {
        return apresentarCPFPix;
    }

    public void setApresentarCPFPix(boolean apresentarCPFPix) {
        this.apresentarCPFPix = apresentarCPFPix;
    }

    private class ParcelasPorMes extends SuperTO {

        private String referencia;
        private List<MovParcelaVO> parcelas;
        private double valor;

        public ParcelasPorMes() {
            referencia = "";
            parcelas = new ArrayList<MovParcelaVO>();
            valor = 0.0;
        }

        public String getReferencia() {
            return referencia;
        }

        public void setReferencia(String referencia) {
            this.referencia = referencia;
        }

        public List<MovParcelaVO> getParcelas() {
            return parcelas;
        }

        public void setParcelas(List<MovParcelaVO> parcelas) {
            this.parcelas = parcelas;
        }

        public double getValor() {
            return valor;
        }

        public void setValor(double valor) {
            this.valor = valor;
        }

        public void adicionaParcela(MovParcelaVO mp) {
            parcelas.add(mp);
            valor += mp.getValorParcela();
        }
    }

    public List getListaSelectItemBanco() {
        return listaSelectItemBanco;
    }

    public void setListaSelectItemBanco(List listaSelectItemBanco) {
        this.listaSelectItemBanco = listaSelectItemBanco;
    }

    public Double getSaldoInicial() {
        return saldoInicial;
    }

    public void setSaldoInicial(Double saldoInicial) {
        this.saldoInicial = saldoInicial;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getMovPagamento().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public Integer getCodigoVendaAvulsa() {
        return codigoVendaAvulsa;
    }

    public void setCodigoVendaAvulsa(Integer codigoVendaAvulsa) {
        this.codigoVendaAvulsa = codigoVendaAvulsa;
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    public Boolean getApresentarPanelContratoPrestacaoServico() {
        return apresentarPanelContratoPrestacaoServico;
    }

    public void setApresentarPanelContratoPrestacaoServico(Boolean apresentarPanelContratoPrestacaoServico) {
        this.apresentarPanelContratoPrestacaoServico = apresentarPanelContratoPrestacaoServico;
    }

    public List<PlanoTextoPadraoVO> getListaSelectModelosContratoPrestacao() {
        return listaSelectModelosContratoPrestacao;
    }

    public void setListaSelectModelosContratoPrestacao(List<PlanoTextoPadraoVO> listaSelectModelosContratoPrestacao) {
        this.listaSelectModelosContratoPrestacao = listaSelectModelosContratoPrestacao;
    }

    public PlanoTextoPadraoVO getTextoContratoPrestacao() {
        return textoContratoPrestacao;
    }

    public void setTextoContratoPrestacao(PlanoTextoPadraoVO textoContratoPrestacao) {
        this.textoContratoPrestacao = textoContratoPrestacao;
    }

    public void fecharPanelContratoPrestacaoServico() {
        setApresentarPanelContratoPrestacaoServico(false);
    }

    public String getMostrarRichModalPanelContratoPrestacaoServico() {
        if (getApresentarPanelContratoPrestacaoServico()) {
            return "Richfaces.showModalPanel('panelContratoPrestacaoServico')";

        } else {
            return "Richfaces.hideModalPanel('panelContratoPrestacaoServico')";

        }
    }


    public Boolean getCobrarMultaJurosAutomaticamente() throws Exception {
        return getEmpresaLogado().getCobrarAutomaticamenteMultaJuros();
    }


    public List<SelectItem> getListaSelectItemCobrarNaoCobrarMulta() {
        if (listaSelectItemCobrarNaoCobrarMulta == null) {
            listaSelectItemCobrarNaoCobrarMulta = new ArrayList<SelectItem>(2);
            listaSelectItemCobrarNaoCobrarMulta.add(new SelectItem(true, "Cobrar Multa"));
            listaSelectItemCobrarNaoCobrarMulta.add(new SelectItem(false, "Não Cobrar Multa"));
        }
        return listaSelectItemCobrarNaoCobrarMulta;
    }

    public void setListaSelectItemCobrarNaoCobrarMulta(List<SelectItem> listaSelectItemCobrarNaoCobrarMulta) {
        this.listaSelectItemCobrarNaoCobrarMulta = listaSelectItemCobrarNaoCobrarMulta;
    }


    public Boolean getOpcaoCobrarMulta() {
        if (opcaoCobrarMulta == null) {
            opcaoCobrarMulta = true;
        }
        return opcaoCobrarMulta;
    }

    public void setOpcaoCobrarMulta(Boolean opcaoCobrarMulta) {
        this.opcaoCobrarMulta = opcaoCobrarMulta;
    }


    public Boolean getLancado() {
        return getTotalLancado() > 0;
    }

    public Double getValorMultaJuros() {
        if (valorMultaJuros == null) {
            valorMultaJuros = 0.0;
        }
        return valorMultaJuros;
    }

    public void setValorMultaJuros(Double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public void validarAutorizacaoIsentarMulta() {
        try{
            limparMsg();
            AutorizacaoFuncionalidadeControle auto = (AutorizacaoFuncionalidadeControle) getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() {
                    try {
                        limparMsg();
                        setOpcaoCobrarMulta(false);
                        naoCobrarMultaJuros();
                    } catch (Exception e) {
                        montarErro(e);
                        onAutorizacaoComErro(e);
                    }
                }

                @Override
                public void onAutorizacaoComErro(Exception e) {
                    setOpcaoCobrarMulta(true);
                    montarErro(e);
                }

            };
            auto.autorizar("Confirmação de não cobrar multa/juros", "IsentarMultaJurosParcela",
                    "Você precisa da permissão \"3.37 - Permite isentar cobrança de multa/juros da parcela\"", "panelEsconderControles,panelCobrancaMultaJuros, panelSaldoContaCorrente, " +
                            "escolhaFormaPagamento,escolhaFormaPagamentoCC,totalLancado, " +
                            "residuo,mensagemInf,mensagemSup,panelBotoesControle, " +
                            "formCheque,formAutorizacao,panelCobrancaMultaJuros,escolhaFormaPagamentoCC,panelAutorizacaoFuncionalidade", listener);
            limparMsg();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void naoCobrarMultaJuros() {
        if (!getOpcaoCobrarMulta()) {
            setValorResiduo(getValorResiduo() - getValorMultaJuros());
            setTotalDivida(getTotalDivida() - getValorMultaJuros());
            setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() - getValorMultaJuros());
            setValorMultaJuros(0.0);
            desmarcarTodos();
            restaurarDivida();
        } else {
            setValorResiduo(getValorResiduo() + getValorMultaJuros());
            setTotalDivida(getTotalDivida() + getValorMultaJuros());
            setValorResiduoBaseCalculo(getValorResiduoBaseCalculo() + getValorMultaJuros());
        }
    }

    private void desmarcarTodos() {
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            movPagamento.setValor(0.0);
            movPagamento.setValorTotal(0.0);
            movPagamento.setMovPagamentoEscolhida(false);
            movPagamento.setPagamentoAberto(false);
        }
    }

    public Boolean getApresentarlinkCliente() {
        return apresentarlinkCliente;
    }

    public void setApresentarlinkCliente(Boolean apresentarlinkCliente) {
        this.apresentarlinkCliente = apresentarlinkCliente;
    }

    public String getMensagemAlerta() {
        if (getDtLancamentoContrato() != null && getDataPagto() != null) {
            if (Uteis.getDataComUltimaHora(getDtLancamentoContrato()).compareTo(Uteis.getDataComUltimaHora(getDataPagto())) < 0) {
                return "Data de lançamento do contrato foi alterada p/ " + getDataLancamentoContratoApresentar() + ", está diferente da data pagamento " + getDataPagto_Apresentar();
            }
        }
        return "";
    }

    public Date getDtLancamentoContrato() {
        return dtLancamentoContrato;
    }

    public void setDtLancamentoContrato(Date dtLancamentoContrato) {
        this.dtLancamentoContrato = dtLancamentoContrato;
    }

    public String getDataLancamentoContratoApresentar() {
        return Uteis.getData(getDtLancamentoContrato());
    }

    public void prepararEnvioContratoPorEmail() throws Exception {
        setMsgAlert("");
        setMensagemDetalhada("");
        try {
            ContratoVO obj = getFacade().getContrato().consultarPorChavePrimaria(getCodigoContrato(), Uteis.NIVELMONTARDADOS_IMPRESSAOCONTRATO);
            EnvioEmailContratoReciboControle envioEmailContratoReciboControle = (EnvioEmailContratoReciboControle) getControlador(EnvioEmailContratoReciboControle.class.getSimpleName());
            envioEmailContratoReciboControle.setContratoVO(obj);
            envioEmailContratoReciboControle.prepararListaEmail(obj.getPessoa().getCodigo(), true);
            envioEmailContratoReciboControle.setEnvioDeContrato(true);
            envioEmailContratoReciboControle.setEnviarContratoParaAssinatura(getConfiguracaoSistema().isAssinaturaContratoViaEmail());
            envioEmailContratoReciboControle.setReciboColaborador(false);
            envioEmailContratoReciboControle.setReciboPagamento(false);
            setMsgAlert("Richfaces.showModalPanel('modalEnviarContratoEmail');");
        } catch (Exception e){
            montarErro(e);
        }
    }

    public PinpadTO getPinpad() {
        return pinpad;
    }

    public void setPinpad(PinpadTO pinpad) {
        this.pinpad = pinpad;
    }

    public List<SelectItem> getListaSelectItemAdquirente() {
        return listaSelectItemAdquirente;
    }

    public void setListaSelectItemAdquirente(List<SelectItem> listaSelectItemAdquirente) {
        this.listaSelectItemAdquirente = listaSelectItemAdquirente;
    }

    public void verificarEGerarParcelasDeJuros(List<MovParcelaVO> listaParcelasPagar) throws Exception {
        if (getEmpresaLogado().getCobrarAutomaticamenteMultaJuros() && isPermitirCalcularJuros() && getOpcaoCobrarMulta()) {
            Date dataConsiderarMultaJuros = obterDataMultaJuros();

            // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
            // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
            // Por isso a inclusão desses logs
            if (!UteisValidacao.emptyList(listaParcelasPagar)) {
                String codigosMovParcelas = listaParcelasPagar.stream()
                        .map(p -> String.valueOf(p.getCodigo()))
                        .collect(Collectors.joining(","));
                Uteis.logarDebug("MovPagamentoControle - verificarEGerarParcelasDeJuros - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
            }

            setParcelasMultasJuros(getFacade().getMovParcela().criarParcelaMultaJuros(listaParcelasPagar, getEmpresaLogado(), getUsuarioLogado(), dataConsiderarMultaJuros, 1.0, null, controlarTransacao));
            setPermitirCalcularJuros(false);
        } else if (getEmpresaLogado().getCobrarAutomaticamenteMultaJuros() && isPermitirCalcularJuros()) {
            for (MovParcelaVO movParcela : listaParcelasPagar) {
                for (MovProdutoParcelaVO movProdutoParcela : movParcela.getMovProdutoParcelaVOs()) {
                    Double percentualProdutoSobreParcela = movProdutoParcela.getMovProdutoVO().getTotalFinal() / movParcela.getValorParcela();

                    Double valorProporcionalJurosProduto = Uteis.arredondarForcando2CasasDecimais(percentualProdutoSobreParcela * movParcela.getValorJuros());
                    Double valorProporcionalMultaProduto = Uteis.arredondarForcando2CasasDecimais(percentualProdutoSobreParcela * movParcela.getValorMulta());

                    movProdutoParcela.getMovProdutoVO().setJurosNaoRecebidos(valorProporcionalJurosProduto);
                    movProdutoParcela.getMovProdutoVO().setMultaNaoRecebida(valorProporcionalMultaProduto);
                }
            }
        }
    }

    private void montarListaSelectItemTabelaParceiroFidelidade(MovPagamentoVO movPagamentoVO) throws Exception {
        movPagamentoVO.setMsgParceiroFidelidade("");
        movPagamentoVO.setTabelaParceiroFidelidadeVO(new TabelaParceiroFidelidadeVO());
        movPagamentoVO.setListaSelectItemTabelaParceiroFidelidade(new ArrayList<SelectItem>());
        movPagamentoVO.getListaSelectItemTabelaParceiroFidelidade().add(new SelectItem(0, ""));

        ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorEmpresaETipo(movPagamentoVO.getEmpresa().getCodigo(), movPagamentoVO.getFormaPagamento().getTipoParceiro(), Uteis.NIVELMONTARDADOS_TODOS);
        if (!UteisValidacao.emptyNumber(parceiroFidelidadeVO.getCodigo())) {
            for (TabelaParceiroFidelidadeVO tab : parceiroFidelidadeVO.getItens()) {
                movPagamentoVO.getListaSelectItemTabelaParceiroFidelidade().add(new SelectItem(tab.getCodigo(), tab.getNomeTabela()));
            }
        }
    }

    private void atualizarPontosParceiroFidelidade(MovPagamentoVO obj) {
        try {
            if (obj == null) {
                return;
            }
            obj.setPontosParceiroFidelidade(0);
            if (!UteisValidacao.emptyNumber(obj.getTabelaParceiroFidelidadeVO().getCodigo())) {
                TabelaParceiroFidelidadeItemVO itemVO = getFacade().getTabelaParceiroFidelidadeItem().consultarPorTabelaParceiroFidelidadeValor(obj.getTabelaParceiroFidelidadeVO().getCodigo(), obj.getValorTotal(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(itemVO.getCodigo())) {
                    obj.setMsgParceiroFidelidade("");
                    obj.setMultiplicadorParceiroFidelidade(itemVO.getMultiplicador());
                    obj.setPontosParceiroFidelidade(itemVO.calcularTotalPontos(obj.getValorTotal()));
                } else {
                    throw new Exception("Tabela Dotz acumular: não foi encontrado um multiplicador equivalente para " + obj.getValorTotal_Apresentar());
                }
            }
        } catch (Exception ex) {
            obj.setPontosParceiroFidelidade(0);
            obj.setMsgParceiroFidelidade(ex.getMessage());
        }
    }

    public void consultarSaldoParceiroFidelidade() {
        try {
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            if (obj != null) {
                ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorEmpresaETipo(obj.getEmpresa().getCodigo(), TipoParceiroEnum.DOTZ, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ParceiroFidelidadeAPIDotzImpl parceiroFidelidadeAPIDotz = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);
                RetornoParceiroTO retornoParceiroTO = parceiroFidelidadeAPIDotz.consultarSaldo(obj.getCpfParceiroFidelidade(), true);
                obj.setMsgParceiroFidelidade(retornoParceiroTO.getMensagem());
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void calcularTotalPontosParceiroFidelidade() {
        Double somaPontos = 0.0;
        try {
            setListaPagamentosParceiroFidelidade(new ArrayList<MovPagamentoVO>());
            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
                if (movPagamento.getMovPagamentoEscolhida()) {
                    if (movPagamento.getValorTotal() < 0) {
                        movPagamento.setValorTotal(movPagamento.getValorTotal() * -1);
                    }

                    if (movPagamento.getFormaPagamento().isGerarPontos()) {
                        atualizarPontosParceiroFidelidade(movPagamento);
                    }
                    if (movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.PARCEIRO_FIDELIDADE.getSigla())) {
                        somaPontos += movPagamento.getPontosParceiroFidelidade();
                        if (!UteisValidacao.emptyNumber(movPagamento.getPontosParceiroFidelidade())) {
                            getListaPagamentosParceiroFidelidade().add(movPagamento);
                        }
                    }
                }
            }
            setTotalPontosParceiroFidelidade(somaPontos);
        } catch (Exception ex) {
            setTotalPontosParceiroFidelidade(somaPontos);
        }
    }

    public boolean isControlarTransacao() {
        return controlarTransacao;
    }

    public void setControlarTransacao(boolean controlarTransacao) {
        this.controlarTransacao = controlarTransacao;
    }

    public void consultarProdutosParceiro() {
        try {
            limparMsg();
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            if (obj != null) {
                obj.setMsgParceiroFidelidade("");
                obj.setSelectItemsProdutosParceiroFidelidade(new ArrayList<SelectItem>());
                obj.getSelectItemsProdutosParceiroFidelidade().add(0, new SelectItem("", ""));
                ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorEmpresaETipo(obj.getEmpresa().getCodigo(), obj.getFormaPagamento().getTipoParceiro(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ParceiroFidelidadeAPIDotzImpl parceiroFidelidadeAPIDotz = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);
                RetornoParceiroTO retornoParceiroTO = parceiroFidelidadeAPIDotz.consultarSaldo(obj.getCpfParceiroFidelidade(), false);
                if (!UteisValidacao.emptyString(retornoParceiroTO.getMensagem())) {

                    Integer pontosCliente;
                    try {
                        pontosCliente = Integer.parseInt(retornoParceiroTO.getMensagem());
                    } catch (Exception ex) {
                        obj.setValorTotal(0.0);
                        obj.setPontosParceiroFidelidade(0);
                        obj.setMsgParceiroFidelidade(retornoParceiroTO.getMensagem());
                        calcularEncapsulado();
                        throw new Exception(retornoParceiroTO.getMensagem());
                    }

                    List<ProdutoParceiroFidelidadeVO> listaProdutosCadastrados = getFacade().getProdutoParceiroFidelidade().consultarPorParceiroMaximoPontos(parceiroFidelidadeVO.getCodigo(), pontosCliente, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    obj.setProdutosParceiroFidelidade(listaProdutosCadastrados);

                    if (UteisValidacao.emptyList(obj.getProdutosParceiroFidelidade())) {
                        throw new Exception("Nenhum produto disponível");
                    }

                    for (ProdutoParceiroFidelidadeVO prodZW : obj.getProdutosParceiroFidelidade()) {
                        obj.getSelectItemsProdutosParceiroFidelidade().add(new SelectItem(prodZW.getCodigoExterno(), prodZW.getDescricao()));
                    }

                    obj.setMsgParceiroFidelidade("São apresentados somente produtos que cliente pode adquirir com sua pontuação disponível (" + retornoParceiroTO.getMensagem() + ")");

                    if (obj.getSelectItemsProdutosParceiroFidelidade().size() == 2) {
                        obj.setCodigoExternoProdutoParceiroFidelidade((String) obj.getSelectItemsProdutosParceiroFidelidade().get(1).getValue());
                        selecionarProdutoParceiro(obj);
                    }
                } else {
                    throw new Exception(retornoParceiroTO.getMensagem());
                }
            }
        } catch (Exception ex) {
            calcularEncapsulado();
            montarErro(ex.getMessage());
        }
    }

    public void selecionarProdutoParceiro() {
        try {
            limparMsg();
            MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
            if (obj != null) {
                selecionarProdutoParceiro(obj);
            }
        } catch (Exception ex) {
            calcularEncapsulado();
            montarErro(ex.getMessage());
        }
    }

    public void selecionarProdutoParceiro(MovPagamentoVO obj) throws Exception {
        limparMsg();
        obj.setValorTotal(0.0);
        obj.setPontosParceiroFidelidade(0);

        Integer pontos = 0;
        for (ProdutoParceiroFidelidadeVO prod : obj.getProdutosParceiroFidelidade()) {
            if (prod.getCodigoExterno().equals(obj.getCodigoExternoProdutoParceiroFidelidade())) {
                pontos = prod.getPontos();
                break;
            }
        }

        if (!UteisValidacao.emptyNumber(pontos)) {
            ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorEmpresaETipo(obj.getEmpresa().getCodigo(), obj.getFormaPagamento().getTipoParceiro(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ProdutoParceiroFidelidadeVO produtoParceiroFidelidadeVO = getFacade().getProdutoParceiroFidelidade().consultarPorParceiroPontos(parceiroFidelidadeVO.getCodigo(), pontos, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(produtoParceiroFidelidadeVO.getCodigo())) {
                throw new Exception("Não foi encontrado um produto do parceiro cadastrado com " + pontos + " pontos");
            }

            if (produtoParceiroFidelidadeVO.getValor() > getTotalDivida()) {
                obj.setCodigoExternoProdutoParceiroFidelidade("");
                throw new Exception("O valor do resgate não pode ser superior ao valor do pagamento.");
            }

            obj.setValorTotal(produtoParceiroFidelidadeVO.getValor());
            obj.setPontosParceiroFidelidade(produtoParceiroFidelidadeVO.getPontos());
        }
        calcularEncapsulado();
    }

    public String realizarAcoesPagamentoConfirmado() throws Exception {
        // incluir cupom fiscal
        // boolean incluirCupom = true;
        if (incluirCupom && getFacade().getConfiguracaoSistema().consultarSeInsereCupom()) {
            getFacade().getCupomFiscalService().setUsuarioLiberacao(usuarioLiberacao);
            getFacade().getCupomFiscalService().incluirCupom(reciboObj);
        }

        String retorno = "";
        Integer codigoEmpresa = reciboObj.getEmpresa().getCodigo();
        int nivelmontardadosConexaoespecifica = Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA;
        final EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(codigoEmpresa, nivelmontardadosConexaoespecifica);
        /// NFSe - ENVIO AUTOMATICO NO PAGAMENTO
        if (podeEmitirNFSeAutomatico(reciboObj, empresaVO)) {
            try {
                for (MovPagamentoVO pagamento : reciboObj.getPagamentosDesteRecibo()) {
                    pagamento.setProdutosPagos(getFacade().getMovPagamento().obterProdutosPagosMovPagamento(pagamento.getCodigo()));
                }
                retorno = getMensagemInternalizacao("msg_nfse_gerado_automatico") + ": ";
                NotaProcessarTO notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboObj, getUsuarioLogado(), getKey());
                String msggravarLote = notaProcessarTO.getRetorno();
                retorno += msggravarLote;
            } catch (Exception e) {
                retorno = getMensagemInternalizacao("msg_nfse_gerado_automatico_problema") + ": ";
                setMensagemDetalhada(e);
                retorno += getMensagemDetalhada();
            }
        }

        //NFCe - ENVIO AUTOMATICO NO PAGAMENTO
        boolean configuradoParaEnviarNFCeAposPagamento = empresaVO.isUsarNFCe() && empresaVO.isEnviarNFCeAutomatico() && empresaVO.isEnvioNFCeAutomaticoNoPagamento();
        if (configuradoParaEnviarNFCeAposPagamento) {
            String prefixoLog = "NFC-e | Envio automático após pagamento | ";
            Uteis.logar(prefixoLog + "Processo iniciado...");
            try {
                for (MovPagamentoVO pagamento : reciboObj.getPagamentosDesteRecibo()) {
                    pagamento.setProdutosPagos(getFacade().getMovPagamento().obterProdutosPagosMovPagamento(pagamento.getCodigo()));
                }
                retorno = getMensagemInternalizacao("msg_nfce_gerado_automatico");
                NotaProcessarTO notaProcessarTO = getFacade().getNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFCE, reciboObj, getUsuarioLogado(), getKey());
                Uteis.logar(prefixoLog + retorno + ", resultado notaProcessarTO: " + notaProcessarTO.getRetorno());
            } catch (Exception e) {
                Uteis.logar(prefixoLog + "Falha ao processar envio automático de NFCe no pagamento: " + e.getMessage());
                retorno = getMensagemInternalizacao("msg_nfce_gerado_automatico_problema") + ": ";
                setMensagemDetalhada(e);
                retorno += getMensagemDetalhada();
            }
        }

        if (getFacade().getZWFacade().getConfiguracaoSistema().realizarEnvioSesiSC()) {
            for (MovPagamentoVO pagamento : reciboObj.getPagamentosDesteRecibo()) {
                if(pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CA") || pagamento.getFormaPagamento().getTipoFormaPagamento().equals("CD") ) {
                    List<Integer> listaReciboSesi = new ArrayList<>();
                    listaReciboSesi.add(reciboObj.getCodigo());
                    getFacade().getZWFacade().startThreadIntegracaoFiesc(codigoEmpresa, listaReciboSesi, "incluirCartao");
                    break;
                }
            }
        }


        MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        VendaAvulsaControle vendaAvulsaControle = (VendaAvulsaControle) context().getExternalContext().getSessionMap().get("VendaAvulsaControle");
        AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");

        limparListaParcelaPaga(movParcelaControle, vendaAvulsaControle, aulaAvulsaDiariaControle);
        setApresentarBotoesControle(false);
        return retorno;
    }

    private void gravarLogCappta(String operacao, String msgErro) {
        try {

            PessoaVO pessoaVO = new PessoaVO();
            for (Object obj : getListaSelectItemMovPagamento()) {
                MovPagamentoVO mPag = (MovPagamentoVO) obj;
                if (mPag.getMovPagamentoEscolhida()) {
                    pessoaVO = mPag.getPessoa();
                    break;
                }
            }

            StringBuilder parcelas = new StringBuilder();
            parcelas.append("\n\nPARCELAS:\n");
            try {
                MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
                List<MovParcelaVO> parcelasParaPagar = movParcelaControle.getListaParcelasPagar();
                for (MovParcelaVO parc : parcelasParaPagar) {
                    parcelas.append("\n | ").append(parc.getCodigo()).append(" | ").append(parc.getDescricao()).append(" | ").append(parc.getDataVencimento_Apresentar()).append(" | ").append(parc.getValorParcela());
                }
            } catch (Exception ignored) {
            }

            try {
                parcelas.append("\n\nFORMAS PAGAMENTO:\n");
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) i.next();
                    parcelas.append("\n | ").append(obj.getMovPagamentoEscolhida()).append(" | ").append(obj.getFormaPagamento().getCodigo()).append(" | ").append(obj.getFormaPagamento().getDescricao()).append(" | ").append(obj.getValorTotal());
                }
            } catch (Exception ignored) {
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setNomeEntidade("CAPPTA");
            obj.setNomeEntidadeDescricao("CAPPTA");
            obj.setOperacao("CAPPTA - " + operacao);

            try {
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("");
                obj.setUserOAMD("");
            }

            StringBuilder infoDados = new StringBuilder();
            infoDados.append("PESSOA: ").append(pessoaVO.getCodigo());
            infoDados.append("\nNOME: ").append(pessoaVO.getNome());

            try {
                infoDados.append("\nEMPRESA LOGADA: ").append(getEmpresaLogado().getNome());
            } catch (Exception ignored) {
            }

            try {
                infoDados.append("\nMULTA JUROS: ").append(getOpcaoCobrarMulta());
                infoDados.append("\nVALOR MULTA JUROS: ").append(getValorMultaJuros());
            } catch (Exception ignored) {
            }

            infoDados.append(parcelas);

            try {
                ObjectMapper mapper = new ObjectMapper();
                mapper.setTimeZone(TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId()));
                infoDados.append("\n\nPINPAD: ").append(mapper.writeValueAsString(pinpad));
            } catch (Exception ignored) {
            }

            if (!UteisValidacao.emptyString(msgErro)) {

                String identificadorMemCached = "OBJ_CAPPTA_ERRO_" + Calendario.hoje().getTime();
                getFacade().getMemCachedManager().gravar(this, (60 * 60 * 24 * 3), identificadorMemCached);

                infoDados.append("\nIdentificadorMemCached Erro: ").append(identificadorMemCached);
                infoDados.append("\n\nERRO: ").append(msgErro);
            }

            obj.setNomeCampo("CAPPTA - " + operacao);
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado(infoDados.toString());
            registrarLogObjetoVO(obj, 0);
        } catch (Exception e) {
            try {
                registrarLogErroObjetoVO("CAPPTA", 0, "ERRO AO GERAR LOG DA CAPPTA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void gravarLogStoneConnect(String operacao, String msg) {
        try {

            PessoaVO pessoaVO = new PessoaVO();
            for (Object obj : getListaSelectItemMovPagamento()) {
                MovPagamentoVO mPag = (MovPagamentoVO) obj;
                if (mPag.getMovPagamentoEscolhida()) {
                    pessoaVO = mPag.getPessoa();
                    break;
                }
            }

            StringBuilder parcelas = new StringBuilder();
            parcelas.append("\n\nPARCELAS:\n");
            try {
                MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
                List<MovParcelaVO> parcelasParaPagar = movParcelaControle.getListaParcelasPagar();
                for (MovParcelaVO parc : parcelasParaPagar) {
                    parcelas.append("\n | ").append(parc.getCodigo()).append(" | ").append(parc.getDescricao()).append(" | ").append(parc.getDataVencimento_Apresentar()).append(" | ").append(parc.getValorParcela());
                }
            } catch (Exception ignored) {
            }

            try {
                parcelas.append("; ").append("\n\nFORMAS PAGAMENTO:\n");
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj = (MovPagamentoVO) i.next();
                    parcelas.append("\n | ").append(obj.getMovPagamentoEscolhida()).append(" | ").append(obj.getFormaPagamento().getCodigo()).append(" | ").append(obj.getFormaPagamento().getDescricao()).append(" | ").append(obj.getValorTotal());
                }
            } catch (Exception ignored) {
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria("0");
            obj.setNomeEntidade("STONE CONNECT");
            obj.setNomeEntidadeDescricao("STONE CONNECT");
            obj.setOperacao("STONE CONNECT - " + operacao);

            try {
                obj.setResponsavelAlteracao(getUsuarioLogado().getNome());
                obj.setUserOAMD(getUsuarioLogado().getUserOamd());
            } catch (Exception ex) {
                obj.setResponsavelAlteracao("");
                obj.setUserOAMD("");
            }

            StringBuilder infoDados = new StringBuilder();
            infoDados.append("PESSOA: ").append(pessoaVO.getCodigo()).append("; ");
            infoDados.append("\nNOME: ").append(pessoaVO.getNome()).append("; ");

            try {
                infoDados.append("\nEMPRESA LOGADA: ").append(getEmpresaLogado().getNome()).append("; ");
            } catch (Exception ignored) {
            }

            infoDados.append(parcelas).append("; ");
            infoDados.append("\nRETORNO STONE CONNECT: ").append(msg).append("; ");

            obj.setNomeCampo("STONE CONNECT - " + operacao);
            obj.setDataAlteracao(Calendario.hoje());
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado(infoDados.toString());
            registrarLogObjetoVO(obj, 0);
        } catch (Exception e) {
            try {
                registrarLogErroObjetoVO("STONE CONNECT", 0, "ERRO AO GERAR LOG DA STONE CONNECT", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public String getOnCompleteFazerCheckoutCappta() {
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO obj = (MovPagamentoVO) i.next();
            if (obj.getFormaPagamento().getPinpad().getPinpad().equals(OpcoesPinpadEnum.CAPPTA.getCodigo())) {
                pinpad = new PinpadTO();
                pinpad.setPinpadCappta(true);
                return "fazerCheckout();";
            }
        }
        return "";
    }

    public Boolean isGeoitd() throws Exception {
        if (getConfiguracaoSistema().isUsarSistemaInternacional() && getEmpresaLogado().getPais().getNome().equals(IdentificadorInternacionalEnum.URUGUAY.getNomePais())) {
            return true;
        }
        return false;
    }

    private void cadastrarPagamentoAmigoFit(List<MovParcelaVO> listaParcelas) throws Exception {
        String retornologin = getFacade().getZWFacade().loginAmigoFit(getEmpresa().getNomeUsuarioAmigoFit(), getEmpresa().getSenhaUsuarioAmigoFit());
        String token = null;
        if (!retornologin.toUpperCase().contains("ERRO AO REALIZAR LOGIN")) {
            try {
                JSONObject jsonObject = new JSONObject(retornologin);
                token = jsonObject.get("accessToken").toString();
            } catch (Exception e) {
                Uteis.logar(e, MovPagamentoControle.class);
            }
        } else {
            Uteis.logar("### Erro no login AmigoFit!", MovPagamentoControle.class);
        }

        if (token == null) {
            return;
        }

        Iterator i = listaParcelas.iterator();
        while (i.hasNext()) {
            ClienteAmigoFitJSON amigoFitJSON = new ClienteAmigoFitJSON();
            JSONObject clienteAmigoJSON;
            MovParcelaVO movParcela = (MovParcelaVO) i.next();
            PessoaVO pessoaVO = getFacade().getPessoa().consultarPorCodigo(movParcela.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            clienteAmigoJSON = amigoFitJSON.toJSONPagamento(movPagamentoVO.getDataLancamento(), new Date(movParcela.getDataVencimento().getTime()), pessoaVO.getCfp(), movParcela.getValorParcela());
            getFacade().getZWFacade().cadastrarPagamentoAmigoFit(clienteAmigoJSON, token);
        }
    }

    private void montarConvenioCobrancaPix(EmpresaVO empresaVO, FormaPagamentoVO formaPagamentoVO) throws Exception {
        try {
            setListaSelectItemConvenioCobrancaPix(new ArrayList<>());
            List<ConvenioCobrancaVO> resultadoConsulta = getFacade().getConvenioCobranca().consultarPorTipoPixComEmpresa(empresaVO);
            List<ConvenioCobrancaVO> listaFinal = new ArrayList<>();
            for (ConvenioCobrancaVO obj : resultadoConsulta) {
                if (formaPagamentoVO.getConvenioCobrancaVO().getCodigo().equals(obj.getCodigo())) {
                    listaFinal.add(obj);
                }
            }
            if (UteisValidacao.emptyNumber(listaFinal.size())) {
                for (ConvenioCobrancaVO obj : resultadoConsulta) {
                    listaFinal.add(obj);
                }
            }

            List<SelectItem> objs = new ArrayList<SelectItem>();
            for (ConvenioCobrancaVO obj : listaFinal) {
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
            setConveniosPix(listaFinal);

            if (objs.size() > 1) {
                objs.add(0, new SelectItem(0, "(Selecione o Convênio)"));
            }
            Ordenacao.ordenarLista(objs, "label");
            setListaSelectItemConvenioCobrancaPix(objs);
        } catch (Exception e) {
            throw new Exception("Não foi possível montar a lista de convênios para Pix");
        }
    }

    private void getFormaPagamento(MovParcelaControle movParcelaControle, MovParcelaVO parcela) throws Exception {
        List<MovParcelaVO> lista = new ArrayList<MovParcelaVO>();

        if (movParcelaControle == null){
            lista.add(parcela);
        }
        else{
            lista = movParcelaControle.getListaParcelasPagar();
        }

        List<FormaPagamentoVO>  formasPag = new ArrayList<>();
        for (MovParcelaVO mp : lista){
            FormaPagamentoVO forma = getFacade().getCategoriaProduto().consultarFormaPagamentoCategoriaProduto(mp.getCodigo());
            if ((forma != null && forma.getCodigo() != 0 && !forma.getDescricao().equals("NENHUM")) && !formasPag.contains(forma)) {
                formasPag.add(forma);
            } else if (forma == null || forma.getCodigo() == 0 || forma.getDescricao().equals("NENHUM")){
                formasPag = new ArrayList<>();
                break;
            }
        }
        if(movParcelaControle != null) setListaValidacaoParcela(movParcelaControle.getListaParcelasPagar());

        Iterator i = getListaSelectItemFormaPagamento().iterator();
        while (i.hasNext()) {
            FormaPagamentoVO formaPagamento = (FormaPagamentoVO) i.next();
            if (!formaPagamento.isSomenteFinanceiro()) {
                if (formasPag.contains(formaPagamento) || formasPag.isEmpty()) {
                    inicializarMovPagamentoVO(formaPagamento, movParcelaControle == null? parcela : movParcelaControle .getMovParcelaVO());
                    getListaSelectItemMovPagamento().add(getMovPagamentoVO());
                }

            }
            setMovPagamentoVO(new MovPagamentoVO());
        }
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public String getMsgGeoitd() {
        return msgGeoitd;
    }

    public void setMsgGeoitd(String msgGeoitd) {
        this.msgGeoitd = msgGeoitd;
    }

    public String getMsgGeoitdPos() {
        return msgGeoitdPos;
    }

    public void setMsgGeoitdPos(String msgGeoitdPos) {
        this.msgGeoitdPos = msgGeoitdPos;
    }

    public Integer getCodMsgGeoitd() {
        return codMsgGeoitd;
    }

    public void setCodMsgGeoitd(Integer codMsgGeoitd) {
        this.codMsgGeoitd = codMsgGeoitd;
    }

    public List<ConvenioCobrancaVO> getConveniosPix() {
        return conveniosPix;
    }

    public void setConveniosPix(List<ConvenioCobrancaVO> conveniosPix) {
        this.conveniosPix = conveniosPix;
    }

    public ConvenioCobrancaVO getConvenioPixSelecionado() {
        return convenioPixSelecionado;
    }

    public void setConvenioPixSelecionado(ConvenioCobrancaVO convenioPixSelecionado) {
        this.convenioPixSelecionado = convenioPixSelecionado;
    }

    public PixPagamentoService getPixPagamentoService() {
        if (pixPagamentoService == null) {
            pixPagamentoService = new PixPagamentoService(Conexao.getFromSession());
        }
        return pixPagamentoService;
    }

    public boolean isConfirmarCobrancaPix() {
        return confirmarCobrancaPix;
    }

    public void setConfirmarCobrancaPix(boolean confirmarCobrancaPix) {
        this.confirmarCobrancaPix = confirmarCobrancaPix;
    }

    public boolean isExisteTelefone() {
        if (getTelefonePix() == null || getTelefonePix().trim().isEmpty()) {
            return false;
        } else {
            if (getTelefonePix().trim().length() < 11) {
                return false;
            }
            return true;
        }
    }

    public boolean isExisteEmail() {
        if (getEmailPix() == null || getEmailPix().trim().isEmpty()) {
            return false;
        } else {
            return Uteis.isValidEmailAddressRegex(getEmailPix());
        }
    }

    public String getTelefoneCodigoPais() {
        if (!(getTelefonePix().startsWith("55") || getTelefonePix().startsWith("+55"))) {
            return "55" + getTelefonePix();
        }
        return getTelefonePix();
    }

    public String getLinkSendWhatsPix() {
        return linkSendWhatsPix;
    }

    public void setLinkSendWhatsPix(String linkSendWhatsPix) {
        this.linkSendWhatsPix = linkSendWhatsPix;
    }

    public boolean isExisteConvenioPix() {
        return getConveniosPix() != null && getConveniosPix().size() > 0;
    }

    public PixVO getPixVO() {
        return pixVO;
    }

    public void setPixVO(PixVO pixVO) {
        this.pixVO = pixVO;
    }

    public boolean isPixConfirmado() {
        return pixConfirmado;
    }

    public void setPixConfirmado(boolean pixConfirmado) {
        this.pixConfirmado = pixConfirmado;
    }

    public String getNomePagadorAbreviado() {
        if (nomePagador != null) {
            if (nomePagador.length() > 36) {
                return nomePagador.substring(0, 36).concat("...").toUpperCase();
            } else {
                return nomePagador.toUpperCase();
            }
        }
        return "";
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaPix() {
        return listaSelectItemConvenioCobrancaPix;
    }

    public void setListaSelectItemConvenioCobrancaPix(List<SelectItem> listaSelectItemConvenioCobrancaPix) {
        this.listaSelectItemConvenioCobrancaPix = listaSelectItemConvenioCobrancaPix;
    }

    public Integer getConvenioPix() {
        return convenioPix;
    }

    public void setConvenioPix(Integer convenioPix) {
        this.convenioPix = convenioPix;
    }

    public boolean isClicouBotaoPagOnline() {
        return clicouBotaoPagOnline;
    }

    public void setClicouBotaoPagOnline(boolean clicouBotaoPagOnline) {
        this.clicouBotaoPagOnline = clicouBotaoPagOnline;
    }

    public void validarConvenioPjBank(){
        Iterator i = getListaSelectItemMovPagamento().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPagamento = (MovPagamentoVO) i.next();
            movPagamento.setExibirDataCreditoBoleto(false);
            if (movPagamento.getMovPagamentoEscolhida() &&
                    movPagamento.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.BOLETOBANCARIO.getSigla()) &&
                    !UteisValidacao.emptyNumber(movPagamento.getConvenio().getCodigo())) {
                ConvenioCobrancaVO conv = getConvenioCobrancaListaTodos(movPagamento.getConvenio().getCodigo());
                movPagamento.setExibirDataCreditoBoleto(conv != null && conv.isBoletoPjBank());
            }
        }
    }

    public ConvenioCobrancaVO getConvenioCobrancaListaTodos(Integer codigo) {
        for (ConvenioCobrancaVO obj : getListaTodosConvenios()) {
            if (obj.getCodigo().equals(codigo)) {
                return obj;
            }
        }
        return null;
    }

    public List<SelectItem> getListaSelectItemConvenioCobrancaBoleto() {
        List<SelectItem> lista = new ArrayList<>();
        for (ConvenioCobrancaVO obj : getListaTodosConvenios()) {
            if (obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                    (obj.isBoleto() || obj.isBoletoPjBank())) {
                lista.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public List<ConvenioCobrancaVO> getListaTodosConvenios() {
        if (listaTodosConvenios == null) {
            listaTodosConvenios = new ArrayList<>();
        }
        return listaTodosConvenios;
    }

    public void setListaTodosConvenios(List<ConvenioCobrancaVO> listaTodosConvenios) {
        this.listaTodosConvenios = listaTodosConvenios;
    }

    public PinPadPedidoVO getPinPadPedidoVO() {
        if (pinPadPedidoVO == null) {
            pinPadPedidoVO = new PinPadPedidoVO();
        }
        return pinPadPedidoVO;
    }

    public void setPinPadPedidoVO(PinPadPedidoVO pinPadPedidoVO) {
        this.pinPadPedidoVO = pinPadPedidoVO;
    }

    public List<PinPadVO> getListaPinpad() {
        if (listaPinpad == null) {
            listaPinpad = new ArrayList<>();
        }
        return listaPinpad;
    }

    public List<SelectItem> getListaSelectItemPinpad() {
        List<SelectItem> lista = new ArrayList<>();
        for (PinPadVO pinPadVO : this.getListaPinpad()) {
            lista.add(new SelectItem(pinPadVO.getCodigo(), pinPadVO.getDescricao().toUpperCase()));
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(0, ""));
        return lista;
    }

    public void setListaPinpad(List<PinPadVO> listaPinpad) {
        this.listaPinpad = listaPinpad;
    }

    public PinPadVO getPinpadVOSelecionado() {
        if (pinpadVOSelecionado == null) {
            pinpadVOSelecionado = new PinPadVO();
        }
        return pinpadVOSelecionado;
    }

    public void setPinpadVOSelecionado(PinPadVO pinpadVOSelecionado) {
        this.pinpadVOSelecionado = pinpadVOSelecionado;
    }

    public Integer getPinpadSelecionado() {
        return pinpadSelecionado;
    }

    public void setPinpadSelecionado(Integer pinpadSelecionado) {
        this.pinpadSelecionado = pinpadSelecionado;
    }

    public List<SelectItem> getSelectItemNrVezesPinpad() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(0, ""));
        for (int i = 1; i <= this.getPinpadVOSelecionado().getNrMaxParcelas(); i++) {
            if (i == 1) {
                lista.add(new SelectItem(i, "À Vista"));
            } else {
                lista.add(new SelectItem(i, i + " vezes"));
            }
        }
        return lista;
    }

    public Integer getNrParcelasPinpad() {
        if (nrParcelasPinpad == null) {
            nrParcelasPinpad = 0;
        }
        return nrParcelasPinpad;
    }

    public void setNrParcelasPinpad(Integer nrParcelasPinpad) {
        this.nrParcelasPinpad = nrParcelasPinpad;
    }

    public boolean isApresentarParcelasPinpad() {
        return apresentarParcelasPinpad;
    }

    public void setApresentarParcelasPinpad(boolean apresentarParcelasPinpad) {
        this.apresentarParcelasPinpad = apresentarParcelasPinpad;
    }

    public String getOnCompletePinpad() {
        if (onCompletePinpad == null) {
            onCompletePinpad = "";
        }
        return onCompletePinpad;
    }

    public void setOnCompletePinpad(String onCompletePinpad) {
        this.onCompletePinpad = onCompletePinpad;
    }

    public boolean isConfirmarPinpadStone() {
        return this.isConfirmarPinpad() && this.getPinpad().getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT);
    }

    public boolean isConfirmarPinpadGetCard() {
        return this.isConfirmarPinpad() && this.getPinpad().getPinpadEnum().equals(OpcoesPinpadEnum.GETCARD);
    }

    public boolean isConfirmarPinpad() {
        return confirmarPinpad;
    }

    public void setConfirmarPinpad(boolean confirmarPinpad) {
        this.confirmarPinpad = confirmarPinpad;
    }

    public boolean isProcessandoPinpad() {
        return processandoPinpad;
    }

    public void setProcessandoPinpad(boolean processandoPinpad) {
        this.processandoPinpad = processandoPinpad;
    }

    public OpcoesPinpadEnum getOpcoesPinpadSelecionado() {
        return opcoesPinpadSelecionado;
    }

    public void setOpcoesPinpadSelecionado(OpcoesPinpadEnum opcoesPinpadSelecionado) {
        this.opcoesPinpadSelecionado = opcoesPinpadSelecionado;
    }

    public boolean isPinpadStoneConnect() {
        return pinpadSelecionadoIgual(OpcoesPinpadEnum.STONE_CONNECT);
    }

    public boolean isPinpadGetCard() {
        return pinpadSelecionadoIgual(OpcoesPinpadEnum.GETCARD);
    }

    private boolean pinpadSelecionadoIgual(OpcoesPinpadEnum opcoesPinpadEnum) {
        for (PinPadVO pinPadVO1 : this.getListaPinpad()) {
            if (!UteisValidacao.emptyNumber(this.getPinpadSelecionado()) &&
                    pinPadVO1.getCodigo().equals(this.getPinpadSelecionado()) &&
                    pinPadVO1.getOpcoesPinpadEnum().equals(opcoesPinpadEnum)) {
                return true;
            }
        }
        return false;
    }

    public String getUrlServicoGetCardScope() {
        return PropsService.getPropertyValue(PropsService.urlServicoGetCardScope);
    }

    public String getBodyPinPadGetCard() {
        try {
            if (UteisValidacao.emptyNumber(this.getPinPadPedidoVO().getCodigo())) {
                return "";
            }

//            tipo
//            0 debito
//            1 credito
            int tipo = 0;

            Iterator i = getListaSelectItemMovPagamento().iterator();
            while (i.hasNext()) {
                MovPagamentoVO obj1 = (MovPagamentoVO) i.next();
                if (obj1.getMovPagamentoEscolhida()) {
                    tipo = (obj1.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) ? 1 : 0;
                    break;
                }
            }

            int valor = (int) Math.round(this.getPinPadPedidoVO().getValor() * 100);

            JSONObject request = new JSONObject();
            request.put("Valor", String.valueOf(valor));
            request.put("Taxa", "0");
            request.put("Tipo", tipo);
            request.put("Modo", "2");
            request.put("NumParcela", this.getNrParcelasPinpad());
            request.put("Empresa", this.getPinPadPedidoVO().getConvenioCobrancaVO().getCodigoAutenticacao01());
            request.put("Filial", this.getPinPadPedidoVO().getConvenioCobrancaVO().getCodigoAutenticacao02());
            request.put("Pdv", this.getPinPadPedidoVO().getPdvPinPad());

            JSONObject json = new JSONObject();
            json.put("request", request);
            return json.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void erroGetCard() {
        boolean debito = false;
        try {
            this.setOnCompletePinpad("");
            this.setConfirmarPinpad(false);
            this.getPinPadPedidoVO().setStatus(StatusPinpadEnum.FALHA);
            getFacade().getPinPadPedido().alterar(this.getPinPadPedidoVO());

            String mensagem = this.getPinPadPedidoVO().getParamsResp();
            try {
                JSONObject json = new JSONObject(this.getPinPadPedidoVO().getParamsResp());
                mensagem = json.getJSONObject("response").optString("Mensagem");
            } catch (Exception ex) {
//                ex.printStackTrace();
            }

            try {
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj1 = (MovPagamentoVO) i.next();
                    if (obj1.getMovPagamentoEscolhida()) {
                        debito = (obj1.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla()));
                        break;
                    }
                }
            } catch (Exception ignored) {
            }

            if (UteisValidacao.emptyString(mensagem)) {
                mensagem = "Falha ao inicializar GetCard, tente novamente e caso persista verifique se o programa controladorScope está inicializado. MPC";
            }
            throw new Exception(mensagem);
        } catch (Exception ex) {
//            ex.printStackTrace();
            montarErro(ex);
            this.limparPinpadSelecionado();
            this.setOnCompletePinpad(getMensagemNotificar() + (debito ? "Richfaces.hideModalPanel('modalPinpad');" : ""));
        }
    }

    public void salvarIdExternoGetCard() {
        try {
            this.setConfirmarPinpad(true);
            this.getPinPadPedidoVO().setStatus(StatusPinpadEnum.AGUARDANDO);
            getFacade().getPinPadPedido().alterar(this.getPinPadPedidoVO());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void gravarLogGetCard() {
        try {
            JSONObject json = new JSONObject();

            try {
                Iterator i = getListaSelectItemMovPagamento().iterator();
                while (i.hasNext()) {
                    MovPagamentoVO obj1 = (MovPagamentoVO) i.next();
                    if (obj1.getMovPagamentoEscolhida()) {
                        json.put("pessoa_codigo", obj1.getPessoa().getCodigo());
                        json.put("pessoa_nome", obj1.getPessoa().getNome());
                        json.put("valor", obj1.getValor());
                        json.put("forma_pagamento_codigo", obj1.getFormaPagamento().getCodigo());
                        json.put("forma_pagamento", obj1.getFormaPagamento().getDescricao());
                    }
                }
            } catch (Exception ignored) {
            }

            json.put("retorno", this.getPinPadPedidoVO().getRetornoPinpad());

            getFacade().getPinPad().incluirHistorico(this.getPinPadPedidoVO().getCodigo(),
                    this.getPinPadPedidoVO().getIdExterno(),
                    this.getPinPadPedidoVO().getRetornoPinpadOperacao(),
                    json.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public boolean isConvenioPinPadSucesso() {
        return convenioPinPadSucesso;
    }

    public void setConvenioPinPadSucesso(boolean convenioPinPadSucesso) {
        this.convenioPinPadSucesso = convenioPinPadSucesso;
    }

    public String getTitleSaldoFinalResumoContas() {
        StringBuilder sb = new StringBuilder();
        sb.append("<b>O saldo final não corresponde ao cálculo (ENTRADA - SAÍDA) e sim ao cálculo ((SALDO ANTERIOR + ENTRADA) - SAÍDA).</b></br>");
        sb.append(" O saldo é sempre acumulativo e considera o saldo anterior que você tinha na conta.</br>");
        sb.append(" <b>Exemplo prático do cálculo:</b></br>");
        sb.append(" -Iniciou o mês atual com R$5.000,00 de saldo anterior</br>");
        sb.append(" -Teve R$7.000,00 de entrada total e R$3.000,00 de saída total no mês atual</br>");
        sb.append(" -Logo o cálculo é : ((5.000,00 + 7.000,00) - 3.000,00)</br>");
        sb.append(" -Saldo final = R$9.000,00");
        return sb.toString();
    }

    public int getCodReciboPagamentoPixImprimirCaixaEmAberto() {
        return codReciboPagamentoPixImprimirCaixaEmAberto;
    }

    public void setCodReciboPagamentoPixImprimirCaixaEmAberto(int codReciboPagamentoPixImprimirCaixaEmAberto) {
        this.codReciboPagamentoPixImprimirCaixaEmAberto = codReciboPagamentoPixImprimirCaixaEmAberto;
    }

    public void atualizarAdquirente(ValueChangeEvent evt) {
        setMensagemPagamento("");
        setMensagemDetalhada("");
        limparMsg();
        MovPagamentoVO obj = (MovPagamentoVO) context().getExternalContext().getRequestMap().get("movPagamento");
        if (evt.getComponent().getClass() == HtmlSelectOneMenu.class) {
            try {
                //adquirenteVo quando muda a combo de Adquirente
                if (evt.getComponent().getId().equals("adquirente")) {
                    Integer codOper = (Integer) evt.getNewValue();
                    obj.setAdquirenteVO(new AdquirenteVO());
                    if (codOper != 0) {
                        obj.setAdquirenteVO(getFacade().getAdquirente().consultarPorCodigo(codOper));
                    }
                }
            } catch (Exception ex) {
                this.getMovPagamentoVO().setUsarPagamentoAprovaFacil(false);
                setMensagemDetalhada(ex.getMessage());
                if (ex.getMessage() != null && ex.getMessage().contains(" necessario vincular")) {
                    montarAviso(ex.getMessage());
                } else {
                    montarErro(ex.getMessage());
                }
            }
        }
    }

}

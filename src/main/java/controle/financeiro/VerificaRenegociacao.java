package controle.financeiro;

import negocio.comuns.basico.EmpresaVO;

import static negocio.comuns.plano.enumerador.TipoProduto.TAXA_RENEGOCIACAO;
import static org.apache.commons.lang.StringUtils.contains;
import static org.apache.commons.lang.StringUtils.isNotBlank;


class VerificaRenegociacao {

    private VerificaRenegociacao() {
    }

    /**
     * Verificar se uma empresa tem cadastrado, isso é ativo, o produto "Renegociação"
     */
    static void verificar(final EmpresaVO empresaLogado) throws Exception {
        if (isNotBlank(empresaLogado.getChaveNFSe()) && empresaLogado.getUsarNFSe()) {
            String codigoRenegociacao = TAXA_RENEGOCIACAO.getCodigo();
            String tiposProdutoEmissaoNFSe = empresaLogado.getTipoProdutoEmissaoNFSe();
            boolean naoTemProdutoRenegociacao = !contains(tiposProdutoEmissaoNFSe, codigoRenegociacao);

            if (naoTemProdutoRenegociacao) {
                throw new Exception("Necessário informar tipo de produto 'Taxa Renegociação' para emitir NFSe na empresa");
            }
        }

    }
}
package controle.financeiro;

import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.ItemFaturamentoFactory;
import negocio.comuns.financeiro.ItemFaturamentoTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.model.SelectItem;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CalculadoraControle extends SuperControle {
    private int filtroSelecionado;
    private int passoAutal = 1;

    private List<ItemFaturamentoTO> detalhesFaturamento;
    private Map<String, Date> intervaloDatasMesCorrente = new HashMap<String, Date>();
    private Map<String, Date> intervaloDatasMesPassado = new HashMap<String, Date>();
    private Map<String, Date> intervaloDatasUltimosSeisMeses = new HashMap<String, Date>();
    private Map<Integer, Map<String, Date>> intervalos = new HashMap<Integer, Map<String, Date>>();
    private List<SelectItem> opcoesFiltroData = new ArrayList<SelectItem>();

    public CalculadoraControle() throws Exception {
        iniciaFiltros();
        carregarFaturamento();
    }

    boolean isDeslogado() {
        return  context().getExternalContext().getSessionMap().get("LoginControle") ==  null;
    }

    private void inicializar() throws Exception {
        carregarFaturamento();
    }

    private void carregaInformacoesSimulador() throws Exception {
        ItemFaturamentoTO debito = ItemFaturamentoFactory.novo("CD", 1);
        ItemFaturamentoTO creditoAVistaa = ItemFaturamentoFactory.novo("CA", 1);
        ItemFaturamentoTO credito6X = ItemFaturamentoFactory.novo("CA", 6);
        ItemFaturamentoTO credito12X = ItemFaturamentoFactory.novo("CA", 12);

        detalhesFaturamento = new ArrayList<ItemFaturamentoTO>();
        detalhesFaturamento.add(debito);
        detalhesFaturamento.add(creditoAVistaa);
        detalhesFaturamento.add(credito6X);
        detalhesFaturamento.add(credito12X);
    }

    private void iniciaFiltros() throws Exception {
        if (opcoesFiltroData.size() == 0) {
            intervaloDatasMesCorrente.put("inicio", Uteis.obterPrimeiroDiaMesPrimeiraHora(Calendario.hoje()));
            intervaloDatasMesCorrente.put("fim", Calendario.hoje());
            intervaloDatasMesPassado.put("inicio", Uteis.obterPimeiroDiaMesAnteriorPrimeiraHora(Calendario.hoje()));
            intervaloDatasMesPassado.put("fim", Uteis.obterUltimoDiaMesAnteriorUltimaHora(Calendario.hoje()));
            intervaloDatasUltimosSeisMeses.put("inicio", Uteis.obterPimeiroDiaUltimosSeisMesesPrimeiraHora(Calendario.hoje()));
            intervaloDatasUltimosSeisMeses.put("fim", Uteis.obterUltimoDiaUltimosSeisMesesUltimaHora(Calendario.hoje()));

            intervalos.put(1, intervaloDatasMesCorrente);
            intervalos.put(2, intervaloDatasMesPassado);
            intervalos.put(3, intervaloDatasUltimosSeisMeses);
            opcoesFiltroData.add(new SelectItem(1, "Mês Corrente"));
            opcoesFiltroData.add(new SelectItem(2, "Mês Passado"));
            opcoesFiltroData.add(new SelectItem(3, "Últimos 6 meses"));

            if (isDeslogado())
                filtroSelecionado = 4;
            else
                filtroSelecionado = 3;
        }
    }

    private Date getInicio() {
        return intervalos.get(filtroSelecionado).get("inicio");
    }

    private Date getFim() {
        return intervalos.get(filtroSelecionado).get("fim");
    }

    public Object getListaSelectItemPeriodo() {
        return opcoesFiltroData;
    }

    public void carregarFaturamento() throws Exception {
        if (filtroSelecionado == 4 || isDeslogado())
            carregaInformacoesSimulador();
        else
            carregaInformacoesDoBanco();
    }

    private void carregaInformacoesDoBanco() throws Exception {
        detalhesFaturamento = getFacade().getMovPagamento().consultarItensFaturamento(getInicio(), getFim());
    }

    public List<ItemFaturamentoTO> getDetalhesFaturamento() {
        return detalhesFaturamento;
    }

    public void setFiltroSelecionado(int filtroSelecionado) throws Exception {
        this.filtroSelecionado = filtroSelecionado;
    }

    public int getFiltroSelecionado() {
        return filtroSelecionado;
    }

    public boolean isInformandoTaxas() {
        return passoAutal == 1;
    }

    public boolean isInformandoMovimentacao() {
        return passoAutal == 2;
    }

    public boolean isExibindoResultado() {
        return passoAutal == 3;
    }

    public void proximoPasso()  {
        if ((isInformandoTaxas() && validarTaxas()) || (isInformandoMovimentacao() && validarFaturamentos()))
            this.passoAutal++;
    }

    private boolean validarTaxas(){
        for(ItemFaturamentoTO item: detalhesFaturamento) {
            if (!item.isPossuiTaxa()) {
                montarErro( new Exception("Preencha as taxas que estão zeradas"));
                return false;
            }
        }

        limparMsg();
        return true;
    }

    private boolean validarFaturamentos() {
        for(ItemFaturamentoTO item: detalhesFaturamento) {
            if (!item.isValorInformado()) {
                montarErro(new Exception("Preencha os faturamentos que estão zerados"));
                return false;
            }
        }

        limparMsg();
        return true;
    }

    public void passoAnterior() {
        this.passoAutal--;
    }

    public String getDescricaoFiltro() {
        if (filtroSelecionado < 1)
            return "";

        return opcoesFiltroData.get(filtroSelecionado-1).getLabel();
    }

    private BigDecimal getEconomiaNoPeriodoFiltrado(){
        BigDecimal total = new BigDecimal("0");

        for(ItemFaturamentoTO item: detalhesFaturamento) {
            total = total.add(item.getEconomia());
        }

        return total;
    }

    public void novaSimulacao() throws Exception {
        this.passoAutal = 1;
        inicializar();
    }


    public BigDecimal getEconomiaMensal() {
        if (filtroSelecionado != 3)
            return getEconomiaNoPeriodoFiltrado();

        return getEconomiaNoPeriodoFiltrado().divide(new BigDecimal("6.00"), 2, BigDecimal.ROUND_CEILING);
    }

    public BigDecimal getEconomiaAnual() {
        if (filtroSelecionado != 3)
            return getEconomiaNoPeriodoFiltrado().multiply(new BigDecimal(12));

        return getEconomiaNoPeriodoFiltrado().multiply(new BigDecimal(2));
    }
}

package controle.financeiro;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;

public class ConsultaCupomTO extends SuperTO {

    private static final long serialVersionUID = 134318812131384877L;
    private Date dataVendaIni;
    private Date dataVendaFim;
    private Date dataImpressaoIni;
    private Date dataImpressaoFim;
    private int statusCupom;
    private int codigoRecibo;
    private String matricula = "";
    private String nome = "";

    /**
     * @return the dataVendaIni
     */
    public Date getDataVendaIni() {
        return dataVendaIni;
    }

    /**
     * @param dataVendaIni the dataVendaIni to set
     */
    public void setDataVendaIni(Date dataVendaIni) {
        this.dataVendaIni = dataVendaIni;
    }

    /**
     * @return the dataVendaFim
     */
    public Date getDataVendaFim() {
        return dataVendaFim;
    }

    /**
     * @param dataVendaFim the dataVendaFim to set
     */
    public void setDataVendaFim(Date dataVendaFim) {
        this.dataVendaFim = dataVendaFim;
    }

    /**
     * @return the dataImpressaoIni
     */
    public Date getDataImpressaoIni() {
        return dataImpressaoIni;
    }

    /**
     * @param dataImpressaoIni the dataImpressaoIni to set
     */
    public void setDataImpressaoIni(Date dataImpressaoIni) {
        this.dataImpressaoIni = dataImpressaoIni;
    }

    /**
     * @return the dataImpressaoFim
     */
    public Date getDataImpressaoFim() {
        return dataImpressaoFim;
    }

    /**
     * @param dataImpressaoFim the dataImpressaoFim to set
     */
    public void setDataImpressaoFim(Date dataImpressaoFim) {
        this.dataImpressaoFim = dataImpressaoFim;
    }

    /**
     * @return the statusCupom
     */
    public int getStatusCupom() {
        return statusCupom;
    }

    /**
     * @param statusCupom the statusCupom to set
     */
    public void setStatusCupom(int statusCupom) {
        this.statusCupom = statusCupom;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer("");
        sb.append("Data de venda de " + getDataVendaIni() + " até " + getDataVendaFim());
        sb.append(" Data de impressao de " + getDataImpressaoIni() + " até " + getDataImpressaoFim());
        sb.append(" Na situação " + getStatusCupom());
        return sb.toString();
    }

    /**
     * @return the codigoRecibo
     */
    public int getCodigoRecibo() {
        return codigoRecibo;
    }

    /**
     * @param codigoRecibo the codigoRecibo to set
     */
    public void setCodigoRecibo(int codigoRecibo) {
        this.codigoRecibo = codigoRecibo;
    }

    /**
     * @return the matricula
     */
    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    /**
     * @param matricula the matricula to set
     */
    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    /**
     * @return the nome
     */
    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    /**
     * @param nome the nome to set
     */
    public void setNome(String nome) {
        this.nome = nome;
    }
}

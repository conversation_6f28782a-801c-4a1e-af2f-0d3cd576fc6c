package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.ObjetoTreeTO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.filtros.FiltroDreTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.CentroCustosDRE;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import relatorio.negocio.jdbc.financeiro.RelatorioDRE;
import servicos.bi.CrossTabExcelLinha;
import servicos.bi.CrossTablePOIGenerics;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class DREControle extends FinanControle {
    private int indiceNaoInformado;
    private Map<String, TipoEquivalenciaDRE> equivalencias;
    private Boolean centroCustos = Boolean.TRUE;
    private List<CentroCustosDRE> centros;
    private List<MesProcessar> meses;
    private String codigoPlano;
    private String codigoCentro;
    private List<DemonstrativoFinanceiro> totais = new ArrayList<DemonstrativoFinanceiro>();
    private Double pontoEquilibrio = 0.0;
    private Double realizadoPeriodo = 0.0;
    private Double naoRealizado = 0.0;
    private Double resultadoExercicio = 0.0;
    private Double resultadoExercicioPorc = 0.0;
    private List<CrossTabExcelLinha> relatorioExcel;
    private List<ObjetoTreeTO> listaCentroCustoTree;
    private String filtroContas;
    private List<ContaVO> contasFiltro;
    private String planosSelecionados;
    private String centrosSelecionados;
    private List<ObjetoTreeTO> listaPlanoContaTree;
    private List<Integer> codigosCentrosSelecionados = new ArrayList<Integer>();
    private int contAjusteTree = 0;



    public DREControle() {
        try {
            inicializarEmpresa();
        } catch (Exception ex) {
            Logger.getLogger(DREControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex);
        }
        criarListaTipoFonteDadosDF();
    }

    public void gerarDemonstrativoComThread() {
        try {
            validarDados();
            setarFiltrosSelecionados();
            gerarDemonstrativo(this.tipoRelatorioDf, !centroCustos, isApresentarDevolucoesRel());
            montarArvoreFiltros();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void exportarExcel() throws  Exception{
        try {
            montarListaExportExcel();
            CrossTablePOIGenerics crossTablePOIGenerics = new CrossTablePOIGenerics();
            crossTablePOIGenerics.setFormatoDados("[$R$-416]#,##0.00;[Red][$R$-416]#,##0.00");
            crossTablePOIGenerics.setTipoDados(2);//  0  -> valor Texto   /  1 -> valor Inteiro / 2 -> valor Double
            crossTablePOIGenerics.gerarXLS(relatorioExcel, "RelatorioDRECentroCustos", true);
            crossTablePOIGenerics.getWb().getSheetAt(0).setDefaultColumnWidth(20);
            crossTablePOIGenerics.getWb().getSheetAt(0).setColumnWidth(0, 256 * 50);

            crossTablePOIGenerics.exportarXLS();
        }catch (Exception erro){
            System.out.println(erro);
        }

    }


    public void montarListaExportExcel(){
            if(centroCustos){
                montarListaExportExcelCentroCusto();
            }else{
                montarListaExportExcelPeriodoMeses();
            }
    }

    public void montarListaExportExcelPeriodoMeses(){

        relatorioExcel = new ArrayList<CrossTabExcelLinha>();
        if(!listaDFBrowser.isEmpty()){
            //Header Centro Custos
            CrossTabExcelLinha header = new CrossTabExcelLinha();
            header.setDescricao("");

            for(MesProcessar item : listaDFBrowser.get(0).getListaMeses()){
                header.addCelula(item.getNomeMes());
            }
            header.addCelula("Total");
            header.addCelula("%");
            relatorioExcel.add(header);
            for(DemonstrativoFinanceiro demo : listaDFBrowser){
                CrossTabExcelLinha linha = new CrossTabExcelLinha();
                String espaco = "";
                for(int e = 0; e < demo.getCodigoAgrupador().length() ; e++)
                    espaco+=" ";

                linha.setDescricao(espaco + demo.getNomeAgrupador());
                for(MesProcessar mes : demo.getListaMeses()){
                    linha.addCelula(valorExcelMonetario(mes.getValorApresentar()));
                }
                linha.addCelula(valorExcelMonetario(demo.getTotalTodosMesesApresentarTela()));
                linha.addCelula(valorExcelMonetario(demo.getPercentualApresentar()));


                relatorioExcel.add(linha);
            }
            CrossTabExcelLinha pontoEquilibrio = new CrossTabExcelLinha();
            pontoEquilibrio.setDescricao("Ponto Equilíbrio");
            pontoEquilibrio.setSumarioDemonstrativo(true);
            pontoEquilibrio.addCelula(getPontoEquilibrioApresentar());

            CrossTabExcelLinha realizadoPeriodo = new CrossTabExcelLinha();
            realizadoPeriodo.setDescricao("Ponto de equilíbrio");
            realizadoPeriodo.setSumarioDemonstrativo(true);
            realizadoPeriodo.addCelula(getRealizadoApresentar());

            CrossTabExcelLinha naoRealizado = new CrossTabExcelLinha();
            naoRealizado.setDescricao("Não realizado");
            naoRealizado.setSumarioDemonstrativo(true);
            naoRealizado.addCelula(getNaoRealizadoApresentar());

            CrossTabExcelLinha resultadoExercicio = new CrossTabExcelLinha();
            resultadoExercicio.setDescricao("Resultado do exercício");
            resultadoExercicio.setSumarioDemonstrativo(true);
            resultadoExercicio.addCelula(getResultadoExercicioApresentar());

            relatorioExcel.add(pontoEquilibrio);
            relatorioExcel.add(realizadoPeriodo);
            relatorioExcel.add(naoRealizado);
            relatorioExcel.add(resultadoExercicio);
        }

    }
    public String valorExcelMonetario(String valor){
        CharSequence negativo = "(-)";
        if (valor.contains(negativo)){
           return valor.replace("(-)", "-");
        }
        return valor.replace(".","").replace(",",".");
    }
    public void montarListaExportExcelCentroCusto(){
        relatorioExcel = new ArrayList<CrossTabExcelLinha>();

        if(!listaDFBrowser.isEmpty()){
            //Header Centro Custos
            CrossTabExcelLinha header = new CrossTabExcelLinha();
            header.setDescricao("");

            for(CentroCustosDRE item : listaDFBrowser.get(0).getListaCentros()){
                header.addCelulaRotulo(item.getNomeCentro());
            }
            header.addCelulaColuna("Total");
            header.addCelulaColuna("%");
            relatorioExcel.add(header);
            for(DemonstrativoFinanceiro demo : listaDFBrowser){
                CrossTabExcelLinha linha = new CrossTabExcelLinha();
                String espaco = "";
                for(int e = 0; e < demo.getCodigoAgrupador().length() ; e++)
                espaco+=" ";

                linha.setDescricao(espaco + demo.getNomeAgrupador());
                for(CentroCustosDRE centroCustosDRE : demo.getListaCentros()){
                  linha.addCelula(valorExcelMonetario(centroCustosDRE.getValorApresentar()));
                }
                linha.addCelula(valorExcelMonetario(demo.getTotalTodosMesesApresentarTela()));
                linha.addCelula(valorExcelMonetario(demo.getPercentualApresentar()));


                relatorioExcel.add(linha);
            }
            CrossTabExcelLinha pontoEquilibrio = new CrossTabExcelLinha();
            pontoEquilibrio.setDescricao("Ponto Equilíbrio");
            pontoEquilibrio.setSumarioDemonstrativo(true);
            pontoEquilibrio.addCelula(valorExcelMonetario(getPontoEquilibrioApresentar()));

            CrossTabExcelLinha realizadoPeriodo = new CrossTabExcelLinha();
            realizadoPeriodo.setDescricao("Realizado no período");
            realizadoPeriodo.setSumarioDemonstrativo(true);
            realizadoPeriodo.addCelula(valorExcelMonetario(getRealizadoApresentar()));

            CrossTabExcelLinha naoRealizado = new CrossTabExcelLinha();
            naoRealizado.setDescricao("Não realizado");
            naoRealizado.setSumarioDemonstrativo(true);
            naoRealizado.addCelula(valorExcelMonetario(getNaoRealizadoApresentar()));

            CrossTabExcelLinha resultadoExercicio = new CrossTabExcelLinha();
            resultadoExercicio.setDescricao("Resultado do exercício");
            resultadoExercicio.setSumarioDemonstrativo(true);
            resultadoExercicio.addCelula(valorExcelMonetario(getResultadoExercicioApresentar()));
            resultadoExercicio.addCelula(getResultadoExercicioPorcApresentar());

            relatorioExcel.add(pontoEquilibrio);
            relatorioExcel.add(realizadoPeriodo);
            relatorioExcel.add(naoRealizado);
            relatorioExcel.add(resultadoExercicio);
        }
    }

    public void paintFoto(OutputStream out, Object data) throws IOException, Exception {
        if (UteisValidacao.emptyNumber(this.getEmpresaVO().getCodigo())) {
            this.getEmpresaVO().setCodigo(getEmpresaLogado().getCodigo());
        }
        this.getEmpresaVO().setFoto(getFacade().getEmpresa().obterFoto(getKey(), 
                this.getEmpresaVO().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA));
        SuperControle.paintFoto(out, this.getEmpresaVO().getFoto());
    }

    public void setarTotais(Double cemPorCento) {
        DemonstrativoFinanceiro receita = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.RECEITA_BRUTA, listaDFBrowser);
        DemonstrativoFinanceiro custos = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.CUSTOS_ESPECIFICOS, listaDFBrowser);
        DemonstrativoFinanceiro lucroBruto = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.LUCRO_BRUTO, listaDFBrowser);
        DemonstrativoFinanceiro despesas = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.DESPESAS_OPERACIONAIS, listaDFBrowser);
        DemonstrativoFinanceiro lucroOperacional = DemonstrativoFinanceiro.obterTituloEquivalencia(TipoEquivalenciaDRE.LUCRO_OPERACIONAL, listaDFBrowser);
        if (centroCustos) {
            for (CentroCustosDRE ccd : receita.getListaCentros()) {
                CentroCustosDRE custosDRE = CentroCustosDRE.obter(ccd, custos.getListaCentros());
                CentroCustosDRE lucroBrt = CentroCustosDRE.obter(ccd, lucroBruto.getListaCentros());

                lucroBrt.setTotal(ccd.getTotal() - custosDRE.getTotalPositivo());
                lucroBruto.setTotalTodosMeses(lucroBruto.getTotalTodosMeses() + lucroBrt.getTotal());
            }
            for (CentroCustosDRE ccd : despesas.getListaCentros()) {
                CentroCustosDRE lucroBrt = CentroCustosDRE.obter(ccd, lucroBruto.getListaCentros());
                CentroCustosDRE lucroOp = CentroCustosDRE.obter(ccd, lucroOperacional.getListaCentros());

                lucroOp.setTotal(lucroBrt.getTotal() - ccd.getTotalPositivo());

                lucroOperacional.setTotalTodosMeses(lucroOperacional.getTotalTodosMeses() + lucroOp.getTotal());
            }
        } else {
            for (MesProcessar mes : receita.getListaMeses()) {
                MesProcessar custosDRE = MesProcessar.obter(mes, custos.getListaMeses());
                MesProcessar lucroBrt = MesProcessar.obter(mes, lucroBruto.getListaMeses());

                lucroBrt.setTotal(mes.getTotal() - custosDRE.getTotalPositivo());
                lucroBruto.setTotalTodosMeses(lucroBruto.getTotalTodosMeses() + lucroBrt.getTotal());
            }
            for (MesProcessar mes : despesas.getListaMeses()) {
                MesProcessar lucroBrt = MesProcessar.obter(mes, lucroBruto.getListaMeses());
                MesProcessar lucroOp = MesProcessar.obter(mes, lucroOperacional.getListaMeses());

                lucroOp.setTotal(lucroBrt.getTotal() - mes.getTotalPositivo());

                lucroOperacional.setTotalTodosMeses(lucroOperacional.getTotalTodosMeses() + lucroOp.getTotal());
            }
        }

        lucroBruto.setPercentual(cemPorCento != 0.0 ? lucroBruto.getTotalTodosMesesPositivo() / cemPorCento : 0.0);
        lucroOperacional.setPercentual(cemPorCento != 0.0 ? lucroOperacional.getTotalTodosMesesPositivo() / cemPorCento : 0.0);
        pontoEquilibrio = lucroBruto.getTotalTodosMeses() == 0 ? 0.0 :
                (despesas.getTotalTodosMeses() / lucroBruto.getTotalTodosMeses()) * receita.getTotalTodosMeses();
        pontoEquilibrio = pontoEquilibrio < 0 ? pontoEquilibrio * -1 : pontoEquilibrio;
        realizadoPeriodo = receita.getTotalTodosMeses();
        naoRealizado = realizadoPeriodo - pontoEquilibrio;
        double despesasOp = despesas.getTotalTodosMeses() < 0.0 ? despesas.getTotalTodosMeses() * -1 : despesas.getTotalTodosMeses();
        resultadoExercicio = lucroBruto.getTotalTodosMeses() - despesasOp;
        resultadoExercicioPorc = (resultadoExercicio * 100) / cemPorCento;

    }

    public List<DemonstrativoFinanceiro> obterDemonstrativos(
            TipoRelatorioDF tipoRelatorioDF,
            boolean gerarRelatorioUsandoThread,
            boolean apresentarDevolucoesRel,
            Date dataInicio,
            Date dataFim,
            Integer codigoEmpresa
    ) throws Exception {
        setDataInicio(dataInicio);
        setDataFim(dataFim);

        getEmpresaVO().setCodigo(codigoEmpresa);
        montarArvoreFiltros();
        gerarDemonstrativo(tipoRelatorioDF, gerarRelatorioUsandoThread, apresentarDevolucoesRel);

        return listaDF;
    }


    private void gerarDemonstrativo(TipoRelatorioDF tipoRelatorioDF, boolean gerarRelatorioUsandoThread, boolean apresentarDevolucoesRel) {
        ContadorTempo.iniciarContagem();
        boolean parametrosIncorretos = true;
        try {
            setMensagemDetalhada("", "");
            Calendar dataInicialRel = Calendario.getInstance();
            if (tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA ||
                    tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA_QUITADA ||
                    tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) {
                setDataInicio(Uteis.obterPrimeiroDiaMes(getDataInicio()));
            }
            dataInicialRel.setTime(getDataInicio());
            if (dataInicialRel.get(Calendar.YEAR) < 1990) {
                throw new Exception("Data de início do relatório inválida. Informe outra data.");
            }

            Calendar dataFinalRel = Calendario.getInstance();
            if (tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA ||
                    tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA_QUITADA ||
                    tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA_NAO_QUITADA) {
                setDataFim(Uteis.obterUltimoDiaMes(getDataFim()));
            }
            dataFinalRel.setTime(getDataFim());
            if (Calendario.diferencaEmDias(getDataInicio(), getDataFim()) > 365) {
                throw new Exception("Período inválido. Informe um período que não ultrapasse 1 ano.");
            }


            if (!isMostrarCampoEmpresa()) {
                setEmpresaVO(this.getEmpresaLogado());
            }
            // Consultar o nome da empresa.
            if(getEmpresaVO() != null && !UteisValidacao.emptyNumber(getEmpresaVO().getCodigo())){
                EmpresaVO objEmpresa = getFacade().getEmpresa().consultarPorCodigo(this.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeEmpresa(objEmpresa.getNome());
            }

            parametrosIncorretos = false;
            setHouveErroNoRelatorio(false);

            // Montar a string do Período do relatório.
            SimpleDateFormat sdf2 = new SimpleDateFormat("dd/MM/yyyy");
            setPeriodoRelatorio(sdf2.format(getDataInicio()) + " à " + sdf2.format(getDataFim()));

            relatorioDRE = new RelatorioDRE();
            montarFiltroContas();
            if ((!filtroContas.isEmpty()) || (codigosCentrosSelecionados.size() > 0)) {
                notificarRecursoEmpresa(RecursoSistema.FILTRO_CONTAS_CENTRO_DE_CUSTO_DRE);
            }
            listaDF = relatorioDRE.gerarDRE(tipoRelatorioDF,
                    dataInicialRel,
                    dataFinalRel,
                    this.getEmpresaVO().getCodigo(),
                    codigosCentrosSelecionados,
                    gerarRelatorioUsandoThread,
                    this.getTipoFonteDadosDF(),
                    getConfFinanceiro().getUsarCentralEventos(),
                    this.isAgruparValorProdutoMMasModalidades(),
                    filtroContas, apresentarDevolucoesRel);
            setCentros(getFacade().getFinanceiro().getCentroCusto().obterCentroCustos());
            getCentros().add(new CentroCustosDRE(0, "999", "Não Atribuído"));
            salvarFiltroSessao();

            // Após processar o Demonstrativo, então exibir na tela a Lista do Demonstrativo Financeiro
            criarListaParaEnviarAoBrowser(true, false);
            List<DemonstrativoFinanceiro> listaDRE = new ArrayList<DemonstrativoFinanceiro>();
            double cemPorCento = listaDFBrowser.get(0).getTotalTodosMeses();

            listaDFBrowser.get(0).setPercentual(1.0);
            for (DemonstrativoFinanceiro df : listaDFBrowser) {
                if (df.getTotalTodosMeses() != 0.0 || df.getDre()) {
                    df.setPercentual(cemPorCento != 0.0 ? (df.getTotalTodosMesesPositivo() / cemPorCento) : 0.0);
                    listaDRE.add(df);
                }
            }
            listaDFBrowser = listaDRE;
            setarTotais(cemPorCento);
            //Verificação para saber qual tipo de consulta está sendo realizada e com isto determinar qual está demorando mais ao notificarEmpresa
            if (tipoRelatorioDF == TipoRelatorioDF.RECEITA) {
                notificarRecursoEmpresa(RecursoSistema.DRE_RECEITA, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim())) + 1);
            }
            if (tipoRelatorioDF == TipoRelatorioDF.COMPETENCIA) {
                notificarRecursoEmpresa(RecursoSistema.DRE_COMPETENCIA, ContadorTempo.encerraContagem(), (Calendario.diferencaEmMeses(getDataInicio(),getDataFim()))+1);
            }
            if (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO_DE_CAIXA) {
                notificarRecursoEmpresa(RecursoSistema.DRE_FATURAMENTO_RECEBIDO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim())) + 1);
            }
            if (tipoRelatorioDF == TipoRelatorioDF.FATURAMENTO) {
                notificarRecursoEmpresa(RecursoSistema.DRE_FATURAMENTO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim())) + 1);
            }
            if (tipoRelatorioDF == TipoRelatorioDF.RECEITAPROVISAO) {
                notificarRecursoEmpresa(RecursoSistema.DRE_RECEITAPROVISAO, ContadorTempo.encerraContagem(), (Calendario.diferencaEmDias(getDataInicio(), getDataFim())) + 1);
            }

        } catch (Exception e) {
            setHouveErroNoRelatorio(true);
            if (parametrosIncorretos) {
                setMensagemDetalhada(e.getMessage());
            } else {
                setMensagemDetalhada("msg_erro", "Não foi possível gerar D.R.E: Classe do erro: " + e.getMessage());
            }

        }

    }

    public void visualizarLancamentos() throws Exception {
        int indexOf = listaDFBrowser.indexOf(new DemonstrativoFinanceiro(codigoPlano));
        if (indexOf >= 0) {
            DemonstrativoFinanceiro df = listaDFBrowser.get(indexOf);
            int indexOfCentro = df.getListaCentros().indexOf(new CentroCustosDRE(Integer.valueOf(codigoCentro)));
            if (indexOfCentro >= 0) {
                DemonstrativoFinanceiroControle dfControl = (DemonstrativoFinanceiroControle) getControlador(DemonstrativoFinanceiroControle.class.getSimpleName());
                dfControl.setDre(true);
                dfControl.setConsultaPaginada(true);
                dfControl.setPaginada(true);
                dfControl.setListaLancamentosDF(df.getListaCentros().get(indexOfCentro).getLancamentos());
                dfControl.setNomeAgrupadorSelecionado(df.getNomeAgrupador());
                dfControl.setTotalSelecionado(df.getListaCentros().get(indexOfCentro).getTotal());
                dfControl.setTotalRegistros(df.getListaCentros().get(indexOfCentro).getLancamentos().size());
                dfControl.setDemonstrativoFinanceiroSelecionado(df);
                Calendar inicio = Calendar.getInstance();
                inicio.setTime(getDataInicio());
                dfControl.totalizadorMesSelecionado = new TotalizadorMesDF();
                dfControl.totalizadorMesSelecionado.setMesProcessar(new MesProcessar());
                dfControl.totalizadorMesSelecionado.getMesProcessar().setDataIni(inicio);
                Calendar fim = Calendar.getInstance();
                fim.setTime(getDataFim());
                dfControl.totalizadorMesSelecionado.getMesProcessar().setDataFim(fim);
            }
        }

    }

    public void visualizarLancamentosMes() throws Exception {
        int indexOf = listaDF.indexOf(new DemonstrativoFinanceiro(getCodigoAgrupadorSelecionado()));
        if (indexOf >= 0) {
            DemonstrativoFinanceiro df = listaDF.get(indexOf);
            TotalizadorMesDF totalizadorMes = new TotalizadorMesDF();
            totalizadorMes.setMesProcessar(new MesProcessar());
            totalizadorMes.getMesProcessar().setMesAno(this.getMesSelecionado());
            int indiceLanc = df.getListaTotalizadorMeses().indexOf(totalizadorMes);
            DemonstrativoFinanceiroControle dfControl = (DemonstrativoFinanceiroControle) getControlador(DemonstrativoFinanceiroControle.class.getSimpleName());
            dfControl.setDre(true);
            dfControl.setNomeAgrupadorSelecionado(df.getNomeAgrupador());
            dfControl.setDemonstrativoFinanceiroSelecionado(df);
            Calendar inicio = Calendar.getInstance();
            inicio.setTime(getDataInicio());
            dfControl.totalizadorMesSelecionado = new TotalizadorMesDF();
            dfControl.totalizadorMesSelecionado.setMesProcessar(new MesProcessar());
            dfControl.totalizadorMesSelecionado.getMesProcessar().setDataIni(inicio);
            Calendar fim = Calendar.getInstance();
            fim.setTime(getDataFim());
            dfControl.totalizadorMesSelecionado.getMesProcessar().setDataFim(fim);

            if (indiceLanc >= 0) {
                totalizadorMes = df.getListaTotalizadorMeses().get(indiceLanc);
                totalizadorMesSelecionado = totalizadorMes;
                Set<LancamentoDF> listaLancamentos = null;
                if (this.getTipoListaLancamentosMostrar() == 1) {
                    dfControl.setTotalSelecionado(totalizadorMes.getTotalNivel());
                    dfControl.setTotalSelecionadoEntrada(Formatador.formatarValorMonetarioSemMoeda(totalizadorMes.getTotalEntradaNivel()));
                    dfControl.setTotalSelecionadoSaida(totalizadorMes.getTotalSaidaNivel());
                    listaLancamentos = totalizadorMes.getListaLancamentos();
                    dfControl.setNomeAgrupadorSelecionado(df.getNomeAgrupador());
                } else {
                    dfControl.setTotalSelecionado(totalizadorMes.getTotalNaoAtribuido());
                    dfControl.setTotalSelecionadoEntrada(Formatador.formatarValorMonetarioSemMoeda(totalizadorMes.getTotalEntradaNaoAtribuido()));
                    dfControl.setTotalSelecionadoSaida(totalizadorMes.getTotalSaidaNaoAtribuido());

                    listaLancamentos = totalizadorMes.getListaLancamentosNaoAtribuido();
                    dfControl.setNomeAgrupadorSelecionado(df.getNomeAgrupador() + " - Não Atribuido");
                }
                dfControl.setTotalRegistros(listaLancamentos.size());
                dfControl.listaLancamentosDF = new ArrayList<LancamentoDF>();
                for (LancamentoDF lancamento : listaLancamentos) {
                    dfControl.listaLancamentosDF.add(lancamento);
                }
            }
        }

    }

    public String abrirDRE() {
        try {
            setConfFinanceiro(getFacade().getConfiguracaoFinanceiro().consultar());
            montarArvoreFiltros();
            montarListaSelectItemEmpresa();
            criarListaTipoFonteDadosDF();
            setAgruparValorProdutoMMasModalidades(false);
            setCentroCustos(false);
            restauraFiltros();
        } catch (Exception e) {
            setMensagem(e.getMessage());
        }
        return "dre";
    }

    public Map<String, TipoEquivalenciaDRE> getEquivalencias() {
        return equivalencias;
    }

    public void setEquivalencias(Map<String, TipoEquivalenciaDRE> equivalencias) {
        this.equivalencias = equivalencias;
    }

    public Boolean getCentroCustos() {
        return centroCustos;
    }

    public void setCentroCustos(Boolean centroCustos) {
        this.centroCustos = centroCustos;
    }

    public List<CentroCustosDRE> getCentros() {
        return centros;
    }

    public void setCentros(List<CentroCustosDRE> centros) {
        this.centros = centros;
    }

    public String getCodigoPlano() {
        return codigoPlano;
    }

    public void setCodigoPlano(String codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getCodigoCentro() {
        return codigoCentro;
    }

    public void setCodigoCentro(String codigoCentro) {
        this.codigoCentro = codigoCentro;
    }

    public List<DemonstrativoFinanceiro> getTotais() {
        return totais;
    }

    public void setTotais(List<DemonstrativoFinanceiro> totais) {
        this.totais = totais;
    }

    public Double getPontoEquilibrio() {
        return pontoEquilibrio;
    }

    public void setPontoEquilibrio(Double pontoEquilibrio) {
        this.pontoEquilibrio = pontoEquilibrio;
    }

    public String getPontoEquilibrioApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.pontoEquilibrio);
    }

    public Double getRealizadoPeriodo() {
        return realizadoPeriodo;
    }

    public void setRealizadoPeriodo(Double realizadoPeriodo) {
        this.realizadoPeriodo = realizadoPeriodo;
    }

    public String getRealizadoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.realizadoPeriodo);
    }

    public Double getNaoRealizado() {
        return naoRealizado;
    }

    public void setNaoRealizado(Double naoRealizado) {
        this.naoRealizado = naoRealizado;
    }

    public String getNaoRealizadoApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.naoRealizado);
    }

    public Double getResultadoExercicio() {
        return resultadoExercicio;
    }

    public void setResultadoExercicio(Double resultadoExercicio) {
        this.resultadoExercicio = resultadoExercicio;
    }

    public String getResultadoExercicioApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(this.resultadoExercicio);
    }

    public String getResultadoExercicioPorcApresentar() {
        return Formatador.formatarValorPercentual(resultadoExercicioPorc / 100, 2);
    }

    public List<MesProcessar> getMeses() {
        return meses;
    }

    public void setMeses(List<MesProcessar> meses) {
        this.meses = meses;
    }

    public String getCorPE() {
        return this.pontoEquilibrio < 0.0 ? "red" : "blue";
    }

    public String getCorRP() {
        return this.realizadoPeriodo < 0.0 ? "red" : "blue";
    }

    public String getCorNR() {
        return this.naoRealizado < 0.0 ? "red" : "blue";
    }

    public String getCorRE() {
        return this.resultadoExercicio < 0.0 ? "red" : "blue";
    }

    public Double getResultadoExercicioPorc() {
        return resultadoExercicioPorc;
    }

    public void setResultadoExercicioPorc(Double resultadoExercicioPorc) {
        this.resultadoExercicioPorc = resultadoExercicioPorc;
    }

    private void validarDados() throws Exception {
        if (getDataInicio() != null && getDataInicio().after(getDataFim())) {
            throw new Exception("Data Inicial não pode ser superior a Data Final.");
        }
    }

    public void salvarFiltroSessao() throws Exception {
        FiltroDreTO filtroDreTO = new FiltroDreTO();
        filtroDreTO.setTipoRelatorioDF(tipoRelatorioDf);
        filtroDreTO.setTipoVisualizacao(this.getTipoVisualizacao());
        filtroDreTO.setDataInicioRelatorio(this.getDataInicio());
        filtroDreTO.setDataFimRelatorio(this.getDataFim());
        filtroDreTO.setCodEmpresa(getEmpresaVO().getCodigo());
        filtroDreTO.setTipoFonteDadosDF(this.getTipoFonteDadosDF());
        filtroDreTO.setAgruparValorProdutoMMasModalidade(isAgruparValorProdutoMMasModalidades());
        filtroDreTO.setCentroCustos(this.getCentroCustos());
        filtroDreTO.setContasFiltro(this.contasFiltro);

        JSFUtilities.storeOnSession(FiltroDreTO.class.getName(), filtroDreTO);
    }

    public void restauraFiltros() {
        FiltroDreTO filtroSessao = (FiltroDreTO) JSFUtilities.getFromSession(FiltroDreTO.class.getName());
        if (filtroSessao != null) {
            this.setTipoRelatorioDf(filtroSessao.getTipoRelatorioDF());
            this.setTipoVisualizacao(filtroSessao.getTipoVisualizacao());
            this.setDataInicio(filtroSessao.getDataInicioRelatorio());
            this.setDataFim(filtroSessao.getDataFimRelatorio());
            this.getEmpresaVO().setCodigo(filtroSessao.getCodEmpresa());
            this.setTipoFonteDadosDF(filtroSessao.getTipoFonteDadosDF());
            this.setCentroCustos(filtroSessao.isCentroCustos());
            this.contasFiltro = filtroSessao.getContasFiltro();
            setAgruparValorProdutoMMasModalidades(filtroSessao.isAgruparValorProdutoMMasModalidade());

        }
    }

    /**
     * @return the listaCentroCustoTree
     */
    public List<ObjetoTreeTO> getListaCentroCustoTree() {
        return listaCentroCustoTree;
    }

    /**
     * @param listaCentroCustoTree the listaCentroCustoTree to set
     */
    public void setListaCentroCustoTree(List<ObjetoTreeTO> listaCentroCustoTree) {
        this.listaCentroCustoTree = listaCentroCustoTree;
    }

    /**
     * @return the centrosSelecionados
     */
    @Override
    public String getCentrosSelecionados() {
        if (centrosSelecionados == null) {
            centrosSelecionados = "";
        }
        return centrosSelecionados;
    }

    /**
     * @param centrosSelecionados the centrosSelecionados to set
     */
    @Override
    public void setCentrosSelecionados(String centrosSelecionados) {
        this.centrosSelecionados = centrosSelecionados;
    }

    /**
     * @return the planosSelecionados
     */
    public List<ContaVO> getContasFiltro() {
        return contasFiltro;
    }

    @Override
    public void setContasFiltro(List<ContaVO> contasFiltro) {
        this.contasFiltro = contasFiltro;
    }

    public void listarPlanoContas() throws Exception {
        this.setListaPlanoContaTree(new ArrayList<ObjetoTreeTO>());
        //consultar os planos de contas
        List<PlanoContaTO> planos = getFacade().getFinanceiro().getPlanoConta().consultar("", "",null, "");
        for (PlanoContaTO plano : planos) {
            ObjetoTreeTO obj = new ObjetoTreeTO();
            obj.setCodigoAgrupador(plano.getCodigoPlano());
            ajustarCodigoAgrupador(obj);
            obj.setCodigoEntidade(plano.getCodigo());
            obj.setNome(plano.getDescricao());
            //marcar como selecionado se o código estiver contido na lista de codigos selecionados
            //isto pq a caixa de filtros é renderizada sempre que o botão 'visualizar' é clicado
            if (this.getCodigosPlanosSelecionados().contains(obj.getCodigoEntidade())) {
                obj.setSelecionado(true);
            }
            this.getListaPlanoContaTree().add(obj);
        }
    }

    public void listarCentroCustos() throws Exception {
        this.setListaCentroCustoTree(new ArrayList<ObjetoTreeTO>());
        //consultar os centros de custos
        List<CentroCustoTO> centros = getFacade().getFinanceiro().getCentroCusto().consultar("", "", null);

        for (CentroCustoTO centro : centros) {
            ObjetoTreeTO obj = new ObjetoTreeTO();
            obj.setCodigoAgrupador(centro.getCodigoCentro());
            ajustarCodigoAgrupador(obj);
            obj.setCodigoEntidade(centro.getCodigo());
            obj.setNome(centro.getDescricao());
            //marcar como selecionado se o código estiver contido na lista de codigos selecionados
            //isto pq a caixa de filtros é renderizada sempre que o botão 'visualizar' é clicado
            if (this.getCodigosCentrosSelecionados().contains(obj.getCodigoEntidade())) {
                obj.setSelecionado(true);
            }
            this.getListaCentroCustoTree().add(obj);
        }
    }


    public void montarArvoreFiltros() throws Exception {
        listarPlanoContas();
        listarCentroCustos();
        setContasFiltro(getFacade().getFinanceiro().getConta().consultarContasSimples(getEmpresaLogado().getCodigo(), false));
        getContasFiltro().add(new ContaVO(-1));
    }
    public void ajustarCodigoAgrupador(ObjetoTreeTO obj) {
        String codigoAgrupador = obj.getCodigoAgrupador();
        //obter codigo pai
        Integer codigoPai = Integer.valueOf(codigoAgrupador.substring(0, 3));
        //verificar  se o código indica objeto filhos
        String filhos = codigoAgrupador.substring(3);
        //se o objeto indicar pai, atualizar o contador do filtro
        if (filhos.isEmpty()) {
            contAjusteTree++;
        }
        //a regra para ajuste do código é o tamanho do demonstrativo + o contador dos filtros + o código pai
        codigoPai = getListaDFBrowser().size() + contAjusteTree;
        codigoAgrupador = codigoPai.toString();
        //adicionar zeros ao código do pai
        while (codigoAgrupador.length() < 3) {
            codigoAgrupador = "0" + codigoAgrupador;
        }
        //adicionar os filhos
        codigoAgrupador += filhos;
        //atualizar o código agrupador
        obj.setCodigoAgrupador(codigoAgrupador);
    }
    /**
     * @return the listaPlanoContaTree
     */
    public List<ObjetoTreeTO> getListaPlanoContaTree() {
        if (listaPlanoContaTree == null) {
            listaPlanoContaTree = new ArrayList<ObjetoTreeTO>();
        }
        return listaPlanoContaTree;
    }

    /**
     * @param listaPlanoContaTree the listaPlanoContaTree to set
     */
    public void setListaPlanoContaTree(List<ObjetoTreeTO> listaPlanoContaTree) {
        this.listaPlanoContaTree = listaPlanoContaTree;
    }
    /**
     * @return the codigosCentrosSelecionados
     */
    public List<Integer> getCodigosCentrosSelecionados() {
        if (codigosCentrosSelecionados == null) {
            codigosCentrosSelecionados = new ArrayList<Integer>();
        }
        return codigosCentrosSelecionados;
    }

    /**
     * @param codigosCentrosSelecionados the codigosCentrosSelecionados to set
     */
    public void setCodigosCentrosSelecionados(List<Integer> codigosCentrosSelecionados) {
        this.codigosCentrosSelecionados = codigosCentrosSelecionados;
    }

    private List<Integer> obterFiltrosSelecionados(String superString) {
        List<Integer> lista = new ArrayList<Integer>();
        String[] listaParams = superString.split(";");
        for (String param : listaParams) {
            if (!param.equals("")) {
                lista.add(Integer.valueOf(param));
            }
        }
        return lista;
    }
    /**
     * <AUTHOR>
     * 18/02/2019
     */
    private void setarFiltrosSelecionados() {
        this.setCodigosCentrosSelecionados(new ArrayList<Integer>());
        this.setCodigosPlanosSelecionados(new ArrayList<Integer>());
        //obter os códigos dos centros e planos selecionados
        this.setCodigosCentrosSelecionados(obterFiltrosSelecionados(this.getCentrosSelecionados()));
        this.setCodigosPlanosSelecionados(obterFiltrosSelecionados(this.getPlanosSelecionados()));
    }
    private void montarFiltroContas(){
        filtroContas = "";
        if(contasFiltro == null || contasFiltro.isEmpty()){
            return;
        }
        for(ContaVO c : contasFiltro){
            if(c.getContaEscolhida()){
                filtroContas += "," + c.getCodigo();
            }
        }

        filtroContas = filtroContas.replaceFirst("\\,", "");
    }
}

package controle.financeiro;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.view.ComboBoxEmpresaControle;
import controle.crm.TreeViewColaboradorControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.MetaFinanceiraConsultorVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaVO;
import negocio.comuns.financeiro.MetaFinanceiraEmpresaValoresVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.controle.basico.BIControle;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class MetaFinanceiroControle extends SuperControleRelatorio {

    private EmpresaVO empresa = new EmpresaVO();
    private Mes mes = Mes.VAZIO;
    private int ano = Uteis.getAnoData(Calendario.hoje());
    private String descricao = "";
    private List<MetaFinanceiraEmpresaVO> metas = new ArrayList<MetaFinanceiraEmpresaVO>();
    private MetaFinanceiraEmpresaVO meta = new MetaFinanceiraEmpresaVO();
    private MetaFinanceiraConsultorVO consultor = new MetaFinanceiraConsultorVO();
    private String valorConsultaColaborador = "";
    private List<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
    private List<MetaFinanceiraEmpresaVO> historico = new ArrayList<MetaFinanceiraEmpresaVO>();
    private Date periodoDe = Calendario.hoje();
    private Date periodoAte = Calendario.hoje();
    private EmpresaVO empresaHistorico = new EmpresaVO();
    private boolean calcularMeta;
    private boolean imprimirListaMeta = false;
    private Date dataMetaAtualizada;
    private ConfiguracaoSistemaVO configuracaoSistema;

    public MetaFinanceiroControle() throws Exception {
        inicializarEmpresa();
        inicializarDados(true);
    }

    public void inicializarConfiguracaoSistema(){
        try {
            setConfiguracaoSistema((ConfiguracaoSistemaVO) JSFUtilities.getFromSession(JSFUtilities.CONFIGURACAO_SISTEMA));
        } catch (Exception e){
            throw e;
        }
    }

    protected void inicializarDados(boolean limparEmpresa) throws Exception {
        if (limparEmpresa) {
            empresa = new EmpresaVO();
        }
        mes = Mes.VAZIO;
        ano = Uteis.getAnoData(Calendario.hoje());
        descricao = "";
        metas = new ArrayList<MetaFinanceiraEmpresaVO>();
        this.imprimirListaMeta = false;
        this.calcularMeta = false;

        periodoDe = Uteis.getDate("01/01/" + Calendario.getInstance().get(Calendar.YEAR));
        periodoAte = Uteis.getDate("31/12/" + (Calendario.getInstance().get(Calendar.YEAR) + 1));
//        consultarMetas();
    }

    public String novo() {
        inicializarMeta();
        return "editarMetasFinanceiro";
    }

    private void inicializarMeta() {
        meta = new MetaFinanceiraEmpresaVO();
        // por padrão a tela terá 5 metas
        // 0 : #BF2A12
        // 1 : #FF6E00
        // 3 : #65B02A
        // 4 : #25A24F
        // 5 : #002C74
        meta.getValores().add(new MetaFinanceiraEmpresaValoresVO("#BF2A12"));
        meta.getValores().add(new MetaFinanceiraEmpresaValoresVO("#FF6E00"));
        meta.getValores().add(new MetaFinanceiraEmpresaValoresVO("#65B02A"));
        meta.getValores().add(new MetaFinanceiraEmpresaValoresVO("#25A24F"));
        meta.getValores().add(new MetaFinanceiraEmpresaValoresVO("#002C74"));
        meta.setAno(Uteis.getAnoData(Calendario.hoje()));
        consultor = new MetaFinanceiraConsultorVO();
    }

    public List<SelectItem> getListaSelectItemEmpresa() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(0, ""));
        try {
            List resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
        return objs;
    }

    public List<SelectItem> getListaSelectItemMeses() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        try {
            for (Mes obj : Mes.values()) {
                objs.add(new SelectItem(obj, obj.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
        }
        return objs;
    }

    private void inicializarEmpresa() throws Exception {
        setEmpresa((EmpresaVO) getEmpresaLogado().getClone(true));
        setEmpresaHistorico((EmpresaVO) getEmpresaLogado().getClone(true));


    }

    private void autorizarMetasFinanceiro() throws Exception {
        setMensagemDetalhada("", "");
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                        consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "CadastroMetas", "9.15 - Cadastro de Metas");
            }
        }
    }

    public void abrirTelaMetas() {
        try {
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.METAS_FINANCEIRO_VENDA.toString()));
            inicializarEmpresa();
            inicializarDados(false);
            autorizarMetasFinanceiro();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/metasFinanceiroCons.jsp', 'MetasFinanceiro', 850, 670);");
            limparMsg();
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public void abrirTelaMetasEdicao() {
        try {
            editarMeta();
            setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/metasFinanceiroForm.jsp', 'MetasFinanceiro', 850, 670);");
            limparMsg();
            setSucesso(false);
            setErro(false);
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }
    // Cores predefinidas para a metas financeiras
    // 0 : #BF2A12
    // 1 : #FF5555
    // 2 : #FF6E00
    // 3 : #65B02A
    // 4 : #25A24F
    // 5 : #002C74
    public void definirCorMetaAtingida(MetaFinanceiraEmpresaVO meta){
        Double metaAtingida = meta.getMetaAtingida();
        meta.setBateuTodosMetas(false);
        if(!meta.getValores().isEmpty()) {
          meta.setCorMetaAtingida("#FF0000");
        }
        for(int e = 0; e <  meta.getValores().size(); e++ ){
            MetaFinanceiraEmpresaValoresVO valor = meta.getValores().get(e);

            if(valor.getValor() <= metaAtingida){
                if(e == meta.getValores().size()){
                    meta.setBateuTodosMetas(true);
                }
                meta.setCorMetaAtingida(valor.getCor());
            }
//            valor.setCor(cores.get(e+1));
        }

    }
// * Ações da tela de consulta *************************************************
    public void consultarMetas() {
        try {
            validarDadosTela();
            metas = getFacade().getMetaFinanceiraEmpresa().consultarPorEmpresaPeriodo(
                    empresa.getCodigo(), periodoDe, periodoAte, descricao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setImprimirListaMeta(metas.size() > 0);
            for (MetaFinanceiraEmpresaVO mf : metas) {
                if (this.calcularMeta) {
                    atualizarValorMeta(mf, null, null,true, true, true, false, false);
                }
                ajustaMetaParaTela(mf);
                definirCorMetaAtingida(mf);
            }
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            metas = new ArrayList<MetaFinanceiraEmpresaVO>();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarDadosTela() throws Exception {
        periodoDe = Uteis.obterUltimoDiaMes(periodoDe);
        periodoAte = Uteis.obterUltimoDiaMes(periodoAte);
        if (Calendario.maior(periodoDe, periodoAte)) {
            throw new Exception("Ano/Mês inicial deve ser menor ou igual ao Ano/Mês final.");
        }
    }

    public void imprimirHistoricoMetaFinanceira() {
        try {
            Map<String, Object> parametros = new HashMap<String, Object>();
            prepareParams(parametros, metas);
            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            System.out.println(e.getMessage());
        }

    }

    public void limparValorMeta(final ActionEvent event) {
        int index = Integer.valueOf(event.getComponent().getAttributes().get("index").toString());
        MetaFinanceiraEmpresaValoresVO valor = getMeta().getValores().get(index);
        valor.setCor("");
        valor.setValor(0.0);
        valor.setObservacao("");

    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "RelatorioHistoricoMetasFinanceira.jrxml");
    }

    private String atualizaFiltros() throws Exception {
        StringBuilder sb = new StringBuilder();
        if ((this.empresa != null) && (this.empresa.getCodigo() > 0)) {
            // Pesquisar a empresa para pegar o nome
            this.empresa = getFacade().getEmpresa().consultarPorCodigo(this.empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            sb.append("Empresa: ").append(this.empresa.getNome());
        }
        if (this.periodoDe != null) {
            sb.append(" ").append("Período: ").append(getApresentarPeriodoDe()).append(" à ").append(getApresentarPeriodoAte());
        }
        if (!this.descricao.equals("")) {
            sb.append(" Descrição: ").append(this.descricao);
        }
        if (this.calcularMeta) {
            sb.append(" Calcular meta atingida: Sim");
        } else {
            sb.append(" Calcular meta atingida: Não");
        }

        return sb.toString();
    }

    private void prepareParams(Map<String, Object> params, List listaObjetos) throws Exception {
        EmpresaVO empresaVo = new EmpresaVO();
        if (this.getEmpresaLogado().getCodigo() != 0) {
            empresaVo = getFacade().getEmpresa().consultarPorChavePrimaria(this.getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        params.put("nomeRelatorio", "HistoricoMetasFinanRel");
        params.put("tituloRelatorio", "Relatorio Histórico Metas Financeira");
        params.put("nomeEmpresa", empresaVo.getNome());
        params.put("enderecoEmpresa", empresaVo.getEndereco());
        params.put("cidadeEmpresa", empresaVo.getCidade().getNome());
        params.put("nomeDesignIReport", getDesignIReportRelatorio());
        params.put("nomeUsuario", getUsuarioLogado().getNomeAbreviado());
        params.put("listaObjetos", listaObjetos);
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio());
        params.put("filtros", atualizaFiltros());
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
    }

    public String editarMeta() {
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            MetaFinanceiraEmpresaVO obj = getFacade().getMetaFinanceiraEmpresa().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            meta = (MetaFinanceiraEmpresaVO) obj.getClone(true);
            meta.setNovoObj(false);
            meta.registrarObjetoVOAntesDaAlteracao();
            calcularMetasParaConsultores();
            setMensagemID("msg_dados_editar");
            setSucesso(true);
            setErro(false);
            return "editarMetasFinanceiro";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void ajustaMetaParaTela(MetaFinanceiraEmpresaVO meta) {
        if (meta.getValores().size() == 5) {
            return;
        }
        for (int i = meta.getValores().size(); i <= 5; i++) {
            meta.getValores().add(new MetaFinanceiraEmpresaValoresVO());
        }
    }

// * Ações da tela de cadastro *************************************************
    public void consultarColaborador() {
        try {
            super.consultar();
            Integer codigoEmpresa = !UteisValidacao.emptyNumber(empresa.getCodigo()) ? empresa.getCodigo() : meta.getEmpresa().getCodigo();
            colaboradores = getFacade().getColaborador().consultarPorTipoColaboradorAtivoNomePessoa(valorConsultaColaborador,TipoColaboradorEnum.CONSULTOR,"AT",codigoEmpresa,false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            colaboradores = new ArrayList<ColaboradorVO>();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void selecionarColaborador() {
        try {
            ColaboradorVO obj = (ColaboradorVO) context().getExternalContext().getRequestMap().get("colaborador");
            if (obj == null) {
                throw new Exception("Erro ao Selecionar Colaborador. Contate Suporte Técnico.");
            }
            consultor.setColaborador(obj);
            valorConsultaColaborador = "";
            colaboradores = new ArrayList<ColaboradorVO>();
            setMensagemID("msg_adicionados_dados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarConsultor() {
        try {
            consultor.validarDados();
            Iterator i = meta.getConsultores().iterator();
            while (i.hasNext()) {
                MetaFinanceiraConsultorVO mfc = (MetaFinanceiraConsultorVO) i.next();
                if (mfc.getColaborador().getCodigo().intValue() == consultor.getColaborador().getCodigo()) {
                    i.remove();
                    break;
                }
            }
            meta.getConsultores().add(consultor);
            calcularMetasParaConsultores();
            consultor = new MetaFinanceiraConsultorVO();
            setMensagemID("msg_adicionados_dados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void editarConsultor() {
        try {
            MetaFinanceiraConsultorVO obj = (MetaFinanceiraConsultorVO) context().getExternalContext().getRequestMap().get("consultor");
            if (obj == null) {
                throw new Exception("Erro ao Selecionar Consultor. Contate Suporte Técnico.");
            }
            consultor = (MetaFinanceiraConsultorVO) obj.getClone(true);
            setMensagemID("msg_dados_selecionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void excluirConsultor() {
        try {
            MetaFinanceiraConsultorVO obj = (MetaFinanceiraConsultorVO) context().getExternalContext().getRequestMap().get("consultor");
            if (obj == null) {
                throw new Exception("Erro ao Excluir Consultor. Contate Suporte Técnico.");
            }
            Iterator i = meta.getConsultores().iterator();
            while (i.hasNext()) {
                MetaFinanceiraConsultorVO mfc = (MetaFinanceiraConsultorVO) i.next();
                if (mfc.getColaborador().getCodigo().intValue() == obj.getColaborador().getCodigo()) {
                    i.remove();
                    break;
                }
            }
            preparaTotalizadorConsultores();
            consultor = new MetaFinanceiraConsultorVO();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    @SuppressWarnings("CallToThreadDumpStack")
    public void gravar() {
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            if (!loginControle.getPermissaoAcessoMenuVO().getVisualizarMetasFinanceirasTodasEmpresas()) {
                meta.setEmpresa(getEmpresaLogado());
            }

            String operacao;
            if (meta.getNovoObj()) {
                meta.inicializarObjetosAntesDaAlteracao();
                getFacade().getMetaFinanceiraEmpresa().incluir(meta);
                incluirLogInclusao();
                operacao = "INCLUSÃO";
            } else {
                getFacade().getMetaFinanceiraEmpresa().alterar(meta);
                incluirLogAlteracao();
                operacao = "ALTERAÇÃO";
            }
            try {
                List<LogVO> logMeta = meta.gerarLogAlteracoes(getUsuarioLogado(), operacao);
                SuperControle.registrarLogObjetoVO(logMeta, 0);
            } catch (Exception e) {
                SuperControle.registrarLogErroObjetoVO("METAFINANCEIRAEMPRESA", 0, "ERRO AO GERAR LOG DE ALTERAÇÃO DE META FINANCEIRA", getUsuarioLogado().getNome(),getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            meta.registrarObjetoVOAntesDaAlteracao();
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    @SuppressWarnings("CallToThreadDumpStack")
    public String excluir() {
        try {
            getFacade().getMetaFinanceiraEmpresa().excluir(meta);
            incluirLogExclusao();
            inicializarDados(true);
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "metasFinanceiro";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public void clonar() {
        try {
            meta = (MetaFinanceiraEmpresaVO) meta.getClone(true);
            meta.setCodigo(0);
            meta.setNovoObj(true);
            meta.setDescricao("Cópia de " + meta.getDescricao());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String voltarTelaConsulta() {
        consultarMetas();
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(true);
        setErro(false);
        return "metasFinanceiro";
    }

    public void gerarMetasParaConsultores() {
        try {
            // pega todos os consultores disponiveis
            List<ColaboradorVO> lista = getFacade().getColaborador().consultarPorNomeTipoColaborador("", empresa.getCodigo(), true,
                    false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, TipoColaboradorEnum.CONSULTOR);
            if (lista.isEmpty()) {
                throw new Exception("Não foram encontrados Consultores cadastrados.");
            } else {
                double percentagem = Uteis.arredondarForcando2CasasDecimais(100.0 / lista.size());
                meta.setConsultores(new ArrayList<MetaFinanceiraConsultorVO>());
                // prepara a lista de metas por consultor
                for (ColaboradorVO colaborador : lista) {
                    MetaFinanceiraConsultorVO mf = new MetaFinanceiraConsultorVO();
                    mf.setColaborador(colaborador);
                    mf.setPercentagem(percentagem);
                    meta.getConsultores().add(mf);
                }
                // atualiza os valores de cada consultor
                calcularMetasParaConsultores();
            }
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void calcularMetasParaConsultores() {
        try {
            for (MetaFinanceiraConsultorVO mfc : meta.getConsultores()) {
                int laco = Math.min(5, meta.getValores().size());
                for (int i = 0; i < laco; i++) {
                    MetaFinanceiraEmpresaValoresVO mfv = meta.getValores().get(i);
                    if (!mfv.isEmpty()) {
                        switch (i) {
                            case 0:
                                mfc.setMeta1(mfv.getValor() * mfc.getPercentagem() / 100);
                                break;
                            case 1:
                                mfc.setMeta2(mfv.getValor() * mfc.getPercentagem() / 100);
                                break;
                            case 2:
                                mfc.setMeta3(mfv.getValor() * mfc.getPercentagem() / 100);
                                break;
                            case 3:
                                mfc.setMeta4(mfv.getValor() * mfc.getPercentagem() / 100);
                                break;
                            case 4:
                                mfc.setMeta5(mfv.getValor() * mfc.getPercentagem() / 100);
                                break;
                        }
                    }
                }
            }
            preparaTotalizadorConsultores();
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void preparaTotalizadorConsultores() {
        // remove o totalizador atual
        Iterator i = meta.getConsultores().iterator();
        while (i.hasNext()) {
            MetaFinanceiraConsultorVO mfc = (MetaFinanceiraConsultorVO) i.next();
            if (mfc.getCodigo() < 0) {
                i.remove();
                break;
            }
        }
        // prepara o novo totalizador
        MetaFinanceiraConsultorVO totalizador = new MetaFinanceiraConsultorVO(-1);
        for (MetaFinanceiraConsultorVO mfc : meta.getConsultores()) {
            totalizador.setMeta1(totalizador.getMeta1() + mfc.getMeta1());
            totalizador.setMeta2(totalizador.getMeta2() + mfc.getMeta2());
            totalizador.setMeta3(totalizador.getMeta3() + mfc.getMeta3());
            totalizador.setMeta4(totalizador.getMeta4() + mfc.getMeta4());
            totalizador.setMeta5(totalizador.getMeta5() + mfc.getMeta5());
        }
        totalizador.getColaborador().getPessoa().setNome("TOTAIS (" + meta.getConsultores().size() + " Consultores)");
        meta.getConsultores().add(totalizador);
    }

    protected void atualizarValorMeta(MetaFinanceiraEmpresaVO prMeta, List<ColaboradorVO> colaboradores, List<UsuarioVO> responsaveis,
            boolean rematricula, boolean renovacao, boolean matricula, boolean consultorAtual, boolean atualizar) throws Exception {
        Date date = Uteis.getDate("01/" + prMeta.getMes().getCodigo() + "/" + prMeta.getAno());
//    	dataMeta.set(Calendar.YEAR, prMeta.getAno());
//    	dataMeta.set(Calendar.MONTH, -1);
        ConfiguracaoFinanceiroVO configFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
        prMeta.setMetaAtingida(getFacade().getMetaFinanceiroBI().obterFaturamentoRecebido(getKey(),
                Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(date)),
                Uteis.obterUltimoDiaMesUltimaHora(date), colaboradores, responsaveis, prMeta.getEmpresa().getCodigo(), rematricula, renovacao, matricula, consultorAtual, atualizar, configFinanceiro.isMetaFinanceiraPorFaturamento()));
    }

    public void atualizarMetaAtingida() throws Exception {
        atualizarValorMeta(meta, null, null,true, true, true, false, false);
        this.setDataMetaAtualizada(Calendario.hoje());
        verificarValorQualMeta();
    }

    public void verificarValorQualMeta() {
        MetaFinanceiraEmpresaValoresVO metaValores = MetaFinanceiraEmpresaValoresVO.verificarValorQualMetaFoiAtingida(getMeta().getMetaAtingida(), getMeta().getValores());
        getMeta().setCor(metaValores.getCor());
        getMeta().setObservacao(metaValores.getObservacao());
        getMeta().setCorTexto(metaValores.getCorTexto());
        definirCorMetaAtingida(getMeta());
    }

    public Date obterDataMesAno() throws Exception {
        return Uteis.getDate("01/" + meta.getMes().getCodigo() + "/" + meta.getAno());
    }

    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = meta.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Finan_MetaFinanceiro_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), meta.getCodigo(), null);
    }
    
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        meta = new MetaFinanceiraEmpresaVO();
        realizarConsultaLogObjetoSelecionado();
    }
    
    

// * Ações da tela de histórico ************************************************
    public void abrirTelaHistorico() {
        try {
            periodoDe = Uteis.getDate("01/01/" + Calendario.getInstance().get(Calendar.YEAR));
            periodoAte = Uteis.getDate("31/12/" + (Calendario.getInstance().get(Calendar.YEAR) + 1));
            consultarHistoricoMetas();

            setMsgAlert("Richfaces.showModalPanel('panelHistorico');");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public void consultarHistoricoMetas() {
        try {
            validarDadosTelaHistorico();
            historico = getFacade().getMetaFinanceiraEmpresa().consultarPorEmpresaPeriodo(
                    empresaHistorico.getCodigo(), periodoDe, periodoAte, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (MetaFinanceiraEmpresaVO mf : historico) {
                atualizarValorMeta(mf, null, null, true, true, true, false, false);
                ajustaMetaParaTela(mf);
            }
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            historico = new ArrayList<MetaFinanceiraEmpresaVO>();
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void validarDadosTelaHistorico() throws Exception {
        if (empresaHistorico == null || empresaHistorico.getCodigo() == 0) {
            throw new Exception("Informe uma empresa para consultar.");
        }
        if (periodoDe == null || periodoAte == null) {
            throw new Exception("Informe um mês/ano válido.");
        }
        periodoDe = Uteis.obterUltimoDiaMes(periodoDe);
        periodoAte = Uteis.obterUltimoDiaMes(periodoAte);
        if (Calendario.maior(periodoDe, periodoAte)) {
            throw new Exception("Ano/Mês inicial deve ser menor ou igual ao Ano/Mês final.");
        }
    }

    public String getApresentarPeriodoDe() {
        return Uteis.getMesNomeReferencia(periodoDe) + "/" + Uteis.getAnoData(periodoDe);
    }

    public String getApresentarPeriodoAte() {
        return Uteis.getMesNomeReferencia(periodoAte) + "/" + Uteis.getAnoData(periodoAte);
    }

    public void clonarHistorico() {
        try {
            MetaFinanceiraEmpresaVO obj = (MetaFinanceiraEmpresaVO) context().getExternalContext().getRequestMap().get("meta");
            if (obj == null) {
                throw new Exception("Erro ao Selecionar Meta. Contate Suporte Técnico.");
            }
            meta = (MetaFinanceiraEmpresaVO) obj.getClone(true);
            meta.setCodigo(0);
            meta.setNovoObj(true);
            meta.setMetaAtingida(0.0);
            meta.setCor("#FFFFFF");
            meta.setCorTexto("#000000");
            meta.setDescricao("Cópia de " + meta.getDescricao());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

// * Gets e Sets ***************************************************************
    @Override
    public EmpresaVO getEmpresa() {
        return empresa;
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Mes getMes() {
        return mes;
    }

    public void setMes(Mes mes) {
        this.mes = mes;
    }

    public int getAno() {
        return ano;
    }

    public void setAno(int ano) {
        this.ano = ano;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<MetaFinanceiraEmpresaVO> getMetas() {
        return metas;
    }

    public void setMetas(List<MetaFinanceiraEmpresaVO> metas) {
        this.metas = metas;
    }

    public MetaFinanceiraEmpresaVO getMeta() {
        if (meta == null) {
            meta = new MetaFinanceiraEmpresaVO();
        }
        return meta;
    }

    public void setMeta(MetaFinanceiraEmpresaVO meta) {
        this.meta = meta;
    }

    public String getValorConsultaColaborador() {
        return valorConsultaColaborador;
    }

    public void setValorConsultaColaborador(String valorConsultaColaborador) {
        this.valorConsultaColaborador = valorConsultaColaborador;
    }

    public List<ColaboradorVO> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<ColaboradorVO> colaboradores) {
        this.colaboradores = colaboradores;
    }

    public MetaFinanceiraConsultorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(MetaFinanceiraConsultorVO consultor) {
        this.consultor = consultor;
    }

    public Date getPeriodoDe() {
        return periodoDe;
    }

    public void setPeriodoDe(Date periodoDe) {
        this.periodoDe = periodoDe;
    }

    public Date getPeriodoAte() {
        return periodoAte;
    }

    public void setPeriodoAte(Date periodoAte) {
        this.periodoAte = periodoAte;
    }

    public List<MetaFinanceiraEmpresaVO> getHistorico() {
        return historico;
    }

    public void setHistorico(List<MetaFinanceiraEmpresaVO> historico) {
        this.historico = historico;
    }

    public EmpresaVO getEmpresaHistorico() {
        return empresaHistorico;
    }

    public void setEmpresaHistorico(EmpresaVO empresaHistorico) {
        this.empresaHistorico = empresaHistorico;
    }

    public boolean isCalcularMeta() {
        return calcularMeta;
    }

    public void setCalcularMeta(boolean calcularMeta) {
        this.calcularMeta = calcularMeta;
    }

    public boolean isImprimirListaMeta() {
        return imprimirListaMeta;
    }

    public void setImprimirListaMeta(boolean imprimirListaMeta) {
        this.imprimirListaMeta = imprimirListaMeta;
    }

    public void setDataMetaAtualizada(Date dataMetaAtualizada) {
        this.dataMetaAtualizada = dataMetaAtualizada;
    }

    public Date getDataMetaAtualizada() {
        return dataMetaAtualizada;
    }

    public ConfiguracaoSistemaVO getConfiguracaoSistema() {
        return configuracaoSistema;
    }

    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getMetaFinanceiraEmpresa().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(),getEmpresaLogado().getMoeda());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro,null );

    }
    public void incluirLogExclusao() throws Exception {
        try {
            meta.setObjetoVOAntesAlteracao(new MetaFinanceiraEmpresaVO());
            meta.setNovoObj(true);
            SuperControle.registrarLogExclusaoTodosDadosObjetoVO(meta, meta.getCodigo(), "METAFINANCEIRAEMPRESA", 0);
        } catch (Exception e) {
            SuperControle.registrarLogErroObjetoVO("METAFINANCEIRAEMPRESA", meta.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE METAFINANCEIRAEMPRESA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
    public void incluirLogInclusao() throws Exception {
        try {
            meta.setObjetoVOAntesAlteracao(new MetaFinanceiraEmpresaVO());
            meta.setNovoObj(true);
            registrarLogObjetoVO(meta, meta.getCodigo(), "METAFINANCEIRAEMPRESA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("METAFINANCEIRAEMPRESA", meta.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE METAFINANCEIRAEMPRESA", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        meta.setNovoObj(new Boolean(false));
    }
    
    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(meta, meta.getCodigo(), "METAFINANCEIRAEMPRESA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("METAFINANCEIRAEMPRESA", meta.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE METAFINANCEIRAEMPRESA ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public List<ColaboradorVO> getListaColaboradorFiltroBI(){
        if(!JSFUtilities.isJSFContext()){
            return new ArrayList<ColaboradorVO>();
        } else {
            BIControle biControle = getControlador(BIControle.class);
            return biControle.getListaColaboradorFiltroBI(BIEnum.METAS_FINANCEIRAS.name());
        }
    }

    public List<GrupoColaboradorVO> getListaGrupoColaboradorFiltroBI(){
        if(!JSFUtilities.isJSFContext()){
            return new ArrayList<GrupoColaboradorVO>();
        } else {
            return (List) JSFUtilities.getManagedBean("BIControle.listaGrupoColaborador");
        }
    }

    public void montarArvoreColaboradores() {
        try {
            setMensagemDetalhada("");
            ComboBoxEmpresaControle comboEmpresas = (ComboBoxEmpresaControle) getControlador(
                    ComboBoxEmpresaControle.class);
            comboEmpresas.registrarGatilhos(new String[]{"gatilhoAtualizarArvoreEmpresaSelecionada"});
            //
            TreeViewColaboradorControle control = (TreeViewColaboradorControle) getControlador(
                    TreeViewColaboradorControle.class.getSimpleName());
            control.carregarArvoreGrupoColaboradores();
            //
        } catch (Exception ex) {
            Logger.getLogger(MetaFinanceiroControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }

    }
    
}

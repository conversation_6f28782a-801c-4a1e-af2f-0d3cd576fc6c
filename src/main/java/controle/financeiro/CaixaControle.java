/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.financeiro;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.MenuControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import controle.basico.FuncionalidadeControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BloqueioCaixaVO;
import negocio.comuns.financeiro.CaixaContaVO;
import negocio.comuns.financeiro.CaixaMovContaVO;
import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.MovContaRateioVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.RelatorioFechamentoCaixa;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;

/**
 *
 * <AUTHOR>
 */
public class CaixaControle extends SuperControleRelatorio {

    private static int MOVIMENTACAO_PLANO_CONTAS = 1;
    private static int MOVIMENTACAO_CONTAS = 2;
    private boolean usuarioTemCaixaEmAberto = false;
    private String senhaUsuario = "";
    private boolean todasContasMarcadas = false;
    private List<CaixaContaVO> listaContasAbrirCaixa = new ArrayList<CaixaContaVO>();
    private String fecharModalAbrirCaixa = "";
    private String abrirModalCaixa = "";
    private String abrirModalConsultarCaixa = "";
    private boolean mostrarPaginacao = true;
    private static final int NR_REGISTROS_PAG = 100;
    private boolean visualizarTodasEmpresas = false;
    private List<SelectItem> listaComboEmpresa = new ArrayList<SelectItem>();
    private EmpresaVO empresaSelecionada;
    private UsuarioVO usuarioSelecionado = new UsuarioVO();
    // Filtros para a tela de consulta de caixa.
    private List<SelectItem> listaComboUsuario = new ArrayList<SelectItem>();
    private String onCompleteFechamento = "";
    private String fecharCaixa;
    private Date dataIniAbertura;
    private Date dataFimAbertura;
    private Date dataIniFechamento;
    private Date dataFimFechamento;
    private List<CaixaVO> listaHistoricoCaixa;
    private CaixaVO caixaVoFechamento;
    private CaixaVO caixaVoEmAberto; // atributo utilizado para ser utilizado na tela "include_box_financeiro.jsp"
    private CaixaVO caixaVoMostrarMov = new CaixaVO(); // atributo utilizado para mostrar as movimentações relializadas no caixa.
    private List<DemonstrativoFinanceiro> listaDF;
    private int tipoMovimentacao = MOVIMENTACAO_PLANO_CONTAS;
    private boolean reabertura = false;
    private Date dataTrabalho = new Date();
    private Integer codigoCaixa;
    private boolean mostrarFecharCaixaOutro = false;
    private boolean mostrarFecharCaixa = false;
    private List<CaixaVO> caixasEmAberto = new ArrayList<CaixaVO>();
    private Date dataIniTrabalho;
    private Date dataFimTrabalho;
    private MensagemGenericaControle mensagemGenericaControle = new MensagemGenericaControle();

    public CaixaControle() throws Exception {
        try {
            abrirCaixaModal(false);
        } catch (Exception e) {
            montarMsgAlert("Dados não encontrado");
            Uteis.logar(e, CaixaControle.class);
        }
    }

    public String abrirTelaCaixaAdm() {
        notificarRecursoEmpresa(RecursoSistema.CAIXA_ADIMISTRATIVO);
        LoginControle loginControle = null;
        try {
            loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        }catch (Exception e){}
        if(loginControle != null){
            return telaCaixa(true, loginControle.isApresentarOpenBank());
        }else {
            return telaCaixa(true, false);
        }
    }

    private String telaCaixa(boolean abrirCaixa, boolean openBankingAtivado) {
        try {
            validarPermissoes();
            consultarMovCaixaEmAberto(abrirCaixa); // Consultar as movimentações do caixa em aberto do usuário logado.
            montarListaComboEmpresa();
            atualizarCaixa(openBankingAtivado);
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
            return "";
        }
        return "telaCaixa";
    }

    public String atualizarTelaCaixaAdm() {
        return telaCaixa(false, false);
    }

    public void atualizarCaixa() throws Exception {
        atualizarCaixa(false);
    }

    public void atualizarCaixa(boolean openBankingAtivado) throws Exception {
        getFacade().getFinanceiro().getCaixa().obterValoresResumidosCaixa(caixaVoEmAberto, openBankingAtivado);
    }

    private void validarPermissoes() throws Exception {
        validarPermissaoAbrirCaixa();
        try {
            // verificar se o usuário tem permissão para abrir/consultar caixa em todas as empresas.
            validarPermissaoAbrirConsultarCaixaParaTodasEmpresa();
            // se chegou até aqui, é porque o usuário tem permissão para abrir/consultar caixa em todas as empresas.
            setVisualizarTodasEmpresas(true);
            setEmpresaSelecionada(getEmpresaLogado());
        } catch (Exception e) {
            setEmpresaSelecionada(getEmpresaLogado());
        }
    }

    public void montarListaComboUsuario() {

        this.listaComboUsuario = new ArrayList<SelectItem>();
        try {
            listaComboUsuario.add(new SelectItem(0, ""));

            List<UsuarioVO> resultadoConsulta = UteisValidacao.emptyNumber(getEmpresaSelecionada().getCodigo())
                    ? getFacade().getUsuario().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS)
                    : getFacade().getUsuario().consultarPorAcessoEmpresa(getEmpresaSelecionada().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Iterator<UsuarioVO> i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                UsuarioVO obj = (UsuarioVO) i.next();
                listaComboUsuario.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    private void montarListaComboEmpresa() {

        this.listaComboEmpresa = new ArrayList<SelectItem>();
        try {
            if (!visualizarTodasEmpresas) {
                listaComboEmpresa.add(new SelectItem(getEmpresaLogado().getCodigo(), getEmpresaLogado().getNome()));
                empresaSelecionada.setCodigo(getEmpresaLogado().getCodigo());
                return;
            }

            List<EmpresaVO> resultadoConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
            Iterator<EmpresaVO> i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                listaComboEmpresa.add(new SelectItem(obj.getCodigo(), obj.getNome()));
            }
            if(resultadoConsulta.size() == 1){
                 empresaSelecionada.setCodigo(resultadoConsulta.get(0).getCodigo());
            }
        } catch (Exception e) {
            setMensagemDetalhada(e.getMessage());
        }
    }

    public void consultarMovCaixaEmAberto(boolean abrirTela) throws Exception {
        this.caixaVoEmAberto = getFacade().getFinanceiro().getCaixa().consultarCaixaEmAberto(getUsuarioLogado().getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        if (abrirTela || this.caixaVoMostrarMov == null
                || UteisValidacao.emptyNumber(this.caixaVoMostrarMov.getCodigo())
                || this.caixaVoMostrarMov.getCodigo().equals(getUsuarioLogado().getCodigo())) {
            this.caixaVoMostrarMov = this.caixaVoEmAberto;
        }
        setUsuarioTemCaixaEmAberto(this.caixaVoEmAberto.getCodigo() > 0);
        consultarPaginado();
    }

    private void verificarSeUsuarioTemCaixaEmAberto() throws Exception {
        if (isUsuarioTemCaixaEmAberto()) {
            setAbrirModalCaixa("alert('O usuário já possui um caixa em aberto.');");
            throw new Exception("O usuário já possui um caixa em aberto.");
        }
    }

    public void abrirModalFechamento() throws Exception {
        abrirModal(false);
    }

    private void abrirModal(boolean reabertura) throws Exception {
        limparMsg();
        setErro(false);
        setSucesso(false);
        setReabertura(reabertura);
        caixaVoMostrarMov.setResponsavelFechamento(getUsuarioLogado());
        autorizarFechamento();
    }

    public void abrirModalReabertura() throws Exception {
        clsMessages();
        setMsgAlertAuxiliar(null);
        BloqueioCaixaVO bloqueio = getFacade().getFinanceiro().getBloqueioCaixa().consultarBloqueioAtual(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Date fechamento = Calendario.getDataComHoraZerada(caixaVoMostrarMov.getDataFechamento());
        if (bloqueio != null && fechamento.before(Calendario.getDataComHora(bloqueio.getDataBloqueio(), "23:59"))) {
            setMensagemDetalhada("", "Não é permitido fazer a reabertura de caixas com a data de fechamento igual ou inferior a data "+ Uteis.getData(bloqueio.getDataBloqueio())+" do bloqueio de caixa!");
            setWarning(true);
            setMensagem("Atenção!");
        } else {
            abrirModal(true);
            setMsgAlertAuxiliar("Richfaces.showModalPanel('panelAutorizacaoFuncionalidade');");
        }
    }

    public String getOncompleteReabertura() {
        if (getMsgAlertAuxiliar() != null && !getMsgAlertAuxiliar().isEmpty()) {
            return getMsgAlertAuxiliar();
        }
        return getMensagemNotificar();
    }

    public void autorizarFechamento() {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                if(!reabertura){
                    setOnCompleteFechamento("");
                    fecharCaixa(caixaVoMostrarMov);
                    setExecutarAoCompletar("abrirPopup('telaFechamentoCaixa.jsp', 'FechamentoCaixa', 800, 530);Richfaces.hideModalPanel('modalFecharCaixas');");
                    setSucesso(true);
                    setErro(false);
                    setMensagemID("");
                    setMensagemDetalhada("", "");
                } else {
                    setMsgAlert("");
                    getFacade().getFinanceiro().getCaixa().reabrirCaixa(caixaVoMostrarMov);
                    salvarLog(caixaVoMostrarMov, "Reabertura de Caixa", auto.getUsuario());
                    caixaVoEmAberto = caixaVoMostrarMov;
                    montarSucessoGrowl("Reabertura realizada com sucesso!");
                    setMsgAlert(getMensagemNotificar());
                    telaCaixa(false, false);
                    limparMsg();
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                setOnCompleteFechamento("");
                setSucesso(false);
                setErro(true);
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {
                setExecutarAoCompletar("fireElementFromParent('form:btnAtualizaCliente');");
            }
        };

        limparMsg();
        try {

            if(!reabertura) {
                auto.autorizar("Confirmação de Fechamento de Caixa de outro Usuário", "FecharCaixaOutroUsuario",
                        "Você precisa da permissão \"9.19 - Fechar Caixa de Outro Usuário\"",
                        "container-mask-menus,caixa-modulos-zw-ui,modalConfirmacaoFecharCaixa", listener);
            } else {
                auto.autorizar("Confirmação de Reabertura de Caixa", "ReabrirCaixa",
                        "Você precisa da permissão \"9.21 - Reabrir Caixa\"",
                        "container-mask-menus,caixa-modulos-zw-ui", listener);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirCaixa(){
        abrirCaixaModal(true);
    }

    public void abrirCaixaModal(boolean comModal) {
        try {
            telaCaixa(comModal, false);
            setAbrirModalCaixa("");
            validarPermissaoAbrirCaixa();
            verificarSeUsuarioTemCaixaEmAberto();
            preencherListaCaixaConta();
            if(comModal) {
                if (listaContasAbrirCaixa.size() > 0) {
                    setMensagemID("");
                    setMensagemDetalhada("", "");
                    setAbrirModalCaixa("Richfaces.showModalPanel('modalAbrirCaixa')");
                } else {
                    throw new Exception("Não existem contas ativas para abrir caixa");
                }
            }
        } catch (Exception e) {
            setAbrirModalCaixa("alert('" + e.getMessage() + "')");
            setMensagemDetalhada("msg_erro", e.getMessage());
            onCloseModalFromNovoFront();
        }
    }

    public void fecharCaixa() {
        try {
            notificarRecursoEmpresa(RecursoSistema.FECHAR_CAIXA);
            setMsgAlert("");
            caixasEmAberto = getFacade().getFinanceiro().getCaixa().consultarCaixasEmAberto(getUsuarioLogado().getCodigo(),
                    getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if (caixasEmAberto.size() > 1) {
                setMsgAlert("Richfaces.showModalPanel('modalFecharCaixas')");
            } else {
                fecharCaixa(caixasEmAberto.get(0));
            }
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void fecharCaixaVisualizado() {
        try {
            fecharCaixa(caixaVoMostrarMov);
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void selecionarCaixaParaFechamento() {
        try {
            CaixaVO caixa = (CaixaVO) context().getExternalContext().getRequestMap().get("caixa");
            fecharCaixa(caixa);
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void fecharCaixa(CaixaVO caixa) throws Exception {
        caixaVoFechamento = caixa;
        atualizarVisualizacaoMovimentacoes();
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/pages/finan/telaFechamentoCaixa.jsp', 'FechamentoCaixa', 800, 530);Richfaces.hideModalPanel('modalFecharCaixas');");
    }

    /**
     * <AUTHOR> Alcides 06/11/2012
     */
    public void atualizarVisualizacaoMovimentacoes() throws Exception {
        RelatorioFechamentoCaixa relatorio = new RelatorioFechamentoCaixa();
        if (tipoMovimentacao == MOVIMENTACAO_CONTAS) {
            List<Integer> contasMovimentadas = new ArrayList<Integer>();
            Map<Integer, Double> valoresIniciais = new HashMap<Integer, Double>();
            for (CaixaContaVO caixaConta : caixaVoFechamento.getListaCaixaConta()) {
                contasMovimentadas.add(caixaConta.getContaVo().getCodigo());
                valoresIniciais.put(caixaConta.getContaVo().getCodigo(), caixaConta.getSaldoInicial());
            }
            this.listaDF = relatorio.gerarDemonstrativoPorCaixaPorTipoConta(caixaVoFechamento.getCodigo(), contasMovimentadas, valoresIniciais, false);
        } else {
            this.listaDF = relatorio.gerarDemonstrativoPorCaixa(caixaVoFechamento.getCodigo());
        }
        for (DemonstrativoFinanceiro obj : this.listaDF) {
            if (obj.getListaTotalizadorMeses().size() > 0) {
                obj.setTotalTodosMeses(obj.getListaTotalizadorMeses().get(0).getTotalNivel());
            }
        }
    }

    public void consultarResponsavelFechamento() {
        try {
            caixaVoMostrarMov.setResponsavelFechamento(getFacade().getUsuario().consultarPorChavePrimaria(
                    caixaVoMostrarMov.getResponsavelFechamento().getCodigo().intValue(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);

        }
    }

    public void confirmarFechamento() {
         MensagemGenericaControle mensagemGenericaControle = new MensagemGenericaControle();
        try {
            if (this.caixaVoFechamento.getDataFechamento() == null) {
                caixaVoFechamento.setDataFechamento(Calendario.hoje());
                caixaVoFechamento.setResponsavelFechamento(getUsuarioLogado());

                if (caixaVoFechamento.getUsuarioVo().getCodigo().equals(getUsuarioLogado().getCodigo())) {
                    if (caixasEmAberto.size() == 1) {
                        setUsuarioTemCaixaEmAberto(Boolean.FALSE);
                    }
                } else {
                    validarPermissao("FecharCaixaOutroUsuario", "9.19 - Fechar Caixa de Outro Usuário", caixaVoMostrarMov.getResponsavelFechamento());
                }

                getFacade().getFinanceiro().getCaixa().alterar(caixaVoFechamento);
                montarSucessoGrowl("Fechamento de caixa realizado com sucesso!");
                mensagemGenericaControle.setOnCompleteBotaoSim(getMensagemNotificar());
               // setFecharCaixa("alert('Fechamento realizado com sucesso!');");
            } else {
              //  setFecharCaixa("alert('Caixa já fechado.');");
                montarAviso("O caixa já está fechado!");
            }

            salvarLog(caixaVoFechamento, "Fechamento de Caixa", getUsuarioLogado());
            //caso tenha mais de um caixa em aberto então remover só o que fechou da lista de caixas em aberto. Se tiver só um, basta inicializar mesmo
            if (!UteisValidacao.emptyList(caixasEmAberto) && caixasEmAberto.size() > 1) {
                for (CaixaVO caixa : caixasEmAberto) {
                    if (caixa.getCodigo().equals(caixaVoFechamento.getCodigo())) {
                        caixasEmAberto.remove(caixa);
                    }
                }
            } else {
                caixaVoEmAberto = new CaixaVO();
                caixaVoMostrarMov = new CaixaVO();
            }
            atualizarMenusCaixa();
        } catch (Exception e) {
            montarAviso(e.getMessage());
            mensagemGenericaControle.setOnCompleteBotaoNao(getMensagemNotificar());

        }
    }

    private void salvarLog(CaixaVO caixa, String operacao, UsuarioVO usuario) throws Exception {
        LogVO log = new LogVO();
        log.setNomeEntidade("Caixa");
        log.setNomeEntidadeDescricao("Caixa");
        log.setChavePrimaria(caixa.getCodigo().toString());
        log.setNomeCampo("Dia Trabalho:: "+caixa.getDataTrabalho_Apresentar()+" Responsável:: " +usuario.getNome().toUpperCase());
        log.setDescricao("");
        log.setValorCampoAnterior(" -- ");
        log.setDataAlteracao(Calendario.hoje());
        log.setOperacao(operacao.toUpperCase());
        log.setUsuarioVO(usuario);
        log.setResponsavelAlteracao(usuario.getNome());
        log.setUserOAMD(getUsuarioLogado().getUserOamd());
        String texto = "Valor Entrada: " + Formatador.formatarValorMonetario(caixa.getTotalEntrada()) + " \n";
        texto = texto + "Valor Saída: " + Formatador.formatarValorMonetario(caixa.getTotalSaida()) + " \n";
        log.setValorCampoAlterado(texto);
        getFacade().getLog().incluir(log);

    }

    public String visualizarCaixa() {
        try {
            CaixaVO obj = (CaixaVO) context().getExternalContext().getRequestMap().get("caixa");
            if (obj == null) {
                throw new Exception("Erro ao Visualizar Caixa. Contate Suporte Técnico.");
            }
            carregarCaixa(obj.getCodigo());
            onCloseModalFromNovoFront();
            return "telaCaixa";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            onCloseModalFromNovoFront();
            return "";
        }
    }

    /**
     * <AUTHOR> Alcides 30/04/2013
     */
    public void carregarCaixa(Integer codigo) throws Exception {
        this.caixaVoMostrarMov = getFacade().getFinanceiro().getCaixa().consultarCaixa(codigo, Uteis.NIVELMONTARDADOS_TODOS);
        getFacade().getFinanceiro().getCaixa().obterValoresResumidosCaixa(caixaVoMostrarMov);
        consultarPaginado();
        setMensagemID("operacoes.consulta.sucesso");
        setSucesso(true);
        setErro(false);
    }

    public void abrirModalHistoricoCaixa() {
        try {
            if (isVisualizarTodasEmpresas()) {
                setEmpresaSelecionada(getEmpresaLogado());
            }
            montarListaComboUsuario();
            montarListaComboEmpresa();
            setMensagemDetalhada("", "");
            setMensagemID("parametros.informar");
            validarPermissoes();
            validarPermissaoConsultarHistCaixa();
            if ((dataIniAbertura != null) && (dataFimAbertura == null)) {
                dataFimAbertura = dataIniAbertura;
            }
            if ((dataIniFechamento != null) && (dataFimFechamento == null)) {
                dataFimFechamento = dataIniFechamento;
            }

            this.abrirModalConsultarCaixa = "Richfaces.showModalPanel('modalConsCaixa')";
            if (listaHistoricoCaixa == null) {
                this.listaHistoricoCaixa = new ArrayList<CaixaVO>();
            }

        } catch (Exception e) {
            this.abrirModalConsultarCaixa = "alert('" + e.getMessage() + "');";
        }
    }
        public void validarDatas() throws Exception{
            if (dataIniAbertura!=null && dataFimAbertura!=null &&  Calendario.maior(dataIniAbertura, dataFimAbertura)) {
                throw new Exception("Informe um período de abertura valido!");
            }
            if (dataIniFechamento!=null && dataFimFechamento!=null && Calendario.maior(dataIniFechamento, dataFimFechamento)) {
                throw new Exception("Informe um período de fechamento valido!");
            }
            if (dataIniTrabalho!=null && dataFimTrabalho!=null && Calendario.maior(dataIniTrabalho, dataFimTrabalho)) {
                throw new Exception("Informe um período de trabalho valido!");
            }
        }
    public void consultarHistoricoCaixa() {
        try {
            setMensagemDetalhada("", "");
            validarDatas();
            this.listaHistoricoCaixa = getFacade().getFinanceiro().getCaixa().consultarHistoricoCaixa(this.empresaSelecionada.getCodigo(),
                    this.usuarioSelecionado.getCodigo(),
                    getUsuarioLogado().getCodigo(),
                    dataIniAbertura,
                    dataFimAbertura,
                    dataIniFechamento,
                    dataFimFechamento,
                    dataIniTrabalho,
                    dataFimTrabalho,
                    getCodigoCaixa(),
                    getVerificarPermissaoConsultarCaixa(),
                    Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            setMensagemID("msg_dados_consultados");
            if(this.listaHistoricoCaixa.isEmpty()){
                setMensagemID("msg_dados_consultados_vazio");
                if(!getVerificarPermissaoConsultarCaixa()){
                    setMensagemDetalhada("Nenhum registro encontrado. (A permissão 9.25 está desmarcada, então não é possível trazer caixa de outros usúarios)");
                }
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void validarPermissaoAbrirCaixa() throws Exception {
        validarPermissao("AbrirCaixaAdm", "9.17 - Abrir Caixa-Administrativo", getUsuarioLogado());
    }

    public void validarPermissao(String permissao, String descricao, UsuarioVO usuario) throws Exception {
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                limparMsg();
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = usuario.getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                        usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        usuario, permissao, descricao);
            }
        }
    }

    public void validarPermissaoAbrirConsultarCaixaParaTodasEmpresa() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "AbrirConsultarHistCaixaAdmTodasEmpresas", "9.25 - Abrir/Consultar Caixa-Administrativo para todas as Empresas");
            }
        }
    }

    public void validarPermissaoConsultarHistCaixa() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "ConsultarHistCaixaAdm", "9.18 - Consultar Histórico Caixa-Administrativo");
            }
        }
    }

    public boolean isUsuarioTemCaixaEmAberto() {
        return usuarioTemCaixaEmAberto;
    }

    public void setUsuarioTemCaixaEmAberto(boolean usuarioTemCaixaEmAberto) {
        this.usuarioTemCaixaEmAberto = usuarioTemCaixaEmAberto;
    }

    public CaixaVO getCaixaVoMostrarMov() {
        return caixaVoMostrarMov;
    }

    public void setCaixaVoMostrarMov(CaixaVO caixaVoMostrarMov) {
        this.caixaVoMostrarMov = caixaVoMostrarMov;
    }

    public String getSenhaUsuario() {
        return senhaUsuario;
    }

    public void setSenhaUsuario(String senhaUsuario) {
        this.senhaUsuario = senhaUsuario;
    }

    public boolean isTodasContasMarcadas() {
        return todasContasMarcadas;
    }

    public void setTodasContasMarcadas(boolean todasContasMarcadas) {
        this.todasContasMarcadas = todasContasMarcadas;
    }

    public List<CaixaContaVO> getListaContasAbrirCaixa() {
        return listaContasAbrirCaixa;
    }

    public void setListaContasAbrirCaixa(List<CaixaContaVO> listaContasAbrirCaixa) {
        this.listaContasAbrirCaixa = listaContasAbrirCaixa;
    }

    public void marcarDesmarcarContas() {
        for (CaixaContaVO obj : this.listaContasAbrirCaixa) {
            obj.getContaVo().setContaEscolhida(this.todasContasMarcadas);
        }
    }

    public void preencherListaCaixaConta() throws Exception {
        this.listaContasAbrirCaixa = new ArrayList<CaixaContaVO>();
        ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) getControlador(ConfiguracaoFinanceiroControle.class);
        ConfiguracaoFinanceiroVO confFinanceiro;
        if(cfg == null){
            confFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
        }else{
            confFinanceiro = cfg.getConfFinanceiro();
        }
        Integer empresa;
        if(confFinanceiro.isPermitirContaOutraUnidade() && visualizarTodasEmpresas){
            empresa = null;
        }else{
            empresa = getEmpresaSelecionada().getCodigo();
        }
        List<ContaVO> lista = getFacade().getFinanceiro().getConta().consultarContasParaCaixa(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (ContaVO obj : lista) {
            if(obj.getTipoConta() != null && obj.getTipoConta().getComportamento() != null && obj.getTipoConta().getComportamento().equals(ComportamentoConta.OPENBANK)) { // CONTA INTEGRAÇÂO OPENBANK NÂO REALIZA MOVIMENTAÇÂO.
                continue;
            }
            CaixaContaVO caixaContaVo = new CaixaContaVO();
            caixaContaVo.setContaVo(obj);
            caixaContaVo.setSaldoInicial(obj.getSaldoAtual());
            caixaContaVo.setSaldoFinal(obj.getSaldoAtual());
            this.listaContasAbrirCaixa.add(caixaContaVo);
        }
    }

    public void preencherListaCaixaContaPorEmpresa() {
        try {
            limparMsg();
            preencherListaCaixaConta();
            this.todasContasMarcadas = false;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void consultarPaginadoListener(ActionEvent evt) {

        if (this.getConfPaginacao() == null) {
            this.setConfPaginacao(new ConfPaginacao());
        }

        getConfPaginacao().setPaginarBanco(this.isMostrarPaginacao());

        //Obtendo qual pagina deverá ser exibida
        Object component = evt.getComponent().getAttributes().get("pagNavegacao");
        if (component != null && !"".equals(component.toString())) {
            getConfPaginacao().setPagNavegacao(component.toString());
        }

        getConfPaginacao().setItensPorPagina(NR_REGISTROS_PAG);
        consultarPaginado();
    }

    public void consultarPaginado() {
        try {
            if (this.caixaVoMostrarMov.getCodigo() == 0) {
                return;
            }
            getConfPaginacao().setItensPorPagina(NR_REGISTROS_PAG);
            this.caixaVoMostrarMov.setListaCaixaMovConta(getFacade().getFinanceiro().getCaixaMovConta().consultarPaginado(caixaVoMostrarMov.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, getConfPaginacao()));
            getConfPaginacao().setNumeroTotalItens(this.caixaVoMostrarMov.getListaCaixaMovConta().size());
            mostrarFecharCaixaOutro();
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void gravarAberturaCaixa() {
        MensagemGenericaControle mensagemGenericaControle = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        try {
            setMsgAlert("");
            /*Validar novamente se o usuário tem um caixa em aberto.
             Obs.: Esta validação é necessária, pois o usuário pode abrir o sistema em
             dois browser diferentes, abrir a tela de abertura de caixa e
             clicar no botão "Gravar" nos dois browser.*/
            verificarSeUsuarioTemCaixaEmAberto();

            this.caixaVoEmAberto = new CaixaVO();
            for (CaixaContaVO obj : listaContasAbrirCaixa) {
                if (obj.getContaVo().getContaEscolhida()) {
                    caixaVoEmAberto.getListaCaixaConta().add(obj);
                }
            }
            if (getEmpresaSelecionada().getCodigo() == 0) {
                throw new Exception("É necessário informar uma empresa!");
            }
            getFacade().getUsuarioPerfilAcesso().verificarLoginUsuario(getUsuarioLogado().getCodigo(), this.senhaUsuario.toUpperCase());
            if (this.caixaVoEmAberto.getListaCaixaConta().size() <= 0) {
                throw new Exception("É necessário selecionar pelo menos uma conta para operar o caixa!");
            }

            caixaVoEmAberto.setDataAbertura(Calendario.hoje());
            caixaVoEmAberto.setDataTrabalho(dataTrabalho);
            caixaVoEmAberto.setUsuarioVo(getUsuarioLogado());
            caixaVoEmAberto.setEmpresaVo(getEmpresaSelecionada());
            getFacade().getFinanceiro().getCaixa().incluir(caixaVoEmAberto);
            this.caixaVoMostrarMov = this.caixaVoEmAberto;
            JSFUtilities.storeOnSession("caixaEmAberto", caixaVoEmAberto);
            montarSucessoGrowl("Abertura de caixa realizada com sucesso!");
            mensagemGenericaControle.setOnCompleteBotaoSim(getMensagemNotificar());
            setUsuarioTemCaixaEmAberto(true);
            setConfPaginacao(new ConfPaginacao());
            salvarLog(caixaVoEmAberto, "Abertura de Caixa", getUsuarioLogado());
            todasContasMarcadas = false;
            onCloseModalFromNovoFront();
            setMsgAlert("Richfaces.hideModalPanel('modalAbrirCaixa');");
            atualizarMenusCaixa();
        } catch (Exception e) {
            montarAviso(e.getMessage());
            mensagemGenericaControle.setOnCompleteBotaoNao(getMensagemNotificar());
            onCloseModalFromNovoFront();
        }
    }

    private void atualizarMenusCaixa(){
        MenuControle menuCtrl = (MenuControle) JSFUtilities.getManagedBean("MenuControle");
        menuCtrl.processarMenus();
        menuCtrl.processarMenusFinaneiro();
    }

    public void alterarSomenteApresentarCaixa() throws Exception {
        CaixaMovContaVO obj = (CaixaMovContaVO) context().getExternalContext().getRequestMap().get("caixaMovConta");
        if (obj != null) {
            getFacade().getFinanceiro().getMovConta().alterarSomenteApresentarCaixa(obj.getMovContaVo());
        }

    }

    public void atualizarValorCaixaAberto(MovContaVO movConta) throws Exception {
        if (caixaVoEmAberto == null) {
            caixaVoEmAberto = getFacade().getFinanceiro().getCaixa().consultarCaixaEmAberto(getUsuarioLogado().getCodigo(),
                    getEmpresaLogado().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (caixaVoEmAberto.getCodigo() == 0) {
            return;
        }
        for (MovContaRateioVO mcr : movConta.getMovContaRateios()) {
            if (mcr.getTipoES().equals(TipoES.ENTRADA)) {
                caixaVoEmAberto.setTotalEntrada(caixaVoEmAberto.getTotalEntrada() + mcr.getValor());
            } else {
                caixaVoEmAberto.setTotalSaida(caixaVoEmAberto.getTotalSaida() + mcr.getValor());
            }
        }
        JSFUtilities.storeOnSession("caixaEmAberto", caixaVoEmAberto);
    }

    public String getFecharModalAbrirCaixa() {
        return fecharModalAbrirCaixa;
    }

    public void setFecharModalAbrirCaixa(String fecharModalAbrirCaixa) {
        this.fecharModalAbrirCaixa = fecharModalAbrirCaixa;
    }

    public String getAbrirModalCaixa() {
        return abrirModalCaixa;
    }

    public void setAbrirModalCaixa(String abrirModalCaixa) {
        this.abrirModalCaixa = abrirModalCaixa;
    }

    public boolean isMostrarPaginacao() {
        return mostrarPaginacao;
    }

    public void setMostrarPaginacao(boolean mostrarPaginacao) {
        this.mostrarPaginacao = mostrarPaginacao;
    }

    public boolean isVisualizarTodasEmpresas() {
        return visualizarTodasEmpresas;
    }

    public void setVisualizarTodasEmpresas(boolean visualizarTodasEmpresas) {
        this.visualizarTodasEmpresas = visualizarTodasEmpresas;
    }

    public List<SelectItem> getListaComboEmpresa() {
        return listaComboEmpresa;
    }

    public void setListaComboEmpresa(List<SelectItem> listaComboEmpresa) {
        this.listaComboEmpresa = listaComboEmpresa;
    }

    public EmpresaVO getEmpresaSelecionada() {
        if (empresaSelecionada == null) {
            empresaSelecionada = new EmpresaVO();
        }
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(EmpresaVO empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public UsuarioVO getUsuarioSelecionado() {
        return usuarioSelecionado;
    }

    public void setUsuarioSelecionado(UsuarioVO usuarioSelecionado) {
        this.usuarioSelecionado = usuarioSelecionado;
    }

    public List<SelectItem> getListaComboUsuario() {
        return listaComboUsuario;
    }

    public void setListaComboUsuario(List<SelectItem> listaComboUsuario) {
        this.listaComboUsuario = listaComboUsuario;
    }

    public Date getDataFimAbertura() {
        return dataFimAbertura;
    }

    public void setDataFimAbertura(Date dataFimAbertura) {
        this.dataFimAbertura = dataFimAbertura;
    }

    public Date getDataFimFechamento() {
        return dataFimFechamento;
    }

    public void setDataFimFechamento(Date dataFimFechamento) {
        this.dataFimFechamento = dataFimFechamento;
    }

    public Date getDataIniAbertura() {
        return dataIniAbertura;
    }

    public void setDataIniAbertura(Date dataIniAbertura) {
        this.dataIniAbertura = dataIniAbertura;
    }

    public Date getDataIniFechamento() {
        return dataIniFechamento;
    }

    public void setDataIniFechamento(Date dataIniFechamento) {
        this.dataIniFechamento = dataIniFechamento;
    }

    public void limparPeriodoAbertura() {
        this.dataIniAbertura = null;
        this.dataFimAbertura = null;
    }
    public void limparPeriodoTrabalho() {
        this.dataIniTrabalho = null;
        this.dataFimTrabalho = null;
    }
    public void limparPeriodoFechamento() {
        this.dataIniFechamento = null;
        this.dataFimFechamento = null;
    }

    public String getAbrirModalConsultarCaixa() {
        return abrirModalConsultarCaixa;
    }

    public void setAbrirModalConsultarCaixa(String abrirModalConsultarCaixa) {
        this.abrirModalConsultarCaixa = abrirModalConsultarCaixa;
    }

    public List<CaixaVO> getListaHistoricoCaixa() {
        return listaHistoricoCaixa;
    }

    public void setListaHistoricoCaixa(List<CaixaVO> listaHistoricoCaixa) {
        this.listaHistoricoCaixa = listaHistoricoCaixa;
    }

    public CaixaVO getCaixaVoEmAberto() {
        return caixaVoEmAberto;
    }

    public void setCaixaVoEmAberto(CaixaVO caixaVoEmAberto) {
        this.caixaVoEmAberto = caixaVoEmAberto;
    }

    public void visualizarLancamentoFinanceiro() {
        try {
            setMensagemID("msg_dados_editar");
            CaixaMovContaVO caixaMovContaVo = (CaixaMovContaVO) context().getExternalContext().getRequestMap().get("caixaMovConta");
            MovContaVO movContaVo = getFacade().getFinanceiro().getMovConta().consultarPorCodigo(caixaMovContaVo.getMovContaVo().getCodigo(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            MovContaControle movContaControle = (MovContaControle) JSFUtilities.getManagedBean("MovContaControle");

            movContaControle.setLancamentoDemonstrativo(Boolean.TRUE);
            movContaVo.setMovContaRateios(getFacade().getFinanceiro().getMovContaRateio().consultarPorMovConta(movContaVo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            movContaControle.setarMovConta(movContaVo, true, true);

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", "Erro ao visualizar Lançamento. Classe do erro: " + e.getClass() + "  MsgErro: " + e.getMessage());
        }
    }

    /**
     * Consulta de logs de lançamentos
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        setMsgAlert("");
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = caixaVoMostrarMov.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_Finan_CaixaAdministrativo"));
        if (caixaVoMostrarMov.getCodigo() != null && caixaVoMostrarMov.getCodigo() != 0) {
            loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), caixaVoMostrarMov.getCodigo(), null);
        } else {
            loginControle.setListaConsultaLog(new ArrayList());
            loginControle.getListaConsultaLog().clear();
        }
        setMsgAlert("abrirPopup('" + request().getContextPath() + "/faces/visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);");

    }

    public void reabrirCaixa() {
        try {
            setMsgAlert("");
            UsuarioVO usuario = getFacade().getControleAcesso().verificarLoginUsuario(caixaVoMostrarMov.getResponsavelFechamento().getUsername(),
                    caixaVoMostrarMov.getResponsavelFechamento().getSenha().toUpperCase());
            validarPermissaoReabrirCaixa(usuario);
            getFacade().getFinanceiro().getCaixa().reabrirCaixa(caixaVoMostrarMov);
            salvarLog(caixaVoMostrarMov, "Reabertura de Caixa", usuario);
            caixaVoEmAberto = caixaVoMostrarMov;
            montarSucessoGrowl("Reabertura realizada com sucesso!");
            setMsgAlert(getMensagemNotificar());
            telaCaixa(false, false);
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setErro(true);
            setSucesso(false);
        }
    }

    public void validarPermissaoReabrirCaixa(UsuarioVO usuario) throws Exception {
        validarPermissao("ReabrirCaixa", "9.21 - Reabrir Caixa", usuario);
    }

    public List<DemonstrativoFinanceiro> getListaDF() {
        return listaDF;
    }

    public void setListaDF(List<DemonstrativoFinanceiro> listaDF) {
        this.listaDF = listaDF;
    }

    public void setFecharCaixa(String fecharCaixa) {
        this.fecharCaixa = fecharCaixa;
    }

    public String getFecharCaixa() {
        return fecharCaixa;
    }

    public void setCaixaVoFechamento(CaixaVO caixaVoFechamento) {
        this.caixaVoFechamento = caixaVoFechamento;
    }

    public CaixaVO getCaixaVoFechamento() {
        return caixaVoFechamento;
    }

    public boolean getApresentarBotaoImprimirCaixa() {
        return caixaVoFechamento != null && caixaVoFechamento.getDataFechamento() != null;
    }

    public boolean getApresentarBotaoImprimirCaixaTelaPrincipal() {
        return caixaVoMostrarMov != null && caixaVoMostrarMov.getDataFechamento() != null;
    }

    public void mostrarFecharCaixaOutro() throws Exception {
        if (getCaixaVoMostrarMov().getDataFechamento() == null) {
            if (getCaixaVoMostrarMov().getUsuarioVo().getCodigo().equals(getUsuarioLogado().getCodigo())) {
                setMostrarFecharCaixa(true);
                setMostrarFecharCaixaOutro(false);
            } else {
                setMostrarFecharCaixa(false);
                setMostrarFecharCaixaOutro(true);
            }
        } else {
            setMostrarFecharCaixa(false);
            setMostrarFecharCaixaOutro(false);
        }
    }

    public void setOnCompleteFechamento(String onCompleteFechamento) {
        this.onCompleteFechamento = onCompleteFechamento;
    }

    public String getOnCompleteFechamento() {
        return onCompleteFechamento;
    }

    public void setTipoMovimentacao(int tipoMovimentacao) {
        this.tipoMovimentacao = tipoMovimentacao;
    }

    public int getTipoMovimentacao() {
        return tipoMovimentacao;
    }

    public void imprimirCaixa() {
        try {
            caixaVoFechamento = caixaVoMostrarMov;
            atualizarVisualizacaoMovimentacoes();
            for (DemonstrativoFinanceiro obj : this.listaDF) {
                if (obj.getListaTotalizadorMeses().size() > 0) {
                    obj.setTotalTodosMeses(obj.getListaTotalizadorMeses().get(0).getTotalNivel());
                }
                //System.out.println(obj.getCodigoAgrupador() + " " + obj.getNomeAgrupador() + " - " + obj.getTotalTodosMeses());
            }
            setMsgAlert("abrirPopup('telaFechamentoCaixa.jsp', 'FechamentoCaixa', 800, 530);");
            // Após concluir o fechamento do caixa, setar um caixa vazio para a varíavel "caixaVoEmAberto" -- this.caixaVoEmAberto = new CaixaVO();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void setReabertura(boolean reabertura) {
        this.reabertura = reabertura;
    }

    public boolean isReabertura() {
        return reabertura;
    }

    public void setDataTrabalho(Date dataTrabalho) {
        this.dataTrabalho = dataTrabalho;
    }

    public Date getDataTrabalho() {
        return dataTrabalho;
    }

    public void setCodigoCaixa(Integer codigoCaixa) {
        this.codigoCaixa = codigoCaixa;
    }

    public Integer getCodigoCaixa() {
        return codigoCaixa;
    }

    public void setMostrarFecharCaixaOutro(boolean mostrarFecharCaixaOutro) {
        this.mostrarFecharCaixaOutro = mostrarFecharCaixaOutro;
    }

    public Date getDataIniTrabalho() {
        return dataIniTrabalho;
    }

    public void setDataIniTrabalho(Date dataIniTrabalho) {
        this.dataIniTrabalho = dataIniTrabalho;
    }

    public Date getDataFimTrabalho() {
        return dataFimTrabalho;
    }

    public void setDataFimTrabalho(Date dataFimTrabalho) {
        this.dataFimTrabalho = dataFimTrabalho;
    }

    public boolean isMostrarFecharCaixaOutro() {
        return mostrarFecharCaixaOutro;
    }

    public void setMostrarFecharCaixa(boolean mostrarFecharCaixa) {
        this.mostrarFecharCaixa = mostrarFecharCaixa;
    }

    public boolean isMostrarFecharCaixa() {
        return mostrarFecharCaixa;
    }

    public void setCaixasEmAberto(List<CaixaVO> caixasEmAberto) {
        this.caixasEmAberto = caixasEmAberto;
    }

    public List<CaixaVO> getCaixasEmAberto() {
        return caixasEmAberto;
    }
    
    public void validarCaixaAbertoConta(Integer codigoConta, boolean recebiveis) throws Exception{
        if ((getCaixaVoEmAberto() == null) || (getCaixaVoEmAberto().getCodigo() == 0)) {
            setMensagemID("msg_naoPossuiCaixaAberto");
            throw new Exception(getMensagem());
        }
        if(UteisValidacao.emptyNumber(codigoConta)){
            return;
        }
        boolean naoPossuiCaixaConta = true;
        for (CaixaContaVO caixaConta : getCaixaVoEmAberto().getListaCaixaConta()) {
            if (caixaConta.getContaVo().getCodigo().equals(codigoConta)) {
                naoPossuiCaixaConta = false;
                break;
            }
        }
        if (naoPossuiCaixaConta) {
            if(recebiveis){
                setMensagemID("msg_naoPossuiCaixaAbertoParaConta_Recebiveis");
            } else {
                setMensagemID("msg_naoPossuiCaixaAbertoParaConta");
            }
            throw new Exception(getMensagem());
        }
    }

    public Boolean getVerificarPermissaoConsultarCaixa() throws Exception {
        if (ControleAcesso.isPermiteMultiEmpresas()) {
            if (isNull(JSFUtilities.getFromSession("funcAbrirConsultarHistCaixaAdmTodasEmpresas"))) {
                JSFUtilities.storeOnSession("funcAbrirConsultarHistCaixaAdmTodasEmpresas", getFacade().getControleAcesso().verificarPermissaoFuncionalidade("AbrirConsultarHistCaixaAdmTodasEmpresas"));
                return (Boolean) JSFUtilities.getFromSession("funcAbrirConsultarHistCaixaAdmTodasEmpresas");
            } else {
                return (Boolean) JSFUtilities.getFromSession("funcAbrirConsultarHistCaixaAdmTodasEmpresas");
            }
        } else return true;
    }

    public void onCloseModalFromNovoFront() {
        FuncionalidadeControle funcionalidadeControle = getControlador(FuncionalidadeControle.class);

        if(funcionalidadeControle != null) {
            if(!UteisValidacao.emptyString(funcionalidadeControle.getFuncionalidadeNome()) &&
                    (funcionalidadeControle.getFuncionalidadeNome().equals("ABRIR_CAIXA") || funcionalidadeControle.getFuncionalidadeNome().equals("CONSULTAR_CAIXA"))){
                funcionalidadeControle.setFuncionalidadeNome("");
            }
        }
    }
}

package controle.vendasonline;

public enum CamposAdicionaisVendasOnlineEnum {
    TELEFONE("Telefone",true,true,true,true, ""),
    CEP("CEP",true,true,true,true, ""),
    ENDERECO("Endereço",true,true,true,true, ""),
    NUMERO("Número",true,true, true,true,""),
    BAIRRO("Bairro",true,true, true,true,""),
    COMPLEMENTO("Complemento",true,true,true,true, ""),
    DIA_VENCIMENTO("Dia vencimento mensalidade",true,false, true,false,""),
//    VENCIMENTO_FATURA("Vencimento da fatura",true,true, ""),
    SEXO("Sexo",true,true, true,true,""),
    RESPONSAVEL_PAI("Responsável Pai",true,true, true,true, ""),
    RESPONSAVEL_MAE("Responsável Mãe",true,true, true,true,""),
    CPF_RESPONSAVEL_PAI("CPF Responsável Pai",true,true, true,true,""),
    CPF_RESPONSAVEL_MAE("CPF Responsável Mãe",true,true, true,true,""),
    DT_NASCIMENTO("Data de Nascimento",true,true, true,true,""),
    RG("RG",true,true, true,true,""),
    INICIO_CONTRATO("Alterar data inicio contrato",true,false, true,false,"Este campo não é válido para plano convencional e plano de crédito"),
    ParQ("ParQ",true,false, true,false,""),
    CUPOM_DESCONTO("Cupom Desconto", true, true, true, true, ""),
    ;

    private String descricao;
    private boolean habilitaPlano;
    private boolean habilitaProduto;
    private boolean habilitaPlanoFlow;
    private boolean habilitaProdutoFlow;
    private String hint;

    CamposAdicionaisVendasOnlineEnum(String descricao,boolean habilitaPlano, boolean habilitaProduto, boolean habilitaPlanoFlow, boolean habilitaProdutoFlow, String hint) {

        this.descricao = descricao;
        this.habilitaPlano = habilitaPlano;
        this.habilitaProduto = habilitaProduto;
        this.habilitaPlanoFlow = habilitaPlanoFlow;
        this.habilitaProdutoFlow = habilitaProdutoFlow;
        this.hint = hint;
    }

    public String getDescricao() {
        return descricao;
    }

    public boolean isHabilitaPlano() {return habilitaPlano; }

    public boolean isHabilitaProduto() {return habilitaProduto;}

    public String getHint() {
        return hint;
    }
}

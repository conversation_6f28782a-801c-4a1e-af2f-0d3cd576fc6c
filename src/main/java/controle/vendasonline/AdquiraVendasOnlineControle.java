package controle.vendasonline;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.produtoPacto.ComprarProdutoPactoDTO;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.security.MessageDigest;
import java.util.HashMap;

public class AdquiraVendasOnlineControle extends SuperControleRelatorio {

    private UsuarioVO usuarioResponsavel;
    private EmpresaVO empresaLogada;
    private Boolean podeAdquirirModulo;
    private String descProdutoPacto;
    private String valorAdesaoProdutoPacto;
    private String valorMensalidadeProdutoPacto;
    private int idProdutoPacto;

    public AdquiraVendasOnlineControle() {
        init();
    }

    public void init() {
        try {
            setIdProdutoPacto(0);
            setDescProdutoPacto("ATIVAÇÃO MÓDULO VENDAS ONLINE");
            setValorAdesaoProdutoPacto("R$ 0,00");
            setValorMensalidadeProdutoPacto("R$ 0,00");
            setPodeAdquirirModulo(false);
            setUsuarioResponsavel(getUsuarioLogado() != null ? getUsuarioLogado() : new UsuarioVO());
            setEmpresaLogada(getEmpresaLogado() != null ? getEmpresaLogado() : new EmpresaVO());
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            Uteis.logar(e, AdquiraVendasOnlineControle.class);
        }
    }

    public void consultarResponsavel() {
        try {
            setUsuarioResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(
                    getUsuarioResponsavel().getCodigo().intValue(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getUsuarioResponsavel().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public String abrilModalNegociacao() {
        try {
            notificarRecursoEmpresa(RecursoSistema.CLICOU_ABRIR_MODAL_COMPRAR_MODULO_VENDAS_ONLINE);
        } catch (Exception ignore) {
        }
        try {
            limparMsg();
            setMsgAlert("");
            setMensagemID("");
            setMensagem("");
            setMensagemDetalhada("");

            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            if (loginControle == null || !loginControle.getApresentarPactoStore()) {
                setMsgAlert("window.open('https://sistemapacto.com.br/compre-agora', '_blank');");
                return "";
            }
            return loginControle.abrirModuloCanalClientePactoStore();
        } catch (Exception e) {
            e.printStackTrace();
            setMsgAlert("window.open('https://sistemapacto.com.br/compre-agora', '_blank');");
            return "";
        }
    }

    public void permisaoAdqurirModulo() {
        try {
            setMsgAlert("");
            setMensagemID("");
            setMensagem("");
            setMensagemDetalhada("");
            if (!MessageDigest.isEqual(getFacade().getUsuario().consultarSenhaPorCodigoUsuario(getUsuarioResponsavel().getCodigo()).getBytes(), Uteis.encriptar(getUsuarioResponsavel().getSenha()).getBytes())) {
                throw new Exception("Senha Incorreta!");
            }
            validarPermissao("BoletosSistema", "4.31 - Emitir boleto para pagamento de contrato mensal para Pacto Software e Gestão", getUsuarioResponsavel());
            for (Object o : getUsuarioResponsavel().getUsuarioPerfilAcessoVOs()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
                if(usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("ADMINISTRADOR") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("ADMIN") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("MASTER") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("ADM") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("DONO") ||
                        usuarioPerfilAcesso.getPerfilAcesso().getNome().equalsIgnoreCase("CEO"));
                setPodeAdquirirModulo(true);
                try{
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '4.43 - Gestão de Vendas Online','(0)(1)(2)(3)(9)(12)', "
                            + " 'VendasOnline', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());
                }catch (Exception ignore){
                }
                break;
            }

            if(isPodeAdquirirModulo()){
                comprarProdutoPacto();
            }
            try{
                notificarRecursoEmpresa(RecursoSistema.CONFIRMOU_MODAL_COMPRAR_MODULO_VENDAS_ONLINE);
            }catch(Exception ignore){
            }
            setMsgAlert("Richfaces.hideModalPanel('modalAdquiraVendas');Richfaces.hideModalPanel('panelUsuarioSenhaAdquiraVendasOnline');Richfaces.showModalPanel('modalFinalizaVendas')");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void comprarProdutoPacto() throws Exception {
        setUsuarioResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(getUsuarioResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        setEmpresaLogada(getFacade().getEmpresa().consultarPorChavePrimaria(getEmpresaLogada().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        validarDados();

        ComprarProdutoPactoDTO compraDTO = new ComprarProdutoPactoDTO();
        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/produto/comprar?token=NFBSMDZVVDBfWjFMTDcwTlc0OA==";

        //DADOS CLIENTE
        compraDTO.setResponsavel(getUsuarioResponsavel().getNome());
        compraDTO.setCpf(getUsuarioResponsavel().getColaboradorVO().getPessoa().getCfp());
        compraDTO.setEmail(getUsuarioResponsavel().getColaboradorVO().getPessoa().getEmail());
        compraDTO.setCelular(getUsuarioResponsavel().getColaboradorVO().getPessoa().getTelefonesCelular());

        //DADOS EMPRESA
        compraDTO.setChave(getKey());
        compraDTO.setEmpresa(getEmpresaLogada().getCodigo());
        compraDTO.setCodFinanceiro(getEmpresaLogada().getCodEmpresaFinanceiro());
        compraDTO.setNomeFantasia(getEmpresaLogada().getNome());
        compraDTO.setRazaoSocial(getEmpresaLogada().getNome());
        compraDTO.setCnpj(getEmpresaLogada().getCNPJ());

        //DADOS DA COMPRA
        compraDTO.setIdProdutoPacto(getIdProdutoPacto());
        compraDTO.setQtdParcelas(1);
        compraDTO.setSistemaOrigem("ZW ADM");

        String response = executeRequestHttpService.executeRequestCupomDesconto(url, new JSONObject(compraDTO).toString(), new HashMap<>());
        try {
            setMensagemDetalhada(new JSONObject(response).get("content").toString());
            registrarLog("VENDASONLINE", getUsuarioResponsavel().getClienteVO().getPessoa().getCodigo(), getUsuarioResponsavel().getNome(), getUsuarioResponsavel().getUserOamd());
        }catch (Exception e){
            throw new Exception(new JSONObject(response).getJSONObject("meta").get("message").toString());
        }
    }

    private void registrarLog(String nomeEntidade, int codPessoa, String responsavel, String userOamd){
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("COMPRA MÓDULO VENDAS ONLINE");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior("-");
            log.setValorCampoAlterado("Módulo vendas online adquirido pelo banner de compra rápida.");
            log.setOperacao("COMPRA DE MÓDULO");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            getFacade().getLog().incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void validarDados() throws Exception{
        if(UteisValidacao.emptyString(getUsuarioResponsavel().getNome())){
            throw new Exception("O nome do usuário responsável pela operação está vazio.");
        }
        if(UteisValidacao.emptyString(getUsuarioResponsavel().getColaboradorVO().getPessoa().getCfp())){
            throw new Exception("O cpf do usuário responsável pela operação está vazio.");
        }
        if(UteisValidacao.emptyString(getUsuarioResponsavel().getColaboradorVO().getPessoa().getEmail())){
            throw new Exception("O e-mail do usuário responsável pela operação está vazio.");
        }
        if(UteisValidacao.emptyString(getUsuarioResponsavel().getColaboradorVO().getPessoa().getTelefonesCelular())){
            throw new Exception("O telefone celular do usuário responsável pela operação está vazio.");
        }
        if(UteisValidacao.emptyString(getEmpresaLogada().getCNPJ())){
            throw new Exception("O cnpj da empresa está vazio.");
        }
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public EmpresaVO getEmpresaLogada() {
        return empresaLogada;
    }

    public void setEmpresaLogada(EmpresaVO empresaLogada) {
        this.empresaLogada = empresaLogada;
    }

    public boolean isPodeAdquirirModulo() {
        return podeAdquirirModulo;
    }

    public void setPodeAdquirirModulo(boolean podeAdquirirModulo) {
        this.podeAdquirirModulo = podeAdquirirModulo;
    }

    public String getDescProdutoPacto() {
        return descProdutoPacto;
    }

    public void setDescProdutoPacto(String descProdutoPacto) {
        this.descProdutoPacto = descProdutoPacto;
    }

    public String getValorAdesaoProdutoPacto() {
        return valorAdesaoProdutoPacto;
    }

    public void setValorAdesaoProdutoPacto(String valorAdesaoProdutoPacto) {
        this.valorAdesaoProdutoPacto = valorAdesaoProdutoPacto;
    }

    public String getValorMensalidadeProdutoPacto() {
        return valorMensalidadeProdutoPacto;
    }

    public void setValorMensalidadeProdutoPacto(String valorMensalidadeProdutoPacto) {
        this.valorMensalidadeProdutoPacto = valorMensalidadeProdutoPacto;
    }

    public int getIdProdutoPacto() {
        return idProdutoPacto;
    }

    public void setIdProdutoPacto(int idProdutoPacto) {
        this.idProdutoPacto = idProdutoPacto;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.acesso;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.SessaoExpiradaException;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.arquitetura.security.LoginControle;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.PessoaConsultaTO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import negocio.facade.jdbc.arquitetura.ControleAcesso;
import servicos.integracao.IntegracaoCadastrosWSConsumer;

/**
 *
 * <AUTHOR>
 */
public class AutorizacaoAcessoGrupoEmpresarialControle extends SuperControle {

    private AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
    private List<SelectItem> integracoes = new ArrayList<>();
    private List<SelectItem> tiposPessoa = new ArrayList<>();
    private List<IntegracaoAcessoGrupoEmpresarialVO> listaIntegracoes = new ArrayList<>();
    private String paramConsulta = "";
    private List<PessoaConsultaTO> pessoas = new ArrayList<>();
    private String senhaNova = "";
    private String senhaNovaConfirmar = "";
    private Boolean senha11Digitos = false;
    private Integer empresaFiltro = 0;
    private boolean consultarInfoTodasEmpresas = false;
    protected List listaSelectItemEmpresa;
    private Integer statusFotoValida = 0;

    public AutorizacaoAcessoGrupoEmpresarialControle() throws Exception {
        try {
            obterUsuarioLogado();
            inicializarPermissaoTodasEmpresas();
            montarListaSelectItemEmpresa();
        } catch (SessaoExpiradaException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public String novo() {
        try {
            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            setParamConsulta("");
            autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
            limparConsulta();
            if (getUsuarioLogado().getAdministrador()) {
                montarListaEmpresas();
            } else {
                autorizacao.setEmpresaLocal(getEmpresaLogado());
                montarIntegracoes();
                setSenha11Digitos(getEmpresaLogado().isSenhaAcessoOnzeDigitos());
            }
            montarTiposPessoa();
            autorizacao.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            limparMsg();
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemID(MSG_ERRO);
            return "";
        }
    }

    public String editar() {
        try {
            limparMsg();
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
            auto.setPedirPermissao(false);
            setParamConsulta("");
            autorizacao = getFacade().getAutorizacaoAcessoGrupoEmpresarial().consultarPorCodigo(codigoConsulta,
                    null, null, null, null, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
            limparConsulta();
            if (getUsuarioLogado().getAdministrador()) {
                montarListaEmpresas();
            } else {
                setSenha11Digitos(getEmpresaLogado().isSenhaAcessoOnzeDigitos());
            }
            montarIntegracoes();
            montarTiposPessoa();
            selecionarIntegracao(false);
            if (autorizacao != null && !UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                statusFotoValida = getFacade().getAutorizacaoAcessoGrupoEmpresarial().statusFotoValida(getAutorizacao().getCodigo());
            }
            limparMsg();
            autorizacao.registrarObjetoVOAntesDaAlteracao();
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemID(MSG_ERRO);
            return "";
        }
    }

    public void selecionarAutorizado() {
        try {
            limparMsg();
            PessoaConsultaTO pessoa = (PessoaConsultaTO) JSFUtilities.getFromRequest("pessoa");
            if (pessoa == null) {
                throw new ConsistirException("Houve um problema ao selecionar a pessoa");
            }
            autorizacao.preencherComPessoaConsulta(pessoa);
            setMensagem(getFacade().getAutorizacaoAcessoGrupoEmpresarial().validarExistenciaDados(autorizacao));
        } catch (Exception e) {
            autorizacao.setCodAcesso("");
            autorizacao.setCodAcessoAlternativo("");
            autorizacao.setNomePessoa("");
            autorizacao.setCodigoGenerico(0);
            autorizacao.setCodigoMatricula(0);
            autorizacao.setSenhaAcesso("");
            autorizacao.setCodigoPessoa(0);
            autorizacao.setProps(new HashMap<>());
            setMensagemDetalhada(e);
            setMensagemDetalhada(MSG_ERRO, getMensagemDetalhada());

        }
    }

    public String inicializarConsultar() {
        limparMsg();
        inicializarPermissaoTodasEmpresas();
        montarListaSelectItemEmpresa();
        return "consultar";
    }

    private void inicializarPermissaoTodasEmpresas() {
        try {
            boolean temPermissao = permissao("ConsultarInfoTodasEmpresas");
            consultarInfoTodasEmpresas = temPermissao;

            if (getUsuarioLogado().getAdministrador() || temPermissao) {
                empresaFiltro = 0;
                consultarInfoTodasEmpresas = true;
            } else {
                empresaFiltro = getEmpresaLogado().getCodigo();
            }
        } catch (Exception ignored) {
        }
    }

    public void validarEmpresa() {
        try {
            if (autorizacao.getEmpresaRemota() == null || UteisValidacao.emptyNumber(autorizacao.getEmpresaRemota().getCodigo())) {
                montarMsgAlert(getMensagemInternalizacao("msg_selecione_empresa_remota"));
                return;
            }
            setMsgAlert("Richfaces.showModalPanel('consultaPessoaModal');");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void montarTiposPessoa() {
        tiposPessoa = new ArrayList<>();
        for (TipoPessoaEnum tipo : TipoPessoaEnum.values()) {
            tiposPessoa.add(new SelectItem(tipo.getTipo(), tipo.getLabel()));
        }
    }

    public void limparConsulta() {
        pessoas = new ArrayList<>();
    }

    public void limpar() {
        limparConsulta();
        limparAutorizacao();
    }

    public void consultarPessoas() {
        try {
            limparMsg();
            setMsgAlert("");
            limparConsulta();
            if (UteisValidacao.emptyString(paramConsulta)) {
                montarMsgAlert(getMensagemInternalizacao(getAluno() ? "msg_informe_matriculaounome" : "msg_informe_matriculaounomecol"));
                return;
            }
            if (getAluno()) {
                List<ClienteVO> listaClientes = IntegracaoCadastrosWSConsumer.getListaClientes(autorizacao.getIntegracao().getUrlZillyonWeb(), autorizacao.getIntegracao().getChave(), autorizacao.getEmpresaRemota().getCodigo(), paramConsulta);
                for (ClienteVO cliente : listaClientes) {
                    pessoas.add(new PessoaConsultaTO(cliente));
                }
            } else {
                List<ColaboradorVO> listaColaboradores = IntegracaoCadastrosWSConsumer.getListaColaboradores(autorizacao.getIntegracao().getUrlZillyonWeb(), autorizacao.getIntegracao().getChave(), autorizacao.getEmpresaRemota().getCodigo(), paramConsulta);
                for (ColaboradorVO colaborador : listaColaboradores) {
                    pessoas.add(new PessoaConsultaTO(colaborador));
                }
            }
        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void limparAutorizacao() {
        autorizacao.setCodAcesso("");
        autorizacao.setCodAcessoAlternativo("");
        autorizacao.setNomePessoa("");
        autorizacao.setCodigoGenerico(0);
        autorizacao.setCodigoMatricula(0);
        autorizacao.setSenhaAcesso("");
        autorizacao.setProps(new HashMap<>());
    }

    public void selecionarIntegracao() {
        selecionarIntegracao(true);
    }

    public void selecionarIntegracao(boolean limparAutorizacao) {
        try {
            if (limparAutorizacao) {
                limparAutorizacao();
            }
            autorizacao.setEmpresaRemota(new EmpresaVO());
            if (UteisValidacao.emptyNumber(autorizacao.getIntegracao().getCodigo())) {
                return;
            }
            for (IntegracaoAcessoGrupoEmpresarialVO integracao : listaIntegracoes) {
                if (integracao.getCodigo().equals(autorizacao.getIntegracao().getCodigo())) {
                    autorizacao.getEmpresaRemota().setNome(integracao.getEmpresaRemota().getNome());
                    autorizacao.getEmpresaRemota().setCodigo(integracao.getEmpresaRemota().getCodigo());
                    autorizacao.setIntegracao((IntegracaoAcessoGrupoEmpresarialVO) integracao.getClone(true));
                    break;
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemID(MSG_ERRO);
        }
    }

    public void acao(final String acao) {
        final AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        limparMsg();
        try {
            if (acao.equals("gravar")) {
                AutorizacaoAcessoGrupoEmpresarialVO.validarDados(autorizacao);
            }
            AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

                @Override
                public void onAutorizacaoComSucesso() throws Exception {
                    autorizacao.setUsuarioResponsavel(getUsuario());
                    if (acao.equals("gravar")) {
                        if (autorizacao.getNovoObj()) {
                            getFacade().getAutorizacaoAcessoGrupoEmpresarial().incluir(autorizacao);
                            incluirLogInclusao();
                        } else {
                            getFacade().getAutorizacaoAcessoGrupoEmpresarial().alterar(autorizacao);
                            incluirLogAlteracao();
                        }
                        setMensagemID("msg_dados_gravados");
                    }
                    if (acao.equals("excluir")) {
                        getFacade().getAutorizacaoAcessoGrupoEmpresarial().excluir(autorizacao);
                        incluirLogExclusao();
                        novo();
                        setMensagemID("msg_dados_excluidos");
                    }
                    if (acao.equals("alterarSenha")) {
                        setMsgAlert("");

                        getFacade().getAutorizacaoAcessoGrupoEmpresarial().alterarSenhaAcesso(autorizacao, Uteis.encriptar(senhaNova));
                        incluirLogAlteracao();
                        setMensagemID("msg_dados_gravados");
                        montarSucesso("Senha cadastrada com sucesso!");
                        setMsgAlert("Richfaces.hideModalPanel('alterarSenhaAutorizacao');");
                    }
                    setSucesso(true);
                    setErro(false);
                    autorizacao.registrarObjetoVOAntesDaAlteracao();
                }

                @Override
                public String getExecutarAoCompletar() {
                    return getOnComplete();
                }
            };
            auto.autorizar("Incluir / Alterar autorização de acesso em grupo empresarial", "IncluirAutorizacaoAGE",
                    "Você precisa da permissão \"Incluir / Alterar autorização de acesso em grupo empresarial\"",
                    "form,alterarSenhaAutorizacao", listener);
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void abrirPopup() {
        try {
            setMsgAlert("");
            limparMsg();
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setMsgAlert("abrirPopup('autorizacaoAcessoGrupoCons.jsp', 'Autorizacao', 1000, 650);");
                    return;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().
                            consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "ConsultarAutorizacaoAGE", "1.09 - Consultar autorização de acesso em grupo empresarial");
                }
            }
            setMsgAlert("abrirPopup('autorizacaoAcessoGrupoCons.jsp', 'Autorizacao', 1000, 650);");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public void excluir() {
        acao("excluir");
    }

    public void gravar() {
        acao("gravar");
    }

    public void montarIntegracoes() {
        integracoes = new ArrayList<>();
        integracoes.add(new SelectItem(0, ""));
        try {
            listaIntegracoes = getFacade().getIntegracaoAcessoGrupoEmpresarial().consultar("", autorizacao.getEmpresaLocal().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (IntegracaoAcessoGrupoEmpresarialVO integracao : listaIntegracoes) {
                integracoes.add(new SelectItem(integracao.getCodigo(), integracao.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemID(MSG_ERRO);
        }

    }

    /**
     * Metodo responsável por criar a lista de empresas disponíveis para filtro
     * @param prm
     * @param temPemissaoConsultarTodas
     * @throws Exception
     */
    public void montarListaSelectItemEmpresa(String prm, boolean temPemissaoConsultarTodas) throws Exception {
        List resultadoConsulta = getFacade().getEmpresa().consultarPorNome(prm, true, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(0, temPemissaoConsultarTodas ? "TODAS EMPRESAS" : ""));
        if (ControleAcesso.isPermiteMultiEmpresas() || getUsuarioLogado().getAdministrador() || temPemissaoConsultarTodas) {
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
            }
        } else {
            int empLogado = getEmpresaLogado().getCodigo();
            while (i.hasNext()) {
                EmpresaVO obj = (EmpresaVO) i.next();
                if (empLogado == obj.getCodigo().intValue()) {
                    objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
                }
            }
        }
        setListaSelectItemEmpresa(objs);
    }

    public void montarListaSelectItemEmpresa() {
        try {
            montarListaSelectItemEmpresa("", false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Integer getStatusFotoValida() {
        if (statusFotoValida == null) {
            statusFotoValida = 0;
        }
        return statusFotoValida;
    }

    public void setStatusFotoValida(Integer statusFotoValida) {
        this.statusFotoValida = statusFotoValida;
    }

    public AutorizacaoAcessoGrupoEmpresarialVO getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(AutorizacaoAcessoGrupoEmpresarialVO autorizacao) {
        this.autorizacao = autorizacao;
    }

    public List<SelectItem> getIntegracoes() {
        return integracoes;
    }

    public void setIntegracoes(List<SelectItem> integracoes) {
        this.integracoes = integracoes;
    }

    public List<SelectItem> getTiposPessoa() {
        return tiposPessoa;
    }

    public void setTiposPessoa(List<SelectItem> tiposPessoa) {
        this.tiposPessoa = tiposPessoa;
    }

    public boolean getAluno() {
        return autorizacao.getTipoPessoa().equals(TipoPessoaEnum.ALUNO.getTipo());
    }

    public String getLabelCodigo() {
        return getAluno() ? "Matrícula" : "Código";
    }

    public String getParamConsulta() {
        return paramConsulta;
    }

    public void setParamConsulta(String paramConsulta) {
        this.paramConsulta = paramConsulta;
    }

    public List<PessoaConsultaTO> getPessoas() {
        return pessoas;
    }

    public String getOnComplete() {//chamada implícita por 'AutorizacaoFuncionalidadeControle.control.onComplete'
        return getMsgAlert();
    }

        public void incluirLogInclusao() throws Exception {
        try {
            autorizacao.setObjetoVOAntesAlteracao(new AutorizacaoAcessoGrupoEmpresarialVO());
            autorizacao.setNovoObj(true);
            registrarLogObjetoVO(autorizacao, autorizacao.getCodigo(), "AUTORIZACAOACESSOGRUPOEMPRESARIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("AUTORIZACAOACESSOGRUPOEMPRESARIAL", autorizacao.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE AUTORIZACAOACESSOGRUPOEMPRESARIAL", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        autorizacao.setNovoObj(false);
        autorizacao.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            autorizacao.setObjetoVOAntesAlteracao(new AutorizacaoAcessoGrupoEmpresarialVO());
            autorizacao.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(autorizacao, autorizacao.getCodigo(), "AUTORIZACAOACESSOGRUPOEMPRESARIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("AUTORIZACAOACESSOGRUPOEMPRESARIAL", autorizacao.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE AUTORIZACAOACESSOGRUPOEMPRESARIAL ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(autorizacao, autorizacao.getCodigo(), "AUTORIZACAOACESSOGRUPOEMPRESARIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("AUTORIZACAOACESSOGRUPOEMPRESARIAL", autorizacao.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE AUTORIZACAOACESSOGRUPOEMPRESARIAL ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        autorizacao.registrarObjetoVOAntesDaAlteracao();
    }

     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = autorizacao.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), autorizacao.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public String getSenhaNova() {
        return senhaNova;
    }

    public void setSenhaNova(String senhaNova) {
        this.senhaNova = senhaNova;
    }

    public String getSenhaNovaConfirmar() {
        return senhaNovaConfirmar;
    }

    public void setSenhaNovaConfirmar(String senhaNovaConfirmar) {
        this.senhaNovaConfirmar = senhaNovaConfirmar;
    }

    public void limparCamposSenha() {
       this.senhaNova = "";
       this.senhaNovaConfirmar="";
    }


    public void alterarSenhaAcesso() {
        try{
            setMsgAlert("");
            limparMsg();
            autorizacao.senhaAcessoValida(senhaNova, senhaNovaConfirmar, getSenha11Digitos());
             // A senha tem que ser única.
            String senhaEncriptada = Uteis.encriptar(senhaNova);
            if (getFacade().getPessoa().senhaAcessoJaUtilizada(autorizacao.getCodigo(), 0, senhaEncriptada)) {
                throw new Exception("Senha não permitida. Informe outra senha.");
            }
            acao("alterarSenha");
        }catch(Exception e){
            montarErro(e);
        }
    }

    public void alterarSenha() {
        acao("alterarSenha");
    }

    public Boolean getSenha11Digitos() {
        return senha11Digitos;
    }

    public void setSenha11Digitos(Boolean senha11Digitos) {
        this.senha11Digitos = senha11Digitos;
    }

    public Integer getEmpresaFiltro() {
        return empresaFiltro;
    }

    public void setEmpresaFiltro(Integer empresaFiltro) {
        this.empresaFiltro = empresaFiltro;
    }

    public boolean isConsultarInfoTodasEmpresas() {
        return consultarInfoTodasEmpresas;
    }

    public void setConsultarInfoTodasEmpresas(boolean consultarInfoTodasEmpresas) {
        this.consultarInfoTodasEmpresas = consultarInfoTodasEmpresas;
    }

    public List getListaSelectItemEmpresa() {
        if (listaSelectItemEmpresa == null) {
            listaSelectItemEmpresa = new ArrayList();
        }
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }
}


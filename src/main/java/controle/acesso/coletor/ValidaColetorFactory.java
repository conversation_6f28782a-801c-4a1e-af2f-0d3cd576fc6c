package controle.acesso.coletor;

import negocio.comuns.acesso.ColetorVO;

public class ValidaColetorFactory {

    public static void validaColetor(ColetorVO coletorVO) throws Exception {

        ValidaColetorImpl iValidaColetor;

        switch (coletorVO.getModelo()) {
            case MODELO_COLETOR_INTEGRA_FACIL:
                iValidaColetor = new IntegraFacilImpl();
                break;
            case MODELO_COLETOR_TUPA:
                iValidaColetor = new TupaImpl();
                break;
            case MODELO_COLETOR_SYSTEMTECV4:
                iValidaColetor = new SystemTecImpl();
                break;

            default:
                iValidaColetor = new ValidadorPadrao();
        }

        iValidaColetor.validar(coletorVO);

    }
}

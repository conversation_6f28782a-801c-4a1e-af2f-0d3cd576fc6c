package controle.acesso.coletor;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.utilitarias.validator.IPAddressValidator;

public class TupaImpl  extends ValidadorPadrao {

    @Override
    public void valida(ColetorVO coletorVO) throws Exception {

        if (null == coletorVO.getIp() || coletorVO.getIp().isEmpty() || !IPAddressValidator.validate(coletorVO.getIp())) {
            throw new Exception("IP de comunicação com a catraca (" + coletorVO.getModelo().getDescricao() + ") é obrigatória e deve ser válida");
        }

        if (null == coletorVO.getPorta() || coletorVO.getPorta() < 1) {
            throw new Exception("Porta de comunicação com a catraca ("+coletorVO.getModelo().getDescricao()+") é obrigatória");
        }


    }
}
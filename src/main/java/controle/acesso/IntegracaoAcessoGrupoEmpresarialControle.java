package controle.acesso;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.RedeEmpresaVO;
import servicos.integracao.IntegracaoCadastrosWSConsumer;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class IntegracaoAcessoGrupoEmpresarialControle extends SuperControle {

    private IntegracaoAcessoGrupoEmpresarialVO integracaoAcesso = new IntegracaoAcessoGrupoEmpresarialVO();
    private List<IntegracaoAcessoGrupoEmpresarialVO> lista = new ArrayList<IntegracaoAcessoGrupoEmpresarialVO>();
    private List<SelectItem> listaEmpresasRemotas = new ArrayList<SelectItem>();

    private List<SelectItem> listaLocaisAcesso = new ArrayList<SelectItem>();
    private List<SelectItem> listaColetores = new ArrayList<SelectItem>();
    private String urlUsada = "";
    private String chaveUsada = "";
    private List<ColetorVO> coletores = new ArrayList<ColetorVO>();
    private List<EmpresaVO> empresasRemotas;
    private String msgAlert;

    public void abrirPopup() {
        try {
            setMsgAlert("");
            limparMsg();
            setMsgAlert("abrirPopup('integracaoAcessoCons.jsp', 'Integracao', 1000, 650);");
        } catch (Exception e) {
            setMensagemDetalhada(e);
            montarMsgAlert(getMensagemDetalhada());
        }
    }

    public String novo() {
        try {
            urlUsada = "";
            chaveUsada = "";
            integracaoAcesso = new IntegracaoAcessoGrupoEmpresarialVO();
            listaEmpresasRemotas = new ArrayList<SelectItem>();
            listaLocaisAcesso = new ArrayList<SelectItem>();
            listaColetores = new ArrayList<SelectItem>();
            if (getUsuarioLogado().getAdministrador()) {
                montarListaEmpresas();
            }else{
                integracaoAcesso.setEmpresaLocal(getEmpresaLogado());
            }
            setSucesso(false);
            setErro(false);
            setMensagemID("msg_entre_dados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public String inicializarConsultar(){
        limparMsg();
        return "consultar";
    }

    public String editar(){
        try {
            limparMsg();
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
            integracaoAcesso = getFacade().getIntegracaoAcessoGrupoEmpresarial().consultarPorCodigo(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(getUsuarioLogado().getAdministrador()){
                montarListaEmpresas();
            }
            consultarEmpresasRemotas(false);
            montarLocaisAcesso(false);
            montarColetores(false);
            setSucesso(false);
            setErro(false);
            integracaoAcesso.registrarObjetoVOAntesDaAlteracao();
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemID("msg_erro");
            return "";
        }
    }
    public void consultarEmpresasRemotas() {
        consultarEmpresasRemotas(true);
    }

    private void consultarEmpresasRemotas(boolean limparCodigoChaveIntegracao) {
        try {
            if (!UteisValidacao.emptyString(integracaoAcesso.getChave())
                    && !UteisValidacao.emptyString(integracaoAcesso.getUrlZillyonWeb())
                    && (!urlUsada.equals(integracaoAcesso.getUrlZillyonWeb())
                    || !chaveUsada.equals(integracaoAcesso.getChave()))) {
                limparMsg();
                if (integracaoAcesso.getChave().equals(getKey())) {
                    RedeEmpresaVO redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
                    if (redeEmpresaVO == null || !redeEmpresaVO.getGestaoRedes()) {
                        throw new Exception(getMensagemInternalizacao("msg_mesma_chave"));
                    }
                }
                if (limparCodigoChaveIntegracao) {
                    integracaoAcesso.setCodigoChaveIntegracaoDigitais(0);
                }
                urlUsada = integracaoAcesso.getUrlZillyonWeb();
                chaveUsada = integracaoAcesso.getChave();
                listaLocaisAcesso = new ArrayList<>();
                listaColetores = new ArrayList<>();
                List<EmpresaVO> empresas = IntegracaoCadastrosWSConsumer.getListaEmpresasRemotas(integracaoAcesso.getUrlZillyonWeb(), integracaoAcesso.getChave());
                setEmpresasRemotas(empresas);
                listaEmpresasRemotas = obterListaEmpresas(empresas);
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            setMensagemDetalhada("msg_erro", getMensagemDetalhada());
            listaEmpresasRemotas = new ArrayList<>();
            listaLocaisAcesso = new ArrayList<>();
            listaColetores = new ArrayList<>();
            setEmpresasRemotas(new ArrayList<>());
        }
    }

    public void gravar() {
        try {
            for (EmpresaVO empresaVO : getEmpresasRemotas()) {
                if (integracaoAcesso.getEmpresaRemota().getCodigo().equals(empresaVO.getCodigo())) {
                    integracaoAcesso.setCodigoChaveIntegracaoDigitais(empresaVO.getCodigoChaveIntegracaoDigitais());
                    break;
                }
            }
            if(integracaoAcesso.getNovoObj()){
                getFacade().getIntegracaoAcessoGrupoEmpresarial().incluir(integracaoAcesso);
                incluirLogInclusao();
            }else{
                getFacade().getIntegracaoAcessoGrupoEmpresarial().alterar(integracaoAcesso);
                incluirLogAlteracao();
            }

            setErro(false);
            setSucesso(true);
            setMensagemID("msg_dados_gravados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setErro(true);
            setSucesso(false);
        }
    }

        public String excluir() {
        try {
            getFacade().getIntegracaoAcessoGrupoEmpresarial().excluir(integracaoAcesso);
            incluirLogExclusao();
            novo();
            setErro(false);
            setSucesso(true);
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"integracaoacessogrupoempresarial\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"integracaoacessogrupoempresarial\" violates foreign key")){
                setMensagemDetalhada("Este Horário não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setErro(true);
            setSucesso(false);
            return "";
        }
    }

    private void selecionarEmpresa(){
        integracaoAcesso.getEmpresaRemota().setNome(Uteis.retornaLabelObjetoSelecionado(listaEmpresasRemotas, integracaoAcesso.getEmpresaRemota().getCodigo()));
    }

    public void montarLocaisAcesso() {
        montarLocaisAcesso(true);
    }

    public void selecionarColetor() {
        integracaoAcesso.setTerminal(0);
        for (ColetorVO coletor : coletores) {
            if (!UteisValidacao.emptyNumber(integracaoAcesso.getColetor()) && integracaoAcesso.getColetor().equals(coletor.getCodigo())) {
                integracaoAcesso.setTerminal(coletor.getNumeroTerminal());
            }
        }
    }
    public void montarLocaisAcesso(boolean limpar) {
        try {
            listaLocaisAcesso = new ArrayList<SelectItem>();
            listaColetores = new ArrayList<SelectItem>();
            if(limpar){
                integracaoAcesso.setLocalAcesso(null);
                integracaoAcesso.setColetor(null);
            }
            limparMsg();
            if (UteisValidacao.emptyNumber(integracaoAcesso.getEmpresaRemota().getCodigo())) {
                return;
            }
            selecionarEmpresa();
            List<LocalAcessoVO> locais = IntegracaoCadastrosWSConsumer.getListaLocaisAcesso(integracaoAcesso.getUrlZillyonWeb(),
                                                                            integracaoAcesso.getChave(), integracaoAcesso.getEmpresaRemota().getCodigo());
            if (locais.isEmpty()) {
                throw new Exception(getMensagemInternalizacao("msg_local_acesso_empresa_nao_contem"));
            }
            listaLocaisAcesso.add(new SelectItem(null, ""));
            for (LocalAcessoVO local : locais) {
                listaLocaisAcesso.add(new SelectItem(local.getCodigo(), local.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    public void montarColetores() {
        montarColetores(true);
    }
    public void montarColetores(boolean limpar) {
        try {
            coletores = new ArrayList<ColetorVO>();
            listaColetores = new ArrayList<SelectItem>();
            if(limpar)
                integracaoAcesso.setColetor(null);
            limparMsg();
            if (UteisValidacao.emptyNumber(integracaoAcesso.getLocalAcesso())) {
                return;
            }
            coletores = IntegracaoCadastrosWSConsumer.getListaColetores(integracaoAcesso.getUrlZillyonWeb(),
                                                                            integracaoAcesso.getChave(), integracaoAcesso.getLocalAcesso());
            if (coletores.isEmpty()) {
                throw new Exception(getMensagemInternalizacao("msg_coletor_nao_contem"));
            }
            listaColetores.add(new SelectItem(null, ""));
            for (ColetorVO coletor : coletores) {
                listaColetores.add(new SelectItem(coletor.getCodigo(), coletor.getDescricao()));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }


    public void setIntegracaoAcesso(IntegracaoAcessoGrupoEmpresarialVO integracaoAcesso) {
        this.integracaoAcesso = integracaoAcesso;
    }

    public IntegracaoAcessoGrupoEmpresarialVO getIntegracaoAcesso() {
        return integracaoAcesso;
    }

    public void setListaLocaisAcesso(List<SelectItem> listaLocaisAcesso) {
        this.listaLocaisAcesso = listaLocaisAcesso;
    }

    public List<SelectItem> getListaLocaisAcesso() {
        return listaLocaisAcesso;
    }

    public void setListaColetores(List<SelectItem> listaColetores) {
        this.listaColetores = listaColetores;
    }

    public List<SelectItem> getListaColetores() {
        return listaColetores;
    }

    public void setListaEmpresasRemotas(List<SelectItem> listaEmpresasRemotas) {
        this.listaEmpresasRemotas = listaEmpresasRemotas;
    }

    public List<SelectItem> getListaEmpresasRemotas() {
        return listaEmpresasRemotas;
    }



    public void setLista(List<IntegracaoAcessoGrupoEmpresarialVO> lista) {
        this.lista = lista;
    }

    public List<IntegracaoAcessoGrupoEmpresarialVO> getLista() {
        return lista;
    }

    public void incluirLogInclusao() throws Exception {
        try {
            integracaoAcesso.setObjetoVOAntesAlteracao(new IntegracaoAcessoGrupoEmpresarialVO());
            integracaoAcesso.setNovoObj(true);
            registrarLogObjetoVO(integracaoAcesso, integracaoAcesso.getCodigo(), "INTEGRACAOACESSOGRUPOEMPRESARIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("INTEGRACAOACESSOGRUPOEMPRESARIAL", integracaoAcesso.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE INTEGRACAOACESSOGRUPOEMPRESARIAL", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        integracaoAcesso.setNovoObj(false);
        integracaoAcesso.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            integracaoAcesso.setObjetoVOAntesAlteracao(new IntegracaoAcessoGrupoEmpresarialVO());
            integracaoAcesso.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(integracaoAcesso, integracaoAcesso.getCodigo(), "INTEGRACAOACESSOGRUPOEMPRESARIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("INTEGRACAOACESSOGRUPOEMPRESARIAL", integracaoAcesso.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE INTEGRACAOACESSOGRUPOEMPRESARIAL ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(integracaoAcesso, integracaoAcesso.getCodigo(), "INTEGRACAOACESSOGRUPOEMPRESARIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("INTEGRACAOACESSOGRUPOEMPRESARIAL", integracaoAcesso.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE INTEGRACAOACESSOGRUPOEMPRESARIAL ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        integracaoAcesso.registrarObjetoVOAntesDaAlteracao();
    }

     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = integracaoAcesso.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), integracaoAcesso.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       integracaoAcesso = new IntegracaoAcessoGrupoEmpresarialVO();
       realizarConsultaLogObjetoSelecionado();
    }

    public List<EmpresaVO> getEmpresasRemotas() {
        if (empresasRemotas == null) {
            empresasRemotas = new ArrayList<EmpresaVO>();
        }
        return empresasRemotas;
    }

    public void setEmpresasRemotas(List<EmpresaVO> empresasRemotas) {
        this.empresasRemotas = empresasRemotas;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Integração de Acesso",
                "Deseja excluir a Integração de Acesso?",
                this, "excluir", "", "", "", "grupoBtnExcluir,mensagemDetalhada2");
    }
}

package controle.acesso;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.CameraVO;
import negocio.comuns.acesso.ServidorFacialVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;

public class ServidorFacialControle extends SuperControle {

    private String NOME_ENTIDADE = "SERVIDORFACIAL";
    private ServidorFacialVO servidorFacial;
    private CameraVO camera;
    private Boolean mostrarFormCamera = false;
    private String msgAlert;

    public ServidorFacialControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        inicializarEmpresaLogado();
        montarListaSelectItemEmpresa();
        setMensagemID("");
    }

    public String novo() {
        try {
            inicializarEmpresaLogado();
            getFacade().getLocalAcesso().novo();
            getServidorFacial().registrarObjetoVOAntesDaAlteracao();
            setMostrarFormCamera(false);
            limparMsg();
            return "editar";
        } catch (Exception e) {
            montarErro(e);
        }
        return "";
    }


    public void editarCamera() {
        CameraVO obj = (CameraVO) context().getExternalContext().getRequestMap().get("camera");
        setCamera(obj);
        setMostrarFormCamera(true);
    }

    public void removerCamera() {
        setMostrarFormCamera(false);
        CameraVO obj = (CameraVO) context().getExternalContext().getRequestMap().get("camera");
        getServidorFacial().excluirObjColetorVOs(obj);
        setMensagemID("msg_dados_excluidos");
    }

    public void incluirLogInclusao() throws Exception {
        try {
            servidorFacial.setObjetoVOAntesAlteracao(new AutorizacaoAcessoGrupoEmpresarialVO());
            servidorFacial.setNovoObj(true);
            registrarLogObjetoVO(servidorFacial, servidorFacial.getCodigo(), "SERVIDORFACIAL", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("SERVIDORFACIAL", servidorFacial.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE SERVIDORFACIAL", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        servidorFacial.setNovoObj(new Boolean(false));
        servidorFacial.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(servidorFacial, servidorFacial.getCodigo(), "SERVIDORFACIAL", 0);
            servidorFacial.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("FORNECEDOR", servidorFacial.getCodigo(), "ERRO AO GERAR LOG DE ALTERAO DE SERVIDORFACIAL ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        servidorFacial.registrarObjetoVOAntesDaAlteracao();
    }


    public String gravar() {
        try {
            servidorFacial.setNomecomputador(servidorFacial.getNomecomputador().toUpperCase());
            if (servidorFacial.isNovoObj()) {
                getFacade().getServidorFacial().incluir(servidorFacial);
                incluirLogInclusao();
            } else {
                getFacade().getServidorFacial().alterar(servidorFacial);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            montarErro(e);
            return "editar";
        }
    }

    public String excluir() {
        try {
            getFacade().getServidorFacial().excluir(servidorFacial);
            setMostrarFormCamera(false);
//            incluirLogExclusao();
            setServidorFacial(new ServidorFacialVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"servidorfacial\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"servidorfacial\" violates foreign key")){
                setMensagemDetalhada("Este Servidor Facial não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public List<GenericoTO> executarAutocompleteTerminal(Object suggest) {
        String pref = (String) suggest;
        List<GenericoTO> coletores;
        try {
            coletores = getFacade().getColetor().consultarColetoresPorNumeroTerminal(pref, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            coletores = new ArrayList<GenericoTO>();
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return coletores;
    }

    public void selecionarTerminal() {
        try {
            GenericoTO obj = (GenericoTO) context().getExternalContext().getRequestMap().get("result");
            this.getCamera().setEndereco(obj.getCodigoString());
            limparMsg();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        montarListaEmpresas();
        if (getEmpresaLogado().getCodigo() != 0) {
            getServidorFacial().setEmpresa(getEmpresaLogado());
        }
    }


    public void inicializarEmpresaLogado() {
        try {
            setServidorFacial(new ServidorFacialVO());
            servidorFacial.setEmpresa(getEmpresaLogado());
        } catch (Exception exception) {
            System.out.println("Erro ServidorFacialControle.inicializarEmpresaLogado() - " + exception.getMessage());
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getServidorFacial().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void realizarConsultaLogObjetoGeral() {
        servidorFacial = new ServidorFacialVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = servidorFacial.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(NOME_ENTIDADE, servidorFacial.getCodigo(), null);
    }

    public ServidorFacialVO getServidorFacial() {
        if (servidorFacial == null) {
            servidorFacial = new ServidorFacialVO();
        }
        return servidorFacial;
    }

    public void setServidorFacial(ServidorFacialVO servidorFacial) {
        this.servidorFacial = servidorFacial;
    }

    public CameraVO getCamera() {
        if (camera == null) {
            camera = new CameraVO();
        }
        return camera;
    }

    public void setCamera(CameraVO camera) {
        this.camera = camera;
    }

    public Boolean getMostrarFormCamera() {
        return mostrarFormCamera;
    }

    public void setMostrarFormCamera(Boolean mostrarFormCamera) {
        this.mostrarFormCamera = mostrarFormCamera;
    }

    public void adicionarCamera() {
        try {
            setMostrarFormCamera(true);
            camera = new CameraVO();
            camera.setServidorFacialVO(servidorFacial);
            getServidorFacial().getCameras().add(this.getCamera());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            limparMsg();
            setMostrarFormCamera(false);
            ServidorFacialVO obj = getFacade().getServidorFacial().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.setCameras(consultarCameras(obj.getCodigo()));
            setServidorFacial(obj);
            obj.registrarObjetoVOAntesDaAlteracao();
            //localAcesso.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            montarErro(e);
        }
        return "editar";
    }

    public List<CameraVO> consultarCameras(Integer servidorFacial) throws Exception {
        return getFacade().getCamera().consultarCameras(servidorFacial, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Servidor Facial",
                "Deseja excluir o Servidor Facial?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}

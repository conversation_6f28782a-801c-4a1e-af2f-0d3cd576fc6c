/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.clubevantagens;

import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TemporalRemocaoPontoEnum;
import br.com.pactosolucoes.integracao.protheus.TipoOperacaoEnum;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.security.LoginControle;
import controle.plano.PlanoControle;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.collections.Predicate;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ItemCampanhaControle extends SuperControleRelatorio {

    private ItemCampanhaVO itemCampanhaPlano;
    private ItemCampanhaVO itemCampanhaPlanoDuracao;
    private ItemCampanhaVO itemCampanhaAula;
    private List<ItemCampanhaVO> listaItemSemCampanha;
    private CampanhaDuracaoVO campanhaDuracaoSelecionado;
    private List<SelectItem> listaTipoItem = new ArrayList<SelectItem>();
    private ProdutoVO produtoPesquisa;
    private TurmaAulaCheiaJSON aulaPesquisada = new TurmaAulaCheiaJSON();
    private String onComplete;
    private PlanoVO planoPesquisado;
    private String descricaoPlano;
    private String descricaoTurma;

    private boolean mostrarPlanosComPonto = true;
    private boolean mostrarAulasComPonto = true;
    private List<ItemCampanhaVO> listaPlanosComPonto = new ArrayList<ItemCampanhaVO>();
    private List<PlanoVO> planosVO =new ArrayList<>();
    private List<ItemCampanhaVO> listaAulasComPonto = new ArrayList<ItemCampanhaVO>();
    private List<PlanoVO> listaPlanosSemPontuacao = new ArrayList<PlanoVO>();
    private List<TurmaAulaCheiaJSON> listaTurmaSemPontuacao = new ArrayList<TurmaAulaCheiaJSON>();
    private ItemCampanhaVO itemCampanhaProduto;
    private ItemCampanhaVO itemCampanhaAcesso;
    private ItemCampanhaVO itemCampanhaAcessoChuva;
    private ItemCampanhaVO itemCampanhaAcessoFrio;
    private ItemCampanhaVO itemCampanhaAcessoCalor;
    private ItemCampanhaVO itemCampanhaIndicacao;
    private ItemCampanhaVO itemCampanhaIndicacaoConvertida;
    private boolean mostrarProdutosComPonto = true;
    private List<ItemCampanhaVO> listaProdutoComPonto = new ArrayList<ItemCampanhaVO>();
    private List<ProdutoVO> listaProdutosSemPonto = new ArrayList<ProdutoVO>();
    private ListaPaginadaTO paginadorListaPlanosCP;
    private ListaPaginadaTO paginadorListaPlanosSP;
    private ListaPaginadaTO paginadorListaAulaCP;
    private ListaPaginadaTO paginadorListaAulaSP;
    private ListaPaginadaTO paginadorListaProdutosCP;
    private ListaPaginadaTO paginadorListaProdutosSP;
    private List<Integer> codigoPlanos;
    private List<SelectItem> planos;
    private EmpresaVO empresaSelecionada;

    public ItemCampanhaControle() {
        try {
            empresaSelecionada = (EmpresaVO) getEmpresaLogado().getClone(false);
            montarListaEmpresasComItemTodasClubeVantagens();
            inicializarDados();
            montarListaTipoItem();
            montarPlanos();
        } catch (Exception e) {
            montarErro(e);
        }
    }
    
    public String abrirItemCampanha(){
        try {
            mudarEmpresa();
            limparPaginadores();
            montarPlanos();
            montarListaPlano();
            montarListaProduto();
            montarItemAcesso();
            montarListaAulas();
            montarDadosIndicacoesConvertidas();
            inicializarDados();
            notificarRecursoEmpresa(RecursoSistema.CLUBE_VANTAGENS_CONFIGURACOES);
        } catch (Exception e) {
            montarErro(e);
            return "";
        }
        return "indicadores";
    }

    public void mudarEmpresa()throws Exception{
        if(empresaSelecionada.getCodigo()!=0)
            setEmpresaSelecionada(getFacade().getEmpresa().consultarPorCodigo(getEmpresaSelecionada().getCodigo(),1));
    }



    private void montarDadosIndicacoesConvertidas() {
        try {
            codigoPlanos = new ArrayList<>();
            List<ItemCampanhaVO> planosIndicacoesConvertida = getFacade().getItemCampanha().listarItensPorTipo(TipoItemCampanhaEnum.INDICACAO_CONVERTIDA.getCodigo(),empresaSelecionada.getCodigo());
            for (ItemCampanhaVO planoIndicadoConvertido : planosIndicacoesConvertida) {
                if(!codigoPlanos.contains(planoIndicadoConvertido.getChaveestrangeira()))
                    codigoPlanos.add(planoIndicadoConvertido.getChaveestrangeira());
            }
            getItemCampanhaIndicacao().setEmpresa(getEmpresaSelecionada());
            getItemCampanhaIndicacao().setCampanha(null);
            setItemCampanhaIndicacao(getFacade().getItemCampanha().consultarUnicosTipoXEmpresa(getItemCampanhaIndicacao()));
            getItemCampanhaIndicacaoConvertida().setEmpresa(getEmpresaSelecionada());
            getItemCampanhaIndicacaoConvertida().setCampanha(null);
            setItemCampanhaIndicacaoConvertida(getFacade().getItemCampanha().consultarUnicosTipoXEmpresa(getItemCampanhaIndicacaoConvertida()));
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void limparPaginadores() {
        paginadorListaProdutosCP = null;
        paginadorListaProdutosSP = null;
        paginadorListaPlanosSP = null;
        paginadorListaPlanosCP = null;
        paginadorListaAulaCP = null;
        paginadorListaAulaSP = null;
    }

    public String getLinkWiki() {
        return getUrlBaseConhecimento() + FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CONFIGURACOES.getBaseConhecimento();
    }

    private void inicializarDados() throws Exception {
        setItemCampanhaProduto(new ItemCampanhaVO());
        setItemCampanhaPlano(new ItemCampanhaVO());
        setItemCampanhaPlanoDuracao(new ItemCampanhaVO());
        setItemCampanhaAula(new ItemCampanhaVO());
        setProdutoPesquisa(new ProdutoVO());
        setPlanoPesquisado(new PlanoVO());
        setAulaPesquisada(new TurmaAulaCheiaJSON());
        setDescricaoTurma("");
        setDescricaoPlano("");


        setListaItemSemCampanha(getFacade().getItemCampanha().listaItemSemCampanha());
    }

    public void processarListaCampanha() {
        try {
            if (campanhaDuracaoSelecionado != null) {
                atualizarListaItemCampanha();
                campanhaDuracaoSelecionado.setListaItem(getFacade().getItemCampanha().listaItemCampanha(campanhaDuracaoSelecionado, Uteis.NIVELMONTARDADOS_TODOS));
                if (campanhaDuracaoSelecionado.getListaItem().isEmpty()) {
                    for (ItemCampanhaVO itemCampanhaVO : listaItemSemCampanha) {
                        itemCampanhaVO.setSelecionarItem(false);
                    }
                } else {
                    for (final ItemCampanhaVO itemCampanhaVO : campanhaDuracaoSelecionado.getListaItem()) {
                        ItemCampanhaVO item = (ItemCampanhaVO) ColecaoUtils.find(listaItemSemCampanha, new Predicate() {
                            @Override
                            public boolean evaluate(Object o) {
                                if (itemCampanhaVO.getTipoItemCampanha().getCodigo() == 3 || itemCampanhaVO.getTipoItemCampanha().getCodigo() == 6 || itemCampanhaVO.getTipoItemCampanha().getCodigo() == 7 || itemCampanhaVO.getTipoItemCampanha().getCodigo() == 8) {
                                    return ((ItemCampanhaVO) o).getTipoItemCampanha().getCodigo().equals(itemCampanhaVO.getTipoItemCampanha().getCodigo());
                                } else {
                                    return ((ItemCampanhaVO) o).getChaveestrangeira().equals(itemCampanhaVO.getChaveestrangeira());
                                }

                            }
                        });
                        listaItemSemCampanha.remove(item);
                        item.setSelecionarItem(true);
                        listaItemSemCampanha.add(item);
                    }
                }
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void atualizarListaItemCampanha() throws Exception {
        setListaItemSemCampanha(getFacade().getItemCampanha().listaItemSemCampanha());
    }

    private void montarListaTipoItem() {
        getListaTipoItem().add(new SelectItem(0, ""));
        for (TipoItemCampanhaEnum tipoItem : TipoItemCampanhaEnum.getValuesEnun()) {
            getListaTipoItem().add(new SelectItem(tipoItem.getCodigo(), tipoItem.getDescricao()));
        }
    }

    private void montarListaPlano() throws Exception {
        consultarListaPlanoCP();
        setMostrarPlanosComPonto(!getListaPlanosComPonto().isEmpty());
        consultarListaPlanoSP();

    }

    private void montarPlanos() throws Exception {
        planosVO = new ArrayList<>();
        planosVO = getFacade().getPlano().consultarVigentes(getEmpresaSelecionada().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, false, false, false);

        setPlanos(new ArrayList<SelectItem>());
        for (PlanoVO plano : planosVO) {
            getPlanos().add(new SelectItem(
                    plano.getCodigo(),
                    plano.getDescricao()
            ));
        }
    }


    public void montarListaProduto() throws Exception {
        consultarListaProdutoCP();
        setMostrarProdutosComPonto(!getListaProdutoComPonto().isEmpty());
        consultarListaProdutoSP();
    }

    public void montarListaAulas() throws Exception {
        consultarListaAulaCP();
        setMostrarAulasComPonto(!getListaAulasComPonto().isEmpty());
        consultarListaAulaSP();
    }

    public void gravarItensNaCampanha() {
        try {
            boolean gravouDados = false;
            for (ItemCampanhaVO itemCampanhaVO : listaItemSemCampanha) {
                itemCampanhaVO.setCampanha(campanhaDuracaoSelecionado);
                if (itemCampanhaVO.isSelecionarItem()) {
                    boolean jaCadastrado = getFacade().getItemCampanha().validarItemJaCadastradoCampanha(itemCampanhaVO);
                    if (!jaCadastrado) {
                        ItemCampanhaVO itemCampanhaVO1 = new ItemCampanhaVO();
                        itemCampanhaVO1.setChaveestrangeira(itemCampanhaVO.getChaveestrangeira());
                        itemCampanhaVO1.setTipoItemCampanha(itemCampanhaVO.getTipoItemCampanha());
                        itemCampanhaVO1.setPontos(itemCampanhaVO.getPontos());
                        itemCampanhaVO1.setDuracao(itemCampanhaVO.getDuracao());
                        if (itemCampanhaVO.getTipoItemCampanha() == TipoItemCampanhaEnum.PRODUTO) {
                            itemCampanhaVO1.setProdutoReferencia(itemCampanhaVO.getProdutoReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANO) {
                            itemCampanhaVO1.setPlanoReferencia(itemCampanhaVO.getPlanoReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.AULA) {
                            itemCampanhaVO1.setAulaReferencia(itemCampanhaVO.getAulaReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSO) {
                            itemCampanhaVO1.setAcessoReferencia(itemCampanhaVO.getAcessoReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSOCHUVA) {
                            itemCampanhaVO1.setAcessoReferencia(itemCampanhaVO.getAcessoReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSOFRIO) {
                            itemCampanhaVO1.setAcessoReferencia(itemCampanhaVO.getAcessoReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.ACESSOCALOR) {
                            itemCampanhaVO1.setAcessoReferencia(itemCampanhaVO.getAcessoReferencia());
                        } else if (itemCampanhaVO1.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANODURACAO) {
                            itemCampanhaVO1.setPlanoDuracaoReferencia(itemCampanhaVO.getPlanoDuracaoReferencia());
                        }
                        itemCampanhaVO1.setCampanha(campanhaDuracaoSelecionado);
                        getFacade().getItemCampanha().incluir(itemCampanhaVO1);
                        gravouDados = true;
                    }
                } else {
                    ItemCampanhaVO itemCampanhaVO1 = getFacade().getItemCampanha().consultarPorCampanhaChaveEstrangeira(campanhaDuracaoSelecionado.getCodigo(), itemCampanhaVO.getChaveestrangeira(), itemCampanhaVO.getTipoItemCampanha().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    if (itemCampanhaVO1.getCodigo() > 0) {
                        getFacade().getItemCampanha().excluir(false,itemCampanhaVO1);
                    }
                }
            }
            if (gravouDados) {
                montarSucessoDadosGravados();
            } else {
                limparMsg();
                montarAviso("Nenhum item novo foi selecionado! Gravado com sucesso.");
            }
        } catch (Exception e) {
            if(e.getMessage().contains("permissão"))
                montarErro(e.getMessage());
            else
                montarErro(e);
        }
    }

    public void incluirLogInclusao() throws Exception {
        try {
            itemCampanhaPlano.setObjetoVOAntesAlteracao(new ItemCampanhaVO());
            itemCampanhaPlano.setNovoObj(true);
            registrarLogObjetoVO(itemCampanhaPlano, itemCampanhaPlano.getCodigo(), "ITEMCAMPANHA", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("ITEMCAMPANHA", itemCampanhaPlano.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DO ITEMCAMPANHA", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        itemCampanhaPlano.setNovoObj(new Boolean(false));
        itemCampanhaPlano.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = itemCampanhaPlano.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), itemCampanhaPlano.getCodigo(), 0);
    }


    public List<TurmaAulaCheiaJSON> executarAutocompleteConsultaAula(Object suggest) {
        String pref = (String) suggest;
        List<TurmaAulaCheiaJSON> result = null;
        try {
            if (!pref.equals("%")) {
                result = getFacade().getTurma().consultarPorDescricaoTurmaColetiva(pref, getEmpresaSelecionada().getCodigo());
            }
        } catch (Exception e) {
            result = (new ArrayList<TurmaAulaCheiaJSON>());
            montarErro(e.getMessage());
        }
        return result;
    }

    public void selecionarAulaSuggestionBox(ActionEvent actionEvent) {
        TurmaAulaCheiaJSON aulaCheiaJSON = (TurmaAulaCheiaJSON) actionEvent.getComponent().getAttributes().get("pequisaTurma");
        selecionarAulaSuggestionBox(aulaCheiaJSON);
    }

    private void selecionarAulaSuggestionBox(TurmaAulaCheiaJSON aulaSelecionada) {
        try {
            setAulaPesquisada(aulaSelecionada);
            setDescricaoTurma(aulaSelecionada.getNome());
            if (getFacade().getItemCampanha().validarItemJaCadastradoChaveEstrangeira(aulaSelecionada.getCodigo(), TipoItemCampanhaEnum.AULA.getCodigo(),aulaSelecionada.getEmpresa())) {
                itemCampanhaAula = getFacade().getItemCampanha().consultarPorChaveEstrangeira(aulaSelecionada.getEmpresa(), aulaSelecionada.getCodigo(), TipoItemCampanhaEnum.AULA.getCodigo());
                aulaPesquisada.setPontosBonus(itemCampanhaAula.getPontos());
            }
            setOnComplete("");
        } catch (Exception e) {
            montarErro(e);
            setOnComplete(getMensagemNotificar());
        }
    }

    public List<ProdutoVO> executarAutocompleteConsultaProduto(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ProdutoVO> result = null;
        try {
            if (!pref.equals("%")) {
                result = (ArrayList<ProdutoVO>) getFacade().getProduto().consultarPorDescricaoAtivo(pref, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS,
                        "'AC', 'AH', 'CC', 'DC', 'DV', 'RD', 'CH', 'AT', 'MM', 'PM', 'CD', 'QU', 'CP', 'DE', 'DR', 'TN', 'FR', 'TR', 'TP'");
            }
        } catch (Exception e) {
            result = (new ArrayList<ProdutoVO>());
            montarErro(e.getMessage());
        }
        return result;
    }

    public void selecionarProdutoSuggestionBox(ActionEvent actionEvent) {
        ProdutoVO produtoVO = (ProdutoVO) actionEvent.getComponent().getAttributes().get("pesquisaProduto");
        selecionarProdutoSuggestionBox(produtoVO);
    }

    private void selecionarProdutoSuggestionBox(ProdutoVO produtoselecionado) {
        try {
            setProdutoPesquisa(produtoselecionado);
            if (getFacade().getItemCampanha().validarItemJaCadastradoChaveEstrangeira(produtoselecionado.getCodigo(), TipoItemCampanhaEnum.PRODUTO.getCodigo(),null)) {
                itemCampanhaProduto = getFacade().getItemCampanha().consultarPorChaveEstrangeira(null, getProdutoPesquisa().getCodigo(), TipoItemCampanhaEnum.PRODUTO.getCodigo());
                produtoselecionado.setPontos(itemCampanhaProduto.getPontos());
            }
            setOnComplete("");
        } catch (Exception e) {
            montarErro(e);
            setOnComplete(getMensagemNotificar());
        }
    }

    public List<PlanoVO> executarAutocompleteConsultaPlano(Object suggest) {
        String pref = (String) suggest;
        List<PlanoVO> result = null;
        try {
            if (!pref.equals("%")) {
                result = getFacade().getPlano().consultarPorDescricaoPlanoVigente(pref, getEmpresaSelecionada().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
        } catch (Exception e) {
            result = (new ArrayList<PlanoVO>());
            montarErro(e.getMessage());
        }
        return result;
    }

    public void salvarConfigPontos() {
        try {
            validarPermissaoConfiguracao();
            getFacade().getEmpresa().alterarConfiguracoesClubeVantagens(getEmpresaSelecionada());
            montarSucessoGrowl("Configurações gravadas com sucesso");
            regristrarLogAlteracaoEmpresa();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void regristrarLogAlteracaoEmpresa() throws Exception {
        registrarLogClubeDeVantagens("ClubeDeVantagens Alteração pontos Empresa Apenas Primeiro Acesso: "+ getEmpresaSelecionada().isApenasPrimeiroAcessoClubeVantagens()+
                " Min para creditar Proximo Ponto: "+ getEmpresaSelecionada().getMinutosCreditarProximoPontoClubeVantagens()+
                " Zerar Pontos Apos vencimento :"+ getEmpresaSelecionada().getZerarPontosAposVencimento().getDescricao()+
                " Pontuar apenas campanhas ativas :"+getEmpresaSelecionada().isPontuarApenasCategoriasEmCampanhasAtivas()+
                " Aplicar indicacao qualquer Plano :" +getEmpresaSelecionada().isAplicarIndicacaoQlqrPlano(), TipoOperacaoEnum.ALTERACAO);
    }

    public void salvarItemCampanhaPlano() {
        try {
            validarPermissaoConfiguracao();
            if (getItemCampanhaPlano()==null || UteisValidacao.emptyNumber(getItemCampanhaPlano().getCodigo()) || getEmpresaSelecionada().getCodigo() == 0) {
                if (getPlanoPesquisado().getCodigo() == 0) {
                    throw new Exception("Selecione um plano");
                }
                List<Integer> codEmpresas = new ArrayList<>();
                if (getEmpresaSelecionada().getCodigo() == 0) {
                    for (PlanoVO plano : planosVO) {
                        if (plano.getCodigo() == getPlanoPesquisado().getCodigo()) {
                            getPlanoPesquisado().setEmpresa(plano.getEmpresa());
                            break;
                        }
                    }
                } else
                    codEmpresas.add(getEmpresaSelecionada().getCodigo());


                ItemCampanhaVO itemCampanhaNovoPlano = new ItemCampanhaVO();
                itemCampanhaNovoPlano.setEmpresa(getPlanoPesquisado().getEmpresa());
                itemCampanhaNovoPlano.setTipoItemCampanha(TipoItemCampanhaEnum.PLANO);
                itemCampanhaNovoPlano.setChaveestrangeira(getPlanoPesquisado().getCodigo());
                itemCampanhaNovoPlano.setPontos(getPlanoPesquisado().getPontos());

                getFacade().getItemCampanha().alterarOuIncluir(itemCampanhaNovoPlano);
                getFacade().getPlano().alterarPontos(getPlanoPesquisado().getCodigo(), getPlanoPesquisado().getPontos(), TipoItemCampanhaEnum.PLANO.getCodigo());

                // Altera para todas a empresas os itens de PlanoDuracao
                for (PlanoDuracaoVO planoDuracaoVO : getPlanoPesquisado().getPlanoDuracaoVOs()) {
                    salvarItemCampanhaPlanoDuracao(planoPesquisado.getEmpresa(), planoDuracaoVO);
                }

            } else {
                getItemCampanhaPlano().setPontos(getPlanoPesquisado().getPontos());
                getFacade().getItemCampanha().alterar(getItemCampanhaPlano());
                getFacade().getPlano().alterarPontos(getPlanoPesquisado().getCodigo(), getPlanoPesquisado().getPontos(), TipoItemCampanhaEnum.PLANO.getCodigo());

                for (PlanoDuracaoVO planoDuracaoVO : getPlanoPesquisado().getPlanoDuracaoVOs()) {
                    salvarItemCampanhaPlanoDuracao(planoPesquisado.getEmpresa(), planoDuracaoVO);
                }
            }


            setItemCampanhaPlano(new ItemCampanhaVO());

            setDescricaoPlano("");
            montarListaPlano();
            setMostrarPlanosComPonto(!getListaPlanosComPonto().isEmpty());
            setPlanoPesquisado(new PlanoVO());
            montarSucessoGrowl("Configurações gravadas com sucesso");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    private void salvarItemCampanhaPlanoDuracao(EmpresaVO empresaVO,PlanoDuracaoVO planoDuracaoVO) {
        try {
            validarPermissaoConfiguracao();
            Integer pontos = planoDuracaoVO.getPontos();
            setItemCampanhaPlanoDuracao(getFacade().getItemCampanha().consultarPorChaveEstrangeira(empresaVO.getCodigo(), planoDuracaoVO.getCodigo(), TipoItemCampanhaEnum.PLANODURACAO.getCodigo()));

            if (UteisValidacao.emptyNumber(getItemCampanhaPlanoDuracao().getCodigo())) {
                if (planoDuracaoVO.getCodigo() == 0) {
                    throw new Exception("Selecione uma duração");
                }
                getItemCampanhaPlanoDuracao().setEmpresa(empresaVO);
                getItemCampanhaPlanoDuracao().setTipoItemCampanha(TipoItemCampanhaEnum.PLANODURACAO);
                getItemCampanhaPlanoDuracao().setChaveestrangeira(planoDuracaoVO.getCodigo());
                getItemCampanhaPlanoDuracao().setDuracao(planoDuracaoVO.getNumeroMeses());
                getItemCampanhaPlanoDuracao().setPontos(pontos);
                getFacade().getPlano().alterarPontos(planoDuracaoVO.getCodigo(), pontos, TipoItemCampanhaEnum.PLANODURACAO.getCodigo());
                getFacade().getItemCampanha().incluir(getItemCampanhaPlanoDuracao());
            } else {
                getItemCampanhaPlanoDuracao().setPontos(pontos);
                getFacade().getItemCampanha().alterar(getItemCampanhaPlanoDuracao());
                getFacade().getPlano().alterarPontos(planoDuracaoVO.getCodigo(), pontos, TipoItemCampanhaEnum.PLANODURACAO.getCodigo());
            }

            setItemCampanhaPlanoDuracao(new ItemCampanhaVO());
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void salvarItemCampanhaProduto() {
        try {
            validarPermissaoConfiguracao();
            if (UteisValidacao.emptyNumber(getItemCampanhaProduto().getCodigo())) {
                if (getProdutoPesquisa().getCodigo() == 0) {
                    throw new Exception("Selecione um produto");
                }

                getItemCampanhaProduto().setTipoItemCampanha(TipoItemCampanhaEnum.PRODUTO);
                getItemCampanhaProduto().setChaveestrangeira(getProdutoPesquisa().getCodigo());
                getItemCampanhaProduto().setPontos(getProdutoPesquisa().getPontos());
                getFacade().getProduto().alterarPontos(getProdutoPesquisa().getCodigo(), getProdutoPesquisa().getPontos());
                getFacade().getItemCampanha().incluir(getItemCampanhaProduto());
            } else {
                getItemCampanhaProduto().setPontos(getProdutoPesquisa().getPontos());
                getFacade().getProduto().alterarPontos(getProdutoPesquisa().getCodigo(), getProdutoPesquisa().getPontos());
                getFacade().getItemCampanha().alterar(getItemCampanhaProduto());
            }

            setItemCampanhaProduto(new ItemCampanhaVO());
            setProdutoPesquisa(new ProdutoVO());
            montarListaProduto();
            montarSucessoGrowl("Configurações gravadas com sucesso");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void validarPermissaoConfiguracao() throws Exception {
        validarPermissao("ConfigurarClubeDeVantagens","Configurar Clube de Vantagens", getUsuarioLogado());
    }

    public void salvarItemCampanhaAula() {
        try {
            validarPermissaoConfiguracao();
            if (UteisValidacao.emptyNumber(getItemCampanhaAula().getCodigo())) {
                if (UteisValidacao.emptyNumber(getAulaPesquisada().getCodigo() )) {
                    throw new Exception("Selecione uma aula");
                }
                getItemCampanhaAula().setEmpresa(new EmpresaVO(aulaPesquisada.getEmpresa()));
                getItemCampanhaAula().setTipoItemCampanha(TipoItemCampanhaEnum.AULA);
                getItemCampanhaAula().setChaveestrangeira(getAulaPesquisada().getCodigo());
                getItemCampanhaAula().setPontos(getAulaPesquisada().getPontosBonus());
                getFacade().getTurma().alterarPontos(getAulaPesquisada().getCodigo(), getAulaPesquisada().getPontosBonus());
                getFacade().getItemCampanha().incluir(getItemCampanhaAula());
            } else {

                getItemCampanhaAula().setPontos(getAulaPesquisada().getPontosBonus());
                getFacade().getItemCampanha().alterar(getItemCampanhaAula());
                getFacade().getTurma().alterarPontos(getAulaPesquisada().getCodigo(), getAulaPesquisada().getPontosBonus());

            }

            setItemCampanhaAula(new ItemCampanhaVO());
            setAulaPesquisada(new TurmaAulaCheiaJSON());
            setDescricaoTurma("");
            setAulaPesquisada(new TurmaAulaCheiaJSON());
            montarListaAulas();
            montarSucessoGrowl("Configurações gravadas com sucesso");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void salvarItemCampanhaAcesso() {
        if(getItemCampanhaAcesso().getDiasDaSemanaAtivos().isEmpty()) {
            montarErro("Selecione pelo menos um dia da semana");
            return;
        }
        try {
            validarPermissaoConfiguracao();
            if (UteisValidacao.emptyNumber(getItemCampanhaAcesso().getCodigo()) || getEmpresaSelecionada().getCodigo() == 0) {
                if (getEmpresaSelecionada().getCodigo() == 0) {
                    for (SelectItem empresa : getListaEmpresas()) {
                        if ((Integer) empresa.getValue() == 0)
                            continue;

                        getItemCampanhaAcesso().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSO);
                        getItemCampanhaAcesso().setEmpresa(new EmpresaVO((Integer)empresa.getValue()));

                        getFacade().getEmpresa().alteraPontosAcesso((Integer) empresa.getValue(), getItemCampanhaAcesso().getPontos());
                        registrarLogClubeDeVantagens("Acesso Empresa: "+empresa.getValue()+ " Pontos:"+getItemCampanhaAcesso().getPontos(),TipoOperacaoEnum.ALTERACAO);
                        getFacade().getItemCampanha().alterarOuIncluir(getItemCampanhaAcesso());

                        getEmpresaSelecionada().setCodigo((Integer)empresa.getValue());
                        getFacade().getEmpresa().alterarConfiguracoesClubeVantagens(getEmpresaSelecionada());

                        getItemCampanhaAcessoChuva().setCodigo(getFacade().getItemCampanha().consultarPorChaveEstrangeira((Integer) empresa.getValue(),null,TipoItemCampanhaEnum.ACESSOCHUVA.getCodigo()).getCodigo());
                        getItemCampanhaAcessoChuva().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOCHUVA);
                        salvarItemCampanhaAcessoChuva((Integer)empresa.getValue());

                        getItemCampanhaAcessoFrio().setCodigo(getFacade().getItemCampanha().consultarPorChaveEstrangeira((Integer) empresa.getValue(),null,TipoItemCampanhaEnum.ACESSOFRIO.getCodigo()).getCodigo());
                        getItemCampanhaAcessoFrio().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOFRIO);
                        salvarItemCampanhaAcessoFrio((Integer)empresa.getValue());

                        getItemCampanhaAcessoCalor().setCodigo(getFacade().getItemCampanha().consultarPorChaveEstrangeira((Integer) empresa.getValue(),null,TipoItemCampanhaEnum.ACESSOCALOR.getCodigo()).getCodigo());
                        getItemCampanhaAcessoCalor().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOCALOR);
                        salvarItemCampanhaAcessoCalor((Integer)empresa.getValue());
                    }
                }else{
                    getItemCampanhaAcesso().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSO);
                    getItemCampanhaAcesso().setEmpresa(getEmpresaSelecionada());
                    getFacade().getEmpresa().alteraPontosAcesso(getEmpresaSelecionada().getCodigo(), getItemCampanhaAcesso().getPontos());
                    registrarLogClubeDeVantagens("Acesso Empresa: "+getEmpresaSelecionada().getCodigo()+ " Pontos:"+getItemCampanhaAcesso().getPontos(),TipoOperacaoEnum.ALTERACAO);
                    getFacade().getItemCampanha().alterarOuIncluir(getItemCampanhaAcesso());

                    getFacade().getEmpresa().alterarConfiguracoesClubeVantagens(getEmpresaSelecionada());
                    salvarItemCampanhaAcessoChuva(getEmpresaSelecionada().getCodigo());
                    salvarItemCampanhaAcessoFrio(getEmpresaSelecionada().getCodigo());
                    salvarItemCampanhaAcessoCalor(getEmpresaSelecionada().getCodigo());
                }
            } else {
                getFacade().getEmpresa().alteraPontosAcesso(getEmpresaSelecionada().getCodigo(), getItemCampanhaAcesso().getPontos());
                getFacade().getEmpresa().alteraDiasPontuacaoAtiva(getEmpresaSelecionada().getCodigo(), getItemCampanhaAcesso().getDiasDaSemanaAtivos());
                registrarLogClubeDeVantagens("Acesso Empresa: "+getEmpresaSelecionada().getCodigo()+ " Pontos:"+getItemCampanhaAcesso().getPontos(),TipoOperacaoEnum.ALTERACAO);
                getFacade().getItemCampanha().alterar(getItemCampanhaAcesso());

                getFacade().getEmpresa().alterarConfiguracoesClubeVantagens(getEmpresaSelecionada());
                salvarItemCampanhaAcessoChuva(getEmpresaSelecionada().getCodigo());
                salvarItemCampanhaAcessoFrio(getEmpresaSelecionada().getCodigo());
                salvarItemCampanhaAcessoCalor(getEmpresaSelecionada().getCodigo());
            }

            montarSucessoGrowl("Configurações gravadas com sucesso");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void salvarItemCampanhaAcessoChuva(Integer codigoEmpresaVO) {
        try {
            if (UteisValidacao.emptyNumber(getItemCampanhaAcessoChuva().getCodigo() )) {
                getItemCampanhaAcessoChuva().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOCHUVA);
                getItemCampanhaAcessoChuva().setEmpresa(new EmpresaVO(codigoEmpresaVO));
                getFacade().getEmpresa().alteraPontosChuva(codigoEmpresaVO, getItemCampanhaAcessoChuva().getPontos());
                getFacade().getItemCampanha().incluir(getItemCampanhaAcessoChuva());
                registrarLogClubeDeVantagens("Acesso na chuva Empresa: "+codigoEmpresaVO + " Pontos:"+getItemCampanhaAcessoChuva().getPontos(),TipoOperacaoEnum.INCLUSAO);
            } else {
                getItemCampanhaAcessoChuva().setEmpresa(new EmpresaVO(codigoEmpresaVO));
                getFacade().getEmpresa().alteraPontosChuva(codigoEmpresaVO, getItemCampanhaAcessoChuva().getPontos());
                getFacade().getItemCampanha().alterar(getItemCampanhaAcessoChuva());
                registrarLogClubeDeVantagens("Acesso na chuva Empresa: "+codigoEmpresaVO + " Pontos:"+getItemCampanhaAcessoChuva().getPontos(),TipoOperacaoEnum.ALTERACAO);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void registrarLogClubeDeVantagens(String mgs, TipoOperacaoEnum operacao ) throws Exception {
        registrarLogObjetoVO("ClubeDeVantagens",mgs ,getUsuarioLogado().getCodigo(),getUsuarioLogado().getNome(),getUsuarioLogado().getNome(), operacao.name());
    }

    private void salvarItemCampanhaAcessoFrio(Integer codigoEmpresaVO) {
        try {
            if (UteisValidacao.emptyNumber(getItemCampanhaAcessoFrio().getCodigo() )) {
                getItemCampanhaAcessoFrio().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOFRIO);
                getItemCampanhaAcessoFrio().setEmpresa(new EmpresaVO(codigoEmpresaVO));
                getFacade().getEmpresa().alteraPontosFrio(codigoEmpresaVO, getItemCampanhaAcessoFrio().getPontos());
                getFacade().getItemCampanha().incluir(getItemCampanhaAcessoFrio());
                registrarLogClubeDeVantagens("Acesso Frio Empresa: "+codigoEmpresaVO + " Pontos:"+getItemCampanhaAcessoFrio().getPontos(),TipoOperacaoEnum.INCLUSAO);
            } else {
                getItemCampanhaAcessoFrio().setEmpresa(new EmpresaVO(codigoEmpresaVO));
                getFacade().getEmpresa().alteraPontosFrio(codigoEmpresaVO, getItemCampanhaAcessoFrio().getPontos());
                getFacade().getItemCampanha().alterar(getItemCampanhaAcessoFrio());
                registrarLogClubeDeVantagens("Acesso Frio Empresa: "+codigoEmpresaVO + " Pontos:"+getItemCampanhaAcessoFrio().getPontos(),TipoOperacaoEnum.ALTERACAO);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void salvarItemCampanhaAcessoCalor(Integer codigoEmpresaVO) {
        try {
            if (UteisValidacao.emptyNumber(getItemCampanhaAcessoCalor().getCodigo())) {
                getItemCampanhaAcessoCalor().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOCALOR);
                getItemCampanhaAcessoCalor().setEmpresa(new EmpresaVO(codigoEmpresaVO));
                getFacade().getEmpresa().alteraPontosCalor(codigoEmpresaVO, getItemCampanhaAcessoCalor().getPontos());
                getFacade().getItemCampanha().incluir(getItemCampanhaAcessoCalor());
                registrarLogClubeDeVantagens("Acesso Calor Empresa: "+codigoEmpresaVO + " Pontos:"+getItemCampanhaAcessoCalor().getPontos(),TipoOperacaoEnum.INCLUSAO);
            } else {
                getItemCampanhaAcessoCalor().setEmpresa(new EmpresaVO(codigoEmpresaVO));
                getFacade().getEmpresa().alteraPontosCalor(codigoEmpresaVO, getItemCampanhaAcessoCalor().getPontos());
                getFacade().getItemCampanha().alterar(getItemCampanhaAcessoCalor());
                registrarLogClubeDeVantagens("Acesso Calor Empresa: "+codigoEmpresaVO + " Pontos:"+getItemCampanhaAcessoCalor().getPontos(),TipoOperacaoEnum.INCLUSAO);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void salvarItemCampanhaIndicacao() {
        try {
            validarPermissaoConfiguracao();
            if(getEmpresaSelecionada().isAplicarIndicacaoQlqrPlano()){
                codigoPlanos = new ArrayList<Integer>();
                for (SelectItem plano : planos) {
                    codigoPlanos.add((Integer) plano.getValue());
                }
            }else if (codigoPlanos.size() <= 0){
                throw new Exception("Informe ao menos um plano");
            }

            List<ItemCampanhaVO> lstItnesIndicacao = new ArrayList<ItemCampanhaVO>();
            List<ItemCampanhaVO> lstItnesIndicacaoConvertidas = new ArrayList<ItemCampanhaVO>();

            if (getEmpresaSelecionada().getCodigo() == 0) {
                for (SelectItem empresa : getListaEmpresas()) {
                    if ((Integer) empresa.getValue() == 0)
                        continue;
                    lstItnesIndicacao.add(new ItemCampanhaVO(null, TipoItemCampanhaEnum.INDICACAO, getItemCampanhaIndicacao().getPontos(), (Integer) empresa.getValue()));
                    for (Integer codigoPlano : codigoPlanos) {
                        lstItnesIndicacaoConvertidas.add(new ItemCampanhaVO(codigoPlano, TipoItemCampanhaEnum.INDICACAO_CONVERTIDA, getItemCampanhaIndicacaoConvertida().getPontos(), (Integer) empresa.getValue()));
                    }
                    getEmpresaSelecionada().setCodigo((Integer)empresa.getValue());
                    getFacade().getEmpresa().alterarConfiguracoesClubeVantagens(getEmpresaSelecionada());
                    regristrarLogAlteracaoEmpresa();
                }
            } else {
                lstItnesIndicacao.add(new ItemCampanhaVO(null, TipoItemCampanhaEnum.INDICACAO, getItemCampanhaIndicacao().getPontos(), getEmpresaSelecionada().getCodigo()));
                for (Integer codigoPlano : codigoPlanos) {
                    lstItnesIndicacaoConvertidas.add(new ItemCampanhaVO(codigoPlano, TipoItemCampanhaEnum.INDICACAO_CONVERTIDA, getItemCampanhaIndicacaoConvertida().getPontos(), getEmpresaSelecionada().getCodigo()));
                }
                getFacade().getEmpresa().alterarConfiguracoesClubeVantagens(getEmpresaSelecionada());
                regristrarLogAlteracaoEmpresa();
            }
            getFacade().getItemCampanha().alterarItemCampanhaIndicacao(lstItnesIndicacao);
            getFacade().getItemCampanha().alterarItemCampanhaIndicacao(lstItnesIndicacaoConvertidas);

            String lstTotxt = " Lista Indicação ";
            lstItnesIndicacao.stream().forEach(a -> lstTotxt.concat(a.toString()+"|") );
            lstTotxt.concat(" | Lista Indicação Convertida :");
            lstItnesIndicacaoConvertidas.stream().forEach(a -> lstTotxt.concat(a.toString()+"|") );

            registrarLogClubeDeVantagens("Alterado configuração Campanha Indicação Emrpesa : "+lstTotxt ,TipoOperacaoEnum.ALTERACAO);

            montarSucessoGrowl("Configurações gravadas com sucesso");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void selecionarPlanoSuggestionBox(ActionEvent actionEvent) {
        PlanoVO plano = (PlanoVO) actionEvent.getComponent().getAttributes().get("pequisaPlano");
        selecionarPlanoSuggestionBox(plano);
    }

    private void selecionarPlanoSuggestionBox(PlanoVO plano) {
        try {
            planoPesquisado = plano;
            setDescricaoPlano(plano.getDescricao());
            if (getFacade().getItemCampanha().validarItemJaCadastradoChaveEstrangeira(plano.getCodigo(), TipoItemCampanhaEnum.PLANO.getCodigo(),empresaSelecionada.getCodigo())) {
                itemCampanhaPlano = getFacade().getItemCampanha().consultarPorChaveEstrangeira(empresaSelecionada.getCodigo(), plano.getCodigo(), TipoItemCampanhaEnum.PLANO.getCodigo());
                planoPesquisado.setPontos(itemCampanhaPlano.getPontos());
            } else
                itemCampanhaPlano = null;
            setOnComplete("");
            planoPesquisado.setPlanoDuracaoVOs(getFacade().getPlanoDuracao().consultarPlanoDuracaos(planoPesquisado.getCodigo(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            montarErro(e);
            setOnComplete(getMensagemNotificar());
        }
    }

    public void montarItemAcesso() {
        try {
            getItemCampanhaAcesso().setEmpresa(empresaSelecionada);
            getItemCampanhaAcesso().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSO);
            getItemCampanhaAcesso().setCampanha(null);
            setItemCampanhaAcesso( getFacade().getItemCampanha().consultarUnicosTipoXEmpresa(getItemCampanhaAcesso()));
            montarItemAcessoChuva();
            montarItemAcessoFrio();
            montarItemAcessoCalor();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void montarItemAcessoChuva() {
        try {
            getItemCampanhaAcessoChuva().setEmpresa(empresaSelecionada);
            getItemCampanhaAcessoChuva().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOCHUVA);
            getItemCampanhaAcessoChuva().setCampanha(null);
            setItemCampanhaAcessoChuva(getFacade().getItemCampanha().consultarUnicosTipoXEmpresa(getItemCampanhaAcessoChuva()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void montarItemAcessoFrio() {
        try {
            getItemCampanhaAcessoFrio().setEmpresa(empresaSelecionada);
            getItemCampanhaAcessoFrio().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOFRIO);
            getItemCampanhaAcessoFrio().setCampanha(null);
            setItemCampanhaAcessoFrio(getFacade().getItemCampanha().consultarUnicosTipoXEmpresa(getItemCampanhaAcessoFrio()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void montarItemAcessoCalor() {
        try {
            getItemCampanhaAcessoCalor().setEmpresa(empresaSelecionada);
            getItemCampanhaAcessoCalor().setTipoItemCampanha(TipoItemCampanhaEnum.ACESSOCALOR);
            getItemCampanhaAcessoCalor().setCampanha(null);
            setItemCampanhaAcessoCalor(getFacade().getItemCampanha().consultarUnicosTipoXEmpresa(getItemCampanhaAcessoCalor()));
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void verificarAulaComPontuacaoESemPontuacao() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("aulaComPonto");
        boolean aulaComPonto = Boolean.parseBoolean(obj);
        try {
            if (aulaComPonto) {
                setMostrarAulasComPonto(true);
            } else {
                setMostrarAulasComPonto(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void verificarPlanoComPontuacaoESemPontuacao() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("planoComPonto");
        boolean planoComPonto = Boolean.parseBoolean(obj);
        try {
            if (planoComPonto) {
                setMostrarPlanosComPonto(true);
            } else {
                setMostrarPlanosComPonto(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void verificarProdutoComPontuacaoESemPontuacao() {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("produtoComPonto");
        boolean produtoComPonto = Boolean.parseBoolean(obj);
        try {
            if (produtoComPonto) {
                setMostrarProdutosComPonto(true);
            } else {
                setMostrarProdutosComPonto(false);
            }
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void irParaTelaPlano(ActionEvent actionEvent) {
        PlanoVO paPlanoVO = (PlanoVO) actionEvent.getComponent().getAttributes().get("planoComPonto");
        irParaTelaPlano(paPlanoVO);
    }

    private void irParaTelaPlano(PlanoVO planoSelecionado) {
        try {
            PlanoControle planoControle = (PlanoControle) JSFUtilities.getManagedBean("PlanoControle");
            planoControle.processarPlanoSelecionado(planoSelecionado);
            setOnComplete("abrirPopup('./planoForm.jsp', 'Plano', 800, 595);");
        } catch (Exception e) {
            setOnComplete(getMensagemNotificar());
            montarErro(e);
        }
    }

    public ItemCampanhaVO getItemCampanhaPlano() {
        if (itemCampanhaPlano == null)
            itemCampanhaPlano = new ItemCampanhaVO();
        return itemCampanhaPlano;
    }

    public void setItemCampanhaPlano(ItemCampanhaVO itemCampanhaPlano) {
        this.itemCampanhaPlano = itemCampanhaPlano;
    }

    public List<ItemCampanhaVO> getListaItemSemCampanha() {
        return listaItemSemCampanha;
    }

    public void setListaItemSemCampanha(List<ItemCampanhaVO> listaItemSemCampanha) {
        this.listaItemSemCampanha = listaItemSemCampanha;
    }

    public CampanhaDuracaoVO getCampanhaDuracaoSelecionado() {
        return campanhaDuracaoSelecionado;
    }

    public void setCampanhaDuracaoSelecionado(CampanhaDuracaoVO campanhaDuracaoSelecionado) {
        this.campanhaDuracaoSelecionado = campanhaDuracaoSelecionado;
    }

    public List<SelectItem> getListaTipoItem() {
        return listaTipoItem;
    }

    public ProdutoVO getProdutoPesquisa() {
        if (produtoPesquisa == null) {
            produtoPesquisa = new ProdutoVO();
        }
        return produtoPesquisa;
    }

    public void setProdutoPesquisa(ProdutoVO produtoPesquisa) {
        this.produtoPesquisa = produtoPesquisa;
    }

    public String getOnComplete() {
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public PlanoVO getPlanoPesquisado() {
        if (planoPesquisado == null) {
            planoPesquisado = new PlanoVO();
        }
        return planoPesquisado;
    }

    public void setPlanoPesquisado(PlanoVO planoPesquisado) {
        if (planoPesquisado != null)
            descricaoPlano = planoPesquisado.getDescricao();
        this.planoPesquisado = planoPesquisado;
    }

    public boolean isMostrarPlanosComPonto() {
        return mostrarPlanosComPonto;
    }

    public void setMostrarPlanosComPonto(boolean mostrarPlanosComPonto) {
        this.mostrarPlanosComPonto = mostrarPlanosComPonto;
    }

    public List<ItemCampanhaVO> getListaPlanosComPonto() {
        return listaPlanosComPonto;
    }

    public void setListaPlanosComPonto(List<ItemCampanhaVO> listaPlanosComPonto) {
        this.listaPlanosComPonto = listaPlanosComPonto;
    }

    public List<PlanoVO> getListaPlanosSemPontuacao() {
        return listaPlanosSemPontuacao;
    }

    public void setListaPlanosSemPontuacao(List<PlanoVO> listaPlanosSemPontuacao) {
        this.listaPlanosSemPontuacao = listaPlanosSemPontuacao;
    }

    public ItemCampanhaVO getItemCampanhaProduto() {
        return itemCampanhaProduto;
    }

    public void setItemCampanhaProduto(ItemCampanhaVO itemCampanhaProduto) {
        this.itemCampanhaProduto = itemCampanhaProduto;
    }

    public boolean isMostrarProdutosComPonto() {
        return mostrarProdutosComPonto;
    }

    public void setMostrarProdutosComPonto(boolean mostrarProdutosComPonto) {
        this.mostrarProdutosComPonto = mostrarProdutosComPonto;
    }

    public List<ItemCampanhaVO> getListaProdutoComPonto() {
        return listaProdutoComPonto;
    }

    public void setListaProdutoComPonto(List<ItemCampanhaVO> listaProdutoComPonto) {
        this.listaProdutoComPonto = listaProdutoComPonto;
    }

    public List<ProdutoVO> getListaProdutosSemPonto() {
        return listaProdutosSemPonto;
    }

    public void setListaProdutosSemPonto(List<ProdutoVO> listaProdutosSemPonto) {
        this.listaProdutosSemPonto = listaProdutosSemPonto;
    }

    public ItemCampanhaVO getItemCampanhaAcesso() {
        if (itemCampanhaAcesso == null)
            itemCampanhaAcesso = new ItemCampanhaVO();
        return itemCampanhaAcesso;
    }

    public void setItemCampanhaAcesso(ItemCampanhaVO itemCampanhaAcesso) {
        this.itemCampanhaAcesso = itemCampanhaAcesso;
    }

    public ItemCampanhaVO getItemCampanhaAula() {
        return itemCampanhaAula;
    }

    public void setItemCampanhaAula(ItemCampanhaVO itemCampanhaAula) {
        this.itemCampanhaAula = itemCampanhaAula;
    }

    public TurmaAulaCheiaJSON getAulaPesquisada() {
        return aulaPesquisada;
    }

    public void setAulaPesquisada(TurmaAulaCheiaJSON aulaPesquisada) {
        this.aulaPesquisada = aulaPesquisada;
    }

    public boolean isMostrarAulasComPonto() {
        return mostrarAulasComPonto;
    }

    public void setMostrarAulasComPonto(boolean mostrarAulasComPonto) {
        this.mostrarAulasComPonto = mostrarAulasComPonto;
    }

    public List<ItemCampanhaVO> getListaAulasComPonto() {
        return listaAulasComPonto;
    }

    public void setListaAulasComPonto(List<ItemCampanhaVO> listaAulasComPonto) {
        this.listaAulasComPonto = listaAulasComPonto;
    }

    public List<TurmaAulaCheiaJSON> getListaTurmaSemPontuacao() {
        return listaTurmaSemPontuacao;
    }

    public void setListaTurmaSemPontuacao(List<TurmaAulaCheiaJSON> listaTurmaSemPontuacao) {
        this.listaTurmaSemPontuacao = listaTurmaSemPontuacao;
    }

    public ItemCampanhaVO getItemCampanhaPlanoDuracao() {
        return itemCampanhaPlanoDuracao;
    }

    public void setItemCampanhaPlanoDuracao(ItemCampanhaVO itemCampanhaPlanoDuracao) {
        this.itemCampanhaPlanoDuracao = itemCampanhaPlanoDuracao;
    }

    public ItemCampanhaVO getItemCampanhaAcessoChuva() {
        if (itemCampanhaAcessoChuva == null)
            itemCampanhaAcessoChuva = new ItemCampanhaVO();
        return itemCampanhaAcessoChuva;
    }

    public void setItemCampanhaAcessoChuva(ItemCampanhaVO itemCampanhaAcessoChuva) {
        this.itemCampanhaAcessoChuva = itemCampanhaAcessoChuva;
    }

    public ItemCampanhaVO getItemCampanhaAcessoFrio() {
        if (itemCampanhaAcessoFrio == null)
            itemCampanhaAcessoFrio = new ItemCampanhaVO();
        return itemCampanhaAcessoFrio;
    }

    public void setItemCampanhaAcessoFrio(ItemCampanhaVO itemCampanhaAcessoFrio) {
        this.itemCampanhaAcessoFrio = itemCampanhaAcessoFrio;
    }

    public ItemCampanhaVO getItemCampanhaAcessoCalor() {
        if (itemCampanhaAcessoCalor == null)
            itemCampanhaAcessoCalor = new ItemCampanhaVO();
        return itemCampanhaAcessoCalor;
    }

    public void setItemCampanhaAcessoCalor(ItemCampanhaVO itemCampanhaAcessoCalor) {
        this.itemCampanhaAcessoCalor = itemCampanhaAcessoCalor;
    }

    public ItemCampanhaVO getItemCampanhaIndicacao() {
        if (itemCampanhaIndicacao == null || itemCampanhaIndicacao.getTipoItemCampanha()==null) {
            itemCampanhaIndicacao = new ItemCampanhaVO();
            itemCampanhaIndicacao.setTipoItemCampanha(TipoItemCampanhaEnum.INDICACAO);
            itemCampanhaIndicacao.setEmpresa(getEmpresaSelecionada());
        }
        return itemCampanhaIndicacao;
    }

    public void setItemCampanhaIndicacao(ItemCampanhaVO itemCampanhaIndicacao) {
        this.itemCampanhaIndicacao = itemCampanhaIndicacao;
    }

    public String getDescricaoTurma() {
        return descricaoTurma;
    }

    public void setDescricaoTurma(String descricaoTurma) {
        this.descricaoTurma = descricaoTurma;
    }

    public String getDescricaoPlano() {
        return descricaoPlano;
    }

    public void setDescricaoPlano(String descricaoPlano) {
        this.descricaoPlano = descricaoPlano;
    }

    public ListaPaginadaTO getPaginadorListaPlanosCP() {
        if (paginadorListaPlanosCP == null) {
            paginadorListaPlanosCP = new ListaPaginadaTO(10);
            paginadorListaPlanosCP.setOrderByDesc(true);
        }
        return paginadorListaPlanosCP;
    }

    public void setPaginadorListaPlanosCP(ListaPaginadaTO paginadorListaPlanosCP) {
        this.paginadorListaPlanosCP = paginadorListaPlanosCP;
    }

    public ListaPaginadaTO getPaginadorListaPlanosSP() {
        if (paginadorListaPlanosSP == null) {
            paginadorListaPlanosSP = new ListaPaginadaTO(10);
            paginadorListaPlanosSP.setOrderByDesc(true);
        }
        return paginadorListaPlanosSP;
    }

    public void setPaginadorListaPlanosSP(ListaPaginadaTO paginadorListaPlanosSP) {
        this.paginadorListaPlanosSP = paginadorListaPlanosSP;
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.proximaPagina();
        carregarListaPaginacao(codigo);
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.paginaAnterior();
        carregarListaPaginacao(codigo);
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.ultimaPagina();
        carregarListaPaginacao(codigo);
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.primeiraPagina();
        carregarListaPaginacao(codigo);
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        String codigo = (String) evt.getComponent().getAttributes().get("tipo");
        ListaPaginadaTO paginacao = obterPaginacaoPorCodigo(codigo);
        paginacao.setOffset(0);
        carregarListaPaginacao(codigo);
    }


    private ListaPaginadaTO obterPaginacaoPorCodigo(String codigo) {
        if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PLANO.getDescricao()))
            return getPaginadorListaPlanosCP();
        else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PLANO.getDescricao() + "SP"))
            return getPaginadorListaPlanosSP();
        else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.AULA.getDescricao()))
            return getPaginadorListaAulaCP();
        else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.AULA.getDescricao() + "SP"))
            return getPaginadorListaAulaSP();
        else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PRODUTO.getDescricao()))
            return getPaginadorListaProdutosCP();
        else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PRODUTO.getDescricao() + "SP"))
            return getPaginadorListaProdutosSP();
        else
            return null;
    }

    public ListaPaginadaTO getPaginadorListaAulaCP() {
        if (paginadorListaAulaCP == null) {
            paginadorListaAulaCP = new ListaPaginadaTO(10);
            paginadorListaAulaCP.setOrderByDesc(true);
        }
        return paginadorListaAulaCP;
    }

    public void setPaginadorListaAulaCP(ListaPaginadaTO paginadorListaAulaCP) {
        this.paginadorListaAulaCP = paginadorListaAulaCP;
    }

    public ListaPaginadaTO getPaginadorListaAulaSP() {
        if (paginadorListaAulaSP == null) {
            paginadorListaAulaSP = new ListaPaginadaTO(10);
            paginadorListaAulaSP.setOrderByDesc(true);
        }
        return paginadorListaAulaSP;
    }

    public void setPaginadorListaAulaSP(ListaPaginadaTO paginadorListaAulaSP) {
        this.paginadorListaAulaSP = paginadorListaAulaSP;
    }

    public EmpresaVO getEmpresaSelecionada() {
        if (empresaSelecionada == null)
            empresaSelecionada = new EmpresaVO();
        return empresaSelecionada;
    }

    public void setEmpresaSelecionada(EmpresaVO empresaSelecionada) {
        this.empresaSelecionada = empresaSelecionada;
    }

    public ListaPaginadaTO getPaginadorListaProdutosCP() {
        if (paginadorListaProdutosCP == null) {
            paginadorListaProdutosCP = new ListaPaginadaTO(10);
            paginadorListaProdutosCP.setOrderByDesc(true);
        }
        return paginadorListaProdutosCP;
    }

    public void setPaginadorListaProdutosCP(ListaPaginadaTO paginadorListaProdutosCP) {
        this.paginadorListaProdutosCP = paginadorListaProdutosCP;
    }

    public ListaPaginadaTO getPaginadorListaProdutosSP() {
        if (paginadorListaProdutosSP == null) {
            paginadorListaProdutosSP = new ListaPaginadaTO(10);
            paginadorListaProdutosSP.setOrderByDesc(true);
        }
        return paginadorListaProdutosSP;
    }

    public void setPaginadorListaProdutosSP(ListaPaginadaTO paginadorListaProdutosSP) {
        this.paginadorListaProdutosSP = paginadorListaProdutosSP;
    }

    private void carregarListaPaginacao(String codigo) throws Exception {
        if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PLANO.getDescricao())) {
            consultarListaPlanoCP();
        } else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PLANO.getDescricao() + "SP")) {
            consultarListaPlanoSP();
        } else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.AULA.getDescricao())) {
            consultarListaAulaCP();
        } else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.AULA.getDescricao() + "SP")) {
            consultarListaAulaSP();
        } else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PRODUTO.getDescricao())) {
            consultarListaProdutoCP();
        } else if (codigo.equalsIgnoreCase(TipoItemCampanhaEnum.PRODUTO.getDescricao() + "SP")) {
            consultarListaProdutoSP();
        }
    }

    private void consultarListaProdutoCP() throws Exception {
        setListaProdutoComPonto(getFacade().getItemCampanha().listaItemCampanhaVigentePontuadoPorTipoPaginada(null, TipoItemCampanhaEnum.PRODUTO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, getPaginadorListaProdutosCP()));
    }

    private void consultarListaProdutoSP() throws Exception {
        setListaProdutosSemPonto(getFacade().getProduto().consultarTodosProdutosSemPontuacaoPaginado(false, getEmpresaSelecionada().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, getPaginadorListaProdutosSP()));
    }

    private void consultarListaPlanoCP() throws Exception {
        setListaPlanosComPonto(getFacade().getItemCampanha().listaItemCampanhaVigentePontuadoPlano(getEmpresaSelecionada().getCodigo(), TipoItemCampanhaEnum.PLANO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, getPaginadorListaPlanosCP()));
    }

    private void consultarListaPlanoSP() throws Exception {
        setListaPlanosSemPontuacao(getFacade().getPlano().consultarPlanoVigenteSemCampanhaPaginado(getEmpresaSelecionada().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, getPaginadorListaPlanosSP()));
    }

    private void consultarListaAulaCP() throws Exception {
        setListaAulasComPonto(getFacade().getItemCampanha().listaItemCampanhaVigentePontuadoPorTipoPaginada(getEmpresaSelecionada().getCodigo(), TipoItemCampanhaEnum.AULA.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS, getPaginadorListaAulaCP()));
    }

    private void consultarListaAulaSP() throws Exception {
        setListaTurmaSemPontuacao(getFacade().getTurma().obterAulasColetivasSemPontuacaoaAtivaPaginada(getEmpresaSelecionada().getCodigo(), getPaginadorListaAulaSP()));
    }

    public List<SelectItem> getTemporalRemocaoPontoEnumToSelectedItens() {
        return TemporalRemocaoPontoEnum.toSelectedItens();
    }

    public Integer getZerarPontosAposVencimentoNaEmpresa() {
        if(empresaSelecionada.getZerarPontosAposVencimento()==null)
            empresaSelecionada.setZerarPontosAposVencimento(TemporalRemocaoPontoEnum.NUNCA);
        return empresaSelecionada.getZerarPontosAposVencimento().getCodigo();
    }

    public void setZerarPontosAposVencimentoNaEmpresa(Integer zerarPontosAposVencimentoNaEmpresa) {
        empresaSelecionada.setZerarPontosAposVencimento(TemporalRemocaoPontoEnum.of(zerarPontosAposVencimentoNaEmpresa));
    }

    public List<Integer> getCodigoPlanos() {
        if(codigoPlanos==null)
                codigoPlanos = new ArrayList<>();
        return codigoPlanos;
    }

    public void setCodigoPlanos(List<Integer> codigoPlanos) {
        this.codigoPlanos = codigoPlanos;
    }

    public List<SelectItem> getPlanos() {
        if(planos==null)
            planos= new ArrayList<>();
        return planos;
    }

    public void setPlanos(List<SelectItem> planos) {
        this.planos = planos;
    }

    public ItemCampanhaVO getItemCampanhaIndicacaoConvertida() {
        if (itemCampanhaIndicacaoConvertida == null || itemCampanhaIndicacaoConvertida.getTipoItemCampanha()==null) {
            itemCampanhaIndicacaoConvertida = new ItemCampanhaVO();
            itemCampanhaIndicacaoConvertida.setTipoItemCampanha(TipoItemCampanhaEnum.INDICACAO_CONVERTIDA);
            itemCampanhaIndicacaoConvertida.setEmpresa(empresaSelecionada);
        }
        return itemCampanhaIndicacaoConvertida;
    }

    public void setItemCampanhaIndicacaoConvertida(ItemCampanhaVO itemCampanhaIndicacaoConvertida) {
        this.itemCampanhaIndicacaoConvertida = itemCampanhaIndicacaoConvertida;
    }

    public void atalhoDiasSelecionados(String atalho) {
        String uteis = "SEG,TER,QUA,QUI,SEX";
        String fds = "SAB,DOM";
        String todos = "SEG,TER,QUA,QUI,SEX,SAB,DOM";
        if(Objects.equals(atalho, "uteis")) {
            getItemCampanhaAcesso().setDiasDaSemanaAtivos(Arrays.asList(uteis.split(",")));
        } else if (Objects.equals(atalho, "fds")) {
            getItemCampanhaAcesso().setDiasDaSemanaAtivos(Arrays.asList(fds.split(",")));
        } else if (Objects.equals(atalho, "todos")) {
            getItemCampanhaAcesso().setDiasDaSemanaAtivos(Arrays.asList(todos.split(",")));
        }
    }

}

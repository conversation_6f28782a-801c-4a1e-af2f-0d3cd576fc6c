package controle.clubevantagens;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ClubeVantagensControle extends SuperControleRelatorio {

    private final int QTD_MAX_DESCRICAO = 40;
    private final int QTD_MAX_NOME = 30;

    private boolean mostrarCampanha = true;
    private boolean mostrarMultiplicadores = false;
    private List<CampanhaDuracaoVO> listaCampanhas = new ArrayList<CampanhaDuracaoVO>();
    private CampanhaDuracaoVO campanha;
    private String oncomplete;

    private List<CampanhaDuracaoVO> listaTotalizadorCampanhas;
    private List<ItemCampanhaVO> listaTotalizadorCategoriaPontos;
    private Integer totalPontos;
    private List<BrindeVO> listaTotalizadorBrindes;
    private ListaPaginadaTO paginadorListaBrindesBi;
    private Integer totalBrindes;
    private List<ClienteVO> listaTotalizadorAlunoSemResgate;
    private Date dataInicial;
    private Date dataFinal;

    private List<ClienteVO> listaTotalizadorPontosCategoria;
    private List<HistoricoPontosVO> listaTotalizadorHistoricoPontos;

    private String nomeArquivoComprovanteOperacao;
    private String atributosRel;
    private String tituloRel;
    private String prefixoRel;
    private EmpresaVO empresaVO;

    public ClubeVantagensControle() {
        try {
            setEmpresa(getEmpresaLogado());
            montarListaEmpresasComItemTodasClubeVantagens();
            setCampanha(new CampanhaDuracaoVO());
            getCampanha().setEmpresa(getEmpresa());
            montarListaCampanha();
            inicializarPaginacao();
            montarNovaCampanha();
            dataInicial = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            dataFinal = Uteis.obterUltimoDiaMes(Calendario.hoje());
        } catch (Exception e) {
            montarErro(e);
        }
    }

    @Override
    public void setEmpresa(EmpresaVO empresa) {
        try {
            this.empresaVO = (EmpresaVO) empresa.getClone(false);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
    }

    @Override
    public EmpresaVO getEmpresa(){
        return this.empresaVO;
    }

    public void montarNovaCampanha() {
        try {
            setEmpresa(getEmpresaLogado());
            setCampanha(new CampanhaDuracaoVO());
            getCampanha().setEmpresa(getEmpresa());
            getCampanha().setListaItem(new ArrayList<ItemCampanhaVO>());
            adicionarItensCampanhaVazia();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void adicionarItensCampanhaVazia() {
        getCampanha().getListaItem().clear();
        for (TipoItemCampanhaEnum tipoCampanha : TipoItemCampanhaEnum.getValuesEnun()) {
            ItemCampanhaVO itemCampanhaVO = new ItemCampanhaVO();
            itemCampanhaVO.setTipoItemCampanha(tipoCampanha);
            getCampanha().getListaItem().add(itemCampanhaVO);
        }
    }

    public void montarListaCampanha() throws Exception{
        listaCampanhas = getFacade().getCampanhaDuracao().consultarTodos();
        for (CampanhaDuracaoVO campanhaDuracaoVO : listaCampanhas) {
            if (campanhaDuracaoVO.getEmpresa() != null) {
                if (campanhaDuracaoVO.getEmpresa().getNome().length() > 23) {
                    campanhaDuracaoVO.getEmpresa().setNomeApresentar(campanhaDuracaoVO.getEmpresa().getNome().substring(0, 23).concat("..."));
                } else {
                    campanhaDuracaoVO.getEmpresa().setNomeApresentar(campanhaDuracaoVO.getEmpresa().getNome());
                }
            }
        }
    }
    
    public String abrirClubeVantagens(){
        try {
            montarListaCampanha();
            return "clubeVantagens";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public void inicializarPaginacao() throws Exception {
        setConfPaginacao(new ConfPaginacao());
        getConfPaginacao().setItensPorPagina(12);
        getConfPaginacao().setPaginaAtual(1);
        getConfPaginacao().setNumeroTotalItens(listaCampanhas.size());
        getConfPaginacao().definirVisibilidadeLinksNavegacao();
    }

    public void gravar(){
        try {
            validarDadosGravacao();

            if (UteisValidacao.emptyNumber(campanha.getCodigo())) {
                CampanhaDuracaoVO.validarDados(campanha);
                getFacade().getCampanhaDuracao().incluir(campanha);
                incluirLogInclusao();
                notificarRecursoEmpresa(RecursoSistema.CLUBE_VANTAGENS_CAMPANHA);
            }else{
                CampanhaDuracaoVO.validarDados(campanha);
                getFacade().getCampanhaDuracao().alterar(campanha);
                incluirLogAlteracao("ALTERAÇÃO");
            }
            montarSucessoDadosGravados();
            montarListaCampanha();
            limparDadosCampanha();
        } catch (Exception e) {
            montarErro(e);
        }
    }

    private void validarDadosGravacao() throws Exception {
        Integer qntSelecionados =0 ;
        for (ItemCampanhaVO itemCampanhaVO : campanha.getListaItem()) {
            if( itemCampanhaVO.getPontos()>=1)
                qntSelecionados++;
        }
        if(qntSelecionados<=0)
            throw new Exception("Nenhuma Categoria tem Multiplicador");

        if (campanha.getDataInicial() == null || campanha.getDataFinal() == null) {
            throw new Exception("Informar data inicial e final.");
        }

        if (campanha.getDescricao().trim().equals("")) {
            throw new Exception("Informar uma descriçao para campanha.");
        }
        if(!Calendario.maior(campanha.getDataFinal(), campanha.getDataInicial()))
            throw new Exception("Data final não pode ser maior que a inicial!");

    }

    public void validarDadosExclusao(){
        try {
            if (campanha.getCodigo() == 0) {
               throw new Exception("Campanhas não salva! Não pode ser excluida.");
            }
            setOncomplete("Richfaces.showModalPanel('mdlAvisoExcluirCampanha')");
        } catch (Exception e) {
            setOncomplete(getMensagemNotificar());
            montarErro(e);
        }
    }

    public void excluir(){
        try {
            excluirCampanhaBd();
            montarListaCampanha();
        } catch (Exception e) {
            if(e.getMessage().contains("permissão"))
                montarErro(e.getMessage());
            else
                montarErro("Não é possível excluir uma campanha que gerou pontos. Altere a vigênia para inativá-la.");
            Uteis.logar(e, ClubeVantagensControle.class);
        } finally {
            setOncomplete("Richfaces.hideModalPanel('mdlAvisoExcluirCampanha'); fireElementFromAnyParent('form:btnAtualizaCampanhaDuracao');");
            limparDadosCampanha();
        }
    }

    private void excluirCampanhaBd() throws Exception {
        setMensagemDetalhada("");
        getFacade().getCampanhaDuracao().excluir(campanha);
        incluirLogExclusao();
        montarSucessoGrowl("Campanha excluida com Sucesso!");
    }

    public String ativarClubeVantagens(){
        notificarRecursoEmpresa(RecursoSistema.CLUBE_VANTAGENS_ATIVAR);
        return "ativarClubeVantagens";
    }

    public String carregarBI(){
        try {
            if(!getEmpresaLogado().isTrabalharComPontuacao()){
                return "ativarClubeVantagens";
            }
            dataFinal = Calendario.ultimaHoraDia(dataFinal);
            notificarRecursoEmpresa(RecursoSistema.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE);
            listaTotalizadorCategoriaPontos = getFacade().getItemCampanha().consultaTotalizadorBiPontoOrderDesc( dataInicial,  dataFinal, getEmpresa());
            consultarTotalizadorBrindes();
            listaTotalizadorCampanhas = getFacade().getCampanhaDuracao().consultarTotalizadorCampanhasAtivas(dataInicial, dataFinal,getEmpresa());
            listaTotalizadorAlunoSemResgate = getFacade().getCliente().consultarTotalizadorBiRegataramBrindes(dataInicial, dataFinal, getEmpresa());
            return "biClubeVantagens";
        }catch (Exception e){
            e.printStackTrace();
            montarErro("Falha ao carregar os dados do BI.");
        }
        return "";
    }

    private void consultarTotalizadorBrindes() throws Exception {
        listaTotalizadorBrindes = getFacade().getBrinde().consultarTotalizadorBiBrindes(dataInicial,dataFinal,getEmpresa(),getPaginadorListaBrindesBi());
    }

    public void abrirTotalizadorCategorias(ActionEvent actionEvent){
        try {
            ItemCampanhaVO item = (ItemCampanhaVO) actionEvent.getComponent().getAttributes().get("objCategoriaModal");
            listaTotalizadorHistoricoPontos = getFacade().getHistoricoPontos().consultarPontosXCategoria(item, dataInicial, dataFinal);
            tituloRel = "Total de Pontos - Categoria "+item.getTipoItemCampanha().getDescricao();

            if(item.getTipoItemCampanha()==TipoItemCampanhaEnum.PLANO||
                    item.getTipoItemCampanha()==TipoItemCampanhaEnum.PRODUTO){
                atributosRel = "cliente.matricula=Codigo,cliente.pessoa.nome=Descrição,pontos=Pontos";
                setOncomplete( "Richfaces.showModalPanel('modalDescricaoXpontuacao')");
            }else if(item.getTipoItemCampanha()==TipoItemCampanhaEnum.AULA) {
                atributosRel = "cliente.pessoa.nome=Descrição,pontos=Pontos";
                setOncomplete( "Richfaces.showModalPanel('modalDescricaoXpontuacao')");
            }else{
                atributosRel = "cliente.matricula=Matricula,cliente.pessoa.nome=Nome,pontos=Pontos";
                setOncomplete( "Richfaces.showModalPanel('modalclienteXpontos')");
            }

            actionEvent.getComponent().getAttributes().remove("objCategoriaModal");
        } catch (Exception e) {
            setOncomplete("");
            e.printStackTrace();
            montarErro("Falha ao carregar os dados de Categorias.");
        }
    }

    public void abrirTotalizadorBrindes(ActionEvent actionEvent){
        try {
            BrindeVO item = (BrindeVO) actionEvent.getComponent().getAttributes().get("objBrindeModal");
            listaTotalizadorHistoricoPontos = getFacade().getHistoricoPontos().consultarPontosXBrinde(item, dataInicial, dataFinal);
            atributosRel = "cliente.matricula=Matricula,cliente.pessoa.nome=Nome,dataConfirmacao=Data Retirada,pontos=Pontuação";
            tituloRel = "Total de Pontos - Brinde : "+item.getNome() ;
            setOncomplete( "Richfaces.showModalPanel('modalclienteXpontos')");
            actionEvent.getComponent().getAttributes().remove("objCampanhaModal");
        }catch (Exception e){
            setOncomplete("");
            e.printStackTrace();
            montarErro("Falha ao carregar os dados de Brindes.");
        }
    }

    public void abrirTotalizadorCampanhas(ActionEvent actionEvent){
        try {
            CampanhaDuracaoVO item = (CampanhaDuracaoVO) actionEvent.getComponent().getAttributes().get("objCampanhaModal");
            listaTotalizadorHistoricoPontos = getFacade().getHistoricoPontos().consultarPontosXCampanha(item, dataInicial, dataFinal);
            atributosRel = "cliente.matricula=Matricula,cliente.pessoa.nome=Nome,pontos=Pontos,tipoPonto.descricao=Categoria";
            tituloRel = "Total de Pontos - "+item.getNome() ;
            setOncomplete( "Richfaces.showModalPanel('modalclienteXpontosXTipo')");
            actionEvent.getComponent().getAttributes().remove("objCampanhaModal");
        }catch (Exception e){
            setOncomplete("");
            e.printStackTrace();
            montarErro("Falha ao carregar os dados de Brindes.");
        }
    }

    public void abrirTotalizadorAlunos(ActionEvent actionEvent){
        try {
            atributosRel = "matricula=Matricula,nomeSocial=Nome,pontuacao=Pontos,situacao=Situaçao";
            tituloRel = "Alunos que não resgataram Brindes ";
            setOncomplete( "Richfaces.showModalPanel('modalclienteCV')");
        }catch (Exception e){
            setOncomplete("");
            e.printStackTrace();
            montarErro("Falha ao carregar os dados de Brindes.");
        }
    }


    public void abrirCampanha(ActionEvent event){
        try {
            limparDadosCampanha();
            campanha = (CampanhaDuracaoVO)event.getComponent().getAttributes().get("objCampanhaGrid");
            campanha = getFacade().getCampanhaDuracao().consultarPorChavePrimaria(campanha.getCodigo(), campanha.getEmpresa());
            if(campanha.getListaItem().isEmpty())
                adicionarItensCampanhaVazia();
            campanha.setNovoObj(campanha.getCodigo()==0);
            campanha.registrarObjetoVOAntesDaAlteracao();
            setOncomplete("Richfaces.showModalPanel('mdlEditarCampanha');");
            event.getComponent().getAttributes().remove("objCampanhaGrid");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public String getStringPercentual(){
        Double valor = (Double)context().getExternalContext().getRequestMap().get("stringPercentualBiClube");
        return Formatador.formatarValorPercentual(valor);
    }

    public void validarDadosExclussao(ActionEvent event){
        limparDadosCampanha();
        campanha = (CampanhaDuracaoVO)event.getComponent().getAttributes().get("objCampanhaGrid");
        setOncomplete("Richfaces.showModalPanel('mdlAvisoExcluirCampanha')");
    }
    
    public void selecionarCampanha(){
        limparDadosCampanha();
        campanha = (CampanhaDuracaoVO) context().getExternalContext().getRequestMap().get("campanha");
        ItemCampanhaControle itemCampanhaControle = (ItemCampanhaControle) JSFUtilities.getManagedBean("ItemCampanhaControle");
        itemCampanhaControle.setCampanhaDuracaoSelecionado(campanha);
        itemCampanhaControle.processarListaCampanha();
    }
    

    
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = campanha.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), campanha.getCodigo(),0);
    }

    
    public void limparDadosCampanha(){
        montarNovaCampanha();
    }
    
    public void incluirLogInclusao() throws Exception{
        try {
            campanha.setObjetoVOAntesAlteracao(new CampanhaDuracaoVO());
            campanha.setNovoObj(true);
            incluirLogAlteracao("INCLUSÃO");
        } catch (Exception e) {
            registrarLogErroObjetoVO("CAMPANHADURACAO", campanha.getCodigo(),"ERRO AO GERAR LOG DE INCLUSÃO DA CAMPANHADURACAO",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }
    
    public void incluirLogExclusao() throws Exception{
        try {
            campanha.setObjetoVOAntesAlteracao(new CampanhaDuracaoVO());
            campanha.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(campanha, campanha.getCodigo(),"CAMPANHADURACAO",0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CAMPANHADURACAO", campanha.getCodigo(),"ERRO AO GERAR LOG DE EXCLUSÃO DA CAMPANHADURACAO",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

public void incluirLogAlteracao(String tipoOperacao) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("CAMPANHADURACAO");
            log.setDescricao(tipoOperacao+" CAMPANHA DURAÇÃO " + campanha.getCodigo() + " ----------- ");
            log.setChavePrimaria(campanha.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(getUsuarioLogado());
            log.setResponsavelAlteracao(getUsuarioLogado().getNome());
            log.setOperacao(tipoOperacao);
            log.setUserOAMD(getUsuarioLogado().getUserOamd());
            CampanhaDuracaoVO campanhaAnterior = (CampanhaDuracaoVO) campanha.getObjetoVOAntesAlteracao();

            if(!campanha.getNome().equalsIgnoreCase(campanhaAnterior.getNome())){
                log.setValorCampoAnterior(campanhaAnterior.getNome());
                log.setValorCampoAlterado(campanha.getNome());
                log.setCodigo(0);
                log.setNomeCampo("nome");
                getFacade().getLog().incluir(log);
            }

            if(campanhaAnterior.getDataInicial()==null || campanha.getDataInicial().getTime()!=campanhaAnterior.getDataInicial().getTime()){
                log.setValorCampoAnterior(campanhaAnterior.getDataInicial()==null?null:campanhaAnterior.getDataInicial().toString());
                log.setValorCampoAlterado(campanha.getDataInicial().toString());
                log.setCodigo(0);
                log.setNomeCampo("data Incial");
                getFacade().getLog().incluir(log);
            }
            if(campanhaAnterior.getDataFinal()==null || campanha.getDataFinal().getTime()!=campanhaAnterior.getDataFinal().getTime()){
                log.setValorCampoAnterior(campanhaAnterior.getDataFinal()==null?null:campanhaAnterior.getDataFinal().toString());
                log.setValorCampoAlterado(campanha.getDataFinal().toString());
                log.setCodigo(0);
                log.setNomeCampo("data Final");
                getFacade().getLog().incluir(log);
            }
            if(!campanha.getDescricao().equalsIgnoreCase(campanhaAnterior.getDescricao())){
                log.setValorCampoAnterior(campanhaAnterior.getDescricao());
                log.setValorCampoAlterado(campanha.getDescricao());
                log.setCodigo(0);
                log.setNomeCampo("descrição");
                getFacade().getLog().incluir(log);
            }

            for (ItemCampanhaVO itenCampanhaAlterada : campanha.getListaItem()) {
                if(campanhaAnterior.getListaItem()==null ||campanhaAnterior.getListaItem().size()==0){
                    if(itenCampanhaAlterada.getPontos()<=0)continue;
                    log.setNomeCampo("Multiplicador Categoria "+itenCampanhaAlterada.getTipoItemCampanha().getDescricao());
                    log.setValorCampoAnterior("");
                    log.setValorCampoAlterado(itenCampanhaAlterada.getPontos().toString());
                    log.setCodigo(0);
                    getFacade().getLog().incluir(log);
                    continue;
                }
                for (ItemCampanhaVO itenCampnhaAnterior : campanhaAnterior.getListaItem()) {
                    if(itenCampanhaAlterada.getCodigo()==itenCampnhaAnterior.getCodigo()
                            && itenCampanhaAlterada.getPontos()!= itenCampnhaAnterior.getPontos()){
                        log.setNomeCampo("Multiplicador Categoria "+itenCampanhaAlterada.getTipoItemCampanha().getDescricao());
                        log.setValorCampoAnterior(itenCampnhaAnterior.getPontos().toString());
                        log.setValorCampoAlterado(itenCampanhaAlterada.getPontos().toString());
                        log.setCodigo(0);
                        getFacade().getLog().incluir(log);
                        break;
                    }
                }
            }

        } catch (Exception e) {
            registrarLogErroObjetoVO("CAMPANHADURACAO", campanha.getCodigo(),"ERRO AO GERAR LOG DE ALTERAÇÃO DA CAMPANHADURACAO",this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        campanha.setNovoObj(campanha.getCodigo()==0);
        campanha.registrarObjetoVOAntesDaAlteracao();
    }

    public void imprimirComprovanteOperacao() {
        HistoricoPontosVO obj = (HistoricoPontosVO) context().getExternalContext().getRequestMap().get("historico");
        try {
            if (obj.getCodigo() != 0) {
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteResgateBrinde(obj, getEmpresaLogado()));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante de resgate de brinde.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
    
    public boolean isMostrarCampanha() {
        return mostrarCampanha;
    }

    public void setMostrarCampanha(boolean mostrarCampanha) {
        this.mostrarCampanha = mostrarCampanha;
    }

    public boolean isMostrarMultiplicadores() {
        return mostrarMultiplicadores;
    }

    public void setMostrarMultiplicadores(boolean mostrarMultiplicadores) {
        this.mostrarMultiplicadores = mostrarMultiplicadores;
    }

    public List<CampanhaDuracaoVO> getListaCampanhas() {
        return listaCampanhas;
    }

    public void setListaCampanhas(List<CampanhaDuracaoVO> listaCampanhas) {
        this.listaCampanhas = listaCampanhas;
    }

    public CampanhaDuracaoVO getCampanha() {
        return campanha;
    }

    public void setCampanha(CampanhaDuracaoVO campanha) {
        this.campanha = campanha;
    }

    public String getOncomplete() {
        return oncomplete;
    }

    public void setOncomplete(String oncomplete) {
        this.oncomplete = oncomplete;
    }

    public  String getLinkiWikiAcesso(){
        return getUrlBaseConhecimento()+"como-configurar-pontos-para-os-alunos-que-vierem-a-academia-em-dias-de-chuva-ou-frio-no-clube-de-vantagens/";
    }

    public String getLinkWikiBi() {
        return getUrlWikiRaiz()+ "/" + FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_BUSINESS_INTELLIGENCE.getWiki();
    }
    public String getLinkWiki() {
        return getUrlBaseConhecimento() + FuncionalidadeSistemaEnum.CLUBE_VANTAGENS_CAMPANHA.getBaseConhecimento();
    }

    public List<CampanhaDuracaoVO> getListaTotalizadorCampanhas() {
        return listaTotalizadorCampanhas;
    }

    public void setListaTotalizadorCampanhas(List<CampanhaDuracaoVO> listaTotalizadorCampanhas) {
        this.listaTotalizadorCampanhas = listaTotalizadorCampanhas;
    }

    public List<ItemCampanhaVO> getListaTotalizadorCategoriaPontos() {
        return listaTotalizadorCategoriaPontos;
    }

    public void setListaTotalizadorCategoriaPontos(List<ItemCampanhaVO> listaTotalizadorCategoriaPontos) {
        this.listaTotalizadorCategoriaPontos = listaTotalizadorCategoriaPontos;
    }

    public List<BrindeVO> getListaTotalizadorBrindes() {
        return listaTotalizadorBrindes;
    }

    public void setListaTotalizadorBrindes(List<BrindeVO> listaTotalizadorBrindes) {
        this.listaTotalizadorBrindes = listaTotalizadorBrindes;
    }

    public List<ClienteVO> getListaTotalizadorAlunoSemResgate() {
        return listaTotalizadorAlunoSemResgate;
    }

    public void setListaTotalizadorAlunoSemResgate(List<ClienteVO> listaTotalizadorAlunoSemResgate) {
        this.listaTotalizadorAlunoSemResgate = listaTotalizadorAlunoSemResgate;
    }

    public Date getDataInicial() {
        return dataInicial;
    }

    public void setDataInicial(Date dataInicial) {
        this.dataInicial = dataInicial;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public Integer getTotalPontos() {
        totalPontos = 0;
        for (ItemCampanhaVO itenTotal : listaTotalizadorCategoriaPontos) {
            totalPontos+=itenTotal.getPontos();
        }
        return (totalPontos==0?1:totalPontos);
    }

    public Integer getTotalBrindes() {
        totalBrindes = 0;
        for (BrindeVO brinde : listaTotalizadorBrindes) {
            totalBrindes+=brinde.getPontos();
        }
        return (totalBrindes==0?1:totalBrindes);
    }

    public void setTotalBrindes(Integer totalBrindes) {
        this.totalBrindes = totalBrindes;
    }

    public List<ClienteVO> getListaTotalizadorPontosCategoria() {
        return listaTotalizadorPontosCategoria;
    }

    public void setListaTotalizadorPontosCategoria(List<ClienteVO> listaTotalizadorPontosCategoria) {
        this.listaTotalizadorPontosCategoria = listaTotalizadorPontosCategoria;
    }

    public List<HistoricoPontosVO> getListaTotalizadorHistoricoPontos() {
        return listaTotalizadorHistoricoPontos;
    }

    public void setListaTotalizadorHistoricoPontos(List<HistoricoPontosVO> listaTotalizadorHistoricoPontos) {
        this.listaTotalizadorHistoricoPontos = listaTotalizadorHistoricoPontos;
    }

    public String getAtributosRel() {
        return atributosRel;
    }

    public void setAtributosRel(String atributosRel) {
        this.atributosRel = atributosRel;
    }

    public String getTituloRel() {
        return tituloRel;
    }

    public void setTituloRel(String tituloRel) {
        this.tituloRel = tituloRel;
    }

    public String getPrefixoRel() {
        if(prefixoRel==null || prefixoRel.isEmpty())
            prefixoRel = "RelatorioBiClubeDeVantagens";
        return prefixoRel;
    }

    public void setPrefixoRel(String prefixoRel) {
        this.prefixoRel = prefixoRel;
    }

    public ListaPaginadaTO getPaginadorListaBrindesBi() {
        if (paginadorListaBrindesBi == null)
            paginadorListaBrindesBi = new ListaPaginadaTO(9);
        return paginadorListaBrindesBi;
    }

    public void setPaginadorListaBrindesBi(ListaPaginadaTO paginadorListaBrindesBi)throws  Exception {
        this.paginadorListaBrindesBi = paginadorListaBrindesBi;
        consultarTotalizadorBrindes();
    }

    public void proximaPagina(ActionEvent evt) throws Exception {
        getPaginadorListaBrindesBi().proximaPagina();
        consultarTotalizadorBrindes();
    }

    public void paginaAnterior(ActionEvent evt) throws Exception {
        getPaginadorListaBrindesBi().paginaAnterior();
        consultarTotalizadorBrindes();
    }

    public void ultimaPagina(ActionEvent evt) throws Exception {
        getPaginadorListaBrindesBi().ultimaPagina();
        consultarTotalizadorBrindes();
    }

    public void primeiraPagina(ActionEvent evt) throws Exception {
        getPaginadorListaBrindesBi().primeiraPagina();
        consultarTotalizadorBrindes();
    }

    public void atualizarNumeroItensPagina(ActionEvent evt) throws Exception {
        getPaginadorListaBrindesBi().setOffset(0);
        consultarTotalizadorBrindes();
    }

    public int getQTD_MAX_DESCRICAO() {
        return QTD_MAX_DESCRICAO;
    }

    public int getQTD_MAX_NOME() {
        return QTD_MAX_NOME;
    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }
}

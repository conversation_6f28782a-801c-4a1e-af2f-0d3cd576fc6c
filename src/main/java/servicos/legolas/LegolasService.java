package servicos.legolas;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.Charsets;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;


public class LegolasService {

    public static JSONObject PATHS = null;
    public static final int maxTentativas = 30;

    static {
        try {
            PATHS = obterUrls();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String obterToken(String urlAutenticacaoMS, boolean persistirCasoErro) throws Exception {
        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", "application/json");

        String path_id_persona = PropsService.getPropertyValue("AUTH_SECRET_PERSONA_PATH");
        JSONObject personaId = new JSONObject();
        personaId.put("id", Uteis.readLineByLineJava8(path_id_persona).replace("\n", "").replace("\r", ""));

        String result = "";
        int tentativa = 0;

        while (true) {
            try {
                ++tentativa;
                ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
                executeRequestHttpService.connectTimeout = 10000;
                executeRequestHttpService.readTimeout = 10000;

                if (tentativa == 1) { //primeira tentativa
                    Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | PRIMEIRA TENTATIVA de obter token de autenticação para usar no Aragorn");
                } else {
                    Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | Tentativa " + tentativa + "/" + maxTentativas + " de obter token de autenticação para usar no Aragorn");
                }

                // Tenta fazer a request para obter o token no autenticacao-ms, se der erro e for pra persistirCasoErro, tenta novamente no máximo 30 vezes,
                // com um tempo de espera randômico. O tempo de espera é randômico pois caso dê problema, não será realizado a chamada ao
                // mesmo tempo de vários clientes, cada um vai fazer requisição em um tempo diferente...
                result = executeRequestHttpService.executeHttpRequestWithTimeout(urlAutenticacaoMS + "/aut/gt",
                        personaId.toString(),
                        headersMap,
                        ExecuteRequestHttpService.METODO_POST,
                        Charsets.UTF_8.name(), false);
                break;
            } catch (Exception ex) {
                if (persistirCasoErro) {
                    long timeout = (long) obterTimeoutRandomico(); // timeout é randômico aqui entre 10 e 20 segundos...
                    Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | ERRO: LegolasService | obterToken --> " + ex.getMessage());
                    Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | Tentativa " + tentativa + "/" + maxTentativas + " falhou. Motivo: " + ex.getMessage());
                    ex.printStackTrace();
                    if (tentativa >= maxTentativas) {
                        Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | NÃO REALIZAR MAIS TENTATIVAS, ATINGIU O LIMITE DE TENTATIVAS");
                        throw ex;
                    }
                    Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | Tentar novamente em " + timeout / 1000 + " segundos...");
                    Thread.sleep(timeout); // timeout é randômico aqui entre 10 e 20 segundos...
                } else {
                    throw ex;
                }
            }
        }

        if (new JSONObject(result).optJSONObject("meta") != null) {
            throw new Exception(new JSONObject(result).optJSONObject("meta").optString("message"));
        }
        Uteis.logarDebug("**OBTER TOKEN PARA ARAGORN** | TOKEN GERADO COM SUCESSO!");
        return new JSONObject(result).optString("content");
    }

    public static Integer obterTimeoutRandomico() {
        return UteisValidacao.gerarNumeroRandomico(10000, 20000);
    }

    public static JSONObject preencherUrls() throws Exception {
        String result = ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue("DISCOVERY_URL") + "/paths", null,
                new HashMap<>(), ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());
        return new JSONObject(result).getJSONObject("content");
    }

    public static JSONObject obterUrls() {
        if (PATHS == null) {
            try {
                PATHS = preencherUrls();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return PATHS;
    }

    public static void init() {
        PATHS = null;
        obterUrls();
    }

    public static void main(String[] args) throws Exception{
        try {
            JSONObject caminhos = obterUrls();
            System.out.println(caminhos);
            System.out.println(obterToken(caminhos.optString("autenticacao"), false));
        }catch (Exception e){
            e.printStackTrace();
        }

    }

}

package servicos.configuracoes;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaCadastroClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.ConfiguracaoSistemaCadastroCliente;
import negocio.interfaces.basico.ClienteInterfaceFacade;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.List;

/**
 * Created by Joao Alcides on 10/10/2016.
 */
public class ThreadProcessarPendenciaAluno extends Thread  {

    private ClienteInterfaceFacade clienteDao;
    private List<ConfiguracaoSistemaCadastroClienteVO> listaCli;
    private List<ConfiguracaoSistemaCadastroClienteVO> listaVi;
    private ConfiguracaoSistemaVO cfgSistema;
    private UsuarioVO usuario;


    public ThreadProcessarPendenciaAluno(ClienteInterfaceFacade clienteDao, List<ConfiguracaoSistemaCadastroClienteVO> listaCli,
            List<ConfiguracaoSistemaCadastroClienteVO> listaVi, ConfiguracaoSistemaVO cfgSistema,
                                         UsuarioVO usuario){
        this.clienteDao = clienteDao;
        this.listaCli = listaCli;
        this.listaVi = listaVi;
        this.cfgSistema = cfgSistema;
        this.usuario = usuario;

    }

    public static void main(String[] args) throws Exception{
        Connection con = DriverManager.getConnection("***************************************", "postgres", "pactodb");
        ClienteInterfaceFacade clienteDao = new Cliente(con);
        ConfiguracaoSistema cfgDao = new ConfiguracaoSistema(con);
        ConfiguracaoSistemaVO configuracaoSistemaVO = cfgDao.buscarPorCodigo(1,
                false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Usuario usuarioDao = new Usuario(con);
        ConfiguracaoSistemaCadastroCliente cfgCadastroDao = new ConfiguracaoSistemaCadastroCliente(con);
        List<ConfiguracaoSistemaCadastroClienteVO> listaCli = cfgCadastroDao.consultar(false);
        List<ConfiguracaoSistemaCadastroClienteVO> listaVi = cfgCadastroDao.consultar(true);
        UsuarioVO usuario = usuarioDao.consultarPorCodigo(2, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        ThreadProcessarPendenciaAluno thread = new ThreadProcessarPendenciaAluno(clienteDao, listaCli,
                listaVi, configuracaoSistemaVO,  usuario);
        thread.processarAlunos();
    }

    @Override
    public void run() {
        processarAlunos();
    }


    public void processarAlunos(){
        try {
            List<Integer> clientes = clienteDao.obterListaTodosClientes();
//            for(Integer cli : clientes){
                clienteDao.gerarPendenciaCadastroCliente(1481, usuario,
                        cfgSistema,
                        listaCli, listaVi);
                System.out.println("Processei aluno de codigo "+1481);
//            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}

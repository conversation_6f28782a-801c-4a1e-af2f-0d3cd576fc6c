package servicos.dadosgerenciaispagamento;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.LogCobrancaPactoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogCobrancaPacto;
import org.json.JSONObject;
import servicos.adm.CreditoDCCService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 02/03/2021
 */
public class DadosGerenciaisPagamentoService {

    private String chave;

    public DadosGerenciaisPagamentoService(String chave) {
        this.chave = chave;
    }

    public void processaDados(Date dia) throws Exception {
        Connection con = null;
        DAO dao = null;
        Empresa empresaDAO = null;
        CreditoDCCService creditoDCCService = null;
        LogCobrancaPacto logCobrancaPactoDAO = null;
        try {
            Uteis.logarDebug("Iniciando | Dados Pagamento de Cobrança Pacto... Dia: " + Calendario.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"));
            dao = new DAO();
            con = dao.obterConexaoEspecifica(this.chave);
            con.setAutoCommit(false);

            empresaDAO = new Empresa(con);
            logCobrancaPactoDAO = new LogCobrancaPacto(con);
            creditoDCCService = new CreditoDCCService(con);

            Date mesProcessar = dia;
            if (Uteis.getDiaMesData(dia) < 2) {
                mesProcessar = Uteis.somarMeses(dia, -1);
            }
            List<EmpresaVO> listaEmpresas = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (EmpresaVO empresaVO : listaEmpresas) {
                try {
                    DadosGerenciaisPagamentoVO obj = obterDadosGerenciaisPagamento(Uteis.obterPrimeiroDiaMes(mesProcessar), empresaVO.getCodigo(), con);
                    obj.setDataRegistro(Calendario.hoje());
                    obj.setEmpresa(empresaVO);
                    obj.setMes(mesProcessar);
                    obj.setFaturamento(obterFaturamento(mesProcessar, empresaVO.getCodigo(), con));
                    obj.setUsaVendasOnline(obterUsaVendasOnline(mesProcessar, empresaVO, con));
                    obj.setUsaCobranca(obterUsaCobranca(mesProcessar, empresaVO, con));
                    obj.setContratosAtivos(obterContratosAtivos(mesProcessar, empresaVO.getCodigo(), con));

                    Integer totalRemessa = 0;
                    Integer totalPix = 0;
                    Integer totalTransacao = 0;
                    Integer totalBoletosOnline = 0;
                    Integer totalBoletosRemessa = 0;

                   if (Calendario.dataNoMesmoMesAno(mesProcessar, Calendario.hoje())) {
                        //se for do mesmo mes então faz a consulta atual
                        totalRemessa = creditoDCCService.consultarItensCobrancaPactoRemessasItemTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, false, null, null);
                        totalPix = creditoDCCService.consultarItensCobrancaPactoPixTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, false, null, null);
                        totalTransacao = creditoDCCService.consultarItensCobrancaPactoTransacaoTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, false, null, null);
                        totalBoletosOnline = creditoDCCService.consultarItensCobrancaPactoBoletoOnlineTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, false, null, null);
                        totalBoletosRemessa = creditoDCCService.consultarItensCobrancaPactoBoletoRemessaTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, false, null, null);
                        obj.setCreditoUtilizado((totalRemessa + totalPix + totalTransacao + totalBoletosOnline + totalBoletosRemessa));
                    } else {
                        Date inicioMes= Uteis.obterPrimeiroDiaMes(mesProcessar);
                        Date fimMes= Uteis.obterUltimoDiaMes(mesProcessar);

                        totalRemessa = creditoDCCService.consultarItensCobrancaPactoRemessasItemTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, null, inicioMes, fimMes);
                        totalPix = creditoDCCService.consultarItensCobrancaPactoPixTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, null, inicioMes, fimMes);
                        totalBoletosOnline = creditoDCCService.consultarItensCobrancaPactoBoletoOnlineTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, null, inicioMes, fimMes);
                        totalBoletosRemessa = creditoDCCService.consultarItensCobrancaPactoBoletoRemessaTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, null, inicioMes, fimMes);
                        totalTransacao = creditoDCCService.consultarItensCobrancaPactoTransacaoTotal(empresaVO.getCodigo(), empresaVO.getTipoCobrancaPacto(), null, null, inicioMes, fimMes);
                        obj.setCreditoUtilizado((totalRemessa + totalPix + totalTransacao + totalBoletosOnline + totalBoletosRemessa));
                    }


                    List<LogCobrancaPactoVO> lista = logCobrancaPactoDAO.consultar(empresaVO.getCodigo(), Uteis.obterPrimeiroDiaMes(mesProcessar),
                            Uteis.obterUltimoDiaMes(mesProcessar), 1, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyList(lista)) {
                        LogCobrancaPactoVO logCobrancaPactoVO = lista.get(0);
                        obj.setTipoCobrancaPacto(logCobrancaPactoVO.getTipoCobrancaPacto());
                        if (!UteisValidacao.emptyNumber(logCobrancaPactoVO.getValorTotal()) &&
                                !UteisValidacao.emptyNumber(logCobrancaPactoVO.getQuantidade())) {
                            obj.setValorCreditoPacto(Uteis.arredondarForcando2CasasDecimais(logCobrancaPactoVO.getValorTotal() / logCobrancaPactoVO.getQuantidade()));
                        }
                    } else {
                        obj.setTipoCobrancaPacto(empresaVO.getTipoCobrancaPacto());
                        obj.setValorCreditoPacto(empresaVO.getValorCreditoPacto());
                    }

                    JSONObject jsonDados = new JSONObject();
                    jsonDados.put("qtdRemessa", totalRemessa);
                    jsonDados.put("qtdPix", totalPix);
                    jsonDados.put("qtdBoletosOnline", totalBoletosOnline);
                    jsonDados.put("qtdBoletosRemessa", totalBoletosRemessa);
                    jsonDados.put("qtdTransacao", totalTransacao);
                    obj.setJsonDados(jsonDados.toString());

                    gravar(obj, con);
                } catch (Exception e) {
                    Uteis.logarDebug("# ERRO Dados Pagamento - CodEmpresa: " + empresaVO.getCodigo() + " - " + e.getMessage());
                    throw e;
                }
            }
            con.commit();
        } catch (Exception ex) {
            if (con != null) {
                con.rollback();
            }
            ex.printStackTrace();
            throw ex;
        } finally {
            if (con != null) {
                con.setAutoCommit(true);
                try {
                    con.close();
                } catch (SQLException ex) {
                    Logger.getLogger(DadosGerenciaisPagamentoService.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            con = null;
            dao = null;
            empresaDAO = null;
            logCobrancaPactoDAO = null;
            creditoDCCService = null;
            Uteis.logarDebug("Finalizando | Dados Pagamento de Cobrança Pacto! | Dia: " + Calendario.getDataAplicandoFormatacao(dia, "dd/MM/yyyy"));
        }
    }

    private DadosGerenciaisPagamentoVO obterDadosGerenciaisPagamento(Date mes, Integer empresa, Connection con) throws SQLException {
        String sqlStr = "select * from dadosgerenciaispagamento where empresa = " + empresa + " and mes = '" + Uteis.getDataFormatoBD(mes) + "';";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlStr)) {
                if (rs.next()) {
                    DadosGerenciaisPagamentoVO obj = new DadosGerenciaisPagamentoVO();
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
                    obj.getEmpresa().setCodigo(rs.getInt("empresa"));
                    obj.setMes(rs.getDate("mes"));
                    obj.setTipoCobrancaPacto(rs.getInt("tipoCobrancaPacto"));
                    obj.setContratosAtivos(rs.getInt("contratosAtivos"));
                    obj.setUsaCobranca(rs.getBoolean("usaCobranca"));
                    obj.setUsaVendasOnline(rs.getBoolean("usaVendasOnline"));
                    obj.setFaturamento(rs.getDouble("faturamento"));
                    obj.setValorCreditoPacto(rs.getDouble("valorCreditoPacto"));
                    obj.setCreditoUtilizado(rs.getInt("creditoUtilizado"));
                    return obj;
                } else {
                    return new DadosGerenciaisPagamentoVO();
                }
            }
        }
    }

    private Double obterFaturamento(Date mes, Integer empresa, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("sum(valortotal::numeric) as valor \n");
        sql.append("from recibopagamento \n");
        sql.append("where empresa = ").append(empresa).append(" \n");
        sql.append("and data::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes))).append("' \n");
        sql.append("and data::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("' \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getDouble("valor");
                } else {
                    return 0.0;
                }
            }
        }
    }

    private boolean obterUsaVendasOnline(Date mes, EmpresaVO empresaVO, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select exists( \n");
        sql.append("select codigo \n");
        sql.append("from contrato \n");
        sql.append("where empresa = ").append(empresaVO.getCodigo()).append(" \n");
        sql.append("and datalancamento::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes))).append("' \n");
        sql.append("and datalancamento::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("' \n");
        sql.append("and origemsistema = ").append(OrigemSistemaEnum.VENDAS_ONLINE_2.getCodigo()).append(" \n");
        sql.append(") as usavendas \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getBoolean("usavendas");
                } else {
                    return false;
                }
            }
        }
    }

    private boolean obterUsaCobranca(Date mes, EmpresaVO empresaVO, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select ( \n");
        sql.append("exists( \n");
        sql.append("select  \n");
        sql.append("codigo  \n");
        sql.append("from remessa  \n");
        sql.append("where empresa = ").append(empresaVO.getCodigo()).append(" \n");
        sql.append("and dataregistro::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes))).append("' \n");
        sql.append("and dataregistro::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("') \n");
        sql.append("or  \n");
        sql.append("exists( \n");
        sql.append("select codigo  \n");
        sql.append("from transacao  \n");
        sql.append("where empresa = ").append(empresaVO.getCodigo()).append(" \n");
        sql.append("and dataprocessamento::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes))).append("' \n");
        sql.append("and dataprocessamento::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("') \n");
        sql.append(") as usacobranca  \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getBoolean("usacobranca");
                } else {
                    return false;
                }
            }
        }
    }

    private Integer obterContratosAtivos(Date mes, Integer empresa, Connection con) throws Exception {

        if (Calendario.dataNoMesmoMesAno(mes, Calendario.hoje())) {
            return obterContratosAtivosMesAtual(mes, empresa, con);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("valor \n");
        sql.append("from dadosgerencialpmg \n");
        sql.append("where identificador = 'TF' \n");
        sql.append("and indicador = 'MC' \n");
        sql.append("and periodicidade = 'MS' \n");
        sql.append("and empresa = ").append(empresa).append(" \n");
        sql.append("and datapesquisafim::date >= '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes))).append("' \n");
        sql.append("and datapesquisafim::date <= '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("' \n");
        sql.append("order by datageracao desc \n");
        sql.append("limit 1 \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("valor");
                } else {
                    return 0;
                }
            }
        }
    }

    private Integer obterContratosAtivosMesAtual(Date mes, Integer empresa, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("count(c.*) as total \n");
        sql.append("from contrato c \n");
        sql.append("where c.situacao = 'AT' \n");
        sql.append("and c.empresa = ").append(empresa).append(" \n");
        sql.append("and not exists(select contrato from historicocontrato where contrato = c.codigo and tipohistorico in ('CA','DE')) \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return rs.getInt("total");
                } else {
                    return 0;
                }
            }
        }
    }

    private void gravar(DadosGerenciaisPagamentoVO obj, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getCodigo())) {
            incluir(obj, con);
        } else {
            alterar(obj, con);
        }
    }

    private void incluir(DadosGerenciaisPagamentoVO obj, Connection con) throws Exception {
        String sqlIncluir = "insert into dadosgerenciaispagamento(dataRegistro,empresa,mes,tipoCobrancaPacto," +
                "contratosAtivos,usaCobranca,usaVendasOnline,faturamento,valorCreditoPacto,creditoUtilizado,jsonDados) " +
                "values (?,?,?,?,?,?,?,?,?,?,?);";
        try (PreparedStatement stm = con.prepareStatement(sqlIncluir)) {
            int i = 0;
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(++i, obj.getEmpresa().getCodigo());
            stm.setDate(++i, Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(obj.getMes())));
            if (obj.getTipoCobrancaPacto() == null) {
                stm.setNull(++i, Types.NULL);
            } else {
                stm.setInt(++i, obj.getTipoCobrancaPacto());
            }
            stm.setInt(++i, obj.getContratosAtivos());
            stm.setBoolean(++i, obj.isUsaCobranca());
            stm.setBoolean(++i, obj.isUsaVendasOnline());
            stm.setDouble(++i, obj.getFaturamento());
            stm.setDouble(++i, obj.getValorCreditoPacto());
            stm.setInt(++i, obj.getCreditoUtilizado());
            stm.setString(++i, obj.getJsonDados());
            stm.execute();
        }
    }

    private void alterar(DadosGerenciaisPagamentoVO obj, Connection con) throws Exception {
        String sqlUpdate = "UPDATE dadosgerenciaispagamento SET " +
                "dataRegistro = ?, empresa = ?, mes = ?, tipoCobrancaPacto = ?, contratosAtivos = ?, usaCobranca = ?, " +
                "usaVendasOnline = ?, faturamento = ?, valorCreditoPacto = ?, creditoUtilizado = ?, jsonDados = ? " +
                "WHERE codigo = ?;";
        try (PreparedStatement stm = con.prepareStatement(sqlUpdate)) {
            int i = 0;
            stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(++i, obj.getEmpresa().getCodigo());
            stm.setDate(++i, Uteis.getDataJDBC(Uteis.obterPrimeiroDiaMes(obj.getMes())));
            if (obj.getTipoCobrancaPacto() == null) {
                stm.setNull(++i, Types.NULL);
            } else {
                stm.setInt(++i, obj.getTipoCobrancaPacto());
            }
            stm.setInt(++i, obj.getContratosAtivos());
            stm.setBoolean(++i, obj.isUsaCobranca());
            stm.setBoolean(++i, obj.isUsaVendasOnline());
            stm.setDouble(++i, obj.getFaturamento());
            stm.setDouble(++i, obj.getValorCreditoPacto());
            stm.setInt(++i, obj.getCreditoUtilizado());
            stm.setString(++i, obj.getJsonDados());
            stm.setInt(++i, obj.getCodigo());
            stm.execute();
        }
    }

    public static void main(String[] args) {
        try {
            String chave = "teste";
            DadosGerenciaisPagamentoService service = new DadosGerenciaisPagamentoService(chave);
//            List<Date> meses = Uteis.getMesesEntreDatas(Uteis.somarMeses(Uteis.obterPrimeiroDiaMes(Calendario.hoje()), -12), Uteis.obterUltimoDiaMes(Calendario.hoje()));
//            for (Date mes : meses) {
//                service.processaDados(Uteis.obterUltimoDiaMes(mes));
//            }
            service.processaDados(Calendario.hoje());
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

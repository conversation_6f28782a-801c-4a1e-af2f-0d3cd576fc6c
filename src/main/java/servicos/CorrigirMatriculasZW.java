package servicos;

import negocio.comuns.utilitarias.Calendario;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;

public class CorrigirMatriculasZW {

    public static void main(String[] args) {
        try {
            Connection bdTR = conection("**********************************************", "postgres", "pactodb");
            Connection bdZW = conection("*************************************************", "postgres", "pactodb");
//            sincronizarClientes(bdTR, bdZW);
//            alunosSemRegistroZW(bdZW, bdTR, true);
//            System.out.println(Calendario.hoje());
            fotodeumprooutro(bdZW, bdTR);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void sincronizarClientes(Connection bdTR, Connection bdZW) throws Exception{
        ResultSet rs = bdZW.prepareStatement("select codigo, codigomatricula from cliente").executeQuery();
        System.out.println("atualizar matriculas...");
        int i = 0;
        while(rs.next()){
            try {
                int codigoCliente = rs.getInt("codigo");
                System.out.println(++i);
                int matricula = rs.getInt("codigomatricula");
//                bdTR.prepareStatement("update clientesintetico set matricula = " + matricula
//                        + " where codigocliente = " + codigoCliente).execute();

                bdTR.prepareStatement("update clientepesquisa set matricula = " + matricula
                        + " where codigoexterno = " + codigoCliente).execute();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        System.out.println("atualizadas!");
    }

    public static void removerDuplicadades(Connection bdTR) throws Exception{
        ResultSet rs = bdTR.prepareStatement("select nome, matricula  from clientepesquisa c \n" +
                "group by nome, matricula\n" +
                "having count(codigoexterno) > 1 ").executeQuery();
        System.out.println("atualizar matriculas...");
        int i = 0;
        while(rs.next()){
            try {
                int codigoCliente = rs.getInt("codigo");
                System.out.println(++i);
                int matricula = rs.getInt("codigomatricula");
//                bdTR.prepareStatement("update clientesintetico set matricula = " + matricula
//                        + " where codigocliente = " + codigoCliente).execute();

                bdTR.prepareStatement("update clientepesquisa set matricula = " + matricula
                        + " where codigoexterno = " + codigoCliente).execute();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        System.out.println("atualizadas!");
    }

    public static Connection conection(String url, String username, String senha) throws Exception{
        return DriverManager.getConnection(url, username, senha);
    }


    public static void fotodeumprooutro(Connection zw, Connection tr) throws Exception{
        ResultSet rs = tr.prepareStatement("select fotokey, codigopessoa from pessoa p\n" +
                "inner join clientesintetico c on c.pessoa_codigo = p.codigo\n" +
                "where fotokey is not null and fotokey <> '' and fotokey <> 'fotoPadrao.jpg'").executeQuery();
        while(rs.next()){
            zw.prepareStatement("update pessoa set fotokey = '" + rs.getString("fotokey") + "' where codigo = " + rs.getInt("codigopessoa")).execute();
        }
    }
    public static void alunosSemRegistroZW(Connection zw, Connection tr, boolean excluir) throws Exception{
        ResultSet rs = zw.prepareStatement("select nomecliente, matricula, codigocliente from situacaoclientesinteticodw s ").executeQuery();
        Map<Integer, String> alunosZW = new HashMap<>();
        while(rs.next()){
            alunosZW.put(rs.getInt("matricula"), rs.getString("nomecliente"));
        }

        ResultSet rstr = tr.prepareStatement("select nome, matricula from clientesintetico s ").executeQuery();
        Map<Integer, String> alunosTR = new HashMap<>();
        while(rstr.next()){
            alunosTR.put(rstr.getInt("matricula"), rstr.getString("nome"));
        }
        String codigosExcluir = "";
        int naoexiste = 0;
        int nomediferente = 0;
        for(Integer matricula : alunosTR.keySet()){
            String nometr = alunosTR.get(matricula);
            String nomezw = alunosZW.get(matricula);

            if(nomezw == null){
                System.out.println(nometr + " não existe no zw");
                if(excluir){

                }
                naoexiste++;
            } else if(!nomezw.equals(nometr)){
                System.out.println(nometr + " está diferente de " + nomezw);
                nomediferente++;
            }
        }
        System.out.println("não existem: " + naoexiste);
        System.out.println("diferentes: " + nomediferente);

    }

}


package servicos;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.threads.ThreadRobo;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.propriedades.PropsService;

/**
 * <AUTHOR>
 */
public class RobotRunner {

    public static void main(String[] args) {
        System.out.println("Entrou no Runner");
        Uteis.debug = true;
        if (args.length == 0) {
            args = new String[]{"pacto"};
        }
        Uteis.logar(null, String.format("#### iniciando processamento Robo da chave %s em: %s ###",
                new Object[]{
                        args[0],
                        Uteis.getDataComHora(Calendario.hoje())
                }));
        if (args.length >= 1) {
            String nomeThread = args[0];
            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(nomeThread);
                Conexao.guardarConexaoForJ2SE(con);
                //atualizar banco de dados, se defasado
                LoginControle control = new LoginControle();
                control.setUsername("RECOR");
                control.setSenha(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
                control.setUserOamd("adm");
                control.login();
                control.atualizarBD();

                ThreadRobo threadRobo = new ThreadRobo();
                threadRobo.setRodarEmLoop(false);
                threadRobo.setChave(nomeThread);
                threadRobo.setName("Robo_" + nomeThread);
                threadRobo.setRealizar(true);

                threadRobo.setValidarEstatisticas(true);


                System.out.println(control.obterNumeroDiasSemProcessamento());
                threadRobo.setDiasDesatualizados(control.getDiasSemProcessamento());
                threadRobo.run();
                Uteis.finalizarExecutor(5);

            } catch (Exception ex) {
                Uteis.logar(null, String.format("Erro ao obter conexao especifica com a chave %s : %s ", new Object[]{ nomeThread, ex.getMessage()}));
            }

        }
        Uteis.logar(null, String.format("#### Finalizado processamento Robo da chave %s em: %s ###",
                new Object[]{
                        args[0],
                        Uteis.getDataComHora(Calendario.hoje())
                }));
    }

}

package servicos.integracao.nuvemshop;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.nuvemshop.OperacoesIntegracaoNuvemshopEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.httpclient.HttpException;

import org.json.JSONObject;
import servicos.integracao.nuvemshop.json.IntegracaoNuvemshopJSON;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class IntegracaoNuvemshopServiceImpl implements IntegracaoNuvemshopServiceInterface {

    private Connection con;

    public IntegracaoNuvemshopServiceImpl(Connection con) {
        this.con = con;
    }

    public IntegracaoNuvemshopJSON montarDadosIntegracao(int codigoEmpresa) throws Exception {
        String sql = "SELECT e.integracaonuvemshopnomeapp, e.integracaonuvemshoptokenacesso, e.integracaonuvemshopemail, e.integracaonuvemshophabilitada, e.integracaonuvemshopstoreid " +
                "FROM empresa e WHERE e.codigo = ?";
        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setInt(1, codigoEmpresa);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    IntegracaoNuvemshopJSON integracaoJson = new IntegracaoNuvemshopJSON();
                    integracaoJson.setNomeApp(rs.getString("integracaonuvemshopnomeapp"));
                    integracaoJson.setTokenAcesso(rs.getString("integracaonuvemshoptokenacesso"));
                    integracaoJson.setEmail(rs.getString("integracaonuvemshopemail"));
                    integracaoJson.setHabilitada(rs.getBoolean("integracaonuvemshophabilitada"));
                    integracaoJson.setStoreId(rs.getString("integracaonuvemshopstoreid"));
                    return integracaoJson;
                } else {
                    throw new Exception("Dados não encontrados para a empresa: " + codigoEmpresa);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            throw e;
        }
    }

    public JSONObject consultarPedidoNuvemshop(IntegracaoNuvemshopJSON integracaoJson, String orderId) throws Exception {
        if (integracaoJson == null || UteisValidacao.emptyString(orderId) || UteisValidacao.emptyString(integracaoJson.getStoreId()) ) {
            throw new IllegalArgumentException("Parâmetros inválidos fornecidos no método consultarDadosNuvemshop");
        }

        String url = "https://api.nuvemshop.com.br/v1/" + integracaoJson.getStoreId() + "/orders/" + orderId;

        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authentication", "bearer " + integracaoJson.getTokenAcesso());
            headers.put("User-Agent", integracaoJson.getNomeApp() + " (" + integracaoJson.getEmail() + ")");

            String retornoChamadaApiNuvemshop = ExecuteRequestHttpService.executeHttpRequest(url, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");
            JSONObject jsonRetorno = new JSONObject(retornoChamadaApiNuvemshop);
            return jsonRetorno;

        } catch (HttpException e) {
            throw new Exception("Erro ao consultar dados na Nuvemshop: " + e.getMessage(), e);
        }
    }

    private void gravaLogNuvemshop(String key, String storeId, String operacao, String retorno){
        try {
            LogVO obj = new LogVO();
            if (operacao.equals(OperacoesIntegracaoNuvemshopEnum.BUSCAR_PEDIDOS)){
                obj.setChavePrimaria("INTEGRAÇAO NUVEMSHOP - BUSCAR PEDIDO" );
            }else{
                obj.setChavePrimaria("INTEGRACAO NUVEMSHOP - OPERAÇÃO DESCONHECIDA");
            }
            obj.setNomeEntidade("Nuvemshop");
            obj.setNomeEntidadeDescricao("Operações com o webhook");
            obj.setOperacao(operacao);
            obj.setNomeCampo("Dados do webhook");
            obj.setValorCampoAlterado("CHAVE: " + key + " STORE ID: " + storeId +  " RETORNO: " + retorno);
            obj.setDataAlteracao(Calendario.hoje());

            DaoAuxiliar.retornarAcessoControle(key).getLogDao().incluirSemCommit(obj);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }


}

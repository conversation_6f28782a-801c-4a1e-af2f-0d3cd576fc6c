package servicos.integracao.nuvemshop.json;

import org.json.JSONObject;

public class IntegracaoNuvemshopJSON {
    private String nomeApp;
    private String tokenAcesso;
    private String email;
    private String storeId;
    private boolean habilitada;

    public String getNomeApp() {
        return nomeApp;
    }

    public void setNomeApp(String nomeApp) {
        this.nomeApp = nomeApp;
    }

    public String getTokenAcesso() {
        return tokenAcesso;
    }

    public void setTokenAcesso(String tokenAcesso) {
        this.tokenAcesso = tokenAcesso;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isHabilitada() {
        return habilitada;
    }

    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("nomeApp", nomeApp);
        json.put("tokenAcesso", tokenAcesso);
        json.put("email", email);
        json.put("habilitada", habilitada);
        return json;
    }

    @Override
    public String toString() {
        return "IntegracaoNuvemshopJSON{" +
                "nomeApp='" + nomeApp + '\'' +
                ", tokenAcesso='" + tokenAcesso + '\'' +
                ", email='" + email + '\'' +
                ", habilitada=" + habilitada +
                '}';
    }
}


package servicos.integracao.nfse;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.nfse package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ConsultarNotas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarNotas");
    private final static QName _ConsultarNotasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarNotasResponse");
    private final static QName _ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse");
    private final static QName _ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.nfse
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ConsultarNotas }
     *
     */
    public ConsultarNotas createConsultarNotas() {
        return new ConsultarNotas();
    }

    /**
     * Create an instance of {@link ConsultarNotasResponse }
     * 
     */
    public ConsultarNotasResponse createConsultarNotasResponse() {
        return new ConsultarNotasResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarNotas }{@code >}}
     *
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarNotas")
    public JAXBElement<ConsultarNotas> createConsultarNotas(ConsultarNotas value) {
        return new JAXBElement<ConsultarNotas>(_ConsultarNotas_QNAME, ConsultarNotas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarNotasResponse }{@code >}}
     *
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarNotasResponse")
    public JAXBElement<ConsultarNotasResponse> createConsultarNotasResponse(ConsultarNotasResponse value) {
        return new JAXBElement<ConsultarNotasResponse>(_ConsultarNotasResponse_QNAME, ConsultarNotasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse }
     *
     */
    public ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse createConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse() {
        return new ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse();
    }

    /**
     * Create an instance of {@link ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem }
     *
     */
    public ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem createConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem() {
        return new ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse }{@code >}}
     *
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse")
    public JAXBElement<ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse> createConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse(ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse value) {
        return new JAXBElement<ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse>(_ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse_QNAME, ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem")
    public JAXBElement<ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem> createConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem value) {
        return new JAXBElement<ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem>(_ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem_QNAME, ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem.class, null, value);
    }

}


package servicos.integracao.nfse;

import negocio.comuns.nfe.DocumentoFiscalRelatorioPeriodoTO;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.1
 * 
 */
@WebService(name = "IntegracaoNFSeWS", targetNamespace = "http://webservice.basico.comuns.negocio/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface IntegracaoNFSeWS {


    /**
     * Os 3 primeiros parâmetros são exigidos para uma certeza exatidão na hora de diferenciar as notas de cada empresa.
     *
     * @param chaveNFSe       para localizar as notas na empresa no módulo de notas.
     * @param chaveZW         para localizar as notas na empresa no módulo de notas.
     * @param cpfCnpj         para localizar as notas na empresa no módulo de notas.
     * @param modelosPesquisa modelos que serão pesquisados.
     *
     * @return um JSON de {@link DocumentoFiscalRelatorioPeriodoTO} contendo o total de notas <b>CANCELADAS</b> ou <b>NÃO AUTORIZADAS</b>
     * do período de ontem até hoje.
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.nfse.ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem")
    @ResponseWrapper(localName = "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.nfse.ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntemResponse")
    String consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(
        @WebParam(name = "chaveNFSe", targetNamespace = "")
        String chaveNFSe,
        @WebParam(name = "chaveZW", targetNamespace = "")
        String chaveZW,
        @WebParam(name = "cpfCnpj", targetNamespace = "")
        String cpfCnpj,
        @WebParam(name = "modelosPesquisa", targetNamespace = "")
        List<String> modelosPesquisa);

    /**
     * 
     * @param rps
     * @param chave
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarNotas", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.nfse.ConsultarNotas")
    @ResponseWrapper(localName = "consultarNotasResponse", targetNamespace = "http://webservice.basico.comuns.negocio/", className = "servicos.integracao.nfse.ConsultarNotasResponse")
    public String consultarNotas(
        @WebParam(name = "chave", targetNamespace = "")
        String chave,
        @WebParam(name = "rps", targetNamespace = "")
        Integer rps);

}

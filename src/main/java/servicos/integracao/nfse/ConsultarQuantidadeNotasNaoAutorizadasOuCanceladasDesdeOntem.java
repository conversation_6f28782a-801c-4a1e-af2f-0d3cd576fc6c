
package servicos.integracao.nfse;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="chaveNFSe" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="chaveZW" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cpfCnpj" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="modelosPesquisa" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem", propOrder = {
    "chaveNFSe",
    "chaveZW",
    "cpfCnpj",
    "modelosPesquisa"
})
public class ConsultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem {

    protected String chaveNFSe;
    protected String chaveZW;
    protected String cpfCnpj;
    protected List<String> modelosPesquisa;

    /**
     * Obtém o valor da propriedade chaveNFSe.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChaveNFSe() {
        return chaveNFSe;
    }

    /**
     * Define o valor da propriedade chaveNFSe.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChaveNFSe(String value) {
        this.chaveNFSe = value;
    }

    /**
     * Obtém o valor da propriedade chaveZW.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChaveZW() {
        return chaveZW;
    }

    /**
     * Define o valor da propriedade chaveZW.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChaveZW(String value) {
        this.chaveZW = value;
    }

    /**
     * Obtém o valor da propriedade cpfCnpj.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpfCnpj() {
        return cpfCnpj;
    }

    /**
     * Define o valor da propriedade cpfCnpj.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpfCnpj(String value) {
        this.cpfCnpj = value;
    }

    /**
     * Gets the value of the modelosPesquisa property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the modelosPesquisa property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getModelosPesquisa().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getModelosPesquisa() {
        if (modelosPesquisa == null) {
            modelosPesquisa = new ArrayList<String>();
        }
        return this.modelosPesquisa;
    }

}


package servicos.integracao.nfse;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "IntegracaoNFSeWS", targetNamespace = "http://webservice.basico.comuns.negocio/", wsdlLocation = "http://localhost:8080/ZilllyonWebNFSe/IntegracaoNFSeWS?wsdl")
public class IntegracaoNFSeWS_Service
    extends Service
{

    private final static URL INTEGRACAONFSEWS_WSDL_LOCATION;
    private final static WebServiceException INTEGRACAONFSEWS_EXCEPTION;
    private final static QName INTEGRACAONFSEWS_QNAME = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoNFSeWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://localhost:8080/ZilllyonWebNFSe/IntegracaoNFSeWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        INTEGRACAONFSEWS_WSDL_LOCATION = url;
        INTEGRACAONFSEWS_EXCEPTION = e;
    }

    public IntegracaoNFSeWS_Service() {
        super(__getWsdlLocation(), INTEGRACAONFSEWS_QNAME);
    }

    public IntegracaoNFSeWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns IntegracaoNFSeWS
     */
    @WebEndpoint(name = "IntegracaoNFSeWSPort")
    public IntegracaoNFSeWS getIntegracaoNFSeWSPort() {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoNFSeWSPort"), IntegracaoNFSeWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IntegracaoNFSeWS
     */
    @WebEndpoint(name = "IntegracaoNFSeWSPort")
    public IntegracaoNFSeWS getIntegracaoNFSeWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.basico.comuns.negocio/", "IntegracaoNFSeWSPort"), IntegracaoNFSeWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (INTEGRACAONFSEWS_EXCEPTION!= null) {
            throw INTEGRACAONFSEWS_EXCEPTION;
        }
        return INTEGRACAONFSEWS_WSDL_LOCATION;
    }

}

package servicos.integracao.push;
import org.joda.time.DateTime;

import java.util.List;

public class PushMessage {
    private String admID;
    private String chave;
    private String userNameUsuario;
    private String titulo;
    private String content;
    private String linkAbrir;
    private List<String> horarios;

    private String cliente;

    public String getAdmID() {return admID;}

    public void setAdmID(String admID) {this.admID = admID;}

    public String getChave() {return chave;}

    public void setChave(String chave) {this.chave = chave;}

    public String getUserNameUsuario() {return userNameUsuario;}

    public void setUserNameUsuario(String userNameUsuario) {this.userNameUsuario = userNameUsuario;}

    public String getTitulo() {return titulo;}

    public void setTitulo(String titulo) {this.titulo = titulo;}

    public String getContent() {return content;}

    public void setContent(String content) {this.content = content;}

    public List<String> getHorarios() {return horarios;}

    public void setHorarios(List<String> horarios) {this.horarios = horarios;}

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public String getLinkAbrir() {
        return linkAbrir;
    }

    public void setLinkAbrir(String linkAbrir) {
        this.linkAbrir = linkAbrir;
    }
}

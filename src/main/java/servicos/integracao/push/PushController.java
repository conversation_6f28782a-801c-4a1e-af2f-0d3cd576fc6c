package servicos.integracao.push;

import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import org.apache.http.HttpHeaders;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static controle.arquitetura.SuperControle.getUrlApiApp;

public class PushController {
    private static final String URI_API = getUrlApiApp();
    private MalaDiretaVO malaDireta;
    private PactoPayComunicacaoVO pactoPayComunicacaoVO;
    private String content;
    private String key;

    private  String cliente;


    public PushController (MalaDiretaVO malaDireta, String key,
                           String content, String cliente,
                           PactoPayComunicacaoVO pactoPayComunicacaoVO){
        this.setMalaDireta(malaDireta);
        this.setKey(key);
        this.setContent(content);
        this.setCliente(cliente);
        this.setPactoPayComunicacaoVO(pactoPayComunicacaoVO);
    }

    public  String dateAgenda(){
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat ag = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        cal.add(Calendar.MINUTE, 3);
        return  ag.format(cal.getTime());
    }
    public List<PushMessage> setEnviaTemplate() throws Exception {
        if (this.getPactoPayComunicacaoVO() != null) {
            return this.setEnviaTemplatePactoPay();
        } else {
            return this.setEnviaTemplateMalaDireta();
        }
    }

    private List<PushMessage> setEnviaTemplatePactoPay() throws Exception {
        List<PushMessage> litms = new ArrayList<>();
        for (String destinatario : this.getPactoPayComunicacaoVO().getPactoPayComunicacaoDTO().getDestinatario()) {
            PushMessage templ = new PushMessage();
            templ.setAdmID("PACTOPAY_" + this.getPactoPayComunicacaoVO().getCodigo().toString());
            templ.setContent(this.getContent());
            templ.setChave(this.getKey());
            templ.setTitulo(this.getPactoPayComunicacaoVO().getPactoPayComunicacaoDTO().getAssunto());
            templ.setCliente(destinatario);
            templ.setUserNameUsuario(destinatario);

            String link = this.getPactoPayComunicacaoVO().getPactoPayComunicacaoDTO().getLink();
            if (UteisValidacao.emptyString(link)) {
                Object obj = this.getPactoPayComunicacaoVO().getTagsMap().get(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE);
                link = obj != null ? obj.toString() : "";
            }
            if (!UteisValidacao.emptyString(link)) {
                templ.setLinkAbrir(link);
            }

            List<String> horarios = new ArrayList<>();
            horarios.add(dateAgenda());
            templ.setHorarios(horarios);
            litms.add(templ);
        }
        return litms;
    }

    private List<PushMessage> setEnviaTemplateMalaDireta() throws Exception {
        PushMessage templ = new PushMessage();

        List<PushMessage> litms = new ArrayList<>();

        templ.setAdmID(this.getMalaDireta().getCodigo().toString());
        templ.setContent(this.getContent());
        templ.setChave(this.getKey());
        templ.setTitulo(this.getMalaDireta().getTitulo());
        UsuarioMovel usuarioMovel = new UsuarioMovel();
        Integer codigo = Integer.parseInt( this.getCliente());
        templ.setCliente((this.getCliente()));

        UsuarioMovelVO usuario = usuarioMovel.consultarPorCodigoCliente(codigo,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        templ.setUserNameUsuario(usuario.getNome());

        List<String> horarios = new ArrayList<>();
        horarios.add(dateAgenda());
        templ.setHorarios (horarios);
        litms.add(templ);

        return litms;
    }

    public String sendMessage() throws Exception {
        return this.sendMessageGeral(false);
    }

    public String send() throws Exception {
        return this.sendMessageGeral(true);
    }

    private String sendMessageGeral(boolean respostaCompleta) throws Exception {
        try {
            List<PushMessage> mensagem = setEnviaTemplate();
            RequestHttpService service = new RequestHttpService();
            Map<String, String> params = new HashMap<>();
            params.put("Authorization", "sistemapacto");
            params.put("Accept-Language", "pt-BR");

            params.put(HttpHeaders.CONTENT_TYPE, "application/json");
            String body = new JSONArray(mensagem).toString();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(URI_API, params, null, body, MetodoHttpEnum.POST);
            String resp = respostaHttpDTO.getResponse();
            JSONObject json = new JSONObject(resp);
            if (json.has("sucesso")) {
                if (respostaCompleta) {
                    return resp;
                } else {
                    return json.getJSONArray("sucesso").getJSONObject(0).getString("admID");
                }
            } else if (respostaCompleta) {
                throw new Exception("Erro enviar PushApp: " + resp);
            }
            return "erro ao disparar Push App" + URI_API;
        }
        catch (Exception e){
            if (respostaCompleta) {
                System.out.println("Erro ao disparar Push App | " + URI_API + " | " + e.getMessage());
                e.printStackTrace();
                throw e;
            } else {
                return e.getMessage();
            }
        }
    }


    public String getContent() {return content;}

    public void setContent(String content) {this.content = content;}

    public String getKey() {return key;}

    public void setKey(String key) {this.key = key;}


    public MalaDiretaVO getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(MalaDiretaVO malaDireta) {
        this.malaDireta = malaDireta;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public PactoPayComunicacaoVO getPactoPayComunicacaoVO() {
        return pactoPayComunicacaoVO;
    }

    public void setPactoPayComunicacaoVO(PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }
}

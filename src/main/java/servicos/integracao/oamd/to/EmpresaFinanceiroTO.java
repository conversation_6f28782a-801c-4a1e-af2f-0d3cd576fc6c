package servicos.integracao.oamd.to;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

/**
 * Created by ulisses on 04/07/2017.
 */
public class EmpresaFinanceiroTO extends SuperTO {

    private Integer codigo;
    private Integer codigoFinanceiro;
    private String nomeFantasia;
    private String razaoSocial;
    private String endereco;
    private String cidade;
    private String estado;
    private String telefone;
    private String ddd;
    private String email;
    private String cnpj;
    private String obs;
    private String pais;
    private String nomeResumo;
    private String contato;
    private String chaveZw;
    private String fotoKey;
    private Integer redeempresa_id;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public String getNomeFantasia() {
        return nomeFantasia;
    }

    public void setNomeFantasia(String nomeFantasia) {
        this.nomeFantasia = nomeFantasia;
    }

    public String getRazaoSocial() {
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getDdd() {
        return ddd;
    }

    public void setDdd(String ddd) {
        this.ddd = ddd;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getNomeResumo() {
        return nomeResumo;
    }

    public void setNomeResumo(String nomeResumo) {
        this.nomeResumo = nomeResumo;
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public String getChaveZw() {
        return chaveZw;
    }

    public void setChaveZw(String chaveZw) {
        this.chaveZw = chaveZw;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public Integer getRedeempresa_id() {
        return redeempresa_id;
    }

    public void setRedeempresa_id(Integer redeempresa_id) {
        this.redeempresa_id = redeempresa_id;
    }
}

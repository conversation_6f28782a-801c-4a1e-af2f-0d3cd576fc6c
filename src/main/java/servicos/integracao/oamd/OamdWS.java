
package servicos.integracao.oamd;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "OamdWS", targetNamespace = "http://ws.pacto.com.br/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface OamdWS {


    /**
     * 
     * @param agrupador
     * @param dados
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarDadosGame", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.GravarDadosGame")
    @ResponseWrapper(localName = "gravarDadosGameResponse", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.GravarDadosGameResponse")
    public String gravarDadosGame(
        @WebParam(name = "agrupador", targetNamespace = "")
        String agrupador,
        @WebParam(name = "dados", targetNamespace = "")
        String dados);

    /**
     * 
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterDadosGame", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ObterDadosGame")
    @ResponseWrapper(localName = "obterDadosGameResponse", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ObterDadosGameResponse")
    public String obterDadosGame();

    /**
     * 
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterFeeds", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ObterFeeds")
    @ResponseWrapper(localName = "obterFeedsResponse", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ObterFeedsResponse")
    public String obterFeeds();

    /**
     * 
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterPaginaInicial", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ObterPaginaInicial")
    @ResponseWrapper(localName = "obterPaginaInicialResponse", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ObterPaginaInicialResponse")
    public String obterPaginaInicial();

    /**
     * 
     * @param dados
     */
    @WebMethod
    @RequestWrapper(localName = "gravarFeedBack", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.GravarFeedBack")
    @ResponseWrapper(localName = "gravarFeedBackResponse", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.GravarFeedBackResponse")
    public void gravarFeedBack(
        @WebParam(name = "dados", targetNamespace = "")
        String dados);

    /**
     * 
     * @param username
     * @param senha
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarUserOamd", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ValidarUserOamd")
    @ResponseWrapper(localName = "validarUserOamdResponse", targetNamespace = "http://ws.pacto.com.br/", className = "servicos.integracao.oamd.ValidarUserOamdResponse")
    public String validarUserOamd(
        @WebParam(name = "username", targetNamespace = "")
        String username,
        @WebParam(name = "senha", targetNamespace = "")
        String senha);

}

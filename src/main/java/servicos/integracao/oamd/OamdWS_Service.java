
package servicos.integracao.oamd;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "OamdWS", targetNamespace = "http://ws.pacto.com.br/", wsdlLocation = "https://app.pactosolucoes.com.br/oamd/OamdWS?wsdl")
public class OamdWS_Service
    extends Service
{

    private final static URL OAMDWS_WSDL_LOCATION;
    private final static WebServiceException OAMDWS_EXCEPTION;
    private final static QName OAMDWS_QNAME = new QName("http://ws.pacto.com.br/", "OamdWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://app.pactosolucoes.com.br/oamd/OamdWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        OAMDWS_WSDL_LOCATION = url;
        OAMDWS_EXCEPTION = e;
    }

    public OamdWS_Service() {
        super(__getWsdlLocation(), OAMDWS_QNAME);
    }

    public OamdWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns OamdWS
     */
    @WebEndpoint(name = "OamdWSPort")
    public OamdWS getOamdWSPort() {
        return super.getPort(new QName("http://ws.pacto.com.br/", "OamdWSPort"), OamdWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OamdWS
     */
    @WebEndpoint(name = "OamdWSPort")
    public OamdWS getOamdWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://ws.pacto.com.br/", "OamdWSPort"), OamdWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (OAMDWS_EXCEPTION!= null) {
            throw OAMDWS_EXCEPTION;
        }
        return OAMDWS_WSDL_LOCATION;
    }

}

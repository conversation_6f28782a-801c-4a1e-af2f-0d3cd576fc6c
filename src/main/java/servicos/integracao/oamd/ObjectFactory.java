
package servicos.integracao.oamd;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.oamd package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ObterFeeds_QNAME = new QName("http://ws.pacto.com.br/", "obterFeeds");
    private final static QName _ValidarUserOamd_QNAME = new QName("http://ws.pacto.com.br/", "validarUserOamd");
    private final static QName _ObterDadosGame_QNAME = new QName("http://ws.pacto.com.br/", "obterDadosGame");
    private final static QName _ObterPaginaInicialResponse_QNAME = new QName("http://ws.pacto.com.br/", "obterPaginaInicialResponse");
    private final static QName _GravarFeedBackResponse_QNAME = new QName("http://ws.pacto.com.br/", "gravarFeedBackResponse");
    private final static QName _ValidarUserOamdResponse_QNAME = new QName("http://ws.pacto.com.br/", "validarUserOamdResponse");
    private final static QName _ObterDadosGameResponse_QNAME = new QName("http://ws.pacto.com.br/", "obterDadosGameResponse");
    private final static QName _ObterFeedsResponse_QNAME = new QName("http://ws.pacto.com.br/", "obterFeedsResponse");
    private final static QName _GravarFeedBack_QNAME = new QName("http://ws.pacto.com.br/", "gravarFeedBack");
    private final static QName _ObterPaginaInicial_QNAME = new QName("http://ws.pacto.com.br/", "obterPaginaInicial");
    private final static QName _GravarDadosGameResponse_QNAME = new QName("http://ws.pacto.com.br/", "gravarDadosGameResponse");
    private final static QName _GravarDadosGame_QNAME = new QName("http://ws.pacto.com.br/", "gravarDadosGame");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.oamd
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ValidarUserOamd }
     * 
     */
    public ValidarUserOamd createValidarUserOamd() {
        return new ValidarUserOamd();
    }

    /**
     * Create an instance of {@link ObterFeeds }
     * 
     */
    public ObterFeeds createObterFeeds() {
        return new ObterFeeds();
    }

    /**
     * Create an instance of {@link ObterFeedsResponse }
     * 
     */
    public ObterFeedsResponse createObterFeedsResponse() {
        return new ObterFeedsResponse();
    }

    /**
     * Create an instance of {@link ObterDadosGameResponse }
     * 
     */
    public ObterDadosGameResponse createObterDadosGameResponse() {
        return new ObterDadosGameResponse();
    }

    /**
     * Create an instance of {@link ObterPaginaInicialResponse }
     * 
     */
    public ObterPaginaInicialResponse createObterPaginaInicialResponse() {
        return new ObterPaginaInicialResponse();
    }

    /**
     * Create an instance of {@link ObterDadosGame }
     * 
     */
    public ObterDadosGame createObterDadosGame() {
        return new ObterDadosGame();
    }

    /**
     * Create an instance of {@link ValidarUserOamdResponse }
     * 
     */
    public ValidarUserOamdResponse createValidarUserOamdResponse() {
        return new ValidarUserOamdResponse();
    }

    /**
     * Create an instance of {@link GravarFeedBackResponse }
     * 
     */
    public GravarFeedBackResponse createGravarFeedBackResponse() {
        return new GravarFeedBackResponse();
    }

    /**
     * Create an instance of {@link ObterPaginaInicial }
     * 
     */
    public ObterPaginaInicial createObterPaginaInicial() {
        return new ObterPaginaInicial();
    }

    /**
     * Create an instance of {@link GravarFeedBack }
     * 
     */
    public GravarFeedBack createGravarFeedBack() {
        return new GravarFeedBack();
    }

    /**
     * Create an instance of {@link GravarDadosGame }
     * 
     */
    public GravarDadosGame createGravarDadosGame() {
        return new GravarDadosGame();
    }

    /**
     * Create an instance of {@link GravarDadosGameResponse }
     * 
     */
    public GravarDadosGameResponse createGravarDadosGameResponse() {
        return new GravarDadosGameResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterFeeds }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "obterFeeds")
    public JAXBElement<ObterFeeds> createObterFeeds(ObterFeeds value) {
        return new JAXBElement<ObterFeeds>(_ObterFeeds_QNAME, ObterFeeds.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUserOamd }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "validarUserOamd")
    public JAXBElement<ValidarUserOamd> createValidarUserOamd(ValidarUserOamd value) {
        return new JAXBElement<ValidarUserOamd>(_ValidarUserOamd_QNAME, ValidarUserOamd.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDadosGame }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "obterDadosGame")
    public JAXBElement<ObterDadosGame> createObterDadosGame(ObterDadosGame value) {
        return new JAXBElement<ObterDadosGame>(_ObterDadosGame_QNAME, ObterDadosGame.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterPaginaInicialResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "obterPaginaInicialResponse")
    public JAXBElement<ObterPaginaInicialResponse> createObterPaginaInicialResponse(ObterPaginaInicialResponse value) {
        return new JAXBElement<ObterPaginaInicialResponse>(_ObterPaginaInicialResponse_QNAME, ObterPaginaInicialResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarFeedBackResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "gravarFeedBackResponse")
    public JAXBElement<GravarFeedBackResponse> createGravarFeedBackResponse(GravarFeedBackResponse value) {
        return new JAXBElement<GravarFeedBackResponse>(_GravarFeedBackResponse_QNAME, GravarFeedBackResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ValidarUserOamdResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "validarUserOamdResponse")
    public JAXBElement<ValidarUserOamdResponse> createValidarUserOamdResponse(ValidarUserOamdResponse value) {
        return new JAXBElement<ValidarUserOamdResponse>(_ValidarUserOamdResponse_QNAME, ValidarUserOamdResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterDadosGameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "obterDadosGameResponse")
    public JAXBElement<ObterDadosGameResponse> createObterDadosGameResponse(ObterDadosGameResponse value) {
        return new JAXBElement<ObterDadosGameResponse>(_ObterDadosGameResponse_QNAME, ObterDadosGameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterFeedsResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "obterFeedsResponse")
    public JAXBElement<ObterFeedsResponse> createObterFeedsResponse(ObterFeedsResponse value) {
        return new JAXBElement<ObterFeedsResponse>(_ObterFeedsResponse_QNAME, ObterFeedsResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarFeedBack }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "gravarFeedBack")
    public JAXBElement<GravarFeedBack> createGravarFeedBack(GravarFeedBack value) {
        return new JAXBElement<GravarFeedBack>(_GravarFeedBack_QNAME, GravarFeedBack.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ObterPaginaInicial }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "obterPaginaInicial")
    public JAXBElement<ObterPaginaInicial> createObterPaginaInicial(ObterPaginaInicial value) {
        return new JAXBElement<ObterPaginaInicial>(_ObterPaginaInicial_QNAME, ObterPaginaInicial.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarDadosGameResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "gravarDadosGameResponse")
    public JAXBElement<GravarDadosGameResponse> createGravarDadosGameResponse(GravarDadosGameResponse value) {
        return new JAXBElement<GravarDadosGameResponse>(_GravarDadosGameResponse_QNAME, GravarDadosGameResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GravarDadosGame }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://ws.pacto.com.br/", name = "gravarDadosGame")
    public JAXBElement<GravarDadosGame> createGravarDadosGame(GravarDadosGame value) {
        return new JAXBElement<GravarDadosGame>(_GravarDadosGame_QNAME, GravarDadosGame.class, null, value);
    }

}

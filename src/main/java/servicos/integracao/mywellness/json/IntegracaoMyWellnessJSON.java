package servicos.integracao.mywellness.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class IntegracaoMyWellnessJSON extends SuperJSON {
    private String operacao = "ADD";
    private String chave;
    private String apiKey;
    private String user;
    private String password;
    private String facilityUrl;
    private String origem;
    private String tokenPessoa;
    private PessoaMyWellnessJSON pessoa;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFacilityUrl() {
        return facilityUrl;
    }

    public void setFacilityUrl(String facilityUrl) {
        this.facilityUrl = facilityUrl;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getTokenPessoa() {
        return tokenPessoa;
    }

    public void setTokenPessoa(String tokenPessoa) {
        this.tokenPessoa = tokenPessoa;
    }

    public PessoaMyWellnessJSON getPessoa() {
        if(pessoa == null) {
            pessoa = new PessoaMyWellnessJSON();
        }
        return pessoa;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public void setPessoa(PessoaMyWellnessJSON pessoa) {
        this.pessoa = pessoa;
    }
}

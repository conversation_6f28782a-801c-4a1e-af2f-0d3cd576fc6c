package servicos.integracao.mywellness.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

public class PessoaMyWellnessJSON  extends SuperJSON {
    private String nome;
    private String genero;
    private String dataNascimento;
    private String cidade;
    private String cpf;
    private Integer empresa;
    private String estado;
    private String dthrentrada;
    private String nomeplano;
    private String datalancamentocontrato;
    private String datavigenciade;
    private String datavigenciaate;
    private Integer numeromesescontrato;
    private Integer codPessoa;
    private String email;
    private String endereco;
    private String cep;
    private String vinculos;
    private String telefone;
    private String celular;
    private Integer codColaborador;
    private String tipocolaborador;
    private String urlFoto;
    private String situacaoContrato;
    private String datavigenciadeanterior;
    private String datavigenciaateanterior;
    private boolean enviarGrupos = true;


    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getGenero() {
        return genero;
    }

    public void setGenero(String genero) {
        this.genero = genero;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public  Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getDthrentrada() {
        return dthrentrada;
    }

    public void setDthrentrada(String dthrentrada) {
        this.dthrentrada = dthrentrada;
    }

    public String getNomeplano() {
        return nomeplano;
    }

    public void setNomeplano(String nomeplano) {
        this.nomeplano = nomeplano;
    }

    public String getDatalancamentocontrato() {
        return datalancamentocontrato;
    }

    public void setDatalancamentocontrato(String datalancamentocontrato) {
        this.datalancamentocontrato = datalancamentocontrato;
    }

    public String getDatavigenciade() {
        return datavigenciade;
    }

    public void setDatavigenciade(String datavigenciade) {
        this.datavigenciade = datavigenciade;
    }

    public String getDatavigenciaate() {
        return datavigenciaate;
    }

    public void setDatavigenciaate(String datavigenciaate) {
        this.datavigenciaate = datavigenciaate;
    }

    public Integer getNumeromesescontrato() {
        return numeromesescontrato;
    }

    public void setNumeromesescontrato(Integer numeromesescontrato) {
        this.numeromesescontrato = numeromesescontrato;
    }

    public Integer getCodPessoa() {
        return codPessoa;
    }

    public void setCodPessoa(Integer codPessoa) {
        this.codPessoa = codPessoa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getVinculos() {
        return vinculos;
    }

    public void setVinculos(String vinculos) {
        this.vinculos = vinculos;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }

    public Integer getCodColaborador() {
        return codColaborador;
    }

    public void setCodColaborador(Integer codColaborador) {
        this.codColaborador = codColaborador;
    }

    public String getTipocolaborador() {
        return tipocolaborador;
    }

    public void setTipocolaborador(String tipocolaborador) {
        this.tipocolaborador = tipocolaborador;
    }

    public String getUrlFoto() {
        return urlFoto;
    }

    public void setUrlFoto(String urlFoto) {
        this.urlFoto = urlFoto;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getDatavigenciadeanterior() {
        return datavigenciadeanterior;
    }

    public void setDatavigenciadeanterior(String datavigenciadeanterior) {
        this.datavigenciadeanterior = datavigenciadeanterior;
    }

    public String getDatavigenciaateanterior() {
        return datavigenciaateanterior;
    }

    public void setDatavigenciaateanterior(String datavigenciaateanterior) {
        this.datavigenciaateanterior = datavigenciaateanterior;
    }

    public boolean isEnviarGrupos() {
        return enviarGrupos;
    }

    public void setEnviarGrupos(boolean enviarGrupos) {
        this.enviarGrupos = enviarGrupos;
    }
}


package servicos.integracao.adm.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "obterEmpresasUsuario", namespace = "http://adm.integracao.servicos/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "obterEmpresasUsuario", namespace = "http://adm.integracao.servicos/", propOrder = {
    "key",
    "usuarioZW"
})
public class ObterEmpresasUsuario {

    @XmlElement(name = "key", namespace = "")
    private String key;
    @XmlElement(name = "usuarioZW", namespace = "")
    private Integer usuarioZW;

    /**
     * 
     * @return
     *     returns String
     */
    public String getKey() {
        return this.key;
    }

    /**
     * 
     * @param key
     *     the value for the key property
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 
     * @return
     *     returns Integer
     */
    public Integer getUsuarioZW() {
        return this.usuarioZW;
    }

    /**
     * 
     * @param usuarioZW
     *     the value for the usuarioZW property
     */
    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

}

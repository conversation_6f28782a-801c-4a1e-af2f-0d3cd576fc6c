
package servicos.integracao.adm.jaxws;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import servicos.integracao.adm.beans.EmpresaWS;

@XmlRootElement(name = "obterEmpresasUsuarioResponse", namespace = "http://adm.integracao.servicos/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "obterEmpresasUsuarioResponse", namespace = "http://adm.integracao.servicos/")
public class ObterEmpresasUsuarioResponse {

    @XmlElement(name = "return", namespace = "")
    private List<EmpresaWS> _return;

    /**
     * 
     * @return
     *     returns List<EmpresaWS>
     */
    public List<EmpresaWS> getReturn() {
        return this._return;
    }

    /**
     * 
     * @param _return
     *     the value for the _return property
     */
    public void setReturn(List<EmpresaWS> _return) {
        this._return = _return;
    }

}

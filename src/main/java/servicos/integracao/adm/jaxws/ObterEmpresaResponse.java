
package servicos.integracao.adm.jaxws;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import br.com.pactosolucoes.oamd.bean.EmpresaTO;

@XmlRootElement(name = "obterEmpresaResponse", namespace = "http://adm.integracao.servicos/")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "obterEmpresaResponse", namespace = "http://adm.integracao.servicos/")
public class ObterEmpresaResponse {

    @XmlElement(name = "return", namespace = "")
    private EmpresaTO _return;

    /**
     * 
     * @return
     *     returns EmpresaTO
     */
    public EmpresaTO getReturn() {
        return this._return;
    }

    /**
     * 
     * @param _return
     *     the value for the _return property
     */
    public void setReturn(EmpresaTO _return) {
        this._return = _return;
    }

}

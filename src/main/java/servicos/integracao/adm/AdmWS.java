package servicos.integracao.adm;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.bean.EmpresaTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.integracao.adm.beans.EmpresaWS;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
@WebService(name = "AdmWS", serviceName = "AdmWS")
public class AdmWS {

    public static final String RESULTADO = "resultado";
    public static final String FALHA = "falha";
    public static final String SUCESSO = "sucesso: operacao realizada com sucesso";

    @WebMethod(operationName = "obterEmpresa")
    public EmpresaTO obterEmpresa(@WebParam(name = "key") String key) {
        try {
            Uteis.logar(null, "====START INTEGRAÇÃO obterEmpresa ==== " + key);
            EmpresaTO emp = DAO.buscarEmpresa(key);
            Uteis.logar(null, "====END INTEGRAÇÃO obterEmpresa ==== " + emp.toString());
            return emp;
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    @WebMethod(operationName = "obterEmpresasUsuario")
    public List<EmpresaWS> obterEmpresasUsuario(
            @WebParam(name = "key") String key,
            @WebParam(name = "usuarioZW") Integer usuarioZW) {
        try {
            Uteis.logar(null, "====START INTEGRAÇÃO obterEmpresasUsuario ==== " + key);
            if (!DaoAuxiliar.retornarAcessoControle(key).
                    getUsuarioDao().consultarUsuarioAtivo(usuarioZW)) {
                return new ArrayList<>();
            }

            List<UsuarioPerfilAcessoVO> listaPerfisUsuario = DaoAuxiliar.retornarAcessoControle(key).
                    getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(usuarioZW,
                    Uteis.NIVELMONTARDADOS_MINIMOS);
            List<EmpresaWS> empresas = new ArrayList<>();
            for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                EmpresaVO emp = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(
                        perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ColaboradorVO colaboradorVO = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarPorUsuarioEmpresaComBasePessoa(usuarioZW, emp.getCodigo());
                EmpresaWS empWS = new EmpresaWS(emp);
                if(colaboradorVO != null){
                    empWS.setCodigoColaborador(colaboradorVO.getCodigo());
                }
                empWS.setDescricaoPerfil(perfil.getPerfilAcesso().getNome());
                empresas.add(empWS);
            }

            if (empresas.isEmpty() && listaPerfisUsuario != null && listaPerfisUsuario.isEmpty() && usuarioZW != null && usuarioZW > 0) { // Usuario com acesso só no treino e não é independente.
                ColaboradorVO colaboradorVO = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarPorCodigoUsuario(usuarioZW, 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                boolean temTipo = colaboradorVO.getListaTipoColaboradorVOs().stream().anyMatch(
                        tipo -> (Objects.equals(TipoColaboradorEnum.PROFESSOR.getSigla(), tipo.getDescricao()) || Objects.equals(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla(), tipo.getDescricao()))
                );
                if (temTipo) {
                    EmpresaVO emp = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(
                            colaboradorVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    EmpresaWS empWS = new EmpresaWS(emp);
                    if (colaboradorVO != null) {
                        empWS.setCodigoColaborador(colaboradorVO.getCodigo());
                    }
                    empWS.setDescricaoPerfil("SEM ACESSO AO ADM - SOMENTE TREINO");
                    empresas.add(empWS);
                }
            }
            Uteis.logar(null, "====END INTEGRAÇÃO obterEmpresasUsuario ==== " + empresas.toString());
            return empresas;
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    @WebMethod(operationName = "obterEmpresas")
    public List<EmpresaWS> obterEmpresas(
            @WebParam(name = "key") String key) {
        if (UteisValidacao.emptyString(key)) {
            Uteis.logar("AdmWS.obterEmpresas Ignorada empresa com chave vazia");
            return null;
        }
        return obterEmpresasComSituacao(key, null);
    }

    @WebMethod(operationName = "obterEmpresasComSituacao")
    public List<EmpresaWS> obterEmpresasComSituacao(
            @WebParam(name = "key") String key,
            @WebParam(name = "ativa") Boolean ativa) {
        try {
            Uteis.logar(null, "====START INTEGRAÇÃO obterEmpresas ==== " + key);
            List<EmpresaWS> empresas = new ArrayList<EmpresaWS>();
            List<EmpresaVO> listEmpresas = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarEmpresas(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (EmpresaVO empresa : listEmpresas) {
                if (ativa == null
                        || (ativa && empresa.isAtiva())
                        || (!ativa && !empresa.isAtiva())) {
                    empresas.add(new EmpresaWS(empresa));
                }
            }
            Uteis.logar(null, "====END INTEGRAÇÃO obterEmpresas ==== " + empresas.toString());
            return empresas;
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    @WebMethod(operationName = "obterUsuarios")
    public List<UsuarioTO> obterUsuarios(
            @WebParam(name = "key") String key) {
        try {
            Uteis.logar(null, "====START INTEGRAÇÃO obterUsuarios ==== " + key);
            List<UsuarioVO> usuarios = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().
                    consultarTodosAtivosSemAdministrador(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            boolean fotosNuvem = PropsService.isTrue(PropsService.fotosParaNuvem);
            List<UsuarioTO> lista = new ArrayList();
            for (UsuarioVO u : usuarios) {
                UsuarioTO uTO = new UsuarioTO(u.getNome(), u.getUsername(), u.getCodigo(),
                        u.getColaboradorVO().getPessoa().getCodigo(),
                        false, null, null, null, null, null, null);
                if (fotosNuvem) {
                    uTO.setUrlFotosNuvem(PropsService.getPropertyValue(PropsService.urlFotosNuvem));
                    uTO.setUrlFoto(u.getColaboradorVO().getPessoa().getUrlFoto());
                    uTO.setTypeMidiasService(PropsService.getPropertyValue(PropsService.typeMidiasService));
                }
                lista.add(uTO);
            }
            Uteis.logar(null, "====END INTEGRAÇÃO obterUsuarios ==== " + lista.toString());
            return lista;
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    @WebMethod(operationName = "obterFoto")
    public byte[] obterFoto(@WebParam(name = "key") String key,
            @WebParam(name = "codEmpresa") Integer codEmpresa) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().obterFoto(key, codEmpresa, MidiaEntidadeEnum.FOTO_EMPRESA);
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    @WebMethod(operationName = "alterarCreditoDCC")
    public Integer alterarCreditoDCC(@WebParam(name = "key") String key,
            @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
            @WebParam(name = "quantidade") int quantidade) {
        try {
            return DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().alterarSaldo(codigoEmpresa, quantidade, "");
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    @WebMethod(operationName = "gravarPrePagoDCCLancarAgendamentoFinan")
    public String gravarPrePagoDCCLancarAgendamentoFinan(@WebParam(name = "key") String key,
                                                         @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
                                                         @WebParam(name = "quantidade") int quantidade,
                                                         @WebParam(name = "qtdParcelas") Integer qtdParcelas,
                                                         @WebParam(name = "valorTotal") Double valorTotal,
                                                         @WebParam(name = "gerarNota") boolean gerarNota,
                                                         @WebParam(name = "nomeUsuarioOAMD") String nomeUsuarioOAMD,
                                                         @WebParam(name = "justificativa") String justificativa,
                                                         @WebParam(name = "gerarCobrancaFinanceiro") boolean gerarCobrancaFinanceiro) {

        try {
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            return DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().processarClientePrePagoCobrancaPactoLancarContaFinanceiro(key, codigoEmpresa, quantidade, qtdParcelas, valorTotal, gerarNota, nomeUsuarioOAMD, justificativa, gerarCobrancaFinanceiro, empresaVO.getTipoCobrancaPacto());
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "gravarPosPagoDCCLancarAgendamentoFinan")
    public String gravarPosPagoDCCLancarAgendamentoFinan(@WebParam(name = "key") String key,
                                                         @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
                                                         @WebParam(name = "tipoCobrancaPacto") Integer tipoCobrancaPacto,
                                                         @WebParam(name = "qtdCredito") int qtdCredito,
                                                         @WebParam(name = "qtdParcelas") Integer qtdParcelas,
                                                         @WebParam(name = "valorTotal") Double valorTotal,
                                                         @WebParam(name = "gerarNota") boolean gerarNota,
                                                         @WebParam(name = "nomeUsuarioOAMD") String nomeUsuarioOAMD,
                                                         @WebParam(name = "justificativa") String justificativa,
                                                         @WebParam(name = "gerarCobrancaFinanceiro") boolean gerarCobrancaFinanceiro) {

        try {
            return DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().processarClientePosPagoCobrancaPactoLancarContaFinanceiro(key, codigoEmpresa, tipoCobrancaPacto, qtdParcelas, valorTotal, 0.0, gerarNota, nomeUsuarioOAMD, justificativa, gerarCobrancaFinanceiro, false);
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @Deprecated
    @WebMethod(operationName = "alterarInformacoesEmpresaCobrancaPacto")
    public String alterarInformacoesEmpresaCobrancaPacto(@WebParam(name = "key") String key,
                                     @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
                                     @WebParam(name = "tipoCobrancaPacto") Integer tipoCobrancaPacto,
                                     @WebParam(name = "gerarCobrancaAutomaticaPacto") boolean gerarCobrancaAutomaticaPacto,
                                     @WebParam(name = "qtdDiasFechamentoCobrancaPacto") Integer qtdDiasFechamentoCobrancaPacto,
                                     @WebParam(name = "valorCreditoPacto") Double valorCreditoPacto,
                                     @WebParam(name = "gerarNotaFiscalCobrancaPacto") boolean gerarNotaFiscalCobrancaPacto,
                                     @WebParam(name = "qtdParcelasCobrancaPacto") Integer qtdParcelasCobrancaPacto,
                                     @WebParam(name = "qtdCreditoRenovarPrePagoCobrancaPacto") Integer qtdCreditoRenovarPrePagoCobrancaPacto,
                                     @WebParam(name = "nomeClienteCobrancaPacto") String nomeClienteCobrancaPacto,
                                     @WebParam(name = "emailClienteCobrancaPacto") String emailClienteCobrancaPacto,
                                     @WebParam(name = "celularClienteCobrancaPacto") String celularClienteCobrancaPacto,
                                     @WebParam(name = "nomeUsuarioOAMD") String nomeUsuarioOAMD) {

        try {
//            String retorno = DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().alterarInformacoesEmpresaCobrancaPacto(codigoEmpresa, tipoCobrancaPacto, gerarCobrancaAutomaticaPacto,
//                    qtdDiasFechamentoCobrancaPacto, valorCreditoPacto, gerarNotaFiscalCobrancaPacto, qtdParcelasCobrancaPacto, qtdCreditoRenovarPrePagoCobrancaPacto, nomeUsuarioOAMD, 0, false);
            if (true) {
                throw new Exception("Método Deprecated");
            }
            return "";
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarQtdCreditoUtilizado")
    public String consultarQtdCreditoUtilizado(@WebParam(name = "key") String key,
                                         @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
                                         @WebParam(name = "tipoCobrancaPacto") Integer tipoCobrancaPacto) {

        try {
            return DaoAuxiliar.retornarAcessoControle(key).getZwFacade().getCreditoDCC().consultarQtdCreditoUtilizado(codigoEmpresa, tipoCobrancaPacto).toString();
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "consultarFavorecidoFinanceiro")
    public String consultarFavorecidoFinanceiro(@WebParam(name = "key") String key,
                                               @WebParam(name = "cpf_cnpj") String cpf_cnpj) {

        try {
            String retorno = DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().consultarFavorecidoFinanceiro(cpf_cnpj);
            if (!retorno.startsWith("Sucesso")) {
                throw new Exception(retorno);
            }
            return retorno;
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "lancarContaFinanceiro")
    public String lancarContaFinanceiro(@WebParam(name = "key") String key,
                                        @WebParam(name = "codigoEmpresa") Integer codigoEmpresa,
                                        @WebParam(name = "qtdCredito") Integer qtdCredito,
                                        @WebParam(name = "qtdParcelas") Integer qtdParcelas,
                                        @WebParam(name = "valorTotal") Double valorTotal,
                                        @WebParam(name = "gerarNota") Boolean gerarNota) {

        try {
            DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().lancarContaFinanceiroPacto(key, codigoEmpresa, qtdCredito, qtdParcelas, valorTotal, gerarNota, 0, null, null, null);
            return "OK";
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "desvincularNotaFiscalEmitida")
    public String desvincularNotaFiscalEmitida(@WebParam(name = "key") String key,
                                               @WebParam(name = "json") String json) {
        try {

            JSONObject jsonObject = new JSONObject(json);
            String listaIdLote = jsonObject.getString("listaIdLote");
            String usuarioOAMD = jsonObject.getString("usuarioOAMD");
            boolean nfce = false;
            try {
                nfce = jsonObject.getBoolean("nfce");
            } catch (Exception ignored) {
            }

            UsuarioVO usuarioVO = DaoAuxiliar.retornarAcessoControle(key).getUsuarioDao().consultarPorUsername("admin", Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);

            if (UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Usuário admin não encontrado!");
            }

            usuarioVO.setUserOamd(usuarioOAMD);

            Integer qtdExcluida = 0;
            Integer qtdNaoEncontrada = 0;
            List<String> listaLote = Arrays.asList(listaIdLote.split(","));

            if (nfce) { //NFCE

                for (String lote : listaLote) {
                    Integer codigoLote;
                    try {
                        lote = lote.replace(" ", "");
                        codigoLote = Integer.parseInt(lote);

                        NotaFiscalConsumidorEletronicaVO notaVO = DaoAuxiliar.retornarAcessoControle(key).getNotaFiscalConsumidorEletronica().consultarPorIdNFCe(codigoLote, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (!UteisValidacao.emptyNumber(notaVO.getCodigo())) {
                            DaoAuxiliar.retornarAcessoControle(key).getNotaFiscalConsumidorEletronica().excluirComLog(notaVO, usuarioVO, ProcessoAjusteGeralEnum.EXCLUIR_NFCe);
                            qtdExcluida++;
                        } else {
                            qtdNaoEncontrada++;
                        }
                    } catch (Exception ignored) {
                    }
                }

            } else { //NFSE

                for (String lote : listaLote) {
                    Integer codigoLote;
                    try {
                        codigoLote = Integer.parseInt(lote);

                        NFSeEmitidaVO nfSeEmitidaVO = DaoAuxiliar.retornarAcessoControle(key).getNfSeEmitida().consultaPorRPS(codigoLote);
                        if (nfSeEmitidaVO != null) {
                            DaoAuxiliar.retornarAcessoControle(key).getNfSeEmitida().excluirComLog(nfSeEmitidaVO, usuarioVO, ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL);
                            qtdExcluida++;
                        } else {
                            qtdNaoEncontrada++;
                        }
                    } catch (Exception ignored) {
                    }
                }
            }

            JSONObject jsonRetorno = new JSONObject();
            jsonRetorno.put("qtdSolicitada", listaLote.size());
            jsonRetorno.put("qtdExcluida", qtdExcluida);
            jsonRetorno.put("qtdNaoEncontrada", qtdNaoEncontrada);
            return jsonRetorno.toString();
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "executarProcessoLatitudeLongitude")
    public String executarProcessoLatitudeLongitude(@WebParam(name = "chave") String chave){
        return "ERRO: NO SUPORTADO MAIS";
    }

    @WebMethod(operationName = "utilizarMoviDesk")
    public String utilizarMoviDesk(@WebParam(name = "chave") final String chave,
                                   @WebParam(name = "deveUtilizarMoviDesk") final String deveUtilizarMoviDesk){
        try {
            final JSONObject jsonObject = new JSONObject();
            jsonObject.put("chave", chave);

            if (DAO.persistirValorEmpresa(chave, DAO.EmpresaColunas.UTILIZAR_MOVI_DESK, deveUtilizarMoviDesk)) {
                jsonObject.put(RESULTADO, SUCESSO);
                return jsonObject.toString();
            }

            jsonObject.put(RESULTADO, FALHA);
            return jsonObject.toString();
        } catch (Exception ex){
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "utilizarChatMoviDesk")
    public String utilizarChatMoviDesk(@WebParam(name = "chave") final String chave,
                                       @WebParam(name = "deveUtilizarChatMoviDesk") final String deveUtilizarChatMoviDesk) {
        try {
            final JSONObject jsonObject = new JSONObject();
            jsonObject.put("chave", chave);

            if (DAO.persistirValorEmpresa(chave, DAO.EmpresaColunas.UTILIZAR_CHAT_MOVI_DESK, deveUtilizarChatMoviDesk)) {
                jsonObject.put(RESULTADO, SUCESSO);
                return jsonObject.toString();
            }

            jsonObject.put(RESULTADO, FALHA);
            return jsonObject.toString();
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "atualizarDadosEmpresa")
    public String atualizarDadosEmpresa(@WebParam(name = "chave") final String chave,
                                        @WebParam(name = "corrigirProtocolo") final String corrigirProtocolo) {
        try {
            final JSONObject jsonObject = new JSONObject();
            jsonObject.put("chave", chave);

            final Boolean corrigirProtocoloBoolean = Boolean.valueOf(corrigirProtocolo);
            final Boolean sistemaCorrigeProtocolo = Boolean.valueOf(DAO.getPropertyValue(chave, PropsService.CORRIGIR_PROTOCOLO));
            if (corrigirProtocoloBoolean == sistemaCorrigeProtocolo) {
                jsonObject.put(RESULTADO, SUCESSO);
                return jsonObject.toString();
            }

            if (DAO.persistirValorEmpresa(chave, DAO.EmpresaColunas.CORRIGIR_PROTOCOLO, corrigirProtocolo)) {
                jsonObject.put(RESULTADO, SUCESSO);
                return jsonObject.toString();
            }

            jsonObject.put(RESULTADO, FALHA);
            return jsonObject.toString();
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "alterarGrupoChatMovidesk")
    public String alterarGrupoChatMovidesk(@WebParam(name = "chave") final String chave,
                                           @WebParam(name = "codigoGrupo") final String codigoGrupo) {
        try {
            final JSONObject jsonObject = new JSONObject();
            jsonObject.put("chave", chave);

            if (DAO.persistirValorEmpresa(chave, DAO.EmpresaColunas.GRUPO_CHAT_MOVIDESK, codigoGrupo)) {
                jsonObject.put(RESULTADO, SUCESSO);
                return jsonObject.toString();
            }

            jsonObject.put(RESULTADO, FALHA);
            return jsonObject.toString();
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }

    @WebMethod(operationName = "ajustarEmpresaParaIniciarPosPago")
    public String ajustarEmpresaParaIniciarPosPago(@WebParam(name = "key") String key,
                                                   @WebParam(name = "json") String json) {

        try {

            JSONObject jsonObject = new JSONObject(json);
            Integer codigoEmpresa = jsonObject.getInt("empresa");
            String nomeUsuarioOAMD = jsonObject.getString("nomeUsuarioOAMD");
            Date dataAlteracao = Uteis.getDate(jsonObject.getString("dataAlteracao"), "dd/MM/yyyy HH:mm:ss");

            String retorno = DaoAuxiliar.retornarAcessoControle(key).getCreditoDCC().ajustarEmpresaParaIniciarPosPago(codigoEmpresa, dataAlteracao, nomeUsuarioOAMD);
            if (!retorno.toUpperCase().startsWith("SUCESSO")) {
                throw new Exception(retorno);
            }
            return retorno;
        } catch (Exception ex) {
            Logger.getLogger(AdmWS.class.getName()).log(Level.SEVERE, null, ex);
            return "ERRO: " + ex.getMessage();
        }
    }
}

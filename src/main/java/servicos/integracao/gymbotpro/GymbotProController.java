package servicos.integracao.gymbotpro;

import com.google.gson.Gson;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.crm.ConfiguracaoIntegracaoGymbotProVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.vendas.VendasConfig;
import org.apache.http.HttpHeaders;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.sms.Message;

import java.sql.Connection;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class GymbotProController extends SuperControle {


    private final static String URI_API = getUrlMidiaSocial();  // "https://a47f-186-202-33-242.sa.ngrok.io";
    private final String key;
    private Connection con;
    private MalaDiretaVO malaDiretaVO;
    private PactoPayComunicacaoVO pactoPayComunicacaoVO;

    public GymbotProController(String key, MalaDiretaVO malaDiretaVO, Connection con) {
        this.con = con;
        this.key = key;
        this.malaDiretaVO = malaDiretaVO;
    }

    public GymbotProController(String key, PactoPayComunicacaoVO pactoPayComunicacaoVO, Connection con) {
        this.con = con;
        this.key = key;
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }

    public GymbotProController(String key, Connection con) {
        this.con = con;
        this.key = key;
    }

    private static synchronized List<String> converterParaTemplateParceiro(String template) {
        Matcher matcher = Pattern.compile("(\\{\\{([^}]*)\\}\\})").matcher(template);
        String tags="";
        List<String> lstTags = new ArrayList<>();
        while(matcher.find()) {
            lstTags.add( matcher.group(2).toString());
        }
        return lstTags;
    }

    public String personalizarTag(List<Message> messageList) throws Exception {
        String json;
        VendasConfig vendasConfigDAO;
        List<String> parameters = new ArrayList<>();
        vendasConfigDAO = new VendasConfig(getCon());
        try {

            List<itemGymbotProJson> listJson = new ArrayList<>();

            messageList.forEach(itemTag ->{
                itemGymbotProJson itemJson = new itemGymbotProJson();
                itemJson.setNome((Uteis.formatarNome(Uteis.getPrimeiroNome( itemTag.getNome()))));
                itemJson.setTelefone(itemTag.getNumero());
                String tags[];
                if(itemTag.getMsg().contains(";")) {
                    tags = itemTag.getMsg().split(";");
                    for (String tag : tags) {

                        switch (tag.toString()) {
                            case "TAG_EVENTO":
                                try {
                                    itemJson.setEvento("");
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                break;
                            case "TAG_PAGONLINE":
                                try {
                                    itemJson.setLink(vendasConfigDAO.tagPagamento(key, getMalaDiretaVO().getEmpresa().getCodigo(), itemTag.getCodigoCliente(), true));
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                break;
                            case "TAG_CADCARTAO":
                                try {
                                    itemJson.setLink(vendasConfigDAO.tagPagamento(key, getMalaDiretaVO().getEmpresa().getCodigo(), itemTag.getCodigoCliente(), false));
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                break;
                        }
                    }
                }
                listJson.add(itemJson);
            });
            json = new Gson().toJson(listJson);
        }
        catch (Exception ex){
            throw new RuntimeException(ex);
        }
        return  json;
    }

    public gymbotpro setEnviaTemplate(String token,String BotId, List<Message> messageList) throws Exception {
        gymbotpro templ = new gymbotpro();
        templ.setBotId(BotId);
        templ.setToken(token);

        Timestamp agora = Timestamp.from(Instant.now().atZone( ZoneId.of("America/Sao_Paulo")).toInstant());
        templ.setScheduleDateTime(agora.toString().replace(" ", "T"));
        try {
            gymbotpro cliente = new gymbotpro();
            try {
                templ.setValues(personalizarTag(messageList));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        catch (Exception ex){
            throw new RuntimeException(ex);
        }
        return templ;
    }

    public void gravaHistoricoContato(Map<String,String> body, String retorno, ConfiguracaoIntegracaoGymbotProVO configuracaoIntegracaoGymbotProVO) throws Exception {
        try {
            String msgBotConversa = "\nDisparo de GymBot Pro, \"" + configuracaoIntegracaoGymbotProVO.getDescricao() + "\", retorno: " + retorno;

            MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
            HistoricoContato historicoContatoDAO = new HistoricoContato(con);

            UsuarioVO usuarioVO = new UsuarioVO();
            usuarioVO.setCodigo(Integer.valueOf(body.get("idUsuario")));
            usuarioVO.setNome(body.get("nomeUsuario"));

            malaDiretaVO.setMeioDeEnvio(MeioEnvio.GYMBOT_PRO.getCodigo());
            malaDiretaVO.setRemetente(usuarioVO);
            malaDiretaVO.setContatoAvulso(true);
            historicoContatoDAO.executarGravacaoVindoGymbot(malaDiretaVO,0,Integer.valueOf(body.get("idCliente")), msgBotConversa, configuracaoIntegracaoGymbotProVO.getCodigo());
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public String sendMessage(String key, Integer empresaId, String token, String idFluxo, List<Message> messageList) throws Exception {
        return this.sendMessageGeral(key, empresaId, token, idFluxo, messageList, false);
    }

    public String sendMessageGeral(String key, Integer empresaId, String token, String idFluxo,
                                   List<Message> messageList, boolean excecaoCasoErro) throws Exception {
        String urlgymbotpro = "";
        try {
            gymbotpro messages = setEnviaTemplate(token,idFluxo, messageList);
            RequestHttpService service = new RequestHttpService();
            Map<String, String> params = new HashMap<>();
            params.put("empresaChave", key);
            params.put("empresaId", empresaId.toString());
            params.put("SISTEMA_ZW", "true");
            params.put("Accept-Language", "pt-BR");

            params.put(HttpHeaders.CONTENT_TYPE, "application/json");
            urlgymbotpro = (URI_API + "/v1/fila/gymbotpro");
            String body = new JSONObject(messages).toString();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlgymbotpro, params, null, body, MetodoHttpEnum.POST);
            Uteis.logarDebug("GymBot Pro: respostaHttpDTO: " + new JSONObject(respostaHttpDTO).toString());
            String resp = respostaHttpDTO.getResponse();
            Uteis.logarDebug("GymBot Pro: Resposta: " + resp);
            JSONObject json = new JSONObject(resp);
            if (json.has("message")) {
                if (excecaoCasoErro) {
                    //todo validar resposta quando sucesso
                    String msgRestorno = json.getJSONArray("message").getJSONObject(0).getString("message");
                    if (!msgRestorno.equalsIgnoreCase("ok") &&
                            !msgRestorno.equalsIgnoreCase("Operação realizada com sucesso.")) {
                        throw new Exception(resp);
                    }
                    return resp;
                } else {
                    return json.getJSONArray("message").getJSONObject(0).getString("message");
                }
            } else if (excecaoCasoErro) {
                throw new Exception(resp);
            }
            return "Erro ao disparar GymBot Pro: URL: " + urlgymbotpro;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao disparar GymBot Pro: URL: " + urlgymbotpro);
            throw e;
        }
    }

    public MalaDiretaVO getMalaDiretaVO() {
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public PactoPayComunicacaoVO getPactoPayComunicacaoVO() {
        return pactoPayComunicacaoVO;
    }

    public void setPactoPayComunicacaoVO(PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }
}

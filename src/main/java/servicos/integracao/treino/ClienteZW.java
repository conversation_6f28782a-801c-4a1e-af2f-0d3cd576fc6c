
package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for clienteZW complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="clienteZW">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="alunoParq" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="bairro" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CPF" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cargo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cidade" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UF" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoCliente" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoColaboradorProfessor" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoContrato" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoPessoa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoUltimoContatoCRM" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="colaboradores" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="crossfit" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="dataCadastro" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataFimPeriodoAcesso" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataInicioPeriodoAcesso" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataLancamentoContrato" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataMatricula" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataNascimento" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataRematriculaContrato" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataRenovacaoContrato" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataUltimarematricula" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataUltimoBV" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataUltimoContatoCRM" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataUltimoacesso" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataVencimentoTreino" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataVigenciaAte" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataVigenciaAteAjustada" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dataVigenciaDe" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="descricaoDuracao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="descricoesModalidades" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dia" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="diasAcessoMes2" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoMes3" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoMes4" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoSemana2" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoSemana3" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoSemana4" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoSemanaPassada" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAcessoUltimoMes" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasAssiduidadeUltRematriculaAteHoje" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="diasFaltaSemAcesso" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="duracaoContratoMeses" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="empresausafreepass" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="endereco" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="estadoCivil" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="existeParcVencidaContrato" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="faseAtualCRM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="freePass" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="idade" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="matricula" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="mediaDiasAcesso4Meses" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="mnemonicoDoContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="modalidades" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeConsulta" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeplano" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nrTreinosPrevistos" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="nrTreinosRealizados" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="pesoRisco" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="professorSintetico" type="{http://webservice.pacto.com.br/}professorSintetico" minOccurs="0"/>
 *         &lt;element name="profissao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="responsavelUltimoContatoCRM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="saldoContaCorrenteCliente" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="saldoCreditoTreino" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="sexo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacaoContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacaoContratoOperacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="situacaoMatriculaContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="telefones" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tipoPeriodoAcesso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="totalCreditoTreino" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="ultimaVisita" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="valorPagoContrato" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="valorParcAbertoContrato" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="valorfaturadocontrato" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="vezesPorSemana" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="freePassInicio" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="freePassFim" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "clienteZW", propOrder = {
    "alunoParq",
    "bairro",
    "cpf",
    "cargo",
    "cidade",
    "uf",
    "codigo",
    "codigoAcesso",
    "codigoCliente",
    "codigoColaboradorProfessor",
    "codigoContrato",
    "codigoPessoa",
    "codigoUltimoContatoCRM",
    "colaboradores",
    "crossfit",
    "dataCadastro",
    "dataFimPeriodoAcesso",
    "dataInicioPeriodoAcesso",
    "dataLancamentoContrato",
    "dataMatricula",
    "dataNascimento",
    "dataRematriculaContrato",
    "dataRenovacaoContrato",
    "dataUltimarematricula",
    "dataUltimoBV",
    "dataUltimoContatoCRM",
    "dataUltimoacesso",
    "dataVencimentoTreino",
    "dataVigenciaAte",
    "dataVigenciaAteAjustada",
    "dataVigenciaDe",
    "descricaoDuracao",
    "descricoesModalidades",
    "dia",
    "diasAcessoMes2",
    "diasAcessoMes3",
    "diasAcessoMes4",
    "diasAcessoSemana2",
    "diasAcessoSemana3",
    "diasAcessoSemana4",
    "diasAcessoSemanaPassada",
    "diasAcessoUltimoMes",
    "diasAssiduidadeUltRematriculaAteHoje",
    "diasFaltaSemAcesso",
    "duracaoContratoMeses",
    "email",
    "empresa",
    "empresausafreepass",
    "endereco",
    "estadoCivil",
    "existeParcVencidaContrato",
    "faseAtualCRM",
    "freePass",
    "idade",
    "matricula",
    "mediaDiasAcesso4Meses",
    "mnemonicoDoContrato",
    "modalidades",
    "nome",
    "nomeConsulta",
    "nomeplano",
    "nrTreinosPrevistos",
    "nrTreinosRealizados",
    "pesoRisco",
    "professorSintetico",
    "profissao",
    "rg",
    "responsavelUltimoContatoCRM",
    "saldoContaCorrenteCliente",
    "saldoCreditoTreino",
    "sexo",
    "situacao",
    "situacaoContrato",
    "situacaoContratoOperacao",
    "situacaoMatriculaContrato",
    "telefones",
    "tipoPeriodoAcesso",
    "totalCreditoTreino",
    "ultimaVisita",
    "valorPagoContrato",
    "valorParcAbertoContrato",
    "valorfaturadocontrato",
    "vezesPorSemana",
    "freePassInicio",
    "freePassFim"
})
public class ClienteZW {

    protected Boolean alunoParq;
    protected String bairro;
    @XmlElement(name = "CPF")
    protected String cpf;
    protected String cargo;
    protected String cidade;
    protected String uf;
    protected Integer codigo;
    protected String codigoAcesso;
    protected Integer codigoCliente;
    protected Integer codigoColaboradorProfessor;
    protected Integer codigoContrato;
    protected Integer codigoPessoa;
    protected Integer codigoUltimoContatoCRM;
    protected String colaboradores;
    protected Boolean crossfit;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataCadastro;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataFimPeriodoAcesso;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataInicioPeriodoAcesso;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataLancamentoContrato;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataMatricula;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataNascimento;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataRematriculaContrato;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataRenovacaoContrato;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataUltimarematricula;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataUltimoBV;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataUltimoContatoCRM;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataUltimoacesso;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataVencimentoTreino;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataVigenciaAte;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataVigenciaAteAjustada;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dataVigenciaDe;
    protected String descricaoDuracao;
    protected String descricoesModalidades;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dia;
    protected Integer diasAcessoMes2;
    protected Integer diasAcessoMes3;
    protected Integer diasAcessoMes4;
    protected Integer diasAcessoSemana2;
    protected Integer diasAcessoSemana3;
    protected Integer diasAcessoSemana4;
    protected Integer diasAcessoSemanaPassada;
    protected Integer diasAcessoUltimoMes;
    protected Integer diasAssiduidadeUltRematriculaAteHoje;
    protected Integer diasFaltaSemAcesso;
    protected Integer duracaoContratoMeses;
    protected String email;
    protected Integer empresa;
    protected Boolean empresausafreepass;
    protected String endereco;
    protected String estadoCivil;
    protected Boolean existeParcVencidaContrato;
    protected String faseAtualCRM;
    protected boolean freePass;
    protected Integer idade;
    protected Integer matricula;
    protected Integer mediaDiasAcesso4Meses;
    protected String mnemonicoDoContrato;
    protected String modalidades;
    protected String nome;
    protected String nomeConsulta;
    protected String nomeplano;
    protected Integer nrTreinosPrevistos;
    protected Integer nrTreinosRealizados;
    protected Integer pesoRisco;
    protected ProfessorSintetico professorSintetico;
    protected String profissao;
    @XmlElement(name = "RG")
    protected String rg;
    protected String responsavelUltimoContatoCRM;
    protected Double saldoContaCorrenteCliente;
    protected Integer saldoCreditoTreino;
    protected String sexo;
    protected String situacao;
    protected String situacaoContrato;
    protected String situacaoContratoOperacao;
    protected String situacaoMatriculaContrato;
    protected String telefones;
    protected String tipoPeriodoAcesso;
    protected Integer totalCreditoTreino;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar ultimaVisita;
    protected Double valorPagoContrato;
    protected Double valorParcAbertoContrato;
    protected Double valorfaturadocontrato;
    protected Integer vezesPorSemana;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar freePassInicio;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar freePassFim;

    /**
     * Gets the value of the alunoParq property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isAlunoParq() {
        return alunoParq;
    }

    /**
     * Sets the value of the alunoParq property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setAlunoParq(Boolean value) {
        this.alunoParq = value;
    }

    /**
     * Gets the value of the bairro property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBairro() {
        return bairro;
    }

    /**
     * Sets the value of the bairro property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBairro(String value) {
        this.bairro = value;
    }

    /**
     * Gets the value of the cpf property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCPF() {
        return cpf;
    }

    /**
     * Sets the value of the cpf property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCPF(String value) {
        this.cpf = value;
    }

    /**
     * Gets the value of the cargo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCargo() {
        return cargo;
    }

    /**
     * Sets the value of the cargo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCargo(String value) {
        this.cargo = value;
    }

    /**
     * Gets the value of the cidade property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCidade() {
        return cidade;
    }

    /**
     * Sets the value of the cidade property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCidade(String value) {
        this.cidade = value;
    }

    /**
     * Gets the value of the uf property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    /**
     * Gets the value of the codigo property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */



    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the codigoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    /**
     * Sets the value of the codigoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoAcesso(String value) {
        this.codigoAcesso = value;
    }

    /**
     * Gets the value of the codigoCliente property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    /**
     * Sets the value of the codigoCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoCliente(Integer value) {
        this.codigoCliente = value;
    }

    /**
     * Gets the value of the codigoColaboradorProfessor property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoColaboradorProfessor() {
        return codigoColaboradorProfessor;
    }

    /**
     * Sets the value of the codigoColaboradorProfessor property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoColaboradorProfessor(Integer value) {
        this.codigoColaboradorProfessor = value;
    }

    /**
     * Gets the value of the codigoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    /**
     * Sets the value of the codigoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoContrato(Integer value) {
        this.codigoContrato = value;
    }

    /**
     * Gets the value of the codigoPessoa property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    /**
     * Sets the value of the codigoPessoa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoPessoa(Integer value) {
        this.codigoPessoa = value;
    }

    /**
     * Gets the value of the codigoUltimoContatoCRM property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoUltimoContatoCRM() {
        return codigoUltimoContatoCRM;
    }

    /**
     * Sets the value of the codigoUltimoContatoCRM property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoUltimoContatoCRM(Integer value) {
        this.codigoUltimoContatoCRM = value;
    }

    /**
     * Gets the value of the colaboradores property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getColaboradores() {
        return colaboradores;
    }

    /**
     * Sets the value of the colaboradores property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setColaboradores(String value) {
        this.colaboradores = value;
    }

    /**
     * Gets the value of the crossfit property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isCrossfit() {
        return crossfit;
    }

    /**
     * Sets the value of the crossfit property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setCrossfit(Boolean value) {
        this.crossfit = value;
    }

    /**
     * Gets the value of the dataCadastro property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataCadastro() {
        return dataCadastro;
    }

    /**
     * Sets the value of the dataCadastro property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataCadastro(XMLGregorianCalendar value) {
        this.dataCadastro = value;
    }

    /**
     * Gets the value of the dataFimPeriodoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    /**
     * Sets the value of the dataFimPeriodoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataFimPeriodoAcesso(XMLGregorianCalendar value) {
        this.dataFimPeriodoAcesso = value;
    }

    /**
     * Gets the value of the dataInicioPeriodoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    /**
     * Sets the value of the dataInicioPeriodoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataInicioPeriodoAcesso(XMLGregorianCalendar value) {
        this.dataInicioPeriodoAcesso = value;
    }

    /**
     * Gets the value of the dataLancamentoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataLancamentoContrato() {
        return dataLancamentoContrato;
    }

    /**
     * Sets the value of the dataLancamentoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataLancamentoContrato(XMLGregorianCalendar value) {
        this.dataLancamentoContrato = value;
    }

    /**
     * Gets the value of the dataMatricula property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataMatricula() {
        return dataMatricula;
    }

    /**
     * Sets the value of the dataMatricula property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataMatricula(XMLGregorianCalendar value) {
        this.dataMatricula = value;
    }

    /**
     * Gets the value of the dataNascimento property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataNascimento() {
        return dataNascimento;
    }

    /**
     * Sets the value of the dataNascimento property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataNascimento(XMLGregorianCalendar value) {
        this.dataNascimento = value;
    }

    /**
     * Gets the value of the dataRematriculaContrato property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataRematriculaContrato() {
        return dataRematriculaContrato;
    }

    /**
     * Sets the value of the dataRematriculaContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataRematriculaContrato(XMLGregorianCalendar value) {
        this.dataRematriculaContrato = value;
    }

    /**
     * Gets the value of the dataRenovacaoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataRenovacaoContrato() {
        return dataRenovacaoContrato;
    }

    /**
     * Sets the value of the dataRenovacaoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataRenovacaoContrato(XMLGregorianCalendar value) {
        this.dataRenovacaoContrato = value;
    }

    /**
     * Gets the value of the dataUltimarematricula property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataUltimarematricula() {
        return dataUltimarematricula;
    }

    /**
     * Sets the value of the dataUltimarematricula property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataUltimarematricula(XMLGregorianCalendar value) {
        this.dataUltimarematricula = value;
    }

    /**
     * Gets the value of the dataUltimoBV property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataUltimoBV() {
        return dataUltimoBV;
    }

    /**
     * Sets the value of the dataUltimoBV property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataUltimoBV(XMLGregorianCalendar value) {
        this.dataUltimoBV = value;
    }

    /**
     * Gets the value of the dataUltimoContatoCRM property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataUltimoContatoCRM() {
        return dataUltimoContatoCRM;
    }

    /**
     * Sets the value of the dataUltimoContatoCRM property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataUltimoContatoCRM(XMLGregorianCalendar value) {
        this.dataUltimoContatoCRM = value;
    }

    /**
     * Gets the value of the dataUltimoacesso property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataUltimoacesso() {
        return dataUltimoacesso;
    }

    /**
     * Sets the value of the dataUltimoacesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataUltimoacesso(XMLGregorianCalendar value) {
        this.dataUltimoacesso = value;
    }

    /**
     * Gets the value of the dataVencimentoTreino property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataVencimentoTreino() {
        return dataVencimentoTreino;
    }

    /**
     * Sets the value of the dataVencimentoTreino property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataVencimentoTreino(XMLGregorianCalendar value) {
        this.dataVencimentoTreino = value;
    }

    /**
     * Gets the value of the dataVigenciaAte property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataVigenciaAte() {
        return dataVigenciaAte;
    }

    /**
     * Sets the value of the dataVigenciaAte property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataVigenciaAte(XMLGregorianCalendar value) {
        this.dataVigenciaAte = value;
    }

    /**
     * Gets the value of the dataVigenciaAteAjustada property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataVigenciaAteAjustada() {
        return dataVigenciaAteAjustada;
    }

    /**
     * Sets the value of the dataVigenciaAteAjustada property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataVigenciaAteAjustada(XMLGregorianCalendar value) {
        this.dataVigenciaAteAjustada = value;
    }

    /**
     * Gets the value of the dataVigenciaDe property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDataVigenciaDe() {
        return dataVigenciaDe;
    }

    /**
     * Sets the value of the dataVigenciaDe property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDataVigenciaDe(XMLGregorianCalendar value) {
        this.dataVigenciaDe = value;
    }

    /**
     * Gets the value of the descricaoDuracao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricaoDuracao() {
        return descricaoDuracao;
    }

    /**
     * Sets the value of the descricaoDuracao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricaoDuracao(String value) {
        this.descricaoDuracao = value;
    }

    /**
     * Gets the value of the descricoesModalidades property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescricoesModalidades() {
        return descricoesModalidades;
    }

    /**
     * Sets the value of the descricoesModalidades property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescricoesModalidades(String value) {
        this.descricoesModalidades = value;
    }

    /**
     * Gets the value of the dia property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDia() {
        return dia;
    }

    /**
     * Sets the value of the dia property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDia(XMLGregorianCalendar value) {
        this.dia = value;
    }

    /**
     * Gets the value of the diasAcessoMes2 property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoMes2() {
        return diasAcessoMes2;
    }

    /**
     * Sets the value of the diasAcessoMes2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoMes2(Integer value) {
        this.diasAcessoMes2 = value;
    }

    /**
     * Gets the value of the diasAcessoMes3 property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoMes3() {
        return diasAcessoMes3;
    }

    /**
     * Sets the value of the diasAcessoMes3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoMes3(Integer value) {
        this.diasAcessoMes3 = value;
    }

    /**
     * Gets the value of the diasAcessoMes4 property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoMes4() {
        return diasAcessoMes4;
    }

    /**
     * Sets the value of the diasAcessoMes4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoMes4(Integer value) {
        this.diasAcessoMes4 = value;
    }

    /**
     * Gets the value of the diasAcessoSemana2 property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoSemana2() {
        return diasAcessoSemana2;
    }

    /**
     * Sets the value of the diasAcessoSemana2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoSemana2(Integer value) {
        this.diasAcessoSemana2 = value;
    }

    /**
     * Gets the value of the diasAcessoSemana3 property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoSemana3() {
        return diasAcessoSemana3;
    }

    /**
     * Sets the value of the diasAcessoSemana3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoSemana3(Integer value) {
        this.diasAcessoSemana3 = value;
    }

    /**
     * Gets the value of the diasAcessoSemana4 property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoSemana4() {
        return diasAcessoSemana4;
    }

    /**
     * Sets the value of the diasAcessoSemana4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoSemana4(Integer value) {
        this.diasAcessoSemana4 = value;
    }

    /**
     * Gets the value of the diasAcessoSemanaPassada property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoSemanaPassada() {
        return diasAcessoSemanaPassada;
    }

    /**
     * Sets the value of the diasAcessoSemanaPassada property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoSemanaPassada(Integer value) {
        this.diasAcessoSemanaPassada = value;
    }

    /**
     * Gets the value of the diasAcessoUltimoMes property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAcessoUltimoMes() {
        return diasAcessoUltimoMes;
    }

    /**
     * Sets the value of the diasAcessoUltimoMes property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAcessoUltimoMes(Integer value) {
        this.diasAcessoUltimoMes = value;
    }

    /**
     * Gets the value of the diasAssiduidadeUltRematriculaAteHoje property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasAssiduidadeUltRematriculaAteHoje() {
        return diasAssiduidadeUltRematriculaAteHoje;
    }

    /**
     * Sets the value of the diasAssiduidadeUltRematriculaAteHoje property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasAssiduidadeUltRematriculaAteHoje(Integer value) {
        this.diasAssiduidadeUltRematriculaAteHoje = value;
    }

    /**
     * Gets the value of the diasFaltaSemAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDiasFaltaSemAcesso() {
        return diasFaltaSemAcesso;
    }

    /**
     * Sets the value of the diasFaltaSemAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDiasFaltaSemAcesso(Integer value) {
        this.diasFaltaSemAcesso = value;
    }

    /**
     * Gets the value of the duracaoContratoMeses property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getDuracaoContratoMeses() {
        return duracaoContratoMeses;
    }

    /**
     * Sets the value of the duracaoContratoMeses property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setDuracaoContratoMeses(Integer value) {
        this.duracaoContratoMeses = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the empresa property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getEmpresa() {
        return empresa;
    }

    /**
     * Sets the value of the empresa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setEmpresa(Integer value) {
        this.empresa = value;
    }

    /**
     * Gets the value of the empresausafreepass property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEmpresausafreepass() {
        return empresausafreepass;
    }

    /**
     * Sets the value of the empresausafreepass property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEmpresausafreepass(Boolean value) {
        this.empresausafreepass = value;
    }

    /**
     * Gets the value of the endereco property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEndereco() {
        return endereco;
    }

    /**
     * Sets the value of the endereco property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndereco(String value) {
        this.endereco = value;
    }

    /**
     * Gets the value of the estadoCivil property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEstadoCivil() {
        return estadoCivil;
    }

    /**
     * Sets the value of the estadoCivil property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEstadoCivil(String value) {
        this.estadoCivil = value;
    }

    /**
     * Gets the value of the existeParcVencidaContrato property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isExisteParcVencidaContrato() {
        return existeParcVencidaContrato;
    }

    /**
     * Sets the value of the existeParcVencidaContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setExisteParcVencidaContrato(Boolean value) {
        this.existeParcVencidaContrato = value;
    }

    /**
     * Gets the value of the faseAtualCRM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFaseAtualCRM() {
        return faseAtualCRM;
    }

    /**
     * Sets the value of the faseAtualCRM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFaseAtualCRM(String value) {
        this.faseAtualCRM = value;
    }

    /**
     * Gets the value of the freePass property.
     * 
     */
    public boolean isFreePass() {
        return freePass;
    }

    /**
     * Sets the value of the freePass property.
     * 
     */
    public void setFreePass(boolean value) {
        this.freePass = value;
    }

    /**
     * Gets the value of the idade property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getIdade() {
        return idade;
    }

    /**
     * Sets the value of the idade property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setIdade(Integer value) {
        this.idade = value;
    }

    /**
     * Gets the value of the matricula property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMatricula() {
        return matricula;
    }

    /**
     * Sets the value of the matricula property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMatricula(Integer value) {
        this.matricula = value;
    }

    /**
     * Gets the value of the mediaDiasAcesso4Meses property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMediaDiasAcesso4Meses() {
        return mediaDiasAcesso4Meses;
    }

    /**
     * Sets the value of the mediaDiasAcesso4Meses property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMediaDiasAcesso4Meses(Integer value) {
        this.mediaDiasAcesso4Meses = value;
    }

    /**
     * Gets the value of the mnemonicoDoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMnemonicoDoContrato() {
        return mnemonicoDoContrato;
    }

    /**
     * Sets the value of the mnemonicoDoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMnemonicoDoContrato(String value) {
        this.mnemonicoDoContrato = value;
    }

    /**
     * Gets the value of the modalidades property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModalidades() {
        return modalidades;
    }

    /**
     * Sets the value of the modalidades property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModalidades(String value) {
        this.modalidades = value;
    }

    /**
     * Gets the value of the nome property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Sets the value of the nome property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Gets the value of the nomeConsulta property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeConsulta() {
        return nomeConsulta;
    }

    /**
     * Sets the value of the nomeConsulta property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeConsulta(String value) {
        this.nomeConsulta = value;
    }

    /**
     * Gets the value of the nomeplano property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeplano() {
        return nomeplano;
    }

    /**
     * Sets the value of the nomeplano property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeplano(String value) {
        this.nomeplano = value;
    }

    /**
     * Gets the value of the nrTreinosPrevistos property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNrTreinosPrevistos() {
        return nrTreinosPrevistos;
    }

    /**
     * Sets the value of the nrTreinosPrevistos property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNrTreinosPrevistos(Integer value) {
        this.nrTreinosPrevistos = value;
    }

    /**
     * Gets the value of the nrTreinosRealizados property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNrTreinosRealizados() {
        return nrTreinosRealizados;
    }

    /**
     * Sets the value of the nrTreinosRealizados property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNrTreinosRealizados(Integer value) {
        this.nrTreinosRealizados = value;
    }

    /**
     * Gets the value of the pesoRisco property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPesoRisco() {
        return pesoRisco;
    }

    /**
     * Sets the value of the pesoRisco property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPesoRisco(Integer value) {
        this.pesoRisco = value;
    }

    /**
     * Gets the value of the professorSintetico property.
     * 
     * @return
     *     possible object is
     *     {@link ProfessorSintetico }
     *     
     */
    public ProfessorSintetico getProfessorSintetico() {
        return professorSintetico;
    }

    /**
     * Sets the value of the professorSintetico property.
     * 
     * @param value
     *     allowed object is
     *     {@link ProfessorSintetico }
     *     
     */
    public void setProfessorSintetico(ProfessorSintetico value) {
        this.professorSintetico = value;
    }

    /**
     * Gets the value of the profissao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProfissao() {
        return profissao;
    }

    /**
     * Sets the value of the profissao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProfissao(String value) {
        this.profissao = value;
    }

    /**
     * Gets the value of the rg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRG() {
        return rg;
    }

    /**
     * Sets the value of the rg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRG(String value) {
        this.rg = value;
    }

    /**
     * Gets the value of the responsavelUltimoContatoCRM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResponsavelUltimoContatoCRM() {
        return responsavelUltimoContatoCRM;
    }

    /**
     * Sets the value of the responsavelUltimoContatoCRM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResponsavelUltimoContatoCRM(String value) {
        this.responsavelUltimoContatoCRM = value;
    }

    /**
     * Gets the value of the saldoContaCorrenteCliente property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getSaldoContaCorrenteCliente() {
        return saldoContaCorrenteCliente;
    }

    /**
     * Sets the value of the saldoContaCorrenteCliente property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setSaldoContaCorrenteCliente(Double value) {
        this.saldoContaCorrenteCliente = value;
    }

    /**
     * Gets the value of the saldoCreditoTreino property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    /**
     * Sets the value of the saldoCreditoTreino property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setSaldoCreditoTreino(Integer value) {
        this.saldoCreditoTreino = value;
    }

    /**
     * Gets the value of the sexo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSexo() {
        return sexo;
    }

    /**
     * Sets the value of the sexo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSexo(String value) {
        this.sexo = value;
    }

    /**
     * Gets the value of the situacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSituacao() {
        return situacao;
    }

    /**
     * Sets the value of the situacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSituacao(String value) {
        this.situacao = value;
    }

    /**
     * Gets the value of the situacaoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    /**
     * Sets the value of the situacaoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSituacaoContrato(String value) {
        this.situacaoContrato = value;
    }

    /**
     * Gets the value of the situacaoContratoOperacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSituacaoContratoOperacao() {
        return situacaoContratoOperacao;
    }

    /**
     * Sets the value of the situacaoContratoOperacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSituacaoContratoOperacao(String value) {
        this.situacaoContratoOperacao = value;
    }

    /**
     * Gets the value of the situacaoMatriculaContrato property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSituacaoMatriculaContrato() {
        return situacaoMatriculaContrato;
    }

    /**
     * Sets the value of the situacaoMatriculaContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSituacaoMatriculaContrato(String value) {
        this.situacaoMatriculaContrato = value;
    }

    /**
     * Gets the value of the telefones property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTelefones() {
        return telefones;
    }

    /**
     * Sets the value of the telefones property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTelefones(String value) {
        this.telefones = value;
    }

    /**
     * Gets the value of the tipoPeriodoAcesso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTipoPeriodoAcesso() {
        return tipoPeriodoAcesso;
    }

    /**
     * Sets the value of the tipoPeriodoAcesso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTipoPeriodoAcesso(String value) {
        this.tipoPeriodoAcesso = value;
    }

    /**
     * Gets the value of the totalCreditoTreino property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getTotalCreditoTreino() {
        return totalCreditoTreino;
    }

    /**
     * Sets the value of the totalCreditoTreino property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setTotalCreditoTreino(Integer value) {
        this.totalCreditoTreino = value;
    }

    /**
     * Gets the value of the ultimaVisita property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getUltimaVisita() {
        return ultimaVisita;
    }

    /**
     * Sets the value of the ultimaVisita property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setUltimaVisita(XMLGregorianCalendar value) {
        this.ultimaVisita = value;
    }

    /**
     * Gets the value of the valorPagoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getValorPagoContrato() {
        return valorPagoContrato;
    }

    /**
     * Sets the value of the valorPagoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setValorPagoContrato(Double value) {
        this.valorPagoContrato = value;
    }

    /**
     * Gets the value of the valorParcAbertoContrato property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getValorParcAbertoContrato() {
        return valorParcAbertoContrato;
    }

    /**
     * Sets the value of the valorParcAbertoContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setValorParcAbertoContrato(Double value) {
        this.valorParcAbertoContrato = value;
    }

    /**
     * Gets the value of the valorfaturadocontrato property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getValorfaturadocontrato() {
        return valorfaturadocontrato;
    }

    /**
     * Sets the value of the valorfaturadocontrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setValorfaturadocontrato(Double value) {
        this.valorfaturadocontrato = value;
    }

    /**
     * Gets the value of the vezesPorSemana property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getVezesPorSemana() {
        return vezesPorSemana;
    }

    /**
     * Sets the value of the vezesPorSemana property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setVezesPorSemana(Integer value) {
        this.vezesPorSemana = value;
    }

    /**
     * Gets the value of the freePassInicio property.
     *
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *
     */
    public XMLGregorianCalendar getFreePassInicio() {
        return freePassInicio;
    }

    /**
     * Sets the value of the freePassInicio property.
     *
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *
     */
    public void setFreePassInicio(XMLGregorianCalendar value) {
        this.freePassInicio = value;
    }

    /**
     * Gets the value of the freePassFim property.
     *
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *
     */
    public XMLGregorianCalendar getFreePassFim() {
        return freePassFim;
    }

    /**
     * Sets the value of the freePassFim property.
     *
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *
     */
    public void setFreePassFim(XMLGregorianCalendar value) {
        this.freePassFim = value;
    }

}

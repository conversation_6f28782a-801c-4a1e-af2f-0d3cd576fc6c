package servicos.integracao.treino.dto;

import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.treino.*;

public class UsuarioZWDTO {
    private String chave;
    private ClienteZW cliente;
    private Integer codigo;
    private String codigoExterno;
    private Integer convite;
    private String cpf;
    private String email;
    private Integer empresaZW;
    private Integer indicado;
    private String nome;
    private Professor<PERSON>int<PERSON>o professor;
    private String senha;
    private String status;
    private String tipo;
    private String userName;
    private Integer usuarioZW;
    private String fotoKey;

    public JSONObject toJSON(){
        JSONObject json = new JSONObject();
        json.put("chave", this.chave);
        json.put("cliente", clienteTreinoToJSON(this.cliente));
        json.put("codigo", this.codigo);
        json.put("codigoExterno", this.codigoExterno);
        json.put("convite", this.convite);
        json.put("cpf", this.cpf);
        json.put("email", this.email);
        json.put("empresaZW", this.empresaZW);
        json.put("indicado", this.indicado);
        json.put("nome", this.nome);
        json.put("professor", this.professor != null ? professorTreinotoJSON(this.professor) : null);
        json.put("senha", this.senha);
        json.put("status", this.status);
        json.put("tipo", this.tipo);
        json.put("userName", this.userName);
        json.put("fotoKey", this.fotoKey);
        return json;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigoExterno() {
        return codigoExterno;
    }

    public void setCodigoExterno(String codigoExterno) {
        this.codigoExterno = codigoExterno;
    }

    public Integer getConvite() {
        return convite;
    }

    public void setConvite(Integer convite) {
        this.convite = convite;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getEmpresaZW() {
        return empresaZW;
    }

    public void setEmpresaZW(Integer empresaZW) {
        this.empresaZW = empresaZW;
    }

    public Integer getIndicado() {
        return indicado;
    }

    public void setIndicado(Integer indicado) {
        this.indicado = indicado;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    public void setUsuarioZW(Integer usuarioZW) {
        this.usuarioZW = usuarioZW;
    }

    public String getFotoKey() {
        return fotoKey;
    }

    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public ClienteZW getCliente() {
        return cliente;
    }

    public void setCliente(ClienteZW cliente) {
        this.cliente = cliente;
    }

    public ProfessorSintetico getProfessor() {
        return professor;
    }

    public void setProfessor(ProfessorSintetico professor) {
        this.professor = professor;
    }

    public JSONObject clienteTreinoToJSON(ClienteZW cliente) {
        JSONObject json = new JSONObject();
        if(cliente != null) {
            json.put("alunoParq", cliente.isAlunoParq());
            json.put("bairro", cliente.getBairro());
            json.put("cpf", cliente.getCPF());
            json.put("cargo", cliente.getCargo());
            json.put("cidade", cliente.getCidade());
            json.put("uf", cliente.getUf());
            json.put("codigo", cliente.getCodigo());
            json.put("codigoAcesso", cliente.getCodigoAcesso());
            json.put("codigoCliente", cliente.getCodigoCliente());
            json.put("codigoColaboradorProfessor", cliente.getCodigoColaboradorProfessor());
            json.put("codigoContrato", cliente.getCodigoContrato());
            json.put("codigoPessoa", cliente.getCodigoPessoa());
            json.put("codigoUltimoContatoCRM", cliente.getCodigoUltimoContatoCRM());
            json.put("colaboradores", cliente.getColaboradores());
            json.put("crossfit", cliente.isCrossfit());
            json.put("dataCadastro", cliente.getDataCadastro() != null ? cliente.getDataCadastro() : null);
            json.put("dataFimPeriodoAcesso", cliente.getDataFimPeriodoAcesso() != null ? cliente.getDataFimPeriodoAcesso() : null);
            json.put("dataInicioPeriodoAcesso", cliente.getDataInicioPeriodoAcesso() != null ? cliente.getDataInicioPeriodoAcesso() : null);
            json.put("dataLancamentoContrato", cliente.getDataLancamentoContrato() != null ? cliente.getDataLancamentoContrato() : null);
            json.put("dataMatricula", cliente.getDataMatricula() != null ? cliente.getDataMatricula() : null);
            json.put("dataNascimento", cliente.getDataNascimento() != null ? cliente.getDataNascimento() : null);
            json.put("dataRematriculaContrato", cliente.getDataRematriculaContrato() != null ? cliente.getDataRematriculaContrato() : null);
            json.put("dataRenovacaoContrato", cliente.getDataRenovacaoContrato() != null ? cliente.getDataRenovacaoContrato() : null);
            json.put("dataUltimarematricula", cliente.getDataUltimarematricula() != null ? cliente.getDataUltimarematricula() : null);
            json.put("dataUltimoBV", cliente.getDataUltimoBV() != null ? cliente.getDataUltimoBV() : null);
            json.put("dataUltimoContatoCRM", cliente.getDataUltimoContatoCRM() != null ? cliente.getDataUltimoContatoCRM() : null);
            json.put("dataUltimoacesso", cliente.getDataUltimoacesso() != null ? cliente.getDataUltimoacesso() : null);
            json.put("dataVencimentoTreino", cliente.getDataVencimentoTreino() != null ? cliente.getDataVencimentoTreino() : null);
            json.put("dataVigenciaAte", cliente.getDataVigenciaAte() != null ? cliente.getDataVigenciaAte() : null);
            json.put("dataVigenciaAteAjustada", cliente.getDataVigenciaAteAjustada() != null ? cliente.getDataVigenciaAteAjustada() : null);
            json.put("dataVigenciaDe", cliente.getDataVigenciaDe() != null ? cliente.getDataVigenciaDe() : null);
            json.put("descricaoDuracao", cliente.getDescricaoDuracao());
            json.put("descricoesModalidades", cliente.getDescricoesModalidades());
            json.put("dia", cliente.getDia() != null ? cliente.getDia() : null);
            json.put("diasAcessoMes2", cliente.getDiasAcessoMes2());
            json.put("diasAcessoMes3", cliente.getDiasAcessoMes3());
            json.put("diasAcessoMes4", cliente.getDiasAcessoMes4());
            json.put("diasAcessoSemana2", cliente.getDiasAcessoSemana2());
            json.put("diasAcessoSemana3", cliente.getDiasAcessoSemana3());
            json.put("diasAcessoSemana4", cliente.getDiasAcessoSemana4());
            json.put("diasAcessoSemanaPassada", cliente.getDiasAcessoSemanaPassada());
            json.put("diasAcessoUltimoMes", cliente.getDiasAcessoUltimoMes());
            json.put("diasAssiduidadeUltRematriculaAteHoje", cliente.getDiasAssiduidadeUltRematriculaAteHoje());
            json.put("diasFaltaSemAcesso", cliente.getDiasFaltaSemAcesso());
            json.put("duracaoContratoMeses", cliente.getDuracaoContratoMeses());
            json.put("email", cliente.getEmail());
            json.put("empresa", cliente.getEmpresa());
            json.put("empresausafreepass", cliente.isEmpresausafreepass());
            json.put("endereco", cliente.getEndereco());
            json.put("estadoCivil", cliente.getEstadoCivil());
            json.put("existeParcVencidaContrato", cliente.isExisteParcVencidaContrato());
            json.put("faseAtualCRM", cliente.getFaseAtualCRM());
            json.put("freePass", cliente.isFreePass());
            json.put("idade", cliente.getIdade());
            json.put("matricula", cliente.getMatricula());
            json.put("mediaDiasAcesso4Meses", cliente.getMediaDiasAcesso4Meses());
            json.put("mnemonicoDoContrato", cliente.getMnemonicoDoContrato());
            json.put("modalidades", cliente.getModalidades());
            json.put("nome", cliente.getNome());
            json.put("nomeConsulta", cliente.getNomeConsulta());
            json.put("nomeplano", cliente.getNomeplano());
            json.put("nrTreinosPrevistos", cliente.getNrTreinosPrevistos());
            json.put("nrTreinosRealizados", cliente.getNrTreinosRealizados());
            json.put("pesoRisco", cliente.getPesoRisco());
            json.put("professorSintetico", cliente.getProfessorSintetico() != null ? professorTreinotoJSON(cliente.getProfessorSintetico()) : null);
            json.put("profissao", cliente.getProfissao());
            json.put("rg", cliente.getRG());
            json.put("responsavelUltimoContatoCRM", cliente.getResponsavelUltimoContatoCRM());
            json.put("saldoContaCorrenteCliente", cliente.getSaldoContaCorrenteCliente());
            json.put("saldoCreditoTreino", cliente.getSaldoCreditoTreino());
            json.put("sexo", cliente.getSexo());
            json.put("situacao", cliente.getSituacao());
            json.put("situacaoContrato", cliente.getSituacaoContrato());
            json.put("situacaoContratoOperacao", cliente.getSituacaoContratoOperacao());
            json.put("situacaoMatriculaContrato", cliente.getSituacaoMatriculaContrato());
            json.put("telefones", cliente.getTelefones());
            json.put("tipoPeriodoAcesso", cliente.getTipoPeriodoAcesso());
            json.put("totalCreditoTreino", cliente.getTotalCreditoTreino());
            json.put("ultimaVisita", cliente.getUltimaVisita() != null ? cliente.getUltimaVisita() : null);
            json.put("valorPagoContrato", cliente.getValorPagoContrato());
            json.put("valorParcAbertoContrato", cliente.getValorParcAbertoContrato());
            json.put("valorfaturadocontrato", cliente.getValorfaturadocontrato());
            json.put("vezesPorSemana", cliente.getVezesPorSemana());
            json.put("freePassInicio", cliente.getFreePassInicio());
            json.put("freePassFim", cliente.getFreePassFim());
        }
        return json;
    }

    public JSONObject empresaTreinotoJSON(Empresa empresa){
        JSONObject json = new JSONObject();
        if(empresa != null) {
            json.put("codZW", empresa.getCodZW());
            json.put("codigo", empresa.getCodigo());
            json.put("email", empresa.getEmail());
            json.put("idiomaBanco", empresa.getIdiomaBanco() != null ? empresa.getIdiomaBanco().name() : null);
            json.put("nome", empresa.getNome());
            json.put("timeZoneDefault", empresa.getTimeZoneDefault());
            json.put("tokenSMS", empresa.getTokenSMS());
            json.put("urlSite", empresa.getUrlSite());
            json.put("usaMobile", empresa.isUsaMobile());
        }
        return json;
    }

    public JSONObject professorTreinotoJSON(ProfessorSintetico professor){
        JSONObject json = new JSONObject();
        if(professor != null) {
            json.put("ativo", professor.isAtivo());
            json.put("avatar", professor.getAvatar());
            json.put("cobrarCreditos", professor.isCobrarCreditos());
            json.put("codigo", professor.getCodigo());
            json.put("codigoColaborador", professor.getCodigoColaborador());
            json.put("codigoPessoa", professor.getCodigoPessoa());
            json.put("colaboradorSimples", professor.isColaboradorSimples());
            json.put("cref", professor.getCref());
            json.put("email", professor.getEmail());
            json.put("empresa", empresaTreinotoJSON(professor.getEmpresa()));
            json.put("fotoKey", professor.getFotoKey());
            json.put("nome", professor.getNome());
            json.put("personaInterno", professor.isPersonaInterno());
            json.put("personal", professor.isPersonal());
            json.put("pessoa", pessoaTreinotoJSON(professor.getPessoa()));
            json.put("posPago", professor.isPosPago());
            json.put("professorTW", professor.isProfessorTW());
            json.put("total", professor.getTotal());
            json.put("uriImagem", professor.getUriImagem());
            json.put("versao", professor.getVersao());
            json.put("codigoAcesso", professor.getCodigoAcesso());
        }
        return json;
    }

    public JSONObject pessoaTreinotoJSON(Pessoa pessoa){
        JSONObject json = new JSONObject();
        if(pessoa != null) {
            json.put("anoNasc", pessoa.getAnoNasc());
            json.put("codigo", pessoa.getCodigo());
            json.put("dataNascimento", pessoa.getDataNascimento() != null ? pessoa.getDataNascimento() : null);

            JSONArray emailsArray = new JSONArray();
            for (Email email : pessoa.getEmails()) {
                emailsArray.put(emailTreinotoJSON(email));
            }
            json.put("emails", emailsArray);

            json.put("fotoKey", pessoa.getFotoKey());
            json.put("mesNasc", pessoa.getMesNasc());
            json.put("nome", pessoa.getNome());
            json.put("parq", pessoa.isParq());
            json.put("sexo", pessoa.getSexo());

            JSONArray telefonesArray = new JSONArray();
            for (Telefone telefone : pessoa.getTelefones()) {
                telefonesArray.put(telefonesTreinotoJSON(telefone));
            }
            json.put("telefones", telefonesArray);
        }
        return json;
    }

    public JSONObject emailTreinotoJSON(Email email) {
        JSONObject json = new JSONObject();
        if(email != null) {
            json.put("codigo", email.getCodigo());
            json.put("email", email.getEmail());
            json.put("pessoa", pessoaTreinotoJSON(email.getPessoa()));
        }
        return json;
    }

    public JSONObject telefonesTreinotoJSON(Telefone telefone){
        JSONObject json = new JSONObject();
        if(telefone != null) {
            json.put("pessoa", pessoaTreinotoJSON(telefone.getPessoa()));
            json.put("telefone", telefone.getTelefone());
            json.put("tipo", telefone.getTipo());
        }
        return json;
    }
}


package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for idiomaBancoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="idiomaBancoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="PT"/>
 *     &lt;enumeration value="ES"/>
 *     &lt;enumeration value="IN"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "idiomaBancoEnum")
@XmlEnum
public enum IdiomaBancoEnum {

    PT,
    PT_MZ,
    ES,
    ES_LA,
    EN,
    IN;

    public String value() {
        return name();
    }

    public static IdiomaBancoEnum fromValue(String v) {
        return valueOf(v);
    }

}

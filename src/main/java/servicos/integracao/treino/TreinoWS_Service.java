
package servicos.integracao.treino;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "TreinoWS", targetNamespace = "http://webservice.pacto.com.br/", wsdlLocation = "http://localhost:9090/TreinoWeb/TreinoWS?wsdl")
public class TreinoWS_Service
    extends Service
{

    private final static URL TREINOWS_WSDL_LOCATION;
    private final static WebServiceException TREINOWS_EXCEPTION;
    private final static QName TREINOWS_QNAME = new QName("http://webservice.pacto.com.br/", "TreinoWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://localhost:9090/TreinoWeb/TreinoWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        TREINOWS_WSDL_LOCATION = url;
        TREINOWS_EXCEPTION = e;
    }

    public TreinoWS_Service() {
        super(__getWsdlLocation(), TREINOWS_QNAME);
    }

    public TreinoWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns TreinoWS
     */
    @WebEndpoint(name = "TreinoWSPort")
    public TreinoWS getTreinoWSPort() {
        return super.getPort(new QName("http://webservice.pacto.com.br/", "TreinoWSPort"), TreinoWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns TreinoWS
     */
    @WebEndpoint(name = "TreinoWSPort")
    public TreinoWS getTreinoWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.pacto.com.br/", "TreinoWSPort"), TreinoWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (TREINOWS_EXCEPTION!= null) {
            throw TREINOWS_EXCEPTION;
        }
        return TREINOWS_WSDL_LOCATION;
    }

}


package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for tipoUsuarioEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="tipoUsuarioEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ALUNO"/>
 *     &lt;enumeration value="PROFESSOR"/>
 *     &lt;enumeration value="COORDENADOR"/>
 *     &lt;enumeration value="ROOT"/>
 *     &lt;enumeration value="CONSULTOR"/>
 *     &lt;enumeration value="CONVIDADO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "tipoUsuarioEnum")
@XmlEnum
public enum TipoUsuarioEnum {

    ALUNO,
    PROFESSOR,
    COORDENADOR,
    ROOT,
    CONSULTOR,
    CONVIDADO;

    public String value() {
        return name();
    }

    public static TipoUsuarioEnum fromValue(String v) {
        return valueOf(v);
    }

}

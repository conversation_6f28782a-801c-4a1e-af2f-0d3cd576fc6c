
package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for empresa complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="empresa">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="codZW" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="idiomaBanco" type="{http://webservice.pacto.com.br/}idiomaBancoEnum" minOccurs="0"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="timeZoneDefault" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tokenSMS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="urlSite" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usaMobile" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "empresa", propOrder = {
    "codZW",
    "codigo",
    "email",
    "idiomaBanco",
    "nome",
    "timeZoneDefault",
    "tokenSMS",
    "urlSite",
    "usaMobile"
})
public class Empresa {

    protected Integer codZW;
    protected Integer codigo;
    protected String email;
    protected IdiomaBancoEnum idiomaBanco;
    protected String nome;
    protected String timeZoneDefault;
    protected String tokenSMS;
    protected String urlSite;
    protected Boolean usaMobile;

    /**
     * Gets the value of the codZW property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodZW() {
        return codZW;
    }

    /**
     * Sets the value of the codZW property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodZW(Integer value) {
        this.codZW = value;
    }

    /**
     * Gets the value of the codigo property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the idiomaBanco property.
     * 
     * @return
     *     possible object is
     *     {@link IdiomaBancoEnum }
     *     
     */
    public IdiomaBancoEnum getIdiomaBanco() {
        return idiomaBanco;
    }

    /**
     * Sets the value of the idiomaBanco property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdiomaBancoEnum }
     *     
     */
    public void setIdiomaBanco(IdiomaBancoEnum value) {
        this.idiomaBanco = value;
    }

    /**
     * Gets the value of the nome property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Sets the value of the nome property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Gets the value of the timeZoneDefault property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTimeZoneDefault() {
        return timeZoneDefault;
    }

    /**
     * Sets the value of the timeZoneDefault property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTimeZoneDefault(String value) {
        this.timeZoneDefault = value;
    }

    /**
     * Gets the value of the tokenSMS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTokenSMS() {
        return tokenSMS;
    }

    /**
     * Sets the value of the tokenSMS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTokenSMS(String value) {
        this.tokenSMS = value;
    }

    /**
     * Gets the value of the urlSite property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUrlSite() {
        return urlSite;
    }

    /**
     * Sets the value of the urlSite property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUrlSite(String value) {
        this.urlSite = value;
    }

    /**
     * Gets the value of the usaMobile property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUsaMobile() {
        return usaMobile;
    }

    /**
     * Sets the value of the usaMobile property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUsaMobile(Boolean value) {
        this.usaMobile = value;
    }

}


package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de usuarioZW complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="usuarioZW">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="chave" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cliente" type="{http://webservice.pacto.com.br/}clienteZW" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoExterno" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="convite" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="cpf" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresaZW" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="indicado" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="professor" type="{http://webservice.pacto.com.br/}professorSintetico" minOccurs="0"/>
 *         &lt;element name="senha" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="status" type="{http://webservice.pacto.com.br/}statusEnum" minOccurs="0"/>
 *         &lt;element name="tipo" type="{http://webservice.pacto.com.br/}tipoUsuarioEnum" minOccurs="0"/>
 *         &lt;element name="userName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="usuarioZW" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="fotoKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "usuarioZW", propOrder = {
    "chave",
    "cliente",
    "codigo",
    "codigoExterno",
    "convite",
    "cpf",
    "email",
    "empresaZW",
    "indicado",
    "nome",
    "professor",
    "senha",
    "status",
    "tipo",
    "userName",
    "usuarioZW",
    "fotoKey",
    "emailVerificado",
    "perfilTw"
})
public class UsuarioZW {

    protected String chave;
    protected ClienteZW cliente;
    protected Integer codigo;
    protected String codigoExterno;
    protected Integer convite;
    protected String cpf;
    protected String email;
    protected Integer empresaZW;
    protected Integer indicado;
    protected String nome;
    protected ProfessorSintetico professor;
    protected String senha;
    @XmlSchemaType(name = "string")
    protected StatusEnum status;
    @XmlSchemaType(name = "string")
    protected TipoUsuarioEnum tipo;
    protected String userName;
    protected Integer usuarioZW;
    protected String fotoKey;
    private Boolean emailVerificado;
    protected Integer perfilTw;

    /**
     * Obtém o valor da propriedade chave.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChave() {
        return chave;
    }

    /**
     * Define o valor da propriedade chave.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChave(String value) {
        this.chave = value;
    }

    /**
     * Obtém o valor da propriedade cliente.
     * 
     * @return
     *     possible object is
     *     {@link ClienteZW }
     *     
     */
    public ClienteZW getCliente() {
        return cliente;
    }

    /**
     * Define o valor da propriedade cliente.
     * 
     * @param value
     *     allowed object is
     *     {@link ClienteZW }
     *     
     */
    public void setCliente(ClienteZW value) {
        this.cliente = value;
    }

    /**
     * Obtém o valor da propriedade codigo.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Define o valor da propriedade codigo.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Obtém o valor da propriedade codigoExterno.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoExterno() {
        return codigoExterno;
    }

    /**
     * Define o valor da propriedade codigoExterno.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoExterno(String value) {
        this.codigoExterno = value;
    }

    /**
     * Obtém o valor da propriedade convite.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getConvite() {
        return convite;
    }

    /**
     * Define o valor da propriedade convite.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setConvite(Integer value) {
        this.convite = value;
    }

    /**
     * Obtém o valor da propriedade email.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Define o valor da propriedade email.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Obtém o valor da propriedade empresaZW.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getEmpresaZW() {
        return empresaZW;
    }

    /**
     * Define o valor da propriedade empresaZW.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setEmpresaZW(Integer value) {
        this.empresaZW = value;
    }

    /**
     * Obtém o valor da propriedade indicado.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getIndicado() {
        return indicado;
    }

    /**
     * Define o valor da propriedade indicado.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setIndicado(Integer value) {
        this.indicado = value;
    }
    /**
     * Obtém o valor da propriedade nome.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Define o valor da propriedade nome.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Obtém o valor da propriedade professor.
     * 
     * @return
     *     possible object is
     *     {@link ProfessorSintetico }
     *     
     */
    public ProfessorSintetico getProfessor() {
        return professor;
    }

    /**
     * Define o valor da propriedade professor.
     * 
     * @param value
     *     allowed object is
     *     {@link ProfessorSintetico }
     *     
     */
    public void setProfessor(ProfessorSintetico value) {
        this.professor = value;
    }

    /**
     * Obtém o valor da propriedade senha.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenha() {
        return senha;
    }

    /**
     * Define o valor da propriedade senha.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenha(String value) {
        this.senha = value;
    }

    /**
     * Obtém o valor da propriedade status.
     * 
     * @return
     *     possible object is
     *     {@link StatusEnum }
     *     
     */
    public StatusEnum getStatus() {
        return status;
    }

    /**
     * Define o valor da propriedade status.
     * 
     * @param value
     *     allowed object is
     *     {@link StatusEnum }
     *     
     */
    public void setStatus(StatusEnum value) {
        this.status = value;
    }

    /**
     * Obtém o valor da propriedade tipo.
     * 
     * @return
     *     possible object is
     *     {@link TipoUsuarioEnum }
     *     
     */
    public TipoUsuarioEnum getTipo() {
        return tipo;
    }

    /**
     * Define o valor da propriedade tipo.
     * 
     * @param value
     *     allowed object is
     *     {@link TipoUsuarioEnum }
     *     
     */
    public void setTipo(TipoUsuarioEnum value) {
        this.tipo = value;
    }

    /**
     * Obtém o valor da propriedade userName.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserName() {
        return userName;
    }

    /**
     * Define o valor da propriedade userName.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserName(String value) {
        this.userName = value;
    }

    /**
     * Obtém o valor da propriedade usuarioZW.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getUsuarioZW() {
        return usuarioZW;
    }

    /**
     * Define o valor da propriedade usuarioZW.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setUsuarioZW(Integer value) {
        this.usuarioZW = value;
    }

    /**
     * Obtém o valor da propriedade cpf.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getCpf() {
        return cpf;
    }

    /**
     * Define o valor da propriedade cpf.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCpf(String value) {
        this.cpf = value;
    }

    /**
     * Obtm o valor da propriedade fotoKey.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFotoKey() {
        return fotoKey;
    }

    /**
     * Define o valor da propriedade fotoKey.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFotoKey(String fotoKey) {
        this.fotoKey = fotoKey;
    }

    public void setEmailVerificado(Boolean emailVerificado) {
        this.emailVerificado = emailVerificado;
    }

    public Boolean isEmailVerificado() {
        return emailVerificado;
    }

    public Integer getPerfilTw() {
        return perfilTw;
    }

    public void setPerfilTw(Integer perfilTw) {
        this.perfilTw = perfilTw;
    }
}

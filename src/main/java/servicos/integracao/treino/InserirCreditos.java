
package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for inserirCreditos complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="inserirCreditos">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="colaborador" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="creditos" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="recibozw" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="dataExpiracao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "inserirCreditos", propOrder = {
    "key",
    "colaborador",
    "creditos",
    "recibozw",
    "dataExpiracao"
})
public class InserirCreditos {

    protected String key;
    protected Integer colaborador;
    protected Integer creditos;
    protected Integer recibozw;
    protected String dataExpiracao;

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the colaborador property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getColaborador() {
        return colaborador;
    }

    /**
     * Sets the value of the colaborador property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setColaborador(Integer value) {
        this.colaborador = value;
    }

    /**
     * Gets the value of the creditos property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCreditos() {
        return creditos;
    }

    /**
     * Sets the value of the creditos property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCreditos(Integer value) {
        this.creditos = value;
    }

    /**
     * Gets the value of the recibozw property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getRecibozw() {
        return recibozw;
    }

    /**
     * Sets the value of the recibozw property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setRecibozw(Integer value) {
        this.recibozw = value;
    }

    /**
     * Gets the value of the dataExpiracao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataExpiracao() {
        return dataExpiracao;
    }

    /**
     * Sets the value of the dataExpiracao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataExpiracao(String value) {
        this.dataExpiracao = value;
    }

}

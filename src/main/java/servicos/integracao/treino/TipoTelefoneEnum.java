
package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for tipoTelefoneEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="tipoTelefoneEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="FIXO"/>
 *     &lt;enumeration value="CELULAR"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "tipoTelefoneEnum")
@XmlEnum
public enum TipoTelefoneEnum {

    FIXO,
    CELULAR;

    public String value() {
        return name();
    }

    public static TipoTelefoneEnum fromValue(String v) {
        return valueOf(v);
    }

}

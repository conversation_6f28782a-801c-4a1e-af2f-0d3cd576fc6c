
package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for professorSintetico complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="professorSintetico">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ativo" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="avatar" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cobrarCreditos" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoColaborador" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="codigoPessoa" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="colaboradorSimples" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="cref" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="empresa" type="{http://webservice.pacto.com.br/}empresa" minOccurs="0"/>
 *         &lt;element name="fotoKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nome" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="personaInterno" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="personal" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="pessoa" type="{http://webservice.pacto.com.br/}pessoa" minOccurs="0"/>
 *         &lt;element name="posPago" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="professorTW" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="total" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/>
 *         &lt;element name="uriImagem" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="versao" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "professorSintetico", propOrder = {
    "ativo",
    "avatar",
    "cobrarCreditos",
    "codigo",
    "codigoColaborador",
    "codigoPessoa",
    "colaboradorSimples",
    "cref",
    "email",
    "empresa",
    "fotoKey",
    "nome",
    "personaInterno",
    "personal",
    "pessoa",
    "posPago",
    "professorTW",
    "total",
    "uriImagem",
    "versao",
    "codigoAcesso"
})
public class ProfessorSintetico {

    protected boolean ativo;
    protected String avatar;
    protected Boolean cobrarCreditos;
    protected Integer codigo;
    protected Integer codigoColaborador;
    protected Integer codigoPessoa;
    protected Boolean colaboradorSimples;
    protected String cref;
    protected String email;
    protected Empresa empresa;
    protected String fotoKey;
    protected String nome;
    protected Boolean personaInterno;
    protected Boolean personal;
    protected Pessoa pessoa;
    protected boolean posPago;
    protected Boolean professorTW;
    protected Double total;
    protected String uriImagem;
    protected Integer versao;
    protected String codigoAcesso;

    /**
     * Gets the value of the ativo property.
     * 
     */
    public boolean isAtivo() {
        return ativo;
    }

    /**
     * Sets the value of the ativo property.
     * 
     */
    public void setAtivo(boolean value) {
        this.ativo = value;
    }

    /**
     * Gets the value of the avatar property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAvatar() {
        return avatar;
    }

    /**
     * Sets the value of the avatar property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAvatar(String value) {
        this.avatar = value;
    }

    /**
     * Gets the value of the cobrarCreditos property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isCobrarCreditos() {
        return cobrarCreditos;
    }

    /**
     * Sets the value of the cobrarCreditos property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setCobrarCreditos(Boolean value) {
        this.cobrarCreditos = value;
    }

    /**
     * Gets the value of the codigo property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the codigoColaborador property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoColaborador() {
        return codigoColaborador;
    }

    /**
     * Sets the value of the codigoColaborador property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoColaborador(Integer value) {
        this.codigoColaborador = value;
    }

    /**
     * Gets the value of the codigoPessoa property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    /**
     * Sets the value of the codigoPessoa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCodigoPessoa(Integer value) {
        this.codigoPessoa = value;
    }

    /**
     * Gets the value of the colaboradorSimples property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isColaboradorSimples() {
        return colaboradorSimples;
    }

    /**
     * Sets the value of the colaboradorSimples property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setColaboradorSimples(Boolean value) {
        this.colaboradorSimples = value;
    }

    /**
     * Gets the value of the cref property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCref() {
        return cref;
    }

    /**
     * Sets the value of the cref property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCref(String value) {
        this.cref = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmail() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmail(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the empresa property.
     * 
     * @return
     *     possible object is
     *     {@link Empresa }
     *     
     */
    public Empresa getEmpresa() {
        return empresa;
    }

    /**
     * Sets the value of the empresa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Empresa }
     *     
     */
    public void setEmpresa(Empresa value) {
        this.empresa = value;
    }

    /**
     * Gets the value of the fotoKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFotoKey() {
        return fotoKey;
    }

    /**
     * Sets the value of the fotoKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFotoKey(String value) {
        this.fotoKey = value;
    }

    /**
     * Gets the value of the nome property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNome() {
        return nome;
    }

    /**
     * Sets the value of the nome property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNome(String value) {
        this.nome = value;
    }

    /**
     * Gets the value of the personaInterno property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPersonaInterno() {
        return personaInterno;
    }

    /**
     * Sets the value of the personaInterno property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPersonaInterno(Boolean value) {
        this.personaInterno = value;
    }

    /**
     * Gets the value of the personal property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isPersonal() {
        return personal;
    }

    /**
     * Sets the value of the personal property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setPersonal(Boolean value) {
        this.personal = value;
    }

    /**
     * Gets the value of the pessoa property.
     * 
     * @return
     *     possible object is
     *     {@link Pessoa }
     *     
     */
    public Pessoa getPessoa() {
        return pessoa;
    }

    /**
     * Sets the value of the pessoa property.
     * 
     * @param value
     *     allowed object is
     *     {@link Pessoa }
     *     
     */
    public void setPessoa(Pessoa value) {
        this.pessoa = value;
    }

    /**
     * Gets the value of the posPago property.
     * 
     */
    public boolean isPosPago() {
        return posPago;
    }

    /**
     * Sets the value of the posPago property.
     * 
     */
    public void setPosPago(boolean value) {
        this.posPago = value;
    }

    /**
     * Gets the value of the professorTW property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isProfessorTW() {
        return professorTW;
    }

    /**
     * Sets the value of the professorTW property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setProfessorTW(Boolean value) {
        this.professorTW = value;
    }

    /**
     * Gets the value of the total property.
     * 
     * @return
     *     possible object is
     *     {@link Double }
     *     
     */
    public Double getTotal() {
        return total;
    }

    /**
     * Sets the value of the total property.
     * 
     * @param value
     *     allowed object is
     *     {@link Double }
     *     
     */
    public void setTotal(Double value) {
        this.total = value;
    }

    /**
     * Gets the value of the uriImagem property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUriImagem() {
        return uriImagem;
    }

    /**
     * Sets the value of the uriImagem property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUriImagem(String value) {
        this.uriImagem = value;
    }

    /**
     * Gets the value of the versao property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getVersao() {
        return versao;
    }

    /**
     * Sets the value of the versao property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setVersao(Integer value) {
        this.versao = value;
    }

    public String getCodigoAcesso() {
        return codigoAcesso;
    }

    public void setCodigoAcesso(String codigoAcesso) {
        this.codigoAcesso = codigoAcesso;
    }
}

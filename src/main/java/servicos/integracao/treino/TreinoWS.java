
package servicos.integracao.treino;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "TreinoWS", targetNamespace = "http://webservice.pacto.com.br/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface TreinoWS {


    /**
     * 
     * @param codigo
     * @param dataAcesso
     * @param key
     * @param empresaAcesso
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarStatusAluno", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AtualizarStatusAluno")
    @ResponseWrapper(localName = "atualizarStatusAlunoResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AtualizarStatusAlunoResponse")
    public String atualizarStatusAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "dataAcesso", targetNamespace = "")
        String dataAcesso,
        @WebParam(name = "empresaAcesso", targetNamespace = "")
        Integer empresaAcesso);

    /**
     * 
     * @param clientes
     * @param titulo
     * @param mensagem
     * @param opcoes
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "enviarNotificacoes", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.EnviarNotificacoes")
    @ResponseWrapper(localName = "enviarNotificacoesResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.EnviarNotificacoesResponse")
    public String enviarNotificacoes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "titulo", targetNamespace = "")
        String titulo,
        @WebParam(name = "mensagem", targetNamespace = "")
        String mensagem,
        @WebParam(name = "opcoes", targetNamespace = "")
        String opcoes,
        @WebParam(name = "clientes", targetNamespace = "")
        String clientes);

    /**
     * 
     * @param frequenciaSemanal
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarFrequenciaSemanal", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AtualizarFrequenciaSemanal")
    @ResponseWrapper(localName = "atualizarFrequenciaSemanalResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AtualizarFrequenciaSemanalResponse")
    public String atualizarFrequenciaSemanal(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente,
        @WebParam(name = "frequenciaSemanal", targetNamespace = "")
        Integer frequenciaSemanal);

    /**
     * 
     * @param data
     * @param codigosClientes
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "realizouTreinoNovo", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.RealizouTreinoNovo")
    @ResponseWrapper(localName = "realizouTreinoNovoResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.RealizouTreinoNovoResponse")
    public String realizouTreinoNovo(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigosClientes", targetNamespace = "")
        String codigosClientes,
        @WebParam(name = "data", targetNamespace = "")
        String data);

    /**
     * 
     * @param key
     * @param matricula
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "deleteAlunoPesquisaTW", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.DeleteAlunoPesquisaTW")
    @ResponseWrapper(localName = "deleteAlunoPesquisaTWResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.DeleteAlunoPesquisaTWResponse")
    public String deleteAlunoPesquisaTW(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param codigoVenda
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "estornarFechamentoCreditos", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.EstornarFechamentoCreditos")
    @ResponseWrapper(localName = "estornarFechamentoCreditosResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.EstornarFechamentoCreditosResponse")
    public String estornarFechamentoCreditos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoVenda", targetNamespace = "")
        Integer codigoVenda);

    /**
     * 
     * @param codigo
     * @param risco
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "sincronizarRiscoCliente", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarRiscoCliente")
    @ResponseWrapper(localName = "sincronizarRiscoClienteResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarRiscoClienteResponse")
    public String sincronizarRiscoCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "risco", targetNamespace = "")
        Integer risco);

    /**
     *
     * @param professor
     * @param empresa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "alterarProfessor", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.alterarProfessor")
    @ResponseWrapper(localName = "alterarProfessorResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.alterarProfessorResponse")
    public String alterarProfessor(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "professor", targetNamespace = "")
        ProfessorSintetico professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param usuario
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "incrementarVersaoCliente", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.IncrementarVersaoCliente")
    @ResponseWrapper(localName = "incrementarVersaoClienteResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.IncrementarVersaoClienteResponse")
    public String incrementarVersaoCliente(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "usuario", targetNamespace = "")
        UsuarioZW usuario);

    /**
     * 
     * @param key
     * @param matricula
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "excluirAlunoMatricula", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ExcluirAlunoMatricula")
    @ResponseWrapper(localName = "excluirAlunoMatriculaResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ExcluirAlunoMatriculaResponse")
    public String excluirAlunoMatricula(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterDataUltimaAtualizacao", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterDataUltimaAtualizacao")
    @ResponseWrapper(localName = "obterDataUltimaAtualizacaoResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterDataUltimaAtualizacaoResponse")
    public String obterDataUltimaAtualizacao(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param crossfit
     * @param key
     * @param matricula
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "atualizarCrossfit", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AtualizarCrossfit")
    @ResponseWrapper(localName = "atualizarCrossfitResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AtualizarCrossfitResponse")
    public String atualizarCrossfit(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "matricula", targetNamespace = "")
        Integer matricula,
        @WebParam(name = "crossfit", targetNamespace = "")
        Boolean crossfit);

    /**
     * 
     * @param codigo
     * @param nrComprados
     * @param saldo
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "sincronizarCreditosAluno", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarCreditosAluno")
    @ResponseWrapper(localName = "sincronizarCreditosAlunoResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarCreditosAlunoResponse")
    public String sincronizarCreditosAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "saldo", targetNamespace = "")
        Integer saldo,
        @WebParam(name = "nrComprados", targetNamespace = "")
        Integer nrComprados);

    /**
     * 
     * @param aluno
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "addAlunoPesquisaTW", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AddAlunoPesquisaTW")
    @ResponseWrapper(localName = "addAlunoPesquisaTWResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AddAlunoPesquisaTWResponse")
    public String addAlunoPesquisaTW(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "aluno", targetNamespace = "")
        String aluno);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterTodosProgramasAlunos", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterTodosProgramasAlunos")
    @ResponseWrapper(localName = "obterTodosProgramasAlunosResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterTodosProgramasAlunosResponse")
    public String obterTodosProgramasAlunos(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param configuracao
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterConfiguracaoSistema", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterConfiguracaoSistema")
    @ResponseWrapper(localName = "obterConfiguracaoSistemaResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterConfiguracaoSistemaResponse")
    public String obterConfiguracaoSistema(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "configuracao", targetNamespace = "")
        String configuracao);

    /**
     * 
     * @param recibozw
     * @param dataExpiracao
     * @param creditos
     * @param colaborador
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "inserirCreditos", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.InserirCreditos")
    @ResponseWrapper(localName = "inserirCreditosResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.InserirCreditosResponse")
    public String inserirCreditos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "colaborador", targetNamespace = "")
        Integer colaborador,
        @WebParam(name = "creditos", targetNamespace = "")
        Integer creditos,
        @WebParam(name = "recibozw", targetNamespace = "")
        Integer recibozw,
        @WebParam(name = "dataExpiracao", targetNamespace = "")
        String dataExpiracao);

    /**
     * 
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "obterTodosAlunos", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterTodosAlunos")
    @ResponseWrapper(localName = "obterTodosAlunosResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ObterTodosAlunosResponse")
    public String obterTodosAlunos(
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigo
     * @param key
     * @param parq
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "sincronizarParq", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarParq")
    @ResponseWrapper(localName = "sincronizarParqResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarParqResponse")
    public String sincronizarParq(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "parq", targetNamespace = "")
        String parq);

    /**
     * 
     * @param recibozw
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "estornarCreditos", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.EstornarCreditos")
    @ResponseWrapper(localName = "estornarCreditosResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.EstornarCreditosResponse")
    public String estornarCreditos(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "recibozw", targetNamespace = "")
        Integer recibozw);

    /**
     * 
     * @param codigoCliente
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "excluirAluno", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ExcluirAluno")
    @ResponseWrapper(localName = "excluirAlunoResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ExcluirAlunoResponse")
    public String excluirAluno(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoCliente", targetNamespace = "")
        Integer codigoCliente);

    /**
     * 
     * @param empresa
     * @param professor
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "adicionarPersonal", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AdicionarPersonal")
    @ResponseWrapper(localName = "adicionarPersonalResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.AdicionarPersonalResponse")
    public String adicionarPersonal(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "professor", targetNamespace = "")
        ProfessorSintetico professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     * 
     * @param usuario
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "sincronizarUsuario", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarUsuario")
    @ResponseWrapper(localName = "sincronizarUsuarioResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarUsuarioResponse")
    public String sincronizarUsuario(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "usuario", targetNamespace = "")
        UsuarioZW usuario);

    /**
     * 
     * @param empresa
     * @param professor
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "sincronizarProfessor", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarProfessor")
    @ResponseWrapper(localName = "sincronizarProfessorResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.SincronizarProfessorResponse")
    public String sincronizarProfessor(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "professor", targetNamespace = "")
        ProfessorSintetico professor,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa);

    /**
     *
     * @param codigosPessoa
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "excluirAlunosOrfaos", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ExcluirAlunosOrfaos")
    @ResponseWrapper(localName = "excluirAlunosOrfaosResponse", targetNamespace = "http://webservice.pacto.com.br/", className = "servicos.integracao.treino.ExcluirAlunosOrfaosResponse")
    public String excluirAlunosOrfaos(
            @WebParam(name = "key", targetNamespace = "")
                    String key,
            @WebParam(name = "codigosPessoa", targetNamespace = "")
                    String codigosPessoa);

}

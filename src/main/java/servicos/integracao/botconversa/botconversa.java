package servicos.integracao.botconversa;

import java.sql.Timestamp;

public class botconversa {
    private String webhook;
    private String loopTime;
    private String scheduleDateTime;
    private String values;

    public String getWebhook() {
        return webhook;
    }

    public void setWebhook(String webhook) {
        this.webhook = webhook;
    }

    public String getLoopTime() {
        return loopTime;
    }

    public void setLoopTime(String loopTime) {
        this.loopTime = loopTime;
    }

    public String getScheduleDateTime() {
        return scheduleDateTime;
    }

    public void setScheduleDateTime(String scheduleDateTime) {
        this.scheduleDateTime = scheduleDateTime;
    }

    public String getValues() {
        return values;
    }

    public void setValues(String values) {
        this.values = values;
    }
}

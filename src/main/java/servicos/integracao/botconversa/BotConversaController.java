package servicos.integracao.botconversa;

import com.google.gson.Gson;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.crm.ConfiguracaoIntegracaoBotConversaVO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.financeiro.enumerador.TagReguaCobrancaPactoPayEnum;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.vendas.VendasConfig;
import org.apache.http.HttpHeaders;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.sms.Message;

import java.sql.Connection;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BotConversaController extends SuperControle {


    private final static String URI_API = getUrlMidiaSocial();  // "https://a47f-186-202-33-242.sa.ngrok.io";
    private final String key;
    private Connection con;
    private MalaDiretaVO malaDiretaVO;
    private PactoPayComunicacaoVO pactoPayComunicacaoVO;

    public BotConversaController(String key, MalaDiretaVO malaDiretaVO, Connection con) {
        this.con = con;
        this.key = key;
        this.malaDiretaVO = malaDiretaVO;
    }

    public BotConversaController(String key, PactoPayComunicacaoVO pactoPayComunicacaoVO, Connection con) {
        this.con = con;
        this.key = key;
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }

    public BotConversaController( String key, Connection con) {
        this.con = con;
        this.key = key;
    }

    private static synchronized List<String> converterParaTemplateParceiro(String template) {
        Matcher matcher = Pattern.compile("(\\{\\{([^}]*)\\}\\})").matcher(template);
        List<String> lstTags = new ArrayList<>();
        while (matcher.find()) {
            lstTags.add(matcher.group(2).toString());
        }
        return lstTags;
    }

    public String personalizarTag(List<Message> messageList) throws Exception {
        String json;
        VendasConfig vendasConfigDAO;
        vendasConfigDAO = new VendasConfig(getCon());
        try {
            List<itemBotconversaJson> listJson = new ArrayList<>();

            messageList.forEach(itemTag -> {
                itemBotconversaJson itemJson = new itemBotconversaJson();
                itemJson.setNome((Uteis.formatarNome(Uteis.getPrimeiroNome( itemTag.getNome()))));
                itemJson.setTelefone(itemTag.getNumero());
                String tags[];
                if (itemTag.getMsg().contains(";")) {
                    tags = itemTag.getMsg().split(";");
                    for (String tag : tags) {

                        switch (tag.toString()) {
                            case "TAG_EVENTO":
                                try {
                                    itemJson.setEvento("");
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                break;
                            case "TAG_PAGONLINE":
                                try {
                                    itemJson.setLink(vendasConfigDAO.tagPagamento(key, getMalaDiretaVO().getEmpresa().getCodigo(), itemTag.getCodigoCliente(), true));
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                break;
                            case "TAG_CADCARTAO":
                                try {
                                    itemJson.setLink(vendasConfigDAO.tagPagamento(key, getMalaDiretaVO().getEmpresa().getCodigo(), itemTag.getCodigoCliente(), false));
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                                break;
                        }
                    }
                }
                listJson.add(itemJson);
            });
            json = new Gson().toJson(listJson);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return json;
    }

    public botconversa setEnviaTemplate(String webHook, List<Message> messageList) throws Exception {
        botconversa templ = new botconversa();
        templ.setWebhook(webHook);
        Timestamp agora = Timestamp.from(Instant.now().atZone(ZoneId.of("America/Sao_Paulo")).toInstant());
        templ.setScheduleDateTime(agora.toString().replace(" ", "T"));
        if (this.pactoPayComunicacaoVO != null &&
                !UteisValidacao.emptyNumber(this.pactoPayComunicacaoVO.getCodigo())) {
            templ.setValues(personalizarTagsRegua(messageList));
        } else {
            try {
                templ.setValues(personalizarTag(messageList));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return templ;
    }

    public String personalizarTagsRegua(List<Message> messageList) throws Exception {
        JSONArray jsonArray = new JSONArray();
        try {
            messageList.forEach(itemTag -> {
                JSONObject itemJson = new JSONObject();

                itemJson.put(TagReguaCobrancaPactoPayEnum.CLIENTE_TELEFONE.getTag(), itemTag.getNumero());

                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.CLIENTE_NOME.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.CLIENTE_NOME.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.CLIENTE_NOME.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.CLIENTE_PRIMEIRO_NOME.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.CLIENTE_PRIMEIRO_NOME.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.CLIENTE_PRIMEIRO_NOME.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.LINK_VENDAS_ONLINE.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_COMPROVANTE_CANCELAMENTO.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_COMPROVANTE_CANCELAMENTO.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_COMPROVANTE_CANCELAMENTO.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_LINK_RECIBO.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_LINK_RECIBO.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_LINK_RECIBO.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_VALOR.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_VALOR.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_VALOR.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_NSU.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_NSU.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_NSU.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_AUTORIZACAO.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_AUTORIZACAO.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_AUTORIZACAO.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA_CANCELAMENTO.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA_CANCELAMENTO.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_DATA_CANCELAMENTO.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_COD_RETORNO.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_COD_RETORNO.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_COD_RETORNO.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_MOTIVO.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_MOTIVO.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_MOTIVO.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.TRANSACAO_ULTIMOS_DIGITOS.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.TRANSACAO_ULTIMOS_DIGITOS.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.TRANSACAO_ULTIMOS_DIGITOS.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.PARCELAS.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.PARCELAS.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.PARCELAS.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.CARTAO_ULTIMOS_DIGITOS.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.CARTAO_ULTIMOS_DIGITOS.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.CARTAO_ULTIMOS_DIGITOS.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.CARTAO_BANDEIRA.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.CARTAO_BANDEIRA.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.CARTAO_BANDEIRA.getTag()));
                }
                if (pactoPayComunicacaoVO.getTagsJSON().has(TagReguaCobrancaPactoPayEnum.CARTAO_VALIDADE.getTag())) {
                    itemJson.put(TagReguaCobrancaPactoPayEnum.CARTAO_VALIDADE.getNomeEnvio(), pactoPayComunicacaoVO.getTagsJSON().getString(TagReguaCobrancaPactoPayEnum.CARTAO_VALIDADE.getTag()));
                }
                jsonArray.put(itemJson);
            });
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return jsonArray.toString();
    }

    public void gravaHistoricoContato(Map<String,String> body, String retorno, ConfiguracaoIntegracaoBotConversaVO configuracaoIntegracaoBotConversaVO) throws Exception {
        try {
            String msgBotConversa = "\nDisparo de fluxo GymBot, \"" + configuracaoIntegracaoBotConversaVO.getDescricao() + "\", retorno: " + retorno;

            MalaDiretaVO malaDiretaVO = new MalaDiretaVO();
            HistoricoContato historicoContatoDAO = new HistoricoContato(con);

            UsuarioVO usuarioVO = new UsuarioVO();
            usuarioVO.setCodigo(Integer.valueOf(body.get("idUsuario")));
            usuarioVO.setNome(body.get("nomeUsuario"));

            malaDiretaVO.setMeioDeEnvio(MeioEnvio.GYMBOT.getCodigo());
            malaDiretaVO.setRemetente(usuarioVO);
            malaDiretaVO.setContatoAvulso(true);
            historicoContatoDAO.executarGravacaoVindoGymbot(malaDiretaVO,0,Integer.valueOf(body.get("idCliente")),msgBotConversa, configuracaoIntegracaoBotConversaVO.getCodigo());
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public String sendMessage(String key, Integer empresaId, String urlHook, List<Message> messageList) throws Exception {
        return this.sendMessageGeral(key, empresaId, urlHook, messageList, false);
    }

    public String send(String key, Integer empresaId, String urlHook, List<Message> messageList) throws Exception {
        return this.sendMessageGeral(key, empresaId, urlHook, messageList, true);
    }

    public String sendMessageGeral(String key, Integer empresaId, String urlHook,
                                   List<Message> messageList, boolean excecaoCasoErro) throws Exception {
        String urlbotconversa = "";
        try {
            botconversa messages = setEnviaTemplate(urlHook, messageList);
            RequestHttpService service = new RequestHttpService();
            Map<String, String> params = new HashMap<>();
            params.put("empresaChave", key);
            params.put("empresaId", empresaId.toString());
            params.put("SISTEMA_ZW", "true");
            params.put("Accept-Language", "pt-BR");

            params.put(HttpHeaders.CONTENT_TYPE, "application/json");
            urlbotconversa = (URI_API + "/v1/fila/chatbot");
            String body = new JSONObject(messages).toString();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlbotconversa, params, null, body, MetodoHttpEnum.POST);
            Uteis.logarDebug("GymBot: respostaHttpDTO: " + new JSONObject(respostaHttpDTO).toString());
            String resp = respostaHttpDTO.getResponse();
            Uteis.logarDebug("GymBot: Resposta: " + resp);
            JSONObject json = new JSONObject(resp);
            if (json.has("message")) {
                if (excecaoCasoErro) {
                    //todo validar resposta quando sucesso
                    String msgRestorno = json.getJSONArray("message").getJSONObject(0).getString("message");
                    if (!msgRestorno.equalsIgnoreCase("ok") &&
                            !msgRestorno.equalsIgnoreCase("Operação realizada com sucesso.")) {
                        throw new Exception(resp);
                    }
                    return resp;
                } else {
                    return json.getJSONArray("message").getJSONObject(0).getString("message");
                }
            } else if (excecaoCasoErro) {
                throw new Exception(resp);
            }
            return "Erro ao disparar GymBot: URL: " + urlbotconversa;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao disparar GymBot: URL: " + urlbotconversa);
            throw e;
        }
    }

    public MalaDiretaVO getMalaDiretaVO() {
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public PactoPayComunicacaoVO getPactoPayComunicacaoVO() {
        return pactoPayComunicacaoVO;
    }

    public void setPactoPayComunicacaoVO(PactoPayComunicacaoVO pactoPayComunicacaoVO) {
        this.pactoPayComunicacaoVO = pactoPayComunicacaoVO;
    }
}

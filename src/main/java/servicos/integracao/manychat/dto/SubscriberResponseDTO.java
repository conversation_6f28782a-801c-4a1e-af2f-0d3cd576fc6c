package servicos.integracao.manychat.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SubscriberResponseDTO {

    private String id;
    private String page_id;
    private List<String> user_refs;
    private String status;
    private String first_name;
    private String last_name;
    private String name;
    private String gender;
    private String profile_pic;
    private String locale;
    private String language;
    private String timezone;
    private String live_chat_url;
    private String last_input_text;
    private boolean optin_phone;
    private String phone;
    private boolean optin_email;
    private String email;
    private String subscribed;
    private String last_interaction;
    private String ig_last_interaction;
    private String last_seen;
    private String ig_last_seen;
    private boolean is_followup_enabled;
    private String ig_username;
    private String ig_id;
    private String whatsapp_phone;
    private boolean optin_whatsapp;
    private List<CustomFieldDTO> custom_fields;
    private List<TagDTO> tags;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPage_id() {
        return page_id;
    }

    public void setPage_id(String page_id) {
        this.page_id = page_id;
    }

    public List<String> getUser_refs() {
        return user_refs;
    }

    public void setUser_refs(List<String> user_refs) {
        this.user_refs = user_refs;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFirst_name() {
        return first_name;
    }

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getProfile_pic() {
        return profile_pic;
    }

    public void setProfile_pic(String profile_pic) {
        this.profile_pic = profile_pic;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getLive_chat_url() {
        return live_chat_url;
    }

    public void setLive_chat_url(String live_chat_url) {
        this.live_chat_url = live_chat_url;
    }

    public String getLast_input_text() {
        return last_input_text;
    }

    public void setLast_input_text(String last_input_text) {
        this.last_input_text = last_input_text;
    }

    public boolean isOptin_phone() {
        return optin_phone;
    }

    public void setOptin_phone(boolean optin_phone) {
        this.optin_phone = optin_phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public boolean isOptin_email() {
        return optin_email;
    }

    public void setOptin_email(boolean optin_email) {
        this.optin_email = optin_email;
    }

    public String getEmail() {
        if (email == null) {
            return "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSubscribed() {
        return subscribed;
    }

    public void setSubscribed(String subscribed) {
        this.subscribed = subscribed;
    }

    public String getLast_interaction() {
        return last_interaction;
    }

    public void setLast_interaction(String last_interaction) {
        this.last_interaction = last_interaction;
    }

    public String getIg_last_interaction() {
        return ig_last_interaction;
    }

    public void setIg_last_interaction(String ig_last_interaction) {
        this.ig_last_interaction = ig_last_interaction;
    }

    public String getLast_seen() {
        return last_seen;
    }

    public void setLast_seen(String last_seen) {
        this.last_seen = last_seen;
    }

    public String getIg_last_seen() {
        return ig_last_seen;
    }

    public void setIg_last_seen(String ig_last_seen) {
        this.ig_last_seen = ig_last_seen;
    }

    public boolean isIs_followup_enabled() {
        return is_followup_enabled;
    }

    public void setIs_followup_enabled(boolean is_followup_enabled) {
        this.is_followup_enabled = is_followup_enabled;
    }

    public String getIg_username() {
        return ig_username;
    }

    public void setIg_username(String ig_username) {
        this.ig_username = ig_username;
    }

    public String getIg_id() {
        return ig_id;
    }

    public void setIg_id(String ig_id) {
        this.ig_id = ig_id;
    }

    public String getWhatsapp_phone() {
        return whatsapp_phone;
    }

    public void setWhatsapp_phone(String whatsapp_phone) {
        this.whatsapp_phone = whatsapp_phone;
    }

    public boolean isOptin_whatsapp() {
        return optin_whatsapp;
    }

    public void setOptin_whatsapp(boolean optin_whatsapp) {
        this.optin_whatsapp = optin_whatsapp;
    }

    public List<CustomFieldDTO> getCustom_fields() {
        return custom_fields;
    }

    public void setCustom_fields(List<CustomFieldDTO> custom_fields) {
        this.custom_fields = custom_fields;
    }

    public List<TagDTO> getTags() {
        return tags;
    }

    public void setTags(List<TagDTO> tags) {
        this.tags = tags;
    }
}

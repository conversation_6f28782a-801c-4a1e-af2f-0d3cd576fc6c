package servicos.integracao.manychat.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SubscriberDTO {

    private Long subscriber_id;
    private String first_name;
    private String last_name;
    private String phone;
    private String whatsapp_phone;
    private String email;

    private String gender;
    private Boolean has_opt_in_sms;
    private Boolean has_opt_in_email;
    private String consent_phrase;

    public Long getSubscriber_id() {
        return subscriber_id;
    }

    public void setSubscriber_id(Long subscriber_id) {
        this.subscriber_id = subscriber_id;
    }

    public String getFirst_name() {
        return first_name;
    }

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWhatsapp_phone() {
        return whatsapp_phone;
    }

    public void setWhatsapp_phone(String whatsapp_phone) {
        this.whatsapp_phone = whatsapp_phone;
    }

    public String getEmail() {
        if (email == null) {
            return "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Boolean getHas_opt_in_sms() {
        return has_opt_in_sms;
    }

    public void setHas_opt_in_sms(Boolean has_opt_in_sms) {
        this.has_opt_in_sms = has_opt_in_sms;
    }

    public Boolean getHas_opt_in_email() {
        return has_opt_in_email;
    }

    public void setHas_opt_in_email(Boolean has_opt_in_email) {
        this.has_opt_in_email = has_opt_in_email;
    }

    public String getConsent_phrase() {
        return consent_phrase;
    }

    public void setConsent_phrase(String consent_phrase) {
        this.consent_phrase = consent_phrase;
    }
}

package servicos.integracao.manychat.impl;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import edu.emory.mathcs.backport.java.util.Arrays;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.LogIntegracoesVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogIntegracoes;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.manychat.dto.FieldDTO;
import servicos.integracao.manychat.dto.SubscriberCustomFieldsDTO;
import servicos.integracao.manychat.dto.SubscriberResponseDTO;
import servicos.integracao.manychat.dto.TagDTO;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ThreadIntegracaoManyChat implements Runnable, AutoCloseable {

    private final String HEADER_AUTHORIZATION = "Authorization";
    private final String HEADER_ACCEPT = "accept";
    private final String HEADER_CONTENT_TYPE = "Content-Type";
    private final String HEADER_APPLICATION_JSON = "application/json";
    private final String API_URL = "https://api.manychat.com/fb";
    private final String ENDPOINT_SUBSCRIBER = "/subscriber";
    private final String ENDPOINT_PAGE = "/page";

    private final Connection con;
    private Cliente clienteDAO;
    private LogIntegracoes logIntegracoesDAO;
    private ClienteVO clienteVO;
    private EmpresaVO empresaVO;
    private String tokenApi;
    private Map<String, Integer> mapCustomFieldId = new HashMap<>();

    public ThreadIntegracaoManyChat(Connection con, ClienteVO clienteVO) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        this.clienteDAO = new Cliente(this.con);
        this.logIntegracoesDAO = new LogIntegracoes(this.con);

        this.clienteVO = clienteVO;
        this.empresaVO = clienteVO.getEmpresa();
        this.tokenApi = empresaVO.getIntegracaoManyChatTokenApi();
    }

    private void getCustomFileds() throws Exception {
        String url = API_URL + ENDPOINT_PAGE + "/getCustomFields";

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, null, getHeaders(), ExecuteRequestHttpService.METODO_GET, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);
        if (jsonRetorno.has("data")) {
            JSONArray data = jsonRetorno.getJSONArray("data");
            for (int i = 0; i < data.length(); i++) {
                JSONObject customField = data.getJSONObject(i);
                mapCustomFieldId.put(customField.getString("name").toUpperCase(), customField.getInt("id"));
            }
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("urlRequisicao", url);
            jsonObject.put("responseApi", jsonRetorno);
            handleError("getCustomFields", "Erro ao buscar custom fields do ManyChat", url, new JSONObject(),jsonObject);
        }

        if (!mapCustomFieldId.containsKey("CPF")) {
            createCustomFiled("CPF");
        }
        if (!mapCustomFieldId.containsKey("UNIDADE")) {
            createCustomFiled("UNIDADE");
        }
        if (!mapCustomFieldId.containsKey("ID_ALUNO")) {
            createCustomFiled("ID_ALUNO");
        }
        if (!mapCustomFieldId.containsKey("PLANO")) {
            createCustomFiled("PLANO");
        }
    }

    private void createCustomFiled(String fieldName) throws Exception {
        if (UteisValidacao.emptyString(fieldName)) {
            throw new Exception("Nome do campo customizado não informado");
        }

        String url = API_URL + ENDPOINT_PAGE + "/createCustomField";

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("caption", fieldName.toUpperCase());
        jsonBody.put("type", TypeCustomFieldEnum.TEXT.getId());
        jsonBody.put("description", "");

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, jsonBody.toString(), getHeaders(), ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);
        if (jsonRetorno.has("data") && jsonRetorno.getJSONObject("data").has("field")) {
            JSONObject customField = jsonRetorno.getJSONObject("data").getJSONObject("field");
            mapCustomFieldId.put(customField.getString("name").toUpperCase(), customField.getInt("id"));
        } else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("urlRequisicao", url);
            jsonObject.put("responseApi", jsonRetorno);
            handleError("createCustomField", "Erro criar custom fields do ManyChat - " + fieldName, url, jsonBody, jsonObject);
        }
    }

    @Override
    public void run() {
        try {
            if (UteisValidacao.emptyString(tokenApi)) {
                throw new Exception("Token da API ManyChat não informado");
            }

            String cpf = clienteVO.getPessoa().getCfp();
            if (UteisValidacao.emptyString(cpf)) {
                return;
            }

            if (UteisValidacao.emptyString(tokenApi)) {
                throw new Exception("Token da API ManyChat não informado");
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta("select dataSincronizacaoManyChat from cliente where codigo = " + clienteVO.getCodigo(), con);
            if (rs.next() && rs.getTimestamp("dataSincronizacaoManyChat") != null) {
                long diffSeconds = (Calendario.hoje().getTime() - rs.getTimestamp("dataSincronizacaoManyChat").getTime());
                if (diffSeconds < 1) {
                    Uteis.logarDebug(">>>>>>>>>>>>>>>>>>>>>>>>>>>> MANY CHAT NÃO SINCRONIZAR - diferenþa de segundos da ultima sincronização: " + diffSeconds + "ms");
                    return;
                }
            }

            clienteVO = clienteDAO.consultarPorChavePrimaria(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
            clienteVO.setEmpresa(empresaVO);

            if (UteisValidacao.emptyString(clienteVO.getSituacao())) {
                return;
            }

            if (!SituacaoClienteEnum.ATIVO.getCodigo().equals(clienteVO.getSituacao())
                    && !SituacaoClienteEnum.TRANCADO.getCodigo().equals(clienteVO.getSituacao())
                    && !SituacaoClienteEnum.INATIVO.getCodigo().equals(clienteVO.getSituacao())
                    && !SituacaoClienteEnum.VISITANTE.getCodigo().equals(clienteVO.getSituacao())) {
                return;
            }

            if (mapCustomFieldId.isEmpty()) {
                getCustomFileds();
            }

            SubscriberResponseDTO subscriberResponseDTO = null;
            if (UteisValidacao.emptyNumber(clienteVO.getIdManyChat())) {
                subscriberResponseDTO = findSubscriberByCPF(clienteVO.getPessoa().getCfp());
                if (subscriberResponseDTO != null) {
                    clienteVO.setIdManyChat(Long.parseLong(subscriberResponseDTO.getId()));
                    clienteDAO.updateIdManyChat(clienteVO.getCodigo(), clienteVO.getIdManyChat());
                }
            } else {
                subscriberResponseDTO = findSubscriberById(clienteVO.getIdManyChat());
            }


            if (validarSintuacaoClienteSincronizar(clienteVO, subscriberResponseDTO)) {
                subscriberResponseDTO = createOrUpdateSubscriber(clienteVO, subscriberResponseDTO);
                updateCustomFields(clienteVO);
                updateTags(clienteVO, subscriberResponseDTO);
            }
        } catch (Exception e) {
             salvarLog(e.getMessage(), "");
        } finally {
            this.close();
        }
    }

    private boolean validarSintuacaoClienteSincronizar(ClienteVO clienteVO, SubscriberResponseDTO subscriberResponseDTO) {
        if (!UteisValidacao.emptyNumber(clienteVO.getIdManyChat())) {
            return true;
        } else if (clienteVO.getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())
                && Calendario.igual(clienteVO.getSituacaoClienteSinteticoVO().getDataLancamentoContrato(), Calendario.hoje())) {
            return true;
        } else if (clienteVO.getSituacao().equals(SituacaoClienteEnum.VISITANTE.getCodigo())
                && Calendario.igual(clienteVO.getPessoa().getDataCadastro(), Calendario.hoje())) {
            return true;
        }

        return false;
    }

    private SubscriberResponseDTO createOrUpdateSubscriber(ClienteVO clienteVO, SubscriberResponseDTO subscriberResponseDTO) throws Exception {
        JSONObject subscriberJson = montarSubscriberDTO(clienteVO);

        if (subscriberResponseDTO != null) {
            if (!UteisValidacao.emptyString(subscriberResponseDTO.getEmail()) && subscriberResponseDTO.getEmail().equals(subscriberJson.optString("email"))) {
                subscriberJson.remove("email");
            }
            if (!UteisValidacao.emptyString(subscriberResponseDTO.getPhone()) && subscriberResponseDTO.getPhone().equals(subscriberJson.optString("phone"))) {
                subscriberJson.remove("phone");
            }
        }

        String url = API_URL + ENDPOINT_SUBSCRIBER;
        String operacao = UteisValidacao.emptyNumber(subscriberJson.getInt("subscriber_id")) ? "createSubscriber" : "updateSubscriber";
        url += "/" + operacao;

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, subscriberJson.toString(), getHeaders(), ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);

        if (jsonRetorno.optString("status").equals("success") && jsonRetorno.has("data")) {
            subscriberResponseDTO = JSONMapper.getObject(jsonRetorno.getJSONObject("data"), SubscriberResponseDTO.class);
            clienteVO.setIdManyChat(Long.parseLong(subscriberResponseDTO.getId()));
            clienteDAO.updateIdManyChat(clienteVO.getCodigo(), clienteVO.getIdManyChat());
            salvarLog("Cliente MAT: " + clienteVO.getCodigoMatricula() + " sincronizado com sucesso", retorno);
            return subscriberResponseDTO;
        } else {
            handleError(operacao, "Erro ao sincronizar cliente no ManyChat", url, subscriberJson, jsonRetorno);
            throw new Exception("Erro ao sincronizar cliente no ManyChat");
        }
    }

    private void handleError(String operacao, String mensagemErro, String url, JSONObject jsonBodyEnviado, JSONObject jsonRetorno) {
        mensagemErro = String.format("Cliente MAT: %d situacao: %s Erro: %s", clienteVO.getCodigoMatricula(), clienteVO.getSituacao(), mensagemErro);
        Uteis.logarDebug("INTEGRAÇÃO MANY CHAT - " + mensagemErro);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("operacao", operacao);
        jsonObject.put("urlRequisicao", url);
        jsonObject.put("jsonBodyEnviado", jsonBodyEnviado);
        jsonObject.put("jsonRetorno", jsonRetorno);
        salvarLog(mensagemErro, jsonObject.toString());
    }

    private void updateCustomFields(ClienteVO clienteVO) throws Exception {
        if (UteisValidacao.emptyNumber(clienteVO.getIdManyChat())) {
            throw new Exception("Id do cliente no ManyChat não informado para sincronizar custom fileds");
        }

        SubscriberCustomFieldsDTO subscriberCustomFieldsDTO = montarSubscriberCustomFieldsDTO(clienteVO);

        String url = API_URL + ENDPOINT_SUBSCRIBER + "/setCustomFields";

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, new JSONObject(subscriberCustomFieldsDTO).toString(), getHeaders(), ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);
        if (!jsonRetorno.optString("status").equals("success")) {
            handleError("setCustomFields", "Erro ao sincronizar custom fields", url, new JSONObject(subscriberCustomFieldsDTO), jsonRetorno);
            throw new Exception("Erro ao sincronizar custom fields");
        }
    }

    private void updateTags(ClienteVO clienteVO, SubscriberResponseDTO subscriberResponseDTO) throws Exception {
        if (UteisValidacao.emptyNumber(clienteVO.getIdManyChat())) {
            throw new Exception("Id do cliente no ManyChat no informado para sincronizar tags");
        }

        TagDTO tagUnidade = new TagDTO();
        tagUnidade.setId(Integer.parseInt(empresaVO.getIntegracaoManyChatTagUnidade().split(";")[0]));
        tagUnidade.setName(empresaVO.getIntegracaoManyChatTagUnidade().split(";")[1]);

        List<String> tagsAdicionar = new ArrayList<>();
        List<String> tagsRemover = new ArrayList<>();

        String tagSituacao;
        if (clienteVO.getSituacao().equals(SituacaoClienteEnum.VISITANTE.getCodigo())) {
            tagSituacao = TagSituacaoEnum.LEAD.getDescricao();
        } else if (clienteVO.getSituacao().equals(SituacaoClienteEnum.INATIVO.getCodigo())) {
            tagSituacao = TagSituacaoEnum.EX_ALUNO.getDescricao();
        } else if (clienteVO.getSituacao().equals(SituacaoClienteEnum.ATIVO.getCodigo())
                || clienteVO.getSituacao().equals(SituacaoClienteEnum.TRANCADO.getCodigo())){
            tagSituacao = TagSituacaoEnum.ALUNO.getDescricao();
        } else {
            throw new Exception("Falha ao obter situação do cliente para sincronizar tags no ManyChat");
        }

        // tag situacao
        boolean possuiTagSituacao = subscriberResponseDTO.getTags().stream().anyMatch(t -> t.getName().equals(tagSituacao));
        if (!possuiTagSituacao) {
            tagsAdicionar.add(tagSituacao);
        }
        List<String> listaTagsSituacao = TagSituacaoEnum.getDescricoes();
        subscriberResponseDTO.getTags().forEach(tagDTO -> {
            if (listaTagsSituacao.contains(tagDTO.getName()) && !tagDTO.getName().equals(tagSituacao)) {
                tagsRemover.add(tagDTO.getName());
            }
        });

        // tag unidade
        boolean possuiTagUnidade = subscriberResponseDTO.getTags().stream().anyMatch(t -> t.getName().equals(tagUnidade.getName()));
        if (!possuiTagUnidade) {
            tagsAdicionar.add(tagUnidade.getName());
        }

        for (String tagAdicionar: tagsAdicionar) {
            addOrRemoveTagByName(clienteVO, tagAdicionar, "addTagByName");
        }

        for (String tagRemover: tagsRemover) {
            addOrRemoveTagByName(clienteVO, tagRemover, "removeTagByName");
        }
    }

    private void addOrRemoveTagByName(ClienteVO clienteVO, String tag, String operacao) throws Exception {
        String url = API_URL + ENDPOINT_SUBSCRIBER + "/" + operacao;

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("subscriber_id", clienteVO.getIdManyChat());
        jsonBody.put("tag_name", tag);

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, jsonBody.toString(), getHeaders(), ExecuteRequestHttpService.METODO_POST, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);

        if (jsonRetorno.optString("status").equals("erro")) {
            String msgErro = String.format("Erro ao sincronizar a tag: %s", tag);
            handleError(operacao, msgErro, url, jsonBody, jsonRetorno);
            throw new Exception(msgErro);
        }
    }

    private JSONObject montarSubscriberDTO(ClienteVO clienteVO) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subscriber_id", clienteVO.getIdManyChat());

        if (!UteisValidacao.emptyString(clienteVO.getPessoa().getNome())) {
            String[] nameParts = clienteVO.getPessoa().getNome().split(" ");
            jsonObject.put("first_name", nameParts[0]);
            if (nameParts.length > 1) {
                jsonObject.put("last_name", Arrays.asList(nameParts).subList(1, nameParts.length).stream().collect(Collectors.joining(" ")));
            }
        }
        if (UteisValidacao.emptyString(clienteVO.getPessoa().getSexo())) {
            jsonObject.put("gender", clienteVO.getPessoa().getSexo());
        }

        // telefone
        String numeroCelular = "";
        for (TelefoneVO telVO : clienteVO.getPessoa().getTelefoneVOs()) {
            if (telVO.getTipoTelefone().equals("CE")) {
                numeroCelular = "55" + Formatador.removerMascara(telVO.getNumero());
            }
        }
        if (!UteisValidacao.emptyString(numeroCelular)) {
            jsonObject.put("phone", numeroCelular);
            jsonObject.put("whatsapp_phone", numeroCelular);
            jsonObject.put("has_opt_in_sms", false);
        }

        // email
        if (!UteisValidacao.emptyList(clienteVO.getPessoa().getEmailVOs())) {
            jsonObject.put("email", clienteVO.getPessoa().getEmailVOs().get(0).getEmail());
            jsonObject.put("has_opt_in_email", clienteVO.getPessoa().getEmailVOs().get(0).getEmailCorrespondencia());
        }

        return jsonObject;
    }

    private SubscriberCustomFieldsDTO montarSubscriberCustomFieldsDTO(ClienteVO clienteVO) throws Exception {
        SubscriberCustomFieldsDTO subscriberCustomFieldsDTO = new SubscriberCustomFieldsDTO();
        subscriberCustomFieldsDTO.setSubscriber_id(clienteVO.getIdManyChat());

        FieldDTO fieldCpf = new FieldDTO();
        fieldCpf.setField_id(mapCustomFieldId.get("CPF"));
        fieldCpf.setField_value(clienteVO.getPessoa().getCfp());
        fieldCpf.setField_name("CPF");
        subscriberCustomFieldsDTO.getFields().add(fieldCpf);

        FieldDTO fieldUnidade = new FieldDTO();
        fieldUnidade.setField_id(mapCustomFieldId.get("UNIDADE"));
        fieldUnidade.setField_value(clienteVO.getEmpresa().getNome());
        fieldUnidade.setField_name("UNIDADE");
        subscriberCustomFieldsDTO.getFields().add(fieldUnidade);

        FieldDTO fieldIdAluno = new FieldDTO();
        fieldIdAluno.setField_id(mapCustomFieldId.get("ID_ALUNO"));
        fieldIdAluno.setField_value(String.valueOf(clienteVO.getCodigoMatricula()));
        fieldIdAluno.setField_name("ID_ALUNO");
        subscriberCustomFieldsDTO.getFields().add(fieldIdAluno);

        FieldDTO fieldPlano = new FieldDTO();
        fieldPlano.setField_id(mapCustomFieldId.get("PLANO"));
        fieldPlano.setField_name("PLANO");
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT nomeplano FROM situacaoclientesinteticodw " +
                " WHERE codigocliente = " + clienteVO.getCodigo(), con);
        String nomePlano = rs.next() ? rs.getString("nomeplano") : "";
        if (!UteisValidacao.emptyString(nomePlano)) {
            fieldPlano.setField_value(nomePlano);
        } else {
            fieldPlano.setField_value("NENHUM");
        }
        subscriberCustomFieldsDTO.getFields().add(fieldPlano);

        return subscriberCustomFieldsDTO;
    }

    public SubscriberResponseDTO findSubscriberByCPF(String cpf) throws Exception {
        Map<String, String> headers = getHeaders();

        String url = API_URL + ENDPOINT_SUBSCRIBER + "/findByCustomField";
        url += "?field_id=" + mapCustomFieldId.get("CPF");
        url += "&field_value=" + cpf;

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);

        if (jsonRetorno.optString("status").equals("success") && jsonRetorno.has("data")) {
            JSONArray data = jsonRetorno.getJSONArray("data");
            for (int i = 0; i < data.length(); i++) {
                JSONObject subscriber = data.getJSONObject(i);
                return JSONMapper.getObject(subscriber, SubscriberResponseDTO.class);
            }
        } else {
            String mensagemErro = "Erro ao buscar subscriber por CPF no ManyChat";
            handleError("findByCustomField", mensagemErro, url, new JSONObject(), jsonRetorno);
            throw new Exception(mensagemErro);
        }

        return null;
    }

    public SubscriberResponseDTO findSubscriberById(Long id) throws Exception {
        Map<String, String> headers = getHeaders();

        String url = API_URL + ENDPOINT_SUBSCRIBER + "/getInfo?subscriber_id=" + id;

        String retorno = ExecuteRequestHttpService.executeHttpRequest(url, null, headers, ExecuteRequestHttpService.METODO_GET, "UTF-8");
        JSONObject jsonRetorno = new JSONObject(retorno);

        if (jsonRetorno.optString("status").equals("success") && jsonRetorno.has("data")) {
            return JSONMapper.getObject(jsonRetorno.getJSONObject("data"), SubscriberResponseDTO.class);
        } else {
            String mensagemErro = "Erro ao buscar subscriber por id no ManyChat";
            handleError("getInfo", mensagemErro + " - subscriber_id: " + id, url, new JSONObject(), jsonRetorno);
            throw new Exception(mensagemErro);
        }
    }


    public Map<String, String> getHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put(HEADER_AUTHORIZATION, "Bearer " + this.tokenApi);
        headers.put(HEADER_ACCEPT, HEADER_APPLICATION_JSON);
        headers.put(HEADER_CONTENT_TYPE, HEADER_APPLICATION_JSON);
        return headers;
    }

    private void salvarLog(String resultado, String dadosRecebidos) {
        try {
            LogIntegracoesVO log = new LogIntegracoesVO();
            log.setDataLancamento(new Date());
            log.setServico("INTEGRACAO_MANYCHAT");
            log.setResultado(resultado);
            log.setDadosRecebidos(dadosRecebidos);
            logIntegracoesDAO.incluir(log);
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao salvar log de integração ManyChat: " + e.getMessage());
        }
    }

    @Override
    public void close() {
        try {
            if (con != null && !con.isClosed()) {
                con.close();
            }
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao fechar conexão com banco de dados: " + e.getMessage());
        }
    }

    public enum TagSituacaoEnum {
        LEAD("Lead"),
        ALUNO("Aluno"),
        EX_ALUNO("Ex-Aluno");

        private String descricao;

        TagSituacaoEnum(String descricao) {
            this.descricao = descricao;
        }

        public String getDescricao() {
            return descricao;
        }

        public static List<String> getDescricoes() {
            List<String> descricoes = new ArrayList<>();
            for (TagSituacaoEnum tagSituacaoEnum : TagSituacaoEnum.values()) {
                descricoes.add(tagSituacaoEnum.getDescricao());
            }
            return descricoes;
        }
    }

    public enum TypeCustomFieldEnum {

        TEXT("text"),
        NUMBER("number"),
        DATE("date");

        TypeCustomFieldEnum(String tipo) {
            this.id = tipo;
        }
        private String id;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }
}

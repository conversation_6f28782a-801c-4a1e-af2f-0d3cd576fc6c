
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSDADITNF complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSDADITNF">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Z5_ALIQISS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z5_BASEISS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z5_CODISS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z5_DATFAT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_DESCRI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_ITEM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_NOTA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_PRCVEN" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_PRODUTO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_QTDVEN" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_SERIE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_VALDESC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z5_VALISS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z5_VALOR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSDADITNF", propOrder = {
    "z5ALIQISS",
    "z5BASEISS",
    "z5CODISS",
    "z5DATFAT",
    "z5DESCRI",
    "z5ITEM",
    "z5NOTA",
    "z5PRCVEN",
    "z5PRODUTO",
    "z5QTDVEN",
    "z5SERIE",
    "z5VALDESC",
    "z5VALISS",
    "z5VALOR"
})
public class WSDADITNF {

    @XmlElement(name = "Z5_ALIQISS")
    protected String z5ALIQISS;
    @XmlElement(name = "Z5_BASEISS")
    protected String z5BASEISS;
    @XmlElement(name = "Z5_CODISS")
    protected String z5CODISS;
    @XmlElement(name = "Z5_DATFAT", required = true)
    protected String z5DATFAT;
    @XmlElement(name = "Z5_DESCRI", required = true)
    protected String z5DESCRI;
    @XmlElement(name = "Z5_ITEM", required = true)
    protected String z5ITEM;
    @XmlElement(name = "Z5_NOTA", required = true)
    protected String z5NOTA;
    @XmlElement(name = "Z5_PRCVEN", required = true)
    protected String z5PRCVEN;
    @XmlElement(name = "Z5_PRODUTO", required = true)
    protected String z5PRODUTO;
    @XmlElement(name = "Z5_QTDVEN", required = true)
    protected String z5QTDVEN;
    @XmlElement(name = "Z5_SERIE", required = true)
    protected String z5SERIE;
    @XmlElement(name = "Z5_VALDESC")
    protected String z5VALDESC;
    @XmlElement(name = "Z5_VALISS")
    protected String z5VALISS;
    @XmlElement(name = "Z5_VALOR", required = true)
    protected String z5VALOR;

    /**
     * Gets the value of the z5ALIQISS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5ALIQISS() {
        return z5ALIQISS;
    }

    /**
     * Sets the value of the z5ALIQISS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5ALIQISS(String value) {
        this.z5ALIQISS = value;
    }

    /**
     * Gets the value of the z5BASEISS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5BASEISS() {
        return z5BASEISS;
    }

    /**
     * Sets the value of the z5BASEISS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5BASEISS(String value) {
        this.z5BASEISS = value;
    }

    /**
     * Gets the value of the z5CODISS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5CODISS() {
        return z5CODISS;
    }

    /**
     * Sets the value of the z5CODISS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5CODISS(String value) {
        this.z5CODISS = value;
    }

    /**
     * Gets the value of the z5DATFAT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5DATFAT() {
        return z5DATFAT;
    }

    /**
     * Sets the value of the z5DATFAT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5DATFAT(String value) {
        this.z5DATFAT = value;
    }

    /**
     * Gets the value of the z5DESCRI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5DESCRI() {
        return z5DESCRI;
    }

    /**
     * Sets the value of the z5DESCRI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5DESCRI(String value) {
        this.z5DESCRI = value;
    }

    /**
     * Gets the value of the z5ITEM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5ITEM() {
        return z5ITEM;
    }

    /**
     * Sets the value of the z5ITEM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5ITEM(String value) {
        this.z5ITEM = value;
    }

    /**
     * Gets the value of the z5NOTA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5NOTA() {
        return z5NOTA;
    }

    /**
     * Sets the value of the z5NOTA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5NOTA(String value) {
        this.z5NOTA = value;
    }

    /**
     * Gets the value of the z5PRCVEN property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5PRCVEN() {
        return z5PRCVEN;
    }

    /**
     * Sets the value of the z5PRCVEN property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5PRCVEN(String value) {
        this.z5PRCVEN = value;
    }

    /**
     * Gets the value of the z5PRODUTO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5PRODUTO() {
        return z5PRODUTO;
    }

    /**
     * Sets the value of the z5PRODUTO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5PRODUTO(String value) {
        this.z5PRODUTO = value;
    }

    /**
     * Gets the value of the z5QTDVEN property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5QTDVEN() {
        return z5QTDVEN;
    }

    /**
     * Sets the value of the z5QTDVEN property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5QTDVEN(String value) {
        this.z5QTDVEN = value;
    }

    /**
     * Gets the value of the z5SERIE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5SERIE() {
        return z5SERIE;
    }

    /**
     * Sets the value of the z5SERIE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5SERIE(String value) {
        this.z5SERIE = value;
    }

    /**
     * Gets the value of the z5VALDESC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5VALDESC() {
        return z5VALDESC;
    }

    /**
     * Sets the value of the z5VALDESC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5VALDESC(String value) {
        this.z5VALDESC = value;
    }

    /**
     * Gets the value of the z5VALISS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5VALISS() {
        return z5VALISS;
    }

    /**
     * Sets the value of the z5VALISS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5VALISS(String value) {
        this.z5VALISS = value;
    }

    /**
     * Gets the value of the z5VALOR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5VALOR() {
        return z5VALOR;
    }

    /**
     * Sets the value of the z5VALOR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5VALOR(String value) {
        this.z5VALOR = value;
    }

}

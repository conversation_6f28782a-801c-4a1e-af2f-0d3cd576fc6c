
package servicos.integracao.selfit.notafiscal;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSRETCNFIS complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSRETCNFIS">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSRETCNFIS" type="{http://54.207.46.24:91/}WSRETCNFIS" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSRETCNFIS", propOrder = {
    "wsretcnfis"
})
public class ARRAYOFWSRETCNFIS {

    @XmlElement(name = "WSRETCNFIS")
    protected List<WSRETCNFIS> wsretcnfis;

    /**
     * Gets the value of the wsretcnfis property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wsretcnfis property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSRETCNFIS().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSRETCNFIS }
     * 
     * 
     */
    public List<WSRETCNFIS> getWSRETCNFIS() {
        if (wsretcnfis == null) {
            wsretcnfis = new ArrayList<WSRETCNFIS>();
        }
        return this.wsretcnfis;
    }

}

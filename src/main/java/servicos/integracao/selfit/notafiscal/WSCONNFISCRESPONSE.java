
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSCONNFISCRESULT" type="{http://54.207.46.24:91/}ARRAYOFWSRETCNFIS"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wsconnfiscresult"
})
@XmlRootElement(name = "WSCONNFISCRESPONSE")
public class WSCONNFISCRESPONSE {

    @XmlElement(name = "WSCONNFISCRESULT", required = true)
    protected ARRAYOFWSRETCNFIS wsconnfiscresult;

    /**
     * Gets the value of the wsconnfiscresult property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSRETCNFIS }
     *     
     */
    public ARRAYOFWSRETCNFIS getWSCONNFISCRESULT() {
        return wsconnfiscresult;
    }

    /**
     * Sets the value of the wsconnfiscresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSRETCNFIS }
     *     
     */
    public void setWSCONNFISCRESULT(ARRAYOFWSRETCNFIS value) {
        this.wsconnfiscresult = value;
    }

}

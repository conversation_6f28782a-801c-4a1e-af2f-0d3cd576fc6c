
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSRETCNFIS complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSRETCNFIS">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="C5_NUM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CHAVGRV" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CSTATUS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="F2_CLIENTE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="F2_DOC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="F2_EMISSAO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="F2_LOJA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="F2_SERIE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LRET" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="MSGERRO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_CGC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_CHVNFE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_NOTA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_SERIE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_STATUS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_TIPOROT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSRETCNFIS", propOrder = {
    "c5NUM",
    "chavgrv",
    "cstatus",
    "f2CLIENTE",
    "f2DOC",
    "f2EMISSAO",
    "f2LOJA",
    "f2SERIE",
    "lret",
    "msgerro",
    "z4CGC",
    "z4CHVNFE",
    "z4NOTA",
    "z4SERIE",
    "z4STATUS",
    "z4TIPOROT"
})
public class WSRETCNFIS {

    @XmlElement(name = "C5_NUM")
    protected String c5NUM;
    @XmlElement(name = "CHAVGRV")
    protected String chavgrv;
    @XmlElement(name = "CSTATUS", required = true)
    protected String cstatus;
    @XmlElement(name = "F2_CLIENTE")
    protected String f2CLIENTE;
    @XmlElement(name = "F2_DOC")
    protected String f2DOC;
    @XmlElement(name = "F2_EMISSAO")
    protected String f2EMISSAO;
    @XmlElement(name = "F2_LOJA")
    protected String f2LOJA;
    @XmlElement(name = "F2_SERIE")
    protected String f2SERIE;
    @XmlElement(name = "LRET")
    protected boolean lret;
    @XmlElement(name = "MSGERRO")
    protected String msgerro;
    @XmlElement(name = "Z4_CGC")
    protected String z4CGC;
    @XmlElement(name = "Z4_CHVNFE")
    protected String z4CHVNFE;
    @XmlElement(name = "Z4_NOTA")
    protected String z4NOTA;
    @XmlElement(name = "Z4_SERIE")
    protected String z4SERIE;
    @XmlElement(name = "Z4_STATUS")
    protected String z4STATUS;
    @XmlElement(name = "Z4_TIPOROT")
    protected String z4TIPOROT;

    /**
     * Gets the value of the c5NUM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getC5NUM() {
        return c5NUM;
    }

    /**
     * Sets the value of the c5NUM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setC5NUM(String value) {
        this.c5NUM = value;
    }

    /**
     * Gets the value of the chavgrv property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHAVGRV() {
        return chavgrv;
    }

    /**
     * Sets the value of the chavgrv property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHAVGRV(String value) {
        this.chavgrv = value;
    }

    /**
     * Gets the value of the cstatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCSTATUS() {
        return cstatus;
    }

    /**
     * Sets the value of the cstatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCSTATUS(String value) {
        this.cstatus = value;
    }

    /**
     * Gets the value of the f2CLIENTE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getF2CLIENTE() {
        return f2CLIENTE;
    }

    /**
     * Sets the value of the f2CLIENTE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setF2CLIENTE(String value) {
        this.f2CLIENTE = value;
    }

    /**
     * Gets the value of the f2DOC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getF2DOC() {
        return f2DOC;
    }

    /**
     * Sets the value of the f2DOC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setF2DOC(String value) {
        this.f2DOC = value;
    }

    /**
     * Gets the value of the f2EMISSAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getF2EMISSAO() {
        return f2EMISSAO;
    }

    /**
     * Sets the value of the f2EMISSAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setF2EMISSAO(String value) {
        this.f2EMISSAO = value;
    }

    /**
     * Gets the value of the f2LOJA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getF2LOJA() {
        return f2LOJA;
    }

    /**
     * Sets the value of the f2LOJA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setF2LOJA(String value) {
        this.f2LOJA = value;
    }

    /**
     * Gets the value of the f2SERIE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getF2SERIE() {
        return f2SERIE;
    }

    /**
     * Sets the value of the f2SERIE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setF2SERIE(String value) {
        this.f2SERIE = value;
    }

    /**
     * Gets the value of the lret property.
     * 
     */
    public boolean isLRET() {
        return lret;
    }

    /**
     * Sets the value of the lret property.
     * 
     */
    public void setLRET(boolean value) {
        this.lret = value;
    }

    /**
     * Gets the value of the msgerro property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMSGERRO() {
        return msgerro;
    }

    /**
     * Sets the value of the msgerro property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMSGERRO(String value) {
        this.msgerro = value;
    }

    /**
     * Gets the value of the z4CGC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4CGC() {
        return z4CGC;
    }

    /**
     * Sets the value of the z4CGC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4CGC(String value) {
        this.z4CGC = value;
    }

    /**
     * Gets the value of the z4CHVNFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4CHVNFE() {
        return z4CHVNFE;
    }

    /**
     * Sets the value of the z4CHVNFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4CHVNFE(String value) {
        this.z4CHVNFE = value;
    }

    /**
     * Gets the value of the z4NOTA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4NOTA() {
        return z4NOTA;
    }

    /**
     * Sets the value of the z4NOTA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4NOTA(String value) {
        this.z4NOTA = value;
    }

    /**
     * Gets the value of the z4SERIE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4SERIE() {
        return z4SERIE;
    }

    /**
     * Sets the value of the z4SERIE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4SERIE(String value) {
        this.z4SERIE = value;
    }

    /**
     * Gets the value of the z4STATUS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4STATUS() {
        return z4STATUS;
    }

    /**
     * Sets the value of the z4STATUS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4STATUS(String value) {
        this.z4STATUS = value;
    }

    /**
     * Gets the value of the z4TIPOROT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4TIPOROT() {
        return z4TIPOROT;
    }

    /**
     * Sets the value of the z4TIPOROT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4TIPOROT(String value) {
        this.z4TIPOROT = value;
    }

}

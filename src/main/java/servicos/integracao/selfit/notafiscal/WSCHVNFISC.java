
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSCHVNFISC complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSCHVNFISC">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CCHAVPROV" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MENSGRAVA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_CGC" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_NOTA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_SERIE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z5_PRODUTO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSCHVNFISC", propOrder = {
    "cchavprov",
    "mensgrava",
    "z4CGC",
    "z4NOTA",
    "z4SERIE",
    "z5PRODUTO"
})
public class WSCHVNFISC {

    @XmlElement(name = "CCHAVPROV", required = true)
    protected String cchavprov;
    @XmlElement(name = "MENSGRAVA", required = true)
    protected String mensgrava;
    @XmlElement(name = "Z4_CGC", required = true)
    protected String z4CGC;
    @XmlElement(name = "Z4_NOTA", required = true)
    protected String z4NOTA;
    @XmlElement(name = "Z4_SERIE", required = true)
    protected String z4SERIE;
    @XmlElement(name = "Z5_PRODUTO", required = true)
    protected String z5PRODUTO;

    /**
     * Gets the value of the cchavprov property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCCHAVPROV() {
        return cchavprov;
    }

    /**
     * Sets the value of the cchavprov property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCCHAVPROV(String value) {
        this.cchavprov = value;
    }

    /**
     * Gets the value of the mensgrava property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMENSGRAVA() {
        return mensgrava;
    }

    /**
     * Sets the value of the mensgrava property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMENSGRAVA(String value) {
        this.mensgrava = value;
    }

    /**
     * Gets the value of the z4CGC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4CGC() {
        return z4CGC;
    }

    /**
     * Sets the value of the z4CGC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4CGC(String value) {
        this.z4CGC = value;
    }

    /**
     * Gets the value of the z4NOTA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4NOTA() {
        return z4NOTA;
    }

    /**
     * Sets the value of the z4NOTA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4NOTA(String value) {
        this.z4NOTA = value;
    }

    /**
     * Gets the value of the z4SERIE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4SERIE() {
        return z4SERIE;
    }

    /**
     * Sets the value of the z4SERIE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4SERIE(String value) {
        this.z4SERIE = value;
    }

    /**
     * Gets the value of the z5PRODUTO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ5PRODUTO() {
        return z5PRODUTO;
    }

    /**
     * Sets the value of the z5PRODUTO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ5PRODUTO(String value) {
        this.z5PRODUTO = value;
    }

}

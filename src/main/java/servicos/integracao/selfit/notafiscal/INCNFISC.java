
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for INCNFISC complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="INCNFISC">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="NINCNFISC" type="{http://54.207.46.24:91/}ARRAYOFWSDADNFISC"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "INCNFISC", propOrder = {
    "nincnfisc"
})
public class INCNFISC {

    @XmlElement(name = "NINCNFISC", required = true)
    protected ARRAYOFWSDADNFISC nincnfisc;

    /**
     * Gets the value of the nincnfisc property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSDADNFISC }
     *     
     */
    public ARRAYOFWSDADNFISC getNINCNFISC() {
        return nincnfisc;
    }

    /**
     * Sets the value of the nincnfisc property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSDADNFISC }
     *     
     */
    public void setNINCNFISC(ARRAYOFWSDADNFISC value) {
        this.nincnfisc = value;
    }

}

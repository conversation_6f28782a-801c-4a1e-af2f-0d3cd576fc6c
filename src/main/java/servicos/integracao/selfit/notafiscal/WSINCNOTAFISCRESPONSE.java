
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSINCNOTAFISCRESULT" type="{http://54.207.46.24:91/}WSRETNFISC"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wsincnotafiscresult"
})
@XmlRootElement(name = "WSINCNOTAFISCRESPONSE")
public class WSINCNOTAFISCRESPONSE {

    @XmlElement(name = "WSINCNOTAFISCRESULT", required = true)
    protected WSRETNFISC wsincnotafiscresult;

    /**
     * Gets the value of the wsincnotafiscresult property.
     * 
     * @return
     *     possible object is
     *     {@link WSRETNFISC }
     *     
     */
    public WSRETNFISC getWSINCNOTAFISCRESULT() {
        return wsincnotafiscresult;
    }

    /**
     * Sets the value of the wsincnotafiscresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link WSRETNFISC }
     *     
     */
    public void setWSINCNOTAFISCRESULT(WSRETNFISC value) {
        this.wsincnotafiscresult = value;
    }

}


package servicos.integracao.selfit.notafiscal;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSCHVNFISC complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSCHVNFISC">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSCHVNFISC" type="{http://54.207.46.24:91/}WSCHVNFISC" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSCHVNFISC", propOrder = {
    "wschvnfisc"
})
public class ARRAYOFWSCHVNFISC {

    @XmlElement(name = "WSCHVNFISC")
    protected List<WSCHVNFISC> wschvnfisc;

    /**
     * Gets the value of the wschvnfisc property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wschvnfisc property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSCHVNFISC().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSCHVNFISC }
     * 
     * 
     */
    public List<WSCHVNFISC> getWSCHVNFISC() {
        if (wschvnfisc == null) {
            wschvnfisc = new ArrayList<WSCHVNFISC>();
        }
        return this.wschvnfisc;
    }

}

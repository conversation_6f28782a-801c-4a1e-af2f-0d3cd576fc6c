
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.selfit.notafiscal package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.selfit.notafiscal
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link WSINCNOTAFISC }
     * 
     */
    public WSINCNOTAFISC createWSINCNOTAFISC() {
        return new WSINCNOTAFISC();
    }

    /**
     * Create an instance of {@link INCNFISC }
     * 
     */
    public INCNFISC createINCNFISC() {
        return new INCNFISC();
    }

    /**
     * Create an instance of {@link WSCONNFISC }
     * 
     */
    public WSCONNFISC createWSCONNFISC() {
        return new WSCONNFISC();
    }

    /**
     * Create an instance of {@link WSINCNOTAFISCRESPONSE }
     * 
     */
    public WSINCNOTAFISCRESPONSE createWSINCNOTAFISCRESPONSE() {
        return new WSINCNOTAFISCRESPONSE();
    }

    /**
     * Create an instance of {@link WSRETNFISC }
     * 
     */
    public WSRETNFISC createWSRETNFISC() {
        return new WSRETNFISC();
    }

    /**
     * Create an instance of {@link WSCONNFISCRESPONSE }
     * 
     */
    public WSCONNFISCRESPONSE createWSCONNFISCRESPONSE() {
        return new WSCONNFISCRESPONSE();
    }

    /**
     * Create an instance of {@link ARRAYOFWSRETCNFIS }
     * 
     */
    public ARRAYOFWSRETCNFIS createARRAYOFWSRETCNFIS() {
        return new ARRAYOFWSRETCNFIS();
    }

    /**
     * Create an instance of {@link ARRAYOFWSDADNFISC }
     * 
     */
    public ARRAYOFWSDADNFISC createARRAYOFWSDADNFISC() {
        return new ARRAYOFWSDADNFISC();
    }

    /**
     * Create an instance of {@link WSDADITNF }
     * 
     */
    public WSDADITNF createWSDADITNF() {
        return new WSDADITNF();
    }

    /**
     * Create an instance of {@link ARRAYOFWSDADITNF }
     * 
     */
    public ARRAYOFWSDADITNF createARRAYOFWSDADITNF() {
        return new ARRAYOFWSDADITNF();
    }

    /**
     * Create an instance of {@link ARRAYOFWSCHVNFISC }
     * 
     */
    public ARRAYOFWSCHVNFISC createARRAYOFWSCHVNFISC() {
        return new ARRAYOFWSCHVNFISC();
    }

    /**
     * Create an instance of {@link WSDADNFISC }
     * 
     */
    public WSDADNFISC createWSDADNFISC() {
        return new WSDADNFISC();
    }

    /**
     * Create an instance of {@link WSRETCNFIS }
     * 
     */
    public WSRETCNFIS createWSRETCNFIS() {
        return new WSRETCNFIS();
    }

    /**
     * Create an instance of {@link WSCHVNFISC }
     * 
     */
    public WSCHVNFISC createWSCHVNFISC() {
        return new WSCHVNFISC();
    }

}

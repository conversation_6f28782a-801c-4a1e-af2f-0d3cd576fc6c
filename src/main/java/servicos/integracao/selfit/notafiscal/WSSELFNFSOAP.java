
package servicos.integracao.selfit.notafiscal;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "WSSELFNFSOAP", targetNamespace = "http://54.207.46.24:91/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface WSSELFNFSOAP {


    /**
     * Consulta Pedido / Nota Fiscal
     * 
     * @param cnpjempr
     * @param cchavgrv
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.notafiscal.ARRAYOFWSRETCNFIS
     */
    @WebMethod(operationName = "WSCONNFISC", action = "http://54.207.46.24:91/WSCONNFISC")
    @WebResult(name = "WSCONNFISCRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSCONNFISC", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.notafiscal.WSCONNFISC")
    @ResponseWrapper(localName = "WSCONNFISCRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.notafiscal.WSCONNFISCRESPONSE")
    public ARRAYOFWSRETCNFIS wsconnfisc(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CCHAVGRV", targetNamespace = "http://54.207.46.24:91/")
        String cchavgrv);

    /**
     * Incluir Nota Fiscal
     * 
     * @param adadnfisc
     * @param cnpjempr
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.notafiscal.WSRETNFISC
     */
    @WebMethod(operationName = "WSINCNOTAFISC", action = "http://54.207.46.24:91/WSINCNOTAFISC")
    @WebResult(name = "WSINCNOTAFISCRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSINCNOTAFISC", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.notafiscal.WSINCNOTAFISC")
    @ResponseWrapper(localName = "WSINCNOTAFISCRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.notafiscal.WSINCNOTAFISCRESPONSE")
    public WSRETNFISC wsincnotafisc(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "ADADNFISC", targetNamespace = "http://54.207.46.24:91/")
        INCNFISC adadnfisc);

}

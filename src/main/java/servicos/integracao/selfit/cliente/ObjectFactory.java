
package servicos.integracao.selfit.cliente;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.selfit.cliente package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.selfit.cliente
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link WSCLIINCL }
     * 
     */
    public WSCLIINCL createWSCLIINCL() {
        return new WSCLIINCL();
    }

    /**
     * Create an instance of {@link INCCLIE }
     * 
     */
    public INCCLIE createINCCLIE() {
        return new INCCLIE();
    }

    /**
     * Create an instance of {@link WSCLICONSRESPONSE }
     * 
     */
    public WSCLICONSRESPONSE createWSCLICONSRESPONSE() {
        return new WSCLICONSRESPONSE();
    }

    /**
     * Create an instance of {@link ARRAYOFWSRETCLI }
     * 
     */
    public ARRAYOFWSRETCLI createARRAYOFWSRETCLI() {
        return new ARRAYOFWSRETCLI();
    }

    /**
     * Create an instance of {@link WSCLICONS }
     * 
     */
    public WSCLICONS createWSCLICONS() {
        return new WSCLICONS();
    }

    /**
     * Create an instance of {@link WSCLIINCLRESPONSE }
     * 
     */
    public WSCLIINCLRESPONSE createWSCLIINCLRESPONSE() {
        return new WSCLIINCLRESPONSE();
    }

    /**
     * Create an instance of {@link WSCONPROCRESPONSE }
     * 
     */
    public WSCONPROCRESPONSE createWSCONPROCRESPONSE() {
        return new WSCONPROCRESPONSE();
    }

    /**
     * Create an instance of {@link WSCONPROC }
     * 
     */
    public WSCONPROC createWSCONPROC() {
        return new WSCONPROC();
    }

    /**
     * Create an instance of {@link ARRAYOFWSDADCLI }
     * 
     */
    public ARRAYOFWSDADCLI createARRAYOFWSDADCLI() {
        return new ARRAYOFWSDADCLI();
    }

    /**
     * Create an instance of {@link WSRETCLI }
     * 
     */
    public WSRETCLI createWSRETCLI() {
        return new WSRETCLI();
    }

    /**
     * Create an instance of {@link WSDADCLI }
     * 
     */
    public WSDADCLI createWSDADCLI() {
        return new WSDADCLI();
    }

}


package servicos.integracao.selfit.cliente;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSCLIINCLRESULT" type="{http://54.207.46.24:91/}ARRAYOFWSRETCLI"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wscliinclresult"
})
@XmlRootElement(name = "WSCLIINCLRESPONSE")
public class WSCLIINCLRESPONSE {

    @XmlElement(name = "WSCLIINCLRESULT", required = true)
    protected ARRAYOFWSRETCLI wscliinclresult;

    /**
     * Gets the value of the wscliinclresult property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSRETCLI }
     *     
     */
    public ARRAYOFWSRETCLI getWSCLIINCLRESULT() {
        return wscliinclresult;
    }

    /**
     * Sets the value of the wscliinclresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSRETCLI }
     *     
     */
    public void setWSCLIINCLRESULT(ARRAYOFWSRETCLI value) {
        this.wscliinclresult = value;
    }

}


package servicos.integracao.selfit.cliente;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * Servico de Consulta/Gravacao Cliente
 * 
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "WSSELFCL", targetNamespace = "http://54.207.46.24:91/", wsdlLocation = "http://54.207.46.24:91/ws/WSSELFCL.apw?WSDL")
public class WSSELFCL
    extends Service
{

    private final static URL WSSELFCL_WSDL_LOCATION;
    private final static WebServiceException WSSELFCL_EXCEPTION;
    private final static QName WSSELFCL_QNAME = new QName("http://54.207.46.24:91/", "WSSELFCL");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://54.207.46.24:91/ws/WSSELFCL.apw?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WSSELFCL_WSDL_LOCATION = url;
        WSSELFCL_EXCEPTION = e;
    }

    public WSSELFCL() {
        super(__getWsdlLocation(), WSSELFCL_QNAME);
    }

    public WSSELFCL(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns WSSELFCLSOAP
     */
    @WebEndpoint(name = "WSSELFCLSOAP")
    public WSSELFCLSOAP getWSSELFCLSOAP() {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFCLSOAP"), WSSELFCLSOAP.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns WSSELFCLSOAP
     */
    @WebEndpoint(name = "WSSELFCLSOAP")
    public WSSELFCLSOAP getWSSELFCLSOAP(WebServiceFeature... features) {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFCLSOAP"), WSSELFCLSOAP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (WSSELFCL_EXCEPTION!= null) {
            throw WSSELFCL_EXCEPTION;
        }
        return WSSELFCL_WSDL_LOCATION;
    }

}

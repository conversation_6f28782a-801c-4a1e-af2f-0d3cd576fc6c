
package servicos.integracao.selfit.cliente;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSRETCLI complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSRETCLI">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="A1_BAIRRO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_CEP" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_CGC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_COD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_COD_MUN" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_COMPLEM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_CONTA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_DDD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_DSCREG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_DTCAD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_DTNASC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_EMAIL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_END" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_EST" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_ESTADO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_HRCAD" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_INSCR" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_INSCRM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_LOJA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_MUN" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_NOME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_NREDUZ" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_PAIS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_PESSOA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_PFISICA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_REGIAO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_TEL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_TIPCLI" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="A1_TIPO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CHAVGRV" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CSTATUS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LRET" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="MSGERRO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSRETCLI", propOrder = {
    "a1BAIRRO",
    "a1CEP",
    "a1CGC",
    "a1COD",
    "a1CODMUN",
    "a1COMPLEM",
    "a1CONTA",
    "a1DDD",
    "a1DSCREG",
    "a1DTCAD",
    "a1DTNASC",
    "a1EMAIL",
    "a1END",
    "a1EST",
    "a1ESTADO",
    "a1HRCAD",
    "a1INSCR",
    "a1INSCRM",
    "a1LOJA",
    "a1MUN",
    "a1NOME",
    "a1NREDUZ",
    "a1PAIS",
    "a1PESSOA",
    "a1PFISICA",
    "a1REGIAO",
    "a1TEL",
    "a1TIPCLI",
    "a1TIPO",
    "chavgrv",
    "cstatus",
    "lret",
    "msgerro"
})
public class WSRETCLI {

    @XmlElement(name = "A1_BAIRRO")
    protected String a1BAIRRO;
    @XmlElement(name = "A1_CEP")
    protected String a1CEP;
    @XmlElement(name = "A1_CGC")
    protected String a1CGC;
    @XmlElement(name = "A1_COD")
    protected String a1COD;
    @XmlElement(name = "A1_COD_MUN")
    protected String a1CODMUN;
    @XmlElement(name = "A1_COMPLEM")
    protected String a1COMPLEM;
    @XmlElement(name = "A1_CONTA")
    protected String a1CONTA;
    @XmlElement(name = "A1_DDD")
    protected String a1DDD;
    @XmlElement(name = "A1_DSCREG")
    protected String a1DSCREG;
    @XmlElement(name = "A1_DTCAD")
    protected String a1DTCAD;
    @XmlElement(name = "A1_DTNASC")
    protected String a1DTNASC;
    @XmlElement(name = "A1_EMAIL")
    protected String a1EMAIL;
    @XmlElement(name = "A1_END")
    protected String a1END;
    @XmlElement(name = "A1_EST")
    protected String a1EST;
    @XmlElement(name = "A1_ESTADO")
    protected String a1ESTADO;
    @XmlElement(name = "A1_HRCAD")
    protected String a1HRCAD;
    @XmlElement(name = "A1_INSCR")
    protected String a1INSCR;
    @XmlElement(name = "A1_INSCRM")
    protected String a1INSCRM;
    @XmlElement(name = "A1_LOJA")
    protected String a1LOJA;
    @XmlElement(name = "A1_MUN")
    protected String a1MUN;
    @XmlElement(name = "A1_NOME")
    protected String a1NOME;
    @XmlElement(name = "A1_NREDUZ")
    protected String a1NREDUZ;
    @XmlElement(name = "A1_PAIS")
    protected String a1PAIS;
    @XmlElement(name = "A1_PESSOA")
    protected String a1PESSOA;
    @XmlElement(name = "A1_PFISICA")
    protected String a1PFISICA;
    @XmlElement(name = "A1_REGIAO")
    protected String a1REGIAO;
    @XmlElement(name = "A1_TEL")
    protected String a1TEL;
    @XmlElement(name = "A1_TIPCLI")
    protected String a1TIPCLI;
    @XmlElement(name = "A1_TIPO")
    protected String a1TIPO;
    @XmlElement(name = "CHAVGRV")
    protected String chavgrv;
    @XmlElement(name = "CSTATUS", required = true)
    protected String cstatus;
    @XmlElement(name = "LRET")
    protected boolean lret;
    @XmlElement(name = "MSGERRO")
    protected String msgerro;

    /**
     * Gets the value of the a1BAIRRO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1BAIRRO() {
        return a1BAIRRO;
    }

    /**
     * Sets the value of the a1BAIRRO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1BAIRRO(String value) {
        this.a1BAIRRO = value;
    }

    /**
     * Gets the value of the a1CEP property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1CEP() {
        return a1CEP;
    }

    /**
     * Sets the value of the a1CEP property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1CEP(String value) {
        this.a1CEP = value;
    }

    /**
     * Gets the value of the a1CGC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1CGC() {
        return a1CGC;
    }

    /**
     * Sets the value of the a1CGC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1CGC(String value) {
        this.a1CGC = value;
    }

    /**
     * Gets the value of the a1COD property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1COD() {
        return a1COD;
    }

    /**
     * Sets the value of the a1COD property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1COD(String value) {
        this.a1COD = value;
    }

    /**
     * Gets the value of the a1CODMUN property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1CODMUN() {
        return a1CODMUN;
    }

    /**
     * Sets the value of the a1CODMUN property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1CODMUN(String value) {
        this.a1CODMUN = value;
    }

    /**
     * Gets the value of the a1COMPLEM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1COMPLEM() {
        return a1COMPLEM;
    }

    /**
     * Sets the value of the a1COMPLEM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1COMPLEM(String value) {
        this.a1COMPLEM = value;
    }

    /**
     * Gets the value of the a1CONTA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1CONTA() {
        return a1CONTA;
    }

    /**
     * Sets the value of the a1CONTA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1CONTA(String value) {
        this.a1CONTA = value;
    }

    /**
     * Gets the value of the a1DDD property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DDD() {
        return a1DDD;
    }

    /**
     * Sets the value of the a1DDD property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DDD(String value) {
        this.a1DDD = value;
    }

    /**
     * Gets the value of the a1DSCREG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DSCREG() {
        return a1DSCREG;
    }

    /**
     * Sets the value of the a1DSCREG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DSCREG(String value) {
        this.a1DSCREG = value;
    }

    /**
     * Gets the value of the a1DTCAD property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DTCAD() {
        return a1DTCAD;
    }

    /**
     * Sets the value of the a1DTCAD property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DTCAD(String value) {
        this.a1DTCAD = value;
    }

    /**
     * Gets the value of the a1DTNASC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DTNASC() {
        return a1DTNASC;
    }

    /**
     * Sets the value of the a1DTNASC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DTNASC(String value) {
        this.a1DTNASC = value;
    }

    /**
     * Gets the value of the a1EMAIL property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1EMAIL() {
        return a1EMAIL;
    }

    /**
     * Sets the value of the a1EMAIL property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1EMAIL(String value) {
        this.a1EMAIL = value;
    }

    /**
     * Gets the value of the a1END property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1END() {
        return a1END;
    }

    /**
     * Sets the value of the a1END property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1END(String value) {
        this.a1END = value;
    }

    /**
     * Gets the value of the a1EST property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1EST() {
        return a1EST;
    }

    /**
     * Sets the value of the a1EST property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1EST(String value) {
        this.a1EST = value;
    }

    /**
     * Gets the value of the a1ESTADO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1ESTADO() {
        return a1ESTADO;
    }

    /**
     * Sets the value of the a1ESTADO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1ESTADO(String value) {
        this.a1ESTADO = value;
    }

    /**
     * Gets the value of the a1HRCAD property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1HRCAD() {
        return a1HRCAD;
    }

    /**
     * Sets the value of the a1HRCAD property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1HRCAD(String value) {
        this.a1HRCAD = value;
    }

    /**
     * Gets the value of the a1INSCR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1INSCR() {
        return a1INSCR;
    }

    /**
     * Sets the value of the a1INSCR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1INSCR(String value) {
        this.a1INSCR = value;
    }

    /**
     * Gets the value of the a1INSCRM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1INSCRM() {
        return a1INSCRM;
    }

    /**
     * Sets the value of the a1INSCRM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1INSCRM(String value) {
        this.a1INSCRM = value;
    }

    /**
     * Gets the value of the a1LOJA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1LOJA() {
        return a1LOJA;
    }

    /**
     * Sets the value of the a1LOJA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1LOJA(String value) {
        this.a1LOJA = value;
    }

    /**
     * Gets the value of the a1MUN property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1MUN() {
        return a1MUN;
    }

    /**
     * Sets the value of the a1MUN property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1MUN(String value) {
        this.a1MUN = value;
    }

    /**
     * Gets the value of the a1NOME property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1NOME() {
        return a1NOME;
    }

    /**
     * Sets the value of the a1NOME property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1NOME(String value) {
        this.a1NOME = value;
    }

    /**
     * Gets the value of the a1NREDUZ property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1NREDUZ() {
        return a1NREDUZ;
    }

    /**
     * Sets the value of the a1NREDUZ property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1NREDUZ(String value) {
        this.a1NREDUZ = value;
    }

    /**
     * Gets the value of the a1PAIS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1PAIS() {
        return a1PAIS;
    }

    /**
     * Sets the value of the a1PAIS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1PAIS(String value) {
        this.a1PAIS = value;
    }

    /**
     * Gets the value of the a1PESSOA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1PESSOA() {
        return a1PESSOA;
    }

    /**
     * Sets the value of the a1PESSOA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1PESSOA(String value) {
        this.a1PESSOA = value;
    }

    /**
     * Gets the value of the a1PFISICA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1PFISICA() {
        return a1PFISICA;
    }

    /**
     * Sets the value of the a1PFISICA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1PFISICA(String value) {
        this.a1PFISICA = value;
    }

    /**
     * Gets the value of the a1REGIAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1REGIAO() {
        return a1REGIAO;
    }

    /**
     * Sets the value of the a1REGIAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1REGIAO(String value) {
        this.a1REGIAO = value;
    }

    /**
     * Gets the value of the a1TEL property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1TEL() {
        return a1TEL;
    }

    /**
     * Sets the value of the a1TEL property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1TEL(String value) {
        this.a1TEL = value;
    }

    /**
     * Gets the value of the a1TIPCLI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1TIPCLI() {
        return a1TIPCLI;
    }

    /**
     * Sets the value of the a1TIPCLI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1TIPCLI(String value) {
        this.a1TIPCLI = value;
    }

    /**
     * Gets the value of the a1TIPO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1TIPO() {
        return a1TIPO;
    }

    /**
     * Sets the value of the a1TIPO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1TIPO(String value) {
        this.a1TIPO = value;
    }

    /**
     * Gets the value of the chavgrv property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHAVGRV() {
        return chavgrv;
    }

    /**
     * Sets the value of the chavgrv property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHAVGRV(String value) {
        this.chavgrv = value;
    }

    /**
     * Gets the value of the cstatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCSTATUS() {
        return cstatus;
    }

    /**
     * Sets the value of the cstatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCSTATUS(String value) {
        this.cstatus = value;
    }

    /**
     * Gets the value of the lret property.
     * 
     */
    public boolean isLRET() {
        return lret;
    }

    /**
     * Sets the value of the lret property.
     * 
     */
    public void setLRET(boolean value) {
        this.lret = value;
    }

    /**
     * Gets the value of the msgerro property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMSGERRO() {
        return msgerro;
    }

    /**
     * Sets the value of the msgerro property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMSGERRO(String value) {
        this.msgerro = value;
    }

}

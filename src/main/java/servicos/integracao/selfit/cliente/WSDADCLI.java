
package servicos.integracao.selfit.cliente;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSDADCLI complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSDADCLI">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="A1_BAIRRO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_CEP" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_COD_MUN" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_COMPLEM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_DDD" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_DSCREG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_DTNASC" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_EMAIL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_END" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_EST" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_INSCR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_INSCRM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_MUN" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_NOME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_NREDUZ" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_PAIS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_PESSOA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_PFISICA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_REGIAO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_TEL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_TIPO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="A1_TIPOROT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSDADCLI", propOrder = {
    "a1BAIRRO",
    "a1CEP",
    "a1CODMUN",
    "a1COMPLEM",
    "a1DDD",
    "a1DSCREG",
    "a1DTNASC",
    "a1EMAIL",
    "a1END",
    "a1EST",
    "a1INSCR",
    "a1INSCRM",
    "a1MUN",
    "a1NOME",
    "a1NREDUZ",
    "a1PAIS",
    "a1PESSOA",
    "a1PFISICA",
    "a1REGIAO",
    "a1TEL",
    "a1TIPO",
    "a1TIPOROT"
})
public class WSDADCLI {

    @XmlElement(name = "A1_BAIRRO", required = true)
    protected String a1BAIRRO;
    @XmlElement(name = "A1_CEP", required = true)
    protected String a1CEP;
    @XmlElement(name = "A1_COD_MUN", required = true)
    protected String a1CODMUN;
    @XmlElement(name = "A1_COMPLEM", required = true)
    protected String a1COMPLEM;
    @XmlElement(name = "A1_DDD", required = true)
    protected String a1DDD;
    @XmlElement(name = "A1_DSCREG", required = true)
    protected String a1DSCREG;
    @XmlElement(name = "A1_DTNASC", required = true)
    protected String a1DTNASC;
    @XmlElement(name = "A1_EMAIL", required = true)
    protected String a1EMAIL;
    @XmlElement(name = "A1_END", required = true)
    protected String a1END;
    @XmlElement(name = "A1_EST", required = true)
    protected String a1EST;
    @XmlElement(name = "A1_INSCR", required = true)
    protected String a1INSCR;
    @XmlElement(name = "A1_INSCRM", required = true)
    protected String a1INSCRM;
    @XmlElement(name = "A1_MUN", required = true)
    protected String a1MUN;
    @XmlElement(name = "A1_NOME", required = true)
    protected String a1NOME;
    @XmlElement(name = "A1_NREDUZ", required = true)
    protected String a1NREDUZ;
    @XmlElement(name = "A1_PAIS", required = true)
    protected String a1PAIS;
    @XmlElement(name = "A1_PESSOA", required = true)
    protected String a1PESSOA;
    @XmlElement(name = "A1_PFISICA", required = true)
    protected String a1PFISICA;
    @XmlElement(name = "A1_REGIAO", required = true)
    protected String a1REGIAO;
    @XmlElement(name = "A1_TEL", required = true)
    protected String a1TEL;
    @XmlElement(name = "A1_TIPO", required = true)
    protected String a1TIPO;
    @XmlElement(name = "A1_TIPOROT", required = true)
    protected String a1TIPOROT;

    /**
     * Gets the value of the a1BAIRRO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1BAIRRO() {
        return a1BAIRRO;
    }

    /**
     * Sets the value of the a1BAIRRO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1BAIRRO(String value) {
        this.a1BAIRRO = value;
    }

    /**
     * Gets the value of the a1CEP property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1CEP() {
        return a1CEP;
    }

    /**
     * Sets the value of the a1CEP property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1CEP(String value) {
        this.a1CEP = value;
    }

    /**
     * Gets the value of the a1CODMUN property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1CODMUN() {
        return a1CODMUN;
    }

    /**
     * Sets the value of the a1CODMUN property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1CODMUN(String value) {
        this.a1CODMUN = value;
    }

    /**
     * Gets the value of the a1COMPLEM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1COMPLEM() {
        return a1COMPLEM;
    }

    /**
     * Sets the value of the a1COMPLEM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1COMPLEM(String value) {
        this.a1COMPLEM = value;
    }

    /**
     * Gets the value of the a1DDD property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DDD() {
        return a1DDD;
    }

    /**
     * Sets the value of the a1DDD property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DDD(String value) {
        this.a1DDD = value;
    }

    /**
     * Gets the value of the a1DSCREG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DSCREG() {
        return a1DSCREG;
    }

    /**
     * Sets the value of the a1DSCREG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DSCREG(String value) {
        this.a1DSCREG = value;
    }

    /**
     * Gets the value of the a1DTNASC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1DTNASC() {
        return a1DTNASC;
    }

    /**
     * Sets the value of the a1DTNASC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1DTNASC(String value) {
        this.a1DTNASC = value;
    }

    /**
     * Gets the value of the a1EMAIL property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1EMAIL() {
        return a1EMAIL;
    }

    /**
     * Sets the value of the a1EMAIL property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1EMAIL(String value) {
        this.a1EMAIL = value;
    }

    /**
     * Gets the value of the a1END property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1END() {
        return a1END;
    }

    /**
     * Sets the value of the a1END property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1END(String value) {
        this.a1END = value;
    }

    /**
     * Gets the value of the a1EST property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1EST() {
        return a1EST;
    }

    /**
     * Sets the value of the a1EST property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1EST(String value) {
        this.a1EST = value;
    }

    /**
     * Gets the value of the a1INSCR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1INSCR() {
        return a1INSCR;
    }

    /**
     * Sets the value of the a1INSCR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1INSCR(String value) {
        this.a1INSCR = value;
    }

    /**
     * Gets the value of the a1INSCRM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1INSCRM() {
        return a1INSCRM;
    }

    /**
     * Sets the value of the a1INSCRM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1INSCRM(String value) {
        this.a1INSCRM = value;
    }

    /**
     * Gets the value of the a1MUN property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1MUN() {
        return a1MUN;
    }

    /**
     * Sets the value of the a1MUN property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1MUN(String value) {
        this.a1MUN = value;
    }

    /**
     * Gets the value of the a1NOME property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1NOME() {
        return a1NOME;
    }

    /**
     * Sets the value of the a1NOME property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1NOME(String value) {
        this.a1NOME = value;
    }

    /**
     * Gets the value of the a1NREDUZ property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1NREDUZ() {
        return a1NREDUZ;
    }

    /**
     * Sets the value of the a1NREDUZ property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1NREDUZ(String value) {
        this.a1NREDUZ = value;
    }

    /**
     * Gets the value of the a1PAIS property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1PAIS() {
        return a1PAIS;
    }

    /**
     * Sets the value of the a1PAIS property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1PAIS(String value) {
        this.a1PAIS = value;
    }

    /**
     * Gets the value of the a1PESSOA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1PESSOA() {
        return a1PESSOA;
    }

    /**
     * Sets the value of the a1PESSOA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1PESSOA(String value) {
        this.a1PESSOA = value;
    }

    /**
     * Gets the value of the a1PFISICA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1PFISICA() {
        return a1PFISICA;
    }

    /**
     * Sets the value of the a1PFISICA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1PFISICA(String value) {
        this.a1PFISICA = value;
    }

    /**
     * Gets the value of the a1REGIAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1REGIAO() {
        return a1REGIAO;
    }

    /**
     * Sets the value of the a1REGIAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1REGIAO(String value) {
        this.a1REGIAO = value;
    }

    /**
     * Gets the value of the a1TEL property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1TEL() {
        return a1TEL;
    }

    /**
     * Sets the value of the a1TEL property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1TEL(String value) {
        this.a1TEL = value;
    }

    /**
     * Gets the value of the a1TIPO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1TIPO() {
        return a1TIPO;
    }

    /**
     * Sets the value of the a1TIPO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1TIPO(String value) {
        this.a1TIPO = value;
    }

    /**
     * Gets the value of the a1TIPOROT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getA1TIPOROT() {
        return a1TIPOROT;
    }

    /**
     * Sets the value of the a1TIPOROT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setA1TIPOROT(String value) {
        this.a1TIPOROT = value;
    }

}

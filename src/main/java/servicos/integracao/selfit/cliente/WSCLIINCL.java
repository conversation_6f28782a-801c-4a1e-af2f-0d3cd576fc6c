
package servicos.integracao.selfit.cliente;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CHAVEMES" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CNPJEMPR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CNPJCLIE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ADADCLIE" type="{http://54.207.46.24:91/}INCCLIE"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "chavemes",
    "cnpjempr",
    "cnpjclie",
    "adadclie"
})
@XmlRootElement(name = "WSCLIINCL")
public class WSCLIINCL {

    @XmlElement(name = "CHAVEMES", required = true)
    protected String chavemes;
    @XmlElement(name = "CNPJEMPR", required = true)
    protected String cnpjempr;
    @XmlElement(name = "CNPJCLIE", required = true)
    protected String cnpjclie;
    @XmlElement(name = "ADADCLIE", required = true)
    protected INCCLIE adadclie;

    /**
     * Gets the value of the chavemes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHAVEMES() {
        return chavemes;
    }

    /**
     * Sets the value of the chavemes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHAVEMES(String value) {
        this.chavemes = value;
    }

    /**
     * Gets the value of the cnpjempr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCNPJEMPR() {
        return cnpjempr;
    }

    /**
     * Sets the value of the cnpjempr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCNPJEMPR(String value) {
        this.cnpjempr = value;
    }

    /**
     * Gets the value of the cnpjclie property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCNPJCLIE() {
        return cnpjclie;
    }

    /**
     * Sets the value of the cnpjclie property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCNPJCLIE(String value) {
        this.cnpjclie = value;
    }

    /**
     * Gets the value of the adadclie property.
     * 
     * @return
     *     possible object is
     *     {@link INCCLIE }
     *     
     */
    public INCCLIE getADADCLIE() {
        return adadclie;
    }

    /**
     * Sets the value of the adadclie property.
     * 
     * @param value
     *     allowed object is
     *     {@link INCCLIE }
     *     
     */
    public void setADADCLIE(INCCLIE value) {
        this.adadclie = value;
    }

}

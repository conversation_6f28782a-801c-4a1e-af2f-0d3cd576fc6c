
package servicos.integracao.selfit.cliente;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "WSSELFCLSOAP", targetNamespace = "http://54.207.46.24:91/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface WSSELFCLSOAP {


    /**
     * Consulta Cadastro de Cliente
     * 
     * @param cnpjclie
     * @param cnpjempr
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.cliente.ARRAYOFWSRETCLI
     */
    @WebMethod(operationName = "WSCLICONS", action = "http://54.207.46.24:91/WSCLICONS")
    @WebResult(name = "WSCLICONSRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSCLICONS", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.cliente.WSCLICONS")
    @ResponseWrapper(localName = "WSCLICONSRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.cliente.WSCLICONSRESPONSE")
    public ARRAYOFWSRETCLI wsclicons(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CNPJCLIE", targetNamespace = "http://54.207.46.24:91/")
        String cnpjclie);

    /**
     * Incluir Cliente
     * 
     * @param cnpjclie
     * @param cnpjempr
     * @param chavemes
     * @param adadclie
     * @return
     *     returns servicos.integracao.selfit.cliente.ARRAYOFWSRETCLI
     */
    @WebMethod(operationName = "WSCLIINCL", action = "http://54.207.46.24:91/WSCLIINCL")
    @WebResult(name = "WSCLIINCLRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSCLIINCL", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.cliente.WSCLIINCL")
    @ResponseWrapper(localName = "WSCLIINCLRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.cliente.WSCLIINCLRESPONSE")
    public ARRAYOFWSRETCLI wscliincl(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CNPJCLIE", targetNamespace = "http://54.207.46.24:91/")
        String cnpjclie,
        @WebParam(name = "ADADCLIE", targetNamespace = "http://54.207.46.24:91/")
        INCCLIE adadclie);

    /**
     * Consulta Processamento Cliente
     * 
     * @param cnpjempr
     * @param cchavgrv
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.cliente.ARRAYOFWSRETCLI
     */
    @WebMethod(operationName = "WSCONPROC", action = "http://54.207.46.24:91/WSCONPROC")
    @WebResult(name = "WSCONPROCRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSCONPROC", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.cliente.WSCONPROC")
    @ResponseWrapper(localName = "WSCONPROCRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.cliente.WSCONPROCRESPONSE")
    public ARRAYOFWSRETCLI wsconproc(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CCHAVGRV", targetNamespace = "http://54.207.46.24:91/")
        String cchavgrv);

}


package servicos.integracao.selfit.cliente;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSRETCLI complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSRETCLI">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSRETCLI" type="{http://54.207.46.24:91/}WSRETCLI" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSRETCLI", propOrder = {
    "wsretcli"
})
public class ARRAYOFWSRETCLI {

    @XmlElement(name = "WSRETCLI")
    protected List<WSRETCLI> wsretcli;

    /**
     * Gets the value of the wsretcli property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wsretcli property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSRETCLI().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSRETCLI }
     * 
     * 
     */
    public List<WSRETCLI> getWSRETCLI() {
        if (wsretcli == null) {
            wsretcli = new ArrayList<WSRETCLI>();
        }
        return this.wsretcli;
    }

}

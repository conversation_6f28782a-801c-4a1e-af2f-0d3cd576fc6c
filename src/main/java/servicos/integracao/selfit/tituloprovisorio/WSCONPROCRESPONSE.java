
package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSCONPROCRESULT" type="{http://54.207.46.24:91/}ARRAYOFWSRETTPRO"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wsconprocresult"
})
@XmlRootElement(name = "WSCONPROCRESPONSE")
public class WSCONPROCRESPONSE {

    @XmlElement(name = "WSCONPROCRESULT", required = true)
    protected ARRAYOFWSRETTPRO wsconprocresult;

    /**
     * Gets the value of the wsconprocresult property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSRETTPRO }
     *     
     */
    public ARRAYOFWSRETTPRO getWSCONPROCRESULT() {
        return wsconprocresult;
    }

    /**
     * Sets the value of the wsconprocresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSRETTPRO }
     *     
     */
    public void setWSCONPROCRESULT(ARRAYOFWSRETTPRO value) {
        this.wsconprocresult = value;
    }

}

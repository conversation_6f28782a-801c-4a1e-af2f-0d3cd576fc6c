
package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSDADPROV complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSDADPROV">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Z2_ADM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_CNO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z2_EMISSAO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_MATRICU" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_NUMCONT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_PARCELA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_PREFIXO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_TIPOROT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_VALOR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_VENCREA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z2_VENCTO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_VTXADMI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSDADPROV", propOrder = {
    "z2ADM",
    "z2CNO",
    "z2EMISSAO",
    "z2MATRICU",
    "z2NUM",
    "z2NUMCONT",
    "z2PARCELA",
    "z2PREFIXO",
    "z2TIPOROT",
    "z2VALOR",
    "z2VENCREA",
    "z2VENCTO",
    "z2VTXADMI"
})
public class WSDADPROV {

    @XmlElement(name = "Z2_ADM", required = true)
    protected String z2ADM;
    @XmlElement(name = "Z2_CNO")
    protected String z2CNO;
    @XmlElement(name = "Z2_EMISSAO", required = true)
    protected String z2EMISSAO;
    @XmlElement(name = "Z2_MATRICU", required = true)
    protected String z2MATRICU;
    @XmlElement(name = "Z2_NUM", required = true)
    protected String z2NUM;
    @XmlElement(name = "Z2_NUMCONT", required = true)
    protected String z2NUMCONT;
    @XmlElement(name = "Z2_PARCELA", required = true)
    protected String z2PARCELA;
    @XmlElement(name = "Z2_PREFIXO", required = true)
    protected String z2PREFIXO;
    @XmlElement(name = "Z2_TIPOROT", required = true)
    protected String z2TIPOROT;
    @XmlElement(name = "Z2_VALOR", required = true)
    protected String z2VALOR;
    @XmlElement(name = "Z2_VENCREA")
    protected String z2VENCREA;
    @XmlElement(name = "Z2_VENCTO", required = true)
    protected String z2VENCTO;
    @XmlElement(name = "Z2_VTXADMI", required = true)
    protected String z2VTXADMI;

    /**
     * Gets the value of the z2ADM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2ADM() {
        return z2ADM;
    }

    /**
     * Sets the value of the z2ADM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2ADM(String value) {
        this.z2ADM = value;
    }

    /**
     * Gets the value of the z2CNO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2CNO() {
        return z2CNO;
    }

    /**
     * Sets the value of the z2CNO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2CNO(String value) {
        this.z2CNO = value;
    }

    /**
     * Gets the value of the z2EMISSAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2EMISSAO() {
        return z2EMISSAO;
    }

    /**
     * Sets the value of the z2EMISSAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2EMISSAO(String value) {
        this.z2EMISSAO = value;
    }

    /**
     * Gets the value of the z2MATRICU property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2MATRICU() {
        return z2MATRICU;
    }

    /**
     * Sets the value of the z2MATRICU property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2MATRICU(String value) {
        this.z2MATRICU = value;
    }

    /**
     * Gets the value of the z2NUM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2NUM() {
        return z2NUM;
    }

    /**
     * Sets the value of the z2NUM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2NUM(String value) {
        this.z2NUM = value;
    }

    /**
     * Gets the value of the z2NUMCONT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2NUMCONT() {
        return z2NUMCONT;
    }

    /**
     * Sets the value of the z2NUMCONT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2NUMCONT(String value) {
        this.z2NUMCONT = value;
    }

    /**
     * Gets the value of the z2PARCELA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2PARCELA() {
        return z2PARCELA;
    }

    /**
     * Sets the value of the z2PARCELA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2PARCELA(String value) {
        this.z2PARCELA = value;
    }

    /**
     * Gets the value of the z2PREFIXO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2PREFIXO() {
        return z2PREFIXO;
    }

    /**
     * Sets the value of the z2PREFIXO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2PREFIXO(String value) {
        this.z2PREFIXO = value;
    }

    /**
     * Gets the value of the z2TIPOROT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2TIPOROT() {
        return z2TIPOROT;
    }

    /**
     * Sets the value of the z2TIPOROT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2TIPOROT(String value) {
        this.z2TIPOROT = value;
    }

    /**
     * Gets the value of the z2VALOR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2VALOR() {
        return z2VALOR;
    }

    /**
     * Sets the value of the z2VALOR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2VALOR(String value) {
        this.z2VALOR = value;
    }

    /**
     * Gets the value of the z2VENCREA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2VENCREA() {
        return z2VENCREA;
    }

    /**
     * Sets the value of the z2VENCREA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2VENCREA(String value) {
        this.z2VENCREA = value;
    }

    /**
     * Gets the value of the z2VENCTO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2VENCTO() {
        return z2VENCTO;
    }

    /**
     * Sets the value of the z2VENCTO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2VENCTO(String value) {
        this.z2VENCTO = value;
    }

    /**
     * Gets the value of the z2VTXADMI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2VTXADMI() {
        return z2VTXADMI;
    }

    /**
     * Sets the value of the z2VTXADMI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2VTXADMI(String value) {
        this.z2VTXADMI = value;
    }

}


package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSINCTITPROVRESULT" type="{http://54.207.46.24:91/}WSRETPROV"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wsinctitprovresult"
})
@XmlRootElement(name = "WSINCTITPROVRESPONSE")
public class WSINCTITPROVRESPONSE {

    @XmlElement(name = "WSINCTITPROVRESULT", required = true)
    protected WSRETPROV wsinctitprovresult;

    /**
     * Gets the value of the wsinctitprovresult property.
     * 
     * @return
     *     possible object is
     *     {@link WSRETPROV }
     *     
     */
    public WSRETPROV getWSINCTITPROVRESULT() {
        return wsinctitprovresult;
    }

    /**
     * Sets the value of the wsinctitprovresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link WSRETPROV }
     *     
     */
    public void setWSINCTITPROVRESULT(WSRETPROV value) {
        this.wsinctitprovresult = value;
    }

}


package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for INCPROV complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="INCPROV">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="NINCPROV" type="{http://54.207.46.24:91/}ARRAYOFWSDADPROV"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "INCPROV", propOrder = {
    "nincprov"
})
public class INCPROV {

    @XmlElement(name = "NINCPROV", required = true)
    protected ARRAYOFWSDADPROV nincprov;

    /**
     * Gets the value of the nincprov property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSDADPROV }
     *     
     */
    public ARRAYOFWSDADPROV getNINCPROV() {
        return nincprov;
    }

    /**
     * Sets the value of the nincprov property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSDADPROV }
     *     
     */
    public void setNINCPROV(ARRAYOFWSDADPROV value) {
        this.nincprov = value;
    }

}

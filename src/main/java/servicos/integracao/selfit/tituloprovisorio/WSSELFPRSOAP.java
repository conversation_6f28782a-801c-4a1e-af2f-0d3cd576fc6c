
package servicos.integracao.selfit.tituloprovisorio;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "WSSELFPRSOAP", targetNamespace = "http://54.207.46.24:91/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface WSSELFPRSOAP {


    /**
     * Consulta Processamento Título Provisório
     * 
     * @param cnpjempr
     * @param cchavgrv
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.tituloprovisorio.ARRAYOFWSRETTPRO
     */
    @WebMethod(operationName = "WSCONPROC", action = "http://54.207.46.24:91/WSCONPROC")
    @WebResult(name = "WSCONPROCRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSCONPROC", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.tituloprovisorio.WSCONPROC")
    @ResponseWrapper(localName = "WSCONPROCRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.tituloprovisorio.WSCONPROCRESPONSE")
    public ARRAYOFWSRETTPRO wsconproc(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CCHAVGRV", targetNamespace = "http://54.207.46.24:91/")
        String cchavgrv);

    /**
     * Incluir Titulo Provisorio
     * 
     * @param cnpjclie
     * @param adadprov
     * @param cnpjempr
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.tituloprovisorio.WSRETPROV
     */
    @WebMethod(operationName = "WSINCTITPROV", action = "http://54.207.46.24:91/WSINCTITPROV")
    @WebResult(name = "WSINCTITPROVRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSINCTITPROV", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.tituloprovisorio.WSINCTITPROV")
    @ResponseWrapper(localName = "WSINCTITPROVRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.tituloprovisorio.WSINCTITPROVRESPONSE")
    public WSRETPROV wsinctitprov(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CNPJCLIE", targetNamespace = "http://54.207.46.24:91/")
        String cnpjclie,
        @WebParam(name = "ADADPROV", targetNamespace = "http://54.207.46.24:91/")
        INCPROV adadprov);

}

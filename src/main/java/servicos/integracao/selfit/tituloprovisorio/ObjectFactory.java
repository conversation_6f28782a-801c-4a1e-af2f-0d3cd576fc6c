
package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.selfit.tituloprovisorio package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.selfit.tituloprovisorio
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link WSINCTITPROVRESPONSE }
     * 
     */
    public WSINCTITPROVRESPONSE createWSINCTITPROVRESPONSE() {
        return new WSINCTITPROVRESPONSE();
    }

    /**
     * Create an instance of {@link WSRETPROV }
     * 
     */
    public WSRETPROV createWSRETPROV() {
        return new WSRETPROV();
    }

    /**
     * Create an instance of {@link WSINCTITPROV }
     * 
     */
    public WSINCTITPROV createWSINCTITPROV() {
        return new WSINCTITPROV();
    }

    /**
     * Create an instance of {@link INCPROV }
     * 
     */
    public INCPROV createINCPROV() {
        return new INCPROV();
    }

    /**
     * Create an instance of {@link WSCONPROCRESPONSE }
     * 
     */
    public WSCONPROCRESPONSE createWSCONPROCRESPONSE() {
        return new WSCONPROCRESPONSE();
    }

    /**
     * Create an instance of {@link ARRAYOFWSRETTPRO }
     * 
     */
    public ARRAYOFWSRETTPRO createARRAYOFWSRETTPRO() {
        return new ARRAYOFWSRETTPRO();
    }

    /**
     * Create an instance of {@link WSCONPROC }
     * 
     */
    public WSCONPROC createWSCONPROC() {
        return new WSCONPROC();
    }

    /**
     * Create an instance of {@link WSDADPROV }
     * 
     */
    public WSDADPROV createWSDADPROV() {
        return new WSDADPROV();
    }

    /**
     * Create an instance of {@link WSRETTPRO }
     * 
     */
    public WSRETTPRO createWSRETTPRO() {
        return new WSRETTPRO();
    }

    /**
     * Create an instance of {@link ARRAYOFWSCHVPROV }
     * 
     */
    public ARRAYOFWSCHVPROV createARRAYOFWSCHVPROV() {
        return new ARRAYOFWSCHVPROV();
    }

    /**
     * Create an instance of {@link ARRAYOFWSDADPROV }
     * 
     */
    public ARRAYOFWSDADPROV createARRAYOFWSDADPROV() {
        return new ARRAYOFWSDADPROV();
    }

    /**
     * Create an instance of {@link WSCHVPROV }
     * 
     */
    public WSCHVPROV createWSCHVPROV() {
        return new WSCHVPROV();
    }

}


package servicos.integracao.selfit.tituloprovisorio;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * Servico de Gravacao Titulos Provisório
 * 
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "WSSELFPR", targetNamespace = "http://54.207.46.24:91/", wsdlLocation = "http://54.207.46.24:91/ws/WSSELFPR.apw?WSDL")
public class WSSELFPR
    extends Service
{

    private final static URL WSSELFPR_WSDL_LOCATION;
    private final static WebServiceException WSSELFPR_EXCEPTION;
    private final static QName WSSELFPR_QNAME = new QName("http://54.207.46.24:91/", "WSSELFPR");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://54.207.46.24:91/ws/WSSELFPR.apw?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WSSELFPR_WSDL_LOCATION = url;
        WSSELFPR_EXCEPTION = e;
    }

    public WSSELFPR() {
        super(__getWsdlLocation(), WSSELFPR_QNAME);
    }

    public WSSELFPR(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns WSSELFPRSOAP
     */
    @WebEndpoint(name = "WSSELFPRSOAP")
    public WSSELFPRSOAP getWSSELFPRSOAP() {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFPRSOAP"), WSSELFPRSOAP.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns WSSELFPRSOAP
     */
    @WebEndpoint(name = "WSSELFPRSOAP")
    public WSSELFPRSOAP getWSSELFPRSOAP(WebServiceFeature... features) {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFPRSOAP"), WSSELFPRSOAP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (WSSELFPR_EXCEPTION!= null) {
            throw WSSELFPR_EXCEPTION;
        }
        return WSSELFPR_WSDL_LOCATION;
    }

}

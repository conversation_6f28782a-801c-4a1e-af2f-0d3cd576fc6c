
package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSRETPROV complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSRETPROV">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CHAVGRV" type="{http://54.207.46.24:91/}ARRAYOFWSCHVPROV" minOccurs="0"/>
 *         &lt;element name="CSTATUS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LRET" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="MSGERRO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSRETPROV", propOrder = {
    "chavgrv",
    "cstatus",
    "lret",
    "msgerro"
})
public class WSRETPROV {

    @XmlElement(name = "CHAVGRV")
    protected ARRAYOFWSCHVPROV chavgrv;
    @XmlElement(name = "CSTATUS", required = true)
    protected String cstatus;
    @XmlElement(name = "LRET")
    protected boolean lret;
    @XmlElement(name = "MSGERRO")
    protected String msgerro;

    /**
     * Gets the value of the chavgrv property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSCHVPROV }
     *     
     */
    public ARRAYOFWSCHVPROV getCHAVGRV() {
        return chavgrv;
    }

    /**
     * Sets the value of the chavgrv property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSCHVPROV }
     *     
     */
    public void setCHAVGRV(ARRAYOFWSCHVPROV value) {
        this.chavgrv = value;
    }

    /**
     * Gets the value of the cstatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCSTATUS() {
        return cstatus;
    }

    /**
     * Sets the value of the cstatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCSTATUS(String value) {
        this.cstatus = value;
    }

    /**
     * Gets the value of the lret property.
     * 
     */
    public boolean isLRET() {
        return lret;
    }

    /**
     * Sets the value of the lret property.
     * 
     */
    public void setLRET(boolean value) {
        this.lret = value;
    }

    /**
     * Gets the value of the msgerro property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMSGERRO() {
        return msgerro;
    }

    /**
     * Sets the value of the msgerro property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMSGERRO(String value) {
        this.msgerro = value;
    }

}

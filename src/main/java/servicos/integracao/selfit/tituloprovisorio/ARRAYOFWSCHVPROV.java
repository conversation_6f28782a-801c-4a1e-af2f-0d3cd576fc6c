
package servicos.integracao.selfit.tituloprovisorio;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSCHVPROV complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSCHVPROV">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSCHVPROV" type="{http://54.207.46.24:91/}WSCHVPROV" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSCHVPROV", propOrder = {
    "wschvprov"
})
public class ARRAYOFWSCHVPROV {

    @XmlElement(name = "WSCHVPROV")
    protected List<WSCHVPROV> wschvprov;

    /**
     * Gets the value of the wschvprov property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wschvprov property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSCHVPROV().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSCHVPROV }
     * 
     * 
     */
    public List<WSCHVPROV> getWSCHVPROV() {
        if (wschvprov == null) {
            wschvprov = new ArrayList<WSCHVPROV>();
        }
        return this.wschvprov;
    }

}


package servicos.integracao.selfit.tituloprovisorio;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSCHVPROV complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSCHVPROV">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CCHAVPROV" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MENSGRAVA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_PARCELA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z2_PREFIXO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSCHVPROV", propOrder = {
    "cchavprov",
    "mensgrava",
    "z2NUM",
    "z2PARCELA",
    "z2PREFIXO"
})
public class WSCHVPROV {

    @XmlElement(name = "CCHAVPROV", required = true)
    protected String cchavprov;
    @XmlElement(name = "MENSGRAVA", required = true)
    protected String mensgrava;
    @XmlElement(name = "Z2_NUM", required = true)
    protected String z2NUM;
    @XmlElement(name = "Z2_PARCELA", required = true)
    protected String z2PARCELA;
    @XmlElement(name = "Z2_PREFIXO", required = true)
    protected String z2PREFIXO;

    /**
     * Gets the value of the cchavprov property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCCHAVPROV() {
        return cchavprov;
    }

    /**
     * Sets the value of the cchavprov property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCCHAVPROV(String value) {
        this.cchavprov = value;
    }

    /**
     * Gets the value of the mensgrava property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMENSGRAVA() {
        return mensgrava;
    }

    /**
     * Sets the value of the mensgrava property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMENSGRAVA(String value) {
        this.mensgrava = value;
    }

    /**
     * Gets the value of the z2NUM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2NUM() {
        return z2NUM;
    }

    /**
     * Sets the value of the z2NUM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2NUM(String value) {
        this.z2NUM = value;
    }

    /**
     * Gets the value of the z2PARCELA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2PARCELA() {
        return z2PARCELA;
    }

    /**
     * Sets the value of the z2PARCELA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2PARCELA(String value) {
        this.z2PARCELA = value;
    }

    /**
     * Gets the value of the z2PREFIXO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ2PREFIXO() {
        return z2PREFIXO;
    }

    /**
     * Sets the value of the z2PREFIXO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ2PREFIXO(String value) {
        this.z2PREFIXO = value;
    }

}

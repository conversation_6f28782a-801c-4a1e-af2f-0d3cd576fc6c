
package servicos.integracao.selfit.tituloprovisorio;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSRETTPRO complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSRETTPRO">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSRETTPRO" type="{http://54.207.46.24:91/}WSRETTPRO" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSRETTPRO", propOrder = {
    "wsrettpro"
})
public class ARRAYOFWSRETTPRO {

    @XmlElement(name = "WSRETTPRO")
    protected List<WSRETTPRO> wsrettpro;

    /**
     * Gets the value of the wsrettpro property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wsrettpro property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSRETTPRO().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSRETTPRO }
     * 
     * 
     */
    public List<WSRETTPRO> getWSRETTPRO() {
        if (wsrettpro == null) {
            wsrettpro = new ArrayList<WSRETTPRO>();
        }
        return this.wsrettpro;
    }

}

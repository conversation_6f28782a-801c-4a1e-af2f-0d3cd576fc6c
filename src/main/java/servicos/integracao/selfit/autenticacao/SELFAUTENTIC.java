
package servicos.integracao.selfit.autenticacao;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * Serviço de Autenticação
 * 
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "SELFAUTENTIC", targetNamespace = "http://54.207.46.24:91/", wsdlLocation = "http://54.207.46.24:91/ws/SELFAUTENTIC.apw?WSDL")
public class SELFAUTENTIC
    extends Service
{

    private final static URL SELFAUTENTIC_WSDL_LOCATION;
    private final static WebServiceException SELFAUTENTIC_EXCEPTION;
    private final static QName SELFAUTENTIC_QNAME = new QName("http://54.207.46.24:91/", "SELFAUTENTIC");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://54.207.46.24:91/ws/SELFAUTENTIC.apw?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        SELFAUTENTIC_WSDL_LOCATION = url;
        SELFAUTENTIC_EXCEPTION = e;
    }

    public SELFAUTENTIC() {
        super(__getWsdlLocation(), SELFAUTENTIC_QNAME);
    }

    public SELFAUTENTIC(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns SELFAUTENTICSOAP
     */
    @WebEndpoint(name = "SELFAUTENTICSOAP")
    public SELFAUTENTICSOAP getSELFAUTENTICSOAP() {
        return super.getPort(new QName("http://54.207.46.24:91/", "SELFAUTENTICSOAP"), SELFAUTENTICSOAP.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns SELFAUTENTICSOAP
     */
    @WebEndpoint(name = "SELFAUTENTICSOAP")
    public SELFAUTENTICSOAP getSELFAUTENTICSOAP(WebServiceFeature... features) {
        return super.getPort(new QName("http://54.207.46.24:91/", "SELFAUTENTICSOAP"), SELFAUTENTICSOAP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (SELFAUTENTIC_EXCEPTION!= null) {
            throw SELFAUTENTIC_EXCEPTION;
        }
        return SELFAUTENTIC_WSDL_LOCATION;
    }

}

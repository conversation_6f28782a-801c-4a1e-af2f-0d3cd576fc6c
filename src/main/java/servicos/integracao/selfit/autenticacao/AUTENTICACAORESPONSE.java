
package servicos.integracao.selfit.autenticacao;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AUTENTICACAORESULT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "autenticacaoresult"
})
@XmlRootElement(name = "AUTENTICACAORESPONSE")
public class AUTENTICACAORESPONSE {

    @XmlElement(name = "AUTENTICACAORESULT", required = true)
    protected String autenticacaoresult;

    /**
     * Gets the value of the autenticacaoresult property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAUTENTICACAORESULT() {
        return autenticacaoresult;
    }

    /**
     * Sets the value of the autenticacaoresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAUTENTICACAORESULT(String value) {
        this.autenticacaoresult = value;
    }

}


package servicos.integracao.selfit.autenticacao;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "SELFAUTENTICSOAP", targetNamespace = "http://54.207.46.24:91/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface SELFAUTENTICSOAP {


    /**
     * 
     * @param cnpjempr
     * @param cnpjself
     * @param chavemes
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "AUTENTICACAO", action = "http://54.207.46.24:91/AUTENTICACAO")
    @WebResult(name = "AUTENTICACAORESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "AUTENTICACAO", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.autenticacao.AUTENTICACAO")
    @ResponseWrapper(localName = "AUTENTICACAORESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.autenticacao.AUTENTICACAORESPONSE")
    public String autenticacao(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJSELF", targetNamespace = "http://54.207.46.24:91/")
        String cnpjself,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr);

}


package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.selfit.titulooperadora package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.selfit.titulooperadora
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link WSINCTITOPERA }
     * 
     */
    public WSINCTITOPERA createWSINCTITOPERA() {
        return new WSINCTITOPERA();
    }

    /**
     * Create an instance of {@link INCTOPE }
     * 
     */
    public INCTOPE createINCTOPE() {
        return new INCTOPE();
    }

    /**
     * Create an instance of {@link WSINCTITOPERARESPONSE }
     * 
     */
    public WSINCTITOPERARESPONSE createWSINCTITOPERARESPONSE() {
        return new WSINCTITOPERARESPONSE();
    }

    /**
     * Create an instance of {@link WSRETTOPER }
     * 
     */
    public WSRETTOPER createWSRETTOPER() {
        return new WSRETTOPER();
    }

    /**
     * Create an instance of {@link WSCONTIOPRESPONSE }
     * 
     */
    public WSCONTIOPRESPONSE createWSCONTIOPRESPONSE() {
        return new WSCONTIOPRESPONSE();
    }

    /**
     * Create an instance of {@link ARRAYOFWSRETCTOPE }
     * 
     */
    public ARRAYOFWSRETCTOPE createARRAYOFWSRETCTOPE() {
        return new ARRAYOFWSRETCTOPE();
    }

    /**
     * Create an instance of {@link WSCONTIOP }
     * 
     */
    public WSCONTIOP createWSCONTIOP() {
        return new WSCONTIOP();
    }

    /**
     * Create an instance of {@link ARRAYOFWSDADTOPE }
     * 
     */
    public ARRAYOFWSDADTOPE createARRAYOFWSDADTOPE() {
        return new ARRAYOFWSDADTOPE();
    }

    /**
     * Create an instance of {@link ARRAYOFWSCHVTOPE }
     * 
     */
    public ARRAYOFWSCHVTOPE createARRAYOFWSCHVTOPE() {
        return new ARRAYOFWSCHVTOPE();
    }

    /**
     * Create an instance of {@link WSCHVTOPE }
     * 
     */
    public WSCHVTOPE createWSCHVTOPE() {
        return new WSCHVTOPE();
    }

    /**
     * Create an instance of {@link WSRETCTOPE }
     * 
     */
    public WSRETCTOPE createWSRETCTOPE() {
        return new WSRETCTOPE();
    }

    /**
     * Create an instance of {@link WSDADTOPE }
     * 
     */
    public WSDADTOPE createWSDADTOPE() {
        return new WSDADTOPE();
    }

}

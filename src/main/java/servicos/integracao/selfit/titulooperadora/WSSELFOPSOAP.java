
package servicos.integracao.selfit.titulooperadora;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "WSSELFOPSOAP", targetNamespace = "http://54.207.46.24:91/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface WSSELFOPSOAP {


    /**
     * Consulta Processamento Título Operadora
     * 
     * @param cnpjempr
     * @param cchavgrv
     * @param chavemes
     * @return
     *     returns servicos.integracao.selfit.titulooperadora.ARRAYOFWSRETCTOPE
     */
    @WebMethod(operationName = "WSCONTIOP", action = "http://54.207.46.24:91/WSCONTIOP")
    @WebResult(name = "WSCONTIOPRESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSCONTIOP", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.titulooperadora.WSCONTIOP")
    @ResponseWrapper(localName = "WSCONTIOPRESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.titulooperadora.WSCONTIOPRESPONSE")
    public ARRAYOFWSRETCTOPE wscontiop(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "CCHAVGRV", targetNamespace = "http://54.207.46.24:91/")
        String cchavgrv);

    /**
     * Incluir Titulo Operadora
     * 
     * @param cnpjempr
     * @param chavemes
     * @param adadtope
     * @return
     *     returns servicos.integracao.selfit.titulooperadora.WSRETTOPER
     */
    @WebMethod(operationName = "WSINCTITOPERA", action = "http://54.207.46.24:91/WSINCTITOPERA")
    @WebResult(name = "WSINCTITOPERARESULT", targetNamespace = "http://54.207.46.24:91/")
    @RequestWrapper(localName = "WSINCTITOPERA", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.titulooperadora.WSINCTITOPERA")
    @ResponseWrapper(localName = "WSINCTITOPERARESPONSE", targetNamespace = "http://54.207.46.24:91/", className = "servicos.integracao.selfit.titulooperadora.WSINCTITOPERARESPONSE")
    public WSRETTOPER wsinctitopera(
        @WebParam(name = "CHAVEMES", targetNamespace = "http://54.207.46.24:91/")
        String chavemes,
        @WebParam(name = "CNPJEMPR", targetNamespace = "http://54.207.46.24:91/")
        String cnpjempr,
        @WebParam(name = "ADADTOPE", targetNamespace = "http://54.207.46.24:91/")
        INCTOPE adadtope);

}

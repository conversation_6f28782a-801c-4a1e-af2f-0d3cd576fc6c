
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSCONTIOPRESULT" type="{http://54.207.46.24:91/}ARRAYOFWSRETCTOPE"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wscontiopresult"
})
@XmlRootElement(name = "WSCONTIOPRESPONSE")
public class WSCONTIOPRESPONSE {

    @XmlElement(name = "WSCONTIOPRESULT", required = true)
    protected ARRAYOFWSRETCTOPE wscontiopresult;

    /**
     * Gets the value of the wscontiopresult property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSRETCTOPE }
     *     
     */
    public ARRAYOFWSRETCTOPE getWSCONTIOPRESULT() {
        return wscontiopresult;
    }

    /**
     * Sets the value of the wscontiopresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSRETCTOPE }
     *     
     */
    public void setWSCONTIOPRESULT(ARRAYOFWSRETCTOPE value) {
        this.wscontiopresult = value;
    }

}

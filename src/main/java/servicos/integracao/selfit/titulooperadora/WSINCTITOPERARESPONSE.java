
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSINCTITOPERARESULT" type="{http://54.207.46.24:91/}WSRETTOPER"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "wsinctitoperaresult"
})
@XmlRootElement(name = "WSINCTITOPERARESPONSE")
public class WSINCTITOPERARESPONSE {

    @XmlElement(name = "WSINCTITOPERARESULT", required = true)
    protected WSRETTOPER wsinctitoperaresult;

    /**
     * Gets the value of the wsinctitoperaresult property.
     * 
     * @return
     *     possible object is
     *     {@link WSRETTOPER }
     *     
     */
    public WSRETTOPER getWSINCTITOPERARESULT() {
        return wsinctitoperaresult;
    }

    /**
     * Sets the value of the wsinctitoperaresult property.
     * 
     * @param value
     *     allowed object is
     *     {@link WSRETTOPER }
     *     
     */
    public void setWSINCTITOPERARESULT(WSRETTOPER value) {
        this.wsinctitoperaresult = value;
    }

}

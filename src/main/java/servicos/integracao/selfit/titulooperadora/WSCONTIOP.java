
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CHAVEMES" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CNPJEMPR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CCHAVGRV" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "chavemes",
    "cnpjempr",
    "cchavgrv"
})
@XmlRootElement(name = "WSCONTIOP")
public class WSCONTIOP {

    @XmlElement(name = "CHAVEMES", required = true)
    protected String chavemes;
    @XmlElement(name = "CNPJEMPR", required = true)
    protected String cnpjempr;
    @XmlElement(name = "CCHAVGRV", required = true)
    protected String cchavgrv;

    /**
     * Gets the value of the chavemes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHAVEMES() {
        return chavemes;
    }

    /**
     * Sets the value of the chavemes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHAVEMES(String value) {
        this.chavemes = value;
    }

    /**
     * Gets the value of the cnpjempr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCNPJEMPR() {
        return cnpjempr;
    }

    /**
     * Sets the value of the cnpjempr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCNPJEMPR(String value) {
        this.cnpjempr = value;
    }

    /**
     * Gets the value of the cchavgrv property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCCHAVGRV() {
        return cchavgrv;
    }

    /**
     * Sets the value of the cchavgrv property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCCHAVGRV(String value) {
        this.cchavgrv = value;
    }

}

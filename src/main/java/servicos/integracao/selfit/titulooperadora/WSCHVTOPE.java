
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSCHVTOPE complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSCHVTOPE">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CCHAVPROV" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MENSGRAVA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_CLIEORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_LOJAORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_NUMORIG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_PARCORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_PRFORIG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_TIPOORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSCHVTOPE", propOrder = {
    "cchavprov",
    "mensgrava",
    "z3CLIEORI",
    "z3LOJAORI",
    "z3NUMORIG",
    "z3PARCORI",
    "z3PRFORIG",
    "z3TIPOORI"
})
public class WSCHVTOPE {

    @XmlElement(name = "CCHAVPROV", required = true)
    protected String cchavprov;
    @XmlElement(name = "MENSGRAVA", required = true)
    protected String mensgrava;
    @XmlElement(name = "Z3_CLIEORI", required = true)
    protected String z3CLIEORI;
    @XmlElement(name = "Z3_LOJAORI", required = true)
    protected String z3LOJAORI;
    @XmlElement(name = "Z3_NUMORIG", required = true)
    protected String z3NUMORIG;
    @XmlElement(name = "Z3_PARCORI", required = true)
    protected String z3PARCORI;
    @XmlElement(name = "Z3_PRFORIG", required = true)
    protected String z3PRFORIG;
    @XmlElement(name = "Z3_TIPOORI", required = true)
    protected String z3TIPOORI;

    /**
     * Gets the value of the cchavprov property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCCHAVPROV() {
        return cchavprov;
    }

    /**
     * Sets the value of the cchavprov property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCCHAVPROV(String value) {
        this.cchavprov = value;
    }

    /**
     * Gets the value of the mensgrava property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMENSGRAVA() {
        return mensgrava;
    }

    /**
     * Sets the value of the mensgrava property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMENSGRAVA(String value) {
        this.mensgrava = value;
    }

    /**
     * Gets the value of the z3CLIEORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3CLIEORI() {
        return z3CLIEORI;
    }

    /**
     * Sets the value of the z3CLIEORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3CLIEORI(String value) {
        this.z3CLIEORI = value;
    }

    /**
     * Gets the value of the z3LOJAORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3LOJAORI() {
        return z3LOJAORI;
    }

    /**
     * Sets the value of the z3LOJAORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3LOJAORI(String value) {
        this.z3LOJAORI = value;
    }

    /**
     * Gets the value of the z3NUMORIG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3NUMORIG() {
        return z3NUMORIG;
    }

    /**
     * Sets the value of the z3NUMORIG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3NUMORIG(String value) {
        this.z3NUMORIG = value;
    }

    /**
     * Gets the value of the z3PARCORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3PARCORI() {
        return z3PARCORI;
    }

    /**
     * Sets the value of the z3PARCORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3PARCORI(String value) {
        this.z3PARCORI = value;
    }

    /**
     * Gets the value of the z3PRFORIG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3PRFORIG() {
        return z3PRFORIG;
    }

    /**
     * Sets the value of the z3PRFORIG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3PRFORIG(String value) {
        this.z3PRFORIG = value;
    }

    /**
     * Gets the value of the z3TIPOORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3TIPOORI() {
        return z3TIPOORI;
    }

    /**
     * Sets the value of the z3TIPOORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3TIPOORI(String value) {
        this.z3TIPOORI = value;
    }

}

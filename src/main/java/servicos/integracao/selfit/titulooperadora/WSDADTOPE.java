
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSDADTOPE complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSDADTOPE">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Z3_ADM" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_CLIEORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_CNO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_EMISSAO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_LOJAORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_MATRICU" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_NUMCONT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_NUMORIG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_PARCORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_PRFORIG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_TIPOORI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_TIPOROT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_VALOR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_VENCTO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z3_VTXADMI" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSDADTOPE", propOrder = {
    "z3ADM",
    "z3CLIEORI",
    "z3CNO",
    "z3EMISSAO",
    "z3LOJAORI",
    "z3MATRICU",
    "z3NUMCONT",
    "z3NUMORIG",
    "z3PARCORI",
    "z3PRFORIG",
    "z3TIPOORI",
    "z3TIPOROT",
    "z3VALOR",
    "z3VENCTO",
    "z3VTXADMI"
})
public class WSDADTOPE {

    @XmlElement(name = "Z3_ADM", required = true)
    protected String z3ADM;
    @XmlElement(name = "Z3_CLIEORI", required = true)
    protected String z3CLIEORI;
    @XmlElement(name = "Z3_CNO", required = true)
    protected String z3CNO;
    @XmlElement(name = "Z3_EMISSAO", required = true)
    protected String z3EMISSAO;
    @XmlElement(name = "Z3_LOJAORI", required = true)
    protected String z3LOJAORI;
    @XmlElement(name = "Z3_MATRICU", required = true)
    protected String z3MATRICU;
    @XmlElement(name = "Z3_NUMCONT", required = true)
    protected String z3NUMCONT;
    @XmlElement(name = "Z3_NUMORIG", required = true)
    protected String z3NUMORIG;
    @XmlElement(name = "Z3_PARCORI", required = true)
    protected String z3PARCORI;
    @XmlElement(name = "Z3_PRFORIG", required = true)
    protected String z3PRFORIG;
    @XmlElement(name = "Z3_TIPOORI", required = true)
    protected String z3TIPOORI;
    @XmlElement(name = "Z3_TIPOROT", required = true)
    protected String z3TIPOROT;
    @XmlElement(name = "Z3_VALOR", required = true)
    protected String z3VALOR;
    @XmlElement(name = "Z3_VENCTO", required = true)
    protected String z3VENCTO;
    @XmlElement(name = "Z3_VTXADMI", required = true)
    protected String z3VTXADMI;

    /**
     * Gets the value of the z3ADM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3ADM() {
        return z3ADM;
    }

    /**
     * Sets the value of the z3ADM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3ADM(String value) {
        this.z3ADM = value;
    }

    /**
     * Gets the value of the z3CLIEORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3CLIEORI() {
        return z3CLIEORI;
    }

    /**
     * Sets the value of the z3CLIEORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3CLIEORI(String value) {
        this.z3CLIEORI = value;
    }

    /**
     * Gets the value of the z3CNO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3CNO() {
        return z3CNO;
    }

    /**
     * Sets the value of the z3CNO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3CNO(String value) {
        this.z3CNO = value;
    }

    /**
     * Gets the value of the z3EMISSAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3EMISSAO() {
        return z3EMISSAO;
    }

    /**
     * Sets the value of the z3EMISSAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3EMISSAO(String value) {
        this.z3EMISSAO = value;
    }

    /**
     * Gets the value of the z3LOJAORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3LOJAORI() {
        return z3LOJAORI;
    }

    /**
     * Sets the value of the z3LOJAORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3LOJAORI(String value) {
        this.z3LOJAORI = value;
    }

    /**
     * Gets the value of the z3MATRICU property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3MATRICU() {
        return z3MATRICU;
    }

    /**
     * Sets the value of the z3MATRICU property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3MATRICU(String value) {
        this.z3MATRICU = value;
    }

    /**
     * Gets the value of the z3NUMCONT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3NUMCONT() {
        return z3NUMCONT;
    }

    /**
     * Sets the value of the z3NUMCONT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3NUMCONT(String value) {
        this.z3NUMCONT = value;
    }

    /**
     * Gets the value of the z3NUMORIG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3NUMORIG() {
        return z3NUMORIG;
    }

    /**
     * Sets the value of the z3NUMORIG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3NUMORIG(String value) {
        this.z3NUMORIG = value;
    }

    /**
     * Gets the value of the z3PARCORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3PARCORI() {
        return z3PARCORI;
    }

    /**
     * Sets the value of the z3PARCORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3PARCORI(String value) {
        this.z3PARCORI = value;
    }

    /**
     * Gets the value of the z3PRFORIG property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3PRFORIG() {
        return z3PRFORIG;
    }

    /**
     * Sets the value of the z3PRFORIG property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3PRFORIG(String value) {
        this.z3PRFORIG = value;
    }

    /**
     * Gets the value of the z3TIPOORI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3TIPOORI() {
        return z3TIPOORI;
    }

    /**
     * Sets the value of the z3TIPOORI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3TIPOORI(String value) {
        this.z3TIPOORI = value;
    }

    /**
     * Gets the value of the z3TIPOROT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3TIPOROT() {
        return z3TIPOROT;
    }

    /**
     * Sets the value of the z3TIPOROT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3TIPOROT(String value) {
        this.z3TIPOROT = value;
    }

    /**
     * Gets the value of the z3VALOR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3VALOR() {
        return z3VALOR;
    }

    /**
     * Sets the value of the z3VALOR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3VALOR(String value) {
        this.z3VALOR = value;
    }

    /**
     * Gets the value of the z3VENCTO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3VENCTO() {
        return z3VENCTO;
    }

    /**
     * Sets the value of the z3VENCTO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3VENCTO(String value) {
        this.z3VENCTO = value;
    }

    /**
     * Gets the value of the z3VTXADMI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ3VTXADMI() {
        return z3VTXADMI;
    }

    /**
     * Sets the value of the z3VTXADMI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ3VTXADMI(String value) {
        this.z3VTXADMI = value;
    }

}

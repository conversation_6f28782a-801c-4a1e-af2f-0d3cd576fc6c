
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSRETCTOPE complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSRETCTOPE">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CHAVGRV" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CSTATUS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="E1_ADM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_BAIXA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_CARTAUT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_CLIENTE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_EMISSAO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_HIST" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_LOJA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_MATRICU" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_NOMCLI" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_NUM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_NUMCONT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_PARCELA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_PREFIXO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_SALDO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_TIPO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_VALOR" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_VENCREA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_VENCTO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="E1_VTXADMI" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LRET" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="MSGERRO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSRETCTOPE", propOrder = {
    "chavgrv",
    "cstatus",
    "e1ADM",
    "e1BAIXA",
    "e1CARTAUT",
    "e1CLIENTE",
    "e1EMISSAO",
    "e1HIST",
    "e1LOJA",
    "e1MATRICU",
    "e1NOMCLI",
    "e1NUM",
    "e1NUMCONT",
    "e1PARCELA",
    "e1PREFIXO",
    "e1SALDO",
    "e1TIPO",
    "e1VALOR",
    "e1VENCREA",
    "e1VENCTO",
    "e1VTXADMI",
    "lret",
    "msgerro"
})
public class WSRETCTOPE {

    @XmlElement(name = "CHAVGRV")
    protected String chavgrv;
    @XmlElement(name = "CSTATUS", required = true)
    protected String cstatus;
    @XmlElement(name = "E1_ADM")
    protected String e1ADM;
    @XmlElement(name = "E1_BAIXA")
    protected String e1BAIXA;
    @XmlElement(name = "E1_CARTAUT")
    protected String e1CARTAUT;
    @XmlElement(name = "E1_CLIENTE")
    protected String e1CLIENTE;
    @XmlElement(name = "E1_EMISSAO")
    protected String e1EMISSAO;
    @XmlElement(name = "E1_HIST")
    protected String e1HIST;
    @XmlElement(name = "E1_LOJA")
    protected String e1LOJA;
    @XmlElement(name = "E1_MATRICU")
    protected String e1MATRICU;
    @XmlElement(name = "E1_NOMCLI")
    protected String e1NOMCLI;
    @XmlElement(name = "E1_NUM")
    protected String e1NUM;
    @XmlElement(name = "E1_NUMCONT")
    protected String e1NUMCONT;
    @XmlElement(name = "E1_PARCELA")
    protected String e1PARCELA;
    @XmlElement(name = "E1_PREFIXO")
    protected String e1PREFIXO;
    @XmlElement(name = "E1_SALDO")
    protected String e1SALDO;
    @XmlElement(name = "E1_TIPO")
    protected String e1TIPO;
    @XmlElement(name = "E1_VALOR")
    protected String e1VALOR;
    @XmlElement(name = "E1_VENCREA")
    protected String e1VENCREA;
    @XmlElement(name = "E1_VENCTO")
    protected String e1VENCTO;
    @XmlElement(name = "E1_VTXADMI")
    protected String e1VTXADMI;
    @XmlElement(name = "LRET")
    protected boolean lret;
    @XmlElement(name = "MSGERRO")
    protected String msgerro;

    /**
     * Gets the value of the chavgrv property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHAVGRV() {
        return chavgrv;
    }

    /**
     * Sets the value of the chavgrv property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHAVGRV(String value) {
        this.chavgrv = value;
    }

    /**
     * Gets the value of the cstatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCSTATUS() {
        return cstatus;
    }

    /**
     * Sets the value of the cstatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCSTATUS(String value) {
        this.cstatus = value;
    }

    /**
     * Gets the value of the e1ADM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1ADM() {
        return e1ADM;
    }

    /**
     * Sets the value of the e1ADM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1ADM(String value) {
        this.e1ADM = value;
    }

    /**
     * Gets the value of the e1BAIXA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1BAIXA() {
        return e1BAIXA;
    }

    /**
     * Sets the value of the e1BAIXA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1BAIXA(String value) {
        this.e1BAIXA = value;
    }

    /**
     * Gets the value of the e1CARTAUT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1CARTAUT() {
        return e1CARTAUT;
    }

    /**
     * Sets the value of the e1CARTAUT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1CARTAUT(String value) {
        this.e1CARTAUT = value;
    }

    /**
     * Gets the value of the e1CLIENTE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1CLIENTE() {
        return e1CLIENTE;
    }

    /**
     * Sets the value of the e1CLIENTE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1CLIENTE(String value) {
        this.e1CLIENTE = value;
    }

    /**
     * Gets the value of the e1EMISSAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1EMISSAO() {
        return e1EMISSAO;
    }

    /**
     * Sets the value of the e1EMISSAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1EMISSAO(String value) {
        this.e1EMISSAO = value;
    }

    /**
     * Gets the value of the e1HIST property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1HIST() {
        return e1HIST;
    }

    /**
     * Sets the value of the e1HIST property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1HIST(String value) {
        this.e1HIST = value;
    }

    /**
     * Gets the value of the e1LOJA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1LOJA() {
        return e1LOJA;
    }

    /**
     * Sets the value of the e1LOJA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1LOJA(String value) {
        this.e1LOJA = value;
    }

    /**
     * Gets the value of the e1MATRICU property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1MATRICU() {
        return e1MATRICU;
    }

    /**
     * Sets the value of the e1MATRICU property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1MATRICU(String value) {
        this.e1MATRICU = value;
    }

    /**
     * Gets the value of the e1NOMCLI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1NOMCLI() {
        return e1NOMCLI;
    }

    /**
     * Sets the value of the e1NOMCLI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1NOMCLI(String value) {
        this.e1NOMCLI = value;
    }

    /**
     * Gets the value of the e1NUM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1NUM() {
        return e1NUM;
    }

    /**
     * Sets the value of the e1NUM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1NUM(String value) {
        this.e1NUM = value;
    }

    /**
     * Gets the value of the e1NUMCONT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1NUMCONT() {
        return e1NUMCONT;
    }

    /**
     * Sets the value of the e1NUMCONT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1NUMCONT(String value) {
        this.e1NUMCONT = value;
    }

    /**
     * Gets the value of the e1PARCELA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1PARCELA() {
        return e1PARCELA;
    }

    /**
     * Sets the value of the e1PARCELA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1PARCELA(String value) {
        this.e1PARCELA = value;
    }

    /**
     * Gets the value of the e1PREFIXO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1PREFIXO() {
        return e1PREFIXO;
    }

    /**
     * Sets the value of the e1PREFIXO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1PREFIXO(String value) {
        this.e1PREFIXO = value;
    }

    /**
     * Gets the value of the e1SALDO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1SALDO() {
        return e1SALDO;
    }

    /**
     * Sets the value of the e1SALDO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1SALDO(String value) {
        this.e1SALDO = value;
    }

    /**
     * Gets the value of the e1TIPO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1TIPO() {
        return e1TIPO;
    }

    /**
     * Sets the value of the e1TIPO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1TIPO(String value) {
        this.e1TIPO = value;
    }

    /**
     * Gets the value of the e1VALOR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1VALOR() {
        return e1VALOR;
    }

    /**
     * Sets the value of the e1VALOR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1VALOR(String value) {
        this.e1VALOR = value;
    }

    /**
     * Gets the value of the e1VENCREA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1VENCREA() {
        return e1VENCREA;
    }

    /**
     * Sets the value of the e1VENCREA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1VENCREA(String value) {
        this.e1VENCREA = value;
    }

    /**
     * Gets the value of the e1VENCTO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1VENCTO() {
        return e1VENCTO;
    }

    /**
     * Sets the value of the e1VENCTO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1VENCTO(String value) {
        this.e1VENCTO = value;
    }

    /**
     * Gets the value of the e1VTXADMI property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getE1VTXADMI() {
        return e1VTXADMI;
    }

    /**
     * Sets the value of the e1VTXADMI property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setE1VTXADMI(String value) {
        this.e1VTXADMI = value;
    }

    /**
     * Gets the value of the lret property.
     * 
     */
    public boolean isLRET() {
        return lret;
    }

    /**
     * Sets the value of the lret property.
     * 
     */
    public void setLRET(boolean value) {
        this.lret = value;
    }

    /**
     * Gets the value of the msgerro property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMSGERRO() {
        return msgerro;
    }

    /**
     * Sets the value of the msgerro property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMSGERRO(String value) {
        this.msgerro = value;
    }

}

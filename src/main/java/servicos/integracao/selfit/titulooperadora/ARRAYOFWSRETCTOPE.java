
package servicos.integracao.selfit.titulooperadora;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSRETCTOPE complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSRETCTOPE">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSRETCTOPE" type="{http://54.207.46.24:91/}WSRETCTOPE" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSRETCTOPE", propOrder = {
    "wsretctope"
})
public class ARRAYOFWSRETCTOPE {

    @XmlElement(name = "WSRETCTOPE")
    protected List<WSRETCTOPE> wsretctope;

    /**
     * Gets the value of the wsretctope property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wsretctope property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSRETCTOPE().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSRETCTOPE }
     * 
     * 
     */
    public List<WSRETCTOPE> getWSRETCTOPE() {
        if (wsretctope == null) {
            wsretctope = new ArrayList<WSRETCTOPE>();
        }
        return this.wsretctope;
    }

}

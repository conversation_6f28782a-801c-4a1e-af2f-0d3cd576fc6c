
package servicos.integracao.selfit.titulooperadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for INCTOPE complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="INCTOPE">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="NINCTOPE" type="{http://54.207.46.24:91/}ARRAYOFWSDADTOPE"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "INCTOPE", propOrder = {
    "ninctope"
})
public class INCTOPE {

    @XmlElement(name = "NINCTOPE", required = true)
    protected ARRAYOFWSDADTOPE ninctope;

    /**
     * Gets the value of the ninctope property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSDADTOPE }
     *     
     */
    public ARRAYOFWSDADTOPE getNINCTOPE() {
        return ninctope;
    }

    /**
     * Sets the value of the ninctope property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSDADTOPE }
     *     
     */
    public void setNINCTOPE(ARRAYOFWSDADTOPE value) {
        this.ninctope = value;
    }

}

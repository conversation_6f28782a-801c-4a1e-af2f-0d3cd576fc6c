package servicos.integracao.intranet.service;

import br.com.pactosolucoes.comuns.json.EstatisticaSolicitacaoJSON;
import br.com.pactosolucoes.comuns.json.EstatisticaTicketJSON;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisJSON;
import negocio.comuns.utilitarias.UtilWS;
import negocio.intranet.SolicitacaoAndamentoJSON;
import negocio.intranet.SolicitacaoJSON;
import servicos.integracao.oamd.to.EmpresaFinanceiroTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 04/07/2017.
 */
public class DashboardService {

    private static DashboardService instance;

    public List<SolicitacaoJSON> buscarSolicitacoesEmAbertoServiceBMP(String emailDaEmpresa) throws Exception {
        try {
            List<SolicitacaoJSON> lista = consultarSolicitacoesEmAberto(emailDaEmpresa);
            for (SolicitacaoJSON solicitacaoJSON : lista) {
                if (solicitacaoJSON.getAndamentos() != null) {
                    Ordenacao.ordenarLista(solicitacaoJSON.getAndamentos(), "dataCadastro");
                    Collections.reverse(solicitacaoJSON.getAndamentos());
                }
            }
            return lista;
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "ERRO AO EXECUTAR buscarSolicitacoesEmAbertoServiceBMP. Erro:" + e.getMessage());
            throw e;
        }
    }

    public List<SolicitacaoJSON> buscarSolicitacoesConcluidasBMP(String emailDaEmpresa, Integer limitDados) throws Exception {
        try {
            List<SolicitacaoJSON> lista = consultarSolicitacoesConcluidas(emailDaEmpresa, limitDados);
            for (SolicitacaoJSON solicitacaoJSON : lista) {
                if (solicitacaoJSON.getAndamentos() != null) {
                    Ordenacao.ordenarLista(solicitacaoJSON.getAndamentos(), "dataCadastro");
                    Collections.reverse(solicitacaoJSON.getAndamentos());
                }
            }
            return lista;
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "ERRO AO EXECUTAR buscarSolicitacoesConcluidasBMP. Erro:" + e.getMessage());
            throw e;
        }
    }


    public void buscarAndamentosDasSolicitacoes(List<SolicitacaoJSON> listaSolicitacoes) throws Exception {
        try {
            StringBuilder listaEnviar = new StringBuilder();
            for (SolicitacaoJSON obj : listaSolicitacoes) {
                if ((obj.getAndamentos() == null) || (obj.getAndamentos().size() <= 0)) {
                    obj.setAndamentos(new ArrayList<SolicitacaoAndamentoJSON>());
                    if (listaEnviar.length() == 0) {
                        listaEnviar.append(obj.getId()).append("-").append(obj.getCodigo());
                    } else {
                        listaEnviar.append(",").append(obj.getId()).append("-").append(obj.getCodigo());
                    }
                }
            }
            if (listaEnviar.length() <= 0) {
                return;
            }
            Map<String, String> params = new HashMap<String, String>();
            params.put("listaSolicitacoes", listaEnviar.toString());
            String stringJsonAndamentos = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarAndamentosDasSolicitacoes", params);
            JSONArray jsonArrayAndamentos = new JSONArray(((new JSONObject(stringJsonAndamentos)).get("andamentos").toString()));
            List<SolicitacaoAndamentoJSON> lista = UteisJSON.jsonToListObject(SolicitacaoAndamentoJSON.class, jsonArrayAndamentos);
            for (SolicitacaoAndamentoJSON andamento : lista) {
                for (SolicitacaoJSON obj : listaSolicitacoes) {
                    if (obj.getId().equals(andamento.getIdSolicitacao())) {
                        obj.getAndamentos().add(andamento);
                    }
                }
            }
            for (SolicitacaoJSON solicitacaoJSON : listaSolicitacoes) {
                if (solicitacaoJSON.getAndamentos() != null) {
                    Ordenacao.ordenarLista(solicitacaoJSON.getAndamentos(), "dataCadastro");
                    Collections.reverse(solicitacaoJSON.getAndamentos());
                }
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "ERRO AO EXECUTAR buscarAndamentosDasSolicitacoes. Erro:" + e.getMessage());
            throw e;
        }
    }

    public void buscarAndamentosDaSolicitacao(SolicitacaoJSON solicitacaoJSON) throws Exception {
        try {
            if ((solicitacaoJSON.getAndamentos() == null) || (solicitacaoJSON.getAndamentos().size() <= 0)) {
                Map<String, String> params = new HashMap<String, String>();
                params.put("idSolicitacao", String.valueOf(solicitacaoJSON.getId()));
                params.put("codigoSolicitacao", solicitacaoJSON.getCodigo());
                params.put("origem", solicitacaoJSON.getOrigem());
                String stringJsonAndamentos = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarAndamentosDaSolicitacao", params);
                JSONArray jsonArrayAndamentos = new JSONArray(((new JSONObject(stringJsonAndamentos)).get("andamentos").toString()));
                List<SolicitacaoAndamentoJSON> lista = UteisJSON.jsonToListObject(SolicitacaoAndamentoJSON.class, jsonArrayAndamentos);
                Ordenacao.ordenarLista(lista, "dataCadastroOriginal");
                Collections.reverse(lista);
                solicitacaoJSON.setAndamentos(lista);
            }
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.SEVERE, "ERRO AO EXECUTAR buscarAndamentosDaSolicitacao. Erro:" + e.getMessage());
            throw e;
        }
    }


    public Integer consultarTotalSolicitacoesConcluidas(EmpresaFinanceiroTO empresaFinanceiroTO) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("codigoFinanceiro", String.valueOf(empresaFinanceiroTO.getCodigoFinanceiro()));
            String stringJsonTotal = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarTotalSolicitacoesConcluidas", params);
            return (new JSONObject(stringJsonTotal)).getInt("total");
        } catch (Exception e) {
            throw new Exception("Erro ao consultar o total de solicitações concluídas. Erro:" + e.getMessage());
        }
    }


    private List<SolicitacaoJSON> consultarSolicitacoesEmAberto(String emailDaEmpresa) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("emailDaEmpresa", emailDaEmpresa);
            params.put("consultarAndamentos", Boolean.FALSE.toString());
            params.put("consultarUltimaDataSolicitacao", Boolean.FALSE.toString());
            String stringJsonSolicitacoes = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarSolicitacoesEmAbertoPorEmailDaEmpresa", params);
            JSONArray jsonArraySolicitacoes = new JSONArray(((new JSONObject(stringJsonSolicitacoes)).get("solicitacoes").toString()));
            return UteisJSON.jsonToListObject(SolicitacaoJSON.class, jsonArraySolicitacoes);
        } catch (Exception e) {
            throw new Exception("Erro ao consultar as solicitações em aberto. Erro:" + e.getMessage());
        }
    }

    private List<SolicitacaoJSON> consultarSolicitacoesConcluidas(String emailDaEmpresa, Integer limitDados) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("emailDaEmpresa", emailDaEmpresa);
            params.put("limitDados", String.valueOf(limitDados));
            params.put("consultarAndamentos", Boolean.FALSE.toString());
            params.put("consultarUltimaDataSolicitacao", Boolean.FALSE.toString());
            String stringJsonSolicitacoes = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarSolicitacoesConcluidasPorEmailDaEmpresa", params);
            JSONArray jsonArraySolicitacoes = new JSONArray(((new JSONObject(stringJsonSolicitacoes)).get("solicitacoes").toString()));
            return UteisJSON.jsonToListObject(SolicitacaoJSON.class, jsonArraySolicitacoes);
        } catch (Exception e) {
            throw new Exception("Erro ao consultar as solicitações concluídas. Erro:" + e.getMessage());
        }
    }

    public EmpresaFinanceiroTO consultarEmpresaFinanceiro(String key) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("key", key);
            String stringJsonEmpresaFinanceiro = UtilWS.executeRequestOAMDRestFulComTratamentoDeErro("empresa/consultarEmpresaFinanceiro", params);
            JSONObject jsonObjectEmpresaFinanceiro = new JSONObject((new JSONObject(stringJsonEmpresaFinanceiro)).get("empresaFinanceiro").toString());
            return UteisJSON.jsonToObject(EmpresaFinanceiroTO.class, jsonObjectEmpresaFinanceiro);
        } catch (Exception e) {
            throw new Exception("Erro ao consultar a empresa no OAMD. " + e.getMessage());
        }
    }

    public EstatisticaSolicitacaoJSON consultarEstatisticaSolicitacao(String email, Integer qtdeSolAnalisarParaCalcularTempoMedioResposta) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("email", email);
            params.put("qtdeSolAnalisarParaCalcularTempoMedioResposta", String.valueOf(qtdeSolAnalisarParaCalcularTempoMedioResposta));
            String stringJson = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarEstatisticaSolicitacaoService", params);

            return UteisJSON.jsonToObject(EstatisticaSolicitacaoJSON.class, new JSONObject((new JSONObject(stringJson)).getString("estatisticaSolicitacao")));
        } catch (Exception e) {
            Uteis.logar("----- Erro consultarEstatisticaSolicitacao: " + e.getMessage(), this.getClass());
            throw new Exception("Erro ao consultar as estatísticas da solicitação. Erro:" + e.getMessage());
        }
    }

    public EstatisticaTicketJSON consultarEstatisticaMovidesk(String id) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("id", id);
            String stringJson = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarEstatisticasMovidesk", params);

            return UteisJSON.jsonToObject(EstatisticaTicketJSON.class, new JSONObject((new JSONObject(stringJson)).getString("estatisticaTicketJSON")));
        } catch (Exception e) {
            throw new Exception("Erro ao consultar as estatísticas da solicitação. Erro:" + e.getMessage());
        }
    }

    public EstatisticaSolicitacaoJSON consultarEstatisticaSolicitacaoPorCodigoFinanceiro(Integer codigoFinanceiro, Integer qtdeSolAnalisarParaCalcularTempoMedioResposta) throws Exception {
        try {
            Map<String, String> params = new HashMap<String, String>();
            params.put("codigoFinanceiro", String.valueOf(codigoFinanceiro));
            params.put("qtdeSolAnalisarParaCalcularTempoMedioResposta", String.valueOf(qtdeSolAnalisarParaCalcularTempoMedioResposta));
            String stringJson = UtilWS.executeRequestDashBoardRestFulComTratamentoDeErro("intranet/buscarEstatisticaSolicitacaoServicePorCodigo", params);
            return UteisJSON.jsonToObject(EstatisticaSolicitacaoJSON.class, new JSONObject((new JSONObject(stringJson)).getString("estatisticaSolicitacao")));
        } catch (Exception e) {
            throw new Exception("Erro ao consultar as estatísticas da solicitação. Erro:" + e.getMessage());
        }
    }
}

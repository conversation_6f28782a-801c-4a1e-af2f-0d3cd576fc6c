/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.enumerador;


/**
 *
 * <AUTHOR>
 */
public enum TipoLeadEnum {

    RDSTATION(0, "RD Station"),
    BUZZLEAD(1, "Buzz Lead"),
    GENERICO(2, "Genérico"),
    JOIN(3, "Join"),
    BITIRX24(5, "Bitrix24"),
    HUBSPOT(4, "Hubspot"),
    GENERICOGYMBOTPRO(6, "GymbotPro");

    private int id;
    private String descricao;

    TipoLeadEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public static TipoLeadEnum getPorCodigo(final int codigo) {
        for (TipoLeadEnum tipo : TipoLeadEnum.values()) {
            if (tipo.getId() == codigo) {
                return tipo;
            }
        }
        return null;
    }
}

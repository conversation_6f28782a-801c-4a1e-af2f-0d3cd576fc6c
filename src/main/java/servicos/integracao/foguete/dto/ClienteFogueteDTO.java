package servicos.integracao.foguete.dto;

public class ClienteFogueteDTO {
    private String identifier;
    private String status;
    private String start_date;
    private String due_date;
    private String cancellation_date;
    private ProductFogueteDTO product;
    private ProductPlanFogueteDTO product_plan;
    private OfferFogueteDTO offer;
    private CustomerFogueteDTO customer;
    private AttributesDTO attributes;

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getDue_date() {
        return due_date;
    }

    public void setDue_date(String due_date) {
        this.due_date = due_date;
    }

    public String getCancellation_date() {
        return cancellation_date;
    }

    public void setCancellation_date(String cancellation_date) {
        this.cancellation_date = cancellation_date;
    }

    public ProductFogueteDTO getProduct() {
        return product;
    }

    public void setProduct(ProductFogueteDTO product) {
        this.product = product;
    }

    public ProductPlanFogueteDTO getProduct_plan() {
        return product_plan;
    }

    public void setProduct_plan(ProductPlanFogueteDTO product_plan) {
        this.product_plan = product_plan;
    }

    public OfferFogueteDTO getOffer() {
        return offer;
    }

    public void setOffer(OfferFogueteDTO offer) {
        this.offer = offer;
    }

    public CustomerFogueteDTO getCustomer() {
        return customer;
    }

    public void setCustomer(CustomerFogueteDTO customer) {
        this.customer = customer;
    }

    public AttributesDTO getAttributes() {
        return attributes;
    }

    public void setAttributes(AttributesDTO attributes) {
        this.attributes = attributes;
    }
}

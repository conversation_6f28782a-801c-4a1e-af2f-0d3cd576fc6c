package servicos.integracao.whatsApp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class template {
    private Integer templateId;


    private String dataProgramada;

    private List<dadosCliente> dadosCliente;

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public String getDataProgramada() {
        return dataProgramada;
    }

    public void setDataProgramada(String dataProgramada) {
        this.dataProgramada = dataProgramada;
    }

    public List<dadosCliente> getDadosCliente() {
        return dadosCliente;
    }

    public void setDadosCliente(List<dadosCliente> dadosCliente) {
        this.dadosCliente = dadosCliente;
    }
}

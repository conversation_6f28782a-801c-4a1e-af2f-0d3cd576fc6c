package servicos.integracao.whatsApp;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import controle.crm.MsgBuildDTO;
import controle.crm.TemplateWhatsAppDTO;
import negocio.comuns.crm.MalaDiretaVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.vendas.VendasConfig;
import org.apache.http.HttpHeaders;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.sms.Message;

import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static controle.arquitetura.SuperControle.getUrlMidiaSocial;


public class WhatsAppController     {

    private final static String URI_API = getUrlMidiaSocial();  // "https://a47f-186-202-33-242.sa.ngrok.io";
    private final String key;
    private MalaDiretaVO malaDiretaVO;
    private Connection con;

    public WhatsAppController( String key,  MalaDiretaVO malaDiretaVO, Connection con) {
        this.con = con;
        this.key = key;
        this.malaDiretaVO = malaDiretaVO;
    }


    private static synchronized List<String> converterParaTemplateParceiro(String template) {
        Matcher matcher = Pattern.compile("(\\{\\{([^}]*)\\}\\})").matcher(template);
        String tags="";
        List<String> lstTags = new ArrayList<>();
        while(matcher.find()) {
            lstTags.add( matcher.group(2).toString());
        }
        return lstTags;
    }

    public List<String> personalizarTag(Message item) throws Exception {
        VendasConfig vendasConfigDAO;
        List<String> parameters = new ArrayList<>();
        vendasConfigDAO = new VendasConfig(getCon());
        try {

                List<String> lstTags = converterParaTemplateParceiro(item.getMsg());
                lstTags.forEach(itemTag ->{
                    switch (itemTag.toString()) {
                        case "TAG_PNOME":
                            parameters.add("TAG_PNOME" + ":" + (Uteis.formatarNome(Uteis.getPrimeiroNome(item.getNome()))));
                            break;
                        case "TAG_NOME":
                            parameters.add("TAG_NOME" + ":" + (Uteis.formatarNome(item.getNome())));
                            break;
                        case "TAG_PAGONLINE":
                            try {
                                parameters.add("TAG_PAGONLINE" + ":" +  vendasConfigDAO.tagPagamento(key, getMalaDiretaVO().getEmpresa().getCodigo(), item.getCodigoCliente(), true));
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            break;
                        case "TAG_CADCARTAO":
                            try {
                                parameters.add("TAG_CADCARTAO" + ":" +  vendasConfigDAO.tagPagamento(key, getMalaDiretaVO().getEmpresa().getCodigo(), item.getCodigoCliente(), false));
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            break;
                    }
                });

        }
        catch (Exception ex){
            throw new RuntimeException(ex);
        }

        return  parameters;
    }

    public template setEnviaTemplate(Integer idTemplate, List<Message> messageList) throws Exception {
        template templ = new template();
        templ.setTemplateId(idTemplate);
        Timestamp agora = Timestamp.from(Instant.now().atZone( ZoneId.of("America/Sao_Paulo")).toInstant());
        templ.setDataProgramada(agora.toString().replace(" ", "T"));
        List<dadosCliente> dadosClientes = new ArrayList<>();
        try {
            messageList.forEach(item -> {
                dadosCliente cliente = new dadosCliente();
                cliente.setCodigoPessoa(item.getCodigoPessoa());
                cliente.setNome(item.getNome());
                try {
                    cliente.setTemplateParams(personalizarTag(item));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                cliente.setNumeroCelular(item.getNumero());
                dadosClientes.add(cliente);
            });
        }
        catch (Exception ex){
            throw new RuntimeException(ex);
        }
        templ.setDadosCliente(dadosClientes);

       return templ;
    }

    public String sendMessage( String key, Integer empresaId, Integer idTemplate, List<Message> messageList) throws Exception {
            template messages = setEnviaTemplate(idTemplate, messageList);
            RequestHttpService service = new RequestHttpService();
            Map<String, String> params = new HashMap<>();
            params.put("empresaChave", key);
            params.put("empresaId", empresaId.toString());
            params.put("SISTEMA_ZW", "true");
            params.put("Accept-Language", "pt-BR");

            params.put(HttpHeaders.CONTENT_TYPE, "application/json");
            String urlWhatsApp = (URI_API + "/v1/fila/whatsapp");
            String body = new JSONObject(messages).toString();
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlWhatsApp, params, null, body, MetodoHttpEnum.POST);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());

            if (json.has("message")) {
                return  json.getJSONArray("message").getJSONObject(0).getString("message");
            }
            return  "erro ao disparar WhatsApp";
      }

    public List<MsgBuildDTO> consultarTemplateWhatsApp(Integer empresaId, String key, MeioEnvio meioEnvio) throws Exception{
        List<MsgBuildDTO> mensagens = new ArrayList<>();

        try {
            RequestHttpService service = new RequestHttpService();
            String urlWhatsApp = URI_API+ "/v1/template/whatsapp/status/aprovado";
            Map<String, String> params = new HashMap<>();
            params.put("empresaChave", key);
            params.put("empresaId", empresaId.toString());
            params.put("SISTEMA_ZW", "true");
            params.put("Accept-Language", "pt-BR");

            RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlWhatsApp, params, null, null, MetodoHttpEnum.GET);
            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());

            if (json.has("result")) {
                for(int i=0; i<= json.getJSONArray("result").length()-1 ; i++){
                    TemplateWhatsAppDTO dto = JSONMapper.getObject(json.getJSONArray("result").getJSONObject(i), TemplateWhatsAppDTO.class);
                    MsgBuildDTO msg = new MsgBuildDTO();
                    msg.setCodigo(dto.getCodigo());
                    msg.setBody(dto.getTexto());
                    msg.setTipo(MeioEnvio.WHATSAPP.getDescricao());
                    msg.setHtml(dto.getTexto());
                    String titulo = dto.getDescricao();
                    msg.setNome(URLEncoder.encode(titulo == null ? "Sem título" : titulo, "iso-8859-1"));
                    mensagens.add(msg);
                }

            } else {
                return  mensagens;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return mensagens;
        }
        return mensagens;
    }
    public MalaDiretaVO getMalaDiretaVO() {
        return malaDiretaVO;
    }

    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }
}

package servicos.integracao.interfaces.hubspot;

import negocio.comuns.crm.ConversaoLeadVO;
import org.json.JSONObject;

public interface IntegracaoHubspotInterface {
    public String processarNovaLead(String stringLead, int codidoEmpresa)  throws  Exception;

    public String inserirCodeHubspot(String codigo , int empresa , String chave) throws Exception;

    public String atulizarTokenHubspot(String access_token, String refresh_token , int empresa , String chave) throws Exception;

    public String atualizarCodeHubspot(String codigo , int empresa , String chave) throws Exception;

    public boolean temCodigoValido( int empresa) throws Exception;

    public boolean temTokenAuth2Valido( int empresa) throws Exception;

    public boolean existeCodigoInserido( int empresa) throws Exception;

    public String obterAcessToken(final int empresa) throws Exception;

    public String buscaClintIDSecret(final int empresa) throws Exception;

    public String obterRefreshToken(final int empresa) throws Exception;

    public void alterarStatusLead(ConversaoLeadVO conversaoLead, boolean sucesso, Double valorVenda, String motivoDesistencia, String chave);

    public String enviarAtualizacaoLeadJSONData(JSONObject json, String email, String oAuthToken, String empresa, String chave) throws Exception;
}

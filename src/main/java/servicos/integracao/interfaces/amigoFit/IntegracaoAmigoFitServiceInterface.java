package servicos.integracao.interfaces.amigoFit;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.IndicadoVO;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

public interface IntegracaoAmigoFitServiceInterface {

    String cadastrarIndicacao(JSONObject json, String token) throws Exception;

    List<IndicadoVO> consultarIndicacoesAmigoFit(ClienteVO clienteVO) throws Exception;

    String cadastrarCliente(JSONObject json) throws Exception;

    String loginAmigoFit(String usernameEmpresa,String senhaAmigoFitEmpresa) throws Exception;

    String consultarUsuarioLogado(String token) throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException;

    String cadastrarPagamentoCliente(JSONObject json, String token) throws Exception;
}

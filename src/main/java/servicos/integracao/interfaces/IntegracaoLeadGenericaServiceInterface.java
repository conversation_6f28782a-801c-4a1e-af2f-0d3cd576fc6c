package servicos.integracao.interfaces;

import org.json.JSONObject;

public interface IntegracaoLeadGenericaServiceInterface {

    String processarNovaLead(String stringLead, int codidoEmpresa, String chave) throws Exception;

    String processarCodeRDStation(String code, int empresa, String chave)throws Exception;

    String atualizarCodeRDStation(String code, int empresa, String chave)throws Exception;

    String processarTokenRDStation(String access_token, String refresh_token, int empresa, String chave)throws Exception;

    boolean temCodigoValido(int empresa)throws Exception;

    boolean temTokenAuth2Valido(int empresa)throws Exception;

    boolean existeCodigoInserido(int empresa)throws Exception;

    String obterAcessToken(int empresa)throws Exception;

    String buscaIDSecret(int empresa)throws Exception;

    String leadFieldBitrix( String url, String entidade) throws Exception;
    Object listNegociacaoBitrix(String url,  JSONObject fields) throws Exception;


}

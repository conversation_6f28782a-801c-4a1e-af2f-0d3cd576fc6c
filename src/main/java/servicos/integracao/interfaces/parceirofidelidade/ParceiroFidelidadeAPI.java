/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.interfaces.parceirofidelidade;

import negocio.comuns.basico.ParceiroFidelidadePontosVO;
import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import servicos.integracao.impl.parceirofidelidade.to.*;

import javax.faces.model.SelectItem;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ParceiroFidelidadeAPI  {

    RetornoParceiroTO consultarSaldo(String cpf, boolean comDescricao);

    RetornoParceiroTO gerarPontos(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO, MovPagamentoVO movPagamentoVO);

    RetornoParceiroTO realizarCobranca(MovPagamentoVO movPagamentoVO);

    List<ProdutoParceiroFidelidadeVO> consultarProdutos(Integer maxPoints) throws Exception;

    RetornoParceiroTO estornoCobranca(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO, String senha);

    RetornoParceiroTO estornoPontos(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO);

    void registrarTentativa(MovPagamentoVO movPagamento, String cpfTentativa, String senhaTentativa, String mensagem, String operacao);
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.interfaces.rd;

import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.LeadVO;
import org.apache.http.client.methods.HttpPost;
import org.json.JSONObject;
/**
 *
 * <AUTHOR>
 */
public interface IntegracaoRDServiceInterface {
    
    public void processarNovaLeadOLD(String stringLead, int codidoEmpresa)  throws  Exception;

    public void processarNovaLead(String stringLead, int codidoEmpresa)  throws  Exception;

    public String enviarNovaLeadJSONData(JSONObject json) throws Exception;
    
    public String enviarAtualizacaoLeadJSONData(JSONObject json, String chavePrivada, String oAuthToken, String chave) throws Exception;
    
    public void alterarStatusLead(ConversaoLeadVO lead, boolean sucesso, Double valorVenda, String motivoDesistencia, String chave);

    public String enviarJSONData(HttpPost post, J<PERSON><PERSON>Object json) throws Exception;

    public String enviarJSONDataOAuth(HttpPost post, JSONObject json) throws Exception;

    public String sendJSONData(JSONObject json) throws Exception;

    public String sendJSONDataOAuth(JSONObject json) throws Exception;
}

package servicos.integracao.studiocalendar;

import com.google.api.client.util.DateTime;
import negocio.modulos.integracao.studiocalendar.EventoCalendario;

import java.net.URLEncoder;
import negocio.comuns.utilitarias.Uteis;

public class CalendarioService {

    private String data;
    private String parametrosTargetURL;

    /**
     * Metodo para insercao de eventos na agenda do colaborador no Google Agenda
     *
     * @param evento evento
     * @return id do evento inserido na agenda caso tenha sucesso senao retorna null
     */
    public String salvaAtualizaEvento(EventoCalendario evento) throws Exception {
        if (evento == null) {
            return null;
        }
        try {
        //Passagem do token
            data = URLEncoder.encode("token", "UTF-8") + "=" + URLEncoder.encode(evento.getTokenUsuario(), "UTF-8");
            //Popula os parametros corretos para a requisicao POST
            data += "&" + URLEncoder.encode("summary", "UTF-8") + "=" + URLEncoder.encode(evento.getTitulo(), "UTF-8");
            data += "&" + URLEncoder.encode("location", "UTF-8") + "=" + URLEncoder.encode(evento.getNomeAcademia(), "UTF-8");
            data += "&" + URLEncoder.encode("description", "UTF-8") + "=" + URLEncoder.encode(evento.getDescricao(), "UTF-8");
            data += "&" + URLEncoder.encode("start", "UTF-8") + "=" + URLEncoder.encode(new DateTime(evento.getHorarioInicial()).toString(), "UTF-8");
            data += "&" + URLEncoder.encode("end", "UTF-8") + "=" + URLEncoder.encode(new DateTime(evento.getHorarioFinal()).toString(), "UTF-8");
            data += "&" + URLEncoder.encode("status", "UTF-8") + "=" + URLEncoder.encode(evento.getStatusAula(), "UTF-8");

            if (evento.getIdEvento() == null) {
                parametrosTargetURL = "insert.php?tokenApp=studio";
            } else {
                data += "&" + URLEncoder.encode("id", "UTF-8") + "=" + URLEncoder.encode(evento.getIdEvento(), "UTF-8");
                parametrosTargetURL = "update.php?tokenApp=studio";
            }
            //Instancia a classe Comunicador e repassa os parmaetros necessarios para inclusao/alteracao do evento
            Comunicador comunicador = new Comunicador();
            //System.out.println(data);
            return comunicador.executePost(parametrosTargetURL, data);
        } catch (Exception e) {
             Uteis.logar(null, "Erro ao sincronizar agendamento no google agenda:" + e.getMessage());
             throw new Exception("Houve problemas com a sincronização com o Google Agenda, agendamento não foi salvo!");
        }
    }

    /**
     * Metodo para deletar evento na agenda do colaborador no Google Agenda
     *
     * @param evento evento
     * @return true para evento deletado
     */
    public boolean deletaEvento(EventoCalendario evento) throws Exception {
        if (evento == null) {
            return false;
        }
        try {
            //Passagem do token
            data = URLEncoder.encode("token", "UTF-8") + "=" + URLEncoder.encode(evento.getTokenUsuario(), "UTF-8");
            //Popula os parametros corretos para a requisicao POST
            data += "&" + URLEncoder.encode("id", "UTF-8") + "=" + URLEncoder.encode(evento.getIdEvento(), "UTF-8");
            parametrosTargetURL = "delete.php?tokenApp=studio";
            //Instancia a classe Comunicador e repassa os parmaetros necessarios para inclusao/alteracao do evento
            Comunicador comunicador = new Comunicador();
            String status = comunicador.executePost(parametrosTargetURL, data);
            return status.replaceAll("\n", "").equals("1");
        } catch (Exception e) {
             Uteis.logar(null, "Erro ao sincronizar agendamento no google agenda:" + e.getMessage());
             throw new Exception("Houve problemas com a sincronização com o Google Agenda, agendamento não foi excluído!");
        }
    }
}

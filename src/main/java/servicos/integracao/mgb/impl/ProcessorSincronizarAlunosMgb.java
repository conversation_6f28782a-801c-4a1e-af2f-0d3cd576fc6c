package servicos.integracao.mgb.impl;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;

public class ProcessorSincronizarAlunosMgb {


    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String[] args) throws SQLException, IOException {

        logGravar = new StringBuilder();

        try {
            Uteis.debug = true;

            Connection con = new Conexao("**********************************************", "postgres", "pactodb").getConexao();
            nomeBanco = con.getCatalog();

            Integer empresa = 1;

            MgbServiceImpl mgbService = new MgbServiceImpl(con);
            String resultado = mgbService.sincronizarAlunosAtivos(empresa);
            adicionarLog(resultado);

        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }


    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }

}

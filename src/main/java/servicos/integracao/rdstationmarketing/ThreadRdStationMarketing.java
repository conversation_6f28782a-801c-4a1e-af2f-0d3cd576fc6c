package servicos.integracao.rdstationmarketing;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.Date;

public class ThreadRdStationMarketing implements Runnable {

    private Connection con;
    private ClienteVO clienteVO;
    private String nomePlano;
    private Date dataMatricula;
    private AtualizarCamposEnum campos;

    public ThreadRdStationMarketing(Connection con, ClienteVO clienteVO, String nomePlano, Date dataMatricula, AtualizarCamposEnum campos) {
        this.con = con;
        this.clienteVO = clienteVO;
        this.nomePlano = nomePlano;
        this.dataMatricula = dataMatricula;
        this.campos = campos;
    }

    @Override
    public void run() {
        Uteis.logar(null, String.format("##@ iniciando processamento ThreadRdStationMarketing da cliente %s em: %s @##",
                new Object[]{
                        clienteVO.getCodigo(),
                        Uteis.getDataComHora(Calendario.hoje())
                }));
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            IntegracaoRdStationMarketingServiceImpl integracaoRD = new IntegracaoRdStationMarketingServiceImpl(c);
            integracaoRD.enviarDadosAtualizadosDoAluno(clienteVO, nomePlano, dataMatricula, campos);
            integracaoRD = null;
        } catch (Exception ex) {
            Uteis.logar(String.format("Falha sincronização Aluno MAT: %d RDStation Marketing. Erro: %s", clienteVO.getCodigoMatricula(), ex.getMessage()), ThreadRdStationMarketing.class, ex.getMessage());
        }
        Uteis.logar(null, String.format("##@ terminando processamento ThreadRdStationMarketing da cliente %s em: %s @##",
                new Object[]{
                        clienteVO.getCodigo(),
                        Uteis.getDataComHora(Calendario.hoje())
                }));
    }
}

package servicos.integracao.sendy.to;

import java.util.ArrayList;
import java.util.List;

public class ListTO {
    private Long id;
    private String nome;
    private Long idApp;
    private String appKey;
    private String custom_fields;
    private List<SubscriberTO> subscribers;

    public ListTO() {
        this.subscribers = new ArrayList<>();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Long getIdApp() {
        return idApp;
    }

    public void setIdApp(Long idApp) {
        this.idApp = idApp;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public List<SubscriberTO> getSubscribers() {
        return subscribers;
    }

    public void setSubscribers(List<SubscriberTO> subscribers) {
        this.subscribers = subscribers;
    }

    public String getCustom_fields() {
        return custom_fields;
    }

    public void setCustom_fields(String custom_fields) {
        this.custom_fields = custom_fields;
    }
}

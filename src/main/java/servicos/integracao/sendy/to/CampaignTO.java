package servicos.integracao.sendy.to;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import servicos.propriedades.PropsService;

import java.io.Serializable;

public class CampaignTO extends TransferObject {

    private long id;
    private String name;
    private String email;
    private String list;
    private String fromName;
    private String fromEmail;
    private String replyTo;
    private String title;
    private String subject;
    private String plainText;
    private String htmlText;
    private Integer sendCampaign = 0;
    private String brandId;
    private String appKey;
    private ListTO listTO;
    private String quota;
    private Boolean usaEmailPacto = false;
    private String mensagem;

    public CampaignTO() {
        this.listTO = new ListTO();
    }

    @Override
    public Serializable getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getList() {
        return list;
    }

    public void setList(String list) {
        this.list = list;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getFromEmail() {
        return fromEmail;
    }

    /**
     *Manter esse cógigo abaixo comentado até resolvermos validação sendy/AWS
     */
    public void setFromEmail(String fromEmail) {
        //if (Uteis.isEmailGratuito(fromEmail)) {
            //setUsaEmailPacto(true);
            this.fromEmail = PropsService.getEmailIntegracaoSendy();
        //} else {
            //setUsaEmailPacto(false);
            //this.fromEmail = fromEmail;n
        //}
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getPlainText() {
        return plainText;
    }

    public void setPlainText(String plainText) {
        this.plainText = plainText;
    }

    public String getHtmlText() {
        return htmlText;
    }

    public void setHtmlText(String htmlText) {
        this.htmlText = htmlText;
    }

    public Integer getSendCampaign() {
        return sendCampaign;
    }

    public void setSendCampaign(Integer sendCampaign) {
        this.sendCampaign = sendCampaign;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public ListTO getListTO() {
        return listTO;
    }

    public void setListTO(ListTO listTO) {
        this.listTO = listTO;
    }

    public String getQuota() {
        return quota;
    }

    public void setQuota(String quota) {
        this.quota = quota;
    }

    public Boolean getUsaEmailPacto() {
        return usaEmailPacto;
    }

    public void setUsaEmailPacto(Boolean usaEmailPacto) {
        this.usaEmailPacto = usaEmailPacto;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}

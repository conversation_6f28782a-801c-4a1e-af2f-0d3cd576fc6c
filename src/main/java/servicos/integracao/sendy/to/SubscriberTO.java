package servicos.integracao.sendy.to;

public class SubscriberTO {
    private Long id;
    private String nome;
    private String email;
    private String custom_fields;
    private Long idList;
    private int bounced;

    public SubscriberTO() {
    }

    public SubscriberTO(String nome, String email, String custom_fields, int bounced) {
        this.nome = nome;
        this.email = email;
        this.custom_fields = custom_fields;
        this.bounced = bounced;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getIdList() {
        return idList;
    }

    public void setIdList(Long idList) {
        this.idList = idList;
    }

    public String getCustom_fields() {
        return custom_fields;
    }

    public void setCustom_fields(String custom_fields) {
        this.custom_fields = custom_fields;
    }

    public int getBounced() {
        return bounced;
    }

    public void setBounced(int bounced) {
        this.bounced = bounced;
    }

    @Override
    public String toString() {
        return "SubscriberTO{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                ", custom_fields='" + custom_fields + '\'' +
                ", idList=" + idList +
                ", bounced=" + bounced +
                '}';
    }
}

package servicos.integracao.sendy.to;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class BrandTO extends TransferObject {

    private long id;
    private String appName;
    private String fromName;
    private String fromEmail;
    private String replyTo;
    private String appKey;
    private String quota;
    private String mensagem;
    private List<ListTO> listas;
    private Boolean usaEmailPacto = false;

    private String apiKeyMailing;
    private String userSmtp;
    private String passwordSmtp;
    private String hostSmtp;
    private String smtpPort;
    private String smtpTsl;

    private String domain;

    public BrandTO() {
        listas = new ArrayList<>();
    }

    public BrandTO(String appKey, String quota) {
        this.appKey = appKey;
        this.quota = quota;
    }

    @Override
    public Serializable getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getFromName() {
        return fromName;
    }

    public void setFromName(String fromName) {
        this.fromName = fromName;
    }

    public String getFromEmail() {
        return fromEmail;
    }

    public void setFromEmail(String fromEmail) {
        this.fromEmail = fromEmail;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getQuota() {
        if (quota == null || quota.isEmpty())
            quota = "-1";
        return quota;
    }

    public void setQuota(String quota) {
        this.quota = quota;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public List<ListTO> getListas() {
        return listas;
    }

    public void setListas(List<ListTO> listas) {
        this.listas = listas;
    }

    public Boolean getUsaEmailPacto() {
        return usaEmailPacto;
    }

    public void setUsaEmailPacto(Boolean usaEmailPacto) {
        this.usaEmailPacto = usaEmailPacto;
    }

    public String getApiKeyMailing() {
        return apiKeyMailing;
    }

    public void setApiKeyMailing(String apiKeyMailing) {
        this.apiKeyMailing = apiKeyMailing;
    }

    public String getUserSmtp() {
        return userSmtp;
    }

    public void setUserSmtp(String userSmtp) {
        this.userSmtp = userSmtp;
    }

    public String getPasswordSmtp() {
        return passwordSmtp;
    }

    public void setPasswordSmtp(String passwordSmtp) {
        this.passwordSmtp = passwordSmtp;
    }

    public String getHostSmtp() {
        return hostSmtp;
    }

    public void setHostSmtp(String hostSmtp) {
        this.hostSmtp = hostSmtp;
    }

    public String getSmtpPort() {
        return smtpPort;
    }

    public void setSmtpPort(String smtpPort) {
        this.smtpPort = smtpPort;
    }

    public String getSmtpTsl() {
        return smtpTsl;
    }

    public void setSmtpTsl(String smtpTsl) {
        this.smtpTsl = smtpTsl;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }
}

package servicos.integracao.sendy.services;

import static edu.emory.mathcs.backport.java.util.Arrays.asList;

import negocio.comuns.utilitarias.Uteis;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import servicos.integracao.sendy.to.BrandTO;
import servicos.integracao.sendy.to.CampaignTO;
import servicos.integracao.sendy.to.SubscriberTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.ArrayList;
import java.util.List;


public class SendyImpl implements Sendy {

    private String send(String json, String service) {
        try {
                StringEntity entity = new StringEntity(json, "UTF-8");
                HttpPost httpPost = new HttpPost(PropsService.getUrlServicoIntegracaoSendy() + service);
                httpPost.setEntity(entity);
                httpPost.setHeader("Accept", "application/json");
                httpPost.setHeader("Content-Type", "application/json");
                httpPost.setHeader("clientid", PropsService.asString(PropsService.tokenApiWagi));
                if(Uteis.isAmbienteDesenvolvimentoTeste()){
                    httpPost.setHeader("clientid",  PropsService.asString(PropsService.tokenApiWagiSandbox));
                }
                HttpClient client = ExecuteRequestHttpService.createConnector();
                HttpResponse response = client.execute(httpPost);
                int codigo = response.getStatusLine().getStatusCode();
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                return (codigo == 200 ? "Enviado com sucesso!" : "Status " + codigo + ".") + " JSON Retornado: " + responseBody;
        } catch (Exception e) {
            e.printStackTrace();
            return e.toString();
        }
    }

    @Override
    public String subscribe(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "subscribe");
    }

    @Override
    public String unsubscribe(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "unsubscribe");
    }

    @Override
    public String deleteSubscriber(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "deleteSubscriber");
    }

    @Override
    public String createCampaign(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "createCampaign");
    }

    @Override
    public String sendCampaign(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "sendCampaign");
    }

    @Override
    public String createBrand(BrandTO brandTO) {
        return send(brandTO.getJSON(), "createBrand");
    }

    @Override
    public String createLogin(BrandTO brandTO) {
        return send(brandTO.getJSON(), "createLogin");
    }

    @Override
    public String getIdBrandByAppKey(BrandTO brandTO) {
        return send(brandTO.getJSON(), "getIdBrandByAppKey");
    }

    @Override
    public String encriptIdList(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "encriptIdList");
    }

    @Override
    public String updateQuota(BrandTO brandTO) {
        return send(brandTO.getJSON(), "updateQuota");
    }

    @Override
    public String createList(CampaignTO campaignTO) {
        return send(campaignTO.getJSON(), "createList");
    }

    @Override
    public String getListByAppKey(BrandTO brandTO) {
        return send(brandTO.getJSON(), "getListByAppKey");
    }

    @Override
    public String healthCheck() {
        return send("{ \"appKey\" : \"aca438e8c9e947e64db2236bb2f1f7a9\" }", "healthCheck");
    }

    @Override
    public List<String> listIdentities() {
        return new ArrayList<>(asList(send("", "listIdentities")
                .split("Retornado: ")[1]
                .replace("[", "")
                .replace("]", "")
                .replace("\"", "")
                .split(",")
        ));
     }

    @Override
    public void verifyEmailIdentity(String email) {
        send(email, "verifyEmailIdentity");
    }

    public static void main(String[] args) {
        SendyImpl s = new SendyImpl();
        CampaignTO c = new CampaignTO();
        c.setTitle("Título");
        c.setSubject("Assunto");
        c.setHtmlText("texto html");
        c.setReplyTo("<EMAIL>");
        c.setFromEmail("<EMAIL>");
        c.setFromName("Dáurio Filho");
        c.getListTO().setNome("Lista teste");
        c.getListTO().setAppKey("ASDTGRSHSERYHGSD");
        for (int x = 1; x <= 2500; x++) {
            c.getListTO().getSubscribers().add(new SubscriberTO("Aluno " + x, x + "<EMAIL>", "",0));
        }
        //c.setName("ZW");
        //c.setEmail("<EMAIL>");
        //c.setList("WnIdR7LkHq08u1Arb3cAkA");
        //System.out.println(s.subscribe(c));
        //System.out.println(s.unsubscribe(c));
        //System.out.println(s.deleteSubscriber(c));
        //System.out.println(PropsService.getUrlServicoIntegracaoSendy());
        System.out.println(s.sendCampaign(c));
    }
}

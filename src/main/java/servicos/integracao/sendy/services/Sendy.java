package servicos.integracao.sendy.services;

import java.util.List;
import servicos.integracao.sendy.to.BrandTO;
import servicos.integracao.sendy.to.CampaignTO;

public interface Sendy {

    String subscribe(CampaignTO campaignTO);

    String unsubscribe(CampaignTO campaignTO);

    String deleteSubscriber(CampaignTO campaignTO);

    String createCampaign(CampaignTO campaignTO);

    String sendCampaign(CampaignTO campaignTO);

    String createBrand(BrandTO brandTO);

    String createLogin(BrandTO brandTO);

    String getIdBrandByAppKey(BrandTO brandTO);

    String encriptIdList(CampaignTO campaignTO);

    String updateQuota(BrandTO brandTO);

    String createList(CampaignTO campaignTO);

    String getListByAppKey(BrandTO brandTO);

    String healthCheck();

    List<String> listIdentities();

    void verifyEmailIdentity(String email);

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package servicos.integracao;

import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoRegistrarAcesso;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class RegistrarAcessoConsumer {

    public static RetornoRequisicaoRegistrarAcesso registrarAcesso(
            AutorizacaoAcessoGrupoEmpresarialVO autorizacao,
            DirecaoAcessoEnum direcao, SituacaoAcessoEnum situacao, Integer usuario,
            MeioIdentificacaoEnum meioIdentificacao,
            IntegracaoAcessoGrupoEmpresarialVO integracao, boolean redeEmpresa, String nomeCodEmpresaRedeAcessou, boolean porMatricula) throws Exception {


        //meio de Id
        negocio.comuns.acesso.webservice.client.MeioIdentificacaoEnum meioId =
                negocio.comuns.acesso.webservice.client.MeioIdentificacaoEnum.valueOf(meioIdentificacao.toString());
        //direcao
        negocio.comuns.acesso.webservice.client.DirecaoAcessoEnum direcaoAc =
                negocio.comuns.acesso.webservice.client.DirecaoAcessoEnum.valueOf(direcao.toString());
        //situacao acesso
        negocio.comuns.acesso.webservice.client.SituacaoAcessoEnum situacaoAcessoClient = negocio.comuns.acesso.webservice.client.SituacaoAcessoEnum.valueOf(situacao.toString());

        String tipo;
        if (autorizacao.getTipoPessoa().equals(TipoPessoaEnum.ALUNO.getTipo())) {
            tipo = TipoAcessoEnum.TA_ALUNO.getId();
        } else {
            tipo = TipoAcessoEnum.TA_COLABORADOR.getId();
        }

        String baseUrl = String.format("%s/prest/registraracesso", integracao.getUrlZillyonWeb());
        Map<String, String> params = new HashMap<>();
        params.put("key", integracao.getChave());
        params.put("operacao", "registrarAcesso");
        params.put("empresa", integracao.getEmpresaRemota().getCodigo().toString());
        if (redeEmpresa) {
            params.put("nomeCodEmpresaAcessou", nomeCodEmpresaRedeAcessou);
        } else {
            params.put("nomeCodEmpresaAcessou", integracao.getEmpresaLocal().getCodigo() + " - " + integracao.getEmpresaLocal().getNome());
        }
        params.put("codigo", autorizacao.getCodigoGenerico().toString());
        if (porMatricula && !UteisValidacao.emptyNumber(autorizacao.getCodigoMatricula())) {
            params.put("codigoMatricula", autorizacao.getCodigoMatricula().toString());
        }
        params.put("dataAcesso", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
        params.put("direcao", direcaoAc.toString());
        params.put("local", integracao.getLocalAcesso().toString());
        params.put("meioIdentificacao", meioId.toString());
        params.put("situacao", situacaoAcessoClient.toString());
        params.put("terminal", integracao.getTerminal().toString());
        params.put("tipo", tipo);
        params.put("usuario", usuario.toString());

        URIBuilder uriBuilder = new URIBuilder(baseUrl);

        // Adicionar parâmetros à URI
        for (Map.Entry<String, String> entry : params.entrySet()) {
            uriBuilder.setParameter(entry.getKey(), entry.getValue());
        }

        URI uriRegistrarAcesso = uriBuilder.build();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(uriRegistrarAcesso);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                int statusCode = response.getStatusLine().getStatusCode();

                String responseBody = EntityUtils.toString(response.getEntity());
                if (statusCode == 200) {
                    System.out.println(responseBody);
                    JSONObject object = new JSONObject(responseBody);
                    if (object.has("sucesso")) {
                        JSONObject sucessoObj = new JSONObject(object.getString("sucesso"));
                        RetornoRequisicaoRegistrarAcesso retornoZW = new RetornoRequisicaoRegistrarAcesso();
                        retornoZW.setResultado(ResultadoWSEnum.valueOf(sucessoObj.getString("resultado")));
                        retornoZW.setAcessoRegistrado(sucessoObj.optBoolean("acessoRegistrado"));
                        retornoZW.setMsgErro(sucessoObj.optString("msgErro"));
                        retornoZW.setTerminal(sucessoObj.optString("terminal"));
                        return retornoZW;
                    }
                }
            }
        }

        return null;
    }


}

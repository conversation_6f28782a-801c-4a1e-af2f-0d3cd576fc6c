/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package servicos.integracao;

import acesso.webservice.retorno.RetornoRequisicaoValidacaoAcesso;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.MetodoAcessoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.acesso.webservice.client.ValidacaoAcessoWS;
import negocio.comuns.acesso.webservice.client.ValidacaoAcessoWS_Service;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 */
public class ValidacaoAcessoWSConsumer {

    private static final ConcurrentMap<String, ValidacaoAcessoWS> servicos = new ConcurrentHashMap<>();

    private static ValidacaoAcessoWS getInstance(String urlServicoParam) {
        if (UteisValidacao.emptyString(urlServicoParam) || !servicos.containsKey(urlServicoParam)) {
            try {
                URL u = new URL(urlServicoParam + "/ValidacaoAcessoWS?wsdl");
                QName qName = new QName("http://webservice.acesso/", "ValidacaoAcessoWS");
                servicos.put(urlServicoParam, new ValidacaoAcessoWS_Service(u, qName).getValidacaoAcessoWSPort());
                Logger.getLogger(ValidacaoAcessoWSConsumer.class.getName()).log(Level.SEVERE, String.format("Iniciado ValidacaoAcessoWSConsumer para zona %s", urlServicoParam));
            } catch (MalformedURLException ex) {
                Logger.getLogger(ValidacaoAcessoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return servicos.get(urlServicoParam);
    }

    public static void excluirPessoaFotoLocalAcesso(AutorizacaoAcessoGrupoEmpresarialVO autorizacao, Integer localAcesso, String key, String url) throws Exception {
        getInstance(url).excluirPessoaFotoLocalAcesso(key, localAcesso, autorizacao.getCodigoPessoa(), null);
    }


    public static RetornoRequisicaoValidacaoAcesso validarAcesso(AutorizacaoAcessoGrupoEmpresarialVO autorizacao, String token, Integer empresa, Integer localAcesso, String terminal, DirecaoAcessoEnum direcao, Boolean forcarLib, MeioIdentificacaoEnum meioIdentificacao, String key, String url, MetodoAcessoEnum metodo, String terminalOrigem, Boolean acessoOutraEmpresa, String codAcessoOutraEmpresa) throws Exception {
        return validarAcesso(autorizacao, token, empresa, localAcesso, terminal, direcao, forcarLib, meioIdentificacao, key, url, metodo, terminalOrigem, acessoOutraEmpresa, codAcessoOutraEmpresa, "", 0);
    }

    public static RetornoRequisicaoValidacaoAcesso validarAcesso(AutorizacaoAcessoGrupoEmpresarialVO autorizacao, String token, Integer empresa, Integer localAcesso, String terminal, DirecaoAcessoEnum direcao, Boolean forcarLib, MeioIdentificacaoEnum meioIdentificacao, String key, String url, MetodoAcessoEnum metodo, String terminalOrigem, Boolean acessoOutraEmpresa, String codAcessoOutraEmpresa, String keyEmpresaOrigem, Integer codigoEmpresaOrigem) throws Exception {

        negocio.comuns.acesso.webservice.client.MeioIdentificacaoEnum meioId = negocio.comuns.acesso.webservice.client.MeioIdentificacaoEnum.valueOf(meioIdentificacao.toString());
        negocio.comuns.acesso.webservice.client.DirecaoAcessoEnum direcaoAc = negocio.comuns.acesso.webservice.client.DirecaoAcessoEnum.valueOf(direcao.toString());
        negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso retornoWS = null;

        switch (metodo) {
            case PELAMATRICULA:
                retornoWS = getInstance(url).validarAcessoClientePelaMatricula(empresa, forcarLib, key, localAcesso, token, meioId, direcaoAc, terminal);
                break;
            case PELOCODIGOACESSO:
                try {
                    Logger.getLogger(ValidacaoAcessoWSConsumer.class.getName()).log(Level.SEVERE, String.format("Tentar validar ACESSO DE INTEGRAÇÃO na chave %s, empresa %s, codigo %s, local %s, meioId %s, direcao %s, terminal %s", key, empresa, token, localAcesso, meioId, direcaoAc, terminal));
                    retornoWS = getInstance(url).validarAcessoPeloCodigoAcesso(token, empresa, forcarLib, key, localAcesso, meioId, direcaoAc, terminal);
                } catch (Exception e) {
                    Logger.getLogger(ValidacaoAcessoWSConsumer.class.getName()).log(Level.SEVERE, String.format("ERRO validando ACESSO DE INTEGRAÇÃO na chave %s, empresa %s, codigo %s, local %s, meioId %s, direcao %s, terminal %s", key, empresa, token, localAcesso, meioId, direcaoAc, terminal), e);
                }
                break;
            case PELASENHAACESSO:
                retornoWS = getInstance(url).validarAcessoPessoaPorSenha(empresa, forcarLib, key, localAcesso, meioId, token, direcaoAc, terminal);
                break;
            case PELOCODIGOCOLABORADOR:
                retornoWS = getInstance(url).validarAcessoColaboradorPeloCodigo(token, empresa, forcarLib, key, localAcesso, meioId, direcaoAc, terminal);
                break;
        }
        RetornoRequisicaoValidacaoAcesso retornoZW = new RetornoRequisicaoValidacaoAcesso();
        retornoZW.fromClient(retornoWS);
        retornoZW.setTipoCartao(TipoAcessoEnum.AUTORIZADO.getId());
        if (acessoOutraEmpresa && retornoWS != null) {
            retornoZW.setCategoriaCliente(retornoWS.getCategoriaCliente());
            retornoZW.setCodigoCliente(retornoWS.getCodigoCliente());
            retornoZW.setCodigoAcesso(codAcessoOutraEmpresa);
        } else {
            retornoZW.setCategoriaCliente(autorizacao.getCodigoAutorizacao() + "-" + retornoZW.getCategoriaCliente());
            retornoZW.setCodigoCliente(autorizacao.getCodigo().toString());
        }
        if (UteisValidacao.emptyString(retornoZW.getUrlFotoCliente())) {
            retornoZW.setUrlFotoCliente(Uteis.getPaintFotoDaNuvem(autorizacao.getFotoKey()));
        }
        retornoZW.setTerminal(terminalOrigem);

        if (retornoZW.getSituacaoAcesso() != null && retornoZW.getSituacaoAcesso().isLiberado()
            && !podeAcessarOutraEmpresaDaRede(retornoZW, keyEmpresaOrigem, codigoEmpresaOrigem)) {
            retornoZW.setSituacaoAcesso(SituacaoAcessoEnum.RV_BLOQSEMAUTORIZACAO);
            retornoZW.setBloqueadoLiberado(retornoZW.getSituacaoAcesso().getBloqueadoLiberado());
            retornoZW.setMsgColetor(retornoZW.getSituacaoAcesso().getMsgColetor());
            retornoZW.setMsgValidacao(retornoZW.getSituacaoAcesso().getDescricao());
        }

        return retornoZW;
    }


    private static boolean podeAcessarOutraEmpresaDaRede(RetornoRequisicaoValidacaoAcesso retorno, String keyEmpresaOrigem, Integer codigoEmpresaOrigem) {
        try {
            if (UteisValidacao.emptyList(retorno.getChavesEmpresasPermiteAcesso())
                    || UteisValidacao.emptyString(keyEmpresaOrigem)
                    || UteisValidacao.emptyNumber(codigoEmpresaOrigem)) {
                return true;
            }

            boolean podeAcessarOutraEmpresa = false;
            for (String chaveEmpresa : retorno.getChavesEmpresasPermiteAcesso()) {
                String key = chaveEmpresa.split("-")[0];
                Integer codEmpresa = Integer.parseInt(chaveEmpresa.split("-")[1]);
                if (key.equals(keyEmpresaOrigem) && codEmpresa.equals(codigoEmpresaOrigem)) {
                    podeAcessarOutraEmpresa = true;
                    break;
                }
            }
            return podeAcessarOutraEmpresa;
        } catch (Exception ex) {
            Logger.getLogger(ValidacaoAcessoWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
        }
        return true;
    }

    public static byte[] pegarFotoPessoa(Integer localAcesso, Integer pessoa, String key, String url) throws Exception {
        return getInstance(url).pegarFotoPessoa(key, localAcesso, pessoa);
    }

}

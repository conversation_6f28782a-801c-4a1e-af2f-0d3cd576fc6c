package servicos.integracao.sms;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.util.*;

public class SmsController {
    private final static String URI_API = "https://s.smsup.com.br/smsservice/api/sender/%s";
    private final String token;
    private final String key;
    private final TimeZone tz;
    private final CloseableHttpClient httpClient;
    private final ObjectMapper mapper;

    public SmsController(String token, String key, TimeZone tz) throws Exception {
        this.token = token;
        this.key = key;
        this.tz = tz;
        httpClient = ExecuteRequestHttpService.createConnector();
        mapper = new ObjectMapper();
        mapper.setTimeZone(tz);
    }

    public String sendMessage(String codigoExterno, Message message) {
        ArrayList<Message> messages = new ArrayList<Message>();
        messages.add(message);
        return sendMessage(codigoExterno, messages);
    }

    public String sendMessage(String codigoExterno, List<Message> messages) {
        try {
            String retorno = "";
            Map<Integer, List<Message>> lotes = new HashMap<>();
            List<Message> lote = new ArrayList<>();
                boolean loteAdicionado = false;
                int i = 1;
                int qtdLotes = 1;
                for (Message m : messages) {
                    loteAdicionado = false;
                    if(i <= 999) {
                        m.setCodigoExterno(codigoExterno);
                        lote.add(m);
                        i++;
                    }else{
                        m.setCodigoExterno(codigoExterno);
                        lote.add(m);
                        lotes.put(qtdLotes++, lote);
                        lote = new ArrayList<>();
                        i = 1;
                        loteAdicionado = true;
                    }
                }
                if(!loteAdicionado){
                    lotes.put(qtdLotes++, lote);
                }

            for (Integer nrLote: lotes.keySet()) {
                HttpPost httpPost = new HttpPost(String.format(URI_API, "doAsObject"));
                List<NameValuePair> params = new ArrayList<>();
                params.add(new BasicNameValuePair("key", key));
                params.add(new BasicNameValuePair("token", token));
                params.add(new BasicNameValuePair("msgs", mapper.writeValueAsString(lotes.get(nrLote))));
                httpPost.setEntity(new UrlEncodedFormEntity(params));
                CloseableHttpResponse response = httpClient.execute(httpPost);
                HttpEntity entity = response.getEntity();
                String content = EntityUtils.toString(entity);
                if(retorno.isEmpty()){
                    retorno = "Lote "+nrLote+" status: "+mapper.readTree(content).elements().next().asText();
                }else{
                    retorno += "\n"+"Lote "+nrLote+" status: "+mapper.readTree(content).elements().next().asText();
                }
            }
            return retorno;
        } catch (Exception e) {
            //e.printStackTrace(System.out);
            return "ERRO - ".concat(e.getMessage());
        }
    }

    public Balance getSaldoRemanecente() {

        try {
            HttpPost httpPost = new HttpPost(String.format(URI_API, "balance"));

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("key", key));
            params.add(new BasicNameValuePair("token", token));
            httpPost.setEntity(new UrlEncodedFormEntity(params));

            CloseableHttpResponse response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            String content = EntityUtils.toString(entity);

            JsonNode rootNode = mapper.readTree(content);
            return mapper.readValue(rootNode.elements().next().asText(), Balance.class);

        } catch (IOException e) {
            //e.printStackTrace(System.out);
        }
        return null;
    }

    public List<Message> getSent(String codigoExterno) {

        try {
            HttpPost httpPost = new HttpPost(String.format(URI_API, "getSends"));

            List<NameValuePair> params = new ArrayList<NameValuePair>();
            params.add(new BasicNameValuePair("key", key));
            params.add(new BasicNameValuePair("token", token));
            params.add(new BasicNameValuePair("idExterno", codigoExterno));
            httpPost.setEntity(new UrlEncodedFormEntity(params));

            CloseableHttpResponse response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            String content = EntityUtils.toString(entity);

            CollectionType javaType = mapper.getTypeFactory()
                    .constructCollectionType(List.class, Message.class);
            JsonNode rootNode = mapper.readTree(content);
            List<Message> m = mapper.readValue(rootNode.elements().next().toString(), javaType);

            return m;

        } catch (IOException e) {
            //e.printStackTrace(System.out);
        }
        return new ArrayList<Message>();
    }


}

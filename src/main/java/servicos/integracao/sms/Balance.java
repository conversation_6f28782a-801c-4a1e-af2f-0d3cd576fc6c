package servicos.integracao.sms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Balance {
    private Integer balance;
    private Integer acquired;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date validityStart;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date validityEnd;

    public Integer getBalance() {
        return balance;
    }

    public void setBalance(Integer balance) {
        this.balance = balance;
    }

    public Date getValidityStart() {
        return validityStart;
    }

    public void setValidityStart(Date validityStart) {
        this.validityStart = validityStart;
    }

    public Date getValidityEnd() {
        return validityEnd;
    }

    public void setValidityEnd(Date validityEnd) {
        this.validityEnd = validityEnd;
    }

    public Integer getAcquired() {
        return acquired;
    }

    public void setAcquired(Integer acquired) {
        this.acquired = acquired;
    }

    public String getValidade(){
        return validityEnd == null ? "" : Calendario.getData(getValidityEnd(), "dd/MM/yyyy");
    }

    public Integer getPercentual(){
        try {
            Integer usados = acquired - balance;
            return acquired == null || acquired == 0 ? 0 : new Double((usados.doubleValue()/acquired.doubleValue())*100.0).intValue();
        }catch (Exception e){
            return 0;
        }
    }

    public String getTitle(){
        try {
            return "Adquiridos: " + acquired + "<br/>Utilizados: " + (acquired - balance);
        }catch (Exception e){
            return "";
        }
    }
}

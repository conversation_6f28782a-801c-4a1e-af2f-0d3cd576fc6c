package servicos.integracao.impl.hubspot;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.LogGenericoTipoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoEmpresaHubspotVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.ConfiguracaoEmpresaHubspot;
import negocio.facade.jdbc.crm.ConversaoLead;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.Indicacao;
import negocio.facade.jdbc.crm.Indicado;
import negocio.facade.jdbc.crm.Lead;
import negocio.facade.jdbc.crm.Passivo;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.interfaces.hubspot.IntegracaoHubspotInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

public class IntegracaoHubspostLeadServiceImpl extends SuperServico implements IntegracaoHubspotInterface {

    private Indicado indicadoDAO;
    private Indicacao indicacaoDAO;
    private Cliente clienteDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private Telefone teledoneDAO;
    private Lead leadDAO;
    private ConversaoLead conversaoLeadDAO;
    private FecharMeta fecharMetaDAO;
    private Contrato contratoDAO;
    private Passivo passivoDAO;

    public IntegracaoHubspostLeadServiceImpl(Connection con) throws Exception {
        super(con);
    }

    @Override
    public String processarNovaLead(String stringLead, int codidoEmpresa) throws Exception {
        JSONObject newLead = new JSONObject(stringLead);
        String uuid;
        try {
            uuid = newLead.getString("id");
        } catch (Exception e) {
            throw new Exception("uuid não foi identificado");
        }

        EmpresaVO empresa = getEmpresaDAO().consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!empresa.getConfiguracaoEmpresaHubspot().isEmpresausahub()) {
            throw new Exception("Empresa não está configurada para integração com o HubSpot");
        }
        UsuarioVO usuarioVO = null;
        try {
            String emailResponsavel = newLead.getString("responsavel");
            usuarioVO = getUsuarioDAO().consultarUsuarioEmailLeadMeta(codidoEmpresa, emailResponsavel, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception e) {

        }
        if (usuarioVO == null) {
            if (!UteisValidacao.emptyNumber(empresa.getConfiguracaoEmpresaHubspot().getResponsavelPadrao().getCodigo())) {
                usuarioVO = empresa.getConfiguracaoEmpresaHubspot().getResponsavelPadrao();
            } else {
                throw new Exception("Não foi encontrado Responsável para a Lead");
            }
        }
        String celular = "";
        String teleFoneResidencial = "";
        String teleFoneTrabalho = "";
        try {
            teleFoneResidencial = Formatador.formataTelefoneZW(newLead.getString("telefone"));
        } catch (Exception ignored) {
        }
        try {
            celular = Formatador.formataTelefoneZW(newLead.getString("mobilephone"));
            if (!UteisValidacao.emptyString(celular)) {
                if (!celular.matches("\\(\\d{2}\\)(9)\\d{8}")) {
                    teleFoneTrabalho = celular;
                    celular = "";
                }
            }
        } catch (Exception ignored) {
        }

        LeadVO lead = getLeadDAO().consultarPorCodigoRD(uuid, Uteis.NIVELMONTARDADOS_TODOS);
        boolean leadEncontrado = false;
        if (UteisValidacao.emptyNumber(lead.getCodigo())) {
            lead.setTipo(TipoLeadEnum.HUBSPOT);
            lead.setUuid(uuid);
            lead.setEmpresa(empresa);
            lead.setEmail(newLead.getString("email"));
            lead.setDados(stringLead);
            ClienteVO clienteVO = getClienteDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO != null) {
                lead.setCliente(clienteVO);
                getTeledoneDAO().persistirTelefonesClienteLead(clienteVO.getPessoa().getCodigo(), celular, teleFoneResidencial, teleFoneTrabalho);
            } else {
                PassivoVO passivoVO = getPassivoDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
                if (passivoVO != null) {
                    passivoVO.setTelefoneResidencial(teleFoneResidencial);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho(teleFoneTrabalho);
                    getPassivoDAO().alterar(passivoVO);
                } else {
                    passivoVO = new PassivoVO();
                    try {
                        passivoVO.setNome(newLead.getString("nome"));
                    } catch (Exception ignore) {
                    }
                    passivoVO.setEmail(newLead.getString("email"));
                    passivoVO.setEmpresaVO(empresa);
                    passivoVO.setResponsavelCadastro(usuarioVO);
                    passivoVO.setTelefoneResidencial(teleFoneResidencial);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho(teleFoneTrabalho);
                    passivoVO.setLead(true);
                    getPassivoDAO().incluir(passivoVO, true);
                }
                lead.setPassivo(passivoVO);
            }
            getLeadDAO().incluir(lead);
        } else {
            leadEncontrado = true;
            if (!UteisValidacao.emptyNumber(lead.getCliente().getCodigo())) {
                getTeledoneDAO().persistirTelefonesClienteLead(lead.getCliente().getPessoa().getCodigo(), celular, teleFoneResidencial, teleFoneTrabalho);
            } else {
                lead.getPassivo().setTelefoneResidencial(teleFoneResidencial);
                lead.getPassivo().setTelefoneCelular(celular);
                lead.getPassivo().setTelefoneTrabalho(teleFoneTrabalho);
                getPassivoDAO().alterar(lead.getPassivo());
            }
        }
        if (!leadEncontrado) {
            ConversaoLeadVO novaLead = new ConversaoLeadVO();
            novaLead.setProps(stringLead);
            novaLead.setLead(lead);
            novaLead.setIdentificador(newLead.get("evetoid").toString());
            novaLead.setResponsavel(usuarioVO);
            novaLead.setDataCriacao(Uteis.getDate(new JSONObject(stringLead).get("timestemp").toString(), "yyyy-MM-dd'T'HH:mm:ss"));
            getConversaoLeadDAO().incluir(novaLead);
            Date agora = Calendario.hoje();
            TimeZone tz = TimeZone.getTimeZone(empresa.getTimeZoneDefault());
            agora = Calendario.hojeCalendar(tz).getTime();
            Date horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoEmpresaHubspot().getHoraLimite());
            if (horarioLimite.after(agora)) {//se já tiver passado do horário limite, só entra na proxima meta
                getFecharMetaDAO().adicionarLeadMetaDoDia(novaLead, agora);
            }
        }
        return "SUCESSO: Codigo persistido com sucesso.";
    }

    @Override
    public String inserirCodeHubspot(String codigo, int empresa, String chave) throws Exception {
        if (!existeCodigoInserido(empresa)) {
            try (PreparedStatement stm = getCon().prepareStatement("INSERT INTO oauth2hubspot(\n" +
                    "            codigoempresa, codigoauth)\n" +
                    "    VALUES (?, ?);")) {
                int i = 1;
                stm.setInt(i++, empresa);
                stm.setString(i++, codigo);
                stm.execute();
            } catch (Exception e) {
                return "ERRO:" + e;
            }
        } else {
            atualizarCodeHubspot(codigo, empresa, chave);
        }
        return "SUCESSO: Codigo persistido com sucesso.";
    }

    @Override
    public String atulizarTokenHubspot(String access_token, String refresh_token, int empresa, String chave) {

        try (PreparedStatement stm = getCon().prepareStatement("update oauth2hubspot set access_token = ? , refresh_token = ?, atulizado = ? where codigoempresa = ?")) {
            int i = 1;
            stm.setString(i++, access_token);
            stm.setString(i++, refresh_token);
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(i, empresa);
            stm.execute();
        } catch (Exception e) {
            return "ERRO:" + e;
        }
        return "SUCESSO: Token persistido com sucesso.";
    }

    @Override
    public String atualizarCodeHubspot(String codigo, int empresa, String chave) {

        try (PreparedStatement stm = getCon().prepareStatement("update oauth2hubspot set codigoauth = ? where codigoempresa = ?")) {
            int i = 1;
            stm.setString(i++, codigo);
            stm.setInt(i, empresa);
        } catch (Exception e) {
            return "ERRO:" + e;
        }
        return "SUCESSO: codigo persistido com sucesso.";
    }

    @Override
    public boolean temCodigoValido(int empresa) throws Exception {
        String sqlExisteCodigo = "select exists(select codigoempresa from oauth2hubspot where codigoempresa = ? and ((atulizado) + INTERVAL '1 DAY') > current_timestamp) as existe;";
        boolean existe;
        try (PreparedStatement sqlExiste = getCon().prepareStatement(sqlExisteCodigo)) {
            int i = 1;
            sqlExiste.setInt(i, empresa);
            try (ResultSet rs = sqlExiste.executeQuery()) {
                existe = false;
                if (rs.next()) {
                    existe = rs.getBoolean(1);
                }
            }
        }
        return existe;
    }

    @Override
    public boolean temTokenAuth2Valido(int empresa) throws Exception {
        String sqlExisteToken = "select exists(select access_token,atulizado from oauth2hubspot where ((atulizado) + INTERVAL '29 MINUTES') > current_timestamp and access_token is not null and codigoempresa = ?) as existe;";
        boolean existe;
        try (PreparedStatement sqlExiste = getCon().prepareStatement(sqlExisteToken)) {
            int i = 1;
            sqlExiste.setInt(i, empresa);
            try (ResultSet rs = sqlExiste.executeQuery()) {
                existe = false;
                if (rs.next()) {
                    existe = rs.getBoolean(1);
                }
            }
        }
        return existe;
    }

    @Override
    public boolean existeCodigoInserido(int empresa) throws Exception {
        String sqlExisteToken = "select exists(select * from oauth2hubspot where codigoempresa = ?) as existe;";
        boolean existe;
        try (PreparedStatement sqlExiste = getCon().prepareStatement(sqlExisteToken)) {
            int i = 1;
            sqlExiste.setInt(i, empresa);
            try (ResultSet rs = sqlExiste.executeQuery()) {
                existe = false;
                if (rs.next()) {
                    existe = rs.getBoolean(1);
                }
            }
        }
        return existe;
    }

    @Override
    public String obterAcessToken(final int empresa) throws Exception {
        String sql = "select access_token from oauth2hubspot where codigoempresa = " + empresa;
        try (ResultSet rs = criarConsulta(sql, getCon())) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    @Override
    public String buscaClintIDSecret(final int empresa) throws Exception {
        String sql = "select clientid,clientsecret, url_redirect from configuracaoempresahubspot where empresa = " + empresa;
        try (ResultSet rs = criarConsulta(sql, getCon())) {
            if (rs.next()) {
                String id = rs.getString(1);
                String secret = rs.getString(2);
                String url = rs.getString(3);
                return id + "," + secret + "," + url;
            } else {
                return null;
            }
        }
    }

    @Override
    public String obterRefreshToken(int empresa) throws Exception {
        String sql = "select refresh_token from oauth2hubspot where codigoempresa = " + empresa;
        try (ResultSet rs = criarConsulta(sql, getCon())) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    @Override
    public void alterarStatusLead(ConversaoLeadVO conversaoLead, boolean sucesso,
                                  Double valorVenda, String motivoDesistencia,
                                  String chave) {
        ConfiguracaoEmpresaHubspot configDAO;
        try {
            configDAO = new ConfiguracaoEmpresaHubspot(getCon());

            ConfiguracaoEmpresaHubspotVO configVO = configDAO.consultarPorEmpresa(conversaoLead.getLead().getEmpresa().getCodigo());
            String tokenAcess = configVO.getTokenPreparado();

            JSONObject jsonAtualizar = new JSONObject();
            String id = conversaoLead.getLead().getUuid();
            JSONObject payload = new JSONObject();
            if (sucesso) {
                payload.put("lifecyclestage", "customer");
                jsonAtualizar.put("properties", payload);
            } else {
                payload.put("hs_lead_status", "BAD_TIMING");
                payload.put("message", motivoDesistencia);
                jsonAtualizar.put("properties", payload);
            }
            String resposta = atualizarContatoHudspot(id, tokenAcess, jsonAtualizar.toString());
            incluirLog(conversaoLead, chave, resposta, "");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            configDAO = null;
        }
    }

    private String atualizarContatoHudspot(String id, String token, String json) throws Exception {
        System.out.println("inside bearer tokens");
        String url = (PropsService.getPropertyValue(PropsService.urlHubspotAPI) + "/crm/v3/objects/contacts/" + id);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        Map<String, String> params = new HashMap<>();

        RequestHttpService requestHttpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(url, headers, params, json, MetodoHttpEnum.PATCH);
        return respostaHttpDTO.getResponse();
    }

    public void alterarStatusLead_ANT(ConversaoLeadVO conversaoLead, boolean sucesso, Double valorVenda, String motivoDesistencia, String chave) {
        try {
            getEmpresaDAO();
            ConfiguracaoEmpresaHubspotVO cfg = Empresa.montarDadosConfigHubspot(conversaoLead.getLead().getEmpresa().getCodigo(), getCon());
            String refreash = obterRefreshToken(cfg.getEmpresa());
            String Token = "";
            //vai no endpoint e verifica se está atualizado o token, se não estiver pega um novo e persiste no banco
            String respostaToken = verificarTokenAtualizado(cfg.getEmpresa(), refreash, chave, cfg.getUrl_redirect());
            try {
                JSONObject token = new JSONObject(respostaToken);
                Token = token.getString("message");
            } catch (Exception e) {
                Token = respostaToken;
            }
            System.out.println(respostaToken);
            JSONObject jsonAtualizar = new JSONObject();
            String id = conversaoLead.getLead().getUuid();
            JSONObject payload = new JSONObject();
            if (sucesso) {
                payload.put("lifecyclestage", "customer");
                jsonAtualizar.put("properties", payload);
            } else {
                payload.put("hs_lead_status", "BAD_TIMING");
                payload.put("message", motivoDesistencia);
                jsonAtualizar.put("properties", payload);
            }
            String resposta = enviarAtualizacaoLeadJSONData(jsonAtualizar, id, Token, chave, conversaoLead.getLead().getEmpresa().getCodigo().toString());
            incluirLog(conversaoLead, chave, resposta, respostaToken);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
    }

    private String verificarTokenAtualizado(int empresa, String refreshToken, String chave, String url_redirect) throws Exception {
        try {
            Uteis.logar(chave);
            String urlBase = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/";
            String param = "oauthHubspotToken";
            String refreshTokenParam = "?code=" + refreshToken;
            String chaveEmpresa = "&chave=" + chave;
            String empresaCodigo = "&empresa=" + empresa;
            String redirect_url = "&redirect_uri=" + url_redirect;
            String acao = "&action=refresh";
            String url = urlBase + param + refreshTokenParam + chaveEmpresa + empresaCodigo + redirect_url + acao;
            HttpGet get = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5 * 1000).build();
            HttpClient httpClient = ExecuteRequestHttpService.createConnector(requestConfig);
            HttpResponse response = httpClient.execute(get);
            System.out.println(response);
            System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() != 200) {
                System.out.println("failed getting access token");
            }
            StringBuffer result = resultado(response);
            System.out.println("raw result for bearer tokens= " + result);
            return result.toString();

        } catch (Exception ex) {
            System.out.println("Exception while retrieving bearer tokens" + ex);
        }
        return null;
    }

    public StringBuffer resultado(HttpResponse response) throws IOException {
        BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
        StringBuffer result = new StringBuffer();
        String line = "";
        while ((line = rd.readLine()) != null) {
            result.append(line);
        }
        return result;
    }

    @Override
    public String enviarAtualizacaoLeadJSONData(JSONObject json, String email, String oAuthToken, String chave, String empresa) throws Exception {
        Uteis.logar(chave);
        String urlBase = PropsService.getPropertyValue(PropsService.urlZWAPI) + "/prest/lead/" + email + "/" + oAuthToken + "/v2/";
        String param = "updatecontatos";
        String url = urlBase + param;
        HttpPost post = new HttpPost(url);
        StringEntity params = new StringEntity(json.toString());
        post.setEntity(params);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5 * 1000).build();
        HttpClient httpClient = ExecuteRequestHttpService.createConnector(requestConfig);
        HttpResponse response = httpClient.execute(post);
        System.out.println(response);
        System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
        if (response.getStatusLine().getStatusCode() != 200) {
            System.out.println("failed getting access token");
        }
        StringBuffer result = resultado(response);
        System.out.println("raw result for bearer tokens= " + result);
        return result.toString();
    }

    public void incluirLog(ConversaoLeadVO conversaoLeadVO, String key, String resposta, String respostaToken) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(conversaoLeadVO.getCodigo().toString());
        obj.setNomeEntidade("PERSISTIR - HUBSPOT");
        obj.setNomeEntidadeDescricao("Persistir - Hu");
        obj.setOperacao("ATUALIZAÇÃO HUBSPOT");
        obj.setNomeCampo("RESPOSTA:" + resposta + "/E-MAIL DA LEAD: " + conversaoLeadVO.getLead().getEmail());
        obj.setValorCampoAlterado("");
        obj.setDataAlteracao(Calendario.hoje());

        try {
            DaoAuxiliar.retornarAcessoControle(key).getLogDao().incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    public Passivo getPassivoDAO() throws Exception {
        if (passivoDAO == null) {
            passivoDAO = new Passivo(getCon());
        }
        return passivoDAO;
    }

    public Indicado getIndicadoDAO() throws Exception {
        if (indicadoDAO == null) {
            indicadoDAO = new Indicado(getCon());
        }
        return indicadoDAO;
    }

    public void setIndicadoDAO(Indicado indicadoDAO) {
        this.indicadoDAO = indicadoDAO;
    }

    public Empresa getEmpresaDAO() throws Exception {
        if (empresaDAO == null) {
            empresaDAO = new Empresa(getCon());
        }
        return empresaDAO;
    }

    public void setEmpresaDAO(Empresa empresaDAO) {
        this.empresaDAO = empresaDAO;
    }

    public Usuario getUsuarioDAO() throws Exception {
        if (usuarioDAO == null) {
            usuarioDAO = new Usuario(getCon());
        }
        return usuarioDAO;
    }

    public void setUsuarioDAO(Usuario usuarioDAO) {
        this.usuarioDAO = usuarioDAO;
    }

    public Lead getLeadDAO() throws Exception {
        if (leadDAO == null) {
            leadDAO = new Lead(getCon());
        }
        return leadDAO;
    }

    public void setLeadDAO(Lead leadDAO) {
        this.leadDAO = leadDAO;
    }

    public FecharMeta getFecharMetaDAO() throws Exception {
        if (fecharMetaDAO == null) {
            fecharMetaDAO = new FecharMeta(getCon());
        }
        return fecharMetaDAO;
    }

    public void setFecharMetaDAO(FecharMeta fecharMetaDAO) {
        this.fecharMetaDAO = fecharMetaDAO;
    }

    public ConversaoLead getConversaoLeadDAO() throws Exception {
        if (conversaoLeadDAO == null) {
            conversaoLeadDAO = new ConversaoLead(getCon());
        }
        return conversaoLeadDAO;
    }

    public void setConversaoLeadDAO(ConversaoLead conversaoLeadDAO) {
        this.conversaoLeadDAO = conversaoLeadDAO;
    }

    public Cliente getClienteDAO() throws Exception {
        if (clienteDAO == null) {
            clienteDAO = new Cliente(getCon());
        }
        return clienteDAO;
    }

    public void setClienteDAO(Cliente clienteDAO) {
        this.clienteDAO = clienteDAO;
    }

    public Telefone getTeledoneDAO() throws Exception {
        if (teledoneDAO == null) {
            teledoneDAO = new Telefone(getCon());
        }
        return teledoneDAO;
    }

    public void setTeledoneDAO(Telefone teledoneDAO) {
        this.teledoneDAO = teledoneDAO;
    }

    public Indicacao getIndicacaoDAO() throws Exception {
        if (indicacaoDAO == null) {
            indicacaoDAO = new Indicacao(getCon());
        }
        return indicacaoDAO;
    }

    public void setIndicacaoDAO(Indicacao indicacaoDAO) {
        this.indicacaoDAO = indicacaoDAO;
    }

    public Contrato getContratoDAO() throws Exception {
        if (contratoDAO == null) {
            contratoDAO = new Contrato(getCon());
        }
        return contratoDAO;
    }

    public void processarWebhook(Integer empresa, String dados) throws Exception {
        ConfiguracaoEmpresaHubspot configuracaoEmpresaHubspotDAO;
        Log logDAO = new Log(this.getCon());
        JSONObject jsonLog = new JSONObject();
        try {
            configuracaoEmpresaHubspotDAO = new ConfiguracaoEmpresaHubspot(this.getCon());
            jsonLog.put("empresa", empresa);
            jsonLog.put("dados", dados);

            ConfiguracaoEmpresaHubspotVO configVO = configuracaoEmpresaHubspotDAO.consultarPorEmpresa(empresa);
            if (!configVO.isEmpresausahub()) {
                throw new Exception("Integração hubspot não habilitada");
            }

            String tokenAcess = configVO.getTokenPreparado();
            JSONArray arrayDados = new JSONArray(dados);
            JSONObject evento = arrayDados.getJSONObject(0);

            Integer eventId = evento.optInt("eventId");
            Long objectId = evento.optLong("objectId");
            String sourceId = evento.optString("sourceId");
//            Integer appId = evento.optInt("appId");
//            Integer portalId = evento.optInt("portalId");
            String subscriptionType = evento.optString("subscriptionType");
            if (!subscriptionType.equalsIgnoreCase("contact.creation")) {
                throw new Exception("Integração hubspot | Evento não implementado | " + subscriptionType);
            }

            JSONObject contatoJSON = getContatoHubspot(objectId, tokenAcess);
            JSONObject userJson = new JSONObject();
            try {
                String userId = sourceId.split(":")[1];
                if (!UteisValidacao.emptyString(userId)) {
                    userJson = getUserHubspot(userId, tokenAcess);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            JSONObject tratado = new JSONObject();
            tratado.put("dados_api_webhook", dados);
            tratado.put("dados_api_contato", contatoJSON.toString());
            tratado.put("dados_api_user", userJson.toString());

            JSONObject original = contatoJSON.getJSONObject("properties");
            String id = "";
            try {
                id=  contatoJSON.getString("id");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (UteisValidacao.emptyString(id)) {
                id = objectId.toString();
            }
            tratado.put("id", id);
            tratado.put("nome", original.getString("firstname"));
            tratado.put("ultimo_nome", original.optString("lastname"));
            tratado.put("email", original.getString("email"));
            tratado.put("timestemp", original.getString("createdate"));
            tratado.put("responsavel", userJson.optString("email"));
            tratado.put("responsavel_email", userJson.optString("email"));
            tratado.put("responsavel_nome", userJson.optString("email"));
            tratado.put("evetoid", eventId);
            try {
                tratado.put("telefone", original.getString("phone").equals("null") ? original.getString("mobilephone") : original.getString("phone"));
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            jsonLog.put("tratado_json", tratado.toString());

            String retorno = processarNovaLead(tratado.toString(), empresa);
            jsonLog.put("msg", retorno);
            jsonLog.put("sucesso", true);
        } catch (Exception ex) {
            ex.printStackTrace();
            jsonLog.put("msg", ex.getMessage());
            jsonLog.put("sucesso", false);
            throw ex;
        } finally {
            logDAO.incluirLogGenerico(LogGenericoTipoEnum.WEBHOOK_HUBSPOT, null, jsonLog.toString());
        }
    }

    private JSONObject getContatoHubspot(Long idcontato, String token) throws Exception {
        String fields = "?properties=phone,firstname,lastname,email";
        String url = (PropsService.getPropertyValue(PropsService.urlHubspotAPI) + "/crm/v3/objects/contacts/" + idcontato + fields);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        Map<String, String> params = new HashMap<>();

        RequestHttpService requestHttpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(url, headers, params, "", MetodoHttpEnum.GET);
        return new JSONObject(respostaHttpDTO.getResponse());
    }

    private JSONObject getUserHubspot(String idusuario, String token) throws Exception {
        String url = (PropsService.getPropertyValue(PropsService.urlHubspotAPI) + "/settings/v3/users/" + idusuario);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        headers.put("Content-Type", "application/json");
        Map<String, String> params = new HashMap<>();

        RequestHttpService requestHttpService = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(url, headers, params, "", MetodoHttpEnum.GET);
        return new JSONObject(respostaHttpDTO.getResponse());
    }
}

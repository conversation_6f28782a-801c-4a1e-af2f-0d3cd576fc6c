package servicos.integracao.impl.join;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.crm.ConversaoLead;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.Lead;
import negocio.facade.jdbc.crm.Passivo;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.integracao.enumerador.TipoLeadEnum;

import java.sql.Connection;
import java.util.Date;
import java.util.TimeZone;

public class JoinService extends SuperServico {

    private Passivo passivoDAO;
    private Cliente clienteDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private Telefone teledoneDAO;
    private Lead leadDAO;
    private ConversaoLead conversaoLeadDAO;
    private FecharMeta fecharMetaDAO;

    public JoinService(Connection con) throws Exception {
        super(con);
    }

    public Integer processarNovaLead(Integer codigo, String stringLead, int codidoEmpresa) throws Exception {
        JSONObject newLead = new JSONObject(stringLead);
        EmpresaVO empresa = getEmpresaDAO().consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        UsuarioVO responsavel = null;
        boolean criando = UteisValidacao.emptyNumber(codigo);
        if (!empresa.getConfiguracaoIntegracaoJoinVO().isHabilitada()) {
            throw new Exception("Empresa não está configurada para integração com a Join Leads");
        }
        try {
            String emailResponsavel = newLead.getString("user");
            responsavel = getUsuarioDAO().consultarUsuarioEmailLeadMeta(codidoEmpresa, emailResponsavel, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception e) {

        }
        if (responsavel == null) {
            if (!UteisValidacao.emptyNumber(empresa.getConfiguracaoIntegracaoJoinVO().getResponsavelPadrao().getCodigo())) {
                responsavel = empresa.getConfiguracaoIntegracaoJoinVO().getResponsavelPadrao();
            } else {
                responsavel = getUsuarioDAO().getUsuarioRecorrencia();
            }
        }
        String celular = "";
        try {
            celular = Formatador.formataTelefoneZW(newLead.getString("telefone"));
        } catch (Exception ignored) {
        }
        LeadVO lead = UteisValidacao.emptyNumber(codigo) ? new LeadVO() : getLeadDAO().consultarPorPassivo(codigo, Uteis.NIVELMONTARDADOS_TODOS);
        if (UteisValidacao.emptyNumber(lead.getCodigo())) {
            lead.setTipo(TipoLeadEnum.JOIN);
            lead.setEmpresa(empresa);
            lead.setEmail(newLead.getString("email"));
            ClienteVO clienteVO = getClienteDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO != null) {
                lead.setCliente(clienteVO);
                getTeledoneDAO().persistirTelefonesClienteLead(clienteVO.getPessoa().getCodigo(), celular,"","");
            } else {
                PassivoVO passivoVO = UteisValidacao.emptyNumber(codigo) ? null :
                        getPassivoDAO().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (passivoVO != null) {
                    passivoVO.setTelefoneResidencial("");
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho("");
                    getPassivoDAO().alterar(passivoVO);
                    codigo = passivoVO.getCodigo();
                } else {
                    passivoVO = new PassivoVO();
                    passivoVO.setNome(newLead.getString("nome"));
                    passivoVO.setEmail(newLead.getString("email"));
                    passivoVO.setEmpresaVO(empresa);
                    passivoVO.setResponsavelCadastro(responsavel);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setLead(true);
                    passivoVO.setValidarDados(false);
                    getPassivoDAO().incluir(passivoVO,false);
                    codigo = passivoVO.getCodigo();
                }
                lead.setPassivo(passivoVO);
            }
            getLeadDAO().incluir(lead);
        } else {
            if(!UteisValidacao.emptyNumber(lead.getCliente().getCodigo())){
                getTeledoneDAO().persistirTelefonesClienteLead(lead.getCliente().getPessoa().getCodigo(), celular,"","");
            } else {
                if(!UteisValidacao.emptyString(newLead.getString("nome"))){
                    lead.getPassivo().setNome(newLead.getString("nome"));
                }

                if(!UteisValidacao.emptyString(newLead.getString("email"))){
                    lead.getPassivo().setEmail(newLead.getString("email"));
                }

                lead.getPassivo().setTelefoneResidencial("");
                lead.getPassivo().setTelefoneCelular(celular);
                lead.getPassivo().setTelefoneTrabalho("");
                getPassivoDAO().alterar(lead.getPassivo());
            }
        }
        if(criando){
            ConversaoLeadVO novaLead = new ConversaoLeadVO();
            novaLead.setProps(stringLead);
            novaLead.setLead(lead);
            novaLead.setIdentificador("JOIN-" + codigo);
            novaLead.setResponsavel(responsavel);
            novaLead.setDataCriacao(Uteis.getDate(new JSONObject(stringLead).getString("timestamp"), "dd/MM/yyyy HH:mm:ss"));
            getConversaoLeadDAO().incluir(novaLead);
            Date agora = Calendario.hoje();
            TimeZone tz = TimeZone.getTimeZone(empresa.getTimeZoneDefault());
            agora = Calendario.hojeCalendar(tz).getTime();
            Date horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoIntegracaoJoinVO().getHoraLimite());
            if (horarioLimite.after(agora)) {//se já tiver passado do horário limite, só entra na proxima meta
                getFecharMetaDAO().adicionarLeadMetaDoDia(novaLead, agora);
            }
        }
        return codigo;
    }

    public Passivo getPassivoDAO() throws Exception {
        if (passivoDAO == null) {
            passivoDAO = new Passivo(getCon());
        }
        return passivoDAO;
    }

    public void setPassivoDAO(Passivo passivoDAO) {
        this.passivoDAO = passivoDAO;
    }

    public Empresa getEmpresaDAO() throws Exception {
        if (empresaDAO == null) {
            empresaDAO = new Empresa(getCon());
        }
        return empresaDAO;
    }

    public void setEmpresaDAO(Empresa empresaDAO) {
        this.empresaDAO = empresaDAO;
    }

    public Usuario getUsuarioDAO() throws Exception {
        if (usuarioDAO == null) {
            usuarioDAO = new Usuario(getCon());
        }
        return usuarioDAO;
    }

    public void setUsuarioDAO(Usuario usuarioDAO) {
        this.usuarioDAO = usuarioDAO;
    }

    public Lead getLeadDAO() throws Exception {
        if (leadDAO == null) {
            leadDAO = new Lead(getCon());
        }
        return leadDAO;
    }

    public void setLeadDAO(Lead leadDAO) {
        this.leadDAO = leadDAO;
    }

    public FecharMeta getFecharMetaDAO() throws Exception {
        if (fecharMetaDAO == null) {
            fecharMetaDAO = new FecharMeta(getCon());
        }
        return fecharMetaDAO;
    }

    public void setFecharMetaDAO(FecharMeta fecharMetaDAO) {
        this.fecharMetaDAO = fecharMetaDAO;
    }

    public ConversaoLead getConversaoLeadDAO() throws Exception {
        if (conversaoLeadDAO == null) {
            conversaoLeadDAO = new ConversaoLead(getCon());
        }
        return conversaoLeadDAO;
    }

    public void setConversaoLeadDAO(ConversaoLead conversaoLeadDAO) {
        this.conversaoLeadDAO = conversaoLeadDAO;
    }

    public Cliente getClienteDAO() throws Exception {
        if (clienteDAO == null) {
            clienteDAO = new Cliente(getCon());
        }
        return clienteDAO;
    }

    public void setClienteDAO(Cliente clienteDAO) {
        this.clienteDAO = clienteDAO;
    }

    public Telefone getTeledoneDAO() throws Exception {
        if (teledoneDAO == null) {
            teledoneDAO = new Telefone(getCon());
        }
        return teledoneDAO;
    }

}

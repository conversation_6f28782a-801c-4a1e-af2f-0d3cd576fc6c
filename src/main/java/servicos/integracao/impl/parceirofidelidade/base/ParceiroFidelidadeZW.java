package servicos.integracao.impl.parceirofidelidade.base;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ParceiroFidelidadePontosVO;
import negocio.comuns.basico.ParceiroFidelidadeVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeItemVO;
import negocio.comuns.basico.TabelaParceiroFidelidadeVO;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.ParceiroFidelidade;
import negocio.facade.jdbc.basico.ParceiroFidelidadePontos;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.TabelaParceiroFidelidade;
import negocio.facade.jdbc.basico.TabelaParceiroFidelidadeItem;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.basico.ParceiroFidelidadeInterfaceFacade;
import negocio.interfaces.basico.ParceiroFidelidadePontosInterfaceFacade;
import negocio.interfaces.basico.PessoaInterfaceFacade;
import negocio.interfaces.basico.TabelaParceiroFidelidadeInterfaceFacade;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;
import servicos.integracao.impl.parceirofidelidade.dotz.ParceiroFidelidadeAPIDotzImpl;
import servicos.integracao.impl.parceirofidelidade.to.RetornoParceiroTO;
import servicos.integracao.interfaces.parceirofidelidade.ParceiroFidelidadeAPI;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ParceiroFidelidadeZW implements Serializable {

    private ZillyonWebFacade zwFacade;
    private ParceiroFidelidadeInterfaceFacade parceiroDao;
    private EmpresaInterfaceFacade empresaDao;
    private TabelaParceiroFidelidadeInterfaceFacade tabelaParceiroDao;
    private TabelaParceiroFidelidadeItem tabelaParceiroFidelidadeItemDao;
    private ReciboPagamento reciboDao;
    private MovPagamentoInterfaceFacade pagamentoDao;
    private ParceiroFidelidadePontosInterfaceFacade parceiroPontosDao;
    private PessoaInterfaceFacade pessoaDao;

    public ParceiroFidelidadeZW(final Connection con) throws Exception {
        zwFacade = new ZillyonWebFacade(con);
        parceiroDao = new ParceiroFidelidade(con);
        empresaDao = new Empresa(con);
        reciboDao = new ReciboPagamento(con);
        pagamentoDao = new MovPagamento(con);
        parceiroPontosDao = new ParceiroFidelidadePontos(con);
        tabelaParceiroDao = new TabelaParceiroFidelidade(con);
        tabelaParceiroFidelidadeItemDao = new TabelaParceiroFidelidadeItem(con);
        pessoaDao = new Pessoa(con);
    }

    public void processarPontos(final Integer codigoRecibo, boolean robo) throws Exception {

        Integer codEmpresa = 0;
        boolean usarParceiroFidelidade =  false;
        String userNameUsuarioLancamento = "";

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("e.codigo as empresa, \n");
        sql.append("e.UsarParceiroFidelidade, \n");
        sql.append("u.username as responsavellancamento, \n");
        sql.append("rp.codigo as recibopagamento \n");
        sql.append("from recibopagamento rp  \n");
        sql.append("inner join empresa e on e.codigo = rp.empresa \n");
        sql.append("inner join usuario u on rp.responsavellancamento = u.codigo \n");
        sql.append("where rp.codigo = ").append(codigoRecibo);

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), reciboDao.getCon())) {
            if (rs.next()) {
                codEmpresa = rs.getInt("empresa");
                usarParceiroFidelidade = rs.getBoolean("usarParceiroFidelidade");
                userNameUsuarioLancamento = rs.getString("responsavellancamento");
            }
        }

        boolean usuarioRecorrencia = userNameUsuarioLancamento.toUpperCase().equals("RECOR");
        boolean sucesso = true;
        StringBuilder msgException = new StringBuilder();

        if (usarParceiroFidelidade) {
            Uteis.logar(null, "PROCESSAR PARCEIRO FIDELIDADE ReciboPagamento: " + codigoRecibo);
            List<MovPagamentoVO> lista = pagamentoDao.consultarPorCodigoRecibo(codigoRecibo, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (MovPagamentoVO mp : lista) {
                try {
                    Uteis.logar(null, "PROCESSAR PARCEIRO FIDELIDADE MovPagamento: " + mp.getCodigo());

                    if (!mp.isUsarParceiroFidelidade()) {
                        registrarLog(mp, "MovPagamento NÃO ESTÁ VAI USAR PARCEIRO FIDELIDADE MovPagamento : " + mp.getCodigo());
                        continue;
                    }

                    if (mp.isParceiroFidelidadeProcessado()) {
                        registrarLog(mp,"MovPagamento JÁ PROCESSADO MovPagamento : " + mp.getCodigo());
                        continue;
                    }

                    if (!SuperVO.verificaCPF(mp.getCpfParceiroFidelidade())) {
                        throw new Exception("CPF NÃO É VÁLIDO: " + mp.getCpfParceiroFidelidade());
                    }

                    ParceiroFidelidadeVO parceiroFidelidadeVO = parceiroDao.consultarPorEmpresaETipo(codEmpresa, mp.getTipoPontoParceiroFidelidade().getTipoParceiroEnum(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (!UteisValidacao.emptyNumber(parceiroFidelidadeVO.getCodigo())) {

                        ParceiroFidelidadePontosVO parceiroPontos = new ParceiroFidelidadePontosVO(mp, Calendario.hoje());
                        ParceiroFidelidadeAPI pfs = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);
                        RetornoParceiroTO retornoParceiroTO;

                        if (mp.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.RESGATAR)) {
                            System.out.println("realizarCobranca MovPagamento " + mp.getCodigo());
                            retornoParceiroTO = pfs.realizarCobranca(mp);
                        } else if ((mp.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.ACUMULAR) ||
                                mp.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.ACUMULAR_RECORRENCIA)) && robo) {

                            if (UteisValidacao.emptyNumber(mp.getPontosParceiroFidelidade())) {
                                registrarLog(mp,"NÃO EXISTE PONTOS PARA GERAR: " + mp.getCodigo());
                                continue;
                            }

                            System.out.println("gerarPontos MovPagamento " + mp.getCodigo());
                            retornoParceiroTO = pfs.gerarPontos(parceiroPontos, mp);
                        } else {
                            continue;
                        }

                        if (retornoParceiroTO.isSucesso()) {
                            parceiroPontosDao.inserirPontos(parceiroPontos, new JSONObject(retornoParceiroTO).toString());
                            pagamentoDao.marcarParceiroFidelidadeProcessado(true, mp.getCodigo());

                            try {
                                SuperControle superControle = (SuperControle) JSFUtilities.getFromSession(SuperControle.class.getSimpleName());
                                if (superControle == null) {
                                    superControle = new SuperControle();
                                }
                                if (mp.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.RESGATAR)) {
                                    superControle.notificarRecursoEmpresa(RecursoSistema.DOTZ_RESGATE);
                                } else if (mp.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.ACUMULAR)) {
                                    superControle.notificarRecursoEmpresa(RecursoSistema.DOTZ_ACUMULO);
                                } else if (mp.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.ACUMULAR_RECORRENCIA)) {
                                    superControle.notificarRecursoEmpresa(RecursoSistema.DOTZ_ACUMULO_RECORRENCIA);
                                }
                            } catch (Exception ex) {
                                System.out.println("ERRO ao notificarRecursoEmpresa:ParceiroFidelidadeZW: " + ex.getMessage());
                            }

                        } else {
                            throw new Exception(retornoParceiroTO.getMensagem());
                        }
                    } else {
                        throw new Exception("Não foi encontrado Parceiro Fidelidade " + mp.getTipoPontoParceiroFidelidade().getTipoParceiroEnum().getNome() + " para a empresa " + codEmpresa);
                    }

                } catch (Exception ex) {
                    registrarLog(mp, ex.getMessage());
                    sucesso = false;
                    msgException.append(ex.getMessage()).append(" ");
                }

            }
        }
        if (!sucesso && !usuarioRecorrencia) {
            throw new Exception(msgException.toString());
        }
    }

    public void processarEstorno(final Integer codigoRecibo, String senha) throws Exception {
        ReciboPagamentoVO reciboPagamentoVO = reciboDao.consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(reciboPagamentoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        if (empresaVO.isUsarParceiroFidelidade()) {
            List<ParceiroFidelidadePontosVO> lista = parceiroPontosDao.consultarPorReciboPagamento(codigoRecibo, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (ParceiroFidelidadePontosVO parceiroFidelidadePontosVO : lista) {
                ParceiroFidelidadeVO parceiroFidelidadeVO = parceiroDao.consultarPorEmpresaETipo(empresaVO.getCodigo(), parceiroFidelidadePontosVO.getTipoPontoParceiroFidelidade().getTipoParceiroEnum(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(parceiroFidelidadeVO.getCodigo())) {

                    ParceiroFidelidadeAPI pfs = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);

                    RetornoParceiroTO retornoParceiroTO;

                    if (parceiroFidelidadePontosVO.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.RESGATAR)) {
                        System.out.println("estornoCobranca MovPagamento " + parceiroFidelidadePontosVO.getMovPagamento().getCodigo());
                        retornoParceiroTO = pfs.estornoCobranca(parceiroFidelidadePontosVO, senha);
                    } else if (parceiroFidelidadePontosVO.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.ACUMULAR) ||
                            parceiroFidelidadePontosVO.getTipoPontoParceiroFidelidade().equals(TipoPontoParceiroFidelidadeEnum.ACUMULAR_RECORRENCIA)) {
                        System.out.println("estornoPontos MovPagamento " + parceiroFidelidadePontosVO.getMovPagamento().getCodigo());
                        retornoParceiroTO = pfs.estornoPontos(parceiroFidelidadePontosVO);
                    } else {
                        continue;
                    }

                    if (retornoParceiroTO.isSucesso()) {
                        parceiroPontosDao.alterarTipoPonto(TipoPontoParceiroFidelidadeEnum.ESTORNADO, parceiroFidelidadePontosVO.getCodigo());
                    } else {
                        throw new Exception(retornoParceiroTO.getMensagem());
                    }
                } else {
                    throw new Exception("Não foi encontrado Parceiro Fidelidade " + parceiroFidelidadePontosVO.getTipoPontoParceiroFidelidade().getTipoParceiroEnum().getNome() + " para a empresa " + empresaVO.getCodigo());
                }
            }
        }
    }

    public void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO obj, MovParcelaVO movParcelaVO) {
        try {
            obj.setEmpresa(empresaDao.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            if (!obj.getEmpresa().isUsarParceiroFidelidade() || !obj.getFormaPagamento().isGerarPontos()) {
                return;
            }

            ParceiroFidelidadeVO parceiroFidelidadeVO = parceiroDao.consultarPorEmpresaETipo(obj.getEmpresa().getCodigo(), TipoParceiroEnum.DOTZ, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(parceiroFidelidadeVO.getCodigo())) {
                throw new Exception("Não foi encontrada Parceiro Fidelidade!");
            }

            PessoaVO pessoaVO = pessoaDao.consultarPorChavePrimaria(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!pessoaVO.isUtilizaDotz()) {
                throw new Exception("Pessoa: " + pessoaVO.getCodigo() + " | " + pessoaVO.getNome() + " - Não habilitado para utilizar DOTZ!");
            }

            if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje()) && !parceiroFidelidadeVO.isParcelaVencidaGeraPonto()) {
                throw new Exception("Parcela está vencida e sistema está configurado para não gerar pontos para pagamento atrasado! Parcela: " + movParcelaVO.getCodigo() + " | Vencimento: " + movParcelaVO.getDataVencimento_Apresentar());
            }

            TabelaParceiroFidelidadeVO tabelaVO = tabelaParceiroDao.obterDefaultRecorrencia(obj.getEmpresa().getCodigo());
            if (tabelaVO == null) {
                throw new Exception("Não foi encontrada tabela de acumulo Recorrência!");
            }

            TabelaParceiroFidelidadeItemVO itemVO = tabelaParceiroFidelidadeItemDao.consultarPorTabelaParceiroFidelidadeValor(tabelaVO.getCodigo(), obj.getValorTotal(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyNumber(itemVO.getCodigo())) {
                throw new Exception("Tabela Dotz acumular: não foi encontrado um multiplicador equivalente para " + obj.getValorTotal_Apresentar());
            }

            obj.setMultiplicadorParceiroFidelidade(itemVO.getMultiplicador());
            obj.setPontosParceiroFidelidade(itemVO.calcularTotalPontos(obj.getValorTotal()));
            obj.setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.ACUMULAR_RECORRENCIA);
            obj.setUsarParceiroFidelidade(true);
            obj.setTabelaParceiroFidelidadeVO(tabelaVO);
            obj.setCpfParceiroFidelidade(obj.getPessoa().getCfp());

        } catch (Exception ex) {
            registrarLog(obj, ex.getMessage());
            System.out.println("prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        }
    }

    private void registrarLog(MovPagamentoVO movPagamento, String mensagem) {
        try {
            //REGISTRAR LOG
            String sqlLog = "insert into log(dataalteracao, pessoa, nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoanterior, valorcampoalterado, responsavelalteracao, operacao) " +
                    "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

            PreparedStatement pst = zwFacade.getCon().prepareStatement(sqlLog);
            pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(2, movPagamento.getPessoa().getCodigo());
            pst.setString(3, "PARCEIRO-FIDELIDADE-DOTZ");
            pst.setString(4, "PARCEIRO-FIDELIDADE-DOTZ");
            pst.setInt(5, movPagamento.getCodigo());
            if (UteisValidacao.emptyNumber(movPagamento.getCodigo())) {
                pst.setString(6, "PARCEIRO-FIDELIDADE-DOTZ");
            } else {
                pst.setString(6, "MOVPAGAMENTO - " + movPagamento.getCodigo());
            }
            pst.setString(7, "");
            pst.setString(8, mensagem);
            pst.setString(9, movPagamento.getResponsavelPagamento().getNome());
            pst.setString(10, "PROCESSO-PARCEIRO-FIDELIDADE");
            pst.execute();
        } catch (Exception ignored) {
        }
    }

    public void processarPontosDotz(final Date diaRobo) {
        try {

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("distinct(mp.recibopagamento) \n");
            sql.append("from movpagamento mp \n");
            sql.append("inner join empresa e on e.codigo = mp.empresa \n");
            sql.append("where mp.tipopontoparceirofidelidade in (").append(TipoPontoParceiroFidelidadeEnum.ACUMULAR.getCodigo()).append(",").append(TipoPontoParceiroFidelidadeEnum.ACUMULAR_RECORRENCIA.getCodigo()).append(") \n");
            sql.append("and mp.usarparceirofidelidade = true \n");
            sql.append("and e.UsarParceiroFidelidade = true \n");
            sql.append("and mp.parceirofidelidadeprocessado = false \n");
            sql.append("and mp.pontosparceirofidelidade > 0 \n");
            sql.append("and mp.recibopagamento is not null \n");
            sql.append("and mp.datalancamento::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(diaRobo, -5))).append("' \n");
            sql.append("order by 1 \n");

            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), reciboDao.getCon())) {
                while (rs.next()) {
                    Integer codRecibo = rs.getInt("recibopagamento");
                    try {
                        processarPontos(codRecibo, true);
                    } catch (Exception ex) {
                        Uteis.logar(null, "Erro processarPontosDotz Automatico RECIBO: " + codRecibo + " | ERRO: " + ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "Erro processarPontosDotz RECIBO: " + ex.getMessage());
        }

    }


    public static void main(String[] args) {
        String chave = args.length > 0 ? args[0] : "teste";
        try {
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ParceiroFidelidadeZW pfs = new ParceiroFidelidadeZW(c);

            SuperFacadeJDBC.executarConsultaUpdate("update empresa set usarparceirofidelidade  = true", c);

            ParceiroFidelidadeVO pfVO = new ParceiroFidelidadeVO();
            pfVO.setEmpresa(pfs.empresaDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            pfVO.setClientID("YTEQHNHJ1XCYCYP");
            pfVO.setClientSecret("PTSSRD817QMRT7V");
            pfVO.setClientIDRedemption("D91T86SIS0Z9DLI");
            pfVO.setClientSecretRedemption("F9WLYZA39WXF2XO");
            pfVO.setCodigoLoja("Matriz");
            pfVO.setCodigoMaquina("123");
            pfVO.setCodigoOferta("4");
            pfVO.setCodigoResgate("49417");
            pfVO.setTags("voucherPratique");
            pfVO.setValidarCliente(true);
            pfVO.setCpf("");

            TabelaParceiroFidelidadeVO tblParcFidel = new TabelaParceiroFidelidadeVO("Tabela1", pfVO);
            tblParcFidel.setDefaultRecorrencia(true);
            tblParcFidel.addItem(new TabelaParceiroFidelidadeItemVO(tblParcFidel, 0.0, 500.0, 0.25));
            tblParcFidel.addItem(new TabelaParceiroFidelidadeItemVO(tblParcFidel, 501.0, 1000.0, 0.50));
            tblParcFidel.addItem(new TabelaParceiroFidelidadeItemVO(tblParcFidel, 1001.0, 2000.0, 0.75));
            tblParcFidel.addItem(new TabelaParceiroFidelidadeItemVO(tblParcFidel, 2001.0, 3000.0, 1.00));
            pfVO.getItens().add(tblParcFidel);
            pfs.parceiroDao.incluir(pfVO);

        } catch (Exception e) {
            Logger.getLogger(ParceiroFidelidadeZW.class.getName()).log(Level.SEVERE, e.getMessage(), e);
        }
    }
}

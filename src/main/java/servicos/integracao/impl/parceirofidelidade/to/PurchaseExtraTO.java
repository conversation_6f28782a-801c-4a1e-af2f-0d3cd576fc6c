package servicos.integracao.impl.parceirofidelidade.to;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class PurchaseExtraTO extends SuperTO {

    private String sku; //sku do produto no parceiro
    private String ean; //id do produto no parceiro
    private Integer quantity; //quantidade de produtos nesta compra
    private Double unitPrice; //preço unitário do produto
    private Double itemPrice; //preço total (itemPrice X quantidade)
    private String supplierIdentification; //CNPJ do fornecedor
    private String name; //Nome do produto
    private Double discount; //desconto; se houver
    private String offerCode; // id da mecânica extra do parceiro utilizada
    private Integer points; //quantidade de pontos extra que serão creditados

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getEan() {
        return ean;
    }

    public void setEan(String ean) {
        this.ean = ean;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Double getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(Double itemPrice) {
        this.itemPrice = itemPrice;
    }

    public String getSupplierIdentification() {
        return supplierIdentification;
    }

    public void setSupplierIdentification(String supplierIdentification) {
        this.supplierIdentification = supplierIdentification;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public String getOfferCode() {
        return offerCode;
    }

    public void setOfferCode(String offerCode) {
        this.offerCode = offerCode;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }
}

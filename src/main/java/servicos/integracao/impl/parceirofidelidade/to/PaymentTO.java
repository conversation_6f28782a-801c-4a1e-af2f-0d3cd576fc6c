package servicos.integracao.impl.parceirofidelidade.to;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class PaymentTO extends SuperTO {

    private Integer paymentType = 1; //tipo de pagamento
    private String bin; //seis primeiros dígitos do cartão de crédito, se houver
    private Double amount; //valor para esse pagamento

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public String getBin() {
        return bin;
    }

    public void setBin(String bin) {
        this.bin = bin;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }
}

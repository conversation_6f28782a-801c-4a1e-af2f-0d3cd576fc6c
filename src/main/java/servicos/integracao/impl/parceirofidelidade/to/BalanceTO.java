package servicos.integracao.impl.parceirofidelidade.to;

import negocio.comuns.arquitetura.SuperTO;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 *
 * <AUTHOR>
 */
public class BalanceTO extends SuperTO {

    @JsonProperty("balance")
    private Double balance;
    @JsonProperty("code")
    private String code;
    @JsonProperty("message")
    private String message;

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}

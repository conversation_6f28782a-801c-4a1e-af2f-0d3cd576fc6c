package servicos.integracao.impl.parceirofidelidade.to;

import negocio.comuns.arquitetura.SuperTO;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 *
 * <AUTHOR>
 */
public class RetornoParceiroTO extends SuperTO {

    @JsonProperty("sucesso")
    private boolean sucesso = false;
    @JsonProperty("mensagem")
    private String mensagem;
    @JsonProperty("retornoParceiro")
    private String retornoParceiro;
    @JsonProperty("identificador")
    private String identificador;
    @JsonProperty("identificadorExterno")
    private String identificadorExterno;

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMensagem() {
        if (mensagem == null) {
            mensagem = "";
        }
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public void setMensagem(Exception ex) {
        this.mensagem = ex.getMessage();
    }

    public String getRetornoParceiro() {
        if (retornoParceiro == null) {
            retornoParceiro = "";
        }
        return retornoParceiro;
    }

    public void setRetornoParceiro(String retornoParceiro) {
        this.retornoParceiro = retornoParceiro;
    }

    public String getIdentificador() {
        if (identificador == null) {
            identificador = "";
        }
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getIdentificadorExterno() {
        if (identificadorExterno == null) {
            identificadorExterno = "";
        }
        return identificadorExterno;
    }

    public void setIdentificadorExterno(String identificadorExterno) {
        this.identificadorExterno = identificadorExterno;
    }
}

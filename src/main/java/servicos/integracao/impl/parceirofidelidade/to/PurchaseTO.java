package servicos.integracao.impl.parceirofidelidade.to;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class PurchaseTO extends SuperTO {

    private String locator;//localizador da compra
    private String purchaseOrder;//número cupom fiscal(coo)
    private String storeCode = "Matriz";//sigla da loja do parceiro associada ao credito
    private String deviceCode;//codigo do dispositivo (PDV, maquina POS, etc)    
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Date purchaseDate;
    private String shopperIdentification; //CPF do cliente (que será creditado os pontos)
    private String employeeIdentification; //CPF do vendedor
    private Boolean calculatePoints = false; // calcular pontos? (apenas false é aceito na primeira versão)
    private String offerCode; //id da mecânica base do parceiro utilizada
    private Integer points; //quantidade de pontos base que serão creditados    
    private List<PurchaseExtraTO> items = new ArrayList(); //array de itens extra para pontos adicionais
    private Double purchaseValue; //valor total de pagamento
    private List<PaymentTO> payments = new ArrayList();

    public String getLocator() {
        return locator;
    }

    public void setLocator(String locator) {
        this.locator = locator;
    }

    public String getPurchaseOrder() {
        return purchaseOrder;
    }

    public void setPurchaseOrder(String purchaseOrder) {
        this.purchaseOrder = purchaseOrder;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Date getPurchaseDate() {
        return purchaseDate;
    }

    public void setPurchaseDate(Date purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public String getShopperIdentification() {
        return shopperIdentification;
    }

    public void setShopperIdentification(String shopperIdentification) {
        this.shopperIdentification = shopperIdentification;
    }

    public String getEmployeeIdentification() {
        return employeeIdentification;
    }

    public void setEmployeeIdentification(String employeeIdentification) {
        this.employeeIdentification = employeeIdentification;
    }

    public Boolean getCalculatePoints() {
        return calculatePoints;
    }

    public void setCalculatePoints(Boolean calculatePoints) {
        this.calculatePoints = calculatePoints;
    }

    public String getOfferCode() {
        return offerCode;
    }

    public void setOfferCode(String offerCode) {
        this.offerCode = offerCode;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public List<PurchaseExtraTO> getItems() {
        return items;
    }

    public void setItems(List<PurchaseExtraTO> items) {
        this.items = items;
    }

    public Double getPurchaseValue() {
        return purchaseValue;
    }

    public void setPurchaseValue(Double purchaseValue) {
        this.purchaseValue = purchaseValue;
    }

    public List<PaymentTO> getPayments() {
        return payments;
    }

    public void setPayments(List<PaymentTO> payments) {
        this.payments = payments;
    }
}

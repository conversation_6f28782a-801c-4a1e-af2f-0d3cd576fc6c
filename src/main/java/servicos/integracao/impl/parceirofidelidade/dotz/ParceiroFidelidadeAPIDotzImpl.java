package servicos.integracao.impl.parceirofidelidade.dotz;

import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONArray;
import org.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ParceiroFidelidadePontosVO;
import negocio.comuns.basico.ParceiroFidelidadeVO;
import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.basico.enumerador.TipoParceiroEnum;
import negocio.comuns.basico.enumerador.TipoPontoParceiroFidelidadeEnum;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ParceiroFidelidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.codec.binary.Base64;
import servicos.integracao.impl.parceirofidelidade.to.*;
import servicos.integracao.interfaces.parceirofidelidade.ParceiroFidelidadeAPI;
import servicos.util.ExecuteRequestHttpService;

/**
 * <AUTHOR>
 */
public class ParceiroFidelidadeAPIDotzImpl extends SuperEntidade implements ParceiroFidelidadeAPI {

    private static String urlAPI = "http://uat-api.dotzlabs.com"; //URL GET -- HOMOLOGACAO
    private static String urlAPIPost = "https://uat-api.dotzlabs.com"; //URL POST -- HOMOLOGACAO
//    private static String urlAPI = "http://api.dotzlabs.com"; //URL GET -- PRODUÇÃO
//    private static String urlAPIPost = "https://api.dotzlabs.com"; //URL POST -- PRODUÇÃO

    private static final String endpointSaldo = "/pos/v1/members/%s/balance";//consulta saldo
    private static final String endpointPurchase = "/pos/v1/purchases";//envio de compra
    private static final String endpointRedemptions = "/redemptions/v1/redemptions";//envio de resgate
    private static final String endPointToken = "/accounts/v1/connect/token"; //gerar token
    private static final String endProdutos = "/rewards/v1/products"; //gerar token

    private static String urlAPIToken = urlAPIPost + endPointToken;

    private static final String APPLICATION_JSON = "application/json";
    private static final String APPLICATION_FORM = "application/x-www-form-urlencoded";
    private static final String AUTHORIZATION = "Authorization";
    private static final String CONTENT_TYPE = "Content-Type";
    private static final String BASIC = "Basic %s";
    private static final String GRANT_TYPE = "grant_type";
    private static final String CLIENT_CREDENTIALS = "client_credentials";
    private static final String USERNAME = "username";
    private static final String PASSWORD = "password";
    private static final String SCOPE = "scope";
    private static final String POS_API = "pos.api";
    private static final String REWARDS_API = "rewards.api";
    private static final String REDEMPTIONS_API = "redemptions.api";

    private ParceiroFidelidadeVO parceiroFidelidadeVO;
    private final ObjectMapper mapper;

    public ParceiroFidelidadeAPIDotzImpl(ParceiroFidelidadeVO parceiroFidelidadeVO) throws Exception {
        super();
        this.parceiroFidelidadeVO = parceiroFidelidadeVO;
        mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getTimeZone(TimeZoneEnum.Brazil_East.getId()));
        if (getParceiroFidelidadeVO().isAmbienteProducao()) {
            urlAPI = "https://api.dotzlabs.com";
            urlAPIPost = "https://api.dotzlabs.com";
        }
        urlAPIToken = urlAPIPost + endPointToken;
    }

    public ParceiroFidelidadeVO getParceiroFidelidadeVO() {
        if (parceiroFidelidadeVO == null) {
            parceiroFidelidadeVO = new ParceiroFidelidadeVO();
        }
        return parceiroFidelidadeVO;
    }

    public void setParceiroFidelidadeVO(ParceiroFidelidadeVO parceiroFidelidadeVO) {
        this.parceiroFidelidadeVO = parceiroFidelidadeVO;
    }

    @Override
    public RetornoParceiroTO consultarSaldo(String cpf, boolean comDescricao) {
        RetornoParceiroTO retornoParceiroTO = new RetornoParceiroTO();
        try {
            String msg;

            String cpfNumeros = Uteis.removerMascara(cpf);

            if (UteisValidacao.emptyString(cpfNumeros)) {
                throw new Exception("Informe um CPF para consultar o saldo.");
            }

            if (!SuperVO.verificaCPF(cpfNumeros)) {
                throw new Exception("Informe um CPF válido.");
            }

            BalanceTO balanceTO = executeRequest(BalanceTO.class, urlAPI + String.format(endpointSaldo, cpfNumeros), null, null, null, null, ExecuteRequestHttpService.METODO_GET, true, false);

            if (balanceTO.getCode() != null) {
                retornoParceiroTO.setRetornoParceiro(mapper.writeValueAsString(balanceTO));
                throw new Exception("DOTZ: " + balanceTO.getMessage());
            }

            Integer saldo = balanceTO.getBalance().intValue();
            if (comDescricao) {
                msg = "Saldo DOTZ para " + Uteis.aplicarMascara(cpfNumeros, "999.999.999-99") + " é de " + TipoParceiroEnum.DOTZ.getSimbolo() + " " + saldo;
            } else {
                msg = saldo.toString();
            }

            retornoParceiroTO.setSucesso(true);
            retornoParceiroTO.setMensagem(msg);
            retornoParceiroTO.setRetornoParceiro(mapper.writeValueAsString(balanceTO));
        } catch (Exception ex) {
            retornoParceiroTO.setSucesso(false);
            retornoParceiroTO.setMensagem(ex.getMessage());
        }
        return retornoParceiroTO;
    }

    @Override
    public RetornoParceiroTO gerarPontos(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO, MovPagamentoVO movPagamentoVO) {
        RetornoParceiroTO retornoParceiroTO = new RetornoParceiroTO();
        try {

            if (getParceiroFidelidadeVO().isValidarCliente()) {
                RetornoParceiroTO retornoParceiroValidacao = validarClienteParceiro(parceiroFidelidadePontosVO);
                if (!retornoParceiroValidacao.isSucesso()) {
                    parceiroFidelidadePontosVO.setTipoPontoParceiroFidelidade(TipoPontoParceiroFidelidadeEnum.NAO_CLIENTE_DOTZ);
                    retornoParceiroValidacao.setSucesso(true);
                    return retornoParceiroValidacao;
                }
            }

            String identificador = Uteis.getDataAplicandoFormatacao(movPagamentoVO.getDataPagamento(), "yyyyMMdd") + "-" + movPagamentoVO.getCodigo();
            retornoParceiroTO.setIdentificador(identificador);

            PurchaseTO purchaseTO = new PurchaseTO();
            purchaseTO.setLocator(identificador);
            purchaseTO.setPurchaseOrder(movPagamentoVO.getCodigo().toString());
            purchaseTO.setStoreCode(getParceiroFidelidadeVO().getCodigoLoja());
            purchaseTO.setDeviceCode(getParceiroFidelidadeVO().getCodigoMaquina());
            purchaseTO.setOfferCode(getParceiroFidelidadeVO().getCodigoOferta());
            purchaseTO.setPurchaseDate(Calendario.hoje());
            purchaseTO.setShopperIdentification(Uteis.removerMascara(movPagamentoVO.getCpfParceiroFidelidade()));
            purchaseTO.setEmployeeIdentification(Uteis.removerMascara(obterCPFOperador(movPagamentoVO)));
            purchaseTO.setCalculatePoints(false);
            purchaseTO.setPoints(movPagamentoVO.getPontosParceiroFidelidade());
            purchaseTO.setPurchaseValue(Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValorTotal()));
            purchaseTO.setItems(new ArrayList<PurchaseExtraTO>());

            PaymentTO paymentTO = new PaymentTO();
            paymentTO.setAmount(Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValorTotal()));
            paymentTO.setBin("");
            paymentTO.setPaymentType(TipoParceiroEnum.getTipoPagamento(TipoParceiroEnum.DOTZ, movPagamentoVO.getFormaPagamento().getTipoFormaPagamento()));

            purchaseTO.setPayments(new ArrayList<PaymentTO>());
            purchaseTO.getPayments().add(paymentTO);

            try {
                registrarTentativa(movPagamentoVO, movPagamentoVO.getCpfParceiroFidelidade(), movPagamentoVO.getSenhaParceiroFidelidade(),
                        mapper.writeValueAsString(purchaseTO), "ACUMULO");
            } catch (Exception ex){

            }

            PurchasesResponseTO responseTO = executeGeraPontos(PurchasesResponseTO.class, urlAPI + endpointPurchase, purchaseTO);

            if (responseTO.getCode() != null) {
                throw new Exception(responseTO.getMessage());
            }

            retornoParceiroTO.setIdentificadorExterno(responseTO.getTransactionId());
            retornoParceiroTO.setMensagem(responseTO.getDescription());
            retornoParceiroTO.setSucesso(true);
            retornoParceiroTO.setRetornoParceiro(mapper.writeValueAsString(responseTO));
        } catch (Exception ex) {
            retornoParceiroTO.setSucesso(false);
            retornoParceiroTO.setMensagem(ex);
        }
        return retornoParceiroTO;
    }

    private RetornoParceiroTO validarClienteParceiro(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO) {
        RetornoParceiroTO retornoParceiroTO = new RetornoParceiroTO();

        RetornoParceiroTO retornoConsulta = consultarSaldo(parceiroFidelidadePontosVO.getCpf(), false);
        retornoParceiroTO.setSucesso(retornoConsulta.isSucesso());
        retornoParceiroTO.setMensagem(retornoConsulta.getMensagem());
        retornoParceiroTO.setRetornoParceiro(retornoConsulta.getRetornoParceiro());
        return retornoParceiroTO;
    }

    @Override
    public RetornoParceiroTO realizarCobranca(MovPagamentoVO movPagamentoVO) {
        RetornoParceiroTO retornoParceiroTO = new RetornoParceiroTO();
        try {

            if (UteisValidacao.emptyString(movPagamentoVO.getCpfParceiroFidelidade())) {
                throw new Exception("CPF não informado.");
            }

            if (UteisValidacao.emptyString(movPagamentoVO.getSenhaParceiroFidelidade())) {
                throw new Exception("Senha da DOTZ não informada.");
            }

            String identificador = Uteis.getDataAplicandoFormatacao(movPagamentoVO.getDataPagamento(), "yyyyMMdd") + "-" + movPagamentoVO.getCodigo();
            retornoParceiroTO.setIdentificador(identificador);

            JSONObject redemptions = new JSONObject();
            JSONObject order = new JSONObject();
            JSONObject store = new JSONObject();
            JSONArray additionalInformation = new JSONArray();

            order.put("sku", movPagamentoVO.getCodigoExternoProdutoParceiroFidelidade());
            order.put("externalOrderId", identificador);
            order.put("cellphoneNumber", "");
            order.put("loyaltyCardNumber", Uteis.removerMascara(movPagamentoVO.getCpfParceiroFidelidade()));
            order.put("emailAddress", "");

            store.put("locationId", Integer.parseInt(getParceiroFidelidadeVO().getCodigoResgate()));
            store.put("deviceId", movPagamentoVO.getResponsavelPagamento().getCodigo().toString());
            store.put("value", Uteis.arredondarForcando2CasasDecimais(movPagamentoVO.getValorTotal()));
            store.put("pointsValue", movPagamentoVO.getPontosParceiroFidelidade());

            JSONObject info = new JSONObject();
            info.put("key", "EmployeeIdentifier");
            info.put("value", Uteis.removerMascara(obterCPFOperador(movPagamentoVO)));
            additionalInformation.put(info);

            redemptions.put("order", order);
            redemptions.put("store", store);
            redemptions.put("additionalInformation", additionalInformation);

            System.out.println("########## redemptions: " + redemptions.toString());

            registrarTentativa(movPagamentoVO, movPagamentoVO.getCpfParceiroFidelidade(), movPagamentoVO.getSenhaParceiroFidelidade(), redemptions.toString(), "RESGATE");

            RedemptionsResponseTO responseTO = executeResgate(RedemptionsResponseTO.class, urlAPIPost + endpointRedemptions, redemptions.toString(), ExecuteRequestHttpService.METODO_POST,
                    Uteis.removerMascara(movPagamentoVO.getCpfParceiroFidelidade()), movPagamentoVO.getSenhaParceiroFidelidade());

            if (responseTO.getCode() != null) {
                throw new Exception(responseTO.getMessage());
            }

            retornoParceiroTO.setIdentificadorExterno(responseTO.getOrderId());
            retornoParceiroTO.setMensagem(responseTO.getMessage());
            retornoParceiroTO.setSucesso(true);
            retornoParceiroTO.setRetornoParceiro(mapper.writeValueAsString(responseTO));
        } catch (Exception ex) {
            retornoParceiroTO.setSucesso(false);
            retornoParceiroTO.setMensagem(ex);
        }
        return retornoParceiroTO;
    }

    private String obterCPFOperador(MovPagamentoVO movPagamentoVO) throws Exception {
        String cpfUsuario;

        if (!UteisValidacao.emptyString(movPagamentoVO.getResponsavelPagamento().getColaboradorVO().getPessoa().getCfp())) {

            cpfUsuario = movPagamentoVO.getResponsavelPagamento().getColaboradorVO().getPessoa().getCfp();

            if (!SuperVO.verificaCPF(cpfUsuario)) {
                throw new Exception("Usuário logado não tem CPF válido (" + cpfUsuario + ").");
            }

        } else {

            cpfUsuario = getParceiroFidelidadeVO().getCpf();

            if (UteisValidacao.emptyString(cpfUsuario)) {
                throw new Exception("Usuário logado deve ter CPF informado ou informe um CPF nas configurações do parceiro fidelidade " + getParceiroFidelidadeVO().getTipoParceiro().getNome() + ".");
            }

            if (!SuperVO.verificaCPF(cpfUsuario)) {
                throw new Exception("O CPF (" + cpfUsuario + ") informado nas configurações do parceiro fidelidade " + getParceiroFidelidadeVO().getTipoParceiro().getNome() + " não é válido.");
            }

        }

        return cpfUsuario;
    }

    @Override
    public List<ProdutoParceiroFidelidadeVO> consultarProdutos(Integer maxPoints) throws Exception {
        List<ProdutoParceiroFidelidadeVO> retorno = new ArrayList<ProdutoParceiroFidelidadeVO>();

        String retornoRequest = executeRequest(urlAPI + endProdutos + "?_pageSize=100&_page=1&Tags=" + getParceiroFidelidadeVO().getTags() + "&MinPoints=1&MaxPoints=" + maxPoints, null, null, new String[]{CONTENT_TYPE}, new String[]{APPLICATION_JSON}, ExecuteRequestHttpService.METODO_GET, true);

        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(retornoRequest);
        } catch (Exception ex) {
            throw new Exception("Erro ao consultar produtos na DOTZ");
        }

        JSONObject products = jsonObject.getJSONObject("products");
        JSONArray lista = new JSONArray(products.get("items").toString());

        for (int e = 0; e < lista.length(); e++) {
            JSONObject obj = lista.getJSONObject(e);
            ProdutoParceiroFidelidadeVO novo = new ProdutoParceiroFidelidadeVO();
            novo.setDescricao(obj.getString("name").toUpperCase());
            novo.setPontos(obj.getInt("points"));
            novo.setCodigoExterno(obj.getString("sku"));
            retorno.add(novo);
        }
        return retorno;
    }

    @Override
    public RetornoParceiroTO estornoCobranca(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO, String senha) {
        RetornoParceiroTO retornoParceiroTO = new RetornoParceiroTO();
        try {

            String url = urlAPIPost + endpointRedemptions + "/" + parceiroFidelidadePontosVO.getOrderId();

            ReturnDeleteTO returnDeleteTO = executeResgate(ReturnDeleteTO.class, url, "", ExecuteRequestHttpService.METODO_DELETE, parceiroFidelidadePontosVO.getCpf(), senha);

            if (returnDeleteTO.getCode() != null && !returnDeleteTO.getCode().equals("032")) {
                throw new Exception(returnDeleteTO.getMessage());
            }

            retornoParceiroTO.setSucesso(true);
            retornoParceiroTO.setMensagem(returnDeleteTO.getMessage());
            retornoParceiroTO.setRetornoParceiro(mapper.writeValueAsString(returnDeleteTO));
        } catch (Exception ex) {
            retornoParceiroTO.setSucesso(false);
            retornoParceiroTO.setMensagem(ex);
        }
        return retornoParceiroTO;
    }

    @Override
    public RetornoParceiroTO estornoPontos(ParceiroFidelidadePontosVO parceiroFidelidadePontosVO) {
        RetornoParceiroTO retornoParceiroTO = new RetornoParceiroTO();
        try {

            ReturnDeleteTO returnDeleteTO = executeRequest(ReturnDeleteTO.class, urlAPI + endpointPurchase + "/" + parceiroFidelidadePontosVO.getCodigoIdentificador(), null, null, null, null, ExecuteRequestHttpService.METODO_DELETE, true, false);

            if (returnDeleteTO.getCode() != null && !returnDeleteTO.getCode().equals("065")) {
                throw new Exception(returnDeleteTO.getMessage());
            }

            retornoParceiroTO.setSucesso(true);
            retornoParceiroTO.setMensagem(returnDeleteTO.getMessage());
            retornoParceiroTO.setRetornoParceiro(mapper.writeValueAsString(returnDeleteTO));
        } catch (Exception ex) {
            retornoParceiroTO.setSucesso(false);
            retornoParceiroTO.setMensagem(ex);
        }
        return retornoParceiroTO;
    }

    private String generateToken() throws Exception {
        try {
            System.out.println("generateToken - GERAR TOKEN PARA " + getParceiroFidelidadeVO().getTipoParceiro().getNome());
            String auto = parceiroFidelidadeVO.getClientID() + ":" + parceiroFidelidadeVO.getClientSecret();
            String authentication = new String(new Base64().encode(auto.getBytes()));
            Date dataSolicitacao = Calendario.hoje();

            TokenTO tokenTO = executeRequest(TokenTO.class, urlAPIToken,
                    new String[]{GRANT_TYPE, SCOPE}, new String[]{CLIENT_CREDENTIALS, REWARDS_API},
                    new String[]{AUTHORIZATION, CONTENT_TYPE}, new String[]{String.format(BASIC, authentication), APPLICATION_FORM},
                    ExecuteRequestHttpService.METODO_POST, false, false);

            if (tokenTO.getCode() != null) {
                throw new Exception(tokenTO.getMessage());
            } else {
                atualizarToken(dataSolicitacao, tokenTO);
            }

            return tokenTO.getAuthorization();
        } catch (Exception ex) {
            Logger.getLogger(ParceiroFidelidadeAPIDotzImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new Exception("ERRO gerar TOKEN DOTZ: " + ex.getMessage());
        }
    }

    private void atualizarToken(Date dataSolicitacao, TokenTO tokenTO) throws Exception {
        ParceiroFidelidade parceiroFidelidadeDAO = new ParceiroFidelidade(con);
        Date dataExpiracaoToken = Uteis.somarCampoData(dataSolicitacao, Calendar.SECOND, tokenTO.getExpiresIn());
        parceiroFidelidadeDAO.atualizarToken(tokenTO.getAuthorization(), dataExpiracaoToken, getParceiroFidelidadeVO().getCodigo());
    }

    private String generateTokenResgate(String cpf, String password) throws Exception {
        try {
            System.out.println("generateTokenResgate - GERAR TOKEN RESGATE PARA " + getParceiroFidelidadeVO().getTipoParceiro().getNome());
            String auto = parceiroFidelidadeVO.getClientIDRedemption() + ":" + parceiroFidelidadeVO.getClientSecretRedemption();
            String authentication = new String(new Base64().encode(auto.getBytes()));
            String password64 = new String(new Base64().encode(password.getBytes()));

            TokenResgateTO tokenTO = executeRequest(TokenResgateTO.class, urlAPIToken,
                    new String[]{GRANT_TYPE, SCOPE, USERNAME, PASSWORD}, new String[]{PASSWORD, REDEMPTIONS_API, Uteis.removerMascara(cpf), password64},
                    new String[]{AUTHORIZATION, CONTENT_TYPE}, new String[]{String.format(BASIC, authentication), APPLICATION_FORM},
                    ExecuteRequestHttpService.METODO_POST, false, false);

            if (tokenTO.getCode() != null) {
                if (tokenTO.getMessage().contains("Deve ser informado os headers Authorization")) {
                    throw new Exception("Tente novamente, por favor.");
                } else {
                    throw new Exception(tokenTO.getMessage());
                }
            }

            return tokenTO.getAuthorization();
        } catch (Exception ex) {
            Logger.getLogger(ParceiroFidelidadeAPIDotzImpl.class.getName()).log(Level.SEVERE, null, ex);
            throw new Exception("DOTZ: " + ex.getMessage());
        }
    }

    private void obterToken(Map<String, String> headers) throws Exception {
        String token;
        if (getParceiroFidelidadeVO().getDataExpiracaoToken() != null && Calendario.maiorComHora(getParceiroFidelidadeVO().getDataExpiracaoToken(), Calendario.hoje())) {
            token = getParceiroFidelidadeVO().getToken();
        } else {
            token = generateToken();
        }
        headers.put(AUTHORIZATION, token);
    }

    protected <T> T executeRequest(Class<T> clazz, final String url,
                                   final String[] params, final String[] values,
                                   final String[] headerParams, final String[] headerValues,
                                   String metodoHTTP,
                                   boolean usarToken, boolean tokenRedemptions) throws Exception {


        Map<String, String> parametros = null;
        if (params != null && values != null) {
            parametros = new HashMap<String, String>();
            for (int i = 0; i < values.length; i++) {
                parametros.put(params[i], values[i]);
            }
        }

        String parametrosCodificados = "";
        if (parametros != null) {
            Uteis.logar(null, parametros.toString());
            Set<String> s = parametros.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = parametros.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        Map<String, String> headers = new HashMap<String, String>();
        if (headerParams != null && headerValues != null) {
            for (int i = 0; i < headerValues.length; i++) {
                headers.put(headerParams[i], headerValues[i]);
            }
        }

        if (usarToken) {
            obterToken(headers);
        }

        String resposta = ExecuteRequestHttpService.executeHttpRequestMock(url, parametrosCodificados, headers, metodoHTTP, "UTF-8", false);

        System.out.println(resposta);

        return mapper.readValue(resposta, clazz);
    }

    protected <T> T executeRequest(Class<T> clazz,
                                   String url,
                                   String parametrosCodificados,
                                   final String[] headerParams,
                                   final String[] headerValues,
                                   String metodoHTTP,
                                   boolean usarTokenAPI,
                                   boolean usarTokenResgate,
                                   String cpf,
                                   String password) throws Exception {

        Map<String, String> headers = new HashMap<String, String>();
        if (headerParams != null && headerValues != null) {
            for (int i = 0; i < headerValues.length; i++) {
                headers.put(headerParams[i], headerValues[i]);
            }
        }

        if (usarTokenAPI) {
            obterToken(headers);
        }
        if (usarTokenResgate) {
            headers.put(AUTHORIZATION, generateTokenResgate(cpf, password));
        }

        String resposta = ExecuteRequestHttpService.executeHttpRequestMock(url, parametrosCodificados, headers, metodoHTTP, "UTF-8", false);

        System.out.println(resposta);

        return mapper.readValue(resposta, clazz);
    }

    protected <T> T executeGeraPontos(Class<T> clazz, String url, PurchaseTO purchase) throws Exception {
        String info = mapper.writeValueAsString(purchase);
        return executeRequest(clazz, url, info, new String[]{CONTENT_TYPE}, new String[]{APPLICATION_JSON}, ExecuteRequestHttpService.METODO_POST, true, false, "", "");
    }

    protected <T> T executeResgate(Class<T> clazz, String url, String info, String metodo, String cpf, String password) throws Exception {
        return executeRequest(clazz, url, info, new String[]{CONTENT_TYPE}, new String[]{APPLICATION_JSON}, metodo, false, true, cpf, password);
    }

    protected String executeRequest(final String url,
                                    final String[] params, final String[] values,
                                    final String[] headerParams, final String[] headerValues,
                                    String metodoHTTP, boolean usarToken) throws Exception {


        Map<String, String> parametros = null;
        if (params != null && values != null) {
            parametros = new HashMap<String, String>();
            for (int i = 0; i < values.length; i++) {
                parametros.put(params[i], values[i]);
            }
        }

        String parametrosCodificados = "";
        if (parametros != null) {
            Uteis.logar(null, parametros.toString());
            Set<String> s = parametros.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = parametros.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        Map<String, String> headers = new HashMap<String, String>();
        if (headerParams != null && headerValues != null) {
            for (int i = 0; i < headerValues.length; i++) {
                headers.put(headerParams[i], headerValues[i]);
            }
        }

        if (usarToken) {
            obterToken(headers);
        }

        String resposta = ExecuteRequestHttpService.executeHttpRequestMock(url, parametrosCodificados, headers, metodoHTTP, "UTF-8", false);

        System.out.println(resposta);

        return resposta;
    }

    public static void main(String[] args) {
        try {

            Connection con = DriverManager.getConnection("********************************************", "zillyonweb", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);

            ParceiroFidelidadeVO parceiroFidelidadeVO = getFacade().getParceiroFidelidade().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
            ParceiroFidelidadePontosVO parceiroFidelidadePontosVO = getFacade().getParceiroFidelidadePontos().consultarPorChavePrimaria(26, Uteis.NIVELMONTARDADOS_TODOS);
            ParceiroFidelidadePontosVO parceiroFidelidadePontosVOCobranca = getFacade().getParceiroFidelidadePontos().consultarPorChavePrimaria(25, Uteis.NIVELMONTARDADOS_TODOS);

            ParceiroFidelidadeAPI pfs = new ParceiroFidelidadeAPIDotzImpl(parceiroFidelidadeVO);
//            pfs.estornoCobranca(parceiroFidelidadePontosVOCobranca, "147258");
//            pfs.estornoPontos(parceiroFidelidadePontosVO);
//            pfs.estornoCobranca(parceiroFidelidadePontosVO);


//            pfs.cobrancaTeste();
//            System.out.println(pfs.consultarSaldo("", true));
//            System.out.println(pfs.consultarSaldo("", true));
        } catch (Exception ex) {
            Logger.getLogger(ParceiroFidelidadeAPIDotzImpl.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void registrarTentativa(MovPagamentoVO movPagamento, String cpfTentativa, String senhaTentativa, String mensagem, String operacao) {
        try {
            Connection conNova = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            StringBuilder msgGravar = new StringBuilder();
            msgGravar.append("CPF: ").append(cpfTentativa).append(" \n");
            msgGravar.append("Senha: ").append(senhaTentativa).append(" \n\n");
            msgGravar.append("Chamada: \n").append(mensagem).append(" \n\n");

            String infoRegistro = "PARCEIRO-DOTZ-TENTATIVA-" + operacao;

            //REGISTRAR LOG
            String sqlLog = "insert into log(dataalteracao, pessoa, nomeentidade, nomeentidadedescricao, chaveprimaria, "
                    + "nomecampo, valorcampoanterior, valorcampoalterado, responsavelalteracao, operacao) "
                    + "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";

            PreparedStatement pst = conNova.prepareStatement(sqlLog);
            pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            pst.setInt(2, 0);
            pst.setString(3, infoRegistro);
            pst.setString(4, infoRegistro);
            pst.setInt(5, movPagamento.getCodigo());
            pst.setString(6, infoRegistro + " - " + movPagamento.getCodigo());
            pst.setString(7, "");
            pst.setString(8, msgGravar.toString());
            pst.setString(9, "LOG-AUTOMATICO-DOTZ");
            pst.setString(10, infoRegistro);
            pst.execute();
            conNova.close();
        } catch (Exception ex) {
            System.out.println("ERRO GERAR LOG-DOTZ: " + mensagem);
            System.out.println("ERRO GERAR LOG-DOTZ: " + ex.getMessage());
        }
    }
}


package servicos.integracao.impl.conciliadora;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="sEstabelecimento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="sSenha" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="sDadosXML" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "sEstabelecimento",
    "sSenha",
    "sDadosXML"
})
@XmlRootElement(name = "WSEnviaXMLVendaCliente")
public class WSEnviaXMLVendaCliente {

    @XmlElementRef(name = "sEstabelecimento", namespace = "http://tempuri.org/", type = JAXBElement.class)
    protected JAXBElement<String> sEstabelecimento;
    @XmlElementRef(name = "sSenha", namespace = "http://tempuri.org/", type = JAXBElement.class)
    protected JAXBElement<String> sSenha;
    @XmlElementRef(name = "sDadosXML", namespace = "http://tempuri.org/", type = JAXBElement.class)
    protected JAXBElement<String> sDadosXML;

    /**
     * Obtém o valor da propriedade sEstabelecimento.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSEstabelecimento() {
        return sEstabelecimento;
    }

    /**
     * Define o valor da propriedade sEstabelecimento.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSEstabelecimento(JAXBElement<String> value) {
        this.sEstabelecimento = value;
    }

    /**
     * Obtém o valor da propriedade sSenha.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSSenha() {
        return sSenha;
    }

    /**
     * Define o valor da propriedade sSenha.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSSenha(JAXBElement<String> value) {
        this.sSenha = value;
    }

    /**
     * Obtém o valor da propriedade sDadosXML.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSDadosXML() {
        return sDadosXML;
    }

    /**
     * Define o valor da propriedade sDadosXML.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSDadosXML(JAXBElement<String> value) {
        this.sDadosXML = value;
    }

}

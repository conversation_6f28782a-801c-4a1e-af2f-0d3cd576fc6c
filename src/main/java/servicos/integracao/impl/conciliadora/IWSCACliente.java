
package servicos.integracao.impl.conciliadora;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.1
 * 
 */
@WebService(name = "IWSCACliente", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface IWSCACliente {


    /**
     * 
     * @param sEstabelecimento
     * @param sSenha
     * @param sDadosXML
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "WSEnviaXMLVendaCliente", action = "http://tempuri.org/IWSCACliente/WSEnviaXMLVendaCliente")
    @WebResult(name = "WSEnviaXMLVendaClienteResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "WSEnviaXMLVendaCliente", targetNamespace = "http://tempuri.org/", className = "servicos.integracao.impl.conciliadora.WSEnviaXMLVendaCliente")
    @ResponseWrapper(localName = "WSEnviaXMLVendaClienteResponse", targetNamespace = "http://tempuri.org/", className = "servicos.integracao.impl.conciliadora.WSEnviaXMLVendaClienteResponse")
    public String wsEnviaXMLVendaCliente(
        @WebParam(name = "sEstabelecimento", targetNamespace = "http://tempuri.org/")
        String sEstabelecimento,
        @WebParam(name = "sSenha", targetNamespace = "http://tempuri.org/")
        String sSenha,
        @WebParam(name = "sDadosXML", targetNamespace = "http://tempuri.org/")
        String sDadosXML);

    /**
     * 
     * @param parameters
     * @return
     *     returns servicos.integracao.impl.conciliadora.MensagemRetorno
     */
    @WebMethod(operationName = "ArquivoVendaCliente", action = "http://tempuri.org/IWSCACliente/ArquivoVendaCliente")
    @WebResult(name = "MensagemRetorno", targetNamespace = "http://tempuri.org/", partName = "parameters")
    @SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
    public MensagemRetorno arquivoVendaCliente(
        @WebParam(name = "RemoteFileInfo", targetNamespace = "http://tempuri.org/", partName = "parameters")
        RemoteFileInfo parameters);

    /**
     * 
     * @param idEmpresa
     * @param sSenha
     * @param sDadosXML
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "XmlVendaCliente", action = "http://tempuri.org/IWSCACliente/XmlVendaCliente")
    @WebResult(name = "XmlVendaClienteResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "XmlVendaCliente", targetNamespace = "http://tempuri.org/", className = "servicos.integracao.impl.conciliadora.XmlVendaCliente")
    @ResponseWrapper(localName = "XmlVendaClienteResponse", targetNamespace = "http://tempuri.org/", className = "servicos.integracao.impl.conciliadora.XmlVendaClienteResponse")
    public String xmlVendaCliente(
        @WebParam(name = "idEmpresa", targetNamespace = "http://tempuri.org/")
        Integer idEmpresa,
        @WebParam(name = "sSenha", targetNamespace = "http://tempuri.org/")
        String sSenha,
        @WebParam(name = "sDadosXML", targetNamespace = "http://tempuri.org/")
        String sDadosXML);

}

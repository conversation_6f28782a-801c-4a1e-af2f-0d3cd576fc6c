
package servicos.integracao.impl.conciliadora;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "WSCACliente", targetNamespace = "http://tempuri.org/", wsdlLocation = "https://webservice.conciliadora.com.br/WSConciliadoraCliente/WSCACliente.svc?singleWsdl")
public class WSCACliente
    extends Service
{

    private final static URL WSCACLIENTE_WSDL_LOCATION;
    private final static WebServiceException WSCACLIENTE_EXCEPTION;
    private final static QName WSCACLIENTE_QNAME = new QName("http://tempuri.org/", "WSCACliente");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("https://webservice.conciliadora.com.br/WSConciliadoraCliente/WSCACliente.svc?singleWsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WSCACLIENTE_WSDL_LOCATION = url;
        WSCACLIENTE_EXCEPTION = e;
    }

    public WSCACliente() {
        super(__getWsdlLocation(), WSCACLIENTE_QNAME);
    }

    public WSCACliente(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns IWSCACliente
     */
    @WebEndpoint(name = "BasicHttpBinding_IWSCACliente")
    public IWSCACliente getBasicHttpBindingIWSCACliente() {
        return super.getPort(new QName("http://tempuri.org/", "BasicHttpBinding_IWSCACliente"), IWSCACliente.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IWSCACliente
     */
    @WebEndpoint(name = "BasicHttpBinding_IWSCACliente")
    public IWSCACliente getBasicHttpBindingIWSCACliente(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "BasicHttpBinding_IWSCACliente"), IWSCACliente.class, features);
    }

    /**
     * 
     * @return
     *     returns IWSCACliente
     */
    @WebEndpoint(name = "BasicHttpsBinding_IWSCACliente")
    public IWSCACliente getBasicHttpsBindingIWSCACliente() {
        return super.getPort(new QName("http://tempuri.org/", "BasicHttpsBinding_IWSCACliente"), IWSCACliente.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IWSCACliente
     */
    @WebEndpoint(name = "BasicHttpsBinding_IWSCACliente")
    public IWSCACliente getBasicHttpsBindingIWSCACliente(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "BasicHttpsBinding_IWSCACliente"), IWSCACliente.class, features);
    }

    private static URL __getWsdlLocation() {
        if (WSCACLIENTE_EXCEPTION!= null) {
            throw WSCACLIENTE_EXCEPTION;
        }
        return WSCACLIENTE_WSDL_LOCATION;
    }

}

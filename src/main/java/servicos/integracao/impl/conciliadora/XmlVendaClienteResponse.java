
package servicos.integracao.impl.conciliadora;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="XmlVendaClienteResult" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "xmlVendaClienteResult"
})
@XmlRootElement(name = "XmlVendaClienteResponse")
public class XmlVendaClienteResponse {

    @XmlElementRef(name = "XmlVendaClienteResult", namespace = "http://tempuri.org/", type = JAXBElement.class)
    protected JAXBElement<String> xmlVendaClienteResult;

    /**
     * Obtém o valor da propriedade xmlVendaClienteResult.
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getXmlVendaClienteResult() {
        return xmlVendaClienteResult;
    }

    /**
     * Define o valor da propriedade xmlVendaClienteResult.
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setXmlVendaClienteResult(JAXBElement<String> value) {
        this.xmlVendaClienteResult = value;
    }

}


package servicos.integracao.impl.conciliadora;

import java.math.BigDecimal;
import java.math.BigInteger;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.datatype.Duration;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the servicos.integracao.impl.conciliadora package.
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _UnsignedLong_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "unsignedLong");
    private final static QName _UnsignedByte_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "unsignedByte");
    private final static QName _UnsignedShort_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "unsignedShort");
    private final static QName _ErroArquivo_QNAME = new QName("http://schemas.datacontract.org/2004/07/WSCACliente", "ErroArquivo");
    private final static QName _Duration_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "duration");
    private final static QName _ArrayOfErroArquivo_QNAME = new QName("http://schemas.datacontract.org/2004/07/WSCACliente", "ArrayOfErroArquivo");
    private final static QName _Estabelecimento_QNAME = new QName("http://tempuri.org/", "Estabelecimento");
    private final static QName _Long_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "long");
    private final static QName _FileName_QNAME = new QName("http://tempuri.org/", "FileName");
    private final static QName _Float_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "float");
    private final static QName _DateTime_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "dateTime");
    private final static QName _AnyType_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "anyType");
    private final static QName _String_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "string");
    private final static QName _UnsignedInt_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "unsignedInt");
    private final static QName _Char_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "char");
    private final static QName _Short_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "short");
    private final static QName _Guid_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "guid");
    private final static QName _Decimal_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "decimal");
    private final static QName _Length_QNAME = new QName("http://tempuri.org/", "Length");
    private final static QName _Senha_QNAME = new QName("http://tempuri.org/", "Senha");
    private final static QName _Boolean_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "boolean");
    private final static QName _Base64Binary_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "base64Binary");
    private final static QName _Int_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "int");
    private final static QName _AnyURI_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "anyURI");
    private final static QName _Byte_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "byte");
    private final static QName _Double_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "double");
    private final static QName _QName_QNAME = new QName("http://schemas.microsoft.com/2003/10/Serialization/", "QName");
    private final static QName _ErroArquivoErro_QNAME = new QName("http://schemas.datacontract.org/2004/07/WSCACliente", "Erro");
    private final static QName _WSEnviaXMLVendaClienteResponseWSEnviaXMLVendaClienteResult_QNAME = new QName("http://tempuri.org/", "WSEnviaXMLVendaClienteResult");
    private final static QName _WSEnviaXMLVendaClienteSEstabelecimento_QNAME = new QName("http://tempuri.org/", "sEstabelecimento");
    private final static QName _WSEnviaXMLVendaClienteSDadosXML_QNAME = new QName("http://tempuri.org/", "sDadosXML");
    private final static QName _WSEnviaXMLVendaClienteSSenha_QNAME = new QName("http://tempuri.org/", "sSenha");
    private final static QName _XmlVendaClienteResponseXmlVendaClienteResult_QNAME = new QName("http://tempuri.org/", "XmlVendaClienteResult");
    private final static QName _MensagemMsg_QNAME = new QName("http://tempuri.org/", "Msg");
    private final static QName _MensagemRetornoXMLErros_QNAME = new QName("http://tempuri.org/", "XMLErros");
    private final static QName _MensagemRetornoMensagem_QNAME = new QName("http://tempuri.org/", "Mensagem");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: servicos.integracao.impl.conciliadora
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link WSEnviaXMLVendaCliente }
     * 
     */
    public WSEnviaXMLVendaCliente createWSEnviaXMLVendaCliente() {
        return new WSEnviaXMLVendaCliente();
    }

    /**
     * Create an instance of {@link MensagemRetorno }
     * 
     */
    public MensagemRetorno createMensagemRetorno() {
        return new MensagemRetorno();
    }

    /**
     * Create an instance of {@link ArrayOfErroArquivo }
     * 
     */
    public ArrayOfErroArquivo createArrayOfErroArquivo() {
        return new ArrayOfErroArquivo();
    }

    /**
     * Create an instance of {@link WSEnviaXMLVendaClienteResponse }
     * 
     */
    public WSEnviaXMLVendaClienteResponse createWSEnviaXMLVendaClienteResponse() {
        return new WSEnviaXMLVendaClienteResponse();
    }

    /**
     * Create an instance of {@link Mensagem }
     * 
     */
    public Mensagem createMensagem() {
        return new Mensagem();
    }

    /**
     * Create an instance of {@link RemoteFileInfo }
     * 
     */
    public RemoteFileInfo createRemoteFileInfo() {
        return new RemoteFileInfo();
    }

    /**
     * Create an instance of {@link XmlVendaCliente }
     * 
     */
    public XmlVendaCliente createXmlVendaCliente() {
        return new XmlVendaCliente();
    }

    /**
     * Create an instance of {@link XmlVendaClienteResponse }
     * 
     */
    public XmlVendaClienteResponse createXmlVendaClienteResponse() {
        return new XmlVendaClienteResponse();
    }

    /**
     * Create an instance of {@link ErroArquivo }
     * 
     */
    public ErroArquivo createErroArquivo() {
        return new ErroArquivo();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BigInteger }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "unsignedLong")
    public JAXBElement<BigInteger> createUnsignedLong(BigInteger value) {
        return new JAXBElement<BigInteger>(_UnsignedLong_QNAME, BigInteger.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Short }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "unsignedByte")
    public JAXBElement<Short> createUnsignedByte(Short value) {
        return new JAXBElement<Short>(_UnsignedByte_QNAME, Short.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "unsignedShort")
    public JAXBElement<Integer> createUnsignedShort(Integer value) {
        return new JAXBElement<Integer>(_UnsignedShort_QNAME, Integer.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ErroArquivo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/WSCACliente", name = "ErroArquivo")
    public JAXBElement<ErroArquivo> createErroArquivo(ErroArquivo value) {
        return new JAXBElement<ErroArquivo>(_ErroArquivo_QNAME, ErroArquivo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Duration }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "duration")
    public JAXBElement<Duration> createDuration(Duration value) {
        return new JAXBElement<Duration>(_Duration_QNAME, Duration.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfErroArquivo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/WSCACliente", name = "ArrayOfErroArquivo")
    public JAXBElement<ArrayOfErroArquivo> createArrayOfErroArquivo(ArrayOfErroArquivo value) {
        return new JAXBElement<ArrayOfErroArquivo>(_ArrayOfErroArquivo_QNAME, ArrayOfErroArquivo.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "Estabelecimento")
    public JAXBElement<String> createEstabelecimento(String value) {
        return new JAXBElement<String>(_Estabelecimento_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Long }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "long")
    public JAXBElement<Long> createLong(Long value) {
        return new JAXBElement<Long>(_Long_QNAME, Long.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "FileName")
    public JAXBElement<String> createFileName(String value) {
        return new JAXBElement<String>(_FileName_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Float }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "float")
    public JAXBElement<Float> createFloat(Float value) {
        return new JAXBElement<Float>(_Float_QNAME, Float.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link XMLGregorianCalendar }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "dateTime")
    public JAXBElement<XMLGregorianCalendar> createDateTime(XMLGregorianCalendar value) {
        return new JAXBElement<XMLGregorianCalendar>(_DateTime_QNAME, XMLGregorianCalendar.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Object }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "anyType")
    public JAXBElement<Object> createAnyType(Object value) {
        return new JAXBElement<Object>(_AnyType_QNAME, Object.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "string")
    public JAXBElement<String> createString(String value) {
        return new JAXBElement<String>(_String_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Long }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "unsignedInt")
    public JAXBElement<Long> createUnsignedInt(Long value) {
        return new JAXBElement<Long>(_UnsignedInt_QNAME, Long.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "char")
    public JAXBElement<Integer> createChar(Integer value) {
        return new JAXBElement<Integer>(_Char_QNAME, Integer.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Short }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "short")
    public JAXBElement<Short> createShort(Short value) {
        return new JAXBElement<Short>(_Short_QNAME, Short.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "guid")
    public JAXBElement<String> createGuid(String value) {
        return new JAXBElement<String>(_Guid_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link BigDecimal }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "decimal")
    public JAXBElement<BigDecimal> createDecimal(BigDecimal value) {
        return new JAXBElement<BigDecimal>(_Decimal_QNAME, BigDecimal.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Long }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "Length")
    public JAXBElement<Long> createLength(Long value) {
        return new JAXBElement<Long>(_Length_QNAME, Long.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "Senha")
    public JAXBElement<String> createSenha(String value) {
        return new JAXBElement<String>(_Senha_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Boolean }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "boolean")
    public JAXBElement<Boolean> createBoolean(Boolean value) {
        return new JAXBElement<Boolean>(_Boolean_QNAME, Boolean.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link byte[]}{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "base64Binary")
    public JAXBElement<byte[]> createBase64Binary(byte[] value) {
        return new JAXBElement<byte[]>(_Base64Binary_QNAME, byte[].class, null, ((byte[]) value));
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Integer }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "int")
    public JAXBElement<Integer> createInt(Integer value) {
        return new JAXBElement<Integer>(_Int_QNAME, Integer.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "anyURI")
    public JAXBElement<String> createAnyURI(String value) {
        return new JAXBElement<String>(_AnyURI_QNAME, String.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Byte }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "byte")
    public JAXBElement<Byte> createByte(Byte value) {
        return new JAXBElement<Byte>(_Byte_QNAME, Byte.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Double }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "double")
    public JAXBElement<Double> createDouble(Double value) {
        return new JAXBElement<Double>(_Double_QNAME, Double.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link QName }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.microsoft.com/2003/10/Serialization/", name = "QName")
    public JAXBElement<QName> createQName(QName value) {
        return new JAXBElement<QName>(_QName_QNAME, QName.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://schemas.datacontract.org/2004/07/WSCACliente", name = "Erro", scope = ErroArquivo.class)
    public JAXBElement<String> createErroArquivoErro(String value) {
        return new JAXBElement<String>(_ErroArquivoErro_QNAME, String.class, ErroArquivo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "WSEnviaXMLVendaClienteResult", scope = WSEnviaXMLVendaClienteResponse.class)
    public JAXBElement<String> createWSEnviaXMLVendaClienteResponseWSEnviaXMLVendaClienteResult(String value) {
        return new JAXBElement<String>(_WSEnviaXMLVendaClienteResponseWSEnviaXMLVendaClienteResult_QNAME, String.class, WSEnviaXMLVendaClienteResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "sEstabelecimento", scope = WSEnviaXMLVendaCliente.class)
    public JAXBElement<String> createWSEnviaXMLVendaClienteSEstabelecimento(String value) {
        return new JAXBElement<String>(_WSEnviaXMLVendaClienteSEstabelecimento_QNAME, String.class, WSEnviaXMLVendaCliente.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "sDadosXML", scope = WSEnviaXMLVendaCliente.class)
    public JAXBElement<String> createWSEnviaXMLVendaClienteSDadosXML(String value) {
        return new JAXBElement<String>(_WSEnviaXMLVendaClienteSDadosXML_QNAME, String.class, WSEnviaXMLVendaCliente.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "sSenha", scope = WSEnviaXMLVendaCliente.class)
    public JAXBElement<String> createWSEnviaXMLVendaClienteSSenha(String value) {
        return new JAXBElement<String>(_WSEnviaXMLVendaClienteSSenha_QNAME, String.class, WSEnviaXMLVendaCliente.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "sDadosXML", scope = XmlVendaCliente.class)
    public JAXBElement<String> createXmlVendaClienteSDadosXML(String value) {
        return new JAXBElement<String>(_WSEnviaXMLVendaClienteSDadosXML_QNAME, String.class, XmlVendaCliente.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "sSenha", scope = XmlVendaCliente.class)
    public JAXBElement<String> createXmlVendaClienteSSenha(String value) {
        return new JAXBElement<String>(_WSEnviaXMLVendaClienteSSenha_QNAME, String.class, XmlVendaCliente.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "XmlVendaClienteResult", scope = XmlVendaClienteResponse.class)
    public JAXBElement<String> createXmlVendaClienteResponseXmlVendaClienteResult(String value) {
        return new JAXBElement<String>(_XmlVendaClienteResponseXmlVendaClienteResult_QNAME, String.class, XmlVendaClienteResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "Msg", scope = Mensagem.class)
    public JAXBElement<String> createMensagemMsg(String value) {
        return new JAXBElement<String>(_MensagemMsg_QNAME, String.class, Mensagem.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ArrayOfErroArquivo }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "XMLErros", scope = MensagemRetorno.class)
    public JAXBElement<ArrayOfErroArquivo> createMensagemRetornoXMLErros(ArrayOfErroArquivo value) {
        return new JAXBElement<ArrayOfErroArquivo>(_MensagemRetornoXMLErros_QNAME, ArrayOfErroArquivo.class, MensagemRetorno.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://tempuri.org/", name = "Mensagem", scope = MensagemRetorno.class)
    public JAXBElement<String> createMensagemRetornoMensagem(String value) {
        return new JAXBElement<String>(_MensagemRetornoMensagem_QNAME, String.class, MensagemRetorno.class, value);
    }

}


package servicos.integracao.impl.conciliadora;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de ArrayOfErroArquivo complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="ArrayOfErroArquivo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ErroArquivo" type="{http://schemas.datacontract.org/2004/07/WSCACliente}ErroArquivo" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArrayOfErroArquivo", namespace = "http://schemas.datacontract.org/2004/07/WSCACliente", propOrder = {
    "erroArquivo"
})
public class ArrayOfErroArquivo {

    @XmlElement(name = "ErroArquivo", nillable = true)
    protected List<ErroArquivo> erroArquivo;

    /**
     * Gets the value of the erroArquivo property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the erroArquivo property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getErroArquivo().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ErroArquivo }
     * 
     * 
     */
    public List<ErroArquivo> getErroArquivo() {
        if (erroArquivo == null) {
            erroArquivo = new ArrayList<ErroArquivo>();
        }
        return this.erroArquivo;
    }

}

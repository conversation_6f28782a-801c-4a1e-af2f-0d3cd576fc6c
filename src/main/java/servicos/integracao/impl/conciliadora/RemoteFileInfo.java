
package servicos.integracao.impl.conciliadora;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de anonymous complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="FileByteStream" type="{http://schemas.microsoft.com/Message}StreamBody"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "fileByteStream"
})
@XmlRootElement(name = "RemoteFileInfo")
public class RemoteFileInfo {

    @XmlElement(name = "FileByteStream", required = true)
    protected byte[] fileByteStream;

    /**
     * Obtém o valor da propriedade fileByteStream.
     * 
     * @return
     *     possible object is
     *     byte[]
     */
    public byte[] getFileByteStream() {
        return fileByteStream;
    }

    /**
     * Define o valor da propriedade fileByteStream.
     * 
     * @param value
     *     allowed object is
     *     byte[]
     */
    public void setFileByteStream(byte[] value) {
        this.fileByteStream = value;
    }

}

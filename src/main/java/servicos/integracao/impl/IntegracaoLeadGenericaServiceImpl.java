package servicos.integracao.impl;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoIntegracaoBuzzLeadVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.*;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.arquitetura.UsuarioTelefoneInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.crm.*;
import negocio.interfaces.plano.ProdutoInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.interfaces.IntegracaoLeadGenericaServiceInterface;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;

public class IntegracaoLeadGenericaServiceImpl extends SuperServico implements IntegracaoLeadGenericaServiceInterface {
    private Indicado indicadoDAO;
    private Indicacao indicacaoDAO;
    private Cliente clienteDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private Telefone teledoneDAO;
    private Lead leadDAO;
    private ConversaoLead conversaoLeadDAO;
    private FecharMeta fecharMetaDAO;
    private Contrato contratoDAO;
    private AberturaMeta aberturaMetaDAO;
    private HistoricoContato historicoContatoDAO;
    private Passivo passivoDAO;

    public IntegracaoLeadGenericaServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public String processarNovaLead(String stringLead, int codidoEmpresa, String chave) throws Exception {
        Uteis.logar("### processarNovaLead: \n" +stringLead);
        JSONObject newLead = new JSONObject(stringLead);

        boolean isGymbotPro = false;
        try {
            isGymbotPro = newLead.getBoolean("isGymbot");
        } catch (Exception ignored) {
            // se não houver flag, assume false (integração genérica)
        }

        AcessoControle acessoControle = null;
        UsuarioVO usuarioAdmin = null;
        Uteis.logar("### processarNovaLead: \n" +stringLead);
        EmpresaInterfaceFacade empresaDao = new Empresa(getCon());
        UsuarioInterfaceFacade usuarioDao = new Usuario(getCon());
        ClienteInterfaceFacade clienteDao = new Cliente(getCon());
        LeadInterfaceFacade leadDao = new Lead(getCon());
        PassivoInterfaceFacade passivoDao = new Passivo(getCon());
        FecharMetaInterfaceFacade fecharMetaDao = new FecharMeta(getCon());
        HistoricoContatoInterfaceFacade historicoContatoDao = new HistoricoContato(getCon());
        Telefone telefoneDao = new Telefone(getCon());
        EmpresaVO empresa = empresaDao.consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        Uteis.logar("### consultarPorChavePrimaria: \n" + empresa.getNome());
        ConfiguracaoEmpresaBitrixVO configBittrix = empresaDao.consultarConfigIntegracaEmpresaBitrix24(chave, getCon());
        TimeZone tz = TimeZone.getTimeZone(empresa.getTimeZoneDefault());
        Date agora = Calendario.hojeCalendar(tz).getTime();
        Date horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoIntegracaoGenericaLeadsVO().getHoraLimite());
        UsuarioVO responsavel;
        AberturaMetaInterfaceFacade aberturaMetaDao = new AberturaMeta(getCon());
        List<AberturaMetaVO> metasAbertas = aberturaMetaDao.consultarMetasAbertaPorDiaPorFase(agora, codidoEmpresa, FasesCRMEnum.LEADS_HOJE, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
        if (metasAbertas.size() == 0) {
            for (int tentativas = 1; tentativas < 5; tentativas++) {
                metasAbertas = aberturaMetaDao.consultarMetasAbertaPorDiaPorFase(Uteis.somarDias(agora, -tentativas), codidoEmpresa, FasesCRMEnum.LEADS_HOJE, Uteis.NIVELMONTARDADOS_METACOLABORADORRESPONSAVEL);
                if (metasAbertas.size() > 0) {
                    break;
                }
            }
        }
        Uteis.logar("### Consultou Abetura de Metas\n");
        boolean wordPress;
        try {
            wordPress = newLead.getBoolean("wordPress");
        } catch (Exception ignored) {
            wordPress = false;
        }

        String tipo = "";
        try {
            tipo = newLead.getString("tipo");
        } catch (Exception ignored) {
        }

        if(wordPress) {
            if (!empresa.getConfiguracaoIntegracaoWordPressVO().isHabilitada()) {
                throw new Exception("Empresa não está configurada para integração de Leads Wordpress");
            }
            try {
                responsavel = empresa.getConfiguracaoIntegracaoWordPressVO().getResponsavelPadrao();
            }catch (Exception e){
                throw new Exception("Não foi encontrado Responsável para a Lead");
            }
            try {
                horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoIntegracaoWordPressVO().getHoraLimite());
            } catch (Exception e) {
                horarioLimite = Calendario.getDataComHora(Calendario.hoje(), "23:59");
            }
        } else {
            if (isGymbotPro) {
                // Fluxo se vier pelo Leads Gymbot Pro
                if (empresa.getConfiguracaoIntegracaoGenericaLeadsGymbotVO() == null ||
                        !empresa.getConfiguracaoIntegracaoGenericaLeadsGymbotVO().isHabilitada()) {
                    throw new Exception("Integração GymBot Pro está inativa.");
                }
                horarioLimite = Calendario.getDataComHora(Calendario.hoje(),
                        empresa.getConfiguracaoIntegracaoGenericaLeadsGymbotVO().getHoraLimite());
                if (empresa.getConfiguracaoIntegracaoGenericaLeadsGymbotVO().getResponsavelPadrao() != null &&
                        empresa.getConfiguracaoIntegracaoGenericaLeadsGymbotVO().getResponsavelPadrao().getCodigo() > 0) {
                    responsavel = empresa.getConfiguracaoIntegracaoGenericaLeadsGymbotVO().getResponsavelPadrao();
                } else if (!metasAbertas.isEmpty()) {
                    int posicao = (int) ((Math.random() * 100) % metasAbertas.size());
                    responsavel = metasAbertas.get(posicao).getColaboradorResponsavel();
                } else {
                    throw new Exception("Não foi encontrado Responsável para a Lead GymBot Pro.");
                }
            } else  {
                if (tipo.equals("Bitrix24") && configBittrix.isHabilitada()) {
                    responsavel = usuarioDao.consultarPorChavePrimaria(configBittrix.getResponsavelPadrao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                } else if (empresa.getConfiguracaoIntegracaoGenericaLeadsVO().isHabilitada() && !UteisValidacao.emptyNumber(empresa.getConfiguracaoIntegracaoGenericaLeadsVO().getResponsavelPadrao().getCodigo())) {
                        responsavel = empresa.getConfiguracaoIntegracaoGenericaLeadsVO().getResponsavelPadrao();
                } else if (!metasAbertas.isEmpty()) {
                    int posicao = (int) ((Math.random() * 100) % metasAbertas.size());
                    responsavel = metasAbertas.get(posicao).getColaboradorResponsavel();
                } else {
                    throw new Exception("Não foi encontrado Responsável para a Lead.");
                }
            }
        }

        String celular = "";
        try {
            celular = Formatador.formataTelefoneZW(newLead.getString("telefone"));
        } catch (Exception ignored) {
        }

        String email = "";
        try {
            email = newLead.getString("email");
        } catch (Exception ignored) {
        }

        String nome = newLead.getString("nome");
        String observacao = "";
        try {
            observacao = newLead.getString("mensagem");
        } catch (Exception ignored) {
        }
        String landing = "";
        try {
            landing = newLead.getString("landing");
        } catch (Exception ignored) {
        }



        PassivoVO passivo = null;
        PassivoVO passivoVO = null;
        ClienteVO cliPassivo = null;
        LeadVO lead = null;

        if (!UteisValidacao.emptyString(email)) {
            cliPassivo = clienteDao.consultarPorEmail(email, codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            Uteis.logar("### Consultou Cliente\n");
            if (cliPassivo != null) {
                lead = leadDao.consultarPorCliente(cliPassivo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                Uteis.logar("### Consultou Lead\n");
            } else {
                passivo = passivoDao.consultarPorEmail(email, codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                Uteis.logar("### Consultou Email\n");
                if (passivo != null) {
                    lead = leadDao.consultarPorIndicado(passivo.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    Uteis.logar("### Consultou Lead 2\n");
                }
            }
        }

        if (lead == null || UteisValidacao.emptyNumber(lead.getCodigo())) {
            lead = new LeadVO();
            lead.setEmpresa(empresa);
            if (tipo.equals("Bitrix24")) {
                lead.setTipo(TipoLeadEnum.BITIRX24);
            } else {
                 lead.setTipo(isGymbotPro ? TipoLeadEnum.GENERICOGYMBOTPRO : TipoLeadEnum.GENERICO);
            }
            if (tipo.equals("Bitrix24")) {
                try {
                    lead.setIdLead(Long.parseLong(newLead.getString("id")));
                    lead.setEmail(email);
                    if (newLead.getInt("responsavelPadrao") > 0)  responsavel.setCodigo(newLead.getInt("responsavelPadrao"));
                } catch (Exception ignored) {
                }
            }
            if (cliPassivo != null) {
                lead.setCliente(cliPassivo);
                telefoneDao.persistirTelefonesClienteLead(cliPassivo.getPessoa().getCodigo(), celular, "", "");
                Uteis.logar("### Gravou Cliente\n");

                passivo = new PassivoVO();
                passivo.setColaboradorResponsavel(responsavel);
                passivo.setDia(Calendario.hoje());
                passivo.setEmpresa(empresa.getNome());
                passivo.setObservacao(observacao);
                passivo.setOrigemSistemaEnum(OrigemSistemaEnum.SITE);
                passivo.setResponsavelCadastro(responsavel);
                passivo.setColaboradorResponsavel(responsavel);
                passivo.setNome(nome);
                passivo.setEmail(email);
                passivo.setClienteVO(cliPassivo);
                passivo.setEmpresaVO(empresa);
                passivo.setTelefoneCelular(celular);
                passivo.setLead(true);
                passivo.setDia(Calendario.hoje());
                passivoDao.incluir(passivo, false, false, true);
                Uteis.logar("### Gravou Passivo\n");
                lead.setPassivo(passivo);
            } else {
                if (passivo != null) {
                    passivoVO = passivoDao.consultarPorChavePrimaria(passivo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    Uteis.logar("### Consultou Passivo\n");

                    HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
                    historicoContatoVO.setDia(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                    historicoContatoVO.setObservacao(passivoVO.getObservacao());
                    historicoContatoVO.setResponsavelCadastro(passivoVO.getResponsavelCadastro());
                    historicoContatoVO.setResultado("Simples Registro");
                    historicoContatoVO.setContatoAvulso(false);
                    historicoContatoVO.setPassivoVO(passivo);
                    historicoContatoVO.setTipoContato("EM");
                    historicoContatoDao.incluir(historicoContatoVO);
                    Uteis.logar("### Gravou Historico Contato\n");

                    passivo.setTelefoneCelular(celular);
                    passivo.setNome(nome);
                   passivoDao.alterar(passivo, true);
                    Uteis.logar("### Alterou Passivo\n");
                } else {
                    passivo = new PassivoVO();
                    passivo.setColaboradorResponsavel(responsavel);
                    passivo.setDia(Calendario.hoje());
                    passivo.setEmpresa(empresa.getNome());
                    passivo.setObservacao(observacao);
                    passivo.setOrigemSistemaEnum(OrigemSistemaEnum.SITE);
                    passivo.setResponsavelCadastro(responsavel);
                    passivo.setColaboradorResponsavel(responsavel);
                    passivo.setNome(nome);
                    passivo.setEmail(email);
                    passivo.setEmpresaVO(empresa);
                    passivo.setTelefoneCelular(celular);
                    passivo.setLead(true);
                    passivo.setDia(Calendario.hoje());
                    passivoDao.incluir(passivo, wordPress, false, true);
                    Uteis.logar("### Gravou Passivo\n");
                    if(wordPress){
                        acessoControle = DaoAuxiliar.retornarAcessoControle(chave);
                        usuarioAdmin = acessoControle.getUsuarioDao().consultarPorChavePrimaria(1, 5);
                        notificarRecursoEmpresa(chave, RecursoSistema.ENVIOU_LEAD_WORDPRESS_PARA_CRM, usuarioAdmin, empresa);
                    }
                }
                lead.setPassivo(passivo);
            }
            leadDao.incluir(lead);
            Uteis.logar("### Gravou Lead\n");
        } else {
            if (!UteisValidacao.emptyNumber(lead.getCliente().getCodigo())) {
                telefoneDao.persistirTelefonesClienteLead(lead.getCliente().getPessoa().getCodigo(), celular, "", "");
                Uteis.logar("### Gravou Teledone Lead\n");
            } else {
                passivoVO = passivoDao.consultarPorChavePrimaria(passivo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Uteis.logar("### Consultou Passivo\n");

                HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
                historicoContatoVO.setDia(Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                historicoContatoVO.setObservacao(passivoVO.getObservacao());
                historicoContatoVO.setResponsavelCadastro(passivoVO.getResponsavelCadastro());
                historicoContatoVO.setResultado("Simples Registro");
                historicoContatoVO.setContatoAvulso(false);
                historicoContatoVO.setIndicadoVO(lead.getIndicado());
                historicoContatoVO.setTipoContato("EM");
                historicoContatoDao.incluir(historicoContatoVO);
                Uteis.logar("### Gravou Histórico de Contato\n");

                PassivoVO receptivo = new PassivoVO();
                receptivo.setColaboradorResponsavel(responsavel);
                receptivo.setDia(Calendario.hoje());
                receptivo.setEmpresa(empresa.getNome());
                receptivo.setObservacao(observacao);
                receptivo.setOrigemSistemaEnum(OrigemSistemaEnum.SITE);
                receptivo.setResponsavelCadastro(responsavel);
                receptivo.setColaboradorResponsavel(responsavel);

                receptivo.setTelefoneCelular(celular);
                receptivo.setNome(nome);
                receptivo.setEmail(email);
                receptivo.setEmpresaVO(empresa);
                passivoDao.incluir(receptivo, false, true);
                Uteis.logar("### Gravou Passivo\n");

                passivoDao.alterar(receptivo);
                Uteis.logar("### Alterou Passivo\n");
            }
        }

        ConversaoLeadVO novaLead = new ConversaoLeadVO();
        novaLead.setProps(stringLead);
        novaLead.setLead(lead);
        novaLead.setIdentificador(landing);
        novaLead.setResponsavel(responsavel);
        novaLead.setDataCriacao(Calendario.hoje());
        getConversaoLeadDAO().incluir(novaLead);
        Uteis.logar("### Gravou ConversaoLead\n");
        if (horarioLimite.after(agora)) {//se já tiver passado do horário limite, só entra na proxima meta
            fecharMetaDao.adicionarLeadMetaDoDia(novaLead, agora);
            Uteis.logar("### Gravou Lead Meta do Dia\n");
        }
        return novaLead.getCodigo().toString();
    }

    private void notificarRecursoEmpresa(String key, RecursoSistema recurso, UsuarioVO usuario, EmpresaVO empresa) {
        try{
            SuperControle.notificarRecursoEmpresa(key, recurso, usuario, empresa);
        }catch (Exception e){
            Uteis.logar("Não foi possivel gerar recurso empresa: ".concat(e.getMessage()));
        }
    }

    @Override
    public Object listNegociacaoBitrix(String url,  JSONObject fields) throws Exception {
        RequestHttpService service = new RequestHttpService();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url,
                headers,
                null,
                fields.toString(),
                MetodoHttpEnum.POST);
        String jsonRetorno = respostaHttpDTO.getResponse();
        if (respostaHttpDTO.getHttpStatus() != 200) {
            Uteis.logar(null, jsonRetorno);
            return new ArrayList<>();
        }
        return jsonRetorno;
    }
    @Override
    public String leadFieldBitrix( String url, String entidade) throws Exception {

        String acao="";
        if(entidade.equals("n")){
            acao = "/crm.deal.fields";
        }else{
            acao="/crm.lead.fields";
        }
        HttpGet get = new HttpGet(url+ acao);
        try {
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5 * 1000).build();
            HttpClient httpClient = ExecuteRequestHttpService.createConnector(requestConfig);
            HttpResponse response = httpClient.execute(get);
            System.out.println("Response Code : " + response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() != 200) {
                System.out.println("failed getting access token");
                return null;
            }
            String jsonResponse = EntityUtils.toString(response.getEntity());

            return jsonResponse;

        } catch (Exception ex) {
            System.out.println("Exception dadaos não encontrato" + ex);
        }

        return null;
    }

    public Object createNegociacaoBitrix(String url,  JSONObject fields, String id) throws Exception {
        RequestHttpService service = new RequestHttpService();
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();

        headers.put("Content-Type", "application/json");
        JSONObject body =  new JSONObject();
        if(!UteisValidacao.emptyString(id)){
            body.put("ID", id);
        }
        body.put("fields", fields);
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url,
                headers,
                null,
                body.toString(),
                MetodoHttpEnum.POST);
        String jsonRetorno = respostaHttpDTO.getResponse();
        if (respostaHttpDTO.getHttpStatus() != 200) {
            Uteis.logar(null, jsonRetorno);
            return new ArrayList<>();
        }
        return jsonRetorno;
    }
    public Object updateStausBitrix(String url, int id, JSONObject fields) throws Exception {
        RequestHttpService service = new RequestHttpService();
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();

        headers.put("Content-Type", "application/json");
        JSONObject body =  new JSONObject();
        body.put("id", id);
        body.put("fields", fields);
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url,
                headers,
                null,
                body.toString(),
                MetodoHttpEnum.POST);
        String jsonRetorno = respostaHttpDTO.getResponse();
        if (respostaHttpDTO.getHttpStatus() != 200) {
            Uteis.logar(null, jsonRetorno);
            return new ArrayList<>();
        }
        return jsonRetorno;
    }

    public Object extornaNegociacaoBitrix(String url) throws Exception {
        RequestHttpService service = new RequestHttpService();
        Map<String, String> headers = new HashMap<>();

        headers.put("Content-Type", "application/json");
        JSONObject body =  new JSONObject();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url,
                headers,
                null,
                body.toString(),
                MetodoHttpEnum.POST);
        String jsonRetorno = respostaHttpDTO.getResponse();
        if (respostaHttpDTO.getHttpStatus() != 200) {
            Uteis.logar(null, jsonRetorno);
            return new ArrayList<>();
        }
        return jsonRetorno;
    }
    public String processarCodeRDStation(String codigo , int empresa , String chave) {

        try (PreparedStatement stm = getCon().prepareStatement("INSERT INTO oauth2rdstation(\n" +
                "            codigoempresa, codigoauth)\n" +
                "    VALUES (?, ?);")) {
            int i = 1;
            stm.setInt(i++, empresa);
            stm.setString(i++, codigo);
            stm.execute();
        }catch (Exception e){
            return "ERRO:" + e;
        }
        return "SUCESSO: Codigo persistido com sucesso.";
    }

    public String processarTokenRDStation(String access_token, String refresh_token , int empresa , String chave) {

        try (PreparedStatement stm = getCon().prepareStatement("update oauth2rdstation set access_token = ? , refresh_token = ?, datapersistir = ? where codigoempresa = ?")) {
            int i = 1;
            stm.setString(i++, access_token);
            stm.setString(i++, refresh_token);
            stm.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(i, empresa);
            stm.execute();
        }catch (Exception e){
            return "ERRO:" + e;
        }
        return "SUCESSO: Token persistido com sucesso.";
    }

    public String atualizarCodeRDStation(String codigo , int empresa , String chave) {

        try (PreparedStatement stm = getCon().prepareStatement("update oauth2rdstation set codigoauth = ? where codigoempresa = ?")) {
            int i = 1;
            stm.setString(i++, codigo);
            stm.setInt(i, empresa);
        }catch (Exception e){
            return "ERRO:" + e;
        }
        return "SUCESSO: codigo persistido com sucesso.";
    }

    public boolean temCodigoValido( int empresa) throws Exception {
        String sqlExisteCodigo = "select exists(select codigoempresa from oauth2rdstation where codigoempresa = ? and ((datapersistir) + INTERVAL '1 DAY') > current_timestamp) as existe;";
        boolean existe;
        try (PreparedStatement sqlExiste = getCon().prepareStatement(sqlExisteCodigo)) {
            int i = 1;
            sqlExiste.setInt(i, empresa);
            try (ResultSet rs = sqlExiste.executeQuery()) {
                existe = false;
                if (rs.next()) {
                    existe = rs.getBoolean(1);
                }
            }
        }
        return existe;
    }

    public boolean temTokenAuth2Valido( int empresa) throws Exception {
        String sqlExisteToken = "select exists(select access_token,datapersistir from oauth2rdstation where ((datapersistir) + INTERVAL '1 DAY') > current_timestamp and access_token is not null and codigoempresa = ?) as existe;";
        boolean existe;
        try (PreparedStatement sqlExiste = getCon().prepareStatement(sqlExisteToken)) {
            int i = 1;
            sqlExiste.setInt(i, empresa);
            try (ResultSet rs = sqlExiste.executeQuery()) {
                existe = false;
                if (rs.next()) {
                    existe = rs.getBoolean(1);
                }
            }
        }
        return existe;
    }

    public boolean existeCodigoInserido( int empresa) throws Exception {
        String sqlExisteToken = "select exists(select * from oauth2rdstation where codigoempresa = ?) as existe;";
        boolean existe;
        try (PreparedStatement sqlExiste = getCon().prepareStatement(sqlExisteToken)) {
            int i = 1;
            sqlExiste.setInt(i, empresa);
            try (ResultSet rs = sqlExiste.executeQuery()) {
                existe = false;
                if (rs.next()) {
                    existe = rs.getBoolean(1);
                }
            }
        }
        return existe;
    }

    @Override
    public String obterAcessToken(final int empresa) throws Exception {
        String sql = "select access_token from oauth2rdstation where codigoempresa = " + empresa;
        try (ResultSet rs = criarConsulta(sql, getCon())) {
            if (rs.next()) {
                return rs.getString(1);
            } else {
                return null;
            }
        }
    }

    @Override
    public String buscaIDSecret(final int empresa) throws Exception {
        String sql = "select clientidoauthrds,clientsecretoauthrds from configuracaoempresardstation where empresa = " + empresa;
        try (ResultSet rs = criarConsulta(sql, getCon())) {
            if (rs.next()) {
                int i = 1;
                String id = rs.getString(i++);
                String secret = rs.getString(i);
                return id+","+secret;
            } else {
                return null;
            }
        }
    }

    private Indicado getIndicadoDAO() throws Exception {
        if (indicadoDAO == null) {
            indicadoDAO = new Indicado(getCon());
        }
        return indicadoDAO;
    }

    private Empresa getEmpresaDAO() throws Exception {
        if (empresaDAO == null) {
            empresaDAO = new Empresa(getCon());
        }
        return empresaDAO;
    }

    private Usuario getUsuarioDAO() throws Exception {
        if (usuarioDAO == null) {
            usuarioDAO = new Usuario(getCon());
        }
        return usuarioDAO;
    }

    private Lead getLeadDAO() throws Exception {
        if (leadDAO == null) {
            leadDAO = new Lead(getCon());
        }
        return leadDAO;
    }

    private FecharMeta getFecharMetaDAO() throws Exception {
        if (fecharMetaDAO == null) {
            fecharMetaDAO = new FecharMeta(getCon());
        }
        return fecharMetaDAO;
    }

    private ConversaoLead getConversaoLeadDAO() throws Exception {
        if (conversaoLeadDAO == null) {
            conversaoLeadDAO = new ConversaoLead(getCon());
        }
        return conversaoLeadDAO;
    }

    private Cliente getClienteDAO() throws Exception {
        if (clienteDAO == null) {
            clienteDAO = new Cliente(getCon());
        }
        return clienteDAO;
    }

    private Telefone getTeledoneDAO() throws Exception {
        if (teledoneDAO == null) {
            teledoneDAO = new Telefone(getCon());
        }
        return teledoneDAO;
    }

    private Indicacao getIndicacaoDAO() throws Exception {
        if (indicacaoDAO == null) {
            indicacaoDAO = new Indicacao(getCon());
        }
        return indicacaoDAO;
    }

    private Passivo getPassivoDAO() throws Exception {
        if (passivoDAO == null) {
            passivoDAO = new Passivo(getCon());
        }
        return passivoDAO;
    }

    private Contrato getContratoDAO() throws Exception {
        if (contratoDAO == null) {
            contratoDAO = new Contrato(getCon());
        }
        return contratoDAO;
    }

    private AberturaMeta getAberturaMetaDAO() throws Exception {
        if (aberturaMetaDAO == null) {
            aberturaMetaDAO = new AberturaMeta(getCon());
        }
        return aberturaMetaDAO;
    }

    public HistoricoContato getHistoricoContatoDAO() throws Exception {
        if (historicoContatoDAO == null) {
            historicoContatoDAO = new HistoricoContato(getCon());
        }
        return historicoContatoDAO;
    }
}

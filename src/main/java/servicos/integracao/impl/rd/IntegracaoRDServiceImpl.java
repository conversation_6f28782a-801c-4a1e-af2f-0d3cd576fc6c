/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.impl.rd;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.Formatador;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.sql.*;
import java.util.Date;
import java.util.Objects;
import java.util.TimeZone;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoEmpresaRDStationVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.crm.OAuth2RDStationVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.crm.ConversaoLead;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.Lead;
import negocio.facade.jdbc.crm.OAuth2RDStation;
import negocio.facade.jdbc.crm.Passivo;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.interfaces.rd.IntegracaoRDServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoRDServiceImpl extends SuperServico implements IntegracaoRDServiceInterface {

    private static final String URL_RD_CRIACAO = "https://www.rdstation.com.br/api/1.3/conversions";
    private static final String URL_RD_UPDATE = "https://www.rdstation.com.br/api/1.2/services/PRIVATE_TOKEN/generic";
    private static final String URL_RD_AUTH ="https://api.rd.services/auth/revoke";
    private static final String URL_RD_OBTER_TOKEN_ACESSO = "https://api.rd.services/auth/token";
    private Passivo passivoDAO;
    private Cliente clienteDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private Telefone teledoneDAO;
    private Lead leadDAO;
    private ConversaoLead conversaoLeadDAO;
    private FecharMeta fecharMetaDAO;
    private OAuth2RDStation oAuth2RDStationDAO;
    private static ObjectMapper mapper = new ObjectMapper();

    public IntegracaoRDServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public String autenticarOAuth(int codidoEmpresa) throws Exception {
        JSONObject jsonObject = new JSONObject();
        EmpresaVO empresa = getEmpresaDAO().consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        OAuth2RDStationVO oAuth2RDStationVO = getoAuth2RDStationDAO().consultarPorEmpresa(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!empresa.getConfiguracaoRDStation().isEmpresaUsaRD()) {
            throw new Exception("Empresa não está configurada para integração com RD");
        }
        jsonObject.put("client_id", empresa.getConfiguracaoRDStation().getClientIdOauth());
        jsonObject.put("client_secret", empresa.getConfiguracaoRDStation().getClientSecretOauth());
        jsonObject.put("code", oAuth2RDStationVO.getAcessToken());
        return enviarJSONData(new HttpPost(URL_RD_OBTER_TOKEN_ACESSO), jsonObject);

    }

    public void processarNovaLeadOLD(String stringLead, int codidoEmpresa) throws Exception {
        JSONObject newLead = new JSONObject(stringLead).getJSONArray("leads").getJSONObject(0);
        String uuid;
        String urlPublica = "";
        try {
            urlPublica = newLead.getString("public_url");
        } catch (Exception ignored) {
        }
        try {
            uuid = newLead.getString("uuid");
        } catch (Exception e) {
            if (!UteisValidacao.emptyString(urlPublica)) {
                uuid = urlPublica.substring(urlPublica.lastIndexOf("/") + 1);
            } else {
                throw new Exception("uuid não foi identificado");
            }
        }
        if (UteisValidacao.emptyString(urlPublica)) {
            urlPublica = "http://rdstation.com.br/leads/public/" + uuid;
        }
        EmpresaVO empresa = getEmpresaDAO().consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!empresa.getConfiguracaoRDStation().isEmpresaUsaRD()) {
            throw new Exception("Empresa não está configurada para integração com RD");
        }
        UsuarioVO responsavel = null;
        try {
            String emailResponsavel = newLead.getString("user");
            responsavel = getUsuarioDAO().consultarUsuarioEmailLeadMeta(codidoEmpresa, emailResponsavel, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception e) {

        }
        if (responsavel == null) {
            if (!UteisValidacao.emptyNumber(empresa.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                responsavel = empresa.getConfiguracaoRDStation().getResponsavelPadrao();
            } else {
                throw new Exception("Não foi encontrado Responsável para a Lead");
            }
        }
        String celular = "";
        String teleFoneResidencial = "";
        String teleFoneTrabalho = "";
        try {
            teleFoneResidencial = Formatador.formataTelefoneZW(newLead.getString("personal_phone"));
        } catch (Exception ignored) {
        }
        try {
            celular = Formatador.formataTelefoneZW(newLead.getString("mobile_phone"));
            if (!UteisValidacao.emptyString(celular)) {
                if (!celular.matches("\\(\\d{2}\\)(9)\\d{8}")) {
                    teleFoneTrabalho = celular;
                    celular = "";
                }
            }
        } catch (Exception ignored) {
        }

        LeadVO lead = getLeadDAO().consultarPorCodigoRD(uuid, Uteis.NIVELMONTARDADOS_TODOS);
        boolean leadEncontrado = false;
        if (UteisValidacao.emptyNumber(lead.getCodigo())) {
            lead.setTipo(TipoLeadEnum.RDSTATION);
            lead.setUuid(uuid);
            lead.setUrlRD(urlPublica);
            lead.setIdLead(newLead.getLong("id"));
            lead.setEmpresa(empresa);
            lead.setEmail(newLead.getString("email"));
            ClienteVO clienteVO = getClienteDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO != null) {
                lead.setCliente(clienteVO);
                getTeledoneDAO().persistirTelefonesClienteLead(clienteVO.getPessoa().getCodigo(), celular,teleFoneResidencial,teleFoneTrabalho);
            } else {
                PassivoVO passivoVO = getPassivoDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
                if (passivoVO != null) {
                    passivoVO.setTelefoneResidencial(teleFoneResidencial);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho(teleFoneTrabalho);
                    getPassivoDAO().alterar(passivoVO);
                } else {
                    passivoVO = new PassivoVO();
                    passivoVO.setNome(newLead.getString("name"));
                    passivoVO.setEmail(newLead.getString("email"));
                    passivoVO.setEmpresaVO(empresa);
                    passivoVO.setResponsavelCadastro(responsavel);
                    passivoVO.setTelefoneResidencial(teleFoneResidencial);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho(teleFoneTrabalho);
                    passivoVO.setLead(true);
                    getPassivoDAO().incluir(passivoVO,true);
                }
                lead.setPassivo(passivoVO);
            }
            getLeadDAO().incluir(lead);
        } else {
            leadEncontrado = true;
            if(!UteisValidacao.emptyNumber(lead.getCliente().getCodigo())){
                getTeledoneDAO().persistirTelefonesClienteLead(lead.getCliente().getPessoa().getCodigo(), celular,teleFoneResidencial,teleFoneTrabalho);
            } else {
                lead.getPassivo().setTelefoneResidencial(teleFoneResidencial);
                lead.getPassivo().setTelefoneCelular(celular);
                lead.getPassivo().setTelefoneTrabalho(teleFoneTrabalho);
                getPassivoDAO().alterar(lead.getPassivo());
            }
        }
        if(!leadEncontrado) {
            JSONObject ultimoContato = newLead.getJSONObject("last_conversion");
            if (ultimoContato == null) {
                ultimoContato = newLead.getJSONObject("first_conversion");
            }
            JSONObject conteudo = ultimoContato.getJSONObject("content");
            ConversaoLeadVO novaLead = new ConversaoLeadVO();
            novaLead.setProps(stringLead);
            novaLead.setLead(lead);
            novaLead.setIdentificador(conteudo.getString("identificador"));
            novaLead.setResponsavel(responsavel);
            novaLead.setDataCriacao(Uteis.getDate(ultimoContato.getString("created_at").substring(0, 9) + " " + ultimoContato.getString("created_at").substring(11, 15), "yyyy-MM-dd hh:mm"));
            getConversaoLeadDAO().incluir(novaLead);
            Date agora = Calendario.hoje();
            TimeZone tz = TimeZone.getTimeZone(empresa.getTimeZoneDefault());
            agora = Calendario.hojeCalendar(tz).getTime();
            Date horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoRDStation().getHoraLimite());
            if (horarioLimite.after(agora)) {//se já tiver passado do horário limite, só entra na proxima meta
                getFecharMetaDAO().adicionarLeadMetaDoDia(novaLead, agora);
            }
        }
    }

    public void processarNovaLead(String stringLead, int codidoEmpresa) throws Exception {
        JSONObject newLead = new JSONObject(stringLead).getJSONObject("contact");
        String uuid;
        String urlPublica = "";
        try {
            urlPublica = newLead.getString("public_url");
        } catch (Exception ignored) {
        }
        try {
            uuid = newLead.getString("uuid");
        } catch (Exception e) {
            if (!UteisValidacao.emptyString(urlPublica)) {
                uuid = urlPublica.substring(urlPublica.lastIndexOf("/") + 1);
            } else {
                throw new Exception("uuid não foi identificado");
            }
        }
        if (UteisValidacao.emptyString(urlPublica)) {
            urlPublica = "https://app.rdstation.com.br/leads/public/" + uuid;
        }
        EmpresaVO empresa = getEmpresaDAO().consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!empresa.getConfiguracaoRDStation().isEmpresaUsaRD()) {
            throw new Exception("Empresa não está configurada para integração com RD");
        }
        UsuarioVO responsavel = null;
        try {
            String emailResponsavel = newLead.getString("user");
            responsavel = getUsuarioDAO().consultarUsuarioEmailLeadMeta(codidoEmpresa, emailResponsavel, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception e) {

        }
        if (responsavel == null) {
            if (!UteisValidacao.emptyNumber(empresa.getConfiguracaoRDStation().getResponsavelPadrao().getCodigo())) {
                responsavel = empresa.getConfiguracaoRDStation().getResponsavelPadrao();
            } else {
                throw new Exception("Não foi encontrado Responsável para a Lead");
            }
        }
        String celular = "";
        String teleFoneResidencial = "";
        String teleFoneTrabalho = "";
        try {
            teleFoneResidencial = Formatador.formataTelefoneZW(newLead.getString("personal_phone"));
        } catch (Exception ignored) {
        }
        try {
            celular = Formatador.formataTelefoneZW(newLead.getString("mobile_phone"));
            if (!UteisValidacao.emptyString(celular)) {
                if (!celular.matches("\\(\\d{2}\\)(9)\\d{8}")) {
                    teleFoneTrabalho = celular;
                    celular = "";
                }
            }
        } catch (Exception ignored) {
        }

        LeadVO lead = getLeadDAO().consultarPorCodigoRD(uuid, Uteis.NIVELMONTARDADOS_TODOS);
        boolean leadEncontrado = false;
        if (UteisValidacao.emptyNumber(lead.getCodigo())) {
            lead.setTipo(TipoLeadEnum.RDSTATION);
            lead.setUuid(uuid);
            lead.setUrlRD(urlPublica);
            lead.setEmpresa(empresa);
            lead.setEmail(newLead.getString("email"));
            ClienteVO clienteVO = getClienteDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (clienteVO != null) {
                lead.setCliente(clienteVO);
                getTeledoneDAO().persistirTelefonesClienteLead(clienteVO.getPessoa().getCodigo(), celular,teleFoneResidencial,teleFoneTrabalho);
            } else {
                PassivoVO passivoVO = getPassivoDAO().consultarPorEmail(newLead.getString("email"), codidoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
                if (passivoVO != null) {
                    passivoVO.setTelefoneResidencial(teleFoneResidencial);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho(teleFoneTrabalho);
                    getPassivoDAO().alterar(passivoVO);
                } else {
                    passivoVO = new PassivoVO();
                    try{
                        passivoVO.setNome(newLead.getString("name"));
                    }catch(Exception ignore){
                        passivoVO.setNome(newLead.getString("cf_nome_que_nao_muda"));
                    }
                    passivoVO.setEmail(newLead.getString("email"));
                    passivoVO.setEmpresaVO(empresa);
                    passivoVO.setResponsavelCadastro(responsavel);
                    passivoVO.setTelefoneResidencial(teleFoneResidencial);
                    passivoVO.setTelefoneCelular(celular);
                    passivoVO.setTelefoneTrabalho(teleFoneTrabalho);
                    passivoVO.setLead(true);
                    getPassivoDAO().incluir(passivoVO,true);
                }
                lead.setPassivo(passivoVO);
            }
            getLeadDAO().incluir(lead);
        } else {
            leadEncontrado = true;
            if(!UteisValidacao.emptyNumber(lead.getCliente().getCodigo())){
                getTeledoneDAO().persistirTelefonesClienteLead(lead.getCliente().getPessoa().getCodigo(), celular,teleFoneResidencial,teleFoneTrabalho);
            } else {
                lead.getPassivo().setTelefoneResidencial(teleFoneResidencial);
                lead.getPassivo().setTelefoneCelular(celular);
                lead.getPassivo().setTelefoneTrabalho(teleFoneTrabalho);
                getPassivoDAO().alterar(lead.getPassivo());
            }
        }
        if(!leadEncontrado) {
            ConversaoLeadVO novaLead = new ConversaoLeadVO();
            novaLead.setProps(stringLead);
            novaLead.setLead(lead);
            novaLead.setIdentificador(new JSONObject(stringLead).get("event_identifier").toString());
            novaLead.setResponsavel(responsavel);
            novaLead.setDataCriacao(Uteis.getDate(new JSONObject(stringLead).get("timestamp").toString().substring(0, 10) + " " + new JSONObject(stringLead).get("timestamp").toString().substring(11, 19), "yyyy-MM-dd hh:mm"));
            getConversaoLeadDAO().incluir(novaLead);
            Date agora = Calendario.hoje();
            TimeZone tz = TimeZone.getTimeZone(empresa.getTimeZoneDefault());
            agora = Calendario.hojeCalendar(tz).getTime();
            Date horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoRDStation().getHoraLimite());
            if (horarioLimite.after(agora)) {//se já tiver passado do horário limite, só entra na proxima meta
                getFecharMetaDAO().adicionarLeadMetaDoDia(novaLead, agora);
            }
        }
    }

    public void alterarStatusLead(ConversaoLeadVO conversaoLead, boolean sucesso, Double valorVenda, String motivoDesistencia, String chave) {
        new Thread(){
            @Override
            public void run(){
        try {
            getEmpresaDAO();
            ConfiguracaoEmpresaRDStationVO cfg = Empresa.montarDadosConfigRDStation(conversaoLead.getLead().getEmpresa().getCodigo(), getCon());
            OAuth2RDStationVO oAuth2RDStationVO = getoAuth2RDStationDAO().consultarPorEmpresa(cfg.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //vai no endpoint e verifica se está atualizado o token, se não estiver pega um novo e persiste no banco
            String respostaToken = verificarTokenAtualizado(cfg.getEmpresa(), oAuth2RDStationVO.getRefreshToken(),chave);
            System.out.println(respostaToken);
            JSONObject jsonAtualizar = new JSONObject();
            jsonAtualizar.put("event_family", "CDP");
            JSONObject payload = new JSONObject();
            jsonAtualizar.put("email", conversaoLead.getLead().getEmail());
            if (sucesso) {
                jsonAtualizar.put("event_type", "SALE");
                payload.put("email",conversaoLead.getLead().getEmail());
                payload.put("funnel_name","default");
                payload.put("value",valorVenda);
                jsonAtualizar.put("payload",payload);
            } else {
                jsonAtualizar.put("event_type", "OPPORTUNITY_LOST");
                payload.put("email",conversaoLead.getLead().getEmail());
                payload.put("funnel_name","default");
                payload.put("reason",motivoDesistencia);
                jsonAtualizar.put("payload",payload);
            }
            String resposta = enviarAtualizacaoLeadJSONData(jsonAtualizar, cfg.getChavePrivada(),"Bearer " + oAuth2RDStationVO.getAcessToken(),chave);
            incluirLog(conversaoLead,chave,resposta,respostaToken);
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
        }
            }
        }.start();

    }

    public void incluirLog(ConversaoLeadVO conversaoLeadVO,String key, String resposta, String respostaToken) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(conversaoLeadVO.getCodigo().toString());
        obj.setNomeEntidade("PERSISTIR - RDSTATION");
        obj.setNomeEntidadeDescricao("Persistir - RD");
        obj.setOperacao("ATUALIZAÇÃO LEAD");
        obj.setNomeCampo("RESPOSTA:"+resposta+"/RESPOSTA DO TOKEN:"+respostaToken+"/E-MAIL DA LEAD: " + conversaoLeadVO.getLead().getEmail());
        obj.setValorCampoAlterado("");
        obj.setDataAlteracao(Calendario.hoje());

        try {
            DaoAuxiliar.retornarAcessoControle(key).getLogDao().incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
    }

    @Override
    public String enviarNovaLeadJSONData(JSONObject json) throws Exception {
        return enviarJSONData(new HttpPost(URL_RD_CRIACAO), json);
    }

//    @Override
//    public String enviarAtualizacaoLeadJSONData(JSONObject json, String chavePrivada) throws Exception {
//        String url = URL_RD_UPDATE.replace("PRIVATE_TOKEN", chavePrivada);
//        HttpPost post = new HttpPost(url);
//        return enviarJSONData(post, json);
//    }

    public String enviarAtualizacaoLeadJSONData(JSONObject json, String chavePrivada, String oAuthToken,String chave) throws Exception {
//        registrando o log
        LogVO obj = new LogVO();
        obj.setChavePrimaria(oAuthToken);
        obj.setNomeEntidade("ATUALIZAR LEAD VARIAVEIS");
        obj.setNomeEntidadeDescricao("ATUALIZAR - RD - VARIAVEIS");
        obj.setOperacao("ATUALIZAÇÃO LEAD VARIAVEIS");
        obj.setNomeCampo("TOKEN:"+oAuthToken + "CHAVE:" + chavePrivada+"JSON:" + json);
        obj.setValorCampoAlterado("");
        obj.setDataAlteracao(Calendario.hoje());

        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        String url = "https://api.rd.services/platform/events";
        HttpPost post = new HttpPost(url);
        post.setHeader("Authorization", oAuthToken);
        StringEntity entity = new StringEntity(json.toString(), "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);

//        registrando o log
        LogVO obj2 = new LogVO();
        obj2.setChavePrimaria(oAuthToken);
        obj2.setNomeEntidade("ATUALIZAR LEAD");
        obj2.setNomeEntidadeDescricao("ATUALIZAR - RD");
        obj2.setOperacao("ATUALIZAÇÃO LEAD");
        obj2.setNomeCampo("TOKEN:"+oAuthToken + "CHAVE:" + chavePrivada+"JSON:" + json);
        obj2.setValorCampoAnterior("RESPOSTA: " +  response.getStatusLine().toString());
        obj2.setValorCampoAlterado("");
        obj2.setDataAlteracao(Calendario.hoje());
        try {
            DaoAuxiliar.retornarAcessoControle(chave).getLogDao().incluirSemCommit(obj);
        } catch (Exception ignored) {

        }
        return response.getStatusLine().toString();
    }

    private String verificarTokenAtualizado(int empresa, String refreshToken,String chave) throws Exception {
        try {
            Uteis.logar(chave);
            String urlBase = PropsService.getPropertyValue(PropsService.urlZWAPI)+"/prest/lead/";
            String param = "oauth2token";
            String refreshTokenParam = "?code="+refreshToken;
            String chaveEmpresa =  "&chave=" + chave;
            String empresaCodigo = "&empresa=" + empresa;
            String acao = "&action=refresh";
            String url = urlBase+param+refreshTokenParam+chaveEmpresa+empresaCodigo+acao;
            URL url2 = new URL(urlBase+param+refreshTokenParam+chaveEmpresa+empresaCodigo+acao);
            HttpGet get = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5 * 1000).build();
            HttpClient httpClient = ExecuteRequestHttpService.createConnector(requestConfig);
            HttpResponse response = httpClient.execute(get);
            System.out.println(response);
            System.out.println("Response Code : "+ response.getStatusLine().getStatusCode());
            if (response.getStatusLine().getStatusCode() != 200) {
                System.out.println("failed getting access token");
            }

            StringBuffer result = resultado(response);
            System.out.println("raw result for bearer tokens= " + result);

            return result.toString();

        } catch (Exception ex) {
            System.out.println("Exception while retrieving bearer tokens" + ex);
        }
        return null;
    }

    public StringBuffer resultado(HttpResponse response) throws IOException {
        BufferedReader rd = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
        StringBuffer result = new StringBuffer();
        String line = "";
        while ((line = rd.readLine()) != null) {
            result.append(line);
        }
        return result;
    }

    @Override
    public String sendJSONData(JSONObject json) throws Exception {
        return enviarJSONData(new HttpPost(URL_RD_CRIACAO), json);
    }

    @Override
    public String enviarJSONData(HttpPost post, JSONObject json) throws Exception {
        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        post.setHeader("Accept", "application/json");
        post.setHeader("headerValue", "HeaderInformation");
        StringEntity entity = new StringEntity(json.toString(), "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);
        return response.getStatusLine().toString();
    }

    @Override
    public String enviarJSONDataOAuth(HttpPost post, JSONObject json) throws Exception {
        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", json.getString(""));
        StringEntity entity = new StringEntity(json.toString(), "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);
        return response.getStatusLine().toString();
    }

    @Override
    public String sendJSONDataOAuth(JSONObject json) throws Exception {
        return enviarJSONData(new HttpPost(URL_RD_CRIACAO), json);
    }


    public Passivo getPassivoDAO() throws Exception {
        if (passivoDAO == null) {
            passivoDAO = new Passivo(getCon());
        }
        return passivoDAO;
    }

    public void setPassivoDAO(Passivo passivoDAO) {
        this.passivoDAO = passivoDAO;
    }

    public Empresa getEmpresaDAO() throws Exception {
        if (empresaDAO == null) {
            empresaDAO = new Empresa(getCon());
        }
        return empresaDAO;
    }

    public void setEmpresaDAO(Empresa empresaDAO) {
        this.empresaDAO = empresaDAO;
    }

    public Usuario getUsuarioDAO() throws Exception {
        if (usuarioDAO == null) {
            usuarioDAO = new Usuario(getCon());
        }
        return usuarioDAO;
    }

    public void setUsuarioDAO(Usuario usuarioDAO) {
        this.usuarioDAO = usuarioDAO;
    }

    public Lead getLeadDAO() throws Exception {
        if (leadDAO == null) {
            leadDAO = new Lead(getCon());
        }
        return leadDAO;
    }

    public void setLeadDAO(Lead leadDAO) {
        this.leadDAO = leadDAO;
    }

    public FecharMeta getFecharMetaDAO() throws Exception {
        if (fecharMetaDAO == null) {
            fecharMetaDAO = new FecharMeta(getCon());
        }
        return fecharMetaDAO;
    }

    public void setFecharMetaDAO(FecharMeta fecharMetaDAO) {
        this.fecharMetaDAO = fecharMetaDAO;
    }

    public ConversaoLead getConversaoLeadDAO() throws Exception {
        if (conversaoLeadDAO == null) {
            conversaoLeadDAO = new ConversaoLead(getCon());
        }
        return conversaoLeadDAO;
    }

    public void setConversaoLeadDAO(ConversaoLead conversaoLeadDAO) {
        this.conversaoLeadDAO = conversaoLeadDAO;
    }

    public Cliente getClienteDAO() throws Exception {
        if (clienteDAO == null) {
            clienteDAO = new Cliente(getCon());
        }
        return clienteDAO;
    }

    public void setClienteDAO(Cliente clienteDAO) {
        this.clienteDAO = clienteDAO;
    }

    public Telefone getTeledoneDAO() throws Exception {
        if (teledoneDAO == null) {
            teledoneDAO = new Telefone(getCon());
        }
        return teledoneDAO;
    }

    public OAuth2RDStation getoAuth2RDStationDAO()  throws Exception{
        if (oAuth2RDStationDAO == null) {
            oAuth2RDStationDAO = new OAuth2RDStation(getCon());
        }
        return oAuth2RDStationDAO;
    }

    public void setoAuth2RDStationDAO(OAuth2RDStation oAuth2RDStationDAO) {
        this.oAuth2RDStationDAO = oAuth2RDStationDAO;
    }

    public void setTeledoneDAO(Telefone teledoneDAO) {
        this.teledoneDAO = teledoneDAO;
    }
    
    public void enviarListaBasicoRD(String sql, String identificador, Integer empresa, String tag) throws Exception{
        ConfiguracaoEmpresaRDStationVO cfg = getEmpresaDAO().montarDadosConfigRDStation(empresa, this.getCon());
        OAuth2RDStationVO oAuth2RDStationVO = getoAuth2RDStationDAO().consultarPorEmpresa(cfg.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if(!cfg.isEmpresaUsaRD() || UteisValidacao.emptyString(cfg.getChavePublica())){
            throw new Exception("Empresa não tem RD configurado corretamente");
        }
        ResultSet resultado = getRS(sql, empresa);
        while(resultado.next()){
            if(UteisValidacao.emptyString(resultado.getString("email"))){
                continue;
            }
            JSONObject json = montarJSONCliente(resultado);
            json.put("token_rdstation", oAuth2RDStationVO.getAcessToken());
            json.put("identificador", identificador);
            json.put("tags", "ZW");
            System.out.println("Enviando "+json.optString("identificador")+" para RD do aluno " + json.optString("nome"));
            Integer chavePacto = json.optInt("cliente");
            json.remove("cliente");
            String sendJSONData = sendJSONData(json);
            if(sendJSONData.contains("OK")){
                inserirIntegracao(chavePacto,identificador);
            }

        }
    }
    public ResultSet getRS(String sqlCodigosCliente,Integer codigoEmpresa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select c.codigo as codigocliente, p.estadocivil, emp.nome as empresa, p.nome as nomecliente, matricula, c.pessoa, c.codigo as cliente, situacao,\n");
        sql.append(" (select email from email where pessoa = p.codigo ORDER BY emailcorrespondencia DESC LIMIT 1) as email, \n");
        sql.append(" (select numero from telefone where pessoa = p.codigo and tipotelefone = 'RE' ORDER BY codigo DESC LIMIT 1) as residencial,\n");
        sql.append(" (select numero from telefone where pessoa = p.codigo and tipotelefone = 'CE' ORDER BY codigo DESC LIMIT 1) as celular, \n");
        sql.append(" (select bairro from endereco where pessoa = p.codigo ORDER BY codigo DESC LIMIT 1) as bairro, \n");
        sql.append(" p.sexo, p.datanasc \n");
        sql.append(" from cliente c \n \n");
        sql.append(" inner join pessoa p on p.codigo = c.pessoa inner join empresa emp on emp.codigo = c.empresa ");
        sql.append(" left join profissao pr on pr.codigo = p.profissao where c.empresa =").append(codigoEmpresa);
        sql.append("    and c.codigo in(").append(sqlCodigosCliente).append(" ) \n");
        
        PreparedStatement stm = getCon().prepareStatement(sql.toString());
        
        return stm.executeQuery();
    }
    
    public JSONObject montarJSONCliente(ResultSet rs) throws Exception {
        JSONObject jsonConversao = new JSONObject();
        jsonConversao.put("email", rs.getString("email"));
        jsonConversao.put("chavePacto", rs.getInt("codigocliente"));
        jsonConversao.put("nome", rs.getString("nomecliente"));
        jsonConversao.put("cliente", rs.getString("cliente"));
        jsonConversao.put("sexo", UteisValidacao.emptyString(rs.getString("sexo")) ? "" : (rs.getString("sexo").equals("F") ?
                "Feminino" : "Masculino"));

        jsonConversao.put("EstadoCivil", UteisValidacao.emptyString(rs.getString("estadocivil")) ?
                            "" : Dominios.getEstadoCivil().get(rs.getString("estadocivil")));

        jsonConversao.put("Bairro", rs.getString("empresa"));
        jsonConversao.put("Unidade", rs.getString("empresa"));
        jsonConversao.put("Nascimento", rs.getDate("datanasc") == null ? "" : Uteis.getData(rs.getDate("datanasc")));
        jsonConversao.put("personal_phone", rs.getString("residencial"));
        jsonConversao.put("mobile_phone", rs.getString("celular"));
        jsonConversao.put("situacaozw", rs.getString("situacao"));
        jsonConversao.put("tags", rs.getString("situacao").equals("VI") ? "Perdido" : "Cliente");
        return jsonConversao;
    }
    
    public void inserirIntegracao(Integer codigoCliente, String identificador) throws SQLException{
        try (PreparedStatement stm = getCon().prepareStatement("INSERT INTO enviosrd(identificador, cliente, envio) VALUES (?,?,?)")) {
            stm.setString(1, identificador);
            stm.setInt(2, codigoCliente);
            stm.setTimestamp(3, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.execute();
        }
    }

    public String autenticarOAuth2(int codidoEmpresa) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("client_id", "8159bc58-83c2-470b-8f11-9e6b8d2ad12c");
        jsonObject.put("client_secret", "2412b22447f341ebbe9f624ac89f86c5");
        jsonObject.put("code", "");
        return enviarJSONData(new HttpPost(URL_RD_OBTER_TOKEN_ACESSO), jsonObject);

    }

    public static void main(String... args) {
        try{
//            System.err.println(new IntegracaoRDServiceImpl(null).autenticarOAuth(1));

//            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "");
            Connection c = DriverManager.getConnection("************************************************"
                    + "", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(c);
            Integer empresa = args.length > 1 ?  Integer.valueOf(args[1]) : 0;
            String sql = args.length > 2 ? args[2] : "0";
            String identificador = args.length > 3 ? args[3] : "0";
            String tags = args.length > 4 ? args[4] : "";
            IntegracaoRDServiceImpl intRD = new IntegracaoRDServiceImpl(c);
//            intRD.autenticarOAuth(1);
                intRD.enviarListaBasicoRD(sql,identificador,1,tags);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao.impl.buzzlead;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConversaoLeadVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.LeadVO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.ConversaoLead;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.Indicacao;
import negocio.facade.jdbc.crm.Indicado;
import negocio.facade.jdbc.crm.Lead;
import negocio.facade.jdbc.crm.Objecao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.integracao.enumerador.TipoLeadEnum;
import servicos.integracao.interfaces.buzzlead.IntegracaoBuzzLeadServiceInterface;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class IntegracaoBuzzLeadServiceImpl extends SuperServico implements IntegracaoBuzzLeadServiceInterface {
    private static final String urlBuzzUpdate = "https://app.buzzlead.com.br/webhook/service/notification/crm/pacto/update/deal";
    private Indicado indicadoDAO;
    private Indicacao indicacaoDAO;
    private Cliente clienteDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private Telefone teledoneDAO;
    private Lead leadDAO;
    private ConversaoLead conversaoLeadDAO;
    private FecharMeta fecharMetaDAO;
    private Contrato contratoDAO;

    public IntegracaoBuzzLeadServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public static String enviarAtualizacaoLeadJSONData(JSONObject json, String chavePrivada) throws Exception {
        Map<String, String> m = new HashMap<>();
        m.put("Content-Type", "application/json");
        m.put("x-api-token-buzzlead", chavePrivada);
        String retornoRequest = ExecuteRequestHttpService.executeHttpRequest(urlBuzzUpdate, json.toString(), m, "POST", "UTF-8");
        Uteis.logar(retornoRequest);
        return retornoRequest;
    }

    public static void main(String[] args) {
        if (args.length > 0) {

            try {
                Connection con;
                if (!args[0].equals("teste")) {
                    con = new DAO().obterConexaoEspecifica(args[0]);
                } else {
                    con = DriverManager.getConnection("*********************************************", "zillyonweb", "pactodb");
                }

                Conexao.guardarConexaoForJ2SE(con);

                IntegracaoBuzzLeadServiceImpl integracaoBuzzLeadService = new IntegracaoBuzzLeadServiceImpl(con);
                Contrato contratoDAO = new Contrato(con);
                Objecao objecaoDAO = new Objecao(con);

                ConversaoLead conversaoLeadDAO = new ConversaoLead(con);
                List<ConversaoLeadVO> listaLeads = conversaoLeadDAO.consultarTodosAcao("Buzzlead");
                for (ConversaoLeadVO conversao : listaLeads) {
                    double valorConversao = 0.0;
                    String motivoDesistencia = "";
                    boolean integrar = false;
                    if (!UteisValidacao.emptyNumber(conversao.getContrato().getCodigo())) {
                        try {
                            ContratoVO contrato = contratoDAO.consultarPorChavePrimaria(conversao.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            conversao.setContrato(contrato);
                            valorConversao = contrato.getValorFinal();
                            integrar = true;
                        } catch (Exception ignored) {
                        }
                    }
                    if (!UteisValidacao.emptyNumber(conversao.getObjecao().getCodigo())) {
                        ObjecaoVO objecao = objecaoDAO.consultarPorChavePrimaria(conversao.getObjecao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        motivoDesistencia = objecao.getComentario();
                        integrar = true;
                    }

                    if (integrar) {
                        System.out.println("LEAD: " + conversao.getCodigo());
                        integracaoBuzzLeadService.alterarStatusLead(conversao, !UteisValidacao.emptyNumber(conversao.getContrato().getCodigo()), valorConversao, motivoDesistencia);
                    }
                }


            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            JSONObject jsonAtualizar = new JSONObject();
            jsonAtualizar.put("crmId", 1);
            jsonAtualizar.put("empresa", 1);
            jsonAtualizar.put("cnpj_empresa", "06.323.555/0001-50");
            jsonAtualizar.put("status", "lost");
            jsonAtualizar.put("lost_reason", Uteis.normalizarStringJSON("Teste"));
            try {
                enviarAtualizacaoLeadJSONData(jsonAtualizar, "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************.koqR-YCLkmRE-q8KJTtZ97UYakbz6AFKyKXpRay4qVQ");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public String processarNovaLead(String stringLead, int codidoEmpresa) throws Exception {
        JSONObject newLead = new JSONObject(stringLead);
        JSONObject destino = newLead.getJSONObject("destino");
        JSONObject origem = newLead.getJSONObject("origem");
        EmpresaVO empresa = getEmpresaDAO().consultarPorChavePrimaria(codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (!empresa.getConfiguracaoIntegracaoBuzzLeadVO().isHabilitada()) {
            throw new Exception("Empresa não está configurada para integração com Buzz Lead");
        }
        UsuarioVO responsavel = null;
        try {
            String emailResponsavel = destino.getString("responsavel");
            responsavel = getUsuarioDAO().consultarUsuarioEmailLeadMeta(codidoEmpresa, emailResponsavel, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        } catch (Exception e) {

        }
        if (responsavel == null) {
            if (!UteisValidacao.emptyNumber(empresa.getConfiguracaoIntegracaoBuzzLeadVO().getResponsavelPadrao().getCodigo())) {
                try {
                    responsavel = getUsuarioDAO().consultarPorChavePrimaria(empresa.getConfiguracaoIntegracaoBuzzLeadVO().getResponsavelPadrao().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } catch (Exception e) {
                    throw new Exception("Falha ao consultar usuario responsável padrão Buzzlead");
                }
            } else {
                throw new Exception("Não foi encontrado Responsável para a Lead");
            }
        }
        String celular = "";
        try {
            celular = Formatador.formataTelefoneZW(destino.getString("telefone"));
        } catch (Exception ignored) {
        }
        String email = "";
        try {
            email = destino.getString("email");
        } catch (Exception ignored) {
        }

        String nome = destino.getString("name");

        String documento = "";
        try {
            documento = destino.getString("documento");
        } catch (Exception ignored) {
        }

        String opcional = "";
        try {
            opcional = destino.getString("opcional");
        } catch (Exception ignored) {
        }
        String origemBuzz = "";
        try {
            origemBuzz = destino.getString("origem");
        } catch (Exception ignored) {
        }

        String emailOrigem = "";
        try {
            emailOrigem = origem.getString("email");
        } catch (Exception ignored) {
        }

        IndicadoVO indicado = null;
        ClienteVO clienteIndicador = null;
        ClienteVO cliIndicado = null;
        LeadVO lead = null;
        clienteIndicador = getClienteDAO().consultarPorEmail(emailOrigem, codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);


        if (!UteisValidacao.emptyString(email)) {
            cliIndicado = getClienteDAO().consultarPorEmail(email, codidoEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (cliIndicado != null) {
                // Consultar o vencimento do último contrato do cliente.
                Date dataUltimoVenct = getContratoDAO().consultarUltimoVencimento(cliIndicado.getPessoa().getCodigo());
                if (dataUltimoVenct == null || Calendario.menorOuIgual(dataUltimoVenct, Uteis.somarDias(Calendario.hoje(), -180))) {
                    lead = getLeadDAO().consultarPorCliente(cliIndicado.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    throw new Exception("ERRO: O indicado já é cliente da academia");
                }
            } else {
                indicado = getIndicadoDAO().consultarPorEmail(codidoEmpresa, email, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if (indicado != null) {
                    lead = getLeadDAO().consultarPorIndicado(codidoEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
                }
            }
        }

        if (lead == null || UteisValidacao.emptyNumber(lead.getCodigo())) {
            lead = new LeadVO();
            lead.setEmpresa(empresa);
            lead.setTipo(TipoLeadEnum.BUZZLEAD);
            if (cliIndicado != null) {
                lead.setCliente(cliIndicado);
                getTeledoneDAO().persistirTelefonesClienteLead(cliIndicado.getPessoa().getCodigo(), celular, "", "");
            } else {
                if (indicado != null) {
                    indicado.setTelefoneIndicado(celular);
                    indicado.setNomeIndicado(nome);
                    getIndicadoDAO().alterar(indicado);
                } else {
                    IndicacaoVO indicacao = new IndicacaoVO();
                    if (clienteIndicador == null) {
                        indicacao.setColaboradorQueIndicou(responsavel.getColaboradorVO());
                    } else {
                        indicacao.setClienteQueIndicou(clienteIndicador);
                    }
                    indicacao.setDia(Calendario.hoje());
                    indicacao.setEmpresa(empresa);
                    indicacao.setObservacao("Documento: " + documento + "\n" + opcional);
                    indicacao.setOrigemSistemaEnum(OrigemSistemaEnum.BUZZLEAD);
                    indicacao.setResponsavelCadastro(responsavel);
                    indicacao.setColaboradorResponsavel(responsavel);
                    indicado = new IndicadoVO();
                    indicado.setNomeIndicado(nome);
                    indicado.setEmail(email);
                    indicado.setEmpresaVO(empresa);
                    indicado.setTelefoneIndicado(celular);
                    indicado.setLead(true);
                    getIndicacaoDAO().incluirSomenteIndicacao(indicacao);
                    indicado.setIndicacaoVO(indicacao);
                    indicado.setDataLancamento(Calendario.hoje());
                    getIndicadoDAO().incluir(indicado);
                }
                lead.setIndicado(indicado);
            }
            getLeadDAO().incluir(lead);
        } else {
            if (!UteisValidacao.emptyNumber(lead.getCliente().getCodigo())) {
                getTeledoneDAO().persistirTelefonesClienteLead(lead.getCliente().getPessoa().getCodigo(), celular, "", "");
            } else {
                lead.getIndicado().setTelefoneIndicado(celular);
                getIndicadoDAO().alterar(lead.getIndicado());
            }
        }

        ConversaoLeadVO novaLead = new ConversaoLeadVO();
        novaLead.setProps(stringLead);
        novaLead.setLead(lead);
        novaLead.setIdentificador(origemBuzz);
        novaLead.setResponsavel(responsavel);
        novaLead.setDataCriacao(Calendario.hoje());
        getConversaoLeadDAO().incluir(novaLead);
        Date agora = Calendario.hoje();
        TimeZone tz = TimeZone.getTimeZone(empresa.getTimeZoneDefault());
        agora = Calendario.hojeCalendar(tz).getTime();
        Date horarioLimite = Calendario.getDataComHora(Calendario.hoje(), empresa.getConfiguracaoIntegracaoBuzzLeadVO().getHoraLimite());
        if (horarioLimite.after(agora)) {//se já tiver passado do horário limite, só entra na proxima meta
            getFecharMetaDAO().adicionarLeadMetaDoDia(novaLead, agora);
        }
        return novaLead.getCodigo().toString();
    }

    @Override
    public String consultarStatusIndicacao(String identificador) throws Exception {
        try {
            Integer codConversao = Integer.parseInt(identificador);
            ConversaoLeadVO conversaoVO = getConversaoLeadDAO().consultarPorChavePrimaria(codConversao, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyNumber(conversaoVO.getContrato().getCodigo())) {
                return "won";
            } else if (!UteisValidacao.emptyNumber(conversaoVO.getObjecao().getCodigo())) {
                return "lost";
            } else {
                return "pending";
            }
        } catch (Exception e) {
            Uteis.logar(e, this.getClass());
            throw e;
        }
    }

    public Indicado getIndicadoDAO() throws Exception {
        if (indicadoDAO == null) {
            indicadoDAO = new Indicado(getCon());
        }
        return indicadoDAO;
    }

    public void setIndicadoDAO(Indicado indicadoDAO) {
        this.indicadoDAO = indicadoDAO;
    }

    public Empresa getEmpresaDAO() throws Exception {
        if (empresaDAO == null) {
            empresaDAO = new Empresa(getCon());
        }
        return empresaDAO;
    }

    public void setEmpresaDAO(Empresa empresaDAO) {
        this.empresaDAO = empresaDAO;
    }

    public Usuario getUsuarioDAO() throws Exception {
        if (usuarioDAO == null) {
            usuarioDAO = new Usuario(getCon());
        }
        return usuarioDAO;
    }

    public void setUsuarioDAO(Usuario usuarioDAO) {
        this.usuarioDAO = usuarioDAO;
    }

    public Lead getLeadDAO() throws Exception {
        if (leadDAO == null) {
            leadDAO = new Lead(getCon());
        }
        return leadDAO;
    }

    public void setLeadDAO(Lead leadDAO) {
        this.leadDAO = leadDAO;
    }

    public FecharMeta getFecharMetaDAO() throws Exception {
        if (fecharMetaDAO == null) {
            fecharMetaDAO = new FecharMeta(getCon());
        }
        return fecharMetaDAO;
    }

    public void setFecharMetaDAO(FecharMeta fecharMetaDAO) {
        this.fecharMetaDAO = fecharMetaDAO;
    }

    public ConversaoLead getConversaoLeadDAO() throws Exception {
        if (conversaoLeadDAO == null) {
            conversaoLeadDAO = new ConversaoLead(getCon());
        }
        return conversaoLeadDAO;
    }

    public void setConversaoLeadDAO(ConversaoLead conversaoLeadDAO) {
        this.conversaoLeadDAO = conversaoLeadDAO;
    }

    public Cliente getClienteDAO() throws Exception {
        if (clienteDAO == null) {
            clienteDAO = new Cliente(getCon());
        }
        return clienteDAO;
    }

    public void setClienteDAO(Cliente clienteDAO) {
        this.clienteDAO = clienteDAO;
    }

    public Telefone getTeledoneDAO() throws Exception {
        if (teledoneDAO == null) {
            teledoneDAO = new Telefone(getCon());
        }
        return teledoneDAO;
    }

    public void setTeledoneDAO(Telefone teledoneDAO) {
        this.teledoneDAO = teledoneDAO;
    }

    public Indicacao getIndicacaoDAO() throws Exception {
        if (indicacaoDAO == null) {
            indicacaoDAO = new Indicacao(getCon());
        }
        return indicacaoDAO;
    }

    public void setIndicacaoDAO(Indicacao indicacaoDAO) {
        this.indicacaoDAO = indicacaoDAO;
    }

    public Contrato getContratoDAO() throws Exception {
        if (contratoDAO == null) {
            contratoDAO = new Contrato(getCon());
        }
        return contratoDAO;
    }

    public void setContratoDAO(Contrato contratoDAO) {
        this.contratoDAO = contratoDAO;
    }

    public void alterarStatusLead(ConversaoLeadVO conversaoLead, boolean sucesso, Double valorVenda, String motivoDesistencia) {
        try {
            JSONObject jsonAtualizar = new JSONObject();
            jsonAtualizar.put("crmId", conversaoLead.getCodigo());
            jsonAtualizar.put("empresa", conversaoLead.getLead().getEmpresa().getCodigo());
            jsonAtualizar.put("cnpj_empresa", conversaoLead.getLead().getEmpresa().getCNPJ());
            if (sucesso) {
                jsonAtualizar.put("status", "won");
                jsonAtualizar.put("total", valorVenda);
                jsonAtualizar.put("contrato", conversaoLead.getContrato().getCodigo());
                jsonAtualizar.put("data", conversaoLead.getContrato().getDataLancamento_Apresentar());
            } else {
                jsonAtualizar.put("status", "lost");
                jsonAtualizar.put("lost_reason", Uteis.normalizarStringJSON(motivoDesistencia));
            }
            System.out.println(jsonAtualizar);
            enviarAtualizacaoLeadJSONData(jsonAtualizar, conversaoLead.getLead().getEmpresa().getTokenBuzzLead());
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(e, this.getClass());
        }

    }
}

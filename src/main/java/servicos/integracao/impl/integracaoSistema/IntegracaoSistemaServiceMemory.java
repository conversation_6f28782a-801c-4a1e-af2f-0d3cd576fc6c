package servicos.integracao.impl.integracaoSistema;

import java.util.HashMap;
import java.util.Map;

public class IntegracaoSistemaServiceMemory {

    //chavedaempresa_codempresa --> urlWebhook
    private static Map<String, String> empresasEConfiguracoes = new HashMap<String, String>();

    public static Map<String, String> getEmpresasEConfiguracoes() {
        return empresasEConfiguracoes;
    }

    public static void setEmpresasEConfiguracoes(Map<String, String> empresasEConfiguracoes) {
        IntegracaoSistemaServiceMemory.empresasEConfiguracoes = empresasEConfiguracoes;
    }
}

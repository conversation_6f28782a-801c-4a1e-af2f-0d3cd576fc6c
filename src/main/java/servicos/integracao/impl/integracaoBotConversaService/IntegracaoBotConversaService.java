package servicos.integracao.impl.integracaoBotConversaService;

import br.com.pactosolucoes.integracao.CadSis.IntegracaoBotConversaDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoIntegracaoBotConversaVO;
import negocio.comuns.crm.ConfiguracaoIntegracaoGymbotProVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.crm.MalaDireta;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.vendas.VendasConfigVO;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.integracao.botconversa.BotConversaController;
import servicos.integracao.gymbotpro.GymbotProController;
import servicos.integracao.sms.Message;
import servicos.vendasonline.dto.VendaDTO;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;


public class IntegracaoBotConversaService extends SuperServico {

    private String key;
    private Connection con;

    public IntegracaoBotConversaService(Connection con) throws Exception {
        super(con);
        this.key = DAO.resolveKeyFromConnection(con);
        this.con = con;
    }

    public String dispararFluxoGymbot ( Map<String,String> body){
        try {
            List<Message> messageList = new ArrayList<>();
            Message m = new Message();
            m.setNumero(body.get("numero"));
            m.setNome(body.get("nome"));
            m.setMsg("ND");
            messageList.add(m);

            MalaDireta malaDiretaDAO = new MalaDireta(con);
            ConfiguracaoIntegracaoBotConversaVO fluxoGymbot = malaDiretaDAO.buscarFluxoPeloCodigoEmpresa(Integer.valueOf(body.get("idEmpresa")), con);
            BotConversaController controller = new BotConversaController(this.key, con);
            String ret =  controller.sendMessage(this.key, Integer.valueOf(body.get("idEmpresa")) , fluxoGymbot.getUrlwebhoobotconversa(), messageList);

            controller.gravaHistoricoContato(body, ret, fluxoGymbot);
            return ret;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String dispararFluxoGymbotPro ( Map<String,String> body){
        try {
            List<Message> messageList = new ArrayList<>();
            Message m = new Message();
            m.setNumero(body.get("numero"));
            m.setNome(body.get("nome"));
            m.setMsg("ND");
            messageList.add(m);

            MalaDireta malaDiretaDAO = new MalaDireta(con);
            ConfiguracaoIntegracaoGymbotProVO fluxoGymbot = malaDiretaDAO.buscarFluxoGymbotProPeloCodigoEmpresa(Integer.valueOf(body.get("idEmpresa")), con);
            GymbotProController controller = new GymbotProController(this.key, con);
            String ret = controller.sendMessage(key, Integer.valueOf(body.get("idEmpresa")), fluxoGymbot.getToken(), fluxoGymbot.getIdFluxo(), messageList);

            controller.gravaHistoricoContato(body, ret, fluxoGymbot);
            return ret;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void notificarWebhookBotConversa(VendaDTO vendaDTO, TransacaoVO transacaoVO, String operacao, EmpresaVO empresaVO, VendasConfigVO vendasConfigVO) throws Exception {
        Plano planoDAO;
        try {
            planoDAO = new Plano(getCon());
            PlanoVO planoVO = planoDAO.consultarPorChavePrimaria(vendaDTO.getPlano(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            //Data/hora da operação deve respeitar o timezone da empresa
            String data = Uteis.getDataComHHMM(Calendario.hojeCalendar(TimeZone.getTimeZone(empresaVO.getTimeZoneDefault())).getTime());

            IntegracaoBotConversaDTO dto = new IntegracaoBotConversaDTO(this.key, operacao, vendaDTO, planoVO, transacaoVO.getValor(), data);
            enviarDadosAssincrono(dto, vendasConfigVO, data, vendaDTO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            planoDAO = null;
        }
    }

    private void enviarDadosAssincrono(IntegracaoBotConversaDTO integracaoBotConversaDTO, VendasConfigVO vendasConfigVO, String dataOperacao, VendaDTO vendaDTO) {

        Uteis.logarDebug("Integração BotConversa habilitada! Enviar informações webhook BotConversa | Integração OTTO PERSONAL");
        new Thread(new Runnable() {
            @Override
            public void run() {
                Connection con = null;
                try {
                    con = new DAO().obterConexaoEspecifica(integracaoBotConversaDTO.getChave());
                    int tentativa = 1;

                    //vai tentar notificar no máximo 3 vezes e de 5 em 5 minutos
                    while (tentativa <= 3) {
                        try {
                            String url = vendasConfigVO.getEnderecoEnviarAcoesBotConversa();
                            if (UteisValidacao.emptyString(url)) {
                                Uteis.logarDebug("Não consegui notificar o webhook pois a url está vazia.");
                                break;
                            }
                            if (tentativa == 1 && integracaoBotConversaDTO.getOperacao().equalsIgnoreCase("CARRINHO")) {
                                //aguardar 5 minutos antes de notificar de fato. Quando acabar os 5 minutos, deve validar se já não teve uma compra com sucesso. Se já tiver uma de sucesso então não precisa notificar o erro;
                                Uteis.logarDebug("Iniciando notificação de operação CARRINHO...!");
                                Uteis.logarDebug("Vou aguardar 5 minutos antes de tentar notificar a primeira vez!");
                                Thread.sleep(60000 * 5); // 5 minutos
                                boolean jaFezACompraComSuceso = verificarSeJaFezACompraComSuceso(con, dataOperacao, vendaDTO);
                                if (jaFezACompraComSuceso) {
                                    Uteis.logarDebug("Aluno já fez compra com sucesso, não preciso notificar o Carrinho.");
                                    break; //se teve venda aprovada não precisa mais notificar a operação "CARRINHO"
                                }
                            } else if (tentativa == 1 && integracaoBotConversaDTO.getOperacao().equalsIgnoreCase("VENDA")) {
                                Uteis.logarDebug("Iniciando notificação de operação VENDA...!");
                                Uteis.logarDebug("Não preciso aguardar, vou notificar direto!");
                            }
                            Map<String, String> headers = new HashMap<>();
                            headers.put("Content-Type", "application/json");
                            RequestHttpService service = new RequestHttpService();
                            Uteis.logarDebug("Vou notificar agora o webhook...Tentativa: " + tentativa);
                            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, new JSONObject(integracaoBotConversaDTO).toString(), MetodoHttpEnum.POST);
                            service = null;
                            if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                                throw new Exception(respostaHttpDTO.getResponse());
                            }
                            Uteis.logarDebug("Notificado com sucesso!");
                            break;
                        } catch (Exception ex) {
                            if (tentativa < 3) {
                                Uteis.logarDebug("Não consegui notificar o webhook de operação " + integracaoBotConversaDTO.getOperacao() + " na tentativa: " + tentativa + ". Agora vou aguardar 5 minutos para tentar de novo");
                            } else if (tentativa == 3) {
                                Uteis.logarDebug("Não consegui notificar o webhook de operação " + integracaoBotConversaDTO.getOperacao() + " na tentativa: " + tentativa + ". Já tentei 3 vezes e não vou tentar mais.");
                            }
                            if (tentativa != 3) {
                                Thread.sleep(60000 * 5); // 5 minutos
                            }
                        }
                        tentativa++;
                    }
                } catch (Exception e) {
                    Uteis.logar(e, IntegracaoBotConversaService.class);
                } finally {
                    try {
                        if (con != null) {
                            con.close();
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        }).start();
    }

    private boolean verificarSeJaFezACompraComSuceso(Connection con, String dataOperacao, VendaDTO vendaDTO) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select exists( \n");
            sql.append("select  \n");
            sql.append("t.codigo \n");
            sql.append("from transacao t  \n");
            sql.append("inner join vendasonlinevenda vv on vv.transacao = t.codigo \n");
            sql.append("inner join pessoa p on p.codigo = vv.pessoa  \n");
            sql.append("where vv.dataregistro > '" + Uteis.getDataJDBCTimestamp(Uteis.getDate(dataOperacao)) + "'");
            sql.append("and p.cfp like '" + Uteis.formatarCpfCnpj(vendaDTO.getCpf(), false) + "'");
            sql.append("and t.situacao in (4,2) \n");
            sql.append(") as existe \n");

            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
                while (rs.next()) {
                    return rs.getBoolean("existe");
                }
            }
        } catch (Exception ignored) {
        }
        return false;
    }
}

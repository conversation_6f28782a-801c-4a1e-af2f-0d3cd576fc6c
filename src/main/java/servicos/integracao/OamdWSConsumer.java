/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao;

import negocio.comuns.utilitarias.Uteis;
import servicos.integracao.oamd.OamdWS;
import servicos.integracao.oamd.OamdWS_Service;
import servicos.propriedades.PropsService;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public final class OamdWSConsumer {

    private static OamdWS servico;
    private static boolean creating = false;

    private static OamdWS getInstance() {
        if (creating) {
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        if (servico == null) {
            creating = true;
            try {
                URL u = new URL(PropsService.getPropertyValue(PropsService.urlOamd) + "/OamdWS?wsdl");
                QName qName = new QName("http://ws.pacto.com.br/", "OamdWS");
                servico = new OamdWS_Service(u, qName).getOamdWSPort();
            } catch (MalformedURLException ex) {
                Logger.getLogger(OamdWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
            } finally {
                creating = false;
            }
        }
        return servico;
    }

    public static String obterFeeds() {
        try {
            return getInstance().obterFeeds();
        } catch (Exception e) {
            Uteis.logar(e, OamdWSConsumer.class);
        }
        return "";
    }
    
    public static String obterPaginaInicial() {
        try {
            return getInstance().obterPaginaInicial();
        } catch (Exception e) {
            Uteis.logar(e, OamdWSConsumer.class);
        }
        return "";
    }
    
    public static void gravarFeedBack(final String dados) {
        try {
            getInstance().gravarFeedBack(dados);
        } catch (Exception e) {
            Uteis.logar(e, OamdWSConsumer.class);
        }
    }

    public static String validarUsuarioOamd(final String user, final String senha) {
        try {
            Uteis.logar(String.format("######################### validarUsuarioOamd %s ############################", user));
            return getInstance().validarUserOamd(user, senha);
        } catch (Exception e) {
            Uteis.logar(e, OamdWSConsumer.class);
        }
        return "erro";
    }
}

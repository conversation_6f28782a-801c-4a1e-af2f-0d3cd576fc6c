/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.integracao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONObject;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.namespace.QName;

import negocio.comuns.nfe.DocumentoFiscalRelatorioPeriodoTO;
import negocio.comuns.nfe.enumerador.ModeloDocumentoFiscal;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.lang.StringUtils;
import servicos.integracao.nfse.IntegracaoNFSeWS;

import servicos.integracao.nfse.IntegracaoNFSeWS_Service;
/**
 *
 * <AUTHOR>
 */
public class IntegracaoNFSeWSConsumer {
    private static IntegracaoNFSeWS servico;
    private static String urlServico;
    
    private static IntegracaoNFSeWS getInstance(String chave) throws Exception {
        String urlServicoParam = DAO.buscarEmpresa(chave).getUrlgestaonotas();

        if (StringUtils.isBlank(urlServicoParam)) {
            urlServicoParam = "http://nfe2-web.pactosolucoes.com.br/nfe";
        }

        if (servico == null || UteisValidacao.emptyString(urlServico) || !urlServico.equals(urlServicoParam)){
            try {
                urlServico = urlServicoParam;
                URL u = new URL( urlServico+"/IntegracaoNFSeWS?wsdl");
                QName qName = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoNFSeWS");
                servico = new IntegracaoNFSeWS_Service(u, qName).getIntegracaoNFSeWSPort();
            } catch (MalformedURLException ex) {
                Logger.getLogger(IntegracaoCadastrosWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return servico;
    }
    
    public static JSONObject consultarNota(String chave, Integer rps) throws Exception{
        String nota = getInstance(chave).consultarNotas(chave, rps);
        return new JSONObject(nota);
    }

    /**
     * Veja em: {@link IntegracaoNFSeWS#consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(String, String, String, List)}.
     */
    public static Map<ModeloDocumentoFiscal, DocumentoFiscalRelatorioPeriodoTO> consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(String chaveNFSe,
                                                                                                                                             String chaveZW,
                                                                                                                                             String cpfCnpj,
                                                                                                                                             List<String> modelosPesquisa) throws Exception {
        String nota = getInstance(chaveZW).consultarQuantidadeNotasNaoAutorizadasOuCanceladasDesdeOntem(chaveNFSe, chaveZW, cpfCnpj, modelosPesquisa);
        return DocumentoFiscalRelatorioPeriodoTO.getMapByModeloFromJSON(new JSONObject(nota));
    }
}

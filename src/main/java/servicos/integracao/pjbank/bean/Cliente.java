package servicos.integracao.pjbank.bean;

import negocio.comuns.arquitetura.SuperVO;

public class Cliente extends SuperVO {
    private String nome;
    private String cpfCnpj;
    private Endereco endereco;
    private int ddd;
    private long telefone;
    private String email;
    private boolean status;

    public Cliente() {
    }

    public Cliente(String nome, String cpfCnpj, Endereco endereco) {
        this.nome = nome;
        this.cpfCnpj = cpfCnpj;
        this.endereco = endereco;
    }

    public Cliente(String nome, String cpfCnpj, Endereco endereco, int ddd, long telefone, String email) {
        this.nome = nome;
        this.cpfCnpj = cpfCnpj;
        this.endereco = endereco;
        this.ddd = ddd;
        this.telefone = telefone;
        this.email = email;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public Endereco getEndereco() {
        return endereco;
    }

    public void setEndereco(Endereco endereco) {
        this.endereco = endereco;
    }

    public int getDdd() {
        return ddd;
    }

    public void setDdd(int ddd) {
        this.ddd = ddd;
    }

    public long getTelefone() {
        return telefone;
    }

    public void setTelefone(long telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean getStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public boolean isAtivo() {
        return status;
    }
}

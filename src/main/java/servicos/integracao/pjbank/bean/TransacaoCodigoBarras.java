package servicos.integracao.pjbank.bean;

import java.util.Date;

public class TransacaoCodigoBarras extends Transacao {
    private String codigoBarras;

    public TransacaoCodigoBarras() {
    }

    public TransacaoCodigoBarras(Date dataPagamento, Date dataVencimento, double valor, String codigoBarras) {
        super(dataPagamento, dataVencimento, valor);
        this.codigoBarras = codigoBarras;
    }

    public String getCodigoBarras() {
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }
}
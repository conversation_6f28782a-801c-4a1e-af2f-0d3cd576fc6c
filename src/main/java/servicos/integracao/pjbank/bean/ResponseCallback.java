package servicos.integracao.pjbank.bean;

import java.util.Map;

public class ResponseCallback {
    private int status;
    private String message;
    private Map<String, Object> data;

    public ResponseCallback() {
    }

    public ResponseCallback(int status, String message, Map<String, Object> data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}

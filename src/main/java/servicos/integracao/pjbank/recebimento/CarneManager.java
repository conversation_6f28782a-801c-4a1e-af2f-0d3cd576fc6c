package servicos.integracao.pjbank.recebimento;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.pjbank.PJBankAuthenticatedService.PJBankAuthenticatedService;
import servicos.integracao.pjbank.api.PJBankClient;
import servicos.integracao.pjbank.beanRecebimento.Carne;
import servicos.integracao.pjbank.exceptions.PJBankException;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class CarneManager extends PJBankAuthenticatedService {
    /**
     * EndPoint a ser requisitado na API
     */
    private String endPoint = "recebimentos/{{credencial}}";
    private ConvenioCobrancaVO convenioCobrancaVO;

    public CarneManager(String credencial, String chave, ConvenioCobrancaVO convenioCobrancaVO) {
        super(credencial, chave);
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.endPoint = this.endPoint.replace("{{credencial}}", credencial);
    }

    /**
     * Gera um carne baseado em uma lista de boletos
     * @param carne a ser emitido
     * @return carne emitido
     */
    public Carne carne(Carne carne) throws IOException, PJBankException {
        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes/lotes"), this.convenioCobrancaVO.getAmbiente());
        HttpPost httpPost = client.getHttpPostClient();
        httpPost.addHeader("x-chave", this.chave);

        JSONObject params = new JSONObject();
        params.put("formato", "carne");
        params.put("pedido_numero", new JSONArray(carne.getPedidoNumero()));

        httpPost.setEntity(new StringEntity(params.toString(), StandardCharsets.UTF_8));

        String response = EntityUtils.toString(client.doRequest(httpPost).getEntity());

        JSONObject responseObject = new JSONObject(response);
        carne.setLinkBoleto(responseObject.getString("linkBoleto"));

        return carne;
    }
}


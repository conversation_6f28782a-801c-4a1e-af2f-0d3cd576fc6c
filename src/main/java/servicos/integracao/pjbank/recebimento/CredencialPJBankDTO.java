package servicos.integracao.pjbank.recebimento;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

public class CredencialPJBankDTO {

    private String cnpj;
    private String tipo;
    private String nome_empresa;
    private String nome_fantasia;
    private String banco;
    private String agencia;
    private String conta;
    private String cep;
    private String endereco;
    private String numero;
    private String bairro;
    private String cidade;
    private String estado;
    private String telefone;
    private String email;
    private String status_conta;
    private String tarifa;

    public CredencialPJBankDTO() {
    }

    public CredencialPJBankDTO(JSONObject json) {
        this.cnpj = json.optString("cnpj");
        this.tipo = json.optString("tipo");
        this.nome_empresa = json.optString("nome_empresa");
        this.nome_fantasia = json.optString("nome_fantasia");
        this.banco = json.optString("banco");
        this.agencia = json.optString("agencia");
        this.conta = json.optString("conta");
        this.cep = json.optString("cep");
        this.endereco = json.optString("endereco");
        this.numero = json.optString("numero");
        this.bairro = json.optString("bairro");
        this.cidade = json.optString("cidade");
        this.estado = json.optString("estado");
        this.telefone = json.optString("telefone");
        this.email = json.optString("email");
        this.status_conta = json.optString("status_conta");
        this.tarifa = json.optString("tarifa");
    }

    public String getCnpjApresentar() {
        return Uteis.formatarCpfCnpj(getCnpj(), false);
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getNome_empresa() {
        return nome_empresa;
    }

    public void setNome_empresa(String nome_empresa) {
        this.nome_empresa = nome_empresa;
    }

    public String getNome_fantasia() {
        return nome_fantasia;
    }

    public void setNome_fantasia(String nome_fantasia) {
        this.nome_fantasia = nome_fantasia;
    }

    public String getBanco() {
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getStatus_conta() {
        return status_conta;
    }

    public void setStatus_conta(String status_conta) {
        this.status_conta = status_conta;
    }

    public String getTarifa() {
        return tarifa;
    }

    public void setTarifa(String tarifa) {
        this.tarifa = tarifa;
    }
}

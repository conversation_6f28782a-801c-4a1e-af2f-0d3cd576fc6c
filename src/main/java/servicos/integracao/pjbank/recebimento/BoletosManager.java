package servicos.integracao.pjbank.recebimento;

import negocio.comuns.financeiro.BoletoPJBankVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.BoletoPJBank;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.pjbank.PJBankAuthenticatedService.PJBankAuthenticatedService;
import servicos.integracao.pjbank.api.PJBankClient;
import servicos.integracao.pjbank.api.PJBankConfig;
import servicos.integracao.pjbank.beanRecebimento.BoletoRecebimento;
import servicos.integracao.pjbank.beanRecebimento.ExtratoBoleto;
import servicos.integracao.pjbank.enums.StatusPagamentoBoleto;
import servicos.integracao.pjbank.exceptions.PJBankException;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

public class BoletosManager extends PJBankAuthenticatedService {
    /**
     * EndPoint a ser requisitado na API
     */
    private String endPoint = "recebimentos/{{credencial}}";
    private ConvenioCobrancaVO convenioCobrancaVO;

    public BoletosManager(String credencial, String chave, ConvenioCobrancaVO convenioCobrancaVO) {
        super(credencial, chave);
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.endPoint = this.endPoint.replace("{{credencial}}", credencial);
    }

    /**
     * Realiza a emissão do boleto bancario para o cliente informado
     * @param boletoRecebimento: boleto à  ser emitido
     * @return BoletoRecebimento
     */
    public BoletoRecebimento create(BoletoRecebimento boletoRecebimento, BoletoPJBank boletoPJBankDAO) throws IOException, PJBankException {
        String response = "";
        try {

            PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes"), this.convenioCobrancaVO.getAmbiente());
            HttpPost httpPost = client.getHttpPostClient();
            DateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");

            JSONObject params = new JSONObject();

            params.put("vencimento", dateFormat.format(boletoRecebimento.getVencimento()));
            params.put("valor", boletoRecebimento.getValor());
            params.put("juros", boletoRecebimento.getJuros());
            params.put("juros_fixo", boletoRecebimento.getJurosFixo());
            params.put("multa", boletoRecebimento.getMulta());
            params.put("desconto", boletoRecebimento.getDesconto());
            params.put("diasdesconto1", boletoRecebimento.getDiaDesconto());
            params.put("nome_cliente", boletoRecebimento.getCliente().getNome());
            params.put("cpf_cliente", boletoRecebimento.getCliente().getCpfCnpj());
            params.put("endereco_cliente", boletoRecebimento.getCliente().getEndereco().getLogradouro());
            params.put("numero_cliente", boletoRecebimento.getCliente().getEndereco().getNumero());
            params.put("complemento_cliente", boletoRecebimento.getCliente().getEndereco().getComplemento());
            params.put("bairro_cliente", boletoRecebimento.getCliente().getEndereco().getBairro());
            params.put("cidade_cliente", boletoRecebimento.getCliente().getEndereco().getCidade());
            params.put("estado_cliente", boletoRecebimento.getCliente().getEndereco().getEstado());
            params.put("cep_cliente", boletoRecebimento.getCliente().getEndereco().getCep());
            params.put("logo_url", boletoRecebimento.getLogoUrl());
            params.put("texto", boletoRecebimento.getTexto());
            params.put("instrucoes", boletoRecebimento.getInstrucoes());
            params.put("instrucao_adicional", boletoRecebimento.getInstrucao_adicional());
            params.put("grupo", boletoRecebimento.getGrupo());
            params.put("pedido_numero", boletoRecebimento.getPedidoNumero());
            params.put("webhook", boletoRecebimento.getWebhook());

            //Opcional. Caso não queira a atualização do vencimento do boleto de forma automática, utilizar este parâmetro, informando o valor 1. length (0-1).
            params.put("nunca_atualizar_boleto", 1);

            httpPost.setEntity(new StringEntity(params.toString(), StandardCharsets.UTF_8));

            response = EntityUtils.toString(client.doRequest(httpPost).getEntity());

            JSONObject responseObject = new JSONObject(response);
            boletoRecebimento.setIdUnico(responseObject.getString("id_unico"));
            boletoRecebimento.setNossoNumero(responseObject.optString("nossonumero"));
            boletoRecebimento.setLinkBoleto(responseObject.getString("linkBoleto"));
            boletoRecebimento.setLinkGrupo(responseObject.getString("linkGrupo"));
            boletoRecebimento.setLinhaDigitavel(responseObject.getString("linhaDigitavel"));
            boletoRecebimento.setPedidoNumeroPJBank(responseObject.optString("pedido_numero"));
            boletoRecebimento.setBanco(responseObject.optString("banco_numero"));

            boletoRecebimento.setChavePJBank(getChave());
            boletoRecebimento.setCredencialPJBank(responseObject.optString("credencial"));
            if (UteisValidacao.emptyString(boletoRecebimento.getCredencialPJBank())) {
                boletoRecebimento.setCredencialPJBank(getCredencial());
            }
            return boletoRecebimento;
        } catch (Exception ex) {
            response = ex.getMessage();
            ex.printStackTrace();
            throw ex;
        } finally {
            try {
                boletoPJBankDAO.incluirHistoricoRetornoPJBank(0, "CRIAR_BOLETO_PARCELA_" + boletoRecebimento.getNumParcela(), response);
            } catch (Exception ignored){
            }
        }
    }

    /**
     * Retorna a lista de boletos emitidos por códigos de pedidos
     * @param pedidos: Lista de códigos de pedidos os quais deseja retornar os boletos
     * @return String: Link contendo os boletos relacionados aos códigos de pedidos enviados
     */
    public String getByIds(Set<String> pedidos) throws IOException, PJBankException {
        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes/lotes"), this.convenioCobrancaVO.getAmbiente());
        HttpPost httpPost = client.getHttpPostClient();
        httpPost.addHeader("x-chave", this.getChave());
        JSONArray pedidosArray = new JSONArray(pedidos);

        JSONObject params = new JSONObject();
        params.put("pedido_numero", pedidosArray);
        params.put("formato","carne");
        params.put("nunca_atualizar_boleto", 1);

        httpPost.setEntity(new StringEntity(params.toString(), StandardCharsets.UTF_8));

        String response = EntityUtils.toString(client.doRequest(httpPost).getEntity());
        JSONObject responseObject = new JSONObject(response);

        return responseObject.getString("linkBoleto");
    }

    public List<ExtratoBoleto> get(Date inicio, Date fim, StatusPagamentoBoleto pago) throws URISyntaxException, IOException, PJBankException, java.text.ParseException {
        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes"), this.convenioCobrancaVO.getAmbiente());
        HttpGet httpGet = client.getHttpGetClient();
        httpGet.addHeader("x-chave", this.getChave());
        this.adicionarFiltros(httpGet, inicio, fim, pago);

        String response = EntityUtils.toString(client.doRequest(httpGet).getEntity());
        JSONArray extratoObject = new JSONArray(response);
        int totalItensExtrato = extratoObject.length();
        List<ExtratoBoleto> extratos = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");
        for (int i = 0; i < totalItensExtrato; i++) {
            JSONObject itemExtrato = extratoObject.getJSONObject(i);
            ExtratoBoleto extrato = new ExtratoBoleto(
                    itemExtrato.getDouble("valor"),
                    itemExtrato.optDouble("valor_pago", 0.0),
                    itemExtrato.getDouble("valor_liquido"),
                    itemExtrato.optDouble("valor_tarifa", 0.0),
                    itemExtrato.getString("nosso_numero"),
                    itemExtrato.getString("nosso_numero_original"),
                    itemExtrato.getString("banco_numero"),
                    itemExtrato.getString("token_facilitador")
            );

            String dataVencimento = itemExtrato.getString("data_vencimento");
            if (!StringUtils.isBlank(dataVencimento))
                extrato.setDataVencimento(dateFormat.parse(dataVencimento));

            String dataPagamento = itemExtrato.getString("data_pagamento");
            if (!StringUtils.isBlank(dataPagamento))
                extrato.setDataPagamento(dateFormat.parse(dataPagamento));

            String dataCredito = itemExtrato.getString("data_credito");
            if (!StringUtils.isBlank(dataCredito))
                extrato.setDataCredito(dateFormat.parse(dataCredito));

            extratos.add(extrato);
        }
        return extratos;
    }

    private void adicionarFiltros(HttpRequestBase httpRequestClient, Date inicio, Date fim, StatusPagamentoBoleto pago)
            throws URISyntaxException {
        SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy");
        URIBuilder uriBuilder = new URIBuilder(httpRequestClient.getURI());

        uriBuilder.addParameter("data_inicio", formatter.format(inicio));
        uriBuilder.addParameter("data_fim", formatter.format(fim));
        uriBuilder.addParameter("pago", pago.getName());

        httpRequestClient.setURI(uriBuilder.build());
    }

    public String get(BoletoPJBankVO obj) throws IOException, PJBankException {
        return get(obj.getIdUnico());
    }

    public String get(BoletoVO obj) throws IOException, PJBankException {
        return get(obj.getIdExterno());
    }

    public String get(String idUnico) throws IOException, PJBankException {
        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes").concat("/").concat(idUnico), this.convenioCobrancaVO.getAmbiente());
        HttpGet httpGet = client.getHttpGetClient();
        httpGet.addHeader("x-chave", this.getChave());
        return EntityUtils.toString(client.doRequest(httpGet).getEntity());
    }

    public String getInfoCredencial() throws IOException {
        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpGet httpGet = new HttpGet(PJBankConfig.getApiBaseUrl(this.convenioCobrancaVO.getAmbiente()).concat("recebimentos/").concat(getCredencial()));
        httpGet.addHeader("Accept", PJBankConfig.accept);
        httpGet.addHeader("Content-Type", PJBankConfig.contentType);
        httpGet.addHeader("Source", PJBankConfig.source);
        httpGet.addHeader("x-chave", this.getChave());
        HttpResponse httpResponse = httpClient.execute(httpGet);
        return EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
    }

    public String getBoletos(boolean pagos, Date dataInicio, Date dataFinal, Integer pagina) throws IOException, PJBankException {
//                           parametro pagina == Inteiro, começando por 1. Retorna 50 itens por página. Recomendado buscar a próxima página quando o sistema retornar 50 itens na página atual.

        String inicio = Calendario.getDataAplicandoFormatacao(dataInicio, "MM/dd/yyyy");
        String fim  = Calendario.getDataAplicandoFormatacao(dataFinal, "MM/dd/yyyy");

        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes?data_inicio=" + inicio + "&data_fim=" + fim +
                (pagos ? "&pago=1" : "") +
                (!UteisValidacao.emptyNumber(pagina) ? ("&pagina=" + pagina) : "")), //começando por 1. Retorna 50 itens por página.
                this.convenioCobrancaVO.getAmbiente());
        HttpGet httpGet = client.getHttpGetClient();
        httpGet.addHeader("x-chave", this.getChave());
        return EntityUtils.toString(client.doRequest(httpGet).getEntity());
    }

    public String cancelar(BoletoVO obj) throws IOException, PJBankException {
        return cancelar(obj.getNumeroInterno());
    }

    public String cancelar(BoletoPJBankVO obj) throws IOException, PJBankException {
        return cancelar(obj.getPedidoNumero().toString());
    }

    public String cancelar(String pedidoNumero) throws IOException, PJBankException {
        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes").concat("/").concat(pedidoNumero), this.convenioCobrancaVO.getAmbiente());
        HttpDelete httpDelete = client.getHttpDeleteClient();
        httpDelete.addHeader("x-chave", this.getChave());
        return EntityUtils.toString(client.doRequest(httpDelete).getEntity());
    }
}


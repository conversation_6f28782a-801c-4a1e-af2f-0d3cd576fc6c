package servicos.integracao.pjbank.api;

import negocio.comuns.financeiro.enumerador.AmbienteEnum;

public class PJBankConfig {
    /**
     * URL base da API de produção
     */
    private final static String apiBaseUrlProducao = "https://api.pjbank.com.br/";

    /**
     * URL base da API de testes
     */
    private final static String apiBaseUrlSandbox = "https://sandbox.pjbank.com.br/";

    /**
     * versão da API a ser consumida
     */
    public final static String version = "v3";
    public final static String accept = "application/json";
    public final static String contentType = "application/json";
    public final static String source = "java-sdk";

    /**
     * Retorna a URL da API baseada no valor da variavel de JVM "pjbank-env". Caso não haja valor definido, será usado o
     * ambiente dev/sandbox por padrão.
     * @return String
     */
    public static String getApiBaseUrl(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.PRODUCAO)) {
            return PJBankConfig.apiBaseUrlProducao; // URL AMBIENTE PRODUÇÃO!!!
        } else if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
            return PJBankConfig.apiBaseUrlSandbox; // url para testes.
        } else {
            return "";
        }
    }

    public static boolean isDebugMode() {
        return "true".equals(System.getProperty("pjbank-debug"));
    }
}

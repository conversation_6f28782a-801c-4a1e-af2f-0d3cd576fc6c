package servicos.integracao.pjbank.beanRecebimento;

import servicos.integracao.pjbank.bean.Boleto;
import servicos.integracao.pjbank.bean.Cliente;

import java.util.Date;

public class BoletoRecebimento extends Boleto {
    /**
     * Dados para requisição
     */

    /**
     * Cliente para o qual o boleto será gerado
     */
    private Cliente cliente;
    private double valor;
    private double juros;
    private double jurosFixo;
    private double multa;
    private double multaFixo;
    private double desconto;
    private Integer diaDesconto;
    private Date vencimento;
    private String logoUrl;
    private String texto;
    private String instrucoes;
    private String instrucao_adicional;
    private String grupo;
    private Integer pedidoNumero;
    private String webhook;
    private Integer numParcela;
    private String pedidoNumeroPJBank;
    private String nossoNumero;
    private String chavePJBank;
    private String credencialPJBank;

    /**
     * Dados para resposta
     */
    private String id;
    private String banco;
    private String tokenFacilitador;
    private String linkGrupo;

    public BoletoRecebimento() {
    }

    public Cliente getCliente() {
        return cliente;
    }

    public void setCliente(Cliente cliente) {
        this.cliente = cliente;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public double getJuros() {
        return juros;
    }

    public void setJuros(double juros) {
        this.juros = juros;
    }

    public double getMulta() {
        return multa;
    }

    public void setMulta(double multa) {
        this.multa = multa;
    }

    public double getDesconto() {
        return desconto;
    }

    public void setDesconto(double desconto) {
        this.desconto = desconto;
    }

    public Date getVencimento() {
        return vencimento;
    }

    public void setVencimento(Date vencimento) {
        this.vencimento = vencimento;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public String getGrupo() {
        return grupo;
    }

    public void setGrupo(String grupo) {
        this.grupo = grupo;
    }

    public Integer getPedidoNumero() {
        return pedidoNumero;
    }

    public void setPedidoNumero(Integer pedidoNumero) {
        this.pedidoNumero = pedidoNumero;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBanco() {
        if (banco == null) {
            banco = "";
        }
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getTokenFacilitador() {
        if (tokenFacilitador == null) {
            tokenFacilitador = "";
        }
        return tokenFacilitador;
    }

    public void setTokenFacilitador(String tokenFacilitador) {
        this.tokenFacilitador = tokenFacilitador;
    }

    public String getLinkGrupo() {
        return linkGrupo;
    }

    public void setLinkGrupo(String linkGrupo) {
        this.linkGrupo = linkGrupo;
    }

    public String getWebhook() {
        return webhook;
    }

    public void setWebhook(String webhook) {
        this.webhook = webhook;
    }

    public Integer getNumParcela() {
        return numParcela;
    }

    public void setNumParcela(Integer numParcela) {
        this.numParcela = numParcela;
    }

    public String getPedidoNumeroPJBank() {
        if (pedidoNumeroPJBank == null) {
            pedidoNumeroPJBank = "";
        }
        return pedidoNumeroPJBank;
    }

    public void setPedidoNumeroPJBank(String pedidoNumeroPJBank) {
        this.pedidoNumeroPJBank = pedidoNumeroPJBank;
    }

    public String getNossoNumero() {
        if (nossoNumero == null) {
            nossoNumero = "";
        }
        return nossoNumero;
    }

    public void setNossoNumero(String nossoNumero) {
        this.nossoNumero = nossoNumero;
    }

    public String getChavePJBank() {
        if (chavePJBank == null) {
            chavePJBank = "";
        }
        return chavePJBank;
    }

    public void setChavePJBank(String chavePJBank) {
        this.chavePJBank = chavePJBank;
    }

    public String getCredencialPJBank() {
        if (credencialPJBank == null) {
            credencialPJBank = "";
        }
        return credencialPJBank;
    }

    public void setCredencialPJBank(String credencialPJBank) {
        this.credencialPJBank = credencialPJBank;
    }

    public String getInstrucoes() {
        return instrucoes;
    }

    public void setInstrucoes(String instrucoes) {
        this.instrucoes = instrucoes;
    }

    public String getInstrucao_adicional() {
        return instrucao_adicional;
    }

    public void setInstrucao_adicional(String instrucao_adicional) {
        this.instrucao_adicional = instrucao_adicional;
    }

    public double getJurosFixo() {
        return jurosFixo;
    }

    public void setJurosFixo(double jurosFixo) {
        this.jurosFixo = jurosFixo;
    }

    public double getMultaFixo() {
        return multaFixo;
    }

    public void setMultaFixo(double multaFixo) {
        this.multaFixo = multaFixo;
    }

    public Integer getDiaDesconto() {
        return diaDesconto;
    }

    public void setDiaDesconto(Integer diaDesconto) {
        this.diaDesconto = diaDesconto;
    }
}

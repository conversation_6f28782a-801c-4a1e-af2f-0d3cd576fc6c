package servicos.integracao.pjbank.enums;

public enum FormatoExtrato {
    JSON("json");

    private String name;

    FormatoExtrato(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public static FormatoExtrato fromString(String name) {
        for (FormatoExtrato obj : FormatoExtrato.values()) {
            if (obj.name.equalsIgnoreCase(name)) {
                return obj;
            }
        }
        return null;
    }
}


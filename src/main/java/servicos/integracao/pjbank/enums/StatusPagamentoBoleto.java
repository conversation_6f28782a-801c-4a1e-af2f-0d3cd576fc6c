package servicos.integracao.pjbank.enums;

public enum StatusPagamentoBoleto {
    PAGO("1"),
    ABERTO("0");

    private String name;

    StatusPagamentoBoleto(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public static StatusPagamentoBoleto fromString(String name) {
        for (StatusPagamentoBoleto obj : StatusPagamentoBoleto.values()) {
            if (obj.name.equalsIgnoreCase(name)) {
                return obj;
            }
        }
        return null;
    }
}
package servicos.http;

import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 29/04/2020
 */
public class RespostaHttpDTO {

    private Integer httpStatus;
    private String response;
    private Map<String, String> headers;
    private String requestBody;

    public Integer getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(Integer httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }
}

package servicos.interfaces;

import controle.financeiro.SimulacaoSaldoVO;
import org.json.JSONArray;
import negocio.comuns.financeiro.FluxoCaixaTO;

import java.util.Date;
import java.util.List;

/**
 * Created by alcides on 06/11/2017.
 */
public interface FluxoCaixaServiceInterface {

    List<FluxoCaixaTO> processarFluxoCaixa(Integer empresa,
                                                  Date inicio,
                                                  Date fim,
                                                  boolean previsto,
                                                  Double saldoInicial,
                                                  FluxoCaixaTO total,
                                                  String contas) throws Exception;

    List<FluxoCaixaTO> processarFluxoCaixa(Integer empresa,
                                                  Date inicio,
                                                  Date fim,
                                                  boolean previsto,
                                                  Double saldoInicial,
                                                  FluxoCaixaTO total,
                                                  String contas,
                                                  boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao,
                                           boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) throws Exception;

    Double obterSaldoInicial(Integer empresa, Date dia, boolean previsto) throws Exception;

    void aplicarSaldo(List<FluxoCaixaTO> lista, Double saldoInicial, FluxoCaixaTO total, String diaIniciar);

    void aplicarSaldo(List<FluxoCaixaTO> lista, Double saldoInicial, FluxoCaixaTO total, String diaIniciar, Boolean isSaldoInicial);

    void incluirSimulacaoSaldo(SimulacaoSaldoVO obj) throws Exception;

    SimulacaoSaldoVO buscarSimulacaoSaldo(Integer ano, Integer mes) throws Exception;

    void exluirSimulacaoSaldo(Integer ano, Integer mes) throws Exception;

    JSONArray montarGrafico(List<FluxoCaixaTO> previsto, List<FluxoCaixaTO> realizado) throws Exception;
}

package servicos.interfaces.cliente;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.util.List;

public interface VendaRapidaRecorrenteService extends SuperInterface {

    void gravar(TelefoneVO telefoneCelularVO,
                       TelefoneVO telefoneComercialVO,
                       TelefoneVO telefoneResidencialVO,
                       EnderecoVO enderecoResidencialVO,
                       EnderecoVO enderecoComercialVO,
                       ClienteVO clienteVO,
                       PessoaVO pessoaVO,
                       EmailVO emailVO,
                       UsuarioVO usuarioVO,
                       EmpresaVO empresaVO,
                       ConfiguracaoSistemaVO configuracaoSistemaVO,
                       boolean pessoaEstrangeira,
                       boolean apenasValidar) throws Exception;

    Integer addAlunoInadimplente(JSONObject json, Integer empresa) throws Exception;

    void adicionarConsultor(ClienteVO cliente, ColaboradorVO consultor) throws Exception;

    void gravarAutorizacao(AutorizacaoCobrancaClienteVO autorizacao) throws Exception;

    List<ConvenioCobrancaVO> convenios(Integer empresa, TipoAutorizacaoCobrancaEnum tipo,
                                              TipoConvenioCobrancaEnum tipoConvenio) throws Exception;

    void alterar(TelefoneVO telefoneCelularVO,
                        TelefoneVO telefoneComercialVO,
                        TelefoneVO telefoneResidencialVO,
                        EnderecoVO enderecoResidencialVO,
                        ClienteVO clienteVO,
                        PessoaVO pessoaVO,
                        EmailVO emailVO,
                        UsuarioVO usuarioVO,
                        EmpresaVO empresaVO,
                        ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception;

    void gerarParcelaInadimplente(ClienteVO cliente, Double valor, UsuarioVO usuario, String idExternoTitulo, boolean adicionarRemessa, String idExternoFilial, String cpf) throws Exception;


    /**
     * Grava parcelas e controle de taxxa personal para planos personal
     *
     * @param plano             Plano escolhico
     * @param diaVencimento     Dia do vencimento das parcelas
     * @param personal          Colaborador do tipo personal
     * @param empresa           Empresa onde será lançada as parcelas
     * @param usuario           Usuário responsábel pelo lançamento
     * @throws Exception
     */
    void incluirPlanoPersonal(PlanoVO plano,
                              int diaVencimento,
                              int numeroVezesParcelarAdesao,
                              ColaboradorVO personal,
                              EmpresaVO empresa,
                              UsuarioVO usuario) throws Exception;

    /**
     * Grava um novo colaborador na base de dados com tipo Personal Trainer
     *
     * @param colaboradorPersonalSelecionado
     * @param telefoneCelularVO
     * @param telefoneComercialVO
     * @param telefoneResidencialVO
     * @param enderecoResidencialVO
     * @param emailVO
     * @param empresa
     */
    void incluirColaboradorPersonal(ColaboradorVO colaboradorPersonalSelecionado,
                                    TelefoneVO telefoneCelularVO,
                                    TelefoneVO telefoneComercialVO,
                                    TelefoneVO telefoneResidencialVO,
                                    EnderecoVO enderecoResidencialVO,
                                    EmailVO emailVO,
                                    EmpresaVO empresa) throws Exception;

    /**
     * Altera os dados de um colaborador personal na base de dados
     *
     * @param colaborador
     * @param telefoneCelular
     * @param telefoneComercial
     * @param telefoneResidencial
     * @param endereco
     * @param email
     * @param empresa
     */
    void alterarColaboradorPersonal(ColaboradorVO colaborador,
                                    TelefoneVO telefoneCelular,
                                    TelefoneVO telefoneComercial,
                                    TelefoneVO telefoneResidencial,
                                    EnderecoVO endereco,
                                    EmailVO email,
                                    EmpresaVO empresa) throws Exception;

    /**
     * Grava uma nova autorização de cobrança de colaborador na base de dados
     *
     * @param autorizacaoColaborador
     */
    void incluirAutorizacaoCobrancaColaborador(AutorizacaoCobrancaColaboradorVO autorizacaoColaborador) throws Exception;

    /**
     * Altera os dados de uma autorização de cobrança de um colaborador na base de dados
     *
     * @param autorizacaoColaborador
     */
    void alterarAutorizacaoCobrancaColaborador(AutorizacaoCobrancaColaboradorVO autorizacaoColaborador) throws Exception;
}

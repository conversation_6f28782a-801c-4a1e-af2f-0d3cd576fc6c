package servicos.interfaces.devolucaocheque;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.*;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

public interface DevolucaoChequeService extends SuperInterface {

    public boolean processarMovimentacaoCheques(String chave,
                                                String urlNotificar,
                                                Integer empresa,
                                                List<ChequeVO> cheques,
                                                MovContaVO conta,
                                                ConfiguracaoFinanceiroVO cfg,
                                                int caixa,
                                                UsuarioVO usuario, ContaVO origem) throws Exception;

    public void voltarChequeEstadoNatural(String chave,
                                          String urlNotificar,
                                          Integer empresa,
                                          List<ChequeVO> cheques,
                                          ConfiguracaoFinanceiroVO cfg,
                                          UsuarioVO usuario, Integer contaOrigem) throws Exception;

    public void removerChequeContaDevolucao(List<MovParcelaVO> parcelas) throws Exception;

}

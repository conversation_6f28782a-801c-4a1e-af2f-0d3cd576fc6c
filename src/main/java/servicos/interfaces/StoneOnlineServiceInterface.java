package servicos.interfaces;

import negocio.comuns.financeiro.TransacaoVO;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public interface StoneOnlineServiceInterface extends AprovacaoServiceInterface {

    String AUTHORIZE_ENDPOINT = "/Authorize";
    String CANCELLATION_ENDPOINT = "/Cancellation";
    String STATUS_REPORT_ENDPOINT = "/TransactionStatusReport";
    String APPLICATION_XML = "application/xml";

}

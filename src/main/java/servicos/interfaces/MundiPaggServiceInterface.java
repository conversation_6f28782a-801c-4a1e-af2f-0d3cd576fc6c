package servicos.interfaces;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.TransacaoVO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/05/2020
 */
public interface MundiPaggServiceInterface extends AprovacaoServiceInterface {

    void incluirPessoa(PessoaVO pessoa) throws  Exception;

    String consultarTransacao(String codigoExterno) throws Exception;
}

package servicos.interfaces;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.TransacaoVO;

public interface CeopagServiceInterface extends AprovacaoServiceInterface{

    static final String METODO_CUSTOMERS = "customers";

    /**
     * Realiza a inclusão de um determinado cliente na pagolivre.
     * @param cliente
     * @throws Exception
     */
    void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws  Exception;

    void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO, ClienteVO clienteVO) throws Exception;

    /**
     * Realiza a consulta de uma transação a partir de seu id.
     */
    String consultarTransacao(Integer id) throws Exception;
}

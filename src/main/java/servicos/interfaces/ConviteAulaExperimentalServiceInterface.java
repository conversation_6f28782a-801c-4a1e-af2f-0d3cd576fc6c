package servicos.interfaces;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalMobileTO;

import java.util.Date;

/**
 * Created by ulisses on 25/01/2016.
 */
public interface ConviteAulaExperimentalServiceInterface {

    void gerarConviteViaWS(String key,
                          OrigemSistemaEnum origemSistemaEnum,
                          Integer codigoEmpresa,
                          Integer codigoClienteConvidou,
                          Integer codigoTipoConvite,
                          String nomeConvidado,
                          String telefoneConvidado,
                          String emailConvidado)throws Exception;
    public void gerarConviteViaWeb(OrigemSistemaEnum origemSistemaEnum,
                                   Integer codigoUsuarioConvidou,
                                   Integer codigoTipoConvite,
                                   Integer codigoIndicadoConvidado,
                                   Integer codigoPassivoConvidado,
                                   Integer codigoClienteConvidado,
                                   Integer codigoColaboradorReponsavelConvite)throws Exception;
    ConviteAulaExperimentalVO validarConvite(Integer codigoConvite)throws Exception;
    void excluirConvite(Integer codigoConvite, Integer codigoCliente)throws Exception;
    void excluir(Integer codigo)throws Exception;
    TipoConviteAulaExperimentalMobileTO consultarTipoConvite(ConviteAulaExperimentalVO conviteAulaExperimentalVO)throws Exception;
    ClienteVO consultarClienteIndicou(Integer codigoIndicado, Integer codigoColaboradorResponsavel, int nivelMontarDados)throws Exception;
    ConviteAulaExperimentalVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception;
    void alterarDadosCadastraisConvidado(Integer codigoConvite,String nome, String telefoneRes, String telefoneCelular, String email)throws Exception;
    void agendarAulaExperimental(Integer codigoConvite, Date dataAula, Integer codigoHorarioTurma)throws Exception;
    void desmarcarAulaExperimental(Integer codigoConvite, Date dataAula, Integer codigoHorarioTurma)throws Exception;
    void prepararConexao() throws Exception;

}

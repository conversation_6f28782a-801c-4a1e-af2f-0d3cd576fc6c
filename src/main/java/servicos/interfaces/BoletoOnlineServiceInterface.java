/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.interfaces;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.BoletoVO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/12/2021
 */
public interface BoletoOnlineServiceInterface {

    BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception;

    void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception;

    void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception;

    void processarWebhook(BoletoVO boletoVO, String dados) throws Exception;

}

package servicos.interfaces;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.TransacaoVO;

/**
 * Cria os métodos extras que a api da MaxiPago possui
 * Created by <PERSON><PERSON> on 17/02/2017.
 */
public interface MaxiPagoServiceInterface extends AprovacaoServiceInterface {

    void incluirPessoa(PessoaVO pessoa) throws  Exception;

    void consultarSituacaoCobrancaTransacao(TransacaoVO transacao) throws Exception;

}

package servicos.interfaces.oamd;

import br.com.pactosolucoes.enumeradores.TipoConsultaCupomDescontoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteValidacaoWS;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoWS;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.CupomDescontoVO;
import negocio.oamd.CupomDescontoWS;
import negocio.oamd.EmpresaFinanceiroVO;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;
import negocio.oamd.LoginSiteRedeEmpresaVO;
import negocio.oamd.RedeEmpresaVO;
import servicos.integracao.oamd.to.EmpresaFinanceiroOAMDTO;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 26/05/2016.
 */
public interface OAMDServiceInterface {

    void incluirLoginSiteRedeEmpresa(String email, Integer idEmpresaFinanceiro, String cpf) throws Exception;

    LoginSiteRedeEmpresaVO consultarChaveDeEmailDaRede(String chaveRede, String email, boolean buscarPorCPF) throws Exception;

    CampanhaCupomDescontoVO incluirCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, String chaveZW, LogInterfaceFacade logFacade) throws Exception;

    CampanhaCupomDescontoVO alterarCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, String chaveZW, LogInterfaceFacade logFacade) throws Exception;

    List consultarCampanhaCupomDescontoParaImpressao(String filtro, String ordem, String campoOrdenacao, String chaveZW) throws Exception;

    RedeEmpresaVO consultarRedeEmpresaPorChaveZW(String chaveZW) throws Exception;

    CampanhaCupomDescontoVO consultarCampanhaCupomDescontoPorId(Integer id, int nivelMontarDados) throws Exception;

    String consultarCampanhaCupomDescontoJSON(RedeEmpresaVO redeEmpresaVO, Integer idFavorecido) throws Exception;

    void excluirCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO) throws Exception;

    List consultarPorNomeCodigoEntidadeAgrupado(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados, boolean agruparSegundo) throws Exception;

    CampanhaCupomDescontoVO gerarNovoLoteCupomDesconto(UsuarioVO usuarioVO, CampanhaCupomDescontoVO campanhaCupomDescontoVO, Integer totalCupomGerar, String observacaoLoteCupom, String nomeCupomEspecifico, String chaveZW, LogInterfaceFacade logFacade) throws Exception;

    String gerarNovoCupomDescontoIndicacao(CampanhaCupomDescontoVO campanhaCupomDescontoVO, String chaveZW, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception;

    CupomDescontoVO consultarPorNumeroCupom(String numeroCupom, Integer codigoFavorecido) throws Exception;

    CampanhaCupomDescontoVO concederPremioCupomDescontoAoAluno(CampanhaCupomDescontoVO campanhaCupomDescontoVO, Date dataBaseVencimentoMatricula, List<CupomDescontoVO> listaCupomDesconto, UsuarioVO usuarioVO, Integer codigoFavorecido) throws Exception;

    void concederPremioCupomPortadorCupom(Connection conZillyon, String chaveZW, ContratoVO contratoVO, String numeroCupom, Integer codigoCliente, String nomeCliente, Double valorPremioProdutos) throws Exception;

    void registrarLogProcessamentoCupomDescontoManualmente(UsuarioVO usuarioVO, Integer idCampanhaCupomDesconto, Date dataBaseVencimentoMatricula, String resultadoProcessamento) throws Exception;

    boolean existeCampanhaCupomDesconto(Integer codigoFavorecido, boolean somenteVigente, String descricaoPlano) throws Exception;

    CupomDescontoVO validarConcederPremioCupomPortadorCupom(Connection conZillyon, Integer codigoFavorecido, String numeroCupom, ContratoVO contratoPremioPortadorCupom) throws Exception;

    List<CupomDescontoWS> consultarCupomDesconto(String key, String listaCupom) throws Exception;

    ClienteValidacaoWS consultarClienteNaRedeEmpresa(String chaveZW, String cpf) throws Exception;

    ContratoVO transferirAlunoDeUnidade(int codigoEmpresaFinanceiro, String chaveZWDestino, int codigoCliente, int codigoPlano, String email) throws Exception;

    Integer consultarTotalCupomDescontoJaUtilizado(Integer idCampanhaCupomDesconto, String chaveZW) throws Exception;

    PlanoWS consultarEquivalenciaPlanoUnidadeDestino(int codigoPlanoOrigem, int codigoEmpresaFinanceiroOrigem, String chaveDestino, Connection conDestino) throws Exception;

    List<EmpresaFinanceiroVO> consultarUnidadesDaRede(String chaveZW) throws Exception;

    List<HistoricoUtilizacaoCupomDescontoVO> consultarCupons(CampanhaCupomDescontoVO campanhaCupomDescontoVO, String chaveZWFiltro, Integer empresaZWFiltro, TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum, String numeroCupom) throws Exception;

    void povoarLoginSiteRedeEmpresa(String chaveRede) throws Exception;

    EmpresaFinanceiroVO consultarEmpresaFinanceiro(String chaveZW) throws Exception;

    void cancelarUtilizacaoCupomDesconto(String numeroCupomDesconto, String chaveZW) throws Exception;

    void informarContratoEstornadoHistoricoUtilizacaoCupom(int codContrato, String chaveZW) throws Exception;

    List<EmpresaFinanceiroOAMDTO> consultarUnidadesDaRedeOAMD(RedeEmpresaVO redeEmpresaVO) throws Exception;
}

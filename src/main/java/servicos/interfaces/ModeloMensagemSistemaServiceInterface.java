package servicos.interfaces;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.IdentificadorMensagemSistema;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by johny<PERSON> on 13/01/2017.
 */
public interface ModeloMensagemSistemaServiceInterface {

    /**
     * Realiza o envio de um email a partir do {@link negocio.comuns.basico.ModeloMensagemSistemaVO} consultado pelo seu {@link IdentificadorMensagemSistema}
     * @param identificador {@link IdentificadorMensagemSistema}
     * @param parametros Parâmetros que serão substituídos dentro da mensagem e do titulo do {@link negocio.comuns.basico.ModeloMensagemSistemaVO}
     * @throws Exception
     */
    void enviarEmail(IdentificadorMensagemSistema identificador, Map<String, String> parametros, UsuarioVO remetente, Collection<String> destinatarios) throws  Exception;

    /**
     * Realiza o envio de um email a partir do {@link negocio.comuns.basico.ModeloMensagemSistemaVO} consultado pelo seu {@link IdentificadorMensagemSistema}
     * @param identificador {@link IdentificadorMensagemSistema}
     * @param parametros Parâmetros que serão substituídos dentro da mensagem e do titulo do {@link negocio.comuns.basico.ModeloMensagemSistemaVO}
     * @param configuracao {@link ConfiguracaoSistemaCRMVO} que será utilizada para enviar o email.
     * @throws Exception
     */
    void enviarEmail(IdentificadorMensagemSistema identificador, Map<String, String> parametros, UsuarioVO remetente, Collection<String> destinatarios, ConfiguracaoSistemaCRMVO configuracao) throws  Exception;

    void enviarEmailRecorrrencia(IdentificadorMensagemSistema identificador, EmpresaVO empresaVO, List<String> listaRetornos, List<String> listaEmails, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) throws Exception;
}

package servicos.interfaces;

import negocio.comuns.financeiro.TransacaoVO;

/**
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
public interface GetNetECommerceServiceInterface extends AprovacaoServiceInterface {


    static final String METODO_TOKEN_API = "/auth/oauth/v2/token";
    static final String METODO_CUSTOMERS = "/v1/customers";
    static final String METODO_TOKEN_CARD = "/v1/tokens/card";
    static final String METODO_PAYMENTS = "/v1/payments/credit";
    static final String METODO_CARD_VERIFICATION = "/v1/cards/verification";
    static final String METODO_CANCEL = "/v1/payments/cancel/request";
    static final String METODO_CANCEL_DIA = "/v1/payments/credit/%s/cancel";
    static final String METODO_CONSULTA_CANCEL = "/v1/payments/cancel/request";

    String consultarTransacao(Integer id) throws Exception;

}

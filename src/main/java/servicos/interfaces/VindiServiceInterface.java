package servicos.interfaces;

import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.TransacaoVO;

/**
 * Cria os métodos extras que a api da vindi possui
 * Created by johny<PERSON> on 17/02/2017.
 */
public interface VindiServiceInterface extends AprovacaoServiceInterface {

    static final String METODO_CUSTOMERS = "customers";

    /**
     * Realiza a inclusão de um determinado cliente na vindi.
     * @param cliente
     * @throws Exception
     */
    void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws  Exception;

    /**
     * Realiza a consulta de uma transação a partir de seu id.
     */
    String consultarTransacao(Integer id) throws Exception;

}

package servicos.interfaces.turma;

import negocio.comuns.plano.HistoricoProfessorTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface HistoricoProfessorTurmaService extends SuperInterface {

    public void inserirHistoricoMudancaProfessor(HorarioTurmaVO horarioTurma, Integer professor<PERSON>tual, Date iniciovigencia) throws Exception;


    public Map<Integer, List<HistoricoProfessorTurmaVO>> montarHistoricoProfessor(Date inicio) throws Exception;

}

package servicos.interfaces;

import negocio.comuns.financeiro.MovParcelaVO;

import java.util.List;

/**
 * Created by ulisses on 24/05/2016.
 */
public interface BoletoServiceInterface {

    String gerarBoleto(String key, Integer codigoMovParcela, Integer codigoPessoa, boolean calcularMultaJuros, String requestURL)throws Exception;
    String gerarBoletoTodasParcelasDoContrato(String key, Integer codigoContrato, Integer codigoPessoa, boolean calcularMultaJuros, String requestURL)throws Exception;
    String gerarBoletoParcelas(String key, List<MovParcelaVO> listaMovParcela, Integer codigoPessoa, boolean calcularMultaJuros, String requestURL)throws Exception;


}

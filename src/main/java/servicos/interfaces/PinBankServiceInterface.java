package servicos.interfaces;

import negocio.comuns.financeiro.TransacaoVO;
import servicos.http.RespostaHttpDTO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 23/02/2022
 */

public interface PinBankServiceInterface extends AprovacaoServiceInterface {

    RespostaHttpDTO consultarTransacao(String codigoExterno) throws Exception;

    void consultarSituacaoCobrancaTransacao(TransacaoVO transacao) throws Exception;
}

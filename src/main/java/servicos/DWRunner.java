package servicos;

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.RoboControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.dcc.base.RemessaService;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class DWRunner {

    public static void main(String[] args) {
        System.out.println("Entrou no DW Runner");
        Uteis.debug = true;
        if (args.length == 0) {
            args = new String[]{"boaviagem"};
        }
        if (args.length >= 1) {
            String chave = args[0];
            try {
                DAO dao = new DAO();
                Connection con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                //atualizar banco de dados, se defasado
                LoginControle control = new LoginControle();
                control.setUsername("RECOR");
                control.setSenha(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
                control.setUserOamd("adm");
                control.login();
                control.atualizarBD();

                RoboControle roboControle = new RoboControle();
                roboControle.inicializarRobo();
                RoboVO robo = roboControle.getRobo();

                //foi adicionado no DW devido o horário 04:30 que é processado para processar antes da academia abrir.
                processarAutomaticoInfoMigracaoUsuario(con);
                
                try {
                    robo.processarDadosGerencialPmg(chave);
                } catch (Exception ex) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(" Não foi possível executar o processo de Dados  Gerencial Pmg no dia ").append(Uteis.getData(robo.getDia()));
                    sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                    sb.append(" - ERRO: ").append(ex.getMessage());
                    Uteis.logar(null, sb.toString());
                }
                
                try {
                    Integer nrMeses = null;
                    if(args.length >= 2){
                        try {
                            nrMeses = Integer.valueOf(args[1]);
                        } catch (Exception e) {
                            nrMeses = null;
                        }
                    }
                    robo.processarDadosDemonstrativoFinanceiro(nrMeses);
                    robo.processarDadosDRE(nrMeses);
                } catch (Exception ex) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(robo.getListaEmpresa().get(0).getNome()).append(" -");
                    sb.append(" Não foi possível executar o processo de dados do Demonstrativo Finaceiro no dia ").append(Uteis.getData(robo.getDia()));
                    sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                    sb.append(" - ERRO: ").append(ex.getMessage());
                    Uteis.logar(null, sb.toString());
                }

                try {
                    robo.processarDadosTicketMedio();
                } catch (Exception ex) {
                    StringBuilder sb = new StringBuilder();
                    sb.append(" Não foi possível executar o processo de dados do Ticket Médio no dia ").append(Uteis.getData(robo.getDia()));
                    sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                    sb.append(" - ERRO: ").append(ex.getMessage());
                    Uteis.logar(null, sb.toString());
                }


                System.out.println("Finalizando DW Runner");
            } catch (Exception ex) {
                Logger.getLogger(DWRunner.class.getName()).log(Level.SEVERE,
                        "Erro ao obter conexao especifica com a chave " + chave, ex);
            }

        }
    }

    private static void processarAutomaticoInfoMigracaoUsuario(Connection con) {
        try {
            RemessaService.processarDadosInfoMigracao(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        Empresa empresaDAO;
        try {
            empresaDAO = new Empresa(con);
            empresaDAO.processarAutomaticoInfoMigracaoUsuario(null, true);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug(ex.getMessage());
        } finally {
            empresaDAO = null;
        }
    }
}

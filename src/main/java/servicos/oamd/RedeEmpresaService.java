package servicos.oamd;

import negocio.oamd.RedeEmpresaVO;
import servicos.impl.oamd.OAMDService;

import java.util.HashMap;
import java.util.Map;

public class RedeEmpresaService {

    private RedeEmpresaService() {

    }

    private static final Map<String, RedeEmpresaVO> redePorChave = new HashMap<>();

    public static RedeEmpresaVO obterRedePorChave(String chave) {
        if (redePorChave.containsKey(chave)) {
            return redePorChave.get(chave);
        } else {
            RedeEmpresaVO redeEmpresaVO = OAMDService.consultarRedeEmpresa(chave);
            redePorChave.put(chave, redeEmpresaVO);
            return redeEmpresaVO;
        }
    }

    public static void limparMapaDeRedes() {
        redePorChave.clear();
    }

}

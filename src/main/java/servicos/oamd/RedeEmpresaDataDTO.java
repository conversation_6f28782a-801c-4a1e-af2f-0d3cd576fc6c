package servicos.oamd;

import servicos.discovery.EmpresaDTO;
import servicos.discovery.EmpresaFinanceiroDTO;
import servicos.discovery.RedeDTO;
import servicos.discovery.ServiceMapDTO;

import java.util.List;

public class RedeEmpresaDataDTO {

    private String[] modulosHabilitados;
    private List<EmpresaDTO> empresas;
    private List<EmpresaFinanceiroDTO> financeiroEmpresas;
    private List<RedeDTO> redeEmpresas;
    private ServiceMapDTO serviceUrls;

    public String[] getModulosHabilitados() {
        return modulosHabilitados;
    }

    public void setModulosHabilitados(String[] modulosHabilitados) {
        this.modulosHabilitados = modulosHabilitados;
    }

    public List<EmpresaDTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<EmpresaDTO> empresas) {
        this.empresas = empresas;
    }

    public ServiceMapDTO getServiceUrls() {
        return serviceUrls;
    }

    public void setServiceUrls(ServiceMapDTO serviceUrls) {
        this.serviceUrls = serviceUrls;
    }

    public List<EmpresaFinanceiroDTO> getFinanceiroEmpresas() {
        return financeiroEmpresas;
    }

    public void setFinanceiroEmpresas(List<EmpresaFinanceiroDTO> financeiroEmpresas) {
        this.financeiroEmpresas = financeiroEmpresas;
    }

    public List<RedeDTO> getRedeEmpresas() {
        return redeEmpresas;
    }

    public void setRedeEmpresas(List<RedeDTO> redeEmpresas) {
        this.redeEmpresas = redeEmpresas;
    }
}


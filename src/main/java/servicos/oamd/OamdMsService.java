package servicos.oamd;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryException;
import servicos.discovery.RedeDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OamdMsService {

    public static String baseUrl() throws OamdException {
        String url = PropsService.getPropertyValue("urlOamd");
        if(url == null || url.equals("@URL_OAMD@")){
            throw new OamdException("A propriedade @URL_OAMD@ não está definida");
        }
        if(!url.contains("/prest")){
            url = url + "/prest";
        }

        return url;
    }

    public static RedeEmpresaDataDTO urls()throws Exception {
        String url = baseUrl() + "/find";

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), RedeEmpresaDataDTO.class);
    }

    public static RedeEmpresaDataDTO urlsChave(String chave)throws Exception {
        String url = baseUrl() + "/find/"+chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), RedeEmpresaDataDTO.class);
    }

    public static RedeEmpresaDataDTO urlsChaveRedeEmpresa(String chave)throws Exception {
        String url = baseUrl() + "/find-url-rede/"+chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        return JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), RedeEmpresaDataDTO.class);
    }

    public static List<RedeDTO> urlsRede(String chave)throws Exception{
        String url = baseUrl() + "/find/rede/"+chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        return JSONMapper.getList(new JSONObject(response).getJSONArray("content"), RedeDTO.class);
    }

    public static boolean integranteRedeEmpresa(String chave) throws Exception {
        String url = baseUrl() + "/find-integrante-rede/"+chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        Map<String, Boolean> dados = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), Map.class);
        return dados.getOrDefault("integranteRedeEmpresa", false);
    }

    public static boolean integranteFranqueadoraRedeEmpresa(String chave) throws Exception {
        String url = baseUrl() + "/empresaFinanceiro/configsTreinoRede?chaveZW=" + chave;

        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        Map<String, String> dados = JSONMapper.getObject(new JSONObject(response).getJSONObject("configsTreinoRede"), Map.class);
        if (dados.containsKey("chaveFranqueadora")) {
            return dados.get("chaveFranqueadora").equals(chave);
        } else {
            return false;
        }
    }
}

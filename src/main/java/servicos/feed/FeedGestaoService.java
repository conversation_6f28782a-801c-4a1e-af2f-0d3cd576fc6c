/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.feed;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.facade.jdbc.estoque.ProdutoEstoque;
import negocio.interfaces.estoque.ProdutoEstoqueInterfaceFacade;
import servicos.integracao.OamdWSConsumer;
import br.com.pactosolucoes.comuns.json.FeedGestaoJSON;
import br.com.pactosolucoes.comuns.json.PaginaInicialFeedJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.feed.CondicaoEnum;
import negocio.comuns.feed.FeedGestaoHistoricoVO;
import negocio.comuns.feed.FeedGestaoVO;
import negocio.comuns.feed.IndicadorEnum;
import negocio.comuns.feed.PaginaInicialFeedVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.feed.FeedGestao;
import negocio.facade.jdbc.feed.PaginaInicialFeed;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.basico.QuestionarioClienteInterfaceFacade;
import negocio.interfaces.contrato.ContratoInterfaceFacade;
import negocio.interfaces.crm.FecharMetaDetalhadoInterfaceFacade;
import negocio.interfaces.feed.FeedGestaoInterfaceFacade;
import negocio.interfaces.feed.PaginaInicialInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import relatorio.negocio.comuns.basico.ICVTO;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;

/**
 *
 * <AUTHOR>
 */
public class FeedGestaoService {

    private Connection con;
    private FeedGestaoInterfaceFacade feedGestaoDao;
    private PaginaInicialInterfaceFacade paginaInicialDao;
    private QuestionarioClienteInterfaceFacade questionarioDao;
    private EmpresaInterfaceFacade empresaDao;
    private ProdutoEstoqueInterfaceFacade produtoEstoqueDao;
    private FecharMetaDetalhadoInterfaceFacade fecharMetaDao;
    private ContratoInterfaceFacade contratoDao;
    private MovParcelaInterfaceFacade movParcelaDao;

    public FeedGestaoService(Connection con) {
        this.con = con;
    }

    public void obterFeedsGestaoDoOamd() {
        try {
            String feeds = OamdWSConsumer.obterFeeds();
            JSONArray json = new JSONArray(feeds);
            List<FeedGestaoJSON> lista = JSONMapper.getList(json, FeedGestaoJSON.class);
            processarFeeds(lista);
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoService.class);
        }

    }

    public void obterCapaFeedsGestaoDoOamd() {
        try {
            paginaInicialDao = new PaginaInicialFeed(con);
            String capaStr = OamdWSConsumer.obterPaginaInicial();
            JSONArray json = new JSONArray(capaStr);
            List<PaginaInicialFeedJSON> lista = JSONMapper.getList(json, PaginaInicialFeedJSON.class);
            if (lista != null && !lista.isEmpty()) {
                paginaInicialDao.alterarPaginaInicial(new PaginaInicialFeedVO(lista.get(0)));
            }
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoService.class);
        }
    }

    public PaginaInicialFeedVO obterCapaFeedsExemplo() {
        try {
            String capaStr = OamdWSConsumer.obterPaginaInicial();
            JSONArray json = new JSONArray(capaStr);
            List<PaginaInicialFeedJSON> lista = JSONMapper.getList(json, PaginaInicialFeedJSON.class);
            if (lista != null && !lista.isEmpty()) {
                return new PaginaInicialFeedVO(lista.get(0));
            }

        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoService.class);
        }
        return new PaginaInicialFeedVO();
    }

    public List<FeedGestaoVO> obterTodos() throws Exception {
        List<FeedGestaoVO> listaFeeds = new ArrayList<FeedGestaoVO>();
        String feeds = OamdWSConsumer.obterFeeds();
        JSONArray json = new JSONArray(feeds);
        List<FeedGestaoJSON> lista = JSONMapper.getList(json, FeedGestaoJSON.class);
        for (FeedGestaoJSON fg : lista) {
            FeedGestaoVO feedGestaoVO = new FeedGestaoVO(fg);
            listaFeeds.add(feedGestaoVO);
        }
        return listaFeeds;
    }

    public void processarFeeds(List<FeedGestaoJSON> lista) throws Exception {
        try {
            con.setAutoCommit(false);
            feedGestaoDao = new FeedGestao(con);
            questionarioDao = new QuestionarioCliente(con);
            fecharMetaDao = new FecharMetaDetalhado(con);
            empresaDao = new Empresa(con);
            contratoDao = new Contrato(con);
            movParcelaDao = new MovParcela(con);
            produtoEstoqueDao = new ProdutoEstoque(con);

            List<EmpresaVO> empresas = empresaDao.consultarEmpresas();
            for (EmpresaVO empresa : empresas) {
                feedGestaoDao.deletarTodos(empresa.getCodigo());

                Map<Integer, ICVTO> icvs = questionarioDao.gerarICVResumido(empresa.getCodigo(), Calendario.hoje());
                Double icv = icv(new ArrayList<ICVTO>(icvs.values()));

                for (FeedGestaoJSON fg : lista) {

                    FeedGestaoVO feedGestaoVO = new FeedGestaoVO(fg);
                    boolean igual = feedGestaoVO.getCondicao() != null && feedGestaoVO.getCondicao().equals(CondicaoEnum.IGUAL);
                    boolean maiorq = feedGestaoVO.getCondicao() != null && feedGestaoVO.getCondicao().equals(CondicaoEnum.MAIOR);
                    boolean menorq = feedGestaoVO.getCondicao() != null && feedGestaoVO.getCondicao().equals(CondicaoEnum.MENOR);
                    boolean gravar = false;
                    if (!UteisValidacao.emptyNumber(feedGestaoVO.getDiaMes())) {
                        int diaMesData = Uteis.getDiaMesData(Calendario.hoje());
                        if (diaMesData > feedGestaoVO.getDiaMes()) {
                            continue;
                        }
                    }
                    if (!UteisValidacao.emptyNumber(feedGestaoVO.getPeriodicidade())) {
                        FeedGestaoHistoricoVO ultimaApresentacaoDaDica = feedGestaoDao.obterUltimaApresentacaoDaDica(feedGestaoVO.getCodigoOAMD());

                        long nrDiasEntreDatas = ultimaApresentacaoDaDica == null ? 0
                                : Uteis.nrDiasEntreDatas(ultimaApresentacaoDaDica.getDia(), Calendario.hoje());
                        if (ultimaApresentacaoDaDica != null && nrDiasEntreDatas < feedGestaoVO.getPeriodicidade() && ultimaApresentacaoDaDica.isFoiVisto()) {
                            continue;
                        }
                    }
                    if (feedGestaoVO.getDica() == null) {
                        gravar = true;
                    } else {
                        switch (feedGestaoVO.getDica()) {
                            case ICV:
                                if (igual && feedGestaoVO.getValorCondicao().equals(icv)) {
                                    gravar = true;
                                } else if (menorq && feedGestaoVO.getValorCondicao() > icv) {
                                    gravar = true;
                                } else if (maiorq && feedGestaoVO.getValorCondicao() < icv) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>", "" + icv.intValue()));
                                break;

                            case ICV_CONSULTOR:
                                String retorno = processarICVPorConsultor(new ArrayList<ICVTO>(icvs.values()), icv, igual, menorq, maiorq, feedGestaoVO.getValorCondicao());
                                if (!retorno.isEmpty()) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<consultores>", retorno));
                                break;

                            case CRM_VENDAS:
                                Double resultado = processarCRMVendas(empresa.getCodigo(), igual, maiorq, menorq, feedGestaoVO);
                                if ((igual && resultado != null && resultado.intValue() == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && resultado != null && resultado.intValue() > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && resultado != null && resultado.intValue() < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                    feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>", String.valueOf(resultado.intValue())));
                                }
                                break;

                            case PLANOS_POR_DURACAO:
                                Date inicio = Uteis.somarDias(Calendario.hoje(), -feedGestaoVO.getPeriodicidade());
                                Date fim = Calendario.hoje();
                                int percentual = contratoDao.percentualContratosLancadosPorDuracao(1, inicio, fim, empresa.getCodigo());

                                if ((igual && percentual == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && percentual > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && percentual < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>", String.valueOf(percentual)));
                                break;

                            case CONTATOS_GRUPO_RISCO:
                                Date inicioGR = Uteis.somarDias(Calendario.hoje(), -feedGestaoVO.getPeriodicidade());
                                Date fimGR = Uteis.somarDias(Calendario.hoje(), -1);
                                Double resultadoGR = fecharMetaDao.resultadoCRM(inicioGR, fimGR, "'" + FasesCRMEnum.GRUPO_RISCO.getSigla() + "'", empresa.getCodigo());

                                if ((igual && resultadoGR != null && resultadoGR.intValue() == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && resultadoGR != null && resultadoGR.intValue() > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && resultadoGR != null && resultadoGR.intValue() < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                    feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>", "" + resultadoGR.intValue()));
                                }

                                break;

                            case CLIENTES_PARCELA_ATRASO:
                                Date dataVencimentoComTolerancia = Uteis.obterDataAnterior(Calendario.hoje(), empresa.getToleranciaPagamento());
                                ResultSet rs = movParcelaDao.contarPendenciaParcelaEmAbertoAtraso(empresa.getCodigo(), dataVencimentoComTolerancia, "");
                                rs.next();
                                int qtd = rs.getInt("qtd");
                                if ((igual && qtd == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && qtd > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && qtd < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>", "" + qtd));
                                break;
                            case PARCELAS_DCC_VENCIDAS_NAO_RECEBIDAS:
                                Integer qtdParcelas = movParcelaDao.consultarParcelasDCCCount(empresa.getCodigo(), Calendario.hoje(), null, null);
                                Double valorParcelas = movParcelaDao.consultarParcelasDCCValor(empresa.getCodigo(), Calendario.hoje(), null, null);

                                if ((igual && qtdParcelas == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && qtdParcelas > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && qtdParcelas < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                    feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<dinheiro>",
                                            Formatador.formatarValorMonetarioSemMoeda(valorParcelas)).replaceAll("<valor>", qtdParcelas.toString()));
                                }

                                break;
                            case NAO_USA_RECORRENCIA:
                                Date inicioUR = Uteis.somarDias(Calendario.hoje(), -feedGestaoVO.getPeriodicidade());
                                Date fimUR = Calendario.hoje();
                                gravar = !feedGestaoDao.usouRecorrenciaPeriodo(inicioUR, fimUR, empresa.getCodigo());
                                break;

                            case SALDO_ALUNOS:
                                RotatividadeAnaliticoDWVO rotatividade = contratoDao.montarRotatividadeResumido(empresa.getCodigo(), Calendario.hoje());
                                if ((igual && rotatividade.getQtdSaldo() == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && rotatividade.getQtdSaldo() > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && rotatividade.getQtdSaldo() < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                }
                                break;
                            case PREVISAO_RENOVACAO_CONTRATOS_ATIVOS:
                                String dia = Uteis.getDataFormatoBD(Calendario.hoje());
                                Integer qtdAtivos = contratoDao.contar(String.format(Contrato.sqlAtivosContar,
                                        new Object[]{empresa.getCodigo(), dia, dia, dia, dia, dia, dia}));
                                Integer previsao = contratoDao.consultaPrevistosRenovacaoResumido(Calendario.hoje(), empresa.getCodigo());
                                Integer percentualAtivosRenovacao = UteisValidacao.emptyNumber(qtdAtivos) ? 0 : (previsao * 100) / qtdAtivos;
                                if ((igual && percentualAtivosRenovacao == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && percentualAtivosRenovacao > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && percentualAtivosRenovacao < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>",
                                        "" + percentualAtivosRenovacao.intValue()));
                                break;

                            case PREVISAO_RENOVACAO_CORRENTE:
                                Integer indice = contratoDao.consultaIndiceRenovacaoResumido(Calendario.hoje(), empresa.getCodigo());
                                if ((igual && indice == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && indice > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && indice < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>",
                                        "" + indice.intValue()));
                                break;
                            case PRODUTOS_ESTOQUE_MINIMO:

                                ResultSet resultadoEstoque = produtoEstoqueDao.consultarProdutoEstoqueMinimo(empresa.getCodigo());
                                resultadoEstoque.next();
                                int quantidade = resultadoEstoque.getInt("qtd");
                                if ((igual && quantidade == feedGestaoVO.getValorCondicao().intValue())
                                        || (maiorq && quantidade > feedGestaoVO.getValorCondicao().intValue())
                                        || (menorq && quantidade < feedGestaoVO.getValorCondicao().intValue())) {
                                    gravar = true;
                                }
                                feedGestaoVO.setMensagem(feedGestaoVO.getMensagem().replaceAll("<valor>", ""+quantidade));
                                break;
                            case TELA_BI:
                                gravar = true;
                                break;
                        }
                    }
                    if (gravar) {
                        if (feedGestaoVO.getIndicadorGrupo() == null || !feedGestaoVO.getIndicadorGrupo().equals(IndicadorEnum.TELA_BI)) {
                            Integer codigoHistorico = feedGestaoDao.inserirHistorico(feedGestaoVO.getCodigoOAMD(), empresa.getCodigo(), Calendario.hoje());
                            feedGestaoVO.setCodigoHistorico(codigoHistorico);
                        }
                        Uteis.logar(null, "INCLUINDO NOVO FEED: "+feedGestaoVO.getNome());
                        feedGestaoDao.incluir(feedGestaoVO, empresa.getCodigo());
                    }
                }
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            Uteis.logar(e, FeedGestaoService.class);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public Double icv(List<ICVTO> icvs) {
        Double bvs = 0.0;
        Double mas = 0.0;
        Double res = 0.0;
        for (ICVTO icv : icvs) {
            bvs += icv.getBv();
            mas += icv.getMa();
            res += icv.getRe();
        }
        return bvs == 0 ? 0.0 : ((mas + res) / bvs) * 100;
    }

    public String processarICVPorConsultor(List<ICVTO> icvs, Double icv, boolean igual, boolean menorq, boolean maiorq, Double condicao) {
        if (icv == 0.0) {
            return "";
        }
        String retorno = "";
        boolean primeiro = true;
        Double valorPorcCondicao = (condicao * 100) / icv;
        for (ICVTO icvTo : icvs) {
            if (icvTo.getBv() > 0) {
                Double icvCol = ((Double.valueOf(icvTo.getRe() + icvTo.getMa())) / Double.valueOf(icvTo.getBv())) * 100.0;
                Double valorPorcCons = (icvCol * 100) / icv;
                if ((igual && valorPorcCons.intValue() == valorPorcCondicao.intValue())
                        || (menorq && ((icv - icvCol) > valorPorcCondicao))
                        || (maiorq && ((icvCol - icv) > valorPorcCondicao))) {
                    retorno = retorno.replaceAll("V1RG ", ", ") + (primeiro ? "" : "V1RG ") + icvTo.getNomeCol();
                    primeiro = false;
                }
            }
        }
        return retorno.replaceFirst(", ", "").replaceAll("V1RG ", "e ");
    }

    public Double processarCRMVendas(Integer empresa, boolean igual, boolean maior, boolean menor, FeedGestaoVO feed) throws Exception {
        Date inicio = Uteis.somarDias(Calendario.hoje(), -feed.getPeriodicidade());
        Date fim = Uteis.somarDias(Calendario.hoje(), -1);
        String fases = "";
        for (FasesCRMEnum fc : FasesCRMEnum.values()) {
            if (fc.getTipoFase().equals(TipoFaseCRM.VENDAS)) {
                fases = fases + ",'" + fc.getSigla() + "' ";
            }
        }
        return fecharMetaDao.resultadoCRM(inicio, fim, fases.replaceFirst(",", ""), empresa);
    }

    public void gravarFeedback() {
        try {
            feedGestaoDao = new FeedGestao(con);
            String gerarFeedBack = feedGestaoDao.gerarFeedBack(Calendario.hoje());
            OamdWSConsumer.gravarFeedBack(gerarFeedBack);
        } catch (Exception e) {
            Uteis.logar(e, FeedGestaoService.class);
        }
    }

    public static void main(String... args) {
        try {
            Connection con = null;
            if (args.length > 0) {
                con = new DAO().obterConexaoEspecifica(args[0]);
            } else {
                con = DriverManager.getConnection("****************************************************************", "postgres", "pactodb");
            }
//            Calendario.dia = new SimpleDateFormat("dd/MM/yyyy").parse("05/09/2017");
            FeedGestaoService service = new FeedGestaoService(con);
            service.obterFeedsGestaoDoOamd();
            service.obterCapaFeedsGestaoDoOamd();
            service.gravarFeedback();
        } catch (Exception ex) {
            Logger.getLogger(FeedGestaoService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

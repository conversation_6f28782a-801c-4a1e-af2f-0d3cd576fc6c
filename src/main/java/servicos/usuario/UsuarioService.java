package servicos.usuario;

import acesso.webservice.AcessoControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioTO;
import negocio.comuns.arquitetura.UsuarioTelefoneVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.webservice.IntegracaoCadastrosWS;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.arquitetura.UsuarioTelefone;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pais;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import servicos.integracao.adm.beans.EmpresaWS;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class UsuarioService implements AutoCloseable {

    private final Connection con;
    private final AcessoControle acessoControle;

    /**
     * Construtor que recebe uma conexao especifica para evitar deadlocks
     * @param con Conexao com o banco de dados
     * @throws Exception se houver erro na inicializacao
     */
    public UsuarioService(Connection con) throws Exception {
        this.con = con;
        this.acessoControle = new AcessoControle(con);
    }

    /**
     * Construtor que cria uma nova conexao baseada na chave
     * @param key Chave da empresa
     * @throws Exception se houver erro na inicializacao
     */
    public UsuarioService(String key) throws Exception {
        this.con = new DAO().obterConexaoEspecifica(key);
        this.acessoControle = new AcessoControle(con);
    }

    @Override
    public void close() throws Exception {
        if (con != null && !con.isClosed()) {
            con.close();
        }
    }

    /**
     * Valida um usuario usando a conexao da instancia
     * @param key Chave da empresa
     * @param senha Senha do usuario
     * @param userName Nome do usuario
     * @return UsuarioTO com os dados do usuario validado
     */
    public UsuarioTO validarUsuario(String key, String senha, String userName) {
        try {
            UsuarioVO usuarioVO = acessoControle.getControleAcessoDao().verificarLoginUsuario(userName, senha, true);
            return prepararUsuario(key, usuarioVO);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            UsuarioTO usu = new UsuarioTO();
            usu.setMensagem(ex.getMessage());
            return usu;
        }
    }

    /**
     * Valida um usuario V4 usando a conexao da instancia
     * @param key Chave da empresa
     * @param usuario Codigo do usuario
     * @return UsuarioTO com os dados do usuario validado
     */
    public UsuarioTO validarUsuarioV4(String key, Integer usuario) {
        try {
            UsuarioVO usuarioVO = acessoControle.getControleAcessoDao().verificarLoginUsuarioInnerV4(usuario);
            return prepararUsuario(key, usuarioVO);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            UsuarioTO usu = new UsuarioTO();
            usu.setMensagem(ex.getMessage());
            return usu;
        }
    }

    private UsuarioTO prepararUsuario(String key, UsuarioVO usuarioVO) throws Exception {
        if (usuarioVO == null
                || usuarioVO.getCodigo() == null
                || usuarioVO.getCodigo() == 0) {
            return null;
        } else {
            UsuarioTO usuarioto = new UsuarioTO();
            usuarioto.setNome(usuarioVO.getNome());
            usuarioto.setUserName(usuarioVO.getUsername());
            usuarioto.setCodigo(usuarioVO.getCodigo());
            usuarioto.setColaboradorId(usuarioVO.getColaboradorVO().getCodigo());
            usuarioto.setUsuarioTreino(acessoControle.getUsuarioMovelDao().usuarioTreino(usuarioVO.getCodigo()));
            if (usuarioto.isUsuarioTreino()) {
                UsuarioMovelVO usuarioMovelVO = acessoControle.getUsuarioMovelDao().consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (usuarioMovelVO != null) {
                    usuarioto.setUsuarioMovelTreino(usuarioMovelVO.getNome());
                    usuarioto.setCredecialTreino(usuarioMovelVO.getSenha());
                }
            }
            usuarioto.setUrlTreino(PropsService.getPropertyValue(key, PropsService.urlTreinoWeb));
            usuarioto.setAdministrador(usuarioVO.getAdministrador());
            usuarioto.setPermissaoAlterarRPS(usuarioVO.isPermissaoAlterarRPS());

            if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                usuarioto.setUrlFotosNuvem(PropsService.getPropertyValue(PropsService.urlFotosNuvem));
                usuarioto.setUrlFoto(usuarioVO.getColaboradorVO().getPessoa().getUrlFoto());
                usuarioto.setTypeMidiasService(PropsService.getPropertyValue(PropsService.typeMidiasService));

            }
            List<UsuarioPerfilAcessoVO> listaPerfisUsuario = acessoControle.
                    getUsuarioPerfilAcessoDao().consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(),
                            Uteis.NIVELMONTARDADOS_MINIMOS);
            if(listaPerfisUsuario.size() == 1){
                if (listaPerfisUsuario.get(0).getEmpresa().getEstado().getCodigo() == 0) {
                    listaPerfisUsuario.get(0).getEmpresa().setEstado(null);
                }
                if (listaPerfisUsuario.get(0).getEmpresa().getPais().getCodigo() == 0) {
                    listaPerfisUsuario.get(0).getEmpresa().setPais(null);
                }
                usuarioto.setEmpresaDefault(new EmpresaWS(listaPerfisUsuario.get(0).getEmpresa()));
            } else {
                usuarioto.setEmpresaDefault(new EmpresaWS(usuarioVO.getColaboradorVO().getEmpresa()));
            }
            return usuarioto;
        }
    }

    public UsuarioTO loginUsuarioSemSenha(String chave, String username) throws Exception {
        Usuario usuarioDAO;
        UsuarioMovel usuarioMovelDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        Empresa empresaDAO;
        Estado estadoDAO;
        Pais paisDAO;
        UsuarioEmail usuarioEmailDAO;
        UsuarioTelefone usuarioTelefoneDAO;
        try {
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave informada");
            }
            if (UteisValidacao.emptyString(username)) {
                throw new Exception("Username informado");
            }

            usuarioDAO = new Usuario(con);
            usuarioMovelDAO = new UsuarioMovel(con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);
            empresaDAO = new Empresa(con);
            estadoDAO = new Estado(con);
            paisDAO = new Pais(con);
            usuarioEmailDAO = new UsuarioEmail(con);
            usuarioTelefoneDAO = new UsuarioTelefone(con);

            UsuarioVO usuarioVO = new UsuarioVO();

            boolean loginEmail = UteisValidacao.validaEmail(username);
            Integer codUsuario = null;
            if (loginEmail) {
                UsuarioEmailVO usuarioEmailVO = usuarioEmailDAO.consultarPorEmail(username);
                codUsuario = usuarioEmailVO.getUsuario();
            } else {
                UsuarioTelefoneVO usuarioTelefoneVO = usuarioTelefoneDAO.consultarPorNumero(username);
                codUsuario = usuarioTelefoneVO.getUsuario();
            }
            if(UteisValidacao.emptyNumber(codUsuario)) {
                usuarioVO = usuarioDAO.consultarPorUsername(username, Uteis.NIVELMONTARDADOS_VALIDACAOACESSO);
            }

            if (UteisValidacao.emptyNumber(codUsuario) && usuarioVO.getCodigo() == null) {
                throw new Exception("Usuario nao encontrado");
            }
            usuarioVO = UteisValidacao.emptyNumber(usuarioVO.getCodigo()) ?
                    usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_VALIDACAOACESSO) : usuarioVO;
            if (usuarioVO == null
                    || usuarioVO.getCodigo() == null
                    || usuarioVO.getCodigo() == 0) {
                throw new Exception("Usuario nao encontrado");
            } else {
                UsuarioTO usuarioto = new UsuarioTO();
                usuarioto.setNome(usuarioVO.getNome());
                usuarioto.setUserName(usuarioVO.getUsername());
                usuarioto.setCodigo(usuarioVO.getCodigo());
                usuarioto.setUsuarioTreino(usuarioMovelDAO.usuarioTreino(usuarioVO.getCodigo()));
                if (usuarioto.isUsuarioTreino()) {
                    UsuarioMovelVO usuarioMovelVO = usuarioMovelDAO.consultarPorUsuario(usuarioVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (usuarioMovelVO != null) {
                        usuarioto.setUsuarioMovelTreino(usuarioMovelVO.getNome());
                        usuarioto.setCredecialTreino(usuarioMovelVO.getSenha());
                    }
                }
                usuarioto.setUrlTreino(PropsService.getPropertyValue(chave, PropsService.urlTreinoWeb));
                usuarioto.setAdministrador(usuarioVO.getAdministrador());
                usuarioto.setPermissaoAlterarRPS(usuarioVO.isPermissaoAlterarRPS());

                if (PropsService.isTrue(PropsService.fotosParaNuvem)) {
                    usuarioto.setUrlFotosNuvem(PropsService.getPropertyValue(PropsService.urlFotosNuvem));
                    usuarioto.setUrlFoto(usuarioVO.getColaboradorVO().getPessoa().getUrlFoto());
                    usuarioto.setTypeMidiasService(PropsService.getPropertyValue(PropsService.typeMidiasService));

                }
                List<UsuarioPerfilAcessoVO> listaPerfisUsuario = usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                if(listaPerfisUsuario.size() == 1){
                    if (listaPerfisUsuario.get(0).getEmpresa().getEstado().getCodigo() == 0) {
                        listaPerfisUsuario.get(0).getEmpresa().setEstado(null);
                    }
                    if (listaPerfisUsuario.get(0).getEmpresa().getPais().getCodigo() == 0) {
                        listaPerfisUsuario.get(0).getEmpresa().setPais(null);
                    }
                    usuarioto.setEmpresaDefault(new EmpresaWS(listaPerfisUsuario.get(0).getEmpresa()));
                } else {
                    usuarioto.setEmpresaDefault(new EmpresaWS(usuarioVO.getColaboradorVO().getEmpresa()));
                }
                for (UsuarioPerfilAcessoVO perfil : listaPerfisUsuario) {
                    EmpresaVO emp = empresaDAO.consultarPorChavePrimaria(perfil.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    try {
                        if (emp.getEstado().getCodigo() > 0) {
                            emp.setEstado(estadoDAO.consultarPorChavePrimaria(emp.getEstado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            emp.setPais(paisDAO.consultarPorChavePrimaria(emp.getPais().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            perfil.setEmpresa(emp);
                        }
                    } catch (Exception e) {
                        Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE,
                                String.format("nao foi possivel consultar dados de PAIS/ESTADO da empresa %s devido ao erro...", emp.getNome()), e);
                    }
                    usuarioto.getEmpresas().add(new EmpresaWS(emp.getCodigo(), emp.getNome(), emp.getTokenSMS(),
                            emp.getEstado().getDescricao(),emp.getPais().getNome(), emp.getUsarNFSe(), emp.isUsarNFCe(), emp.getChaveNFSe(), emp.isUsaEnotas(), emp.getTotalDiasExtras()));

                    EmpresaWS empresaAcessoModuloNota = retornarPerfilSePossuirPermissao("AcessoModuloNotasUsuario", perfil);

                    if (empresaAcessoModuloNota != null) {
                        usuarioto.getEmpresasPermissaoModuloNotas().add(empresaAcessoModuloNota);
                    }
                }

                if (usuarioVO.getColaboradorVO() != null
                        && !UteisValidacao.emptyNumber(usuarioVO.getColaboradorVO().getCodigo())) {
                    usuarioto.setColaboradorId(usuarioVO.getColaboradorVO().getCodigo());
                    usuarioto.setCodigoPessoa(usuarioDAO.obterCodigoPessoaUsuario(usuarioVO.getColaboradorVO().getCodigo()));
                }

                usuarioto.setValidarUserOamd(PropsService.isTrue(PropsService.validarUsuarioOAMD));
                return usuarioto;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            usuarioDAO = null;
            usuarioMovelDAO = null;
            usuarioPerfilAcessoDAO = null;
            empresaDAO = null;
            estadoDAO = null;
            paisDAO = null;
            usuarioEmailDAO = null;
            usuarioTelefoneDAO = null;
        }
    }

    private EmpresaWS retornarPerfilSePossuirPermissao(String entidadePermissao, UsuarioPerfilAcessoVO usuarioPerfilAcessoVO) {
        for (PermissaoVO permissaoVO : usuarioPerfilAcessoVO.getPerfilAcesso().getPermissaoVOs()) {
            if (permissaoVO.getNomeEntidade().equals(entidadePermissao)) {
                return new EmpresaWS(
                        usuarioPerfilAcessoVO.getEmpresa().getCodigo(),
                        usuarioPerfilAcessoVO.getEmpresa().getNome()
                );
            }
        }
        return null;
    }

    // Metodos estaticos para manter compatibilidade com codigo existente
    // Estes metodos criam uma nova instancia e fecham a conexao automaticamente

    /**
     * Metodo estatico para validar usuario (compatibilidade com codigo existente)
     * @param key Chave da empresa
     * @param senha Senha do usuario
     * @param userName Nome do usuario
     * @return UsuarioTO com os dados do usuario validado
     */
    public static UsuarioTO validarUsuarioStatic(String key, String senha, String userName) {
        try (UsuarioService service = new UsuarioService(key)) {
            return service.validarUsuario(key, senha, userName);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            UsuarioTO usu = new UsuarioTO();
            usu.setMensagem(ex.getMessage());
            return usu;
        }
    }

    /**
     * Metodo estatico para validar usuario V4 (compatibilidade com codigo existente)
     * @param key Chave da empresa
     * @param usuario Codigo do usuario
     * @return UsuarioTO com os dados do usuario validado
     */
    public static UsuarioTO validarUsuarioV4Static(String key, Integer usuario) {
        try (UsuarioService service = new UsuarioService(key)) {
            return service.validarUsuarioV4(key, usuario);
        } catch (Exception ex) {
            Logger.getLogger(IntegracaoCadastrosWS.class.getName()).log(Level.SEVERE, null, ex);
            UsuarioTO usu = new UsuarioTO();
            usu.setMensagem(ex.getMessage());
            return usu;
        }
    }
}

package servicos;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.bigquery.*;
import negocio.comuns.utilitarias.Uteis;
import servicos.propriedades.PropsService;

import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Locale;
import java.util.Objects;

public class ChurnRunner {

    private final static int BATCH_SIZE = 1000;
    private final static int KEY_ARG_IDX = 0;

    private final static String BIG_QUERY_ALL = "WITH ultima_predicao_dia AS (\n" +
            "  SELECT \n" +
            "    _chave, \n" +
            "    codigocliente, \n" +
            "    prob_class_1, \n" +
            "    ROW_NUMBER() OVER (\n" +
            "      PARTITION BY codigocliente \n" +
            "      ORDER BY data_predicao DESC\n" +
            "    ) AS row_num\n" +
            "  FROM `academia-de-verdade.producao.churn_predicao`\n" +
            "  WHERE \n" +
            "    _chave = @chave\n" +
            "    AND CAST(data_predicao AS DATE) = CURRENT_DATE()\n" +
            ")\n" +
            "SELECT \n" +
            "  _chave, \n" +
            "  codigocliente, \n" +
            "  prob_class_1 \n" +
            "FROM ultima_predicao_dia\n" +
            "WHERE row_num = 1\n" +
            "ORDER BY codigocliente;";

    private final static String BIG_QUERY_ALL_GPT =  "WITH ultima_recomendacao_dia AS (\n" +
            "  SELECT \n" +
            "    _chave, \n" +
            "    codigocliente, \n" +
            "    recomendacao, \n" +
            "    ROW_NUMBER() OVER (\n" +
            "      PARTITION BY codigocliente \n" +
            "      ORDER BY datahora_execucao DESC\n" +
            "    ) AS row_num\n" +
            "  FROM `academia-de-verdade.producao.relatorio`\n" +
            "  WHERE \n" +
            "    _chave = @chave\n" +
            "    AND CAST(datahora_execucao AS DATE) = CURRENT_DATE()\n" +
            ")\n" +
            "SELECT \n" +
            "  _chave, \n" +
            "  codigocliente, \n" +
            "  recomendacao \n" +
            "FROM ultima_recomendacao_dia\n" +
            "WHERE row_num = 1\n" +
            "ORDER BY codigocliente;";

    private final static String UPDATE_CHURN_RISCO = "UPDATE situacaoclientesinteticodw SET riscochurn = %f, riscochurnlancamento = current_timestamp WHERE codigocliente = %d";
    private final static String UPDATE_SUGESTAO_GPT = "UPDATE situacaoclientesinteticodw SET sugestaogpt = '%s', riscochurnlancamento = current_timestamp WHERE codigocliente = %d";
    public static final String ENV_KEY_GOOGLE_APPLICATION_CREDENTIALS = "GOOGLE_APPLICATION_CREDENTIALS";

    public static void main(String[] args) {
        long inicio = System.currentTimeMillis();
        Uteis.debug = true;

        if (args.length < 1) {
//            throw new IllegalArgumentException("Necessario passar chave como argumento de execucao");
            args = new String[]{"7f68c95f97f795e60b30f89f1cbe3a47"};
        }

        final String chave = args[KEY_ARG_IDX];
        Uteis.logar("ChurnRunner Iniciado para a chave: " + chave);

        Uteis.logar("Buscando arquivo de configuracao definido em .env GOOGLE_APPLICATION_CREDENTIALS");
        GoogleCredentials credentials;
        String googleApplicationCredentialsPath = PropsService.getPropertyValue(ENV_KEY_GOOGLE_APPLICATION_CREDENTIALS);

        if (Objects.isNull(googleApplicationCredentialsPath)) {
            Uteis.logar("Arquivo de configuracao definido em GOOGLE_APPLICATION_CREDENTIALS nao encontrado");
            throw new IllegalStateException("E necessario definir em .env a variavel GOOGLE_APPLICATION_CREDENTIALS com o caminho para o arquivo de credentials.json do projeto academia-de-verdade");
        }

        try (
                FileInputStream arquivoServicoJson = new FileInputStream(googleApplicationCredentialsPath);
                Connection connection = new DAO().obterConexaoEspecifica(chave)
        ) {

            Uteis.logar("Arquivo encontrado com sucesso.");
            Uteis.logar("Iniciando configuracao de Credentials.");
            credentials = ServiceAccountCredentials.fromStream(arquivoServicoJson);
            BigQuery bigQueryService = BigQueryOptions.newBuilder()
                    .setCredentials(credentials)
                    .build()
                    .getService();
            Uteis.logar("Configuracao de Credentials definida com sucesso.");

            povoarRiscoChurn(chave, connection, bigQueryService);
            povoarSugestaoGPT(chave, connection, bigQueryService);

            Uteis.logar("ChurnRunner finalizado com sucesso para a chave: " + chave + ".");
            Uteis.logar("Tempo de execucao: " + (System.currentTimeMillis() - inicio) + "ms");
            Uteis.logar("----------------------------------------------------------------------");
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException("Problema com BigQuery: " + e);
        } catch (Exception e) {
            throw new RuntimeException("Problema de do conexao com o banco: " + e);
        }
    }

    private static void povoarRiscoChurn(String chave, Connection connection, BigQuery bigQueryService) throws InterruptedException, SQLException {
        QueryJobConfiguration queryConfig = QueryJobConfiguration
                .newBuilder(BIG_QUERY_ALL)
                .addNamedParameter("chave", QueryParameterValue.string(chave))
                .setMaxResults((long) BATCH_SIZE)
                .build();

        TableResult resultadoPaginado = bigQueryService.query(queryConfig);

        Uteis.logar("Iniciando Atualizacao dos riscos de churn");
        Statement statement = connection.createStatement();
        final long totalDeLinhas = resultadoPaginado.getTotalRows();
        int atualizacoesAdicionadasAoBatch = 0;
        int execucoesBatch = 0;

        do {
            for (FieldValueList fieldValues : resultadoPaginado.getValues()) {
                double churnRisco = fieldValues.get("prob_class_1").getDoubleValue();
                long codigoCliente = fieldValues.get("codigocliente").getLongValue();
                statement.addBatch(String.format(Locale.US, UPDATE_CHURN_RISCO, churnRisco, codigoCliente));

                if (++atualizacoesAdicionadasAoBatch % BATCH_SIZE == 0) {
                    statement.executeBatch();
                    Uteis.logar(String.format("#%d Execucao em batch finalizada com sucesso - %.0f%%", ++execucoesBatch, ((double) atualizacoesAdicionadasAoBatch / totalDeLinhas) * 100.0));
                }
            }

            if (atualizacoesAdicionadasAoBatch % BATCH_SIZE != 0) {
                statement.executeBatch();
                Uteis.logar(String.format("#%d Execucao em batch finalizada com sucesso - %.0f%%", ++execucoesBatch, ((double) atualizacoesAdicionadasAoBatch / totalDeLinhas) * 100.0));
            }

            if (resultadoPaginado.hasNextPage()) {
                resultadoPaginado = resultadoPaginado.getNextPage();
            } else {
                break;
            }
        } while (true);
    }

    private static void povoarSugestaoGPT(String chave, Connection connection, BigQuery bigQueryService) throws InterruptedException, SQLException {
        QueryJobConfiguration queryConfig = QueryJobConfiguration
                .newBuilder(BIG_QUERY_ALL_GPT)
                .addNamedParameter("chave", QueryParameterValue.string(chave))
                .setMaxResults((long) BATCH_SIZE)
                .build();

        TableResult resultadoPaginado = bigQueryService.query(queryConfig);

        Uteis.logar("Iniciando Atualizacao das Sugestões GPT");
        Statement statement = connection.createStatement();
        final long totalDeLinhas = resultadoPaginado.getTotalRows();
        int atualizacoesAdicionadasAoBatch = 0;
        int execucoesBatch = 0;

        do {
            for (FieldValueList fieldValues : resultadoPaginado.getValues()) {
                String sugestaoGPT = fieldValues.get("recomendacao").getStringValue();
                sugestaoGPT = sugestaoGPT.replace("'", "*");
                long codigoCliente = fieldValues.get("codigocliente").getLongValue();
                statement.addBatch(String.format(Locale.US, UPDATE_SUGESTAO_GPT, sugestaoGPT, codigoCliente));

                if (++atualizacoesAdicionadasAoBatch % BATCH_SIZE == 0) {
                    statement.executeBatch();
                    Uteis.logar(String.format("#%d Execucao em batch finalizada com sucesso - %.0f%%", ++execucoesBatch, ((double) atualizacoesAdicionadasAoBatch / totalDeLinhas) * 100.0));
                }
            }

            if (atualizacoesAdicionadasAoBatch % BATCH_SIZE != 0) {
                statement.executeBatch();
                Uteis.logar(String.format("#%d Execucao em batch finalizada com sucesso - %.0f%%", ++execucoesBatch, ((double) atualizacoesAdicionadasAoBatch / totalDeLinhas) * 100.0));
            }

            if (resultadoPaginado.hasNextPage()) {
                resultadoPaginado = resultadoPaginado.getNextPage();
            } else {
                break;
            }
        } while (true);
    }

}

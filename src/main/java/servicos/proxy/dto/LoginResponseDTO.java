package servicos.proxy.dto;

public class LoginResponseDTO {

    private ContentDTO content;

    public ContentDTO getContent() {
        return content;
    }

    public void setContent(ContentDTO content) {
        this.content = content;
    }

    public static class ContentDTO {
        private DadosDTO dados;
        private String token;
        private Long validade;

        public DadosDTO getDados() {
            return dados;
        }

        public void setDados(DadosDTO dados) {
            this.dados = dados;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public Long getValidade() {
            return validade;
        }

        public void setValidade(Long validade) {
            this.validade = validade;
        }
    }

    public static class DadosDTO {
        private int codZW;
        private int codTreino;
        private String perfilZW;
        private String perfilTR;
        private String userName;
        private String nome;
        private int colaboradorId;
        private String provider;
        private boolean administrador;
        private String zwJSessionId;

        public int getCodZW() {
            return codZW;
        }

        public void setCodZW(int codZW) {
            this.codZW = codZW;
        }

        public int getCodTreino() {
            return codTreino;
        }

        public void setCodTreino(int codTreino) {
            this.codTreino = codTreino;
        }

        public String getPerfilZW() {
            return perfilZW;
        }

        public void setPerfilZW(String perfilZW) {
            this.perfilZW = perfilZW;
        }

        public String getPerfilTR() {
            return perfilTR;
        }

        public void setPerfilTR(String perfilTR) {
            this.perfilTR = perfilTR;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getNome() {
            return nome;
        }

        public void setNome(String nome) {
            this.nome = nome;
        }

        public int getColaboradorId() {
            return colaboradorId;
        }

        public void setColaboradorId(int colaboradorId) {
            this.colaboradorId = colaboradorId;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

        public boolean isAdministrador() {
            return administrador;
        }

        public void setAdministrador(boolean administrador) {
            this.administrador = administrador;
        }

        public String getZwJSessionId() {
            return zwJSessionId;
        }

        public void setZwJSessionId(String zwJSessionId) {
            this.zwJSessionId = zwJSessionId;
        }
    }
}
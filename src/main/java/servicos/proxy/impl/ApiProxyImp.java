package servicos.proxy.impl;

import kong.unirest.HttpResponse;
import kong.unirest.JsonNode;
import kong.unirest.Unirest;
import servicos.proxy.interfaces.ApiProxy;

public class ApiProxyImp implements ApiProxy {

    @Override
    public <T> T mandarPost(String urlCompleta, String token, Object body, Class<T> responseType) {
        try {
            HttpResponse<T> response = Unirest.post(urlCompleta)
                    .header("Authorization", token != null ? "Bearer " + token : "")
                    .header("Content-Type", "application/json")
                    .body(body)
                    .asObject(responseType);

            if (response.getStatus() == 200 || response.getStatus() == 201) {
                return response.getBody();
            } else {
                throw new RuntimeException("Erro na requisição: " + response.getStatus());
            }
        } catch (Exception e) {
            throw new RuntimeException("Erro ao processar a requisição POST", e);
        }
    }

    @Override
    public <T> T mandarPut(String urlCompleta, String token, Object body, Class<T> responseType) {
        try {
            HttpResponse<T> response = Unirest.put(urlCompleta)
                    .header("Authorization", token != null ? "Bearer " + token : "")
                    .header("Content-Type", "application/json")
                    .body(body)
                    .asObject(responseType);

            if (response.getStatus() == 200 || response.getStatus() == 201) {
                return response.getBody();
            } else {
                throw new RuntimeException("Erro na requisição: " + response.getStatus());
            }
        } catch (Exception e) {
            throw new RuntimeException("Erro ao processar a requisição PUT", e);
        }
    }

    @Override
    public Object mandarPost(String urlCompleta, String token, Object body) {
        try {
            HttpResponse<JsonNode> response = Unirest.post(urlCompleta)
                    .header("Authorization", token != null ? "Bearer " + token : "")
                    .header("Content-Type", "application/json")
                    .body(body)
                    .asJson();

            if (response.getStatus() == 200 || response.getStatus() == 201) {
                return response.getBody();
            } else {
                throw new RuntimeException("Erro na requisição: " + response.getStatus());
            }
        } catch (Exception e) {
            throw new RuntimeException("Erro ao processar a requisição POST", e);
        }
    }
}
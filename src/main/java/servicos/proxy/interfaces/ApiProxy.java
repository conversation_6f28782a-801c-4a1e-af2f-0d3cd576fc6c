package servicos.proxy.interfaces;

import javax.annotation.Nullable;

public interface ApiProxy {

    Object mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body);

    <T> T mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body, Class<T> responseType);

    <T> T mandarPut(String urlCompleta, @Nullable String token, @Nullable Object body, Class<T> responseType);

}
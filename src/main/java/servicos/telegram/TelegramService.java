package servicos.telegram;

import negocio.comuns.utilitarias.Uteis;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

public class TelegramService {

    public static void enviarErro(String ctx, Exception e, String content){
        String msg = "ERRO: " + ctx + "-" + e.getMessage() + " \nconteudo: " + content;
        sendToTelegram(msg, "-1001200157958");
    }

    public static void enviarRetorno(String ctx, String content){
        String msg = "ERRO: " + ctx + "-" + " \nconteudo: " + content;
        sendToTelegram(msg, "-1001200157958");
    }

    public static void sendToTelegram(String message, String chatId) {
        System.out.println("enviar erro telegram");
        String urlString = "https://api.telegram.org/bot%s/sendMessage?chat_id=%s&text=%s";

        //Add Telegram token (given Token is fake)
        String apiToken = "1225924271:AAHGPakQgGJpW-OEO9ezvrsx8GDCqj1qAvU";

        urlString = String.format(urlString, apiToken, chatId, Uteis.encodeValue(message));

        try {
            URL url = new URL(urlString);
            URLConnection conn = url.openConnection();
            InputStream is = new BufferedInputStream(conn.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        sendToTelegram("teste mensagem  \"categoriaFicha\" : \"\",\n" +
                "  \"nivel\" : {\n" +
                "    \"ordem\" : 6,\n" +
                "    \"nome\" : \"AVANÇADO 1\",\n" +
                "    \"codigo\" : 5\n" +
                "  },\n" +
                "  \"categoria\" : 1,\n" +
                "  \"codigo\" : 13,", "-400581481");
    }


}

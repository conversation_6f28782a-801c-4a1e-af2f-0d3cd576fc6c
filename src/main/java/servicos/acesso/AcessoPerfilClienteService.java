package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.retorno.RetornoRequisicaoAtualizarTemplatePessoa;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;


public class AcessoPerfilClienteService {

    public static byte[] pegarFotoPessoa(String key, Integer localAcesso, Integer pessoa) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        return acessoControle.pegarFotoPessoa(localAcesso, pessoa);
    }

    public static String pegarUrlFotoPessoa(String key, Integer localAcesso, Integer pessoa) throws Exception {
        AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
        return acessoControle.pegarUrlFotoPessoa(localAcesso, pessoa);
    }

    public static RetornoRequisicaoAtualizarTemplatePessoa pegarUrlTemplateFacialPessoa(String key, Integer pessoa) throws Exception {
        RetornoRequisicaoAtualizarTemplatePessoa retorno = new RetornoRequisicaoAtualizarTemplatePessoa();
        ClienteVO cli = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().
                consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
        retorno.setCodigoAcesso(cli.getCodAcesso());
        retorno.setNome(cli.getPessoa().getNome());
        retorno.setFoto(urlFotoPessoa(key, pessoa));
        return retorno;
    }

    public static RetornoRequisicaoAtualizarTemplatePessoa pegarUrlFotoPessoaAvaliandoEmpresa(String key, Integer pessoa, Integer empresa) throws Exception {
        RetornoRequisicaoAtualizarTemplatePessoa retorno = new RetornoRequisicaoAtualizarTemplatePessoa();
        ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().
                consultarPorCodigoPessoa(pessoa, empresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

        ColaboradorVO colaboradorVO = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().
                consultarPorCodigoPessoaSituacao(pessoa, "AT", empresa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);

        if (!UteisValidacao.emptyNumber(colaboradorVO.getCodigo())){
            retorno.setCodigoAcesso(colaboradorVO.getCodAcesso());
            retorno.setNome(colaboradorVO.getPessoa().getNome());
        } else if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())){
            retorno.setCodigoAcesso(clienteVO.getCodAcesso());
            retorno.setNome(clienteVO.getPessoa().getNome());
        }
        retorno.setFoto(urlFotoPessoa(key, pessoa));
        return retorno;
    }

    private static Pessoa getPessoaDAO(String chave) throws Exception {
        return new Pessoa(new DAO().obterConexaoEspecifica(chave));
    }

    public static String urlFotoPessoa(String chave, Integer pessoa) throws Exception {
        return Uteis.getPaintFotoDaNuvem(getPessoaDAO(chave).obterFotoKey(pessoa));
    }

    public static String atualizarFotoPerfilAluno(String key, String codigoAcesso, String foto) {
        try {
            String tipoAcesso = codigoAcesso.substring(0, 1);
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipoAcesso)) {
                ClienteVO cli = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().
                        consultarPorCodAcesso(codigoAcesso, false,
                                Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                if (cli != null && cli.getPessoa() != null
                        && !UteisValidacao.emptyNumber(cli.getPessoa().getCodigo())) {
                    final String fotoKey = DaoAuxiliar.retornarAcessoControle(key).getContratoAssinaturaDigitalService().alterarFotoAluno(
                            key, null, cli.getPessoa().getCodigo(), foto, 0, null);
                    return Uteis.getPaintFotoDaNuvem(fotoKey);
                } else {
                    return "ERRO: Cliente não encontrado com o código de acesso " + codigoAcesso;
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipoAcesso)) {
                ColaboradorVO col = DaoAuxiliar.retornarAcessoControle(key).getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                if (col != null && col.getPessoa() != null && !UteisValidacao.emptyNumber(col.getPessoa().getCodigo())) {
                    final String fotoKey = DaoAuxiliar.retornarAcessoControle(key).getContratoAssinaturaDigitalService().alterarFotoAluno(key, null, col.getPessoa().getCodigo(), foto, 0, null);
                    return Uteis.getPaintFotoDaNuvem(fotoKey);
                } else {
                    return "ERRO: Colaborador não encontrado com o código de acesso " + codigoAcesso;
                }
            } else {
                return "ERRO: Tipo de acesso não definido com o código de acesso " + codigoAcesso;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return "ERRO: " + ex.getMessage();
        }
    }
}

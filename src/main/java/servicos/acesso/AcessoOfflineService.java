/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoDadosOffline;
import acesso.webservice.retorno.RetornoRequisicaoWS;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.DadosAcessoOffline;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import java.lang.management.ManagementFactory;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class AcessoOfflineService {

    public static RetornoRequisicaoDadosOffline montarDadosOffline(String key, Integer localAcesso) {

        RetornoRequisicaoDadosOffline ret = new RetornoRequisicaoDadosOffline();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ret = acessoControle.gerarArquivoOffline(key, localAcesso,null);

        } catch (Exception e) {
            ret.setResultado(ResultadoWSEnum.ERRO);
            ret.setMsgErro(e.getMessage());
            Uteis.logar(null, e.toString());
        }

        return ret;
    }

    public static RetornoRequisicaoDadosOffline montarDadosOfflinePessoa( String key, Integer localAcesso, Integer codigoPessoa) {

        RetornoRequisicaoDadosOffline ret = new RetornoRequisicaoDadosOffline();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            ret = acessoControle.gerarArquivoOffline(key, localAcesso,codigoPessoa);

        } catch (Exception e) {
            ret.setResultado(ResultadoWSEnum.ERRO);
            ret.setMsgErro(e.getMessage());
            Uteis.logar(null, e.toString());
        }

        return ret;
    }

    public static RetornoRequisicaoWS gravarSenhaIntegracaoEncriptada(String key, String codigoAcesso, String senhaEncriptada) throws Exception {

        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, codigoAcesso, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
        RetornoRequisicaoWS retorno = new RetornoRequisicaoWS();
        try {
            if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                // A senha tem que ser única.
                if (DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().senhaAcessoJaUtilizada(autorizacao.getCodigo(), 0, senhaEncriptada)) {
                    throw new Exception("Senha não permitida. Informe outra senha.");
                }
                DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().alterarSenhaAcesso(autorizacao, senhaEncriptada);
                retorno.setResultado(ResultadoWSEnum.SUCESSO);
            } else {
                throw new Exception("Autorização não encontrada: "+codigoAcesso);
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            retorno.setMsgErro("Método que ocorreu o erro: "
                    + "\"public RetornoRequisicaoWS gravarSenhaIntegracaoCriptografada\""
                    + " Classe do Erro: " + e.getClass()
                    + " Mensagem Erro: " + e.getMessage());

        }
        return retorno;
    }

    public static void executar(String chave, StringBuffer sb) {
        long l1 = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
        long t1 = System.currentTimeMillis();
        try {
            DAO oamd = new DAO();
            Conexao conexao = Conexao.getInstance();
            Connection con = null;
            if (!conexao.getUrlOAMD().isEmpty()) {
                con = Conexao.getConexaoForJ2SE();
                if (con == null || con.isClosed()) {
                    con = oamd.obterConexaoEspecifica(chave);
                    Conexao.guardarConexaoForJ2SE(con);
                }
                Uteis.logar(sb, "Guardada conexao para " + chave + "\r");
            } else {
                Conexao.guardarConexaoForJ2SE(FacadeManager.getFacade().getZWFacade().getCon());
                Uteis.logar(sb, "Usando cfgBD.xml para -> " + conexao.getIpServidor());
            }

            LoginControle control = new LoginControle();
            control.setUsername("admin");
            control.setSenha("070922PMG");
            control.setUserOamd("adm");
            control.login();

            //chave = "xiquin2";
            AcessoControle validacao = DaoAuxiliar.retornarAcessoControle(chave);
            validacao.setValidacaoAcessoOffline(true);
            try {
                Uteis.logar(sb, "### Iniciando processo de geração da base offline para chave  " + chave + "\r");
                DadosAcessoOffline dao = new DadosAcessoOffline(con);
                dao.preencherDados(chave, null);
                validacao.getLocalAcessoDao().alterarDataBaseOffline(null, Calendario.hoje());
//                Map<String, String> mapa = new HashMap();
//                mapa.put("op", "createNOTF");
//                mapa.put("desc", "atualizar offline");
//                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss",
//                    Calendario.getDefaultLocale());
//                mapa.put("dti", df.format(Calendario.hoje()));
//                mapa.put("dtf", df.format(Calendario.fimDoDia(Calendario.hoje())));
//                mapa.put("tipoNOTF", "WS");
//                mapa.put("chave", chave);
//                mapa.put("localAcesso", "");
//                mapa.put("propagable", "s");
//                String url = PropsService.getPropertyValue(PropsService.urlNotificacaoAcesso);
//                ExecuteRequestHttpService.executeRequest(url, mapa);
            } finally {
                validacao.setValidacaoAcessoOffline(false);
                long l2 = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage().getUsed();
                long t2 = System.currentTimeMillis();

                Uteis.logar(null, "Generated data from: " + chave + " in " + (t2 - t1) + "ms");
                Uteis.logar(null, "Memory used: " + ((l2 - l1) / 1024) + "KB");
            }
        } catch (Exception e) {
            Uteis.logar(sb, "FALHOU para chave " + chave);
            Uteis.logar(sb, "            " + e.getMessage() + "\n");
        } finally {
            FacadeManager.limparFactory();
        }
    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            StringBuffer sb = new StringBuffer();
            executar(args.length == 0 ? "c6f6b02fff1ab2d835d4446868ec8c" : args[0], sb);
        } catch (Exception ex) {
            Logger.getLogger(AcessoOfflineService.class.getName()).log(Level.SEVERE, null, ex);
        }

    }
}

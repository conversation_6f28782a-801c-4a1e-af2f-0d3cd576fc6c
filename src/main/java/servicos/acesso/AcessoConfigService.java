package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.retorno.IntegracaoAcessoWS;
import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoBuscarIntegracoes;
import acesso.webservice.retorno.RetornoRequisicaoWS;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.RedeEmpresaVO;
import servicos.oamd.RedeEmpresaService;

import java.util.ArrayList;
import java.util.List;

public class AcessoConfigService {

    public static RetornoRequisicaoWS gravarSenhaIntegracao(String key, String codigoAcesso, String senha) throws Exception {

        RetornoRequisicaoWS retorno = new RetornoRequisicaoWS();
        try {
            if (codigoAcesso.startsWith("AUT")) {
                AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, null, null, null, codigoAcesso, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
                EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(autorizacao.getEmpresaLocal().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                    autorizacao.senhaAcessoValida(senha, senha, empresaVO.isSenhaAcessoOnzeDigitos());
                    // A senha tem que ser única.
                    String senhaEncriptada = Uteis.encriptar(senha);
                    if (DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().senhaAcessoJaUtilizada(autorizacao.getCodigo(), 0, senhaEncriptada)) {
                        throw new Exception("Senha não permitida. Informe outra senha.");
                    }
                    DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().alterarSenhaAcesso(autorizacao, senhaEncriptada);
                    retorno.setResultado(ResultadoWSEnum.SUCESSO);
                } else {
                    throw new Exception("Autorização não encontrada: " + codigoAcesso);
                }
            } else {
                ClienteVO cliente = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().consultarPorChavePrimaria(cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                PessoaVO pessoaVO = cliente.getPessoa();
                pessoaVO.setSenhaAcesso(senha);
                pessoaVO.setConfirmarSenhaAcesso(senha);
                //Devido a vir do acesso, não irá permitir mais que 5 dígitos
                pessoaVO.validarSenhaAcesso(empresaVO.isSenhaAcessoOnzeDigitos(), cliente);
                // A senha tem que ser única.
                String senhaEncriptada = Uteis.encriptar(pessoaVO.getSenhaAcesso());
                if (DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().senhaAcessoJaUtilizada(0, pessoaVO.getCodigo(), senhaEncriptada)) {
                    throw new Exception("Senha não permitida. Informe outra senha.");
                }
                // Se chegou até aqui sem exceção, então não houve restrições na validação. Desta forma, gravar a senha no banco de dados.
                DaoAuxiliar.retornarAcessoControle(key).getPessoaDao().alterarSenhaAcesso(pessoaVO.getCodigo(), pessoaVO.getSenhaAcesso(), false);
                retorno.setResultado(ResultadoWSEnum.SUCESSO);
            }
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            e.printStackTrace();
        }

        return retorno;
    }

    public static void registrarIpLocalAcesso(String key, Integer localAcesso, String ip) {
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.getLocalAcessoDao().alterarIpLocalAcesso(localAcesso, ip);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static RetornoRequisicaoBuscarIntegracoes buscarConfigIntegracaoAcesso(Integer empresa, String key) {
        RedeEmpresaVO redeEmpresaVO = RedeEmpresaService.obterRedePorChave(key);

        RetornoRequisicaoBuscarIntegracoes retorno = null;
        List<IntegracaoAcessoWS> integracoesWS = new ArrayList<>();
        try {
            if (redeEmpresaVO != null && redeEmpresaVO.getGestaoRedes() && !redeEmpresaVO.getChaveFranqueadora().equals(key)) {
                IntegracaoAcessoWS integracaoAcessoWS = new IntegracaoAcessoWS();
                integracaoAcessoWS.setChaveEmpresaRemota(redeEmpresaVO.getChaveFranqueadora());
                integracaoAcessoWS.setCodigoEmpresaRemota(redeEmpresaVO.getCodigoUnidadeFranqueadora());
                integracoesWS.add(integracaoAcessoWS);
            }
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<IntegracaoAcessoGrupoEmpresarialVO> integracoes = acessoControle.getIntegracaoDao().consultar("", empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (IntegracaoAcessoGrupoEmpresarialVO integracao : integracoes) {
                IntegracaoAcessoWS integracaoAcessoWS = new IntegracaoAcessoWS();
                integracaoAcessoWS.setChaveEmpresaRemota(integracao.getChave());
                integracaoAcessoWS.setCodigoEmpresaRemota(integracao.getEmpresaRemota().getCodigo());
                integracaoAcessoWS.setCodigoChaveIntegracaoDigitais(integracao.getCodigoChaveIntegracaoDigitais());
                integracoesWS.add(integracaoAcessoWS);
            }
            //Adicionar a própria empresa na lista;
            EmpresaVO empresaVO = acessoControle.getEmpresaDao().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
            IntegracaoAcessoWS integracaoAcessoWS = new IntegracaoAcessoWS();
            integracaoAcessoWS.setChaveEmpresaRemota(key);
            integracaoAcessoWS.setCodigoEmpresaRemota(empresaVO.getCodigo());
            integracaoAcessoWS.setCodigoChaveIntegracaoDigitais(empresaVO.getCodigoChaveIntegracaoDigitais());
            integracoesWS.add(integracaoAcessoWS);

            retorno = new RetornoRequisicaoBuscarIntegracoes();
            retorno.setListaIntegracoes(integracoesWS);
        } catch (Exception e) {
            if (retorno == null) {
                retorno = new RetornoRequisicaoBuscarIntegracoes();
            }
            retorno.setResultado(ResultadoWSEnum.ERRO);
            e.printStackTrace();
        }

        return retorno;
    }
}

package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.Validador;
import acesso.webservice.retorno.ResultadoWSEnum;
import acesso.webservice.retorno.RetornoRequisicaoLiberacaoAcesso;
import acesso.webservice.retorno.RetornoRequisicaoValidacaoAcesso;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.*;
import negocio.comuns.utilitarias.Uteis;
import negocio.oamd.RedeEmpresaVO;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.integracao.ValidacaoAcessoWSConsumer;
import servicos.oamd.RedeEmpresaService;

import java.util.Date;

public class ValidacaoAcessoService {


    public static RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcessoAvaliandoIntegracao(
            String codigoCartao,
            Integer empresa,
            Boolean forcarLib,
            String key,
            Integer localAcesso,
            MeioIdentificacaoEnum meioIdentificacao,
            DirecaoAcessoEnum direcao,
            String terminal,
            Boolean acessoOutraEmpresa) throws Exception {

        return validarAcessoPeloCodigoAcessoAvaliandoIntegracao(codigoCartao, empresa, forcarLib, key, localAcesso, meioIdentificacao, direcao, terminal, acessoOutraEmpresa, false);
    }

    public static RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcessoAvaliandoIntegracao(
            String codigoCartao,
            Integer empresa,
            Boolean forcarLib,
            String key,
            Integer localAcesso,
            MeioIdentificacaoEnum meioIdentificacao,
            DirecaoAcessoEnum direcao,
            String terminal,
            Boolean acessoOutraEmpresa,
            Boolean publicarAutorizacao) throws Exception {

        if (acessoOutraEmpresa) {
            String identificadorOutraEmpresa = codigoCartao.substring(0, 4);
            String codAcesso = codigoCartao.substring(4);
            IntegracaoAcessoGrupoEmpresarialVO integracao = DaoAuxiliar.retornarAcessoControle(key).getIntegracaoDao().consultarPorCodigoChaveIdentificacao(identificadorOutraEmpresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            if (integracao == null) {
                throw new Exception("Integração " + identificadorOutraEmpresa + " não encontrada");
            }

            return ValidacaoAcessoWSConsumer.validarAcesso(new AutorizacaoAcessoGrupoEmpresarialVO(),
                    codAcesso, integracao.getEmpresaRemota().getCodigo(),
                    integracao.getLocalAcesso(),
                    integracao.getTerminal().toString(),
                    direcao, forcarLib, meioIdentificacao,
                    integracao.getChave(),
                    integracao.getUrlZillyonWeb(),
                    MetodoAcessoEnum.PELOCODIGOACESSO,
                    terminal, acessoOutraEmpresa, codigoCartao);
        } else {
            if (codigoCartao.startsWith("AUT")) {
                AutorizacaoAcessoGrupoEmpresarialVO autorizacao = DaoAuxiliar.retornarAcessoControle(key).getAutorizacaoDao().consultarPorCodigo(null, null, codigoCartao, null, null, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
                if (publicarAutorizacao) {
                    RedeEmpresaVO redeEmpresaVO = RedeEmpresaService.obterRedePorChave(key);
                    if (redeEmpresaVO != null && redeEmpresaVO.getGestaoRedes()) {
                        AcessoSistemaMSService.publish(autorizacao, false, redeEmpresaVO);
                    }
                }
                autorizacao.setCodAcesso(autorizacao.getCodAcesso().replaceFirst("NU", ""));
                return ValidacaoAcessoWSConsumer.validarAcesso(autorizacao,
                        autorizacao.getCodAcesso(), autorizacao.getEmpresaRemota().getCodigo(),
                        autorizacao.getIntegracao().getLocalAcesso(),
                        autorizacao.getIntegracao().getTerminal().toString(),
                        direcao, forcarLib, meioIdentificacao,
                        autorizacao.getIntegracao().getChave(),
                        autorizacao.getIntegracao().getUrlZillyonWeb(),
                        MetodoAcessoEnum.PELOCODIGOACESSO, terminal, acessoOutraEmpresa, codigoCartao);
            } else {
                return Validador.validarAcesso(codigoCartao, empresa, localAcesso, terminal, direcao, forcarLib, meioIdentificacao, key);
            }
        }
    }

    public static RetornoRequisicaoLiberacaoAcesso liberacaoAcesso(Date dataAcesso,
                                                             DirecaoAcessoEnum direcao,
                                                             Integer empresa,
                                                             String key,
                                                             Integer local,
                                                             Integer terminal,
                                                             TipoLiberacaoEnum tipoLiberacao,
                                                             Integer usuario,
                                                             String codAcesso,
                                                             String justificativa,
                                                             String nomeGenerico) {
        RetornoRequisicaoLiberacaoAcesso retorno = new RetornoRequisicaoLiberacaoAcesso();
        retorno.setTerminal(terminal.toString());
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            acessoControle.registrarLiberacaoAcesso(dataAcesso, tipoLiberacao, empresa, local, terminal, direcao, usuario, codAcesso, justificativa, nomeGenerico);
            retorno.setAcessoLiberado(true);
        } catch (Exception e) {
            retorno.setResultado(ResultadoWSEnum.ERRO);
            e.printStackTrace();
        }

        return retorno;
    }
}

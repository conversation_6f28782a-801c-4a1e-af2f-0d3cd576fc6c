package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.BaseAcessoWS;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.ValidacaoAcessoWS;
import acesso.webservice.retorno.ResultadoWS;
import acesso.webservice.retorno.RetornoRequisicaoRegistrarAcesso;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.PessoaConsultaTO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.oamd.RedeEmpresaVO;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.integracao.IntegracaoCadastrosWSConsumer;
import servicos.oamd.RedeEmpresaService;

import java.text.SimpleDateFormat;
import java.util.Date;

public class AutorizacaoAcessoService extends BaseAcessoWS {

    public RetornoRequisicaoRegistrarAcesso registrarAcessoAvaliandoIntegracao(
            Integer codigo,
            Date dataAcesso,
            DirecaoAcessoEnum direcao,
            Integer empresa,
            String key,
            Integer local,
            MeioIdentificacaoEnum meioIdentificacao,
            SituacaoAcessoEnum situacao,
            Integer terminal,
            String tipo,
            Integer usuario,
            String codAcessoIntegracao,
            String nomeCodEmpresaAcessou,
            Integer codigoMatricula) throws Exception {
        if (situacao == null){
            /* O ZillyonAcesso por motivos desconhecidos as vezes envia registros com o parâmetro "SituacaoAcessoEnum" igual a "null" e
               isso gerava erro e o zillyonAcesso ficava enviando infinitamente o mesmo acesso. Desta forma, a solução adotada
               foi ignorar o registro deste acesso.
             */
            SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
            StringBuilder msg = new StringBuilder();
            msg.append("#### IGNORADO REGISTRO DE ACESSO DEVIDO O PARAMETRO SituacaoAcessoEnum ESTÁ NULL. ").append("key:").append(key).append(" codigo:").append(codigo).append(" Data acesso:").append(sdf.format(dataAcesso));
            return new RetornoRequisicaoRegistrarAcesso();
        }
        return registrarAcessoInternal(codigo, dataAcesso, direcao, empresa, key,
                local, meioIdentificacao, situacao, terminal, tipo, usuario, codAcessoIntegracao, nomeCodEmpresaAcessou, codigoMatricula);

    }

    public static ResultadoWS inserirAutorizacaoAcesso(
            String key,
            String codAcesso,
            String codAcessoAlternativo,
            String nome,
            Integer codPessoa,
            String matricula,
            String senhaAcesso,
            String tipoPessoa,
            String chaveRemota,
            Integer codEmpresaRemota,
            Integer codEmpresaLocal
    ) {
        ResultadoWS result = new ResultadoWS();
        result.setSucesso(false);
        result.setMensagem("Erro desconhecido");
        try {
            RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
            if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService.findByMatriculaAndCodAcesso(matricula, codAcesso, redeEmpresa);
                if (autorizacao != null && autorizacao.getCodigo() > 0) {
                    result.setSucesso(true);
                    result.setMensagem(autorizacao.getCodigoAutorizacao());
                } else {
                    result.setSucesso(false);
                    result.setMensagem("Autorização não encontrada na Matriz");
                }
                return result;
            }

            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            codAcesso = codAcesso.startsWith("NU") ? codAcesso : "NU".concat(codAcesso);

            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoJaCadastrada = acessoControle.getAutorizacaoDao().consultarPorCodigo(
                    0, Integer.valueOf(matricula), codAcesso, null, null,
                    null, null, 0, Uteis.NIVELMONTARDADOS_TODOS, chaveRemota, codEmpresaLocal
            );
            if (autorizacaoJaCadastrada != null && autorizacaoJaCadastrada.getCodigo() > 0) {
                result.setSucesso(true);
                result.setMensagem(autorizacaoJaCadastrada.getCodigoAutorizacao());
                return result;
            }


            IntegracaoAcessoGrupoEmpresarialVO integracao = acessoControle.getIntegracaoDao().consultarPorChaveEEmpresa(
                    chaveRemota, codEmpresaRemota, codEmpresaLocal, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            if (integracao == null) {
                result.setMensagem("Integração não encontrada");
                return result;
            }

            AutorizacaoAcessoGrupoEmpresarialVO autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO();
            if (tipoPessoa == null) {
                throw new ConsistirException("Tipo de Pessoa deve ser informado");
            }
            TipoPessoaEnum tipoPessoaEnum;
            if (tipoPessoa.equalsIgnoreCase(TipoAcessoEnum.TA_COLABORADOR.getId())) {
                tipoPessoaEnum = TipoPessoaEnum.COLABORADOR;
            } else {
                tipoPessoaEnum = TipoPessoaEnum.ALUNO;
            }
            autorizacao.setTipoPessoa(tipoPessoaEnum.getTipo());
            autorizacao.setUsuarioResponsavel(acessoControle.getUsuarioDao().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS));

            autorizacao.setIntegracao(integracao);
            autorizacao.setEmpresaLocal(integracao.getEmpresaLocal());
            autorizacao.setEmpresaRemota(integracao.getEmpresaRemota());

            PessoaConsultaTO pessoa = IntegracaoCadastrosWSConsumer.findByAccessCode(
                    integracao.getUrlZillyonWeb(), integracao.getChave(), codAcesso, tipoPessoaEnum);

            if (pessoa == null) {
                throw new ConsistirException("Houve um problema ao selecionar a pessoa");
            }

            autorizacao.preencherComPessoaConsulta(pessoa);

            acessoControle.getAutorizacaoDao().incluir(autorizacao);
            result.setSucesso(true);
            result.setMensagem(autorizacao.getCodigoAutorizacao());
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao tentar inserir a autorização: " + e.getMessage());
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }
}

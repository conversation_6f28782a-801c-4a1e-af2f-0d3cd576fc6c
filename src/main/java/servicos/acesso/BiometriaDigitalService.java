package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.ValidacaoAcessoWS;
import acesso.webservice.retorno.ResultadoWS;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.RedeEmpresaVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.impl.microsservice.acessosistema.ValidacaoAcessoService;
import servicos.oamd.RedeEmpresaService;

public class BiometriaDigitalService {

    private BiometriaDigitalService() {

    }

    public static ResultadoWS atualizarAssinaturaBiometriaDigitalAutorizacao(String key, Integer codigoAutorizacao, String assinatura) {
        ResultadoWS result = new ResultadoWS();
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            AutorizacaoCobrancaClienteVO cobrancaClienteVO = acessoControle.getAutorizacaoCobrancaDao().consultarPorChavePrimaria(codigoAutorizacao);
            if (!UteisValidacao.emptyNumber(cobrancaClienteVO.getCodigo())) {
                acessoControle.getAutorizacaoCobrancaDao().atualizarAssinaturaDigitalBiometria(codigoAutorizacao, assinatura);
                result.setMensagem("Assinatura atualizada");
                result.setSucesso(true);
            }
        }catch (Exception e){
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao gravar assinatura digital da autorização");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }

        return result;
    }

    private static void gravarAssinaturaBiometriaDigitalColaborador(ColaboradorVO colaborador, ResultadoWS resultadoWS,
                                                             AcessoControle acessoControle, String assinatura, String codigoAcesso) throws Exception {
        if(colaborador == null){
            resultadoWS.setMensagem("O colaborador com código de acesso "+ codigoAcesso +" não foi encontrado");
            resultadoWS.setSucesso(false);
        }else{
            acessoControle.getPessoaDao().atualizarAssinaturaBiometriaDigital(colaborador.getPessoa().getCodigo(), assinatura);
            resultadoWS.setMensagem("Assinatura digital do colaborador atualizada");
            resultadoWS.setSucesso(true);
        }
    }

    public static ResultadoWS atualizarAssinaturaBiometriaDigitalPorCodAcesso(String key, String codigoAcesso, String assinatura) throws Exception {
        ResultadoWS result = new ResultadoWS();
        try{
            Uteis.logar(null, "Atualizar autorizaçao: " + codigoAcesso);
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            if (codigoAcesso.startsWith("AUT")) {
                return atualizarAssinaturaBiometricaPelAutorizacaoAcesso(key, codigoAcesso, assinatura, result, acessoControle);
            }

            String tipoAcesso = codigoAcesso.substring(0, 1);
            Uteis.logar(null, "tipoAcesso: " + tipoAcesso);
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipoAcesso)) {

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if(cliente == null){
                    result.setMensagem("O cliente com código de acesso "+ codigoAcesso +" não foi encontrado");
                    result.setSucesso(false);
                }else{
                    acessoControle.getPessoaDao().atualizarAssinaturaBiometriaDigital(cliente.getPessoa().getCodigo(), assinatura);
                    if (cliente.getSincronizadoRedeEmpresa() != null) {
                        RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
                        Uteis.logar(null, "Rede empresa: " + redeEmpresa != null ? redeEmpresa.getNome() : "Sem rede de empresa");

                        AutorizacaoAcessoGrupoEmpresarialVO autorizacao;
                        if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                            autorizacao = AcessoSistemaMSService.findByMatriculaAndCodAcesso(cliente.getMatricula(), cliente.getCodAcesso(), redeEmpresa);
                            Uteis.logar(null, "Código da autorização para o findByMatriculaAndCodAcesso: " + autorizacao.getCodigo());
                            autorizacao.setAssinaturaBiometriaDigital(assinatura);
                            AutorizacaoAcessoGrupoEmpresarialVO resultUpdate = AcessoSistemaMSService.updateAccessAuthorization(autorizacao, redeEmpresa, false);
                            if (UteisValidacao.emptyNumber(resultUpdate.getCodigo())) {
                                Uteis.logar(null, "Houve problemas ao atualiza a biometria no acesso-sistema-ms");
                            }
                        }
                    }

                    result.setMensagem("Assinatura digital do cliente atualizada");
                    result.setSucesso(true);
                }

            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipoAcesso)) {

                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                gravarAssinaturaBiometriaDigitalColaborador(colaborador, result, acessoControle, assinatura, codigoAcesso);

            } else {
                result.setMensagem("ERRO: Tipo de acesso não definido com o código de acesso " + codigoAcesso);
                result.setSucesso(false);
            }
        }catch (Exception e){
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao gravar assinatura digital na base de dados");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }

    private static ResultadoWS atualizarAssinaturaBiometricaPelAutorizacaoAcesso(String key, String codigoAcesso, String assinatura, ResultadoWS result, AcessoControle acessoControle) throws Exception {
        RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
        AutorizacaoAcessoGrupoEmpresarialVO autorizacao;
        if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
            autorizacao = AcessoSistemaMSService.findByCodigoAutorizacao(codigoAcesso, redeEmpresa);
            autorizacao.setAssinaturaBiometriaDigital(assinatura);
            AutorizacaoAcessoGrupoEmpresarialVO resultUpdate = AcessoSistemaMSService.updateAccessAuthorization(autorizacao, redeEmpresa, true);
            AcessoSistemaMSService.publish(autorizacao, false, redeEmpresa);
            if (UteisValidacao.emptyNumber(resultUpdate.getCodigo())) {
                result.setMensagem("Houve problemas ao atualiza a biometria");
                result.setSucesso(false);
                return result;
            }

            result.setMensagem("Assinatura digital do cliente atualizada");
            result.setSucesso(true);
            return result;
        } else {
            autorizacao = acessoControle.getAutorizacaoDao().consultarPorCodigo(null, null, codigoAcesso, null, null, null, null, null, Uteis.NIVELMONTARDADOS_DADOSBASICOS, null, null);
            if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                result.setMensagem(String.format("Autorização de Acesso %s não encontrada", codigoAcesso));
                result.setSucesso(false);
                return result;
            }
            autorizacao.setAssinaturaBiometriaDigital(assinatura);
            return ValidacaoAcessoService.updateAccessAuthorization(autorizacao);
        }
    }

    public static ResultadoWS obterAssinaturaBiometriaDigitalPorCodAcesso(String key, String codigoAcesso) throws Exception {
        ResultadoWS result = new ResultadoWS();
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);

            String tipoAcesso = codigoAcesso.substring(0, 1);
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipoAcesso)) {

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                String assinaturaBiometriaDigital = cliente == null ? "" : acessoControle.getPessoaDao().obterAssinaturaBiometriaDigital(cliente.getPessoa().getCodigo());
                if (cliente == null) {
                    result.setMensagem("O cliente com código de acesso " + codigoAcesso + " não foi encontrado");
                    result.setSucesso(false);
                } else if (UteisValidacao.emptyString(assinaturaBiometriaDigital)) {
                    result.setMensagem("O cliente com código de acesso " + codigoAcesso + " não tem biometria cadastrada");
                    result.setSucesso(false);
                } else {
                    result.setMensagem(assinaturaBiometriaDigital);
                    result.setSucesso(true);
                }

            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipoAcesso)) {

                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String assinaturaBiometriaDigital = colaborador == null ? "" : acessoControle.getPessoaDao().obterAssinaturaBiometriaDigital(colaborador.getPessoa().getCodigo());
                if (colaborador == null) {
                    result.setMensagem("O colaborador com código de acesso " + codigoAcesso + " não foi encontrado");
                    result.setSucesso(false);
                } else if (UteisValidacao.emptyString(assinaturaBiometriaDigital)) {
                    result.setMensagem("O colaborador com código de acesso " + codigoAcesso + " não tem biometria cadastrada");
                    result.setSucesso(true);
                } else {
                    result.setMensagem(assinaturaBiometriaDigital);
                    result.setSucesso(true);
                }

            } else {
                result.setMensagem("ERRO: Tipo de acesso não definido com o código de acesso " + codigoAcesso);
                result.setSucesso(false);
            }
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao obter a assinatura da biometria digital");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }

    public static ResultadoWS atualizarAssinaturaBiometriaDigital(String key, String matricula, String assinatura) throws Exception {
        ResultadoWS result = new ResultadoWS();
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            if(matricula.length() == 10){
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(matricula, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(colaborador == null){
                    result.setMensagem("O colaborador com código de acesso "+ matricula +" não foi encontrado");
                    result.setSucesso(false);
                }else{
                    acessoControle.getPessoaDao().atualizarAssinaturaBiometriaDigital(colaborador.getPessoa().getCodigo(), assinatura);
                    result.setMensagem("Assinatura do colaborador atualizada");
                    result.setSucesso(true);
                }
            }else{
                ClienteVO cliente = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if(cliente == null){
                    result.setMensagem("O cliente com matricula "+ matricula +" não foi encontrado");
                    result.setSucesso(false);
                }else{
                    acessoControle.getPessoaDao().atualizarAssinaturaBiometriaDigital(cliente.getPessoa().getCodigo(), assinatura);
                    result.setMensagem("Assinatura do cliente atualizada");
                    result.setSucesso(true);
                }
            }
        }catch (Exception e){
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao gravar assinatura na base de dados");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }

        return result;
    }

    public static ResultadoWS obterAssinaturaBiometriaDigital( String key, String matricula) {
        ResultadoWS result = new ResultadoWS();
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            if (matricula.length() == 10) {
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(matricula, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String assinaturaBiometriaDigital = colaborador == null ? "" : acessoControle.getPessoaDao().obterAssinaturaBiometriaDigital(colaborador.getPessoa().getCodigo());
                if (colaborador == null) {
                    result.setMensagem("O colaborador com código de acesso " + matricula + " não foi encontrado");
                    result.setSucesso(false);
                } else if (UteisValidacao.emptyString(assinaturaBiometriaDigital)) {
                    result.setMensagem("O colaborador com código de acesso " + matricula + " não tem biometria cadastrada");
                    result.setSucesso(true);
                } else {
                    result.setMensagem(assinaturaBiometriaDigital);
                    result.setSucesso(true);
                }
            } else {
                ClienteVO cliente = acessoControle.getClienteDao().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                String assinaturaBiometriaDigital = cliente == null ? "" : acessoControle.getPessoaDao().obterAssinaturaBiometriaDigital(cliente.getPessoa().getCodigo());
                if (cliente == null) {
                    result.setMensagem("O cliente com matricula " + matricula + " não foi encontrado");
                    result.setSucesso(false);
                } else if (UteisValidacao.emptyString(assinaturaBiometriaDigital)) {
                    result.setMensagem("O cliente com matricula " + matricula + " não tem biometria cadastrada");
                    result.setSucesso(false);
                } else {
                    result.setMensagem(assinaturaBiometriaDigital);
                    result.setSucesso(true);
                }
            }
        }catch (Exception e){
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao obter a assinatura da biometria");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }

    public static Boolean validarSePodeExcluirDigital(String key, String codigoAcesso) {
        try {
            if(verificaSeEColaborador(codigoAcesso)) {
                return false;
            }
            ClienteVO cliente = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (cliente == null) {
                return true;
            }
            SituacaoClienteSinteticoDWVO scsVO = DaoAuxiliar.retornarAcessoControle(key).getSituacaoClienteSinteticoDWDao().consultarCliente(cliente.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            return !"AT".equals(cliente.getSituacao()) && !"TR".equals(cliente.getSituacao()) && Calendario.menor(scsVO.getDia(), Uteis.somarDias(Calendario.hoje(), -90));
        } catch (Exception e) {
            Uteis.logar(null, e.toString());
        }

        return false;
    }

    private static boolean verificaSeEColaborador(String codigoAcesso) {
        return !UteisValidacao.emptyString(codigoAcesso) && (codigoAcesso.startsWith("5") || codigoAcesso.startsWith("9"));
    }
}

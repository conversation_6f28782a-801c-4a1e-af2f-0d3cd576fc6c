package servicos.acesso;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import acesso.webservice.ValidacaoAcessoWS;
import acesso.webservice.retorno.ResultadoWS;
import acesso.webservice.retorno.RetornoRequisicaoInformacoesAcessoAluno;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.enumerador.TipoAcessoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.oamd.RedeEmpresaService;

import java.util.ArrayList;
import java.util.List;

public class AcessoPessoaService {

    public static ResultadoWS obterCodigoPessoaPorCodigoAcesso(String key, String codigoAcesso
    ) throws Exception {
        ResultadoWS result = new ResultadoWS();
        result.setSucesso(false);
        result.setMensagem("Pessoa não encontrada");
        try{
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            String tipoAcesso = codigoAcesso.substring(0, 1);
            if (TipoAcessoEnum.TA_ALUNO.getId().equals(tipoAcesso)) {

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodAcesso(codigoAcesso, false, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                if(cliente == null){
                    result.setMensagem("O cliente com código de acesso "+ codigoAcesso +" não foi encontrado");
                }else{
                    result.setMensagem(cliente.getPessoa().getCodigo().toString());
                    result.setSucesso(true);
                }
            } else if (TipoAcessoEnum.TA_COLABORADOR.getId().equals(tipoAcesso)) {

                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodAcesso(codigoAcesso, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(colaborador == null){
                    result.setMensagem("O colaborador com código de acesso "+ codigoAcesso +" não foi encontrado");
                }else{
                    result.setMensagem(colaborador.getPessoa().getCodigo().toString());
                    result.setSucesso(true);
                }
            } else {
                result.setMensagem("ERRO: Tipo de acesso não definido com o código de acesso " + codigoAcesso);
            }
        }catch (Exception e){
            result.setMensagem("Houve um erro ao consultar a pessoa");
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }

    public static RetornoRequisicaoInformacoesAcessoAluno obterInformacoesAcessoAlunoPorCPF(String key, String cpf
    ) throws Exception {
        RetornoRequisicaoInformacoesAcessoAluno result = new RetornoRequisicaoInformacoesAcessoAluno();
        result.setSucesso(false);
        result.setMensagem("Não foi encontrado nenhuma pessoa com esse CPF");
        try {

            RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(key);
            if (redeEmpresa != null && redeEmpresa.getGestaoRedes()) {
                AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService.findByCPF(cpf, null, redeEmpresa);
                if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                    result.setCodigoPessoa(autorizacao.getCodigoPessoa());
                    result.setNome(autorizacao.getNomePessoa());
                    result.setSenhaAcesso(autorizacao.getSenhaAcesso());
                    result.setTemplateBiometria(autorizacao.getAssinaturaBiometriaDigital());
                    result.setTemplateFacial(autorizacao.getAssinaturaBiometriaFacial());
                    result.setCodigoEmpresa(autorizacao.getEmpresaRemota().getCodigo());
                    result.setCodigoMatricula(autorizacao.getCodigoMatricula().toString());
                    result.setCodigoAcesso(autorizacao.getCodAcesso());
                    result.setCodigoAcessoAlternativo(autorizacao.getCodAcessoAlternativo());
                    result.setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                    result.setMensagem("Cliente localizado");
                    result.setSucesso(true);
                }
            } else {
                AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
                PessoaVO pessoa = acessoControle.getPessoaDao().consultarPorCPF(cpf, Uteis.NIVELMONTARDADOS_CONSULTA_WS_COMPLETA);
                if (pessoa != null && pessoa.getCodigo() > 0) {
                    result.setCodigoPessoa(pessoa.getCodigo());
                    result.setNome(pessoa.getNome());
                    result.setSenhaAcesso(pessoa.getSenhaAcesso());
                    result.setTemplateBiometria(acessoControle.getPessoaDao().obterAssinaturaBiometriaDigital(pessoa.getCodigo()));
                    result.setTemplateFacial(acessoControle.getPessoaDao().obterAssinaturaBiometriaFacial(pessoa.getCodigo()));

                    ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
                    if ((cliente.getCodAcesso() != null) && !cliente.getCodAcesso().trim().isEmpty()) {
                        result.setSucesso(true);
                        result.setMensagem("Cliente localizado");

                        result.setCodigoEmpresa(cliente.getEmpresa().getCodigo());
                        result.setCodigoMatricula(cliente.getMatricula());
                        result.setTipo(TipoAcessoEnum.TA_ALUNO.getId());
                        result.setCodigoAcesso(cliente.getCodAcesso());
                        result.setCodigoAcessoAlternativo(cliente.getCodAcessoAlternativo());
                    } else {
                        ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodigoPessoa(pessoa.getCodigo(), 0, Uteis.NIVELMONTARDADOS_TODOS);
                        if ((colaborador.getCodAcesso() != null) && !colaborador.getCodAcesso().trim().isEmpty()) {
                            result.setSucesso(true);
                            result.setMensagem("Colaborador localizado");
                            result.setCodigoEmpresa(colaborador.getEmpresa().getCodigo());
                            result.setCodigoMatricula(colaborador.getCodAcesso());
                            result.setTipo(TipoAcessoEnum.TA_COLABORADOR.getId());
                            result.setCodigoAcesso(colaborador.getCodAcesso());
                            result.setCodigoAcessoAlternativo(colaborador.getCodAcessoAlternativo());
                        }
                    }
                }
            }
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao tentar obter as informações: "+e.getMessage());
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;


    }

    public static ResultadoWS obterCodigoAcessoPorCPF(String key, String cpf
    ) {
        ResultadoWS result = new ResultadoWS();
        result.setSucesso(false);
        result.setMensagem("Não foi encontrado nenhuma pessoa com esse CPF");
        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<PessoaVO> listaPessoas = new ArrayList<>();
            listaPessoas = acessoControle.getPessoaDao().consultarPorCfp(cpf, true, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            for (PessoaVO pessoa : listaPessoas) {

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                if ((cliente.getCodAcesso() != null) && !cliente.getCodAcesso().trim().isEmpty()) {
                    result.setMensagem(cliente.getCodAcesso());
                    result.setSucesso(true);
                    break;
                }
                ColaboradorVO colaborador = acessoControle.getColaboradorDao().consultarPorCodigoPessoa(pessoa.getCodigo(), 0, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                if ((colaborador.getCodAcesso() != null) && !colaborador.getCodAcesso().trim().isEmpty()) {
                    result.setMensagem(colaborador.getCodAcesso());
                    result.setSucesso(true);
                    break;
                }
            }
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao tentar obter o código de acesso: " + e.getMessage());
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }

    public static ResultadoWS obterSituacaoPorCPF(String key, String cpf) {
        ResultadoWS result = new ResultadoWS();
        result.setSucesso(false);
        result.setMensagem("Não foi encontrado nenhuma pessoa com esse CPF");

        try {
            AcessoControle acessoControle = DaoAuxiliar.retornarAcessoControle(key);
            List<PessoaVO> listaPessoas = acessoControle.getPessoaDao().consultarPorCfp(cpf, true, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
            for (PessoaVO pessoa : listaPessoas) {
                pessoa.setFotoKey(acessoControle.getPessoaDao().obterFotoKey(pessoa.getCodigo()));

                ClienteVO cliente = acessoControle.getClienteDao().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                if ((cliente.getCodAcesso() != null) && !cliente.getCodAcesso().trim().isEmpty()) {

                    JSONObject objReturn = new JSONObject();
                    objReturn.put("nome", pessoa.getNome());
                    objReturn.put("cpf", cpf);
                    objReturn.put("foto", pessoa.getUrlFoto());
                    objReturn.put("situacao", cliente.getSituacao_Apresentar());

                    JSONArray arrayEmails = new JSONArray();
                    for (EmailVO emailVO : pessoa.getEmailVOs()) {
                        arrayEmails.put(emailVO.getEmail());
                    }
                    objReturn.put("email", arrayEmails);

                    result.setMensagem(objReturn.toString());
                    result.setSucesso(true);
                    break;
                }
            }
        } catch (Exception e) {
            result.setSucesso(false);
            result.setMensagem("Houve um erro ao tentar obter o código de acesso: " + e.getMessage());
            Uteis.logar(e, ValidacaoAcessoWS.class);
        }
        return result;
    }
}

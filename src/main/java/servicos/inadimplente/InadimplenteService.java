package servicos.inadimplente;

import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.financeiro.InadimplenteTO;
import negocio.facade.jdbc.financeiro.MovParcela;

import java.sql.Connection;
import java.util.List;

public class InadimplenteService {

    private final Connection con;
    private final MovParcela movParcelaDao;

    public InadimplenteService(Connection con) throws Exception {
        this.con = con;
        this.movParcelaDao = new MovParcela(con);
    }

    public List<InadimplenteTO> buscarParcelasVencidasPorQtdDias(Integer diasVencido, PaginadorDTO paginador) throws Exception {
        return this.movParcelaDao.buscarParcelasVencidasPorQtdDias(diasVencido, paginador);
    }
}

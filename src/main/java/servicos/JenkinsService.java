/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos;

import br.com.pactosolucoes.enumeradores.TipoAgendamentoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pactopay.PactoPaySuperService;
import negocio.comuns.basico.PactoPayComunicacaoVO;
import negocio.comuns.basico.PactoPayEnvioEmailVO;
import negocio.comuns.crm.MeioEnvio;
import negocio.comuns.financeiro.enumerador.PactoPayEnvioEmailStatusEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.PactoPayComunicacao;
import negocio.facade.jdbc.basico.PactoPayEnvioEmail;
import org.apache.commons.io.FileUtils;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.input.SAXBuilder;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import servicos.propriedades.PropsService;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class JenkinsService {

    private static String NOME_TASK_PADRAO = "Mailing";
    private static String NOME_TASK_PACTOPAY = "PactoPay";
    private static String NOME_TASK_PACTOPAY_LISTA = "PactoPayList";

    private static List<Element> lerXML(InputStream arquivo) {

        //criar documento
        Document doc = null;
        //ler documento
        SAXBuilder builder = new SAXBuilder();
        try {
            doc = builder.build(arquivo);
        } catch (Exception e) {

            e.printStackTrace();
        }
        //criar uma lista dos dados contidos no documento
        Element dados = doc.getRootElement();
        //retornar lista dos dados lidos no xml
        return dados.getChildren();
    }

    private static void saveXML(Document doc, final String filePath) throws IOException {
        XMLOutputter xmlOutput = new XMLOutputter(Format.getPrettyFormat());
        Format formatXML = Format.getPrettyFormat();
        formatXML.setEncoding("ISO-8859-1");
        xmlOutput.setFormat(formatXML);

        xmlOutput.output(doc, new FileWriter(filePath));

        System.out.println("File " + filePath + " Saved!");
    }

    private static void postURL(final String urlPost, boolean exception) throws Exception {
        URL url = new URL(urlPost);
        HttpURLConnection openConnection = (HttpURLConnection) url.openConnection();
        openConnection.setRequestMethod("POST");
        openConnection.setRequestProperty("Content-Type", "text/html");
        openConnection.setDoOutput(true);
        openConnection.setDoInput(true);
        openConnection.setUseCaches(false);

        openConnection.connect();

        if (openConnection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            System.out.println(openConnection.getResponseMessage());
            validarRespostaErro(openConnection, exception);
        } else {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(openConnection.getInputStream()));
            do {
//                System.out.println(bufferedReader.readLine());
                bufferedReader.readLine();
            } while (bufferedReader.ready());
        }
    }

    private static void validarRespostaErro(HttpURLConnection openConnection, boolean exception) throws Exception {
        StringBuilder resposta = new StringBuilder();
        try {
            InputStream in = openConnection.getErrorStream();
            InputStreamReader inputStreamReader = new InputStreamReader(in, StandardCharsets.UTF_8);
            BufferedReader rd = new BufferedReader(inputStreamReader);
            String line;

            while ((line = rd.readLine()) != null) {
                // Processa linha a linha
                resposta.append(line);
            }
            rd.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        if (exception) {
            if (UteisValidacao.emptyNumber(resposta.length())) {
                resposta.append(openConnection.getResponseMessage());
            }
            throw new Exception(resposta.toString());
        }
    }

    private static void createTask(final String chave, final String url, final String id_mailing, final String cron,
                                   final String urlJenkins, final String chaveAntiga, final String nomePactoPay) throws Exception {
        File file = new File(JenkinsService.class.getResource("/servicos/propriedades/config.xml").toURI());
        FileInputStream in = new FileInputStream(file);
        List<Element> elementos = lerXML(in);

        //alterar parametros do Modelo
        Document doc = null;
        for (Element element : elementos) {
            if (doc == null) {
                doc = element.getDocument();
            }
            if (element.getName().equals("builders")) {
                Element e = element.getChild("hudson.tasks.Ant");
                if (e != null) {
                    if (e.getChild("antOpts") != null) {
                        e.getChild("antOpts").setText(
                                String.format("-Dchave=%s -Did_mailing=%s -Durl=%s -Dant.libs=%s",
                                chave.substring(0, chave.indexOf("_")),
                                id_mailing,
                                url,
                                PropsService.getPropertyValue(PropsService.diretorioLocalAnt) + "ant-lib"));
                    }

                    if (e.getChild("antName") != null) {
                        e.getChild("antName").setText(PropsService.getPropertyValue(PropsService.diretorioLocalAnt) + "bin");
                    }

                    if (e.getChild("buildFile") != null) {
                        e.getChild("buildFile").setText(PropsService.getPropertyValue(PropsService.diretorioLocalAnt) + "build.request.mailing.xml");
                    }
                }
            }
            if (cron != null) {
                if (element.getName().equals("triggers")) {
                    Element e = element.getChild("hudson.triggers.TimerTrigger");
                    if (e != null) {
                        if (e.getChild("spec") != null) {
                            e.getChild("spec").setText(cron);
                        }
                    }

                }
            }
        }

        File dest = new File(
                file.getPath().substring(0, file.getPath().lastIndexOf(File.separator))
                + File.separator + (UteisValidacao.emptyString(nomePactoPay) ? NOME_TASK_PADRAO : nomePactoPay) + "_" + chave + "_" + id_mailing + ".xml");
        saveXML(doc, dest.getAbsolutePath());
        postTask(chave, id_mailing, dest.getAbsolutePath(), urlJenkins, chaveAntiga, nomePactoPay);
        Thread.sleep(2000);
        dest = null;
    }

    public static void postTask(final String chave, final String id_mailing, final String fileXML,
                                final String urlJenkins, final String chaveAntiga, final String nomePactoPay) throws Exception {
        deleteTask(chaveAntiga, id_mailing, urlJenkins, nomePactoPay); // casos de edição de agendamento
        deleteTask(chave, id_mailing, urlJenkins, nomePactoPay);
        URL url = new URL(urlJenkins + "/createItem?name=" + (UteisValidacao.emptyString(nomePactoPay) ? NOME_TASK_PADRAO : nomePactoPay) + "_" + chave + "_" + id_mailing);
        HttpURLConnection openConnection = (HttpURLConnection) url.openConnection();
        openConnection.setRequestMethod("POST");
        openConnection.setRequestProperty("Content-Type", "text/xml");
        openConnection.setDoOutput(true);
        openConnection.setDoInput(true);
        openConnection.setUseCaches(false);

        if (fileXML != null) {
            BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(
                    openConnection.getOutputStream()));
            BufferedReader buffReader = new BufferedReader(new InputStreamReader(new FileInputStream(fileXML)));

            String line;

            while ((line = buffReader.readLine()) != null) {
                bufferedWriter.write(line);
                bufferedWriter.newLine();
            }

            buffReader.close();
            bufferedWriter.flush();
            bufferedWriter.close();
        }

        openConnection.connect();

        if (openConnection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            System.out.println(openConnection.getResponseMessage());
            validarRespostaErro(openConnection, !UteisValidacao.emptyString(nomePactoPay));
        } else {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(openConnection.getInputStream()));
            do {
                System.out.println(bufferedReader.readLine());
            } while (bufferedReader.ready());
        }

    }

    public static void deleteTask(final String chave, final String id_mailing,
                                  final String urlJenkins, final String nomePactoPay) throws Exception {
        postURL(urlJenkins + "/job/" + (UteisValidacao.emptyString(nomePactoPay) ? NOME_TASK_PADRAO : nomePactoPay) + "_" + chave + "_" + id_mailing + "/doDelete", false);
    }

    public static void deleteFileTask(final String chave) throws Exception {

        String dirRoot = "/root/.jenkins/jobs/";

        File[] diretorios = new File(dirRoot).listFiles(new FilenameFilter() {
            @Override
            public boolean accept(File dir, String name) {
                return name.contains(chave);
            }
        });

        if(diretorios.length > 0){
            FileUtils.deleteDirectory(diretorios[0]);
        }


    }

    public static void buildTask(final String chave, final String id_mailing, final String urlJenkins) throws Exception {
        postURL(urlJenkins + "/job/" + NOME_TASK_PADRAO + "_" + chave + "_" + id_mailing + "/build/", false);
    }

    public static void createTaskMailing(final String chave, final String id_mailing, final String cron, final String urlJenkins,
                                         final String urlMailing, final String chaveAntiga) throws Exception {
        JenkinsService.createTask(chave, urlMailing, id_mailing, cron, urlJenkins, chaveAntiga, null);
    }

    public static void createTaskPactoPay(final String chaveJobJenkins, final String id_mailing,
                                          final String cron, PactoPayEnvioEmailVO pactoPayEnvioEmailVO) throws Exception {
        final String urlJenkins = PropsService.getPropertyValue("urlJenkins");

        String nomePactoPay = NOME_TASK_PACTOPAY;
        String urlMailing = PropsService.getPropertyValue("urlMailing").replace("/ms", "/pactopay/comunicacao");
        if (pactoPayEnvioEmailVO != null && !UteisValidacao.emptyNumber(pactoPayEnvioEmailVO.getCodigo())) {
            urlMailing = PropsService.getPropertyValue("urlMailing").replace("/ms", "/pactopay/comunicacaoemail");
            nomePactoPay = NOME_TASK_PACTOPAY_LISTA;
        }
        createTask(chaveJobJenkins, urlMailing, id_mailing, cron, urlJenkins, chaveJobJenkins, nomePactoPay);
    }

    public static void buildTaskPactoPay(final String chave, final String id_mailing,
                                         PactoPayEnvioEmailVO pactoPayEnvioEmailVO) throws Exception {
        final String urlJenkins = PropsService.getPropertyValue("urlJenkins");
        if (pactoPayEnvioEmailVO != null && !UteisValidacao.emptyNumber(pactoPayEnvioEmailVO.getCodigo())) {
            postURL(urlJenkins + "/job/" + NOME_TASK_PACTOPAY_LISTA + "_" + chave + "_" + id_mailing + "/build", true);
        } else {
            postURL(urlJenkins + "/job/" + NOME_TASK_PACTOPAY + "_" + chave + "_" + id_mailing + "/build", true);
        }
    }

    public static void deleteTaskPactoPay(final String chaveJobJenkins, final String urlJenkins) throws Exception {
        postURL(urlJenkins + "/job/" + chaveJobJenkins + "/doDelete", true);
    }

    private static StringBuilder getConsulta() {
        StringBuilder s = new StringBuilder();
        s.append("select md.codigo,md.vigenteate,ma.cron,ma.ultimaexecucao,ma.ocorrencia,md.tipoagendamento,md.meiodeenvio,datainicial ");
        s.append("from maladireta md ");
        s.append("left outer join mailingagendamento ma on ma.maladireta = md.codigo ");
        s.append("where md.excluida = false AND ((md.codigo in (select maladireta from mailingagendamento ");
        s.append("                       where (ocorrencia = 1 and ultimaexecucao is not null) ");
        s.append("                             or (ocorrencia = 1 and  ultimaexecucao is null and ( current_date - datainicial > '1 day')) ");
        s.append("       )) ");
        s.append("      or (coalesce(md.vigenteate,current_date) < current_date)) order by datainicial ");
        return s;
    }

    public static void limparJobsAntigos(final Connection con, final String key) throws Exception {
        StringBuilder s = getConsulta();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(s.toString(), con);
        String url = PropsService.getPropertyValue("ulrJenkins");
        while (rs.next()) {
            try {
                MeioEnvio meio = MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meiodeenvio"));
                TipoAgendamentoEnum tipo = TipoAgendamentoEnum.getTipo(rs.getInt("tipoagendamento"));
                int codigo = rs.getInt("codigo");
                final String id_mailing = String.format("%s_%s_%s", meio.getDescricao(), tipo.name(), codigo);
                Uteis.logar(null, "Vou tentar excluir Jenkins -> " + id_mailing);
                deleteTask(key, id_mailing, url, null);

                SuperFacadeJDBC.executarConsultaUpdate("UPDATE maladireta SET excluida = TRUE WHERE codigo = " + codigo, con);
            } catch (Exception ignored) {
            }
        }
    }

    private static StringBuilder getConsultaAgendamentosVigentes() {
        StringBuilder s = new StringBuilder();
        s.append("select md.codigo,md.vigenteate,ma.cron,ma.ultimaexecucao,ma.ocorrencia,md.tipoagendamento,md.meiodeenvio,datainicial ");
        s.append("from maladireta md ");
        s.append("inner join mailingagendamento ma on ma.maladireta = md.codigo ");
        s.append("where coalesce(md.excluida,false) = false AND ((md.codigo in (select maladireta from mailingagendamento ");
        s.append("                       where ocorrencia <> 1 ");
        s.append("       )) ");
        s.append("      and (md.vigenteAte is null or coalesce(md.vigenteate,current_date) > current_date)) order by datainicial ");
        return s;
    }

    public static void recriarJobsVigentes(final Connection con, final String key) throws Exception {
        StringBuilder s = getConsultaAgendamentosVigentes();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(s.toString(), con);

        final String urlJenkins = PropsService.getPropertyValue("urlJenkins");
        final String urlMailing = PropsService.getPropertyValue("urlMailing");
        while (rs.next()) {
            try {
                MeioEnvio meio = MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meiodeenvio"));
                TipoAgendamentoEnum tipo = TipoAgendamentoEnum.getTipo(rs.getInt("tipoagendamento"));
                Integer codigo = rs.getInt("codigo");
                final String id_mailing = String.format("%s_%s_%s", meio.getDescricao(), tipo.name(), codigo);
                final String chave = String.format("%s_%s_%s", key,
                    meio.getDescricao(),
                    tipo.name());

                Uteis.logar(null, "Vou tentar excluir Jenkins -> " + id_mailing);

                createTask(chave,
                        urlMailing,
                        codigo.toString(),
                        rs.getString("cron"),
                        urlJenkins,
                        chave, null);

            } catch (Exception ignored) {
                Uteis.logar(ignored, JenkinsService.class);
            }
        }
    }

    public static void limparJobsAntigosPactoPayComunicacao(final Connection con, final String key) {
        PactoPaySuperService pactoPaySuperService;
        PactoPayComunicacao pactoPayComunicacaoDAO;
        try {
            pactoPaySuperService = new PactoPaySuperService(con);
            pactoPayComunicacaoDAO = new PactoPayComunicacao(con);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("p.codigo, \n");
            sql.append("p.meioenvio, \n");
            sql.append("p.comunicacao, \n");
            sql.append("p.dataexecucao \n");
            sql.append("from pactopaycomunicacao p \n");
            sql.append("where p.jobexcluido = false \n");
            sql.append("and p.dataexecucao::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -5))).append("' \n");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            String urlJenkins = PropsService.getPropertyValue("ulrJenkins");
            while (rs.next()) {
                try {
                    PactoPayComunicacaoVO obj = new PactoPayComunicacaoVO();
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setComunicacao(rs.getString("comunicacao"));
                    obj.setMeioEnvio(MeioEnvio.getMeioEnvioPorCodigo(rs.getInt("meioenvio")));

                    String chaveJenkinsDTO = obj.getPactoPayComunicacaoDTO().getChaveJobJenkins();
                    if (UteisValidacao.emptyString(chaveJenkinsDTO)) {
                        chaveJenkinsDTO = pactoPaySuperService.obterChaveJobJenkins(key, obj.getMeioEnvio());
                    }

                    final String chaveJobJenkins = (chaveJenkinsDTO + "_" + obj.getCodigo().toString());
                    Uteis.logar(null, "Vou tentar excluir Jenkins -> " + chaveJobJenkins);
                    deleteTaskPactoPay(chaveJobJenkins, urlJenkins);

                    pactoPayComunicacaoDAO.alteraJobExcluido(true, obj.getCodigo());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex){
            ex.printStackTrace();
        } finally {
            pactoPaySuperService = null;
            pactoPayComunicacaoDAO = null;
        }
    }

    public static void limparJobsAntigosPactoPayEnvioEmail(final Connection con, final String key) {
        PactoPayEnvioEmail pactoPayEnvioEmailDAO;
        try {
            pactoPayEnvioEmailDAO = new PactoPayEnvioEmail(con);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("p.codigo, \n");
            sql.append("p.dados \n");
            sql.append("from pactopayenvioemail p \n");
            sql.append("where p.jobexcluido = false \n");
            sql.append("and p.status = ").append(PactoPayEnvioEmailStatusEnum.ENVIADO_SENDY.getId()).append(" \n");
            sql.append("and p.dataregistro::date >= '").append(Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -10))).append("' \n");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            String urlJenkins = PropsService.getPropertyValue("ulrJenkins");
            while (rs.next()) {
                try {
                    PactoPayEnvioEmailVO obj = new PactoPayEnvioEmailVO();
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setDados(rs.getString("dados"));

                    String chaveJenkinsDTO = obj.getPactoPayEnvioEmailDadosDTO().getChaveJobJenkins();
                    final String chaveJobJenkins = (chaveJenkinsDTO + "_" + obj.getCodigo().toString());
                    Uteis.logar(null, "Vou tentar excluir Jenkins -> " + chaveJobJenkins);
                    deleteTaskPactoPay(chaveJobJenkins, urlJenkins);

                    pactoPayEnvioEmailDAO.alteraJobExcluido(true, obj.getCodigo());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        } catch (Exception ex){
            ex.printStackTrace();
        } finally {
            pactoPayEnvioEmailDAO = null;
        }
    }

    public static void main(String[] args) throws IOException, URISyntaxException {

        try {
            DAO oamd = new DAO();
            List<String> chaves = oamd.buscarListaChaves();
            for (String k : chaves) {

                Connection c = null;
                try {
                    deleteFileTask(k);
                    c = oamd.obterConexaoEspecifica(k);
                    recriarJobsVigentes(c, k);
                } finally {
                    if (c != null) {
                        c.close();
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(JenkinsService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

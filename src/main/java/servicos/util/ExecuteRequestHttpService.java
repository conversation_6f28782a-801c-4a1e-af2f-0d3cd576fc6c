/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.util;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import com.amazonaws.util.IOUtils;
import com.sun.xml.fastinfoset.stax.events.Util;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.oamd.dto.BackupClienteDTO;
import org.apache.commons.io.FileUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

import javax.net.ssl.*;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.security.cert.Certificate;
import javax.security.cert.X509Certificate;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import java.util.zip.GZIPInputStream;

/**
 *
 * <AUTHOR>
 */
public class ExecuteRequestHttpService implements Serializable {

    private static final long serialVersionUID = -7110880934684074185L;
    public int connectTimeout = 0;
    public int readTimeout = 0;
    public static final String METODO_POST = "POST";
    public static final String METODO_GET = "GET";
    public static final String METODO_PUT = "PUT";
    public static final String METODO_DELETE = "DELETE";
    public static final String ENCODE_UTF8 = "UTF-8";

    public static int CONNECT_TIMEOUT = 3000;
    public static int READ_TIMEOUT = 30000;

    static {
        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

            }

            @Override
            public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

            }

            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }

            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }
        }};

        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance("TLS");
            Uteis.logarDebug("Starting TLS ALL VALID HOSTS...");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            Uteis.logarDebug("Done. Started TLS ALL VALID HOSTS!!!");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static String obterIPExterno() {
        String urlRequest = "https://app.pactosolucoes.com.br/ip/v2.php";
        String retorno;
        try {
            String ipSessao = (String) JSFUtilities.getFromSession("ip");
            if (ipSessao != null && !ipSessao.isEmpty()
                    && !ipSessao.equals("N/C")
                    && !ipSessao.toUpperCase().contains("N")
                    && !ipSessao.toUpperCase().contains("C")) {
                return ipSessao;
            } else {
                retorno = executeHttpRequest(urlRequest, null);
                if (retorno != null && !retorno.isEmpty()) {
                    retorno = retorno.replace("\n", "");
                    return retorno;
                }
            }
        } catch (Exception ex) {
            retorno = "SEM_IP";
        }
        return retorno;
    }

    public static String executeRequest(String urlRequest, Map<String, String> params) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public static String executeRequestCupomDesconto(String urlRequest, String corpo, Map<String, String> headers) throws IOException {

        StringEntity entity = new StringEntity(corpo, "UTF-8");
        HttpPost httpPost = new HttpPost(urlRequest);
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json;");
        for (String key: headers.keySet()) {
            httpPost.setHeader(key, headers.get(key));
        }

        HttpClient client = ExecuteRequestHttpService.createConnector();
        HttpResponse response = client.execute(httpPost);

        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity());
        return responseBody;
    }

    public static int executeRequestPutWebHookRD(String urlRequest, String corpo, Map<String, String> headers) throws IOException {

        StringEntity entity = new StringEntity(corpo, "UTF-8");
        HttpPut httpPut = new HttpPut(urlRequest);
        httpPut.setEntity(entity);
        httpPut.setHeader("Content-Type", "application/json");
        for (String key: headers.keySet()) {
            httpPut.setHeader(key, headers.get(key));
        }

        HttpClient client = ExecuteRequestHttpService.createConnector();

        HttpResponse response = client.execute(httpPut);
        String responseBody = EntityUtils.toString(response.getEntity());
        return response.getStatusLine().getStatusCode();
    }

    public static int executeRequestAddWebHookRD(String urlRequest, String corpo, Map<String, String> headers) throws IOException {

        StringEntity entity = new StringEntity(corpo, "UTF-8");
        HttpPost httpPost = new HttpPost(urlRequest);
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        for (String key: headers.keySet()) {
            httpPost.setHeader(key, headers.get(key));
        }

        HttpClient client = ExecuteRequestHttpService.createConnector();

        HttpResponse response = client.execute(httpPost);
        String responseBody = EntityUtils.toString(response.getEntity());
        return response.getStatusLine().getStatusCode();
    }

    public static String executeRequestGetWebHookRD(String urlRequest, Map<String, String> headers) throws IOException {

        HttpGet httpGet = new HttpGet(urlRequest);
        httpGet.setHeader("Content-Type", "application/json");
        for (String key: headers.keySet()) {
            httpGet.setHeader(key, headers.get(key));
        }

        HttpClient client = ExecuteRequestHttpService.createConnector();

        HttpResponse response = client.execute(httpGet);
        int codeResponse = response.getStatusLine().getStatusCode();
        return EntityUtils.toString(response.getEntity());
    }

    public static String post(String url, String body, Map<String, String> headers) throws IOException, RequestException {
        headers.put("Content-Type", "application/json");
        return executeHttpRequest(url, body, headers, METODO_POST, ENCODE_UTF8, true);
    }

    public static String patch(String url, String body, Map<String, String> headers, String encode) throws IOException, RequestException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPatch httpPatch = new HttpPatch(url);
            httpPatch.setHeader("Content-Type", "application/json");
            for (Map.Entry<String, String> entry: headers.entrySet()) {
                httpPatch.setHeader(entry.getKey(), entry.getValue());
            }

            if(!Util.isEmptyString(encode)) {
                StringEntity entity = new StringEntity(body, encode);
                httpPatch.setEntity(entity);
            } else {
                httpPatch.setEntity(new StringEntity(body));
            }

            try (CloseableHttpResponse response = httpClient.execute(httpPatch)) {
                int statusCode = response.getStatusLine().getStatusCode();

                String responseBody = EntityUtils.toString(response.getEntity());
                if (statusCode == 200) {
                    System.out.println(responseBody);
                    return responseBody;
                } else {
                    String errorReturn = "PATCH request failed with status code: " + statusCode;
                    System.out.println(errorReturn);
                    throw new RequestException(responseBody);
                }
            }
        }
    }

    public static byte[] post(String url, String body, Map<String, String> headers, String encode) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }


            if (!Util.isEmptyString(encode)) {
                StringEntity entity = new StringEntity(body, encode);
                httpPost.setEntity(entity);
            } else {
                httpPost.setEntity(new StringEntity(body));
            }

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {

                //tratamento erro
                int status = response.getStatusLine().getStatusCode();
                if (status != 200) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject json = new JSONObject(responseBody);
                    throw new Exception("Erro ao gerar Certificado PFX: " + json.optString("error"));
                }

                HttpEntity entity = response.getEntity();

                if (entity != null) {
                    // Get the response as an InputStream
                    try (InputStream inputStream = entity.getContent()) {

//                       TESTAR ARQUIVO
//                        String filePath = "C:\\Desenv\\Certificados\\teste\\arquivo.pfx";
//                        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
//                            byte[] buffer = new byte[4096];
//                            int bytesRead;
//                            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                                outputStream.write(buffer, 0, bytesRead);
//                            }
//                        }

                        byte[] byteArray = readFully(inputStream);
                        EntityUtils.consume(entity);
                        return byteArray;
                    }
                }
            } catch (Exception e) {
                throw e;
            }
        }
        return null;
    }

    public static byte[] readFully(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192]; // Adjust the buffer size as needed
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        return byteArrayOutputStream.toByteArray();
    }

    public static String delete(String url, Map<String, String> headers) throws IOException, RequestException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpDelete httpDelete = new HttpDelete(url);
            httpDelete.setHeader("Content-Type", "application/json");
            for (Map.Entry<String, String> entry: headers.entrySet()) {
                httpDelete.setHeader(entry.getKey(), entry.getValue());
            }

            try (CloseableHttpResponse response = httpClient.execute(httpDelete)) {
                int statusCode = response.getStatusLine().getStatusCode();

                String responseBody = EntityUtils.toString(response.getEntity());
                if (statusCode == 200) {
                    Uteis.logarDebug(responseBody);
                    return responseBody;
                } else {
                    String errorReturn = "DELETE request failed with status code: " + statusCode;
                    Uteis.logarDebug(errorReturn);
                    throw new RequestException(responseBody);
                }
            }
        }
    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode) throws IOException {
        return executeHttpRequest(urlRequest, corpo, headers, metodo, encode, false);
    }

    public static String executeHttpRequest(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode, boolean exceptionOnError) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : headers.keySet()){
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        if(corpo != null){
            conn.setDoOutput(true);
            OutputStream os = conn.getOutputStream();
            BufferedWriter wr = new BufferedWriter(
                    new OutputStreamWriter(os, "UTF-8"));
            wr.write(corpo);
            wr.flush();
            wr.close();
            os.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException  e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }

        InputStreamReader inputStreamReader = new InputStreamReader(in, Charset.forName(encode));
        if ("gzip".equals(conn.getContentEncoding())) {
            if (conn.getResponseCode() == 200 || conn.getResponseCode() == 201 || conn.getResponseCode() == 202) {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getInputStream()), encode);
            } else {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getErrorStream()), encode);
            }
        }

        BufferedReader rd = new BufferedReader(inputStreamReader);
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();

        if((conn.getResponseCode() < 200 || conn.getResponseCode() > 299) && exceptionOnError){
            throw new IOException(resposta.toString());
        }

        return resposta.toString();
    }

    public String executeHttpRequestWithTimeout(String urlRequest, String corpo, Map<String, String> headers, String metodo,
                                                       String encode, boolean exceptionOnError) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        if (this.connectTimeout != 0) {
            conn.setConnectTimeout(connectTimeout);
        }
        if (readTimeout != 0){
            conn.setReadTimeout(readTimeout);
        }
        for(String keyHeader : headers.keySet()){
            conn.setRequestProperty(keyHeader, headers.get(keyHeader));
        }
        conn.setRequestMethod(metodo);
        if(corpo != null){
            conn.setDoOutput(true);
            OutputStream os = conn.getOutputStream();
            BufferedWriter wr = new BufferedWriter(
                    new OutputStreamWriter(os, "UTF-8"));
            wr.write(corpo);
            wr.flush();
            wr.close();
            os.close();
        }
        // Pega a Resposta
        InputStream in = null;
        try{
            in = conn.getInputStream();
        }catch (IOException  e){
            if(conn.getResponseCode() != 200){
                in = conn.getErrorStream();
            }
        }

        InputStreamReader inputStreamReader = new InputStreamReader(in, Charset.forName(encode));
        if ("gzip".equals(conn.getContentEncoding())) {
            if (conn.getResponseCode() == 200 || conn.getResponseCode() == 201 || conn.getResponseCode() == 202) {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getInputStream()), encode);
            } else {
                inputStreamReader = new InputStreamReader(new GZIPInputStream(conn.getErrorStream()), encode);
            }
        }

        BufferedReader rd = new BufferedReader(inputStreamReader);
        String line;
        StringBuilder resposta = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta.append(line);
        }
        rd.close();

        if((conn.getResponseCode() < 200 || conn.getResponseCode() > 299) && exceptionOnError){
            throw new IOException(resposta.toString());
        }

        return resposta.toString();
    }

    public static String executeHttpRequest(String urlRequest, Map<String, String> params) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                            + paramName + "="
                            + URLEncoder.encode(valor, "iso-8859-1");
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public String executeRequestMock(String urlRequest, Map<String, String> params, Map<String, String> headers, String metodo, String encode, boolean retornoJSON) throws IOException, JSONException {
        String parametrosCodificados = "";
        if (params != null) {
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + valor;
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }
            }
        }
        return executeHttpRequestMock(urlRequest, parametrosCodificados, headers, metodo, encode, retornoJSON);
    }

    public String executeRequestInnerMock(String urlRequest, Map<String, String> params, String metodo, String encode, boolean retornoJSON) throws IOException, JSONException {
        Map<String, String> headers = new HashMap<String, String>();
        String parametrosCodificados = "";
        if (params != null) {
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + valor;
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }
            }
        }
        return executeHttpRequestMock(urlRequest, parametrosCodificados, headers, metodo, encode, retornoJSON);
    }


    public String executeRequestInner(String urlRequest, Map<String, String> params, String encode) throws IOException {
        String parametrosCodificados = "";
        if (params != null) {
            Uteis.logar(null, params.toString());
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (connectTimeout != 0) {
            conn.setConnectTimeout(connectTimeout);
        }
        if (readTimeout != 0){
            conn.setReadTimeout(readTimeout);
        }
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encode));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        Uteis.logar(null, resposta);
        return resposta;
    }

    public String executeRequestInner(String urlRequest, Map<String, String> params) throws IOException {
        return executeRequestInner(urlRequest, params, "ISO-8859-1");
    }
//    public static void main(String... args) {
//        try {
//            Map<String, String> mapa = new HashMap();
//            mapa.put("op", "createNOTF");
//            mapa.put("desc", "atualizar offline");
//            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss",
//                    Calendario.getDefaultLocale());
//            mapa.put("dti", df.format(Calendario.hoje()));
//            mapa.put("dtf", df.format(Calendario.fimDoDia(Calendario.hoje())));
//            mapa.put("tipoNOTF", "WS");
//            mapa.put("chave", args[0]);
//            mapa.put("localAcesso", "");
//            ExecuteRequestHttpService.executeRequest(args[1], mapa);
//        } catch (Exception ex) {
//            Logger.getLogger(ExecuteRequestHttpService.class.getName()).log(Level.SEVERE, null, ex);
//        }
//    }

    public static String executeRequest(String urlRequest, Map<String, String> params, Boolean encodeParams, String charsetResponse) throws  IOException{
        String parametrosCodificados = "";
        if (params != null) {
            Set<String> s = params.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = params.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + (encodeParams ? URLEncoder.encode(valor, "iso-8859-1") : valor);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(charsetResponse != null ? new InputStreamReader(conn.getInputStream(), charsetResponse) : new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }
    
    public static String executeRequestGET(final String urlRequest, final Map<String, String> paramsHeader) throws IOException {
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        if (paramsHeader != null){
            Set<String> setHeader = paramsHeader.keySet();
            for (String k : setHeader){
                conn.setRequestProperty(k, paramsHeader.get(k));
            }
        }
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }

    public static String executeHttpRequestGETEncode(final String urlRequest, final Map<String, String> paramsHeader, String encode, int connectTimeout, int readTimeout) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();

        conn.setConnectTimeout(connectTimeout); // Timeout para estabelecer a conexão com o servidor mock (em milissegundos)
        conn.setReadTimeout(readTimeout); // Timeout para estabelecer a resposta do servidor mock (em milissegundos)

        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.forName(encode)));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }

    public static String executeHttpRequestGETEncode(final String urlRequest, final Map<String, String> paramsHeader, String encode) throws IOException{
        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), Charset.forName(encode)));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        rd.close();
        return resposta;
    }


    public static String executeHttpRequestGenerico(String urlRequest, Map<String, String> paramsHeader, Map<String, String> paramsCorpo, String metodo, String encode, String encodeResponse) throws IOException {
        if (encodeResponse == null) {
            encodeResponse = encode;
        }

        String parametrosCodificados = "";
        if (paramsCorpo != null) {
            Set<String> s = paramsCorpo.keySet();
            String separador = "";

            for (String paramName : s) {
                String valor = paramsCorpo.get(paramName);
                if (valor != null) {
                    parametrosCodificados +=
                            separador
                                    + paramName + "="
                                    + URLEncoder.encode(valor, encode);
                    if (separador.isEmpty()) {
                        separador = "&";
                    }
                }

            }
        }

        // Envia parametros
        URL url = new URL(urlRequest +"?" + parametrosCodificados);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }

        conn.setRequestMethod(metodo);
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
//        wr.write(parametrosCodificados);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encodeResponse));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }


    public static String executeHttpRequestNFSe(String urlRequest, Map<String, String> paramsHeader, String parametros, String metodo, String encode, String encodeResponse) throws IOException {
        if (encodeResponse == null) {
            encodeResponse = encode;
        }

        // Envia parametros
        URL url = new URL(urlRequest);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        for(String keyHeader : paramsHeader.keySet()){
            conn.setRequestProperty(keyHeader, paramsHeader.get(keyHeader));
        }

        conn.setRequestMethod(metodo);
        conn.setDoOutput(true);

        OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
        wr.write(parametros);
        wr.flush();

        // Pega a Resposta
        BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream(), encodeResponse));
        String line;
        String resposta = "";
        while ((line = rd.readLine()) != null) {
            // Processa linha a linha
            resposta += line + "\n";
        }

        wr.close();
        rd.close();
        return resposta;
    }

    public static String executeHttpRequestMock(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode, boolean retornoJSON) throws JSONException, IOException {
        //        no rest
        if (corpo == null) {
            corpo = "";
        }

        JSONObject json = new JSONObject();
        json.put("tipoMock", "requestMock");

        JSONObject jsonInfo = new JSONObject();
        jsonInfo.put("urlRequest", urlRequest);
        jsonInfo.put("corpo", corpo);
        jsonInfo.put("metodo", metodo);
        jsonInfo.put("encode", encode);
        jsonInfo.put("retornoJSON", retornoJSON);

        JSONObject objHeader = new JSONObject();
        for (String keyHeader : headers.keySet()) {
            objHeader.put(keyHeader, headers.get(keyHeader));
        }
        jsonInfo.put("headers", objHeader);
        json.put("json", jsonInfo);

        final String urlConsultar = String.format("%s?mockcontent=%s", PropsService.getPropertyValue(PropsService.urlMockZWServer), Uteis.encriptar(json.toString(), "m0oOCk"));
        Map<String, String> headersMock = new HashMap<String, String>();
        return ExecuteRequestHttpService.executeHttpRequestGETEncode(urlConsultar, headersMock, "UTF-8");
    }

    public static String executeHttpRequestMockStone(String urlRequest, String corpo, Map<String, String> headers, String metodo, String encode, boolean retornoJSON,
                                                int connectTimeout, int readTimeout) throws JSONException, IOException {
        //        no rest
        if (corpo == null) {
            corpo = "";
        }

        JSONObject json = new JSONObject();
        json.put("tipoMock", "requestMockStone");

        JSONObject jsonInfo = new JSONObject();
        jsonInfo.put("urlRequest", urlRequest);
        jsonInfo.put("corpo", corpo);
        jsonInfo.put("metodo", metodo);
        jsonInfo.put("encode", encode);
        jsonInfo.put("retornoJSON", retornoJSON);

        JSONObject objHeader = new JSONObject();
        for (String keyHeader : headers.keySet()) {
            objHeader.put(keyHeader, headers.get(keyHeader));
        }
        jsonInfo.put("headers", objHeader);
        json.put("json", jsonInfo);

        final String urlConsultar = String.format("%s?mockcontent=%s", PropsService.getPropertyValue(PropsService.urlMockZWServer), Uteis.encriptar(json.toString(), "m0oOCk"));
        Map<String, String> headersMock = new HashMap<String, String>();

        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
        if (!UteisValidacao.emptyNumber(connectTimeout) && !UteisValidacao.emptyNumber(readTimeout)) {
            return executeRequestHttpService.executeHttpRequestGETEncode(urlConsultar, headersMock, "UTF-8", connectTimeout, readTimeout);
        }
        return executeRequestHttpService.executeHttpRequestGETEncode(urlConsultar, headersMock, "UTF-8");
    }

    public void executeRequestDownload(String urlRequest, OutputStream out) throws IOException {
        // Envia parametros
        URL url = new URL(urlRequest);
        URLConnection conn = url.openConnection();
        if (connectTimeout  != 0) {
            conn.setReadTimeout(connectTimeout);
        }
        if (readTimeout != 0){
            conn.setReadTimeout(readTimeout);
        }
        // Pega a Resposta
        InputStream in = conn.getInputStream();
        int b = -1;
        while ((b = in.read()) != -1) {
            out.write(b);
        }
    }

    public static String get(String urlRequest, Map<String, String> headers) throws IOException, RequestException {
        return executeRequestGet(urlRequest, headers);
    }

    public static String executeRequestGet(String urlRequest, Map<String, String> headers) throws IOException, RequestException {
        HttpGet httpGet = new HttpGet(urlRequest);
        httpGet.setHeader("Content-Type", "application/json");
        for (String key: headers.keySet()) {
            httpGet.setHeader(key, headers.get(key));
        }
        try (CloseableHttpClient client = ExecuteRequestHttpService.createConnector()) {
            HttpResponse response = client.execute(httpGet);
            int codeResponse = response.getStatusLine().getStatusCode();
            if (codeResponse >= 200 && codeResponse <= 299) {
                return EntityUtils.toString(response.getEntity());
            } else {
                throw new RequestException(EntityUtils.toString(response.getEntity()));
            }
        }
    }

    public static byte[] obterByteFromUrl(String urlDownload, Integer connectTimeout, Integer readTimeout) throws IOException {
        if (UteisValidacao.emptyNumber(connectTimeout)) {
            connectTimeout = 10000;
        }
        if (UteisValidacao.emptyNumber(readTimeout)) {
            readTimeout = 10000;
        }
        URL url = new URL(urlDownload);
        URLConnection urlConn = url.openConnection();
        urlConn.setConnectTimeout(connectTimeout);
        urlConn.setReadTimeout(readTimeout);
        urlConn.setAllowUserInteraction(false);
        urlConn.setDoOutput(true);
        InputStream inStream = urlConn.getInputStream();
        return IOUtils.toByteArray(inStream);
    }

    public static CloseableHttpClient createConnector() {
        return createConnector(null);
    }

    public static CloseableHttpClient createConnector(RequestConfig requestConfig) {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

                    }

                    @Override
                    public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

                    }

                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };

        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return HttpClients.custom().setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).setDefaultRequestConfig(
                requestConfig != null ? requestConfig : RequestConfig.DEFAULT).
                setSslcontext(sc).build();
    }

    public static CloseableHttpClient createConnector(String path, String senha) {
        try {
            //Certificado privado Pacto
            File arqCert = new File(ExecuteRequestHttpService.class.getResource(path).toURI());
            InputStream keyStoreData = new FileInputStream(arqCert);
            return createConnector(keyStoreData, senha);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String executePostComCertificado(String url, String body, Map<String, String> headers, String pathCert, String pKey, String senha) {
        return executeComCertificado(url, body, headers, pathCert, pKey, senha, "POST");
    }

    public static String executeComCertificado(String url, String body, Map<String, String> headers, String pathCert, String pKey, String senha, String metodo) {
        HttpURLConnection connection = null;
        JSONObject responseJson = new JSONObject();
        try {
            // URL de autenticação
            URL url2 = new URL(url);
            connection = (HttpURLConnection) url2.openConnection();
            HttpURLConnection finalConnection = connection;
            headers.keySet().forEach(key -> finalConnection.setRequestProperty(key, headers.get(key)));
            if (metodo != null && metodo.equalsIgnoreCase("PATCH")) {
                allowMethods("PATCH");
                connection.setRequestMethod("PATCH");
            } else {
                connection.setRequestMethod(metodo == null ? "POST" : metodo.toUpperCase());
            }
            connection.setDoOutput(true);

            // Carregar o certificado
            CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
            byte[] certBytes = pathCert.getBytes(StandardCharsets.UTF_8);
            try (ByteArrayInputStream certInputStream = new ByteArrayInputStream(certBytes)) {
                java.security.cert.X509Certificate certificate = (java.security.cert.X509Certificate) certFactory.generateCertificate(certInputStream);

                // Carregar chave privada
                PrivateKey privateKey = carregarChavePrivada(pKey);

                // Configurar o KeyStore com o certificado e a chave privada
                KeyStore keyStore = KeyStore.getInstance("JKS");
                keyStore.load(null, null);
                keyStore.setKeyEntry("alias", privateKey, senha.toCharArray(), new Certificate[]{certificate});

                // Configurar SSLContext com o KeyManagerFactory
                KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
                keyManagerFactory.init(keyStore, senha.toCharArray());

                SSLContext sslContext = SSLContext.getInstance("TLS");
                sslContext.init(keyManagerFactory.getKeyManagers(), null, new java.security.SecureRandom());

                SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
                if (connection instanceof HttpsURLConnection) {
                    ((HttpsURLConnection) connection).setSSLSocketFactory(sslSocketFactory);
                }
            }

            // Enviar o corpo
            if (body != null) {
                try (OutputStream outputStream = connection.getOutputStream()) {
                    outputStream.write(body.getBytes());
                }
            }

            // Obter o status code da resposta
            int statusCode = connection.getResponseCode();
            responseJson.put("statusCode", statusCode);

            // Ler resposta
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                responseJson.put("response", response.toString());
                return responseJson.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            try {
                if (connection != null) {
                    try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()))) {
                        StringBuilder response = new StringBuilder();
                        String line;
                        while ((line = bufferedReader.readLine()) != null) {
                            response.append(line);
                        }
                        responseJson.put("response_error", response.toString());
                    }
                }
            } catch (Exception ex11) {
                ex11.printStackTrace();
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return responseJson.toString();
    }

    private static void allowMethods(String... methods) {
        try {
            Field methodsField = HttpURLConnection.class.getDeclaredField("methods");

            Field modifiersField = Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(methodsField, methodsField.getModifiers() & ~Modifier.FINAL);

            methodsField.setAccessible(true);

            String[] oldMethods = (String[]) methodsField.get(null);
            Set<String> methodsSet = new LinkedHashSet<>(Arrays.asList(oldMethods));
            methodsSet.addAll(Arrays.asList(methods));
            String[] newMethods = methodsSet.toArray(new String[0]);

            methodsField.set(null/*static field*/, newMethods);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new IllegalStateException(e);
        }
    }

    private static PrivateKey carregarChavePrivada(String keyPath) throws Exception {
        // Remover cabeçalhos e rodapés da chave PEM
        keyPath = keyPath.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
            byte[] decodedKey = Base64.getDecoder().decode(keyPath);

            // Recuperar a chave privada
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
            return keyFactory.generatePrivate(keySpec);
    }

    private static byte[] lerTodosBytes(FileInputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }
        return byteArrayOutputStream.toByteArray();
    }


    public static CloseableHttpClient createConnector(byte[] byteArray, String nomeArquivo, String extensao, String senha) {
        File arquivoTemp = new File("");
        try {
            arquivoTemp = File.createTempFile(String.format("cert-%s-%s", Calendario.hoje().getTime(), nomeArquivo), ("." + extensao));
            FileUtils.writeByteArrayToFile(arquivoTemp, byteArray);
            try (InputStream keyStoreData = new FileInputStream(arquivoTemp)) {
                return createConnector(keyStoreData, senha);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (arquivoTemp.exists()) {
                boolean excluiuArqTemp = arquivoTemp.delete(); //excluir imediato
                if (!excluiuArqTemp) {
                    arquivoTemp.deleteOnExit();  // Garante a exclusão ao sair se a exclusão imediata falhar
                }
            }
        }
        return null;
    }

    public static CloseableHttpClient createConnector(InputStream keyStoreData, String senha) {
        try {
            //Certificado privado
            char[] keyPassword = senha.toCharArray();
            KeyStore keyStore = KeyStore.getInstance("PKCS12");

            keyStore.load(keyStoreData, keyPassword);

            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(new TrustAllStrategy())
                    .loadKeyMaterial(keyStore, keyPassword)
                    .build();

            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());

            return HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setSSLSocketFactory(sslsf)
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static BackupClienteDTO consumirEndPointDadosParaTestOAMD(String url) throws Exception {
        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpPost post = new HttpPost(url);
        post.setHeader("Accept", "application/json");
        post.setHeader("headerValue", "HeaderInformation");
        StringEntity entity = new StringEntity("UTF-8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);

        if (response.getStatusLine().getStatusCode() == 200) {
            String resposta = EntityUtils.toString(response.getEntity(), "UTF-8");
            JSONObject jsonRetorno = new JSONObject(resposta);
            return new BackupClienteDTO(new JSONObject(jsonRetorno.getString("empresa")));
        } else {
            throw new Exception("Não foi possivel conectar ao OAMD");
        }
    }

    public static String gerarURLConexaoOAMD(String chave) throws Exception {
        try {
            return PropsService.getPropertyValue(PropsService.urlOamd)+"/prest/empresa/obterDadosParaTeste?chave=" + chave;
        } catch (Exception e) {
            Uteis.logar(e, ConfiguracaoSistema.class);
            throw new Exception("Não foi possivel capturar a chave do banco");
        }
    }

}


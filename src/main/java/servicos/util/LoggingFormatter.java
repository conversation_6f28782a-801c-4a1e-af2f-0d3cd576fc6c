/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.util;

import java.text.DateFormat;
import java.util.Date;
import java.util.logging.Formatter;
import java.util.logging.LogRecord;

/**
 *
 * <AUTHOR>
 */
public class LoggingFormatter extends Formatter {

    private StringBuilder buf = new StringBuilder();
    private boolean printDetails = true;

    @Override
    public String format(LogRecord record) {

        if (printDetails) {
            buf.append(DateFormat.getDateTimeInstance().format(new Date()));
            buf.append(" ");
            buf.append(record.getSourceClassName()).append(" ").append(record.getSourceMethodName()).append("\n");
            buf.append("===> ").append(record.getLevel().getName()).append(": ");
        }
        buf.append(formatMessage(record));
        if (record.getThrown() != null) {
            StringBuilder stackTrace = new StringBuilder();
            stackTrace.append(record.getThrown().getStackTrace()[0]);
            for (int i = 1; i < 5; i++) {
                stackTrace.append("\n").append(record.getThrown().getStackTrace()[i]);
            }
            buf.append(stackTrace);
        }

        buf.append('\n');

        return buf.toString();
    }

    public StringBuilder getBuf() {
        return buf;
    }

    public void setPrintDetails(boolean printDetails) {
        this.printDetails = printDetails;
    }
}

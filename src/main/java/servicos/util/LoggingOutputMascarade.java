/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Handler;
import java.util.logging.Logger;
import java.util.logging.StreamHandler;

/**
 * <AUTHOR>
 */
public class LoggingOutputMascarade {

    static final Logger logger = Logger.getLogger("");
    private final LoggingFormatter lf = new LoggingFormatter();
    List<Handler> oldHandlers = new ArrayList<>();

    public StringBuilder getResult() {
        return lf.getBuf();
    }

    public void setPrintDetails(boolean printDetails) {
        lf.setPrintDetails(printDetails);
    }

    public void init() {
        Handler[] defaultHandlers = logger.getHandlers();
        Collections.addAll(oldHandlers, defaultHandlers);
        StreamHandler hOut = new StreamHandler(System.out, lf);
        logger.addHandler(hOut);
        StreamHandler hErr = new StreamHandler(System.err, lf);
        logger.addHandler(hErr);
    }

    public void finalizing() {
        Handler[] temp = logger.getHandlers();
        for (Handler handler : temp) {
            handler.flush();
            handler.close();
            logger.removeHandler(handler);
        }
        for (Handler handler : oldHandlers) {
            logger.addHandler(handler);
        }
        oldHandlers.clear();
        oldHandlers = null;
    }
}

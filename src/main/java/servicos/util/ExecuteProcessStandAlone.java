/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.util;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class ExecuteProcessStandAlone {

    public static void main(String... args) {
        if (args.length == 0) {
            args = new String[]{
                        "r2",
                        "br.com.pactosolucoes.atualizadb.processo.ExecutarProcessos",
                        "migracaoVersao686"
                    };
        }
        String chave = args[0];
        Connection con;
        try {
            con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(con);
            String classe = args[1];
            String metodo = args[2];
            Class clazz = Class.forName(classe);
            Object obj = clazz.newInstance();
            UtilReflection.invoke(obj, metodo);
        } catch (Exception ex) {
            Logger.getLogger(ExecuteProcessStandAlone.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.util;

import java.io.IOException;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ThreadRequest extends Thread {

    private String url;
    private Map params;
    private ExecuteRequestHttpService serviceRequest = new ExecuteRequestHttpService();

    public ThreadRequest(final String url, Map params) {
        this.url = url;
        this.params = params;
    }

    @Override
    public void run() {
        serviceRequest.connectTimeout = 5000;
        serviceRequest.readTimeout = 15000;
        try {
            serviceRequest.executeRequestInner(url, params);
        } catch (Exception ex) {
            Logger.getLogger(ThreadRequest.class.getName()).log(Level.SEVERE, "Erro ao processar Requisição da URL -> " + url + params, ex);
        }
    }
}

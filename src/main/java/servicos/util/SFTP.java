/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.util;

import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.Vector;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class SFTP {

    private static final String ID_RSA = "/root/.ssh/id_rsa";
    private String host;
    private String user;
    private String pwd;
    private String myIdRSA;
    private int port;
    
    public SFTP(final String host, final String user, final String pwd, final int port) {
        this.host = host;
        this.user = user;
        this.pwd = pwd;
        this.port = port;
    }

    public void setMyIdRSA(String myIdRSA) {
        this.myIdRSA = myIdRSA;
    }
    
    public JSch getInstance() throws JSchException {
        JSch jsch = new JSch();
        //
        if (myIdRSA != null) {
            File myRsa = new File(myIdRSA);
            if (myRsa.exists()) {
                jsch.addIdentity(myIdRSA);
            } else {
                Uteis.logar(String.format("SFTP.getInstance() -> ATENÇÃO! Arquivo de chave RSA %s informado não existe! ", myRsa.getAbsolutePath()));
            }
        } else {
            File serverRsa = new File(ID_RSA);
            if (serverRsa.exists()) {
                jsch.addIdentity(ID_RSA);
            }
        }
        return jsch;
    }

    public void list(final String pathOrigem) throws Exception {
            //
        Session session = getInstance().getSession(user, host, port);
        session.setPassword(pwd);

        session.setConfig("StrictHostKeyChecking", "no");

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp c = (ChannelSftp) channel;

        Uteis.logar("SFTP.list -> Listando diretório... " + pathOrigem);

        try {
            c.cd(pathOrigem);
            Vector files = c.ls(pathOrigem);

            for (Object obj : files) {
                LsEntry f = (LsEntry) obj;
                String nomeArq = f.getLongname();
                Uteis.logar(nomeArq);
            }
            if (files.isEmpty()){ 
                Uteis.logar("SFTP.list -> Nenhum arquivo encontrado!");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            throw e;

        } finally {
            channel.disconnect();
            session.disconnect();
        }
    }

    @Deprecated
    public void getFiles(final String pathOrigem, final String pathDestino,
            final String inicioArq) throws Exception {
        getFiles(pathOrigem, pathDestino, inicioArq, null, false, false);
    }

    public int countLocalFiles(final String pathDir) {
        return new File(pathDir).listFiles().length;
    }
    
    public Date getGreaterDateInLocalFiles(final String pathDir) {
        Date data = null;
        File[] listFiles = new File(pathDir).listFiles();
        for (int i = 0; i < listFiles.length; i++) {
            File file = listFiles[i];
            if (file.isFile()) {
                Date modified = new Date(file.lastModified());
                if (data == null) {
                    data = modified;
                } else if (modified.after(data)) {
                    data = modified;
                }
            }
        }
        return data;
    }
    
    public Date getGreaterDateInFiles(final Vector files) {
        Date data = null;
        for (Object obj : files) {
            LsEntry f = (LsEntry) obj;
            if (f.getFilename().replaceAll("\\.", "").isEmpty()) {
                continue;
            }
            SftpATTRS attrs = f.getAttrs();
            Date dateModify = new Date(attrs.getMTime() * 1000L);
            if (data == null){
                data = dateModify;
            }else if (dateModify.after(data)){
                data = dateModify;
            }
        }
        return data;
    }

    public void getFiles(final String pathOrigem, final String pathDestino,
                         final String inicioArq, final Date fromDate, boolean deleteOldFiles,
                         boolean verifyDifference) throws Exception {
        getFiles(pathOrigem, pathDestino, inicioArq, fromDate, deleteOldFiles, verifyDifference, null, null);
    }

    public void getFiles(final String pathOrigem, final String pathDestino,
            final String inicioArq, final Date fromDate, boolean deleteOldFiles,
            boolean verifyDifference, String arquivoRejeitado, String prefixoExtratoAdicional) throws Exception {
        //
        Session session = getInstance().getSession(user, host, port);
        session.setPassword(pwd);

        session.setConfig("StrictHostKeyChecking", "no");

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp c = (ChannelSftp) channel;

        Uteis.logar("SFTP.getFiles -> Vou listar e copiar arquivos do diretório... " + pathOrigem);
        int descartados = 0;
        int copiados = 0;
        int excluidos = 0;
        try {
            boolean temNovosArquivosRemotos = true;
            int nFilesLocal = countLocalFiles(pathDestino);
            int nFilesRemote = countFiles(pathOrigem, session, c);
            Uteis.logar("SFTP.getFiles -> Total de arquivos no diretorio LOCAL: " + nFilesLocal);
            Uteis.logar("SFTP.getFiles -> Total de arquivos no diretorio REMOTO: " + nFilesRemote);
            //
            if (temNovosArquivosRemotos) {
                c.cd(pathOrigem);
                Vector files = c.ls(pathOrigem);                
                for (Object obj : files) {
                    LsEntry f = (LsEntry) obj;
                    if (f.getAttrs().isDir()) continue;
                    //
                    boolean prefixoValido = (UteisValidacao.emptyString(inicioArq)
                            && (f.getFilename().toUpperCase().startsWith("RETMOV")
                            || f.getFilename().toUpperCase().startsWith("VISAERRO")
                            || f.getFilename().toUpperCase().startsWith("PGRCR")
                            || f.getFilename().toUpperCase().startsWith("R")
                            || f.getFilename().toUpperCase().startsWith("CANEL")));


                    if (!UteisValidacao.emptyString(inicioArq)) {
                        prefixoValido = f.getFilename().toLowerCase().contains(inicioArq.toLowerCase());
                    }

                    if (!UteisValidacao.emptyString(inicioArq) && user.contains(":") && !prefixoValido) {
                        prefixoValido = f.getFilename().contains(user.replace(":", ""));
                    }

                    if (prefixoValido && !UteisValidacao.emptyString(prefixoExtratoAdicional) &&
                            (!f.getFilename().toLowerCase().contains(prefixoExtratoAdicional.toLowerCase())) && (!f.getFilename().toLowerCase().contains("getnetextr".toLowerCase())))
                    {
                        prefixoValido = false;
                    }

                    //se o arquivo é válido (do convênio que está sendo processado)
                    //Se tem a informação de rejeitado... verificar se o arquivo contem no nome o identificador de inválido
                    //Getnet arquivo termina com... ".REJ"
                    boolean arquivoFoiRejeitado = false;
                    if (prefixoValido && !UteisValidacao.emptyString(arquivoRejeitado)) {
                        if (f.getFilename().toUpperCase().contains(arquivoRejeitado.toUpperCase())) {
                            arquivoFoiRejeitado = true;
                        } else {
                            prefixoValido = false;
                        }
                    }

                    //
                    if (prefixoValido) {
                        SftpATTRS attrs = f.getAttrs();
                        Date dateModify = new Date(attrs.getMTime() * 1000L);
                        if (fromDate != null) {
                            if (dateModify.before(fromDate)) {
                                descartados++;
                                if (deleteOldFiles) {
                                    final String fileName = pathOrigem + "/" + f.getFilename();
                                    try {
                                        Uteis.logar("SFTP.getFiles -> deleting Old File " + fileName);
                                        deleteFile(fileName, session, c);
                                        excluidos++;
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        Uteis.logar("SFTP.getFiles -> ERROR in deleting Old File "
                                                + fileName + " - " + e.getMessage());
                                    }
                                }
                                continue;
                            }
                        }
                        //
                        final String pathArqDestino = pathDestino + "/" + f.getFilename();
                        File fArqDestino = new File(pathArqDestino);
                        if (verifyDifference && fArqDestino.exists() 
                                && (dateModify.before(new Date(fArqDestino.lastModified())) 
                                    || dateModify.equals(new Date(fArqDestino.lastModified())))){//Local já tem arquivos mais recente
                            continue;
                        }
                        final String nomeArq = f.getLongname();
                        Uteis.logar("                 GETTING -> " + nomeArq);
                        c.get(f.getFilename(), pathDestino);
                        fArqDestino.setLastModified(dateModify.getTime());
                        fArqDestino = null;
                        copiados++;

                        if (arquivoFoiRejeitado) {
                            String fileName = "";
                            try {
                                fileName = pathOrigem + "/" + f.getFilename();
                                Uteis.logar("Vou deletar ARQUIVO REJEITADO remoto: " + fileName);
                                deleteFile(fileName, session, c);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                                Uteis.logar("Erro deletar ARQUIVO REJEITADO remoto:  " + fileName + " - " + ex.getMessage());
                            }
                        }
                    }
                }
                if (files.isEmpty()) {
                    Uteis.logar("SFTP.getFiles -> Nenhum arquivo encontrado!");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            Uteis.logarDebug("SFTP.getFiles | Erro: " + e.getMessage());
            throw e;
        } finally {
            channel.disconnect();
            session.disconnect();
            if (descartados > 0) {
                Uteis.logar(String.format("SFTP.getFiles -> ATENÇÃO! %s arquivos "
                        + "descartados e não foram copiados por serem antigos demais, antes de %s ",
                        descartados, Calendario.getData(fromDate, "dd/MM/yyyy")));
            }
            if (copiados > 0) {
                Uteis.logar("SFTP.getFiles -> Total de arquivos copiados: " + copiados);
            } else {
                Uteis.logar("SFTP.getFiles -> Nenhum arquivo copiado!");
            }
            if (excluidos > 0) {
                Uteis.logar("SFTP.getFiles -> Arquivos Antigos Excluidos: " + excluidos);
            }
        }
    }

    public int countFiles(final String pathOrigem) throws Exception {
        //
        Session session = getInstance().getSession(user, host, port);
        session.setPassword(pwd);
        session.setConfig("StrictHostKeyChecking", "no");

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp c = (ChannelSftp) channel;
        try {
            return countFiles(pathOrigem, session, c);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            Uteis.logar(null, "ERRO em countFiles -> " + e.getMessage());
            throw e;
        } finally {
            channel.disconnect();
            session.disconnect();
        }
    }

    public int countFiles(final String pathOrigem, Session session, ChannelSftp c) throws Exception {
        Uteis.logar("SFTP.countFiles -> Vou contar arquivos do diretório... " + pathOrigem);
        try {
            c.cd(pathOrigem);
            Vector files = c.ls(pathOrigem);
            int ignored = 0;
            if (files.size() >= 2) {
                for (int i = 0; i < 2; i++) {
                    LsEntry f = (LsEntry) files.get(i);
                    if (f.getFilename().replaceAll("\\.", "").isEmpty()) {
                        ignored++;
                    }
                }
            }
            int total = files.size() - ignored;
            Uteis.logar("SFTP.getFiles -> Total de arquivos no diretorio: " + total);
            return total;
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            throw e;
        }
    }

    /**
     *
     * @param arquivo       path do arquivo a ser enviado
     * @param pathDestino   pasta do ftp que será salvo o arquivo
     * @throws Exception
     */
    public void putFile(final String arquivo, final String pathDestino) throws Exception {
        putFile(arquivo, pathDestino, "sftp");
    }

    /**
     *
     * @param arquivo               path do arquivo a ser enviado
     * @param pathDestinoDirCreate  pasta do ftp que será criada(Sem o nome do arquivo)
     * @param pathDestino           pasta do ftp que será salvo o arquivo
     * @throws Exception
     */
    public void putFileWithMkdir(final String arquivo, final String pathDestinoDirCreate, final String pathDestino) throws Exception {
        putFileWithMkdir(arquivo, pathDestinoDirCreate, pathDestino, "sftp");
    }

    /**
     *
     * @param arquivo       path do arquivo a ser enviado
     * @param pathDestino   pasta do ftp que será salvo o arquivo
     * @param channelType   ftp ou sftp
     * @throws Exception
     */
    public void putFile(final String arquivo, final String pathDestino, String channelType) throws Exception {
        putFileWithMkdir(arquivo, null, pathDestino, channelType);
    }

    /**
     *
     * @param arquivo               path do arquivo a ser enviado
     * @param pathDestinoDirCreate  pasta do ftp que será criada(Sem o nome do arquivo)
     * @param pathDestino           pasta do ftp que será salvo o arquivo
     * @param channelType           ftp ou sftp
     * @throws Exception
     */
    private void putFileWithMkdir(final String arquivo, final String pathDestinoDirCreate, final String pathDestino, String channelType) throws Exception {
     putFileWithMkdir(arquivo, pathDestinoDirCreate, pathDestino, channelType, false);
    }

    public void putFileWithMkdir(final String arquivo, final String pathDestinoDirCreate, final String pathDestino, String channelType, boolean criarSubPastasAutomaticamente) throws Exception {
        //
        Session session = getInstance().getSession(user, host, port);
        session.setPassword(pwd);
        session.setConfig("StrictHostKeyChecking", "no");
        session.setTimeout(8000); //8 segundos tempo de espera para conectar ao servidor

        session.connect();

        Channel channel = session.openChannel(channelType);
        channel.connect();
        ChannelSftp c = (ChannelSftp) channel;

        if (!UteisValidacao.emptyString(pathDestinoDirCreate)) {
            if (criarSubPastasAutomaticamente) {
                try {
                    if (!UteisValidacao.emptyString(pathDestinoDirCreate)) {
                        if (!pathDestinoDirCreate.contains("/files/")) {
                            throw new Exception("Diretório de destino inválido! O caminho do diretório Deve conter o /files/ configurado");
                        }
                        try {
                            String[] pastas = pathDestinoDirCreate.split("/");
                            StringBuilder caminhoAtual = new StringBuilder();
                            for (String pasta : pastas) {
                                if (!pasta.isEmpty()) {  // Evita strings vazias no caminho
                                    caminhoAtual.append("/").append(pasta); // Constrói o caminho progressivamente

                                    try {
                                        // Verifica se o diretório existe
                                        c.stat(caminhoAtual.toString());
                                    } catch (SftpException e) {
                                        // Se o erro for 'no such file', significa que a pasta não existe, então cria
                                        if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                                            c.mkdir(caminhoAtual.toString());
                                        } else {
                                            throw e; // Lança a exceção se for outro erro
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                        }
                    }
                } catch (Exception e) {
                    throw e;
                }
            } else {
                try {
                    c.mkdir(pathDestinoDirCreate);
                } catch (Exception e) {
                }
            }
        }

        int mode = ChannelSftp.OVERWRITE;

        try {
            Uteis.logar(null, "PUT arquivo -> " + arquivo + " para diretório " + pathDestino);
            c.put(arquivo, pathDestino, mode);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            Uteis.logar(null, "ERRO em PUT arquivo -> " + e.getMessage());
            throw e;
        } finally {
            channel.disconnect();
            session.disconnect();
        }
    }

    public boolean deleteFile(final String arquivo) throws Exception {
        //
        Session session = getInstance().getSession(user, host, port);
        session.setPassword(pwd);
        session.setConfig("StrictHostKeyChecking", "no");

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp c = (ChannelSftp) channel;
        try {
            return deleteFile(arquivo, session, c);
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            Uteis.logar(null, "ERRO em deleteFile arquivo -> " + e.getMessage());
            throw e;
        } finally {
            channel.disconnect();
            session.disconnect();
        }
    }

    public boolean deleteFile(final String arquivo, Session session, ChannelSftp c) throws Exception {
        try {
            c.rm(arquivo);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            Logger.getLogger(SFTP.class.getName()).log(Level.SEVERE, null, e);
            Uteis.logar(null, "ERRO em deleteFile arquivo -> " + e.getMessage());
            throw e;
        }
    }
    
    private static int checkAck(InputStream in) throws IOException {
        int b = in.read();
        // b may be 0 for success,
        //          1 for error,
        //          2 for fatal error,
        //          -1
        if (b == 0) {
            return b;
        }
        if (b == -1) {
            return b;
        }

        if (b == 1 || b == 2) {
            StringBuffer sb = new StringBuffer();
            int c;
            do {
                c = in.read();
                sb.append((char) c);
            } while (c != '\n');
            if (b == 1) { // error
                Uteis.logar(sb.toString());
            }
            if (b == 2) { // fatal error
                Uteis.logar(sb.toString());
            }
        }
        return b;
    }
    
    public static Session getInstance(final String host, final String port, 
            final String user, final String pwd) throws Exception {
        JSch jsch = new JSch();        
        Session session = jsch.getSession(user, host, new Integer(port));
        session.setPassword(pwd);
        session.setConfig("StrictHostKeyChecking", "no");
        return session;
    }
    
    public static void enviarArquivo(File file, Session session, final String caminhoRemoto) throws Exception {
        //        
        session.connect();
        //
        // exec 'scp -t rfile' remotely
        String command = "scp -p -t " + caminhoRemoto;
        Channel channel = session.openChannel("exec");
        ((ChannelExec) channel).setCommand(command);

        // get I/O streams for remote scp
        OutputStream out = channel.getOutputStream();
        InputStream in = channel.getInputStream();

        channel.connect();

        if (checkAck(in) != 0) {
            throw new Exception("Erro ao conectar no servidor dos Serviços StandAlone");
        }

        // send "C0755 filesize filename", where filename should not include '/'
        long filesize = file.length();
        command = "C0755 " + filesize + " " + file.getName() + "\n";
        out.write(command.getBytes());
        out.flush();
        if (checkAck(in) != 0) {
            throw new Exception("Erro ao alterar permissão dos arquivos de Serviços StandAlone");
        }

        // send a content of lfile
        FileInputStream fis = new FileInputStream(file.getAbsolutePath());
        byte[] buf = new byte[1024];
        while (true) {
            int len = fis.read(buf, 0, buf.length);
            if (len <= 0) {
                break;
            }
            out.write(buf, 0, len); //out.flush();
        }
        fis.close();
        fis = null;
        // send '\0'
        buf[0] = 0;
        out.write(buf, 0, 1);
        out.flush();
        if (checkAck(in) != 0) {
            throw new Exception("Erro ao concluir o envio dos Serviços StandAlone");
        }

        out.close();

        channel.disconnect();
        session.disconnect();
    }

    public static void main(String... p) throws Exception {
//        SFTP s = new SFTP("prod2-gw-na.firstdataclients.com", "NAGW-KBUUQ001", "jeH8oT28H", 6522);
//        s.setMyIdRSA("/C:/Users/<USER>/Downloads/iweb-app9/id_rsa_z1/id_rsa");
//        s.list("/available");
//        
        SFTP s = new SFTP("sftp.pactosolucoes.com.br", "root", "pac!@#br12", 22);
        s.getFiles("/opt/ZW_GETNET/down/03-05-2017/", "/C:/opt/ZW_GETNET/down/26-04-2017/",
                null, Uteis.somarDias(Calendario.hoje(), -30), false, true);
//        s.countFiles("/opt/ZW_GETNET/down/22-04-2017/");
    }
}

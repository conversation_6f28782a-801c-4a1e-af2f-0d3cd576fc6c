package servicos.util.thread;

import java.util.Map;

/**
 * Interface para chamadas assíncronas.
 * Quando um callback deve ser executado em outra thread ou quando a mesma finaliza com sucesso ou falha.
 *
 * <AUTHOR>
 * @since 18/08/2018
 */
public interface Callback {

    /**
     * Executa uma determinada ação
     *
     * @throws Exception caso ocorra algum erro na execução do serviço
     */
    void call() throws Exception;

    /**
     * Os parâmetros transmitidos entre os callbacks
     *
     * @return Os parâmetros trocados entre os callbacks de ação, sucesso e falha
     */
    Map<String, Object> getParametros();

}

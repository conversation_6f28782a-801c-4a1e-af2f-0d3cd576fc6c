package servicos.util.thread;

import negocio.comuns.utilitarias.Uteis;

/**
 * Executor assíncrono para realizar tarefas em outra thread
 *
 * <AUTHOR>
 * @since 18/08/2018
 */
public final class ExecutorAssincronoUtil {

    // tempo padrão para a execução de uma tarefa
    private static final Long TEMPO_DEFAULT = 10000L;

    /**
     * Executa o callback informado em uma thread separada
     *
     * @param acao Objeto que guarda a ação que será executada em outra thread
     */
    public static void executarAssincrono(final Callback acao) {
        executarAssincrono(acao, null, null);
    }

    /**
     * Executa o callback informado em uma thread separada
     *
     * @param acao  Objeto que guarda a ação que será executada em outra thread
     * @param tempo Timeout de execução da chamada
     */
    public static void executarAssincrono(final Callback acao, final Long tempo) {
        executarAssincrono(acao, null, null, tempo);
    }

    /**
     * Executa o callback informado em uma thread separada
     *
     * @param acao      Objeto que guarda a ação que será executada em outra thread
     * @param onSucesso Ação executada quando a thread finaliza a ação atual com sucesso
     * @param onFalha   Ação executada quando ocorre algum erro durante a ação atual
     */
    public static void executarAssincrono(final Callback acao, final Callback onSucesso, final Callback onFalha) {
        executarAssincrono(acao, onSucesso, onFalha, TEMPO_DEFAULT);
    }

    /**
     * Executa o callback informado em uma thread separada
     *
     * @param acao      Objeto que guarda a ação que será executada em outra thread
     * @param onSucesso Ação executada quando a thread finaliza a ação atual com sucesso
     * @param onFalha   Ação executada quando ocorre algum erro durante a ação atual
     * @param tempo     Tempo para execução desta ação
     */
    public static void executarAssincrono(final Callback acao, final Callback onSucesso, final Callback onFalha, final Long tempo) {
        final Thread threadControladora = new Thread(getControladorAcao(acao, onSucesso, onFalha, tempo));
        threadControladora.setDaemon(true);
        threadControladora.start();
    }

    private static Runnable getControladorAcao(final Callback acao, final Callback onSucesso, final Callback onFalha, final Long tempo) {
        return new Runnable() {
            @Override
            public void run() {
                final Thread executor = new Thread(getExecutorAcao(acao, onSucesso, onFalha));
                executor.setDaemon(true);
                executor.start();
                esperarTimeout(executor, tempo, onFalha);
            }
        };
    }

    private static void esperarTimeout(Thread executor, Long tempo, Callback onFalha) {
        try {
            Thread.sleep(tempo);
            if (executor.isAlive()) {
                executor.interrupt();

                if (onFalha != null) {
                    onFalha.call();
                }
            }
        } catch (Exception e) {
            // nada a fazer neste caso... interrompendo tudo...
            Uteis.logar(e, ExecutorAssincronoUtil.class);
            try {
                executor.interrupt();
                if (onFalha != null) {
                    onFalha.call();
                }
            } catch (Exception e1) {
                // ignorada...
                Uteis.logar(e1, ExecutorAssincronoUtil.class);
            }
        }
    }

    private static Runnable getExecutorAcao(final Callback acao, final Callback onSucesso, final Callback onFalha) {
        return new Runnable() {
            @Override
            public void run() {
                try {
                    acao.call();
                    if (onSucesso != null) {
                        onSucesso.call();
                    }
                } catch (Exception e) {
                    Uteis.logar(e, ExecutorAssincronoUtil.class);
                    executarSilenciosamente(onFalha);
                }
            }
        };
    }

    private static void executarSilenciosamente(Callback callback) {
        try {
            if (callback != null) {
                callback.call();
            }
        } catch (Exception e) {
            Uteis.logar(e, ExecutorAssincronoUtil.class);
        }
    }

}

package servicos.util.thread;

import negocio.comuns.utilitarias.Uteis;
import servicos.util.ExecuteRequestHttpService;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ThresholdAsyncService {

    private static final ExecuteRequestHttpService executeRequestService = new ExecuteRequestHttpService();

    private ThresholdAsyncService() {

    }

    public static void makeAsyncRequest(RequestAction requestAction, int numAttempts, int timeBetweenAttempts, String mensagemErro) {
        executeRequestService.connectTimeout = 3000;
        executeRequestService.readTimeout = 3000;

        ExecutorService executor = Executors.newSingleThreadExecutor();

        for (int i = 0; i < numAttempts; i++) {
            for (int attempt = 0; attempt < numAttempts; attempt++) {
                try {
                    requestAction.execute(executeRequestService);
                    break; // Se a execução for bem-sucedida, saia do loop de tentativas.
                } catch (Exception ex) {
                    Uteis.logarDebug(mensagemErro);
                }

                try {
                    Thread.sleep(timeBetweenAttempts);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        executor.shutdown();
    }

    @FunctionalInterface
    public interface RequestAction {
        void execute(ExecuteRequestHttpService service) throws Exception;
    }

}

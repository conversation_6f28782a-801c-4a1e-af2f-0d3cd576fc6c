/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.util;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.net.ProtocolCommandEvent;
import org.apache.commons.net.ProtocolCommandListener;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.FTPSClient;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class FTPSimple {

    private String host;
    private String user;
    private String pwd;
    private String myIdRSA;
    private int port;

    private FTPClient ftpClient;

    private boolean secure = false;

    public FTPSimple(final String host, final String user, final String pwd, final int port) throws Exception {
        this.host = host;
        this.user = user;
        this.pwd = pwd;
        this.port = port;

        if (null != host && host.toLowerCase().startsWith("sftp")) {
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

                        }

                        @Override
                        public void checkServerTrusted(java.security.cert.X509Certificate[] x509Certificates, String s) throws CertificateException {

                        }

                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[0];
                        }

                    }
            };

            SSLContext sc = SSLContext.getInstance("TLSv1.2");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            ftpClient = new FTPSCustomClient(sc);
            secure = true;

        } else {
            ftpClient = new FTPClient();
        }

        ftpClient.addProtocolCommandListener(new ProtocolCommandListener() {
            @Override
            public void protocolCommandSent(ProtocolCommandEvent protocolCommandEvent) {
                System.out.println(String.format("[%s][%s][%s][%s] Command sent : [%s]-%s", Thread.currentThread().getName(),
                        Calendario.hoje(), host, user, protocolCommandEvent.getCommand(),
                        protocolCommandEvent.getMessage()));
            }

            @Override
            public void protocolReplyReceived(ProtocolCommandEvent protocolCommandEvent) {
                System.out.println(String.format("[%s][%s][%s][%s] Reply received : %s", Thread.currentThread().getName(),
                        Calendario.hoje(), host, user, protocolCommandEvent.getMessage()));
            }
        });

    }

    /**
     * Usar FTP para gravar arquivos
     */
    public void putFile(final String arquivo, final String pathDestino) throws Exception {
        try {
            putFileWithMkdir(arquivo, null, pathDestino, false);
        } catch (Exception e) {
            try {
                putFileWithMkdir(arquivo, null, pathDestino, true);
            } catch (Exception e2) {
                if (e2 instanceof ConsistirException) {
                    throw e2;
                } else {
                    Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "Erro Arquivo FTP - putFile -> " + e.getMessage());
                    throw e;
                }
            }
        }
    }



    private void putFileWithMkdir(final String arquivo, final String pathDestinoDirCreate, final String pathDestino, boolean modoAtivo) throws Exception {

        InputStream inputStream = null;

        try {

            ftpClient.connect(host, port);

            if (secure) {
                ((FTPSClient)ftpClient).execPROT("P");
            }

            int reply = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                throw new ConsistirException("Erro ao tentar conectar ao servidor FTP Server replied code: " + reply);
            }

            if (!ftpClient.login(user, pwd)) {
                ftpClient.logout();
                throw new ConsistirException("Login ou senha incorretos");
            }

            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.setFileTransferMode(FTP.BINARY_FILE_TYPE);

            File firstLocalFile = new File(arquivo);
            inputStream = new FileInputStream(firstLocalFile);

            putFileWithMkdir(arquivo, inputStream, pathDestinoDirCreate, pathDestino, modoAtivo);
        } catch (Exception e) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "Erro Arquivo FTP -> " + e.getMessage());
            throw e;
        } finally {
            if(inputStream != null) {
                inputStream.close();
            }
            if(ftpClient.isConnected()) {
                ftpClient.disconnect();
            }
        }
    }

    private void putFileWithMkdir(String arquivo, InputStream inputStream, String pathDestinoDirCreate, String pathDestino, boolean activeMode) throws Exception {
        if (activeMode && !secure) {
            ftpClient.enterLocalActiveMode();
        } else {
            ftpClient.enterLocalPassiveMode();
        }

        if (!UteisValidacao.emptyString(pathDestinoDirCreate)) {
            try {
                ftpClient.makeDirectory(pathDestinoDirCreate);
            } catch (Exception e) {
            }
        }

        boolean done = ftpClient.storeFile(pathDestino, inputStream);

        Uteis.logar(null, "PUT arquivo -> " + arquivo + " para destino " + pathDestino + " -> " + done + " - Modo ativo -> " + activeMode);
        if (!done) {
            Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "Erro Arquivo FTP -> " + ftpClient.getReplyCode() + "-" + ftpClient.getReplyString());
            Uteis.logar(null, "Erro Arquivo FTP -> " + ftpClient.getReplyCode() + "-" + ftpClient.getReplyString());
            throw new ConsistirException("Erro ao salvar arquivo no FTP:" + ftpClient.getReplyCode() + "-" + ftpClient.getReplyString());
        }
    }

}

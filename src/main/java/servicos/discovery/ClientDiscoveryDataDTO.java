package servicos.discovery;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClientDiscoveryDataDTO {

    private String[] modulosHabilitados;
    private List<EmpresaDTO> empresas;
    private List<EmpresaFinanceiroDTO> financeiroEmpresas;
    private List<RedeDTO> redeEmpresas;
    private ServiceMapDTO serviceUrls;
    private Boolean utilizarMoviDesk;
    private Boolean utilizarChatMoviDesk;
    private String grupoChatMovidesk;
    private String chaveEmpresa;

    public String[] getModulosHabilitados() {
        return modulosHabilitados;
    }

    public void setModulosHabilitados(String[] modulosHabilitados) {
        this.modulosHabilitados = modulosHabilitados;
    }

    public List<EmpresaDTO> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<EmpresaDTO> empresas) {
        this.empresas = empresas;
    }

    public ServiceMapDTO getServiceUrls() {
        return serviceUrls;
    }

    public void setServiceUrls(ServiceMapDTO serviceUrls) {
        this.serviceUrls = serviceUrls;
    }

    public List<EmpresaFinanceiroDTO> getFinanceiroEmpresas() {
        return financeiroEmpresas;
    }

    public void setFinanceiroEmpresas(List<EmpresaFinanceiroDTO> financeiroEmpresas) {
        this.financeiroEmpresas = financeiroEmpresas;
    }

    public List<RedeDTO> getRedeEmpresas() {
        return redeEmpresas;
    }

    public void setRedeEmpresas(List<RedeDTO> redeEmpresas) {
        this.redeEmpresas = redeEmpresas;
    }

    public Boolean getUtilizarMoviDesk() {
        return utilizarMoviDesk;
    }

    public void setUtilizarMoviDesk(Boolean utilizarMoviDesk) {
        this.utilizarMoviDesk = utilizarMoviDesk;
    }

    public Boolean getUtilizarChatMoviDesk() {
        return utilizarChatMoviDesk;
    }

    public void setUtilizarChatMoviDesk(Boolean utilizarChatMoviDesk) {
        this.utilizarChatMoviDesk = utilizarChatMoviDesk;
    }

    public String getGrupoChatMovidesk() {
        return grupoChatMovidesk;
    }

    public void setGrupoChatMovidesk(String grupoChatMovidesk) {
        this.grupoChatMovidesk = grupoChatMovidesk;
    }

    public String getChaveEmpresa() {
        return chaveEmpresa;
    }

    public void setChaveEmpresa(String chaveEmpresa) {
        this.chaveEmpresa = chaveEmpresa;
    }
}


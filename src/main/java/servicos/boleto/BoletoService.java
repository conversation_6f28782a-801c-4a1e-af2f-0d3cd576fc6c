package servicos.boleto;

import controle.arquitetura.SuperControle;
import controle.financeiro.BoletoBancarioControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.BoletoMovParcelaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.RemessaItem;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class BoletoService extends SuperControle {

    private Connection con;

    public BoletoService(Connection con) throws Exception {
        this.con = con;
    }

    public JSONArray todos(Integer empresa, String matricula, int limit, int offset, int movParcela) throws Exception {
        Cliente clienteDAO;
        try {
            JSONArray jsonArray = new JSONArray();
            clienteDAO = new Cliente(con);
            ClienteVO cliente = clienteDAO.consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (cliente != null && cliente.getPessoa() != null && !UteisValidacao.emptyNumber(cliente.getPessoa().getCodigo())) {
                RemessaItem remessaItemDAO = new RemessaItem(con);
                TipoCobrancaEnum[] tiposBoleto = {TipoCobrancaEnum.BOLETO};
                List<RemessaItemVO> itensBoleto = remessaItemDAO.consultarTelaCliente(cliente.getPessoa().getCodigo(), limit, offset, tiposBoleto, Uteis.NIVELMONTARDADOS_DADOSBASICOS, movParcela);
                itensBoleto.forEach(item -> {
                    try {
                        JSONObject json = new JSONObject();
                        json.put("valor", item.getValorBoleto().doubleValue());
                        json.put("dataVencimento", item.getDataVencimentoBoleto());
                        json.put("linhaDigitavel", remessaItemDAO.obterJBoleto(item, con).getBoleto().getLinhaDigitavel()
                                .replace(".", "").replace(" ", ""));
                        jsonArray.put(json);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });

                StringBuilder sqlBoletoOnline = new StringBuilder();
                sqlBoletoOnline.append("select b.valor, b.dataVencimento, b.linhadigitavel from boleto b");
                sqlBoletoOnline.append(" inner join boletomovparcela bmp on bmp.boleto = b.codigo");
                sqlBoletoOnline.append(" where b.empresa = ").append(empresa);
                sqlBoletoOnline.append(" and b.pessoa = ").append(cliente.getPessoa().getCodigo());
                if (!UteisValidacao.emptyNumber(movParcela)) {
                    sqlBoletoOnline.append(" and bmp.movparcela = ").append(movParcela);
                    sqlBoletoOnline.append(" and b.situacao in (").append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(",")
                            .append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",").append(SituacaoBoletoEnum.GERADO.getCodigo()).append(")");
                    sqlBoletoOnline.append(" order by b.codigo desc");
                }
                if (!UteisValidacao.emptyNumber(limit)) {
                    sqlBoletoOnline.append(" limit ").append(limit).append(" offset ").append(offset);
                }
                try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlBoletoOnline.toString(), con)) {
                    while (rs.next()) {
                        JSONObject json = new JSONObject();
                        json.put("valor", rs.getDouble("valor"));
                        json.put("dataVencimento", rs.getString("datavencimento"));
                        json.put("linhaDigitavel", rs.getString("linhadigitavel").replace(".", "").replace(" ", ""));
                        jsonArray.put(json);
                    }
                }
            }
            return jsonArray;
        } catch (Exception ex) {
            return new JSONArray();
        } finally {
            clienteDAO = null;
        }
    }

    public String imprimirBoletoBancoBrasilNovaTelaCliente(int codBoleto, HttpServletRequest request) throws Exception {
        RemessaItemVO remessaItemVO = montarObjetoParaImprimir(codBoleto);
        return executarImprimirBoleto(remessaItemVO, request);
    }

    public RemessaItemVO montarObjetoParaImprimir(int codBoleto) throws Exception {
        Boleto boletoDAO;
        Empresa empresaDAO;
        Endereco enderecoDAO;
        Cidade cidadeDAO;
        Estado estadoDAO;
        Pessoa pessoaDAO;
        RemessaItem remessaItemDAO;
        try {
            boletoDAO = new Boleto(con);
            empresaDAO = new Empresa(con);
            enderecoDAO = new Endereco(con);
            cidadeDAO = new Cidade(con);
            estadoDAO = new Estado(con);
            pessoaDAO = new Pessoa(con);
            remessaItemDAO = new RemessaItem(con);

            BoletoVO boletoVO = boletoDAO.consultarPorChavePrimaria(codBoleto, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            RemessaItemVO remessaItemVO = new RemessaItemVO();
            remessaItemVO.getRemessa().setConvenioCobranca(boletoVO.getConvenioCobrancaVO());
            remessaItemVO.getRemessa().getConvenioCobranca().setEmpresa(empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getRemessa().getConvenioCobranca().getTipoRemessa().setArquivoLayoutRemessa(ArquivoLayoutRemessaEnum.CARNE_BANCO_DO_BRASIL);
            remessaItemVO.getRemessa().setDataRegistro(boletoVO.getDataRegistro());
            remessaItemVO.getRemessa().setEmpresa(boletoVO.getEmpresaVO().getCodigo());
            remessaItemVO.setCodigo(boletoVO.getIdentificador());
            remessaItemVO.setIdentificador(boletoVO.getIdentificador());
            remessaItemVO.setPessoa(pessoaDAO.consultarPorChavePrimaria(boletoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            remessaItemVO.getPessoa().setEnderecoVOs(enderecoDAO.consultarPorCodigoPessoa(remessaItemVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getPessoa().setCidade(cidadeDAO.consultarPorCodigoExato(remessaItemVO.getPessoa().getCidade().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.getPessoa().setEstadoVO(estadoDAO.consultarPorCodigo(remessaItemVO.getPessoa().getEstadoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            remessaItemVO.setMesesAbertos(remessaItemDAO.consultarMesesEmAberto(remessaItemVO));
            remessaItemVO.setPorcentagemDescontoBoleto(remessaItemVO.getRemessa().getConvenioCobranca().getDescontoBoleto());
            remessaItemVO.setDataVencimentoBoleto(boletoVO.getDataVencimento());

            List<RemessaItemMovParcelaVO> movParcelasLista = new ArrayList<>();
            Double valorBoleto = 0.0;
            for (BoletoMovParcelaVO boletoMovParcelaVO : boletoVO.getListaBoletoMovParcela()) {
                RemessaItemMovParcelaVO remessaItemMovParcelaVO = new RemessaItemMovParcelaVO();
                remessaItemMovParcelaVO.setValorOriginal(boletoMovParcelaVO.getMovParcelaVO().getValorParcela());
                remessaItemMovParcelaVO.setMovParcelaVO(boletoMovParcelaVO.getMovParcelaVO());
                movParcelasLista.add(remessaItemMovParcelaVO);
                valorBoleto += boletoMovParcelaVO.getMovParcelaVO().getValorParcela();
            }
            remessaItemVO.setMovParcelas(movParcelasLista);
            remessaItemVO.setValorItemRemessa(valorBoleto);

            String parametrosCriacaoBoleto = boletoVO.getParamsEnvio();
            JSONObject jsonEnvio = new JSONObject(parametrosCriacaoBoleto);
            JSONObject jsonPagador = jsonEnvio.getJSONObject("pagador");
            HashMap<String, String> propriedades = new HashMap<>();
            propriedades.put("CpfCnpjPagador", String.valueOf(jsonPagador.getLong("numeroInscricao")));
            propriedades.put("NomePagador", jsonPagador.getString("nome"));
            remessaItemVO.setProps(propriedades);

            return remessaItemVO;
        } catch (Exception ex) {
            String msgErro = "Não foi possível imprimir o boleto!" + ex.getMessage();
            throw new Exception(msgErro);
        } finally {
            boletoDAO = null;
            empresaDAO = null;
            enderecoDAO = null;
            cidadeDAO = null;
            estadoDAO = null;
            remessaItemDAO = null;
            pessoaDAO = null;
        }
    }

    public String executarImprimirBoleto(RemessaItemVO remessaItemVO, HttpServletRequest request) throws Exception {
        try {
            BoletoBancarioControle boletoBancarioControle = new BoletoBancarioControle();
            String caminhoArquivoPDFBoleto = boletoBancarioControle.executarImprimirBoleto(remessaItemVO, con, request);
            return caminhoArquivoPDFBoleto;

        } catch (Exception ex) {
            String msgErro = "Não foi possível imprimir o boleto, verifique todas as configurações! " + ex.getMessage();
            throw new Exception(msgErro);
        }
    }

    public boolean enviarEmailClienteBoletoBancoBrasil(int codBoleto, HttpServletRequest request, int codUsuario, int empresa) {
        boolean emailEnviado = false;
        try {
            RemessaItemVO remessaItemVO = montarObjetoParaImprimir(codBoleto);
            BoletoBancarioControle boletoBancarioControle = new BoletoBancarioControle();
            String caminhoArquivoPDFBoleto = boletoBancarioControle.executarImprimirBoleto(remessaItemVO, con, request);
            boolean sucesso = enviarEmail(remessaItemVO, caminhoArquivoPDFBoleto, remessaItemVO.getPessoa().getCodigo(), codUsuario, empresa, boletoBancarioControle, request);
            return sucesso;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return emailEnviado;
    }

    private boolean enviarEmail(RemessaItemVO remessaItemVO, String caminhoArquivoPDFBoleto, int codPessoa, int codUsuario, int empresa,
                                BoletoBancarioControle controle, HttpServletRequest request) throws Exception {
        Pessoa pessoaDAO;
        Usuario usuarioDAO;
        Empresa empresaDAO;
        boolean sucesso = false;
        try {
            pessoaDAO = new Pessoa(con);
            usuarioDAO = new Usuario(con);
            empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_MINIMOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_REGISTRAR_JUSTIFICATIVA);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(codPessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            //verificar se o cliente tem email válido
            if (UteisValidacao.emptyList(pessoaVO.getEmailVOs())) {
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            } else {
                //obter o boleto
                String nomeArquivoGeradoAgora = request.getAttribute("nomeArquivoRelatorioGeradoAgora").toString();
                File arquivo = new File(new File(request.getSession().getServletContext().getRealPath("/")).getAbsolutePath() + "/servlet-relatorio/" +
                        nomeArquivoGeradoAgora);
                //obter configurações do envio de email
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
                UteisEmail email = new UteisEmail();
                // assunto do email será "BOLETO"
                email.novo("BOLETO", configuracaoSistemaCRMVO);
                //remetente é o usuario logado
                email.setRemetente(usuarioVO);
                if (remessaItemVO.getRemessa().getConvenioCobranca().getTipo().getCodigoBanco() != TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigoBanco()) {
                    email = email.addAnexo(nomeArquivoGeradoAgora, arquivo);
                }


                String[] emails = new String[pessoaVO.getEmailVOs().size()];
                int i = 0;
                for (Object obj : pessoaVO.getEmailVOs()) {
                    EmailVO emailVO = (EmailVO) obj;
                    emails[i] = emailVO.getEmail();
                    i++;
                }
                String nomeEmpresa = empresaVO.getNome();
                String mensagem = controle.gerarHTMLModeloPadraoBoleto(caminhoArquivoPDFBoleto);
                email.enviarEmailN(emails, mensagem, "BOLETO", nomeEmpresa);
                sucesso = true;
            }
            return sucesso;
        } catch (Exception e) {
            throw e;
        } finally {
            pessoaDAO = null;
            usuarioDAO = null;
            empresaDAO = null;
        }
    }


}

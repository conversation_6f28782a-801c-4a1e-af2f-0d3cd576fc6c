/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class SuperServico extends SuperTO{
    private static final long serialVersionUID = -3693878420322098522L;

    private transient Connection con;

    public static void init(String url) throws Exception {
        Properties props = new Properties();
        InputStream in = SuperServico.class.getResourceAsStream(
                "/servicos/propriedades/Aprovacao.properties");
        try {
            if (url != null && !url.isEmpty() && !url.contains("://teste.")) {
                props.load(in);
                
                ArrayList<String> lista = new ArrayList(props.values());
                String hostNameServidor = InetAddress.getLocalHost().getHostName();
                System.out.println("Verificando -> "+ hostNameServidor + " em -> " + props.toString());
                boolean permite = false;
                for (String host : lista) {
                    if (host.equalsIgnoreCase(hostNameServidor)) {
                        permite = true;
                    }
                }
                if (!permite) {
                    throw new Exception(String.format("O Servidor \"%s\" não "
                            + "possui autorização para realizar esta operação.",
                            new Object[]{hostNameServidor.toUpperCase()}));
                }
            }

        } catch (IOException ex) {
            Logger.getLogger(SuperServico.class.getName()).log(Level.SEVERE, null, ex);
            throw new Exception("Não foi possível iniciar o serviço. "
                    + "Falha ao carregar arquivo Aprovacao.properties -> " + ex.getMessage());
        } finally {
            in.close();
        }
    }

    public SuperServico(Connection con) throws Exception {
        this.con = con;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }
}

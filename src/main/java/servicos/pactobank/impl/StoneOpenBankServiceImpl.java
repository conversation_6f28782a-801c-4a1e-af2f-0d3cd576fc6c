package servicos.pactobank.impl;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.enumeradores.BancoOpenBankEnum;
import negocio.comuns.financeiro.ComportamentoConta;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.openbanking.stone.ContaStoneVO;
import negocio.comuns.financeiro.openbanking.stone.PagamentoStoneVO;
import negocio.comuns.financeiro.openbanking.stone.RetornoStoneVO;
import negocio.comuns.financeiro.openbanking.stone.TransferenciaStoneVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.openbanking.stone.ContaStone;
import negocio.facade.jdbc.financeiro.openbanking.stone.PagamentoStone;
import negocio.facade.jdbc.financeiro.openbanking.stone.RetornoStone;
import negocio.facade.jdbc.financeiro.openbanking.stone.TransferenciaStone;
import org.json.JSONObject;
import servicos.pactobank.intf.StoneOpenBankServiceInterface;

import javax.servlet.ServletContext;
import java.sql.Connection;

public class StoneOpenBankServiceImpl implements StoneOpenBankServiceInterface {

    private Connection con;
    private ServletContext servletContext;

    public StoneOpenBankServiceImpl(Connection con) throws Exception {
        this.con = con;
    }

    public String gravarContaStoneFinanceiro(String body, String chave, Integer empresa) throws Exception {

        AcessoControle acessoControle = null;
        acessoControle = DaoAuxiliar.retornarAcessoControle(chave);
        ContaStone contaStoneDAO = new ContaStone(acessoControle.getCon());
        ContaStoneVO contaStone = contaStoneDAO.montarContaStoneInserirZwFin(body, empresa);
        contaStoneDAO.incluir(contaStone);
        contaStoneDAO = null;
        if (!acessoControle.getContaDao().existeConta(ComportamentoConta.getFromDescricao(contaStone.getComportamentoTipoConta()))) {
            acessoControle.getCon().setAutoCommit(false);

            TipoContaVO tipoConta = acessoControle.getTipoContaDao().existeTipoConta(ComportamentoConta.getFromDescricao(contaStone.getComportamentoTipoConta()));
            if (tipoConta != null && tipoConta.getCodigo().equals(0)) {
                tipoConta.setDescricao(contaStone.getDescricaoTipoConta());
                tipoConta.setComportamento(ComportamentoConta.getFromDescricao(contaStone.getComportamentoTipoConta()));
                acessoControle.getTipoContaDao().incluir(tipoConta);
            }

            ContaVO conta = new ContaVO();
            conta.setEmpresa(acessoControle.getEmpresaDao().consultarPorChavePrimaria(contaStone.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            conta.setTipoConta(tipoConta);
            conta.setDescricao(contaStone.getDescricaoConta());
            conta.setDescricaoCurta(contaStone.getDescricaoCurtaConta());
            conta.setBanco(acessoControle.getBancoDao().consultarCodigoBanco(contaStone.getCodigoBanco(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            conta.setAgencia(contaStone.getAgenciaConta());
            conta.setAgenciaDV(contaStone.getDigitoAgenciaConta());
            conta.setNumero(contaStone.getAccount_code());
            conta.setNumeroDV(contaStone.getBranch_code());
            conta.setAtiva(contaStone.getContaAtiva());
            conta.setMostrarBi(contaStone.getContaTotalizadaBI());
            conta.setObservacao(contaStone.getContaObservacao());
            conta.setBancoOpenBankEnum(BancoOpenBankEnum.getFromDescricao(contaStone.getDescricaoBancoOpenBankZW()));
            acessoControle.getContaDao().incluir(conta);
            acessoControle.getCon().setAutoCommit(true);
        }

        return "Conta incluída com sucesso!";
    }

    public String atualizarTransPagRespostaStone(String body, String chave, Integer empresa, String eventIdWebhook, String eventTypeWebhook) throws Exception {

        AcessoControle acessoControle = null;
        acessoControle = DaoAuxiliar.retornarAcessoControle(chave);
        RetornoStone retornoStoneDAO = new RetornoStone(acessoControle.getCon());
        JSONObject transacao = new JSONObject(body);
        RetornoStoneVO retornoStone = retornoStoneDAO.buscarRetornoStoneByEventId(eventIdWebhook, empresa);
        if(retornoStone == null) {
            TransferenciaStone transferenciaStoneDAO = new TransferenciaStone(acessoControle.getCon());
            TransferenciaStoneVO transferenciaStone = transferenciaStoneDAO.buscarTransferenciaStoneById(transacao.getString("id"), empresa);
            if (transferenciaStone != null) {
                transferenciaStone.setEventIdWebhook(eventIdWebhook);
                transferenciaStoneDAO.alterar(transferenciaStone);
            }
            PagamentoStone pagamentoStoneDAO = new PagamentoStone(acessoControle.getCon());
            PagamentoStoneVO pagamentoStone = pagamentoStoneDAO.buscarPagamentoStoneById(transacao.getString("id"), empresa);
            if (pagamentoStone != null) {
                pagamentoStone.setEventIdWebhook(eventIdWebhook);
                pagamentoStoneDAO.alterar(pagamentoStone);
            }

            RetornoStoneVO retornoStoneInserir = new RetornoStoneVO();
            retornoStoneInserir.setEventId(eventIdWebhook);
            retornoStoneInserir.setTransacaoId(transacao.getString("id"));
            retornoStoneInserir.setEmpresa(empresa);
            retornoStoneInserir.setStatus(transacao.getJSONObject("target_data").getString("status"));
            retornoStoneInserir.setRetorno(body);
            retornoStoneInserir.setEventType(eventTypeWebhook);
            retornoStoneDAO.incluir(retornoStoneInserir);


            transferenciaStoneDAO = null;
            pagamentoStoneDAO = null;
        }else{
            retornoStone.setStatus(transacao.getJSONObject("target_data").getString("status"));
            retornoStone.setRetorno(body);
            retornoStone.setEventType(eventTypeWebhook);
            retornoStoneDAO.alterar(retornoStone);
        }
        retornoStoneDAO = null;
        return "Retorno Stone processado com sucesso!";
    }
}

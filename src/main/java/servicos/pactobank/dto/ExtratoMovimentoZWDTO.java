package servicos.pactobank.dto;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TypeExtratoStoneOpenBankEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtratoMovimentoZWDTO {

    private String account_id;
    private Integer amount;
    private Integer balance_after;
    private Integer balance_before;
    private ExtratoCounterPartyDTO counter_party;
    private String created_at;
    private Boolean delayed_to_next_business_day;
    private Integer fee_amount;
    private String id;
    private String operation;
    private Integer operation_amount;
    private String operation_id;
    private Integer refund_reason_code;
    private String refund_reason_description;
    private String scheduled_at;
    private String scheduled_to_effective;
    private String scheduled_to_requested;
    private String status;
    private String title;
    private String type;
    private String subtitle;
    private ExtratoDetails details;


    public String getTipoMovimento_Apresentar() {
        if (!UteisValidacao.emptyString(this.type)) {
            return TypeExtratoStoneOpenBankEnum.valueOf(this.type.toUpperCase()).getDescricao();
        }
        return "";
    }
    public String getOperacao_Apresentar() {
        if (!UteisValidacao.emptyString(this.operation)) {
            if (this.operation.equals("credit")) {
                return "Entrada";
            } else if (this.operation.equals("debit")) {
                return "Saida";
            }
        }
        return "";
    }
    public String getStatus_Style() {
        if (!UteisValidacao.emptyString(this.status)) {
            if (this.status.equals("FINISHED")) {
                return "fa-icon-ok-sign green";
            } else if (this.status.equals("REFUNDED")) {
                return "fa-icon-undo cinza";
            } else {
                return "fa-icon-time cinza";
            }
        } else {
            return "";
        }
    }
    public String getStatus_Title() {
        if (!UteisValidacao.emptyString(this.status)) {
            if (this.status.equals("FINISHED")) {
                return "Finalizado";
            } else if (this.status.equals("REFUNDED")) {
                return "Devolvido";
            } else {
                return "Aguardando Aprovação";
            }
        } else {
            return "";
        }
    }
    public String getAmout_calculado() {
        if (!UteisValidacao.emptyNumber(this.amount)) {
            return Formatador.formatarValorMonetarioSemMoeda(Double.valueOf((float) this.amount / 100));
        }
        return "";
    }
    public String getBalanceBefore_calculado() {
        if (!UteisValidacao.emptyNumber(this.balance_before)) {
            return Formatador.formatarValorMonetarioSemMoeda(Double.valueOf((float) this.balance_before / 100));
        }
        return "";
    }
    public String getBalanceAfter_calculado() {
        if (!UteisValidacao.emptyNumber(this.balance_after)) {
            return Formatador.formatarValorMonetarioSemMoeda(Double.valueOf((float) this.balance_after / 100));
        }
        return "";
    }
    public String getDateCreate_Convertido() {
        try {
            if (!UteisValidacao.emptyString(this.created_at)) {
                String data = this.created_at.substring(0, this.created_at.indexOf("T"));
                String hora = this.created_at.substring(this.created_at.indexOf("T") + 1, this.created_at.indexOf("Z"));
                return Calendario.getData(Calendario.getDate("yyyy-MM-dd", data), "dd/MM/yyyy - " + hora);
            }
        } catch (Exception ignore) {
            return "";
        }
        return "";
    }
    public Long getDateCreate_TimesTamp() {
        try {
            return Calendario.getDate("yyyy-MM-dd - HH:mm:ss", this.created_at.replace("T", " - ").replace("Z", "")).getTime();
        } catch (Exception ignore) {
            return Calendario.hoje().getTime();
        }
    }

    public String getTitleFormatadoStone() {
        try {
            StringBuilder titleFormatado = new StringBuilder();
            if (TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.INTERNAL) ||
                    TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.EXTERNAL)) {
                titleFormatado.append("Nome da Instituição: ");
                titleFormatado.append(this.getCounter_party().getAccount().getInstitution_name()).append(" | ");
                titleFormatado.append("Agência: ");
                titleFormatado.append(this.getCounter_party().getAccount().getBranch_code()).append(" | ");
                titleFormatado.append("Conta: ");
                titleFormatado.append(this.getCounter_party().getAccount().getAccount_code()).append(" | ");
                titleFormatado.append("Nome: ");
                titleFormatado.append(this.getCounter_party().getEntity().getName());
                return titleFormatado.toString();
            } else if (TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.PAYMENT) ||
                    TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.PAYMENT_REFUND)) {
                titleFormatado.append("Tipo de documento: ");
                titleFormatado.append(this.getDetails().getDocument_type()).append(" | ");
                titleFormatado.append("Nome Legal do Pagador: ");
                titleFormatado.append(this.getDetails().getPayer_legal_name()).append(" | ");
                titleFormatado.append("Nome Comercial do Pagador: ");
                titleFormatado.append(this.getDetails().getPayer_trade_name()).append(" | ");
                if (this.getDetails().getExpiration_date() != null) {
                    titleFormatado.append("Data de validade: ");
                    titleFormatado.append(Calendario.getDataAplicandoFormatacao(
                            Calendario.getDate("yyyy-MM-dd", this.getDetails().getExpiration_date()), "dd/MM/yyyy"))
                            .append(" | ");
                }
                titleFormatado.append("Nome do Destinatário: ");
                titleFormatado.append(this.getDetails().getRecipient_name()).append(" | ");
                titleFormatado.append("Linha gravável: ");
                titleFormatado.append(this.getDetails().getWritable_line());
                if(TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.PAYMENT_REFUND)){
                    titleFormatado.append(" | ");
                    titleFormatado.append("Descrição do Motivo do Reembolso: ");
                    titleFormatado.append(this.getRefund_reason_description());
                }
                return titleFormatado.toString();
            }else if(TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.BALANCE_BLOCKED) ||
                    TypeExtratoStoneOpenBankEnum.valueOf(this.getType().toUpperCase()).equals(TypeExtratoStoneOpenBankEnum.BALANCE_UNBLOCKED)){
                titleFormatado.append("Entre em contato com a STONE para mais detalhes.");
                return titleFormatado.toString();
            }
            return "";
        }catch (Exception e){
            return "";
        }
    }

    public Integer getBalance_before() {
        return balance_before;
    }

    public void setBalance_before(Integer balance_before) {
        this.balance_before = balance_before;
    }

    public Integer getBalance_after() {
        return balance_after;
    }

    public void setBalance_after(Integer balance_after) {
        this.balance_after = balance_after;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccount_id() {
        return account_id;
    }

    public void setAccount_id(String account_id) {
        this.account_id = account_id;
    }

    public Boolean getDelayed_to_next_business_day() {
        return delayed_to_next_business_day;
    }

    public void setDelayed_to_next_business_day(Boolean delayed_to_next_business_day) {
        this.delayed_to_next_business_day = delayed_to_next_business_day;
    }

    public Integer getFee_amount() {
        return fee_amount;
    }

    public void setFee_amount(Integer fee_amount) {
        this.fee_amount = fee_amount;
    }

    public Integer getOperation_amount() {
        return operation_amount;
    }

    public void setOperation_amount(Integer operation_amount) {
        this.operation_amount = operation_amount;
    }

    public String getOperation_id() {
        return operation_id;
    }

    public void setOperation_id(String operation_id) {
        this.operation_id = operation_id;
    }

    public Integer getRefund_reason_code() {
        return refund_reason_code;
    }

    public void setRefund_reason_code(Integer refund_reason_code) {
        this.refund_reason_code = refund_reason_code;
    }

    public String getRefund_reason_description() {
        return refund_reason_description;
    }

    public void setRefund_reason_description(String refund_reason_description) {
        this.refund_reason_description = refund_reason_description;
    }

    public String getScheduled_at() {
        return scheduled_at;
    }

    public void setScheduled_at(String scheduled_at) {
        this.scheduled_at = scheduled_at;
    }

    public String getScheduled_to_effective() {
        return scheduled_to_effective;
    }

    public void setScheduled_to_effective(String scheduled_to_effective) {
        this.scheduled_to_effective = scheduled_to_effective;
    }

    public String getScheduled_to_requested() {
        return scheduled_to_requested;
    }

    public void setScheduled_to_requested(String scheduled_to_requested) {
        this.scheduled_to_requested = scheduled_to_requested;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public ExtratoCounterPartyDTO getCounter_party() {
        return counter_party;
    }

    public void setCounter_party(ExtratoCounterPartyDTO counter_party) {
        this.counter_party = counter_party;
    }

    public ExtratoDetails getDetails() {
        return details;
    }

    public void setDetails(ExtratoDetails details) {
        this.details = details;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }
}

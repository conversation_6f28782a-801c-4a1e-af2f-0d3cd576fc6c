package servicos.pactobank.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtratoDetails {

    private String bank_name;
    private String discount_value;
    private String document_payment_type;
    private String document_type;
    private String expiration_date;
    private String face_value;
    private String fine_value;
    private String interest_value;
    private String max_value;
    private String min_value;
    private String payer_cpf_cnpj;
    private String payer_legal_name;
    private String payer_trade_name;
    private String payment_end_time;
    private String payment_limit_date;
    private String payment_start_time;
    private String recipient_cpf_cnpj;
    private String recipient_name;
    private String settlement_date;
    private String status;
    private String total_added_value;
    private String total_discounted_value;
    private String updatable_value;
    private String value;
    private String writable_line;

    public String getBank_name() {
        return bank_name;
    }

    public void setBank_name(String bank_name) {
        this.bank_name = bank_name;
    }

    public String getDiscount_value() {
        return discount_value;
    }

    public void setDiscount_value(String discount_value) {
        this.discount_value = discount_value;
    }

    public String getDocument_payment_type() {
        return document_payment_type;
    }

    public void setDocument_payment_type(String document_payment_type) {
        this.document_payment_type = document_payment_type;
    }

    public String getDocument_type() {
        return document_type;
    }

    public void setDocument_type(String document_type) {
        this.document_type = document_type;
    }

    public String getExpiration_date() {
        return expiration_date;
    }

    public void setExpiration_date(String expiration_date) {
        this.expiration_date = expiration_date;
    }

    public String getFace_value() {
        return face_value;
    }

    public void setFace_value(String face_value) {
        this.face_value = face_value;
    }

    public String getFine_value() {
        return fine_value;
    }

    public void setFine_value(String fine_value) {
        this.fine_value = fine_value;
    }

    public String getInterest_value() {
        return interest_value;
    }

    public void setInterest_value(String interest_value) {
        this.interest_value = interest_value;
    }

    public String getMax_value() {
        return max_value;
    }

    public void setMax_value(String max_value) {
        this.max_value = max_value;
    }

    public String getMin_value() {
        return min_value;
    }

    public void setMin_value(String min_value) {
        this.min_value = min_value;
    }

    public String getPayer_cpf_cnpj() {
        return payer_cpf_cnpj;
    }

    public void setPayer_cpf_cnpj(String payer_cpf_cnpj) {
        this.payer_cpf_cnpj = payer_cpf_cnpj;
    }

    public String getPayer_legal_name() {
        return payer_legal_name;
    }

    public void setPayer_legal_name(String payer_legal_name) {
        this.payer_legal_name = payer_legal_name;
    }

    public String getPayer_trade_name() {
        return payer_trade_name;
    }

    public void setPayer_trade_name(String payer_trade_name) {
        this.payer_trade_name = payer_trade_name;
    }

    public String getPayment_end_time() {
        return payment_end_time;
    }

    public void setPayment_end_time(String payment_end_time) {
        this.payment_end_time = payment_end_time;
    }

    public String getPayment_limit_date() {
        return payment_limit_date;
    }

    public void setPayment_limit_date(String payment_limit_date) {
        this.payment_limit_date = payment_limit_date;
    }

    public String getPayment_start_time() {
        return payment_start_time;
    }

    public void setPayment_start_time(String payment_start_time) {
        this.payment_start_time = payment_start_time;
    }

    public String getRecipient_cpf_cnpj() {
        return recipient_cpf_cnpj;
    }

    public void setRecipient_cpf_cnpj(String recipient_cpf_cnpj) {
        this.recipient_cpf_cnpj = recipient_cpf_cnpj;
    }

    public String getRecipient_name() {
        return recipient_name;
    }

    public void setRecipient_name(String recipient_name) {
        this.recipient_name = recipient_name;
    }

    public String getSettlement_date() {
        return settlement_date;
    }

    public void setSettlement_date(String settlement_date) {
        this.settlement_date = settlement_date;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTotal_added_value() {
        return total_added_value;
    }

    public void setTotal_added_value(String total_added_value) {
        this.total_added_value = total_added_value;
    }

    public String getTotal_discounted_value() {
        return total_discounted_value;
    }

    public void setTotal_discounted_value(String total_discounted_value) {
        this.total_discounted_value = total_discounted_value;
    }

    public String getUpdatable_value() {
        return updatable_value;
    }

    public void setUpdatable_value(String updatable_value) {
        this.updatable_value = updatable_value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getWritable_line() {
        return writable_line;
    }

    public void setWritable_line(String writable_line) {
        this.writable_line = writable_line;
    }
}

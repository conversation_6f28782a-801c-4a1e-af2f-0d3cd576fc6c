package servicos.pactobank.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SimulacaoPagamentoStoneDTO {

    Integer bank_code;
    String bank_name;
    String document_type;
    Integer face_value;
    String expiration_date;
    Boolean payment_being_processed;
    String status;
    String approved_at;

    public Integer getBank_code() {
        return bank_code;
    }

    public void setBank_code(Integer bank_code) {
        this.bank_code = bank_code;
    }

    public String getBank_name() {
        return bank_name;
    }

    public void setBank_name(String bank_name) {
        this.bank_name = bank_name;
    }

    public String getDocument_type() {
        return document_type;
    }

    public void setDocument_type(String document_type) {
        this.document_type = document_type;
    }

    public Boolean getPayment_being_processed() {
        return payment_being_processed;
    }

    public void setPayment_being_processed(Boolean payment_being_processed) {
        this.payment_being_processed = payment_being_processed;
    }

    public String getExpiration_date() {
        return expiration_date;
    }

    public void setExpiration_date(String expiration_date) {
        this.expiration_date = expiration_date;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApproved_at() {
        return approved_at;
    }

    public void setApproved_at(String approved_at) {
        this.approved_at = approved_at;
    }

    public Integer getFace_value() {
        return face_value;
    }

    public void setFace_value(Integer face_value) {
        this.face_value = face_value;
    }
}

package servicos.pactobank.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtratoCounterPartyDTO {
    private ExtratoAccountDTO account;
    private ExtratoEntityDTO entity;

    public ExtratoAccountDTO getAccount() {
        return account;
    }

    public void setAccount(ExtratoAccountDTO account) {
        this.account = account;
    }

    public ExtratoEntityDTO getEntity() {
        return entity;
    }

    public void setEntity(ExtratoEntityDTO entity) {
        this.entity = entity;
    }
}

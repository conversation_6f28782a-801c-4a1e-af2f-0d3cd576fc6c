package servicos;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 * Created by Rafael on 20/10/2016.
 */
public class UploadLogos {
    public static void main(String[] args) {
        System.out.println("Entrou no Administrativo Runner");
        if (args.length == 0) {
            args = new String[]{"sereia"};
        }
        if (args.length >= 1) {
            String chave = args[0];
            Connection con = null;
            try {
                DAO dao = new DAO();
                con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo from empresa", con);
                while (rs.next()){
                     ExecuteRequestHttpService.executeRequest(getUrlOamd(chave) + "/imagenslogoapp?key=" + chave
                            + "&empresa=" + rs.getInt("codigo") + "&reload=true", null);
                }
            } catch (Exception ex) {
                Logger.getLogger(AdministrativoRunner.class.getName()).log(Level.SEVERE,
                        "Erro ao obter conexao especifica com a chave " + chave, ex);
            }
        }
    }
    public static String getUrlOamd(String key) {
        try {
            return PropsService.getPropertyValue(key, PropsService.urlOamd);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }
}

package servicos;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.plano.Modalidade;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AjustarCrossfitContratoRunner {

    public static void main(String... args) {
        try {
            DAO oamd = new DAO();
            List<String> chaves = oamd.buscarListaChaves();
            List<String> chavesAfetadas = new ArrayList<>();
            for (String chave : chaves) {
                try {
                    Connection c = new DAO().obterConexaoEspecifica(chave);
                    ajustarCrossfitContratos(c, chave);
                    chavesAfetadas.add(chave);
                } catch (Exception ex) {
                    Logger.getLogger(AjustarCrossfitContratoRunner.class.getName())
                            .log(Level.SEVERE, String.format("ERRO para a chave %s | AjustarCrossfitContratoRunner - " + ex.getMessage(), chave), ex);
                }
            }
            Uteis.logarDebug("Chaves afetadas: " + chavesAfetadas);
        } catch (Exception ex) {
            Logger.getLogger(AjustarCrossfitContratoRunner.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarCrossfitContratos(Connection con, String chave) {
        try {
            Uteis.logarDebug(String.format("INÍCIO para a chave %s | AjustarCrossfitContratoRunner", chave));
            StringBuilder sql = new StringBuilder("select\n" +
                    " con.codigo as contrato,\n" +
                    " cli.codigomatricula,\n" +
                    " cli.codigo,\n" +
                    " cli.pessoa,\n" +
                    " mod.crossfit\n" +
                    "from\n" +
                    " contrato con\n" +
                    "inner join cliente cli on\n" +
                    " con.pessoa = cli.pessoa\n" +
                    "inner join contratomodalidade conmod on\n" +
                    " conmod.contrato = con.codigo\n" +
                    "inner join modalidade mod on\n" +
                    " mod.codigo = conmod.modalidade\n" +
                    "where\n" +
                    " con.situacao = 'AT'\n" +
                    " and mod.codigo::text in (\n" +
                    "  select\n" +
                    "   chaveprimaria\n" +
                    "  from\n" +
                    "   log\n" +
                    "  where\n" +
                    "   nomeentidade = 'MODALIDADE'\n" +
                    "   and operacao = 'INCLUSÃO'\n" +
                    "   and dataalteracao >= '2024-11-01'\n" +
                    "   and chaveprimaria not in (\n" +
                    "   select\n" +
                    "    chaveprimaria\n" +
                    "   from\n" +
                    "    log\n" +
                    "   where\n" +
                    "    nomeentidade = 'MODALIDADE'\n" +
                    "    and operacao = 'EXCLUSÃO'\n" +
                    "    and nomeentidadedescricao = 'Modalidade'" +
                    "    and dataalteracao >= '2024-11-01')\n" +
                    "  order by\n" +
                    "   codigo desc) and mod.crossfit;");
            SituacaoClienteSinteticoDW sinteticoDW = new SituacaoClienteSinteticoDW(con);

            List<Integer> codContratosAlterados = new ArrayList<>();

            try (ResultSet rs = con.prepareStatement(sql.toString()).executeQuery()) {
                while (rs.next()) {
                    try {
                        codContratosAlterados.add(rs.getInt("contrato"));
                        sinteticoDW.atualizarInformacoesCrossfit(null, false, rs.getInt("pessoa"));
                        TreinoWSConsumer.atualizarCrossfit(chave, rs.getInt("codigomatricula"), rs.getBoolean("crossfit"));
                    } catch (Exception e) {
                        Uteis.logar(e, Modalidade.class);
                    }
                }
            }
            Uteis.logarDebug(String.format("Contratos afetados para a chave %s: ", chave) + codContratosAlterados);
            Uteis.logarDebug(String.format("FIM para a chave %s | AjustarCrossfitContratoRunner", chave));
        } catch (Exception ex) {
            Uteis.logarDebug(String.format("ERRO para a chave %s | AjustarCrossfitContratoRunner - " + ex.getMessage(), chave));
        }
    }
}

package servicos.adm;

import br.com.pactosolucoes.enumeradores.EImportacaoClinteEstacionamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaConfigEstacionamentoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.negocio.comuns.basico.ConsultaTO;
import relatorio.negocio.comuns.basico.ConsultasCadastradasTO;
import servicos.integracao.IntegracaoCadastrosWSConsumer;
import servicos.propriedades.PropsService;
import servicos.util.FTPSimple;
import servicos.util.SFTP;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Date;

/*
 * Created by Rafael on 17/05/2016.
 */
public class IntegracaoEstacionamento {

    private static String key = "";

    public static void main(String args[]) throws Exception{
        try{
            key = "selfit";
            Connection con = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : key);
            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(7, Uteis.NIVELMONTARDADOS_TODOS);
            gerarArquivoEnviarFTP(false, empresaVO.getConfigEstacionamento(), con, empresaVO.getCodigo());
        }catch (Exception erro){
            erro.printStackTrace();
        }
    }

    public static void gerarArquivoEnviarFTP(boolean adicionarAlunosIntegracaoAcesso, String host, String user, String pwd, int porta, String localArq, Connection con) {
        gerarArquivoEnviarFTP(adicionarAlunosIntegracaoAcesso, host, user, pwd, porta, localArq, con, null, EImportacaoClinteEstacionamento.COM_VALOR_COM_HORA);
    }

    public static void gerarArquivoEnviarFTP(boolean adicionarAlunosIntegracaoAcesso, String host, String user, String pwd, int porta, String localArq, Connection con, Integer empresa, EImportacaoClinteEstacionamento retornoValor) {
        gerarArquivoEnviarFTP(adicionarAlunosIntegracaoAcesso, host, user, pwd, porta, localArq, con, empresa, PropsService.getPropertyValue(PropsService.diretorioArquivos), retornoValor);
    }

    public static void gerarArquivoTesteEnviarFTP(String chave, boolean adicionarAlunosIntegracaoAcesso, EmpresaConfigEstacionamentoVO estConfig, Connection con, Integer empresa) throws Exception {
        key = chave;
        if (UteisValidacao.emptyString(estConfig.getNomeArquivo())) {
            estConfig.setNomeArquivo("teste_estacionamento.txt");
        }
        gerarArquivoEnviarFTP(adicionarAlunosIntegracaoAcesso, estConfig, con, empresa);
    }

    public static void gerarArquivoEnviarFTP(boolean adicionarAlunosIntegracaoAcesso, EmpresaConfigEstacionamentoVO estConfig, Connection con, Integer empresa) throws Exception {
        String path = "";
        try {
            String prefixoArquivo = String.format("%s-%s-%s", "AlunoEstacionamento", key, new Date().getTime());
            String textoSalvar = "";
            textoSalvar = new Cliente(con).obterImportacaoClinteEstacionamento(obterSqlClientesEstacionamento(empresa, estConfig, false).toString(), null, estConfig);
            // clientes dependentes
            textoSalvar += new Cliente(con).obterImportacaoClinteEstacionamento(obterSqlClientesEstacionamento(empresa, estConfig, true).toString(), null, estConfig);

            if (adicionarAlunosIntegracaoAcesso) {
                textoSalvar += buscarAlunosAutorizacaoAcesso(con);
            }

            path = Uteis.salvarArquivo(prefixoArquivo, textoSalvar, PropsService.getPropertyValue(PropsService.diretorioArquivos));
            try {
                if (null != path && !path.trim().isEmpty()) {
                    Path fullPath = Paths.get(path);
                    if (fullPath.toFile().exists()) {
                        System.out.println(Calendario.hoje() +  " - Tamanho do arquivo gerado: " + Files.size(fullPath));
                    }
                }
            } catch (Exception ex) {
                System.out.println("Falha ao obter informações do arquivo");
                ex.printStackTrace();
            }

            try {
                //Servidor usa protocolo FTP Tradicional com ou sem TLS, se o host começar com 'sftp' instancia conector com TLS, caso contrário usa FTP convencional, sem criptografia
                if (estConfig.getPort() == 21) {
                    FTPSimple ftp = new FTPSimple(estConfig.getHost(), estConfig.getUser(), estConfig.getPass(), estConfig.getPort());
                    ftp.putFile(path, estConfig.getNomeArquivo());
                } else { //caso a porta não seja OUTRA tenta utilizar protocolo SFTP (Secure File Transfer Protocol) sobre SSH
                    SFTP sftp = new SFTP(estConfig.getHost(), estConfig.getUser(), estConfig.getPass(), estConfig.getPort());
                    sftp.putFile(path, estConfig.getNomeArquivo());
                }
            } catch (Exception e){
                Uteis.logar(null, "ERRO em PUT arquivo -> " + e.getMessage());
                if ((e.getMessage() != null && e.getMessage().contains("No such file")) || e.getMessage() != null && e.getMessage().contains("No such file")) {
                    throw new Exception("Erro ao salvar arquivo no FTP, verifique se o diretório existe ou se possui as permissões de acesso!");
                } else {
                    throw e;
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, IntegracaoEstacionamento.class);
            throw ex;
        } finally {
            if (null != path && !path.trim().isEmpty()) {
                try {
                    new File(path).delete();
                } catch (Exception ignored) {
                    Uteis.logar("ERRO ao remover arquivo temporario de dados para estacionamento");
                }
            }
        }

    }

    private static StringBuilder obterSqlClientesEstacionamento(Integer empresa, EmpresaConfigEstacionamentoVO estConfig, boolean clientesDependentes) {
        Date dia = Uteis.somarDias(Calendario.hoje(), -1);

        StringBuilder sqlEstacionamento = new StringBuilder();
        sqlEstacionamento.append("SELECT DISTINCT\n")
                .append("cli.codigomatricula as matriculaCliente\n")
                .append(",pes.nome as nomeAluno\n")
                .append(",cli.pessoaresponsavel as responsavel\n");
        if (clientesDependentes) {
            sqlEstacionamento.append(",cd.datainicio as validadeInicial\n");
            sqlEstacionamento.append(",cd.datafinalajustada as validadeFinal\n");
        } else {
            sqlEstacionamento.append(",con.vigenciade as validadeInicial\n");
            sqlEstacionamento.append(",con.vigenciaateajustada as validadeFinal\n");
        }
        sqlEstacionamento.append(",cli.situacao as situacao\n");
        sqlEstacionamento.append(",pes.cfp as CPF\n");
        if (estConfig.isEnviaValor()) {
            sqlEstacionamento.append(clientesDependentes ? ",0" : ",con.valorfinal").append(" as ValorFinalContrato\n");
        }
        if (estConfig.isEnviaTelefoneEmail()) {
            sqlEstacionamento.append(",coalesce((select tel.numero from telefone tel where tel.pessoa = pes.codigo order by tel.codigo desc limit 1),'')  as telefone \n");
            sqlEstacionamento.append(",coalesce((select e.email from email e where e.pessoa = pes.codigo order by e.codigo desc limit 1),'')  as email \n");
        }
        sqlEstacionamento.append("FROM Cliente cli\n")
                .append("INNER JOIN pessoa pes on cli.pessoa = pes.codigo\n");
        if (clientesDependentes) {
            sqlEstacionamento.append("INNER JOIN contratodependente cd on cd.cliente = cli.codigo\n")
                    .append("AND '").append(Uteis.getData(dia)).append("' BETWEEN cd.datainicio::DATE AND cd.datafinalajustada::DATE \n")
                    .append("INNER JOIN contrato con ON con.codigo = cd.contrato \n")
                    .append("INNER JOIN plano p on p.codigo = con.plano \n");
        } else {
            sqlEstacionamento.append("INNER JOIN contrato con on con.pessoa = cli.pessoa\n")
                    .append("AND '").append(Uteis.getData(dia)).append("' BETWEEN con.vigenciaDe::DATE AND con.vigenciaateajustada::DATE\n")
                    .append("INNER JOIN plano p on p.codigo = con.plano\n");
        }
        sqlEstacionamento.append("LEFT JOIN planoempresa pe on pe.plano = p.codigo \n")
                .append("LEFT JOIN movproduto prod on prod.pessoa = pes.codigo\n");

        if (!UteisValidacao.emptyString(estConfig.getProdutosAdicionar())) {
            sqlEstacionamento.append(" AND '").append(Uteis.getData(dia)).append("' BETWEEN prod.datainiciovigencia::DATE and prod.datafinalvigencia\n");
        }

        sqlEstacionamento.append("WHERE 1 = 1 \n");
        if (empresa != null) {
            sqlEstacionamento.append("AND cli.empresa = ").append(empresa).append("\n");
            sqlEstacionamento.append("or (pe.codigo is not null and pe.acesso and pe.empresa = ").append(empresa).append(" and p.permitiracessosomentenaempresavendeucontrato = false) \n");
        }
        if (!UteisValidacao.emptyString(estConfig.getProdutosAdicionar())) {
            sqlEstacionamento.append("or prod.produto in (").append(estConfig.getProdutosAdicionar()).append(")\n");
        }
        return sqlEstacionamento;
    }

    public static void gerarArquivoEnviarFTP(boolean adicionarAlunosIntegracaoAcesso, String host, String user, String pwd, int porta, String localArq, Connection con, Integer empresa, String diretorioSalvar) {
        gerarArquivoEnviarFTP(adicionarAlunosIntegracaoAcesso, host, user, pwd, porta, localArq, con, empresa, diretorioSalvar, EImportacaoClinteEstacionamento.COM_VALOR_COM_HORA);
    }

    public static void gerarArquivoEnviarFTP(boolean adicionarAlunosIntegracaoAcesso, String host, String user, String pwd, int porta, String localArq, Connection con, Integer empresa, String diretorioSalvar, EImportacaoClinteEstacionamento retornoValor) {
        String path = "";
        try {
            ConsultaTO consulta = ConsultasCadastradasTO.obterConsultaAlunoEstacionamento(Uteis.somarDias(Calendario.hoje(), -1), retornoValor);
            if (empresa != null) {
                consulta.setWhere(consulta.getWhere() + "\nAND cli.empresa = " + empresa);
            }
            String prefixoArquivo = String.format("%s-%s-%s", consulta.getNomeArquivo(), key, new Date().getTime());
            String textoSalvar = "";
            textoSalvar = new Cliente(con).obterImportacaoClinteEstacionamento(consulta.getSQLMontada() + " WHERE "+consulta.getWhere(),empresa, retornoValor);
            if (adicionarAlunosIntegracaoAcesso) {
                textoSalvar += buscarAlunosAutorizacaoAcesso(con);
            }
            path = Uteis.salvarArquivo(prefixoArquivo, textoSalvar, diretorioSalvar);

            try{
                //Com servidor FTP com segurança
                SFTP sftp = new SFTP(host,user,pwd,porta);
                sftp.putFile(path,localArq);
            } catch (Exception e){
                try {
                    //Com servidor FTP sem segurança
                    FTPSimple ftp = new FTPSimple(host,user,pwd,porta);
                    ftp.putFile(path,localArq);
                } catch (ConsistirException e1) {
                    Uteis.logar(null, "ERRO em PUT arquivo -> " + e1.getMessage());
                    throw e1;
                } catch (Exception e2) {
                    Uteis.logar(null, "ERRO em PUT arquivo -> " + e.getMessage());
                    throw e;
                }
            }
        }catch (Exception ex){
            Uteis.logar(ex, IntegracaoEstacionamento.class);
        }finally {
            if(null != path && !path.trim().isEmpty()){
                try {
                    new File(path).delete();
                }catch (Exception ignored){
                    Uteis.logar("ERRO ao remover arquivo temporariao de dados para estacionamento");
                }
            }
        }
    }

    public static String buscarAlunosAutorizacaoAcesso(Connection con) {
        try {
            String sql = "select * from \n" +
                    "(select \n" +
                    "i.urlzillyonweb as url,\n" +
                    "i.chave as chave,\n" +
                    "(SELECT array_to_string(foo, ',') FROM (SELECT ARRAY(SELECT codigopessoa :: TEXT FROM autorizacaoacessogrupoempresarial where integracaoacessogrupoempresarial = i.codigo) foo) AS a) as pessoas\n" +
                    "from integracaoacessogrupoempresarial  i) as sql\n" +
                    "where pessoas <> '' limit 1 ";
            PreparedStatement sqlConsultar = con.prepareStatement(sql);
            ResultSet rs = sqlConsultar.executeQuery();
            StringBuilder alunosAdicionar = new StringBuilder();
            while (rs.next()) {
                alunosAdicionar.append(IntegracaoCadastrosWSConsumer.getListaClientesEstacionamentoSelfit(rs.getString("url"), rs.getString("chave"), rs.getString("pessoas")));
            }
            return alunosAdicionar.toString();
        } catch (Exception ex) {
            Uteis.logar(ex, IntegracaoEstacionamento.class);
            return "";
        }
    }
}

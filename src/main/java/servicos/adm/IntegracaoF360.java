package servicos.adm;

import br.com.pactosolucoes.enumeradores.EImportacaoClinteEstacionamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.GestaoRecebiveisControle;
import negocio.comuns.basico.EmpresaConfigEstacionamentoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.controle.financeiro.FaturamentoSinteticoControleRel;
import relatorio.negocio.comuns.basico.ConsultaTO;
import relatorio.negocio.comuns.basico.ConsultasCadastradasTO;
import servicos.integracao.IntegracaoCadastrosWSConsumer;
import servicos.propriedades.PropsService;
import servicos.util.SFTP;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/*
 * Created by Rafael on 17/05/2016.
 */
public class IntegracaoF360 {

    public static void main(String args[]) throws Exception {
        try {
            String key = "d867a043a14520b67f35b5bb7dabf3e1";
            Connection con = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : key);
            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_TODOS);
            IntegracaoF360.gerarArquivoEnviarFTPTeste(empresaVO.getIntegracaoF360FtpServer(), empresaVO.getIntegracaoF360User(), empresaVO.getIntegracaoF360Password(), empresaVO.getIntegracaoF360FtpPort(), empresaVO.getIntegracaoF360Dir(), con);
        } catch (Exception erro) {
            erro.printStackTrace();
        }
    }

    public static void gerarArquivoEnviarFTP(EmpresaVO empresaVO, Date dataInicio, Date dataTermino, Connection con) throws Exception {
        String pathDiario = "";
        String pathQuinzenal = "";
        try {
            GestaoRecebiveisControle gestaoRecebiveisControle = new GestaoRecebiveisControle(empresaVO, false);
            Uteis.logar("Instanciei o gestaoRecebiveisControle");
            preencherGestaoRecebiveisControle(empresaVO, gestaoRecebiveisControle);
            Uteis.logar("preencherGestaoRecebiveisControle --> OK");
            pathDiario = gestaoRecebiveisControle.gerarRelatorioXlsF360PorPeriodo(empresaVO, dataTermino, dataTermino);
            Uteis.logar("Relatório diário gerado com sucesso! --> " + pathDiario);

            if (UteisValidacao.emptyString(pathDiario)) {
                Uteis.logar("Nenhum pathDiario para enviar ao FTP F360");
            } else {
                String dataStringDiario = Calendario.getDataAplicandoFormatacao(dataTermino, "yyyy-MM-dd_HH-mm");
                String nomeArquivoFtp = String.format("%s_%s.xls", "RelatorioFaturamento_diario", dataStringDiario);
                enviarArquivoF360(pathDiario, nomeArquivoFtp, empresaVO);
                try {
                    new File(pathDiario).delete();
                } catch (Exception ignored) {
                    Uteis.logar("ERRO ao remover arquivo temporario de dados diário para F360");
                }
            }

            if (empresaVO.isIntegracaoF360Quinzenal()) {
                Uteis.logar("Vou gerar o relatório quinzenal para enviar ao FTP F360...");
                pathQuinzenal = gestaoRecebiveisControle.gerarRelatorioXlsF360PorPeriodo(empresaVO, dataInicio, dataTermino);
                if (!UteisValidacao.emptyString(pathDiario)) {
                    String dataStringQuinzenal = Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd_HH-mm");
                    String nomeArquivoFtp = String.format("%s-%s.xls", "RelatorioFaturamento_quinzenal", dataStringQuinzenal);
                    enviarArquivoF360(pathQuinzenal, nomeArquivoFtp, empresaVO);
                    try {
                        new File(pathQuinzenal).delete();
                    } catch (Exception ignored) {
                        Uteis.logar("ERRO ao remover arquivo temporario de dados quinzenal para F360");
                    }
                    Uteis.logar("Relatório quinzenal gerado com sucesso! --> " + pathQuinzenal);
                }
            }

        } catch (Exception ex) {
            Uteis.logar(ex, IntegracaoF360.class);
            if (ex.getMessage() != null && ex.getMessage().contains("No such file")) {
                throw new Exception("Erro ao salvar arquivo, verifique se o diretório existe ou se possui as permissões de acesso!");
            }
            if (!UteisValidacao.emptyString(pathDiario)) {
                try {
                    new File(pathDiario).delete();
                } catch (Exception ignored) {
                    Uteis.logar("ERRO ao remover arquivo temporario de dados diário para F360");
                }
            }

            if (!UteisValidacao.emptyString(pathQuinzenal)) {
                try {
                    new File(pathQuinzenal).delete();
                } catch (Exception ignored) {
                    Uteis.logar("ERRO ao remover arquivo temporario de dados quinzenal para F360");
                }
            }
            throw ex;
        }

    }

    private static void enviarArquivoF360(String pathArquivo, String nomeArquivoFtp, EmpresaVO empresaVO) throws Exception {
        String dir = formatarNomeDiretorioFtpF360(empresaVO);
        Uteis.logar("formatarNomeDiretorioFtpF360 --> OK | " + dir);

        SFTP sftp = new SFTP(empresaVO.getIntegracaoF360FtpServer(), empresaVO.getIntegracaoF360User(), empresaVO.getIntegracaoF360Password(), empresaVO.getIntegracaoF360FtpPort());
        Uteis.logar("Criei o sftp");
        Uteis.logar("Vou enviar o arquivo  para o FTP F360... " + nomeArquivoFtp);
        sftp.putFileWithMkdir(pathArquivo, dir, dir + nomeArquivoFtp, "sftp", true );
        Uteis.logar("Arquivo: " + dir + nomeArquivoFtp);
        Uteis.logar("Enviado com Sucesso!");


    }

    private static void preencherGestaoRecebiveisControle(EmpresaVO empresaVO, GestaoRecebiveisControle gestaoRecebiveisControle) {
        gestaoRecebiveisControle.getEmpresasSelecionadas().add(empresaVO.getCodigo().toString());

        List<EmpresaVO> empresaVOList = new ArrayList<>();
        empresaVOList.add(empresaVO);
        gestaoRecebiveisControle.setEmpresas(empresaVOList);

        List<MovContaVO> devolucoesList = new ArrayList<>();
        gestaoRecebiveisControle.setDevolucoes(devolucoesList);
    }

    public static void gerarArquivoEnviarFTPTeste(String host, String user, String pwd, int port, String dir, Connection con) throws Exception {
        String path = "";
        try {
            String nomeArquivoFtp = String.format("%s-%s.txt", "teste_integracao", new Date().getTime());
            String textoSalvar = "Teste integração";
            path = Uteis.salvarArquivo(nomeArquivoFtp, textoSalvar, PropsService.getPropertyValue(PropsService.diretorioArquivos));

            dir = dir != null ? dir : "";
            dir += "/teste_integracao/";

            SFTP sftp = new SFTP(host, user, pwd, port);
            sftp.putFileWithMkdir(path, dir, dir + nomeArquivoFtp);
        } catch (Exception ex) {
            Uteis.logar(ex, IntegracaoF360.class);
            if (ex.getMessage() != null && ex.getMessage().contains("No such file")) {
                throw new Exception("Erro ao salvar arquivo, verifique se o diretório existe ou se possui as permissões de acesso!");
            }
            throw ex;
        } finally {
            if (null != path && !path.trim().isEmpty()) {
                try {
                    new File(path).delete();
                } catch (Exception ignored) {
                    Uteis.logar("ERRO ao remover arquivo temporariao de dados F360");
                }
            }
        }
    }

    private static String formatarNomeDiretorioFtpF360(EmpresaVO empresaVO) {
        String dir = empresaVO.getIntegracaoF360Dir() != null ? empresaVO.getIntegracaoF360Dir() : "";

        String nfdNormalizedString = Normalizer.normalize(empresaVO.getNome(), Normalizer.Form.NFD);
        nfdNormalizedString = Uteis.removerCaracteresNaoAscii(nfdNormalizedString).replace("-"," ").replace("   "," ").replace("  "," ");

        Pattern pattern = Pattern.compile("[\\p{InCombiningDiacriticalMarks}!@#$%&\\*\\{\\}\\[\\]\\?:><,.|\\\\/\"\\+]");
        dir += "/" + pattern.matcher(nfdNormalizedString).replaceAll("").replace(" ", "_") + "/";

        return dir;
    }
}

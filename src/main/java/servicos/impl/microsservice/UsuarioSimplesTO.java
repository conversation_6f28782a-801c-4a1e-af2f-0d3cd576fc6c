package servicos.impl.microsservice;

import org.json.JSONObject;

public class UsuarioSimplesTO {

    private String chave;
    private int codZw;
    private String username;
    private boolean administrador;
    private int idEmpresa;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public int getCodZw() {
        return codZw;
    }

    public void setCodZw(int codZw) {
        this.codZw = codZw;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public boolean isAdministrador() {
        return administrador;
    }

    public void setAdministrador(boolean administrador) {
        this.administrador = administrador;
    }

    public int getIdEmpresa() {
        return idEmpresa;
    }

    public void setIdEmpresa(int idEmpresa) {
        this.idEmpresa = idEmpresa;
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }
}

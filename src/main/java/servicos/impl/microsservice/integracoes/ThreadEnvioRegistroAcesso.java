package servicos.impl.microsservice.integracoes;

import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.util.Base64;
import java.util.Date;

public class ThreadEnvioRegistroAcesso implements Runnable, AutoCloseable {

    private final String urlIntegracao;
    private final String tokenIntegracao;
    private final String empresaIdExterno;
    private final String matriculaCliente;
    private final Date dataHoraAcesso;
    private final String meioIdentificacao;
    private final String sentidoAcesso;

    public ThreadEnvioRegistroAcesso(String urlIntegracao, String tokenIntegracao, String empresaIdExterno,
                                     String matriculaCliente, Date dataHoraAcesso, String meioIdentificacao, String sentidoAcesso) {
        this.urlIntegracao = urlIntegracao;
        this.tokenIntegracao = tokenIntegracao;
        this.empresaIdExterno = empresaIdExterno;
        this.matriculaCliente = matriculaCliente;
        this.dataHoraAcesso = dataHoraAcesso;
        this.meioIdentificacao = meioIdentificacao;
        this.sentidoAcesso = sentidoAcesso;
    }

    public void run() {
        try {
            if (!UteisValidacao.emptyString(empresaIdExterno)) {
                org.json.JSONObject jsonObjectData = new org.json.JSONObject();
                org.json.JSONObject jsonObject = new org.json.JSONObject();
                jsonObject.put("matricula", matriculaCliente);
                jsonObject.put("datahoraacesso", String.valueOf(dataHoraAcesso.getTime()));
                jsonObject.put("sentidoAcesso", sentidoAcesso);
                jsonObject.put("meioIdentificacao", meioIdentificacao);
                jsonObject.put("idExternoEmpresa", empresaIdExterno);

                jsonObjectData.put("data", Base64.getEncoder().encodeToString(jsonObject.toString().getBytes()));

                final RequestConfig params = RequestConfig.custom().setConnectTimeout(2000).setSocketTimeout(2000).build();

                try (CloseableHttpClient client = HttpClients.createDefault()) {
                    HttpPost httpPost = new HttpPost(urlIntegracao);
                    httpPost.setConfig(params);

                    httpPost.setHeader("Content-type", "application/json");
                    httpPost.setHeader("Authorization", "Bearer " + tokenIntegracao);
                    httpPost.setEntity(new StringEntity(jsonObjectData.toString(), "UTF8"));


                    HttpResponse response = client.execute(httpPost);
                    int statusCode = response.getStatusLine().getStatusCode();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    public void close() throws Exception {
    }
}

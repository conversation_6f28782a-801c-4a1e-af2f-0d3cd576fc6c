package servicos.impl.microsservice.integracoes;

public class PessoaCDLTO {

    private EnderecoCDLTO endereco;
    private String nome;
    private String cpf;
    private long dataNascimento;
    private TelefoneCDLTO telefone;
    private String numeroRg;
    private String ufRg;
    private String email;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public long getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(long dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public EnderecoCDLTO getEndereco() {
        if (endereco == null) {
            endereco = new EnderecoCDLTO();
        }
        return endereco;
    }

    public void setEndereco(EnderecoCDLTO endereco) {
        this.endereco = endereco;
    }

    public TelefoneCDLTO getTelefone() {
        return telefone;
    }

    public void setTelefone(TelefoneCDLTO telefone) {
        this.telefone = telefone;
    }

    public String getNumeroRg() {
        return numeroRg;
    }

    public void setNumeroRg(String numeroRg) {
        this.numeroRg = numeroRg;
    }

    public String getUfRg() {
        return ufRg;
    }

    public void setUfRg(String ufRg) {
        this.ufRg = ufRg;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

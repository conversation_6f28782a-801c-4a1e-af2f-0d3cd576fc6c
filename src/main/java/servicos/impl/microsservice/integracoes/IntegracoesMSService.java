package servicos.impl.microsservice.integracoes;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import servicos.impl.microsservice.SuperMSService;
import servicos.util.ExecuteRequestHttpService;

import java.net.ConnectException;
import java.util.HashMap;
import java.util.Map;

public class IntegracoesMSService extends SuperMSService {

    private static final Map<String, RetornoConsultaCDLTO> consultasCDL = new HashMap<>();
    private static final String SPC_SERVICE_KEY = "integr@c@0SPC";

    public static RetornoConsultaCDLTO consultar(String urlIntegracoesMS, String cpfConsultar, String operador, String senha, Long codigoAssociado, String chave) throws Exception {
        RetornoConsultaCDLTO retornoConsultaCDLTO = consultasCDL.get(cpfConsultar);
        if (retornoConsultaCDLTO != null) {
            return retornoConsultaCDLTO;
        }

        final String templateUrl = "%s/cdl-cds/v1/consultar?cpf=%s";
        final String url = String.format(templateUrl,
                urlIntegracoesMS,
                cpfConsultar);

        try {
            Map<String, String> headers = getHeaders(operador, senha, codigoAssociado,chave);
            String response = ExecuteRequestHttpService.post(url, null, headers);
            JSONObject objResponse = new JSONObject(response);
            if (objResponse.has(CONTENT_RESPONSE)) {
                JSONObject content = objResponse.getJSONObject(CONTENT_RESPONSE);
                retornoConsultaCDLTO = JSONMapper.getObject(content, RetornoConsultaCDLTO.class);
                consultasCDL.put(cpfConsultar, retornoConsultaCDLTO);
                return retornoConsultaCDLTO;
            } else {
                String errorMessage = objResponse.optString("message");
                throw new IntegracoesMSException(errorMessage);
            }
        } catch (Exception e) {
            if (e instanceof ConnectException) {
                throw new ConnectException("Não foi possível conectar ao servidor para realizar esta operação.");
            }

            e.printStackTrace();
            throw new IntegracoesMSException(messageError(e.getMessage(), true));
        }

    }

    public static String negativar(String urlIntegracoesMS, MovParcelaCDLTO movParcela, String operador, String senha, Long codigoAssociado, String chave) throws IntegracoesMSException {
        final String templateUrl = "%s/cdl-cds/v1/incluirSPC";
        final String url = String.format(templateUrl, urlIntegracoesMS);

        try {
            Map<String, String> headers = getHeaders(operador, senha, codigoAssociado, chave);
            String response = ExecuteRequestHttpService.post(url, movParcela.toString(), headers);
            JSONObject objResponse = new JSONObject(response);
            if (objResponse.has(CONTENT_RESPONSE)) {
                return objResponse.optString(CONTENT_RESPONSE);
            } else {
                return objResponse.optString("message");
            }
        } catch (Exception e) {
            e.printStackTrace();
            String errorMessage;
            try {
                JSONObject objError = new JSONObject(e.getMessage());
                if (objError.has("meta")) {
                    objError = objError.optJSONObject("meta");
                }
                errorMessage = objError.optString("message");
                return errorMessage;
            } catch (Exception ignored) {
                throw new IntegracoesMSException(messageError(e.getMessage()));
            }
        }
    }

    public static String removerNegativacao(String urlIntegracoesMS, String movParcelaCDLTO, String operador, String senha, Long codigoAssociado, String chave) throws IntegracoesMSException {
        final String templateUrl = "%s/cdl-cds/v1/excluirSPC";
        final String url = String.format(templateUrl, urlIntegracoesMS);

        try {
            Map<String, String> headers = getHeaders(operador, senha, codigoAssociado, chave);
            String response = ExecuteRequestHttpService.post(url, movParcelaCDLTO, headers);
            JSONObject objResponse = new JSONObject(response);
            if (objResponse.has(CONTENT_RESPONSE)) {
                return objResponse.optString(CONTENT_RESPONSE);
            } else {
                String errorMessage = objResponse.optString("message");
                throw new IntegracoesMSException(errorMessage);
            }
        } catch (Exception e) {
            String errorMessage;
            try {
                JSONObject objError = new JSONObject(e.getMessage());
                if (objError.has("meta")) {
                    objError = objError.optJSONObject("meta");
                }
                errorMessage = objError.optString("message");
                return errorMessage;
            } catch (Exception ignored) {
                throw new IntegracoesMSException(messageError(e.getMessage()));
            }
        }
    }

    private static Map<String, String> getHeaders(String operador, String senha, Long codigoAssociado, String chave) {
        UsuarioCDLTO usuarioCDLTO = new UsuarioCDLTO(codigoAssociado, operador, senha, chave);
        Map<String, String> headers = new HashMap<>();
        headers.put(AUTHORIZATION_HEADER, Uteis.encriptar(usuarioCDLTO.toJSON(), SPC_SERVICE_KEY));
        return headers;
    }

}

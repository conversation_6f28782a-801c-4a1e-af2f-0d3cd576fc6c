package servicos.impl.microsservice.integracoes;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.logging.Level;
import java.util.logging.Logger;

public class MovParcelaCDLTO {

    private long dataRegistro;
    private long dataVencimento;
    private String codigoContrato;
    private Double valorParcela;
    private PessoaCDLTO pessoa;

    public long getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(long dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public long getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(long dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public String getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(String codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public PessoaCDLTO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaCDLTO();
        }
        return pessoa;
    }

    public void setPessoa(PessoaCDLTO pessoa) {
        this.pessoa = pessoa;
    }

    @Override
    public String toString() {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(this);
        } catch (Exception e) {
            Logger.getLogger(SuperJSON.class.getName()).log(Level.SEVERE, e.getMessage());
            return null;
        }
    }
}


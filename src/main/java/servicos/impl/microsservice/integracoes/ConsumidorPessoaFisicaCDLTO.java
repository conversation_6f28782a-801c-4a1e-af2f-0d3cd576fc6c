package servicos.impl.microsservice.integracoes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsumidorPessoaFisicaCDLTO {

    private EnderecoCDLTO endereco;
    private TelefoneCDLTO telefoneResidencial;
    private TelefoneCDLTO telefoneCelular;
    private long dataNascimento;
    private String email;
    private String nome;
    private String sexo;

    public EnderecoCDLTO getEndereco() {
        return endereco;
    }

    public void setEndereco(EnderecoCDLTO endereco) {
        this.endereco = endereco;
    }

    public TelefoneCDLTO getTelefoneResidencial() {
        return telefoneResidencial;
    }

    public void setTelefoneResidencial(TelefoneCDLTO telefoneResidencial) {
        this.telefoneResidencial = telefoneResidencial;
    }

    public TelefoneCDLTO getTelefoneCelular() {
        return telefoneCelular;
    }

    public void setTelefoneCelular(TelefoneCDLTO telefoneCelular) {
        this.telefoneCelular = telefoneCelular;
    }

    public long getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(long dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }
}

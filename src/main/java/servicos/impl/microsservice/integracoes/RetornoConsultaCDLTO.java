package servicos.impl.microsservice.integracoes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RetornoConsultaCDLTO {

    private ProtocoloCDLTO protocolo;
    private ConsumidorCDLTO consumidor;
    private SpcCDLTO spc;
    private boolean restricao;
    private Date data;

    public ProtocoloCDLTO getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(ProtocoloCDLTO protocolo) {
        this.protocolo = protocolo;
    }

    public ConsumidorCDLTO getConsumidor() {
        return consumidor;
    }

    public void setConsumidor(ConsumidorCDLTO consumidor) {
        this.consumidor = consumidor;
    }

    public SpcCDLTO getSpc() {
        return spc;
    }

    public void setSpc(SpcCDLTO spc) {
        this.spc = spc;
    }

    public boolean isRestricao() {
        return restricao;
    }

    public void setRestricao(boolean restricao) {
        this.restricao = restricao;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }
}

package servicos.impl.microsservice.integracoes;

import org.json.JSONObject;

public class UsuarioCDLTO {

    private final Long codigoAssociado;
    private final String operador;
    private final String password;
    private final String tipoAmbiente;
    private final String chave;

    public UsuarioCDLTO(Long codigoAssociado, String operador, String password, String chave) {
        this.codigoAssociado = codigoAssociado;
        if (operador.equals("7805931")) {
            this.operador = "7805931";
            this.password = "temp@123";
            this.tipoAmbiente = "TESTE";
        } else {
            this.operador = operador;
            this.password = password;
            this.tipoAmbiente = "PRODUCAO";
        }
        this.chave = chave;

    }

    public Long getCodigoAssociado() {
        return codigoAssociado;
    }

    public String getOperador() {
        return operador;
    }

    public String getPassword() {
        return password;
    }

    public String getTipoAmbiente() {
        return tipoAmbiente;
    }

    public String getChave() {
        return chave;
    }

    public String toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigoAssociado", this.codigoAssociado);
        json.put("operador", this.operador);
        json.put("password", this.password);
        json.put("ambiente", this.tipoAmbiente);
        json.put("chave", this.chave);
        return json.toString();
    }


}

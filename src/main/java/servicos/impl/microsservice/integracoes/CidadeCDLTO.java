package servicos.impl.microsservice.integracoes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CidadeCDLTO {

    private EstadoCDLTO estado;
    private String nome;

    public EstadoCDLTO getEstado() {
        return estado;
    }

    public void setEstado(EstadoCDLTO estado) {
        this.estado = estado;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}

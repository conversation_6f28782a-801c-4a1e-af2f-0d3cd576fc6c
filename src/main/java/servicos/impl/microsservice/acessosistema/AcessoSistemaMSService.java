package servicos.impl.microsservice.acessosistema;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.microsservice.SuperMSService;
import servicos.util.ExecuteRequestHttpService;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class AcessoSistemaMSService extends SuperMSService {

    private static final String ACCESS_AUTHORIZATION_METHOD = "autorizacoesAcesso";
    private static final String PUB_SUB_METHOD = "pubsub";

    private AcessoSistemaMSService() {
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO findById(Integer codigo, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s/%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, codigo);
        try {
            JSONObject objRetorno = get(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO findByCodigoAutorizacao(String codAutorizacao, RedeEmpresaVO redeEmpresa) {
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### Entrou no método findByCodigoAutorizacao ");
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### PARÂMETROS");
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### codAutorizacao: " + codAutorizacao);
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### redeEmpresa diferente de nulo: " + redeEmpresa != null ? "sim" : "não");
        if (redeEmpresa != null) {
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### redeEmpresa.getServiceMap() diferente de nulo: " + redeEmpresa.getServiceMap() != null ? "sim" : "não");
        }
        if (redeEmpresa.getServiceMap() != null) {
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### redeEmpresa.getServiceMap().getAcessoSistemaMsUrl() diferente de nulo: " + redeEmpresa.getServiceMap().getAcessoSistemaMsUrl() != null ? redeEmpresa.getServiceMap().getAcessoSistemaMsUrl() : "não");
        }
        String url = String.format("%s/%s?codAutorizacao=%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, codAutorizacao);
        try {
            JSONObject objRetorno = get(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### ERRO AO LOCALIZAR COIGO AUTORIZAÇÃO");
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### CLASSE: AcessoSistemaMSService.java");
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### MÉTODO: findByCodigoAutorizacao");
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### EXCEPTION: " + ex.getMessage());
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO findByCPF(String cpf, String tipoPessoa, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s?cpf=%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, cpf);
        if (!UteisValidacao.emptyString(tipoPessoa)) {
            url = url + "&tipoPessoa="+tipoPessoa;
        }

        try {
            JSONObject objRetorno = get(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO findByMatriculaAndChave(String matricula, String chave, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s?matricula=%s&chaveOrigem=%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, matricula, chave);
        try {
            JSONObject objRetorno = get(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO findByMatriculaAndCodAcesso(String matricula, String codigoAcesso, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s?matricula=%s&codAcesso=%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, matricula, codigoAcesso);
        try {
            JSONObject objRetorno = get(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static List<AutorizacaoAcessoGrupoEmpresarialVO> findAllByNameOrCPF(String paramToFind, RedeEmpresaVO redeEmpresa) {
        try {
            String url = String.format("%s/%s/findAll?param=%s",
                    redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(),
                    ACCESS_AUTHORIZATION_METHOD,
                    URLEncoder.encode(paramToFind, "UTF-8"));

            JSONArray objRetorno = getList(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getList(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new ArrayList<>();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO insertAccessAuthorization(AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcesso, String originKey, Integer originEmpresa, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s/%s/%d/save", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, originKey, originEmpresa);
        try {
            JSONObject body = autorizacaoAcesso.toJSON();
            body.put("urlServidorBiometricoUnificado", redeEmpresa.getUrlServidorBiometricoUnificado());

            JSONObject objRetorno = post(redeEmpresa.getChaveFranqueadora(), redeEmpresa.getCodigoUnidadeFranqueadora(), body.toString(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO updateAccessAuthorization(AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcesso, String newOriginKey, Integer newOriginEmpresa, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s/%s/%d/update", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, newOriginKey, newOriginEmpresa);
        try {
            JSONObject body = autorizacaoAcesso.toJSON();
            body.put("urlServidorBiometricoUnificado", redeEmpresa.getUrlServidorBiometricoUnificado());

            JSONObject objRetorno = post(redeEmpresa.getChaveFranqueadora(), redeEmpresa.getCodigoUnidadeFranqueadora(), body.toString(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static void deleteAccessAuthorization(final AutorizacaoAcessoGrupoEmpresarialVO autorizacaoAcesso, final RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s/delete", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD);
        try {
            JSONObject body = autorizacaoAcesso.toJSON();
            body.put("urlServidorBiometricoUnificado", redeEmpresa.getUrlServidorBiometricoUnificado());

            post(redeEmpresa.getChaveFranqueadora(), redeEmpresa.getCodigoUnidadeFranqueadora(), body.toString(), url);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO updateAccessAuthorization(final AutorizacaoAcessoGrupoEmpresarialVO autorizacao, final RedeEmpresaVO redeEmpresa, Boolean callbackZW) {
        String url = String.format("%s/%s/%d", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, autorizacao.getCodigo());
        Uteis.logar(null, url);
        try {
            JSONObject body = new JSONObject();
            body.put("codigoAutorizacao", autorizacao.getCodigoAutorizacao());
            body.put("assinaturaBiometriaDigital", autorizacao.getAssinaturaBiometriaDigital());
            body.put("assinaturaBiometriaFacial", autorizacao.getAssinaturaBiometriaFacial());
            body.put("urlServidorBiometricoUnificado", redeEmpresa.getUrlServidorBiometricoUnificado());
            body.put("callbackZW", callbackZW);

            JSONObject objRetorno = patch(redeEmpresa.getChaveFranqueadora(), redeEmpresa.getCodigoUnidadeFranqueadora(), body.toString(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static AutorizacaoAcessoGrupoEmpresarialVO findOnCompany(Integer codigoGenerico, String tipoPessoa, String originKey, Integer originEmpresa, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s/%s/%d/find?codigoGenerico=%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), ACCESS_AUTHORIZATION_METHOD, originKey, originEmpresa, codigoGenerico);
        if (!UteisValidacao.emptyString(tipoPessoa)) {
            url = url + "&tipoPessoa="+tipoPessoa;
        }
        try {
            JSONObject objRetorno = get(redeEmpresa.getChaveFranqueadora(), url);
            return JSONMapper.getObject(objRetorno, AutorizacaoAcessoGrupoEmpresarialVO.class);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return new AutorizacaoAcessoGrupoEmpresarialVO();
    }

    public static JSONObject prepareQueue(String queueName, RedeEmpresaVO redeEmpresa) {
        String url = String.format("%s/%s/create-queue?queueName=%s", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), PUB_SUB_METHOD, queueName);
        Uteis.logar(null, url);
        try {
            return post(redeEmpresa.getChaveFranqueadora(), redeEmpresa.getCodigoUnidadeFranqueadora(), null, url);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static JSONObject publish(final AutorizacaoAcessoGrupoEmpresarialVO autorizacao, boolean deletar, RedeEmpresaVO redeEmpresa) {
        Uteis.logarDebug("Publishing message to queue " + autorizacao.getCodigoAutorizacao());
        String url = String.format("%s/%s/publish", redeEmpresa.getServiceMap().getAcessoSistemaMsUrl(), PUB_SUB_METHOD);
        try {
            JSONObject body = autorizacao.toJSON();
            body.put("chaveFranqueadora", redeEmpresa.getChaveFranqueadora());
            body.put("delete", deletar);

            return post(redeEmpresa.getChaveFranqueadora(), redeEmpresa.getCodigoUnidadeFranqueadora(), body.toString(), url);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private static JSONObject get(String chave, String url) throws Exception {
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### ENTROU NO MÉTODO get");
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### PARÂMETROS");
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### chave: " + chave);
        Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### url: " + url);
        Map<String, String> headers = new HashMap<>();
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject(CONTENT_RESPONSE);
        } catch (Exception e) {
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### ERRO AO LOCALIZAR COIGO AUTORIZAÇÃO");
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### CLASSE: AcessoSistemaMSService.java");
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### MÉTODO: get(String chave, String url)");
            Logger.getLogger(AcessoSistemaMSService.class.getName()).log(Level.INFO, "#### EXCEPTION: " + e.getMessage());
            e.printStackTrace();
            throw new AcessoSistemaMSException(messageError(e.getMessage()));
        }
    }

    private static JSONArray getList(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONArray(CONTENT_RESPONSE);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AcessoSistemaMSException(messageError(e.getMessage()));
        }
    }


    private static JSONObject post(String chave, Integer empresaId, String body, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        String zwToken = getTokenZW(chave, empresaId, getUsuarioAdmin());
        headers.put(ZW_AUTHORIZATION_HEADER, zwToken);

        try {
            String response = ExecuteRequestHttpService.post(url, body, headers);
            return new JSONObject(response).getJSONObject(CONTENT_RESPONSE);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AcessoSistemaMSException(messageError(e.getMessage()));
        }
    }

    private static JSONObject patch(String chave, Integer empresaId, String body, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(chave, empresaId, getUsuarioAdmin()));

        try {
            String response = ExecuteRequestHttpService.patch(url, body, headers, "");
            return new JSONObject(response).getJSONObject(CONTENT_RESPONSE);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AcessoSistemaMSException(messageError(e.getMessage()));
        }
    }

    private static JSONObject delete(String chave, Integer empresaId, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(chave, empresaId, getUsuarioAdmin()));

        try {
            String response = ExecuteRequestHttpService.delete(url, headers);
            return new JSONObject(response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AcessoSistemaMSException(messageError(e.getMessage()));
        }
    }

    private static UsuarioVO getUsuarioAdmin() {
        UsuarioVO usuarioAdmin = new UsuarioVO();
        usuarioAdmin.setCodigo(1);
        usuarioAdmin.setUsername("admin");
        usuarioAdmin.setNome("ADMINISTRADOR");
        usuarioAdmin.setAdministrador(true);
        return usuarioAdmin;
    }
}

package servicos.impl.microsservice;

import controle.arquitetura.exceptions.SecretException;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.propriedades.PropsService;

public class SuperMSService {

    protected static final String CONTENT_RESPONSE = "content";
    protected static final String AUTHORIZATION_HEADER = "Authorization";
    protected static final String ZW_AUTHORIZATION_HEADER = "mD0dL5oG5xI6pR8b";
    private static final String MESSAGE = "message";
    private static final String MESSAGE_VALUE = "messageValue";
    private static final String META_KEY = "meta";

    protected static String messageError(String responseError) {
        return messageError(responseError, false);
    }

    protected static String messageError(String responseError, boolean onlyMessage) {
        JSONObject jsonResponse = new JSONObject(responseError);

        String message = null;
        if (jsonResponse.has(META_KEY) &&
                jsonResponse.getJSONObject(META_KEY).has(MESSAGE_VALUE) &&
                !jsonResponse.getJSONObject(META_KEY).optString(MESSAGE_VALUE).trim().isEmpty()) {
            message = jsonResponse.getJSONObject(META_KEY).optString(MESSAGE_VALUE);
        }

        if (jsonResponse.has(META_KEY) &&
                jsonResponse.getJSONObject(META_KEY).has(MESSAGE) &&
                !jsonResponse.getJSONObject(META_KEY).optString(MESSAGE).trim().isEmpty()) {
            message = jsonResponse.getJSONObject(META_KEY).optString(MESSAGE);
        }

        if (onlyMessage) {
            return message;
        }

        return "Não foi possível se conectar ao servidor neste momento. Tente novamente em alguns instantes. (" + message + ")";
    }

    protected static String getTokenZW(final String key) throws SecretException {
        return getTokenZW(key, null, null);
    }

    protected static String getTokenZW(final String key, Integer idEmpresa, UsuarioVO originUsuer) throws SecretException {
        UsuarioSimplesTO usuarioSimplesTO = new UsuarioSimplesTO();
        usuarioSimplesTO.setChave(key);
        if (!UteisValidacao.emptyNumber(idEmpresa)) {
            usuarioSimplesTO.setIdEmpresa(idEmpresa);
        }

        if (originUsuer != null) {
            usuarioSimplesTO.setAdministrador(originUsuer.getAdministrador());
            usuarioSimplesTO.setCodZw(originUsuer.getCodigo());
            usuarioSimplesTO.setUsername(originUsuer.getUsername());
        }

        return Uteis.encriptar(usuarioSimplesTO.toJSON().toString(), Uteis.getAuthZwSecret());
    }
}

package servicos.impl.microsservice.cadaux;

import negocio.comuns.arquitetura.UsuarioVO;
import org.json.JSONObject;
import servicos.impl.microsservice.SuperMSService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class CadAuxMsService extends SuperMSService {

    public static JSONObject replicar(JSONObject planoJSON, String urlCadAuxMs, String entidade, String chave, String empresa) throws Exception {
        String url = urlCadAuxMs + "/" + entidade + "/replicar";
        Map<String, String> headers = new HashMap<>();
        Integer idEmpresa = isNotBlank(empresa) ? Integer.parseInt(empresa) : null;
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(chave, idEmpresa, getUsuarioAdmin()));
        Logger logger = Logger.getLogger(CadAuxMsService.class.getName());

        try {
            String response = ExecuteRequestHttpService.post(url, planoJSON.toString(), headers);
            logger.log(Level.INFO, "RETORNO MÉTODO REPLICAR {0}: {1}", new Object[]{CadAuxMsService.class.getName(), response});
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            e.printStackTrace();
            throw new CadAuxMSException(messageError(e.getMessage()));
        }
    }

    public static JSONObject clonar(Integer codigo, String urlCadAuxMs, String entidade, String chave) throws Exception {
        String url = urlCadAuxMs + "/" + entidade + "/" + codigo + "/clonar";
        return get(chave, url);
    }

    private static JSONObject get(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put(ZW_AUTHORIZATION_HEADER, getTokenZW(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            e.printStackTrace();
            throw new CadAuxMSException(messageError(e.getMessage()));
        }
    }

    private static UsuarioVO getUsuarioAdmin() {
        UsuarioVO usuarioAdmin = new UsuarioVO();
        usuarioAdmin.setCodigo(1);
        usuarioAdmin.setUsername("admin");
        usuarioAdmin.setNome("ADMINISTRADOR");
        usuarioAdmin.setAdministrador(true);
        return usuarioAdmin;
    }
}

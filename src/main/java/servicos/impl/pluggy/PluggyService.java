package servicos.impl.pluggy;

import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.PluggyAccountDTO;
import negocio.comuns.financeiro.PluggyConnectorDTO;
import negocio.comuns.financeiro.PluggyItemVO;
import negocio.comuns.financeiro.PluggyTransactionDTO;
import negocio.comuns.financeiro.enumerador.TipoTransactionPluggyEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.PluggyAccountBloqueio;
import negocio.facade.jdbc.financeiro.PluggyItem;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;


import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Rodrigo Estulano on 12/07/2023.
 */

public class PluggyService extends SuperEntidade {

    private String URL_API_PLUGGY = "";
    private static final Integer TIMEOUT_REQUEST = 10000; //milisegundos

    public PluggyService() throws Exception {
        this.URL_API_PLUGGY = PropsService.getPropertyValue(PropsService.URL_API_PLUGGY);
    }

    public PluggyService(Connection con) throws Exception {
        super(con);
        this.URL_API_PLUGGY = PropsService.getPropertyValue(PropsService.URL_API_PLUGGY);
    }

    public String createAPIKey() throws Exception {
        try {
            String clientID = PropsService.getPropertyValue(PropsService.clientIdPluggy);
            String clientSecret = PropsService.getPropertyValue(PropsService.clientSecretPluggy);

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            JSONObject body = new JSONObject();
            body.put("clientId", clientID);
            body.put("clientSecret", clientSecret);

            String endpoint = URL_API_PLUGGY + "/auth";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, body.toString(), MetodoHttpEnum.POST);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                return "";
            }

            JSONObject json;
            try {
                json = new JSONObject(resposta.getResponse());
            } catch (Exception ex) {
                throw new Exception("Não foi possível obter gerar o token para consultar. tente novamente mais tarde!");
            }

            return json.optString("apiKey");
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String createConnectToken(String apiKey) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("X-API-KEY", apiKey);

            String endpoint = URL_API_PLUGGY + "/connect_token";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.POST);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                return "";
            }
            return resposta.getResponse();
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String prepareUpdateConnectToken(String apiKey, String itemId) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-KEY", apiKey);

        String endpoint = URL_API_PLUGGY + "/connect_token";
        RequestHttpService service = new RequestHttpService();
        service.connectTimeout = TIMEOUT_REQUEST;

        JSONObject jsonBody = new JSONObject();
        jsonBody.put("itemId", itemId);

        String response = ExecuteRequestHttpService.post(endpoint, jsonBody.toString(), headers);

        if (UteisValidacao.emptyString(response)) {
            return "";
        }
        return response;
    }

    public String incluirItem(Connection con, int empresa, String dadosRetorno) throws Exception {
        PluggyItem pluggyItemDAO = null;
        try {
            pluggyItemDAO = new PluggyItem(con);

            PluggyItemVO pluggyItemVO = new PluggyItemVO();

            JSONObject json = new JSONObject(dadosRetorno);
            pluggyItemVO.setId(json.getString("id"));
            pluggyItemVO.setEmpresa(empresa);
            pluggyItemVO.setDadosRetorno(dadosRetorno);
            pluggyItemVO.setAtivo(true);

            pluggyItemDAO.incluir(pluggyItemVO);

            return "sucesso";
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pluggyItemDAO = null;
        }
    }

    public String updateItem(Connection con, int empresa, String dadosRetorno) throws Exception {
        PluggyItem pluggyItemDAO = null;
        try {
            pluggyItemDAO = new PluggyItem(con);

            PluggyItemVO pluggyItemVO = new PluggyItemVO();

            JSONObject json = new JSONObject(dadosRetorno);
            pluggyItemVO.setId(json.getString("id"));
            pluggyItemVO.setEmpresa(empresa);
            pluggyItemVO.setDadosRetorno(dadosRetorno);

            pluggyItemDAO.update(pluggyItemVO);

            return "sucesso";
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pluggyItemDAO = null;
        }
    }

    public List<PluggyItemVO> consultarPluggyItem(Connection con, int empresa) throws Exception {

        PluggyItem pluggyItemDAO = null;
        try {
            pluggyItemDAO = new PluggyItem(con);
            List<PluggyItemVO> itens = pluggyItemDAO.consultar(empresa);

            return itens;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
        } finally {
            pluggyItemDAO = null;
        }
        return null;
    }

    public PluggyConnectorDTO obterInfoItem(String idItem, String apiKey, String dadosRetorno) throws Exception {
        Empresa empresaDAO;
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("X-API-KEY", apiKey);

            String endpoint = URL_API_PLUGGY + "/items/" + idItem;
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                return null;
            }

            JSONObject json = new JSONObject(resposta.getResponse());
            PluggyConnectorDTO connectorDTO = new PluggyConnectorDTO(json);

            //tratamento de data que vem sem timezone da pluggy
            empresaDAO = new Empresa(getCon());
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            connectorDTO.setCreatedAt(sdf.parse(json.optString("createdAt")));
            connectorDTO.setCreatedAtApresentar(Uteis.getDataComHHMM(connectorDTO.getCreatedAt()));

            connectorDTO.setUpdatedAt(sdf.parse(json.optString("updatedAt")));
            connectorDTO.setUpdatedAtApresentar(Uteis.getDataComHHMM(connectorDTO.getUpdatedAt()));

            if (connectorDTO.getName().contains("Caixa Economica Federal")) {
                boolean conectorCaixaEconomicaNaoAutorizado = false;
                List<PluggyAccountDTO> accounts = obterAccountsByIdItem(idItem);

                // se o item é da caixa economica e não encontrou nenhuma account no obterAccountsByIdItem, então quer dizer que não foi autorizado no momento de conexão
                // a caixa é o único que solicita essa liberação de dispositivo
                if (UteisValidacao.emptyList(accounts)){
                    conectorCaixaEconomicaNaoAutorizado = true;
                    connectorDTO.setStatusApresentar("PENDENTE DE AUTORIZAÇÃO NO APP/SITE DA CAIXA... Se você já autorizou, precisa aguardar no mín. 30 minutos" +
                            " para ver o status da conexão atualizar para 'Online'. Caso não tenha autorizado, entre no app ou site da caixa e faça a liberação do dispositivo \"" +
                            connectorDTO.getDeviceNickname() + "\"");

                    if (connectorDTO.getError() != null && !UteisValidacao.emptyString(connectorDTO.getError().getCode()) &&
                            (connectorDTO.getError().getCode().equals("USER_AUTHORIZATION_NOT_GRANTED") || connectorDTO.getError().getCode().equals("USER_AUTHORIZATION_PENDING")) &&
                            !UteisValidacao.emptyString(dadosRetorno) && dadosRetorno.contains("qrCodes")) {
                        JSONObject jsonDadosRetorno = new JSONObject(dadosRetorno);
                        JSONObject jsonError = jsonDadosRetorno.optJSONObject("error");
                        JSONObject jsonAttributes = jsonError.optJSONObject("attributes");
                        String stringQrCodes = jsonAttributes.optString("qrCodes");

                        String[] arrayQrCodes = stringQrCodes.split(",");

                        connectorDTO.getError().setQrCodeCaixa(arrayQrCodes);
                    }
                }
            }

            connectorDTO.setIdItem(idItem);
            return connectorDTO;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
        }
        return null;
    }

    public PluggyConnectorDTO obterInfoItem(String idItem) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("X-API-KEY", createAPIKey());

            String endpoint = URL_API_PLUGGY + "/items/" + idItem;
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                return null;
            }

            JSONObject json = new JSONObject(resposta.getResponse());
            PluggyConnectorDTO connectorDTO = new PluggyConnectorDTO(json);

            //tratamento de data que vem sem timezone da pluggy
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            connectorDTO.setCreatedAt(sdf.parse(json.optString("createdAt")));
            connectorDTO.setCreatedAtApresentar(Uteis.getDataComHHMM(connectorDTO.getCreatedAt()));

            connectorDTO.setPluggyItemVO(getFacade().getPluggyItem().consultarByIdItem(idItem));

            List<PluggyAccountDTO> accounts = new ArrayList<>();
            accounts = obterAccountsByIdItem(idItem);

            validarAccountsBloqueadas(accounts);

            connectorDTO.getPluggyItemVO().setPluggyAccountsDTO(accounts);

            connectorDTO.setIdItem(idItem);
            return connectorDTO;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public void validarAccountsBloqueadas(List<PluggyAccountDTO> accounts) throws Exception {
        for (PluggyAccountDTO accountDTO : accounts) {
            //validar se existe na tabela de bloqueio
            boolean isBloqueada = getFacade().getPluggyAccountBloqueio().consultarById(accountDTO.getId());
            accountDTO.setBloqueadaParaExibicao(isBloqueada);
        }
    }

    public void inativarItem(Connection con, String idItem) throws Exception {

        PluggyItem pluggyItemDAO = null;
        PluggyAccountBloqueio pluggyAccountBloqueioDAO = null;
        try {

            //primeiro tentar excluir o conector lá da pluggy, se não der certo não deve prosseguir
            excluirPluggyItem(idItem);

            pluggyItemDAO = new PluggyItem(con);
            pluggyItemDAO.inativar(idItem);

            pluggyAccountBloqueioDAO  = new PluggyAccountBloqueio(con);
            pluggyAccountBloqueioDAO.excluirByIdPluggyItem(idItem);

        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pluggyItemDAO = null;
            pluggyAccountBloqueioDAO = null;
        }
    }

    public void inativarItemSomenteNoBanco(Connection con, String idItem) throws Exception {

        PluggyItem pluggyItemDAO = null;
        PluggyAccountBloqueio pluggyAccountBloqueioDAO = null;
        try {
            pluggyItemDAO = new PluggyItem(con);
            pluggyItemDAO.inativar(idItem);

            pluggyAccountBloqueioDAO  = new PluggyAccountBloqueio(con);
            pluggyAccountBloqueioDAO.excluirByIdPluggyItem(idItem);

        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            pluggyItemDAO = null;
            pluggyAccountBloqueioDAO = null;
        }
    }

    public void excluirPluggyItem(String idItem) throws Exception {
        String apiKey = createAPIKey();
        Map<String, String> headers = new HashMap<>();
        headers.put("X-API-KEY", apiKey);

        Map<String, String> params = new HashMap<>();
        params.put("itemId", idItem);

        String endpoint = URL_API_PLUGGY + "/items/" + idItem;
        RequestHttpService service = new RequestHttpService();
        service.connectTimeout = TIMEOUT_REQUEST;
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, params, null, MetodoHttpEnum.DELETE);
        if (resposta.getHttpStatus() != 200) {
            throw new Exception("Não foi possível obter as contas associadas");
        }
    }

    public List<PluggyConnectorDTO> obterContasConectadas(int codEmpresa) throws Exception {
        try {
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new Exception("Código da empresa não informado.");
            }

            List<PluggyItemVO> lista = consultarPluggyItem(con, codEmpresa);
            List<PluggyConnectorDTO> listaConnector = new ArrayList<>();

            if (!UteisValidacao.emptyList(lista)) {
                for (PluggyItemVO item : lista) {
                    listaConnector.add(obterInfoItem(item.getId()));
                }
            }
            return listaConnector;
        } catch (Exception ex) {
            throw new Exception("Não foi possível obter o token: " + ex.getMessage());
        } finally {
            con = null;
        }
    }

    public List<PluggyAccountDTO> obterAccountsByIdItem(String idItem) throws Exception {
        try {
            String apiKey = createAPIKey();
            Map<String, String> headers = new HashMap<>();
            headers.put("X-API-KEY", apiKey);

            Map<String, String> params = new HashMap<>();
            params.put("itemId", idItem);

            String endpoint = URL_API_PLUGGY + "/accounts";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, params, null, MetodoHttpEnum.GET);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                throw new Exception("Não foi possível obter as contas associadas");
            }

            JSONObject json;
            try {
                json = new JSONObject(resposta.getResponse());
            } catch (Exception ex) {
                throw new Exception("Não foi possível obter as contas associadas");
            }

            List<PluggyAccountDTO> accounts = new ArrayList<>();

            if (!UteisValidacao.emptyNumber(json.optInt("total")) && json.optJSONArray("results").length() > 0) {
                JSONArray array = json.optJSONArray("results");
                for (int i = 0; i < array.length(); i++) {
                    accounts.add(new PluggyAccountDTO((JSONObject) array.get(i)));
                }
            }
            return accounts;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public List<PluggyTransactionDTO> obterTransactions(PluggyAccountDTO pluggyAccountDTO, TipoTransactionPluggyEnum tipo, Date inicio, Date fim) throws Exception {
        try {
            String apiKey = createAPIKey();
            Map<String, String> headers = new HashMap<>();
            headers.put("X-API-KEY", apiKey);
            headers.put("Content-Type", "application/json");

            Map<String, String> params = new HashMap<>();
            params.put("accountId", pluggyAccountDTO.getId());
            params.put("from", Uteis.getDataFormatoBD(inicio)); //yyyy-MM-dd
            params.put("to", Uteis.getDataFormatoBD(fim)); //yyyy-MM-dd
            params.put("pageSize", "500"); //500 é o máximo permitido na doc da pluggy

            String endpoint = URL_API_PLUGGY + "/transactions";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, params, null, MetodoHttpEnum.GET);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                throw new Exception("Não foi possível obter as transactions");
            }

            List<PluggyTransactionDTO> transactions = new ArrayList<>();
            JSONObject json = new JSONObject(resposta.getResponse());

            int paginasPercorrer = 1;
            if (!UteisValidacao.emptyNumber(json.optInt("totalPages"))) {
                paginasPercorrer = json.optInt("totalPages");
            }

            for (int paginaAtual = 1; paginaAtual <= paginasPercorrer; paginaAtual++) {
                //página 1 aproveitar da primeira consulta
                if (paginaAtual == 1) {
                    if (!UteisValidacao.emptyNumber(json.optInt("total")) && json.optJSONArray("results").length() > 0) {
                        JSONArray array = json.optJSONArray("results");
                        for (int i = 0; i < array.length(); i++) {
                            PluggyTransactionDTO item = new PluggyTransactionDTO();
                            item = new PluggyTransactionDTO((JSONObject) array.get(i));
                            //adicionar na lista somente do tipo informado
                            if (tipo.equals(TipoTransactionPluggyEnum.CREDIT) && item.getAmount() > 0) {
                                PluggyTransactionDTO transaction = new PluggyTransactionDTO((JSONObject) array.get(i));
                                transaction.setPluggyAccountDTO(pluggyAccountDTO);
                                transactions.add(transaction);
                            } else if (tipo.equals(TipoTransactionPluggyEnum.DEBIT) && item.getAmount() < 0) {
                                PluggyTransactionDTO transaction = new PluggyTransactionDTO((JSONObject) array.get(i));
                                transaction.setPluggyAccountDTO(pluggyAccountDTO);
                                transactions.add(transaction);
                            }
                        }
                    }
                } else {
                    transactions.addAll(obterTransactionsPagina(pluggyAccountDTO, tipo, inicio, fim, paginaAtual));
                }
            }
            return transactions;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public List<PluggyTransactionDTO> obterTransactionsPagina(PluggyAccountDTO pluggyAccountDTO, TipoTransactionPluggyEnum tipo, Date inicio, Date fim, int pagina) throws Exception {
        try {
            String apiKey = createAPIKey();
            Map<String, String> headers = new HashMap<>();
            headers.put("X-API-KEY", apiKey);

            Map<String, String> params = new HashMap<>();
            params.put("accountId", pluggyAccountDTO.getId());
            params.put("from", Uteis.getDataFormatoBD(inicio)); //yyyy-MM-dd
            params.put("to", Uteis.getDataFormatoBD(fim)); //yyyy-MM-dd
            params.put("page", String.valueOf(pagina));
            params.put("pageSize", "500");

            String endpoint = URL_API_PLUGGY + "/transactions";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, params, null, MetodoHttpEnum.GET);
            if (UteisValidacao.emptyString(resposta.getResponse())) {
                throw new Exception("Não foi possível obter as transactions");
            }

            List<PluggyTransactionDTO> transactions = new ArrayList<>();
            JSONObject json = new JSONObject(resposta.getResponse());

            if (!UteisValidacao.emptyNumber(json.optInt("total")) && json.optJSONArray("results").length() > 0) {
                JSONArray array = json.optJSONArray("results");
                for (int i = 0; i < array.length(); i++) {
                    PluggyTransactionDTO item = new PluggyTransactionDTO();
                    item = new PluggyTransactionDTO((JSONObject) array.get(i));
                    if (tipo.equals(item.getType())) {
                        PluggyTransactionDTO transaction = new PluggyTransactionDTO((JSONObject) array.get(i));
                        transaction.setPluggyAccountDTO(pluggyAccountDTO);
                        transactions.add(transaction);
                    }
                }
            }

            return transactions;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public boolean validarQtdLimiteContasConectadas(Connection con, int codEmpresa) throws  Exception {
        try {

            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            PluggyItem pluggyItemDAO = new PluggyItem(con);
            List<PluggyItemVO> lstContasConectadas = new ArrayList<>();
            lstContasConectadas = pluggyItemDAO.consultar(codEmpresa);

            if(empresaVO.getQtdLmtContasConcFacilitePay() == 0) {
                return false;
            }

            if(!UteisValidacao.emptyList(lstContasConectadas)) {
                int qtdContasConectadas = lstContasConectadas.size();

                if(qtdContasConectadas == empresaVO.getQtdLmtContasConcFacilitePay()) {
                    return true;
                }else {
                    return false;
                }
            }else {
                return false;
            }

        }catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

}

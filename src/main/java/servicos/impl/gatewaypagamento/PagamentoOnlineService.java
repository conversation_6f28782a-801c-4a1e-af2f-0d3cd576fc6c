package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovParcelaResultadoCobranca;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import servicos.SuperServico;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.PagamentoCartaoTO;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
import servicos.interfaces.AprovacaoServiceInterface;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class PagamentoOnlineService extends SuperServico {

    private MovPagamento movPagamentoDAO;
    private MovParcela movParcelaDAO;
    private Transacao transacaoDAO;
    private NotaFiscal notaFiscalDAO;
    private FormaPagamento formaPagamentoDAO;
    private Adquirente adquirenteDAO;
    private PagamentoCartaoTO pagamentoCartaoTO;
    private FormaPagamentoVO formaPagamentoVO;

    public PagamentoOnlineService(PagamentoCartaoTO pagamentoCartaoTO, Connection con) throws Exception {
        super(con);
        this.movPagamentoDAO = new MovPagamento(con);
        this.movParcelaDAO = new MovParcela(con);
        this.transacaoDAO = new Transacao(con);
        this.notaFiscalDAO = new NotaFiscal(con);
        this.formaPagamentoDAO = new FormaPagamento(con);
        this.adquirenteDAO = new Adquirente(con);
        this.pagamentoCartaoTO = pagamentoCartaoTO;
        this.formaPagamentoVO = this.formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente();
    }

    public TransacaoVO realizarCobranca() throws Exception {
        TransacaoVO transacaoVO = new TransacaoVO();
        CartaoCreditoTO cartaoTO;
        List<MovParcelaVO> parcelasMultaCriadas = new ArrayList<>();
        try {

            Uteis.logarDebug("Start Process | Pagador: " + this.pagamentoCartaoTO.getPessoaPagador().getCodigo() + " - " +
                    this.pagamentoCartaoTO.getPessoaPagador().getNome() + " | Parcelas: " + Uteis.retornarCodigos(this.pagamentoCartaoTO.getParcelas()) +
                    (this.pagamentoCartaoTO.getEmpresaVO() != null ? " | Cód. Empresa: " + this.pagamentoCartaoTO.getEmpresaVO().getCodigo() + " | Empresa: " + this.pagamentoCartaoTO.getEmpresaVO().getNome() : ""));

            //construir cartão
            cartaoTO = construirCartaoCreditoTO();
            cartaoTO.setOrigemCobranca(this.pagamentoCartaoTO.getOrigemCobranca());
            cartaoTO.setAsync(this.pagamentoCartaoTO.isAsync());

            Integer empresa = cartaoTO.getListaParcelas().get(0).getEmpresa().getCodigo();
            for (MovParcelaVO movParcelaVO : cartaoTO.getListaParcelas()) {
                if (!empresa.equals(movParcelaVO.getEmpresa().getCodigo())) {
                    throw new Exception("As parcelas devem ser da mesma empresa.");
                }

                //validar se a parcela não está pendente em uma transação
                validarParcelaEstaBloqueadaPorCobranca(movParcelaVO);
            }

            // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
            // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
            // Por isso a inclusão desses logs
            if (!UteisValidacao.emptyList(this.pagamentoCartaoTO.getParcelas())) {
                String codigosMovParcelas = this.pagamentoCartaoTO.getParcelas().stream()
                        .map(p -> String.valueOf(p.getCodigo()))
                        .collect(Collectors.joining(","));
                Uteis.logarDebug("PagamentoOnlineService - realizarCobranca - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
            }

            //criar parcela de multa e juros
            for (MovParcelaVO movParcelaVO : this.pagamentoCartaoTO.getParcelas()) {
                if (!UteisValidacao.emptyNumber(movParcelaVO.getValorMulta()) || !UteisValidacao.emptyNumber(movParcelaVO.getValorJuros())) {
                    MovParcelaVO parcelaMultaJuros = this.movParcelaDAO.criarParcelaMultaJuros(movParcelaVO, movParcelaVO.getValorMulta(), movParcelaVO.getValorJuros(), this.pagamentoCartaoTO.getUsuarioVO());
                    cartaoTO.getListaParcelas().add(parcelaMultaJuros);
                    parcelasMultaCriadas.add(parcelaMultaJuros);
                }
            }

            //compra parcelada pela operadora
            if (!UteisValidacao.emptyNumber(this.pagamentoCartaoTO.getNrParcelasOperadora())) {
                cartaoTO.setParcelas(this.pagamentoCartaoTO.getNrParcelasOperadora());

                if (this.pagamentoCartaoTO.getConvenioCobrancaVO() != null &&
                        this.pagamentoCartaoTO.getConvenioCobrancaVO().getTipo() != null &&
                        this.pagamentoCartaoTO.getConvenioCobrancaVO().getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                    if (this.pagamentoCartaoTO.getNrParcelasOperadora() > 1) {
                        if (UteisValidacao.emptyString(this.pagamentoCartaoTO.getTipoParcelamentoStone())) {
                            throw new Exception("Tipo Parcelamento Stone não informado.");
                        }
                        cartaoTO.setTipoParcelamentoStone(InstalmentTypeInstlmtTp.fromValue(this.pagamentoCartaoTO.getTipoParcelamentoStone()));
                    } else {
                        cartaoTO.setTipoParcelamentoStone(InstalmentTypeInstlmtTp.A_VISTA);
                    }
                }
            }

            //desconto só é utilizado no link de pagamento
            cartaoTO.setAplicarDesconto(false);

            //preencher o valor que será cobrando
            cartaoTO.preencherValor();

            new AragornService().povoarCartaoCreditoTO(cartaoTO);

            long inicio = System.currentTimeMillis();
            try {
                transacaoVO = getServiceTransacaoOnline().tentarAprovacao(cartaoTO);
            } finally {
                long fim = System.currentTimeMillis();
                Uteis.logarDebug("tentarAprovacao finalizado em " + (fim - inicio) + " milisegundos...");
            }

            try {
                //incrementa o número de tentativas da parcela no convenio
                if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                    //incrementa o número de tentativas da parcela no convenio
                    for (MovParcelaVO movParcelaVO : transacaoVO.getListaParcelas()) {
                        this.movParcelaDAO.incrementarNrTentativasParcelaConvenio(movParcelaVO, transacaoVO.getConvenioCobrancaVO());
                    }
                }
                if (transacaoVO != null && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                    List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
                    MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                    boolean temFormaPagamento = false;
                    List<FormaPagamentoVO> formasPgtConvenio = this.formaPagamentoDAO.consultarFormaPagamentoAtivoPorConvenioCobranca(transacaoVO.getConvenioCobrancaVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                    if(!UteisValidacao.emptyList(formasPgtConvenio)) {
                        if(formasPgtConvenio.size() == 1) {
                            for (FormaPagamentoVO form : formasPgtConvenio) {
                                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                                    movPagamentoVO.setFormaPagamento(form);
                                    temFormaPagamento = true;
                                    break;
                                }
                            }
                        }else{
                            boolean encontrouDefaultRecorrencia = false;
                            boolean cobrancaAutomatica = transacaoVO.getUsuarioResponsavel() != null && transacaoVO.getUsuarioResponsavel().getCodigo() == 3;
                            for (FormaPagamentoVO form : formasPgtConvenio) {
                                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                                    if(cobrancaAutomatica){
                                        if(form.getDefaultRecorrencia()) {
                                            movPagamentoVO.setFormaPagamento(form);
                                            temFormaPagamento = true;
                                            encontrouDefaultRecorrencia = true;
                                            break;
                                        }
                                    }else{
                                        movPagamentoVO.setFormaPagamento(form);
                                        temFormaPagamento = true;
                                        break;
                                    }
                                }
                            }

                            if(!encontrouDefaultRecorrencia && cobrancaAutomatica) {
                                movPagamentoVO.setFormaPagamento(formasPgtConvenio.get(0));
                                temFormaPagamento = true;
                            }
                        }
                    }

                    if (!temFormaPagamento) {
                        movPagamentoVO.setFormaPagamento(this.formaPagamentoVO);
                    }

                    try {
                        movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacaoVO));
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                    movPagamentoVO.setMovPagamentoEscolhida(true);
                    movPagamentoVO.setValor(cartaoTO.getValor());
                    movPagamentoVO.setValorTotal(cartaoTO.getValor());
                    movPagamentoVO.setPessoa(this.pagamentoCartaoTO.getPessoaPagador());
                    movPagamentoVO.setNomePagador(this.pagamentoCartaoTO.getPessoaPagador().getNome());
                    movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
                    if (!UteisValidacao.emptyNumber(cartaoTO.getParcelas())) {
                        movPagamentoVO.setNrParcelaCartaoCredito(cartaoTO.getParcelas());
                    } else {
                        movPagamentoVO.setNrParcelaCartaoCredito(1);
                    }
                    movPagamentoVO.setOperadoraCartaoVO(obterOperadoraCartao(transacaoVO, cartaoTO));
                    movPagamentoVO.setResponsavelPagamento(this.pagamentoCartaoTO.getUsuarioVO());
                    movPagamentoVO.setNsu(transacaoVO.getNSU());
                    movPagamentoVO.setEmpresa(this.pagamentoCartaoTO.getEmpresaVO());
                    movPagamentoVO.setConvenio(transacaoVO.getConvenioCobrancaVO());
                    prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, transacaoVO.getListaParcelas());
                    listaPagamento.add(movPagamentoVO);
                    ReciboPagamentoVO reciboObj = this.movPagamentoDAO.incluirListaPagamento(
                            listaPagamento,
                            cartaoTO.getListaParcelas(),
                            null,
                            obterContratoParcelas(),
                            false, 0.0);


                    Empresa empresaDAO = null;
                    try {
                        //o próprio metodo de enviar o nfse já valida se a empresa usa ou não o recurso
                        empresaDAO = new Empresa(this.getCon());
                        reciboObj.getPagamentosDesteRecibo().get(0).setProdutosPagos(this.movPagamentoDAO.obterProdutosPagosMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo()));
                        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(reciboObj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        if (empresaVO.isEnviarNFSeAutomatico() && empresaVO.getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()) {
                            String chave = DAO.resolveKeyFromConnection(this.getCon());
                            this.notaFiscalDAO.gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboObj, this.pagamentoCartaoTO.getUsuarioVO(), chave);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        Uteis.logarDebug("Nota Fiscal Eletronica ignorada para o Recibo: " + reciboObj.getCodigo() + ", devido ao erro: " + e.getMessage());
                    } finally {
                        empresaDAO = null;
                    }

                    transacaoVO.setReciboPagamento(reciboObj.getCodigo());
                    transacaoVO.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());

                    if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                        transacaoVO.setCodigoExterno(transacaoVO.getValorAtributoResposta(APF.Transacao));
                    }

                    transacaoDAO.alterar(transacaoVO);

                    try {
                        if (this.pagamentoCartaoTO.getEmpresaVO().isNotificarWebhook()) {
                            Cliente clienteDAO = new Cliente(this.getCon());
                            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboObj.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            clienteDAO = null;
                            ZillyonWebFacade zwFacade = new ZillyonWebFacade(this.getCon());
                            zwFacade.notificarPagamento(clienteVO, reciboObj);
                            zwFacade = null;
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    ZillyonWebFacade zwFacade;
                    try {
                        zwFacade = new ZillyonWebFacade(this.getCon());
                        if (zwFacade.getConfiguracaoSistema().realizarEnvioSesiSC()) {
                            List<Integer> listaReciboSesi = new ArrayList<>();
                            listaReciboSesi.add(reciboObj.getCodigo());
                            zwFacade.startThreadIntegracaoFiesc(reciboObj.getEmpresa().getCodigo(), listaReciboSesi, "incluirCartao");
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    } finally {
                        zwFacade = null;
                    }
                }
            } catch (Exception e) {
                transacaoDAO.alterarMessagemErro(transacaoVO, e.getMessage());
                throw e;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Error Process | Pagador: " + this.pagamentoCartaoTO.getPessoaPagador().getCodigo() + " - " +
                    this.pagamentoCartaoTO.getPessoaPagador().getNome() + " | Parcelas: " + Uteis.retornarCodigos(this.pagamentoCartaoTO.getParcelas()) + " | Erro: " + ex.getMessage());
            registrarErro(transacaoVO, ex);
            throw ex;
        } finally {
            excluirParcelasMultaJuros(transacaoVO, parcelasMultaCriadas);
            processarMovParcelaResultadoCobranca(transacaoVO);
            Uteis.logarDebug("Finish Process | Pagador: " + this.pagamentoCartaoTO.getPessoaPagador().getCodigo() + " - " +
                    this.pagamentoCartaoTO.getPessoaPagador().getNome() + " | Parcelas: " + Uteis.retornarCodigos(this.pagamentoCartaoTO.getParcelas()));
        }
        return transacaoVO;
    }

    private void excluirParcelasMultaJuros(TransacaoVO transacaoVO, List<MovParcelaVO> parcelasMultaCriadas) {
        for (MovParcelaVO movParcelaVO : parcelasMultaCriadas) {
            this.transacaoDAO.excluirParcelaMultaJurosTransacao(transacaoVO, movParcelaVO);
        }
    }

    private void validarParcelaEstaBloqueadaPorCobranca(MovParcelaVO movParcelaVO) throws Exception {
        /**
         * Parcela está bloqueada.. não realizar aa tentativa de cobrança
         * by Luiz Felipe 17/04/2020
         */
        boolean parcelaEstaBloqueadaPorCobranca = this.movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO);
        if (parcelaEstaBloqueadaPorCobranca) {
            String msg = "PARCELA ESTÁ BLOQUEADA PARA COBRANÇA - " + movParcelaVO.getCodigo();
            Uteis.logarDebug(msg);
            throw new Exception(msg);
        }
    }

    private OperadoraCartaoVO obterOperadoraCartao(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO) {
        OperadoraCartao operadoraDAO;
        try {
            operadoraDAO = new OperadoraCartao(getCon());

            OperadorasExternasAprovaFacilEnum operadoraEnum = cartaoCreditoTO.getBand();
            if (operadoraEnum == null) {
                operadoraEnum = transacaoVO.getBandeiraPagamento();
            }
            if (operadoraEnum == null) {
                throw new Exception("Bandeira não identificada.");
            }
            return operadoraDAO.consultarOuCriaPorCodigoIntegracao(this.pagamentoCartaoTO.getConvenioCobrancaVO().getTipo(), cartaoCreditoTO.getParcelas(), operadoraEnum);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            operadoraDAO = null;
        }
        return new OperadoraCartaoVO();
    }

    private CartaoCreditoTO construirCartaoCreditoTO() {
        CartaoCreditoTO cartao = new CartaoCreditoTO();
        cartao.setIdPessoaCartao(this.pagamentoCartaoTO.getPessoaPagador().getCodigo());
        cartao.setUsuarioResponsavel(this.pagamentoCartaoTO.getUsuarioVO());
        cartao.setEmpresa(this.pagamentoCartaoTO.getEmpresaVO().getCodigo());
        cartao.setSomenteUmEnvioCartaoTentativa(this.pagamentoCartaoTO.getEmpresaVO().isSomenteUmEnvioCartaoTentativa());
        cartao.setTransacaoPresencial(this.pagamentoCartaoTO.isTransacaoPresencial());
        cartao.setTipoTransacaoEnum(this.pagamentoCartaoTO.getConvenioCobrancaVO().getTipo().getTipoTransacao());
        cartao.getListaParcelas().addAll(this.pagamentoCartaoTO.getParcelas());
        cartao.setRetentativaManual(this.pagamentoCartaoTO.isRetentativaManual());
        cartao.setParcelas(1);
        cartao.setIpClientePacto(this.pagamentoCartaoTO.getIpCliente());

        AutorizacaoCobrancaVO autorizacaoVO = this.pagamentoCartaoTO.getAutorizacaoCobrancaVO();

        if (autorizacaoVO != null) {
            cartao.setTokenVindi(autorizacaoVO.getCodigoExterno());
            cartao.setTokenCielo(autorizacaoVO.getTokenCielo());
            cartao.setIdCardMundiPagg(autorizacaoVO.getIdCardMundiPagg());
            cartao.setIdCardPagarMe(autorizacaoVO.getIdCardPagarMe());

            //utilizando token da pago livre
            cartao.setTokenPagoLivre(autorizacaoVO.getTokenPagoLivre());
            if (!UteisValidacao.emptyString(autorizacaoVO.getTokenPagoLivre())) {
                cartao.setValidade(autorizacaoVO.getValidadeCartao());
                cartao.setNomeTitular(autorizacaoVO.getNomeTitularCartao());
            }
        }

        if (autorizacaoVO != null && !UteisValidacao.emptyString(autorizacaoVO.getNumeroCartao())) {
            cartao.setNumero(autorizacaoVO.getNumeroCartao());
            cartao.setValidade(autorizacaoVO.getValidadeCartao());
            cartao.setBand(autorizacaoVO.getOperadoraCartao());
            cartao.setNomeTitular(autorizacaoVO.getNomeTitularCartao());
            cartao.setCpfCnpjPortador(autorizacaoVO.getCpfTitular());
            cartao.setTokenAragorn(autorizacaoVO.getTokenAragorn());
        }
        return cartao;
    }

    private void registrarErro(TransacaoVO transacaoVO, Exception ex) {

    }

    private AprovacaoServiceInterface getServiceTransacaoOnline() throws Exception {
        return CobrancaOnlineService.getImplementacaoAprovacaoService(this.pagamentoCartaoTO.getConvenioCobrancaVO().getTipo().getTipoTransacao(),
                this.pagamentoCartaoTO.getEmpresaVO().getCodigo(), this.pagamentoCartaoTO.getConvenioCobrancaVO().getCodigo(),
                this.pagamentoCartaoTO.getConvenioCobrancaVO().isPactoPay(), this.getCon());
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> listaMovParcelas) {
        ParceiroFidelidadeZW parceiroFidelidadeZW = null;
        try {
            parceiroFidelidadeZW = new ParceiroFidelidadeZW(getCon());
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        } finally {
            parceiroFidelidadeZW = null;
        }
    }

    private void processarMovParcelaResultadoCobranca(TransacaoVO transacaoVO) {
        MovParcelaResultadoCobranca dao = null;
        try {
            if (transacaoVO == null || UteisValidacao.emptyList(transacaoVO.getListaParcelas())) {
                return;
            }

            dao = new MovParcelaResultadoCobranca(getCon());
            dao.processarParcelas(transacaoVO.getListaParcelas());
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "processarMovParcelaResultadoCobranca ERRO: " + ex.getMessage());
        } finally {
            dao = null;
        }
    }

    private ContratoVO obterContratoParcelas() {
        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = this.pagamentoCartaoTO.getParcelas().get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : this.pagamentoCartaoTO.getParcelas()) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                return null;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        return contratoVO;
    }
}

package servicos.impl.gatewaypagamento;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 *
 * https://docs.pagar.me/reference#criando-um-recebedor
 * https://documenter.getpostman.com/view/11754280/Tzm6nwxG#7857137a-ce2e-46bc-ac7f-c42d00ac15f2
 */
public class RecebedorDTO {

    private String id;
    private String name;
    private String status;
    //utilizado para ordenar
    private Double valor;
    private Double percentual;
    private Integer valorCentavos;
    private boolean recebedorPrincipal;

    public RecebedorDTO() {
    }

    public RecebedorDTO(JSONObject json, ConvenioCobrancaVO convenioCobrancaVO) {
        if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)){
            this.id = json.optString("id");
            this.status = json.optString("status");

            if (json.has("bank_account")) {
                JSONObject bank_account = json.getJSONObject("bank_account");
                String agencia = bank_account.optString("agencia");
                String agencia_dv = bank_account.optString("agencia_dv");
                String conta = bank_account.optString("conta");
                String conta_dv = bank_account.optString("conta_dv");

                this.name = bank_account.optString("legal_name") + " | Ag. " + agencia + "-" + agencia_dv + " | Cc. " + conta + "-" + conta_dv;
            }
        } else if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)){
            this.id = json.optString("id");
            this.status = json.optString("status");

            if (json.has("default_bank_account")) {
                JSONObject default_bank_account = json.getJSONObject("default_bank_account");
                String agencia = default_bank_account.optString("branch_number");
                String agencia_dv = default_bank_account.optString("branch_check_digit");
                String conta = default_bank_account.optString("account_number");
                String conta_dv = default_bank_account.optString("account_check_digit");

                this.name = json.optString("name") + " | " + default_bank_account.optString("holder_name") + " | Ag. " + agencia + "-" + agencia_dv + " | Cc. " + conta + "-" + conta_dv;
            }
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Double getPercentual() {
        if (percentual == null) {
            percentual = 0.0;
        }
        return percentual;
    }

    public void setPercentual(Double percentual) {
        this.percentual = percentual;
    }

    public Integer getValorCentavos() {
        if (valorCentavos == null) {
            valorCentavos = 0;
        }
        return valorCentavos;
    }

    public void setValorCentavos(Integer valorCentavos) {
        this.valorCentavos = valorCentavos;
    }

    public Double getValorCentavosDouble() {
        return Double.valueOf(getValorCentavos()) / 100;
    }

    public boolean isRecebedorPrincipal() {
        return recebedorPrincipal;
    }

    public void setRecebedorPrincipal(boolean recebedorPrincipal) {
        this.recebedorPrincipal = recebedorPrincipal;
    }
}

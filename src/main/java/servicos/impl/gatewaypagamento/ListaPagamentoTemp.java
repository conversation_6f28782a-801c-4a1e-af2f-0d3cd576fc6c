package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.LeitorExcel;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.hssf.usermodel.HSSFRow;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.Usuario.montarDados;

public class ListaPagamentoTemp {

    /*
    1 - As colunas da planilha devem estar dentro do seguinte padro:
        Aluno	Vencimento	Pago	Pagamento	Bandeira	OPERAÇÃO
    2 - Converter planilha xlsx para xls
    3 - Nome do arquivo deve estar pequeno
    4 - Colunas Vencimento e Pago devem estar no formato: 31/12/1999
    5 - Coluna Operacao deve estar no formato: -1235
    ** - Tabela movParcela: select count(m.situacao), m.situacao from movparcela m where codigo in (346738) group by m.situacao
     */
    private static String PLANILHA_EXCEL;

    public static void main(String[] args) throws Exception {
        String chave = "endfitmg";
        PLANILHA_EXCEL = "C:/Temp/planilhas_remessa/4/arquivo.xls";

        Connection con = null;
        // con = DriverManager.getConnection("**************************************************", "postgres", "pactodb");
        // con = DriverManager.getConnection("**************************************************", "postgres", "pactodb");

        if (args.length == 0) {
            args = new String[]{chave};
        }
        if (args.length >= 2) {
            chave = args[0];
            PLANILHA_EXCEL = args[1];
        }

        if (con == null) {
            con = new DAO().obterConexaoEspecifica(chave);
        }
        Conexao.guardarConexaoForJ2SE(chave, con);

        ProcessarPagamento(con);
    }

    private static String ParcelaProcessamentoSituacao(Connection con, int codigo) throws Exception {
        String sql = "SELECT * FROM MovParcela mpa WHERE mpa.codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new Exception("Dados Não Encontrados ( MovParcela: " + codigo + " ).");
        }

        return tabelaResultado.getString("situacao").toUpperCase();
    }

    private static void ProcessarPagamento(Connection con) throws Exception {
        String aluno;
        String parcela;

        List<ListaPagamento> listaExcel = listaExcel();
        for (ListaPagamento listaPagamentoExcel : listaExcel) {
            aluno = listaPagamentoExcel.getAluno();
            parcela = listaPagamentoExcel.getCodigoParcela();
            System.out.println("Aluno: " + aluno + "\nParcela: " + parcela);

            //            int difDias = Calendario.diferencaEmDias(listaPagamentoExcel.getPago(), Calendario.hoje());
            //            Calendario.dia = Uteis.somarDias(Calendario.hoje(), -difDias);
            Calendario.dia = listaPagamentoExcel.getPago();
            try {
                String situacaoParcela = ParcelaProcessamentoSituacao(con, Integer.valueOf(parcela));
                if (situacaoParcela.equals("PG")) {
                    System.out.println("Parcela não está em aberto.");
                    continue;
                }

                MovParcela movParcelaDAO = new MovParcela(con);
                MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(Integer.valueOf(listaPagamentoExcel.getCodigoParcela()), Uteis.NIVELMONTARDADOS_TODOS);

                if (!movParcelaVO.getSituacao().equals("EA")) {
                    throw new Exception("Parcela não está em aberto.\nAluno: " + aluno + "\nParcela: " + parcela);
                }

                List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
                MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                FormaPagamento fm = new FormaPagamento(con);

                FormaPagamentoVO formasPgtConvenio = null;
                if (listaPagamentoExcel.getPagamento().toUpperCase().contains("DEBITO")) {
                    formasPgtConvenio = fm.consultarPorChavePrimaria(5, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if (listaPagamentoExcel.getPagamento().toUpperCase().contains("CHEQUE")) {
                    formasPgtConvenio = fm.consultarPorChavePrimaria(6, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else if (listaPagamentoExcel.getPagamento().toUpperCase().contains("CREDITO")) {
                    formasPgtConvenio = fm.consultarPorChavePrimaria(4, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                } else {
                    throw new Exception("ERRO AO BUSCAR A FORMA DE PAGAMENTO: " + listaPagamentoExcel.getPagamento() + "\nAluno: " + aluno + "\nParcela: " + parcela);
                }

                movPagamentoVO.setFormaPagamento(formasPgtConvenio);
                movPagamentoVO.setMovPagamentoEscolhida(true);
                movPagamentoVO.setValor(movParcelaVO.getValorParcela());
                movPagamentoVO.setValorTotal(movParcelaVO.getValorParcela());
                movPagamentoVO.setPessoa(movParcelaVO.getPessoa());
                movPagamentoVO.setNomePagador(movParcelaVO.getPessoa().getNome());

                OperadoraCartao operadoraCartao = new OperadoraCartao(con);
                List<OperadoraCartaoVO> listaOperaCredi = operadoraCartao.consultarTodas(true, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                List<OperadoraCartaoVO> listaOperaDebito = operadoraCartao.consultarTodas(true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//        //percorrer lista e identificar a bandeira...
//        //para pagamento de créidto "listaOperaCredi" e débito "listaOperaDebito"

                OperadoraCartaoVO operadoraCartaoVO = null;
                if (listaPagamentoExcel.getPagamento().toUpperCase().contains("CREDITO")) {
                    for (OperadoraCartaoVO ope : listaOperaCredi) {
                        if (ope.getDescricao().toUpperCase().contains(listaPagamentoExcel.getBandeira().toUpperCase())) {
                            operadoraCartaoVO = ope;
                            break;
                        }
                    }

                    movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);

                    int nrParcelas = 1;
                    if (!UteisValidacao.emptyNumber(nrParcelas)) {
                        movPagamentoVO.setNrParcelaCartaoCredito(nrParcelas);
                    } else {
                        movPagamentoVO.setNrParcelaCartaoCredito(1);
                    }

                    movPagamentoVO.setOperadoraCartaoVO(operadoraCartaoVO);

                } else if (listaPagamentoExcel.getPagamento().toUpperCase().contains("DEBITO")) {

                    for (OperadoraCartaoVO ope : listaOperaDebito) {
                        if (ope.getDescricao().toUpperCase().contains(listaPagamentoExcel.getBandeira().toUpperCase())) {
                            operadoraCartaoVO = ope;
                            break;
                        }
                    }
                    movPagamentoVO.setOperadoraCartaoVO(operadoraCartaoVO);
                }


                movPagamentoVO.setNsu("");
                movPagamentoVO.setAutorizacaoCartao("");

                //ver com o Lucas o usuario
                UsuarioVO responsavelPagamento = consultarPorCodigo(con, 2, Uteis.NIVELMONTARDADOS_TODOS);
                movPagamentoVO.setResponsavelPagamento(responsavelPagamento);

                movPagamentoVO.setEmpresa(movParcelaVO.getEmpresa());
                listaPagamento.add(movPagamentoVO);

                List<MovParcelaVO> parcelas = new ArrayList<>();
                parcelas.add(movParcelaVO);

                MovPagamento movPagamentoDAO = new MovPagamento(con);

                ReciboPagamentoVO reciboObj = movPagamentoDAO.incluirListaPagamento(
                        listaPagamento,
                        parcelas,
                        null,
                        null,
                        false, 0.0);
            } catch (Exception ex) {
                System.out.println(ex.getMessage() + "\nAluno: " + aluno + "\nParcela: " + parcela);
                ex.printStackTrace();
            }
        }

    }

    private static UsuarioVO consultarPorCodigo(Connection con, Integer codigoPrm, int nivelMontarDados) throws Exception {
        UsuarioVO usuario = null;
        String sql = "SELECT * FROM Usuario WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet resultDados = sqlConsultar.executeQuery()) {
                if (resultDados.next()) {
                    usuario = montarDados(resultDados, nivelMontarDados, con);
                }
            }
        }

        return usuario;
    }

    private static List<ListaPagamento> listaExcel() throws Exception {
        List<HSSFRow> xssfRows = LeitorExcel.lerLinhas(PLANILHA_EXCEL);
        List<ListaPagamento> listaPagamentos = new ArrayList<>();

        int iLinha = 0;
        for (HSSFRow linha : xssfRows) {
            ListaPagamento listaPagamento = new ListaPagamento();
            listaPagamento.setAluno(String.valueOf(LeitorExcel.obterString(linha, 0)).trim());
            listaPagamento.setVencimento(LeitorExcel.obterDataNoFormatoData(linha, 1));
            listaPagamento.setPago(LeitorExcel.obterDataNoFormatoData(linha, 2));
            listaPagamento.setPagamento(String.valueOf(LeitorExcel.obterString(linha, 3)).trim());
            listaPagamento.setBandeira(String.valueOf(LeitorExcel.obterString(linha, 4)).trim());
//            listaPagamento.setVlrBruto(String.valueOf(LeitorExcel.obterString(linha, 5)).trim());
//            listaPagamento.setVlrLiquido(String.valueOf(LeitorExcel.obterString(linha, 6)).trim());
            listaPagamento.setCodigoParcela(LeitorExcel.obterStringDoNumero(linha, 5).trim());

            listaPagamentos.add(listaPagamento);
            iLinha++;
        }

        return listaPagamentos;
    }
}

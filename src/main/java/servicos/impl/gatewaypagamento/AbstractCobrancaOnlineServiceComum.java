package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.ResponsavelPagamentoTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Telefone;
import negocio.facade.jdbc.financeiro.Transacao;

import java.sql.Connection;

/**
 * <AUTHOR> Cattany
 * @since 26/02/2019
 */
public abstract class AbstractCobrancaOnlineServiceComum extends CobrancaOnlineService {

    public AbstractCobrancaOnlineServiceComum(Connection con) throws Exception {
        super(con);
    }

    public static TransacaoVO criarTransacao(CartaoCreditoTO dadosCartao,
                                             ConvenioCobrancaVO convenioCobrancaVO) {
        TipoTransacaoEnum tipoTransacaoEnum = convenioCobrancaVO.getTipo().getTipoTransacao();
        TransacaoVO transacaoVO = tipoTransacaoEnum.getTransacaoVO();
        return criarTransacao(dadosCartao, transacaoVO, tipoTransacaoEnum, convenioCobrancaVO);
    }

    protected static TransacaoVO criarTransacao(CartaoCreditoTO dadosCartao,
                                                TransacaoVO transacao,
                                                TipoTransacaoEnum tipoTransacaoEnum,
                                                ConvenioCobrancaVO convenioCobrancaVO) {
        transacao.setTipo(tipoTransacaoEnum);
        transacao.setTipoOrigem(convenioCobrancaVO.getTipo().getTipoTransacao());
        transacao.setOrigem(dadosCartao.getOrigemCobranca());
        transacao.setConvenioCobrancaVO(convenioCobrancaVO);
        transacao.setAmbiente(convenioCobrancaVO.getAmbiente());

        transacao.setUrlTransiente(dadosCartao.getUrl());
        transacao.setIpTransiente(dadosCartao.getIpClientePacto());
        transacao.setUsuarioResponsavel(dadosCartao.getUsuarioResponsavel());
        transacao.setNomePessoa(dadosCartao.getNomeTitular());
        transacao.setDataProcessamento(Calendario.hoje());
        transacao.setEmpresa(dadosCartao.getEmpresa());
        transacao.getEmpresaVO().setCodigo(dadosCartao.getEmpresa());
        transacao.setListaParcelas(dadosCartao.getListaParcelas());
        transacao.setValor(Uteis.arredondarForcando2CasasDecimais(dadosCartao.getValor()));
        transacao.setCartaoCreditoTO(dadosCartao);
        transacao.setTransacaoVerificarCartao(dadosCartao.isTransacaoVerificarCartao());

        //SEMPRE SALVAR O TOKEN DO CARTAO UTILIZADO!! by Luiz Felipe
        transacao.setTokenAragorn(dadosCartao.getTokenAragorn());

        if (dadosCartao.getIdPessoaCartao() != null && !UteisValidacao.emptyNumber(dadosCartao.getIdPessoaCartao())) {
            PessoaVO pessoa = new PessoaVO();
            pessoa.setCodigo(dadosCartao.getIdPessoaCartao());
            transacao.setPessoaPagador(pessoa);
        }

        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.convenio_nome, convenioCobrancaVO.getDescricao());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao01, convenioCobrancaVO.getCodigoAutenticacao01());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao02, convenioCobrancaVO.getCodigoAutenticacao02());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao03, convenioCobrancaVO.getCodigoAutenticacao03());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao04, convenioCobrancaVO.getCodigoAutenticacao04());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao05, convenioCobrancaVO.getCodigoAutenticacao05());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao06, convenioCobrancaVO.getCodigoAutenticacao06());
        transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao07, convenioCobrancaVO.getCodigoAutenticacao07());
        if (dadosCartao.getBand() != null) {
            transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira, dadosCartao.getBand().getDescricao());
        }
        if (dadosCartao.getParcelas() != null) {
            transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, dadosCartao.getParcelas().toString());
        }
        try {
            transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.ipCliente, dadosCartao.getIpClientePacto());
        } catch (Exception ignored) {
        }
        try {
            transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.presencial, String.valueOf(dadosCartao.isTransacaoPresencial()));
        } catch (Exception ignored) {
        }
        return transacao;
    }

    public String obterIdentificadorPacto(TransacaoVO obj) {
        return obterIdentificadorPacto(obj, null);
    }

    public String obterIdentificadorPacto(TransacaoVO obj, String siglaPagarMe) {
        String identificador = ("TRA" + obj.getCodigo());

        if (!UteisValidacao.emptyString(siglaPagarMe)) {
            //utilizado na pagarme
            identificador = ("TRA" + siglaPagarMe + obj.getCodigo());
        }

        if (!UteisValidacao.emptyString(identificador)) {
            obj.adicionarOutrasInformacoes(AtributoTransacaoEnum.identificadorPacto, identificador);
        }
        return identificador;
    }

    public void preencherOutrasInformacoes(TransacaoVO obj) {
        //prencher aqui pois tem algumas informações que pega na resposta da adquirente
        if (!UteisValidacao.emptyString(obj.getBandeira())) {
            obj.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira, obj.getBandeira());
        }
        if (!UteisValidacao.emptyNumber(obj.getNrVezesCobranca())) {
            obj.adicionarOutrasInformacoes(AtributoTransacaoEnum.numeroParcelas, obj.getNrVezesCobranca().toString());
        }
        if (!UteisValidacao.emptyString(obj.getIdentificadorPacto())) {
            obj.adicionarOutrasInformacoes(AtributoTransacaoEnum.identificadorPacto, obj.getIdentificadorPacto());
        }
        try {
            if (!UteisValidacao.emptyString(obj.getCartaoMascarado())) {
                obj.adicionarOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado, obj.getCartaoMascarado());
            }
        } catch (Exception ex) {}
    }

    public ResponsavelPagamentoTO obterPessoaResponsavelPagamento(PessoaVO pessoaVO, ClienteVO clienteVO) throws Exception {
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(this.getCon());

            ResponsavelPagamentoTO responsavelPagamentoTO = new ResponsavelPagamentoTO(pessoaVO);
            PessoaCPFTO pessoaCPFTO = new PessoaCPFTO();
            try {
                pessoaCPFTO = transacaoDAO.obterDadosPessoaPagador(pessoaVO, false, true);
            } catch (Exception ignore) {
            }

            if (clienteVO.isUtilizarResponsavelPagamento()) {

                if (!SuperVO.verificaCPF(pessoaCPFTO.getCpfResponsavel())) {
                    throw new ConsistirException("O responsável pelo aluno não possui um CPF válido!");
                } else {
                    responsavelPagamentoTO.setPessoa(UteisValidacao.emptyNumber(pessoaCPFTO.getPessoaResponsavel()) ? pessoaVO.getCodigo() : pessoaCPFTO.getPessoaResponsavel());
                    responsavelPagamentoTO.setCpf(pessoaCPFTO.getCpfResponsavel());
                    responsavelPagamentoTO.setNome(UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel()) ? pessoaVO.getNome() : pessoaCPFTO.getNomeResponsavel());
                    responsavelPagamentoTO.setUsandoDoResponsavel(true);
                }

            } else if (pessoaVO.getCategoriaPessoa().equals(TipoPessoa.FISICA)) {

                if (SuperVO.verificaCPF(pessoaVO.getCfp())) {
                    responsavelPagamentoTO.setCpf(pessoaVO.getCfp());
                } else if (!clienteVO.isUtilizarResponsavelPagamento() && pessoaCPFTO != null &&
                        !UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel())) {
                    throw new ConsistirException("O aluno não possui CPF válido, mas possui um responsável com CPF informado, " +
                            "porém para utilizá-lo é necessário marcar a configuração " +
                            "\"Utilizar responsável do cliente para pagamentos\" nos dados pessoais do aluno.");
                } else {
                    throw new ConsistirException("O aluno não possui CPF cadastrado!");
                }

            } else if (pessoaVO.getCategoriaPessoa().equals(TipoPessoa.JURIDICA)) {

                if (SuperVO.verificaCPF(pessoaVO.getCpfResponsavelEmpresa())) {
                    responsavelPagamentoTO.setCpf(pessoaVO.getCpfResponsavelEmpresa());
                } else if (SuperVO.verificaCPF(pessoaVO.getCfp())) {
                    responsavelPagamentoTO.setCpf(pessoaVO.getCfp());
                } else if (!UteisValidacao.emptyString(pessoaVO.getCnpj())) {
                    responsavelPagamentoTO.setCpf(pessoaVO.getCnpj());
                } else {
                    throw new ConsistirException("O responsável pela empresa não possui CPF cadastrado!");
                }
            }

            //buscar os emails do responsável caso não tenha o da pessoa
            if (responsavelPagamentoTO.getEmails().isEmpty() &&
                    !UteisValidacao.emptyNumber(responsavelPagamentoTO.getPessoa())) {
                Email emailDAO;
                try {
                    emailDAO = new Email(this.getCon());
                    responsavelPagamentoTO.setEmails(emailDAO.consultarEmails(responsavelPagamentoTO.getPessoa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    emailDAO = null;
                }
            }
            //buscar os telefones do responsável caso não tenha o da pessoa
            if (responsavelPagamentoTO.getTelefones().isEmpty() &&
                    !UteisValidacao.emptyNumber(responsavelPagamentoTO.getPessoa())) {
                Telefone telefoneDAO;
                try {
                    telefoneDAO = new Telefone(this.getCon());
                    responsavelPagamentoTO.setTelefones(telefoneDAO.consultarTelefones(responsavelPagamentoTO.getPessoa(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    telefoneDAO = null;
                }
            }
            return responsavelPagamentoTO;
        } finally {
            transacaoDAO = null;
        }
    }
}

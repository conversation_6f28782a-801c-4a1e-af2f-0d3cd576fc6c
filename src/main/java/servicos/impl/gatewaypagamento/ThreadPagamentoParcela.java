package servicos.impl.gatewaypagamento;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovParcelaResultadoCobranca;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativa;
import negocio.facade.jdbc.vendas.VendasOnlineConvenioTentativaVO;
import servicos.impl.apf.APF;
import servicos.impl.apf.RecorrenciaService;
import servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp;
import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
import servicos.interfaces.AprovacaoServiceInterface;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by johnys on 24/02/2017.
 */
public abstract class ThreadPagamentoParcela {

    protected MovParcelaVO movParcela;
    protected AprovacaoServiceInterface aprovacaoService;
    protected RecorrenciaService recorrenciaService;
    protected ConvenioCobrancaVO convenioCobrancaVO;

    public ThreadPagamentoParcela(MovParcelaVO movParcela, AprovacaoServiceInterface aprovacaoService, RecorrenciaService recorrenciaService, ConvenioCobrancaVO convenioCobrancaVO){
        this.movParcela = movParcela;
        this.aprovacaoService = aprovacaoService;
        this.recorrenciaService = recorrenciaService;
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public abstract void validarDados() throws Exception;

    protected abstract void executarAcoesPosProcessamento(TransacaoVO transacao) throws Exception;

    protected abstract Boolean podeExecutarPagamento();

    protected abstract CartaoCreditoTO construirCartaoCreditoTO() throws Exception;

    protected abstract OperadoraCartaoVO getOperadoraCartao()throws Exception;

    protected abstract void registrarErro(TransacaoVO transacao);

    protected abstract void registrarErro(Exception ex);

    public TransacaoVO execute(Integer nrParcelas, List<String> msgErro) {
        TransacaoVO transacao = new TransacaoVO();
        MovParcelaVO parcelaMultaJuros = new MovParcelaVO();
        if (podeExecutarPagamento()) {
            try {
                recorrenciaService.logar("Start Process -> MovParcela_" + movParcela.getCodigo());

                /**
                 * validar se não existe transação com essa parcela com situação PENDENTE ou Aprovada..
                 * Evitar erros de pagamento duplicado.
                 * by Luiz Felipe 20/04/2020
                 */
                recorrenciaService.getMovParcelaDAO().validarMovParcelaComTransacaoConcluidaOuPendente(movParcela.getCodigo());


                CartaoCreditoTO cartao = construirCartaoCreditoTO();
                if (!UteisValidacao.emptyNumber(movParcela.getValorMulta()) || !UteisValidacao.emptyNumber(movParcela.getValorJuros())) {
                    parcelaMultaJuros = recorrenciaService.getMovParcelaDAO().criarParcelaMultaJuros(movParcela, movParcela.getValorMulta(), movParcela.getValorJuros(), recorrenciaService.getUsuario());
                    cartao.getListaParcelas().add(parcelaMultaJuros);
                }
                if (!UteisValidacao.emptyNumber(nrParcelas)) {
                    cartao.setParcelas(nrParcelas);
                    if (nrParcelas > 1 && convenioCobrancaVO != null && convenioCobrancaVO.getTipo() != null && convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                        VendasOnlineConvenioTentativa vendasConfigDAO = new VendasOnlineConvenioTentativa(recorrenciaService.getCon());

                        String tipoParcelamentoStoneVendasOnline = "";
                        List<VendasOnlineConvenioTentativaVO> lista = vendasConfigDAO.consultarPorEmpresa(movParcela.getCodigoEmpresa(), true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        for (VendasOnlineConvenioTentativaVO tentativaVO : lista) {
                            if (tentativaVO.getConvenioCobrancaVO().getCodigo().equals(this.convenioCobrancaVO.getCodigo())) {
                                tipoParcelamentoStoneVendasOnline = tentativaVO.getTipoParcelamentoStone();
                            }
                        }
                        if(UteisValidacao.emptyString(tipoParcelamentoStoneVendasOnline)){
                            tipoParcelamentoStoneVendasOnline = convenioCobrancaVO.getTipoParcelamentoStone();
                        }
                        if (!UteisValidacao.emptyString(tipoParcelamentoStoneVendasOnline)) {
                            cartao.setTipoParcelamentoStone(InstalmentTypeInstlmtTp.fromValue(tipoParcelamentoStoneVendasOnline));
                        }
                        vendasConfigDAO = null;
                    }
                }

                //desconto só é utilizado no link de pagamento
                cartao.setAplicarDesconto(false);

                //preencher o valor que será cobrando
                cartao.preencherValor();

                new AragornService().povoarCartaoCreditoTO(cartao);
                transacao = aprovacaoService.tentarAprovacao(cartao);
                try {
                    recorrenciaService.atualizarRemessa(transacao);
                    //incrementa o número de tentativas da parcela no convenio
                    if (transacao != null && !UteisValidacao.emptyNumber(transacao.getCodigo())) {
                        recorrenciaService.getMovParcelaDAO().incrementarNrTentativasParcelaConvenio(movParcela, transacao.getConvenioCobrancaVO());
                    }
                    if (transacao != null && transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
                        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                        boolean temFormaPagamento = false;
                        FormaPagamento fm = new FormaPagamento(recorrenciaService.getCon());
                        List<FormaPagamentoVO> formasPgtConvenio = fm.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                        fm = null;
                        for (FormaPagamentoVO form : formasPgtConvenio) {
                            if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(transacao.getConvenioCobrancaVO().getCodigo())) {
                                movPagamentoVO.setFormaPagamento(form);
                                temFormaPagamento = true;
                                break;
                            }
                        }
                        if(!temFormaPagamento){
                            movPagamentoVO.setFormaPagamento(recorrenciaService.getFormaPagamento());
                        }

                        Adquirente adquirenteDAO = null;
                        try {
                            adquirenteDAO = new Adquirente(recorrenciaService.getCon());
                            movPagamentoVO.setAdquirenteVO(adquirenteDAO.obterAdquirenteTransacao(transacao));
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        } finally {
                            adquirenteDAO = null;
                        }

                        movPagamentoVO.setMovPagamentoEscolhida(true);
                        movPagamentoVO.setValor(cartao.getValor());
                        movPagamentoVO.setValorTotal(cartao.getValor());
                        movPagamentoVO.setPessoa(movParcela.getPessoa());
                        movPagamentoVO.setNomePagador(movParcela.getPessoa().getNome());
                        movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
                        if (!UteisValidacao.emptyNumber(nrParcelas)) {
                            movPagamentoVO.setNrParcelaCartaoCredito(nrParcelas);
                        } else {
                            movPagamentoVO.setNrParcelaCartaoCredito(1);
                        }
                        movPagamentoVO.setOperadoraCartaoVO(getOperadoraCartao());
                        movPagamentoVO.setResponsavelPagamento(recorrenciaService.getUsuario());
                        movPagamentoVO.setNsu(transacao.getNSU());
                        movPagamentoVO.setEmpresa(movParcela.getEmpresa());
                        movPagamentoVO.setConvenio(transacao.getConvenioCobrancaVO());
                        prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcela);
                        listaPagamento.add(movPagamentoVO);
                        ReciboPagamentoVO reciboObj = recorrenciaService.getMovPagamentoDAO().incluirListaPagamento(
                                listaPagamento,
                                cartao.getListaParcelas(),
                                null,
                                movParcela.getContrato(),
                                false, 0.0);

                        if (recorrenciaService.getConfigSistema().isUsaEcf()) {
                            recorrenciaService.obterServicoCupom().setUsuarioLiberacao(
                                    recorrenciaService.getUsuario());
                            recorrenciaService.obterServicoCupom().incluirCupom(reciboObj, true);
                        }
                        //o próprio método de enviar o nfse já valida se a empresa usa ou não o recurso
                        try {

                            reciboObj.getPagamentosDesteRecibo().get(0).setProdutosPagos(recorrenciaService.getMovPagamentoDAO().obterProdutosPagosMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo()));
                            Empresa empresa = new Empresa(recorrenciaService.getCon());
                            EmpresaVO empresaVO = empresa.consultarPorChavePrimaria(reciboObj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                            if (empresaVO.isEnviarNFSeAutomatico() && empresaVO.getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()) {
                                String chave = DAO.resolveKeyFromConnection(recorrenciaService.getCon());
                                recorrenciaService.obterServicoNotaFiscal().gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboObj, recorrenciaService.getUsuario(), chave);
                            }
                            empresa = null;
                        } catch (Exception e) {
                            Uteis.logar(null, String.format("Nota Fiscal Eletronica ignorada para o Recibo: %s, "
                                    + "devido ao erro: %s ", new Object[]{reciboObj.getCodigo(),
                                e.getMessage()}));
                        }

                        transacao.setReciboPagamento(reciboObj.getCodigo());
                        transacao.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());


                        if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                            transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
                        }

                        aprovacaoService.getTransacaoFacade().alterar(transacao);
                        executarAcoesPosProcessamento(transacao);
                        aprovacaoService.getTransacaoFacade().alterar(transacao);

                    } else {
                        registrarErro(transacao);
                    }
                } catch (Exception e) {
                    aprovacaoService.getTransacaoFacade().alterarMessagemErro(transacao, e.getMessage());
                    throw e;
                }
            } catch (Exception ex) {
                msgErro.add(convenioCobrancaVO.getTipo().getDescricao() + " - parcela " + movParcela.getDescricao() + " cliente: " + movParcela.getPessoa().getNome() + " Erro processar parcela: \"" + movParcela.getCodigo() + "\": " + ex.getMessage());
                registrarErro(ex);

            } finally {
                aprovacaoService.getTransacaoFacade().excluirParcelaMultaJurosTransacao(transacao, parcelaMultaJuros);
                recorrenciaService.notificarThreadProcessada();
                processarMovParcelaResultadoCobranca(transacao);
                recorrenciaService.logar("End Process -> MovParcela_" + movParcela.getCodigo());
            }
        }
        return transacao;
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, MovParcelaVO movParcelaVO) {
        ParceiroFidelidadeZW parceiroFidelidadeZW = null;
        try {
            parceiroFidelidadeZW = new ParceiroFidelidadeZW(recorrenciaService.getCon());
            parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        } finally {
            parceiroFidelidadeZW = null;
        }
    }

    private void processarMovParcelaResultadoCobranca(TransacaoVO transacaoVO) {
        MovParcelaResultadoCobranca dao = null;
        try {
            if (transacaoVO == null || UteisValidacao.emptyList(transacaoVO.getListaParcelas())) {
                return;
            }

            dao = new MovParcelaResultadoCobranca(recorrenciaService.getCon());
            dao.processarParcelas(transacaoVO.getListaParcelas());
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "processarMovParcelaResultadoCobranca ERRO: " + ex.getMessage());
        } finally {
            dao = null;
        }
    }

}

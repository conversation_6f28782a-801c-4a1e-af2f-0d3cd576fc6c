package servicos.impl.gatewaypagamento;

import acesso.webservice.AcessoControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Telefone;
import org.apache.commons.lang.StringUtils;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.text.Normalizer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ThreadNotificacaoTransacao extends Thread {

    final String urlSMS = "http://s.smsup.com.br:8081/smsservice/api/sender/do";

    private Connection con;
    private MovParcelaVO movParcelaVO;
    private ClienteVO clienteVO;
    private TransacaoVO transacaoVO;
    private String key;

    public ThreadNotificacaoTransacao(MovParcelaVO movParcelaVO, TransacaoVO transacaoVO, ClienteVO clienteVO, String key, Connection con) {
        this.movParcelaVO = movParcelaVO;
        this.transacaoVO = transacaoVO;
        this.clienteVO = clienteVO;
        this.con = con;
        this.key = key;
        this.setName("ThreadNotificacaoTransacao_" + transacaoVO.getCodigo());
    }

    @Override
    public void run() {
        try {
            Integer codPessoa = movParcelaVO.getPessoa().getCodigo();
            try {
                Email emailDao = new Email(con);
                List<EmailVO> emails = emailDao.consultarEmails(codPessoa, false, Uteis.NIVELMONTARDADOS_MINIMOS);

                Empresa empresaDao = new Empresa(con);
                EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                if (StringUtils.isNotBlank(empresaVO.getEmailNotificacaoVendasOnline())) {
                    emails.add(new EmailVO(0, empresaVO.getEmailNotificacaoVendasOnline(), false));
                }

                String[] emailsEnviar = new String[emails.size()];
                for (int i = 0; i < emails.size(); i++) {
                    emailsEnviar[i] = emails.get(i).getEmail();
                }

                enviarEmail(emailsEnviar, empresaVO);
            } catch (Exception ex) {
                Uteis.logar(ex, ThreadNotificacaoTransacao.class);
            }

            try {
                Empresa empresaDao = new Empresa(con);
                EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                Telefone telefoneDao = new Telefone(con);
                List<TelefoneVO> telefones = telefoneDao.consultarTelefones(codPessoa, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                for (TelefoneVO telefoneVO : telefones) {
                    if (telefoneVO.getNumeroCelular()) {
                        enviarSMS(key, empresaVO.getTokenSMSShortCode(), telefoneVO.getNumero());
                    }
                }
            } catch (Exception ex) {
                Uteis.logar(ex, ThreadNotificacaoTransacao.class);
            }

        } catch (Exception ex) {
            Uteis.logar(ex, ThreadNotificacaoTransacao.class);
        }
    }

    public String enviarEmail(String[] email, EmpresaVO empresaVO) throws Exception {
        AcessoControle acessoControle = new AcessoControle(con);
        StringBuilder html = new StringBuilder();
        html.append("<h1>Situação da compra</h1><br/>");
        try {
            html.append("<h3>").append(clienteVO.getMatricula()).append(" - ").append(clienteVO.getNome_Apresentar()).append("</h3><br/>");
        } catch (Exception ignored) {
        }
        html.append("Informamos que a parcela <b>").append(movParcelaVO.getCodigo()).append("</b> no valor de ").append(transacaoVO.getValor_Apresentar()).append("<br/>");
        String assuntoEmail;
        if (transacaoVO == null) {
            html.append("Houve <b>erro ao processar pagamento</b>");
            assuntoEmail = empresaVO.getNome() + " - Erro ao processar pagamento";
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
            html.append("Foi <b>realizado com sucesso</b>");
            assuntoEmail = empresaVO.getNome() + " - Pagamento realizado com sucesso";
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            html.append("Está <b>pendente de confirmação</b>");
            assuntoEmail = empresaVO.getNome() + " - Pagamento pendente de confirmação";
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            html.append("<b>").append(transacaoVO.getSituacao().getHint()).append("</b>");
            assuntoEmail = empresaVO.getNome() + " - Pagamento não foi aprovado";
        } else {
            html.append("Está com a seguinte situação: <b>").append(transacaoVO.getResultadoRequisicao()).append("</b>");
            assuntoEmail = empresaVO.getNome() + " - Situação do pagamento: " + transacaoVO.getResultadoRequisicao();
        }

        acessoControle.enviarEmail(email, assuntoEmail, html);
        return "OK";
    }

    public String enviarSMS(String chaveEmpresa, String tokenSMS, String telefone) throws Exception {

        Map<String, String> params = new HashMap<String, String>();
        params.put("key", chaveEmpresa);
        params.put("token", tokenSMS);

        String mensagem = montarMensagemSMS(telefone);
        params.put("msgs", mensagem);

        String retornoRequest = "";
        try {
            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            executeRequestHttpService.connectTimeout = 5000;
            executeRequestHttpService.readTimeout = 15000;
            retornoRequest = executeRequestHttpService.executeRequestInner(urlSMS, params);
        } catch (IOException ioException) {
            Uteis.logar(ioException, this.getClass());
        }
        return retornoRequest;
    }

    private String montarMensagemSMS(String telefone) throws Exception {
        telefone = telefone.replaceAll("[()-.]", "");
        if (telefone.length() == 11) {
            telefone = "55" + telefone;
        }

        String mensagem;
        if (transacaoVO == null) {
            mensagem = "Erro ao processar pagamento";
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
            mensagem = "Pagamento realizado com sucesso";
        } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            mensagem = "Pagamento pendente de confirmacao";
        } else {
            String resultadoNormalizado = Normalizer.normalize(transacaoVO.getResultadoRequisicao(), Normalizer.Form.NFD);
            resultadoNormalizado = resultadoNormalizado.replaceAll("[^\\p{ASCII}]", "");
            mensagem = "Situacao do pagamento: " + resultadoNormalizado;
        }

        if (telefone.length() == 13) {
            return telefone + "| " + mensagem;
        }
        return null;
    }
}

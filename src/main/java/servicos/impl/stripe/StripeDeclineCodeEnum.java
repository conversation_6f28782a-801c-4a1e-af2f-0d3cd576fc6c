package servicos.impl.stripe;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 19/10/2021
 */
public enum StripeDeclineCodeEnum {

    StatusNENHUM("NENHUM", ""),
    Status0("0", "Transação autorizada.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status01("authentication_required", "The card was declined as the transaction requires authentication. The customer should try again and authenticate their card when prompted during the transaction. If the card issuer returns this decline code on an authenticated transaction, the customer needs to contact their card issuer for more information."),
    Status02("approve_with_id", "The payment cannot be authorized. The payment should be attempted again. If it still cannot be processed, the customer needs to contact their card issuer."),
    Status03("call_issuer", "The card has been declined for an unknown reason. The customer needs to contact their card issuer for more information."),
    Status04("card_not_supported", "The card does not support this type of purchase. The customer needs to contact their card issuer to make sure their card can be used to make this type of purchase."),
    Status05("card_velocity_exceeded", "The customer has exceeded the balance or credit limit available on their card. The customer should contact their card issuer for more information."),
    Status06("currency_not_supported", "The card does not support the specified currency.The customer needs to check with the issuer whether the card can be used for the type of currency specified."),
    Status07("do_not_honor", "The card has been declined for an unknown reason.The customer needs to contact their card issuer for more information."),
    Status08("do_not_try_again", "The card has been declined for an unknown reason.The customer should contact their card issuer for more information."),
    Status09("duplicate_transaction", "A transaction with identical amount and credit card information was submitted very recently.Check to see if a recent payment already exists."),
    Status10("expired_card", "The card has expired.The customer should use another card."),
    Status11("fraudulent", "The payment has been declined as Stripe suspects it is fraudulent.Do not report more detailed information to your customer. Instead, present as you would the generic_decline described below."),
    Status12("generic_decline", "The card has been declined for an unknown reason.The customer needs to contact their card issuer for more information."),
    Status13("incorrect_number", "The card number is incorrect.The customer should try again using the correct card number."),
    Status14("incorrect_cvc", "The CVC number is incorrect. The customer should try again using the correct CVC."),
    Status15("incorrect_pin", "The PIN entered is incorrect. This decline code only applies to payments made with a card reader.The customer should try again using the correct PIN."),
    Status16("incorrect_zip", "The ZIP/postal code is incorrect.The customer should try again using the correct billing ZIP/postal code."),
    Status17("insufficient_funds", "The card has insufficient funds to complete the purchase.The customer should use an alternative payment method."),
    Status18("invalid_account", "The card, or account the card is connected to, is invalid.The customer needs to contact their card issuer to check that the card is working correctly."),
    Status19("invalid_amount", "The payment amount is invalid, or exceeds the amount that is allowed.If the amount appears to be correct, the customer needs to check with their card issuer that they can make purchases of that amount."),
    Status20("invalid_cvc", "The CVC number is incorrect.The customer should try again using the correct CVC."),
    Status21("invalid_expiry_month", "The expiration month is invalid.The customer should try again using the correct expiration date."),
    Status22("invalid_expiry_year", "The expiration year is invalid.The customer should try again using the correct expiration date."),
    Status23("invalid_number", "The card number is incorrect.The customer should try again using the correct card number."),
    Status24("invalid_pin", "The PIN entered is incorrect. This decline code only applies to payments made with a card reader.The customer should try again using the correct PIN."),
    Status25("lost_card", "The payment has been declined because the card is reported lost.The specific reason for the decline should not be reported to the customer. Instead, it needs to be presented as a generic decline."),
    Status26("merchant_blacklist", "The payment has been declined because it matches a value on the Stripe user?s block list.Do not report more detailed information to your customer. Instead, present as you would the generic_decline described above."),
    Status27("new_account_information_available", "The card, or account the card is connected to, is invalid.The customer needs to contact their card issuer for more information."),
    Status28("no_action_taken", "The card has been declined for an unknown reason.The customer should contact their card issuer for more information."),
    Status29("not_permitted", "The payment is not permitted.The customer needs to contact their card issuer for more information."),
    Status30("offline_pin_required", "The card has been declined as it requires a PIN.The customer should try again by inserting their card and entering a PIN."),
    Status31("online_or_offline_pin_required", "The card has been declined as it requires a PIN.If the card reader supports Online PIN, the customer should be prompted for a PIN without a new transaction being created. If the card reader does not support Online PIN, the customer should try again by inserting their card and entering a PIN."),
    Status32("pickup_card", "The card cannot be used to make this payment (it is possible it has been reported lost or stolen).The customer needs to contact their card issuer for more information."),
    Status33("pin_try_exceeded", "The allowable number of PIN tries has been exceeded.The customer must use another card or method of payment."),
    Status34("processing_error", "An error occurred while processing the card.The payment should be attempted again. If it still cannot be processed, try again later."),
    Status35("reenter_transaction", "The payment could not be processed by the issuer for an unknown reason.The payment should be attempted again. If it still cannot be processed, the customer needs to contact their card issuer."),
    Status36("restricted_card", "The card cannot be used to make this payment (it is possible it has been reported lost or stolen).The customer needs to contact their card issuer for more information."),
    Status37("revocation_of_all_authorizations", "The card has been declined for an unknown reason.The customer should contact their card issuer for more information."),
    Status38("revocation_of_authorization", "The card has been declined for an unknown reason.The customer should contact their card issuer for more information."),
    Status39("security_violation", "The card has been declined for an unknown reason.The customer needs to contact their card issuer for more information."),
    Status40("service_not_allowed", "The card has been declined for an unknown reason.The customer should contact their card issuer for more information."),
    Status41("stolen_card", "The payment has been declined because the card is reported stolen.The specific reason for the decline should not be reported to the customer. Instead, it needs to be presented as a generic decline."),
    Status42("stop_payment_order", "The card has been declined for an unknown reason.The customer should contact their card issuer for more information."),
    Status43("testmode_decline", "A Stripe test card number was used.A genuine card must be used to make a payment."),
    Status44("transaction_not_allowed", "The card has been declined for an unknown reason.The customer needs to contact their card issuer for more information."),
    Status45("try_again_later", "The card has been declined for an unknown reason.Ask the customer to attempt the payment again. If subsequent payments are declined, the customer should contact their card issuer for more information."),
    Status46("withdrawal_count_limit_exceeded", "The customer has exceeded the balance or credit limit available on their card.The customer should use an alternative payment method."),
    Status47("test_mode_live_card", "Your card was declined. Your request was in\n test mode, but used a non test (live) card.");


    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    StripeDeclineCodeEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private StripeDeclineCodeEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private StripeDeclineCodeEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static StripeDeclineCodeEnum valueOff(String id) {
        for (StripeDeclineCodeEnum declinedCode : StripeDeclineCodeEnum.values()) {
            if (declinedCode.getId().equals(id)) {
                return declinedCode;
            }
        }
        return StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}

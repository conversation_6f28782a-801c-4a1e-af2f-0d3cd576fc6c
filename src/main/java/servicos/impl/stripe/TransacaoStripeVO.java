package servicos.impl.stripe;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 18/10/2021
 */
public class TransacaoStripeVO extends TransacaoVO {

    @Override
    public String getValorCartaoMascarado() {
        return getCartaoMascarado();
    }

    @Override
    public String getResultadoRequisicao() {
        //Na Stripe pode ser que tenha code, declineCode e também message, todos trazem detalhes sobre o erro.
        //verifica uma por uma e vai concatenando a cada mensagem se houver
        try {
            StringBuilder msg = new StringBuilder();
            JSONObject json = new JSONObject(getParamsResposta());
            if (json.has("error")) {
                //primeira mensagem do enum StripeRetornoEnum
                String code = json.getJSONObject("error").optString("code");
                if (!UteisValidacao.emptyString(code)) {
                    StripeRetornoEnum retornoStripe = StripeRetornoEnum.valueOff(code);
                    if (!retornoStripe.equals(StripeRetornoEnum.StatusNENHUM)) {
                        msg.append(retornoStripe.getDescricao());
                    }
                }
                //Segunda mensagem do enum StripeDeclineCodeEnum
                String declinedCode = json.getJSONObject("error").optString("decline_code");
                if (!UteisValidacao.emptyString(declinedCode)) {
                    StripeDeclineCodeEnum retornoErroStripe = StripeDeclineCodeEnum.valueOff(declinedCode);
                    if (!retornoErroStripe.equals(StripeDeclineCodeEnum.StatusNENHUM)) {
                        msg.append("<br><b>Reason: </b>" + retornoErroStripe.getDescricao());
                    }
                }
                //Terceira mensagem do objeto json de retorno (atributo message);
                String message = json.getJSONObject("error").optString("message");
                if (!UteisValidacao.emptyString(message)) {
                    msg.append(message);
                }
                return msg.toString();
            } else {
                //se não tiver error e tiver status "succeeded" é porque foi aprovada
                String code = json.optString("status");
                if (!UteisValidacao.emptyString(code) && code.equals("succeeded")) {
                    StripeRetornoEnum retornoStripe = StripeRetornoEnum.valueOff("0");
                    msg.append(retornoStripe.getDescricao());
                    return msg.toString();
                }
                return "Erro Desconhecido";
            }
        } catch (Exception ignored) {
            return "Erro desconhecido";
        }
    }

    @Override
    public String getAutorizacao() {
        //autorização fica sendo o id do paymentIntent pois não tem o cód. de aut. no retorno.
        try {
            String aut = "";
            JSONObject json = new JSONObject(getParamsResposta());
            aut = json.optString("id");
            if (!UteisValidacao.emptyString(aut)) {
                return aut;
            }
        } catch (Exception ex) {
            return "";
        }
        return "";
    }

    @Override
    public String getBandeira() {
        try {
            String brand = "";
            try {
                JSONObject retornoJSON = new JSONObject(getParamsResposta());
                if (retornoJSON.has("error")) {
                    brand = retornoJSON.getJSONObject("error").getJSONObject("payment_intent").getJSONObject("charges").getJSONArray("data").getJSONObject(0).getJSONObject("payment_method_details").getJSONObject("card").optString("brand");
                    if (!UteisValidacao.emptyString(brand)) {
                        return brand.toUpperCase();
                    }
                } else {
                    brand = retornoJSON.getJSONObject("charges").getJSONArray("data").getJSONObject(0).getJSONObject("payment_method_details").getJSONObject("card").optString("brand");
                    if (!UteisValidacao.emptyString(brand)) {
                        return brand.toUpperCase();
                    }
                }
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(brand)) {
                JSONObject obj = new JSONObject(getOutrasInformacoes());
                brand = obj.optString("cartaoBandeira");
            }
            return brand;
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getCartaoMascarado() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String card = obj.optString("cartaoMascarado");
            if (!UteisValidacao.emptyString(card)) {
                return APF.getCartaoMascarado(card);
            }
        } catch (Exception e) {
            return "";
        }

        try {
            String last4 = "";
            JSONObject json = new JSONObject(getParamsResposta());
            if (json.has("error")) {
                last4 = json.getJSONObject("error").getJSONObject("payment_intent").getJSONObject("charges").getJSONArray("data").getJSONObject(0).getJSONObject("payment_method_details").getJSONObject("card").optString("last4");
                if (!UteisValidacao.emptyString(last4)) {
                    return "************" + last4;
                }
            } else {
                last4 = json.getJSONObject("charges").getJSONArray("data").getJSONObject(0).getJSONObject("payment_method_details").getJSONObject("card").optString("last4");
                return "************" + last4;
            }
        } catch (Exception ex) {
        }
        return "";
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao) && !UteisValidacao.emptyString(getCodigoExterno())) {
            return getCodigoExterno();
        }
        if (nomeAtributo.equals(APF.CodigoAutorizacao)) {
            return getAutorizacao();
        }
        if (nomeAtributo.equals(APF.CartaoMascarado)) {
            return getCartaoMascarado();
        }
        return valor;
    }

    @Override
    public String getCodErroExterno() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            if (json.has("error")) {
                String code = json.getJSONObject("error").optString("code");
                if (!UteisValidacao.emptyString(code)) {
                    StripeRetornoEnum retornoStripe = StripeRetornoEnum.valueOff(code);
                    if (!retornoStripe.equals(StripeRetornoEnum.StatusNENHUM)) {
                        return retornoStripe.name().replace("Status", "");
                    }
                }
            }
        } catch (Exception ignored) {
            return "";
        }
        return "";
    }
}

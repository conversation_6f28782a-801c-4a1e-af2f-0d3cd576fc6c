package servicos.impl.admCoreMs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClienteRedeEmpresaDTO {
    private Integer codigo;
    private Integer codigoMatricula;
    private String nome;
    private String cpf;
    private String chaveEmpresa;
    private Integer codigoEmpresa;
    private String nomeEmpresa;
    private Long dataSincronizacao;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCodigoMatricula() {
        return codigoMatricula;
    }

    public void setCodigoMatricula(Integer codigoMatricula) {
        this.codigoMatricula = codigoMatricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getChaveEmpresa() {
        return chaveEmpresa;
    }

    public void setChaveEmpresa(String chaveEmpresa) {
        this.chaveEmpresa = chaveEmpresa;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Long getDataSincronizacao() {
        return dataSincronizacao;
    }

    public void setDataSincronizacao(Long dataSincronizacao) {
        this.dataSincronizacao = dataSincronizacao;
    }
}

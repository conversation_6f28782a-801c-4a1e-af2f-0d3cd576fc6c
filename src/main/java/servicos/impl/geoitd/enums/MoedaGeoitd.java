package servicos.impl.geoitd.enums;

public enum MoedaGeoitd {
    PESOS("858","Pesos"),
    DOLARES("840","Dolares");

    private String cod;
    private String desc;

    MoedaGeoitd(){

    }

    MoedaGeoitd(String cod, String desc) {
        this.cod = cod;
        this.desc = desc;
    }

    public String getCod() {
        return cod;
    }

    public String getDesc() {
        return desc;
    }
}

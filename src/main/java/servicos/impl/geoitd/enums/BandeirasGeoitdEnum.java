package servicos.impl.geoitd.enums;

import negocio.comuns.financeiro.enumerador.TipoFormaPagto;

public enum BandeirasGeoitdEnum {
    DEFAULT("0","(Nenhum)","0",""),
    AMEX(AcquirerGeoitd.AMERICANEXPRESS.getCod(),"Amex","2", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    ANDA(AcquirerGeoitd.ANDA.getCod(),"Anda","74", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    CABAL(AcquirerGeoitd.CABAL.getCod(),"Cabal","5", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    CABALBPS(AcquirerGeoitd.CABAL.getCod(),"Cabal BPS","6", <PERSON><PERSON>oFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    CABALDEBIT(AcquirerGeoitd.CABAL.getCod(),"Cabal Debit","7", TipoFormaPagto.CARTAODEBITO.getDescricao()),
    CREDITEL(AcquirerGeoitd.CREDITEL.getCod(),"Creditel","55", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    CREDITELPREPAGA(AcquirerGeoitd.CREDITEL.getCod(),"Creditel Prepaga","57", TipoFormaPagto.CARTAODEBITO.getDescricao()),
    CREDITELBPS(AcquirerGeoitd.CREDITEL.getCod(),"Creditel BPS","58", TipoFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    CDIRECTOS(AcquirerGeoitd.CREDITOSDIRECTOS.getCod(),"C. Directos","4", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    EDENRED(AcquirerGeoitd.EDENRED.getCod(),"Edenred","10", TipoFormaPagto.CARTAODEBITO.getDescricao()),
    MASTERCARD(AcquirerGeoitd.FIRSTDATA.getCod(),"MasterCard","52", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    MASTERCARDJCB(AcquirerGeoitd.FIRSTDATA.getCod(),"MasterCard JCB","70", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    DINERS(AcquirerGeoitd.FIRSTDATA.getCod(),"Diners","8", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    MAESTRO(AcquirerGeoitd.FIRSTDATA.getCod(),"Maestro","15", TipoFormaPagto.CARTAODEBITO.getDescricao()),
    MAESTROBPS(AcquirerGeoitd.FIRSTDATA.getCod(),"Maestro BPS","16", TipoFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    LIDER(AcquirerGeoitd.FIRSTDATA.getCod(),"Lider","13", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    LIDERBPS(AcquirerGeoitd.FIRSTDATA.getCod(),"Lider BPS","14", TipoFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    LIDERSODEXO(AcquirerGeoitd.FIRSTDATA.getCod(),"Lider Sodexo","86", TipoFormaPagto.CARTAODEBITO.getDescricao()),
    MIDES(AcquirerGeoitd.MIDES.getCod(),"Mides","19", TipoFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    OCACARD(AcquirerGeoitd.OCA.getCod(),"OCA Card","21", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    OCABPS(AcquirerGeoitd.OCA.getCod(),"OCA BPS","35", TipoFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    PASSCARD(AcquirerGeoitd.PASSCARD.getCod(),"PassCard","75", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    VISA(AcquirerGeoitd.VISA.getCod(),"Visa","24", TipoFormaPagto.CARTAOCREDITO.getDescricao()),
    VISADEBITO(AcquirerGeoitd.VISA.getCod(),"Visa Debito","34", TipoFormaPagto.CARTAODEBITO.getDescricao()),
    VISADEBITOBPSPRESTACIONES(AcquirerGeoitd.VISA.getCod(),"Visa Debito BPS Prestaciones","11", TipoFormaPagto.CARTAODEBITO.getDescricao() + "AFAM"),
    VISAALIMENTACION(AcquirerGeoitd.VISA.getCod(),"Visa Alimentacion","44", TipoFormaPagto.CARTAODEBITO.getDescricao());

    private String codAcquirerGeoitd;
    private String descIssuerGeoitd;
    private String codcIssuerGeoitd;
    private String tipoFormaPagto;

    BandeirasGeoitdEnum(String codAcquirerGeoitd, String descIssuerGeoitd, String codcIssuerGeoitd, String tipoFormaPagto) {
        this.codAcquirerGeoitd = codAcquirerGeoitd;
        this.descIssuerGeoitd = descIssuerGeoitd;
        this.codcIssuerGeoitd = codcIssuerGeoitd;
        this.tipoFormaPagto = tipoFormaPagto;
    }

    public String getCodAcquirerGeoitd() {
        return codAcquirerGeoitd;
    }

    public String getDescIssuerGeoitd() {
        return codcIssuerGeoitd + " - " + descIssuerGeoitd;
    }

    public String getCodcIssuerGeoitd() {
        return codcIssuerGeoitd;
    }

    public String getTipoFormaPagto() {
        return tipoFormaPagto;
    }
}

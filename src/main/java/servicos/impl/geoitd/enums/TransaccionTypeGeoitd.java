package servicos.impl.geoitd.enums;

public enum TransaccionTypeGeoitd {
    COMPRA("C","Compra"),
    ANULACION("A","Anulación"),
    DEVOLUCION("D","Devolución"),
    VENTAADELANTO("T","Venta con Adelanto"),
    INICIALIZACION("I","Inicialización"),
    CIERRELOTE("L","Cierre de lote"),
    ECHOTESTE("E","Echo test"),
    ERROR("X","Error"),
    CONSULTATARJETA("Q","Consulta de Tarjeta"),
    TESTTRANSACTION("W","Echo test transaction");

    private String cod;
    private String desc;

    TransaccionTypeGeoitd(String cod, String desc) {
        this.cod = cod;
        this.desc = desc;
    }

    TransaccionTypeGeoitd() {

    }

    public String getCod() {
        return cod;
    }

    public String getDesc() {
        return desc;
    }
}

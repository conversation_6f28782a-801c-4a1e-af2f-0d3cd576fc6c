package servicos.impl.geoitd.enums;

public enum RespostaPosPinpad {
    RESPONSEVAZIO("", "En espera de operación en la máquina de tarjetas. Inténvalo de nuevo.", "EM ESPERA DE OPERACIÓN EN LA MÁQUINA DE TARJETAS. INTÉNVALO DE NUEVO."),
    RESPONSE00("00", "Aprobado", "APROBADA"),
    RESPONSE01("01", "Contacte al emisor, en caso de ser aprobada realizar operación Off Line", "PEDIR AUTORIZACION"),
    RESPONSE02("02", "Contacte al emisor, en caso de ser aprobada realizar operación Off Line", "PEDIR AUTORIZACION"),
    RESPONSE03("03", "Comercio inválido ", "COMERCIO INVALIDO"),
    RESPONSE04("04", "Retener tarjeta", "RETENER TARJETA"),
    RESPONSE05("05", "Transacción negada", "DENEGADA"),
    RESPONSE06("06", "Error (utilizado en transferencia de archivos)", "N/A"),
    RESPONSE07("07", "Retenga y llame", "RETENGA Y LLAME"),
    RESPONSE10("10", "Aprobado Parcialmente (CashBack)", "APROBADO SOLO VENTAS"),
    RESPONSE11("11", "Aprobado (igual que 00)", "APROBADA"),
    RESPONSE12("12", "Transacción inválida", "TRANSAC. INVALIDA"),
    RESPONSE13("13", "Monto inválido", "MONTO INVALIDO"),
    RESPONSE14("14", "Tarjeta inválida o cédula no corresponde con titular", "TARJETA INVALIDA"),
    RESPONSE15("15", "Emisor no valido", "EMISOR NO VALIDO"),
    RESPONSE21("21", "No se tomó acción (reversas y anulaciones)", "NO EXISTE ORIGINAL"),
    RESPONSE25("25", "No existe original, registro no encontrado en el archivo de transacciones", "NO EXISTE ORIGINAL"),
    RESPONSE30("30", "Error en formato del mensaje", "ERROR EN FORMATO"),
    RESPONSE31("31", "Tarjeta no soportada", "CARD NOT SUPPORTED"),
    RESPONSE38("38", "Denegada, excede cantidad de reintentos de PIN permitida", "EXCEDE ING. DE PIN"),
    RESPONSE41("41", "Tarjeta perdida, retener", "PERDIDA, RETENER"),
    RESPONSE43("43", "Tarjeta robada, retener ", "ROBADA, RETENER"),
    RESPONSE45("45", "Tarjeta inhabilitada para operar en cuotas", "NO OPERA EN CUOTAS"),
    RESPONSE46("46", "Tarjeta no vigente", "TARJETA NO VIGENTE"),
    RESPONSE47("47", "PIN requerido", "PIN REQUERIDO"),
    RESPONSE48("48", "Excede cantidad máxima de cuotas permitidas", "EXCEDE MAX. CUOTAS"),
    RESPONSE49("49", "Error en formato de fecha de expiración", "ERROR FECHA VENCIM"),
    RESPONSE50("50", "Monto ingresado en entrega supera limite", "ENTREGA SUPERA LIM"),
    RESPONSE51("51", "Sin disponible", "SALDO INSUFICIENTE"),
    RESPONSE53("53", "Cuenta inexistente", "CTA. INEXISTENTE"),
    RESPONSE54("54", "Tarjeta vencida", "TARJETA VENCIDA"),
    RESPONSE55("55", "PIN incorrecto", "PIN INCORRECTO"),
    RESPONSE56("56", "Emisor no habilitado en el sistema", "TARJ.NO HABILITADA"),
    RESPONSE57("57", "Transacción no permitida a esta tarjeta", "TRANS.NO PERMITIDA"),
    RESPONSE58("58", "Servicio inválido. Transacción no permitida a la terminal", "SERVICIO INVALIDO"),
    RESPONSE61("61", "Excede  monto límite de actividad - Contacte al emisor", "EXCEDE MONTO LIMIT"),
    RESPONSE62("62", "Tarjeta restringida para dicha terminal u operacion", "TARJETA RESTRINGIDA"),
    RESPONSE65("65", "Límite de actividad excedido ? Contacte al emisor", "EXCEDE LIM.TARJETA"),
    RESPONSE76("76", "Solicitar autorización telefónica, en caso de ser aprobada, cargar el código obtenido y dejar operación en OFFLINE", "LLAMAR AL EMISOR"),
    RESPONSE77("77", "Error en plan/cuotas", "ERROR PLAN/CUOTAS "),
    RESPONSE81("81", "Error criptográfico en manejo de pin online", "ERROR CRIPTOGRAFICO"),
    RESPONSE82("82", "Error en validación de CVV", "CVV INVALIDO"),
    RESPONSE83("83", "Imposible verificar PIN en manejo de pin online", "IMPOSIBLE VERIFICAR PIN"),
    RESPONSE84("84", "Moneda Invalida", "MONEDA INVALIDA"),
    RESPONSE85("85", "Aprobado", "APROBADA"),
    RESPONSE89("89", "Terminal inválida", "TERMINAL INVALIDA"),
    RESPONSE91("91", "Emisor no responde", "EMISOR NO RESPONDE"),
    RESPONSE94("94", "Número de secuencia duplicado, repita incrementando en uno el system trace", "NRO. SEC.DUPLICADO"),
    RESPONSE95("95", "Diferencia en el cierre de transacciones, inicie Batch Upload", "RE-TRANSMITIENDO"),
    RESPONSE96("96", "Error de sistema", "ERROR EN SISTEMA"),
    RESPONSE98("98", "Mensajes Especiales", "MENSAJES ESPECIALES"),
    RESPONSECE("CE", "Error en conexión al Host", ""),
    RESPONSECF("CF", "Consulta Caja Fallido", ""),
    RESPONSECT("CT", "Cancelar Transacción", ""),
    RESPONSEEA("EA", "Error en código de comercio", ""),
    RESPONSEEB("EB", "Error en Batch (Lote) ", ""),
    RESPONSEEC("EC", "Error en Cierre de lote", ""),
    RESPONSEEE("EE", "Error Rutinas EMV", ""),
    RESPONSEEI("EI", "Error en Información enviada al PinPad", ""),
    RESPONSEER("ER", "Error enviando Reverso al Autorizador", ""),
    RESPONSEET("ET", "Error en Ingreso Inicial de Datos", ""),
    RESPONSELL("LL", "Lote Lleno", ""),
    RESPONSELV("LV", "Lote Vacío", ""),
    RESPONSEMK("MK", "MasterKey Ausente", ""),
    RESPONSEN7("N7", "CVV2 no válido", "CVV2 NO VALIDO"),
    RESPONSENC("NC", "No responde Caja a Mensaje Inicial", "CVV2 NO VALIDO"),
    RESPONSENP("NP", "Operación NO Permitida", "CVV2 NO VALIDO"),
    RESPONSENR("NR", "No responde Autorizador", ""),
    RESPONSEOF("OF", "Aprobación Offline", "APROBADA OFFLINE"),
    RESPONSETI("TI", "Tarjeta incorrecta", ""),
    RESPONSETN("TN", "Tarjeta Incorrecta en Offline (ùltimos N dígitos)", ""),
    RESPONSETO("TO", "TimeOut Ingreso Tarjeta", ""),
    RESPONSEXX("XX", "Cualquier otro código no especificado, denegada", "RECHAZADA [código]"),
    RESPONSEY1("Y1", "Aprobado", "APROBADA CHIP"),
    RESPONSEY3("Y3", "Aprobado", "APROBADA CHIP"),
    RESPONSEZ1("Z1", "Denegada", "DENEGADA CHIP OFFLINE"),
    RESPONSEZ3("Z3", "Denegada", "DENEGADA CHIP OFFLINE");

    private String cod;
    private String descDetalahada;
    private String desc;

    RespostaPosPinpad(String cod, String descDetalahada, String desc) {
        this.cod = cod;
        this.descDetalahada = descDetalahada;
        this.desc = desc;
    }

    public static String consultarPorCod(String cod) {
        for (RespostaPosPinpad lstRespostaPosPinpad : RespostaPosPinpad.values()) {
            if (lstRespostaPosPinpad.cod.equals(cod)) {
                return lstRespostaPosPinpad.desc;
            }
        }
        return "";
    }

    public String getCod() {
        return cod;
    }

    public String getDescDetalahada() {
        return descDetalahada;
    }

    public String getDesc() {
        return desc;
    }
}

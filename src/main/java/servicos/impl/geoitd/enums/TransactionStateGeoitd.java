package servicos.impl.geoitd.enums;

public enum TransactionStateGeoitd {
    RESPONSE1(1, "Esperando transaccion en el pinpad."),
    RESPONSE2(2, "Transaccion completada con exito."),
    RESPONSE3(3, "Transaccion completada con exito. (pinpad trabajando solo)."),
    RESPONSE5(5, "Transaccion expirada."),
    RESPONSE6(6, "Transaccion cancelada."),
    RESPONSE7(7, "Datos consultados por elpinpad."),
    RESPONSE8(8, "Datos de la tarjeta obtenidos por el pinpad."),
    RESPONSE9(9, "Pinpad esta en procesamiento de transacción."),
    RESPONSE10(10, "Transaccion reservada."),
    RESPONSE11(11, "Transaccion cancelada"),
    RESPONSE12(12, "Transaccion devuelta.");

    private Integer cod;
    private String desc;

    TransactionStateGeoitd(Integer cod, String desc){
        this.cod = cod;
        this.desc = desc;
    }

    public static String obterPorCod(Integer cod){
        for (TransactionStateGeoitd transactionStateGeoitd : TransactionStateGeoitd.values() ) {
            if(transactionStateGeoitd.getCod() != null && transactionStateGeoitd.getCod() == cod)
                return transactionStateGeoitd.getDesc();
        }

        return "";
    }

    public Integer getCod() {
        return cod;
    }

    public String getDesc() {
        return desc;
    }
}

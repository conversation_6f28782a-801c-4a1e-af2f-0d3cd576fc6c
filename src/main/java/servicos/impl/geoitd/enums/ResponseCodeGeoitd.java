package servicos.impl.geoitd.enums;

/*
Para detalhes dos campos favor verificar a documentação localizada
 \\master\suporte\Documentacoes\GEOCOM.
*
 */

public enum ResponseCodeGeoitd {
    RESPONSE0(0, "OK"),
    RESPONSE100(100, "Número de pinpad no es valido."),
    RESPONSE101(101, "el número de establecimiento no es válido."),
    RESPONSE102(102, "Número de caja inválido."),
    RESPONSE103(103, "Fecha de transacción inválida.."),
    RESPONSE104(104, "Valor no es valido."),
    RESPONSE105(105, "Número de parcelas inválidas."),
    RESPONSE106(106, "Plan no es valido."),
    RESPONSE107(107, "La moneda no es válida."),
    RESPONSE108(108, "Moeda não é válida."),
    RESPONSE109(109, "Ticket inválido."),
    RESPONSE110(110, "No hay transacción."),
    RESPONSE111(111, "Transacción finalizada."),
    RESPONSE112(112, "Identificador de sistema no válido."),
    RESPONSE113(113, "Debe consultar por transacción."),
    RESPONSE400(400, "Bad Request, status 400."),
    RESPONSE10(10, "En espera de la operación de pinpad"),
    RESPONSE11(11, "Se ha excedido el tiempo de transacción. Enviar los datos nuevamente."),
    RESPONSE12(12, "  La tarjeta fue leída con éxito por el pinpad. "),
    RESPONSE999(999, "Error indeterminado. Inténtalo de nuevo..");

    private Integer cod;
    private String desc;

    ResponseCodeGeoitd(int cod, String desc) {
        this.cod = cod;
        this.desc = desc;
    }

    public static String obterPorCod(Integer cod) {
        for (ResponseCodeGeoitd responseCodeGeoitd : ResponseCodeGeoitd.values()) {
            if (responseCodeGeoitd.getCod()  != null && responseCodeGeoitd.getCod() == cod.intValue())
                return responseCodeGeoitd.getDesc();
        }
        return "";
    }

    public Integer getCod() {
        return cod;
    }

    public String getDesc() {
        return desc;
    }

//    public static void main(String[]args){
//        System.out.println(obterPorCod(0));
//    }

}





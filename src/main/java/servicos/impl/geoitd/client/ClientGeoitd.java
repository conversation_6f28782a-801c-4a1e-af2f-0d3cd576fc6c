package servicos.impl.geoitd.client;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.impl.geoitd.DadosGeoitdFinancialPurchaseTO;
import servicos.impl.geoitd.enums.ResponseCodeGeoitd;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;


import java.io.IOException;

public class ClientGeoitd  {
    private String sTransactionId;
    private Long transactionId;
    private JSONObject resultLoadingPinpad,jsonPinpadGeoitd;

    public  JSONObject retornoEndpointGeoitd(String json) throws IOException {
        jsonPinpadGeoitd = DadosGeoitdFinancialPurchaseTO.toJsonPinpadGeoitd(comunicarServidorGeoitd(json)); // Retorna as chaves para comunicar com o pinpad.
        if(jsonPinpadGeoitd.getInt("msgCode") != 0){
            jsonPinpadGeoitd.put("descCode", String.valueOf(ResponseCodeGeoitd.obterPorCod(jsonPinpadGeoitd.getInt("msgCode"))));
            return jsonPinpadGeoitd;
        }

        sTransactionId = jsonPinpadGeoitd.getString("STransactionId");
        transactionId = (Long) jsonPinpadGeoitd.get("TransactionId");

        resultLoadingPinpad = ClientGeoitd.comunicarComPinpadGeoitd(jsonPinpadGeoitd); //Comunica com o pinpad

        resultLoadingPinpad.put("msgCode", resultLoadingPinpad.getInt("ResponseCode"));
        resultLoadingPinpad.put("descCode", String.valueOf(ResponseCodeGeoitd.obterPorCod(resultLoadingPinpad.getInt("ResponseCode"))));
        //resultLoadingPinpad.put("posResponseCode", resultLoadingPinpad.getString("PosResponseCode"));

        return resultLoadingPinpad;
    }

    private static JSONObject comunicarServidorGeoitd(String json) throws IOException {
        //String url = PropsService.getPropertyValue("urlAPIGeoitdDev"); Ambiente de Desenvolvimento
        String url = PropsService.getPropertyValue("urlAPIGeoitd");

        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpPost post = new HttpPost(url + "processFinancialPurchase");
        post.setHeader("Content-Type", "application/json");
        post.setHeader("headerValue", "HeaderInformation");
        StringEntity entity = new StringEntity(json, "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);

        String resultado = EntityUtils.toString(response.getEntity(), "UTF-8");
        JSONObject jsonObject = new JSONObject(resultado);

        jsonObject.put("msgCode", jsonObject.getInt("ResponseCode"));
        jsonObject.put("descCode", String.valueOf(ResponseCodeGeoitd.obterPorCod(jsonObject.getInt("ResponseCode"))));

        return jsonObject;
    }

    private static JSONObject comunicarComPinpadGeoitd(JSONObject objectJson) throws IOException {
        String url = PropsService.getPropertyValue("urlAPIGeoitd");

        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpPost post = new HttpPost(url + "processFinancialPurchaseQuery");
        post.setHeader("Content-Type", "application/json");
        post.setHeader("headerValue", "HeaderInformation");

        StringEntity entity = new StringEntity(String.valueOf(objectJson), "UTF8");
        entity.setContentType(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        post.setEntity(entity);
        HttpResponse response = httpClient.execute(post);
        String resultado = EntityUtils.toString(response.getEntity(), "UTF-8");
        JSONObject jsonObject = new JSONObject(resultado);

        jsonObject.put("msgCode", jsonObject.getInt("ResponseCode"));
        jsonObject.put("descCode", String.valueOf(ResponseCodeGeoitd.obterPorCod(jsonObject.getInt("ResponseCode"))));

        return jsonObject;
    }

    public JSONObject posComunicacaoPinpadGeoitd() throws IOException {
        if(jsonPinpadGeoitd == null){
            jsonPinpadGeoitd.put("descCode", "Erro - Objeto Nullo");
            return jsonPinpadGeoitd;
        }

        if(resultLoadingPinpad.getInt("ResponseCode") == ResponseCodeGeoitd.RESPONSE10.getCod().intValue()){
            jsonPinpadGeoitd.put("STransactionId", sTransactionId);
            jsonPinpadGeoitd.put("TransactionId", transactionId);
            return comunicarComPinpadGeoitd(jsonPinpadGeoitd);
        }

        return jsonPinpadGeoitd;
    }
}

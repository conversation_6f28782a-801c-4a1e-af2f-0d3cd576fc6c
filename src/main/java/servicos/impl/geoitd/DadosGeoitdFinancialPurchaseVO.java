package servicos.impl.geoitd;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class DadosGeoitdFinancialPurchaseVO extends SuperVO {
    private String amount;
    private String merchant;
    private Integer quotas;
    private String plan;
    private String invoiceNumber;
    private String currency;
    private String taxRefund;
    private Boolean needToReadCard;
    private String bin;
    private String issuer;
    private String invoiceAmount;
    private String taxableAmount;
    private String terminalGroup;
    private String pinpadInitQuestion;
    private String tipAmount;
    private String ciNoCheckDigit;
    private String posId;
    private String systemId;
    private String branch;
    private String clientAppId;
    private String userId;
    private String transactionDateTimeYyyyMmDdHhMmSssss;

    public DadosGeoitdFinancialPurchaseVO() {

    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getMerchant() {
        return merchant;
    }

    public void setMerchant(String merchant) {
        this.merchant = merchant;
    }

    public Integer getQuotas() {
        return quotas;
    }

    public void setQuotas(Integer quotas) {
        this.quotas = quotas;
    }

    public String getPlan() {
        return (plan == null ? "0" : plan);
    }

    public void setPlan(String plan) {
        this.plan = plan;
    }

    public String getInvoiceNumber() {
        return (invoiceNumber == null ? "" : this.invoiceNumber);
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getTaxRefund() {
        return (taxRefund == null ? "0" : this.taxRefund); // maior que 0 para devolução de inmpostos.
    }

    public void setTaxRefund(String taxRefund) {
        this.taxRefund = taxRefund;
    }

    public Boolean getNeedToReadCard() {
        return (needToReadCard == null ? false : this.needToReadCard);
    }

    public void setNeedToReadCard(Boolean needToReadCard) {
        this.needToReadCard = needToReadCard;
    }

    public String getBin() {
        return bin;
    }

    public void setBin(String bin) {
        this.bin = bin;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public String getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(String invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getTaxableAmount() {
        return (taxableAmount == null ? "0" : this.taxableAmount);
    }

    public void setTaxableAmount(String taxableAmount) {
        this.taxableAmount = taxableAmount;
    }

    public String getTerminalGroup() {
        return terminalGroup;
    }

    public void setTerminalGroup(String terminalGroup) {
        this.terminalGroup = terminalGroup;
    }

    public String getPinpadInitQuestion() {
        return pinpadInitQuestion;
    }

    public void setPinpadInitQuestion(String pinpadInitQuestion) {
        this.pinpadInitQuestion = pinpadInitQuestion;
    }

    public String getTipAmount() {
        return tipAmount;
    }

    public void setTipAmount(String tipAmount) {
        this.tipAmount = tipAmount;
    }

    public String getCiNoCheckDigit() {
        return ciNoCheckDigit;
    }

    public void setCiNoCheckDigit(String ciNoCheckDigit) {
        this.ciNoCheckDigit = ciNoCheckDigit;
    }

    public String getPosId() {
        return (posId == null ? "" : this.posId);
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public String getSystemId() {
        return (systemId == null ? "" : this.systemId);
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getBranch() {
        return (branch == null ? "Prueba" : this.branch);
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getClientAppId() {
        return (clientAppId == null ? "Prueba" : this.clientAppId);
    }

    public void setClientAppId(String clientAppId) {
        this.clientAppId = clientAppId;
    }

    public String getUserId() {
        return (userId == null ? "" : this.userId);
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTransactionDateTimeYyyyMmDdHhMmSssss() {
        transactionDateTimeYyyyMmDdHhMmSssss = Calendario.getData("yyyyMMddHHmmssSSS");
        return transactionDateTimeYyyyMmDdHhMmSssss;
    }

    public void setTransactionDateTimeYyyyMmDdHhMmSssss(String transactionDateTimeYyyyMmDdHhMmSssss) {
        this.transactionDateTimeYyyyMmDdHhMmSssss = transactionDateTimeYyyyMmDdHhMmSssss;
    }
}



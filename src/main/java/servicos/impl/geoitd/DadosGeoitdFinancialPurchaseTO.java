package servicos.impl.geoitd;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.financeiro.MovPagamentoVO;
import org.json.JSONObject;

public class DadosGeoitdFinancialPurchaseTO extends SuperTO {
    private static  String sysId = "";
    private static  String posId = "";


    public static String montarJSON(MovPagamentoVO obj) {
        sysId = obj.getFormaPagamento().getSystemIdGeoitd();
        posId = obj.getFormaPagamento().getPosIDGeoitd();

        DadosGeoitdFinancialPurchaseVO dadosGeoitd = new DadosGeoitdFinancialPurchaseVO();

        JSONObject dadosGeoitdJSON = new JSONObject();
        dadosGeoitdJSON.put("Amount", String.valueOf(Formatador.formatarValorMonetarioSemMoeda(obj.getValorTotal())).replaceAll("[^0-9]",""));
        dadosGeoitdJSON.put("Merchant", "");

        if(!obj.getCredito() && obj.getOpcaoPagamentoCartaoDebito()) {
            obj.setNrParcelaCartaoCredito(1);
            dadosGeoitdJSON.put("Quotas", String.valueOf(obj.getNrParcelaCartaoCredito()));
        }else{
            dadosGeoitdJSON.put("Quotas", String.valueOf(obj.getNrParcelaCartaoCredito()));
        }

        dadosGeoitdJSON.put("Plan", dadosGeoitd.getPlan());
        dadosGeoitdJSON.put("InvoiceNumber", dadosGeoitd.getInvoiceNumber());
        if(String.valueOf(obj.getEmpresa().getLocale()).equals(LocaleEnum.ESPANHOL_LA.getLocale())){
            dadosGeoitdJSON.put("Currency", "858");
        }else{
            dadosGeoitdJSON.put("Currency", "840");
        }
        dadosGeoitdJSON.put("TaxRefund", dadosGeoitd.getTaxRefund());
        dadosGeoitdJSON.put("NeedToReadCard", dadosGeoitd.getNeedToReadCard());
        //dadosGeoitdJSON.put("Issuer", String.valueOf(obj.getOperadoraCartaoVO().getBandeirasGeoitd().getCodcIssuerGeoitd())); //TODO descomentar quando for em produção se necessario. Este trecho valida a bandeira do cartão com o banco.
        dadosGeoitdJSON.put("InvoiceAmount", String.valueOf(obj.getValorTotal()).replaceAll("[^0-9]",""));
        dadosGeoitdJSON.put("TaxableAmount", dadosGeoitd.getTaxableAmount());
        dadosGeoitdJSON.put("PosID", obj.getFormaPagamento().getPosIDGeoitd());
        dadosGeoitdJSON.put("SystemId", obj.getFormaPagamento().getSystemIdGeoitd());
        dadosGeoitdJSON.put("Branch", dadosGeoitd.getBranch());
        dadosGeoitdJSON.put("ClientAppId", dadosGeoitd.getClientAppId());
        dadosGeoitdJSON.put("UserId", dadosGeoitd.getUserId());
        dadosGeoitdJSON.put("TransactionDateTimeyyyyMMddHHmmssSSS", dadosGeoitd.getTransactionDateTimeYyyyMmDdHhMmSssss());

        return dadosGeoitdJSON.toString();

    }

    public static JSONObject toJsonPinpadGeoitd(JSONObject objJson){
        DadosGeoitdFinancialPurchaseVO dadosGeoitd = new DadosGeoitdFinancialPurchaseVO();

        JSONObject jsonPinpadGeoitd = new JSONObject();
        jsonPinpadGeoitd.put("PosID", posId);
        jsonPinpadGeoitd.put("SystemId",sysId);
        jsonPinpadGeoitd.put("Branch", dadosGeoitd.getBranch());
        jsonPinpadGeoitd.put("ClientAppId", dadosGeoitd.getClientAppId());
        jsonPinpadGeoitd.put("UserId", dadosGeoitd.getUserId());
        jsonPinpadGeoitd.put("TransactionDateTimeyyyyMMddHHmmssSSS", String.valueOf(dadosGeoitd.getTransactionDateTimeYyyyMmDdHhMmSssss()));
        jsonPinpadGeoitd.put("TransactionId",objJson.getLong("TransactionId"));
        jsonPinpadGeoitd.put("STransactionId", String.valueOf(objJson.getString("STransactionId")));
        jsonPinpadGeoitd.put("msgCode", objJson.getInt("msgCode"));

        return  jsonPinpadGeoitd;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getPosId() {
        return posId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }
}

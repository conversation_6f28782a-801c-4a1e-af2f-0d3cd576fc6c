package servicos.impl.conviteaulaexperimental;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoAgendaEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.enumeradores.TipoTurmaEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ReposicaoVO;
import negocio.comuns.basico.webservice.IntegracaoCadastrosWS;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalMobileTO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.FecharMetaVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.crm.PassivoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.crm.AberturaMeta;
import negocio.facade.jdbc.crm.Agenda;
import negocio.facade.jdbc.crm.FecharMeta;
import negocio.facade.jdbc.crm.fecharMetaDetalhado.FecharMetaDetalhado;
import negocio.facade.jdbc.crm.HistoricoContato;
import negocio.facade.jdbc.crm.Indicacao;
import negocio.facade.jdbc.crm.Indicado;
import negocio.facade.jdbc.crm.Passivo;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Turma;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.*;
import negocio.interfaces.crm.AberturaMetaInterfaceFacade;
import negocio.interfaces.crm.AgendaInterfaceFacade;
import negocio.interfaces.crm.FecharMetaDetalhadoInterfaceFacade;
import negocio.interfaces.crm.FecharMetaInterfaceFacade;
import negocio.interfaces.crm.HistoricoContatoInterfaceFacade;
import negocio.interfaces.crm.IndicacaoInterfaceFacade;
import negocio.interfaces.crm.IndicadoInterfaceFacade;
import negocio.interfaces.crm.PassivoInterfaceFacade;
import negocio.interfaces.plano.HorarioTurmaInterfaceFacade;
import negocio.interfaces.plano.TurmaInterfaceFacade;
import servicos.interfaces.ConviteAulaExperimentalServiceInterface;
import servicos.propriedades.PropsService;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.*;

import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.treino.UsuarioZW;

/**
 * Created by ulisses on 25/01/2016.
 */
public class ConviteAulaExperimentalService extends SuperEntidade implements ConviteAulaExperimentalServiceInterface{

    private IndicacaoInterfaceFacade indicacao;
    private IndicadoInterfaceFacade indicado;
    private EmpresaInterfaceFacade empresaDao;
    private PassivoInterfaceFacade passivo;
    private UsuarioInterfaceFacade usuarioInterfaceFacade;
    private UsuarioMovelInterfaceFacade usuarioMovelDao;
    private AberturaMetaInterfaceFacade aberturaMeta;
    private FecharMetaInterfaceFacade fecharMeta;
    private FecharMetaDetalhadoInterfaceFacade fecharMetaDetalhado;
    private HistoricoContatoInterfaceFacade historicoContato;
    private ClienteInterfaceFacade cliente;
    private TipoConviteAulaExperimentalModalidadeInterfaceFacade tipoConviteAulaExperimentalModalidade;
    private TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade tipoConviteAulaExperimentalModalidadeHorario;
    private AgendaInterfaceFacade agenda;
    private PessoaInterfaceFacade pessoa;
    private HorarioTurmaInterfaceFacade horarioTurma;
    private TurmaInterfaceFacade turma;
    private ReposicaoInterfaceFacade reposicao;
    private AulaDesmarcadaInterfaceFacade aulaDesmarcada;
    private ColaboradorInterfaceFacade colaborador;

    private static StringBuilder SQL_PADRAO_BASICO = new StringBuilder("select c.*, tp.codigo as codigoTipoConvite, tp.descricao, tp.vigenciaInicial,  \n")
                                                               .append("       tp.vigenciaFinal, tp.aulasAgendadasEmDiasSeguido, tp.quantidadeAulaExperimental, tp.empresa \n")
                                                               .append("from conviteAulaExperimental c \n")
                                                               .append("inner join tipoConviteAulaExperimental tp on tp.codigo = c.tipoConviteAulaExperimental");

    /*private static StringBuilder SQL_PADRAO_TODOS = new StringBuilder("select c.*, tp.codigo as codigoTipoConvite, tp.descricao, tp.vigenciaInicial, \n ")
                                                              .append("       tp.vigenciaFinal, tp.aulasAgendadasEmDiasSeguido, tp.quantidadeAulaExperimental, tp.empresa,\n")
                                                              .append("       mod.nome as nomeModalidade, tpModHor.horaInicial, tpModHor.horaFinal, tpModHor.diasSemana \n")
                                                              .append("from conviteAulaExperimental c \n")
                                                              .append("inner join tipoConviteAulaExperimental tp on tp.codigo = c.tipoConviteAulaExperimental \n")
                                                              .append("left join tipoConviteAulaExperimentalModalidade tpMod on tpMod.tipoConviteAulaExperimental = tp.codigo  \n")
                                                              .append("left join tipoConviteAulaExperimentalModalidadeHorario tpModHor on tpModHor.tipoConviteAulaExperimentalModalidade = tpMod.codigo \n")
                                                              .append("left join modalidade mod on mod.codigo = tpMod.modalidade \n");*/

    public ConviteAulaExperimentalService(Connection con) throws Exception {
        super(con);
    }

    public ConviteAulaExperimentalService() throws Exception {
        super();
    }

    public void gerarConviteViaWeb(OrigemSistemaEnum origemSistemaEnum,
                                   Integer codigoUsuarioConvidou,
                                   Integer codigoTipoConvite,
                                   Integer codigoIndicadoConvidado,
                                   Integer codigoPassivoConvidado,
                                   Integer codigoClienteConvidado,
                                   Integer codigoColaboradorReponsavelConvite)throws Exception{

        UsuarioVO usuarioConvidou = getUsuarioInterfaceFacade().consultarPorCodigo(codigoUsuarioConvidou, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ColaboradorVO colaboradorReponsavelConvite = getColaborador().consultarPorChavePrimaria(codigoColaboradorReponsavelConvite, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        ConviteAulaExperimentalVO conviteAulaExperimentalVO = validarConviteWeb(origemSistemaEnum, usuarioConvidou, codigoTipoConvite, codigoIndicadoConvidado, codigoPassivoConvidado, codigoClienteConvidado, colaboradorReponsavelConvite);
        incluirConvite(conviteAulaExperimentalVO);
        Integer codigoConvidado = 0;
        String nomeConvidado = "";
        String emailConvidado = "";
        if ((codigoIndicadoConvidado != null) && (codigoIndicadoConvidado >0)){
            codigoConvidado = codigoIndicadoConvidado;
            nomeConvidado = conviteAulaExperimentalVO.getIndicadoConvidado().getNomeIndicado();
            emailConvidado = conviteAulaExperimentalVO.getIndicadoConvidado().getEmail();
        } else if ((codigoPassivoConvidado != null) && (codigoPassivoConvidado >0)){
            codigoConvidado = codigoPassivoConvidado;
            nomeConvidado = conviteAulaExperimentalVO.getPassivoConvidado().getNome();
            emailConvidado = conviteAulaExperimentalVO.getPassivoConvidado().getEmail();
        } else if ((codigoClienteConvidado != null) && (codigoClienteConvidado >0)){
            codigoConvidado = codigoClienteConvidado;
            nomeConvidado = conviteAulaExperimentalVO.getClienteConvidado().getPessoa().getNome();
            emailConvidado = conviteAulaExperimentalVO.getClienteConvidado().getPessoa().getEmailCorrespondencia();
        }
        enviarEmailConvite(JSFUtilities.getFromSession("key").toString(), conviteAulaExperimentalVO, codigoConvidado, nomeConvidado, emailConvidado, new UsuarioMovelVO());
    }

    private ConviteAulaExperimentalVO validarConviteWeb(OrigemSistemaEnum origemSistemaEnum,
                                                        UsuarioVO usuarioConvidou,
                                                       Integer codigoTipoConvite,
                                                       Integer codigoIndicadoConvidado,
                                                       Integer codigoPassivoConvidado,
                                                       Integer codigoClienteConvidado,
                                                       ColaboradorVO colaboradorReponsavelConvite)throws Exception{

        verificarVigenciaELimiteConvitePorAluno(codigoTipoConvite, null);

        ConviteAulaExperimentalVO conviteAulaExperimentalVO = new ConviteAulaExperimentalVO();
        conviteAulaExperimentalVO.setTipoConviteAulaExperimentalVO(new TipoConviteAulaExperimentalVO());
        conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().setCodigo(codigoTipoConvite);
        conviteAulaExperimentalVO.setColaboradorResponsavelConvite(colaboradorReponsavelConvite);
        conviteAulaExperimentalVO.setUsuarioConvidou(usuarioConvidou);

        if ((codigoIndicadoConvidado != null) && (codigoIndicadoConvidado >0)){
            IndicadoVO indicadoVO = getIndicado().consultarPorCodigo(codigoIndicadoConvidado,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UtilReflection.objetoMaiorQueZero(indicadoVO, "getCodigo()")){
                if ((indicadoVO.getEmail() == null) || (indicadoVO.getEmail().equals(""))){
                    throw new ConsistirException("Operação não permitida. O indicado " + indicadoVO.getNomeIndicado().toUpperCase() + " não tem email cadastrado.");
                }
            }else{
                throw new ConsistirException("Operação não permitida. Não foi encontrado nenhum indicado com o código: " + codigoIndicadoConvidado);
            }
            conviteAulaExperimentalVO.setIndicadoConvidado(indicadoVO);
        }else if ((codigoPassivoConvidado != null) && (codigoPassivoConvidado >0)){
            PassivoVO passivoVO = getPassivo().consultarPorCodigo(codigoPassivoConvidado, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UtilReflection.objetoMaiorQueZero(passivoVO, "getCodigo()")){
                if ((passivoVO.getEmail() == null) || (passivoVO.getEmail().equals(""))){
                    throw new ConsistirException("Operação não permitida. O receptivo " + passivoVO.getNome().toUpperCase() + " não tem email cadastrado.");
                }
            }else{
                throw new ConsistirException("Operação não permitida. Não foi encontrado nenhum receptivo com o código: " + codigoPassivoConvidado);
            }
            conviteAulaExperimentalVO.setPassivoConvidado(passivoVO);

        }else if ((codigoClienteConvidado != null) && (codigoClienteConvidado >0)){
            ClienteVO clienteVO = getCliente().consultarDadosClienteUnicaConsulta(codigoClienteConvidado);
            if (UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")){
                if (clienteVO.getPessoa().getEmailVOs().size() <= 0){
                    throw new ConsistirException("Operação não permitida. O cliente " + clienteVO.getPessoa().getNome().toUpperCase() + " não tem email cadastrado.");
                }
            }else{
                throw new ConsistirException("Operação não permitida. Não foi encontrado nenhum cliente com o código: " + codigoClienteConvidado);
            }
            conviteAulaExperimentalVO.setClienteConvidado(clienteVO);

        }
        verificarSeConvidadoJaRecebeuConvite(conviteAulaExperimentalVO);
        return conviteAulaExperimentalVO;
    }

    public void gerarConviteViaWS(String key,
                                 OrigemSistemaEnum origemSistemaEnum,
                                 Integer codigoEmpresa,
                                 Integer codigoClienteConvidou,
                                 Integer codigoTipoConvite,
                                 String nomeConvidado,
                                 String telefoneConvidado,
                                 String emailConvidado)throws Exception{
        if ((codigoEmpresa == null) || (codigoEmpresa <= 0))
            throw new ConsistirException("O parâmetro codigoEmpresa é obrigatório.");
        if ((codigoClienteConvidou == null) || (codigoClienteConvidou <= 0))
            throw new ConsistirException("O parâmetro codigoClienteConvidou é obrigatório.");
        if ((codigoTipoConvite == null) || (codigoTipoConvite <= 0))
            throw new ConsistirException("O parâmetro codigoTipoConvite é obrigatório.");
        if ((nomeConvidado == null) || (nomeConvidado.equals("")))
            throw new ConsistirException("O parâmetro nomeConvidado é obrigatório.");
        if ((telefoneConvidado == null) || (telefoneConvidado.equals("")))
            throw new ConsistirException("O parâmetro telefoneConvidado é obrigatório.");
        if ((emailConvidado == null) || (emailConvidado.equals("")))
            throw new ConsistirException("O parâmetro emailConvidado é obrigatório.");
        // Valida a vigência do convite e se o aluno excedeu o limite de convites enviado.
        verificarVigenciaELimiteConvitePorAluno(codigoTipoConvite, codigoClienteConvidou);

        ClienteVO clienteVO = getCliente().consultarPorEmail(emailConvidado,0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        ConviteAulaExperimentalVO conviteAulaExperimentalVO = new ConviteAulaExperimentalVO();
        conviteAulaExperimentalVO.setTipoConviteAulaExperimentalVO(new TipoConviteAulaExperimentalVO());
        conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().setCodigo(codigoTipoConvite);

        Integer codigoConvidado = null;
        // O convidado só pode receber um convite por cada tipo de convite.
        if (UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")){
            verificarSeClienteJaRecebeuConvite(codigoTipoConvite, emailConvidado);
            codigoConvidado = clienteVO.getCodigo();
            conviteAulaExperimentalVO.setClienteConvidado(clienteVO);
            UsuarioVO colaboradorResponsavel = getUsuarioInterfaceFacade().consultarColaboradorResponsavelPeloCliente(codigoClienteConvidou, Uteis.NIVELMONTARDADOS_MINIMOS);
            conviteAulaExperimentalVO.setColaboradorResponsavelConvite(colaboradorResponsavel.getColaboradorVO());
        }else{
            verificarSeIndicadoJaRecebeuConvite(codigoTipoConvite,  emailConvidado);
        }

        IndicadoVO indicadoVO = null;
        try{
            con.setAutoCommit(false);
            UsuarioZW usuarioTreino = null;
            UsuarioMovelVO usuarioMovel = null;
            if (!UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")){
                indicadoVO = incluirIndicado(codigoEmpresa, codigoClienteConvidou,nomeConvidado,telefoneConvidado,emailConvidado,origemSistemaEnum);
                codigoConvidado = indicadoVO.getCodigo();
                conviteAulaExperimentalVO.setIndicadoConvidado(indicadoVO);
                conviteAulaExperimentalVO.setColaboradorResponsavelConvite(indicadoVO.getIndicacaoVO().getColaboradorResponsavel().getColaboradorVO());
                usuarioMovel = getUsuarioMovelDao().gerarUsuarioMovel(indicadoVO);
                usuarioTreino = usuarioMovel.toUsuarioTreino();
                
            }
            conviteAulaExperimentalVO.setClienteConvidou(new ClienteVO());
            conviteAulaExperimentalVO.getClienteConvidou().setCodigo(codigoClienteConvidou);

            incluirConvite(conviteAulaExperimentalVO);
            if(usuarioTreino != null){
                usuarioTreino.setConvite(conviteAulaExperimentalVO.getCodigo());
                String result = TreinoWSConsumer.sincronizarUsuario(key, usuarioTreino);
                if(!result.equals("ok")){
                    throw new Exception(result);
                }
            }
            con.commit();
            enviarEmailConvite(key, conviteAulaExperimentalVO, codigoConvidado, nomeConvidado, emailConvidado, usuarioMovel);
        }catch (Exception e){
            con.rollback();
            throw new ConsistirException("Erro ao gerar convite. Erro:" + e.getMessage());
        }finally {
            con.setAutoCommit(true);
        }
    }


    public void alterarDadosCadastraisConvidado(Integer codigoConvite,String nome, String telefoneRes, String telefoneCelular, String email)throws Exception{
        if ((codigoConvite == null) || (codigoConvite <= 0)){
            throw new ConsistirException("É necessário informar o codigoConvite.");
        }
        if ((nome == null) || (nome.trim().equals(""))){
            throw new ConsistirException("É necessário informar o nome.");
        }
        if ((email == null) || (email.trim().equals(""))){
            throw new ConsistirException("É necessário informar o email.");
        }
        boolean temFoneRes = ((telefoneRes != null) && (!telefoneRes.trim().equals("")));
        boolean temFoneCel = ((telefoneCelular != null) && (!telefoneCelular.trim().equals("")));
        if ((!temFoneCel) && (!temFoneRes)){
            throw new ConsistirException("É necessário informar um telefone.");
        }

        ConviteAulaExperimentalVO conviteAulaExperimentalVO = consultarPorCodigo(codigoConvite,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ClienteVO clienteVO = conviteAulaExperimentalVO.getClienteIndicadoOuPassivo();
        if (!UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")){
          clienteVO =  conviteAulaExperimentalVO.getClienteConvidado();
        }
        PessoaVO pessoaVO = null;
        if (UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")){
            clienteVO = getCliente().consultarPorCodigo(clienteVO.getCodigo(),false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            pessoaVO = clienteVO.getPessoa();
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getIndicadoConvidado().getCodigo()")){
            IndicadoVO indicadoVO = getIndicado().consultarPorCodigo(conviteAulaExperimentalVO.getIndicadoConvidado().getCodigo(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            indicadoVO.setNomeIndicado(nome);
            indicadoVO.setTelefoneIndicado(telefoneRes);
            indicadoVO.setTelefone(telefoneCelular);
            indicadoVO.setEmail(email);
            getIndicado().alterar(indicadoVO);
        }else if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getPassivoConvidado()().getCodigo()")){
            PassivoVO passivoVO = getPassivo().consultarPorChavePrimaria(conviteAulaExperimentalVO.getPassivoConvidado().getCodigo(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            passivoVO.setNome(nome);
            passivoVO.setTelefoneResidencial(telefoneRes);
            passivoVO.setTelefoneCelular(telefoneCelular);
            passivoVO.setEmail(email);
            getPassivo().alterar(passivoVO);
        }
        if (UtilReflection.objetoMaiorQueZero(pessoaVO, "getCodigo()")){
            alterarDadosPessoa(pessoaVO.getCodigo(), nome,telefoneRes,telefoneCelular,email);
        }

    }



    private void alterarDadosPessoa(Integer codigoPessoa,String nome, String telefoneRes, String telefoneCelular, String email)throws Exception{
        // alterar o nome da pessoa
        String sql = "update pesssoa set nome = ? where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setString(1, nome);
        pst.setInt(2, codigoPessoa);
        pst.execute();

        String sqlExcluir = "delete from telefone where tipoTelefone =? and pessoa = ?";
        String sqlIncluir = "insert into telefone(pessoa, tipoTelefone, numero) values (?,?,?) ";
        PreparedStatement pstIncluir = con.prepareStatement(sqlIncluir);
        PreparedStatement pstExcluir = con.prepareStatement(sqlIncluir);
        if ((telefoneRes != null) && (!telefoneRes.trim().equals(""))){
            // alterar o telefone residencial.
            pstExcluir.setString(1, TipoTelefoneEnum.RESIDENCIAL.getCodigo());
            pstExcluir.setInt(2, codigoPessoa);
            pstExcluir.execute();

            pstIncluir.setInt(1, codigoPessoa);
            pstIncluir.setString(2,TipoTelefoneEnum.RESIDENCIAL.getCodigo());
            pstIncluir.setString(3, telefoneRes);
            pstIncluir.execute();
        }
        if ((telefoneCelular != null) && (!telefoneCelular.trim().equals(""))){
            // alterar o telefone celular.
            pstExcluir.setString(1, TipoTelefoneEnum.CELULAR.getCodigo());
            pstExcluir.setInt(2, codigoPessoa);
            pstExcluir.execute();

            pstIncluir.setInt(1, codigoPessoa);
            pstIncluir.setString(2, TipoTelefoneEnum.CELULAR.getCodigo());
            pstIncluir.setString(3, telefoneCelular);
            pstIncluir.execute();
        }


    }

    private ConviteAulaExperimentalVO validarAgendarAulaExperimental(Integer codigoConvite, Date dataAula,HorarioTurmaVO horarioTurmaVO, TurmaVO turmaDestino)throws Exception{
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = null;
        if ((codigoConvite == null) || (codigoConvite <= 0)){
            throw new ConsistirException("O parâmetro codigoConvite dever ser informado.");
        }
        if (dataAula == null){
            throw new ConsistirException("O parâmetro dataAula dever ser informado.");
        }
        if (Calendario.menor(dataAula, Calendario.hoje())){
            throw new ConsistirException("Operação não permitida. A data da aula não pode ser menor que a data atual.");
        }
        conviteAulaExperimentalVO = consultarPorCodigo(codigoConvite, Uteis.NIVELMONTARDADOS_TODOS);
        if (!UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getCodigo()")){
            throw new ConsistirException("Operação não permitida. O convite foi excluído.");
        }
        if (Calendario.maior(Calendario.getDataComHoraZerada(Calendario.hoje()), Calendario.getDataComHoraZerada(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getVigenciaFinal()))){
            throw new ConsistirException("Operação não permitida. O convite já expirou.");
        }
        TimeZone tz = TimeZone.getTimeZone(empresaDao.obterTimeZoneDefault(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo()));
        Date agora = Calendario.hojeCalendar(tz).getTime();
        ReposicaoVO.validarMinutosAntecedenciaMarcarAula(turmaDestino, horarioTurmaVO, dataAula, agora);

        List<AgendaVO> listaAgenda = getAgenda().consultarPorConviteAulaExperimental(TipoTurmaEnum.TODOS,codigoConvite,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (listaAgenda.size() >= conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getQuantidadeAulaExperimental().intValue()){
            throw new ConsistirException("Operação não permitida. Limite de aula experimental excedido.");
        }
        if (aulaExperimentalJaFoiAgendada(codigoConvite, dataAula, horarioTurmaVO)){
            throw new ConsistirException("Operação não permitida. A aula experimental já foi agendada.");
        }
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        if  ((listaAgenda.size() > 0) && (conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().isAulasAgendadasEmDiasSeguido())){
            Date dataUltimoAgendamento = listaAgenda.get(listaAgenda.size() - 1).getDataAgendamento();
            Date dataComparar = Calendario.getDataComHoraZerada(Uteis.somarDias(dataUltimoAgendamento, 1));
            if (!(Calendario.igual(dataComparar, dataAula))){
                if (Calendario.maior(Calendario.getDataComHoraZerada(Calendario.hoje()), dataComparar)){
                    throw new ConsistirException("Operação não permitida. Devido a última aula ter sido agendada no dia " + sdf.format(dataUltimoAgendamento) +
                            " a próxima aula deveria ter sido agendada obrigatoriamente no dia " + sdf.format(dataComparar));
                }else{
                    throw new ConsistirException("Operação não permitida. Devido a última aula ter sido agendada no dia " + sdf.format(dataUltimoAgendamento) +
                            " a próxima aula deve ser agendada obrigatoriamente no dia " + sdf.format(dataComparar));
                }
            }
        }
        return conviteAulaExperimentalVO;
    }

    private boolean aulaExperimentalJaFoiAgendada(Integer codigoConvite, Date dataAula,HorarioTurmaVO horarioTurmaVO)throws  Exception{
        String sql = "select * from reposicao where dataReposicao = ? and horarioTurma = ? and conviteAulaExperimental = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setDate(1, Uteis.getDataJDBC(Calendario.getDataComHoraZerada(dataAula)));
        pst.setInt(2, horarioTurmaVO.getCodigo());
        pst.setInt(3, codigoConvite);
        ResultSet rs = pst.executeQuery();
        return rs.next();
    }

    public void desmarcarAulaExperimental(Integer codigoConvite, Date dataAula,Integer codigoHorarioTurma)throws Exception{
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = consultarPorCodigo(codigoConvite,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getCodigo()")){
            if (Calendario.maior(Calendario.getDataComHoraZerada(Calendario.hoje()), Calendario.getDataComHoraZerada(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getVigenciaFinal()))){
                throw new ConsistirException("Operação não permitida. O convite tinha validade somente até a data " + sdf.format(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getVigenciaFinal()));
            }
        }else{
            throw new ConsistirException("Operação não permitida. O convite foi excluído pelo convidante.");
        }
        try{
            con.setAutoCommit(false);

            HorarioTurmaVO horarioTurmaVO = getHorarioTurma().consultarPorCodigo(codigoHorarioTurma,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            TurmaVO turmaVO = getTurma().consultarPorChavePrimaria(horarioTurmaVO.getTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            AgendaVO agendaVO = getAgenda().consultarPorConvite(codigoConvite, turmaVO.getModalidade().getCodigo(), dataAula,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!UtilReflection.objetoMaiorQueZero(agendaVO, "getCodigo()")){
                throw new ConsistirException("Operação não permitida. Não foi encontrado nenhum agendamento de aula para a data " + sdf.format(dataAula));
            }
            getHistoricoContato().excluirHistoricoContatoDeConvite(codigoConvite, agendaVO.getCodigo());
            getFecharMetaDetalhado().excluirFecharMetaDetalhadoDoAgendamentoAulaExperimental(agendaVO,codigoConvite);
            getReposicao().excluirReposicaoDaAulaExperimental(codigoHorarioTurma,codigoConvite,dataAula);
            getAgenda().excluirAgendaConviteAulaExperimental(codigoConvite,turmaVO.getModalidade().getCodigo(),dataAula);

            con.commit();
        }catch (Exception e){
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    public void agendarAulaExperimental(Integer codigoConvite, Date dataAula,Integer codigoHorarioTurma)throws Exception{
        if ((codigoHorarioTurma == null) || (codigoHorarioTurma <= 0)){
            throw new ConsistirException("O parâmetro codigoHorarioTurma dever ser informado.");
        }
        HorarioTurmaVO horarioTurmaVO = getHorarioTurma().consultarPorCodigo(codigoHorarioTurma,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        TurmaVO turmaVO = getTurma().consultarPorChavePrimaria(horarioTurmaVO.getTurma(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = validarAgendarAulaExperimental(codigoConvite,dataAula,horarioTurmaVO, turmaVO);

        int totalMatriculados = getHorarioTurma().nrAlunosMatriculadosRenovacaoRematricula(horarioTurmaVO, dataAula, null);
        Integer totalDesmacados = getAulaDesmarcada().consultarTotalAulasDesmarcadas(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo(),horarioTurmaVO.getCodigo(),dataAula, null);
        Integer totalReposicao = getReposicao().consultarTotalReposicao(horarioTurmaVO.getCodigo(), dataAula, null);
        if (((totalMatriculados + totalReposicao) - totalDesmacados)  >= horarioTurmaVO.getNrMaximoAluno()){
            throw new ConsistirException("Operação não permitida. Não há vaga disponível para a turma informada.");
        }
        UsuarioVO usuarioVO = getUsuarioInterfaceFacade().consultarColaboradorResponsavelPeloCliente(conviteAulaExperimentalVO.getClienteConvidou().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        ClienteVO clienteConvite = getClienteConvite(conviteAulaExperimentalVO);
        if (clienteConvite != null){

            try{
                con.setAutoCommit(false);

                AgendaVO agendaVO = incluirAgendaAulaExperimental(conviteAulaExperimentalVO,usuarioVO, dataAula, horarioTurmaVO,turmaVO);
                incluirReposicaoParaAulaExperimental(conviteAulaExperimentalVO,clienteConvite,dataAula,horarioTurmaVO);
                incluirHistoricoContatoCRM(agendaVO,conviteAulaExperimentalVO,usuarioVO);
                incluirFecharMetaDetalhadoParaAgendamentoCRM(dataAula, agendaVO, conviteAulaExperimentalVO, usuarioVO);

                con.commit();
            }catch (Exception e){
                con.rollback();
                throw e;
            }finally {
                con.setAutoCommit(true);
            }

        }else{
            throw new ConsistirException("Não foi possível econtrar o cliente do convite.");
        }
    }

    private void incluirFecharMetaDetalhadoParaAgendamentoCRM(Date dataAula, AgendaVO agendaVO, ConviteAulaExperimentalVO conviteAulaExperimentalVO, UsuarioVO colaboradorResponsavel)throws Exception{
        Integer codigoEmpresa = conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo();

        AberturaMetaVO aberturaMetaVO = getAberturaMeta().consultar(colaboradorResponsavel.getCodigo(), codigoEmpresa, dataAula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UtilReflection.objetoMaiorQueZero(aberturaMetaVO, "getCodigo()")){
            FecharMetaVO fecharMetaAgendamento = getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.AGENDAMENTO.getSigla(), aberturaMetaVO.getDia() , colaboradorResponsavel.getCodigo(), false, codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            incluirFecharMetaDetalhado(fecharMetaAgendamento, agendaVO, conviteAulaExperimentalVO, colaboradorResponsavel);
            PreparedStatement pst = con.prepareStatement("update fecharMeta set meta = meta +  1 where codigo = " + fecharMetaAgendamento.getCodigo());
            pst.execute();

            FecharMetaVO fecharMetaConversaoAgendamento = getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.CONVERSAO_AGENDADOS.getSigla(), aberturaMetaVO.getDia() , colaboradorResponsavel.getCodigo(), false, codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UtilReflection.objetoMaiorQueZero(fecharMetaConversaoAgendamento, "getCodigo()")){
                incluirFecharMetaDetalhado(fecharMetaConversaoAgendamento, agendaVO, conviteAulaExperimentalVO, colaboradorResponsavel);
            }
        }
    }

    private void incluirFecharMetaDetalhado(FecharMetaVO fecharMetaVO, AgendaVO agendaVO, ConviteAulaExperimentalVO conviteAulaExperimentalVO, UsuarioVO colaboradorResponsavel)throws Exception{
        FecharMetaDetalhadoVO obj = new FecharMetaDetalhadoVO();
        obj.setFecharMeta(fecharMetaVO);
        obj.setObteveSucesso(false);
        if (conviteAulaExperimentalVO.conviteDeIndicado()){
            obj.setIndicado(conviteAulaExperimentalVO.getIndicadoConvidado());
        }else if (conviteAulaExperimentalVO.conviteDePassivo()){
            obj.setPassivo(conviteAulaExperimentalVO.getPassivoConvidado());
        }else if (conviteAulaExperimentalVO.conviteDeCliente()){
            obj.setCliente(conviteAulaExperimentalVO.getClienteConvidado());
        }
        obj.setCodigoOrigem(agendaVO.getCodigo());
        obj.setOrigem("AGENDA");
        obj.setPesoRisco(0);
        obj.setSessoesFinais(0);
        obj.setVendaAvulsa(0);
        obj.setDiasSemAgendamento(0);
        obj.setRepescagem(false);
        obj.setTeveContato(false);
        obj.setConviteAulaExperimentalVO(conviteAulaExperimentalVO);
        getFecharMetaDetalhado().incluir(obj);

        if (conviteAulaExperimentalVO.conviteDeIndicado()){
            // alterar a meta do dia em que o indicado foi cadastrado para o mesmo não aparecer na lista de "Indicados sem Contato"
            StringBuilder sql = new StringBuilder();
            sql.append(" update fecharMetaDetalhado set obteveSucesso = true \n");
            sql.append(" from \n");
            sql.append("       (select det.codigo \n");
            sql.append("        from fecharMetaDetalhado det\n");
            sql.append("        inner join fecharMeta fm on fm.codigo = det.fecharMeta \n");
            sql.append("        inner join aberturaMeta am on am.codigo = fm.aberturaMeta\n");
            sql.append("        where am.colaboradorResponsavel = ? \n");
            sql.append("              and det.indicado = ? and origem = ?) as sql \n");
            sql.append("where sql.codigo = fecharMetaDetalhado.codigo");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            pst.setInt(1, colaboradorResponsavel.getCodigo());
            pst.setInt(2, conviteAulaExperimentalVO.getIndicadoConvidado().getCodigo());
            pst.setString(3, "INDICADO");
            pst.execute();
        }
    }

    private void incluirHistoricoContatoCRM(AgendaVO agendaVO, ConviteAulaExperimentalVO conviteAulaExperimentalVO, UsuarioVO usuarioVO)throws Exception{
        HistoricoContatoVO historicoContatoVO = new HistoricoContatoVO();
        historicoContatoVO.setResultado(agendaVO.qualResultadoAgendamento());
        if (conviteAulaExperimentalVO.conviteDeCliente()){
            historicoContatoVO.setClienteVO(conviteAulaExperimentalVO.getClienteConvidado());
        }else if (conviteAulaExperimentalVO.conviteDeIndicado()){
            historicoContatoVO.setIndicadoVO(conviteAulaExperimentalVO.getIndicadoConvidado());
        }else if (conviteAulaExperimentalVO.conviteDePassivo()){
            historicoContatoVO.setPassivoVO(conviteAulaExperimentalVO.getPassivoConvidado());
        }
        historicoContatoVO.setFase(FasesCRMEnum.INDICACOES.getSigla());
        historicoContatoVO.setResponsavelCadastro(usuarioVO);
        historicoContatoVO.setDia(Calendario.hoje());
        historicoContatoVO.setTipoContato("AP");
        historicoContatoVO.setAgendaVO(agendaVO);
        historicoContatoVO.setConviteAulaExperimentalVO(conviteAulaExperimentalVO);
        getHistoricoContato().incluirSemCommitSemAtualizarSintetico(historicoContatoVO);
    }



    private AgendaVO incluirAgendaAulaExperimental(ConviteAulaExperimentalVO conviteAulaExperimentalVO, UsuarioVO usuarioVO, Date dataAula,HorarioTurmaVO horarioTurmaVO, TurmaVO turmaVO)throws Exception{
        AgendaVO agendaVO = new AgendaVO();
        agendaVO.setTipoAgendamento(TipoAgendaEnum.AULA_EXPERIMENTAL.getId());
        agendaVO.setDataAgendamento(dataAula);
        agendaVO.setDataLancamento(Calendario.hoje());
        agendaVO.setHora(horarioTurmaVO.getHoraInicial().split(":")[0]);
        agendaVO.setMinuto(horarioTurmaVO.getHoraInicial().split(":")[1]);
        agendaVO.setModalidade(turmaVO.getModalidade());
        if (conviteAulaExperimentalVO.conviteDeCliente()){
            agendaVO.setCliente(conviteAulaExperimentalVO.getClienteConvidado());
        }else if (conviteAulaExperimentalVO.conviteDeIndicado()){
            agendaVO.setIndicado(conviteAulaExperimentalVO.getIndicadoConvidado());
        }else if (conviteAulaExperimentalVO.conviteDePassivo()){
            agendaVO.setPassivo(conviteAulaExperimentalVO.getPassivoConvidado());
        }
        agendaVO.setColaboradorResponsavel(usuarioVO);
        agendaVO.setResponsavelCadastro(getUsuarioInterfaceFacade().getUsuarioRecorrencia());
        agendaVO.setEmpresa(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO().getCodigo());
        agendaVO.setConviteAulaExperimentalVO(conviteAulaExperimentalVO);

        getAgenda().incluirSemValidacao(agendaVO);
        return agendaVO;
    }

    private void incluirReposicaoParaAulaExperimental(ConviteAulaExperimentalVO conviteAulaExperimentalVO, ClienteVO clienteVO, Date dataAula, HorarioTurmaVO horarioTurmaVO)throws Exception{

        String sql = "insert into reposicao(cliente, dataReposicao, horarioTurma, turmaOrigem, turmaDestino, dataLancamento, horarioTurmaOrigem, dataOrigem, usuario, origemSistema, conviteAulaExperimental) values (?,?,?,?,?,?,?,?,?,?, ?)";
        PreparedStatement pst = con.prepareStatement(sql);
        int i=1;
        pst.setInt(i++, clienteVO.getCodigo());
        pst.setDate(i++, Uteis.getDataJDBC(dataAula));
        pst.setInt(i++, horarioTurmaVO.getCodigo());
        pst.setInt(i++, horarioTurmaVO.getTurma());
        pst.setInt(i++, horarioTurmaVO.getTurma());
        pst.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.setInt(i++, horarioTurmaVO.getCodigo());
        pst.setDate(i++, Uteis.getDataJDBC(dataAula));
        pst.setInt(i++, getUsuarioInterfaceFacade().getUsuarioRecorrencia().getCodigo());
        pst.setInt(i++, OrigemSistemaEnum.APP_TREINO.getCodigo());
        pst.setInt(i++, conviteAulaExperimentalVO.getCodigo());
        pst.execute();
    }

    private ClienteVO getClienteConvite(ConviteAulaExperimentalVO conviteAulaExperimentalVO)throws Exception{
        ClienteVO clienteVO = null;
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getClienteConvidado().getCodigo()")) {
            clienteVO = getCliente().consultarPorChavePrimaria(conviteAulaExperimentalVO.getClienteConvidado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }else{
            if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getClienteIndicadoOuPassivo().getCodigo()")) {
                return getCliente().consultarPorChavePrimaria(conviteAulaExperimentalVO.getClienteIndicadoOuPassivo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }else{
                if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getIndicadoConvidado().getCodigo()")){
                    // incluir Indicado como ClienteVisitante
                    IndicadoVO indicadoVO = getIndicado().consultarPorChavePrimaria(conviteAulaExperimentalVO.getIndicadoConvidado().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteVO = getCliente().incluirClienteViaWebService(conviteAulaExperimentalVO.getColaboradorResponsavelConvite(),
                                                                         conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO(),
                                                                         indicadoVO.getNomeIndicado(),indicadoVO.getEmail(),indicadoVO.getTelefoneIndicado(), indicadoVO.getTelefone());
                    alterarClienteDoConvite(conviteAulaExperimentalVO.getCodigo(), clienteVO);
                }else if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getPassivoConvidado().getCodigo()")){
                    // incluir Passivo como ClienteVisitante
                    PassivoVO passivoVO = getPassivo().consultarPorChavePrimaria(conviteAulaExperimentalVO.getPassivoConvidado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteVO = getCliente().incluirClienteViaWebService(conviteAulaExperimentalVO.getColaboradorResponsavelConvite(),
                                                                         conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getEmpresaVO(),
                                                                         passivoVO.getNome(),passivoVO.getEmail(),passivoVO.getTelefoneResidencial(), passivoVO.getTelefoneCelular());
                    alterarClienteDoConvite(conviteAulaExperimentalVO.getCodigo(), clienteVO);
                }
            }
        }
        return clienteVO;
    }

    private void alterarClienteDoConvite(Integer codigoConvite, ClienteVO clienteVO)throws Exception{
        String sql = "update conviteAulaExperimental set clienteIndicadoOuPassivo = ? where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, clienteVO.getCodigo());
        pst.setInt(2, codigoConvite);
        pst.execute();
    }


    private static ConviteAulaExperimentalVO montarDadosBasico(ResultSet rs)throws Exception{
        ConviteAulaExperimentalVO obj = new ConviteAulaExperimentalVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setIndicadoConvidado(new IndicadoVO());
        obj.getIndicadoConvidado().setCodigo(rs.getInt("indicadoConvidado"));
        obj.setPassivoConvidado(new PassivoVO());
        obj.getPassivoConvidado().setCodigo(rs.getInt("passivoConvidado"));
        obj.setClienteConvidado(new ClienteVO());
        obj.getClienteConvidado().setCodigo(rs.getInt("clienteConvidado"));
        obj.setClienteConvidou(new ClienteVO());
        obj.getClienteConvidou().setCodigo(rs.getInt("clienteConvidou"));
        obj.setUsuarioConvidou(new UsuarioVO());
        obj.getUsuarioConvidou().setCodigo(rs.getInt("usuarioConvidou"));
        obj.setColaboradorResponsavelConvite(new ColaboradorVO());
        obj.getColaboradorResponsavelConvite().setCodigo(rs.getInt("colaboradorResponsavelConvite"));
        obj.setDataValidacaoConvidado(rs.getTimestamp("dataValidacaoConvidado"));
        obj.setClienteIndicadoOuPassivo(new ClienteVO());
        obj.getClienteIndicadoOuPassivo().setCodigo(rs.getInt("clienteIndicadoOuPassivo"));
        obj.setDataLancamento(rs.getTimestamp("dataLancamento"));

        TipoConviteAulaExperimentalVO tipoConvite = new TipoConviteAulaExperimentalVO();
        tipoConvite.setCodigo(rs.getInt("tipoConviteAulaExperimental"));
        tipoConvite.setDescricao(rs.getString("descricao"));
        tipoConvite.setVigenciaInicial(rs.getDate("vigenciaFinal"));
        tipoConvite.setVigenciaFinal(rs.getDate("vigenciaFinal"));
        tipoConvite.setAulasAgendadasEmDiasSeguido(rs.getBoolean("aulasAgendadasEmDiasSeguido"));
        tipoConvite.setQuantidadeAulaExperimental(rs.getInt("quantidadeAulaExperimental"));
        tipoConvite.setEmpresaVO(new EmpresaVO());
        tipoConvite.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.setTipoConviteAulaExperimentalVO(tipoConvite);

        return obj;
    }

   private ConviteAulaExperimentalVO montarDados(ResultSet rs, int nivelMontarDados)throws Exception{
        ConviteAulaExperimentalVO obj = montarDadosBasico(rs);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS){
            List<TipoConviteAulaExperimentalModalidadeVO> listaModalidade = getTipoConviteAulaExperimentalModalidade().consultar(obj.getTipoConviteAulaExperimentalVO(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (TipoConviteAulaExperimentalModalidadeVO modalidade: listaModalidade){
                modalidade.setListaHorarioAux(getTipoConviteAulaExperimentalModalidadeHorario().consultar(modalidade, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            obj.getTipoConviteAulaExperimentalVO().setListaModalidade(listaModalidade);
            return obj;
        }
        return obj;
    }

    public ConviteAulaExperimentalVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(SQL_PADRAO_BASICO).append(" where c.codigo = ?");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigo);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
          return montarDados(rs, nivelMontarDados);
        return null;
    }


    public ConviteAulaExperimentalVO validarConvite(Integer codigoConvite)throws Exception{
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = consultarPorCodigo(codigoConvite,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getCodigo()")){
            if (Calendario.maior(Calendario.getDataComHoraZerada(Calendario.hoje()), Calendario.getDataComHoraZerada(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getVigenciaFinal()))){
                throw new ConsistirException("Operação não permitida. O convite tinha validade somente até a data " + sdf.format(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getVigenciaFinal()));
            }
        }else{
            throw new ConsistirException("Operação não permitida. O convite foi excluído pelo convidante.");
        }
        String sql = "update conviteAulaExperimental set dataValidacaoConvidado = current_timestamp where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoConvite);
        pst.execute();
        return conviteAulaExperimentalVO;
    }

    public void excluirConvite(Integer codigoConvite, Integer codigoCliente)throws Exception{
        ConviteAulaExperimentalVO conviteAulaExperimentalVO = consultarPorCodigo(codigoConvite,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getCodigo()")){
            if (conviteAulaExperimentalVO.getDataValidacaoConvidado() != null){
                throw new ConsistirException("Operação não permitida. O convite já foi validado pelo convidado.");
            }
        }else{
            throw new ConsistirException("Operação não permitida. O convite já foi excluído pelo convidante.");
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getIndicadoConvidado().getCodigo()")){
            IndicadoVO indicadoVO = getIndicado().consultarPorChavePrimaria(conviteAulaExperimentalVO.getIndicadoConvidado().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            HistoricoContatoVO historicoContatoVO = getHistoricoContato().consultarHistoricoContatoPorCodigoIndicado(indicadoVO.getCodigo(),false,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UtilReflection.objetoMaiorQueZero(historicoContatoVO, "getCodigo()")){
                throw new ConsistirException("Operação não permitida. Um consultor da empresa já realizou contato com o Convidado.");
            }
            Integer totalConviteIndicado = consultarTotalConvitePorIndicado(indicadoVO.getCodigo(), codigoConvite);
            try{
                con.setAutoCommit(false);
                excluir(codigoConvite);
                if ((totalConviteIndicado <= 0) && (indicadoVO.getOrigemSistemaEnum() == OrigemSistemaEnum.APP_TREINO)){
                    getFecharMetaDetalhado().excluirPorIndicado(indicadoVO.getCodigo());
                    getIndicado().excluir(indicadoVO);
                    getIndicacao().excluir(indicadoVO.getIndicacaoVO());
                }

                con.commit();
            }catch (Exception e){
                con.rollback();
                throw e;
            }finally {
                con.setAutoCommit(true);
            }
        }else{
            excluir(codigoConvite);
        }
    }

    private Integer consultarTotalConvitePorIndicado(Integer codigoIndicado, Integer codigoConviteDesconsiderar)throws Exception{
        String sql = "select count(*) as total from conviteAulaExperimental where indicadoConvidado = ? and codigo <> ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoIndicado);
        pst.setInt(2, codigoConviteDesconsiderar);
        ResultSet rs = pst.executeQuery();
        if (rs.next())
            return rs.getInt("total");
        return 0;
    }

    public void excluir(Integer codigo)throws Exception{
        String sql = "delete from conviteAulaExperimental where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigo);
        pst.execute();
    }

    private AberturaMetaVO consultarAberturaMeta(UsuarioVO colaboradorResponsavel, Integer codigoEmpresa)throws Exception{
        AberturaMetaVO aberturaMetaVO = getAberturaMeta().consultar(colaboradorResponsavel.getCodigo(), codigoEmpresa, Calendario.hoje(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!(UtilReflection.objetoMaiorQueZero(aberturaMetaVO, "getCodigo()"))){
            // se não tiver meta aberta para o dia, então pegar a última meta aberta do usuário responsavel.
            aberturaMetaVO = getAberturaMeta().consultarUltimaAberturaMeta(colaboradorResponsavel.getCodigo(), codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!(UtilReflection.objetoMaiorQueZero(aberturaMetaVO, "getCodigo()"))){
                throw new ConsistirException("Operação não permitida. O consultor '"+ colaboradorResponsavel.getUsername() + "' não tem nenhuma meta aberta no CRM.");
            }
        }
        return aberturaMetaVO;
    }

    private IndicadoVO incluirIndicado(Integer codigoEmpresa,
                                    Integer codigoCliente,
                                    String nomeIndicado,
                                    String telefoneIndicado,
                                    String emailIndicado,
                                    OrigemSistemaEnum origemSistemaEnum)throws Exception{

        UsuarioVO colaboradorResponsavel = getUsuarioInterfaceFacade().consultarColaboradorResponsavelPeloCliente(codigoCliente, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (!UtilReflection.objetoMaiorQueZero(colaboradorResponsavel, "getCodigo()")){
            throw new ConsistirException("Operação não permitida. O aluno não tem vinculo com nenhum consultor.");
        }
        AberturaMetaVO aberturaMetaVO = consultarAberturaMeta(colaboradorResponsavel,codigoEmpresa);

        IndicadoVO indicadoVO = getIndicado().consultarPorEmail(codigoEmpresa, emailIndicado, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        FecharMetaVO fecharMetaVO = getFecharMeta().consultarPorIdentificadorMetaPorDiaPorColaborador(FasesCRMEnum.INDICACOES.getSigla(), aberturaMetaVO.getDia() , colaboradorResponsavel.getCodigo(), false, codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!(UtilReflection.objetoMaiorQueZero(fecharMetaVO, "getCodigo()"))){
            throw new ConsistirException("Operação não permitida. O consultor '"+ colaboradorResponsavel.getUsername() + "' não tem meta para 'INDICADO'.");
        }
        if (UtilReflection.objetoMaiorQueZero(indicadoVO, "getCodigo()")){
            indicadoVO.getIndicacaoVO().setColaboradorResponsavel(colaboradorResponsavel);
            getFecharMeta().inserirFecharMetaDetalhadoIndicado(fecharMetaVO, indicadoVO.getCodigo());
        }else{
            // incluir indicação
            IndicacaoVO indicacaoVO = new IndicacaoVO();
            indicacaoVO.setClienteQueIndicou(new ClienteVO());
            indicacaoVO.getClienteQueIndicou().setCodigo(codigoCliente);
            indicacaoVO.setColaboradorQueIndicou(new ColaboradorVO());
            indicacaoVO.setColaboradorResponsavel(colaboradorResponsavel);
            indicacaoVO.setDiaAbertura(aberturaMetaVO.getDia());
            indicacaoVO.setDia(aberturaMetaVO.getDia());
            indicacaoVO.setObservacao("INDICADO ATRAVÉS DO CONVITE DE AULA EXPERIMENTAL");
            getIndicacao().incluirSomenteIndicacao(indicacaoVO);

            // incluir indicado
            indicadoVO = new IndicadoVO();
            indicadoVO.setNomeIndicado(nomeIndicado);
            indicadoVO.setTelefone(telefoneIndicado);
            indicadoVO.setEmail(emailIndicado);
            indicadoVO.setIndicacaoVO(indicacaoVO);
            indicadoVO.setEmpresaVO(new EmpresaVO());
            indicadoVO.getEmpresaVO().setCodigo(codigoEmpresa);
            indicadoVO.setOrigemSistemaEnum(origemSistemaEnum);

            getIndicado().incluirIndicadoMeta(indicadoVO, fecharMetaVO);
        }
        return indicadoVO;
    }


    private void incluirConvite(ConviteAulaExperimentalVO conviteAulaExperimentalVO)throws Exception{
        conviteAulaExperimentalVO.getValidarDados();
        StringBuilder sql = new StringBuilder();
        sql.append("insert into ConviteAulaExperimental (tipoConviteAulaExperimental, indicadoConvidado, passivoConvidado,");
        sql.append("clienteConvidado, usuarioConvidou, clienteConvidou, colaboradorResponsavelConvite, dataLancamento)");
        sql.append("values(?,?,?,?,?,?,?,?)");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getCodigo());
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getIndicadoConvidado().getCodigo()")){
            pst.setInt(2, conviteAulaExperimentalVO.getIndicadoConvidado().getCodigo());
        }else{
            pst.setNull(2, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getPassivoConvidado().getCodigo()")){
            pst.setInt(3, conviteAulaExperimentalVO.getPassivoConvidado().getCodigo());
        }else{
            pst.setNull(3, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getClienteConvidado().getCodigo()")){
            pst.setInt(4, conviteAulaExperimentalVO.getClienteConvidado().getCodigo());
        }else{
            pst.setNull(4, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getUsuarioConvidou().getCodigo()")){
            pst.setInt(5, conviteAulaExperimentalVO.getUsuarioConvidou().getCodigo());
        }else{
            pst.setNull(5, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getClienteConvidou().getCodigo()")){
            pst.setInt(6, conviteAulaExperimentalVO.getClienteConvidou().getCodigo());
        }else{
            pst.setNull(6, Types.NULL);
        }
        if (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getColaboradorResponsavelConvite().getCodigo()")){
            pst.setInt(7, conviteAulaExperimentalVO.getColaboradorResponsavelConvite().getCodigo());
        }else{
            pst.setNull(7, Types.NULL);
        }
        pst.setTimestamp(8, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        pst.execute();
        conviteAulaExperimentalVO.setCodigo(obterUltimoCodigoGeradoTabela(con, "ConviteAulaExperimental"));
    }

    public static int obterUltimoCodigoGeradoTabela(Connection con, String nomeTabela) throws Exception {
        String csCodigoGerado = "SELECT last_value FROM " + nomeTabela + "_codigo_seq";
        Statement stmt = con.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getInt(1));
    }



    private void enviarEmailConvite(String key, ConviteAulaExperimentalVO conviteAulaExperimentalVO, Integer codigoConvidado, 
            String nomeConvidado, String emailConvidado, UsuarioMovelVO usuarioMovel)throws Exception{
        DaoAuxiliar.retornarAcessoControle(key).enviarEmail(
                new String[]{emailConvidado}, "Convite Aula Experimental",
                gerarCorpoEmailConvite(key,conviteAulaExperimentalVO,codigoConvidado,nomeConvidado,emailConvidado, usuarioMovel));
    }

    private StringBuilder gerarCorpoEmailConvite(String key, ConviteAulaExperimentalVO conviteAulaExperimentalVO, 
            Integer codigoConvidado,String nomeConvidado, String emailConvidado, UsuarioMovelVO usuarioMovel) throws Exception {
        TipoConviteAulaExperimentalMobileTO tipoConviteAulaExperimentalMobileTO = consultarTipoConvite(conviteAulaExperimentalVO);

        final String expiracao = Uteis.getDataAplicandoFormatacao(tipoConviteAulaExperimentalMobileTO.getVigenciaFinal(), "yyyyMMdd");
        final String hash = Uteis.encriptar(String.format("%s|%s|%s|%s|%s", new Object[]{key,conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getCodigo(),
                conviteAulaExperimentalVO.getCodigo(),codigoConvidado, expiracao}), IntegracaoCadastrosWS.CHAVE_URL_MOBILE);
        final String urlAtivacaoIOS = "pactotreino://?token=" + hash;
        final String urlAtivacaoAndroid = "http://www.pactotreino.com.br/?token=" + hash;

        String[] imagens = new String[]{"baixar_androidP4CT0.png", "baixar_appleP4CT0.png", "entrar_androidP4CT0.png", "entrar_appleP4CT0.png", "topo_emailP4CT0.png", "setaP4CT0.png"};
        Map<String, File> mapaImagem = new HashMap<String, File>();
        for(String imagem : imagens){
            File arqImg = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/"+imagem).toURI());
            mapaImagem.put(imagem, arqImg);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        String validadeConvite = "V&aacute;lido at&eacute; " + sdf.format(tipoConviteAulaExperimentalMobileTO.getVigenciaFinal());

        UteisEmail.criarImagensEmailTreino(mapaImagem);
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPactoTreinoConviteAulaExperimental.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        final String aux = texto.toString()
                .replaceAll("#CHAVE_ACADEMIA ", key)
                .replaceAll("#NEW_USER", nomeConvidado)
                .replaceAll("#USER_NAME", usuarioMovel.getNome())
                .replaceAll("#SENHA", usuarioMovel.getSenhaAnterior())
                .replaceAll("#INFORMACOESCONVITE", montarDescricaoParabensEmailConvite(tipoConviteAulaExperimentalMobileTO))
                .replaceAll("#INFORMACOESVIGENCIA", validadeConvite)
                .replaceAll("#INFORMACOESEMPRESA", tipoConviteAulaExperimentalMobileTO.getNomeEmpresa())
                .replaceAll("#INFORMACOESENDERECO", montarDescricaoEnderecoConvite(tipoConviteAulaExperimentalMobileTO))
                .replaceAll("#INFOPHONE", montarDescricaoTelefoneConvite(tipoConviteAulaExperimentalMobileTO))
                 .replaceAll("#URL_PLAY", PropsService.getPropertyValue(PropsService.urlTreinoGooglePlay))
                 .replaceAll("#URL_ITUNES", PropsService.getPropertyValue(PropsService.urlTreinoAppleStore))
                .replaceAll("#URL_IOS", urlAtivacaoIOS)
                .replaceAll("#URL_ANDROID", urlAtivacaoAndroid);
        return new StringBuilder(aux);


    }

    public String montarDescricaoParabensEmailConvite(TipoConviteAulaExperimentalMobileTO tipoConviteAulaExperimentalMobileTO)throws Exception{
        StringBuilder texto = new StringBuilder();
        if ((tipoConviteAulaExperimentalMobileTO.getNomeClienteConvidou() != null) && (!tipoConviteAulaExperimentalMobileTO.getNomeClienteConvidou().equals(""))){
            texto.append("Você foi convidado(a) através de seu(sua) amigo(a) ").append(tipoConviteAulaExperimentalMobileTO.getNomeClienteConvidou());
        }else{
            texto.append("Você foi convidado(a) ");
        }
        texto.append(" a participar de (").append(tipoConviteAulaExperimentalMobileTO.getQuantidadeAulaExperimental());
        texto.append(") aula(s) experimental");
        if ((tipoConviteAulaExperimentalMobileTO.getModalidadesConvite() != null) && (!tipoConviteAulaExperimentalMobileTO.getModalidadesConvite().trim().equals(""))){
            texto.append(" na(s) modalidade(s): ").append(tipoConviteAulaExperimentalMobileTO.getModalidadesConvite());
        }
        if ((tipoConviteAulaExperimentalMobileTO.isAulasAgendadasEmDiasSeguido()) && (tipoConviteAulaExperimentalMobileTO.getQuantidadeAulaExperimental() > 1)) {
            texto.append(", que devem ser agendadas obrigatoriamente em dias seguidos.");
        }else{
            texto.append(".");
        }

        return texto.toString();
    }

    public String montarDescricaoEnderecoConvite(TipoConviteAulaExperimentalMobileTO tipoConviteAulaExperimentalMobileTO) throws Exception {
        StringBuilder texto = new StringBuilder();
        texto.append(tipoConviteAulaExperimentalMobileTO.getEnderecoEmpresa());
        texto.append(" ").append(tipoConviteAulaExperimentalMobileTO.getSetorEmpresa());
        texto.append(" ").append(" Número:").append(tipoConviteAulaExperimentalMobileTO.getNumeroEmpresa());
        if ((tipoConviteAulaExperimentalMobileTO.getComplementoEmpresa() != null) && (!tipoConviteAulaExperimentalMobileTO.getComplementoEmpresa().trim().equals("")))
          texto.append(" - ").append(tipoConviteAulaExperimentalMobileTO.getComplementoEmpresa());
        return texto.toString();
    }

    public String montarDescricaoTelefoneConvite(TipoConviteAulaExperimentalMobileTO tipoConviteAulaExperimentalMobileTO) throws Exception {
        boolean temFone1 = ((tipoConviteAulaExperimentalMobileTO.getTelefone1() != null) && (!tipoConviteAulaExperimentalMobileTO.getTelefone1().trim().equals("")));
        boolean temFone2 = ((tipoConviteAulaExperimentalMobileTO.getTelefone2() != null) && (!tipoConviteAulaExperimentalMobileTO.getTelefone2().trim().equals("")));
        StringBuilder fone = new StringBuilder();
        if ((temFone1) || (temFone2)) {
            if (temFone1)
                fone.append(tipoConviteAulaExperimentalMobileTO.getTelefone1());
            if (temFone2){
                if (temFone1)
                    fone.append(" - ");
                fone.append(tipoConviteAulaExperimentalMobileTO.getTelefone2());
            }
        }
        return fone.toString();
    }

    public TipoConviteAulaExperimentalMobileTO consultarTipoConvite(ConviteAulaExperimentalVO conviteAulaExperimentalVO)throws Exception{
        Integer codigoClienteConvidou = (UtilReflection.objetoMaiorQueZero(conviteAulaExperimentalVO, "getClienteConvidou().getCodigo()")) ? conviteAulaExperimentalVO.getClienteConvidou().getCodigo() : 0;
        TipoConviteAulaExperimentalMobileTO convite = new TipoConviteAulaExperimentalMobileTO();
        StringBuilder sql = new StringBuilder("");
        sql.append("select tConv.quantidadeAulaExperimental, tConv.vigenciaFinal, mod.nome as nomeModalidade, tConv.aulasAgendadasEmDiasSeguido, \n");
        sql.append("       emp.nome as nomeEmpresa,endereco, setor,numero, complemento, emp.telComercial1, emp.telComercial2,   \n");
        sql.append("     (select p.nome from cliente cli inner join pessoa p on p.codigo = cli.pessoa where cli.codigo = ?) as clienteConvidou ");
        sql.append("from tipoConviteAulaExperimental tConv  \n");
        sql.append("inner join empresa emp on emp.codigo = tConv.empresa  \n");
        sql.append("left join tipoConviteAulaExperimentalModalidade tConvMod on tConvMod.tipoConviteAulaExperimental = tConv.codigo  \n");
        sql.append("left join modalidade mod on mod.codigo = tConvMod.modalidade  \n");
        sql.append("where tConv.codigo = ?");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setInt(1, codigoClienteConvidou);
        pst.setInt(2, conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getCodigo());
        ResultSet rs = pst.executeQuery();
        StringBuilder texto = new StringBuilder();
        StringBuilder modalidades = new StringBuilder();
        while (rs.next()){
            if (convite.getCodigoTipoConvite() == null){
                convite.setCodigoTipoConvite(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getCodigo());
                convite.setQuantidadeAulaExperimental(rs.getInt("quantidadeAulaExperimental"));
                convite.setVigenciaFinal(rs.getDate("vigenciaFinal"));
                convite.setNomeEmpresa(rs.getString("nomeEmpresa"));
                convite.setEnderecoEmpresa(rs.getString("endereco"));
                convite.setSetorEmpresa(rs.getString("setor"));
                convite.setNumeroEmpresa(rs.getString("numero"));
                convite.setComplementoEmpresa(rs.getString("complemento"));
                convite.setTelefone1(rs.getString("telComercial1"));
                convite.setTelefone2(rs.getString("telComercial2"));
                convite.setNomeClienteConvidou(rs.getString("clienteConvidou"));
                convite.setAulasAgendadasEmDiasSeguido(rs.getBoolean("aulasAgendadasEmDiasSeguido"));
            }
            if (modalidades.length() <=0){
                if (rs.getString("nomeModalidade") != null)
                  modalidades.append(rs.getString("nomeModalidade"));
            }else{
                if (rs.getString("nomeModalidade") != null)
                  modalidades.append(",").append(rs.getString("nomeModalidade"));
            }
        }
        convite.setModalidadesConvite(modalidades.toString());
        return convite;
    }

    private void verificarVigenciaELimiteConvitePorAluno(Integer codigoTipoConvite, Integer codigoClienteConvidou)throws Exception{
        StringBuilder sql = new StringBuilder("");
        sql.append("select tConv.* \n");
        if ((codigoClienteConvidou != null) && (codigoClienteConvidou >0))
          sql.append(",(tConv.quantidadeConviteAlunoPodeEnviar - (select count(*) from conviteAulaExperimental where tipoConviteAulaExperimental = tConv.codigo and clienteConvidou = ").append(codigoClienteConvidou).append(" )) saldoConvite \n");
        sql.append("from tipoConviteAulaExperimental tConv \n");
        sql.append("where tConv.codigo = ").append(codigoTipoConvite);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            if ((codigoClienteConvidou != null) && (codigoClienteConvidou >0)){
                if (rs.getInt("saldoConvite") <=0) {
                    throw new ConsistirException("Operação não permitida. Limite de convites excedido.");
                }
            }
            if (Calendario.maior(Calendario.getDataComHoraZerada(Calendario.hoje()), rs.getDate("vigenciaFinal"))){
                throw new ConsistirException("Operação não permitida. Convite já expirou.");
            }
        }else{
            throw new ConsistirException("Não foi encontrado nenhum tipo de convite com o codigo '" + codigoTipoConvite + "'");
        }

    }

    private void verificarSeConvidadoJaRecebeuConvite(ConviteAulaExperimentalVO conviteAulaExperimentalVO)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from conviteAulaExperimental where tipoConviteAulaExperimental = ").append(conviteAulaExperimentalVO.getTipoConviteAulaExperimentalVO().getCodigo());
        String msg = "";
        if (conviteAulaExperimentalVO.conviteDeIndicado()){
            sql.append(" and indicadoConvidado = ").append(conviteAulaExperimentalVO.getIndicadoConvidado().getCodigo());
            msg = " o indicado " + conviteAulaExperimentalVO.getIndicadoConvidado().getNomeIndicado();
        }else if (conviteAulaExperimentalVO.conviteDePassivo()){
            sql.append(" and passivoConvidado = ").append(conviteAulaExperimentalVO.getPassivoConvidado().getCodigo());
            msg = " o receptivo " + conviteAulaExperimentalVO.getPassivoConvidado().getNome();
        }else{
            sql.append(" and clienteConvidado = ").append(conviteAulaExperimentalVO.getClienteConvidado().getCodigo());
            msg = " o cliente " + conviteAulaExperimentalVO.getClienteConvidado().getPessoa().getNome();
        }
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            throw new ConsistirException("Operação não permitida. Este convite já foi enviado para " + msg);
        }

    }

    private void verificarSeIndicadoJaRecebeuConvite(Integer codigoTipoConvite,String emailIndicado)throws Exception{
        StringBuilder sql = new StringBuilder("");
        sql.append("select conv.*  \n");
        sql.append("from conviteAulaExperimental conv  \n");
        sql.append("inner join indicado ind on ind.codigo = conv.indicadoConvidado \n");
        sql.append("where (ind.email = ?) \n");
        sql.append(" and conv.tipoConviteAulaExperimental = ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setString(1, emailIndicado);
        pst.setInt(2,codigoTipoConvite);
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
           throw new ConsistirException("Operação não permitida. Este convite já foi enviado para o email informado.");
        }
    }

    private void verificarSeClienteJaRecebeuConvite(Integer codigoTipoConvite,String emailCliente)throws Exception{
        StringBuilder sql = new StringBuilder("");
        sql.append("select conv.*  \n");
        sql.append("from conviteAulaExperimental conv  \n");
        sql.append("inner join cliente cli on cli.codigo = conv.clienteConvidado or cli.codigo = conv.clienteIndicadoOuPassivo \n");
        sql.append("inner join pessoa p on p.codigo = cli.pessoa \n");
        sql.append("inner join email em on em.pessoa = p.codigo \n");
        sql.append("where em.email = ?");
        sql.append(" and conv.tipoConviteAulaExperimental = ? ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.setString(1, emailCliente);
        pst.setInt(2,codigoTipoConvite);
        ResultSet rs = pst.executeQuery();
        if (rs.next()) {
            throw new ConsistirException("Operação não permitida. Este convite já foi enviado para o email informado.");
        }
    }


    public ClienteVO consultarClienteIndicou(Integer codigoIndicado, Integer codigoColaboradorResponsavel, int nivelMontarDados)throws Exception{
        String sql = "select * from conviteAulaExperimental where indicadoConvidado = ? and colaboradorresponsavelconvite = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        pst.setInt(1, codigoIndicado);
        pst.setInt(2, codigoColaboradorResponsavel);
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return getFacade().getCliente().consultarPorChavePrimaria(rs.getInt("clienteConvidou"),nivelMontarDados);
        }
        return null;

    }

    public IndicacaoInterfaceFacade getIndicacao()throws Exception{
        if (this.indicacao == null)
            this.indicacao = new Indicacao(con);
        this.indicacao.setCon(con);
        return indicacao;
    }

    public IndicadoInterfaceFacade getIndicado()throws Exception {
        if (this.indicado == null)
            this.indicado = new Indicado(con);
        this.indicado.setCon(con);
        return indicado;

    }

    public UsuarioInterfaceFacade getUsuarioInterfaceFacade()throws Exception  {
        if (this.usuarioInterfaceFacade == null)
            this.usuarioInterfaceFacade = new Usuario(con);
        this.usuarioInterfaceFacade.setCon(con);
        return usuarioInterfaceFacade;
    }

    public AberturaMetaInterfaceFacade getAberturaMeta()throws Exception {
        if (this.aberturaMeta == null)
            this.aberturaMeta = new AberturaMeta(con);
        this.aberturaMeta.setCon(con);
        return aberturaMeta;
    }

    public FecharMetaInterfaceFacade getFecharMeta()throws Exception{
        if (this.fecharMeta == null)
            this.fecharMeta = new FecharMeta(con);
        this.fecharMeta.setCon(con);
        return fecharMeta;
    }

    public FecharMetaDetalhadoInterfaceFacade getFecharMetaDetalhado()throws Exception{
        if (this.fecharMetaDetalhado == null)
            this.fecharMetaDetalhado = new FecharMetaDetalhado(con);
        this.fecharMetaDetalhado.setCon(con);
        return fecharMetaDetalhado;
    }


    public HistoricoContatoInterfaceFacade getHistoricoContato()throws Exception {
        if (this.historicoContato == null)
            this.historicoContato = new HistoricoContato(con);
        this.historicoContato.setCon(con);
        return historicoContato;
    }

    public ClienteInterfaceFacade getCliente()throws Exception {
        if (this.cliente == null)
            this.cliente = new Cliente(con);
        this.cliente.setCon(con);
        return cliente;
    }

    public TipoConviteAulaExperimentalModalidadeInterfaceFacade getTipoConviteAulaExperimentalModalidade()throws Exception {
        if (this.tipoConviteAulaExperimentalModalidade == null)
            this.tipoConviteAulaExperimentalModalidade = new TipoConviteAulaExperimentalModalidade(con);
        this.tipoConviteAulaExperimentalModalidade.setCon(con);

        return tipoConviteAulaExperimentalModalidade;
    }

    public TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade getTipoConviteAulaExperimentalModalidadeHorario()throws Exception {
        if (this.tipoConviteAulaExperimentalModalidadeHorario == null)
            this.tipoConviteAulaExperimentalModalidadeHorario = new TipoConviteAulaExperimentalModalidadeHorario(con);
        this.tipoConviteAulaExperimentalModalidadeHorario.setCon(con);

        return tipoConviteAulaExperimentalModalidadeHorario;
    }

    public AgendaInterfaceFacade getAgenda()throws Exception {
        if (this.agenda == null)
            this.agenda = new Agenda(con);
        this.agenda.setCon(con);

        return agenda;
    }

    public PassivoInterfaceFacade getPassivo()throws Exception {
        if (this.passivo == null)
            this.passivo = new Passivo(con);
        this.passivo.setCon(con);
        return passivo;
    }

    public PessoaInterfaceFacade getPessoa()throws Exception {
        if (this.pessoa == null)
            this.pessoa = new Pessoa(con);
        this.pessoa.setCon(con);
        return pessoa;
    }

    public HorarioTurmaInterfaceFacade getHorarioTurma()throws Exception {
        if (this.horarioTurma == null)
            this.horarioTurma = new HorarioTurma(con);
        this.horarioTurma.setCon(con);
        return horarioTurma;
    }

    public TurmaInterfaceFacade getTurma() throws Exception{
        if (this.turma == null)
            this.turma = new Turma(con);
        this.turma.setCon(con);

        return turma;
    }

    public ReposicaoInterfaceFacade getReposicao() throws Exception{
        if (this.reposicao == null)
            this.reposicao = new Reposicao(con);
        this.reposicao.setCon(con);
        return reposicao;
    }

    public AulaDesmarcadaInterfaceFacade getAulaDesmarcada() throws Exception{
        if (this.aulaDesmarcada == null)
            this.aulaDesmarcada = new AulaDesmarcada(con);
        this.aulaDesmarcada.setCon(con);
        return aulaDesmarcada;
    }

    public ColaboradorInterfaceFacade getColaborador()throws Exception {
        if (this.colaborador == null)
            this.colaborador = new Colaborador(con);
        this.colaborador.setCon(con);
        return colaborador;
    }
    
    public UsuarioMovelInterfaceFacade getUsuarioMovelDao()throws Exception {
        if (this.usuarioMovelDao == null)
            this.usuarioMovelDao = new UsuarioMovel(con);
        this.usuarioMovelDao.setCon(con);
        return usuarioMovelDao;
    }

    public EmpresaInterfaceFacade getEmpresaDao() throws Exception {
        if(this.empresaDao ==null){
            empresaDao = new Empresa(con);
        }
        return empresaDao;
    }

    public void setEmpresaDao(EmpresaInterfaceFacade empresaDao) {
        this.empresaDao = empresaDao;
    }
}

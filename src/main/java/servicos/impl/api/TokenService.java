package servicos.impl.api;

import acesso.webservice.AcessoControle;
import negocio.comuns.basico.ClienteTokenVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import servicos.SuperServico;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

public class TokenService extends SuperServico {

    final String urlSMS = "http://s.smsup.com.br:8081/smsservice/api/sender/do";

    public TokenService(Connection con) throws Exception {
        super(con);
    }

    public String enviarEmail(EmpresaVO empresa, String email, ClienteTokenVO tokenVO) throws Exception {
        AcessoControle acessoControle = new AcessoControle(getCon());
        StringBuilder html = new StringBuilder();
        html.append("<h1>Bem vindo a ").append(empresa.getNome()).append("</h1><br/>");
        html.append("O código gerado é: <b>").append(tokenVO.getCodigoValidacao()).append("</b><br/>");
        html.append("Insira este código no site.");
        acessoControle.enviarEmail(new String[]{email}, "Token para Acesso ao Vendas Online", html);
        return "OK";
    }

    public String enviarSMS(EmpresaVO empresaVO, String telefone, ClienteTokenVO tokenVO) throws ConsistirException {
        final String CHAVE_SMS = "aae06a9469a47e5b58da769ec6041af0";
        final String TOKEN_SMS = "klWlG3r2VOhNeJS6zSEbzg==";

        Map<String, String> params = new HashMap<String, String>();
        params.put("key", CHAVE_SMS);
        params.put("token", TOKEN_SMS);

        String mensagem = montarMensagemSMS(telefone, tokenVO);
        params.put("msgs", mensagem);

        String retornoRequest = "";
        try {
            ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
            executeRequestHttpService.connectTimeout = 5000;
            executeRequestHttpService.readTimeout = 15000;
            retornoRequest = executeRequestHttpService.executeRequestInner(urlSMS, params);
        } catch (IOException ioException) {
            Uteis.logar(ioException, this.getClass());
        }
        return retornoRequest;
    }

    private String montarMensagemSMS(String telefone, ClienteTokenVO clienteTokenVO) {
        telefone = telefone.replaceAll("[()-.]", "");
        if (telefone.length() == 11) {
            telefone = "55" + telefone;
        }

        if (telefone.length() == 13) {
            return telefone + "| O codigo gerado para o site: " + clienteTokenVO.getCodigoValidacao();
        }
        return null;
    }
}

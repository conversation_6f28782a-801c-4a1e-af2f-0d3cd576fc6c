package servicos.impl.pessoaMs;

import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.microsservice.SuperMSService;
import servicos.impl.planoMs.PlanoMsException;
import servicos.util.ExecuteRequestHttpService;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PessoaMsService extends SuperMSService {

    public static JSONObject replicarFornecedor(JSONObject fornecedorJSON, String pessoaMsUrl, String chave, Integer codigoEmpresaDestino) throws Exception {
        final String url = pessoaMsUrl + "/fornecedor/replicar";
        final Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", codigoEmpresaDestino.toString());

        String response = null;
        try {
            response = ExecuteRequestHttpService.post(url, fornecedorJSON.toString(), headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            Logger.getLogger(PessoaMsService.class.getName()).log(Level.SEVERE, "Retorno URL: " + url + " - Resposta: " +
                    response + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new PessoaMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new PessoaMsException(e.getMessage() + " - Response: " + response);
            }
        }
    }

    public static JSONObject clonarFornecedor(Integer codigo, String pessoaMsUrl, String chave) throws Exception {
        final String url = pessoaMsUrl + "/fornecedor/" + codigo + "/clonar";
        return get(chave, url);
    }

    public static String atualizarFornecedorReplicado(JSONObject fornecedorJSON, String pessoaMsUrl, String chave, Integer codigoEmpresaDestino) throws Exception {
        final String url = pessoaMsUrl + "/fornecedor/atualizarFornecedorReplicado";
        final Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", codigoEmpresaDestino.toString());
        String result = null;

        try {
            result = ExecuteRequestHttpService.post(url, fornecedorJSON.toString(), headers);
            return "sucesso: " + result;
        } catch (Exception e) {
            Logger.getLogger(PessoaMsException.class.getName()).log(Level.SEVERE, "Resposta: " +
                    result + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new PessoaMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new PessoaMsException(e.getMessage());
            }
        }
    }

    public static JSONObject replicarUsuario(JSONObject usuarioJSON, String pessoaMsUrl, String chaveDestino, String empresa) throws Exception {
        String url = pessoaMsUrl + "/usuario/saveOrUpdateReplicando";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chaveDestino));
        headers.put("empresaId", empresa);

        try {
            String response = ExecuteRequestHttpService.post(url, usuarioJSON.toString(), headers);

            return new JSONObject(new JSONObject(response).getString("content"));
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    public static JSONObject replicarUsuarioPerfilAcesso(JSONObject obj, String pessoaMsUrl, String chaveDestino) throws Exception {
        String url = pessoaMsUrl + "/usuario/replicarUsuarioPerfilAcesso";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chaveDestino));
        headers.put("Content-Type", "application/json");

        try {
            String response = ExecuteRequestHttpService.post(url, obj.toString(), headers);

            return new JSONObject(new JSONObject(response).getString("content"));
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    public static JSONObject replicarColaborador(JSONObject colaboradorJSON, String pessoaMsUrl, String chaveDestino, String empresa) throws Exception {
        String url = pessoaMsUrl + "/colaborador/saveOrUpdateReplicando";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chaveDestino));
        headers.put("empresaId", empresa);

        try {
            String response = ExecuteRequestHttpService.post(url, colaboradorJSON.toString(), headers);

            return new JSONObject(new JSONObject(response).getString("content"));
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }

    public static JSONObject clonarUsuario(Integer codigo, String pessoaMsUrl, String chaveOrigem) throws Exception {
        String url = pessoaMsUrl + "/usuario/clonar/" + codigo;
        return get(chaveOrigem, url);
    }

    public static JSONObject clonarColaborador(Integer codigo, String pessoaMsUrl, String chaveOrigem) throws Exception {
        String url = pessoaMsUrl + "/colaborador/clonar/" + codigo;
        return get(chaveOrigem, url);
    }

    public static JSONObject obterUsuario(Integer codigo, String pessoaMsUrl, String chave) throws Exception {
        String url = pessoaMsUrl + "/usuario/" + codigo;
        return get(chave, url);
    }

    public static JSONArray obterUsuarioPorNomeUsuario(String nomeUsuario, String pessoaMsUrl, String chave) throws Exception {
        String nomeCodificado = URLEncoder.encode(nomeUsuario, StandardCharsets.UTF_8.toString());
        String url = pessoaMsUrl + "/usuario/findByName?nome=" + nomeCodificado;

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONArray(response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }


    private static JSONObject get(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));

        try {
            String response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            e.printStackTrace();
            throw new PlanoMsException(messageError(e.getMessage()));
        }
    }
}

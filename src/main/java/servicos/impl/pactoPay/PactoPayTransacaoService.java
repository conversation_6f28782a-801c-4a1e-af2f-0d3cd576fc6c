package servicos.impl.pactoPay;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import br.com.pactosolucoes.integracao.pactopay.PactoPayService;
import br.com.pactosolucoes.integracao.pactopay.SituacaoTransacaoPactoPayEnum;
import br.com.pactosolucoes.integracao.pactopay.dto.RemessaDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.ResultadoDTO;
import br.com.pactosolucoes.integracao.pactopay.dto.TransacaoResultadoDTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoPactoPayVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.PactoPayServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 23/11/2020
 */
public class PactoPayTransacaoService extends AbstractCobrancaOnlineServiceComum implements PactoPayServiceInterface {

    private String urlAPIRequisicao = "";
    private String urlAPIConsulta = "";
    private String merchantId;
    private String merchantKey;

    private Transacao transacaoDAO;
    private Pessoa pessoaDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private String chave;

    private String METODO_PAGAR = "";
    private String METODO_CONSULTAR = "/consultar";
    private String METODO_CAPTURAR = "/capturar";
    private String METODO_CANCELAR = "/cancelar";

    public PactoPayTransacaoService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.transacaoDAO = new Transacao(con);
        this.pessoaDAO = new Pessoa(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        this.chave = DAO.resolveKeyFromConnection(con);
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            this.merchantId = this.convenioCobrancaVO.getCodigoAutenticacao01();
            this.merchantKey = this.convenioCobrancaVO.getCodigoAutenticacao02();

            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCieloRequisicaoProducao);
                this.urlAPIConsulta = PropsService.getPropertyValue(PropsService.urlApiCieloConsultaProducao);
            } else {
                this.urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCieloRequisicaoSandbox);
                this.urlAPIConsulta = PropsService.getPropertyValue(PropsService.urlApiCieloConsultaSandbox);
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacaoVO = null;
        Transacao transacaoDAO = null;
        Empresa empresaDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            empresaDAO = new Empresa(getCon());

            transacaoVO = criarTransacao(dadosCartao, new TransacaoPactoPayVO(), TipoTransacaoEnum.PACTO_PAY, this.convenioCobrancaVO);
            transacaoVO.setCodigo(0);
            transacaoDAO.incluir(transacaoVO);

            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);
            transacaoVO.setPessoaPagador(pessoaVO);

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            transacaoVO.setEmpresaVO(empresaVO);

            //salvar para ser apresentado no gestão de transação
            String numeroCartao = "";
            if (!UteisValidacao.emptyString(dadosCartao.getNumero())) {
                numeroCartao = dadosCartao.getNumero();
            }
            gravarOutrasInformacoes(numeroCartao, dadosCartao, transacaoVO);

            RemessaDTO remessaDTO = transacaoVO.toRemessaDTO(this.chave, dadosCartao);
            if (transacaoVO.getOrigem().equals(OrigemCobrancaEnum.PACTO_PAY_RETENTATIVA)) {
                remessaDTO.setAsync(dadosCartao.isAsync());
            } else {
                remessaDTO.setAsync(false);
            }

            JSONObject remessaDTOJSON = new JSONObject(remessaDTO);

            transacaoVO.setParamsEnvio(encriptarDadosSigilosos(remessaDTOJSON));

            validarDadosTransacao(transacaoVO, dadosCartao);

            //enviar para microservico
            String retorno = executarRequest(METODO_PAGAR, remessaDTOJSON.toString(), MetodoHttpEnum.POST);
            processarRetorno(transacaoVO, retorno);

            transacaoDAO.alterar(transacaoVO);

            if (!dadosCartao.isAsync() &&
                    transacaoVO.isProcessarNovamente() &&
                    transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                transacaoVO.getCartaoCreditoNovo().setParcelas(dadosCartao.getParcelas());
                return tentarAprovacao(transacaoVO.getCartaoCreditoNovo());
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacaoVO, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacaoVO, ex.getMessage());
        } finally {
            gravarTentativaCartao(transacaoVO);
            transacaoDAO = null;
            empresaDAO = null;
        }
        return transacaoVO;
    }

    private String encriptarDadosSigilosos(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        JSONArray lista = parametrosPagamento.getJSONArray("transacoes");
        for (int e = 0; e < lista.length(); e++) {
            JSONObject obj = lista.getJSONObject(e);
            try {
                if (obj.has("codigoSeguranca") &&
                        !UteisValidacao.emptyString(obj.optString("codigoSeguranca"))) {
                    obj.put("codigoSeguranca", "***");
                }
            } catch (Exception ignored) {
            }
        }
        return parametrosPagamento.toString();
    }

    @Override
    public String consultarTransacao(TransacaoVO transacaoVO) throws Exception {
        return executarRequest(METODO_CONSULTAR + "/" + transacaoVO.getCodigoExterno(), null, MetodoHttpEnum.GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoVO, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarCapturaTransacao(transacaoVO);
        new Transacao(getCon()).alterar(transacaoVO);
        return transacaoVO;
    }

    public void realizarCapturaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!transacaoVO.isTransacaoVerificarCartao() &&
                !UteisValidacao.emptyString(transacaoVO.getCodigoExterno()) &&
                transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {

            String retorno = executarRequest(METODO_CAPTURAR + "/" + transacaoVO.getCodigoExterno(), null, MetodoHttpEnum.PUT);
            incluirHistoricoRetornoTransacao(transacaoVO, retorno, "realizarCapturaTransacao");
            processarRetorno(transacaoVO, retorno);
        }
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        new Exception("Não implementado");
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoNova, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        new Exception("Não implementado");
        return transacaoNova;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        String retorno = executarRequest(METODO_CANCELAR + "/" + transacaoVO.getCodigoExterno(), null, MetodoHttpEnum.PUT);
        processarRetornoCancelamento(transacaoVO, retorno);
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && estornarRecibo &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        Transacao transacaoDAO = new Transacao(this.getCon());
        transacaoDAO.alterar(transacaoVO);
        transacaoDAO = null;
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoTransacao(TransacaoVO transacaoVO) throws Exception {
        String retorno = executarRequest(METODO_CONSULTAR + "/" + transacaoVO.getCodigoExterno(), null, MetodoHttpEnum.GET);
        incluirHistoricoRetornoTransacao(transacaoVO, retorno, "consultarSituacaoTransacao");
        processarRetorno(transacaoVO, retorno);
        Transacao transacaoDAO = new Transacao(this.getCon());
        transacaoDAO.alterar(transacaoVO);
        transacaoDAO = null;
    }

    public void processarRetornoCancelamento(TransacaoVO transacaoVO, String retorno) {
        try {
            incluirHistoricoRetornoTransacao(transacaoVO, retorno, "processarRetornoCancelamento");
            transacaoVO.setResultadoCancelamento(retorno);
            ResultadoDTO resultadoDTO = JSONMapper.getObject(new JSONObject(retorno), ResultadoDTO.class);
            TransacaoResultadoDTO transacaoResultadoDTO = resultadoDTO.getTransacaoResultado();
            transacaoVO.setSituacao(SituacaoTransacaoPactoPayEnum.obterSituacaoTransacaoPactoPay(SituacaoTransacaoPactoPayEnum.obterPorCodigo(transacaoResultadoDTO.getSituacao())));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void processarRetorno(TransacaoVO transacaoVO, String retorno) {
        try {
            incluirHistoricoRetornoTransacao(transacaoVO, retorno, "processarRetorno");
            transacaoVO.setParamsResposta(retorno);
            ResultadoDTO resultadoDTO = JSONMapper.getObject(new JSONObject(retorno), ResultadoDTO.class);
            TransacaoResultadoDTO transacaoResultadoDTO = resultadoDTO.getTransacaoResultado();

            //id da remessa no PactoPay
            transacaoVO.setCodigoExterno(resultadoDTO.getTransacaoResultado().getId());
            transacaoVO.setCodigoAutorizacao(transacaoResultadoDTO.getAutorizacao());
            transacaoVO.setSituacao(SituacaoTransacaoPactoPayEnum.obterSituacaoTransacaoPactoPay(SituacaoTransacaoPactoPayEnum.obterPorCodigo(transacaoResultadoDTO.getSituacao())));

            if (!UteisValidacao.emptyString(transacaoResultadoDTO.getNovoCartaoAragorn())) {
                processarNovoCartao(transacaoVO, resultadoDTO);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarNovoCartao(TransacaoVO transacaoVO,
                                     ResultadoDTO resultadoDTO) {
        Cliente clienteDAO = null;
        AutorizacaoCobrancaCliente autoDAO = null;
        try {
            transacaoVO.setProcessarNovamente(false);

            String cartaoEnviado = transacaoVO.getValorCartaoMascarado();
            if (UteisValidacao.emptyString(cartaoEnviado)) {
                return;
            }

            AragornService aragornService = new AragornService();
            NazgDTO nazgDTO = aragornService.obterNazg(resultadoDTO.getTransacaoResultado().getNovoCartaoAragorn());
            aragornService = null;

            clienteDAO = new Cliente(getCon());
            autoDAO = new AutorizacaoCobrancaCliente(getCon());
            ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(transacaoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            List<AutorizacaoCobrancaClienteVO> listaAutorizacoes = autoDAO.consultarPorClienteTipoAutorizacao(clienteVO.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (AutorizacaoCobrancaClienteVO auto : listaAutorizacoes) {
                if (auto.getCartaoMascarado_Apresentar().equals(cartaoEnviado)) {

                    autoDAO.alterarSituacaoAutorizacaoCobranca(false, auto, "Processo de RENOVA FACIL PactoPay");

                    AutorizacaoCobrancaClienteVO novaAutorizacao = new AutorizacaoCobrancaClienteVO();
                    novaAutorizacao.setNumeroCartao(nazgDTO.getCard());
                    novaAutorizacao.setCartaoMascarado(APF.getCartaoMascarado(novaAutorizacao.getNumeroCartao()));
                    novaAutorizacao.setValidadeCartao(nazgDTO.getValidadeMMYYYY(true));
                    novaAutorizacao.setNomeTitularCartao(nazgDTO.getName());
                    novaAutorizacao.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
                    novaAutorizacao.setCvv("");
                    novaAutorizacao.setConvenio(auto.getConvenio());
                    novaAutorizacao.setTipoAutorizacao(auto.getTipoAutorizacao());
                    novaAutorizacao.setTipoACobrar(auto.getTipoACobrar());
                    novaAutorizacao.setCliente(auto.getCliente());
//                    novaAutorizacao.setValidarCvv(false);
                    novaAutorizacao.setClienteTitularCartao(auto.isClienteTitularCartao());
                    novaAutorizacao.setValidarClienteTitularCartao(false);
                    novaAutorizacao.setOrigemCobrancaEnum(OrigemCobrancaEnum.ZW_AUTOMATICO_RENOVA_FACIL_FACILITE_PAY);
                    autoDAO.incluir(novaAutorizacao);
                    autoDAO.marcarRenovadoAutomatico(auto, novaAutorizacao);

                    CartaoCreditoTO novoCartao = new CartaoCreditoTO();
                    novoCartao.setTokenAragorn(novaAutorizacao.getTokenAragorn());
                    novoCartao.setNumero(nazgDTO.getCard());
                    novoCartao.setValidade(nazgDTO.getValidadeMMYYYY(true));
                    novoCartao.setNomeTitular(nazgDTO.getName());
                    novoCartao.setBand(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
                    novoCartao.setUrl(transacaoVO.getUrlTransiente());
                    novoCartao.setUsuarioResponsavel(transacaoVO.getUsuarioResponsavel());
                    novoCartao.setEmpresa(transacaoVO.getEmpresa());
                    novoCartao.setListaParcelas(transacaoVO.getListaParcelas());
                    novoCartao.setValor(transacaoVO.getValor());
                    novoCartao.setIdPessoaCartao(transacaoVO.getPessoaPagador().getCodigo());
                    transacaoVO.setCartaoCreditoNovo(novoCartao);
                    transacaoVO.setProcessarNovamente(true);
                    Uteis.logar(null, "Adicionei cartão atraves do renova facil PactoPay na autorização de cobrança do cliente " + auto.getCliente().getNome_Apresentar());
                    break;
                }
            }
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao adicionar cartão renovado automaticamente: " + e.getMessage());
            transacaoVO.setProcessarNovamente(false);
        } finally {
            clienteDAO = null;
            autoDAO = null;
        }
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        //não implementado
    }

    private String executarRequest(String metodo, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        RespostaHttpDTO respostaHttpDTO = PactoPayService.enviar("/transacao" + metodo, body, metodoHttpEnum);
        JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
        if (json.has("data")) {
            return json.getJSONObject("data").toString();
        } else if (!UteisValidacao.emptyString(json.optString("message"))) {
            throw new Exception(json.optString("message"));
        } else {
            throw new Exception(respostaHttpDTO.getResponse());
        }
    }
}


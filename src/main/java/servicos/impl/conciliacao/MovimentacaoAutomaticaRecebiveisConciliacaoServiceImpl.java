/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.conciliacao;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoDadoConciliacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.financeiro.ConfiguracaoFinanceiroControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.criarConsulta;
import static negocio.facade.jdbc.financeiro.ExtratoDiarioItem.montarDadosConciliacao;


/**
 * <AUTHOR>
 */
public class MovimentacaoAutomaticaRecebiveisConciliacaoServiceImpl {

    EmpresaVO empresaVo;
    private Connection con;
    private Log logDAO;
    private ContaVO contaOrigem;
    private ContaVO contaDestino;
    private Integer nrNaoConciliados;
    private List<MovPagamentoVO> listaOutros = new ArrayList<MovPagamentoVO>();
    private TipoFormaPagto tipoMovimentarDebito = TipoFormaPagto.CARTAODEBITO;
    private TipoFormaPagto tipoMovimentarCredito = TipoFormaPagto.CARTAOCREDITO;
    private List<CartaoCreditoTO> listaCartoes;
    private Double taxaAntecipacao = 0.0;
    private FormaPagamentoVO formaPagamentosDebito = new FormaPagamentoVO();
    private FormaPagamentoVO formaPagamentosCredito = new FormaPagamentoVO();
    private FormaPagamentoVO formaPagamentosRecorrente = new FormaPagamentoVO();
    private ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = new ConfiguracaoFinanceiroVO();
    private UsuarioVO usuarioVO = new UsuarioVO();
    private CaixaVO caixaVoEmAberto = new CaixaVO();
    private Conta conta;
    private MovConta movConta;
    private MovPagamento movPagamento;
    private HistoricoCartao historicoCartao;
    private CartaoCredito cartaoCredito;
    private Lote lote;
    private Empresa empresa;
    private Usuario usuario;
    private FormaPagamento formaPagamento;
    private Date databloqueio = null;

    public MovimentacaoAutomaticaRecebiveisConciliacaoServiceImpl(Connection con) throws Exception {
        this.con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        this.logDAO = new Log(con);
    }

    public MovimentacaoAutomaticaRecebiveisConciliacaoServiceImpl() throws Exception {
        super();
    }


    public void processarMovimentacaoAutomaticaRecebiveisConciliacao(Date reprocessarAPartirDe, Date reprocessarAte) throws Exception {
        try {
            ConfiguracaoFinanceiro configuracaoFinanceiro = new ConfiguracaoFinanceiro(con);
            configuracaoFinanceiroVO = configuracaoFinanceiro.consultar();

            if (configuracaoFinanceiroVO.isMovimentacaoAutomaticaRecebiveisConciliacao()) {
                Uteis.logarDebug("#####Configuração de Movimentação Automática Recebíveis da Conciliação está HABILITADA#####");
                conta = new Conta(con);
                movConta = new MovConta(con);
                movPagamento = new MovPagamento(con);
                empresa = new Empresa(con);
                historicoCartao = new HistoricoCartao(con);
                cartaoCredito = new CartaoCredito(con);
                lote = new Lote(con);
                usuario = new Usuario(con);
                List<EmpresaVO> listaEmpresas = empresa.consultarEmpresas();
                usuarioVO = usuario.consultarPorUsername("PACTOBR", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                formaPagamento = new FormaPagamento(con);
                List<FormaPagamentoVO> listaFormaPagamentoDebito = formaPagamento.consultarPorTipoFormaPagamento(TipoFormaPagto.CARTAODEBITO, null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                List<FormaPagamentoVO> listaFormaPagamentoCredito = formaPagamento.consultarPorTipoFormaPagamento(TipoFormaPagto.CARTAOCREDITO, null, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                for (EmpresaVO empresa : listaEmpresas) {
                    empresaVo = empresa;
                    int qtdDias = Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte));
                    Date diaProcessar;
                    for (int i = 0; i <= qtdDias; i++) {
                        diaProcessar = Calendario.somarDias(reprocessarAPartirDe, i);
                        //abrir somente um caixa, independente da qtd de dias a processar
                        this.abrirCaixa(usuarioVO, empresa, diaProcessar);

                        //EXCLUIR LANCAMENTOS AUTOMÁTICOS QUE JÁ TINHAM NO DIA para criar os novos...
                        excluirMovContaCasoJaTenhaSidoCriadaAnteriormente(diaProcessar);

                        for (FormaPagamentoVO formaPagamentoDebito : listaFormaPagamentoDebito) {
                            this.filtrarExtrato(diaProcessar, formaPagamentoDebito);
                        }
                        for (FormaPagamentoVO formaPagamentoCredito : listaFormaPagamentoCredito) {
                            this.filtrarExtrato(diaProcessar , formaPagamentoCredito);
                        }
                        this.fecharCaixa(caixaVoEmAberto, diaProcessar);
                    }
                }
            } else {
                Uteis.logarDebug("#####Configuração de Movimentação Automática Recebíveis da Conciliação está DESABILITADA#####");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            configuracaoFinanceiroVO = null;
            conta = null;
            movConta = null;
            movPagamento = null;
            empresa = null;
            historicoCartao = null;
            cartaoCredito = null;
            lote = null;
            usuario = null;
            formaPagamento = null;
        }

    }

    private void filtrarExtrato(Date diaProcessar, FormaPagamentoVO formaPagamento) throws Exception {
        List<ExtratoDiarioItemVO> lista = new ArrayList<ExtratoDiarioItemVO>();
        ExtratoDiarioItem extratoDiarioItemDAO = new ExtratoDiarioItem(con);
        Uteis.logarDebug("# Obter lista de extratoItem da forma de pagamento: " + formaPagamento.getDescricao() + " para movimentar os recebíveis posteriormente");
        ResultSet rs = extratoDiarioItemDAO.consultarConciliadosPorMovpagamento(diaProcessar, diaProcessar, "", "", TipoConciliacaoEnum.PAGAMENTOS.getCodigo(), empresaVo.getCodigo(), null,
                formaPagamento.getCodigo(), false, 0, 0, "");
        lista.addAll(montarDadosConciliacao(rs, true, false, con, false, TipoConciliacaoEnum.PAGAMENTOS.getCodigo(), false, TipoDadoConciliacaoEnum.Movpagamento));

        //Adiciona a lista MovPagamentos de ContaCorrenteOrigem preenchido, para CC que foi utilizado e movimentado
        ResultSet rsCCDataAlterada = extratoDiarioItemDAO.consultarCCDataAlterada(diaProcessar, diaProcessar, "", "", TipoConciliacaoEnum.PAGAMENTOS.getCodigo(), empresaVo.getCodigo(),
                null, formaPagamento.getCodigo(), false, 0, 0, "");
        lista.addAll(montarDadosConciliacao(rsCCDataAlterada, true, false, con, false, TipoConciliacaoEnum.PAGAMENTOS.getCodigo(), false, TipoDadoConciliacaoEnum.CCDataAlterada));

        if (lista.size() > 0) {
            //Estornados são os itens Pretos da Conciliação
            desconsiderarEstornadosDaLista(lista);

            extratoDiarioItemDAO.ajustarValorMovpagamentoOuCartaoCreditoComSaldoEnviadoDeCancelamentoContratoParaContaCorrente(lista, con);
            extratoDiarioItemDAO.validarSituacaoExtratoDiarioItemVO(lista);

            //Pendencia são os itens Amarelos da Conciliação
            desconsiderarPendentesDaLista(lista);
            lista = retirarListaItensJaMovimentadosContaConfiguradaMovimentacaoAutomatica(lista);

            this.validacoesMovimentacaoConciliacao(lista);
            this.efetivarMovimentacao(lista, formaPagamento, diaProcessar);

        } else {
            Uteis.logarDebug("# Lista de extratos com a forma de pagamento " + formaPagamento.getDescricao() + " vazia");
        }
    }

    private List<ExtratoDiarioItemVO> retirarListaItensJaMovimentadosContaConfiguradaMovimentacaoAutomatica(List<ExtratoDiarioItemVO> lista) {
        List<ExtratoDiarioItemVO> listaSemMovimentados = new ArrayList<>();
        for (ExtratoDiarioItemVO ei : lista) {
            //if para verificar se o extrato é de crédito ou débito, pois o comportamento é diferente.
            if (ei.getCredito()) {
                if (UteisValidacao.emptyString(ei.getContaMovimento()) ||
                        (!UteisValidacao.emptyString(ei.getContaMovimento()) && ei.getCartao() != null &&
                        ei.getCartao().getCodigoContaContido() != this.configuracaoFinanceiroVO.getContamovimentacaoautomaticacredito().getCodigo())) {
                    listaSemMovimentados.add(ei);
                }
            } else {
                if (UteisValidacao.emptyString(ei.getContaMovimento()) ||
                        (!UteisValidacao.emptyString(ei.getContaMovimento()) &&
                        !ei.getContaMovimento().equals(this.configuracaoFinanceiroVO.getContamovimentacaoautomaticadebito().getDescricaoCurta()))) {
                    listaSemMovimentados.add(ei);
                }
            }
        }
        return listaSemMovimentados;
    }

    private static void desconsiderarEstornadosDaLista(List<ExtratoDiarioItemVO> lista) {
        //Estornados são os itens pretos da Conciliação
        List<ExtratoDiarioItemVO> listaAux = new ArrayList<ExtratoDiarioItemVO>();
        for (ExtratoDiarioItemVO ei : lista) {
            if (ei.getCodigo() != 0 && ei.getCartao() != null && !SituacaoItemExtratoEnum.ESTORNADO_OPERADORA.equals(ei.getSituacao())) {
                listaAux.add(ei);
            }
        }

        if (listaAux.size() > 0) {
            // Limpar a lista original e adicionar todos os elementos de listaAux
            lista.clear();
            lista.addAll(listaAux);
        }
    }

    private static void desconsiderarPendentesDaLista(List<ExtratoDiarioItemVO> lista) {
        //Pendencia são os itens Amarelos da Conciliação
        List<ExtratoDiarioItemVO> listaAux = new ArrayList<ExtratoDiarioItemVO>();
        for (ExtratoDiarioItemVO ei : lista) {
            BigDecimal valorAdquirente = BigDecimal.valueOf(ei.getValorBruto()).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal valorSistema = BigDecimal.valueOf(ei.getMovPagamento().getValor()).setScale(2, BigDecimal.ROUND_HALF_UP);

            boolean mesmoValorZWEAdquirente = valorAdquirente.compareTo(valorSistema) == 0;
            boolean itemPendente = (ei.getSituacao() != null && SituacaoItemExtratoEnum.PENDENCIAS.equals(ei.getSituacao())) || !mesmoValorZWEAdquirente;

            //adicionar na lsita somente os não pendentes
            if (!itemPendente) {
                listaAux.add(ei);
            }
        }
        if (listaAux.size() > 0) {
            // Limpar a lista original e adicionar todos os elementos de listaAux
            lista.clear();
            lista.addAll(listaAux);
        }
    }

    public void validacoesMovimentacaoConciliacao(List<ExtratoDiarioItemVO> extratoDiario) throws Exception {
        if (UteisValidacao.emptyList(extratoDiario)) {
            return;
        }
        Uteis.logarDebug("# Inicio validacoesMovimentacaoConciliacao");
        nrNaoConciliados = 0;

        boolean temAlguemConciliado = false;
        List<ExtratoDiarioItemVO> listaAux = new ArrayList<ExtratoDiarioItemVO>();
        for (ExtratoDiarioItemVO ei : extratoDiario) {
            if (SituacaoItemExtratoEnum.OK.equals(ei.getSituacao())) {
                listaAux.add(ei);
            }
            if (!temAlguemConciliado && (SituacaoItemExtratoEnum.OK.equals(ei.getSituacao()) || SituacaoItemExtratoEnum.PENDENCIAS.equals(ei.getSituacao()))) {
                temAlguemConciliado = true;
            }
        }
        if (listaAux.size() > 0) {
            extratoDiario = listaAux;
        }
        contaOrigem = new ContaVO();
        Conta conta = new Conta(con);
        if (extratoDiario.size() > 0 && extratoDiario.get(0).getContaMovimento() != null && !extratoDiario.get(0).getContaMovimento().equals("")) {
            contaOrigem = conta.consultarPorDescricao(extratoDiario.get(0).getContaMovimento(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
        if (!temAlguemConciliado) {
            Uteis.logarDebug("# validacoesMovimentacaoConciliacao, temAlguemConciliado");
        }
    }

    public void efetivarMovimentacao(List<ExtratoDiarioItemVO> extratoDiario, FormaPagamentoVO formaPagamento, Date diaProcessar) {
        try {
            double totalSelecionado = 0.0;
            double valorLiquidoConciliacao = 0.0;
            listaCartoes = new ArrayList<>();
            Uteis.logarDebug("# Inicio efetivarMovimentacao forma de pagamento: " + formaPagamento.getDescricao());
            listaOutros = new ArrayList<>();
            LoteVO loteVo = new LoteVO();
            loteVo.setDataDeposito(diaProcessar);
            loteVo.setEmpresa(empresaVo);
            loteVo.setUsuarioResponsavel(usuarioVO);
            loteVo.setDataLancamento(diaProcessar);

            for (ExtratoDiarioItemVO ei : extratoDiario) {
                if (ei.getSituacao().equals(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA)
                        || ei.getSituacao().equals(SituacaoItemExtratoEnum.ESTORNADO_OPERADORA)) {
                    continue;
                }
                if (!UteisValidacao.emptyNumber(ei.getCodigoCartaoCredito()) && (TipoFormaPagto.CARTAOCREDITO.equals(tipoMovimentarCredito))) {
                    if (TipoConciliacaoEnum.VENDAS.getCodigo().equals(ei.getTipoConciliacao())) {
                        for (CartaoCreditoTO cartao : ei.getCartoes()) {
                            cartao.setCartaoEscolhido(true);
                            totalSelecionado = Uteis.arredondarForcando2CasasDecimais(totalSelecionado + cartao.getValor());
                            listaCartoes.add(cartao);
                        }
                    } else {
                        if (ei.getCartao() != null) {
                            ei.getCartao().setCartaoEscolhido(true);
                            listaCartoes.add(ei.getCartao());
                        }
                        totalSelecionado = Uteis.arredondarForcando2CasasDecimais(totalSelecionado + ei.getValorCCNumber());
                    }
                    valorLiquidoConciliacao += Uteis.arredondarForcando2CasasDecimais(ei.getValorLiquido());
                } else if (!UteisValidacao.emptyNumber(ei.getCodigoMovPagamento())
                        && ei.getMovPagamento() != null
                        && TipoFormaPagto.CARTAODEBITO.equals(tipoMovimentarDebito)
                        && ei.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAODEBITO.getSigla())) {
                    valorLiquidoConciliacao += Uteis.arredondarForcando2CasasDecimais(ei.getValorLiquido());

                    MovPagamento movPagamento = new MovPagamento(con);
                    List<MovPagamentoVO> pagamentosItemDebito = movPagamento.consultarPagamentosItemExtrato(ei);
                    for (MovPagamentoVO pag : pagamentosItemDebito) {
                        pag.setMovPagamentoEscolhidaFinan(true);
                        totalSelecionado = Uteis.arredondarForcando2CasasDecimais(pag.getValor() + totalSelecionado);
                        listaOutros.add(pag);
                    }
                }
            }
            double valorLiquido = valorLiquidoConciliacao;
            MovContaVO movContaVO = new MovContaVO();

            movContaVO.setDescricao((formaPagamento.getTipoFormaPagamento().equals("CD") ? configuracaoFinanceiroVO.getDescricaomovimentacaoautomaticaDebito() :
                    (formaPagamento.getDescricao().equals("CARTÃO RECORRENTE") ? configuracaoFinanceiroVO.getDescricaomovimentacaoautomaticaCredito() + " - RECORRENTE" :
                            configuracaoFinanceiroVO.getDescricaomovimentacaoautomaticaCredito())) + " " + Uteis.getData(diaProcessar));

            movContaVO.setInfoMovimentacaoAutomaticaConciliacao((formaPagamento.getDescricao().equals("CARTÃO RECORRENTE") ? formaPagamento.getTipoFormaPagamento() + " - RECORRENTE - " :
                    formaPagamento.getTipoFormaPagamento() + " - ") + Uteis.getDataFormatoBD(diaProcessar));

            movContaVO.setPessoaVO(formaPagamento.getTipoFormaPagamento().equals("CD") ? configuracaoFinanceiroVO.getFavorecidomovimentacaoautomaticaDebito() :
                    configuracaoFinanceiroVO.getFavorecidomovimentacaoautomaticaCredito());

            movContaVO.setContaVO(formaPagamento.getTipoFormaPagamento().equals("CD") ? configuracaoFinanceiroVO.getContamovimentacaoautomaticadebito() :
                    configuracaoFinanceiroVO.getContamovimentacaoautomaticacredito());

            movContaVO.setEmpresaVO(empresaVo);

            movContaVO.setUsuarioVO(usuarioVO);
            movContaVO.setDataLancamento(diaProcessar);
            movContaVO.setValor(totalSelecionado);
            movContaVO.setValorPago(totalSelecionado);
            movContaVO.setValorLiquido(valorLiquido);
            movContaVO.setTipoForma(formaPagamento.getTipoFormaPagamento().equals("CD") ? TipoFormaPagto.CARTAODEBITO : TipoFormaPagto.CARTAOCREDITO);
            movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.DEPOSITO);
            movContaVO.setDataVencimento(diaProcessar);
            movContaVO.setDataCompetencia(diaProcessar);
            movContaVO.setDataQuitacao(diaProcessar);

            MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
            movContaRateioVO.setDescricao(formaPagamento.getTipoFormaPagamento().equals("CD") ? configuracaoFinanceiroVO.getDescricaomovimentacaoautomaticaDebito() : configuracaoFinanceiroVO.getDescricaomovimentacaoautomaticaCredito());
            movContaRateioVO.setTipoES(TipoES.ENTRADA);
            movContaRateioVO.setValor(movContaVO.getValor());

            movContaRateioVO.setFormaPagamentoVO(formaPagamento);
            movContaVO.getMovContaRateios().add(movContaRateioVO);

            movContaVO.getMovContaRateios().get(0).setPlanoContaVO(new PlanoContaTO());
            movContaVO.getMovContaRateios().get(0).setCentroCustoVO(new CentroCustoTO());

            if (!formaPagamento.getTipoFormaPagamento().equals("CD")) {
                loteVo.setDescricao(movContaVO.getDescricao());
                loteVo.setValor(totalSelecionado);
                loteVo.setCartoes(new ArrayList<CartaoCreditoVO>());
                this.criaLote(extratoDiario, movContaVO, loteVo);
                movContaVO.setLote(loteVo);
            }

            //INCLUIR O LANÇAMENTO DA MOVIMENTAÇÃO, SEM VALIDAR DATA DE BLOQUEIO DO CAIXA
            movConta.incluirSemCommit(movContaVO, caixaVoEmAberto.getCodigo(), false, ComportamentoConta.BANCO, false, true);

            if (formaPagamento.getTipoFormaPagamento().equals("CD")) {
                this.alterarDataCompensacaoCartao(extratoDiario, diaProcessar);
            } else {
                this.inicializarHistorico(extratoDiario, movContaVO, diaProcessar);
            }

            this.atualizarExtrato(extratoDiario, movContaVO);
            Uteis.logarDebug("# Fim efetivarMovimentacao forma de pagamento: " + formaPagamento.getDescricao());
        } catch (Exception e) {
            Uteis.logarDebug("# ERRO efetivarMovimentacao: " + e.getMessage());
        }
    }


    private double getValorLiquido(FormaPagamentoVO formaPagamento) throws Exception {
        FormaPagamentoEmpresa formaPagamentoEmpresa = new FormaPagamentoEmpresa(con);
        double valorLiquido = 0.0;
        setTaxaAntecipacao(0.0);

        if (formaPagamento.getTipoFormaPagamento().equals("CA")) {
            FormaPagamentoEmpresa formaPag = new FormaPagamentoEmpresa(con);
            for (CartaoCreditoTO cartaoTO : getListaCartoes()) {
                if (cartaoTO.isCartaoEscolhido()) {
                    double taxa = formaPag.descrobrirTaxaApropriada(
                            cartaoTO.getNrVezes(),
                            cartaoTO.getDataLancamento(),
                            cartaoTO.getAdquirenteCod(),
                            cartaoTO.getOperadoraCodigo(),
                            cartaoTO.getFormaPagamentoVO(),
                            empresaVo.getCodigo());
                    if (cartaoTO.isAntecipacao()) {
                        setTaxaAntecipacao(cartaoTO.getTaxaCalculadaAntecipacao());
                        valorLiquido += cartaoTO.getValor() - (((taxa + getTaxaAntecipacao()) / 100) * cartaoTO.getValor());
                    } else {
                        valorLiquido += ((100 - taxa) / 100) * cartaoTO.getValor();
                    }
                }
            }
        } else if (formaPagamento.getTipoFormaPagamento().equals("CD")) {
            for (MovPagamentoVO movPagamentoVO : getListaOutros()) {
                if (movPagamentoVO.getMovPagamentoEscolhidaFinan()) {
                    double taxa = formaPagamentoEmpresa.descrobrirTaxaApropriada(0,
                            movPagamentoVO.getDataLancamento(),
                            movPagamentoVO.getAdquirenteVO().getCodigo(),
                            movPagamentoVO.getOperadoraCartaoVO().getCodigo(),
                            movPagamentoVO.getFormaPagamento(),
                            empresaVo.getCodigo()
                    );
                    if (movPagamentoVO.isAntecipacao()) {
                        setTaxaAntecipacao(movPagamentoVO.getTaxaAntecipacao());
                        valorLiquido += movPagamentoVO.getValor() - (((taxa + movPagamentoVO.getTaxaAntecipacao()) / 100) * movPagamentoVO.getValor());
                    } else {
                        valorLiquido += ((100 - taxa) / 100) * movPagamentoVO.getValor();
                    }
                }
            }
        }
        return valorLiquido;
    }

    public void gravarSaida(MovContaVO entrada, int caixaAberto, ContaVO origem, boolean transferindoOpenBank, Date diaProcessar) throws Exception {
        MovContaVO movContaVO = (MovContaVO) entrada.getClone(true);
        movContaVO.setTemLote(false);
        ContaVO contaVo = null;
        try {
            contaVo = conta.consultarPorChavePrimaria(entrada.getContaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        } catch (Exception ex) {
            if (!transferindoOpenBank) {
                throw new Exception(ex);
            }
        }
        movContaVO.setDescricao("Saída de valor - CONTA DESTINO : " + contaVo.getDescricao());
        movContaVO.setContaOrigem(null);
        movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.TRANSFERENCIA);
        movContaVO.setApresentarNoCaixa(false);
        movContaVO.setMovContaRateios(new ArrayList<MovContaRateioVO>());
        movContaVO.setContaVO(origem);
        movContaVO.setLiquido(false);
        movContaVO.setValorLiquido(0.0);
        movContaVO.setDataQuitacao(diaProcessar);
        MovContaRateioVO saida = new MovContaRateioVO();
        if (!entrada.getMovContaRateios().isEmpty()) {
            saida.setFormaPagamentoVO(entrada.getMovContaRateios().get(0).getFormaPagamentoVO());
        }
        saida.setTipoES(TipoES.SAIDA);
        saida.setDescricao(movContaVO.getDescricao());
        saida.setValor(movContaVO.getValor());
        movContaVO.getMovContaRateios().add(saida);
        movConta.incluir(movContaVO, caixaAberto, false, null);
    }

    private void atualizarExtrato(List<ExtratoDiarioItemVO> extratoDiario, MovContaVO movContaVO) throws Exception {

        for (ExtratoDiarioItemVO pagamento : extratoDiario) {
            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setCodigo(pagamento.getCodigoMovPagamento());
            movPagamento.alterarSomenteEntrouNoCaixa(movPagamentoVO, movContaVO.getCodigo());
        }
    }

    private void alterarDataCompensacaoCartao(List<ExtratoDiarioItemVO> extratoDiario, Date diaProcessar) throws Exception {
        for (ExtratoDiarioItemVO pagamento : extratoDiario) {
            movPagamento.alterarDataCompensacao(pagamento.getCodigoMovPagamento(), diaProcessar);
        }
    }

    private void inicializarHistorico(List<ExtratoDiarioItemVO> extratoDiario, MovContaVO movContaVO, Date diaProcessar) throws Exception {
        CartaoCredito cartaoCredito = new CartaoCredito(con);

        for (ExtratoDiarioItemVO pagamento : extratoDiario) {
            if (pagamento.getCartao() != null) {
                CartaoCreditoVO cartaoCreditoVO = cartaoCredito.consultarPorChavePrimaria(pagamento.getCartao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                HistoricoCartaoVO histCartao = new HistoricoCartaoVO();
                histCartao.setCartao(cartaoCreditoVO);
                histCartao.setDataInicio(diaProcessar);
                histCartao.getMovConta().setCodigo(movContaVO.getCodigo());
                histCartao.setLote(movContaVO.getLote());
                historicoCartao.incluir(histCartao);
            }
        }
    }

    private void criaLote(List<ExtratoDiarioItemVO> extratoDiario, MovContaVO movContaVO, LoteVO loteVO) throws Exception {
        for (ExtratoDiarioItemVO pagamento : extratoDiario) {
            if (pagamento.getCartao() != null) {
                CartaoCreditoVO cartaoCreditoVO = cartaoCredito.consultarPorChavePrimaria(pagamento.getCartao().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                loteVO.getCartoes().add(cartaoCreditoVO);
            }
        }
        lote.incluirSemCommit(loteVO, true);
    }

    public Boolean getUsarMovimentacao() {
        try {
            return getCfg().getUsarMovimentacaoContas();
        } catch (Exception e) {
            return true;
        }
    }

    public ConfiguracaoFinanceiroVO getCfg() {
        try {
            ConfiguracaoFinanceiroControle cfg = (ConfiguracaoFinanceiroControle) JSFUtilities.getFromSession(ConfiguracaoFinanceiroControle.class.getSimpleName());
            return cfg.getConfFinanceiro();
        } catch (Exception e) {
            return new ConfiguracaoFinanceiroVO();
        }
    }

    private void abrirCaixa(UsuarioVO usuario, EmpresaVO empresaVo, Date diaProcessar) throws Exception {
        Caixa caixa = new Caixa(con);
        if (UteisValidacao.emptyNumber(caixaVoEmAberto.getCodigo())) {
            caixaVoEmAberto.setUsuarioVo(usuario);
            caixaVoEmAberto.setEmpresaVo(empresaVo);
            caixaVoEmAberto.setDataAbertura(diaProcessar);
            caixaVoEmAberto.setDataTrabalho(diaProcessar);
            caixa.incluir(caixaVoEmAberto, false);
        }
    }

    private void fecharCaixa(CaixaVO caixaVoEmAberto, Date diaProcessar) throws Exception {
        Caixa caixa = new Caixa(con);
        caixaVoEmAberto.setDataFechamento(diaProcessar);
        caixaVoEmAberto.setResponsavelFechamento(caixaVoEmAberto.getUsuarioVo());
        caixa.alterar(caixaVoEmAberto);
    }

    private void excluirMovContaCasoJaTenhaSidoCriadaAnteriormente(Date diaProcessar) throws Exception {

        ResultSet resultSet = criarConsulta("select codigo  from movconta m where infoMovimentacaoAutomaticaConciliacao IS NOT NULL " +
                " AND infoMovimentacaoAutomaticaConciliacao <> '' " +
                " AND datalancamento::date = '" + Calendario.getDataAplicandoFormatacao(diaProcessar, "yyyy-MM-dd") + "'", con);
        int codigoMovConta = 0;
        int codigoLote = 0;
        while (resultSet.next()) {
            codigoMovConta = resultSet.getInt("codigo");
            String sqlDeleteMovconta = "delete from movconta m where codigo  = " + codigoMovConta + "";
            String sqlDeleteHistoricoCartao = "delete from historicocartao where movconta = " + codigoMovConta + "";
            String sqlDeleteCaixaMovconta = "delete from caixamovconta where movconta = " + codigoMovConta + "";

            ResultSet resultSetLot = criarConsulta("select * from historicocartao where movconta ='" + codigoMovConta + "'", con);
            while (resultSetLot.next()) {
                codigoLote = resultSetLot.getInt("lote");
                String sqlDeleteLote = "delete FROM lote where codigo =" + codigoLote + "";
                this.excluir(sqlDeleteHistoricoCartao);
                this.excluir(sqlDeleteCaixaMovconta);
                this.excluir(sqlDeleteMovconta);
                this.excluir(sqlDeleteLote);

            }
            this.excluir(sqlDeleteHistoricoCartao);
            this.excluir(sqlDeleteCaixaMovconta);
            this.excluir(sqlDeleteMovconta);
        }

    }

    public void excluir(String sql) throws SQLException {
        PreparedStatement sqlExcluir = this.con.prepareStatement(sql);
        sqlExcluir.execute();
    }

    public CaixaVO getCaixaVoEmAberto() {
        return caixaVoEmAberto;
    }

    public void setCaixaVoEmAberto(CaixaVO caixaVoEmAberto) {
        this.caixaVoEmAberto = caixaVoEmAberto;
    }

    public List<MovPagamentoVO> getListaOutros() {
        return listaOutros;
    }

    public void setListaOutros(List<MovPagamentoVO> listaOutros) {
        this.listaOutros = listaOutros;
    }

    public Double getTaxaAntecipacao() {
        return taxaAntecipacao;
    }

    public void setTaxaAntecipacao(Double taxaAntecipacao) {
        this.taxaAntecipacao = taxaAntecipacao;
    }

    public List<CartaoCreditoTO> getListaCartoes() {
        return listaCartoes;
    }

    public void setListaCartoes(List<CartaoCreditoTO> listaCartoes) {
        this.listaCartoes = listaCartoes;
    }

    public Connection getCon() {
        return null;
    }

    public void setCon(Connection con) {
    }

    public void close() throws Exception {
    }
}

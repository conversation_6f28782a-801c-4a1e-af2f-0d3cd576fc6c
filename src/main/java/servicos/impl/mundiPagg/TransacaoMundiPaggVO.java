package servicos.impl.mundiPagg;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/05/2020
 */
public class TransacaoMundiPaggVO extends TransacaoVO {

    @Override
    public String getValorCartaoMascarado() {
        return getCartaoMascarado();
    }

    @Override
    public String getResultadoRequisicao() {
        String retorno = obterValorLastTransaction("acquirer_message");
        if (UteisValidacao.emptyString(retorno) && getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA))  {
            return "Erro desconhecido, tente novamente mais tarde.";
        }
        return retorno;
    }

    @Override
    public String getAutorizacao() {
        return obterValorLastTransaction("acquirer_auth_code");
    }

    @Override
    public String getBandeira() {
        try {
            String brand = "";

            try {
                JSONObject lastTransaction = obterLastTransaction();
                if (lastTransaction != null) {
                    JSONObject card = lastTransaction.getJSONObject("card");
                    brand = card.optString("brand");
                }
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(brand)) {
                JSONObject obj = new JSONObject(getOutrasInformacoes());
                brand = obj.optString("cartaoBandeira");
            }
            return brand;
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getCartaoMascarado() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String card = obj.optString("cartaoMascarado");
            return APF.getCartaoMascarado(card);
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao) && !UteisValidacao.emptyString(getCodigoExterno())) {
            return getCodigoExterno();
        }
        if (nomeAtributo.equals(APF.CodigoAutorizacao)) {
            return getAutorizacao();
        }
        if (nomeAtributo.equals(APF.CartaoMascarado)) {
            return getCartaoMascarado();
        }
        return valor;
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) {
        return getResultadoRequisicaoCancelamento();
    }

    @Override
    public String getCodErroExterno() {
        return obterValorLastTransaction("acquirer_return_code");
    }

    @Override
    public String getNSU() {
        return obterValorLastTransaction("acquirer_nsu");
    }

    @Override
    public String getTID() {
        return obterValorLastTransaction("acquirer_tid");
    }

    private String getResultadoRequisicaoCancelamento() {
        String chargeStatus = "";
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            JSONArray charges = retornoJSON.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);
            chargeStatus = charge.optString("status");

            if (chargeStatus.equalsIgnoreCase("processing")) {
                return "A cobrança está em processamento, tente novamente mais tarde.";
            }
        } catch (Exception ex) {
        }

        try {
            JSONObject json = new JSONObject(getResultadoCancelamento());
            return json.optString("message");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getAdquirente(){
        return obterValorLastTransaction("acquirer_name");
    }

    private String obterValorLastTransaction(String key) {
        try {
            JSONObject lastTransaction = obterLastTransaction();
            if (lastTransaction != null) {
                return lastTransaction.optString(key);
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    private JSONObject obterLastTransaction() {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            JSONArray charges = retornoJSON.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);
            return charge.getJSONObject("last_transaction");
        } catch (Exception ex) {
            return null;
        }
    }
}

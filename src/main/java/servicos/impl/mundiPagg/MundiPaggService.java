package servicos.impl.mundiPagg;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.MundiPaggServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 06/05/2020
 */
public class MundiPaggService extends AbstractCobrancaOnlineServiceComum implements MundiPaggServiceInterface {

    private String urlAPI;
    private String secretKey;
    private String publicKey;

    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public MundiPaggService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() throws Exception {
        if (this.convenioCobrancaVO != null) {
            this.publicKey = this.convenioCobrancaVO.getCodigoAutenticacao01();
            this.secretKey = this.convenioCobrancaVO.getCodigoAutenticacao02();

            this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiMundipagg);

            //mundipagg é a mesma Url o que altera é os códigos de integração
            //validar tem a palavra "_test_"
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO) &&
                    (!this.convenioCobrancaVO.getCodigoAutenticacao01().toLowerCase().contains("_test_") ||
                            !this.convenioCobrancaVO.getCodigoAutenticacao02().toLowerCase().contains("_test_"))) {
                throw new Exception("Está em ambiente de " + AmbienteEnum.HOMOLOGACAO.getDescricao() + " e não contem a palavra \"_test_\" na chave informada no convênio.");
            } else if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO) &&
                    (this.convenioCobrancaVO.getCodigoAutenticacao01().toLowerCase().contains("_test_") ||
                            this.convenioCobrancaVO.getCodigoAutenticacao02().toLowerCase().contains("_test_"))) {
                throw new Exception("Está em ambiente de " + AmbienteEnum.PRODUCAO.getDescricao() + " porem as chave informada no convênio é do ambiente de testes.");
            }
        }
    }

    public boolean isPossuiConfiguracao() {
        return StringUtils.isNotBlank(this.secretKey) && StringUtils.isNotBlank(this.publicKey);
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoMundiPaggVO(), TipoTransacaoEnum.MUNDIPAGG, convenioCobrancaVO);
            transacaoDAO.incluir(transacao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa;
            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                pessoa = this.pessoaDAO.consultarPorChavePrimaria(clienteVO.getPessoaResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            } else {
                pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            }

            verificarAlteracaoPessoa(pessoa);

            JSONObject parametrosPagamento = criarOrderJSON(transacao, dadosCartao, pessoa);
            transacao.setParamsEnvio(removerDadosSigilososEnvio(parametrosPagamento));
            transacaoDAO.alterar(transacao);

            if (UteisValidacao.emptyString(pessoa.getIdMundiPagg())) {
                throw new Exception("Pessoa não incluída na Mundipagg");
            }

            validarDadosTransacao(transacao, dadosCartao);

            String retornoOrder = executarRequestMundiPagg("/orders", parametrosPagamento.toString(), MetodoHttpEnum.POST);
            processarRetornoOrder(transacao, retornoOrder);

            //consultar para verificar se já foi aprovada...
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                realizarConsultaSituacao(3, transacao);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private String removerDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        if (parametrosPagamento.has("payments")) {
            JSONArray payments = parametrosPagamento.getJSONArray("payments");
            if (payments.length() > 0 && payments.getJSONObject(0).has("credit_card")) {
                JSONObject credit_card = payments.getJSONObject(0).getJSONObject("credit_card");
                if (credit_card.has("card")) {
                    JSONObject card = credit_card.getJSONObject("card");
                    if (card.has("number")) {
                        card.put("number", APF.getCartaoMascarado(card.getString("number")));
                    }
                    if (card.has("cvv")) {
                        card.put("cvv", "***");
                    }
                }
            }
        }
        return parametrosPagamento.toString();
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            if (qtd > 0) {
                String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
                processarRetornoOrder(transacaoVO, retorno);
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    Thread.sleep(1000);
                    realizarConsultaSituacao(qtd - 1, transacaoVO);
                }
            }
        }
    }

    @Override
    public String consultarTransacao(String codigoExterno) throws Exception {
        return executarRequestMundiPagg("/orders/" + codigoExterno, null, MetodoHttpEnum.GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Mundipagg.");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        // atualiza situação da transação, caso ela esteja aguardando
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            retransmitirTransacao(transacaoVO, null, null);
        }
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetornoOrder(transacaoVO, retorno);
        }
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        String idCharge = obterIdCharge(transacaoVO);
        String retornoCharge = executarRequestMundiPagg("/charges/" + idCharge, null, MetodoHttpEnum.DELETE);
        processarRetornoChargeCancelamento(transacaoVO, retornoCharge);
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {

            estornarRecibo(transacaoVO, estornarRecibo);

            //caso tenha realizado o estorno e a transação ficou como ESTORNADA.
            //se ESTORNOU O Recibo fica como cancelada
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA) &&
                    UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoChargeCancelamento(TransacaoVO transacaoVO, String retornoCharge) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCharge, "processarRetornoChargeCancelamento");
        transacaoVO.setResultadoCancelamento(retornoCharge);
        try {
            JSONObject chargeJSON = new JSONObject(retornoCharge);
            String status = chargeJSON.getString("status");
            if (status.equalsIgnoreCase("canceled")) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private String obterIdCharge(TransacaoVO transacaoVO) {
        JSONObject retornoJSON = new JSONObject(transacaoVO.getParamsResposta());
        JSONObject charge = retornoJSON.getJSONArray("charges").getJSONObject(0);
        return charge.getString("id");
    }

    private void processarRetornoOrder(TransacaoVO transacao, String retorno) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetornoOrder");
        transacao.setParamsResposta(retorno);
        JSONObject retornoJSON = new JSONObject(retorno);

        if (retornoJSON.has("id")) {

            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                transacao.setCodigoExterno(retornoJSON.getString("id"));
            }

            JSONArray charges = retornoJSON.getJSONArray("charges");
            JSONObject charge = charges.getJSONObject(0);


            try {
                String statusLastTransaction = "";
                JSONObject lastTransactionJSON = charge.getJSONObject("last_transaction");

                //https://docs.mundipagg.com/reference#cobran%C3%A7as
                //Status da cobrança. Valores possíveis:
                //pending, paid, canceled, processing, failed, overpaid ou underpaid

                statusLastTransaction = lastTransactionJSON.getString("status");

//                String dataCobrancaString = charge.getString("paid_at");
//                Date dataCobranca = Uteis.getDate(dataCobrancaString, "yyyy-MM-dd'T'HH:mm:ss");
//                transacao.setDataCobranca(dataCobranca);
            } catch (Exception ignored) {
            }

            //IDENTIFICAR SE O PAGAMENTO FOI EM DINHEIRO PELO PORTAL DA Mundipagg
            try {
                String payment_method = charge.optString("payment_method");
                if (payment_method.equalsIgnoreCase("cash")) {
                    transacao.setPagamentoDinheiro(true);
                }
            } catch (Exception ignored) {
            }

            try { //IDENTIFICAR A OPERADORA DO CARTAO
                JSONObject lastTransactionJSON = charge.getJSONObject("last_transaction");
                JSONObject card = lastTransactionJSON.getJSONObject("card");
                String brand = card.optString("brand");
                if (!UteisValidacao.emptyString(brand)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = brand.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }


            //https://docs.mundipagg.com/reference#cobran%C3%A7as
            //Status da cobrança. Valores possíveis:
            //pending, paid, canceled, processing, failed, overpaid ou underpaid
            String status = retornoJSON.getString("status");
            if (status.equalsIgnoreCase("paid")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("pending") || status.equalsIgnoreCase("processing")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("canceled")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("failed")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }
        }
    }

    private JSONObject criarOrderJSON(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO) throws Exception {
        JSONObject payment = new JSONObject();

        String identificador = "TRA" + transacaoVO.getCodigo();
        Uteis.logarDebug("IDENTIFICADOR MUNDIPAGG: " + identificador);

        payment.put("code", identificador);
        payment.put("customer_id", pessoaVO.getIdMundiPagg());

        //Informa se o pedido será criado aberto ou fechado.
        payment.put("closed", true);

        //Indica se o pedido passará ou não pelo antifraude. Se não for enviado, será considerada a configuração da conta
        payment.put("antifraud_enabled", false);

        //Endereço IP do dispositivo que solicitou a compra
        if (cartaoCreditoTO.isTransacaoPresencial() && !UteisValidacao.emptyString(cartaoCreditoTO.getIpClientePacto())) {
            payment.put("ip", cartaoCreditoTO.getIpClientePacto());
        }

        payment.put("items", criarItemsJSON(cartaoCreditoTO));

        payment.put("payments", criarPaymentJSON(transacaoVO, cartaoCreditoTO, pessoaVO));
        return payment;
    }

    private JSONArray criarPaymentJSON(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO) throws Exception {
        JSONObject payment = new JSONObject();
        payment.put("payment_method", "credit_card");
        payment.put("credit_card", criarCreditCardJSON(transacaoVO, cartaoCreditoTO, pessoaVO));

        JSONArray payments = new JSONArray();
        payments.put(payment);
        return payments;
    }

    private JSONArray criarItemsJSON(CartaoCreditoTO cartaoCreditoTO) {
        JSONObject item = new JSONObject();
        int amount = (int) (cartaoCreditoTO.getValor() * 100);
        item.put("amount", amount);
        item.put("description", "PARCELA COBRANCA");
        item.put("quantity", 1);

        JSONArray items = new JSONArray();
        items.put(item);
        return items;
    }

    private JSONObject criarCreditCardJSON(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO) throws Exception {

        if (UteisValidacao.emptyNumber(cartaoCreditoTO.getParcelas())) {
            throw new Exception("Número de parcelas não informado");
        }

        JSONObject credit_card = new JSONObject();

        //Quantidade de parcelas. Valor padrão: 1.
        credit_card.put("installments", cartaoCreditoTO.getParcelas());

        //Texto exibido na fatura do cartão. Max: 22 caracteres.
        credit_card.put("statement_descriptor", formatarCampo(this.convenioCobrancaVO.getEmpresa().getRazaoSocialParaSoftDescriptor(cartaoCreditoTO.isTransacaoVerificarCartao()), 22));

        //_Indica se a transação deve ser capturada auth_and_capture, autorizada auth_only, ou pré autorizada pre_auth.
        //Valor padrão: auth_and_capture.
        credit_card.put("operation_type", "auth_and_capture");

        //Indica se é uma cobrança/pedido de recorrência. Valor padrão: false
        credit_card.put("recurrence", !cartaoCreditoTO.isTransacaoPresencial());


        String numeroCartao = "";
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            numeroCartao = cartaoCreditoTO.getNumero();
        }

        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(numeroCartao, cartaoCreditoTO, transacaoVO);

        String card_id = obterIdCard(numeroCartao, cartaoCreditoTO, pessoaVO);

        //utilizando uma id de um cartão já cadastrado na mundipagg
        if (!UteisValidacao.emptyString(card_id)) {
            credit_card.put("card_id", card_id);
        } else if (!UteisValidacao.emptyString(numeroCartao)) {
            credit_card.put("card", criarCardJSON(numeroCartao, cartaoCreditoTO));
        }

        return credit_card;
    }

    private JSONObject criarCardJSON(String numeroCartao, CartaoCreditoTO cartaoCreditoTO) {
        JSONObject card = new JSONObject();

        //REQUIRED Número do cartão. Entre 13 e 19 caracteres
        card.put("number", numeroCartao);

        //Nome do portador como está impresso no cartão. Máximo de 64 caracteres (Caracteres especiais e números não são aceitos)
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNomeTitular())) {
            card.put("holder_name", formatarCampo(Uteis.retirarAcentuacaoRegex(cartaoCreditoTO.getNomeTitular()), 64));
        }

        //CPF ou CNPJ do portador do cartão.
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getCpfCnpjSomenteNumeros())) {
            card.put("holder_document", cartaoCreditoTO.getCpfCnpjSomenteNumeros());
        }

        //REQUIRED Mês de validade do cartão. Valor entre 1 e 12 (inclusive)
        card.put("exp_month", cartaoCreditoTO.getMesValidade());

        //REQUIRED Ano de validade do cartão. Formatos yy ou yyyy. Ex: 23 ou 2023.
        card.put("exp_year", cartaoCreditoTO.getAnoValidade());

        //Código de segurança do cartão.
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
            card.put("cvv", cartaoCreditoTO.getCodigoSeguranca());
        }

        //Bandeira do cartão. Para cartões de crédito, temos como valores possíveis: Elo, Mastercard, Visa, Amex, JCB, Aura, Hipercard, Diners ou Discover.
        //Para voucher, temos como valores possíveis: Alelo, VR ou Sodexo.
        if (cartaoCreditoTO.getBand() != null) {
            String brand = cartaoCreditoTO.getBand().getDescricao();
            card.put("brand", brand);
        }

        return card;
    }

    private String obterIdCard(String numeroCartaoPassando, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoa) {
        AutorizacaoCobrancaCliente autoDAO = null;
        try {

            if (!UteisValidacao.emptyString(cartaoCreditoTO.getIdCardMundiPagg())) {
                return cartaoCreditoTO.getIdCardMundiPagg();
            }


            autoDAO = new AutorizacaoCobrancaCliente(getCon());
            List<AutorizacaoCobrancaClienteVO> autorizacoesCadastradas = autoDAO.consultarPorPessoaTipoAutorizacao(pessoa.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UteisValidacao.emptyList(autorizacoesCadastradas)) {
                //se não tem autorização de cobrança cadastrada retornar nulll
                return null;
            }

            String numeroCartaoPassandoMascarado = APF.getCartaoMascarado(numeroCartaoPassando);

            //buscar a autorização de cobrança referente ao cartão que está sendo utilizado.
            AutorizacaoCobrancaClienteVO auto = null;
            for (AutorizacaoCobrancaClienteVO obj : autorizacoesCadastradas) {
                if (obj.getCartaoMascarado().equalsIgnoreCase(numeroCartaoPassandoMascarado)) {
                    if (!UteisValidacao.emptyString(obj.getIdCardMundiPagg())) {
                        return obj.getIdCardMundiPagg();
                    } else {
                        auto = obj;
                        break;
                    }
                }
            }

            //tem autorização de cobrança.. então vou enviar o cartão para a Mundipagg..
            if (auto != null) {

                //se não tiver o id do cliente na mundipagg..
                if (UteisValidacao.emptyString(pessoa.getIdMundiPagg())) {
                    return null;
                }

                JSONObject card = criarCardJSON(numeroCartaoPassando, cartaoCreditoTO);

                Uteis.logar(null, "Vou cadastrar cartão Mundipagg... " + numeroCartaoPassandoMascarado);

                String resposta = executarRequestMundiPagg("/customers/" + pessoa.getIdMundiPagg() + "/cards", card.toString(), MetodoHttpEnum.POST);
                JSONObject respostaJSON = new JSONObject(resposta);
                String idCardMundipagg = respostaJSON.optString("id");
                if (!UteisValidacao.emptyString(idCardMundipagg)) {
                    auto.setIdCardMundiPagg(idCardMundipagg);
                    autoDAO.alterarIdCardMundiPagg(auto);
                    return auto.getIdCardMundiPagg();
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            autoDAO = null;
        }
    }

    private void verificarAlteracaoPessoa(PessoaVO pessoa) throws Exception {
        if (dadosDesatualizados(pessoa)) {
            incluirPessoa(pessoa);
        }
    }

    private Boolean dadosDesatualizados(PessoaVO pessoa) throws Exception {
        boolean dadosDesatualizados = UteisValidacao.emptyString(pessoa.getIdMundiPagg());
        if (!dadosDesatualizados) {
            LogVO ultimoLog = this.logDAO.consultarUltimoLogPessoa(pessoa.getCodigo(), "CLIENTE", "PESSOA", "CLIENTE - EMPRESA");
            if (ultimoLog != null) {
                dadosDesatualizados = pessoa.getDataAlteracaoMundiPagg().getTime() < ultimoLog.getDataAlteracao().getTime();
            } else {
                dadosDesatualizados = true;
            }
        }
        return dadosDesatualizados;
    }

    @Override
    public void incluirPessoa(PessoaVO pessoa) throws Exception {

        JSONObject customerJSON = criarCustomerJSON(pessoa);
        JSONObject resposta = null;

        if (UteisValidacao.emptyString(pessoa.getIdMundiPagg())) {
            //incluir
            resposta = new JSONObject(executarRequestMundiPagg("/customers", customerJSON.toString(), MetodoHttpEnum.POST));
        } else {
            //editar
            resposta = new JSONObject(executarRequestMundiPagg("/customers/" + pessoa.getIdMundiPagg(), customerJSON.toString(), MetodoHttpEnum.PUT));
            if (resposta.has("errors")) {
                pessoa.setIdMundiPagg(null);
                resposta = new JSONObject(executarRequestMundiPagg("/customers", customerJSON.toString(), MetodoHttpEnum.POST));
            }
        }

        if (resposta.has("errors")) {
            try {
                resposta = tentarBuscarPessoaParaAtualizar(pessoa, customerJSON);
            } catch (Exception e) {
                throw new ConsistirException(e.getMessage() + " " + resposta.get("errors").toString());
            }
        }

        if (resposta.has("errors") || (!resposta.has("id") && resposta.has("message"))) {
            if (resposta.has("message")) {
                throw new ConsistirException("Mundipagg: " + resposta.getString("message"));
            } else {
                throw new ConsistirException("Falha ao inserir a pessoa na Mundipagg. " + resposta.get("errors").toString());
            }
        } else {
            pessoa.setIdMundiPagg(getIdMundiPagg(resposta));
            new Pessoa(getCon()).alterarIdMundiPagg(pessoa);
        }
    }

    private String getIdMundiPagg(JSONObject resposta) {
        String id = null;
        if (resposta.has("id")) {
            id = resposta.getString("id");
        } else if (resposta.has("customer")) {
            JSONObject customer = resposta.getJSONObject("customer");
            id = customer.getString("id");
        }
        return id;
    }

    private JSONObject tentarBuscarPessoaParaAtualizar(PessoaVO pessoa, JSONObject customerJSON) throws Exception {
        StringBuilder url = new StringBuilder("/customers?");
        if (!UteisValidacao.emptyString(pessoa.getCfp())) {
            url.append("document=").append(Uteis.removerMascara(pessoa.getCfp()));
        } else if (!UteisValidacao.emptyString(pessoa.getIdMundiPagg())) {
            url.append("code=").append(pessoa.getIdMundiPagg());
        } else {
            url.append("code=P").append(pessoa.getCodigo());
        }

        boolean encontrou = false;
        JSONObject retorno = new JSONObject(executarRequestMundiPagg(url.toString(), null, MetodoHttpEnum.GET));
        if (retorno.has("data")) {
            JSONArray customers = retorno.getJSONArray("data");
            if (customers.length() > 0) {
                for (int i = 0; i < customers.length(); i++) {
                    JSONObject customer = customers.getJSONObject(i);

                    String id = customer.optString("id");
                    if (id.equals(customerJSON.optString("code"))) {
                        retorno = new JSONObject(executarRequestMundiPagg("/customers/" + customer.getInt("id"), customerJSON.toString(), MetodoHttpEnum.PUT));
                        encontrou = true;
                        break;
                    }
                }
                if (!encontrou) {//caso de importação onde o code pode não ser o informado pelo zw.
                    JSONObject customer = customers.getJSONObject(0);
                    retorno = new JSONObject(executarRequestMundiPagg("/customers/" + customer.getInt("id"), customerJSON.toString(), MetodoHttpEnum.PUT));
                    encontrou = true;
                }
            }
        }
        if (!encontrou) {
            throw new ConsistirException("Falha ao inserir a pessoa na Mundipagg");
        }

        return retorno;
    }

    private String executarRequestMundiPagg(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = this.urlAPI + endPoint;

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + new String(new Base64().encode((this.secretKey + ":").getBytes())));

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        return respostaHttpDTO.getResponse();
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }

    private JSONObject criarCustomerJSON(PessoaVO pessoaVO) throws Exception {

        if (!SuperVO.verificaCPF(pessoaVO.getCfp())) {
            throw new Exception("O CPF cadastrado para essa pessoa é inválido");
        }

        JSONObject customer = new JSONObject();
        customer.put("name", formatarCampo(Uteis.retirarAcentuacaoRegex(pessoaVO.getNome()), 64)); //Max: 64 characters.
        customer.put("code", "P" + pessoaVO.getCodigo()); // Max: 52 characters

        String cpfCNPJ = Uteis.removerMascara(pessoaVO.getCfp());
        if (!UteisValidacao.emptyString(cpfCNPJ)) {
            if (cpfCNPJ.length() > 11) {
                customer.put("type", "Company");
            } else {
                customer.put("type", "individual");
            }

            if (cpfCNPJ.length() <= 16) {
                customer.put("document", cpfCNPJ); //Max: 16 characters
            }
        }

        String email = obterEmailPessoa(pessoaVO);
        String telefoneCelular = obterTelefonePessoa(TipoTelefoneEnum.CELULAR, pessoaVO);
        String telefoneResidencial = obterTelefonePessoa(TipoTelefoneEnum.RESIDENCIAL, pessoaVO);
        String telefoneQualquer = obterTelefonePessoa(null, pessoaVO);
        EnderecoVO enderecoVO = obterEnderecoPessoa(pessoaVO);

        if (!UteisValidacao.emptyString(email)) {
            customer.put("email", formatarCampo(email, 64)); //Max: 64 characters
        }

        //Possible values: male or female
        if (pessoaVO.getSexo().equals("M")) {
            customer.put("gender", "male");
        } else if (pessoaVO.getSexo().equals("F")) {
            customer.put("gender", "female");
        }

//        JSONObject address = criarAddressJSON(pessoaVO, enderecoVO);
//        if (address != null) {
//            customer.put("address", address);
//        }


        return customer;
    }

    private JSONObject criarAddressJSON(PessoaVO pessoaVO, EnderecoVO enderecoVO) {
        if (enderecoVO != null && !UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            JSONObject address = new JSONObject();

            String line_1 = formatarCampo(enderecoVO.getEndereco() + " " + enderecoVO.getBairro() + " " + enderecoVO.getNumero(), 256);
            if (UteisValidacao.emptyString(line_1.trim())) {
                return null;
            }

            String zip_code = Uteis.removerMascara(enderecoVO.getCep());
            if (UteisValidacao.emptyString(zip_code.trim())) {
                return null;
            }
            address.put("line_1", line_1); //REQUIRED Max: 256 characters.
            address.put("line_2", formatarCampo(enderecoVO.getComplemento(), 128));//Max: 128 characters.
            address.put("zip_code", zip_code); //REQUIRED Max: 16 characters.
            address.put("city", ""); //REQUIRED Max: 64 characters.
            address.put("state", ""); //REQUIRED State code in ISO 3166-2 format.
            address.put("country", "BR"); //REQUIRED Country code in ISO 3166-1 alpha-2 format.

            return address;
        } else {
            return null;
        }
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    private String obterTelefonePessoa(TipoTelefoneEnum tipoTelefoneEnum, PessoaVO pessoaVO) {
        for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
            if (UteisValidacao.validaTelefone(telefoneVO.getNumero()) && (tipoTelefoneEnum == null || tipoTelefoneEnum.getCodigo().equals(telefoneVO.getTipoTelefone()))) {
                return Uteis.tirarCaracteres(telefoneVO.getNumero(), true);
            }
        }
        return "";
    }

    private EnderecoVO obterEnderecoPessoa(PessoaVO pessoaVO) {
        for (EnderecoVO enderecoVO : pessoaVO.getEnderecoVOs()) {
            return enderecoVO;
        }
        return new EnderecoVO();
    }
}

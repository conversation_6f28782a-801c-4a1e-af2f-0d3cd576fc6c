package servicos.impl.stoneV5;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/01/2025
 */

public enum TypePayableStoneEnum {

    NENHUM("NENHUM", ""),
    CREDIT("credit", "Crédito"),
    REFUND("refund", "Estorno"),
    REFUND_REVERSAL("refund_reversal", "Cancelamento de Estorno"),
    CHARGEBACK("chargeback", "ChargeBack"),
    CHARGEBACK_REFUND("chargeback_refund", "Estorno de Chargeback");



    private String id;
    private String descricao;

    TypePayableStoneEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TypePayableStoneEnum obterPorId(String id) {
        for (TypePayableStoneEnum stone : TypePayableStoneEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return NENHUM;
    }
}

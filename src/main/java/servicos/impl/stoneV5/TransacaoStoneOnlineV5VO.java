package servicos.impl.stoneV5;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.RetornoAbecsBandeirasEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/12/2024
 */
public class TransacaoStoneOnlineV5VO extends TransacaoVO {

    @Override
    public String getValorCartaoMascarado() {
        return getCartaoMascarado();
    }

    @Override
    public String getResultadoRequisicao() {
        try {
            if (isTransacaoVerificarCartao()) {
                if (getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
                    return "Cartão verificado com sucesso!";
                }
            }
            if (getSituacao().equals(SituacaoTransacaoEnum.CANCELADA)) {
                return "Transação cancelada com sucesso!";
            }
            if (erroAntifraud()) {
                return "Transação recusada pelo sistema de antifraude da Stone";
            }
            JSONObject json = new JSONObject(getParamsResposta());
            if (json.has("errors")) {
                String message = json.getJSONObject("errors").toString();
                return traduzirErro(message);
            } else if (json.has("charges")) {
                JSONObject obj = (JSONObject) json.getJSONArray("charges").get(0);
                if (obj.has("last_transaction") && obj.getJSONObject("last_transaction").has("gateway_response") &&
                        obj.getJSONObject("last_transaction").getJSONObject("gateway_response").has("errors")) {
                    String message = "";
                    if (obj.getJSONObject("last_transaction").getJSONObject("gateway_response").getJSONArray("errors").length() > 0) {
                        message = new JSONObject(obj.getJSONObject("last_transaction").getJSONObject("gateway_response").getJSONArray("errors").get(0).toString()).getString("message");
                    } else if (!UteisValidacao.emptyString(obj.getJSONObject("last_transaction").optString("acquirer_return_code"))) {
                        String code = obj.optJSONObject("last_transaction").getString("acquirer_return_code");
                        if (code.length() == 2) { //MODELO GATEWAY (retorno da bandeira) pega do enum RetornoAbecsBandeirasEnum
                            OperadorasExternasAprovaFacilEnum bandeira = getBandeiraPagamento() != null ? getBandeiraPagamento() : getBandeiraEnum();
                            if (bandeira != null) {
                                String motivoDescricao = RetornoAbecsBandeirasEnum.obterMotivoPorCodigoEBandeira(code, bandeira);
                                String acquirer_message = obj.optJSONObject("last_transaction").optString("acquirer_message");
                                boolean motivoNaoFoiInformado = !UteisValidacao.emptyString(motivoDescricao) &&  motivoDescricao.contains("Motivo não informado para a bandeira.");
                                if (motivoNaoFoiInformado && !UteisValidacao.emptyString(acquirer_message)) {
                                    return acquirer_message;
                                } else {
                                    return motivoDescricao;
                                }
                            } else {
                                return "Não foi possível obter a bandeira pra descobrir o motivo da transação";
                            }
                        } else if (code.length() == 4) { //MODELO PSP pega do enum StoneOnlineV5RetornoPSPEnum
                            StoneOnlineV5RetornoPSPEnum stoneOnlineV5RetornoPSPEnum = StoneOnlineV5RetornoPSPEnum.valueOff(code);
                            if (!stoneOnlineV5RetornoPSPEnum.equals(StoneOnlineV5RetornoPSPEnum.StatusNENHUM)) {
                                return stoneOnlineV5RetornoPSPEnum.getDescricao();
                            }
                        } else {
                            return "A Stone ou a bandeira não nos informou o motivo da transação negada";
                        }
                    }

                    if (!UteisValidacao.emptyString(message)) {
                        if (message.contains("with status refused is not allowed to create a transaction")) {
                            return "Inconsistência nos recebedores. Você deve regularizar o cadastro dos recebedores lá no portal da Pagar.me e só depois tentar novamente.";
                        }
                        return message;
                    }
                }
            } else if (json.has("message")) {
                String msg = json.getString("message");
                return traduzirErro(msg);
            }
        } catch (Exception ignored) {
        }
        return "A Stone ou a bandeira não nos informou o motivo da transação negada";
    }

    private String traduzirErro(String erro) {
        if (erro.contains("Card expired")) {
            return "O cartão informado está expirado/vencido.";
        }
        if (erro.contains("The number field is not a valid card number")) {
            return "O número do cartão informado é inválido!";
        }
        if (erro.contains("with status refused is not allowed to create a transaction")) {
            return "Inconsistência nos recebedores. Você deve regularizar o cadastro dos recebedores no portal da Pagar.me e só depois tentar novamente.";
        }
        if (erro.toUpperCase().contains("SPLIT IS DISABLED")) {
            return "O Split não está disponível para as credenciais cadastradas neste convênio de cobrança!";
        } else if (erro.toUpperCase().contains("CUSTOMER NOT FOUND")) {
            return "O Customer_id atual do aluno não existe no portal da Stone! Verifique as credenciais do seu convênio.";
        }
        return erro; // Se não encontrar tradução, retorna o erro original
    }

    @Override
    public String getAutorizacao() {
        if (erroAntifraud()) {
            return "";
        } else {
            return obterValorParamsRespostaChargeLastTransaction("acquirer_auth_code");
        }
    }

    @Override
    public String getBandeira() {
        try {
            String brand = "";

            try {
                brand = obterValorParamsRespostaChargeLastTransactionCard("brand");
                if (!UteisValidacao.emptyString(brand)) {
                    return brand.toUpperCase();
                }
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(brand)) {
                brand = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
            }
            return brand.toUpperCase();
        } catch (Exception ex) {
            return "";
        }
    }

    public OperadorasExternasAprovaFacilEnum getBandeiraEnum() {
        try {
            String brand = "";

            try {
                brand = obterValorParamsRespostaChargeLastTransactionCard("brand");
                if (!UteisValidacao.emptyString(brand)) {
                    return OperadorasExternasAprovaFacilEnum.obterPorDescricao(brand.toUpperCase());
                }
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(brand)) {
                brand = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
            }
            return OperadorasExternasAprovaFacilEnum.obterPorDescricao(brand.toUpperCase());
        } catch (Exception ex) {
            return null;
        }
    }

    @Override
    public String getCartaoMascarado() {
        try {
            String first_six_digits = "";
            String last_four_digits = "";
            if (isTransacaoVerificarCartao()) {
                first_six_digits = obterValorParamsResposta("first_six_digits");
                last_four_digits = obterValorParamsResposta("last_four_digits");
            } else {
                first_six_digits = obterValorParamsRespostaChargeLastTransactionCard("first_six_digits");
                last_four_digits = obterValorParamsRespostaChargeLastTransactionCard("last_four_digits");
            }
            if (!UteisValidacao.emptyString(first_six_digits) && !UteisValidacao.emptyString(last_four_digits)) {
                return first_six_digits + "******" + last_four_digits;
            } else if (!UteisValidacao.emptyString(getParamsEnvio()) && getParamsEnvio().contains("payments") && getParamsEnvio().contains("number")) {
                JSONObject envioJSON = new JSONObject(getParamsEnvio());
                JSONObject json = (JSONObject) envioJSON.optJSONArray("payments").get(0);
                String card = json.optJSONObject("credit_card").optJSONObject("card").optString("number");
                return card;
            } else if (!UteisValidacao.emptyString(getParamsResposta()) && getParamsResposta().contains("request") && getParamsResposta().contains("last_four_digits")) {
                //verificações de cartão que são negadas caem aqui pois só vem no retorno o last_four_digits e vem o objeto diferente "request".
                last_four_digits = new JSONObject(getParamsResposta()).getJSONObject("request").optString("last_four_digits");
                return "************" + last_four_digits;
            }
            String cardString = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
            return APF.getCartaoMascarado(cardString);
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao) && !UteisValidacao.emptyString(getCodigoExterno())) {
            return getCodigoExterno();
        }
        if (nomeAtributo.equals(APF.CodigoAutorizacao)) {
            return getAutorizacao();
        }
        if (nomeAtributo.equals(APF.CartaoMascarado)) {
            return getCartaoMascarado();
        }
        return valor;
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) {
        return "";
    }

    @Override
    public String getCodErroExterno() {
        if (erroAntifraud()) {
            return "";
        } else {
            return obterValorParamsRespostaChargeLastTransaction("acquirer_return_code");
        }
    }

    @Override
    public String getNSU() {
        if (erroAntifraud()) {
            return "";
        } else {
            return obterValorParamsRespostaChargeLastTransaction("acquirer_nsu");
        }
    }

    @Override
    public String getTID() {
        return obterValorParamsRespostaChargeLastTransaction("acquirer_tid");
    }

    private String getResultadoRequisicaoCancelamento() {
        return "No foi possvel realizar o cancelamento. Tente novamente mais tarde.";
    }

    public String getAdquirente() {
        return obterValorParamsRespostaChargeLastTransaction("acquirer_name");
    }

    private String obterValorParamsResposta(String key) {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            return retornoJSON.optString(key);
        } catch (Exception ex) {
            return "";
        }
    }

    private String obterValorParamsRespostaChargeLastTransaction(String key) {
        try {
            JSONObject obj = (JSONObject) new JSONObject(getParamsResposta()).getJSONArray("charges").get(0);
            if (obj.has("last_transaction")) {
                return obj.getJSONObject("last_transaction").optString(key);
            }
        } catch (Exception ex) {
            return "";
        }
        return "";
    }

    private String obterValorParamsRespostaChargeLastTransactionCard(String key) {
        try {
            JSONObject obj = (JSONObject) new JSONObject(getParamsResposta()).getJSONArray("charges").get(0);
            if (obj.has("last_transaction")) {
                JSONObject card = obj.getJSONObject("last_transaction").getJSONObject("card");
                return card.optString(key);
            }
        } catch (Exception ex) {
            return "";
        }
        return "";
    }

    private boolean erroAntifraud() {
        //verifica se o motivo de negar a transação foi o antifraude
        try {
            JSONObject obj = (JSONObject) new JSONObject(getParamsResposta()).getJSONArray("charges").get(0);
            if (obj.has("last_transaction") &&
                    obj.getJSONObject("last_transaction").optJSONObject("antifraud_response").length() > 0 &&
                    !obj.getJSONObject("last_transaction").optJSONObject("antifraud_response").optString("status").equals("approved")) {
                return true;
            }
        } catch (Exception ignored) {
            return false;
        }
        return false;
    }

    public Integer getNrVezes() {
        try {
            return Integer.valueOf(obterValorParamsRespostaChargeLastTransaction("installments"));
        } catch (Exception ex) {
            return 0;
        }
    }

    public String getIdentificadorPacto() {
        String identificador = "";
        try {
            JSONObject jsonEnvio = new JSONObject(getParamsEnvio());
            identificador = jsonEnvio.optString("reference_key");
        } catch (Exception ignored) {
        }

        if (!UteisValidacao.emptyString(identificador)) {
            return identificador;
        }

        identificador = obterItemOutrasInformacoes(AtributoTransacaoEnum.identificadorPacto);
        if (!UteisValidacao.emptyString(identificador)) {
            return identificador;
        }
        return "";
    }
}

package servicos.impl.stoneV5;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 25/01/2025
 */
public enum OriginatorModelPayableStoneEnum {

    NENHUM("NENHUM"),
    REFUND("REFUND"),
    CHARGEBACK("CHARGEBACK"),
    ;

    private String id;

    OriginatorModelPayableStoneEnum(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }


    public static OriginatorModelPayableStoneEnum obterPorId(String id) {
        for (OriginatorModelPayableStoneEnum stone : OriginatorModelPayableStoneEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return NENHUM;
    }
}

package servicos.impl.stoneV5;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.GeradorCPF;
import br.com.pactosolucoes.comuns.util.GeradorTelefone;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.github.javafaker.Faker;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.impl.gatewaypagamento.RecebedorDTO;
import servicos.impl.pagarMe.PagarMeRetornoEnum;
import servicos.interfaces.StoneOnlineV5ServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 14/12/2024
 */
public class StoneOnlineV5Service extends AbstractCobrancaOnlineServiceComum implements StoneOnlineV5ServiceInterface {

    private String urlAPI;
    private String chaveAPI;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private boolean usaSplitPagamentoStoneV5;

    public StoneOnlineV5Service(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    public StoneOnlineV5Service(Connection con, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = convenioCobrancaVO;
        popularInformacoes();
    }

    private void popularInformacoes() throws Exception {
        if (this.convenioCobrancaVO != null) {
            this.usaSplitPagamentoStoneV5 = this.convenioCobrancaVO.isUsaSplitPagamentoStoneV5();
            this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiStone_V5);

            if (usaSplitPagamentoStoneV5) {
                this.chaveAPI = this.convenioCobrancaVO.getCodigoAutenticacao04(); //chave de API (PSP) para split de pagamento
            } else {
                this.chaveAPI = this.convenioCobrancaVO.getCodigoAutenticacao03(); //chave de API (GATEWAY) sem split de pagamento
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoStoneOnlineV5VO(), TipoTransacaoEnum.DCC_STONE_ONLINE_V5, convenioCobrancaVO);
            transacaoDAO.incluir(transacao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                PessoaCPFTO pessoaCPFTO = transacaoDAO.obterDadosPessoaPagador(pessoa, false, true);

                //usar CPF do responsável
                pessoa.setCfp(pessoaCPFTO.getCpfResponsavel());
            }

            incluirOuAtualizarPessoa(pessoa, transacao);

            JSONObject parametrosPagamento = montarBodyJSONEnvio(transacao, dadosCartao, pessoa);
            transacao.setParamsEnvio(removerDadosSigilososEnvio(parametrosPagamento));
            transacaoDAO.alterar(transacao);

            if (UteisValidacao.emptyString(pessoa.getIdStone())) {
                throw new Exception("Pessoa não incluída na Stone");
            }

            validarDadosTransacao(transacao, dadosCartao);

            String retorno = "";

            boolean verificacaoZeroDollar = dadosCartao.isTransacaoVerificarCartao() && dadosCartao.isVerificacaoZeroDollar();

            if (verificacaoZeroDollar) { //verificar cartão é no zero dollar, endpoint diferente do de venda
                String bodyEnvioVerificacao = montarCardJSON(dadosCartao, pessoa).toString();
                RespostaHttpDTO respostaHttpDTO = executarRequestStone_V5_ReturningHttp("/customers/" + pessoa.getIdStone() + "/cards", bodyEnvioVerificacao, MetodoHttpEnum.POST);
                processarRetornoVerificacaoZeroDollar(transacao, respostaHttpDTO);
            } else {
                retorno = executarRequestStone_V5("/orders", parametrosPagamento.toString(), MetodoHttpEnum.POST);
                processarRetorno(transacao, retorno);

                //consultar para verificar se já foi aprovada...
                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    realizarConsultaSituacao(3, transacao);
                }
            }

            //Tratamento para quando houver erro ao tentar enviar cartão com bandeira Discover
            if(transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) && !UteisValidacao.emptyString(transacao.getCodigoRetornoGestaoTransacaoMotivo())
                && transacao.getCodigoRetornoGestaoTransacaoMotivo().contains("Esta loja não possui um meio de pagamento configurado para a bandeira")) {
                try {
                    Uteis.logar("Tentando excluir o cartão pois houve um erro ao mesmo ser identificado como Bandeira não existente no request Stone V5.");
                    String retornoExclusao = executarRequestStone_V5("/customers/" + pessoa.getIdStone() + "/cards/" + transacao.getCartaoCreditoTO().getIdCardStoneV5(), null, MetodoHttpEnum.DELETE);

                    if(!UteisValidacao.emptyString(retornoExclusao)) {
                        boolean cartaoExcluido = validarCartaoExcluido(retornoExclusao);
                        if(cartaoExcluido) {
                            Uteis.logar("O cartão foi excluído com sucesso na API da Stone V5.");
                        }else{
                            Uteis.logar("Não foi possível excluir o cartão na API da Stone V5.");
                        }
                    }else{
                        Uteis.logar("Não foi possível excluir o cartão na API da Stone V5.");
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                    Uteis.logar("Não foi possível excluir o cartão.");
                }
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private void processarRetornoVerificacaoZeroDollar(TransacaoVO transacao, RespostaHttpDTO respostaHttpDTO) {
        incluirHistoricoRetornoTransacao(transacao, respostaHttpDTO.getResponse(), "processarRetornoVerificacaoZeroDollar");
        transacao.setParamsResposta(respostaHttpDTO.getResponse());

        if (respostaHttpDTO.getHttpStatus() == 200 || respostaHttpDTO.getHttpStatus() == 201) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private String removerDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());

        if (parametrosPagamento.has("payments")) {
            JSONObject payment = (JSONObject) parametrosPagamento.getJSONArray("payments").get(0);
            JSONObject card = payment.getJSONObject("credit_card").getJSONObject("card");
            card.put("number", APF.getCartaoMascarado(card.getString("number")));
            if (card.has("card_cvv")) {
                card.put("card_cvv", "***");
            }
        }

        return parametrosPagamento.toString();
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO) throws Exception {
        if (qtd > 0) {
            try {
                try {
                    consultarSituacaoCobrancaTransacao(transacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    Thread.sleep(2000);
                    realizarConsultaSituacao(qtd - 1, transacaoVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public String consultarTransacao(String codigoExterno) throws Exception {
        return executarRequestStone_V5("/orders/" + codigoExterno, null, MetodoHttpEnum.GET);
    }

    private String consultarTransacaoReference(String referenceKey) throws Exception {
        return executarRequestStone_V5("/transactions?api_key=" + this.chaveAPI + "&reference_key=" + referenceKey, null, MetodoHttpEnum.GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Pagar.Me");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        // atualiza situação da transação, caso ela esteja aguardando
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) && !transacaoVO.isTransacaoVerificarCartao()) {
            retransmitirTransacao(transacaoVO, null, null);
        }
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno);
        } else if (!UteisValidacao.emptyString(transacaoVO.getIdentificadorPacto())) {
            String retorno = consultarTransacaoReference(transacaoVO.getIdentificadorPacto());
            try {
                JSONArray jsonArray = new JSONArray(retorno);
                if (transacaoVO.isVerificador() && jsonArray.length() == 0) {

                    transacaoVO.setCodigoRetorno(PagarMeRetornoEnum.Status8888.getId());
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.COM_ERRO);

                    String mensagemErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.mensagemErro);
                    String msgErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.msgErro);
                    String erroGenericoTransacao = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao);

                    String msgGravar = PagarMeRetornoEnum.Status8888.getDescricao();

                    if (UteisValidacao.emptyString(mensagemErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.mensagemErro, msgGravar);
                    } else if (UteisValidacao.emptyString(msgErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.msgErro, msgGravar);
                    } else if (UteisValidacao.emptyString(erroGenericoTransacao)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao, msgGravar);
                    }
                    return;
                } else if (jsonArray.length() > 0) {
                    retorno = jsonArray.getJSONObject(0).toString();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            processarRetorno(transacaoVO, retorno);
        } else {
            throw new Exception("Sem código referencia para consultar a transação");
        }
    }

    public void consultarSituacaoCobrancaTransacaoCancelamento(TransacaoVO transacaoVO, boolean fluxoCancelamento) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno);
        } else if (!UteisValidacao.emptyString(transacaoVO.getIdentificadorPacto())) {
            String retorno = consultarTransacaoReference(transacaoVO.getIdentificadorPacto());
            try {
                JSONArray jsonArray = new JSONArray(retorno);
                if (transacaoVO.isVerificador() && jsonArray.length() == 0) {

                    transacaoVO.setCodigoRetorno(PagarMeRetornoEnum.Status8888.getId());
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.COM_ERRO);

                    String mensagemErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.mensagemErro);
                    String msgErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.msgErro);
                    String erroGenericoTransacao = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao);

                    String msgGravar = PagarMeRetornoEnum.Status8888.getDescricao();

                    if (UteisValidacao.emptyString(mensagemErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.mensagemErro, msgGravar);
                    } else if (UteisValidacao.emptyString(msgErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.msgErro, msgGravar);
                    } else if (UteisValidacao.emptyString(erroGenericoTransacao)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao, msgGravar);
                    }
                    return;
                } else if (jsonArray.length() > 0) {
                    retorno = jsonArray.getJSONObject(0).toString();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            processarRetorno(transacaoVO, retorno);
        } else {
            throw new Exception("Sem código referencia para consultar a transação");
        }
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        JSONObject body = new JSONObject();
        body.put("api_key", this.chaveAPI);

        int amount = (int) (transacaoVO.getValor() * 100);
        body.put("amount", amount);
        String retornoCancelamento = executarRequestStone_V5("/charges/" + transacaoVO.getCodigoExterno2(), body.toString(), MetodoHttpEnum.DELETE);
        if (retornoCancelamento.contains("Valor inválido para estorno") && transacaoVO.isTransacaoVerificarCartao()) {
            int retentativaCancelamento = 0;
            String retornoRetentativaCancelamento = retornoCancelamento; // Inicializa com o retorno original

            //fazer 3 retentativas de cancelar a transação
            while (retentativaCancelamento <= 3 && retornoRetentativaCancelamento.contains("Valor inválido para estorno")) {
                Thread.sleep(3000);
                retentativaCancelamento++;
                retornoRetentativaCancelamento = executarRequestStone_V5("/charges/" + transacaoVO.getCodigoExterno2(), body.toString(), MetodoHttpEnum.DELETE);
                if (!retornoRetentativaCancelamento.contains("Valor inválido para estorno")) {
                    break;
                }
            }
            retornoCancelamento = retornoRetentativaCancelamento;
        }
        processarRetornoCancelamento(transacaoVO, retornoCancelamento);
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, false);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
        transacaoVO.setResultadoCancelamento(retornoCancelamento);
        try {
            JSONObject cancelamentoJSON = new JSONObject(retornoCancelamento);
            if (cancelamentoJSON.has("status")) {
                String status = cancelamentoJSON.getString("status");
                if (status.equalsIgnoreCase("canceled")) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                } else {
                    TimeUnit.SECONDS.sleep(4);
                    consultarSituacaoCobrancaTransacaoCancelamento(transacaoVO, true);
                }
            } else {
                TimeUnit.SECONDS.sleep(4);
                consultarSituacaoCobrancaTransacao(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            TimeUnit.SECONDS.sleep(4);
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        transacao.setParamsResposta(retorno);

        JSONObject retornoJSON = new JSONObject();
        try {
            retornoJSON = new JSONObject(retorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
        }

        if (retornoJSON.has("id")) {

            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                String id = retornoJSON.optString("id");
                if (!UteisValidacao.emptyString(id)) {
                    transacao.setCodigoExterno(id); //ORDER ID
                }
            }

            if (UteisValidacao.emptyString(transacao.getCodigoExterno2()) && retornoJSON.optJSONArray("charges").length() > 0) {
                String id2 = new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).optString("id");
                if (!UteisValidacao.emptyString(id2)) {
                    transacao.setCodigoExterno2(id2); //CHARGE ID
                }
            }

            //NSU e Código de Autorização
            try {
                if (new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).has("last_transaction")) {
                    transacao.setCodigoNSU(new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).optJSONObject("last_transaction").optString("acquirer_nsu"));
                    transacao.setCodigoAutorizacao(new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).optJSONObject("last_transaction").optString("acquirer_auth_code"));
                    transacao.setGateway_id(new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).optJSONObject("last_transaction").optLong("gateway_id"));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            try { //IDENTIFICAR A OPERADORA DO CARTAO
                String card_brand = new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).getJSONObject("last_transaction").getJSONObject("card").getString("brand");
                if (!UteisValidacao.emptyString(card_brand)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = card_brand.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }

            try {//Obtendo o card id gerado na API da Stone

                String card_id = new JSONObject(retornoJSON.getJSONArray("charges").get(0).toString()).getJSONObject("last_transaction").getJSONObject("card").getString("id");
                if(!UteisValidacao.emptyString(card_id)) {
                    if(transacao != null && transacao.getCartaoCreditoTO() != null) {
                        transacao.getCartaoCreditoTO().setIdCardStoneV5(card_id);
                    }
                }

            } catch (Exception ignore) {}


            //https://docs.pagar.me/reference#status-das-transacoes
            //Valores possíveis: processing, authorized, paid, refunded, waiting_payment, pending_refund, refused .
            String status = retornoJSON.getString("status");
            if (status.equalsIgnoreCase("paid")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("Pending")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("Canceled")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("Failed")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }

        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        }
    }

    private JSONObject montarBodyJSONEnvio(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO,
                                           PessoaVO pessoaVO) throws Exception {

        List<String> itensObrigatorios = new ArrayList<>();

        JSONObject jsonEnvio = new JSONObject();
        int amount = (int) (transacaoVO.getValor() * 100);

        String identificador = "TRAN" + transacaoVO.getCodigo();

        Uteis.logarDebug("IDENTIFICADOR STONE PAGAR.ME V5: " + identificador);

        //Valor único que identifica a transação para permitir uma nova tentativa de requisição com a segurança de que a mesma operação não será executada duas vezes acidentalmente.
        jsonEnvio.put("code", identificador);

        jsonEnvio.put("customer_id", pessoaVO.getIdStone());

        jsonEnvio.put("items", montarItemsJSON(amount, identificador));

        jsonEnvio.put("payments", montarPaymentJSON(amount, cartaoCreditoTO, transacaoVO, pessoaVO));

        jsonEnvio.put("closed", true);

        //Indica se o pedido passará ou não pelo antifraude. Se não for enviado, será considerada a configuração da conta
        jsonEnvio.put("antifraud_enabled", false);

        if (!UteisValidacao.emptyList(itensObrigatorios)) {
            String campos = "";
            for (String s : itensObrigatorios) {
                campos += (", " + s);
            }
            throw new Exception("O cliente não possui " + campos.replaceFirst(", ", "") + ".");
        }

        //METADATA
        //Passar dados adicionais na criação da transação para
        //facilitar uma futura análise de dados tanto em nossa dashboard, quanto por seus sistemas.
        preencherMetadata(transacaoVO, jsonEnvio, identificador);

        return jsonEnvio;
    }

    public JSONArray montarPaymentJSON(int amount, CartaoCreditoTO cartaoCreditoTO, TransacaoVO transacaoVO, PessoaVO pessoaVO) throws Exception {
        JSONObject payment = new JSONObject();
        payment.put("payment_method", "credit_card");
        payment.put("credit_card", montarCreditCardJSON(cartaoCreditoTO, pessoaVO));
        payment.put("amount", amount);
        if (this.usaSplitPagamentoStoneV5) {
            //verifica se existe regra de recebedores
            //Regras de divisão da transação
            preencherJSONRecebedoresParaCobranca(transacaoVO, payment);
        }
        JSONArray payments = new JSONArray();
        payments.put(payment);
        return payments;
    }

    private void preencherJSONRecebedoresParaCobranca(TransacaoVO transacaoVO, JSONObject payment) throws Exception {
        List<RecebedorDTO> lista = obterRecebedoresSplitPagamentoDoConvenio(transacaoVO, this.convenioCobrancaVO);
        if (!UteisValidacao.emptyList(lista)) {
            JSONArray splits = new JSONArray();
            boolean jaMontouORecebedorPrincipal = false;
            for (RecebedorDTO recebedor : lista) {
                JSONObject obj = new JSONObject();
                obj.put("recipient_id", recebedor.getId());
                obj.put("amount", recebedor.getValorCentavos());
                obj.put("type", "flat");

                if (recebedor.isRecebedorPrincipal() && !jaMontouORecebedorPrincipal) {
                    obj.put("options", montarOptionsRecebedorJSON(obj, true));
                    jaMontouORecebedorPrincipal = true;
                } else {
                    obj.put("options", montarOptionsRecebedorJSON(obj, false));
                }
                splits.put(obj);
            }

            if (splits.length() > 0) {
                payment.put("split", splits);
            }
        }
    }

    public JSONObject montarOptionsRecebedorJSON(JSONObject jsonObject, boolean recebedorPrincipal) {
        JSONObject options = new JSONObject();
        options.put("charge_processing_fee", recebedorPrincipal ? "true" : "false"); //Indica se o recebedor vinculado à regra será cobrado pelas taxas da transação
        options.put("charge_remainder_fee", recebedorPrincipal ? "true" : "false"); //Indica se o recebedor vinculado à regra irá receber o restante dos recebíveis após uma divisão
        options.put("liable", recebedorPrincipal ? "true" : "false"); //Indica se o recebedor é responsável pela transação em caso de chargeback.
        return options;
    }

    public JSONObject montarCreditCardJSON(CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO) {
        JSONObject credit_card = new JSONObject();
        credit_card.put("operation_type", "auth_and_capture");
        credit_card.put("installments", cartaoCreditoTO.getParcelas());
        //Descrição que aparecerá na fatura depois do nome de sua empresa. Máximo de 13 caracteres, sendo alfanuméricos e espaços.
        credit_card.put("statement_descriptor",
                formatarSoftDescriptor(this.convenioCobrancaVO.getEmpresa().getRazaoSocialParaSoftDescriptor(cartaoCreditoTO.isTransacaoVerificarCartao()), 13));
        credit_card.put("card", montarCardJSON(cartaoCreditoTO, pessoaVO));
        return credit_card;
    }

    public JSONObject montarCardJSON(CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO) {
        JSONObject card = new JSONObject();
        card.put("number", cartaoCreditoTO.getNumero());
        card.put("holder_name", formatarCampo(Uteis.retirarAcentuacaoRegex(cartaoCreditoTO.getNomeTitular()), 64).trim());
        card.put("exp_month", cartaoCreditoTO.getMesValidadeMM());
        card.put("exp_year", cartaoCreditoTO.getAnoValidadeYY());

        //Código de segurança do cartão.
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
            card.put("cvv", cartaoCreditoTO.getCodigoSeguranca());
        }
        card.put("billing_address", montarBillingAddressJSON(pessoaVO));
        return card;
    }

    private JSONObject montarBillingAddressJSON(PessoaVO pessoaVO) {
        EnderecoVO enderecoVO = obterEnderecoPessoa(pessoaVO);
        JSONObject address = new JSONObject();
        Faker faker = new Faker(new java.util.Locale("pt-BR"));

        String numero = enderecoVO.getNumero();
        if (UteisValidacao.emptyString(numero)) {
            numero = faker.address().buildingNumber();
        }

        String endereco = Uteis.retirarAcentuacaoRegex(enderecoVO.getEndereco().trim());
        if (UteisValidacao.emptyString(endereco)) {
            endereco = faker.address().streetName();
        }

        String bairro = enderecoVO.getBairro();
        if (UteisValidacao.emptyString(bairro)) {
            bairro = faker.address().streetName();
        }

        //LINE_1
        address.put("line_1", numero + "," + endereco + ", " + bairro);

        String complemento = enderecoVO.getComplemento();
        if (UteisValidacao.emptyString(complemento)) {
            complemento = faker.address().secondaryAddress();
        }

        //LINE_2
        address.put("line_2", complemento);


        //ZIPCODE
        String cep = Uteis.tirarCaracteres(enderecoVO.getCep(), true);
        if (UteisValidacao.emptyString(cep)) {
            cep = Uteis.tirarCaracteres(faker.address().zipCode(), true);
        }
        address.put("zip_code", cep);

        //CITY
        String cidade = Uteis.retirarAcentuacaoRegex(pessoaVO.getCidade().getNome());
        if (UteisValidacao.emptyString(cidade)) {
            cidade = faker.address().cityName();
        }
        address.put("city", cidade);


        //STATE
        String estado = pessoaVO.getEstadoVO().getSigla();
        if (UteisValidacao.emptyString(estado)) {
            estado = faker.address().stateAbbr();
        }
        address.put("state", estado);

        //COUNTRY
        address.put("country", "BR");

        return address;
    }

    private EnderecoVO obterEnderecoPessoa(PessoaVO pessoaVO) {
        for (EnderecoVO enderecoVO : pessoaVO.getEnderecoVOs()) {
            return enderecoVO;
        }
        return new EnderecoVO();
    }

    private JSONArray montarItemsJSON(int amount, String identificador) {
        JSONObject item = new JSONObject();
        item.put("amount", amount);
        item.put("description", "PARCELA COBRANCA");
        item.put("quantity", 1);
        item.put("code", identificador);
        JSONArray items = new JSONArray();
        items.put(item);
        return items;
    }

    private Boolean dadosDesatualizados(PessoaVO pessoa) throws Exception {
        boolean dadosDesatualizados = false;
        LogVO ultimoLog = this.logDAO.consultarUltimoLogPessoa(pessoa.getCodigo(), "CLIENTE", "PESSOA", "CLIENTE - EMPRESA");
        if (ultimoLog != null) {
            dadosDesatualizados = pessoa.getDataAlteracaoStone().getTime() < ultimoLog.getDataAlteracao().getTime();
        } else {
            dadosDesatualizados = true;
        }
        return dadosDesatualizados;
    }

    public void incluirOuAtualizarPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
        try {
            boolean cadastroNovo = UteisValidacao.emptyString(pessoa.getIdStone());
            boolean atualizarCadastroExistente = !cadastroNovo && dadosDesatualizados(pessoa);
            JSONObject customerJSON = new JSONObject();
            if (cadastroNovo) {
                customerJSON = criarCustomerJSON(pessoa);
            } else if (atualizarCadastroExistente) {
//                customerJSON = obterCustomerJSONAtualizacao(pessoa);
                customerJSON = criarCustomerJSON(pessoa);
            }

            JSONObject respostaJSON = null;

            if (cadastroNovo) {
                //incluir
                String resposta = executarRequestStone_V5("/customers", customerJSON.toString(), MetodoHttpEnum.POST);
                incluirHistoricoRetornoTransacao(transacaoVO, resposta, "incluirPessoa");
                respostaJSON = new JSONObject(resposta);
            } else if (atualizarCadastroExistente) {
                //editar
                String resposta = executarRequestStone_V5("/customers/" + pessoa.getIdStone(), customerJSON.toString(), MetodoHttpEnum.PUT);
                incluirHistoricoRetornoTransacao(transacaoVO, resposta, "incluirPessoaAlterar");
                respostaJSON = new JSONObject(resposta);
            }

            if (respostaJSON != null) {
                if (respostaJSON.has("errors")) {
                    throw new ConsistirException("Falha ao inserir a pessoa na Stone. Erro: " + respostaJSON.get("errors").toString());
                } else if (respostaJSON.has("message") && !respostaJSON.has("id")) {
                    if (respostaJSON.get("message").toString().contains("Authorization has been denied for this request")) {
                        throw new Exception("Credenciais inválidas para incluir/editar pessoa. Verifique a Chave de API do convênio!");
                    } else {
                        throw new ConsistirException("Falha ao inserir a pessoa na Stone. Erro: " + respostaJSON.get("message").toString());
                    }
                } else if (respostaJSON.has("id")) {
                    String id = respostaJSON.optString("id");
                    if (!UteisValidacao.emptyString(id)) {
                        pessoa.setIdStone(id.toString());
                        new Pessoa(getCon()).alterarIdStone(pessoa);
                    }
                }
            }
        } catch (Exception ex) {
            incluirHistoricoRetornoTransacao(transacaoVO, ex.getMessage(), "incluirPessoaErro");
            throw ex;
        }
    }

    private String executarRequestStone_V5(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = this.urlAPI + endPoint;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("ServiceRefererName", PropsService.getPropertyValue(PropsService.serviceRefererNamePagarMePacto));
        //A chave da API é combinada com os dois pontos : para formar um base64-encoded string:
        String chaveAPI = this.chaveAPI + ":";
        headers.put("Authorization", "Basic " + new String(new Base64().encode(chaveAPI.getBytes())));

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        return respostaHttpDTO.getResponse();
    }

    private RespostaHttpDTO executarRequestStone_V5_ReturningHttp(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = this.urlAPI + endPoint;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("ServiceRefererName", PropsService.getPropertyValue(PropsService.serviceRefererNamePagarMePacto));
        //A chave da API é combinada com os dois pontos : para formar um base64-encoded string:
        String chaveAPI = this.chaveAPI + ":";
        headers.put("Authorization", "Basic " + new String(new Base64().encode(chaveAPI.getBytes())));

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        return respostaHttpDTO;
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }

    public String formatarSoftDescriptor(String texto, Integer tamanho) {
        String textoSemAcento = Uteis.removerCaracteresEspeciaisEAcentuacao(texto);
        if (textoSemAcento.length() >= tamanho) {
            return textoSemAcento.substring(0, tamanho);
        }
        return textoSemAcento;
    }

    private JSONObject criarCustomerJSON(PessoaVO pessoaVO) {

        JSONObject customer = new JSONObject();

        String email = obterEmailPessoa(pessoaVO);
        if (UteisValidacao.emptyString(email) || email.toLowerCase().contains("naoinformado") || email.toLowerCase().contains("naotem")) {
            String emailFake = getEmailFake(pessoaVO.getCodigo());
            customer.put("email", emailFake.trim());
        } else {
            customer.put("email", email.trim());
        }

        customer.put("name", Uteis.retirarAcentuacaoRegex(pessoaVO.getNome()));

        String cpf = Uteis.removerMascara(pessoaVO.getCfp());
        if (UteisValidacao.emptyString(cpf)) {
            String cpfFake = GeradorCPF.gerarCPF(true);
            customer.put("type", "individual");
            customer.put("document", cpfFake);
            customer.put("document_type", "CPF");
        } else {
            customer.put("type", "individual");
            customer.put("document", cpf);
            customer.put("document_type", "CPF");
        }

        boolean adicionouTelefoneOriginal = false;
        if (!UteisValidacao.emptyList(pessoaVO.getTelefoneVOs())) {
            JSONObject phones = new JSONObject();
            JSONObject mobile_phone = new JSONObject();
            for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
                if (telefoneVO.getTipoTelefone().equals(TipoTelefone.CELULAR.getCodigo())) {
                    String countryCode = UteisValidacao.emptyString(telefoneVO.getDdi()) ? "55" : telefoneVO.getDdi().replace("+", "");
                    String areaCode = telefoneVO.getNumero().substring(1, 3);
                    String number = Uteis.tirarCaracteres(telefoneVO.getNumero(), true);

                    if (UteisValidacao.somenteNumeros(countryCode) &&
                        UteisValidacao.somenteNumeros(areaCode) &&
                        UteisValidacao.somenteNumeros(number)) {

                        mobile_phone.put("country_code", countryCode);
                        mobile_phone.put("area_code", areaCode);
                        mobile_phone.put("number", number);
                        phones.put("mobile_phone", mobile_phone);
                        customer.put("phones", phones);
                        adicionouTelefoneOriginal = true;
                    } else {
                        adicionouTelefoneOriginal = false;
                    }
                    break;
                }
            }
        }

        if (!adicionouTelefoneOriginal) {
            String telefoneFake = GeradorTelefone.gerarNumeroTelefone(9, false);
            JSONObject phones = new JSONObject();
            JSONObject mobile_phone = new JSONObject();
            mobile_phone.put("country_code", "55");
            mobile_phone.put("area_code", "62");
            mobile_phone.put("number", telefoneFake);
            phones.put("mobile_phone", mobile_phone);
            customer.put("phones", phones);
        }

        return customer;
    }

    private String getEmailFake(Integer codigoPessoa) {
        String key = "";
        try {
            key = DAO.resolveKeyFromConnection(this.getCon());
        } catch (Exception ignored) {
        }
        return ("pessoa_" + codigoPessoa + (!UteisValidacao.emptyString(key) ? ("_" + key.substring(0, 3)) : "") + "@pacto.com");
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    private void preencherMetadata(TransacaoVO transacaoVO, JSONObject jsonEnvio, String identificador) {
        JSONObject metadata = new JSONObject();
        metadata.put("identificadorPacto", identificador);
        metadata.put("empresa", this.convenioCobrancaVO.getEmpresa().getRazaoSocialParaSoftDescriptor(false));
        metadata.put("nomeFantasia", this.convenioCobrancaVO.getEmpresa().getNome());
        if (!UteisValidacao.emptyString(this.convenioCobrancaVO.getMensagem())) {
            metadata.put("metadata", this.convenioCobrancaVO.getMensagem());
        }

        try {

            String parcelas = "";
            for (MovParcelaVO parcelaVO : transacaoVO.getListaParcelas()) {
                parcelas += "," + parcelaVO.getCodigo();
            }
            parcelas = (parcelas.replaceFirst(",", ""));

            if (!UteisValidacao.emptyString(parcelas)) {
                Integer codPlano = 0;
                String plano = "";
                Set<String> listaProdutos = new HashSet<>();

                StringBuilder sql = new StringBuilder();
                sql.append("select   \n");
                sql.append("pl.codigo as codPlano,  \n");
                sql.append("pl.descricao as plano,  \n");
                sql.append("pro.descricao as produto \n");
                sql.append("from movparcela mp  \n");
                sql.append("left join contrato con on con.codigo = mp.contrato  \n");
                sql.append("left join plano pl on pl.codigo = con.plano \n");
                sql.append("inner join movprodutoparcela mpp on mpp.movparcela = mp.codigo  \n");
                sql.append("inner join movproduto mov on mpp.movproduto = mov.codigo  \n");
                sql.append("inner join produto pro on pro.codigo = mov.produto  \n");
                sql.append("where mp.codigo IN (").append(parcelas).append(") \n");
                sql.append("group by 1,2,3 \n");

                try (Statement stm = getCon().createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sql.toString())) {
                        while (rs.next()) {

                            //codPlano
                            Integer codPl = rs.getInt("codPlano");
                            if (!UteisValidacao.emptyNumber(codPl)) {
                                codPlano = codPl;
                            }

                            //plano
                            String pl = rs.getString("plano");
                            if (!UteisValidacao.emptyString(pl)) {
                                plano = pl;
                            }

                            //produto
                            String pr = rs.getString("produto");
                            if (!UteisValidacao.emptyString(pr)) {
                                listaProdutos.add(pr);
                            }
                        }
                    }
                }

                metadata.put("codigoPlano", codPlano);
                metadata.put("plano", plano);

                String produtos = "";
                for (String prod : listaProdutos) {
                    produtos += "," + prod;
                }
                produtos = (produtos.replaceFirst(",", ""));

                metadata.put("produtos", produtos);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        jsonEnvio.put("metadata", metadata);
    }

    public List<RecebedorDTO> obterRecebedoresCadastradosNoPortal() throws Exception {
        List<RecebedorDTO> lista = new ArrayList<>();

        String retorno = executarRequestStone_V5("/recipients?page=1&count=1000", null, MetodoHttpEnum.GET);

        if (retorno.contains("errors")) {
            JSONObject json = new JSONObject(retorno);
            JSONArray jsonArray = json.getJSONArray("errors");
            JSONObject jsonMsg = new JSONObject(jsonArray.get(0).toString());
            throw new Exception("Não foi possível consultar os recebedores: " + jsonMsg.getString("message"));
        }

        if (retorno.contains("message")) {
            JSONObject json = new JSONObject(retorno);
            String msg = json.getString("message");
            if (msg.contains("Authorization has been denied for this request")) {
                throw new Exception("Credenciais inválidas para consultar os recebedores. Verifique a Chave de API do convênio!");
            }
            throw new Exception(msg);
        }

        JSONArray jsonArray = new JSONObject(retorno).getJSONArray("data");
        for (int e = 0; e < jsonArray.length(); e++) {
            JSONObject obj = jsonArray.getJSONObject(e);

            RecebedorDTO recebedorDTO = new RecebedorDTO(obj, this.convenioCobrancaVO);
            if (recebedorDTO.getStatus().equalsIgnoreCase("active") &&
                    !UteisValidacao.emptyString(recebedorDTO.getName())) {
                lista.add(recebedorDTO);
            }
        }
        return lista;
    }

    private boolean validarCartaoExcluido(String retornoJSON) throws Exception {
        try {

            String returnedStatus = new JSONObject(retornoJSON).optString("status");

            if(!UteisValidacao.emptyString(returnedStatus)) {
                if(returnedStatus.equals("deleted")) {
                    return true;
                }
            }

            return false;

        } catch (Exception ignored) {
            return false;
        }
    }
}

package servicos.impl.stoneV5;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/01/2025
 */
public enum TipoAgrupamentoPayableStoneEnum {

    NENHUM("NENHUM", "Nenhum"),
    VENDA_NORMAL("VENDA_NORMAL", "Venda Normal"),
    VENDA_PARCELADA("VENDA_PARCELADA", "Venda Parcelada"),
    SPLIT("SPLIT", "Split"),
    ;


    private String id;
    private String descricao;

    TipoAgrupamentoPayableStoneEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public static TipoAgrupamentoPayableStoneEnum obterPorId(String id) {
        for (TipoAgrupamentoPayableStoneEnum stone : TipoAgrupamentoPayableStoneEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return NENHUM;
    }
}

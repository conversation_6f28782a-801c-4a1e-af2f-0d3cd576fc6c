package servicos.impl.stoneV5;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/01/2025
 */

public class PayableStoneTO extends SuperVO {

    private String id;
    private StatusPayableStoneEnum status;
    private Integer amount;
    private Integer fee;
    private Integer anticipation_fee;
    private Integer fraud_coverage_fee;
    private int installment;
    private Long gateway_id;
    private String charge_id;
    private String split_id;
    private String transaction_id;
    private String split_rule_id;
    private String bulk_anticipation_id;
    private String anticipation_id;
    private String recipient_id;
    private OriginatorModelPayableStoneEnum originator_model;
    private String originator_model_id;
    private Date payment_date;
    private Date original_payment_date;
    private TypePayableStoneEnum type;
    private String payment_method;
    private Date accrual_at;
    private Date created_at;
    private String liquidation_arrangement_id;
    private TipoConciliacaoEnum tipoConciliacaoEnum;
    @NaoControlarLogAlteracao
    private int totalInstallments;
    private TipoAgrupamentoPayableStoneEnum tipoAgrupamento;

    public PayableStoneTO() {
    }

    public PayableStoneTO(JSONObject jsonItem, TipoConsultaPayablesStoneEnum tipoConsultaPayablesStoneEnum) throws Exception {
        this.id = String.valueOf(jsonItem.optLong("id"));
        if (jsonItem.has("status")) {
            this.status = StatusPayableStoneEnum.obterPorId(jsonItem.getString("status"));
        }
        this.amount = jsonItem.optInt("amount");
        this.fee = jsonItem.optInt("fee");
        this.fraud_coverage_fee = jsonItem.optInt("fraud_coverage_fee");
        this.anticipation_fee = jsonItem.optInt("anticipation_fee");
        this.installment = jsonItem.optInt("installment");
        this.gateway_id = jsonItem.optLong("gateway_id");
        this.charge_id = jsonItem.optString("charge_id");
        this.split_id = jsonItem.optString("split_id");
        this.transaction_id = jsonItem.optString("transaction_id");
        this.split_rule_id = jsonItem.optString("split_rule_id");
        this.bulk_anticipation_id = jsonItem.optString("bulk_anticipation_id");
        this.anticipation_id = jsonItem.optString("anticipation_id");
        this.recipient_id = jsonItem.optString("recipient_id");
        if (jsonItem.has("payment_date") && !jsonItem.isNull("payment_date")) {
            this.payment_date = Uteis.getDate(jsonItem.getString("payment_date"), "yyyy-MM-dd");
        }
        if (jsonItem.has("original_payment_date") && !jsonItem.isNull("original_payment_date")) {
            this.original_payment_date = Uteis.getDate(jsonItem.getString("original_payment_date"), "yyyy-MM-dd");
        }
        if (jsonItem.has("status")) {
            this.type = TypePayableStoneEnum.obterPorId(jsonItem.getString("type"));
        }
        this.payment_method = jsonItem.getString("payment_method");
        if (jsonItem.has("created_at") && !jsonItem.isNull("created_at")) {
            // Não remover o timezone daqui de jeito nenhum pois a data do servidor deles lá sempre fica +3 horas
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(jsonItem.getString("created_at"));
            this.created_at = dataPagamento;
        }
        if (jsonItem.has("accrual_at") && !jsonItem.isNull("accrual_at")) {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date accrual_at = sdf.parse(jsonItem.getString("accrual_at"));
            this.accrual_at = accrual_at;
        }
        if (jsonItem.has("created_at") && !jsonItem.isNull("created_at")) {
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(jsonItem.getString("created_at"));
            this.created_at = dataPagamento;
        }

        if (jsonItem.has("originator_model") && !jsonItem.isNull("originator_model")) {
            this.originator_model = OriginatorModelPayableStoneEnum.obterPorId(jsonItem.optString("originator_model").toUpperCase());
        }
        this.originator_model_id = jsonItem.optString("originator_model_id");

        this.liquidation_arrangement_id = jsonItem.optString("liquidation_arrangement_id");

        preencherTipoConciliacao(tipoConsultaPayablesStoneEnum);
    }

    private void preencherTipoConciliacao(TipoConsultaPayablesStoneEnum tipoConsultaPayablesStoneEnum) {
        if (this.type.equals(TypePayableStoneEnum.CREDIT)) {
            if (tipoConsultaPayablesStoneEnum.equals(TipoConsultaPayablesStoneEnum.DATA_VENDA)) {
                this.tipoConciliacaoEnum = TipoConciliacaoEnum.VENDAS;
            } else if (tipoConsultaPayablesStoneEnum.equals(TipoConsultaPayablesStoneEnum.DATA_PAGAMENTO)) {
                this.tipoConciliacaoEnum = TipoConciliacaoEnum.PAGAMENTOS;
            }
        } else if (this.type.equals(TypePayableStoneEnum.CHARGEBACK)) {
            this.tipoConciliacaoEnum = TipoConciliacaoEnum.CHARGEBACK;
        } else if (this.type.equals(TypePayableStoneEnum.REFUND)) {
            this.tipoConciliacaoEnum = TipoConciliacaoEnum.CANCELAMENTO;
        } else if (this.type.equals(TypePayableStoneEnum.CHARGEBACK_REFUND)) {
            this.tipoConciliacaoEnum = TipoConciliacaoEnum.ESTORNO_CHARGEBACK;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public StatusPayableStoneEnum getStatus() {
        return status;
    }

    public void setStatus(StatusPayableStoneEnum status) {
        this.status = status;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Integer getFee() {
        return fee;
    }

    public void setFee(Integer fee) {
        this.fee = fee;
    }

    public Integer getAnticipation_fee() {
        return anticipation_fee;
    }

    public void setAnticipation_fee(Integer anticipation_fee) {
        this.anticipation_fee = anticipation_fee;
    }

    public Integer getFraud_coverage_fee() {
        return fraud_coverage_fee;
    }

    public void setFraud_coverage_fee(Integer fraud_coverage_fee) {
        this.fraud_coverage_fee = fraud_coverage_fee;
    }

    public int getInstallment() {
        return installment;
    }

    public void setInstallment(int installment) {
        this.installment = installment;
    }

    public Long getGateway_id() {
        if (UteisValidacao.emptyNumber(gateway_id)) {
            return null;
        }
        return gateway_id;
    }

    public void setGateway_id(Long gateway_id) {
        this.gateway_id = gateway_id;
    }

    public String getCharge_id() {
        return charge_id;
    }

    public void setCharge_id(String charge_id) {
        this.charge_id = charge_id;
    }

    public String getSplit_id() {
        return split_id;
    }

    public void setSplit_id(String split_id) {
        this.split_id = split_id;
    }

    public String getTransaction_id() {
        return transaction_id;
    }

    public void setTransaction_id(String transaction_id) {
        this.transaction_id = transaction_id;
    }

    public String getSplit_rule_id() {
        return split_rule_id;
    }

    public void setSplit_rule_id(String split_rule_id) {
        this.split_rule_id = split_rule_id;
    }

    public String getBulk_anticipation_id() {
        return bulk_anticipation_id;
    }

    public void setBulk_anticipation_id(String bulk_anticipation_id) {
        this.bulk_anticipation_id = bulk_anticipation_id;
    }

    public String getAnticipation_id() {
        return anticipation_id;
    }

    public void setAnticipation_id(String anticipation_id) {
        this.anticipation_id = anticipation_id;
    }

    public String getRecipient_id() {
        return recipient_id;
    }

    public void setRecipient_id(String recipient_id) {
        this.recipient_id = recipient_id;
    }

    public OriginatorModelPayableStoneEnum getOriginator_model() {
        return originator_model;
    }

    public void setOriginator_model(OriginatorModelPayableStoneEnum originator_model) {
        this.originator_model = originator_model;
    }

    public String getOriginator_model_id() {
        return originator_model_id;
    }

    public void setOriginator_model_id(String originator_model_id) {
        this.originator_model_id = originator_model_id;
    }

    public Date getPayment_date() {
        return payment_date;
    }

    public void setPayment_date(Date payment_date) {
        this.payment_date = payment_date;
    }

    public Date getOriginal_payment_date() {
        return original_payment_date;
    }

    public void setOriginal_payment_date(Date original_payment_date) {
        this.original_payment_date = original_payment_date;
    }

    public TypePayableStoneEnum getType() {
        if (this.type == null) {
            return TypePayableStoneEnum.NENHUM;
        }
        return type;
    }

    public void setType(TypePayableStoneEnum type) {
        this.type = type;
    }

    public String getPayment_method() {
        return payment_method;
    }

    public void setPayment_method(String payment_method) {
        this.payment_method = payment_method;
    }

    public Date getAccrual_at() {
        return accrual_at;
    }

    public void setAccrual_at(Date accrual_at) {
        this.accrual_at = accrual_at;
    }

    public Date getCreated_at() {
        return created_at;
    }

    public void setCreated_at(Date created_at) {
        this.created_at = created_at;
    }

    public String getLiquidation_arrangement_id() {
        return liquidation_arrangement_id;
    }

    public void setLiquidation_arrangement_id(String liquidation_arrangement_id) {
        this.liquidation_arrangement_id = liquidation_arrangement_id;
    }

    public TipoConciliacaoEnum getTipoConciliacaoEnum() {
        return tipoConciliacaoEnum;
    }

    public void setTipoConciliacaoEnum(TipoConciliacaoEnum tipoConciliacaoEnum) {
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
    }

    public int getTotalInstallments() {
        return totalInstallments;
    }

    public void setTotalInstallments(int totalInstallments) {
        this.totalInstallments = totalInstallments;
    }

    public TipoAgrupamentoPayableStoneEnum getTipoAgrupamento() {
        return tipoAgrupamento;
    }

    public void setTipoAgrupamento(TipoAgrupamentoPayableStoneEnum tipoAgrupamento) {
        this.tipoAgrupamento = tipoAgrupamento;
    }
}

package servicos.impl.stoneV5;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 20/01/2025
 */
public enum StatusPayableStoneEnum {

    NENHUM("NENHUM", "Nenhum"),
    WAITING_REFUNDS("waiting_funds", "Aguardando fundos"),
    PREPAID("prepaid", "Pré-Pago"),
    PAID("paid", "Pago");


    private String id;
    private String descricao;

    StatusPayableStoneEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StatusPayableStoneEnum obterPorId(String id) {
        for (StatusPayableStoneEnum stone : StatusPayableStoneEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return NENHUM;
    }
}

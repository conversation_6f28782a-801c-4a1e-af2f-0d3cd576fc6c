/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.apf;

import negocio.comuns.utilitarias.Criptografia;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public final class APF {

    public static final String NumeroDoc = "NumeroDocumento";
    public static final String ValorDoc = "ValorDocumento";
    public static final String QtdParcelas = "QuantidadeParcelas";
    public static final String NrVezes = "NrVezes";
    public static final String ParceladoLojista = "ParceladoLojista";
    public static final String NumCartao = "NumeroCartao";
    public static final String MesValidade = "MesValidade";
    public static final String AnoValidade = "AnoValidade";
    public static final String CodSeguranca = "CodigoSeguranca";
    public static final String IPComprador = "EnderecoIPComprador";
    public static final String NomePortador = "NomePortadorCartao";
    public static final String Bandeira = "Bandeira";
    public static final String TokenAragorn = "TokenAragorn";
    public static final String CPF = "CPFPortadorCartao";
    public static final String DataNasc = "DataNascimentoPortadorCartao";
    public static final String NomeMae = "NomeMaePortadorCartao";
    public static final String Moeda = "Moeda";
    public static final String TransAprovada = "TransacaoAprovada";
    public static final String Transacao = "Transacao";
    public static final String ResultSolicConfirmacao = "ResultadoSolicitacaoConfirmacao";
    public static final String Confirmado = "Confirmado";
    public static final String Cancelado = "Cancel";
    public static final String CancelamentoNSU = "NSUCancelamento";
    public static final String True = "True";
    public static final String False = "False";
    public static final String CartaoMascarado = "CartaoMascarado";
    public static final String TransAnterior = "TransacaoAnterior";
    public static final String ResultSolicAprovacao = "ResultadoSolicitacaoAprovacao";
    public static final String IPClientePacto = "IPClientePacto";
    public static final String URLRequest = "UrlRequest";
    public static final String CodigoAutorizacao = "CodigoAutorizacao";
    public static final String ResultSolicCancel = "ResultadoSolicitacaoCancelamento";
    public static final String CodExternoTransacaoRetransmitida = "CodExternoTransacaoRetransmitida";
    public static final String NumTentativasRepescagem = "NumTentativasRepescagem";

    public static String decifrar(final String numCartao) {
        String nCartao = numCartao.contains("=") ? Criptografia.decrypt(numCartao,
                SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM) : numCartao;
        return nCartao;
    }

    public static String encriptar(final String numCartao) throws Exception {
        String nCartao = Criptografia.encrypt(numCartao,
                SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
        return nCartao;
    }

    public static String getCartaoMascarado(String numCartao) {
        String nCartao = "";
        if(null != numCartao) {
            nCartao = decifrar(numCartao);
            if (!UteisValidacao.emptyString(nCartao)) {
                CharSequence cs = nCartao.subSequence(6, nCartao.length() - 4);
                nCartao = nCartao.replace(cs, "******");
            }
        }
        return nCartao;
    }

    public static String getCvvMascarado(String numCvv) {
        if(null != numCvv) {
            if (!UteisValidacao.emptyString(numCvv)) {
                if (numCvv.length() == 3 ){
                    numCvv = numCvv.replace(numCvv,"***");
                } else {
                    numCvv = numCvv.replace(numCvv, "****");
                }
            }
        }
        return numCvv;
    }

    public static boolean isNumCardIntoBINS(final String numCartao, final Integer[] bins) {
        if (numCartao.contains("******") || numCartao.lastIndexOf("=") != -1){
            return true;
        }
        for (int i = 0; i < bins.length; i++) {
            Integer bin = bins[i];
            if (numCartao.startsWith(String.valueOf(bin))) {
                return true;
            }
        }

        return false;
    }
}

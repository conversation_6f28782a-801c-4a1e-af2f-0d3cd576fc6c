/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.apf;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import servicos.impl.gatewaypagamento.ThreadPagamentoParcela;
import servicos.interfaces.AprovacaoServiceInterface;

/**
 * Thread responsável por processar uma TransacaoVO para uma MovParcelaVO junto ao serviço AprovaFacilService
 *
 * <AUTHOR>
 */
public class ThreadAprovaFacilParcela extends ThreadPagamentoParcela{

    private ContratoRecorrenciaVO contratoRecorrencia;
    private String ipClientePacto = "";
    private TransacaoVO transacao;

    public ThreadAprovaFacilParcela(RecorrenciaService recorrenciaService,
                                    MovParcelaVO movParcela, ContratoRecorrenciaVO contratoRecorrencia,
                                    Connection con, String ipClientePacto) throws Exception {
        super(movParcela, new AprovaFacilService(con), recorrenciaService, new ConvenioCobrancaVO());
        this.contratoRecorrencia = contratoRecorrencia;
        this.ipClientePacto = ipClientePacto;
        this.validarDados();

    }

    public void validarDados() throws Exception {
        try{
            if (!movParcela.getRegimeRecorrencia()) {
                throw new Exception(String.format("Parcela %s não pertence ao Regime de Recorrência.", new Object[]{movParcela.getCodigo()}));
            }
            if (contratoRecorrencia.getDataInutilizada() != null) {
                throw new Exception(String.format("ContratoRecorrência do contrato %s já foi substituído em %s, não podendo assim ser utilizado.",
                        new Object[]{contratoRecorrencia.getContrato().getCodigo(),
                                Uteis.getData(contratoRecorrencia.getDataInutilizada())}));
            }
            if (contratoRecorrencia.getUltimaTransacaoAprovada().isEmpty()) {
                throw new Exception(String.format("ContratoRecorrência do contrato %s ainda não teve nenhuma transação aprovada para que seja efetuada a Recorrência.",
                        new Object[]{contratoRecorrencia.getContrato().getCodigo()}));

            }
        } catch (NullPointerException ne) {
            throw new Exception("Problema na validação dos dados da Parcela e do Contrato. ");
        }

    }

    @Override
    protected void registrarErro(Exception ex) {
        recorrenciaService.logar(String.format("Erro ao processar transacao da parcela: \"%s\" do contrato: \"%s\": %s",  new Object[]{movParcela.getCodigo(), contratoRecorrencia.getContrato().getCodigo(), ex.getMessage()}));
    }

    @Override
    protected void registrarErro(TransacaoVO transacao) {
        //houve qualquer tipo de problema que não possibilitou a cobrança correta, adicionar na lista de parcelas para depois enviar e-mail aos responsáveis
        recorrenciaService.registrarParcelaErro(transacao, contratoRecorrencia);
    }

    @Override
    protected void executarAcoesPosProcessamento(TransacaoVO transacao) throws Exception{
        contratoRecorrencia.setUltimaTransacaoAprovada(transacao.getValorAtributoResposta(APF.Transacao));
        recorrenciaService.getContratoRecorrenciaDAO().alterar(contratoRecorrencia);
    }

    @Override
    protected Boolean podeExecutarPagamento() {
        return !UteisValidacao.emptyString(ConfiguracaoSistemaVO.obterURLRecorrencia(movParcela.getEmpresa(), recorrenciaService.getConfigSistema()));
    }

    @Override
    protected CartaoCreditoTO construirCartaoCreditoTO() throws Exception {
        CartaoCreditoTO cartao = new CartaoCreditoTO();
        cartao.setTransacaoAnterior(contratoRecorrencia.getUltimaTransacaoAprovada());
        cartao.setValor(movParcela.getValorParcela());
        cartao.setEmpresa(movParcela.getEmpresa().getCodigo());
        cartao.setNumeroDocumento(String.valueOf(movParcela.getCodigo()));
        cartao.setParcelas(1);
        cartao.setUsuarioResponsavel(recorrenciaService.getUsuario());
        cartao.setListaParcelas(new ArrayList());
        cartao.getListaParcelas().add(movParcela);
        cartao.setUrl(ConfiguracaoSistemaVO.obterURLRecorrencia(movParcela.getEmpresa(), recorrenciaService.getConfigSistema()));
        this.transacao = recorrenciaService.obterUltimaTransacao(contratoRecorrencia.getUltimaTransacaoAprovada());
        OperadoraCartaoVO operadora = recorrenciaService.obterOperadoraUltimaTransacao(this.transacao);
        cartao.setNomeTitular(this.transacao.getNomePessoa());
        cartao.setBand(operadora.getCodigoIntegracaoAPF());
        cartao.setIp(recorrenciaService.getIP());
        cartao.setIpClientePacto(this.ipClientePacto);
        return cartao;
    }

    @Override
    protected OperadoraCartaoVO getOperadoraCartao() throws Exception {
        return recorrenciaService.obterOperadoraUltimaTransacao(this.transacao);
    }

    public void run() {
        execute(null, new ArrayList<String>());
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.apf;

import org.json.JSONException;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.util.ExecuteRequestHttpService;
import servicos.interfaces.AprovacaoServiceInterface;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import controle.financeiro.EstornoReciboControle;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import servicos.SuperServico;

/**
 *
 * <AUTHOR>
 */
public class AprovaFacilService extends CobrancaOnlineService implements AprovacaoServiceInterface {

    private RecorrenciaService aprovaRemessa;
    private ExecuteRequestHttpService objAction;
    private EmpresaInterfaceFacade empresaFacade;

    public AprovaFacilService(Connection con) throws Exception {
        super(con);
        this.aprovaRemessa = new RecorrenciaService(con);
        this.empresaFacade = new Empresa(con);
        this.objAction = new ExecuteRequestHttpService();
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return transacaoFacade;
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = new TransacaoVO();
        transacao.setTokenAragorn(dadosCartao.getTokenAragorn());
        return executarAprovacao(transacao, dadosCartao);
    }

    private String executeRequestTransacao(final String url, final String codigoTransacao) throws IOException, JSONException {
        Map<String, String> params = new HashMap();
        if (codigoTransacao != null && !codigoTransacao.isEmpty()) {
            params.put("Transacao", codigoTransacao);
        }
        return objAction.executeRequestInnerMock(url, params, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", false);
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal,
            Boolean estornarRecibo) throws Exception {

        String url = transacaoOriginal.getUrlTransiente();
        init(url);

        if (!url.isEmpty()) {
            //testar se a comunicação está ok
            String urlCancelamento = url.replace("/APC", "/CAN");
            try {
                executeRequestTransacao(urlCancelamento, "testeComunicacao");
            } catch (Exception e) {
                throw new Exception("Não foi possível iniciar o processo de cancelamento da transação. " + e.getMessage());
            }

            urlCancelamento = url.replace("/APC", "/CAN");
            transacaoOriginal.setResultadoCancelamento(executeRequestTransacao(urlCancelamento,
                    transacaoOriginal.getCodigoExterno()));
        }

        try {
            if (estornarRecibo && transacaoOriginal.getReciboPagamento().intValue() != 0) {
                estornarRecibo(transacaoOriginal, false);
            }

            if (transacaoOriginal.getValorAtributoCancelamento(APF.ResultSolicCancel).contains(APF.Cancelado)) {
                transacaoOriginal.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoOriginal.setDataHoraCancelamento(Calendario.hoje());
            } else {
                transacaoOriginal.setSituacao(SituacaoTransacaoEnum.ESTORNADA);
                transacaoOriginal.setDataHoraCancelamento(Calendario.hoje());
            }
            transacaoFacade.alterar(transacaoOriginal);
            System.out.println("Cancelou Transação => " + transacaoOriginal);

        } catch (Exception e) {
            System.out.println("Não Cancelou Transação => " + transacaoOriginal + " devido ao erro -> " + e.getMessage());
            throw e;
        }


        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    private TransacaoVO executarAprovacao(TransacaoVO transacao, CartaoCreditoTO dadosCartao) throws Exception {
        init(dadosCartao.getUrl());
        transacao.setUrlTransiente(dadosCartao.getUrl());
        transacao.setUsuarioResponsavel(dadosCartao.getUsuarioResponsavel());
        transacao.setNomePessoa(dadosCartao.getNomeTitular());
        transacao.setDataProcessamento(Calendario.hoje());
        String urlAprovacao = dadosCartao.getUrl();
        String urlCaptura = dadosCartao.getUrl().replace("/APC", "/CAP");
        transacao.setTipo(TipoTransacaoEnum.AprovaFacilCB);
        transacao.setEmpresa(dadosCartao.getEmpresa());
        //setar o contador de tentativas da repescagem de uma transação, para mostrar quantas vezes já fora executadas transações em relação a esta parcela
        if (dadosCartao.getListaParcelas() != null && dadosCartao.getListaParcelas().size() == 1
                && dadosCartao.getListaParcelas().get(0).getNrTentativas() >= 1) {
            dadosCartao.setTentativasRepescagem(String.valueOf(dadosCartao.getListaParcelas().get(0).getNrTentativas()));
        }
        boolean abriuTransacao = false;
        try {
            Map<String, String> params = dadosCartao.parseToMapPadraoCobreBem();
            transacao.setListaParcelas(dadosCartao.getListaParcelas());
            transacao.setValor(Uteis.arredondarForcando2CasasDecimais(dadosCartao.getValor()));
            transacao.setEmpresa(dadosCartao.getEmpresa());
            transacao.setParamsEnvio(params.toString());
            transacao.setParamsResposta(objAction.executeRequestInnerMock(urlAprovacao, params, ExecuteRequestHttpService.METODO_POST, "ISO-8859-1", false));
            String retorno = transacao.getValorAtributoResposta(APF.TransAprovada);
            transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
            String ultimos4Caracteres = "";
            //se é a primeira transação de aprovação, criptografa as informações que foram sigilosas
            if (dadosCartao.getNumero() != null && dadosCartao.getNumero().length() > 4) {
                ultimos4Caracteres = dadosCartao.getNumero().substring(dadosCartao.getNumero().length() - 4);
                CartaoCreditoTO clone = (CartaoCreditoTO) dadosCartao.getClone(true);

                clone.setNumero(APF.encriptar(dadosCartao.getNumero()) + ultimos4Caracteres);

                clone.setCodigoSeguranca(APF.encriptar(dadosCartao.getCodigoSeguranca()));

                params = clone.parseToMapPadraoCobreBem();
                transacao.setParamsEnvio(params.toString());
            }
            //
            if (retorno.equals(APF.True)) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
                transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
                if (!dadosCartao.isTrocarCartao()) {
                    //se foi aprovada, temos que enviar o pedido de confirmação que efetivamente confirma o débito no cartão de crédito
                    String retornoCaptura = executeRequestTransacao(urlCaptura, transacao.getCodigoExterno());
                    transacao.setResultadoCaptura(retornoCaptura);
                    if (transacao.getValorAtributoCaptura(
                            APF.ResultSolicConfirmacao).contains(
                            APF.Confirmado)) {
                        transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);

                    } else {
                        transacao.setSituacao(SituacaoTransacaoEnum.ERRO_CAPTURA);
                    }
                }

            } else if (retorno.equals(APF.False) && !transacao.getValorAtributoResposta(APF.Transacao).isEmpty()) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }
            transacao.setCodigo(0);
            transacaoFacade.getCon().setAutoCommit(false);
            abriuTransacao = true;

            transacaoFacade.incluir(transacao);

            transacaoFacade.getCon().commit();
        } catch (Exception e) {
            if (abriuTransacao) {
                transacaoFacade.getCon().rollback();
            }
            System.out.println("Houve um erro durante o processamento da transação. Havia aberto transação? "
                    + abriuTransacao + " Transação: " + transacao + " Exceção => " + e.getMessage());
            throw e;
        } finally {
            transacaoFacade.getCon().setAutoCommit(true);
        }

        return transacao;

    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal,
            ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {

        init(transacaoOriginal.getUrlTransiente());

        if (transacaoOriginal.getListaParcelas().isEmpty()) {
            throw new Exception("Você não pode retransmitir uma "
                    + "transação sem parcelas relacionadas: "
                    + transacaoOriginal.getCodigoExterno());
        }

        CartaoCreditoTO cartao = new CartaoCreditoTO();
        Map<String, String> paramsEnvio = Uteis.obterMapFromString(
                transacaoOriginal.getParamsEnvio());
        if (!paramsEnvio.isEmpty()) {
            String tmp = transacaoOriginal.getValorAtributoResposta(APF.CartaoMascarado);
            List<TransacaoVO> tempL = transacaoFacade.consultar("select * from transacao where paramsresposta "
                    + "like('%<CartaoMascarado>" + tmp + "</CartaoMascarado>%') and paramsenvio like('{MesValidade%') "
                    + "order by codigo desc limit 1",
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!tempL.isEmpty()) {
                TransacaoVO t = tempL.get(0);
                Map<String, String> paramsVendaUnica = Uteis.obterMapFromString(t.getParamsEnvio());

                cartao.setMesValidade(Integer.valueOf(paramsVendaUnica.get(APF.MesValidade)));
                //tratamento para o ano de validade
                String anoValidade = paramsVendaUnica.get(APF.AnoValidade);
                String seculo = String.valueOf(Calendario.getInstance().get(Calendar.YEAR)).substring(0, 2);
                anoValidade = anoValidade.length() == 4 ? anoValidade : seculo + anoValidade;
                cartao.setAnoValidade(Integer.valueOf(anoValidade));
                //
                cartao.setBand(OperadorasExternasAprovaFacilEnum.valueOf(paramsVendaUnica.get(APF.Bandeira)));

//                cartao.setCodigoSeguranca(APF.decifrar(paramsVendaUnica.get(APF.CodSeguranca)));

//                String s = paramsVendaUnica.get(APF.NumCartao);
//                String ncard = APF.decifrar(s.substring(0, s.length() - 4));
//                cartao.setNumero(ncard);

                cartao.setIp(paramsEnvio.get(APF.IPComprador));

                cartao.setListaParcelas(transacaoOriginal.getListaParcelas());

                List<MovParcelaVO> parcelas = cartao.getListaParcelas();
                double valor = 0.0;
                for (MovParcelaVO mp : parcelas) {
                    valor = valor + mp.getValorParcela();
                }

                cartao.setNomeTitular(paramsEnvio.get(APF.NomePortador));

                cartao.setNumeroDocumento(paramsEnvio.get(APF.NumeroDoc));

                cartao.setParcelas(Integer.valueOf(paramsEnvio.get(APF.QtdParcelas)));

                cartao.setTransacaoAnterior(paramsEnvio.get(APF.TransAnterior));

                cartao.setUrl(transacaoOriginal.getUrlTransiente());

                cartao.setUsuarioResponsavel(transacaoOriginal.getUsuarioResponsavel());

                cartao.setValor(Uteis.arredondarForcando2CasasDecimais(valor));

                if (!cartao.getListaParcelas().isEmpty()) {
                    cartao.setEmpresa(cartao.getListaParcelas().get(0).getEmpresa().getCodigo());
                }

                cartao.setValorDocumento(
                        Formatador.formatarValorMonetario(cartao.getValor()));

                cartao.setValidade(Formatador.formatarValorNumerico(new Double(cartao.getMesValidade()), "00") + "/" + cartao.getAnoValidade());

                cartao.setCodExternoTransacaoAnterior(transacaoOriginal.getCodigoExterno());
                cartao.setIpClientePacto(transacaoOriginal.getIpTransiente());

                CartaoCreditoTO.validarDados(cartao);

                this.executarAprovacao(transacaoOriginal, cartao);

                if (transacaoOriginal.getValorAtributoCaptura(//se confirmou, gravar pagamento
                        APF.ResultSolicConfirmacao).contains(
                        APF.Confirmado)) {

                    gravarPagamento(transacaoOriginal, contratoRecorrencia, cliente,
                            transacaoOriginal.getUsuarioResponsavel());
                    transacaoOriginal.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);

                } else {

                    transacaoOriginal.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                }

                transacaoFacade.alterar(transacaoOriginal);
            }

        }

        return transacaoOriginal;
    }

    public TransacaoVO gravarPagamento(TransacaoVO transacao,
            ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente,
            final UsuarioVO usuario)
            throws Exception {

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();

        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setFormaPagamento(aprovaRemessa.getFormaPagamento());
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(Uteis.arredondarForcando2CasasDecimais(transacao.getValor()));
        movPagamentoVO.setValorTotal(Uteis.arredondarForcando2CasasDecimais(transacao.getValor()));
        movPagamentoVO.setNomePagador(cliente.getPessoa().getNome().isEmpty() ? transacao.getNomePessoa() : cliente.getPessoa().getNome());
        movPagamentoVO.setPessoa(cliente.getPessoa());
        movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
        movPagamentoVO.setNrParcelaCartaoCredito(1);
        Map<String, String> map = Uteis.obterMapFromString(transacao.getParamsEnvio());
        OperadorasExternasAprovaFacilEnum op = OperadorasExternasAprovaFacilEnum.valueOf(map.get(APF.Bandeira));
        OperadoraCartaoVO operadora = aprovaRemessa.obterOperadoraPorEnum(op);
        movPagamentoVO.setOperadoraCartaoVO(operadora);
        movPagamentoVO.setResponsavelPagamento(usuario);
        movPagamentoVO.setEmpresa(empresaFacade.consultarPorCodigo(transacao.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        listaPagamento.add(movPagamentoVO);

        ReciboPagamentoVO reciboObj = aprovaRemessa.getMovPagamentoDAO().incluirListaPagamento(
                listaPagamento,
                transacao.getListaParcelas(),
                null,
                contratoRecorrencia != null ? contratoRecorrencia.getContrato() : null,
                false, 0.0);

        transacao.setReciboPagamento(reciboObj.getCodigo());
        transacao.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo());
        transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
        transacaoFacade.alterar(transacao);
        if (contratoRecorrencia != null) {
            contratoRecorrencia.setUltimaTransacaoAprovada(transacao.getCodigoExterno());
            aprovaRemessa.getContratoRecorrenciaDAO().alterar(contratoRecorrencia);
        }
        transacaoFacade.alterar(transacao);

        return transacao;

    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal,
            ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente)
            throws Exception {
        String urlCaptura = transacaoOriginal.getUrlTransiente().replace("/APC", "/CAP");

        init(urlCaptura);

        if (!transacaoOriginal.getCodigoExterno().isEmpty()) {
            //
            if (transacaoOriginal.getSituacao() == SituacaoTransacaoEnum.APROVADA
                    || transacaoOriginal.getSituacao() == SituacaoTransacaoEnum.ERRO_CAPTURA) {
                //se foi aprovada, temos que enviar o pedido de confirmação que efetivamente confirma o débito no cartão de crédito
                String retornoCaptura = executeRequestTransacao(urlCaptura, transacaoOriginal.getValorAtributoResposta(APF.Transacao));
                transacaoOriginal.setResultadoCaptura(retornoCaptura);

                if (transacaoOriginal.getValorAtributoCaptura(//se confirmou, gravar pagamento
                        APF.ResultSolicConfirmacao).contains(
                        APF.Confirmado)) {

                    gravarPagamento(transacaoOriginal, contratoRecorrencia, cliente,
                            transacaoOriginal.getUsuarioResponsavel());
                    transacaoOriginal.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                } else {

                    transacaoOriginal.setSituacao(SituacaoTransacaoEnum.ERRO_CAPTURA);
                }

                transacaoFacade.alterar(transacaoOriginal);

            }


        }
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        transacaoOriginal.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception{
        //não implementado
    }

    /*public static void main(String... args) {
        Connection con;
        try {
            con = new Conexao("*******************************************************************",
                    "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE(con);
            Uteis.logar(null, new AprovaFacilService(con).executeRequestTransacao("https://www.aprovafacil.com/cgi-bin/APFW/easyfit001/CAP", "73594661172329"));
        } catch (Exception ex) {
            Logger.getLogger(AprovaFacilService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }*/
}

package servicos.impl.pinbank;


import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created by Estulano on 23/02/2022.
 */

public class TransacaoPinBankVO extends TransacaoVO {

    @Override
    public String getResultadoRequisicao() {
        String resultado = "";
        if (!UteisValidacao.emptyString(getReturnCode())) {
            resultado = PinBankRetornoEnum.valueOff(getReturnCode()).getDescricao();
            try {
                String motivo = getReason();
                if (!UteisValidacao.emptyString(motivo) && !UteisValidacao.emptyString(resultado)) {
                    resultado += ": " + motivo;
                } else {
                    resultado += motivo;
                }
            } catch (Exception e) {
                //ignore
            }
            return resultado;
        }
        return resultado;
    }

    @Override
    public String getCartaoMascarado() {
        try {
            return getValorCartaoMascarado();
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String getValorCartaoMascarado() throws Exception {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String creditCardMascarado = obj.optString("numeroCartao");
            return creditCardMascarado;
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getTID() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.PINBANK)) {
                JSONObject obj = new JSONObject(getParamsEnvio());
                return obj.getJSONObject("Data").optString("DescricaoPedido");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getAutorizacao() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            return obj.getString("CodigoAutorizacao");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            return obj.getJSONObject("Data").optString("DescricaoPedido");
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                if (APF.ResultSolicCancel.equalsIgnoreCase(nomeAtributo)) {
                    valor = new JSONObject(getResultadoCancelamento()).getJSONObject("Data").optString("CodigoAutorizacaoCancelamento");
                    if (!UteisValidacao.emptyString(valor)) {
                        return "Sucesso: " + valor;
                    } else {
                        return "Não foi possível cancelar e não foi possível obter o motivo";
                    }
                }
            } catch (Exception e) {
                return valor;
            }
        }
        return valor;
    }

    @Override
    public String getCodErroExterno() {
        return getReturnCode();
    }

    public String getReturnCode() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            return json.optString("ResultCode");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getReason() {
        try {
            if (!UteisValidacao.emptyString(getParamsResposta())) {
                JSONObject json = new JSONObject(getParamsResposta());
                return json.optString("Message");
            }
        } catch (Exception ex) {
            return "";
        }
        return "";
    }

    @Override
    public String getNSU() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.PINBANK)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                String keyValue = obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao02);
                String jsonString = PinBankService.decryptjSON(obj.getJSONObject("Data").optString("Json"), keyValue);
                JSONObject json = new JSONObject(jsonString);
                return json.getString("NsuOperacao");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }
}

package servicos.impl.pinbank;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.DetalhesRequestEnviadaVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.DetalhesRequestEnviada;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.PinBankServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 21/02/2022
 */
public class PinBankService extends AbstractCobrancaOnlineServiceComum implements PinBankServiceInterface {

    private String URL_API_PINBANK = "";
    private String userName = "";
    private String keyValue = "";
    private String grantType = "";
    private String requestOrigin = "";
    private String codigoCanal = "";
    private String codigoCliente = "";
    private String keyLoja = "";

    //Criptografia
    private static byte[] IV = new byte[16];

    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private DetalhesRequestEnviada detalhesRequestEnviadaDAO;

    public PinBankService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
        this.detalhesRequestEnviadaDAO = new DetalhesRequestEnviada(con);
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API_PINBANK = PropsService.getPropertyValue(PropsService.urlApiPinBankProducao);
            } else {
                this.URL_API_PINBANK = PropsService.getPropertyValue(PropsService.urlApiPinBankSandbox);
            }
        }
        this.userName = this.convenioCobrancaVO.getCodigoAutenticacao01();
        this.keyValue = this.convenioCobrancaVO.getCodigoAutenticacao02();
        this.grantType = this.convenioCobrancaVO.getCodigoAutenticacao03();
        this.requestOrigin = this.convenioCobrancaVO.getCodigoAutenticacao04();
        this.codigoCanal = this.convenioCobrancaVO.getCodigoAutenticacao05();
        this.codigoCliente = this.convenioCobrancaVO.getCodigoAutenticacao06();
        this.keyLoja = this.convenioCobrancaVO.getCodigoAutenticacao07();
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoPinBankVO(), TipoTransacaoEnum.PINBANK, this.convenioCobrancaVO);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa;
            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                pessoa = this.pessoaDAO.consultarPorChavePrimaria(clienteVO.getPessoaResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);
            } else {
                pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);
            }

            JSONObject parametrosPagamento = montarParametrosPagamentoCartaoId(transacao, dadosCartao, pessoa);

            JSONObject jsonEncriptado = encriptarObjeto(parametrosPagamento);
            RespostaHttpDTO retorno = executarRequestPinBank("/Transacoes/EfetuarTransacaoCartaoIdEncrypted", jsonEncriptado.toString(), null, MetodoHttpEnum.POST);
            processarRetorno(transacao, retorno.getResponse());

            transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
            addNumeroCartaoNoOutrasInformacoes(transacao, dadosCartao.getNumero());
            transacaoDAO.alterar(transacao);

            return transacao;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    public void addNumeroCartaoNoOutrasInformacoes(TransacaoVO transacaoVO, String numeroCartao) throws Exception {
        try {
            JSONObject json = new JSONObject(transacaoVO.getOutrasInformacoes());
            json.put("numeroCartao", APF.getCartaoMascarado(numeroCartao));
            transacaoVO.setOutrasInformacoes(json.toString());
        } catch (Exception e) {}
    }

    @Override
    public RespostaHttpDTO consultarTransacao(String codigoExterno) throws Exception {
        //PinBank não tem endpoint de consultar detalhes da trasação;
//        return executarRequestStripe("/payment_intents/" + codigoExterno, null, null, MetodoHttpEnum.GET);
        return null;
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para PinBank");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
//        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        JSONObject paramsRefund = montarObjetoRefund(transacaoVO);
        RespostaHttpDTO retorno = executarRequestPinBank("/Transacoes/CancelarTransacaoEncrypted", paramsRefund.toString(), null, MetodoHttpEnum.POST);
        processarRetornoCancelamento(transacaoVO, retorno.getResponse());
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoVO.setDataHoraCancelamento(Calendario.hoje());
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
        JSONObject retornoJSON = new JSONObject(retornoCancelamento);

        try {
            retornoJSON.getJSONObject("Data");
        } catch (Exception ex) {
            if (!UteisValidacao.emptyString(retornoJSON.optString("Message"))) {
                throw new Exception("Não foi possível processar o cancelamento da transação: " + retornoJSON.optString("Message"));
            }
            throw new Exception("Não foi possível processar o cancelamento da transação: " + retornoCancelamento);
        }

        String jsonDecrypted = desencriptarObjeto(retornoJSON.getJSONObject("Data").optString("Json"));
        transacaoVO.setResultadoCancelamento(jsonDecrypted);
        transacaoVO.setDataHoraCancelamento(Calendario.hoje());

        try {
            JSONObject cancelamentoJSON = new JSONObject(jsonDecrypted);

            if (retornoJSON.has("ResultCode") && !UteisValidacao.emptyString(cancelamentoJSON.getJSONObject("Data").optString("CodigoAutorizacaoCancelamento"))) {
                int resultCode = retornoJSON.getInt("ResultCode");
                if (resultCode == 0 && retornoJSON.optString("Message").equalsIgnoreCase("Success")) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            } else if (retornoJSON.has("ResultCode")) {
                int status = retornoJSON.getInt("ResultCode");
                if (status == 35 && retornoJSON.optString("Message").contains("TRANSACAO JA CANCELADA")) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) throws Exception {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        JSONObject retornoJSON = new JSONObject(retorno);
        String jsonDecrypted = "";
        if (retornoJSON.optJSONObject("Data") != null) {
            jsonDecrypted = desencriptarObjeto(retornoJSON.getJSONObject("Data").optString("Json"));
            JSONObject jsonRetorno = new JSONObject(jsonDecrypted);
            if (retornoJSON.optString("Message").equalsIgnoreCase("Success") && !UteisValidacao.emptyString(retornoJSON.getJSONObject("Data").optString("Json"))) {
                if (!UteisValidacao.emptyString(jsonRetorno.optString("CodigoAutorizacao"))) {
                    transacao.setCodigoExterno(jsonRetorno.optString("CodigoAutorizacao"));
                }
                if (!UteisValidacao.emptyString(jsonRetorno.optString("NsuOperacao"))) {
                    transacao.setCodigoNSU(jsonRetorno.optString("NsuOperacao"));
                }
            }
        }
        transacao.setParamsResposta(jsonDecrypted);
        transacao.setBandeiraPagamento(null);

        //Valores possíveis de erro: 0 = Success, 1 = Empty, 2 = ExceptionError, 3 = ValidationError, 4 = BadRequest, 5 = BlockedUser, 6 = UserNotFound, 7 = CustomerAccountDocumentationNotApproved,
        // 8 = EmptySplit, 9 = InvalidTotalSplit, 10 = EmptyCartaoId, 11 = InvalidLayoutEmail,12 = EmptyRate
        int statusCode = retornoJSON.optInt("ResultCode");
        String message = retornoJSON.optString("Message");
        if (statusCode == 0 && message.equalsIgnoreCase("Success")) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        } else if (statusCode >= 1 && statusCode <= 12) {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private JSONObject montarParametrosPagamento(TransacaoVO transacaoVO, CartaoCreditoTO cartao, PessoaVO pessoaVO) {
        try {
            String identificador = "TRA" + transacaoVO.getCodigo();
            String amount = Integer.toString((int) (transacaoVO.getValor() * 100));
            String numeroCartao = "";
            if (!UteisValidacao.emptyString(cartao.getNumero())) {
                numeroCartao = cartao.getNumero();
            }

            //Objeto para pagamento
            JSONObject parametros = new JSONObject();
            parametros.put("CodigoCanal", Integer.parseInt(this.codigoCanal));
            parametros.put("CodigoCliente", Integer.parseInt(this.codigoCliente));
            parametros.put("KeyLoja", this.keyLoja);
            parametros.put("NomeImpresso", cartao.getNomeTitular());
            parametros.put("DataValidade", cartao.getAnoValidadeYYYY() + cartao.getMesValidadeApre()); //formato aaaa/MM
            parametros.put("NumeroCartao", numeroCartao);
            parametros.put("CodigoSeguranca", cartao.getCodigoSeguranca());
            parametros.put("Valor", Double.parseDouble(amount));
            parametros.put("FormaPagamento", String.valueOf(cartao.getParcelas() > 1 ? 2 : 1)); //1 = A Vista | 2 = Parcelado sem juros | 3 = Parcelado com juros
            parametros.put("QuantidadeParcelas", cartao.getParcelas());
            parametros.put("DescricaoPedido", identificador);
            if (!UteisValidacao.emptyString(pessoaVO.getCfp())) {
                parametros.put("CpfComprador", new BigInteger(Uteis.removerMascara(pessoaVO.getCfp())));
            }
            parametros.put("NomeComprador", transacaoVO.getNomePessoa());
            parametros.put("TransacaoPreAutorizada", false); //false já captura automaticamente

            JSONObject data = new JSONObject();
            data.put("Data", parametros);
            return data;
        } catch (Exception e) {
            return new JSONObject();
        }
    }

    private JSONObject montarParametrosPagamentoCartaoId(TransacaoVO transacaoVO, CartaoCreditoTO cartao, PessoaVO pessoaVO) throws Exception {
        try {
            String identificador = "TRA" + transacaoVO.getCodigo();
            Uteis.logarDebug("IDENTIFICADOR PINBANK: " + identificador);

            String numeroCartaoPassando = "";
            if (!UteisValidacao.emptyString(cartao.getNumero())) {
                numeroCartaoPassando = cartao.getNumero();
            }

            //tenta obter o idCard da PinBank ou cadastrará um novo
            String idCard = obterCardId(transacaoVO, numeroCartaoPassando, cartao, pessoaVO);

            if (UteisValidacao.emptyString(idCard)) {
                throw new Exception("Não foi possível incluir o cartão na PinBank");
            }
            if (idCard.contains("Erro:")) {
                throw new Exception(idCard);
            }

            String amount = Integer.toString((int) (transacaoVO.getValor() * 100));

            //Objeto para pagamento
            JSONObject parametros = new JSONObject();
            parametros.put("CodigoCanal", Integer.parseInt(this.codigoCanal));
            parametros.put("CodigoCliente", Integer.parseInt(this.codigoCliente));
            parametros.put("KeyLoja", this.keyLoja);
            parametros.put("CartaoId", idCard);
            parametros.put("Valor", Double.parseDouble(amount));
            parametros.put("FormaPagamento", String.valueOf(cartao.getParcelas() > 1 ? 2 : 1)); //1 = A Vista | 2 = Parcelado sem juros | 3 = Parcelado com juros
            parametros.put("QuantidadeParcelas", cartao.getParcelas());
            parametros.put("DescricaoPedido", identificador);
            if (!UteisValidacao.emptyString(pessoaVO.getCfp())) {
                parametros.put("CpfComprador", new BigInteger(Uteis.removerMascara(pessoaVO.getCfp())));
            }
            parametros.put("NomeComprador", transacaoVO.getNomePessoa().toUpperCase());
            parametros.put("TransacaoPreAutorizada", false); //false já captura automaticamente

            JSONObject data = new JSONObject();
            data.put("Data", parametros);
            return data;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private String tokenizarCartao(TransacaoVO transacaoVO, String numeroCartaoPassando, CartaoCreditoTO cartaoCreditoTO) throws Exception {

        try {
            String apelido = "CARD-TRA" + transacaoVO.getCodigo();
            String numeroCartaoPassandoMascarado = APF.getCartaoMascarado(numeroCartaoPassando);

            //Objeto para tokenização do cartão
            JSONObject dadosCartao = new JSONObject();
            dadosCartao.put("CodigoCanal", Integer.parseInt(this.codigoCanal));
            dadosCartao.put("CodigoCliente", Integer.parseInt(this.codigoCliente));
            dadosCartao.put("Apelido", apelido);
            dadosCartao.put("NomeImpresso", cartaoCreditoTO.getNomeTitular().toUpperCase());
            dadosCartao.put("NumeroCartao", numeroCartaoPassando);
            dadosCartao.put("DataValidade", cartaoCreditoTO.getAnoValidadeYYYY() + cartaoCreditoTO.getMesValidadeApre()); //formato aaaa/MM
            dadosCartao.put("CodigoSeguranca", cartaoCreditoTO.getCodigoSeguranca());
            dadosCartao.put("ValidarCartao", false);

            JSONObject card = new JSONObject();
            card.put("Data", dadosCartao);

            JSONObject cardMascarado = new JSONObject(card.toString());
            JSONObject dataMascarado = cardMascarado.getJSONObject("Data");
            dataMascarado.put("NumeroCartao", numeroCartaoPassandoMascarado);
            dataMascarado.put("CodigoSeguranca", "***");

            Date dataAtual = negocio.comuns.utilitarias.Calendario.hoje();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
            String dataFormatada = sdf.format(dataAtual);
            dataMascarado.put("DataRequisicao", dataFormatada);

            DetalhesRequestEnviadaVO detalhesVO = new DetalhesRequestEnviadaVO();
            detalhesVO.setDataRegistro(dataAtual);
            detalhesVO.setUrl(this.URL_API_PINBANK + "/Transacoes/IncluirCartaoEncrypted");
            detalhesVO.setBody(cardMascarado.toString());
            detalhesVO.setNomeTabelaForeignKey("transacao");
            detalhesVO.setCodigoTabelaForeignKey(transacaoVO.getCodigo());
            if (transacaoVO.getOrigem() != null) {
                detalhesVO.setOrigem(transacaoVO.getOrigem().getDescricao());
            }
            try {
                this.detalhesRequestEnviadaDAO.incluir(detalhesVO);
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao salvar detalhes iniciais da requisição: " + e.getMessage());
                e.printStackTrace();
            }

            long startTime = System.currentTimeMillis();

            JSONObject jsonEncriptado = encriptarObjeto(card);
            Uteis.logar(null, "Vou cadastrar cartão na PinBank... " + numeroCartaoPassandoMascarado);
            RespostaHttpDTO retorno = executarRequestPinBank("/Transacoes/IncluirCartaoEncrypted", jsonEncriptado.toString(), null, MetodoHttpEnum.POST);

            long tempoRequisicaoMs = System.currentTimeMillis() - startTime;
            
            int statusResponse = retorno.getHttpStatus();
            boolean sucesso = false;
            
            JSONObject retornoJsonEncriptado = new JSONObject(retorno.getResponse());
            
            if (statusResponse == 200 && !UteisValidacao.emptyString(retornoJsonEncriptado.optString("Message"))
                && retornoJsonEncriptado.optString("Message").equalsIgnoreCase("Success")) {
                sucesso = true;
            }
            
            try {
                DetalhesRequestEnviadaVO detalhesAtualizado = this.detalhesRequestEnviadaDAO.consultarPorChavePrimaria(detalhesVO.getCodigo());
                
                detalhesAtualizado.setResponse(retorno.getResponse());
                detalhesAtualizado.setStatusResponse(statusResponse);
                detalhesAtualizado.setTempoRequisicaoMs(tempoRequisicaoMs);
                detalhesAtualizado.setSucesso(sucesso);
                
                this.detalhesRequestEnviadaDAO.alterar(detalhesAtualizado);
            } catch (Exception e) {
                Uteis.logar(null, "Erro ao atualizar detalhes da requisição: " + e.getMessage());
                e.printStackTrace();
            }

            //verifica se o retorno foi de cartão já cadastrado e exclui caso já tenha sido
            if (!UteisValidacao.emptyString(retornoJsonEncriptado.optString("Message")) && retornoJsonEncriptado.optString("Message").contains("já")) {
                String retornoJsonDesencriptado = desencriptarObjeto(retornoJsonEncriptado.getJSONObject("Data").optString("Json"));
                JSONObject json = new JSONObject(retornoJsonDesencriptado);
                if (!UteisValidacao.emptyString(json.getJSONObject("Data").optString("CartaoId"))) {
                    String cartaoId = json.getJSONObject("Data").optString("CartaoId");
                    try {
                        boolean sucessoExclusaoCartao = excluirCartao(cartaoId);
                        if (sucessoExclusaoCartao) {
                            //Depois de exluir o cartão, tentar incluir novamente
                            RespostaHttpDTO retornoRepescagem = executarRequestPinBank("/Transacoes/IncluirCartaoEncrypted", jsonEncriptado.toString(), null, MetodoHttpEnum.POST);
                            JSONObject jsonRetornoRepescagem = new JSONObject(retornoRepescagem.getResponse());
                            if (!UteisValidacao.emptyString(jsonRetornoRepescagem.optString("Message")) && jsonRetornoRepescagem.optString("Message").equalsIgnoreCase("Success")) {
                                String retornoJsonDesencriptadoRepescagem = desencriptarObjeto(jsonRetornoRepescagem.getJSONObject("Data").optString("Json"));
                                JSONObject jsonRepescagem = new JSONObject(retornoJsonDesencriptadoRepescagem);
                                //retorna o id do cartão incluído com sucesso na repescagem
                                return jsonRepescagem.getJSONObject("Data").optString("CartaoId");
                            } else {
                                throw new Exception("Consegui excluir o cartão que existia mas não consegui incluir o novo na repescagem");
                            }
                        } else {
                            throw new Exception("Não foi possível excluir o cartão existente");
                        }
                    } catch (Exception e) {
                        throw e;
                    }
                }
            } else if (!UteisValidacao.emptyString(retornoJsonEncriptado.optString("Message")) && retornoJsonEncriptado.optString("Message").equalsIgnoreCase("Success")) {
                String retornoJsonDesencriptado = desencriptarObjeto(retornoJsonEncriptado.getJSONObject("Data").optString("Json"));
                JSONObject json = new JSONObject(retornoJsonDesencriptado);
                //retorna o id do cartão incluído com sucesso na repescagem
                return json.getJSONObject("Data").optString("CartaoId");
            } else if (!UteisValidacao.emptyString(retornoJsonEncriptado.optString("Message")) && retornoJsonEncriptado.optString("Message").equalsIgnoreCase("Autorização negada.")) {
                throw new Exception("Erro: Autorização Negada. O ip do servidor deve ser liberado na PinBank primeiramente.");
            } else if (!UteisValidacao.emptyString(retornoJsonEncriptado.optString("Message"))) {
                return "Erro: Não foi possível gerar o token do cartão na PinBank: " + retornoJsonEncriptado.optString("Message");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex.getMessage().contains("pois o Grant_type informado no convênio não é válido")) {
                return "Não foi possível gerar o token pra realizar a requisição pois o Grant_type informado no convênio não é válido. Caso o seu tipo seja \"Password\", tente escrever tudo minúsculo \"password\"";
            }
            return ex.getMessage();
        }
        return "";
    }

    public boolean excluirCartao(String cartaoId) throws Exception {
        try {
            //Objeto para exclusão do cartão
            JSONObject parametros = new JSONObject();
            parametros.put("CodigoCanal", Integer.parseInt(this.codigoCanal));
            parametros.put("CodigoCliente", Integer.parseInt(this.codigoCliente));
            parametros.put("CartaoId", cartaoId);

            JSONObject card = new JSONObject();
            card.put("Data", parametros);

            JSONObject jsonEncriptado = encriptarObjeto(card);
            RespostaHttpDTO retorno = executarRequestPinBank("/Transacoes/ExcluirCartaoEncrypted", jsonEncriptado.toString(), null, MetodoHttpEnum.POST);

            JSONObject retornoJsonEncriptado = new JSONObject(retorno.getResponse());

            if (!UteisValidacao.emptyString(retornoJsonEncriptado.optString("Message")) && retornoJsonEncriptado.optInt("ResultCode") == 0) {
                if (retornoJsonEncriptado.optString("Message").equalsIgnoreCase("Success")) {
                    return true; //sucesso na exclusão
                } else {
                    return false; // erro na exclusão
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível excluir o cartão. " + ex.getMessage());
        }
        return false;
    }

    private String obterCardId(TransacaoVO transacaoVO, String numeroCartaoPassando, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoa) {
        AutorizacaoCobrancaCliente autoDAO = null;
        try {
            autoDAO = new AutorizacaoCobrancaCliente(getCon());

            List<AutorizacaoCobrancaClienteVO> autorizacoesCadastradas = new ArrayList<>();
            //se estiver editando deve incluir um novo
            if (!cartaoCreditoTO.isEditandoAutorizacao()){
                autorizacoesCadastradas = autoDAO.consultarPorPessoaTipoAutorizacao(pessoa.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            }


            String numeroCartaoPassandoMascarado = APF.getCartaoMascarado(numeroCartaoPassando);

            //buscar a autorização de cobrança referente ao cartão que está sendo utilizado.
            AutorizacaoCobrancaClienteVO auto = null;
            if (!UteisValidacao.emptyList(autorizacoesCadastradas)) {
                for (AutorizacaoCobrancaClienteVO obj : autorizacoesCadastradas) {
                    if (obj.getCartaoMascarado().equalsIgnoreCase(numeroCartaoPassandoMascarado)) {
                        if (!UteisValidacao.emptyString(obj.getIdPinBank())) {
                            return obj.getIdPinBank();
                        } else {
                            auto = obj;
                            break;
                        }
                    }
                }
            }

            //Não encontrou nenhuma autorização de cobrança da PinBank com idPinBank preenchido então deve cadastrar o novo cartão
            String idCartao = tokenizarCartao(transacaoVO, numeroCartaoPassando, cartaoCreditoTO);

            if (idCartao.contains("Erro:")){
                return idCartao;
            }

            transacaoVO.setIdCartaoPinBank(idCartao);

            //se já tem autorização de cobrança.. então vou atualizar os dados no banco..
            if (auto != null && !UteisValidacao.emptyString(idCartao)) {
                auto.setIdPinBank(idCartao);
                autoDAO.alterarIdPinBank(auto);
                return auto.getIdPinBank();
            } else {
                return idCartao;
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            autoDAO = null;
        }
    }

    private JSONObject montarObjetoRefund(TransacaoVO transacaoVO) throws Exception {
        JSONObject jsonObject = new JSONObject();

        //para usar os dados do convênio de cobrança do momento da transação criada, pois o convênio pode ter sido alterado.
        String codigoCanal = obterCodigoCanalTransacao(transacaoVO);
        String codigoCliente = obterCodigoClienteTransacao(transacaoVO);
        String keyLoja = obterKeyLojaTransacao(transacaoVO);

        if (!UteisValidacao.emptyString(codigoCanal) && !UteisValidacao.emptyString(codigoCliente)) {
            jsonObject.put("CodigoCanal", Integer.parseInt(codigoCanal));
            jsonObject.put("CodigoCliente", Integer.parseInt(codigoCliente));
            jsonObject.put("KeyLoja", keyLoja);
        } else {
            jsonObject.put("CodigoCanal", Integer.parseInt(this.codigoCanal));
            jsonObject.put("CodigoCliente", Integer.parseInt(this.codigoCliente));
            jsonObject.put("KeyLoja", this.keyLoja);
        }
        jsonObject.put("Valor", Integer.toString((int) (transacaoVO.getValor() * 100)));
        jsonObject.put("NsuOperacao", transacaoVO.getCodigoNSU());

        JSONObject jsonEncriptar = new JSONObject();
        jsonEncriptar.put("Data", jsonObject);
        JSONObject jsonEncriptado = encriptarObjeto(jsonEncriptar);
        return jsonEncriptado;
    }

    private RespostaHttpDTO executarRequestPinBank(String endPoint, String body, Map<String, String> params, MetodoHttpEnum metodoHttpEnum) throws Exception {

        String path = this.URL_API_PINBANK + endPoint;

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + getTokenAcesso());
        headers.put("UserName", this.userName);
        headers.put("RequestOrigin", this.requestOrigin);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, params, body, metodoHttpEnum);
        return respostaHttpDTO;
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }

    private String getTokenAcesso() throws Exception {

        String path = this.URL_API_PINBANK + "/token";
        HttpPost post = new HttpPost(path);
        post.addHeader("Content-Type", "application/x-www-form-urlencoded");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("username", this.userName));
        params.add(new BasicNameValuePair("password", this.keyValue));
        params.add(new BasicNameValuePair("grant_type", this.grantType));
        post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

        HttpClient httpClient = ExecuteRequestHttpService.createConnector();
        HttpResponse response = httpClient.execute(post);
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

        try {
            JSONObject responseJSON = new JSONObject(responseJsonString);
            return responseJSON.optString("access_token");
        } catch (Exception ex) {
            if (responseJsonString.contains("unsupported_grant_type")) {
                throw new Exception("Erro: Não foi possível gerar o token pra realizar a requisição pois o Grant_type informado no convênio não é válido. Caso o seu tipo seja \"Password\", tente escrever tudo minúsculo \"password\" ");
            } else {
                throw new Exception("Erro:" + responseJsonString);
            }
        }



    }

    private JSONObject encriptarObjeto(JSONObject value) throws Exception {
        //PinBank só aceita requisições com JSON criptografado em AES - CBC | para testes de criptografia: https://www.devglan.com/online-tools/aes-encryption-decryption
        //pega o objeto parametrosPagamento e criptografa
        String jsonEncriptado = encryptJson(value.toString(), this.keyValue);

        JSONObject json = new JSONObject();
        json.put("Json", jsonEncriptado);

        //monta o JSON final adicionando o outro objeto criptografado nele como parâmetro
        JSONObject jsonEncrypted = new JSONObject();
        jsonEncrypted.put("Data", json);

        return jsonEncrypted;
    }

    private String desencriptarObjeto(String value) throws Exception {
        return decryptjSON(value, this.keyValue);
    }

    public static String encryptJson(String value, String key) {
        try {
            IvParameterSpec iv = new IvParameterSpec(IV);
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] encrypted = cipher.doFinal(value.getBytes());

            return DatatypeConverter.printBase64Binary(encrypted);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static String decryptjSON(String encrypted, String key) {
        try {
            IvParameterSpec iv = new IvParameterSpec(IV);
            SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] original = cipher.doFinal(DatatypeConverter.parseBase64Binary(encrypted));

            return new String(original);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return null;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        JSONObject payment = parametrosPagamento.getJSONObject("Data");
        if (payment.has("CodigoSeguranca")) {
            payment.put("CodigoSeguranca", "***");
        }
        return parametrosPagamento.toString();
    }

    private String obterCodigoCanalTransacao(TransacaoVO transacaoVO) {
        try {
            String codigoCanal = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao05);
            if (!UteisValidacao.emptyString(codigoCanal)) {
                return codigoCanal;
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    private String obterCodigoClienteTransacao(TransacaoVO transacaoVO) {

        try {
            String codigoCliente = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao06);
            if (!UteisValidacao.emptyString(codigoCliente)) {
                return codigoCliente;
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    private String obterKeyLojaTransacao(TransacaoVO transacaoVO) {

        try {
            String keyLoja = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao07);
            if (!UteisValidacao.emptyString(keyLoja)) {
                return keyLoja;
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }
}

package servicos.impl.pinbank;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/*
 * Created by <PERSON> on 24/02/2022.
 */

public enum PinBankRetornoEnum {

    //Estes são os enum de erro da API. Os da adquirente não são disponibilizados pela PinBank.
    //Para exibir o motivo de uma transação negada deverá ser usado o valor do campo "ErrorCode" dos paramsresposta, ao invés de um enum.

    RetornoNENHUM("NENHUM", ""),
    Status1("1", "Empty"),
    Status2("2", "ExceptionError"),
    Status3("3", "ValidationError"),
    Status4("4", "BadRequest"),
    Status5("5", "BlockedUser"),
    Status6("6", "UserNotFound"),
    Status7("7", "CustomerAccountDocumentationNotApproved"),
    Status8("8", "EmptySplit"),
    Status9("9", "InvalidTotalSplit"),
    Status10("10", "EmptyCartaoId"),
    Status11("11", "InvalidLayoutEmail"),
    Status12("12", "EmptyRate"),
    ;

    private String codigo;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private PinBankRetornoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }


    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static PinBankRetornoEnum valueOff(String id) {
        for (PinBankRetornoEnum pinBankRetornoEnumStatusEnum : PinBankRetornoEnum.values()) {
            if (pinBankRetornoEnumStatusEnum.getCodigo().equals(id)) {
                return pinBankRetornoEnumStatusEnum;
            }
        }
        return RetornoNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }

}

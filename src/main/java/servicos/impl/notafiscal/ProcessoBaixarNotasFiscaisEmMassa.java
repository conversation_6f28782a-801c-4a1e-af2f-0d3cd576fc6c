package servicos.impl.notafiscal;

import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ProcessoBaixarNotasFiscaisEmMassa {

    private static final Object lock = new Object();

    public static void main(String[] args) throws Exception {
        String nomeEmpresaParaNomearArquivoFinal = "IRONBERG SANTOS";
        String nomeDoBanco = "bdzillyonironbergunidsantossp";
        Connection con = DriverManager.getConnection("********************************/" + nomeDoBanco, "postgres", "pactodb");
        String inicio = "2024-04-01";
        String fim = "2024-04-30";
        String caminhoArquivoFinalZip = "C:\\Users\\<USER>\\Desktop\\Notas\\" + nomeEmpresaParaNomearArquivoFinal + ".zip";

        int qtdLimitParaTestar = 10;

        List<String> linksNFSE = obterLinksDeDownloadNFSE(con, inicio, fim, qtdLimitParaTestar);
        List<String> linksNFCE = obterLinksDeDownloadNFCE(con, inicio, fim, qtdLimitParaTestar);

        System.out.println("-----------------------------------------");
        System.out.println("TOTAL DE ARQUIVOS A SEREM BAIXADOS: " + (linksNFSE.size() + linksNFCE.size()));
        System.out.println("-----------------------------------------");
        if (!UteisValidacao.emptyList(linksNFSE) || !UteisValidacao.emptyList(linksNFCE)) {
            baixarArquivosECompactar(linksNFSE, linksNFCE, caminhoArquivoFinalZip);
        }
    }

    public static List<String> obterLinksDeDownloadNFSE(Connection con, String inicio, String fim, int limit) throws Exception {
        System.out.println("Obtendo as NFSe's no banco de dados do período de " + inicio + " a " + fim + "...");
        ResultSet nfse = SuperFacadeJDBC.criarConsulta(" select codigo, jsonretorno from notafiscal n \n" +
                " where statusnota ilike 'Autorizada' \n" +
                " and dataautorizacao::date between '" + inicio + "' and '" + fim + "'\n" +
                " and tipo in (" + TipoNotaFiscalEnum.NFE.getCodigo() + "," + TipoNotaFiscalEnum.NFSE.getCodigo() + ")\n" +
                " order by codigo " + (UteisValidacao.emptyNumber(limit) ? "" : "limit " + limit), con);

        List<String> links = new ArrayList<>();
        while (nfse.next()) {
            String jsonRetornoNota = nfse.getString("jsonretorno");
            if (!UteisValidacao.emptyString(jsonRetornoNota)) {
                JSONObject json = new JSONObject(jsonRetornoNota);
                String link = json.optString("nfeLinkXml");
                if (!UteisValidacao.emptyString(link)) {
                    links.add(link);
                } else {
                    String linkAlternativo = json.optString("linkDownloadXML");
                    if (!UteisValidacao.emptyString(linkAlternativo)) {
                        links.add(linkAlternativo);
                    } else {
                        int codigo = nfse.getInt("codigo");
                        System.out.println("NFSe cód. " + codigo + " sem link para download no json de retorno");
                    }
                }
            } else {
                int codigo = nfse.getInt("codigo");
                System.out.println("NFSe cód. " + codigo + " sem json de retorno");
            }
        }

        if (UteisValidacao.emptyList(links)) {
            System.out.println("Nenhuma NFSE encontrada!!!");
            return Collections.emptyList();
        } else {
            System.out.println(links.size() + " NFSe's encontradas...");
        }

        return links;
    }

    public static List<String> obterLinksDeDownloadNFCE(Connection con, String inicio, String fim, int limit) throws Exception {
        System.out.println("Obtendo as NFCe's no banco de dados do período de " + inicio + " a " + fim + "...");
        ResultSet nfce = SuperFacadeJDBC.criarConsulta(" select codigo, jsonretorno from notafiscal n \n" +
                " where statusnota ilike 'Autorizada' \n" +
                " and dataautorizacao::date between '" + inicio + "' and '" + fim + "'\n" +
                " and tipo = " + TipoNotaFiscalEnum.NFCE.getCodigo() + " \n" +
                " order by codigo " + (UteisValidacao.emptyNumber(limit) ? "" : "limit " + limit), con);

        List<String> links = new ArrayList<>();
        while (nfce.next()) {
            String jsonRetornoNota = nfce.getString("jsonretorno");
            if (!UteisValidacao.emptyString(jsonRetornoNota)) {
                JSONObject json = new JSONObject(jsonRetornoNota);
                String link = json.optString("nfeLinkXml");
                if (!UteisValidacao.emptyString(link)) {
                    links.add(link);
                } else {
                    String linkAlternativo = json.optString("linkDownloadXml");
                    if (!UteisValidacao.emptyString(linkAlternativo)) {
                        links.add(linkAlternativo);
                    } else {
                        int codigo = nfce.getInt("codigo");
                        System.out.println("NFCe cód. " + codigo + " sem link para download no json de retorno");
                    }
                }
            } else {
                int codigo = nfce.getInt("codigo");
                System.out.println("NFCe cód. " + codigo + " sem json de retorno");
            }
        }

        if (UteisValidacao.emptyList(links)) {
            System.out.println("Nenhuma NFCE encontrada!!!");
            return Collections.emptyList();
        } else {
            System.out.println(links.size() + " NFCe's encontradas...");
        }

        return links;
    }

    public static void baixarArquivosECompactar(List<String> linksNFSE, List<String> linksNFCE, String caminhoArquivoFinalZIP) {
        AtomicInteger totalArquivosBaixados = new AtomicInteger(0);
        int totalArquivos = linksNFSE.size() + linksNFCE.size();
        int batchSize = 1000;

        List<List<String>> batchesNFSE = partition(linksNFSE, batchSize);
        List<List<String>> batchesNFCE = partition(linksNFCE, batchSize);

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(batchesNFSE.size(), batchesNFCE.size()));

        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(caminhoArquivoFinalZIP))) {
            for (int i = 0; i < batchesNFSE.size(); i++) {
                List<String> batch = batchesNFSE.get(i);
                executor.submit(() -> processarLinks(batch, "NFSE", zos, totalArquivosBaixados, totalArquivos));
            }

            for (int i = 0; i < batchesNFCE.size(); i++) {
                List<String> batch = batchesNFCE.get(i);
                executor.submit(() -> processarLinks(batch, "NFCE", zos, totalArquivosBaixados, totalArquivos));
            }

            executor.shutdown();
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);

            System.out.println("Download e compactação concluídos!");

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    private static List<List<String>> partition(List<String> list, int batchSize) {
        List<List<String>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return batches;
    }

    private static void processarLinks(List<String> links, String tipo, ZipOutputStream zos, AtomicInteger totalArquivosBaixados, int totalArquivos) {
        byte[] buffer = new byte[1024];
        for (String link : links) {
            try {
                URL url = new URL(link);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("GET");

                String nomeArquivo = extrairNomeArquivo(conn.getHeaderField("Content-Disposition"));
                if (nomeArquivo == null || nomeArquivo.isEmpty()) {
                    nomeArquivo = link.substring(link.lastIndexOf('/') + 1);
                }

                String pasta;
                if (tipo.equals("NFSE")) {
                    pasta = "NFSE";
                } else if (tipo.equals("NFCE")) {
                    pasta = "NFCE";
                } else {
                    System.out.println("Link não contém indicação de NFSE ou NFCE: " + link);
                    continue;
                }

                synchronized (zos) {
                    zos.putNextEntry(new ZipEntry(pasta + "/" + nomeArquivo));

                    InputStream is = conn.getInputStream();
                    int length;
                    while ((length = is.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }

                    zos.closeEntry();
                    is.close();
                }

                int totalBaixados = totalArquivosBaixados.incrementAndGet();
                logProgresso(totalBaixados, totalArquivos);

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static String extrairNomeArquivo(String headerField) {
        if (headerField == null) {
            return null;
        }

        Pattern pattern = Pattern.compile("filename=(\"[^\"]*\"|[^;]*)");
        Matcher matcher = pattern.matcher(headerField);
        if (matcher.find()) {
            String nomeCompleto = matcher.group(1);
            if (nomeCompleto.startsWith("\"") && nomeCompleto.endsWith("\"")) {
                return nomeCompleto.substring(1, nomeCompleto.length() - 1);
            } else {
                return nomeCompleto;
            }
        } else {
            return null;
        }
    }

    private static void logProgresso(int arquivosBaixados, int totalArquivos) {
        synchronized (lock) {
            double percentualConcluido = (double) arquivosBaixados / totalArquivos * 100;
            System.out.println("Progresso: " + arquivosBaixados + " de " + totalArquivos + " arquivos baixados (" + String.format("%.2f", percentualConcluido) + "%)");
        }
    }
}

package servicos.impl.autenticacaoMs;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.utilitarias.Uteis;
import org.apache.commons.io.Charsets;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.microsservice.SuperMSService;
import servicos.oamd.OamdMsService;
import servicos.oamd.RedeEmpresaDataDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;

public class AutenticacaoMsService extends SuperMSService {

    public static String token(String chave) throws Exception {
        RedeEmpresaDataDTO reseEmpresaDataDTO = OamdMsService.urls();
        String url = reseEmpresaDataDTO.getServiceUrls().getAutenticacaoUrl() + "/login";

        // Refatorar usuário e chave para autentificação
        AutenticacaoDTO autenticacaoDTO = new AutenticacaoDTO();
        autenticacaoDTO.setChave(chave);
        autenticacaoDTO.setUsername("PACTOBR");
        if (Uteis.isAmbienteDesenvolvimentoTeste()) {
            autenticacaoDTO.setSenha(PropsService.getPropertyValue(PropsService.senhaPactoBrTeste));
        } else {
            autenticacaoDTO.setSenha(PropsService.getPropertyValue(PropsService.senhaPactoBr));
        }

        String response = ExecuteRequestHttpService.post(url, new JSONObject(autenticacaoDTO).toString(), new HashMap<>());
        AutenticacaoTokenDTO autenticacaoTokenDTO = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), AutenticacaoTokenDTO.class);
        return autenticacaoTokenDTO.getToken();
    }

    public static String token(String chave, String userName, String senha) throws Exception {
        return token(chave, userName, senha, null);
    }

    public static String token(String chave, String userName, String senha, String jSessionId) throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urls();
        String url = clientDiscoveryDataDTO.getServiceUrls().getAutenticacaoUrl() + "/login";

        // Refatorar usuário e chave para autentificação
        AutenticacaoDTO autenticacaoDTO = new AutenticacaoDTO();
        autenticacaoDTO.setChave(chave);
        autenticacaoDTO.setUsername(userName);
        autenticacaoDTO.setSenha(senha);
        autenticacaoDTO.setZwJSessionId(jSessionId);

        Uteis.logarDebug(String.format("AutenticacaoMsService => url %s chave %s login %s", url, chave, userName));

        String response = ExecuteRequestHttpService.post(url, new JSONObject(autenticacaoDTO).toString(), new HashMap<>());
        AutenticacaoTokenDTO autenticacaoTokenDTO = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), AutenticacaoTokenDTO.class);
        return autenticacaoTokenDTO.getToken();
    }

    public static String personaToken() throws Exception {
        return personaToken(null);
    }

    public static String personaToken(String chave) throws Exception {
        try {
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");

            String path_id_persona = PropsService.getPropertyValue("AUTH_SECRET_PERSONA_PATH");
            JSONObject personaId = new JSONObject();
            personaId.put("id", Uteis.readLineByLineJava8(path_id_persona).replace("\n", "").replace("\r", ""));
            if(chave != null) {
                personaId.put("chave", chave);
            }

            String result = ExecuteRequestHttpService.executeHttpRequest(baseUrl() + "/aut/gt",
                    personaId.toString(),
                    headers,
                    ExecuteRequestHttpService.METODO_POST,
                    Charsets.UTF_8.name());

            if(new JSONObject(result).optJSONObject("meta") != null){
                throw new Exception(new JSONObject(result).optJSONObject("meta").optString("message"));
            }

            return new JSONObject(result).optString("content");
        } catch (Exception e) {
            Uteis.logarDebug("Falha ao obter token persona de autenticação "+e.getMessage());
            throw new Exception(e);
        }
    }

    private static String baseUrl() throws Exception {
        ClientDiscoveryDataDTO clientDiscoveryDataDTO = null;
        try {
            clientDiscoveryDataDTO = DiscoveryMsService.urls();
            return clientDiscoveryDataDTO.getServiceUrls().getAutenticacaoUrl();
        } catch (Exception e) {
            Uteis.logarDebug("Falha ao obter url do serviço autenticacao ms do discovery "+e.getMessage());
            throw e;
        }
    }
}

package servicos.impl.movimentacoes;

import negocio.facade.jdbc.arquitetura.SuperEntidade;
import servicos.interfaces.movimentacoes.MovimentacoesAutomaticasService;

import java.sql.Connection;
import java.sql.Date;

public class MovimentacoesAutomaticasServiceImpl extends SuperEntidade implements MovimentacoesAutomaticasService {

    public MovimentacoesAutomaticasServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public MovimentacoesAutomaticasServiceImpl() throws Exception {
        super();
    }

    public void movimentarReceitaDia(Integer empresa, Date dia) throws Exception{

    }

}

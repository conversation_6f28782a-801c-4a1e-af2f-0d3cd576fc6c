package servicos.impl.fitnessCard;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoFitnessCardVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioPrivateLabelEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONObject;
import pacto.fitnesscard.FitnessCardConsumerWS;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.interfaces.FitnessCardServiceInterface;

import java.sql.Connection;
import java.util.Date;

/*
 * Created by Luiz Felipe
 */
public class FitnessCardService extends CobrancaOnlineService implements FitnessCardServiceInterface {

    private String urlFitnessCard = TipoConvenioPrivateLabelEnum.FITNESS_CARD.getUrlAPI();

    private Pessoa pessoa;
    private Log log;
    private Transacao transacao;
    private ConvenioCobranca convenioCobranca;
    private ConvenioCobrancaVO convenioFitnessCard;

    public FitnessCardService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoa = new Pessoa(con);
        this.log = new Log(con);
        this.transacao = new Transacao(con);
        this.convenioCobranca = new ConvenioCobranca(con);
        this.convenioFitnessCard = this.convenioCobranca.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        Empresa empresaDao = new Empresa(con);
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(dadosCartao.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        TransacaoVO transacao = criarTransacao(dadosCartao);
        JSONObject parametrosPagamento = criarJSONCriarTransacao(empresaVO, transacao, dadosCartao);
        transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
        transacao.setCodigo(0);
        new Transacao(con).incluir(transacao);
        FitnessCardConsumerWS consumerWS = new FitnessCardConsumerWS();
        JSONObject jsonObject = consumerWS.criarTransacao(urlFitnessCard, parametrosPagamento);
        processarRetorno(transacao, jsonObject);
        realizarCapturaTransacao(transacao);
        new Transacao(con).alterar(transacao);
        return transacao;
    }

    private TransacaoVO criarTransacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = new TransacaoFitnessCardVO();
        transacao.setUrlTransiente(dadosCartao.getUrl());
        transacao.setUsuarioResponsavel(dadosCartao.getUsuarioResponsavel());
        transacao.setNomePessoa(dadosCartao.getNomeTitular());
        transacao.setDataProcessamento(Calendario.hoje());
        transacao.setTipo(TipoTransacaoEnum.FITNESS_CARD);
        transacao.setEmpresa(dadosCartao.getEmpresa());
        transacao.setListaParcelas(dadosCartao.getListaParcelas());
        transacao.setValor(Uteis.arredondarForcando2CasasDecimais(dadosCartao.getValor()));
        if (dadosCartao.getIdPessoaCartao() != null && !UteisValidacao.emptyNumber(dadosCartao.getIdPessoaCartao())) {
            PessoaVO pessoa = new PessoaVO();
            pessoa.setCodigo(dadosCartao.getIdPessoaCartao());
            transacao.setPessoaPagador(pessoa);
        }
        return transacao;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) throws Exception {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        parametrosPagamento.put("numCartao", APF.getCartaoMascarado(parametrosPagamento.getString("numCartao")));
//        parametrosPagamento.put("cvv", APF.encriptar(parametrosPagamento.getString("cvv")));
        return parametrosPagamento.toString();
    }

    private JSONObject criarJSONCriarTransacao(EmpresaVO empresaVO, TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO) throws Exception {
        JSONObject dadosJSON = new JSONObject();
        dadosJSON.put("cnpj", Uteis.removerMascara(empresaVO.getCNPJ()));
        dadosJSON.put("codParcela", transacaoVO.getCodigo().toString());
        dadosJSON.put("valorParcelas", cartaoCreditoTO.getValor());
        dadosJSON.put("qtdeParcelas", cartaoCreditoTO.getParcelas());
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            dadosJSON.put("numCartao", cartaoCreditoTO.getNumero());
        }
        dadosJSON.put("nomeTitular", cartaoCreditoTO.getNomeTitular());
        dadosJSON.put("validadeCartao", cartaoCreditoTO.getValidade());
        dadosJSON.put("cvv", cartaoCreditoTO.getCodigoSeguranca());
        return dadosJSON;
    }

    private JSONObject criarJSONCadastrarAluno(ClienteVO clienteVO, Integer qtdeParcelas, Double valorParcelas, String diaVencimento) throws Exception {
        TelefoneVO telefoneResidencial = new TelefoneVO();
        TelefoneVO telefoneComercial = new TelefoneVO();
        TelefoneVO telefoneCelular = new TelefoneVO();
        for (TelefoneVO telefoneVO : clienteVO.getPessoa().getTelefoneVOs()) {
            if (telefoneVO.getTipoTelefone().equals(TipoTelefone.RESIDENCIAL.getCodigo())) {
                telefoneResidencial = telefoneVO;
            } else if (telefoneVO.getTipoTelefone().equals(TipoTelefone.COMERCIAL.getCodigo())) {
                telefoneComercial = telefoneVO;
            } else if (telefoneVO.getTipoTelefone().equals(TipoTelefone.CELULAR.getCodigo())) {
                telefoneCelular = telefoneVO;
            }
        }

        String cpf = clienteVO.getPessoa().getCfp();
        Date dataNascimento = clienteVO.getPessoa().getDataNasc();
        String cnpj = clienteVO.getEmpresa().getCNPJ();

        if (UteisValidacao.emptyString(cnpj)) {
            throw new Exception("Empresa não está com o CNPJ cadastrado.");
        }
        if (UteisValidacao.emptyString(cpf)) {
            throw new Exception(String.format("Cliente %s não possui CPF cadastrado.", clienteVO.getPessoa().getNome()));
        }
        if (dataNascimento == null) {
            throw new Exception(String.format("Cliente %s não possui data de nascimento.", clienteVO.getPessoa().getNome()));
        }

        JSONObject dadosJSON = new JSONObject();
        dadosJSON.put("cnpj", Uteis.removerMascara(cnpj));
        dadosJSON.put("dataCadastro", Uteis.getDataAplicandoFormatacao(clienteVO.getPessoa().getDataCadastro(), "dd/MM/yyyy HH:mm:ss"));
        dadosJSON.put("cpf", Uteis.removerMascara(cpf));
        dadosJSON.put("matricula", clienteVO.getMatricula());
        dadosJSON.put("nome", clienteVO.getPessoa().getNome());
        dadosJSON.put("dataNascimento", Uteis.getDataAplicandoFormatacao(dataNascimento, "dd/MM/yyyy"));
        dadosJSON.put("nomeResponsavel", "");
        dadosJSON.put("sexo", clienteVO.getPessoa().getSexo());
        dadosJSON.put("profissao", clienteVO.getPessoa().getProfissao().getDescricao());
        dadosJSON.put("estadoCivil", clienteVO.getPessoa().getEstadoCivil_Apresentar());
        dadosJSON.put("numeroRG", clienteVO.getPessoa().getRg());
        dadosJSON.put("telResidencial", telefoneResidencial.getNumero());
        dadosJSON.put("telComercial", telefoneComercial.getNumero());
        dadosJSON.put("telCelular", telefoneCelular.getNumero());

        if (UteisValidacao.emptyList(clienteVO.getPessoa().getEmailVOs())) {
            dadosJSON.put("email", "");
        } else {
            dadosJSON.put("email", clienteVO.getPessoa().getEmailVOs().get(0).getEmail());
        }

        if (UteisValidacao.emptyList(clienteVO.getPessoa().getEnderecoVOs())) {
            dadosJSON.put("cep", "");
            dadosJSON.put("endereco", "");
            dadosJSON.put("numero", "");
            dadosJSON.put("complemento", "");
            dadosJSON.put("bairro", "");
        } else {
            dadosJSON.put("cep", Uteis.removerMascara(clienteVO.getPessoa().getEnderecoVOs().get(0).getCep()));
            dadosJSON.put("endereco", clienteVO.getPessoa().getEnderecoVOs().get(0).getEndereco());
            dadosJSON.put("numero", clienteVO.getPessoa().getEnderecoVOs().get(0).getNumero());
            dadosJSON.put("complemento", clienteVO.getPessoa().getEnderecoVOs().get(0).getComplemento());
            dadosJSON.put("bairro", clienteVO.getPessoa().getEnderecoVOs().get(0).getBairro());
        }

        dadosJSON.put("cidade", clienteVO.getPessoa().getCidade().getNome());
        dadosJSON.put("uf", clienteVO.getPessoa().getEstadoVO().getSigla());
        dadosJSON.put("pais", clienteVO.getPessoa().getPais().getNome());
        dadosJSON.put("qtdeParcelas", qtdeParcelas);
        dadosJSON.put("valorParcelas", valorParcelas);
        dadosJSON.put("diaVencimento", diaVencimento);
        return dadosJSON;
    }

    public AutorizacaoCobrancaClienteVO cadastrarAlunoFitnessCard(Integer codCliente, Integer qtdeParcelas, Double valorParcelas, Integer diaVencimento, Integer convenioPrivateLavel) throws Exception {
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        Cliente clienteDao = new Cliente(con);
        ClienteVO clienteVO = clienteDao.consultarPorChavePrimaria(codCliente, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

        Empresa empresaDao = new Empresa(con);
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        clienteVO.setEmpresa(empresaVO);

        ConvenioCobranca convenioDao = new ConvenioCobranca(con);
        ConvenioCobrancaVO convenioCobrancaPrivate = convenioDao.consultarPorCodigoEmpresa(convenioPrivateLavel, empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        FitnessCardConsumerWS consumerWS = new FitnessCardConsumerWS();
        JSONObject jsonObject = consumerWS.cadastrarAluno(urlFitnessCard, criarJSONCadastrarAluno(clienteVO, qtdeParcelas, valorParcelas, diaVencimento.toString()));
        String retorno = jsonObject.getString("return");
        String numCartao = jsonObject.getString("numCartao");
        String valCartao = jsonObject.getString("valCartao");
        String nomeTitular = jsonObject.getString("nomeTitular");
        String cvv = jsonObject.getString("cvv");
        String retornoFitness = jsonObject.getString("retorno");
        String msg = jsonObject.getString("msg");

        if (!retornoFitness.equals("1")) {
            throw new Exception(msg);
        }

//        String mesCartao = valCartao.substring(0, 2);
//        String anoCartao = valCartao.substring(2, 4);

        AutorizacaoCobrancaClienteVO auto = new AutorizacaoCobrancaClienteVO();
        auto.setNumeroCartao(numCartao);
//        auto.setValidadeCartao(mesCartao + "/20" + anoCartao);
        auto.setValidadeCartao(valCartao);
        auto.setNomeTitularCartao(nomeTitular);
        auto.setCliente(clienteVO);
        auto.setTipoAutorizacao(convenioCobrancaPrivate.getTipo().getTipoAutorizacao());
        auto.setConvenio(convenioCobrancaPrivate);
        auto.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
        auto.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.COM_VC);

        AutorizacaoCobrancaCliente cobrancaDao = new AutorizacaoCobrancaCliente(con);
        cobrancaDao.incluir(auto);
        return auto;
    }

    private void realizarCapturaTransacao(TransacaoVO transacaoVO) throws Exception {
        try {
            if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno()) && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {

                Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());

                Empresa empresaDao = new Empresa(con);
                EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                JSONObject dadosJSON = new JSONObject();
                dadosJSON.put("cnpj", Uteis.removerMascara(empresaVO.getCNPJ()));
                dadosJSON.put("codParcela", transacaoVO.getCodigoExterno());
                dadosJSON.put("valorParcelas", transacaoVO.getValor());

                FitnessCardConsumerWS consumerWS = new FitnessCardConsumerWS();
                JSONObject jsonObject = consumerWS.capturarTransacao(urlFitnessCard, dadosJSON);

                String status = jsonObject.getString("status");

                if (status.equals("2")) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                }
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public String consultarTransacao(TransacaoVO transacaoVO) throws Exception {
//        return executarRequestCieloConsulta(null, "/1/sales/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
        return "";
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacao;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarCapturaTransacao(transacaoOriginal);
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        new Transacao(con).alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        transacaoOriginal.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoNova, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        transacaoNova.setDataProcessamento(Calendario.hoje());
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        new Transacao(con).incluir(transacaoNova);
        JSONObject parametrosEnviar = decifrarDadosSigilososReEnvio(transacaoNova);
        FitnessCardConsumerWS consumerWS = new FitnessCardConsumerWS();
        JSONObject jsonObject = consumerWS.criarTransacao(urlFitnessCard, parametrosEnviar);
        processarRetorno(transacaoNova, jsonObject);
        new Transacao(con).alterar(transacaoNova);
        return transacaoNova;
    }

    private JSONObject decifrarDadosSigilososReEnvio(TransacaoVO transacaoVO) throws Exception {
        JSONObject parametrosEnvio = new JSONObject(transacaoVO.getParamsEnvio());
        NazgDTO nazgDTO = obterNazgTO(parametrosEnvio.getString("tokenAragorn"));
        parametrosEnvio.put("numCartao", nazgDTO.getCard());
//        parametrosEnvio.put("cvv", APF.decifrar(parametrosEnvio.getString("cvv")));
        return parametrosEnvio;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        Empresa empresaDao = new Empresa(con);
        EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        JSONObject dadosJSON = new JSONObject();
        dadosJSON.put("cnpj", Uteis.removerMascara(empresaVO.getCNPJ()));
        dadosJSON.put("codParcela", transacaoVO.getCodigoExterno());
        dadosJSON.put("valorParcelas", transacaoVO.getValor());

        FitnessCardConsumerWS consumerWS = new FitnessCardConsumerWS();
        JSONObject jsonObject = consumerWS.cancelarTransacao(urlFitnessCard, dadosJSON);
        processarRetornoCancelamento(transacaoVO, jsonObject);
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && (estornarRecibo && transacaoVO.getReciboPagamento() != 0)) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, JSONObject jsonObject) throws Exception {
        transacao.setResultadoCancelamento(jsonObject.toString());
        try {
            String status = jsonObject.getString("status");

            if (status.equals("10")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        } catch (Exception e) {
//            consultarSituacaoTransacao(transacao);
        }
    }

    private void consultarSituacaoTransacao(TransacaoVO transacao) throws Exception {
//        String retorno = executarRequestCieloConsulta(null, "/1/sales/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
        String retorno = "";
        processarRetornoConsultaTransacao(transacao, retorno);
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoConsultaTransacao(TransacaoVO transacao, String retorno) throws Exception {
        try {
//            JSONObject retornoTransacao = new JSONObject(retorno);
//            JSONObject payment = retornoTransacao.getJSONObject("Payment");
//            Integer status = payment.getInt("status");

//            if (status.equals(CieloECommerceStatusEnum.ANULADO.getId())) {
//                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
//            } else if (status.equals(CieloECommerceStatusEnum.AUTORIZADO.getId())) {
//                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
//            } else if (status.equals(CieloECommerceStatusEnum.PAGAMENTO_CONFIRMADO.getId())) {
//                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
//            } else {
//                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
//            }
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private void processarRetorno(TransacaoVO transacao, JSONObject retornoJSON) {
        transacao.setParamsResposta(retornoJSON.toString());
        try {

            String codAuto = retornoJSON.getString("codAuto");
            String identificadorAuto = retornoJSON.getString("identificadorAuto");
            String status = retornoJSON.getString("status");
            String retorno = retornoJSON.getString("retorno");
            String msg = retornoJSON.getString("msg");

            transacao.setCodigoExterno(identificadorAuto);

            if (status.equals("1")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
                transacao.setPermiteRepescagem(false);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                transacao.setPermiteRepescagem(true);
            }
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            transacao.setPermiteRepescagem(true);
        }
    }

    public Pessoa getPessoa() throws Exception {
        if (this.pessoa == null) {
            this.pessoa = new Pessoa(getCon());
        }
        return pessoa;
    }

    public Log getLog() throws Exception {
        if (this.log == null) {
            this.log = new Log(getCon());
        }
        return log;
    }

    public ConvenioCobrancaVO getConvenioFitnessCard() {
        if (convenioFitnessCard == null) {
            convenioFitnessCard = new ConvenioCobrancaVO();
        }
        return convenioFitnessCard;
    }

    public void setConvenioFitnessCard(ConvenioCobrancaVO convenioFitnessCard) {
        this.convenioFitnessCard = convenioFitnessCard;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception{
        //não implementado
    }
}

package servicos.impl.pagolivre;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.financeiro.Transacao;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

import static negocio.comuns.utilitarias.Calendario.somarDias;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class PagoLivreConciliacaoService {

    private final static String URL_API_CONCILIACAO = PropsService.getPropertyValue(PropsService.urlApiPagoLivreConciliacaoProducao);
    private final static String URL_API_ORDERS = URL_API_CONCILIACAO + "/orders";
    private final static String URL_API_CONCILIACAO_DEPOSITS_CONSOLIDATED = URL_API_CONCILIACAO + "/orders/GetDepositsConsolidated";


    public static void processarExtratos(EmpresaVO empresa, ConvenioCobrancaVO convenio,
                                         Date reprocessarAPartirDe, Date reprocessarAte,
                                         ResultadoServicosVO resultadoExtrato) throws Exception {

        if (UteisValidacao.emptyString(convenio.getCodigoAutenticacao04())) {
            throw new Exception(convenio.getLabelCodigoAutenticacao04() + " Não informado!");
        }

        int dia = 1;
        int i = 0;
        if (reprocessarAPartirDe != null && Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte)) >= 0)
            dia = Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte));
        for (; i <= dia; i++) {

            Date dataTransacao = somarDias((reprocessarAPartirDe != null ? reprocessarAPartirDe : Calendario.ontem()), i);
            if (Calendario.maiorOuIgual(dataTransacao, Calendario.hoje())) {
                continue;
            }

            processar(convenio, empresa, dataTransacao, TipoConciliacaoEnum.VENDAS, reprocessarAPartirDe, resultadoExtrato);
            processar(convenio, empresa, dataTransacao, TipoConciliacaoEnum.PAGAMENTOS, reprocessarAPartirDe, resultadoExtrato);
        }
    }

    private static void processar(ConvenioCobrancaVO convenio, EmpresaVO empresa, Date dataTransacao,
                                  TipoConciliacaoEnum tipoConciliacaoEnum,
                                  Date reprocessarAPartirDe, ResultadoServicosVO resultadoExtrato) {

        String arquivoProcessamentoTemp = "PagoLivreConciliacao|" + Calendario.getDataAplicandoFormatacao(dataTransacao, "yyyyMMdd") + "|" + tipoConciliacaoEnum.name() + "|" + convenio.getCodigoAutenticacao01();
        String retorno = "";
        try {
            if (getFacade().getExtratoDiarioItem().arquivoProcessado(arquivoProcessamentoTemp)) {
                Uteis.logarDebug("Arquivo já processado: " + arquivoProcessamentoTemp);
                return;
            }

            int tentativa = 0;
            while (tentativa <= 10) {
                try {
                    ++tentativa;
                    Uteis.logarDebug("Tentativa: " + tentativa + " | Buscando conciliação " + tipoConciliacaoEnum.name() + " do dia: " +
                            Uteis.getData(dataTransacao) + " utilizando PagoLivre - Merchant_id: " + convenio.getCodigoAutenticacao01() +
                            (!UteisValidacao.emptyString(convenio.getDescricao()) ? " | CONVÊNIO: " + convenio.getDescricao() : ""));

                    retorno = executarRequest(convenio, dataTransacao, tipoConciliacaoEnum);

                    if (!UteisValidacao.emptyString(retorno)) {
                        break;
                    }

                    if (reprocessarAPartirDe == null) { //só aguardar quando é o processo automatico, reprocessamento não precisa aguardar
                        Uteis.logarDebug("Erro... Aguardar 5 segundos e tentar novamente extrato");
                        Thread.sleep(5000);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    if (ex.getMessage() != null &&
                            ex.getMessage().contains("does not have access")) { //se não concedeu acesso não consultar mais que uma vez...
                        break;
                    }
                    if (reprocessarAPartirDe == null) { //só aguardar quando é o processo automatico, reprocessamento não precisa aguardar
                        Uteis.logarDebug("Erro... Aguardar 5 segundos e tentar novamente extrato | " + ex.getMessage());
                        Thread.sleep(5000);
                    }
                }
            }
            if (!UteisValidacao.emptyString(retorno)) {
                List<ExtratoDiarioItemVO> lista = new ArrayList<>();
                if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS)) {
                    lista.addAll(processarVendas(retorno, convenio, empresa, arquivoProcessamentoTemp, dataTransacao, tipoConciliacaoEnum));
                } else if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                    lista.addAll(processarPagamentos(retorno, convenio, empresa, arquivoProcessamentoTemp, dataTransacao, tipoConciliacaoEnum));
                }

                Uteis.logarDebug("Conciliação PagoLivre | " + tipoConciliacaoEnum.name() + " | Capturou " + lista.size());
                if (!lista.isEmpty()) {
                    getFacade().getExtratoDiarioItem().processarListaExtratoDiario(lista, false, convenio);
                }
                try {
                    preencherPessoaItemExtrato(lista, getFacade().getCliente().getCon());
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultadoExtrato.getResultado().put(ex.getMessage());
            Uteis.logarDebug("Erro ao processar '" + arquivoProcessamentoTemp + "' | " + ex.getMessage());
        }
    }

    public static String executarRequest(ConvenioCobrancaVO convVO, Date dataTransacao,
                                          TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        String url = "https://api.pagolivre.com.br/api/v2/orders/GetDepositsConsolidated/" + sdf.format(dataTransacao) + "/" + sdf.format(dataTransacao);
        String url = URL_API_CONCILIACAO_DEPOSITS_CONSOLIDATED + "/" + sdf.format(dataTransacao) + "/" + sdf.format(dataTransacao);
        if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS)) {
            url = URL_API_ORDERS;
            Date dataTransacaoInicio = Calendario.getDataComHoraZerada(dataTransacao);
            Date dataTransacaoFim = Calendario.getDataComUltimaHora(dataTransacao);
            params.put("initDate", dataTransacaoInicio.toInstant().toString());
            params.put("endDate", dataTransacaoFim.toInstant().toString());
        }


        String tokenConciliacao = convVO.getCodigoAutenticacao04();
        if (!tokenConciliacao.contains("Basic")) {
            tokenConciliacao = ("Basic " + convVO.getCodigoAutenticacao04());
        }

        headers.put("Content-Type", "application/json");
//        headers.put("Authorization", "Basic ****************************************************************************************************************************************");
        headers.put("Authorization", tokenConciliacao);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, params, null, MetodoHttpEnum.GET);
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception("Erro consultar " + tipoConciliacaoEnum.name() + ": HttpStatus: " + respostaHttpDTO.getHttpStatus() + (UteisValidacao.emptyString(respostaHttpDTO.getResponse()) ? "" : (" | Resposta: " + respostaHttpDTO.getResponse())));
        }
        return respostaHttpDTO.getResponse();
    }

    private static List<ExtratoDiarioItemVO> processarVendas(String retorno, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                                             String arquivoProcessamentoTemp, Date dataArquivo,
                                                             TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {

        List<ExtratoDiarioItemVO> lista = new ArrayList<>();
        JSONArray array = new JSONArray(retorno);
        for (int i = 0; i < array.length(); i++) {
            JSONObject item = array.getJSONObject(i);
            lista.addAll(processarItemVenda(item, convenio, empresa, arquivoProcessamentoTemp, dataArquivo, tipoConciliacaoEnum));
        }
        return lista;
    }

    private static List<ExtratoDiarioItemVO> processarPagamentos(String retorno, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                                                 String arquivoProcessamentoTemp, Date dataArquivo,
                                                                 TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {
        List<ExtratoDiarioItemVO> lista = new ArrayList<>();
        JSONArray array = new JSONArray(retorno);
        for (int i = 0; i < array.length(); i++) {
            JSONObject json1 = array.getJSONObject(i);
            JSONArray itens = json1.getJSONArray("itens");

            for (int i2 = 0; i2 < itens.length(); i2++) {
                JSONObject item = itens.getJSONObject(i2);
                lista.addAll(processarItemPagamento(item, convenio, empresa, arquivoProcessamentoTemp, dataArquivo, tipoConciliacaoEnum));
            }
        }
        return lista;
    }

    public static void preencherPessoaItemExtrato(List<ExtratoDiarioItemVO> extratos, Connection con) throws Exception {
        ExtratoDiarioItem extratoDiarioItemDAO;
        try {
            extratoDiarioItemDAO = new ExtratoDiarioItem(con);

            for (ExtratoDiarioItemVO extrato : extratos) {
                try {
                    //buscar por movpagamento que já existe no item do extrato
                    if (extrato.getMovPagamento() != null && extrato.getMovPagamento().getPessoa() != null) {
                        if (!UteisValidacao.emptyNumber(extrato.getMovPagamento().getPessoa().getCodigo())) {
                            extrato.getPessoa().setCodigo(extrato.getMovPagamento().getPessoa().getCodigo());
                        }
                    }
                    if (UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        //buscar no sistema e nas transações a pessoa do extrato
                        extratoDiarioItemDAO.obterPessoaExtrato(extrato);
                    }
                    if (!UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        extratoDiarioItemDAO.incluirInfoCodPessoa(extrato);
                    }
                } catch (Exception ignore) {
                    if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao() + " e nem pelo nsu: " + extrato.getNsu());
                    } else if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao());
                    } else if (UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pelo nsu: " + extrato.getNsu());
                    }
                }
            }
        } finally {
            extratoDiarioItemDAO = null;
        }
    }

    private static List<ExtratoDiarioItemVO> processarItemPagamento_ANT(JSONObject item, ConvenioCobrancaVO convenioVO, EmpresaVO empresaVO,
                                                                        String arquivoProcessamentoTemp, Date dataArquivo,
                                                                        TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyyMMdd");
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        Map<String, String> mapa = new HashMap();
        mapa.put("item", item.toString());

        String nsu = item.getString("paymentID");

        Transacao transacaoDAO = new Transacao(getFacade().getCliente().getCon());
        TransacaoVO transacaoVO = transacaoDAO.consultarPorCodigoExternoETipo(nsu, convenioVO.getTipo().getTipoTransacao());
        transacaoDAO = null;

        ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(mapa);
        extratoDiarioItemVO.setDataLancamento(Calendario.hoje());
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.PAGAMENTOS.getCodigo());
        extratoDiarioItemVO.setNsu(nsu);
        extratoDiarioItemVO.setAutorizacao(transacaoVO != null ? transacaoVO.getAutorizacao() : "");
        extratoDiarioItemVO.setEstabelecimento(convenioVO.getCodigoAutenticacao01());
        extratoDiarioItemVO.setNrCartao(transacaoVO != null ? transacaoVO.getCartaoMascarado() : "");
        extratoDiarioItemVO.setNrParcela(item.getInt("installment"));
        extratoDiarioItemVO.setNrTotalParcelas(item.getInt("installments"));
        extratoDiarioItemVO.setApresentarExtrato(true);
        extratoDiarioItemVO.setConvenio(convenioVO);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresaVO);
        extratoDiarioItemVO.setEstorno(false);
//        extratoDiarioItemVO.setRo(transaction.getAcquirerTransactionKey());


        double valorBruto = item.getDouble("fullAmount");
        double valorLiquido = item.getDouble("amount");
        double valorComissao = valorBruto - valorLiquido;
        double taxa1 = item.getDouble("discountPercent");
        double taxa = 0.0;
        double taxaAntecipacao = 0.0;

        extratoDiarioItemVO.setValorBruto(valorBruto);
        extratoDiarioItemVO.setValorLiquido(valorLiquido);
        extratoDiarioItemVO.setValorComissao(valorComissao);

        if (extratoDiarioItemVO.isAntecipacao()) {
//            extratoDiarioItemVO.setValorDescontadoAntecipacao(stoneConciliationInstallment.getAdvanceRateAmount());  //valor em R$ descontado pela taxa de antecipação (double).
//            taxaAntecipacao = (extratoDiarioItemVO.getValorDescontadoAntecipacao() * 100) / valorBruto;   //calcular porcentagem da taxa de antecipação
//            extratoDiarioItemVO.setTaxaCalculadaAntecipacao(taxaAntecipacao);

            taxa = ((valorComissao - extratoDiarioItemVO.getValorDescontadoAntecipacao()) * 100) / valorBruto;
        } else {
            taxa = (valorComissao * 100) / valorBruto;
        }

        extratoDiarioItemVO.setTaxa(taxa);

//        if (transaction.getStoneConciliationEvents().getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS)) {
//            if (!UteisValidacao.emptyString(stoneConciliationInstallment.getAdvancedReceivableOriginalPaymentDate())) {
//                extratoDiarioItemVO.setDataPgtoOriginalAntesDaAntecipacao(sdfYMD.parse(stoneConciliationInstallment.getAdvancedReceivableOriginalPaymentDate()));
//                extratoDiarioItemVO.setAntecipacao(true);
//            }
//            extratoDiarioItemVO.setDataPrevistaPagamento(sdfYMD.parse(stoneConciliationInstallment.getPaymentDate()));
//        } else {
//            if (UteisValidacao.emptyString(stoneConciliationInstallment.getPrevisionPaymentDate())) {
//                dataPrevistaPagamento = stoneConciliationInstallment.getOriginalPaymentDate();
//            } else {
//                dataPrevistaPagamento = stoneConciliationInstallment.getPrevisionPaymentDate();
//            }
//            try {
//                extratoDiarioItemVO.setDataPrevistaPagamento(sdfYMD.parse(dataPrevistaPagamento));
//            } catch (Exception ex) {
//            }
//        }
//
//        if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
//            if (transaction.getAccountType() == StoneAccountTypeConciliationEnum.Credit.getId() || transaction.getAccountType() == StoneAccountTypeConciliationEnum.PrepaidCredit.getId()) {
//                extratoDiarioItemVO.setCredito(true);
//            }
//        } else {
//            getFacade().getExtratoDiarioItem().verificarCreditoDebitoExtrato(extratoDiarioItemVO);
//        }
//
        if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
            getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);
        } else if (extratoDiarioItemVO.getCredito()) {
            getFacade().getExtratoDiarioItem().preencherCartaoCredito(extratoDiarioItemVO, false);
        } else if (!extratoDiarioItemVO.getCredito()) {
            getFacade().getExtratoDiarioItem().preencherCartaoDebito(extratoDiarioItemVO);
        }

        extratoDiarioItens.add(extratoDiarioItemVO);

//
//        try {
//            if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
//                //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
//                extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
//            }
//        } catch (Exception ex) {
//        }
//
//        //para antecipação de recebíveis, mudar a data do recebimento no zw também automaticamente;
//        if (extratoDiarioItemVO.isAntecipacao()) {
//            Uteis.logarDebug("Encontrei um pagamento que foi antecipado, vou mudar a data de pagamento dentro do ZW automaticamente..." +
//                    (!UteisValidacao.emptyString(extratoDiarioItemVO.getAutorizacao()) ? " | AUT: " + extratoDiarioItemVO.getAutorizacao() : "") +
//                    (!UteisValidacao.emptyString(extratoDiarioItemVO.getNsu()) ? " | NSU: " + extratoDiarioItemVO.getNsu() : ""));
//            try {
//                getFacade().getExtratoDiarioItem().alterarDatasPagamento(extratoDiarioItemVO);
//            } catch (Exception ex) {
//                ex.printStackTrace();
//                try {
//                    Uteis.logarDebug("Não foi possível mudar a data de pagamento dentro do ZW automaticamente... | " + transaction);
//                } catch (Exception ignored) {
//                }
//            }
//
//        } else {
//            taxa = (valorComissao * 100) / valorBruto;
//        }
//
//        ExtratoDiarioItemVO extratoDiarioItemVOEstornoChargeBack = (ExtratoDiarioItemVO) extratoDiarioItemVO.getClone(true);
//        extratoDiarioItens.add(extratoDiarioItemVO);
//
//        //**PROCESSAR CHARGEBACK**
//        if (stoneConciliationInstallment.getChargeBacks() != null && stoneConciliationInstallment.getChargeBacks().getChargeback() != null) {
//            for (StoneConciliationChargeBack chargeBack : stoneConciliationInstallment.getChargeBacks().getChargeback()) {
//                if (extratoDiarioItemVO.getDataPrevistaPagamento() == null) {
//                    extratoDiarioItemVO.setDataPrevistaPagamento(sdfYMD.parse(chargeBack.getChargeDate()));
//                }
//                if ((Calendario.maiorOuIgual(sdfYMD.parse(chargeBack.getDate()), extratoDiarioItemVO.getDataPrevistaPagamento()) && !financialTransaction) ||
//                        (Calendario.menorOuIgual(sdfYMD.parse(chargeBack.getDate()), extratoDiarioItemVO.getDataPrevistaPagamento()) && financialTransaction)) {
//                    ExtratoDiarioItemVO extratoDiarioItemVO1 = (ExtratoDiarioItemVO) extratoDiarioItemVO.getClone(true);
//                    extratoDiarioItemVO1.setTipoConciliacao(TipoConciliacaoEnum.CHARGEBACK.getCodigo());
//                    extratoDiarioItemVO1.setValorBruto(chargeBack.getAmount() > 0 ? chargeBack.getAmount() * -1 : chargeBack.getAmount());
//                    extratoDiarioItemVO1.setValorLiquido(extratoDiarioItemVO1.getValorBruto());
//                    extratoDiarioItemVO1.setDataLancamento(sdfYMD.parse(chargeBack.getDate()));
//                    extratoDiarioItemVO1.setDataPrevistaPagamento(sdfYMD.parse(chargeBack.getChargeDate()));
//                    extratoDiarioItemVO1.setEstorno(true);
//                    extratoDiarioItemVO1.setIdExterno(chargeBack.getId());
//                    extratoDiarioItens.add(extratoDiarioItemVO1);
//                }
//            }
//        }
//        if (transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.CHARGEBACK) {
//            extratoDiarioItens.remove(extratoDiarioItemVO);
//        }
//
//        //**PROCESSAR ESTORNO DE CHARGEBACK**
//        if (
//                transaction.getStoneConciliationEvents().getTipoConciliacaoArray().contains(TipoConciliacaoEnum.ESTORNO_CHARGEBACK)
//                        && extratoDiarioItemVOEstornoChargeBack != null
//                        && extratoDiarioItemVOEstornoChargeBack.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())
//        ) {
//            montarEstratoDiarioItemEstornoChargebackPagamentos(extratoDiarioItens, stoneConciliationInstallment, extratoDiarioItemVOEstornoChargeBack);
//        }
//
//        try {
//            //**PROCESSAR CANCELAMENTOS e TAXAS DE CANCELAMENTOS**
//            if (!UteisValidacao.emptyList(transaction.getStoneConciliationCancellations())) {
//                extratoDiarioItens.addAll(processarTransactionCancellations(transaction, convenio, stoneCode, empresa, arquivoProcessamentoTemp, convenioPossuiVariasEmpresasConfiguradas, dataArquivo));
//            }
//        } catch (Exception ignore) {
//        }

        return extratoDiarioItens;
    }

    private static List<ExtratoDiarioItemVO> processarItemVenda(JSONObject item, ConvenioCobrancaVO convenioVO, EmpresaVO empresaVO,
                                                                String arquivoProcessamentoTemp, Date dataArquivo,
                                                                TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {

        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        Map<String, String> mapa = new HashMap();
        mapa.put("item", item.toString());

        Date dataLancamento = null;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
            dataLancamento = format.parse(item.getString("date"));
        } catch (Exception ex) {
//            ex.printStackTrace();
        }
        try {
            if (dataLancamento == null) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SS'Z'", Locale.US);
                format.setTimeZone(TimeZone.getTimeZone("UTC"));
                dataLancamento = format.parse(item.getString("date"));
            }
        } catch (Exception ex) {
//            ex.printStackTrace();
        }
        try {
            if (dataLancamento == null) {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US);
                format.setTimeZone(TimeZone.getTimeZone("UTC"));
                dataLancamento = format.parse(item.getString("date"));
            }
        } catch (Exception ex) {
//            ex.printStackTrace();
        }

        if (dataLancamento == null || !Calendario.igual(dataLancamento, dataArquivo)) {
            dataLancamento = dataArquivo;
        }

        String nsu = item.getString("id");
        String status = item.getString("status");
        String orderKey = item.getString("orderKey");
        Integer codigoTransacao = 0;
        try {
            codigoTransacao = Integer.parseInt(orderKey.replace("TRA", ""));
        } catch (Exception ignored) {
        }

        if (!status.equalsIgnoreCase("expired") &&
                !status.equalsIgnoreCase("paid") &&
                !status.equalsIgnoreCase("created")) {
            Uteis.logarDebug("########################## Status | " + status + " ##########################");
        }

        if (status.equalsIgnoreCase("expired") || status.equalsIgnoreCase("created")) {
            return extratoDiarioItens;
        }


        String ro = "";
        String autorizacao = "";
        try {
            JSONArray array = item.getJSONArray("installments");
            for (int i = 0; i < array.length(); i++) {
                JSONObject json1 = array.getJSONObject(i);
                if (!UteisValidacao.emptyString(json1.optString("acquirerAuthorization"))) {
                    autorizacao = json1.optString("acquirerAuthorization");
                }
                if (!UteisValidacao.emptyString(json1.optString("acquirerUniqueNumber"))) {
                    ro = json1.optString("acquirerUniqueNumber");
                }
            }
        } catch (Exception ignored) {
        }

        Transacao transacaoDAO = new Transacao(getFacade().getCliente().getCon());
        TransacaoVO transacaoVO = null;
        if (!UteisValidacao.emptyNumber(codigoTransacao)) {
            transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigoTransacao, Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
        } else {
            transacaoVO = transacaoDAO.consultarPorCodigoExternoETipo(nsu, convenioVO.getTipo().getTipoTransacao());
        }
        transacaoDAO = null;

        ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(mapa);
        extratoDiarioItemVO.setDataLancamento(dataLancamento);
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        extratoDiarioItemVO.setTipoConciliacao(tipoConciliacaoEnum.getCodigo());
        extratoDiarioItemVO.setNsu(nsu);
        extratoDiarioItemVO.setAutorizacao(UteisValidacao.emptyString(autorizacao) ? (transacaoVO != null ? transacaoVO.getAutorizacao() : "") : autorizacao);
        extratoDiarioItemVO.setEstabelecimento(convenioVO.getCodigoAutenticacao01());
        extratoDiarioItemVO.setNrCartao(transacaoVO != null ? transacaoVO.getCartaoMascarado() : "");
        extratoDiarioItemVO.setNrTotalParcelas(item.getInt("qtdInstallments"));
        extratoDiarioItemVO.setApresentarExtrato(true);
        extratoDiarioItemVO.setConvenio(convenioVO);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresaVO);
        extratoDiarioItemVO.setEstorno(false);
        extratoDiarioItemVO.setRo(ro);

        boolean credito = item.optString("receivingOptionType").toUpperCase().contains("CREDITCARD");
        if (!credito) {
            System.out.println("receivingOptionType | " + item.optString("receivingOptionType"));
        }
        extratoDiarioItemVO.setCredito(credito);

        Double taxaValor = 0.0;
        try {
            JSONArray array = item.getJSONArray("installments");
            for (int i = 0; i < array.length(); i++) {
                try {
                    JSONObject json1 = array.getJSONObject(i);
                    JSONObject fee = json1.getJSONObject("fee");
                    if (!UteisValidacao.emptyNumber(fee.optDouble("value"))) {
                        taxaValor = fee.getDouble("value");
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ignored) {
        }

        double valorBruto = item.getDouble("amount");
        double valorLiquido = (valorBruto - taxaValor);

        double taxa = 0.0;
        try {
            taxa = ((100 * taxaValor) / valorBruto);
        } catch (Exception ignored) {
        }

        extratoDiarioItemVO.setTaxa(taxa);
        extratoDiarioItemVO.setValorBruto(valorBruto);
        extratoDiarioItemVO.setValorLiquido(valorLiquido);
        extratoDiarioItemVO.setValorComissao(valorBruto - valorLiquido);

        getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);

        try {
            if (!UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
            }
        } catch (Exception ignored) {
        }

        extratoDiarioItens.add(extratoDiarioItemVO);
        return extratoDiarioItens;
    }

    private static List<ExtratoDiarioItemVO> processarItemPagamento(JSONObject item, ConvenioCobrancaVO convenioVO, EmpresaVO empresaVO,
                                                                    String arquivoProcessamentoTemp, Date dataArquivo,
                                                                    TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {
        String accountEntryType = item.getString("accountEntryType");
        if (accountEntryType.equalsIgnoreCase("sale")) {
            return processarItemRecebimento(item, convenioVO, empresaVO, arquivoProcessamentoTemp, dataArquivo, tipoConciliacaoEnum);
        } else if (accountEntryType.equalsIgnoreCase("chargeback") ||
                accountEntryType.equalsIgnoreCase("canceled")) {
            return processarItemChargebackCancelamento(item, convenioVO, empresaVO, arquivoProcessamentoTemp, dataArquivo);
        } else {
            return new ArrayList<>();
        }
    }

    private static List<ExtratoDiarioItemVO> processarItemRecebimento(JSONObject item, ConvenioCobrancaVO convenioVO, EmpresaVO empresaVO,
                                                                      String arquivoProcessamentoTemp, Date dataArquivo,
                                                                      TipoConciliacaoEnum tipoConciliacaoEnum) throws Exception {

        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        Map<String, String> mapa = new HashMap();
        mapa.put("item", item.toString());
        String nsu = item.getString("paymentID");

        Transacao transacaoDAO = new Transacao(getFacade().getCliente().getCon());
        TransacaoVO transacaoVO = transacaoDAO.consultarPorCodigoExternoETipo(nsu, convenioVO.getTipo().getTipoTransacao());
        transacaoDAO = null;

        Date dataPrevistaPagamento = dataArquivo;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
            dataPrevistaPagamento = format.parse(item.getString("expectedDate"));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(mapa);
        extratoDiarioItemVO.setDataLancamento(dataArquivo);
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        extratoDiarioItemVO.setDataPrevistaPagamento(dataPrevistaPagamento);
        extratoDiarioItemVO.setTipoConciliacao(tipoConciliacaoEnum.getCodigo());
        extratoDiarioItemVO.setNsu(nsu);
        extratoDiarioItemVO.setAutorizacao(transacaoVO != null ? transacaoVO.getAutorizacao() : "");
        extratoDiarioItemVO.setEstabelecimento(convenioVO.getCodigoAutenticacao01());
        extratoDiarioItemVO.setNrCartao(transacaoVO != null ? transacaoVO.getCartaoMascarado() : "");
        extratoDiarioItemVO.setNrParcela(item.getInt("installment"));
        extratoDiarioItemVO.setNrTotalParcelas(item.getInt("installments"));
        extratoDiarioItemVO.setApresentarExtrato(true);
        extratoDiarioItemVO.setConvenio(convenioVO);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresaVO);
        extratoDiarioItemVO.setEstorno(false);
        extratoDiarioItemVO.setRo("");

        boolean credito = item.optString("receivingOptionType").toUpperCase().contains("CREDITCARD");
        if (!credito) {
            System.out.println("receivingOptionType | " + item.optString("receivingOptionType"));
        }
        extratoDiarioItemVO.setCredito(credito);

        double valorBruto = item.getDouble("fullAmount");
        double valorLiquido = item.getDouble("amount");

        double taxa = 0.0;
        try {
            taxa = (((100 * valorLiquido) / valorBruto) - 100);
        } catch (Exception ignored) {
        }

        extratoDiarioItemVO.setTaxa(taxa);
        extratoDiarioItemVO.setValorBruto(valorBruto);
        extratoDiarioItemVO.setValorLiquido(valorLiquido);
        extratoDiarioItemVO.setValorComissao(valorBruto - valorLiquido);

        if (extratoDiarioItemVO.getCredito()) {
            getFacade().getExtratoDiarioItem().preencherCartaoCredito(extratoDiarioItemVO, false);
        } else {
            getFacade().getExtratoDiarioItem().preencherCartaoDebito(extratoDiarioItemVO);
        }

        try {
            if (!UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
            }
        } catch (Exception ignored) {
        }

        extratoDiarioItens.add(extratoDiarioItemVO);
        return extratoDiarioItens;
    }

    private static List<ExtratoDiarioItemVO> processarItemChargebackCancelamento(JSONObject item, ConvenioCobrancaVO convenioVO, EmpresaVO empresaVO,
                                                                                 String arquivoProcessamentoTemp, Date dataArquivo) throws Exception {

        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        Map<String, String> mapa = new HashMap();
        mapa.put("item", item.toString());
        String nsu = item.getString("paymentID");
        TipoConciliacaoEnum tipoConciliacaoEnum = null;

        String accountEntryType = item.getString("accountEntryType");
        if (accountEntryType.equalsIgnoreCase("chargeback")) {
            tipoConciliacaoEnum = TipoConciliacaoEnum.CHARGEBACK;
        } else if (accountEntryType.equalsIgnoreCase("canceled")) {
            tipoConciliacaoEnum = TipoConciliacaoEnum.CANCELAMENTO;
        } else {
            return new ArrayList<>();
        }

        Transacao transacaoDAO = new Transacao(getFacade().getCliente().getCon());
        TransacaoVO transacaoVO = transacaoDAO.consultarPorCodigoExternoETipo(nsu, convenioVO.getTipo().getTipoTransacao());
        transacaoDAO = null;

        Date dataPrevistaPagamento = dataArquivo;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
            dataPrevistaPagamento = format.parse(item.getString("expectedDate"));
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();
        extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
        extratoDiarioItemVO.setProps(mapa);
        extratoDiarioItemVO.setDataLancamento(dataArquivo);
        extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
        extratoDiarioItemVO.setDataPrevistaPagamento(dataPrevistaPagamento);
        extratoDiarioItemVO.setTipoConciliacao(tipoConciliacaoEnum.getCodigo());
        extratoDiarioItemVO.setNsu(nsu);
        extratoDiarioItemVO.setAutorizacao(transacaoVO != null ? transacaoVO.getAutorizacao() : "");
        extratoDiarioItemVO.setEstabelecimento(convenioVO.getCodigoAutenticacao01());
        extratoDiarioItemVO.setNrCartao(transacaoVO != null ? transacaoVO.getCartaoMascarado() : "");
        extratoDiarioItemVO.setNrParcela(item.getInt("installment"));
        extratoDiarioItemVO.setNrTotalParcelas(item.getInt("installments"));
        extratoDiarioItemVO.setObservacao(item.optString("description"));
        extratoDiarioItemVO.setApresentarExtrato(true);
        extratoDiarioItemVO.setConvenio(convenioVO);
        extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
        extratoDiarioItemVO.setEmpresa(empresaVO);
        extratoDiarioItemVO.setEstorno(false);
        extratoDiarioItemVO.setRo("");

        boolean credito = item.optString("receivingOptionType").toUpperCase().contains("CREDITCARD");
        if (!credito) {
            System.out.println("receivingOptionType | " + item.optString("receivingOptionType"));
        }
        extratoDiarioItemVO.setCredito(credito);

        double valorBruto = item.getDouble("fullAmount");
        double valorLiquido = item.getDouble("amount");

        double taxa = 0.0;
        try {
            taxa = (((100 * valorLiquido) / valorBruto) - 100);
        } catch (Exception ignored) {
        }

        extratoDiarioItemVO.setTaxa(taxa);
        extratoDiarioItemVO.setValorBruto(valorBruto);
        extratoDiarioItemVO.setValorLiquido(valorLiquido);
        extratoDiarioItemVO.setValorComissao(valorBruto - valorLiquido);

        if (extratoDiarioItemVO.getCredito()) {
            getFacade().getExtratoDiarioItem().preencherCartaoCredito(extratoDiarioItemVO, false);
        } else {
            getFacade().getExtratoDiarioItem().preencherCartaoDebito(extratoDiarioItemVO);
        }

        try {
            if (!UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
            }
        } catch (Exception ignored) {
        }

        extratoDiarioItens.add(extratoDiarioItemVO);
        return extratoDiarioItens;
    }
}

package servicos.impl.pagolivre;

import org.json.JSONObject;

public class BankAccountPagoLivreDto {

    private String bankNumber;
    private String agencyNumber;
    private String agencyDigit;
    private String accountNumber;
    private String accountDigit;
    private String type;
    private String cnpj;

    public BankAccountPagoLivreDto() {
    }

    public BankAccountPagoLivreDto(JSONObject json) {
        this.bankNumber = json.optString("bankNumber");
        this.agencyNumber = json.optString("agencyNumber");
        this.agencyDigit = json.optString("agencyDigit");
        this.accountNumber = json.optString("accountNumber");
        this.accountDigit = json.optString("accountDigit");
        this.type = json.optString("type");
        this.cnpj = json.optString("cnpj");
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getAgencyNumber() {
        return agencyNumber;
    }

    public void setAgencyNumber(String agencyNumber) {
        this.agencyNumber = agencyNumber;
    }

    public String getAgencyDigit() {
        return agencyDigit;
    }

    public void setAgencyDigit(String agencyDigit) {
        this.agencyDigit = agencyDigit;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getAccountDigit() {
        return accountDigit;
    }

    public void setAccountDigit(String accountDigit) {
        this.accountDigit = accountDigit;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }
}

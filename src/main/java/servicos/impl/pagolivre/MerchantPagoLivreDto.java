package servicos.impl.pagolivre;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class MerchantPagoLivreDto {

    private String email;
    private String cnpj;
    private String companyName;
    private String phoneNumber;
    private BankAccountPagoLivreDto bankAccount;
    private List<PortalUsersPagoLivreDto> portalUsers = new ArrayList<PortalUsersPagoLivreDto>();
    private String merchantId;

    //Nome usual do merchant
    private String name;

    public String getEmail() {
        if (email == null) {
            return "";
        } else {
            return email;
        }
    }

    public MerchantPagoLivreDto() {
    }

    public MerchantPagoLivreDto(JSONObject json) {
        this.merchantId = json.optString("merchantId");
        this.email = json.optString("email");
        this.companyName = json.optString("companyName");
        this.cnpj = json.optString("cnpj");
        this.phoneNumber = json.optString("phoneNumber");
        this.name = json.optString("name");
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public BankAccountPagoLivreDto getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(BankAccountPagoLivreDto bankAccount) {
        this.bankAccount = bankAccount;
    }

    public List<PortalUsersPagoLivreDto> getPortalUsers() {
        return portalUsers;
    }

    public void setPortalUsers(List<PortalUsersPagoLivreDto> portalUsers) {
        this.portalUsers = portalUsers;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

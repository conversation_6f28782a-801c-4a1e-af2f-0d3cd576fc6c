package servicos.impl.cliente;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.integracao.importacao.ClienteJSON;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoReenvioMovParcelaEmpresaVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.TipoColaboradorVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.webservice.IntegracaoImportacao;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.financeiro.RemessaItem;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.interfaces.cliente.VendaRapidaRecorrenteService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class VendaRapidaRecorrenteServiceImpl extends SuperEntidade implements VendaRapidaRecorrenteService {
    ConfiguracaoSistemaVO configuracaoSistemaVO = new ConfiguracaoSistemaVO();


    public VendaRapidaRecorrenteServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public VendaRapidaRecorrenteServiceImpl() throws Exception {
        super();
    }


    public void alterar(TelefoneVO telefoneCelularVO,
                        TelefoneVO telefoneComercialVO,
                        TelefoneVO telefoneResidencialVO,
                        EnderecoVO enderecoResidencialVO,
                        ClienteVO clienteVO,
                        PessoaVO pessoaVO,
                        EmailVO emailVO,
                        UsuarioVO usuarioVO,
                        EmpresaVO empresaVO,
                        ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception {
        pessoaVO.setTelefoneVOs(new ArrayList<>());
        if (!UteisValidacao.emptyString(telefoneCelularVO.getNumero())) {
            telefoneCelularVO.setTipoTelefone("CE");
            pessoaVO.getTelefoneVOs().add(telefoneCelularVO);
        }
        if (!UteisValidacao.emptyString(telefoneComercialVO.getNumero())) {
            telefoneComercialVO.setTipoTelefone("CO");
            pessoaVO.getTelefoneVOs().add(telefoneComercialVO);
        }
        if (!UteisValidacao.emptyString(telefoneResidencialVO.getNumero())) {
            telefoneResidencialVO.setTipoTelefone("RE");
            pessoaVO.getTelefoneVOs().add(telefoneResidencialVO);
        }
        pessoaVO.setEnderecoVOs(new ArrayList<>());
        if (!UteisValidacao.emptyString(enderecoResidencialVO.getEndereco())) {
            pessoaVO.getEnderecoVOs().add(enderecoResidencialVO);
        }
        pessoaVO.setEmailVOs(new ArrayList<>());
        if (!UteisValidacao.emptyString(emailVO.getEmail())) {
            pessoaVO.getEmailVOs().add(emailVO);
        }
        getFacade().getPessoa().alterar(pessoaVO);
        getFacade().getCliente().alterar(clienteVO, configuracaoSistemaVO, false, false);
    }

    public Integer addAlunoInadimplente(JSONObject json, Integer empresa) throws Exception {
        IntegracaoImportacao integracao = new IntegracaoImportacao(con);
        ClienteJSON clienteJSON = new ClienteJSON();
        clienteJSON.montarClienteInadimplente(json);
        JSONObject jsonObject = integracao.persistirClienteJSON(null, clienteJSON, empresa, true);
        return jsonObject.getInt("codigoRegistroZW");

    }

    public void gerarParcelaInadimplente(ClienteVO cliente, Double valor, UsuarioVO usuario,
                                         String idExternoTitulo,
                                         boolean adicionarRemessa,
                                         String idExternoFilial,
                                         String cpf) throws Exception {
        ProdutoVO taxa = getFacade().getProduto().criarOuConsultarExisteProdutoPorDescricao("ACERTO DE TITULO PENDENTE", TipoProduto.SERVICO.getCodigo(), 0.0);
        if (taxa != null) {
            taxa.setValorFinal(valor);
            VendaAvulsaVO vendaAvulsa = new VendaAvulsaVO();
            vendaAvulsa.setDescricaoAdicional(taxa.getDescricao() + " - " + idExternoTitulo + (adicionarRemessa ? " - BORDERÔ" : ""));
            vendaAvulsa.setCliente(cliente);
            vendaAvulsa.setNomeComprador(cliente.getPessoa().getNome());
            vendaAvulsa.setDataRegistro(Calendario.hoje());
            vendaAvulsa.setEmpresa(cliente.getEmpresa());
            vendaAvulsa.setResponsavel(usuario);
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setProduto(taxa);
            item.setQuantidade(1);
            item.setValorParcial(valor);
            vendaAvulsa.setValorTotal(valor);
            vendaAvulsa.setTipoComprador("CI");
            vendaAvulsa.getItemVendaAvulsaVOs().add(item);
            Integer idvenda = getFacade().getVendaAvulsa().incluir(vendaAvulsa, false,
                    Calendario.hoje(), null, null, null);

            executarConsulta("update movproduto set idtitulo =  '" + idExternoTitulo + "' where vendaavulsa = " + idvenda, con);
            executarConsulta("update movparcela set recno =  '" + idExternoTitulo + "' where vendaavulsa = " + idvenda, con);

            if (!adicionarRemessa && !UteisValidacao.emptyString(idExternoTitulo)) {
                Map<String, String> headers = new HashMap<String, String>();
                headers.put("Content-Type", "application/json");
                JSONObject body = new JSONObject();
                body.put("filial", idExternoFilial);
                body.put("titulo", idExternoTitulo);

                String retorno = ExecuteRequestHttpService.executeHttpRequest(PropsService.getPropertyValue(PropsService.urlValidarInadimplencia) +
                                "?cpf=" + Uteis.removerMascara(cpf.trim()) +
                                "&token=" + PropsService.getPropertyValue(PropsService.tokenValidarInadimplencia),
                        body.toString(),
                        headers,
                        ExecuteRequestHttpService.METODO_POST, "UTF-8");
                System.out.println(retorno);
            } else if (adicionarRemessa) {
                incluirParcelaRemessa(vendaAvulsa, idExternoFilial);
            }
        }
    }

    private RemessaItemVO gerarRemessaItem(VendaAvulsaVO vendaAvulsaVO, RemessaVO remessaVO) {
        RemessaItemVO remessaItem = new RemessaItemVO();
        remessaItem.setRemessa(remessaVO);
        remessaItem.setMovParcela(vendaAvulsaVO.getMovParcelaVOs().get(0));
        remessaItem.setProps(new HashMap<String, String>());
        remessaItem.setPessoa(vendaAvulsaVO.getCliente().getPessoa());
        remessaItem.setNrTentativaParcela(1);
        remessaItem.setTipo(remessaVO.getTipo());
        remessaItem.setValorItemRemessa(vendaAvulsaVO.getValorTotal());
        remessaItem.setValorParcela(vendaAvulsaVO.getValorTotal());
        remessaItem.setContabilizadaPacto(true);
        return remessaItem;
    }

    private void incluirParcelaRemessa(VendaAvulsaVO vendaAvulsaVO, String idExterno) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorIdExterno(idExterno, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        if (UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            empresaVO = vendaAvulsaVO.getEmpresa();
        }
        RemessaVO remessa;
        Remessa remessaDAO = new Remessa(con);
        List<RemessaVO> remessas = remessaDAO.consultarPorIdentificador("FAKE-" + idExterno);

        if (UteisValidacao.emptyList(remessas)) {
            remessa = new RemessaVO();
            String fakeName = "FAKE-" + idExterno + "-" + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMM");

            remessa.setTipo(TipoRemessaEnum.EDI_CIELO);
            remessa.setDataRegistro(Calendario.hoje());
            remessa.setDataInicio(Calendario.hoje());
            remessa.getUsuario().setCodigo(1);
            remessa.setCancelamento(false);
            remessa.setArquivoUnico(true);
            remessa.setIdentificador(fakeName);
            remessa.setEmpresa(empresaVO.getCodigo());
            remessa.getConvenioCobranca().setCodigo(1);
            remessa.setSituacaoRemessa(SituacaoRemessaEnum.REMESSA_ENVIADA);
            remessa.setProps(new HashMap<String, String>());
            remessa.setNomeArquivo(fakeName);
            remessa.getListaItens().add(gerarRemessaItem(vendaAvulsaVO, remessa));
            remessaDAO.incluir(remessa);
        } else {
            remessa = remessas.get(0);
            RemessaItemVO remessaItemVO = gerarRemessaItem(vendaAvulsaVO, remessa);
            RemessaItem remessaItemDAO = new RemessaItem(con);
            remessaItemDAO.incluir(remessaItemVO);
        }
        atualizarRemessa(remessa);
    }

    private void atualizarRemessa(RemessaVO remessa) throws SQLException {
        int quantidadeRegistrosRemessa = 0;
        double valorRegistrosRemessa = 0.0;

        String valores = "SELECT COUNT(1)+2 AS qtd, SUM(valormovparcela) AS valor FROM remessaitem WHERE remessa = " + remessa.getCodigo();
        PreparedStatement ps = con.prepareStatement(valores);
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            quantidadeRegistrosRemessa = rs.getInt("qtd");
            valorRegistrosRemessa = rs.getDouble("valor");
        }
        ps.close();
        rs.close();

        String tipoRegistro = "TipoRegistro=99";
        String quantidadeRegistros = "QuantidadeRegistros=" + StringUtilities.formatarCampoForcandoZerosAEsquerda(String.valueOf(quantidadeRegistrosRemessa), 7);
        String valorTotalBruto = "ValorTotalBruto=" + StringUtilities.formatarCampoMonetario(valorRegistrosRemessa, 15);
        String valorTotalAceito = "ValorTotalLiquido=" + StringUtilities.formatarCampoMonetario(0, 15);
        String valorTotalLiquido = "ValorTotalLiquido=" + StringUtilities.formatarCampoMonetario(0, 15);

        String sqlAtualizarRemessa = "UPDATE remessa SET trailer = ? WHERE codigo = ?";
        ps = con.prepareStatement(sqlAtualizarRemessa);
        String trailer = "[" +
                tipoRegistro + ", " +
                quantidadeRegistros + ", " +
                valorTotalBruto + ", " +
                valorTotalAceito + ", " +
                valorTotalLiquido +
                "]";
        ps.setString(1, trailer);
        ps.setInt(2, remessa.getCodigo());
        ps.execute();
        ps.close();
    }


    public void gravar(TelefoneVO telefoneCelularVO,
                       TelefoneVO telefoneComercialVO,
                       TelefoneVO telefoneResidencialVO,
                       EnderecoVO enderecoResidencialVO,
                       EnderecoVO enderecoComercialVO,
                       ClienteVO clienteVO,
                       PessoaVO pessoaVO,
                       EmailVO emailVO,
                       UsuarioVO usuarioVO,
                       EmpresaVO empresaVO,
                       ConfiguracaoSistemaVO configuracaoSistemaVO,
                       boolean pessoaEstrangeira,
                       boolean apenasValidar) throws Exception {

        clienteVO.setEmpresa(empresaVO);
        if (pessoaVO.getNomeFoto() == null
                || pessoaVO.getNomeFoto().equals("")) {
            pessoaVO.setNomeFoto("fotoPadrao.jpg");
        }
        clienteVO.setPessoa(pessoaVO);
        if (apenasValidar) {
            validarDados(clienteVO,
                    enderecoResidencialVO,
                    enderecoComercialVO,
                    telefoneCelularVO,
                    telefoneResidencialVO,
                    emailVO,
                    configuracaoSistemaVO,
                    pessoaEstrangeira);

            return;
        }

        if (UteisValidacao.emptyString(pessoaVO.getSexo())) {
            pessoaVO.setSexo("M");
        }
        for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
            TelefoneVO.validarTelefone(telefoneVO.getNumero(), telefoneVO.getTipoTelefone_Apresentar(), configuracaoSistemaVO.isUsarSistemaInternacional());
        }
        while (pessoaVO.getNome().contains("  ")) {
            pessoaVO.setNome(pessoaVO.getNome().replace("  ", " "));
        }
        clienteVO.setPessoa(pessoaVO);
        carregarTelefoneEnderecoEmail(telefoneCelularVO,
                telefoneComercialVO,
                telefoneResidencialVO,
                enderecoResidencialVO,
                enderecoComercialVO,
                pessoaVO,
                emailVO,
                configuracaoSistemaVO);
        clienteVO.setPessoa(pessoaVO);

        getFacade().getCliente().gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO);
        getFacade().getCliente().incluir(clienteVO, configuracaoSistemaVO, false, false);
        // LOG - INICIO
        try {
            clienteVO.setObjetoVOAntesAlteracao(new ClienteVO());
            clienteVO.setNovoObj(true);
            registrarLogEndereco(clienteVO, usuarioVO);
            registrarLogEmail(clienteVO, usuarioVO);
            SuperControle.registrarLogObjetoVO(clienteVO, clienteVO.getCodigo().intValue(), "CLIENTE", clienteVO.getPessoa().getCodigo());

        } catch (Exception e) {
            SuperControle.registrarLogErroObjetoVO("CLIENTE", clienteVO.getPessoa().getCodigo(),
                    "ERRO AO GERAR LOG DE INCLUSÃO DE CLIENTE",
                    usuarioVO.getNome(), usuarioVO.getUserOamd());
            e.printStackTrace();

        }
        try {
            if (empresaVO.isNotificarWebhook()) {
                getFacade().getZWFacade().notificarCadastro(clienteVO, usuarioVO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ClienteVO clieAtualizar = getFacade().getCliente().consultarPorCodigoPessoa(clienteVO.getPessoa().getCodigo(),
                Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
        clieAtualizar.setDadosSinteticoPreparados(true);
        SituacaoClienteSinteticoDWVO clienteSinteticoDWVO = getFacade().getZWFacade().atualizarSintetico(clieAtualizar,
                Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        clienteVO.setSituacaoClienteSinteticoVO(clienteSinteticoDWVO);
        JSFUtilities.storeOnSession(ClienteVO.ULTIMO_CLIENTE_EDITADO, clienteVO.getCodigo().toString());
        clienteVO.registrarObjetoVOAntesDaAlteracao();
        clienteVO.getPessoa().registrarObjetoVOAntesDaAlteracao();
    }

    public void carregarTelefoneEnderecoEmail(TelefoneVO telefoneCelularVO,
                                              TelefoneVO telefoneComercialVO,
                                              TelefoneVO telefoneResidencialVO,
                                              EnderecoVO enderecoResidencialVO,
                                              EnderecoVO enderecoComercialVO,
                                              PessoaVO pessoaVO,
                                              EmailVO emailVO,
                                              ConfiguracaoSistemaVO configuracaoSistemaVO) throws Exception {
        telefoneCelularVO.setTipoTelefone("CE");
        telefoneComercialVO.setTipoTelefone("CO");
        telefoneResidencialVO.setTipoTelefone("RE");
        enderecoResidencialVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
        enderecoComercialVO.setTipoEndereco(TipoEnderecoEnum.COMERCIAL.getCodigo());

        if (telefoneCelularVO.getNumero().trim().equals("")) {
            pessoaVO.excluirObjTelefoneVOs(telefoneCelularVO.getNumero());
        } else {
            telefoneCelularVO.setValidarDados(false);
            pessoaVO.adicionarObjTelefoneVOsSimples(telefoneCelularVO);
        }
        if (telefoneComercialVO.getNumero().trim().equals("")) {
            pessoaVO.excluirObjTelefoneVOs(telefoneComercialVO.getNumero());
        } else {
            telefoneComercialVO.setValidarDados(false);
            pessoaVO.adicionarObjTelefoneVOsSimples(telefoneComercialVO);
        }
        if (telefoneResidencialVO.getNumero().trim().equals("")) {
            pessoaVO.excluirObjTelefoneVOs(telefoneResidencialVO.getNumero());
        } else {
            telefoneResidencialVO.setValidarDados(false);
            pessoaVO.adicionarObjTelefoneVOsSimples(telefoneResidencialVO);
        }
        // impede que a classe de pessoa valide os dados
        telefoneComercialVO.setValidarDados(false);

        if (!enderecoResidencialVO.getEndereco().trim().equals("")) {

            enderecoResidencialVO.setEnderecoCorrespondencia(configuracaoSistemaVO.getDefaultEnderecoCorrespondecia());
            pessoaVO.adicionarObjEnderecoVOsSimples(enderecoResidencialVO);
        }

        if (!enderecoComercialVO.getEndereco().trim().equals("")) {
            enderecoComercialVO.setEnderecoCorrespondencia(configuracaoSistemaVO.getDefaultEnderecoCorrespondecia());
            pessoaVO.adicionarObjEnderecoVOsSimples(enderecoComercialVO);
        }

        emailVO.setValidarDados(true);

        if (emailVO.getEmail().trim().equals("")) {
            pessoaVO.excluirObjEmailOs(emailVO);
        } else {
            pessoaVO.adicionarObjEmailVOs(emailVO);
        }
    }

    public void registrarLogEndereco(ClienteVO clienteVO, UsuarioVO usuario) {
        //Gerando log Inserção
        try {
            for (EnderecoVO end : clienteVO.getPessoa().getEnderecoVOs()) {
                end.setObjetoVOAntesAlteracao(new EnderecoVO());
                List<LogVO> logs = end.gerarLogAlteracaoObjetoVO();
                for (LogVO log : logs) {
                    log.setNomeEntidade("CLIENTE");
                    log.setChavePrimaria(clienteVO.getCodigo().toString());
                    log.setResponsavelAlteracao(usuario.getNome());
                    log.setOperacao("INSERÇÃO");
                }
                end.registrarObjetoVOAntesDaAlteracao();
                SuperControle.registrarLogObjetoVO(logs, clienteVO.getCodigo());
            }
        } catch (Exception erro) {

        }
    }

    public void registrarLogEmail(ClienteVO clienteVO, UsuarioVO usuarioVO) {
        //Gerando log Inserção
        try {
            for (EmailVO end : clienteVO.getPessoa().getEmailVOs()) {
                end.setObjetoVOAntesAlteracao(new EmailVO());
                List<LogVO> logs = end.gerarLogAlteracaoObjetoVO();
                for (LogVO log : logs) {
                    log.setNomeEntidade("CLIENTE");
                    log.setChavePrimaria(clienteVO.getCodigo().toString());
                    log.setResponsavelAlteracao(usuarioVO.getNome());
                    log.setOperacao("INSERÇÃO");
                }
                end.registrarObjetoVOAntesDaAlteracao();
                SuperControle.registrarLogObjetoVO(logs, clienteVO.getCodigo());
            }
        } catch (Exception erro) {

        }
    }

    public void registrarLogVendaPlanoPersonal(ControleTaxaPersonalVO controleTaxaPersonalVO, List<MovParcelaVO> parcelas) {
        //Gerando log Inserção
        try {
            controleTaxaPersonalVO.setObjetoVOAntesAlteracao(new EmailVO());

            StringBuilder sbContrato = new StringBuilder();
            StringBuilder sbParcelas = new StringBuilder();

            Double valorTotalContrato = 0.0;

            for (MovParcelaVO movParcelaVO : parcelas) {
                sbParcelas.append("Parcela - ").append(movParcelaVO.getCodigo()).append("\n");
                sbParcelas.append("Valor - ").append(controleTaxaPersonalVO.getEmpresa().getMoeda()).append(Formatador.formatarValorMonetarioSemMoeda(movParcelaVO.getValorParcela()));
                valorTotalContrato += movParcelaVO.getValorParcela();
            }


            sbContrato.append("-------------------------------------").append("\n");
            sbContrato.append("Código do Contrato - ").append(controleTaxaPersonalVO.getCodigo()).append("\n");
            sbContrato.append(sbParcelas).append("\n");
            sbContrato.append("Valor do Contrato - ").append(controleTaxaPersonalVO.getEmpresa().getMoeda()).append(Formatador.formatarValorMonetarioSemMoeda(valorTotalContrato)).append("\n");
            sbContrato.append("Empresa - ").append(controleTaxaPersonalVO.getEmpresa().getNome()).append("\n");
            sbContrato.append("Plano - ").append(controleTaxaPersonalVO.getPlano().getDescricao()).append("\n");
            sbContrato.append("Cód. Plano - ").append(controleTaxaPersonalVO.getPlano().getCodigo()).append("\n");
            sbContrato.append("Responsável pelo Contrato - ").append(controleTaxaPersonalVO.getResponsavel().getNome()).append("\n");
            sbContrato.append("Data Lançamento - ").append(controleTaxaPersonalVO.getDataRegistro_apresentar()).append("\n");
            sbContrato.append("Data Início - ").append(controleTaxaPersonalVO.getDataInicioVigencia_apresentar()).append("\n");
            sbContrato.append("Data Final - ").append(controleTaxaPersonalVO.getDataFimVigencia_apresentar()).append("\n");
            sbContrato.append("--------------------------------------");

            LogVO logVO = new LogVO();
            logVO.setNomeEntidade("COLABORADOR");
            logVO.setNomeEntidadeDescricao("CONTRATO PERSONAL");
            logVO.setChavePrimaria(controleTaxaPersonalVO.getPersonal().getCodigo().toString());
            logVO.setResponsavelAlteracao(controleTaxaPersonalVO.getResponsavel().getNome());
            logVO.setPessoa(controleTaxaPersonalVO.getPersonal().getPessoa().getCodigo());
            logVO.setDataAlteracao(controleTaxaPersonalVO.getDataRegistro());
            logVO.setNomeCampo("TODOS");
            logVO.setValorCampoAnterior("");
            logVO.setValorCampoAlterado(sbContrato.toString());
            logVO.setOperacao("INSERÇÃO CONTRATO PERSONAL");

            controleTaxaPersonalVO.registrarObjetoVOAntesDaAlteracao();
            SuperControle.registrarLogObjetoVO(logVO, controleTaxaPersonalVO.getPersonal().getPessoa().getCodigo());

        } catch (Exception erro) {
            Uteis.logar(erro.getMessage(), VendaRapidaRecorrenteServiceImpl.class.getName());
        }
    }

    public void adicionarConsultor(ClienteVO cliente, ColaboradorVO consultor) throws Exception {
        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setColaborador(consultor);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
        cliente.getVinculoVOs().add(vinculoVO);

        getFacade().getVinculo().incluirVinculo(cliente.getCodigo(), cliente.getVinculoVOs(), "VENDA_RAPIDA");
    }

    public List<ConvenioCobrancaVO> convenios(Integer empresa, TipoAutorizacaoCobrancaEnum tipo,
                                              TipoConvenioCobrancaEnum tipoConvenio) throws Exception {
        List<TipoConvenioCobrancaEnum> tiposConvenio = new ArrayList<>();
        TipoConvenioCobrancaEnum[] tipos;
        if (tipo.equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

            //todos os convênios Online
            tiposConvenio = TipoConvenioCobrancaEnum.obterListaTipoCobranca(TipoCobrancaEnum.ONLINE);

            tipos = tiposConvenio.toArray(new TipoConvenioCobrancaEnum[]{tipoConvenio});

            List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaConfiguracao = getFacade().getConfiguracaoReenvioMovParcelaEmpresa().consultarConfiguracaoReenvioMovParcelaEmpresa(empresa, Uteis.NIVELMONTARDADOS_TODOS);
            if (!UteisValidacao.emptyList(listaConfiguracao)) {
                List<ConvenioCobrancaVO> retorno = new ArrayList<ConvenioCobrancaVO>();
                retorno.add(listaConfiguracao.get(0).getConvenioCobrancaVO());
                return retorno;
            }
        } else {
            tipos = new TipoConvenioCobrancaEnum[]{tipoConvenio};
        }
        //todos os convênios Online ATIVOS
        return getFacade().getConvenioCobranca().consultarPorTiposESituacao(tipos, empresa, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, false);
    }

    public void gravarAutorizacao(AutorizacaoCobrancaClienteVO autorizacao) throws Exception {
        getFacade().getAutorizacaoCobrancaCliente().incluir(autorizacao);
    }

    public static void validarDadosColaboradorPersonal(
            ColaboradorVO colaborador,
            EnderecoVO endereco,
            TelefoneVO celular,
            TelefoneVO residencial,
            EmailVO email,
            ConfiguracaoSistemaVO configuracaoSistema) throws ValidacaoException {

        if (configuracaoSistema.getCfpOb()) {
            if (colaborador.getPessoa().getCfp().trim().isEmpty()) {
                throw new ValidacaoException(new String[]{"cpfid"},
                        "O campo CPF deve ser informado");
            }
        }

        validarDadosPessoa(colaborador.getPessoa(), configuracaoSistema);
        validarEmail(email, configuracaoSistema);
        validarTelefones(celular, residencial, configuracaoSistema);
        validarEndereco(endereco, colaborador.getPessoa(), configuracaoSistema);
    }

    public static void validarDados(ClienteVO obj,
                                    EnderecoVO endereco,
                                    EnderecoVO enderecoComercial,
                                    TelefoneVO celular,
                                    TelefoneVO residencial,
                                    EmailVO email,
                                    ConfiguracaoSistemaVO configuracaoSistema,
                                    boolean pessoaEstrangeira) throws Exception {
        //----------CPF
        if (configuracaoSistema.getCfpOb()) {
            if (obj.getPessoa().getCfp().trim().isEmpty()
                    && UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())
                    && !pessoaEstrangeira) {
                throw new ValidacaoException(new String[]{"cpfid"},
                        "O campo CPF deve ser informado");
            }

            if (obj.getPessoa().getRne().trim().isEmpty()
                    && UteisValidacao.emptyNumber(obj.getPessoaResponsavel().getCodigo())
                    && pessoaEstrangeira) {
                throw new ValidacaoException(new String[]{"cpfid"},
                        "O campo RNE deve ser informado");
            }
        }

        validarDadosPessoa(obj.getPessoa(), configuracaoSistema);
        validarEmail(email, configuracaoSistema);
        validarTelefones(celular, residencial, configuracaoSistema);
        validarEndereco(endereco, obj.getPessoa(), configuracaoSistema);

        if (Uteis.nrDiasEntreDatas(obj.getPessoa().getDataNasc(), Calendario.hoje()) < (18 * 365 + 4)
                && (UteisValidacao.emptyString(obj.getPessoa().getNomeMae()) || UteisValidacao.emptyString(obj.getPessoa().getCpfMae()) || UteisValidacao.emptyString(obj.getPessoa().getEmailMae()))) {
            throw new ValidacaoException(new String[]{"cpfrespon", "nomerespon", "emailResponsavel"},
                    "Nome, CPF e Email do responsável são obrigatórios em caso de aluno menor de idade.");
        }
    }

    public static void validarEndereco(EnderecoVO endereco, PessoaVO pessoa, ConfiguracaoSistemaVO configuracaoSistema) throws ValidacaoException {
        if (endereco.getEndereco().trim().isEmpty()) {
            throw new ValidacaoException(new String[]{"enderecoid"}, "O campo ENDEREÇO (Aba - Endereço) deve ser informado.");
        }
        if (configuracaoSistema.getCepOb() && (endereco.getCep() == null || endereco.getCep().trim().isEmpty())) {
            throw new ValidacaoException(new String[]{"cepid"}, "O campo CEP (Endereço) deve ser informado.");
        }
        if (configuracaoSistema.getPaisOb() && (pessoa.getPais() == null || pessoa.getPais().getCodigo().intValue() == 0)) {
            throw new ValidacaoException(new String[]{"paisid"}, "O campo PAÍS deve ser informado.");
        }
        if (configuracaoSistema.getEstadoOb() && (pessoa.getEstadoVO() == null || pessoa.getEstadoVO().getCodigo().intValue() == 0)) {
            throw new ValidacaoException(new String[]{"estadoid"}, "O campo ESTADO deve ser informado.");
        }
        if (configuracaoSistema.getCidadeOb() && (pessoa.getCidade() == null || pessoa.getCidade().getCodigo().intValue() == 0)) {
            throw new ValidacaoException(new String[]{"cidadeid"}, "O campo CIDADE deve ser informado.");
        }
    }

    public static void validarDadosPessoa(PessoaVO pessoa, ConfiguracaoSistemaVO configuracaoSistema) throws ValidacaoException {
        if (pessoa.getNome().trim().isEmpty()) {
            throw new ValidacaoException(new String[]{"nomeid"}, "O campo NOME deve ser informado.");
        }

        if (Pattern.compile("\\d*[!@#$%&\\*\\{\\}\\[\\]\\?:><,|\\\\/\"\\+]\\d*").matcher(pessoa.getNome()).find()) {
            throw new ValidacaoException(new String[]{"nomeid"}, "O campo NOME não pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).");
        }

        if (configuracaoSistema.getDataNascOb() && pessoa.getDataNasc() == null) {
            throw new ValidacaoException(new String[]{"nascimentoid"}, "O campo DATA NASCIMENTO deve ser informado.");
        }

        if (configuracaoSistema.getSexoOb() && (pessoa.getSexo() == null || pessoa.getSexo().trim().isEmpty())) {
            throw new ValidacaoException(new String[]{"sexoid"}, "O campo SEXO deve ser informado.");
        }
    }

    public static void validarTelefones(TelefoneVO celular, TelefoneVO residencial, ConfiguracaoSistemaVO configuracaoSistema) throws ValidacaoException {
        if (configuracaoSistema.getTelefoneOb() &&
                (celular == null || UteisValidacao.emptyString(celular.getNumero())) &&
                (residencial == null || UteisValidacao.emptyString(residencial.getNumero()))

        ) {
            throw new ValidacaoException(new String[]{"celularid", "telefoneid"}, "O campo TELEFONE deve ser informado.");
        }
    }

    ;

    public static void validarEmail(EmailVO email, ConfiguracaoSistemaVO configuracaoSistema) throws ValidacaoException {

        if (configuracaoSistema.getEmailOb() && UteisValidacao.emptyString(email.getEmail())) {
            throw new ValidacaoException(new String[]{"emailid"}, "O campo EMAIL deve ser informado.");
        }
        if (!UteisValidacao.emptyString(email.getEmail())) {
            if (email.getEmail().matches("^((.)*(\\s)+(.)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O email não pode conter espaços em branco");
            }
            if (!email.getEmail().matches("^(([a-zA-Z0-9]|\\.|\\_|\\-)*@(.)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O endereço de email não pode conter caracteres especiais além de ponto (.), hífen (-) e underline (_)");
            }
            if (!email.getEmail().matches("^((.)*@([a-zA-Z0-9]|\\.|\\-)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O domínio do email não pode conter caracteres especiais além de ponto (.) e hífen (-)");
            }
            if (!email.getEmail().matches("^((.){2,}@(.){2,})$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O endereço e o domínio do email devem possuir ao menos dois caracteres cada e estarem separados por arroba (@)");
            }
            if (!email.getEmail().matches("^([a-zA-Z0-9](.)*@(.)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O endereço de email deve começar com uma letra");
            }
            if (!email.getEmail().matches("^((.)*@[a-zA-Z0-9](.)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O domínio do email deve começar com uma letra ou número");
            }
            if (!email.getEmail().matches("^((.)*@(.)*[a-zA-Z0-9])$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O domínio do email deve terminar com uma letra ou número");
            }
            if (!email.getEmail().matches("^((.)*@[^\\.\\-]*([\\.|\\-][^\\.\\-]+)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "O domínio do email não pode conter dois caracteres especiais seguidos");
            }
            if (!email.getEmail().matches("^((.)*@[^\\.]{1,26}(\\.[^\\.]{2,26})+)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "Cada domínio do email deve possuir no máximo vinte e seis caracteres e estarem separados por ponto \'.\'");
            }
            if (!email.getEmail().matches("^((.)*@[^\\.]*[a-zA-Z][^\\.]*(\\.[^\\.]*[a-zA-Z][^\\.]*)*)$")) {
                throw new ValidacaoException(new String[]{"emailid"}, "Cada domínio do email deve possuir ao menos uma letra");
            }
        }
    }

    public void incluirPlanoPersonal(PlanoVO plano,
                                     int diaVencimento,
                                     int numeroVezesParcelarAdesao,
                                     ColaboradorVO personal,
                                     EmpresaVO empresa,
                                     UsuarioVO usuario) throws Exception {

        Connection con = getFacade().getMovParcela().getCon();
        try {
            con.setAutoCommit(false);
            ControleTaxaPersonalVO controlePersonal = new ControleTaxaPersonalVO();
            controlePersonal.setDataRegistro(new Date());
            controlePersonal.setEmpresa(empresa);
            controlePersonal.setResponsavel(usuario);
            controlePersonal.setPersonal(personal);
            controlePersonal.setPlano(plano);
            controlePersonal.setPlanoPersonalTextoPadrao(new PlanoPersonalTextoPadraoVO());
            controlePersonal.getPlanoPersonalTextoPadrao().setPlanoTextoPadrao(plano.getPlanoTextoPadrao());
            controlePersonal.setDataInicioVigenciaPlano(new Date());
            controlePersonal.setDataFimVigenciaPlano(Calendario.somarMeses(new Date(), plano.getPlanoRecorrencia().getDuracaoPlano()));
            getFacade().getControleTaxaPersonal().incluirSemCommit(controlePersonal);

            List<MovParcelaVO> parcelas = getFacade()
                    .getPlanoRecorrencia()
                    .calcularParcelas(
                            plano,
                            diaVencimento,
                            numeroVezesParcelarAdesao,
                            personal.getPessoa(),
                            empresa,
                            usuario,
                            "PERSONAL TRAINER",
                            "TAXA PERSONAL");

            for (MovParcelaVO parcela : parcelas) {
                parcela.setPersonal(controlePersonal);
                parcela.setPlanoPersonal(plano.getCodigo());
                if (UteisValidacao.emptyNumber(parcela.getValorParcela())) {
                    parcela.setSituacao("PG");
                }
                getFacade().getMovParcela().incluirParcelaSemCommit(parcela);

                for (MovProdutoParcelaVO movProdutoParcela : parcela.getMovProdutoParcelaVOs()) {
                    movProdutoParcela.setMovParcela(parcela.getCodigo());
                    if (UteisValidacao.emptyNumber(movProdutoParcela.getMovProdutoVO().getTotalFinal())) {
                        movProdutoParcela.getMovProdutoVO().setSituacao("PG");
                    }
                    getFacade().getMovProduto().incluirSemCommit(movProdutoParcela.getMovProdutoVO());

                    movProdutoParcela.setMovProduto(movProdutoParcela.getMovProdutoVO().getCodigo());
                    getFacade().getMovProdutoParcela().incluir(movProdutoParcela);
                }

            }

            registrarLogVendaPlanoPersonal(controlePersonal, parcelas);

            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirColaboradorPersonal(ColaboradorVO colaborador,
                                           TelefoneVO telefoneCelular,
                                           TelefoneVO telefoneComercial,
                                           TelefoneVO telefoneResidencial,
                                           EnderecoVO endereco,
                                           EmailVO email,
                                           EmpresaVO empresa) throws Exception {

        telefoneCelular.setTipoTelefone("CE");
        telefoneComercial.setTipoTelefone("CO");
        telefoneResidencial.setTipoTelefone("RE");
        endereco.setTipoEndereco("RE");
        colaborador.setSituacao("AT");

        colaborador.getPessoa().setTelefoneVOs(new ArrayList<TelefoneVO>());
        colaborador.getPessoa().setEmailVOs(new ArrayList<EmailVO>());
        colaborador.getPessoa().setEnderecoVOs(new ArrayList<EnderecoVO>());

        if (telefoneCelular.getNumero().length() > 0) {
            colaborador.getPessoa().getTelefoneVOs().add(telefoneCelular);
        }
        if (telefoneComercial.getNumero().length() > 0) {
            colaborador.getPessoa().getTelefoneVOs().add(telefoneComercial);
        }
        if (telefoneResidencial.getNumero().length() > 0) {
            colaborador.getPessoa().getTelefoneVOs().add(telefoneResidencial);
        }

        colaborador.getPessoa().getEnderecoVOs().add(endereco);
        colaborador.getPessoa().getEmailVOs().add(email);
        colaborador.setEmpresa(empresa);

        TipoColaboradorVO tipo = new TipoColaboradorVO();
        tipo.setDescricao(TipoColaboradorEnum.PERSONAL_EXTERNO.getSigla());
        colaborador.getListaTipoColaboradorVOs().add(tipo);

        getFacade().getColaborador().incluirVendaRapida(colaborador, !validarPermissaoPersonalVendaRapida());

    }

    public void alterarColaboradorPersonal(ColaboradorVO colaborador,
                                           TelefoneVO telefoneCelular,
                                           TelefoneVO telefoneComercial,
                                           TelefoneVO telefoneResidencial,
                                           EnderecoVO endereco,
                                           EmailVO email,
                                           EmpresaVO empresa) throws Exception {

        telefoneCelular.setTipoTelefone("CE");
        telefoneComercial.setTipoTelefone("CO");
        telefoneResidencial.setTipoTelefone("RE");
        endereco.setTipoEndereco("RE");

        colaborador.getPessoa().setTelefoneVOs(new ArrayList<TelefoneVO>());
        colaborador.getPessoa().setEmailVOs(new ArrayList<EmailVO>());
        colaborador.getPessoa().setEnderecoVOs(new ArrayList<EnderecoVO>());

        if (telefoneCelular.getNumero().length() > 0) {
            colaborador.getPessoa().getTelefoneVOs().add(telefoneCelular);
        }
        if (telefoneComercial.getNumero().length() > 0) {
            colaborador.getPessoa().getTelefoneVOs().add(telefoneComercial);
        }
        if (telefoneResidencial.getNumero().length() > 0) {
            colaborador.getPessoa().getTelefoneVOs().add(telefoneResidencial);
        }

        colaborador.getPessoa().getEnderecoVOs().add(endereco);
        colaborador.getPessoa().getEmailVOs().add(email);
        colaborador.setEmpresa(empresa);

        getFacade().getColaborador().alterarVendaRapida(colaborador, !validarPermissaoPersonalVendaRapida());

    }

    public boolean validarPermissaoPersonalVendaRapida() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuario().getAdministrador()) {
                    return true;
                }
                throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "personalVendaRapida", "4.46 - Incluir/Alterar personal através da venda rápida");
                }
            }
            return true;
        } catch (Exception ex) {
            return false;
        }
    }

    public void incluirAutorizacaoCobrancaColaborador(AutorizacaoCobrancaColaboradorVO autorizacaoColaborador) throws Exception {
        getFacade().getAutorizacaoCobrancaColaborador().incluir(autorizacaoColaborador);
    }

    public void alterarAutorizacaoCobrancaColaborador(AutorizacaoCobrancaColaboradorVO autorizacaoColaborador) throws Exception {
        getFacade().getAutorizacaoCobrancaColaborador().alterar(autorizacaoColaborador);
    }

    public UsuarioVO getUsuarioLogado() throws Exception {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getUsuario();
        } catch (Exception e) {
            return null;
        }
    }

    public EmpresaVO getEmpresaLogado() throws Exception {
        if (context() != null) {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getEmpresa();
        } else {
            return new EmpresaVO();
        }
    }

}

package servicos.impl.kobana;

import br.com.pactosolucoes.enumeradores.TipoContaPagarLoteEnum;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>stulano on 21/06/2024.
 */

public class IntegracaoKobanaService extends SuperEntidade {

    private static final Integer TIMEOUT_REQUEST = 15000; //milisegundos
    private String URL_API_KOBANA = "";
    private String TOKEN_PACTO_API_KOBANA = "";
    private AmbienteEnum ambienteEnum;

    public IntegracaoKobanaService(IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        if (integracaoKobanaVO == null) {
            throw new Exception("Integração para lote de pagamento não encontrada! Entre em contato com a Pacto!");
        }
        if (integracaoKobanaVO != null && integracaoKobanaVO.getAmbiente() == null) {
            throw new Exception("Ambiente da integração para lote de pagamento não encontrado! Entre em contato com a Pacto!");
        }
        if (integracaoKobanaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO.getCodigo())) {
            this.URL_API_KOBANA = PropsService.getPropertyValue(PropsService.URL_API_KOBANA_PRODUCAO);
            this.TOKEN_PACTO_API_KOBANA = PropsService.getPropertyValue(PropsService.TOKEN_PACTO_API_KOBANA_PRODUCAO);
            this.ambienteEnum = AmbienteEnum.PRODUCAO;
        } else {
            this.URL_API_KOBANA = PropsService.getPropertyValue(PropsService.URL_API_KOBANA_SANDBOX);
            this.TOKEN_PACTO_API_KOBANA = PropsService.getPropertyValue(PropsService.TOKEN_PACTO_API_KOBANA_SANDBOX);
            this.ambienteEnum = AmbienteEnum.HOMOLOGACAO;
        }
    }

    public IntegracaoKobanaService(IntegracaoKobanaVO integracaoKobanaVO, Connection con) throws Exception {
        super(con);
        if (integracaoKobanaVO == null) {
            throw new Exception("Integração para lote de pagamento não encontrada! Entre em contato com a Pacto!");
        }
        if (integracaoKobanaVO != null && integracaoKobanaVO.getAmbiente() == null) {
            throw new Exception("Ambiente da integração para lote de pagamento não encontrado! Entre em contato com a Pacto!");
        }
        if (integracaoKobanaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO.getCodigo())) {
            this.URL_API_KOBANA = PropsService.getPropertyValue(PropsService.URL_API_KOBANA_PRODUCAO);
            this.TOKEN_PACTO_API_KOBANA = PropsService.getPropertyValue(PropsService.TOKEN_PACTO_API_KOBANA_PRODUCAO);
            this.ambienteEnum = AmbienteEnum.PRODUCAO;
        } else {
            this.URL_API_KOBANA = PropsService.getPropertyValue(PropsService.URL_API_KOBANA_SANDBOX);
            this.TOKEN_PACTO_API_KOBANA = PropsService.getPropertyValue(PropsService.TOKEN_PACTO_API_KOBANA_SANDBOX);
            this.ambienteEnum = AmbienteEnum.HOMOLOGACAO;
        }
    }

    public IntegracaoKobanaService(Connection con, AmbienteEnum ambienteEnum) throws Exception {
        super(con);
        if (ambienteEnum.equals(AmbienteEnum.PRODUCAO)) {
            this.URL_API_KOBANA = PropsService.getPropertyValue(PropsService.URL_API_KOBANA_PRODUCAO);
            this.TOKEN_PACTO_API_KOBANA = PropsService.getPropertyValue(PropsService.TOKEN_PACTO_API_KOBANA_PRODUCAO);
            this.ambienteEnum = AmbienteEnum.PRODUCAO;
        } else {
            this.URL_API_KOBANA = PropsService.getPropertyValue(PropsService.URL_API_KOBANA_SANDBOX);
            this.TOKEN_PACTO_API_KOBANA = PropsService.getPropertyValue(PropsService.TOKEN_PACTO_API_KOBANA_SANDBOX);
            this.ambienteEnum = AmbienteEnum.HOMOLOGACAO;
        }
    }

    public LoteKobanaVO criarLotePagamentoBoleto(EmpresaVO empresaVO, List<MovContaVO> listaMovContasElegiveisBoleto, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        //OBS: Este método não deve lançar exceção, sempre deve persistir o lote independente do erro
        String bodyEnvio = "";
        LoteKobanaVO lote = null;
        try {
            try {
                lote = criarLote(empresaVO, listaMovContasElegiveisBoleto, integracaoKobanaVO, TipoContaPagarLoteEnum.BOLETO);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_ERRO_CRIAR_LOTE.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.BOLETO);
                return null;
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/payment/bank_billet_batches";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            try {
                bodyEnvio = montarJsonEnvioLoteBoleto(listaMovContasElegiveisBoleto, integracaoKobanaVO);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_ERRO_CRIAR_JSON.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.BOLETO);
                return null;
            }

            lote.setParamsEnvio(bodyEnvio);
            RespostaHttpDTO resposta = null;

            try {
                resposta = service.executeRequest(endpoint, headers, null, bodyEnvio, MetodoHttpEnum.POST);
                lote.setParamsRetorno(resposta.getResponse());
            } catch (Exception ex) {
                //ERRO NA REQUISIÇÃO
                lote.setParamsRetorno(ex.getMessage());
                lote.setStatus(StatusLoteKobanaEnum.PAC_ERROR);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.PAC_ERROR);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_ERRO_REQUISICAO.getDescricao());
                return null;
            }

            //NEGADO POR ALGUM MOTIVO DA KOBANA
            if (resposta.getHttpStatus() != 201) {
                lote.setStatus(StatusLoteKobanaEnum.RECUSADO);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.RECUSADO);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_ERRO_RECUSADO.getDescricao());
            } else { //CRIADO COM SUCESSO
                processarRetornoSucessoCriarLote(empresaVO, lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_SUCESSO.getDescricao(), resposta.getResponse());
            }
            return lote;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gravar lote de pagamento depois de uma exceção: " + ex.getMessage());
        }
        return null;
    }

    public LoteKobanaVO criarLotePagamentoBoletoConsumo(EmpresaVO empresaVO, List<MovContaVO> listaMovContasElegiveisBoletoConsumo, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        //OBS: Este método não deve lançar exceção, sempre deve persistir o lote independente do erro
        String bodyEnvio = "";
        LoteKobanaVO lote = null;
        try {
            try {
                lote = criarLote(empresaVO, listaMovContasElegiveisBoletoConsumo, integracaoKobanaVO, TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_CRIAR_LOTE.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO);
                return null;
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/payment/utility_batches";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            try {
                bodyEnvio = montarJsonEnvioLoteBoleto(listaMovContasElegiveisBoletoConsumo, integracaoKobanaVO);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_CRIAR_JSON.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO);
                return null;
            }

            lote.setParamsEnvio(bodyEnvio);
            RespostaHttpDTO resposta = null;

            try {
                resposta = service.executeRequest(endpoint, headers, null, bodyEnvio, MetodoHttpEnum.POST);
                lote.setParamsRetorno(resposta.getResponse());
            } catch (Exception ex) {
                //ERRO NA REQUISIÇÃO
                lote.setParamsRetorno(ex.getMessage());
                lote.setStatus(StatusLoteKobanaEnum.PAC_ERROR);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.PAC_ERROR);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_REQUISICAO.getDescricao());
                return null;
            }

            //NEGADO POR ALGUM MOTIVO DA KOBANA
            if (resposta.getHttpStatus() != 201) {
                lote.setStatus(StatusLoteKobanaEnum.RECUSADO);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.RECUSADO);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_CONSUMO_ERRO_RECUSADO.getDescricao());
            } else { //CRIADO COM SUCESSO
                processarRetornoSucessoCriarLote(empresaVO, lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_BOLETO_CONSUMO_SUCESSO.getDescricao(), resposta.getResponse());
            }
            return lote;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gravar lote de pagamento depois de uma exceção: " + ex.getMessage());
        }
        return null;
    }

    public LoteKobanaVO criarLotePagamentoPix(EmpresaVO empresaVO, List<MovContaVO> listaMovContasElegiveisPix, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        //OBS: Este método não deve lançar exceção, sempre deve persistir o lote independente do erro
        String bodyEnvio = "";
        LoteKobanaVO lote = null;
        try {
            try {
                lote = criarLote(empresaVO, listaMovContasElegiveisPix, integracaoKobanaVO, TipoContaPagarLoteEnum.PAYLOAD_PIX);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_PIX_ERRO_CRIAR_LOTE.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.PAYLOAD_PIX);
                return null;
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/payment/pix_batches";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            try {
                bodyEnvio = montarJsonEnvioLotePix(listaMovContasElegiveisPix, integracaoKobanaVO);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_PIX_ERRO_CRIAR_JSON.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.PAYLOAD_PIX);
                return null;
            }

            lote.setParamsEnvio(bodyEnvio);
            RespostaHttpDTO resposta = null;

            try {
                resposta = service.executeRequest(endpoint, headers, null, bodyEnvio, MetodoHttpEnum.POST);
                lote.setParamsRetorno(resposta.getResponse());
            } catch (Exception ex) {
                //ERRO NA REQUISIÇÃO
                lote.setParamsRetorno(ex.getMessage());
                lote.setStatus(StatusLoteKobanaEnum.PAC_ERROR);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.PAC_ERROR);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_PIX_ERRO_REQUISICAO.getDescricao());
                return null;
            }

            //NEGADO POR ALGUM MOTIVO DA KOBANA
            if (resposta.getHttpStatus() != 201) {
                lote.setStatus(StatusLoteKobanaEnum.RECUSADO);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.RECUSADO);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_PIX_ERRO_RECUSADO.getDescricao());
            } else { //CRIADO COM SUCESSO
                processarRetornoSucessoCriarLote(empresaVO, lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_PIX_SUCESSO.getDescricao(), resposta.getResponse());
            }
            return lote;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gravar lote de pagamento depois de uma exceção: " + ex.getMessage());
        }
        return null;
    }

    public LoteKobanaVO criarLotePagamentoTransferencia(EmpresaVO empresaVO, List<MovContaVO> listaMovContasElegiveisTransferencia, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        //OBS: Este método não deve lançar exceção, sempre deve persistir o lote independente do erro
        String bodyEnvio = "";
        LoteKobanaVO lote = null;
        try {
            try {
                lote = criarLote(empresaVO, listaMovContasElegiveisTransferencia, integracaoKobanaVO, TipoContaPagarLoteEnum.TRANSFERENCIA);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_CRIAR_LOTE.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.TRANSFERENCIA);
                return null;
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/transfer/ted_batches";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            try {
                bodyEnvio = montarJsonEnvioLoteTransferencia(listaMovContasElegiveisTransferencia, integracaoKobanaVO);
            } catch (Exception ex) {
                criarLoteTentativaSomenteReferencia(empresaVO, ex.getMessage(), MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_CRIAR_JSON.getDescricao(), integracaoKobanaVO, TipoContaPagarLoteEnum.TRANSFERENCIA);
                return null;
            }

            lote.setParamsEnvio(bodyEnvio);
            RespostaHttpDTO resposta = null;

            try {
                resposta = service.executeRequest(endpoint, headers, null, bodyEnvio, MetodoHttpEnum.POST);
                lote.setParamsRetorno(resposta.getResponse());
            } catch (Exception ex) {
                //ERRO NA REQUISIÇÃO
                lote.setParamsRetorno(ex.getMessage());
                lote.setStatus(StatusLoteKobanaEnum.PAC_ERROR);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.PAC_ERROR);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_REQUISICAO.getDescricao());
                return null;
            }

            //NEGADO POR ALGUM MOTIVO DA KOBANA
            if (resposta.getHttpStatus() != 201) {
                lote.setStatus(StatusLoteKobanaEnum.RECUSADO);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.RECUSADO);
                processarRetornoCriarLote(lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_TRANSFERENCIA_ERRO_RECUSADO.getDescricao());
            } else { //CRIADO COM SUCESSO
                processarRetornoSucessoCriarLote(empresaVO, lote, MetodoZWKobanaEnum.CRIAR_NOVO_LOTE_TRANSFERENCIA_SUCESSO.getDescricao(), resposta.getResponse());
            }
            return lote;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gravar lote de pagamento depois de uma exceção: " + ex.getMessage());
        }
        return null;
    }

    public void aprovarLotePagamento(LoteKobanaVO lote, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/payment/batches/" + lote.getUid() + "/approve";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            RespostaHttpDTO resposta = null;
            resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.PUT);

            //NEGADO POR ALGUM MOTIVO DA KOBANA
            processarRetornoTentarAprovarLote(resposta, lote);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public void reprovarLotePagamento(LoteKobanaVO lote, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/payment/batches/" + lote.getUid() + "/reprove";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            RespostaHttpDTO resposta = null;
            resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.PUT);

            //NEGADO POR ALGUM MOTIVO DA KOBANA
            processarRetornoTentarReprovarLote(resposta, lote);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String sincronizarLotePagamento(LoteKobanaVO lote, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        try {
            LoteKobanaVO loteAnterior = consultarLoteBanco(lote.getCodigo());
            if (loteAnterior == null) {
                throw new Exception("Lote não encontrado no banco de dados para realizar a sincronização!");
            }
            LoteKobanaVO loteAtual;
            if (lote.getTipoContaPagarLoteEnum().equals(TipoContaPagarLoteEnum.TRANSFERENCIA)) {
                loteAtual = consultarLotePagamentoTransferencia(lote, integracaoKobanaVO);
            } else {
                //boleto, consumo e pix
                loteAtual = consultarLotePagamento(lote, integracaoKobanaVO);
            }

            boolean precisaAtualizarStatusRegistroLote = !loteAnterior.getRegistration_status().equals(loteAtual.getRegistration_status());
            boolean precisaAtualizarStatusLote = validarSePrecisaAtualizarStatusDoLote(loteAtual, loteAnterior);

            if (precisaAtualizarStatusRegistroLote) {
                loteAnterior.setRegistration_status(loteAtual.getRegistration_status());
                loteAnterior.setUpdated_at(loteAtual.getUpdated_at());
                atualizarStatusLote(loteAnterior);
            }

            boolean atualizarPagamento = false;
            List<LoteKobanaItemVO> pagamentosAnteriores = loteAnterior.getListaLoteKobanaItemVO();
            List<LoteKobanaItemVO> pagamentosAtuais = loteAtual.getListaLoteKobanaItemVO();
            List<LoteKobanaItemVO> pagamentosAtualizar = new ArrayList<>();

            for (LoteKobanaItemVO pgtoAnterior : pagamentosAnteriores) {
                for (LoteKobanaItemVO pgtoAtual : pagamentosAtuais) {
                    if (pgtoAnterior.getUid().equals(pgtoAtual.getUid())) {
                        boolean precisaAtualizarStatusItem = !pgtoAnterior.getStatus().equals(pgtoAtual.getStatus());
                        boolean precisaAtualizarStatusRegistroItem = !pgtoAnterior.getRegistration_status().equals(pgtoAtual.getRegistration_status());
                        if (precisaAtualizarStatusItem || precisaAtualizarStatusRegistroItem) {
                            atualizarPagamento = true;
                            pgtoAtual.setCodigo(pgtoAnterior.getCodigo());
                            pagamentosAtualizar.add(pgtoAtual);
                        }
                    }
                    continue;
                }
            }

            StringBuilder msgRetorno = new StringBuilder();
            if (precisaAtualizarStatusRegistroLote) {
                msgRetorno.append("Status do registro do lote foi atualizado! ");
            }
            if (atualizarPagamento && !UteisValidacao.emptyList(pagamentosAtualizar)) {
                atualizarItemsControlandoTransacao(pagamentosAtualizar, true);

                if (pagamentosAtualizar.size() == 1) {
                    msgRetorno.append("1 pagamento alterado!");
                } else {
                    msgRetorno.append(pagamentosAtualizar.size() + " pagamentos alterados!");
                }
            } else {
                msgRetorno.append("Nenhum pagamento atualizado!");
            }
            return msgRetorno.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private static boolean validarSePrecisaAtualizarStatusDoLote(LoteKobanaVO loteAtual, LoteKobanaVO loteAnterior) {
        if (loteAtual.getStatus() != null && loteAtual.getStatus().equals(StatusLoteKobanaEnum.AWAITING_APPROVAL)) {
            for (LoteKobanaItemVO item : loteAtual.getListaLoteKobanaItemVO()) {
                if (item.getStatus().equals(StatusPagamentoKobanaEnum.PENDING) || item.getStatus().equals(StatusPagamentoKobanaEnum.AWAITING_APPROVAL)) {
                    return true;
                } else if (item.getStatus().equals(StatusPagamentoKobanaEnum.CANCELED)) {
                    loteAtual.setStatus(StatusLoteKobanaEnum.GERADO);
                    return true;
                }
            }
        }
        if (loteAnterior.getStatus().equals(StatusLoteKobanaEnum.AWAITING_APPROVAL) && loteAtual.getStatus().equals(StatusLoteKobanaEnum.PENDING)) {
            //não deixar como pending, deixar como gerado mesmo...
            loteAtual.setStatus(StatusLoteKobanaEnum.GERADO);
            return true;
        }
        if (loteAtual.getStatus().equals(StatusLoteKobanaEnum.CONFIRMED)) {
            return true;
        }
        return false;
    }

    public LoteKobanaVO consultarLotePagamento(LoteKobanaVO lote, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/payment/batches/" + lote.getUid();
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            RespostaHttpDTO resposta = null;
            resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

            if (resposta.getHttpStatus() != 200) {
                throw new Exception(obterMotivoErrosKobana(resposta));
            } else {
                JSONObject jsonResposta = new JSONObject(new JSONObject(resposta).getString("response")).getJSONObject("data");
                LoteKobanaVO loteVO = new LoteKobanaVO();
                loteVO = new LoteKobanaVO(lote.getEmpresa(), jsonResposta);
                return loteVO;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public LoteKobanaVO consultarLotePagamentoTransferencia(LoteKobanaVO lote, IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token());

            String endpoint = URL_API_KOBANA + "/transfer/batches/" + lote.getUid();
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            RespostaHttpDTO resposta = null;
            resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

            if (resposta.getHttpStatus() != 200) {
                throw new Exception(obterMotivoErrosKobana(resposta));
            } else {
                JSONObject jsonResposta = new JSONObject(new JSONObject(resposta).getString("response")).getJSONObject("data");
                LoteKobanaVO loteVO = new LoteKobanaVO();
                loteVO = new LoteKobanaVO(lote.getEmpresa(), jsonResposta);
                return loteVO;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public LoteKobanaVO criarLote(EmpresaVO empresaVO, List<MovContaVO> listaMovContasElegiveis, IntegracaoKobanaVO integracaoKobanaVO, TipoContaPagarLoteEnum tipoContaPagarLoteEnum) throws Exception {
        try {
            LoteKobanaVO lote = new LoteKobanaVO();
            lote.setEmpresa(empresaVO.getCodigo());
            lote.setCreated_at(Calendario.hoje());
            lote.setUpdated_at(Calendario.hoje());
            lote.setTipoContaPagarLoteEnum(tipoContaPagarLoteEnum);
            lote.setFinancial_account_uid(integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0).getUid());
            lote.setStatus(StatusLoteKobanaEnum.GERADO);
            double valorLote = 0.0;
            for (MovContaVO movContaVO : listaMovContasElegiveis) {
                valorLote += movContaVO.getValor();
            }
            lote.setValor(valorLote);

            Connection conn = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            try {
                conn.setAutoCommit(false);

                //GRAVAR LOTE
                LoteKobanaVO loteGravado = incluirLote(lote, conn);
                lote.setCodigo(loteGravado.getCodigo());

                //GRAVAR ITEMS
                List<LoteKobanaItemVO> listaItens = new ArrayList<>();
                for (MovContaVO movContaVO : listaMovContasElegiveis) {
                    LoteKobanaItemVO item = new LoteKobanaItemVO(movContaVO, lote);
                    listaItens.add(incluirItem(item, conn));
                }
                conn.commit();
                lote.setListaLoteKobanaItemVO(listaItens);
            } catch (Exception ex) {
                conn.rollback();
                conn.setAutoCommit(true);
                throw ex;
            } finally {
                conn.setAutoCommit(true);
            }
            return lote;
        } catch (Exception e) {
            throw e;
        }
    }

    private LoteKobanaVO consultarLoteBanco(int codLote) throws Exception {
        LoteKobana loteDAO;
        try {
            loteDAO = new LoteKobana(con);
            return loteDAO.consultarPorCodigo(codLote, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception ignore) {
            return null;
        } finally {
            loteDAO = null;
        }
    }

    private void criarLoteTentativaSomenteReferencia(EmpresaVO empresaVO, String paramsRetorno, String metodo, IntegracaoKobanaVO integracaoKobanaVO, TipoContaPagarLoteEnum tipoContaPagarLoteEnum) throws Exception {
        try {
            gravarHistoricoIntegracaoKobana(empresaVO.getCodigo(), metodo, "", paramsRetorno, false, null);
            LoteKobana loteDAO;
            try {
                loteDAO = new LoteKobana(con);
                LoteKobanaVO lote = new LoteKobanaVO();
                lote.setEmpresa(empresaVO.getCodigo());
                lote.setCreated_at(Calendario.hoje());
                lote.setUpdated_at(Calendario.hoje());
                lote.setTipoContaPagarLoteEnum(tipoContaPagarLoteEnum);
                lote.setFinancial_account_uid(integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0).getUid());
                lote.setValor(0.0);
                lote.setStatus(StatusLoteKobanaEnum.PAC_ERROR);
                lote.setRegistration_status(RegistrationStatusKobanaEnum.PAC_ERROR);
                lote.setParamsEnvio("");
                lote.setParamsRetorno(paramsRetorno);
                loteDAO.incluir(lote);
            } catch (Exception ignore) {
            } finally {
                loteDAO = null;
            }
        } catch (Exception ignore) {
        }
    }

    private void processarRetornoTentarAprovarLote(RespostaHttpDTO resposta, LoteKobanaVO loteVO) throws Exception {
        //NEGADO POR ALGUM MOTIVO DA KOBANA
        if (resposta.getHttpStatus() != 201 && resposta.getHttpStatus() != 200) {
            throw new Exception(obterMotivoErrosKobana(resposta));
        } else { //SUCESSO
            loteVO.setStatus(StatusLoteKobanaEnum.GERADO);
            atualizarLoteByUid(loteVO);
        }
    }

    private void processarRetornoTentarReprovarLote(RespostaHttpDTO resposta, LoteKobanaVO loteVO) throws Exception {
        //NEGADO POR ALGUM MOTIVO DA KOBANA
        if (resposta.getHttpStatus() != 201 && resposta.getHttpStatus() != 200) {
            throw new Exception(obterMotivoErrosKobana(resposta));
        } else { //SUCESSO
            loteVO.setStatus(StatusLoteKobanaEnum.REPROVED);
            atualizarLoteByUid(loteVO);
        }
    }

    private void processarRetornoCriarLote(LoteKobanaVO loteVO, String metodo) throws Exception {
        gravarHistoricoIntegracaoKobana(loteVO.getEmpresa(), metodo, loteVO.getParamsEnvio(), loteVO.getParamsRetorno(), false, loteVO.getCodigo());
        atualizarLoteByUid(loteVO);
    }

    private void processarRetornoSucessoCriarLote(EmpresaVO empresaVO, LoteKobanaVO loteVO, String metodo, String resposta) throws Exception {
        gravarHistoricoIntegracaoKobana(loteVO.getEmpresa(), metodo, loteVO.getParamsEnvio(), loteVO.getParamsRetorno(), false, loteVO.getCodigo());
        JSONObject jsonResposta = new JSONObject(resposta).getJSONObject("data");

        //alterar o lote existente usando alguns dados que veio da resposta da requisição
        LoteKobanaVO retorno = new LoteKobanaVO(empresaVO.getCodigo(), jsonResposta);
        loteVO.setUid(retorno.getUid());
        loteVO.setStatus(StatusLoteKobanaEnum.GERADO);
        loteVO.setRegistration_status(retorno.getRegistration_status());
        loteVO.setCreated_at(retorno.getCreated_at());
        loteVO.setUpdated_at(retorno.getUpdated_at());

        atualizarLoteByCodigo(loteVO);

        //preencher objeto lote para cada item
        for (LoteKobanaItemVO item : retorno.getListaLoteKobanaItemVO()) {
            item.setLoteKobanaVO(loteVO);
        }

        //alterar items existente do lote usando alguns dados que veio da resposta da requisição
        if (!UteisValidacao.emptyList(retorno.getListaLoteKobanaItemVO())) {
            atualizarItemsControlandoTransacao(retorno.getListaLoteKobanaItemVO(), loteVO.getTipoContaPagarLoteEnum());
        }
    }

    public String montarJsonEnvioLoteBoleto(List<MovContaVO> listaMovContasElegiveisBoleto, IntegracaoKobanaVO integracaoKobanaVO) {
        List<JSONObject> boletos = new ArrayList<>();
        for (MovContaVO movContaVO : listaMovContasElegiveisBoleto) {
            JSONObject obj = new JSONObject();
            obj.put("amount", movContaVO.getValor());
            obj.put("code", movContaVO.getCodigoBarras());

            //objeto beneficiary
            JSONObject beneficiary = new JSONObject();
            beneficiary.put("document_number", Uteis.formatarCpfCnpj(movContaVO.getCpfOuCnpjBeneficiario(), true));
            beneficiary.put("name", movContaVO.getPessoaVO().getNome());
            obj.put("beneficiary", beneficiary);
            boletos.add(obj);
        }
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("financial_account_uid", integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0).getUid());
        jsonEnvio.put("payments", new JSONArray(boletos));
        return jsonEnvio.toString();
    }

    public String montarJsonEnvioLotePix(List<MovContaVO> listaMovContasPix, IntegracaoKobanaVO integracaoKobanaVO) {
        List<JSONObject> pix = new ArrayList<>();
        for (MovContaVO movContaVO : listaMovContasPix) {
            JSONObject obj = new JSONObject();
            obj.put("amount", movContaVO.getValor());
            obj.put("qrcode", movContaVO.getPayloadPix());
            pix.add(obj);
        }
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("financial_account_uid", integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0).getUid());
        jsonEnvio.put("payments", new JSONArray(pix));
        return jsonEnvio.toString();
    }

    public String montarJsonEnvioLoteTransferencia(List<MovContaVO> listaMovContasTransferencia, IntegracaoKobanaVO integracaoKobanaVO) {
        List<JSONObject> transfer = new ArrayList<>();
        for (MovContaVO movContaVO : listaMovContasTransferencia) {
            JSONObject obj = new JSONObject();
            obj.put("amount", movContaVO.getValor());
            obj.put("financial_account_uid", integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0).getUid());

            //objeto beneficiary
            JSONObject beneficiary = new JSONObject();
            beneficiary.put("document_number", movContaVO.getContaBancariaFornecedorVO().getCpfOuCnpj());
            beneficiary.put("name", movContaVO.getPessoaVO().getNome());
            obj.put("beneficiary", beneficiary);

            //objeto bank_account
            JSONObject bank_account = new JSONObject();
            bank_account.put("compe_number", ISPBContaBancaria.obterPorCodigoBanco(movContaVO.getContaBancariaFornecedorVO().getBancoVO().getCodigoBanco()).getCodigo_banco());
            bank_account.put("ispb_number", ISPBContaBancaria.obterPorCodigoBanco(movContaVO.getContaBancariaFornecedorVO().getBancoVO().getCodigoBanco()).getIspb());
            bank_account.put("agency_number", movContaVO.getContaBancariaFornecedorVO().getAgency_number());
            bank_account.put("agency_digit", movContaVO.getContaBancariaFornecedorVO().getAgency_digit());
            bank_account.put("account_number", movContaVO.getContaBancariaFornecedorVO().getAccount_number());
            bank_account.put("account_digit", movContaVO.getContaBancariaFornecedorVO().getAccount_digit());
            bank_account.put("document_number", movContaVO.getContaBancariaFornecedorVO().getCpfOuCnpj());
            obj.put("bank_account", bank_account);
            transfer.add(obj);
        }
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("financial_account_uid", integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0).getUid());
        jsonEnvio.put("transfers", new JSONArray(transfer));
        return jsonEnvio.toString();
    }

    public LoteKobanaVO incluirLote(LoteKobanaVO loteKobanaVO, Connection conn) throws Exception {
        LoteKobana loteKobanaDAO = null;
        try {
            loteKobanaDAO = new LoteKobana(conn);
            LoteKobanaVO lote = loteKobanaDAO.incluir(loteKobanaVO);
            return lote;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            loteKobanaDAO = null;
        }
    }

    public LoteKobanaItemVO incluirItem(LoteKobanaItemVO loteKobanaItemVO, Connection conn) throws Exception {
        LoteKobanaItem loteKobanaItemDAO = null;
        try {
            loteKobanaItemDAO = new LoteKobanaItem(conn);
            LoteKobanaItemVO item = loteKobanaItemDAO.incluir(loteKobanaItemVO);
            return item;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            loteKobanaItemDAO = null;
        }
    }

    public void atualizarLoteByCodigo(LoteKobanaVO lote) throws Exception {
        LoteKobana loteKobanaDAO;
        try {
            loteKobanaDAO = new LoteKobana(con);
            loteKobanaDAO.atualizarByCodigo(lote);
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            loteKobanaDAO = null;
        }
    }

    public void atualizarLoteByUid(LoteKobanaVO lote) throws Exception {
        LoteKobana loteKobanaDAO;
        try {
            loteKobanaDAO = new LoteKobana(con);
            loteKobanaDAO.atualizarByUid(lote);
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            loteKobanaDAO = null;
        }
    }

    public void atualizarItemsControlandoTransacao(List<LoteKobanaItemVO> listaItems, TipoContaPagarLoteEnum tipoContaPagarLoteEnum) throws Exception {
        if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.BOLETO) || tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.BOLETO_CONTA_CONSUMO)) {
            atualizarLoteKobanaItemBoletoOuBoletoConsumo(listaItems);
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.PAYLOAD_PIX)) {
            atualizarItemsLotePix(listaItems);
        } else if (tipoContaPagarLoteEnum.equals(TipoContaPagarLoteEnum.TRANSFERENCIA)) {
            //atualizar items transferência
            ContaBancariaFornecedor contaBancariaFornecedorDAO;
            try {
                contaBancariaFornecedorDAO = new ContaBancariaFornecedor(con);
                for (LoteKobanaItemVO item : listaItems) {
                    Integer codigo = contaBancariaFornecedorDAO.obterCodigoPorNumeroContaEAgencia(
                            item.getContaBancariaFornecedorVO().getAccount_number(), item.getContaBancariaFornecedorVO().getAgency_number());
                    item.getContaBancariaFornecedorVO().setCodigo(codigo);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao tentar atualizar items de transferência: " + ex.getMessage());
            } finally {
                contaBancariaFornecedorDAO = null;
            }
            atualizarItemsLoteTransferencia(listaItems);
        }
    }

    public void atualizarLoteKobanaItemBoletoOuBoletoConsumo(List<LoteKobanaItemVO> listaItemsBoleto) throws Exception {
        LoteKobanaItem loteKobanaItemDAO;
        Connection conn = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        try {
            conn.setAutoCommit(false);
            loteKobanaItemDAO = new LoteKobanaItem(conn);
            for (LoteKobanaItemVO item : listaItemsBoleto) {
                loteKobanaItemDAO.atualizarByCodigoBarrasAndCodLote(item);
            }
            conn.commit();
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            conn.rollback();
            conn.setAutoCommit(true);
            throw ex;
        } finally {
            conn.setAutoCommit(true);
            loteKobanaItemDAO = null;
        }
    }

    public void atualizarItemsLotePix(List<LoteKobanaItemVO> listaItemsPix) throws Exception {
        LoteKobanaItem loteKobanaItemDAO;
        Connection conn = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        try {
            conn.setAutoCommit(false);
            loteKobanaItemDAO = new LoteKobanaItem(conn);
            for (LoteKobanaItemVO item : listaItemsPix) {
                loteKobanaItemDAO.atualizarByQrCodeAndCodLote(item);
            }
            conn.commit();
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            conn.rollback();
            conn.setAutoCommit(true);
            throw ex;
        } finally {
            conn.setAutoCommit(true);
            loteKobanaItemDAO = null;
        }
    }

    public void atualizarItemsLoteTransferencia(List<LoteKobanaItemVO> listaItemsPix) throws Exception {
        LoteKobanaItem loteKobanaItemDAO;
        Connection conn = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        try {
            conn.setAutoCommit(false);
            loteKobanaItemDAO = new LoteKobanaItem(conn);
            for (LoteKobanaItemVO item : listaItemsPix) {
                loteKobanaItemDAO.atualizarByContaBancariaFornecedorAndCodLote(item);
            }
            conn.commit();
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            conn.rollback();
            conn.setAutoCommit(true);
            throw ex;
        } finally {
            conn.setAutoCommit(true);
            loteKobanaItemDAO = null;
        }
    }


    public String criarSubconta(int empresa, String bodyEnvio) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + TOKEN_PACTO_API_KOBANA);

            String endpoint = URL_API_KOBANA + "/admin/subaccounts";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            String bodyEnvioSubconta = montarBodyEnvioSubconta(bodyEnvio);

            RespostaHttpDTO resposta;
            try {
                resposta = service.executeRequest(endpoint, headers, null, bodyEnvioSubconta, MetodoHttpEnum.POST);
            } catch (Exception ex) {
                gravarHistoricoIntegracaoKobana(empresa, MetodoZWKobanaEnum.CRIAR_SUBCONTA.getDescricao(), bodyEnvioSubconta, ex.getMessage(), false, null);
                throw ex;
            }

            if (resposta.getHttpStatus() != 201) {
                gravarHistoricoIntegracaoKobana(empresa, MetodoZWKobanaEnum.CRIAR_SUBCONTA.getDescricao(), bodyEnvioSubconta, resposta.getResponse(), false, null);
                throw new Exception(obterMotivoErrosKobana(resposta));
            } else {
                gravarHistoricoIntegracaoKobana(empresa, MetodoZWKobanaEnum.CRIAR_SUBCONTA.getDescricao(), bodyEnvioSubconta, resposta.getResponse(), true, null);
                IntegracaoKobanaVO integracaoKobanaVO = salvarSubconta(empresa, resposta.getResponse(), bodyEnvioSubconta);
                try {
                    criarFinancialAccountContaCorrente(integracaoKobanaVO, empresa, bodyEnvio);
                } catch (Exception ex) {
                }
            }
            return "";
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            return ex.getMessage();
        }
    }

    public IntegracaoKobanaVO salvarSubconta(int empresa, String resposta, String bodyEnvio) throws Exception {
        IntegracaoKobana integracaoKobanaDAO = null;
        try {
            integracaoKobanaDAO = new IntegracaoKobana(con);

            String api_access_token = new JSONObject(resposta).getJSONObject("data").getString("api_access_token");
            Integer id = new JSONObject(resposta).getJSONObject("data").getInt("id");
            JSONObject jsonBodyEnvio = new JSONObject(bodyEnvio);
            IntegracaoKobanaVO obj = new IntegracaoKobanaVO(empresa, jsonBodyEnvio);
            obj.setAtivo(true);
            obj.setApi_access_token(api_access_token);
            obj.setId(id);
            obj.setCreated_At(Calendario.hoje());

            if (this.ambienteEnum == null || UteisValidacao.emptyNumber(this.ambienteEnum.getCodigo())) { // colocar homologação para não impedir de gravar no banco de dados devido erro
                obj.setAmbiente(AmbienteEnum.HOMOLOGACAO.getCodigo());
            }
            obj.setAmbiente(this.ambienteEnum.getCodigo());

            IntegracaoKobanaVO integracaoKobanaVO = integracaoKobanaDAO.incluir(obj);
            return integracaoKobanaVO;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            integracaoKobanaDAO = null;
        }
    }

    public String montarBodyEnvioSubconta(String body) throws Exception {
        JSONObject jsonEnvio = new JSONObject(body).getJSONObject("body");

        //Caso queria enviar email para o proprietárioda conta conseguir logar na Kobana basta utilizar esse método ao criar a subconta...
        // montarNomeDeUsuarioPortal(jsonEnvio);

        jsonEnvio.remove("contaCorrente");
        return jsonEnvio.toString();
    }

    public void montarNomeDeUsuarioPortal(JSONObject jsonEnvio) throws Exception {
        //Caso queria enviar email para o proprietárioda conta conseguir logar na Kobana basta utilizar esse método ao criar a subconta...
        //Utilizar a razão social para criar o nome de usuário do portal
        String[] splitRazaoSocial = jsonEnvio.getString("business_legal_name").split(" ");
        if (splitRazaoSocial.length == 1) {
            throw new Exception("Nome da Razão Social não pode ser somente uma palavra");
        } else if (splitRazaoSocial.length == 2) {
            jsonEnvio.put("first_name", splitRazaoSocial[0]);
            jsonEnvio.put("last_name", splitRazaoSocial[1]);
        } else if (splitRazaoSocial.length > 2) {
            jsonEnvio.put("first_name", splitRazaoSocial[0]);
            jsonEnvio.put("middle_name", splitRazaoSocial[1]);
            jsonEnvio.put("last_name", splitRazaoSocial[2]);
        }
    }

    public String montarBodyEnvioFinancialAccount(String body) {
        JSONObject contaCorrente = new JSONObject(body).getJSONObject("body").getJSONObject("contaCorrente");

        JSONObject financialAccount = new JSONObject();
        financialAccount.put("account_number", contaCorrente.getString("contaCorrente"));
        financialAccount.put("account_digit", contaCorrente.getString("contaCorrenteDv"));
        financialAccount.put("agency_number", contaCorrente.getString("agencia"));
        financialAccount.put("agency_digit", contaCorrente.getString("agenciaDv"));

        JSONObject dadosBanco = contaCorrente.getJSONObject("banco");
        FinancialProvidersKobanaEnum financialProvidersKobanaEnum = FinancialProvidersKobanaEnum.obterPorCodigoBancario(dadosBanco.getInt("codigoBanco"));
        financialAccount.put("financial_provider_slug", financialProvidersKobanaEnum.getSlug());
        financialAccount.put("bank_id", financialProvidersKobanaEnum.getNumber());
        financialAccount.put("kind", "checking");
        return financialAccount.toString();
    }

    public String montarBodyEnvioInativarAtivarFinancialAccount(boolean ativar) {

        int enabled = ativar ? 1 : 0; //Ativar/Desativar subconta. Obs: 1 para ativar e 0 para desativar
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("enabled", enabled);
        return jsonEnvio.toString();
    }

    public String criarFinancialAccountContaCorrente(IntegracaoKobanaVO integracaoKobanaVO, int empresa, String bodyEnvio) throws Exception {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + integracaoKobanaVO.getApi_access_token()); //usar o token da subaccount para criar a conta financeira

            String endpoint = URL_API_KOBANA + "/financial/accounts";
            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = TIMEOUT_REQUEST;

            String bodyEnvioFinancialAccount = montarBodyEnvioFinancialAccount(bodyEnvio);

            RespostaHttpDTO resposta;
            try {
                resposta = service.executeRequest(endpoint, headers, null, bodyEnvioFinancialAccount, MetodoHttpEnum.POST);
            } catch (Exception ex) {
                gravarHistoricoIntegracaoKobana(empresa, MetodoZWKobanaEnum.CRIAR_FINANCIAL_ACCOUNT.getDescricao(), bodyEnvioFinancialAccount, ex.getMessage(), false, null);
                throw ex;
            }

            if (resposta.getHttpStatus() == 201) {
                gravarHistoricoIntegracaoKobana(empresa, MetodoZWKobanaEnum.CRIAR_FINANCIAL_ACCOUNT.getDescricao(), bodyEnvioFinancialAccount, resposta.getResponse(), true, null);
                salvarFinancialAccountContaCorrente(integracaoKobanaVO.getCodigo(), empresa, resposta.getResponse());
            } else {
                gravarHistoricoIntegracaoKobana(empresa, MetodoZWKobanaEnum.CRIAR_FINANCIAL_ACCOUNT.getDescricao(), bodyEnvioFinancialAccount, resposta.getResponse(), false, null);
            }
            return "";
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public void salvarFinancialAccountContaCorrente(int codIntegracaoKobana, int empresa, String resposta) throws Exception {
        FinancialAccountKobana financialAccountKobanaDAO = null;
        try {
            financialAccountKobanaDAO = new FinancialAccountKobana(con);

            JSONObject jsonResposta = new JSONObject(resposta).getJSONObject("data");
            FinancialAccountKobanaVO obj = new FinancialAccountKobanaVO(empresa, jsonResposta);
            obj.setAtivo(true);
            obj.setCodIntegracaoKobana(codIntegracaoKobana);
            financialAccountKobanaDAO.incluir(obj);
        } catch (CobrancaException ex) {
            ex.printStackTrace();
        } finally {
            financialAccountKobanaDAO = null;
        }
    }


    public String obterMotivoErrosKobana(RespostaHttpDTO resposta) throws Exception {
        try {
            if (resposta.getHttpStatus() == 503) {
                throw new Exception("Servidor da Kobana indisponível. Tente novamente daqui a pouco");
            }
            String msg = "";
            JSONArray erros = new JSONObject(resposta.getResponse()).getJSONArray("errors");

            // Percorrendo o JSONArray de erros do retorno
            for (int i = 0; i < erros.length(); i++) {
                // Obtendo cada objeto de erro
                JSONObject erro = erros.getJSONObject(i);

                String detail = erro.getString("detail");

                msg += detail + " | ";
            }
            if (msg.contains("Payments payable code precisa conter 44, 47 ou 48 caracteres numéricos")) {
                return "Existem códigos de barras incorretos para um ou mais boletos. Verifique e tente novamente.";
            }
            if (msg.contains("CNPJ da Empresa não é válido")) {
                msg = msg.replace("CNPJ da Empresa não é válido", "CNPJ da Empresa não é válido ou já existe um cadastro com este CNPJ");
                return msg;
            }
            if (UteisValidacao.emptyString(msg)) {
                return resposta.getResponse();
            }
            return Uteis.removerUltimosCaracteres(msg, 3);
        } catch (Exception ex) {
            return resposta.getResponse();
        }
    }

    public void inativarConta(Connection con, Integer codEmpresa) throws Exception {
        IntegracaoKobana integracaoKobanaDAO = null;
        try {
            //obter a integração a ser inativada
            integracaoKobanaDAO = new IntegracaoKobana(con);
            IntegracaoKobanaVO integracaoKobanaVO = integracaoKobanaDAO.consultar(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //primeiro tentar inativar a subconta lá da kobana, se não der certo não deve prosseguir
            String retorno = inativarSubcontaKobana(integracaoKobanaVO);

            if (retorno.equals("sucesso")) {
                integracaoKobanaDAO.inativar(integracaoKobanaVO.getId());
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            integracaoKobanaDAO = null;
        }
    }

    public String inativarSubcontaKobana(IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + TOKEN_PACTO_API_KOBANA); //usar o token da subaccount

        String endpoint = URL_API_KOBANA + "/admin/subaccounts/" + integracaoKobanaVO.getId();
        RequestHttpService service = new RequestHttpService();
        service.connectTimeout = TIMEOUT_REQUEST;

        String jsonEnvio = montarBodyEnvioInativarAtivarFinancialAccount(false);

        RespostaHttpDTO resposta;
        try {
            resposta = service.executeRequest(endpoint, headers, null, jsonEnvio, MetodoHttpEnum.PUT);
        } catch (Exception ex) {
            gravarHistoricoIntegracaoKobana(integracaoKobanaVO.getEmpresa(), MetodoZWKobanaEnum.INATIVAR_SUBCONTA.getDescricao(), jsonEnvio, ex.getMessage(), false, null);
            throw ex;
        }

        if (resposta.getHttpStatus() != 200) {
            gravarHistoricoIntegracaoKobana(integracaoKobanaVO.getEmpresa(), MetodoZWKobanaEnum.INATIVAR_SUBCONTA.getDescricao(), jsonEnvio, resposta.getResponse(), false, null);
            throw new Exception(obterMotivoErrosKobana(resposta));
        } else {
            gravarHistoricoIntegracaoKobana(integracaoKobanaVO.getEmpresa(), MetodoZWKobanaEnum.INATIVAR_SUBCONTA.getDescricao(), jsonEnvio, resposta.getResponse(), true, null);
            return "sucesso";
        }
    }

    public void reativarConta(Connection con, Integer codEmpresa) throws Exception {
        IntegracaoKobana integracaoKobanaDAO = null;
        try {
            //obter a integração a ser inativada
            integracaoKobanaDAO = new IntegracaoKobana(con);
            IntegracaoKobanaVO integracaoKobanaVO = integracaoKobanaDAO.consultar(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            //primeiro tentar inativar a subconta lá da kobana, se não der certo não deve prosseguir
            String retorno = reativarSubcontaKobana(integracaoKobanaVO);

            if (retorno.equals("sucesso")) {
                integracaoKobanaDAO.reativar(integracaoKobanaVO.getId());
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            integracaoKobanaDAO = null;
        }
    }

    public String reativarSubcontaKobana(IntegracaoKobanaVO integracaoKobanaVO) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + TOKEN_PACTO_API_KOBANA); //usar o token da subaccount

        String endpoint = URL_API_KOBANA + "/admin/subaccounts/" + integracaoKobanaVO.getId();
        RequestHttpService service = new RequestHttpService();
        service.connectTimeout = TIMEOUT_REQUEST;

        String jsonEnvio = montarBodyEnvioInativarAtivarFinancialAccount(true);

        RespostaHttpDTO resposta;
        try {
            resposta = service.executeRequest(endpoint, headers, null, jsonEnvio, MetodoHttpEnum.PUT);
        } catch (Exception ex) {
            gravarHistoricoIntegracaoKobana(integracaoKobanaVO.getEmpresa(), MetodoZWKobanaEnum.REATIVAR_SUBCONTA.getDescricao(), jsonEnvio, ex.getMessage(), false, null);
            throw ex;
        }

        if (resposta.getHttpStatus() != 200) {
            gravarHistoricoIntegracaoKobana(integracaoKobanaVO.getEmpresa(), MetodoZWKobanaEnum.REATIVAR_SUBCONTA.getDescricao(), jsonEnvio, resposta.getResponse(), false, null);
            throw new Exception(obterMotivoErrosKobana(resposta));
        } else {
            gravarHistoricoIntegracaoKobana(integracaoKobanaVO.getEmpresa(), MetodoZWKobanaEnum.REATIVAR_SUBCONTA.getDescricao(), jsonEnvio, resposta.getResponse(), true, null);
            return "sucesso";
        }
    }

    public void gravarHistoricoIntegracaoKobana(int empresa, String metodo, String paramsEnvio, String paramsRetorno, boolean sucesso, Integer codLote) {
        HistoricoIntegracaoKobana historicoIntegracaoKobanaDAO = null;
        try {
            HistoricoIntegracaoKobanaVO obj = new HistoricoIntegracaoKobanaVO();
            obj.setEmpresa(empresa);
            obj.setMetodo(metodo);
            obj.setParamsEnvio(paramsEnvio);
            obj.setParamsRetorno(paramsRetorno);
            obj.setSucesso(sucesso);
            obj.setLote(codLote);

            historicoIntegracaoKobanaDAO = new HistoricoIntegracaoKobana(getCon());
            historicoIntegracaoKobanaDAO.incluir(obj);

        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Não foi possível incluir o registro na tabela de histórico de integração com a Kobana: " + ex.getMessage());
        } finally {
            historicoIntegracaoKobanaDAO = null;
        }
    }

    public String consultarDados(Connection con, int empresa) throws Exception {

        IntegracaoKobana integracaoKobanaDAO = null;
        FinancialAccountKobana financialAccountKobanaDAO = null;
        try {
            //obter a integração local no bd
            integracaoKobanaDAO = new IntegracaoKobana(con);
            IntegracaoKobanaVO integracaoKobanaVO = integracaoKobanaDAO.consultar(empresa, Uteis.NIVELMONTARDADOS_TODOS);

            //obter objeto completo lá na API
            JSONObject ret = consultarFinancialAccount(integracaoKobanaVO.getApi_access_token(), integracaoKobanaVO.getFinancialAccountsKobanaVO().get(0));
            ret.put("ambiente_apresentar", integracaoKobanaVO.getAmbiente_Apresentar());
            ret.put("api_access_token", integracaoKobanaVO.getApi_access_token());
            return ret.toString();

        } catch (CobrancaException ex) {
            ex.printStackTrace();
        } finally {
            integracaoKobanaDAO = null;
            financialAccountKobanaDAO = null;
        }
        return null;
    }

    public JSONObject consultarFinancialAccount(String token, FinancialAccountKobanaVO financialAccountKobanaVO) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + token); //usar o token da subaccount

        String endpoint = URL_API_KOBANA + "/financial/accounts/" + financialAccountKobanaVO.getUid();
        RequestHttpService service = new RequestHttpService();
        service.connectTimeout = TIMEOUT_REQUEST;

        RespostaHttpDTO resposta;
        try {
            resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.PUT);
        } catch (Exception ex) {
            throw ex;
        }

        if (resposta.getHttpStatus() != 200) {
            throw new Exception(obterMotivoErrosKobana(resposta));
        } else {
            JSONObject jsonRetorno = new JSONObject(resposta.getResponse()).getJSONObject("data");
            return jsonRetorno;
        }
    }

    public void atualizarItemsControlandoTransacao(List<LoteKobanaItemVO> listaItems, boolean controlarTransacao) throws Exception {
        LoteKobanaItem loteKobanaItemDAO;
        Connection conn;
        if (controlarTransacao) {
            conn = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        }  else {
            conn = this.con;
        }
        try {
            if (controlarTransacao) {
                conn.setAutoCommit(false);
            }
            loteKobanaItemDAO = new LoteKobanaItem(conn);
            for (LoteKobanaItemVO item : listaItems) {
                loteKobanaItemDAO.alterar(item);
            }
            if (controlarTransacao) {
                conn.commit();
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            if (controlarTransacao) {
                conn.rollback();
                conn.setAutoCommit(true);
            }
            throw ex;
        } finally {
            if (controlarTransacao) {
                conn.setAutoCommit(true);
            }
            loteKobanaItemDAO = null;
        }
    }

    public void atualizarStatusLote(LoteKobanaVO loteKobanaVO) throws Exception {
        LoteKobana loteKobanaDAO;
        Connection conn = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
        try {
            conn.setAutoCommit(false);
            loteKobanaDAO = new LoteKobana(conn);
            loteKobanaDAO.alterar(loteKobanaVO);
            conn.commit();
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            conn.rollback();
            conn.setAutoCommit(true);
            throw ex;
        } finally {
            conn.setAutoCommit(true);
            loteKobanaDAO = null;
        }
    }
}

package servicos.impl.pagbank;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.DetalhesRequestEnviadaVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.DetalhesRequestEnviada;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.PagBankServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 02/10/2024
 */
public class PagBankService extends AbstractCobrancaOnlineServiceComum implements PagBankServiceInterface {

    private String urlApiPagBank;
    private String token;

    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public PagBankService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    public PagBankService(Connection con, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    public PagBankService(Connection con, Integer convenioCobranca, boolean requisicaoConnect) throws Exception {
        super(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoesRequisicaoConnect();
    }

    private void popularInformacoes() throws Exception {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlApiPagBank = PropsService.getPropertyValue(PropsService.urlApiPagBankProducao);
            } else if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO)) {
                this.urlApiPagBank = PropsService.getPropertyValue(PropsService.urlApiPagBankSandbox);
            }
        }
        this.token = this.convenioCobrancaVO.getCodigoAutenticacao01();
    }

    private void popularInformacoesRequisicaoConnect() throws Exception {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlApiPagBank = PropsService.getPropertyValue(PropsService.urlApiPagBankProducao);
                this.token = PropsService.getPropertyValue(PropsService.tokenContaPactoPagBankProducao);
            } else if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO)) {
                this.urlApiPagBank = PropsService.getPropertyValue(PropsService.urlApiPagBankSandbox);
                this.token = PropsService.getPropertyValue(PropsService.tokenContaPactoPagBankSandbox);
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoPagBankVO(), TipoTransacaoEnum.PAGBANK, convenioCobrancaVO);
            transacaoDAO.incluir(transacao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);

            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                PessoaCPFTO pessoaCPFTO = transacaoDAO.obterDadosPessoaPagador(pessoa, false, true);

                //usar CPF do responsável
                pessoa.setCfp(pessoaCPFTO.getCpfResponsavel());
            }

            JSONObject jsonEnvio = criarTransacaoJSON(transacao, dadosCartao, pessoa);
            transacao.setParamsEnvio(removerDadosSigilososEnvio(jsonEnvio));
            transacaoDAO.alterar(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            String retorno = executarRequestPagBank("orders", jsonEnvio.toString(), MetodoHttpEnum.POST, true,
                    "transacao", transacao.getCodigo());
            processarRetorno(transacao, retorno);

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private String removerDadosSigilososEnvio(JSONObject jsonEnvio) {
        JSONObject jsonEnvioTratado = new JSONObject(jsonEnvio.toString()); //clonar para não alterar o original
        JSONObject card = jsonEnvioTratado.getJSONArray("charges").getJSONObject(0).getJSONObject("payment_method").getJSONObject("card");
        if (card.has("number")) {
            card.put("number", APF.getCartaoMascarado(card.getString("number")));
        }
        if (card.has("security_code")) {
            card.put("security_code", "***");
        }

        return jsonEnvioTratado.toString();
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO) throws Exception {
        if (qtd > 0) {
            try {
                try {
                    consultarSituacaoCobrancaTransacao(transacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    Thread.sleep(2000);
                    realizarConsultaSituacao(qtd - 1, transacaoVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public String consultarTransacao(String codigoExterno) throws Exception {
        return executarRequestPagBank("orders/" + codigoExterno, null, MetodoHttpEnum.GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Pagar.Me");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno);
        } else {
            throw new Exception("Sem código externo para consultar a transação");
        }
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno2())) {
            throw new Exception("Código externo não encontrado para realizar o cancelamento da transação");
        }
        String endpoint = "charges/" + transacaoVO.getCodigoExterno2() + "/cancel";
        String retorno = executarRequestPagBank(endpoint, montarJsonEnvioCancelamento(transacaoVO), MetodoHttpEnum.POST,
                true, "transacao", transacaoVO.getCodigo());
        processarRetornoCancelamento(transacaoVO, retorno);
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private String montarJsonEnvioCancelamento(TransacaoVO transacaoVO) {
        int amount = (int) (transacaoVO.getValor() * 100);
        JSONObject value = new JSONObject();
        value.put("value", amount);

        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("amount", value);

        return jsonEnvio.toString();
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        try {
            incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
            transacaoVO.setResultadoCancelamento(retornoCancelamento);
            if (UteisValidacao.emptyString(retornoCancelamento)) {
                throw new Exception("Erro ao realizar o cancelamento da transação. A Pagbank não nos informou o motivo, tente novamente mais tarde!");
            } else {
                JSONObject retJSON = new JSONObject(retornoCancelamento);
                if (retJSON.has("error_messages") && retJSON.optJSONArray("error_messages") != null) {
                    JSONObject error = retJSON.getJSONArray("error_messages").getJSONObject(0);
                    String code = error.optString("code");
                    String messageError = error.optString("description");
                    StringBuilder sb = new StringBuilder();
                    sb.append(code);
                    if (!UteisValidacao.emptyString(code)) {
                        sb.append(" | ");
                    }
                    sb.append(messageError);
                    if (UteisValidacao.emptyString(sb.toString())) {
                        throw new Exception("Não foi possível cancelar e nem obter o motivo da Pagbank");
                    }
                    throw new Exception(sb.toString());
                } else {
                    String status = retJSON.getString("status");
                    if (status.equalsIgnoreCase("CANCELED")) {
                        transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                        transacaoVO.setDataHoraCancelamento(Calendario.hoje());
                    } else {
                        throw new Exception("Transação não cancelada");
                    }
                }
            }
        } catch (Exception ex) {
            //gravar resultado do cancelamento
            transacaoVO.setResultadoCancelamento(retornoCancelamento);
            new Transacao(getCon()).atualizarRetornoCancelamento(retornoCancelamento, transacaoVO.getCodigo());
            ex.printStackTrace();
            consultarSituacaoCobrancaTransacao(transacaoVO);
            throw ex;
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        transacao.setParamsResposta(retorno);

        JSONObject retornoJSON = new JSONObject();
        try {
            retornoJSON = new JSONObject(retorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
        }

        if (retornoJSON.has("id")) {

            //Codigo Externo é o id do pedido (Order) no formato ex: ORDE_DE0373F5-3E99-49A8-AB5E-8067FE4B3019
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                String idOrder = retornoJSON.optString("id");
                if (!UteisValidacao.emptyString(idOrder)) {
                    transacao.setCodigoExterno(idOrder);
                }
            }

            JSONObject charge = retornoJSON.getJSONArray("charges").getJSONObject(0);

            //Codigo Externo 2 é o id da cobrança (Charge) no formato ex: CHAR_9878E93D-549B-445C-A5CA-DF810825306A
            if (UteisValidacao.emptyString(transacao.getCodigoExterno2())) {
                String idCharge = charge.getString("id");
                if (!UteisValidacao.emptyString(idCharge)) {
                    transacao.setCodigoExterno2(idCharge);
                }
            }

            try { //IDENTIFICAR A OPERADORA DO CARTAO
                String card_brand = charge.getJSONObject("payment_method").getJSONObject("card").getString("brand");
                if (!UteisValidacao.emptyString(card_brand)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = card_brand.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }


            //https://developer.pagbank.com.br/reference/objeto-charge#atributos
            //Valores possíveis: AUTHORIZED, PAID , IN_ANALYSIS , DECLINED , CANCELED , WAITING
            String status = charge.getString("status");
            if (status.equalsIgnoreCase("PAID")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("IN_ANALYSIS ") || status.equalsIgnoreCase("AUTHORIZED ") || status.equalsIgnoreCase("WAITING")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("CANCELED")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("DECLINED")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }

        } else if (retornoJSON.has("error_messages")) {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        }
    }

    private JSONObject criarTransacaoJSON(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO,
                                          PessoaVO pessoaVO) throws Exception {

        List<String> itensObrigatorios = new ArrayList<>();
        int amount = (int) (transacaoVO.getValor() * 100);

        JSONObject jsonEnvio = new JSONObject();
        String reference_id = "TRA" + transacaoVO.getCodigo();
        jsonEnvio.put("reference_id", reference_id);
        jsonEnvio.put("customer", criarCustomerJSON(itensObrigatorios, pessoaVO));
        jsonEnvio.put("items", criarItemsJSON(amount, reference_id));
        jsonEnvio.put("charges", criarChargesJSON(amount, cartaoCreditoTO, transacaoVO, reference_id));

        if (!UteisValidacao.emptyList(itensObrigatorios)) {
            String campos = "";
            for (String s : itensObrigatorios) {
                campos += (", " + s);
            }
            throw new Exception("O cliente não possui " + campos.replaceFirst(", ", "") + ".");
        }

        Uteis.logarDebug("IDENTIFICADOR PAGBANK: " + reference_id);
        return jsonEnvio;
    }

    private String executarRequestPagBank(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        return executarRequestPagBank(endPoint, body, metodoHttpEnum, false, null, null);
    }

    private String executarRequestPagBank(String endPoint, String body, MetodoHttpEnum metodoHttpEnum, boolean gravarDetalhesRequestEnviada,
                                          String nomeTabelaForeignKey, Integer codigoTabelaForeignKey) throws Exception {
        long inicio = 0;
        long tempoDecorrido = 0;
        String path = this.urlApiPagBank + endPoint;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", this.token); //no caso aqui sempre vai ser o access_token do cliente gerado no fluxo de autorizar a aplicação...

        RequestHttpService service = new RequestHttpService();
        Uteis.logarDebug("Iniciando requisição na Pagbank | URL: " + path);
        inicio = System.currentTimeMillis();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        tempoDecorrido = System.currentTimeMillis() - inicio;
        Uteis.logarDebug("Tempo de resposta da Pagbank: " + tempoDecorrido + "ms");
        if (gravarDetalhesRequestEnviada) {
            DetalhesRequestEnviada detalhesRequestEnviadaDAO;
            Connection connection;
            try {
                connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getCon());
                detalhesRequestEnviadaDAO = new DetalhesRequestEnviada(connection);
                detalhesRequestEnviadaDAO.incluir(montarObjDetalhesRequest(path, respostaHttpDTO, tempoDecorrido, nomeTabelaForeignKey, codigoTabelaForeignKey));
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                connection = null;
                detalhesRequestEnviadaDAO = null;
            }
        }
        return respostaHttpDTO.getResponse();
    }

    private String executarRequestAccessTokenPagBank(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String client_id = "";
        String client_secret = "";
        if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
            client_id = PropsService.getPropertyValue(PropsService.clientIdAplicacaoPactoPagbankProducao);
            client_secret = PropsService.getPropertyValue(PropsService.clientSecretAplicacaoPactoPagbankProducao);
        } else if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO)) {
            client_id = PropsService.getPropertyValue(PropsService.clientIdAplicacaoPactoPagbankSandbox);
            client_secret = PropsService.getPropertyValue(PropsService.clientSecretAplicacaoPactoPagbankSandbox);
        }
        String path = this.urlApiPagBank + endPoint;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("accept", "application/json");
        headers.put("Authorization", "Bearer" + this.token); //no caso aqui sempre vai ser o token da conta da Pacto, produção ou sandbox...
        headers.put("X_CLIENT_ID", client_id);
        headers.put("X_CLIENT_SECRET", client_secret);

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        return respostaHttpDTO.getResponse();
    }

    public DetalhesRequestEnviadaVO montarObjDetalhesRequest(String endpoint, RespostaHttpDTO respostaHttpDTO,
                                                             long tempoRequisicao, String nomeTabelaForeignKey, Integer codigoTabelaForeignKey) {
        DetalhesRequestEnviadaVO obj = new DetalhesRequestEnviadaVO();
        obj.setDataRegistro(Calendario.hoje());
        obj.setUrl(endpoint);
        obj.setStatusResponse(respostaHttpDTO.getHttpStatus());
        obj.setTempoRequisicaoMs(tempoRequisicao);
        if (obj.getStatusResponse() == 200 || obj.getStatusResponse() == 201) {
            obj.setSucesso(true);
        } else {
            obj.setSucesso(false);
        }
        obj.setNomeTabelaForeignKey(nomeTabelaForeignKey);
        obj.setCodigoTabelaForeignKey(codigoTabelaForeignKey);
        return obj;
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }

    private JSONObject criarCustomerJSON(List<String> itensObrigatorios, PessoaVO pessoaVO) {
        JSONObject customer = new JSONObject();

        if (UteisValidacao.emptyString(pessoaVO.getNome())) {
            itensObrigatorios.add("NOME");
        }
        customer.put("name", Uteis.retirarAcentuacaoRegex(pessoaVO.getNome()));

        if (!SuperVO.verificaCPF(pessoaVO.getCfp())) {
            itensObrigatorios.add("CPF");
        }
        customer.put("tax_id", Uteis.removerMascara(pessoaVO.getCfp())); //CPF

        String email = obterEmailPessoa(pessoaVO);
        if (UteisValidacao.emptyString(email)) {
            itensObrigatorios.add("EMAIL");
        }
        customer.put("email", email);
        return customer;
    }

    private JSONArray criarItemsJSON(int amount, String reference_id) {
        JSONArray items = new JSONArray();
        JSONObject item = new JSONObject();
        item.put("name", "Academia | Sistema Pacto"); //Descrição do item do pedido que mostrará lá no portal
        item.put("reference_id", "ITEM-" + reference_id); //Referência do item do pedido que mostrará lá no portal
        item.put("quantity", 1);
        item.put("unit_amount", amount); //valor do item do pedido que mostrará lá no portal, deve ser sempre o mesmo que o valor da transação
        items.put(item);
        return items;
    }

    private JSONArray criarChargesJSON(int amount, CartaoCreditoTO cartaoCreditoTO, TransacaoVO transacaoVO, String reference_id) {
        String numeroCartao = "";
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            numeroCartao = cartaoCreditoTO.getNumero();
        }
        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(numeroCartao, cartaoCreditoTO, transacaoVO);

        JSONArray charges = new JSONArray();
        JSONObject json = new JSONObject();
        json.put("description", "DESCRIPTION-CHARGE-" + reference_id);
        json.put("reference_id", "REFERENCE-CHARGE-" + reference_id);
        json.put("amount", montarJsonAmount(amount));
        json.put("payment_method", montarJsonPaymentMethod(cartaoCreditoTO));
        charges.put(json);

        return charges;
    }

    private JSONObject montarJsonAmount(int amount) {
        JSONObject amountJson = new JSONObject();
        amountJson.put("value", amount);
        amountJson.put("currency", "BRL");
        return amountJson;
    }

    private JSONObject montarJsonPaymentMethod(CartaoCreditoTO cartaoCreditoTO) {
        JSONObject payment_method = new JSONObject();
        payment_method.put("card", montarJsonCard(cartaoCreditoTO));
        payment_method.put("type", "CREDIT_CARD");
        payment_method.put("installments", 1);
        payment_method.put("capture", true);
        payment_method.put("soft_descriptor", formatarCampo(this.convenioCobrancaVO.getEmpresa().getRazaoSocialParaSoftDescriptor(cartaoCreditoTO.isTransacaoVerificarCartao()), 13));
        return payment_method;
    }

    private JSONObject montarJsonCard(CartaoCreditoTO cartaoCreditoTO) {
        JSONObject card = new JSONObject();
        JSONObject holder = new JSONObject();
        holder.put("name", formatarCampo(Uteis.retirarAcentuacaoRegex(cartaoCreditoTO.getNomeTitular()), 64));
        card.put("holder", holder);
        card.put("number", cartaoCreditoTO.getNumero());
        card.put("exp_month", cartaoCreditoTO.getMesValidadeMM());
        card.put("exp_year", cartaoCreditoTO.getAnoValidadeYYYY());
        card.put("security_code", cartaoCreditoTO.getCodigoSeguranca());
        card.put("store", false);
        return card;
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    //Usado para gerar o access token no fluxo de autorização lá do convênio de cobrança PagBank
    public String gerarAccessTokenConnect(String code) throws Exception {
        String retorno = "";
        try {
            if (UteisValidacao.emptyString(code)) {
                throw new Exception("Code não informado para gerar o access token");
            }
            String endpoint = "oauth2/token";
            retorno = executarRequestAccessTokenPagBank(endpoint, montarJsonAccessToken(code), MetodoHttpEnum.POST);
            Uteis.logarDebug("**AUTORIZAÇÃO PAGBANX X PACTO --> AUTORIZAÇÃO REALIZADA COM SUCESSO** | Resposta: " + retorno);

            JSONObject jsonObject = new JSONObject(retorno);
            if (jsonObject.has("error_messages") && jsonObject.optJSONArray("error_messages") != null) {
                String erro = jsonObject.getJSONArray("error_messages").getJSONObject(0).toString();
                throw new Exception(erro);
            }
            return retorno;
        } catch (Exception ex) {
            throw ex;
        }
    }

    public String montarJsonAccessToken(String code) {
        JSONObject jsonEnvio = new JSONObject();
        jsonEnvio.put("grant_type", "authorization_code");
        jsonEnvio.put("code", code);

        String redirect_uri = PropsService.getPropertyValue(PropsService.redirectUriConnectPagBank);
        jsonEnvio.put("redirect_uri", redirect_uri);

        return jsonEnvio.toString();
    }
}

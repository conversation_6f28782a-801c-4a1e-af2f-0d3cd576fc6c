package servicos.impl.pagbank;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 02/10/2024
 */

public class TransacaoPagBankVO extends TransacaoVO {

    @Override
    public String getValorCartaoMascarado() {
        return getCartaoMascarado();
    }

    @Override
    public String getResultadoRequisicao() {
        JSONObject payment_response = null;
        String message = "";
        try {
            if (!UteisValidacao.emptyString(obterValorParamsResposta("charges"))) {
                JSONObject json = new JSONObject(getParamsResposta());
                payment_response = json.getJSONArray("charges").getJSONObject(0).getJSONObject("payment_response");
                message = payment_response.optString("message");
                if (!UteisValidacao.emptyString(message)) {
                    return message;
                }
            } else if (!UteisValidacao.emptyString(getParamsResposta()) && getParamsResposta().contains("error_messages")) {
                try {
                    String code = new JSONObject(getParamsResposta()).getJSONArray("error_messages").getJSONObject(0).optString("code");
                    String messageError = new JSONObject(getParamsResposta()).getJSONArray("error_messages").getJSONObject(0).optString("description");
                    StringBuilder sb = new StringBuilder();
                    sb.append(code);
                    if (!UteisValidacao.emptyString(code)) {
                        sb.append(" | ");
                    }
                    sb.append(messageError);
                    if (UteisValidacao.emptyString(sb.toString())) {
                        return "Não foi possível obter o motivo da Pagbank";
                    }
                    return sb.toString();
                } catch (Exception ex) {
                    return "Erro ao obter o motivo da Pagbank";
                }
            }
        } catch (Exception ignored) {
        }

        if (erroAntifraud()) {
            return "Transação recusada pelo sistema de antifraude da Pagbank";
        }

        try {
            String code = payment_response.optString("code");
            PagBankRetornoEnum pagBankRetornoEnum = PagBankRetornoEnum.valueOff(code);
            if (!pagBankRetornoEnum.equals(PagBankRetornoEnum.StatusNENHUM)) {
                return pagBankRetornoEnum.getDescricao();
            } else {
                return "A Pagbank não nos informou o motivo da transação negada";
            }
        } catch (Exception ex) {
            return "Erro desconhecido";
        }
    }

    @Override
    public String getAutorizacao() {
        try {
            if (erroAntifraud()) {
                return "";
            } else {
                if (!UteisValidacao.emptyString(obterValorParamsResposta("charges"))) {
                    JSONObject charge = new JSONArray(obterValorParamsResposta("charges")).getJSONObject(0);
                    return charge.optJSONObject("payment_response").optJSONObject("raw_data").optString("authorization_code");
                }
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getBandeira() {
        try {
            String brand = "";

            try {
                brand = new JSONArray(obterValorParamsResposta("charges")).optJSONObject(0)
                        .optJSONObject("payment_method").optJSONObject("card").optString("brand");
                if (!UteisValidacao.emptyString(brand)) {
                    return brand.toUpperCase();
                }
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(brand)) {
                brand = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
            }
            return brand;
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getCartaoMascarado() {
        if (!UteisValidacao.emptyString(obterValorParamsResposta("charges"))) {
            String card_first_digits = new JSONArray(obterValorParamsResposta("charges")).optJSONObject(0)
                    .optJSONObject("payment_method").optJSONObject("card").optString("first_digits");
            String card_last_digits = new JSONArray(obterValorParamsResposta("charges")).optJSONObject(0)
                    .optJSONObject("payment_method").optJSONObject("card").optString("last_digits");
            if (!UteisValidacao.emptyString(card_first_digits) && !UteisValidacao.emptyString(card_last_digits)) {
                return card_first_digits + "******" + card_last_digits;
            }
        }

        try {
            String card = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
            return APF.getCartaoMascarado(card);
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao) && !UteisValidacao.emptyString(getCodigoExterno())) {
            return getCodigoExterno();
        }
        if (nomeAtributo.equals(APF.CodigoAutorizacao)) {
            return getAutorizacao();
        }
        if (nomeAtributo.equals(APF.CartaoMascarado)) {
            return getCartaoMascarado();
        }
        return valor;
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) {
        return getResultadoRequisicaoCancelamento();
    }

    @Override
    public String getCodErroExterno() {
        try {
            if (!UteisValidacao.emptyString(obterValorParamsResposta("charges"))) {
                return new JSONArray(obterValorParamsResposta("charges")).getJSONObject(0)
                        .optJSONObject("payment_response").optString("code");
            }
            return "?";
        } catch (Exception ex) {
            return "?";
        }
    }

    @Override
    public String getNSU() {
        try {
            if (erroAntifraud()) {
                return "";
            } else {
                if (!UteisValidacao.emptyString(obterValorParamsResposta("charges"))) {
                    JSONObject charge = new JSONArray(obterValorParamsResposta("charges")).getJSONObject(0);
                    return charge.optJSONObject("payment_response").optJSONObject("raw_data").optString("nsu");
                }
            }
            return "";
        } catch (Exception ignore) {
            return "";
        }
    }

    @Override
    public String getTID() {
        return obterValorParamsResposta("tid");
    }

    private String getResultadoRequisicaoCancelamento() {
        return "Não foi possível realizar o cancelamento. Tente novamente mais tarde.";
    }

    public String getAdquirente() {
        return obterValorParamsResposta("acquirer_name");
    }

    private String obterValorParamsResposta(String key) {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            return retornoJSON.optString(key);
        } catch (Exception ex) {
            return "";
        }
    }

    private String obterValorParamsEnvio(String key) {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsEnvio());
            return retornoJSON.optString(key);
        } catch (Exception ex) {
            return "";
        }
    }

    private boolean erroAntifraud() {
        //verifica se o motivo de negar a transação foi o antifraude
        try {
            String code = new JSONArray(obterValorParamsResposta("charges")).optJSONObject(0)
                    .optJSONObject("payment_response").optString("code");
            if (UteisValidacao.emptyString(code) && code.equals("10000")) {
                return true;
            }
            return false;
        } catch (Exception ignored) {
            return false;
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONArray(obterValorParamsEnvio("charges")).optJSONObject(0)
                    .optJSONObject("payment_method");
            return obj.optInt("installments");
        } catch (Exception ex) {
            return 0;
        }
    }

    public String getIdentificadorPacto() {
        return "";
    }
}

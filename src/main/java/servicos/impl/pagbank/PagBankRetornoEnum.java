package servicos.impl.pagbank;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 02/10/2024
 */
public enum PagBankRetornoEnum {

    StatusNENHUM("NENHUM", ""),
    Status0("20000", "Transação autorizada.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status10000("10000", "NÃO AUTORIZADO PELO PAGSEGURO. NEGADO PELO ANTIFRAUDE PAGBANK"),
    Status10001("10001", "Quantidade ADE DE TENTATIVAS EXCEDIDAS - NAO TENTE NOVAMENTE"),
    Status10002("10002", "NÃO AUTORIZADO PELO EMISSOR DO CARTAO"),
    Status10003("10003", "TRANSAÇÃO INVÁLIDA - TRANSAÇÃO MAL FORMATADA - NÃO TENTE NOVAMENTE"),
    Status10004("10004", "TRANSAÇÃO INVÁLIDA - NÃO TENTE NOVAMENTE"),
    Status20001("20001", "CONTATE A CENTRAL DO SEU CARTÃO"),
    Status20003("20003", "SALDO/LIMITE INSUFICIENTE"),
    Status20007("20007", "VERIFIQUE TODOS OS DADOS DO CARTãO, existem dados incorretos/inválidos"),
    Status20008("20008", "PARCELAMENTO INVALIDO - NAO TENTE NOVAMENTE"),
    Status20012("20012", "VALOR DA TRANSACÃO NAO PERMITIDO - NAO TENTE NOVAMENTE"),
    Status20017("20017", "TRANSACAO NAO PERMITIDA - NAO TENTE NOVAMENTE"),
    Status20018("20018", "CONTATE A CENTRAL DO SEU CARTAO - NAO TENTE NOVAMENTE"),
    Status20019("20019", "FALHA DE COMUNICACAO - TENTE MAIS TARDE"),
    Status20039("20039", "TRANSACAO NAO PERMITIDA PARA O CARTAO - NAO TENTE NOVAMENTE"),
    Status20101("20101", "SENHA INVALIDA"),
    Status20102("20102", "SENHA INVALIDA UTILIZE A NOVA SENHA"),
    Status20103("20103", "EXCEDIDAS TENTATIVAS DE SENHA. CONTATE A CENTRAL DO SEU CARTAO"),
    Status20104("20104", "VALOR EXCEDIDO. CONTATE A CENTRAL DO SEU CARTAO"),
    Status20105("20105", "Quantidade DE SAQUES EXCEDIDA. CONTATE A CENTRAL DO SEU CARTAO"),
    Status20110("20110", "CONTA DESTINO INVALIDA - NAO TENTE NOVAMENTE"),
    Status20111("20111", "CONTA ORIGEM INVALIDA - NAO TENTE NOVAMENTE"),
    Status20112("20112", "VALOR DIFERENTE DA PRE AUTORIZACAO - NAO TENTE NOVAMENTE"),
    Status20113("20113", "UTILIZE A FUNCAO CREDITO"),
    Status20114("20114", "UTILIZE a FUNCAO DEBITO"),
    Status20115("20115", "SAQUE NAO DISPONIVEL - NAO TENTE NOVAMENTE"),
    Status20116("20116", "DADOS DO CARTAO INVALIDO - NAO TENTE NOVAMENTE"),
    Status20117("20117", "ERRO NO CARTAO - NAO TENTE NOVAMENTE"),
    Status20118("20118", "SUSPENSAO DE PAGAMENTO RECORRENTE PARA SERVICO - NAO TENTE NOVAMENTE"),
    Status20119("20119", "REFAÇA A TRANSAÇÃO (EMISSOR SOLICITou RETENTATIVA)"),
    Status20158("20158", "NAO AUTORIZADA - TENTE NOVAMENTE MAIS TARDE"),
    Status20159("20159", "NAO AUTORIZADA - TENTE NOVAMENTE USANDO AUTENTICACAO"),
    Status20301("20301", "DESBLOQUEIE O CARTAO"),
    Status20999("20999", "LOJISTA, CONTATE O ADQUIRENTE - CONSULTAR CREDENCIADOR"),
    ;

    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private PagBankRetornoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private PagBankRetornoEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private PagBankRetornoEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static PagBankRetornoEnum valueOff(String id) {
        for (PagBankRetornoEnum stone : PagBankRetornoEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}

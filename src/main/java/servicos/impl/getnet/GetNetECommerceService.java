package servicos.impl.getnet;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoGetNetVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.GetNetECommerceServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.BufferedReader;
import java.io.File;
import java.io.StringReader;
import java.sql.Connection;
import java.util.*;

/**
 * Created by Luiz Felipe on 28/09/2018.
 */
public class GetNetECommerceService extends AbstractCobrancaOnlineServiceComum implements GetNetECommerceServiceInterface {

    private String urlAPI = "";
    private String org_id = "";
    private String sellerID;
    private boolean validarIpCliente = false;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioGetNet;
    private List<ConvenioCobrancaVO> listaConveniosGetnetOnline;

    public GetNetECommerceService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioGetNet = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        this.listaConveniosGetnetOnline = this.convenioCobrancaDAO.consultarPorTipoConvenio(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        popularInformacoes();
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());

            transacao = criarTransacao(dadosCartao, new TransacaoGetNetVO(), TipoTransacaoEnum.GETNET_ONLINE, this.convenioGetNet);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            //obter token para comunicação
            if (UteisValidacao.emptyString(obterTokenAcesso())) {
                throw new ConsistirException("Não foi possível obter Token Getnet.");
            }

            String email = null;
            String telefoneCelular = null;
            String telefoneResidencial = null;
            String telefoneQualquer = null;
            EnderecoVO enderecoVO = null;

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa;
            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                pessoa = this.pessoaDAO.consultarPorChavePrimaria(clienteVO.getPessoaResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                email = obterEmailPessoa(pessoa);
                telefoneCelular = obterTelefonePessoa(TipoTelefoneEnum.CELULAR, pessoa);
                telefoneResidencial = obterTelefonePessoa(TipoTelefoneEnum.RESIDENCIAL, pessoa);
                telefoneQualquer = obterTelefonePessoa(null, pessoa);
                enderecoVO = obterEnderecoPessoa(pessoa);


                //caso não tenha alguma das informações então vamos pegar da pessoa original e não do reponsável
                PessoaVO pessoaOriginal = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                if (UteisValidacao.emptyString(email)) {
                    email = obterEmailPessoa(pessoaOriginal);
                }
                if (UteisValidacao.emptyString(telefoneCelular)) {
                    telefoneCelular = obterTelefonePessoa(TipoTelefoneEnum.CELULAR, pessoaOriginal);
                }
                if (UteisValidacao.emptyString(telefoneResidencial)) {
                    telefoneResidencial = obterTelefonePessoa(TipoTelefoneEnum.RESIDENCIAL, pessoaOriginal);
                }
                if (UteisValidacao.emptyString(telefoneQualquer)) {
                    telefoneQualquer = obterTelefonePessoa(null, pessoaOriginal);
                }
                if (enderecoVO == null || UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                    enderecoVO = obterEnderecoPessoa(pessoaOriginal);
                }
                if (UteisValidacao.emptyNumber(pessoa.getCidade().getCodigo())) { //pessoa responsável sem cidade
                    pessoa.setCidade(pessoaOriginal.getCidade());
                }
                if (UteisValidacao.emptyNumber(pessoa.getEstadoVO().getCodigo())) { //pessoa responsável sem cidade
                    pessoa.setEstadoVO(pessoaOriginal.getEstadoVO());
                }
                if (UteisValidacao.emptyNumber(pessoa.getPais().getCodigo())) { //pessoa responsável sem cidade
                    pessoa.setPais(pessoaOriginal.getPais());
                }


            } else {
                pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                email = obterEmailPessoa(pessoa);
                telefoneCelular = obterTelefonePessoa(TipoTelefoneEnum.CELULAR, pessoa);
                telefoneResidencial = obterTelefonePessoa(TipoTelefoneEnum.RESIDENCIAL, pessoa);
                telefoneQualquer = obterTelefonePessoa(null, pessoa);
                enderecoVO = obterEnderecoPessoa(pessoa);
            }

            String telefoneParaPagamento = telefoneCelular;
            if (UteisValidacao.emptyString(telefoneParaPagamento)) {
                telefoneParaPagamento = telefoneResidencial;
            }
            if (UteisValidacao.emptyString(telefoneParaPagamento)) {
                telefoneParaPagamento = telefoneQualquer;
            }

            if (!UteisValidacao.emptyString(telefoneQualquer) && UteisValidacao.emptyString(telefoneCelular)) {
                telefoneCelular = telefoneQualquer;
            }
            if (!UteisValidacao.emptyString(telefoneQualquer) && UteisValidacao.emptyString(telefoneResidencial)) {
                telefoneResidencial = telefoneQualquer;
            }

            verificarAlteracaoPessoa(transacao, pessoa, email, telefoneCelular, telefoneResidencial, enderecoVO);
            if (UteisValidacao.emptyString(pessoa.getIdGetNet())) {
                throw new Exception("Não foi possível incluir cliente na GetNet.");
            }

            final String uuid = obterUUID();

            JSONObject parametrosPagamento = new JSONObject();

            //transação de verificação de cartão vai em outro endpoint
            if (dadosCartao.isTransacaoVerificarCartao()) {
                parametrosPagamento = criarParametrosPagamentoVerificacaoCartao(dadosCartao, pessoa);
                transacao.setParamsEnvio(encriptarDadosSigilososEnvioVerificacao(parametrosPagamento));
                transacaoDAO.alterar(transacao);
                validarDadosTransacao(transacao, dadosCartao);

                String retornoVerificacao = executarRequestGetNet(parametrosPagamento.toString(), METODO_CARD_VERIFICATION, ExecuteRequestHttpService.METODO_POST, false);
                processarRetornoVerificacao(transacao, retornoVerificacao);

            } else {
                parametrosPagamento = criarParametrosPagamento(dadosCartao, pessoa, transacao, uuid, email, telefoneParaPagamento, enderecoVO);
                transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
                transacaoDAO.alterar(transacao);

                validarDadosTransacao(transacao, dadosCartao);

                //somente em transação presencial
                //Device Fingerprint
                if (dadosCartao.isTransacaoPresencial() && !this.validarIpCliente) {
                    ExecuteRequestHttpService.executeRequestGET("https://h.online-metrix.net/fp/tags.js?org_id=" + this.org_id + "&session_id=" + uuid, null);
                }

                String retorno = executarRequestGetNet(parametrosPagamento.toString(), METODO_PAYMENTS, ExecuteRequestHttpService.METODO_POST, false);
                processarRetorno(transacao, retorno);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);

        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            gerarLogErroTentativaCobranca(dadosCartao, transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) {
        //não precisa criptografar cartão pois getnet utiliza o token..
        //by Luiz Felipe 25/04/2020
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        JSONObject credit = parametrosPagamento.getJSONObject("credit");
        JSONObject card = credit.getJSONObject("card");
        if (card.has("security_code")) {
            card.put("security_code", "***");
        }
        return parametrosPagamento.toString();
    }

    private String encriptarDadosSigilososEnvioVerificacao(JSONObject parametrosPagamento) {
        //não precisa criptografar cartão pois getnet utiliza o token..
        //by Luiz Felipe 25/04/2020
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        if (parametrosPagamento.has("security_code")) {
            parametrosPagamento.put("security_code", "***");
        }
        return parametrosPagamento.toString();
    }

    private String obterUUID() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        if (uuid.length() > 30) {
            return uuid.substring(0, 30);
        }
        return uuid;
    }

    private void popularInformacoes() throws Exception {
        if (this.convenioGetNet != null) {
            if (UteisValidacao.emptyString(convenioGetNet.getCodigoAutenticacao01())) {
                throw new Exception(convenioGetNet.getLabelCodigoAutenticacao01() + " não informado!");
            } else if (UteisValidacao.emptyString(convenioGetNet.getCodigoAutenticacao02())) {
                throw new Exception(convenioGetNet.getLabelCodigoAutenticacao02() + " não informado!");
            } else if (UteisValidacao.emptyString(convenioGetNet.getCodigoAutenticacao03())) {
                throw new Exception(convenioGetNet.getLabelCodigoAutenticacao03() + " não informado!");
            }

            if (this.convenioGetNet.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiGetnetProducao);
                this.org_id = PropsService.getPropertyValue(PropsService.getNetECommerceOrgIdProducao);
            } else {
                this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiGetnetSandbox);
                this.org_id = PropsService.getPropertyValue(PropsService.getNetECommerceOrgIdSandbox);
            }

            this.sellerID = convenioGetNet.getCodigoAutenticacao03();

            try {
                if (this.convenioGetNet.getCodigoAutenticacao07().equalsIgnoreCase("true")) {
                    this.validarIpCliente = true;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void realizarRetentativa(int qtd, TransacaoVO transacao) throws Exception {
        if (qtd > 0) {
            String retorno = consultarTransacao(Integer.valueOf(transacao.getCodigoExterno()));
            processarRetorno(transacao, retorno);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                Thread.sleep(1000);
                realizarRetentativa(qtd - 1, transacao);
            }
        }
    }

    @Override
    public String consultarTransacao(Integer id) throws Exception {
        return executarRequestGetNet(null, "/bills/" + id, ExecuteRequestHttpService.METODO_GET, false);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para GetNet.");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarRetentativa(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {

            if (transacao.getDataProcessamento() != null && isMesmoDia(transacao.getDataProcessamento(), Calendario.hoje())) {
                cancelarPagamentoMesmaDataProcessamento(transacao);
            } else {
                cancelarPagamentoPosteriorDataProcessamento(transacao);
            }


//            JSONObject jsonCance = new JSONObject();
//            try {
//                jsonCance = new JSONObject(transacao.getResultadoCancelamento());
//            } catch (Exception ignored) { }
//
//            if (jsonCance.has("cancel_request_id")) {
//                String cancel_request_id = jsonCance.optString("cancel_request_id");
//                retorno = executarRequestGetNet(null, METODO_CONSULTA_CANCEL + "/" + cancel_request_id, ExecuteRequestHttpService.METODO_GET);
//            } else if (jsonCance.has("cancel_custom_key")) {
//                String cancel_custom_key = jsonCance.optString("cancel_custom_key");
//                retorno = executarRequestGetNet(null, METODO_CONSULTA_CANCEL + "?cancel_custom_key=" + cancel_custom_key, ExecuteRequestHttpService.METODO_GET);
//            }


            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && (estornarRecibo && transacao.getReciboPagamento() != 0)) {
                estornarRecibo(transacao, estornarRecibo);
                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            }
            new Transacao(getCon()).alterar(transacao);
        }
        return transacao;
    }

    private void cancelarPagamentoPosteriorDataProcessamento(TransacaoVO transacao) throws Exception {
        String retorno = "";
        String cancel_custom_key = "TRA" + transacao.getCodigo() + "CAN";

        int valorCentavos = (int) (transacao.getValor() * 100);
        JSONObject json = new JSONObject();
        json.put("payment_id", transacao.getCodigoExterno());
        json.put("cancel_amount", valorCentavos);
        json.put("cancel_custom_key", cancel_custom_key);
        retorno = executarRequestGetNet(json.toString(), METODO_CANCEL, ExecuteRequestHttpService.METODO_POST, false);

        if (retorno.toUpperCase().contains("CHAVE DO CLIENTE JA SE ENCONTRA EM USO")) {
            try {
                String consultaCancelamento = executarRequestGetNetNovo(METODO_CANCEL + "?cancel_custom_key=" + cancel_custom_key, null, MetodoHttpEnum.GET);
                incluirHistoricoRetornoTransacao(transacao, consultaCancelamento, "retornoConsultaCancelamento");
                transacao.setResultadoCancelamento(consultaCancelamento);
                JSONObject jsonCan = new JSONObject(consultaCancelamento);
                if (jsonCan.optString("status_processing_cancel_message").equalsIgnoreCase("REALIZADO")) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            } catch (Exception ignored) {
            }
        } else {
            processarRetornoCancelamento(transacao, retorno);
        }
    }

    private void cancelarPagamentoMesmaDataProcessamento(TransacaoVO transacao) throws Exception {
        String retorno = "";
        String urlCancelamento = "/v1/payments/credit/" + transacao.getCodigoExterno() + "/cancel";

        retorno = executarRequestGetNet(null, urlCancelamento, ExecuteRequestHttpService.METODO_POST, true);
        processarRetornoCancelamento(transacao, retorno);
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacao) throws Exception {
//        String retorno = executarRequestGetNet(null, "/charges/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
//        processarRetorno(transacao, retorno);
//        new Transacao(getCon()).alterar(transacao);
        throw new Exception("Consulta de pagamento não disponível para Getnet");
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, String retorno) {
        try {
            incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetornoCancelamento");
            transacao.setResultadoCancelamento(retorno);
            JSONObject retornoCancelamento = new JSONObject(retorno);

            String status = retornoCancelamento.optString("status");
            if (status.equalsIgnoreCase("ACCEPTED") ||
                    status.equalsIgnoreCase("CANCELED")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacao.setDataHoraCancelamento(Calendario.hoje());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) throws JSONException {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");

        transacao.setParamsResposta(retorno);
        JSONObject retornoJSON = new JSONObject(retorno);

        if (retornoJSON.has("payment_id")) {

            String payment_id = retornoJSON.getString("payment_id");
            transacao.setCodigoExterno(payment_id);

            if (retornoJSON.has("status")) {
                String status = retornoJSON.getString("status");

                JSONObject credit = retornoJSON.getJSONObject("credit");

                String autorizacao = credit.getString("authorization_code");
                transacao.setCodigoAutorizacao(autorizacao);

                if (status.equalsIgnoreCase("APPROVED")) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                } else if (status.equalsIgnoreCase("AUTHORIZED")) {
                    transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
                } else {
                    transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                }

            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            }

        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            preencherCodigoRetornoErroAntifraude(transacao, retornoJSON);
        }
    }

    private static void preencherCodigoRetornoErroAntifraude(TransacaoVO transacao, JSONObject retornoJSON) {
        try {
            if (retornoJSON.opt("status_code").equals(402) && retornoJSON.keySet().contains("details")) {
                JSONArray arrayDetalhesJSON = new JSONArray(retornoJSON.opt("details").toString());
                if (arrayDetalhesJSON.length() > 0) {
                    JSONObject detalhes = arrayDetalhesJSON.getJSONObject(0);
                    if (detalhes.keySet().contains("antifraud")) {
                        JSONObject antifraud = detalhes.optJSONObject("antifraud");
                        if (antifraud.keySet().contains("status_code")) {
                            transacao.setCodigoRetorno(antifraud.optString("status_code"));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            System.out.println("Erro para tentar pegar codigo retorno negacao antifraude: " + ex.getMessage());
        }
    }

    private void processarRetornoVerificacao(TransacaoVO transacao, String retorno) throws JSONException {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");

        transacao.setParamsResposta(retorno);
        JSONObject retornoJSON = new JSONObject(retorno);

        if (retornoJSON.has("status")) {
            String status = retornoJSON.optString("status");
            if (status.equals("VERIFIED")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private JSONObject criarParametrosPagamento(CartaoCreditoTO dadosCartao, PessoaVO pessoa, TransacaoVO transacao, String uuid,
                                                String email, String telefone, EnderecoVO enderecoVO) throws Exception {

        JSONObject pagamento = new JSONObject();
        pagamento.put("seller_id", getSellerID());

        int valorCentavos = (int) (dadosCartao.getValor() * 100);

        pagamento.put("amount", valorCentavos); //valor em centavos
        pagamento.put("currency", "BRL");
        pagamento.put("order", criarParametrosOrder(transacao));
        pagamento.put("customer", criarParametrosCustomer(pessoa, email, telefone, enderecoVO));
        pagamento.put("shippings", criarParametrosShippings(pessoa, email, telefone, enderecoVO));
        pagamento.put("credit", criarParametrosCartao(dadosCartao, pessoa, transacao));

        //somente presencial enviar o device
        if (dadosCartao.isTransacaoPresencial()) {
            JSONObject deviceJSON = criarParametrosDevice(dadosCartao, transacao, uuid);
            if (deviceJSON != null) {
                pagamento.put("device", deviceJSON);
            }
        }
        return pagamento;
    }

    private JSONObject criarParametrosPagamentoVerificacaoCartao(CartaoCreditoTO dadosCartao, PessoaVO pessoa) throws Exception {

        JSONObject verificacao = new JSONObject();

        //tokenizar o cartão primeiro
        verificacao.put("number_token", gerarTokenCartao(dadosCartao.getNumero(), pessoa));

        //demais dados...
        verificacao.put("brand", dadosCartao.getBand().getDescricao()); //valor em centavos
        verificacao.put("cardholder_name", dadosCartao.getNomeTitular().toUpperCase());
        verificacao.put("expiration_month", dadosCartao.getValidade().substring(0, 2));
        verificacao.put("expiration_year", dadosCartao.getAnoValidadeYY());
        verificacao.put("security_code", dadosCartao.getCodigoSeguranca());

        return verificacao;
    }

    private JSONObject criarParametrosDevice(CartaoCreditoTO cartaoCreditoTO,
                                             TransacaoVO transacaoVO, String uuid) throws Exception {
        if (this.validarIpCliente) {
            return criarJSONDeviceIpCliente(cartaoCreditoTO, transacaoVO, uuid);
        } else {
            return criarJSONDeviceAntigo(uuid);
        }
    }

    private JSONObject criarJSONDeviceIpCliente(CartaoCreditoTO cartaoCreditoTO, TransacaoVO transacaoVO, String uuid) throws Exception {
        String ipCliente = "";
        try {
            if (JSFUtilities.isJSFContext()) {
                SuperControle superControle = (SuperControle) JSFUtilities.getFromSession("SuperControle");
                if (superControle == null) {
                    superControle = (SuperControle) JSFUtilities.getManagedBean("SuperControle");
                }
                if (superControle != null) {
                    ipCliente = superControle.getIpCliente();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("GETNET | Transacao " + transacaoVO.getCodigo() + " | ERRO | criarParametrosDeviceIpCliente | " + ex.getMessage());
        }
        if (UteisValidacao.emptyString(ipCliente)) {
            ipCliente = cartaoCreditoTO.getIpClientePacto();
        }

        if (UteisValidacao.emptyString(ipCliente)) {
            Uteis.logarDebug("GETNET | Transacao " + transacaoVO.getCodigo() + " | IP do cliente não identificado");
            return null;
        }

        JSONObject device = new JSONObject();
        device.put("ip_address", ipCliente);
        return device;
    }

    private JSONObject criarJSONDeviceAntigo(String uuid) throws Exception {
        String ipExterno = obterIPGetNet();

        if (UteisValidacao.emptyString(ipExterno)) {
            ipExterno = ExecuteRequestHttpService.obterIPExterno();
        }

        if (UteisValidacao.emptyString(ipExterno) || ipExterno.equalsIgnoreCase("SEM_IP")) {
            throw new Exception("Erro ao obter IP cliente!");
        }

        JSONObject device = new JSONObject();
        device.put("ip_address", ipExterno);
        device.put("device_id", uuid);
        return device;
    }

    private String obterIPGetNet() {
        String ipExterno = "";
        try {

            try {
                if (PropsService.isTrue(PropsService.utilizarIpServidorGetnet) &&
                        !UteisValidacao.emptyString(SuperControle.getIpServidor())) {
                    return SuperControle.getIpServidor();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            ipExterno = (String) JSFUtilities.getFromSession("ip");
            if (!(ipExterno != null && !ipExterno.isEmpty()
                    && !ipExterno.equals("N/C")
                    && !ipExterno.toUpperCase().contains("N")
                    && !ipExterno.toUpperCase().contains("C"))) {


                Random rand = new Random();
                int n = rand.nextInt(35); //35 tamanho da lista de IP do arquivo ipAleatorioGetNet.txt
                n += 1;

                File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/ipAleatorioGetNet.txt").toURI());
                StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
                BufferedReader arquivo = new BufferedReader(new StringReader(texto.toString()));
                String linha = null;
                int i = 0;
                while ((linha = arquivo.readLine()) != null) {
                    try {
                        if (!UteisValidacao.emptyString(linha) && (i == n)) {
                            return linha;
                        }
                        i++;
                    } catch (Exception ignored) {
                    }
                }
            } else {
                return ipExterno;
            }
        } catch (Exception ex) {
            ipExterno = "";
        }
        return ipExterno;
    }

    private JSONObject criarParametrosOrder(TransacaoVO transacaoVO) {

        String identificador = "TRA" + transacaoVO.getCodigo();
        Uteis.logarDebug("IDENTIFICADOR GETNET-ONLINE: " + identificador);

        JSONObject order = new JSONObject();
        order.put("order_id", identificador);
        return order;
    }

    private JSONArray criarParametrosShippings(PessoaVO pessoa, String email, String telefone, EnderecoVO enderecoVO) {
        JSONObject json = new JSONObject();
        json.put("first_name", Uteis.retirarAcentuacaoRegex(Uteis.getPrimeiroNome(pessoa.getNome())));
        json.put("name", Uteis.retirarAcentuacaoRegex(pessoa.getNome()));

        if (!UteisValidacao.emptyString(email)) {
            json.put("email", email);
        }

        if (!UteisValidacao.emptyString(telefone)) {
            json.put("phone_number", telefone);
        }

        JSONObject address = new JSONObject();
        if (!UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            address.put("street", Uteis.retirarAcentuacaoRegex(enderecoVO.getEndereco()));
            address.put("number", enderecoVO.getNumero());
            address.put("complement", Uteis.retirarAcentuacaoRegex(enderecoVO.getComplemento()));
            address.put("district", Uteis.retirarAcentuacaoRegex(enderecoVO.getBairro()));
            address.put("city", Uteis.retirarAcentuacaoRegex(pessoa.getCidade().getNome()));
            address.put("state", pessoa.getEstadoVO().getSigla());
            address.put("country", pessoa.getCidade().getPais().getNome());
            address.put("postal_code", Uteis.tirarCaracteres(enderecoVO.getCep(), true));
        }

        json.put("address", address);

        JSONArray array = new JSONArray();
        array.put(json);
        return array;
    }

    private JSONObject criarParametrosCustomer(PessoaVO pessoa, String email, String telefone, EnderecoVO enderecoVO) throws Exception {
        JSONObject customer = new JSONObject();
        customer.put("customer_id", pessoa.getIdGetNet());
        customer.put("first_name", Uteis.retirarAcentuacaoRegex(Uteis.getPrimeiroNome(pessoa.getNome())));
        customer.put("last_name", Uteis.retirarAcentuacaoRegex(Uteis.getSobrenome(pessoa.getNome())));

        String cpfCNPJ = Uteis.removerMascara(pessoa.getCfp());
        if (cpfCNPJ.length() > 11) {
            customer.put("document_type", "CNPJ");
        } else {
            customer.put("document_type", "CPF");
        }
        customer.put("document_number", cpfCNPJ);

        if (!UteisValidacao.emptyString(email)) {
            customer.put("email", email);
        }

        if (!UteisValidacao.emptyString(telefone)) {
            customer.put("phone_number", telefone);
        }

        JSONObject billing_address = new JSONObject();
        if (!UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            billing_address.put("street", Uteis.retirarAcentuacaoRegex(enderecoVO.getEndereco()));
            billing_address.put("number", enderecoVO.getNumero());
            billing_address.put("complement", Uteis.retirarAcentuacaoRegex(enderecoVO.getComplemento()));
            billing_address.put("district", Uteis.retirarAcentuacaoRegex(enderecoVO.getBairro()));
            billing_address.put("city", Uteis.retirarAcentuacaoRegex(pessoa.getCidade().getNome()));
            billing_address.put("state", pessoa.getEstadoVO().getSigla());
            billing_address.put("country", pessoa.getCidade().getPais().getNome());
            billing_address.put("postal_code", Uteis.tirarCaracteres(enderecoVO.getCep(), true));
        }

        customer.put("billing_address", billing_address);
        return customer;
    }

    private JSONObject criarParametrosCartao(CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO, TransacaoVO transacaoVO) throws Exception {
        JSONObject dadosCartao = new JSONObject();
        dadosCartao.put("delayed", false);
        dadosCartao.put("save_card_data", false);

        if (cartaoCreditoTO.getParcelas() > 1) {
            dadosCartao.put("transaction_type", "INSTALL_NO_INTEREST");
        } else {
            dadosCartao.put("transaction_type", "FULL");
        }

        dadosCartao.put("number_installments", cartaoCreditoTO.getParcelas());

        if (!UteisValidacao.emptyString(this.convenioGetNet.getEmpresa().getRazaoSocial())) {
            dadosCartao.put("soft_descriptor", StringUtilities.formatarCampoEmBranco(this.convenioGetNet.getEmpresa().getRazaoSocialParaSoftDescriptor(cartaoCreditoTO.isTransacaoVerificarCartao()), 22));
        }

        dadosCartao.put("card", criarParametrosCard(cartaoCreditoTO, pessoaVO, transacaoVO));

        //para transação recorrência (não presencial)
        if (!cartaoCreditoTO.isTransacaoPresencial()) {
            dadosCartao.put("credentials_on_file_type", "RECURRING");

            //Para Getnet na recorrência (Transação não presencial)
            //Enviar na tentativa da transação, o código de uma transação (transaction_id) feita no passado com o mesmo cartão e que foi efetivada com sucesso
            try {
                Integer situacoes[] = {SituacaoTransacaoEnum.APROVADA.getId(), SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId(), SituacaoTransacaoEnum.CANCELADA.getId()}; //tipo 2 é referente ao dcc, 8 é getnet
                String paramsRespostaUltTransacaoAprovadaDoCartao = transacaoDAO.consultarParamsRespostaPorPessoaCartaoEmpresaSituacao(
                        transacaoVO.getPessoaPagador().getCodigo(), APF.getCartaoMascarado(cartaoCreditoTO.getNumero()), transacaoVO.getEmpresa(), situacoes);
                String transactionIDUltTransacaoAprovadaDoCartao = obterTransactionIDUltTransacaoAprovadaDoCartao(paramsRespostaUltTransacaoAprovadaDoCartao);
                if (!UteisValidacao.emptyString(transactionIDUltTransacaoAprovadaDoCartao)) {
                    dadosCartao.put("transaction_id", transactionIDUltTransacaoAprovadaDoCartao);
                }
            } catch (Exception ex) {
            }
        }
        return dadosCartao;
    }

    private String obterTransactionIDUltTransacaoAprovadaDoCartao(String paramsResposta) {
        try {
            JSONObject json = new JSONObject(paramsResposta);
            if (json.has("credit")) {
                JSONObject credit = json.getJSONObject("credit");
                return credit.optString("transaction_id");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    private JSONObject criarParametrosCard(CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO, TransacaoVO transacaoVO) throws Exception {
        JSONObject dadosCartao = new JSONObject();

        String numeroCartao = "";
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            numeroCartao = cartaoCreditoTO.getNumero();
        }

        dadosCartao.put("number_token", gerarTokenCartao(numeroCartao, pessoaVO));
        dadosCartao.put("cardholder_name", cartaoCreditoTO.getNomeTitular());
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
            dadosCartao.put("security_code", cartaoCreditoTO.getCodigoSeguranca());
        }

        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(numeroCartao, cartaoCreditoTO, transacaoVO);

        dadosCartao.put("expiration_month", cartaoCreditoTO.getValidade().substring(0, 2));
        dadosCartao.put("expiration_year", cartaoCreditoTO.getAnoValidadeYY());
        dadosCartao.put("brand", cartaoCreditoTO.getBand().getDescricao());
        return dadosCartao;
    }

    private void verificarAlteracaoPessoa(TransacaoVO transacaoVO, PessoaVO pessoa, String email, String celular,
                                          String telefone, EnderecoVO enderecoVO) throws Exception {
        if ((this.getListaConveniosGetnetOnline().size() > 1) || dadosDesatualizados(pessoa)) {
            incluirPessoa(transacaoVO, pessoa, email, celular, telefone, enderecoVO);
        }
    }

    private String gerarTokenCartao(String numeroCartao, PessoaVO pessoaVO) throws Exception {
        try {
            JSONObject json = new JSONObject();
            json.put("customer_id", pessoaVO.getIdGetNet());
            json.put("card_number", numeroCartao);

            String retorno = executarRequestGetNet(json.toString(), METODO_TOKEN_CARD, ExecuteRequestHttpService.METODO_POST, false);
            JSONObject retornoJSON = new JSONObject(retorno);
            if (retornoJSON.has("number_token")) {
                String cartaoToken = retornoJSON.optString("number_token");
                if (UteisValidacao.emptyString(cartaoToken)) {
                    throw new Exception(retorno);
                }
                return cartaoToken;
            } else if (retornoJSON.has("message")) {
                String msgErro = retornoJSON.optString("message");
                if (retornoJSON.has("details")) {
                    msgErro += " - " + retornoJSON.optString("details");
                }
                throw new Exception(msgErro);
            }
            throw new Exception(retorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Não foi possível gerar o token do cartão na Getnet: " + ex.getMessage());
        }
    }

    private Boolean dadosDesatualizados(PessoaVO pessoa) throws Exception {
        Boolean dadosDesatualizados = UteisValidacao.emptyString(pessoa.getIdGetNet());
        if (!dadosDesatualizados) {
            LogVO ultimoLog = this.logDAO.consultarUltimoLogPessoa(pessoa.getCodigo(), "CLIENTE", "PESSOA", "CLIENTE - EMPRESA");
            if (ultimoLog != null) {
                dadosDesatualizados = pessoa.getDataAlteracaoGetNet().getTime() < ultimoLog.getDataAlteracao().getTime();
            } else {
                dadosDesatualizados = true;
            }
        }
        return dadosDesatualizados;
    }

    private void incluirPessoa(TransacaoVO transacaoVO, PessoaVO pessoa, String email,
                               String celular, String telefone, EnderecoVO enderecoVO) throws Exception {
        Pessoa pessoaDAO = null;
        Empresa empresaDAO = null;
        try {
            pessoaDAO = new Pessoa(getCon());
            empresaDAO = new Empresa(getCon());
            List<String> obs = new ArrayList<>();

            if (UteisValidacao.emptyString(pessoa.getCfp().trim())) {
                obs.add("CPF");
            } else if (!SuperVO.verificaCPF(pessoa.getCfp())) {
                obs.add("CPF válido");
            }

            if (UteisValidacao.emptyString(Uteis.getSobrenome(pessoa.getNome().trim()))) {
                obs.add("sobrenome");
            }

            // CIDADE, ESTADO E PAÍS se não tiver, pode pegar da empresa mesmo
            boolean precisaUtilizarEnderecoDaEmpresa = UteisValidacao.emptyString(pessoa.getCidade().getNome().trim())
                    || UteisValidacao.emptyString(pessoa.getEstadoVO().getSigla().trim())
                    || UteisValidacao.emptyString(pessoa.getCidade().getPais().getNome().trim());

            if (precisaUtilizarEnderecoDaEmpresa && !UteisValidacao.emptyNumber(transacaoVO.getEmpresa())) {
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
                if (empresaVO != null && UteisValidacao.emptyString(pessoa.getCidade().getNome().trim()) || UteisValidacao.emptyString(pessoa.getCidade().getPais().getNome().trim())
                        && empresaVO.getCidade() != null) {
                    pessoa.setCidade(empresaVO.getCidade());
                }
                if (empresaVO != null && UteisValidacao.emptyString(pessoa.getEstadoVO().getSigla().trim()) && empresaVO.getEstado() != null) {
                    pessoa.setEstadoVO(empresaVO.getEstado());
                }
            }

            //validar se ainda está vazio
            if (UteisValidacao.emptyString(pessoa.getCidade().getNome().trim())) {
                obs.add("cidade");
            }
            if (UteisValidacao.emptyString(pessoa.getEstadoVO().getSigla().trim())) {
                obs.add("estado");
            }
            if (UteisValidacao.emptyString(pessoa.getCidade().getPais().getNome().trim())) {
                obs.add("país");
            }

            if (UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                obs.add("endereço");
            } else {
                if (UteisValidacao.emptyString(enderecoVO.getCep().trim())) {
                    obs.add("CEP");
                }
                if (UteisValidacao.emptyString(enderecoVO.getEndereco().trim())) {
                    obs.add("endereço");
                }
                if (UteisValidacao.emptyString(enderecoVO.getBairro().trim())) {
                    obs.add("bairro");
                }
            }

            if (obs.size() > 0) {
                String campos = "";
                for (String s : obs) {
                    campos += (", " + s);
                }

                throw new Exception("O cliente ou seu responsável não possui " + campos.replaceFirst(", ", "") + ". Cadastre e volte aqui para efetuar o pagamento.");
            }

            JSONObject pessoaJSON = criarPessoaJSON(pessoa, email, celular, telefone, enderecoVO);
            JSONObject resposta = null;
            if (UteisValidacao.emptyString(pessoa.getIdGetNet())) {
                String requestString = executarRequestGetNet(pessoaJSON.toString(), METODO_CUSTOMERS, ExecuteRequestHttpService.METODO_POST, false);
                incluirHistoricoRetornoTransacao(transacaoVO, requestString, "incluirPessoa");
                resposta = new JSONObject(requestString);

                if (resposta.has("message") && (resposta.optString("message").contains("registrado com outro número de documento") ||
                        resposta.optString("message").contains("Número de documento já está cadastrado"))) {
                    Uteis.logar(null, "Cliente já cadastrado.. buscar pelo CPF " + pessoa.getCfp() + " -- " + resposta.toString());

                    JSONObject respostaConsulta = new JSONObject(executarRequestGetNet(null, METODO_CUSTOMERS + "?page=1&limit=10&document_number=" + Uteis.formatarCpfCnpj(pessoa.getCfp(), true), ExecuteRequestHttpService.METODO_GET, false));
                    JSONArray lista = respostaConsulta.getJSONArray("customers");
                    for (int e = 0; e < lista.length(); e++) {
                        try {
                            resposta = lista.getJSONObject(e);
                            break;
                        } catch (Exception ignored) {
                        }
                    }
                } else if (resposta.has("message") && resposta.optString("message").contains("registrado com outro")) {
                    String nossoCodigo = "P" + pessoa.getCodigo();
                    Uteis.logar(null, "Cliente já cadastrado.. buscar pelo nosso código " + nossoCodigo + " -- " + resposta.toString());
                    resposta = new JSONObject(executarRequestGetNet(null, METODO_CUSTOMERS + "/" + nossoCodigo, ExecuteRequestHttpService.METODO_GET, false));
                }
            } else {
                String requestString = executarRequestGetNet(null, METODO_CUSTOMERS + "/" + pessoa.getIdGetNet(), ExecuteRequestHttpService.METODO_GET, false);
                incluirHistoricoRetornoTransacao(transacaoVO, requestString, "alterarPessoa");
                resposta = new JSONObject(requestString);
                if (resposta.has("errors") || resposta.optString("message").toLowerCase().contains("cliente não encontrado com id") ||
                        resposta.optString("message").toLowerCase().contains("cliente não encontrado com customer_id")) {
                    pessoa.setIdGetNet(null);

                    String requestString2 = executarRequestGetNet(pessoaJSON.toString(), METODO_CUSTOMERS, ExecuteRequestHttpService.METODO_POST, false);
                    incluirHistoricoRetornoTransacao(transacaoVO, requestString2, "incluirPessoaAposErroAlterar");
                    resposta = new JSONObject(requestString2);
                }
            }
            if (resposta.has("errors")) {
                throw new ConsistirException("Falha ao inserir a pessoa na GetNet. " + resposta.get("errors").toString());
            } else {
                pessoa.setIdGetNet(getIdGetNet(resposta));
                if (!UteisValidacao.emptyString(pessoa.getIdGetNet())) {
                    pessoaDAO.alterarIdGetNet(pessoa);
                } else {
                    throw new Exception(tratarMensagemGetNet(resposta.toString()));
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex.getMessage().toLowerCase().contains("cliente não encontrado com id")) {
                try {
                    Uteis.logar(null, "Não encontrou na Getnet vou limpar o idGetnet da pessoa IdGetnet " + pessoa.getIdGetNet());
                    pessoa.setIdGetNet("");
                    pessoaDAO.alterarIdGetNet(pessoa);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            throw new Exception(tratarMensagemGetNet(ex.getMessage()));
        } finally {
            pessoaDAO = null;
            empresaDAO = null;
        }
    }

    private String tratarMensagemGetNet(String resposta) {
        String mensagemTratada = "";
        try {
            JSONObject jsonMensagem = new JSONObject(resposta);
            mensagemTratada = jsonMensagem.optString("message");
            if (!UteisValidacao.emptyString(mensagemTratada)) {
                return mensagemTratada;
            }
            return resposta;
        } catch (Exception ex) {
            return resposta;
        }
    }

    private String getIdGetNet(JSONObject resposta) throws Exception {
        if (resposta.has("customer_id")) {
            return resposta.getString("customer_id");
        } else {
            throw new Exception(resposta.toString());
        }
    }

    private String executarRequestGetNet(String parametros, String metodo, String metodoHTTP, boolean cancelarMesmoDiaCobranca) throws Exception {
        validarDadosConvenio();

        String token = obterTokenAcesso();
        if (UteisValidacao.emptyString(token)) {
            throw new ConsistirException("Não foi possível obter Token Getnet.");
        }

        String path = this.urlAPI + metodo;
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", "Bearer " + token);

        if (cancelarMesmoDiaCobranca) {
            parametros = "{}";
        } else {
            header.put("seller_id", getSellerID());
        }

        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, header, metodoHTTP, "UTF-8");
    }

    private String executarRequestGetNetNovo(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        validarDadosConvenio();

        String token = obterTokenAcesso();
        if (UteisValidacao.emptyString(token)) {
            throw new ConsistirException("Não foi possível obter Token Getnet.");
        }

        String path = this.urlAPI + endPoint;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + token);
        headers.put("seller_id", getSellerID());

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        return respostaHttpDTO.getResponse();
    }

    private void validarDadosConvenio() throws Exception {
        if (this.convenioGetNet == null || UteisValidacao.emptyNumber(this.convenioGetNet.getCodigo())) {
            throw new Exception("Convênio de cobrança não encontrado ou inativo.");
        }
        if (UteisValidacao.emptyString(this.convenioGetNet.getCodigoAutenticacao01())) {
            throw new Exception(this.convenioGetNet.getLabelCodigoAutenticacao01() + " no convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyString(this.convenioGetNet.getCodigoAutenticacao02())) {
            throw new Exception(this.convenioGetNet.getLabelCodigoAutenticacao02() + " no convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyString(this.convenioGetNet.getCodigoAutenticacao03())) {
            throw new Exception(this.convenioGetNet.getLabelCodigoAutenticacao03() + " no convênio de cobrança não informado.");
        }
    }

    private JSONObject criarPessoaJSON(PessoaVO pessoa, String email, String celular, String telefone, EnderecoVO enderecoVO) throws JSONException {
        JSONObject pes = new JSONObject();
        pes.put("seller_id", getSellerID());
        pes.put("first_name", Uteis.retirarAcentuacaoRegex(Uteis.getPrimeiroNome(pessoa.getNome())));
        pes.put("last_name", Uteis.retirarAcentuacaoRegex(Uteis.getSobrenome(pessoa.getNome())));
        pes.put("customer_id", "P" + pessoa.getCodigo());

        String cpfCNPJ = Uteis.removerMascara(pessoa.getCfp());
        if (cpfCNPJ.length() > 11) {
            pes.put("document_type", "CNPJ");
        } else {
            pes.put("document_type", "CPF");
        }
        pes.put("document_number", cpfCNPJ);

        if (!UteisValidacao.emptyString(email)) {
            pes.put("email", email);
        }

        if (!UteisValidacao.emptyString(celular)) {
            pes.put("celphone_number", celular);
            //adicionar o celular como número..
            //caso ele tenha residencial será subistituido abaixo..
            pes.put("phone_number", celular);
        }

        if (!UteisValidacao.emptyString(telefone)) {
            pes.put("phone_number", telefone);
        }

        JSONObject address = new JSONObject();
        if (!UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            address.put("street", Uteis.retirarAcentuacaoRegex(enderecoVO.getEndereco()));
            address.put("number", enderecoVO.getNumero());
            address.put("complement", Uteis.retirarAcentuacaoRegex(enderecoVO.getComplemento()));
            address.put("district", Uteis.retirarAcentuacaoRegex(enderecoVO.getBairro()));
            address.put("city", Uteis.retirarAcentuacaoRegex(pessoa.getCidade().getNome()));
            address.put("state", pessoa.getEstadoVO().getSigla());
            address.put("country", pessoa.getCidade().getPais().getNome());
            address.put("postal_code", Uteis.tirarCaracteres(enderecoVO.getCep(), true));
        }
        pes.put("address", address);

        return pes;
    }

    private String obterTokenAcesso() throws Exception {
        boolean gerarNovoToken = convenioGetNet.getDataChaveAPI() == null || Calendario.maiorOuIgualComHora(Calendario.hoje(), Uteis.somarCampoData(convenioGetNet.getDataChaveAPI(), Calendar.HOUR, 0));
        Uteis.logarDebug("gerarNovoToken para requisição Getnet: " + gerarNovoToken);
        if (gerarNovoToken) {
            String path = this.urlAPI + METODO_TOKEN_API;
            Map<String, String> headers = new HashMap<String, String>();
            String auto = convenioGetNet.getCodigoAutenticacao01() + ":" + convenioGetNet.getCodigoAutenticacao02();
            headers.put("Authorization", "Basic " + new String(new Base64().encode(auto.getBytes())));
            headers.put("Content-type", "application/x-www-form-urlencoded");

            Uteis.logarDebug("gerando novo token Getnet: " + " | Path: " + path + " | Headers: " + headers.toString());
            String retorno = ExecuteRequestHttpService.executeHttpRequest(path, "scope=oob&grant_type=client_credentials", headers, ExecuteRequestHttpService.METODO_POST, "UTF-8");

            JSONObject jsonObject;
            try {
                jsonObject = new JSONObject(retorno);
            } catch (Exception ex) {
                Uteis.logarDebug("Erro token getnet | " + ex.getMessage() + " | Retorno: " + retorno);
                ex.printStackTrace();
                throw new Exception(retorno);
            }

            try {
                if (jsonObject.has("access_token")) {
                    String token = jsonObject.getString("access_token");
                    convenioGetNet.setChaveAPI(token);
                    convenioGetNet.setDataChaveAPI(Calendario.hoje());

                    ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(getCon());
                    convenioCobrancaDAO.alterarChaveAPIGetNet(convenioGetNet);
                    convenioCobrancaDAO = null;
                } else {
                    throw new Exception(retorno);
                }
            } catch (Exception e) {
                e.printStackTrace();
                if (jsonObject.optString("error_description").equalsIgnoreCase("The given client credentials were not valid")) {
                    throw new Exception("As credenciais fornecidas no convenio de cobrança não são válidas.");
                } else {
                    throw e;
                }
            }
        }

        if (UteisValidacao.emptyString(convenioGetNet.getChaveAPI())) {
            throw new Exception("Não foi possível obter o token de acesso a Getnet.");
        }

        return convenioGetNet.getChaveAPI();
    }

    public String getSellerID() {
        if (sellerID == null) {
            sellerID = "";
        }
        return sellerID;
    }

    public void setSellerID(String sellerID) {
        this.sellerID = sellerID;
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    private String obterTelefonePessoa(TipoTelefoneEnum tipoTelefoneEnum, PessoaVO pessoaVO) {
        for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
            if (UteisValidacao.validaTelefone(telefoneVO.getNumero()) && (tipoTelefoneEnum == null || tipoTelefoneEnum.getCodigo().equals(telefoneVO.getTipoTelefone()))) {
                return Uteis.tirarCaracteres(telefoneVO.getNumero(), true);
            }
        }
        return "";
    }

    private EnderecoVO obterEnderecoPessoa(PessoaVO pessoaVO) {
        for (EnderecoVO enderecoVO : pessoaVO.getEnderecoVOs()) {
            return enderecoVO;
        }
        return new EnderecoVO();
    }

    public List<ConvenioCobrancaVO> getListaConveniosGetnetOnline() {
        if (listaConveniosGetnetOnline == null) {
            listaConveniosGetnetOnline = new ArrayList<>();
        }
        return listaConveniosGetnetOnline;
    }

    public void setListaConveniosGetnetOnline(List<ConvenioCobrancaVO> listaConveniosGetnetOnline) {
        this.listaConveniosGetnetOnline = listaConveniosGetnetOnline;
    }

    public boolean isMesmoDia(Date data1, Date data2) {
        if (data1 == null || data2 == null) {
            return false;
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(data1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(data2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }


}

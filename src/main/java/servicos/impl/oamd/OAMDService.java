package servicos.impl.oamd;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.enumeradores.TipoConsultaCupomDescontoEnum;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ClienteValidacaoWS;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.plano.PlanoWS;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJdbcOAMD;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.basico.webservice.IntegracaoCadastros;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.oamd.CampanhaCupomDesconto;
import negocio.facade.jdbc.oamd.CampanhaCupomDescontoJSON;
import negocio.facade.jdbc.oamd.CampanhaCupomDescontoPremioPortador;
import negocio.facade.jdbc.oamd.CupomDesconto;
import negocio.facade.jdbc.oamd.EquivalenciaPlanoEmpresa;
import negocio.facade.jdbc.oamd.LoginSiteRedeEmpresa;
import negocio.facade.jdbc.oamd.RedeEmpresa;
import negocio.facade.jdbc.oamd.TransferenciaUnidade;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.oamd.CampanhaCupomDescontoInterfaceFacade;
import negocio.interfaces.oamd.CupomDescontoInterfaceFacade;
import negocio.interfaces.oamd.EquivalenciaPlanoEmpresaInterfaceFacade;
import negocio.interfaces.oamd.LoginSiteRedeEmpresaInterfaceFacade;
import negocio.interfaces.oamd.RedeEmpresaInterfaceFacade;
import negocio.interfaces.oamd.TransferenciaUnidadeInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelInterfaceFacade;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.CampanhaCupomDescontoPremioPortadorVO;
import negocio.oamd.CampanhaCupomDescontoVO;
import negocio.oamd.CupomDescontoVO;
import negocio.oamd.CupomDescontoWS;
import negocio.oamd.EmpresaFinanceiroVO;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;
import negocio.oamd.LoginSiteRedeEmpresaVO;
import negocio.oamd.RedeEmpresaVO;
import negocio.oamd.TransferenciaUnidadeVO;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.oamd.to.EmpresaFinanceiroOAMDTO;
import servicos.interfaces.oamd.OAMDServiceInterface;
import servicos.operacoes.CancelamentoContratoAutomaticoService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Created by ulisses on 26/05/2016.
 */
public class OAMDService extends SuperFacadeJdbcOAMD implements OAMDServiceInterface {


    private LoginSiteRedeEmpresaInterfaceFacade loginSiteRedeEmpresaDao;
    private RedeEmpresaInterfaceFacade redeEmpresaDao;
    private LogInterfaceFacade logDao;
    private TransferenciaUnidadeInterfaceFacade transferenciaUnidadeDao;
    private EquivalenciaPlanoEmpresaInterfaceFacade equivalenciaPlanoEmpresaDao;
    private CampanhaCupomDescontoInterfaceFacade campanhaCupomDescontoDAO;
    private CupomDescontoInterfaceFacade cupomDescontoDAO;

    public OAMDService() throws Exception {
        super(Conexao.obterConexaoBancoEmpresas());
    }

    public void incluirLoginSiteRedeEmpresa(String email, Integer idEmpresaFinanceiro, String cpf) throws Exception {
        try {
            RedeEmpresaVO redeEmpresaVO = getRedeEmpresaDao().consultarPorEmpresaFinanceiro(idEmpresaFinanceiro);
            if (redeEmpresaVO != null)
                getLoginSiteRedeEmpresaDao().registrarLoginRedeEmpresa(email, redeEmpresaVO, idEmpresaFinanceiro, cpf);
        } finally {
            getConOAMD().close();
        }
    }

    public EmpresaFinanceiroVO consultarEmpresaFinanceiro(String chaveZW) throws Exception {
        try {
            return getRedeEmpresaDao().consultarEmpresaFinanceiro(chaveZW);
        } finally {
            getConOAMD().close();
        }
    }

    public void cancelarUtilizacaoCupomDesconto(String numeroCupomDesconto, String chaveZW) throws Exception {
        CupomDesconto cupomDescontoDAO = new CupomDesconto();
        cupomDescontoDAO.cancelarUtilizacaoCupomDescontoOAMD(numeroCupomDesconto, chaveZW);
        cupomDescontoDAO = null;
    }

    public void informarContratoEstornadoHistoricoUtilizacaoCupom(int codContrato, String chaveZW) throws Exception {
        CupomDesconto cupomDescontoDAO = new CupomDesconto();
        cupomDescontoDAO.informarContratoEstornadoHistoricoUtilizacaoCupom(codContrato, chaveZW);
        cupomDescontoDAO = null;
    }

    public List<CampanhaCupomDescontoPremioPortadorVO> consultarPremiosPortadorCupomDesconto(Integer idCampanhaCupomDesconto, String descricaoPlano, String chaveZW) throws Exception {
        CampanhaCupomDescontoPremioPortador campanhaCupomDescontoPremioPortadorDAO = new CampanhaCupomDescontoPremioPortador();
        List<CampanhaCupomDescontoPremioPortadorVO> listPremio = campanhaCupomDescontoPremioPortadorDAO.consultarOAMD(idCampanhaCupomDesconto, descricaoPlano);
        campanhaCupomDescontoPremioPortadorDAO = null;
        return listPremio;
    }

    public void povoarLoginSiteRedeEmpresa(String chaveRede) throws Exception {
        try {
            RedeEmpresaVO redeEmpresaVO = getRedeEmpresaDao().consultarPorChaveRede(chaveRede);
            if (redeEmpresaVO != null) {
                List<EmpresaFinanceiroVO> listaEmpresa = getRedeEmpresaDao().consultarUnidadesDaRede(redeEmpresaVO);
                if ((listaEmpresa == null) || (listaEmpresa.size() <= 0)) {
                    throw new ConsistirException("Nenhuma empresa foi configurada para a chave da rede: " + chaveRede);
                }
                Map<String, Connection> mapaConexaoRede = getRedeEmpresaDao().obterConexoesRedeEmpresa(listaEmpresa.get(0).getChaveZW());
                UsuarioMovel usuarioMovelDao = new UsuarioMovel();
                for (EmpresaFinanceiroVO obj : listaEmpresa) {
                    Connection conEmp = mapaConexaoRede.get(obj.getChaveZW());
                    if (conEmp != null) {
                        usuarioMovelDao.setCon(conEmp);
                        List<UsuarioMovelVO> listaUserMovel = usuarioMovelDao.consultarTodosPorOrigem("API", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }

                }
            } else {
                throw new ConsistirException("Nenhuma rede de empresa encontrada para a chave: " + chaveRede);
            }

        } finally {
            getConOAMD().close();
        }

    }

    public List<EmpresaFinanceiroVO> consultarUnidadesDaRede(String chaveZW) throws Exception {
        try {
            RedeEmpresaVO redeEmpresaVO = getRedeEmpresaDao().consultarPorChaveZW(chaveZW);
            if (redeEmpresaVO != null)
                return getRedeEmpresaDao().consultarUnidadesDaRede(redeEmpresaVO);
        } finally {
            getConOAMD().close();
        }
        return null;
    }

    public List<HistoricoUtilizacaoCupomDescontoVO> consultarCupons(CampanhaCupomDescontoVO campanhaCupomDescontoVO, String chaveZWFiltro, Integer empresaZWFiltro, TipoConsultaCupomDescontoEnum tipoConsultaCupomDescontoEnum, String numeroCupom) throws Exception {
        return getCupomDescontoDAO().consultarHistoricoUtilizacaoCuponsOAMD(campanhaCupomDescontoVO, chaveZWFiltro, empresaZWFiltro, tipoConsultaCupomDescontoEnum, numeroCupom);
    }

    public ClienteValidacaoWS consultarClienteNaRedeEmpresa(String chaveZW, String cpf) throws Exception {
        try {
            return getRedeEmpresaDao().consultarClienteNaRedeEmpresa(chaveZW, cpf);
        } finally {
            getConOAMD().close();
        }
    }

    public Integer consultarTotalCupomDescontoJaUtilizado(Integer idCampanhaCupomDesconto, String chaveZW) throws Exception {
        return getCupomDescontoDAO().consultarTotalCupomDescontoJaUtilizadoOAMD(idCampanhaCupomDesconto);
    }

    public CampanhaCupomDescontoVO incluirCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, String chaveZW, LogInterfaceFacade logFacade) throws Exception {
        if (campanhaCupomDescontoVO.getQuantidadeCupomGerar() > 15000) {
            throw new ConsistirException("Operação não permitida. A quantidade máxima de cupons por lote é de 15000");
        }

        CampanhaCupomDescontoVO.validarDados(campanhaCupomDescontoVO);
        CampanhaCupomDesconto campanhaCupomDescontoDAO = new CampanhaCupomDesconto();
        CampanhaCupomDescontoVO campanhaReturn = campanhaCupomDescontoDAO.incluirOAMD(campanhaCupomDescontoVO, usuarioVO, logFacade);
        incluirLogInclusaoCampanhaCupomDesconto(campanhaReturn, usuarioVO, chaveZW);
        return campanhaReturn;
    }

    public ContratoVO transferirAlunoDeUnidade(int codigoEmpresaFinanceiro, String chaveZWDestino, int codigoCliente, int codigoPlano, String email) throws Exception {
        DAO dao = new DAO();
        EmpresaFinanceiroVO empresaFinanceiroVO = getRedeEmpresaDao().consultarEmpresaFinanceiro(codigoEmpresaFinanceiro);
        if (empresaFinanceiroVO == null) {
            throw new ConsistirException("Operação não realizada, chave de origem não encontrada.");
        }
        String chaveZWOrigem = empresaFinanceiroVO.getChaveZW();
        Connection conOrigem = dao.obterConexaoEspecifica(chaveZWOrigem);
        Connection conDestino = dao.obterConexaoEspecifica(chaveZWDestino);
        SituacaoClienteSinteticoDW sinteticoOrigemDao = new SituacaoClienteSinteticoDW(conOrigem);
        Contrato contratoDao = new Contrato(conOrigem);
        UsuarioMovelInterfaceFacade usuarioMovelOrigemDao = new UsuarioMovel(conOrigem);
        ContratoVO contratoVO = new ContratoVO();
        try {
            RedeEmpresaVO redeEmpresaOrigem = getRedeEmpresaDao().consultarPorChaveZW(chaveZWOrigem);
            if (redeEmpresaOrigem == null) {
                throw new ConsistirException("Operação não realizada, a chave de origem " + chaveZWOrigem + " não foi configurada a nenhuma rede de empresa.");
            }
            RedeEmpresaVO redeEmpresaDestino = getRedeEmpresaDao().consultarPorChaveZW(chaveZWDestino);
            if (redeEmpresaDestino == null) {
                throw new ConsistirException("Operação não realizada, a chave de destino " + chaveZWDestino + " não foi configurada a nenhuma rede de empresa.");
            }
            if (!redeEmpresaOrigem.getId().equals(redeEmpresaDestino.getId())) {
                throw new ConsistirException("Operação não realizada, a chave de origem e destino pertencem a rede de empresas distintas.");
            }
            if (chaveZWOrigem.equals(chaveZWDestino)) {
                throw new ConsistirException("Operação não realizada, a chave de origem e destino devem ser distintas.");
            }
            UsuarioMovelVO usuarioMovelOrigem = usuarioMovelOrigemDao.consultarPorNome(email, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            String senhaUsuarioMovelOrigem = (usuarioMovelOrigem != null) ? usuarioMovelOrigem.getSenha() : null;

            SituacaoClienteSinteticoDWVO sinteticoOrigemVO = sinteticoOrigemDao.consultarCliente(codigoCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if ((sinteticoOrigemVO.getCpf() == null) || (sinteticoOrigemVO.getCpf().trim().equals(""))) {
                throw new ConsistirException("Operação não realizada, aluno sem CPF cadastrado.");
            }
            TransferenciaUnidadeVO transferenciaUnidadeVO = new TransferenciaUnidadeVO();
            transferenciaUnidadeVO.setCpf(sinteticoOrigemVO.getCpf());
            transferenciaUnidadeVO.setNomeAluno(sinteticoOrigemVO.getNomeCliente());
            transferenciaUnidadeVO.setContratoOrigem(sinteticoOrigemVO.getCodigoContrato());
            transferenciaUnidadeVO.setChaveOrigem(chaveZWOrigem);
            transferenciaUnidadeVO.setUnidadeOrigem(getRedeEmpresaDao().consultarNomeEmpresaDaRede(chaveZWOrigem));
            transferenciaUnidadeVO.setRedeEmpresaVO(redeEmpresaOrigem);
            ContratoVO contratoOrigem = contratoDao.consultarPorCodigo(sinteticoOrigemVO.getCodigoContrato(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(contratoOrigem, "getCodigo()")) {
                throw new ConsistirException("Operação não realizada, aluno não possui contrato ativo na unidade de origem.");
            }
            TransferenciaUnidadeVO ultimaTransferencia = getTransferenciaUnidadeDao().consultarUltimaTransferenciaUnidade(transferenciaUnidadeVO.getRedeEmpresaVO().getChaverede(), transferenciaUnidadeVO.getCpf());
            if ((ultimaTransferencia != null) && (ultimaTransferencia.getChaveDestino() != null) &&
                    (ultimaTransferencia.getChaveDestino().equals(chaveZWDestino))) {
                throw new ConsistirException("A transferência de unidade já foi realizada, faça o login no site.");
            }
            // cancelar o contrato na unidade de origem.
            Conexao.guardarConexaoForJ2SE(chaveZWOrigem, conOrigem);
            MovProdutoVO movProdutoAnuidade = FacadeManager.getFacade().getMovProduto().consultaMovProdutoEmAberto(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA, transferenciaUnidadeVO.getContratoOrigem(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            transferenciaUnidadeVO.setIncluirAnuidade(movProdutoAnuidade != null);
            cancelarContratoUnidadeOrigem(chaveZWOrigem, conOrigem, contratoOrigem, transferenciaUnidadeVO, ultimaTransferencia);

            // incluir novo contrato na unidade de destino
            Conexao.guardarConexaoForJ2SE(chaveZWDestino, conDestino);
            contratoVO = incluirContratoUnidadeDestino(redeEmpresaDestino.getId(), chaveZWDestino, conOrigem, conDestino, transferenciaUnidadeVO, codigoPlano, senhaUsuarioMovelOrigem, email);

        } finally {
            dao = null;
            sinteticoOrigemDao = null;
            usuarioMovelOrigemDao = null;
            contratoDao = null;
            conOrigem.close();
            conDestino.close();
            getConOAMD().close();
        }
        return contratoVO;

    }

    private void cancelarContratoUnidadeOrigem(String chaveOrigem, Connection conOrigem, ContratoVO contratoVO, TransferenciaUnidadeVO transferenciaUnidadeVO, TransferenciaUnidadeVO ultimaTransferencia) throws Exception {

        MovParcela movParcelaDao = new MovParcela(conOrigem);
        CancelamentoContratoAutomaticoService cancelamentoAutomatico = new CancelamentoContratoAutomaticoService(conOrigem);
        try {
            boolean cancelarContrato = true;
            if (ultimaTransferencia != null) {
                if ((ultimaTransferencia.getChaveOrigem().equals(chaveOrigem)) && (ultimaTransferencia.getChaveDestino() == null)) {
                    // neste caso pode ter ocorrido de na primeira tentativa de transferencia, o sistema concluiu o cancelamento do contrato
                    // na unidade de origem mas não conseguiu incluir o novo contrato na unidade de destino.
                    cancelarContrato = false;
                    transferenciaUnidadeVO.setId(ultimaTransferencia.getId());
                } else {
                    if (contratoVO.getSituacao().equals("CA")) {
                        throw new ConsistirException("Operação não realizada, o contrato está cancelado na unidade de origem.");
                    }
                }
            } else {
                if (contratoVO.getSituacao().equals("CA")) {
                    throw new ConsistirException("Operação não realizada, o contrato está cancelado na unidade de origem.");
                }
            }

            if (cancelarContrato) {
                if (movParcelaDao.parcelaEmAberto(contratoVO.getPessoa().getCodigo(), Calendario.hoje())) {
                    throw new ConsistirException("Operação não realizada, existe parcela com vencimento menor ou igual ao mês atual que ainda não foi paga.");
                }
                cancelamentoAutomatico.cancelarAutomatico(contratoVO, TipoParcelaCancelamento.MAIOR_IGUAL_MES_ATUAL, false, true, true);
                getTransferenciaUnidadeDao().incluir(transferenciaUnidadeVO);

            }
        } finally {
            cancelamentoAutomatico = null;
            movParcelaDao = null;
        }

    }

    private ContratoVO incluirContratoUnidadeDestino(Integer redeEmpresaId, String chaveDestino, Connection conOrigem, Connection conDestino,
                                                     TransferenciaUnidadeVO transferenciaUnidadeVO,
                                                     Integer codigoPlano, String senhaUsuarioMovelEmpresaOrigem, String emailCliente) throws Exception {

        Pessoa pessoaDestinoDao = new Pessoa(conDestino);
        Cliente clienteOrigemDao = new Cliente(conOrigem);
        Cliente clienteDestinoDao = new Cliente(conDestino);
        Pessoa pessoaOrigemDao = new Pessoa(conOrigem);
        IntegracaoCadastros integracaoCadastroDao = new IntegracaoCadastros(conDestino);
        Empresa empresaDestinoDao = new Empresa(conDestino);
        Contrato contratoDestinoDao = new Contrato(conDestino);
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteOrigemDao = new AutorizacaoCobrancaCliente(conOrigem);
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDestinoDao = new AutorizacaoCobrancaCliente(conDestino);
        ConvenioCobranca convenioCobrancaDestinoDao = new ConvenioCobranca(conDestino);
        ContratoVO contratoDestino = new ContratoVO();
        try {
            Integer idEmpresaFinanceiro = getRedeEmpresaDao().consultarIdEmpresaFinanceiro(redeEmpresaId, chaveDestino);
            if (idEmpresaFinanceiro == null) {
                throw new ConsistirException("Operação não permitida. A empresa destino não está associada a nenhuma rede de empresa.");
            }

            PessoaVO pessoaDestino = pessoaDestinoDao.consultarPorCPF(transferenciaUnidadeVO.getCpf(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ClienteVO clienteVO = null;
            boolean incluirAutorizacaoCobranca = false;
            PessoaVO pessoaOrigem = pessoaOrigemDao.consultarPorCPF(transferenciaUnidadeVO.getCpf(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (pessoaDestino == null) {
                if (pessoaOrigem == null) {
                    throw new ConsistirException("Operação não permitida, não foi encontrada nenhuma pessoa na unidade de origem com o cpf: " + transferenciaUnidadeVO.getCpf());
                }
                incluirAutorizacaoCobranca = true;
                String email = "";
                EnderecoVO enderecoVO = new EnderecoVO();
                enderecoVO.setEndereco("");
                enderecoVO.setComplemento("");
                enderecoVO.setNumero("");
                enderecoVO.setBairro("");
                enderecoVO.setCep("");
                String telCelular = "";
                String telResidencial = "";
                Integer codigoCidade = pessoaOrigem.getCidade().getCodigo();
                Integer codigoEstado = pessoaOrigem.getEstadoVO().getCodigo();
                List<EmpresaVO> listaEmpresa = empresaDestinoDao.consultarEmpresas();
                Integer empresa = listaEmpresa.get(0).getCodigo();

                if ((pessoaOrigem.getEmailVOs() != null) && (pessoaOrigem.getEmailVOs().size() > 0)) {
                    email = pessoaOrigem.getEmailVOs().get(0).getEmail();
                }
                if ((pessoaOrigem.getEnderecoVOs() != null) && (pessoaOrigem.getEnderecoVOs().size() > 0)) {
                    enderecoVO = pessoaOrigem.getEnderecoVOs().get(0);
                }
                if ((pessoaOrigem.getTelefoneVOs() != null) && (pessoaOrigem.getTelefoneVOs().size() > 0)) {
                    for (TelefoneVO telefoneVO : pessoaOrigem.getTelefoneVOs()) {
                        if (telefoneVO.getTipoTelefone().equals("CE")) {
                            if (telCelular.equals(""))
                                telCelular = telefoneVO.getNumero();
                        } else {
                            if (telResidencial.equals(""))
                                telResidencial = telefoneVO.getNumero();
                        }
                        if ((!telCelular.equals("")) && (!telResidencial.equals(""))) {
                            break;
                        }
                    }
                }
                String retorno = integracaoCadastroDao.persistirClienteSite(pessoaOrigem.getNome(), pessoaOrigem.getCfp(), email, pessoaOrigem.getSexo(),
                        pessoaOrigem.getDataNasc_Apresentar(), enderecoVO.getEndereco(),
                        enderecoVO.getComplemento(), enderecoVO.getNumero(), enderecoVO.getBairro(),
                        enderecoVO.getCep(), telCelular, telResidencial, "123456", empresa,
                        codigoCidade, codigoEstado, idEmpresaFinanceiro, senhaUsuarioMovelEmpresaOrigem, null, null);
                if (retorno.contains("ERRO")) {
                    throw new ConsistirException(retorno);
                }
                pessoaDestino = pessoaDestinoDao.consultarPorCPF(transferenciaUnidadeVO.getCpf(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            } else {
                clienteVO = clienteDestinoDao.consultarPorCodigoPessoa(pessoaDestino.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                UsuarioMovelInterfaceFacade usuarioMovelDestinoDao = new UsuarioMovel(conDestino);
                UsuarioMovelVO usuarioMovelVO = usuarioMovelDestinoDao.consultarPorNome(emailCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                if ((usuarioMovelVO != null) && (senhaUsuarioMovelEmpresaOrigem != null)) {
                    usuarioMovelDestinoDao.alterarSenhaJaEncriptada(usuarioMovelVO.getCodigo(), senhaUsuarioMovelEmpresaOrigem);
                } else {
                    List<EmpresaVO> listaEmpresa = empresaDestinoDao.consultarEmpresas();
                    EmpresaVO empresaVO = empresaDestinoDao.consultarPorChavePrimaria(listaEmpresa.get(0).getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    Random generator = new Random();
                    String senha = String.valueOf(generator.nextInt(900000));
                    integracaoCadastroDao.incluirUsuarioMovel(null, empresaVO, clienteVO, emailCliente, senha, pessoaDestino.getNome(), null, idEmpresaFinanceiro, null, true);
                }
            }
            if (clienteVO == null) {
                clienteVO = clienteDestinoDao.consultarPorCodigoPessoa(pessoaDestino.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }
            if (!UtilReflection.objetoMaiorQueZero(clienteVO, "getCodigo()")) {
                throw new ConsistirException("Operação não permitida, não foi encontrado nenhum cliente na unidade de destino com o cpf: " + transferenciaUnidadeVO.getCpf());
            }
            contratoDestino = contratoDestinoDao.gravarContratoSite(chaveDestino, codigoPlano, clienteVO.getCodigo(), 1, 1, null, false, transferenciaUnidadeVO.isIncluirAnuidade(), 0);

            // incluir autorização de cobrança DCC.
            incluirAutorizacaoCobrancaDCC(incluirAutorizacaoCobranca, clienteOrigemDao, clienteDestinoDao, pessoaOrigem, autorizacaoCobrancaClienteOrigemDao, clienteVO, convenioCobrancaDestinoDao, autorizacaoCobrancaClienteDestinoDao);

            transferenciaUnidadeVO.setChaveDestino(chaveDestino);
            transferenciaUnidadeVO.setUnidadeDestino(getRedeEmpresaDao().consultarNomeEmpresaDaRede(chaveDestino));
            transferenciaUnidadeVO.setContratoDestino(contratoDestino.getCodigo());
            getTransferenciaUnidadeDao().alterar(transferenciaUnidadeVO);
        } catch (Exception e) {
            getTransferenciaUnidadeDao().gravarMensagemErro(transferenciaUnidadeVO.getId(), e.getMessage());
            throw e;
        } finally {
            pessoaDestinoDao = null;
            clienteOrigemDao = null;
            clienteDestinoDao = null;
            integracaoCadastroDao = null;
            pessoaOrigemDao = null;
            contratoDestinoDao = null;
            autorizacaoCobrancaClienteOrigemDao = null;
            autorizacaoCobrancaClienteDestinoDao = null;
            convenioCobrancaDestinoDao = null;

        }
        return contratoDestino;
    }

    private void incluirAutorizacaoCobrancaDCC(boolean incluirAutorizacaoCobranca, Cliente clienteOrigemDao, Cliente clienteDestinoDao,
                                               PessoaVO pessoaOrigem, AutorizacaoCobrancaCliente autorizacaoCobrancaClienteOrigemDao,
                                               ClienteVO clienteDestinoVO, ConvenioCobranca convenioCobrancaDestinoDao,
                                               AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDestinoDao) throws Exception {

        List<ConvenioCobrancaVO> listaConvenio = convenioCobrancaDestinoDao.consultarPorTipos(new TipoConvenioCobrancaEnum[]{TipoConvenioCobrancaEnum.DCC}, 0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        ConvenioCobrancaVO convenioCobrancaDestino = null;
        if ((listaConvenio != null) && (listaConvenio.size() > 0)) {
            convenioCobrancaDestino = listaConvenio.get(0);
        } else {
            throw new ConsistirException("Operação não permitida, não foi configurado nenhum convênio de cobrança para a unidade de destino.");
        }
        ClienteVO clienteOrigemVO = clienteOrigemDao.consultarPorCodigoPessoa(pessoaOrigem.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<AutorizacaoCobrancaClienteVO> listaAutorizacaoCobrancaOrigem = autorizacaoCobrancaClienteOrigemDao.consultarPorCliente(clienteOrigemVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        AutorizacaoCobrancaClienteVO autorizacaoCobrancaOrigem = null;
        for (AutorizacaoCobrancaClienteVO obj : listaAutorizacaoCobrancaOrigem) {
            if ((obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) && (obj.getConvenio().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.DCC))) {
                autorizacaoCobrancaOrigem = obj;
                break;
            }
        }
        if (autorizacaoCobrancaOrigem != null) {

            List<AutorizacaoCobrancaClienteVO> listaAutorizacaoCobrancaDestino = autorizacaoCobrancaClienteDestinoDao.consultarPorCliente(clienteDestinoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            AutorizacaoCobrancaClienteVO autorizacaoCobrancaDestino = null;
            for (AutorizacaoCobrancaClienteVO obj : listaAutorizacaoCobrancaDestino) {
                if ((obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) && (obj.getConvenio().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.DCC))) {
                    autorizacaoCobrancaDestino = obj;
                    break;
                }
            }
            if (autorizacaoCobrancaDestino == null)
                incluirAutorizacaoCobranca = true;

            if (incluirAutorizacaoCobranca) {
                autorizacaoCobrancaOrigem.setNovoObj(false);
                autorizacaoCobrancaOrigem.setCodigo(0);
                autorizacaoCobrancaOrigem.setCliente(clienteDestinoVO);
                autorizacaoCobrancaOrigem.setConvenio(convenioCobrancaDestino);
                autorizacaoCobrancaClienteDestinoDao.incluir(autorizacaoCobrancaOrigem);
            } else {
                // transformar a autorização de cobrança de origem na autorização de cobrança destino.
                autorizacaoCobrancaOrigem.setCodigo(autorizacaoCobrancaDestino.getCodigo());
                autorizacaoCobrancaOrigem.setCliente(clienteDestinoVO);
                autorizacaoCobrancaOrigem.setConvenio(convenioCobrancaDestino);
                autorizacaoCobrancaOrigem.setValidarAutorizacaoCobrancaSemelhante(false);
                autorizacaoCobrancaClienteDestinoDao.alterar(autorizacaoCobrancaOrigem);
            }
        }

    }

    private void incluirLogInclusaoCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, String chaveZW) throws Exception {
        try {
            campanhaCupomDescontoVO.setObjetoVOAntesAlteracao(new CampanhaCupomDescontoVO());
            campanhaCupomDescontoVO.setNovoObj(true);
            registrarLogObjetoVO(campanhaCupomDescontoVO, campanhaCupomDescontoVO.getId(), "CAMPANHACUPOMDESCONTO", 0);
            registrarLogListPremioPortador(campanhaCupomDescontoVO);
            campanhaCupomDescontoVO.setNovoObj(false);
            campanhaCupomDescontoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            campanhaCupomDescontoVO.setNovoObj(false);
            registrarLogErroObjetoVOZW("CAMPANHACUPOMDESCONTO", campanhaCupomDescontoVO.getId(), "ERRO AO GERAR LOG DE INCLUSÃO DE CAMPANHACUPOMDESCONTO", usuarioVO.getNome(), usuarioVO.getUserOamd(), chaveZW);
            e.printStackTrace();
        }
    }

    private void incluirLogAlteracaoCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, String chaveZW) throws Exception {
        try {
            campanhaCupomDescontoVO.setNovoObj(false);
            registrarLogObjetoVO(campanhaCupomDescontoVO, campanhaCupomDescontoVO.getId(), "CAMPANHACUPOMDESCONTO", 0);
            registrarLogListPremioPortador(campanhaCupomDescontoVO);
            campanhaCupomDescontoVO.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO("CAMPANHACUPOMDESCONTO", campanhaCupomDescontoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CAMPANHACUPOMDESCONTO", usuarioVO.getNome(), usuarioVO.getUserOamd());
            e.printStackTrace();
        }
    }

    private void registrarLogListPremioPortador(CampanhaCupomDescontoVO campanhaCupomDescontoVO) throws Exception {
        List<CampanhaCupomDescontoPremioPortadorVO> premiosPortador1 = campanhaCupomDescontoVO.getListaPremioPortador();
        List<CampanhaCupomDescontoPremioPortadorVO> premiosPortador2 = ((CampanhaCupomDescontoVO)campanhaCupomDescontoVO.getObjetoVOAntesAlteracao()).getListaPremioPortador();

        List<CampanhaCupomDescontoPremioPortadorVO> premiosPortadorExcluidos = diferencaEntreListasPremio(premiosPortador2, premiosPortador1);
        List<CampanhaCupomDescontoPremioPortadorVO> premiosPortadorAdicionados = diferencaEntreListasPremio(premiosPortador1, premiosPortador2);

        for(CampanhaCupomDescontoPremioPortadorVO itemExcluido : premiosPortadorExcluidos) {
            LogVO log = new LogVO();
            log.setChavePrimaria(campanhaCupomDescontoVO.getId().toString());
            log.setNomeEntidade("CAMPANHACUPOMDESCONTO");
            log.setNomeEntidadeDescricao("CAMPANHACUPOMDESCONTO - PRÊMIOS AO PORTADOR DO CUPOM");
            log.setNomeCampo("listaPremioPortador");
            log.setValorCampoAnterior(itemExcluido.toString());
            log.setValorCampoAlterado("");
            log.setPessoa(0);
            log.setOperacao("EXCLUSÃO");
            getLogDao().incluirSemCommit(log);
        }

        for(CampanhaCupomDescontoPremioPortadorVO itemAdicionado : premiosPortadorAdicionados) {
            LogVO log = new LogVO();
            log.setChavePrimaria(campanhaCupomDescontoVO.getId().toString());
            log.setNomeEntidade("CAMPANHACUPOMDESCONTO");
            log.setNomeEntidadeDescricao("CAMPANHACUPOMDESCONTO - PRÊMIOS AO PORTADOR DO CUPOM");
            log.setNomeCampo("listaPremioPortador");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado(itemAdicionado.toString());
            log.setPessoa(0);
            log.setOperacao("INCLUSÃO");
            getLogDao().incluirSemCommit(log);
        }
    }

    private List<CampanhaCupomDescontoPremioPortadorVO> diferencaEntreListasPremio(List<CampanhaCupomDescontoPremioPortadorVO> listaPremio1, List<CampanhaCupomDescontoPremioPortadorVO> listaPremio2) {
        List<CampanhaCupomDescontoPremioPortadorVO> listaRetorno = new ArrayList();
        for(CampanhaCupomDescontoPremioPortadorVO premio1 : listaPremio1){
            boolean isContem = false;
            for(CampanhaCupomDescontoPremioPortadorVO premio2 : listaPremio2){
                if (premio1.getTipoPremio().toUpperCase().equals(premio2.getTipoPremio().toUpperCase()) &&
                        premio1.getDescricaoPremio().toUpperCase().equals(premio2.getDescricaoPremio().toUpperCase()) &&
                        premio1.getDescricaoPlano().toUpperCase().equals(premio2.getDescricaoPlano().toUpperCase()) &&
                        premio1.getPercentualDesconto() == premio2.getPercentualDesconto() &&
                        premio1.getValorDesconto() == premio2.getValorDesconto()) {
                    isContem = true;
                    break;
                }
            }

            if(!isContem) {
                listaRetorno.add(premio1);
            }
        }
        return listaRetorno;
    }

    private void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            getLogDao().incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void registrarLogErroObjetoVOZW(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd, String chaveZW) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            Log logDAO = new Log(new DAO().obterConexaoEspecifica(chaveZW));
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List consultarPorNomeCodigoEntidadeAgrupado(String nomeEntidade, Integer codigoEntidade, Date dataInicio, Date dataFim, Integer codigoPessoa, int nivelMontarDados, boolean agruparSegundo) throws Exception {
        try {
            return getLogDao().consultarPorNomeCodigoEntidadeAgrupado(nomeEntidade, codigoEntidade, dataInicio, dataFim, codigoPessoa, nivelMontarDados, agruparSegundo);
        } finally {
            getConOAMD().close();
        }

    }

    private void registrarLogObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        List lista = ObjetoVO.gerarLogAlteracaoObjetoVO(true);
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            LogVO log = (LogVO) i.next();
            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            getLogDao().incluirSemCommit(log);
        }
    }

    private void registrarLogObjetoVO(LogVO logVO, int codPessoa) throws Exception {
        if (logVO != null) {
            logVO.setPessoa(codPessoa);
            getLogDao().incluirSemCommit(logVO);
        }
    }

    private void registrarLogObjetoVOZWLIST(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa, String chaveZW) throws Exception {
        List lista = ObjetoVO.gerarLogAlteracaoObjetoVO();
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            LogVO log = (LogVO) i.next();
            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            Log logDAO = new Log(new DAO().obterConexaoEspecifica(chaveZW));
            logDAO.incluirSemCommit(log);
            logDAO = null;
        }
    }

    private void registrarLogObjetoVOZW(LogVO logVO, int codPessoa, String chaveZW) throws Exception {
        if (logVO != null) {
            logVO.setPessoa(codPessoa);
            Log logDAO = new Log(new DAO().obterConexaoEspecifica(chaveZW));
            logDAO.incluirSemCommit(logVO);
            logDAO = null;
        }
    }

    public String montarCampanhaCupomDescontoSite(String numeroCupom, List<CampanhaCupomDescontoPremioPortadorVO> listaPremios) {
        JSONObject json = new JSONObject();
        json.put("numeroCupom", numeroCupom);
        JSONArray arrayPremioPortador = new JSONArray();
        if (listaPremios != null) {
            for (CampanhaCupomDescontoPremioPortadorVO ccdpp : listaPremios) {
                arrayPremioPortador.put(new JSONObject(ccdpp.toSITE()));
            }
        }
        json.put("listaPremios", arrayPremioPortador.length() == 0 ? null : arrayPremioPortador);
        return json.toString();
    }


    public CampanhaCupomDescontoVO consultarCampanhaCupomDescontoPorId(Integer id, int nivelMontarDados) throws Exception {
        CampanhaCupomDescontoVO campanhaCupomDescontoVO = getCampanhaCupomDescontoDAO().consultarPorId(id);
        campanhaCupomDescontoVO.setTotalCupomUtilizado(getCupomDescontoDAO().consultarTotalCupomDescontoJaUtilizadoOAMD(campanhaCupomDescontoVO.getId()));
        return campanhaCupomDescontoVO;
    }

    public void concederPremioCupomPortadorCupom(Connection conZillyon, String chaveZW, ContratoVO contratoVO, String numeroCupom, Integer codigoCliente, String nomeCliente, Double valorPremioProdutos) throws Exception {
        getCupomDescontoDAO().concederPremioCupomPortadorCupom(conZillyon, chaveZW, contratoVO, numeroCupom, codigoCliente, nomeCliente, valorPremioProdutos);
    }

    public CampanhaCupomDescontoVO concederPremioCupomDescontoAoAluno(CampanhaCupomDescontoVO campanhaCupomDescontoVO, Date dataBaseVencimentoMatricula, List<CupomDescontoVO> listaCupomDesconto, UsuarioVO usuarioVO, Integer codigoFavorecido) throws Exception {
        String resultado = getCupomDescontoDAO().concederPremioCupomDescontoAoAluno(this, campanhaCupomDescontoVO, listaCupomDesconto, dataBaseVencimentoMatricula, usuarioVO, codigoFavorecido);
        CampanhaCupomDescontoVO objCampanhaCupomVO = consultarCampanhaCupomDescontoPorId(campanhaCupomDescontoVO.getId(), Uteis.NIVELMONTARDADOS_TODOS);
        objCampanhaCupomVO.setMsgResultadoProcessamento(resultado);
        return objCampanhaCupomVO;
    }

    public CupomDescontoVO consultarPorNumeroCupom(String numeroCupom, Integer codigoFavorecido) throws Exception {
        try {
            return getCupomDescontoDAO().consultarPorNumeroCupomOAMD(numeroCupom, codigoFavorecido);
        } catch (Exception e) {
            return null;
        }
    }

    public boolean existeCampanhaCupomDesconto(Integer codigoFavorecido, boolean somenteVigente, String descricaoPlano) throws Exception {
        return getCampanhaCupomDescontoDAO().existeCampanhaCupomDescontoOAMD(codigoFavorecido, somenteVigente, descricaoPlano);
    }

    public CupomDescontoVO validarConcederPremioCupomPortadorCupom(Connection conZillyon, Integer codigoFavorecido, String numeroCupom, ContratoVO contratoPremioPortadorCupom) throws Exception {
       return validarConcederPremioCupomPortadorCupom(conZillyon, codigoFavorecido, numeroCupom, contratoPremioPortadorCupom, "");
    }

    public CupomDescontoVO validarConcederPremioCupomPortadorCupom(Connection conZillyon, Integer codigoFavorecido, String numeroCupom, ContratoVO contratoPremioPortadorCupom, String nomePlano) throws Exception {
        CupomDescontoVO cupomDescontoVO = getCupomDescontoDAO().validarCupomPortadorCupomOAMD(codigoFavorecido, numeroCupom, nomePlano);
        if ((cupomDescontoVO.getMsgValidacao().equals("")) && (UtilReflection.objetoMaiorQueZero(contratoPremioPortadorCupom, "getCodigo()"))) {
            concederPremioCupomPortadorCupom(conZillyon, contratoPremioPortadorCupom.getChave(), contratoPremioPortadorCupom, contratoPremioPortadorCupom.getNumeroCupomDesconto(), contratoPremioPortadorCupom.getCliente().getCodigo(), contratoPremioPortadorCupom.getCliente().getPessoa().getNome(), contratoPremioPortadorCupom.getPremioProdutosCupomDesconto());
        }
        return cupomDescontoVO;
    }

    public CampanhaCupomDescontoVO alterarCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO, UsuarioVO usuarioVO, String chaveZW, LogInterfaceFacade logFacade) throws Exception {
        CampanhaCupomDescontoVO.validarDados(campanhaCupomDescontoVO);
        CampanhaCupomDescontoVO campanhaReturn = getCampanhaCupomDescontoDAO().alterarOAMD(campanhaCupomDescontoVO, usuarioVO, logFacade);
        incluirLogAlteracaoCampanhaCupomDesconto(campanhaReturn, usuarioVO, chaveZW);
        return campanhaReturn;
    }

    public List<CupomDescontoWS> consultarCupomDesconto(String key, String listaCupom) throws Exception {
        RedeEmpresaVO redeEmpresaVO = getRedeEmpresaDao().consultarPorChaveZWOAMD(key);
        if (redeEmpresaVO != null) {
            return getCupomDescontoDAO().consultarCupomDescontoOAMD(redeEmpresaVO.getChaverede(), listaCupom);
        }
        return new ArrayList<>();
    }

    public void excluirCampanhaCupomDesconto(CampanhaCupomDescontoVO campanhaCupomDescontoVO) throws Exception {
        Integer totalCupomJaUtilizado = getCupomDescontoDAO().consultarTotalCupomDescontoJaUtilizadoOAMD(campanhaCupomDescontoVO.getId());
        if (totalCupomJaUtilizado > 0) {
            if (totalCupomJaUtilizado == 1) {
                throw new ConsistirException("Operação não permitida. 1(um) Cupom já foi validado.");
            } else {
                throw new ConsistirException("Operação não permitida. " + totalCupomJaUtilizado + " Cupons já foram validados.");
            }
        } else {
            getCampanhaCupomDescontoDAO().excluirOAMD(campanhaCupomDescontoVO);
        }
    }

    public List<CampanhaCupomDescontoVO> consultarCampanhaCupomDescontoParaImpressao(String filtro, String ordem, String campoOrdenacao, String chaveZW) throws Exception {
        try {
            RedeEmpresaVO redeEmpresaVO = getRedeEmpresaDao().consultarPorChaveZWOAMD(chaveZW);
            return getCampanhaCupomDescontoDAO().consultarParaImpressaoOAMD(filtro, ordem, campoOrdenacao, redeEmpresaVO.getChaverede());
        } catch (Exception e) {
            e.getStackTrace();
            Uteis.logar(null, e.getMessage());
            return new ArrayList<>();
        }
    }

    public String consultarCampanhaCupomDescontoJSON(RedeEmpresaVO redeEmpresaVO, Integer idFavorecido) throws Exception {
        JSONObject objReturn = new JSONObject();
        try {
            String chaveRede = null;
            if (redeEmpresaVO != null) {
                chaveRede = redeEmpresaVO.getChaverede();
            }

            JSONArray aaData = new JSONArray();

            List<CampanhaCupomDescontoJSON> result = getCampanhaCupomDescontoDAO().consultarCampanhaCupomDescontoJSONOAMD(chaveRede, idFavorecido, false);
            for (CampanhaCupomDescontoJSON ccd :result) {
                JSONArray itemArray = new JSONArray();
                itemArray.put(ccd.getId());
                itemArray.put(ccd.getDescricaoCampanha());
                itemArray.put(ccd.getVigenciaInicial());
                itemArray.put(ccd.getVigenciaFinal());
                aaData.put(itemArray);
            }

            objReturn.put("aaData", aaData);
            return objReturn.toString();
        } catch (Exception e) {
            e.getStackTrace();
            Uteis.logar(null, e.getMessage());
            JSONArray aaData = new JSONArray();
            objReturn.put("aaData", aaData);
            return objReturn.toString();
        }


    }

    public RedeEmpresaVO consultarRede(String keyEmpresa) throws Exception {
        return getRedeEmpresaDao().consultarPorChaveZW(keyEmpresa);
    }

    public PlanoWS consultarEquivalenciaPlanoUnidadeDestino(int codigoPlanoOrigem, int codigoEmpresaFinanceiroOrigem, String chaveDestino, Connection conDestino) throws Exception {
        try {
            return getEquivalenciaPlanoEmpresaDao().consultarEquivalenciaPlanoUnidadeDestino(codigoPlanoOrigem, codigoEmpresaFinanceiroOrigem, chaveDestino, conDestino);
        } finally {
            getConOAMD().close();
        }
    }


    public RedeEmpresaVO consultarRedeEmpresaPorChaveZW(String chaveZW) throws Exception {
        try {
            return getRedeEmpresaDao().consultarPorChaveZW(chaveZW);
        } finally {
            getConOAMD().close();
        }
    }

    public boolean atualizarInfosPixWebhookOAMD(String chaveZW, int codigoPixWebhookOamdAtualizar, String dataFinalizouProcessamentoZW, int codPixWebhookZW) throws Exception {
        String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/pixWebhook/" + chaveZW + "/" + codigoPixWebhookOamdAtualizar + "/atualizar";
        try {
            //Params
            Map<String, String> params = new HashMap<String, String>();
            params.put("dataFinalizouProcessamentoZW", dataFinalizouProcessamentoZW); //yyyy-MM-dd
            params.put("codPixWebhookZW", String.valueOf(codPixWebhookZW)); //yyyy-MM-dd

            String response = ExecuteRequestHttpService.executeRequest(url, params);
            if (response.contains("ok")) {
                return true;
            }
        } catch (Exception ex) {
        }
        return false;
    }

    public CampanhaCupomDescontoVO gerarNovoLoteCupomDesconto(UsuarioVO usuarioVO, CampanhaCupomDescontoVO campanhaCupomDescontoVO,
                                                              Integer totalCupomGerar, String observacaoLoteCupom, String nomeCupomEspecifico, String chaveZW, LogInterfaceFacade logFacade) throws Exception {
        if (totalCupomGerar > 15000) {
            throw new ConsistirException("Operação não permitida. A quantidade máxima de cupons por lote é de 15000");
        }
        int loteGerar = campanhaCupomDescontoVO.getTotalLote() + 1;
        getCampanhaCupomDescontoDAO().gerarNovoLoteCupomDescontoOAMD(campanhaCupomDescontoVO, totalCupomGerar);
        campanhaCupomDescontoVO.getListaCupom().addAll(getCupomDescontoDAO().incluirLoteCupomOAMD(campanhaCupomDescontoVO, loteGerar, totalCupomGerar, nomeCupomEspecifico));
        registrarLogGeracaoLoteDescontoManualmente(usuarioVO, campanhaCupomDescontoVO.getId(), loteGerar, totalCupomGerar, observacaoLoteCupom, chaveZW);
        campanhaCupomDescontoVO.setQuantidadeCupomExtra(campanhaCupomDescontoVO.getQuantidadeCupomGerar() < campanhaCupomDescontoVO.getListaCupom().size() ? campanhaCupomDescontoVO.getListaCupom().size() - campanhaCupomDescontoVO.getQuantidadeCupomGerar() : 0);
        campanhaCupomDescontoVO.setTotalLote(campanhaCupomDescontoVO.getTotalLote() + 1);
        return getCampanhaCupomDescontoDAO().alterarOAMD(campanhaCupomDescontoVO, usuarioVO, logFacade);
    }

    public String gerarNovoCupomDescontoIndicacao(CampanhaCupomDescontoVO campanhaCupomDescontoVO, String chaveZW, UsuarioVO usuarioVO, LogInterfaceFacade logFacade) throws Exception {
        int loteGerar = campanhaCupomDescontoVO.getTotalLote() + 1;
        getCampanhaCupomDescontoDAO().gerarNovoLoteCupomDescontoOAMD(campanhaCupomDescontoVO, 1);
        List<CupomDescontoVO> cupomDescontoVOS = getCupomDescontoDAO().incluirLoteCupomOAMD(campanhaCupomDescontoVO, loteGerar, 1, "");
        String cupomDesconto = cupomDescontoVOS.isEmpty() ? "" : cupomDescontoVOS.get(0).getNumeroCupom();
        campanhaCupomDescontoVO.getListaCupom().addAll(cupomDescontoVOS);
        registrarLogGeracaoLoteDescontoManualmente(usuarioVO, campanhaCupomDescontoVO.getId(), loteGerar, 1, "Indicação", chaveZW);
        campanhaCupomDescontoVO.setQuantidadeCupomExtra(campanhaCupomDescontoVO.getQuantidadeCupomGerar() < campanhaCupomDescontoVO.getListaCupom().size() ? campanhaCupomDescontoVO.getListaCupom().size() - campanhaCupomDescontoVO.getQuantidadeCupomGerar() : 0);
        campanhaCupomDescontoVO.setTotalLote(campanhaCupomDescontoVO.getTotalLote() + 1);
        getCampanhaCupomDescontoDAO().alterarOAMD(campanhaCupomDescontoVO, usuarioVO, logFacade);
        return cupomDesconto;
    }

    private void registrarLogGeracaoLoteDescontoManualmente(UsuarioVO usuarioVO, Integer idCampanhaCupomDesconto, Integer numeroLote,
                                                            Integer quantidadeCupomGerar, String observacaoLoteCupom, String chaveZW) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(idCampanhaCupomDesconto.toString());
        obj.setNomeEntidade("CAMPANHACUPOMDESCONTO");
        obj.setNomeEntidadeDescricao("CAMPANHA CUPOM DESCONTO");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        StringBuilder msg = new StringBuilder();
        obj.setOperacao("ALTERAÇÃO");
        msg.append("Adicionado novo lote de cupons à campanha. \n");
        msg.append("Número Lote: ").append(numeroLote).append(" \n");
        msg.append("Total de cupons extra gerados:").append(quantidadeCupomGerar).append("\n");
        msg.append("Observações:").append(observacaoLoteCupom).append("\n");
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVOZW(obj, idCampanhaCupomDesconto, chaveZW);
    }

    public void registrarLogProcessamentoCupomDescontoManualmente(UsuarioVO usuarioVO, Integer idCampanhaCupomDesconto, Date dataBaseVencimentoMatricula, String resultadoProcessamento) throws Exception {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(idCampanhaCupomDesconto.toString());
        obj.setNomeEntidade("CAMPANHACUPOMDESCONTO");
        obj.setNomeEntidadeDescricao("CAMPANHA CUPOM DESCONTO");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setUserOAMD(usuarioVO.getUserOamd());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        StringBuilder msg = new StringBuilder();
        obj.setOperacao("ALTERAÇÃO");
        msg.append("Processado arquivo de Cupom Desconto para isenção de mensalidade. \n");
        msg.append("Isentar mensalidades com vencimento maior ou igual a: ").append(Uteis.getData(dataBaseVencimentoMatricula)).append(" \n");
        msg.append("Resultado do processamento:").append(resultadoProcessamento).append("\n");
        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
        registrarLogObjetoVO(obj, idCampanhaCupomDesconto);
    }


    public LoginSiteRedeEmpresaVO consultarChaveDeEmailDaRede(String chaveRede, String email, boolean buscarPorCPF) throws Exception {
        try {
            return getLoginSiteRedeEmpresaDao().consultar(chaveRede, email, buscarPorCPF);
        } finally {
            getConOAMD().close();
        }

    }

    public LoginSiteRedeEmpresaInterfaceFacade getLoginSiteRedeEmpresaDao() throws Exception {
        if (this.loginSiteRedeEmpresaDao == null)
            this.loginSiteRedeEmpresaDao = new LoginSiteRedeEmpresa(getConOAMD());
        this.loginSiteRedeEmpresaDao.prepararConexao();
        return loginSiteRedeEmpresaDao;
    }

    public static RedeEmpresaVO consultarRedeEmpresa(final String key) {
        try {
            final String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/empresaFinanceiro/consultarRede?key=" + key;
            final String response = ExecuteRequestHttpService.executeRequestGet(url, new HashMap<>());
            JSONObject resposta = new JSONObject(response).optJSONObject("return");
            if (resposta == null) {
                return null;
            }
            return JSONMapper.getObject(resposta, RedeEmpresaVO.class);
        } catch (Exception ex) {
            Uteis.logar(ex, LoginControle.class);
        }
        return null;
    }

    public static List<String> consultarChavesRedeEmpresaByChaveRede(final String chaveRede) {
        try {
            List<String> chaves = new ArrayList<>();
            final String url = PropsService.getPropertyValue(PropsService.urlOamd) + "/prest/infra/hV0dU9aJ8dY3oE4qL2fD6jI0jF7fD2uQ?chaveRede=" + chaveRede;
            final String response = ExecuteRequestHttpService.executeRequestCupomDesconto(url, "", new HashMap<>());
            JSONArray resposta = new JSONObject(response).optJSONArray("stringList");
            if (resposta == null || resposta.length() == 0) {
                return null;
            }
            for (int i = 0; i < resposta.length(); i++) {
                chaves.add(resposta.getString(i));
            }
            return chaves;
        } catch (Exception ex) {
            Uteis.logar(ex, OAMDService.class);
        }
        return null;
    }

    public void setLoginSiteRedeEmpresaDao(LoginSiteRedeEmpresaInterfaceFacade loginSiteRedeEmpresaDao) {
        this.loginSiteRedeEmpresaDao = loginSiteRedeEmpresaDao;
    }

    public RedeEmpresaInterfaceFacade getRedeEmpresaDao() throws Exception {
        if (this.redeEmpresaDao == null)
            this.redeEmpresaDao = new RedeEmpresa(getConOAMD());
        this.redeEmpresaDao.prepararConexao();

        return redeEmpresaDao;
    }

    public void setRedeEmpresaDao(RedeEmpresaInterfaceFacade redeEmpresaDao) {
        this.redeEmpresaDao = redeEmpresaDao;
    }

    public LogInterfaceFacade getLogDao() throws Exception {
        if (this.logDao == null)
            this.logDao = new Log(getConOAMD());
        this.logDao.prepararConexao();
        return logDao;
    }

    public void setLogDao(LogInterfaceFacade logDao) {
        this.logDao = logDao;
    }

    public TransferenciaUnidadeInterfaceFacade getTransferenciaUnidadeDao() throws Exception {
        if (this.transferenciaUnidadeDao == null)
            this.transferenciaUnidadeDao = new TransferenciaUnidade(getConOAMD());
        this.transferenciaUnidadeDao.prepararConexao();

        return transferenciaUnidadeDao;
    }

    public void setTransferenciaUnidadeDao(TransferenciaUnidadeInterfaceFacade transferenciaUnidadeDao) {
        this.transferenciaUnidadeDao = transferenciaUnidadeDao;
    }

    public EquivalenciaPlanoEmpresaInterfaceFacade getEquivalenciaPlanoEmpresaDao() throws Exception {
        if (this.equivalenciaPlanoEmpresaDao == null)
            this.equivalenciaPlanoEmpresaDao = new EquivalenciaPlanoEmpresa(getConOAMD());
        this.equivalenciaPlanoEmpresaDao.prepararConexao();

        return equivalenciaPlanoEmpresaDao;
    }

    public void setEquivalenciaPlanoEmpresaDao(EquivalenciaPlanoEmpresaInterfaceFacade equivalenciaPlanoEmpresaDao) {
        this.equivalenciaPlanoEmpresaDao = equivalenciaPlanoEmpresaDao;
    }

    public List<EmpresaFinanceiroOAMDTO> consultarUnidadesDaRedeOAMD(RedeEmpresaVO redeEmpresaVO) throws Exception {
        return getRedeEmpresaDao().consultarUnidadesDaRedeOAMD(redeEmpresaVO);
    }

    public CupomDescontoVO validarCupomPortadorCupom(Integer codigoFavorecido, String numeroCupom, String nomePlano) throws Exception {
        return getCupomDescontoDAO().validarCupomPortadorCupomOAMD(codigoFavorecido, numeroCupom, nomePlano);
    }

    public CampanhaCupomDescontoInterfaceFacade getCampanhaCupomDescontoDAO() {
        if (campanhaCupomDescontoDAO == null) {
            campanhaCupomDescontoDAO = new CampanhaCupomDesconto();
        }
        return campanhaCupomDescontoDAO;
    }

    public CupomDescontoInterfaceFacade getCupomDescontoDAO() {
        if (cupomDescontoDAO == null) {
            cupomDescontoDAO = new CupomDesconto();
        }
        return cupomDescontoDAO;
    }
}

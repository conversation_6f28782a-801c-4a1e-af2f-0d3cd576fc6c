package servicos.impl.onepayment;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.OnePaymentServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 08/07/2022
 */
public class OnePaymentService extends AbstractCobrancaOnlineServiceComum implements OnePaymentServiceInterface {

    private String URL_API_ONE_PAYMENT = "";
    private String URL_API_ONE_PAYMENT_QUERY = "";
    private String privateSecretKey = "";

    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public OnePaymentService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            // A url é uma só para produção e sandbox, o que muda é o secret key do convênio e eles vão saber de lá se é produção ou teste.
            this.URL_API_ONE_PAYMENT = PropsService.getPropertyValue(PropsService.urlApiOnePayment);
            this.URL_API_ONE_PAYMENT_QUERY = PropsService.getPropertyValue(PropsService.urlApiOnePaymentQuery);
        }
        this.privateSecretKey = this.convenioCobrancaVO.getCodigoAutenticacao01();
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoOnePaymentVO(), TipoTransacaoEnum.ONE_PAYMENT, this.convenioCobrancaVO);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            Map<String, String> parametrosPagamento = montarParametrosPagamento(transacao, dadosCartao);

            RespostaHttpDTO retorno = executarRequestOnePayment(null, parametrosPagamento, MetodoHttpEnum.POST, false);
            processarRetorno(transacao, retorno.getResponse());

            // para exibir a bandeira nos detalhes da transação
            parametrosPagamento.put("bandeira", dadosCartao.getBand().getDescricao());
            transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento.toString()));
            transacaoDAO.alterar(transacao);

            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    @Override
    public RespostaHttpDTO consultarTransacao(String codigoExterno) throws Exception {
        return null;
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para One Payment");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
//        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        if (isCancelada(transacaoVO.getResultadoCancelamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
        } else {
            realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        }
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        try {
            if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
                Map<String, String> paramsConsulta = montarParametrosConsulta(transacaoVO);
                RespostaHttpDTO retorno = executarRequestOnePayment(null, paramsConsulta, MetodoHttpEnum.POST, true);
                //retorno do consultar transação é em XML, converter para JSON:
                JSONObject json = XML.toJSONObject(retorno.getResponse()).getJSONObject("nm_response");
                String status = json.getJSONArray("transaction").getJSONObject(0).optString("condition");
                processarRetornoConsultarTransacao(transacaoVO, status, json);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        Map<String, String> paramsRefund = montarObjetoRefund(transacaoVO);
        RespostaHttpDTO retorno = executarRequestOnePayment(null, paramsRefund, MetodoHttpEnum.POST, false);
        processarRetornoCancelamento(transacaoVO, retorno.getResponse());
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
        JSONObject retornoJSON = converterRetornoStringToJson(retornoCancelamento);

        transacaoVO.setResultadoCancelamento(retornoJSON.toString());

        try {
            if (retornoJSON.optString("response") != null && retornoJSON.optString("response").equals("1")
                    && (retornoJSON.optString("responsetext").equalsIgnoreCase("SUCCESS")
                    || retornoJSON.optString("responsetext").equalsIgnoreCase("APPROVAL")
                    || retornoJSON.optString("responsetext").equalsIgnoreCase("APPROVED"))) {
                int resultCode = retornoJSON.getInt("response_code");
                if (resultCode == 100) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                    transacaoVO.setDataHoraCancelamento(Calendario.hoje());
                }
            } else {
                consultarSituacaoCobrancaTransacao(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) throws Exception {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");

        JSONObject retornoJSON = converterRetornoStringToJson(retorno);
        //Response: 1 = Transaction Approved, 2 = Transaction Declined, 3 = Error in transaction data or system error

        if (retornoJSON.optString("response") != null && retornoJSON.optString("response").equals("1")) {
            if (!UteisValidacao.emptyString(retornoJSON.optString("authcode"))) {
                transacao.setCodigoAutorizacao(retornoJSON.optString("authcode"));
            }
            if (!UteisValidacao.emptyString(retornoJSON.optString("transactionid"))) {
                transacao.setCodigoExterno(retornoJSON.optString("transactionid"));
            }
        }

        transacao.setParamsResposta(retornoJSON.toString());
        transacao.setBandeiraPagamento(null);

        int statusCode = retornoJSON.optInt("response");
        String message = retornoJSON.optString("responsetext");
        if (statusCode == 1 && (message.equalsIgnoreCase("SUCCESS") || message.equalsIgnoreCase("APPROVAL") || message.equalsIgnoreCase("APPROVED"))) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        } else if (statusCode == 3) {
            if (message.contains("Duplicate transaction")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private void processarRetornoConsultarTransacao(TransacaoVO transacao, String status, JSONObject jsonRetorno) throws Exception {

        if (UteisValidacao.emptyString(transacao.getParamsResposta())) {
            transacao.setParamsResposta(jsonRetorno.toString());
        }

        if (isCancelada(transacao.getResultadoCancelamento()) &&
                (status.equalsIgnoreCase("pendingsettlement") || status.equalsIgnoreCase("complete"))) {
            transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
        } else if (status.equalsIgnoreCase("complete") && status.equalsIgnoreCase("pendingsettlement")
                && !isCancelada(transacao.getResultadoCancelamento())) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        } else if (status.equalsIgnoreCase("failed")) {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private JSONObject converterRetornoStringToJson(String retorno) {
        //A resposta vem toda descaracterizada em String, necessário montar um Json final com os parâmetros que vieram na resposta
        //Utilizado no retorno do tentarAprovação e também no método de cancelamento da transação
        JSONArray lista = new JSONArray(retorno.split("&"));
        JSONObject jsonRetorno = new JSONObject();

        for (int e = 0; e < lista.length(); e++) {
            Object obj = lista.get(e);
            String[] arrayString = obj.toString().split("=");

            String valor = "";
            try {
                valor = arrayString[1].replace("=", "");
            } catch (Exception ex) {

            }
            //Só adiciona no objeto Json se tiver algum valor, caso contrário ignora
            if (!UteisValidacao.emptyString(valor)) {
                jsonRetorno.put(arrayString[0], valor);
            }
        }
        return jsonRetorno;
    }

    private JSONObject converterParamsEnvioStringToJson(String retorno) {
        //Necessário montar um Json final com os parâmetros que foram criados para envio
        JSONArray lista = new JSONArray(retorno.split(","));
        JSONObject jsonRetorno = new JSONObject();

        for (int e = 0; e < lista.length(); e++) {
            Object obj = lista.get(e);
            String[] arrayString = obj.toString().replace("{", "").replace(" ", "").split("=");

            String valor = "";
            try {
                valor = arrayString[1].replace("=", "").replace("}", "");
            } catch (Exception ex) {

            }
            jsonRetorno.put(arrayString[0], valor);
        }
        return jsonRetorno;
    }

    private Map<String, String> montarParametrosPagamento(TransacaoVO transacaoVO, CartaoCreditoTO cartao) {
        try {
            String identificador = "TRA" + transacaoVO.getCodigo();
            String amount = String.valueOf(transacaoVO.getValor());
            String numeroCartao = "";
            if (!UteisValidacao.emptyString(cartao.getNumero())) {
                numeroCartao = cartao.getNumero();
            }

            //Na requisição One Payment tudo é passado como parâmetro junto com a URL
            Map<String, String> params = new HashMap<>();
            params.put("type", "sale");
            params.put("security_key", this.privateSecretKey);
            params.put("ccnumber", numeroCartao);
            params.put("ccexp", cartao.getValidadeMMYY(true));
            params.put("amount", amount);
            params.put("currency", transacaoVO.getConvenioCobrancaVO().getCurrencyConvenioEnum().getDescricao());
            if (!UteisValidacao.emptyString(cartao.getCodigoSeguranca())) {
                params.put("cvv", cartao.getCodigoSeguranca());
            }
            params.put("orderid", identificador);

            //caso seja parcelamento
            if (cartao.getParcelas() > 1) {
                params.put("billing_method", "installment");
                params.put("billing_number", cartao.getParcelas().toString());
                params.put("billing_total", transacaoVO.getValor().toString());
            }

            return params;
        } catch (Exception e) {
            return new HashMap<String, String>();
        }
    }

    private Map<String, String> montarObjetoRefund(TransacaoVO transacaoVO) throws Exception {
        Map<String, String> params = new HashMap<>();

        //para usar os dados do convênio de cobrança do momento da transação criada, pois o convênio pode ter sido alterado.
        String privateSecurityKey = obterPrivateSecurityKeyTransacao(transacaoVO);

        if (!UteisValidacao.emptyString(privateSecurityKey)) {
            params.put("type", "refund");
            params.put("security_key", privateSecurityKey);
            params.put("transactionid", transacaoVO.getCodigoExterno());
        }
        return params;
    }

    private Map<String, String> montarParametrosConsulta(TransacaoVO transacaoVO) throws Exception {
        Map<String, String> params = new HashMap<>();
        String privateSecurityKey = obterPrivateSecurityKeyTransacao(transacaoVO);

        if (!UteisValidacao.emptyString(privateSecurityKey)) {
            params.put("security_key", privateSecurityKey);
            params.put("transactionid", transacaoVO.getCodigoExterno());
        }
        return params;
    }

    private RespostaHttpDTO executarRequestOnePayment(String body, Map<String, String> params, MetodoHttpEnum metodoHttpEnum, boolean consulta) throws Exception {
        //Request é montado somente de parâmetros, a url sempre é a mesma, não tem header nem body.
        String path = "";
        if (consulta) {
            path = this.URL_API_ONE_PAYMENT_QUERY;
        } else
            path = this.URL_API_ONE_PAYMENT;

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, null, params, body, metodoHttpEnum);
        return respostaHttpDTO;
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }

    private String encriptarDadosSigilososEnvio(String parametrosPagamento) {
        JSONObject parametrosPgto = converterParamsEnvioStringToJson(parametrosPagamento);
        String creditCard = parametrosPgto.optString("ccnumber");
        parametrosPgto.put("ccnumber", APF.getCartaoMascarado(creditCard));
        if (parametrosPgto.has("cvv")) {
            parametrosPgto.put("cvv", "***");
        }
        return parametrosPgto.toString();
    }

    private String obterPrivateSecurityKeyTransacao(TransacaoVO transacaoVO) {
        try {
            String privateSecurityKey = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao01);
            if (!UteisValidacao.emptyString(privateSecurityKey)) {
                return privateSecurityKey;
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    private boolean isCancelada(String resultadoCancelamento) {
        try {
            if (resultadoCancelamento.contains("Approval") && resultadoCancelamento.contains("refund")) {
                //já cancelada
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
    }
}

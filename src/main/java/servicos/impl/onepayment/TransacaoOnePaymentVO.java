package servicos.impl.onepayment;


import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created by Estulano on 08/07/2022.
 */

public class TransacaoOnePaymentVO extends TransacaoVO {

    @Override
    public String getValorCartaoMascarado() {
        return getCartaoMascarado();
    }

    @Override
    public String getResultadoRequisicao() {

        //Response: 1 = Transaction Approved, 2 = Transaction Declined, 3 = Error in transaction data or system error
        try {
            //Em caso de cancelamento
            if (isCancelada()) {
                return "Transaction was canceled";
            }
        } catch (Exception ex) {
        }

        try {
            //Em caso de autorização sucesso
            JSONObject json = new JSONObject(getParamsResposta());
            if (json.has("response") && json.optString("response").equals("1")) {
                String message = OnePaymentRetornoEnum.Status100.getDescricao();
                if (!UteisValidacao.emptyString(message)) {
                    return message;
                }
            }
        } catch (Exception ignored) {
        }

        try {
            //Em caso de autorização negada
            String code = obterValorParamsResposta("response_code");
            OnePaymentRetornoEnum onePaymentRetornoEnum = OnePaymentRetornoEnum.valueOff(code);
            if (!onePaymentRetornoEnum.equals(OnePaymentRetornoEnum.StatusNENHUM)) {
                return onePaymentRetornoEnum.getDescricao() + " " + obterValorParamsResposta("responsetext");
            } else {
                return "Erro desconhecido";
            }
        } catch (Exception ex) {
            return "Erro desconhecido";
        }
    }

    @Override
    public String getAutorizacao() {
        try {
            JSONObject resposta = new JSONObject(getParamsResposta());
            return resposta.optString("authcode");
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getCartaoMascarado() {
        String card_number = obterValorParamsEnvio("ccnumber");
        if (!UteisValidacao.emptyString(card_number)) {
            return card_number;
        }

        try {
            String card = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
            return APF.getCartaoMascarado(card);
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao) && !UteisValidacao.emptyString(getCodigoExterno())) {
            return getCodigoExterno();
        }
        if (nomeAtributo.equals(APF.CodigoAutorizacao)) {
            return getAutorizacao();
        }
        if (nomeAtributo.equals(APF.CartaoMascarado)) {
            return getCartaoMascarado();
        }
        return valor;
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) {
        return getResultadoRequisicaoCancelamento();
    }

    @Override
    public String getCodErroExterno() {
        return obterValorParamsResposta("response_code");
    }


    private String getResultadoRequisicaoCancelamento() {
        return "Não foi possível realizar o cancelamento. Tente novamente mais tarde.";
    }

    private String obterValorParamsResposta(String key) {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            return retornoJSON.optString(key);
        } catch (Exception ex) {
            return "";
        }
    }

    private String obterValorParamsEnvio(String key) {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsEnvio());
            return retornoJSON.optString(key);
        } catch (Exception ex) {
            return "";
        }
    }

    private boolean isCancelada() {
        try {
            String cancelamento = getResultadoCancelamento();
            if (cancelamento.contains("SUCCESS") ||
                    ((cancelamento.contains("Approval") || cancelamento.contains("Approved")) && cancelamento.contains("refund"))) {
                //já cancelada
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            return false;
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            return obj.getInt("billing_number");
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public String getTID() {
        try {
            if (getTipo().equals(TipoTransacaoEnum.ONE_PAYMENT)) {
                JSONObject obj = new JSONObject(getParamsResposta());
                return obj.optString("transactionid");
            }
            return "";
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getBandeira() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            String bandeira = obj.optString("bandeira");
            if (bandeira.equals("Mastercard")){
                return "MASTER";
            } else {
                return bandeira.toUpperCase();
            }
        } catch (Exception ex) {
            return "";
        }
    }
}

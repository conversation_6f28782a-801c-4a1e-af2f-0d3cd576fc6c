package servicos.impl.onepayment;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/*
 * Created by <PERSON> on 08/07/2022.
 */

public enum OnePaymentRetornoEnum {

    //Para exibir o motivo de uma transação negada deverá ser usado o valor do campo "response_code" dos paramsresposta.

    StatusNENHUM("NENHUM", ""),
    Status100("100", "Transaction was approved.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status200("200", "Transaction was declined by processor."),
    Status201("201", "Do not honor."),
    Status202("202", "Insufficient funds.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Status203("203", "Over limit."),
    Status204("204", "Transaction not allowed."),
    Status220("220", "Incorrect payment information."),
    Status221("221", "No such card issuer."),
    Status222("222", "No card number on file with issuer."),
    Status223("223", "Expired card.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status224("224", "Invalid expiration date.", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status225("225", "Invalid card security code.", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status226("226", "Invalid PIN."),
    Status240("240", "Call issuer for further information."),
    Status250("250", "Pick up card."),
    Status251("251", "Lost card.", CodigoRetornoPactoEnum.CARTAO_CANCELADO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status252("252", "Stolen card.", CodigoRetornoPactoEnum.CARTAO_CANCELADO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status253("253", "Fraudulent card.", CodigoRetornoPactoEnum.CARTAO_CANCELADO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status260("260", "Declined with further instructions available. (See response text)."),
    Status261("261", "Declined-Stop all recurring payments."),
    Status262("262", "Declined-Stop this recurring program."),
    Status263("263", "Declined-Update cardholder data available."),
    Status264("264", "Declined-Retry in a few days."),
    Status300("300", "Transaction was rejected by gateway."),
    Status400("400", "Transaction error returned by processor."),
    Status410("410", "Invalid merchant configuration."),
    Status411("411", "Merchant account is inactive."),
    Status420("420", "Communication error."),
    Status421("421", "Communication error with issuer."),
    Status430("430", "Duplicate transaction at processor."),
    Status440("440", "Processor format error."),
    Status441("441", "Invalid transaction information."),
    Status460("460", "Processor feature not available."),
    Status461("461", "Unsupported card type.\n"),


    ;

    private String codigo;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private OnePaymentRetornoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private OnePaymentRetornoEnum(String codigo, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }


    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static OnePaymentRetornoEnum valueOff(String id) {
        for (OnePaymentRetornoEnum onePaymentRetornoEnumStatusEnum : OnePaymentRetornoEnum.values()) {
            if (onePaymentRetornoEnumStatusEnum.getCodigo().equals(id)) {
                return onePaymentRetornoEnumStatusEnum;
            }
        }
        return StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }

}

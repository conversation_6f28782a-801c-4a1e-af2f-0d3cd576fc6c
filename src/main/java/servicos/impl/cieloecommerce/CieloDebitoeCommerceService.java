package servicos.impl.cieloecommerce;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import org.json.JSONException;
import org.json.JSONObject;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.interfaces.CieloDebitoeCommerceServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/*
 * Created by Luiz Felipe on 31/08/2017.
 */
public class CieloDebitoeCommerceService extends CobrancaOnlineService implements CieloDebitoeCommerceServiceInterface {

    private String urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCieloRequisicaoProducao);
    private String urlAPIConsulta = PropsService.getPropertyValue(PropsService.urlApiCieloConsultaProducao);
    private Pessoa pessoa;
    private Log log;
    private Transacao transacao;
    private FormaPagamentoVO formaPagamentoVO;
    private FormaPagamento formaPagamento;
    private String merchantId;
    private String merchantKey;
    private UsuarioVO usuarioLogado;
    private String urlReturn;
    private MovPagamentoVO movPagamentoVO;

    public CieloDebitoeCommerceService(Connection con, UsuarioVO usuarioLogado, MovPagamentoVO movPagamentoVO, String returnURL) throws Exception {
        super(con);
        this.transacao = new Transacao(con);
        this.formaPagamento = new FormaPagamento(con);
        this.usuarioLogado = usuarioLogado;
        this.movPagamentoVO = movPagamentoVO;
        this.urlReturn = returnURL;
        verificarFormaPagamento();
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.formaPagamentoVO != null) {
            this.merchantId = this.formaPagamentoVO.getMerchantid();
            this.merchantKey = this.formaPagamentoVO.getMerchantkey();
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = criarTransacao(dadosCartao);
        PessoaVO pessoa = getPessoa().consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        JSONObject parametrosPagamento = criarParametrosPagamento(dadosCartao, pessoa);
        transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
        transacao.setCodigo(0);
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        new Transacao(con).incluir(transacao);
        String retorno = executarRequestCieloRequisicao(parametrosPagamento.toString(), "/1/sales/", ExecuteRequestHttpService.METODO_POST);
        processarRetornoDebito(transacao, retorno);
//        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
//            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
//                consultarPaymentIdPeloMerchantOrderId(transacao);
//            }
//            consultarSituacaoTransacao(transacao);
//        }
        new Transacao(con).alterar(transacao);
        return transacao;
    }

    private TransacaoVO criarTransacao(CartaoCreditoTO dadosCartao) {
        TransacaoVO transacao = new TransacaoCieloVO();
        transacao.setUrlTransiente(dadosCartao.getUrl());
        transacao.setUsuarioResponsavel(dadosCartao.getUsuarioResponsavel());
        transacao.setNomePessoa(dadosCartao.getNomeTitular());
        transacao.setDataProcessamento(Calendario.hoje());
        transacao.setTipo(TipoTransacaoEnum.CIELO_DEBITO_ONLINE);
        transacao.setEmpresa(dadosCartao.getEmpresa());
        transacao.setListaParcelas(dadosCartao.getListaParcelas());
        transacao.setValor(Uteis.arredondarForcando2CasasDecimais(dadosCartao.getValor()));

        //SEMPRE SALVAR O TOKEN DO CARTAO UTILIZADO!! by Luiz Felipe
        transacao.setTokenAragorn(dadosCartao.getTokenAragorn());

        if (dadosCartao.getIdPessoaCartao() != null && !UteisValidacao.emptyNumber(dadosCartao.getIdPessoaCartao())) {
            PessoaVO pessoa = new PessoaVO();
            pessoa.setCodigo(dadosCartao.getIdPessoaCartao());
            transacao.setPessoaPagador(pessoa);
        }
        return transacao;
    }

    private JSONObject criarParametrosPagamento(CartaoCreditoTO dadosCartao, PessoaVO pessoa) throws Exception {
        JSONObject pagamento = new JSONObject();
        pagamento.put("MerchantOrderId", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss"));
        JSONObject pessoaJson = new JSONObject();
        pessoaJson.put("Name", Uteis.retirarAcentuacao(pessoa.getNome()));
        pagamento.put("Customer", pessoaJson);

        if (!UteisValidacao.emptyString(dadosCartao.getNumero())) {
            pagamento.put("Payment", criarParametrosCartao(dadosCartao));
        }
        return pagamento;
    }

    private JSONObject criarParametrosCartao(CartaoCreditoTO cartaoCreditoTO) throws JSONException {
        JSONObject dadosPagamento = new JSONObject();

        dadosPagamento.put("Type", "DebitCard");
        dadosPagamento.put("Amount", StringUtilities.formatarCampoMonetario(cartaoCreditoTO.getValor(), 15));
        dadosPagamento.put("ReturnUrl", this.getUrlReturn());
//        dadosPagamento.put("SoftDescriptor", StringUtilities.formatarCampoEmBranco("", 13));

        JSONObject dadosCartao = new JSONObject();
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            dadosCartao.put("CardNumber", cartaoCreditoTO.getNumero());
        }
        dadosCartao.put("Holder", cartaoCreditoTO.getNomeTitular());
        dadosCartao.put("ExpirationDate", cartaoCreditoTO.getValidadeMMYYYY(true));
//        dadosCartao.put("SecurityCode", cartaoCreditoTO.getCodigoSeguranca());
        dadosCartao.put("Brand", cartaoCreditoTO.getBand().getDescricaoCielo());
        dadosPagamento.put("DebitCard", dadosCartao);

        return dadosPagamento;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) throws Exception {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        JSONObject payment = parametrosPagamento.getJSONObject("Payment");
        JSONObject creditCard = payment.getJSONObject("DebitCard");
        creditCard.put("CardNumber", APF.getCartaoMascarado(creditCard.getString("CardNumber")));
        creditCard.remove("SecurityCode");
        return parametrosPagamento.toString();
    }

    @Override
    public String consultarTransacao(TransacaoVO transacaoVO) throws Exception {
        return executarRequestCieloConsulta(null, "/1/sales/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacao;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacao, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        consultarSituacaoTransacaoDebito(transacao);
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.PENDENTE)) {
            confirmarTransacao(transacao, null, null);
        }
        return transacao;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        transacaoOriginal.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoNova, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
//        transacaoNova.setDataProcessamento(Calendario.hoje());
//        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
//        new Transacao(con).incluir(transacaoNova);
//        JSONObject parametrosEnviar = decifrarDadosSigilososReEnvio(transacaoNova);
//        String retorno = executarRequestCieloRequisicao(parametrosEnviar.toString(), "/1/sales", ExecuteRequestHttpService.METODO_POST);
//        processarRetorno(transacaoNova, retorno);
//        if (transacaoNova.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
//            consultarSituacaoTransacao(transacaoNova);
//        }
//        new Transacao(con).alterar(transacaoNova);
        return transacaoNova;
    }

    private JSONObject decifrarDadosSigilososReEnvio(TransacaoVO transacaoVO) throws Exception {
        JSONObject parametrosEnvio = new JSONObject(transacaoVO.getParamsEnvio());
        JSONObject payment = parametrosEnvio.getJSONObject("Payment");
        JSONObject creditCard = payment.getJSONObject("CreditCard");
        creditCard.put("CardNumber", APF.getCartaoMascarado(creditCard.getString("CardNumber")));
        return parametrosEnvio;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        String retorno = executarRequestCieloRequisicao("", "/1/sales/" + transacao.getCodigoExterno() + "/void?amount=" + StringUtilities.formatarCampoMonetario(transacao.getValor(), 15), ExecuteRequestHttpService.METODO_PUT);
        processarRetornoCancelamento(transacao, retorno);
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && (estornarRecibo && transacao.getReciboPagamento() != 0)) {
            estornarRecibo(transacao, estornarRecibo);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, String retorno) throws Exception {
        transacao.setResultadoCancelamento(retorno);
        try {
            JSONObject retornoCancelamento = new JSONObject(retorno);
            Integer status = retornoCancelamento.getInt("Status");

            if (status.equals(CieloECommerceStatusEnum.ANULADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        } catch (Exception e) {
            consultarSituacaoTransacao(transacao);
        }
    }

    public void consultarSituacaoTransacao(TransacaoVO transacao) throws Exception {
        String retorno = executarRequestCieloConsulta(null, "/1/sales/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
        processarRetornoConsultaTransacao(transacao, retorno);
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoConsultaTransacao(TransacaoVO transacao, String retorno) throws Exception {
        try {
            JSONObject retornoTransacao = new JSONObject(retorno);
            JSONObject payment = retornoTransacao.getJSONObject("Payment");
            Integer status = payment.getInt("Status");

            if (status.equals(CieloECommerceStatusEnum.ANULADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equals(CieloECommerceStatusEnum.AUTORIZADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            }
        } catch (Exception e) {
        }
    }

    private void consultarPaymentIdPeloMerchantOrderId(TransacaoVO transacaoVO) {
        try {
            JSONObject envio = new JSONObject(transacaoVO.getParamsEnvio());
            String merchantOrderId = envio.getString("MerchantOrderId");

            String retorno = executarRequestCieloConsulta(null, "/1/sales?merchantOrderId=" + merchantOrderId, ExecuteRequestHttpService.METODO_GET);
            JSONObject retornoTransacao = new JSONObject(retorno);
            JSONObject payment = retornoTransacao.getJSONArray("Payment").getJSONObject(0);
            String paymentId = payment.getString("PaymentId");
            transacaoVO.setCodigoExterno(paymentId);
            Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
            new Transacao(con).alterar(transacaoVO);
        } catch (Exception e) {
        }
    }

    private void processarRetornoDebito(TransacaoVO transacao, String retorno) {
        transacao.setParamsResposta(retorno);
        try {
            JSONObject retornoJSON = new JSONObject(retorno);
            JSONObject payment = retornoJSON.getJSONObject("Payment");

            Integer status = payment.getInt("Status");
            String paymentId = payment.getString("PaymentId");
            String urlAutenticacao = payment.getString("AuthenticationUrl");

            transacao.setCodigoExterno(paymentId);
            transacao.setUrlTransiente(urlAutenticacao);

            if (status.equals(CieloECommerceStatusEnum.NEGADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                transacao.setPermiteRepescagem(true);
            }
//                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
//                transacao.setPermiteRepescagem(false);
//            } else {
//                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
//                transacao.setPermiteRepescagem(true);
//            }
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            transacao.setPermiteRepescagem(true);
        }
    }

    private TransacaoVO consultarSituacaoTransacaoDebito(TransacaoVO transacao) {
        try {
            JSONObject retornoJSON = new JSONObject(transacao.getParamsResposta());
            JSONObject payment = retornoJSON.getJSONObject("Payment");
            JSONObject link = payment.getJSONArray("Links").getJSONObject(0);
            String urlConsultar = link.getString("Href");

            String situacao = executarConsultaCieloDebito(urlConsultar,"", ExecuteRequestHttpService.METODO_GET);

            JSONObject retornoSituacaoJSON = new JSONObject(situacao);
            JSONObject paymentRetorno = retornoSituacaoJSON.getJSONObject("Payment");
            Integer status = paymentRetorno.getInt("Status");
            String paymentId = paymentRetorno.getString("PaymentId");

            transacao.setCodigoExterno(paymentId);

            if (status.equals(CieloECommerceStatusEnum.PAGAMENTO_CONFIRMADO.getId()) || status.equals(CieloECommerceStatusEnum.AUTORIZADO.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                transacao.setParamsResposta(situacao);
            } else if (status.equals(CieloECommerceStatusEnum.PENDENTE.getId())) {
                transacao.setSituacao(SituacaoTransacaoEnum.PENDENTE);
            } else {
                transacao.setParamsResposta(situacao);
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
            Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
            new Transacao(con).alterar(transacao);
            return transacao;
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            return transacao;
        }
    }

    private String executarRequestCieloConsulta(String parametros, String metodo, String metodoHTTP) throws IOException, JSONException {
        return executarRequestCieloECommerce(true, parametros, metodo, metodoHTTP);
    }

    private String executarRequestCieloRequisicao(String parametros, String metodo, String metodoHTTP) throws IOException, JSONException {
        return executarRequestCieloECommerce(false, parametros, metodo, metodoHTTP);
    }

    private String executarRequestCieloECommerce(boolean consulta, String parametros, String metodo, String metodoHTTP) throws IOException, JSONException {
        String URL = this.urlAPIRequisicao;
        if (consulta) {
            URL = this.urlAPIConsulta;
        }
        String path = URL + metodo;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("MerchantId", this.merchantId);
        headers.put("MerchantKey", this.merchantKey);
        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, headers, metodoHTTP, "UTF-8");
    }

    private String executarConsultaCieloDebito(String url, String parametros, String metodoHTTP) throws IOException {
        Map<String, String> headers = new HashMap<String, String>();
//        headers.put("Content-Type", "application/json");
        headers.put("MerchantId", this.merchantId);
        headers.put("MerchantKey", this.merchantKey);
        return ExecuteRequestHttpService.executeRequestGET(url, headers);
    }

    public Pessoa getPessoa() throws Exception {
        if (this.pessoa == null) {
            this.pessoa = new Pessoa(getCon());
        }
        return pessoa;
    }

    public Log getLog() throws Exception {
        if (this.log == null) {
            this.log = new Log(getCon());
        }
        return log;
    }

    private void verificarFormaPagamento() throws Exception {
        if (this.formaPagamentoVO == null) {
            this.formaPagamentoVO = formaPagamento.consultarPorChavePrimaria(movPagamentoVO.getFormaPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantKey() {
        return merchantKey;
    }

    public void setMerchantKey(String merchantKey) {
        this.merchantKey = merchantKey;
    }

    public String getUrlReturn() {
        if (urlReturn == null) {
            urlReturn = "";
        }
        return urlReturn;
    }

    public void setUrlReturn(String urlReturn) {
        this.urlReturn = urlReturn;
    }

    public FormaPagamento getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamento formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception{
        //não implementado
    }
}


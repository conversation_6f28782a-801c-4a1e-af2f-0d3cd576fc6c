/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.cieloecommerce;

/*
 * Created by <PERSON><PERSON> on 31/08/2017.
 */
public enum CieloECommerceStatusEnum {

    NAO_FINALIZADO          (0, "Não finalizado", "Aguardando atualização de status"),
    AUTORIZADO              (1, "Autorizado", "Pagamento apto a ser capturado ou definido como pago"),
    PAGAMENTO_CONFIRMADO    (2, "Pagamento Confirmado", "Pagamento confirmado e finalizado"),
    NEGADO                  (3, "Negado", "Pagamento negado por Autorizador"),
    ANULADO                 (10, "<PERSON><PERSON><PERSON>", "Pagamento cancelado"),
    DEVOLVIDO               (11, "Devolvido", "Pagamento cancelado após 23:59 do dia de autorização"),
    PENDENTE                (12, "Pendente", "Aguardando Status de instituição financeira"),
    ABORTADO                (13, "<PERSON><PERSON><PERSON><PERSON>", "Pagamento cancelado por falha no processamento"),
    PROGRAMADO              (20, "Programado", "Recorrência agendada");

    private Integer id;
    private String status;
    private String descricao;

    private CieloECommerceStatusEnum(Integer id, String status, String descricao) {
        this.id = id;
        this.status = status;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static CieloECommerceStatusEnum valueOff(String id) {
        CieloECommerceStatusEnum[] values = CieloECommerceStatusEnum.values();
        for (CieloECommerceStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

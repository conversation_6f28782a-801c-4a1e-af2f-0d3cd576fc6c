package servicos.impl.cieloecommerce;

public class ValidacaoCartao {
    /**
     * NULL = Não foi possível comunicar com api para validar
     * TRUE = validação realizada e com cartõ válido
     * FALSE = validação realizada com cartão não válido
     */
    private Boolean valido;
    /**
     * 00 ? Analise autorizada
     * 01 ? Bandeira não suportada
     * 02 ? Cartão não suportado na consulta de bin
     * 73 ? Afiliação bloqueada
     */
    private String mensagem;
    /**
     * Master
     * Visa
     * ...
     */
    private String bandeira;

    /**
     * Credito
     * Debito
     * Multiplo
     */
    private String tipo;

    /**
     *
     * @return  TRUE = o cartão é de crédito ou multiplo ou Não foi validado na api por falta de comunicação
     *          FALSE = o cartão é de débito ou inválido
     */
    public boolean isCredito(){
        if(valido == null){
            return false;
        }
        String tipo = getTipo().toUpperCase();
        if(tipo.equals("CRÉDITO") || tipo.equals("CREDITO") ||
                tipo.equals("MÚLTIPLO") || tipo.equals("MULTIPLO") ){
            return true;
        }else{
            return false;
        }
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Boolean getValido() {
        return valido;
    }

    public void setValido(Boolean valido) {
        this.valido = valido;
    }
}

package servicos.impl.planoMs;

import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.microsservice.SuperMSService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PlanoMsService extends SuperMSService {

    public static JSONObject replicarPlano(JSONObject planoJSON, String urlPlanoMs, String chave, Integer codigoEmpresaDestino) throws Exception {
        String url = urlPlanoMs + "/planos/replicar";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", codigoEmpresaDestino.toString());

        String response = null;
        try {
            response = ExecuteRequestHttpService.post(url, planoJSON.toString(), headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            Logger.getLogger(PlanoMsService.class.getName()).log(Level.SEVERE, "Retorno URL: " + url + " - Resposta: " +
                    response + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new PlanoMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new PlanoMsException(e.getMessage() + " - Response: " + response);
            }
        }
    }

    public static JSONObject clonarPlano(Integer codigo, String urlPlanoMs, String chave) throws Exception {
        String url = urlPlanoMs + "/planos/" + codigo + "/clonar";
        return get(chave, url);
    }


    public static JSONObject obterPlano(Integer codigo, String urlPlanoMs, String chave) throws Exception {
        String url = urlPlanoMs + "/planos/" + codigo;
        return get(chave, url);
    }

    private static JSONObject get(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", "1");

        String response = null;
        try {
            response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            Logger.getLogger(PlanoMsService.class.getName()).log(Level.SEVERE, "Retorno URL: " + url + " - Resposta: " +
                    response + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new PlanoMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new PlanoMsException(e.getMessage() + " - Response: " + response);
            }
        }
    }
}

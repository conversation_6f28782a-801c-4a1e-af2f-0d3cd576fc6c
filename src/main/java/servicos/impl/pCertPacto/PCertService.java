package servicos.impl.pCertPacto;

import org.json.JSONObject;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.PCertServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;


/*
 * Created by <PERSON> on 30/01/2024.
 */

public class PCertService extends AbstractCobrancaOnlineServiceComum implements PCertServiceInterface {

    private String URL_PCERT_PACTO = "";

    public PCertService(Connection con) throws Exception {
        super(con);
        URL_PCERT_PACTO = popularUrl();
    }

    public byte[] obterPFXAtravesDoPublic<PERSON>rivado(String certificate_Base64, String private_key_Base64) throws Exception {

        String endpoint = URL_PCERT_PACTO + "/generate_pfx";

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");

        JSONObject envio = new JSONObject();
        envio.put("certificate", certificate_Base64);
        envio.put("private_key", private_key_Base64);

        try {
            byte[] certPFX = ExecuteRequestHttpService.post(endpoint, envio.toString(), headers, "UTF-8");
            return certPFX;
        } catch (Exception ex) {
            throw new Exception("Não foi possível converter os certificados para pfx: " + ex.getMessage());
        }

    }

    private String popularUrl() {
        return PropsService.getPropertyValue(PropsService.urlAPIPcertPacto);
    }
}

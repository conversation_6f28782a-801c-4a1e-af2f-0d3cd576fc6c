package servicos.impl.fluxocaixa;

import java.math.BigDecimal;
import java.util.Date;

public class LancamentoCotaImpostacaoDTO {

    public LancamentoCotaImpostacaoDTO() {
    }

    private Integer tipoLancamentoId;
    private Integer empresaId;
    private Integer receberDe;
    private String descricao;
    private BigDecimal valor;
    private Date dataLancamento;
    private Date dataVencimento;
    private Date dataCompetencia;
    private String observacao;
    private Date dataQuitacao;
    private Integer formaPagamentoId;
    private Integer contaId;
    private Integer planoContasId;
    private Integer centroCustosId;

    private String clienteNovoNome, clienteNovoDocumento,clienteNovoContato;


    public Integer getTipoLancamentoId() {
        return tipoLancamentoId;
    }

    public void setTipoLancamentoId(Integer tipoLancamentoId) {
        this.tipoLancamentoId = tipoLancamentoId;
    }

    public Integer getEmpresaId() {
        return empresaId;
    }

    public void setEmpresaId(Integer empresaId) {
        this.empresaId = empresaId;
    }

    public Integer getReceberDe() {
        return receberDe;
    }

    public void setReceberDe(Integer receberDe) {
        this.receberDe = receberDe;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Date getDataQuitacao() {
        return dataQuitacao;
    }

    public void setDataQuitacao(Date dataQuitacao) {
        this.dataQuitacao = dataQuitacao;
    }

    public Integer getFormaPagamentoId() {
        return formaPagamentoId;
    }

    public void setFormaPagamentoId(Integer formaPagamentoId) {
        this.formaPagamentoId = formaPagamentoId;
    }

    public Integer getContaId() {
        return contaId;
    }

    public void setContaId(Integer contaId) {
        this.contaId = contaId;
    }

    public Integer getPlanoContasId() {
        return planoContasId;
    }

    public void setPlanoContasId(Integer planoContasId) {
        this.planoContasId = planoContasId;
    }

    public Integer getCentroCustosId() {
        return centroCustosId;
    }

    public void setCentroCustosId(Integer centroCustosId) {
        this.centroCustosId = centroCustosId;
    }

    public String getClienteNovoNome() {
        return clienteNovoNome;
    }

    public void setClienteNovoNome(String clienteNovoNome) {
        this.clienteNovoNome = clienteNovoNome;
    }

    public String getClienteNovoDocumento() {
        return clienteNovoDocumento;
    }

    public void setClienteNovoDocumento(String clienteNovoDocumento) {
        this.clienteNovoDocumento = clienteNovoDocumento;
    }

    public String getClienteNovoContato() {
        return clienteNovoContato;
    }

    public void setClienteNovoContato(String clienteNovoContato) {
        this.clienteNovoContato = clienteNovoContato;
    }
}

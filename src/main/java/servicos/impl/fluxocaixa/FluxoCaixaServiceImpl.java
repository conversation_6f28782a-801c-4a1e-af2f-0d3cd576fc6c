package servicos.impl.fluxocaixa;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import controle.financeiro.SimulacaoSaldoVO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.financeiro.FluxoCaixaTO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.PlanoConta;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.RelatorioDemonstrativoFinanceiro;
import servicos.interfaces.FluxoCaixaServiceInterface;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;

/**
 * Created by alcides on 06/11/2017.
 */
public class FluxoCaixaServiceImpl implements FluxoCaixaServiceInterface {
    private final Connection con;
    private final PlanoConta planoConta;

    public FluxoCaixaServiceImpl(Connection con) throws Exception {
        this.con = con;
        planoConta = new PlanoConta(con);
    }

    public List<DemonstrativoFinanceiro> gerarDemonstrativoPrevisto(Integer empresa,
                                                                    Date inicio,
                                                                    Date fim,
                                                                    Boolean previsto,
                                                                    String filtroContas,
                                                                    boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao,
                                                                    boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) throws Exception{
        RelatorioDemonstrativoFinanceiro relatorioDF = new RelatorioDemonstrativoFinanceiro();
        Calendar dataInicialRel = Calendario.getInstance();
        dataInicialRel.setTime(Calendario.getDataComHoraZerada(inicio));
        Calendar dataFinalRel = Calendario.getInstance();
        dataFinalRel.setTime(Calendario.getDataComHora(fim, "23:59:59"));

        List<DemonstrativoFinanceiro> listaDF = relatorioDF.gerarDemonstrativo(
                previsto ? TipoRelatorioDF.RECEITAPROVISAO : TipoRelatorioDF.RECEITA,
                TipoVisualizacaoRelatorioDF.PLANOCONTA,
                dataInicialRel,
                dataFinalRel,
                empresa,
                new ArrayList<Integer>(),
                false,
                TipoFonteDadosDF.TODAS,
                false,
                false,
                UteisValidacao.emptyString(filtroContas) ? "fluxo_caixa" : filtroContas,
                incluirParcelasRecorrenciaEmRelatorioReceitaProvisao, true, true, null, true,
                incluirParcelasEmAbertoEmRelatorioReceitaProvisao);

        return listaDF;
    }

    public List<FluxoCaixaTO> converterDFtoFC(List<DemonstrativoFinanceiro> listaDF, Boolean previsto) throws Exception {
        List<FluxoCaixaTO> listaFinan = new ArrayList<>();
        for (DemonstrativoFinanceiro df : listaDF) {
            for (TotalizadorMesDF tot : df.getListaTotalizadorMeses()) {
                Set<LancamentoDF> listaProcessar;
                if (tot.getListaLancamentosNaoAtribuido().isEmpty()) { // isso para evitar duplicações
                    listaProcessar = tot.getListaLancamentos();
                } else {
                    listaProcessar = tot.getListaLancamentosNaoAtribuido();
                }
                for (LancamentoDF lanc : listaProcessar) {
                    boolean adicionar = false;
                    String agrupador = null;
                    FluxoCaixaTO fPlano = new FluxoCaixaTO(df.getCodigoAgrupador(), df.getNomeAgrupador(), lanc.getDiaConta());
                    if (!listaFinan.contains(fPlano)) {
                        listaFinan.add(fPlano);
                    }
                    for (RateioIntegracaoTO rt : lanc.getRateios()) {
                        if ("Não Informado Plano de Conta".equals(df.getNomeAgrupador())) {
                            adicionar = true;
                            agrupador = null;
                            break;
                        } else if (df.getCodigoAgrupador() != null
                                && df.getCodigoAgrupador().equals(rt.getCodigoPlano())) {
                            adicionar = true;
                            agrupador = df.getCodigoAgrupador();
                            break;
                        }
                    }
                    if (adicionar || lanc.getRateios().isEmpty()) {
                        FluxoCaixaTO fc = getFluxoCaixaTO(lanc, agrupador);
//                        if(!adds.contains(lanc)){
                        listaFinan.add(fc);
//                            adds.add(lanc);
//                        }

                    }
                }
            }
        }
        return listaFinan;
    }

    private static FluxoCaixaTO getFluxoCaixaTO(LancamentoDF lanc, String agrupador) {
        FluxoCaixaTO fc = new FluxoCaixaTO();
        fc.setDia(lanc.getDiaConta());
        fc.setTipoAutorizacaoCobranca(lanc.getTipoAutorizacaoCobranca());
        if (lanc.getMovConta() > 0) {
            fc.setCodigoMovConta(lanc.getMovConta());
            fc.setDescricao(lanc.getDescricaoLancamento());
        } else {
            fc.setDescricao(lanc.getNomePessoa());
            fc.setCodigoPessoa(lanc.getCodigoPessoa());
        }
        try {
            fc.setConta(lanc.getConta());
            fc.setTipoFormaPagto(lanc.getTipoFormaPagto().getDescricao());
        } catch (Exception e) {

        }

        if (TipoES.SAIDA.equals(lanc.getTipoES())) {
            fc.setSaidas(lanc.getValorLancamento());
        } else {
            fc.setEntradas(lanc.getValorLancamento());
        }
        if (UteisValidacao.emptyString(agrupador)) {
            fc.setAgrupador("NAOINFORMADO");
        } else {
            fc.setAgrupador(agrupador);
        }

        fc.setTipoES(lanc.getTipoES() == null ? TipoES.ENTRADA : lanc.getTipoES());
        return fc;
    }

    public List<FluxoCaixaTO> processarFluxoCaixa(Integer empresa,
                                              Date inicio,
                                              Date fim,
                                              boolean previsto,
                                              Double saldoInicial,
                                              FluxoCaixaTO total,
                                              String contas) throws Exception {
        return processarFluxoCaixa(empresa, inicio, fim, previsto, saldoInicial, total, contas, false, false);
    }

    public List<FluxoCaixaTO> processarFluxoCaixa(Integer empresa,
                                                  Date inicio,
                                                  Date fim,
                                                  boolean previsto,
                                                  Double saldoInicial,
                                                  FluxoCaixaTO total,
                                                  String contas,
                                                  boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao,
                                                  boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) throws Exception {
        List<FluxoCaixaTO> lista = new ArrayList<FluxoCaixaTO>();
        Map<Date, Map<String, List<FluxoCaixaTO>>> lancamentosDia = new HashMap<Date, Map<String, List<FluxoCaixaTO>>>();
        List<Date> diasEntreDatas = Uteis.getDiasEntreDatas(inicio, fim);
        for(Date d : diasEntreDatas){
            HashMap<String, List<FluxoCaixaTO>> mapa = new HashMap<String, List<FluxoCaixaTO>>();
            mapa.put("NAOINFORMADO", new ArrayList<FluxoCaixaTO>());
            lancamentosDia.put(Calendario.getDataComHoraZerada(d), mapa);
        }
        Map<String, PlanoContaTO> mapaTodosPlanos = new HashMap<String, PlanoContaTO>();
        List<PlanoContaTO> planoContaTOES = planoConta.consultarTodos();
        for(PlanoContaTO pc : planoContaTOES){
            mapaTodosPlanos.put(pc.getCodigoPlano(), pc);
        }
        List<DemonstrativoFinanceiro> listaDF = gerarDemonstrativoPrevisto(empresa, inicio, fim, previsto, contas,
                incluirParcelasRecorrenciaEmRelatorioReceitaProvisao, incluirParcelasEmAbertoEmRelatorioReceitaProvisao);

        List<FluxoCaixaTO> listaFinan = converterDFtoFC(listaDF, previsto);
        for (FluxoCaixaTO f : listaFinan) {
            if (f.isPlanoAgrupador()) {
                try {
                    lancamentosDia.get(f.getDia()).put(f.getAgrupador(), new ArrayList<>());
                } catch (Exception e) {
                    System.out.println(f.getAgrupador());
                }

            }
        }
        for (FluxoCaixaTO f : listaFinan) {
            if (!f.isPlanoAgrupador()) {
                Map<String, List<FluxoCaixaTO>> mapaPlan = lancamentosDia.get(f.getDia());
                List<FluxoCaixaTO> fluxoCaixaTOES = mapaPlan.get(f.getAgrupador());
                fluxoCaixaTOES.add(f);
            }
        }

        for (Date dia : lancamentosDia.keySet()) {
            FluxoCaixaTO fluxo = new FluxoCaixaTO();
            fluxo.setDia(dia);
            fluxo.setListaFluxoCaixa(new ArrayList<>());
            Map<String, List<FluxoCaixaTO>> mapaPlanos = lancamentosDia.get(dia);
            for (String agrupadorPlano : mapaPlanos.keySet()) {
                List<FluxoCaixaTO> fluxoCaixaTOES = mapaPlanos.get(agrupadorPlano);
                PlanoContaTO planoContaTO = mapaTodosPlanos.get(agrupadorPlano);
                if ((agrupadorPlano.equals("NAOINFORMADO")
                        && UteisValidacao.emptyList(fluxoCaixaTOES))
                        || (!agrupadorPlano.equals("NAOINFORMADO") && planoContaTO == null)) {
                    continue;
                }
                FluxoCaixaTO fluxoPlano = new FluxoCaixaTO();
                fluxoPlano.setListaFluxoCaixa(fluxoCaixaTOES);
                fluxoPlano.setAgrupador(agrupadorPlano);
                fluxoPlano.setPlanoConta(planoContaTO);
                fluxo.getListaFluxoCaixa().add(fluxoPlano);

            }
            lista.add(fluxo);
        }
        Ordenacao.ordenarLista(lista, "dia");
        aplicarSaldo(lista, saldoInicial, total, null);
        return lista;
    }

    public JSONArray montarGrafico(List<FluxoCaixaTO> previsto, List<FluxoCaixaTO> realizado) throws Exception{
        JSONArray grafico = new JSONArray();
        Map<Date, Map<String, Double>> mapa = new HashMap<Date, Map<String, Double>>();
        boolean incluirSimulado = false;
        for(FluxoCaixaTO f : previsto){
            preencherMapa(f.getDia(), mapa, "previsto", f.getSaldo());
            if(f.getSaldoRealizado() == null){
                preencherMapa(f.getDia(), mapa, "simulado", f.getSaldo());
            }else{
                incluirSimulado = true;
                preencherMapa(f.getDia(), mapa, "simulado", f.getSaldoRealizado());
            }
        }
        for(FluxoCaixaTO f : realizado){
            preencherMapa(f.getDia(), mapa, "realizado", f.getSaldo());
        }
        List<Date> dias = new ArrayList<Date>(mapa.keySet());
        Collections.sort(dias);
        for(Date d : dias){
            JSONObject json = new JSONObject();
            json.put("dia", Uteis.getDataAplicandoFormatacao(d, "dd/MM"));
            if(mapa.get(d).get("realizado") != null){
                json.put("realizado", mapa.get(d).get("realizado"));
            }
            if(mapa.get(d).get("previsto") != null){
                json.put("previsto", mapa.get(d).get("previsto"));
            }
            if(incluirSimulado && mapa.get(d).get("simulado") != null){
                json.put("simulado", mapa.get(d).get("simulado"));
            }
            grafico.put(json);
        }
        return grafico;
    }

    private void preencherMapa(Date dia, Map<Date, Map<String, Double>> m, String tipo, Double valor){
        Map<String, Double> dds = m.get(dia);
        if(dds == null){
            dds = new HashMap<>();
            m.put(dia, dds);
        }
        dds.put(tipo, valor);
    }

    private void totalizarTipoAutorizacaoCobrancaFluxoCaixa(FluxoCaixaTO fluxoItem, FluxoCaixaTO fluxoTotalizar){
        if(fluxoItem.getTipoAutorizacaoCobranca().equals(TipoAutorizacaoCobrancaEnum.NENHUM)){
            fluxoTotalizar.setEntradasTipoAutorizacaoNenhum(fluxoTotalizar.getEntradasTipoAutorizacaoNenhum() + fluxoItem.getEntradas());
        }

        if(fluxoItem.getTipoAutorizacaoCobranca().equals(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO)){
            fluxoTotalizar.setEntradasTipoAutorizacaoBoletoBancario(fluxoTotalizar.getEntradasTipoAutorizacaoBoletoBancario() + fluxoItem.getEntradas());
        }

        if(fluxoItem.getTipoAutorizacaoCobranca().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)){
            fluxoTotalizar.setEntradasTipoAutorizacaoCartaoCredito(fluxoTotalizar.getEntradasTipoAutorizacaoCartaoCredito() + fluxoItem.getEntradas());
        }

        if(fluxoItem.getTipoAutorizacaoCobranca().equals(TipoAutorizacaoCobrancaEnum.DEBITOCONTA)){
            fluxoTotalizar.setEntradasTipoAutorizacaoDebitoConta(fluxoTotalizar.getEntradasTipoAutorizacaoDebitoConta() + fluxoItem.getEntradas());
        }
    }

    public void aplicarSaldo(List<FluxoCaixaTO> lista, Double saldoInicial, FluxoCaixaTO total, String diaIniciar){
        aplicarSaldo(lista, saldoInicial, total, null, false);

    }

    public void aplicarSaldo(List<FluxoCaixaTO> lista, Double saldoInicial, FluxoCaixaTO total, String diaIniciar, Boolean isSaldoInicial){
        Double saldo = saldoInicial;
        total.setEntradas(0.0);
        total.setSaidas(0.0);
        boolean iniciado = false;
        for(FluxoCaixaTO f : lista){
            if(!iniciado && diaIniciar != null && !f.getDiaApresentar().equals(diaIniciar)){
                continue;
            }
            f.setFormasEntrada(new HashMap<>());
            f.setFormasSaida(new HashMap<>());
            f.setEntradas(0.0);
            f.setSaidas(0.0);
            iniciado = true;
            String indexadorFP= "";
            f.setListaFluxoCaixa(Ordenacao.ordenarLista(f.getListaFluxoCaixa(), "agrupador"));
            for(FluxoCaixaTO fPl : f.getListaFluxoCaixa()){
                fPl.setListaFluxoCaixa(Ordenacao.ordenarLista(fPl.getListaFluxoCaixa(), "descricao"));
                fPl.setEntradas(0.0);
                fPl.setSaidas(0.0);
                for(FluxoCaixaTO fLc : fPl.getListaFluxoCaixa()){
                    indexadorFP = fLc.getTipoFormaPagto() == null? "NÃO INFORMADO" : fLc.getTipoFormaPagto();
                    if(!UteisValidacao.emptyNumber(fLc.getEntradas())){
                        f.getFormasEntrada().put(indexadorFP,
                            f.getFormasEntrada().get(indexadorFP) == null ? fLc.getTotal()
                                    : (f.getFormasEntrada().get(indexadorFP)+fLc.getTotal()));
                    }
                    if(!UteisValidacao.emptyNumber(fLc.getSaidas())){
                        f.getFormasSaida().put(indexadorFP,
                            f.getFormasSaida().get(indexadorFP) == null ? fLc.getTotal()
                                    : (f.getFormasSaida().get(indexadorFP)+fLc.getTotal()));
                    }
                    f.getContas().put(fLc.getConta(),
                            f.getContas().get(fLc.getConta()) == null ? fLc.getTotal()
                                    : (f.getContas().get(fLc.getConta())+fLc.getTotal()));

                    fPl.setEntradas(fPl.getEntradas() + fLc.getEntradas());
                    f.setEntradas(f.getEntradas() + fLc.getEntradas());
                    total.setEntradas(total.getEntradas() + fLc.getEntradas());

                    totalizarTipoAutorizacaoCobrancaFluxoCaixa(fLc, fPl);
                    totalizarTipoAutorizacaoCobrancaFluxoCaixa(fLc, f);

                    fPl.setSaidas(fPl.getSaidas() + fLc.getSaidas());
                    f.setSaidas(f.getSaidas() + fLc.getSaidas());
                    total.setSaidas(total.getSaidas() + fLc.getSaidas());
                    saldo += fLc.getTotal();
                    fLc.setSaldo(saldo);
                }
                fPl.setSaldo(saldo);
            }
            if(!isSaldoInicial && diaIniciar == null){
                f.setSaldo(saldo);
                f.setSaldoRealizado(null);
            }else{
                f.setSaldoRealizado(saldo);
            }
        }
        for (FluxoCaixaTO f : lista) {
            for (FluxoCaixaTO fPl : f.getListaFluxoCaixa()) {
                for(FluxoCaixaTO fFilhos : new ArrayList<FluxoCaixaTO>(f.getListaFluxoCaixa())){
                    if(!fPl.getAgrupador().equals(fFilhos.getAgrupador())
                        && fFilhos.getAgrupador().startsWith(fPl.getAgrupador())){
                        for(FluxoCaixaTO fLc : fFilhos.getListaFluxoCaixa()){
                            fPl.setEntradas(fPl.getEntradas() + fLc.getEntradas());
                            fPl.setSaidas(fPl.getSaidas() + fLc.getSaidas());
                        }
                    }
                }
            }
        }


        total.setSaldo(saldo);
    }



    public Double obterSaldoInicial(Integer empresa, Date dia, boolean previsto) throws Exception{
        Double saldo = 0.0;
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT SUM (valor) AS soma, COUNT(*) AS total, tipooperacao  AS tipo FROM ( ");
        sql.append("         SELECT distinct (mc.codigo), mc.* ");
        sql.append("                         FROM movconta mc ");
        sql.append(" LEFT JOIN conta ON mc.conta = conta.codigo ");
        sql.append(" INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo ");
        sql.append(" INNER JOIN empresa ON empresa.codigo = mc.empresa ");
        sql.append(" INNER JOIN pessoa ON mc.pessoa = pessoa.codigo ");
        sql.append(" WHERE ");
        //sql.append(" mc.tipooperacao IN (2,1) ");
        sql.append(" AND mc.empresa = ").append(empresa);
        sql.append(" AND ").append(previsto ? "mc.datavencimento" : "mc.dataquitacao").append(" < '").append(Uteis.getDataAplicandoFormatacao(dia, "yyyy-MM-dd")).append(" 00:00:00' ");
        sql.append(" ) AS total GROUP BY tipooperacao ");
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                saldo += rs.getDouble("soma");
            }
            return saldo;
        }
    }

    public void incluirSimulacaoSaldo(SimulacaoSaldoVO obj) throws Exception {
        String sql = "INSERT INTO simulacaosaldo(empresa,usuariolancamento, saldo, dia, datalancamento, ano, mes, saldoinicial) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            sqlInserir.setInt(1, obj.getEmpresa());
            sqlInserir.setInt(2, obj.getUsuarioLancamento());
            sqlInserir.setDouble(3, obj.getSaldo());
            sqlInserir.setString(4, obj.getDia());
            sqlInserir.setString(5, obj.getDataLancamento());
            sqlInserir.setInt(6, obj.getAno());
            sqlInserir.setInt(7, obj.getMes());
            sqlInserir.setBoolean(8, obj.getSaldoInicial());
            try (ResultSet rs = sqlInserir.executeQuery()) {
                if (rs.next()) {
                    obj.setCodigo(rs.getInt("codigo"));
                }
            }
        }
    }

    public SimulacaoSaldoVO buscarSimulacaoSaldo(Integer ano, Integer mes) throws Exception{

        StringBuffer sql = new StringBuffer();

        sql.append("SELECT * FROM simulacaosaldo ");
        sql.append("where ano = ");
        sql.append(ano);
        sql.append(" and mes = ");
        sql.append(mes);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(String.valueOf(sql))) {
                if (tabelaResultado.next()) {
                    return montarDadosBasico(tabelaResultado);
                } else  {
                    return new SimulacaoSaldoVO();
                }
            }
        }
    }

    public void exluirSimulacaoSaldo(Integer mes, Integer ano) throws Exception{
        try (PreparedStatement stm = con.prepareStatement("DELETE FROM simulacaosaldo WHERE mes = " + mes + " and ano = " + ano)) {
            stm.execute();
        }
    }

    public static SimulacaoSaldoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        SimulacaoSaldoVO obj = new SimulacaoSaldoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDataLancamento(dadosSQL.getString("datalancamento"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setUsuarioLancamento(dadosSQL.getInt("usuariolancamento"));
        obj.setSaldo(dadosSQL.getDouble("saldo"));
        obj.setDia(dadosSQL.getString("dia"));
        obj.setSaldoInicial(dadosSQL.getBoolean("saldoinicial"));
        return obj;
    }
}

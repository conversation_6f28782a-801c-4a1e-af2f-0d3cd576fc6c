package servicos.impl.recebiveis;

import br.com.pactosolucoes.enumeradores.StatusMovimentacaoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.CartaoCredito;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.HistoricoCartao;
import negocio.facade.jdbc.financeiro.HistoricoCheque;
import negocio.facade.jdbc.financeiro.Lote;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.financeiro.TipoRemessa;
import negocio.facade.jdbc.utilitarias.CacheControl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Objects.nonNull;

public class GestaoRecebiveisServiceImpl extends SuperEntidade {

    private List<Integer> cartaoComposicao = new ArrayList<Integer>();
    private List<Integer> chequeComposicao = new ArrayList<Integer>();

    public GestaoRecebiveisServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public GestaoRecebiveisServiceImpl() throws Exception {
        super();
    }

    public List<FormaPagamentoVO> montarTotais(List<FormaPagamentoVO> lista,
                                               List<MovPagamentoVO> pagamentos,
                                               Boolean comLote,
                                               boolean chequeAvista, boolean chequeAprazo,
                                               Date iniciocompensacao,
                                               Date finalcompensacao,
                                               Boolean considerarDataOriginal,
                                               StatusMovimentacaoEnum status, ChequeVO chequeVO,
                                               String nomeConta, boolean mostrarCancelados, Date dataInicioMovimentacaoInicio, Date dataInicioMovimentacaoFim, String horaInicioMovimentacaoInicio, String horaInicioMovimentacaoFim) throws Exception {
        Map<Integer, String> codigos = new HashMap<>();
        Map<Integer, String> codigosAvulsos = new HashMap<>();
        Map<Integer, Double> valores = new HashMap<>();
        Map<Integer, Integer> qtds = new HashMap<>();
        List<String> qtdAlunosFormaPgto = new ArrayList<>();
        List<String> alunosPorFormaPagamento = new ArrayList<>();

        for (MovPagamentoVO mp : pagamentos) {
            if (mp.getFormaPagamento().getTipoFormaPagamento().equals("CA") || mp.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                if(mp.isPagamentoAvulso()){
                    String cods = codigosAvulsos.get(mp.getFormaPagamento().getCodigo());
                    cods = (cods == null ? "" : cods) + ("," + mp.getCodigo());
                    codigosAvulsos.put(mp.getFormaPagamento().getCodigo(), cods);
                }else{
                    String cods = codigos.get(mp.getFormaPagamento().getCodigo());
                    cods = (cods == null ? "" : cods) + ("," + mp.getCodigo());
                    codigos.put(mp.getFormaPagamento().getCodigo(), cods);
                }
                qtdAlunosFormaPgto.add(mp.getFormaPagamento().getCodigo() + "-" + mp.getPessoa().getCodigo());
            } else {
                if ((status != null
                        && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                        && !UteisValidacao.emptyString(mp.getContaFinanceiro()))
                        || (status != null
                        && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                        && UteisValidacao.emptyString(mp.getContaFinanceiro()))) {
                    continue;
                }

                if (!UteisValidacao.emptyString(nomeConta) && !mp.getContaFinanceiro().toLowerCase().contains(nomeConta.toLowerCase())) {
                    continue;
                }

                if(nonNull(status) && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS) && dataInicioMovimentacaoFim != null && dataInicioMovimentacaoInicio != null){
                    String horaIni = UteisValidacao.emptyString(horaInicioMovimentacaoInicio) ? "00:00:00" : horaInicioMovimentacaoInicio;
                    String horaFim = UteisValidacao.emptyString(horaInicioMovimentacaoFim) ? "23:59:59" : horaInicioMovimentacaoFim;
                    if(mp.getDataMovimento() != null && !Calendario.entreComHora(mp.getDataMovimento(), Calendario.getDataComHora(dataInicioMovimentacaoInicio, horaIni), Calendario.getDataComHora(dataInicioMovimentacaoFim, horaFim))){
                        continue;
                    }
                }

                valores.put(mp.getFormaPagamento().getCodigo(), mp.getValor() + (valores.get(mp.getFormaPagamento().getCodigo()) == null ? 0.0 : valores.get(mp.getFormaPagamento().getCodigo())));
                qtds.put(mp.getFormaPagamento().getCodigo(), 1 + (qtds.get(mp.getFormaPagamento().getCodigo()) == null ? 0 : qtds.get(mp.getFormaPagamento().getCodigo())));
                qtdAlunosFormaPgto.add(mp.getFormaPagamento().getCodigo() + "-" + mp.getPessoa().getCodigo());
            }
        }

        for (FormaPagamentoVO fp : lista) {

            alunosPorFormaPagamento = new ArrayList<>();
            for (String e : qtdAlunosFormaPgto) {
                if (e.split("-")[0].equals(fp.getCodigo().toString())) {
                    alunosPorFormaPagamento.add(e.split("-")[1]);
                }
            }
            fp.setQuantidadeAlunos(alunosPorFormaPagamento.stream().distinct().count());

            if ((fp.getTipoFormaPagamento().equals("CH") || fp.getTipoFormaPagamento().equals("CA"))
                    && codigos.get(fp.getCodigo()) == null && codigosAvulsos.get(fp.getCodigo()) == null) {
                fp.setValor(0.0);
                fp.setQuantidade(0);
                continue;
            }

            if (fp.getTipoFormaPagamento().equals("CH")) {

                ResultSet rs = consultarPagamentoCheques(true,
                        codigos.get(fp.getCodigo()) == null ? "" :codigos.get(fp.getCodigo()).replaceFirst(",", ""),
                        codigosAvulsos.get(fp.getCodigo()) == null ? "" : codigosAvulsos.get(fp.getCodigo()).replaceFirst(",", ""),
                        comLote,
                        chequeAvista, chequeAprazo,
                        iniciocompensacao,
                        finalcompensacao,
                        considerarDataOriginal,
                        status, chequeVO, false, mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);

                int count = 0;
                Double soma = 0.0;
                chequeComposicao = new ArrayList<Integer>();
                while (rs.next()) {
                    ChequeTO novo = new ChequeTO();
                    Double valor= 0.0;
                    novo.setCodigo(rs.getInt("codigocheque"));
                    novo.setCodigosComposicao(rs.getString("composicao"));
                    valor = rs.getDouble("totalvalor");
                    HistoricoCheque historicoCheque = new HistoricoCheque(con);
                    historicoCheque.getContaLoteCheque(novo, false);
                    if (!UteisValidacao.emptyString(nomeConta) && !novo.getContaContido().toLowerCase().contains(nomeConta.toLowerCase())) {
                        continue;
                    }
                    if (novo.getCodigosComposicao() != null && !novo.getCodigosComposicao().equals("")) {
                        Boolean presente = false;
                        for (Integer codigo : chequeComposicao) {
                            if (novo.getCodigo() == codigo) {
                                presente = true;
                                break;
                            }
                        }
                        if (!presente) {
                            String[] codigos1 = novo.getCodigosComposicao().split(",");
                            for (String codComposicao : codigos1) {
                                chequeComposicao.add(Integer.parseInt(codComposicao));
                            }
                            soma += valor;
                            count++;
                        } else {
                            continue;
                        }

                    }else {
                        soma += valor;
                        count++;
                    }
                    fp.setValor(soma);
                    fp.setQuantidade(count);

                }

            } else if (fp.getTipoFormaPagamento().equals("CA")) {
                ResultSet rs = consultarCartaoPorPeriodoCompensacao(
                        codigos.get(fp.getCodigo()) == null ? "" : codigos.get(fp.getCodigo()).replaceFirst(",", ""),
                        codigosAvulsos.get(fp.getCodigo()) == null ? "" : codigosAvulsos.get(fp.getCodigo()).replaceFirst(",", ""),
                        iniciocompensacao,
                        finalcompensacao,
                        comLote,
                        considerarDataOriginal,
                        false,
                        status,true, mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);

                int count = 0;
                Double soma = 0.0;
                cartaoComposicao = new ArrayList<Integer>();
                while (rs.next()) {
                    CartaoCreditoTO novo = new CartaoCreditoTO();
                    Double valor= 0.0;
                    novo.setCodigo(rs.getInt("codigocartao"));
                    novo.setCodigosComposicao(rs.getString("composicao"));
                    valor = rs.getDouble("totalvalor");
                    HistoricoCartao historicoCartao = new HistoricoCartao(con);
                    historicoCartao.getContaLoteCartao(novo, false);
                    if (!UteisValidacao.emptyString(nomeConta) && !novo.getContaContido().toLowerCase().contains(nomeConta.toLowerCase())) {
                        continue;
                    }

                    if ((status != null
                            && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                            && !UteisValidacao.emptyString(novo.getContaContido()))
                            || (status != null
                            && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                            && UteisValidacao.emptyString(novo.getContaContido()))) {
                         continue;
                    }

                    if (novo.getCodigosComposicao() != null && !novo.getCodigosComposicao().equals("")) {
                        Boolean presente = false;
                        for (Integer codigo : cartaoComposicao) {
                            if (novo.getCodigo().intValue() == codigo) {
                                presente = true;
                                break;
                            }
                        }
                        if (!presente) {
                            String[] codigos1 = novo.getCodigosComposicao().split(",");
                            for (String codComposicao : codigos1) {
                                cartaoComposicao.add(Integer.parseInt(codComposicao));
                            }
                            soma += valor;
                            count++;
                        } else {
                            continue;
                        }

                    }else {
                        soma += valor;
                        count++;
                    }
                    fp.setValor(soma);
                    fp.setQuantidade(count);
                }

            } else {
                fp.setValor(valores.get(fp.getCodigo()) == null ? 0.0 : valores.get(fp.getCodigo()));
                fp.setQuantidade(qtds.get(fp.getCodigo()) == null ? 0 : qtds.get(fp.getCodigo()));
            }
        }
        return lista;
    }

    public ResultSet consultarPagamentoCheques(Boolean total,
                                               String codigos,
                                               String codigosAvulsos,
                                               Boolean comLote,
                                               Boolean chequeAvista,
                                               Boolean chequeAprazo,
                                               Date inicio,
                                               Date fim,
                                               Boolean considerarDataOriginal,
                                               StatusMovimentacaoEnum status, ChequeVO chequeVO, boolean composicao, boolean mostrarCancelados,
                                               Date dataInicioMovimentacaoInicio, Date dataInicioMovimentacaoFim, String horaInicioMovimentacaoInicio, String horaInicioMovimentacaoFim) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT ");
        if (total) {

            if (composicao){
                sql.append("sum(round(ch.valortotal::numeric, 2)) as totalvalor, count(ch.codigo) as total \n");
            }else {
                sql.append("round(ch.valortotal::numeric, 2) as totalvalor, ch.composicao as composicao,ch.codigo as codigocheque,ch.movpagamento as movpagamento \n");
            }

//            sql.append(" sum(round(ch.valortotal::numeric, 2)) as totalvalor, count(ch.codigo) as total ");
        } else {
            sql.append(" ch.valor,ch.numero, p.codigo as pessoa, ch.codigo as codigocheque, cli.matricula, \n");
            sql.append(" mp.nomepagador, p.nome as nomepessoa, p.cfp, mp.datalancamento, mc.datalancamento as mclan, ch.datacompesancao, ch.dataoriginal, ch.valortotal, mp.recibopagamento,  \n");
            sql.append(" ch.composicao, ch.movconta,ch.situacao, b.codigobanco as banco, b.nome as nomebanco, \n");
            sql.append(" mp.empresa, e.nome as nomeEmpresa, ch.agencia, ch.conta, ch.nomenocheque, ch.numero, mp.codigo as codigoMovPagamento \n");
        }

        sql.append(" FROM Cheque ch ");

        if(nonNull(status) && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS) && dataInicioMovimentacaoInicio != null && dataInicioMovimentacaoFim != null){
            sql.append(" left join historicocheque hc on hc.cheque = ch.codigo \n");
        }

        if (!total) {
            sql.append(" inner join banco b on b.codigo = ch.banco \n");
            sql.append(" left join movpagamento mp on mp.codigo = ch.movpagamento \n");
            sql.append(" left join empresa e on mp.empresa = e.codigo \n");
            sql.append(" left join movconta mc on mc.codigo = ch.movconta \n");
            sql.append(" left join pessoa p on mp.pessoa = p.codigo\n");
            sql.append(" left join cliente cli on cli.pessoa = p.codigo \n");

        }

        sql.append(" where (");
        if(!UteisValidacao.emptyString(codigos)){
            sql.append("movpagamento in (").append(codigos).append(") ").append(UteisValidacao.emptyString(codigosAvulsos) ? "" : " or ");
        }
        if(!UteisValidacao.emptyString(codigosAvulsos)){
            sql.append("ch.movconta in (").append(codigosAvulsos).append(") ");
        }
        sql.append(") ");


        if(inicio != null && fim != null){
            if (considerarDataOriginal) {
                sql.append(" and ((dataoriginal >= '").append(Uteis.getDataJDBC(inicio)).append(" 00:00:00'");
                sql.append(" and dataoriginal <= '").append(Uteis.getDataJDBC(fim)).append(" 23:59:59' )");
                sql.append(" or (datacompesancao >= '").append(Uteis.getDataJDBC(inicio)).append(" 00:00:00'");
                sql.append(" and datacompesancao <= '").append(Uteis.getDataJDBC(fim)).append(" 23:59:59' and dataoriginal is null)) ");
            } else {
                sql.append(" and datacompesancao >= '").append(Uteis.getDataJDBC(inicio)).append(" 00:00:00'");
                sql.append(" and datacompesancao <= '").append(Uteis.getDataJDBC(fim)).append(" 23:59:59'");
            }
        }

        if (comLote != null || status != null) {
            if ((comLote != null && comLote) || (status != null && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS))) {
                sql.append(" and ch.codigo IN (select distinct(cheque) from chequecartaolote where cheque is not null)");
            }

            if((comLote != null && !comLote) || (status != null && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS))){
                sql.append(" and ch.codigo NOT IN (select distinct(cheque) from chequecartaolote where cheque is not null)");
            }
        }
        if (!UteisValidacao.emptyString(chequeVO.getNumero())) {
            sql.append(" and ch.numero = '").append(chequeVO.getNumero()).append("'");
        }

        if(!mostrarCancelados) {
            sql.append(" and ch.situacao not like 'CA' ");
            sql.append(" and ch.situacao not like 'DV' ");
        }

        if (chequeAprazo && !chequeAvista) {
            sql.append(" and vistaouprazo = 'PR' ");
        }
        if (!chequeAprazo && chequeAvista) {
            sql.append(" and vistaouprazo = 'AV' ");
        }

        if(nonNull(status) && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS) && dataInicioMovimentacaoInicio != null && dataInicioMovimentacaoFim != null) {
            String horaIni = UteisValidacao.emptyString(horaInicioMovimentacaoInicio) ? "00:00:00" : horaInicioMovimentacaoInicio;
            String horaFim = UteisValidacao.emptyString(horaInicioMovimentacaoFim) ? "23:59:59" : horaInicioMovimentacaoFim;
            sql.append(" and hc.datainicio between '")
                    .append(Uteis.getDataJDBC(dataInicioMovimentacaoInicio)).append(" ")
                    .append(horaIni).append("' and '")
                    .append(Uteis.getDataJDBC(dataInicioMovimentacaoFim)).append(" ")
                    .append(horaFim).append("'");
        }

        if (!total) {
            if (composicao){
                sql.append(" order by  movpagamento\n");
            }else{
                sql.append(" ORDER BY p.nome ");
            }
        }

        PreparedStatement sqlConsulta = con.prepareStatement(sql.toString());
        return sqlConsulta.executeQuery();
    }

    private String validarComposicao(String composicao){
        try {
            if(UteisValidacao.emptyString(composicao)){
                return null;
            }

            return SuperFacadeJDBC.existe("select codigo from cheque where codigo in (" + composicao + ")", con) ?
                    composicao : null;
        }catch (Exception e){
            return composicao;
        }
    }

    public List<ChequeTO> consultarPagamentoChequesLista(Boolean total,
                                                         String codigos,
                                                         String codigosAvulsos,
                                                         Boolean comLote,
                                                         Boolean chequeAvista,
                                                         Boolean chequeAprazo,
                                                         Date inicio,
                                                         Date fim,
                                                         Boolean considerarDataOriginal,
                                                         StatusMovimentacaoEnum status,
                                                         String nomeConta,
                                                         String codigoLoteFiltro,
                                                         ChequeVO chequeVO,
                                                         List<Integer> codigosListaFiltro,
                                                         boolean mostrarCancelados, Date dataInicioMovimentacaoInicio, Date dataInicioMovimentacaoFim, String horaInicioMovimentacaoInicio, String horaInicioMovimentacaoFim) throws Exception {
        List<ChequeTO> lista = new ArrayList<>();
        chequeComposicao =  new ArrayList<Integer>();
        ResultSet rs = consultarPagamentoCheques(total,
                codigos,
                codigosAvulsos,
                comLote,
                chequeAvista,
                chequeAprazo,
                inicio,
                fim,
                considerarDataOriginal,
                status, chequeVO, true, mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);

        while (rs.next()) {
            // objeto a ser utilizado na tela
            ChequeTO novo = new ChequeTO();
            novo.setMatricula(rs.getString("matricula"));
            novo.setCodigo(rs.getInt("codigocheque"));
            novo.setNomePagador(UteisValidacao.emptyString(rs.getString("nomepagador")) ? rs.getString("nomepessoa") : rs.getString("nomepagador"));
            novo.setCpfPagador(rs.getString("cfp"));
            novo.setNumeroBanco(rs.getString("banco"));
            novo.setAgencia(rs.getString("agencia"));
            novo.setConta(rs.getString("conta"));
            novo.setNumero(rs.getString("numero"));
            novo.setDataLancamento(rs.getDate("datalancamento") == null ? rs.getDate("mclan") : rs.getDate("datalancamento"));
            novo.setDataCompensacao(rs.getDate("datacompesancao"));
            novo.setDataOriginal(rs.getDate("dataoriginal"));
            novo.setValor(rs.getDouble("valortotal"));
            novo.setRecibo(rs.getInt("recibopagamento"));
            novo.setMovConta(rs.getInt("movconta"));
            novo.setCodigosComposicao(validarComposicao(rs.getString("composicao")));
            novo.setNomeNoCheque(rs.getString("nomenocheque"));
            novo.setCodigoPessoa(rs.getInt("pessoa"));

            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(rs.getInt("empresa"));
            empresaVO.setNome(rs.getString("nomeEmpresa"));
            novo.setEmpresa(empresaVO);

            if (novo.getCodigosComposicao() != null && !novo.getCodigosComposicao().equals("")) {
                Boolean presente = false;
                for (Integer codigo : chequeComposicao) {
                    if (novo.getCodigo() == codigo) {
                        presente = true;
                        break;
                    }
                }
                if (!presente) {
                    String[] codigos1 = novo.getCodigosComposicao().split(",");
                    for (String codComposicao : codigos1) {
                        chequeComposicao.add(Integer.parseInt(codComposicao));
                    }
                } else {
                    continue;
                }

            }

            novo.setAtivo(!rs.getString("situacao").equals("CA"));
            novo.setDevolvido(rs.getString("situacao").equals("DV"));

            HistoricoCheque historicoCheque = new HistoricoCheque(con);
            Lote lote = new Lote(con);
            historicoCheque.getContaLoteCheque(novo, false);
            novo.setLoteAvulso(lote.consultarLoteAvulso(novo.getCodigo()));
            if (UteisValidacao.emptyNumber(novo.getNumeroLote())) {
                novo.setNumeroLote(lote.consultarPorCheque(novo.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
            }

            if (novo.getDataFim() != null && novo.getLoteAvulso() == 0) {
                novo.setNumeroLote(0);
                novo.setContaContido("");
                novo.setCodigoContaContido(0);
            }

            Integer codigoMovPagamento = rs.getInt("codigoMovPagamento");
            if(!UteisValidacao.emptyNumber(codigoMovPagamento)){
                MovPagamento movPagamentoDAO = new MovPagamento(con);
                MovPagamentoVO movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(codigoMovPagamento, Uteis.NIVELMONTARDADOS_PARCELA);

                MovPagamentoVO movPagamentoVOAjustadoAlunos = (MovPagamentoVO) movPagamentoVO.getClone(false);
                movPagamentoVOAjustadoAlunos.getPagamentoMovParcelaVOs().clear();

                // Adicionar o MovPagamentoParcelasVOs, porquê está nele a lista dos alunos donos das parcelas pagas.
                if(movPagamentoVO != null && movPagamentoVO.getPagamentoMovParcelaVOs() != null && movPagamentoVO.getPagamentoMovParcelaVOs().size() > 0){
                    List<Integer> listaIdPessoasJaAdicionadas = new ArrayList<>();
                    // For para não adicionar aluno repetido no caso de 1 pagamento, pagar mais de 1 parcela do mesmo aluno
                    for (PagamentoMovParcelaVO pagamentoMovParcelaVO : movPagamentoVO.getPagamentoMovParcelaVOs()) {
                        if (!listaIdPessoasJaAdicionadas.contains(pagamentoMovParcelaVO.getMovParcela().getPessoa().getCodigo())) {
                            movPagamentoVOAjustadoAlunos.getPagamentoMovParcelaVOs().add(pagamentoMovParcelaVO);
                            listaIdPessoasJaAdicionadas.add(pagamentoMovParcelaVO.getMovParcela().getPessoa().getCodigo());
                        }
                    }
                }

                novo.setMovPagamentoVO(movPagamentoVOAjustadoAlunos);
            }

            if ((status != null
                    && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                    && !UteisValidacao.emptyString(novo.getContaContido()))
                    || (status != null
                    && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                    && UteisValidacao.emptyString(novo.getContaContido()))) {
                continue;
            }

            if (!codigoLoteFiltro.trim().isEmpty()
                    && !codigosListaFiltro.contains(Integer.valueOf(novo.getLoteAvulso()))
                    && !codigosListaFiltro.contains(Integer.valueOf(novo.getNumeroLote()))) {
                continue;
            }

            if (!UteisValidacao.emptyString(nomeConta) && !novo.getContaContido().toLowerCase().contains(nomeConta.toLowerCase())) {
                continue;
            }
            lista.add(novo);
        }
        return lista;
    }

    public List selecionarListaFormaPagamento(FormaPagamentoVO fp,
                                              List<MovPagamentoVO> pagamentos,
                                              Boolean comLote,
                                              boolean chequeAvista, boolean chequeAprazo,
                                              Date iniciocompensacao, Date finalcompensacao, Boolean considerarDataOriginal,
                                              StatusMovimentacaoEnum status,
                                              String nomeConta,
                                              String codigoLoteFiltro, ChequeVO chequeVO,
                                              List<Integer> codigosListaFiltro, boolean mostrarCancelados, Date dataInicioMovimentacaoInicio, Date dataInicioMovimentacaoFim, String horaInicioMovimentacaoInicio, String horaInicioMovimentacaoFim) throws Exception {
        String codigos = "";
        String codigosAvulsos = "";
        if (fp.getTipoFormaPagamento().equals("CA") || fp.getTipoFormaPagamento().equals("CH")) {
            for (MovPagamentoVO mp : pagamentos) {
                if (mp.getFormaPagamento().getCodigo().equals(fp.getCodigo()) && mp.isPagamentoAvulso()) {
                    codigosAvulsos = codigosAvulsos + ("," + mp.getCodigo());
                } else if (mp.getFormaPagamento().getCodigo().equals(fp.getCodigo()) && !mp.isPagamentoAvulso()) {
                    codigos = codigos + ("," + mp.getCodigo());
                }
            }
        }

        Map<Integer, String> mapaCPF = new HashMap<>();

        if (fp.getTipoFormaPagamento().equals("CH")) {
            List<ChequeTO> lista = consultarPagamentoChequesLista(false,
                    codigos.replaceFirst(",", ""),
                    codigosAvulsos.replaceFirst(",", ""),
                    comLote,
                    chequeAvista,
                    chequeAprazo,
                    iniciocompensacao,
                    finalcompensacao,
                    considerarDataOriginal,
                    status,
                    nomeConta,
                    codigoLoteFiltro,
                    chequeVO,
                    codigosListaFiltro,
                    mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);

            for (ChequeTO cq: lista) {
                if (!UteisValidacao.emptyNumber(cq.getCodigoPessoa()) &&
                        UteisValidacao.emptyString(cq.getCpfPagador())) {
                    String cpf = mapaCPF.get(cq.getCodigoPessoa());
                    if (cpf == null) {
                        cpf = obterCPFResponsavelAlunoMenorIdade(cq.getCodigoPessoa());
                        mapaCPF.put(cq.getCodigoPessoa(), cpf);
                    }
                    cq.setCpfPagador(cpf);
                }
            }

            return lista;
        } else if (fp.getTipoFormaPagamento().equals("CA")) {
            List<CartaoCreditoTO> lista = consultarListaCartoes(codigos.replaceFirst(",", ""),
                    codigosAvulsos.replaceFirst(",", ""),
                    iniciocompensacao, finalcompensacao,
                    comLote,
                    considerarDataOriginal,
                    status,
                    nomeConta,
                    codigoLoteFiltro,
                    codigosListaFiltro,
                    mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim);

            for (CartaoCreditoTO cc: lista) {
                if (!UteisValidacao.emptyNumber(cc.getCodigoPessoa()) &&
                        UteisValidacao.emptyString(cc.getCpfPagador())) {
                    String cpf = mapaCPF.get(cc.getCodigoPessoa());
                    if (cpf == null) {
                        cpf = obterCPFResponsavelAlunoMenorIdade(cc.getCodigoPessoa());
                        mapaCPF.put(cc.getCodigoPessoa(), cpf);
                    }
                    cc.setCpfPagador(cpf);
                }

                // Adicionar o MovPagamentoParcelasVOs, porquê está nele a lista dos alunos donos das parcelas pagas.
                for (MovPagamentoVO mp : pagamentos) {
                    if(cc.getMovPagamentoVO() != null && cc.getMovPagamentoVO().getCodigo().equals(mp.getCodigo()) && mp.getPagamentoMovParcelaVOs().size() > 0){
                        List<Integer> listaIdPessoasJaAdicionadas = new ArrayList<>();
                        int posicaoLista = 0;
                        // For para não adicionar aluno repetido no caso de 1 pagamento, pagar mais de 1 parcela do mesmo aluno
                        for (PagamentoMovParcelaVO pagamentoMovParcelaVO : mp.getPagamentoMovParcelaVOs()) {
                            if (!listaIdPessoasJaAdicionadas.contains(pagamentoMovParcelaVO.getMovParcela().getPessoa().getCodigo())) {
                                cc.getMovPagamentoVO().getPagamentoMovParcelaVOs().add(mp.getPagamentoMovParcelaVOs().get(posicaoLista));
                                listaIdPessoasJaAdicionadas.add(pagamentoMovParcelaVO.getMovParcela().getPessoa().getCodigo());
                            }
                            posicaoLista++;
                        }
                        cc.getMovPagamentoVO().setFormaPagamento(mp.getFormaPagamento());
                        cc.setEmpresa(mp.getEmpresa());
                        cc.setDataLancamento(mp.getDataLancamento());
                    }
                }
            }

            return lista;
        } else {
            List<MovPagamentoVO> lista = new ArrayList<>();
            for (MovPagamentoVO mp : pagamentos) {
                if (mp.getFormaPagamento().getCodigo().equals(fp.getCodigo())) {

                    if ((status != null
                            && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                            && !UteisValidacao.emptyString(mp.getContaFinanceiro()))
                            || (status != null
                            && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                            && UteisValidacao.emptyString(mp.getContaFinanceiro()))) {
                        continue;
                    }

                    if (!UteisValidacao.emptyString(nomeConta) && !mp.getContaFinanceiro().toLowerCase().contains(nomeConta.toLowerCase())) {
                        continue;
                    }

                    if(nonNull(status) && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS) && dataInicioMovimentacaoFim != null && dataInicioMovimentacaoInicio != null){
                        String horaIni = UteisValidacao.emptyString(horaInicioMovimentacaoInicio) ? "00:00:00" : horaInicioMovimentacaoInicio;
                        String horaFim = UteisValidacao.emptyString(horaInicioMovimentacaoFim) ? "23:59:59" : horaInicioMovimentacaoFim;
                        if(mp.getDataMovimento() != null && Calendario.entreComHora(mp.getDataMovimento(), Calendario.getDataComHora(dataInicioMovimentacaoInicio, horaIni), Calendario.getDataComHora(dataInicioMovimentacaoFim, horaFim))){
                            lista.add(mp);
                        }
                    }else {
                        lista.add(mp);
                    }
                }
            }

            for (MovPagamentoVO mp: lista) {
                if (!UteisValidacao.emptyNumber(mp.getPessoa().getCodigo()) &&
                        UteisValidacao.emptyString(mp.getCpfPagador())) {
                    String cpf = mapaCPF.get(mp.getPessoa().getCodigo());
                    if (cpf == null) {
                        cpf = obterCPFResponsavelAlunoMenorIdade(mp.getPessoa().getCodigo());
                        mapaCPF.put(mp.getPessoa().getCodigo(), cpf);
                    }
                    mp.setCpfPagador(cpf);
                }
                //preencher antecipacoes de cartões de DÉBITO aqui... As de crédito são preenchidas no método de consultar lista de cartões consultarListaCartoes
                if (mp.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase("CD") && !UteisValidacao.emptyString(mp.getAutorizacaoCartao())) {
                    preencherAntecipacoesDeDebito(mp);
                }
            }

            return lista;
        }
    }


    public ResultSet consultarCartaoPorPeriodoCompensacao(String codigos,
                                                          String codigosAvulsos,
                                                          Date compi, Date compf,
                                                          Boolean pesquisarPorLote,
                                                          boolean considerarDataOriginal,
                                                          boolean lista,
                                                          StatusMovimentacaoEnum status, boolean composicao, boolean mostrarCancelados,
                                                          Date dataInicioMovimentacaoInicio, Date dataInicioMovimentacaoFim, String horaInicioMovimentacaoInicio, String horaInicioMovimentacaoFim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        if (lista) {
            sql.append(" cc.valor, p.codigo as pessoa, op.codigo as opcodigo, op.descricao as operadora, cc.codigo as codigocartao, cli.matricula, a.nome as adquirente, a.codigo as adcodigo, \n");
            sql.append(" mp.nomepagador,p.nome as nomepessoa, p.cfp, mp.datalancamento, mc.datalancamento as mclan, cc.datacompesancao, cc.dataoriginal, cc.valortotal, mp.recibopagamento, mp.autorizacaocartao, \n");
            sql.append(" mp.numerounicotransacao, mp.nsu, cc.composicao, cc.movconta, nrParcelaCartaoCredito, cc.situacao, mc.autorizacaocartao as mcaut, \n");
            sql.append(" mp.empresa, ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.valorDescontadoAntecipacao, ei.taxaCalculadaAntecipacao, cc.movpagamento, \n");
            sql.append(" cc.alterouDataRecebimentoZWAutomaticamente, cc.dataPgtoOriginalZWAntesDaAlteracaoAutomatica \n");
        } else {
            if (!composicao){
                sql.append("sum(round(cc.valortotal::numeric, 2)) as totalvalor, count(cc.codigo) as total \n");
            }else {
                sql.append("round(cc.valortotal::numeric, 2) as totalvalor, cc.composicao as composicao,cc.codigo as codigocartao,cc.movpagamento as movpagamento \n");
            }
        }

        sql.append(" from CartaoCredito cc \n");

        if(nonNull(status) && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS) && dataInicioMovimentacaoInicio != null && dataInicioMovimentacaoFim != null){
            sql.append(" left join historicocartao hc on hc.cartao = cc.codigo \n");
        }

        if (lista) {
            sql.append(" left join movpagamento mp on mp.codigo = cc.movpagamento \n");
            sql.append(" left join operadoracartao op on mp.operadoracartao = op.codigo \n");
            sql.append(" left join movconta mc on mc.codigo = cc.movconta \n");
            sql.append(" left join pessoa p on mp.pessoa = p.codigo or p.codigo = mc.pessoa \n");
            sql.append(" left join cliente cli on cli.pessoa = p.codigo \n");
            sql.append(" left join adquirente a on a.codigo = mp.adquirente \n");
        }

       if (considerarDataOriginal) {
           sql.append(" left join extratodiarioitem ei on ei.codigo = (\n");
           sql.append(" select max(ei2.codigo) from extratodiarioitem ei2\n");
           sql.append(" where ei2.codigocartaocredito IS NOT NULL and ei2.codigocartaocredito = cc.codigo\n");
           sql.append(" and ei2.datapgtooriginalantesdaantecipacao is not null\n");
           sql.append(" and ei2.antecipacao is true\n");
           sql.append(" and ei2.tipoconciliacao = ").append(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()).append(" \n");
           sql.append(")\n");
       } else {
           sql.append(" left join extratodiarioitem ei on ei.codigo = (select max(ei2.codigo) from extratodiarioitem ei2 where ei2.codigocartaocredito IS NOT NULL and ei2.codigocartaocredito = cc.codigo and ei2.tipoconciliacao =  " + TipoConciliacaoEnum.PAGAMENTOS.getCodigo() + ") \n");
        }

        sql.append(" where (");
        if(!UteisValidacao.emptyString(codigos)){
            sql.append("cc.movpagamento in (").append(codigos).append(") ").append(UteisValidacao.emptyString(codigosAvulsos) ? "" : " or ");
        }
        if(!UteisValidacao.emptyString(codigosAvulsos)){
            sql.append("cc.movconta in (").append(codigosAvulsos).append(") ");
        }
        sql.append(") ");

        if(!mostrarCancelados) {
            sql.append(" and cc.situacao not like 'CA' ");
        }

        if (compi != null) {
            if (considerarDataOriginal) {
                sql.append(" and ((ei.datapgtooriginalantesdaantecipacao BETWEEN '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'").append(" and '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' )");
                sql.append(" or (cc.dataoriginal >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
                sql.append(" and cc.dataoriginal <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' and ei.datapgtooriginalantesdaantecipacao is null)");
                sql.append(" or (cc.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'");
                sql.append(" and cc.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59' and cc.dataoriginal is null and ei.datapgtooriginalantesdaantecipacao is null)) ");
            } else {
                sql.append(" and cc.datacompesancao >= '").append(Uteis.getDataJDBC(compi)).append(" 00:00:00'\n");
                sql.append(" and cc.datacompesancao <= '").append(Uteis.getDataJDBC(compf)).append(" 23:59:59'\n");
            }
        }

        if (pesquisarPorLote != null || status != null) {
            if ((pesquisarPorLote != null && pesquisarPorLote) || (status != null && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS))) {
                sql.append(" and cc.codigo IN (SELECT distinct(cartao) from chequecartaolote where cartao is not null)\n");
            }

            if ((pesquisarPorLote != null && !pesquisarPorLote) || (status != null && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS))) {
                sql.append(" and cc.codigo not IN (SELECT distinct(cartao) from chequecartaolote where cartao is not null)\n");
            }
        }
        if(nonNull(status) && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS) && dataInicioMovimentacaoInicio != null && dataInicioMovimentacaoFim != null) {
            String horaIni = UteisValidacao.emptyString(horaInicioMovimentacaoInicio) ? "00:00:00" : horaInicioMovimentacaoInicio;
            String horaFim = UteisValidacao.emptyString(horaInicioMovimentacaoFim) ? "23:59:59" : horaInicioMovimentacaoFim;
            sql.append(" and hc.datainicio between '")
                    .append(Uteis.getDataJDBC(dataInicioMovimentacaoInicio)).append(" ")
                    .append(horaIni).append("' and '")
                    .append(Uteis.getDataJDBC(dataInicioMovimentacaoFim)).append(" ")
                    .append(horaFim).append("'");
        }
        if (lista) {
            sql.append(" order by p.nome");
        }else {
            if (!composicao) {
                sql.append(" \n");
            } else {
                sql.append("order by  movpagamento\n");
            }
        }
        Statement stm = con.createStatement();
        return stm.executeQuery(sql.toString());
    }

    public List<CartaoCreditoTO> consultarListaCartoes(String codigos,
                                                       String codigosAvulsos,
                                                       Date compi, Date compf,
                                                       Boolean pesquisarPorLote,
                                                       boolean considerarDataOriginal,
                                                       StatusMovimentacaoEnum status,
                                                       String nomeConta,
                                                       String codigoLoteFiltro,
                                                       List<Integer> codigosListaFiltro,
                                                       boolean mostrarCancelados, Date dataInicioMovimentacaoInicio, Date dataInicioMovimentacaoFim, String horaInicioMovimentacaoInicio, String horaInicioMovimentacaoFim) throws Exception {
        try {
            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);
            CacheControl.toggleCache(FormaPagamento.class, true);
            CacheControl.toggleCache(Adquirente.class, true);
            CacheControl.toggleCache(OperadoraCartao.class, true);
            CacheControl.toggleCache(Banco.class, true);
            CacheControl.toggleCache(TipoRemessa.class, true);


            try (ResultSet rs = consultarCartaoPorPeriodoCompensacao(codigos, codigosAvulsos, compi, compf, pesquisarPorLote, considerarDataOriginal, true, status,false, mostrarCancelados, dataInicioMovimentacaoInicio, dataInicioMovimentacaoFim, horaInicioMovimentacaoInicio, horaInicioMovimentacaoFim)) {

                Empresa empresaDAO = new Empresa(con);

                Map<Integer, EmpresaVO> mapEmpresas = empresaDAO.obterMapaEmpresas();

                List<CartaoCreditoTO> lista = new ArrayList<>();
                cartaoComposicao = new ArrayList<>();
                while (rs.next()) {
                    // objeto a ser utilizado na tela
                    CartaoCreditoTO novo = new CartaoCreditoTO();
                    novo.setMatricula(rs.getString("matricula"));
                    novo.setCodigo(rs.getInt("codigocartao"));
                    novo.setAdquirente(rs.getString("adquirente"));
                    novo.setAdquirenteCod(rs.getInt("adcodigo"));
                    novo.setOperadora(rs.getString("operadora"));
                    novo.setOperadoraCodigo(rs.getInt("opcodigo"));

                    if (!UteisValidacao.emptyNumber(rs.getInt("movpagamento"))) {
                        MovPagamento movPagamentoDAO;
                        try {
                            movPagamentoDAO = new MovPagamento(con);
                            novo.setMovPagamentoVO(movPagamentoDAO.consultarPorChavePrimaria(rs.getInt("movpagamento"), Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS));
                        } catch (Exception ignored) {
                        } finally {
                            movPagamentoDAO = null;
                        }
                    }

                    novo.setNomePagador(UteisValidacao.emptyString(rs.getString("nomepagador")) ? rs.getString("nomepessoa") : rs.getString("nomepagador"));
                    novo.setNomePessoa(rs.getString("nomepessoa"));
                    novo.setCpfPagador(rs.getString("cfp"));
                    novo.setDataLancamento(rs.getDate("datalancamento") == null ? rs.getDate("mclan") : rs.getDate("datalancamento"));
                    novo.setDataCompensacao(rs.getDate("datacompesancao"));
                    novo.setDataOriginal(rs.getDate("dataoriginal"));
                    novo.setValor(rs.getDouble("valortotal"));
                    novo.setRecibo(rs.getInt("recibopagamento"));
                    novo.setAutorizacao(rs.getString("autorizacaocartao") == null ? rs.getString("mcaut") : rs.getString("autorizacaocartao"));
                    novo.setNsu(rs.getString("nsu"));
                    novo.setAntecipacao(rs.getBoolean("antecipacao"));
                    novo.setDataPgtoOriginalAntesDaAntecipacao(rs.getTimestamp("datapgtooriginalantesdaantecipacao"));

                    //em caso de antecipação, setar data de compensação original no campo dataOriginal
                    if (novo.getDataPgtoOriginalAntesDaAntecipacao() != null) {
                        novo.setDataOriginal(novo.getDataPgtoOriginalAntesDaAntecipacao());
                    }

                    try {
                        novo.setAlterouDataRecebimentoZWAutomaticamente(rs.getBoolean("alterouDataRecebimentoZWAutomaticamente"));
                    } catch (Exception ex) {
                    }

                    try {
                        novo.setDataPgtoOriginalZWAntesDaAlteracaoAutomatica(rs.getTimestamp("dataPgtoOriginalZWAntesDaAlteracaoAutomatica"));
                    } catch (Exception ex) {
                    }


                    novo.setValorDescontadoAntecipacao(rs.getDouble("valorDescontadoAntecipacao"));
                    novo.setTaxaCalculadaAntecipacao(rs.getDouble("taxaCalculadaAntecipacao"));

                    ResultSet rslote = criarConsulta("select max(lote) as lote from chequecartaolote where cartao in (" + rs.getInt("codigocartao") +
                            (UteisValidacao.emptyString(rs.getString("composicao")) ? "" : ("," + rs.getString("composicao"))) + ")", con);
                    if (rslote.next()) {
                        novo.setNumeroLote(rslote.getInt("lote"));
                    }

                    if (!codigoLoteFiltro.trim().isEmpty() && !codigosListaFiltro.contains(novo.getNumeroLote())) {
                        continue;
                    }
                    novo.setCodigosComposicao(rs.getString("composicao"));
                    novo.setCodigoPessoa(rs.getInt("pessoa"));
                    novo.setMovConta(rs.getInt("movconta"));
                    novo.setNrVezes(rs.getInt("nrParcelaCartaoCredito"));
                    if (rs.getString("situacao").equals("CA")) {
                        novo.setAtivo(false);
                    }
                    CartaoCredito cartaoCredito = new CartaoCredito(con);
                    cartaoCredito.getContaLoteCartao(novo, novo.getNumeroLote());

                    Integer codEmpresa = rs.getInt("empresa");
                    novo.setEmpresa(mapEmpresas.get(codEmpresa));

                    if (novo.getCodigosComposicao() != null && !novo.getCodigosComposicao().isEmpty()) {
                        boolean presente = false;
                        for (Integer codigo : cartaoComposicao) {
                            if (novo.getCodigo().intValue() == codigo) {
                                presente = true;
                                break;
                            }
                        }
                        if (!presente) {
                            String[] codigos1 = novo.getCodigosComposicao().split(",");
                            for (String codComposicao : codigos1) {
                                cartaoComposicao.add(Integer.parseInt(codComposicao));
                            }
                        } else {
                            continue;
                        }

                    }

                    if ((status != null
                            && status.equals(StatusMovimentacaoEnum.NAO_MOVIMENTADOS)
                            && !UteisValidacao.emptyString(novo.getContaContido()))
                            || (status != null
                            && status.equals(StatusMovimentacaoEnum.MOVIMENTADOS)
                            && UteisValidacao.emptyString(novo.getContaContido()))) {
                        continue;
                    }

                    if (!UteisValidacao.emptyString(nomeConta) && !novo.getContaContido().toLowerCase().contains(nomeConta.toLowerCase())) {
                        continue;
                    }

                    lista.add(novo);
                }
                return lista;
            }
        } finally {
            CacheControl.clear();
        }
    }

    public String obterCPFResponsavelAlunoMenorIdade(Integer idPessoa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("p.cfp, \n");
        sql.append("p.cpfmae, \n");
        sql.append("p.cpfpai, \n");
        sql.append("c.pessoaresponsavel ,\n");
        sql.append("pres.cfp as cpfpessoaresp \n");
        sql.append("from pessoa p \n");
        sql.append("inner join cliente c on c.pessoa = p.codigo \n");
        sql.append("left join pessoa pres on pres.codigo =  c.pessoaresponsavel \n");
        sql.append("where p.codigo = ").append(idPessoa).append(" \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    if (!UteisValidacao.emptyString(rs.getString("cfp"))) {
                        return rs.getString("cfp");
                    } else if (!UteisValidacao.emptyString(rs.getString("cpfmae"))) {
                        return rs.getString("cpfmae");
                    } else if (!UteisValidacao.emptyString(rs.getString("cpfpai"))) {
                        return rs.getString("cpfpai");
                    } else if (!UteisValidacao.emptyString(rs.getString("cpfpessoaresp"))) {
                        return rs.getString("cpfpessoaresp");
                    }
                }
            }
        }
        return "";
    }

    public void preencherAntecipacoesDeDebito(MovPagamentoVO mp) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" select ei.antecipacao, ei.datapgtooriginalantesdaantecipacao, ei.taxaCalculadaAntecipacao \n");
            sql.append(" from extratodiarioitem ei \n");
            sql.append(" inner join movpagamento m on m.autorizacaocartao = ei.autorizacao \n");
            sql.append(" where tipoconciliacao = ").append(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()).append(" \n");
            sql.append(" and ei.autorizacao = '").append(mp.getAutorizacaoCartao()).append("' ").append("limit 1");

            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sql.toString());
            while (rs.next()) {
                mp.setAntecipacao(rs.getBoolean("antecipacao"));
                mp.setDataPgtoOriginalAntesDaAntecipacao(rs.getTimestamp("dataPgtoOriginalAntesDaAntecipacao"));
                mp.setTaxaAntecipacao(rs.getDouble("taxaCalculadaAntecipacao"));
            }
        } catch (Exception ex) {
        }
    }

}

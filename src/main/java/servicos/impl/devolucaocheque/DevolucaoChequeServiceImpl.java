package servicos.impl.devolucaocheque;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import controle.arquitetura.SuperControle;
import controle.financeiro.ConfiguracaoFinanceiroControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.financeiro.*;
import servicos.interfaces.devolucaocheque.DevolucaoChequeService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.basico.ColaboradorVO;

public class DevolucaoChequeServiceImpl extends SuperEntidade implements DevolucaoChequeService {


    public DevolucaoChequeServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public DevolucaoChequeServiceImpl() throws Exception {
        super();
    }

    public void voltarChequeEstadoNatural(String chave,
                                          String urlNotificar,
                                          Integer empresa,
                                          List<ChequeVO> cheques,
                                          ConfiguracaoFinanceiroVO cfg,
                                          UsuarioVO usuario, Integer contaOrigem


    //        List<ChequeVO> cheques, Integer contaOrigem
    ) throws Exception{
        ContaVO contaVO = getFacade().getFinanceiro().getConta().consultarPorChavePrimaria(contaOrigem, Uteis.NIVELMONTARDADOS_TODOS);
        if(contaVO.getTipoConta() != null
                && contaVO.getTipoConta().getComportamento() != null
                && contaVO.getTipoConta().getComportamento().equals(ComportamentoConta.DEVOLUCOES)){
            for(ChequeVO ch : cheques){
                ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT vendaavulsa, situacao, codigo from movproduto where chequeDevolucao = " + ch.getCodigo(), con);
                while(rs.next()){
                    if(rs.getString("situacao").equals("PG")
                            || UteisValidacao.emptyNumber(rs.getInt("vendaavulsa"))){
                        continue;
                    }
                    MovParcelaVO movParcelaVO = getFacade().getMovParcela().consultarPorMovProduto(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_MINIMOS);
                    getFacade().getMovParcela().excluir(movParcelaVO);

                    MovProdutoVO movProdutoVO = getFacade().getMovProduto().consultarPorChavePrimaria(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_MINIMOS);
                    getFacade().getMovProduto().excluir(movProdutoVO);

                    VendaAvulsaVO vendaavulsa = getFacade().getVendaAvulsa().consultarPorChavePrimaria(rs.getInt("vendaavulsa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    getFacade().getVendaAvulsa().excluir(vendaavulsa);

                    executarConsulta("update cheque set situacao = 'EA' where codigo in (" + ch.getObterTodosChequesComposicao()+")", con);

                    Integer movpagamento = 0;
                    Integer movconta = 0;
                    String produtosPagos = "";
                    ResultSet consultaCheque = criarConsulta("SELECT movconta, movpagamento, produtospagos from cheque where codigo = " + ch.getCodigo(), con);
                    if(consultaCheque.next()){
                        movconta = consultaCheque.getInt("movconta");
                        movpagamento = consultaCheque.getInt("movpagamento");
                        produtosPagos = consultaCheque.getString("produtospagos");


                    }

                    if(!UteisValidacao.emptyNumber(movpagamento) && !UteisValidacao.emptyNumber(movconta)){
                        executarConsulta("update movconta set descricao = 'Transferência entre Contas'," +
                                " tipooperacao = "+TipoOperacaoLancamento.TRANSFERENCIA.getCodigo()+
                                " where codigo = " + movconta, con);
                        executarConsulta("update cheque set movconta = null where codigo in (" + ch.getObterTodosChequesComposicao()+")", con);

                        if(!produtosPagos.trim().isEmpty()){
                            ChequeVO chProdutos = new ChequeVO();
                            chProdutos.setProdutosPagos(produtosPagos);
                            removerMensagemCatraca(chave, urlNotificar, empresa, chProdutos, cfg);

                        }


                    }


                }
            }
        }

    }

    public void removerChequeContaDevolucao(List<MovParcelaVO> parcelas) throws Exception{

        ConfiguracaoFinanceiroVO confFinanceiro = null;
        try {
            ConfiguracaoFinanceiro configDAO = new ConfiguracaoFinanceiro(con);
            confFinanceiro = configDAO.consultar();
            configDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
        }

        for(MovParcelaVO parcela : parcelas){




            StringBuilder sql = new StringBuilder();
            sql.append("select ch.valortotal, ch.codigo, ch.movconta from movprodutoparcela mpp \n");
            sql.append("inner join movproduto mp on mp.codigo = mpp.movproduto \n");
            sql.append("inner join produto p on p.codigo = mp.produto \n");
            sql.append("inner join cheque ch on ch.codigo = mp.chequedevolucao \n");
            sql.append("where movparcela = ").append(parcela.getCodigo());
            ResultSet rs = criarConsulta(sql.toString(), con);

            while(rs.next()){
                if(UteisValidacao.emptyNumber(rs.getInt("movconta"))){ //parcela de edição de pagamento
                    continue;
                }

                MovConta movConta1DAO = new MovConta(con);
                MovContaVO movcontaSaida = movConta1DAO.consultarPorCodigo(rs.getInt("movconta"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                movConta1DAO = null;

                ResultSet historico = criarConsulta("select mc.conta, hc.codigo, hc.cheque, ch.produtospagos from historicocheque hc\n" +
                        " inner join movconta mc on mc.codigo = hc.movconta inner join cheque ch on hc.cheque = ch.codigo where datafim is null and cheque = " + rs.getInt("codigo"), con);
                while(historico.next()){

                    MovContaVO movConta = (MovContaVO) movcontaSaida.getClone(true);
                    movConta.setTemLote(false);
                    movConta.setDescricao("Pagamento de parcela de cheque devolvido");
                    movConta.setContaOrigem(null);
                    movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.TRANSFERENCIA);
                    movConta.setApresentarNoCaixa(false);
                    movConta.setMovContaRateios(new ArrayList<MovContaRateioVO>());
                    movConta.setLiquido(false);
                    movConta.setValor(parcela.getValorParcela());
                    movConta.setDataQuitacao(Calendario.igual(movConta.getDataVencimento(), Calendario.hoje()) ?
                            Calendario.hoje() : movConta.getDataVencimento());
                    movConta.setValorLiquido(0.0);
                    movConta.setContaVO(new ContaVO());
                    movConta.getContaVO().setCodigo(historico.getInt("conta"));
                    movConta.setTemLote(false);
                    movConta.setLote(null);
                    MovContaRateioVO rateio = new MovContaRateioVO();
                    rateio.setTipoES(TipoES.SAIDA);
                    rateio.setDescricao(movConta.getDescricao());
                    rateio.setValor(parcela.getValorParcela());
                    movConta.getMovContaRateios().add(rateio);

                    MovConta movContaDAO = new MovConta(con);
                    movContaDAO.incluirSemCommit(movConta, 0, false, null, false);
                    movContaDAO = null;

                    HistoricoChequeVO histpagamento = new HistoricoChequeVO();
                    histpagamento.setCheque(new ChequeVO());
                    histpagamento.getCheque().setCodigo(historico.getInt("cheque"));
                    histpagamento.setDataInicio(Calendario.hoje());
                    histpagamento.setDataFim(Calendario.hoje());
                    histpagamento.setMovConta(movConta);
                    histpagamento.setStatus(StatusCheque.CHEQUE_DEVOLVIDO_PAGO);

                    HistoricoCheque historicoChequeDAO = new HistoricoCheque(con);
                    historicoChequeDAO.incluirSemCommit(histpagamento);
                    historicoChequeDAO = null;

                    if(null != historico.getString("produtospagos") && null != confFinanceiro){
                        ChequeVO chq = new ChequeVO();
                        chq.setProdutosPagos(historico.getString("produtospagos"));
                        removerMensagemCatraca( chq, confFinanceiro);
                    }
                }
            }
        }

    }

    public boolean processarMovimentacaoCheques(String chave,
                                                String urlNotificar,
                                                Integer empresa,
                                                List<ChequeVO> cheques,
                                                MovContaVO mov,
                                                ConfiguracaoFinanceiroVO cfg,
                                                int caixa,
                                                UsuarioVO usuario, ContaVO origem) throws Exception {
        MovContaVO movConta = (MovContaVO) mov.getClone(true);
        movConta.setTemLote(false);
        Integer pessoa = 0;
        String nrcheques = "";
        for(ChequeVO ch : cheques){
            ResultSet resultSet = criarConsulta("select numero, movpagamento.pessoa, cheque.produtospagos  from cheque " +
                    " inner join movpagamento on movpagamento.codigo = cheque.movpagamento " +
                    " where cheque.codigo = " + ch.getCodigo(), con);
            if(resultSet.next()){
                nrcheques += ";"+resultSet.getString("numero");
                pessoa = resultSet.getInt("pessoa");
                ch.setNumero(resultSet.getString("numero"));
                ch.setProdutosPagos(resultSet.getString("produtospagos"));
            }
        }
        movConta.setPessoaVO(new PessoaVO());
        movConta.getPessoaVO().setCodigo(pessoa);
        movConta.setDescricao((cheques.size() > 1 ? "Cheques devolvidos - " : "Cheque devolvido - ")+nrcheques.replaceFirst(";",""));
        movConta.setContaOrigem(null);
        movConta.setTipoOperacaoLancamento(TipoOperacaoLancamento.DEVOLUCAO_CHEQUE);
        movConta.setApresentarNoCaixa(false);
        movConta.setMovContaRateios(new ArrayList<MovContaRateioVO>());
        movConta.setLiquido(false);
        movConta.setDataQuitacao(Calendario.igual(movConta.getDataVencimento(), Calendario.hoje()) ?
                Calendario.hoje() : movConta.getDataVencimento());
        movConta.setValorLiquido(0.0);
        movConta.setContaVO(origem);
        movConta.setTemLote(false);
        movConta.setLote(null);
        MovContaRateioVO rateio = new MovContaRateioVO();
        rateio.setTipoES(TipoES.SAIDA);
        rateio.setDescricao(movConta.getDescricao());
        rateio.setValor(movConta.getValor());
        rateio.setFormaPagamentoVO(mov.getMovContaRateios().get(0).getFormaPagamentoVO());
        if(cfg.getPlanoContasDevolucao() != null && !UteisValidacao.emptyNumber(cfg.getPlanoContasDevolucao().getCodigo())){
            rateio.setPlanoContaVO(cfg.getPlanoContasDevolucao());
            rateio.setCentroCustoVO(cfg.getCentroCustoDevolucao());
        }

        movConta.getMovContaRateios().add(rateio);
        getFacade().getFinanceiro().getMovConta().incluirSemCommit(movConta, caixa,
                false, ComportamentoConta.DEVOLUCOES);
        for (ChequeVO ch : cheques) {
            executarConsulta("update cheque set movconta = " + movConta.getCodigo()
                            + " where codigo in (" + ch.getObterTodosChequesComposicao()+")",
                    con);
        }
        Date dataMovimentacao = movConta.getDataQuitacao() != null ? movConta.getDataQuitacao() : Calendario.hoje();
        gerarParcelaEmAberto(cheques, empresa, usuario, dataMovimentacao);
        gerarMensagemCatraca(chave, urlNotificar, empresa, cheques, mov, cfg, caixa, usuario);
        return true;
    }

    public void gerarParcelaEmAberto(List<ChequeVO> cheques, Integer empresa, UsuarioVO usuario, Date dataMovimentacao) throws Exception {
        for (ChequeVO cheque : cheques) {
            VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
            ResultSet rs = criarConsulta("select p.nome, cli.codigo,co.codigo as colaborador, p.codigo as pcodigo from movpagamento mp " +
                    " inner join cheque ch on ch.movpagamento = mp.codigo " +
                    " inner join pessoa p on p.codigo = mp.pessoa " +
                    " left join cliente cli on cli.pessoa = mp.pessoa "
                  + " left join colaborador co on co.pessoa = mp.pessoa" +
                    " where ch.codigo = " + cheque.getCodigo(), con);
            if(rs.next() && (!UteisValidacao.emptyNumber(rs.getInt("codigo")) || !UteisValidacao.emptyNumber(rs.getInt("colaborador")))){
                if(UteisValidacao.emptyNumber(rs.getInt("codigo"))){
                    vendaAvulsaVO.setColaborador(new ColaboradorVO());
                    vendaAvulsaVO.getColaborador().setCodigo(rs.getInt("colaborador"));
                    vendaAvulsaVO.getColaborador().setPessoa(new PessoaVO());
                    vendaAvulsaVO.getColaborador().getPessoa().setCodigo(rs.getInt("pcodigo"));
                    vendaAvulsaVO.setTipoComprador("CO");
                } else {
                    vendaAvulsaVO.setCliente(new ClienteVO());
                    vendaAvulsaVO.getCliente().setCodigo(rs.getInt("codigo"));
                    vendaAvulsaVO.getCliente().setPessoa(new PessoaVO());
                    vendaAvulsaVO.getCliente().getPessoa().setCodigo(rs.getInt("pcodigo"));
                    vendaAvulsaVO.setTipoComprador("CI");
                }
                

                vendaAvulsaVO.setNomeComprador(rs.getString("nome"));
            }else{
                throw new Exception("Pessoa não foi encontrada.");
            }
           
            vendaAvulsaVO.setDataRegistro(dataMovimentacao);
            vendaAvulsaVO.setEmpresa(new EmpresaVO());
            vendaAvulsaVO.getEmpresa().setCodigo(empresa);
            ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
            item.setDataVenda(dataMovimentacao);
            item.setQuantidade(1);
            item.setUsuarioVO(usuario);

            ResultSet rsProduto = criarConsulta("SELECT codigo FROM produto WHERE tipoproduto = '" + TipoProduto.CHEQUE_DEVOLVIDO.getCodigo() + "'", con);
            if (rsProduto.next()) {
                item.setProduto(new ProdutoVO());
                item.getProduto().setCodigo(rsProduto.getInt("codigo"));
            } else {
                throw new Exception("Produto 'cheques devolvidos' não encontrado.");
            }

            item.setValorParcial(cheque.getValor());
            item.getProduto().setValorFinal(cheque.getValor());
            vendaAvulsaVO.setValorTotal(cheque.getValor());
            vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
            vendaAvulsaVO.setResponsavel(item.getUsuarioVO());
            vendaAvulsaVO.setDescricaoAdicional("CHEQUE DEVOLVIDO - " + cheque.getNumero());

            VendaAvulsa vendaAvulsaDao = new VendaAvulsa(con);
            vendaAvulsaDao.incluirSemCommit(vendaAvulsaVO, false, dataMovimentacao);

            executarConsulta("update movproduto set chequedevolucao = " + cheque.getCodigo() +
                    " where vendaavulsa = " + vendaAvulsaVO.getCodigo(), con);
        }

    }

    //Este não será usado, por decisão de projeto
    public void gerarMensagemCatraca(String chave,
                                     String urlNotificar,
                                     Integer empresa,
                                     List<ChequeVO> cheques,
                                     MovContaVO mov,
                                     ConfiguracaoFinanceiroVO cfg,
                                     int caixa,
                                     UsuarioVO usuario) throws Exception {
        if (cfg.isBloquearAlunoChequeDevolvido()) {
            Map<Integer, String> cliCodAcesso = new HashMap<Integer, String>();
            for (ChequeVO ch : cheques) {
                for( ProdutoPago pp : ch.getProdutoPagos()) {
                    StringBuilder sql = new StringBuilder("select cl.codigo, codacesso ");
                    sql.append(" from movproduto mp inner join ");
                    sql.append("     cliente cl on mp.pessoa = cl.pessoa ");
                    sql.append("     where mp.codigo =  ") .append(pp.getProduto());
                    sql.append(" group by cl.codigo, codacesso ");

                    ResultSet rsCliente = criarConsulta(sql.toString(),
                            con);
                    if (rsCliente.next()) {
                        cliCodAcesso.put(rsCliente.getInt("codigo"), rsCliente.getString("codacesso"));
                    }
                }
            }
            for (Integer cliente : cliCodAcesso.keySet()) {
                ResultSet jaExisteBloqueio = criarConsulta("select codigo from clientemensagem " +
                                "where bloqueio and cliente = " + cliente,
                        con);
                if (jaExisteBloqueio.next()) {
                    continue;
                }
                ClienteMensagemVO mensagemCatraca = new ClienteMensagemVO();
                mensagemCatraca.setCliente(new ClienteVO());
                mensagemCatraca.getCliente().setCodigo(cliente);
                mensagemCatraca.setTipomensagem(TiposMensagensEnum.CATRACA);
                mensagemCatraca.setMensagem(cfg.getMensagembloqueio());
                mensagemCatraca.setBloqueio(Boolean.TRUE);
                mensagemCatraca.setUsuario(usuario);
                mensagemCatraca.setBloqueioCheque(Boolean.TRUE);
                mensagemCatraca.setDataBloqueio(Calendario.hoje());
                getFacade().getClienteMensagem().incluir(mensagemCatraca);
                notificarOuvintes(chave,
                        "bloquear agora(" + cliCodAcesso.get(cliente) + ")",
                        urlNotificar,
                        empresa);

            }

        }
    }

    public void removerMensagemCatraca(ChequeVO cheques,
                                       ConfiguracaoFinanceiroVO cfg) throws Exception {
        removerMensagemCatraca(null, null, null, cheques, cfg);
    }

    public void removerMensagemCatraca(String chave,
                                     String urlNotificar,
                                     Integer empresa,
                                     ChequeVO cheques,
                                     ConfiguracaoFinanceiroVO cfg) throws Exception {
        if (cfg.isBloquearAlunoChequeDevolvido()) {

            StringBuilder sb = new StringBuilder("delete from clientemensagem where cliente = %d and bloqueiocheque = 't'");

            if (null != cheques) {
                for( ProdutoPago pp : cheques.getProdutoPagos()) {
                    StringBuilder sql = new StringBuilder("select cl.codigo, codacesso ");
                    sql.append(" from movproduto mp inner join ");
                    sql.append("     cliente cl on mp.pessoa = cl.pessoa ");
                    sql.append("     where mp.codigo =  ") .append(pp.getProduto());
                    sql.append(" group by cl.codigo, codacesso ");

                    ResultSet rsCliente = criarConsulta(sql.toString(),
                            con);
                    if (rsCliente.next()) {
                        executarConsultaUpdate(String.format(sb.toString(), rsCliente.getInt("codigo")), con);

                        if(null != chave) {
                            notificarOuvintes(chave,
                                    "desbloquear agora(" + rsCliente.getString("codacesso") + ")",
                                    urlNotificar,
                                    empresa);
                        }
                    }
                }
            }

        }
    }

    public void notificarOuvintes(final String chave, final String descNotf, final String url,
                                  final Integer empresa) {
        String timeZone = null;
        try {
            timeZone = getFacade().getEmpresa().obterTimeZoneDefault(empresa);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        SuperControle.notificarOuvintes(descNotf, url, chave, timeZone);
    }
    
    
    public void tratarEstornoReciboPagamentoChequesDevolvidos(List<MovParcelaVO> parcelas) throws Exception{
        for(MovParcelaVO parcela : parcelas){
            StringBuilder sql = new StringBuilder();
            sql.append("select hc.* from movprodutoparcela mpp \n");
            sql.append("inner join movproduto mp on mp.codigo = mpp.movproduto \n");
            sql.append("inner join produto p on p.codigo = mp.produto \n");
            sql.append("inner join cheque ch on ch.codigo = mp.chequedevolucao \n");
            sql.append("inner join historicocheque hc on ch.codigo = hc.cheque \n");
            sql.append("where movparcela = ").append(parcela.getCodigo());
            sql.append("and hc.status = ").append(StatusCheque.CHEQUE_DEVOLVIDO_PAGO.getCodigo());
            ResultSet rs = criarConsulta(sql.toString(), con);

            if (rs.next()){
                executarConsultaUpdate("delete from movcontarateio where movconta = " + rs.getInt("movconta"), con);
                executarConsultaUpdate("delete from movconta where codigo = " + rs.getInt("movconta"), con);
                executarConsultaUpdate("delete from historicocheque where codigo = " + rs.getInt("codigo"), con);
                
                ResultSet historicoDevolvido = criarConsulta("select * from historicocheque where cheque =" + rs.getInt("cheque") +" and status = "+StatusCheque.DEVOLVIDO.getCodigo() , con);
                while(historicoDevolvido.next()){
                    executarConsultaUpdate("update historicocheque  set datafim = null  where codigo = " + historicoDevolvido.getInt("codigo"), con);
                }
            }
        }

    }

}

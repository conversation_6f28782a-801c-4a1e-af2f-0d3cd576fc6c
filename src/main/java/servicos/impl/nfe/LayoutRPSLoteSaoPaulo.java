package servicos.impl.nfe;

/**
 * Created by <PERSON><PERSON> on 27/12/2016.
 */

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import servicos.impl.nfe.base.LoteRPSAttEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class LayoutRPSLoteSaoPaulo extends LayoutRemessaBase {

    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();

        //HEADER
        sb.append(remessa.getHeaderRemessa().toStringBuffer());
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuffer sbDetail = new StringBuffer();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer());
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer());

        return sb;
    }

    public static void preencherArquivoLote(RemessaVO remessa, EmpresaVO empresaVO) {

        ConfiguracaoNotaFiscalVO configNotaVO = empresaVO.getConfiguracaoNotaFiscalNFSe();

        String quebraLinha = System.getProperty("line.separator");

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);

        header.put(LoteRPSAttEnum.TipoRegistro, "1");
        header.put(LoteRPSAttEnum.VersaoLayout, "001");
        header.put(LoteRPSAttEnum.InscricaoMunicipal, StringUtilities.formatarCpfCnjp(configNotaVO.getInscricaoMunicipal(), 8));
        header.put(LoteRPSAttEnum.DataInicio, StringUtilities.formatarCampoData(remessa.getDataInicio(), "yyyyMMdd"));
        header.put(LoteRPSAttEnum.DataFinal, StringUtilities.formatarCampoData(remessa.getDataFim(), "yyyyMMdd"));

        header.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        Integer sequencialRPS = empresaVO.getSequencialLoteRPS();
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

            detail.put(LoteRPSAttEnum.TipoRegistro, "2");
            detail.put(LoteRPSAttEnum.TipoRPS, StringUtilities.formatarCampoEmBranco("RPS", 5));
            detail.put(LoteRPSAttEnum.SerieRPS, StringUtilities.formatarCpfCnjp(configNotaVO.getSerie(), 5));
            detail.put(LoteRPSAttEnum.NumeroRPS, StringUtilities.formatarCampo(new BigDecimal(sequencialRPS), 12));
            detail.put(LoteRPSAttEnum.DataEmissaoRPS, StringUtilities.formatarCampoData(item.getDataEmissaoRPS(), "yyyyMMdd"));
            detail.put(LoteRPSAttEnum.SituacaoRPS, "M");

            soma += item.getValorRPS();
            detail.put(LoteRPSAttEnum.ValorServicos, StringUtilities.formatarCampoMonetario(item.getValorRPS(), 15));
            detail.put(LoteRPSAttEnum.ValorDeducoes, StringUtilities.formatarCampoMonetario(0.0, 15));
            detail.put(LoteRPSAttEnum.CodigoServicoPrestado, StringUtilities.formatarCpfCnjp(configNotaVO.getCodListaServico(), 5));
            detail.put(LoteRPSAttEnum.Aliquota, StringUtilities.formatarCampoMonetario(configNotaVO.getIss(), 4));

            if (configNotaVO.isIssRetido()) {
                detail.put(LoteRPSAttEnum.ISSRetido, "1");
            } else {
                detail.put(LoteRPSAttEnum.ISSRetido, "2");
            }

            String indCPFCNPJTomador;
            if (item.getCpfCnpjRPS().length() == 11) {
                indCPFCNPJTomador = "1";
            } else if (item.getCpfCnpjRPS().length() == 14) {
                indCPFCNPJTomador = "2";
            } else {
                indCPFCNPJTomador = "3";
            }

            detail.put(LoteRPSAttEnum.IndicadorCPFCNPJTomador, indCPFCNPJTomador);

            detail.put(LoteRPSAttEnum.CPFCNPJTomador, StringUtilities.formatarCpfCnjp(item.getCpfCnpjRPS(), 14));

            if (!UteisValidacao.emptyString(item.getPessoa().getInscMunicipal())) {
                detail.put(LoteRPSAttEnum.InscricaoMunicipalTomador, StringUtilities.formatarCpfCnjp(item.getPessoa().getInscMunicipal(), 8));
            } else {
                detail.put(LoteRPSAttEnum.InscricaoMunicipalTomador, StringUtilities.formatarCampoZerado(8));
            }

            if (!UteisValidacao.emptyString(item.getPessoa().getInscEstadual())) {
                detail.put(LoteRPSAttEnum.InscricaoEstadualTomador, StringUtilities.formatarCpfCnjp(item.getPessoa().getInscEstadual(), 12));
            } else {
                detail.put(LoteRPSAttEnum.InscricaoEstadualTomador, StringUtilities.formatarCampoZerado(12));
            }

            detail.put(LoteRPSAttEnum.NomeRazaoSocialTomador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getNome(), 75));

            if (item.getPessoa().getEnderecoVOs().isEmpty()) {

                detail.put(LoteRPSAttEnum.TipoEnderecoTomador, StringUtilities.formatarCampoEmBranco(3));
                detail.put(LoteRPSAttEnum.EnderecoTomador, StringUtilities.formatarCampoEmBranco(50));
                detail.put(LoteRPSAttEnum.NumeroEnderecoTomador, StringUtilities.formatarCampoEmBranco(10));
                detail.put(LoteRPSAttEnum.ComplementoEnderecoTomador, StringUtilities.formatarCampoEmBranco(30));
                detail.put(LoteRPSAttEnum.BairroTomador, StringUtilities.formatarCampoEmBranco(30));
                detail.put(LoteRPSAttEnum.CidadeTomador, StringUtilities.formatarCampoEmBranco(50));
                detail.put(LoteRPSAttEnum.UFTomador, StringUtilities.formatarCampoEmBranco(2));
                detail.put(LoteRPSAttEnum.CEPTomador, StringUtilities.formatarCampoEmBranco(8));

            } else {

                EnderecoVO endereco = item.getPessoa().getEnderecoVOs().get(0);

                detail.put(LoteRPSAttEnum.TipoEnderecoTomador, StringUtilities.formatarCampoEmBranco(3));
                detail.put(LoteRPSAttEnum.EnderecoTomador, StringUtilities.formatarCampoEmBranco(endereco.getEndereco(), 50));
                detail.put(LoteRPSAttEnum.NumeroEnderecoTomador, StringUtilities.formatarCampoEmBranco(endereco.getNumero(), 10));
                detail.put(LoteRPSAttEnum.ComplementoEnderecoTomador, StringUtilities.formatarCampoEmBranco(endereco.getComplemento(), 30));
                detail.put(LoteRPSAttEnum.BairroTomador, StringUtilities.formatarCampoEmBranco(endereco.getBairro(), 30));
                detail.put(LoteRPSAttEnum.CidadeTomador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNome(), 50));
                detail.put(LoteRPSAttEnum.UFTomador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
                detail.put(LoteRPSAttEnum.CEPTomador, StringUtilities.formatarCampoEmBranco(Formatador.removerMascara(endereco.getCep()), 8));
            }

            if (item.getPessoa().getEmailVOs().isEmpty()) {

                detail.put(LoteRPSAttEnum.EmailTomador, StringUtilities.formatarCampoEmBranco(75));

            } else {

                EmailVO emailVO = item.getPessoa().getEmailVOs().get(0);

                detail.put(LoteRPSAttEnum.EmailTomador, StringUtilities.formatarCampoEmBranco(emailVO.getEmail().toUpperCase(), 75));
            }

            detail.put(LoteRPSAttEnum.DiscriminacaoServicos, item.getDescricaoRPS() + "|" + configNotaVO.getObservacao());
            detail.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);

            listaDetalhe.add(detail);
            sequencialRPS++;
        }
        empresaVO.setSequencialLoteRPS(sequencialRPS);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);

        trailer.put(LoteRPSAttEnum.TipoRegistro, "9");
        trailer.put(LoteRPSAttEnum.NumeroLinhasDetalhe, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size()), 7));
        trailer.put(LoteRPSAttEnum.ValorTotalServicoes, StringUtilities.formatarCampoMonetario(soma, 15));
        trailer.put(LoteRPSAttEnum.ValorDeducoes, StringUtilities.formatarCampo(new BigDecimal(0.0), 15));
        trailer.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }
}

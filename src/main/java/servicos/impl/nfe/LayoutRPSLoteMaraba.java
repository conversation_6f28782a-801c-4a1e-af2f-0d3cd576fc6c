package servicos.impl.nfe;

import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.MovProduto;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import servicos.impl.nfe.base.LoteRPSAttEnum;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class LayoutRPSLoteMaraba extends LayoutRemessaBase {

    public static StringBuilder preencherArquivoLote(List<ItemGestaoNotasTO> notasEmitir, EmpresaVO empresaVO, Map<String, PessoaTO> pessoasResponsaveis, Date dataEmissao, Connection con) throws Exception {
        String quebraLinha = System.getProperty("line.separator");
        int qtdRegistros = 1;
        int qtdRegistrosRPS2 = 0;

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        header.put(LoteRPSAttEnum.TipoRegistro, "RPS1");
        header.put(LoteRPSAttEnum.NumSequencial, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros++), 6));
        header.put(LoteRPSAttEnum.InscricaoMunicipal, StringUtilities.formatarCampoEmBranco(empresaVO.getInscMunicipal(), 15));
        header.put(LoteRPSAttEnum.VersaoLayout, "5");
        header.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);

        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        for (ItemGestaoNotasTO item : notasEmitir) {
            String chave = resolveChavePessoa(item);
            PessoaTO pessoaTO = pessoasResponsaveis.get(chave);
            if (!validar(item, pessoaTO)){
                continue;
            }

            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(LoteRPSAttEnum.TipoRegistro, "RPS2");
            detail.put(LoteRPSAttEnum.NumSequencial, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros++), 6));
            detail.put(LoteRPSAttEnum.VersaoLayout, "5");
            detail.put(LoteRPSAttEnum.NumeroNota, StringUtilities.formatarCampoZerado(10));
            detail.put(LoteRPSAttEnum.NumeroRPS, StringUtilities.formatarCampoZerado(10));
            detail.put(LoteRPSAttEnum.CodigoVerificacao, StringUtilities.formatarCampoEmBranco(10));
            detail.put(LoteRPSAttEnum.CidadeServico, StringUtilities.formatarCampoEmBranco(empresaVO.getCidade().getNome(), 50));
            detail.put(LoteRPSAttEnum.UFServico, StringUtilities.formatarCampoEmBranco(empresaVO.getEstado().getSigla(), 2));
            detail.put(LoteRPSAttEnum.NumeroNotaSubstituida, StringUtilities.formatarCampoZerado(10));
            detail.put(LoteRPSAttEnum.MesCompetencia, StringUtilities.formatarCampoForcandoZerosAEsquerda(new BigDecimal(Uteis.getMesData(dataEmissao)), 2));
            detail.put(LoteRPSAttEnum.AnoCompetencia, StringUtilities.formatarCampoForcandoZerosAEsquerda(new BigDecimal(Uteis.getAnoData(dataEmissao)), 4));
            detail.put(LoteRPSAttEnum.DataEmissaoRPS, StringUtilities.formatarCampoData(dataEmissao, "yyyyMMdd"));
//            if (UteisValidacao.emptyString(empresaVO.getNaturezaOperacaoRPS())){
//                throw new ConsistirException("Natureza da Operação deve ser informada no cadastro da empresa");
//            }
//            detail.put(LoteRPSAttEnum.NaturezaOperacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(new BigDecimal(empresaVO.getNaturezaOperacaoRPS()),1));
//            detail.put(LoteRPSAttEnum.NomeRazaoSocialTomador, StringUtilities.formatarCampoEmBranco(item.getNome(), 100));
//            detail.put(LoteRPSAttEnum.CPFCNPJTomador, StringUtilities.formatarCampoEmBranco(pessoaTO.getCpf(), 20));
//            detail.put(LoteRPSAttEnum.InscricaoEstadualTomador, UteisValidacao.emptyString(pessoaTO.getInscEstadual()) ?
//                    StringUtilities.formatarCampoZerado(20) :
//                    StringUtilities.formatarCpfCnjp(pessoaTO.getInscEstadual(), 20));
//            detail.put(LoteRPSAttEnum.InscricaoMunicipalTomador, UteisValidacao.emptyString(pessoaTO.getInscMunicipal()) ?
//                    StringUtilities.formatarCampoZerado(20) :
//                    StringUtilities.formatarCpfCnjp(pessoaTO.getInscMunicipal(), 20));
//            detail.put(LoteRPSAttEnum.EnderecoTomador, UteisValidacao.emptyString(pessoaTO.getEndereco()) ?
//                    StringUtilities.formatarCampoEmBranco(100) :
//                    StringUtilities.formatarCampoEmBranco(pessoaTO.getEndereco(), 100));
//            detail.put(LoteRPSAttEnum.BairroTomador, UteisValidacao.emptyString(pessoaTO.getBairro()) ?
//                    StringUtilities.formatarCampoEmBranco(60) :
//                    StringUtilities.formatarCampoEmBranco(pessoaTO.getBairro(), 60));
//            detail.put(LoteRPSAttEnum.CidadeTomador, UteisValidacao.emptyString(pessoaTO.getCidade()) ?
//                    StringUtilities.formatarCampoEmBranco(60) :
//                    StringUtilities.formatarCampoEmBranco(pessoaTO.getCidade(), 60));
//            detail.put(LoteRPSAttEnum.UFTomador, UteisValidacao.emptyString(pessoaTO.getUf()) ?
//                    StringUtilities.formatarCampoEmBranco(2) :
//                    StringUtilities.formatarCampoEmBranco(pessoaTO.getUf(), 2));
//            detail.put(LoteRPSAttEnum.CEPTomador, UteisValidacao.emptyString(pessoaTO.getCep()) ?
//                    StringUtilities.formatarCampoEmBranco(8) :
//                    StringUtilities.formatarCpfCnjp(pessoaTO.getCep(), 8));
//            detail.put(LoteRPSAttEnum.EmailTomador, UteisValidacao.emptyString(pessoaTO.getEmail()) ?
//                    StringUtilities.formatarCampoEmBranco(150) :
//                    StringUtilities.formatarCampoEmBranco(pessoaTO.getEmail(), 150));
//            detail.put(LoteRPSAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(0.0, 16));
//            detail.put(LoteRPSAttEnum.ValorDeducoes, StringUtilities.formatarCampoMonetario(0.0, 16));
//            detail.put(LoteRPSAttEnum.CodigoServicoPrestado, StringUtilities.formatarCpfCnjp(empresaVO.getCodigoListaServico(), 10));
//            detail.put(LoteRPSAttEnum.Aliquota, StringUtilities.formatarCampoMonetario(empresaVO.getIss(), 5));
//            detail.put(LoteRPSAttEnum.ISSRetido, empresaVO.getIssRetido() ? "S" : "N");
//            detail.put(LoteRPSAttEnum.ValorPIS, StringUtilities.formatarCampoMonetario(item.getValor() * (empresaVO.getAliquotaPIS() / 100.0), 16));
//            detail.put(LoteRPSAttEnum.ValorCOFINS, StringUtilities.formatarCampoMonetario(item.getValor() * (empresaVO.getAliquotaCOFINS() / 100.0), 16));
//            detail.put(LoteRPSAttEnum.ValorIR, StringUtilities.formatarCampoMonetario(item.getValor() * (empresaVO.getAliquotaIRRF() / 100.0), 16));
//            detail.put(LoteRPSAttEnum.ValorCSLL, StringUtilities.formatarCampoMonetario(0.0, 16));
//            detail.put(LoteRPSAttEnum.ValorINSS, StringUtilities.formatarCampoMonetario(0.0, 16));
//            detail.put(LoteRPSAttEnum.Observacao, StringUtilities.formatarCampoEmBranco(500));
//            detail.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);
            qtdRegistrosRPS2++;
            listaDetalhe.add(detail);


            String[] produtos = item.getProdutosPagos().split("\\|");
            int sequencialItemServico = 1;
            for (String prod : produtos) {
                if (UteisValidacao.emptyString(prod) || prod.equals("null")) {
                    continue;
                }
                String[] split = prod.split(",");
                Integer codigoMovProduto = Integer.valueOf(split[0]);
                Double valorPagoProduto = Double.valueOf(split[3]);
                if (UteisValidacao.emptyNumber(valorPagoProduto)) {
                    continue;
                }

                MovProdutoVO movProdutoVO = new MovProduto(con).consultarPorChavePrimaria(codigoMovProduto, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (empresaVO.getTipoProdutoEmissaoNFSe().contains(movProdutoVO.getProduto().getTipoProduto())) {
                    RegistroRemessa itemServico = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    itemServico.put(LoteRPSAttEnum.TipoRegistro, "RPS3");
                    itemServico.put(LoteRPSAttEnum.NumSequencial, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros++), 6));
                    itemServico.put(LoteRPSAttEnum.ItemServico, StringUtilities.formatarCampo(new BigDecimal(sequencialItemServico++), 2));
                    itemServico.put(LoteRPSAttEnum.DiscriminacaoServicos, StringUtilities.formatarCampoEmBranco(movProdutoVO.getDescricao(), 100));
                    itemServico.put(LoteRPSAttEnum.QuantidadeItem, StringUtilities.formatarCampo(new BigDecimal(1), 7));
                    itemServico.put(LoteRPSAttEnum.ValorUnitarioItem, StringUtilities.formatarCampoMonetario(valorPagoProduto, 16));
                    itemServico.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);
                    listaDetalhe.add(itemServico);
                }
            }
        }

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);

        trailer.put(LoteRPSAttEnum.TipoRegistro, "RPS9");
        trailer.put(LoteRPSAttEnum.NumSequencial, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros++), 6));
        trailer.put(LoteRPSAttEnum.NumeroRegistrosDetalhe, StringUtilities.formatarCampo(new BigDecimal(qtdRegistrosRPS2), 6));
        trailer.put(LoteRPSAttEnum.CaractereFimDeLinha, quebraLinha);

        StringBuilder sb = new StringBuilder();
        //HEADER
        sb.append(header.toStringBuffer());
        //DETAIL
        for (RegistroRemessa regD : listaDetalhe) {
            sb.append(regD.toStringBuffer());
        }
        //TRAILER
        sb.append(trailer.toStringBuffer());
        return sb;
    }

    private static boolean validar(ItemGestaoNotasTO item, PessoaTO pessoaTO) {
        if (UteisValidacao.emptyString(pessoaTO.getCpf())) {
            item.setRetorno("CPF deve ser preenchido");
            return false;
        }

//        if (UteisValidacao.emptyString(pessoaTO.getEndereco())) {
//            item.setRetorno("Endereço deve ser preenchido");
//            return false;
//        }
//
//        if (UteisValidacao.emptyString(pessoaTO.getBairro())) {
//            item.setRetorno("Bairro deve ser preenchido");
//            return false;
//        }
//
//        if (UteisValidacao.emptyString(pessoaTO.getCidade())) {
//            item.setRetorno("Cidade deve ser preenchido");
//            return false;
//        }
//
//        if (UteisValidacao.emptyString(pessoaTO.getUf())) {
//            item.setRetorno("UF deve ser preenchido");
//            return false;
//        }
//
//        if (UteisValidacao.emptyString(pessoaTO.getCep())) {
//            item.setRetorno("CEP deve ser preenchido");
//            return false;
//        }
//
//        if (UteisValidacao.emptyString(pessoaTO.getEmail())) {
//            item.setRetorno("E-mail deve ser preenchido");
//            return false;
//        } else if (!UteisValidacao.validaEmail(pessoaTO.getEmail())) {
//            item.setRetorno("E-mail inválido");
//            return false;
//        }

        return true;
    }

    private static String resolveChavePessoa(ItemGestaoNotasTO item) {
        if (!UteisValidacao.emptyNumber(item.getCodCliente())) {
            return "CLI" + item.getCodCliente();
        } else {
            return "COL" + item.getCodColaborador();
        }
    }
}

package servicos.impl.pagarMe;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.plano.ConvenioCobrancaEmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.plano.ConvenioCobrancaEmpresa;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.impl.gatewaypagamento.RecebedorDTO;
import servicos.interfaces.PagarMeServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 10/05/2020
 */
public class PagarMeService extends AbstractCobrancaOnlineServiceComum implements PagarMeServiceInterface {

    private String urlAPI;
    private String chaveAPI;
    private String chaveCriptografia;

    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Log logDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public PagarMeService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.logDAO = new Log(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() throws Exception {
        if (this.convenioCobrancaVO != null) {
            this.chaveAPI = this.convenioCobrancaVO.getCodigoAutenticacao01();
            this.chaveCriptografia = this.convenioCobrancaVO.getCodigoAutenticacao02();

            this.urlAPI = PropsService.getPropertyValue(PropsService.urlApiPagarme);

            //Pagarme é a mesma Url o que altera é os códigos de integração
            //Key Types
            //We will provide you with two keys so that you can perform tests:
            //
            //Exemplo de prefixo da Chave Secreta de Sandbox: sk_test_*
            //        Exemplo de prefixo da Public Key de Sandbox: pk_test_*
            //        After closing the contract you will receive the keys of the production environment:
            //
            //Exemplo de prefixo da Chave Secreta de Produção: sk_*
            //        Exemplo de prefixo da Public Key de Produção: pk_*
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO) &&
                    (this.convenioCobrancaVO.getCodigoAutenticacao01().toLowerCase().contains("_test_") ||
                            this.convenioCobrancaVO.getCodigoAutenticacao02().toLowerCase().contains("_test_"))) {
                throw new Exception("Está em ambiente de " + AmbienteEnum.PRODUCAO.getDescricao() + " porem as chaves de integração contem a palavra \"_test_\".");
            } else if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.HOMOLOGACAO) &&
                    (!this.convenioCobrancaVO.getCodigoAutenticacao01().toLowerCase().contains("_test_") ||
                            !this.convenioCobrancaVO.getCodigoAutenticacao02().toLowerCase().contains("_test_"))) {
                throw new Exception("Está em ambiente de " + AmbienteEnum.HOMOLOGACAO.getDescricao() + " porem as chaves de integração não contem a palavra \"_test_\".");
            }
        }
    }

    public boolean isPossuiConfiguracao() {
        return StringUtils.isNotBlank(this.chaveAPI) && StringUtils.isNotBlank(this.chaveCriptografia);
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoPagarMeVO(), TipoTransacaoEnum.PAGAR_ME, convenioCobrancaVO);
            transacaoDAO.incluir(transacao);

            String siglaPagarMe = obterSiglaEmpresa(transacao);

            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);;

            if (clienteVO.isUtilizarResponsavelPagamento() &&
                    !UteisValidacao.emptyNumber(clienteVO.getCodigo()) && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
                PessoaCPFTO pessoaCPFTO = transacaoDAO.obterDadosPessoaPagador(pessoa, false, true);

                //usar CPF do responsável
                pessoa.setCfp(pessoaCPFTO.getCpfResponsavel());
            }

            verificarAlteracaoPessoa(pessoa, transacao, siglaPagarMe);

            JSONObject parametrosPagamento = criarTransacaoJSON(transacao, dadosCartao, pessoa, siglaPagarMe);
            transacao.setParamsEnvio(removerDadosSigilososEnvio(parametrosPagamento));
            transacaoDAO.alterar(transacao);

            if (UteisValidacao.emptyString(pessoa.getIdPagarMe())) {
                throw new Exception("Pessoa não incluída na Pagar.me");
            }

            validarDadosTransacao(transacao, dadosCartao);

            String retorno = executarRequestPagarMe("/transactions", parametrosPagamento.toString(), MetodoHttpEnum.POST);
            processarRetorno(transacao, retorno, false);

            //consultar para verificar se já foi aprovada...
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                realizarConsultaSituacao(3, transacao);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
            return transacao;
        } catch (CobrancaException ex) {
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private String obterSiglaEmpresa(TransacaoVO transacaoVO) {
        ConvenioCobrancaEmpresa dao = null;
        try {
            dao = new ConvenioCobrancaEmpresa(getCon());

            ConvenioCobrancaEmpresaVO obj = dao.consultarPorConvenioCobrancaEmpresa(convenioCobrancaVO.getCodigo(), transacaoVO.getEmpresa());
            if (obj != null) {
                return obj.getSiglaPagarMe().trim().toUpperCase();
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        } finally {
            dao = null;
        }
    }

    private String removerDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());

        if (parametrosPagamento.has("card_number")) {
            parametrosPagamento.put("card_number", APF.getCartaoMascarado(parametrosPagamento.getString("card_number")));

        }
        if (parametrosPagamento.has("card_cvv")) {
            parametrosPagamento.put("card_cvv", "***");
        }

        return parametrosPagamento.toString();
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO) throws Exception {
        if (qtd > 0) {
            try {
                try {
                    consultarSituacaoCobrancaTransacao(transacaoVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                    Thread.sleep(2000);
                    realizarConsultaSituacao(qtd - 1, transacaoVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public String consultarTransacao(String codigoExterno) throws Exception {
        return executarRequestPagarMe("/transactions/" + codigoExterno + "?api_key=" + this.chaveAPI, null, MetodoHttpEnum.GET);
    }

    private String consultarTransacaoReference(String referenceKey) throws Exception {
        return executarRequestPagarMe("/transactions?api_key=" + this.chaveAPI + "&reference_key="+ referenceKey, null, MetodoHttpEnum.GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        throw new Exception("Não disponibilizado para Pagar.Me");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarConsultaSituacao(1, transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {

        // atualiza situação da transação, caso ela esteja aguardando
        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA) && !transacaoVO.isTransacaoVerificarCartao()) {
            retransmitirTransacao(transacaoVO, null, null);
        }
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno, false);
        } else if (!UteisValidacao.emptyString(transacaoVO.getIdentificadorPacto())) {
            String retorno = consultarTransacaoReference(transacaoVO.getIdentificadorPacto());
            try {
                JSONArray jsonArray = new JSONArray(retorno);
                if (transacaoVO.isVerificador() && jsonArray.length() == 0) {

                    transacaoVO.setCodigoRetorno(PagarMeRetornoEnum.Status8888.getId());
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.COM_ERRO);

                    String mensagemErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.mensagemErro);
                    String msgErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.msgErro);
                    String erroGenericoTransacao = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao);

                    String msgGravar = PagarMeRetornoEnum.Status8888.getDescricao();

                    if (UteisValidacao.emptyString(mensagemErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.mensagemErro, msgGravar);
                    } else if (UteisValidacao.emptyString(msgErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.msgErro, msgGravar);
                    } else if (UteisValidacao.emptyString(erroGenericoTransacao)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao, msgGravar);
                    }
                    return;
                } else if (jsonArray.length() > 0) {
                    retorno = jsonArray.getJSONObject(0).toString();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            processarRetorno(transacaoVO, retorno, false);
        } else {
            throw new Exception("Sem código referencia para consultar a transação");
        }
    }

    public void consultarSituacaoCobrancaTransacaoCancelamento(TransacaoVO transacaoVO, boolean fluxoCancelamento) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            String retorno = consultarTransacao(transacaoVO.getCodigoExterno());
            processarRetorno(transacaoVO, retorno, true);
        } else if (!UteisValidacao.emptyString(transacaoVO.getIdentificadorPacto())) {
            String retorno = consultarTransacaoReference(transacaoVO.getIdentificadorPacto());
            try {
                JSONArray jsonArray = new JSONArray(retorno);
                if (transacaoVO.isVerificador() && jsonArray.length() == 0) {

                    transacaoVO.setCodigoRetorno(PagarMeRetornoEnum.Status8888.getId());
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.COM_ERRO);

                    String mensagemErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.mensagemErro);
                    String msgErro = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.msgErro);
                    String erroGenericoTransacao = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao);

                    String msgGravar = PagarMeRetornoEnum.Status8888.getDescricao();

                    if (UteisValidacao.emptyString(mensagemErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.mensagemErro, msgGravar);
                    } else if (UteisValidacao.emptyString(msgErro)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.msgErro, msgGravar);
                    } else if (UteisValidacao.emptyString(erroGenericoTransacao)) {
                        transacaoVO.adicionarOutrasInformacoes(AtributoTransacaoEnum.erroGenericoTransacao, msgGravar);
                    }
                    return;
                } else if (jsonArray.length() > 0) {
                    retorno = jsonArray.getJSONObject(0).toString();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            processarRetorno(transacaoVO, retorno, false);
        } else {
            throw new Exception("Sem código referencia para consultar a transação");
        }
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        JSONObject body = new JSONObject();
        body.put("api_key", this.chaveAPI);

        int amount = (int) (transacaoVO.getValor() * 100);
        body.put("amount", amount);
        String retornoCancelamento = executarRequestPagarMe("/transactions/" + transacaoVO.getCodigoExterno() + "/refund", body.toString(), MetodoHttpEnum.POST);
        processarRetornoCancelamento(transacaoVO, retornoCancelamento);
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, String retornoCancelamento) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoCancelamento, "processarRetornoCancelamento");
        transacaoVO.setResultadoCancelamento(retornoCancelamento);
        try {
            JSONObject cancelamentoJSON = new JSONObject(retornoCancelamento);
            String status = cancelamentoJSON.getString("status");
            if (status.equalsIgnoreCase("refunded")) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoVO.setDataHoraCancelamento(Calendario.hoje());
            } else {
                TimeUnit.SECONDS.sleep(4);
                consultarSituacaoCobrancaTransacaoCancelamento(transacaoVO, true);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno, boolean fluxoCancelamento) {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetorno");
        transacao.setParamsResposta(retorno);

        JSONObject retornoJSON = new JSONObject();
        try {
            retornoJSON = new JSONObject(retorno);
        } catch (Exception ex) {
            ex.printStackTrace();
            transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
        }

        if (retornoJSON.has("id")) {

            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                String id = retornoJSON.optString("id");
                if (!UteisValidacao.emptyString(id)) {
                    transacao.setCodigoExterno(id);
                }
            }

            try { //IDENTIFICAR A OPERADORA DO CARTAO
                String card_brand = retornoJSON.optString("card_brand");
                if (!UteisValidacao.emptyString(card_brand)) {
                    for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                        String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                        String operadoraPagamento = card_brand.toUpperCase().replaceAll(" ", "");
                        if (operadoraEnum.equalsIgnoreCase(operadoraPagamento)) {
                            transacao.setBandeiraPagamento(ope);
                            break;
                        }
                    }
                } else {
                    transacao.setBandeiraPagamento(null);
                }
            } catch (Exception ignored) {
                transacao.setBandeiraPagamento(null);
            }


            //https://docs.pagar.me/reference#status-das-transacoes
            //Valores possíveis: processing, authorized, paid, refunded, waiting_payment, pending_refund, refused .
            String status = retornoJSON.getString("status");
            if (status.equalsIgnoreCase("paid") || (transacao.isTransacaoVerificarCartao() && status.equalsIgnoreCase("authorized") && !fluxoCancelamento)) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else if (status.equalsIgnoreCase("processing") || status.equalsIgnoreCase("authorized") || status.equalsIgnoreCase("pending_review")) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (status.equalsIgnoreCase("chargedback") || status.equalsIgnoreCase("refunded")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (status.equalsIgnoreCase("refused")) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
            }

        } else if (retornoJSON.has("errors")) {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        }
    }

    private JSONObject criarTransacaoJSON(TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO,
                                          PessoaVO pessoaVO, String siglaPagarMe) throws Exception {

        List<String> itensObrigatorios = new ArrayList<>();

        JSONObject payment = new JSONObject();
        payment.put("api_key", this.chaveAPI);


        //Endpoint do seu sistema que receberá informações a cada atualização da transação. Caso você defina este parâmetro, o processamento da transação se torna assíncrono.
        try {
            final String key = DAO.resolveKeyFromConnection(getCon());
            String postback_url = this.convenioCobrancaVO.getTipo().getWebhookURL(key);
            if (!UteisValidacao.emptyString(postback_url) && postback_url.startsWith("http")) {
                payment.put("postback_url", postback_url);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }


        String identificador = "TRAN" + siglaPagarMe + transacaoVO.getCodigo();
//        String identificador = obterIdentificadorPacto(transacaoVO, siglaPagarMe);
        Uteis.logarDebug("IDENTIFICADOR PAGAR.ME: " + identificador);

        //Valor único que identifica a transação para permitir uma nova tentativa de requisição com a segurança de que a mesma operação não será executada duas vezes acidentalmente.
        payment.put("reference_key", identificador);

        payment.put("customer", criarCustomerJSON(itensObrigatorios, pessoaVO, false, false, siglaPagarMe));

        //Utilize false caso queira manter o processamento síncrono de uma transação. Ou seja, a resposta da transação é recebida na hora.
        payment.put("async", false);

        //Após a autorização de uma transação, você pode escolher se irá capturar ou adiar a captura do valor. Caso opte por postergar a captura, atribua o valor false.
        payment.put("capture", !cartaoCreditoTO.isTransacaoVerificarCartao());

        //Número de parcelas da transação, sendo mínimo: 1 e Máximo: 12. OBS: Se o pagamento for boleto, o padrão é 1
        payment.put("installments", cartaoCreditoTO.getParcelas());

        //Descrição que aparecerá na fatura depois do nome de sua empresa. Máximo de 13 caracteres, sendo alfanuméricos e espaços.
        payment.put("soft_descriptor", formatarCampo(this.convenioCobrancaVO.getEmpresa().getRazaoSocialParaSoftDescriptor(cartaoCreditoTO.isTransacaoVerificarCartao()), 13));

        //Valor a ser cobrado. Deve ser passado em centavos. Ex: R$ 10.00 = 1000. Deve ser no mínimo 1 real (100)
        int amount = (int) (transacaoVO.getValor() * 100);
        payment.put("amount", amount);

        //Método de pagamento da transação. Aceita dois tipos: credit_card e boleto
        payment.put("payment_method", "credit_card");

        //Obrigatório com o antifraude habilitado. Define os dados dos itens vendidos, como nome, preço unitário e quantidade
        payment.put("items", criarItemsJSON(transacaoVO));

        //Obrigatório com o antifraude habilitado. Define os dados de cobrança, como nome e endereço
        payment.put("billing", criarBillingJSON(itensObrigatorios, pessoaVO));

        criarPagamentoJSON(payment, transacaoVO, cartaoCreditoTO, pessoaVO);


        //verifica se existe regra de recebedores
        //Regras de divisão da transação
        verificarRecebedores(transacaoVO, payment);

        //METADATA
        //Você pode passar dados adicionais na criação da transação para
        //facilitar uma futura análise de dados tanto em nossa dashboard, quanto por seus sistemas.
        preencherMetadata(transacaoVO, payment);


        if (!UteisValidacao.emptyList(itensObrigatorios)) {
            String campos = "";
            for (String s : itensObrigatorios) {
                campos += (", " + s);
            }
            throw new Exception("O cliente não possui " + campos.replaceFirst(", ", "") + ".");
        }

        return payment;
    }

    private void preencherMetadata(TransacaoVO transacaoVO, JSONObject payment) {
        JSONObject metadata = new JSONObject();
        metadata.put("identificadorPacto", transacaoVO.getIdentificadorPacto());
        metadata.put("empresa", this.convenioCobrancaVO.getEmpresa().getRazaoSocialParaSoftDescriptor(false));
        metadata.put("nomeFantasia", this.convenioCobrancaVO.getEmpresa().getNome());
        if (!UteisValidacao.emptyString(this.convenioCobrancaVO.getMensagem())) {
            metadata.put("metadata", this.convenioCobrancaVO.getMensagem());
        }

        try {

            String parcelas = "";
            for (MovParcelaVO parcelaVO : transacaoVO.getListaParcelas()) {
                parcelas += "," + parcelaVO.getCodigo();
            }
            parcelas = (parcelas.replaceFirst(",", ""));

            if (!UteisValidacao.emptyString(parcelas)) {
                Integer codPlano = 0;
                String plano = "";
                Set<String> listaProdutos = new HashSet<>();

                StringBuilder sql = new StringBuilder();
                sql.append("select   \n");
                sql.append("pl.codigo as codPlano,  \n");
                sql.append("pl.descricao as plano,  \n");
                sql.append("pro.descricao as produto \n");
                sql.append("from movparcela mp  \n");
                sql.append("left join contrato con on con.codigo = mp.contrato  \n");
                sql.append("left join plano pl on pl.codigo = con.plano \n");
                sql.append("inner join movprodutoparcela mpp on mpp.movparcela = mp.codigo  \n");
                sql.append("inner join movproduto mov on mpp.movproduto = mov.codigo  \n");
                sql.append("inner join produto pro on pro.codigo = mov.produto  \n");
                sql.append("where mp.codigo IN (").append(parcelas).append(") \n");
                sql.append("group by 1,2,3 \n");

                try (Statement stm = getCon().createStatement()) {
                    try (ResultSet rs = stm.executeQuery(sql.toString())) {
                        while (rs.next()) {

                            //codPlano
                            Integer codPl = rs.getInt("codPlano");
                            if (!UteisValidacao.emptyNumber(codPl)) {
                                codPlano = codPl;
                            }

                            //plano
                            String pl = rs.getString("plano");
                            if (!UteisValidacao.emptyString(pl)) {
                                plano = pl;
                            }

                            //produto
                            String pr = rs.getString("produto");
                            if (!UteisValidacao.emptyString(pr)) {
                                listaProdutos.add(pr);
                            }
                        }
                    }
                }

                metadata.put("codigoPlano", codPlano);
                metadata.put("plano", plano);

                String produtos = "";
                for (String prod : listaProdutos) {
                    produtos += "," + prod;
                }
                produtos = (produtos.replaceFirst(",", ""));

                metadata.put("produtos", produtos);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        payment.put("metadata", metadata);
    }

    private void verificarRecebedores(TransacaoVO transacaoVO, JSONObject payment) throws Exception {
        List<RecebedorDTO> lista = obterRecebedoresSplitPagamentoDoConvenio(transacaoVO, this.convenioCobrancaVO);
        if (!UteisValidacao.emptyList(lista)) {
            JSONArray split_rules = new JSONArray();
            for (RecebedorDTO dto : lista) {
                JSONObject rules = new JSONObject();
                rules.put("recipient_id", dto.getId());
                rules.put("amount", dto.getValorCentavos());
                split_rules.put(rules);
            }

            if (split_rules.length() > 0) {
                payment.put("split_rules", split_rules);
            }
        }
    }

    private JSONObject criarBillingJSON(List<String> itensObrigatorios, PessoaVO pessoaVO) throws Exception {

        EnderecoVO enderecoVO = obterEnderecoPessoa(pessoaVO);
        JSONObject address = new JSONObject();


        String endereco = Uteis.retirarAcentuacaoRegex(enderecoVO.getEndereco().trim());
        if (UteisValidacao.emptyString(endereco)) {
            itensObrigatorios.add("ENDEREÇO");
        }
        address.put("street", endereco);

        String numero = enderecoVO.getNumero();
        if (UteisValidacao.emptyString(numero)) {
            itensObrigatorios.add("NÚMERO do endereço");
        }
        address.put("street_number", numero);

        if (!UteisValidacao.emptyString(enderecoVO.getComplemento())) {
            address.put("complementary", Uteis.retirarAcentuacaoRegex(enderecoVO.getComplemento()));
        }

        if (!UteisValidacao.emptyString(enderecoVO.getBairro())) {
            address.put("neighborhood", Uteis.retirarAcentuacaoRegex(enderecoVO.getBairro()));
        }

        String cidade = Uteis.retirarAcentuacaoRegex(pessoaVO.getCidade().getNome());
        if (UteisValidacao.emptyString(cidade)) {
            itensObrigatorios.add("CIDADE do cliente");
        }
        address.put("city", cidade);


        String estado = pessoaVO.getEstadoVO().getSigla();
        if (UteisValidacao.emptyString(estado)) {
            itensObrigatorios.add("ESTADO do cliente");
        }
        address.put("state", estado);


        address.put("country", "br");

        String cep = Uteis.tirarCaracteres(enderecoVO.getCep(), true);
        if (UteisValidacao.emptyString(cep)) {
            itensObrigatorios.add("CEP do endereço");
        }
        address.put("zipcode", cep);

        JSONObject billing = new JSONObject();
        billing.put("name", "Endereco Cliente");
        billing.put("address", address);
        return billing;
    }

    private void criarPagamentoJSON(JSONObject payment, TransacaoVO transacaoVO, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO) throws Exception {

        String numeroCartao = "";
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            numeroCartao = cartaoCreditoTO.getNumero();
        }

        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(numeroCartao, cartaoCreditoTO, transacaoVO);

//        String card_id = obterIdCard(numeroCartao, cartaoCreditoTO, pessoaVO);
        String card_id = null;

        //utilizando uma id de um cartão já cadastrado na pagarme
        if (!UteisValidacao.emptyString(card_id)) {
            payment.put("card_id", card_id);
        } else if (!UteisValidacao.emptyString(numeroCartao)) {

            payment.put("card_number", numeroCartao);
            payment.put("card_holder_name", formatarCampo(Uteis.retirarAcentuacaoRegex(cartaoCreditoTO.getNomeTitular()), 64));
            payment.put("card_expiration_date", cartaoCreditoTO.getValidadeMMYY(false));

            //Código de segurança do cartão.
            if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
                payment.put("card_cvv", cartaoCreditoTO.getCodigoSeguranca());
            }
        }
    }

    private JSONArray criarItemsJSON(TransacaoVO transacaoVO) {
        JSONObject item = new JSONObject();
        int amount = (int) (transacaoVO.getValor() * 100);
        item.put("id", "1");
        item.put("unit_price", amount);
        item.put("title", "PARCELA COBRANCA");
        item.put("quantity", 1);
        item.put("tangible", false);

        JSONArray items = new JSONArray();
        items.put(item);
        return items;
    }

    private JSONObject criarCardJSON(String numeroCartao, CartaoCreditoTO cartaoCreditoTO) {
        JSONObject card = new JSONObject();
        card.put("api_key", this.chaveAPI);
        card.put("card_number", numeroCartao);
        card.put("card_holder_name", formatarCampo(Uteis.retirarAcentuacaoRegex(cartaoCreditoTO.getNomeTitular()), 64));
        card.put("card_expiration_date", cartaoCreditoTO.getValidadeMMYY(false));

        //Código de segurança do cartão.
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
            card.put("card_cvv", cartaoCreditoTO.getCodigoSeguranca());
        }

        return card;
    }

    private String obterIdCard(String numeroCartaoPassando, CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoa) {
        AutorizacaoCobrancaCliente autoDAO = null;
        try {

            if (!UteisValidacao.emptyString(cartaoCreditoTO.getIdCardPagarMe())) {
                return cartaoCreditoTO.getIdCardPagarMe();
            }


            autoDAO = new AutorizacaoCobrancaCliente(getCon());
            List<AutorizacaoCobrancaClienteVO> autorizacoesCadastradas = autoDAO.consultarPorPessoaTipoAutorizacao(pessoa.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (UteisValidacao.emptyList(autorizacoesCadastradas)) {
                //se não tem autorização de cobrança cadastrada retornar nulll
                return null;
            }

            String numeroCartaoPassandoMascarado = APF.getCartaoMascarado(numeroCartaoPassando);

            //buscar a autorização de cobrança referente ao cartão que está sendo utilizado.
            AutorizacaoCobrancaClienteVO auto = null;
            for (AutorizacaoCobrancaClienteVO obj : autorizacoesCadastradas) {
                if (obj.getCartaoMascarado().equalsIgnoreCase(numeroCartaoPassandoMascarado)) {
                    if (!UteisValidacao.emptyString(obj.getIdCardPagarMe())) {
                        return obj.getIdCardPagarMe();
                    } else {
                        auto = obj;
                        break;
                    }
                }
            }

            //tem autorização de cobrança.. então vou enviar o cartão para a Pagarme..
            if (auto != null) {

                //se não tiver o id do cliente na Pagarme..
                if (UteisValidacao.emptyString(pessoa.getIdPagarMe())) {
                    return null;
                }

                JSONObject card = criarCardJSON(numeroCartaoPassando, cartaoCreditoTO);
                card.put("customer_id", pessoa.getIdPagarMe());

                Uteis.logar(null, "Vou cadastrar cartão Pagar.Me... " + numeroCartaoPassandoMascarado);

                String resposta = executarRequestPagarMe("/cards", card.toString(), MetodoHttpEnum.POST);
                JSONObject respostaJSON = new JSONObject(resposta);
                Integer idCardPagarme = respostaJSON.optInt("id");
                if (!UteisValidacao.emptyNumber(idCardPagarme)) {
                    auto.setIdCardPagarMe(idCardPagarme.toString());
                    autoDAO.alterarIdCardPagarMe(auto);
                    return auto.getIdCardPagarMe();
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            autoDAO = null;
        }
    }

    private void verificarAlteracaoPessoa(PessoaVO pessoa, TransacaoVO transacaoVO, String siglaPagarMe) throws Exception {
        if (dadosDesatualizados(pessoa)) {
            incluirPessoa(pessoa, transacaoVO, siglaPagarMe);
        }
    }

    private Boolean dadosDesatualizados(PessoaVO pessoa) throws Exception {
        boolean dadosDesatualizados = UteisValidacao.emptyString(pessoa.getIdPagarMe());
        if (!dadosDesatualizados) {
            LogVO ultimoLog = this.logDAO.consultarUltimoLogPessoa(pessoa.getCodigo(), "CLIENTE", "PESSOA", "CLIENTE - EMPRESA");
            if (ultimoLog != null) {
                dadosDesatualizados = pessoa.getDataAlteracaoPagarMe().getTime() < ultimoLog.getDataAlteracao().getTime();
            } else {
                dadosDesatualizados = true;
            }
        }
        return dadosDesatualizados;
    }

    public void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO, String siglaPagarMe) throws Exception {
        try {

            boolean cadastroNovo = UteisValidacao.emptyString(pessoa.getIdPagarMe());

            List<String> itensObrigatorios = new ArrayList<>();

            JSONObject customerJSON = criarCustomerJSON(itensObrigatorios, pessoa, true, !cadastroNovo, siglaPagarMe);
            JSONObject jsonObject = null;

            if (!UteisValidacao.emptyList(itensObrigatorios)) {
                String campos = "";
                for (String s : itensObrigatorios) {
                    campos += (", " + s);
                }
                throw new Exception("O cliente não possui " + campos.replaceFirst(", ", "") + ".");
            }

            if (cadastroNovo) {
                //incluir
                String resposta = executarRequestPagarMe("/customers", customerJSON.toString(), MetodoHttpEnum.POST);
                incluirHistoricoRetornoTransacao(transacaoVO, resposta, "incluirPessoa");
                jsonObject = new JSONObject(resposta);
            } else {
                //editar
                String resposta = executarRequestPagarMe("/customers/" + pessoa.getIdPagarMe(), customerJSON.toString(), MetodoHttpEnum.PUT);
                incluirHistoricoRetornoTransacao(transacaoVO, resposta, "incluirPessoaAlterar");
                jsonObject = new JSONObject(resposta);
            }

            if (jsonObject.has("errors")) {
                throw new ConsistirException("Falha ao inserir a pessoa na Pagar.me. Erro: " + jsonObject.get("errors").toString());
            } else {
                if (jsonObject.has("id")) {
                    Integer id = jsonObject.optInt("id");
                    if (!UteisValidacao.emptyNumber(id)) {
                        pessoa.setIdPagarMe(id.toString());
                        new Pessoa(getCon()).alterarIdPagarMe(pessoa);
                    }
                }
            }
        } catch (Exception ex) {
            incluirHistoricoRetornoTransacao(transacaoVO, ex.getMessage(), "incluirPessoaErro");
            throw ex;
        }
    }

    private String executarRequestPagarMe(String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = this.urlAPI + endPoint;

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("api_key", this.chaveAPI);
        headers.put("ServiceRefererName", PropsService.getPropertyValue(PropsService.serviceRefererNamePagarMePacto));

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        return respostaHttpDTO.getResponse();
    }

    public String formatarCampo(String texto, Integer tamanho) {
        if (texto.length() >= tamanho) {
            return texto.substring(0, tamanho);
        }
        return texto;
    }

    private JSONObject criarCustomerJSON(List<String> itensObrigatorios, PessoaVO pessoaVO,
                                         boolean enviarApiKey, boolean atualizacao, String siglaPagarMe) {

        if (!SuperVO.verificaCPF(pessoaVO.getCfp())) {
            itensObrigatorios.add("CPF");
        }

        JSONObject customer = new JSONObject();
        if (enviarApiKey || atualizacao) {
            customer.put("api_key", this.chaveAPI);
        }

        String email = obterEmailPessoa(pessoaVO);
        if (UteisValidacao.emptyString(email)) {
            itensObrigatorios.add("EMAIL");
        }
        customer.put("email", email);

        if (UteisValidacao.emptyString(pessoaVO.getNome())) {
            itensObrigatorios.add("NOME");
        }
        customer.put("name", Uteis.retirarAcentuacaoRegex(pessoaVO.getNome()));


        //quando é atualização é somente nome e email e a api Key
        if (atualizacao) {
            return customer;
        }

        customer.put("country", "br");
        customer.put("external_id", "P" + siglaPagarMe + pessoaVO.getCodigo());

        if (pessoaVO.getDataNasc() != null) {
            customer.put("birthday", Calendario.getDataAplicandoFormatacao(pessoaVO.getDataNasc(), "yyyy-MM-dd"));
        }

        String cpfCNPJ = Uteis.removerMascara(pessoaVO.getCfp());
        customer.put("type", "individual");

        JSONArray documents = new JSONArray();
        JSONObject cpf = new JSONObject();
        cpf.put("type", "cpf");
        cpf.put("number", cpfCNPJ);
        documents.put(cpf);
        customer.put("documents", documents);

        if (!UteisValidacao.emptyList(pessoaVO.getTelefoneVOs())) {
            JSONArray phone_numbers = new JSONArray();
            for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
                String ddi = telefoneVO.getDdi();
                if (UteisValidacao.emptyString(ddi)) {
                    ddi = "+55";
                }
                phone_numbers.put(ddi + Uteis.tirarCaracteres(telefoneVO.getNumero(), true));
            }
            customer.put("phone_numbers", phone_numbers);
        } else {
            itensObrigatorios.add("TELEFONE");
        }
        return customer;
    }

    private JSONObject criarAddressJSON(PessoaVO pessoaVO, EnderecoVO enderecoVO) {
        if (enderecoVO != null && !UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            JSONObject address = new JSONObject();

            String line_1 = formatarCampo(enderecoVO.getEndereco() + " " + enderecoVO.getBairro() + " " + enderecoVO.getNumero(), 256);
            if (UteisValidacao.emptyString(line_1.trim())) {
                return null;
            }

            String zip_code = Uteis.removerMascara(enderecoVO.getCep());
            if (UteisValidacao.emptyString(zip_code.trim())) {
                return null;
            }
            address.put("line_1", line_1); //REQUIRED Max: 256 characters.
            address.put("line_2", formatarCampo(enderecoVO.getComplemento(), 128));//Max: 128 characters.
            address.put("zip_code", zip_code); //REQUIRED Max: 16 characters.
            address.put("city", ""); //REQUIRED Max: 64 characters.
            address.put("state", ""); //REQUIRED State code in ISO 3166-2 format.
            address.put("country", "BR"); //REQUIRED Country code in ISO 3166-1 alpha-2 format.

            return address;
        } else {
            return null;
        }
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    private String obterTelefonePessoa(TipoTelefoneEnum tipoTelefoneEnum, PessoaVO pessoaVO) {
        for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
            if (UteisValidacao.validaTelefone(telefoneVO.getNumero()) && (tipoTelefoneEnum == null || tipoTelefoneEnum.getCodigo().equals(telefoneVO.getTipoTelefone()))) {
                return Uteis.tirarCaracteres(telefoneVO.getNumero(), true);
            }
        }
        return "";
    }

    private EnderecoVO obterEnderecoPessoa(PessoaVO pessoaVO) {
        for (EnderecoVO enderecoVO : pessoaVO.getEnderecoVOs()) {
            return enderecoVO;
        }
        return new EnderecoVO();
    }

    public List<RecebedorDTO> obterRecebedores() throws Exception {
        List<RecebedorDTO> lista = new ArrayList<>();

        String retorno = executarRequestPagarMe("/recipients?api_key=" + this.chaveAPI + "&count=1000", null, MetodoHttpEnum.GET);

        if (retorno.contains("errors")) {
            JSONObject json = new JSONObject(retorno);
            JSONArray jsonArray = json.getJSONArray("errors");
            JSONObject jsonMsg = new JSONObject(jsonArray.get(0).toString());
            throw new Exception("Não foi possível consultar os recebedores: " + jsonMsg.getString("message"));
        }

        JSONArray jsonArray = new JSONArray(retorno);
        for (int e = 0; e < jsonArray.length(); e++) {
            JSONObject obj = jsonArray.getJSONObject(e);

            RecebedorDTO recebedorDTO = new RecebedorDTO(obj, this.convenioCobrancaVO);
            if (recebedorDTO.getStatus().equalsIgnoreCase("active") &&
                    !UteisValidacao.emptyString(recebedorDTO.getName())) {
                lista.add(recebedorDTO);
            }
        }
        return lista;
    }
}

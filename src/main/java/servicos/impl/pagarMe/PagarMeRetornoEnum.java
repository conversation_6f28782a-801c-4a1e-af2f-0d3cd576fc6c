package servicos.impl.pagarMe;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 10/05/2020
 */
public enum PagarMeRetornoEnum {

    StatusNENHUM("NENHUM", ""),
    Status0("0", "Transação autorizada.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status0000("0000", "Transação autorizada.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status1000("1000", "Transação não autorizada. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1001("1001", "Cartão vencido. Você pode orientar o portador a entrar em contato com o banco emissor do cartão.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1002("1002", "Transação não permitida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1003("1003", "Rejeitado emissor. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1004("1004", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1005("1005", "Transação não autorizada. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1006("1006", "Tentativas de senha excedidas. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1007("1007", "Rejeitado emissor. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1008("1008", "Rejeitado emissor. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1009("1009", "Transação não autorizada. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1010("1010", "Valor inválido. É possível também que o tipo de operação seja inválido. Por exemplo, cartão de débito onde se aceita apenas crédito."),
    Status1011("1011", "Cartão inválido. O número do cartão digitado está incorreto. Oriente o portador a tentar novamente, se atentando ao preenchimento do número do cartão.", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1013("1013", "Transação não autorizada. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1014("1014", "Tipo de conta inválido. Pode acontecer quando o tipo de conta selecionado não existe."),
    Status1015("1015", "Função não suportada. É possível também que o tipo de operação seja inválido. Por exemplo, cartão de débito onde se aceita apenas crédito."),
    Status1016("1016", "Saldo insuficiente. Você poder orientar o portador a entrar em contato com o banco emissor do cartão para aumento do limite.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Status1017("1017", "Senha inválida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1019("1019", "Transação não permitida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1020("1020", "Transação não permitida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1021("1021", "Rejeitado emissor. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1022("1022", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1023("1023", "Rejeitado emissor. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1024("1024", "Transação não permitida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1025("1025", "Cartão bloqueado. Você pode orientar o portador a entrar em contato com o banco emissor do cartão.", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_TEMPORARIO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1027("1027", "Excedida a quantidade de transações para o cartão. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status1042("1042", "Tipo de conta inválido. O tipo de conta selecionado não existe."),
    Status1045("1045", "Código de segurança inválido. O CVV digitado está incorreto. Oriente o portador a tentar novamente, se atentando ao preenchimento do CVV."),
    Status1049("1049", "Banco/emissor do cartão inválido. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2000("2000", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2001("2001", "Cartão vencido. Você pode orientar o portador a entrar em contato com o banco emissor do cartão.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status2002("2002", "Transação não permitida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2003("2003", "Rejeitado emissor. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2004("2004", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2005("2005", "Transação não autorizada. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2006("2006", "Tentativas de senha excedidas. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2007("2007", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2008("2008", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status2009("2009", "Cartão com restrição. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status5003("5003", "Erro interno. Tente refazer a transação.", OperacaoRetornoCobrancaEnum.REENVIAR),
    Status5006("5006", "Erro interno. Tente refazer a transação.", OperacaoRetornoCobrancaEnum.REENVIAR),
    Status5025("5025", "Código de segurança (CVV) do cartão não foi enviado. Oriente o portador a certificar-se que foi realizado o envio do CVV no fechamento do checkout."),
    Status5054("5054", "Erro interno. Tente refazer a transação."),
    Status5062("5062", "Transação não permitida para este produto ou serviço. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status5086("5086", "Cartão poupança inválido. O tipo de conta selecionado não existe."),
    Status5088("5088", "Transação não autorizada Amex. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status5089("5089", "Erro interno.Tente refazer a transação.", OperacaoRetornoCobrancaEnum.REENVIAR),
    Status5092("5092", "O valor solicitado para captura não é válido."),
    Status5093("5093", "Banco emissor Visa indisponível. Transação não autorizada."),
    Status5095("5095", "Erro interno.Tente refazer a transação."),
    Status5097("5097", "Erro interno.Tente refazer a transação."),
    Status9102("9102", "Transação inválida. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status9103("9103", "Cartão cancelado. Você pode orientar o portador a entrar em contato com o banco emissor do cartão.", CodigoRetornoPactoEnum.CARTAO_CANCELADO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status9107("9107", "O banco/emissor do cartão ou a conexão parece estar offline. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status9108("9108", "Erro no processamento. Tente refazer a transação."),
    Status9109("9109", "Erro no processamento. Tente refazer a transação."),
    Status9111("9111", "Time-out na transação. Tente refazer a transação."),
    Status9112("9112", "Emissor indisponível. Você pode orientar o portador a entrar em contato com o banco emissor do cartão."),
    Status9113("9113", "Transmissão duplicada. É possível que o cliente já tenha realizado a compra com sucesso e está erroneamente enviando o pagamento uma segunda vez."),
    Status9124("9124", "Código de segurança inválido. O CVV digitado está incorreto. Oriente o portador a tentar novamente, se atentando ao preenchimento do CVV."),
    Status9999("9999", "Erro não especificado. Tente refazer a transação."),
    StatusIMSG("IMSG", "Algum dado enviado na criação da transação não condiz com o modo de leitura aceito pela adquirente."),
    Status8888("8888", "Erro ao realizar o envio da transação. Indisponibilidade Pagar.me.");


    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private PagarMeRetornoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private PagarMeRetornoEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private PagarMeRetornoEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static PagarMeRetornoEnum valueOff(String id) {
        for (PagarMeRetornoEnum stone : PagarMeRetornoEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}

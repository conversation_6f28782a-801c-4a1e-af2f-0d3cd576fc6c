package servicos.impl.pagarMe;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.apf.APF;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 10/05/2020
 */
public class TransacaoPagarMeVO extends TransacaoVO {

    @Override
    public String getValorCartaoMascarado() {
        return getCartaoMascarado();
    }

    @Override
    public String getResultadoRequisicao() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            if (json.has("errors") && json.getJSONArray("errors").length() > 0) {
                String message =  json.getJSONArray("errors").getJSONObject(0).optString("message");
                if (!UteisValidacao.emptyString(message)) {
                    if (message.contains("with status refused is not allowed to create a transaction")) {
                        return "Inconsistência nos recebedores. Você deve regularizar o cadastro dos recebedores lá no portal da Pagar.me e só depois tentar novamente.";
                    }
                    return message;
                }
            }
        } catch (Exception ignored) {
        }

        if (erroAntifraud()){
            return "Transação recusada pelo sistema de antifraude da Pagarme";
        }

        try {

            String code = obterValorParamsResposta("acquirer_response_code");
            PagarMeRetornoEnum pagarMeRetornoEnum = PagarMeRetornoEnum.valueOff(code);
            if (!pagarMeRetornoEnum.equals(PagarMeRetornoEnum.StatusNENHUM)) {
                return pagarMeRetornoEnum.getDescricao();
            } else {
                return "A Pagar.me não nos informou o motivo da transação negada";
            }
        } catch (Exception ex) {
            return "Erro desconhecido";
        }
    }

    @Override
    public String getAutorizacao() {
        if (erroAntifraud()) {
            return "";
        } else {
            return obterValorParamsResposta("authorization_code");
        }
    }

    @Override
    public String getBandeira() {
        try {
            String brand = "";

            try {
                brand = obterValorParamsResposta("card_brand");
                if (!UteisValidacao.emptyString(brand)) {
                    return brand.toUpperCase();
                }
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(brand)) {
                brand = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoBandeira);
            }
            return brand;
        } catch (Exception ex) {
            return "";
        }
    }

    @Override
    public String getCartaoMascarado() {
        String card_first_digits = obterValorParamsResposta("card_first_digits");
        String card_last_digits = obterValorParamsResposta("card_last_digits");
        if (!UteisValidacao.emptyString(card_first_digits) && !UteisValidacao.emptyString(card_last_digits)) {
            return card_first_digits + "*******" + card_last_digits;
        }

        try {
            String card = obterItemOutrasInformacoes(AtributoTransacaoEnum.cartaoMascarado);
            return APF.getCartaoMascarado(card);
        } catch (Exception e) {
            return "";
        }
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao) && !UteisValidacao.emptyString(getCodigoExterno())) {
            return getCodigoExterno();
        }
        if (nomeAtributo.equals(APF.CodigoAutorizacao)) {
            return getAutorizacao();
        }
        if (nomeAtributo.equals(APF.CartaoMascarado)) {
            return getCartaoMascarado();
        }
        return valor;
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) {
        return getResultadoRequisicaoCancelamento();
    }

    @Override
    public String getCodErroExterno() {
        if (erroAntifraud()) {
            return "999";
        } else {
            return obterValorParamsResposta("acquirer_response_code");
        }
    }

    @Override
    public String getNSU() {
        if (erroAntifraud()) {
            return "";
        } else {
            return obterValorParamsResposta("nsu");
        }
    }

    @Override
    public String getTID() {
        return obterValorParamsResposta("tid");
    }

    private String getResultadoRequisicaoCancelamento() {
        return "Não foi possível realizar o cancelamento. Tente novamente mais tarde.";
    }

    public String getAdquirente(){
        return obterValorParamsResposta("acquirer_name");
    }

    private String obterValorParamsResposta(String key) {
        try {
            JSONObject retornoJSON = new JSONObject(getParamsResposta());
            return retornoJSON.optString(key);
        } catch (Exception ex) {
            return "";
        }
    }

    private boolean erroAntifraud() {
        //verifica se o motivo de negar a transação foi o antifraude
        try {
            String status = obterValorParamsResposta("status");
            String antiFraud = obterValorParamsResposta("refuse_reason");
            return (status.equalsIgnoreCase("refused") && antiFraud.equalsIgnoreCase("antifraud"));
        } catch (Exception ignored) {
            return false;
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            return obj.getInt("installments");
        } catch (Exception ex) {
            return 0;
        }
    }

    public String getIdentificadorPacto() {
        String identificador = "";
        try {
            JSONObject jsonEnvio = new JSONObject(getParamsEnvio());
            identificador = jsonEnvio.optString("reference_key");
        } catch (Exception ignored) {
        }

        if (!UteisValidacao.emptyString(identificador)) {
            return identificador;
        }

        identificador = obterItemOutrasInformacoes(AtributoTransacaoEnum.identificadorPacto);
        if (!UteisValidacao.emptyString(identificador)) {
            return identificador;
        }
        return "";
    }
}

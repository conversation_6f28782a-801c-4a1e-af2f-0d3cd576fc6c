/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcccaixaonline;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/*
 * Created by <PERSON><PERSON><PERSON> on 02/10/2024.
 */
public enum DCCCaixaOnlineRetornoEnum {
    //Lista retirada do link abaixo. Como são muitos retornos, adicionei apenas os principais.
    //https://dev.softwareexpress.com.br/docs/e-sitef/codigos-da-api-resposta

    StatusNENHUM("NENHUM", ""),
    Status0("0","Transação OK"),
    Status9("9","Campo cardExpireDate inválido"),
    Status10("10","Campo cardNumber está nulo"),
    Status11("11","Campo cardNumber inválido"),
    Status27("27","Campo merchantKey inválido"),
    Status255("255","Transação já efetuada"),
    Status1026("1026","Merchant não tem registro de Chave SSH Pública na Ciaxa");

    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private DCCCaixaOnlineRetornoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private DCCCaixaOnlineRetornoEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private DCCCaixaOnlineRetornoEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static DCCCaixaOnlineRetornoEnum valueOff(String id) {
        DCCCaixaOnlineRetornoEnum[] values = DCCCaixaOnlineRetornoEnum.values();
        for (DCCCaixaOnlineRetornoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return StatusNENHUM;
    }

    public static String obterCodigosRetorno(CodigoRetornoPactoEnum retornoPacto) {
        StringBuilder retorno = new StringBuilder();
        for (DCCCaixaOnlineRetornoEnum obj : DCCCaixaOnlineRetornoEnum.values()) {
            if (obj.getCodigoRetornoPacto().equals(retornoPacto)) {
                retorno.append("'").append(obj.getId()).append("'").append(",");
            }
        }
        if (!UteisValidacao.emptyString(retorno.toString())) {
            retorno.deleteCharAt(retorno.length() - 1);
        }
        return retorno.toString();
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}

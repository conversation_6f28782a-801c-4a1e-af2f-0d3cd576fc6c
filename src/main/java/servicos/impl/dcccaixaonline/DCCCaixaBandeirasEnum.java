/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcccaixaonline;

/*
 * Created by <PERSON><PERSON><PERSON> on 23/09/2024.
 */
public enum DCCCaixaBandeirasEnum {

    //Link da documentao onde encontra a lista de bandeiras da Fiserv
    //Adicionei as principais.
    //https://dev.softwareexpress.com.br/docs/e-sitef/autorizadoras/

    NENHUM("0","Nenhum", ""),
    VISA("1","VISA", "Visa"),
    MASTERCARD("2","MASTERCARD","Mastercard"),
    AMERICAN_EXPRESS("3","AMERICAN EXPRESS","Amex"),
    HIPER("5","HIPER","Hipercard"),
    DINERS("33","DINERS","Diners"),
    ELO("41","ELO","Elo"),
    DISCOVER("44", "DISCOVER", "Discover"),
    ;

    private String codigoFiserv;
    private String descricaoFiserv;
    private String descricaoPacto;

    private DCCCaixaBandeirasEnum(String codigoFiserv, String descricaoFiserv, String descricaoPacto) {
        this.codigoFiserv = codigoFiserv;
        this.descricaoFiserv = descricaoFiserv;
        this.descricaoPacto = descricaoPacto;
    }

    public String getCodigoFiserv() {
        return codigoFiserv;
    }

    public String getDescricaoFiserv() {
        return descricaoFiserv;
    }

    public String getDescricaoPacto() {
        return descricaoPacto;
    }

    public static DCCCaixaBandeirasEnum obterBandeiraFiserv(String descricaoPacto) {
        DCCCaixaBandeirasEnum[] values = DCCCaixaBandeirasEnum.values();
        for (DCCCaixaBandeirasEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getDescricaoPacto().equals(descricaoPacto)) {
                return eDIStatusEnum;
            }
        }
        return NENHUM;
    }

    public static DCCCaixaBandeirasEnum obterBandeiraFiservCodigo(String codigoFiserv) {
        DCCCaixaBandeirasEnum[] values = DCCCaixaBandeirasEnum.values();
        for (DCCCaixaBandeirasEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getCodigoFiserv().equals(codigoFiserv)) {
                return eDIStatusEnum;
            }
        }
        return NENHUM;
    }

}

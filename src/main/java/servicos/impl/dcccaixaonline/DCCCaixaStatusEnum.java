/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcccaixaonline;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/*
 * Created by <PERSON><PERSON><PERSON> on 23/09/2024.
 */
public enum DCCCaixaStatusEnum {

    StatusNENHUM("","Nenhum", ""),
    StatusNOV("NOV","Nova","Transação recém-criada"),
    StatusINV("INV","Inválida","Transação não foi criada com sucesso, provavelmente a loja enviou algum parâmetro incorreto, e não foi possível inicializar a transação corretamente."),
    StatusPPC("PPC","Pendente de confirmação","Pagamento pendente de confirmação."),
    StatusPPN("PPN","Desfeita","Pagamento pendente não confirmado (cancelado)."),
    StatusPEN("PEN","Aguardando pagamento","Transação aguardando resposta da instituição financeira."),
    StatusCON("CON","Efetuada","Transação confirmada pela instituição financeira."),
    StatusNEG("NEG","Negada","Transação negada pela instituição financeira."),
    StatusERR("ERR","Erro ao efetuar pagamento","Erro na comunicação com a autorizadora. Tente novamente."),
    StatusBLQ("BLQ","Bloqueada", "Transação bloqueada após múltiplas tentativas de consulta de cartão."),
    StatusEXP("EXP","Expirada", "A transação expirou devido ao prazo de validade do NIT."),
    StatusEST("EST","Estornada","Pagamento estornado."),
    StatusAGU("AGU","Aguardando Usuário","Transação aguardando ação do usuário."),
    StatusABA("ABA","Abandonada","Transação expirou devido a tempo excessivo sem atualização por parte do usuário."),
    StatusCAN("CAN","Cancelada","Transação cancelada pelo usuário/comprador."),
    StatusRET("RET","Retentativa","Transação negada pela instituição financeira, porém agendada para retentativa.");

    private String codigo;
    private String nome;
    private String descricao;

    private DCCCaixaStatusEnum(String codigo, String nome, String descricao) {
        this.codigo = codigo;
        this.nome = nome;
        this.descricao = descricao;
    }

    public String getNome() {
        return nome;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static DCCCaixaStatusEnum valueOff(String codigo) {
        DCCCaixaStatusEnum[] values = DCCCaixaStatusEnum.values();
        for (DCCCaixaStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getCodigo().equals(codigo)) {
                return eDIStatusEnum;
            }
        }
        return StatusNENHUM;
    }

}

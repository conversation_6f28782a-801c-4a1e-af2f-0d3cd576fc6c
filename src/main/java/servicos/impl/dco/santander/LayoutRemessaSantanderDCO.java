/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dco.santander;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.IdentificadorClienteEmpresaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import servicos.impl.dcc.bb.DCOBBStatusEnum;
import servicos.impl.dco.febraban.LayoutRemessaFebrabanDCO;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LayoutRemessaSantanderDCO extends LayoutRemessaFebrabanDCO {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito =  remessa.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "A");
        header.put(DCCAttEnum.CodigoRemessa, "1");
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 20));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getRazaoSocial(), 20));
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), MathContext.UNLIMITED), 3));
        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 20));
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "yyyyMMdd"));

        String numeroResumoOperacoes = "";
        RegistroRemessa headerRemessa = remessa.getHeaderRemessa();
        for (ObjetoGenerico obj : headerRemessa.getAtributos()) {
            if (obj.getAtributo().equals(DCCAttEnum.NumeroResumoOperacoes.name())) {
                numeroResumoOperacoes = StringUtilities.formatarCampo(new BigDecimal(obj.getValor()), 6);
            }
        }
        numeroResumoOperacoes = UteisValidacao.emptyString(numeroResumoOperacoes) ?
                StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getSequencialDoArquivo()), 6)
                : numeroResumoOperacoes;

        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(numeroResumoOperacoes), 6));//sequencial do arquivo zerado para o banco não exigir o sequencial correto
        remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), numeroResumoOperacoes);
        header.put(DCCAttEnum.VersaoLayout, "05");
        header.put(DCCAttEnum.IdentificacaoServico, "DEBITO AUTOMATICO");
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(52));


        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            //E01-Código do Registro 001 - 001 X(001) ?E?
            detail.put(DCCAttEnum.TipoRegistro, "E");
            //E02-Identificação do Cliente na Empresa 002 - 026 X(025) O conteúdo deverá ser idêntico ao anteriormente enviado pelo Banco, no registro tipo ?B?
            if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.COD_PESSOA)) {
                detail.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getPessoa().getCodigo()), 25));
            } else if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.CPF)) {
                detail.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.formatarCampoEmBranco(StringUtilities.formatarCpfCnjp(item.getMovParcela().getPessoa().getCfp(), 11), 25));
            }else if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.MATRICULA)) {
                detail.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.formatarCampoEmBranco(item.getClienteVO().getMatricula(), 25));
            }
            //E03-Agência para Débito 027 - 030 X(004) O conteúdo deverá ser idêntico ao anteriormente enviado pelo Banco, no registro tipo ?B?
            detail.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.AgenciaDebito.name()), MathContext.UNLIMITED), 4));
            //E04-Identificação do Cliente no Banco 031 - 044 X(014) O conteúdo deverá ser idêntico ao anteriormente enviado pelo Banco, no registro tipo ?B?
            if (UteisValidacao.emptyString(item.get(DCCAttEnum.IdentificadorClienteBanco.name()))) {
                detail.put(DCCAttEnum.IdentificadorClienteBanco, StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.ContaCorrente.name()) + item.get(DCCAttEnum.ContaCorrenteDigito.name()), MathContext.UNLIMITED), 14));
            } else {
                detail.put(DCCAttEnum.IdentificadorClienteBanco, StringUtilities.formatarCampoEmBranco(item.get(DCCAttEnum.IdentificadorClienteBanco.name()), 14));
            }
            //E05-Data do Vencimento 045 - 052 9(008) Data em que deverá ser efetuado o débito na conta. Ser for informado um dia não útil, o débito será efetuado no primeiro dia útil subseqüente. Formato AAAAMMDD.
            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(getDataVencimento(item.getMovParcela().getDataVencimento(), dataDeposito, remessa), "yyyyMMdd"));
            //E06-Valor do Débito 053 - 067 9(015) Valor a ser debitado na conta. Quando for igual a ?zero?, será utilizado para efeito de ?manutenção? da autorização no cadastro de cliente.
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));
            //E07-Código da moeda 068 - 069 X(002) ?01? = UFIR, neste caso, ler o valor do débito com 5 decimais ?03? = REAL, neste caso, ler o valor do débito com 2 decimais
            detail.put(DCCAttEnum.Moeda, "03");
            //E08-Uso da Empresa 070 - 128 129 ? 129 X(059) X(001) Esta informação não será tratada pelo Banco (posições 070 a 118). Será retornada para a empresa, com o mesmo conteúdo enviado. Se X na ultima posição = tratamento acordado com a Empresa
            //reservado para empresa = 60 caracteres
            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getCodigo()), 7));
            detail.put(DCCAttEnum.NomePessoa, StringUtilities.formatarCampoEmBranco(item.getMovParcela().getPessoa().getNome(), 53));

            String cpfCnpj = item.get(DCCAttEnum.CpfOuCnpj.name());
            if(UteisValidacao.emptyString(cpfCnpj)){
                cpfCnpj = item.getPessoa().getCfp().replaceAll("[.,-]", "");
            }

            //E09-Tipo de Identificação  130 - 130 9(001) 2 = CPF 1 -  CNPJ
            detail.put(DCCAttEnum.CpfOuCnpj, cpfCnpj.length() == 11 ? 2 : 1);
            //E10-Identificação 131 ? 145 9(015) O preenchimento do campo deverá obedecer: CNPJ: 999999999 = Número, 9999 = Filial, e 99 = DV CPF: 0000999999999 = Número, 99 = DV (alinhamento conforme item 9.2)
            detail.put(DCCAttEnum.CpfCliente, StringUtilities.formatarCpfCnjp(cpfCnpj, 15));
            //E11-Reservado para o futuro  146 ? 149 X(004) Brancos
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(4));
            //E12-Código de movimento 150 ? 150 9(001) 0 = Débito Normal 1 = Cancelamento (exclusão) de lançamento enviado anteriormente para o Banco. O cancelamento só será efetuado, desde que o débito ainda não tenha sido efetivado.
            detail.put(DCCAttEnum.CodigoMovimento, 0);

            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "Z");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 6));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 17));
        trailer.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(126));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("A")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("B")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_B);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("F")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("J")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_J);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("Z")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CodigoRemessa, StringUtilities.readString(1, 2, linha));
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(2, 22, linha));
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(22, 42, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(42, 45, linha));
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(45, 65, linha));
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(65, 73, linha));
            r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(73, 79, linha));
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(79, 81, linha));
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(81, 98, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(98, 150, linha));
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 150) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
                r.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.readString(1, 26, linha));
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(26, 30, linha));
                r.put(DCCAttEnum.IdentificadorClienteBanco, StringUtilities.readString(30, 44, linha));
                r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(44, 52, linha));
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(52, 67, linha));
                final String status = StringUtilities.readString(67, 69, linha);
                r.put(DCCAttEnum.StatusVenda, status);
                //Se o Débito foi efetuado, usar como Código de Autorização a Conta/Corrente do Débito realizado
                if (status != null && !status.isEmpty() && DCOBBStatusEnum.Status00.getId().equals(status)) {
                    r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(1, 26, linha));
                }
                //reservado para empresa = 70 caracteres
                //7 - Comprovante de Venda
                //63 - Nome Pessoa
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(69, 76, linha));
                r.put(DCCAttEnum.NomePessoa, StringUtilities.readString(76, 139, linha));
                //fim reservado
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(139, 149, linha));
                r.put(DCCAttEnum.CodigoMovimento, StringUtilities.readString(149, 150, linha));
            }
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE_REGISTRO_B) {
            if (linha.length() >= 150) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
                r.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.readString(1, 26, linha));
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(26, 30, linha));
                r.put(DCCAttEnum.IdentificadorClienteBanco, StringUtilities.readString(30, 44, linha));
                r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(44, 52, linha));
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(52, 149, linha));
                r.put(DCCAttEnum.CodigoMovimento, StringUtilities.readString(149, 150, linha));
            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(1, 7, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(7, 24, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(24, 150, linha));

        }
    }

    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                String tipo = StringUtilities.readString(0, 1, linha);
                if (tipo.equals("F")) {
                    codigos.add(Integer.valueOf(StringUtilities.readString(69, 76, linha)));
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Integer cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }


}

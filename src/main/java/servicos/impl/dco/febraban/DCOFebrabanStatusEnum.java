/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dco.febraban;

/**
 *
 * <AUTHOR>
 */
public enum DCOFebrabanStatusEnum {
    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status00("00", "Débito efetuado"),
    Status01("01", "Débito não efetuado - Insuficiência de fundos"),
    Status02("02", "Débito não efetuado - Conta corrente não cadastrada"),
    Status03("04", "Débito não efetuado - Outras restrições"),
    Status04("05", "Débito não efetuado - valor do débito excede valor limite aprovado."),
    //
    Status10("10", "Débito não efetuado - Agência em regime de encerramento"),
    Status12("12", "Débito não efetuado - Valor inválido"),
    Status13("13", "Débito não efetuado - Data de lançamento inválida"),
    Status14("14", "Débito não efetuado - Agência inválida"),
    Status15("15", "Débito não efetuado - conta corrente inválida"),
    //
    Status18("18", "Débito não efetuado - Data do débito anterior à do processamento"),
    //
    Status30("30", "Débito não efetuado - Sem contrato de débito automático "),
    Status31("31", "Débito efetuado em data diferente da data informada ? feriado na praça de débito"),
    Status96("96", "Manutenção do Cadastro"),
    Status97("97", "Cancelamento - Não encontrado"),
    Status98("98", "Cancelamento - Não efetuado, fora do tempo hábil"),
    Status99("99", "Cancelamento - cancelado conforme solicitação");
    //
    private String id;
    private String descricao;

    private DCOFebrabanStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOFebrabanStatusEnum valueOff(String id) {
        DCOFebrabanStatusEnum[] values = DCOFebrabanStatusEnum.values();
        for (DCOFebrabanStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCOFebrabanStatusEnum.StatusNENHUM;
    }
}

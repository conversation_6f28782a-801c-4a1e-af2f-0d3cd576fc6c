package servicos.impl.email;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.File;

public class EmailNovoLoginService {

    private ConfiguracaoSistemaCRMVO obterConfigCRM() throws Exception {
        ConfiguracaoSistemaCRMVO configCRMVO = SuperControle.getConfiguracaoSMTPNoReply();
        configCRMVO.setRemetentePadrao("Sistema Pacto");
        if (!configCRMVO.isConfiguracaoEmailValida()) {
            throw new Exception("ConfiguracaoSistemaCRMVO não é válida");
        }
        return configCRMVO;
    }

    public void enviarEmailRedefinirSenha(String email, String nomeCliente, String nomeEmpresa, String urlLink) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = obterConfigCRM();
            UteisEmail uteis = new UteisEmail();
            if (!UteisValidacao.emptyString(nomeEmpresa)) {
                uteis.novo("ACADEMIA " + nomeEmpresa.toUpperCase() + " - Solicitação de alteração da senha - " + toCamelCase(nomeCliente), configCRMVO);
            } else {
                uteis.novo("Solicitação de alteração da senha - " + toCamelCase(nomeCliente), configCRMVO);
            }
            uteis.enviarEmail(email, "", gerarCorpoEmailRedefinirSenha(nomeCliente, urlLink).toString(), "",
                    configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private StringBuilder gerarCorpoEmailRedefinirSenha(String nome, String urlLink) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailRedefinirSenhaNovoLogin_V1.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nome.toUpperCase()))
                .replaceAll("#URL_LINK", urlLink);
        return new StringBuilder(aux);
    }

    public void enviarEmailBoasVindas(String email, String nomeCliente, String nomeEmpresa,
                                                      String urlLink) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = obterConfigCRM();
            UteisEmail uteis = new UteisEmail();
            if (!UteisValidacao.emptyString(nomeEmpresa)) {
                uteis.novo("ACADEMIA " + nomeEmpresa.toUpperCase() + " - Bem-vindo(a) à sua conta do Sistema Pacto - " + toCamelCase(nomeCliente), configCRMVO);
            } else {
                uteis.novo("Bem-vindo(a) à sua conta do Sistema Pacto - " + toCamelCase(nomeCliente), configCRMVO);
            }
            uteis.enviarEmail(email, "", gerarCorpoEmailBoasVindas(nomeCliente, nomeEmpresa, urlLink).toString(), "",
                    configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private StringBuilder gerarCorpoEmailBoasVindas(String nome , String nomeEmpresa, String urlLink) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailBoasVindasNovoLogin_V1.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nome.toUpperCase()))
                .replaceAll("#NOME_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeEmpresa.toUpperCase()))
                .replaceAll("#URL_LINK", urlLink);
        return new StringBuilder(aux);
    }

    public void enviarEmailAtivarConta(String email, String nomeCliente, String nomeEmpresa, String urlLink) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = obterConfigCRM();
            UteisEmail uteis = new UteisEmail();
            if (!UteisValidacao.emptyString(nomeEmpresa)) {
                uteis.novo("ACADEMIA " + nomeEmpresa + " - Clique para ativar sua conta - " + toCamelCase(nomeCliente), configCRMVO);
            } else {
                uteis.novo("Clique para ativar sua conta - " + toCamelCase(nomeCliente), configCRMVO);
            }
            uteis.enviarEmail(email, "", gerarCorpoEmailAtivarConta(nomeCliente, nomeEmpresa, urlLink).toString(), "",
                    configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private StringBuilder gerarCorpoEmailAtivarConta(String nome, String nomeEmpresa, String urlLink) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailAtivarContaNovoLogin_V1.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nome.toUpperCase()))
                .replaceAll("#NOME_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeEmpresa.toUpperCase()))
                .replaceAll("#URL_LINK", urlLink);
        return new StringBuilder(aux);
    }

    public void enviarEmailCodigoVerificacao(String email, String nomeCliente, String nomeEmpresa, String codigoVerificacao,
                                             String urlLink, boolean enviarLink, Boolean trocaEmailVincularDados) throws Exception {
        try {
            ConfiguracaoSistemaCRMVO configCRMVO = obterConfigCRM();
            UteisEmail uteis = new UteisEmail();
            if (!UteisValidacao.emptyString(nomeEmpresa)) {
                uteis.novo("ACADEMIA " + nomeEmpresa + " - Sistema Pacto: Código de verificação - " + toCamelCase(nomeCliente), configCRMVO);
            } else {
                uteis.novo("Sistema Pacto: Código de verificação - " + toCamelCase(nomeCliente), configCRMVO);
            }
            if (trocaEmailVincularDados) {
                uteis.enviarEmail(email, "", gerarCorpoEmailVincularDadosAcesso(email, nomeCliente, nomeEmpresa, urlLink).toString(), "",
                        configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
            } else {
                uteis.enviarEmail(email, "", gerarCorpoEmailCodigoVerificacao(nomeCliente, nomeEmpresa, codigoVerificacao, urlLink, enviarLink).toString(), "",
                        configCRMVO.getIntegracaoPacto(), configCRMVO.preparaEnvioSendy());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private StringBuilder gerarCorpoEmailCodigoVerificacao(String nome, String nomeEmpresa, String codigoVerificacao, String urlLink, boolean enviarLink) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailCodigoVerificacao_V1.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nome.toUpperCase()))
                .replaceAll("#CODIGO_VERIFICACAO", codigoVerificacao)
                .replaceAll("#URL_LINK", urlLink)
                .replaceAll("#NOME_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeEmpresa.toUpperCase()))
                .replaceAll("#DISPLAY_LINK", enviarLink ? "block" : "none");
        return new StringBuilder(aux);
    }

    private StringBuilder gerarCorpoEmailVincularDadosAcesso(String email, String nome, String nomeEmpresa, String urlLink) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailVincularDadosAcessoPorEmail_V1.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String aux = texto.toString()
                .replaceAll("#NOME_USUARIO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nome.toUpperCase()))
                .replaceAll("#URL_LINK", urlLink)
                .replaceAll("#NOME_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeEmpresa.toUpperCase()))
                .replaceAll("#NOVO_EMAIL", email);
        return new StringBuilder(aux);
    }

    private String toCamelCase(String s) {
        String[] names = s.trim().split(" ");
        StringBuilder result = new StringBuilder();
        for (String name : names) {
            String lower = name.toLowerCase().trim();
            if (lower.length() == 0) {
                continue;
            }
            result.append(Character.toUpperCase(lower.charAt(0))).append(lower.substring(1)).append(" ");
        }
        result.deleteCharAt(result.length() - 1);
        return result.toString();
    }
}

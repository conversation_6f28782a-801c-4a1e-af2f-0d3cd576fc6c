package servicos.impl.tokenOperacao;

import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.tokenOperacao.TokenOperacao;
import negocio.tokenOperacao.TokenOperacaoVO;

import java.sql.Connection;
import java.util.UUID;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 14/11/2023
 */

public class TokenOperacaoService extends SuperEntidade {

    public TokenOperacaoService(Connection con) throws Exception {
        super(con);
    }

    public TokenOperacaoVO consultarGerarToken(int usuario) throws Exception {
        try {
            TokenOperacaoVO tokenValidoExistente = consultarTokenExistente(usuario);

            //Caso já tenha token gerado válido
            if (tokenValidoExistente != null) {
                return tokenValidoExistente;
            } else {
                return gerarNovo(usuario);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public TokenOperacaoVO consultarTokenExistente(int usuario) throws Exception {
        TokenOperacao tokenOperacaoDAO;
        try {
            tokenOperacaoDAO = new TokenOperacao(con);

            return tokenOperacaoDAO.consultarUltimoGeradoPeloUsuario(usuario);

        } catch (Exception ex) {
            return null;
        } finally {
            tokenOperacaoDAO = null;
        }
    }

    public TokenOperacaoVO gerarNovo(int usuario) throws Exception {
        TokenOperacao tokenOperacaoDAO;
        try {
            tokenOperacaoDAO = new TokenOperacao(con);

            String uuid = UUID.randomUUID().toString();
            String tokenRandomico = uuid.substring(0, 6).toUpperCase();

            TokenOperacaoVO token = new TokenOperacaoVO();
            token.setDataRegistro(Calendario.hoje());
            token.setUsuario(usuario);
            token.setToken(tokenRandomico);

            tokenOperacaoDAO.incluir(token);

            return token;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            tokenOperacaoDAO = null;
        }
    }

}

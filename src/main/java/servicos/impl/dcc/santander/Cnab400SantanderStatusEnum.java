package servicos.impl.dcc.santander;


/**
 * Created by <PERSON> on 31/08/2016.
 */
public enum Cnab400SantanderStatusEnum {
    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status01("01", "Título não existe"),
    Status02("02", "Entrada Confirmada com possibilidade de mensagem"),
    Status03("03", "Entrada título rejeitada"),
    Status06("06", "Liquidação normal"),
    Status07("07", "Liquidação por conta"),
    Status08("08", "Liquidação por saldo"),
    Status09("09", "Baixa automática"),
    Status10("10","Título baixa confirmada pela instrução ou por título protestado"),
    Status12("12","Abatimento concedido"),
    Status13("13","Abatimento cancelado"),
    Status14("14","Prorrogação de vencimento"),
    Status15("15","Enviado para Cartório"),
    Status16("16","<PERSON><PERSON><PERSON>lo já baixado ou liquidado"),
    Status17("17","Liquidado em cartório"),
    Status21("21","Entrada em Cartório"),
    Status22("22","Retirado de cartório"),
    Status24("24","Custas de Cartório"),
    Status25("25","Protestar Título"),
    Status26("26","Sustar Protesto"),
    Status9999("9999", "Retorno manual");

    private String id;
    private String descricao;

    Cnab400SantanderStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    public static Cnab400SantanderStatusEnum valueOff(String id) {
        Cnab400SantanderStatusEnum[] values = values();
        for (Cnab400SantanderStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return Cnab400SantanderStatusEnum.StatusNENHUM;
    }
}

package servicos.impl.dcc.sicoob;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jboleto.bancos.Bancoob;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Created by Luiz Felipe
 */
public class LayoutRemessaSicoobCNAB240 extends LayoutRemessaBase {

    private static Pattern patternHEAD  = Pattern.compile("^[0-9]{8}\\s");
    private static Pattern patternT  = Pattern.compile("^[0-9]{13}T");
    private static Pattern patternU  = Pattern.compile("^[0-9]{13}U");
    private static Pattern patterTRAILER = Pattern.compile("^[0-9]{7}5");

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        header.put(DCCAttEnum.LoteServico, "0000");
        header.put(DCCAttEnum.TipoRegistro, "0");
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        header.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        header.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
//        header.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Integer.valueOf(remessa.getConvenioCobranca().getNumeroContrato()), 9));
        header.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoEmBranco(20));
        header.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        header.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
        header.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
        header.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(1));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getRazaoSocial(), 30));
        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco("SICOOB", 30));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
        header.put(DCCAttEnum.CodigoRemessa, "1");
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
        header.put(DCCAttEnum.HoraGeracao, StringUtilities.formatarCampoData(dataDeposito, "HHmmss"));
        header.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getCodigo(), 6));
        header.put(DCCAttEnum.VersaoLayout, "081");
        header.put(DCCAttEnum.DensidadeGravacao, "00000");
        header.put(DCCAttEnum.ReservadoBanco, StringUtilities.formatarCampoEmBranco(20));
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(20));
        header.put(DCCAttEnum.ReservadoBanco2, StringUtilities.formatarCampoEmBranco(29));

        remessa.setHeaderRemessa(header);
        remessa.setHeaderRemessaArquivo(getHeaderLote(remessa, dataDeposito));

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 1;
        for (RemessaItemVO item : lista) {
            item.setRemessa(remessa);
            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    enderecoVO = endereco;
                }
            }
            if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                    enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
                }
            }

            RegistroRemessa detailP = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_P);
            detailP.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailP.put(DCCAttEnum.LoteServico, "0001");
            detailP.put(DCCAttEnum.TipoRegistro, "3");
            detailP.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailP.put(DCCAttEnum.Segmento, "P");
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.CodigoMovimento, "01");
            detailP.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));//
            detailP.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
            detailP.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
            detailP.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.NossoNumero, calcularNossoNumero(item));
            detailP.put(DCCAttEnum.Carteira, "1");
            detailP.put(DCCAttEnum.FormaDeCadastramentoTituloBanco, "0");
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.IdentificacaoEmissaoBoleto, "2");
            detailP.put(DCCAttEnum.TipoDistribuicao, "2");
            detailP.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoEmBranco(item.getIdentificador().toString(), 15));
            detailP.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));
            detailP.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 15));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(5));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));

            detailP.put(DCCAttEnum.EspecieTitulo, "04");

            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, "N");
            detailP.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));

            //JUROS
            boolean cobrarJuros = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros();
            if (cobrarJuros) {
                //Juros por valor fixo
                if (remessa.getConvenioCobranca().getEmpresa().isUtilizarJurosValorAbsoluto()) {
                    detailP.put(DCCAttEnum.CodigoJuros, "1");
                    detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoData(Calendario.somarDias(item.getDataVencimentoBoleto(), 1), "ddMMyyyy"));
                    detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica(), 15));
                } else { //Juros por percentual (mensal)
                    Double jurosMesPercentual = 0.0;
                    jurosMesPercentual = Uteis.arredondarForcando2CasasDecimais(remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica() * 30);
                    detailP.put(DCCAttEnum.CodigoJuros, "2");
                    detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoData(Calendario.somarDias(item.getDataVencimentoBoleto(), 1), "ddMMyyyy"));
                    detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(jurosMesPercentual, 15));
                }
            } else {
                detailP.put(DCCAttEnum.CodigoJuros, "0"); //verificar manual, pois no mesmo os valores são 1,2,3, já no validador temos o número 0
                detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoZerado(8));
                detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoZerado(15));
            }

            validarDesconto(item, detailP);
            detailP.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, StringUtilities.formatarCampoEmBranco(item.getIdentificador().toString(), 25));
            detailP.put(DCCAttEnum.Protesto, "3");
            detailP.put(DCCAttEnum.DiasProtesto, "00");
            detailP.put(DCCAttEnum.CodigoBaixa, "0");
            detailP.put(DCCAttEnum.NumeroDiasBaixa, StringUtilities.formatarCampoEmBranco(3));
            detailP.put(DCCAttEnum.Moeda, "09");
            detailP.put(DCCAttEnum.NumeroContratoOperacaoCredito, StringUtilities.formatarCampoZerado(10));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));



            RegistroRemessa detailQ = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_Q);
            detailQ.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailQ.put(DCCAttEnum.LoteServico, "0001");
            detailQ.put(DCCAttEnum.TipoRegistro, "3");
            seq++;
            detailQ.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailQ.put(DCCAttEnum.Segmento, "Q");
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailQ.put(DCCAttEnum.CodigoMovimento, "01");
            detailQ.put(DCCAttEnum.TipoInscricao, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "1" : "2");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detailQ.put(DCCAttEnum.CpfOuCnpj, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 15));
            detailQ.put(DCCAttEnum.NomeCliente, StringUtilities.formatarCampoEmBranco(item.getPessoa().getNome(), 40));
            detailQ.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 40));
            detailQ.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 15));
            detailQ.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
            detailQ.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNome(), 15));
            detailQ.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(16));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(3));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(20));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
            seq++;

            RegistroRemessa detailR = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_R);
            //01-03 num(3)
            detailR.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            //04-07 num(4)
            detailR.put(DCCAttEnum.LoteServico, "0001");
            //08-08 num(1)
            detailR.put(DCCAttEnum.TipoRegistro, "3");
            //09-13 num(5)
            detailR.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));

            detailR.put(DCCAttEnum.Segmento, "R");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.CodigoMovimento, "01");
            detailR.put(DCCAttEnum.CodigoDesconto, "0");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(13));
            detailR.put(DCCAttEnum.CodigoDesconto, "0");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(17));

            //MULTA
            boolean cobrarMulta = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros();
            if (cobrarMulta) {
                //Multa por valor fixo
                if (remessa.getConvenioCobranca().getEmpresa().isUtilizarMultaValorAbsoluto()) {
                    detailR.put(DCCAttEnum.CobrarMulta, "1");
                    detailR.put(DCCAttEnum.DataMulta, StringUtilities.formatarCampoData(Calendario.somarDias(item.getDataVencimentoBoleto(), 1), "ddMMyyyy"));
                    detailR.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetario(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica(), 15));
                } else { //Multa por percentual
                    detailR.put(DCCAttEnum.CobrarMulta, "2");
                    detailR.put(DCCAttEnum.DataMulta, StringUtilities.formatarCampoData(Calendario.somarDias(item.getDataVencimentoBoleto(), 1), "ddMMyyyy"));
                    detailR.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetario(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica(), 15));
                }
            } else {
                detailR.put(DCCAttEnum.CobrarMulta, "0");
                detailR.put(DCCAttEnum.DataMulta, StringUtilities.formatarCampoZerado(8));
                detailR.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoZerado(15));
            }

            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(22));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.Banco, StringUtilities.formatarCampoZerado(3));
            detailR.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoZerado(5));
            detailR.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoZerado(12));
            detailR.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(1));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
            seq++;

            RegistroRemessa detailS = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_S);
            detailS.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailS.put(DCCAttEnum.LoteServico, "0001");
            detailS.put(DCCAttEnum.TipoRegistro, "3");
            detailS.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailS.put(DCCAttEnum.Segmento, "S");
            detailS.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1)); //
            detailS.put(DCCAttEnum.CodigoMovimento, "01"); //Cód. Mov.
            detailS.put(DCCAttEnum.TipoImpressao, "3"); //Cód. Mov.
            String instrucao = remessa.getConvenioCobranca().getInstrucoesBoleto().replaceAll("\n|\r\n", " ");
            detailS.put(DCCAttEnum.Mensagem1, StringUtilities.formatarCampoEmBranco(instrucao, 200)); //Mensagem
            detailS.put(DCCAttEnum.UsoBanco, StringUtilities.formatarCampoEmBranco(22)); //CNAB
            seq++;

            listaDetalhe.add(detailP);
            listaDetalhe.add(detailQ);
            listaDetalhe.add(detailR);
            listaDetalhe.add(detailS);
        }

        remessa.setQtdAceito(lista.size());
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }

        remessa.setTrailerRemessaArquivo(getTrailerArquivo(remessa));

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "9999");
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QtdLotes, StringUtilities.formatarCampoForcandoZerosAEsquerda(1, 6));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 2 + 4, 6));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(6));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(205));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setTrailerArquivo(new StringBuilder(remessa.getTrailerRemessaArquivo().toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setHeaderArquivo(new StringBuilder(remessa.getHeaderRemessaArquivo().toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
    }

    public static void validarDesconto(RemessaItemVO item, RegistroRemessa detail) {
        if (item.possuiDesconto() && item.getDataPagamentoAntecipado() != null) {
            Double valor = item.getValorBoleto();
            valor = valor * item.getPorcentagemDescontoBoletoPagAntecipado() / 100;

            detail.put(DCCAttEnum.CodigoDesconto, "1");
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(item.getDataPagamentoAntecipado(), "ddMMyyyy"));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 15));
        } else {
            detail.put(DCCAttEnum.CodigoDesconto, "0");
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(8));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(15));
        }
    }

    private static RegistroRemessa getTrailerArquivo(RemessaVO remessa) {
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);
        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "0001");
        trailer.put(DCCAttEnum.TipoRegistro, "5");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 3 + 2, 6));
        trailer.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(92));
        trailer.put(DCCAttEnum.UsoBanco, StringUtilities.formatarCampoEmBranco(8));
        trailer.put(DCCAttEnum.UsoBanco, StringUtilities.formatarCampoEmBranco(117));
        return trailer;
    }

    private static RegistroRemessa getHeaderLote(RemessaVO remessa, Date dataDeposito) {

        RegistroRemessa registro = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        registro.put(DCCAttEnum.LoteServico, "0001");
        registro.put(DCCAttEnum.TipoRegistro, "1");
        registro.put(DCCAttEnum.TipoOperacao, "R");
        registro.put(DCCAttEnum.TipoServico, "01");
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
        registro.put(DCCAttEnum.VersaoLayout, "040");
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 15));
//        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Integer.valueOf(remessa.getConvenioCobranca().getNumeroContrato()), 9));
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoEmBranco(20));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getRazaoSocial(), 30));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(80));
        registro.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getCodigo(), 8));
        registro.put(DCCAttEnum.DataGravacao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(33));
        return registro;
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            while ((linha = br.readLine()) != null) {
                if(patternHEAD.matcher(linha).find()){
                    lerHeader(linha, h);
                    break;
                }
            }
        }
        return h;
    }

    /**
     * Realiza a leitura do header do lote para a validação do arquivo.
     * @param linha
     * @param registro
     */
    private static void lerHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(8,9,linha));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, StringUtilities.readString(17,18,linha));
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(18,32,linha));
//        registro.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(18,32,linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(53,57,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(59,70,linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(72,102,linha));
        registro.put(DCCAttEnum.NomeBanco, StringUtilities.readString(102,108,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(108,142,linha));
        registro.put(DCCAttEnum.CodigoRemessa, StringUtilities.readString(142,143,linha));
        registro.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(143,151,linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(143,151,linha));
        registro.put(DCCAttEnum.HoraGeracao, StringUtilities.readString(151,155,linha));
        registro.put(DCCAttEnum.IdentificacaoCobranca, StringUtilities.readString(157,211,linha));
        registro.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(211,214,linha));
        registro.put(DCCAttEnum.DensidadeGravacao, StringUtilities.readString(214,219,linha));
    }

    /**
     * Realiza a leitura do registro T do arquivo de retorno do banco.
     * @param linha
     * @param registro
     */
    private static void lerDetailT(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(8,13,linha));
        registro.put(DCCAttEnum.Segmento, StringUtilities.readString(13,14,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(14,15,linha));
        registro.put(DCCAttEnum.StatusVenda, StringUtilities.readString(15,17,linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(17,22,linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(22,23,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(23,35,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(35,36,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(36,37,linha));
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.readString(37,44,linha));
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(37,46,linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(57,58,linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(57,73,linha));
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(73,81,linha));
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(81,96,linha));
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(96,99,linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(99,104,linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(104,105,linha));
        registro.put(DCCAttEnum.Moeda, StringUtilities.readString(130,132,linha));
        registro.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(198,213,linha));
    }

    /**
     * Realiza a leitura do registro U do arquivo de retorno do banco.
     * @param linha
     * @param registro
     */
    private static void lerDetailU(String linha, RegistroRemessa registro){
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(17,32,linha));
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(77,92,linha));
        registro.put(DCCAttEnum.DataEntrada, StringUtilities.readString(137,145, linha));
        registro.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(198,213,linha));
    }


    /**
     * Realiza a leitura do trailer
     * @param linha
     * @param registro
     */
    private static void lerTrailer(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17,23,linha));
    }


    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();

        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            RegistroRemessa detail = null;
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            int contLinha = 0;
            while ((linha = br.readLine()) != null) {
                if ((patternHEAD.matcher(linha).find()) && contLinha == 0) {
                    lerHeader(linha, h);
                    contLinha++;
                } else if (patternT.matcher(linha).find()) {
                    detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_T);
                    lerDetailT(linha, detail);
                    contLinha++;
                } else if (patternU.matcher(linha).find()) {
                    lerDetailU(linha, detail);
                    listaDetalheRetorno.add(detail);
                    contLinha++;
                } else if ((patterTRAILER.matcher(linha).find()) && (!linha.startsWith("9"))) {
                    lerTrailer(linha, t);
                    contLinha++;
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static String obterCodigosRemessaItem(StringBuilder retorno) throws IOException{
        StringBuilder codigos = new StringBuilder();
        BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
        String linha;
        while ((linha = br.readLine()) != null) {
            if(patternT.matcher(linha).find()){
                String nossoNumero = StringUtilities.readString(39, 46, linha);
                nossoNumero = nossoNumero.trim();
                codigos.append(nossoNumero);
                codigos.append(",");
            }
        }

        if (codigos.length() > 0) {
            codigos.deleteCharAt(codigos.length() - 1);
        }

        return codigos.toString();
    }

    public static String calcularDVNossoNumero(String identificador) {
        return new Bancoob(null, true).getDvNossoNumero(identificador);
    }

    public static String getDVNossoNumero(ConvenioCobrancaVO convenio, Integer identificador) {
        String nossoNumeroTemp = convenio.getContaEmpresa().getAgencia()+ String.format("%010d", Integer.valueOf(convenio.getNumeroContrato())) + StringUtilities.formatarCampoForcandoZerosAEsquerda(identificador.toString(), 7);
        return calcularDVNossoNumero(nossoNumeroTemp);
    }

    public static String calcularNossoNumero(RemessaItemVO remessaItemVO) {
        String retorno = "";
//        retorno += "0000050000";
        String nn = StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItemVO.getIdentificador() , 9);
        retorno +=  nn;//num titulo
        retorno += LayoutRemessaSicoobCNAB240.getDVNossoNumero(remessaItemVO.getRemessa().getConvenioCobranca(), remessaItemVO.getIdentificador());
        retorno += "01"; //parcela
        retorno += "01"; //modalidade
        retorno += "4"; //tipo formulario
        retorno += StringUtilities.formatarCampoEmBranco(5); //em branco
        return retorno;
    }
}




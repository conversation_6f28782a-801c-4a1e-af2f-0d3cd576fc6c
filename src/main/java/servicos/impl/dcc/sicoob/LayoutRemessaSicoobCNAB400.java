/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.sicoob;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.jboleto.JBoletoBean;
import org.jboleto.bancos.Bancoob;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import servicos.impl.dcc.bradesco.DCOBradescoOcorrenciaEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaSicoobCNAB400 extends LayoutRemessaBase {


    /**
     * Realiza o preenchimento das informações da linha de header do arquivo de remessa do Sicoob no padrão CNAB400.
     * @return RegistroRemessa do reader.
     */
    public static RegistroRemessa preencherHeaderArquivoRemessa(ConvenioCobrancaVO convenio, Date dataGeracao, Integer codigoRemessa){
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");//7
        header.put(DCCAttEnum.CodigoServico, "01");//2
        header.put(DCCAttEnum.IdentificacaoServico, new StringBuilder(StringUtilities.formatarCampoEmBranco("COBRANÇA", 15)));
        header.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getAgencia(), 4)); // Prefixo cooperativa
        header.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getAgenciaDV(), 1)); // Dígito Prefixo cooperativa
        header.put(DCCAttEnum.CodigoCliente, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getNumeroContrato(), 9));
        //header.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getContaCorrente(), 8)); // Código Cliente/Beneficiário
        //header.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getContaCorrenteDV(), 1)); // Dígito Código Cliente/Beneficiário
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(6));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(convenio.getEmpresa().getNome(), 30)); // Nome do Beneficiário
        header.put(DCCAttEnum.IdentificacaoBanco, StringUtilities.formatarCampoEmBranco("756BANCOOBCED", 18));
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyy"));
        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(codigoRemessa), 7));//sequencial de Remessa
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(287));
        header.put(DCCAttEnum.SequencialRegistro, "000001");//6
        return header;
    }

    /**
     * Realiza a consulta do {@link EnderecoVO} que será utilizado na remessa.
     * @param item
     * @return
     */
    public static EnderecoVO getEndereco(RemessaItemVO item){
        return LayoutRemessaBase.getEndereco(item);
    }

    /**
     * Realiza a criação da linha de detalhe para uma determinada {@link RemessaItemVO} do arquivo de remessa do Sicoob no padrão CNAB400.
     * @param remessaItem {@link RemessaItemVO} que será gerada a linha de detalhe.
     * @param dataRegistro Data de geração do arquivo
     * @param sequencial Sequencial da linha.
     * @return
     */
    public static RegistroRemessa preencherDetalheArquivoRemessa(RemessaItemVO remessaItem, Date dataRegistro, Integer sequencial){
        RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
        ConvenioCobrancaVO convenio = remessaItem.getRemessa().getConvenioCobranca();
        detail.put(DCCAttEnum.TipoRegistro, "1");
        detail.put(DCCAttEnum.IdentificadorClienteEmpresa, "02");
        detail.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(convenio.getEmpresa().getCNPJ()), 14));
        detail.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getAgencia(), 4)); // Prefixo cooperativa
        detail.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getAgenciaDV(), 1)); // Dígito Prefixo cooperativa
        detail.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrente(), 8)); // Código Cliente/Beneficiário
        detail.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getContaCorrenteDV(), 1)); // Dígito Código Cliente/Beneficiário
        detail.put(DCCAttEnum.ConvenioCobranca, "000000");
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(25));
        detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getIdentificador().toString(), 11));
        detail.put(DCCAttEnum.NossoNumeroDV, getDVNossoNumero(convenio, remessaItem.getIdentificador()));
        detail.put(DCCAttEnum.NumeroParcela, "01");
        detail.put(DCCAttEnum.GrupoDeValor, "00");
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(3));
        detail.put(DCCAttEnum.IndicativoMensagem, "A");
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(3));
        detail.put(DCCAttEnum.VariacaoCarteira, "000");
        detail.put(DCCAttEnum.ContaCaucao, "0");
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(5)); /// Numero contrato
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(1)); // Dígito contrato
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(6)); // Número borderô
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(4)); // Complemento registro
        detail.put(DCCAttEnum.TipoEmissao, "2"); // Tipo Emissão
        detail.put(DCCAttEnum.Carteira, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getCarteiraBoleto(),2));
        detail.put(DCCAttEnum.CodigoMovimento, "01");
        detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getCodigo(), 10));
        detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(remessaItem.getDataVencimentoBoleto(), "ddMMyy"));
        detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoMonetario(remessaItem.getValorBoleto(), 13));
        detail.put(DCCAttEnum.Banco, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getBanco().getCodigoBanco(), 3));
        detail.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getAgencia(), 4)); // Prefixo cooperativa
        detail.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getAgenciaDV(), 1)); // Dígito Prefixo cooperativa
        detail.put(DCCAttEnum.EspecieTitulo, "01");
        detail.put(DCCAttEnum.Aceite, "0");
        detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataRegistro, "ddMMyy"));
        detail.put(DCCAttEnum.Instrucao1, "00");
        detail.put(DCCAttEnum.Instrucao2, "00");
        if(convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros()){
            detail.put(DCCAttEnum.MoraMes, StringUtilities.formatarCampoMonetarioVirgula(convenio.getEmpresa().getJurosCobrancaAutomatica(), 2, 4).replace(",",""));
            detail.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetarioVirgula(convenio.getEmpresa().getMultaCobrancaAutomatica(), 2, 4).replace(",",""));
        }else{
            detail.put(DCCAttEnum.MoraMes, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoZerado(6));
        }
        detail.put(DCCAttEnum.TipoDistribuicao, "2");
        if(remessaItem.possuiDesconto() && remessaItem.getDataPagamentoAntecipado() != null){
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(remessaItem.getDataPagamentoAntecipado(), "ddMMyy"));
            Double valor = remessaItem.getValorBoleto();
            valor = valor * remessaItem.getPorcentagemDescontoBoletoPagAntecipado() / 100;
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 13));
        }else{
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));
        }
        detail.put(DCCAttEnum.Moeda, "9");
        detail.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(12));
        detail.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(13));
        detail.put(DCCAttEnum.TipoInscricao, remessaItem.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02");// Tipo inscrição pagador 01 = CPF, 02 = CNPJ
        String cpfCnpj = remessaItem.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(remessaItem) : getCNPJ(remessaItem);
        detail.put(DCCAttEnum.InscricaoPagador, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));
        detail.put(DCCAttEnum.NomeCliente, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getNome(), 40));
        EnderecoVO enderecoVO = getEndereco(remessaItem);
        detail.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 37));
        detail.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 15));
        detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
        detail.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getCidade().getNome(), 15));
        detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getEstadoVO().getSigla(), 2));
        detail.put(DCCAttEnum.NomeCliente, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getNome(), 40));
        detail.put(DCCAttEnum.DiasProtesto, "00");
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1)); // Complemento do registro
        detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(sequencial, 6));
        return detail;
    }

    public static String getDVNossoNumero(ConvenioCobrancaVO convenio, Integer identificador) {
        String nossoNumeroTemp = convenio.getContaEmpresa().getAgencia()+ String.format("%010d", new Object[]{Integer.valueOf(convenio.getNumeroContrato())}) + StringUtilities.formatarCampoForcandoZerosAEsquerda(identificador.toString(), 7);
        return calcularDVNossoNumero(nossoNumeroTemp);
    }

    /**
     * Realiza o preenchimento da linha de trailler do arquivo de remessa do Sicoob no padrão CNAB400.
     * @param convenio
     * @param sequencial
     * @return
     */
    public static RegistroRemessa preencherTraillerArquivoRemessa(Integer sequencial){
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(393));
        trailer.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(sequencial), 6));
        return trailer;
    }

    /**
     * Popula as informações adicionais da remessa que não são informados no retorno do arquivo.
     * @param remessa
     * @param convenio
     */
    private static void popularInformacoesAdicionais(RemessaVO remessa) {
        //Usar o Props pelo fato do Banco não aceitar informações no Trailer
        Map<String, String> infoAdicional = new HashMap<String, String>();
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }
        infoAdicional.put(DCCAttEnum.QuantidadeRegistros.name(), String.valueOf(remessa.getListaItens().size()));
        infoAdicional.put(DCCAttEnum.ValorTotalBruto.name(), StringUtilities.formatarCampoMonetario(valorTotalBruto, 10));
        remessa.setProps(infoAdicional);
    }

    /**
     * Realiza o preenchimento do arquivo do arquivo de remessa do Sicoob no padrão CNAB400.
     * @param remessa
     */
    public static void preencherArquivoRemessa(RemessaVO remessa) {
        Date dataDeposito = remessa.getDataRegistro();
        ConvenioCobrancaVO convenio = remessa.getConvenioCobranca();
        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 2;
        for (RemessaItemVO item : lista) {
            item.setRemessa(remessa);
            RegistroRemessa detail = preencherDetalheArquivoRemessa(item, dataDeposito, seq);
            if (item.getCodigo() != 0) {
                qtdAceito += 1;
            }
            seq++;
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtdAceito);
        remessa.setHeaderRemessa(preencherHeaderArquivoRemessa(convenio, dataDeposito, remessa.getCodigo()));
        remessa.setHead(new StringBuilder(remessa.getHeaderRemessa().toString()));
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
        remessa.setTrailerRemessa(preencherTraillerArquivoRemessa(seq));
        remessa.setTrailer(new StringBuilder(remessa.getTrailerRemessa().toString()));
        popularInformacoesAdicionais(remessa);
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02Retorno")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    /**
     * Realiza a leitura do arquivo de retorno do banco Sicoob no padrão CNAB400.
     * @param remessa
     * @throws IOException
     * @throws ConsistirException
     */
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        switch (r.getTipo()){
            case HEADER:
                lerAtributosHeader(linha, r);
                break;
            case DETALHE:
                lerAtributosDetalhe(linha, r);
                break;
            case TRAILER:
                lerAtributosTrailler(linha, r);
                break;
            default:
                break;
        }
    }

    /**
     * Realiza a leitura do trailler do arquivo de retorno do Sicoob no padrão CNAB400
     * @param linha
     * @param registro
     */
    private static void lerAtributosTrailler(String linha, RegistroRemessa registroRemessa) {
        registroRemessa.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
        registroRemessa.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, 3, linha));//1
        registroRemessa.put(DCCAttEnum.Banco, StringUtilities.readString(3, 6, linha));//1
        registroRemessa.put(DCCAttEnum.DadosCooperativaRemetente, StringUtilities.readString(6, 155, linha));//1
        registroRemessa.put(DCCAttEnum.DataGeracao, StringUtilities.readString(155, 163, linha));//1
        registroRemessa.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(163, 171, linha));//8
        registroRemessa.put(DCCAttEnum.UltimoNossoNumero, StringUtilities.readString(171, 182, linha));//8
        registroRemessa.put(DCCAttEnum.EmBranco, StringUtilities.readString(182, 394, linha));//10
        registroRemessa.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//10
    }

    /**
     * Realiza a leitura do detail do arquivo de retorno do Sicoob no padrão CNAB400
     * @param linha
     * @param registro
     */
    private static void lerAtributosDetalhe(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
        registro.put(DCCAttEnum.TipoPagador, StringUtilities.readString(1, 3, linha));//2
        registro.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(3, 17, linha));//14
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(17, 21, linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(21, 22, linha));
        registro.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(22, 30, linha));
        registro.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(30, 31, linha));
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.readString(31, 37, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(37, 62, linha));//10
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(62, 73, linha));//11
        registro.put(DCCAttEnum.NossoNumeroDV, StringUtilities.readString(73, 74, linha));//1
        registro.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(74, 76, linha));//1
        registro.put(DCCAttEnum.GrupoDeValor, StringUtilities.readString(76, 80, linha));//1
        registro.put(DCCAttEnum.CodigoBaixa, StringUtilities.readString(80, 82, linha));//1
        registro.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(82, 85, linha));//1
        registro.put(DCCAttEnum.VariacaoCarteira, StringUtilities.readString(85, 88, linha));//1
        registro.put(DCCAttEnum.ContaCaucao, StringUtilities.readString(88, 89, linha));//1
        registro.put(DCCAttEnum.CodigoResponsabilidade, StringUtilities.readString(89, 94, linha));//1
        registro.put(DCCAttEnum.CodigoResponsabilidadeDV, StringUtilities.readString(94, 95, linha));//1
        registro.put(DCCAttEnum.TaxaDesconto, StringUtilities.readString(95, 100, linha));//1
        registro.put(DCCAttEnum.IOF, StringUtilities.readString(100, 105, linha));//1
        registro.put(DCCAttEnum.EmBranco2, StringUtilities.readString(105, 106, linha));//10
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(106, 108, linha));//10
        String codigoMovimento = StringUtilities.readString(108, 110, linha);
        registro.put(DCCAttEnum.CodigoMovimento, codigoMovimento);//10
        registro.put(DCCAttEnum.DataEntrada, StringUtilities.readString(110, 116, linha));//10
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(116, 126, linha));//10
        registro.put(DCCAttEnum.EmBranco3, StringUtilities.readString(126, 146, linha));//10
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));//10
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));//10
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(165, 168, linha));//10
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(168, 172, linha));//10
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(172, 173, linha));//10
        registro.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));//10
        registro.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(175, 181, linha));//6
        registro.put(DCCAttEnum.Tarifa, StringUtilities.readString(181, 188, linha));//6
        registro.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(188, 201, linha));//6
        registro.put(DCCAttEnum.JurosDesconto, StringUtilities.readString(201, 214, linha));//6
        registro.put(DCCAttEnum.IOFDesconto, StringUtilities.readString(214, 227, linha));//6
        registro.put(DCCAttEnum.Abatimento, StringUtilities.readString(227, 240, linha));//6
        registro.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));//6
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));//6
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(266, 279, linha));//6
        registro.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(279, 292, linha));//6
        registro.put(DCCAttEnum.Abatimento, StringUtilities.readString(292, 305, linha));//6
        registro.put(DCCAttEnum.ValorLancamento, StringUtilities.readString(305, 318, linha));//6
        registro.put(DCCAttEnum.IndicadorCredDeb, StringUtilities.readString(318, 319, linha));//6
        registro.put(DCCAttEnum.IndicadorValor, StringUtilities.readString(319, 320, linha));//6
        registro.put(DCCAttEnum.ValorAjuste, StringUtilities.readString(320, 332, linha));//6
        registro.put(DCCAttEnum.EmBranco3, StringUtilities.readString(332, 342, linha));//6
        registro.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(342, 357, linha));//6
        registro.put(DCCAttEnum.EmBranco4, StringUtilities.readString(357, 394, linha));//6
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6
        registro.put(DCCAttEnum.StatusVenda, codigoMovimento);
    }

    /**
     * Realiza a leitura do header do arquivo de retorno do Sicoob no padrão CNAB400
     * @param linha
     * @param registro
     */
    private static void lerAtributosHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registro.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));
        registro.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));
        registro.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));
        registro.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(26, 30, linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(30, 31, linha));
        registro.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(31, 39, linha) + StringUtilities.readString(39, 40, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(40, 46, linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));
        registro.put(DCCAttEnum.IdentificacaoBanco, StringUtilities.readString(76, 94, linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));
        registro.put(DCCAttEnum.SequencialRetorno, StringUtilities.readString(100, 107, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(107, 394, linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }


    public static String calcularDVNossoNumero(String identificador) {
        return new Bancoob(null).getDvNossoNumero(identificador);
    }
}

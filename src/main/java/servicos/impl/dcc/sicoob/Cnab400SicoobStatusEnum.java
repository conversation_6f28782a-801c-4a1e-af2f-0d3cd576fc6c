package servicos.impl.dcc.sicoob;


/**
 * Created by <PERSON> on 31/08/2016.
 */
public enum Cnab400SicoobStatusEnum {
    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status02("02", "Confirmação Entrada Título"),
    Status03("03", "Entrada Rejeita"),
    Status05("05", "Liquidação Sem Registro: Identifica a liquidação de título da modalidade 'SEM REGISTRO'"),
    Status06("06", "Liquidação Normal: Identificar a liquidação de título de modalidade 'REGISTRADA', com exceção dos títulos que forem liquidados em cartório (Cód. de movimento 15=Liquidação em Cartório)"),
    Status09("09", "Baixa de Titulo: Identificar as baixas de títulos, com exceção da baixa realizada com o cód. de movimento 10 (Baixa - Pedido Beneficiário)"),
    Status10("10", "Baixa Solicitada (Baixa - Pedido Beneficiário): Ident<PERSON>ar as baixas de títulos comandadas a pedido do Beneficiário"),
    Status11("11", "Títulos em Ser: Identifica os títulos em carteira, que estiverem com a situação 'em abarto' (vencidos e a vencer)"),
    Status14("14","Alteração de Vencimento"),
    Status15("15","Liquidação em Cartório: Identifica as liquidações dos títulos ocorridas em cartórios de protesto"),
    Status23("23","Encaminhado a Protesto: Identifica o recebimento da instrução de protesto"),
    Status27("27","Confirmação Alteração Dados"),
    Status48("48","Confirmação de instrução de transferência de carteira/modalidade de cobrança"),
    Status9999("9999", "Retorno manual");

    private String id;
    private String descricao;

    Cnab400SicoobStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    public static Cnab400SicoobStatusEnum valueOff(String id) {
        Cnab400SicoobStatusEnum[] values = values();
        for (Cnab400SicoobStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return Cnab400SicoobStatusEnum.StatusNENHUM;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bin;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

import static servicos.impl.dcc.base.LayoutRemessaBase.preencherAtributosTransientes;

/**
 * <AUTHOR>
 */
public class LayoutExtratoDiarioBin {
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {

                //000  Header do arquivo
                if (linha.startsWith("000")) {
                    lerAtributos(linha, h);

                    if (remessa.getNomeArquivo().startsWith("PGTOS")) {
                        h.put(DCCAttEnum.OpcaoExtrato, TipoConciliacaoEnum.PAGAMENTOS.getCodigo());
                    } else if (remessa.getNomeArquivo().startsWith("VENDAS")) {
                        h.put(DCCAttEnum.OpcaoExtrato, TipoConciliacaoEnum.VENDAS.getCodigo());
                    }


                    //011  Comprovante de vendas débito
                    //013  Comprovante de vendas - cartão crédito
                    //016  Comprovante de vendas parcelado
                    //021  Comprovante de vendas débito
                    //023  Comprovante de vendas - cartão crédito
                    //026  Comprovante de vendas parcelado
                } else if (linha.startsWith(ExtratoBinTipoRegistroEnum.TipoRegistro011.getId()) ||
                        linha.startsWith(ExtratoBinTipoRegistroEnum.TipoRegistro013.getId()) ||
                        linha.startsWith(ExtratoBinTipoRegistroEnum.TipoRegistro016.getId()) ||
                        linha.startsWith(ExtratoBinTipoRegistroEnum.TipoRegistro021.getId()) ||
                        linha.startsWith(ExtratoBinTipoRegistroEnum.TipoRegistro023.getId()) ||
                        linha.startsWith(ExtratoBinTipoRegistroEnum.TipoRegistro026.getId())) {

                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);

                    //999  Trailer do arquivo
                } else if (linha.startsWith("999")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
            for (ObjetoGenerico objetoGenerico : atributos) {
                preencherAtributosTransientes(remessa, objetoGenerico);
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {

        if (r.getTipo() == TipoRegistroEnum.HEADER) {

            String[] dadosHeader = linha.split(",");

            //  Tipo de Registro Número do Registro 3 Num
            r.put(DCCAttEnum.TipoRegistro, dadosHeader[0]);
            //  Data do Processamento Data de Geração do Arquivo 8 ddmmaaaa
            r.put(DCCAttEnum.DataGeracao, dadosHeader[1]);
            //  Nome do Adquirente First Data Brasil 50 Alpha
            r.put(DCCAttEnum.NomeAdquirente, dadosHeader[2]);
            //  Tipo do Arquivo Movimentação de Vendas 25 Fixo Movimentação de Vendas"
            r.put(DCCAttEnum.TipoArquivo, dadosHeader[3]);
            //  Número do Arquivo Número de Seqüência de Geração do Arquivo  8 Num
            r.put(DCCAttEnum.NumeroArquivo, dadosHeader[4]);
            //  Código do Cliente Código do Cliente (Grupo\Matriz ou Filial) 8 Num
            r.put(DCCAttEnum.NumeroEstabelecimento, dadosHeader[5]);
            //  Nome do Cliente Nome do Cliente (Nome do Grupo\Matriz\Filial) 50 Alpha
            r.put(DCCAttEnum.NomeCliente, dadosHeader[6]);
            //  Tipo de Processamento Processamento \ Reprocessamento  15 (Processamento\Reprocessamento)
            r.put(DCCAttEnum.TipoProcessamento, dadosHeader[7]);
            //  Layout do Arquivo Versão do Arquivo 1 Versão do arquivo
            r.put(DCCAttEnum.VersaoLayout, dadosHeader[8]);
            //  Número de Seqüência do Registro Seqüência do Registro no arquivo  6 Num - inicia em 1 até 999999
            r.put(DCCAttEnum.SequencialRegistro, dadosHeader[9]);

        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {

            //011  Comprovante de vendas débito
            //013  Comprovante de vendas - cartão crédito
            //016  Comprovante de vendas parcelado
            //021  Comprovante de vendas débito
            //023  Comprovante de vendas - cartão crédito
            //026  Comprovante de vendas parcelado

            String[] dadosDetalhe = linha.split(",");

            if (dadosDetalhe[0].equals(ExtratoBinTipoRegistroEnum.TipoRegistro011.getId())) {

                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[2]);
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[3]);
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[4]);
                r.put(DCCAttEnum.TipoVenda, dadosDetalhe[5]);
                r.put(DCCAttEnum.BandeiraCartao, dadosDetalhe[6]);
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[7]);
                r.put(DCCAttEnum.NumeroControleCliente, dadosDetalhe[8]);
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[9]);
                r.put(DCCAttEnum.HoraTransacao, dadosDetalhe[10]);
                r.put(DCCAttEnum.NumeroTerminal, dadosDetalhe[11]);
                r.put(DCCAttEnum.TipoCaptura, dadosDetalhe[12]);
                r.put(DCCAttEnum.EmissorCartao, dadosDetalhe[13]);
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[14]);
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[15]);
                r.put(DCCAttEnum.ValorInterchangePlus, dadosDetalhe[16]);
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[17]);
                r.put(DCCAttEnum.DataPrevistaCredito, dadosDetalhe[18]);
                r.put(DCCAttEnum.NomeOperadoraRecarga, dadosDetalhe[19]);
                r.put(DCCAttEnum.CodigoClienteRecarga, dadosDetalhe[20]);
                r.put(DCCAttEnum.TerminalRecarga, dadosDetalhe[21]);
                r.put(DCCAttEnum.CodigoRecarga, dadosDetalhe[22]);
                r.put(DCCAttEnum.SequencialRegistro, dadosDetalhe[23]);

            } else if (dadosDetalhe[0].equals(ExtratoBinTipoRegistroEnum.TipoRegistro013.getId())) {

                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[2]);
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[3]);
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[4]);
                r.put(DCCAttEnum.BandeiraCartao, dadosDetalhe[5]);
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[6]);
                r.put(DCCAttEnum.NumeroControleCliente, dadosDetalhe[7]);
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[8]);
                r.put(DCCAttEnum.HoraTransacao, dadosDetalhe[9]);
                r.put(DCCAttEnum.NumeroTerminal, dadosDetalhe[10]);
                r.put(DCCAttEnum.TipoCaptura, dadosDetalhe[11]);
                r.put(DCCAttEnum.EmissorCartao, dadosDetalhe[12]);
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[13]);
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[14]);
                r.put(DCCAttEnum.ValorInterchangePlus, dadosDetalhe[15]);
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[16]);
                r.put(DCCAttEnum.DataPrevistaCredito, dadosDetalhe[17]);
                r.put(DCCAttEnum.NomeOperadoraRecarga, dadosDetalhe[18]);
                r.put(DCCAttEnum.CodigoClienteRecarga, dadosDetalhe[19]);
                r.put(DCCAttEnum.TerminalRecarga, dadosDetalhe[20]);
                r.put(DCCAttEnum.CodigoRecarga, dadosDetalhe[21]);
                r.put(DCCAttEnum.SequencialRegistro, dadosDetalhe[22]);

            } else if (dadosDetalhe[0].equals(ExtratoBinTipoRegistroEnum.TipoRegistro016.getId())) {

                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[2]);
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[3]);
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[4]);
                r.put(DCCAttEnum.BandeiraCartao, dadosDetalhe[5]);
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[6]);
                r.put(DCCAttEnum.NumeroControleCliente, dadosDetalhe[7]);
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[8]);
                r.put(DCCAttEnum.HoraTransacao, dadosDetalhe[9]);
                r.put(DCCAttEnum.NumeroTerminal, dadosDetalhe[10]);
                r.put(DCCAttEnum.TipoCaptura, dadosDetalhe[11]);
                r.put(DCCAttEnum.EmissorCartao, dadosDetalhe[12]);
                r.put(DCCAttEnum.QuantidadeParcelas, dadosDetalhe[13]);
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[14]);
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[15]);
                r.put(DCCAttEnum.ValorInterchangePlus, dadosDetalhe[16]);
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[17]);
                r.put(DCCAttEnum.ValorPrimeiraParcela, dadosDetalhe[18]);
                r.put(DCCAttEnum.DataCreditoPrimeiraParcela, dadosDetalhe[19]);
                r.put(DCCAttEnum.SequencialRegistro, dadosDetalhe[20]);

            } else if (dadosDetalhe[0].equals(ExtratoBinTipoRegistroEnum.TipoRegistro021.getId())) {

                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
                r.put(DCCAttEnum.DataPagamento, dadosDetalhe[2]);
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[3]);
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[4]);
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[5]);
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[6]);
                r.put(DCCAttEnum.ValorInterchangePlus, dadosDetalhe[7]);
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[8]);
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[9]);
                r.put(DCCAttEnum.NumeroControleCliente, dadosDetalhe[10]);
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[11]);
                r.put(DCCAttEnum.TipoPagamento, dadosDetalhe[12]);
                r.put(DCCAttEnum.StatusPagamento, dadosDetalhe[13]);
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[14]);
                r.put(DCCAttEnum.NomeOperadoraRecarga, dadosDetalhe[15]);
                r.put(DCCAttEnum.CodigoClienteRecarga, dadosDetalhe[16]);
                r.put(DCCAttEnum.TerminalRecarga, dadosDetalhe[17]);
                r.put(DCCAttEnum.CodigoRecarga, dadosDetalhe[18]);
                r.put(DCCAttEnum.SequencialRegistro, dadosDetalhe[19]);


            } else if (dadosDetalhe[0].equals(ExtratoBinTipoRegistroEnum.TipoRegistro023.getId())) {

                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
                r.put(DCCAttEnum.DataPagamento, dadosDetalhe[2]);
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[3]);
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[4]);
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[5]);
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[6]);
                r.put(DCCAttEnum.ValorInterchangePlus, dadosDetalhe[7]);
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[8]);
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[9]);
                r.put(DCCAttEnum.NumeroControleCliente, dadosDetalhe[10]);
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[11]);
                r.put(DCCAttEnum.TipoPagamento, dadosDetalhe[12]);
                r.put(DCCAttEnum.StatusPagamento, dadosDetalhe[13]);
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[14]);
                r.put(DCCAttEnum.NomeOperadoraRecarga, dadosDetalhe[15]);
                r.put(DCCAttEnum.CodigoClienteRecarga, dadosDetalhe[16]);
                r.put(DCCAttEnum.TerminalRecarga, dadosDetalhe[17]);
                r.put(DCCAttEnum.CodigoRecarga, dadosDetalhe[18]);
                r.put(DCCAttEnum.SequencialRegistro, dadosDetalhe[19]);

            } else if (dadosDetalhe[0].equals(ExtratoBinTipoRegistroEnum.TipoRegistro026.getId())) {

                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
                r.put(DCCAttEnum.DataPagamento, dadosDetalhe[2]);
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[3]);
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[4]);
                r.put(DCCAttEnum.NumeroParcela, dadosDetalhe[5]);
                r.put(DCCAttEnum.QuantidadeParcelas, dadosDetalhe[6]);
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[7]);
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[8]);
                r.put(DCCAttEnum.ValorInterchangePlus, dadosDetalhe[9]);
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[10]);
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[11]);
                r.put(DCCAttEnum.NumeroControleCliente, dadosDetalhe[12]);
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[13]);
                r.put(DCCAttEnum.TipoPagamento, dadosDetalhe[14]);
                r.put(DCCAttEnum.StatusPagamento, dadosDetalhe[15]);
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[16]);
                r.put(DCCAttEnum.ParcelaResumoVenda, dadosDetalhe[17]);
                r.put(DCCAttEnum.SequencialRegistro, dadosDetalhe[18]);

            }

        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {

            String[] dadosTrailer = linha.split(",");

            if (dadosTrailer.length <= 17) {

                r.put(DCCAttEnum.TipoRegistro, dadosTrailer[0]);
                r.put(DCCAttEnum.DataGeracao, dadosTrailer[1]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosTrailer[2]);
                r.put(DCCAttEnum.QuantidadeMatriz, dadosTrailer[3]);
                r.put(DCCAttEnum.QuantidadeVendasDebito, dadosTrailer[4]);
                r.put(DCCAttEnum.ValorBrutoVendasDebito, dadosTrailer[5]);
                r.put(DCCAttEnum.TotalTaxasVendasDebito, dadosTrailer[6]);
                r.put(DCCAttEnum.ValorLiquidoVendasDebito, dadosTrailer[7]);
                r.put(DCCAttEnum.QuantidadeVendasCredito, dadosTrailer[8]);
                r.put(DCCAttEnum.ValorBrutoVendasCredito, dadosTrailer[9]);
                r.put(DCCAttEnum.TotalTaxasVendasCredito, dadosTrailer[10]);
                r.put(DCCAttEnum.ValorLiquidoVendasCredito, dadosTrailer[11]);
                r.put(DCCAttEnum.QuantidadeVendasCreditoParcelado, dadosTrailer[12]);
                r.put(DCCAttEnum.ValorBrutoVendasCreditoParcelado, dadosTrailer[13]);
                r.put(DCCAttEnum.TotalTaxasVendasCreditoParcelado, dadosTrailer[14]);
                r.put(DCCAttEnum.ValorLiquidoVendasCreditoParcelado, dadosTrailer[15]);
                r.put(DCCAttEnum.QuantidadeRegistros, dadosTrailer[16]);

            } else {

                r.put(DCCAttEnum.TipoRegistro, dadosTrailer[0]);
                r.put(DCCAttEnum.DataGeracao, dadosTrailer[1]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosTrailer[2]);
                r.put(DCCAttEnum.QuantidadeMatriz, dadosTrailer[3]);
                r.put(DCCAttEnum.QuantidadeVendasDebito, dadosTrailer[4]);
                r.put(DCCAttEnum.ValorBrutoVendasDebito, dadosTrailer[5]);
                r.put(DCCAttEnum.TotalTaxasVendasDebito, dadosTrailer[6]);
                r.put(DCCAttEnum.ValorLiquidoVendasDebito, dadosTrailer[7]);
                r.put(DCCAttEnum.QuantidadeVendasCredito, dadosTrailer[8]);
                r.put(DCCAttEnum.ValorBrutoVendasCredito, dadosTrailer[9]);
                r.put(DCCAttEnum.TotalTaxasVendasCredito, dadosTrailer[10]);
                r.put(DCCAttEnum.ValorLiquidoVendasCredito, dadosTrailer[11]);
                r.put(DCCAttEnum.QuantidadeVendasCreditoParcelado, dadosTrailer[12]);
                r.put(DCCAttEnum.ValorBrutoVendasCreditoParcelado, dadosTrailer[13]);
                r.put(DCCAttEnum.TotalTaxasVendasCreditoParcelado, dadosTrailer[14]);
                r.put(DCCAttEnum.ValorLiquidoVendasCreditoParcelado, dadosTrailer[15]);
                r.put(DCCAttEnum.QuantidadeRegistros, dadosTrailer[16]);

            }
        }
    }
}

package servicos.impl.dcc.bin;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;

/**
 * Created by <PERSON><PERSON> on 25/07/2017.
 */
public enum ExtratoBinTipoRegistroEnum {

    TipoRegistro021("021", "Comprovante de vendas débito", TipoConciliacaoEnum.VENDAS),
    TipoRegistro023("023", "Comprovante de vendas - cartão crédito", TipoConciliacaoEnum.VENDAS),
    TipoRegistro026("026", "Comprovante de vendas parcelado", TipoConciliacaoEnum.VENDAS),

    TipoRegistro011("011", "Comprovante de vendas débito ", TipoConciliacaoEnum.PAGAMENTOS),
    TipoRegistro013("013", "Comprovante de vendas - cartão crédito", TipoConciliacaoEnum.PAGAMENTOS),
    TipoRegistro016("016", "Comprovante de vendas parcelado", TipoConciliacaoEnum.PAGAMENTOS);

    private String id;
    private String descricao;
    private TipoConciliacaoEnum tipoConciliacaoEnum;

    private ExtratoBinTipoRegistroEnum(String id, String descricao, TipoConciliacaoEnum tipoConciliacaoEnum) {
        this.id = id;
        this.descricao = descricao;
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoConciliacaoEnum getTipoConciliacaoEnum() {
        return tipoConciliacaoEnum;
    }

    public void setTipoConciliacaoEnum(TipoConciliacaoEnum tipoConciliacaoEnum) {
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
    }

    public static ExtratoBinTipoRegistroEnum valueOff(String id) {
        ExtratoBinTipoRegistroEnum[] values = ExtratoBinTipoRegistroEnum.values();
        for (ExtratoBinTipoRegistroEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public static boolean extratoBIN(String id) {
        ExtratoBinTipoRegistroEnum[] values = ExtratoBinTipoRegistroEnum.values();
        for (ExtratoBinTipoRegistroEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }


    public static String retornarIDs(){
        StringBuilder codigos = new StringBuilder();
        for (ExtratoBinTipoRegistroEnum eDIStatusEnum : values()) {
            if (codigos.toString().equals("")){
                codigos.append("'").append(eDIStatusEnum.getId()).append("'");
            }else{
                codigos.append(",").append("'").append(eDIStatusEnum.getId()).append("'");
            }
        }
        return codigos.toString();
    }
}

package servicos.impl.dcc.bin;

/**
 * Created by <PERSON> on 21/06/2016.
 */
/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LayoutRemessaBinDCC extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "01");
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito,"yyyyMMdd"));
        header.put(DCCAttEnum.DataVenda, StringUtilities.formatarCampoData(Calendario.hoje(),"yyyyMMdd"));
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato()), 10));
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getCodigo(), MathContext.UNLIMITED), 20));
        header.put(DCCAttEnum.IndicadorProcesso, "P");
        header.put(DCCAttEnum.CodigoRetorno, StringUtilities.formatarCampoEmBranco(2));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(149));


        boolean remessaAgrupada = remessa.isNovoFormato();

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(DCCAttEnum.TipoRegistro, "02");
            detail.put(DCCAttEnum.NumeroCartao, StringUtilities.formatarCampo(new BigDecimal(item.getNazgDTO().getCard().trim(), MathContext.UNLIMITED), 20));
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 12));

            detail.put(DCCAttEnum.NumeroReferencia, StringUtilities.formatarCampoZerado(16));
            detail.put(DCCAttEnum.IndicacaoEspecial, "0");

            detail.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.formatarCampoEmBranco(6));

            String mes = Formatador.formatarValorNumerico((double) item.getNazgDTO().getMonth(), "00");
            String ano = String.valueOf(item.getNazgDTO().getYear());
            String validade = mes + ano.substring(ano.length() - 2 , ano.length());

            detail.put(DCCAttEnum.ValidadeCartao, validade);

            detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoEmBranco(2));

            detail.put(DCCAttEnum.CodigoCorrente, "986");

            detail.put(DCCAttEnum.CVV2, StringUtilities.formatarCampoZerado(3));
            //Não possui valor
            detail.put(DCCAttEnum.CVV2, StringUtilities.formatarCampoEmBranco(3));
            detail.put(DCCAttEnum.TipoTransacao,"3");
            detail.put(DCCAttEnum.TipoVenda,"00");
            detail.put(DCCAttEnum.TipoEstorno,"0");

            detail.put(DCCAttEnum.DataTransacao,StringUtilities.formatarCampoData(Calendario.hoje(),"yyyyMMdd"));
            detail.put(DCCAttEnum.ValorEstorno,StringUtilities.formatarCampoZerado(12));

            if (remessaAgrupada) {
                detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 15));
            } else {
                detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getCodigo()), 15));
            }

            detail.put(DCCAttEnum.SolicitacaoEstorno, StringUtilities.formatarCampoZerado(15));

            detail.put(DCCAttEnum.NumeroTerminal, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroLogico(),8));

            detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(66));

            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "03");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size()), 8));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 14));
        trailer.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(176));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("01")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    public static void lerRetorno(RemessaVO remessa, boolean validar) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("01")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("02")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("03")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            if (validar) {
                LayoutRemessaBase.validarArquivoRemessaRetorno(remessa);
            }

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (linha.length() != 200) {
            return;
        }
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));//ok
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(2, 10, linha));
            r.put(DCCAttEnum.DataVenda, StringUtilities.readString(10, 18, linha));
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(18, 28, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(28, 48, linha));

            r.put(DCCAttEnum.IndicadorProcesso, StringUtilities.readString(48, 49, linha));//Reais
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(49, 51, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(52, 200, linha));

        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(2, 22, linha));
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(22, 34, linha));
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(34, 50, linha));
                r.put(DCCAttEnum.IndicacaoEspecial, StringUtilities.readString(50, 51, linha));

                r.put(DCCAttEnum.CodigoAutorizacao,
                        StringUtilities.readString(51,57, linha));
                r.put(DCCAttEnum.ValidadeCartao,
                        StringUtilities.readString(57, 61, linha));
//
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(61, 63, linha));

                r.put(DCCAttEnum.CodigoCorrente, StringUtilities.readString(63, 66, linha));
                r.put(DCCAttEnum.CVV2, StringUtilities.readString(66, 69, linha));
                r.put(DCCAttEnum.Zeros2, StringUtilities.readString(69, 72, linha));

                r.put(DCCAttEnum.TipoTransacao, StringUtilities.readString(72, 73, linha));
                r.put(DCCAttEnum.TipoVenda, StringUtilities.readString(73, 75, linha));
                r.put(DCCAttEnum.TipoEstorno, StringUtilities.readString(75, 76, linha));
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(76, 84, linha));
                r.put(DCCAttEnum.ValorEstorno, StringUtilities.readString(84, 96, linha));
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(96, 111, linha));
                r.put(DCCAttEnum.SolicitacaoEstorno, StringUtilities.readString(111, 126, linha));
                r.put(DCCAttEnum.NumeroTerminal, StringUtilities.readString(126, 134, linha));
                r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(134, 200, linha));

        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(2, 10, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(10, 24, linha));
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(24, 200, linha));

        }
    }
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("01")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("02")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("03")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
            for (ObjetoGenerico objetoGenerico : atributos) {
                preencherAtributosTransientes(remessa, objetoGenerico);
            }
        }
    }
//    public static void preencherArquivoRemessaCancelamento(RemessaVO remessa) {
//        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
//
//        header.put(DCCAttEnum.TipoRegistro, "00");
//        header.put(DCCAttEnum.DataDeposito, StringUtilities.formatarCampoData(remessa.getDataRegistro()));
//        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 7));
//        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 10));
//        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));
//        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
//        header.put(DCCAttEnum.Moeda, "986");
//        header.put(DCCAttEnum.IndicadorProcesso, "P");
//        header.put(DCCAttEnum.IndicadorVenda, "C");
//        header.put(DCCAttEnum.IndicacaoEspecial, StringUtilities.formatarCampoEmBranco(1));
//        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(204));
//
//        List<RemessaCancelamentoItemVO> lista = remessa.getListaItensCancelamento();
//        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
//        double soma = 0;
//        int qtd = 0;
//        for (RemessaCancelamentoItemVO cancelamentoItemVO : lista) {
//            RemessaItemVO item = cancelamentoItemVO.getItemRemessaCancelar();
//            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
//
//            String props = item.getProps().toString();
//            String autorizacao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.CodigoAutorizacao.name(), props);
//            String nrcartao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.NumeroCartao.name(), props);
//            Double valorVenda = item.getMovParcela().getValorParcela();
//            Date dataVenda = remessa.getDataRegistro();
//            Integer comprovanteVenda = item.getMovParcela().getCodigo();
//            String mesValidade = LayoutRemessaBase.obterValorCampoProps("MesValidade", props);
//            String anoValidade = LayoutRemessaBase.obterValorCampoProps("AnoValidade", props);
//            String validadeCartao = mesValidade + anoValidade.substring(2);
//            Integer codRemessa = remessa.getCodigo();
//            //
//            if (UteisValidacao.emptyString(autorizacao)) {
//                continue;
//            }
//
//            detail.put(DCCAttEnum.TipoRegistro, "01");
//            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(comprovanteVenda), 7));
//            detail.put(DCCAttEnum.NumeroCartao, StringUtilities.formatarCampo(new BigDecimal(APF.decifrar(nrcartao)), 19));
//            detail.put(DCCAttEnum.CodigoAutorizacao, autorizacao);
//            detail.put(DCCAttEnum.DataVenda, StringUtilities.formatarCampoData(dataVenda));
//            detail.put(DCCAttEnum.OpcaoVenda, "0");
//            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(valorVenda, 15));
//            detail.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.formatarCampoZerado(3));
//            detail.put(DCCAttEnum.ValorFinanciado, StringUtilities.formatarCampoZerado(15));
//            detail.put(DCCAttEnum.ValorEntrada, StringUtilities.formatarCampoZerado(15));
//            detail.put(DCCAttEnum.ValorEmbarque, StringUtilities.formatarCampoZerado(15));
//            detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoZerado(15));
//            detail.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 7));
//            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));
//            detail.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
//            detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(30));
//            detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoEmBranco(2));
//            detail.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));
//            detail.put(DCCAttEnum.ValidadeCartao, validadeCartao);
//            detail.put(DCCAttEnum.NumeroResumoOperacoesOriginal, StringUtilities.formatarCampoForcandoZerosAEsquerda(codRemessa, 7));
//            detail.put(DCCAttEnum.ValorReembolso, StringUtilities.formatarCampoMonetario(valorVenda, 15));
//            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(3));
//            detail.put(DCCAttEnum.CodigoErro, StringUtilities.formatarCampoEmBranco(4));
//            detail.put(DCCAttEnum.NumeroReferencia, StringUtilities.formatarCampoEmBranco(11));
//            detail.put(DCCAttEnum.CartaoNovo, StringUtilities.formatarCampoEmBranco(19));
//            detail.put(DCCAttEnum.DataVencimentoNovo, StringUtilities.formatarCampoEmBranco(4));
//            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(2));
//
//
//            soma += item.getMovParcela().getValorParcela();
//            String statusVendaCancelamento = cancelamentoItemVO.get(DCCAttEnum.StatusVenda.name());
//            if (statusVendaCancelamento != null && statusVendaCancelamento.equals(DCCCieloStatusEnum.Status00.getId())) {
//                qtd += 1;
//            }
//            //
//            listaDetalhe.add(detail);
//        }
//        remessa.setQtdAceito(qtd);
//
//        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
//        trailer.put(DCCAttEnum.TipoRegistro, "99");
//        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 7));
//        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 15));
//        trailer.put(DCCAttEnum.ValorTotalAceito, StringUtilities.formatarCampoZerado(15));
//        trailer.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.formatarCampoZerado(15));
//        trailer.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));
//        trailer.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(188));
//
//        remessa.setTrailerRemessa(trailer);
//        remessa.setDetailsRemessa(listaDetalhe);
//        remessa.setHeaderRemessa(header);
//
//        remessa.setTrailer(new StringBuilder(trailer.toString()));
//        remessa.setHead(new StringBuilder(header.toString()));
//        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
//    }
}

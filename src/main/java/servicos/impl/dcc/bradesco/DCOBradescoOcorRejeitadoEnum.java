/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bradesco;

/**
 *
 * <AUTHOR>
 */
public enum DCOBradescoOcorRejeitadoEnum {

    MotivoNENHUM("NENHUM", "Nenhum Motivo"),
    Motivo02("02", "Código do registro detalhe inválido"),
    Motivo03("03", "Código da ocorrência inválida"),
    <PERSON><PERSON><PERSON>04("04", "Código de ocorrência não permitida para a carteira"),
    Motivo05("05", "Código de ocorrência não numérico"),
    Motivo07("07", "Agência/conta/Dígito - Inválido"),
    Motivo08("08", "Nosso número inválido"),
    <PERSON><PERSON>vo09("09", "Nosso número duplicado"),
    Motivo10("10", "Carteira inválida"),
    Motivo13("13", "Identificação da emissão do bloqueto inválida (Novo)"),
    <PERSON>ti<PERSON><PERSON>("16", "Data de vencimento inválida"),
    Motivo18("18", "Vencimento fora do prazo de operação"),
    Motivo20("20", "Valor do Título inválido"),
    Motivo21("21", "Espécie do Título inválida"),
    Motivo22("22", "Espécie não permitida para a carteira"),
    Motivo24("24", "Data de emissão inválida"),
    Motivo28("28", "Código do desconto inválido (Novo)"),
    Motivo38("38", "Prazo para protesto inválido"),
    Motivo44("44", "Agência Beneficiário não prevista"),
    Motivo45("45", "Nome do pagador não informado (Novo)"),
    Motivo46("46", "Tipo/número de inscrição do pagador inválidos (Novo)"),
    Motivo47("47", "Endereço do pagador não informado (Novo)"),
    Motivo48("48", "CEP Inválido (Novo)"),
    Motivo50("50", "CEP irregular - Banco Correspondente"),
    Motivo63("63", "Entrada para Título já cadastrado"),
    Motivo65("65", "Limite excedido (Novo)"),
    Motivo66("66", "Número autorização inexistente (Novo)"),
    Motivo68("68", "Débito não agendado - erro nos dados de remessa"),
    Motivo69("69", "Débito não agendado - Pagador não consta no cadastro de autorizante"),
    Motivo70("70", "Débito não agendado - Beneficiario não autorizado pelo Pagador"),
    Motivo71("71", "Débito não agendado - Beneficiario não participa do débito Automático"),
    Motivo72("72", "Débito não agendado - Código de moeda diferente de R$"),
    Motivo73("73", "Débito não agendado - Data de vencimento inválida"),
    Motivo74("74", "Débito não agendado - Conforme seu pedido, Título não registrado"),
    Motivo75("75", "Débito não agendado - Tipo de número de inscrição do debitado inválido");
    //
    private String id;
    private String descricao;

    private DCOBradescoOcorRejeitadoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOBradescoOcorRejeitadoEnum valueOff(String id) {
        DCOBradescoOcorRejeitadoEnum[] values = DCOBradescoOcorRejeitadoEnum.values();
        for (DCOBradescoOcorRejeitadoEnum eDIMotivoEnum : values) {
            if (eDIMotivoEnum.getId().equals(id)) {
                return eDIMotivoEnum;
            }
        }
        return DCOBradescoOcorRejeitadoEnum.MotivoNENHUM;
    }
}

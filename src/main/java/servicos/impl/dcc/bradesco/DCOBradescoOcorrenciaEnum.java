/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bradesco;

/**
 *
 * <AUTHOR>
 */
public enum DCOBradescoOcorrenciaEnum {

    OcorNENHUM("NENHUM", "Nenhum Status"),
    Ocor02("02", "Entrada Confirmada"),
    Ocor03("03", "Entrada Rejeitada"),
    Ocor06("06", "Liquidação normal (sem motivo)"),
    Ocor09("09", "Baixado Automat. via Arquivo (verificar motivo posição 319 a 328)"),
    Ocor10("10", "Baixado conforme instruções da Agência(verificar motivo pos.319 a 328)"),
    Ocor11("11", "Em Ser - Arquivo de Títulos pendentes (sem motivo)"),
    Ocor12("12", "Abatimento Concedido (sem motivo)"),
    Ocor13("13", "Abatimento Cancelado (sem motivo)"),
    Ocor14("14", "Vencimento Alterado (sem motivo)"),
    Ocor15("15", "Liquidação em Cartório (sem motivo)"),
    Ocor16("16", "Título Pago em Cheque ? Vinculado"),
    Ocor17("17", "Liquidação após baixa ou Título não registrado (sem motivo)"),
    Ocor18("18", "Acerto de Depositária (sem motivo)"),
    Ocor19("19", "Confirmação Receb. Inst. de Protesto (verificar motivo pos.295 a 295)"),
    Ocor20("20", "Confirmação Recebimento Instrução Sustação de Protesto (sem motivo)"),
    Ocor21("21", "Acerto do Controle do Participante (sem motivo)"),
    Ocor22("22", "Título Com Pagamento Cancelado"),
    Ocor23("23", "Entrada do Título em Cartório (sem motivo)"),
    Ocor24("24", "Entrada rejeitada por CEP Irregular (verificar motivo pos.319 a 328)"),
    Ocor25("25", "Confirmação Receb.Inst.de Protesto Falimentar (verificar pos.295 a 295)"),
    Ocor27("27", "Baixa Rejeitada (verificar motivo posição 319 a 328)"),
    Ocor28("28", "Débito de tarifas/custas (verificar motivo na posição 319 a 328)"),
    Ocor29("29", "Ocorrências do Pagador (NOVO)"),
    Ocor30("30", "Alteração de Outros Dados Rejeitados (verificar motivo pos.319 a 328)"),
    Ocor32("32", "Instrução Rejeitada (verificar motivo posição 319 a 328)"),
    Ocor33("33", "Confirmação Pedido Alteração Outros Dados (sem motivo)"),
    Ocor34("34", "Retirado de Cartório e Manutenção Carteira (sem motivo)"),
    Ocor35("35", "Desagendamento do débito automático (verificar motivos pos. 319 a 328)"),
    Ocor40("40", "Estorno de pagamento(Novo)"),
    Ocor55("55", "Sustado judicial(Novo)"),
    Ocor68("68", "Acerto dos dados do rateio de Crédito (verificar motivo posição de Ocor do registro tipo 3)"),
    Ocor69("69", "Cancelamento dos dados do rateio (verificar motivo posição de Ocor do registro tipo 3)");
    //
    private String id;
    private String descricao;

    private DCOBradescoOcorrenciaEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOBradescoOcorrenciaEnum valueOff(String id) {
        DCOBradescoOcorrenciaEnum[] values = DCOBradescoOcorrenciaEnum.values();
        for (DCOBradescoOcorrenciaEnum eDIOcorEnum : values) {
            if (eDIOcorEnum.getId().equals(id)) {
                return eDIOcorEnum;
            }
        }
        return DCOBradescoOcorrenciaEnum.OcorNENHUM;
    }
}

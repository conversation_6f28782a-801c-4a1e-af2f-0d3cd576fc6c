/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bradesco;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaBradescoCNAB400 extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");//7
        header.put(DCCAttEnum.CodigoServico, "01");//2
        header.put(DCCAttEnum.IdentificacaoServico,
                StringUtilities.formatarCampoEmBranco("COBRANCA", 15));

        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(),
                MathContext.UNLIMITED), 20));

        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(
                remessa.getConvenioCobranca().getEmpresa().getNome(), 30));

        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(),
                MathContext.UNLIMITED), 3));

        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco(
                "BRADESCO", 15));

        header.put(DCCAttEnum.DataGeracao,
                StringUtilities.formatarCampoData(dataDeposito, "ddMMyy"));//6

        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));

        header.put(DCCAttEnum.IdentificacaoSistema, "MX");//2

        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getSequencialArquivo()), 7));//sequencial de Remessa

        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(277));

        header.put(DCCAttEnum.SequencialRegistro, "000001");//6


        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 2;
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(DCCAttEnum.TipoRegistro, "1");

            detail.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoEmBranco(5));

            detail.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.formatarCampoEmBranco(1));

            detail.put(DCCAttEnum.IdentificadorClienteEmpresa, StringUtilities.formatarCampoEmBranco(5));

            detail.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoEmBranco(7));

            detail.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoEmBranco(1));

            String carteira = StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 3);
            detail.put(DCCAttEnum.IdentificadorEmpresaBeneficiaria, "0"//1
                    + carteira//3
                    + StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getConvenioCobranca().getContaEmpresa().getAgencia()), 5)
                    + StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente()), 7)
                    + StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV()), 1));

            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 25));

            detail.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoZerado(3));
//            detail.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco()), 3));
            if (remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros()) {
                detail.put(DCCAttEnum.CobrarMulta, "2");//0 -> sem multa 2 -> considerar percentual de multa
                detail.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetario(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica(), 4, 2));
            } else {
                detail.put(DCCAttEnum.CobrarMulta, "0");//0 -> sem multa 2 -> considerar percentual de multa
                detail.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoZerado(4));
            }

            String nossoNumero = StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 5) + StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 6);
            if (remessa.getTipo().equals(TipoRemessaEnum.BOLETO)) {
                nossoNumero = StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 11);
            }
            detail.put(DCCAttEnum.NossoNumero, nossoNumero);//11

            String carteiraParaDV = StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 2);
            if (remessa.getTipo().equals(TipoRemessaEnum.BOLETO)) {
                detail.put(DCCAttEnum.NossoNumeroDV, novoCalcularDV(carteiraParaDV + nossoNumero));//1
            } else {
                detail.put(DCCAttEnum.NossoNumeroDV, calcularDV(carteiraParaDV + nossoNumero));//1
            }

            detail.put(DCCAttEnum.DescontoDia, StringUtilities.formatarCampoZerado(10));
            detail.put(DCCAttEnum.CondicaoEmissaoBoleto, "2");//1
            detail.put(DCCAttEnum.CondicaoRegistroDebitoAutomatico, "N");//1
            detail.put(DCCAttEnum.IdentificacaoBanco, StringUtilities.formatarCampoEmBranco(10));
            detail.put(DCCAttEnum.RateioCredito, " ");//1
            detail.put(DCCAttEnum.EnderecamentoAvisoDebitoAutomatico, "2");//1
            detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
            detail.put(DCCAttEnum.IdentificacaoOcorrencia, "01");//2

            detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampo(
                    new BigDecimal(item.getCodigo()), 10));

            if (remessa.getTipo().equals(TipoRemessaEnum.BOLETO)) {
                detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyy"));//6
            } else {
                detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(Calendario.proximoDiaUtil(dataDeposito, 5), "ddMMyy"));//6
            }

            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 13));

            detail.put(DCCAttEnum.BancoCobranca, StringUtilities.formatarCampoZerado(3));
            detail.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoZerado(5));
            detail.put(DCCAttEnum.EspecieTitulo, "01");//2
            detail.put(DCCAttEnum.Identificacao, "N");//1

            detail.put(DCCAttEnum.DataDeposito, StringUtilities.formatarCampoData(dataDeposito, "ddMMyy"));//6
            if (!remessa.getConvenioCobranca().isPermitirReceberBoletoAposVencimento()){
                detail.put(DCCAttEnum.Instrucao1, "09"); //1ª e 2ª instrução
            } else {
                detail.put(DCCAttEnum.Instrucao1, StringUtilities.formatarCampoZerado(2)); //1ª instrução
            }
            detail.put(DCCAttEnum.Protesto, StringUtilities.formatarCampoZerado(2)); //2ª instrução

            if (remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros()) {
                if (remessa.getConvenioCobranca().getEmpresa().isUtilizarJurosValorAbsoluto()) {
                    Double valorJuros = remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica(); // NO BRADESCO ESTÁ DISPONÍVEL APENAS EM VALOR ABSOLUTO, NÃO TEM PERCENTUAL
                    detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoMonetario(valorJuros, 13, 2));
                } else {
                    Double valorJuros = item.getValorBoleto() * (remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica() / 100);
                    detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoMonetario(valorJuros, 13, 2));
                }
            } else {
                detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoZerado(13));
            }
            item.setPorcentagemDescontoBoleto(remessa.getConvenioCobranca().getDescontoBoleto());
            validarDesconto(item, detail);
            detail.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.ValorAbatimento, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.TipoPagador, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detail.put(DCCAttEnum.InscricaoPagador, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 14));

            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    enderecoVO = endereco;
                }
            }
            StringBuilder endereco = new StringBuilder();
            endereco.append(Uteis.removerCaracteresNaoAscii(enderecoVO.getEndereco()));
            if (!UteisValidacao.emptyString(enderecoVO.getNumero())) {
                endereco.append(", ").append(enderecoVO.getNumero());
            }
            if (!UteisValidacao.emptyString(enderecoVO.getComplemento())) {
                endereco.append(", ").append(Uteis.removerCaracteresNaoAscii(enderecoVO.getComplemento()));
            }

            detail.put(DCCAttEnum.NomePessoa, getNome(item, 40));
            detail.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(endereco.toString(), 40));
            detail.put(DCCAttEnum.Mensagem1, StringUtilities.formatarCampoEmBranco(12));
            detail.put(DCCAttEnum.CEP, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()),8));
            detail.put(DCCAttEnum.Mensagem2, StringUtilities.formatarCampoEmBranco("COBRANCA ACADEMIA", 60));
            detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

            if (item.getCodigo() != 0) {
                qtdAceito += 1;
            }
            seq++;
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtdAceito);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(393));
        trailer.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

        //Usar o Props pelo fato do Banco não aceitar informações no Trailer
        Map<String, String> infoAdicional = remessa.getProps();
        if (infoAdicional == null) {
            infoAdicional = new HashMap<String, String>();
        }
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            valorTotalBruto += boleto.getValorItemRemessa();
        }

        infoAdicional.put(DCCAttEnum.QuantidadeRegistros.name(), String.valueOf(remessa.getListaItens().size()));
        infoAdicional.put(DCCAttEnum.ValorTotalBruto.name(), StringUtilities.formatarCampoMonetario(valorTotalBruto, 10));

        String sequencialArquivo = infoAdicional.get(DCCAttEnum.NumeroResumoOperacoes.name());
        if (sequencialArquivo == null) {
            sequencialArquivo = StringUtilities.formatarCampo(new BigDecimal(remessa.getSequencialArquivo()), 7);
        }
        infoAdicional.put(DCCAttEnum.NumeroResumoOperacoes.name(), sequencialArquivo);//sequencial de Remessa
        remessa.setProps(infoAdicional);
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02Retorno")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02Retorno")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

            validarArquivoRemessaRetorno(remessa);

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));//1
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));//7
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));//2
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));//15
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(26, 46, linha));//20
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));//30
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));//3
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 94, linha));//15
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));//6
            r.put(DCCAttEnum.DensidadeGravacao, StringUtilities.readString(100, 108, linha));//8
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(108, 113, linha));//5
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(113, 379, linha));//266
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(379, 385, linha));//6
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(385, 394, linha));//9
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 400) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
                r.put(DCCAttEnum.TipoPagador, StringUtilities.readString(1, 3, linha));//2
                r.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(3, 17, linha));//14
                r.put(DCCAttEnum.Zeros, StringUtilities.readString(17, 20, linha));//3
                r.put(DCCAttEnum.IdentificadorEmpresaBeneficiaria, StringUtilities.readString(20, 37, linha));//17
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(37, 62, linha));//25
                r.put(DCCAttEnum.Zeros2, StringUtilities.readString(62, 70, linha));//8
                r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(70, 81, linha));//11
                r.put(DCCAttEnum.NossoNumeroDV, StringUtilities.readString(81, linha));//1
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(83, 92, linha));//10
                r.put(DCCAttEnum.ReservadoBanco2, StringUtilities.readString(92, 104, linha));//12
                r.put(DCCAttEnum.RateioCredito, StringUtilities.readString(104, linha));//1
                r.put(DCCAttEnum.Zeros3, StringUtilities.readString(105, 107, linha));//2
                r.put(DCCAttEnum.Carteira, StringUtilities.readString(107, linha));//1
                String idOcorrencia = StringUtilities.readString(108, 110, linha);//2
                r.put(DCCAttEnum.IdentificacaoOcorrencia, idOcorrencia);
                r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(110, 116, linha));//6
                r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(116, 126, linha));//10
                r.put(DCCAttEnum.IdentificacaoTituloBanco, StringUtilities.readString(126, 146, linha));//20
                r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));//6
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));//13
                r.put(DCCAttEnum.BancoCobranca, StringUtilities.readString(165, 168, linha));//3
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(168, 173, linha));//5
                r.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));//2
                r.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(175, 188, linha));//13
                r.put(DCCAttEnum.DespesaOutras, StringUtilities.readString(188, 201, linha));//13
                r.put(DCCAttEnum.Juros, StringUtilities.readString(201, 214, linha));//13
                r.put(DCCAttEnum.IOF, StringUtilities.readString(214, 227, linha));//13
                r.put(DCCAttEnum.Abatimento, StringUtilities.readString(227, 240, linha));//13
                r.put(DCCAttEnum.DescontoDia, StringUtilities.readString(240, 253, linha));//13
                r.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));//13
                r.put(DCCAttEnum.MoraDia, StringUtilities.readString(266, 279, linha));//13
                r.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(279, 292, linha));//13
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(292, 294, linha));//2
                r.put(DCCAttEnum.MotivoProtesto, StringUtilities.readString(294, linha));//1
                r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(295, 301, linha));//6
                r.put(DCCAttEnum.OrigemPagamento, StringUtilities.readString(301, 304, linha));//3
                r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(304, 314, linha));//10
                r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(315, 318, linha));//4
                r.put(DCCAttEnum.Motivo, StringUtilities.readString(318, 328, linha));//10
                r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(328, 368, linha));//40
                r.put(DCCAttEnum.NumeroCartorio, StringUtilities.readString(368, 370, linha));//2
                r.put(DCCAttEnum.NumeroProtocolo, StringUtilities.readString(370, 380, linha));//10
                r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(380, 394, linha));//14
                r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6

                r.put(DCCAttEnum.StatusVenda, idOcorrencia);
                String motivo = r.get(DCCAttEnum.Motivo.name());
                //Se o Débito foi efetuado, usar como Código de Autorização o "Nosso Número" retornado
                if (idOcorrencia != null && !idOcorrencia.isEmpty()
                        && (DCOBradescoOcorrenciaEnum.Ocor02.name().equals(idOcorrencia)
                        || DCOBradescoOcorrenciaEnum.Ocor06.name().equals(idOcorrencia)
                        || DCOBradescoOcorrenciaEnum.Ocor09.name().equals(idOcorrencia)
                        || DCOBradescoOcorrenciaEnum.Ocor10.name().equals(idOcorrencia))
                        && motivo != null && motivo.equals("00")) {
                    r.put(DCCAttEnum.CodigoAutorizacao,
                            r.get(DCCAttEnum.NossoNumero.name()) + r.get(DCCAttEnum.NossoNumeroDV.name()));
                }

            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//1
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, linha));//1
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(2, 4, linha));//2
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(4, 7, linha));//3
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 17, linha));//10

            //r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17, 25, linha));//8
            //r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(25, 39, linha));//14
            String qtdConfirmada = StringUtilities.readString(57, 62, linha);
            String qtdLiquidada = StringUtilities.readString(86, 91, linha);
            String qtdBaixada = StringUtilities.readString(103, 108, linha);
            Integer qtdRegistros = Integer.valueOf(qtdConfirmada) +  Integer.valueOf(qtdLiquidada) + Integer.valueOf(qtdBaixada);
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(qtdRegistros, 5));//8
            String valorLiquidado = StringUtilities.readString(91, 103, linha);
            String valorBaixado = StringUtilities.readString(108, 120, linha);
            Integer valorTotal = Integer.valueOf(valorLiquidado) +  Integer.valueOf(valorBaixado);
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoForcandoZerosAEsquerda(valorTotal.toString(), 12));//14
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(39, 47, linha));//8
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(47, 57, linha));//10
            r.put(DCCAttEnum.QtdConfirmados, StringUtilities.readString(57, 62, linha));//5
            r.put(DCCAttEnum.ValorConfirmados, StringUtilities.readString(62, 74, linha));//12
            r.put(DCCAttEnum.ValorLiquidado, StringUtilities.readString(74, 86, linha));//12
            r.put(DCCAttEnum.QtdLiquidado, StringUtilities.readString(86, 91, linha));//5
            r.put(DCCAttEnum.ValorLiquidado2, StringUtilities.readString(91, 103, linha));//12
            r.put(DCCAttEnum.QtdTitulosBaixados, StringUtilities.readString(103, 108, linha));//5
            r.put(DCCAttEnum.ValorTitulosBaixados, StringUtilities.readString(108, 120, linha));//12
            r.put(DCCAttEnum.QtdAbatimentoCancelado, StringUtilities.readString(120, 125, linha));//5
            r.put(DCCAttEnum.ValorAbatimentoCancelado, StringUtilities.readString(125, 137, linha));//12
            r.put(DCCAttEnum.QtdVencAlterado, StringUtilities.readString(137, 142, linha));//5
            r.put(DCCAttEnum.ValorVencimentoAlterado, StringUtilities.readString(142, 154, linha));//12
            r.put(DCCAttEnum.QtdAbatimentoConcedido, StringUtilities.readString(154, 159, linha));//5
            r.put(DCCAttEnum.ValorAbatimentoConcedido, StringUtilities.readString(159, 171, linha));//12
            r.put(DCCAttEnum.QtdConfirmacaoProtesto, StringUtilities.readString(171, 176, linha));//5
            r.put(DCCAttEnum.ValorConfirmacaoProtesto, StringUtilities.readString(176, 188, linha));//12
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(188, 362, linha));//174
            r.put(DCCAttEnum.ValorRateios, StringUtilities.readString(362, 377, linha));//15
            r.put(DCCAttEnum.QtdRateios, StringUtilities.readString(377, 385, linha));//8
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(385, 394, linha));//9
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//6

        }
    }

    private static String calcularDV(final String src) {
        String base2 = "";
        int[] arrBase7 = new int[]{2, 7, 6, 5, 4, 3};
        int indexBase7 = 0;
        for (int i = 0; i < src.length(); i++) {
            if (indexBase7 + 1 > arrBase7.length) {
                indexBase7 = 0;
            }
            int fator = Integer.valueOf(src.substring(i, i + 1)) * arrBase7[indexBase7];
            String parc = StringUtilities.formatarCampo(new BigDecimal(fator), 2);
            base2 += parc;
            indexBase7++;
        }

        System.out.println(base2 + " - " + base2.length());
        int par[] = new int[base2.length() / 2];
        int impar[] = new int[base2.length() / 2];

        int j = 0, k = 0;
        for (int i = 0; i < base2.length(); i++) {
            int valor = Integer.valueOf(base2.substring(i, i + 1));
            if (i % 2 == 0) {
                par[j] = valor;
                j++;
            } else {
                impar[k] = valor;
                k++;
            }
        }
        int somaImpar = 0;
        for (int i = 0; i < par.length; i++) {
            somaImpar += impar[i];
        }
        int somaPar = 0;
        for (int i = 0; i < par.length; i++) {
            somaPar += par[i];
        }
        int resto = Integer.valueOf(Integer.toString(somaPar) + Integer.toString(somaImpar)) % 11;
        if (resto == 1) {
            return "P";
        } else if (resto == 0) {
            return "0";
        } else {
            return Integer.toString(11 - resto);
        }
    }

    public static String novoCalcularDV(final String src) {
        int soma = 0;
        int[] arrBase7 = new int[]{2, 7, 6, 5, 4, 3, 2, 7, 6, 5, 4, 3, 2};
        int indexBase7 = 0;
        for (int i = 0; i < src.length(); i++) {
            if (indexBase7 + 1 > arrBase7.length) {
                indexBase7 = 0;
            }
            int fator = Integer.valueOf(src.substring(i, i + 1)) * arrBase7[indexBase7];
            soma += fator;
            indexBase7++;
        }
        int resto = (soma % 11);

        if (resto == 1) {
            return "P";
        } else if (resto == 0) {
            return "0";
        } else {
            return Integer.toString(11 - resto);
        }
    }

//    public static void main(String... args) {
//        System.out.println(calcularDV("1900000000006"));
//        System.out.println("007.424.101-02".replaceAll("[.,-]", "").substring(0, 9));
//    }
}

package servicos.impl.dcc.caixa;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jboleto.JBoletoBean;
import org.jboleto.bancos.CaixaEconomica;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Created by Luiz Felipe on 31/07/2017
 */
public class LayoutRemessaCaixaCNAB240 extends LayoutRemessaBase {

    private static Pattern patternHEAD = Pattern.compile("^[0-9]{8}T");
    private static Pattern patternT = Pattern.compile("^[0-9]{13}T");
    private static Pattern patternU = Pattern.compile("^[0-9]{13}U");
    private static Pattern patterTRAILER = Pattern.compile("^[0-9]{7}5");
    private static String tituloRegistratoEmissaoBeneficiario = "14";


    public static void preencherArquivoRemessa(RemessaVO remessa) {


//        DESCRIÇÃO DE REGISTRO TIPO 1
//        HEADER DE LOTE DE ARQUIVO REMESSA

        int tamanhoCodigoBeneficiario = remessa.getConvenioCobranca().getNumeroContrato().length();

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataGeracao = remessa.getDataRegistro();
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        header.put(DCCAttEnum.LoteServico, "0000");
        header.put(DCCAttEnum.TipoRegistro, "0");
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        header.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        header.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
        header.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(20));
        header.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        header.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
        if (tamanhoCodigoBeneficiario <= 6) {
            header.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 6));
            header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(1));
        } else {
            header.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 7));
        }
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(7));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        header.put(DCCAttEnum.Banco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 30));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
        header.put(DCCAttEnum.CodigoRemessa, "1");
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyyyy"));
        header.put(DCCAttEnum.HoraGeracao, StringUtilities.formatarCampoData(dataGeracao, "HHmmss"));
        header.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getSequencialArquivo(), 6));
        if (tamanhoCodigoBeneficiario <= 6) {
            header.put(DCCAttEnum.VersaoLayout, "101");
        } else {
            header.put(DCCAttEnum.VersaoLayout, "107");
        }
        header.put(DCCAttEnum.DensidadeGravacao, StringUtilities.formatarCampoZerado(5));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(20));

//        header.put(DCCAttEnum.SituacaoArquivo, StringUtilities.formatarCampoEmBranco("REMESSA-TESTE", 20));
        header.put(DCCAttEnum.SituacaoArquivo, StringUtilities.formatarCampoEmBranco("REMESSA-PRODUCAO", 20));

        header.put(DCCAttEnum.VersaoAplicativo, StringUtilities.formatarCampoEmBranco(4));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(25));

        remessa.setHeaderRemessa(header);
        remessa.setHeaderRemessaArquivo(getHeaderLote(remessa, dataGeracao, tamanhoCodigoBeneficiario));


        boolean cobrarJuros = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros();
        Double jurosMesPercentual = 0.0;
        if (cobrarJuros) {
            jurosMesPercentual = Uteis.arredondarForcando2CasasDecimais(remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica() * 30);
        }

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int seq = 1;
        for (RemessaItemVO item : lista) {
            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    enderecoVO = endereco;
                }
            }
            if (UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                if (!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())) {
                    enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
                }
            }


//            DESCRIÇÃO DE REGISTRO TIPO 3, Segmento P:
//            DADOS DO TÍTULO
            RegistroRemessa detailP = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_P);
            detailP.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailP.put(DCCAttEnum.LoteServico, "0001");
            detailP.put(DCCAttEnum.TipoRegistro, "3");
            detailP.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailP.put(DCCAttEnum.Segmento, "P");
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.CodigoMovimento, "01");
            detailP.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
            detailP.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
            if (tamanhoCodigoBeneficiario <= 6) {
                detailP.put(DCCAttEnum.CodigoConvenioBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 6));
                detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(1));
            } else {
                detailP.put(DCCAttEnum.CodigoConvenioBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 7));
            }
            detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(7));
            detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(2));
            detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(1));
            detailP.put(DCCAttEnum.NossoNumero, calcularNossoNumero(item));
            detailP.put(DCCAttEnum.Carteira, "1");
            detailP.put(DCCAttEnum.FormaDeCadastramentoTituloBanco, "1");
            detailP.put(DCCAttEnum.TipoDocumento, "2");
            detailP.put(DCCAttEnum.IdentificacaoEmissaoBoleto, "2");
            detailP.put(DCCAttEnum.IdentificacaoEntregaBoleto, "0");
            detailP.put(DCCAttEnum.NumeroDocumentoCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getIdentificador(), 11));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(4));
            detailP.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));
            detailP.put(DCCAttEnum.ValorNominalTitulo, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));
            detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(5));
            detailP.put(DCCAttEnum.DigitoVerificadorAgencia, "0");
            detailP.put(DCCAttEnum.EspecieTitulo, "02");
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, "N");
            detailP.put(DCCAttEnum.DataEmissao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyyyy"));

            //Juros
            if (cobrarJuros) {
                detailP.put(DCCAttEnum.CodigoJuros, "2");
                detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));
                detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(jurosMesPercentual, 15));
            } else {
                detailP.put(DCCAttEnum.CodigoJuros, "3");
                detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoZerado(8));
                detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoZerado(15));
            }

            detailP.put(DCCAttEnum.CodigoDesconto, "0");
            detailP.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(8));
            detailP.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, StringUtilities.formatarCampoEmBranco(item.getIdentificador().toString(), 25));
            if (remessa.getConvenioCobranca().getNrDiasProtesto() > 0) {
                //Para protesto, é necessário código 2 no tipo de baixa;
                detailP.put(DCCAttEnum.Protesto, "1");
                detailP.put(DCCAttEnum.DiasProtesto, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNrDiasProtesto(), 2));
                detailP.put(DCCAttEnum.CodigoBaixa, "2");
            } else {
                detailP.put(DCCAttEnum.Protesto, "3");
                detailP.put(DCCAttEnum.DiasProtesto, StringUtilities.formatarCampoZerado(2));
                detailP.put(DCCAttEnum.CodigoBaixa, "1");
            }

            if (remessa.getConvenioCobranca().getNrDiasBaixaAutomatica() > 0) {
                detailP.put(DCCAttEnum.NumeroDiasBaixa, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNrDiasBaixaAutomatica(), 3));
            } else {
                detailP.put(DCCAttEnum.NumeroDiasBaixa, StringUtilities.formatarCampoZerado(3));
            }

            detailP.put(DCCAttEnum.Moeda, "09");
            detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(10));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));


//            DESCRIÇÃO DE REGISTRO TIPO 3, Segmento Q:
//            DADOS DO PAGADOR E SACADOR/AVALISTA

            RegistroRemessa detailQ = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_Q);

            detailQ.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailQ.put(DCCAttEnum.LoteServico, "0001");
            detailQ.put(DCCAttEnum.TipoRegistro, "3");
            seq++;

            detailQ.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailQ.put(DCCAttEnum.Segmento, "Q");
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailQ.put(DCCAttEnum.CodigoMovimento, "01");
            detailQ.put(DCCAttEnum.TipoInscricao, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "1" : "2");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detailQ.put(DCCAttEnum.CpfOuCnpj, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 15));
            detailQ.put(DCCAttEnum.NomeCliente, getNome(item, 40));
            detailQ.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 40));
            detailQ.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 15));
            detailQ.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
            detailQ.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNome(), 15));
            detailQ.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
            detailQ.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(16));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailQ.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoEmBranco(3));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(20));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));

            seq++;

            RegistroRemessa detailR = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_R);
            detailR.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailR.put(DCCAttEnum.LoteServico, "0001");
            detailR.put(DCCAttEnum.TipoRegistro, "3");
            detailR.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailR.put(DCCAttEnum.Segmento, "R");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.CodigoMovimento, "01");
            detailR.put(DCCAttEnum.CodigoDesconto, "0");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(13));
            detailR.put(DCCAttEnum.CodigoDesconto, "0");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(17));

            //Multa
            if(remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros()){
                detailR.put(DCCAttEnum.CobrarMulta, "2");
                detailR.put(DCCAttEnum.DataMulta, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));
                detailR.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetarioVirgula(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica(), 13, 2).replace(",",""));
            }else{
                detailR.put(DCCAttEnum.CobrarMulta, "0");
                detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
                detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(15));
            }

            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(50));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(11));
            seq++;
            //
            listaDetalhe.add(detailP);
            listaDetalhe.add(detailQ);
            listaDetalhe.add(detailR);
        }

        remessa.setQtdAceito(lista.size());

//        double valorTotalBruto = 0.0;
//        for (RemessaItemVO boleto : remessa.getListaItens()) {
//            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
//                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
//            }
//        }


//        DESCRIÇÃO DE REGISTRO TIPO 5:
//        TRAILER DE LOTE DE ARQUIVO REMESSA

        remessa.setTrailerRemessaArquivo(getTrailerArquivo(remessa));

//        DESCRIÇÃO DE REGISTRO TIPO 9:
//        TRAILER DE ARQUIVO REMESSA

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);

        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "9999");
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QtdLotes, StringUtilities.formatarCampoForcandoZerosAEsquerda(1, 6));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 3 + 4, 6));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(211));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setTrailerArquivo(new StringBuilder(remessa.getTrailerRemessaArquivo().toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setHeaderArquivo(new StringBuilder(remessa.getHeaderRemessaArquivo().toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
    }

    public static String calcularNossoNumero(RemessaItemVO obj) {
        return tituloRegistratoEmissaoBeneficiario + StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getIdentificador(), 15);
    }

    private static RegistroRemessa getTrailerArquivo(RemessaVO remessa) {
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);

        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "0001");
        trailer.put(DCCAttEnum.TipoRegistro, "5");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QtdLotes, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 3 + 2, 6));
        trailer.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(69));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(148));
        return trailer;
    }

    private static RegistroRemessa getHeaderLote(RemessaVO remessa, Date dataGravacao, int tamanhoCodigoBeneficiario) {

        RegistroRemessa registro = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);

        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        registro.put(DCCAttEnum.LoteServico, "0001");
        registro.put(DCCAttEnum.TipoRegistro, "1");
        registro.put(DCCAttEnum.TipoOperacao, "R");
        registro.put(DCCAttEnum.TipoServico, "01"); //Preencher com 01, se Cobrança Registrada; ou 02, se Cobrança Sem Registro/Serviços
        registro.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(2));
        if (tamanhoCodigoBeneficiario <= 6) {
            registro.put(DCCAttEnum.VersaoLayout, "060");
        } else {
            registro.put(DCCAttEnum.VersaoLayout, "067");
        }
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 15));
        if (tamanhoCodigoBeneficiario <= 6) {
            registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 6));
            registro.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(1));
        } else {
            registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 7));
        }
        registro.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(13));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
        if (tamanhoCodigoBeneficiario <= 6) {
            registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 6));
        } else {
            registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoZerado(6));
        }
        registro.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(7));
        registro.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(1));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        registro.put(DCCAttEnum.Mensagem1, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getMensagem(), 40));
        registro.put(DCCAttEnum.Mensagem2, StringUtilities.formatarCampoEmBranco(40));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getSequencialArquivo(), 8));
        registro.put(DCCAttEnum.DataGravacao, StringUtilities.formatarCampoData(dataGravacao, "ddMMyyyy"));
        registro.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(8));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(33));

        return registro;
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            while ((linha = br.readLine()) != null) {
                if (patternHEAD.matcher(linha).find()) {
                    lerHeader(linha, h);
                    break;
                }
            }
        }
        return h;
    }

    /**
     * Realiza a leitura do header do lote para a validação do arquivo.
     *
     * @param linha
     * @param registro
     */
    private static void lerHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0, 3, linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3, 7, linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));
        registro.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(8, 9, linha));
        registro.put(DCCAttEnum.TipoServico, StringUtilities.readString(9, 11, linha));
        registro.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(13, 16, linha));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, StringUtilities.readString(17, 18, linha));
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(18, 33, linha));
        registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.readString(33, 39, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(39, 53, linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(53, 58, linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(58, 59, linha));
        registro.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(59, 65, linha));
        registro.put(DCCAttEnum.CodigoModeloBoleto, StringUtilities.readString(65, 72, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(72, 73, linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(73, 103, linha));
        registro.put(DCCAttEnum.Mensagem1, StringUtilities.readString(103, 143, linha));
        registro.put(DCCAttEnum.Mensagem2, StringUtilities.readString(143, 183, linha));
        registro.put(DCCAttEnum.NumeroArquivo, StringUtilities.readString(183, 191, linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(191, 199, linha));
        registro.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(199, 207, linha));
        registro.put(DCCAttEnum.Filler, StringUtilities.readString(207, 209, linha));
        registro.put(DCCAttEnum.Filler, StringUtilities.readString(209, 235, linha));
        registro.put(DCCAttEnum.Filler, StringUtilities.readString(235, 237, linha));
        registro.put(DCCAttEnum.Filler, StringUtilities.readString(237, 240, linha));
    }

    /**
     * Realiza a leitura do registro T do arquivo de retorno do banco.
     *
     * @param linha
     * @param registro
     */
    private static void lerDetailT(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0, 3, linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3, 7, linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));

        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(8, 13, linha));
        registro.put(DCCAttEnum.Segmento, StringUtilities.readString(13, 14, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(14, 15, linha));
        registro.put(DCCAttEnum.StatusVenda, StringUtilities.readString(15, 17, linha));

        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(17, 22, linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(22, 23, linha));
        registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.readString(23, 29, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(29, 32, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(32, 35, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(35, 36, linha));
        registro.put(DCCAttEnum.EmBranco2, StringUtilities.readString(36, 39, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(39, 41, linha));
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(41, 56, linha));
        registro.put(DCCAttEnum.UsoBanco, StringUtilities.readString(56, 57, linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(57, 58, linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(58, 69, linha));
        registro.put(DCCAttEnum.EmBranco3, StringUtilities.readString(69, 73, linha));
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(73, 81, linha));
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(81, 96, linha));
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(96, 99, linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(99, 104, linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(104, 105, linha));
        registro.put(DCCAttEnum.UsoEmpresa, StringUtilities.readString(105, 130, linha));
        registro.put(DCCAttEnum.Moeda, StringUtilities.readString(130, 132, linha));
        registro.put(DCCAttEnum.TipoInscricao, StringUtilities.readString(132, 133, linha));
        registro.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(133, 148, linha));
        registro.put(DCCAttEnum.NomeCliente, StringUtilities.readString(148, 188, linha));
        registro.put(DCCAttEnum.EmBranco4, StringUtilities.readString(188, 198, linha));
        registro.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(198, 213, linha));
        registro.put(DCCAttEnum.Motivo, StringUtilities.readString(213, 223, linha));
        registro.put(DCCAttEnum.EmBranco5, StringUtilities.readString(223, linha));
    }

    /**
     * Realiza a leitura do registro U do arquivo de retorno do banco.
     *
     * @param linha
     * @param registro
     */
    private static void lerDetailU(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(17, 32, linha));
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(77, 92, linha));
        registro.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(198, 213, linha));
    }


    /**
     * Realiza a leitura do trailer
     *
     * @param linha
     * @param registro
     */
    private static void lerTrailer(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0, 3, linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3, 7, linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));
        registro.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17, 23, linha));
    }


    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();

        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            RegistroRemessa detail = null;
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (patternHEAD.matcher(linha).find()) {
                    lerHeader(linha, h);
                } else if (patternT.matcher(linha).find()) {
                    detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_T);
                    lerDetailT(linha, detail);
                } else if (patternU.matcher(linha).find()) {
                    lerDetailU(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (patterTRAILER.matcher(linha).find()) {
                    lerTrailer(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static String obterCodigosRemessaItem(StringBuilder retorno) throws IOException {
        StringBuilder codigos = new StringBuilder();
        BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
        String linha;
        while ((linha = br.readLine()) != null) {
            if (patternT.matcher(linha).find()) {
                String nossoNumero = StringUtilities.readString(41, 56, linha);
                nossoNumero = nossoNumero.trim();
                if (!nossoNumero.isEmpty()) {
                    codigos.append(nossoNumero);
                    codigos.append(",");
                }
            }
        }

        if (codigos.length() > 0) {
            codigos.deleteCharAt(codigos.length() - 1);
        }

        return codigos.toString();
    }

    public static String calcularDVNossoNumero(JBoletoBean jBoletoBean, RemessaItemVO obj) {
        return new CaixaEconomica(jBoletoBean).calcularDVNossoNumero(calcularNossoNumero(obj));
    }
}




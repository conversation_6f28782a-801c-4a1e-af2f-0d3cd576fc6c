/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.caixa;

import negocio.comuns.financeiro.enumerador.IdentificadorClienteEmpresaEnum;
import negocio.comuns.utilitarias.*;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import br.com.pactosolucoes.comuns.util.StringUtilities;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;


/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 08/07/1989
 *
 * Leiaute do Arquivo Padrão FEBRABAN 150 - SIACC
 *
 */

public class LayoutRemessaCaixaSIACC150DCO extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {


        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();

//A.01 1 1 X(01) Código do registro = "A"
        header.put(DCCAttEnum.TipoRegistro, "A");
//A.02 2 2 9(01) Código da Remessa
        header.put(DCCAttEnum.CodigoRemessa, "1");
//A.03 3 22 X(20) Código do convênio
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getNumeroContrato(), 6));

        header.put(DCCAttEnum.TipoCompromisso, "11"); // 11 - Débito Automático

        header.put(DCCAttEnum.NumeroCompromisso, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroCompromisso(), 4));

        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));

//A.04 23 42 X(20) Nome da Empresa
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 20));

//A.05 43 45 9(03) Código do Banco = "104"
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), MathContext.UNLIMITED), 3));

//A.06 46 65 X(20) Nome do Banco
        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 20));

//A.07 66 73 9(08) Data do Movimento
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "yyyyMMdd"));
//A.08 74 79 9(06) Número seqüencial do arquivo

        String numeroResumoOperacoes = "";
        RegistroRemessa headerRemessa = remessa.getHeaderRemessa();
        for (ObjetoGenerico obj : headerRemessa.getAtributos()) {
            if (obj.getAtributo().equals(DCCAttEnum.NumeroResumoOperacoes.name())) {
                numeroResumoOperacoes = StringUtilities.formatarCampo(new BigDecimal(obj.getValor()), 6);
            }
        }
        numeroResumoOperacoes = UteisValidacao.emptyString(numeroResumoOperacoes) ?
                StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getSequencialDoArquivo()), 6)
                : numeroResumoOperacoes;

        header.put(DCCAttEnum.NumeroResumoOperacoes, numeroResumoOperacoes);
//A.09 80 81 9(02) Versão do layout
        header.put(DCCAttEnum.VersaoLayout, "04");
//A.10 82 98 X(17) Identificação do Serviço
        header.put(DCCAttEnum.IdentificacaoServico, StringUtilities.formatarCampoEmBranco("DEB AUTOMAT", 17));
//A.11 99 115 9(16) Conta Compromisso
        header.put(DCCAttEnum.AgenciaDepositaria,
                StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 4));
        header.put(DCCAttEnum.CodigoOperacao,
                StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getCodigoOperacao(), 3));
        header.put(DCCAttEnum.ContaCorrente,
                StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 8));
        header.put(DCCAttEnum.ContaCorrenteDigito,
                StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV(), 1));
//A.12 116 116 X(01) Identificação do Ambiente Cliente
        header.put(DCCAttEnum.IdentificacaoAmbienteCliente, "P");
//A.13 117 117 X(01) Identificação do Ambiente Caixa
        header.put(DCCAttEnum.IdentificacaoAmbienteCaixa, "P");
//A.14 118 143 X(27) Reservado para o futuro (?filler?)
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(27));
//A.15 144 149 9(06) Número Seqüencial do Registro
        header.put(DCCAttEnum.SequencialRegistro, "000000");
//A.16 150 150 X(01) Reservado para o futuro (?filler?)
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(1));


        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        int seq = 1;
        Ordenacao.ordenarLista(lista, "nome");
        for (RemessaItemVO item : lista) {
            if (item.getAutorizacaoCobrancaVO() != null && item.getAutorizacaoCobrancaVO().isAutorizarClienteDebito()) {
                listaDetalhe.add(montarAutorizarDebito(remessa, item));
            }

            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

//E.01 1 1 X(01) Código do registro = "E"
            detail.put(DCCAttEnum.TipoRegistro, "E");
//E.02 2 26 X(25) Identificação do cliente na Empresa
            if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.COD_PESSOA)) {
                detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getPessoa().getCodigo()), 25));
            } else if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.CPF)) {
                detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoEmBranco(StringUtilities.formatarCpfCnjp(item.getMovParcela().getPessoa().getCfp(), 11), 25));
            } else if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.MATRICULA)) {
                detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoEmBranco(item.getClienteVO().getMatricula(), 25));
            }

//E.03 27 30 9(04) Agência para débito/crédito
            detail.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.AgenciaDebito.name()), MathContext.UNLIMITED), 4));
//E.04 31 44 X(14) Identificação do cliente no banco
            //E.04 31 33 código da operação
            detail.put(DCCAttEnum.CodigoOperacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.get(DCCAttEnum.CodigoOperacao.name()), 3));
            //E.04 34 41 numero de conta
            detail.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampo(
                    new BigDecimal(item.get(DCCAttEnum.ContaCorrenteDebito.name())), 8));
            //E.04 42 42 DV da conta
            detail.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.formatarCampo(
                    new BigDecimal(item.get(DCCAttEnum.ContaCorrenteDebitoDigito.name())), 1));
            //E.04 43 44 branco
            detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));

//E.05 e E06
            //E.05 45 52 9(08) Data do vencimento
            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(getDataVencimento(item.getMovParcela().getDataVencimento(), dataDeposito, remessa), "yyyyMMdd"));
            //E.06 53 67 9(15) Valor do débito/crédito
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));

//E.07 68 69 X(02) Código da moeda
            detail.put(DCCAttEnum.Moeda, "03");
//E.08 70 129 X(60) Uso da Empresa
            detail.put(DCCAttEnum.NomePessoa, StringUtilities.formatarCampoEmBranco(
                    item.getMovParcela().getCodigo().toString(), 60));
//E.09 130 135 9(06) Número do Agendamento Cliente
            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(seq), 6));
//E.10 136 143 X(08) Reservado para o futuro (?filler?)
            detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(8));
//E.11 144 149 9(06) Número Seqüencial do Registro
            detail.put(DCCAttEnum.SequencialRegistro,
                    StringUtilities.formatarCampo(new BigDecimal(seq), 6));
//E.12 150 150 9(01) Código do movimento
            detail.put(DCCAttEnum.CodigoMovimento, "0");
            seq++;
            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            listaDetalhe.add(detail);
        }

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
//        Z.01 1 1 X(01) Código do registro = "Z"
        trailer.put(DCCAttEnum.TipoRegistro, "Z");
//Z.02 2 7 9(06) Total de registro do arquivo
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 6));
//Z.03 8 24 9(17) Valor total dos registros do arquivo
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 17));
//Z.04 25 143 X(119) Reservado para o futuro
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(119));
//Z.05 144 149 9(06) Número Seqüencial do Registro
        trailer.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(
                new BigDecimal(seq), 6));
//Z.06 150 150 9(01) Reservado para o futuro
        trailer.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(1));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                lerAtributos(linha, h);
                break;
            }
        }
        return h;
    }

    public static RegistroRemessa montarAutorizarDebito(RemessaVO remessa, RemessaItemVO item) {
        List<RemessaItemVO> lista = remessa.getListaItens();
        RegistroRemessa detail = null;
        int qtd = 0;
        detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

//B.01 1 1 X(01) Código do registro = "B"
        detail.put(DCCAttEnum.TipoRegistro, "B");
//B.02 2 26 X(25) Identificação do cliente na Empresa
        if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.COD_PESSOA)) {
            detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getPessoa().getCodigo()), 25));
        } else if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.CPF)) {
            detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoEmBranco(StringUtilities.formatarCpfCnjp(item.getMovParcela().getPessoa().getCfp(), 11), 25));
        } else if (remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.MATRICULA)) {
            detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoEmBranco(item.getClienteVO().getMatricula(), 25));
        }

//B.03 27 30 9(04) Agência para débito/crédito
        detail.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampo(new BigDecimal(item.get(DCCAttEnum.AgenciaDebito.name()), MathContext.UNLIMITED), 4));
//B.04 31 44 X(14) Identificação do cliente no banco
        //B.04 31 33 código da operação
        detail.put(DCCAttEnum.CodigoOperacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.get(DCCAttEnum.CodigoOperacao.name()), 3));
        //B.04 34 41 numero de conta
        detail.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampo(
                new BigDecimal(item.get(DCCAttEnum.ContaCorrenteDebito.name())), 8));
        //B.04 42 42 DV da conta
        detail.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.formatarCampo(
                new BigDecimal(item.get(DCCAttEnum.ContaCorrenteDebitoDigito.name())), 1));
        //B.04 43 44 branco
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
//B.05 45 52 9(08)
        detail.put(DCCAttEnum.DataGeracao, Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMdd"));
//B.06 53 149 X(97)
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(97));
        //B.07 150 150 9(1)
        detail.put(DCCAttEnum.CodigoMovimento, "2");// 1 para exclusão e 2 para inclusão

        remessa.setQtdAceito(qtd);
        return detail;
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("A")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("B")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_B);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("F")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("Z")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

//            validarArquivoRemessaRetorno(remessa);
//            lerHeaderETrailerRemessa(remessa);

            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
            for (ObjetoGenerico objetoGenerico : atributos) {
                preencherAtributosTransientes(remessa, objetoGenerico);
            }


            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
//            A.01 1 1 X(01) Código do registro = "A"
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
//A.02 2 2 9(01) Código da Remessa
            r.put(DCCAttEnum.CodigoRemessa, StringUtilities.readString(1, 2, linha));
//A.03 3 22 X(20) Código do convênio
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(2, 22, linha));
//A.04 23 42 X(20) Nome da Empresa
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(22, 42, linha));
//A.05 43 45 9(03) Código do Banco = "104"
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(42, 45, linha));
//A.06 46 65 X(20) Nome do Banco
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(45, 65, linha));
//A.07 66 73 9(08) Data do Movimento
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(65, 73, linha));
//A.08 74 79 9(06) Número seqüencial do arquivo
            r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(73, 79, linha));
//A.09 80 81 9(02) Versão do layout
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(79, 81, linha));
//A.10 82 98 X(17) Identificação do Serviço
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(81, 98, linha));
//A.11 99 114 9(16) Conta Compromisso
            r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(98, 102, linha));
            r.put(DCCAttEnum.CodigoOperacao, StringUtilities.readString(102, 105, linha));
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(105, 113, linha));
            r.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(113, 114, linha));
//A.12 115 115 X(01) Identificação do Ambiente Cliente
            r.put(DCCAttEnum.IdentificacaoAmbienteCliente, StringUtilities.readString(114, 115, linha));
//A.13 116 116 X(01) Identificação do Ambiente Caixa
            r.put(DCCAttEnum.IdentificacaoAmbienteCaixa, StringUtilities.readString(115, 116, linha));
//A.14 117 143 X(27) Reservado para o futuro (?filler?)
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(116, 143, linha));
//A.15 144 149 9(06) Número Seqüencial do Registro
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(143, 149, linha));
//A.16 150 150 X(01) Reservado para o futuro (?filler?)
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(149, 150, linha));


        } else if (r.getTipo() == TipoRegistroEnum.DETALHE_REGISTRO_B) {
            if (linha.length() >= 150) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
                r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(1, 26, linha));
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(26, 30, linha));
                r.put(DCCAttEnum.CodigoOperacao, StringUtilities.readString(30, 33, linha));
                r.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(33, 41, linha));
                r.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(41, 42, linha));
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(42, 44, linha));
                r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(44, 52, linha));
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(52, 149, linha));
                r.put(DCCAttEnum.CodigoMovimento, StringUtilities.readString(149, 150, linha));
            }
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 150) {
//                F.01 1 1 X(01) Código do registro = "F"
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
//F.02 2 26 X(25) Identificação do cliente na Empresa
                r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(1, 26, linha));
//F.03 27 30 9(04) Agência para débito/crédito
                r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(26, 30, linha));
//F.04 31 44 X(14) Identificação do cliente no Banco
                r.put(DCCAttEnum.CodigoOperacao, StringUtilities.readString(30, 33, linha));
                r.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(33, 41, linha));
                r.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(41, 42, linha));
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(42, 44, linha));
//F.05 45 52 9(08) Data do vencimento débito/crédito
                r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(44, 52, linha));
//F.06 53 67 9(15) Valor do débito/crédito
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(52, 67, linha));
//F.07 68 69 X(02) Código de retorno
                final String status = StringUtilities.readString(67, 69, linha);
                r.put(DCCAttEnum.StatusVenda, status);
                //Se o Débito foi efetuado, usar como Código de Autorização a Conta/Corrente do Débito realizado
                if (status != null && !status.isEmpty() && DCOCaixaStatusSIACC150Enum.Status00.getId().equals(status)) {
                    r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(1, 26, linha));
                }
//F.08 70 129 X(60) Uso da Empresa
                r.put(DCCAttEnum.NomePessoa, StringUtilities.readString(69, 129, linha));
//F.09 130 149 X(20) Reservado para o futuro (?filler?)
                r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(129, 149, linha));
//F.10 150 150 9(01) Código do movimento
                r.put(DCCAttEnum.CodigoMovimento, StringUtilities.readString(149, 150, linha));

            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(1, 7, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(7, 24, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(24, 143, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(143, 149, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(149, 150, linha));

        }
    }

    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                String tipo = StringUtilities.readString(0, 1, linha);
                if (tipo.equals("F")) {
                    codigos.add(Integer.valueOf(StringUtilities.readString(69, 129, linha).trim()));
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Integer cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }
}

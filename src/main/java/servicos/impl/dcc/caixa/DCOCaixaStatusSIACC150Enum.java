/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.caixa;

public enum DCOCaixaStatusSIACC150Enum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status00("00", "Débito/crédito efetuado."),
    Status01("01", "Número Remessa Inválido."),
    Status02("02", "Arquivo sem HEADER."),
    Status03("03", "Tipo Registro Invalido."),
    Status04("04", "Código Banco Inválido."),
    Status05("05", "Insuficiência de Fundos."),
    Status06("06", "Tipo Serviço Inválido."),
    Status07("07", "Código do Convênio Inválido."),
    Status08("08", "<PERSON>ó<PERSON> da Remessa Inválido."),
    Status09("09", "Outras Restrições."),
    Status10("10", "Tipo de Operação Inválido."),
    Status11("11", "Agência Inválida."),
    Status12("12", "Número da Conta Inválido."),
    Status13("13", "Número de Lote Inválido."),
    Status14("14", "Código Segmento Inválido."),
    Status15("15", "Tipo Movimento Inválido"),
    Status16("16", "Banco Favorecido Inválido."),
    Status17("17", "Nome do Favorecido Inválido."),
    Status18("18", "Seu Número Inválido."),
    Status19("19", "Data de Pagamento Inválido"),
    Status20("20", "Tipo de Moeda Inválido."),
    Status21("21", "Quantidade de Moeda Inválida."),
    Status22("22", "Valor de Pagamento Inválido."),
    Status23("23", "Tipo de Inscrição Inválido."),
    Status24("24", "Número de Inscrição Inválido."),
    Status25("25", "Logradouro/Complemento Inválido."),
    Status26("26", "Num. Local do Favorecido Inválido."),
    Status27("27", "Código Documento Favorecido Inválido."),
    Status28("28", "Bairro do Favorecido Inválido."),
    Status29("29", "Cidade do Favorecido Inválida."),
    Status30("30", "Num. CEP/Complemento Inválido."),
    Status31("31", "Estado do Favorecido Inválido."),
    Status32("32", "Código de Barras Inválido."),
    Status33("33", "Nome do Cedente Inválido."),
    Status34("34", "Data de Vencimento Inválida. "),
    Status35("35", "Valor do Título Inválido."),
    Status36("36", "Qtde Regist. Lote C/Diferença."),
    Status37("37", "Valor Regist. Lote C/Diferença."),
    Status38("38", "Lote sem TRAILLER."),
    Status39("39", "Remessa sem TRAILLER."),
    Status40("40", "Total Registros do TRAILLER Inválido."),
    Status41("41", "Valor Total Registros do TRAILLER Inválido."),
    Status42("42", "Lote Fora de Seqüência."),
    Status43("43", "Nome Empresa Inválido."),
    Status44("44", "Num. Seq. De Registro Inválido."),
    Status45("45", "Nome do Banco Inválido."),
    Status46("46", "Data Movimento Inválida."),
    Status47("47", "Identificação Cliente Empresa Inválido."),
    Status48("48", "Código do Movimento Inválido."),
    Status49("49", "Tot. Lote no Arq C/Diferença."),
    Status50("50", "Convênio não Cadastrado."),
    Status51("51", "Parâmetro Transmissão não Cadastrado."),
    Status52("52", "Compromisso não Cadastrado."),
    Status53("53", "Agência Inativa."),
    Status54("54", "Agendamento já efetivado."),
    Status55("55", "Lote sem HEADER."),
    Status56("56", "Tipo de Operação Inválido."),
    Status57("57", "Agência Invalida."),
    Status58("58", "Cadastramento Convênio Incompleto."),
    Status59("59", "Situação Atual Convênio não ativo."),
    Status60("60", "Conta a Debitar Inexistente no Cadastro de Optantes."),
    Status61("61", "Conta Compromisso Inválida. "),
    Status62("62", "Número do Convênio Inválido."),
    Status63("63", "Tipo de Compromisso Inválido."),
    Status64("64", "Número de Compromisso Inválido."),
    Status65("65", "Mais de 1 TRAILLER na Remessa."),
    Status66("66", "Remessa com Erro."),
    Status67("67", "Data Opção Inválida."),
    Status68("68", "Qtde Moeda Lote C/Diferença."),
    Status69("69", "Optante já cadastrado para este Convênio."),
    Status70("70", "Indicação de Aviso sem endereço."),
    Status71("71", "Cód. De Barras/Cód. Banco Inválido."),
    Status72("72", "Cód. De Barras/Cód.Moeda Inválido."),
    Status73("73", "Cód de Barras/Dígito Verificador Geral Inválido."),
    Status74("74", "Código de Barras/Valor do Título Inválido."),
    Status76("76", "Quantidade de Parcelas Inválida."),
    Status77("77", "Indicador Bloqueio Parcela Inválido"),
    Status78("78", "Cadastro de Optantes Inexistente."),
    Status79("79", "Opção de Aviso sem endereço."),
    Status80("80", "Opção de Doc/OP sem endereço."),
    Status81("81", "Conta não Cadastrada."),
    Status82("82", "Tipo de Conta Inválido."),
    Status83("83", "Tipo de Operação diverge de Tipo de Compromisso."),
    Status84("84", "Tipo de Operação diverge com Tipo de Serviço."),
    Status85("85", "Data Cancelamento Expirada."),
    Status86("86", "Agendamento não Encontrado."),
    Status87("87", "Valor do débito maior que o valor limite."),
    Status88("88", "Índice Inválido."),
    Status89("89", "Data Atual do Compromisso não Ativa."),
    Status90("90", "Histórico não cadastrado."),
    Status91("91", "Registro já Existente na Base."),
    Status92("92", "Forma Parcelamento/Período Inválido."),
    Status93("93", "Erro no acesso TAB Parâmetro de Optantes."),
    Status94("94", "Convênio não cadastrado na TAB Parâmetro Optantes."),
    Status95("95", "Arquivo com data vencimento inferior a 03 dias úteis."),
    Status96("96", "Manutenção de Cadastro."),
    Status97("97", "Câmara de Compensação Inválida."),
    Status99("99", "Cancelamento Conforme Solicitado."),
    Status100("100", "Código DOC Favorecido Inválido.");
    //
    private String id;
    private String descricao;

    private DCOCaixaStatusSIACC150Enum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOCaixaStatusSIACC150Enum valueOff(String id) {
        for (DCOCaixaStatusSIACC150Enum eDIStatusEnum : DCOCaixaStatusSIACC150Enum.values()) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCOCaixaStatusSIACC150Enum.StatusNENHUM;
    }
}

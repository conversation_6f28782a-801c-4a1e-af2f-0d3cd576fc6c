/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.caixa;

public enum DCOCaixaStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status00("00", " Débito efetuado"),
    Status01("01", " Número Remessa Inválido"),
    Status02("02", " Arquivo sem HEADER?"),
    Status03("03", " Tipo Registro Invalido"),
    Status04("04", " Código Banco Inválido"),
    Status05("05", " Insuficiência de Fundos"),
    Status07("07", " Código do Convênio Inválido"),
    Status08("08", " Código da Remessa Inválido"),
    Status09("09", " Outras Restrições"),
    Status11("11", " Agência Inválida"),
    Status12("12", " Número da Conta Inválido"),
    Status15("15", " Tipo Movimento Inválido"),
    Status19("19", " Data de Pagamento Inválido"),
    Status20("20", " Tipo de Moeda Inválido"),
    Status21("21", " Quantidade de Moeda Inválida"),
    Status22("22", " Valor de Pagamento Inválido"),
    Status34("34", " Data de Vencimento Inválida"),
    Status39("39", " Remessa sem TRAILLER"),
    Status40("40", " Total Registros do TRAILLER Inválido"),
    Status41("41", " Valor Total Registros do TRAILLER Inválido"),
    Status46("46", " Data Movimento Inválida"),
    Status47("47", " Identificação Cliente Empresa Inválido"),
    Status50("50", " Convênio não Cadastrado"),
    Status51("51", " Parâmetro Transmissão não Cadastrado"),
    Status52("52", " Compromisso não Cadastrado"),
    Status53("53", " Agência Inativa"),
    Status57("57", " Agência Invalida"),
    Status60("60", " Conta a Debitar Inexistente no Cadastro de Optantes"),
    Status62("62", " Número do Convênio Inválido"),
    Status63("63", " Tipo de Compromisso Inválido"),
    Status64("64", " Número de Compromisso Inválido"),
    Status65("65", " Mais de 1 TRAILLER na Remessa"),
    Status66("66", " Remessa com Erro"),
    Status67("67", " Data Opção Inválida"),
    Status76("76", " Quantidade de Parcelas Inválida"),
    Status78("78", " Cadastro de Optantes Inexistente"),
    Status81("81", " Conta não Cadastrada"),
    Status82("82", " Conta não Cadastrada"),
    Status85("85", " Data Cancelamento Expirada"),
    Status86("86", " Agendamento não Encontrado"),
    Status87("87", " Valor do débito maior que o valor limite"),
    Status89("89", " Data Atual do Compromisso não Ativa"),
    Status91("91", " Registro já Existente na Base"),
    Status96("96", " Manutenção de Cadastro"),
    Status99("99", " Cancelamento Conforme Solicitado"),
    Status61("61", " Nenhum Status");
    //
    private String id;
    private String descricao;

    private DCOCaixaStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOCaixaStatusEnum valueOff(String id) {
        DCOCaixaStatusEnum[] values = DCOCaixaStatusEnum.values();
        for (DCOCaixaStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCOCaixaStatusEnum.StatusNENHUM;
    }
}

package servicos.impl.dcc.caixa;


public enum CaixaCNAB240StatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status02("02", "Entrada Confirmada"),
    Status03("03", "Entrada Rejeitada"),
    Status04("04", "Transferência de Carteira/Entrada"),
    Status05("05", "Transferência de Carteira/Baixa"),
    Status06("06", "Liquidação"),
    Status07("07", "Confirmação do Recebimento da Instrução de Desconto"),
    Status08("08", "Confirmação do Recebimento do Cancelamento do Desconto"),
    Status09("09", "Baixa"),
    Status11("11", "<PERSON><PERSON><PERSON>los em Carteira (Em Ser)"),
    Status12("12", "Confirmação Recebimento Instrução de Abatimento"),
    Status13("13", "Confirmação Recebimento Instrução de Cancelamento Abatimento"),
    Status14("14", "Confirmação Recebimento Instrução Alteração de Vencimento"),
    Status15("15", "Franco de Pagamento"),
    Status17("17", "Liquidação Após Baixa ou Liquidação Título Não Registrado"),
    Status19("19", "Confirmação Recebimento Instrução de Protesto"),
    Status20("20", "Confirmação Recebimento Instrução de Sustação/Cancelamento de Protesto"),
    Status23("23", "Remessa a Cartório (Aponte em Cartório)"),
    Status24("24", "Retirada de Cartório e Manutenção em Carteira"),
    Status25("25", "Protestado e Baixado (Baixa por Ter Sido Protestado)"),
    Status26("26", "Instrução Rejeitada"),
    Status27("27", "Confirmação do Pedido de Alteração de Outros Dados"),
    Status28("28", "Débito de Tarifas/Custas"),
    Status29("29", "Ocorrências do Sacado"),
    Status30("30", "Alteração de Dados Rejeitada"),
    Status33("33", "Confirmação da Alteração dos Dados do Rateio de Crédito"),
    Status34("34", "Confirmação do Cancelamento dos Dados do Rateio de Crédito"),
    Status35("35", "Confirmação do Desagendamento do Débito Automático"),
    Status36("36", "Confirmação de envio de e-mail/SMS"),
    Status37("37", "Envio de e-mail/SMS rejeitado"),
    Status38("38", "Confirmação de alteração do Prazo Limite de Recebimento (a data deve ser informada no campo 28.3.p)"),
    Status39("39", "Confirmação de Dispensa de Prazo Limite de Recebimento"),
    Status40("40", "Confirmação da alteração do número do título dado pelo cedente"),
    Status41("41", "Confirmação da alteração do número controle do Participante"),
    Status42("42", "Confirmação da alteração dos dados do Sacado"),
    Status43("43", "Confirmação da alteração dos dados do Sacador/Avalista"),
    Status44("44", "Título pago com cheque devolvido"),
    Status45("45", "Título pago com cheque compensado"),
    Status46("46", "Instrução para cancelar protesto confirmada"),
    Status47("47", "Instrução para protesto para fins falimentares confirmada"),
    Status48("48", "Confirmação de instrução de transferência de carteira/modalidade de cobrança"),
    Status49("49", "Alteração de contrato de cobrança"),
    Status50("50", "Título pago com cheque pendente de liquidação"),
    Status51("51", "Título DDA reconhecido pelo sacado"),
    Status52("52", "Título DDA não reconhecido pelo sacado"),
    Status53("53", "Título DDA recusado pela CIP"),
    Status54("54", "Confirmação da Instrução de Baixa de Título Negativado sem Protesto");

    private String id;
    private String descricao;

    private CaixaCNAB240StatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static CaixaCNAB240StatusEnum valueOff(String id) {
        CaixaCNAB240StatusEnum[] values = CaixaCNAB240StatusEnum.values();
        for (CaixaCNAB240StatusEnum statusEnum : values) {
            if (statusEnum.getId().equals(id)) {
                return statusEnum;
            }
        }
        return CaixaCNAB240StatusEnum.StatusNENHUM;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}

package servicos.impl.dcc.rede;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;

/**
 * Created by <PERSON><PERSON>
 */
public enum TabelaAjusteRedeEnum {

    Ajuste1("1", "PACOTE E-REDE"),
    <PERSON><PERSON><PERSON><PERSON>("2", "CONSULTA DE CHEQUES"),
    <PERSON><PERSON>ste<PERSON>("3", "DEBITO PARCELADO"),
    <PERSON><PERSON><PERSON><PERSON>("4", "DEBITO TX TRIBUTO"),
    <PERSON><PERSON><PERSON><PERSON>("5", "TX MAN DO TEF"),
    <PERSON><PERSON><PERSON><PERSON>("6", "POS-INATIV/CONEC/PIN"),
    <PERSON><PERSON><PERSON><PERSON>("7", "CREDENC/ADESAO"),
    <PERSON><PERSON><PERSON><PERSON>("8", "CANCEL VENDAS HIPERC"),
    <PERSON><PERSON><PERSON><PERSON>("9", "CANCEL/CHBK MAESTRO"),
    A<PERSON><PERSON>10("10", "CANCELAMENTOS POR DISPUTAS"),
    <PERSON><PERSON><PERSON><PERSON>("11", "MENS.SECURECODE"),
    <PERSON><PERSON><PERSON><PERSON>("12", "MENSALIDADE HIPERCARD"),
    <PERSON><PERSON><PERSON><PERSON>("13", "MAQUININHA NO CONTA CERTA"),
    <PERSON><PERSON><PERSON>14("14", "<PERSON><PERSON>IFA DEBITO"),
    Ajuste15("15", "CBK CARTAO CHIP"),
    Ajuste16("16", "ESTORNO CR.INDEV.CI"),
    Ajuste17("17", "INDENIZA POS PERDIDO"),
    Ajuste18("18", "CANCEL.DE VENDAS"),
    Ajuste19("19", "SEGUNDA VIA EXTRATO"),
    Ajuste20("20", "POS-INATIV/CONEC/PIN"),
    Ajuste21("21", "CANCELAMENTO MAESTRO"),
    Ajuste22("22", "CONTESTAÇAO DE VENDA"),
    Ajuste23("23", "CONTESTAÇAO DE VENDA"),
    Ajuste24("24", "TRF AD EXCESSO CBACK"),
    Ajuste25("25", "PACOTE DE SERVICOS"),
    Ajuste26("26", "PACOTE SERV EXCEDENT"),
    Ajuste27("27", "COF PERFORMACE"),
    Ajuste28("28", "AL.POS/PINPAD/TX CONECT"),
    Ajuste29("29", "DEBITO RECARGA"),
    Ajuste30("30", "CANCEL DESP DOLAR"),
    Ajuste31("31", "ASSISTENCIA REDE"),
    Ajuste32("32", "CANCEL VENDA DEBITO"),
    Ajuste33("33", "PACOTE GATEWAY"),
    Ajuste34("34", "MODELO TARIFARIO"),
    Ajuste35("35", "CONSULTA AVS"),
    Ajuste36("36", "DEVOLUÇAO CV"),
    Ajuste37("37", "ESTORNO CR.INDEV."),
    Ajuste38("38", "COB RETROAT TX ADM"),
    Ajuste39("39", "CONTEST VENDAS HIPER"),
    Ajuste40("40", "TARIFA EXT. MENSAL"),
    Ajuste41("41", "MANUAL REVIEW"),
    Ajuste42("42", "MONITORING"),
    Ajuste43("43", "BOLETO BANCARIO"),
    Ajuste44("44", "MENSALIDADE CONTROL REDE"),
    Ajuste45("45", "RETROATIVO CONTROL REDE"),
    Ajuste46("46", "TAXA PARC. ESPECIAL"),
    Ajuste48("48", "AL.POS/PINPAD/TX CONECT"),
    Ajuste49("49", "POS-INATIV/CONEC/PIN"),
    Ajuste50("50", "DISPONIVEL"),
    Ajuste51("51", "CRED SECURECODE"),
    Ajuste52("52", "REVERSAO DEBITO CBK"),
    Ajuste53("53", "CREDITO RECARGA"),
    Ajuste54("54", "TOT.LIQID.A MENOR"),
    Ajuste55("55", "CV N CONSIDERADO RV"),
    Ajuste56("56", "PREMIO PROMOÇAO PARCELADO"),
    Ajuste57("57", "PAGTO DESAGIO"),
    Ajuste58("58", "CREDITO ALUGUEL"),
    Ajuste59("59", "REBATE MENSAL"),
    Ajuste60("60", "REBATE FINAL"),
    Ajuste61("61", "DEV CRED PGTO MAIOR"),
    Ajuste62("62", "REGULARIZAÇAO DIF. TAXA"),
    Ajuste63("63", "REGUL.DB ANTERIOR"),
    Ajuste64("64", "PGTO DE RV"),
    Ajuste65("65", "DEV MENSAL HIPERCARD"),
    Ajuste66("66", "PGT.JURO CORREÇAO"),
    Ajuste67("67", "COMISSAO DE RECARGA"),
    Ajuste68("68", "BONUS CELULAR REDE"),
    Ajuste69("69", "PGTO VIAÇAO AEREA"),
    Ajuste70("70", "ESTORNO PREÇO UNICO"),
    Ajuste71("71", "PGT.N S/J-1A PARC"),
    Ajuste72("72", "PGT.N S/J-2A PARC"),
    Ajuste73("73", "PGT.N S/J-3A PARC"),
    Ajuste74("74", "PGT.N S/J-4A PARC"),
    Ajuste75("75", "PGT.N S/J-5A PARC"),
    Ajuste76("76", "PAGTO MANUAL"),
    Ajuste77("77", "PGT.N S/J-7A PARC"),
    Ajuste78("78", "PGT.N S/J-8A PARC"),
    Ajuste79("79", "PGT.N S/J-9A PARC"),
    Ajuste80("80", "CAPTURA CV OFF-TO"),
    Ajuste81("81", "PCTE TUR.CREDITO"),
    Ajuste82("82", "COMISS.SOBRE VENDA"),
    Ajuste83("83", "IR S/COMISSAO"),
    Ajuste84("84", "ADIC.IR ESTAD."),
    Ajuste85("85", "PGTO DE RV"),
    Ajuste86("86", "ANTECIPAÇAO DOLAR"),
    Ajuste87("87", "DEVOL.CANCEL.DOLAR"),
    Ajuste88("88", "TIER PRICING"),
    Ajuste89("89", "EST CONS.SERASA/AVS"),
    Ajuste90("90", "ESTOR.TARIFA ENV.DOC"),
    Ajuste91("91", "ESTORNO POS PERDIDO"),
    Ajuste92("92", "ESTOR.TAR EXT MENSAL"),
    Ajuste93("93", "EST.EXT SALDOS ABER."),
    Ajuste94("94", "ESTOR.TAXA DE ADESAO"),
    Ajuste95("95", "EST AL.POS/TX.CONET"),
    Ajuste96("96", "ESTOR.TAR 2ª VIA EXT"),
    Ajuste97("97", "ESTORNO DE DEBITO"),
    Ajuste98("98", "OFERTA REDE"),
    Ajuste99("99", "DEBITO NAO ACATADO");

    private String id;
    private String descricao;

    private TabelaAjusteRedeEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TabelaAjusteRedeEnum valueOff(String id) {
        TabelaAjusteRedeEnum[] values = TabelaAjusteRedeEnum.values();
        for (TabelaAjusteRedeEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public static String retornarIDs() {
        StringBuilder codigos = new StringBuilder();
        for (TabelaAjusteRedeEnum eDIStatusEnum : values()) {
            if (codigos.toString().equals("")) {
                codigos.append("'").append(eDIStatusEnum.getId()).append("'");
            } else {
                codigos.append(",").append("'").append(eDIStatusEnum.getId()).append("'");
            }
        }
        return codigos.toString();
    }
}

package servicos.impl.dcc.rede;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;

/**
 * Created by <PERSON><PERSON>
 */
public enum TipoArquivoRedeEnum {

    EEVD("EEVD", "Extrato Eletrônico de Vendas Débito", TipoConciliacaoEnum.VENDAS, true),
    EEVC("EEVC", "Extrato Eletrônico de Vendas Crédito", TipoConciliacaoEnum.VENDAS, false),
    EEFI("EEFI", "Extrato Eletrônico Financeiro", TipoConciliacaoEnum.PAGAMENTOS, false),
    API_REDE("API_REDE", "Dados da API de Conciliação da Rede", TipoConciliacaoEnum.PAGAMENTOS, false);


    private String id;
    private String descricao;
    private TipoConciliacaoEnum tipoConciliacaoEnum;
    private boolean separadoPorVirgula;

    private TipoArquivoRedeEnum(String id, String descricao, TipoConciliacaoEnum tipoConciliacaoEnum, boolean separadoPorVirgula) {
        this.id = id;
        this.descricao = descricao;
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
        this.separadoPorVirgula = separadoPorVirgula;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoConciliacaoEnum getTipoConciliacaoEnum() {
        return tipoConciliacaoEnum;
    }

    public void setTipoConciliacaoEnum(TipoConciliacaoEnum tipoConciliacaoEnum) {
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
    }

    public static TipoArquivoRedeEnum valueOff(String id) {
        TipoArquivoRedeEnum[] values = TipoArquivoRedeEnum.values();
        for (TipoArquivoRedeEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public static boolean extratoBIN(String id) {
        TipoArquivoRedeEnum[] values = TipoArquivoRedeEnum.values();
        for (TipoArquivoRedeEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }


    public static String retornarIDs(){
        StringBuilder codigos = new StringBuilder();
        for (TipoArquivoRedeEnum eDIStatusEnum : values()) {
            if (codigos.toString().equals("")){
                codigos.append("'").append(eDIStatusEnum.getId()).append("'");
            }else{
                codigos.append(",").append("'").append(eDIStatusEnum.getId()).append("'");
            }
        }
        return codigos.toString();
    }

    public boolean isSeparadoPorVirgula() {
        return separadoPorVirgula;
    }

    public void setSeparadoPorVirgula(boolean separadoPorVirgula) {
        this.separadoPorVirgula = separadoPorVirgula;
    }
}

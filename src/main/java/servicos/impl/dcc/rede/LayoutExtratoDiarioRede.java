/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.rede;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */


//        EEVC - Extrato Eletrônico de Vendas Crédito
//        EEVD - Extrato Eletrônico de Vendas Débito
//        EEFI - Extrato Eletrônico Financeiro
//        EESA - Extrato Eletrônico de Saldos em Aberto

//        Nomenclaturas:

//        RV - (Resumo de Vendas)
//        CV - (Comprovante de Vendas)
//        NSU - (Número Sequencial Único)
//        RAV - (Resumo Antecipado de Vendas)
//        AVS - (Address Verification System)
//        TO - (Transmissão Off-line)
//        OC - (Ordem de Crédito)


public class LayoutExtratoDiarioRede {

    public static void lerRetorno(RemessaVO remessa) throws Exception {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<>();

            TipoArquivoRedeEnum tipoArquivoRedeEnum = null;

            while ((linha = br.readLine()) != null) {

                if (tipoArquivoRedeEnum == null) {
                    for (TipoArquivoRedeEnum tipo : TipoArquivoRedeEnum.values()) {
                        if (linha.contains(tipo.getId().toUpperCase())) {
                            tipoArquivoRedeEnum = tipo;
                            break;
                        }
                    }
                }

                if (tipoArquivoRedeEnum == null) {
                    throw new Exception("TipoArquivoRedeEnum null");
                }

                //Header do arquivo
                if ((linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro00.getId()) && tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEVD)) ||
                        (linha.startsWith(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro030.getId()) && tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEFI)) ||
                        (linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro002.getId()) && tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEVC))) {

                    lerAtributos(linha, header, tipoArquivoRedeEnum);

                    header.put(DCCAttEnum.TipoArquivo, tipoArquivoRedeEnum.getId());
                    header.put(DCCAttEnum.OpcaoExtrato, tipoArquivoRedeEnum.getTipoConciliacaoEnum().getCodigo());

                    if (tipoArquivoRedeEnum.isSeparadoPorVirgula()) {

                        String dataGeracao = header.getValue(DCCAttEnum.DataGeracao.name());
                        String sequencialRegistro = header.getValue(DCCAttEnum.SequencialRegistro.name());

                        try {
                            Date dataGeracaoDate = Uteis.getDate(dataGeracao, "ddMMyyyy");
                            dataGeracaoDate = Uteis.somarCampoData(dataGeracaoDate, Calendar.MINUTE, Integer.parseInt(sequencialRegistro));
                            remessa.setDataRegistro(dataGeracaoDate);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    } else {

                        String dataEmissao = header.getValue(DCCAttEnum.DataEmissao.name());
                        String sequenciaMovimento = header.getValue(DCCAttEnum.SequenciaMovimento.name());

                        try {
                            Date dataEmissaoDate = Uteis.getDate(dataEmissao, "ddMMyyyy");
                            dataEmissaoDate = Uteis.somarCampoData(dataEmissaoDate, Calendar.MINUTE, Integer.parseInt(sequenciaMovimento));
                            remessa.setDataRegistro(dataEmissaoDate);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }


                    //Detalhe
                } else if ((tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEVD) &&
                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro05.getId()+ ",")) ||
                        (tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEFI) && (
                                linha.startsWith(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getId()))) ||
                        (tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEVC) && (
                                linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro005.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getId()) ||
                                        linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getId())))) {

                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

                    lerAtributos(linha, detail, tipoArquivoRedeEnum);

                    detail.put(DCCAttEnum.TipoArquivo, tipoArquivoRedeEnum.getId());
                    detail.put(DCCAttEnum.OpcaoExtrato, tipoArquivoRedeEnum.getTipoConciliacaoEnum().getCodigo());

                    listaDetalheRetorno.add(detail);

                    //Trailer do arquivo
                } else if ((linha.startsWith(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro028.getId()) && tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEVC))) {

                    lerAtributos(linha, trailer, tipoArquivoRedeEnum);

                    trailer.put(DCCAttEnum.TipoArquivo, tipoArquivoRedeEnum.getId());
                    trailer.put(DCCAttEnum.OpcaoExtrato, tipoArquivoRedeEnum.getTipoConciliacaoEnum().getCodigo());
                }
            }
            remessa.setHeaderRetorno(header);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(trailer);
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r, TipoArquivoRedeEnum tipoArquivoRedeEnum) {
        if (tipoArquivoRedeEnum.isSeparadoPorVirgula()) {
            lerAtributosVirgula(linha, r, tipoArquivoRedeEnum);
        } else {
            lerAtributosNormal(linha, r, tipoArquivoRedeEnum);
        }
    }

    private static void lerAtributosVirgula(final String linha, RegistroRemessa r, TipoArquivoRedeEnum tipoArquivoRedeEnum) {

        if (r.getTipo() == TipoRegistroEnum.HEADER) {

            String[] dadosHeader = linha.split(",");

            //  1ª- 002 Num. Tipo de registro
            r.put(DCCAttEnum.TipoRegistro, dadosHeader[0]);
            //  2ª- 009 Num. Nº- de filiação da matriz ou grupo comercial
            r.put(DCCAttEnum.NumeroEstabelecimento, dadosHeader[1]);
            //  3ª- 008 Num. Data de emissão (DDMMAAAA)
            r.put(DCCAttEnum.DataEmissao, dadosHeader[2]);
            //  4ª- 008 Num. Data de movimento (DDMMAAAA)
            r.put(DCCAttEnum.DataGeracao, dadosHeader[3]);
            //  5ª- 039 Alfa ?Movimentação diária ? Cartões de débito?
            r.put(DCCAttEnum.TipoOperacao, dadosHeader[4]);
            //  6ª- 008 Alfa ?Rede"
            r.put(DCCAttEnum.Descricao, dadosHeader[5]);
            //  7ª- 026 Alfa Nome comercial do estabelecimento
            r.put(DCCAttEnum.NomeEmpresa, dadosHeader[6]);
            //  8ª- 006 Num. Sequência de movimento
            r.put(DCCAttEnum.SequencialRegistro, dadosHeader[7]);
            //  9ª- 015 Alfa Tipo de processamento
            //(diário/reprocessamento)
            r.put(DCCAttEnum.TipoProcessamento, dadosHeader[8]);
            //  10ª- 020 Alfa Versão do arquivo (V1.04 ? 07/10 ? EEVD)
            r.put(DCCAttEnum.VersaoLayout, dadosHeader[9]);

        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {

            String[] dadosDetalhe = linha.split(",");

            if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro01.getId())) { //OK

//                1ª- 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. Nº de filiação do ponto de venda
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 008 Num. Data de crédito (será demonstrado?zero? quando pré-datado)
                r.put(DCCAttEnum.DataCredito, dadosDetalhe[2]);
//                4ª- 008 Num. Data do resumo de vendas
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[3]);
//                5ª- 009 Num. Nº do resumo de vendas
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[4]);
//                6ª- 006 Num. Quantidade de comprovantes de vendas
                r.put(DCCAttEnum.QuantidadeVendasDebito, dadosDetalhe[5]);
//                7ª- 015 9(13)V99 Valor bruto (para o compre e saque, este campo será composto pelo ?Valor da Compra?+?Valor do Saque?)
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[6]);
//                8ª- 015 9(13)V99 Valor desconto
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[7]);
//                9ª- 015 9(13)V99 Valor líquido
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[8]);
//                10ª- 001 Alfa Tipo de resumo (D ? Débito à vista/ P ? Pré-datado)
                r.put(DCCAttEnum.TipoResumo, dadosDetalhe[9]);
//                11ª- 003 Num. Banco
                r.put(DCCAttEnum.Banco, dadosDetalhe[10]);
//                12ª- 006 Num. Agência
                r.put(DCCAttEnum.Agencia, dadosDetalhe[11]);
//                13ª- 011 Num. Conta-corrente
                r.put(DCCAttEnum.ContaCorrente, dadosDetalhe[12]);
//                14ª- 001 Alfa Bandeira
                r.put(DCCAttEnum.Bandeira, dadosDetalhe[13]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro02.getId())) { //OK

//                1ª- 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. Nº de filiação da matriz
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 003 Num. Quantidade de resumos de vendas acatados
                r.put(DCCAttEnum.QtdConfirmados, dadosDetalhe[2]);
//                4ª- 006 Num. Quantidade de comprovantes de vendas
                r.put(DCCAttEnum.QtdLotes, dadosDetalhe[3]);
//                5ª- 015 9(13)V99 Total bruto
                r.put(DCCAttEnum.ValorTotalBruto, dadosDetalhe[4]);
//                6ª- 015 9(13)V99 Total desconto
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[5]);
//                7ª- 015 9(13)V99 Total líquido
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[6]);
//                8ª- 015 9(13)V99 Valor bruto pré-datado
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[7]);
//                9ª- 015 9(13)V99 Desconto pré-datado
                r.put(DCCAttEnum.DescontoDia, dadosDetalhe[8]);
//                10ª- 015 9(13)V99 Líquido pré-datado
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[9]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro03.getId())) { //OK

//                1ª- 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. Nº de filiação da matriz
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 003 Num. Quantidade de resumos de vendas acatados
                r.put(DCCAttEnum.QtdVendasAcatados, dadosDetalhe[2]);
//                4ª- 006 Num. Quantidade de comprovantes de vendas
                r.put(DCCAttEnum.QtdComprovantesVendas, dadosDetalhe[3]);
//                5ª- 015 9(13)V99 Total bruto
                r.put(DCCAttEnum.ValorTotalBruto, dadosDetalhe[4]);
//                6ª- 015 9(13)V99 Total desconto
                r.put(DCCAttEnum.ValorTotalDescontos, dadosDetalhe[5]);
//                7ª- 015 9(13)V99 Total líquido
                r.put(DCCAttEnum.ValorTotalLiquido, dadosDetalhe[6]);
//                8ª- 015 9(13)V99 Valor bruto pré-datado
                r.put(DCCAttEnum.ValorBrutoPreDatado, dadosDetalhe[7]);
//                9ª- 015 9(13)V99 Desconto pré-datado
                r.put(DCCAttEnum.ValorDescontoPreDatado, dadosDetalhe[8]);
//                10ª- 015 9(13)V99 Líquido pré-datado
                r.put(DCCAttEnum.ValorLiquidoPreDatado, dadosDetalhe[9]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro04.getId())) { //OK

//                1ª- 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. Nº de filiação da matriz ou grupo comercial
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 006 Num. Quantidade de resumos de vendas
                r.put(DCCAttEnum.QtdVendas, dadosDetalhe[2]);
//                4ª- 006 Num. Quantidade de comprovantes de vendas
                r.put(DCCAttEnum.QtdComprovantesVendas, dadosDetalhe[3]);
//                5ª- 015 9(13)V99 Total bruto
                r.put(DCCAttEnum.ValorTotalBruto, dadosDetalhe[4]);
//                6ª- 015 9(13)V99 Total desconto
                r.put(DCCAttEnum.ValorTotalDescontos, dadosDetalhe[5]);
//                7ª- 015 9(13)V99 Total líquido
                r.put(DCCAttEnum.ValorTotalLiquido, dadosDetalhe[6]);
//                8ª- 015 9(13)V99 Valor bruto pré-datado
                r.put(DCCAttEnum.ValorBrutoPreDatado, dadosDetalhe[7]);
//                9ª- 015 9(13)V99 Desconto pré-datado
                r.put(DCCAttEnum.ValorDescontoPreDatado, dadosDetalhe[8]);
//                10ª- 015 9(13)V99 Líquido pré-datado
                r.put(DCCAttEnum.ValorLiquidoPreDatado, dadosDetalhe[9]);
//                11ª- 006 Num. Total de registros no arquivo
                r.put(DCCAttEnum.QtdRegistros, dadosDetalhe[10]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro05.getId())) { //OK

//                1ª- 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. N.º de filiação do ponto de venda
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 009 Num. N.º do resumo de Vendas
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[2]);
//                4ª- 008 Num. Data do CV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[3]);
//                5ª- 015 9(13)V99 Valor bruto (para o compre e saque, este campo será composto pelo?Valor da Compra?+?Valor do Saque?)
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[4]);
//                6ª- 015 9(13)V99 Valor desconto
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[5]);
//                7ª- 015 9(13)V99 Valor líquido
                r.put(DCCAttEnum.ValorLiquido, dadosDetalhe[6]);
//                8ª- 019 Alfa Número do cartão
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[7]);
//                9ª- 001 Alfa Tipo de transação
                r.put(DCCAttEnum.TipoTransacao, dadosDetalhe[8]);
//                10ª- 012 Num. Número do CV
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[9]);
//                11ª- 008 Num. Data do crédito (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, dadosDetalhe[10]);
//                12ª- 002 Num. Status da transação (01 ? acatada)
                r.put(DCCAttEnum.StatusTransacao, dadosDetalhe[11]);
//                13ª- 006 Num. Hora da transação (HHMMSS)
                r.put(DCCAttEnum.HoraTransacao, dadosDetalhe[12]);
//                14ª- 008 Alfa Número do terminal
                r.put(DCCAttEnum.NumeroTerminal, dadosDetalhe[13]);
//                15ª- 002 Num. Tipo de captura
                r.put(DCCAttEnum.TipoCaptura, dadosDetalhe[14]);
//                16ª- 005 Num. Reservado
                r.put(DCCAttEnum.ReservadoBanco, dadosDetalhe[15]);
//                17ª- 015 9(13)V99 Valor da compra (para o compre e saque )
                r.put(DCCAttEnum.ValorCompra, dadosDetalhe[16]);
//                18ª- 015 9(13)V99 Valor do saque (para o compre e saque)
                r.put(DCCAttEnum.ValorSaque, dadosDetalhe[17]);
//                19ª- 001 Alfa Bandeira
                r.put(DCCAttEnum.Bandeira, dadosDetalhe[18]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro13.getId())) { //OK

//                1ª- 2 Num Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 9 Num Número de filiação do Ponto de Vendas
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 9 Num Número do Resumo de Vendas
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[2]);
//                4ª- 8 Num Data do CV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[3]);
//                5ª- 15 9(13)V99 Valor Bruto (para o Compre e Saque, este campo será Composto pelo?Valor da Compra?+?Valor do Saque?)
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[4]);
//                6ª- 19 Alfa Número do Cartão
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[5]);
//                7ª- 12 Num Número do CV
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[6]);
//                8ª- 20 Alfa TID
                r.put(DCCAttEnum.TID, dadosDetalhe[7]);
//                9ª- 30 Alfa Número Pedido
                r.put(DCCAttEnum.NumeroPedido, dadosDetalhe[8]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro20.getId())) { //OK

//                1ª 2 Num Tipo de registro (?20?)
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª 9 Num Número de filiação da matriz ou grupo comercial
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª 9 Num Número do resumo de vendas
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[2]);
//                4ª 8 Alfa Data do CV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[3]);
//                5ª 15 9(15)v99 Valor da Recarga
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[4]);
//                6ª 12 Num Número do Comprovantes
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[5]);
//                7ª 15 Num Número do telefone (DDDXXXXX9999)
                r.put(DCCAttEnum.NumeroTelefone, dadosDetalhe[6]);
//                8ª 1 Num Bandeira
                r.put(DCCAttEnum.Bandeira, dadosDetalhe[7]);
//                9ª 7 Num Código de autorização
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[8]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro08.getId())) { //OK

//                1ª- 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. N.º de filiação do ponto de venda
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 009 Num. N.º do resumo de vendas
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[2]);
//                4ª- 008 Num. Data do CV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, dadosDetalhe[3]);
//                5ª- 012 Num. Número do CV (NSU)
                r.put(DCCAttEnum.NumeroComprovanteVenda, dadosDetalhe[4]);
//                6ª- 015 9(13)V99 Valor bruto do CV
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[5]);
//                7ª- 015 9(13)V99 Valor do cancelamento
                r.put(DCCAttEnum.ValorCancelamento, dadosDetalhe[6]);
//                8ª- 002 Num. Motivo do cancelamento
//                        (00 ? Cancelamento /01 ? Chargeback)
                r.put(DCCAttEnum.Motivo, dadosDetalhe[7]);
//                9ª- 008 Num. Data do crédito (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, dadosDetalhe[8]);
//                10ª- 015 9(13)V99 Novo valor do crédito
                r.put(DCCAttEnum.NovoValorCredito, dadosDetalhe[9]);
//                11ª- 001 Alfa
//                Tipo de transação
//                V = Cartão de Débito à vista (RedeShop/Maestro)
//                C = CDC
//                T = Trishop
//                S = Construcard
//                E = VisaElectron
//                B = Cabal
//                O = VisaElectron pré-datado
                r.put(DCCAttEnum.TipoTransacao, dadosDetalhe[10]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro09.getId())) { //OK

//                1ª 002 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª 009 Num. N.º de filiação do ponto de venda
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª 008 Num. Data da liquidação (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, dadosDetalhe[2]);
//                4ª 015 9(13)V99 Valor liquidado
                r.put(DCCAttEnum.ValorLiquidado, dadosDetalhe[3]);
//                5ª 012 Num. Número do NSU da transação
                r.put(DCCAttEnum.NSU, dadosDetalhe[4]);
//                6ª 008 Num. Data da transação (DDMMAAAA)
                r.put(DCCAttEnum.DataTransacao, dadosDetalhe[5]);
//                7ª 008 Num. Data do vencimento (DDMMAAAA)
                r.put(DCCAttEnum.DataVencimento, dadosDetalhe[6]);
//                8ª 015 9(13)V99 Valor desconto
                r.put(DCCAttEnum.ValorDesconto, dadosDetalhe[7]);
//                9ª 015 9(13)V99 Valor bruto
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[8]);
//                10ª 001 Alfa Bandeira
                r.put(DCCAttEnum.Bandeira, dadosDetalhe[9]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro11.getId())) { //OK

//                1ª- 003 Num. Tipo de registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 009 Num. Número do PV ajustado
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosDetalhe[1]);
//                3ª- 009 Num. Número do RV ajustado
                r.put(DCCAttEnum.NumeroResumoVenda, dadosDetalhe[2]);
//                4ª- 008 Num. Data do ajuste (DDMMAAAA)
                r.put(DCCAttEnum.DataAjuste, dadosDetalhe[3]);
//                5ª- 015 9(13)V99 Valor do ajuste
                r.put(DCCAttEnum.ValorAjuste, dadosDetalhe[4]);
//                6ª- 001 Alfa D (Débito)
                r.put(DCCAttEnum.Debito, dadosDetalhe[5]);
//                7ª- 003 Num. Motivo do ajuste (cód. da tabela de ajustes)
                r.put(DCCAttEnum.CodigoMotivo, dadosDetalhe[6]);
//                8ª- 028 Alfa Motivo do ajuste (string da tabela de ajustes)
                r.put(DCCAttEnum.Motivo, dadosDetalhe[7]);
//                9ª- 016 Num. Número do cartão
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[8]);
//                10ª- 008 Num. Data da transação?CV?
                r.put(DCCAttEnum.DataTransacao, dadosDetalhe[9]);
//                11ª- 009 Num. Número do RV original
                r.put(DCCAttEnum.NumeroResumoVendaOriginal, dadosDetalhe[10]);
//                12ª- 015 Alfa Número de referência da carta/fax
                r.put(DCCAttEnum.NumeroReferencia, dadosDetalhe[11]);
//                13ª- 008 Num. Data da carta
                r.put(DCCAttEnum.DataCarta, dadosDetalhe[12]);
//                14ª- 006 Num. Mês de referência (serviços, POS etc.)
                r.put(DCCAttEnum.MesReferencia, dadosDetalhe[13]);
//                15ª- 009 Num. Nº PV original
                r.put(DCCAttEnum.NumeroPontoVendaOriginal, dadosDetalhe[14]);
//                16ª- 008 Alfa Data RV original
                r.put(DCCAttEnum.DataResumoVendaOriginal, dadosDetalhe[15]);
//                17ª- 015 9(13)V99 Valor da transação
                r.put(DCCAttEnum.ValorCompra, dadosDetalhe[16]);
//                18ª- 001 Alfa N (Net)
                r.put(DCCAttEnum.OrigemAjuste, dadosDetalhe[17]);
//                19ª- 008 Num. Data do crédito
                r.put(DCCAttEnum.DataCredito, dadosDetalhe[18]);
//                20ª- 015 9(13)V99 Valor bruto do resumo de vendas original
                r.put(DCCAttEnum.ValorBruto, dadosDetalhe[19]);
//                21ª- 015 9(13)V99 Valor do cancelamento solicitado
                r.put(DCCAttEnum.ValorCancelamento, dadosDetalhe[20]);
//                22ª- 012 Num. Número do NSU (motivos 16, 18 e 23)
                r.put(DCCAttEnum.NSU, dadosDetalhe[21]);
//                23ª- 006 Alfa Número da autorização
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[22]);
//                24ª- 001 Alfa Tipo de débito
                r.put(DCCAttEnum.TipoDebito, dadosDetalhe[23]);
//                25ª- 011 Num. Número da ordem de débito
                r.put(DCCAttEnum.NumeroOrdemDebito, dadosDetalhe[24]);
//                26ª- 015 9(13)V99 Valor do débito total
                r.put(DCCAttEnum.ValorDebito, dadosDetalhe[25]);
//                27ª- 015 9(13)V99 Valor pendente
                r.put(DCCAttEnum.ValorPendente, dadosDetalhe[26]);
//                28ª- 001 Alfa Bandeira do RV de origem
                r.put(DCCAttEnum.Bandeira, dadosDetalhe[27]);
//                29ª- 001 Alfa Bandeira do RV ajustado
                r.put(DCCAttEnum.BandeiraAjustado, dadosDetalhe[28]);

            } else if (dadosDetalhe[0].equals(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro17.getId())) { //OK

//                1ª- 2 Num. Tipo de Registro
                r.put(DCCAttEnum.TipoRegistro, dadosDetalhe[0]);
//                2ª- 19 Num. Número do Cartão
                r.put(DCCAttEnum.NumeroCartao, dadosDetalhe[1]);
//                3ª- 8 Num. Data da Transação?CV?(DDMMAAAA)
                r.put(DCCAttEnum.DataTransacao, dadosDetalhe[2]);
//                4ª- 9 Num. Número do RV original
                r.put(DCCAttEnum.NumeroResumoVendaOriginal, dadosDetalhe[3]);
//                5ª- 9 Num. Nº PV Original
                r.put(DCCAttEnum.NumeroPontoVendaOriginal, dadosDetalhe[4]);
//                6ª- 8 Alfa Data RV Original (DDMMAAAA)
                r.put(DCCAttEnum.DataResumoVendaOriginal, dadosDetalhe[5]);
//                7ª- 15 9(13)V99 Valor da transação
                r.put(DCCAttEnum.ValorTransacao, dadosDetalhe[6]);
//                8ª- 12 Num. Número do NSU (motivos 16, 18 e 23)
                r.put(DCCAttEnum.NSU, dadosDetalhe[7]);
//                9ª- 6 Alfa Número da autorização
                r.put(DCCAttEnum.CodigoAutorizacao, dadosDetalhe[8]);
//                10ª- 20 Alfa TID
                r.put(DCCAttEnum.TID, dadosDetalhe[9]);
//                11ª- 30 Alfa Número do Pedido
                r.put(DCCAttEnum.NumeroPedido, dadosDetalhe[10]);

            }

        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {

            String[] dadosTrailer = linha.split(",");

            if (dadosTrailer.length <= 17) {

                r.put(DCCAttEnum.TipoRegistro, dadosTrailer[0]);
                r.put(DCCAttEnum.DataGeracao, dadosTrailer[1]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosTrailer[2]);
                r.put(DCCAttEnum.QuantidadeMatriz, dadosTrailer[3]);
                r.put(DCCAttEnum.QuantidadeVendasDebito, dadosTrailer[4]);
                r.put(DCCAttEnum.ValorBrutoVendasDebito, dadosTrailer[5]);
                r.put(DCCAttEnum.TotalTaxasVendasDebito, dadosTrailer[6]);
                r.put(DCCAttEnum.ValorLiquidoVendasDebito, dadosTrailer[7]);
                r.put(DCCAttEnum.QuantidadeVendasCredito, dadosTrailer[8]);
                r.put(DCCAttEnum.ValorBrutoVendasCredito, dadosTrailer[9]);
                r.put(DCCAttEnum.TotalTaxasVendasCredito, dadosTrailer[10]);
                r.put(DCCAttEnum.ValorLiquidoVendasCredito, dadosTrailer[11]);
                r.put(DCCAttEnum.QuantidadeVendasCreditoParcelado, dadosTrailer[12]);
                r.put(DCCAttEnum.ValorBrutoVendasCreditoParcelado, dadosTrailer[13]);
                r.put(DCCAttEnum.TotalTaxasVendasCreditoParcelado, dadosTrailer[14]);
                r.put(DCCAttEnum.ValorLiquidoVendasCreditoParcelado, dadosTrailer[15]);
                r.put(DCCAttEnum.QuantidadeRegistros, dadosTrailer[16]);

            } else {

                r.put(DCCAttEnum.TipoRegistro, dadosTrailer[0]);
                r.put(DCCAttEnum.DataGeracao, dadosTrailer[1]);
                r.put(DCCAttEnum.NumeroEstabelecimento, dadosTrailer[2]);
                r.put(DCCAttEnum.QuantidadeMatriz, dadosTrailer[3]);
                r.put(DCCAttEnum.QuantidadeVendasDebito, dadosTrailer[4]);
                r.put(DCCAttEnum.ValorBrutoVendasDebito, dadosTrailer[5]);
                r.put(DCCAttEnum.TotalTaxasVendasDebito, dadosTrailer[6]);
                r.put(DCCAttEnum.ValorLiquidoVendasDebito, dadosTrailer[7]);
                r.put(DCCAttEnum.QuantidadeVendasCredito, dadosTrailer[8]);
                r.put(DCCAttEnum.ValorBrutoVendasCredito, dadosTrailer[9]);
                r.put(DCCAttEnum.TotalTaxasVendasCredito, dadosTrailer[10]);
                r.put(DCCAttEnum.ValorLiquidoVendasCredito, dadosTrailer[11]);
                r.put(DCCAttEnum.QuantidadeVendasCreditoParcelado, dadosTrailer[12]);
                r.put(DCCAttEnum.ValorBrutoVendasCreditoParcelado, dadosTrailer[13]);
                r.put(DCCAttEnum.TotalTaxasVendasCreditoParcelado, dadosTrailer[14]);
                r.put(DCCAttEnum.ValorLiquidoVendasCreditoParcelado, dadosTrailer[15]);
                r.put(DCCAttEnum.QuantidadeRegistros, dadosTrailer[16]);

            }
        }
    }

    private static void lerAtributosNormal(final String linha, RegistroRemessa r, TipoArquivoRedeEnum tipoArquivoRedeEnum) {

        if (r.getTipo() == TipoRegistroEnum.HEADER) {

            if (tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEVC)) {

//            *********** Num. Tipo de registro (?004?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//            *********** Num. Tipo de registro (?002?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//            *********** Num. Data de emissão (DDMMAAAA)
                r.put(DCCAttEnum.DataEmissao, StringUtilities.readString(3, 11, linha));
//            *********** Alfa ?Rede?
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(11, 19, linha));
//            020 049 030 Alfa ?Extrato Eletrônico de Vendas?
                r.put(DCCAttEnum.ReservadoBanco2, StringUtilities.readString(19, 49, linha));
//            050 071 022 Alfa Nome comercial (grupo/matriz)
                r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(49, 71, linha));
//            072 077 006 Num. Sequência do movimento
                r.put(DCCAttEnum.SequenciaMovimento, StringUtilities.readString(71, 77, linha));
//            078 086 009 Num. Nº PV grupo ou matriz
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(77, 86, linha));
//            087 101 015 Alfa Tipo de movimento
                r.put(DCCAttEnum.TipoMovimento, StringUtilities.readString(86, 101, linha));
//            102 121 020 Alfa Versão do arquivo
                r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(101, 121, linha));
//            122 1024 903 Alfa Livre
//            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(121, 1023, linha));


            } else if (tipoArquivoRedeEnum.equals(TipoArquivoRedeEnum.EEFI)) {

//                *********** Num. Tipo de registro (?030?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Data de emissão (DDMMAAAA)
                r.put(DCCAttEnum.DataEmissao, StringUtilities.readString(3, 11, linha));
//                *********** Alfa ?Rede?
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(11, 19, linha));
//                020 053 034 Alfa ?Extrato de movimentação financeira?
                r.put(DCCAttEnum.ReservadoBanco2, StringUtilities.readString(19, 53, linha));
//                054 075 022 Alfa Nome comercial (grupo/matriz)
                r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(53, 75, linha));
//                076 081 006 Num. Sequência do movimento
                r.put(DCCAttEnum.SequenciaMovimento, StringUtilities.readString(75, 81, linha));
//                082 090 009 Num. Nº- PV grupo ou matriz
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(81, 90, linha));
//                091 105 015 Alfa Tipo de processamento
                r.put(DCCAttEnum.TipoProcessamento, StringUtilities.readString(90, 105, linha));
//                106 125 020 Alfa Versão do arquivo
                r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(105, 125, linha));

            }


        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {

            String tipoRegistro = StringUtilities.readString(0, 3, linha);

            if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro004.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro004.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?004?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Alfa Nº- PV matriz
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 034 022 Alfa Nome comercial da matriz
                r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(11, 33, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro005.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro005.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?005?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 037 016 Alfa Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(21, 37, linha));
//                038 052 015 9(13)V99 Valor da transação ?CV/NSU?
                r.put(DCCAttEnum.ValorTransacao, StringUtilities.readString(37, 52, linha));
//                053 060 008 Num. Data da transação ?CV/NSU? (DDMMAAAA)
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(52, 60, linha));
//                061 075 015 Num. Número de Referência
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(60, 75, linha));
//                076 090 015 Num. Número do processo
                r.put(DCCAttEnum.NumeroProcesso, StringUtilities.readString(75, 90, linha));
//                091 102 012 Num. Número do CV/NSU
                r.put(DCCAttEnum.NSU, StringUtilities.readString(90, 102, linha));
//                103 108 006 Alfa Nº- de autorização
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(102, 108, linha));
//                109 112 004 Alfa Código do Request
                r.put(DCCAttEnum.CodigoRequest, StringUtilities.readString(108, 112, linha));
//                113 120 008 Num. Limite de envio dos documentos (DDMMAAAA)
                r.put(DCCAttEnum.DataLimite, StringUtilities.readString(112, 120, linha));
//                121 121 001 Alfa Bandeira (2)
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(120, 121, linha));
//                122 1024 903 Alfa Livre
//                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(120, 1023, linha));

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getTipoArquivoRedeEnum())) {


//                *********** Num. Tipo de registro (?006?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 024 003 Num. Nº do banco
                r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(21, 24, linha));
//                025 029 005 Num. Nº da agência
                r.put(DCCAttEnum.Agencia, StringUtilities.readString(24, 29, linha));
//                030 040 011 Num. Nº da conta-corrente
                r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(29, 40, linha));
//                041 048 008 Num. Data do RV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(40, 48, linha));
//                049 053 005 Num. Quantidade de CV/NSUs acatados (somente aprovadas)
                r.put(DCCAttEnum.QuantidadeAceitos, StringUtilities.readString(48, 53, linha));
//                054 068 015 9(13)V99 Valor bruto
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(53, 68, linha));
//                069 083 015 9(13)V99 Valor da gorjeta
                r.put(DCCAttEnum.ValorGorjeta, StringUtilities.readString(68, 83, linha));
//                084 098 015 9(13)V99 Valor rejeitado (transações manuais e TO)
                r.put(DCCAttEnum.ValorRejeitado, StringUtilities.readString(83, 98, linha));
//                099 113 015 9(13)V99 Valor do desconto
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(98, 113, linha));
//                114 128 015 9(13)V99 Valor líquido
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(113, 128, linha));
//                129 136 008 Num. Data de crédito (DDMMAAAA)
                r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(128, 136, linha));
//                137 137 001 Alfa Bandeira (2)
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(136, 137, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro008.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?008?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 029 008 Num. Data do CV/NSU (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(21, 29, linha));
//                030 037 008 Num. Zeros
                r.put(DCCAttEnum.Zeros, StringUtilities.readString(29, 37, linha));
//                038 052 015 9(13)V99 Valor do CV/NSU
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(37, 52, linha));
//                053 067 015 9(13)V99 Valor da gorjeta
                r.put(DCCAttEnum.ValorGorjeta, StringUtilities.readString(52, 67, linha));
//                068 083 016 Alfa Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(67, 83, linha));
//                084 086 003 Alfa Status do CV/NSU (4)
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(83, 86, linha));
//                087 098 012 Num. Número do CV/NSU
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(86, 98, linha));
//                099 111 013 Alfa Número de referência
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(98, 111, linha));
//                112 126 015 9(13)V99 Valor do desconto
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(111, 126, linha));
//                127 132 006 Alfa Nº autorização
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(126, 132, linha));
//                133 138 006 Num. Hora da transação (HHMMSS)
                r.put(DCCAttEnum.HoraTransacao, StringUtilities.readString(132, 138, linha));
//                139 154 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(138, 154, linha));
//                155 170 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(154, 170, linha));
//                171 186 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(170, 186, linha));
//                187 202 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(186, 202, linha));
//                203 203 001 Alfa Tipo de captura
                r.put(DCCAttEnum.TipoCaptura, StringUtilities.readString(202, 203, linha));
//                204 218 015 9(13)V99 Valor líquido
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(203, 218, linha));
//                219 226 008 Alfa Número do terminal
                r.put(DCCAttEnum.NumeroTerminal, StringUtilities.readString(218, 226, linha));
//                227 229 003 Alfa Sigla do país
                r.put(DCCAttEnum.SiglaPais, StringUtilities.readString(226, 229, linha));
//                230 230 001 Alfa Bandeira
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(229, 230, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro010.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?010?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 024 003 Num. Nº do banco
                r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(21, 24, linha));
//                025 029 005 Num. Nº da agência
                r.put(DCCAttEnum.Agencia, StringUtilities.readString(24, 29, linha));
//                030 040 011 Num. Nº da conta-corrente
                r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(29, 40, linha));
//                041 048 008 Num. Data do RV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(40, 48, linha));
//                049 053 005 Num. Quantidade de CV/NSUs
                r.put(DCCAttEnum.QtdCV, StringUtilities.readString(48, 53, linha));
//                054 068 015 9(13)V99 Valor bruto
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(53, 68, linha));
//                069 083 015 9(13)V99 Valor da gorjeta
                r.put(DCCAttEnum.ValorGorjeta, StringUtilities.readString(68, 83, linha));
//                084 098 015 9(13)V99 Valor rejeitado
                r.put(DCCAttEnum.ValorRejeitado, StringUtilities.readString(83, 98, linha));
//                099 113 015 9(13)V99 Valor do desconto
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(98, 113, linha));
//                114 128 015 9(13)V99 Valor líquido
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(113, 128, linha));
//                129 136 008 Num. Data de crédito da 1ª parcela (DDMMAAAA)
                r.put(DCCAttEnum.DataCreditoPrimeiraParcela, StringUtilities.readString(128, 136, linha));
//                137 137 001 Alfa Bandeira (2)
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(136, 137, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro012.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?012?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 029 008 Num. Data do CV/NSU (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(21, 29, linha));
//                030 037 008 Num. Zeros
                r.put(DCCAttEnum.Zeros, StringUtilities.readString(29, 37, linha));
//                038 052 015 9(13)V99 Valor do CV/NSU
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(37, 52, linha));
//                053 067 015 9(13)V99 Valor da gorjeta
                r.put(DCCAttEnum.ValorGorjeta, StringUtilities.readString(52, 67, linha));
//                068 083 016 Alfa Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(67, 83, linha));
//                084 086 003 Alfa Status do CV/NSU (1)
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(83, 86, linha));
//                087 088 002 Num. Número de parcelas
                r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(86, 88, linha));
//                089 100 012 Num. Número do CV/NSU
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(88, 100, linha));
//                101 113 013 Alfa Número de referência
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(100, 113, linha));
//                114 128 015 9(13)V99 Valor do desconto
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(113, 128, linha));
//                129 134 006 Alfa Nº da autorização
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(128, 134, linha));
//                135 140 006 Num. Hora da transação (HHMMSS)
                r.put(DCCAttEnum.HoraTransacao, StringUtilities.readString(134, 140, linha));
//                141 156 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(140, 156, linha));
//                157 172 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(156, 172, linha));
//                173 188 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(172, 188, linha));
//                189 204 016 Alfa Nº do bilhete
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(188, 204, linha));
//                205 205 001 Alfa Tipo de captura
                r.put(DCCAttEnum.TipoCaptura, StringUtilities.readString(204, 205, linha));
//                206 220 015 9(13)V99 Valor líquido do CV/NSU
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(205, 220, linha));
//                221 235 015 9(13)V99 Valor líquido da primeira parcela
                r.put(DCCAttEnum.ValorPrimeiraParcela, StringUtilities.readString(220, 235, linha));
//                236 250 015 9(13)V99 Valor líquido das demais parcelas
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(235, 250, linha));
//                251 258 008 Alfa Número do terminal
                r.put(DCCAttEnum.NumeroTerminal, StringUtilities.readString(250, 258, linha));
//                259 261 003 Alfa Sigla do país
                r.put(DCCAttEnum.SiglaPais, StringUtilities.readString(258, 261, linha));
//                262 262 001 Alfa Bandeira
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(261, 262, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro014.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?014?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 029 008 Num. Data do RV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(21, 29, linha));
//                030 037 008 Num. Brancos
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(29, 37, linha));
//                038 039 002 Num. Número da parcela
                r.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(37, 39, linha));
//                040 054 015 9(13)V99 Valor da parcela bruto
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(39, 54, linha));
//                055 069 015 9(13)V99 Valor do desconto sobre a parcela
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(54, 69, linha));
//                070 084 015 9(13)V99 Valor da parcela líquida
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(69, 84, linha));
//                085 092 008 Num. Data do crédito (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, StringUtilities.readString(84, 92, linha));
//                093 1024 932 Alfa Livre


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro034.getTipoArquivoRedeEnum())) {

//                1 3 3 Num Tipo de registro (?034?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                4 12 9 Num Número do PV
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                13 21 9 Num Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                22 29 8 Num Data do CV / NSU (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(21, 29, linha));
//                30 44 15 9(13)V99 Valor do CV / NSU
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(29, 44, linha));
//                45 60 16 Alfa Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(44, 60, linha));
//                61 72 12 Num Número do CV / NSU
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(60, 72, linha));
//                73 78 6 Alfa Nº Autorização
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(72, 78, linha));
//                79 98 20 Alfa TID
                r.put(DCCAttEnum.TID, StringUtilities.readString(78, 98, linha));
//                99 128 30 Alfa Número do Pedido
                r.put(DCCAttEnum.NumeroPedido, StringUtilities.readString(98, 128, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro026.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro026.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?026?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Alfa Nº PV matriz
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 027 015 9(13)V99 Valor total bruto
                r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(12, 27, linha));
//                028 033 006 Num. Quantidade de CV/NSUs rejeitados
                r.put(DCCAttEnum.QtdCVRejeitado, StringUtilities.readString(27, 33, linha));
//                034 048 015 9(13)V99 Valor total rejeitado
                r.put(DCCAttEnum.ValorRejeitado, StringUtilities.readString(33, 48, linha));
//                049 063 015 9(13)V99 Valor total rotativo
                r.put(DCCAttEnum.ValorTotalRotativo, StringUtilities.readString(48, 63, linha));
//                064 078 015 9(13)V99 Valor total parcelado sem juros
                r.put(DCCAttEnum.ValorTotalParceladoSemJuros, StringUtilities.readString(63, 78, linha));
//                079 093 015 9(13)V99 Valor total parcelado IATA
                r.put(DCCAttEnum.ValorTotalParceladoIATA, StringUtilities.readString(78, 93, linha));
//                094 108 015 9(13)V99 Valor total dólar
                r.put(DCCAttEnum.ValorTotalDolar, StringUtilities.readString(93, 108, linha));
//                109 123 015 9(13)V99 Valor total desconto
                r.put(DCCAttEnum.ValorTotalDescontos, StringUtilities.readString(108, 123, linha));
//                *********** 9(13)V99 Valor total líquido
                r.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.readString(123, 138, linha));
//                *********** 9(13)V99 Valor total da gorjeta
                r.put(DCCAttEnum.ValorTotalGorjeta, StringUtilities.readString(138, 153, linha));
//                *********** 9(13)V99 Valor total da taxa de embarque
                r.put(DCCAttEnum.ValorTotalTaxaEmbarque, StringUtilities.readString(153, 168, linha));
//                *********** Num. Quant. CV/NSU acatados
                r.put(DCCAttEnum.QuantidadeAceitos, StringUtilities.readString(168, 174, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro034.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?034?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV centralizador
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                *********** Num. Número do documento
                r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(12, 23, linha));
//                *********** Num. Data do lançamento (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, StringUtilities.readString(23, 31, linha));
//                032 046 015 9(13)V99 Valor do lançamento
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(31, 46, linha));
//                047 047 001 Alfa C (Crédito)
                r.put(DCCAttEnum.IndicadorCredDeb, StringUtilities.readString(46, 47, linha));
//                048 050 003 Num. Banco
                r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(47, 50, linha));
//                051 056 006 Num. Agência
                r.put(DCCAttEnum.Agencia, StringUtilities.readString(50, 56, linha));
//                057 067 011 Num. Conta-corrente
                r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(56, 67, linha));
//                068 075 008 Num. Data do movimento (DDMMAAAA)
                r.put(DCCAttEnum.DataEmissao, StringUtilities.readString(67, 75, linha));
//                076 084 009 Num. Número do RV
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(75, 84, linha));
//                085 092 008 Num. Data do RV (DDMMAAAA)
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(84, 92, linha));
//                *********** Alfa Bandeira
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(92, 93, linha));
//                *********** Num. Tipo de transação
                r.put(DCCAttEnum.TipoTransacao, StringUtilities.readString(93, 94, linha));
//                *********** 9(13)V99 Valor bruto do RV
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(94, 109, linha));
//                *********** 9(13)V99 Valor da taxa de desconto
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(109, 124, linha));
//                *********** Alfa Número da parcela/total
                String numeroParcela = StringUtilities.readString(124, 129, linha);
                r.put(DCCAttEnum.NumeroParcela, numeroParcela.split("/")[0]);
                r.put(DCCAttEnum.QuantidadeParcelas, numeroParcela.split("/")[1]);
//                *********** Alfa Status do crédito - Tabela II
                r.put(DCCAttEnum.StatusTransacao, StringUtilities.readString(129, 131, linha));
//                *********** Num. Nº- PV original
                r.put(DCCAttEnum.NumeroPontoVendaOriginal, StringUtilities.readString(131, 140, linha));


            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro035.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?035?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV ajustado
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV ajustado
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(12, 21, linha));
//                022 029 008 Num. Data do ajuste (DDMMAAAA)
                r.put(DCCAttEnum.DataAjuste, StringUtilities.readString(21, 29, linha));
//                030 044 015 9(13)V99 Valor do ajuste
                r.put(DCCAttEnum.ValorAjuste, StringUtilities.readString(29, 44, linha));
//                045 045 001 Alfa D (Débito)
                r.put(DCCAttEnum.IndicadorCredDeb, StringUtilities.readString(44, 45, linha));
//                046 047 002 Num. Motivo do ajuste (cód. da tabela III)
                r.put(DCCAttEnum.CodigoMotivo, StringUtilities.readString(45, 47, linha));
//                048 075 028 Alfa Motivo do ajuste (string - tabela III)
                r.put(DCCAttEnum.Motivo, StringUtilities.readString(47, 75, linha));
//                076 091 016 Num. Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(75, 91, linha));
//                092 099 008 Num. Data da transação ?CV? (DDMMAAAA)
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(91, 99, linha));
//                100 108 009 Num. Número do RV original
                r.put(DCCAttEnum.NumeroResumoVendaOriginal, StringUtilities.readString(99, 108, linha));
//                109 123 015 Alfa Número de referência da carta/fax
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(108, 123, linha));
//                124 131 008 Num. Data da carta (DDMMAAAA)
                r.put(DCCAttEnum.DataCarta, StringUtilities.readString(123, 131, linha));
//                132 137 006 Num. Mês de referência (serviços, POS etc.) (MMAAAA)
                r.put(DCCAttEnum.MesReferencia, StringUtilities.readString(131, 137, linha));
//                138 146 009 Num. Nº- PV original
                r.put(DCCAttEnum.NumeroPontoVendaOriginal, StringUtilities.readString(137, 146, linha));
//                147 154 008 Alfa Data RV original (DDMMAAAA)
                r.put(DCCAttEnum.DataResumoVendaOriginal, StringUtilities.readString(146, 154, linha));
//                155 169 015 9(13)V99 Valor da transação
                r.put(DCCAttEnum.ValorTransacao, StringUtilities.readString(154, 169, linha));
//                170 170 001 Alfa D (Desagendamento) ou N (Net)
                r.put(DCCAttEnum.ReservadoBanco, StringUtilities.readString(169, 170, linha));
//                171 178 008 Num. Data do crédito (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, StringUtilities.readString(170, 178, linha));
//                179 193 015 9(13)V99 Novo valor da parcela
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(178, 193, linha));
//                194 208 015 9(13)V99 Valor original da parcela
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(193, 208, linha));
//                209 223 015 9(13)V99 Valor bruto do resumo de vendas original
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(208, 223, linha));
//                224 238 015 9(13)V99 Valor do cancelamento solicitado
                r.put(DCCAttEnum.ValorCancelamento, StringUtilities.readString(223, 238, linha));
//                239 250 012 Num. Número do NSU (motivos 16, 18 e 23)
                r.put(DCCAttEnum.NSU, StringUtilities.readString(238, 250, linha));
//                251 256 006 Alfa Número da autorização
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(250, 256, linha));
//                257 257 001 Alfa Tipo de débito
                r.put(DCCAttEnum.TipoDebito, StringUtilities.readString(256, 257, linha));
//                258 268 011 Num. Número da Ordem de Débito
                r.put(DCCAttEnum.NumeroOrdemDebito, StringUtilities.readString(257, 268, linha));
//                269 283 015 9(13)V99 Valor do débito total
                r.put(DCCAttEnum.ValorDebito, StringUtilities.readString(268, 283, linha));
//                284 298 015 9(13)V99 Valor pendente
                r.put(DCCAttEnum.ValorPendente, StringUtilities.readString(283, 298, linha));
//                299 299 001 Alfa Bandeira do RV de origem (2)
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(298, 299, linha));
//                300 300 001 Alfa Bandeira do RV ajustado (2)
                r.put(DCCAttEnum.BandeiraAjustado, StringUtilities.readString(299, 300, linha));
//                303 304 002 Num. Nº da parcela RV original
                r.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(302, 304, linha));



            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro049.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro049.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?049?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Número do PV original
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                013 021 009 Num. Número do RV original
                r.put(DCCAttEnum.NumeroResumoVendaOriginal, StringUtilities.readString(12, 21, linha));
//                022 036 015 Alpha Número de referência
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(21, 36, linha));
//                037 044 008 Num. Data do crédito (DDMMAAAA)
                r.put(DCCAttEnum.DataCredito, StringUtilities.readString(36, 44, linha));
//                045 059 015 9(13)V99 Novo valor da parcela
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(44, 59, linha));
//                060 074 015 9(13)V99 Valor original da parcela alterada
                r.put(DCCAttEnum.ValorParcelaOriginal, StringUtilities.readString(59, 74, linha));
//                075 089 015 9(13)V99 Valor do ajuste
                r.put(DCCAttEnum.ValorAjuste, StringUtilities.readString(74, 89, linha));
//                090 097 008 Num. Data do cancelamento (DDMMAAAA)
                r.put(DCCAttEnum.DataCancelamento, StringUtilities.readString(89, 97, linha));
//                098 112 015 9(13)V99 Valor do RV original
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(97, 112, linha));
//                113 127 015 9(13)V99 Valor do cancelamento solicitado
                r.put(DCCAttEnum.ValorCancelamento, StringUtilities.readString(112, 127, linha));
//                128 143 016 Num. Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(127, 143, linha));
//                144 151 008 Num. Data da transação (DDMMAAAA)
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(143, 151, linha));
//                152 163 012 Num. NSU
                r.put(DCCAttEnum.NSU, StringUtilities.readString(151, 163, linha));
//                164 164 001 Num. Tipo de débito
                r.put(DCCAttEnum.TipoDebito, StringUtilities.readString(163, 164, linha));
//                165 166 002 Num. Número da parcela
                String numeroParcela = StringUtilities.readString(164, 166, linha);
                r.put(DCCAttEnum.NumeroParcela, numeroParcela.split("/")[0]);
                r.put(DCCAttEnum.QuantidadeParcelas, numeroParcela.split("/")[1]);
//                167 167 001 Alfa Bandeira do RV de origem
                r.put(DCCAttEnum.Bandeira, StringUtilities.readString(166, 167, linha));

            } else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro057.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro057.getTipoArquivoRedeEnum())) {


//                1 3 3 Num Tipo de registro (?057?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                4 12 9 Num Número do PV original
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(3, 12, linha));
//                13 21 9 Num Número do RV original
                r.put(DCCAttEnum.NumeroResumoVendaOriginal, StringUtilities.readString(12, 21, linha));
//                98 112 15 9(13)V99 Valor do RV original
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(21, 36, linha));
//                128 143 16 Num Número do cartão
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(36, 52, linha));
//                144 151 8 Num Data da transação
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(52, 60, linha));
//                152 163 12 Num NSU
                r.put(DCCAttEnum.NSU, StringUtilities.readString(60, 72, linha));
//                165 166 20 Alfa TID
                r.put(DCCAttEnum.TID, StringUtilities.readString(72, 92, linha));
//                167 196 30 Alfa Número do pedido
                r.put(DCCAttEnum.NumeroPedido, StringUtilities.readString(92, 112, linha));

            }

        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {

            String tipoRegistro = StringUtilities.readString(0, 3, linha);

            if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro028.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro028.getTipoArquivoRedeEnum())) {


//            *********** Num. Tipo de registro (?028?) - Trailer de arquivo
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//            *********** Num. Quantidade de matrizes
                r.put(DCCAttEnum.QuantidadeMatriz, StringUtilities.readString(3, 7, linha));
//            *********** Num. Quantidade de registros
                r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(7, 13, linha));
//            *********** Alfa Nº PV grupo
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(13, 22, linha));
//            023 037 015 9(13)V99 Valor total bruto
                r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(22, 37, linha));
//            038 043 006 Num. Quantidade de CV/NSUs rejeitados
                r.put(DCCAttEnum.QtdCVRejeitado, StringUtilities.readString(37, 43, linha));
//            044 058 015 9(13)V99 Valor total rejeitado
                r.put(DCCAttEnum.ValorRejeitado, StringUtilities.readString(43, 58, linha));
//            059 073 015 9(13)V99 Valor total rotativo
                r.put(DCCAttEnum.ValorTotalRotativo, StringUtilities.readString(58, 73, linha));
//            074 088 015 9(13)V99 Valor total parcelado sem juros
                r.put(DCCAttEnum.ValorTotalParceladoSemJuros, StringUtilities.readString(73, 88, linha));
//            089 103 015 9(13)V99 Valor total parcelado IATA
                r.put(DCCAttEnum.ValorTotalParceladoIATA, StringUtilities.readString(88, 103, linha));
//            104 118 015 9(13)V99 Valor total dólar
                r.put(DCCAttEnum.ValorTotalDolar, StringUtilities.readString(103, 118, linha));
//            *********** 9(13)V99 Valor total desconto
                r.put(DCCAttEnum.ValorTotalDescontos, StringUtilities.readString(118, 133, linha));
//            *********** 9(13)V99 Valor total líquido
                r.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.readString(133, 148, linha));
//            *********** 9(13)V999 Valor total gorjeta
                r.put(DCCAttEnum.ValorTotalGorjeta, StringUtilities.readString(148, 163, linha));
//            *********** 9(13)V99 Valor total da taxa de embarque
                r.put(DCCAttEnum.ValorTotalTaxaEmbarque, StringUtilities.readString(163, 178, linha));
//            *********** Num. Quant. CV/NSU acatados
                r.put(DCCAttEnum.QuantidadeAceitos, StringUtilities.readString(178, 184, linha));

            }else if (tipoRegistro.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro052.getId())
                    && tipoArquivoRedeEnum.equals(ExtratoRedeTipoRegistroEnum.EEFI_TipoRegistro052.getTipoArquivoRedeEnum())) {

//                *********** Num. Tipo de registro (?052?)
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 3, linha));
//                *********** Num. Quantidade de matrizes no arquivo
                r.put(DCCAttEnum.QuantidadeMatriz, StringUtilities.readString(3, 7, linha));
//                *********** Num. Quantidade de registros no arquivo
                r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(7, 13, linha));
//                *********** Num. Nº PV grupo
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(13, 22, linha));
//                023 026 004 Num. Quantidade total de resumos grupo
                r.put(DCCAttEnum.QtdLotes, StringUtilities.readString(22, 26, linha));
//                027 041 015 9(13)V99 Valor total dos créditos normais
                r.put(DCCAttEnum.ValorTotalCredito, StringUtilities.readString(26, 41, linha));
//                042 047 006 Num. Quantidade de créditos antecipados
                r.put(DCCAttEnum.QtdAntecipado, StringUtilities.readString(41, 47, linha));
//                048 062 015 9(13)V99 Valor total antecipado
                r.put(DCCAttEnum.ValorTotalAntecipado, StringUtilities.readString(47, 62, linha));
//                063 066 004 Num. Quantidade de ajustes a crédito
                r.put(DCCAttEnum.QtdAjustesCredito, StringUtilities.readString(62, 66, linha));
//                067 081 015 9(13)V99 Valor total de ajustes a crédito
                r.put(DCCAttEnum.ValorTotalAjustes, StringUtilities.readString(66, 81, linha));
//                082 085 004 Num. Quantidade de ajustes a débito
                r.put(DCCAttEnum.QtdAjustesDebito, StringUtilities.readString(81, 85, linha));
//                086 100 015 9(13)V99 Valor total de ajustes a débito
                r.put(DCCAttEnum.ValorTotalDebito, StringUtilities.readString(85, 100, linha));

            }

        }
    }
}

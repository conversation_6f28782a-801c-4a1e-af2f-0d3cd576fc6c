package servicos.impl.dcc.rede;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import servicos.impl.dcc.base.TipoRegistroEnum;

/**
 * Created by <PERSON><PERSON>
 */
public enum ExtratoRedeTipoRegistroEnum {

    //  Extrato Eletrônico de Vendas Débito
    EEVD_TipoRegistro00("00", "Cabeçalho do arquivo", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro01("01", "Resumo de vendas", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro02("02", "Total do ponto de venda", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro03("03", "Total do estabelecimento matriz", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro04("04", "Total do arquivo", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro05("05", "Detalhamento dos comprovantes de vendas", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro13("13", "Detalhamento dos comprovantes de vendas (E-Commerce)", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro20("20", "Detalhamento dos comprovantes de vendas (exclusivo para operadoras de celular)", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro08("08", "Desagendamento de vendas pré-datadas (total ou parcial)", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro09("09", "Transações pré-datadas liquidadas", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro11("11", "Ajustes Net", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),
    EEVD_TipoRegistro17("17", "Ajustes Net (E-Commerce)", TipoArquivoRedeEnum.EEVD, TipoRegistroEnum.DETALHE),

    //  Extrato Eletrônico de Vendas Crédito
    EEVC_TipoRegistro002("002", "Header do Arquivo", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro004("004", "Header Matriz", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro005("005", "Aviso de Request", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro033("033", "Aviso de Request (E-Commerce)", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro006("006", "RV Rotativo", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro008("008", "CV/NSU Rotativo", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro034("034", "CV/NSU Rotativo (E-Commerce)", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro040("040", "CV/NSU Recarga (exclusivo para operadoras de celular)", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro010("010", "RV Parcelado sem juros", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro011("011", "Ajuste a Crédito CV/NSU Parcelado sem juros", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro012("012", "CV/NSU Parcelado sem juros", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro035("035", "CV/NSU Parcelado sem juros (E-Commerce)", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro014("014", "Parcelas sem juros", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro016("016", "RV IATA", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro017("017", "AVS", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro018("018", "CV/NSU IATA", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro036("036", "CV/NSU IATA (E-Commerce)", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro019("019", "Serasa", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro020("020", "Parcelas IATA", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro021("021", "SecureCode", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro022("022", "RV Dólar", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro024("024", "CV/NSU Dólar 012", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro026("026", "Totalizador Matriz", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),
    EEVC_TipoRegistro028("028", "Trailer do Arquivo", TipoArquivoRedeEnum.EEVC, TipoRegistroEnum.DETALHE),


    //Extrato Eletrônico Financeiro
    EEFI_TipoRegistro030("030", "Header do Arquivo", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro032("032", "Header do Solicitante", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro034("034", "Ordem de Crédito (esse registro deverá ser acompanhado do registro 35, caso haja algum Ajuste Net ou desagendamento)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro035("035", "Ajustes Net e Desagendamento", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro053("053", "Ajustes Net e Desagendamentos (E-Commerce)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro036("036", "RAV - Resumo Antecipado de Vendas (esse registro deverá ser acompanhado do registro 35, caso haja algum Ajuste Net ou desagendamento)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro037("037", "Totalizador de Créditos (normais e antecipados)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro038("038", "Ajustes a Débito (via Banco)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro054("054", "Ajustes a Débito (via Banco) - (E-Commerce)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro040("040", "Serasa ? Identifica detalhes das consultas Serasa", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro041("041", "AVS ? Identifica detalhes das consultas AVS", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro042("042", "SecureCode ? identifica detalhes das consultas SecureCode", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro043("043", "Ajustes a Crédito (esse registro deverá ser acompanhado do registro 35, caso haja algum Ajuste Net)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro044("044", "Débitos Pendentes", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro055("055", "Débitos Pendentes (E-Commerce)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro045("045", "Débitos Liquidados", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro056("056", "Débitos Liquidados (E-Commerce)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro049("049", "Desagendamento das Parcelas com informações complementares", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro057("057", "Desagendamento das Parcelas com informações complementares (E-Commerce)", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro050("050", "Totalizador do Solicitante", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE),
    EEFI_TipoRegistro052("052", "Trailer do Arquivo", TipoArquivoRedeEnum.EEFI, TipoRegistroEnum.DETALHE);

    private String id;
    private String descricao;
    private TipoArquivoRedeEnum tipoArquivoRedeEnum;
    private TipoRegistroEnum tipoRegistroEnum;

    private ExtratoRedeTipoRegistroEnum(String id, String descricao, TipoArquivoRedeEnum tipoArquivoRedeEnum, TipoRegistroEnum tipoRegistroEnum) {
        this.id = id;
        this.descricao = descricao;
        this.tipoArquivoRedeEnum = tipoArquivoRedeEnum;
        this.tipoRegistroEnum = tipoRegistroEnum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public TipoArquivoRedeEnum getTipoArquivoRedeEnum() {
        return tipoArquivoRedeEnum;
    }

    public void setTipoArquivoRedeEnum(TipoArquivoRedeEnum tipoArquivoRedeEnum) {
        this.tipoArquivoRedeEnum = tipoArquivoRedeEnum;
    }

    public static ExtratoRedeTipoRegistroEnum valueOff(String id) {
        ExtratoRedeTipoRegistroEnum[] values = ExtratoRedeTipoRegistroEnum.values();
        for (ExtratoRedeTipoRegistroEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public static String retornarIDs(TipoConciliacaoEnum tipoConciliacaoEnum) {
        String[] tipos;
        if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS)) {
            tipos = new String[]{EEVC_TipoRegistro012.getId()};
        } else if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
            tipos = new String[]{EEFI_TipoRegistro034.getId()};
        } else {
            return "";
        }

        StringBuilder codigos = new StringBuilder();
        for (String tipoRegistro : tipos) {
            if (codigos.toString().equals("")) {
                codigos.append("'").append(tipoRegistro).append("'");
            } else {
                codigos.append(",").append("'").append(tipoRegistro).append("'");
            }
        }
        return codigos.toString();
    }

    public static boolean isArquivoMapaRede(ExtratoDiarioItemVO item) {
        if (!item.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            return false;
        }
        if (item.getTipoRegistro().equals(ExtratoRedeTipoRegistroEnum.EEVC_TipoRegistro006.getId()) && item.getTipoArquivo().equals(TipoArquivoRedeEnum.EEVC.getId())) {
            return true;
        }
        return false;
    }

    public TipoRegistroEnum getTipoRegistroEnum() {
        return tipoRegistroEnum;
    }

    public void setTipoRegistroEnum(TipoRegistroEnum tipoRegistroEnum) {
        this.tipoRegistroEnum = tipoRegistroEnum;
    }
}

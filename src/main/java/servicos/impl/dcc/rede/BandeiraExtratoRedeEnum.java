package servicos.impl.dcc.rede;

/**
 * Created by <PERSON><PERSON>
 */
public enum BandeiraExtratoRedeEnum {

    Bandeira_0("0", "Outras bandeiras"),
    Bandeira_1("1", "MasterCard"),
    Bandeira_2("2", "Diners Club"),
    Bandeira_3("3", "<PERSON>"),
    Bandeira_4("4", "<PERSON><PERSON>l"),
    Bandeira_5("5", "Hipercard"),
    Bandeira_6("6", "Sorocred"),
    Bandeira_7("7", "CUP"),
    Bandeira_8("8", "Credsystem (Mais)"),
    Bandeira_9("9", "<PERSON>cre<PERSON>"),
    Bandeira_A("A", "Avista"),
    Bandeira_E("E", "Elo"),
    Bandeira_B("B", "Banescard"),
    Bandeira_J("J", "JCB"),
    Bandeira_X("X", "Amex"),
    Bandeira_Z("Z", "Credz");

    private String id;
    private String descricao;

    private BandeiraExtratoRedeEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static BandeiraExtratoRedeEnum valueOff(String id) {
        BandeiraExtratoRedeEnum[] values = BandeiraExtratoRedeEnum.values();
        for (BandeiraExtratoRedeEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public static boolean extratoBIN(String id) {
        BandeiraExtratoRedeEnum[] values = BandeiraExtratoRedeEnum.values();
        for (BandeiraExtratoRedeEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }


    public static String retornarIDs() {
        StringBuilder codigos = new StringBuilder();
        for (BandeiraExtratoRedeEnum eDIStatusEnum : values()) {
            if (codigos.toString().equals("")) {
                codigos.append("'").append(eDIStatusEnum.getId()).append("'");
            } else {
                codigos.append(",").append("'").append(eDIStatusEnum.getId()).append("'");
            }
        }
        return codigos.toString();
    }
}

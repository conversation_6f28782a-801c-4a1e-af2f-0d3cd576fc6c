package servicos.impl.dcc.safra;

public enum Cnab400SafraStatusEnum {
    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status02("02", "ENTRADA CONFIRMADA"),
    Status03("03", "ENTRADA REJEITADA"),
    Status04("04", "TRANSFERÊNCIA DE CARTEIRA (ENTRADA)"),
    Status05("05", "TRANSFERÊNCIA DE CARTEIRA (BAIXA)"),
    Status06("06", "LIQUIDAÇÃO NORMAL"),
    Status09("09", "BAIXADO AUTOMATICAMENTE"),
    Status10("10", "BAIXADO CONFORME INSTRUÇÕES"),
    Status11("11", "TÍTULOS EM SER (PARA ARQUIVO MENSAL)"),
    Status12("12", "ABATIMENTO CONCEDIDO"),
    Status13("13", "ABATIMENTO CANCELADO"),
    Status14("14", "VENCIMENTO ALTERADO"),
    Status15("15", "LIQUIDAÇÃO EM CARTÓRIO"),
    Status19("19", "CONFIRMAÇÃO DE INSTRUÇÃO DE PROTESTO"),
    Status20("20", "CONFIRMAÇÃO DE SUSTAR PROTESTO"),
    Status21("21", "TRANSFERÊNCIA DE BENEFICIÁRIO"),
    Status23("23", "TÍTULO ENVIADO A CARTÓRIO"),
    Status40("40", "BAIXA DE TÍTULO PROTESTADO"),
    Status41("41", "LIQUIDAÇÃO DE TÍTULO BAIXADO"),
    Status42("42", "TÍTULO RETIRADO DO CARTÓRIO"),
    Status43("43", "DESPESA DE CARTÓRIO"),
    Status44("44", "ACEITE DO TÍTULO DDA PELO PAGADOR"),
    Status45("45", "NÃO ACEITE DO TÍTULO DDA PELO PAGADOR"),
    Status51("51", "VALOR DO TÍTULO ALTERADO"),
    Status52("52", "ACERTO DE DATA DE EMISSAO"),
    Status53("53", "ACERTO DE COD ESPECIE DOCTO"),
    Status54("54", "ALTERACAO DE SEU NUMERO"),
    Status56("56", "INSTRUÇÃO NEGATIVAÇÃO ACEITA"),
    Status57("57", "INSTRUÇÃO BAIXA DE NEGATIVAÇÃO ACEITA"),
    Status58("58", "INSTRUÇÃO NÃO NEGATIVAR ACEITA"),
    Status9999("9999", "Retorno manual");

    private String id;
    private String descricao;

    Cnab400SafraStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static Cnab400SafraStatusEnum valueOff(String id) {
        Cnab400SafraStatusEnum[] values = values();
        for (Cnab400SafraStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return Cnab400SafraStatusEnum.StatusNENHUM;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

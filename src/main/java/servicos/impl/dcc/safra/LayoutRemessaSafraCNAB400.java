/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.safra;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.jboleto.JBoletoBean;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LayoutRemessaSafraCNAB400 extends LayoutRemessaBase {


    public static RegistroRemessa preencherHeaderArquivoRemessa(ConvenioCobrancaVO convenio, Date dataGeracao, Integer codigoRemessa) {
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");//7
        header.put(DCCAttEnum.CodigoServico, "01");//2
        header.put(DCCAttEnum.IdentificacaoServico, new StringBuilder(StringUtilities.formatarCampoEmBranco("COBRANCA", 15)));
        header.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getAgencia(), 4)); // Agência/Beneficiário
        header.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getAgenciaDV(), 1)); //Dígito Agência/Beneficiário
        header.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrente(), 8)); // Código Cliente/Beneficiário
        header.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrenteDV(), 1)); // Dígito Código Cliente/Beneficiário
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(6));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(convenio.getEmpresa().getRazaoSocial(), 30)); // Nome do Beneficiário
        header.put(DCCAttEnum.IdentificacaoBanco, StringUtilities.formatarCampoEmBranco("422BANCO SAFRA", 18));
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyy"));
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(291));
        header.put(DCCAttEnum.NumeroArquivo, StringUtilities.formatarCampo(new BigDecimal(codigoRemessa), 3));//sequencial de Remessa
        header.put(DCCAttEnum.SequencialRegistro, "000001");//6
        return header;
    }

    public static EnderecoVO getEndereco(RemessaItemVO item) {
        return LayoutRemessaBase.getEndereco(item);
    }

    public static RegistroRemessa preencherDetalheArquivoRemessa(RemessaItemVO remessaItem, Date dataRegistro, Integer sequencial) {
        RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
        ConvenioCobrancaVO convenio = remessaItem.getRemessa().getConvenioCobranca();
        detail.put(DCCAttEnum.TipoRegistro, "1");
        detail.put(DCCAttEnum.IdentificadorClienteEmpresa, "02");
        detail.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(convenio.getEmpresa().getCNPJ()), 14));
        detail.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getAgencia(), 4)); // Agência/Beneficiário
        detail.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getAgenciaDV(), 1)); //Dígito Agência/Beneficiário
        detail.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrente(), 8)); // Código Cliente/Beneficiário
        detail.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrenteDV(), 1)); // Dígito Código Cliente/Beneficiário
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(31));
        detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getIdentificador().toString(), 8));
        detail.put(DCCAttEnum.NossoNumeroDV, calcularDVNossoNumero(StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getIdentificador().toString(), 8)));
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(30));
        detail.put(DCCAttEnum.IOF, "0");
        //103-104   Identificação do tipo de Moeda (00=REAL)
        detail.put(DCCAttEnum.Moeda, "00");
        //105-105   Campo sem preenchimento (Brancos)
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        //106-107   3º Inst de Cob Utilizar somente quando Inst 2 = 10
        detail.put(DCCAttEnum.DiasProtesto, "00");
        //108-108   Identificação Tipo Carteira (1=COB SIMP / 2=COB VINC)
        detail.put(DCCAttEnum.CodCarteira, "1");
        //109-110   Identificação do Tipo de Ocorrência (01=Remessa Ttítulo)
        detail.put(DCCAttEnum.CodOcorrencia, "01");
        //111-120   Identificação do Titulo na Empresa
        detail.put(DCCAttEnum.SeuNumero, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getCodigo(), 10)); // Seu número
        //121-126   Data de vencimento do titulo
        detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(remessaItem.getDataVencimentoBoleto(), "ddMMyy"));
        //127-139   Valor Nominal do Titulo
        detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoMonetario(remessaItem.getValorBoleto(), 13));
        //140-142   Cód do banco encarregado da cobrança
        detail.put(DCCAttEnum.Banco, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getBanco().getCodigoBanco(), 3));
        //143-147   Agência Encarregada da cobrança ( 99999 )
        String agenciaDepositaria = convenio.getContaEmpresa().getAgencia() + convenio.getContaEmpresa().getAgenciaDV();
        detail.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoForcandoZerosAEsquerda(agenciaDepositaria, 5));
        //148-149   Espécie do Titulo ( 01=DUP MERCANTIL)
        detail.put(DCCAttEnum.EspecieTitulo, "01");
        //150-150   Identificação do Aceite do titulo
        detail.put(DCCAttEnum.Aceite, "A");
        //151-156   Data de emissão do titulo
        detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataRegistro, "ddMMyy"));
        //157-158   Primeira Instrução de Cobrança
        if (convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros()) {
            detail.put(DCCAttEnum.Instrucao1, "01");
        } else {
            detail.put(DCCAttEnum.Instrucao1, "00");
        }
        //159-160   Segunda Instrução de Cobrança
        detail.put(DCCAttEnum.Instrucao2, "00");
        //161-173   Juros de mora por dia de atraso
        double valorCobrar = 0.0;
        if (convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros()) {
            if (convenio.getEmpresa().isUtilizarJurosValorAbsoluto()) {
                valorCobrar = convenio.getEmpresa().getJurosCobrancaAutomatica();
            } else {
                Double juros = convenio.getEmpresa().getJurosCobrancaAutomatica() / 30;
                valorCobrar = remessaItem.getValorBoleto() * juros;
            }
        }
        detail.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(valorCobrar, 13));
        //174-179   Data Limite para desconto
        //180-192   Valor do desconto concedido
        if (remessaItem.possuiDesconto() && remessaItem.getDataPagamentoAntecipado() != null) {
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(remessaItem.getDataPagamentoAntecipado(), "ddMMyy"));
            Double valor = remessaItem.getValorBoleto();
            valor = valor * (1 - remessaItem.getPorcentagemDescontoBoletoPagAntecipado()) / 100;
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 13));
        } else {
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));
        }
        //193-205   Valor de IOF operações de seguro
        detail.put(DCCAttEnum.ValorBaseIOF, StringUtilities.formatarCampoZerado(13));
        //206-218   Valor do abatimento concedido ou cancelado / multa
        detail.put(DCCAttEnum.ValorAbatimentoConcedido, StringUtilities.formatarCampoZerado(13));
        //219-220   Cód de inscrição do sacado (01 - CPF / 02 - CNPJ)
        detail.put(DCCAttEnum.TipoInscricao, "01");// Tipo inscrição pagador 01 = CPF, 02 = CNPJ
        //221-234   Número de incrição do sacado
        String cpf = getCPF(remessaItem);
        detail.put(DCCAttEnum.InscricaoPagador, StringUtilities.formatarCpfCnjp(cpf, 14));
        //235-274   Nome do Sacado
        detail.put(DCCAttEnum.NomeCliente, getNome(remessaItem, 40));
        //275-314   Endereço do Sacado
        EnderecoVO enderecoVO = getEndereco(remessaItem);
        detail.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 40));
        //315-324   Bairro do Sacado
        detail.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 10));
        //325-326   Campo sem preenchimento (Brancos)
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
        //327-334   Cód de endereçamento postal do sacado
        detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
        //335-349   Cidade do Sacado
        detail.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getCidade().getNome(), 15));
        //350-351   Estado do Sacado
        detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getEstadoVO().getSigla(), 2));
        //352-381   Nome do sacador avalista
        detail.put(DCCAttEnum.NomeSacadorAvalista, StringUtilities.formatarCampoEmBranco(30));
        //382-388   Campo sem preenchimento (Brancos)
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(7));
        //389-391   Banco emitente do boleto
        detail.put(DCCAttEnum.Banco, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getBanco().getCodigoBanco(), 3));
        //392-394   Numero Sequencial Geração Arquivo remessa
        detail.put(DCCAttEnum.NumeroArquivo, StringUtilities.formatarCampo(new BigDecimal(remessaItem.getRemessa().getCodigo()), 3));//sequencial de Remessa
        //395-400   Numero sequencial de registro de arquivo
        detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(sequencial, 6));
        return detail;
    }

    public static RegistroRemessa preencherTraillerArquivoRemessa(RemessaVO remessa, Integer sequencial) {
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(367));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(remessa.getListaItens().size()), 8));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(valorTotalBruto, 15));
        trailer.put(DCCAttEnum.NumeroArquivo, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 3));//sequencial de Remessa
        trailer.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(sequencial), 6));
        return trailer;
    }


    public static void preencherArquivoRemessa(RemessaVO remessa) {
        Date dataDeposito = remessa.getDataRegistro();
        ConvenioCobrancaVO convenio = remessa.getConvenioCobranca();
        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 2;
        for (RemessaItemVO item : lista) {
            item.setRemessa(remessa);
            RegistroRemessa detail = preencherDetalheArquivoRemessa(item, dataDeposito, seq);
            if (item.getCodigo() != 0) {
                qtdAceito += 1;
            }
            seq++;
            listaDetalhe.add(detail);
        }

        remessa.setQtdAceito(qtdAceito);
        remessa.setHeaderRemessa(preencherHeaderArquivoRemessa(convenio, dataDeposito, remessa.getCodigo()));
        remessa.setHead(new StringBuilder(remessa.getHeaderRemessa().toString()));
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
        remessa.setTrailerRemessa(preencherTraillerArquivoRemessa(remessa, seq));
        remessa.setTrailer(new StringBuilder(remessa.getTrailerRemessa().toString()));
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02Retorno")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        switch (r.getTipo()) {
            case HEADER:
                lerAtributosHeader(linha, r);
                break;
            case DETALHE:
                lerAtributosDetalhe(linha, r);
                break;
            case TRAILER:
                lerAtributosTrailler(linha, r);
                break;
            default:
                break;
        }
    }

    private static void lerAtributosTrailler(String linha, RegistroRemessa registroRemessa) {
        registroRemessa.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registroRemessa.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, linha));
        registroRemessa.put(DCCAttEnum.CodigoServico, StringUtilities.readString(2, 4, linha));
        registroRemessa.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(4, 7, linha));
        registroRemessa.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 17, linha));
        registroRemessa.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17, 25, linha));
        registroRemessa.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(25, 39, linha));
        registroRemessa.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(39, 47, linha));
        registroRemessa.put(DCCAttEnum.EmBranco2, StringUtilities.readString(47, 97, linha));
        registroRemessa.put(DCCAttEnum.EmBranco3, StringUtilities.readString(97, 391, linha));
        registroRemessa.put(DCCAttEnum.SequencialRetorno, StringUtilities.readString(391, 394, linha));
        registroRemessa.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }

    private static void lerAtributosDetalhe(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registro.put(DCCAttEnum.TipoPagador, StringUtilities.readString(1, 3, linha));
        registro.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(3, 17, linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(17, 21, linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(21, 22, linha));
        registro.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(22, 30, linha));
        registro.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(30, 31, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(31, 37, linha));
        registro.put(DCCAttEnum.UsoEmpresa, StringUtilities.readString(37, 62, linha));
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(62, 71, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(71, 102, linha));
        registro.put(DCCAttEnum.CodOcorrencia, StringUtilities.readString(102, 104, linha));
        registro.put(DCCAttEnum.Motivo, StringUtilities.readString(104, 107, linha));
        registro.put(DCCAttEnum.TipoCarteira, StringUtilities.readString(107, linha));
        registro.put(DCCAttEnum.StatusVenda, StringUtilities.readString(108, 110, linha));
        registro.put(DCCAttEnum.DataEntrada, StringUtilities.readString(110, 116, linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(116, 126, linha));
        registro.put(DCCAttEnum.ConfirmacaoNossoNumero, StringUtilities.readString(126, 135, linha));
        registro.put(DCCAttEnum.EmBranco2, StringUtilities.readString(135, 146, linha));
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(165, 168, linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(168, 172, linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(172, 173, linha));
        registro.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));
        registro.put(DCCAttEnum.Tarifa, StringUtilities.readString(175, 188, linha));
        registro.put(DCCAttEnum.DespesaOutras, StringUtilities.readString(188, 201, linha));
        registro.put(DCCAttEnum.Zeros, StringUtilities.readString(201, 214, linha));
        registro.put(DCCAttEnum.IOFDesconto, StringUtilities.readString(214, 227, linha));
        registro.put(DCCAttEnum.Abatimento, StringUtilities.readString(227, 240, linha));
        registro.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(266, 279, linha));
        registro.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(279, 292, linha));
        registro.put(DCCAttEnum.Moeda, StringUtilities.readString(292, 295, linha));
        registro.put(DCCAttEnum.DataCredito, StringUtilities.readString(295, 301, linha));
        registro.put(DCCAttEnum.EmBranco3, StringUtilities.readString(301, 307, linha));
        registro.put(DCCAttEnum.ContaMovimentoCedente, StringUtilities.readString(307, 321, linha));
        registro.put(DCCAttEnum.BoletoDDA, StringUtilities.readString(321,linha));
        registro.put(DCCAttEnum.CodigoLiquidacao, StringUtilities.readString(322, 324, linha));
        registro.put(DCCAttEnum.EmBranco4, StringUtilities.readString(324, 376, linha));
        registro.put(DCCAttEnum.SeuNumero, StringUtilities.readString(376, 391, linha));
        registro.put(DCCAttEnum.SequencialRetorno, StringUtilities.readString(391, 394, linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }

    /**
     * Realiza a leitura do header do arquivo de retorno do Sicoob no padrão CNAB400
     */
    private static void lerAtributosHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registro.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));
        registro.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));
        registro.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));
        registro.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 19, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(19, 26, linha));
        registro.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.readString(26, 40, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(40, 46, linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));
        registro.put(DCCAttEnum.Banco, StringUtilities.readString(79, 84, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(84, 94, linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(100, 391, linha));
        registro.put(DCCAttEnum.SequencialRetorno, StringUtilities.readString(391, 394, linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }


    public static String calcularDVNossoNumero(String identificador) {
        return new JBoletoBean().getModulo11(identificador, 9, true);
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.itau;

import br.com.pactosolucoes.comuns.util.Formatador;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaItauDCO extends LayoutRemessaBase {
    
    public static void preencherArquivoRemessa(RemessaVO remessa) {
        
        RegistroRemessa headerArquivo = preencherHeaderArquivo(remessa);
        remessa.setHeaderRemessaArquivo(headerArquivo);
        
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito =  remessa.getDataRegistro();
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco(),
                MathContext.UNLIMITED), 3));
        header.put(DCCAttEnum.CodigoServico, "0001");//
        header.put(DCCAttEnum.TipoRegistro, "1");//1
        header.put(DCCAttEnum.TipoOperacao, "D");//1
        header.put(DCCAttEnum.TipoServico, "05");//1
        header.put(DCCAttEnum.FormaLancamento, "50");//1

        header.put(DCCAttEnum.VersaoLayout, "030");//1
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        header.put(DCCAttEnum.CpfOuCnpj, "2");
        header.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
        
        header.put(DCCAttEnum.NumeroEstabelecimento,
                StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getNumeroContrato(), 13));
        
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(7));
        header.put(DCCAttEnum.EmBranco3, "0");
        //agencia
        header.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 4));
        header.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(1));
        header.put(DCCAttEnum.EmBranco5, "0000000");
        //conta
        header.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 5));
        header.put(DCCAttEnum.EmBranco6, StringUtilities.formatarCampoEmBranco(1));
//dac
        header.put(DCCAttEnum.AgenciaDepositariaDigito, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV(), 1));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(
                remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        
        header.put(DCCAttEnum.EmBranco7, StringUtilities.formatarCampoEmBranco(40));
        header.put(DCCAttEnum.EnderecoEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getEndereco(), 30));
        header.put(DCCAttEnum.EnderecoNumeroEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getEmpresa().getNumero().trim().replaceAll("[^0123456789]", ""), 5));
        header.put(DCCAttEnum.EnderecoComplementoEmpresa, StringUtilities.formatarCampoEmBranco(Formatador.removerMascara(remessa.getConvenioCobranca().getEmpresa().getComplemento()), 15));
        header.put(DCCAttEnum.EnderecoCidadeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getCidade_Apresentar(), 20));
        header.put(DCCAttEnum.CEP, StringUtilities.formatarCampoEmBranco(Formatador.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCEP()), 8));
        header.put(DCCAttEnum.EnderecoEstadoEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getEstado().getSigla(), 2));
        header.put(DCCAttEnum.EmBranco8, StringUtilities.formatarCampoEmBranco(8));
        header.put(DCCAttEnum.IdentificacaoOcorrencia, StringUtilities.formatarCampoEmBranco(10));
        
        
        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 0;
        double valor = 0.0;
        for (RemessaItemVO item : lista) {
            
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(DCCAttEnum.BancoCompensacao, "341");
            detail.put(DCCAttEnum.Identificacao, "0001");
            detail.put(DCCAttEnum.TipoRegistro, "3");//1
            seq++;
            detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 5));
            detail.put(DCCAttEnum.Segmento, "A");
            detail.put(DCCAttEnum.Instrucao, "000");
            detail.put(DCCAttEnum.Compensacao, "000");
            detail.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getConvenioCobranca().getBanco().getCodigoBanco()), 3));
            detail.put(DCCAttEnum.EmBranco, "0");
            
            detail.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampo(
                    new BigDecimal(item.get(DCCAttEnum.AgenciaDebito.name()), MathContext.UNLIMITED), 4));
            
            detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(1));
            detail.put(DCCAttEnum.EmBranco3, "0000000");
//            Criado tratamento para não impedir o arquivo de ir, caso entre com mais dígitos do que deveria na conta.
            String contacorrente = item.get(DCCAttEnum.ContaCorrenteDebito.name());
            if (contacorrente.length() > 5) {
                contacorrente = contacorrente.substring(0, 5);
            }
            detail.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampo(new BigDecimal(contacorrente), 5));
            
            detail.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(1));
            
            detail.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.formatarCampo(
                    new BigDecimal(item.get(DCCAttEnum.ContaCorrenteDebitoDigito.name()),
                    MathContext.UNLIMITED), 1));
            
            detail.put(DCCAttEnum.NomePessoa, StringUtilities.formatarCampoEmBranco(
                    item.getMovParcela().getPessoa().getNome(), 30));
            detail.put(DCCAttEnum.NumeroComprovanteVenda,
                    StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getCodigo()), 15));
            
            detail.put(DCCAttEnum.EmBranco5, StringUtilities.formatarCampoEmBranco(5));
            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(getDataVencimento(item.getMovParcela().getDataVencimento(), dataDeposito, remessa), "ddMMyyyy"));//6
            detail.put(DCCAttEnum.Moeda, "REA");
            detail.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));
            detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoEmBranco(20));
            detail.put(DCCAttEnum.DataDeposito,
                    StringUtilities.formatarCampoEmBranco(8));//8
            //valor cobrado
            detail.put(DCCAttEnum.ValorPago, StringUtilities.formatarCampoEmBranco(15));
            detail.put(DCCAttEnum.TipoMora, "00");
            detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoZerado(17));
            detail.put(DCCAttEnum.InformacaoHistorico, StringUtilities.formatarCampoEmBranco(16));
            detail.put(DCCAttEnum.EmBranco6, StringUtilities.formatarCampoEmBranco(04));
            
            String cpfCnpj = item.get(DCCAttEnum.CpfOuCnpj.name());
            if(UteisValidacao.emptyString(cpfCnpj)){
                cpfCnpj = item.getPessoa().getCfp().replaceAll("[.,-]", "");
            }
            detail.put(DCCAttEnum.InscricaoPagador, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 14));//14
            detail.put(DCCAttEnum.IdentificacaoOcorrencia, StringUtilities.formatarCampoEmBranco(10));
            if (item.getMovPagamento().getCodigo().intValue() != 0) {
                qtdAceito += 1;
                
            }
            valor += item.getValorItemRemessa();


            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtdAceito);
        
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
//        CÓDIGO DO BANCO CÓDIGO BANCO NA COMPENSAÇÃO 001 003 9(03)'341'
        trailer.put(DCCAttEnum.BancoCompensacao, "341");
//        CÓDIGO DO LOTE LOTE IDENTIFICAÇÃO DE SERVIÇO 004 007 9(04) NOTA 5
        trailer.put(DCCAttEnum.Identificacao, "0001");
//TIPO DE REGISTRO REGISTRO TRAILER DE LOTE 008 008 9(01) '5' 
        trailer.put(DCCAttEnum.TipoRegistro, "5");
//BRANCOS COMPLEMENTO 009 017 X(09) BRANCOS
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
//TOTAL QTDE REGISTROS QTDE REGISTROS DO LOTE 018 023 9(06) NOTA 15 
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(
                new BigDecimal(seq + 2), 6));
//TOTAL VALOR DÉBITOS SOMA VALOR DOS DÉBITOS DO LOTE 024 041 9(16)V9(2) NOTA 14
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(valor, 18));
//TOTAL QTDE. DE MOEDAS SOMATÓRIA DA QTDE DE MOEDAS DO LOTE 042 059 9(13)V9(5) NOTA 14 
        trailer.put(DCCAttEnum.QTDIOF, StringUtilities.formatarCampoMonetario(0, 18));
//BRANCOS COMPLEMENTO DE REGISTRO 060 230 X(171) BRANCOS 
        trailer.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(171));
//OCORRÊNCIAS CÓDIGOS OCORRÊNCIAS P/ RETORNO  231 240 X(10) BRANCOS
        trailer.put(DCCAttEnum.IdentificacaoOcorrencia, StringUtilities.formatarCampoEmBranco(10));
        
        
        RegistroRemessa trailerArquivo = preencherTrailerArquivo(remessa, seq);
        remessa.setTrailerRemessaArquivo(trailerArquivo);
        
        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);
        
        remessa.setTrailerArquivo(new StringBuilder(trailerArquivo.toString()));
        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setHeaderArquivo(new StringBuilder(headerArquivo.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
        
    }
    
    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(7, 8, linha));
                    if (tipo.equals(1)) {
                        lerAtributos(linha, h);   
                        break;
                    }
            }
        }
        return h;
    }
    public static RegistroRemessa obterHeaderArquivoRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(7, 8, linha));
                    if (tipo.equals(0)) {
                        lerAtributosRetornoHeaderArquivo(linha, h);   
                        break;
                    }
            }
        }
        return h;
    }
    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(7, 8, linha));
                    if (tipo.equals(3)) {
                        codigos.add(Integer.valueOf(StringUtilities.readString(73, 88, linha)));
                    }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for(Integer cod : codigos){
            cods = cods +","+cod;
        }
        return cods.replaceFirst(",", "");
    }
    
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            RegistroRemessa harq = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
            RegistroRemessa tarq = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(7, 8, linha));
                switch (tipo) {
                    //header arquivo
                    case 0:
                        lerAtributosRetornoHeaderArquivo(linha, harq);
                        break;
                    //header lote
                    case 1:
                        lerAtributos(linha, h);
                        break;
                    //detalhe
                    case 3:
                        RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                        lerAtributos(linha, detail);
                        listaDetalheRetorno.add(detail);
                        break;
                    //trailer lote
                    case 5:
                        lerAtributos(linha, t);
                        break;
                    //trailer arquivo
                    case 9:
                        lerAtributosRetornoHeaderArquivo(linha, tarq);
                        break;
                    
                }
            }
            remessa.setHeaderRemessaArquivo(harq);
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            remessa.setTrailerRemessaArquivo(tarq);
            
//            validarArquivoRemessaRetorno(remessa);
            //Analisar Trailer
            RegistroRemessa tRem = remessa.getTrailerRemessa();
            RegistroRemessa tRet = remessa.getTrailerRetorno();
            List<ObjetoGenerico> attrTrailer = tRem.getAtributos();
            for (int i = 0; i < attrTrailer.size(); i++) {
                ObjetoGenerico obj = attrTrailer.get(i);
                preencherAtributosTransientes(remessa, tRet.getAtributos().get(i));
            }
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }
    
    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
//            CÓDIGO DO BANCO CÓDIGO BANCO NA COMPENSAÇÃO 001 003 9(03) '341' 
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0, 3, linha));//1
//            CÓDIGO DO LOTE LOTE IDENTIFICAÇÃO DE SERVIÇO 004 007 9(04) NOTA 5
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(3, 7, linha));//1
//TIPO DE REGISTRO REGISTRO HEADER DE LOTE 008 008 9(01) '1' 
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));//1


//TIPO DE OPERAÇÃO TIPO DA OPERAÇÃO 009 009 X(01) 'D'
            r.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(8, 9, linha));//1


//SERVIÇO  TIPO DE SERVIÇO 010 011 9(02) ?05? 
            r.put(DCCAttEnum.TipoServico, StringUtilities.readString(9, 11, linha));//1


//FORMALANCTO FORMA DE LANÇAMENTO 012 013 9(02) ?50?
            r.put(DCCAttEnum.FormaLancamento, StringUtilities.readString(11, 13, linha));//1

//LAYOUT Nº DA VERSÃO DO LAYOUT 014 016 X(03) ?030?
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(13, 16, linha));//1

//BRANCOS COMPLEMENTO DE REGISTRO 017 017 X(01) BRANCOS
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(16, 17, linha));//1

//EMPRESA - INSCRIÇÃO TIPO INSCRIÇÃO EMPRESA CREDITADA 018 018 9(01) '1' = CPF '2' = CGC 
            r.put(DCCAttEnum.CpfOuCnpj, StringUtilities.readString(17, 18, linha));//1

//        INSCRIÇÃO NÚMERO CGC EMPRESA OU CPF CREDITADA 019 032 9(14) NOTA 1
            r.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(18, 32, linha));//1

//CONVÊNIO CÓDIGO DO CONVÊNIO NO BANCO 033 045 X(13) NOTA 2 
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(32, 45, linha));//1

//BRANCOS COMPLEMENTO DE REGISTRO 046 052 X(07) BRANCOS
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(45, 52, linha));//1

//BRANCOS  COMPLEMENTO DE REGISTRO 053 053 9(01) ?0? 
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(52, 53, linha));//1
//AGÊNCIA NÚMERO AGÊNCIA MANTEDORA DA CONTA 054 057 9(04) NOTA 1 
            r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(53, 57, linha));//1
//BRANCOS COMPLEMENTO DE REGISTRO  058 058 X(01)BRANCOS 
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(57, 58, linha));//1
//        BRANCOS COMPLEMENTO DE REGISTRO 059 065 9(07) ?0000000?
            r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(58, 65, linha));//1
//CONTA NÚMERO DE C/C CREDITADA 066 070 9(05) NOTA 1 
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(65, 70, linha));//1
//BRANCOS COMPLEMENTO DE REGISTRO 071 071 X(01) BRANCOS
            r.put(DCCAttEnum.EmBranco6, StringUtilities.readString(70, 71, linha));//1
//DAC DAC AGÊNCIA CONTA CREDITADA 072 072 9(01) NOTA 1 
            r.put(DCCAttEnum.AgenciaDepositariaDigito, StringUtilities.readString(71, 72, linha));//1
//NOME DA EMPRESA NOME DA EMPRESA 073 102 X(30)
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(72, 102, linha));//1
//BRANCOS COMPLEMENTO DE REGISTRO 103 142 X(40) BRANCOS 
            r.put(DCCAttEnum.EmBranco7, StringUtilities.readString(102, 142, linha));//1
//        ENDEREÇO EMPRESA NOME DA RUA, AV, PÇA, ETC... 143 172 X(30)
            r.put(DCCAttEnum.EnderecoEmpresa, StringUtilities.readString(142, 172, linha));//1
//NÚMERO NÚMERO DO LOCAL 173 177 9(05) 
            r.put(DCCAttEnum.EnderecoNumeroEmpresa, StringUtilities.readString(172, 177, linha));//1
//COMPLEMENTO CASA, APTO, SALA, ETC... 178 192 X(15) 
            r.put(DCCAttEnum.EnderecoComplementoEmpresa, StringUtilities.readString(177, 192, linha));//1
//        CIDADE NOME DA CIDADE 193 212 X(20) 
            r.put(DCCAttEnum.EnderecoCidadeEmpresa, StringUtilities.readString(192, 212, linha));//1
//CEP CEP 213 220 9(08)
            r.put(DCCAttEnum.CEP, StringUtilities.readString(212, 220, linha));//1
//ESTADO SIGLA DO ESTADO 221 222 X(02)
            r.put(DCCAttEnum.EnderecoEstadoEmpresa, StringUtilities.readString(220, 222, linha));//1
//BRANCOS COMPLEMENTO DE REGISTRO 223 230 X(08) BRANCOS
            r.put(DCCAttEnum.EmBranco8, StringUtilities.readString(222, 230, linha));//1
//OCORRÊNCIAS CÓDIGO OCORRÊNCIAS P/RETORNO 231 240 X(10) NOTA 4 
            r.put(DCCAttEnum.IdentificacaoOcorrencia, StringUtilities.readString(230, 240, linha));//1
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {


//            CÓDIGO DO BANCO CÓDIGO BANCO NA COMPENSAÇÃO 001 0039(03) '341' 
            r.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(0, 3, linha));
//            CÓDIGO DO LOTE LOTE IDENTIFICAÇÃO DE SERVIÇO 004 007 9(04) NOTA 5
            r.put(DCCAttEnum.Identificacao, StringUtilities.readString(3, 7, linha));

//TIPO DE REGISTRO REGISTRO DETALHE DE LOTE 008 008 9(01) '3' 
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));

//NÚMERO DO REGISTRO Nº SEQUENCIAL REGISTRO NO LOTE 009 013 9(05) NOTA 6
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(8, 13, linha));

//SEGMENTO CÓDIGO SEGMENTO REG. DETALHE 014 014 X(01) 'A'
            r.put(DCCAttEnum.Segmento, StringUtilities.readString(13, 14, linha));

//CÓDIGO CÓDIGO DA INSTRUÇÃO PARA MOVIMENTO 015 017 9(03) NOTA 7
            r.put(DCCAttEnum.Instrucao, StringUtilities.readString(14, 17, linha));

//COMPENSAÇÃO CÓDIGO DA CÂMARA DE COMPENSAÇÃO 018 020 9(03) ?000?  
            r.put(DCCAttEnum.Compensacao, StringUtilities.readString(17, 20, linha));

//BANCO CÓDIGO DO BANCO 021 023 9(03) ?341?
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(20, 23, linha));

//BRANCOS COMPLEMENTO DE REGISTROS 024 024 9(01) ?0? 
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(23, 24, linha));

//AGÊNCIA Nº. AGÊNCIA DEBITADA 025 028 9(04) 
            r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(24, 28, linha));

//BRANCOS COMPLEMENTO DE REGISTROS 029 029 X(01) BRANCOS 
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(28, 29, linha));

//        BRANCOS COMPLEMENTO DE REGISTROS 030 036 9(07) ?0000000?
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(29, 36, linha));

//CONTA NR. DA CONTA DEBITADA 037 041 9(05) 
            r.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(36, 41, linha));

//BRANCOS COMPLEMENTO DE REGISTROS 042 042 X(01) BRANCOS
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(41, 42, linha));

//DAC DIGITO VERIFICADOR DA AG/CONTA 043 043 9(01)
            r.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(42, 43, linha));

//NOME NOME DO DEBITADO 044 073 X(30) 
            r.put(DCCAttEnum.NomePessoa, StringUtilities.readString(43, 73, linha));

//SEU NÚMERO NR. DO DOCUM. ATRIBUÍDO P/EMPRESA 074 088 X(15) NOTA 8 
            r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(73, 88, linha));

//BRANCOS COMPLENTO DE REGISTROS 089 093 X(05) BRANCOS
            r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(88, 93, linha));

//DATA AGENDADA DATA PARA O LANÇAMENTO DO DÉBITO 094 101 9(08) DDMMAAAA 
            r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(93, 101, linha));

//        TIPO TIPO DA MOEDA 102 104 X(03) NOTA 9 
            r.put(DCCAttEnum.Moeda, StringUtilities.readString(101, 104, linha));

//QUANTIDADE QUANTIDADE DA MOEDA OU IOF 105 119 9(10)V9(05) NOTA 10 
            r.put(DCCAttEnum.IOF, StringUtilities.readString(104, 119, linha));

//VALOR AGENDADO VALOR DO LANÇAMENTO PARA DÉBITO 120 134 9(13)V9(02) NOTA 10
            r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(119, 134, linha));

//NOSSO NÚMERO NR. DO DOCUM. ATRIBUÍDO PELO BANCO 135 154 X(20) NOTA 11 
            r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(134, 154, linha));

//DATA COBRADA DATA REAL DA EFETIVAÇÃO DO LANÇTO. 155 162 X(08) DDMMAAAA
            r.put(DCCAttEnum.DataDeposito, StringUtilities.readString(154, 162, linha));

//VALOR COBRADO VALOR REAL DA EFETIVAÇÃO DO LANÇTO. 163 177 9(13)V9(02)
            r.put(DCCAttEnum.ValorPago, StringUtilities.readString(162, 177, linha));

//TIPO DA MORA TIPO DO ENCARGO POR DIA DE ATRASO 178 179 9(02) NOTA 12
            r.put(DCCAttEnum.TipoMora, StringUtilities.readString(177, 179, linha));

//VALOR DA MORA VALOR DO ENCARGO P/ DIA DE ATRASO 180 196 9(17) NOTA 12 
            r.put(DCCAttEnum.MoraDia, StringUtilities.readString(179, 196, linha));

//COMPLEMENTO INFORMAÇÃO COMPL. P/ HISTÓRICO C/C 197 212 X(16) NOTA 13
            r.put(DCCAttEnum.InformacaoHistorico, StringUtilities.readString(196, 212, linha));

//BRANCO COMPLEMENTO DE REGISTRO 213 216 X(04) 
            r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(212, 216, linha));

//        Nº DE INSCRIÇÃO Nº DE INSCRIÇÃO DO DEBITADO (CPF/CNPJ) 217 230 9(14)
            r.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(216, 230, linha));
//OCORRÊNCIAS CÓDIGO DAS OCORRÊNCIAS P/ RETORNO 231 240 X(10) NOTA 4
            String status = StringUtilities.readString(230, 240, linha);//2

            r.put(DCCAttEnum.StatusVenda, status != null ? status.trim() : null);
            //Se o Débito foi efetuado, usar como Código de Autorização a Conta/Corrente do Débito realizado
            if (status != null && !status.isEmpty()
                    && (DCOItauStatusEnum.Status00.getId().equals(status.trim())
                    || DCOItauStatusEnum.Status03.getId().equals(status.trim()))) {
                r.put(DCCAttEnum.CodigoAutorizacao, r.getValue(DCCAttEnum.NossoNumero.name()));
            }
            
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
//            CÓDIGO DO BANCO CÓDIGO BANCO NA COMPENSAÇÃO 001 003 9(03) '341' 
            r.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(0, 3, linha));
//            CÓDIGO DO LOTE LOTE IDENTIFICAÇÃO DE SERVIÇO 004 007 9(04) NOTA 5
            r.put(DCCAttEnum.Identificacao, StringUtilities.readString(3, 7, linha));
//TIPO DE REGISTRO REGISTRO TRAILER DE LOTE 008 008 9(01)'5' 
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));
//BRANCOS COMPLEMENTO 009 017 X(09) BRANCOS
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(8, 17, linha));
//TOTAL QTDE REGISTROS QTDE REGISTROS DO LOTE 018 023 9(06) NOTA 15 
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17, 23, linha));
//TOTAL VALOR DÉBITOS SOMA VALOR DOS DÉBITOS DO LOTE 024 041 9(16)V9(2) NOTA 14
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(23, 41, linha));
//TOTAL QTDE. DE MOEDAS SOMATÓRIA DA QTDE DE MOEDAS DO LOTE 042 059 9(13)V9(5) NOTA 14 
            r.put(DCCAttEnum.QTDIOF, StringUtilities.readString(41, 59, linha));
//BRANCOS COMPLEMENTO DE REGISTRO 060 230 X(171) BRANCOS
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(59, 230, linha));
//OCORRÊNCIAS CÓDIGOS OCORRÊNCIAS P/ RETORNO 231 240 X(10) NOTA 4
            r.put(DCCAttEnum.IdentificacaoOcorrencia, StringUtilities.readString(230, 240, linha));
        }
    }
    
    public static RegistroRemessa preencherHeaderArquivo(RemessaVO remessa) {
        RegistroRemessa headerArquivo = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
//        CÓDIGO DO BANCO CÓDIGO DO BCO NA COMPENSAÇÃO 001 003 9(03) '341'
        headerArquivo.put(DCCAttEnum.CodigoBanco, "341");
//        CÓDIGO DO LOTE LOTE DE SERVIÇO 004 007 9(04) '0000'
        headerArquivo.put(DCCAttEnum.CodigoServico, "0000");
//        TIPO DE REGISTRO REGISTRO HEADER DE ARQUIVO 008 008 9(01)'0'
        headerArquivo.put(DCCAttEnum.TipoRegistro, "0");
//        BRANCOS COMPLEMENTO DE REGISTRO 009 017 X(09) BRANCOS
        headerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
//        CÓDIGO DE INSCRIÇÃO TIPO DE INSCRIÇÃO DA EMPRESA 018 018 9(01) '1' = CPF '2' = CGC
        headerArquivo.put(DCCAttEnum.CpfOuCnpj, "2");
//        NÚMERO DE INSCRIÇÃO NÚMERO DO CGC/CPF DA EMPRESA 019 032 9(14) NOTA 1
        headerArquivo.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoEmBranco(
                Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
//        CONVÊNIO CÓDIGO DO CONVÊNIO NO BANCO 033 045 X(13) NOTA 2
        headerArquivo.put(DCCAttEnum.NumeroEstabelecimento,
                StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getNumeroContrato(), 13));
//        BRANCOS COMPLEMENTO DE REGISTRO 046 052 X(07) BRANCOS
        headerArquivo.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(7));
//          BRANCOS COMPLEMENTO DE REGISTRO 053 053 9(01) 0.
        headerArquivo.put(DCCAttEnum.EmBranco3, "0");
//        AGENCIA AGENCIA REFERENTE CONVÊNIO ASSINADO 054 057 9(04) NOTA 1
        headerArquivo.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 4));
//BRANCOS COMPLEMENTO DE REGISTRO 058 058 X(01)
        headerArquivo.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(1));
//BRANCOS BRANCOS COMPLEMENTO DE REGISTRO 059 065 9(07) ?0000000?
        headerArquivo.put(DCCAttEnum.EmBranco5, "0000000");
//        CONTA NÚMERO DA C/C DO CLIENTE 066 0709(05)NOTA 1 
        headerArquivo.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoEmBranco(
                remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 5));
//        BRANCOS COMPLEMENTO DE REGISTRO 071 071 X(01) BRANCOS
        headerArquivo.put(DCCAttEnum.EmBranco6, StringUtilities.formatarCampoEmBranco(1));
//        DAC DAC DA AGÊNCIA/ CONTA 072 072 9(01) NOTA 1
        headerArquivo.put(DCCAttEnum.AgenciaDepositariaDigito,
                StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV(), 1));
//         NOME NOME DA EMPRESA 073 102 X(30)
        headerArquivo.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(
                remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
//         ANCO NOME DO BANCO 103 132 X(30)BANCO ITAU 
        headerArquivo.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco("BANCO ITAU", 30));
//         BRANCOS COMPLEMENTO DE REGISTRO 133 142 X(10) BRANCOS
        headerArquivo.put(DCCAttEnum.EmBranco7, StringUtilities.formatarCampoEmBranco(10));
//        CÓDIGO CÓDIGO REMESSA/RETORNO 143 143 9(01)1 = REMESSA 2 = RETORNO 
        headerArquivo.put(DCCAttEnum.CodigoOperacao, "1");
//        DATA DA GERAÇÃO DATA DE GERAÇÃO DO ARQUIVO 144 151 9(08) DDMMAAAA
        headerArquivo.put(DCCAttEnum.DataGeracao,
                StringUtilities.formatarCampoData(remessa.getDataRegistro(), "ddMMyyyy"));
//        HORA DA GERAÇÃO HORA DE GERAÇÃO DO ARQUIVO 152 157 9(06) HHMMSS
        headerArquivo.put(DCCAttEnum.HoraGeracao,
                StringUtilities.formatarCampoData(remessa.getDataRegistro(), "HHmmss"));
//        SEQUÊNCIA NR. SEQUENCIAL DO ARQUIVO 158 163 9(06) NOTA 3

        String sequencialArquivo = "";
        try {
            sequencialArquivo = remessa.getProps().get(DCCAttEnum.NumeroResumoOperacoes.name());
        } catch (Exception e) {
        }
        sequencialArquivo = UteisValidacao.emptyString(sequencialArquivo) ?
                StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getSequencialDoArquivo()), 6) :
                sequencialArquivo;
        remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), sequencialArquivo);

        headerArquivo.put(DCCAttEnum.NumeroResumoOperacoes, sequencialArquivo);
//LAYOUT NR. DA VERSÃO DO LAYOUT 164 166 9(03) ?040. 
        headerArquivo.put(DCCAttEnum.CodigoOperacao, "040");
//UNIDADE DE DENSIDADE DENSIDADE DE GRAVAÇÃO DO ARQUIVO 167 171 9(05) ?00000.
        headerArquivo.put(DCCAttEnum.DensidadeGravacao, "00000");
//RESERVADO DO BANCO PARA USO RESERVADO DO BANCO 172 191 X(20)
        headerArquivo.put(DCCAttEnum.EmBranco8, StringUtilities.formatarCampoEmBranco(20));
//BRANCOS COMPLEMENTO DE REGISTRO 192 240 X(49) BRANCOS 
        headerArquivo.put(DCCAttEnum.EmBranco9, StringUtilities.formatarCampoEmBranco(49));
        
        return headerArquivo;
    }
    
    public static RegistroRemessa preencherTrailerArquivo(RemessaVO remessa, int qtdRegistros) {
        
        RegistroRemessa trailerArquivo = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);
//        CÓDIGO DO BANCO CÓDIGO BANCO NA COMPENSAÇÃO 001 003 9(03) '341' 
        trailerArquivo.put(DCCAttEnum.BancoCompensacao, "341");
//        CÓDIGO DO LOTE LOTE IDENTIFICAÇÃO DE SERVIÇO 004 007 9(04) '9999'
        trailerArquivo.put(DCCAttEnum.Identificacao, "9999");
//TIPO DE REGISTRO REGISTRO TRAILER DE ARQUIVO 008 008 9(01) '9' 
        trailerArquivo.put(DCCAttEnum.TipoRegistro, "9");
//BRANCOS COMPLEMENTO DE REGISTRO 009 017 X(09) BRANCOS
        trailerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
//TOTAL QTDE DE LOTES QTDE LOTES DO ARQUIVO 018 023 9(06) NOTA 15 
        trailerArquivo.put(DCCAttEnum.QuantidadeLotes, StringUtilities.formatarCampo(
                new BigDecimal(1), 6));
//TOTAL QTDE REGISTROS QTDE REGISTROS DO ARQUIVO 024 029 9(06) NOTA 15
//        TOTAL QTDE REGISTROS = quantidade dos registros tipo 0,1,3,5 e 9.
        trailerArquivo.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(
                new BigDecimal(1 + 1 + qtdRegistros + 1 + 1), 6));
//BRANCOS COMPLEMENTO DE REGISTRO 030 240 X(211) BRANCOS
        trailerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(211));
        
        return trailerArquivo;
    }
    
    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();

//        sb.append("<table>");

        //HEADER
        sb.append(remessa.getHeaderRemessaArquivo().toStringBuffer()).append("\r\n");
        sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuffer sbDetail = new StringBuffer();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer()).append("\r\n");
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer()).append("\r\n");;
        sb.append(remessa.getTrailerRemessaArquivo().toStringBuffer());
        sb.append("\r\n");

//        sb.append("</table>");

        return sb;
    }
    
    public static void lerAtributosRetornoHeaderArquivo(final String linha, RegistroRemessa r) {
//        CÓDIGO DO BANCO CÓDIGO DO BCO NA COMPENSAÇÃO 001 003 9(03) '341' 
        r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0, 3, linha));
//        CÓDIGO DO LOTE LOTE DE SERVIÇO 004 007 9(04) '0000'
        r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(3, 7, linha));
//TIPO DE REGISTRO REGISTRO HEADER DE ARQUIVO 008 008 9(01) '0' 
        r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));
//BRANCOS COMPLEMENTO DE REGISTRO 009 017 X(09) BRANCOS
        r.put(DCCAttEnum.CpfOuCnpj, StringUtilities.readString(8, 17, linha));
//CÓDIGO DE INSCRIÇÃO TIPO DE INSCRIÇÃO DA EMPRESA 018 018 9(01) '1' = CPF '2' = CGC 
        r.put(DCCAttEnum.EmBranco, StringUtilities.readString(17, 18, linha));
//        NÚMERO DE INSCRIÇÃO NÚMERO DO CGC/CPF DA EMPRESA 019 032 9(14) NOTA 1
        r.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(18, 32, linha));
//CONVÊNIO CÓDIGO DO CONVÊNIO NO BANCO 033 045 X(13) NOTA 2 
        r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(32, 45, linha));
//BRANCOS COMPLEMENTO DE REGISTRO 046 052 X(07) BRANCOS 
        r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(45, 52, linha));
//        BRANCOS COMPLEMENTO DE REGISTRO 053 053 9(01) ?0? 
        r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(52, 53, linha));
//        AGENCIA AGENCIA REFERENTE CONVÊNIO ASSINADO 054 057 9(04) NOTA 1
        r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(53, 57, linha));
//        BRANCOS COMPLEMENTO DE REGISTRO 058 058 X(01)
        r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(57, 58, linha));
//        BRANCOS BRANCOS COMPLEMENTO DE REGISTRO 059 065 9(07) ?0000000?
        r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(59, 65, linha));
//        CONTA NÚMERO DA C/C DO CLIENTE 066 070 9(05) NOTA 1 
        r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(65, 70, linha));
//        BRANCOS COMPLEMENTO DE REGISTRO 071 071 X(01) BRANCOS 
        r.put(DCCAttEnum.EmBranco6, StringUtilities.readString(70, 71, linha));
//        DAC DAC DA AGÊNCIA/ CONTA 072 072 9(01) NOTA 1 
        r.put(DCCAttEnum.AgenciaDepositariaDigito, StringUtilities.readString(71, 72, linha));
//        NOME NOME DA EMPRESA 073 102 X(30)
        r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(72, 102, linha));
//        BANCO NOME DO BANCO 103 132 X(30) BANCO ITAU 
        r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(102, 132, linha));
//        BRANCOS COMPLEMENTO DE REGISTRO 133 142 X(10) BRANCOS
        r.put(DCCAttEnum.EmBranco7, StringUtilities.readString(132, 142, linha));
//        CÓDIGO CÓDIGO REMESSA/RETORNO 143 143 9(01) 1 = REMESSA 2 = RETORNO
        r.put(DCCAttEnum.CodigoOperacao, StringUtilities.readString(142, 143, linha));
//    DATA DA GERAÇÃO DATA DE GERAÇÃO DO ARQUIVO 144 151 9(08) DDMMAAAA
        r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(143, 151, linha));
//HORA DA GERAÇÃO HORA DE GERAÇÃO DO ARQUIVO 152 157 9(06) HHMMSS 
        r.put(DCCAttEnum.HoraGeracao, StringUtilities.readString(151, 157, linha));
//        SEQUÊNCIA NR. SEQUENCIAL DO ARQUIVO 158 163 9(06) NOTA 3
        r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(157, 163, linha));
//LAYOUT NR. DA VERSÃO DO LAYOUT 164 166 9(03) ?040? 
        r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(163, 166, linha));//1
//UNIDADE DE DENSIDADE DENSIDADE DE GRAVAÇÃO DO ARQUIVO 167 171 9(05) ?00000?
        r.put(DCCAttEnum.DensidadeGravacao, StringUtilities.readString(167, 171, linha));
//RESERVADO DO BANCO PARA USO RESERVADO DO BANCO 172 191 X(20)
        r.put(DCCAttEnum.EmBranco8, StringUtilities.readString(172, 191, linha));
//BRANCOS COMPLEMENTO DE REGISTRO 192 240 X(49) BRANCOS
        r.put(DCCAttEnum.EmBranco9, StringUtilities.readString(191, 240, linha));
    }
    
    public static void lerAtributosRetornoTrailerArquivo(final String linha, RegistroRemessa r) {
//        CÓDIGO DO BANCO CÓDIGO BANCO NA COMPENSAÇÃO 001 003 9(03) '341' 
        r.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(0, 3, linha));
//        CÓDIGO DO LOTE LOTE IDENTIFICAÇÃO DE SERVIÇO 004 007 9(04) '9999'
        r.put(DCCAttEnum.Identificacao, StringUtilities.readString(3, 7, linha));
//        TIPO DE REGISTRO REGISTRO TRAILER DE ARQUIVO 008 008 9(01) '9'
        r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7, 8, linha));
//        BRANCOS COMPLEMENTO DE REGISTRO 009 017 X(09) BRANCOS
        r.put(DCCAttEnum.EmBranco, StringUtilities.readString(8, 17, linha));
//        TOTAL QTDE DE LOTES QTDE LOTES DO ARQUIVO 018 023 9(06) NOTA 15 
        r.put(DCCAttEnum.QuantidadeLotes, StringUtilities.readString(17, 23, linha));
//        TOTAL QTDE REGISTROS QTDE REGISTROS DO ARQUIVO 024 029 9(06) NOTA 15
        r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(23, 29, linha));
//        BRANCOS COMPLEMENTO DE REGISTRO 030 240 X(211) BRANCOS
        r.put(DCCAttEnum.EmBranco, StringUtilities.readString(29, 240, linha));
        
    }
    
    public static void main(String... args) {
        String[] ar = new String[]{"2617901503200"};
        int i = 0;
        for (String a : ar) {
            i += a.length();
            System.out.println(a + " - " + a.length());            
        }
        System.out.println(i);
        
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.itau;

import servicos.impl.dcc.cielo.DCCCieloStatusEnum;

/**
 *
 * <AUTHOR>
 */
public enum DCOItauStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status00("00", "Débito efetuado"),
    Status01("01", "Débito não efetuado - Insuficiência de fundos"),
    Status02("02", "Débito cancelado"),
    Status03("03", "Débito autorizado pela agência - Efetuado"),
    StatusHA("HA", "Lote Não Aceito "),
    StatusHB("HB", "Inscrição Da Empresa Inválida Para O Contrato"),
    StatusHC("HC", "Convênio Com A Empresa Inexistente/Inválido Para O Contrato "),
    StatusAA("AA", "Controle Inválido"),
    StatusAB("AB", "Tipo De Operação Inválido "),
    StatusAC("AC", "Tipo De Serviço Inválido"),
    StatusAD("AD", "Forma de lançamento inválida"),
    StatusAF("AF", "Código De Convênio Inválido "),
    StatusAH("AH", "Nr. Sequencial Do Registro No Lote Inválido "),
    StatusAI("AI", "Código De Segmento De Detalhe Inválido"),
    StatusAJ("AJ", "Tipo De Movimento Inválido"),
    StatusAL("AL", "Código Do Banco Inválido"),
    StatusAM("AM", "Agência Mantedora Da Conta Corrente Do Debitado Inválida"),
    StatusAN("AN", "Conta Corrente/Dígito Verificadora Do Debitado Inválido"),
    StatusAP("AP", "Data Lançamento Inválida"),
    StatusAQ("AQ", "Tipo/Quantidade Da Moeda Inválida"),
    StatusAR("AR", "Valor Do Lançamento Inválido"),
    StatusAS("AS", "Parcela Vinculada"),
    StatusAT("AT", "Autorizada pelo Cliente"),
    StatusAU("AU", "Logradouro do Favorecido Não Informado"),
    StatusAV("AV", "Nº do Local do Favorecido Não Informado"),
    StatusAW("AW", "Cidade do Favorecido Não Informada"),
    StatusAX("AX", "CEP/Complemento do Favorecido Inválido"),
    StatusAY("AY", "Sigla do Estado do Favorecido Inválida"),
    StatusAZ("AZ", "Código/Nome do Banco Depositário Inválido"),
    StatusBD("BD", "Confirmação De Agendamento"),
    StatusIA("IA", "Tipo Do Encargo Inválido"),
    StatusIB("IB", "C/C Com Restrição"),
    StatusIC("IC", "C/C Do Debitado Em Liquidação "),
    StatusID("ID", "Valor Da Mora / Taxa Da Mora Inválida"),
    StatusIE("IE", "Conta Corrente Do Debitado Encerrada"),
    StatusIF("IF", "Taxa Da Mora Maior Que 50,00000 %"),
    StatusIG("IG", "Complemento De Histórico Inválido"),
    StatusIH("IH", "Conta Corrente Para Crédito Não Autorizada"),
    StatusII("II", "Cancelamento Não Encontrado "),
    StatusIK("IK", "Valor Do Débito Acima Do Limite "),
    StatusIL("IL", "Limite Diário De Débito Ultrapassado "),
    StatusIM("IM", "Cpf/Cnpj Do Debitado Inválido"),
    StatusIN("IN", "Cpf/Cnpj Do Debitado Não Pertence À Conta Corrente Indicada"),
    StatusIZ("IZ", "Reservado ( Data Da Mora)"),
    StatusTA("TA", "Lote Não Aceito - Totais Do Lote Com Diferença"),
    StatusNA("NA", "Não Autorizado - Cliente não autorizou o débito em conta"),
    StatusRC("RC", "Débito Recusado"),
    StatusPE("PE", "Pendente de Autorização");
    //
    private String id;
    private String descricao;

    private DCOItauStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOItauStatusEnum valueOff(String id) {
        DCOItauStatusEnum[] values = DCOItauStatusEnum.values();
        for (DCOItauStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCOItauStatusEnum.StatusNENHUM;
    }
}

package servicos.impl.dcc.itau;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

public class LayoutRemessaItauCNAB400BoletoAtualizado extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa headerArquivo = preencherHeaderArquivo(remessa);
        remessa.setHeaderRemessaArquivo(headerArquivo);

        Date dataDeposito = remessa.getDataRegistro();

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();

        int qtdAceito = 0;
        int seq = 1;
        double valor = 0.0;

        boolean cobrarJuros = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros();
        Double jurosDiaPercentual = 0.0;
        Double multa = 0.0;
        if (cobrarJuros) {
            jurosDiaPercentual = Uteis.arredondarForcando2CasasDecimais(remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica());
            multa = Uteis.arredondarForcando2CasasDecimais(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica());
        }

        for (RemessaItemVO item : lista) {
         RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            //001-001 9(01)
            detail.put(DCCAttEnum.TipoRegistro, "1");//1
            //002-003 9(02)
            detail.put(DCCAttEnum.CpfOuCnpj, "02");
            //004-017 9(14)
            detail.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
            //018-021 9(04)
            detail.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 4));
            //022-023 9(02)
            detail.put(DCCAttEnum.Zeros, "00");
            //024-028 9(05)
            detail.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 5));
            //029-029 9(01)
            detail.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV(), 1));
            //030-033 x(04)
            detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(4));
            //034-037 9(04)
            detail.put(DCCAttEnum.InstrucaoAlegacao, StringUtilities.formatarCampoZerado(4)); //para mais detalhes ver NOTA27 da documentação
            StringBuilder usoEmpresa = new StringBuilder();
            usoEmpresa.append(StringUtilities.formatarCampoEmBranco(StringUtilities.formatarCampoData(Calendario.proximoDiaUtil(dataDeposito, 5), "dd/MM/yyyy"), 25));
            //038-062 X(25)
            detail.put(DCCAttEnum.UsoEmpresa, usoEmpresa);
            String nossoNumero;
            nossoNumero = StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 8);
            //063-070 9(08)
            detail.put(DCCAttEnum.NossoNumero, nossoNumero);//8
            //071-083 9(08V9(5)
            detail.put(DCCAttEnum.Zeros2, StringUtilities.formatarCampoZerado(13));//QTDE DE MOEDA
            //084-086 9(03)
            detail.put(DCCAttEnum.Carteira, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 3));
            //087-107 X(21)
            detail.put(DCCAttEnum.UsoBanco, StringUtilities.formatarCampoEmBranco(21));
            //108-108 X(01)
            detail.put(DCCAttEnum.CodCarteira, "I");
            //109-110 (9(02)
            detail.put(DCCAttEnum.CodOcorrencia, "01");
            //111-120 (X(10)
            detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getCodigo().toString(), 10));
            //121-126 9(6)
            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto() == null ? Uteis.somarDias(Calendario.hoje(), 7) : item.getDataVencimentoBoleto(), "ddMMyy"));
            //127-139 9(11)V9(2)
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 13));//13
            //140-142 9(03)
            detail.put(DCCAttEnum.CodigoBanco, "341");
            //143-147 9(05)
            detail.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoZerado(5)); //NOTA5 da documentação para remessa de envio preecher com zeros, o Itaú define a agência cobradora pelo CEP do pagador.
            //148-149 x(02)
            detail.put(DCCAttEnum.EspecieTitulo, "99");
            //150-150 x(01)
            detail.put(DCCAttEnum.Identificacao, "N"); //Aceite
            //151-156 9(06)
            detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro(), "ddMMyy"));
            //157-158 x(02)
            detail.put(DCCAttEnum.Instrucao1, "10");
            //159-160 x(02)
            detail.put(DCCAttEnum.Instrucao2, "93");
            if (cobrarJuros) {
                detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoMonetario(jurosDiaPercentual, 13));
            } else {
                detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoZerado(13));
            }
            detail.put(DCCAttEnum.DataLimiteDesconto, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.ValorBaseIOF, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.CpfOuCnpj, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detail.put(DCCAttEnum.CpfCliente, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));
            detail.put(DCCAttEnum.NomePessoa, getNome(item, 30));

            detail.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(10));

            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getTipoEndereco().equals("RE") || endereco.getEnderecoCorrespondencia()) {
                    enderecoVO = endereco;
                    break;
                }
            }

            detail.put(DCCAttEnum.EnderecoPagador, retirarCaracteresInvalidos(StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco() + ", " + enderecoVO.getNumero() + ", " + enderecoVO.getComplemento(), 40)));
            detail.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 12));
            detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
            detail.put(DCCAttEnum.CidadePagador, retirarCaracteresInvalidos(StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade_Apresentar(), 15)));
            detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
            detail.put(DCCAttEnum.SacadorAvalista, StringUtilities.formatarCampoEmBranco( 30));
            detail.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(4));
            detail.put(DCCAttEnum.DataMora, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.Prazo, StringUtilities.formatarCampoZerado(2));
            detail.put(DCCAttEnum.EmBranco5, StringUtilities.formatarCampoEmBranco(1));
            seq++;
            detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

            if (item.getMovPagamento().getCodigo() != 0) {
                qtdAceito += 1;
            }
            valor += item.getValorBoleto();

            listaDetalhe.add(detail);

            if (remessa.getConvenioCobranca().isGerarMultaEJurosRemessaItauCNAB400() && cobrarJuros) {
                RegistroRemessa detailMulta = new RegistroRemessa(TipoRegistroEnum.DETALHE);

                //001-001 9(001)
                detailMulta.put(DCCAttEnum.TipoRegistro, "2");//2
                //002-002 X(001)
                if (remessa.getConvenioCobranca().getEmpresa().isUtilizarMultaValorAbsoluto()) {
                    detailMulta.put(DCCAttEnum.CobrarMulta, "1");//0 -> sem multa, 1 -> considerar valor em reais de multa, 2 -> considerar percentual de multa
                } else {
                    detailMulta.put(DCCAttEnum.CobrarMulta, "2");//0 -> sem multa, 1 -> considerar valor em reais de multa, 2 -> considerar percentual de multa
                }
                //003-010 9(008)
                detailMulta.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getMovParcela().getDataVencimento(), "ddMMyyyy"));
                //011-023 9(013) - 9(11)V9(2)
                detailMulta.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetario(multa, 13));
                //024-394 X(370)
                detailMulta.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(371));
                seq++;
                detailMulta.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

                listaDetalhe.add(detailMulta);
            }

        }
        remessa.setQtdAceito(qtdAceito);

        seq++;


        RegistroRemessa trailerArquivo = preencherTrailerArquivo(remessa, seq, valor);
        remessa.setHeaderRemessa(headerArquivo);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setTrailerRemessa(trailerArquivo);
        remessa.setTrailerRemessaArquivo(trailerArquivo);

        remessa.setHeaderArquivo(new StringBuilder(headerArquivo.toString()));
        remessa.setHead(new StringBuilder(headerArquivo.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
        remessa.setTrailerArquivo(new StringBuilder(trailerArquivo.toString()));
        remessa.setTrailer(new StringBuilder(trailerArquivo.toString()));

    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(0)) {
                    lerAtributos(linha, h);
                    break;
                }
            }
        }
        return h;
    }

    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1)) {
                    try {
                        codigos.add(Integer.valueOf(StringUtilities.readString(116, 126, linha).trim()));
                    }catch (Exception e){
                    }
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Integer cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }

    public static String obterCodigosNossoNumero(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1)) {
                    try {
                        codigos.add(Integer.valueOf(StringUtilities.readString(62, 70, linha).trim()));
                    }catch (Exception e){
                    }
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Integer cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                switch (tipo) {
                    //header arquivo
                    case 0:
                        lerAtributos(linha, h);
                        break;
                    //detalhe
                    case 1:
                        RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                        lerAtributos(linha, detail);
                        listaDetalheRetorno.add(detail);
                        break;
                    //trailer
                    case 9:
                        lerAtributos(linha, t);
                        break;

                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

//            validarArquivoRemessaRetorno(remessa);
            //Analisar Trailer
            RegistroRemessa tRem = remessa.getTrailerRemessa();
            RegistroRemessa tRet = remessa.getTrailerRetorno();
            List<ObjetoGenerico> attrTrailer = tRem.getAtributos();
            for (int i = 0; i < attrTrailer.size(); i++) {
                ObjetoGenerico obj = attrTrailer.get(i);
                preencherAtributosTransientes(remessa, tRet.getAtributos().get(i));
            }
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, 2, linha));
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));
            r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(26, 30, linha));
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(30, 32, linha));
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(32, 37, linha));
            r.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(37, 38, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(38, 46, linha));
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 94, linha));
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));
            r.put(DCCAttEnum.DensidadeGravacao, StringUtilities.readString(100, 105, linha));
            r.put(DCCAttEnum.UnidadeDensidade, StringUtilities.readString(105, 108, linha));
            //Nº Seq. Arquivo Ret. (Número Seqüencial do arquivo de retorno)
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(108, 113, linha));
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(113, 119, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(119, 394, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CpfOuCnpj, StringUtilities.readString(1, 3, linha));
            r.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(3, 17, linha));
            r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(17, 21, linha));
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(21, 23, linha));
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(23, 28, linha));
            r.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(28, 29, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(29, 37, linha));
            r.put(DCCAttEnum.UsoEmpresa, StringUtilities.readString(37, 62, linha));
            r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(62, 70, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(70, 82, linha));
            r.put(DCCAttEnum.Carteira, StringUtilities.readString(82, 85, linha));
            r.put(DCCAttEnum.NossoNumero2, StringUtilities.readString(85, 93, linha));
            r.put(DCCAttEnum.NossoNumero2DV, StringUtilities.readString(93, 94, linha));
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(94, 107, linha));
            r.put(DCCAttEnum.CodCarteira, StringUtilities.readString(107, 108, linha));
            r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(108, 110, linha));
            r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(110, 116, linha));
            r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(116, 126, linha));
            r.put(DCCAttEnum.NossoNumero3, StringUtilities.readString(126, 134, linha));
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(134, 146, linha));
            r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));
            r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(165, 168, linha));
            r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(168, 172, linha));
            r.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(172, 173, linha));
            r.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));
            r.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(175, 188, linha));
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(188, 214, linha));
            r.put(DCCAttEnum.ValorBaseIOF, StringUtilities.readString(214, 227, linha));
            r.put(DCCAttEnum.ValorAbatimento, StringUtilities.readString(227, 240, linha));
            r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));
            r.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));//??
            r.put(DCCAttEnum.Juros, StringUtilities.readString(266, 279, linha));
            r.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(279, 292, linha));
            r.put(DCCAttEnum.BoletoDDA, StringUtilities.readString(292, 293, linha));
            r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(293, 295, linha));
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(295, 301, linha));
            r.put(DCCAttEnum.InstrucaoCancelada, StringUtilities.readString(301, 305, linha));
            r.put(DCCAttEnum.EmBranco6, StringUtilities.readString(305, 311, linha));
            r.put(DCCAttEnum.Zeros2, StringUtilities.readString(311, 324, linha));
            r.put(DCCAttEnum.NomePessoa, StringUtilities.readString(324, 354, linha));
            r.put(DCCAttEnum.EmBranco7, StringUtilities.readString(354, 377, linha));
            r.put(DCCAttEnum.CodigoErro, StringUtilities.readString(377, 385, linha));
            r.put(DCCAttEnum.EmBranco8, StringUtilities.readString(385, 392, linha));
            r.put(DCCAttEnum.CodigoLiquidacao, StringUtilities.readString(392, 394, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, 2, linha));
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(2, 4, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(4, 7, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 13, linha));
            r.put(DCCAttEnum.QtdTitulosCobrancaSimples, StringUtilities.readString(17, 25, linha));
            r.put(DCCAttEnum.ValorTotalCobrancaSimples, StringUtilities.readString(25, 39, linha));
            r.put(DCCAttEnum.AvisoBancarioCobrancaSimples, StringUtilities.readString(39, 47, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(47, 57, linha));
            r.put(DCCAttEnum.QtdTitulosCobrancaVinculada, StringUtilities.readString(57, 65, linha));
            r.put(DCCAttEnum.ValorTotalCobrancaVinculada, StringUtilities.readString(65, 79, linha));
            r.put(DCCAttEnum.AvisoBancarioCobrancaVinculada, StringUtilities.readString(79, 87, linha));
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(87, 177, linha));
            r.put(DCCAttEnum.QtdTitulosCobrancaDireta, StringUtilities.readString(177, 185, linha));
            r.put(DCCAttEnum.ValorTotalCobrancaDireta, StringUtilities.readString(185, 199, linha));
            r.put(DCCAttEnum.AvisoBancarioCobrancaDireta, StringUtilities.readString(199, 207, linha));
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(207, 212, linha));
            r.put(DCCAttEnum.QtdTitulosBaixados, StringUtilities.readString(212, 220, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(220, 234, linha));
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(234, 394, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        }

    }

    public static RegistroRemessa preencherHeaderArquivo(RemessaVO remessa) {

        Date dataGeracao = remessa.getDataRegistro();

        RegistroRemessa headerArquivo = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
        headerArquivo.put(DCCAttEnum.TipoRegistro, "0");
        headerArquivo.put(DCCAttEnum.CodigoOperacao, "1");
        headerArquivo.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");
        headerArquivo.put(DCCAttEnum.CodigoServico, "01");
        headerArquivo.put(DCCAttEnum.IdentificacaoServico, StringUtilities.formatarCampoEmBranco("COBRANCA", 15));
        headerArquivo.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 4));
        headerArquivo.put(DCCAttEnum.Zeros, "00");
        headerArquivo.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 5));
        headerArquivo.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV(), 1));
        headerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
        headerArquivo.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        headerArquivo.put(DCCAttEnum.CodigoBanco, "341");
        headerArquivo.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco("BANCO ITAU SA", 15));
        headerArquivo.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyy"));
        headerArquivo.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(294));
        headerArquivo.put(DCCAttEnum.SequencialRegistro, "000001");

        return headerArquivo;
    }

    public static RegistroRemessa preencherTrailerArquivo(RemessaVO remessa, int qtdRegistros, double valorTotalBruto) {
        RegistroRemessa trailerArquivo = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);

        trailerArquivo.put(DCCAttEnum.TipoRegistro, "9");
        trailerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
        trailerArquivo.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros), 10));
        trailerArquivo.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(valorTotalBruto, 13));
        trailerArquivo.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(360));

        trailerArquivo.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros), 6));

        return trailerArquivo;
    }

    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();

//        sb.append("<table>");

        //HEADER
        sb.append(remessa.getHeaderRemessaArquivo().toStringBuffer()).append("\r\n");
        sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuffer sbDetail = new StringBuffer();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer()).append("\r\n");
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer()).append("\r\n");
        ;
        sb.append(remessa.getTrailerRemessaArquivo().toStringBuffer());
        sb.append("\r\n");

//        sb.append("</table>");

        return sb;
    }

    public static String obterCodigosRemessaItem(StringBuilder dados) throws IOException {
        List<Long> codigos = new ArrayList<Long>();
        if (dados.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(dados.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Long tipo = Long.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1L)) {
                    Long codRetorno = Long.valueOf(StringUtilities.readString(108, 110, linha));
                    if (!ignorarStatusVenda(codRetorno)) {
                        String valor = StringUtilities.readString(116, 126, linha);
                        if (valor.isEmpty() || valor.replaceAll(" ", "").isEmpty()) {
                            valor = StringUtilities.readString(126, 133, linha);
                        }
                        codigos.add(Long.valueOf(valor));
                    }
                }
            }
        }
        Collections.sort(codigos);
        StringBuilder cods = new StringBuilder();
        for (Long cod : codigos) {
            cods.append(",").append(cod);
        }
        return cods.toString().replaceFirst(",", "");
    }

    private static boolean ignorarStatusVenda(Long codRetorno) {
        return codRetorno.equals(54L) //Código 54: Tarifa Mensal;
            || codRetorno.equals(52L); //Código 52: Tarifa Mensal Baixas na Carteira;
    }
}

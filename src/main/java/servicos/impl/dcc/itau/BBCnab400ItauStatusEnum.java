package servicos.impl.dcc.itau;

/**
 * Created by GlaucoT on 28/04/2015
 */
public enum BBCnab400ItauStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status02("02", "Entrada Confirmada com possibilidade de mensagem"),
    Status03("03", "Entrada Rejeitada"),
    Status04("04", "Alteração de dados(nova entrada ou alteração/exclusãoados acatada)"),
    Status05("05", "Alteração de dados(baixa)"),
    Status06("06", "Liquidação normal"),
    Status07("07", "Liquidação parcial"),
    Status08("08", "Liquidação em cartório"),
    Status09("09", "Baixa Simples"),
    Status10("10", "Baixa por liquidação"),
    Status11("11", "Em ser(só no retorno mensal)"),
    Status12("12", "Abatimento Concedido"),
    Status13("13", "Abatimento Cancelado"),
    Status21("21", "Título não Registrado no Sistema"),
    Status9999("9999", "Retorno manual");

    private String id;
    private String descricao;

    BBCnab400ItauStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }


    public static BBCnab400ItauStatusEnum valueOff(String id) {
        BBCnab400ItauStatusEnum[] values = BBCnab400ItauStatusEnum.values();
        for (BBCnab400ItauStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return BBCnab400ItauStatusEnum.StatusNENHUM;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}

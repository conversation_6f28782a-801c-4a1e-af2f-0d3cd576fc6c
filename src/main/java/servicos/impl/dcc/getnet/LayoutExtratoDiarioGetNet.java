/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.getnet;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

import static servicos.impl.dcc.base.LayoutRemessaBase.preencherAtributosTransientes;

/**
 * Created by <PERSON><PERSON> on 11/10/2018.
 */
public class LayoutExtratoDiarioGetNet {
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {

        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();

            while ((linha = br.readLine()) != null) {

                if (linha.length() == 0) {
                    continue;
                }

                String inicioLinha = StringUtilities.readString(0, 1, linha);
                TipoRegistroExtratoGetNetEnum tipoRegGetNet = TipoRegistroExtratoGetNetEnum.valueOff(inicioLinha);

                if (tipoRegGetNet == null) {
                    continue;
                }

                if (tipoRegGetNet.getTipoRegistroEnum().equals(TipoRegistroEnum.HEADER)) {
                    h.put(DCCAttEnum.OpcaoExtrato, tipoRegGetNet.getTipoConciliacaoEnum().getCodigo());
                    lerAtributos(linha, h);
                } else if (tipoRegGetNet.getTipoRegistroEnum().equals(TipoRegistroEnum.DETALHE)) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (tipoRegGetNet.getTipoRegistroEnum().equals(TipoRegistroEnum.TRAILER)) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
//            if (t.toString().length() > 2) {
//                remessa.setTrailer(new StringBuilder(t.toString()));
//            }
//            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
//            for (ObjetoGenerico objetoGenerico : atributos) {
//                preencherAtributosTransientes(remessa, objetoGenerico);
//            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {

        if (r.getTipo() == TipoRegistroEnum.HEADER) {

//            1 Tipo de Registro 1 1 1 A Fixo = ?0?
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
//            2 Data de criação do arquivo 2 9 8 N Data (DDMMAAAA)
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(1, 9, linha));
//            3 Hora de criação do arquivo 10 15 6 N Hora (HHMMSS)
            r.put(DCCAttEnum.HoraGeracao, StringUtilities.readString(9, 15, linha));
//            4 Data de referência do Movimento 16 23 8 N Data movimento (DDMMAAAA)
            r.put(DCCAttEnum.DataVenda, StringUtilities.readString(15, 23, linha));
//            5 Arquivo e Versão 24 31 8 A Fixo = ?CEADM100?
            r.put(DCCAttEnum.NumeroArquivo, StringUtilities.readString(23, 31, linha));
//            6 Código do Estabelecimento (loja ou matriz) 32 46 15 A De acordo com o agrupamento no arq.
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(31, 46, linha));
//            7 CNPJ do adquirente 47 60 14 N CNPJ do adquirente
            r.put(DCCAttEnum.CnpjCliente, StringUtilities.readString(46, 60, linha));
//            8 Nome do adquirente 61 80 20 A Nome do adquirente
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(60, 80, linha));
//            9 Sequência 81 89 9 N Número sequencial de remessa
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(80, 89, linha));
//            10 Código do adquirente 90 91 2 A Código do adquirente
            r.put(DCCAttEnum.CodigoAdquirente, StringUtilities.readString(89, 91, linha));
//            11 Versão do Layout 92 116 25 A
//            Para as versões anteriores a V.8.0 serão mantidos os
//            padrões existentes.
//            Versão 7.0 - 200 bytes = nome: informação virá em branco
//            Versão 4.01 = nome: LAYOUT400POSICOES-V4
//            Versão 8.0 = nome:
//            Sant. v.8.0 400 bytes
//            Para reprocessamento de arquivos dessa versão, será
//            utilizado o nome:
//            Sant. reprocessamento
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(91, 116, linha));
//            12 Reservado para uso futuro 117 400 284 A Espaços
//            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(116, 11, linha));

        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {

            //Itentificar o tipo para determinar o Detalhe a ser utilizado
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));

            if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals(TipoRegistroExtratoGetNetEnum.TipoRegistro1.getId())) {


                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 16, linha));//ok
                r.put(DCCAttEnum.IdentificadorProduto, StringUtilities.readString(16, 18, linha));//ok
                r.put(DCCAttEnum.TipoCaptura, StringUtilities.readString(18, 21, linha));//ok
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(21, 30, linha));//ok
                r.put(DCCAttEnum.DataResumoVenda, StringUtilities.readString(30, 38, linha));//ok
                r.put(DCCAttEnum.DataCredito, StringUtilities.readString(38, 46, linha));//ok
                r.put(DCCAttEnum.Banco, StringUtilities.readString(46, 49, linha));//ok
                r.put(DCCAttEnum.Agencia, StringUtilities.readString(49, 55, linha));//ok
                r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(55, 66, linha));//ok
                r.put(DCCAttEnum.QuantidadeAceitos, StringUtilities.readString(66, 75, linha));//ok
                r.put(DCCAttEnum.QtdCVRejeitado, StringUtilities.readString(75, 84, linha));//ok
                r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(84, 96, linha));//ok
                r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(96, 108, linha));//ok
                r.put(DCCAttEnum.ValorTarifa, StringUtilities.readString(108, 120, linha));//ok
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(120, 132, linha));//ok
                r.put(DCCAttEnum.ValorRejeitado, StringUtilities.readString(132, 144, linha));//ok
                r.put(DCCAttEnum.ValorCredito, StringUtilities.readString(144, 156, linha));//ok
                r.put(DCCAttEnum.ValorEncargos, StringUtilities.readString(156, 168, linha));//ok
                r.put(DCCAttEnum.TipoPagamento, StringUtilities.readString(168, 170, linha));//ok
                r.put(DCCAttEnum.ParcelaResumoVenda, StringUtilities.readString(170, 172, linha));//ok
                r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(172, 174, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.readString(174, 189, linha));//ok
                r.put(DCCAttEnum.NumeroOperacaoAntecipacao, StringUtilities.readString(189, 204, linha));//ok
                r.put(DCCAttEnum.DataResumoVendaAntecipado, StringUtilities.readString(204, 212, linha));//ok
                r.put(DCCAttEnum.CustoOperacao, StringUtilities.readString(212, 224, linha));//ok
                r.put(DCCAttEnum.ValorLiquidoAntecipado, StringUtilities.readString(224, 236, linha));//ok
                r.put(DCCAttEnum.NumeroOperacaoFinanceira, StringUtilities.readString(236, 254, linha));//ok
                r.put(DCCAttEnum.ValorLiquidoCobranca, StringUtilities.readString(254, 266, linha));//ok
                r.put(DCCAttEnum.IdentificadorCompensacao, StringUtilities.readString(266, 281, linha));//ok
                r.put(DCCAttEnum.Moeda, StringUtilities.readString(281, 284, linha));//ok
                r.put(DCCAttEnum.CodigoBaixa, StringUtilities.readString(284, 285, linha));//ok
                r.put(DCCAttEnum.IndicadorCredDeb, StringUtilities.readString(285, 287, linha));//ok

            } else if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals(TipoRegistroExtratoGetNetEnum.TipoRegistro2.getId())) {

                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 16, linha));//ok
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(16, 25, linha));//ok
                r.put(DCCAttEnum.NSU, StringUtilities.readString(25, 37, linha));//ok
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(25, 37, linha));//ok
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(37, 45, linha));//ok
                r.put(DCCAttEnum.HoraTransacao, StringUtilities.readString(45, 51, linha));//ok
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(51, 70, linha));//ok
                r.put(DCCAttEnum.ValorTransacao, StringUtilities.readString(70, 82, linha));//ok
                r.put(DCCAttEnum.ValorSaque, StringUtilities.readString(82, 94, linha));//ok
                r.put(DCCAttEnum.ValorTotalTaxaEmbarque, StringUtilities.readString(94, 106, linha));//ok
                r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(106, 108, linha));//ok
                r.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(108, 110, linha));//ok
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(110, 122, linha));//ok
                r.put(DCCAttEnum.DataPagamento, StringUtilities.readString(122, 130, linha));//ok
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(130, 140, linha));//ok
                r.put(DCCAttEnum.TipoCaptura, StringUtilities.readString(140, 143, linha));//ok
                r.put(DCCAttEnum.StatusTransacao, StringUtilities.readString(143, 144, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.readString(144, 159, linha));//ok
                r.put(DCCAttEnum.NumeroTerminal, StringUtilities.readString(159, 167, linha));//ok
                r.put(DCCAttEnum.Moeda, StringUtilities.readString(167, 170, linha));//ok
                r.put(DCCAttEnum.EmissorCartao, StringUtilities.readString(170, 171, linha));//ok
                r.put(DCCAttEnum.IndicadorCredDeb, StringUtilities.readString(171, 172, linha));//ok
                r.put(DCCAttEnum.Carteira, StringUtilities.readString(172, 175, linha));//ok

            } else if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals(TipoRegistroExtratoGetNetEnum.TipoRegistro3.getId())) {

                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 16, linha));//ok
                r.put(DCCAttEnum.NumeroResumoVenda, StringUtilities.readString(16, 25, linha));//ok
                r.put(DCCAttEnum.DataResumoVenda, StringUtilities.readString(25, 33, linha));//ok
                r.put(DCCAttEnum.DataPagamento, StringUtilities.readString(33, 41, linha));//ok
                r.put(DCCAttEnum.OrigemAjuste, StringUtilities.readString(41, 61, linha));//ok
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(61, 62, linha));//ok
                r.put(DCCAttEnum.IndicadorCredDeb, StringUtilities.readString(62, 63, linha));//ok
                r.put(DCCAttEnum.ValorAjuste, StringUtilities.readString(63, 75, linha));//ok
                r.put(DCCAttEnum.Motivo, StringUtilities.readString(75, 77, linha));//ok
                r.put(DCCAttEnum.DataCarta, StringUtilities.readString(77, 85, linha));//ok
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(85, 104, linha));//ok
                r.put(DCCAttEnum.NumeroResumoVendaOriginal, StringUtilities.readString(104, 113, linha));//ok
                r.put(DCCAttEnum.NSU, StringUtilities.readString(113, 125, linha));//ok
                r.put(DCCAttEnum.DataTransacao, StringUtilities.readString(125, 133, linha));//ok
                r.put(DCCAttEnum.TipoPagamento, StringUtilities.readString(133, 135, linha));//ok
                r.put(DCCAttEnum.NumeroTerminal, StringUtilities.readString(135, 143, linha));//ok
                r.put(DCCAttEnum.DataPagamentoOriginal, StringUtilities.readString(143, 151, linha));//ok
                r.put(DCCAttEnum.Moeda, StringUtilities.readString(151, 154, linha));//ok

            } else if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals(TipoRegistroExtratoGetNetEnum.TipoRegistro4.getId())) {

                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 16, linha));//ok
                r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(16, 24, linha));//ok
                r.put(DCCAttEnum.DataCredito, StringUtilities.readString(24, 32, linha));//ok
                r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(32, 47, linha));//ok
                r.put(DCCAttEnum.ValorBrutoAntecipado, StringUtilities.readString(47, 59, linha));//ok
                r.put(DCCAttEnum.ValorTaxaAntecipado, StringUtilities.readString(59, 71, linha));//ok
                r.put(DCCAttEnum.ValorLiquidoAntecipado, StringUtilities.readString(71, 83, linha));//ok
                r.put(DCCAttEnum.TaxaComissao, StringUtilities.readString(83, 94, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.readString(94, 109, linha));//ok
                r.put(DCCAttEnum.Banco, StringUtilities.readString(109, 112, linha));//ok
                r.put(DCCAttEnum.Agencia, StringUtilities.readString(112, 118, linha));//ok
                r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(118, 129, linha));//ok
                r.put(DCCAttEnum.CanalAntecipacao, StringUtilities.readString(129, 132, linha));//ok
                r.put(DCCAttEnum.TipoPagamento, StringUtilities.readString(132, 134, linha));//ok

            }

        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {

            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(1, 10, linha));

        }
    }
}

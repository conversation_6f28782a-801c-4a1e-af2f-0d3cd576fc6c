/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.getnet;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 *
 * <AUTHOR>
 */
public enum DCOGetNetStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    //    00 Transação aceita
    Status00("00", "Transação aceita", CodigoRetornoPactoEnum.SUCESSO),
    //02 Código de autorização inválido
    Status02("02", "Código de autorização inválido"),
    //03 Número do estabelecimento inválido
    Status03("03", "Número do estabelecimento inválido"),
    //05 Número de parcelas inválido
    Status05("05", "Número de parcelas inválido"),
    //08 Valor de entrada inválido
    Status08("08", "Valor de entrada inválido"),
    //09 Valor da taxa de embarque inválido
    Status09("09", "Valor da taxa de embarque inválido"),
    //10 Valor da parcela inválido
    Status10("10", "Valor da parcela inválido"),
    //13 Valor da venda inválida
    Status13("13", "Valor da venda inválida"),
    //14 Número do cartão inválido
    Status14("14", "Número do cartão inválido", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_INVALIDO),
    //15 Valor do cancelamento inválido
    Status15("15", "Valor do cancelamento inválido"),
    //16 Transação original não localizada para cancelamento
    Status16("16", "Transação original não localizada para cancelamento"),
    //19 Cancelamento com prazo superior ao permitido (90 dias)
    Status19("19", "Cancelamento com prazo superior ao permitido (90 dias)"),
    //20 Transação já cancelada
    Status20("20", "Transação já cancelada"),
    //21 Valor do cancelamento maior que o valor da venda
    Status21("21", "Valor do cancelamento maior que o valor da venda"),
    //22 Valor do cancelamento maior que o permitido (alçada)
    Status22("22", "Valor do cancelamento maior que o permitido (alçada)"),
    //23 Número do resumo de venda (RV) original inválido
    Status23("23", "Número do resumo de venda (RV) original inválido"),
    //24 Número do comprovante de venda (CV) inválido
    Status24("24", "Número do comprovante de venda (CV) inválido"),
    //25 Data da venda inválida
    Status25("25", "Data da venda inválida"),
    //26 Modalidade da venda inválida
    Status26("26", "Modalidade da venda inválida"),
    //28 Valor financiado inválido
    Status28("28", "Valor financiado inválido"),
    //29 Quantidade de parcelas inválida
    Status29("29", "Quantidade de parcelas inválida"),
    //30 Número do terminal inválido
    Status30("30", "Número do terminal inválido"),
    //54 Não é permitido cancelamento de uma transação que está sendo contestada pelo portador
    Status54("54", "Não é permitido cancelamento de uma transação que está sendo contestada pelo portador"),
    //55 Débito efetuado conforme contestação do portador.
    Status55("55", "Débito efetuado conforme contestação do portador."),
    //61 Tipo de registro inválido
    Status61("61", "Tipo de registro inválido"),
    //62 Data do movimento do header inválida
    Status62("62", "Data do movimento do header inválida"),
    //63 Moeda do header inválida
    Status63("63", "Moeda do header inválida"),
    //64 Indicador do processo do header inválido
    Status64("64", "Indicador do processo do header inválido"),
    //65 Quantidade de registros do trailler não confere
    Status65("65", "Quantidade de registros do trailler não confere"),
    //66 Valor total da venda do trailler não confere
    Status66("66", "Valor total da venda do trailler não confere"),
    //67 Número da sequência do movimento inválido
    Status67("67", "Número da sequência do movimento inválido"),
    //68 Número da sequência do movimento repetido
    Status68("68", "Número da sequência do movimento repetido"),
    //69 Indicador de arquivo do header inválido
    Status69("69", "Indicador de arquivo do header inválido"),
    //70 Loja ou Matriz do header inválido
    Status70("70", "Loja ou Matriz do header inválido"),
    //                    71 Transação rejeitada pelo banco emissor
    Status71("71", "Transação rejeitada pelo banco emissor"),
    //73 Cartão com problema - reter o cartão
    Status73("73", "Cartão com problema - reter o cartão", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE),
    //74 Autorização negada
    Status74("74", "Autorização negada"),
    //79 Cartão perdido - reter o cartão
    Status79("79", "Cartão perdido - reter o cartão", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE),
    //80 Cartão roubado - reter o cartão
    Status80("80", "Cartão roubado - reter o cartão", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE),
    //81 Fundos insuficientes
    Status81("81", "Fundos insuficientes", OperacaoRetornoCobrancaEnum.REENVIAR, CodigoRetornoPactoEnum.SALDO_INSUFICIENTE),
    //82 Cartão vencido ou data do vencimento errada
    Status82("82", "Cartão vencido ou data do vencimento errada", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_VENCIDO),
    //92 Banco emissor sem comunicação
    Status92("92", "Banco emissor sem comunicação"),
    //95 Sem saldo em aberto
    Status95("95", "Sem saldo em aberto", OperacaoRetornoCobrancaEnum.REENVIAR, CodigoRetornoPactoEnum.SALDO_INSUFICIENTE),
    //99 Outros motivos
    Status99("99", "Outros motivos");
    
    
    private String id;
    private String descricao;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;
    private CodigoRetornoPactoEnum codigoRetornoPacto;

    private DCOGetNetStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
    }

    private DCOGetNetStatusEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.id = id;
        this.descricao = descricao;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    private DCOGetNetStatusEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca, CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.id = id;
        this.descricao = descricao;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOGetNetStatusEnum valueOff(String id) {
        DCOGetNetStatusEnum[] values = DCOGetNetStatusEnum.values();
        for (DCOGetNetStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCOGetNetStatusEnum.StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }
}

package servicos.impl.dcc.getnet;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import servicos.impl.dcc.base.TipoRegistroEnum;

/**
 * Created by <PERSON><PERSON> on 12/10/2018.
 */
public enum CodigosProdutosExtratoGetNetEnum {

    Produto1("1", "<PERSON>ítu<PERSON>", false),
    Produto2("2", "<PERSON>v<PERSON><PERSON>", false),
    Produto3("3", "Crédito Digital", false),
    Produto00("00", "Cupom Eletrônico", false),
    ProdutoCE("CE", "Cupom Eletrônico", false),
    ProdutoCP("CP", "Cupom Papel", false),
    ProdutoSM("SM", "Cartão Crédito MASTERCARD", true),
    ProdutoSV("SV", "Cartão Crédito VISA", true),
    ProdutoSR("SR", "Cartão Débito MAETRO", false),
    ProdutoSE("SE", "Cartão Débito VISA ELECTRON", false),
    ProdutoPV("PV", "Pagamento Carnê Débito VISA ELECTRON", false),
    ProdutoPM("PM", "Pagamento Carnê Débito MAESTRO", false),
    ProdutoPR("PR", "Pagamento Recorrente", true),
    ProdutoBM("BM", "BNDES Crédito Master", true),
    ProdutoBV("BV", "BNDES Crédito Visa", true),
    ProdutoED("ED", "Cartão Débito ELO", false),
    ProdutoEC("EC", "Cartão Crédito ELO", true),
    ProdutoAC("AC", "Cartão Crédito AMEX", true),
    ProdutoHC("HC", "Cartão Crédito HIPER / HIPERCARD", true),
    ;

    private String id;
    private String descricao;
    private boolean credito = false;

    private CodigosProdutosExtratoGetNetEnum(String id, String descricao, boolean credito) {
        this.id = id;
        this.descricao = descricao;
        this.credito = credito;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static CodigosProdutosExtratoGetNetEnum valueOff(String id) {
        CodigosProdutosExtratoGetNetEnum[] values = CodigosProdutosExtratoGetNetEnum.values();
        for (CodigosProdutosExtratoGetNetEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public boolean isCredito() {
        return credito;
    }

    public void setCredito(boolean credito) {
        this.credito = credito;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.getnet;

import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.apf.APF;
import static servicos.impl.dcc.base.LayoutRemessaBase.preencherAtributosTransientes;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaGetNetDCO extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        Date dataDeposito = remessa.getDataRegistro();
        RegistroRemessa header = obterRegistroRemessaHead(remessa, null);

        boolean remessaAgrupada = remessa.isNovoFormato();

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(DCCAttEnum.TipoRegistro, "01");//ok
            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampoZerado(7));//ok
            detail.put(DCCAttEnum.NumeroCartao, StringUtilities.formatarCampo(new BigDecimal(item.getNazgDTO().getCard(), MathContext.UNLIMITED), 19));//ok
            detail.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.formatarCampoZerado(6));//ok
            detail.put(DCCAttEnum.DataVenda, StringUtilities.formatarCampoData(dataDeposito));//ok
            detail.put(DCCAttEnum.OpcaoVenda, "0");//ok
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));//ok
            detail.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.formatarCampoZerado(3));//ok
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//ok
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//ok
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//ok
            detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoZerado(15));//ok
            detail.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampoZerado(7));//7//ok
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));//3//ok
            detail.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(),
                    MathContext.UNLIMITED), 10));//10//ok
            /*
             * 8 = data registro parcela
             * 8 = data vencimento parcela
             * 14 = brancos
             */

            if (remessaAgrupada) {
                detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(item.getCodigo().toString(), 30));
            } else {
                detail.put(DCCAttEnum.ReservadoEstabelecimento,
                        StringUtilities.formatarCampoData(item.getMovParcela().getDataRegistro())//8
                                + StringUtilities.formatarCampoData(item.getMovParcela().getDataVencimento())//8
                                + StringUtilities.formatarCampoEmBranco(item.getMovParcela().getCodigo().toString(), 14));//14//ok
            }

            //
            detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoZerado(2));//2//ok
            detail.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));//8

            String ano = Formatador.formatarValorNumerico((double) item.getNazgDTO().getYear(), "00");
            if (ano != null && ano.length() >= 4) {
                ano = ano.substring(2);
            }
            String validade = ano
                    + Formatador.formatarValorNumerico((double) item.getNazgDTO().getMonth(), "00");//ok

            detail.put(DCCAttEnum.ValidadeCartao, validade);//4//ok

            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(7));//7//ok
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//15//ok
            detail.put(DCCAttEnum.CVV2, StringUtilities.formatarCampoEmBranco(3));//3//ok
            detail.put(DCCAttEnum.CodigoErro, StringUtilities.formatarCampoZerado(4));//4//ok
            detail.put(DCCAttEnum.NumeroReferencia, StringUtilities.formatarCampoZerado(9));//9//ok
            detail.put(DCCAttEnum.NumeroProtocolo, StringUtilities.formatarCampoZerado(8));//8//ok
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(19));//19//ok
            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = obterRegistroRemessaTrailer(listaDetalhe.size(), soma);

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }

    public static RegistroRemessa obterRegistroRemessaHead(RemessaVO remessaVO, Integer sequencialUtilizar) {
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessaVO.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "00");//ok
        header.put(DCCAttEnum.DataDeposito, StringUtilities.formatarCampoData(dataDeposito));//ok
        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampoZerado(7));//ok
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(remessaVO.getCodigo().toString(), 10));//ok
        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));//ok
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessaVO.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));//ok
        header.put(DCCAttEnum.Moeda, StringUtilities.formatarCampo(new BigDecimal(986), 3));//Reais//ok
        header.put(DCCAttEnum.IndicadorProcesso, "P");//ok
        header.put(DCCAttEnum.IndicadorVenda, "2");//ok
        header.put(DCCAttEnum.IndicacaoEspecial, " ");//ok
        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(195));
        String sequencial = "";
        if (sequencialUtilizar == null) {
            try {
                sequencial = remessaVO.getProps().get(DCCAttEnum.SequencialRegistro.name());
            } catch (Exception e) {
            }
            sequencial = UteisValidacao.emptyString(sequencial)
                    ? StringUtilities.formatarCampo(new BigDecimal(remessaVO.getConvenioCobranca().getSequencialDoArquivo()), 9)
                    : sequencial;
        } else {
            sequencial = StringUtilities.formatarCampo(new BigDecimal(sequencialUtilizar), 9);
        }
        remessaVO.getProps().put(DCCAttEnum.SequencialRegistro.name(), sequencial);
        header.put(DCCAttEnum.SequencialRegistro, sequencial);
        return header;
    }

    public static RegistroRemessa obterRegistroRemessaTrailer(Integer qtdItens, Double somaItens) {
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "99");//ok
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(qtdItens + 2), 7));//ok
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(somaItens, 15));//ok
        trailer.put(DCCAttEnum.ValorTotalAceito, StringUtilities.formatarCampo(new BigDecimal(0), 15));//ok
        trailer.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.formatarCampo(new BigDecimal(0), 15));//ok
        trailer.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampo(new BigDecimal(0), 8));//ok
        trailer.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(188));//ok
        return trailer;
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("00")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("00")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("01")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("99")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
            for (ObjetoGenerico objetoGenerico : atributos) {
                preencherAtributosTransientes(remessa, objetoGenerico);
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));//ok
            r.put(DCCAttEnum.DataDeposito, StringUtilities.readString(2, 10, linha));
            r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(10, 17, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(17, 27, linha));
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(27, 30, linha));
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(30, 40, linha));
            r.put(DCCAttEnum.Moeda, StringUtilities.readString(40, 43, linha));//Reais
            r.put(DCCAttEnum.IndicadorProcesso, "P");
            r.put(DCCAttEnum.IndicadorVenda, "2");
            r.put(DCCAttEnum.IndicacaoEspecial, " ");
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(46, 241, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(241, 250, linha));

        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 250) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));//ok
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(2, 9, linha));//ok
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(9, 28, linha));//ok
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(28, 34, linha));//ok
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(34, 42, linha));//ok
                r.put(DCCAttEnum.OpcaoVenda, "0");//ok
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(43, 58, linha));//ok
                r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(58, 61, linha));//ok
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(61, 76, linha));//ok
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(76, 91, linha));//ok
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(91, 106, linha));//ok
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(106, 121, linha));//ok
                r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(121, 128, linha));//ok
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(128, 131, linha));//ok
                r.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.readString(131, 141, linha));//ok
                /*
                 * 8 = data registro parcela
                 * 8 = data vencimento parcela
                 * 14 = brancos
                 */
                r.put(DCCAttEnum.ReservadoEstabelecimento,
                        StringUtilities.readString(141, 171, linha));//ok
//
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(171, 173, linha));//ok
                r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(173, 181, linha));//ok
                r.put(DCCAttEnum.ValidadeCartao, StringUtilities.readString(181, 185, linha));//ok
                
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(185, 192, linha));//ok
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(192, 207, linha));
                r.put(DCCAttEnum.CVV2, StringUtilities.readString(207, 210, linha));
                r.put(DCCAttEnum.CodigoErro, StringUtilities.readString(210, 214, linha));
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(214, 223, linha));
                r.put(DCCAttEnum.NumeroTerminal, StringUtilities.readString(223, 231, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(231, 250, linha));
            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));//ok
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(2, 9, linha));//ok
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(9, 24, linha));//ok
            r.put(DCCAttEnum.ValorTotalAceito, StringUtilities.readString(24, 39, linha));//ok
            r.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.readString(39, 54, linha));//ok
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(54, 62, linha));//ok
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(62, 250, linha));//ok

        }
    }

    public static void preencherArquivoRemessaCancelamento(RemessaVO remessa) {
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);

        header.put(DCCAttEnum.TipoRegistro, "00");
        header.put(DCCAttEnum.DataDeposito, StringUtilities.formatarCampoData(remessa.getDataRegistro()));
        header.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(7));
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo(), MathContext.UNLIMITED), 10));
        header.put(DCCAttEnum.Zeros2, StringUtilities.formatarCampoZerado(3));
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
        header.put(DCCAttEnum.Moeda, "986");
        header.put(DCCAttEnum.IndicadorProcesso, "P");
        header.put(DCCAttEnum.IndicadorVenda, "1");
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(195));

        String sequencial = "";
        try {
            sequencial = remessa.getProps().get(DCCAttEnum.SequencialCancelamentoRegistro.name());
        } catch (Exception e) {
        }
        sequencial = UteisValidacao.emptyString(sequencial)
                ? StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getSequencialArquivoCancelamento(), 9)
                : sequencial;
        remessa.getProps().put(DCCAttEnum.SequencialCancelamentoRegistro.name(), StringUtilities.formatarCampoForcandoZerosAEsquerda(sequencial,9));

        header.put(DCCAttEnum.SequencialCancelamentoRegistro, sequencial);

        List<RemessaCancelamentoItemVO> lista = remessa.getListaItensCancelamento();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaCancelamentoItemVO cancelamentoItemVO : lista) {
            RemessaItemVO item = cancelamentoItemVO.getItemRemessaCancelar();
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

            String props = item.getProps().toString();
            String autorizacao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.CodigoAutorizacao.name(), props);

            Double valorVenda = item.getValorItemRemessa();
            Integer comprovanteVenda;

            boolean remessaAgrupada = item.getRemessa().isNovoFormato();
            if (remessaAgrupada) {
                comprovanteVenda = item.getCodigo();
            } else {
                comprovanteVenda = item.getMovParcela().getCodigo();
            }

            if (UteisValidacao.emptyString(autorizacao)) {
                continue;
            }

            detail.put(DCCAttEnum.TipoRegistro, "01");
            detail.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(7));
            detail.put(DCCAttEnum.NumeroCartao,StringUtilities.formatarCampoZerado(19));
            detail.put(DCCAttEnum.CodigoAutorizacao,StringUtilities.formatarCampoForcandoZerosAEsquerda(autorizacao,6));
            detail.put(DCCAttEnum.DataVenda, LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.DataVenda.name(), props));
            detail.put(DCCAttEnum.OpcaoVenda, "0");
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(valorVenda, 15));
            detail.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.formatarCampoZerado(3));
            detail.put(DCCAttEnum.ValorFinanciado, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorEntrada, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorEmbarque, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampoZerado(7));
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));
            detail.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
            if (remessaAgrupada) {
                detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(cancelamentoItemVO.getItemRemessaCancelar().getCodigo(), MathContext.UNLIMITED), 30));
            } else {
                detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(cancelamentoItemVO.getItemRemessaCancelar().getMovParcela().getCodigo(), MathContext.UNLIMITED), 30));
            }
            detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoZerado(2));
            detail.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));
            detail.put(DCCAttEnum.ValidadeCartao, StringUtilities.formatarCampoEmBranco(4));
            detail.put(DCCAttEnum.NumeroResumoOperacoesOriginal, StringUtilities.formatarCampoZerado(7));
            detail.put(DCCAttEnum.ValorReembolso, StringUtilities.formatarCampoMonetario(valorVenda, 15));
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(3));
            detail.put(DCCAttEnum.CodigoErro, StringUtilities.formatarCampoZerado(4));
            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampoForcandoZerosAEsquerda(comprovanteVenda,9));
            detail.put(DCCAttEnum.NumeroTerminal, LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.NumeroTerminal.name(), props));
            detail.put(DCCAttEnum.CartaoNovo, StringUtilities.formatarCampoEmBranco(19));

            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "99");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 7));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 15));
        trailer.put(DCCAttEnum.ValorTotalAceito, StringUtilities.formatarCampoZerado(15));
        trailer.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.formatarCampoZerado(15));
        trailer.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));
        trailer.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(188));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
    }
}

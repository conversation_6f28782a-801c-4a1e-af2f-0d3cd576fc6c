package servicos.impl.dcc.bnb;

public enum ErrosBNBEnum {
    StatusNENHUM(-1, ""),
    STATUS_01(1, "Falta valor do IOC."),
    STATUS_02(2, "Não permite desconto/ abatimento."),
    STATUS_03(3, "Código do serviço inválido."),
    STATUS_04(4, "Novo vencimento igual/ menor que o da entrada."),
    STATUS_05(5, "Novo vencimento igual ao do Título."),
    STATUS_06(6, "Espécie Documento Inválida."),
    STATUS_07(7, "Espécie Documento Inexistente."),
    STATUS_08(8, "Tipo Operação Inválida."),
    STATUS_09(9, "Tipo Operação Inexistente."),
    STATUS_10(10, "Contrato Proibido para esta Carteira."),
    STATUS_11(11, "Falta Número do Contrato."),
    STATUS_12(12, "Proibido Informar Tipo de Conta."),
    STATUS_13(13, "Tipo de Conta do Contrato Inexistente."),
    STATUS_14(14, "Dígito de Contrato não confere."),
    STATUS_15(15, "Contrato Inexistente."),
    STATUS_16(16, "Data de Emissão Inválida."),
    STATUS_17(17, "Falta Valor do Título."),
    STATUS_18(18, "Vencimento Inválido."),
    STATUS_19(19, "Data Vencimento Anterior a Emissão."),
    STATUS_20(20, "Falta Vencimento Desconto."),
    STATUS_21(21, "Data Desconto Inválida."),
    STATUS_22(22, "Data Desconto Posterior ao Vencimento."),
    STATUS_23(23, "Falta Valor Desconto."),
    STATUS_24(24, "Falta Mora-1-Dia."),
    STATUS_25(25, "Banco/Agência Cobrador Inexistente."),
    STATUS_26(26, "BCO/AGE Cobrador não Cadastrado."),
    STATUS_27(27, "Código Pessoa Inválido."),
    STATUS_28(28, "Falta CEP, Banco e Agência Cobrador."),
    STATUS_29(29, "Falta Nome Sacado."),
    STATUS_30(30, "Falta Endereço."),
    STATUS_31(31, "Falta Cidade."),
    STATUS_32(32, "Falta Estado."),
    STATUS_33(33, "Estado Inválido."),
    STATUS_34(34, "Falta CPF/ CGC do Sacado."),
    STATUS_35(35, "Falta numeração - Bloquete emitido."),
    STATUS_36(36, "Título Pré-Numerado já Existente."),
    STATUS_37(37, "Dígito do Título Não Confere."),
    STATUS_38(38, "Proibido Protestar."),
    STATUS_39(39, "Proibido título pré-numerado p/ Correspondente."),
    STATUS_40(40, "Dígito Cliente/ Contrato com Erro."),
    STATUS_41(41, "Dígito Nosso Número com Erro."),
    STATUS_42(42, "Título Inexistente."),
    STATUS_43(43, "Título Liquidado."),
    STATUS_44(44, "Título Não Pode Ser Baixado."),
    STATUS_45(45, "Valor Nominal Incorreto."),
    STATUS_46(46, "Proibido Taxa ? Multa p/ Correspondente."),
    STATUS_47(47, "Falta Tipo de Conta do Contrato."),
    STATUS_48(48, "Tipo de Conta Inexistente."),
    STATUS_49(49, "Dígito Contrato Não Confere."),
    STATUS_50(50, "Dígito do Título Não Confere."),
    STATUS_51(51, "Título Inexistente ou Liquidado."),
    STATUS_52(52, "Valor Abatimento Inválido."),
    STATUS_53(53, "Data Vencimento Inválida."),
    STATUS_54(54, "Estado Inválido."),
    STATUS_55(55, "Falta Tipo de Pessoa P/ Alteração de CGC/ CPF."),
    STATUS_56(56, "CPF/ CGC com Erro."),
    STATUS_57(57, "Data Emissão Inválida."),
    STATUS_58(58, "Data Vencimento Desconto Inválida."),
    STATUS_59(59, "Aceite Inválido para Espécie Documento."),
    STATUS_60(60, "Não Aceite Inválido para Espécie Documento."),
    STATUS_61(61, "Banco/ Agência Cobrador Inválido."),
    STATUS_62(62, "Limite Operacional Não Cadastrado."),
    STATUS_63(63, "Título já em situação de protesto."),
    STATUS_64(64, "Proibido alterar vencimento título descontado."),
    STATUS_65(65, "Proibido informar nosso número p/ cod. carteira."),
    STATUS_66(66, "Falta vencimento desconto-2."),
    STATUS_67(67, "Data desconto-2 inválida."),
    STATUS_68(68, "Data desconto-2 posterior ao vencimento."),
    STATUS_69(69, "Falta valor desconto-2."),
    STATUS_70(70, "Data vencimento desconto-2 inválida."),
    STATUS_71(71, "IOC maior que valor do título."),
    STATUS_72(72, "CEP não pertence ao Estado."),
    STATUS_73(73, "Seu número já existente."),
    STATUS_74(74, "Moeda Inválida para o tipo de Operação."),
    STATUS_75(75, "Moeda inexistente."),
    STATUS_76(76, "Nosso número/ dígito com erro."),
    STATUS_77(77, "Dias vencidos superior ao prazo de devolução.");

    private Integer id;
    private String descricao;

    ErrosBNBEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static ErrosBNBEnum valueOff(Integer id) {
        ErrosBNBEnum[] values = values();
        for (ErrosBNBEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return ErrosBNBEnum.StatusNENHUM;
    }

    public Integer getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bnb;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jboleto.bancos.BancoBNB;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaBNBCNAB400 extends LayoutRemessaBase {


    /**
     * Realiza o preenchimento das informações da linha de header do arquivo de remessa do Sicoob no padrão CNAB400.
     * @return RegistroRemessa do reader.
     */
    public static RegistroRemessa preencherHeaderArquivoRemessa(ConvenioCobrancaVO convenio, Date dataGeracao, Integer codigoRemessa){
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");//7
        header.put(DCCAttEnum.CodigoServico, "01");//2
        header.put(DCCAttEnum.IdentificacaoServico, StringUtilities.formatarCampoEmBranco("COBRANCA", 15));
        header.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getAgencia(), 4));
        header.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(2));
        header.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrente(), 7));
        header.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getContaCorrenteDV(), 1)); // Dígito Código Cliente/Beneficiário
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(6));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(convenio.getEmpresa().getNome(), 30)); // Nome do Beneficiário
        header.put(DCCAttEnum.Banco, "004");
        header.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco("B. DO NORDESTE ", 15));
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyy"));
        header.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampo(new BigDecimal(convenio.getNumeroContrato()), 3));//Código da caixa postal no Sistema EDI (Fornecido pelo Banco).
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(291));
        header.put(DCCAttEnum.SequencialRegistro, "000001");//6
        return header;
    }

    /**
     * Realiza a consulta do {@link EnderecoVO} que será utilizado na remessa.
     * @param item
     * @return
     */
    public static EnderecoVO getEndereco(RemessaItemVO item){
        EnderecoVO enderecoVO = new EnderecoVO();
        for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
            if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                enderecoVO = endereco;
            }
        }
        if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
            }
        }
        return enderecoVO;
    }

    /**
     * Realiza a criação da linha de detalhe para uma determinada {@link RemessaItemVO} do arquivo de remessa do Sicoob no padrão CNAB400.
     * @param remessaItem {@link RemessaItemVO} que será gerada a linha de detalhe.
     * @param dataRegistro Data de geração do arquivo
     * @param sequencial Sequencial da linha.
     * @return
     */
    public static RegistroRemessa preencherDetalheArquivoRemessa(RemessaItemVO remessaItem, Date dataRegistro, Integer sequencial) {
        RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
        ConvenioCobrancaVO convenio = remessaItem.getRemessa().getConvenioCobranca();
        detail.put(DCCAttEnum.TipoRegistro, "1");
        detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(16));
        detail.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getAgencia(), 4));
        detail.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(2));
        detail.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampoForcandoZerosAEsquerda(convenio.getContaEmpresa().getContaCorrente(), 7));
        detail.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.formatarCampoEmBranco(convenio.getContaEmpresa().getContaCorrenteDV(), 1));
        if(convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros()){
            detail.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoForcandoZerosAEsquerda(Integer.valueOf(convenio.getEmpresa().getMultaCobrancaAutomatica().intValue()).toString(), 2));
        }else{
            detail.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoZerado(2));
        }
        detail.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(4));
        detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getCodigo(), 25));
        detail.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getIdentificador().toString(), 7));
        detail.put(DCCAttEnum.NossoNumeroDV, getDVNossoNumero(convenio, remessaItem.getIdentificador()));
        detail.put(DCCAttEnum.NumeroContrato, StringUtilities.formatarCampoZerado(10));
        detail.put(DCCAttEnum.DataSegundoDesconto, StringUtilities.formatarCampoZerado(6));
        detail.put(DCCAttEnum.ValorSegundoDesconto, StringUtilities.formatarCampoZerado(13));
        detail.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(8));
        detail.put(DCCAttEnum.Carteira, convenio.getCarteira()); //1 posição
        detail.put(DCCAttEnum.CodigoServico, "01"); // Entrada normal.
        detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessaItem.getCodigo(), 10));
        detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(remessaItem.getDataVencimentoBoleto(), "ddMMyy"));
        detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoMonetario(remessaItem.getValorBoleto(), 13));
        detail.put(DCCAttEnum.Zeros2, StringUtilities.formatarCampoZerado(3)); // Número do banco cobrador
        detail.put(DCCAttEnum.Zeros3, StringUtilities.formatarCampoZerado(4)); // Número da agência cobradora
        detail.put(DCCAttEnum.EmBranco7, StringUtilities.formatarCampoEmBranco(1));
        detail.put(DCCAttEnum.EspecieTitulo, "06");
        detail.put(DCCAttEnum.Aceite, "N");
        detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataRegistro, "ddMMyy"));
        detail.put(DCCAttEnum.CodigoInstrucao, "0000");
        if(convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && convenio.getEmpresa().getCobrarAutomaticamenteMultaJuros()){
            Double juros = convenio.getEmpresa().getJurosCobrancaAutomatica() / 30;
            Double valorCobrar = remessaItem.getValorBoleto() * juros;
            detail.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(valorCobrar, 13));
        }else{
            detail.put(DCCAttEnum.Juros, StringUtilities.formatarCampoZerado(13));
        }
        if(remessaItem.possuiDesconto() && remessaItem.getDataPagamentoAntecipado() != null){
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(remessaItem.getDataPagamentoAntecipado(), "ddMMyy"));
            Double valor = remessaItem.getValorBoleto() * (1 - (remessaItem.getPorcentagemDescontoBoletoPagAntecipado() / 100));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 13));
        }else{
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));
        }
        detail.put(DCCAttEnum.IOC, StringUtilities.formatarCampoZerado(13));
        detail.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(13));
        detail.put(DCCAttEnum.TipoInscricao, remessaItem.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02"); // Tipo inscrição pagador 01 = CPF, 02 = CNPJ
        String cpfCnpj = remessaItem.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(remessaItem) : getCNPJ(remessaItem);
        detail.put(DCCAttEnum.InscricaoPagador, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));
        detail.put(DCCAttEnum.NomeCliente, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getNome(), 40));
        EnderecoVO enderecoVO = getEndereco(remessaItem);
        detail.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 40));
        detail.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 12));
        detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
        detail.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getCidade().getNome(), 15));
        detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(remessaItem.getPessoa().getEstadoVO().getSigla(), 2));
        detail.put(DCCAttEnum.EmBranco8,  StringUtilities.formatarCampoEmBranco(40)); //Mensagem do Cedente ou Nome do Sacador / Avalista.
        detail.put(DCCAttEnum.DiasProtesto, "99");
        detail.put(DCCAttEnum.Moeda, "0");
        detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(sequencial, 6));
        return detail;
    }

    public static String getDVNossoNumero(ConvenioCobrancaVO convenio, Integer identificador) {
        String nossoNumeroTemp = StringUtilities.formatarCampoForcandoZerosAEsquerda(identificador.toString(), 7);
        return calcularDVNossoNumero(nossoNumeroTemp);
    }

    /**
     * Realiza o preenchimento da linha de trailler do arquivo de remessa do Sicoob no padrão CNAB400.
     * @param sequencial
     */
    public static RegistroRemessa preencherTraillerArquivoRemessa(Integer sequencial){
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(393));
        trailer.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(sequencial), 6));
        return trailer;
    }

    /**
     * Popula as informações adicionais da remessa que não são informados no retorno do arquivo.
     * @param remessa
     */
    private static void popularInformacoesAdicionais(RemessaVO remessa) {
        //Usar o Props pelo fato do Banco não aceitar informações no Trailer
        Map<String, String> infoAdicional = new HashMap<String, String>();
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }
        infoAdicional.put(DCCAttEnum.QuantidadeRegistros.name(), String.valueOf(remessa.getListaItens().size()));
        infoAdicional.put(DCCAttEnum.ValorTotalBruto.name(), StringUtilities.formatarCampoMonetario(valorTotalBruto, 10));
        remessa.setProps(infoAdicional);
    }

    /**
     * Realiza o preenchimento do arquivo do arquivo de remessa do Sicoob no padrão CNAB400.
     * @param remessa
     */
    public static void preencherArquivoRemessa(RemessaVO remessa) {
        Date dataDeposito = remessa.getDataRegistro();
        ConvenioCobrancaVO convenio = remessa.getConvenioCobranca();
        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 2;
        for (RemessaItemVO item : lista) {
            item.setRemessa(remessa);
            RegistroRemessa detail = preencherDetalheArquivoRemessa(item, dataDeposito, seq);
            if (item.getCodigo() != 0) {
                qtdAceito += 1;
            }
            seq++;
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtdAceito);
        remessa.setHeaderRemessa(preencherHeaderArquivoRemessa(convenio, dataDeposito, remessa.getCodigo()));
        remessa.setHead(new StringBuilder(remessa.getHeaderRemessa().toString()));
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
        remessa.setTrailerRemessa(preencherTraillerArquivoRemessa(seq));
        remessa.setTrailer(new StringBuilder(remessa.getTrailerRemessa().toString()));
        popularInformacoesAdicionais(remessa);
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    /**
     * Realiza a leitura do arquivo de retorno do banco Sicoob no padrão CNAB400.
     * @param remessa
     * @throws IOException
     * @throws ConsistirException
     */
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        switch (r.getTipo()){
            case HEADER:
                lerAtributosHeader(linha, r);
                break;
            case DETALHE:
                lerAtributosDetalhe(linha, r);
                break;
            case TRAILER:
                lerAtributosTrailler(linha, r);
                break;
            default:
                break;
        }
    }

    /**
     * Realiza a leitura do trailler do arquivo de retorno do Sicoob no padrão CNAB400
     * @param linha
     * @param registroRemessa
     */
    private static void lerAtributosTrailler(String linha, RegistroRemessa registroRemessa) {
        registroRemessa.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registroRemessa.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, 4, linha));
        registroRemessa.put(DCCAttEnum.Banco, StringUtilities.readString(4, 7, linha));
        registroRemessa.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 17, linha));
        registroRemessa.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17, 25, linha));
        registroRemessa.put(DCCAttEnum.ValorTotalCobrancaSimples, StringUtilities.readString(25, 39, linha));
        registroRemessa.put(DCCAttEnum.AvisoBancarioCobrancaSimples, StringUtilities.readString(39, 47, linha));
        registroRemessa.put(DCCAttEnum.EmBranco, StringUtilities.readString(47, 394, linha));
        registroRemessa.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }

    /**
     * Realiza a leitura do detail do arquivo de retorno do Sicoob no padrão CNAB400
     * @param linha
     * @param registro
     */
    private static void lerAtributosDetalhe(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registro.put(DCCAttEnum.TipoPagador, StringUtilities.readString(1, 3, linha));
        registro.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(3, 17, linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(17, 21, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(21, 23, linha));
        registro.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(23, 30, linha));
        registro.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(30, 31, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(31, 37, linha));
        registro.put(DCCAttEnum.NumeroControleCliente, StringUtilities.readString(37, 62, linha));
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(62, 69, linha));
        registro.put(DCCAttEnum.NossoNumeroDV, StringUtilities.readString(69, 70, linha));
        registro.put(DCCAttEnum.NumeroContrato, StringUtilities.readString(70, 80, linha));
        registro.put(DCCAttEnum.EmBranco2, StringUtilities.readString(80, 107, linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(107, 108, linha));
        registro.put(DCCAttEnum.StatusVenda, StringUtilities.readString(108, 110, linha));
        registro.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(110, 116, linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(116, 126, linha));
        registro.put(DCCAttEnum.ConfirmacaoNossoNumero, StringUtilities.readString(126, 133, linha));
        registro.put(DCCAttEnum.ConfirmacaoDigitoNossoNumero, StringUtilities.readString(133, 134, linha));
        registro.put(DCCAttEnum.EmBranco3, StringUtilities.readString(134, 146, linha));
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(165, 168, linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(168, 172, linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(172, 173, linha));
        registro.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));
        registro.put(DCCAttEnum.Tarifa, StringUtilities.readString(175, 188, linha));
        registro.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(188, 201, linha));
        registro.put(DCCAttEnum.JurosDesconto, StringUtilities.readString(201, 214, linha));
        registro.put(DCCAttEnum.IOFDesconto, StringUtilities.readString(214, 227, linha));
        registro.put(DCCAttEnum.Abatimento, StringUtilities.readString(227, 240, linha));
        registro.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(266, 279, linha));
        registro.put(DCCAttEnum.CodigoErro, StringUtilities.readString(279, 394, linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }

    /**
     * Realiza a leitura do header do arquivo de retorno do Sicoob no padrão CNAB400
     * @param linha
     * @param registro
     */
    private static void lerAtributosHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
        registro.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));
        registro.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));
        registro.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));
        registro.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(26, 30, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(30, 32, linha));
        registro.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(32, 39, linha));
        registro.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(39, 40, linha)); // Dígito Código Cliente/Beneficiário
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(40, 46, linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha)); // Nome do Beneficiário
        registro.put(DCCAttEnum.Banco, StringUtilities.readString(76, 79, linha));
        registro.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 94, linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));
        registro.put(DCCAttEnum.SequencialRetorno, StringUtilities.readString(108, 113, linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(113, 119, linha));
        registro.put(DCCAttEnum.DataCredito, StringUtilities.readString(119, 125, linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
    }


    public static String calcularDVNossoNumero(String identificador) {
        return new BancoBNB(null).calcularDV(identificador);
    }
}

package servicos.impl.dcc.bnb;


/**
 * Created by <PERSON> on 31/08/2016.
 */
public enum Cnab400BNBStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status02("02", "Entrada Confirmada"),
    Status04("04", "Alteração"),
    Status06("06", "Liquidação Normal"),
    Status07("07", "Pagamento por Conta"),
    Status08("08", "Pagamento por Cartório"),
    Status09("09", "Baixa Simples"),
    Status10("10", "Devolvido / Protestado"),
    Status11("11", "Em ser"),
    Status12("12", "Abatimento Concedido"),
    Status13("13", "Abatimento Cancelado"),
    Status14("14", "Vencimento Alterado"),
    Status15("15", "Baixa Automática"),
    Status18("18", "Alteração Depositária"),
    Status19("19", "Confirmação de Protesto"),
    Status20("20", "Confirmação de Sustar Protesto"),
    Status21("21", "Alteração Informações de Controle da Empresa"),
    Status22("22", "Alteração Seu Número"),
    Status51("51", "Entrada Rejeitada");


    private String id;
    private String descricao;

    Cnab400BNBStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    public static Cnab400BNBStatusEnum valueOff(String id) {
        Cnab400BNBStatusEnum[] values = values();
        for (Cnab400BNBStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return Cnab400BNBStatusEnum.StatusNENHUM;
    }
}

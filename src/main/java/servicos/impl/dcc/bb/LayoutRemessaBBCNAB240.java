package servicos.impl.dcc.bb;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Created by Johnys on 20/01/2017
 */
public class LayoutRemessaBBCNAB240 extends LayoutRemessaBase {

    private static Pattern patternHEAD  = Pattern.compile("^[0-9]{8}T");
    private static Pattern patternT  = Pattern.compile("^[0-9]{13}T");
    private static Pattern patternU  = Pattern.compile("^[0-9]{13}U");
    private static Pattern patterTRAILER = Pattern.compile("^[0-9]{7}5");


    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        header.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        header.put(DCCAttEnum.LoteServico, "0000");
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        header.put(DCCAttEnum.TipoInscricaoEmpresa, "2");//1
        header.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
        header.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Integer.valueOf(remessa.getConvenioCobranca().getNumeroContrato()), 9));
        header.put(DCCAttEnum.IdentificacaoCobranca, "0014");
        header.put(DCCAttEnum.Carteira, "17");
        if(UteisValidacao.emptyNumber(remessa.getConvenioCobranca().getVariacao())){
             header.put(DCCAttEnum.VariacaoCarteira, "027");
        } else {
            header.put(DCCAttEnum.VariacaoCarteira, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getVariacao(),3));
        }
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
        header.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        header.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
        header.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
        header.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        header.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        header.put(DCCAttEnum.Banco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 30));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
        header.put(DCCAttEnum.HoraGeracao, StringUtilities.formatarCampoData(dataDeposito, "HHmmss"));
        header.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getCodigo(), 6));
        header.put(DCCAttEnum.VersaoLayout, "000");
        header.put(DCCAttEnum.DensidadeGravacao, "00000");
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(69));

        remessa.setHeaderRemessa(header);
        remessa.setHeaderRemessaArquivo(getHeaderLote(remessa, dataDeposito));
        Double juros = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() ? remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica() : 0;

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 1;
        for (RemessaItemVO item : lista) {
            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    enderecoVO = endereco;
                }
            }
            if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                    enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
                }
            }

            RegistroRemessa detailP = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_P);
            detailP.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailP.put(DCCAttEnum.LoteServico, "0001");
            detailP.put(DCCAttEnum.TipoRegistro, "3");
            detailP.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailP.put(DCCAttEnum.Segmento, "P");
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.CodigoMovimento, "01");
            detailP.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
            detailP.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
            detailP.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
            detailP.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Integer.valueOf(remessa.getConvenioCobranca().getNumeroContrato()), 7));
            detailP.put(DCCAttEnum.NossoNumero, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getIdentificador().toString(), 10));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(3));
            detailP.put(DCCAttEnum.Carteira, "7");
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(4));
            detailP.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoEmBranco(item.getCodigo().toString(), 15));
            detailP.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));
            detailP.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 15));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(5));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.EspecieTitulo, "02");
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, "N");
            detailP.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
            detailP.put(DCCAttEnum.CodigoJuros, "1");
            detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoZerado(8));
            detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(juros, 15));
            validarDesconto(item, detailP);
            detailP.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, StringUtilities.formatarCampoEmBranco(item.getCodigo().toString(), 25));
            detailP.put(DCCAttEnum.Protesto, "3");
            detailP.put(DCCAttEnum.DiasProtesto, StringUtilities.formatarCampoZerado(2));
            detailP.put(DCCAttEnum.CodigoBaixa, "0");
            detailP.put(DCCAttEnum.NumeroDiasBaixa, StringUtilities.formatarCampoZerado(3));
            detailP.put(DCCAttEnum.Moeda, "09");
            detailP.put(DCCAttEnum.NumeroContratoOperacaoCredito, StringUtilities.formatarCampoZerado(10));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));

            RegistroRemessa detailQ = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_Q);
            detailQ.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailQ.put(DCCAttEnum.LoteServico, "0001");
            detailQ.put(DCCAttEnum.TipoRegistro, "3");
            seq++;
            detailQ.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailQ.put(DCCAttEnum.Segmento, "Q");
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailQ.put(DCCAttEnum.CodigoMovimento, "01");
            detailQ.put(DCCAttEnum.TipoInscricao, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "1" : "2");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detailQ.put(DCCAttEnum.CpfOuCnpj, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 15));
            detailQ.put(DCCAttEnum.NomeCliente, StringUtilities.formatarCampoEmBranco(Uteis.retirarAcentuacaoRegex(item.getPessoa().getNome()), 40));
            detailQ.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerCaracteresNaoAscii(enderecoVO.getEndereco()), 40));
            detailQ.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 15));
            detailQ.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
            detailQ.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNome(), 15));
            detailQ.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(16));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(3));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(20));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
            seq++;
            //
            listaDetalhe.add(detailP);
            listaDetalhe.add(detailQ);
        }

        remessa.setQtdAceito(lista.size());
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }

        remessa.setTrailerRemessaArquivo(getTrailerArquivo(remessa));

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "9999");
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QtdLotes, StringUtilities.formatarCampoForcandoZerosAEsquerda(1, 6));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 2 + 4, 6));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(6));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(205));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setTrailerArquivo(new StringBuilder(remessa.getTrailerRemessaArquivo().toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setHeaderArquivo(new StringBuilder(remessa.getHeaderRemessaArquivo().toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
    }

    public static void validarDesconto(RemessaItemVO item, RegistroRemessa detail) {
        if (item.possuiDesconto() && item.getDataPagamentoAntecipado() != null) {
            Double valor = item.getValorBoleto();
            valor = valor * item.getPorcentagemDescontoBoletoPagAntecipado() / 100;

            detail.put(DCCAttEnum.CodigoDesconto, "1");
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(item.getDataPagamentoAntecipado(), "ddMMyyyy"));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 15));
        } else {
            detail.put(DCCAttEnum.CodigoDesconto, "0");
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(8));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(15));
        }
    }

    private static RegistroRemessa getTrailerArquivo(RemessaVO remessa) {
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);
        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "0001");
        trailer.put(DCCAttEnum.TipoRegistro, "5");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 2 + 2, 6));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(217));
        return trailer;
    }

    private static RegistroRemessa getHeaderLote(RemessaVO remessa, Date dataDeposito) {
        RegistroRemessa registro = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        registro.put(DCCAttEnum.LoteServico, "0001");
        registro.put(DCCAttEnum.TipoRegistro, "1");
        registro.put(DCCAttEnum.TipoOperacao, "R");
        registro.put(DCCAttEnum.TipoServico, "01");
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
        registro.put(DCCAttEnum.VersaoLayout, "042");
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 15));
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Integer.valueOf(remessa.getConvenioCobranca().getNumeroContrato()), 9));
        registro.put(DCCAttEnum.IdentificacaoCobranca, "0014");
        registro.put(DCCAttEnum.Carteira, "17");
         if(UteisValidacao.emptyNumber(remessa.getConvenioCobranca().getVariacao())){
            registro.put(DCCAttEnum.VariacaoCarteira, "027");
        } else {
            registro.put(DCCAttEnum.VariacaoCarteira, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getVariacao(),3));
        }
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(80));
        registro.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getCodigo(), 8));
        registro.put(DCCAttEnum.DataGravacao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(41));
        return registro;
    }

    public static String calcularNossoNumero(RemessaItemVO obj, String identificadorEmpreasFinanceiro) {
        String nossoNumero;
        if(identificadorEmpreasFinanceiro != null){
            StringBuilder sb = new StringBuilder();
            sb.append(identificadorEmpreasFinanceiro);
            sb.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getIdentificador().toString(), (10 - identificadorEmpreasFinanceiro.length())));
            nossoNumero = sb.toString();
        }else{
            nossoNumero = StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getIdentificador().toString(), 10);
        }
        return nossoNumero;
    }


    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            while ((linha = br.readLine()) != null) {
                if(patternHEAD.matcher(linha).find()){
                    lerHeader(linha, h);
                    break;
                }
            }
        }
        return h;
    }

    /**
     * Realiza a leitura do header do lote para a validação do arquivo.
     * @param linha
     * @param registro
     */
    private static void lerHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(8,9,linha));
        registro.put(DCCAttEnum.TipoServico, StringUtilities.readString(9,11,linha));
        registro.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(13,16,linha));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, StringUtilities.readString(17,18,linha));
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(18,33,linha));
        registro.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(33,42,linha));
        registro.put(DCCAttEnum.IdentificacaoCobranca, StringUtilities.readString(42,46,linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(46,48,linha));
        registro.put(DCCAttEnum.VariacaoCarteira, StringUtilities.readString(48,51,linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(53,58,linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(58,59,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(59,71,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(71,72,linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(73,103,linha));
        registro.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(183,191,linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(191,199,linha));
    }

    /**
     * Realiza a leitura do registro T do arquivo de retorno do banco.
     * @param linha
     * @param registro
     */
    private static void lerDetailT(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(8,13,linha));
        registro.put(DCCAttEnum.Segmento, StringUtilities.readString(13,14,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(14,15,linha));
        registro.put(DCCAttEnum.StatusVenda, StringUtilities.readString(15,17,linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(17,22,linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(22,23,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(23,35,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(35,36,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(36,37,linha));
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.readString(37,44,linha));
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(44,57,linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(57,58,linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(58,73,linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(58,73,linha));
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(73,81,linha));
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(81,96,linha));
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(96,99,linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(99,104,linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(104,105,linha));
        registro.put(DCCAttEnum.Moeda, StringUtilities.readString(130,132,linha));
        registro.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(198,213,linha));
    }

    /**
     * Realiza a leitura do registro U do arquivo de retorno do banco.
     * @param linha
     * @param registro
     */
    private static void lerDetailU(String linha, RegistroRemessa registro){
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(17,32,linha));
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(77,92,linha));
        registro.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(198,213,linha));
    }


    /**
     * Realiza a leitura do trailer
     * @param linha
     * @param registro
     */
    private static void lerTrailer(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17,23,linha));
    }


    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();

        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            RegistroRemessa detail = null;
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (patternHEAD.matcher(linha).find()) {
                    lerHeader(linha, h);
                } else if (patternT.matcher(linha).find()) {
                    detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_T);
                    lerDetailT(linha, detail);
                } else if (patternU.matcher(linha).find()) {
                    lerDetailU(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (patterTRAILER.matcher(linha).find()) {
                    lerTrailer(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static String obterCodigosRemessaItem(StringBuilder retorno) throws IOException{
        StringBuilder codigos = new StringBuilder();
        BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
        String linha;
        while ((linha = br.readLine()) != null) {
            if(patternT.matcher(linha).find()){
                String nossoNumero = StringUtilities.readString(44, 57, linha);
                nossoNumero = nossoNumero.trim();
                codigos.append(nossoNumero);
                codigos.append(",");
            }
        }

        if (codigos.length() > 0) {
            codigos.deleteCharAt(codigos.length() - 1);
        }

        return codigos.toString();
    }
}




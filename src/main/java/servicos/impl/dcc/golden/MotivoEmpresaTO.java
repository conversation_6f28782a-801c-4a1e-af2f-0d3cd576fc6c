package servicos.impl.dcc.golden;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public class MotivoEmpresaTO {

    private Integer empresa;
    private String empresaNome;
    private List<ItemInadimplenciaTO> listaItens;


    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmpresaNome() {
        if (empresaNome == null) {
            empresaNome = "";
        }
        return empresaNome;
    }

    public void setEmpresaNome(String empresaNome) {
        this.empresaNome = empresaNome;
    }

    public List<ItemInadimplenciaTO> getListaItens() {
        if (listaItens == null) {
            listaItens = new ArrayList<>();
        }
        return listaItens;
    }

    public void setListaItens(List<ItemInadimplenciaTO> listaItens) {
        this.listaItens = listaItens;
    }
}

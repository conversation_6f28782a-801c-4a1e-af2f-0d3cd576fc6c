package servicos.impl.dcc.golden;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public class MotivoTO {

    private String motivo;
    private Map<String, Double> meses;

    public String getMotivo() {
        if (motivo == null) {
            motivo = "";
        }
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public Map<String, Double> getMeses() {
        if (meses== null) {
            meses = new HashMap<>();
        }
        return meses;
    }

    public void setMeses(Map<String, Double> meses) {
        this.meses = meses;
    }
}

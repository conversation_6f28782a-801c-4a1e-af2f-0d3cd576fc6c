/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.golden;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovParcelaResultadoCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import servicos.impl.cieloecommerce.CieloECommerceRetornoEnum;
import servicos.impl.dcc.bin.DCCBinStatusEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;
import servicos.impl.getnet.GetnetOnlineRetornoEnum;
import servicos.impl.pagarMe.PagarMeRetornoEnum;
import servicos.impl.pagbank.PagBankRetornoEnum;
import servicos.impl.redepay.ERedeRetornoEnum;
import servicos.impl.stone.StoneRetornoEnum;
import servicos.impl.stripe.StripeRetornoEnum;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 15/05/2020
 */
public class ProcessoPlanilhaTentativaCobranca {

    private Connection con;
    private static StringBuilder logGravar;

    public ProcessoPlanilhaTentativaCobranca(Connection con) {
        this.con = con;
        this.logGravar = new StringBuilder();
    }

    public static void main(String... args) throws IOException {
        Connection con = null;
        ProcessoPlanilhaTentativaCobranca service = null;
        String diretorioSalvarArquivo = "C:\\PactoJ\\golden";
        try {
            Uteis.debug = true;
            adicionarLog("ProcessoPlanilhaTentativaCobranca | Início");

            con = DriverManager.getConnection("***************************************", "zillyonweb", "pactodb");
            service = new ProcessoPlanilhaTentativaCobranca(con);

            Integer empresa = 0;
//            String mesReferencia = "04/2020";
            String mesReferencia = "";

            Integer codigoDados = 0;
//            Integer codigoDados = 9;

            List<ItemInadimplenciaTO> lista;

            if (!UteisValidacao.emptyNumber(codigoDados)) {
                lista = service.obterListaBanco(codigoDados);
            } else {
                lista = service.consultarBanco(empresa, mesReferencia);
            }

            if (!UteisValidacao.emptyList(lista)) {
                service.gerarArquivoExcel(lista, diretorioSalvarArquivo, null);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog("ProcessoPlanilhaTentativaCobranca | Erro | " + ex.getMessage());
        } finally {
            adicionarLog("ProcessoPlanilhaTentativaCobranca | Fim");
            service = null;
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ignored) {
                }
            }
            Uteis.salvarArquivo("ProcessoPlanilhaTentativaCobranca --- " + Calendario.hoje().getTime() + " - LOG.txt", getLogGravar().toString(), diretorioSalvarArquivo + File.separator);
        }
    }

    public List<ItemInadimplenciaTO> consultarBanco(Integer empresa, String mesReferencia) throws Exception {

        Date mes = null;
        if (!UteisValidacao.emptyString(mesReferencia)) {
            mes = Uteis.getDate(mesReferencia, "MM/yyyy");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("m.codigo as codigoparcela, \n");
        sql.append("m.descricao as descricaoparcela, \n");
        sql.append("m.datavencimento, \n");
        sql.append("m.valorparcela, \n");
        sql.append("m.situacao as situacaoparcela, \n");
        sql.append("fp.descricao as formapagamento, \n");
        sql.append("mp.datalancamento as dataPagamento, \n");
        sql.append("emp.codigo as empresaCodigo, \n");
        sql.append("emp.nome as empresaNome, \n");
        sql.append("array_to_string(array(select mov.descricao from movproduto mov inner join movprodutoparcela mpp on mpp.movproduto = mov.codigo where mpp.movparcela = mp.codigo), '<br/>', '') as produtos \n");
        sql.append("FROM movparcela m  \n");
        sql.append("LEFT JOIN empresa emp on emp.codigo = m.empresa  \n");
        sql.append("LEFT JOIN pessoa p on p.codigo = m.pessoa  \n");
        sql.append("LEFT JOIN cliente cl on cl.pessoa = p.codigo  \n");
        sql.append("LEFT JOIN situacaoclientesinteticodw  dw on dw.codigopessoa = p.codigo  \n");
        sql.append("LEFT JOIN contrato c on c.codigo = m.contrato  \n");
        sql.append("LEFT JOIN contratooperacao coo ON coo.contrato = c.codigo AND coo.tipooperacao = 'CA'  \n");
        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.movparcela = m.codigo  \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pm.movpagamento  \n");
        sql.append("LEFT JOIN formapagamento fp on mp.formapagamento = fp.codigo \n");
        sql.append("WHERE m.situacao not in ('RG') \n");
        sql.append("AND (m.situacao IN ('EA', 'PG') OR (m.datavencimento :: DATE <= coo.dataoperacao :: DATE)) \n");

        if (mes == null) {
            sql.append("AND m.datavencimento::date between '01/11/2019' and '30/04/2020' \n");
        } else {
            sql.append("AND m.datavencimento::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes)));
            sql.append("' and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND m.empresa = ").append(empresa).append(" \n");
        }

        sql.append(" order by m.datavencimento \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        List<ItemInadimplenciaTO> lista = new ArrayList<>();

        int i = 0;
        while (rs.next()) {
            adicionarLog("Atual " + ++i + "/" + total);

            ItemInadimplenciaTO item = new ItemInadimplenciaTO();
            item.setMatricula(rs.getString("matricula"));
            item.setNome(rs.getString("nome"));
            item.setMovParcela(rs.getInt("codigoparcela"));
            item.setValorParcela(rs.getDouble("valorparcela"));
            item.setDataVencimentoParcela(rs.getDate("datavencimento"));
            item.setSituacaoParcela(rs.getString("situacaoparcela"));
            item.setDescricaoParcela(rs.getString("descricaoparcela"));
            item.setFormaPagamento(rs.getString("formapagamento"));
            item.setDataPagamento(rs.getTimestamp("dataPagamento"));
            item.setProdutosParcela(rs.getString("produtos"));
            item.setEmpresaNome(rs.getString("empresaNome"));
            item.setEmpresaCodigo(rs.getInt("empresaCodigo"));

            consultarDadosUltimaTentativa(item);

            lista.add(item);
        }

        gravarJSONDadosBanco(lista);
        return lista;
    }

    private void consultarDadosUltimaTentativa(ItemInadimplenciaTO item) throws Exception {

        StringBuilder sqlBase = new StringBuilder();
        sqlBase.append("select * from ( \n");
        sqlBase.append("select  \n");
        sqlBase.append("'REMESSA' as meio,\n");
        sqlBase.append("r.dataregistro as data, \n");
        sqlBase.append("r.tipo as tipoEnum,\n");
        sqlBase.append("split_part(split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1), '}', 1) AS codigoRetorno,\n");
        sqlBase.append("'' as resposta,\n");
        sqlBase.append("''  as outrasinformacoes,\n");
        sqlBase.append("0 as situacao\n");
        sqlBase.append("from remessaitem ri \n");
        sqlBase.append("inner join remessa r on r.codigo = ri.remessa \n");
        sqlBase.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sqlBase.append("where (ri.movparcela = ").append(item.getMovParcela()).append(" or rim.movparcela = ").append(item.getMovParcela()).append(") \n");
        sqlBase.append("union \n");
        sqlBase.append("select  \n");
        sqlBase.append("'TRANSACAO' as meio,\n");
        sqlBase.append("t.dataprocessamento as data,\n");
        sqlBase.append("t.tipo as tipoEnum,\n");
        sqlBase.append("t.codigoretorno as codigoRetorno,\n");
        sqlBase.append("t.paramsresposta as resposta,\n");
        sqlBase.append("t.outrasinformacoes  as outrasinformacoes,\n");
        sqlBase.append("t.situacao as situacao\n");
        sqlBase.append("from transacao t \n");
        sqlBase.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sqlBase.append("where tm.movparcela = ").append(item.getMovParcela()).append(" \n");
        sqlBase.append(") sql \n");

        //total de tentivas
        Integer qtdTentativas = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sqlBase.toString() + ") as qtd", con);
        item.setQtdTentativasCobranca(qtdTentativas);

        //pegar o ultimo registro
        sqlBase.append("order by sql.data desc limit 1 \n");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlBase.toString(), con);
        if (rs.next()) {

            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = null;
            String motivoRetornoUltimaTentativa;
            String meioUltimaTentativa;
            String motivoTransacao = "";

            Date dataUltimaTentativa = rs.getTimestamp("data");
            String codigoRetornoUltimaTentativa = rs.getString("codigoRetorno");
            Integer tipoEnum = rs.getInt("tipoEnum");
            String meio = rs.getString("meio");


            //remessa
            if (meio.equalsIgnoreCase("REMESSA")) {
                motivoRetornoUltimaTentativa = "EDI";

                TipoRemessaEnum tipoRemessaEnum = TipoRemessaEnum.getTipoRemessaEnum(tipoEnum);
                tipoConvenioCobrancaEnum = obterTipoConvenioCobrancaEnum(tipoRemessaEnum);

                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.NENHUM)) {
                    meioUltimaTentativa = tipoRemessaEnum.getDescricao().toUpperCase();
                } else {
                    meioUltimaTentativa = tipoConvenioCobrancaEnum.getDescricao().toUpperCase();
                }


            } else { //transação
                motivoRetornoUltimaTentativa = "ONLINE";

                TipoTransacaoEnum tipoTransacaoEnum = TipoTransacaoEnum.getTipoTransacaoEnum(tipoEnum);
                tipoConvenioCobrancaEnum = obterTipoConvenioCobrancaEnum(tipoTransacaoEnum);

                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.NENHUM)) {
                    meioUltimaTentativa = tipoTransacaoEnum.getDescricao().toUpperCase();
                } else {
                    meioUltimaTentativa = tipoConvenioCobrancaEnum.getDescricao().toUpperCase();
                }

                TransacaoVO transacaoVO = Transacao.obterObjetoTransacaoPorTipo(tipoTransacaoEnum);
                transacaoVO.setTipo(tipoTransacaoEnum);
                transacaoVO.setParamsResposta(rs.getString("resposta"));
                transacaoVO.setOutrasInformacoes(rs.getString("outrasinformacoes"));
                transacaoVO.setCodigoRetorno(codigoRetornoUltimaTentativa);
                transacaoVO.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao")));

                motivoTransacao = transacaoVO.getCodigoRetornoGestaoTransacaoMotivo();

                if (UteisValidacao.emptyString(motivoTransacao)) {
                    adicionarLog("Parcela " + item.getMovParcela() + " | Sem retorno transacao");
                }
            }

            String retornoIdentificado = "";
            if (tipoConvenioCobrancaEnum != null) {
                retornoIdentificado = obterDescricaoRetorno(tipoConvenioCobrancaEnum, codigoRetornoUltimaTentativa);
            }

            if (!UteisValidacao.emptyString(motivoTransacao) && retornoIdentificado != null && !motivoTransacao.equalsIgnoreCase(retornoIdentificado)) {
                adicionarLog("Parcela " + item.getMovParcela() + " | Motivo diferentes");
                adicionarLog("Parcela " + item.getMovParcela() + " | motivoTransacao " + motivoTransacao);
                adicionarLog("Parcela " + item.getMovParcela() + " | retornoIdentificado " + retornoIdentificado);
            }

            if (!UteisValidacao.emptyString(motivoTransacao) && UteisValidacao.emptyString(retornoIdentificado)) {
                retornoIdentificado = motivoTransacao;
            }

            item.setDataUltimaTentativa(dataUltimaTentativa);
            item.setMeioUltimaTentativa(meioUltimaTentativa);
            item.setCodigoRetornoUltimaTentativa(codigoRetornoUltimaTentativa);
            item.setMotivoRetornoUltimaTentativa(motivoRetornoUltimaTentativa + " - " + retornoIdentificado);

            if (UteisValidacao.emptyString(item.getMotivoRetornoUltimaTentativa())) {
                adicionarLog("Parcela " + item.getMovParcela() + " | SEM MOTIVO DA TENTATIVA");
            }

        } else {

            if (qtdTentativas > 0) {
                adicionarLog("Parcela " + item.getMovParcela() + " | Caiu como sem tentativa... mas qtd maior que 0");
            }

            if (item.getSituacaoParcela().equalsIgnoreCase("PG") && item.getValorParcela() == 0.0) {
                //PARCELA COM SITUAÇÃO PARA MAS COM VALOR ZERADO
                item.setMotivoRetornoUltimaTentativa("PARCELA PAGA - PARCELA SEM VALOR (0.0)");
            } else if (item.getSituacaoParcela().equalsIgnoreCase("PG") && item.getValorParcela() > 0.0) {
                item.setMotivoRetornoUltimaTentativa("PAGA MANUALMENTE - " + item.getFormaPagamento());
            } else if (item.getSituacaoParcela().equalsIgnoreCase("EA")) {
                item.setMotivoRetornoUltimaTentativa("PARCELA SEM TENTATIVA DE COBRANÇA");
            }
        }
    }

    private TipoConvenioCobrancaEnum obterTipoConvenioCobrancaEnum(TipoTransacaoEnum tipoTransacaoEnum) {
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo != null &&
                    tipo.getTipoRemessa() != null &&
                    tipo.getTipoRemessa().getTipoTransacao() != null &&
                    tipo.getTipoRemessa().getTipoTransacao().equals(tipoTransacaoEnum)) {
                return tipo;
            }
        }
        return TipoConvenioCobrancaEnum.NENHUM;
    }

    public TipoConvenioCobrancaEnum obterTipoConvenioCobrancaEnum(TipoRemessaEnum tipoRemessaEnum) {
        for (TipoConvenioCobrancaEnum tipo : TipoConvenioCobrancaEnum.values()) {
            if (tipo != null &&
                    tipo.getTipoRemessa() != null &&
                    tipo.getTipoRemessa().equals(tipoRemessaEnum)) {
                return tipo;
            }
        }
        return TipoConvenioCobrancaEnum.NENHUM;
    }

    private String obterDescricaoRetorno(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, String codigoRetorno) {
        try {
            if (tipoConvenioCobrancaEnum == null) {
                return "";
            }

            if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                return CieloECommerceRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                return ERedeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                return GetnetOnlineRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                return StoneRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGAR_ME)) {
                return PagarMeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_PAGBANK)) {
                return PagBankRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_STRIPE)) {
                return StripeRetornoEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC)) {
                return DCCCieloStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                return DCOGetNetStatusEnum.valueOff(codigoRetorno).getDescricao();
            } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                return DCCBinStatusEnum.valueOff(codigoRetorno).getDescricao();
            }

            //verificar se o codigo de retorno é um codigo pacto
            CodigoRetornoPactoEnum codigoRetornoPactoEnum = CodigoRetornoPactoEnum.consultarPorCodigo(codigoRetorno);
            if (codigoRetornoPactoEnum != null) {
                return codigoRetornoPactoEnum.getDescricao();
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
            return "";
        }
    }

    private void gravarJSONDadosBanco(List<ItemInadimplenciaTO> lista) {
        JSONArray array = new JSONArray();
        for (ItemInadimplenciaTO item : lista) {
            array.put(item.toJSON());
        }
        gravaDadosBanco(array.toString());
    }

    private List<ItemInadimplenciaTO> obterListaBanco(Integer codigo) throws Exception {
        List<ItemInadimplenciaTO> lista = new ArrayList<>();

        ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from golden where codigo = " + codigo, con);
        if (rs.next()) {
            JSONArray array = new JSONArray(rs.getString("dados"));

            for (int e = 0; e < array.length(); e++) {
                JSONObject obj = array.getJSONObject(e);
                try {
                    lista.add(new ItemInadimplenciaTO(obj));
                } catch (Exception ex) {
                    ex.printStackTrace();
                    adicionarLog(ex.getMessage());
                }
            }
        }
        return lista;
    }

    private void gravaDadosBanco(String dados) {
        try {
            String create = "CREATE TABLE dadosplanilhainadimplencia\n" +
                    "(codigo serial NOT NULL,\n" +
                    " dados text,\n" +
                    " dataregistro timestamp without time zone)";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(create, con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        try {
            String sql = "insert into dadosplanilhainadimplencia(dataregistro,dados) values (?,?)";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                pst.setString(2, dados);
                pst.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog(ex.getMessage());
        }
    }

    private Map<Integer, String> obterMapaEmpresas() throws Exception {
        Map<Integer, String> map = new HashMap<>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select codigo,nome from empresa", con);
        while (rs.next()) {
            map.put(rs.getInt("codigo"), rs.getString("nome"));
        }
        return map;
    }

    private void gerarArquivoExcel(List<ItemInadimplenciaTO> listaGeral, String diretorioSalvarArquivo, File file) throws Exception {

        Map<Integer, String> mapaEmpresas = obterMapaEmpresas();

        //agrupar por empresa
        Map<Integer, MotivoEmpresaTO> mapaMotivoEmpresa = new HashMap<>();
        for (ItemInadimplenciaTO item : listaGeral) {

            Integer codigoEmpresa = item.getEmpresaCodigo();

            MotivoEmpresaTO motivoEmpresaTO = mapaMotivoEmpresa.get(codigoEmpresa);
            if (motivoEmpresaTO == null) {
                motivoEmpresaTO = new MotivoEmpresaTO();
                motivoEmpresaTO.setListaItens(new ArrayList<>());
                motivoEmpresaTO.setEmpresa(codigoEmpresa);
                motivoEmpresaTO.setEmpresaNome(mapaEmpresas.get(codigoEmpresa));
            }

            motivoEmpresaTO.getListaItens().add(item);
            mapaMotivoEmpresa.put(codigoEmpresa, motivoEmpresaTO);
        }

        // Criar arquivo excel
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

        for (Integer empresa : mapaMotivoEmpresa.keySet()) {
            MotivoEmpresaTO motivoEmpresaTO = mapaMotivoEmpresa.get(empresa);
            gerarSheetTotalizador(hssfWorkbook, motivoEmpresaTO);
        }

        //lista geral das parcelas
        gerarSheetGeral(hssfWorkbook, listaGeral);

        //salvar arquivo
        if (file == null) {
            file = new File(diretorioSalvarArquivo + File.separator + "ProcessoPlanilhaTentativaCobranca --- " + Calendario.hoje().getTime() + ".xls");
        }

        FileOutputStream out = new FileOutputStream(file);
        hssfWorkbook.write(out);
        out.close();
        hssfWorkbook.close();
    }

    private void gerarSheetTotalizador(HSSFWorkbook hssfWorkbook, MotivoEmpresaTO motivoEmpresaTO) {

        List<ItemInadimplenciaTO> lista = motivoEmpresaTO.getListaItens();
        HSSFSheet sheet = hssfWorkbook.createSheet(motivoEmpresaTO.getEmpresaNome());

        Map<String, MotivoTO> mapaMotivo = new HashMap<>();

        //gerar a lista dos meses que existem
        Map<String, String> mapaMeses = new HashMap<>();
        for (ItemInadimplenciaTO item : lista) {
            String mesParcela = Calendario.getDataAplicandoFormatacao(item.getDataVencimentoParcela(), "yyyy/MM");
            mapaMeses.put(mesParcela, mesParcela);
        }

        //processar os motivos
        for (ItemInadimplenciaTO item : lista) {
            String motivo = item.getMotivoRetornoUltimaTentativa();

            MotivoTO motivoTO = mapaMotivo.get(motivo);
            if (motivoTO == null) {
                motivoTO = new MotivoTO();
                motivoTO.setMotivo(motivo);
            }
            mapaMotivo.put(motivo, motivoTO);
        }


        //gerar a lista de meses para cada motivo
        for (String keyMotivo : mapaMotivo.keySet()) {

            MotivoTO motivoTO = mapaMotivo.get(keyMotivo);
            motivoTO.setMeses(new HashMap<>());

            for (String key : mapaMeses.keySet()) {
                String mes = mapaMeses.get(key);
                motivoTO.getMeses().put(mes, 0.0);
            }
        }

        for (ItemInadimplenciaTO item : lista) {

            String motivo = item.getMotivoRetornoUltimaTentativa();
            String mesParcela = Calendario.getDataAplicandoFormatacao(item.getDataVencimentoParcela(), "yyyy/MM");

            MotivoTO motivoTO = mapaMotivo.get(motivo);
            Double valorMes = motivoTO.getMeses().get(mesParcela);

            motivoTO.getMeses().put(mesParcela, (valorMes + item.getValorParcela()));
        }

        int rownum = 0;
        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(rownum++);


        //criar uma lista dos meses e ordenar do mais antigo para o mais recente.
        List<ItemRelatorioTO> mesesOrdenados = new ArrayList<>();
        for (String mes : mapaMeses.keySet()) {
            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setMesAno(mes);
            mesesOrdenados.add(item);
        }
        Ordenacao.ordenarLista(mesesOrdenados, "mesAno");


        //Cabeçalho motivo
        cabecalho(hssfWorkbook, row, cellnum++, "Motivo Retorno");

        //Criar cabeçalho dos meses
        for (ItemRelatorioTO mes : mesesOrdenados) {
            cabecalho(hssfWorkbook, row, cellnum++, mes.getMesAno());
        }

        //Criar cabeçalho de total motivo
        cabecalho(hssfWorkbook, row, cellnum++, "Total Geral");

        SortedSet<String> listaMotivo = new TreeSet<>(mapaMotivo.keySet());
        for (String keyMotivo : listaMotivo) {

            MotivoTO motivoTO = mapaMotivo.get(keyMotivo);

            row = sheet.createRow(rownum++);
            cellnum = 0;

            //motivo
            criarCelula(hssfWorkbook, cellnum++, row, keyMotivo);

            Double valorTotalMotivo = 0.0;
            for (ItemRelatorioTO mes : mesesOrdenados) {
                Double valorMes = Uteis.arredondarForcando2CasasDecimais(motivoTO.getMeses().get(mes.getMesAno()));
                criarCelula(hssfWorkbook, cellnum++, row, valorMes);
                valorTotalMotivo += valorMes;
            }

            criarCelulaTotal(hssfWorkbook, cellnum++, row, valorTotalMotivo);
        }


        //total geral final
        row = sheet.createRow(rownum++);
        cellnum = 0;
        criarCelulaTotal(hssfWorkbook, cellnum++, row, "Total Geral");

        Double totalTodosMeses = 0.0;
        for (ItemRelatorioTO mes : mesesOrdenados) {

            Double valorTotalMes = 0.0;
            for (String keyMotivo : mapaMotivo.keySet()) {

                MotivoTO motivoTO = mapaMotivo.get(keyMotivo);
                Double valorMes = Uteis.arredondarForcando2CasasDecimais(motivoTO.getMeses().get(mes.getMesAno()));
                valorTotalMes += valorMes;
            }
            totalTodosMeses += valorTotalMes;

            criarCelulaTotal(hssfWorkbook, cellnum++, row, valorTotalMes);
        }

        criarCelulaTotal(hssfWorkbook, cellnum++, row, totalTodosMeses);
    }

    private void gerarSheetGeral(HSSFWorkbook hssfWorkbook, List<ItemInadimplenciaTO> listaGeral) {

        HSSFSheet sheet = hssfWorkbook.createSheet("LISTA GERAL PARCELAS");

        int rownum = 0;
        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(rownum++);
        String[] colunas = new String[]{
                "Matrícula", "Nome", "Parcela", "Valor", "Situação", "Descricao", "Dt. Vencimento", "Mês Referência",
                "Tentativas Cobrança", "Última Tentativa", "Meio Última Tentativa", "Código Retorno", "Motivo Retorno",
                "Dt. Pagamento", "Forma Pagamento", "Produtos Parcela", "Empresa"};
        for (String coluna : colunas) {
            cabecalho(hssfWorkbook, row, cellnum++, coluna);
        }

        for (ItemInadimplenciaTO item : listaGeral) {

            row = sheet.createRow(rownum++);
            cellnum = 0;

            criarCelula(hssfWorkbook, cellnum++, row, item.getMatricula());
            criarCelula(hssfWorkbook, cellnum++, row, item.getNome());
            criarCelula(hssfWorkbook, cellnum++, row, item.getMovParcela());
            criarCelula(hssfWorkbook, cellnum++, row, item.getValorParcela());
            criarCelula(hssfWorkbook, cellnum++, row, item.getSituacaoParcela_Apresentar());
            criarCelula(hssfWorkbook, cellnum++, row, item.getDescricaoParcela());
            criarCelula(hssfWorkbook, cellnum++, row, item.getDataVencimento_Apresentar());
            criarCelula(hssfWorkbook, cellnum++, row, item.getMesReferenciaVencimento());
            criarCelula(hssfWorkbook, cellnum++, row, item.getQtdTentativasCobranca());
            criarCelula(hssfWorkbook, cellnum++, row, item.getDataUltimaTentativa_Apresentar());
            criarCelula(hssfWorkbook, cellnum++, row, item.getMeioUltimaTentativa());
            criarCelula(hssfWorkbook, cellnum++, row, item.getCodigoRetornoUltimaTentativa());
            criarCelula(hssfWorkbook, cellnum++, row, item.getMotivoRetornoUltimaTentativa());
            criarCelula(hssfWorkbook, cellnum++, row, item.getDataPagamento_Apresentar());
            criarCelula(hssfWorkbook, cellnum++, row, item.getFormaPagamento());
            criarCelula(hssfWorkbook, cellnum++, row, item.getProdutosParcela());
            criarCelula(hssfWorkbook, cellnum++, row, item.getEmpresaNome());
        }
    }

    private void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue((String) valor);
        } else if (valor instanceof Integer) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Integer) valor);
        } else if (valor instanceof Double) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Double) valor);
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellType(CellType.BOOLEAN);
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private Cell cabecalho(HSSFWorkbook hssfWorkbook, Row row, int cellnum, String textoCelula) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Cell cell = row.createCell(cellnum++);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(textoCelula);
        return cell;
    }

    private void criarCelula(HSSFWorkbook hssfWorkbook, int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    private void criarCelulaTotal(HSSFWorkbook hssfWorkbook, int numeroCelula, Row row, Object valor) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        Font font = hssfWorkbook.createFont();
        font.setBold(true);
        cellStyle.setFont(font);


        Cell cell = row.createCell(numeroCelula);
        cell.setCellStyle(cellStyle);
        setValorCelula(cell, valor);
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void verificarPrecisaGerarDados(Integer codigoEmpresa, Date dataInicio) throws Exception {

        Date dataLimite = Uteis.getDate("01/05/2020", "dd/MM/yyyy");

        if (Calendario.igual(Uteis.obterPrimeiroDiaMes(dataInicio), Uteis.obterPrimeiroDiaMes(dataLimite))) {

            boolean processarMes = false;

            StringBuilder sql = new StringBuilder();
            sql.append("select exists( \n");
            sql.append("select  \n");
            sql.append("mp.codigo, \n");
            sql.append("mr.codigo as movparcelaresultadocobranca \n");
            sql.append("from movparcela mp \n");
            sql.append("left join movparcelaresultadocobranca mr on mr.movparcela = mp.codigo \n");
            sql.append("where mp.datavencimento::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(dataLimite)));
            sql.append("' and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(dataLimite))).append("' \n");
            sql.append("and mr.codigo is null) as existe \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            if (rs.next()) {
                processarMes = rs.getBoolean(1);
            }

            if (processarMes) {
                MovParcelaResultadoCobranca processo = new MovParcelaResultadoCobranca(this.con);
                processo.processarParcelas(null, dataInicio);
                processo = null;
            }
        }
    }

    public void processarPlanilhaBIInadimplencia(Integer codigoEmpresa, Date dataInicio, Date dataFim, File file, String parcelas) throws Exception {

        verificarPrecisaGerarDados(codigoEmpresa, dataInicio);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("m.codigo as codigoparcela, \n");
        sql.append("m.descricao as descricaoparcela, \n");
        sql.append("m.datavencimento, \n");
        sql.append("m.valorparcela, \n");
        sql.append("m.situacao as situacaoparcela, \n");
        sql.append("fp.descricao as formapagamento, \n");
        sql.append("mp.datalancamento as dataPagamento, \n");
        sql.append("emp.codigo as empresaCodigo, \n");
        sql.append("emp.nome as empresaNome, \n");
        sql.append("mr.nrtentativas, \n");
        sql.append("mr.dataultimatentativa,\n");
        sql.append("mr.meioultimatentativa,\n");
        sql.append("mr.codigoretorno,\n");
        sql.append("mr.motivoretorno, \n");
        sql.append("mr.produtosparcela\n");
        sql.append("FROM movparcela m  \n");
//        sql.append("LEFT JOIN movparcelaresultadocobranca mr on mr.movparcela = m.codigo \n");
        sql.append("LEFT JOIN movparcelaresultadocobranca mr on mr.codigo = (select codigo from movparcelaresultadocobranca where movparcela = m.codigo order by dataalteracao desc limit 1) \n");
        sql.append("LEFT JOIN empresa emp on emp.codigo = m.empresa \n");
        sql.append("LEFT JOIN pessoa p on p.codigo = m.pessoa \n");
        sql.append("LEFT JOIN cliente cl on cl.pessoa = p.codigo \n");
        sql.append("LEFT JOIN situacaoclientesinteticodw  dw on dw.codigopessoa = p.codigo \n");
        sql.append("LEFT JOIN contrato c on c.codigo = m.contrato \n");
        sql.append("LEFT JOIN contratooperacao coo ON coo.contrato = c.codigo AND coo.tipooperacao = 'CA' \n");
//        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.movparcela = m.codigo \n");
        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.codigo = (select codigo from pagamentomovparcela where movparcela = m.codigo order by valorpago desc limit 1) \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pm.movpagamento \n");
        sql.append("LEFT JOIN formapagamento fp on mp.formapagamento = fp.codigo \n");
        if (!UteisValidacao.emptyString(parcelas)) {
            sql.append("WHERE m.codigo in (").append(parcelas).append(") \n");
        } else {
            sql.append("WHERE m.situacao not in ('RG') \n");
            sql.append("AND (m.situacao IN ('EA', 'PG') OR (m.datavencimento :: DATE <= coo.dataoperacao :: DATE)) \n");
            sql.append("AND m.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");

            if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
                sql.append("AND m.empresa = ").append(codigoEmpresa).append(" \n");
            }
        }

        sql.append(" order by m.datavencimento \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        List<ItemInadimplenciaTO> lista = new ArrayList<>();

        int i = 0;
        while (rs.next()) {
            adicionarLog("Atual " + ++i + "/" + total);

            ItemInadimplenciaTO item = new ItemInadimplenciaTO();
            item.setMatricula(rs.getString("matricula"));
            item.setNome(rs.getString("nome"));
            item.setMovParcela(rs.getInt("codigoparcela"));
            item.setValorParcela(rs.getDouble("valorparcela"));
            item.setDataVencimentoParcela(rs.getDate("datavencimento"));
            item.setSituacaoParcela(rs.getString("situacaoparcela"));
            item.setDescricaoParcela(rs.getString("descricaoparcela"));
            item.setFormaPagamento(rs.getString("formapagamento"));
            item.setDataPagamento(rs.getTimestamp("dataPagamento"));
            item.setEmpresaNome(rs.getString("empresaNome"));
            item.setEmpresaCodigo(rs.getInt("empresaCodigo"));
            item.setProdutosParcela(rs.getString("produtosparcela"));

            item.setQtdTentativasCobranca(rs.getInt("nrtentativas"));
            item.setDataUltimaTentativa(rs.getTimestamp("dataultimatentativa"));
            item.setMeioUltimaTentativa(rs.getString("meioultimatentativa"));
            item.setCodigoRetornoUltimaTentativa(rs.getString("codigoretorno"));
            item.setMotivoRetornoUltimaTentativa(rs.getString("motivoretorno"));

            lista.add(item);
        }
        gerarArquivoExcel(lista, null, file);
    }
}

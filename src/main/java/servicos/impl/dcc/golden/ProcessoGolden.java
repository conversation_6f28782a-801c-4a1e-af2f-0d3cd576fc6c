/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.golden;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.dcc.bin.DCCBinStatusEnum;
import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 15/05/2020
 */
public class ProcessoGolden {

    private Connection con;

    public ProcessoGolden(Connection con) throws Exception {
        this.con = con;
    }

    public static void main(String... args) {
        Connection con = null;
        ProcessoGolden service = null;
        try {
            Uteis.debug = true;
            Uteis.logar(null, "ProcessoGolden | Início");

            con = DriverManager.getConnection("***************************************", "zillyonweb", "pactodb");
            service = new ProcessoGolden(con);

            Integer empresa = 0;
            String mesReferencia = "04/2020";
//            String mesReferencia = "";

            service.consultar(empresa, mesReferencia);

        } catch (Exception ex) {
            Logger.getLogger(ProcessoGolden.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.logar(null, "ProcessoGolden | Fim");
            service = null;
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ignored) {
                }
            }
        }
    }

    public void consultar(Integer empresa, String mesReferencia) throws Exception {

        Date mes = null;
        if (!UteisValidacao.emptyString(mesReferencia)) {
            mes = Uteis.getDate(mesReferencia, "MM/yyyy");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("cl.matricula, \n");
        sql.append("p.nome, \n");
        sql.append("m.codigo as codigoparcela, \n");
        sql.append("m.descricao as descricaoparcela, \n");
        sql.append("m.datavencimento, \n");
        sql.append("m.valorparcela, \n");
        sql.append("m.situacao as situacaoparcela, \n");
        sql.append("fp.descricao as formapagamento, \n");
        sql.append("emp.nome as empresa, \n");
        sql.append("array_to_string(array(select mov.descricao from movproduto mov inner join movprodutoparcela mpp on mpp.movproduto = mov.codigo where mpp.movparcela = mp.codigo), '<br/>', '') as produtos \n");
        sql.append("FROM movparcela m  \n");
        sql.append("LEFT JOIN empresa emp on emp.codigo = m.empresa  \n");
        sql.append("LEFT JOIN pessoa p on p.codigo = m.pessoa  \n");
        sql.append("LEFT JOIN cliente cl on cl.pessoa = p.codigo  \n");
        sql.append("LEFT JOIN situacaoclientesinteticodw  dw on dw.codigopessoa = p.codigo  \n");
        sql.append("LEFT JOIN contrato c on c.codigo = m.contrato  \n");
        sql.append("LEFT JOIN contratooperacao coo ON coo.contrato = c.codigo AND coo.tipooperacao = 'CA'  \n");
        sql.append("LEFT JOIN pagamentomovparcela pm ON pm.movparcela = m.codigo  \n");
        sql.append("LEFT JOIN movpagamento mp ON mp.codigo = pm.movpagamento  \n");
        sql.append("LEFT JOIN formapagamento fp on mp.formapagamento = fp.codigo \n");
        sql.append("WHERE m.situacao not in ('RG') \n");
        sql.append("AND (m.situacao IN ('EA', 'PG') OR (m.datavencimento :: DATE <= coo.dataoperacao :: DATE)) \n");

        if (mes == null) {
            sql.append("AND m.datavencimento::date between '01/11/2019' and '30/04/2020' \n");
        } else {
            sql.append("AND m.datavencimento::date between '").append(Uteis.getDataFormatoBD(Uteis.obterPrimeiroDiaMes(mes)));
            sql.append("' and '").append(Uteis.getDataFormatoBD(Uteis.obterUltimoDiaMes(mes))).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND m.empresa = ").append(empresa).append(" \n");
        }

        sql.append(" order by m.datavencimento \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        List<ItemInadimplenciaTO> lista = new ArrayList<>();

        int i = 0;
        while (rs.next()) {
            Uteis.logar(null, "Atual " + ++i + "/" + total);
            ItemInadimplenciaTO item = new ItemInadimplenciaTO();
            item.setMatricula(rs.getString("matricula"));
            item.setNome(rs.getString("nome"));
            item.setMovParcela(rs.getInt("codigoparcela"));
            item.setDescricaoParcela(rs.getString("descricaoparcela"));
            item.setValorParcela(rs.getDouble("valorparcela"));
            item.setSituacaoParcela(rs.getString("situacaoparcela"));
            item.setFormaPagamento(rs.getString("formapagamento"));
            item.setEmpresaNome(rs.getString("empresa"));
            item.setDataVencimentoParcela(rs.getDate("datavencimento"));
            item.setProdutosParcela(rs.getString("produtos"));
            consultarUltimaTentativa(item);

            lista.add(item);
        }

        gerarArquivoExcel(lista, empresa, mes);
    }

    private void consultarUltimaTentativa(ItemInadimplenciaTO item) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select * from ( \n");
        sql.append("select  \n");
        sql.append("r.dataregistro as data, \n");
        sql.append("CASE  \n");
        sql.append("WHEN r.tipo =  2 THEN 'CIELO EDI' \n");
        sql.append("WHEN r.tipo =  8 THEN 'GETNET EDI' \n");
        sql.append("WHEN r.tipo = 12 THEN 'BIN EDI' \n");
        sql.append("ELSE 'OUTRA' END as tipo, \n");
        sql.append("split_part(split_part(split_part(ri.props, 'StatusVenda=', 2), ',', 1), '}', 1) AS codigoRetorno, \n");
        sql.append("'' AS motivoRetorno \n");
        sql.append("from remessaitem ri \n");
        sql.append("inner join remessa r on r.codigo = ri.remessa \n");
        sql.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
        sql.append("where (ri.movparcela = ").append(item.getMovParcela()).append(" or rim.movparcela = ").append(item.getMovParcela()).append(") \n");
        sql.append("union \n");
        sql.append("select  \n");
        sql.append("t.dataprocessamento as data, \n");
        sql.append("CASE  \n");
        sql.append("WHEN t.tipo = 11 THEN 'STONE ONLINE' \n");
        sql.append("WHEN t.tipo =  2 THEN 'VINDI ONLINE' \n");
        sql.append("WHEN t.tipo = 3 THEN 'CIELO ONLINE' \n");
        sql.append("ELSE 'OUTRA' END as tipo, \n");
        sql.append("t.codigoretorno, \n");
        sql.append("t.motivoRetorno \n");
        sql.append("from transacao t \n");
        sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sql.append("where tm.movparcela = ").append(item.getMovParcela()).append(" \n");
        sql.append(") sql \n");
        sql.append("order by sql.data desc limit 1 \n");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            item.setMeioUltimaTentativa(rs.getString("tipo"));
            item.setCodigoRetornoUltimaTentativa(rs.getString("codigoretorno"));

            String motivo = "ONLINE - " + rs.getString("motivoretorno");
            if (item.getMeioUltimaTentativa().contains("CIELO EDI")) {
                motivo = ("EDI - " + DCCCieloStatusEnum.valueOff(item.getCodigoRetornoUltimaTentativa()).getDescricao());
            } else if (item.getMeioUltimaTentativa().contains("GETNET EDI")) {
                motivo = ("EDI - " + DCOGetNetStatusEnum.valueOff(item.getCodigoRetornoUltimaTentativa()).getDescricao());
            } else if (item.getMeioUltimaTentativa().contains("BIN EDI")) {
                motivo = ("EDI - " + DCCBinStatusEnum.valueOff(item.getCodigoRetornoUltimaTentativa()).getDescricao());
            }

            item.setMotivoRetornoUltimaTentativa(motivo);
        }
    }

    private void gerarArquivoExcel(List<ItemInadimplenciaTO> lista, Integer empresa, Date mes) throws IOException {

        Uteis.logar(null, "Gerar arquivo excel");

        // Criar arquivo excel
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

        int rownum = 0;
        int cellnum = 0;
        Cell cell;
        Row row;

        // Gerar planilha
//        String sheetName = Calendario.getData(logCobrancaPacto.getDataCobranca(), "yyyyMMdd HHmmss");
        HSSFSheet sheet = hssfWorkbook.createSheet("TESTE");

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        // Configurando Cabecalho
        CellStyle estiloCabecalho = hssfWorkbook.createCellStyle();

        row = sheet.createRow(rownum++);
        String[] colunas = new String[]{"Matricula", "Nome", "CodigoParcela", "DescricaoParcela", "Vencimento", "MesVencimento",
                "ValorParcela", "ValorParcela2", "SituaçãoParcela", "Tipo", "CodigoRetorno", "MotivoRetorno", "FormaPagamento", "Produtos", "Empresa"};
        for (String coluna : colunas) {
            cell = cabecalho(estiloCabecalho, row, cellnum++, coluna);
        }

        JSONArray array = new JSONArray();

        for (ItemInadimplenciaTO item : lista) {

            JSONObject json = new JSONObject();

            row = sheet.createRow(rownum++);
            cellnum = 0;

            criarCelula(cellnum++, row, item.getMatricula());
            criarCelula(cellnum++, row, item.getNome());
            criarCelula(cellnum++, row, item.getMovParcela());
            criarCelula(cellnum++, row, item.getDescricaoParcela());
            criarCelula(cellnum++, row, item.getDataVencimento_Apresentar());
            criarCelula(cellnum++, row, Calendario.getDataAplicandoFormatacao(item.getDataVencimentoParcela(), "yyyy/MM"));
            criarCelula(cellnum++, row, item.getValorParcela());
            criarCelula(cellnum++, row, item.getSituacaoParcela_Apresentar());
            criarCelula(cellnum++, row, item.getMeioUltimaTentativa());
            criarCelula(cellnum++, row, item.getCodigoRetornoUltimaTentativa());
            criarCelula(cellnum++, row, item.getMotivoRetornoUltimaTentativa());
            criarCelula(cellnum++, row, item.getFormaPagamento());
            criarCelula(cellnum++, row, item.getProdutosParcela());
            criarCelula(cellnum++, row, item.getEmpresaNome());

            json.put("matricula", item.getMatricula());
            json.put("nome", item.getNome());
            json.put("movParcela", item.getMovParcela());
            json.put("descricaoParcela", item.getDescricaoParcela());
            json.put("dataVencimento", item.getDataVencimento_Apresentar());
            json.put("mesReferencia", Calendario.getDataAplicandoFormatacao(item.getDataVencimentoParcela(), "yyyy/MM"));
            json.put("valorParcela", item.getValorParcela());
            json.put("situacaoParcela", item.getSituacaoParcela_Apresentar());
            json.put("tipoCobranca", item.getMeioUltimaTentativa());
            json.put("codigoRetorno", item.getCodigoRetornoUltimaTentativa());
            json.put("motivoRetorno", item.getMotivoRetornoUltimaTentativa());
            json.put("formaPagamento", item.getFormaPagamento());
            json.put("produtos", item.getProdutosParcela());
            json.put("empresa", item.getEmpresaNome());
            array.put(json);
        }

        gravaDadosBanco(array.toString());

        //salvar arquivo
        File file = null;
        if (mes == null) {
            file = new File("C:\\PactoJ\\golden\\Golden-Geral--" + Calendario.hoje().getTime() + ".xls");
        } else {
            file = new File("C:\\PactoJ\\golden\\Golden-Empresa-" + empresa + "-Mes-" + Calendario.getDataAplicandoFormatacao(mes, "MM-yyyy") + "--" + Calendario.hoje().getTime() + ".xls");
        }

        FileOutputStream out = new FileOutputStream(file);
        hssfWorkbook.write(out);
        out.close();
        hssfWorkbook.close();
    }

    private void gravaDadosBanco(String dados) {
        try {
            String sql = "insert into golden(dataregistro,dados) values (?,?)";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                pst.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                pst.setString(2, dados);
                pst.execute();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void criarCelula(int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    private void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellValue((String) valor);
        } else if (valor instanceof Number) {
            cell.setCellValue(((Number) valor).doubleValue());
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private Cell cabecalho(CellStyle estilo, Row row, int cellnum, String textoCelula) {
        estilo.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        estilo.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        estilo.setAlignment(HorizontalAlignment.CENTER);
        estilo.setVerticalAlignment(VerticalAlignment.CENTER);

        Cell cell = row.createCell(cellnum++);
        cell.setCellStyle(estilo);
        cell.setCellValue(textoCelula);
        return cell;
    }
}

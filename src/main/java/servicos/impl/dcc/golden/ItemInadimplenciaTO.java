package servicos.impl.dcc.golden;

import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 15/05/2020
 */
public class ItemInadimplenciaTO {

    private String matricula;
    private String nome;
    private Integer movParcela;
    private Double valorParcela;
    private Date dataVencimentoParcela;
    private String situacaoParcela;
    private String descricaoParcela;
    private Integer qtdTentativasCobranca;
    private Date dataUltimaTentativa;
    private String meioUltimaTentativa;
    private String codigoRetornoUltimaTentativa;
    private String motivoRetornoUltimaTentativa;
    private String formaPagamento;
    private Date dataPagamento;
    private String produtosParcela;
    private String empresaNome;
    private Integer empresaCodigo;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;
    private Integer tipoConvenioCobranca;

    public ItemInadimplenciaTO(){
    }

    public ItemInadimplenciaTO(JSONObject json) throws Exception {
        this.matricula = json.optString("matricula");
        this.nome = json.optString("nome");
        this.movParcela = json.optInt("movParcela");
        this.valorParcela = json.optDouble("valorParcela");
        this.situacaoParcela = json.optString("situacaoParcela");
        this.descricaoParcela = json.optString("descricaoParcela");
        this.qtdTentativasCobranca = json.optInt("qtdTentativasCobranca");
        this.meioUltimaTentativa = json.optString("meioUltimaTentativa");
        this.codigoRetornoUltimaTentativa = json.optString("codigoRetornoUltimaTentativa");
        this.motivoRetornoUltimaTentativa = json.optString("motivoRetornoUltimaTentativa");
        this.formaPagamento = json.optString("formaPagamento");
        this.produtosParcela = json.optString("produtosParcela");
        this.empresaNome = json.optString("empresaNome");
        this.empresaCodigo = json.optInt("empresaCodigo");

        if (!UteisValidacao.emptyString(json.optString("dataPagamento"))) {
            this.dataPagamento = Uteis.getDate(json.optString("dataPagamento"), "dd/MM/yyyy HH:mm:ss");
        }
        if (!UteisValidacao.emptyString(json.optString("dataUltimaTentativa"))) {
            this.dataUltimaTentativa = Uteis.getDate(json.optString("dataUltimaTentativa"), "dd/MM/yyyy HH:mm:ss");
        }
        if (!UteisValidacao.emptyString(json.optString("dataVencimentoParcela"))) {
            this.dataVencimentoParcela = Uteis.getDate(json.optString("dataVencimentoParcela"), "dd/MM/yyyy");
        }
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("matricula", this.getMatricula());
        json.put("nome", this.getNome());
        json.put("movParcela", this.getMovParcela());
        json.put("valorParcela", this.getValorParcela());
        json.put("dataVencimentoParcela", this.getDataVencimento_Apresentar());
        json.put("situacaoParcela", this.getSituacaoParcela());
        json.put("situacaoParcelaApresentar", this.getSituacaoParcela_Apresentar());
        json.put("descricaoParcela", this.getDescricaoParcela());
        json.put("qtdTentativasCobranca", this.getQtdTentativasCobranca());
        json.put("dataUltimaTentativa", this.getDataUltimaTentativa_Apresentar());
        json.put("meioUltimaTentativa", this.getMeioUltimaTentativa());
        json.put("codigoRetornoUltimaTentativa", this.getCodigoRetornoUltimaTentativa());
        json.put("motivoRetornoUltimaTentativa", this.getMotivoRetornoUltimaTentativa());
        json.put("formaPagamento", this.getFormaPagamento());
        json.put("dataPagamento", this.getDataPagamento_Apresentar());
        json.put("produtosParcela", this.getProdutosParcela());
        json.put("empresaNome", this.getEmpresaNome());
        json.put("empresaCodigo", this.getEmpresaCodigo());
        return json;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getMovParcela() {
        if (movParcela == null) {
            movParcela = 0;
        }
        return movParcela;
    }

    public void setMovParcela(Integer movParcela) {
        this.movParcela = movParcela;
    }

    public Double getValorParcela() {
        if (valorParcela == null) {
            valorParcela = 0.0;
        }
        return Uteis.arredondarForcando2CasasDecimais(valorParcela);
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Date getDataVencimentoParcela() {
        return dataVencimentoParcela;
    }

    public void setDataVencimentoParcela(Date dataVencimentoParcela) {
        this.dataVencimentoParcela = dataVencimentoParcela;
    }

    public String getSituacaoParcela() {
        if (situacaoParcela == null) {
            situacaoParcela = "";
        }
        return situacaoParcela;
    }

    public void setSituacaoParcela(String situacaoParcela) {
        this.situacaoParcela = situacaoParcela;
    }

    public String getDataVencimento_Apresentar() {
        if (getDataVencimentoParcela() == null) {
            return "";
        } else {
            return Calendario.getDataAplicandoFormatacao(getDataVencimentoParcela(), "dd/MM/yyyy");
        }
    }

    public String getMesReferenciaVencimento() {
        if (getDataVencimentoParcela() == null) {
            return "";
        } else {
            return  Calendario.getDataAplicandoFormatacao(getDataVencimentoParcela(), "yyyy/MM");
        }
    }

    public String getDescricaoParcela() {
        if (descricaoParcela == null) {
            descricaoParcela = "";
        }
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getSituacaoParcela_Apresentar() {
        if (getSituacaoParcela().equals("EA")) {
            return "Em Aberto";
        }
        if (getSituacaoParcela().equals("PG")) {
            return "Pago";
        }
        if (getSituacaoParcela().equals("CA")) {
            return "Cancelado";
        }
        if (getSituacaoParcela().equals("RG")) {
            return "Renegociado";
        }
        return "";
    }

    public String getFormaPagamento() {
        if (formaPagamento == null) {
            formaPagamento = "";
        }
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Date getDataUltimaTentativa() {
        return dataUltimaTentativa;
    }

    public void setDataUltimaTentativa(Date dataUltimaTentativa) {
        this.dataUltimaTentativa = dataUltimaTentativa;
    }

    public String getDataUltimaTentativa_Apresentar() {
        if (getDataUltimaTentativa() == null) {
            return "";
        } else {
            return Calendario.getDataAplicandoFormatacao(getDataUltimaTentativa(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public String getProdutosParcela() {
        if (produtosParcela == null) {
            produtosParcela = "";
        }
        return produtosParcela;
    }

    public void setProdutosParcela(String produtosParcela) {
        this.produtosParcela = produtosParcela;
    }

    public Integer getEmpresaCodigo() {
        if (empresaCodigo == null) {
            empresaCodigo = 0;
        }
        return empresaCodigo;
    }

    public void setEmpresaCodigo(Integer empresaCodigo) {
        this.empresaCodigo = empresaCodigo;
    }

    public String getEmpresaNome() {
        if (empresaNome == null) {
            empresaNome = "";
        }
        return empresaNome;
    }

    public void setEmpresaNome(String empresaNome) {
        this.empresaNome = empresaNome;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getDataPagamento_Apresentar() {
        if (getDataPagamento() == null) {
            return "";
        } else {
            return Calendario.getDataAplicandoFormatacao(getDataPagamento(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public Integer getQtdTentativasCobranca() {
        if (qtdTentativasCobranca == null) {
            qtdTentativasCobranca = 0;
        }
        return qtdTentativasCobranca;
    }

    public void setQtdTentativasCobranca(Integer qtdTentativasCobranca) {
        this.qtdTentativasCobranca = qtdTentativasCobranca;
    }

    public String getMeioUltimaTentativa() {
        if (meioUltimaTentativa == null) {
            meioUltimaTentativa = "";
        }
        return meioUltimaTentativa;
    }

    public void setMeioUltimaTentativa(String meioUltimaTentativa) {
        this.meioUltimaTentativa = meioUltimaTentativa;
    }

    public String getCodigoRetornoUltimaTentativa() {
        if (codigoRetornoUltimaTentativa == null) {
            codigoRetornoUltimaTentativa = "";
        }
        return codigoRetornoUltimaTentativa;
    }

    public void setCodigoRetornoUltimaTentativa(String codigoRetornoUltimaTentativa) {
        this.codigoRetornoUltimaTentativa = codigoRetornoUltimaTentativa;
    }

    public String getMotivoRetornoUltimaTentativa() {
        if (motivoRetornoUltimaTentativa == null) {
            motivoRetornoUltimaTentativa = "";
        }
        return motivoRetornoUltimaTentativa;
    }

    public void setMotivoRetornoUltimaTentativa(String motivoRetornoUltimaTentativa) {
        this.motivoRetornoUltimaTentativa = motivoRetornoUltimaTentativa;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        if (operacaoRetornoCobranca == null) {
            operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
        }
        return operacaoRetornoCobranca;
    }

    public void setOperacaoRetornoCobranca(OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public Integer getTipoConvenioCobranca() {
        if (tipoConvenioCobranca == null) {
            tipoConvenioCobranca = TipoConvenioCobrancaEnum.NENHUM.getCodigo();
        }
        return tipoConvenioCobranca;
    }

    public void setTipoConvenioCobranca(Integer tipoConvenioCobranca) {
        this.tipoConvenioCobranca = tipoConvenioCobranca;
    }
}

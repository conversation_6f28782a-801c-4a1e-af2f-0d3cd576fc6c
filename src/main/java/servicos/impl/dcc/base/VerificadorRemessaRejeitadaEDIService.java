/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 24/03/2020
 */
public class VerificadorRemessaRejeitadaEDIService extends SuperEntidade {

    private String key;
    private List<EmpresaVO> listaEmpresas;
    private ConfiguracaoSistemaVO configSistema;
    public Integer[] CONVENIOS_DCC_EDI = new Integer[]{TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo()};
    private RemessaService remessaService;
    private UsuarioVO usuarioVO;

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Conexao.guardarConexaoForJ2SE(chave, new DAO().obterConexaoEspecifica(chave));

            VerificadorRemessaRejeitadaEDIService service = new VerificadorRemessaRejeitadaEDIService();
            service.setKey(chave);
            service.processar();
            service = null;
        } catch (Exception ex) {
            Logger.getLogger(VerificadorRemessaRejeitadaEDIService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public VerificadorRemessaRejeitadaEDIService() throws Exception {
        this.listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        this.configSistema = getFacade().getConfiguracaoSistema().consultarConfigs(Uteis.NIVELMONTARDADOS_TODOS);
        this.remessaService = new RemessaService();
        this.usuarioVO = getFacade().getZWFacade().getUsuarioRecorrencia();
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public void processar() throws Exception {
        recursoHabilitado();
        for (EmpresaVO empresa : listaEmpresas) {

            Uteis.logar(null, "### Iniciando Verificador EDI Empresa: " + empresa.getNome());

            List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS, CONVENIOS_DCC_EDI, SituacaoConvenioCobranca.ATIVO);

            for (ConvenioCobrancaVO conv : convenios) {
                try {
                    conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(conv.getCodigo(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

                    verificarRemessasRejeitadas(conv, empresa);

                    Uteis.logar(null, "### Iniciando Envio Das Remessas: " + conv.getDescricao());
                    remessaService.processoUnicoEnviarRemessas(conv.getCodigo(), empresa.getCodigo(), this.usuarioVO, false);
                    Uteis.logar(null, "### Fim Envio Das Remessas: " + conv.getDescricao());
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "### Convênio: " + conv.getDescricao() + " | Erro: " + ex.getMessage());
                }
            }
        }
    }

    public void verificarRemessasRejeitadas(ConvenioCobrancaVO conv, EmpresaVO empresaVO) throws Exception {
        recursoHabilitado();

        if (!conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            String msg = "### Convênio não é do tipo GetNet! Tipo não habilitado para verificar remessas rejeitadas: " + conv.getDescricao();
            Uteis.logar(null, msg);
            throw new Exception(msg);
        }

        if (configSistema.isEnviarRemessasRemotamente()) {
            Uteis.logar(null, "### Iniciando Verificador Convênio: " + conv.getDescricao());

            String arquivoRejeitado;
            if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                arquivoRejeitado = ".REJ";
            } else {
                throw new Exception("Convênio não é GetNet!");
            }

            List<Map<String, File>> arquivos = obterArquivosRejeitados(conv, arquivoRejeitado);
            List<RemessaVO> listaRemessaAlteradas;
            if (!arquivos.isEmpty()) {
                Uteis.logar(null, "### Processar arquivos rejeitados do convênio: " + conv.getDescricao() + " | Total de arquivos encontrados: " + arquivos.size());
                listaRemessaAlteradas = processarArquivosRejeitado(arquivos, conv);
                Uteis.logar(null, "### Arquivos rejeitados do convênio: " + conv.getDescricao() + " foram processados!");
            } else {
                throw new Exception("Nenhum arquivo rejeitado foi encontrado");
            }


            //verificar se existe existe uma mais recente desse convênio
            //e que a remessa está com status diferente de gerada!!!
            if (!UteisValidacao.emptyList(listaRemessaAlteradas)) {
                for (RemessaVO remessaVO : listaRemessaAlteradas) {
                    StringBuilder sql = new StringBuilder();
                    sql.append("select exists(select codigo from remessa where situacaoremessa <> ").append(SituacaoRemessaEnum.GERADA.getId());
                    sql.append(" and empresa = ").append(empresaVO.getCodigo());
                    sql.append(" and conveniocobranca = ").append(conv.getCodigo());
                    sql.append(" and codigo > ").append(remessaVO.getCodigo());
                    sql.append(" ) as existe ");

                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                    boolean existe = false;
                    if (rs.next()) {
                        existe = rs.getBoolean("existe");
                    }

                    if (existe) {
                        throw new Exception("Existe remessa mais recente que está com situação diferente de gerada!");
                    }
                }
            }

            Uteis.logar(null, "### Fim Verificador Convênio: " + conv.getDescricao());
        } else {
            Uteis.logar(null, "### Configuração EnviarRemessasRemotamente em ConfiguracaoSistema não está habilitada!");
        }
    }

    private List<Map<String, File>> obterArquivosRejeitados(ConvenioCobrancaVO conv, String arquivoRejeitado) throws Exception {
        String msgErro = "";
        int tentar = 3;
        while (tentar > 0) {
            try {
                return RetornoService.receberRetornos(conv, false, null, arquivoRejeitado);
            } catch (Exception ex) {
                msgErro = ex.getMessage();
                Uteis.logar(null, "### Forçar obter arquivos rejeitados remoto: " + conv.getDescricao() + " | Erro: " + msgErro);
                tentar--;
            }
        }
        throw new Exception("Erro ao obter arquivos! Erro: " + msgErro);
    }

    private List<RemessaVO> processarArquivosRejeitado(final List<Map<String, File>> arquivos, final ConvenioCobrancaVO conv) {
        List<RemessaVO> listaRemessaAlteradas = new ArrayList<>();
        for (Map<String, File> entrada : arquivos) {
            Set<String> s = entrada.keySet();
            FORARQ:
            for (String fileName : s) {
                File f = entrada.get(fileName);
                try {
                    if (f.length() > 0) {
                        if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) && !fileName.contains(conv.getNumeroContrato())) {
                            continue;
                        }

                        String nomeArquivo = f.getName();

                        StringBuilder arq = FileUtilities.readContentFile(fileName);

                        RegistroRemessa regHead = LayoutRemessaBase.obterHeaderRetorno(arq, conv.getTipo());
                        if (regHead.get(DCCAttEnum.NumeroEstabelecimento.name()).isEmpty() && regHead.get(DCCAttEnum.ReservadoEstabelecimento.name()).isEmpty()) {
                            Uteis.logar(null, "Erro: Leitura do header falhou para esse arquivo com esse tipo de convênio");
                            continue;
                        }

                        String sql = "";
                        if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                            // Getnet "ReservadoEstabelecimento" é o codigo da remessa!!!!
                            //by Luiz Felipe
                            Integer codigoRemessa = Integer.parseInt(regHead.getValue(DCCAttEnum.ReservadoEstabelecimento.name()).trim());
                            sql = String.format("select * from remessa "
                                            + "where empresa = %s and codigo = %s and (head like ('%%%s%%') and head like ('%%%s%%'))",
                                    conv.getEmpresa().getCodigo(), codigoRemessa,
                                    regHead.get(DCCAttEnum.NumeroEstabelecimento.name()),
                                    regHead.get(DCCAttEnum.ReservadoEstabelecimento.name()));
                        }

                        ResultSet criarConsulta = ZillyonWebFacade.criarConsulta(sql, getCon());
                        List<RemessaVO> remessas = Remessa.montarDadosConsulta(criarConsulta, getCon());
                        if (remessas.size() == 1) {
                            RemessaVO remessaVO = remessas.get(0);
                            Uteis.logar(null, "Encontrei remessa compatível: " + remessaVO + ", vou processar alterar para GERADA para realizar reenvio;..");

                            if (!remessaVO.getSituacaoRemessa().equals(SituacaoRemessaEnum.REMESSA_ENVIADA)) {
                                Uteis.logar(null, "Remessa está com situação \"" + remessaVO.getSituacaoRemessa().name() + "\" diferente de REMESSA_ENVIADA: " + remessaVO + ", vou ignorar...");
                                continue;
                            }

                            Remessa remessaDAO = new Remessa(con);
                            boolean arquivoJaProcessado = remessaDAO.arquivoRejeitadoProcessado(remessaVO.getCodigo(), remessaVO.getConvenioCobranca().getCodigo(), nomeArquivo);
                            remessaDAO = null;
                            if (arquivoJaProcessado) {
                                Uteis.logar(null, "Remessa está com situação diferente de REMESSA_ENVIADA: " + remessaVO + ", vou ignorar...");
                                continue;
                            }


                            try {
                                con.setAutoCommit(false);

                                Uteis.logar(null, "Vou alterar remessa para GERADA: " + remessaVO.getCodigo());
                                remessaDAO = new Remessa(con);
                                remessaVO.setSituacaoRemessa(SituacaoRemessaEnum.GERADA);
                                remessaDAO.alterar(remessaVO);
                                remessaDAO.registrarLogReenvioReenvio(remessaVO);
                                remessaDAO.incluirRemessaRejeitada(remessaVO.getCodigo(), remessaVO.getConvenioCobranca().getCodigo(), nomeArquivo);

                                con.commit();
                            } catch (Exception ex) {
                                con.rollback();
                                con.setAutoCommit(true);
                                throw ex;
                            } finally {
                                con.setAutoCommit(true);
                                remessaDAO = null;
                            }

                            listaRemessaAlteradas.add(remessaVO);

                            Uteis.logar(null, "Processamento retorno da Remessa: " + remessaVO + " concluído com sucesso! ");
                        } else if (remessas.size() > 1) {
                            Uteis.logar(null, "Erro: Mais de uma Remessa compatível com este Retorno foi encontrada");
                        }
                    } else {
                        Uteis.logar(null, "Erro: Desprezando arquivo vazio: " + f.getAbsolutePath());
                    }
                } catch (Exception e) {
                    Uteis.logar(null, String.format("Erro %s ao processar arquivo %s", e.getMessage(), f.getAbsolutePath()));
                    e.printStackTrace();
                }
            }
        }
        return listaRemessaAlteradas;
    }

    private void recursoHabilitado() throws Exception {
        ConfiguracaoSistema configDAO = new ConfiguracaoSistema(con);
        boolean habilitado = configDAO.isUsarVerificadorRemessasRejeitadas();
        configDAO = null;

        if (!habilitado) {
            throw new Exception("Recurso não habilitado! UsarVerificadorRemessasRejeitadas em ConfiguracaoSistema");
        }

    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class PreencherNSUStonneConnect extends SuperEntidade {

    public PreencherNSUStonneConnect() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            Connection con = DriverManager.getConnection("************************************/bdzillyonengenhariadocorpomatriz__", "postgres", "pactodb");
//            Connection con = DriverManager.getConnection("********************************************************", "postgres", "pactodb");
//            ajustar(con);
            ajustarTodasDaZona(con);
        } catch (Exception ex) {
            Logger.getLogger(PreencherNSUStonneConnect.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarTodasDaZona(Connection conP) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT datname as banco FROM pg_database where datname ilike '%bdzillyon%'", conP);
        while (rs.next()) {
            Uteis.logarDebug("Ajustar banco | " + rs.getString("banco"));
            try (Connection con = DriverManager.getConnection("************************************/" + rs.getString("banco"), "postgres", "pactodb")) {
                ajustar(con);
            }
        }
    }

    public static void ajustar(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("m.codigo,\n");
        sql.append("m.respostarequisicaopinpad \n");
        sql.append("FROM movpagamento m \n");
        sql.append("where m.nsu = '' \n");
        sql.append("and m.respostarequisicaopinpad ilike '%STONE_CONNECT%' \n");
        sql.append("order by m.codigo ");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as a", con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        Integer atual = 0;
        while (rs.next()) {
            try {
                Uteis.logarDebug(++atual + "/" + total + " - Ajustar NSU StoneConnect");
                Integer codMovPagamento = rs.getInt("codigo");
                JSONObject json = new JSONObject(rs.getString("respostarequisicaopinpad"));
                if (json.optString("pinpadTipo").equals("STONE_CONNECT")) {
                    JSONObject charge = new JSONObject(json.optString("respostaRequisicao")).getJSONArray("charges").getJSONObject(0);
                    String code = charge.getString("code");
                    if (!UteisValidacao.emptyString(code)) {
                        SuperFacadeJDBC.executarUpdateExecutarProcessos("update movpagamento set nsu = '" + code + "' where codigo = " + codMovPagamento, con);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public static void ajustarNSUTransacao(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select t.codigo , t.paramsresposta  \n");
        sql.append("from transacao t  \n");
        sql.append("inner join conveniocobranca c on c.codigo = t.conveniocobranca   \n");
        sql.append("where paramsresposta <> '' \n");
        sql.append("and paramsresposta like '%RcptTxId%' \n");
        sql.append("and c.tipoconvenio = 21 \n");
        sql.append("and dataprocessamento::date between '2023-01-01' and '2023-03-10' \n");
        sql.append("order by t.codigo desc ");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as a", con);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        Integer atual = 0;
        while (rs.next()) {
            try {
                Uteis.logarDebug(++atual + "/" + total + " - Ajustar NSU na transação");
                Integer codTransacao = rs.getInt("codigo");
                if (!UteisValidacao.emptyString(rs.getString("paramsresposta"))) {
                    String nsu = obterValorParametroXML(rs.getString("paramsresposta"), "RcptTxId");
                    if (!UteisValidacao.emptyString("nsu")) {
                        SuperFacadeJDBC.executarUpdateExecutarProcessos("update transacao set nsu = '" + nsu + "' where codigo = " + codTransacao, con);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    public static String obterValorParametroXML(String xml, String parametro) {
        if (!UteisValidacao.emptyString(xml) && xml.contains("<"+parametro+">")) {
            try {
                String[] split = xml.split("<"+parametro+">");
                String[] split2 = split[1].split("</"+parametro+">");
                String valor = split2[0];
                if (!UteisValidacao.emptyString(valor)) {
                    return valor;
                }
            } catch (Exception ignored) {
            }
        }
        return "";
    }
}

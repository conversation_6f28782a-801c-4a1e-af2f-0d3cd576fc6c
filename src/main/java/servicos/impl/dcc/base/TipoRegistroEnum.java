/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

/**
 * <AUTHOR>
 */
public enum TipoRegistroEnum {

    HEADER(1, "HEADER"),
    DETALHE(2, "DE<PERSON>LH<PERSON>"),
    <PERSON><PERSON><PERSON><PERSON>(3, "TRAILER"),
    //pro layout do itau
    HEADER_ARQUIVO(4, "HEADERARQUIVO"),
    TRAILER_ARQUIVO(5, "TRAILERARQUIVO"),
    //Layout FEBRABAN
    DETALHE_REGISTRO_B(6, "REGISTRO_B"),
    DETALHE_REGISTRO_J(7, "REGISTRO_J"),

    DETALHE_REGISTRO_P(8, "REGISTRO_P"),
    DETALHE_REGISTRO_Q(9, "REGISTRO_Q"),

    DETALHE_REGISTRO_T(10, "REGISTRO_T"),
    DETALHE_REGISTRO_U(11, "<PERSON>EGISTRO_U"),
    DETALHE_REGISTRO_R(12, "REGISTRO_R"),
    DETALHE_REGISTRO_S(13, "REGISTRO_S");

    private int id;
    private String descricao;

    TipoRegistroEnum(int id, String desc) {
        this.id = id;
        this.descricao = desc;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}

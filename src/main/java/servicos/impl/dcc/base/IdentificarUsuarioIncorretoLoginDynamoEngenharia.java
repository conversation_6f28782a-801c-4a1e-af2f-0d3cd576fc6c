package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class IdentificarUsuarioIncorretoLoginDynamoEngenharia extends SuperEntidade {

    public IdentificarUsuarioIncorretoLoginDynamoEngenharia() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            Connection con = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
            String chave = "704ee44b0ad0da5b7c1e195fd0c6dc32";

            ResultSet rs = SuperEntidade.criarConsulta("select \n" +
                    "u.codigo as usuariozw,\n" +
                    "u.nome,\n" +
                    "u.usuariogeral,\n" +
                    "(select usuariotw from usuariomovel  where  usuariozw  = u.codigo) as usuariotw\n" +
                    "from usuario u \n" +
                    "where length(coalesce(u.usuariogeral,'')) > 0", con);

            Integer atual = 0;
            while (rs.next()) {

                Integer codigoZw = rs.getInt("usuariozw");
                Integer usuarioTw = rs.getInt("usuariotw");
                String usuarioGeral = rs.getString("usuariogeral");
                String nome = rs.getString("nome");

                String urlLoginV2 = "https://app.pactosolucoes.com.br/login/prest/login/v2";
                Map<String, String> params = new HashMap<>();
                params.put("ug", usuarioGeral);

                RequestHttpService service = new RequestHttpService();
                RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlLoginV2+"/empresas", null, params, null, MetodoHttpEnum.POST);

                JSONArray array = new JSONObject(respostaHttpDTO.getResponse()).getJSONArray("content");
                for (int i = 0; i < array.length(); i++) {
                    try {
                        JSONObject obj = array.getJSONObject(i);
                        if (obj.optString("key").equals(chave)) {
                            Integer usuarioDynamo = obj.optInt("usu");
                            if (!usuarioDynamo.equals(codigoZw)) {
                                System.out.println(++atual + " - Usuário Incorreto! -->> " + nome + " | UsuarioGeral " + usuarioGeral + " | Chave " + chave + " | UsuarioDynamo " + usuarioDynamo + " | UsuarioZW " + codigoZw);
                                System.out.println(" - Vou tentar corrigir...");
                                System.out.println(" - Chamando endpoint -->> /atualizarcodigos");

                                Map<String, String> params2 = new HashMap<>();
                                params2.put("usuarioGeral", usuarioGeral);
                                params2.put("key", chave);
                                params2.put("usuarioZw", codigoZw.toString());
                                params2.put("usuarioTw", usuarioTw.toString());

                                Map<String, String> header = new HashMap<>();
                                header.put("Authorization", "Basic cGFjdG9AcGFjdG9hcHA6cGFjdG9AMjAyMg==");
                                RespostaHttpDTO respostaHttpDTO2 = service.executeRequest(urlLoginV2 + "/usuario/atualizarcodigos", header, params2, null, MetodoHttpEnum.POST);
                                if (respostaHttpDTO2.getHttpStatus() == 200) {
                                    System.out.println(" - Corrigido com sucesso.");
                                } else {
                                    System.out.println(" - Ops, não deu certo. Next.");
                                }
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(IdentificarUsuarioIncorretoLoginDynamoEngenharia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}

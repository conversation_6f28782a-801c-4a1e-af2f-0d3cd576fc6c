package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RodarScriptsTodosBancos extends SuperEntidade {

    private static Integer qtdAjustado = 0;
    private static boolean simular = true;

    public RodarScriptsTodosBancos() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");

            //id rede empresa engenharia 467
            //id rede empresa pratique 136

            Integer idRedeEmpresa = 136;
            simular = true;

            processarZW(idRedeEmpresa, conOAMD); //banco zw
//            processarTreino(idRedeEmpresa, conOAMD); //banco treino
        } catch (Exception ex) {
            Logger.getLogger(RodarScriptsTodosBancos.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    private static void processarTreino(Integer idRedeEmpresa, Connection conOAMD) throws Exception {
        String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                " \"nomeBD\" as banco, porta, urlintegracaows " +
                "from empresa " +
                "where chave in (select distinct chavezw from empresafinanceiro " +
                "where redeempresa_id = " + idRedeEmpresa + " and length(coalesce(chavezw ,'')) > 0)";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
        while (rs.next()) {
            String chave = rs.getString("chave");
            String host = rs.getString("host");
            Integer porta = rs.getInt("porta");
            String banco = rs.getString("banco").replace("bdzillyon", "bdmusc");
            String user = rs.getString("user");
            String pass = rs.getString("pass");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                try {
//                    Uteis.logarDebug("Rodar Script: " + chave + " - " + banco);
                    adicionarPermissaoTreino(chave, banco, 29, con);
                } catch (Exception ex) {
                    Uteis.logarDebug("ERRO Script: " + chave + " - " + banco + " | " + ex.getMessage());
                }
            } catch (Exception ex) {
                Uteis.logarDebug("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
            }
        }
        Uteis.logarDebug("qtdAjustado: " + qtdAjustado);
    }

    private static void processarZW(Integer idRedeEmpresa, Connection conOAMD) throws Exception {
        String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                " \"nomeBD\" as banco, porta, urlintegracaows " +
                "from empresa " +
                "where ativa " +
                (UteisValidacao.emptyNumber(idRedeEmpresa) ? "" :
                        ("and chave in (select distinct chavezw from empresafinanceiro where redeempresa_id = " + idRedeEmpresa + " and length(coalesce(chavezw ,'')) > 0)"));
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
        while (rs.next()) {
            String chave = rs.getString("chave");
            String host = rs.getString("host");
            Integer porta = rs.getInt("porta");
            String banco = rs.getString("banco");
            String user = rs.getString("user");
            String pass = rs.getString("pass");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                try {
                    Uteis.logarDebug("Rodar Script: " + chave + " - " + banco);
//                    executarProcessoHistoricoVinculo(con);
                } catch (Exception ex) {
                    Uteis.logarDebug("ERRO Script: " + chave + " - " + banco + " | " + ex.getMessage());
                }
            } catch (Exception ex) {
                Uteis.logarDebug("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
            }
        }
        Uteis.logarDebug("qtdAjustado: " + qtdAjustado);
    }

    private static void adicionarPermissaoTreino(String chave, String banco, Integer recurso, Connection con) throws Exception {
        try {
            if (!simular) {
                con.setAutoCommit(false);
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from perfil WHERE nome NOT ILIKE 'PROFESSOR' and codigo not in (select perfil_codigo from permissao where recurso = " + recurso + ")", con);
            while (rs.next()) {
                String nomePerfil = rs.getString("nome");
                Integer codigoPerfil = rs.getInt("codigo");

                Uteis.logarDebug("Rodar Script: " + chave + " - " + banco + " | Adicionando para perfil: " + codigoPerfil + " - " + nomePerfil);

                if (!simular) {
                    String sql1 = "insert into permissao(codigo, recurso, perfil_codigo) values ((select max(codigo) + 1 from permissao) ," + recurso + "," + codigoPerfil + ") RETURNING codigo";
                    PreparedStatement sqlInserir1 = con.prepareStatement(sql1);
                    ResultSet rsNovo = sqlInserir1.executeQuery();
                    rsNovo.next();
                    Integer codigoPermissao = rsNovo.getInt(1);

                    String sql2 = "insert into permissao_tipopermissoes(permissao_codigo, tipopermissoes) values (" + codigoPermissao + ",5)";
                    SuperFacadeJDBC.executarUpdate(sql2, con);
                }

                ++qtdAjustado;
            }

            if (!simular) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("##### Erro chave: " + chave + " | Erro: " + ex.getMessage());
            if (!simular) {
                con.rollback();
            }
        } finally {
            if (!simular) {
                con.setAutoCommit(true);
            }
        }
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.dcc.getnet.LayoutRemessaGetNetDCO;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 31/03/2020
 */
public class AgruparRemessasGetnet extends SuperEntidade {

    private String key;
    private List<EmpresaVO> listaEmpresas;
    public Integer[] CONVENIOS_DCC_EDI = new Integer[]{TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo()};

    public static void main(String... args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);

            AgruparRemessasGetnet service = new AgruparRemessasGetnet();
            service.setKey(chave);
            service.processar();
            service = null;
        } catch (Exception ex) {
            Logger.getLogger(AgruparRemessasGetnet.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ignored){
                }
            }
        }
    }

    public AgruparRemessasGetnet() throws Exception {
        this.listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public void processar() throws Exception {
        recursoHabilitado();

        Uteis.logar(null, "### Iniciando Agrupar Remessas Getnet");
        for (EmpresaVO empresa : listaEmpresas) {
            List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS, CONVENIOS_DCC_EDI, SituacaoConvenioCobranca.ATIVO);
            for (ConvenioCobrancaVO conv : convenios) {
                try {
                    conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(conv.getCodigo(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                    agruparRemessasGeradas(conv, empresa);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "### Agrupar Remessas: " + conv.getDescricao() + " | Erro: " + ex.getMessage());
                }
            }
        }
        Uteis.logar(null, "### Fim Agrupar Remessas Getnet");
    }

    public void agruparRemessasGeradas(ConvenioCobrancaVO conv, EmpresaVO empresaVO) throws Exception {
        Remessa remessaDAO = null;
        RemessaItem remessaItemDAO = null;
        try {

            if (!conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                String msg = "### Convênio não é do tipo Getnet! Convênio: " + conv.getDescricao();
                Uteis.logar(null, msg);
                return;
            }

            recursoHabilitado();

            Uteis.logar(null, "### Agrupar remessas | Convênio: " + conv.getDescricao());

            remessaDAO = new Remessa(this.con);
            List<RemessaVO> listaRemessas = remessaDAO.consultarPorSituacao(SituacaoRemessaEnum.GERADA, empresaVO.getCodigo(), conv.getCodigo());

            if (UteisValidacao.emptyList(listaRemessas)) {
                Uteis.logar(null, "### Agrupar remessas | Sem remessas para agrupar...");
                return;
            }
            if (listaRemessas.size() == 1) {
                Uteis.logar(null, "### Agrupar remessas | Somente uma remessa vou ignorar...");
                return;
            }

            Uteis.logar(null, "### Agrupar remessas | Encontrei " + listaRemessas.size() + " remessas para agrupar...");

            Integer sequencialUtilizar = null;
            RemessaVO remessaUnica = null;
            for (RemessaVO remessaVO : listaRemessas) {
                if (remessaVO.isCancelamento()) {
                    //ignorar remessas de cancelamento
                    continue;
                }

                Integer sequencialRemessa = Integer.parseInt(remessaVO.getSequencialArquivoExibicao());
                if (UteisValidacao.emptyNumber(sequencialRemessa)) {
                    throw new Exception("Remessa sem sequencial " + remessaVO.getCodigo());
                }

                if (sequencialUtilizar == null || sequencialRemessa < sequencialUtilizar) {
                    sequencialUtilizar = sequencialRemessa;
                    remessaUnica = remessaVO;
                }

                if ((remessaUnica.isNovoFormato() && !remessaVO.isNovoFormato()) ||
                        (!remessaUnica.isNovoFormato() && remessaVO.isNovoFormato())) {
                    throw new Exception("As remessas devem estar do mesmo formato de geração");
                }
            }


            if (remessaUnica == null || UteisValidacao.emptyNumber(remessaUnica.getCodigo())) {
                throw new Exception("Remessa unica não encontrada.");
            }


            //verificar se o sequencial está de acordo com a ultima remessa com retorno processado
            Integer[] situacaoFiltrar = new Integer[]{SituacaoRemessaEnum.RETORNO_PROCESSADO.getId(), SituacaoRemessaEnum.REMESSA_ENVIADA.getId()};
            RemessaVO ultimaRemessaProcessada = remessaDAO.obterUltimaRemessaPorSituacao(situacaoFiltrar, empresaVO.getCodigo(), false, conv.getCodigo());
            if (ultimaRemessaProcessada != null && !UteisValidacao.emptyNumber(ultimaRemessaProcessada.getCodigo())) {
                Integer ultimoSequencialProcessado = Integer.parseInt(ultimaRemessaProcessada.getSequencialArquivoExibicao());
                if (!sequencialUtilizar.equals(ultimoSequencialProcessado + 1)) {
                    sequencialUtilizar = (ultimoSequencialProcessado + 1);
                }
            }

            if (UteisValidacao.emptyNumber(sequencialUtilizar)) {
                throw new Exception("Erro não foi identificado sequencial para utilizar...");
            }

            Uteis.logar(null, "### Agrupar remessas | Vou utilizar o sequencial " + sequencialUtilizar);

            remessaDAO = null;
            try {
                con.setAutoCommit(false);

                remessaDAO = new Remessa(this.con);
                remessaItemDAO = new RemessaItem(this.con);

                for (RemessaVO remessaVO : listaRemessas) {

                    if (remessaVO.getCodigo().equals(remessaUnica.getCodigo()) || remessaVO.isCancelamento()) {
                        //ignorar remessas de cancelamento
                        continue;
                    }

                    Uteis.logar(null, "### Agrupar remessas | Vou passar os itens da remessa " + remessaVO.getCodigo() + " para a remessa " + remessaUnica.getCodigo());

                    String update = "update remessaitem set remessa = " + remessaUnica.getCodigo() + " where remessa = " + remessaVO.getCodigo();
                    SuperFacadeJDBC.executarConsultaUpdate(update, this.con);

                    Uteis.logar(null, "### Agrupar remessas | Vou deletar a remessa " + remessaVO.getCodigo());

                    String delete = "delete from remessa where codigo = " + remessaVO.getCodigo();
                    SuperFacadeJDBC.executarConsultaUpdate(delete, this.con);
                }

                Uteis.logar(null, "### Agrupar remessas | Vou ajustar header e trailer da remessa única... " + remessaUnica.getCodigo());

                //alterar a data de geração da remessa
                remessaUnica.setDataRegistro(Calendario.hoje());
                remessaUnica.setDataEnvio(null);

                //ajustar o sequencial da remessa
                String sequencialGravar = StringUtilities.formatarCampo(new BigDecimal(sequencialUtilizar), 9);
                remessaUnica.getProps().put(DCCAttEnum.SequencialRegistro.name(), sequencialGravar);

                RegistroRemessa head = LayoutRemessaGetNetDCO.obterRegistroRemessaHead(remessaUnica, sequencialUtilizar);
                remessaUnica.setHead(new StringBuilder(head.toString()));

                //ajustar o nome do arquivo e identificador
                remessaUnica.setIdentificador(remessaUnica.getTipo().getIdent(sequencialUtilizar, StringUtilities.formatarCampoForcandoZerosAEsquerda(conv.getNumeroContrato(), 10), remessaUnica.getDataRegistro()));
                remessaUnica.setNomeArquivo(remessaUnica.getTipo().getNomeArquivo(sequencialUtilizar, StringUtilities.formatarCampoForcandoZerosAEsquerda(conv.getNumeroContrato(), 10), remessaUnica.getDataRegistro()));
                remessaUnica.setIdentificador(remessaUnica.getIdentificador().replace("XXXX", StringUtilities.formatarCampoForcandoZerosAEsquerda(sequencialUtilizar, 9)));
                remessaUnica.setNomeArquivo(remessaUnica.getIdentificador());

                //ajustar a soma e o total de itens da remessa
                Integer qtdItensTotal = remessaItemDAO.consultarQtdPorCodigoRemessa(remessaUnica.getCodigo());
                Double valorTotal = remessaItemDAO.consultarPorCodigoValorRemessa(remessaUnica.getCodigo());

                RegistroRemessa trailer = LayoutRemessaGetNetDCO.obterRegistroRemessaTrailer(qtdItensTotal, valorTotal);
                remessaUnica.setTrailer(new StringBuilder(trailer.toString()));

                remessaDAO.alterar(remessaUnica);


                // ajustar sequencial do convenio
                Integer sequencialConvenio = (sequencialUtilizar + 1);

                Uteis.logar(null, "### Agrupar remessas | Vou ajustar o sequencial do convênio de cobrança " + conv.getCodigo() + " - Sequencial " + sequencialConvenio);

                String ajusteSequencial = "update conveniocobranca set sequencialdoarquivo = " + sequencialConvenio + " where codigo = " + conv.getCodigo();
                SuperFacadeJDBC.executarConsultaUpdate(ajusteSequencial, this.con);

                con.commit();
            } catch (Exception ex) {
                ex.printStackTrace();
                con.rollback();
                con.setAutoCommit(true);
                throw ex;
            } finally {
                con.setAutoCommit(true);
            }

        } finally {
            remessaDAO = null;
            remessaItemDAO = null;
        }
    }

    private void recursoHabilitado() throws Exception {
        ConfiguracaoSistema configDAO = new ConfiguracaoSistema(con);
        boolean habilitado = configDAO.isAgruparRemessasGetnet();
        configDAO = null;

        if (!habilitado) {
            throw new Exception("Recurso não habilitado! AgruparRemessasGetnet em ConfiguracaoSistema");
        }
    }
}

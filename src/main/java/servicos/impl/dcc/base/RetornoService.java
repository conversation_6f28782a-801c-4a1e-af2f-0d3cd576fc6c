/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.reflexao.Propriedade;
import org.apache.commons.collections.Predicate;
import servicos.impl.dcc.rede.TipoArquivoRedeEnum;
import servicos.propriedades.PropsService;
import servicos.util.SFTP;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static servicos.impl.dcc.base.RemessaService.sleepGetnet;

/**
 * <AUTHOR>
 */
public class RetornoService {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyMMdd");

    public static List<Map<String, File>> receberRetornos(ConvenioCobrancaVO convenio, boolean receberExtratos,
                                                          Date dia, String arquivoRejeitado) throws Exception {

        int nrTentativas = 1;
        if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            nrTentativas = 3;
        }

        String msgErro = "";
        while (nrTentativas > 0) {
            try {
                /**
                 * Criado um sleep para conexao com a Getnet.
                 * Devido a várias tentativas ao mesmo tempo está ocorrendo problemas de bloqueio e envio de arquivos.
                 * by Luiz Felipe 16/04/2020
                 */
                if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                    Uteis.logar(null, "# receberRetornos - Sleep Getnet: " + sleepGetnet + " | " + convenio.getDescricao());
                    Thread.sleep(sleepGetnet);
                }

                return receberRetornosGeral(convenio, receberExtratos, dia, arquivoRejeitado);
            } catch (Exception ex) {
                ex.printStackTrace();
                msgErro = ex.getMessage();
                Uteis.logar(null, "### Forçar obter arquivos remoto: " + convenio.getDescricao() + " | Erro: " + msgErro);
                nrTentativas--;
            }
        }
        throw new Exception("Erro ao obter arquivos remoto! Erro: " + msgErro);
    }

    private static List<Map<String, File>> receberRetornosGeral(ConvenioCobrancaVO convenio, boolean receberExtratos,
                                                                Date dia, String arquivoRejeitado) throws Exception {

        if (dia == null) {
            dia = Calendario.hoje();
        }

        Date dataInicio = Uteis.somarDias(dia, -7);
        String pathDestino = convenio.getDiretorioLocalTIVIT() + convenio.getDiretorioLocalDownloadTIVIT();

        if (receberExtratos) {
            pathDestino += Calendario.getDataAplicandoFormatacao(dia, "dd-MM-yyyy");
        } else {
            if (dia.getHours() < 5) {
                pathDestino += Calendario.getDataAplicandoFormatacao(Uteis.obterDataAnterior(dia, 1), "dd-MM-yyyy");
            } else {
                pathDestino += Calendario.getDataAplicandoFormatacao(dia, "dd-MM-yyyy");
            }
        }

        String PREFIXO_MSG = "";
        String PREFIXO_ARQUIVOS = null;
        String prefixoExtratoAdicional = null;
        if (receberExtratos) {
            pathDestino += "_ED";
            PREFIXO_MSG = "EXTRATO - ";
            PREFIXO_ARQUIVOS = convenio.getNumeroContrato();

            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                PREFIXO_ARQUIVOS = Uteis.removerMascara(convenio.getEmpresa().getCNPJ());
            }

            //se não for extrato getnet deve contem no arquivo a palavra "extrato"
            //evitar de pegar arquivos de retorno de remessa EDI
            //by Luiz Felipe 01/04/2020
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                    convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                prefixoExtratoAdicional = "extrato";
            }

        }
        //verifica se nestes tipo de convenio o codigo do estabelecimento está presente no nome do arquivo de retorno
        if (convenio.getTipo() == TipoConvenioCobrancaEnum.DCC_BIN || convenio.getTipo() == TipoConvenioCobrancaEnum.DCC_GETNET) {
            PREFIXO_ARQUIVOS = convenio.getNumeroContrato();
        }
        Uteis.logar(null, String.format(PREFIXO_MSG + "Iniciando Servico Receber Retornos DEFAULT para o CONVENIO: %s ... ", convenio.getDescricao()));

        File pDestino = new File(pathDestino);
        if (!pDestino.exists()) {
            Uteis.logar(null, PREFIXO_MSG + "ReceberRetornos -> Criar diretório  (forçado): " + pathDestino);
            FileUtilities.forceDirectory(pathDestino);
        }


        /**
         * Configuração para buscar os arquivos remoto.
         * Caso esteja desmarcado entende-se que os arquivos já estão na pasta local "pathDestino".
         * by Luiz Felipe 03/07/2020
         */
        boolean buscarRemotoExtratoRetornoRemessa = Uteis.isBuscarRemotoExtratoRetornoRemessa();
        if (!buscarRemotoExtratoRetornoRemessa) {
            Uteis.logarDebug("#### Configuração \"BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA\" - NÃO ESTÁ ATIVADA!");
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                Uteis.logarDebug("#### Considerar configuração \"BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA\" como TRUE | Convênio é GETNET...");
                buscarRemotoExtratoRetornoRemessa = true;
            } else {
                Uteis.logarDebug("#### Não será obtido os arquivos remotamente (\"sftp.getFiles\" - Não será executado) ");
            }
        }


        /**
         * Fluxos Básicos:
         *
         * 1. Caixas Postais compartilhadas como TIVIT (DCC Cielo) e BIN podemos
         * ter um servidor central da Pacto que obtém os dados através do método
         * 'receberRetornosCentralizados' vide caixas postais em
         * SuperControle.properties.arrayCaixasPostaisSFTP;
         *      a. Neste caso neste servidor centralizado pode haver os arquivos
         *         de retorno que serão utilizados pelas empresas que rodam nele;
         *
         *      b. Outras empresas fora deste servidor, usarão a configuração
         *         SuperControle.properties.serverSFTPSlave para obter os arquivos
         *         de cada Tipo de Convênio que são compartilhados;
         *
         * 2. Caixas postais que não se enquadrem no item 1, como GETNET por exemplo,
         * deve seguir o fluxo de efetuar o download do arquivo da forma tradicional,
         * ou seja, cada empresa em cada convênio deverá fazer o download de seus arquivos.
         */
        CaixaPostalSFTP cpp = null;
        String nomeCaixaLog = PREFIXO_MSG + "CaixaPostalDefault";
        boolean verificarDiferencaArquivos = false;
        if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && !convenio.isBuscarCentralPacto())) {//Caso 2
            cpp = new CaixaPostalSFTP(convenio.getHostSFTP(),
                    Integer.valueOf(convenio.getPortSFTP()), convenio.getUserSFTP(),
                    convenio.getPwdSFTP(), convenio.getDiretorioRemotoTIVIT_OUT());
        } else if ((convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && convenio.isBuscarCentralPacto()) || RetornoService.obterCaixaPostalCentralPacto(pathDestino, convenio.getTipo()) != null) {//Caso 1.b
            cpp = RetornoService.obterCaixaPostalCentralPacto(pathDestino, convenio.getTipo());
            nomeCaixaLog = PREFIXO_MSG + "CaixaPostalCentralPacto";
            verificarDiferencaArquivos = true;
        } else if (UteisValidacao.emptyList(RetornoService.obterListaCaixasPostaisGlobais())) {//Caso 1.a
            cpp = new CaixaPostalSFTP(convenio.getHostSFTP(),
                    Integer.valueOf(convenio.getPortSFTP()), convenio.getUserSFTP(),
                    convenio.getPwdSFTP(), convenio.getDiretorioRemotoTIVIT_OUT());
        }

        if (cpp != null) {
            SFTP sftp = new SFTP(cpp.getHost(), cpp.getUser(), cpp.getPwd(), cpp.getPorta());
            try {
                Uteis.logar(null, nomeCaixaLog + " -> Convênio: " + convenio.getDescricao()
                        + " -> Vou copiar arquivos via SFTP de: " + cpp);
                if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                    if (convenio.isAdicionarData()) {
                        cpp.setDiretorioRemoto(convenio.getDiretorioRemotoTIVIT_OUT() + "/" + Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
                    } else {
                        cpp.setDiretorioRemoto(convenio.getDiretorioRemotoTIVIT_OUT());
                    }

                    if (buscarRemotoExtratoRetornoRemessa) {
                        for (TipoArquivoRedeEnum tipoArquivo : TipoArquivoRedeEnum.values()) {
                            String nomeArquivo = PREFIXO_ARQUIVOS + "_" + tipoArquivo.getId().toLowerCase() + "_" + Uteis.getDataAplicandoFormatacao(dia, convenio.getMascaraDataArquivo());
                            Uteis.logarDebug("#### Obter arquivos... | " + nomeArquivo);
                            sftp.getFiles(cpp.getDiretorioRemoto(), pathDestino,
                                    nomeArquivo, dataInicio, convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET), verificarDiferencaArquivos);
                        }
                    } else {
                        Uteis.logarDebug("#### Ignorar \"sftp.getFiles\"...");
                    }
                } else {

                    boolean deleteArquivosAntigos = convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET);

                    if (receberExtratos && !UteisValidacao.emptyString(convenio.getDiretorioRemotoExtrato())) {
                        //extrato getnet não deletar os arquivos!
                        deleteArquivosAntigos = false;

                        if (convenio.isAdicionarData()) {
                            cpp.setDiretorioRemoto(convenio.getDiretorioRemotoExtrato() + "/" + Uteis.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
                        } else {
                            cpp.setDiretorioRemoto(convenio.getDiretorioRemotoExtrato());
                        }
                    }


                    //buscar somente arquivos rejeitados.
                    //by Luiz Felipe
                    if (!UteisValidacao.emptyString(arquivoRejeitado)) {
                        deleteArquivosAntigos = false;
                        verificarDiferencaArquivos = false;

                        pathDestino += "_REJ";
                        pDestino = new File(pathDestino);
                        if (!pDestino.exists()) {
                            Uteis.logar(null, "ReceberRetornos Arquivos Rejeitados -> Criar diretório (forçado): " + pathDestino);
                            FileUtilities.forceDirectory(pathDestino);
                        }
                    }

                    if (buscarRemotoExtratoRetornoRemessa) {
                        Uteis.logarDebug("#### Obter arquivos... | " + PREFIXO_ARQUIVOS);
                        sftp.getFiles(cpp.getDiretorioRemoto(), pathDestino, PREFIXO_ARQUIVOS, dataInicio, deleteArquivosAntigos, verificarDiferencaArquivos, arquivoRejeitado, prefixoExtratoAdicional);
                    } else {
                        Uteis.logarDebug("#### Ignorar \"sftp.getFiles\"...");
                    }

                }
                Uteis.logar(null, nomeCaixaLog + " -> Convênio: " + convenio.getDescricao()
                        + " - Arquivos copiados com sucesso! ");
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logar(null, nomeCaixaLog + " | receberRetornosGeral -> Erro ao obter arquivos remotos:  "
                        + e.getMessage() + " - convênio: " + convenio.getDescricao()
                        + " - DEBUG CaixaPostalSFTP: " + cpp);

                if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                        && (e.getMessage().toLowerCase().contains("connection reset")
                        || e.getMessage().toLowerCase().contains("connection is closed by foreign host"))) {

                    //forçar lançar a exceção para que o processo chame o processo novamente.
                    //by Luiz Felipe
                    Uteis.logar(null, "# Falha de conexão - Getnet " + convenio.getDescricao());
                    throw e;
                }
            }
        } else if (UteisValidacao.emptyList(RetornoService.obterListaCaixasPostaisGlobais())) {
            Uteis.logar(null, nomeCaixaLog + " -> ATENÇÃO!!! Não foi possível determinar "
                    + "qual SFTP seria utilizado para Download dos Arquivos de Retorno. "
                    + "Verifique sua configuração! Pode ser que a Aplicação não esteja "
                    + "configurada nem para Obter da Caixa Postal Central da Pacto "
                    + "e nem para obter os arquivos individualmente! "
                    + "Convênio: " + convenio.getDescricao());
        }

        Uteis.logarDebug("#### Ler os arquivos do diretório local -->> " + pathDestino);
        return FileUtilities.readListFilesDirectory(pathDestino);
    }

    public static List<Map<String, File>> receberRetornosDCO(ConvenioCobrancaVO convenio) throws Exception {

        Date dataInicio = Uteis.somarDias(Calendario.hoje(), -7);
        String diretorioRemoto = convenio.getDiretorioRemotoTIVIT_OUT();
        String pathDestino = convenio.getDiretorioLocalTIVIT() +
                convenio.getDiretorioLocalDownloadTIVIT() + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy");


        Uteis.logar(null, String.format("Iniciando Servico Receber Retornos DCO - CONVENIO: %s ... ", convenio.getDescricao()));

        File pDestino = new File(pathDestino);
        if (!pDestino.exists()) {
            Uteis.logar(null, "ReceberRetornosDCO -> Criar diretório  (forçado): " + pathDestino);
            FileUtilities.forceDirectory(pathDestino);
        }

        CaixaPostalSFTP cpp = new CaixaPostalSFTP(convenio.getHostSFTP(),
                new Integer(convenio.getPortSFTP()), convenio.getUserSFTP(),
                convenio.getPwdSFTP(), pathDestino);

        SFTP sftp = new SFTP(cpp.getHost(), cpp.getUser(), cpp.getPwd(), cpp.getPorta());
        try {
            Uteis.logar(null, "---> Convênio: " + convenio.getDescricao() + " -> Vou copiar arquivos via SFTP de: " + cpp);

            sftp.getFiles(diretorioRemoto, pathDestino, "", dataInicio, false, false);

            //TEMPORÁRIO: Criar configuração de mascara de sub-diretório
            Uteis.logar(null, "---> Convênio NOMENCLATURA: " + convenio.getDescricao() + " -> " + convenio.getNomenclaturaArquivo());
            if (convenio.getTipo().toString().contains("DCO") && diretorioRemoto.toLowerCase().contains("nexxera")) {
                diretorioRemoto += String.format("/%s", SDF.format(Calendario.hoje()));
                Uteis.logar(null, "---> Convênio: " + convenio.getDescricao() + " -> Vou copiar arquivos via SFTP de: " + cpp + " para de diretorio remoto alternativo: " + diretorioRemoto);
                sftp.getFiles(diretorioRemoto, pathDestino, "", dataInicio, false, false);
            }
            Uteis.logar(null, "---> Convênio: " + convenio.getDescricao() + " - Arquivos copiados com sucesso! ");
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, " | receberRetornosDCO -> Erro ao obter arquivos remotos:  " + e.getMessage() + " - convênio: " + convenio.getDescricao() + " - DEBUG CaixaPostalSFTP: " + cpp);
        }

        Uteis.logarDebug("#### Ler os arquivos do diretório local -->> " + pathDestino);
        return FileUtilities.readListFilesDirectory(pathDestino);
    }

    /**
     * Obtem Lista das Caixas Postais do arquivo SuperControle.properties,
     * que serao lidas e efetuado download pelo servico RetornoService, no servidor responsavel pelo Download
     * e distribuicao dos arquivos de retorno dos tipo de Remessa configurados no .properties (ex.: DCC, DCC_BIN)
     *
     * @return
     */
    public static List<CaixaPostalSFTP> obterListaCaixasPostaisGlobais() {
        return obterListaCaixasPostaisGlobais(PropsService.arrayCaixasPostaisSFTP);
    }

    public static List<CaixaPostalSFTP> obterListaCaixasPostaisGlobais(String propsService) {
        final String arrayCaixasPostais = PropsService.getPropertyValue(propsService);
        List<CaixaPostalSFTP> lista = new ArrayList<CaixaPostalSFTP>();
        for (StringTokenizer regToken = new StringTokenizer(arrayCaixasPostais, ","); regToken.hasMoreTokens(); ) {
            final String caixasPostais = regToken.nextToken();
            TipoConvenioCobrancaEnum tipoConvEnum = null;
            String host = null;
            String porta = null;
            String user = null;
            String senha = null;
            String caminhoRemoto = null;

            for (StringTokenizer caixa = new StringTokenizer(caixasPostais, ":"); caixa.hasMoreTokens(); ) {
                String tipoConvenio = caixa.nextToken();
                tipoConvEnum = TipoConvenioCobrancaEnum.valueOf(tipoConvenio);
                host = caixa.nextToken();
                porta = caixa.nextToken();
                user = caixa.nextToken();
                senha = caixa.nextToken();
                caminhoRemoto = caixa.nextToken();
            }
            if (host != null && porta != null && user != null && senha != null
                    && caminhoRemoto != null && tipoConvEnum != null) {
                lista.add(new CaixaPostalSFTP(tipoConvEnum, host, new Integer(porta), user, senha, caminhoRemoto));
            }
        }
        return lista;
    }

    /**
     * Obtem Caixa Postal Centralizada da Pacto onde estão todos os arquivos
     * de retorno de todos os tipos de Convenio (SuperControle.properties -> serverSFTPSlave),
     *
     * @return
     */
    public static CaixaPostalSFTP obterCaixaPostalCentralPacto(final String diretorioRemoto, TipoConvenioCobrancaEnum tipoConv) {
        final String serverSFTPSlave = PropsService.getPropertyValue(PropsService.serverSFTPSlave);
        boolean tipoConvenioValido = false;
        for (StringTokenizer c = new StringTokenizer(serverSFTPSlave, ":"); c.hasMoreTokens(); ) {
            final String tiposConvenio = c.nextToken();
            if (tiposConvenio != null && !tiposConvenio.isEmpty()) {
                for (StringTokenizer tipo = new StringTokenizer(tiposConvenio, ","); tipo.hasMoreTokens(); ) {
                    final String t = tipo.nextToken();
                    if (t.equals(tipoConv.name())) {
                        tipoConvenioValido = true;
                        break;
                    }
                }
            }
            if (tipoConvenioValido) {
                final String host = c.nextToken();
                final String porta = c.nextToken();
                final String user = c.nextToken();
                final String senha = c.nextToken();

                if (host != null && porta != null && user != null && senha != null) {
                    return new CaixaPostalSFTP(host, new Integer(porta), user, senha, diretorioRemoto);
                }
            }
        }
        return null;
    }


    /**
     * Verifica se esse 'tipo' de convenio de cobranca pode ser baixado de forma individual,
     * haja visto quando temos um servidor que centraliza a distribuicao dos arquivos de retorno
     * alguns tipos de convenio ainda podem ser processados de forma individual,
     * mas nao podem interferir nos outros tipos. Por exemplo: DCC_GETNET individual,
     * ja DCC e DCC_BIN podem ser obtidos do SFTP coletivamente em uma unica conexao
     */
    public static boolean exigeDownloadRetornoIndividual(final TipoConvenioCobrancaEnum tipo) {
        List<CaixaPostalSFTP> lista = obterListaCaixasPostaisGlobais();
        if (lista.isEmpty())
            return false;
        return !ColecaoUtils.exists(lista, new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                return ((CaixaPostalSFTP) o).getTipo().equals(tipo);
            }
        });
    }

    public void receberRetornosCentralizados() throws Exception {
        List<CaixaPostalSFTP> listaCaixasSFTP = obterListaCaixasPostaisGlobais();
        for (CaixaPostalSFTP c : listaCaixasSFTP) {
            Uteis.logar(null, String.format(
                    "ReceberRetornosCentralizados() -> Obter arquivos do Tipo de convenio: %s - Atraves do HOST: %s PORTA: %s USER: %s SENHA: %s DIR_REMOTO: %s ",
                    c.getTipo(), c.getHost(), c.getPorta(), c.getUser(), c.getPwd(), c.getDiretorioRemoto()));

            String diretorioDestino = String.format("%s%s%s",
                    (String) c.getTipo().getPropriedadePadrao(Propriedade.CONVENIO_DIRETORIO_LOCAL).getValorPadrao(),
                    (String) c.getTipo().getPropriedadePadrao(Propriedade.CONVENIO_DIRETORIO_LOCAL_DOWNLOAD).getValorPadrao(),
                    Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy"));

            SFTP sftp = new SFTP(c.getHost(), c.getUser(), c.getPwd(), c.getPorta());
            File pDestino = new File(diretorioDestino);
            if (!pDestino.exists()) {
                Uteis.logar(null, "ReceberRetornosCentralizados() -> Criar diretório  (forçado): " + diretorioDestino);
                FileUtilities.forceDirectory(diretorioDestino);
            }
            try {
                sftp.getFiles(c.getDiretorioRemoto(), diretorioDestino,
                        null, null, false, false);
                //BAIXAR ARQUIVOS DE EXTRATO SE EXISTE ESTA INTEGRAÇÃO PARA OS TIPOS ABAIXO
                if (c.getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {
                    diretorioDestino += "_ED";
                    FileUtilities.forceDirectory(diretorioDestino);
                    CaixaPostalSFTP caixaNew = obterDiretorioNovoCieloExtrato();
                    if (caixaNew != null) {
                        SFTP sftpExtratoCielo = new SFTP(caixaNew.getHost(), caixaNew.getUser(), caixaNew.getPwd(), caixaNew.getPorta());
                        sftpExtratoCielo.getFiles(caixaNew.getDiretorioRemoto(), diretorioDestino,
                                "_CIELO", Calendario.getDataComHoraZerada(Calendario.hoje()), true, false);
                    } else {
                        new Exception("Caixa postal de extratos da cielo não encontrada.");
                    }
                }
                if (c.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                    diretorioDestino += "_ED";
                    FileUtilities.forceDirectory(diretorioDestino);
                    sftp.getFiles(c.getDiretorioRemoto(), diretorioDestino,
                            "VENDAS", null, false, false);
                    sftp.getFiles(c.getDiretorioRemoto(), diretorioDestino,
                            "PGTOS", null, false, false);
                }
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logar(null, "ReceberRetornosCentralizados() -> Erro ao obter arquivos remotos:  " + e.getMessage());
            }
        }

    }

    private CaixaPostalSFTP obterDiretorioNovoCieloExtrato() {
        if (!obterListaCaixasPostaisGlobais(PropsService.arrayCaixasPostaisExtratoSFTP).isEmpty()) {
            return obterListaCaixasPostaisGlobais(PropsService.arrayCaixasPostaisExtratoSFTP).get(0);
        } else {
            return null;
        }
    }

    public static List<Map<String, File>> receberArquivosExtrato(ConvenioCobrancaVO convenio,
                                                                 String chave, Date dia, Boolean arquivosLocais) throws Exception {
        if (convenio.isUtilizaExtrato()) {
            return receberExtratos(convenio, chave, dia, arquivosLocais);
        } else {
            return receberRetornos(convenio, true, dia, null);
        }
    }

    private static List<Map<String, File>> receberExtratos(ConvenioCobrancaVO convenioVO, String chave, Date dia, Boolean arquivosLocais) throws Exception {

        Uteis.logar(null, "Receber Extratos | INICIO.. | " + convenioVO.getDescricao());

        if (dia == null) {
            dia = Calendario.hoje();
        }

        Date dataInicio = Uteis.somarDias(dia, -7);

        String diretorioLocalSalvar = convenioVO.getDiretorioLocalExtrato_Preparado(dia);
        String diretorioBuscar = convenioVO.getDiretorioRemotoExtrato_Preparado(dia);
        String prefixoExtratoAdicional = null;

        File pDestino = new File(diretorioLocalSalvar);
        if (!pDestino.exists()) {
            FileUtilities.forceDirectory(diretorioLocalSalvar);
        }

        /**
         * Configuração para buscar os arquivos remoto.
         * Caso esteja desmarcado entende-se que os arquivos já estão na pasta local "pathDestino".
         * by Luiz Felipe 03/07/2020
         */
        boolean buscarRemotoExtratoRetornoRemessa = arquivosLocais ? false : Uteis.isBuscarRemotoExtratoRetornoRemessa();
        if (!buscarRemotoExtratoRetornoRemessa) {
            Uteis.logarDebug("#### Configuração \"BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA\" - NÃO ESTÁ ATIVADA!");
            if (convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                Uteis.logarDebug("#### Considerar configuração \"BUSCAR_REMOTO_EXTRATO_RETORNO_REMESSA\" como TRUE | Convênio é GETNET...");
                buscarRemotoExtratoRetornoRemessa = arquivosLocais ? false : true;
            } else {
                Uteis.logarDebug("#### Não será obtido os arquivos remotamente (\"sftp.getFiles\" - Não será executado) ");
            }
        }

        //se não for extrato getnet deve contem no arquivo a palavra "extrato"
        //evitar de pegar arquivos de retorno de remessa EDI
        //by Luiz Felipe 01/04/2020
        if (convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            prefixoExtratoAdicional = "extrato";
        }

        CaixaPostalSFTP cpp;
        if (convenioVO.isObterCentralPactoExtrato()) {
            cpp = RetornoService.obterCaixaPostalCentralPacto("", convenioVO.getTipo());
        } else {
            cpp = new CaixaPostalSFTP(convenioVO.getHostSFTPExtrato(), convenioVO.getPortSFTPExtrato(), convenioVO.getUserSFTPExtrato(),
                    convenioVO.getPwdSFTPExtrato(), "");
        }

        boolean deleteOldFiles = false;
        if (cpp != null) {
            SFTP sftp = new SFTP(cpp.getHost(), cpp.getUser(), cpp.getPwd(), cpp.getPorta());
            try {
                cpp.setDiretorioRemoto(diretorioBuscar);

                Uteis.logar(null, "Receber Extratos | Origem   | " + cpp.toString());
                Uteis.logar(null, "Receber Extratos | Destino  | " + diretorioLocalSalvar);

                if (convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                    deleteOldFiles = true;
                }

                if (buscarRemotoExtratoRetornoRemessa) {
                    if (convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                        for (TipoArquivoRedeEnum tipoArquivo : TipoArquivoRedeEnum.values()) {
                            String nomeArquivo = convenioVO.getNomenclaturaExtrato_Preenchido(chave, dia, tipoArquivo.getId());
                            Uteis.logar(null, "Receber Extratos | Arquivos | " + nomeArquivo);
                            sftp.getFiles(cpp.getDiretorioRemoto(), diretorioLocalSalvar, nomeArquivo, dataInicio, deleteOldFiles, false);
                        }
                    } else {
                        String nomeArquivo = convenioVO.getNomenclaturaExtrato_Preenchido(chave, dia, "");
                        Uteis.logar(null, "Receber Extratos | Arquivos | " + nomeArquivo);
                        sftp.getFiles(cpp.getDiretorioRemoto(), diretorioLocalSalvar, nomeArquivo, dataInicio, deleteOldFiles, false, null, prefixoExtratoAdicional);
                    }
                } else {
                    Uteis.logarDebug("#### Ignorar \"sftp.getFiles\"...");
                }

                Uteis.logar(null, "Receber Extratos | " + convenioVO.getDescricao() + " | Arquivos copiados com sucesso!");
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logar(null, "Erro Receber Extratos | " + convenioVO.getDescricao() + " | " + ex.getMessage());
            }
        }

        Uteis.logar(null, "Receber Extratos | FIM.. | " + convenioVO.getDescricao());

        Uteis.logarDebug("#### Ler os arquivos do diretório local -->> " + diretorioLocalSalvar);
        return FileUtilities.readListFilesDirectory(diretorioLocalSalvar);
    }

    public static void main(String... p) {
        try {
            Uteis.debug = true;
            RetornoService rs = new RetornoService();
            rs.receberRetornosCentralizados();
        } catch (Exception ex) {
            Logger.getLogger(RetornoService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

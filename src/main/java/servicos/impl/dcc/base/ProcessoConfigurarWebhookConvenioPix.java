package servicos.impl.dcc.base;

import br.com.pactosolucoes.atualizadb.processo.ProcessarMetasDoDia;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONObject;
import servicos.impl.pix.PixWebhookService;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoConfigurarWebhookConvenioPix extends SuperEntidade {

    private static StringBuilder logGravar;

    public ProcessoConfigurarWebhookConvenioPix() throws Exception {
    }

    public static void main(String... args) throws IOException {
        try {
            Uteis.debug = true;

            //Produção
//            Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");

            //Localhost
            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            JSONObject jsonObject = processarTodosOsBancos(conOAMD);

        } catch (Exception ex) {
            Logger.getLogger(ProcessoConfigurarWebhookConvenioPix.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.salvarArquivo(ProcessoConfigurarWebhookConvenioPix.class.getSimpleName() + "_" + "webhookPix" + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    public static JSONObject processarTodosOsBancos(Connection conOAMD) throws Exception {
        JSONObject json = new JSONObject();
        try {
            //TODO IFNROMAR O TIPO DE CONVÊNIO AQUI
            TipoConvenioCobrancaEnum tipoConvenio = TipoConvenioCobrancaEnum.PIX_SANTANDER;

            String sql = "select chave, \"hostBD\" as host, porta, \"nomeBD\" as nomeDB from empresa where ativa\n" +
                    "and chave in (select chavezw from empresafinanceiro where codigo in (select distinct empresafinanceiro_codigo from z_oamd_pactopay_convenio_cobranca where TIPO_CONVENIO_DESCRICAO ilike '%" + tipoConvenio.name() + "%'))";
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", conOAMD);
            adicionarLog("total empresas: " + total);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
            while (rs.next()) {
                String chave = rs.getString("chave");
                String host = rs.getString("host");
                Integer porta = rs.getInt("porta");
                String nomeDB = rs.getString("nomeDB");
                try {
                    processarConfiguracaoWebhook(chave, host, porta, nomeDB, tipoConvenio);
                } catch (Exception ex) {
//                    ex.printStackTrace();
                    adicionarLog("ERRO " + chave + " - " + ex.getMessage());
                }
//                try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
//                    JSONObject jsonEmp = processarUmaEmpresa(chave, con);
//                    array.put(jsonEmp);
//                } catch (Exception ex) {
//                    adicionarLog("ERRO " + banco + " - " + ex.getMessage());
//                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            json.put("erroGeral", ex.getMessage());
            throw ex;
        }
        return json;
    }

    private static void processarConfiguracaoWebhook(String chave, String host, Integer porta, String nomeDB, TipoConvenioCobrancaEnum tipoConvenio) throws Exception {
        try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + nomeDB, "postgres", "pactodb")) {
            JSONObject retorno = processarConvenioCobrancaPix(con, chave, tipoConvenio);
            adicionarLog("##############################################################");
            adicionarLog("Chave: " + chave);
            adicionarLog("NomeBD: " + nomeDB);
            adicionarLog(retorno.toString());
        } catch (Exception ex) {
            adicionarLog("##### ERRO CHAVE | " + chave + " | " +host+ "| " + ex.getMessage());
//            Logger.getLogger(ProcessoConfigurarWebhookConvenioPix.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static JSONObject processarConvenioCobrancaPix(Connection con, String chave, TipoConvenioCobrancaEnum tipoConvenio) {
        ConvenioCobranca convenioCobrancaDAO;
        Empresa empresaDAO;
        JSONObject json = new JSONObject();
        PixWebhookService pixWebhookService;
        try {
            empresaDAO = new Empresa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            pixWebhookService = new PixWebhookService(con);

            adicionarLog("Processando Chave: " + chave);

            json.put("chave", chave);

            List<EmpresaVO> empresaVOList = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (EmpresaVO empresaVO : empresaVOList) {
                try {
                    //**********DEFINIR O TIPO DO CONVÊNIO PIX A PROCESSAR*****************
                    Integer[] ARRAY = new Integer[]{tipoConvenio.getCodigo()};
                    //***************************

                    List<ConvenioCobrancaVO> listaConv = convenioCobrancaDAO.consultarPorEmpresaESituacao(empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                            ARRAY, SituacaoConvenioCobranca.ATIVO);
                    if (UteisValidacao.emptyList(listaConv)) {
                        throw new Exception("Nenhum convênio PIX encontrado");
                    }
                    for (ConvenioCobrancaVO convenioCobrancaVO : listaConv) {
                        try {
                            boolean sucesso = pixWebhookService.configurarUrlCallback(convenioCobrancaVO, chave);
                            if (sucesso) {
                                json.put("sucesso:", "true");
                                String sucessoMsg = "Chave | " + chave + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao();
                                adicionarLog(sucessoMsg);
                                json.put("sucessoMsg", sucessoMsg);
                            }
                        } catch (Exception ex) {
                            String erro1 = "Chave | " + chave + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao() + " | ERRO: " + ex.getMessage();
                            adicionarLog(erro1);
                            json.put("erro1", erro1);
                        }
                    }
                } catch (Exception ex) {
                    String erro2 = "Chave | " + chave + "| Empresa |  " + empresaVO.getCodigo() + " - " + empresaVO.getNome() + " | ERRO: " + ex.getMessage();
                    adicionarLog(erro2);
                    json.put("erro2", erro2);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            json.put("erro3", ex.getMessage());
        } finally {
            empresaDAO = null;
            convenioCobrancaDAO = null;
            pixWebhookService = null;
        }
        return json;
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }
}

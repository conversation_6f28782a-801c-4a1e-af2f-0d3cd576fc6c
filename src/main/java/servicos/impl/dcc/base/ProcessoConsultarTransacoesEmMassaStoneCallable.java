package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.Charsets;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

import static javax.ws.rs.core.MediaType.APPLICATION_XML;
import static servicos.util.ExecuteRequestHttpService.METODO_POST;

public class ProcessoConsultarTransacoesEmMassaStoneCallable implements Callable<String> {

    private final String sak;
    private final String identificador;

    public ProcessoConsultarTransacoesEmMassaStoneCallable(String sak, String identificador) {
        this.sak = sak;
        this.identificador = identificador;

    }

    @Override
    public String call() throws Exception {
        try {
            if (UteisValidacao.emptyString(sak)) {
                throw new Exception("ProcessoConsultarTransacoesEmMassaStoneCallable | Sak não informado");
            }

            if (UteisValidacao.emptyString(identificador)) {
                throw new Exception("ProcessoConsultarTransacoesEmMassaStoneCallable | Identificador não informado");
            }

            StringBuilder xmlConsulta = new StringBuilder();
            xmlConsulta.append("<Document xmlns=\"urn:AcceptorTransactionStatusReportRequestV02.1\">");
            xmlConsulta.append("	<AccptrTxStsRptRq>");
            xmlConsulta.append("		<Hdr>");
            xmlConsulta.append("			<MsgFctn>TSRR</MsgFctn>");
            xmlConsulta.append("			<PrtcolVrsn>2.0</PrtcolVrsn>");
            xmlConsulta.append("			<InitgPty>");
            xmlConsulta.append("				<Id>").append(sak).append("</Id>");
            xmlConsulta.append("			</InitgPty>");
            xmlConsulta.append("		</Hdr>");
            xmlConsulta.append("		<TxStsRpt>");
            xmlConsulta.append("			<Tx>");
            xmlConsulta.append("            <TxRpt>OPRS</TxRpt>");
            xmlConsulta.append("            <TxRpt>SUMM</TxRpt>");
            xmlConsulta.append("				<OrgnlTx>");
            xmlConsulta.append("					<InitrTxId>").append(identificador).append("</InitrTxId>");
            xmlConsulta.append("				</OrgnlTx>");
            xmlConsulta.append("			</Tx>");
            xmlConsulta.append("		</TxStsRpt>");
            xmlConsulta.append("	</AccptrTxStsRptRq>");
            xmlConsulta.append("</Document>");

            try {
                executarRequestConsultaTransacaoPorItkStone(xmlConsulta.toString());
            } catch (Exception ex) {
                Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStoneCallable | **ERRO** Consultando transação... | SAK " + this.sak + " | Identificador " + this.identificador);
            }
        } catch (Exception ex) {
            Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStoneCallable | Erro : " + ex.getMessage());
        }
        return null;
    }

    private String executarRequestConsultaTransacaoPorItkStone(String xmlBody) throws Exception {
        Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStoneCallable | Consultando transação... | SAK " + this.sak + " | Identificador " + this.identificador);
        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", APPLICATION_XML);

        String UrlEndpoint = "https://e-commerce.stone.com.br/TransactionStatusReport";
        return ExecuteRequestHttpService.executeHttpRequest(UrlEndpoint, xmlBody, headersMap, METODO_POST, Charsets.UTF_8.name());
    }
}

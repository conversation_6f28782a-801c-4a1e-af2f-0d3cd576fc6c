/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;

/**
 *
 * <AUTHOR>
 */
public class CaixaPostalSFTP {

    private TipoConvenioCobrancaEnum tipo;
    private String host;
    private Integer porta;
    private String user;
    private String pwd;
    private String diretorioRemoto;

    public CaixaPostalSFTP(TipoConvenioCobrancaEnum tipo, String host,
            Integer porta, String user, String pwd, String diretorioRemoto) {
        this.tipo = tipo;
        this.host = host;
        this.porta = porta;
        this.user = user;
        this.pwd = pwd;
        this.diretorioRemoto = diretorioRemoto;
    }

    public CaixaPostalSFTP(String host, Integer porta, String user, String pwd,
            String diretorioRemoto) {
        this.host = host;
        this.porta = porta;
        this.user = user;
        this.pwd = pwd;
        this.diretorioRemoto = diretorioRemoto;
    }

    public TipoConvenioCobrancaEnum getTipo() {
        return tipo;
    }

    public String getHost() {
        return host;
    }

    public Integer getPorta() {
        return porta;
    }

    public String getUser() {
        return user;
    }

    public String getPwd() {
        return pwd;
    }

    public String getDiretorioRemoto() {
        return diretorioRemoto;
    }

    public void setDiretorioRemoto(String diretorioRemoto) {
        this.diretorioRemoto = diretorioRemoto;
    }

    @Override
    public String toString() {
        return "CaixaPostalSFTP{" + "tipo=" + tipo + ", host="
                + host + ", porta=" + porta + ", user=" + user + ", pwd="
                + pwd + ", diretorioRemoto=" + diretorioRemoto + '}';
    }
}

package servicos.impl.dcc.base;

import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAlterarCredenciaisConvenioStone extends SuperEntidade {

    private static Integer qtdAjustado = 0;
    private static boolean simular = true;

    public ProcessoAlterarCredenciaisConvenioStone() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
//            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");

            //id rede empresa engenharia 467
            //id rede empresa pratique 136

            Integer idRedeEmpresa = 136;
            simular = true;

            Map<String, AlterarConvenioStoneDTO> mapa = mapa();
            processarZW(idRedeEmpresa, mapa, conOAMD);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAlterarCredenciaisConvenioStone.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    private static void processarZW(Integer idRedeEmpresa, Map<String, AlterarConvenioStoneDTO> mapa, Connection conOAMD) throws Exception {
        String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                " \"nomeBD\" as banco, porta, urlintegracaows " +
                "from empresa " +
                "where chave in (select distinct chavezw from empresafinanceiro " +
                "where redeempresa_id = " + idRedeEmpresa + " and length(coalesce(chavezw ,'')) > 0)";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
        while (rs.next()) {
            String chave = rs.getString("chave");
            String host = rs.getString("host");
            Integer porta = rs.getInt("porta");
            String banco = rs.getString("banco");
            String user = rs.getString("user");
            String pass = rs.getString("pass");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                try {
                    Uteis.logarDebug("Rodar Script: " + chave + " - " + banco);
                    if (!banco.contains("pratique")) {
                        Uteis.logarDebug("######## Rodar Script: " + chave + " - " + banco);
                        Uteis.logarDebug("######## Rodar Script: " + chave + " - " + banco);
                        Uteis.logarDebug("######## Rodar Script: " + chave + " - " + banco);
                    }
                    executarAlterarConvenioStone(chave, mapa, con);
                } catch (Exception ex) {
                    Uteis.logarDebug("ERRO Script: " + chave + " - " + banco + " | " + ex.getMessage());
                }
            } catch (Exception ex) {
                Uteis.logarDebug("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
            }
        }
        Uteis.logarDebug("qtdAjustado: " + qtdAjustado);
    }

    private static void executarAlterarConvenioStone(String chave, Map<String, AlterarConvenioStoneDTO> mapa, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("c.codigo, \n");
        sql.append("c.descricao, \n");
        sql.append("c.tipoconvenio, \n");
        sql.append("c.situacao, \n");
        sql.append("c.codigoautenticacao01 as stonecode, \n");
        sql.append("c.codigoautenticacao02 as sak \n");
        sql.append("from conveniocobranca c \n");
        sql.append("where c.tipoconvenio = ").append(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo()).append(" \n");
//        sql.append("and c.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo());

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            Integer codigo = rs.getInt("codigo");
            String descricao = rs.getString("descricao");
            String stonecodeAtual = rs.getString("stonecode");
            String sakAtual = rs.getString("sak");

            AlterarConvenioStoneDTO dto = mapa.get(chave);
            if (dto == null) {
                throw new Exception("Mapa não encontrado | Chave " + chave);
            }
            String stonecodeNovo = dto.getStoneCode();
            String sakNovo = dto.getSak();

            if (!stonecodeNovo.equals(stonecodeAtual) || !sakNovo.equals(sakAtual)) {
                Uteis.logarDebug(chave + " | Alterar convênio: " + codigo + " - " + descricao + " | StoneCode Atual: " + stonecodeAtual + " | SAK Atual: " + sakAtual);

                String scriptRollBack = ("update conveniocobranca set codigoautenticacao01 = '" + stonecodeAtual + "', codigoautenticacao02 = '" + sakAtual + "' where codigo = " + codigo);
                String script = ("update conveniocobranca set codigoautenticacao01 = '" + stonecodeNovo + "', codigoautenticacao02 = '" + sakNovo + "' where codigo = " + codigo);

                Uteis.logarDebug("Rollback: Chave " + chave + " | " + scriptRollBack);
                Uteis.logarDebug("Update: Chave " + chave + " | " + script);
                if (!simular) {
                    SuperFacadeJDBC.executarUpdate(script, con);
                }
                ++qtdAjustado;
            } else {
                Uteis.logarDebug(chave + " | CONVENIO JA ESTA CONFIGURADO: " + codigo + " - " + descricao + " | StoneCode Atual: " + stonecodeAtual + " | SAK Atual: " + sakAtual);
            }
        }
    }

    private static Map<String, AlterarConvenioStoneDTO> mapa() {
        List<AlterarConvenioStoneDTO> lista = new ArrayList<>();
        lista.add(new AlterarConvenioStoneDTO("5a8cc4832efe42145cc18ddb1e5ad6f5	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("bb4018128272283142e869f5d34cf084	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("3fcd1b74da3b2c6f680e82b72db39c61	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("69452aa8b15686834704921d31540db4	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("6f655e458a8caf8dfec249b995cf0d0f	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("94d443b55fdc9dea62f45a4f64769f4f	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("950e70287c5020596e0d4be18cf549db	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("953fd09bb12467409b834cd00146bf22	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("a5a986ae237438324cac1a87aab8bab0	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("bcf34b330655edd31d812bd0653dc862	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("10c2f72f6a17c43bb679fe9628e5f1da	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("15d562417b8fa07a156a99a67c346a04 	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("176d4211bde27432add1be2e768581a0	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("21e39cadedda34c6e26d4f1d6c897b24	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("28e5dd449b0dc90671f1dc938a05a18d	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("2affa34296b5bf0267cccfe64f7e5119	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("35e6999659075160839ae0171e2e405c	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("42e47c2df46b2e00b9d96194ce822a12	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("442c3f0cf4adcba347aa73d42785bcc8	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("4761b428f38980a5a3a9c5fada15fa84	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("4b36b9842885ebf326ff3a1c182c4092	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("510a1fde98c85c4ae278696c2ecba4d9	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("5548559f158335936fdadf3496dc58f9	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("55eae2b88e623beb1419e02588c8560b	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("56bd14b9d48583896a9622e42158137	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("589b54c2e94d0ad20091c3876bd34ddb	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("5c27f435bed41b3933a9ef698481f700	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("5c8b2e9a77b7e0f10f8b8612db7359b9	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("5cb1f1bc55b1878a48dc01e10e658290	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("61b885f1c340bbb538131fe88caa0a68	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("6718ee34a62862aba2beeb1d9acfceaf	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("67da95acc77dda4365adffb6560a924d	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("67da95acc77dda4365adffb6560a924d	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("703d1805e9b3a0b18a81eb822f66655c	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("750241fde1cd5bd7b0032541b191be49	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("76763bdf7e1c3f5b518dd62d8b5766a1	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("767f1ae6e9272de3e6ce8c42be3ff4ea	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("76f01cabb189e8d48bc4a382ced0a1dc	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("7bb2d34d3172169c43f083b8c0574a7f	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("7c24bb9deb941e9545f1fb18596e060d	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("7d291e4b18a0a0a0898249988c9fc624	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("841e09b8336fe639ce0d869938953915	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("8553af5a471fde839264af3fe6d2d81a	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("8ad6a93a48c86b00edfba545e86d39b1	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("8ad6a93a48c86b00edfba545e86d39b1	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("90feb6cf79c043140bd85d18f7a420ac	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("970871f7b472090a2fe80eaa9e08711e	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a48910f14af8f4c8c450a2007f119244	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a7773d241986e326045e3d3124d2dc92	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("a86d3f9e83200630a4975aecad8d445e	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a93b7fb8bc2999f9e2f3c90801809497	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("ade1581674222d645c6220f3f2ed4d35	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("ae66ec91296114207b102e5452f32f6e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("b34b808abb4713d666122b0a8283f15e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("b9055aace082097a99ba272a5613ab5f	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("bbb173370b0e5e7a7394c292678e434a	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("bc90a77b5b1b42a8d392b0be68303e55	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("be5a2bf9bdaed7df7abd8c9854fca4	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("be5a2bf9bdaed7df7abd8c9854fca4	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("bf5e296f25133b3400a06f38989a897b	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("c3f142aff19990abc1b5b9212447303a	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("c66fa993c772af1fa766826fc9da9669	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("c8d80d04b5a0566016113b15aefe5d5b	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("ca32ba4f275d27c304da2852b238a023	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("cac3c71719378b2377068ccf2303f948	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("cbd380193beb91bd61658935cf1e6a80	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("cd8c63c16eeef458361510fadfc5b319	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d33e2dc6e025d05489e261b28ca5c6af	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d391d251f8bf9e16f63cdd8f60c21558	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d48b4fa775ecc46413730e800ab59c80	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d623351470cccf3ad5ff20a117f450e2	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("d826fbbdd2c37d1342b8d16dfa5c75fd	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d826fbbdd2c37d1342b8d16dfa5c75fd	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d8d7b998f15f59f27617b0790702c63e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d8d7b998f15f59f27617b0790702c63e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d8d7b998f15f59f27617b0790702c63e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("d8d7b998f15f59f27617b0790702c63e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("e8249b227b1c3cfb732b8180aa484a38	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("e9248c86e86a42cd891b584f786ff3ab	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("ed5ab38f77ec8b6cb74ada821559487e	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("eee722990a3717f4f657059df7e388f3	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("f7ede23039c6b36f3426a1717e7022ec 	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("fc9f8597610376a7aef4dca025ae9ac7	", "6bc11d389531416098b895a10dee5c61	", "24fac3c00ced43038a965395d68c60bb"));
        lista.add(new AlterarConvenioStoneDTO("fd73282d5ca5865952a7b952228d659a	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("8adace7a495c4ffc1cc72465a5d90972	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("250bb4ef7f65adec2c927caca85a3f9a	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("87c3b562574301ec96ff898add1a01a9	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("adbd8170be062d57d67b515f59f8a934	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("491897ad0047892a0417f289495f01b5	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("46c6509e511fb588e2d66ffdaa690965	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("634ef1e6880c381c615ba2fcdedfff9e		", "", ""));
        lista.add(new AlterarConvenioStoneDTO("d76a94be5d04c831f49231c5dab065d5	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("7abe760654464774bb383d3b2153ceb	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("acada83f2ce83737553b7bd24ccbc03e	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("65c85f8d5191adccb6733307292e980f	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("5aaba87cd18884a2c4d17c872adbf351	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("5d953b2cccf46cacb01a6aebe1b150c1	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a5f31ffd648bd313ee8212b87464f6e3	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("d84017d5be86c834cb2d0cd48253785	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("ff9bdc1899b2097474823395db2841c4	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("80f86f436289f10f27fec4036589681e	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("cb116c1716d537587bc4ec8397ad6273	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("e6d8f1a1f3daf1cf3fb841ca03dc0181	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("3f76c5f4704874f224c6fbff17475a85	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("3a19c1e4117c971cea310a84d143dee5	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("3edef8f45484dcf19daf267bfbdcc566	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a2c837b1d39bc0a5199e9f60820315fb	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("19830abc7c9c57d7147b05025f68c5f3	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("8ac568d74191771ac51536eec0a914c0	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("dbc0da1ae28dca3eba80523a378b8080	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("6196dc520dfd4636cfb05785b2f8b4ae	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a7d256f32ecb1afa0c660048846123ca	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("77ff8085434367107c64d7f9da792dc0	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("adbd8170be062d57d67b515f59f8a934	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("bf507defb24f6021f1ee0f43cc3722c4	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("bdfd0b64da6255bdb1658ba11e770fac	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("15a727c1a5c63e01f948a471d913b126	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("adbd8170be062d57d67b515f59f8a934	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("34281f9115b3184e312ca3bb6496f3ad	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("34281f9115b3184e312ca3bb6496f3ad	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("fdff0273fdd405160ed5237371f37457	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("ed201ec1d1ed33c61c03a9cfdc9363e8	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("e31e70c08cce88d85a3ec59e29e031a6	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("a61c98c71894331206618d095aadc6a	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("2fab0a2fb3fc265720704506aa285c 	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("4d557dbd7fd3a5333ae7e7a744c39e54	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("189ae066f0951a18334ca0d147d5927d	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));
        lista.add(new AlterarConvenioStoneDTO("fa88db166d31f80532374868a46fad	", "f35b57d2ff474fca84246be6433482b3	", "3a17c17fed104136807efd2d749a4694"));

        Map<String, AlterarConvenioStoneDTO> mapa = new HashMap<>();
        Set<String> chavesDuplicadas = new HashSet<>();
        for (AlterarConvenioStoneDTO dto : lista) {
            AlterarConvenioStoneDTO dtoMapa = mapa.get(dto.getChave());
            if (dtoMapa != null) {
                if (dtoMapa.getId().equals(dto.getId())) {
                    //mesmos dados ignorar
                } else {
                    chavesDuplicadas.add(dto.getChave());
                    Uteis.logarDebug(dto.getChave() + " | DADOS DIFERENTES dtoMapa: " + dtoMapa.getId() + " - dto: " + dto.getId());
                }
            } else {
                mapa.put(dto.getChave(), dto);
            }
        }

        Map<String, AlterarConvenioStoneDTO> mapaFinal = new HashMap<>();
        for (AlterarConvenioStoneDTO dto : lista) {
            if (!chavesDuplicadas.contains(dto.getChave())) {
                mapaFinal.put(dto.getChave(), dto);
            }
        }
        return mapaFinal;
    }
}

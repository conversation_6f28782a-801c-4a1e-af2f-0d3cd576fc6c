/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RegistroRemessa extends SuperTO {

    private static final long serialVersionUID = 5963058258701261415L;
    private TipoRegistroEnum tipo = TipoRegistroEnum.HEADER;
    private List<ObjetoGenerico> atributos = new ArrayList<ObjetoGenerico>();

    public RegistroRemessa(TipoRegistroEnum tipo) {
        this.tipo = tipo;
    }

    public TipoRegistroEnum getTipo() {
        return tipo;
    }

    public void setTipo(TipoRegistroEnum tipo) {
        this.tipo = tipo;
    }

    public List<ObjetoGenerico> getAtributos() {
        return atributos;
    }

    public void setAtributos(List<ObjetoGenerico> atributos) {
        this.atributos = atributos;
    }

    public void put(Enum q, final Object valor) {
        ObjetoGenerico o;
        if (valor instanceof String) {
            o = new ObjetoGenerico(q.name(), Uteis.retirarAcentuacaoRegex((String) valor));
        } else {
            o = new ObjetoGenerico(q.name(), valor);
        }

        atributos.add(o);
    }

    public void set(Enum q, final String valor) {
        for (ObjetoGenerico objetoGenerico : atributos) {
            if (objetoGenerico.getAtributo().equals(q.name())) {
                objetoGenerico.setValor(StringUtilities.formatarCampoForcandoZerosAEsquerda(valor, objetoGenerico.getValor().length()));
                break;
            }
        }
    }

    public String getValue(final String atributo) {
        for (ObjetoGenerico objetoGenerico : atributos) {
            if (objetoGenerico.getAtributo().equals(atributo)) {
                return objetoGenerico.getValor();
            }
        }
        return "";
    }

    public String get(final String atributo) {
        for (ObjetoGenerico obj : atributos) {
            if (obj.getAtributo().equals(atributo)) {
                return String.format("%s=%s", obj.getAtributo(), obj.getValor());
            }
        }
        return "";
    }

    public StringBuilder toStringBuffer() {
        StringBuilder sb = new StringBuilder();
//        sb.append("<tr>");
        for (ObjetoGenerico o : atributos) {
//            sb.append("<td>").append(o.getValor()).append("</td>");
            sb.append(o.getValor());
        }
//        sb.append("</tr>");
        return sb;
    }

    public StringBuilder firstBlock(final String stopBeforeAttribute) {
        StringBuilder sb = new StringBuilder();
        for (ObjetoGenerico o: atributos) {
            if (o.getAtributo().equalsIgnoreCase(stopBeforeAttribute))
                break;
            sb.append(o.getValor());
        }
        return sb;
    }

    @Override
    public String toString() {
        return atributos.toString();
    }
}

package servicos.impl.dcc.base;

import br.com.pactosolucoes.enumeradores.LogGenericoTipoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.stone.StoneOnlineServiceConciliation;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoSolicitarConcessoExtratoStone extends SuperEntidade {

    public ProcessoSolicitarConcessoExtratoStone() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            processarTodosOsBancos(conOAMD);

//            Connection con = DriverManager.getConnection("*********************************************", "postgres", "pactodb");
//            processarUmaEmpresa("", con);
        } catch (Exception ex) {
            Logger.getLogger(ProcessarBoletosPJBankEngenharia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static JSONObject processarTodosOsBancos(Connection conOAMD) throws Exception {
        JSONObject json = new JSONObject();
        JSONArray array = new JSONArray();
        try {
            String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user, \"nomeBD\" as banco, porta, urlintegracaows from empresa where ativa\n" +
                    "and chave in (select chavezw from empresafinanceiro where codigo in (select distinct empresafinanceiro_codigo from z_oamd_pactopay_faturamento where adquirente ilike '%stone%'))";
            Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", conOAMD);
            Uteis.logarDebug("total empresas: "+ total);
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
            while (rs.next()) {
                String chave = rs.getString("chave");
//                String pass = rs.getString("pass");
//                String host = rs.getString("host");
//                String user = rs.getString("user");
//                String porta = rs.getString("porta");
//                String banco = rs.getString("banco");
//                String robocontrole = rs.getString("robocontrole");
                String urlintegracaows = rs.getString("urlintegracaows");
                try {
                    String rest = processarServlet(chave, urlintegracaows);
                    array.put(rest);
                } catch (Exception ex) {
//                    ex.printStackTrace();
                    Uteis.logarDebug("ERRO " + chave + " - " + ex.getMessage());
                }
//                try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
//                    JSONObject jsonEmp = processarUmaEmpresa(chave, con);
//                    array.put(jsonEmp);
//                } catch (Exception ex) {
//                    Uteis.logarDebug("ERRO " + banco + " - " + ex.getMessage());
//                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            json.put("erroGeral", ex.getMessage());
            throw ex;
        }
        return json;
    }

    private static String processarServlet(String chave, String urlZw) throws Exception {
//        String urlGeral = "http://localhost:8082/zw/prest/generico?key=TODAS_EMPRESAS&op=concessao-stone-conciliacao"

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        Map<String, String> params = new HashMap<String, String>();
        params.put("key", chave);
        params.put("op", "concessao-stone-conciliacao");

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(urlZw + "/prest/generico", headers, params, null, MetodoHttpEnum.GET);
        service = null;
        return respostaHttpDTO.getResponse();
    }

    public static JSONObject processarUmaEmpresa(String chave, Connection con) {
        ConvenioCobranca convenioCobrancaDAO;
        Empresa empresaDAO;
        Usuario usuarioDAO;
        JSONObject json = new JSONObject();
        Log logDAO;
        try {
            empresaDAO = new Empresa(con);
            usuarioDAO = new Usuario(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            logDAO = new Log(con);

            Uteis.logarDebug("Processando Chave: " + chave);

            json.put("chave", chave);

            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            List<EmpresaVO> empresaVOList = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (EmpresaVO empresaVO : empresaVOList) {
                try {
                    Integer[] ARRAY = new Integer[]{TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(), TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.getCodigo()};
                    List<ConvenioCobrancaVO> listaConv = convenioCobrancaDAO.consultarPorEmpresaESituacao(empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                            ARRAY, SituacaoConvenioCobranca.ATIVO);
                    if (UteisValidacao.emptyList(listaConv)) {
                        throw new Exception("Nenhum convênio DCC_STONE_ONLINE / PINPAD_STONE_CONNECT encontrado");
                    }
                    for (ConvenioCobrancaVO convenioCobrancaVO : listaConv) {
                        JSONObject jsonLog = new JSONObject();
                        jsonLog.put("convenioCod", convenioCobrancaVO.getCodigo());
                        jsonLog.put("convenioDesc", convenioCobrancaVO.getDescricao());
                        jsonLog.put("convenioTipo", convenioCobrancaVO.getTipo().getCodigo());
                        try {
                            String msg;
                            boolean existeExtrato = SuperFacadeJDBC.existe("select codigo from extratodiarioitem " +
                                    "where conveniocobranca = "+convenioCobrancaVO.getCodigo() +
                                    " and datalancamento::date >= '"+Uteis.getDataFormatoBD(Calendario.somarDias(Calendario.hoje(), -15))+"'", con);
                            if (existeExtrato) {
                                //já tem extrato então vou ignorar
                                msg = ("Já existe extrato para o convenio - " + convenioCobrancaVO.getDescricao());
                            } else {
                                msg = StoneOnlineServiceConciliation.concessaoAcesso(convenioCobrancaVO);
                            }
                            json.put("sucesso", msg);
                            String sucessoMsg = "Chave | " + chave + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao() + " | MSG: " + msg;
                            Uteis.logarDebug(sucessoMsg);
                            json.put("sucessoMsg", sucessoMsg);

                            jsonLog.put("sucesso", true);
                            jsonLog.put("msg", msg);
                        } catch (Exception ex) {
                            String erro1 = "Chave | " + chave + "| Convenio |  " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao() + " | ERRO: " + ex.getMessage();
                            Uteis.logarDebug(erro1);
                            json.put("erro1", erro1);

                            jsonLog.put("sucesso", false);
                            jsonLog.put("msg", ex.getMessage());
                        } finally {
                            logDAO.incluirLogGenerico(LogGenericoTipoEnum.SOLICITAR_CONCILIACAO_STONE, usuarioVO, jsonLog.toString());
                        }
                    }
                } catch (Exception ex) {
                    String erro2 = "Chave | " + chave + "| Empresa |  " + empresaVO.getCodigo() + " - " + empresaVO.getNome() + " | ERRO: " + ex.getMessage();
                    Uteis.logarDebug(erro2);
                    json.put("erro2", erro2);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            json.put("erro3", ex.getMessage());
        } finally {
            empresaDAO = null;
            convenioCobrancaDAO = null;
            logDAO = null;
            usuarioDAO = null;
        }
        return json;
    }
}

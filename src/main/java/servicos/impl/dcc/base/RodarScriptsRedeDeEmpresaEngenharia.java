package servicos.impl.dcc.base;

import controle.financeiro.MovParcelaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class RodarScriptsRedeDeEmpresaEngenharia extends SuperEntidade {

    private static Integer qtdAjustado = 0;
    private static boolean simular = true;

    public RodarScriptsRedeDeEmpresaEngenharia() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");

            //id rede empresa engenharia 467
            //id rede empresa pratique 136

            Integer idRedeEmpresa = 0;
            String[] chaves = new String[] {}; // opcional
            simular = true;

            processarZW(idRedeEmpresa, conOAMD, chaves); //banco zw

//            processarTreino(idRedeEmpresa, conOAMD, chaves); //banco treino
        } catch (Exception ex) {
            Logger.getLogger(RodarScriptsRedeDeEmpresaEngenharia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    private static void processarTreino(Integer idRedeEmpresa, Connection conOAMD, String[] chaves) throws Exception {
        String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                " \"nomeBD\" as banco, porta, urlintegracaows " +
                "from empresa " +
                "where chave in (select distinct chavezw from empresafinanceiro " +
                "where redeempresa_id = " + idRedeEmpresa + " and length(coalesce(chavezw ,'')) > 0 \n";
        if (!UteisValidacao.emptyArray(chaves)) {
            sql += "and chave in (" +  String.join(",", chaves) + ")";
        }
        sql += ")";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
        while (rs.next()) {
            String chave = rs.getString("chave");
            String host = rs.getString("host");
            Integer porta = rs.getInt("porta");
            String banco = rs.getString("banco").replace("bdzillyon", "bdmusc");
            String user = rs.getString("user");
            String pass = rs.getString("pass");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                try {
//                    Uteis.logarDebug("Rodar Script: " + chave + " - " + banco);
                    adicionarPermissaoTreino(chave, banco, 29, con);
                } catch (Exception ex) {
                    Uteis.logarDebug("ERRO Script: " + chave + " - " + banco + " | " + ex.getMessage());
                }
            } catch (Exception ex) {
                Uteis.logarDebug("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
            }
        }
        Uteis.logarDebug("qtdAjustado: " + qtdAjustado);
    }

    private static void processarZW(Integer idRedeEmpresa, Connection conOAMD, String[] chaves) throws Exception {
        String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                " \"nomeBD\" as banco, porta, urlintegracaows " +
                "from empresa " +
                "where chave in (select distinct chavezw from empresafinanceiro " +
                "where redeempresa_id = " + idRedeEmpresa + " and length(coalesce(chavezw ,'')) > 0 \n";
        if (!UteisValidacao.emptyArray(chaves)) {
            sql += "and chave in ('" +  String.join("','", chaves) + "')";
        }
        sql += ")";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
        while (rs.next()) {
            String chave = rs.getString("chave");
            String host = rs.getString("host");
            Integer porta = rs.getInt("porta");
            String banco = rs.getString("banco");
            String user = rs.getString("user");
            String pass = rs.getString("pass");

            try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                try {
                    Uteis.logarDebug("Rodar Script: " + chave + " - " + banco);
                    // Adicionar o metodo ou script que será executado...
                } catch (Exception ex) {
                    Uteis.logarDebug("ERRO Script: " + chave + " - " + banco + " | " + ex.getMessage());
                }
            } catch (Exception ex) {
                Uteis.logarDebug("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
            }
        }
        Uteis.logarDebug("qtdAjustado: " + qtdAjustado);
    }

    private static void cancelarParcelasFiltrandoPorDataVencimentoOuCodigosParcelas(Connection con, String dateVencimentoLimite, String codigosParcelas) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDAO.consultarPorUsername("pactobr", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        MovParcela movParcelaDAO = new MovParcela(con);


        String sql = "select * from movparcela \n" +
                "where situacao = 'EA' \n" +
                "and datavencimento < '" + dateVencimentoLimite + "' \n";

        if (!UteisValidacao.emptyString(codigosParcelas)) {
            sql += "and codigo in (" + codigosParcelas + ") \n";
        }
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        while (rs.next()) {
            MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<MovParcelaVO> parcelasCancelar = new ArrayList<>();
            parcelasCancelar.add(movParcelaVO);
            if (!simular) {
                MovParcelaControle movParcelaControle = new MovParcelaControle(true);
                movParcelaControle.setListaParcelasPagar(parcelasCancelar);
                movParcelaControle.setJustificativaCancelamento("Cancelamento solicitado pela diretoria da Live");
                movParcelaControle.cancelarParcelas(usuarioVO);
                movParcelaControle = null;
            }

            System.out.println(String.format("Cancelei a parcela: %d %s %s %s %s", movParcelaVO.getCodigo(), movParcelaVO.getDescricao(),
                    Calendario.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy"), movParcelaVO.getValorParcela_Apresentar(), movParcelaVO.getSituacao()));
        }

        qtdAjustado++;
    }

    private static void adicionarPermissaoTreino(String chave, String banco, Integer recurso, Connection con) throws Exception {
        try {
            if (!simular) {
                con.setAutoCommit(false);
            }

            ResultSet rs = SuperFacadeJDBC.criarConsulta("select * from perfil WHERE nome NOT ILIKE 'PROFESSOR' and codigo not in (select perfil_codigo from permissao where recurso = " + recurso + ")", con);
            while (rs.next()) {
                String nomePerfil = rs.getString("nome");
                Integer codigoPerfil = rs.getInt("codigo");

                Uteis.logarDebug("Rodar Script: " + chave + " - " + banco + " | Adicionando para perfil: " + codigoPerfil + " - " + nomePerfil);

                if (!simular) {
                    String sql1 = "insert into permissao(codigo, recurso, perfil_codigo) values ((select max(codigo) + 1 from permissao) ," + recurso + "," + codigoPerfil + ") RETURNING codigo";
                    PreparedStatement sqlInserir1 = con.prepareStatement(sql1);
                    ResultSet rsNovo = sqlInserir1.executeQuery();
                    rsNovo.next();
                    Integer codigoPermissao = rsNovo.getInt(1);

                    String sql2 = "insert into permissao_tipopermissoes(permissao_codigo, tipopermissoes) values (" + codigoPermissao + ",5)";
                    SuperFacadeJDBC.executarUpdate(sql2, con);
                }

                ++qtdAjustado;
            }

            if (!simular) {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("##### Erro chave: " + chave + " | Erro: " + ex.getMessage());
            if (!simular) {
                con.rollback();
            }
        } finally {
            if (!simular) {
                con.setAutoCommit(true);
            }
        }
    }

    private static void executarAlterarConvitePlano(Integer convidadosPorMes, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("distinct  \n");
        sql.append("p.codigo, \n");
        sql.append("p.descricao, \n");
        sql.append("p.convidadosPorMes \n");
        sql.append("from plano p \n");
        sql.append("left join planoduracao pd on pd.plano = p.codigo \n");
        sql.append("left join planorecorrencia pr on pr.plano = p.codigo \n");
        sql.append("where (p.ingressoate >= current_date or p.vigenciaate >= current_date) \n");
        sql.append("and (pr.duracaoplano >= 12 or pd.numeromeses >= 12 ) \n");
        sql.append("and p.convidadosPorMes <> ").append(convidadosPorMes).append(" \n");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            Integer codigo = rs.getInt("codigo");
            String descricao = rs.getString("descricao");
            Integer convidadosPorMesAtual = rs.getInt("convidadosPorMes");

            Uteis.logarDebug("Ajustando plano: " + descricao + " | convidadosPorMes Atual: " + convidadosPorMesAtual + " | convidadosPorMes: " + convidadosPorMes);

            String scriptRollBack = ("update plano set convidadosPorMes = " + convidadosPorMesAtual + " where codigo = " + codigo);
            String script = ("update plano set convidadosPorMes = " + convidadosPorMes + " where codigo = " + codigo);

            Uteis.logarDebug("Rollback: " + scriptRollBack);
            Uteis.logarDebug("Scrip: " + script);
            SuperFacadeJDBC.executarUpdate(script, con);
            ++qtdAjustado;
        }
    }
}


package servicos.impl.dcc.base;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
public class ProcessoConsultarTransacoesEmMassaStone extends SuperEntidade {

    public ProcessoConsultarTransacoesEmMassaStone(Connection con) throws Exception {
        super(con);
    }

    public ProcessoConsultarTransacoesEmMassaStone() throws Exception {
        Connection con = getFacade().getEmpresa().getCon();
    }

    public static void main(String... args) {
        try {
            String chave = null;
            String qtdDiasAtrasPesquisar = null;
            String qtdLoteThreads = null;

            if (args.length == 0) {
                args = new String[]{"pelotas", "3", "4"};
            }
            if (args.length > 0) {
                chave = args[0];
                qtdDiasAtrasPesquisar = args[1];
                qtdLoteThreads = args[2];
            }
            Uteis.logarDebug("###### INÍCIO ProcessoConsultarTransacoesEmMassaStone ######");
            Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStone | Obter conexão para chave: " + chave +
                    " | qtdDiasAtrasPesquisar: " + qtdDiasAtrasPesquisar + " | qtdLoteThreads: " + qtdLoteThreads);

            Uteis.debug = true;
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);

            StringBuilder sql = new StringBuilder();
            sql.append("select t.codigo , t.codigoexterno, t.situacao, c.codigoautenticacao01 as stonecode, c.codigoautenticacao02 as sak, \n");
            sql.append(" regexp_replace( \n");
            sql.append(" regexp_matches(t.paramsresposta, '<TxRef>(.*?)</TxRef>')::text, \n");
            sql.append(" '\\{|\\}|\"', \n");
            sql.append(" '', \n");
            sql.append(" 'g' \n");
            sql.append("    ) AS InitrTxId \n");
            sql.append("from transacao t \n");
            sql.append("inner join conveniocobranca c on c.codigo = t.conveniocobranca \n");
            sql.append("where c.tipoconvenio = 21 \n");
            sql.append("and dataprocessamento::date >= (current_date - interval '2 days') \n");
            sql.append("and t.situacao in (2,3) \n");
            sql.append("and t.paramsresposta <> '' and t.paramsresposta is not null \n");
            sql.append("and t.codigoexterno is not null \n");
            sql.append("and c.somenteextrato = false \n");
            sql.append("order by t.codigo desc \n");

            List<ProcessoConsultarTransacoesEmMassaStoneCallable> callableTasks = new ArrayList<>();
            int totalTransacoesEncontradas = 0;
            int totalTransacoesHabeisAConsultar = 0;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    while (rs.next()) {
                        totalTransacoesEncontradas++;
                        String sak = rs.getString("sak");
                        String identificador = rs.getString("InitrTxId");
                        if (!UteisValidacao.emptyString(sak) && !UteisValidacao.emptyString(identificador)) {
                            callableTasks.add(new ProcessoConsultarTransacoesEmMassaStoneCallable(sak, identificador));
                            totalTransacoesHabeisAConsultar++;
                        }
                    }
                }
            }

            Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStone | Total de transações encontradas: " + totalTransacoesEncontradas +
                    " | Total de transações hábeis a consultar: " + totalTransacoesHabeisAConsultar);

            Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStone | Iniciando consulta em massa de transações na Stone usando: " + qtdLoteThreads + " threads");
            final ExecutorService executorService = Executors.newFixedThreadPool(Integer.parseInt(qtdLoteThreads));
            executorService.invokeAll(callableTasks);
            executorService.shutdown();

        } catch (Exception e) {
            Uteis.logarDebug("ProcessoConsultarTransacoesEmMassaStone | Erro: " + e.getMessage());
            e.printStackTrace();
        }
    }


}

package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.UteisValidacao;

import java.io.BufferedReader;
import java.io.FileReader;

/**
 * <AUTHOR>
 */
public class ProcessoLerArquivoCobranca {

    public static void main(String[] args) throws Exception {
        BufferedReader br = new BufferedReader(new FileReader("C:\\Desenv\\remessa-adrianasilva.txt"));
        StringBuilder sb = new StringBuilder();
        String line = br.readLine();
        System.out.println("###########INICIO PROCESSO###########");
        while (line != null) {
            String data = "";
            String codPessoa = "";
            String nome = "";
            String parcelas = "";
            String identificadorStone = "";
            String textoImprimir = "";

            sb.append(line);
            if (line.contains("Start Process | Pagador:")) {
                String[] split = line.split("Start Process | Pagador:");
                data = split[0].replace("-", "").trim();
                String segundaLinha = split[2];
                String[] split2 = segundaLinha.split("-");
                codPessoa = split2[0].trim();
                String[] split3 = split2[1].split("\\|");
                nome = split3[0].trim();
                String[] split4 = split3[1].split("Parcelas:");
                parcelas = split4[1].trim();
                textoImprimir = data + ";" + codPessoa + ";" + nome + ";" + parcelas;
                line = br.readLine();
            }
            if (line != null && line.contains("IDENTIFICADOR STONE:")) {
                String[] split = line.split("IDENTIFICADOR STONE:");
                identificadorStone = split[1].trim();
                textoImprimir += ";" + identificadorStone;
            }
            if (!UteisValidacao.emptyString(textoImprimir)) {
                System.out.println(textoImprimir);
            }
            line = br.readLine();
        }
        System.out.println("###########FIM PROCESSO###########");
    }

}

package servicos.impl.dcc.base;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ReprocessarExtratos extends SuperEntidade {

    private Date reprocessarAPartirDe;
    private Date reprocessarAte;

    public ReprocessarExtratos(Date reprocessarAPartirDe, Date reprocessarAte) throws Exception {
        this.reprocessarAPartirDe = reprocessarAPartirDe;
        this.reprocessarAte = reprocessarAte;
    }

    public void reprocessarExtratos(String chave) throws Exception {
        reprocessarExtratos(chave, true);
    }

    public void reprocessarExtratos(String chave, boolean reprocessarConciliacaoAutomatica) throws Exception {
        Connection c = null;

        try {
            c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
            c.setAutoCommit(false);

            String condicaoCielo = " and arquivo ilike '%CIELO%' ";
            String condicaoStoneOnline = " and arquivo ilike 'StoneOnlineServiceConciliation%'";
            String condicaoStoneOnlineV5 = " and arquivo ilike 'StoneOnlineServiceV5Conciliation%'";
            String condicaoConvenioAtivo = "    and conveniocobranca in (select codigo from conveniocobranca where situacao = 1) \n";
            StringBuilder sql = new StringBuilder();

            if (getReprocessarAte() == null) {
                sql.append("delete from extratodiarioitem \n");
                sql.append(" where dataprevistapagamento::date >= '"+ Uteis.getDataFormatoBD(getReprocessarAPartirDe())+ "' \n");
                sql.append(condicaoCielo);
                sql.append(condicaoConvenioAtivo);
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append(" where cast(substring(arquivo, 32, 8) as date) >= '"+ Uteis.getDataFormatoBD(getReprocessarAPartirDe())+ "'");
                sql.append(condicaoStoneOnline);
                sql.append(condicaoConvenioAtivo);
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append(" where cast(substring(arquivo, 32, 8) as date) >= '"+ Uteis.getDataFormatoBD(getReprocessarAPartirDe())+ "'");
                sql.append(condicaoStoneOnlineV5);
                sql.append(condicaoConvenioAtivo);
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

            } else {
                //Apagar dados Cielo
                sql.append("delete from extratodiarioitem \n");
                sql.append("where ( \n");
                Integer intervalo = Calendario.diferencaEmDias(getReprocessarAPartirDe(), getReprocessarAte());
                Date dataClone = new Date(getReprocessarAPartirDe().getTime());
                for (int i = 0; i <= intervalo; i++){
                    SimpleDateFormat formatador = new SimpleDateFormat("yyyyMMdd", Calendario.getDefaultLocale());
                    String dataString = formatador.format(dataClone);
                    sql.append("arquivo ilike '%" + dataString + "_CIELO%' \n");
                    if((i + 1) <= intervalo){
                        sql.append("or ");
                    }
                    dataClone = Calendario.somarDias(dataClone, 1);
                }
                sql.append(") \n");
                sql.append(condicaoConvenioAtivo);
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                //Apagar dados Rede Arquivo
                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append("where ( \n");
                intervalo = Calendario.diferencaEmDias(getReprocessarAPartirDe(), getReprocessarAte());
                dataClone = new Date(getReprocessarAPartirDe().getTime());
                for (int i = 0; i <= intervalo; i++){
                    SimpleDateFormat formatador = new SimpleDateFormat("ddMMyyyy", Calendario.getDefaultLocale());
                    String dataString = formatador.format(dataClone);
                    sql.append("arquivo ilike '%" + dataString + "%' \n");
                    if((i + 1) <= intervalo){
                        sql.append("or ");
                    }
                    dataClone = Calendario.somarDias(dataClone, 1);
                }
                sql.append(") \n");
                sql.append(" and (arquivo ilike '%EEFI%' or arquivo ilike '%EEVC%' or arquivo ilike '%EEVD%') \n");
                sql.append(condicaoConvenioAtivo);
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                //Apagar dados Stone
                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append(" where arquivo in (select arquivo from extratodiarioitem where cast(substring(arquivo, 32, 8) as date) >= '"+ Uteis.getDataFormatoBD(getReprocessarAPartirDe())+ "'");
                sql.append("   and cast(substring(arquivo, 32, 8) as date) <= '" + Uteis.getDataFormatoBD(getReprocessarAte()) + "'");
                sql.append(condicaoStoneOnline);
                sql.append(condicaoConvenioAtivo);
                sql.append(")");
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                //Apagar dados Stone v5
                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append(" where arquivo in (select arquivo from extratodiarioitem where cast(substring(arquivo, 34, 8) as date) >= '"+ Uteis.getDataFormatoBD(getReprocessarAPartirDe())+ "'");
                sql.append("   and cast(substring(arquivo, 34, 8) as date) <= '" + Uteis.getDataFormatoBD(getReprocessarAte()) + "'");
                sql.append(condicaoStoneOnlineV5);
                sql.append(condicaoConvenioAtivo);
                sql.append(")");
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                //Apagar dados ERede
                Date dataCloneVendaDe = new Date(getReprocessarAPartirDe().getTime());
                Date dataCloneVendaAte = new Date(getReprocessarAte().getTime());
                dataCloneVendaDe = Calendario.subtrairDias(dataCloneVendaDe, 1);
                dataCloneVendaAte = Calendario.subtrairDias(dataCloneVendaAte, 1);
                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append(" where codigo in ( \n");
                sql.append("    select codigo from extratodiarioitem where ( \n");
                sql.append("      (cast(substring(arquivo, 25, 8) as date) >= '" + Uteis.getDataFormatoBD(getReprocessarAPartirDe()) + "' \n");
                sql.append("      and cast(substring(arquivo, 25, 8) as date) <= '" + Uteis.getDataFormatoBD(getReprocessarAte()) + "' \n");
                sql.append("      and tipoconciliacao in (").append(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()).append(",").append(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo()).append(")) \n");
                sql.append("      or \n");
                sql.append("      (cast(substring(arquivo, 25, 8) as date) >= '" + Uteis.getDataFormatoBD(dataCloneVendaDe) + "' \n");
                sql.append("      and cast(substring(arquivo, 25, 8) as date) <= '" + Uteis.getDataFormatoBD(dataCloneVendaAte) + "' \n");
                sql.append("      and tipoconciliacao in (").append(TipoConciliacaoEnum.VENDAS.getCodigo()).append(",").append(TipoConciliacaoEnum.CANCELAMENTO.getCodigo()).append(")) \n");
                sql.append("    ) \n");
                sql.append("    and arquivo ilike 'ERedeServiceConciliacao%' \n");
                sql.append(condicaoConvenioAtivo);
                sql.append(");");
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                //Apagar dados PagoLivre
                sql = new StringBuilder();
                sql.append("delete from extratodiarioitem \n");
                sql.append("where codigo in ( \n");
                sql.append("    select codigo from extratodiarioitem where ( \n");
                sql.append("      (cast(substring(arquivo, 22, 8) as date) >= '" + Uteis.getDataFormatoBD(getReprocessarAPartirDe()) + "' \n");
                sql.append("      and cast(substring(arquivo, 22, 8) as date) <= '" + Uteis.getDataFormatoBD(getReprocessarAte()) + "' \n");
                sql.append("      and tipoconciliacao in (").append(TipoConciliacaoEnum.PAGAMENTOS.getCodigo()).append(",").append(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo()).append(",");
                sql.append(         TipoConciliacaoEnum.VENDAS.getCodigo()).append(",").append(TipoConciliacaoEnum.CANCELAMENTO.getCodigo()).append(",");
                sql.append(         TipoConciliacaoEnum.CHARGEBACK.getCodigo()).append(") \n");
                sql.append("      ) \n");
                sql.append("    ) \n");
                sql.append("    and arquivo ilike 'PagoLivreConciliacao|%' \n");
                sql.append(condicaoConvenioAtivo);
                sql.append(");");
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);
            }
            c.commit();
            c.close();
            ExtratoDiarioService extService = new ExtratoDiarioService();
            extService.processarExtratos(chave, getReprocessarAPartirDe(), getReprocessarAte(), true, reprocessarConciliacaoAutomatica, "Manual");
        }catch(Exception e){
            throw new Exception(e);
        }finally {
            if (c != null) {
                c.close();
            }
        }
    }

    public Date getReprocessarAPartirDe() {
        return reprocessarAPartirDe;
    }

    public void setReprocessarAPartirDe(Date reprocessarAPartirDe) {
        this.reprocessarAPartirDe = reprocessarAPartirDe;
    }

    public Date getReprocessarAte() {
        return reprocessarAte;
    }

    public void setReprocessarAte(Date reprocessarAte) {
        this.reprocessarAte = reprocessarAte;
    }

    public static void main(String[] args){
        try{
            String chave = "";
            Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            Date reprocessarApartir = new Date("2019/09/01");
            ReprocessarExtratos reprocessarExtratos = new ReprocessarExtratos(reprocessarApartir, null);
            reprocessarExtratos.reprocessarExtratos(chave);
        }catch(Exception e){
            e.getStackTrace();
        }
    }
}

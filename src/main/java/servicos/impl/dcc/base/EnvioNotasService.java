package servicos.impl.dcc.base;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

public class EnvioNotasService {

    public static void main(String... args) {
        NotaFiscal notaFiscalDAO = null;
        try {
            Uteis.logar(null, "ENVIO DE NOTAS | INICIO...");

            String chave = null;
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            Uteis.debug = true;
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);

            notaFiscalDAO = new NotaFiscal(con);
            notaFiscalDAO.enviarNotasAguardando();
        } catch (Exception ex) {
            ex.printStackTrace();
            Logger.getLogger(EnvioNotasService.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            notaFiscalDAO = null;
            Uteis.logar(null, "ENVIO DE NOTAS | FIM...");
        }
    }
}

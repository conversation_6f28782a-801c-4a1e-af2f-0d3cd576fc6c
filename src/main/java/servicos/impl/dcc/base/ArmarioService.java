package servicos.impl.dcc.base;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.armario.AluguelArmarioVO;
import negocio.armario.OperacaoArmarioEnum;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.armario.ProrataArmario;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Rafael on 21/10/2015.
 */
public class ArmarioService extends SuperEntidade {

    private StringBuffer sb = new StringBuffer();
    private ConfiguracaoSistemaVO config;
    private Date dia;
    private String key = "";

    public ArmarioService(Connection con) throws Exception{
        super(con);
        Conexao.guardarConexaoForJ2SE(con);
        Uteis.logar(sb, "Conexão utilizada: " + con);
        config = getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1,Uteis.NIVELMONTARDADOS_TODOS);
    }
    public static void main(String... args) {
        try {
            Connection con = new DAO().obterConexaoEspecifica("sereia");
            ArmarioService armarioService = new ArmarioService(con);
            armarioService.renovarArmarioAutomaticamente(Calendario.hoje());
        } catch (Exception ex) {
            Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    public int numeroParcelasVenda(VendaAvulsaVO venda){

        int total = 0;
        for(MovParcelaVO parcela : venda.getMovParcelaVOs()){
            if(!parcela.getDescricao().toUpperCase().contains("PRO-RATA"))
                total++;
        }
        return total;
    }
    public void renovarArmarioAutomaticamente() throws Exception {

        List<AluguelArmarioVO> aluguelArmario = getFacade().getArmario().consultarAluguelPorDataFim(dia);
        for(AluguelArmarioVO aluguel : aluguelArmario){
            //Verifica se realmente é o dia do fim do Armario ////////////////////////////////////////////////////////////
            if(Calendario.maior(dia,aluguel.getFimOriginal())) {
               Date diaInicio = Uteis.somarDias(dia, 1);
               Date diaFinal = Uteis.somarDias(diaInicio, aluguel.getProduto().getNrDiasVigencia());
               int numeroVezes = numeroParcelasVenda(aluguel.getVendaAvulsa());
               aluguel.getVendaAvulsa().setNrVezesParcelamento(numeroVezes);
               Date diaVencimento = aluguel.getVendaAvulsa().getMovParcelaVOs().isEmpty()? dia: aluguel.getVendaAvulsa().getMovParcelaVOs().get(0).getDataVencimento();

               aluguel.setDataRenovacaoAutomatica(dia);
               aluguel.getArmario().setAluguelAtual(aluguel);
               getFacade().getArmario().aluguelRenovado(aluguel.getArmario());
               Date dataFimAluguel = aluguel.getFimOriginal();
               ProrataArmario prorataArmario = inicializarProrata(diaVencimento,dia);
               VendaAvulsaVO vendaRenovar = prorataArmario.montarVenda(aluguel, aluguel.getVendaAvulsa().getResponsavel(),aluguel.getProduto(), numeroVezes, diaVencimento);
               vendaRenovar.setVencimentoPrimeiraParcela(Uteis.obterDataFuturaParcela(dataFimAluguel,1));
               aluguel.setVendaAvulsa(vendaRenovar);
               processarArmario(prorataArmario, aluguel, vendaRenovar);
                aluguel.setRenovarAutomatico(true);
               aluguel.getRelacionamentoRenovacao().setCodigo(aluguel.getCodigo());
               getFacade().getArmario().alugarArmarioProrata(aluguel.getArmario(), aluguel, vendaRenovar, diaInicio,
                       diaFinal, numeroVezes, diaVencimento,dia);
               getFacade().getArmario().gerarHistoricoOperacao(aluguel.getCodigo(),aluguel.getArmario().getCodigo(), OperacaoArmarioEnum.RENOVACAO.getDescricao(),OperacaoArmarioEnum.RENOVACAO,aluguel.getResponsavelCadastro().getCodigo());
               Uteis.logar(null, "VENDA RENOVADA : " + aluguel.getCliente().getPessoa() + "\nArmario:" + aluguel.getArmario().getNumeracao());
               Uteis.logar(null, "Inicio:" + Uteis.getData(dia) + "\nFim: " + Uteis.getData(aluguel.getFimOriginal()));
               Uteis.logar(null, "-----------------------------------");
           }
        }
    }
    public ProrataArmario inicializarProrata(Date vencimentoPrimeiraParcela,Date dataInicio) throws Exception{
        ProrataArmario prorataArmario = new ProrataArmario();
        prorataArmario.setConfiguracaoSistemaVO(config);
        prorataArmario.setDataInicio(dataInicio);
        prorataArmario.setHoje(dataInicio);
        prorataArmario.setDiaReferenciaProrata(Uteis.getDiaMesData(vencimentoPrimeiraParcela));
        prorataArmario.verificarConfiguracoesSistema();
        return prorataArmario;
    }
    public void processarArmario(ProrataArmario prorataArmario,AluguelArmarioVO aluguel,VendaAvulsaVO venda) throws Exception{
        prorataArmario.setNumeroParcelas(venda.getNrVezesParcelamento());
        prorataArmario.setVenda(venda);
        prorataArmario.processarParcelas(aluguel);
        aluguel.setVendaAvulsa(prorataArmario.getVenda());
    }
    public void renovarArmarioAutomaticamente(Date dia){
        try {
            this.dia = dia;
            if (config.getHabilitarGestaoArmarios()){
                renovarArmarioAutomaticamente();
            Uteis.logar(null, "Terminou com Sucesso Renovação Armários !");
            }else{
                Uteis.logar(null, "Armários não Renovados : Não possui Configuração Sistema Habilitada!");
            }
        }catch (Exception erro){
            Uteis.logar(null, "Ocorreu um problema ..." + erro.getMessage());
        }

    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ProcessoColocarBoletoPJBankPago extends SuperEntidade {

    public ProcessoColocarBoletoPJBankPago() throws Exception {
    }

    public static void main(String... args) {
        Boleto boletoDAO;
        try {
            Uteis.debug = true;

            String chave = "e82cdafdf149e399e18ae3eb096bd873";
            Integer boleto = 51;
            Connection con = DriverManager.getConnection("**************************************************************************************", "postgres", "pactodb");

            Uteis.logarDebug("ProcessoColocarBoletoPJBankPago | Chave: " + chave);
            Uteis.logarDebug("ProcessoColocarBoletoPJBankPago | Boleto: " + boleto);

            Conexao.guardarConexaoForJ2SE(chave, con);

            boletoDAO = new Boleto(con);
            boletoDAO.forcarGerarPagamentoBoleto(boleto, null);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoColocarBoletoPJBankPago.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            boletoDAO = null;
        }
    }
}

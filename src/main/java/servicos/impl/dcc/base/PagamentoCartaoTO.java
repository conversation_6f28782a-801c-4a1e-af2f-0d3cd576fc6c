package servicos.impl.dcc.base;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;

import java.util.ArrayList;
import java.util.List;

public class PagamentoCartaoTO {

    private PessoaVO pessoaPagador;
    private AutorizacaoCobrancaVO autorizacaoCobrancaVO;
    private EmpresaVO empresaVO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private UsuarioVO usuarioVO;
    private Integer nrParcelasOperadora;
    private List<MovParcelaVO> parcelas;
    //identifica se a transação é automática
    private boolean transacaoPresencial = false;
    private String tipoParcelamentoStone;
    private boolean retentativaManual = false;
    private OrigemCobrancaEnum origemCobranca;
    private boolean async = false;
    private String ipCliente;

    public PessoaVO getPessoaPagador() {
        if (pessoaPagador == null) {
            pessoaPagador = new PessoaVO();
        }
        return pessoaPagador;
    }

    public void setPessoaPagador(PessoaVO pessoaPagador) {
        this.pessoaPagador = pessoaPagador;
    }

    public List<MovParcelaVO> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<>();
        }
        return parcelas;
    }

    public void setParcelas(List<MovParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public AutorizacaoCobrancaVO getAutorizacaoCobrancaVO() {
        return autorizacaoCobrancaVO;
    }

    public void setAutorizacaoCobrancaVO(AutorizacaoCobrancaVO autorizacaoCobrancaVO) {
        this.autorizacaoCobrancaVO = autorizacaoCobrancaVO;
    }

    public Double valorTotal() {
        Double valorTotal = 0.0;
        for (MovParcelaVO movParcelaVO : getParcelas()) {
            valorTotal += movParcelaVO.getValorParcela();
        }
        return valorTotal;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public Integer getNrParcelasOperadora() {
        return nrParcelasOperadora;
    }

    public void setNrParcelasOperadora(Integer nrParcelasOperadora) {
        this.nrParcelasOperadora = nrParcelasOperadora;
    }

    public boolean isTransacaoPresencial() {
        return transacaoPresencial;
    }

    public void setTransacaoPresencial(boolean transacaoPresencial) {
        this.transacaoPresencial = transacaoPresencial;
    }

    public String getTipoParcelamentoStone() {
        return tipoParcelamentoStone;
    }

    public void setTipoParcelamentoStone(String tipoParcelamentoStone) {
        this.tipoParcelamentoStone = tipoParcelamentoStone;
    }

    public boolean isRetentativaManual() {
        return retentativaManual;
    }

    public void setRetentativaManual(boolean retentativaManual) {
        this.retentativaManual = retentativaManual;
    }

    public OrigemCobrancaEnum getOrigemCobranca() {
        if (origemCobranca == null) {
            origemCobranca = OrigemCobrancaEnum.NENHUM;
        }
        return origemCobranca;
    }

    public void setOrigemCobranca(OrigemCobrancaEnum origemCobranca) {
        this.origemCobranca = origemCobranca;
    }

    public boolean isAsync() {
        return async;
    }

    public void setAsync(boolean async) {
        this.async = async;
    }

    public String getIpCliente() {
        if (ipCliente == null) {
            ipCliente = "";
        }
        return ipCliente;
    }

    public void setIpCliente(String ipCliente) {
        this.ipCliente = ipCliente;
    }
}

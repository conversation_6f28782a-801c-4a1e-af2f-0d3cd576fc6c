/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.apf.APF;
import servicos.impl.boleto.BancoEnum;
import servicos.impl.boleto.LayoutBoletoPadrao;
import servicos.impl.boleto.daycoval.LayoutRemessaDaycovalBoletoCNAB400;
import servicos.impl.dcc.bb.LayoutRemessaBBDCO;
import servicos.impl.dcc.bin.LayoutRemessaBinDCC;
import servicos.impl.dcc.bradesco.LayoutRemessaBradescoCNAB150;
import servicos.impl.dcc.bradesco.LayoutRemessaBradescoCNAB240;
import servicos.impl.dcc.caixa.LayoutRemessaCaixaSIACC150DCO;
import servicos.impl.dcc.caixa.LayoutRemessaCaixaDCOTipoSICOV;
import servicos.impl.dcc.cielo.LayoutRemessaCieloDCC;
import servicos.impl.dcc.getnet.LayoutRemessaGetNetDCO;
import servicos.impl.dcc.hsbc.LayoutRemessaHSBCDCO;
import servicos.impl.dcc.itau.LayoutRemessaItauBoletoCNAB400;
import servicos.impl.dcc.itau.LayoutRemessaItauCNAB400BoletoAtualizado;
import servicos.impl.dcc.itau.LayoutRemessaItauDCO;
import servicos.impl.dco.febraban.LayoutRemessaFebrabanDCO;
import servicos.impl.dco.santander.LayoutRemessaSantanderDCO;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class LayoutRemessaBase extends SuperTO {

    private static final long serialVersionUID = -2472588970519813340L;

    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();

//        sb.append("<table>");

        //HEADER
        sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuilder sbDetail = new StringBuilder();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer()).append("\r\n");
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer());
        sb.append("\r\n");

//        sb.append("</table>");

        return sb;
    }

    public void lerHeaderETrailerRemessa(RemessaVO remessa) throws IOException, ConsistirException {
        //Header
        RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
        h.setAtributos(Uteis.getAtributosFromStringBuffer(remessa.getHead()));
        //Trailer
        RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        t.setAtributos(Uteis.getAtributosFromStringBuffer(remessa.getTrailer()));
        //
        remessa.setHeaderRemessa(h);
        remessa.setTrailerRemessa(t);

        if (remessa.getRetorno().length() <= 2 || remessa.isCancelamento()) {
            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
            for (ObjetoGenerico objetoGenerico : atributos) {
                preencherAtributosTransientes(remessa, objetoGenerico);
            }
            for (String atributo : remessa.getProps().keySet()) {
                ObjetoGenerico objetoGenerico = new ObjetoGenerico(atributo, remessa.getProps().get(atributo));
                preencherAtributosTransientes(remessa, objetoGenerico);
            }
        }
    }

    public static void validarArquivoRemessaRetorno(RemessaVO remessa) throws ConsistirException {
        RegistroRemessa hRem = remessa.getHeaderRemessa();
        RegistroRemessa hRet = remessa.getHeaderRetorno();
        if (!hRet.getAtributos().isEmpty()) {
            List<ObjetoGenerico> attrHeader = hRem.getAtributos();
            //Analisar Header
            for (int i = 0; i < attrHeader.size(); i++) {
                ObjetoGenerico obj = attrHeader.get(i);
                if (!ignorarAtributo(obj.getAtributo(), TipoRegistroEnum.HEADER, remessa.getTipo())) {
                    if (!obj.getValor().equals(hRet.getAtributos().get(i).getValor())) {
                        throw new ConsistirException(String.format("Erro no Registro Header, atributo %s está diferente do Retorno: REM: %s RET: %s",
                                obj.getAtributo(),
                                obj.getValor(),
                                hRet.getAtributos().get(i).getValor()));
                    }
                }
            }
            //Analisar Detalhe
            List<RegistroRemessa> detailsRemessa = remessa.getDetailsRemessa();
            List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
            for (int i = 0; i < detailsRemessa.size(); i++) {
                //verificar se parcela foi estornada
                if (remessa.getListaItens().get(i).getMovParcela().getCodigo() == 0) {
                    //item estornado
                    continue;
                }
                RegistroRemessa regRemessa = detailsRemessa.get(i);
                List<ObjetoGenerico> attrRemessa = regRemessa.getAtributos();

                for (int j = 0; j < attrRemessa.size(); j++) {
                    ObjetoGenerico attRem = attrRemessa.get(j);
                    if (!ignorarAtributo(attRem.getAtributo(), TipoRegistroEnum.DETALHE, remessa.getTipo())) {
                        ObjetoGenerico attRet = detailsRetorno.get(i).getAtributos().get(j);
                        if (attRem.getAtributo().equals(DCCAttEnum.NumeroComprovanteVenda.name())) {
                            if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                                continue;
                            }
                            int tam = remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU) ? 15 : 7;

                            String codigoParcela = StringUtilities.formatarCampo(new BigDecimal(remessa.getListaItens().get(i).getMovParcela().getCodigo()), tam);
                            if (!attRet.getValor().equals(codigoParcela)) {
                                //validar o código de parcela!!!
                                throw new ConsistirException(String.format("Erro no Registro Detalhe na linha %s. "
                                        + "Atributo %s está diferente do Retorno na mesma linha. "
                                        + "Na remessa lido \"%s\", no retorno \"%s\"",
                                        i + 1, attRem.getAtributo(), attRem.getValor(), codigoParcela));

                            }
                        }
                        if (!attRem.getValor().equals(attRet.getValor())) {
                            throw new ConsistirException(String.format("Erro no Registro Detalhe na linha %s. "
                                    + "Atributo %s está diferente do Retorno na mesma linha. "
                                    + "Na remessa lido \"%s\", no retorno \"%s\"",
                                    i + 1, attRem.getAtributo(), attRem.getValor(), attRet.getValor()));
                        }
                    }
                }
            }
            //Analisar Trailer
            RegistroRemessa tRem = remessa.getTrailerRemessa();
            RegistroRemessa tRet = remessa.getTrailerRetorno();
            List<ObjetoGenerico> attrTrailer = tRem.getAtributos();
            for (int i = 0; i < attrTrailer.size(); i++) {
                ObjetoGenerico obj = attrTrailer.get(i);
                preencherAtributosTransientes(remessa, tRet.getAtributos().get(i));
                if (!ignorarAtributo(obj.getAtributo(), TipoRegistroEnum.TRAILER, remessa.getTipo())) {
                    if (!obj.getValor().equals(tRet.getAtributos().get(i).getValor())) {
                        throw new ConsistirException(String.format("Erro no Registro Trailer, atributo %s está diferente do Retorno: REM: %s RET: %s",
                                obj.getAtributo(),
                                obj.getValor(),
                                tRet.getAtributos().get(i).getValor()));
                    }
                }
            }

            // Analisando código das autorizacoes.
           if(remessa.getDetailsRetorno() != null && !remessa.getDetailsRetorno().isEmpty()){
                int i = 1;
                for(RegistroRemessa details : remessa.getDetailsRetorno()){
                    String codigoAutorizacao = details.get(DCCAttEnum.CodigoAutorizacao.name());
                    if(!UteisValidacao.emptyString(codigoAutorizacao) && !codigoAutorizacao.trim().isEmpty() && !codigoAutorizacao.trim().replaceAll("[0-9a-zA-Z=]", "").isEmpty()){
                        throw new ConsistirException(String.format("Erro no Registro Detalhe na linha %s. "
                                + "Atributo CodigoAutorização inválido: %s", i , codigoAutorizacao));
                    }
                    i++;
                }
            }
        }
    }

    public static void preencherAtributosTransientes(RemessaVO remessa, final ObjetoGenerico obj) {
        if (obj.getAtributo().equals(DCCAttEnum.ValorTotalBruto.name())) {
            Double d = new Double(obj.getValor().substring(0, obj.getValor().length() - 2) + "." + obj.getValor().substring(obj.getValor().length() - 2));
            remessa.setValorBruto(d);
        } else if (obj.getAtributo().equals(DCCAttEnum.ValorTotalAceito.name())) {
            Double d = new Double(obj.getValor().substring(0, obj.getValor().length() - 2) + "." + obj.getValor().substring(obj.getValor().length() - 2));
            remessa.setValorAceito(d);
        } else if (obj.getAtributo().equals(DCCAttEnum.ValorTotalLiquido.name())) {
            Double d = new Double(obj.getValor().substring(0, obj.getValor().length() - 2) + "." + obj.getValor().substring(obj.getValor().length() - 2));
            remessa.setValorLiquido(d);
        } else if (obj.getAtributo().equals(DCCAttEnum.QuantidadeRegistros.name())) {
            BigDecimal n = new BigDecimal(obj.getValor().substring(0, obj.getValor().length()));
            if (remessa.getTipo().equals(TipoRemessaEnum.BOLETO)) {
                if (remessa.getConvenioCobranca().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo()) ||
                        remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())) {
                    if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())) {
                        remessa.setQtdRegistros((n.intValue() - 4) / 3);
                    } else {
                        remessa.setQtdRegistros((n.intValue() - 4) / 2);
                    }
                } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.ITAU.getCodigo()) {
                    remessa.setQtdRegistros((n.intValue() - 2 >= 0) ? n.intValue() - 2 : 0);
                } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.SANTANDER.getCodigo()){
                    remessa.setQtdRegistros(n.intValue() - 2);
                }
                else {
                    remessa.setQtdRegistros(n.intValue());
                }
            } else if(remessa.getTipo().equals(TipoRemessaEnum.DCC_BIN)){
                remessa.setQtdRegistros(n.intValue());
            } else {
                remessa.setQtdRegistros((n.intValue() - 2 >= 0) ? n.intValue() - 2 : 0);
            }
        }else if(obj.getAtributo().equals(DCCAttEnum.SequencialRegistro.name())){
            if (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.SICREDI.getCodigo()){
                remessa.setQtdRegistros(Integer.parseInt(obj.getValor()));
            }
        }
        else if (obj.getAtributo().equals(DCCAttEnum.DataPrevistaCredito.name())) {
            try {
                remessa.setDataPrevistaCredito(obj.getValor() == null || obj.getValor().isEmpty() || obj.getValor().equals("00000000") ? null : Calendario.getDate("ddMMyyyy", obj.getValor()));
            } catch (ParseException ex) {
                Logger.getLogger(LayoutRemessaBase.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

    }

    private static boolean ignorarAtributo(final String nomeAtributo, TipoRegistroEnum tipoReg, TipoRemessaEnum tipo) {
        if (tipoReg == TipoRegistroEnum.HEADER) {
            return nomeAtributo.equals(DCCAttEnum.CodigoRemessa.name())
                    || nomeAtributo.equals(DCCAttEnum.ReservadoCielo.name());
        } else if (tipoReg == TipoRegistroEnum.DETALHE) {
            return (nomeAtributo.equals(DCCAttEnum.CodigoAutorizacao.name())
                    || nomeAtributo.equals(DCCAttEnum.ValorParcela.name())
                    || nomeAtributo.equals(DCCAttEnum.TipoRegistro.name())
                    || nomeAtributo.equals(DCCAttEnum.Moeda.name())
                    || nomeAtributo.equals(DCCAttEnum.NomePessoa.name())
                    || nomeAtributo.equals(DCCAttEnum.ReservadoCielo.name())
                    || nomeAtributo.equals(DCCAttEnum.ReservadoEstabelecimento.name())
                    || nomeAtributo.equals(DCCAttEnum.StatusVenda.name())
                    || nomeAtributo.equals(DCCAttEnum.CVV2.name())
                    || nomeAtributo.equals(DCCAttEnum.DataPrevistaCredito.name())
                    || nomeAtributo.equals(DCCAttEnum.CodigoErro.name())
                    || nomeAtributo.equals(DCCAttEnum.EmBranco.name())
                    || nomeAtributo.equals(DCCAttEnum.EmBranco2.name())
                    || nomeAtributo.equals(DCCAttEnum.IOF.name()))
                    || (nomeAtributo.equals(DCCAttEnum.NumeroComprovanteVenda.name())
                    || nomeAtributo.equals(DCCAttEnum.NumeroCartao.name())
                    || nomeAtributo.equals(DCCAttEnum.ValidadeCartao.name())
                    || nomeAtributo.equals(DCCAttEnum.NumeroReferencia.name())
                    || nomeAtributo.equals(DCCAttEnum.NumeroProtocolo.name())
                    && tipo != null && tipo.equals(TipoRemessaEnum.GET_NET));
        } else if (tipoReg == TipoRegistroEnum.TRAILER) {
            return nomeAtributo.equals(DCCAttEnum.ValorTotalAceito.name())
                    || nomeAtributo.equals(DCCAttEnum.ValorTotalLiquido.name())
                    || nomeAtributo.equals(DCCAttEnum.ValorTotalBruto.name())
                    || nomeAtributo.equals(DCCAttEnum.DataPrevistaCredito.name());
        }

        return false;
    }

    public static void preencherArquivoRemessa(RemessaVO remessa) throws Exception {
        preencherArquivoRemessa(remessa, false);
    }

    public static void preencherArquivoRemessa(RemessaVO remessa, boolean utilizarProps) throws Exception {

        if (remessa.getConvenioCobranca().getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            if (!UteisValidacao.emptyList(remessa.getListaItens())) {

                Map<String, NazgDTO> mapaAragorn = new HashMap<>();
                if (!utilizarProps) {

                    List<String> listaToken = new ArrayList<>();
                    for (RemessaItemVO item : remessa.getListaItens()) {
                        String tokenAragorn = item.get(APF.TokenAragorn);
                        if (!UteisValidacao.emptyString(tokenAragorn)) {
                            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                                tokenAragorn = "cde730a96771663c4484f2ed7e9198cb";
                            }
                            listaToken.add(tokenAragorn);
                        }
                    }

                    AragornService aragornService = new AragornService();
                    mapaAragorn = aragornService.obterMapaNazg(listaToken);
                    aragornService = null;
                }

                for (RemessaItemVO item : remessa.getListaItens()) {

                    if (utilizarProps) {
                        //não tem necessidade de ir no aragorn para processar retorno de DCC
                        //pegar as informações que tem no props
                        //by Luiz Felipe 29/04/2020
                        String props = item.getProps().toString();
                        String mesValidade = LayoutRemessaBase.obterValorCampoProps(APF.MesValidade, props);
                        String anoValidade = LayoutRemessaBase.obterValorCampoProps(APF.AnoValidade, props);
                        String cartaoMascarado = LayoutRemessaBase.obterValorCampoProps(APF.CartaoMascarado, props);

                        NazgDTO nazgDTO = new NazgDTO();

                        //replica o * para não dar erro ao processar o arquivo "new BigDecimal"
                        nazgDTO.setCard(cartaoMascarado.replace("*", "9"));
                        nazgDTO.setMonth(Integer.parseInt(mesValidade));
                        nazgDTO.setYear(Integer.parseInt(anoValidade));
                        item.setNazgDTO(nazgDTO);

                    } else {
                        String tokenAragorn = item.get(APF.TokenAragorn);
                        if (tokenAragorn == null) {
                            item.setNazgDTO(new NazgDTO(item));
                        } else if (!UteisValidacao.emptyString(tokenAragorn)) {
                            if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                                tokenAragorn = "cde730a96771663c4484f2ed7e9198cb";
                            }
                            NazgDTO nazgDTO = mapaAragorn.get(tokenAragorn);
                            if (nazgDTO == null) {
                                throw new Exception("Dados não encontrados. Token " + tokenAragorn);
                            }
                            item.setNazgDTO(nazgDTO);
                        }
                    }
                }
            }
        }

        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {
            LayoutRemessaCieloDCC.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) {
            LayoutRemessaItauDCO.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
            LayoutRemessaCaixaSIACC150DCO.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA_SICOV)) {
            LayoutRemessaCaixaDCOTipoSICOV.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_HSBC)) {
            LayoutRemessaHSBCDCO.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            LayoutRemessaGetNetDCO.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU)) {
            LayoutRemessaItauBoletoCNAB400.preencherArquivoRemessa(remessa, false);
        }else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.ITAU)) {
            LayoutRemessaItauCNAB400BoletoAtualizado.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
            LayoutBoletoPadrao.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            LayoutRemessaBinDCC.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_SANTANDER)) {
            LayoutRemessaSantanderDCO.preencherArquivoRemessa(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
            LayoutRemessaBradescoCNAB240.preencherArquivoRemessa(remessa);
        }else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)) {
            LayoutRemessaDaycovalBoletoCNAB400.preencherArquivoRemessa(remessa);
        }else if (remessa.getConvenioCobranca().isLayoutFebrabanDCO()) {
            LayoutRemessaFebrabanDCO.preencherArquivoRemessa(remessa);
        }
    }

    public static void preencherArquivoRemessaCancelamento(RemessaVO remessa) throws Exception {
        preencherArquivoRemessaCancelamento(remessa, false);
    }

    public static void preencherArquivoRemessaCancelamento(RemessaVO remessa, boolean utilizarProps) throws Exception {
        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {
            if (utilizarProps) {
                povoarDadosCartaoUtilizandoProps(remessa);
            } else {
                povoarDadosCartaoAragorn(remessa);
            }
            LayoutRemessaCieloDCC.preencherArquivoRemessaCancelamento(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            LayoutRemessaGetNetDCO.preencherArquivoRemessaCancelamento(remessa);
        }
    }


    private static void povoarDadosCartaoUtilizandoProps(RemessaVO remessa) throws Exception {
        for (RemessaCancelamentoItemVO cancelamentoItemVO : remessa.getListaItensCancelamento()) {
            RemessaItemVO item = cancelamentoItemVO.getItemRemessaCancelar();
            //não tem necessidade de ir no aragorn para processar retorno de DCC
            //pegar as informações que tem no props
            //by Luiz Felipe 29/04/2020
            String props = item.getProps().toString();
            String mesValidade = LayoutRemessaBase.obterValorCampoProps(APF.MesValidade, props);
            String anoValidade = LayoutRemessaBase.obterValorCampoProps(APF.AnoValidade, props);
            String cartaoMascarado = LayoutRemessaBase.obterValorCampoProps(APF.CartaoMascarado, props);

            NazgDTO nazgDTO = new NazgDTO();

            //replica o * para não dar erro ao processar o arquivo "new BigDecimal"
            nazgDTO.setCard(cartaoMascarado.replace("*", "9"));
            nazgDTO.setMonth(Integer.parseInt(mesValidade));
            nazgDTO.setYear(Integer.parseInt(anoValidade));
            item.setNazgDTO(nazgDTO);
        }
    }

    private static void povoarDadosCartaoAragorn(RemessaVO remessa) throws Exception {

        List<String> listaToken = new ArrayList<>();
        for (RemessaCancelamentoItemVO cancelamentoItemVO : remessa.getListaItensCancelamento()) {
            RemessaItemVO item = cancelamentoItemVO.getItemRemessaCancelar();
            String props = item.getProps().toString();
            String tokenAragorn = LayoutRemessaBase.obterValorCampoProps(APF.TokenAragorn, props);
            if (!UteisValidacao.emptyString(tokenAragorn)) {
                if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                    tokenAragorn = "cde730a96771663c4484f2ed7e9198cb";
                }
                listaToken.add(tokenAragorn);
            }
        }

        AragornService aragornService = new AragornService();
        Map<String, NazgDTO> mapaAragorn = aragornService.obterMapaNazg(listaToken);
        aragornService = null;

        for (RemessaCancelamentoItemVO cancelamentoItemVO : remessa.getListaItensCancelamento()) {
            RemessaItemVO item = cancelamentoItemVO.getItemRemessaCancelar();
            String props = item.getProps().toString();
            String tokenAragorn = LayoutRemessaBase.obterValorCampoProps(APF.TokenAragorn, props);
            if (!UteisValidacao.emptyString(tokenAragorn)) {
                if (Uteis.isAmbienteDesenvolvimentoTeste()) {
                    tokenAragorn = "cde730a96771663c4484f2ed7e9198cb";
                }
                NazgDTO nazgDTO = mapaAragorn.get(tokenAragorn);
                if (nazgDTO == null) {
                    throw new Exception("Dados não encontrados. Token " + tokenAragorn);
                }
                item.setNazgDTO(nazgDTO);
            }
        }
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {
            LayoutRemessaCieloDCC.lerRetorno(remessa, true);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) {
            LayoutRemessaItauDCO.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
            LayoutRemessaCaixaSIACC150DCO.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_HSBC)) {
            LayoutRemessaHSBCDCO.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            LayoutRemessaGetNetDCO.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            LayoutRemessaBinDCC.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.ITAU)) {
            LayoutRemessaItauCNAB400BoletoAtualizado.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().isLayoutFebrabanDCO()) {
            if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_SANTANDER)) {
                LayoutRemessaSantanderDCO.lerRetorno(remessa);
            } else {
                LayoutRemessaFebrabanDCO.lerRetorno(remessa);
            }
        }
    }

    public static void lerRetornoCancelamento(RemessaVO remessa) throws IOException, ConsistirException {
        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)) {
            LayoutRemessaCieloDCC.lerRetorno(remessa, false);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            LayoutRemessaGetNetDCO.lerRetorno(remessa);
        }
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno,
            final TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws IOException {
        if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCC) {
            return LayoutRemessaCieloDCC.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCC_GETNET) {
            return LayoutRemessaGetNetDCO.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_BB) {
            return LayoutRemessaBBDCO.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_BRADESCO) {
            return LayoutRemessaBradescoCNAB150.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_ITAU) {
            return LayoutRemessaItauDCO.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_CAIXA) {
            return LayoutRemessaCaixaSIACC150DCO.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_CAIXA_SICOV) {
            return LayoutRemessaCaixaDCOTipoSICOV.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_HSBC) {
            return LayoutRemessaHSBCDCO.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.BOLETO_ITAU) {
            return LayoutRemessaItauBoletoCNAB400.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL) {
            return LayoutRemessaDaycovalBoletoCNAB400.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCC_BIN) {
            return LayoutRemessaBinDCC.obterHeaderRetorno(retorno);
        } else if (tipoConvenioCobrancaEnum == TipoConvenioCobrancaEnum.DCO_SANTANDER) {
            return LayoutRemessaSantanderDCO.obterHeaderRetorno(retorno);
        }
        return null;

    }

    public static String obterValorCampoProps(final String nomeCampo, final String props) {
        int ind = props.indexOf(nomeCampo) + nomeCampo.length();
        int to = 0;
        try {
            to = props.indexOf(",", ind + 1);
            if (to == -1) {
                to = props.indexOf("}", ind + 1);
            }
        } catch (Exception ignored) {
        }
        if (to == -1) {
            to = props.length();
        }
        return props.substring(ind + 1, to);
    }

    protected static Date getDataVencimento(Date vencimentoParcela, Date dataBase) {
        return getDataVencimento(vencimentoParcela, dataBase, null);
    }

    public static Date getDataVencimento(Date vencimentoParcela, Date dataBase, RemessaVO remessa) {
        int diasAntecipacao = 5;
        Date dataMinimaVencimento;

        // Dias de antecipação da parcela, em dias corridos
        if(remessa != null){
            if(remessa.getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)){
                if(remessa.getConvenioCobranca().getDiasAntecipacaoRemessaDCO() > 0){
                    diasAntecipacao = remessa.getConvenioCobranca().getDiasAntecipacaoRemessaDCO();
                    dataMinimaVencimento = Calendario.somarDias(dataBase, diasAntecipacao);
                    if(Calendario.maior(vencimentoParcela, dataMinimaVencimento)){
                        return vencimentoParcela;
                    }else{
                        return dataMinimaVencimento;
                    }
                }
            }
        }

        dataMinimaVencimento = Calendario.proximoDiaUtil(dataBase, diasAntecipacao);
        if(Calendario.maior(vencimentoParcela, dataMinimaVencimento)){
            dataMinimaVencimento = vencimentoParcela;
        }

        return dataMinimaVencimento;
    }

    public static void validarDesconto(RemessaItemVO item, RegistroRemessa detail) {
        if(item.possuiDescontoConvenio()){
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(6));
            Double valor = item.getValorBoleto();
            valor = valor * item.getPorcentagemDescontoBoleto() / 100;
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 13));
        }else if (item.possuiDesconto() && item.getDataPagamentoAntecipado() != null) {
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(item.getDataPagamentoAntecipado(), "ddMMyy"));
            Double valor = item.getValorBoleto();
            valor = valor * item.getPorcentagemDescontoBoletoPagAntecipado() / 100;
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 13));
        } else {
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));
        }
    }

    public static String getCPF(RemessaItemVO remessaItem) {
        String cpf = remessaItem.get(DCCAttEnum.CpfCnpjPagador.name());

        if (UteisValidacao.emptyString(cpf)) {
            cpf = remessaItem.get(DCCAttEnum.CpfOuCnpj.name());
        }

        if (UteisValidacao.emptyString(cpf)) {
            boolean menorIdade = false;
            if (remessaItem.getClienteVO().getPessoa().getDataNasc() != null) {
                int idadePessoa = remessaItem.getClienteVO().getPessoa().getIdade();
                menorIdade = idadePessoa < 18;
            }

            if (UteisValidacao.emptyString(cpf) && !menorIdade) {
                cpf = remessaItem.getClienteVO().getPessoa().getCfp();
            }
            if (UteisValidacao.emptyString(cpf)) {
                cpf = remessaItem.getClienteVO().getPessoaResponsavel().getCfp();
            }
            if (UteisValidacao.emptyString(cpf)) {
                cpf = remessaItem.getPessoa().getCfp();
            }

            if (remessaItem.getRemessa().getConvenioCobranca().getEmpresa().isUtilizarNomeResponsavelNoBoleto()
                    && remessaItem.getPessoa().getDataNasc() != null  && menorIdade) {
                if (remessaItem.getClienteVO().getPessoaResponsavel() != null && remessaItem.getClienteVO().getPessoaResponsavel().getCodigo() > 0) {
                    cpf = remessaItem.getClienteVO().getPessoaResponsavel().getCfp();
                } else {
                    if (!UteisValidacao.emptyString(remessaItem.getPessoa().getNomePai())) {
                        cpf = remessaItem.getClienteVO().getPessoa().getCpfPai();
                    } else if (!UteisValidacao.emptyString(remessaItem.getPessoa().getNomeMae())) {
                        cpf = remessaItem.getClienteVO().getPessoa().getCpfMae();
                    }
                }
            }

            cpf = cpf.replaceAll("[.,-]", "");
            cpf = !cpf.isEmpty() && cpf.length() > 11 ? cpf.substring(0, 11) : cpf;
            return cpf;
        } else {
            cpf = cpf.replaceAll("[.,-]", "");
            cpf = !cpf.isEmpty() && cpf.length() > 11 ? cpf.substring(0, 11) : cpf;
            return cpf;
        }
    }

    public static String getCNPJ(RemessaItemVO remessaItem) {
        String cnpj = remessaItem.get(DCCAttEnum.CpfCnpjPagador.name());

        if (UteisValidacao.emptyString(cnpj)) {
            cnpj = remessaItem.get(DCCAttEnum.CpfOuCnpj.name());
        }

        if (UteisValidacao.emptyString(cnpj)) {
            if (UteisValidacao.emptyString(cnpj)) {
                cnpj = remessaItem.getClienteVO().getPessoa().getCnpj();
            }
            if (UteisValidacao.emptyString(cnpj)) {
                cnpj = remessaItem.getClienteVO().getPessoaResponsavel().getCnpj();
            }
            if (UteisValidacao.emptyString(cnpj)) {
                cnpj = remessaItem.getPessoa().getCnpj();
            }
            return cnpj.replaceAll("[.,-]", "");
        } else {
            return cnpj.replaceAll("[.,-]", "");
        }
    }

    public static String getNome(final RemessaItemVO remessaItem, final int tamanho) {
        String nome = remessaItem.get(DCCAttEnum.NomePagador.name());
        if (UteisValidacao.emptyString(nome)) {
            if (remessaItem.getRemessa().getConvenioCobranca().getEmpresa().isUtilizarNomeResponsavelNoBoleto()
                    && remessaItem.getPessoa().getDataNasc() != null  && Uteis.calcularIdadePessoa(Calendario.hoje(), remessaItem.getPessoa().getDataNasc()) < 18) {
                if (remessaItem.getClienteVO().getPessoaResponsavel() != null && remessaItem.getClienteVO().getPessoaResponsavel().getCodigo() > 0) {
                    return StringUtilities.formatarCampoEmBranco(retirarCaracteresInvalidos(remessaItem.getClienteVO().getPessoaResponsavel().getNome()), tamanho);
                } else {
                    if (!UteisValidacao.emptyString(remessaItem.getPessoa().getNomePai())) {
                        return StringUtilities.formatarCampoEmBranco(retirarCaracteresInvalidos(remessaItem.getPessoa().getNomePai()), tamanho);
                    } else if (!UteisValidacao.emptyString(remessaItem.getPessoa().getNomeMae())) {
                        return StringUtilities.formatarCampoEmBranco(retirarCaracteresInvalidos(remessaItem.getPessoa().getNomeMae()), tamanho);
                    }
                }
            }

            return StringUtilities.formatarCampoEmBranco(retirarCaracteresInvalidos(remessaItem.getPessoa().getNome()), tamanho);
        } else {
            return StringUtilities.formatarCampoEmBranco(retirarCaracteresInvalidos(nome), tamanho);
        }
    }

    public static EnderecoVO getEndereco(RemessaItemVO item){
        EnderecoVO enderecoVO = new EnderecoVO();
        for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
            if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                enderecoVO = endereco;
            }
        }
        if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
            if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
            }
        }
        return enderecoVO;
    }

    protected static String retirarCaracteresInvalidos(String text) {
        return retirarCaracteresInvalidos(text, null);
    }

    protected static String retirarCaracteresInvalidos(String text, String[] adicionais) {
        String textoValido = text.replaceAll("'", " ");
        textoValido = textoValido.replaceAll("`", " ");
        textoValido = textoValido.replaceAll("´", " ");
        textoValido = textoValido.replaceAll("_", " ");
        textoValido = textoValido.replaceAll("°", " ");
        textoValido = textoValido.replaceAll("º", " ");
        textoValido = textoValido.replaceAll("\n", " ");
        if (adicionais != null) {
            for (String item : adicionais) {
                textoValido = textoValido.replaceAll(item, " ");
            }
        }
        return textoValido;
    }
}

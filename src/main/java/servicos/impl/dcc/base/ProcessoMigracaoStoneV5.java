package servicos.impl.dcc.base;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCredencialStoneEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> | 28/01/2025
 */
public class ProcessoMigracaoStoneV5 {

    private static StringBuilder logGravar;
    private static List<String> listaChavesOAMDMigrar = new ArrayList<>();
    private static Map<String, String> mapaStoneCodeENovaCredencialSAK = new HashMap<>();


    public static void main(String[] args) throws Exception {
        try {
            adicionarLog("###########################################################################");
            adicionarLog("******INÍCIO PROCESSO MIGRAÇÃO CONVÊNIOS STONE V5******");
            adicionarLog("###########################################################################");
            povoarListaChavesOAMDMigrar();
            povoarMapaStoneCodeENovaCredencialSKGateway();

            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            // Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");

            if (UteisValidacao.emptyList(listaChavesOAMDMigrar)) {
                adicionarLog("Nenhuma chave OAMD informada na lista para migrar!");
                return;
            }
            for (String key : listaChavesOAMDMigrar) {
                adicionarLog("CHAVE: " + key + " | INÍCIO");
                String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                        " \"nomeBD\" as banco, porta, urlintegracaows " +
                        "from empresa " +
                        "where chave = '" + key + "'";

                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
                if (rs.next()) {
                    String chave = rs.getString("chave");
                    String host = rs.getString("host");
                    Integer porta = rs.getInt("porta");
                    String banco = rs.getString("banco");
                    String user = rs.getString("user");
                    String pass = rs.getString("pass");

                    try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                        adicionarLog("CHAVE: " + chave + " | Obter conexão..." + " - " + host + ":" + porta + "/" + banco);
                        processar(chave, con);
                        adicionarLog("CHAVE: " + chave + " | FIM");
                        adicionarLog("---------------------------------------------------------------------------------------------------");
                    } catch (Exception ex) {
                        adicionarLog("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
                        adicionarLog("---------------------------------------------------------------------------------------------------");
                    }
                } else {
                    adicionarLog("Chave OAMD " + key + " não encontrada!");
                    adicionarLog("---------------------------------------------------------------------------------------------------");
                }
            }
        } catch (Exception ex) {
        } finally {
            adicionarLog("******FIM PROCESSO MIGRAÇÃO CONVÊNIOS STONE V5******");
            Uteis.salvarArquivo(ProcessoMigracaoStoneV5.class.getSimpleName() + "_" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    public static void processar(String chave, Connection con) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        List<ConvenioCobrancaVO> convenioStoneExistentes = new ArrayList<>();
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            TipoConvenioCobrancaEnum[] tipos = new TipoConvenioCobrancaEnum[]{TipoConvenioCobrancaEnum.DCC_STONE_ONLINE};
            convenioStoneExistentes = convenioCobrancaDAO.consultarPorTiposESituacaoEAmbiente(tipos, 0, SituacaoConvenioCobranca.ATIVO,
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS, false, AmbienteEnum.PRODUCAO);
            if (UteisValidacao.emptyList(convenioStoneExistentes)) {
                adicionarLog("CHAVE: " + chave + " | Nenhum Convênio STONE PRODUÇÃO ativo!");
                return;
            } else {
                adicionarLog("CHAVE: " + chave + " | ** Encontrado " + convenioStoneExistentes.size() + " convênio(s) de cobrança ativo(s) STONE **");
            }
            for (ConvenioCobrancaVO convenio : convenioStoneExistentes) {
                try {
                    adicionarLog("CHAVE: " + chave + " | Iniciando migração do Convênio cód.: " + convenio.getCodigo() + " | StoneCode: " + convenio.getCodigoAutenticacao01());
                    boolean sucesso = migrarConvenio(convenio, chave, con);
                    if (!sucesso) {
                        adicionarLog("CHAVE: " + chave + " | Não migrou o convênio cód.: " + convenio.getCodigo() + " | StoneCode: " + convenio.getCodigoAutenticacao01());
                    }
                } catch (Exception ex) {
                    adicionarLog("CHAVE: " + chave + " | Erro ao migrar o convênio cód.: " + convenio.getCodigo() + " | StoneCode: " + convenio.getCodigoAutenticacao01() + " | " + ex.getMessage());
                }
            }
        } catch (Exception ex) {
            adicionarLog("CHAVE: " + chave + " | Erro ao processar o convênio" + " | " + ex.getMessage());
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    private static boolean migrarConvenio(ConvenioCobrancaVO convenio, String chave, Connection con) {
        try {
            String novaCredencialSK = mapaStoneCodeENovaCredencialSAK.getOrDefault(convenio.getCodigoAutenticacao01().trim(), "");
            if (UteisValidacao.emptyString(novaCredencialSK)) {
                adicionarLog("CHAVE: " + chave + " | Não foi encontrada nova credencial no mapa de SK's para o StoneCode: " + convenio.getCodigoAutenticacao01());
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append("update conveniocobranca set tipoconvenio = " + TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo() + ", ");
                sb.append(" codigoautenticacao03 = '" + novaCredencialSK + "', ");
                sb.append(" verificacaozerodollar = true, ");
                sb.append(" tipocredencialstoneenum = " + TipoCredencialStoneEnum.GATEWAY.getCodigo());
                sb.append(" where codigo = " + convenio.getCodigo());

                SuperFacadeJDBC.executarUpdate(sb.toString(), con);
                adicionarLog("CHAVE: " + chave + " | SUCESSO Convênio cód.: " + convenio.getCodigo() + " | StoneCode: " + convenio.getCodigoAutenticacao01() + " | MIGRADO COM SUCESSO para Nova credencial SK: " + novaCredencialSK);
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
        return false;
    }


    private static void povoarListaChavesOAMDMigrar() {
        listaChavesOAMDMigrar.add("eng");
        listaChavesOAMDMigrar.add("eng2");
        listaChavesOAMDMigrar.add("bio");
    }

    private static void povoarMapaStoneCodeENovaCredencialSKGateway() {
        mapaStoneCodeENovaCredencialSAK = new HashMap<>();
        mapaStoneCodeENovaCredencialSAK.put("89549409100", "sk_test_420ed84b009f44fc99aabdfa5b2d983d");
        mapaStoneCodeENovaCredencialSAK.put("895494090111", "sk_test_420ed84b009f44fc99aabdfa5b2d983aENG2");
        mapaStoneCodeENovaCredencialSAK.put("1970622045888", "sk_test_420ed84b009f44fc99aabdfa5b2d983vENG1");
    }

    private static void adicionarLog(String msg) {
        String s = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

}

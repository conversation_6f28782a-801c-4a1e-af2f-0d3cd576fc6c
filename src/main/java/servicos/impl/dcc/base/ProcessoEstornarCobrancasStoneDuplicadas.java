package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.Charsets;
import org.json.JSONObject;
import org.json.XML;
import servicos.impl.stone.xml.cancellation.request.DocumentAcceptorCancellationRequestV0_21;
import servicos.impl.stone.xml.cancellation.request.DocumentAcceptorCancellationRequestV0_21Builder;
import servicos.util.ExecuteRequestHttpService;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static javax.ws.rs.core.MediaType.APPLICATION_XML;
import static servicos.util.ExecuteRequestHttpService.METODO_POST;

/**
 * <AUTHOR>
 */
public class ProcessoEstornarCobrancasStoneDuplicadas {

    static StringBuilder textoArquivo = new StringBuilder();
    private static StringBuilder logGravar;
    private static Map<String, String> mapaSakEStoneCode = new HashMap<>();


    public static void main(String[] args) throws Exception {
        try {
            povoarMapa();

            textoArquivo = new StringBuilder("");
            adicionarLog("******INÍCIO PROCESSO ESTORNAR COBRANÇAS DUPLICADAS******");

            String itks = "{ITKS_AQUI_SEPARADOS_POR_VIRGULA}";
            String[] arrayItks = itks.split(",");
            for (String itk : arrayItks) {
                processarITK(itk);
            }

        } catch (Exception ex) {
        } finally {
            adicionarLog("******FIM PROCESSO ESTORNAR COBRANÇAS DUPLICADAS******");
            Uteis.salvarArquivo(ProcessoEstornarCobrancasStoneDuplicadas.class.getSimpleName() + "_" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void povoarMapa() {
        mapaSakEStoneCode = new HashMap<>();
        mapaSakEStoneCode.put("f5f42407d4e34cddae2d465ab656b04e", "120350706");
        mapaSakEStoneCode.put("2e641398644b49b7a9b21b67551215fb", "140374040");
        mapaSakEStoneCode.put("dac42bc1f7004c3cb5f3aa647d7df9dd", "209209393");
        mapaSakEStoneCode.put("9f231b2a6e7c4b60a20c657f7812ae2b", "379098112");
        mapaSakEStoneCode.put("8db9ad8740fa43fd814f330fca58f912", "140496701");
        mapaSakEStoneCode.put("894c70332ba246c88fe42c9a0f751a06", "144115002");
        mapaSakEStoneCode.put("ec1fb7ee04a9453087d54507a155d128", "197384166");
        mapaSakEStoneCode.put("baa6c200a1fe4210a1235a38e83dfa43", "198129763");
        mapaSakEStoneCode.put("63e422a4afbd42f7882c0eda074a379b", "102892576");
        mapaSakEStoneCode.put("ff7a77082f1340079d0b491c2af2cc29", "108963211");
        mapaSakEStoneCode.put("9174b08f6f864472bfc43c58037c788b", "135516897");
        mapaSakEStoneCode.put("4763ea275edb427391f5b59c38f86180", "157790968");
        mapaSakEStoneCode.put("d3d6041c68554c8097cc9aa2f0d900df", "862842257");
        mapaSakEStoneCode.put("645d9c88b1b546fbbf94731350aa59a0", "100905740");
        mapaSakEStoneCode.put("51472a3379a34b2d96cf6ae45213cc4d", "336120820");
        mapaSakEStoneCode.put("8c7b9f959e6e447cad0b8a892dc5b232", "197947211");
        mapaSakEStoneCode.put("5c8c0bd72fdf4e5aa8e036a5c561e95b", "117679500");
        mapaSakEStoneCode.put("9106e84b47034e12b79dc69dfefc87ae", "629900278");
        mapaSakEStoneCode.put("424da955a79a45289612bce338412657", "180125403");
        mapaSakEStoneCode.put("bc68f6d51e6b4c2096720b352643bf5d", "128601458");
        mapaSakEStoneCode.put("6d5ed896d6f64d9f811d5b1836e15ee1", "845710041");
        mapaSakEStoneCode.put("2168fc6ee37d4a3399e05604de065320", "195327493");
        mapaSakEStoneCode.put("70e1e0399aa54283a11dfbac7265a2e3", "421044175");
        mapaSakEStoneCode.put("2a9d73168e844ea0888a751ffad8d272", "206092615");
    }

    private static String processarITK(String itk) throws Exception {
        adicionarLog("INICIANDO CONSULTA DO ITK: " + itk);
        String saks = "2e641398644b49b7a9b21b67551215fb,f5f42407d4e34cddae2d465ab656b04e,dac42bc1f7004c3cb5f3aa647d7df9dd,9f231b2a6e7c4b60a20c657f7812ae2b,8db9ad8740fa43fd814f330fca58f912,894c70332ba246c88fe42c9a0f751a06,ec1fb7ee04a9453087d54507a155d128,baa6c200a1fe4210a1235a38e83dfa43,63e422a4afbd42f7882c0eda074a379b,ff7a77082f1340079d0b491c2af2cc29,9174b08f6f864472bfc43c58037c788b,4763ea275edb427391f5b59c38f86180,d3d6041c68554c8097cc9aa2f0d900df,645d9c88b1b546fbbf94731350aa59a0,51472a3379a34b2d96cf6ae45213cc4d,8c7b9f959e6e447cad0b8a892dc5b232,5c8c0bd72fdf4e5aa8e036a5c561e95b,9106e84b47034e12b79dc69dfefc87ae,424da955a79a45289612bce338412657,bc68f6d51e6b4c2096720b352643bf5d,6d5ed896d6f64d9f811d5b1836e15ee1,2168fc6ee37d4a3399e05604de065320,70e1e0399aa54283a11dfbac7265a2e3,2a9d73168e844ea0888a751ffad8d272";
        String[] arraySaks = saks.split(",");
        boolean encontrouSak = false;
        int i = 0;
        for (String sak : arraySaks) {
            i++;
            String retornoConsulta = consultarSituacaoCobrancaTransacao(sak, itk);
            if (!UteisValidacao.emptyString(retornoConsulta)) {
                if (podeTentarCancelar(retornoConsulta)) {
                    adicionarLog("SAK: " + sak + " | OK");
                    adicionarLog("INICIANDO ESTORNO DA COBRANÇA...");
                    boolean estornoSucesso;
                    try {
                        estornoSucesso = cancelarTransacao(sak, itk, retornoConsulta);
                        encontrouSak = true;
                    } catch (Exception ex) {
                        adicionarLog("Erro ao tentar cancelar a transação: " + ex.getMessage());
                        encontrouSak = true;
                        break;
                    }
                    if (estornoSucesso) {
                        adicionarLog("ESTORNO REALIZADO COM SUCESSO!");
                    } else {
                        adicionarLog("Não foi possível estornar esta cobrança");
                    }
                    adicionarLog("-------------------------------------------------------------------------------------------");
                    break;
                }
            }
        }
        if (i == arraySaks.length && !encontrouSak) { //terminou de consultar em todos os saks não encontrou o SAK do itk
            adicionarLog("ITK não foi encontrado para uma cobrança aprovada");
            adicionarLog("-------------------------------------------------------------------------------------------");
        }


        return "";
    }

    public static String consultarSituacaoCobrancaTransacao(String sak, String identificador) throws Exception {
        if (UteisValidacao.emptyString(sak)) {
            throw new Exception("Sak não informado");
        }

        if (UteisValidacao.emptyString(identificador)) {
            throw new Exception("Identificador não informado");
        }

        StringBuilder xmlConsulta = new StringBuilder();
        xmlConsulta.append("<Document xmlns=\"urn:AcceptorTransactionStatusReportRequestV02.1\">");
        xmlConsulta.append("	<AccptrTxStsRptRq>");
        xmlConsulta.append("		<Hdr>");
        xmlConsulta.append("			<MsgFctn>TSRR</MsgFctn>");
        xmlConsulta.append("			<PrtcolVrsn>2.0</PrtcolVrsn>");
        xmlConsulta.append("			<InitgPty>");
        xmlConsulta.append("				<Id>").append(sak).append("</Id>");
        xmlConsulta.append("			</InitgPty>");
        xmlConsulta.append("		</Hdr>");
        xmlConsulta.append("		<TxStsRpt>");
        xmlConsulta.append("			<Tx>");
        xmlConsulta.append("            <TxRpt>OPRS</TxRpt>");
        xmlConsulta.append("            <TxRpt>SUMM</TxRpt>");
        xmlConsulta.append("				<OrgnlTx>");
        xmlConsulta.append("					<InitrTxId>").append(identificador).append("</InitrTxId>");
        xmlConsulta.append("				</OrgnlTx>");
        xmlConsulta.append("			</Tx>");
        xmlConsulta.append("		</TxStsRpt>");
        xmlConsulta.append("	</AccptrTxStsRptRq>");
        xmlConsulta.append("</Document>");

        String resposta = "";
        try {
            resposta = executarRequestConsultaTransacaoPorItkStone(xmlConsulta.toString());
            return resposta;
        } catch (Exception ex) {
            adicionarLog("Erro ao realizar a request de consulta DE COBRANÇA na Stone: " + ex.getMessage());
        }
        return "";
    }

    public static boolean cancelarTransacao(String sak, String identificadorStone, String retornoConsulta) throws Exception {
        JSONObject jsonObject = XML.toJSONObject(retornoConsulta);
        String response = "";
        try {
            DocumentAcceptorCancellationRequestV0_21 paramsCancelamento = criarParametrosEnvioCancelamento(sak, identificadorStone, jsonObject);

            response = executarRequestCancelamentoTransacaoPorItkStone(paramsCancelamento.toXML());

            JSONObject jsonRespCancelamento = XML.toJSONObject(response);
            boolean canceladoComSucesso = jsonRespCancelamento.getJSONObject("Document").getJSONObject("AccptrCxlRspn").getJSONObject("CxlRspn").getJSONObject("TxRspn").getJSONObject("AuthstnRslt").getJSONObject("RspnToAuthstn").optString("Rspn").equals("APPR");
            return canceladoComSucesso;
        } catch (Exception e) {
            throw e;
        }
    }

    private static String executarRequestCancelamentoTransacaoPorItkStone(String xmlBody) throws Exception {

        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", APPLICATION_XML);

        String UrlEndpoint = "https://e-commerce.stone.com.br/Cancellation";

        try {
            return ExecuteRequestHttpService.executeHttpRequest(UrlEndpoint, xmlBody, headersMap, METODO_POST, Charsets.UTF_8.name());
        } catch (Exception e) {
            adicionarLog("Erro ao realizar a request de estorno DE COBRANÇA na Stone");
            throw e;
        }

    }

    private static boolean podeTentarCancelar(String retornoXML) {

        boolean foiCobrado = retornoXML.toUpperCase().contains("<AuthrsdSts>FULL</AuthrsdSts>".toUpperCase());
        boolean foiCancelado = retornoXML.toUpperCase().contains("<CancSts>FULL</CancSts>".toUpperCase());

        if (foiCobrado && foiCancelado) {
            adicionarLog("cobrança já estava cancelada...");
        }

        if (foiCobrado && !foiCancelado) {
            return true;
        }
        return false;
    }

    private static String executarRequestConsultaTransacaoPorItkStone(String xmlBody) throws Exception {

        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", APPLICATION_XML);

        String UrlEndpoint = "https://e-commerce.stone.com.br/TransactionStatusReport";
        return ExecuteRequestHttpService.executeHttpRequest(UrlEndpoint, xmlBody, headersMap, METODO_POST, Charsets.UTF_8.name());
    }

    private static void adicionarLog(String msg) {
        String s = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static DocumentAcceptorCancellationRequestV0_21 criarParametrosEnvioCancelamento(String sak, String identificadorStone, JSONObject consulta) throws Exception {

        String LclDtTm = consulta.getJSONObject("Document").getJSONObject("AccptrTxStsRptRspn").getJSONObject("TxStsRptRspn").getJSONObject("Tx").getJSONObject("Summry").optString("LclDtTm");
        String TtlCaptrdAmt = consulta.getJSONObject("Document").getJSONObject("AccptrTxStsRptRspn").getJSONObject("TxStsRptRspn").getJSONObject("Tx").getJSONObject("Summry").optString("TtlCaptrdAmt").replace(".", "");


        DocumentAcceptorCancellationRequestV0_21Builder doc;
        doc = DocumentAcceptorCancellationRequestV0_21Builder.init();
        return doc.comSaleAffiliationKeySAK(sak)
                .comCodigoIdentificacaoDoPontoDeInteracao(mapaSakEStoneCode.get(sak)) //obter stonecode pelo sak
                .comDataHoraLocalDaTransacaoDoPontoDeInteracao(
                        LclDtTm
                )
                .comValorTotalTransacaoEmCentavosASerCancelado(
                        TtlCaptrdAmt
                )
                .comIdentificacaoTransacaoSistemaITK(
                        identificadorStone
                )
                .build();
    }
}

package servicos.impl.dcc.base;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAdicionarPermissaoM13149 extends SuperEntidade {

    private static Integer qtdPerfilAfetado = 0;
    private static boolean simular = true;

    public ProcessoAdicionarPermissaoM13149() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

//            Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");


            //id rede empresa engenharia 467
            //id rede empresa pratique 136

            Integer idRedeEmpresa = 0;
            simular = true;

            processar(idRedeEmpresa, conOAMD);

//            Connection conZW = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");
//            executarProcessos(conZW);
        } catch (Exception ex) {
            ex.printStackTrace();
            Logger.getLogger(ProcessoAdicionarPermissaoM13149.class.getName()).log(Level.SEVERE, null, ex);
        }
    }


    private static void processar(Integer idRedeEmpresa, Connection conOAMD) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user,");
        sql.append(" \"nomeBD\" as banco, porta, urlintegracaows ");
        sql.append("from empresa ");
        sql.append("where ativa \n ");
        if (!UteisValidacao.emptyNumber(idRedeEmpresa)) {
            sql.append("and chave in (select distinct chavezw from empresafinanceiro where redeempresa_id = " + idRedeEmpresa + " and length(coalesce(chavezw ,'')) > 0)");
        }

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql.toString() + " ) as sql", conOAMD);
        Integer atual = 0;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conOAMD);
        while (rs.next()) {
            String chave = rs.getString("chave");
            String host = rs.getString("host");
            Integer porta = rs.getInt("porta");
            String bancoZW = rs.getString("banco");
            String bancoMusc = rs.getString("banco").replace("bdzillyon", "bdmusc");
            String user = rs.getString("user");
            String pass = rs.getString("pass");

            Connection conZW = null;
            Connection conMusc = null;
            try {
                Uteis.logarDebug(++atual + "/" + total + " | Qtd Perfil Afetados Atual: " + qtdPerfilAfetado + " | Chave: " + chave + " - " + bancoZW);
                conZW = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + bancoZW, user, pass);
                conMusc = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + bancoMusc, user, pass);
                verificarPermissao(chave, bancoZW, conZW, conMusc);
            } catch (Exception ex) {
                Uteis.logarDebug("ERRO Script: " + chave + " - " + bancoZW + " | " + ex.getMessage());
            } finally {
                if (conZW != null) {
                    conZW.close();
                }
                if (conMusc != null) {
                    conMusc.close();
                }
            }
        }
        Uteis.logarDebug("####################");
        Uteis.logarDebug("####################");
        Uteis.logarDebug("Total Chaves: " + total);
        Uteis.logarDebug("Perfil afetado: " + qtdPerfilAfetado);
        Uteis.logarDebug("####################");
        Uteis.logarDebug("####################");
    }

    public static void executarProcessos(Connection conZW) throws Exception {
        Connection conMusc = null;
        try {
            String chave = DAO.resolveKeyFromConnection(conZW);

            String[] teste = conZW.getMetaData().getURL().split("/");
            String bancoZW = teste[teste.length - 1];
            String bancoMusc = teste[teste.length - 1].replace("bdzillyon", "bdmusc");
            String urlBancoMusc = conZW.getMetaData().getURL().replace(bancoZW, bancoMusc);

            String user = conZW.getMetaData().getUserName();
            String pass = "pactodb";

            conMusc = DriverManager.getConnection(urlBancoMusc, user, pass);
            verificarPermissao(chave, bancoMusc, conZW, conMusc);
        } finally {
            if (conMusc != null) {
                conMusc.close();
            }
        }
    }

    private static void verificarPermissao(String chave, String banco, Connection conZW, Connection conMusc) throws Exception {
        try {
            if (!simular) {
                conZW.setAutoCommit(false);
            }

            StringBuilder perfilTr = new StringBuilder();

            //consultar perfil do treino com a permissao
            ResultSet rs = SuperFacadeJDBC.criarConsulta("select distinct perfil_codigo from permissao where recurso = 10", conMusc);
            while (rs.next()) {
                Integer perfil_codigo = rs.getInt("perfil_codigo");
                perfilTr.append(",").append(perfil_codigo);
            }

            if (UteisValidacao.emptyNumber(perfilTr.length())) {
                throw new Exception("Nenhum perfil do TR com o recurso 10");
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("distinct \n");
            sql.append("pa.nome as nomeperfilacesso, \n");
            sql.append("up.perfilacesso, \n");
            sql.append("exists(select * from permissao where tituloapresentacao ilike '13.01 - %' and codperfilacesso = up.perfilacesso) as tempermissao1301, \n");
            sql.append("exists(select * from permissao where tituloapresentacao ilike '13.05 - %' and codperfilacesso = up.perfilacesso) as tempermissao1305, \n");
            sql.append("exists(select * from permissao where tituloapresentacao ilike '13.08 - %' and codperfilacesso = up.perfilacesso) as tempermissao1308, \n");
            sql.append("exists(select codigo from log where nomeentidade ilike 'PERFIL ACESSO' and chaveprimaria = up.perfilacesso::text and valorcampoalterado ilike '%13.%') as temalteracaopermissao13 \n");
            sql.append("from usuario u \n");
            sql.append("inner join usuarioperfilacesso up on up.usuario = u.codigo \n");
            sql.append("inner join perfilacesso pa on pa.codigo = up.perfilacesso \n");
            sql.append("where u.perfiltw in (" + perfilTr.toString().replaceFirst(",", "") + ") \n");

            ResultSet rszw = SuperFacadeJDBC.criarConsulta(sql.toString(), conZW);
            while (rszw.next()) {
                String nomeperfilacesso = rszw.getString("nomeperfilacesso");
                Integer codigoPerfil = rszw.getInt("perfilacesso");
                boolean tempermissao1301 = rszw.getBoolean("tempermissao1301");
                boolean tempermissao1305 = rszw.getBoolean("tempermissao1305");
                boolean tempermissao1308 = rszw.getBoolean("tempermissao1308");

                boolean temalteracaopermissao13 = rszw.getBoolean("temalteracaopermissao13");

                if (!tempermissao1301 && !tempermissao1305 && !tempermissao1308) {

                    if (temalteracaopermissao13) {
                        Uteis.logarDebug(chave + " | " + banco + " | Não vou adicionar pq tem log com alteração de permissoes perfil: " + codigoPerfil + " - " + nomeperfilacesso);
                        continue;
                    }

                    Uteis.logarDebug(chave + " | " + banco + " | Vou adicionar permissões para perfil: " + codigoPerfil + " - " + nomeperfilacesso);

                    if (!simular) {
                        String sqlInsert1301 = "INSERT INTO permissao (sistema, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                                + " VALUES ('bdzillyon',2, '13.01 - Ver aba Avaliação Física','(0)(1)(2)(3)(9)(12)', 'AlunoAbaAvaliacaoFisica', " + codigoPerfil + ")";
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlInsert1301, conZW);
                        registrarLog(codigoPerfil, nomeperfilacesso, "13.01 - Ver aba Avaliação Física", conZW);

                        String sqlInsert1305 = "INSERT INTO permissao (sistema, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                                + " VALUES ('bdzillyon', 2, '13.05 - Ver aba Graduação','(0)(1)(2)(3)(9)(12)', 'AlunoAbaGraduacao', " + codigoPerfil + ")";
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlInsert1305, conZW);
                        registrarLog(codigoPerfil, nomeperfilacesso, "13.05 - Ver aba Graduação", conZW);

                        String sqlInsert1308 = "INSERT INTO permissao (sistema, tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                                + " VALUES ('bdzillyon', 2, '13.08 - Ver aba Treino','(0)(1)(2)(3)(9)(12)', 'AlunoAbaTreino', " + codigoPerfil + ")";
                        SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlInsert1308, conZW);
                        registrarLog(codigoPerfil, nomeperfilacesso, "13.08 - Ver aba Treino", conZW);
                    }
                    ++qtdPerfilAfetado;
                }
            }
            if (!simular) {
                conZW.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("##### Erro chave: " + chave + " | Erro: " + ex.getMessage());
            if (!simular) {
                conZW.rollback();
            }
        } finally {
            if (!simular) {
                conZW.setAutoCommit(true);
            }
        }
    }

    private static void registrarLog(Integer perfil, String nomePerfil,
                                     String permissao, Connection conzw) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(conzw);
            LogVO obj = new LogVO();
            obj.setChavePrimaria(perfil.toString());
            obj.setNomeEntidade("PERFIL ACESSO - PROCESSO");
            obj.setNomeEntidadeDescricao("PERFIL ACESSO - PROCESSO");
            obj.setOperacao("ALTERAÇÃO");
            obj.setResponsavelAlteracao("ADMIN");
            obj.setUserOAMD("");
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("ADICIONADO PERMISSAO " + permissao + " | PERFIL: " + nomePerfil);
            obj.setDataAlteracao(Calendario.hoje());
            logDAO.incluirSemCommit(obj);
        } finally {
            logDAO = null;
        }
    }
}

package servicos.impl.dcc.base;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.http.HttpStatus;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ProcessoAdicionarModuloPixOAMD {
    public static void main(String... args) throws SQLException {
        Connection conOAMD = null;
        try {
            Uteis.debug = true;

            processarChaves();
//            conOAMD = DriverManager.getConnection("*****************************************", "postgres", "pactodb");
//            processarTodas("", conOAMD);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAdicionarModuloPixOAMD.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (conOAMD != null) {
                conOAMD.close();
            }
        }
    }

    private static void processarChaves() {
        List<String> chaves = new ArrayList<>();
        chaves.add("15f150dcf36f4169afbfdab318b859b3");
        chaves.add("19830abc7c9c57d7147b05025f68c5f3");
        chaves.add("29025cfc8437aee264e99db5e0d919ef");
        chaves.add("29b37da1cfc4ed44dc7196596fd72a52");
        chaves.add("33fa79e54690bc45beb83343f7e3c730");
        chaves.add("3a19c1e4117c971cea310a84d143dee5");
        chaves.add("3a42debb1531138d8cb3b934ef030625");
        chaves.add("3b913357a8eee7b925e9a683b95f8103");
        chaves.add("3d9d99c9a2ff71c5ae2944eb8fb345da");
        chaves.add("413e7fd67c83c03437c3739a253cbf7a");
        chaves.add("4761b428f38980a5a3a9c5fada15fa84");
        chaves.add("4fd9471dfa9ae9609b31d6ed8ad9aab3");
        chaves.add("50621156776558914371e1d87aee05dc");
        chaves.add("589b54c2e94d0ad20091c3876bd34ddb");
        chaves.add("60af17cd16f72fa21a3a3ec06b3304a7");
        chaves.add("623a2f0c116fccc90aa99fdbe9fb3a9f");
        chaves.add("6718ee34a62862aba2beeb1d9acfceaf");
        chaves.add("738d717da74df715bb47bc457c4062a4");
        chaves.add("75f5c18366325438ef36da2a3de82a6b");
        chaves.add("7c24bb9deb941e9545f1fb18596e060d");
        chaves.add("8553af5a471fde839264af3fe6d2d81a");
        chaves.add("877efbd35f3c4f70f54e51e81e6d18ec");
        chaves.add("8ac568d74191771ac51536eec0a914c0");
        chaves.add("8f9da71d879dc26c071ea956b0d1b9f9");
        chaves.add("97883a5ce058ecb8b97d01b8cdc1752");
        chaves.add("9ab122f6c3b624c8abe1e7000e303c42");
        chaves.add("9e78d9613fb5135ea8d1cdd143a01e72");
        chaves.add("a5652f6ca338dbb94f89e97a1409b6d1");
        chaves.add("a855c5c74458a07ac7301a1a8df5b7ed");
        chaves.add("a86d3f9e83200630a4975aecad8d445e");
        chaves.add("a93c78ce9785c9d3ea4943a2b97cceba");
        chaves.add("adc0c5ac89e586eb8363e5e7c3ef5505");
        chaves.add("ade1581674222d645c6220f3f2ed4d35");
        chaves.add("ae66ec91296114207b102e5452f32f6e");
        chaves.add("b773defb764c1a9e25b7ac8fb20eec58");
        chaves.add("ba2a75fbd36a68dd61ca5a6265180d4");
        chaves.add("bc90a77b5b1b42a8d392b0be68303e55");
        chaves.add("bdfd0b64da6255bdb1658ba11e770fac");
        chaves.add("c3f142aff19990abc1b5b9212447303a");
        chaves.add("c98ee261baf802cc5b270bfb06a25a6d");
        chaves.add("cd5f2dae5ccdc19d495e5586d642a36e");
        chaves.add("d212353ea7584f6d02d9acea1b257655");
        chaves.add("dd65ef9a5579d4e518c6d4abbd0cb1c6");
        chaves.add("ed2d9856ad38cbe4bfb640f94afdd802");
        chaves.add("ef9f2365f60ac8e2356e254c8c91488f");
        chaves.add("f09689b14845ec8ca03ae5d47a00d648");

        Integer total = chaves.size();
        Uteis.logarDebug("Total | " + total);
        int atual = 0;
        int sucesso = 0;
        int falha = 0;
        for (String chave : chaves) {
            try {
                Uteis.logarDebug("Processando | " + ++atual + "/" + total + " | " + chave);
                adicionarModulo(chave);
                ++sucesso;
            } catch (Exception ex) {
                Uteis.logarDebug("ERRO | " + chave + " | " + ex.getMessage());
                ++falha;
            }
        }
        Uteis.logarDebug("Total | " + total);
        Uteis.logarDebug("Sucesso | " + sucesso);
        Uteis.logarDebug("Falha | " + falha);
    }

    private static void processarTodas(String chaveProcessar, Connection conOAMD) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("distinct (ef.chavezw) as chave \n");
        sql.append("from empresafinanceiro ef \n");
        sql.append("inner join empresa e on e.chave = ef.chavezw \n");
        sql.append("where e.ativa \n");
        if (UteisValidacao.emptyString(chaveProcessar)) {
            sql.append("and ef.codigo in (select empresafinanceiro_codigo from z_oamd_pactopay_convenio_cobranca where tipo_cobranca ilike '%PIX%') \n");
            sql.append("and e.modulos not ilike '%pix%' \n");
        } else {
            sql.append("and ef.chavezw = '").append(chaveProcessar).append("' \n");
        }
        sql.append("order by 1 \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", conOAMD);
        Uteis.logarDebug("Total | " + total);

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), conOAMD);
        int atual = 0;
        int sucesso = 0;
        int falha = 0;
        while (rs.next()) {
            String chave = "";
            try {
                chave = rs.getString("chave");
                Uteis.logarDebug("Processando | " + ++atual + " | " + chave);
                adicionarModulo(chave);
                ++sucesso;
            } catch (Exception ex) {
//                ex.printStackTrace();
                Uteis.logarDebug("ERRO | " + chave + " | " + ex.getMessage());
                ++falha;
            }
        }
        Uteis.logarDebug("Total | " + total);
        Uteis.logarDebug("Sucesso | " + sucesso);
        Uteis.logarDebug("Falha | " + falha);
    }

    private static void adicionarModulo(String chave) throws Exception {
        String path = "http://app.pactosolucoes.com.br/oamd/prest/adm/" + chave + "/ativarPix";
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, null, MetodoHttpEnum.POST);
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception(respostaHttpDTO.getResponse());
        }
    }
}

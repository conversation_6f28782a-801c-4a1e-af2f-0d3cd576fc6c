/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.integracao.enotas.to.InutilizacaoNotaFiscalTO;
import br.com.pactosolucoes.integracao.enotas.to.NotaEnotasTO;
import controle.notaFiscal.NotaFiscalControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.notaFiscal.StatusEnotasEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
public class AjustarNotaFiscalSESCMS extends SuperEntidade {

    public AjustarNotaFiscalSESCMS() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            String chave = "9fe5efa91ec0625b18d8f4cf8eddf95f";
            Connection con = DriverManager.getConnection("****************************************", "postgres", "pactodb");
            Connection conNotas = DriverManager.getConnection("*******************************************", "postgres", "pactodb");

            Uteis.logar(null, "Obter conexão para chave: " + chave);
//            Connection con = new DAO().obterConexaoEspecifica(chave);
//            Conexao.guardarConexaoForJ2SE(chave, con);

            processar(chave, con, conNotas);
        } catch (Exception ex) {
            Logger.getLogger(AjustarNotaFiscalSESCMS.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void atualizarBanco(Connection con) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("update notafiscal set dataregistro = dataemissao where dataregistro::date <= '1955-12-07';", con);
        SuperFacadeJDBC.executarUpdateExecutarProcessos("alter table notafiscal add column statusprocesso character varying (150);", con);
    }

    private static void processar(String chave, Connection con, Connection conNotas) {
        NotaFiscal notaFiscalDAO;
        Usuario usuarioDAO;
        try {
            notaFiscalDAO = new NotaFiscal(con);
            usuarioDAO = new Usuario(con);

            atualizarBanco(con);

            UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();

            String sqlBase = "select * from notafiscal where statusnota ilike '%aguardando%'";

            List<NotaFiscalVO> notasAjustar = notaFiscalDAO.montarDadosConsulta(SuperFacadeJDBC.criarConsulta(sqlBase, con), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);

            for (NotaFiscalVO notaFiscalVO : notasAjustar) {
                boolean erro = false;
                try {

                    String sql2 = "select * from notafiscal where data::date = '" + Uteis.getDataFormatoBD(notaFiscalVO.getDataRegistro()) +
                            "' and chave = '" + chave + "' and jsonenvio ilike '%" + notaFiscalVO.getRazaoSocial() + "%' and codnotafiscalzw = " + notaFiscalVO.getCodigo();
                    Integer totalNota = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql2 + " ) as sql", conNotas);

                    if (UteisValidacao.emptyNumber(totalNota)) {

                        sql2 = "select * from notafiscal where data::date = '" + Uteis.getDataFormatoBD(notaFiscalVO.getDataRegistro()) +
                                "' and chave = '" + chave + "' and jsonenvio ilike '%" + notaFiscalVO.getNomeCliente() + "%' ";
                        totalNota = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql2 + " ) as sql", conNotas);
                        if (UteisValidacao.emptyNumber(totalNota)) {
                            Uteis.logarDebug("2 - Nota não encontrada");
                            erro = true;
                            continue;
                        } else if (totalNota > 1) {

                            sql2 = "select * from notafiscal where data::date = '" + Uteis.getDataFormatoBD(notaFiscalVO.getDataRegistro()) +
                                    "' and chave = '" + chave + "' and jsonenvio ilike '%" + notaFiscalVO.getNomeCliente() + "%' and valor = " + notaFiscalVO.getValor();
                            totalNota = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql2 + " ) as sql", conNotas);

                            if (UteisValidacao.emptyNumber(totalNota)) {
                                Uteis.logarDebug("3 - Nota não encontrada");
                                erro = true;
                                continue;
                            } else if (totalNota > 1) {
                                Uteis.logarDebug("3 - Mais de uma nota encontrada | " + notaFiscalVO.getCodigo());
                                Uteis.logarDebug(sql2);
                                erro = true;
                                continue;
                            }
                        }
                    } else if (totalNota > 1) {
                        Uteis.logarDebug("2 - Mais de uma nota encontrada | " + notaFiscalVO.getCodigo());
                        Uteis.logarDebug(sql2);
                        erro = true;
                        continue;
                    }

                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sql2, conNotas);
                    while (rs.next()) {
                        if (UteisValidacao.emptyString(notaFiscalVO.getIdEmpresaEnotas())) {
//                            Uteis.logarDebug("Nota sem IdEmpresaEnotas");
                            notaFiscalVO.setIdEmpresaEnotas(rs.getString("idempresaenotas"));
                        }
                        if (UteisValidacao.emptyString(notaFiscalVO.getIdExterno())) {
//                            Uteis.logarDebug("Nota sem IdExterno");
                            notaFiscalVO.setIdExterno(rs.getString("idexterno"));
                        }
                        if (UteisValidacao.emptyString(notaFiscalVO.getIdPacto())) {
//                            Uteis.logarDebug("Nota sem IdPacto");
                            notaFiscalVO.setIdPacto(rs.getString("idpacto"));
                        }
                    }

                    NotaEnotasTO notaEnotasTO = notaFiscalDAO.consultarNotaEnotas(notaFiscalVO);
                    if (notaEnotasTO != null && !notaEnotasTO.getStatus().equalsIgnoreCase(notaFiscalVO.getStatusNota())) {
                        prepararStatusInutilizacao(notaFiscalVO, notaEnotasTO);
                        notaFiscalDAO.atualizarDadosNotaFiscalEnotas(notaFiscalVO, notaEnotasTO, usuarioVO);
                        Uteis.logarDebug("#### AJUSTADA " + notaFiscalVO.getCodigo());
                    }

                } catch (Exception ex) {
//                    ex.printStackTrace();
                    Uteis.logarDebug("ERRO: " + ex.getMessage());
                    if (ex.getMessage().contains("A Nota fiscal não foi encontrada")) {
                        erro = true;
                    }
                } finally {
                    if (erro) {
                        SuperFacadeJDBC.executarConsulta("update notafiscal set statusprocesso = statusnota where codigo = " + notaFiscalVO.getCodigo(), con);
                        notaFiscalDAO.atualizarStatusNota(StatusEnotasEnum.ERRO, notaFiscalVO);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            notaFiscalDAO = null;
        }
    }

    private static InutilizacaoNotaFiscalTO prepararStatusInutilizacao(NotaFiscalVO notaFiscalVO, NotaEnotasTO enotasTO) {
        InutilizacaoNotaFiscalTO inutilizacaoNotaFiscalTO = null;
        try {
            if (notaFiscalVO.getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZACAOSOLICITADO) ||
                    notaFiscalVO.getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZACAONEGADA) ||
                    notaFiscalVO.getStatusNotaEnum().equals(StatusEnotasEnum.INUTILIZADA)
            ) {
                inutilizacaoNotaFiscalTO = getFacade().getNotaFiscal().consultarInutilizacaoNotaEnotas(notaFiscalVO);

                if (inutilizacaoNotaFiscalTO.getStatus().equals(StatusEnotasEnum.NEGADA.getDescricaoEnotas()) &&
                        inutilizacaoNotaFiscalTO.getMotivoStatus().contains("Uma NF-e da faixa já está inutilizada na Base de dados da SEFAZ")) {
                    enotasTO.setStatus(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                    notaFiscalVO.setStatusNota(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                } else if (inutilizacaoNotaFiscalTO.getStatus().equals(StatusEnotasEnum.NEGADA.getDescricaoEnotas())) {
                    enotasTO.setStatus(StatusEnotasEnum.INUTILIZACAONEGADA.getDescricaoEnotas());
                    notaFiscalVO.setStatusNota(StatusEnotasEnum.INUTILIZACAONEGADA.getDescricaoEnotas());
                }

                if (inutilizacaoNotaFiscalTO.getStatus().equals(StatusEnotasEnum.AUTORIZADA.getDescricaoEnotas())) {
                    enotasTO.setStatus(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                    notaFiscalVO.setStatusNota(StatusEnotasEnum.INUTILIZADA.getDescricaoEnotas());
                }

                return inutilizacaoNotaFiscalTO;
            }
        } catch (Exception ex) {
            Uteis.logar("Erro ao consultar inutilização de notas");
            Uteis.logar(ex, NotaFiscalControle.class);
        }

        return inutilizacaoNotaFiscalTO;
    }
}

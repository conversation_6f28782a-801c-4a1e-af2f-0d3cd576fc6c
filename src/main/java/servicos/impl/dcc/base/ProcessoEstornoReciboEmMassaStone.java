package servicos.impl.dcc.base;

import controle.financeiro.EstornoReciboControle;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.financeiro.Transacao;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> | 15/04/2025
 */

public class ProcessoEstornoReciboEmMassaStone {

    private static StringBuilder logGravar;

    public static void main(String[] args) throws Exception {
        try {
            adicionarLog("###########################################################################");
            adicionarLog("******INÍCIO PROCESSO ESTORNO TRANSAÇÕES STONE******");
            adicionarLog("###########################################################################");

            Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            // Connection conOAMD = DriverManager.getConnection("*****************************************************", "postgres", "pactodb");

            String sql = "select chave,\"passwordBD\" as pass,\"hostBD\" as host,\"userBD\" as user," +
                    " \"nomeBD\" as banco, porta, urlintegracaows " +
                    "from empresa where ativa";

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql.toString() + " ) as sql", conOAMD);
            adicionarLog("******* TOTAL DE EMPRESAS ATIVAS NO OAMD PRA RODAR O PROCESSO: " + total + " *******");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
            int i = 0;
            while (rs.next()) {
                i++;
                adicionarLog("******* PROCESSAR EMPRESA " + i + "/" + total + " *******");
                String chave = rs.getString("chave");
                String host = rs.getString("host");
                Integer porta = rs.getInt("porta");
                String banco = rs.getString("banco");
                String user = rs.getString("user");
                String pass = rs.getString("pass");

                try (Connection con = DriverManager.getConnection("jdbc:postgresql://" + host + ":" + porta + "/" + banco, user, pass)) {
                    adicionarLog("CHAVE: " + chave + " | Obter conexão..." + " - " + host + ":" + porta + "/" + banco);
                    processar(chave, con);
                    adicionarLog("CHAVE: " + chave + " | FIM");
                    adicionarLog("---------------------------------------------------------------------------------------------------");
                } catch (Exception ex) {
                    adicionarLog("#### ERRO conectar: " + chave + " - " + banco + " | " + ex.getMessage());
                    adicionarLog("---------------------------------------------------------------------------------------------------");
                }
            }

        } catch (Exception ex) {
        } finally {
            adicionarLog("******FIM PROCESSO ESTORNO RECIBOS STONE******");
            Uteis.salvarArquivo(ProcessoEstornoReciboEmMassaStone.class.getSimpleName() + "_" + Calendario.getData("yyyyMMddHHmmss") + ".txt",
                    getLogGravar().toString(), "C:\\Processos" + File.separator);
        }
    }

    public static void processar(String chave, Connection con) throws Exception {
        try {
            List<TransacaoVO> transacoesEstornarRecibo = new ArrayList<>();
            transacoesEstornarRecibo = obterTransacoesIncorretasParaEstornar(con);

            if (UteisValidacao.emptyList(transacoesEstornarRecibo)) {
                adicionarLog("** CHAVE: " + chave + " | Nenhuma transação incorreta STONE para estornar! **");
                return;
            } else {
                adicionarLog("** CHAVE: " + chave + " | Encontrado " + transacoesEstornarRecibo.size() + " transações STONE para estornar **");
            }
            Integer totalTransacoesEstornar = transacoesEstornarRecibo.size();
            int i = 0;
            for (TransacaoVO transacaoVO : transacoesEstornarRecibo) {
                i++;
                try {
                    Integer codRecibo = transacaoVO.getReciboPagamento();
                    adicionarLog("Estornando transação " + transacaoVO.getCodigo() + " " + i + "/" + totalTransacoesEstornar + " --> Recibo: " + codRecibo);
                    boolean sucesso = estornarRecibo(transacaoVO, false, con);
                    if (sucesso) {
                        adicionarLog("Transação " + transacaoVO.getCodigo() + " --> " + "Recibo: " + codRecibo + " estornado com sucesso!");
                    } else {
                        adicionarLog("ERRO ao estornar Transação " + transacaoVO.getCodigo() + " --> " + "Recibo: " + codRecibo);
                    }
                } catch (Exception ex) {
                    adicionarLog("ERRO AO PROCESSAR TRANSAÇÃO: " + transacaoVO.getCodigo() + ex.getMessage());
                }
            }
        } catch (Exception ex) {
            adicionarLog("CHAVE: " + chave + " | Erro ao processar o convênio" + " | " + ex.getMessage());
        }
    }

    public static boolean estornarRecibo(TransacaoVO transacaoEstornar, Boolean estornarNaOperadora, Connection con) throws Exception {
        ReciboPagamento reciboPagamentoDAO;
        MovPagamento movPagamentoDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        Transacao transacaoDAO;
        Usuario usuarioDAO;
        try {
            reciboPagamentoDAO = new ReciboPagamento(con);
            movPagamentoDAO = new MovPagamento(con);
            movProdutoParcelaDAO = new MovProdutoParcela(con);
            transacaoDAO = new Transacao(con);
            usuarioDAO = new Usuario(con);

            //usuario responsável cancelamento
            transacaoEstornar.setUsuarioResponsavelCancelamento(usuarioDAO.getUsuarioRecorrencia());

            EstornoReciboControle estorno = new EstornoReciboControle();
            EstornoReciboVO estornoVO = new EstornoReciboVO();
            estornoVO.setExcluirNFSe(true);

            estornoVO.setResponsavelEstornoRecivo(transacaoEstornar.getUsuarioResponsavelCancelamento());

            List<MovPagamentoVO> listaMovPagamento = new ArrayList();
            listaMovPagamento = movPagamentoDAO.consultarPorCodigoRecibo(transacaoEstornar.getReciboPagamento(), false, Uteis.NIVELMONTARDADOS_TODOS);

            estornoVO.setListaMovPagamento(listaMovPagamento);
            estornoVO.getListaTransacoes().add(transacaoEstornar);

            if (UteisValidacao.emptyList(transacaoEstornar.getListaParcelas())) {
                adicionarLog("Lista de parcelas transação vazia | Transação " + transacaoEstornar.getCodigo());
                transacaoEstornar.setListaParcelas(transacaoDAO.obterParcelasDaTransacao(transacaoEstornar));
            }

            estornoVO.setListaMovParcela(transacaoEstornar.getListaParcelas());
            estornoVO.setEstornarOperadora(estornarNaOperadora);
            estornoVO.setReciboPagamentoVO(reciboPagamentoDAO.consultarPorChavePrimaria(transacaoEstornar.getReciboPagamento(), Uteis.NIVELMONTARDADOS_TODOS));

            estorno.setEstornoReciboVO(estornoVO);

            reciboPagamentoDAO.estornarReciboPagamento(estornoVO, movPagamentoDAO, movProdutoParcelaDAO, null, null, null, false);

            // colcoar os parametros de resposta em outra coluna
            transacaoEstornar.setResultadoCancelamento(transacaoEstornar.getParamsResposta());

            alterarTransacao(transacaoEstornar, con);

            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        } finally {
            reciboPagamentoDAO = null;
            movPagamentoDAO = null;
            movProdutoParcelaDAO = null;
            transacaoDAO = null;
            usuarioDAO = null;
        }
    }

    public static void alterarTransacao(TransacaoVO obj, Connection con) throws Exception {
        try {
            String sql = ("UPDATE transacao set situacao = ?, codigoretorno = ?, nsu = ?, codigoAutorizacao = ?, resultadocancelamento = ?, paramsResposta = ? WHERE codigo = ? ");
            try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
                int i = 0;
                sqlAlterar.setInt(++i, SituacaoTransacaoEnum.NAO_APROVADA.getId());
                sqlAlterar.setString(++i, "?");
                sqlAlterar.setString(++i, "");
                sqlAlterar.setString(++i, "");
                sqlAlterar.setString(++i, obj.getResultadoCancelamento());
                sqlAlterar.setString(++i, "");
                sqlAlterar.setInt(++i, obj.getCodigo());
                sqlAlterar.execute();
            }
        } catch (Exception ex) {
            adicionarLog("Não foi possível atualizar a transação" + ex.getMessage());
        }
    }

    public static List<TransacaoVO> obterTransacoesIncorretasParaEstornar(Connection con) throws Exception {
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(con);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("t.dataprocessamento, \n");
            sql.append("t.codigo as id_tran_pct, \n");
            sql.append("e.nome, \n");
            sql.append("e.cnpj, \n");
            sql.append("c.codigoautenticacao01 as stonecode, \n");
            sql.append("'\"' || (xpath('//*[local-name()=\"TxRef\"]/text()', t.paramsenvio::xml))[1]::text || '\"' as tx_ref_envio, \n");
            sql.append("'\"' || (xpath('//*[local-name()=\"TxRef\"]/text()', t.paramsresposta::xml))[1]::text || '\"' as tx_ref_resp, \n");
            sql.append("'\"' || (xpath('//*[local-name()=\"RcptTxId\"]/text()', t.paramsresposta::xml))[1]::text || '\"' as RcptTxId, \n");
            sql.append("(xpath('//*[local-name()=\"PAN\"]/text()', t.paramsenvio::xml))[1]::text as numcard_req, \n");
            sql.append("(xpath('//*[local-name()=\"TtlAmt\"]/text()', t.paramsenvio::xml))[1]::text as ttl_amt_req, \n");
            sql.append("(xpath('//*[local-name()=\"TtlAmt\"]/text()', t.paramsresposta::xml))[1]::text as ttl_amt_resp \n");
            sql.append("from \n");
            sql.append("transacao t \n");
            sql.append("inner join empresa e on \n");
            sql.append("e.codigo = t.empresa \n");
            sql.append("inner join conveniocobranca c on \n");
            sql.append("c.codigo = t.conveniocobranca \n");
            sql.append("where \n");
            sql.append("t.dataprocessamento >= '2025-03-18' \n");
            sql.append("and t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append("\n");
            sql.append("and t.paramsenvio ~ '<ShrtNm>([^<]+)</ShrtNm>' \n");
            sql.append("and t.paramsresposta ~ '<ShrtNm>([^<]+)</ShrtNm>' \n");
            sql.append("and substring(t.paramsenvio from '<ShrtNm>([^<]+)</ShrtNm>') != substring(t.paramsresposta from '<ShrtNm>([^<]+)</ShrtNm>') \n");
            sql.append("and c.tipoconvenio = 21 \n");
            sql.append("order by \n");
            sql.append("t.dataprocessamento \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

            List<TransacaoVO> listaTransacoes = new ArrayList<>();
            while (rs.next()) {
                Integer codigo = 0;
                try {
                    codigo = rs.getInt("id_tran_pct");
                    TransacaoVO transacaoVO = transacaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_BASICO_TRANSACAO);
                    listaTransacoes.add(transacaoVO);
                } catch (Exception ex) {
                    adicionarLog("ERRO AO OBTER TRANSAÇÃO: " + codigo + ex.getMessage());
                }
            }
            return listaTransacoes;
        } catch (Exception ex) {
            throw ex;
        } finally {
            transacaoDAO = null;
        }
    }

    private static void adicionarLog(String msg) {
        String s = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

}

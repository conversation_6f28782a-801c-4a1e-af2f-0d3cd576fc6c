package servicos.impl.dcc.base;

import br.com.pactosolucoes.atualizadb.processo.GerarReciboTransacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.Transacao;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import servicos.impl.stone.StoneOnlineServiceConciliation;
import servicos.impl.stone.xml.cancellation.request.DocumentAcceptorCancellationRequestV0_21;
import servicos.impl.stone.xml.cancellation.request.DocumentAcceptorCancellationRequestV0_21Builder;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static javax.ws.rs.core.MediaType.APPLICATION_XML;
import static servicos.util.ExecuteRequestHttpService.METODO_POST;

/**
 * <AUTHOR>
 */
public class ProcessoIdentificarCobrancasStoneAprovadasInterrogacao {
    private static StringBuilder logGravar;
    private static Map<String, String> mapaStoneCode = new HashMap<>();
    private static Integer qtd_transacoes = 0;
    private static Integer qtd_transacoes_pagas_encontradas_conciliacao = 0;
    private static Integer qtd_transacoes_cartao_encontrado_outros_dados_nao_bate = 0;
    private static Integer qtd_transacoes_sem_conciliacao = 0;
    private static Integer qtd_transacoes_parcelas_pagas = 0;
    private static Integer qtd_transacoes_sem_pagamento_extrato = 0;
    private static Integer qtd_transacoes_gerar_recibo = 0;
    private static Integer qtd_transacoes_gerar_recibo_erro = 0;
    private static Integer qtd_transacoes_sem_parcela_encontrada = 0;
    private static Integer qtd_transacoes_estornadas_sucesso = 0;
    private static Integer qtd_transacoes_estornadas_erro = 0;

    //Data padrão para processo automático é sempre o dia anterior ao atual
    private static String data_inicio = Calendario.getDataAplicandoFormatacao(Calendario.ontem(), "yyyy-MM-dd");
    private static String data_fim = Calendario.getDataAplicandoFormatacao(Calendario.ontem(), "yyyy-MM-dd");

    public static void main(String[] args) throws Exception {
        //para testar
//        args = new String[]{"todas", "6"};

        adicionarLog("INICIANDO PROCESSO AUTOMÁTICO DE IDENTIFICAÇÃO DE COBRANÇAS APROVADAS OU NÃO NA STONE");

        int qtdDiasAtrasProcessar = 0;
        Uteis.debug = true;
        List<Map> lista = new ArrayList<Map>();

        if (args.length > 0 && args[0].equals("todas")) {
            DAO oamd = new DAO();
            lista = oamd.buscarListaEmpresas();
        }

        for (Map map : lista) {
            final String chave = (String) map.get("chave");
            Uteis.logar(null, "Obter conexão para chave: " + chave);

            Connection con = null;
            try {
                adicionarLog("Obter conexão para chave: " + chave);
                con = new DAO().obterConexaoEspecifica(chave);
                qtdDiasAtrasProcessar = Integer.parseInt(args[1]);
                data_inicio = Calendario.getDataAplicandoFormatacao(Calendario.subtrairDias(Calendario.ontem(), qtdDiasAtrasProcessar), "yyyy-MM-dd");
                adicionarLog(" | Data Inicio: " + data_inicio + " | Data Fim: " + data_fim + " | Quantidade de dias atras para processar: " + qtdDiasAtrasProcessar);
                processar(con);
            } catch (Exception ex) {
                Logger.getLogger(ProcessoIdentificarCobrancasStoneAprovadasInterrogacao.class.getName()).log(Level.SEVERE, null, ex);
            } finally {
                try {
                    con.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static String processarOrigemSistemaPacto(Connection con, String datainicio, String datafim) throws Exception {
        adicionarLog("INICIANDO PROCESSO MANUAL DE IDENTIFICAÇÃO DE COBRANÇAS APROVADAS OU NÃO NA STONE");
        data_inicio = datainicio;
        data_fim = datafim;
        processar(con);
        JSONObject retorno = new JSONObject();
        retorno.put("Total de transações verificadas", qtd_transacoes);
        retorno.put("Transações que não foi encontrado pagamento na conciliação", qtd_transacoes_sem_pagamento_extrato);
        retorno.put("Transações que foi encontrado o registro na conciliação", qtd_transacoes_pagas_encontradas_conciliacao);
        retorno.put("Transações que foi encontrado o cartão na conciliação porem a data/hora ou valor não condiz com a transação", qtd_transacoes_cartao_encontrado_outros_dados_nao_bate);
        retorno.put("Transações que não foi possível consultar a conciliação do stone code ou do dia", qtd_transacoes_sem_conciliacao);
        retorno.put("Transações com parcelas que já foram pagas (Pagamento Duplicado)", qtd_transacoes_parcelas_pagas);
        retorno.put(" Transações que foi gerado o recibo", qtd_transacoes_gerar_recibo);
        retorno.put("Transações que não foi possível gerar o recibo", qtd_transacoes_gerar_recibo_erro);
        retorno.put("Transações que não foi possível gerar o recibo pois não foi encontrado parcelas para a transação", qtd_transacoes_sem_parcela_encontrada);
        retorno.put("Transações que foram estornadas com sucesso", qtd_transacoes_estornadas_sucesso);
        retorno.put("Transações que não foi possível estornar", qtd_transacoes_estornadas_erro);

        return retorno.toString();
    }

    public static void processar(Connection con) throws Exception {
        try {
            qtd_transacoes = 0;
            qtd_transacoes_sem_pagamento_extrato = 0;
            qtd_transacoes_pagas_encontradas_conciliacao = 0;
            qtd_transacoes_cartao_encontrado_outros_dados_nao_bate = 0;
            qtd_transacoes_sem_conciliacao = 0;
            qtd_transacoes_parcelas_pagas = 0;
            qtd_transacoes_gerar_recibo = 0;
            qtd_transacoes_gerar_recibo_erro = 0;
            qtd_transacoes_sem_parcela_encontrada = 0;
            qtd_transacoes_estornadas_sucesso = 0;
            qtd_transacoes_estornadas_erro = 0;

            Uteis.debug = true;
            mapaStoneCode = new HashMap<>();

            Integer atual = 0;
            qtd_transacoes = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sqlTransacoesInterrogacao() + ") as qtd", con);
            adicionarLog("---------------------------------------------------------------------------------");
            adicionarLog("--------------------------- " + qtd_transacoes + " TRANSAÇÕES ---------------------------");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlTransacoesInterrogacao(), con);
            while (rs.next()) {
                String matricula = "";
                String nome = "";
                String msg = "";
                Integer codTransacao = 0;
                Integer reciboGerado = 0;
                try {
                    codTransacao = rs.getInt("codtransacao");
                    matricula = rs.getString("matricula");
                    nome = rs.getString("nome");
                    Date dataTransacao = rs.getTimestamp("data");
                    String dataTransacaoStringComparar = Calendario.getDataAplicandoFormatacao(dataTransacao, "yyyyMMddHHmm");
                    String diaExibir = Calendario.getDataAplicandoFormatacao(dataTransacao, "dd/MM/yyyy");
                    String stoneCode = rs.getString("stonecode");
                    String sak = rs.getString("sak");
                    String cartaoMascarado = rs.getString("cartaoMascarado");
                    Double valorTransacao = rs.getDouble("valor");
                    Double valorTransacaoArredondadoComparar = Uteis.arredondarForcando2CasasDecimais(valorTransacao);

                    String parametrosEnvio = rs.getString("paramsenvio");
                    int startIdx = parametrosEnvio.indexOf("<InitrTxId>") + "<InitrTxId>".length();
                    int endIdx = parametrosEnvio.indexOf("</InitrTxId>");

                    String initrTxIdParametrosEnvio = "";
                    if (startIdx != -1 && endIdx != -1) {
                        String initrTxId = parametrosEnvio.substring(startIdx, endIdx);
                        initrTxIdParametrosEnvio = initrTxId;
                    }

                    msg = ("Processando: " + ++atual + "/" + qtd_transacoes + " | Cod: " + codTransacao + " | Mat: " + matricula + " - " + nome);

                    try {
                        preencherConciliacaoDoDiaStoneCode(dataTransacao, stoneCode);
                    } catch (Exception ex) {
                        ++qtd_transacoes_sem_conciliacao;
                        throw ex;
                    }

                    JSONArray concVendasMaisDeUmRegistro = null;
                    JSONObject concVendasSomenteUmRegistro = null;
                    try {
                        concVendasMaisDeUmRegistro = new JSONObject(mapaStoneCode.getOrDefault(chaveMapaConciliacao(dataTransacao, stoneCode), "")).
                                getJSONObject("Conciliation").getJSONObject("FinancialTransactions").getJSONArray("Transaction");
                    } catch (Exception ignored) {
                    }

                    try {
                        concVendasSomenteUmRegistro = new JSONObject(mapaStoneCode.getOrDefault(chaveMapaConciliacao(dataTransacao, stoneCode), "")).
                                getJSONObject("Conciliation").getJSONObject("FinancialTransactions").getJSONObject("Transaction");
                    } catch (Exception ignored) {
                    }

                    //NENHUM REGISTRO DE VENDA
                    if (concVendasMaisDeUmRegistro == null && concVendasSomenteUmRegistro == null) {
                        ++qtd_transacoes_sem_pagamento_extrato;
                        alterarSituacaoDaTransacao(codTransacao, SituacaoTransacaoEnum.NAO_APROVADA, con);
                        throw new Exception("PAGAMENTO NÃO ENCONTRADO - Data " + diaExibir + " | StoneCode " + stoneCode + " | transação: " + codTransacao);
                    }

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

                    //MAIS DE UM REGISTRO DE VENDA
                    boolean encontrouNaConciliacao = false;
                    String iTKTransacaoParaEstorno = "";
                    String lclDtTmParaEstorno = "";
                    Double ttlCaptrdAmtParaEstorno = 0.0;
                    if (concVendasMaisDeUmRegistro != null && concVendasSomenteUmRegistro == null) {
                        for (int i = 0; i < concVendasMaisDeUmRegistro.length(); i++) {
                            JSONObject venda = concVendasMaisDeUmRegistro.getJSONObject(i);

                            iTKTransacaoParaEstorno = venda.optString("InitiatorTransactionKey");
                            if (cartaoMascarado.equals(venda.optString("CardNumber")) || iTKTransacaoParaEstorno.equals(initrTxIdParametrosEnvio)) {

                                Date captureLocalDateTime = sdf.parse(String.valueOf(venda.getLong("CaptureLocalDateTime")));
                                Double capturedAmount = venda.getDouble("CapturedAmount");

//                                Date authorizationDateTime = sdf.parse(String.valueOf(venda.getLong("AuthorizationDateTime")));
//                                adicionarLog("capturedAmount - " + capturedAmount);
//                                adicionarLog("captureLocalDateTime - " + captureLocalDateTime);
//                                adicionarLog("authorizationDateTime - " + authorizationDateTime);
//                                adicionarLog("valorTransacao - " + valorTransacao);
//                                adicionarLog("dataTransacao - " + dataTransacao);

                                String dataVendaConciliacao = Calendario.getDataAplicandoFormatacao(captureLocalDateTime, "yyyyMMddHHmm");
                                Double valorConciliacao = Uteis.arredondarForcando2CasasDecimais(capturedAmount);

                                if (dataTransacaoStringComparar.equalsIgnoreCase(dataVendaConciliacao) &&
                                        valorTransacaoArredondadoComparar.equals(valorConciliacao)) {
                                    encontrouNaConciliacao = true;
                                    iTKTransacaoParaEstorno = venda.getString("InitiatorTransactionKey");
                                    lclDtTmParaEstorno = String.valueOf(venda.getLong("CaptureLocalDateTime"));
                                    ttlCaptrdAmtParaEstorno = venda.optDouble("CapturedAmount");
                                    break;
                                } else {
                                    ++qtd_transacoes_cartao_encontrado_outros_dados_nao_bate;
                                }
                            }
                        }
                    }

                    //SOMENTE UM REGISTRO DE VENDA
                    if (!encontrouNaConciliacao &&
                            concVendasSomenteUmRegistro != null && concVendasMaisDeUmRegistro == null) {
                        if (cartaoMascarado.equals(concVendasSomenteUmRegistro.optString("CardNumber"))) {

                            Date captureLocalDateTime = sdf.parse(String.valueOf(concVendasSomenteUmRegistro.getLong("CaptureLocalDateTime")));
                            Double capturedAmount = concVendasSomenteUmRegistro.getDouble("CapturedAmount");

//                            Date authorizationDateTime = sdf.parse(String.valueOf(concVendasSomenteUmRegistro.getLong("AuthorizationDateTime")));
//                            adicionarLog("capturedAmount - " + capturedAmount);
//                            adicionarLog("captureLocalDateTime - " + captureLocalDateTime);
//                            adicionarLog("authorizationDateTime - " + authorizationDateTime);
//                            adicionarLog("valorTransacao - " + valorTransacao);
//                            adicionarLog("dataTransacao - " + dataTransacao);

                            String dataVendaConciliacao = Calendario.getDataAplicandoFormatacao(captureLocalDateTime, "yyyyMMddHHmm");
                            Double valorConciliacao = Uteis.arredondarForcando2CasasDecimais(capturedAmount);

                            if (dataTransacaoStringComparar.equalsIgnoreCase(dataVendaConciliacao) &&
                                    valorTransacaoArredondadoComparar.equals(valorConciliacao)) {
                                encontrouNaConciliacao = true;
                                iTKTransacaoParaEstorno = concVendasSomenteUmRegistro.getString("InitiatorTransactionKey");
                                lclDtTmParaEstorno = String.valueOf(concVendasSomenteUmRegistro.getLong("CaptureLocalDateTime"));
                                ttlCaptrdAmtParaEstorno = concVendasSomenteUmRegistro.optDouble("CapturedAmount");
                            } else {
                                ++qtd_transacoes_cartao_encontrado_outros_dados_nao_bate;
                            }
                        }
                    }

                    if (encontrouNaConciliacao) {
                        ++qtd_transacoes_pagas_encontradas_conciliacao;

                        //aqui ele achou a venda do mesmo cartão, então validar as parcelas
                        validarParcelas(codTransacao, con);

                        boolean gerouReciboSucesso = false;
                        try {
                            //tentar gerar o recibo
                            reciboGerado = GerarReciboTransacao.gerarReciboDaTransacao(con, codTransacao, true);
                            gerouReciboSucesso = true;
                        } catch (Exception ex) {
                            //se não existe transacaomovparcela (quando é do vendas online) não tem como gerar recibo, então neste caso, estornar
                            // a transação visto que o contrato também já foi estornado na tentativa da venda online.
                            if (ex.getMessage().contains("Nenhuma parcela da transação encontrada") && UteisValidacao.emptyNumber(reciboGerado)) {
                                qtd_transacoes_sem_parcela_encontrada++;
                                boolean estornouComSucesso = tentarEstornarTransacao(codTransacao.toString(), sak, iTKTransacaoParaEstorno, stoneCode, lclDtTmParaEstorno, ttlCaptrdAmtParaEstorno);
                                if (estornouComSucesso) {
                                    qtd_transacoes_estornadas_sucesso++;
                                    alterarSituacaoDaTransacao(codTransacao, SituacaoTransacaoEnum.ESTORNADA, con);
                                } else {
                                    qtd_transacoes_estornadas_erro++;
                                }
                                continue;
                            } else {
                                throw ex;
                            }
                        }
                        if (UteisValidacao.emptyNumber(reciboGerado)) {
                            ++qtd_transacoes_gerar_recibo_erro;
                            throw new Exception("Não foi gerado recibo da transação");
                        } else if (gerouReciboSucesso) {
                            alterarSituacaoDaTransacao(codTransacao, SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO, con);
                        }
                        msg += (" | Recibo Gerado: " + reciboGerado);
                        ++qtd_transacoes_gerar_recibo;

                        throw new Exception("Recibo gerado - Venda com o cartão da transação no dia e as parcelas estão em aberto, pode ser gerado o recibo.");
                    } else {
                        ++qtd_transacoes_sem_pagamento_extrato;
                        alterarSituacaoDaTransacao(codTransacao, SituacaoTransacaoEnum.NAO_APROVADA, con);
                        throw new Exception("Não foi encontrado o pagamento na conciliação");
                    }
                } catch (Exception ex) {
                    msg += (" | Mensagem: " + ex.getMessage());
                } finally {
                    adicionarLog(msg);
                }
            }

            adicionarLog("--------------------------- INFORMAÇÕES ---------------------------");
            adicionarLog(qtd_transacoes + " - Total de transações");
            adicionarLog(qtd_transacoes_sem_pagamento_extrato + " - Transações que não foi encontrado pagamento na conciliação");
            adicionarLog(qtd_transacoes_pagas_encontradas_conciliacao + " - Transações que foi encontrado o registro na conciliação");
            adicionarLog(qtd_transacoes_parcelas_pagas + " - Transações com parcelas que já foram pagas (Pagamento Duplicado)");
            adicionarLog(qtd_transacoes_gerar_recibo + " - Transações que foi gerado o recibo");
            adicionarLog(qtd_transacoes_gerar_recibo_erro + " - Transações que não foi possível gerar o recibo");
            adicionarLog("--------------------------- OUTRAS INFORMAÇÕES ---------------------------");
            adicionarLog(qtd_transacoes_sem_conciliacao + " - Transação que não foi possível consultar a conciliação do stone code ou do dia");
            adicionarLog(qtd_transacoes_cartao_encontrado_outros_dados_nao_bate + " - Transações que foi encontrado o cartão na conciliação porem a data/hora ou valor não condiz com a transação");
            adicionarLog(qtd_transacoes_sem_parcela_encontrada + " - Transações que não foi possível gerar o recibo pois não foi encontrado parcelas para a transação");
            adicionarLog(qtd_transacoes_estornadas_sucesso + " - Transações que foram estornadas com sucesso");
            adicionarLog(qtd_transacoes_estornadas_erro + " - Transações que não foi possível estornar");
            adicionarLog("---------------------------------------------------------------------------------");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            adicionarLog("--------------------------- FIM PROCESSO ---------------------------");
//            Uteis.salvarArquivo(ProcessoIdentificarCobrancasStoneAprovadasInterrogacao.class.getSimpleName() + "_" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\Processos\\" + File.separator);
        }
    }

    private static void alterarSituacaoDaTransacao(Integer codTransacao, SituacaoTransacaoEnum situacao, Connection con) throws Exception {
        Transacao transacaoDAO;
        try {
            transacaoDAO = new Transacao(con);
            adicionarLog("Alterando situação da parcela no banco de dados para: " + situacao.getDescricao());
            TransacaoVO transacaoVO = new TransacaoVO();
            transacaoVO.setCodigo(codTransacao);
            transacaoVO.setSituacao(situacao);
            transacaoDAO.alterarSituacao(transacaoVO);
            adicionarLog("Situação da transação foi alterada com sucesso para: " + situacao.getDescricao());
        } catch (Exception ex2) {
            adicionarLog("Erro ao tentar alterar a situação da transação: " + ex2.getMessage());
        } finally {
            transacaoDAO = null;
        }
    }

    private static void preencherConciliacaoDoDiaStoneCode(Date dia, String stoneCode) throws Exception {
        try {
            String conci = mapaStoneCode.get(chaveMapaConciliacao(dia, stoneCode));
            if (UteisValidacao.emptyString(conci)) {
                conci = StoneOnlineServiceConciliation.executarRequestStone(stoneCode, Calendario.getDataAplicandoFormatacao(dia, "yyyyMMdd"));
                mapaStoneCode.put(chaveMapaConciliacao(dia, stoneCode), conci);
            }
            if (UteisValidacao.emptyString(conci)) {
                throw new Exception("Não obteve conciliação");
            }
        } catch (Exception ex) {
            throw new Exception("Não foi possível obter a conciliação | StoneCode " + stoneCode + " | Dia " + Calendario.getDataAplicandoFormatacao(dia, "dd/MM/yyyy") + " | " + ex.getMessage());
        }
    }

    private static String chaveMapaConciliacao(Date dia, String stoneCode) {
        return stoneCode + "_" + Calendario.getDataAplicandoFormatacao(dia, "yyyyMMdd");
    }

    public static JSONObject validarParcelas(Integer transacao, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("t.codigo,\n");
        sql.append("count(distinct tm.movparcela) as qtd_parcelas,\n");
        sql.append("sum(case when mp.situacao = 'EA' then 1 else 0 end) as qtd_parcelas_emaberto,\n");
        sql.append("sum(case when mp.situacao = 'PG' then 1 else 0 end) as qtd_parcelas_pagas\n");
        sql.append("from transacao t \n");
        sql.append("left join transacaomovparcela tm on tm.transacao = t.codigo \n");
        sql.append("left join movparcela mp on mp.codigo = tm.movparcela\n");
        sql.append("where t.codigo = ").append(transacao).append(" \n");
        sql.append("group by 1 ");
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        JSONObject json = new JSONObject();
        if (rs.next()) {
            Integer qtd_parcelas = rs.getInt("qtd_parcelas");
            Integer qtd_parcelas_emaberto = rs.getInt("qtd_parcelas_emaberto");
            Integer qtd_parcelas_pagas = rs.getInt("qtd_parcelas_pagas");
            if (qtd_parcelas_pagas > 0) {
                ++qtd_transacoes_parcelas_pagas;
                alterarSituacaoDaTransacao(transacao, SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO, con);
                throw new Exception("Parcelas Pagas! Qtd Total: " + qtd_parcelas + " | Qtd Paga: " + qtd_parcelas_pagas);
            }
        }
        return json;
    }

    private static String sqlTransacoesInterrogacao() {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT \n");
        sb.append("cl.matricula, \n");
        sb.append("p.nome, \n");
        sb.append("t.codigo AS codtransacao, \n");
        sb.append("t.paramsenvio , \n");
        sb.append("t.valor , \n");
        sb.append("t.dataprocessamento as data, \n");
        sb.append("e.nome AS empresa, \n");
        sb.append("COALESCE(\n" +
                "  NULLIF(split_part(split_part(t.outrasinformacoes, 'codigoAutenticacao01\":\"', 2), '\"', 1), ''),\n" +
                "  split_part(split_part(t.outrasinformacoes, 'codigoAutenticacao01_ANTERIOR\":\"', 2), '\"', 1)\n" +
                ") as stonecode,\n");
        sb.append("COALESCE(\n" +
                "  NULLIF(split_part(split_part(t.outrasinformacoes, 'codigoAutenticacao02\":\"', 2), '\"', 1), ''),\n" +
                "  split_part(split_part(t.outrasinformacoes, 'codigoAutenticacao02_ANTERIOR\":\"', 2), '\"', 1)\n" +
                ") as sak, \n");
        sb.append("split_part(split_part(t.outrasinformacoes, 'cartaoMascarado\":\"', 2), '\"', 1) as cartaoMascarado \n");
        sb.append("FROM transacao t \n");
        sb.append("INNER JOIN empresa e ON e.codigo = t.empresa \n");
        sb.append("INNER JOIN pessoa p on p.codigo = t.pessoapagador \n");
        sb.append("LEFT JOIN cliente cl on cl.pessoa = p.codigo \n");
        sb.append("WHERE t.codigoretorno = '?' \n");
        sb.append("AND t.tipo = 11 \n");
        sb.append("AND t.situacao = 2 \n");
        sb.append("AND t.recibopagamento is null  \n");
        sb.append("AND t.dataprocessamento::date >= '" + data_inicio + "' AND t.dataprocessamento::date <= '" + data_fim + "' \n");
        sb.append("AND (t.codigoretornodescricao ilike '%Houve algum problema de comunicação com o E-Commerce da Stone%' \n");
        sb.append("OR erroprocessamento ilike '%Houve algum problema de comunicação com o E-Commerce da Stone%') \n");
        sb.append("ORDER BY t.codigo \n");
        return sb.toString();
    }

    private static boolean tentarEstornarTransacao(String codTransacao, String sak, String itk, String stoneCode, String captureLocalDateTime, Double capturedAmount) throws Exception {
        try {
            adicionarLog("INICIANDO TENTATIVA DE ESTORNO DA TRANSAÇÃO - CÓD: " + codTransacao + " | SAK: " + sak + " | Stone Code: " + stoneCode + " | ITK: " + itk);
            boolean sucesso = cancelarTransacao(sak, itk, stoneCode, captureLocalDateTime, capturedAmount);
            if (sucesso) {
                adicionarLog("Estorno realizado com sucesso");
            } else {
                adicionarLog("Não foi possível realizar o estorno");
            }
            return sucesso;
        } catch (Exception ex) {
            return false;
        }
    }

    public static String consultarSituacaoCobrancaTransacao(String sak, String identificador) throws Exception {
        if (UteisValidacao.emptyString(sak)) {
            throw new Exception("Sak não informado");
        }

        if (UteisValidacao.emptyString(identificador)) {
            throw new Exception("Identificador não informado");
        }

        StringBuilder xmlConsulta = new StringBuilder();
        xmlConsulta.append("<Document xmlns=\"urn:AcceptorTransactionStatusReportRequestV02.1\">");
        xmlConsulta.append("	<AccptrTxStsRptRq>");
        xmlConsulta.append("		<Hdr>");
        xmlConsulta.append("			<MsgFctn>TSRR</MsgFctn>");
        xmlConsulta.append("			<PrtcolVrsn>2.0</PrtcolVrsn>");
        xmlConsulta.append("			<InitgPty>");
        xmlConsulta.append("				<Id>").append(sak).append("</Id>");
        xmlConsulta.append("			</InitgPty>");
        xmlConsulta.append("		</Hdr>");
        xmlConsulta.append("		<TxStsRpt>");
        xmlConsulta.append("			<Tx>");
        xmlConsulta.append("            <TxRpt>OPRS</TxRpt>");
        xmlConsulta.append("            <TxRpt>SUMM</TxRpt>");
        xmlConsulta.append("				<OrgnlTx>");
        xmlConsulta.append("					<InitrTxId>").append(identificador).append("</InitrTxId>");
        xmlConsulta.append("				</OrgnlTx>");
        xmlConsulta.append("			</Tx>");
        xmlConsulta.append("		</TxStsRpt>");
        xmlConsulta.append("	</AccptrTxStsRptRq>");
        xmlConsulta.append("</Document>");

        String resposta = "";
        try {
            resposta = executarRequestConsultaTransacaoPorItkStone(xmlConsulta.toString());
            return resposta;
        } catch (Exception ex) {
            adicionarLog("Erro ao realizar a request de consulta DE COBRANÇA na Stone: " + ex.getMessage());
        }
        return "";
    }

    public static boolean cancelarTransacao(String sak, String itk, String stoneCode, String captureLocalDateTime, Double capturedAmount) throws Exception {
        String response = "";
        try {
            DocumentAcceptorCancellationRequestV0_21 paramsCancelamento = criarParametrosEnvioCancelamento(sak, itk, stoneCode, captureLocalDateTime, capturedAmount);
            response = executarRequestCancelamentoTransacaoPorItkStone(paramsCancelamento.toXML());
            JSONObject jsonRespCancelamento = XML.toJSONObject(response);
            boolean canceladoComSucesso = jsonRespCancelamento.getJSONObject("Document").getJSONObject("AccptrCxlRspn").
                    getJSONObject("CxlRspn").getJSONObject("TxRspn").getJSONObject("AuthstnRslt").
                    getJSONObject("RspnToAuthstn").optString("Rspn").equals("APPR");
            return canceladoComSucesso;
        } catch (Exception e) {
            throw e;
        }
    }

    private static String executarRequestCancelamentoTransacaoPorItkStone(String xmlBody) throws Exception {

        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", APPLICATION_XML);

        String UrlEndpoint = "https://e-commerce.stone.com.br/Cancellation";

        try {
            return ExecuteRequestHttpService.executeHttpRequest(UrlEndpoint, xmlBody, headersMap, METODO_POST, Charsets.UTF_8.name());
        } catch (Exception e) {
            adicionarLog("Erro ao realizar a request de estorno DE COBRANÇA na Stone");
            throw e;
        }

    }

    private static boolean podeTentarCancelar(String retornoXML) {

        boolean foiCobrado = retornoXML.toUpperCase().contains("<AuthrsdSts>FULL</AuthrsdSts>".toUpperCase());
        boolean foiCancelado = retornoXML.toUpperCase().contains("<CancSts>FULL</CancSts>".toUpperCase());

        if (foiCobrado && foiCancelado) {
            adicionarLog("cobrança já estava cancelada...");
        }

        if (foiCobrado && !foiCancelado) {
            return true;
        }
        return false;
    }

    private static String executarRequestConsultaTransacaoPorItkStone(String xmlBody) throws Exception {

        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", APPLICATION_XML);

        String UrlEndpoint = "https://e-commerce.stone.com.br/TransactionStatusReport";
        return ExecuteRequestHttpService.executeHttpRequest(UrlEndpoint, xmlBody, headersMap, METODO_POST, Charsets.UTF_8.name());
    }

    private static void adicionarLog(String msg) {
        String s = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy hh:mm:ss") + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    private static DocumentAcceptorCancellationRequestV0_21 criarParametrosEnvioCancelamento(String sak, String itk, String stoneCode, String captureLocalDateTime, Double capturedAmount) throws Exception {
//        // Converter para Double e arredondar para duas casas decimais
//        Double amount = Math.round(capturedAmount * 100) / 100.0;
//
//        // Multiplicar por 100 para remover as casas decimais
//        int amountWithoutDecimals = (int) (amount * 100);
//
//        // Converter para String e remover o ponto
//        String finalAmount = String.valueOf(amountWithoutDecimals);

        DocumentAcceptorCancellationRequestV0_21Builder doc;
        doc = DocumentAcceptorCancellationRequestV0_21Builder.init();
        return doc.comSaleAffiliationKeySAK(sak)
                .comCodigoIdentificacaoDoPontoDeInteracao(stoneCode) //obter stonecode pelo sak
                .comDataHoraLocalDaTransacaoDoPontoDeInteracao(
                        captureLocalDateTime
                )
                .comValorTotalTransacaoEmCentavosASerCancelado(
                        String.valueOf(capturedAmount).replace(".", "")
                )
                .comIdentificacaoTransacaoSistemaITK(
                        itk
                )
                .build();
    }

    public static String obterItemOutrasInformacoes(AtributoTransacaoEnum atributoEnum, String outrasInformacoes) {
        try {
            JSONObject jsonOutrasInformacoes = new JSONObject(outrasInformacoes);
            try {
                return jsonOutrasInformacoes.get(atributoEnum.name()).toString();
            } catch (Exception ex) {
//                Uteis.logarPrintStackTrace(ex);
            }
            return jsonOutrasInformacoes.getString(atributoEnum.name());
        } catch (Exception ex) {
//            Uteis.logarPrintStackTrace(ex);
            return "";
        }
    }
}


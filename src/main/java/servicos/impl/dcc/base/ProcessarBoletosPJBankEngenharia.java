/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import servicos.integracao.pjbank.recebimento.BoletosManager;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Felipe
 */
public class ProcessarBoletosPJBankEngenharia extends SuperEntidade {

    public ProcessarBoletosPJBankEngenharia() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;

            String chave = "1c81b0d9bb3b592b05ee1558e154137c";
            Integer diasProcessar = 30;
//            if (args.length > 1) {
//                diasProcessar = Integer.parseInt(args[1]);
//            }

            Connection con = DriverManager.getConnection("************************************/bdzillyonengenhariadocorporizzo", "postgres", "pactodb");
//            Connection con = DriverManager.getConnection("***********************************************************", "postgres", "pactodb");

            Uteis.logar(null, "Obter conexão para chave: " + chave);
            Uteis.logar(null, "Números dias processar: " + diasProcessar);
//            Connection con = new DAO().obterConexaoEspecifica(chave);
//            Conexao.guardarConexaoForJ2SE(chave, con);

//            processarConsultaTodosOsBancos();

//            processarBoletos(diasProcessar, con);
//            verificarBoletoPago(con, diasProcessar);
//            buscarInformacoesBoletosPJBank(con);
            processarBoletosPendentes(con, diasProcessar);
        } catch (Exception ex) {
            Logger.getLogger(ProcessarBoletosPJBankEngenharia.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private static void processarConsultaTodosOsBancos() throws Exception {
        Connection conOAMD = DriverManager.getConnection("************************************/OAMD", "postgres", "pactodb");
        String sql = "SELECT datname as banco FROM pg_database WHERE datistemplate = false and datname ilike 'bdzillyonengenharia%'";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, conOAMD);
        while (rs.next()) {
            String banco = rs.getString("banco");
            try (Connection con = DriverManager.getConnection("************************************/" + banco, "postgres", "pactodb")) {
                ResultSet rs2 = SuperFacadeJDBC.criarConsulta("select count(*) as qtd from boleto where recibopagamento is null and length(coalesce(idimportacao,'')) > 0 and length(coalesce(idexterno,'')) = 0 and situacao = 5", con);
                while (rs2.next()) {
                    Uteis.logarDebug(banco + " - " + rs2.getInt("qtd"));
                    if (!UteisValidacao.emptyNumber(rs2.getInt("qtd"))) {
                        String update = "update boleto set situacao = " + SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo() + " where recibopagamento is null and length(coalesce(idimportacao,'')) > 0 and length(coalesce(idexterno,'')) = 0 and situacao = 5";
                        SuperFacadeJDBC.executarUpdate(update, con);
                    }
                }
            } catch (Exception ex) {
                Uteis.logarDebug("ERRO " + banco + " - " + ex.getMessage());
            }
        }
    }

    private static void processarBoletos(Integer diasProcessarPJBank, Connection con) {
        Empresa empresaDAO;
        RemessaService remessaService;
        try {
            empresaDAO = new Empresa(con);
            remessaService = new RemessaService(con);
            List<EmpresaVO> listaEmpresas = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (EmpresaVO empresa : listaEmpresas) {
                Uteis.logar(null, "## Iniciando processarBoletosOnline | Empresa: " + empresa.getNome());
                remessaService.processarBoletosOnline(Calendario.hoje(), empresa, diasProcessarPJBank);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            remessaService = null;
        }
    }


    private static void processarBoletosPendentes(Connection con, Integer diasProcessar) {
        Boleto boletoDAO;
        Usuario usuarioDAO;
        try {
            boletoDAO = new Boleto(con);
            usuarioDAO = new Usuario(con);

//            Integer atual = 43965; //antes de colocar vencimento
//            Integer atual = 51161; //com filtro de vencimento
            Integer atual = 0; //com filtro de vencimento
            while (true) {
                StringBuilder sql = new StringBuilder();
                sql.append("select \n");
                sql.append("b.* \n");
                sql.append("from boleto b \n");
                sql.append("where coalesce(b.idimportacao,'') <> '' \n");
//                sql.append("and b.situacao in (0,3,4) \n");
                sql.append("and length(coalesce(b.idexterno,'')) > 0 \n");
                sql.append("and ( \n");
                sql.append("(b.situacao in (0,3,4) and b.datavencimento::date <= current_date and b.datavencimento::date >= (current_date - ").append(diasProcessar).append(")) \n");
                sql.append("or (b.situacao in (0,3,4) and length(coalesce(b.linkboleto,'')) = 0 and length(coalesce(b.idexterno,'')) > 0) \n");
                sql.append("or (situacao = 6 and length(coalesce(b.idexterno,'')) > 0 and b.datavencimento::date > current_date) \n");
                sql.append(") \n");
                sql.append("and b.codigo > ").append(atual).append(" \n");
                sql.append("order by b.codigo \n");
                sql.append("limit 250 \n");
                List<BoletoVO> lista;
                try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                    try (ResultSet rs = ps.executeQuery()) {
                        lista = boletoDAO.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    }
                }

                if (UteisValidacao.emptyList(lista)) {
                    break;
                }

                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();


                int i = 0;
                for (BoletoVO boletoVO : lista) {
                    StringBuilder msg = new StringBuilder();
                    msg.append("## Processamento Boletos Pendentes | Sincronizar | ").append(++i).append("/").append(lista.size());
                    try {
                        msg.append(" | Boleto ").append(boletoVO.getCodigo());
                        String retorno = boletoDAO.sincronizarBoleto(boletoVO, usuarioVO, "ProcessarBoletosPJBank - processarBoletos");
                        msg.append(" | Retorno: ").append(retorno);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        msg.append(" | Erro: ").append(ex.getMessage());
                    } finally {
                        Uteis.logarDebug(msg.toString());
                        atual = boletoVO.getCodigo();
                    }
                }
            }

            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("FIM!!!!!!");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            boletoDAO = null;
            usuarioDAO = null;
        }
    }

    private static void verificarBoletoPago(Connection con, Integer diasRegredirProcessar) {
        Empresa empresaDAO;
        RemessaService remessaService;
        try {
            empresaDAO = new Empresa(con);
            remessaService = new RemessaService(con);

//            Date dia = Calendario.getDate("yyyyMMdd", "********");
            Date dia = Calendario.hoje();

            List<EmpresaVO> listaEmpresas = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (EmpresaVO empresaVO : listaEmpresas) {
                remessaService.processarPagamentosPJBank(dia, empresaVO, diasRegredirProcessar);
            }

            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("FIM!!!!!!");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            empresaDAO = null;
            remessaService = null;
        }
    }

    public static void buscarInformacoesBoletosPJBank(Connection con) {
        Boleto boletoDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            boletoDAO = new Boleto(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            List<ItemRelatorioTO> diasProcessar = new ArrayList<>();

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("conveniocobranca, \n");
            sql.append("datavencimento as data_filtro \n");
            sql.append("from boleto \n");
            sql.append("where length(coalesce(idimportacao,'')) > 0 \n");
            sql.append("and length(coalesce(idexterno,'')) = 0 \n");
            sql.append("and datavencimento::date > '").append(Uteis.getDataFormatoBD(Calendario.somarMeses(Calendario.hoje(), -1))).append("' \n");
            sql.append("group by 1,2 \n");
            sql.append("order by 2 \n");
            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    while (rs.next()) {
                        ItemRelatorioTO item = new ItemRelatorioTO();
                        item.setConvenio(rs.getInt("conveniocobranca"));
                        item.setDataCadastro(rs.getDate("data_filtro"));
                        diasProcessar.add(item);
                    }
                }
            }

//            StringBuilder sql2 = new StringBuilder();
//            sql2.append("select \n");
//            sql2.append("* \n");
//            sql2.append("from boleto \n");
//            sql2.append("where situacao = 4 \n");
//            sql2.append("and length(coalesce(idexterno,'')) = 0 \n");
//            sql2.append("order by codigo \n");
//            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql2.toString(), con);
//            List<BoletoVO> listaBoletos = boletoDAO.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            int iDia = 0;
            Map<Integer, ConvenioCobrancaVO> mapConve = new HashMap<>();
            for (ItemRelatorioTO item : diasProcessar) {
                Uteis.logarDebug("## Consultar informacoes boletos | " + ++iDia + "/" + diasProcessar.size() + " | Dia " + item.getDataCadastroApresentar());
                try {
                    ConvenioCobrancaVO convenioCobrancaVO = mapConve.get(item.getConvenio());
                    if (convenioCobrancaVO == null) {
                        convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(item.getConvenio(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        mapConve.put(convenioCobrancaVO.getCodigo(), convenioCobrancaVO);
                    }

                    BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);

                    int pagina = 0;
                    int erros = 0;
                    while (true) {
                        try {
                            pagina++;

                            Uteis.logarDebug("Pesquisando boletos na API da PJBank | Página: " + pagina);
                            String response = boletosManager.getBoletos(false, item.getDataCadastro(), item.getDataCadastro(), pagina);
                            JSONArray array = new JSONArray(response);
                            int i = 0;
                            for (int e = 0; e < array.length(); e++) {
                                String retorno = "";
                                String id_unico = "";
                                String pedido_numero = "";
                                String nomePagador = "";
                                String vencimento = "";
                                double valor = 0.0;

                                try {
                                    JSONObject obj = array.getJSONObject(e);
                                    id_unico = obj.optString("id_unico");
                                    pedido_numero = obj.optString("pedido_numero");
                                    nomePagador = obj.optString("pagador");
                                    vencimento = obj.optString("data_vencimento");
                                    valor = obj.getDouble("valor_original");

                                    processarIdentificador(id_unico, pedido_numero, nomePagador, vencimento, valor, con);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    retorno = ex.getMessage();
                                } finally {
//                                    Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | " + ++i + "/" + array.length() + " | IdUnico " + id_unico + " | " + retorno);
                                }
                            }

//                            Recomendado buscar a próxima página quando o sistema retornar 50 itens na página atual.
                            if (array.length() != 50) {
//                                Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | sair while | array " + array.length());
                                break;
                            }

                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | ERRO: " + ex.getMessage());
                            erros++;
                            if (erros == 3) {
                                Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | sair while | erros " + erros);
                                break;
                            }
                        }
                    }


                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("FIM!!!!!!");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
            Uteis.logarDebug("###################");
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    private static void processarIdentificador(String id_unico, String pedido, String nomePagador, String vencimento, double valor, Connection con) throws Exception {
        String sql = "select codigo from boleto where idimportacao = '" + pedido + "' and length(coalesce(idexterno,'')) = 0;";
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        if (rs.next()) {
            Integer codigoBoleto = rs.getInt("codigo");
            SuperFacadeJDBC.executarUpdate("update boleto set idexterno = '" + id_unico + "' where codigo = " + codigoBoleto, con);
            Uteis.logarDebug("Ajustado boleto " + codigoBoleto + " pelo idImportação");
        } else {
            //se não encontrou no zw pelo idImportacao, pesquisar pelo nomePagador, vencimento e valor.
            String sql2 = "select b.codigo \n" +
                    "from boleto b\n" +
                    "inner join pessoa p on p.codigo = b.pessoa\n" +
                    "where (b.idexterno = '' or b.idexterno is null)\n" +
                    "and ( \n" +
                    "   upper(trim(p.nome)) = '" + nomePagador.toUpperCase().trim() + "' \n" +
                    "   or upper(trim(unaccent(p.nome))) = '" + Uteis.retirarAcentuacao(nomePagador.toUpperCase().trim()) + "' \n" +
                    ") \n " +
                    "and b.datavencimento = '" + Calendario.getDataAplicandoFormatacao(new Date(vencimento),"yyyy-MM-dd") + "'\n" +
                    "and trunc(b.valor::numeric,2) = trunc(" + valor + "::numeric,2)";
            ResultSet rs2 = SuperFacadeJDBC.criarConsulta(sql2, con);
            if (rs2.next()) {
                Integer codigoBoleto = rs2.getInt("codigo");
                SuperFacadeJDBC.executarUpdate("update boleto set idexterno = '" + id_unico + "' where codigo = " + codigoBoleto, con);
                Uteis.logarDebug("Ajustado boleto " + codigoBoleto + " pelo nomePagador");
            }
        }
    }
}

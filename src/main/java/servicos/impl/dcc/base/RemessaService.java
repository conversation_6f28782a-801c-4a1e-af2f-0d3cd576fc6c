 /*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

 import br.com.pactosolucoes.atualizadb.processo.ProcessosPJBank;
 import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
 import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
 import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
 import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
 import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
 import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
 import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
 import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
 import br.com.pactosolucoes.comuns.util.FileUtilities;
 import br.com.pactosolucoes.comuns.util.Formatador;
 import br.com.pactosolucoes.comuns.util.JSFUtilities;
 import br.com.pactosolucoes.comuns.util.StringUtilities;
 import br.com.pactosolucoes.ecf.cupomfiscal.comuns.servico.impl.CupomFiscalServiceImpl;
 import br.com.pactosolucoes.encrypt.gpg.PgpEncryption;
 import br.com.pactosolucoes.enumeradores.BIEnum;
 import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
 import br.com.pactosolucoes.enumeradores.TipoCobrancaPactoEnum;
 import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
 import br.com.pactosolucoes.enumeradores.TipoInfoMigracaoEnum;
 import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
 import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
 import br.com.pactosolucoes.integracao.aragorn.AragornService;
 import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
 import br.com.pactosolucoes.integracao.pactopay.PactoPayService;
 import br.com.pactosolucoes.oamd.controle.basico.DAO;
 import controle.arquitetura.SuperControle;
 import controle.financeiro.EstornoReciboControle;
 import controle.financeiro.GestaoRemessasControle;
 import negocio.comuns.arquitetura.ResultadoServicosVO;
 import negocio.comuns.arquitetura.RoboVO;
 import negocio.comuns.arquitetura.UsuarioVO;
 import negocio.comuns.basico.AcoesStatusRemessaVO;
 import negocio.comuns.basico.CidadeVO;
 import negocio.comuns.basico.ClienteVO;
 import negocio.comuns.basico.ColaboradorVO;
 import negocio.comuns.basico.ConfiguracaoSistemaVO;
 import negocio.comuns.basico.EmpresaVO;
 import negocio.comuns.basico.EnderecoVO;
 import negocio.comuns.basico.EstadoVO;
 import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
 import negocio.comuns.basico.PessoaVO;
 import negocio.comuns.basico.enumerador.AcoesRemessasEnum;
 import negocio.comuns.basico.enumerador.IdentificadorMensagemSistema;
 import negocio.comuns.basico.enumerador.ServicoEnum;
 import negocio.comuns.basico.enumerador.TipoCategoriaClubeEnum;
 import negocio.comuns.basico.enumerador.TipoNotificacaoUsuarioEnum;
 import negocio.comuns.basico.enumerador.TipoPessoa;
 import negocio.comuns.contrato.ContratoVO;
 import negocio.comuns.contrato.MovProdutoVO;
 import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
 import negocio.comuns.crm.GenericoTO;
 import negocio.comuns.financeiro.AdquirenteVO;
 import negocio.comuns.financeiro.BoletoVO;
 import negocio.comuns.financeiro.CaixaAbertoTO;
 import negocio.comuns.financeiro.CartaoCreditoVO;
 import negocio.comuns.financeiro.CartaoTentativaVO;
 import negocio.comuns.financeiro.ConvenioCobrancaVO;
 import negocio.comuns.financeiro.EstornoReciboVO;
 import negocio.comuns.financeiro.FormaPagamentoVO;
 import negocio.comuns.financeiro.ItemRetornoRemessaTO;
 import negocio.comuns.financeiro.MovContaRateioVO;
 import negocio.comuns.financeiro.MovContaVO;
 import negocio.comuns.financeiro.MovPagamentoVO;
 import negocio.comuns.financeiro.MovParcelaVO;
 import negocio.comuns.financeiro.MovProdutoParcelaVO;
 import negocio.comuns.financeiro.NFSeEmitidaVO;
 import negocio.comuns.financeiro.ObjetoGenerico;
 import negocio.comuns.financeiro.OperadoraCartaoVO;
 import negocio.comuns.financeiro.ReciboPagamentoVO;
 import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
 import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
 import negocio.comuns.financeiro.RemessaItemVO;
 import negocio.comuns.financeiro.RemessaVO;
 import negocio.comuns.financeiro.RetornoRemessaVO;
 import negocio.comuns.financeiro.SuperRemessaItemVO;
 import negocio.comuns.financeiro.TransacaoVO;
 import negocio.comuns.financeiro.VendaAvulsaVO;
 import negocio.comuns.financeiro.enumerador.*;
 import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
 import negocio.comuns.plano.enumerador.TipoProduto;
 import negocio.comuns.utilitarias.Calendario;
 import negocio.comuns.utilitarias.ConsistirException;
 import negocio.comuns.utilitarias.NotificacaoUsuarioVO;
 import negocio.comuns.utilitarias.Ordenacao;
 import negocio.comuns.utilitarias.OrdenacaoMovParcelaPagamento;
 import negocio.comuns.utilitarias.ThreadEnviarEmail;
 import negocio.comuns.utilitarias.Uteis;
 import negocio.comuns.utilitarias.UteisEmail;
 import negocio.comuns.utilitarias.UteisValidacao;
 import negocio.comuns.utilitarias.UtilReflection;
 import negocio.facade.jdbc.arquitetura.SuperEntidade;
 import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
 import negocio.facade.jdbc.arquitetura.Usuario;
 import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
 import negocio.facade.jdbc.basico.Cliente;
 import negocio.facade.jdbc.basico.Colaborador;
 import negocio.facade.jdbc.basico.ConfiguracaoSistema;
 import negocio.facade.jdbc.basico.Empresa;
 import negocio.facade.jdbc.basico.Estado;
 import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
 import negocio.facade.jdbc.basico.PactoPayConfig;
 import negocio.facade.jdbc.basico.Pessoa;
 import negocio.facade.jdbc.crm.Feriado;
 import negocio.facade.jdbc.financeiro.AcoesStatusRemessa;
 import negocio.facade.jdbc.financeiro.Adquirente;
 import negocio.facade.jdbc.financeiro.Boleto;
 import negocio.facade.jdbc.financeiro.CartaoTentativa;
 import negocio.facade.jdbc.financeiro.ConvenioCobranca;
 import negocio.facade.jdbc.financeiro.FormaPagamento;
 import negocio.facade.jdbc.financeiro.MovPagamento;
 import negocio.facade.jdbc.financeiro.MovParcela;
 import negocio.facade.jdbc.financeiro.MovParcelaResultadoCobranca;
 import negocio.facade.jdbc.financeiro.MovProdutoParcela;
 import negocio.facade.jdbc.financeiro.NFSeEmitida;
 import negocio.facade.jdbc.financeiro.OperadoraCartao;
 import negocio.facade.jdbc.financeiro.ReciboPagamento;
 import negocio.facade.jdbc.financeiro.Remessa;
 import negocio.facade.jdbc.financeiro.RemessaItem;
 import negocio.facade.jdbc.financeiro.RemessaItemMovParcela;
 import negocio.facade.jdbc.financeiro.Transacao;
 import negocio.facade.jdbc.notaFiscal.NotaFiscal;
 import negocio.facade.jdbc.plano.ConvenioCobrancaEmpresa;
 import negocio.facade.jdbc.utilitarias.Conexao;
 import negocio.facade.jdbc.utilitarias.NotificacaoUsuario;
 import negocio.oamd.EmpresaFinanceiroVO;
 import org.bouncycastle.jce.provider.BouncyCastleProvider;
 import org.jboleto.JBoleto;
 import org.jboleto.JBoletoBean;
 import org.jboleto.bancos.BancoSicredi;
 import org.json.JSONArray;
 import org.json.JSONObject;
 import relatorio.negocio.comuns.basico.ResultadoBITO;
 import relatorio.negocio.comuns.financeiro.DescontoBoletoTO;
 import servicos.AdministrativoRunner;
 import servicos.EmailNotificacao;
 import servicos.adm.CreditoDCCService;
 import servicos.http.RespostaHttpDTO;
 import servicos.impl.apf.APF;
 import servicos.impl.apf.RecorrenciaService;
 import servicos.impl.boleto.AtributoBoletoEnum;
 import servicos.impl.boleto.BancoEnum;
 import servicos.impl.boleto.BoletoOnlineService;
 import servicos.impl.boleto.LayoutBoletoPadrao;
 import servicos.impl.boleto.asaas.BoletoAsaasService;
 import servicos.impl.boleto.bancobrasil.BancoBrasilService;
 import servicos.impl.boleto.brb.LayoutRemessaBRBCNAB400;
 import servicos.impl.boleto.caixa.CaixaService;
 import servicos.impl.boleto.daycoval.LayoutRemessaDaycovalBoletoCNAB400;
 import servicos.impl.boleto.itau.ItauService;
 import servicos.impl.dcc.bb.DCOBBStatusEnum;
 import servicos.impl.dcc.bb.LayoutRemessaBBCNAB240;
 import servicos.impl.dcc.bb.LayoutRemessaBBDCO;
 import servicos.impl.dcc.bin.LayoutRemessaBinDCC;
 import servicos.impl.dcc.bnb.LayoutRemessaBNBCNAB400;
 import servicos.impl.dcc.bradesco.DCOBradescoOcorrenciaEnum;
 import servicos.impl.dcc.bradesco.LayoutRemessaBradescoCNAB240;
 import servicos.impl.dcc.caixa.LayoutRemessaCaixaCNAB240;
 import servicos.impl.dcc.caixa.LayoutRemessaCaixaSIACC150DCO;
 import servicos.impl.dcc.cielo.DCCCieloStatusEnum;
 import servicos.impl.dcc.cielo.LayoutRemessaCieloDCC;
 import servicos.impl.dcc.getnet.DCOGetNetStatusEnum;
 import servicos.impl.dcc.itau.BBCnab400ItauStatusEnum;
 import servicos.impl.dcc.itau.DCOItauStatusEnum;
 import servicos.impl.dcc.itau.LayoutRemessaItauBoletoCNAB400;
 import servicos.impl.dcc.itau.LayoutRemessaItauCNAB400BoletoAtualizado;
 import servicos.impl.dcc.itau.LayoutRemessaItauDCO;
 import servicos.impl.dcc.safra.LayoutRemessaSafraCNAB400;
 import servicos.impl.dcc.sicoob.Cnab240SicoobStatusEnum;
 import servicos.impl.dcc.sicoob.LayoutRemessaSicoobCNAB240;
 import servicos.impl.dcc.sicoob.LayoutRemessaSicoobCNAB400;
 import servicos.impl.dco.febraban.LayoutRemessaFebrabanDCO;
 import servicos.impl.dco.santander.LayoutRemessaSantanderDCO;
 import servicos.impl.email.ModeloMensagemSistemaService;
 import servicos.impl.gatewaypagamento.PagamentoService;
 import servicos.impl.gatewaypagamento.VerificadorTransacaoService;
 import servicos.integracao.impl.conciliadora.ConciliadoraServiceImpl;
 import servicos.integracao.impl.parceirofidelidade.base.ParceiroFidelidadeZW;
 import servicos.integracao.pjbank.recebimento.BoletosManager;
 import servicos.operacoes.EstornoContratoAutomaticoService;
 import servicos.pix.PixScheduleService;
 import servicos.pix.PixService;
 import servicos.propriedades.PropsService;
 import servicos.remessa.to.ProcessoRetornoRemessaTO;
 import servicos.util.SFTP;

 import javax.swing.text.MaskFormatter;
 import java.io.ByteArrayInputStream;
 import java.io.File;
 import java.io.FileInputStream;
 import java.io.InputStream;
 import java.math.BigDecimal;
 import java.security.Security;
 import java.sql.Connection;
 import java.sql.PreparedStatement;
 import java.sql.ResultSet;
 import java.util.ArrayList;
 import java.util.Arrays;
 import java.util.Calendar;
 import java.util.Collections;
 import java.util.Date;
 import java.util.HashMap;
 import java.util.HashSet;
 import java.util.List;
 import java.util.Map;
 import java.util.Set;
 import java.util.StringTokenizer;
 import java.util.Vector;
 import java.util.logging.Level;
 import java.util.logging.Logger;
 import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class RemessaService extends SuperEntidade {

    private List<EmpresaVO> listaEmpresas;
    private StringBuilder sql = new StringBuilder();
    private int limiteRegistrosPorEmpresa = -1;
    private boolean somenteRepescagem = false;
    private String somenteErroX = null;
    private FormaPagamentoVO formaPagamentoCartaoCredito = null;
    private FormaPagamentoVO formaPagamentoDebitoEmConta = null;
    private FormaPagamentoVO formaPagamentoBoletoBancario = null;
    private UsuarioVO usuarioVO = null;
    private ConfiguracaoSistemaVO configSistema;
    private List<AcoesStatusRemessaVO> acoesStatusRemessaVOs;
    private LayoutRemessaBase l = new LayoutRemessaBase();
    private String key;
    private RetornoService retornoService;
    private NotaFiscal notaFiscal;
    public final static int PADRAO_CREDITO_DCC = 300;
    public final static int INICIO_LIMITE_EMERGENCIAL_DCC = 0;
    public final static int LIMITE_EMERGENCIAL_DCC = 0;
    public static String CHAVE_EMPRESA_REMESSA;
    private List<FormaPagamentoVO> listaFormasPagamentoComConvenio;
    private boolean processarApenasRemessas = false;
    private boolean passeiPorConvenioOnlineProcessoMultiplosConvenios = false;

    private Integer sequencialArquivo;

    /**
     * Criado um sleep para conexao com a Getnet.
     * Devido a várias tentativas ao mesmo tempo está ocorrendo problemas de bloqueio e envio de arquivos.
     * by Luiz Felipe 16/04/2020
     */
    public final static Integer sleepGetnet = 5000;




    public ConfiguracaoSistemaVO getConfigSistema() {
        return configSistema;
    }

    public int getLimiteRegistrosPorEmpresa() {
        return limiteRegistrosPorEmpresa;
    }

    public void setLimiteRegistrosPorEmpresa(int limiteRegistrosPorEmpresa) {
        this.limiteRegistrosPorEmpresa = limiteRegistrosPorEmpresa;
    }

    public boolean isSomenteRepescagem() {
        return somenteRepescagem;
    }

    public void setSomenteRepescagem(boolean somenteRepescagem) {
        this.somenteRepescagem = somenteRepescagem;
    }

    public String getSomenteErroX() {
        return somenteErroX;
    }

    public void setSomenteErroX(String somenteErroX) {
        this.somenteErroX = somenteErroX;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public boolean isProcessarApenasRemessas() {
        return processarApenasRemessas;
    }

    public void setProcessarApenasRemessas(boolean processarApenasRemessas) {
        this.processarApenasRemessas = processarApenasRemessas;
    }

    public LayoutRemessaBase getL() {
        return l;
    }

    public String getKey() {
        if (key == null) {
            key = "chave_nao_informada";
        }
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public RemessaService(Connection con) throws Exception {
        super(con);
        inicializarVariaves(con);
    }

    public RemessaService() throws Exception {
        Connection con = getFacade().getEmpresa().getCon();
        inicializarVariaves(con);
    }

    public void inicializarVariaves(Connection con) throws Exception {
        Empresa empresaDAO = null;
        FormaPagamento formaPagamentoDAO = null;
        Usuario usuarioDAO = null;
        ConfiguracaoSistema configuracaoSistemaDAO = null;
        AcoesStatusRemessa acoesStatusRemessaDAO = null;
        try {
            empresaDAO = new Empresa(con);
            formaPagamentoDAO = new FormaPagamento(con);
            usuarioDAO = new Usuario(con);
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            acoesStatusRemessaDAO = new AcoesStatusRemessa(con);

            listaEmpresas = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            formaPagamentoCartaoCredito = formaPagamentoDAO.obterFormaPagamentoCartaoRecorrente();

            listaFormasPagamentoComConvenio = formaPagamentoDAO.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            //
            formaPagamentoDebitoEmConta = new FormaPagamentoVO();
            formaPagamentoDebitoEmConta.setDescricao("DÉBITO CONTA CORRENTE");
            formaPagamentoDebitoEmConta.setTipoFormaPagamento("AV");
            formaPagamentoDebitoEmConta.setDefaultDCO(true);
            formaPagamentoDebitoEmConta = formaPagamentoDAO.criarOuConsultarSeExistePorFlag(formaPagamentoDebitoEmConta);
            //
            formaPagamentoBoletoBancario = new FormaPagamentoVO();
            formaPagamentoBoletoBancario.setDescricao("BOLETO BANCÁRIO");
            formaPagamentoBoletoBancario.setTipoFormaPagamento("BB");
            formaPagamentoBoletoBancario = formaPagamentoDAO.criarOuConsultarSeExistePorFlag(formaPagamentoBoletoBancario);

            usuarioVO = usuarioDAO.getUsuarioRecorrencia();
            this.configSistema = configuracaoSistemaDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS);
            this.acoesStatusRemessaVOs = acoesStatusRemessaDAO.consultarPorAcao(AcoesRemessasEnum.REALIZAR_REENVIO);

            retornoService = new RetornoService();
            notaFiscal = new NotaFiscal(con);
        } finally {
            empresaDAO = null;
            formaPagamentoDAO = null;
            usuarioDAO = null;
            configuracaoSistemaDAO = null;
            acoesStatusRemessaDAO = null;
        }
    }

    private void prepareSQL(ConvenioCobrancaVO convenio, boolean gerarRemessaContratoCancelado,
                            boolean habilitarReenvioAutomaticoRemessa, EmpresaVO empresa, Date dia) throws Exception {
        /**
         * Essa é a consulta responsável por obter as parcelas para pagamento automático de Remessa! (Remessa)
         * by Luiz Felipe 22/04/2020
         */

        Date dataMaximaLancamento = Calendario.hoje(); // isso para evitar que lancamentos recentes entrem no processamento e parcela seja paga via sistema e entre na remessa ao mesmo tempo
        if(!UteisValidacao.emptyString(convenio.getEmpresa().getTimeZoneDefault())){
            dataMaximaLancamento = Calendario.getDateInTimeZone(Calendario.hoje(), convenio.getEmpresa().getTimeZoneDefault());
        }
        sql.setLength(0);
        ///////////////////  Clientes    //////////////
        sql.append("select * from (select distinct mp.* from movparcela mp \n");
        sql.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = mp.codigo \n");
        sql.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql.append("INNER JOIN produto p on p.codigo = mprod.produto \n");
        sql.append("INNER JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql.append("INNER JOIN cliente cli on cli.pessoa = pes.codigo \n");
        sql.append("INNER JOIN autorizacaocobrancacliente auto on auto.cliente = cli.codigo and auto.ativa \n");


        ///////////////////  Colaboradores    ////////////// Para enviar remessas de cliente e Colaborador
        StringBuilder sql2 = new StringBuilder();
        sql2.append("union \n");
        sql2.append("select * from (select distinct mp.* from movparcela mp \n");
        sql2.append("INNER JOIN movprodutoparcela mpr on mpr.movparcela = mp.codigo \n");
        sql2.append("INNER JOIN movproduto mprod on mprod.codigo = mpr.movproduto \n");
        sql2.append("INNER JOIN produto p on p.codigo = mprod.produto \n");
        sql2.append("INNER JOIN pessoa pes on pes.codigo = mp.pessoa \n");
        sql2.append("INNER JOIN colaborador cli on cli.pessoa = pes.codigo \n");
        sql2.append("INNER JOIN autorizacaocobrancacolaborador auto on auto.colaborador = cli.codigo and auto.ativa \n");

        StringBuilder sqlWhere = new StringBuilder();
        sqlWhere.append("WHERE mp.situacao = 'EA' \n");
        sqlWhere.append("AND mp.valorParcela > 0 \n");

        if (habilitarReenvioAutomaticoRemessa) {
            sqlWhere.append("AND auto.tipoautorizacao = ").append(convenio.getTipo().getTipoAutorizacao().getId()).append(" \n");
            sqlWhere.append("AND mp.nrTentativasProcessoRetentativa < ").append(convenio.getEmpresa().getQtdExecucoesRetentativa()).append(" \n");
        } else {
            sqlWhere.append("AND auto.conveniocobranca = ").append(convenio.getCodigo()).append(" \n");
        }

        //caso desmarcado não pode cobrar parcela que tenha boleto online pendente
        if (!empresa.isCobrarParcelaComBoletoGerado()) {
            sqlWhere.append("AND not exists( \n");
            sqlWhere.append("select \n");
            sqlWhere.append("b.codigo \n");
            sqlWhere.append("from boleto b \n");
            sqlWhere.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
            sqlWhere.append("where b.situacao in (").append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",");
            sqlWhere.append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append(",");
            sqlWhere.append(SituacaoBoletoEnum.GERADO.getCodigo()).append(",");
            sqlWhere.append(SituacaoBoletoEnum.PAGO.getCodigo()).append(") \n");
            sqlWhere.append("and coalesce(bm.movparcela,0) = mp.codigo \n");
            sqlWhere.append(") \n");
        }

        //NÃO PODE TER PARCELA AGUARDANDO RETORNO
        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("select  \n");
        sqlWhere.append("ri1.codigo \n");
        sqlWhere.append("from remessaitem ri1 \n");
        sqlWhere.append("inner join remessa r1 on r1.codigo = ri1.remessa \n");
        sqlWhere.append("inner join remessaitemmovparcela rim1 on rim1.remessaitem = ri1.codigo \n");
        sqlWhere.append("where r1.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("and coalesce(rim1.movparcela, 0) = mp.codigo) \n");

        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("select  \n");
        sqlWhere.append("ri1.codigo \n");
        sqlWhere.append("from remessaitem ri1 \n");
        sqlWhere.append("inner join remessa r1 on r1.codigo = ri1.remessa \n");
        sqlWhere.append("where r1.situacaoremessa in (").append(SituacaoRemessaEnum.GERADA.getId()).append(",").append(SituacaoRemessaEnum.REMESSA_ENVIADA.getId()).append(") \n");
        sqlWhere.append("and coalesce(ri1.movparcela, 0) = mp.codigo) \n");


        //NÃO PODE TER PARCELA TRANSAÇÃO APROVADA OU CONCLUIDA COM SUCESSO
        sqlWhere.append("AND NOT EXISTS(\n");
        sqlWhere.append("select  \n");
        sqlWhere.append("tr1.codigo \n");
        sqlWhere.append("from transacao tr1 \n");
        sqlWhere.append("inner join transacaomovparcela trm on tr1.codigo = trm.transacao \n");
        sqlWhere.append("where tr1.situacao in (").append(SituacaoTransacaoEnum.APROVADA.getId()).append(",").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sqlWhere.append("and coalesce(trm.movparcela, 0) = mp.codigo) \n");


        if (gerarRemessaContratoCancelado) {
            sqlWhere.append("and p.tipoproduto in ('").append(TipoProduto.QUITACAO_DE_DINHEIRO.getCodigo()).append("') \n");
        } else {
            sqlWhere.append("and p.tipoproduto in (").append(Uteis.splitFromArray(AutorizacaoCobrancaClienteVO.TiposProdutoContrato, true)).append(") \n");
        }

        if(convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)){ //REGRAS PARA O DCO

            sqlWhere.append("AND mp.nrtentativas = 0 \n"); //pegar somente parcelas que nunca foram enviadas

            if(!UteisValidacao.emptyNumber(convenio.getDiasLimiteVencimentoParcelaDCO())) {
                //(Processo Automático) Não cobrar automaticamente do cliente com parcela vencida a mais de X dias
                sqlWhere.append(" and not exists(select codigo from movparcela where situacao = 'EA' and pessoa = mp.pessoa and datavencimento <= (mp.datavencimento::date + interval '-").append(convenio.getDiasLimiteVencimentoParcelaDCO()).append("' day)) \n");
            }
        }

        sqlWhere.append("and '").append(Uteis.getDataJDBCTimestamp(dataMaximaLancamento)).append("' - mprod.datalancamento > '01:00:00.000' \n");

        if (convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && convenio.isGerarArquivoUnico()) {
            ConvenioCobrancaEmpresa convenioCobrancaEmpresaDAO = new ConvenioCobrancaEmpresa(con);
            sqlWhere.append("and mp.empresa in ( ").append(convenioCobrancaEmpresaDAO.obterEmpresasConvenio(convenio.getCodigo())).append(" ) \n");
            convenioCobrancaEmpresaDAO = null;
        } else {
            sqlWhere.append("and mp.empresa = ").append(empresa.getCodigo()).append(" \n");
        }


        if (convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
            sqlWhere.append("and ((mp.datavencimento = ?::date + interval '").append(convenio.getDiasAntecipacaoRemessaDCO()).append(" days') ");
            sqlWhere.append("or (mp.datacobranca = ?::date + interval '").append(convenio.getDiasAntecipacaoRemessaDCO()).append(" days')) \n");
        } else {
            if (gerarRemessaContratoCancelado) {
                //cancelado só busca do dia
                sqlWhere.append("and (mp.datavencimento::date = ? or mp.datacobranca::date = ?) \n");
            } else {
                sqlWhere.append("and ( \n");
                sqlWhere.append(" (mp.datacobranca::date = ?) \n");
                sqlWhere.append(" OR \n");
                sqlWhere.append(" (mp.datavencimento::date <= ? \n");
                sqlWhere.append(" AND NOT EXISTS(SELECT ri1.codigo FROM remessaitem ri1 INNER JOIN remessaitemmovparcela rim ON ri1.codigo = rim.remessaitem WHERE coalesce(rim.movparcela,0) = mp.codigo AND mp.nrtentativas > 0) \n");
                sqlWhere.append(" AND NOT EXISTS(SELECT ri1.codigo FROM remessaitem ri1 WHERE coalesce(ri1.movparcela,0) = mp.codigo AND mp.nrtentativas > 0) \n");
                sqlWhere.append(")) \n");
            }
        }

        if (!gerarRemessaContratoCancelado) {
            sqlWhere.append("and not exists(select codigo from contratooperacao where contrato = coalesce(mp.contrato, 0) and tipooperacao in ('CA','DE')) \n");
        }


        /**
         * Caso esteja marcado tentativa única de cobrança
         * Verificar se não houve nenhuma tentativa de cobrança para a parcela
         * Consulta todas as parcelas que não tem tentativa de cobrança.
         * by Luiz Felipe 22/04/2020
         */
        if (!habilitarReenvioAutomaticoRemessa && convenio.getEmpresa().isTentativaUnicaDeCobranca()) {

            //parcelas que nunca foram enviadas tentaiva 0
            sqlWhere.append(" AND mp.nrtentativas = 0 \n");
            //sem tentativa de cobrança online
            sqlWhere.append(" AND NOT EXISTS(SELECT tran.codigo FROM transacao tran inner join transacaomovparcela tmp on tmp.transacao = tran.codigo WHERE coalesce(tmp.movparcela,0) = mp.codigo AND mp.nrtentativas > 0) \n");
            //sem tentativa de cobrança EDI
            sqlWhere.append(" AND NOT EXISTS(SELECT ri1.codigo FROM remessaitem ri1 INNER JOIN remessaitemmovparcela rim ON ri1.codigo = rim.remessaitem WHERE coalesce(rim.movparcela,0) = mp.codigo AND mp.nrtentativas > 0) \n");
            sqlWhere.append(" AND NOT EXISTS(SELECT ri1.codigo FROM remessaitem ri1 WHERE coalesce(ri1.movparcela,0) = mp.codigo AND mp.nrtentativas > 0) \n");

        }

        sqlWhere.append("and NOT EXISTS( \n");
        sqlWhere.append("SELECT rmov.codigo \n");
        sqlWhere.append("FROM remessaitemmovparcela rmov \n");
        sqlWhere.append("INNER JOIN remessaitem ri ON ri.codigo = rmov.remessaitem \n");
        sqlWhere.append("WHERE ri.tipo IN (").append(TipoRemessaEnum.BOLETO.getId()).append(",").append(TipoRemessaEnum.ITAU_BOLETO.getId()).append(") \n");
        sqlWhere.append("AND coalesce(rmov.movparcela,0) = mp.codigo) \n");
        if (somenteRepescagem) {
            sqlWhere.append(" and mp.nrtentativas > 0 \n");
        }
        if (somenteErroX != null && !somenteErroX.isEmpty()) {
            sqlWhere.append(" and mp.codigo in (select movparcela from transacaomovparcela where transacao in (select codigo from transacao where paramsresposta  like('%<ResultadoSolicitacaoAprovacao>Não Autorizado - ").append(somenteErroX).append("%</ResultadoSolicitacaoAprovacao>%'))) \n");
        }
        sqlWhere.append(" order by mp.pessoa, mp.datavencimento \n");
        if (limiteRegistrosPorEmpresa != -1) {
            sqlWhere.append(" limit ").append(limiteRegistrosPorEmpresa);
        }
        /// Append where
        sql.append(sqlWhere).append(") rescli ");
        sql2.append(sqlWhere).append(") rescolab ");
        // UNION
        sql.append(sql2);

    }

    public List<MovParcelaVO> consultarParcelasProcessoAutomatico(final Date dataVencimento, EmpresaVO empresa, final ConvenioCobrancaVO convenio) throws Exception {

        boolean habilitarReenvioAutomaticoRemessa = empresa.isHabilitarReenvioAutomaticoRemessa();
        if (!convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
            habilitarReenvioAutomaticoRemessa = false;
        }
        prepareSQL(convenio, false, habilitarReenvioAutomaticoRemessa, empresa, dataVencimento);
        PreparedStatement ps = criarQuery(sql.toString(), getCon());
        ps.setDate(1, Uteis.getDataJDBC(dataVencimento));
        ps.setDate(2, Uteis.getDataJDBC(dataVencimento));
        ps.setDate(3, Uteis.getDataJDBC(dataVencimento));
        ps.setDate(4, Uteis.getDataJDBC(dataVencimento));
        List<MovParcelaVO> listaParcelas = MovParcela.montarDadosConsulta(ps.executeQuery(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
        List<MovParcelaVO> listaParcelasRetorno;
        if (empresa.isGerarRemessaContratoCancelado()) {
            listaParcelasRetorno = consultarParcelasCancelamentoAntecipado(listaParcelas, dataVencimento, empresa, convenio);
        } else {
            listaParcelasRetorno = listaParcelas;
        }

        Pessoa pessoaDAO = new Pessoa(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        Map<Integer, PessoaVO> mapaPessoaBloqueio = pessoaDAO.obterMapaPessoasBloqueioCobrancaAutomatica();

        List<MovParcelaVO> listaFinal = new ArrayList<MovParcelaVO>();
        for (MovParcelaVO movParcelaVO : listaParcelasRetorno) {

            /**
             * VERIFICAR SE A PESSOA ESTÁ BLOQUEADA PARA COBRANÇA AUTOMÁTICA
             * by Luiz Felipe 28/04/2020
             */
            if (!pessoaDAO.podeCobrarParcelaBloqueioCobrancaAutomatica(movParcelaVO, mapaPessoaBloqueio)) {
                Uteis.logarDebug("PESSOA ESTÁ BLOQUEADA PARA COBRANÇA AUTOMÁTICA - " + movParcelaVO.getCodigo());
                continue;
            }


            /**
             * PARCELA DE MULTA E JUROS NÃO DEVE SER COBRADA AUTOMATICAMENTE
             * SISTEMA DEVE CRIAR NO MOMENTO DA TRANSAÇÃO E CASO NÃO SEJA PAGO A TRANSAÇÃO DEVE SER EXCLUIDA
             * by Luiz Felipe 12/06/2020
             */
            if (movParcelaVO.getDescricao().toUpperCase().startsWith("MULTA E JUROS - PARCELA")) {
                Uteis.logarDebug("IGNORAR | PARCELA DE MULTA E JUROS - " + movParcelaVO.getCodigo());
                continue;
            }


            /**
             * Parcela está bloqueada.. não realizar a tentativa de cobrança
             * by Luiz Felipe 17/04/2020
             */
            boolean parcelaEstaBloqueadaPorCobranca = movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO);
            if (parcelaEstaBloqueadaPorCobranca) {
                Uteis.logarDebug("PARCELA ESTÁ BLOQUEADA PARA COBRANÇA - " + movParcelaVO.getCodigo());
                continue;
            }

            listaFinal.add(movParcelaVO);
        }
        pessoaDAO = null;
        movParcelaDAO = null;
        return listaFinal;
    }

    private List<MovParcelaVO> consultarParcelasCancelamentoAntecipado(List<MovParcelaVO> listaParcelas, final Date dataVencimento, EmpresaVO empresa, final ConvenioCobrancaVO convenio) throws Exception {
        prepareSQL(convenio, true, false, empresa, dataVencimento);
        PreparedStatement ps = criarQuery(sql.toString(), getCon());
        ps.setDate(1, Uteis.getDataJDBC(dataVencimento));
        ps.setDate(2, Uteis.getDataJDBC(dataVencimento));
        ps.setDate(3, Uteis.getDataJDBC(dataVencimento));
        ps.setDate(4, Uteis.getDataJDBC(dataVencimento));
        List<MovParcelaVO> retorno = new ArrayList<MovParcelaVO>();
        List<MovParcelaVO> parcelasQuitacao = MovParcela.montarDadosConsulta(ps.executeQuery(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
        for (MovParcelaVO parcela : parcelasQuitacao) {
            boolean existe = false;
            for (MovParcelaVO parcela1 : listaParcelas) {
                if (parcela.getCodigo().equals(parcela1.getCodigo())) {
                    existe = true;
                }
            }
            if (!existe) {
                retorno.add(parcela);
            }
        }
        retorno.addAll(listaParcelas);
        return retorno;
    }

    public List<RemessaVO> preencherRemessasAntigo(List<MovParcelaVO> listaParcelas, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                                   boolean validarTiposIncompativeis, UsuarioVO usuario) throws Exception {
        List<RemessaVO> remessas = new ArrayList<RemessaVO>();
        List<List<MovParcelaVO>> parcelasDivididas = verificarDivisaoParcelas(listaParcelas);
        for(List<MovParcelaVO> parcelas : parcelasDivididas) {
            List<MovParcelaVO> listaParcelasProcessar = verificarAgruparParcelasPorPessoa(parcelas, convenio, empresa);
            List<RemessaItemVO> listaItensRemessa = validarParcelasGerarItensParaRemessas(listaParcelasProcessar, convenio, validarTiposIncompativeis, empresa);
            remessas.addAll(processarRemessas(convenio, listaItensRemessa, empresa, usuario));
        }
        return remessas;
    }

    public List<RemessaVO> preencherRemessas(List<MovParcelaVO> listaParcelas, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                             boolean validarTiposIncompativeis, UsuarioVO usuario, List<String> msgErro) throws Exception {
        validarDataDosCreditos(empresa);

        ConfiguracaoSistema confiDAO = new ConfiguracaoSistema(con);
        boolean agruparRemessasCartaoEDI = confiDAO.isAgruparRemessasCartaoEDI();
        confiDAO = null;

        if (agruparRemessasCartaoEDI && convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC)) {
            return preencherRemessasAgrupando(listaParcelas, convenio, empresa, usuario, msgErro);
        } else {
            return preencherRemessasAntigo(listaParcelas, convenio, empresa, validarTiposIncompativeis, usuario);
        }
    }

    public List<RemessaVO> preencherRemessasAgrupando(List<MovParcelaVO> listaParcelas, ConvenioCobrancaVO convenioCobrancaVO,
                                                      EmpresaVO empresaVO, UsuarioVO usuarioVO, List<String> msgErro) throws Exception {

        PagamentoCartaoAgrupadoService agrupadoService;
        try {
            agrupadoService = new PagamentoCartaoAgrupadoService(convenioCobrancaVO, empresaVO, this.getCon());
            List<PagamentoCartaoTO> listaPagamentoAgrupado = agrupadoService.processarParcelas(listaParcelas, usuarioVO, msgErro, null);
            List<RemessaItemVO> itens = gerarItensRemessa(listaPagamentoAgrupado, convenioCobrancaVO);

            List<RemessaVO> remessas = new ArrayList<>();
            remessas.addAll(gerarRemessas(convenioCobrancaVO, itens, empresaVO, usuarioVO));
            return remessas;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            agrupadoService = null;
        }
    }

    private List<RemessaItemVO> gerarItensRemessa(List<PagamentoCartaoTO> listaPagamentoAgrupado, ConvenioCobrancaVO cobrancaVO) throws Exception {
        RemessaVO remessa = new RemessaVO();
        remessa.setTipo(cobrancaVO.getTipo().getTipoRemessa());
        remessa.setConvenioCobranca(cobrancaVO);

        List<RemessaItemVO> itens = new ArrayList<>();
        for(PagamentoCartaoTO pagamentoCartaoTO : listaPagamentoAgrupado) {
            RemessaItemVO item = new RemessaItemVO();
            item.setTipo(remessa.getTipo());
            item.setRemessa(remessa);
            item.setPessoa(pagamentoCartaoTO.getPessoaPagador());

            for (MovParcelaVO parc : pagamentoCartaoTO.getParcelas()) {
                RemessaItemMovParcelaVO itemMovParcelaVO = new RemessaItemMovParcelaVO();
                itemMovParcelaVO.setMovParcelaVO(parc);
                itemMovParcelaVO.setValorOriginal(parc.getValorParcela());
                itemMovParcelaVO.setValorMulta(parc.getValorMulta());
                itemMovParcelaVO.setValorJuros(parc.getValorJuros());
                itemMovParcelaVO.setNrTentativaParcela(parc.getNrTentativas() + 1);

                item.setValorItemRemessa(item.getValorItemRemessa() + parc.getValorParcela() + parc.getValorMultaJuros());
                item.setValorMulta(item.getValorMulta() + parc.getValorMulta());
                item.setValorJuros(item.getValorJuros() + parc.getValorJuros());
                item.getMovParcelas().add(itemMovParcelaVO);
            }

            NazgDTO nazgDTO = pagamentoCartaoTO.getAutorizacaoCobrancaVO().getNazgDTO();
            if (nazgDTO == null || UteisValidacao.emptyString(nazgDTO.getCard())) {
                throw new Exception("Número do cartão não informado! AutorizacaoCobrancaCliente " + pagamentoCartaoTO.getAutorizacaoCobrancaVO().getCodigo());
            }


            pagamentoCartaoTO.getAutorizacaoCobrancaVO().setNumeroCartao(nazgDTO.getCard());
            pagamentoCartaoTO.getAutorizacaoCobrancaVO().setNomeTitularCartao(nazgDTO.getName());
            pagamentoCartaoTO.getAutorizacaoCobrancaVO().setCpfTitular(nazgDTO.getCpf());
            pagamentoCartaoTO.getAutorizacaoCobrancaVO().setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
            pagamentoCartaoTO.getAutorizacaoCobrancaVO().setValidadeCartao(Uteis.getValidadeMMYYYY(nazgDTO.getMonth(), nazgDTO.getYear(), true));
//            pagamentoCartaoTO.getAutorizacaoCobrancaVO().setCvv(nazgDTO.getCvv());
            pagamentoCartaoTO.getAutorizacaoCobrancaVO().preencherOperadoraCartao();
            item.setNazgDTO(new NazgDTO(pagamentoCartaoTO.getAutorizacaoCobrancaVO()));

            pagamentoCartaoTO.getAutorizacaoCobrancaVO().preencherRemessaItemSegundoAutorizacao(item, this.con);
            item.setAutorizacaoCobrancaVO(pagamentoCartaoTO.getAutorizacaoCobrancaVO());
            itens.add(item);
        }
        return itens;
    }

    private List<RemessaVO> gerarRemessas(ConvenioCobrancaVO convenioCobrancaVO, List<RemessaItemVO> listaItensRemessa,
                                         EmpresaVO empresaVO, UsuarioVO usuarioVO) throws Exception {
        List<RemessaVO> listaRemessas = new ArrayList<>();
        Integer limiteItensRemessa = convenioCobrancaVO.getLimiteItensRemessa();

        RemessaVO remessaVO = null;
        int adicionado = 0;
        for (RemessaItemVO itemVO : listaItensRemessa) {

            if (remessaVO == null) {
                //adicionado para evitar remessas com a mesma data de registro.
                //by Luiz Felipe
                Thread.sleep(2000);
                remessaVO = new RemessaVO();
                remessaVO.setUsuario(usuarioVO);
                remessaVO.setTipo(convenioCobrancaVO.getTipo().getTipoRemessa());
                remessaVO.setDataRegistro(Calendario.hoje());
                remessaVO.setDataInicio(Calendario.hoje());
                remessaVO.setConvenioCobranca(convenioCobrancaVO);
                remessaVO.setArquivoUnico(convenioCobrancaVO.isGerarArquivoUnico());
                remessaVO.setEmpresaVO(empresaVO);
                remessaVO.setEmpresa(empresaVO.getCodigo());
                remessaVO.setListaItens(new ArrayList<>());
                remessaVO.setNovoFormato(true);
            }

            itemVO.setTipo(remessaVO.getTipo());
            itemVO.setRemessa(remessaVO);
            remessaVO.getListaItens().add(itemVO);
            ++adicionado;

            if (remessaVO.getListaItens().size() == limiteItensRemessa || adicionado == listaItensRemessa.size()) {
                listaRemessas.add(remessaVO);
                remessaVO = null;
            }
        }

        gerarIdentificadoresListaRemessas(listaRemessas);
        return listaRemessas;
    }

    private List<List<MovParcelaVO>> verificarDivisaoParcelas(List<MovParcelaVO> listaParcelas) {
        List<MovParcelaVO> parcelasUnicas = new ArrayList<MovParcelaVO>();
        List<MovParcelaVO> parcelasParceladas = new ArrayList<MovParcelaVO>();
        for(MovParcelaVO mov : listaParcelas){
            if(mov.getNumeroParcelasOperadora() != null && mov.getNumeroParcelasOperadora() > 1){
                parcelasParceladas.add(mov);
            }else{
                parcelasUnicas.add(mov);
            }
        }
        return Arrays.asList(parcelasUnicas, parcelasParceladas);
    }

    private List<RemessaVO> processarRemessas(ConvenioCobrancaVO convenio, List<RemessaItemVO> listaItensRemessa, EmpresaVO empresa, UsuarioVO usuario) throws Exception {
        List<RemessaVO> listaRemessas = new ArrayList<RemessaVO>();
        int limiteItens = 0;
        boolean todosItensProcessados = false;
        while (!todosItensProcessados) {
            List<RemessaItemVO> parcelasAutorizacoesIguaisGeral = new ArrayList<RemessaItemVO>();
            List<List> listaItensSubDivididas = new ArrayList<List>();
            limiteItens = convenio.getLimiteItensRemessa();
            if (TipoRemessaEnum.ITAU_BOLETO.equals(convenio.getTipo().getTipoRemessa())) {
                limiteItens = 170;
            }
            Uteis.subDivide(listaItensRemessa, limiteItens, listaItensSubDivididas);
            for (List aListaItensRemessa : listaItensSubDivididas) {
                //adicionado para evitar remessas com a mesma data de registro.
                //by Luiz Felipe
                Thread.sleep(2000);

                List<String> cartoes = new ArrayList<String>();
                List<RemessaItemVO> parcelasAutorizacoesIguaisRemessa = new ArrayList<RemessaItemVO>();
                RemessaVO remessa = new RemessaVO();
                remessa.setUsuario(usuario);
                remessa.setTipo(convenio.getTipo().getTipoRemessa());
                remessa.setDataRegistro(Calendario.hoje());
                remessa.setDataInicio(Calendario.hoje());
                remessa.setConvenioCobranca(convenio);
                remessa.setArquivoUnico(convenio.isGerarArquivoUnico());
                remessa.setEmpresa(empresa.getCodigo());
                List<RemessaItemVO> listaI = aListaItensRemessa;
                parcelasAutorizacoesIguaisRemessa = validarAdicionarItensRemessa(remessa, listaI, cartoes, false, limiteItens);
                if (!parcelasAutorizacoesIguaisGeral.isEmpty() && remessa.getListaItens().size() < limiteItens) { // tenta adicionar itens de listas anteriores que tinha autorizações iguais,  até o limite itens seja preenchido
                    parcelasAutorizacoesIguaisGeral = validarAdicionarItensRemessa(remessa, parcelasAutorizacoesIguaisGeral, cartoes, true, limiteItens);
                }
                parcelasAutorizacoesIguaisGeral.addAll(parcelasAutorizacoesIguaisRemessa);

                if (!remessa.getListaItens().isEmpty()) {
                    listaRemessas.add(remessa);
                }
            }
            if (parcelasAutorizacoesIguaisGeral.isEmpty()) {
                todosItensProcessados = true;
            } else {
                listaItensRemessa = parcelasAutorizacoesIguaisGeral;
            }
        }
        gerarIdentificadoresListaRemessas(listaRemessas);

        return listaRemessas;
    }

    public void debitarCreditosDCC(EmpresaVO empresa, RemessaVO remessaVO, boolean somenteValidar) throws Exception {
        Empresa empresaDAO = null;
        RemessaItem remessaItemDAO = null;
        try {
            empresaDAO = new Empresa(con);
            remessaItemDAO = new RemessaItem(con);

            int creditoDCC = empresa.getCreditoDCC();
            int qtdUsada = calcularQtdCreditosUsados(remessaVO);

            if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                if (somenteValidar) {
                    if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                        throw new ConsistirException("Não é possível criar a remessa. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
                    }
                } else {
                    if (creditoDCC - qtdUsada <= 0) {
                        enviarEmailCreditosAcabando(empresa, remessaVO, creditoDCC);
                    }
                    if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                        throw new ConsistirException("Não é possível criar a remessa. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
                    }
                    empresaDAO.debitarCreditoDCC(qtdUsada, empresa.getCodigo(), "REMESSA");
                    empresa.setCreditoDCC(creditoDCC - qtdUsada);
                }
                remessaItemDAO.marcarRemessaComoContabilizadaPacto(true, remessaVO);
            } else {
                remessaItemDAO.marcarRemessaComoContabilizadaPacto(false, remessaVO);
            }

            if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {
                String urlOamd = PropsService.getPropertyValue(getKey(), PropsService.urlOamd);
                JSONObject info = empresaDAO.obterInfoRedeDCC(urlOamd, getKey());
                creditoDCC = info.getInt("creditos");
                if (!somenteValidar && (creditoDCC - qtdUsada <= 0)) {
                    enviarEmailCreditosAcabando(empresa, remessaVO, creditoDCC);
                }
                if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                    throw new ConsistirException("Não é possível criar a remessa. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
                }
                if (!somenteValidar) {
                    empresa.setCreditoDCC(creditoDCC - qtdUsada);
                }
            }
        } finally {
            empresaDAO = null;
            remessaItemDAO = null;
        }
    }

    public void debitarCreditosDCCBoleto(EmpresaVO empresa, RemessaVO remessaVO, boolean somenteValidar) throws Exception {
        Empresa empresaDAO = null;
        RemessaItem remessaItemDAO = null;
        try {
            empresaDAO = new Empresa(con);
            remessaItemDAO = new RemessaItem(con);

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            //Se não cobra crédito por boleto, não fazer nada
            if (!empresaVO.isCobrarCreditoPactoBoleto()) {
                return;
            }

            int creditoDCC = empresa.getCreditoDCC();
            int qtdUsada = calcularQtdCreditosUsados(remessaVO);

            if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.PRE_PAGO.getCodigo())) {
                if (somenteValidar) {
                    if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                        throw new ConsistirException("Não é possível criar a remessa. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
                    }
                } else {
                    if (creditoDCC - qtdUsada <= 0) {
                        enviarEmailCreditosAcabando(empresa, remessaVO, creditoDCC);
                    }
                    if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                        throw new ConsistirException("Não é possível criar a remessa. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
                    }
                    empresaDAO.debitarCreditoDCC(qtdUsada, empresa.getCodigo(), "REMESSA BOLETO");
                    empresa.setCreditoDCC(creditoDCC - qtdUsada);
                }
                remessaItemDAO.marcarRemessaComoContabilizadaPacto(true, remessaVO);
            } else {
                remessaItemDAO.marcarRemessaComoContabilizadaPacto(false, remessaVO);
            }

            if (empresa.getTipoCobrancaPacto().equals(TipoCobrancaPactoEnum.REDE_EMPRESAS_PRE_PAGO.getCodigo())) {
                String urlOamd = PropsService.getPropertyValue(getKey(), PropsService.urlOamd);
                JSONObject info = empresaDAO.obterInfoRedeDCC(urlOamd, getKey());
                creditoDCC = info.getInt("creditos");
                if (!somenteValidar && (creditoDCC - qtdUsada <= 0)) {
                    enviarEmailCreditosAcabando(empresa, remessaVO, creditoDCC);
                }
                if (creditoDCC - qtdUsada < RemessaService.LIMITE_EMERGENCIAL_DCC) {
                    throw new ConsistirException("Não é possível criar a remessa. Limite de crédito Pacto não é suficiente. Empresa: " + empresa.getNome());
                }
                if (!somenteValidar) {
                    empresa.setCreditoDCC(creditoDCC - qtdUsada);
                }
            }
        } finally {
            empresaDAO = null;
            remessaItemDAO = null;
        }
    }

    public void enviarEmailCreditosAcabando(EmpresaVO empresa, RemessaVO remessaVO, Integer saldoCreditos) {
        try {
            Map<String, String> parametros = construirParametrosEmailCreditoAcabanco(empresa, remessaVO, saldoCreditos);
            ModeloMensagemSistemaService modeloMensagemSistemaService = new ModeloMensagemSistemaService();
            if (getConfigSistema() != null && !getConfigSistema().getListaEmailsRecorrencia().isEmpty()) {
                modeloMensagemSistemaService.enviarEmail(IdentificadorMensagemSistema.EMAIL_EMPRESA_CREDITOS_REMESSA_ACABANDO, parametros, getUsuario(), getConfigSistema().getListaEmailsRecorrencia(), SuperControle.getConfiguracaoSMTPNoReply());
            }

            //comercial
            String emailComercialPacto = PropsService.getPropertyValue(PropsService.emailComercialPacto);
            emailComercialPacto = getEmailPorEstado(empresa.getEstado(), emailComercialPacto);
            if (!UteisValidacao.emptyString(emailComercialPacto)) {
                modeloMensagemSistemaService.enviarEmail(IdentificadorMensagemSistema.EMAIL_COMERCIAL_CREDITOS_REMESSA_ACABANDO, parametros, getUsuario(), Arrays.asList(emailComercialPacto.split(",")), SuperControle.getConfiguracaoSMTPNoReply());
            }
        } catch (Exception e) {
            Uteis.logar(e, RemessaService.class);
        }
    }

    /**
     * Retorna a lista de emails que sera utilizada para enviar o email. A lista
     * de email deve estar da seguinte forma
     * SIGLA_ESTADO=>emails_separandos_por_vigula->SIGLA_ESTADO_2=>emails_separandos_por_vigula->OUTROS=>emails_separados_por_virgula
     *
     * @param estado Estado que se deseja checar o envio do email.
     * @param parametroEmail
     * @return
     */
    private String getEmailPorEstado(EstadoVO estado, String parametroEmail) throws Exception {
        Estado estadoDAO;
        try {
            estadoDAO = new Estado(con);

            String[] emailsPorEstado = parametroEmail.split("->");
            String sigla = null;
            if (estado != null && estado.getCodigo() != null && !estado.getCodigo().equals(0)) {
                estado = estadoDAO.consultarPorChavePrimaria(estado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                sigla = UteisValidacao.emptyString(estado.getSigla()) ? "OUTROS" : estado.getSigla().trim();
            } else {
                sigla = "OUTROS";
            }
            String emailsOutros = null;
            String emailsEnviar = null;
            for (String emailEstado : emailsPorEstado) {
                String[] emails = emailEstado.split("=>");
                if (emails[0].equals(sigla)) {
                    emailsEnviar = emails[1];
                    break;
                } else {
                    if (emails[0].equals("OUTROS")) {
                        emailsOutros = emails[1];
                    }
                }
            }
            return emailsEnviar == null ? emailsOutros : emailsEnviar;
        } finally {
            estadoDAO = null;
        }
    }

    /**
     * Constroi um mapa com os parametros da remessa para se popupar o email de
     * notificacao que os créditos da empresa estão acabando.
     *
     * @param empresa
     * @param remessaVO
     * @param saldoCreditos
     * @return
     */
    private Map<String, String> construirParametrosEmailCreditoAcabanco(EmpresaVO empresa, RemessaVO remessaVO, Integer saldoCreditos) {
        Map<String, String> parametros = new HashMap<String, String>(5);
        parametros.put("SALDO_ATUAL_CREDITO", saldoCreditos.toString());
        if (remessaVO == null) {
            parametros.put("QTD_REMESSAS_PENDENTES", "TRANSACAO");
        }  else {
            parametros.put("QTD_REMESSAS_PENDENTES", new Integer(remessaVO.getListaItens().size()).toString());
        }
        parametros.put("DATA_EXECUCAO", Uteis.getData(Calendario.hoje(), "dd/MM/yyyy HH:mm"));
        parametros.put("NOME_EMPRESA", empresa.getNome());
        parametros.put("CNPJ_EMPRESA", empresa.getCNPJ());
        if (!UteisValidacao.emptyString(empresa.getTelComercial1())) {
            parametros.put("TELEFONE_EMPRESA", empresa.getTelComercial1());
        }
        if (!UteisValidacao.emptyString(empresa.getEmail())) {
            parametros.put("EMAIL_EMPRESA", empresa.getEmail());
        }
        return parametros;
    }

    public List<RemessaVO> preencherRemessaBoleto(List<MovParcelaVO> listaParcelas, ConvenioCobrancaVO convenio,
            boolean validarTiposIncompativeis, UsuarioVO usuario) throws Exception {

        //adicionado para evitar remessas com a mesma data de registro.
        //by Luiz Felipe
        Thread.sleep(2000);

        RemessaVO remessa = new RemessaVO();
        remessa.setUsuario(usuario);
        remessa.setTipo(convenio.getTipo().getTipoRemessa());
        remessa.setDataRegistro(Calendario.hoje());
        remessa.setDataInicio(Calendario.hoje());
        remessa.setConvenioCobranca(convenio);
        remessa.setEmpresa(convenio.getEmpresa().getCodigo());
        remessa.gerarIdentificador(con);

        for (MovParcelaVO parcela : listaParcelas) {
            List<AutorizacaoCobrancaClienteVO> autos = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(parcela.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

            for (AutorizacaoCobrancaClienteVO auto : autos) {
                boolean compativel = false;
                if (convenio.getBanco() != null) {
                    compativel = Uteis.enumIn(convenio.getTipo(), auto.getTipoAutorizacao().getTiposConvenio());
                }
                if (compativel) {
                    boolean achou = false;
                    RemessaItemVO item = new RemessaItemVO();
                    parcela.setTipoProdutos(getFacade().getMovParcela().consultaTiposProdutosMovParcela(parcela.getCodigo()));
                    if (auto.isParcelaCompativel(parcela, false, this.con)) {
                        achou = true;
                        RemessaItemMovParcelaVO itemMovParcelaVO = new RemessaItemMovParcelaVO();
                        itemMovParcelaVO.getMovParcelaVO().setCodigo(parcela.getCodigo());
                        itemMovParcelaVO.getMovParcelaVO().setValorParcela(parcela.getValorParcela());
                        itemMovParcelaVO.getMovParcelaVO().setPessoa(parcela.getPessoa());
                        itemMovParcelaVO.setValorOriginal(parcela.getValorParcela());
                        item.getMovParcelas().add(itemMovParcelaVO);
                    }
                    item.setPessoa(parcela.getPessoa());
                    item.setRemessa(remessa);
                    item.setDataVencimentoBoleto(parcela.getDataVencimento());
                    item.setAutorizarDebito(auto.isAutorizarClienteDebito());
                    auto.preencherRemessaItemSegundoAutorizacao(item, this.con);
                    if (achou) {
                        remessa.getListaItens().add(item);
                    }
                    break;
                }
            }
        }

        List<RemessaVO> remessas = new ArrayList<RemessaVO>();
        remessas.add(remessa);
        return remessas;
    }

    public List<RemessaVO> preencherRemessaSemListaBoleto(ConvenioCobrancaVO convenio, final Date inicio, final Date fim,
            final Date inicioCobranca, final Date fimCobranca, Integer codigoEmpresa, Integer plano, UsuarioVO usuario, boolean somenteParcelasSemBoletoGerado, Integer modalidade) throws Exception {

        //adicionado para evitar remessas com a mesma data de registro.
        //by Luiz Felipe
        Thread.sleep(2000);

        RemessaVO remessa = new RemessaVO();
        remessa.setUsuario(usuario);
        remessa.setTipo(convenio.getTipo().getTipoRemessa());
        remessa.setDataRegistro(Calendario.hoje());
        remessa.setDataInicio(Calendario.hoje());
        remessa.setConvenioCobranca(convenio);
        remessa.setEmpresa(convenio.getEmpresa().getCodigo());
        if (convenio.getUsarSequencialUnico()) {
            sequencialArquivo = getFacade().getConfiguracaoSistema().incrementarSequencialArquivo();
            remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), sequencialArquivo.toString());
            convenio.setSequencialDoArquivo(sequencialArquivo);
            remessa.gerarIdentificador(con, sequencialArquivo);
        }else{
            remessa.gerarIdentificador(con);
        }
        MovParcela movParcela = new MovParcela(con);
        Cliente cliente = new Cliente(con);
        Map<Integer, Integer> mapaPessoasTitulares = cliente.montarMapaPessoasTitulares();
        preencherItensRemessaBoleto(convenio, inicio, fim, inicioCobranca, fimCobranca, codigoEmpresa, plano, remessa, movParcela, mapaPessoasTitulares, somenteParcelasSemBoletoGerado, modalidade);
        List<RemessaVO> remessas = new ArrayList<RemessaVO>();
        remessas.add(remessa);
        return remessas;
    }

    private void preencherItensRemessaBoleto(ConvenioCobrancaVO convenio, Date inicio, Date fim,
            Date inicioCobranca, Date fimCobranca, Integer codigoEmpresa, Integer plano,
            RemessaVO remessa, MovParcela movParcela, Map<Integer, Integer> mapaPessoasTitulares, boolean somenteParcelasSemBoletoGerado, Integer modalidade) throws Exception {

        Map<Integer, List<MovParcelaVO>> mapaParcelasPorTitular = new HashMap<Integer, List<MovParcelaVO>>();

        ResultSet rs = movParcela.getRsParcelasDCC(plano, inicio, fim, inicioCobranca, fimCobranca, Arrays.asList(convenio.getCodigo()), Arrays.asList(codigoEmpresa),
                false, false, false, true, false, somenteParcelasSemBoletoGerado, modalidade,true, true, null);
        RemessaItem remessaItemDAO;
        try {
            remessaItemDAO = new RemessaItem(con);
            while (rs.next()) {
                MovParcelaVO parcela = MovParcela.montarDados(rs, Uteis.NIVELMONTARDADOS_GESTAOREMESSA, con);

                try {
                    List<RemessaItemVO> itens = remessaItemDAO.consultarPorCodigoParcela(parcela.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    boolean irProximoItemWhile = false;
                    if (!UteisValidacao.emptyList(itens)) {
                        // Verificar se a parcela está em uma remessa com boleto maior que hoje
                        // Se sim, pula para o próximo item e não adiciona na lista de parcelas para gerar um novo boleto
                        for (RemessaItemVO item : itens) {
                            if ((item.getRemessa().getSituacaoRemessa() == SituacaoRemessaEnum.REMESSA_ENVIADA || item.getRemessa().getSituacaoRemessa() == SituacaoRemessaEnum.GERADA)
                                    && Calendario.maiorOuIgualDataAtual(item.getDataVencimentoBoleto())) {
                                irProximoItemWhile = true;
                                break;
                            }
                        }
                        if (irProximoItemWhile) {
                            continue;
                        }
                    }
                } catch (Exception ex) {
                    // Log do erro mas continua o processamento
                    // Continua o fluxo normalmente sem adicionar a parcela
                    Uteis.logar(null, "Erro ao consultar remessa item para parcela " + parcela.getCodigo() + ": " + ex.getMessage());
                    ex.printStackTrace();
                }

                Integer codTitular = mapaPessoasTitulares.get(parcela.getPessoa().getCodigo());
                List<MovParcelaVO> parcelasDoTitular = mapaParcelasPorTitular.get(codTitular);
                if (parcelasDoTitular == null) {
                    parcelasDoTitular = new ArrayList<MovParcelaVO>();
                    mapaParcelasPorTitular.put(codTitular, parcelasDoTitular);
                }
                parcelasDoTitular.add(parcela);
            }
        } finally {
            remessaItemDAO = null;
        }

        for (Integer codTitular : mapaParcelasPorTitular.keySet()) {
            List<MovParcelaVO> parcelasParaBoleto = mapaParcelasPorTitular.get(codTitular);
            //Analisar essa criação.
            List<AutorizacaoCobrancaClienteVO> autos = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(codTitular, Uteis.NIVELMONTARDADOS_TODOS);
            for (AutorizacaoCobrancaClienteVO auto : autos) {
                boolean compativel = false;
                if (convenio.getBanco() != null) {
                    compativel = Uteis.enumIn(convenio.getTipo(), auto.getTipoAutorizacao().getTiposConvenio());
                }
                for (MovParcelaVO parcela : parcelasParaBoleto) {
                    try {
                        if (compativel) {
                            boolean achou = false;
                            RemessaItemVO item = new RemessaItemVO();

                            parcela.setTipoProdutos(getFacade().getMovParcela().consultaTiposProdutosMovParcela(parcela.getCodigo()));
                            achou = validarAdicionarParcelaRemessaItem(parcela, auto, achou, item);
                            item.setPessoa(auto.getCliente().getPessoa());
                            item.setRemessa(remessa);
                            item.setDataVencimentoBoleto(parcela.getDataVencimento());
                            item.setValorItemRemessa(parcela.getValorParcela());
                            item.setTipo(TipoRemessaEnum.BOLETO);
                            item.setAutorizarDebito(auto.isAutorizarClienteDebito());

                            auto.preencherRemessaItemSegundoAutorizacao(item, this.con);
                            if (!parcelasParaBoleto.isEmpty()) {
                                DescontoBoletoTO desconto = getFacade().getCliente().descobrirDescontoBoletoParaCliente(parcelasParaBoleto);
                                if (desconto != null) {
                                    item.setDiaDoMesDescontoBoletoPagAntecipado(desconto.getDiaMaximoPagamentoDesconto());
                                    item.setPorcentagemDescontoBoletoPagAntecipado(desconto.getPorcentagemDesconto());
                                }
                            }

                            if (achou) {
                                Double saldoAtual = getFacade().getMovimentoContaCorrenteCliente().consultarSaldoAtual(item.getPessoa().getCodigo());
                                if (saldoAtual < 0.0) {
                                    saldoAtual = 0.0;
                                }

                                if (saldoAtual >= item.getValorBoleto()) {
                                    processarContaCorrente(convenio, codigoEmpresa, auto.getCliente().getPessoa(),
                                            item.getMovParcela().getContrato(), inicio, fim, Calendario.hoje());
                                } else {
                                    item.setValorCredito(saldoAtual);
                                    if (item.getValorBoleto() > 0) {
                                        remessa.getListaItens().add(item);
                                    }
                                }
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        if (convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO)) {
                            if (remessa != null) {
                                remessa.getErrosGeracao().add(ex.getMessage());
                            }
                        } else {
                            throw ex;
                        }
                    }
                }
            }
        }
    }

    private boolean validarAdicionarParcelaRemessaItem(MovParcelaVO parcela, AutorizacaoCobrancaClienteVO auto, boolean achou, RemessaItemVO item) throws Exception {
        if (auto.isParcelaCompativel(parcela, false, this.con)) {
            achou = true;
            RemessaItemMovParcelaVO itemMovParcelaVO = new RemessaItemMovParcelaVO();
            itemMovParcelaVO.getMovParcelaVO().setCodigo(parcela.getCodigo());
            itemMovParcelaVO.getMovParcelaVO().setValorParcela(parcela.getValorParcela());
            itemMovParcelaVO.getMovParcelaVO().setPessoa(parcela.getPessoa());
            itemMovParcelaVO.setValorOriginal(parcela.getValorParcela());
            item.getMovParcelas().add(itemMovParcelaVO);
        }

        return achou;
    }


    public RemessaVO criarRemessaBoleto(MovParcelaVO parcelaSacado, List<MovParcelaVO> listaParcelas,
            ConvenioCobrancaVO convenio, Date dataVencimento, boolean validarTiposIncompativeis,
            UsuarioVO usuario) throws Exception {

        //adicionado para evitar remessas com a mesma data de registro.
        //by Luiz Felipe
        Thread.sleep(2000);

        RemessaVO remessa = new RemessaVO();
        remessa.setUsuario(usuario);
        remessa.setTipo(convenio.getTipo().getTipoRemessa());
        remessa.setDataRegistro(Calendario.hoje());
        remessa.setDataInicio(Calendario.hoje());
        remessa.setConvenioCobranca(convenio);
        remessa.setEmpresa(parcelaSacado.getEmpresa().getCodigo());
        remessa.gerarIdentificador(con);

        List<AutorizacaoCobrancaClienteVO> autos = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(parcelaSacado.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);

        boolean achou = false;

        for (AutorizacaoCobrancaClienteVO auto : autos) {
            boolean compativel = false;
            if (convenio.getBanco() != null) {
                compativel = Uteis.enumIn(convenio.getTipo(), auto.getTipoAutorizacao().getTiposConvenio());
            }
            if (compativel) {

                RemessaItemVO item = new RemessaItemVO();
                for (MovParcelaVO mp : listaParcelas) {
                    mp.setTipoProdutos(getFacade().getMovParcela().consultaTiposProdutosMovParcela(mp.getCodigo()));
                    if (auto.isParcelaCompativel(mp, false, this.con)) {
                        achou = true;
                        RemessaItemMovParcelaVO itemMovParcelaVO = new RemessaItemMovParcelaVO();
                        itemMovParcelaVO.setRemessaItemVO(item);
                        itemMovParcelaVO.setMovParcelaVO(mp);
                        item.getMovParcelas().add(itemMovParcelaVO);
                    }
                }
                item.setPessoa(parcelaSacado.getPessoa());
                item.setRemessa(remessa);
                item.setDataVencimentoBoleto(dataVencimento);
                item.setMovParcela(parcelaSacado);
                item.setAutorizarDebito(auto.isAutorizarClienteDebito());
                auto.preencherRemessaItemSegundoAutorizacao(item, this.con);
                remessa.getListaItens().add(item);
                break;

            }
        }
        if (validarTiposIncompativeis && !achou) {
            throw new ConsistirException(String.format("Aluno \"%s\" da \"%s\", "
                    + "não possui autorização compatível com a parcela. Verifique o Cadastro de Autorizações de Cobrança.",
                    parcelaSacado.getPessoa().getNome(), parcelaSacado));
        }
        return remessa;
    }

    public JBoletoBean gerarBoletoCobranca(RemessaItemVO obj){
        JBoletoBean boleto = new JBoletoBean();

        boleto.setMoeda("9");
        boleto.setAceite("N");

        String numAgencia = obj.getRemessa().getConvenioCobranca().getContaEmpresa().getAgencia();
        String dvAg = obj.getRemessa().getConvenioCobranca().getContaEmpresa().getAgenciaDV();
        boleto.setAgencia(numAgencia);
        boleto.setDvAgencia(dvAg);
        boleto.setNumConvenio(obj.getRemessa().getConvenioCobranca().getNumeroContrato());
        boleto.setCodigoOperacao(obj.getRemessa().getConvenioCobranca().getOperacao());

        String numConta = obj.getRemessa().getConvenioCobranca().getContaEmpresa().getContaCorrente();
        String dvConta = obj.getRemessa().getConvenioCobranca().getContaEmpresa().getContaCorrenteDV();
        boleto.setContaCorrente(numConta);
        boleto.setDvContaCorrente(dvConta);

        if( obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco() == JBoleto.SICREDI ){
            boleto.setTipoCarteira("1");
            boleto.setTipoCobranca("1");
            obj.getRemessa().getConvenioCobranca().setNumeroContrato(obj.getRemessa().getConvenioCobranca().getNumeroContrato().replace("-",""));
            boleto.setContaCorrente(StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getRemessa().getConvenioCobranca().getNumeroContrato(), 5));
            boleto.setDvAgencia(StringUtilities.formatarCampoForcandoZerosAEsquerda(dvAg, 2));
        }

        if (obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_CAIXA)) {
            boleto.setCarteira("RG");
        } else {
            boleto.setCarteira(obj.getRemessa().getConvenioCobranca().getCarteiraBoleto().toString());
        }
        try {
            boolean tipoDM = ArquivoLayoutRemessaEnum.BOLETO_CAIXA.equals(obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa()) ||
                    ArquivoLayoutRemessaEnum.BOLETO_SANTANDER.equals(obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa());
            boleto.setEspecieDocumento(tipoDM ? "DM" : "DS");
        }catch (Exception e){
            boleto.setEspecieDocumento("DS");
        }

        boleto.setDataProcessamento(Calendario.getData(Calendario.hoje(), "dd/MM/yyyy"));
        boleto.setDataDocumento(Calendario.getData(obj.getRemessa().getDataRegistro(), "dd/MM/yyyy"));
        boleto.setDataVencimento(Calendario.getData(obj.getDataVencimentoBoleto(), "dd/MM/yyyy"));

        boleto.setValorBoleto(Formatador.formatarValorMonetarioSemMoeda(obj.getValorBoleto()));
        if (obj.getValorItemRemessa() != null) {
            boleto.setValorBoleto(Formatador.formatarValorMonetarioSemMoeda(obj.getValorItemRemessa()));
        }

        List<String> descricaoProdutosBoleto = LayoutBoletoPadrao.montarListaProdutos(obj, false);

        Vector<ObjetoGenerico> descricaoProdutos = new Vector<ObjetoGenerico>();
        for (String descricao : descricaoProdutosBoleto) {
            ObjetoGenerico og = new ObjetoGenerico("descricao", descricao);
            descricaoProdutos.add(og);
        }

        boleto.setDescricoes(descricaoProdutos);

        //Usar Instrução1 para instruções do boleto
        boolean ehSocio = (obj.getClienteVO().getCategoria().getCodigo() != 0) &&
                (obj.getClienteVO().getCategoria().getTipoCategoriaClube() != null && ((TipoCategoriaClubeEnum.PROPRIETARIO.getCodigo() == obj.getClienteVO().getCategoria().getTipoCategoriaClube())
                        || (TipoCategoriaClubeEnum.DEPENDENTE.getCodigo() == obj.getClienteVO().getCategoria().getTipoCategoriaClube())
                        || (TipoCategoriaClubeEnum.ASSEMELHADO.getCodigo() == obj.getClienteVO().getCategoria().getTipoCategoriaClube())));
        String dataLimite = (ehSocio) ?
                Calendario.getData(Uteis.obterUltimoDiaMes(obj.getDataVencimentoBoleto()), "dd/MM/yyyy") :
                Calendario.getData(Uteis.somarDias(obj.getDataVencimentoBoleto(), 5), "dd/MM/yyyy");

        //Usar Instrução1 para a instrução no boleto.
        String instrucao = obj.getRemessa().getConvenioCobranca().getInstrucoesBoleto().replaceAll("<DATA_LIMITE>", dataLimite);
        try{
            instrucao = processarInstrucaoDesconto(instrucao, obj);
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
        try{
            instrucao = processarInstrucaoMatricula(instrucao, obj);
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
        boleto.setInstrucao1(instrucao);

        //Usar Instrução2 para Links
        boleto.setInstrucao2(obj.getRemessa().getConvenioCobranca().getSitesBoleto());

        //Usar Instrução3 para mês de referência do boleto
        boleto.setInstrucao3(Uteis.getMesNomeReferencia(obj.getDataVencimentoBoleto()).toUpperCase() + "/" + Uteis.getAnoData(obj.getDataVencimentoBoleto()));

        //Usar Instrução4 para mensagem informar quando será bloqueado;
        if (obj.getClienteVO().getCategoria().getTipoCategoria().equals(TipoCategoria.SOCIO.getCodigo())) {
            boleto.setInstrucao4("Seu acesso será bloqueado no primeiro dia do próximo mês caso não conste o pagamento referente a este boleto.");
        } else {
            boleto.setInstrucao4("Seu acesso será bloqueado 5 dias após o vencimento caso não conste o pagamento.");
        }

        //Usar instrução5 para dizer quais os boletos que estão em aberto.
        if (obj.getMesesAbertos() != null && !obj.getMesesAbertos().equals("")) {
            boleto.setInstrucao5("Quando este boleto foi gerado não constava pagamento referente ao(s) mes(es): " + obj.getMesesAbertos());
        } else {
            boleto.setInstrucao5("");
        }

        //boleto.setDescricoes(descricao);
        //TODO criar essa configuração no Banco, provavelmente outros bancos dependem disso
        if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BRADESCO)) {
            boleto.setLocalPagamento("Pagável preferencialmente na Rede Bradesco ou Bradesco Expresso.");
        }else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.CAIXA_ECONOMICA)) {
            boleto.setLocalPagamento("PREFERENCIALMENTE NAS CASAS LOTÉRICAS ATÉ O VALOR LIMITE");
        }else if(obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCO_DO_BRASIL)){
            boleto.setLocalPagamento("PAGAVEL EM QUALQUER BANCO");
        }
        else {
            boleto.setLocalPagamento("PAGAVEL EM QUALQUER BANCO ATE O VENCIMENTO");
        }
        boleto.setLocalPagamento2("");
        StringBuilder cedente = new StringBuilder();

        if(obj.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)){
            cedente.append(obj.getRemessa().getConvenioCobranca().getEmpresa().getRazaoSocial());
            cedente.append(" - CPNJ: ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getCNPJ());
        }else{
            cedente.append(obj.getRemessa().getConvenioCobranca().getEmpresa().getRazaoSocial());
        }

        if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BRADESCO) ||
                obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCO_DO_BRASIL) ||
                obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCO_BRB) ||
                obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.CAIXA_ECONOMICA) ||
                obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BNB) ||
                obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.ITAU) ||
                (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCOOB) &&
                        obj.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {

            if (!obj.getRemessa().getConvenioCobranca().getEmpresa().getCNPJ().isEmpty() &&
                    !obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.ITAU)) {
                cedente.append(" - CPNJ: ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getCNPJ());
            }

            if ((obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCOOB) &&
                    obj.getRemessa().getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))
                    || obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.ITAU)) {
                cedente.append(" - End.: ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getEndereco());
                CidadeVO cidade = obj.getRemessa().getConvenioCobranca().getEmpresa().getCidade();
                try {
                    if (cidade != null && cidade.getCodigo() != null && !cidade.getCodigo().equals(0)) {
                        cidade = getFacade().getCidade().consultarPorChavePrimaria(cidade.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        cedente.append(", ").append(cidade.getNome());
                        cedente.append("- ").append(cidade.getEstado().getSigla());
                    }
                } catch (Exception e) {

                }
            }
            if ((obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BRADESCO)
                    || obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCO_BRB)
                    || obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.CAIXA_ECONOMICA)
                    || obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCO_DO_BRASIL))
                    && !obj.getRemessa().getConvenioCobranca().getEmpresa().getEndereco().isEmpty()) {
                cedente.append(" - End.: ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getEndereco());
                if (obj.getRemessa().getConvenioCobranca().getEmpresa().getNumero().trim().length() > 0) {
                    cedente.append(", ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getNumero());
                }
                if (!obj.getRemessa().getConvenioCobranca().getEmpresa().getComplemento().isEmpty()) {
                    cedente.append(", ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getComplemento());
                }
                if(obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(JBoleto.BANCO_BRB)){
                    CidadeVO cidade = obj.getRemessa().getConvenioCobranca().getEmpresa().getCidade();
                    cedente.append(", CEP: ").append(obj.getRemessa().getConvenioCobranca().getEmpresa().getCEP());
                    try{
                        if(cidade != null && cidade.getCodigo() != null && !cidade.getCodigo().equals(0)){
                            cidade = getFacade().getCidade().consultarPorChavePrimaria(cidade.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            cedente.append(", ").append(cidade.getNome());
                            cedente.append(", ").append(cidade.getEstado().getSigla());
                        }
                    }catch (Exception e){

                    }
                }
            }
        }
        boleto.setCedente(cedente.toString());


        EnderecoVO enderecoVO = new EnderecoVO();
        for (EnderecoVO endereco : obj.getPessoa().getEnderecoVOs()) {
            if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                enderecoVO = endereco;
            }
        }


        //nova forma, informações gravadas no props
        String nome = obj.get(DCCAttEnum.NomePagador.name());
        String cpfCNPJ = obj.get(DCCAttEnum.CpfCnpjPagador.name());

        if (!UteisValidacao.emptyString(nome)
                && !UteisValidacao.emptyString(cpfCNPJ)) {

            boleto.setCpfSacado(cpfCNPJ);
            boleto.setResponsavel(nome);
            boleto.setNomeSacado(nome);

        } else {

            if (obj.getPessoa().getCategoriaPessoa().equals(TipoPessoa.JURIDICA)) {
                boleto.setCpfSacado(obj.getPessoa().getCnpj());
            } else {
                boleto.setCpfSacado(obj.getPessoa().getCfp());
            }

            if (obj.getRemessa().getConvenioCobranca().getEmpresa().isUtilizarNomeResponsavelNoBoleto() && Uteis.calcularIdadePessoa(Calendario.hoje(), obj.getClienteVO().getPessoa().getDataNasc()) < 18) {
                if (obj.getClienteVO().getPessoaResponsavel() != null && obj.getClienteVO().getPessoaResponsavel().getCodigo() > 0) {
                    boleto.setCpfSacado(obj.getClienteVO().getPessoaResponsavel().getCfp());
                    boleto.setResponsavel(obj.getClienteVO().getPessoaResponsavel().getNome());
                } else {
                    if (!UteisValidacao.emptyString(obj.getPessoa().getNomePai())) {
                        boleto.setCpfSacado(obj.getPessoa().getCpfPai());
                        boleto.setResponsavel(obj.getPessoa().getNomePai());
                    } else if (!UteisValidacao.emptyString(obj.getPessoa().getNomeMae())) {
                        boleto.setCpfSacado(obj.getPessoa().getCpfMae());
                        boleto.setResponsavel(obj.getPessoa().getNomeMae());
                    }
                }
            }
            boleto.setNomeSacado(obj.getPessoa().getNome());
        }

        StringBuilder enderecoSb = new StringBuilder();
        enderecoSb.append(enderecoVO.getEndereco());
        if (!UteisValidacao.emptyString(enderecoVO.getNumero())) {
            enderecoSb.append(", ").append(enderecoVO.getNumero());
        }
        if (!UteisValidacao.emptyString(enderecoVO.getComplemento())) {
            enderecoSb.append(", ").append(enderecoVO.getComplemento());
        }
        boleto.setNumConvenio(StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getRemessa().getConvenioCobranca().getNumeroContrato(), 7));
        boleto.setEnderecoSacado(enderecoSb.toString());
        boleto.setBairroSacado(enderecoVO.getBairro());
        boleto.setCepSacado(Formatador.removerMascara(enderecoVO.getCep()));
        boleto.setUfSacado(obj.getPessoa().getEstadoVO().getSigla());
        boleto.setCidadeSacado(obj.getPessoa().getCidade().getNome());
        boleto.setCodCliente(obj.getRemessa().getConvenioCobranca().getNumeroContrato());
        try {
            if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())){
                boleto.setNoDocumento(obj.getIdentificador().toString());
            }else{
                boleto.setNoDocumento(obj.getCodigo().toString());
            }
        }catch (Exception e){
            boleto.setNoDocumento(obj.getCodigo().toString());
        }

        calcularNossoNumeroEDV(obj, boleto);
        return boleto;
    }

    private void calcularNossoNumeroEDV(RemessaItemVO obj, JBoletoBean boleto){
        if(obj.getTipo().equals(TipoRemessaEnum.DAYCOVAL_BOLETO)){
            boleto.setNossoNumero(obj.getIdentificador().toString(), 10);
        }else if (obj.getTipo().equals(TipoRemessaEnum.BOLETO)) {
            if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                    && (!obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) &&
                    !obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
                boleto.setNossoNumero(obj.getIdentificador().toString(), 7);
                boleto.setDvNossoNumero(LayoutRemessaSicoobCNAB400.getDVNossoNumero(obj.getRemessa().getConvenioCobranca(), obj.getIdentificador()));
            } else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                    && (obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                    obj.getRemessa().getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
                boleto.setNossoNumero(obj.getIdentificador().toString(), 7);
                boleto.setDvNossoNumero(LayoutRemessaSicoobCNAB240.getDVNossoNumero(obj.getRemessa().getConvenioCobranca(), obj.getIdentificador()));
            } else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BNB.getCodigo())) {
                boleto.setNossoNumero(obj.getIdentificador().toString(), 7);
                boleto.setDvNossoNumero(LayoutRemessaBNBCNAB400.getDVNossoNumero(obj.getRemessa().getConvenioCobranca(), obj.getIdentificador()));
            } else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())) {
                boleto.setNossoNumero(LayoutRemessaCaixaCNAB240.calcularNossoNumero(obj));
                boleto.setDvNossoNumero(LayoutRemessaCaixaCNAB240.calcularDVNossoNumero(boleto, obj));
            } else if( obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo()) ) {
                boleto.setAgencia(obj.getRemessa().getConvenioCobranca().getContaEmpresa().getAgencia().replace(" ", ""));
                boleto.setDvAgencia(StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getRemessa().getConvenioCobranca().getContaEmpresa().getAgenciaDV().replace("", ""), 2));
                boleto.setContaCorrente(obj.getRemessa().getConvenioCobranca().getNumeroContrato().replace(" ", ""));
                boleto.setNossoNumero(BancoSicredi.gerarNossoNumero(obj.getIdentificador(), 2, Integer.parseInt(boleto.getAgencia()),
                        Integer.parseInt(boleto.getDvAgencia()), Integer.parseInt(boleto.getContaCorrente())));
            } else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SAFRA.getCodigo())) {
                String identificador = obj.getIdentificador().toString();
                boleto.setNossoNumero(identificador, 8);
                boleto.setDvNossoNumero(LayoutRemessaSafraCNAB400.calcularDVNossoNumero(boleto.getNossoNumero()));
            }else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
                String nossoNumero =  StringUtilities.formatarCampo(new BigDecimal(obj.getIdentificador()), 7);
                boleto.setNossoNumero(nossoNumero);
                boleto.setDvNossoNumero(obj.obterDVNossoNumeroCNB400(nossoNumero));
            } else {
                if (obj.getIdentificadorEmpresaFinanceiro().equals("")) {
                    boleto.setDvNossoNumero(obj.obterDVNossoNumeroCNB400(obj.getCodigo().toString()));
                    if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BRB.getCodigo())) {
                        boleto.setNossoNumero(LayoutRemessaBRBCNAB400.calcularNossoNumero(obj));
                    } else if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
                        boleto.setNossoNumero(LayoutRemessaBBCNAB240.calcularNossoNumero(obj, null), 10);
                    } else {
                        String nossoNumero = obj.getCodigo().toString();
                        if (obj.getRemessa().getConvenioCobranca().isUsarIdentificador()) {
                            nossoNumero = obj.getIdentificador().toString();
                        }
                        boleto.setNossoNumero(nossoNumero);
                    }
                } else {
                    if (obj.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
                        boleto.setNossoNumero(LayoutRemessaBBCNAB240.calcularNossoNumero(obj, obj.getIdentificadorEmpresaFinanceiro()), 10);
                        boleto.setNumConvenio(obj.getRemessa().getConvenioCobranca().getNumeroContrato());
                    } else {
                        boleto.setNossoNumero(obj.getIdentificadorEmpresaFinanceiro() + StringUtilities.formatarCampo(new BigDecimal(obj.getIdentificador().toString()), 8));
                    }
                }
            }
        }
    }

    private String processarInstrucaoDesconto(String instrucao, RemessaItemVO remessaItem) throws Exception{
        if(instrucao.contains("<desconto>")){
            int inicioDesconto = instrucao.indexOf("<desconto>");
            int finalDesconto = instrucao.indexOf("</desconto>");
            String aux = instrucao.substring(0, inicioDesconto);
            if(remessaItem.possuiDesconto() && remessaItem.getValorBoleto().doubleValue() > 0 && remessaItem.getDataPagamentoAntecipado() != null){
                String data = Uteis.getDataAplicandoFormatacao(remessaItem.getDataPagamentoAntecipado(), "dd/MM/yyyy");
                Double valor = remessaItem.getValorItemRemessa().doubleValue() - ( remessaItem.getValorBoleto().doubleValue() * remessaItem.getPorcentagemDescontoBoletoPagAntecipado().doubleValue() / 100);
                aux += instrucao.substring(inicioDesconto + 10, finalDesconto);
                aux = aux.replaceAll("TAG_DIA_DESCONTO", data);
                aux = aux.replaceAll("TAG_DESCONTO", Uteis.getDoubleFormatado(valor));
            }
            aux += instrucao.substring(finalDesconto + 11);
            instrucao = aux;
        }else if(instrucao.contains("<descontoBoleto>")){//desconto boletos convenio
            int inicioDesconto = instrucao.indexOf("<descontoBoleto>");
            int finalDesconto = instrucao.indexOf("</descontoBoleto>");
            String aux = instrucao.substring(0, inicioDesconto);
            if(remessaItem.getValorBoleto().doubleValue() > 0 && Calendario.hoje().before(Calendario.subtrairDias(remessaItem.getDataVencimentoBoleto(), 0))){
                String data =  Uteis.getDataAplicandoFormatacao(Calendario.subtrairDias(remessaItem.getDataVencimentoBoleto(), 0), "dd/MM/yyyy");
                 Double valor = remessaItem.getValorItemRemessa().doubleValue() - (remessaItem.getValorBoleto().doubleValue() * remessaItem.getPorcentagemDescontoBoleto() /100);
                aux += instrucao.substring(inicioDesconto + 16, finalDesconto);
                aux = aux.replaceAll("TAG_DIA_DESCONTO_BOLETO", data);
                aux = aux.replaceAll("TAG_DESCONTO_BOLETO", Uteis.getDoubleFormatado(valor));
            }
            aux += instrucao.substring(finalDesconto + 17);
            instrucao = aux;
        }
        return instrucao;
    }

    private String processarInstrucaoMatricula(String instrucao, RemessaItemVO remessaItem) throws Exception {
        if (instrucao.contains("<matricula>")) {
            int inicioMatricula = instrucao.indexOf("<matricula>");
            int finalMatricula = instrucao.indexOf("</matricula>");
            String aux = instrucao.substring(0, inicioMatricula);
            aux = aux + instrucao.substring((inicioMatricula + 11), finalMatricula);
            String matricula = "";
            if (remessaItem.getClienteVO() != null &&
                    !UteisValidacao.emptyString(remessaItem.getClienteVO().getMatricula())) {
                matricula = remessaItem.getClienteVO().getMatricula();
            }
            ;
            aux = aux.replaceAll("TAG_MATRICULA", matricula);
            aux += instrucao.substring(finalMatricula + 12);
            instrucao = aux;
        }
        return instrucao;
    }

    public void gravarRemessa(RemessaVO remessa, EmpresaVO empresa) throws Exception {
        Remessa remessaDAO = null;
        ConvenioCobranca convenioCobrancaDAO = null;
        MovParcela movParcelaDAO = null;
        try {
            remessaDAO = new Remessa(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);
            movParcelaDAO = new MovParcela(con);

            if(remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)){
                remessa.setIdentificador(remessa.getIdentificador().replace("XXXX", StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getSequencialDoArquivo(), 9)));
                remessa.setNomeArquivo(remessa.getIdentificador());
            }
            remessaDAO.incluir(remessa);
            if (remessa.isCancelamento()) {
                LayoutRemessaBase.preencherArquivoRemessaCancelamento(remessa);
            } else {
                LayoutRemessaBase.preencherArquivoRemessa(remessa);
            }

            //DCO NÃO CONSOME CREDITO PACTO
            if (!remessa.getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                if (remessa.isBoleto()) {
                    debitarCreditosDCCBoleto(empresa, remessa, false);
                } else {
                    debitarCreditosDCC(empresa, remessa, false);
                }
            }

            remessa.setSituacaoRemessa(SituacaoRemessaEnum.GERADA);
            remessaDAO.alterar(remessa);

            for (RemessaItemVO item : remessa.getListaItens()) {
                movParcelaDAO.incrementarNrTentativasParcelaConvenio(item, item.getRemessa().getConvenioCobranca());
            }

            if(remessa.isCancelamento()){
                convenioCobrancaDAO.incrementarSequencialArquivoCancelamento(remessa.getConvenioCobranca());
            }else{
                convenioCobrancaDAO.incrementarSequencialArquivo(remessa.getConvenioCobranca());
            }
        } catch (Exception e) {
            e.printStackTrace();
            remessaDAO.excluir(remessa);
            throw e;
        } finally {
            remessaDAO = null;
            convenioCobrancaDAO = null;
            movParcelaDAO = null;
        }
    }

    public void processarCobrancas(Date dia, String chave) {
        Set<Integer> conveniosUnicosProcessados = new HashSet<Integer>();
        ResultadoServicosVO resultadoGeral = new ResultadoServicosVO(ServicoEnum.REMESSA_SERVICE_GERAL);
        try {
            Uteis.logar(null, "ProcessarRemessa -> Iniciando Processamento de Remessas de Cobrança...");

            for (EmpresaVO empresa : listaEmpresas) {
                ResultadoServicosVO resultadoRemessaEmpresa = new ResultadoServicosVO(ServicoEnum.REMESSA_SERVICE_EMPRESA, "REMESSA_SERVICE - EMPRESA " + empresa.getCodigo(), empresa.getCodigo());

                Uteis.logar(null, "## Iniciando processamento Empresa: " + empresa.getNome());

                //verificar itens que foram pagos PJBank, Asaas e Banco do Brasil e que estão pendentes
                processarBoletosOnline(dia, empresa, 5);

                //verificador de transações pendentes
                verificarPendenciasTransacaoOnline(empresa, false);

                //verificador de parcelas vinculadas com as transacoes erradas
                verificarECorrigirParcelasVinculadasEmTransacoesErradas(empresa);

                List<ConvenioCobrancaVO> convenios;

                if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                    Uteis.logar(null, "## Retentativa Habilitado!...");
                    convenios = getFacade().getConfiguracaoReenvioMovParcelaEmpresa().consultarConvenioReenvioAutomatico(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS,
                            TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC_RETENTATIVA, SituacaoConvenioCobranca.ATIVO);
                } else {
                    convenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                            TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC, SituacaoConvenioCobranca.ATIVO);

                    List<ConvenioCobrancaVO> conveniosOnline = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                            TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC_ONLINE, SituacaoConvenioCobranca.ATIVO);
                    convenios.addAll(conveniosOnline);
                }

                convenios.addAll(getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                        TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCO, SituacaoConvenioCobranca.ATIVO));


                //processo com número de vezes fixa que a parcela irá passar no ciclo
                //Quando TRUE não incrementar "nrtentativasprocessoretentativa" em movParcela
                //incrementar somente ao final do ciclo dos convênios
                //by Luiz Felipe 06/05/2020
                Set<Integer> codParcelasIncrementarTentativa = new HashSet<>();
                Set<Integer> codParcelasVindi = new HashSet<>();


                //Cuidado ao alterar aqui...
                //Verifique onde é utilizado tem uma explicação.!
                //by Luiz Felipe
                Set<Integer> convenioProcessados = new HashSet<>();

                for (ConvenioCobrancaVO conv : convenios) {

                    Uteis.logar(null, "## Iniciando processamento Convênio: " + conv.getDescricao());

                    //Caso o convênio estiver para somente extrato não deve ser processado pagamento nesse convênio.
                    //Ignorar o convênio..
                    //by Luiz Felipe 16/09/2020
                    if (conv.isSomenteExtrato()) {
                        Uteis.logar(null, "## Convênio está marcado SOMENTE EXTRATO | " + conv.getDescricao());
                        continue;
                    }

                    //será consultado os convênios DCC EDI
                    //Necessário fazer isso quando está habilitado o HabilitarReenvioAutomaticoRemessa pois pode acontecer do convênio não estar na lista de retentativa porem ele está ativo.
                    //Se não adicionar ele, NUNCA irá fazer o processo automático de remessas seja envio ou retorno
                    //AQUI NÃO SERÁ GERADO REMESSAS!! E NÃO DEVE GERAR AQUI!!
                    //Isso para evitar que o sistema processe o convênio e seja gerado remessas!
                    //by Luiz Felipe 09/04/2020
                    convenioProcessados.add(conv.getCodigo());


                    if (conv.isGerarArquivoUnico() && conveniosUnicosProcessados.contains(conv.getCodigo())) {
                        Uteis.logar(null, "## Convênio já processado " + conv.getDescricao());
                        continue;
                    } else if (conv.isGerarArquivoUnico()) {
                        Uteis.logar(null, "## Adicionar convênio " + conv.getDescricao());
                        conveniosUnicosProcessados.add(conv.getCodigo());
                    }

                    conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(conv.getCodigo(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);


                    boolean cobrancasAutomaticasDesativada = !getFacade().getPactoPayConfig().isEnvioAutomaticoCobranca(empresa.getCodigo());
                    if (cobrancasAutomaticasDesativada) {
                        Uteis.logar(null, "###################################################################################################################################################################");
                        Uteis.logar(null, "###### PACTOPAY ENVIO_AUTOMATICO_ COBRANCA - EMPRESA " + empresa.getCodigo() + " - " + empresa.getNome() + " está desativado ######################");
                        Uteis.logar(null, "###################################################################################################################################################################");
                    }


                    //opção de bloquear as cobranças automáticas!
                    //by Luiz Felipe 28/04/2020
                    if (conv.isBloquearCobrancaAutomatica()) {
                        Uteis.logar(null, "###################################################################################################################################################################");
                        Uteis.logar(null, "###### CONVÊNIO " + conv.getCodigo() + " - " + conv.getDescricao().toUpperCase() + " está com as cobranças automáticas BLOQUEADAS (bloquearCobrancaAutomatica)");
                        Uteis.logar(null, "###################################################################################################################################################################");
                    }

                    if (conv.getTipo().isTransacaoOnline()) {

                        ResultadoServicosVO resultadoTransacao = new ResultadoServicosVO(ServicoEnum.TRANSACAO, "CONVENIO: " + conv.getCodigo() +  " || EMPRESA: " + empresa.getCodigo(), empresa.getCodigo());
                        try {
                            Uteis.logar(null, "## Iniciando Transação Online: " + conv.getTipo().getDescricao().toUpperCase() + " ...");
                            if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                                passeiPorConvenioOnlineProcessoMultiplosConvenios = true;
                                Uteis.logar(null, "## Retentativa Habilitado ## Transação Online Parcelas");
                            }

                            if (conv.isBloquearCobrancaAutomatica() || cobrancasAutomaticasDesativada) {
                                String msgBloq = "Transação Online - Convênio " + conv.getDescricao().toUpperCase() + " está com as cobranças automáticas BLOQUEADAS (bloquearCobrancaAutomatica)";
                                Uteis.logar(null, "## " + msgBloq);
                                resultadoTransacao.getResultado().put(msgBloq);
                            } else {
                                List<String> msgErro = new ArrayList<String>();

                                //Realizar cobranças Transação Automático
                                PagamentoService pagamentoService = new PagamentoService(getCon(), conv);
                                pagamentoService.processarCobrancasOnlineAutomatico(dia, msgErro, codParcelasIncrementarTentativa, codParcelasVindi);

                                for (String msg : msgErro) {
                                    resultadoTransacao.getResultado().put(msg);
                                }
                                if (!UteisValidacao.emptyList(msgErro)) {
                                    ModeloMensagemSistemaService modeloMensagemSistemaService = new ModeloMensagemSistemaService();
                                    if(!getConfigSistema().getListaEmailsRecorrencia().isEmpty()){
                                        modeloMensagemSistemaService.enviarEmailRecorrrencia(IdentificadorMensagemSistema.EMAIL_RECORRENCIA_ERROS_TRANSACAO, empresa, msgErro, getConfigSistema().getListaEmailsRecorrencia(), SuperControle.getConfiguracaoSMTPNoReply());
                                    } else {
                                        Uteis.logar(null, "Email's Recorrência não configurados");
                                    }
                                }
                            }
                        } catch (Exception e) {
                            resultadoTransacao.getResultado().put(e.getMessage());
                            e.printStackTrace();
                            Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                        } finally {
                            gravarResultadoServicos(resultadoTransacao);
                        }

                    } else {

                        if (conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && !conv.isProcessarRemessasAutomatico()) {
                            Uteis.logar(null, "## NÃO CONFIGURADO PARA GERAR REMESSA AUTOMÁTICO - " + conv.getTipo().getTipoCobranca().getDescricao() +  ": "   + conv.getTipo().getDescricao().toUpperCase());
                            continue;
                        } else if (conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && conv.isProcessarRemessasAutomatico()) {
                            Uteis.logar(null, "## REALIZAR RETORNO AUTOMÁTICO - " + conv.getTipo().getTipoCobranca().getDescricao() +  ": "   + conv.getTipo().getDescricao().toUpperCase());
                            realizarBaixaAutomaticaRetornoDCO(conv);
                        }


                        VerificadorRemessaRejeitadaEDIService rejeitadaEDIService = null;
                        try {
                            Uteis.logar(null, "## Iniciando VerificadorRemessaRejeitadaEDIService " + conv.getDescricao());
                            rejeitadaEDIService = new VerificadorRemessaRejeitadaEDIService();
                            rejeitadaEDIService.verificarRemessasRejeitadas(conv, empresa);
                        } catch (Exception ex) {
                            Uteis.logar(null, "## Erro VerificadorRemessaRejeitadaEDIService " + conv.getDescricao() + " | " + ex.getMessage());
                        } finally {
                            Uteis.logar(null, "## Fim VerificadorRemessaRejeitadaEDIService " + conv.getDescricao());
                            rejeitadaEDIService = null;
                        }

                        Uteis.logar(null, "## Iniciando Remessa " + conv.getTipo().getTipoCobranca().getDescricao() +  ": "   + conv.getTipo().getDescricao().toUpperCase() + " - " + conv.getDescricao() + " ...");


                        //CONSULTAR PARCELAS
                        ResultadoServicosVO resultadoGeracaoRemessa = new ResultadoServicosVO(ServicoEnum.REMESSA, "CONVENIO: " + conv.getCodigo() +  " || EMPRESA: " + empresa.getCodigo(), empresa.getCodigo());
                        try {

                            List<MovParcelaVO> listaParcelas = new ArrayList<>();
                            if (conv.isBloquearCobrancaAutomatica() || cobrancasAutomaticasDesativada) {
                                String msgBloq = "Remessas - Convênio " + conv.getDescricao().toUpperCase() + " está com as cobranças automáticas BLOQUEADAS (bloquearCobrancaAutomatica)";
                                Uteis.logar(null, "## " + msgBloq);
                                resultadoGeracaoRemessa.getResultado().put(msgBloq);
                            } else {
                                listaParcelas = consultarParcelasProcessoAutomatico(dia, empresa, conv);
                            }

                            //SOMENTE LOG
                            if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                                Uteis.logar(null, "## Retentativa Habilitado ## Parcelas compatíveis da Empresa: " + empresa.getNome() + ". Tamanho Lista: " + listaParcelas.size() + " Convenio: " + conv.getDescricao());
                            }

                            if (!listaParcelas.isEmpty()) {
                                Uteis.logar(null, "# Preencher Remessas com as parcelas compatíveis da Empresa: " + empresa.getNome() + ". Tamanho Lista: " + listaParcelas.size());


                                //MONTAR MULTA E JUROS
                                getFacade().getZWFacade().getMovParcela().montarMultaJurosParcelaVencida(conv.getEmpresa(), conv.getTipo().getTipoCobranca(), listaParcelas, dia);


                                List<RemessaVO> remessas = new ArrayList<RemessaVO>();
                                try {
                                    List<String> msgErro = new ArrayList<>();
                                    remessas = preencherRemessas(listaParcelas, conv, empresa, false, usuarioVO, msgErro);
                                    for (String msg : msgErro) {
                                        resultadoGeracaoRemessa.getResultado().put(msg);
                                    }
                                } catch (Exception ex) {
                                    resultadoGeracaoRemessa.getResultado().put(ex.getMessage());
                                    Uteis.logar(null, "## Não foram geradas novas remessas na Empresa: " + empresa.getNome());
                                    Uteis.logar(null, "## Motivo do erro:  " + ex.getMessage());
                                }

                                for (RemessaVO remessa : remessas) {
                                    try {
                                        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                                            remessa.setIdentificador(remessa.getIdentificador().replace("XXXX", StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getSequencialDoArquivo(), 9)));
                                            remessa.setNomeArquivo(remessa.getIdentificador());
                                        }
                                        getFacade().getZWFacade().getRemessa().incluir(remessa);
                                        Uteis.logar(null, "# Incluída. Preparar pra gerar arquivos...");
                                        LayoutRemessaBase.preencherArquivoRemessa(remessa);
                                        remessa.setSituacaoRemessa(SituacaoRemessaEnum.GERADA);
                                        getFacade().getZWFacade().getRemessa().alterar(remessa);
                                        //DCO NÃO CONSOME CREDITO PACTO
                                        if (!conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                                            if (remessa.isBoleto()) {
                                                debitarCreditosDCCBoleto(empresa, remessa, passeiPorConvenioOnlineProcessoMultiplosConvenios);
                                            } else {
                                                debitarCreditosDCC(empresa, remessa, passeiPorConvenioOnlineProcessoMultiplosConvenios);
                                            }
                                        }
                                        Uteis.logar(null, "# Arquivos gerados!");
                                        Uteis.logar(null, "# Header: " + remessa.getHead().toString());
                                        Uteis.logar(null, "# Trailer: " + remessa.getTrailer().toString());
                                        Uteis.logar(null, "# Atualizar Tentativas de Parcelas...");
                                        for (RemessaItemVO item : remessa.getListaItens()) {
                                            getFacade().getMovParcela().incrementarNrTentativasParcelaConvenio(item, item.getRemessa().getConvenioCobranca());

                                            //Incrementa retentiva somente para Cartão de Crédito
                                            if (conv.getTipo().getTipoCobranca() != null &&
                                                    !conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                                                if (!UteisValidacao.emptyNumber(item.getMovParcela().getCodigo())) {
                                                    codParcelasIncrementarTentativa.add(item.getMovParcela().getCodigo());
                                                    codParcelasVindi.remove(item.getMovParcela().getCodigo());
                                                } else if (!UteisValidacao.emptyList(item.getMovParcelas())) {
                                                    for (RemessaItemMovParcelaVO itemVO : item.getMovParcelas()) {
                                                        codParcelasIncrementarTentativa.add(itemVO.getMovParcelaVO().getCodigo());
                                                        codParcelasVindi.remove(itemVO.getMovParcelaVO().getCodigo());
                                                    }
                                                }
                                            }
                                        }
                                        getFacade().getConvenioCobranca().incrementarSequencialArquivo(conv);
                                        Uteis.logar(null, "# tentativas Atualizadas!");
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        Uteis.logar(null, e.getMessage());
                                        resultadoGeracaoRemessa.getResultado().put(e.getMessage());
                                        getFacade().getZWFacade().getRemessa().excluir(remessa);
                                        break;
                                    }
                                }
                            } else {
                                String msgSemParcela = "# Não existe parcela para ser gerada: " + conv.getDescricao();
                                Uteis.logar(null, msgSemParcela);
                                resultadoGeracaoRemessa.getResultado().put(msgSemParcela);
                            }

                        } catch (Exception ex) {
                            ex.printStackTrace();
                            resultadoGeracaoRemessa.getResultado().put(ex.getMessage());
                        } finally {
                            gravarResultadoServicos(resultadoGeracaoRemessa);
                        }

                        //AGRUPAR REMESSAS GETNET
                        //by Luiz Felipe 31/03/2020
                        //esse processo tem q rodar AQUI ! antes de enviar as remessas e após a geração !!!
                        agruparRemessasGetnet(conv, empresa);

                        //enviar Remotamente
                        //processo enviar remoto
                        processoUnicoEnviarRemessas(conv.getCodigo(), empresa.getCodigo(), usuarioVO, true);
                    }// fim else convenio não é online
                }// fim for convenio..



                //será consultado os convênios DCC EDI
                //Necessário fazer isso quando está habilitado o HabilitarReenvioAutomaticoRemessa pois pode acontecer do convênio não estar na lista de retentativa porem ele está ativo.
                //Se não adicionar ele, NUNCA irá fazer o processo automático de remessas seja envio ou retorno
                //AQUI NÃO SERÁ GERADO REMESSAS!! E NÃO DEVE GERAR AQUI!!
                //Isso para evitar que o sistema processe o convênio e seja gerado remessas!
                //by Luiz Felipe 09/04/2020
                processarRetornoEnvioConvenioEDI(convenioProcessados, empresa);



                //processo com número de vezes fixa que a parcela irá passar no ciclo
                //Quando TRUE não incrementar "nrtentativasprocessoretentativa" em movParcela
                //incrementar somente ao final do ciclo
                //by Luiz Felipe 06/05/2020
                if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                    Set<Integer> fullSetParcelas = new HashSet<>();
                    fullSetParcelas.addAll(codParcelasIncrementarTentativa);
                    fullSetParcelas.addAll(codParcelasVindi);
                    for (int codParcela : fullSetParcelas) {
                        getFacade().getMovParcela().incrementarNrTentativasProcessoRetentativa(codParcela);
                    }
                }


                //Multiplos Convenios debitar apenas um saldo de credito dcc ao fim de cada ciclo.
                //Somente transacao, pois parcela de remessa já debita apenas 1 por ciclo, pois a mesma cai na situacao de aguardando retorno e nao entra no proximo convenio do mesmo ciclo.
                if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                    Empresa empresaDao = new Empresa(con);
                    Transacao transacaoDAO = new Transacao(con);
                    List<TransacaoVO> transacoes = transacaoDAO.consultarGeradasAgoraPelaRecorrencia(empresa.getCodigo(), usuarioVO.getCodigo());


                    Set<Integer> fullSetParcelas = new HashSet<>(codParcelasIncrementarTentativa);
                    if (empresa.isCobrarCreditoVindi()) {
                        fullSetParcelas.addAll(codParcelasVindi);
                    }

                    int quantidadeCobrar = codParcelasIncrementarTentativa.size();
                    Uteis.logar("Contabilizando " + quantidadeCobrar + " créditos devido ao processamento automático;");
                    transacaoDAO.debitarCreditosDCC(true, transacoes, empresa, empresaDao, quantidadeCobrar);
                    Uteis.logar("Contabilização OK!");

                    empresaDao = null;
                    transacaoDAO = null;
                }

                try {
                    String identificador = getIdentifcadorBICache(empresa, dia) + "|" + BIEnum.DCC.name();
                    getFacade().getMemCachedManager().remover(ResultadoBITO.class, identificador, getKey());
                } catch (Exception e) {
                    Uteis.logar(null, "# ERRO ao remover dados da MemCached: " + e.getMessage());
                    e.printStackTrace();
                }

                try {
                    Remessa remessa = new Remessa(con);

                    Integer totalRemessas = remessa.consultarTotalConvitePorIndicado(empresa.getCodigo());
                    if (totalRemessas > 0){

                        NotificacaoUsuarioVO notificacaoUsuarioVO = new NotificacaoUsuarioVO();
                        notificacaoUsuarioVO.getUsuarioVO().setCodigo(usuarioVO.getCodigo());
                        notificacaoUsuarioVO.setEmpresaVO(empresa);
                        notificacaoUsuarioVO.getTipo().setCodigo(TipoNotificacaoUsuarioEnum.REMESSA_SEM_RETORNO.getCodigo());
                        notificacaoUsuarioVO.setDataLancamento(dia);
                        notificacaoUsuarioVO.setMensagem("Você possui " + totalRemessas + " remessas que não retornaram dentro do prazo." +
                                " Já estamos verificando o ocorrido junto a sua adquirente para solucionar o problema. Favor aguarde a conclusão.");
                        notificacaoUsuarioVO.setApresentarHoje(true);
                        NotificacaoUsuario notificacaoUsuario = new NotificacaoUsuario(con);
                        notificacaoUsuario.incluir(notificacaoUsuarioVO);
                    }
                } catch (Exception e) {
                    Uteis.logar(null, "# ERRO ao criar notificação de remessas: " + e.getMessage());
                    e.printStackTrace();
                }

                gravarResultadoServicos(resultadoRemessaEmpresa);
            }

            Uteis.logar(null, "Terminando REMESSA DO DIA -> " + Uteis.getDataComHora(dia));

            if (!processarApenasRemessas) {
                //Transações ON-LINE
                RecorrenciaService recorrenciaService = new RecorrenciaService(con);
                recorrenciaService.processarDia(dia);
                recorrenciaService.processarRenovacoesAutomaticas(dia);
                recorrenciaService.processarRenovacoesAutomaticasProduto(dia);
                recorrenciaService.processarAnuidadeContratoRecorrencia(dia);
                recorrenciaService.processarCancelamentoAutomaticoContratosForaRegimeRecorrencia(dia);
                recorrenciaService.processarCancelamentoAutomaticoContratosSemAssinatura(dia);

                //Estorno Automático
                EstornoContratoAutomaticoService estornoService = new EstornoContratoAutomaticoService(con);
                estornoService.estornarContratosAutomaticamente(dia);
                estornoService.estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(dia, false);
                estornoService.estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(dia, true);
            }
            //

            //processar a Régua de Cobrança do PactoPay
            processarReguaCobrancaPactoPay();

            //Processo gerar recibo para transações aprovadas sem recibo
            Transacao transacaoDAO;
            try {
                Uteis.logarDebug("Vou rodar o processo de gerar recibo para transações sem recibo caso houver...");
                transacaoDAO = new Transacao(con);
                JSONObject json = transacaoDAO.gerarReciboTransacoesSemRecibo();
                StringBuilder msg = new StringBuilder();
                msg.append("Total: ").append(json.getInt("total")).append(" ");
                msg.append("Sucesso: ").append(json.getInt("ajustado")).append(" ");
                msg.append("Falha: ").append(json.getInt("erro")).append(" ");
                Uteis.logarDebug(msg.toString());
            } catch (Exception e) {
                StringBuilder sb = new StringBuilder();
                sb.append(" Não foi possível executar o processo de geração de recibo para transações sem recibo no dia ").append(Uteis.getData(dia));
                sb.append(" em: ").append(Uteis.getDataComHora(Calendario.hoje()));
                sb.append(" - ERRO: ").append(e.getMessage());
                Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, sb.toString() + " - " + chave, e);
            } finally {
                transacaoDAO = null;
                Uteis.logarDebug("Fim processo de gerar recibo para transações sem recibo...");
            }

        } catch (Exception e) {
            resultadoGeral.getResultado().put(e.getMessage());
            Uteis.logar(null, "# ERRO ao processar Remessas: " + e.getMessage());
            e.printStackTrace();
        }finally {
            ThreadEnviarEmail.finalmente();
            Uteis.logar(null, "ProcessarRemessa -> Finalizando Processamento!");
            gravarResultadoServicos(resultadoGeral);
        }
    }

    public void processarTransacoesOnlineManual(Date dia) {
        Set<Integer> conveniosUnicosProcessados = new HashSet<Integer>();
        try {
            Uteis.logar(null, "processarTransacoesOnlineManual -> Iniciando Processamento de Remessas de Cobrança...");

            for (EmpresaVO empresa : listaEmpresas) {

                Uteis.logar(null, "## Iniciando processamento Empresa: " + empresa.getNome());

                //verificador de transações pendentes
                verificarPendenciasTransacaoOnline(empresa, false);

                //verificador de parcelas vinculadas com as transacoes erradas
                verificarECorrigirParcelasVinculadasEmTransacoesErradas(empresa);

                List<ConvenioCobrancaVO> convenios = new ArrayList<>();

                if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                    Uteis.logar(null, "## Retentativa Habilitado!...");
                    convenios = getFacade().getConfiguracaoReenvioMovParcelaEmpresa().consultarConvenioReenvioAutomatico(empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS,
                            TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC_RETENTATIVA, SituacaoConvenioCobranca.ATIVO);
                } else {
                    List<ConvenioCobrancaVO> conveniosOnline = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                            TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC_ONLINE, SituacaoConvenioCobranca.ATIVO);
                    convenios.addAll(conveniosOnline);
                }

                //processo com número de vezes fixa que a parcela irá passar no ciclo
                //Quando TRUE não incrementar "nrtentativasprocessoretentativa" em movParcela
                //incrementar somente ao final do ciclo dos convênios
                //by Luiz Felipe 06/05/2020
                Set<Integer> codParcelasIncrementarTentativa = new HashSet<>();
                Set<Integer> codParcelasVindi = new HashSet<>();


                //Cuidado ao alterar aqui...
                //Verifique onde é utilizado tem uma explicação.!
                //by Luiz Felipe
                Set<Integer> convenioProcessados = new HashSet<>();

                for (ConvenioCobrancaVO conv : convenios) {

                    Uteis.logar(null, "## Iniciando processamento Convênio: " + conv.getDescricao());

                    //Caso o convênio estiver para somente extrato não deve ser processado pagamento nesse convênio.
                    //Ignorar o convênio..
                    //by Luiz Felipe 16/09/2020
                    if (conv.isSomenteExtrato()) {
                        Uteis.logar(null, "## Convênio está marcado SOMENTE EXTRATO | " + conv.getDescricao());
                        continue;
                    }

                    //será consultado os convênios DCC EDI
                    //Necessário fazer isso quando está habilitado o HabilitarReenvioAutomaticoRemessa pois pode acontecer do convênio não estar na lista de retentativa porem ele está ativo.
                    //Se não adicionar ele, NUNCA irá fazer o processo automático de remessas seja envio ou retorno
                    //AQUI NÃO SERÁ GERADO REMESSAS!! E NÃO DEVE GERAR AQUI!!
                    //Isso para evitar que o sistema processe o convênio e seja gerado remessas!
                    //by Luiz Felipe 09/04/2020
                    convenioProcessados.add(conv.getCodigo());


                    if (conv.isGerarArquivoUnico() && conveniosUnicosProcessados.contains(conv.getCodigo())) {
                        Uteis.logar(null, "## Convênio já processado " + conv.getDescricao());
                        continue;
                    } else if (conv.isGerarArquivoUnico()) {
                        Uteis.logar(null, "## Adicionar convênio " + conv.getDescricao());
                        conveniosUnicosProcessados.add(conv.getCodigo());
                    }

                    conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(conv.getCodigo(), empresa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);


                    boolean cobrancasAutomaticasDesativada = !getFacade().getPactoPayConfig().isEnvioAutomaticoCobranca(empresa.getCodigo());
                    if (cobrancasAutomaticasDesativada) {
                        Uteis.logar(null, "###################################################################################################################################################################");
                        Uteis.logar(null, "###### PACTOPAY ENVIO_AUTOMATICO_ COBRANCA - EMPRESA " + empresa.getCodigo() + " - " + empresa.getNome() + " está desativado ######################");
                        Uteis.logar(null, "###################################################################################################################################################################");
                    }


                    //opção de bloquear as cobranças automáticas!
                    //by Luiz Felipe 28/04/2020
                    if (conv.isBloquearCobrancaAutomatica()) {
                        Uteis.logar(null, "###################################################################################################################################################################");
                        Uteis.logar(null, "###### CONVÊNIO " + conv.getCodigo() + " - " + conv.getDescricao().toUpperCase() + " está com as cobranças automáticas BLOQUEADAS (bloquearCobrancaAutomatica)");
                        Uteis.logar(null, "###################################################################################################################################################################");
                    }

                    if (conv.getTipo().isTransacaoOnline()) {

                        ResultadoServicosVO resultadoTransacao = new ResultadoServicosVO(ServicoEnum.TRANSACAO, "CONVENIO: " + conv.getCodigo() + " || EMPRESA: " + empresa.getCodigo(), empresa.getCodigo());
                        try {
                            Uteis.logar(null, "## Iniciando Transação Online: " + conv.getTipo().getDescricao().toUpperCase() + " ...");
                            if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                                passeiPorConvenioOnlineProcessoMultiplosConvenios = true;
                                Uteis.logar(null, "## Retentativa Habilitado ## Transação Online Parcelas");
                            }

                            if (conv.isBloquearCobrancaAutomatica() || cobrancasAutomaticasDesativada) {
                                String msgBloq = "Transação Online - Convênio " + conv.getDescricao().toUpperCase() + " está com as cobranças automáticas BLOQUEADAS (bloquearCobrancaAutomatica)";
                                Uteis.logar(null, "## " + msgBloq);
                                resultadoTransacao.getResultado().put(msgBloq);
                            } else {
                                List<String> msgErro = new ArrayList<String>();

                                //Realizar cobranças Transação Automático
                                PagamentoService pagamentoService = new PagamentoService(getCon(), conv);
                                pagamentoService.processarCobrancasOnlineAutomatico(dia, msgErro, codParcelasIncrementarTentativa, codParcelasVindi);

                                for (String msg : msgErro) {
                                    resultadoTransacao.getResultado().put(msg);
                                }
                                if (!UteisValidacao.emptyList(msgErro)) {
                                    ModeloMensagemSistemaService modeloMensagemSistemaService = new ModeloMensagemSistemaService();
                                    if (!getConfigSistema().getListaEmailsRecorrencia().isEmpty()) {
                                        modeloMensagemSistemaService.enviarEmailRecorrrencia(IdentificadorMensagemSistema.EMAIL_RECORRENCIA_ERROS_TRANSACAO, empresa, msgErro, getConfigSistema().getListaEmailsRecorrencia(), SuperControle.getConfiguracaoSMTPNoReply());
                                    } else {
                                        Uteis.logar(null, "Email's Recorrência não configurados");
                                    }
                                }
                            }
                        } catch (Exception e) {
                            resultadoTransacao.getResultado().put(e.getMessage());
                            e.printStackTrace();
                            Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                        } finally {
                            gravarResultadoServicos(resultadoTransacao);
                        }

                    }
                }
                // fim for convenio..

                //processo com número de vezes fixa que a parcela irá passar no ciclo
                //Quando TRUE não incrementar "nrtentativasprocessoretentativa" em movParcela
                //incrementar somente ao final do ciclo
                //by Luiz Felipe 06/05/2020
                if (empresa.isHabilitarReenvioAutomaticoRemessa()) {
                    Set<Integer> fullSetParcelas = new HashSet<>();
                    fullSetParcelas.addAll(codParcelasIncrementarTentativa);
                    fullSetParcelas.addAll(codParcelasVindi);
                    for (int codParcela : fullSetParcelas) {
                        getFacade().getMovParcela().incrementarNrTentativasProcessoRetentativa(codParcela);
                    }
                }
            }

            Uteis.logar(null, "Terminando processarTransacoesOnlineManual DO DIA -> " + Uteis.getDataComHora(dia));
        } catch (Exception e) {
            Uteis.logar(null, "# ERRO processarTransacoesOnlineManual: " + e.getMessage());
            e.printStackTrace();
        } finally {
            Uteis.logar(null, "processarTransacoesOnlineManual -> Finalizando Processamento!");
        }
    }

    public void agruparRemessasGetnet(ConvenioCobrancaVO conv, EmpresaVO empresa) {
        //AGRUPAR REMESSAS GETNET
        //by Luiz Felipe 31/03/2020
        //esse processo tem q rodar AQUI ! antes de enviar as remessas e após a geração !!!
        AgruparRemessasGetnet agruparService = null;
        try {
            Uteis.logar(null, "## Iniciando AgruparRemessasGetnet " + conv.getDescricao());
            agruparService = new AgruparRemessasGetnet();
            agruparService.agruparRemessasGeradas(conv, empresa);
        } catch (Exception ex) {
            Uteis.logar(null, "## Erro AgruparRemessasGetnet " + conv.getDescricao() + " | " + ex.getMessage());
        } finally {
            Uteis.logar(null, "## Fim AgruparRemessasGetnet " + conv.getDescricao());
            agruparService = null;
        }
    }

    private void gravarResultadoServicos(ResultadoServicosVO obj) {
        try {
            getFacade().getResultadoServicos().gravarResultado(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "# ERRO gravarResultadoServicos: " + ex.getMessage());
        }
    }

    private void processarReguaCobrancaPactoPay() {
        PactoPayConfig pactoPayConfigDAO;
        try {
            Uteis.logar(null, "ProcessarReguaCobrancaPactoPay | Início...");
            pactoPayConfigDAO = new PactoPayConfig(this.con);
            for (EmpresaVO empresaVO : listaEmpresas) {
                if (!empresaVO.isFacilitePayReguaCobranca()) {
                    Uteis.logar(null, "ProcessarReguaCobrancaPactoPay | Não está habilitado régua de cobrança: Empresa " + empresaVO.getCodigo() + " | " + empresaVO.getNome());
                    continue;
                }
                pactoPayConfigDAO.processarCobrancaAntecipada(empresaVO.getCodigo(), false, null, false);
                pactoPayConfigDAO.processarCobrancaPendente(empresaVO.getCodigo(), false, null, false);
                pactoPayConfigDAO.processarCartao(empresaVO.getCodigo(), false, null, false);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "ProcessarReguaCobrancaPactoPay | ERRO: " + ex.getMessage());
        } finally {
            pactoPayConfigDAO = null;
            Uteis.logar(null, "ProcessarReguaCobrancaPactoPay | Início...");
        }
    }

    public void verificarPendenciasTransacaoOnlineGeral() {
        for (EmpresaVO empresaVO : this.listaEmpresas) {
            verificarPendenciasTransacaoOnline(empresaVO, false);
        }
    }

    public void verificarPendenciasTransacaoOnline(EmpresaVO empresaVO, boolean somenteCancelarTransacoesVerificacaoCartao) {
        try {
            List<ConvenioCobrancaVO> conveniosOnline = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresaVO.getCodigo(),
                    false, Uteis.NIVELMONTARDADOS_TODOS, TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC_ONLINE, SituacaoConvenioCobranca.ATIVO);
            for (ConvenioCobrancaVO conv : conveniosOnline) {
                PagamentoService pagamentoService = null;
                try {
                    pagamentoService = new PagamentoService(getCon(), conv);
                    pagamentoService.verificarPendenciasTransacaoOnline(conv, empresaVO, somenteCancelarTransacoesVerificacaoCartao);
                } catch (Exception ex) {
                    ex.printStackTrace();
                } finally {
                    pagamentoService = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void verificarECorrigirParcelasVinculadasEmTransacoesErradas(EmpresaVO empresaVO) {
        try {
            VerificadorTransacaoService service = null;
            try {
                service = new VerificadorTransacaoService(getCon());
                service.verificarECorrigirParcelasVinculadasEmTransacoesErradas(empresaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                service = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void processarBoletosPendentesGeral(Date dataVencimentoLimite) {
        for (EmpresaVO empresaVO : listaEmpresas) {
            processarBoletosPendentes(empresaVO, true, dataVencimentoLimite);
        }
    }

    private void verificarPodeProcessarConvenioBoleto(ConvenioCobrancaVO obj) throws Exception {
        if (obj.getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
            return;
        } else if (obj.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                (obj.getDescricao().toLowerCase().contains("importacao") ||
                        obj.getDescricao().toLowerCase().contains("importação"))) {
            return;
        }
        throw new Exception("Processamento " + obj.getTipo().getDescricao() + " | Ignorar convênio: " + obj.getDescricao() + " | Situação: " + obj.getSituacao().getDescricao());
    }

    private void processarBoletosPendentesCancelamento(EmpresaVO empresaVO) {
        try {
            Uteis.logarDebug("Boletos Pendentes de Cancelamento | Empresa: " + empresaVO.getNome() + " | Iniciando...");

            Integer[] convenioPJ = new Integer[]{TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo()};
            List<ConvenioCobrancaVO> listaConvenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresaVO.getCodigo(), false,
                    Uteis.NIVELMONTARDADOS_TODOS, convenioPJ, null);

            for (ConvenioCobrancaVO convVO : listaConvenios) {
                Boleto boletoDAO;
                try {
                    boletoDAO = new Boleto(con);

                    Uteis.logarDebug("Boletos Pendentes de Cancelamento | Convenio: " + convVO.getDescricao());

                    this.verificarPodeProcessarConvenioBoleto(convVO);

                    List<BoletoVO> listaAguardando = boletoDAO.consultarPendentesCancelamento(convVO, empresaVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    Uteis.logarDebug("Boletos Pendentes de Cancelamento | Total Aguardando: " + listaAguardando.size());

                    int i = 0;
                    for (BoletoVO boletoVO : listaAguardando) {
                        StringBuilder msg  = new StringBuilder();
                        msg.append("Boletos Pendentes de Cancelamento | ").append(++i).append("/").append(listaAguardando.size());
                        try {
                            msg.append(" | Boleto ").append(boletoVO.getCodigo());
                            boletoDAO.cancelarBoleto(boletoVO, this.usuarioVO, "Boletos Pendentes de Cancelamento - RemessaService");
                            msg.append(" | Cancelado");
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg.append(" | Erro: ").append(ex.getMessage());
                            boletoDAO.incluirBoletoHistorico(boletoVO.getCodigo(), "TENTATIVA_CANCELAMENTO_PENDENTE", "Erro: " + ex.getMessage());
                        } finally {
                            Uteis.logarDebug(msg.toString());
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logarDebug("Boletos Pendentes de Cancelamento | Convenio: " + convVO.getDescricao() + " | Erro: " + ex.getMessage());
                } finally {
                    boletoDAO = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Boletos Pendentes de Cancelamento | Erro: " + ex.getMessage());
        } finally {
            Uteis.logarDebug("Boletos Pendentes de Cancelamento | Empresa: " + empresaVO.getNome() + " | Finalizado...");
        }
    }

    private void processarBoletosPendentes(EmpresaVO empresaVO, boolean adicionarAguardandoPagamento, Date dataVencimentoLimite) {
        try {
            Uteis.logarDebug("Processamento Boletos Pendentes | Empresa: " + empresaVO.getNome() + " | Iniciando...");

            List<ConvenioCobrancaVO> listaConvenios = getFacade().getConvenioCobranca().consultarPorTipoCobranca(TipoCobrancaEnum.BOLETO_ONLINE,
                    empresaVO.getCodigo(), null, null, Uteis.NIVELMONTARDADOS_TODOS);

            for (ConvenioCobrancaVO convVO : listaConvenios) {
                Boleto boletoDAO;
                try {
                    Uteis.logarDebug("## Processamento Boletos Pendentes | Sincronizar | Convenio: " + convVO.getDescricao());

                    this.verificarPodeProcessarConvenioBoleto(convVO);

                    boletoDAO = new Boleto(con);
                    List<BoletoVO> listaAguardando = boletoDAO.consultarPendentes(convVO, empresaVO, adicionarAguardandoPagamento, dataVencimentoLimite, Uteis.NIVELMONTARDADOS_DADOSBASICOS,
                            null, null);

                    Uteis.logarDebug("## Processamento Boletos Pendentes | Sincronizar | Total Aguardando: " + listaAguardando.size());

                    int i = 0;
                    for (BoletoVO boletoVO : listaAguardando) {
                        StringBuilder msg  = new StringBuilder();
                        msg.append("## Processamento Boletos Pendentes | Sincronizar | ").append(++i).append("/").append(listaAguardando.size());
                        try {
                            msg.append(" | Boleto ").append(boletoVO.getCodigo());
                            String retorno = boletoDAO.sincronizarBoleto(boletoVO, this.usuarioVO, "boletoPendente - RemessaService");
                            msg.append(" | Retorno: ").append(retorno);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            msg.append(" | Erro: ").append(ex.getMessage());
                        } finally {
                            Uteis.logarDebug(msg.toString());
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logarDebug("## Processamento Boletos Pendentes | Sincronizar | Convenio: " + convVO.getDescricao() + " | Erro: " + ex.getMessage());
                } finally {
                    boletoDAO = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Processamento Boletos Pendentes | Erro: " + ex.getMessage());
        } finally {
            Uteis.logarDebug("Processamento Boletos Pendentes | Empresa: " + empresaVO.getNome() + " | Finalizado...");
        }
    }

    public void processarBoletosOnline(Date dia, EmpresaVO empresaVO, Integer diasProcessarBoletosPagos) {
        //verificar itens que foram pagos PJBank
        processarPagamentosPJBank(dia, empresaVO, diasProcessarBoletosPagos);
        //verificar itens que foram pagos Itau
        processarPagamentosBoletoItau(empresaVO , 10);
        //verificar itens que foram pagos Asaas
        processarPagamentosAsaas(dia, empresaVO, diasProcessarBoletosPagos);
        //verificar itens que foram pagos Caixa Econômica
        processarPagamentosCaixaOnline(empresaVO);
        //verificar itens que foram pagos Banco do Brasil
        processarPagamentoAutomaticosBoletoBancoBrasil(empresaVO);
        //verificar itens pendentes de registro
        processarBoletosPendentes(empresaVO, false, null);
        //tentar cancelar boletos pendentes
        processarBoletosPendentesCancelamento(empresaVO);

    }

    public void processarPagamentosPJBank(Date dia, EmpresaVO empresaVO, Integer diasProcessarPJBankPagos) {
        try {
            Uteis.logar(null, "Processamento PJBank | Empresa: " + empresaVO.getNome() + " | Iniciando...");

            Integer[] convenioPJ = new Integer[]{TipoConvenioCobrancaEnum.BOLETO_PJBANK.getCodigo()};
            List<ConvenioCobrancaVO> listaConvenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresaVO.getCodigo(), false,
                    Uteis.NIVELMONTARDADOS_TODOS, convenioPJ, null);

            for (ConvenioCobrancaVO convVO : listaConvenios) {
                try {
                    Uteis.logar(null, "## Processamento PJBank | Pagamentos | Convenio: " + convVO.getDescricao());

                    this.verificarPodeProcessarConvenioBoleto(convVO);

                    String credencial = convVO.getCredencialPJBank();
                    String chavePJBank = convVO.getChavePJBank();

                    BoletosManager boletosManager = new BoletosManager(credencial, chavePJBank, convVO);

                    int pagina = 0;
                    int erros = 0;
                    while (true) {
                        try {
                            pagina++;

                            Uteis.logar(null, "## Processamento PJBank | Processar Pagina " + pagina);

                            String response = boletosManager.getBoletos(true, Calendario.somarDias(dia, -diasProcessarPJBankPagos), dia, pagina);
                            JSONArray array = new JSONArray(response);
                            int i = 0;
                            for (int e = 0; e < array.length(); e++) {

                                String id_unico = "";
                                String retorno = "";
                                Boleto boletoDAO;
                                try {
                                    JSONObject obj = array.getJSONObject(e);
                                    id_unico = obj.optString("id_unico");
                                    boletoDAO = new Boleto(con);
                                    retorno = boletoDAO.processarBoletoPJBank(obj.toString(), "REMESSA SERVICE");
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    retorno = ex.getMessage();
                                } finally {
                                    boletoDAO = null;
                                    Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | "+  ++i + "/" + array.length() +" | IdUnico " + id_unico + " | " + retorno);
                                }
                            }

//                            Recomendado buscar a próxima página quando o sistema retornar 50 itens na página atual.
                            if (array.length() != 50) {
                                Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | sair while | array " + array.length());
                                break;
                            }

                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | ERRO: " + ex.getMessage());
                            erros++;
                            if (erros == 3) {
                                Uteis.logar(null, "## Processamento PJBank | Pagina " + pagina + " | sair while | erros " + erros);
                                break;
                            }
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "## Processamento PJBank | Pagamentos | Convenio: " + convVO.getDescricao() + " | Erro: " + ex.getMessage());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Processamento PJBank | Erro: " + ex.getMessage());
        } finally {
            Uteis.logar(null, "Processamento PJBank | Empresa: " + empresaVO.getNome() + " | Finalizado...");
        }
    }

    public void processarPagamentosBoletoItau(EmpresaVO empresaVO, Integer diasProcessarBoletosItauPagos) {
        ConvenioCobranca convenioCobrancaDAO = null;
        Usuario usuarioDAO = null;
        Boleto boletoDAO = null;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            usuarioDAO = new Usuario(con);
            boletoDAO = new Boleto(con);

            Uteis.logar(null, "Processamento Boleto Itaú | Empresa: " + empresaVO.getNome() + " | Iniciando...");

            List<BoletoVO> listaBoletos = new ArrayList<>();

            StringBuilder sql = new StringBuilder();
            sql.append("select b.codigo \n");
            sql.append("from boleto b \n");
            sql.append("inner join conveniocobranca c on c.codigo = b.conveniocobranca \n");
            sql.append("where datapagamento is null \n");
            sql.append("and b.situacao = ").append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo()).append("\n");
            sql.append("and empresa = ? \n");
            sql.append("and c.tipoconvenio = ? \n");
            sql.append("and datavencimento BETWEEN now() - INTERVAL '" + diasProcessarBoletosItauPagos + " days' "); //vencimento para trás
            sql.append("AND now() + INTERVAL '" + diasProcessarBoletosItauPagos + " days' \n"); //vencimento para frente

            PreparedStatement ps = con.prepareStatement(sql.toString());
            ps.setInt(1, empresaVO.getCodigo());
            ps.setInt(2, TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE.getCodigo());
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                BoletoVO boletoVO = new BoletoVO();
                boletoVO = boletoDAO.consultarPorChavePrimaria(rs.getInt("codigo"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                listaBoletos.add(boletoVO);
            }

            ItauService itauService;
            for (BoletoVO boletoVO : listaBoletos) {
                try {
                    itauService = new ItauService(con, empresaVO.getCodigo(), boletoVO.getConvenioCobrancaVO().getCodigo());
                    Uteis.logar("Consultar boleto " + boletoVO.getCodigo() + " na API do Itaú...");
                    JSONObject json = itauService.consultar(boletoVO);
                    JSONObject jsonBoleto = json.getJSONArray("data").getJSONObject(0);
                    itauService.processarPagamentoBoletoItauV2Processo(boletoVO, jsonBoleto.toString(), usuarioDAO.getUsuarioRecorrencia(), "processoAutomaticoPagamentosBoletoItau");
                    Uteis.logar("Boleto " + boletoVO.getCodigo() + " processado com sucesso!");
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "## Processamento Boleto Itaú | Pagamentos | Convenio: " + boletoVO.getConvenioCobrancaVO().getDescricao() + " | Erro: " + ex.getMessage());
                } finally {
                    itauService = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Processamento Itaú | Erro: " + ex.getMessage());
        } finally {
            convenioCobrancaDAO = null;
            usuarioDAO = null;
            boletoDAO = null;
            Uteis.logar(null, "Processamento Itaú | Empresa: " + empresaVO.getNome() + " | Finalizado...");
        }
    }

    public void processarPagamentosAsaas(Date dia, EmpresaVO empresaVO, Integer diasProcessarAsaasPagos) {
        try {
            Uteis.logar(null, "Processamento Boletos Asaas | Empresa: " + empresaVO.getNome() + " | Iniciando...");

            Integer[] convenioAsaas = new Integer[]{TipoConvenioCobrancaEnum.BOLETO_ASAAS.getCodigo()};
            List<ConvenioCobrancaVO> listaConvenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresaVO.getCodigo(), false,
                    Uteis.NIVELMONTARDADOS_TODOS, convenioAsaas, null);

            String dtInicioConsultar = Calendario.getData(Uteis.getDataJDBC(Uteis.voltarDias(dia, diasProcessarAsaasPagos)), "yyyy-MM-dd");
            String dtFimConsultar = Calendario.getData(dia, "yyyy-MM-dd");

            for (ConvenioCobrancaVO convVO : listaConvenios) {
                BoletoAsaasService boletoAsaasService;
                try {
                    boletoAsaasService = new BoletoAsaasService(getCon(), empresaVO.getCodigo(), convVO.getCodigo());
                    Uteis.logar(null, "## Processamento Boletos Asaas | Pagamentos | Convenio: " + convVO.getDescricao());

                    this.verificarPodeProcessarConvenioBoleto(convVO);
                    int pagina = 0;
                    int erros = 0;
                    while (true) {
                        try {
                            pagina++;
                            Uteis.logar(null, "## Processamento Asaas | Processar Pagina " + pagina);
                            RespostaHttpDTO respostaHttpDTO = boletoAsaasService.consultarBoletosPagos(dtInicioConsultar, dtFimConsultar);
                            JSONObject json = new JSONObject(respostaHttpDTO.getResponse());
                            Uteis.logar(null, "## Processamento Asaas | Encontrei um total de " + json.optString("totalCount") + " pagamento(s) na página " + pagina);
                            JSONArray array = json.optJSONArray("data");

                            int i = 0;
                            for (int e = 0; e < array.length(); e++) {

                                String id = "";
                                String retorno = "";
                                Boleto boletoDAO;
                                try {
                                    JSONObject obj = array.getJSONObject(e);
                                    id = obj.optString("id");
                                    boletoDAO = new Boleto(con);
                                    retorno = boletoDAO.processarBoletoAsaas(obj, "REMESSA SERVICE");
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                    retorno = ex.getMessage();
                                } finally {
                                    boletoDAO = null;
                                    Uteis.logar(null, "## Processamento Asaas | Pagina " + pagina + " | "+  ++i + "/" + array.length() +" | IdUnico " + id + " | " + retorno);
                                }
                            }

//                            Recomendado buscar a próxima página quando o sistema retornar 50 itens na página atual.
                            if (array.length() != 50) {
                                Uteis.logar(null, "## Processamento Asaas | Pagina " + pagina + " | sair while | array " + array.length());
                                break;
                            }

                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logar(null, "## Processamento Asaas | Pagina " + pagina + " | ERRO: " + ex.getMessage());
                            erros++;
                            if (erros == 3) {
                                Uteis.logar(null, "## Processamento Asaas | Pagina " + pagina + " | sair while | erros " + erros);
                                break;
                            }
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "## Processamento Asaas | Pagamentos | Convenio: " + convVO.getDescricao() + " | Erro: " + ex.getMessage());
                } finally {
                    boletoAsaasService = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Processamento Asaas | Erro: " + ex.getMessage());
        } finally {
            Uteis.logar(null, "Processamento Asaas | Empresa: " + empresaVO.getNome() + " | Finalizado...");
        }
    }

    private void processarRetornoEnvioConvenioEDI(Set<Integer> convenioProcessados, EmpresaVO empresaVO) {
        try {

            if (!empresaVO.isHabilitarReenvioAutomaticoRemessa()) {
                Uteis.logar(null, "## Não está ativo a retentativa... ignorar processarRetornoEnvioConvenioEDI");
                return;
            }

            if (convenioProcessados == null) {
                Uteis.logar(null, "## Mapa convenioProcessados está NULL!");
                return;
            }

            //será consultado os convênios DCC EDI
            //Necessário fazer isso quando está habilitado o HabilitarReenvioAutomaticoRemessa pois pode acontecer do convênio não estar na lista de retentativa porem ele está ativo.
            //Se não adicionar ele, NUNCA irá fazer o processo automático de remessas seja envio ou retorno
            //AQUI NÃO SERÁ GERADO REMESSAS!! E NÃO DEVE GERAR AQUI!!
            //Isso para evitar que o sistema processe o convênio e seja gerado remessas!
            //by Luiz Felipe 09/04/2020
            List<ConvenioCobrancaVO> conveniosDCCEDI = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                    TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC, SituacaoConvenioCobranca.ATIVO);
            if (!UteisValidacao.emptyList(conveniosDCCEDI)) {
                for (ConvenioCobrancaVO conv : conveniosDCCEDI) {

                    if (convenioProcessados.contains(conv.getCodigo())) {
                        Uteis.logar(null, "## Convênio já foi processado " + conv.getDescricao());
                        continue;
                    }

                    //Caso o convênio estiver para somente extrato não deve ser processado pagamento nesse convênio.
                    //Ignorar o convênio..
                    //by Luiz Felipe 16/09/2020
                    if (conv.isSomenteExtrato()) {
                        Uteis.logar(null, "## Convênio está marcado SOMENTE EXTRATO | " + conv.getDescricao());
                        continue;
                    }

                    conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(conv.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);


                    Uteis.logar(null, "## Vou processar convênio que não estava na lista! " + conv.getDescricao());


                    VerificadorRemessaRejeitadaEDIService rejeitadaEDIService = null;
                    try {
                        Uteis.logar(null, "## Iniciando VerificadorRemessaRejeitadaEDIService " + conv.getDescricao());
                        rejeitadaEDIService = new VerificadorRemessaRejeitadaEDIService();
                        rejeitadaEDIService.verificarRemessasRejeitadas(conv, empresaVO);
                    } catch (Exception ex) {
                        Uteis.logar(null, "## Erro VerificadorRemessaRejeitadaEDIService " + conv.getDescricao() + " | " + ex.getMessage());
                    } finally {
                        Uteis.logar(null, "## Fim VerificadorRemessaRejeitadaEDIService " + conv.getDescricao());
                        rejeitadaEDIService = null;
                    }


                    //AGRUPAR REMESSAS GETNET
                    //by Luiz Felipe 31/03/2020
                    //esse processo tem q rodar AQUI ! antes de enviar as remessas e após a geração !!!
                    agruparRemessasGetnet(conv, empresaVO);


                    //enviar Remotamente
                    //processo enviar remoto
                    processoUnicoEnviarRemessas(conv.getCodigo(), empresaVO.getCodigo(), usuarioVO, true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private boolean verificarHorarioEnvio(ConvenioCobrancaVO conv) {
        if (!conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            return true;
        }

        try {
            String horariosEnvio = Uteis.getHorarioEnvioRemessaGetnet();
            if (UteisValidacao.emptyString(horariosEnvio)) {
                Uteis.logar(null, "Horarios de Envio Getnet vazio... enviar sempre!");
                return true;
            }

            Integer horarioAtual = Uteis.gethoraHH(Calendario.hoje());

            String[] listaPeriodos = horariosEnvio.split("-");

            for (String periodo : listaPeriodos) {
                if (UteisValidacao.emptyString(periodo) || !periodo.contains(":")) {
                    continue;
                }

                String[] horarios = periodo.split(":");

                Integer horaInicio = Integer.parseInt(horarios[0]);
                Integer horaFinal = Integer.parseInt(horarios[1]);

                if (horarioAtual >= horaInicio && horarioAtual <= horaFinal) {
                    return true;
                }
            }

            Uteis.logar(null, "Fora do horário de envio Getnet... Horario Atual: " + horarioAtual + " | Horarios Envio: " + horariosEnvio);
            return false;
        } catch (Exception ex) {
            ex.printStackTrace();
            return true;
        }
    }

    public String processoEnvioRemessasGestaoRemessas(ConvenioCobrancaVO convenioCobrancaVO, UsuarioVO usuarioVO) {
        if (convenioCobrancaVO.getEnviarRemessaSFTPNow()
                && (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC) ||
                (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && convenioCobrancaVO.isProcessarRemessasAutomatico()))) {
            //getnet não é para enviar quando é gerada manualmente
            //by Luiz Felipe 31/03/2020
            try {
                processoUnicoEnviarRemessas(convenioCobrancaVO.getCodigo(), convenioCobrancaVO.getEmpresa().getCodigo(), usuarioVO, false);
            } catch (Exception ex) {
                ex.printStackTrace();
                return "Erro ao enviar remessas: " + ex.getMessage();
            }
        }
        return "";
    }

    //TODO LOCAL QUE DESEJA ENVIAR REMESSA DEVE CHAMAR ESSE MÉTODO!
    //by Luiz Felipe
    public void processoUnicoEnviarRemessas(Integer convenio, Integer empresa,
                                            UsuarioVO usuarioVO, boolean processarRetornoRemoto) throws Exception {

        ResultadoServicosVO resultadoEnvioRemessa = new ResultadoServicosVO(ServicoEnum.ENVIO_REMESSA, "CONVENIO: " + convenio +  " || EMPRESA: " + empresa, empresa);
        try {
            //recebe como integer para poder consultar aqui os dados com o nível de montar dados necessário.
            //evitar problemas de dados
            ConvenioCobrancaVO conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(convenio, empresa, Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            if (configSistema.isEnviarRemessasRemotamente()
                    && conv.getSituacao() == SituacaoConvenioCobranca.ATIVO
                    && ((conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                    || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                    || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) ||
                    (conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO) && conv.getEnviarRemessaSFTPNow()))) {

                //consultar todas as remessas que estão ainda como "Gerada", pois pode haver outras remessas que o usúario gerou manualmente mas não enviou.
                List<RemessaVO> remessas = getFacade().getZWFacade().getRemessa().consultarPorSituacao(SituacaoRemessaEnum.GERADA, empresaVO.getCodigo(), conv.getCodigo());

                if (verificarHorarioEnvio(conv)) {
                    for (RemessaVO remessa : remessas) {
                        if (remessa.isCancelamento()) {
                            remessa.setListaItensCancelamento(getFacade().getZWFacade().getRemessaCancelamentoItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                            if (!remessa.getListaItensCancelamento().isEmpty()) {
                                try {
                                    LayoutRemessaBase.preencherArquivoRemessaCancelamento(remessa);
                                    Uteis.logar(null, "# Tentar enviar Remessa Cancelamento para " + conv.getTipo().getDescricao());
                                    enviarRemessaRemoto(remessa, usuarioVO);
                                    Uteis.logar(null, "# remessa Cancelamento: " + remessa + " enviada com SUCESSO!");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    String msg = "# remessa Cancelamento: " + remessa + " NÃO ENVIADA devido ao erro: " + e.getMessage();
                                    Uteis.logar(null, msg);
                                    resultadoEnvioRemessa.getResultado().put(msg);
                                }
                            } else {
                                Uteis.logar(null, "# REMESSA CANCELAMENTO NÃO GERADA! Remessa cancelamento sem parcelas elegíveis.");
                            }
                        } else {
                            Integer qtdItens = getFacade().getZWFacade().getRemessaItem().consultarQtdPorCodigoRemessa(remessa.getCodigo());
                            if (!UteisValidacao.emptyNumber(qtdItens)) {
                                try {

                                    //validar se existe uma remessa pendente de envio com sequencial anterior a remessa que está sendo enviada
                                    boolean existeRemessaAntigaPendente = validarRemessaAntigaPendenteEnvio(remessa, conv);
                                    if (existeRemessaAntigaPendente) {
                                        String msgEx = "Existe remessa antiga pendente de envio. A remessa atual não será enviada para evitar problemas de sequencial.";
                                        Uteis.logar(null, msgEx);
                                        enviarEmailBloqueioEnvioGetNet(remessa, msgEx);
                                        continue;
                                    }

                                    //validar se existe uma remessa que já foi enviada ou processa com sequencial superior a remessa que está sendo enviada
                                    boolean existeRemessaSuperioresJaEnviadasOuProcessadas = validarRemessaSuperioresJaEnviadasOuProcessadas(remessa, conv);
                                    if (existeRemessaSuperioresJaEnviadasOuProcessadas) {
                                        String msgEx = "Existe remessa superior que já foi enviada ou processadas. A remessa atual não será enviada para evitar problemas de sequencial.";
                                        Uteis.logar(null, msgEx);
                                        enviarEmailBloqueioEnvioGetNet(remessa, msgEx);
                                        continue;
                                    }

                                    remessa.setListaItens(getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                                    if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240) ||
                                            remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                                        for (RemessaItemVO ri : remessa.getListaItens()) {
                                            List<AutorizacaoCobrancaClienteVO> lista = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoaTipoConvenio(ri.getPessoa().getCodigo(), remessa.getConvenioCobranca().getTipo(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                                            if (UteisValidacao.emptyList(lista)) {
                                                ri.setAutorizacaoCobrancaVO(new AutorizacaoCobrancaClienteVO());
                                            } else {
                                                ri.setAutorizacaoCobrancaVO(lista.get(0));
                                            }
                                        }
                                    }

                                    LayoutRemessaBase.preencherArquivoRemessa(remessa);
                                    Uteis.logar(null, "# Tentar enviar Remessa para " + conv.getTipo().getDescricao());
                                    enviarRemessaRemoto(remessa, usuarioVO);
                                    Uteis.logar(null, "# remessa: " + remessa + " enviada com SUCESSO!");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    String msg = "";

                                    // Tratando estas exeções especificas pois as demais não há motivos para tentar o reenvio, "Consumo de recurso desnecessario".
                                    if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                                        Uteis.logar(null, "# Falha envio Getnet: " + e.getMessage());
                                    }

                                    if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                                            && (e.getMessage().equals("Session.connect: java.net.SocketException: Connection reset")
                                            || e.getMessage().equals("connection is closed by foreign host"))) {

                                        Uteis.logar(null, "# Falha de conexão - Forçar por mais 3 vezes o enviar Remessa para " + conv.getTipo().getDescricao());
                                        if (forcarEnvioGetnet(remessa, usuarioVO)) {
                                            msg = "# remessa: " + remessa + " NÃO ENVIADA devido ao erro: " + e.getMessage() + " Envio interrompido para que não haja rejeito de sequencial.";
                                            resultadoEnvioRemessa.getResultado().put(msg);
                                            break;
                                        }
                                    } else {
                                        msg = "# remessa: " + remessa + " NÃO ENVIADA devido ao erro: " + e.getMessage();
                                    }

                                    Uteis.logar(null, msg);
                                    resultadoEnvioRemessa.getResultado().put(msg);
                                }
                            } else {
                                Uteis.logar(null, "# REMESSA NÃO GERADA! Remessa sem parcelas elegíveis.");
                            }
                        }
                    }
                } else {
                    String msgHorario = "Fora do horário para envio!";
                    Uteis.logar(null, msgHorario);
                    resultadoEnvioRemessa.getResultado().put(msgHorario);
                }

                //baixar Arquivos de Retorno Remotos e efetuar a Baixa Automática
                if (processarRetornoRemoto && !conv.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                    realizarBaixaAutomaticaRetorno(conv);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            resultadoEnvioRemessa.getResultado().put(ex.getMessage());
        } finally {
            gravarResultadoServicos(resultadoEnvioRemessa);
        }
    }

    public void enviarEmailBloqueioEnvioGetNet(RemessaVO remessaVO, String msg) {
        try {
            UteisEmail email = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
            String assunto = "Bloqueio envio remessa GetNet";
            email.novo(assunto, config);

            String[] emailEnviar = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>"};

            String chave = DAO.resolveKeyFromConnection(getCon());

            StringBuilder emailTexto = new StringBuilder();
            emailTexto.append("Bloqueio de envio de remessa Getnet! \n\n");
            emailTexto.append("Chave: ").append(chave).append(" \n");
            emailTexto.append("Remessa de código ").append(remessaVO.getCodigo()).append(" \n");
            emailTexto.append("Mensagem: ").append(msg).append(" \n");

            email.enviarEmailN(emailEnviar, emailTexto.toString(), assunto, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro enviarEmailBloqueioEnvioGetNet: " + ex.getMessage());
        }
    }

    private boolean validarRemessaAntigaPendenteEnvio(RemessaVO remessaVO, ConvenioCobrancaVO conv) throws Exception {
        //somente validar para GetNet
        if (!conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            return false;
        }

        Remessa remessaDAO = null;
        try {
            remessaDAO = new Remessa(this.con);

            Integer sequencialRemessaEnviando = Integer.parseInt(remessaVO.getSequencialArquivoExibicao());

            List<RemessaVO> listaRemessas = remessaDAO.consultarPorSituacao(SituacaoRemessaEnum.GERADA, remessaVO.getEmpresa(), conv.getCodigo());

            for (RemessaVO obj : listaRemessas) {
                if (!obj.getCodigo().equals(remessaVO.getCodigo())) {
                    Integer sequencialRemessaPendente = Integer.parseInt(obj.getSequencialArquivoExibicao());
                    if (sequencialRemessaPendente < sequencialRemessaEnviando) {
                        return true;
                    }
                }
            }
            return false;
        } finally {
            remessaDAO = null;
        }
    }

    private boolean validarRemessaSuperioresJaEnviadasOuProcessadas(RemessaVO remessaVO, ConvenioCobrancaVO conv) throws Exception {
        //somente validar para GetNet
        if (!conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            return false;
        }

        Remessa remessaDAO = null;
        try {
            remessaDAO = new Remessa(this.con);

            Integer sequencialRemessaEnviando = Integer.parseInt(remessaVO.getSequencialArquivoExibicao());

            Integer[] situacaoFiltrar = new Integer[]{SituacaoRemessaEnum.RETORNO_PROCESSADO.getId(), SituacaoRemessaEnum.REMESSA_ENVIADA.getId()};
            List<RemessaVO> listaRemessas = remessaDAO.consultarPorSituacao(situacaoFiltrar, remessaVO.getEmpresa(), conv.getCodigo(), Uteis.somarDias(remessaVO.getDataRegistro(), -10));

            for (RemessaVO obj : listaRemessas) {
                if (!obj.getCodigo().equals(remessaVO.getCodigo())) {
                    Integer sequencialRemessaProcessada = Integer.parseInt(obj.getSequencialArquivoExibicao());
                    if (sequencialRemessaProcessada > sequencialRemessaEnviando) {
                        return true;
                    }
                }
            }

            return false;
        } finally {
            remessaDAO = null;
        }
    }

    private boolean forcarEnvioGetnet(RemessaVO remessa, UsuarioVO usuarioVO) {
        int tentar = 3;
        while (tentar > 0) {
            try {
                enviarRemessaRemoto(remessa, usuarioVO);
                tentar = 0;
                Uteis.logar(null, "# Forçar envio getnet: " + remessa + " enviada com SUCESSO!");
                return false;
            } catch (Exception e) {
                Uteis.logar(null, "# Forçar envio getnet: " + remessa + " NÃO ENVIADA devido ao erro: " + e.getMessage());
                tentar--;
            }
        }
        return true;
    }

    private String getIdentifcadorBICache(EmpresaVO empresa, Date dia){
        return String.format("%s|%s|%s",
                empresa.getCodigo(),
                Uteis.getData(dia),
                "");
    }

    private void realizarBaixaAutomaticaRetorno(ConvenioCobrancaVO conv) {
        try {
            Uteis.logar(null, "RealizarBaixaAutomaticaRetorno -> Iniciando Processamento de Retorno das Remessas de Cobrança...");
            //enviar Remotamente
            if (configSistema.isEnviarRemessasRemotamente()
                    && (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                    || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                    || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN))) {
                //baixar Arquivos de Retorno Remotos e efetuar a Baixa Automática
                if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                        || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
//                    List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory("C:\\PactoJ\\remessas\\brasilfitness");
                    List<Map<String, File>> arquivos = RetornoService.receberRetornos(conv, false, null, null);
                    if (!arquivos.isEmpty()) {
                        Uteis.logar(null, "# Vou processar retorno dos arquivos remotos do convênio: " + conv.getDescricao());
                        processarArquivosRetornoRemoto(arquivos, conv);
                        Uteis.logar(null, "# retorno dos arquivos do convênio: " + conv.getDescricao() + " processado!");
                    }
                }
            }//if enviarRemotamente?
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, "# ERRO ao processar retorno remessas: " + e.getMessage());
        } finally {
            Uteis.logar(null, "RealizarBaixaAutomaticaRetorno -> Finalizando Processamento!");
        }
    }

    private void processarApenasRetorno() {
        try {
            Uteis.logar(null, "ProcessarApenasRetorno -> Iniciando Processamento...");
            for (EmpresaVO empresaVO : listaEmpresas) {
                //Consultar os convênios DCC EDI
                List<ConvenioCobrancaVO> convenios = getFacade().getConvenioCobranca().consultarPorEmpresaESituacao(empresaVO.getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_TODOS, TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC, SituacaoConvenioCobranca.ATIVO);
                for (ConvenioCobrancaVO conv : convenios) {
                    //obter retornos Remotamente?
                    if (configSistema.isEnviarRemessasRemotamente()
                            && (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                            || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                            || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN))) {
                        //baixar Arquivos de Retorno Remotos e efetuar a Baixa Automática
                        conv = getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(conv.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                        realizarBaixaAutomaticaRetorno(conv);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, "# ERRO ao processar apenas retorno remessas: " + e.getMessage());
        } finally {
            Uteis.logar(null, "ProcessarApenasRetorno -> Finalizando Processamento!");
        }
    }

    public void processarRetorno(RemessaVO remessa, UsuarioVO usuarioRetorno) throws Exception {
        if (!remessa.getSituacaoRemessa().equals(SituacaoRemessaEnum.RETORNO_PROCESSADO)
            || remessa.getTipo().equals(TipoRemessaEnum.GET_NET)) {
            if (remessa.isCancelamento()) {
                prepararBaixarItensRemessaCancelamento(remessa, usuarioRetorno);
            } else {
                prepararBaixarItensRemessa(remessa);
            }
            remessa.getProps().put(DCCAttEnum.CodUsuarioRetorno.name(), usuarioRetorno.getCodigo().toString());
            remessa.getProps().put(DCCAttEnum.NomeUsuarioRetorno.name(), usuarioRetorno.getNome());
            remessa.getProps().put(DCCAttEnum.DataHoraRetorno.name(), Long.toString(Calendario.hoje().getTime()));
            if (!UteisValidacao.emptyString(remessa.getHeaderRetorno().get(DCCAttEnum.CodigoRetorno.name()))) {
                remessa.getProps().put(DCCAttEnum.CodigoRetorno.name(), remessa.getHeaderRetorno().getValue(DCCAttEnum.CodigoRetorno.name()));
            }
            remessa.setSituacaoRemessa(SituacaoRemessaEnum.RETORNO_PROCESSADO);
            getFacade().getZWFacade().getRemessa().alterar(remessa);
            remessa.setListaItens(getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            enviarEmailResultadoRetornoRemessa(remessa);
        }
    }

    private void prepararBaixarItensRemessa(RemessaVO remessa) throws Exception {
        remessa.setListaItens(getFacade().getZWFacade().getRemessaItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

        if (remessa.isNovoFormato()) {
            for (RemessaItemVO itemVO : remessa.getListaItens()) {
                itemVO.setMovParcelas(getFacade().getRemessaItemMovParcela().consultarPorRemessaItem(itemVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }

        LayoutRemessaBase.preencherArquivoRemessa(remessa, true);
        l.lerHeaderETrailerRemessa(remessa);
        LayoutRemessaBase.lerRetorno(remessa);
        realizarBaixaAutomatica(remessa);
    }

    private void prepararBaixarItensRemessaCancelamento(RemessaVO remessa, UsuarioVO usuarioRetorno) throws Exception {
        remessa.setListaItensCancelamento(getFacade().getZWFacade().getRemessaCancelamentoItem().consultarPorCodigoRemessa(remessa.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        LayoutRemessaBase.preencherArquivoRemessaCancelamento(remessa, true);
        l.lerHeaderETrailerRemessa(remessa);
        LayoutRemessaBase.lerRetornoCancelamento(remessa);
        realizarBaixaCancelamento(remessa, usuarioRetorno);
    }

    private void realizarBaixaCancelamento(RemessaVO remessa,UsuarioVO usuarioRetorno) throws Exception {
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        for (int i = 0; i < detailsRetorno.size(); i++) {
            RegistroRemessa regRet = detailsRetorno.get(i);
            RemessaCancelamentoItemVO itemCancelamento = null;

            if (remessa.isGetnet()) {
                itemCancelamento = (RemessaCancelamentoItemVO) obterItem(remessa, regRet, false);
            } else {
                itemCancelamento = remessa.getListaItensCancelamento().get(i);
            }

            String statusVenda = regRet.getValue(DCCAttEnum.StatusVenda.name());
            if (statusVenda != null) {
                itemCancelamento.getProps().put(DCCAttEnum.StatusVenda.name(), statusVenda);
                if(remessa.isGetnet()){
                    if (statusVenda.equals(DCOGetNetStatusEnum.Status00.getId())) {
                        cancelarPagamento(itemCancelamento, usuarioRetorno);
                    }
                }else{
                    if (statusVenda.equals(DCCCieloStatusEnum.Status00.getId())) {
                        cancelarPagamento(itemCancelamento, usuarioRetorno);
                    }
                }
            }
            getFacade().getZWFacade().getRemessaCancelamentoItem().alterar(itemCancelamento);
        }
    }

    private void cancelarPagamento(RemessaCancelamentoItemVO item,UsuarioVO usuarioRetorno) throws Exception {
        if(UteisValidacao.emptyNumber(item.getMovPagamento())){ // pagamento já foi estornado
            return;
        }
        ReciboPagamento reciboFacade = new ReciboPagamento(this.con);
        MovPagamento movPagamentoFacade = new MovPagamento(this.con);
        MovProdutoParcela movProdutoParcelaFacade = new MovProdutoParcela(this.con);
        try {
            this.con.setAutoCommit(false);
            EstornoReciboControle estorno = new EstornoReciboControle();
            EstornoReciboVO estornoVO = new EstornoReciboVO();

            List<MovPagamentoVO> listaMovPagamento = new ArrayList<MovPagamentoVO>();
            listaMovPagamento.add(movPagamentoFacade.consultarPorChavePrimaria(item.getMovPagamento(), Uteis.NIVELMONTARDADOS_TODOS));

            estornoVO.setListaMovPagamento(listaMovPagamento);

            estornoVO.setListaMovParcela(item.getListaMovParcela());

            if (item.getItemRemessaCancelar() != null) {
                for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getItemRemessaCancelar().getMovParcelas()) {
                    item.getListaMovParcela().add(itemMovParcelaVO.getMovParcelaVO());
                }
            }
            estornoVO.setReciboPagamentoVO(reciboFacade.consultarPorChavePrimaria(item.getReciboPagamento(), Uteis.NIVELMONTARDADOS_TODOS));
            estornoVO.setValidarNFSeProdutosPagos(false);
            estornoVO.setResponsavelEstornoRecivo(usuarioRetorno);
            estornoVO.setExcluirNFSe(true);
            estorno.setEstornoReciboVO(estornoVO);

            desvincularNotas(listaMovPagamento);
            desvicularPagamento(item);
            reciboFacade.estornarReciboPagamentoSemCommit(estornoVO, movPagamentoFacade, movProdutoParcelaFacade, null, null, null);
            this.con.commit();
        } catch(Exception e){
            this.con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void desvicularPagamento(RemessaCancelamentoItemVO item) throws Exception {
        RemessaItem remessaItemDAO = new RemessaItem(con);
        item.getItemRemessaCancelar().setMovPagamento(new MovPagamentoVO());
        remessaItemDAO.alterar(item.getItemRemessaCancelar());
        remessaItemDAO = null;
    }

    private void desvincularNotas(List<MovPagamentoVO> pagamentos) throws Exception {
        NFSeEmitida nfseEmitida = new NFSeEmitida(this.con);
        List<Integer> listaRPS = new ArrayList<Integer>();
        for (MovPagamentoVO pagamento : pagamentos) {
            if (pagamento.getReciboPagamento().getCodigo() != 0) {
                NFSeEmitidaVO notaEmitida = nfseEmitida.consultarPorRecibo(pagamento.getReciboPagamento().getCodigo());
                if (notaEmitida != null && !listaRPS.contains(notaEmitida.getIdRps())) {
                    listaRPS.add(notaEmitida.getIdRps());
                }

                notaEmitida = nfseEmitida.consultaPorPagamento(pagamento.getCodigo());
                if (notaEmitida != null && !listaRPS.contains(notaEmitida.getIdRps())) {
                    listaRPS.add(notaEmitida.getIdRps());
                }

                for (CartaoCreditoVO cartaoCreditoVO : pagamento.getCartaoCreditoVOs()) {
                    List<NFSeEmitidaVO> notasEmitidasCartao = nfseEmitida.consultaListaPorCartao(cartaoCreditoVO.getObterTodosCartoesComposicao());
                    for(NFSeEmitidaVO notaEmitidaCartao:  notasEmitidasCartao){
                        listaRPS.add(notaEmitidaCartao.getIdRps());
                    }
                }
            }
        }

        List<NFSeEmitidaVO> nfseEmitidas = new ArrayList<NFSeEmitidaVO>();
        for (Integer codRps : listaRPS) {
            NFSeEmitidaVO nfSeEmitidaVO = nfseEmitida.consultaPorRPS(codRps);
            if (nfSeEmitidaVO != null) {
                nfseEmitidas.add(nfSeEmitidaVO);
            }
        }
        for (NFSeEmitidaVO nfSeEmitidaVO : nfseEmitidas) {
            nfseEmitida.excluirComLogSemCommit(nfSeEmitidaVO, usuarioVO, ProcessoAjusteGeralEnum.REMESSA_CANCELAMENTO);
        }
    }


    public Set<Integer> processarRetornoFebraban(RemessaVO remessa, List<String> dadosInvalidos,
                                                 Map<Integer, RemessaItemVO> mapaItens, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {

        Set<Integer> codsRemessas = new HashSet<Integer>();
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        for (RegistroRemessa regRet : detailsRetorno) {
            if (regRet.getTipo().equals(TipoRegistroEnum.DETALHE_REGISTRO_B)) {
                try {
                    processarRegistroB(remessa, regRet, dadosInvalidos);
                } catch (Exception ex) {
                    Uteis.logar(null, "ERRO AO PROCESSAR REGISTRO B");
                }
            } else {
                RemessaItemVO item = obterItemFebraban(remessa, mapaItens, regRet, convenioCobrancaVO);
                if (item == null) {
                    continue;
                }

                RemessaVO remessaParcela = getFacade().getZWFacade().getRemessa().consultarPorChavePrimaria(item.getRemessa().getCodigo());
                codsRemessas.add(remessaParcela.getCodigo());
                item.setRemessa(remessaParcela);

                for (ObjetoGenerico obj : regRet.getAtributos()) {
                    if (obj.getAtributo().equals(DCCAttEnum.StatusVenda.name())) {
                        item.put(DCCAttEnum.StatusVenda.name(), obj.getValor());
                        if (obj.getValor().equals(DCOBBStatusEnum.Status00.getId())) {
                            String dataPagamento = regRet.getValue(DCCAttEnum.DataVencimento.name());
                            if (UteisValidacao.emptyString(dataPagamento)) {
                                dataPagamento = regRet.getValue(DCCAttEnum.DataVencimento.name());
                            }
                            if (!UteisValidacao.emptyString(dataPagamento)) {
                                Date dataOcorrencia = (Calendario.getDate("yyyyMMdd", dataPagamento));
                                incluirPagamento(item, dataOcorrencia, true);
                            } else {
                                incluirPagamento(item);
                            }
                        }
                        getFacade().getZWFacade().getRemessaItem().alterar(item);
                    }
                }
                processosPosRetornoRemessaItem(item);
            }
        }
        return codsRemessas;
    }

    public Set<Integer> processarRetornoItau(RemessaVO remessa, Map<Integer, RemessaItemVO> itens) throws Exception {
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        remessa.setListaItens(new ArrayList<RemessaItemVO>());
        Set<Integer> codsRemessas = new HashSet<Integer>();
        for (RegistroRemessa regRet : detailsRetorno) {
            RemessaItemVO item = itens.get(Integer.valueOf(regRet.getValue(DCCAttEnum.NumeroComprovanteVenda.name())));
            if (item == null) {
                continue;
            }
            RemessaVO remessaParcela = getFacade().getZWFacade().getRemessa().consultarPorChavePrimaria(item.getRemessa().getCodigo());
            codsRemessas.add(remessaParcela.getCodigo());
            item.setRemessa(remessaParcela);

            String pattern = "ddMMyyyy";
            if (remessa.getConvenioCobranca().isLayoutFebrabanDCO()) {
                pattern = "yyyyMMdd";
            }
            for (ObjetoGenerico obj : regRet.getAtributos()) {
                if (obj.getAtributo().equals(DCCAttEnum.StatusVenda.name())) {
                    String statusAtual = item.get(DCCAttEnum.StatusVenda.name());
                    if (statusAtual == null || !obj.getValor().equals(DCOItauStatusEnum.StatusPE.getId())) {
                        item.put(DCCAttEnum.StatusVenda.name(), obj.getValor());
                    }

                    boolean isPago = (remessa.getTipo().equals(TipoRemessaEnum.BB_DCO) && obj.getValor().equals(DCOBBStatusEnum.Status00.getId()))
                            || (item.getRemessa().getTipo().equals(TipoRemessaEnum.BRADESCO_DCO) && obj.getValor().equals(DCOBradescoOcorrenciaEnum.Ocor06.getId()));

                    if (isPago) {
                        String dataPagamento = regRet.getValue(DCCAttEnum.DataDeposito.name());
                        if (UteisValidacao.emptyString(dataPagamento)) {
                            dataPagamento = regRet.getValue(DCCAttEnum.DataVencimento.name());
                        }
                        if (!UteisValidacao.emptyString(dataPagamento)) {
                            Date dataOcorrencia = (Calendario.getDate(pattern, dataPagamento));
                            incluirPagamento(item, dataOcorrencia, true);
                        } else {
                            incluirPagamento(item);
                        }
                    } else {
                        String codigoErro = regRet.getValue(DCCAttEnum.Motivo.name());
                        if (!UteisValidacao.emptyString(codigoErro)) {
                            item.put(DCCAttEnum.Motivo.name(), codigoErro);
                        }
                    }
                    getFacade().getZWFacade().getRemessaItem().alterar(item);
                } else if (obj.getAtributo().equals(DCCAttEnum.CodigoAutorizacao.name())) {
                    item.put(DCCAttEnum.CodigoAutorizacao.name(), obj.getValor());
                    try {
                        if (obj.getValor() != null && !obj.getValor().isEmpty()
                                && new BigDecimal(obj.getValor().trim().replaceAll("[^0-9]", "")).longValue() > 0) {
                            String dataPagamento = regRet.getValue(DCCAttEnum.DataDeposito.name());
                            if (UteisValidacao.emptyString(dataPagamento)) {
                                dataPagamento = regRet.getValue(DCCAttEnum.DataVencimento.name());
                            }
                            if (!UteisValidacao.emptyString(dataPagamento)) {
                                Date dataOcorrencia = (Calendario.getDate(pattern, dataPagamento));
                                incluirPagamento(item, dataOcorrencia, true);
                            } else {
                                incluirPagamento(item);
                            }
                        }

                    } catch (Exception e) {
                        Uteis.logar(null, "ERRO ITEM -> " + item.getCodigo());
                        e.printStackTrace();
                        throw e;
                    }
                    getFacade().getZWFacade().getRemessaItem().alterar(item);
                }else if(obj.getAtributo().equals(DCCAttEnum.DataVencimento.name())){
                    if (!UteisValidacao.emptyString(obj.getValor())) {
                        item.put(DCCAttEnum.DataVencimento.name(), obj.getValor());
                        getFacade().getZWFacade().getRemessaItem().alterar(item);
                    }
                }

            }
            remessa.getListaItens().add(item);
        }
        enviarEmailResultadoRetornoRemessa(remessa);
        return codsRemessas;
    }

    public Set<Integer> processarRetornoBoleto(RemessaVO remessa, Map<Long, RemessaItemVO> itens, RetornoRemessaVO retornoRemessaVO, Map<Integer, EmpresaVO> mapaEmpresas, String identificadorEmpresa) throws Exception {
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        RegistroRemessa headerRetorno = remessa.getHeaderRetorno();
        String dataOcorrenciaBancoValor = headerRetorno.getValue(DCCAttEnum.DataGeracao.name());
        Date dataOcorrencia;
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())) {
            dataOcorrencia = (Calendario.getDate("yyyyMMdd", dataOcorrenciaBancoValor));
        } else {
            dataOcorrencia = (Calendario.getDate("ddMMyy", dataOcorrenciaBancoValor));
        }
        remessa.setListaItens(new ArrayList<RemessaItemVO>());
        Set<Integer> codsRemessas = new HashSet<Integer>();
        for (RegistroRemessa regRet : detailsRetorno) {
            if(!identificadorEmpresa.equals("") && !regRet.getValue(DCCAttEnum.NossoNumero.name()).startsWith(identificadorEmpresa)){
                continue;
            }

            if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())) {
                String dataOcorrenciaItemSicredi = regRet.getValue(DCCAttEnum.DataOcorrencia.name());
                if (!UteisValidacao.emptyString(dataOcorrenciaItemSicredi)) {
                    dataOcorrencia = (Calendario.getDate("ddMMyy", dataOcorrenciaItemSicredi));
                }
            }

            String valorVenda = regRet.getValue(DCCAttEnum.ValorVenda.name());
            double valorBoleto = new Double(valorVenda.substring(0, valorVenda.length() - 2) + "." + valorVenda.substring(valorVenda.length() - 2));

            String valorPagoRetorno = regRet.getValue(DCCAttEnum.ValorPago.name());
            double valorPago = new Double(valorPagoRetorno.substring(0, valorPagoRetorno.length() - 2) + "." + valorPagoRetorno.substring(valorPagoRetorno.length() - 2));

            String nossoNumeroTmp = regRet.getValue(DCCAttEnum.NossoNumero.name()).trim();
            if (UteisValidacao.emptyString(nossoNumeroTmp)) {
                retornoRemessaVO.incrementarErro();
                retornoRemessaVO.incrementarValorErro(valorBoleto);
                continue;
            }

            Long nossoNumero = 0L;
            if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())
                    || remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)) {
                nossoNumero = Long.valueOf(nossoNumeroTmp);
            } else {
                nossoNumero = Long.valueOf(nossoNumeroTmp.substring(3));
            }
            RemessaItemVO item = itens.get(nossoNumero);
            if (item == null && remessa.getConvenioCobranca().getUsarSequencialUnico()) {
                continue;
            }

            String statusVenda = regRet.getValue(DCCAttEnum.StatusVenda.name());
            if (!statusVendaValido(item, valorPago, statusVenda)) {
                retornoRemessaVO.incrementarErro();
                retornoRemessaVO.incrementarValorErro(valorBoleto);
                continue;
            }

            String valorMoraRetorno = regRet.getValue(DCCAttEnum.ValorMora.name());
            Double valorMora = null;
            if (!UteisValidacao.emptyString(valorMoraRetorno)) {
                valorMora = new Double(valorMoraRetorno.substring(0, valorMoraRetorno.length() - 2) + "." + valorMoraRetorno.substring(valorMoraRetorno.length() - 2));
            }

            if (item.getValorItemRemessa() > item.getValorBoleto()) {
                if (valorMora == null) {
                    valorMora = 0.0;
                }
                valorMora += (item.getValorItemRemessa() - item.getValorBoleto());
            }

            String tarifa = regRet.getValue(DCCAttEnum.DespesaCobranca.name());
            double valorTarifa = 0.0;
            if (!tarifa.trim().equals("")) {
                valorTarifa = new Double(tarifa.substring(0, tarifa.length() - 2) + "." + tarifa.substring(tarifa.length() - 2));
            }

            if (remessa.getConvenioCobranca().isTarifaBoletoSeparadaValorPago()) {
                valorPago = valorPago + valorTarifa;
            }

            RemessaVO remessaParcela = getFacade().getZWFacade().getRemessa().consultarPorChavePrimaria(item.getRemessa().getCodigo());
            codsRemessas.add(remessaParcela.getCodigo());
            item.setRemessa(remessaParcela);

            String codigoRetorno = remessa.getHeaderRetorno().getValue(DCCAttEnum.NumAvisoBancario.name());
            if (codigoRetorno.equals("")) {
                codigoRetorno = remessa.getHeaderRetorno().getValue(DCCAttEnum.DataGeracao.name());
            }

            try {
                if (item.getMovPagamento() != null &&
                        !UteisValidacao.emptyNumber(item.getMovPagamento().getCodigo()) &&
                        statusVenda != null && statusVenda.equalsIgnoreCase("02")) {
                    //item já foi pago e realizado a baixa.
                    continue;
                }
                if (!item.processouRetorno(codigoRetorno)) {
                    String codigoErro = "";
                    if (item.isLiquidado(statusVenda)) {
                        Date dataEntrada = dataOcorrencia;

                        String dataEntradaArquivo = regRet.getValue(DCCAttEnum.DataEntrada.name());
                        if (!UteisValidacao.emptyString(dataEntradaArquivo)) {
                            if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())){
                                dataEntrada = Calendario.getDate("yyyyMMdd", dataEntradaArquivo);
                            } else if (dataEntradaArquivo.length() == 8) {
                                dataEntrada = Calendario.getDate("ddMMyyyy", dataEntradaArquivo);
                            } else {
                                dataEntrada = Calendario.getDate("ddMMyy", dataEntradaArquivo);
                            }
                        }


                        /**
                         * Pegar a data de compensação que vem no arquivo!
                         * by Luiz Felipe 23/04/2020
                         */
                        Date dataCompensacao = dataEntrada; //Antes da alteração para pegar a data do retorno a data era a data de entrada.
                        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.ITAU.getCodigo())) {
                            try {
                                String dataPrevistaCredito = regRet.getValue(DCCAttEnum.DataPrevistaCredito.name());
                                if (!UteisValidacao.emptyString(dataPrevistaCredito)) {
                                    dataCompensacao = Calendario.getDate("ddMMyy", dataPrevistaCredito);
                                }
                            } catch (Exception ex){
                                ex.printStackTrace();
                                dataCompensacao = dataEntrada;
                            }
                        }

                        incluirPagamentoBoleto(item, valorPago, valorMora, dataEntrada, dataCompensacao, mapaEmpresas);
                        item.setCodigosRetorno(codigoRetorno + "|" + item.getCodigosRetorno());
                    } else {
                        codigoErro = regRet.getValue(DCCAttEnum.CodigoErro.name());
                    }
                    item.put(DCCAttEnum.StatusVenda.name(), statusVenda);
                    if (!UteisValidacao.emptyString(codigoErro)) {
                        item.put(DCCAttEnum.CodigoErro.name(), codigoErro);
                    }
                    getFacade().getZWFacade().getRemessaItem().alterar(item);
                }
                if (item.isLiquidado(statusVenda)) {
                    retornoRemessaVO.incrementarSucesso();
                    retornoRemessaVO.incrementarValorSucesso(valorBoleto);
                    retornoRemessaVO.incrementarValorTarifas(remessaParcela.getEmpresa(),valorTarifa);
                }
                remessa.getListaItens().add(item);
            } catch (Exception e) {
                retornoRemessaVO.incrementarErro();
                retornoRemessaVO.incrementarValorErro(valorBoleto);
                Uteis.logar(null, "ERRO ITEM -> " + item.getCodigo());
            }
        }
        lancarContaPagarDasTarifas(retornoRemessaVO);
        getFacade().getRetornoRemessa().alterar(retornoRemessaVO, false);
        for (Integer codigo : codsRemessas) {
            getFacade().getZWFacade().getRemessa().processarSituacaoRemessa(codigo);
        }
        return codsRemessas;
    }

    private boolean statusVendaValido(RemessaItemVO item, Double valorPago, String statusVenda) {
        if (item == null) {
            return false;
        }
        if (item.isLiquidado(statusVenda)) {
            return true;
        }
        return valorPago == 0.0 && item.isStatusPrevisto(statusVenda);
    }

    private void lancarContaPagarDasTarifas(RetornoRemessaVO retornoRemessaVO) {
        for(Map.Entry<Integer, Double> pairTarifa: retornoRemessaVO.getValorTarifasEmpresa().entrySet()){
            if (pairTarifa.getValue() > 0.0) {
                try {
                    MovContaVO movContaVO = new MovContaVO();
                    movContaVO.setValor(pairTarifa.getValue());
                    movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);

                    movContaVO.getEmpresaVO().setCodigo(pairTarifa.getKey());
                    movContaVO.setPessoaVO(getFacade().getPessoa().consultarPessoaEmpresaFinan(movContaVO.getEmpresaVO()));
                    movContaVO.setUsuarioVO(usuarioVO);
                    movContaVO.setDescricao("Tarifas de boletos emitidos");
                    movContaVO.setCaixa(0);
                    movContaVO.setDataLancamento(Calendario.hoje());
                    movContaVO.setDataVencimento(Calendario.hoje());
                    movContaVO.setDataCompetencia(Calendario.hoje());
                    MovContaRateioVO movContaRateio = new MovContaRateioVO();
                    movContaRateio.setValor(movContaVO.getValor());
                    movContaRateio.setTipoES(TipoES.SAIDA);
                    movContaRateio.setDescricao(movContaVO.getDescricao());
                    movContaVO.getMovContaRateios().add(movContaRateio);
                    getFacade().getFinanceiro().getMovConta().incluirSemCommit(movContaVO, 0, false, null);
                } catch (Exception ex) {
                    Uteis.logar(null, "Houve problemas ao tentar inserir MovConta das Tarifas");
                    Uteis.logar(ex, this.getClass());
                }
            }
        }
    }

    public Set<Integer> processarRetornoItauBoleto(RemessaVO remessa, Map<Integer, RemessaItemVO> itens, UsuarioVO usuarioRetorno, List<ItemRetornoRemessaTO> registrosNaoEncontrados) throws Exception {
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        remessa.setListaItens(new ArrayList<RemessaItemVO>());
        Set<Integer> codsRemessas = new HashSet<Integer>();
        for (RegistroRemessa regRet : detailsRetorno) {
            RemessaItemVO item = itens.get(Integer.valueOf(regRet.getValue(DCCAttEnum.NumDocumento.name())));
            if (item == null) {
                ItemRetornoRemessaTO itemRetornoRemessaTO = new ItemRetornoRemessaTO(regRet);
                registrosNaoEncontrados.add(itemRetornoRemessaTO);
                continue;
            }
            RemessaVO remessaParcela = getFacade().getZWFacade().getRemessa().consultarPorChavePrimaria(item.getRemessa().getCodigo());
            codsRemessas.add(remessaParcela.getCodigo());
            item.setRemessa(remessaParcela);

            for (ObjetoGenerico obj : regRet.getAtributos()) {
                if (obj.getAtributo().equals(DCCAttEnum.StatusVenda.name())) {
                    item.put(DCCAttEnum.StatusVenda.name(), obj.getValor());
                    if (obj.getValor().equals("06")) {
                        incluirPagamento(item);
                    }
                    getFacade().getZWFacade().getRemessaItem().alterar(item);
                }

            }
            remessa.getListaItens().add(item);
        }
        enviarEmailResultadoRetornoRemessa(remessa);
        return codsRemessas;
    }

    private void enviarEmailResultadoRetornoRemessa(RemessaVO remessaVO) throws Exception {
        EmailNotificacao emailService = new EmailNotificacao();
        emailService.enviarEmailRetorno(remessaVO);
    }

    public OperadoraCartaoVO obterOperadora(RemessaItemVO item) {
        try {
            String nomeBandeira = item.get(APF.Bandeira);
            if (!nomeBandeira.isEmpty()) {
                OperadorasExternasAprovaFacilEnum oper = OperadorasExternasAprovaFacilEnum.valueOf(nomeBandeira);
                return obterOperadoraPorEnum(item.getRemessa().getConvenioCobranca().getTipo(), oper);
            } else {
                return null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    private OperadoraCartaoVO obterOperadoraPorEnum(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, OperadorasExternasAprovaFacilEnum oper) throws Exception {
        OperadoraCartao operadoraCartaoDAO;
        try {
            operadoraCartaoDAO = new OperadoraCartao(this.con);

            OperadoraCartaoVO operadoraCartaoVO = operadoraCartaoDAO.consultarPorCodigoIntegracaoAPF(oper, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, true);
            if (operadoraCartaoVO != null) {
                return operadoraCartaoVO;
            }

            if (tipoConvenioCobrancaEnum != null) {
                //procurar por operadora online
                if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC)) {
                    operadoraCartaoVO = operadoraCartaoDAO.consultarPorCodigoIntegracaoCielo(oper, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, true);
                } else if (tipoConvenioCobrancaEnum.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                    operadoraCartaoVO = operadoraCartaoDAO.consultarPorCodigoIntegracaoGetNet(oper, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, true);
                }
            }
            if (operadoraCartaoVO != null) {
                return operadoraCartaoVO;
            }

            List<OperadoraCartaoVO> lista = operadoraCartaoDAO.consultarPorCodigoIntegracaoAPF(oper.getId(), true, true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            if (!UteisValidacao.emptyList(lista)) {
                return lista.get(0);
            }

            try {
                return operadoraCartaoDAO.consultarOuCriaPorCodigoIntegracao(tipoConvenioCobrancaEnum, 1, oper);
            } catch (Exception ex) {
                ex.printStackTrace();
                return null;
            }
        } finally {
            operadoraCartaoDAO = null;
        }
    }

    public void incluirPagamento(RemessaItemVO item) throws Exception {
        incluirPagamento(item, item.getRemessa().getDataPrevistaCredito(), false);
    }

    public void incluirPagamento(RemessaItemVO item, Date dataOcorrencia, boolean alterarDataRecibo) throws Exception {
        if (item.getRemessa().isNovoFormato()) {
            incluirPagamentoAgrupado(item, dataOcorrencia, alterarDataRecibo, false);
        } else {
            incluirPagamentoAntigo(item, dataOcorrencia, alterarDataRecibo);
        }
    }

    public void incluirPagamentoAgrupado(RemessaItemVO item, Date dataOcorrencia, boolean alterarDataRecibo, boolean reciboItemRemessa) throws Exception {

        Uteis.logar(null, "### VOU INCLUIR PAGAMENTO REMESSA ITEM " + item.getCodigo());

        //consultar os RemessaItemMovparcela com dos dados das parcelas
        RemessaItemMovParcela remessaItemMovParcelaDAO = new RemessaItemMovParcela(this.con);
        item.setMovParcelas(remessaItemMovParcelaDAO.consultarPorRemessaItem(item.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        remessaItemMovParcelaDAO = null;

        //obter pessoa
        item.setPessoa(obterPessoaRemessaItem(item));

        List<MovParcelaVO> listaMovParcelas = new ArrayList<>();
        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();

        if (reciboItemRemessa) {
            Cliente clienteDAO = new Cliente(this.con);
            Colaborador colaboradorDAO = new Colaborador(this.con);
            MovimentoContaCorrenteCliente movimentoDAO = new MovimentoContaCorrenteCliente(this.con);
            MovParcela movParcelaDAO = new MovParcela(this.con);

            for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getMovParcelas()) {
                if (itemMovParcelaVO.getMovParcelaVO() != null &&
                        !UteisValidacao.emptyNumber(itemMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                        !itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                    Uteis.logar(null, "RemessaItem " + item.getCodigo() + " - Parcela " + itemMovParcelaVO.getMovParcelaVO().getCodigo() + " - Situação " + itemMovParcelaVO.getMovParcelaVO().getSituacao());

                    StringBuilder sqlExiste = new StringBuilder();
                    sqlExiste.append("select \n");
                    sqlExiste.append("mp.codigo \n");
                    sqlExiste.append("from movpagamento mp \n");
                    sqlExiste.append("inner join pagamentomovparcela pag on pag.movpagamento = mp.codigo \n");
                    sqlExiste.append("where mp.observacao ilike '%gerado crédito pois a parcela ").append(itemMovParcelaVO.getMovParcelaVO().getCodigo()).append(" - %' \n");
                    sqlExiste.append("limit 1 \n");
                    boolean existe = SuperFacadeJDBC.existe(sqlExiste.toString(), con);

                    if (existe) {
                        throw new Exception("Já existe um crédito em conta corrente referente à parcela " + itemMovParcelaVO.getMovParcelaVO().getCodigo());
                    }

                    EmpresaVO empresaVO;
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(itemMovParcelaVO.getMovParcelaVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ColaboradorVO colaboradorVO = null;
                    if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        clienteVO = null;
                        colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(itemMovParcelaVO.getMovParcelaVO().getPessoa().getCodigo(), itemMovParcelaVO.getMovParcelaVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        empresaVO = colaboradorVO.getEmpresa();
                    } else {
                        empresaVO = clienteVO.getEmpresa();
                    }

                    String descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_PARCELA_NAO_ABERTO;
                    VendaAvulsaVO vendaAvulsaVO = movimentoDAO.gerarProdutoPagamentoCredito(itemMovParcelaVO.getMovParcelaVO().getValorParcela(), clienteVO, colaboradorVO, null, usuarioVO, descLancCC, dataOcorrencia, dataOcorrencia, empresaVO);

                    MovParcelaVO movParcela = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    movPagamentoVO.setObservacao("Foi gerado crédito pois a parcela " + itemMovParcelaVO.getMovParcelaVO().getCodigo() + " - " + itemMovParcelaVO.getMovParcelaVO().getDescricao() + " estava com a situação: " + itemMovParcelaVO.getMovParcelaVO().getSituacao_Apresentar());
                    listaMovParcelas.add(movParcela);
                } else {
                    listaMovParcelas.add(itemMovParcelaVO.getMovParcelaVO());
                }
            }
        } else {
            //verificar se todas as parcelas já estão pagas
            boolean todasParcelasEmAberto = true;
            for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getMovParcelas()) {
                if (itemMovParcelaVO.getMovParcelaVO() != null &&
                        !UteisValidacao.emptyNumber(itemMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                        !itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                    Uteis.logar(null, "RemessaItem " + item.getCodigo() + " - Parcela " + itemMovParcelaVO.getMovParcelaVO().getCodigo() + " - Situação " + itemMovParcelaVO.getMovParcelaVO().getSituacao());
                    todasParcelasEmAberto = false;
                    break;
                }
            }
            if (!todasParcelasEmAberto) {
                Uteis.logar(null, "RemessaItem " + item.getCodigo() + " - Existem parcelas que não estão em aberto!");
                return;
            }

            boolean estornado = false;
            for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getMovParcelas()) {
                if (UteisValidacao.emptyNumber(itemMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                        !UteisValidacao.emptyString(itemMovParcelaVO.getJsonEstorno())) {
                    Uteis.logar(null, "RemessaItemMovParcela " + itemMovParcelaVO.getCodigo() + " - Parcela foi estornada!");
                    estornado = true;
                    continue;
                }
                if (!UteisValidacao.emptyNumber(itemMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                        !itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                    throw new Exception("Parcela " + itemMovParcelaVO.getMovParcelaVO().getCodigo() + " não está em aberto!");
                }
                listaMovParcelas.add(itemMovParcelaVO.getMovParcelaVO());
            }

            if (estornado &&
                    UteisValidacao.emptyList(listaMovParcelas)) {
                Uteis.logar(null, "RemessaItem " + item.getCodigo() + " - Foi estornado o pagamento!");
                return;
            }
        }


        if (UteisValidacao.emptyList(listaMovParcelas)) {
            throw new Exception("Nenhuma parcela encontrada para realizar a baixa. RemessaItem " + item.getCodigo());
        }

        movPagamentoVO.setDataPrevistaDeposito(dataOcorrencia);

        boolean temFormaPagamento = false;
        for (FormaPagamentoVO form : getListaFormasPagamentoComConvenio()) {
            if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(item.getRemessa().getConvenioCobranca().getCodigo())) {
                movPagamentoVO.setFormaPagamento(form);
                temFormaPagamento = true;
                break;
            }
        }

        if (!temFormaPagamento) {
            movPagamentoVO.setFormaPagamento(
                    item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC) || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                            || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)
                            ? formaPagamentoCartaoCredito : formaPagamentoDebitoEmConta);
        }

        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(item.getValorItemRemessa());
        movPagamentoVO.setValorTotal(item.getValorItemRemessa());
        movPagamentoVO.setPessoa(item.getPessoa());
        movPagamentoVO.setNomePagador(item.getPessoa().getNome());
        movPagamentoVO.setResponsavelPagamento(usuarioVO);
        movPagamentoVO.setEmpresa(listaMovParcelas.get(0).getEmpresa());
        movPagamentoVO.setConvenio(item.getRemessa().getConvenioCobranca());
        movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
        movPagamentoVO.setAutorizacaoCartao(item.get(DCCAttEnum.CodigoAutorizacao.name()));

        Integer nrParcelaCartaoCredito = listaMovParcelas.get(0).getNumeroParcelasOperadora();
        if (UteisValidacao.emptyNumber(nrParcelaCartaoCredito)) {
            movPagamentoVO.setNrParcelaCartaoCredito(1);
        } else {
            movPagamentoVO.setNrParcelaCartaoCredito(nrParcelaCartaoCredito);
        }

        OperadoraCartaoVO oper = obterOperadora(item);
        if (oper != null) {
            movPagamentoVO.setOperadoraCartaoVO(oper);
        }

        movPagamentoVO.setAdquirenteVO(incluirAdquirenteMovPagamento(item));

        if (alterarDataRecibo) {
            movPagamentoVO.setDataLancamento(dataOcorrencia);
            movPagamentoVO.setDataQuitacao(dataOcorrencia);
            movPagamentoVO.setDataPagamento(dataOcorrencia);
        }

        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = listaMovParcelas.get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : listaMovParcelas) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                contrato = 0;
                break;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);

        // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
        // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
        // Por isso a inclusão desses logs
        if (!UteisValidacao.emptyList(item.getMovParcelas())) {
            String codigosMovParcelas = item.getMovParcelas().stream()
                    .map(p -> String.valueOf(p.getCodigo()))
                    .collect(Collectors.joining(","));
            Uteis.logarDebug("RemessaService - incluirPagamentoAgrupado - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
        }

        for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getMovParcelas()) {
            //CRIAR PARCELA MULTA E JUROS
            if (!UteisValidacao.emptyNumber(itemMovParcelaVO.getValorMulta()) || !UteisValidacao.emptyNumber(itemMovParcelaVO.getValorJuros())) {
                MovParcela movParcelaDAO = new MovParcela(this.con);
                MovParcelaVO parcelaMultaJuros = movParcelaDAO.criarParcelaMultaJuros(itemMovParcelaVO.getMovParcelaVO(), itemMovParcelaVO.getValorMulta(), itemMovParcelaVO.getValorJuros(), usuarioVO);
                movParcelaDAO = null;

                RemessaItemMovParcelaVO novo = new RemessaItemMovParcelaVO();
                novo.setRemessaItemVO(item);
                novo.setMovParcelaVO(parcelaMultaJuros);

                RemessaItemMovParcela itemDAO = new RemessaItemMovParcela(this.con);
                itemDAO.incluirSemCommit(novo);
                itemDAO = null;

                listaMovParcelas.add(parcelaMultaJuros);
            }
        }

        //dotz
        prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, listaMovParcelas);

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        listaPagamento.add(movPagamentoVO);

        MovPagamentoVO.validarDados(movPagamentoVO);

        MovPagamento movPagamentoDAO = new MovPagamento(this.con);
        ReciboPagamentoVO reciboObj = movPagamentoDAO.incluirListaPagamento(listaPagamento, listaMovParcelas, null, contratoVO, false, 0.0);
        movPagamentoDAO = null;
        if (alterarDataRecibo) {
            reciboObj.setData(dataOcorrencia);
            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(this.con);
            reciboPagamentoDAO.alterar(reciboObj);
            reciboPagamentoDAO = null;
        }

        emitirEcfNfse(reciboObj);

        item.setReciboPagamentoVO(reciboObj);
        item.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0));

        notificarWebhook(reciboObj);
    }

    private void notificarWebhook(ReciboPagamentoVO reciboPagamentoVO) {
        try {
            if (reciboPagamentoVO.getEmpresa().isNotificarWebhook()) {
                Cliente clienteDAO = new Cliente(this.getCon());
                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboPagamentoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                clienteDAO = null;
                ZillyonWebFacade zwFacade = new ZillyonWebFacade(this.getCon());
                zwFacade.notificarPagamento(clienteVO, reciboPagamentoVO);
                zwFacade = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private PessoaVO obterPessoaRemessaItem(RemessaItemVO remessaItemVO) throws Exception {
        String sql = "select pessoa from remessaitem where codigo = " + remessaItemVO.getCodigo();
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        Integer pessoa = 0;
        if (rs.next()) {
            pessoa = rs.getInt("pessoa");
        }

        if (UteisValidacao.emptyNumber(pessoa)) {
            throw new Exception("Pessoa do remessa item " + remessaItemVO.getCodigo() + " não encontrado");
        }

        Pessoa pessoaDAO = new Pessoa(con);
        PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        pessoaDAO = null;
        return pessoaVO;
    }

    public void incluirPagamentoAntigo(RemessaItemVO item, Date dataOcorrencia, boolean alterarDataRecibo) throws Exception {
        if (item.getMovParcela().getSituacao().equals("EA")) {

            Uteis.logar(null, "### VOU INCLUIR PAGAMENTO PARCELA " + item.getMovParcela().getCodigo());

            List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();

            if (item.getMovParcela().getPessoa().getCodigo() != 0) {
                item.getMovParcela().setPessoa(getFacade().getPessoa().
                        consultarPorChavePrimaria(item.getMovParcela().
                        getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setDataPrevistaDeposito(dataOcorrencia);

            boolean temFormaPagamento = false;
            for (FormaPagamentoVO form : getListaFormasPagamentoComConvenio()) {
                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(item.getRemessa().getConvenioCobranca().getCodigo())) {
                    movPagamentoVO.setFormaPagamento(form);
                    temFormaPagamento = true;
                    break;
                }
            }

            if (!temFormaPagamento) {
                movPagamentoVO.setFormaPagamento(
                        item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC) || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                        || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)
                        ? formaPagamentoCartaoCredito : formaPagamentoDebitoEmConta);
            }

            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setValor(item.getValorItemRemessa());
            movPagamentoVO.setValorTotal(item.getValorItemRemessa());
            movPagamentoVO.setPessoa(item.getMovParcela().getContrato().getPessoa());
            movPagamentoVO.setNomePagador(item.getMovParcela().getPessoa().getNome());
            if (item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC)
                    || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                    || item.getRemessa().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                movPagamentoVO.setConvenio(item.getRemessa().getConvenioCobranca());
                movPagamentoVO.setOpcaoPagamentoCartaoCredito(true);
                if(item.getMovParcela().getNumeroParcelasOperadora() == null
                        || item.getMovParcela().getNumeroParcelasOperadora() == 0){
                    movPagamentoVO.setNrParcelaCartaoCredito(1);
                }else{
                    movPagamentoVO.setNrParcelaCartaoCredito(item.getMovParcela().getNumeroParcelasOperadora());
                }
                OperadoraCartaoVO oper = obterOperadora(item);
                if (oper != null) {
                    movPagamentoVO.setOperadoraCartaoVO(oper);
                }
                movPagamentoVO.setAutorizacaoCartao(item.get(DCCAttEnum.CodigoAutorizacao.name()));
                movPagamentoVO.setAdquirenteVO(incluirAdquirenteMovPagamento(item));
            } else {
                movPagamentoVO.setOpcaoPagamentoDinheiro(true);
            }

            movPagamentoVO.setResponsavelPagamento(usuarioVO);
            movPagamentoVO.setPessoa(item.getMovParcela().getPessoa());
            movPagamentoVO.setEmpresa(item.getMovParcela().getEmpresa());
            if (alterarDataRecibo) {
                movPagamentoVO.setDataLancamento(dataOcorrencia);
                movPagamentoVO.setDataQuitacao(dataOcorrencia);
                movPagamentoVO.setDataPagamento(dataOcorrencia);
            }
            prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, item.getMovParcela());
            listaPagamento.add(movPagamentoVO);

            List<MovParcelaVO> parcelas = new ArrayList<MovParcelaVO>();
            parcelas.add(item.getMovParcela());


            //CRIAR PARCELA MULTA E JUROS
            if (!UteisValidacao.emptyNumber(item.getValorMulta()) || !UteisValidacao.emptyNumber(item.getValorJuros())) {
                parcelas.add(getFacade().getMovParcela().criarParcelaMultaJuros(item.getMovParcela(), item.getValorMulta(), item.getValorJuros(), usuarioVO));
                try {
                    getFacade().getRemessaItemMovParcela().alterarRemessaItemMovParcela(item, parcelas, parcelas);
                } catch (Exception ignored) {
                }
            }

            MovPagamentoVO.validarDados(movPagamentoVO);

            ReciboPagamentoVO reciboObj = getFacade().getMovPagamento().incluirListaPagamento(
                    listaPagamento,
                    parcelas,
                    null,
                    item.getMovParcela().getContrato(),
                    false, 0.0);
            if (alterarDataRecibo) {
                reciboObj.setData(dataOcorrencia);
                getFacade().getReciboPagamento().alterar(reciboObj);
            }

            emitirEcfNfse(reciboObj);

            item.setReciboPagamentoVO(reciboObj);
            item.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0));

            notificarWebhook(reciboObj);
        }else{
            if(item.getMovParcela().getSituacao().equals("CA")){
                getFacade().getRemessaItem().alterarProps(item.getCodigo(), "remessaCancelada=true");
                item.getProps().put("remessaCancelada", "true");
            }
        }
    }

    private AdquirenteVO incluirAdquirenteMovPagamento(RemessaItemVO obj) {
        if (obj != null) {
            Adquirente adquirenteDAO = null;
            try {
                 adquirenteDAO = new Adquirente(con);
                 return adquirenteDAO.obterAdquirenteRemessaItem(obj.getRemessa());
            } catch (Exception e) {
                e.getStackTrace();
            } finally {
                adquirenteDAO = null;
            }
        }
        return new AdquirenteVO();
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, List<MovParcelaVO> listaMovParcelas) {
        try {
            for (MovParcelaVO movParcelaVO : listaMovParcelas) {
                prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
            }
        } catch (Exception ex) {
            Uteis.logar(null, "prepararMovPagamentoAcumuloAutomatico ERRO: " + ex.getMessage());
        }
    }

    private void prepararMovPagamentoAcumuloAutomatico(MovPagamentoVO movPagamentoVO, MovParcelaVO movParcelaVO) {
        try {
            ParceiroFidelidadeZW parceiroFidelidadeZW = new ParceiroFidelidadeZW(con);
            parceiroFidelidadeZW.prepararMovPagamentoAcumuloAutomatico(movPagamentoVO, movParcelaVO);
        } catch (Exception ex) {
            System.out.println("prepararMovPagamentoAcumuloAutomaticoRemessaService ERRO: " + ex.getMessage());
        }
    }

    /**
     * Popula os campos {@link negocio.comuns.contrato.MovProdutoVO}
     * @param movParcelasPagar
     */
    private void popularMovProdutos(List<RemessaItemMovParcelaVO> movParcelasPagar) throws  Exception{
        for(RemessaItemMovParcelaVO item : movParcelasPagar){
            item.getMovParcelaVO().setMovProdutoParcelaVOs(getFacade().getMovProdutoParcela().consultarMovProdutoParcelasPorParcelas(item.getMovParcelaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        }
    }

    /**
     * Seta a situacao da {@link MovParcelaVO} para 'EA' e
     * seta a situação dos {@link MovProdutoVO} vinculados a ela como 'EA'
     * @param movParcelaVO
     * @return
     */
    private MovParcelaVO reabrirMovParcela(MovParcelaVO movParcelaVO) throws  Exception{
        movParcelaVO.setSituacao("EA");
        getFacade().getMovParcela().alterarSomenteSituacaoSemCommit(movParcelaVO);
        for(MovProdutoParcelaVO mov : movParcelaVO.getMovProdutoParcelaVOs()){
            mov.getMovProdutoVO().setSituacao("EA");
            getFacade().getMovProduto().alterarSomenteSituacaoSemCommit(mov.getMovProdutoVO().getCodigo(), "EA");
        }
        return movParcelaVO;
    }

    /**
     * Realiza a ordenação das parcelas da forma que elas serão pagas.
     * @param parcelas parcelas que serão ordenadas.
     * @throws Exception
     */
    private void ordenarParcelasParaPagamento(List<MovParcelaVO> parcelas) throws  Exception{
        //Ordenar parcelas em aberto, do boleto, de forma decrescente de valor
        Collections.sort(parcelas, new OrdenacaoMovParcelaPagamento());
    }

    private void incluirPagamentoBoleto(RemessaItemVO item, Double valorPago, Double valorMora,
                                        Date dataOcorrencia, Date dataCompensacao, Map<Integer, EmpresaVO> mapaEmpresas) throws Exception {
        if (dataCompensacao == null) {
            dataCompensacao = dataOcorrencia;
        }

        List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
        List<MovParcelaVO> parcelasEmAberto = new ArrayList<MovParcelaVO>();

        //Boleto Caixa montar o objeto parcela consultando no banco toda vez para saber se já foi paga ou não,
        // para possível lançamento de crédito caso cliente pague 2 vezes mesmo boleto
        try {
            if (item.getRemessa().getConvenioCobranca().getBanco().getCodigoBanco().equals(104)) {
                item.setMovParcelas(getFacade().getRemessaItemMovParcela().consultarPorRemessaItem(item.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        } catch (Exception ignore) {
        }

        //Avaliar se alguma parcela do boleto não está "Em Aberto"
        for (RemessaItemMovParcelaVO itemMovParcelaVO : item.getMovParcelas()) {
            if (itemMovParcelaVO.getMovParcelaVO().getSituacao().equals("EA")) {
                parcelasEmAberto.add(itemMovParcelaVO.getMovParcelaVO());
            }
        }

        //Criar Multa e Juros para as parcelas em Aberto
        if ((Calendario.maior(dataOcorrencia, item.getDataVencimentoBoleto()) && getFacade().getMovParcela().deveGerarMulta(dataOcorrencia, item.getDataVencimentoBoleto(),mapaEmpresas.get(item.getRemessa().getEmpresa())))
                || (valorPago > item.getValorBoleto())) {
            carregarMovProdutos(parcelasEmAberto);
            double multiplicadorMultaJuros = 1.0;
            if (item.getValorCredito() > 0) {
                multiplicadorMultaJuros = Uteis.arredondarForcando2CasasDecimais(1 - (item.getValorCredito() / item.getValorBoleto()));
            }

            try {
                descobrirMultaEJurosDoMomentoDaGeracaoDaRemessa(item, mapaEmpresas);
            } catch (Exception ex) {
                Uteis.logarDebug("Erro no método descobrirMultaEJurosDoMomentoDaGeracaoDaRemessa: " + ex.getMessage());
                ex.printStackTrace();
            }

            getFacade().getMovParcela().montarMultaJurosParcelaVencida(mapaEmpresas.get(item.getRemessa().getEmpresa()), parcelasEmAberto, dataOcorrencia, true, multiplicadorMultaJuros, valorMora);

            // Estamos tendo problemas onde cria as parcelas de Multa e Juros e as mesmas não são apagadas em caso de erro.
            // Mas não temos como saber a origem, pois a criação vem de vários locais diferentes.
            // Por isso a inclusão desses logs
            if (!UteisValidacao.emptyList(parcelasEmAberto)) {
                String codigosMovParcelas = parcelasEmAberto.stream()
                        .map(p -> String.valueOf(p.getCodigo()))
                        .collect(Collectors.joining(","));
                Uteis.logarDebug("RemessaService - incluirPagamentoBoleto - Identificar multa e juros criada e nao excluida apos pagamento. - Parcelas: " + codigosMovParcelas);
            }

            List<MovParcelaVO> multasEJuros = getFacade().getMovParcela().criarParcelaMultaJuros(parcelasEmAberto, mapaEmpresas.get(item.getRemessa().getEmpresa()), usuarioVO, dataOcorrencia, multiplicadorMultaJuros, valorMora, true);
            for (MovParcelaVO movParcelaMJ : multasEJuros) {
                if (movParcelaMJ.getSituacao().equals("EA")) {
                    parcelasEmAberto.add(movParcelaMJ);
                }
            }
        } else {
            if (deveGerarDesconto(item, dataOcorrencia, valorPago, mapaEmpresas.get(item.getRemessa().getEmpresa()))) {
                gerarDescontoRemessa(item, parcelasEmAberto);
            }
        }


        ordenarParcelasParaPagamento(parcelasEmAberto);

        //Preparando o uso de CC;
        MovimentoContaCorrenteClienteVO ultimoMovimento = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(item.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        MovimentoContaCorrenteClienteVO movimento = new MovimentoContaCorrenteClienteVO();

        List<MovParcelaVO> parcelasPagar = new ArrayList<MovParcelaVO>();
        double valorCreditoEmConta = (ultimoMovimento != null && ultimoMovimento.getSaldoAtual() > 0) ? ultimoMovimento.getSaldoAtual() : 0.0;
        double valorPagoRestante = valorPago + valorCreditoEmConta;
        double valorUtilizado = 0.0;
        for (MovParcelaVO movParcelaVO : parcelasEmAberto) {
            if (valorPagoRestante >= movParcelaVO.getValorParcela()) {
                parcelasPagar.add(movParcelaVO);
                valorPagoRestante -= Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
                valorUtilizado += Uteis.arredondarForcando2CasasDecimais(movParcelaVO.getValorParcela());
                valorPagoRestante = Uteis.arredondarForcando2CasasDecimais(valorPagoRestante);
            }
        }

        //Gerar MovPagamento padrão
        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
        movPagamentoVO.setDataPrevistaDeposito(dataCompensacao);
        boolean temFormaPagamento = false;
        for (FormaPagamentoVO form : getListaFormasPagamentoComConvenio()) {
            if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(item.getRemessa().getConvenioCobranca().getCodigo())) {
                movPagamentoVO.setFormaPagamento(form);
                temFormaPagamento = true;
                break;
            }
        }
        if (!temFormaPagamento) {
            movPagamentoVO.setFormaPagamento(formaPagamentoBoletoBancario);
        }
        movPagamentoVO.setMovPagamentoEscolhida(true);
        movPagamentoVO.setValor(valorPago);
        movPagamentoVO.setValorTotal(valorPago);
        movPagamentoVO.setOpcaoPagamentoBoleto(true);
        movPagamentoVO.setConvenio(item.getRemessa().getConvenioCobranca());
        movPagamentoVO.setPessoa(item.getPessoa());
        movPagamentoVO.setNomePagador(item.getPessoa().getNome());
        movPagamentoVO.setResponsavelPagamento(usuarioVO);
        movPagamentoVO.getEmpresa().setCodigo(item.getRemessa().getEmpresa());
        movPagamentoVO.setCredito(false);
        movPagamentoVO.setDataLancamento(dataOcorrencia);
        movPagamentoVO.setDataQuitacao(dataCompensacao);
        movPagamentoVO.setDataPagamento(dataCompensacao);

        listaPagamento.add(movPagamentoVO);

        if (valorUtilizado > valorPago && ultimoMovimento != null) {
            double valorUsadoCC = Uteis.arredondarForcando2CasasDecimais(valorUtilizado - valorPago);

            movimento.setSaldoAnterior(ultimoMovimento.getSaldoAtual());
            movimento.setTipoMovimentacao("DE");
            movimento.setDescricao("Pagar Parcelas");
            movimento.setValor(valorUsadoCC);
            movimento.setSaldoAtual(Uteis.arredondarForcando2CasasDecimais(ultimoMovimento.getSaldoAtual() - valorUsadoCC));
            movimento.setResponsavelAutorizacao(usuarioVO);
            movimento.setPessoa(item.getPessoa());

            MovPagamentoVO movPagamentoCC = new MovPagamentoVO();
            movPagamentoCC.setFormaPagamento(getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            movPagamentoCC.setMovPagamentoEscolhida(true);
            movPagamentoCC.setValor(movimento.getValor());
            movPagamentoCC.setValorTotal(movimento.getValor());
            movPagamentoCC.setConvenio(item.getRemessa().getConvenioCobranca());

            movPagamentoCC.setPessoa(item.getPessoa());
            movPagamentoCC.setNomePagador(item.getPessoa().getNome());
            movPagamentoCC.setResponsavelPagamento(usuarioVO);
            movPagamentoCC.getEmpresa().setCodigo(item.getRemessa().getEmpresa());
            movPagamentoCC.setCredito(false);

            movPagamentoCC.setDataLancamento(dataOcorrencia);
            movPagamentoCC.setDataQuitacao(dataOcorrencia);
            movPagamentoCC.setDataPagamento(dataOcorrencia);
            listaPagamento.add(movPagamentoCC);
        }

        if (Uteis.arredondarForcando2CasasDecimaisMantendoSinal(valorPago - valorUtilizado) > 0) {
            double valorCredito = Uteis.arredondarForcando2CasasDecimais(valorPago - valorUtilizado);
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(
                    movPagamentoVO.getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            String descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO_EXCEDENTE;
            VendaAvulsaVO vendaAvulsaVO =  getFacade().getMovimentoContaCorrenteCliente().gerarProdutoPagamentoCredito(
                    valorCredito, clienteVO, null, null, usuarioVO, descLancCC, dataOcorrencia, dataOcorrencia, clienteVO.getEmpresa());

            MovParcelaVO movParcela = getFacade().getMovParcela().consultarPorCodigoVendaAvulsa(
                    vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
            movParcela.setSituacao("EA");
            parcelasPagar.add(movParcela);
        }
        ReciboPagamentoVO reciboObj = getFacade().getMovPagamento().incluirListaPagamento(
                listaPagamento, parcelasPagar, movimento,
                item.getMovParcela().getContrato(), false, 0.0);
        //Ajustar data do recibo
        reciboObj.setData(dataOcorrencia);
        getFacade().getReciboPagamento().alterar(reciboObj);

        //Avaliar o crédito na conta, para quitar as parcelas da mesma competência
        processarContaCorrente(item.getRemessa().getConvenioCobranca(),
                item.getRemessa().getEmpresa(), item.getPessoa(), item.getMovParcela().getContrato(),
                Uteis.obterPrimeiroDiaMes(item.getDataVencimentoBoleto()),
                Uteis.obterUltimoDiaMes(item.getDataVencimentoBoleto()), dataOcorrencia);

        emitirEcfNfse(reciboObj);
        if(!UtilReflection.objetoMaiorQueZero(item.getMovPagamento(), "getCodigo()")){
            item.setMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0));
        }

        notificarWebhook(reciboObj);
    }

    private static void descobrirMultaEJurosDoMomentoDaGeracaoDaRemessa(RemessaItemVO item, Map<Integer, EmpresaVO> mapaEmpresas) throws Exception {
        //Utilizado para saber como estava a configuração de multa e juros do momento de geração da remessa, pois caso o cliente gere uma remessa
        // com a configuração X e depois da remessa gerada e enviada ele muda as configurações de multa e juros, então o sistema criava as parcelas de
        // multa e juros incorretamente quando processava o retorno do banco.
        EmpresaVO empresaAtual = (EmpresaVO) mapaEmpresas.getOrDefault(item.getRemessa().getEmpresa(), null).getClone(true);
        if (empresaAtual != null && !UteisValidacao.emptyNumber(item.getRemessa().getEmpresa())) {
            //primeiro obter do log, pois a multa de hoje (atual) pode não ser a mesma de quando a remessa foi gerada
            Boolean cobrarAutomaticamenteMultaJurosAntesDeGerarARemessa = getFacade().getLog().consultarAlteracaoCobrarMultaJurosAntesDaGeracaoDaRemessa(item.getRemessa().getDataRegistro(), item.getRemessa().getEmpresa());
            if (cobrarAutomaticamenteMultaJurosAntesDeGerarARemessa != null && (cobrarAutomaticamenteMultaJurosAntesDeGerarARemessa != empresaAtual.getCobrarAutomaticamenteMultaJuros())) {
                empresaAtual.setCobrarAutomaticamenteMultaJuros(cobrarAutomaticamenteMultaJurosAntesDeGerarARemessa);
            }

            if (empresaAtual.getCobrarAutomaticamenteMultaJuros()) {
                Double multaCobrancaAutomaticaAntesDeGerarARemessa = getFacade().getLog().consultarAlteracaoMultaAntesDaGeracaoDaRemessa(item.getRemessa().getDataRegistro(), item.getRemessa().getEmpresa());
                Double jurosCobrancaAutomaticaAntesDeGerarARemessa = getFacade().getLog().consultarAlteracaoJurosAntesDaGeracaoDaRemessa(item.getRemessa().getDataRegistro(), item.getRemessa().getEmpresa());
                Boolean utilizarMultaValorAbsolutoAntesDeGerarARemessa = getFacade().getLog().consultarAlteracaoMultaValorAbsolutoAntesDaGeracaoDaRemessa(item.getRemessa().getDataRegistro(), item.getRemessa().getEmpresa());
                Boolean utilizarJurosValorAbsolutoAntesDeGerarARemessa = getFacade().getLog().consultarAlteracaoJurosValorAbsolutoAntesDaGeracaoDaRemessa(item.getRemessa().getDataRegistro(), item.getRemessa().getEmpresa());

                //verificar se houve mudanças... atualizar somente as que teve mudanças
                if (multaCobrancaAutomaticaAntesDeGerarARemessa != null && (!multaCobrancaAutomaticaAntesDeGerarARemessa.equals(empresaAtual.getMultaCobrancaAutomatica()))) {
                    empresaAtual.setMultaCobrancaAutomatica(multaCobrancaAutomaticaAntesDeGerarARemessa);
                }
                if (jurosCobrancaAutomaticaAntesDeGerarARemessa != null && (!jurosCobrancaAutomaticaAntesDeGerarARemessa.equals(empresaAtual.getJurosCobrancaAutomatica()))) {
                    empresaAtual.setJurosCobrancaAutomatica(jurosCobrancaAutomaticaAntesDeGerarARemessa);
                }
                if (utilizarMultaValorAbsolutoAntesDeGerarARemessa != null && (utilizarMultaValorAbsolutoAntesDeGerarARemessa != empresaAtual.isUtilizarMultaValorAbsoluto())) {
                    empresaAtual.setUtilizarMultaValorAbsoluto(utilizarMultaValorAbsolutoAntesDeGerarARemessa);
                }
                if (utilizarJurosValorAbsolutoAntesDeGerarARemessa != null && (utilizarJurosValorAbsolutoAntesDeGerarARemessa != empresaAtual.isUtilizarJurosValorAbsoluto())) {
                    empresaAtual.setUtilizarJurosValorAbsoluto(utilizarJurosValorAbsolutoAntesDeGerarARemessa);
                }
                //atualizar a empresa no mapa
                mapaEmpresas.put(item.getRemessa().getEmpresa(), empresaAtual);
            }
        }
    }

    private void gerarDescontoRemessa(RemessaItemVO item, List<MovParcelaVO> parcelasEmAberto) throws Exception{
        List<MovParcelaVO> parcelasNovas = new ArrayList<MovParcelaVO>();
        for(MovParcelaVO parcela : parcelasEmAberto) {
            Double valorDesconto = parcela.getValorParcela().doubleValue() * item.getPorcentagemDescontoBoletoPagAntecipado() / 100;
            List<MovParcelaVO> parcelasRenegociar = new ArrayList<MovParcelaVO>();
            parcelasRenegociar.add(parcela);
            // parcela desconto
            MovParcelaVO parcelaRenegociar = new MovParcelaVO();
            parcelaRenegociar.setDescricao("DESCONTOS");
            parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorDesconto));
            parcelaRenegociar.setDataVencimento(Calendario.hoje());
            parcelasRenegociar.add(parcelaRenegociar);

            MovParcelaVO parcelaDesconto = new MovParcelaVO();
            parcelaDesconto.setDescricao("");
            parcelaDesconto.setValorParcela(valorDesconto);
            parcelaDesconto.setDataVencimento(Calendario.hoje());

            // Parcelas Renegociadas
            List<MovParcelaVO> parcelasRenegociadas = new ArrayList<MovParcelaVO>();
            MovParcelaVO novaParcela = (MovParcelaVO) parcela.getClone(true);
            novaParcela.setDescricao("PARCELA RENEGOCIADA");
            novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(parcela.getValorParcela() - valorDesconto));
            novaParcela.setDataRegistro(Calendario.hoje());
            parcelasRenegociadas.add(novaParcela);
            parcelaDesconto.setDescricao("");

            getFacade().getMovParcela().renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, new MovParcelaVO(), "DE", false, null, null, 0.0, false, usuarioVO, true, false, true, null, null);
            parcelasNovas.addAll(parcelasRenegociadas);

        }
        item.getMovParcelas().clear();
        item.getMovParcelas().addAll(getFacade().getRemessaItemMovParcela().alterarRemessaItemMovParcela(item, parcelasEmAberto, parcelasNovas));
        parcelasEmAberto.clear();
        parcelasEmAberto.addAll(parcelasNovas);
    }

    private Boolean deveGerarDesconto(RemessaItemVO item, Date dataOcorrencia, Double valorPago, EmpresaVO empresaVO) throws Exception {
        if (item.possuiDesconto() && valorPago < item.getValorItemRemessa() && (item.getValorItemRemessa() - valorPago > 1)) {
            boolean darDesconto = true;
            String dataMaximaDescontoAux = item.getDiaDoMesDescontoBoletoPagAntecipado().toString() + "/" + (Calendario.getData(item.getDataVencimentoBoleto(), "MM/yyyy"));
            Date dataMaximaDesconto = Calendario.getDate("dd/MM/yyyy", dataMaximaDescontoAux);
            dataMaximaDesconto = Calendario.fimDoDia(dataMaximaDesconto);
            Feriado feriadoDAO = new Feriado(this.con);
            Calendar cal = Calendario.getInstance();
            cal.setTime(dataMaximaDesconto);
            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                int diasSomar = (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) ? 2 : 1;
                dataMaximaDesconto = Uteis.somarDias(cal.getTime(), diasSomar);
            }
            List<Date> dataLimiteFeriado = feriadoDAO.consultarDiasFeriados(dataMaximaDesconto, dataMaximaDesconto, empresaVO);
            if (!dataLimiteFeriado.isEmpty()) {
                dataMaximaDesconto = Uteis.somarDias(dataMaximaDesconto, 1);
            }
            if (Calendario.maior(dataOcorrencia, dataMaximaDesconto)) {
                darDesconto = false;
            }
            return darDesconto;
        }
        return false;
    }

    /**
     * Realiza o carregamento dos {@link MovProdutoVO} correspondentes a {@link MovParcelaVO}
     * @param movparcelas Lista de {@link MovParcelaVO} que serão carregados os {@link MovProdutoVO}
     */
    private void carregarMovProdutos(List<MovParcelaVO> movparcelas) throws Exception{
        for(MovParcelaVO mov : movparcelas){
            mov.setMovProdutoParcelaVOs(getFacade().getMovProdutoParcela().consultarMovProdutoParcelasPorParcelas(mov.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        }
    }

    private void processarContaCorrente(ConvenioCobrancaVO convenioCobrancaVO,
            Integer codEmpresa, PessoaVO pessoaVO, ContratoVO contratoVO,
            Date dataInicio, Date dataFinal, Date dataOcorrencia) throws Exception {
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        MovimentoContaCorrenteClienteVO ultimoMovimento = getFacade().getMovimentoContaCorrenteCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (ultimoMovimento != null && ultimoMovimento.getSaldoAtual() > 0) {
            //Preparar dados para Análise
            double valorCredito = ultimoMovimento.getSaldoAtual();
            List<CaixaAbertoTO> caixas = getFacade().getMovParcela().consultaCaixaEmAberto(cliente.getMatricula(),
                    cliente.getNome_Apresentar(), cliente.getEmpresa().getCodigo(),
                    dataInicio, dataFinal, new ConfPaginacao(), false, false, new ArrayList<Integer>(), false);
            List<MovParcelaVO> parcelasPagarComCredito = new ArrayList<MovParcelaVO>();
            List<MovParcelaVO> parcelasEmAbertoDaMesmaCompetencia = new ArrayList<MovParcelaVO>();
            for (CaixaAbertoTO caixaAbertoTO : caixas) {
                parcelasEmAbertoDaMesmaCompetencia.addAll(caixaAbertoTO.getParcelas());
            }
            Ordenacao.ordenarLista(parcelasEmAbertoDaMesmaCompetencia, "valorParcela");
            Collections.reverse(parcelasEmAbertoDaMesmaCompetencia);

            //Analisar Parcelas e Valores
            for (MovParcelaVO movParcelaVO : parcelasEmAbertoDaMesmaCompetencia) {
                if (valorCredito >= movParcelaVO.getValorParcela() && !movParcelaVO.isNaoRealizarCobrancaAutomatica()) {
                    parcelasPagarComCredito.add(movParcelaVO);
                    valorCredito -= movParcelaVO.getValorParcela();
                }
            }

            if (parcelasPagarComCredito.size() > 0) {
                MovimentoContaCorrenteClienteVO movimento = new MovimentoContaCorrenteClienteVO();
                movimento.setSaldoAnterior(ultimoMovimento.getSaldoAtual());
                movimento.setTipoMovimentacao("DE");
                movimento.setDescricao("Pagar Parcelas");
                movimento.setValor(ultimoMovimento.getSaldoAtual() - valorCredito);
                movimento.setSaldoAtual(valorCredito);
                movimento.setResponsavelAutorizacao(usuarioVO);
                movimento.setPessoa(pessoaVO);

                MovPagamentoVO movPagamentoCC = new MovPagamentoVO();
                movPagamentoCC.setFormaPagamento(getFacade().getFormaPagamento().consultarPorTipoFormaPagamentoAtiva("CC", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                movPagamentoCC.setMovPagamentoEscolhida(true);
                movPagamentoCC.setValor(movimento.getValor());
                movPagamentoCC.setValorTotal(movimento.getValor());
                movPagamentoCC.setConvenio(convenioCobrancaVO);

                movPagamentoCC.setPessoa(pessoaVO);
                movPagamentoCC.setNomePagador(pessoaVO.getNome());
                movPagamentoCC.setResponsavelPagamento(usuarioVO);
                movPagamentoCC.getEmpresa().setCodigo(codEmpresa);
                movPagamentoCC.setCredito(false);

                movPagamentoCC.setDataLancamento(dataOcorrencia);
                movPagamentoCC.setDataQuitacao(dataOcorrencia);
                movPagamentoCC.setDataPagamento(dataOcorrencia);
                List<MovPagamentoVO> listaPagamentoAdicionais = new ArrayList<MovPagamentoVO>();
                listaPagamentoAdicionais.add(movPagamentoCC);

                ReciboPagamentoVO reciboAdicional = getFacade().getMovPagamento().incluirListaPagamento(
                        listaPagamentoAdicionais, parcelasPagarComCredito, movimento, contratoVO, false, 0.0);
                reciboAdicional.setData(dataOcorrencia);
                getFacade().getReciboPagamento().alterar(reciboAdicional);

                notificarWebhook(reciboAdicional);
            }
        }
    }

    private void emitirEcfNfse(ReciboPagamentoVO reciboObj) throws Exception {
        if (configSistema.isUsaEcf()) {
            CupomFiscalServiceImpl cupomFiscalServiceDAO = new CupomFiscalServiceImpl(this.con);
            cupomFiscalServiceDAO.setUsuarioLiberacao( usuarioVO);
            cupomFiscalServiceDAO.incluirCupom(reciboObj, true);
            cupomFiscalServiceDAO = null;
        }
        //o próprio método de enviar o nfse já valida se a empresa usa ou não o recurso
        try {
            MovPagamento movPagamentoDAO = new MovPagamento(this.con);
            reciboObj.getPagamentosDesteRecibo().get(0).setProdutosPagos(movPagamentoDAO.obterProdutosPagosMovPagamento(reciboObj.getPagamentosDesteRecibo().get(0).getCodigo()));
            movPagamentoDAO = null;
            Empresa empresaDAO = new Empresa(this.con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(reciboObj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            empresaDAO = null;
            if (empresaVO.isEnviarNFSeAutomatico() && empresaVO.getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()) {
                String chave = DAO.resolveKeyFromConnection(getCon());
                notaFiscal.gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboObj, usuarioVO, chave);
            }
        } catch (Exception e) {
            Uteis.logar(null, String.format("Nota Fiscal Eletronica ignorada para o Recibo: %s, "
                    + "devido ao erro: %s ", new Object[]{reciboObj.getCodigo(),
                e.getMessage()}));
        }
    }

    private SuperRemessaItemVO obterItem(RemessaVO remessa, RegistroRemessa regRet, boolean cobranca){
        String reservadoEstabelecimento = remessa.isDCCBin() ?  regRet.getValue(DCCAttEnum.NumeroComprovanteVenda.name()) :
                (remessa.isGetnet() ? regRet.getValue(DCCAttEnum.ReservadoEstabelecimento.name()) : "" );
        if (reservadoEstabelecimento == null || reservadoEstabelecimento.trim().isEmpty() ||
                (!remessa.isNovoFormato() && reservadoEstabelecimento.trim().length() < 15)) {
            return null;
        }

        Integer codigoParcela;
        if (remessa.isNovoFormato()) {
            codigoParcela = Integer.valueOf(reservadoEstabelecimento.trim());
        } else {
            codigoParcela = remessa.isDCCBin() ?  Integer.valueOf(reservadoEstabelecimento.trim()) :  Integer.valueOf(reservadoEstabelecimento.trim().substring(16));
        }

        if (cobranca) {
            for (RemessaItemVO remessaItem : remessa.getListaItens()) {
                if (remessa.isNovoFormato()) {
                    if (remessaItem.getCodigo().equals(codigoParcela)) {
                        return remessaItem;
                    }
                } else {
                    if (remessaItem.getMovParcela().getCodigo().equals(codigoParcela)) {
                        return remessaItem;
                    }
                }
            }
        } else {
            for (RemessaCancelamentoItemVO remessaCancelamentoItemVO : remessa.getListaItensCancelamento()) {
                if (remessa.isNovoFormato()) {
                    if (remessaCancelamentoItemVO.getItemRemessaCancelar().getCodigo().equals(codigoParcela)) {
                        return remessaCancelamentoItemVO;
                    }
                } else {
                    if (remessaCancelamentoItemVO.getItemRemessaCancelar().getMovParcela().getCodigo().equals(codigoParcela)) {
                        return remessaCancelamentoItemVO;
                    }
                }
            }
        }
        return null;
    }

    private RemessaItemVO obterItemFebraban(RemessaVO remessa, Map<Integer, RemessaItemVO> mapaItens,
                                            RegistroRemessa regRet, ConvenioCobrancaVO convenioCobrancaVO) {

        String codParcela = null;
        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
            codParcela = regRet.getValue(DCCAttEnum.NomePessoa.name());
            if (codParcela == null || codParcela.trim().isEmpty()) {
                return null;
            }
        } else {
            codParcela = regRet.getValue(DCCAttEnum.NumeroComprovanteVenda.name());
            if (codParcela == null || codParcela.trim().isEmpty() || codParcela.trim().length() < 7) {
                return null;
            }
        }

        Integer codigoParcela = Integer.valueOf(codParcela.trim());
        if (mapaItens == null) {
            mapaItens = processarMapaItens(remessa.getListaItens(), convenioCobrancaVO);
        }
        return mapaItens.get(codigoParcela);
    }

    private void realizarBaixaAutomatica(RemessaVO remessa) throws Exception {
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        if (remessa.getListaItens().isEmpty()) {
            return;
        }

        EmpresaVO empresaVO = new EmpresaVO();
        try {
            empresaVO = getFacade().getZWFacade().getEmpresa().consultarPorChavePrimaria(remessa.getEmpresa(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        List<String> erros = new ArrayList<>();
        for (int i = 0; i < detailsRetorno.size(); i++) {
            RegistroRemessa regRet = detailsRetorno.get(i);
            RemessaItemVO item;
            if (remessa.isGetnet() || remessa.isDCCBin()) {
                RemessaItemVO remessaItemVO = (RemessaItemVO) obterItem(remessa, regRet, true);
                if (remessaItemVO == null)
                    continue;
                item = getFacade().getZWFacade().getRemessaItem().consultarPorChavePrimaria(remessaItemVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            } else {

                if (remessa.isCielo() && remessa.isNovoFormato()) {
                    try {
                        item = getFacade().getZWFacade().getRemessaItem().consultarPorChavePrimaria(Integer.parseInt(regRet.getValue(DCCAttEnum.NumeroComprovanteVenda.name())), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        item = getFacade().getZWFacade().getRemessaItem().consultarPorChavePrimaria(remessa.getListaItens().get(i).getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    }
                } else {
                    item = getFacade().getZWFacade().getRemessaItem().consultarPorChavePrimaria(remessa.getListaItens().get(i).getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                }
            }

            if (remessa.getTipo() != null &&
                    (remessa.getTipo().equals(TipoRemessaEnum.EDI_CIELO) || remessa.getTipo().equals(TipoRemessaEnum.GET_NET) || remessa.getTipo().equals(TipoRemessaEnum.DCC_BIN))) {
                if (item.getMovPagamento() != null &&
                        !UteisValidacao.emptyNumber(item.getMovPagamento().getCodigo())) {
                    Uteis.logar(null, "### REMESSA ITEM " + item.getCodigo() + " - JÁ FOI REALIZADO A BAIXA");
                    continue;
                }
            }

            boolean erroCielo = false;
            for (ObjetoGenerico obj : regRet.getAtributos()) {

                //pular os demais itens
                if (erroCielo) {
                    continue;
                }

                if (obj.getAtributo().equals(DCCAttEnum.StatusVenda.name())) {
                    item.put(DCCAttEnum.StatusVenda.name(), obj.getValor());
                    if ((remessa.getTipo().equals(TipoRemessaEnum.BB_DCO) || remessa.getTipo().equals(TipoRemessaEnum.CAIXA_DCO))
                            && obj.getValor().equals(DCOBBStatusEnum.Status00.getId())) {
                        item.setRemessa(remessa);
                        incluirPagamento(item);
                    }

                    for (AcoesStatusRemessaVO acao : acoesStatusRemessaVOs) {
                        if (!empresaVO.isHabilitarReenvioAutomaticoRemessa() &&
                                acao.getCodigoStatus().equals(obj.getValor()) && acao.getReagendarAutomaticamente()) {
                            try {
                                List<MovParcelaVO> parcelasReagendar = new ArrayList<>();
                                if (item.getRemessa().isNovoFormato()) {
                                    for (RemessaItemMovParcelaVO itemMov : item.getMovParcelas()) {
                                        if (itemMov.getMovParcelaVO() != null &&
                                                !UteisValidacao.emptyNumber(itemMov.getMovParcelaVO().getCodigo())) {
                                            parcelasReagendar.add(itemMov.getMovParcelaVO());
                                        }
                                    }
                                } else {
                                    parcelasReagendar.add(item.getMovParcela());
                                }

                                for (MovParcelaVO movParcelaVO : parcelasReagendar) {
                                    try {
                                        if (!movParcelaVO.getSituacao().equals("EA")) {
                                            Uteis.logar(null, "ERRO AO REAGENDAR PARCELA NÃO ESTÁ EM ABERTO -> " + movParcelaVO.getCodigo() + " - REMESSA ITEM " + item.getCodigo());
                                            continue;
                                        }

                                        if (movParcelaVO.getNrTentativas() < (acao.getQtdTentativasParaReagendamentoAutomatico() + 1)) {
                                            List<MovParcelaVO> listaParcelasOriginais = new ArrayList<MovParcelaVO>();
                                            listaParcelasOriginais.add(movParcelaVO);

                                            List<MovParcelaVO> listaParcelas = new ArrayList<MovParcelaVO>();
                                            MovParcelaVO movParcelaAgendada = (MovParcelaVO) movParcelaVO.getClone(true);
                                            if (acao.getQtdDiasParaReagendamentoAutomatico() > 0) {
                                                movParcelaAgendada.setDataCobranca(Uteis.somarDias(Calendario.hoje(), acao.getQtdDiasParaReagendamentoAutomatico()));
                                                listaParcelas.add(movParcelaAgendada);
                                                getFacade().getZWFacade().getMovParcela().alterarVencimentoListaParcelas(listaParcelas, listaParcelasOriginais, true, "RECORRÊNCIA", "RemessaService", false);
                                            }
                                        }
                                    } catch (Exception e) {
                                        Uteis.logar(null, "ERRO AO REAGENDAR PARCELA -> " + movParcelaVO.getCodigo() + " - REMESSA ITEM " + item.getCodigo());
                                        if (movParcelaVO.getSituacao().equals("CA")) {
                                            getFacade().getRemessaItem().alterarProps(item.getCodigo(), "remessaCancelada=true");
                                            item.getProps().put("remessaCancelada", "true");
                                        }
                                        e.printStackTrace();
                                    }
                                }
                            } catch (Exception ex) {
                                ex.printStackTrace();
                                Uteis.logar(null, "ERRO AO REAGENDAR ITEM -> " + item.getCodigo());
                            }
                        }
                    }
                    getFacade().getZWFacade().getRemessaItem().alterar(item);
                } else if (obj.getAtributo().equals(DCCAttEnum.CodigoAutorizacao.name()) && (!remessa.getTipo().equals(TipoRemessaEnum.CAIXA_DCO))) {
                    item.put(DCCAttEnum.CodigoAutorizacao.name(), obj.getValor());

                    try {

                        //verificar se o status "00" porem sem código de autorização de cobrança
                        //erro que ocorre na cielo algumas vezes
                        if (remessa.getTipo().equals(TipoRemessaEnum.EDI_CIELO)) {
                            String statusVenda = item.get(DCCAttEnum.StatusVenda.name());
                            if (statusVenda == null || UteisValidacao.emptyString(statusVenda)) {
                                for (ObjetoGenerico obj2 : regRet.getAtributos()) {
                                    if (obj2.getAtributo().equals(DCCAttEnum.StatusVenda.name())) {
                                        statusVenda = obj2.getValor();
                                        break;
                                    }
                                }
                            }

                            if (statusVenda != null &&
                                    statusVenda.equalsIgnoreCase("00") &&
                                    (obj.getValor() == null || obj.getValor().isEmpty() ||
                                            obj.getValor().trim().replaceAll("0", "").isEmpty())) {
                                item.remove(DCCAttEnum.StatusVenda.name());
                                item.remove(DCCAttEnum.CodigoAutorizacao.name());
                                getFacade().getZWFacade().getRemessaItem().alterar(item);
                                erroCielo = true;
                                throw new Exception("Status \"00\" - CIELO não informou código de autorização");
                            }
                        }

                        if (remessa.getTipo().equals(TipoRemessaEnum.DCC_BIN) && obj.getValor().trim().isEmpty()) {
                            obj.setValor(null);
                        }
                        if (obj.getValor() != null && !obj.getValor().isEmpty() && obj.getValor().trim().replaceAll("[0-9a-zA-Z]", "").isEmpty()
                                && !obj.getValor().trim().replaceAll("0", "").isEmpty()) {
                            item.setRemessa(remessa);
                            incluirPagamento(item);
                        }

                    } catch (Exception e) {
                        String mensagemErro = "ERRO ITEM -> " + item.getCodigo() + " - " + e.getMessage();
                        Uteis.logar(null, mensagemErro);
                        erros.add(mensagemErro);
                        e.printStackTrace();
                    }
                    getFacade().getZWFacade().getRemessaItem().alterar(item);
                } else if (remessa.getTipo().equals(TipoRemessaEnum.GET_NET)) {
                    if (obj.getAtributo().equals(DCCAttEnum.DataVenda.name())) {
                        item.put(DCCAttEnum.DataVenda.name(), obj.getValor());
                        getFacade().getZWFacade().getRemessaItem().alterar(item);
                    } else if (obj.getAtributo().equals(DCCAttEnum.NumeroTerminal.name())) {
                        item.put(DCCAttEnum.NumeroTerminal.name(), obj.getValor());
                        getFacade().getZWFacade().getRemessaItem().alterar(item);
                    }

                }
            }
            processosPosRetornoRemessaItem(item);
        }

        if (!UteisValidacao.emptyList(erros)) {
            Uteis.logarDebug("### ERRO: Houve erros no processamento do retorno da remessa " + remessa.getCodigo());
            enviarEmailErroRetornoRemessa(erros, remessa);
            throw new Exception("### ERRO: Houve erros no processamento do retorno da remessa " + remessa.getCodigo());
        }
    }

    private void enviarEmailErroRetornoRemessa(List<String> listaErros, RemessaVO remessaVO) {
        try {
            if (UteisValidacao.emptyList(listaErros)) {
                return;
            }

            String chave = DAO.resolveKeyFromConnection(this.con);


            ZillyonWebFacade zillyonWebFacadeDAO;
            try {
                zillyonWebFacadeDAO = new ZillyonWebFacade(this.con);
                zillyonWebFacadeDAO.notificarRecursoSistema(chave, RecursoSistema.ERRO_RETORNO_REMESSA, usuarioVO.getCodigo(), remessaVO.getEmpresa());
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                zillyonWebFacadeDAO = null;
            }


            UteisEmail email = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
            String assunto = "Erro retorno remessa: " + remessaVO.getCodigo() + " | Chave " + chave;
            email.novo(assunto, config);

            String[] emailEnviar = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>"};

            StringBuilder emailTexto = new StringBuilder();
            emailTexto.append("<h2>Erros ao realizar retorno da remessa ").append(remessaVO.getCodigo()).append(" </h2>");
            emailTexto.append("<h4>Chave: ").append(chave).append("</h4>");
            for (String erro : listaErros) {
                emailTexto.append("<p> - ").append(erro).append("</p>");
            }

            email.enviarEmailN(emailEnviar, emailTexto.toString(), assunto, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro enviarEmailErroRetornoRemessa: " + ex.getMessage());
        }
    }

    public void processosPosRetornoRemessaItem(RemessaItemVO itemVO) {
        gravarTentativaCartao(itemVO);
        processarMovParcelaResultadoCobranca(itemVO);
        processarCartaoVerificado(itemVO);
    }

    private void gravarTentativaCartao(RemessaItemVO itemVO) {
        CartaoTentativa cartaoDAO = null;
        try {

            if (itemVO == null) {
                Uteis.logar(null, "Item null...");
                return;
            }

            String cartao = itemVO.get(APF.CartaoMascarado);
            String status = itemVO.getCodigoStatus();
            if (UteisValidacao.emptyString(cartao)) {
                Uteis.logar(null, "Cartão em branco...");
                return;
            }

            cartaoDAO = new CartaoTentativa(getCon());

            CartaoTentativaVO tentativaVO = new CartaoTentativaVO();
            tentativaVO.setTransacaoPresencial(false);
            tentativaVO.setRemessaItem(itemVO.getCodigo());
            tentativaVO.setCartao(cartao);
            tentativaVO.setCodigoRetorno(status);
            tentativaVO.setConvenioCobrancaVO(itemVO.getRemessa().getConvenioCobranca());
            tentativaVO.setTipoConvenioCobranca(itemVO.getRemessa().getConvenioCobranca().getTipo());
            tentativaVO.setOperacaoRetornoCobranca(OperacaoRetornoCobrancaEnum.obter(status, itemVO.getRemessa().getConvenioCobranca().getTipo()));
            if (itemVO.getRemessa().getUsuarioVO() != null) {
                tentativaVO.setUsuario(itemVO.getRemessa().getUsuarioVO().getCodigo());
            }

            cartaoDAO.incluir(tentativaVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            cartaoDAO = null;
        }
    }

    private void processarMovParcelaResultadoCobranca(RemessaItemVO itemVO) {
        MovParcelaResultadoCobranca dao = null;
        try {
            if (itemVO == null) {
                return;
            }

            String listaParcelas = "";
            if (!UteisValidacao.emptyNumber(itemVO.getMovParcela().getCodigo())) {
                listaParcelas += ("," + itemVO.getMovParcela().getCodigo());
            }

            for (RemessaItemMovParcelaVO itemParcela : itemVO.getMovParcelas()) {
                if (!UteisValidacao.emptyNumber(itemParcela.getMovParcelaVO().getCodigo())) {
                   listaParcelas += ("," + itemParcela.getMovParcelaVO().getCodigo());
                }
            }

            if (UteisValidacao.emptyString(listaParcelas)) {
                return;
            }

            dao = new MovParcelaResultadoCobranca(getCon());
            dao.processarParcelas(listaParcelas.replaceFirst(",",""), null);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "processarMovParcelaResultadoCobranca ERRO: " + ex.getMessage());
        } finally {
            dao = null;
        }
    }

    private void processarCartaoVerificado(RemessaItemVO itemVO) {
        AutorizacaoCobrancaCliente autoDAO;
        try {
            if (itemVO == null ||
                    UteisValidacao.emptyString(itemVO.getValorTokenAragorn()) ||
                    UteisValidacao.emptyString(itemVO.getValorCartaoMascaradoOuAgenciaConta())) {
                return;
            }

            autoDAO = new AutorizacaoCobrancaCliente(getCon());
            autoDAO.processarCartaoVerificado(itemVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "processarCartaoVerificado ERRO: " + ex.getMessage());
        } finally {
            autoDAO = null;
        }
    }

    private void processarRegistroB(RemessaVO remessa, RegistroRemessa regRet, List<String> dadosInvalidos) throws Exception {
        String identificadorClienteEmpresa = regRet.getValue(DCCAttEnum.IdentificadorClienteEmpresa.name()).trim();
        String identificadorClienteBanco = regRet.getValue(DCCAttEnum.IdentificadorClienteBanco.name()).trim();

        String codigoMovimento = regRet.getValue(DCCAttEnum.CodigoMovimento.name());
        //TODO criar um Enum para este códigoMovimento (1 - Exclusão; 2 - Inclusão)

        try {
            MaskFormatter mask = new MaskFormatter("###.###.###-##");
            mask.setValueContainsLiteralCharacters(false);
            String cpf = mask.valueToString(identificadorClienteEmpresa);
            List<PessoaVO> pessoas = new ArrayList<PessoaVO>();
            if(!cpf.equals("000.000.000-00")){
                pessoas = getFacade().getZWFacade().getPessoa().consultarPorCfp(cpf, false, Uteis.NIVELMONTARDADOS_MINIMOS);
            }
            int codPessoa = 0;
            if(pessoas.size() == 0 && remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.COD_PESSOA)){
                codPessoa = Integer.parseInt(identificadorClienteEmpresa);
                if(UteisValidacao.notEmptyNumber(codPessoa)){
                    PessoaVO pessoaVO = getFacade().getZWFacade().getPessoa().consultarPorCodigo(codPessoa, Uteis.NIVELMONTARDADOS_MINIMOS);
                    if(UtilReflection.objetoMaiorQueZero(pessoaVO,"getCodigo()")){
                        pessoas.add(pessoaVO);
                    }
                }
            }


            if (pessoas.size() == 0 || pessoas.size() > 1) {
                if(pessoas.size() == 0 && remessa.getConvenioCobranca().getIdentificadorClienteEmpresa().equals(IdentificadorClienteEmpresaEnum.COD_PESSOA)){
                    dadosInvalidos.add("pessoa: "+codPessoa);
                } else{
                    dadosInvalidos.add("CPF:"+identificadorClienteEmpresa.trim());
                }
            } else {
                PessoaVO pessoaVO = pessoas.get(0);

                ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                boolean pessoaCliente = (cliente.getCodigo() != 0);

                List<AutorizacaoCobrancaVO> autorizacoes = new ArrayList<AutorizacaoCobrancaVO>();
                if (pessoaCliente) {
                    autorizacoes.addAll(getFacade().getZWFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                } else {
                    autorizacoes.addAll(getFacade().getZWFacade().getAutorizacaoCobrancaColaborador().consultarPorPessoa(pessoaVO.getCodigo()));
                }
                boolean encontrouAutorizacao = false;
                for (AutorizacaoCobrancaVO autorizacao : autorizacoes) {
                    if (autorizacao.getConvenio().getCodigo().equals(remessa.getConvenioCobranca().getCodigo())) {
                        encontrouAutorizacao = true;
                        if (codigoMovimento.equals("1")) {
                            if (pessoaCliente) {
                                AutorizacaoCobrancaClienteVO autoCliente = (AutorizacaoCobrancaClienteVO) autorizacao;
                                autoCliente.setGravarLogRegistroB(true);
                                getFacade().getZWFacade().getAutorizacaoCobrancaCliente().alterarSituacaoAutorizacaoCobranca(false, autoCliente, "", usuarioVO);
                                getFacade().getZWFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(autoCliente.getCartaoMascarado(), autoCliente.getCliente().getCodigo());
                            } else {
                                AutorizacaoCobrancaColaboradorVO autoColaborador = (AutorizacaoCobrancaColaboradorVO) autorizacao;
                                getFacade().getZWFacade().getAutorizacaoCobrancaColaborador().desativar(autoColaborador);
                            }
                        } else {
                            autorizacao.setIdentificacaoClienteBanco(identificadorClienteBanco);
                            if (pessoaCliente) {
                                AutorizacaoCobrancaClienteVO autoCliente = (AutorizacaoCobrancaClienteVO) autorizacao;
                                autoCliente.setGravarLogRegistroB(true);
                                getFacade().getZWFacade().getAutorizacaoCobrancaCliente().alterar(autoCliente);

                                if(!autoCliente.isAtiva()){
                                    getFacade().getZWFacade().getClienteMensagem().excluirClienteMensagemCartaoVencido(autoCliente.getCartaoMascarado(), autoCliente.getCliente().getCodigo());
                                }

                            } else {
                                AutorizacaoCobrancaColaboradorVO autoColaborador = (AutorizacaoCobrancaColaboradorVO) autorizacao;
                                getFacade().getZWFacade().getAutorizacaoCobrancaColaborador().alterar(autoColaborador);
                            }
                        }
                        break;
                    }
                }
                if (!encontrouAutorizacao && codigoMovimento.equals("2")) {
                    String agencia = regRet.getValue(DCCAttEnum.AgenciaDebito.name());

                    AutorizacaoCobrancaVO autorizacao;
                    if (pessoaCliente) {
                        AutorizacaoCobrancaClienteVO autoCliente = new AutorizacaoCobrancaClienteVO();
                        autoCliente.setGravarLogRegistroB(true);
                        autoCliente.setCliente(getFacade().getZWFacade().getCliente().consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                        autorizacao = autoCliente;
                    } else {
                        AutorizacaoCobrancaColaboradorVO autoColaborador = new AutorizacaoCobrancaColaboradorVO();
                        autoColaborador.setColaborador(getFacade().getZWFacade().getColaborador().consultarPorCodigoPessoa(pessoaVO.getCodigo(), remessa.getEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS));
                        autorizacao = autoColaborador;
                    }

                    autorizacao.setConvenio(remessa.getConvenioCobranca());

                    autorizacao.setAgencia(Integer.parseInt(agencia));
                    autorizacao.setIdentificacaoClienteBanco(identificadorClienteBanco);
                    autorizacao.setBanco(remessa.getConvenioCobranca().getBanco());
                    autorizacao.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.DEBITOCONTA);
                    autorizacao.setTipoACobrar(TipoObjetosCobrarEnum.APENAS_PLANOS);

                    if (pessoaCliente) {
                        AutorizacaoCobrancaClienteVO autoCliente = (AutorizacaoCobrancaClienteVO) autorizacao;
                        autoCliente.setGravarLogRegistroB(true);
                        getFacade().getZWFacade().getAutorizacaoCobrancaCliente().incluir(autoCliente);
                    } else {
                        AutorizacaoCobrancaColaboradorVO autoColaborador = (AutorizacaoCobrancaColaboradorVO) autorizacao;
                        getFacade().getZWFacade().getAutorizacaoCobrancaColaborador().incluir(autoColaborador);
                    }
                }
            }
        } catch (Exception ex) {
            Uteis.logar(null, "ERRO (REGISTRO B) PESSOA -> " + identificadorClienteEmpresa);
            throw ex;
        }
    }

    private void enviarRemessaRemoto(RemessaVO remessaVO, UsuarioVO usuarioVO) throws Exception {
        if (remessaVO.getConvenioCobranca().isPactoPay()) {
            String chave = DAO.resolveKeyFromConnection(this.getCon());
            String idPactoPay = PactoPayService.enviarRemessa(remessaVO, chave, remessaVO.isCancelamento());
            remessaVO.setIdPactoPay(idPactoPay);
            getFacade().getZWFacade().getRemessa().alterarIdPactoPay(remessaVO);
            //marcar remessa como enviada
            marcarRemessaEnviada(remessaVO, usuarioVO);
        } else {
            enviarRemessaRemotoGeral(remessaVO, usuarioVO);
        }
    }

    private void enviarRemessaRemotoGeral(RemessaVO remessa, UsuarioVO usuarioVO) throws Exception {
        //TODO CASO DESEJE ENVIAR UMA REMESSA DEVE CHAMAR O MÉTODO -- processoUnicoEnviarRemessas !
        //by Luiz Felipe

        Uteis.logar(true, null, "Inicia enviarRemessaRemoto | " + remessa.getConvenioCobranca().getDescricao() + " | Cod. " + remessa.getCodigo() + " | Seq. " + remessa.getSequencialArquivoExibicao());

        SFTP sftp = new SFTP(remessa.getConvenioCobranca().getHostSFTP(),
                remessa.getConvenioCobranca().getUserSFTP(),
                remessa.getConvenioCobranca().getPwdSFTP(),
                Integer.valueOf(remessa.getConvenioCobranca().getPortSFTP()));
        StringBuilder sb = new StringBuilder();
        String path = remessa.getConvenioCobranca().getDiretorioLocalTIVIT()
                + remessa.getConvenioCobranca().getDiretorioLocalUploadTIVIT()
                + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy")
                + File.separator;

        Uteis.logar(null, "### Path para enviar Remessa: " + path);
        if (remessa.getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {

            if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                for (RemessaItemVO ri : remessa.getListaItens()) {
                    ri.setAutorizacaoCobrancaVO(getFacade().getAutorizacaoCobrancaCliente().consultar(ri.getClienteVO().getCodigo(), TipoAutorizacaoCobrancaEnum.DEBITOCONTA));
                }
            }

            if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) {
                sb = LayoutRemessaItauDCO.prepareFile(remessa);
            } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                sb = LayoutRemessaCaixaSIACC150DCO.prepareFile(remessa);
            } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                sb = LayoutRemessaBradescoCNAB240.prepareFile(remessa);
            } else if (remessa.getConvenioCobranca().isLayoutFebrabanDCO()) {
                if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BB)) {
                    sb = LayoutRemessaBBDCO.prepareFile(remessa);
                } else {
                    sb = LayoutRemessaFebrabanDCO.prepareFile(remessa);
                }
            }

        } else if (remessa.getConvenioCobranca().getTipo() == TipoConvenioCobrancaEnum.DCC_BIN) {
            //se o tipo de convenio pode ser configurado para usar chave privada para conexao ao SFTP para envio de arquivos, informar o caminho da chave
            sftp.setMyIdRSA(remessa.getConvenioCobranca().getDiretorioLocalTIVIT() + "id_rsa");
            sb = LayoutRemessaBinDCC.prepareFile(remessa);
        } else {
            sb = LayoutRemessaCieloDCC.prepareFile(remessa);
        }

        FileUtilities.forceDirectory(path);

        if (remessa.getConvenioCobranca().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {

            String destino = remessa.getConvenioCobranca().getDiretorioRemotoTIVIT_IN() + File.separator;
            path = destino + remessa.getNomeArquivoDownload();
            remessa.setNomeArquivoEnvio(remessa.getNomeArquivoDownload());
            Uteis.logar(true, null, "### Arquivo destino: " + path);

            remessa.getConvenioCobranca().setDiretorioRemotoTIVIT_IN(destino);

            StringUtilities.saveToFile(sb, path);

        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            String nomeArquivoEnvio = (remessa.getNomeArquivo() + "_" + remessa.getConvenioCobranca().getNumeroContrato() + "_" + remessa.getCodigo() + ".txt.enc");
            remessa.setNomeArquivoEnvio(nomeArquivoEnvio);
            path += nomeArquivoEnvio;
            PgpEncryption.salvarCriptografado(sb.toString(), path, remessa.getConvenioCobranca().getChaveBIN());
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            String nomeArquivoEnvio = (remessa.getNomeArquivo() + ".txt.gpg");
            remessa.setNomeArquivoEnvio(nomeArquivoEnvio);
            path += nomeArquivoEnvio;
            PgpEncryption.salvarCriptografado(sb.toString(), path, remessa.getConvenioCobranca().getChaveGETNET());
        } else {
            final String sufixo = remessa.getConvenioCobranca().getTipo() == TipoConvenioCobrancaEnum.DCC
                    ? remessa.getConvenioCobranca().getNumeroContrato() : getKey();

            String nomeArquivoEnvio = (String.format("%s_%s_%s.rem", remessa.getNomeArquivo(), sufixo, remessa.getCodigo()));
            remessa.setNomeArquivoEnvio(nomeArquivoEnvio);

            path += nomeArquivoEnvio;
            StringUtilities.saveToFile(sb, path);
        }


        //aguardar para enviar remessas Cielo e GetNet
        if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC) ||
                remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            Integer aguardar = Uteis.getTempoAguardarRemessa();
            Uteis.logar(null, "# Vou aguardar... " + aguardar + " milisegundos antes de enviar: " + remessa);
            if (!UteisValidacao.emptyNumber(aguardar)) {
                Thread.sleep(aguardar);
            }
        }


        if (remessa.isCancelamento()
                && remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            Uteis.logar(null, "# Preparando para enviar a remessa de Cancelamento: " + remessa);
            Uteis.logar(null, "# Diretório: " + remessa.getConvenioCobranca().getDiretorioGETNET_CANCELAMENTO_IN());
            sftp.putFile(path, remessa.getConvenioCobranca().getDiretorioGETNET_CANCELAMENTO_IN());
        } else {
            sftp.putFile(path, remessa.getConvenioCobranca().getDiretorioRemotoTIVIT_IN());

            if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
                try {
                    /**
                     * Criado um sleep para conexao com a Getnet.
                     * Devido a várias tentativas ao mesmo tempo está ocorrendo problemas de bloqueio e envio de arquivos.
                     * by Luiz Felipe 16/04/2020
                     */
                    Uteis.logar(null, "# Listar Arquivos após envio da remessa - Sleep Getnet: " + sleepGetnet + " | " + remessa.getConvenioCobranca().getDescricao());
                    Thread.sleep(sleepGetnet);

                    Uteis.logar(null, "# Vou listar arquivos enviados para Getnet: " + remessa.getConvenioCobranca().getDescricao());
                    sftp.list(remessa.getConvenioCobranca().getDiretorioRemotoTIVIT_IN());
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar(null, "# Erro ao listar arquivos enviados para Getnet: " + remessa.getConvenioCobranca().getDescricao());
                }
            }
        }

        //marcar remessa como enviada
        marcarRemessaEnviada(remessa, usuarioVO);

        Uteis.logar(true, null, "Final enviarRemessaRemoto | " + remessa.getConvenioCobranca().getDescricao() + " | Cod. " + remessa.getCodigo() + " | Seq. " + remessa.getSequencialArquivoExibicao());
    }

    private void marcarRemessaEnviada(RemessaVO remessaVO, UsuarioVO usuarioVO) throws Exception {
        getFacade().getZWFacade().getRemessa().incluirRemessaHistoricoEnvio(remessaVO, usuarioVO);
        remessaVO.setDataEnvio(Calendario.hoje());
        remessaVO.setSituacaoRemessa(SituacaoRemessaEnum.REMESSA_ENVIADA);
        getFacade().getZWFacade().getRemessa().alterar(remessaVO);
    }

    public void processarArquivosRetornoRemoto(final List<Map<String, File>> arquivos,
            final ConvenioCobrancaVO conv) throws Exception {
        TipoConvenioCobrancaEnum tipo = conv.getTipo();
        if (tipo != null && tipo.equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            Security.addProvider(new BouncyCastleProvider());
        }
        for (Map<String, File> entrada : arquivos) {
            Set<String> s = entrada.keySet();
            FORARQ:
            for (String fileName : s) {
                File f = entrada.get(fileName);
                try {
                    if (f.length() > 0) {
                        if ((tipo == TipoConvenioCobrancaEnum.DCC_BIN || tipo == TipoConvenioCobrancaEnum.DCC_GETNET)
                                && (!fileName.contains(conv.getNumeroContrato()))){
                            continue;
                        }
                        StringBuilder arq;
                        if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                                && !UteisValidacao.emptyString(conv.getNossaChave())) {
                            byte[] arquivo = FileUtilities.obterBytesArquivo(f);
                            InputStream chave = new ByteArrayInputStream(conv.getNossaChave().getBytes());
                            try {
                                byte[] decrypt = PgpEncryption.decrypt(arquivo, chave, conv.getNossaSenha(), false);
                                arq = new StringBuilder(new String(decrypt, "UTF-8"));
                            } catch (Exception e) {
                                Uteis.logar(null, "Erro na descriptografia do arquivo" + fileName + " :" + e.getMessage());
                                continue FORARQ;
                            }

                        } else {
                            arq = FileUtilities.readContentFile(fileName);
                        }
                        /*Uteis.logar(null, String.format("Lendo arquivo de retorno \"%s\"\nHeader: %s\n",
                                new Object[]{f.getAbsolutePath(), arq.substring(0, arq.indexOf("\n"))}));*/

                        RegistroRemessa regHead = LayoutRemessaBase.obterHeaderRetorno(arq, conv.getTipo());
                        if (regHead.get(DCCAttEnum.NumeroEstabelecimento.name()).isEmpty() && regHead.get(DCCAttEnum.ReservadoEstabelecimento.name()).isEmpty()) {
                            Uteis.logar(null, "Erro: Leitura do header falhou para esse arquivo com esse tipo de convênio");
                            continue;
                        }
                        ResultSet criarConsulta = ZillyonWebFacade.criarConsulta(String.format("select * from remessa "
                                + "where empresa = %s and (head like ('%%%s%%') and head like ('%%%s%%'))", conv.getEmpresa().getCodigo(),
                                regHead.get(DCCAttEnum.NumeroEstabelecimento.name()),
                                regHead.get(DCCAttEnum.ReservadoEstabelecimento.name())), getCon());
                        List<RemessaVO> remessas = Remessa.montarDadosConsulta(criarConsulta, getCon());
                        if (remessas.size() == 1) {
                            RemessaVO remessaVO = remessas.get(0);
                            Uteis.logar(null, "Encontrei remessa compatível: " + remessaVO + ", vou processar a baixa automática... Codigo " + remessaVO.getCodigo());
                            remessaVO.setRetorno(arq);
                            if (remessaVO.isCancelamento()) {
                                LayoutRemessaBase.lerRetornoCancelamento(remessaVO);
                                getL().lerHeaderETrailerRemessa(remessaVO);
                            } else {
                                getL().lerHeaderETrailerRemessa(remessaVO);
                                LayoutRemessaBase.lerRetorno(remessaVO);
                            }
                            processarRetorno(remessaVO, usuarioVO);
                            remessaVO.setResultadoProcessamentoRetorno("Processamento concluído. Clique no botão resultado para maiores detalhes.");
                            Uteis.logar(null, "Processamento retorno da Remessa: " + remessaVO + " concluído com sucesso! Codigo " + remessaVO.getCodigo());
                        } else if (remessas.size() > 1) {
                            Uteis.logar(null, "Erro: Mais de uma Remessa compatível com este Retorno foi encontrada");
                        }
                    } else {
                        Uteis.logar(null, "Erro: Desprezando arquivo vazio: " + f.getAbsolutePath());
                    }
                } catch (Exception e) {
                    Uteis.logar(null, String.format("Erro %s ao processar arquivo %s",
                            e.getMessage(), f.getAbsolutePath()));
                    e.printStackTrace();
                }
            }


        }

    }

    public List<RemessaVO> preencherRemessasCancelamento(List<RemessaCancelamentoItemVO> listaItensCancelar, ConvenioCobrancaVO convenio, EmpresaVO empresa, UsuarioVO usuario) throws Exception {
        validarDataDosCreditos(empresa);

        List<RemessaVO> listaRemessas = new ArrayList<RemessaVO>();

        //adicionado para evitar remessas com a mesma data de registro.
        //by Luiz Felipe
        Thread.sleep(2000);

        RemessaVO remessa = new RemessaVO();
        remessa.setCancelamento(true);

        remessa.setUsuario(usuario);
        remessa.setTipo(convenio.getTipo().getTipoRemessa());
        remessa.setDataRegistro(Calendario.hoje());
        remessa.setDataInicio(Calendario.hoje());
        remessa.setConvenioCobranca(convenio);
        remessa.setEmpresa(empresa.getCodigo());
        remessa.gerarIdentificador(con);

        for (RemessaCancelamentoItemVO itemCancelar : listaItensCancelar) {
            itemCancelar.setRemessa(remessa);
            remessa.getListaItensCancelamento().add(itemCancelar);
        }
        listaRemessas.add(remessa);

        return listaRemessas;
    }

    private void validarDataDosCreditos(EmpresaVO empresa) throws ConsistirException {
        Date dataExpiracaoCreditoDCC = empresa.getDataExpiracaoCreditoDCC();
        if (dataExpiracaoCreditoDCC != null && Calendario.menorOuIgual(dataExpiracaoCreditoDCC, Calendario.hoje())) {
            throw new ConsistirException("Não é possível criar a remessa. Seus créditos estão bloqueados.");
        }
    }

    private int calcularQtdCreditosUsados(RemessaVO remessaVO) {
        int qtdUsada = 0;
        qtdUsada += remessaVO.getListaItens().size();
        if (!remessaVO.isBoleto()) {
            qtdUsada += remessaVO.getListaItensCancelamento().size();
        }
        return qtdUsada;
    }

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"teste", "somenteRetorno"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            Uteis.debug = true;
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);
            CreditoDCCService creditoDCCService = new CreditoDCCService();
            creditoDCCService.setKey(chave);
            RemessaService service = new RemessaService();
            service.setKey(chave);
            CHAVE_EMPRESA_REMESSA = chave;

            // Testar consulta de pagamentos
           /* ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(3, Uteis.NIVELMONTARDADOS_PARCELA);
            convenioCobrancaVO.getEmpresa().setCodigo(1);
            PagamentoService pagamentoService = new PagamentoService(con, convenioCobrancaVO);
            Calendario.dia = new SimpleDateFormat("ddMMyyyy").parse("18082021");
            List<MovParcelaVO> listaParcelas = pagamentoService.consultarPagamentos(Calendario.hoje());
            Integer a = Integer.parseInt("A");*/

//            service.setLimiteRegistrosPorEmpresa(5);
//            service.setSomenteRepescagem(true);
//            service.setSomenteErroX("08");

            preencherTabelasEnums(con);
            processarDadosInfoMigracao(con);

            if (args.length > 1 && args[1].equals("somenteRetorno")) {
                service.processarApenasRetorno();
            } else {
                try {
                    //Processar apenas tipos de convênios especificados via Shell no formato "convenios=1,2,3,4"
                    if (args.length > 1 && args[1].contains("convenios=")) {
                        final String convenios = args[1].substring(10);
                        List<Integer> codigos = new ArrayList();
                        for (StringTokenizer ids_convenios = new StringTokenizer(convenios, ","); ids_convenios.hasMoreTokens();) {
                            final String token = ids_convenios.nextToken();
                            codigos.add(new Integer(token));
                        }
                        //converte ArrayList para Array
                        if (codigos.size() > 0) {
                            Integer[] ids = new Integer[codigos.size()];
                            TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC = codigos.toArray(ids);
                            service.setProcessarApenasRemessas(true);
                        }
                    }
//
                    creditoDCCService.processarCobrancaPacto(Calendario.hoje());
                    service.processarCobrancas(Calendario.hoje(), chave);
                }catch (Exception e){
                    Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                }

                try {
                    VerificadorCobrancasService veri = new VerificadorCobrancasService(service.getCon());
                    veri.processar();
                    veri = null;
                }catch (Exception e){
                    Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                }

                // Executado de forma assíncrona porque os as rotinas a abaixo não dependem de processarNotas
                // Quando o processarNotas falhar ele não deve impactar no processamentos abaixo
                String finalChave = chave;
                Thread threadProcessarNotas = new Thread(() -> {
                    try {
                        Connection conNotas = new DAO().obterConexaoEspecifica(finalChave);
                        Conexao.guardarConexaoForJ2SE(finalChave, conNotas);
                        RoboVO roboVO = new RoboVO();
                        roboVO.setDia(Calendario.hoje());
                        roboVO.setListaEmpresa(getFacade().getEmpresa().consultarTodas(null, Uteis.NIVELMONTARDADOS_ROBO));
                        AdministrativoRunner.processarNotas(roboVO, finalChave, con);
                    } catch (Exception e) {
                        Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                    }
                });
                threadProcessarNotas.start();

                try {
                    ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(con);
                    conciliadoraService.processarAutomatico(Calendario.hoje());
                    conciliadoraService = null;
                } catch (Exception e) {
                    Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
                }
            }

            // consultar pix pagos e dar baixa caso não tenham sido dado baixa via webhook ainda
            try {
                Uteis.logarDebug("Processamento de PIX | Origem: RemessaService --> INÍCIO ");
                PixScheduleService pixScheduleService = new PixScheduleService(chave);
                pixScheduleService.processarCobrancas();
                Uteis.logarDebug("Processamento de PIX | Origem: RemessaService --> FIM");
            } catch (Exception ex) {
                Uteis.logarDebug("ERRO Processamento de PIX | Origem: RemessaService");
                ex.printStackTrace();
            }

            // Expirar os pix que chegaram na expiração
            try {
                PixService pixService = new PixService(con);
                pixService.processarExpirarPix();
            } catch (Exception e) {
                Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
            }

            // Expirar boletos PJBank 58 dias ou mais
            try {
                ProcessosPJBank processosPJBank = new ProcessosPJBank();
                processosPJBank.cancelarBoletos58DiasVencidos(con);
            } catch (Exception e){
                Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, e);
            }

            solicitarConciliacaoStone(chave, con);

        } catch (Exception ex) {
            Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            Uteis.finalizarExecutor(0);
        }
    }

    private static void preencherTabelasEnums(Connection con) {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);
            Integer diaAtual = Uteis.getDiaMesData(Calendario.hoje());
            convenioCobrancaDAO.preencherTabelasEnums(diaAtual.equals(1) || diaAtual.equals(10) || diaAtual.equals(20));
            preencherTabelaTipoInfoMigracaoEnum(diaAtual.equals(1) || diaAtual.equals(10) || diaAtual.equals(20), con);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    private static void solicitarConciliacaoStone(String chave, Connection con) {
        try {
            Integer diaAtual = Uteis.getDiaMesData(Calendario.hoje());
            Integer ultimoDiaMesAtual = Uteis.getDiaMesData(Calendario.ultimoDiaMes());
            if (diaAtual.equals(1) || diaAtual.equals(15) || diaAtual.equals(ultimoDiaMesAtual)) {
                ProcessoSolicitarConcessoExtratoStone.processarUmaEmpresa(chave, con);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void processarDadosInfoMigracao(Connection con) {
        ajustarInfoMigracao(con);
        ajustarInfoMigracaoHistorico(con);
        processarDadosInfoMigracaoUsuario(con);
        processarDadosInfoMigracaoEmpresa(con);
    }

    private static void processarDadosInfoMigracaoUsuario(Connection con) {
        try {
            Usuario usuarioDAO;
            try {
                usuarioDAO = new Usuario(con);
                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                for (TipoInfoMigracaoEnum tipoInfoMigracaoEnum : TipoInfoMigracaoEnum.values()) {
                    if (tipoInfoMigracaoEnum.isRecursoPadrao()) {
                        usuarioDAO.incluirInfoMigracaoParaUsuarioSem("false", null, tipoInfoMigracaoEnum, usuarioVO);
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                usuarioDAO = null;
            }

            //apagar os registros de hoje
            SuperFacadeJDBC.executarUpdate("DELETE FROM infomigracaohistorico WHERE dataregistro::date = '" + Uteis.getDataFormatoBD(Calendario.hoje()) + "';", con);

            //incluir os registro de hoje
            StringBuilder sqlHist = new StringBuilder();
            sqlHist.append("INSERT INTO infomigracaohistorico(tipoinfo, usuario, info, origem, dataregistro, usuario_ativo) \n");
            sqlHist.append("select tipoinfo, usuario, info, origem, now(), \n");
            sqlHist.append("exists(\n");
            sqlHist.append("select \n");
            sqlHist.append("u1.codigo as usuario\n");
            sqlHist.append("from usuario u1 \n");
            sqlHist.append("inner join colaborador c1 on c1.codigo = u1.colaborador \n");
            sqlHist.append("inner join pessoa p1 on p1.codigo = c1.pessoa \n");
            sqlHist.append("where exists(select c2.codigo from colaborador c2 where c2.situacao = 'AT' and c2.pessoa = p1.codigo)\n");
            sqlHist.append("and u1.codigo = u.codigo\n");
            sqlHist.append(") as usuario_ativo\n");
            sqlHist.append("from infomigracao i\n");
            sqlHist.append("inner join usuario u on u.codigo = i.usuario\n");
            sqlHist.append(" where tipoinfo in (").append(TipoInfoMigracaoEnum.obterCodigos(true)).append(");");
            SuperFacadeJDBC.executarUpdate(sqlHist.toString(), con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void ajustarInfoMigracao(Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("usuario, \n");
            sql.append("tipoinfo, \n");
            sql.append("count(*) as qtd, \n");
            sql.append("max(codigo) as codigo \n");
            sql.append("from infomigracao \n");
            sql.append("group by 1,2 \n");
            sql.append("having count(*) > 1 \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                StringBuilder sqlDel = new StringBuilder();
                sqlDel.append("delete from infomigracao where usuario = ").append(rs.getInt("usuario"));
                sqlDel.append(" and tipoinfo = ").append(rs.getInt("tipoinfo"));
                sqlDel.append(" and codigo not in (").append(rs.getInt("codigo")).append(");");
                SuperFacadeJDBC.executarUpdate(sqlDel.toString(), con);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void ajustarInfoMigracaoHistorico(Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("dataregistro::date as dia, \n");
            sql.append("usuario, \n");
            sql.append("tipoinfo, \n");
            sql.append("count(*), \n");
            sql.append("max(codigo) as codigo \n");
            sql.append("from infomigracaohistorico \n");
            sql.append("group by 1,2,3 \n");
            sql.append("having count(*) > 1 \n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                StringBuilder sqlDel = new StringBuilder();
                sqlDel.append("delete from infomigracaohistorico where usuario = ").append(rs.getInt("usuario"));
                sqlDel.append(" and tipoinfo = ").append(rs.getInt("tipoinfo"));
                sqlDel.append(" and dataregistro::date = '").append(Uteis.getDataFormatoBD(rs.getDate("dia"))).append("' ");
                sqlDel.append(" and codigo not in (").append(rs.getInt("codigo")).append(");");
                SuperFacadeJDBC.executarUpdate(sqlDel.toString(), con);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void processarDadosInfoMigracaoEmpresa(Connection con) {
        try {
            //apagar os registros de hoje
            SuperFacadeJDBC.executarUpdate("DELETE FROM infomigracaohistoricoempresa WHERE dataregistro::date = '" + Uteis.getDataFormatoBD(Calendario.hoje()) + "';", con);

            for (TipoInfoMigracaoEnum tipoInfoMigracaoEnum : TipoInfoMigracaoEnum.values()) {
                if (!tipoInfoMigracaoEnum.isRecursoPadrao()) {
                    continue;
                }
                StringBuilder sqlHist = new StringBuilder();
                sqlHist.append("INSERT INTO infomigracaohistoricoempresa(dataregistro, tipoinfo, tiposinfomigracaopadrao, empresa, padrao) \n");
                sqlHist.append("select \n");
                sqlHist.append("now() as dataregistro, \n");
                sqlHist.append(tipoInfoMigracaoEnum.getId()).append(" as tipoinfo, \n");
                sqlHist.append("coalesce(e.tiposinfomigracaopadrao,'') as tiposinfomigracaopadrao, \n");
                sqlHist.append("e.codigo as empresa, \n");
                if (tipoInfoMigracaoEnum.equals(TipoInfoMigracaoEnum.LISTA_PESSOAS)) {
                    sqlHist.append("(coalesce(e.tiposinfomigracaopadrao,'') ilike '%").append(TipoInfoMigracaoEnum.TELA_ALUNO.name()).append("%') as padrao \n");
                } else {
                    sqlHist.append("(coalesce(e.tiposinfomigracaopadrao,'') ilike '%").append(tipoInfoMigracaoEnum.name()).append("%') as padrao \n");
                }

                sqlHist.append("from empresa e \n");
                SuperFacadeJDBC.executarUpdate(sqlHist.toString(), con);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void incluirPagamentoItem(RemessaItemVO item) throws Exception {
        this.incluirPagamento(item);
    }

    public FormaPagamentoVO getFormaPagamentoBoletoBancario() {
        return formaPagamentoBoletoBancario;
    }

    public void setFormaPagamentoBoletoBancario(FormaPagamentoVO formaPagamentoBoletoBancario) {
        this.formaPagamentoBoletoBancario = formaPagamentoBoletoBancario;
    }

    public FormaPagamentoVO getFormaPagamentoCartaoCredito() {
        return formaPagamentoCartaoCredito;
    }

    public void setFormaPagamentoCartaoCredito(FormaPagamentoVO formaPagamentoCartaoCredito) {
        this.formaPagamentoCartaoCredito = formaPagamentoCartaoCredito;
    }

    private void gerarIdentificadoresListaRemessas(List<RemessaVO> listaRemessas) throws Exception {
        int j = 1;
        for (RemessaVO remessa : listaRemessas) {
            remessa.gerarIdentificador(con);
            if (!remessa.getTipo().equals(TipoRemessaEnum.BRADESCO_DCO)
                    && !remessa.getTipo().equals(TipoRemessaEnum.ITAU_DCO)) {

                remessa.setIdentificador(remessa.getTipo().equals(TipoRemessaEnum.GET_NET)
                        ? String.format(remessa.getIdentificador())
                        : String.format(remessa.getIdentificador() + "-%s-%s", j, listaRemessas.size()));
                //somente adicionar a informação de que a remessa foi dividida se ela for
                remessa.setNomeArquivo(listaRemessas.size() > 1 && !remessa.getTipo().equals(TipoRemessaEnum.GET_NET) ? String.format(remessa.getNomeArquivo()
                        + "-%s-%s", j, listaRemessas.size())
                        : remessa.getNomeArquivo());
            }
            j++;
        }
    }

    private List<RemessaItemVO> validarAdicionarItensRemessa(RemessaVO remessa, List<RemessaItemVO> listaItens, List<String> cartoes, boolean validarLimite, int limiteItens) throws Exception {
        List<RemessaItemVO> itensAutorizacoesIguais = new ArrayList<RemessaItemVO>();
        for (RemessaItemVO item : listaItens) {
            if (validarLimite && remessa.getListaItens().size() >= limiteItens) { // Isso só é válido quando o sistema tenta adicionar itens com autorizações iguais de outras lista anteriores
                itensAutorizacoesIguais.add(item);
                continue;
            }
            try {
                if (item.getAutorizacaoCobrancaVO().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                    if (cartoes.contains(item.getAutorizacaoCobrancaVO().getNumeroCartao())) {
                        itensAutorizacoesIguais.add(item);
                        continue;
                    } else {
                        cartoes.add(item.getAutorizacaoCobrancaVO().getNumeroCartao());
                    }
                }
                item.setTipo(remessa.getTipo());
                if (item.getRemessa() != null && item.getRemessa().getListaItens() != null) {
                    item.getRemessa().getListaItens().remove(item);
                }
                item.setRemessa(remessa);
                remessa.getListaItens().add(item);
            } catch (Exception ex) {
                Logger.getLogger(RemessaService.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return itensAutorizacoesIguais;
    }

    private List<MovParcelaVO> verificarAgruparParcelasPorPessoa(List<MovParcelaVO> listaParcelasGeral, ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO) {
        if (convenioCobrancaVO.isAgruparPorPessoaParcela()) {

            List<MovParcelaVO> listaParcelasAgrupada = new ArrayList<MovParcelaVO>();
            Map<Integer, List<MovParcelaVO>> mapaPessoaParcelas = new HashMap<Integer, List<MovParcelaVO>>();

            for (MovParcelaVO parcelaVO : listaParcelasGeral) {
                List<MovParcelaVO> parcelas = mapaPessoaParcelas.get(parcelaVO.getPessoa().getCodigo());
                if (parcelas == null) {
                    parcelas = new ArrayList<MovParcelaVO>();
                    parcelas.add(parcelaVO);
                } else {
                    parcelas.add(parcelaVO);
                }
                mapaPessoaParcelas.put(parcelaVO.getPessoa().getCodigo(), parcelas);
            }

            Set<Integer> keySet = mapaPessoaParcelas.keySet();
            for (Integer pessoa : keySet) {
                try {

                    List<MovParcelaVO> parcelas = mapaPessoaParcelas.get(pessoa);
                    if (!UteisValidacao.emptyList(parcelas) && parcelas.size() > 1) {

                        Double valorTotal = 0.0;
                        Double valorMulta = 0.0;
                        Double valorJuros = 0.0;
                        Date dataVencimento = Calendario.hoje();
                        for (MovParcelaVO movParcelaVO : parcelas) {
                            if (Calendario.maior(movParcelaVO.getDataVencimento(), dataVencimento)) {
                                dataVencimento = movParcelaVO.getDataVencimento();
                            }
                            valorTotal += movParcelaVO.getValorParcela();
                            valorMulta += movParcelaVO.getValorMulta();
                            valorJuros += movParcelaVO.getValorJuros();
                        }

                        List<MovParcelaVO> parcelaRenegociada = new ArrayList<MovParcelaVO>();
                        MovParcelaVO novaParcela = (MovParcelaVO) parcelas.get(0).getClone(true);
                        novaParcela.getPessoa().setCodigo(pessoa);
                        novaParcela.setDescricao("PARCELA RENEGOCIADA - REMESSA");
                        novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorTotal));
                        novaParcela.setValorMulta(Uteis.arredondarForcando2CasasDecimais(valorMulta));
                        novaParcela.setValorJuros(Uteis.arredondarForcando2CasasDecimais(valorJuros));
                        novaParcela.setDataRegistro(Calendario.hoje());
                        novaParcela.setDataCobranca(dataVencimento);
                        novaParcela.setDataVencimento(dataVencimento);
                        novaParcela.setResponsavel(usuarioVO);
                        novaParcela.setSituacao("EA");

                        parcelaRenegociada.add(novaParcela);

                        getFacade().getMovParcela().renegociarParcelas(parcelas, parcelaRenegociada, null, null, "", false, null, null, 0.0, false, usuarioVO, false, false, true, null, null);

                        listaParcelasAgrupada.addAll(parcelaRenegociada);
                    } else if (!UteisValidacao.emptyList(parcelas) && parcelas.size() == 1) {
                        listaParcelasAgrupada.addAll(parcelas);
                    }
                } catch (Exception ex) {
                    Uteis.logar(null, "agruparParcelasPorPessoa - Erro ao agrupar parcelas pessoa: " + pessoa + ": " + ex.getMessage());
                }
            }
            return listaParcelasAgrupada;
        } else {
            return listaParcelasGeral;
        }
    }

    private List<RemessaItemVO> validarParcelasGerarItensParaRemessas(List<MovParcelaVO> listaParcelas, ConvenioCobrancaVO convenio, boolean validarTiposIncompativeis, EmpresaVO empresaVO) throws Exception {
        List<RemessaItemVO> itensRemessa = new ArrayList<RemessaItemVO>();
        RemessaVO remessa = new RemessaVO();
        remessa.setTipo(convenio.getTipo().getTipoRemessa());
        remessa.setConvenioCobranca(convenio);
        if (UteisValidacao.emptyNumber(remessa.getEmpresa()) && empresaVO != null && !UteisValidacao.emptyNumber(empresaVO.getCodigo())) {
            remessa.setEmpresa(empresaVO.getCodigo());
        }
        remessa.setEmpresa(empresaVO.getCodigo());
        List<MovParcelaVO> parcelasComErro = new ArrayList<MovParcelaVO>();
        boolean causouErro = false;

        Map<String, NazgDTO> mapaAragorn = obterMapaAragorn(convenio, listaParcelas);

        for (MovParcelaVO parc : listaParcelas) {
            boolean achou = false;
            try {
                List<AutorizacaoCobrancaVO> autos = new ArrayList<AutorizacaoCobrancaVO>();
                autos.addAll(getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(parc.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                autos.addAll(getFacade().getAutorizacaoCobrancaColaborador().consultarPorPessoa(parc.getPessoa().getCodigo()));
                FORAUTO:
                for (AutorizacaoCobrancaVO auto : autos) {
                    boolean compativel;
                    //verifica se a autorização tem banco informado e o convenio tbm, depois compara os bancos
                    if (convenio.getBanco() != null && auto.getBanco() != null
                            && !UteisValidacao.emptyNumber(auto.getBanco().getCodigoBanco())
                            && !UteisValidacao.emptyNumber(convenio.getBanco().getCodigoBanco())) {
                        compativel = convenio.getBanco().getCodigoBanco().equals(auto.getBanco().getCodigoBanco());
                    } else {
                        compativel = Uteis.enumIn(convenio.getTipo(), auto.getTipoAutorizacao().getTiposConvenio());
                    }

                    if (compativel && empresaVO.isHabilitarReenvioAutomaticoRemessa() &&
                            !convenio.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCO)) {
                        compativel = auto.isBandeiraValidaParaTipoConvenioCobranca(convenio.getTipo());
                    }

                    if (compativel) {
                        parc.setTipoProdutos(getFacade().getMovParcela().consultaTiposProdutosMovParcela(parc.getCodigo()));
                        if (auto.isParcelaCompativel(parc, convenio.getEmpresa().isGerarRemessaContratoCancelado(), this.con)) {
                            achou = true;
                            RemessaItemVO item = new RemessaItemVO();
                            item.setTipo(remessa.getTipo());
                            item.setMovParcela(parc);
                            item.setPessoa(parc.getPessoa());
                            item.setRemessa(remessa);
                            item.setAutorizarDebito(auto.isAutorizarClienteDebito());
                            item.setValorItemRemessa(parc.getValorParcela() + parc.getValorMultaJuros());

                            item.setValorParcela(parc.getValorParcela());
                            item.setValorMulta(parc.getValorMulta());
                            item.setValorJuros(parc.getValorJuros());

                            RemessaItemMovParcelaVO itemMovParcelaVO = new RemessaItemMovParcelaVO();
                            itemMovParcelaVO.getMovParcelaVO().setCodigo(parc.getCodigo());
                            itemMovParcelaVO.getMovParcelaVO().setValorParcela(parc.getValorParcela());
                            itemMovParcelaVO.getMovParcelaVO().setPessoa(parc.getPessoa());
                            itemMovParcelaVO.setValorOriginal(parc.getValorParcela());
                            item.getMovParcelas().add(itemMovParcelaVO);

                            if (convenio.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

                                NazgDTO nazgDTO = mapaAragorn.get(auto.getTokenAragorn());
                                if (nazgDTO == null) {
                                    throw new Exception("Não foi possível obter o cartão. " + auto.getTokenAragorn());
                                }

                                auto.setNumeroCartao(nazgDTO.getCard());
                                auto.setNomeTitularCartao(nazgDTO.getName());
                                auto.setCpfTitular(nazgDTO.getCpf());
                                auto.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
                                auto.setValidadeCartao(Uteis.getValidadeMMYYYY(nazgDTO.getMonth(), nazgDTO.getYear(), true));
                                auto.preencherOperadoraCartao();
                                item.setNazgDTO(new NazgDTO(auto));

                                if (UteisValidacao.emptyString(auto.getNumeroCartao())) {
                                    throw new Exception("Número do cartão não informado! AutorizacaoCobrancaCliente " + auto.getCodigo());
                                }
                            }

                            auto.preencherRemessaItemSegundoAutorizacao(item, this.con);
                            item.setAutorizacaoCobrancaVO(auto);
                            itensRemessa.add(item);
                            break FORAUTO;
                        }
                    }
                }
            } catch (Exception ex) {
                Uteis.logar(null, "Preencher remessa - Erro ao processar parcelas " + parc.getCodigo() + " : " + ex.getMessage());
                if (validarTiposIncompativeis) {
                    throw new ConsistirException(String.format(" parcela: %s aluno: %s teve o seguinte problema : %s", parc, parc.getPessoa().getNome(), ex.getMessage()));
                }
            }
            if (validarTiposIncompativeis && !achou) {
                causouErro = true;
                parcelasComErro.add(parc);
            }
        }

        if (causouErro) {
            GestaoRemessasControle gestaoRemessasControle = (GestaoRemessasControle) JSFUtilities.getManagedBean("GestaoRemessasControle");
            gestaoRemessasControle.setListaRemessaComErro(parcelasComErro);
            throw new ConsistirException("Não possui autorização compatível com a parcela. Verifique o Cadastro de Autorizações de Cobrança.");
        }

        return itensRemessa;
    }


    private Map<String, NazgDTO> obterMapaAragorn(ConvenioCobrancaVO convenio, List<MovParcelaVO> listaParcelas) throws Exception {

        if (!convenio.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) ||
                UteisValidacao.emptyList(listaParcelas)) {
            return new HashMap<>();
        }

        List<Integer> pessoas = new ArrayList<>();
        for (MovParcelaVO parc : listaParcelas) {
            pessoas.add(parc.getPessoa().getCodigo());
        }

        List<String> tokens = getFacade().getAutorizacaoCobrancaCliente().obterTokensAragornParaRemessa(pessoas);

        AragornService aragornService = new AragornService();
        Map<String, NazgDTO> mapaAragorn = aragornService.obterMapaNazg(tokens);
        aragornService = null;
        return mapaAragorn;
    }

    public String processarRetornoBoletoServlet(final String nomeArquivo, final String arquivo, final String codigoItens,
                                                final String identificadorEmpresa, final String userSolicitante, final String empresaSolicitante,
                                                final String tipoConvenio, final String bancoConvenio){
        try {
            RemessaVO retornoBoleto = new RemessaVO();
            StringBuilder header = new StringBuilder(arquivo);
            retornoBoleto.setRetorno(header);
            LayoutBoletoPadrao.lerRetorno(retornoBoleto);
            String dataPrevistaCreditoValor = retornoBoleto.getHeaderRetorno().getValue(DCCAttEnum.DataPrevistaCredito.name());
            Date dataPrevistaCredito = Calendario.getDate("ddMMyy", dataPrevistaCreditoValor);

            String quantidadeDeItensValor = retornoBoleto.getTrailerRetorno().getValue(DCCAttEnum.QuantidadeRegistros.name());
            Integer quantidadeDeItens = Integer.parseInt(quantidadeDeItensValor);

            String valorTotalBruto = retornoBoleto.getTrailerRetorno().getValue(DCCAttEnum.ValorTotalBruto.name());
            Double valorBruto = new Double(valorTotalBruto.substring(0, valorTotalBruto.length() - 2) + "." + valorTotalBruto.substring(valorTotalBruto.length() - 2));
            RetornoRemessaVO retornoRemessa = obterRetornoRemessaServlet(nomeArquivo, arquivo, empresaSolicitante, userSolicitante, valorBruto, quantidadeDeItens, dataPrevistaCredito);
            if (retornoRemessa.getDataProcessamento() == null) {
                retornoRemessa.setDataProcessamento(Calendario.hoje());
            }
            retornoRemessa.setDataUltimoProcessamento(Calendario.hoje());
            retornoRemessa.limpar();

            TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum = TipoConvenioCobrancaEnum.valueOf(tipoConvenio);
            ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorTipoEBanco(tipoConvenioCobrancaEnum, bancoConvenio);
            if (convenioCobrancaVO == null) {
                throw new Exception("Convênio de Cobrança equivalente não encontrado");
            }

            List<RemessaItemVO> itensProcessar = getFacade().getRemessaItem().consultarIdentificadores(codigoItens, convenioCobrancaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Map<Integer, EmpresaVO> mapaEmpresas = new HashMap<Integer, EmpresaVO>();
            Map<Long, RemessaItemVO> mapaItens = new HashMap<Long, RemessaItemVO>();
            for (RemessaItemVO item : itensProcessar) {
                mapaItens.put(Long.valueOf(item.getIdentificador()), item);
            }
            processarRetornoBoleto(retornoBoleto, mapaItens, retornoRemessa, mapaEmpresas, identificadorEmpresa);

            return "sucesso";
        } catch (Exception e) {
            return "Erro: " + e.getMessage();
        }
    }

    private RetornoRemessaVO obterRetornoRemessaServlet(final String nomeArquivo, final String arquivoRetorno, final String empresaProcessou, final String userProcessou, final Double valorBruto, final Integer qtItens, final Date dataPrevista) throws Exception {
        RetornoRemessaVO retornoRemessaVO = new RetornoRemessaVO();
        retornoRemessaVO.setNomeArquivo(nomeArquivo);
        retornoRemessaVO = getFacade().getRetornoRemessa().consultarPorNomeArquivo(retornoRemessaVO.getNomeArquivo());
        if (retornoRemessaVO == null || retornoRemessaVO.getCodigo() == null || retornoRemessaVO.getCodigo() <= 0) {
            retornoRemessaVO = new RetornoRemessaVO();
            retornoRemessaVO.setNomeArquivo(nomeArquivo);
            retornoRemessaVO.setUsuarioVO(getFacade().getUsuario().getUsuarioRecorrencia());
            retornoRemessaVO.setArquivoRetorno(arquivoRetorno);
            retornoRemessaVO.setDataPrevistaCredito(dataPrevista);
            retornoRemessaVO.setQuantidadeDeItens(qtItens);
            retornoRemessaVO.setValorTotalDoArquivo(valorBruto);
            retornoRemessaVO.setUserSolicitante(userProcessou);
            retornoRemessaVO.setEmpresaSolicitante(empresaProcessou);
            getFacade().getRetornoRemessa().incluir(retornoRemessaVO);
        } else {
            retornoRemessaVO.setUserSolicitante(userProcessou);
            retornoRemessaVO.setEmpresaSolicitante(empresaProcessou);
            if (!UteisValidacao.emptyString(arquivoRetorno) && (retornoRemessaVO.getArquivoRetorno() == null || retornoRemessaVO.getArquivoRetorno().length() == 0)) {
                retornoRemessaVO.setArquivoRetorno(arquivoRetorno);
            }
            getFacade().getRetornoRemessa().alterar(retornoRemessaVO, true);
        }

        return retornoRemessaVO;
    }

    public List<FormaPagamentoVO> getListaFormasPagamentoComConvenio() {
        return listaFormasPagamentoComConvenio;
    }

    public void setListaFormasPagamentoComConvenio(List<FormaPagamentoVO> listaFormasPagamentoComConvenio) {
        this.listaFormasPagamentoComConvenio = listaFormasPagamentoComConvenio;
    }

    private void realizarBaixaAutomaticaRetornoDCO(ConvenioCobrancaVO conv) {
        Uteis.logar(null, "RealizarBaixaAutomaticaRetorno DCO -> Iniciando Processamento de Retorno das Remessas de Cobrança DCO...");
        try {
            //baixar Arquivos de Retorno Remotos e efetuar a Baixa Automática
//            List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory("C:\\PactoJ\\retorno");
            List<Map<String, File>> arquivos = RetornoService.receberRetornosDCO(conv);
            if (!UteisValidacao.emptyList(arquivos)) {
                Uteis.logar(null, "# Vou processar retorno dos arquivos remotos do convênio: " + conv.getDescricao());
                processarArquivosRetornoRemotoDCO(arquivos, conv);
                Uteis.logar(null, "# retorno dos arquivos do convênio: " + conv.getDescricao() + " processado!");
            }
        } catch (Exception e) {
            Uteis.logar(null, "# ERRO ao processar Remessas: " + e.getMessage());
        }
        Uteis.logar(null, "RealizarBaixaAutomaticaRetorno DCO -> Finalizando Processamento!");
    }

    public void preencherRemessaDoRetorno(ProcessoRetornoRemessaTO processoRetornoRemessaTO, ConvenioCobrancaVO convenio, EmpresaVO empresaVO) throws Exception {
        processoRetornoRemessaTO.setItensProcessar(new ArrayList<RemessaItemVO>());
        processoRetornoRemessaTO.setItensProcessarBoleto(new ArrayList<>());
        processoRetornoRemessaTO.setRemessaRetorno(new RemessaVO());
        processoRetornoRemessaTO.setRetornoBoleto(new RemessaVO());
        if (convenio == null || convenio.getCodigo() == 0) {
            throw new ConsistirException("Selecione um Convênio de Cobrança. Para esta operação, não pode ser todos.");
        }

        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
        ConvenioCobrancaVO conv = convenioCobrancaDAO.consultarPorCodigoEmpresa(convenio.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        convenioCobrancaDAO = null;

        List<RemessaVO> remessas;
        processoRetornoRemessaTO.getRetornoBoleto().setConvenioCobranca(conv);
        if (conv.isLayoutFebrabanDCO()) {
            processoRetornoRemessaTO.getRemessaRetorno().setConvenioCobranca(convenio);
            processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());

            LayoutRemessaBase.lerRetorno(processoRetornoRemessaTO.getRemessaRetorno());

            validarBancoArquivo(processoRetornoRemessaTO, convenio);

            int quantidadeAutorizacoes = 0;
            int quantidadeConfirmacoes = 0;
            int quantidadeRetornos = 0;
            for (RegistroRemessa registroRemessa : processoRetornoRemessaTO.getRemessaRetorno().getDetailsRetorno()) {
                if (registroRemessa.getTipo().equals(TipoRegistroEnum.DETALHE_REGISTRO_B)) {
                    quantidadeAutorizacoes++;
                } else if (registroRemessa.getTipo().equals(TipoRegistroEnum.DETALHE_REGISTRO_J)) {
                    quantidadeConfirmacoes++;
                } else if (registroRemessa.getTipo().equals(TipoRegistroEnum.DETALHE)) {
                    quantidadeRetornos++;
                }
            }

            processoRetornoRemessaTO.setQuantidadeAutorizacoes(quantidadeAutorizacoes);
            processoRetornoRemessaTO.setQuantidadeConfirmacoes(quantidadeConfirmacoes);
            processoRetornoRemessaTO.setQuantidadeItensReconhecidos(quantidadeRetornos);
            processoRetornoRemessaTO.setQuantidadeDeItens(quantidadeAutorizacoes + quantidadeConfirmacoes + quantidadeRetornos);

            String parcelas;
            if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_SANTANDER)) {
                parcelas = LayoutRemessaSantanderDCO.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
            }else if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                parcelas = LayoutRemessaCaixaSIACC150DCO.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
            }else {
                parcelas = LayoutRemessaFebrabanDCO.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
            }


            if (!UteisValidacao.emptyString(parcelas)) {
                RemessaItem remessaItemDAO = new RemessaItem(con);
                processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarPorParcelas(parcelas, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                remessaItemDAO = null;
            }
        } else {
            //diferente pra CAIXA
            if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                String parcelas = LayoutRemessaCaixaSIACC150DCO.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
                if (!UteisValidacao.emptyString(parcelas)) {
                    String sql;
                    sql = "select r.* from remessa r "
                            + " where ARRAY_TO_STRING("
                            + "ARRAY(select movparcela from remessaitem where remessa = r.codigo order by movparcela), ',') like '" + parcelas
                            + "' ORDER BY r.dataregistro DESC LIMIT 1";
                    ResultSet criarConsulta = ZillyonWebFacade.criarConsulta(sql, con);
                    remessas = Remessa.montarDadosConsulta(criarConsulta, con);
                } else {
                    throw new ConsistirException("Não foram encontradas remessas para o arquivo informado");
                }
            } else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) { //diferente pro BRADESCO 240
                String codigoRemessaItens = LayoutRemessaBradescoCNAB240.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
                if (!UteisValidacao.emptyString(codigoRemessaItens)) {
                    processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaBradescoCNAB240.lerRetorno(processoRetornoRemessaTO.getRemessaRetorno());
                    RemessaItem remessaItemDAO = new RemessaItem(con);
                    processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarCodigosPorConvenio(codigoRemessaItens, conv.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    remessaItemDAO = null;
                    return;
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }
            } else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_ITAU)) { //diferente pro ITAU
                processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaItauDCO.lerRetorno(processoRetornoRemessaTO.getRemessaRetorno());

                validarBancoArquivo(processoRetornoRemessaTO, convenio);

                String parcelas = LayoutRemessaItauDCO.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
                if (!UteisValidacao.emptyString(parcelas)) {
                    processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaItauDCO.lerRetorno(processoRetornoRemessaTO.getRemessaRetorno());
                    RemessaItem remessaItemDAO = new RemessaItem(con);
                    processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarPorParcelasConvenioCobranca(parcelas, conv.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    remessaItemDAO = null;
                    return;
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }
            } else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU)) {//modelo utilizado pela Stella Torreao
                String parcelas = LayoutRemessaItauBoletoCNAB400.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
                if (!UteisValidacao.emptyString(parcelas)) {
                    processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaItauBoletoCNAB400.lerRetorno(processoRetornoRemessaTO.getRemessaRetorno(), conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU));
                    RemessaItem remessaItemDAO = new RemessaItem(con);
                    processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarPorParcelas(parcelas, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    remessaItemDAO = null;
                    if (processoRetornoRemessaTO.getItensProcessar().size() == 0) {
                        throw new ConsistirException("Erro: Nenhuma parcela foi encontrada para este retorno.");
                    }
                    return;
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }

            }else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.ITAU)) {
                String itens = LayoutRemessaItauCNAB400BoletoAtualizado.obterCodigosMovParcelas(processoRetornoRemessaTO.getHeadRetorno());
                if (!UteisValidacao.emptyString(itens)) {
                    processoRetornoRemessaTO.setRemessaVO(processoRetornoRemessaTO.getRetornoBoleto());
                    processoRetornoRemessaTO.getRetornoBoleto().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaItauCNAB400BoletoAtualizado.lerRetorno(processoRetornoRemessaTO.getRetornoBoleto());
                    RemessaItem remessaItemDAO = new RemessaItem(con);
                    processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarCodigos(itens, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    remessaItemDAO = null;
                    if (processoRetornoRemessaTO.getItensProcessar().size() == 0) {
                        throw new ConsistirException("Erro: Nenhuma parcela foi encontrada para este retorno.");
                    }
                    preencherInformacoesRetorno(processoRetornoRemessaTO, null);
                    return;
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }
            }else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
                String identificadorBoleto = LayoutRemessaItauCNAB400BoletoAtualizado.obterCodigosNossoNumero(processoRetornoRemessaTO.getHeadRetorno());
                if (!UteisValidacao.emptyString(identificadorBoleto)) {
                    processoRetornoRemessaTO.setRemessaVO(processoRetornoRemessaTO.getRetornoBoleto());
                    processoRetornoRemessaTO.getRetornoBoleto().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaItauCNAB400BoletoAtualizado.lerRetorno(processoRetornoRemessaTO.getRetornoBoleto());
                    Boleto boletoDAO = new Boleto(con);
                    processoRetornoRemessaTO.setItensProcessarBoleto(boletoDAO.consultarPorIdentificador(identificadorBoleto, convenio.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
                    boletoDAO = null;
                    if (UteisValidacao.emptyList(processoRetornoRemessaTO.getItensProcessarBoleto())) {
                        throw new ConsistirException("Erro: Nenhum boleto foi encontrado para este retorno.");
                    }
                    preencherInformacoesRetorno(processoRetornoRemessaTO, null);
                    return;
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }

            } else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)) {
                String parcelas = LayoutBoletoPadrao.obterCodigosRemessaItem(processoRetornoRemessaTO.getHeadRetorno(), conv);
                if (!UteisValidacao.emptyString(parcelas)) {
                    processoRetornoRemessaTO.setRemessaVO(processoRetornoRemessaTO.getRetornoBoleto());
                    processoRetornoRemessaTO.getRetornoBoleto().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaDaycovalBoletoCNAB400.lerRetorno(processoRetornoRemessaTO.getRetornoBoleto());
                    processoRetornoRemessaTO.getRemessaRetorno().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                    LayoutRemessaDaycovalBoletoCNAB400.lerRetorno(processoRetornoRemessaTO.getRemessaRetorno());
                    RemessaItem remessaItemDAO = new RemessaItem(con);
                    processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarCodigos(parcelas, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    remessaItemDAO = null;
                    if (processoRetornoRemessaTO.getItensProcessar().size() == 0) {
                        throw new ConsistirException("msg_err_nenhuma_parc_retorno");
                    }
                    preencherInformacoesRetorno(processoRetornoRemessaTO, null);
                    return;
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }

            } else if (conv.getTipo() != null && conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO)) {
                if(processoRetornoRemessaTO.getIdentificadorEmpresaFinaceiro().equals("")){
                    String codRemessaItem = LayoutBoletoPadrao.obterCodigosRemessaItem(processoRetornoRemessaTO.getHeadRetorno(),conv);
                    if (!UteisValidacao.emptyString(codRemessaItem)) {
                        processoRetornoRemessaTO.setRemessaVO(processoRetornoRemessaTO.getRetornoBoleto());
                        processoRetornoRemessaTO.getRetornoBoleto().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                        LayoutBoletoPadrao.lerRetorno(processoRetornoRemessaTO.getRetornoBoleto());
                        if (!convenio.validarArquivoRetorno(processoRetornoRemessaTO.getRetornoBoleto())) {
                            throw new ConsistirException("Erro: Arquivo incompatível com a Conta Corrente/Agência do Convênio Selecionado.");
                        }
                        RemessaItem remessaItemDAO = new RemessaItem(con);
                        if (conv.isUsarIdentificador()) {
                            processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarIdentificadores(codRemessaItem, conv.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                            // Cliente estava gerando arquivos de retorno do Sicoob, com informações duplicadas de Boleto que já foi pago
                            // Como o dado vinha em arquivos diferentes, sistema aceitava e gerava saldo de Conta Correte, pois a baxa já tinha acontecido no envio anterior
                            // Por isso, a criação desse código, para não tentar dar baixa novamente em Parcela que de RemessaItem que já tem retorno 06 - Liquidado
                            // Adicionado o máximo de validação, para só entrar aqui no caso especifico desse cliente e surgindo novos clientes reclamando, ai vamos adaptando
                            if (
                                    convenio.getTipoRemessa() != null
                                    && convenio.getTipoRemessa().getArquivoLayoutRemessa() != null
                                    && convenio.getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)
                                    && !UteisValidacao.emptyList(processoRetornoRemessaTO.getItensProcessar())
                            ) {
                                List itensProcessarVerificados = new ArrayList();
                                for (RemessaItemVO remessaItemVO: processoRetornoRemessaTO.getItensProcessar()) {
                                    String situacaoBoletoNoBancoDados = remessaItemVO.getCodigoStatus();
                                    if (!situacaoBoletoNoBancoDados.equals(Cnab240SicoobStatusEnum.Status06.getId())) {
                                        itensProcessarVerificados.add(remessaItemVO);
                                    }
                                }
                                processoRetornoRemessaTO.setItensProcessar(itensProcessarVerificados);
                            }
                        } else {
                            processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarCodigos(codRemessaItem, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        }
                        remessaItemDAO = null;

                        if (processoRetornoRemessaTO.getItensProcessar().size() == 0) {
                            throw new ConsistirException("Erro: Nenhum boleto foi encontrado no sistema para este retorno.");
                        }
                        preencherInformacoesRetorno(processoRetornoRemessaTO, null);
                        return;
                    } else {
                        throw new ConsistirException("Erro: Nenhum boleto foi encontrado no sistema para este retorno.");
                    }
                } else {
                    //GTC: Sempre que for rede de empresa, irá usar identificar
                    processoRetornoRemessaTO.setMapaItemEmpresa(LayoutBoletoPadrao.obterCodigosItemPorEmpresas(processoRetornoRemessaTO.getHeadRetorno(), processoRetornoRemessaTO.getEmpresaFinanceiroVOs()));
                    if(!processoRetornoRemessaTO.getMapaItemEmpresa().isEmpty()){
                        processoRetornoRemessaTO.setRemessaVO(processoRetornoRemessaTO.getRetornoBoleto());
                        processoRetornoRemessaTO.getRetornoBoleto().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                        LayoutBoletoPadrao.lerRetorno(processoRetornoRemessaTO.getRetornoBoleto());
                        for (Map.Entry<EmpresaFinanceiroVO, String> entrySet : processoRetornoRemessaTO.getMapaItemEmpresa().entrySet()) {
                            if( entrySet.getKey().getCodigo().equals(processoRetornoRemessaTO.getEmpresaFinaceiro().getCodigo())){
                                RemessaItem remessaItemDAO = new RemessaItem(con);
                                processoRetornoRemessaTO.setItensProcessar(remessaItemDAO.consultarIdentificadores(entrySet.getValue(), conv.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                                remessaItemDAO = null;
                                break;
                            }
                        }
                        preencherInformacoesRetorno(processoRetornoRemessaTO, processoRetornoRemessaTO.getMapaItemEmpresa());
                        return;
                    } else {
                        throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                    }
                }
            } else { //demais BANCOS
                RegistroRemessa regHead = LayoutRemessaBase.obterHeaderRetorno(processoRetornoRemessaTO.getHeadRetorno(), conv.getTipo());
                if (regHead != null) {
                    String sql;
                    sql = String.format("select * from remessa "
                                    + "where %s  (head like ('%%%s%%') and head like ('%%%s%%'))",
                            (UteisValidacao.emptyNumber(empresaVO.getCodigo()) ? "" : "empresa = "+empresaVO.getCodigo()+ " AND "),
                            regHead.get(DCCAttEnum.NumeroEstabelecimento.name()),
                            regHead.get(conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)
                                    ? DCCAttEnum.SequencialRegistro.name() : DCCAttEnum.ReservadoEstabelecimento.name()));
                    ResultSet criarConsulta = ZillyonWebFacade.criarConsulta(sql, con);
                    remessas = Remessa.montarDadosConsulta(criarConsulta, con);
                } else {
                    throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
                }
            }

            if (remessas.size() == 1) {
                processoRetornoRemessaTO.setRemessaVO(remessas.get(0));
                processoRetornoRemessaTO.getRemessaVO().setRetorno(processoRetornoRemessaTO.getHeadRetorno());
                if (processoRetornoRemessaTO.getRemessaVO().isCancelamento()) {
                    LayoutRemessaBase.lerRetornoCancelamento(processoRetornoRemessaTO.getRemessaVO());
                    getL().lerHeaderETrailerRemessa(processoRetornoRemessaTO.getRemessaVO());
                } else {
                    getL().lerHeaderETrailerRemessa(processoRetornoRemessaTO.getRemessaVO());
                    LayoutRemessaBase.lerRetorno(processoRetornoRemessaTO.getRemessaVO());
                }
            } else if (remessas.size() > 1) {
                throw new ConsistirException("Erro: Mais de uma Remessa compatível com este Retorno foi encontrada");
            } else {
                throw new ConsistirException("Erro: Nenhuma Remessa encontrada que seja compatível com este Retorno");
            }
        }
    }

    private void validarBancoArquivo(ProcessoRetornoRemessaTO processoRetornoRemessaTO, ConvenioCobrancaVO convenio) throws ConsistirException {
        boolean arquivoCorreto = true;
        if (processoRetornoRemessaTO.getRemessaRetorno().getHeaderRetorno().getAtributos().isEmpty()) {
            throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
        } else {

            String codBanco = processoRetornoRemessaTO.getRemessaRetorno().getHeaderRetorno().getValue(DCCAttEnum.CodigoBanco.name());
            try {
                if (Integer.parseInt(codBanco) != convenio.getBanco().getCodigoBanco()) {
                    arquivoCorreto = false;
                }
            } catch (Exception ignored) {

            }
        }

        if (!arquivoCorreto) {
            throw new ConsistirException("Erro: Header de Arquivo incompatível com o Tipo de Convênio Selecionado -> " + convenio.getTipo().getDescricao());
        }
    }

    private void preencherInformacoesRetorno(ProcessoRetornoRemessaTO processoRetornoRemessaTO, Map<EmpresaFinanceiroVO, String> empresasItens) {
        tratarDataPrevistaCredito(processoRetornoRemessaTO);

        String quantidadeDeItensValor = processoRetornoRemessaTO.getRemessaVO().getTrailerRetorno().getValue(DCCAttEnum.QuantidadeRegistros.name());
        //Validacao necessária devido ao trailler de retorno CNAB400Santander não exister está informação
        if (processoRetornoRemessaTO.getRemessaVO().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            processoRetornoRemessaTO.setQuantidadeDeItens(processoRetornoRemessaTO.getItensProcessar().size());
            processoRetornoRemessaTO.setValorTotalDoArquivo(0.0);
        } else {
            if (!UteisValidacao.emptyString(quantidadeDeItensValor)) {
                processoRetornoRemessaTO.setQuantidadeDeItens(Integer.parseInt(quantidadeDeItensValor));
            }

            String valorTotalBruto = processoRetornoRemessaTO.getRemessaVO().getTrailerRetorno().getValue(DCCAttEnum.ValorTotalBruto.name());
            valorTotalBruto = valorTotalBruto.trim();
            if(!UteisValidacao.emptyString(valorTotalBruto)) {
                processoRetornoRemessaTO.setValorTotalDoArquivo(new Double(valorTotalBruto.substring(0, valorTotalBruto.length() - 2) + "." + valorTotalBruto.substring(valorTotalBruto.length() - 2)));
            }
        }

        processarDetail(processoRetornoRemessaTO, empresasItens);
    }

    private void tratarDataPrevistaCredito(ProcessoRetornoRemessaTO processoRetornoRemessaTO) {
        processoRetornoRemessaTO.setDataPrevistaCredito(null);
        String dataPrevistaCreditoValor = processoRetornoRemessaTO.getRemessaVO().getHeaderRetorno().getValue(DCCAttEnum.DataPrevistaCredito.name());
        //Validacao necessária devido ao trailler de retorno CNAB400Santander não exister está informação
        if(!UteisValidacao.emptyString(dataPrevistaCreditoValor)) {
            try {
                Long dataPrevistaCreditoLong = Long.parseLong(dataPrevistaCreditoValor);
                if (dataPrevistaCreditoLong > 0) {
                    processoRetornoRemessaTO.setDataPrevistaCredito(Calendario.getDate("ddMMyy", dataPrevistaCreditoValor));
                }
            } catch (Exception ignored) {

            }
        }
    }

    private void processarDetail(ProcessoRetornoRemessaTO processoRetornoRemessaTO, Map<EmpresaFinanceiroVO, String> empresasItens) {
        double valorASerBaixado = 0.0;
        String codigoRetorno = processoRetornoRemessaTO.getRemessaVO().getHeaderRetorno().getValue(DCCAttEnum.NumAvisoBancario.name());
        for (RegistroRemessa detail : processoRetornoRemessaTO.getRemessaVO().getDetailsRetorno()) {
            if(!processoRetornoRemessaTO.getIdentificadorEmpresaFinaceiro().equals("") && !detail.getValue(DCCAttEnum.NossoNumero.name()).startsWith(processoRetornoRemessaTO.getIdentificadorEmpresaFinaceiro())){
                continue;
            }

            String statusVenda =  detail.getValue(DCCAttEnum.StatusVenda.name());
            String valorPagoProcessar = detail.getValue(DCCAttEnum.ValorPago.name());
            double valorPago = new Double(valorPagoProcessar.substring(0, valorPagoProcessar.length() - 2) + "." + valorPagoProcessar.substring(valorPagoProcessar.length() - 2));

            String valorVendaProcessar = detail.getValue(DCCAttEnum.ValorVenda.name());
            double valorVenda = new Double(valorVendaProcessar.substring(0, valorVendaProcessar.length() - 2) + "." + valorVendaProcessar.substring(valorVendaProcessar.length() - 2));

            boolean encontrou = false;

            String nossoNumeroTmp = detail.getValue(DCCAttEnum.NossoNumero.name()).trim();
            if (UteisValidacao.emptyString(nossoNumeroTmp)) {
                incrementarNaoReconhecido(processoRetornoRemessaTO, valorVenda);
                continue;
            }

            Long nossoNumero = 0L;
            if (processoRetornoRemessaTO.getRemessaVO().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())
                || processoRetornoRemessaTO.getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL)) {
                nossoNumero = Long.valueOf(nossoNumeroTmp);
            } else {
                nossoNumero = Long.valueOf(nossoNumeroTmp.substring(3));
            }

            if (processoRetornoRemessaTO.getRemessaVO().getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_ITAU_ONLINE)) {
                for (BoletoVO boletoVO : processoRetornoRemessaTO.getItensProcessarBoleto()) {
                    Long boletoCodigo = Long.valueOf(boletoVO.getCodigo());
                    if (boletoCodigo.equals(nossoNumero) && statusVenda.equals(BBCnab400ItauStatusEnum.Status06.getId())) {
                        encontrou = true;
                        processoRetornoRemessaTO.setQuantidadeItensReconhecidos(processoRetornoRemessaTO.getQuantidadeItensReconhecidos() + 1);
                        valorASerBaixado += valorPago;
                        valorVenda = boletoVO.getValor();
                        if (!UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                            continue;
                        }
                        if (Uteis.arredondarForcando2CasasDecimais(valorPago) < Uteis.arredondarForcando2CasasDecimais(valorVenda)) {
                            processoRetornoRemessaTO.setQuantidadeItensBaixaValorMenor(processoRetornoRemessaTO.getQuantidadeItensBaixaValorMenor() + 1);
                            boletoVO.setTipoBaixa(TipoBaixaEnum.MENOR_VALOR);
                        } else if (Uteis.arredondarForcando2CasasDecimais(valorPago) > Uteis.arredondarForcando2CasasDecimais(valorVenda)) {
                            processoRetornoRemessaTO.setQuantidadeItensBaixaValorMaior(processoRetornoRemessaTO.getQuantidadeItensBaixaValorMaior() + 1);
                            boletoVO.setTipoBaixa(TipoBaixaEnum.MAIOR_VALOR);
                        } else if (Uteis.arredondarForcando2CasasDecimais(valorPago) == Uteis.arredondarForcando2CasasDecimais(valorVenda)) {
                            if (boletoVO.getCodigosRetorno().isEmpty()) {
                                processoRetornoRemessaTO.setQuantidadeItensBaixaNormal(processoRetornoRemessaTO.getQuantidadeItensBaixaNormal() + 1);
                                boletoVO.setTipoBaixa(TipoBaixaEnum.NORMAL);
                            } else if (!boletoVO.processouRetorno(codigoRetorno)) {
                                processoRetornoRemessaTO.setQuantidadeItensBaixaEmDuplicidade(processoRetornoRemessaTO.getQuantidadeItensBaixaEmDuplicidade() + 1);
                                boletoVO.setTipoBaixa(TipoBaixaEnum.DUPLICIDADE);
                            }
                        }
                    } else if (boletoCodigo.equals(nossoNumero) && statusVenda.equals(BBCnab400ItauStatusEnum.Status02.getId())) {
                        encontrou = true;
                        processoRetornoRemessaTO.setQuantidadeItensReconhecidos(processoRetornoRemessaTO.getQuantidadeItensReconhecidos() + 1);
                        processoRetornoRemessaTO.setQuantidadeItensRegistrados(processoRetornoRemessaTO.getQuantidadeItensRegistrados() + 1);
                    }
                }
            } else {
                for (RemessaItemVO remessaItemVO : processoRetornoRemessaTO.getItensProcessar()) {
                    Long remessaItemCodigo = Long.valueOf(remessaItemVO.getCodigo());
                    if (remessaItemVO.getRemessa().getConvenioCobranca().isUsarIdentificador() || !processoRetornoRemessaTO.getIdentificadorEmpresaFinaceiro().equals("")) {
                        remessaItemCodigo = Long.valueOf(remessaItemVO.getIdentificador());
                    }

                    if (remessaItemCodigo.equals(nossoNumero) && remessaItemVO.isLiquidado(statusVenda)) {
                        encontrou = true;
                        processoRetornoRemessaTO.setQuantidadeItensReconhecidos(processoRetornoRemessaTO.getQuantidadeItensReconhecidos() + 1);
                        valorASerBaixado += valorPago;
                        valorVenda = remessaItemVO.getValorBoleto();
                        if (remessaItemVO.getMovPagamento() != null && remessaItemVO.getMovPagamento().getCodigo() > 0) {
                            continue;
                        }
                        if (Uteis.arredondarForcando2CasasDecimais(valorPago) < Uteis.arredondarForcando2CasasDecimais(valorVenda)) {
                            processoRetornoRemessaTO.setQuantidadeItensBaixaValorMenor(processoRetornoRemessaTO.getQuantidadeItensBaixaValorMenor() + 1);
                            remessaItemVO.setTipoBaixa(TipoBaixaEnum.MENOR_VALOR);
                        } else if (Uteis.arredondarForcando2CasasDecimais(valorPago) > Uteis.arredondarForcando2CasasDecimais(valorVenda)) {
                            processoRetornoRemessaTO.setQuantidadeItensBaixaValorMaior(processoRetornoRemessaTO.getQuantidadeItensBaixaValorMaior() + 1);
                            remessaItemVO.setTipoBaixa(TipoBaixaEnum.MAIOR_VALOR);
                        } else if (Uteis.arredondarForcando2CasasDecimais(valorPago) == Uteis.arredondarForcando2CasasDecimais(valorVenda)) {
                            if (remessaItemVO.getCodigosRetorno().isEmpty()) {
                                processoRetornoRemessaTO.setQuantidadeItensBaixaNormal(processoRetornoRemessaTO.getQuantidadeItensBaixaNormal() + 1);
                                remessaItemVO.setTipoBaixa(TipoBaixaEnum.NORMAL);
                            } else if (!remessaItemVO.processouRetorno(codigoRetorno)) {
                                processoRetornoRemessaTO.setQuantidadeItensBaixaEmDuplicidade(processoRetornoRemessaTO.getQuantidadeItensBaixaEmDuplicidade() + 1);
                                remessaItemVO.setTipoBaixa(TipoBaixaEnum.DUPLICIDADE);
                            }
                        }
                    } else if (remessaItemCodigo.equals(nossoNumero) && remessaItemVO.isRegistroAceito(statusVenda)) {
                        encontrou = true;
                        processoRetornoRemessaTO.setQuantidadeItensReconhecidos(processoRetornoRemessaTO.getQuantidadeItensReconhecidos() + 1);
                        processoRetornoRemessaTO.setQuantidadeItensRegistrados(processoRetornoRemessaTO.getQuantidadeItensRegistrados() + 1);
                    }
                }
            }
            if (!encontrou) {
                incrementarNaoReconhecido(processoRetornoRemessaTO, valorVenda);
            }
            if(processoRetornoRemessaTO.getRemessaVO().getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())){
                processoRetornoRemessaTO.setValorTotalDoArquivo(processoRetornoRemessaTO.getValorTotalDoArquivo() + valorVenda);
            }
        }
        processoRetornoRemessaTO.setItensOutrasEmpresa(new ArrayList<GenericoTO>());
        if(empresasItens != null){
            for(Map.Entry<EmpresaFinanceiroVO, String> pair: empresasItens.entrySet()){
                if(pair.getKey().getCodigo().equals(processoRetornoRemessaTO.getEmpresaFinaceiro().getCodigo())){
                    continue;
                }
                GenericoTO generico = new GenericoTO(pair.getKey().getCodigo(), "");
                generico.setCodigo(pair.getKey().getCodigo());
                generico.setValor(1.0 + (pair.getValue().length() - pair.getValue().replace(",", "").length()));//pega a quantidade de item de uma empresa
                generico.setCodigoString(pair.getKey().getNomeFantasia());
                if(pair.getKey().getCodigo() == 0){
                    generico.setLabel("Entre em contato com a Pacto para resolver isso");
                    generico.setSelecionado(false);
                }
                processoRetornoRemessaTO.getItensOutrasEmpresa().add(generico);
            }
        }
        processoRetornoRemessaTO.setValorTotalASerBaixado(valorASerBaixado);
    }

    private void incrementarNaoReconhecido(ProcessoRetornoRemessaTO processoRetornoRemessaTO, double valorVenda) {
        processoRetornoRemessaTO.setQuantidadeItensNaoReconhecido(processoRetornoRemessaTO.getQuantidadeItensNaoReconhecido() + 1);
        processoRetornoRemessaTO.setValorTotalNaoReconhecido(processoRetornoRemessaTO.getValorTotalNaoReconhecido() + valorVenda);
    }

    private void processarArquivosRetornoRemotoDCO(final List<Map<String, File>> arquivos,
                                                   final ConvenioCobrancaVO conv) {

        for (Map<String, File> entrada : arquivos) {
            Set<String> s = entrada.keySet();
            FORARQ:
            for (String fileName : s) {
                File f = entrada.get(fileName);
                try {
                    if (f.length() > 0) {

                        String nomeArquivo = f.getName();

                        if ((conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN) ||
                                conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) ||
                                conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA))
                                && (!nomeArquivo.contains(conv.getNumeroContrato()))) {
                            continue;
                        }

                        FileInputStream is = new FileInputStream(f.getPath());
                        StringBuilder head = Uteis.convertStreamToStringBuffer(is);

                        if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_CAIXA)) {
                            if (head.toString().contains("ARQUIVO RECEBIDO - ACATADO")) {
                                throw new Exception("Arquivo foi ACATADO! " + nomeArquivo);
                            }
                            if (head.toString().contains("ARQUIVO RECEBIDO - REJEITADO")) {
                                throw new Exception("Arquivo foi REJEITADO! " + nomeArquivo);
                            }
                        }

                        ProcessoRetornoRemessaTO processoRetornoRemessaTO = new ProcessoRetornoRemessaTO();
                        processoRetornoRemessaTO.setNomeArquivo(nomeArquivo);
                        processoRetornoRemessaTO.setIdentificadorEmpresaFinaceiro("");
                        processoRetornoRemessaTO.setEmpresaFinaceiro(new EmpresaFinanceiroVO());
                        processoRetornoRemessaTO.setEmpresaFinanceiroVOs(new ArrayList<>());
                        processoRetornoRemessaTO.setMapaItemEmpresa(new HashMap<>());
                        processoRetornoRemessaTO.setHeadRetorno(new StringBuilder(head));

                        preencherRemessaDoRetorno(processoRetornoRemessaTO, conv, conv.getEmpresa());

                        Map<Integer, RemessaItemVO> mapaItens = processarMapaItens(processoRetornoRemessaTO.getItensProcessar(), conv);

                        RemessaVO remessaRetorno = processoRetornoRemessaTO.getRemessaRetorno();
                        remessaRetorno.setEmpresa(conv.getEmpresa().getCodigo());
                        remessaRetorno.setTipo(conv.getTipo().getTipoRemessa());
                        remessaRetorno.setConvenioCobranca(conv);
                        remessaRetorno.setListaItens(processoRetornoRemessaTO.getItensProcessar());

                        Set<Integer> codsRemessas;
                        if (conv.isLayoutFebrabanDCO()) {
                            List<String> dadosInvalidos = new ArrayList<String>();
                            codsRemessas = processarRetornoFebraban(remessaRetorno, dadosInvalidos, mapaItens, conv);
                        } else {
                            codsRemessas = processarRetornoItau(remessaRetorno, mapaItens);
                        }

                        processarSituacaoRemessas(codsRemessas);

                    } else {
                        Uteis.logar(null, "Erro: Desprezando arquivo vazio: " + f.getAbsolutePath());
                    }
                } catch (Exception e) {
                    Uteis.logar(null, String.format("Erro %s ao processar arquivo %s",
                            e.getMessage(), f.getAbsolutePath()));
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<Integer, RemessaItemVO> processarMapaItens(List<RemessaItemVO> listaItens, ConvenioCobrancaVO conv) {
        Map<Integer, RemessaItemVO> mapaItens = new HashMap<Integer, RemessaItemVO>();
        for (RemessaItemVO item : listaItens) {
            if (conv.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_DAYCOVAL) ||
                    conv.getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                mapaItens.put(item.getCodigo(), item);
            } else {
                RemessaItemVO remessaItemVO = mapaItens.get(item.getMovParcela().getCodigo());
                if (remessaItemVO == null || item.getCodigo() > remessaItemVO.getCodigo()) {
                    mapaItens.put(item.getMovParcela().getCodigo(), item);
                }
            }
        }
        return mapaItens;
    }

    private void processarSituacaoRemessas(Set<Integer> codsRemessas) throws Exception {
        if (codsRemessas == null) {
            return;
        }
        for (Integer codigo : codsRemessas) {
            getFacade().getZWFacade().getRemessa().processarSituacaoRemessa(codigo);
        }
    }

    public String processarRetornoBoletoOnline(RemessaVO remessa, Map<Long, BoletoVO> itens,
                                               RetornoRemessaVO retornoRemessaVO, String identificadorEmpresa) throws Exception {
        List<RegistroRemessa> detailsRetorno = remessa.getDetailsRetorno();
        RegistroRemessa headerRetorno = remessa.getHeaderRetorno();
        String dataOcorrenciaBancoValor = headerRetorno.getValue(DCCAttEnum.DataGeracao.name());
        Date dataOcorrencia;
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())) {
            dataOcorrencia = (Calendario.getDate("yyyyMMdd", dataOcorrenciaBancoValor));
        } else {
            dataOcorrencia = (Calendario.getDate("ddMMyy", dataOcorrenciaBancoValor));
        }
        remessa.setListaItens(new ArrayList<>());
        remessa.setListaItensBoleto(new ArrayList<>());
        for (RegistroRemessa regRet : detailsRetorno) {
            if(!identificadorEmpresa.equals("") && !regRet.getValue(DCCAttEnum.NossoNumero.name()).startsWith(identificadorEmpresa)){
                continue;
            }

            String valorVenda = regRet.getValue(DCCAttEnum.ValorVenda.name());
            double valorBoleto = new Double(valorVenda.substring(0, valorVenda.length() - 2) + "." + valorVenda.substring(valorVenda.length() - 2));

            String valorPagoRetorno = regRet.getValue(DCCAttEnum.ValorPago.name());
            double valorPago = new Double(valorPagoRetorno.substring(0, valorPagoRetorno.length() - 2) + "." + valorPagoRetorno.substring(valorPagoRetorno.length() - 2));

            String valorMoraRetorno = regRet.getValue(DCCAttEnum.ValorMora.name());
            Double valorMora = 0.0;
            if (!UteisValidacao.emptyString(valorMoraRetorno)) {
                valorMora = new Double(valorMoraRetorno.substring(0, valorMoraRetorno.length() - 2) + "." + valorMoraRetorno.substring(valorMoraRetorno.length() - 2));
            }

            String tarifa = regRet.getValue(DCCAttEnum.DespesaCobranca.name());
            double valorTarifa = 0.0;
            if (!tarifa.trim().equals("")) {
                valorTarifa = new Double(tarifa.substring(0, tarifa.length() - 2) + "." + tarifa.substring(tarifa.length() - 2));
            }

            String nossoNumeroTmp = regRet.getValue(DCCAttEnum.NossoNumero.name()).trim();
            if (UteisValidacao.emptyString(nossoNumeroTmp)) {
                retornoRemessaVO.incrementarErro();
                retornoRemessaVO.incrementarValorErro(valorBoleto);
                continue;
            }

            Long nossoNumero = Long.valueOf(nossoNumeroTmp);
            BoletoVO boletoVO = itens.get(nossoNumero);
            if (boletoVO == null) {
                continue;
            }

            String statusVenda = regRet.getValue(DCCAttEnum.StatusVenda.name());
            if (!statusVendaValido(boletoVO, valorPago, statusVenda)) {
                retornoRemessaVO.incrementarErro();
                retornoRemessaVO.incrementarValorErro(valorBoleto);
                continue;
            }

//
//            if (item.getValorPago() > item.getValor()) {
//                if (valorMora == null) {
//                    valorMora = 0.0;
//                }
//                valorMora += (item.getValorPago() - item.getValor());
//            }


//
//            if (remessa.getConvenioCobranca().isTarifaBoletoSeparadaValorPago()) {
//                valorPago = valorPago + valorTarifa;
//            }

//            RemessaVO remessaParcela = getFacade().getZWFacade().getRemessa().consultarPorChavePrimaria(item.getRemessa().getCodigo());
//            codsRemessas.add(remessaParcela.getCodigo());
//            item.setRemessa(remessaParcela);

            String codigoRetorno = remessa.getHeaderRetorno().getValue(DCCAttEnum.NumAvisoBancario.name());
            if (codigoRetorno.equals("")) {
                codigoRetorno = remessa.getHeaderRetorno().getValue(DCCAttEnum.DataGeracao.name());
            }

            try {
                getFacade().getZWFacade().getBoleto().incluirBoletoHistorico(boletoVO, "Retorno-RemessaService-StatusVenda" + statusVenda, regRet.toString());

                if (!UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                    //item já foi pago e realizado a baixa.
                    continue;
                }
                if (!boletoVO.processouRetorno(codigoRetorno)) {
                    String codigoErro = "";
                    if (boletoVO.isLiquidado(statusVenda)) {
                        Date dataEntrada = dataOcorrencia;

                        String dataEntradaArquivo = regRet.getValue(DCCAttEnum.DataEntrada.name());
                        if (!UteisValidacao.emptyString(dataEntradaArquivo)) {
                            if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())){
                                dataEntrada = Calendario.getDate("yyyyMMdd", dataEntradaArquivo);
                            } else if (dataEntradaArquivo.length() == 8) {
                                dataEntrada = Calendario.getDate("ddMMyyyy", dataEntradaArquivo);
                            } else {
                                dataEntrada = Calendario.getDate("ddMMyy", dataEntradaArquivo);
                            }
                        }


                        /**
                         * Pegar a data de compensação que vem no arquivo!
                         * by Luiz Felipe 23/04/2020
                         */
                        Date dataCompensacao = dataEntrada; //Antes da alteração para pegar a data do retorno a data era a data de entrada.
                        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.ITAU.getCodigo())) {
                            try {
                                String dataPrevistaCredito = regRet.getValue(DCCAttEnum.DataPrevistaCredito.name());
                                if (!UteisValidacao.emptyString(dataPrevistaCredito)) {
                                    dataCompensacao = Calendario.getDate("ddMMyy", dataPrevistaCredito);
                                }
                            } catch (Exception ex){
                                ex.printStackTrace();
                            }
                        }

                        boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.ValorTarifa, String.valueOf(valorTarifa));
                        boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.ValorMora, String.valueOf(valorMora));

                        boletoVO.setValorPago(valorPago + valorTarifa + valorMora);
                        boletoVO.setDataCredito(dataCompensacao);
                        boletoVO.setDataPagamento(dataEntrada);
                        boletoVO.setSituacao(SituacaoBoletoEnum.PAGO);
                        BoletoOnlineService boletoService = new BoletoOnlineService(con);
                        boletoService.gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);

                        boletoVO.setCodigosRetorno(codigoRetorno + "|" + boletoVO.getCodigosRetorno());
                    } else {
                        codigoErro = regRet.getValue(DCCAttEnum.CodigoErro.name());
                    }

                    boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.StatusVenda, statusVenda);
                    boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.data_atualizacao, Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                    if (!UteisValidacao.emptyString(codigoErro)) {
                        boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.CodigoErro, codigoErro);
                    }
                    getFacade().getZWFacade().getBoleto().alterar(boletoVO);
                }
                if (boletoVO.isLiquidado(statusVenda)) {
                    retornoRemessaVO.incrementarSucesso();
                    retornoRemessaVO.incrementarValorSucesso(valorBoleto);
                    retornoRemessaVO.incrementarValorTarifas(boletoVO.getEmpresaVO().getCodigo(), valorTarifa);
                }
                remessa.getListaItensBoleto().add(boletoVO);
            } catch (Exception e) {
                retornoRemessaVO.incrementarErro();
                retornoRemessaVO.incrementarValorErro(valorBoleto);
                e.printStackTrace();
                Uteis.logarDebug("ERRO ITEM BOLETO -> " + boletoVO.getCodigo());
            }
        }
        lancarContaPagarDasTarifas(retornoRemessaVO);
        getFacade().getRetornoRemessa().alterar(retornoRemessaVO, false);

        if (!UteisValidacao.emptyNumber(retornoRemessaVO.getQuantidadeSucesso())) {
            return "Realizado a baixa de " + retornoRemessaVO.getQuantidadeSucesso() + " boleto(s).";
        } else {
            return "Nenhum boleto foi realizado a baixa";
        }
    }

    private boolean statusVendaValido(BoletoVO item, Double valorPago, String statusVenda) {
        if (item == null) {
            return false;
        }
        if (item.isLiquidado(statusVenda)) {
            return true;
        }
        return valorPago == 0.0 && item.isStatusPrevisto(statusVenda);
    }

    public static void preencherTabelaTipoInfoMigracaoEnum(boolean forcarAtualizacao, Connection con) {
        try {
            String nomeEnum = TipoInfoMigracaoEnum.class.getSimpleName();
            if (forcarAtualizacao) {
                SuperFacadeJDBC.executarUpdate("DELETE FROM tabelagenericaenum where nomeenum ilike '" + nomeEnum + "';", con);
            }

            Integer totalCadastrado = SuperFacadeJDBC.contar("select count(*) as qtd from (select * from tabelagenericaenum where nomeenum ilike '" + nomeEnum + "') as sql", con);
            if (totalCadastrado < TipoInfoMigracaoEnum.values().length) {
                for (TipoInfoMigracaoEnum obj : TipoInfoMigracaoEnum.values()) {
                    String sql = "INSERT INTO tabelagenericaenum(nomeenum,codigo,descricao) select ?, ?, ? " +
                            "where not exists(select 1 from tabelagenericaenum where nomeenum ilike ? and codigo = ?);";
                    try (PreparedStatement pst = con.prepareStatement(sql)) {
                        int i = 0;
                        pst.setString(++i, nomeEnum);
                        pst.setString(++i, obj.getId().toString());
                        pst.setString(++i, processarDescricaoEnum(obj.name()));
                        pst.setString(++i, nomeEnum);
                        pst.setString(++i, obj.getId().toString());
                        pst.execute();
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static String processarDescricaoEnum(String descricao) {
        return Uteis.retirarAcentuacaoRegex(descricao).replaceAll(" ", "_")
                .replaceAll("\\.", "_")
                .replaceAll("-", "_")
                .replaceAll("\\(", "_")
                .replaceAll("\\)", "_")
                .replaceAll("___", "_")
                .replaceAll("__", "_")
                .toUpperCase();
    }

    private void processarPagamentoAutomaticosBoletoBancoBrasil(EmpresaVO empresa) {
        try {
            List<ConvenioCobrancaVO> listaConveniosBancoBrasil = getFacade().getConvenioCobranca().consultarPorTipoECodEmpresa(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE,
                    empresa.getCodigo(), SituacaoConvenioCobranca.ATIVO, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (ConvenioCobrancaVO convenioCobrancaVO: listaConveniosBancoBrasil) {
                BancoBrasilService service = new BancoBrasilService(getCon(), empresa.getCodigo(), convenioCobrancaVO.getCodigo());
                service.processoAutomaticoPagamentosBoletoBancoBrasil(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void processarPagamentosCaixaOnline(EmpresaVO empresaVO) {
        CaixaService caixaService;
        try {
            caixaService = new CaixaService(con);
            caixaService.processoAutomaticoPagamentosBoletoCaixa(empresaVO);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            caixaService = null;
        }
    }

}

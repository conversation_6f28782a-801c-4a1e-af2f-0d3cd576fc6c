package servicos.impl.dcc.base;

import br.com.pactosolucoes.atualizadb.processo.CorrigirHistoricoVinculos;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.HistoricoVinculo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoCorrecaoVinculosE21356 extends SuperEntidade {

    private static Integer qtdAjustado = 0;
    private static boolean simular = false;

    public ProcessoCorrecaoVinculosE21356() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            simular = true;

            String chave = "inner";
            try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
                processar(con);
                ajustarHistoricoSemEntrada(con);
            }
        } catch (Exception ex) {
            Logger.getLogger(ProcessoCorrecaoVinculosE21356.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static boolean existeRegistrosComProblema(Connection con) throws Exception {
        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( select codigo from backhistoricovinculo ) as sql", con);
        return !UteisValidacao.emptyNumber(total);
    }

    public static void processarExecutarProcessos(Connection con) throws Exception {
        try {
            StringBuilder sqlCreate = new StringBuilder();
            sqlCreate.append("CREATE TABLE public.backhistoricovinculo ( \n");
            sqlCreate.append("	codigo serial4 NOT NULL, \n");
            sqlCreate.append("	dataregistro timestamp NOT NULL, \n");
            sqlCreate.append("	tipohistoricovinculo varchar(2) NOT NULL, \n");
            sqlCreate.append("	tipocolaborador varchar(2) NOT NULL, \n");
            sqlCreate.append("	cliente int4 NOT NULL, \n");
            sqlCreate.append("	colaborador int4 NOT NULL, \n");
            sqlCreate.append("	origem varchar(50) NULL, \n");
            sqlCreate.append("	usuarioresponsavel int4 NULL, \n");
            sqlCreate.append("	codigo_backup int4 NOT NULL, \n");
            sqlCreate.append("	ajustado boolean, \n");
            sqlCreate.append("	CONSTRAINT backhistoricovinculo_pkey PRIMARY KEY (codigo) \n");
            sqlCreate.append("); \n");
            SuperFacadeJDBC.executarUpdate(sqlCreate.toString(), con);

            SuperFacadeJDBC.executarUpdate("alter table historicovinculo add column backhistoricovinculo integer;", con);

            ProcessoCorrecaoVinculosE21356.processar(con);
            ProcessoCorrecaoVinculosE21356.ajustarHistoricoSemEntrada(con);
            CorrigirHistoricoVinculos corrigirHistoricoVinculos = new CorrigirHistoricoVinculos(con);
            corrigirHistoricoVinculos.processarCorrecao(false, true);
            corrigirHistoricoVinculos = null;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void processar(Connection con) throws Exception {
        try {
            con.setAutoCommit(false);

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( select codigo from historicovinculo where tipohistoricovinculo = 'AT' ) as sql", con);
            if (UteisValidacao.emptyNumber(total)) {
                return;
            }

            StringBuilder sqlInsert = new StringBuilder();
            sqlInsert.append("insert into backhistoricovinculo(dataregistro,tipohistoricovinculo,tipocolaborador,cliente,colaborador,");
            sqlInsert.append("origem,usuarioresponsavel,codigo_backup) ");
            sqlInsert.append("select dataregistro,tipohistoricovinculo,tipocolaborador,cliente,colaborador,");
            sqlInsert.append("origem,usuarioresponsavel,codigo from historicovinculo h where tipohistoricovinculo = 'AT' ");
            sqlInsert.append("and codigo not in (select codigo_backup from backhistoricovinculo)");
            SuperFacadeJDBC.executarUpdate(sqlInsert.toString(), con);

            SuperFacadeJDBC.executarUpdate("delete from historicovinculo where tipohistoricovinculo = 'AT'", con);

            if (simular) {
                con.rollback();
            } else {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }

    public static void ajustarHistoricoSemEntrada(Connection con) throws Exception {
        try {
            con.setAutoCommit(false);

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("v.*, \n");
            sql.append("EXISTS(select min(codigo) from backhistoricovinculo where cliente = v.cliente AND tipocolaborador = v.tipovinculo and colaborador = v.colaborador) AS existe_back, \n");
            sql.append("(select min(codigo) from backhistoricovinculo where cliente = v.cliente AND tipocolaborador = v.tipovinculo and colaborador = v.colaborador) AS codigo_backhistoricovinculo, \n");
            sql.append("(select min(dataregistro) from backhistoricovinculo where cliente = v.cliente AND tipocolaborador = v.tipovinculo and colaborador = v.colaborador) AS dataregistro_backhistoricovinculo, \n");
            sql.append("(select count(*) from backhistoricovinculo where cliente = v.cliente AND tipocolaborador = v.tipovinculo  and colaborador = v.colaborador) AS qtd_back \n");
            sql.append("from vinculo v \n");
            sql.append("where not exists(select codigo from historicovinculo h where h.cliente = v.cliente and h.colaborador = v.colaborador and h.tipohistoricovinculo= 'EN' and v.tipovinculo = h.tipocolaborador) \n");
            sql.append("order by v.cliente \n");

            Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", con);
            Uteis.logarDebug("ajustarHistoricoSemEntrada | Total " + total);

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {

                Integer qtd_back = rs.getInt("qtd_back");
                Integer codigoVinculo = rs.getInt("codigo");
                Integer cliente = rs.getInt("cliente");
                Integer colaboradorAtual = rs.getInt("colaborador");
                String tipovinculo = rs.getString("tipovinculo");
                Date dataregistro_backhistoricovinculo = rs.getTimestamp("dataregistro_backhistoricovinculo");

                HistoricoVinculo historicoVinculoDAO;
                try {
                    historicoVinculoDAO = new HistoricoVinculo(con);

                    if (qtd_back > 1) {
                        Uteis.logarDebug("Mais de um registro | Cliente " + cliente + " | Vinculo " + codigoVinculo + " | Tipo " + tipovinculo);

                        ResultSet rs2 = SuperFacadeJDBC.criarConsulta("select * from historicovinculo where cliente = " + cliente + " and tipocolaborador = '" +
                                tipovinculo + "' and dataregistro <= '" + Uteis.getData(dataregistro_backhistoricovinculo, "bdtimestamp") + "' order by dataregistro desc", con);
                        if (rs2.next()) {
                            String tipohistoricovinculo = rs2.getString("tipohistoricovinculo");
                            if (!tipohistoricovinculo.equals("EN")) {
                                throw new Exception("Erroooooooo_1");
                            }

                            Integer colaboradorUltimoHistorico = rs2.getInt("colaborador");
                            ResultSet rs3 = SuperFacadeJDBC.criarConsulta("select * from backhistoricovinculo where cliente = " + cliente + " and tipocolaborador = '" + tipovinculo + "' and colaborador = " + colaboradorAtual + " order by dataregistro ", con);
                            if (rs3.next()) {

                                Integer back_codigo = rs3.getInt("codigo");
                                Date back_dataRegistro = rs3.getTimestamp("dataregistro");
//                                String back_tipohistoricovinculo = rs3.getDate("tipohistoricovinculo");
                                String back_tipocolaborador = rs3.getString("tipocolaborador");
                                Integer back_cliente = rs3.getInt("cliente");
                                Integer back_colaborador = rs3.getInt("colaborador");
                                String back_origem = rs3.getString("origem");
                                Integer back_usuarioresponsavel = rs3.getInt("usuarioresponsavel");

                                //incluindo a saida do colaborador
                                HistoricoVinculoVO historicoVinculoSaida = new HistoricoVinculoVO();
                                historicoVinculoSaida.setDataRegistro(back_dataRegistro);
                                historicoVinculoSaida.setTipoHistoricoVinculo("SD");
                                historicoVinculoSaida.setTipoColaborador(back_tipocolaborador);
                                historicoVinculoSaida.getCliente().setCodigo(back_cliente);
                                historicoVinculoSaida.getColaborador().setCodigo(colaboradorUltimoHistorico);
                                historicoVinculoSaida.setOrigem(back_origem);
                                historicoVinculoSaida.setUsuarioVO(new UsuarioVO());
                                historicoVinculoSaida.getUsuarioVO().setCodigo(back_usuarioresponsavel);
                                historicoVinculoDAO.incluirSemCommit(historicoVinculoSaida, false);
                                SuperFacadeJDBC.executarUpdate("update historicovinculo set backhistoricovinculo =  " + back_codigo + " where codigo = " + historicoVinculoSaida.getCodigo(), con);

                                //incluindo a entrada do colaborador atual
                                HistoricoVinculoVO historicoVinculoEntrada = new HistoricoVinculoVO();
                                historicoVinculoEntrada.setDataRegistro(Calendario.somarSegundos(back_dataRegistro, 1));
                                historicoVinculoEntrada.setTipoHistoricoVinculo("EN");
                                historicoVinculoEntrada.setTipoColaborador(back_tipocolaborador);
                                historicoVinculoEntrada.getCliente().setCodigo(back_cliente);
                                historicoVinculoEntrada.getColaborador().setCodigo(colaboradorAtual);
                                historicoVinculoEntrada.setOrigem(back_origem);
                                historicoVinculoEntrada.setUsuarioVO(new UsuarioVO());
                                historicoVinculoEntrada.getUsuarioVO().setCodigo(back_usuarioresponsavel);
                                historicoVinculoDAO.incluirSemCommit(historicoVinculoEntrada, false);
                                SuperFacadeJDBC.executarUpdate("update historicovinculo set backhistoricovinculo =  " + back_codigo + " where codigo = " + historicoVinculoEntrada.getCodigo(), con);

                                SuperFacadeJDBC.executarUpdate("update backhistoricovinculo set ajustado = true where codigo = " + back_codigo, con);
                            } else {
                                throw new Exception("Erroooooooo_3");
                            }
                        } else {
                            throw new Exception("Erroooooooo_5");
                        }
                    } else {

                        Integer codigo_backhistoricovinculo = rs.getInt("codigo_backhistoricovinculo");
                        StringBuilder sqlInsert = new StringBuilder();
                        sqlInsert.append("insert into historicovinculo(");
                        sqlInsert.append("tipohistoricovinculo,dataregistro,tipocolaborador,cliente,colaborador,origem,usuarioresponsavel,backhistoricovinculo) ");
                        sqlInsert.append("select ");
                        sqlInsert.append("'EN',dataregistro,tipocolaborador,cliente,colaborador,origem,usuarioresponsavel,codigo ");
                        sqlInsert.append("from backhistoricovinculo where codigo = ").append(codigo_backhistoricovinculo);
                        SuperFacadeJDBC.executarUpdate(sqlInsert.toString(), con);
                        SuperFacadeJDBC.executarUpdate("update backhistoricovinculo set ajustado = true where codigo = " + codigo_backhistoricovinculo, con);
                    }
                    ++qtdAjustado;
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logarDebug("ERRROooooooo " + cliente);
                } finally {
                    historicoVinculoDAO = null;
                }
            }

            Uteis.logarDebug("qtdAjustado = " + qtdAjustado);

            if (simular) {
                con.rollback();
            } else {
                con.commit();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            con.rollback();
        } finally {
            con.setAutoCommit(true);
        }
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.cielo;

/**
 *
 * <AUTHOR>
 */
public enum OrigemAjusteCieloEnum {

    NENHUM("", "Nenhum", "", false),
    Origem01("01", "Acerto de correção monetária", "Acerto", false),
    Origem02("02", "Acerto de data de pagamento", "Acerto", false),
    Origem03("03", "Acerto de taxa de comissão", "Acerto", false),
    Origem04("04", "Acerto de valores não processados", "Acerto", false),
    Origem05("05", "Acerto de valores não recebidos", "Acerto", false),
    Origem06("06", "Acerto de valores não reconhecidos", "Acerto", false),
    Origem07("07", "Acerto de valores negociados", "Acerto", false),
    Origem08("08", "Acerto de valores processados indevidamente", "Acerto", false),
    Origem09("09", "Acerto de lançamento não compensado em conta-corrente", "Acerto", false),
    Origem10("10", "Acerto referente valores contestados", "Acerto", false),
    Origem11("11", "Acerto temporário de valores contestados", "Acerto", false),
    Origem12("12", "Acertos diversos", "Acerto", false),
    Origem13("13", "Acordo de cobrança", "Acerto", false),
    Origem14("14", "Acordo jurídico", "Acerto", false),
    Origem15("15", "Aplicação de multa Programa Monitoria Chargeback", "Acerto", false),
    Origem16("16", "Bloqueio de valor por ordem judicial", "Bloqueio", false),
    Origem17("17", "Cancelamento da venda", "Cancelamento", false),
    Origem18("18", "Cobrança de tarifa operacional", "Cobrança", false),
    Origem19("19", "Cobrança mensal Lynx Comércio", "Cobrança", false),
    Origem20("20", "Cobrança Plano Cielo", "Cobrança", false),
    Origem21("21", "Contrato de caução", "Acerto", false),
    Origem22("22", "Crédito de devolução do cancelamento ? banco emissor", "Acerto", false),
    Origem23("23", "Crédito EC ? referente contestação portador", "Acerto", false),
    Origem24("24", "Crédito por cancelamento rejeitado ? Cielo", "Acerto", false),
    Origem25("25", "Processamento do débito duplicado ? Visa Pedágio", "Acerto", false),
    Origem26("26", "Débito por venda realizada sem a leitura do chip", "Acerto", false),
    Origem27("27", "Débito por venda rejeitada no sistema ? Cielo", "Acerto", false),
    Origem28("28", "Débito referente à contestação do portador", "Chargeback", true),
    Origem29("29", "Estorno de acordo jurídico", "Acerto", false),
    Origem30("30", "Estorno de contrato de caução", "Acerto", false),
    Origem31("31", "Estorno de acordo de cobrança", "Acerto", false),
    Origem32("32", "Estorno de bloqueio de valor por ordem judicial", "Acerto", false),
    Origem33("33", "Estorno de cancelamento de venda", "Acerto", false),
    Origem34("34", "Estorno de cobrança de tarifa operacional", "Acerto", false),
    Origem35("35", "Estorno de cobrança mensal Lynx Comércio", "Acerto", false),
    Origem36("36", "Estorno de cobrança Plano Cielo", "Acerto", false),
    Origem37("37", "Estorno de débito venda sem a leitura do Chip", "Acerto", false),
    Origem38("38", "Estorno de incentivo comercial", "Acerto", false),
    Origem39("39", "Estorno de Multa Programa Monitoria Chargeback", "Acerto", false),
    Origem40("40", "Estorno de rejeição ARV", "Acerto", false),
    Origem41("41", "Estorno de reversão de duplicidade do pagamento - ARV", "Acerto", false),
    Origem42("42", "Estorno de tarifa de cadastro", "Acerto", false),
    Origem43("43", "Estorno de extrato papel", "Acerto", false),
    Origem44("44", "Estorno de processamento duplicado de débito - Visa Pedágio", "Acerto", false),
    Origem45("45", "Incentivo comercial", "Acerto", false),
    Origem46("46", "Incentivo por venda de Recarga", "Acerto", false),
    Origem47("47", "Regularização de rejeição ARV", "Acerto", false),
    Origem48("48", "Reversão de duplicidade do pagamento - ARV", "Acerto", false),
    Origem49("49", "Tarifa de cadastro", "Cobrança", false),
    Origem50("50", "Tarifa de extrato no papel", "Cobrança", false),
    Origem51("51", "Aceleração de débito de antecipação", "Acerto", false),
    Origem52("52", "Débito por descumprimento de cláusula contratual", "Chargeback", true),
    Origem53("53", "Débito por cancelamento de venda", "Chargeback", true),
    Origem54("54", "Débito por não reconhecimento de compra", "Chargeback", true),
    Origem55("55", "Débito por venda com cartão com validade vencida", "Chargeback", true),
    Origem56("56", "Débito por não reconhecimento de compra", "Chargeback", true),
    Origem57("57", "Débito por cancelamento e/ou devolução dos serviços", "Chargeback", true),
    Origem58("58", "Débito por transação irregular", "Chargeback", true),
    Origem59("59", "Débito por não entrega da mercadoria", "Chargeback", true),
    Origem60("60", "Débito por serviços não prestados", "Chargeback", true),
    Origem61("61", "Débito efetuado venda sem código de autorização", "Chargeback", true),
    Origem62("62", "Débito efetuado venda com número cartão inválido", "Chargeback", true),
    Origem63("63", "Débito por cópia de CV e/ou documento não atendido", "Chargeback", true),
    Origem64("64", "Débito venda efetuada com autorização negada", "Chargeback", true),
    Origem65("65", "Débito envio de CV e/ou documento ilegível", "Chargeback", true),
    Origem66("66", "Débito por venda efetuada sem leitura de chip", "Chargeback", true),
    Origem67("67", "Débito por venda em outra moeda", "Chargeback", true),
    Origem68("68", "Débito venda processada incorretamente", "Chargeback", true),
    Origem69("69", "Débito por cancelamento de venda", "Chargeback", true),
    Origem70("70", "Débito por crédito em duplicidade", "Chargeback", true),
    Origem71("71", "Débito por documentos solicitados e não recebidos", "Chargeback", true),
    Origem72("72", "Débito envio de CV e/ou documento incorreto", "Chargeback", true),
    Origem73("73", "Débito por envio de CV e/ou documento fora do prazo", "Chargeback", true),
    Origem74("74", "Débito por não reconhecimento de despesa", "Chargeback", true),
    Origem75("75", "Débito documentação solicitada incompleta", "Chargeback", true),
    Origem76("76", "Débito estabelecimento não possui CV e/ou Doc.", "Chargeback", true),
    Origem77("77", "Programa de monitoria de chargeback", "Chargeback", true),
    Origem78("78", "Serviços Score", "Cobrança", false),
    Origem79("79", "Reagendamento do débito de antecipação", "Acerto", false),
    Origem80("80", "Ajuste do débito de cessão", "Cobrança/Acerto", false),
    Origem81("81", "Cielo e-Commerce", "Cobrança/Acerto", false);

    private String id;
    private String descricao;
    private String tipoAjuste;
    private boolean chargeback;

    private OrigemAjusteCieloEnum(String id, String descricao, String tipoAjuste, boolean chargeback) {
        this.id = id;
        this.descricao = descricao;
        this.tipoAjuste = tipoAjuste;
        this.chargeback = chargeback;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoAjuste() {
        return tipoAjuste;
    }

    public void setTipoAjuste(String tipoAjuste) {
        this.tipoAjuste = tipoAjuste;
    }

    public boolean isChargeback() {
        return chargeback;
    }

    public void setChargeback(boolean chargeback) {
        this.chargeback = chargeback;
    }

    public static OrigemAjusteCieloEnum valueOff(String id) {
        OrigemAjusteCieloEnum[] values = OrigemAjusteCieloEnum.values();
        for (OrigemAjusteCieloEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return OrigemAjusteCieloEnum.NENHUM;
    }
}

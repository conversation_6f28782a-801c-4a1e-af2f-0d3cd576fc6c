/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.cielo;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.collections.Predicate;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class LayoutRemessaCieloDCC extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        header.put(DCCAttEnum.TipoRegistro, "00");
        header.put(DCCAttEnum.DataDeposito, StringUtilities.formatarCampoData(dataDeposito));
        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getCodigo()), 7));
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getCodigo()), 10));
        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(
                new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
        header.put(DCCAttEnum.Moeda, StringUtilities.formatarCampo(new BigDecimal(986), 3));//Reais
        header.put(DCCAttEnum.IndicadorProcesso, "P");
        header.put(DCCAttEnum.IndicadorVenda, "V");
        header.put(DCCAttEnum.IndicacaoEspecial, " ");
        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(204));

        boolean remessaAgrupada = remessa.isNovoFormato();

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaItemVO item : lista) {

            boolean parceladoLojista;
            Integer numeroParcelasOperadora;
            if (remessaAgrupada) {
                parceladoLojista = item.getMovParcelas().get(0).getMovParcelaVO().isParceladoLojista();
                numeroParcelasOperadora = item.getMovParcelas().get(0).getMovParcelaVO().getNumeroParcelasOperadora();
            } else {
                parceladoLojista = item.getMovParcela().isParceladoLojista();
                numeroParcelasOperadora = item.getMovParcela().getNumeroParcelasOperadora();
            }

            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
            detail.put(DCCAttEnum.TipoRegistro, "01");//2

            if (remessaAgrupada) {
                detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 7));//7
            } else {
                detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getMovParcela().getCodigo()), 7));//7
            }

            detail.put(DCCAttEnum.NumeroCartao, StringUtilities.formatarCampo(new BigDecimal(item.getNazgDTO().getCard().trim(), MathContext.UNLIMITED), 19));//19
            detail.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.formatarCampoZerado(6));//6
            detail.put(DCCAttEnum.DataVenda, StringUtilities.formatarCampoData(dataDeposito));//8
            detail.put(DCCAttEnum.OpcaoVenda, parceladoLojista ? "2" : "0");//1
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorItemRemessa(), 15));//15
            detail.put(DCCAttEnum.QuantidadeParcelas, parceladoLojista ? StringUtilities.formatarCampoForcandoZerosAEsquerda(numeroParcelasOperadora, 3) : StringUtilities.formatarCampoZerado(3));//3
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//15
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//15
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//15

            Double valorParcela = 0.0;
            if (parceladoLojista) {
                valorParcela = item.getValorItemRemessa() / numeroParcelasOperadora;
            }
            detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoMonetario(valorParcela,15));//15
            detail.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getCodigo()), 7));//7
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));//3
            detail.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.formatarCampo(
                    new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(),
                            MathContext.UNLIMITED), 10));//10
            /*
             * 8 = data registro parcela
             * 8 = data vencimento parcela
             * 14 = brancos
             */
            if (remessaAgrupada) {
                detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(30));
            } else {
                detail.put(DCCAttEnum.ReservadoEstabelecimento,
                        StringUtilities.formatarCampoData(item.getMovParcela().getDataRegistro())//8
                                + StringUtilities.formatarCampoData(item.getMovParcela().getDataVencimento())//8
                                + StringUtilities.formatarCampoEmBranco(14));//14
            }

            //
            detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoZerado(2));//2
            detail.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));//8

            String ano = Formatador.formatarValorNumerico((double) item.getNazgDTO().getYear(), "00");
            if (ano != null && ano.length() >= 4) {
                ano = ano.substring(2);
            }
            String validade = ano + Formatador.formatarValorNumerico((double) item.getNazgDTO().getMonth(), "00");

            detail.put(DCCAttEnum.ValidadeCartao, validade);//4

            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(7));//7
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(15));//15
            detail.put(DCCAttEnum.CVV2, StringUtilities.formatarCampoEmBranco(3));//3
            detail.put(DCCAttEnum.CodigoErro, StringUtilities.formatarCampoEmBranco(4));//4
            detail.put(DCCAttEnum.NumeroReferencia, StringUtilities.formatarCampoEmBranco(11));//11
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(25));//25
            soma += item.getValorItemRemessa();
            if (item.getMovPagamento().getCodigo() != 0) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "99");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 7));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 15));
        trailer.put(DCCAttEnum.ValorTotalAceito, StringUtilities.formatarCampo(new BigDecimal(0), 15));
        trailer.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.formatarCampo(new BigDecimal(0), 15));
        trailer.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampo(new BigDecimal(0), 8));
        trailer.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(188));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("00")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    /**
     * Método criado para solucionar problema que a CIELO inseriu nos arquivos de retorno, que duplica uma linha do Comprovante de Venda quando um cartão é cancelado.
     * Verifica se o mesmo comprovante de venda e mesmo cartão (bloco 1) está duplicado no arquivo com uma linha com status 00 (aprovado) porém sem autorização, e outra linha com status 42 (cancelado).
     * Em caso positivo, deve verificar se o registro de status 00 ñ possui cod de autorização se ñ houver, remove-o e deixa apenas o status 42.
     * Em caso negativo, verifica se a transação aprovada possui autorização e remove o registro de cartão cancelado.
     * @param listaDetalheRetorno
     * @return
     */
    public static List<RegistroRemessa> removerInconsistenciaComprovanteVendaDeCartoesCancelados(List<RegistroRemessa> listaDetalheRetorno) {
        List<RegistroRemessa> tmp = new ArrayList(listaDetalheRetorno);
        List<RegistroRemessa> tmpOriginal = new ArrayList(listaDetalheRetorno);
        for (RegistroRemessa r : listaDetalheRetorno) {
            if (r.getTipo() == TipoRegistroEnum.DETALHE) {
                List<RegistroRemessa> filtered = new ArrayList(tmp);
                final StringBuilder sb = r.firstBlock(DCCAttEnum.StatusVenda.name());
                ColecaoUtils.filter(filtered, new Predicate() {
                    @Override
                    public boolean evaluate(Object object) {
                        final RegistroRemessa current = (RegistroRemessa) object;
                        final StringBuilder found = current.firstBlock(DCCAttEnum.StatusVenda.name());
                        if (found != null && found.length() > 0 && sb != null && sb.length() > 0) {
                            return found.toString().equalsIgnoreCase(sb.toString());
                        }
                        return false;
                    }
                });
                //
                if (filtered != null && filtered.size() == 2) { //encontrou mais de uma linha do mesmo CV
                    boolean temAutorizacao = false;
                    RegistroRemessa regCancelamento = null;
                    RegistroRemessa regTransacaoOriginal = null;
                    for (RegistroRemessa reg1 : filtered) {
                        if (reg1.getValue(DCCAttEnum.StatusVenda.name()).equals(DCCCieloStatusEnum.Status00.getId())) {
                            regTransacaoOriginal = reg1;
                        }
                        if (reg1.getValue(DCCAttEnum.StatusVenda.name()).equals(DCCCieloStatusEnum.Status42.getId()))
                            regCancelamento = reg1;
                    }
                    if (regTransacaoOriginal != null && !regTransacaoOriginal.getValue(DCCAttEnum.CodigoAutorizacao.name()).equals("000000"))
                        temAutorizacao = true;
                    if (!temAutorizacao && regCancelamento != null){//remover registro original e deixar apenas do cancelamento
                        tmpOriginal.remove(regTransacaoOriginal);
                    }
                    if (temAutorizacao && regCancelamento != null) {//remover registro de cancelamento e deixar apenas o autorizado
                        tmpOriginal.remove(regCancelamento);
                    }
                }
            }
        }
        return removerDuplicados(tmpOriginal);
    }

    public static List<RegistroRemessa> removerDuplicados(List<RegistroRemessa> listaDetalheRetorno) {
        List<RegistroRemessa> tmp = new ArrayList(listaDetalheRetorno);
        Map<String, LinkedHashSet<RegistroRemessa>> map = new HashMap<>();
        for (RegistroRemessa registroRemessa : tmp) {
            if (registroRemessa.getTipo() == TipoRegistroEnum.DETALHE) {
                LinkedHashSet<RegistroRemessa> lista = map.get(registroRemessa.getValue(DCCAttEnum.NumeroComprovanteVenda.name()));
                if (lista == null) {
                    lista = new LinkedHashSet<>();
                }
                lista.add(registroRemessa);
                map.put(registroRemessa.getValue(DCCAttEnum.NumeroComprovanteVenda.name()), lista);
            }
        }

        List<RegistroRemessa> tmpOriginal = new ArrayList(listaDetalheRetorno);
        for (RegistroRemessa r : listaDetalheRetorno) {
            if (r.getTipo() == TipoRegistroEnum.DETALHE) {
                LinkedHashSet<RegistroRemessa> filtered = map.get(r.getValue(DCCAttEnum.NumeroComprovanteVenda.name()));
                if (filtered != null && filtered.size() == 2) { //encontrou mais de uma linha do mesmo CV
                    boolean temAutorizacao = false;
                    RegistroRemessa regCancelamento = null;
                    RegistroRemessa regTransacaoOriginal = null;
                    for (RegistroRemessa reg1 : filtered) {
                        if (reg1.getValue(DCCAttEnum.StatusVenda.name()).equals(DCCCieloStatusEnum.Status00.getId())) {
                            regTransacaoOriginal = reg1;
                        }
                        if (reg1.getValue(DCCAttEnum.StatusVenda.name()).equals(DCCCieloStatusEnum.Status99.getId()))
                            regCancelamento = reg1;
                    }
                    if (regTransacaoOriginal != null && !regTransacaoOriginal.getValue(DCCAttEnum.CodigoAutorizacao.name()).equals("000000"))
                        temAutorizacao = true;
                    if (!temAutorizacao && regCancelamento != null){//remover registro original e deixar apenas do cancelamento
                        tmpOriginal.remove(regTransacaoOriginal);
                    }
                    if (temAutorizacao && regCancelamento != null) {//remover registro de cancelamento e deixar apenas o autorizado
                        tmpOriginal.remove(regCancelamento);
                    }
                }
            }
        }
        return tmpOriginal;
    }

    public static void lerRetorno(RemessaVO remessa, boolean validar) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            boolean processouArquivo = false;
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("00")) {
                    if (processouArquivo) {
                        break;
                    }

                    lerAtributos(linha, h);
                    processouArquivo = true;
                } else if (linha.startsWith("01")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("99")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(removerInconsistenciaComprovanteVendaDeCartoesCancelados(listaDetalheRetorno));
            remessa.setTrailerRetorno(t);
            if (validar) {
                LayoutRemessaBase.validarArquivoRemessaRetorno(remessa);
            }

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));//ok
            r.put(DCCAttEnum.DataDeposito, StringUtilities.readString(2, 10, linha));
            r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(10, 17, linha));
            r.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.readString(17, 27, linha));
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(27, 30, linha));
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(30, 40, linha));
            r.put(DCCAttEnum.Moeda, StringUtilities.readString(40, 43, linha));//Reais
            r.put(DCCAttEnum.IndicadorProcesso, StringUtilities.readString(43, 44, linha));
            r.put(DCCAttEnum.IndicadorVenda, StringUtilities.readString(44, 45, linha));
            r.put(DCCAttEnum.IndicacaoEspecial, " ");
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(46, 250, linha));
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 250) {
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));
                r.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(2, 9, linha));
                r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(9, 28, linha));
                r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(28, 34, linha));
                r.put(DCCAttEnum.DataVenda, StringUtilities.readString(34, 42, linha));
                r.put(DCCAttEnum.OpcaoVenda, StringUtilities.readString(42, linha));
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(43, 58, linha));
                r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(58, 61, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(61, 76, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(76, 91, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(91, 106, linha));
                r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(106, 121, linha));
                r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(121, 128, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(128, 131, linha));
                r.put(DCCAttEnum.NumeroEstabelecimentoComercial, StringUtilities.readString(131, 141, linha));
                /*
                 * 8 = data registro parcela
                 * 8 = data vencimento parcela
                 * 14 = brancos
                 */
                r.put(DCCAttEnum.ReservadoEstabelecimento,
                        StringUtilities.readString(141, 171, linha));
//
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(171, 173, linha));
                r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(173, 181, linha));
                r.put(DCCAttEnum.ValidadeCartao, StringUtilities.readString(181, 185, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(185, 192, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(192, 207, linha));
                r.put(DCCAttEnum.CVV2, StringUtilities.readString(207, 210, linha));
                r.put(DCCAttEnum.CodigoErro, StringUtilities.readString(210, 214, linha));
                r.put(DCCAttEnum.NumeroReferencia, StringUtilities.readString(214, 225, linha));
                r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(225, 250, linha));
            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 2, linha));
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(2, 9, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(9, 24, linha));
            r.put(DCCAttEnum.ValorTotalAceito, StringUtilities.readString(24, 39, linha));
            r.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.readString(39, 54, linha));
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(54, 62, linha));
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(62, 250, linha));

        }
    }

    public static void preencherArquivoRemessaCancelamento(RemessaVO remessa) throws Exception {
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);

        header.put(DCCAttEnum.TipoRegistro, "00");
        header.put(DCCAttEnum.DataDeposito, StringUtilities.formatarCampoData(remessa.getDataRegistro()));
        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 7));
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 10));
        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
        header.put(DCCAttEnum.Moeda, "986");
        header.put(DCCAttEnum.IndicadorProcesso, "P");
        header.put(DCCAttEnum.IndicadorVenda, "C");
        header.put(DCCAttEnum.IndicacaoEspecial, StringUtilities.formatarCampoEmBranco(1));
        header.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(204));

        List<RemessaCancelamentoItemVO> lista = remessa.getListaItensCancelamento();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        double soma = 0;
        int qtd = 0;
        for (RemessaCancelamentoItemVO cancelamentoItemVO : lista) {
            RemessaItemVO item = cancelamentoItemVO.getItemRemessaCancelar();
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

            String props = item.getProps().toString();
            String autorizacao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.CodigoAutorizacao.name(), props);
            String nrcartao = LayoutRemessaBase.obterValorCampoProps(DCCAttEnum.NumeroCartao.name(), props);

            Double valorVenda = item.getValorItemRemessa();
            Integer comprovanteVenda;

            boolean remessaAgrupada = item.getRemessa().isNovoFormato();
            if (remessaAgrupada) {
                comprovanteVenda = item.getCodigo();
            } else {
                comprovanteVenda = item.getMovParcela().getCodigo();
            }

            Date dataVenda = item.getRemessa().getDataRegistro();
            String mesValidade = LayoutRemessaBase.obterValorCampoProps("MesValidade", props);
            String anoValidade = LayoutRemessaBase.obterValorCampoProps("AnoValidade", props);
            String validadeCartao = mesValidade + anoValidade.substring(2);
            Integer codRemessa = remessa.getCodigo();
            //
            if (UteisValidacao.emptyString(autorizacao)) {
                continue;
            }

            detail.put(DCCAttEnum.TipoRegistro, "01");
            detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(comprovanteVenda), 7));

            String tokenAragorn = LayoutRemessaBase.obterValorCampoProps(APF.TokenAragorn, props);
            if (!UteisValidacao.emptyString(tokenAragorn)) {
                nrcartao = item.getNazgDTO().getCard();
            } else {
                nrcartao = APF.decifrar(nrcartao);
            }

            detail.put(DCCAttEnum.NumeroCartao, StringUtilities.formatarCampo(new BigDecimal(nrcartao), 19));
            detail.put(DCCAttEnum.CodigoAutorizacao, autorizacao);
            detail.put(DCCAttEnum.DataVenda, StringUtilities.formatarCampoData(dataVenda));
            detail.put(DCCAttEnum.OpcaoVenda, "0");
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(valorVenda, 15));
            detail.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.formatarCampoZerado(3));
            detail.put(DCCAttEnum.ValorFinanciado, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorEntrada, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorEmbarque, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.ValorParcela, StringUtilities.formatarCampoZerado(15));
            detail.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(remessa.getCodigo()), 7));
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoZerado(3));
            detail.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getNumeroContrato(), MathContext.UNLIMITED), 10));
            detail.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampoEmBranco(30));
            detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoEmBranco(2));
            detail.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));
            detail.put(DCCAttEnum.ValidadeCartao, validadeCartao);
            detail.put(DCCAttEnum.NumeroResumoOperacoesOriginal, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getRemessa().getCodigo(), 7));
            detail.put(DCCAttEnum.ValorReembolso, StringUtilities.formatarCampoMonetario(valorVenda, 15));
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(3));
            detail.put(DCCAttEnum.CodigoErro, StringUtilities.formatarCampoEmBranco(4));
            detail.put(DCCAttEnum.NumeroReferencia, StringUtilities.formatarCampoEmBranco(11));
            detail.put(DCCAttEnum.CartaoNovo, StringUtilities.formatarCampoEmBranco(19));
            detail.put(DCCAttEnum.DataVencimentoNovo, StringUtilities.formatarCampoEmBranco(4));
            detail.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(2));


            soma += item.getValorItemRemessa();
            String statusVendaCancelamento = cancelamentoItemVO.get(DCCAttEnum.StatusVenda.name());
            if (statusVendaCancelamento != null && statusVendaCancelamento.equals(DCCCieloStatusEnum.Status00.getId())) {
                qtd += 1;
            }
            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtd);

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.TipoRegistro, "99");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(listaDetalhe.size() + 2), 7));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(soma, 15));
        trailer.put(DCCAttEnum.ValorTotalAceito, StringUtilities.formatarCampoZerado(15));
        trailer.put(DCCAttEnum.ValorTotalLiquido, StringUtilities.formatarCampoZerado(15));
        trailer.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.formatarCampoZerado(8));
        trailer.put(DCCAttEnum.ReservadoCielo, StringUtilities.formatarCampoEmBranco(188));

        remessa.setTrailerRemessa(trailer);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(header);

        remessa.setTrailer(new StringBuilder(trailer.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

        List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
        for (ObjetoGenerico objetoGenerico : atributos) {
            preencherAtributosTransientes(remessa, objetoGenerico);
        }
    }
}

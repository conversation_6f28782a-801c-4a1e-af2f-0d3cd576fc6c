/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.cielo;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 *
 * <AUTHOR>
 */
public enum DCCCieloStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status", ""),
    Status00("00", "Transação OK  (TRANSAÇÃO ACEITA)", "", CodigoRetornoPactoEnum.SUCESSO),
    Status01("01", "Erro no arquivo", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status02("02", "Código de autorização inválido", "Entre em contato com a academia para verificar o seu cartão cadastrado."),
    Status03("03", "Estabelecimento inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status04("04", "Lote misturado", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status05("05", "Número de parcelas inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status06("06", "Diferença de valor no RO", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status07("07", "Número do RO inválido (registro BH)", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status08("08", "Valor de entrada inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status09("09", "Valor da taxa de embarque inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status10("10", "Valor da parcela inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status11("11", "Código de transação inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status12("12", "Transação inválida", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status13("13", "Valor inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status14("14", "Não aplicável", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status15("15", "Valor do cancelamento inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status16("16", "Transação original não localizada (para cancelamento)", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status17("17", "Nº de itens informados no RO não compatível com os CV?s", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status18("18", "Número de referência inválido", "Entre em contato com a sua academia para que verifiquem essa situação."),
    //
    Status20("20", "Cancelamento para parcelado de transação já cancelada", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status21("21", "Valor do cancelamento maior que o valor da venda", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status22("22", "Valor do cancelamento maior que o permitido (alçada)", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status23("23", "Número do RO original inválido (registro I2)", "Entre em contato com a sua academia para que verifiquem essa situação."),
    //
    Status42("42", "Cartão cancelado", "Entre em contato com a sua academia para regularizar os seus dados de pagamento cadastrados.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_CANCELADO),
    //
    Status54("54", "Não é permitido cancelamento parcial de um plano parcelado que está sendo contestado pelo portador.", "Entre em contato com o emissor do seu cartão de crédito para mais informações."),
    Status55("55", "Débito efetuado, conforme contestação do portador", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status56("56", "Tipo de plano de cartão inválido", "Entre em contato com o emissor do seu cartão para mais informações."),
    //
    Status59("59", "Tipo cartão inválido", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status60("60", "Data inválida", "Entre em contato com o emissor do seu cartão para mais informações."),
    //
    Status71("71", "Transação rejeitada pelo banco emissor", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status72("72", "Transação rejeitada pelo banco emissor", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status73("73", "Cartão com problema - reter o cartão", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status74("74", "Autorização negada", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status75("75", "Erro", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status76("76", "Transação rejeitada pelo banco emissor", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status77("77", "Erro de sintaxe - refaça a transação", "Entre em contato com a sua academia para que verifiquem essa situação."),
    Status78("78", "Não foi encontrada autorização no emissor", " Entre em contato com o emissor do seu cartão para mais informações."),
    Status79("79", "Cartão cancelado", "Entre em contato com a sua academia para regularizar os seus dados de pagamento cadastrados.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_CANCELADO),
    Status80("80", "Cartão cancelado", "Entre em contato com a sua academia para regularizar os seus dados de pagamento cadastrados.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_CANCELADO),
    Status81("81", "Fundos insuficientes", "Entre em contato com a sua academia para regularizar os seus dados de pagamento cadastrados.", OperacaoRetornoCobrancaEnum.REENVIAR, CodigoRetornoPactoEnum.SALDO_INSUFICIENTE),
    Status82("82", "Cartão vencido ou data do vencimento errada", "Entre em contato com a sua academia para regularizar os seus dados de pagamento cadastrados."),
    Status83("83", "Senha incorreta", ""),
    //
    Status87("87", "Cartão não permitido", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status88("88", "Excedeu o número de transações no período", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status89("89", "Mensagem difere da mensagem original", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status90("90", "CVV inválido", "Entre em contato com o emissor do seu cartão para mais informações.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_INVALIDO),
    Status91("91", "Impossível verificar senha", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status92("92", "Banco emissor sem comunicação", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status93("93", "Cancelamento com mais de 365 dias", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status94("94", "Duplicidade de linhas aéreas", "Entre em contato com o emissor do seu cartão para mais informações."),
    Status95("95", "Sem saldo em aberto", "Entre em contato com o emissor do seu cartão para mais informações."),
    //
    Status99("99", "Outros Motivos - Verificar junto a Cielo se foi cobrado e dar baixa manualmente", "Entre em contato com o emissor do seu cartão para mais informações.");
    private String id;
    private String descricao;
    private String acaoRealizar;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;
    private CodigoRetornoPactoEnum codigoRetornoPacto;

    private DCCCieloStatusEnum(String id, String descricao, String acaoRealizar) {
        this.id = id;
        this.descricao = descricao;
        this.acaoRealizar = acaoRealizar;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
    }

    private DCCCieloStatusEnum(String id, String descricao, String acaoRealizar, CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.id = id;
        this.descricao = descricao;
        this.acaoRealizar = acaoRealizar;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    private DCCCieloStatusEnum(String id, String descricao, String acaoRealizar, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca, CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.id = id;
        this.descricao = descricao;
        this.acaoRealizar = acaoRealizar;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCCCieloStatusEnum valueOff(String id) {
        DCCCieloStatusEnum[] values = DCCCieloStatusEnum.values();
        for (DCCCieloStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCCCieloStatusEnum.StatusNENHUM;
    }

    public String getAcaoRealizar() {
        return acaoRealizar;
    }

    public void setAcaoRealizar(String acaoRealizar) {
        this.acaoRealizar = acaoRealizar;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }
}

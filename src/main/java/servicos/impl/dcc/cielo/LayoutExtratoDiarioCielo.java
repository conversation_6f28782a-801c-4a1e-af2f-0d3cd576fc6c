/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.cielo;

import br.com.pactosolucoes.comuns.util.StringUtilities;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import servicos.impl.dcc.base.DCCAttEnum;

import static servicos.impl.dcc.base.LayoutRemessaBase.preencherAtributosTransientes;

import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import static servicos.impl.dcc.getnet.LayoutRemessaGetNetDCO.lerAtributos;

/**
 * <AUTHOR>
 */
public class LayoutExtratoDiarioCielo {
    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        // Esse método segue o layout 15.11 da Cielo.
        // link documento: https://desenvolvedores.cielo.com.br/api-portal/sites/default/files/CIELO_Extrato_Eletronico_Manual_Versao_15_11.pdf

        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            TipoConciliacaoEnum tipoConciliacaoEnum = null;
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("0")) {
                    lerAtributos(linha, h, tipoConciliacaoEnum);
                    tipoConciliacaoEnum = TipoConciliacaoEnum.getTipo(Integer.parseInt(StringUtilities.readString(47, 49, linha)));
                } else if (linha.startsWith("E") || linha.startsWith("1") || linha.startsWith("2")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail, tipoConciliacaoEnum);
                    if (detail.getAtributos().size() > 3) {
                        listaDetalheRetorno.add(detail);
                    }
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t, tipoConciliacaoEnum);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
            List<ObjetoGenerico> atributos = remessa.getTrailerRemessa().getAtributos();
            for (ObjetoGenerico objetoGenerico : atributos) {
                preencherAtributosTransientes(remessa, objetoGenerico);
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r, TipoConciliacaoEnum tipoConciliacaoEnum) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            //            001 001 1 Num. Tipo de registro Constante 0: identifica o tipo de registro header (início do arquivo).
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));//ok
            //        001 011  10 Num. Estabelecimento Matriz Número do estabelecimento matriz da cadeia de extrato eletrônico.
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 11, linha));//ok
            //        012 019 8 Num. Data de processamento AAAAMMDD data em que o arquivo foi gerado.
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(11, 19, linha));
            //        020 027  8 Num. Período inicial AAAAMMDD menor data de captura encontrada no movimento.
            r.put(DCCAttEnum.PeriodoInicial, StringUtilities.readString(19, 27, linha));
            //        028 035 8 Num. Período final AAAAMMDD maior data de captura encontrada no movimento.
            r.put(DCCAttEnum.PeriodoFinal, StringUtilities.readString(27, 35, linha));
            //        036 042 7 Num. Sequência Número sequencial do arquivo. Nos casos de recuperação, este dado será enviado como 9999999.
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(35, 42, linha));
            //        043 047 5 Alfa Empresa adquirente Constante Cielo.
            r.put(DCCAttEnum.IdentificadorEmpresaBeneficiaria, StringUtilities.readString(42, 47, linha));
            //        048 049 2 Num. Opção de extrato Vide Tabela I.
            r.put(DCCAttEnum.OpcaoExtrato, StringUtilities.readString(47, 49, linha));
            //        050 050 1 Alfa VAN OpenText (antiga GXS). TIVIT.
            r.put(DCCAttEnum.VAN, StringUtilities.readString(49, 50, linha));
            //        051 070 20 Alfanum. Caixa Postal Informação obtida no formulário de cadastro na VAN.
            r.put(DCCAttEnum.CaixaPostal, StringUtilities.readString(50, 70, linha));
            //        071 073 3 Num. Versão Layout Constante 001.
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(70, 73, linha));
            //        074 250 177 Alfanum. Uso Cielo Em Branco. Reservado para Cielo.
            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(73, 250, linha));


        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 250) {

                //Itentificar o tipo para determinar o Detalhe a ser utilizado
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));

                if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals("E")) {
                    r.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(14, 17, linha)); //001 cartão débito - 002 cartão crédito - OBS: A anotação anterior é com base na V15.4, mas na V15.11 é 001 cartão crédito - 002 cartão débito. Ainda não sei se isso gerou algum impacto no sistema, pois não tem cliente reclamando. Anotado para observar isso no futuro.
                    r.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(17, 19, linha));
                }

                if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals("1")) {

                    //001 001 1 Num. Tipo de registro Constante 1 - Identifica o tipo de registro detalhe do RO.
                    r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));//ok
                    //002 011  10 Num. Estabelecimento Submissor Número do estabelecimento e/ou filial onde a venda foi realizada.
                    r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 11, linha));//ok
                    //012 018 7 Num. Número do RO Número do resumo de operação. Contêm informações referentes a um grupo de vendas realizadas em uma determinada data.
                    r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(11, 18, linha));//ok
                    //019  020 2 Num. Parcela
                    r.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(18, 20, linha));//ok
                    //021 021 1 Alfa Filler Para vendas parceladas. aceleração das parcelas. demais situações.
                    r.put(DCCAttEnum.Filler, StringUtilities.readString(20, 21, linha));
                    //022 023 2 Alfanum. Plano
                    r.put(DCCAttEnum.Plano, StringUtilities.readString(21, 23, linha));
                    //024 025 2 Num. Tipo de Transação
                    r.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(23, 25, linha));
                    //026 031 6 Num. Data de apresentação AAMMDD Data em que o RO foi transmitido para a Cielo.
                    r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(25, 31, linha));
                    //032 037 6 Num. Data prevista de pagamento AAMMDD Data prevista de pagamento. Na recuperação, pode ser atualizada após o processamento da transação ou ajuste.
                    r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(31, 37, linha));
                    //038 043 6 Num. Data de envio para o banco (*****) AAMMDD Data em que o arquivo de pagamento foi enviado ao banco. Na recuperação, pode ser atualizada após o processamento da transação ou ajuste.
                    r.put(DCCAttEnum.DataEnvioBanco, StringUtilities.readString(37, 43, linha));
                    //044 044 1 Alfa Sinal valor bruto identifica valor a crédito. - identifica valor a débito.
                    r.put(DCCAttEnum.SinalValorBruto, StringUtilities.readString(43, 44, linha));
                    //045 057 13 Num. Valor bruto (*) Somatória dos valores de venda.
                    r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(44, 57, linha));
                    //058 058 1 Alfa Sinal da comissão (*****) identifica valor a crédito. - identifica valor a débito.
                    r.put(DCCAttEnum.SinalComissao, StringUtilities.readString(57, 58, linha));
                    //059  071 13 Num. Valor da comissão (*) (*****) Valor da comissão descontada sobre as vendas.
                    r.put(DCCAttEnum.Comissao, StringUtilities.readString(58, 71, linha));
                    //072 072 1 Alfa Sinal do valor rejeitado (****) identifica valor a crédito. - identifica valor a débito.
                    r.put(DCCAttEnum.SinalValorRejeitado, StringUtilities.readString(71, 72, linha));
                    //073 085 13 Num. Valor rejeitado (*) (****) Se houver rejeição, será preenchido com a somatória das transações rejeitadas.
                    r.put(DCCAttEnum.ValorRejeitado, StringUtilities.readString(72, 85, linha));
                    //086 086 1 Alfa Sinal do valor líquido identifica o valor a crédito. - identifica o valor a débito.
                    r.put(DCCAttEnum.SinalValorLiquido, StringUtilities.readString(85, 86, linha));
                    //087 099 13 Num. Valor líquido (*) Valor das vendas descontado o valor da comissão.
                    r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(86, 99, linha));
                    //100 103 4 Num. Banco Código do banco no qual os valores foram depositados.
                    r.put(DCCAttEnum.Banco, StringUtilities.readString(99, 103, linha));
                    //104 108 5 Num. Agência (*****) Código da agência na qual os valores foram depositados.
                    r.put(DCCAttEnum.Agencia, StringUtilities.readString(103, 108, linha));
                    //109 122 14 Alfanum. Conta-corrente (*****) Conta-corrente na qual os valores foram depositados.
                    r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(108, 122, linha));
                    //123 124 02 Num. Status do pagamento
                    r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(122, 124, linha));
                    //125 130 6 Num. Quantidade de CVs aceitos Quantidades de vendas aceitas no RO.
                    r.put(DCCAttEnum.QtdCV, StringUtilities.readString(124, 130, linha));

                    //131 132 2  Num. Código do Produt (Desconsiderar) A partir de 01/03/2014, o Identificador do produto passa a ser enviado nas posições 233-235 com três caracteres. Desconsidere a informação enviada nesta posição.

                    //133 138 6 Num. Quantidade de vendas rejeitadas no RO.
                    r.put(DCCAttEnum.QtdCVRejeitado, StringUtilities.readString(132, 138, linha));
                    //139 139 1 Alfa Identificador de revenda/aceleração (*****) Identifica as ocorrências de manutenção em transações parceladas na loja: - Revenda - Aceleração Brancos (nenhuma ocorrência)
                    r.put(DCCAttEnum.RevendaAceleracao, StringUtilities.readString(138, 139, linha));
                    //140 145 6 Num. Data da captura de transação (*****) AAMMDD - Data em que a transação foi capturada pela Cielo. Na recuperação, pode ser atualizada após o processamento da transação ou ajuste.
                    r.put(DCCAttEnum.DataCapturaTransacao, StringUtilities.readString(139, 145, linha));
                    //146  147  2 Alfanum. Origem do ajuste (****)
                    r.put(DCCAttEnum.OrigemAjuste, StringUtilities.readString(145, 147, linha));
                    //148 160 13 Num. Valor complementar (*****) Valor do saque quando o produto for igual a 36 ou valor do Agro Electron para transações dos produtos 22 23 ou 25 apresentados na Tabela IV.
                    r.put(DCCAttEnum.ValorComplementar, StringUtilities.readString(147, 160, linha));
                    //161 161 1 Alfa  Identificador de Antecipação Identificador de antecipação do RO: Não antecipado; Antecipado na Cielo ARV; Antecipado no banco Cessão de Recebíveis.
                    r.put(DCCAttEnum.IdentificadorProdutoFinanceiro, StringUtilities.readString(160, 161, linha));
                    //162 170 9 Num. Número da operação de Antecipação Identifica o número da operação de Antecipação apresentada no registro tipo 5 campo 12 ao 20, associada ao RO antecipado/cedido na Cielo ou no banco.
                    r.put(DCCAttEnum.NumeroOperacaoFinanceira, StringUtilities.readString(161, 170, linha));
                    //171 171 1 Alfa Sinal do valor Bruto antecipado identifica valor a crédito. - identifica valor a débito.
                    r.put(DCCAttEnum.SinalValorBrutoAntecipado, StringUtilities.readString(170, 171, linha));
                    //172 184 13 Num. Valor Bruto Antecipado (*) Valor bruto antecipado, fornecido quando o RO for antecipado/cedido. Será preenchido com zeros quando não houver antecipação. O valor bruto antecipado corresponde ao valor líquido do RO.
                    r.put(DCCAttEnum.ValorBrutoAntecipado, StringUtilities.readString(171, 184, linha));
                    //185 187 3 Num. Bandeira Código da Bandeira vide tabela VI.
                    r.put(DCCAttEnum.CodigoBandeira, StringUtilities.readString(184, 187, linha));
                    //188 209 22 Num. Número Único do RO
                    r.put(DCCAttEnum.NumeroUnicoRO, StringUtilities.readString(187, 209, linha));
                    //210 213 4 Num. Taxa de Comissão (*) (*****) Percentual de comissão aplicado no valor da transação.
                    r.put(DCCAttEnum.TaxaComissao, StringUtilities.readString(209, 213, linha));
                    //214 218 5 Num. Tarifa (*) (**) (*****) Tarifa cobrada por transação.
                    r.put(DCCAttEnum.Tarifa, StringUtilities.readString(213, 218, linha));
                    //219 222 4 Num. Taxa de Garantia (*) (**) (*****) Percentual de desconto aplicado sobre transações Electron Pré-Datado.
                    r.put(DCCAttEnum.TaxaGarantia, StringUtilities.readString(218, 222, linha));
                    //223 224 2 Num. Meio de Captura (*****) Vide tabela VII. Caso a venda tenha sido reprocessada, o sistema enviará o meio de captura 06: Meio de captura manual. Neste caso, desconsiderar o valor informado no número lógico do terminal. Campo não disponível para vendas a débito no arquivo de pagamento diário e segunda parcela em diante das vendas parceladas no arquivo de pagamento diário e recuperado.
                    r.put(DCCAttEnum.MeioCaptura, StringUtilities.readString(222, 224, linha));
                    //225 232 8 Num. Número lógico do terminal (*****) Número lógico do terminal onde foi efetuada a venda. Quando o meio de captura for igual a 06, desconsiderar o número lógico do terminal, pois este será um número interno da Cielo.
                    r.put(DCCAttEnum.NumeroLogicoTerminal, StringUtilities.readString(224, 232, linha));
                    //233 235 3 Num. Código do Produto Código que identifica o produto vide Tabela IV.
                    r.put(DCCAttEnum.IdentificadorProduto, StringUtilities.readString(232, 235, linha));
                    //236 245 10 Num. Matriz de Pagamento (***) Estabelecimento matriz da cadeia centralizada de pagamento.
                    r.put(DCCAttEnum.MatrizPagamento, StringUtilities.readString(235, 245, linha));
                    //246 250 5 Alfanum. Uso Cielo Em Branco. Reservado para Cielo.
                    r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(245, 250, linha));


                } else if (r.getValue(DCCAttEnum.TipoRegistro.name()).equals("2")) {

                    r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
                    r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 11, linha));
                    r.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(11, 18, linha));
                    r.put(DCCAttEnum.NumeroCartao, StringUtilities.readString(18, 37, linha));
                    r.put(DCCAttEnum.DataVenda, StringUtilities.readString(37, 45, linha));
                    r.put(DCCAttEnum.SinalValor, StringUtilities.readString(45, 46, linha));
                    r.put(DCCAttEnum.ValorParcela, StringUtilities.readString(46, 59, linha));
                    r.put(DCCAttEnum.NumeroParcela, StringUtilities.readString(59, 61, linha));
                    r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(61, 63, linha));
                    r.put(DCCAttEnum.Motivo, StringUtilities.readString(63, 66, linha));
                    r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(66, 72, linha));
                    r.put(DCCAttEnum.TID, StringUtilities.readString(72, 92, linha));
                    r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(92, 98, linha));
                    r.put(DCCAttEnum.ValorComplementar, StringUtilities.readString(98, 111, linha));
                    r.put(DCCAttEnum.Dig, StringUtilities.readString(111, 113, linha));
                    r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(113, 126, linha));
                    r.put(DCCAttEnum.ValorProximaParcela, StringUtilities.readString(126, 139, linha));
                    r.put(DCCAttEnum.NumeroNotaFiscal, StringUtilities.readString(139, 148, linha));
                    r.put(DCCAttEnum.IndicadorCartaoExterior, StringUtilities.readString(148, 152, linha));
                    r.put(DCCAttEnum.NumeroLogicoTerminal, StringUtilities.readString(152, 160, linha));

//                r.put(DCCAttEnum.Tra, StringUtilities.readString(160, 162, linha));
//                r.put(DCCAttEnum.Codigo, StringUtilities.readString(162, 182, linha));

                    r.put(DCCAttEnum.HoraTransacao, StringUtilities.readString(182, 188, linha));
                    r.put(DCCAttEnum.NumeroUnicoTransacao, StringUtilities.readString(188, 217, linha));
                    r.put(DCCAttEnum.IndicadorCieloPromo, StringUtilities.readString(217, 218, linha));
                    r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(218, 250, linha));
                } else if (carregarDetalhes(r, tipoConciliacaoEnum)) {
                    //002  011  10 Num.       Estabelecimento Submissor Número do estabelecimento que efetuou a venda/ajuste.
                    r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(1, 11, linha));
                    //012  014  3  Num.       Bandeira Liquidação Código da Bandeira vide tabela III.
                    r.put(DCCAttEnum.CodigoBandeira, StringUtilities.readString(11, 14, linha));
                    //020  021  2  Num.       Plano da venda - Número total de parcelas da venda
                    r.put(DCCAttEnum.QuantidadeParcelas, StringUtilities.readString(19, 21, linha));
                    //022  027  6  Alpha/Num. Código Autorização - Não é único e deve ser validado junto com outros dados, como a Empresa ou Tipo de Liquidação
                    r.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.readString(21, 27, linha));
                    //028  029  2  Num. Tipo de Lançamento - Nosso sistema trabalha com: 01 - Venda Débito, 02 - Venda Crédito, 06 - Cancelamento, 07 - Reversão Cancelamento, 08 - Chargeback, 09 - Reversão Chargeback
                    r.put(DCCAttEnum.TipoLancamento, StringUtilities.readString(27, 29, linha));
                    //566  573  8  Num.       Data de autorização DDMMAAAA
                    r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(565, 573, linha));
                    //630  637  8  Num.       Data de pagamento original DDMMAAAA
                    r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(629, 637, linha));
                    //166  175  10 Num.       Número do cartão
                    r.put(DCCAttEnum.NumeroCartao, (StringUtilities.readString(165, 171, linha) + "******" + StringUtilities.readString(171, 175, linha)));

                    if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS)) {
                        //247  247  1  Alfa       Sinal valor total da venda
                        r.put(DCCAttEnum.SinalValorBruto, StringUtilities.readString(246, 247, linha));
                        //248  260  13 Num.       Valor bruto (*) Valor da compra, no caso de venda parcelada será informado o total da venda.
                        r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(247, 260, linha));
                    } else if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                        //261  261  1  Alfa       Sinal valor total da parcela
                        r.put(DCCAttEnum.SinalValorBruto, StringUtilities.readString(260, 261, linha));
                        //262  274  13 Num.       Valor bruto da parcela
                        r.put(DCCAttEnum.ValorBruto, StringUtilities.readString(261, 274, linha));
                    }

                    //289  289  1  Alfa       Sinal da comissão
                    r.put(DCCAttEnum.SinalComissao, StringUtilities.readString(288, 289, linha));
                    //290  302  13 Num.       Valor da comissão - valor por parcela x número parcelas
                    r.put(DCCAttEnum.Comissao, StringUtilities.readString(289, 302, linha));
                    //275  275  1  Alfa       Sinal do valor líquido
                    r.put(DCCAttEnum.SinalValorLiquido, StringUtilities.readString(274, 275, linha));
                    //276  288 13  Num.       Valor líquido - valor por parcela x número parcelas
                    r.put(DCCAttEnum.ValorLiquido, StringUtilities.readString(275, 288, linha));
                    //242  246  5  Num.       Taxa da Venda - percentual de taxa aplicado ao valor da venda
                    r.put(DCCAttEnum.TaxaComissao, StringUtilities.readString(241, 246, linha));
                    //156  158  3  Num.       Forma de pagamento - código do Produto vide Tabela V.
                    r.put(DCCAttEnum.IdentificadorProduto, StringUtilities.readString(155, 158, linha));
                    //541  543  3  Num.       Meio de Captura - Vide tabela VII.
                    r.put(DCCAttEnum.MeioCaptura, StringUtilities.readString(540, 543, linha));
                    //544  551  8  Num.       Número do Terminal - Número lógico do terminal onde foi efetuada a venda.
                    r.put(DCCAttEnum.NumeroLogicoTerminal, StringUtilities.readString(152, 160, linha));
                    //638  647 10  Num.       Matriz de Pagamento - Estabelecimento matriz da cadeia centralizada de pagamento.
                    r.put(DCCAttEnum.MatrizPagamento, StringUtilities.readString(637, 647, linha));
                    //176  181  6  Num.       NSU/DOC - Identifica a transação no dia, mas pode ser alterado, não servindo para conciliação.
                    r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(175, 181, linha));
                    //182  191 10  Num.       Número Nota
                    r.put(DCCAttEnum.NumeroNotaFiscal, StringUtilities.readString(181, 191, linha));
                    //192  211 20  Alpha/Num. Código de pedido
                    r.put(DCCAttEnum.TID, StringUtilities.readString(72, 92, linha));

                }
            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
//          001 001 1 Num. Tipo de registro Constante 9: identifica o tipo de registro trailer (final do arquivo).
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
//          031 041 11 Num. Total registro tipo "E" Quantidade total de registros tipo E - Detalhes.
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(30, 41, linha));
//            r.put(DCCAttEnum.ReservadoCielo, StringUtilities.readString(41, 250, linha));

        }
    }

    private static boolean carregarDetalhes(RegistroRemessa r, TipoConciliacaoEnum tipoConciliacaoEnum) {
        boolean carregar = false;
        String tipoRegistro = r.getValue(DCCAttEnum.TipoRegistro.name()); //E - para movimentos analíticos da UR
        String tipoLiquidacao = r.getValue(DCCAttEnum.TipoOperacao.name()); //001 cartão débito - 002 cartão crédito
        String parcela = r.getValue(DCCAttEnum.NumeroParcela.name()); //01 ou 00 para Venda, só carregar se for a linha da parcela 1 ou 0.

        /*
        * Layout 15 Cielo
        * Carregar apenas dados de linha E
        * Venda - Tudo de Cartão de Débito
        * Venda - Apenas se for a linha da parcela 1 para Cartão de Crédito.
        */
        if (tipoRegistro.equals("E") && tipoConciliacaoEnum.equals(TipoConciliacaoEnum.VENDAS) &&
                (tipoLiquidacao.equals("001") || (tipoLiquidacao.equals("002") && (parcela.equals("01") || parcela.equals("00"))))) {
            carregar = true;
        } else if (tipoConciliacaoEnum.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
            carregar = true;
        }
        return carregar;
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.maxiPago;

import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;

/*
 * Created by <PERSON><PERSON> on 15/08/2017.
 */
public enum MaxiPagoResportaTransacaoEnum {


    APROVADA("0" , "Aprovada", SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO),
    NEGADA("1", "Negada", SituacaoTransacaoEnum.NAO_APROVADA),
    NEGADA_DUPLICIDADE ("2", "Negada por Duplicidade ou Fraude", SituacaoTransacaoEnum.NAO_APROVADA),
    EM_REVISAO("5", "Em Revisão (Análise Manual de Fraude", SituacaoTransacaoEnum.APROVADA),
    ERRO_OPERADORA("1022" , "Erro na operadora de cartão", SituacaoTransacaoEnum.NAO_APROVADA),
    ERRO_PARAMETROS("1024", "Erro nos parâmetros enviados", SituacaoTransacaoEnum.NAO_APROVADA), // Ver 'responseMessage' para mais informações
    ERRO_CREDENCIAIS ("1025", "Erro nas credenciais", SituacaoTransacaoEnum.NAO_APROVADA),
    ERRO_MAXIPAGO("2048", "Erro interno na maxiPago", SituacaoTransacaoEnum.NAO_APROVADA),
    TIMEOUT("4097", "Timeout com a adquirente", SituacaoTransacaoEnum.NAO_APROVADA);

    private String id;
    private String descricao;
    private SituacaoTransacaoEnum situacaoTransacaoEnum;

    private MaxiPagoResportaTransacaoEnum(String id, String descricao, SituacaoTransacaoEnum situacaoTransacaoEnum) {
        this.id = id;
        this.descricao = descricao;
        this.situacaoTransacaoEnum = situacaoTransacaoEnum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static MaxiPagoResportaTransacaoEnum valueOff(String id) {
        MaxiPagoResportaTransacaoEnum[] values = MaxiPagoResportaTransacaoEnum.values();
        for (MaxiPagoResportaTransacaoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public SituacaoTransacaoEnum getSituacaoTransacaoEnum() {
        return situacaoTransacaoEnum;
    }

    public void setSituacaoTransacaoEnum(SituacaoTransacaoEnum situacaoTransacaoEnum) {
        this.situacaoTransacaoEnum = situacaoTransacaoEnum;
    }

    public static SituacaoTransacaoEnum retornaSituacaoTransacaoPeloStatus(String id) {
        MaxiPagoResportaTransacaoEnum[] values = MaxiPagoResportaTransacaoEnum.values();
        for (MaxiPagoResportaTransacaoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum.getSituacaoTransacaoEnum();
            }
        }
        return SituacaoTransacaoEnum.NENHUMA;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.maxiPago;

import negocio.comuns.financeiro.enumerador.TipoDebitoOnlineEnum;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 15/08/2017.
 */
public enum AdquirenteMaxiPagoEnum {


    NENHUM(0, "(Nenhum)"),
    SIMULADOR_TESTES(1, "SIMULADOR DE TESTES"),
    REDE(2, "Rede"),
    GETNET(3, "GetNet"),
    CIELO(4, "Cielo"),
    TEF(5, "TEF"),
    ELAVON(6, "Elavon"),
    CHASEPAYMENTECH(8, "ChasePaymentech");

    private Integer id;
    private String descricao;

    private AdquirenteMaxiPagoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static AdquirenteMaxiPagoEnum valueOff(Integer id) {
        AdquirenteMaxiPagoEnum[] values = AdquirenteMaxiPagoEnum.values();
        for (AdquirenteMaxiPagoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return AdquirenteMaxiPagoEnum.NENHUM;
    }

    public static List getSelectListAdquirenteMaxiPago() {
        List temp = new ArrayList<AdquirenteMaxiPagoEnum>();
        for (AdquirenteMaxiPagoEnum tipo : AdquirenteMaxiPagoEnum.values()) {
            temp.add(new SelectItem(tipo, tipo.getDescricao()));
        }
        return temp;
    }

}

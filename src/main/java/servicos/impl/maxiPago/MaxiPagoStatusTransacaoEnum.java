/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.maxiPago;

import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;

/*
 * Created by <PERSON><PERSON> on 15/08/2017.
 */
public enum MaxiPagoStatusTransacaoEnum {


    EM_ANDAMENTO("1", "Em andamento", SituacaoTransacaoEnum.APROVADA),
    CAPTURADA("3", "Capturada", SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO),
    AUTORIZADA("6", "Autorizada", SituacaoTransacaoEnum.APROVADA),
    NEGADA("7", "Negada", SituacaoTransacaoEnum.NAO_APROVADA),
    CANCELADA("9", "Cancelada (Voided)", SituacaoTransacaoEnum.CANCELADA),
    PAGA("10", "Paga", SituacaoTransacaoEnum.NAO_APROVADA),
    BOLETO_EMITIDO("22", "Boleto Emitido", SituacaoTransacaoEnum.NAO_APROVADA),
    BOLETO_VISUALIZADO("34", "Boleto Visualizado", SituacaoTransacaoEnum.NAO_APROVADA),
    BOLETO_PAGO_MENOR("35", "Boleto Pago A Menor", SituacaoTransacaoEnum.NAO_APROVADA),
    BOLETO_PAGO_MAIOR("36", "Boleto Page A Maior", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_CAPTURA("4", "Pendente de captura", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_AUTORIZACAO("5", "Pendente de autorização", SituacaoTransacaoEnum.NAO_APROVADA),
    REVERTIDA("8", "Revertida", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_CONFIRMACAO("11", "Pendente de Confirmação", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_REVISAO("12", "Pendente de Revisão (verificar com Suporte)", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_REVERSAO("13", "Pendente de Reversão", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_CAPTURA_RETENTATIVA("14", "Pendente de Captura (retentativa)", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_ESTORNO("16", "Pendente de Estorno", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_VOID("18", "Pendente de Void", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_VOID_RETENTATIVA("19", "Pendente de Void (retentativa)", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_AUTENTICACAO("29", "Pendente de Autenticação", SituacaoTransacaoEnum.NAO_APROVADA),
    AUTENTICADA("30", "Autenticada", SituacaoTransacaoEnum.NAO_APROVADA),
    PENDENTE_ESTORNO_RETENTATIVA("31", "Pendente de Estorno (retentativa)", SituacaoTransacaoEnum.NAO_APROVADA),
    AUTENTICACAO_EM_ANDAMENTO("32", "Autenticação em andamento", SituacaoTransacaoEnum.NAO_APROVADA),
    AUTENTICACAO_ENVIADA("33", "Autenticação enviada", SituacaoTransacaoEnum.NAO_APROVADA),
    AUTENTICACAO_ENVIO_ARQUIVO_ESTORNO("38", "Pendente de envio de arquivo de Estorno", SituacaoTransacaoEnum.NAO_APROVADA),
    APROVADA_NA_FRAUDE("44", "Aprovada na Fraude", SituacaoTransacaoEnum.NAO_APROVADA),
    NEGADA_POR_FRAUDE("45", "Negada por Fraude", SituacaoTransacaoEnum.NAO_APROVADA),
    REVISAO_DE_FRAUDE("46", "Revisão de Fraude", SituacaoTransacaoEnum.NAO_APROVADA);

    private String id;
    private String descricao;
    private SituacaoTransacaoEnum situacaoTransacaoEnum;

    private MaxiPagoStatusTransacaoEnum(String id, String descricao, SituacaoTransacaoEnum situacaoTransacaoEnum) {
        this.id = id;
        this.descricao = descricao;
        this.situacaoTransacaoEnum = situacaoTransacaoEnum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static MaxiPagoStatusTransacaoEnum valueOff(String id) {
        MaxiPagoStatusTransacaoEnum[] values = MaxiPagoStatusTransacaoEnum.values();
        for (MaxiPagoStatusTransacaoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public SituacaoTransacaoEnum getSituacaoTransacaoEnum() {
        return situacaoTransacaoEnum;
    }

    public void setSituacaoTransacaoEnum(SituacaoTransacaoEnum situacaoTransacaoEnum) {
        this.situacaoTransacaoEnum = situacaoTransacaoEnum;
    }

    public static SituacaoTransacaoEnum retornaSituacaoTransacaoPeloStatus(String id) {
        MaxiPagoStatusTransacaoEnum[] values = MaxiPagoStatusTransacaoEnum.values();
        for (MaxiPagoStatusTransacaoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum.getSituacaoTransacaoEnum();
            }
        }
        return SituacaoTransacaoEnum.NENHUMA;
    }
}

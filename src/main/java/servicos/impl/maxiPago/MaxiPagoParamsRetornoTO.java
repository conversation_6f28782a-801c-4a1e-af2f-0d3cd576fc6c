package servicos.impl.maxiPago;

/*
 * Created by <PERSON> on 19/07/2022.
 */

import maxiPago.DataContract.Transactional.TransactionResponse;
import org.json.JSONObject;

public class MaxiPagoParamsRetornoTO {

    private String authCode;
    private String orderID;
    private String referenceNum;
    private String transactionID;
    private String transactionTimestamp;
    private String responseCode;
    private String responseMessage;
    private String avsResponseCode;
    private String cvvResponseCode;
    private String processorCode;
    private String processorMessage;
    private String errorMessage;
    private String processorReferenceNumber;
    private String processorTransactionID;
    private String boletoURL;
    private String authenticationURL;
    private String fraudScore;
    private String saveOnFile;
    private String onlineDebitUrl;
    private String partiallyApprovedAmount;

    public MaxiPagoParamsRetornoTO(String authCode, String orderID, String referenceNum, String transactionID, String transactionTimestamp, String responseCode, String responseMessage, String avsResponseCode, String cvvResponseCode, String processorCode, String processorMessage, String errorMessage, String processorReferenceNumber, String processorTransactionID, String boletoURL, String authenticationURL, String fraudScore, String saveOnFile, String onlineDebitUrl, String partiallyApprovedAmount) {
        this.authCode = authCode;
        this.orderID = orderID;
        this.referenceNum = referenceNum;
        this.transactionID = transactionID;
        this.transactionTimestamp = transactionTimestamp;
        this.responseCode = responseCode;
        this.responseMessage = responseMessage;
        this.avsResponseCode = avsResponseCode;
        this.cvvResponseCode = cvvResponseCode;
        this.processorCode = processorCode;
        this.processorMessage = processorMessage;
        this.errorMessage = errorMessage;
        this.processorReferenceNumber = processorReferenceNumber;
        this.processorTransactionID = processorTransactionID;
        this.boletoURL = boletoURL;
        this.authenticationURL = authenticationURL;
        this.fraudScore = fraudScore;
        this.saveOnFile = saveOnFile;
        this.onlineDebitUrl = onlineDebitUrl;
        this.partiallyApprovedAmount = partiallyApprovedAmount;
    }

    public MaxiPagoParamsRetornoTO(TransactionResponse transactionResponse) {
        this.authCode = transactionResponse.getAuthCode();
        this.orderID = transactionResponse.getOrderID();
        this.referenceNum = transactionResponse.getReferenceNum();
        this.transactionID = transactionResponse.getTransactionID();
        this.transactionTimestamp = transactionResponse.getTransactionTimestamp();
        this.responseCode = transactionResponse.getResponseCode();
        this.responseMessage = transactionResponse.getResponseMessage();
        this.avsResponseCode = transactionResponse.getAvsResponseCode();
        this.cvvResponseCode = transactionResponse.getCvvResponseCode();
        this.processorCode = transactionResponse.getProcessorCode();
        this.processorMessage = transactionResponse.getProcessorMessage();
        this.errorMessage = transactionResponse.getErrorMessage();
        this.processorReferenceNumber = transactionResponse.getProcessorReferenceNumber();
        this.processorTransactionID = transactionResponse.getProcessorTransactionID();
        this.boletoURL = transactionResponse.getBoletoUrl();
        this.authenticationURL = transactionResponse.getAuthenticationURL();
        this.fraudScore = transactionResponse.getFraudScore();
        this.onlineDebitUrl = transactionResponse.getOnlineDebitURL();
    }

    public JSONObject toJSON(){
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("authCode", getAuthCode());
        jsonObject.put("orderID", getOrderID());
        jsonObject.put("referenceNum", getReferenceNum());
        jsonObject.put("transactionID", getTransactionID());
        jsonObject.put("transactionTimestamp", getTransactionTimestamp());
        jsonObject.put("responseCode", getResponseCode());
        jsonObject.put("responseMessage", getResponseMessage());
        jsonObject.put("avsResponseCode", getAvsResponseCode());
        jsonObject.put("cvvResponseCode", getCvvResponseCode());
        jsonObject.put("processorCode", getProcessorCode());
        jsonObject.put("processorMessage", getProcessorMessage());
        jsonObject.put("errorMessage", getErrorMessage());
        jsonObject.put("processorReferenceNumber", getProcessorReferenceNumber());
        jsonObject.put("processorTransactionID", getProcessorTransactionID());
        jsonObject.put("boletoURL", getBoletoURL());
        jsonObject.put("authenticationURL", getAuthenticationURL());
        jsonObject.put("fraudScore", getFraudScore());
        jsonObject.put("onlineDebitUrl", getOnlineDebitUrl());

        return jsonObject;
    }

    public MaxiPagoParamsRetornoTO() {
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getOrderID() {
        return orderID;
    }

    public void setOrderID(String orderID) {
        this.orderID = orderID;
    }

    public String getReferenceNum() {
        return referenceNum;
    }

    public void setReferenceNum(String referenceNum) {
        this.referenceNum = referenceNum;
    }

    public String getTransactionID() {
        return transactionID;
    }

    public void setTransactionID(String transactionID) {
        this.transactionID = transactionID;
    }

    public String getTransactionTimestamp() {
        return transactionTimestamp;
    }

    public void setTransactionTimestamp(String transactionTimestamp) {
        this.transactionTimestamp = transactionTimestamp;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public String getAvsResponseCode() {
        return avsResponseCode;
    }

    public void setAvsResponseCode(String avsResponseCode) {
        this.avsResponseCode = avsResponseCode;
    }

    public String getCvvResponseCode() {
        return cvvResponseCode;
    }

    public void setCvvResponseCode(String cvvResponseCode) {
        this.cvvResponseCode = cvvResponseCode;
    }

    public String getProcessorCode() {
        return processorCode;
    }

    public void setProcessorCode(String processorCode) {
        this.processorCode = processorCode;
    }

    public String getProcessorMessage() {
        return processorMessage;
    }

    public void setProcessorMessage(String processorMessage) {
        this.processorMessage = processorMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getProcessorReferenceNumber() {
        return processorReferenceNumber;
    }

    public void setProcessorReferenceNumber(String processorReferenceNumber) {
        this.processorReferenceNumber = processorReferenceNumber;
    }

    public String getProcessorTransactionID() {
        return processorTransactionID;
    }

    public void setProcessorTransactionID(String processorTransactionID) {
        this.processorTransactionID = processorTransactionID;
    }

    public String getBoletoURL() {
        return boletoURL;
    }

    public void setBoletoURL(String boletoURL) {
        this.boletoURL = boletoURL;
    }

    public String getAuthenticationURL() {
        return authenticationURL;
    }

    public void setAuthenticationURL(String authenticationURL) {
        this.authenticationURL = authenticationURL;
    }

    public String getFraudScore() {
        return fraudScore;
    }

    public void setFraudScore(String fraudScore) {
        this.fraudScore = fraudScore;
    }

    public String getSaveOnFile() {
        return saveOnFile;
    }

    public void setSaveOnFile(String saveOnFile) {
        this.saveOnFile = saveOnFile;
    }

    public String getOnlineDebitUrl() {
        return onlineDebitUrl;
    }

    public void setOnlineDebitUrl(String onlineDebitUrl) {
        this.onlineDebitUrl = onlineDebitUrl;
    }

    public String getPartiallyApprovedAmount() {
        return partiallyApprovedAmount;
    }

    public void setPartiallyApprovedAmount(String partiallyApprovedAmount) {
        this.partiallyApprovedAmount = partiallyApprovedAmount;
    }
}

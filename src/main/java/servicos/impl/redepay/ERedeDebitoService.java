package servicos.impl.redepay;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.interfaces.ERedeDebitoServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;

/*
 * Created by Luiz Felipe on 21/09/2017.
 */
public class ERedeDebitoService extends CobrancaOnlineService implements ERedeDebitoServiceInterface {

    private String urlApiERede = PropsService.getPropertyValue(PropsService.urlApiRedeProducao);
    private Pessoa pessoa;
    private Log log;
    private Transacao transacao;
    private String numeroFiliacao;
    private String token;
    private UsuarioVO usuarioLogado;
    private FormaPagamentoVO formaPagamentoVO;
    private FormaPagamento formaPagamento;
    private String urlReturn;
    private MovPagamentoVO movPagamentoVO;

    public ERedeDebitoService(Connection con, UsuarioVO usuarioLogado, MovPagamentoVO movPagamentoVO, String returnURL) throws Exception {
        super(con);
        this.transacao = new Transacao(con);
        this.formaPagamento = new FormaPagamento(con);
        this.usuarioLogado = usuarioLogado;
        this.movPagamentoVO = movPagamentoVO;
        this.urlReturn = returnURL;
        verificarFormaPagamento();
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.formaPagamentoVO != null) {
            this.numeroFiliacao = this.formaPagamentoVO.getMerchantid();
            this.token = this.formaPagamentoVO.getMerchantkey();
        }
    }

    private void verificarFormaPagamento() throws Exception {
        if (this.formaPagamentoVO == null) {
            this.formaPagamentoVO = formaPagamento.consultarPorChavePrimaria(movPagamentoVO.getFormaPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = criarTransacao(dadosCartao);
        JSONObject parametrosPagamento = criarParametrosPagamento(dadosCartao);
        transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
        transacao.setCodigo(0);
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        new Transacao(con).incluir(transacao);
        String retorno = executarRequestRede(parametrosPagamento.toString(), "/transactions", ExecuteRequestHttpService.METODO_POST);
        processarRetorno(transacao, retorno);
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                consultarPeloCodigoReferencia(transacao);
            }
            consultarSituacaoTransacao(transacao);
        }
        new Transacao(con).alterar(transacao);
        return transacao;
    }

    private TransacaoVO criarTransacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = new TransacaoERedeVO();
        transacao.setUrlTransiente(dadosCartao.getUrl());
        transacao.setUsuarioResponsavel(dadosCartao.getUsuarioResponsavel());
        transacao.setNomePessoa(dadosCartao.getNomeTitular());
        transacao.setDataProcessamento(Calendario.hoje());
        transacao.setTipo(TipoTransacaoEnum.E_REDE_DEBITO);
        transacao.setEmpresa(dadosCartao.getEmpresa());
        transacao.setListaParcelas(dadosCartao.getListaParcelas());
        transacao.setValor(Uteis.arredondarForcando2CasasDecimais(dadosCartao.getValor()));
        if (dadosCartao.getIdPessoaCartao() != null && !UteisValidacao.emptyNumber(dadosCartao.getIdPessoaCartao())) {
            PessoaVO pessoa = new PessoaVO();
            pessoa.setCodigo(dadosCartao.getIdPessoaCartao());
            transacao.setPessoaPagador(pessoa);
        }
        return transacao;
    }

    private JSONObject criarParametrosPagamento(CartaoCreditoTO dadosCartao) throws Exception {
        JSONObject pagamento = new JSONObject();

        pagamento.put("capture", false);
        pagamento.put("reference", Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss"));
        pagamento.put("amount", StringUtilities.formatarCampoMonetario(dadosCartao.getValor(), 10));
        pagamento.put("cardHolderName", Uteis.retirarAcentuacao(dadosCartao.getNomeTitular()));
        if (!UteisValidacao.emptyString(dadosCartao.getNumero())) {
            pagamento.put("cardNumber", dadosCartao.getNumero());
        }
        pagamento.put("expirationMonth", dadosCartao.getMesValidade());
        pagamento.put("expirationYear", dadosCartao.getAnoValidade());
        if (!UteisValidacao.emptyString(dadosCartao.getCodigoSeguranca())) {
            pagamento.put("securityCode", dadosCartao.getCodigoSeguranca());
        }

        JSONObject threeDSecure = new JSONObject();
        threeDSecure.put("embedded", true);
        threeDSecure.put("onFailure", "decline");
        threeDSecure.put("userAgent", SuperControle.getUserAgent());
        pagamento.put("threeDSecure", threeDSecure);

        JSONArray array = new JSONArray();
        JSONObject objectSucess = new JSONObject();
        objectSucess.put("kind", "threeDSecureSuccess");
        objectSucess.put("url",  getUrlReturn());
        array.put(objectSucess);
        JSONObject objectFailure = new JSONObject();
        objectFailure.put("kind", "threeDSecureFailure");
        objectFailure.put("url",  getUrlReturn());
        array.put(objectFailure);
        pagamento.put("urls", array);

        return pagamento;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) throws Exception {
        JSONObject payment = new JSONObject(parametrosPagamento.toString());
        payment.put("cardNumber", APF.encriptar(payment.getString("cardNumber")));

        try {
            String securityCode = payment.getString("securityCode");
            if (!UteisValidacao.emptyString(securityCode)) {
                payment.put("securityCode", APF.encriptar(securityCode));
            }
        } catch (Exception ignored) {
        }
        return payment.toString();
    }

    @Override
    public String consultarTransacao(TransacaoVO transacaoVO) throws Exception {
        return executarRequestRede(null, "/transactions/" + transacaoVO.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacao;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return retransmitirTransacao(transacaoOriginal, contratoRecorrencia, cliente);
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        transacaoOriginal.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoNova, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        transacaoNova.setDataProcessamento(Calendario.hoje());
        Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
        new Transacao(con).incluir(transacaoNova);
        JSONObject parametrosEnviar = decifrarDadosSigilososReEnvio(transacaoNova);
        String retorno = executarRequestRede(parametrosEnviar.toString(), "/1/sales", ExecuteRequestHttpService.METODO_POST);
        processarRetorno(transacaoNova, retorno);
        if (transacaoNova.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            consultarSituacaoTransacao(transacaoNova);
        }
        new Transacao(con).alterar(transacaoNova);
        return transacaoNova;
    }

    private JSONObject decifrarDadosSigilososReEnvio(TransacaoVO transacaoVO) throws Exception {
        JSONObject parametrosEnvio = new JSONObject(transacaoVO.getParamsEnvio());
        JSONObject payment = parametrosEnvio.getJSONObject("Payment");
        JSONObject creditCard = payment.getJSONObject("CreditCard");
        NazgDTO nazgDTO = obterNazgTO(parametrosEnvio.getString("tokenAragorn"));
        creditCard.put("CardNumber", nazgDTO.getCard());
        return parametrosEnvio;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO)) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("amount", StringUtilities.formatarCampoMonetario(transacao.getValor(), 10));
        String retorno = executarRequestRede(jsonObject.toString(), "/transactions/" + transacao.getCodigoExterno() + "/refunds", ExecuteRequestHttpService.METODO_POST);
        processarRetornoCancelamento(transacao, retorno);
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && (estornarRecibo && transacao.getReciboPagamento() != 0)) {
            estornarRecibo(transacao, estornarRecibo);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, String retorno) throws Exception {
        try {
            JSONObject retornoCancelamento = new JSONObject(retorno);
            String returnCode = retornoCancelamento.getString("returnCode");

            if (returnCode.equals(ERedeRetornoEnum.Retorno359.getCodigo())) {
                transacao.setResultadoCancelamento(retorno);
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else if (returnCode.equals(ERedeRetornoEnum.Retorno355.getCodigo())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            } else {
                transacao.setResultadoCancelamento(retorno);
            }
        } catch (Exception e) {
            consultarSituacaoTransacao(transacao);
        }
    }

    public void consultarSituacaoTransacao(TransacaoVO transacao) throws Exception {
        String retorno = executarRequestRede(null, "/transactions/" + transacao.getCodigoExterno(), ExecuteRequestHttpService.METODO_GET);
        processarRetornoConsultaTransacao(transacao, retorno);
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoConsultaTransacao(TransacaoVO transacao, String retorno) throws Exception {
        try {
            JSONObject retornoTransacao = new JSONObject(retorno);
            String returnCode = retornoTransacao.getString("returnCode");

            if (returnCode.equals(ERedeRetornoEnum.Retorno00.getCodigo())) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            } else if (returnCode.equals(ERedeRetornoEnum.Retorno360.getCodigo())) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        } catch (Exception e) {
        }
    }

    private void consultarPeloCodigoReferencia(TransacaoVO transacaoVO) {
        try {
            JSONObject envio = new JSONObject(transacaoVO.getParamsEnvio());
            String reference = envio.getString("reference");

            String retorno = executarRequestRede(null, "/transactions?reference=" + reference, ExecuteRequestHttpService.METODO_GET);
            JSONObject retornoTransacao = new JSONObject(retorno);
            transacaoVO.setCodigoExterno(retornoTransacao.getString("tid"));
            Connection con = Conexao.getInstance().obterNovaConexaoBaseadaOutra(getTransacaoFacade().getCon());
            new Transacao(con).alterar(transacaoVO);
        } catch (Exception e) {
        }
    }

    private void processarRetorno(TransacaoVO transacao, String retorno) {
        transacao.setParamsResposta(retorno);
        try {
            JSONObject retornoJSON = new JSONObject(retorno);
            String returnCode = retornoJSON.getString("returnCode");
            JSONObject threeDSecure = retornoJSON.getJSONObject("threeDSecure");
            String urlAutenticacao = threeDSecure.getString("url");

            transacao.setUrlTransiente(urlAutenticacao);

            if (returnCode.equals("220")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                transacao.setPermiteRepescagem(false);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                transacao.setPermiteRepescagem(true);
            }
        } catch (Exception e) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            transacao.setPermiteRepescagem(true);
        }
    }

    private String executarRequestRede(String parametros, String metodo, String metodoHTTP) throws IOException {
        String autenticacao = numeroFiliacao + ":" + token;

        String URL = this.urlApiERede;
        String path = URL + metodo;
        Map<String, String>  maps = new HashMap<String, String>();
        maps.put("Content-Type", "application/json");
        maps.put("Authorization", "Basic " + new String(new Base64().encode(autenticacao.getBytes())));
        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, maps, metodoHTTP, "UTF-8");
    }

    public Pessoa getPessoa() throws Exception {
        if (this.pessoa == null) {
            this.pessoa = new Pessoa(getCon());
        }
        return pessoa;
    }

    public Log getLog() throws Exception {
        if (this.log == null) {
            this.log = new Log(getCon());
        }
        return log;
    }

    public String getNumeroFiliacao() {
        return numeroFiliacao;
    }

    public void setNumeroFiliacao(String numeroFiliacao) {
        this.numeroFiliacao = numeroFiliacao;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUrlReturn() {
        if (urlReturn == null) {
            urlReturn = "";
        }
        return urlReturn;
    }

    public void setUrlReturn(String urlReturn) {
        this.urlReturn = urlReturn;
    }

    public FormaPagamento getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(FormaPagamento formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public FormaPagamentoVO getFormaPagamentoVO() {
        return formaPagamentoVO;
    }

    public void setFormaPagamentoVO(FormaPagamentoVO formaPagamentoVO) {
        this.formaPagamentoVO = formaPagamentoVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception{
        //não implementado
    }
}

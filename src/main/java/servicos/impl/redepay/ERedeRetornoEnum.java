/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.redepay;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/*
 * Created by <PERSON><PERSON> on 20/09/2017.
 */
public enum ERedeRetornoEnum {

    RetornoNenhum("NENHUM", ""),
    Retorno00("00", "Sucesso", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Retorno101("101", "Não autorizado. Problemas no cartão, entre em contato com o emissor."),
    Retorno102("102", "Não autorizado. Verifique a situação da loja com o emissor."),
    Retorno103("103", "Não autorizado. Por favor, tente novamente."),
    Retorno104("104", "Não autorizado. Por favor, tente novamente."),
    Retorno105("105", "Não autorizado. Cartao restrito."),
    Retorno106("106", "Erro no processamento do emissor. Por favor, tente novamente."),
    Retorno107("107", "Não autorizado. Por favor, tente novamente."),
    Retorno108("108", "Não autorizado. Valor não permitido para este tipo de cartão"),
    Retorno109("109", "Não autorizado. Cartão inexistente.", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Retorno110("110", "Não autorizado. Tipo de transação não permitido para este cartão."),
    Retorno111("111", "Não autorizado. Fundos insuficientes.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Retorno112("112", "Não autorizado. A data de expiração expirou.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Retorno113("113", "Não autorizado. Identificou risco moderado pelo emissor."),
    Retorno114("114", "Não autorizado. O cartão não pertence à rede de pagamento."),
    Retorno115("115", "Não autorizado. Excedeu o limite de transações permitidas no período."),
    Retorno116("116", "Não autorizado. Por favor, entre em contato com o emissor do cartão."),
    Retorno117("117", "Transação não encontrada."),
    Retorno118("118", "Não autorizado. Cartão bloqueado.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Retorno119("119", "Não autorizado. Código de segurança inválido."),
    Retorno121("121", "Erro no processamento. Por favor, tente novamente."),
    Retorno122("122", "Transação enviada anteriormente"),
    Retorno123("123", "Não autorizado. O portador solicitou o fim das recorrências no emissor."),
    Retorno124("124", "Não autorizado. Entre em contato com a Rede.", OperacaoRetornoCobrancaEnum.CONTATO_CONVENIO),

    Retorno01("1", "período de expiração: tamanho de parâmetro inválido"),
    Retorno02("2", "período de expiração: formato de parâmetro inválido"),
    Retorno03("3", "período de expiração: parâmetro necessário faltando"),
    Retorno04("4", "cavv: tamanho de parâmetro inválido"),
    Retorno05("5", "cavv: formato de parâmetro inválido"),
    Retorno06("6", "postalCódigo: tamanho de parâmetro inválido"),
    Retorno07("7", "postalCódigo: formato de parâmetro inválido"),
    Retorno08("8", "postalCódigo: parâmetro necessário faltando"),
    Retorno09("9", "complemento: tamanho de parâmetro inválido"),
    Retorno10("10", "complemento: formato de parâmetro inválido"),
    Retorno11("11", "departureTax: formato de parâmetro inválido"),
    Retorno12("12", "documentNumber: Tamanho de parâmetro inválido"),
    Retorno13("13", "documentNumber: Formato de parâmetro inválido"),
    Retorno14("14", "documentNumber: parâmetro necessário faltando"),
    Retorno15("15", "securityCode: Tamanho de parâmetro inválido"),
    Retorno16("16", "securityCode: Formato de parâmetro inválido"),
    Retorno17("17", "distribuidoraAferificação: tamanho de parâmetro inválido"),
    Retorno18("18", "distribuidorAferificação: Formato de parâmetro inválido"),
    Retorno19("19", "xid: Tamanho de parâmetro inválido"),
    Retorno20("20", "eci: Formato de parâmetro inválido"),
    Retorno21("21", "xid: o parâmetro necessário para o cartão Visa está faltando"),
    Retorno22("22", "rua: parâmetro necessário faltando"),
    Retorno23("23", "rua: formato de parâmetro inválido"),
    Retorno24("24", "afiliação: tamanho de parâmetro inválido"),
    Retorno25("25", "afiliação: formato de parâmetro inválido"),
    Retorno26("26", "afiliação: parâmetro necessário faltando"),
    Retorno27("27", "Parameter cavv ou eci missing"),
    Retorno28("28", "código: tamanho de parâmetro inválido"),
    Retorno29("29", "código: formato de parâmetro inválido"),
    Retorno30("30", "código: parâmetro necessário faltando"),
    Retorno31("31", "softdescriptor: Tamanho de parâmetro inválido"),
    Retorno32("32", "softdescriptor: formato de parâmetro inválido"),
    Retorno33("33", "expirationMonth: Formato de parâmetro inválido"),
    Retorno34("34", "código: formato de parâmetro inválido"),
    Retorno35("35", "expirationMonth: parâmetro necessário faltando"),
    Retorno36("36", "número do cartão: tamanho do parâmetro inválido"),
    Retorno37("37", "número do cartão: formato de parâmetro inválido"),
    Retorno38("38", "número do cartão: parâmetro necessário faltando"),
    Retorno39("39", "referência: tamanho de parâmetro inválido"),
    Retorno40("40", "referência: formato de parâmetro inválido"),
    Retorno41("41", "referência: parâmetro necessário faltando"),
    Retorno42("42", "referência: o número da encomenda já existe"),
    Retorno43("43", "número: tamanho de parâmetro inválido"),
    Retorno44("44", "número: formato de parâmetro inválido"),
    Retorno45("45", "número: parâmetro necessário faltando"),
    Retorno46("46", "parcelas: não corresponde à transação de autorização"),
    Retorno47("47", "origem: formato de parâmetro inválido"),
    Retorno49("49", "O valor da transação excede o autorizado"),
    Retorno50("50", "parcelas: formato de parâmetro inválido"),
    Retorno51("51", "Produto ou serviço desativado para este comerciante. Entre em contato com a Rede"),
    Retorno53("53", "Transação não permitida para o emissor. Entre em contato com a Rede"),
    Retorno54("54", "parcelas: parâmetro não permitido para esta transação"),
    Retorno55("55", "cardHolderName: Tamanho de parâmetro inválido"),
    Retorno56("56", "Erro nos dados reportados. Tente novamente."),
    Retorno57("57", "afiliação: comerciante inválido"),
    Retorno58("58", "Emissor de contato não autorizado"),
    Retorno59("59", "cardHolderName: formato de parâmetro inválido"),
    Retorno60("60", "rua: tamanho de parâmetro inválido"),
    Retorno61("61", "subscrição: formato de parâmetro inválido"),
    Retorno63("63", "softdescriptor: não habilitado para este comerciante"),
    Retorno64("64", "Transação não processada. Tente novamente"),
    Retorno65("65", "token: token inválido"),
    Retorno66("66", "departureTax: Tamanho de parâmetro inválido"),
    Retorno67("67", "departureTax: Formato de parâmetro inválido"),
    Retorno68("68", "departureTax: parâmetro necessário faltando"),
    Retorno69("69", "Transação não permitida para este produto ou serviço"),
    Retorno70("70", "quantidade: tamanho de parâmetro inválido"),
    Retorno71("71", "quantidade: formato de parâmetro inválido"),
    Retorno72("72", "emissor de contato"),
    Retorno73("73", "quantidade: parâmetro necessário faltando"),
    Retorno74("74", "Falha na comunicação. Tente novamente"),
    Retorno75("75", "departureTax: Parâmetro não deve ser enviado para este tipo de transação"),
    Retorno76("76", "tipo: formato de parâmetro inválido"),
    Retorno78("78", "Transação não existe"),
    Retorno79("79", "Cartão expirado. A transação não pode ser reenviada. Emissor de contatos.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Retorno80("80", "Emissor de contato não autorizado. (Fundo insuficiente)", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Retorno81("81", "Produto ou serviço desativado para este emissor (avs)"),
    Retorno82("82", "Transação não autorizada para cartão de débito"),
    Retorno83("83", "Emissor de contato não autorizado"),
    Retorno84("84", "Transação não autorizada\" não pode ser reenviada. Emissor em contato. "),
    Retorno85("85", "complemento: tamanho de parâmetro inválido"),
    Retorno86("86", "Cartão expirado", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Retorno87("87", "pelo menos um dos seguintes campos deve ser preenchido: tid ou referência"),
    Retorno88("88", "Comerciante não aprovado. Regular o seu site e entre em contato com a Rede para retornar para transacionar."),
    Retorno89("89", "token: token inválido"),
    Retorno97("97", "tid: Tamanho de parâmetro inválido"),
    Retorno98("98", "tid: Formato de parâmetro inválido"),
    Retorno150("150", "Tempo limite. Tente novamente"),
    Retorno151("151", "parcelas: maior que permitido"),
    Retorno152("152", "tipo: débito não permitido para este tipo de transação"),
    Retorno153("153", "número do documento: número inválido"),
    Retorno154("154", "incorporado: formato de parâmetro inválido"),
    Retorno155("155", "eci: parâmetro necessário faltando"),
    Retorno156("156", "eci: tamanho de parâmetro inválido"),
    Retorno157("157", "cavv: parâmetro necessário faltando"),
    Retorno158("158", "captura: Tipo não permitido para esta transação"),
    Retorno159("159", "userAgent: Tamanho de parâmetro inválido"),
    Retorno160("160", "urls: parâmetro necessário em falta (tipo)"),
    Retorno161("161", "urls: Formato de parâmetro inválido"),
    Retorno167("167", "Solicitação inválida JSON"),
    Retorno169("169", "Tipo de conteúdo inválido"),
    Retorno171("171", "Operação não permitida para esta transação"),
    Retorno172("172", "Transação já capturada"),
    Retorno173("173", "Autorização expirada"),
    Retorno174("174", "Transação de verificação validado com sucesso"),
    Retorno176("176", "urls: parâmetro necessário faltando (url)"),

    //Retornos 3DS
    Retorno200 ("200", "Cardholder successfully authenticated"),
    Retorno201 ("201", "Authentication not required"),
    Retorno202 ("202", "Unauthenticated cardholder"),
    Retorno203 ("203", "Authentication service not registered for the merchant. Please contact Rede"),
    Retorno204 ("204", "Cardholder not registered in the issuer's authentication program"),
    Retorno220 ("220", "Transaction request with authentication received. Redirect URL sent"),
    Retorno250 ("250", "onFailure: Required parameter missing"),
    Retorno251 ("251", "onFailure: Invalid parameter format"),
    Retorno252 ("252", "urls: Required parameter missing (url/threeDSecureFailure)"),
    Retorno253 ("253", "urls: Invalid parameter size (url/threeDSecureFailure)"),
    Retorno254 ("254", "urls: Invalid parameter format (url/threeDSecureFailure)"),
    Retorno255 ("255", "urls: Required parameter missing (url/threeDSecureSuccess)"),
    Retorno256 ("256", "urls: Invalid parameter size (url/threeDSecureSuccess)"),
    Retorno257 ("257", "urls: Invalid parameter format (url/threeDSecureSuccess)"),
    Retorno258 ("258", "userAgent: Required parameter missing"),
    Retorno259 ("259", "urls: Required parameter missing"),
    Retorno260 ("260", "urls: Required parameter missing (kind/threeDSecureFailure)"),
    Retorno261 ("261", "urls: Required parameter missing (kind/threeDSecureSuccess)"),

    //Retornos de cancelamento
    Retorno351("351", "Proibido"),
    Retorno353("353", "Transação não encontrada"),
    Retorno354("354", "Transação com prazo expirado para reembolso"),
    Retorno355("355", "Transação já cancelada"),
    Retorno357("357", "Soma dos reembolsos do montante superior ao valor da transação"),
    Retorno358("358", "Soma do montante reembolsado maior que o valor processado disponível para reembolso"),
    Retorno359("359", "Reembolso de sucesso"),
    Retorno360("360", "Pedido de reembolso foi bem sucedido"),
    Retorno362("362", "Reembolso não encontrado"),
    Retorno363("363", "Caráter de URL de retorno excedeu 500"),
    Retorno365("365", "reembolso parcial não disponível"),
    Retorno368("368", "Não foi bem sucedido. Por favor, tente novamente"),
    Retorno369("369", "Reembolso não encontrado"),
    Retorno370("370", "Pedido falhado. Entre em contato com a Rede"),
    Retorno371("371", "Transação não disponível para reembolso. Tente novamente em algumas horas."),
    Retorno899("899", "Transação não autorizada! Por favor entre em contato com a Rede.");


    private String codigo;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private ERedeRetornoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private ERedeRetornoEnum(String codigo, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private ERedeRetornoEnum(String codigo, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static ERedeRetornoEnum valueOff(String id) {
        ERedeRetornoEnum[] values = ERedeRetornoEnum.values();
        for (ERedeRetornoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getCodigo().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return RetornoNenhum;
    }

    public static String obterCodigosRetorno(CodigoRetornoPactoEnum retornoPacto) {
        StringBuilder retorno = new StringBuilder();
        for (ERedeRetornoEnum obj : ERedeRetornoEnum.values()) {
            if (obj.getCodigoRetornoPacto().equals(retornoPacto)) {
                retorno.append("'").append(obj.getCodigo()).append("'").append(",");
            }
        }
        if (!UteisValidacao.emptyString(retorno.toString())) {
            retorno.deleteCharAt(retorno.length() - 1);
        }
        return retorno.toString();
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}

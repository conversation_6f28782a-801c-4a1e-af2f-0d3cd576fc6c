package servicos.impl.redepay;

import negocio.comuns.financeiro.enumerador.TipoBoletoPJBankEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON><PERSON>
 * Date: 11/01/2024
 */
public enum ERedeStatusConciliacaoEnum {

    NENHUM(0, "Não teve processo de solicitação de acesso"),
    APROVADO(1, "Acesso aos dados de Conciliação foi APROVADO pelo cliente no Portal da Rede"),
    CANCELADO(2, "Acesso aos dados de Conciliação foi CANCELADO pelo cliente no Portal da Rede"),
    EXPIRADO(3, "EXPIRADO o período de liberação de acesso aos dados de Conciliação pelo cliente no Portal da Rede"),
    PENDENTE(4, "PENDENTE a liberação de acesso aos dados de Conciliação pelo cliente no Portal da Rede"),
    REPROVADO(5, "Acesso aos dados de Conciliação foi REPROVADO pelo cliente no Portal da Rede");

    private Integer id;
    private String descricao;

    ERedeStatusConciliacaoEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static ERedeStatusConciliacaoEnum obterPorId(int id) {
        for (ERedeStatusConciliacaoEnum status : ERedeStatusConciliacaoEnum.values()) {
            if (status.getId() == id) {
                return status;
            }
        }
        return null;
    }

}

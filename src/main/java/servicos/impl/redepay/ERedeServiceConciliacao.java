package servicos.impl.redepay;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.dcc.rede.TipoArquivoRedeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static negocio.comuns.utilitarias.Calendario.somarDias;
import static negocio.comuns.utilitarias.Calendario.subtrairDias;

/**
 * Serviço responsável por realizar a comunicação com a API <b>Conciliação Rede</b>.
 * Para que o "serviço de comunicação" com a API de conciliação da Rede funcione correntamente,
 * é necessário que a empresa esteja cadastrada no (ZW) Convênio de Cobrança, tipo DCC Rede Online.
 *
 * Documentação: https://developer.userede.com.br/gestao-acessos#tutorial-sandbox-simulacao-consulta-acesso-extrato
 */
public class ERedeServiceConciliacao extends SuperEntidade {

    private String URL_API_Rede_Producao = PropsService.getPropertyValue(PropsService.urlApiRedeConciliacaoProducao);

    public ERedeServiceConciliacao() throws Exception {
    }

    public String integrarConciliacaoOnline(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {

            if (UteisValidacao.emptyString(convenioCobrancaVO.getNumeroContrato().trim())) {
                throw new Exception("Informe o Ponto de Venda.");
            }
            String url = URL_API_Rede_Producao + "/partner/v1/organizations/requests/features/merchant-statement";

            String acessToken = createAcessToken();

            JSONObject body = new JSONObject();
            body.put("requestCompanyNumber", Integer.parseInt(convenioCobrancaVO.getNumeroContrato()));
            body.put("requestType", "I"); //I = Individual por PV não importando o tipo, P = Especifica qual PV Matriz e especifica quais PV Filiais quer ter acesso, T = Informa só o PV Matriz que os Filiais vem automaticamente
            body.put("permissions", "R"); //R = Escrita, D = Exclusão, W = Escrita, RW = Leitura e Escrita, RWD = Leitura, Escrita e Exclusão

            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = 10000;
            service.connectionRequestTimeout = 10000;
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, header(acessToken), null, body.toString(), MetodoHttpEnum.POST);
            if (respostaHttpDTO.getHttpStatus().equals(201)) {
                convenioCobrancaVO.setStatusConciliacaoRedeOnline(ERedeStatusConciliacaoEnum.PENDENTE);

                JSONObject retornoCapture = new JSONObject(respostaHttpDTO.getResponse());
                String requestId = retornoCapture.getString("requestId");
                convenioCobrancaVO.setRequestIdConciliacaoRedeOnline(requestId);

                getFacade().getConvenioCobranca().alterar(convenioCobrancaVO);
                return "Solicitação enviada com Sucesso. O cliente agora precisa Aprovar o Acesso da Pacto Soluções. Essa aprovação é feita na área logada do Portal da Rede.";
            } else if (respostaHttpDTO.getHttpStatus().equals(400)) {
                throw new Exception("Erro nos dados informados para a API.");
            } else if (respostaHttpDTO.getHttpStatus().equals(409)) {
                throw new Exception("Já existe uma solicitação aguardando aprovação na área logada do Portal da Rede para esse Ponto de Venda.");
            } else if (respostaHttpDTO.getHttpStatus().equals(422)) {
                throw new Exception("Dado informado não está no formato esperado pela API Rede. Entre em contato com o suporte da Pacto.");
            } else if (respostaHttpDTO.getHttpStatus().equals(500)) {
                throw new Exception("Serviço da API Rede indisponível no momento, tente mais tarde.");
            } else {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao solicitar concessão de acesso. Retorno Rede: " + ex.getMessage());
        }
    }

    public String limparDadosIntegracaoConciliacaoOnline(ConvenioCobrancaVO convenioCobrancaVO, UsuarioVO usuarioVOLogado) throws Exception {
        try {
            convenioCobrancaVO.setStatusConciliacaoRedeOnline(ERedeStatusConciliacaoEnum.NENHUM);
            convenioCobrancaVO.setRequestIdConciliacaoRedeOnline("");
            getFacade().getConvenioCobranca().alterar(convenioCobrancaVO);
            registraLogs(convenioCobrancaVO, usuarioVOLogado);
            return "Dados apagados com com Sucesso.";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao tentar apagar os dados da concessão de acesso. Retorno Rede: " + ex.getMessage());
        }
    }

    public void statusIntegracaoConciliacaoOnline(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {

            if (UteisValidacao.emptyString(convenioCobrancaVO.getNumeroContrato().trim())) {
                throw new Exception("Informe o Ponto de Venda.");
            }

            String acessToken = createAcessToken();

            String url = URL_API_Rede_Producao + "/partner/v1/organizations/requests/" + convenioCobrancaVO.getRequestIdConciliacaoRedeOnline() + "/features/merchant-statement";

            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = 10000;
            service.connectionRequestTimeout = 10000;
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, header(acessToken), null, null, MetodoHttpEnum.GET);
            if (respostaHttpDTO.getHttpStatus().equals(200)) {
                JSONObject retornoCapture = new JSONObject(respostaHttpDTO.getResponse());
                String status = retornoCapture.getString("status");
                convenioCobrancaVO.setStatusConciliacaoRedeOnline(ERedeStatusConciliacaoEnum.valueOf(status));
                getFacade().getConvenioCobranca().alterarStatusConciliacaoERede(convenioCobrancaVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao Consultar Status de acesso. Retorno Rede: " + ex.getMessage());
        }
    }

    private String createAcessToken() throws Exception {
        try {
            String endpoint = "https://api.userede.com.br/redelabs/oauth/token";
//            String endpoint = "https://rl7-sandbox-api.useredecloud.com.br/oauth/token";
            String userNameRede = PropsService.getPropertyValue(PropsService.userNameRede);
            String passwordRede = PropsService.getPropertyValue(PropsService.passwordRede);

            String authorization = "";
            try {
                authorization = authorizationBase64();
            } catch (Exception ex) {
                throw new Exception("Não foi possível gerar o Authorization");
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");
            headers.put("Authorization", authorization);

            Map<String, String> corpo = new HashMap<String, String>();
            corpo.put("grant_type", "password");
            corpo.put("username", userNameRede);
            corpo.put("password", passwordRede);

            String executeRequest = ExecuteRequestHttpService.executeHttpRequestGenerico(endpoint, headers, corpo, ExecuteRequestHttpService.METODO_POST, Charsets.UTF_8.name(), Charsets.UTF_8.name());

            JSONObject json;
            try {
                json = new JSONObject(executeRequest);
                return "Bearer " + json.optString("access_token");
            } catch (Exception ex) {
                throw new Exception("Não foi possível obter gerar o token para consultar. tente novamente mais tarde!");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    private String authorizationBase64() throws Exception {
        try {
            String concatenacao = PropsService.getPropertyValue(PropsService.clientIdRede) + ":" + PropsService.getPropertyValue(PropsService.clientSecretRede);
            String authorizationBase64 = "Basic " + new String(new Base64().encode(concatenacao.getBytes()));
            return authorizationBase64;
        } catch (Exception e){
            e.printStackTrace();
            throw new Exception("Erro ao gerar o Authorization");
        }
    }

    public void processarExtratosERede(TipoConciliacaoEnum tipoConciliacao, ConvenioCobrancaVO convenioCobrancaVO, Date reprocessarAPartirDeRecebido, Date reprocessarAteRecebido, EmpresaVO empresa,
                                boolean convenioPossuiVariasEmpresasConfiguradas, JSONArray paymentsCreditOrdersCompensacao) throws Exception {

        Date reprocessarAPartirDe;
        if (reprocessarAPartirDeRecebido == null) {
            reprocessarAPartirDe = new Date(Calendario.hoje().getTime());
        } else {
            reprocessarAPartirDe = new Date(reprocessarAPartirDeRecebido.getTime());
        }
        Date reprocessarAte = new Date(reprocessarAteRecebido.getTime());

        //A Rede trás os dados de Venda na data da Consulta, o que torna confuso, pois todas as outras trás o dado no dia seguinte
        //Pensando na usabilidade, adicionei esse método para tornar padrão esse funcionamento
        if (tipoConciliacao.equals(TipoConciliacaoEnum.VENDAS)) {
            reprocessarAPartirDe = subtrairDias((reprocessarAPartirDe != null ? reprocessarAPartirDe : Calendario.ontem()), 1);
            reprocessarAte = subtrairDias((reprocessarAte != null ? reprocessarAte : Calendario.ontem()), 1);
        }

        int dia = 1;
        int i = 0;
        if (reprocessarAPartirDe != null && Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte)) >= 0)
            dia = Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte));
        for (; i <= dia; i++) {
            Date dataTransacao = somarDias((reprocessarAPartirDe != null ? reprocessarAPartirDe : Calendario.ontem()), i);
            if (Calendario.maior(dataTransacao, Calendario.hoje())) {
                try {
                    Uteis.logarDebug("Conciliação Rede Online | Data Processamento maior do que Hoje");
                    Uteis.logarDebug("Conciliação Rede Online | Data Processamento: " + dataTransacao.toString());
                    Uteis.logarDebug("Conciliação Rede Online | Data Hoje: " + Calendario.hoje().toString());
                } catch (Exception e) {}
                continue;
            }

            if (UteisValidacao.emptyString(convenioCobrancaVO.getNumeroContrato().trim())) {
                throw new Exception("Informe o Ponto de Venda.");
            }

            //Para não processar duplicado, pois o processo roda várias vezes ao dia
            if (validarArquivoFoiProcessado(convenioCobrancaVO, dataTransacao, tipoConciliacao)) continue;

            try {
                String acessToken = createAcessToken();
                String url = "";

                if (tipoConciliacao.equals(TipoConciliacaoEnum.VENDAS)) {
                    url = URL_API_Rede_Producao + "/merchant-statement/v1/sales";
                } else if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                    url = URL_API_Rede_Producao + "/merchant-statement/v1/payments/credit-orders";
                }

                if (UteisValidacao.emptyString(url)) {
                    Uteis.logarDebug("Erro Conciliação Rede Online | TipoConciliacaoEnum não é do tipo VENDAS ou PAGAMENTOS.");
                    throw new Exception("Erro Conciliação Rede Online | TipoConciliacaoEnum não é do tipo VENDAS ou PAGAMENTOS.");
                }

                Map<String, String> params = new HashMap<String, String>();
                params.put("parentCompanyNumber", convenioCobrancaVO.getNumeroContrato().trim());
                params.put("subsidiaries", convenioCobrancaVO.getNumeroContrato().trim());
                params.put("startDate", Calendario.getDataAplicandoFormatacao(dataTransacao, "yyyy-MM-dd"));
                params.put("endDate", Calendario.getDataAplicandoFormatacao(dataTransacao, "yyyy-MM-dd"));
                params.put("size", "100");

                RequestHttpService service = new RequestHttpService();
                service.connectTimeout = 10000;
                service.connectionRequestTimeout = 10000;

                //A Rede retorna por página limitada a 100, então precisa validar se tem mais dados pra continuar consultando.
                boolean temMaisPaginas = true;
                List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
                do {
                    JSONObject cursor = null;
                    RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, header(acessToken), params, null, MetodoHttpEnum.GET);
                    if (respostaHttpDTO.getHttpStatus().equals(200)) {
                        JSONObject retornoCapture = new JSONObject(respostaHttpDTO.getResponse());
                        JSONObject content = retornoCapture.optJSONObject("content");
                        cursor = retornoCapture.optJSONObject("cursor");

                        if (tipoConciliacao.equals(TipoConciliacaoEnum.VENDAS)) {
                            JSONArray transactions = content.optJSONArray("transactions");
                            converterJSONParaExtratoDiarioItem(convenioCobrancaVO, transactions, extratoDiarioItens, dataTransacao, empresa, convenioPossuiVariasEmpresasConfiguradas);
                            Uteis.logarDebug("Conciliação Rede Online | Fim Processo VENDAS para o PV: " + convenioCobrancaVO.getNumeroContrato());
                        } else if (tipoConciliacao.equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                            JSONArray paymentsCreditOrders = content.optJSONArray("paymentsCreditOrders");
                            if (paymentsCreditOrdersCompensacao != null) {
                                for (int j = 0; j < paymentsCreditOrders.length(); j++) {
                                    paymentsCreditOrdersCompensacao.put(paymentsCreditOrders.getJSONObject(j));
                                }
                            }
                            montarListaExtratoDiarioitemPagamentos(paymentsCreditOrders, extratoDiarioItens, dataTransacao, convenioCobrancaVO, convenioPossuiVariasEmpresasConfiguradas, empresa);
                            Uteis.logarDebug("Conciliação Rede Online | Fim Processo PAGAMENTOS para o PV: " + convenioCobrancaVO.getNumeroContrato());
                        }
                    } else if (respostaHttpDTO.getHttpStatus().equals(401)) {
                        Uteis.logarDebug("Erro Conciliação Rede Online | Token não autorizado para o PV: " + convenioCobrancaVO.getNumeroContrato());
                    } else if (respostaHttpDTO.getHttpStatus().equals(403)) {
                        Uteis.logarDebug("Erro Conciliação Rede Online | A Pacto não tem permissão de acesso aos dados do PV: " + convenioCobrancaVO.getNumeroContrato());
                    } else if (respostaHttpDTO.getHttpStatus().equals(422)) {
                        Uteis.logarDebug("Algum dado informado não está no formato esperado pela API Rede na consulta do PV: " + convenioCobrancaVO.getNumeroContrato());
                    } else if (respostaHttpDTO.getHttpStatus().equals(500)) {
                        Uteis.logarDebug("Serviço da API Rede indisponível no momento da consulta do PV: " + convenioCobrancaVO.getNumeroContrato());
                    } else {
                        Uteis.logarDebug("Retorno desconhecido na Consulta dos dados de Conciliação na API Rede para o PV: " + convenioCobrancaVO.getNumeroContrato());
                        throw new Exception(respostaHttpDTO.getResponse());
                    }

                    if (cursor != null && cursor.has("nextKey")) {
                        String nextKey = cursor.optString("nextKey");
                        params.put("pageKey", nextKey);
                    } else {
                        temMaisPaginas = false;
                    }

                } while (temMaisPaginas);

                if (extratoDiarioItens.size() > 0) {
                    getFacade().getExtratoDiarioItem().processarListaExtratoDiario(extratoDiarioItens, false, convenioCobrancaVO);
                }
                try {
                    preencherPessoaItemExtrato(extratoDiarioItens, getFacade().getCliente().getCon());
                } catch (Exception ignored) {}

            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro ao consultar PV: " + convenioCobrancaVO.getNumeroContrato() + " | " + ex.getMessage());
            }
        }
    }

    private boolean validarArquivoFoiProcessado(ConvenioCobrancaVO convenioCobrancaVO, Date dataTransacao, TipoConciliacaoEnum tipoConciliacao) {
        ExtratoDiarioItem extratoDiarioItemDAO;
        try {
            String validarExiste = "ERedeServiceConciliacao:" + new SimpleDateFormat("yyyyMMdd").format(dataTransacao) + "-" + convenioCobrancaVO.getNumeroContrato().trim() + "-" + tipoConciliacao;
            extratoDiarioItemDAO = new ExtratoDiarioItem(con);
            boolean arquivoProcessado = extratoDiarioItemDAO.arquivoProcessado(validarExiste);
            if (arquivoProcessado) {
                return true;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            extratoDiarioItemDAO = null;
        }
        return false;
    }

    public void converterJSONParaExtratoDiarioItem(ConvenioCobrancaVO convenioCobrancaVO, JSONArray transactions, List<ExtratoDiarioItemVO> extratoDiarioItens, Date dataTransacao,
                                                   EmpresaVO empresa, boolean convenioPossuiVariasEmpresasConfiguradas) throws Exception {
        ExtratoDiarioItemVO extratoDiarioItemVO;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        for (int i = 0; i < transactions.length(); i++) {
            JSONObject retornoCapture = transactions.optJSONObject(i);
            JSONArray tracking = retornoCapture.optJSONArray("tracking");

            try {
                for (int p = 0; p < tracking.length(); p++) {
                    extratoDiarioItemVO = new ExtratoDiarioItemVO();
                    extratoDiarioItemVO.setProps(obterPropsDoJSON(transactions.getJSONObject(i)));
                    extratoDiarioItemVO.setDataLancamento(sdf.parse(retornoCapture.getString("saleDate")));

                    extratoDiarioItemVO.setAutorizacao(String.valueOf(retornoCapture.getInt("authorizationCode")));
                    if (!extratoDiarioItemVO.getAutorizacao().equals("0")) {
                        while (extratoDiarioItemVO.getAutorizacao().length() < 6) {
                            extratoDiarioItemVO.setAutorizacao("0" + extratoDiarioItemVO.getAutorizacao());
                        }
                    } else {
                        extratoDiarioItemVO.setAutorizacao(String.valueOf(retornoCapture.getString("strAuthorizationCode").trim()));
                    }

                    extratoDiarioItemVO.setEstabelecimento(convenioCobrancaVO.getNumeroContrato().trim());
                    try {
                        extratoDiarioItemVO.setNrCartao(retornoCapture.getString("cardNumber"));
                    } catch (Exception e) {
                        extratoDiarioItemVO.setNrCartao("Não informado Rede");
                    }
                    extratoDiarioItemVO.setNrParcela(1);
                    extratoDiarioItemVO.setRo(String.valueOf(retornoCapture.getInt("saleSummaryNumber")));
                    extratoDiarioItemVO.setTaxa(retornoCapture.getDouble("mdrFee"));
                    extratoDiarioItemVO.setArquivo("ERedeServiceConciliacao:" + new SimpleDateFormat("yyyyMMdd").format(dataTransacao) + "-" + convenioCobrancaVO.getNumeroContrato().trim() + "-" + TipoConciliacaoEnum.VENDAS);
                    extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
                    extratoDiarioItemVO.setNrTotalParcelas(retornoCapture.getInt("installmentQuantity"));

                    JSONObject modalidade = retornoCapture.optJSONObject("modality");
                    extratoDiarioItemVO.setCredito(modalidade.getInt("code") == 1); // 1=Credito, 2=Debito

                    JSONObject trackingObject = tracking.optJSONObject(p);
                    String status = trackingObject.optString("status");
                    if (status.equals("APPROVED")) {
                        extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.VENDAS.getCodigo());
                        extratoDiarioItemVO.setValorBruto(trackingObject.optDouble("amount"));
                        extratoDiarioItemVO.setValorLiquido(retornoCapture.getDouble("netAmount"));
                        extratoDiarioItemVO.setValorComissao(retornoCapture.getDouble("mdrAmount"));
                    } else if (status.equals("PARTIAL_CANCELLED")) {
                        extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.CANCELAMENTO.getCodigo());
                        extratoDiarioItemVO.setValorBruto(trackingObject.optDouble("amount") * -1);
                    } else if (status.equals("CANCELLED")) {
                        extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.CANCELAMENTO.getCodigo());
                        extratoDiarioItemVO.setValorBruto(trackingObject.optDouble("amount") * -1);
                    }

                    extratoDiarioItemVO.setApresentarExtrato(true);
                    extratoDiarioItemVO.setConvenio(convenioCobrancaVO);
                    extratoDiarioItemVO.setCodConvenio(convenioCobrancaVO.getCodigo());
                    extratoDiarioItemVO.setTipoConvenioCobrancaEnum(convenioCobrancaVO.getTipo());
                    extratoDiarioItemVO.setEmpresa(empresa);
                    extratoDiarioItemVO.setNsu(String.valueOf(retornoCapture.getInt("nsu")));
                    extratoDiarioItemVO.setEstorno(false);
                    extratoDiarioItemVO.setTipoArquivo(TipoArquivoRedeEnum.API_REDE.getId()); //Tive de criar um novo tipo, pois a exibição no financeiro não encontrava devido a uma regra de Rede, precisa validar o tipo

                    getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);

                    try {
                        if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                            //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                            extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(),
                                    Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
                        }
                    } catch (Exception ex) {}
                    extratoDiarioItens.add(extratoDiarioItemVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                try {
                    Uteis.logarDebug("Não foi possível converter JSON para ExtratoDiarioItem no item de NSU: " + String.valueOf(retornoCapture.optInt("nsu")));
                } catch (Exception ignored) {
                }
            }
        }
    }

    public void montarListaExtratoDiarioitemPagamentos(JSONArray paymentsCreditOrders, List<ExtratoDiarioItemVO> extratoDiarioItens, Date dataTransacao, ConvenioCobrancaVO convenioCobrancaVO,
                                                       boolean convenioPossuiVariasEmpresasConfiguradas, EmpresaVO empresa) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ExtratoDiarioItem extratoDiarioItemDAO;

        for (int i = 0; i < paymentsCreditOrders.length(); i++) {
            List<ExtratoDiarioItemVO> listaExtratoDiarioItemVOVenda = new ArrayList<>();
            JSONObject retornoPagamento = paymentsCreditOrders.getJSONObject(i);
            try {
                extratoDiarioItemDAO = new ExtratoDiarioItem(con);
                Integer saleSummaryNumber = retornoPagamento.getInt("saleSummaryNumber");
                if (!UteisValidacao.emptyNumber(saleSummaryNumber)) {
                    //A Rede as vezes vincula o mesmo RO a várias vendas, então precisa encontrar todas para conciliar
                    listaExtratoDiarioItemVOVenda = getFacade().getExtratoDiarioItem().consultarExtratosRedeVenda(Integer.toString(saleSummaryNumber));

                    for (ExtratoDiarioItemVO extratoDiarioItemVOVenda: listaExtratoDiarioItemVOVenda) {
                        //Depois de receber as várias vendas com o mesmo RO, precisa validar se cada venda já terminou o parcelamento e não entrar no if
                        if (validarVendaTerminouParcelamento(extratoDiarioItemVOVenda, dataTransacao)) {
                            ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO();

                            extratoDiarioItemVO = (ExtratoDiarioItemVO) extratoDiarioItemVOVenda.getClone(true);
                            extratoDiarioItemVO.setCodigo(0);
                            extratoDiarioItemVO.setProps(obterPropsDoJSON(paymentsCreditOrders.getJSONObject(i)));
                            extratoDiarioItemVO.setDataPrevistaPagamento(sdf.parse(retornoPagamento.getString("paymentDate")));
                            extratoDiarioItemVO.setApresentarExtrato(true);
                            extratoDiarioItemVO.setEstorno(false);
                            extratoDiarioItemVO.setDataProcessamentoExtrato(dataTransacao);
                            extratoDiarioItemVO.setDataReprocessamento(Calendario.hoje());
                            extratoDiarioItemVO.setArquivo("ERedeServiceConciliacao:" + new SimpleDateFormat("yyyyMMdd").format(dataTransacao) + "-" + convenioCobrancaVO.getNumeroContrato().trim() + "-" + TipoConciliacaoEnum.PAGAMENTOS);

                            if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
                                extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.PAGAMENTOS.getCodigo());
                            } else if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.CANCELAMENTO.getCodigo())) {
                                extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo());
                            }

                            //A Rede não informa o Número da Parcela, então a gente tem de tentar adivinhar com base na diferença de meses, caso não tenha os dados de conciliações anteriores.
                            if (extratoDiarioItemVO.getCredito()) {
                                Integer numeroUltimaParcelaConciliada = extratoDiarioItemDAO.consultarUltimaParcelaPelaAutorizacao(extratoDiarioItemVO.getAutorizacao(), empresa.getCodigo(),
                                        extratoDiarioItemVO.getTipoConciliacao(), dataTransacao, extratoDiarioItemVO.getPessoa().getCodigo());
                                Integer diferencaMeses = Math.toIntExact(Math.round(Calendario.diferencaEmDias(extratoDiarioItemVO.getDataLancamento(), extratoDiarioItemVO.getDataPrevistaPagamento()) / 30.0));
                                //Se o número última pacela for maior, é porquê teve antecipação.
                                if (numeroUltimaParcelaConciliada >= diferencaMeses) {
                                    extratoDiarioItemVO.setNrParcela(numeroUltimaParcelaConciliada + 1);
                                } else {
                                    extratoDiarioItemVO.setNrParcela(diferencaMeses == 0 ? 1 : diferencaMeses);
                                }
                            }

                            extratoDiarioItemVO.setConvenio(convenioCobrancaVO);
                            extratoDiarioItemVO.setTipoConvenioCobrancaEnum(TipoConvenioCobrancaEnum.DCC_E_REDE);
                            extratoDiarioItemVO.setTipoArquivo(TipoArquivoRedeEnum.API_REDE.getId()); //Tive de criar um novo tipo, pois a exibição no financeiro não encontrava devido a uma regra de Rede, precisa validar o tipo

                            Double valorBruto = extratoDiarioItemVO.getValorBruto() / extratoDiarioItemVO.getNrTotalParcelas();
                            Double valorLiquido = extratoDiarioItemVO.getValorLiquido() / extratoDiarioItemVO.getNrTotalParcelas();
                            Double valorComissao = extratoDiarioItemVO.getValorComissao() / extratoDiarioItemVO.getNrTotalParcelas();
                            extratoDiarioItemVO.setValorBruto(valorBruto);
                            extratoDiarioItemVO.setValorLiquido(valorLiquido);
                            extratoDiarioItemVO.setValorComissao(valorComissao);

                            if (extratoDiarioItemVO.getCredito()) {
                                getFacade().getExtratoDiarioItem().preencherCartaoCredito(extratoDiarioItemVO, true);
                            } else if (!extratoDiarioItemVO.getCredito()) {
                                getFacade().getExtratoDiarioItem().preencherCartaoDebito(extratoDiarioItemVO);
                            }

                            try {
                                if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                                    //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                                    extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(),
                                            Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
                                }
                            } catch (Exception ex) {}

                            extratoDiarioItens.add(extratoDiarioItemVO);
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                try {
                    Uteis.logarDebug("Não foi possível converter JSON para ExtratoDiarioItem no item de RO: " + String.valueOf(retornoPagamento.optInt("saleSummaryNumber")));
                } catch (Exception ignored) {
                }
            } finally {
                extratoDiarioItemDAO = null;
            }
        }
    }

    public boolean validarVendaTerminouParcelamento(ExtratoDiarioItemVO extratoDiarioItemVO, Date dataTransacao) {
        Date dataUltimaParcela = Calendario.somarMeses(extratoDiarioItemVO.getDataLancamento(), extratoDiarioItemVO.getNrTotalParcelas());
        //Adiciona mais uns dias a data devido a Adquirente não pagar em um dia fixo se tiver Fim de Semana ou Feriado e a última parcela acabar ficando de fora.
        //Obs: observar se vir algum caso de Antecipação e com mais experiências se não ter impacto, pode reduz ou retira esse adicionar dias.
        dataUltimaParcela = Calendario.somarDias(dataUltimaParcela, 7);

        return Calendario.maiorOuIgual(dataUltimaParcela, dataTransacao);
    }

    public Map<String, String> obterPropsDoJSON(JSONObject retornoCapture) throws Exception {
        Map<String, String> props = new HashMap<String, String>();
        Iterator<String> keys = retornoCapture.keys();
        try {
            while (keys.hasNext()) {
                String key = keys.next();
                Object value = retornoCapture.get(key);
                if (value instanceof String) {
                    props.put(key, (String) value);
                    continue;
                }
                if (value instanceof Integer) {
                    props.put(key, String.valueOf((Integer) value));
                    continue;
                }
                if (value instanceof Double) {
                    props.put(key, String.valueOf((Double) value));
                    continue;
                }
                if (value instanceof Boolean) {
                    props.put(key, String.valueOf((Boolean) value));
                    continue;
                }
                if (value instanceof JSONObject) {
                    props.put(key, String.valueOf((JSONObject) value));
                    continue;
                }
                if (value instanceof JSONArray) {
                    props.put(key, String.valueOf((JSONArray) value));
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            try {
                Uteis.logarDebug("Não foi possível obter Props do JSON na conversão do ExtratoDiarioItem.");
            } catch (Exception ignored) {
            }
        }
        return props;
    }

    private Map<String, String> header(String acessToken) throws Exception {
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/json");
        header.put("Authorization", acessToken);
        return header;
    }

    public static void preencherPessoaItemExtrato(List<ExtratoDiarioItemVO> extratos, Connection con) throws Exception {
        ExtratoDiarioItem extratoDiarioItemDAO;
        try {
            extratoDiarioItemDAO = new ExtratoDiarioItem(con);

            for (ExtratoDiarioItemVO extrato : extratos) {
                try {
                    //buscar por movpagamento que já existe no item do extrato
                    if (extrato.getMovPagamento() != null && extrato.getMovPagamento().getPessoa() != null) {
                        if (!UteisValidacao.emptyNumber(extrato.getMovPagamento().getPessoa().getCodigo())) {
                            extrato.getPessoa().setCodigo(extrato.getMovPagamento().getPessoa().getCodigo());
                        }
                    }
                    if (UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        //buscar no sistema e nas transações a pessoa do extrato
                        extratoDiarioItemDAO.obterPessoaExtrato(extrato);
                    }
                    if (!UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        extratoDiarioItemDAO.incluirInfoCodPessoa(extrato);
                    }
                } catch (Exception ignore) {
                    if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao() + " e nem pelo nsu: " + extrato.getNsu());
                    } else if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao());
                    } else if (UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pelo nsu: " + extrato.getNsu());
                    }
                }
            }
        } finally {
            extratoDiarioItemDAO = null;
        }
    }

    public void processarExtratosERedeCompensacao(ConvenioCobrancaVO convenioCobrancaVO, Date reprocessarAPartirDeRecebido, Date reprocessarAteRecebido, EmpresaVO empresa,
                                       boolean convenioPossuiVariasEmpresasConfiguradas) throws Exception {

        //Esse fluxo é para coletar todos os Resumo de Vendas e para atualizar as informações de Vendas e Cancelamentos
        JSONArray paymentsCreditOrders = new JSONArray();
        //Busca na API a lista de Compensando Hoje para buscar o Resumo as Vendas e Cancelamentos
        processarExtratosERede(TipoConciliacaoEnum.PAGAMENTOS, convenioCobrancaVO, reprocessarAPartirDeRecebido, reprocessarAteRecebido, empresa,
                convenioPossuiVariasEmpresasConfiguradas, paymentsCreditOrders);

        //Reprocessa as Vendas, pois é nela que vem os dados de Venda e Cancelamento
        reprocessarVendasPeloResumoDeVendasCompensados(convenioCobrancaVO, empresa, convenioPossuiVariasEmpresasConfiguradas, paymentsCreditOrders);

        //Depois de Reprocessar todas as Vendas método anterior, precisa processar as Compensações
        processarExtratosERede(TipoConciliacaoEnum.PAGAMENTOS, convenioCobrancaVO, reprocessarAPartirDeRecebido, reprocessarAteRecebido, empresa,
                convenioPossuiVariasEmpresasConfiguradas, null);

    }

    private void reprocessarVendasPeloResumoDeVendasCompensados(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresa, boolean convenioPossuiVariasEmpresasConfiguradas,
                                                                JSONArray paymentsCreditOrders) throws Exception {
        if (paymentsCreditOrders.length() > 0) {
            Set<String> arquivosUnicos = new HashSet<>();
            for (int i = 0; i < paymentsCreditOrders.length(); i++) {
                int saleSummaryNumber = paymentsCreditOrders.getJSONObject(i).optInt("saleSummaryNumber");
                //Lista de Códigos Resumo de Vendas para buscar as Vendas e Cancelamentos
                String saleSummaryNumberStr = String.valueOf(saleSummaryNumber == 0 ? "" : saleSummaryNumber);
                if (UteisValidacao.emptyString(saleSummaryNumberStr)) {
                    continue;
                }
                //Encontra o ExtratoDiaItem de Venda para cada Resumo de Vendas
                List<ExtratoDiarioItemVO> extratoDiarioItemVOVendas = getFacade().getExtratoDiarioItem().consultarExtratosRedeVenda(saleSummaryNumberStr);
                //A Rede as vezes coloca o mesmo RO em várias vendas, então precisa ajustar para executrar o fluxo abaixo apenas uma vez
                List<ExtratoDiarioItemVO> extratoDiarioItemVOVendasUnicos = new ArrayList<>();
                for (ExtratoDiarioItemVO extratoDiarioItemVOVenda : extratoDiarioItemVOVendas) {
                    String arquivo = extratoDiarioItemVOVenda.getArquivo();
                    if (!arquivosUnicos.contains(arquivo)) {
                        arquivosUnicos.add(arquivo);
                        extratoDiarioItemVOVendasUnicos.add(extratoDiarioItemVOVenda);
                    }
                }

                for (ExtratoDiarioItemVO extratoDiarioItemVOVenda: extratoDiarioItemVOVendasUnicos) {
                    //Apagar dados Venda da ERede para Reprocessar
                    StringBuilder sql = new StringBuilder();
                    sql.append("delete from extratodiarioitem \n");
                    sql.append("where codigo in ( \n");
                    sql.append("    select codigo from extratodiarioitem where ( \n");
                    sql.append("      (cast(substring(arquivo, 25, 8) as date) >= '" + Uteis.getDataFormatoBD(extratoDiarioItemVOVenda.getDataLancamento()) + "' \n");
                    sql.append("      and cast(substring(arquivo, 25, 8) as date) <= '" + Uteis.getDataFormatoBD(extratoDiarioItemVOVenda.getDataLancamento()) + "' \n");
                    sql.append("      and cast(substring(arquivo, 34, 8) as text) ilike '%" + convenioCobrancaVO.getNumeroContrato() + "%' \n");
                    sql.append("      and tipoconciliacao in (").append(TipoConciliacaoEnum.VENDAS.getCodigo()).append(",").append(TipoConciliacaoEnum.CANCELAMENTO.getCodigo()).append(")) \n");
                    sql.append("    ) \n");
                    sql.append("    and arquivo ilike 'ERedeServiceConciliacao%' \n");
                    sql.append("    and conveniocobranca in (select codigo from conveniocobranca where situacao = 1) \n");
                    sql.append(");");
                    SuperFacadeJDBC.executarUpdate(sql.toString(), getFacade().getExtratoDiarioItem().getCon());

                    //Adiciona um dia, pois no processo de Venda, tira um dia para Conciliação de Venda e Reprocessa a Venda
                    Date dataCloneVenda = new Date(extratoDiarioItemVOVenda.getDataLancamento().getTime());
                    dataCloneVenda = Calendario.somarDias(dataCloneVenda, 1);
                    processarExtratosERede(TipoConciliacaoEnum.VENDAS, convenioCobrancaVO, dataCloneVenda, dataCloneVenda, empresa,
                            convenioPossuiVariasEmpresasConfiguradas, null);
                }
            }
        }
    }

    private void registraLogs(ConvenioCobrancaVO convenioCobrancaVO, UsuarioVO usuarioLogado) throws Exception {
        ConvenioCobrancaVO convenioAntesAlteracao = (ConvenioCobrancaVO) convenioCobrancaVO.getObjetoVOAntesAlteracao();

        LogVO log = new LogVO();
        log.setOperacao("ALTERAÇÃO");
        log.setNomeEntidade("CONVENIOCOBRANCA");
        log.setChavePrimaria(convenioCobrancaVO.getCodigo().toString());
        log.setPessoa(usuarioLogado.getCodigo());
        log.setResponsavelAlteracao(usuarioLogado.getNome());
        log.setNomeCampo("requestidconciliacaoredeonline");
        log.setValorCampoAnterior(convenioAntesAlteracao.getRequestIdConciliacaoRedeOnline());
        log.setValorCampoAlterado(convenioCobrancaVO.getRequestIdConciliacaoRedeOnline());
        log.setUserOAMD(usuarioLogado.getUserOamd());
        getFacade().getLog().incluir(log);

        log.setNomeCampo("statusconciliacaoredeonline");
        log.setValorCampoAnterior(convenioAntesAlteracao.getStatusConciliacaoRedeOnline().toString());
        log.setValorCampoAlterado(convenioCobrancaVO.getStatusConciliacaoRedeOnline().toString());
        getFacade().getLog().incluir(log);
    }

}

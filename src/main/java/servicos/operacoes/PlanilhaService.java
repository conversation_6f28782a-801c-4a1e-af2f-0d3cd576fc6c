package servicos.operacoes;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import servicos.impl.fluxocaixa.LancamentoCotaImpostacaoDTO;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class PlanilhaService {

    public List<LancamentoCotaImpostacaoDTO> lerPlanilhaContas(InputStream input) throws Exception {
        List<LancamentoCotaImpostacaoDTO> contas = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(input)) {
            Sheet sheet = workbook.getSheetAt(0);
            int primeiraLinha = 1; // Ignora cabeçalho

            for (int i = primeiraLinha; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (linhaVazia(row)) continue;

                LancamentoCotaImpostacaoDTO dto = new LancamentoCotaImpostacaoDTO();
                dto.setTipoLancamentoId(getCellInteger(row.getCell(0)));
                dto.setEmpresaId(getCellInteger(row.getCell(1)));
                dto.setReceberDe(getCellInteger(row.getCell(2)));
                dto.setDescricao(getCellString(row.getCell(3)));
                dto.setValor(getCellBigDecimal(row.getCell(4)));
                dto.setDataLancamento(getCellDate(row.getCell(5)));
                dto.setDataVencimento(getCellDate(row.getCell(6)));
                dto.setDataCompetencia(getCellDate(row.getCell(7)));
                dto.setObservacao(getCellString(row.getCell(8)));
                dto.setDataQuitacao(getCellDate(row.getCell(9)));
                dto.setFormaPagamentoId(getCellInteger(row.getCell(10)));
                dto.setContaId(getCellInteger(row.getCell(11)));
                dto.setPlanoContasId(getCellInteger(row.getCell(12)));
                dto.setCentroCustosId(getCellInteger(row.getCell(13)));
                dto.setClienteNovoNome(getCellString(row.getCell(14)));
                dto.setClienteNovoDocumento(getCellString(row.getCell(15)));
                dto.setClienteNovoContato(getCellString(row.getCell(16)));

                contas.add(dto);
            }
        }

        return contas;
    }

    private boolean linhaVazia(Row row) {
        if (row == null) return true;

        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            if (cell != null && !cell.toString().trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }


    private String getCellString(Cell cell) {
        if (cell == null) return null;
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }

    private Integer getCellInteger(Cell cell)   throws Exception {
        if (cell == null) return null;

        switch (cell.getCellTypeEnum()) {
            case NUMERIC:
                return (int) cell.getNumericCellValue();
            case STRING:
                String value = cell.getStringCellValue().trim();
                if (value.isEmpty()) return null;
                try {
                    return Integer.parseInt(value);
                } catch (NumberFormatException e) {
                    throw new Exception("Não foi possível converter o valor \"" + cell.getStringCellValue().trim() + "\" para um número válido.");
                }
            case FORMULA:
                return (int) cell.getNumericCellValue();
            default:
                return null;
        }
    }


    private BigDecimal getCellBigDecimal(Cell cell) throws Exception {
        if (cell == null) return BigDecimal.ZERO;
        try {
            return BigDecimal.valueOf(cell.getNumericCellValue());
        } catch (Exception e) {
            throw new Exception("Não foi possível converter o valor \"" + cell.getStringCellValue().trim() + "\" para número. Verifique se o formato está correto.");
        }
    }

    private Date getCellDate(Cell cell) throws Exception {
        if (cell == null) return null;
        try {
            if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return DateUtil.getJavaDate(cell.getNumericCellValue());
                }
            } else {
                String dateStr = getCellString(cell).trim();
                return new SimpleDateFormat("dd/MM/yyyy").parse(dateStr);
            }
        } catch (Exception e) {
            throw new Exception("Erro ao converter data: " + getCellString(cell) + " - " + e.getMessage());
        }
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.utilitarias.Criptografia;
import controle.arquitetura.SuperControle;
import java.net.InetAddress;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.arquitetura.ResultadoServicos;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.SuperServico;

/**
 *
 * <AUTHOR>
 */
public class EstornoContratoAutomaticoService extends SuperServico {

    private ConfiguracaoSistema configFacade = null;
    private Contrato contratoFacade = null;
    private Cliente clienteFacade = null;
    private ConfiguracaoSistemaVO configSistema = null;
    private ZillyonWebFacade zwFacade = null;
    private UsuarioVO usuarioVO = null;
    private List<ContratoVO> listaContratoEstornados = new ArrayList();
    private StringBuffer errosTentativasEstorno = new StringBuffer();
    private MovParcela movParcelaFacade;
    private ResultadoServicos resultadoServicos;

    private void logarErro(String msg) {
        Uteis.logarDebug(msg);
        errosTentativasEstorno.append("<br>").append(msg);
    }

    public EstornoContratoAutomaticoService(Connection con) throws Exception {
        super(con);
        configFacade = new ConfiguracaoSistema(con);
        clienteFacade = new Cliente(con);
        configSistema = configFacade.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_ROBO);
        contratoFacade = new Contrato(con);
        zwFacade = new ZillyonWebFacade(con);
        usuarioVO = zwFacade.getUsuarioRecorrencia();
        movParcelaFacade = new MovParcela(con);
        resultadoServicos = new ResultadoServicos(con);

    }

    /**
     * Estorna contratos automaticamente seguindo se e somente se, as seguinte
     * regras: 1. Se na configuração do Sistema está definido um valor maior que
     * ZERO para o atributo 'qtdDiasEstornoAutomaticoContrato'; 2. Se este
     * contrato possui data de lançamento anterior aos dias configurados na
     * Configuração do Sistema Por exemplo: Se o Numero de dias para estorno
     * seja 2 (dois): após 2 dias do lançamento, um contrato feito dia
     * 19/10/2011, seria estornado automaticamente na virada do dia 20 para o
     * dia 21 2. Se este contrato não possui nenhuma parcela paga;
     *
     *
     * @param dia
     */
    public void estornarContratosAutomaticamente(Date dia) {
        ResultadoServicosVO resultadoEstorno = new ResultadoServicosVO(ServicoEnum.ESTORNO, "estornarContratosAutomaticamente");
        if (configSistema.getQtdDiasEstornoAutomaticoContrato() > 0) {
            try {
                ClienteVO clienteVO = new ClienteVO();
                List<ContratoVO> listaPotencial = contratoFacade.consultarContratosEstornoAutomatico(
                        dia, configSistema.getQtdDiasEstornoAutomaticoContrato());
                for (ContratoVO contratoVO : listaPotencial) {
                    if(movParcelaFacade.existeParcelaContratoEmRemessaGeradaOuAguardando(contratoVO.getCodigo(), null)){
                        continue;
                    }
                    clienteVO = clienteFacade.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ContratoVO contratoCompleto = contratoFacade.consultarPorChavePrimaria(contratoVO.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    contratoCompleto.setUsuarioVO(usuarioVO);
                    EmpresaVO emp = zwFacade.getEmpresa().consultarPorChavePrimaria(
                            contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteVO.setEmpresa(emp);
                    contratoCompleto.setCliente(clienteVO);
                    contratoCompleto.setEmpresa(emp);
                    contratoCompleto.getPlano().setDataVencimentoParcela(contratoFacade.getDataVencimentoParcelaMaisProximaNaoPaga(contratoCompleto));
                    try {
                        String url = ConfiguracaoSistemaVO.obterURLRecorrencia(emp, configSistema);
                        init(url);
                        contratoFacade.montarListasParaEstorno(contratoCompleto);
                        if(contratoFacade.contratosAindaNaConfiguracaoEstornoAutomatico(dia, configSistema.getQtdDiasEstornoAutomaticoContrato(), contratoCompleto.getCodigo())) {
                            contratoFacade.estornoContrato(contratoCompleto, clienteVO, null, "Estorno Automático");
                            processarCliente(clienteVO, dia);
                            listaContratoEstornados.add(contratoCompleto);
                        }
                    } catch (Exception e) {
                        String msgErro = String.format("[DEBUG] => Erro ao estornar contrato \"%s\" do cliente \"%s\" da empresa \"%s\" mensagem: \"%s\"",
                                contratoVO.getCodigo(), clienteVO.getPessoa().getNome(),
                                clienteVO.getEmpresa().getNome(), e.getMessage());
                        logarErro(msgErro);
                        resultadoEstorno.getResultado().put(msgErro);
                    }
                }
                if (!listaContratoEstornados.isEmpty()) {
                    enviarEmailContratosEstornados(true);
                    for(ContratoVO contratoVO : listaContratoEstornados) {
                        contratoFacade.inserirAvisoContratoEstornado(contratoVO, contratoVO.getCliente());
                    }
                }

            } catch (Exception ex) {
                logarErro("[DEBUG] Erro ao processar contratos para Estorno Automático: " + ex.getMessage());
            } finally {
                if (errosTentativasEstorno.length() > 0) {
                    UteisValidacao.enfileirarEmail(new MsgTO("ESTORNOAUTOMATICO",
                            "Estorno Automático - Erro ao processar", errosTentativasEstorno));
                }
            }

        } else {
            resultadoEstorno.getResultado().put("Quantidade de Dias após o lançamento para o Contrato ser Estornado Automaticamente não está desabilitado!");
        }
        resultadoServicos.gravarResultado(resultadoEstorno);
    }

    public void estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(Date dia, Boolean estornarApenasContratosOrigemVendasOnline) {
        Integer qtdDiasPrimeiraParcelaVencidaEstornarContrato = 0;

        if(estornarApenasContratosOrigemVendasOnline) {
            qtdDiasPrimeiraParcelaVencidaEstornarContrato = configSistema.getQtdDiaPrimeiraParcelaVencidaEstornarContratoOrigemVendasOnline();
        } else {
            qtdDiasPrimeiraParcelaVencidaEstornarContrato = configSistema.getQtdDiaPrimeiraParcelaVencidaEstornarContrato();
        }

        if (qtdDiasPrimeiraParcelaVencidaEstornarContrato > 0) {
            ClienteVO clienteVO;
            try {
                List<ContratoVO> listaPotencial = contratoFacade.consultarContratosPrimeiraParcelaVencidaEstornoAutomatico(
                        dia, qtdDiasPrimeiraParcelaVencidaEstornarContrato);
                ResultadoServicosVO resultadoEstorno = new ResultadoServicosVO(ServicoEnum.ESTORNO, "estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga");
                for (ContratoVO contratoVO : listaPotencial) {
                    Boolean contratoNaoPossuiOrigemVendasOnline = estornarApenasContratosOrigemVendasOnline && contratoVO.getOrigemSistema().getCodigo() != 9;
                    if(contratoNaoPossuiOrigemVendasOnline) {
                        continue; /////IGNORA CONTRATOS QUE NAO SEJAM DE ORIGEM VNDAS ONLINE
                    }
                    if(movParcelaFacade.existeParcelaContratoEmRemessaGeradaOuAguardando(contratoVO.getCodigo(), new ArrayList<>(TipoRemessaEnum.ITAU_BOLETO.getId()))){
                        continue;
                    }
                    clienteVO = clienteFacade.consultarPorCodigoPessoa(contratoVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ContratoVO contratoCompleto = contratoFacade.consultarPorChavePrimaria(contratoVO.getCodigo(),
                            Uteis.NIVELMONTARDADOS_PARCELA);
                        
                    contratoCompleto.setUsuarioVO(usuarioVO);
                    EmpresaVO emp = zwFacade.getEmpresa().consultarPorChavePrimaria(
                            contratoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    clienteVO.setEmpresa(emp);
                    contratoCompleto.setCliente(clienteVO);
                    contratoCompleto.setEmpresa(emp);
                    contratoCompleto.getPlano().setDataVencimentoParcela(contratoFacade.getDataVencimentoParcelaMaisProximaNaoPaga(contratoCompleto));

                    try {
                        if (!UteisValidacao.emptyNumber(contratoCompleto.getContratoResponsavelRenovacaoMatricula()) && 
                                !estornarRenovacaoFutura(contratoCompleto,clienteVO)) { //Faz o estorno do contrato futuro e retorna false em caso de não conseguir
                            throw new Exception("Contrato já renovado e contrato futuro teve problemas no estorno");
                        }
                        contratoCompleto.setContratoPossuiRemessaBoleto(movParcelaFacade.consultarParcelasRemessasComBoletosItauDeContrato(contratoCompleto.getCodigo()));
                        contratoFacade.montarListasParaEstorno(contratoCompleto);
                        if(contratoFacade.contratosAindaComPrimeiraParcelaVencidaEstornoAutomatico(dia,qtdDiasPrimeiraParcelaVencidaEstornarContrato, contratoCompleto.getCodigo())) {
                            contratoFacade.estornoContrato(contratoCompleto, clienteVO, null, "Estorno Automático");
                            processarCliente(clienteVO, dia);
                            listaContratoEstornados.add(contratoCompleto);
                        }

                    } catch (Exception e) {
                        String msg = String.format("[DEBUG] => Erro ao estornar contrato considerando primeira parcela em aberto "
                                        + "\"%s\" do cliente \"%s\" da empresa \"%s\" mensagem: \"%s\"",
                                contratoVO.getCodigo(), clienteVO.getPessoa().getNome(),
                                clienteVO.getEmpresa().getNome(), e.getMessage());
                        logarErro(msg);
                        resultadoEstorno.getResultado().put(msg);
                    }
                }
                resultadoServicos.gravarResultado(resultadoEstorno);
                if (!listaContratoEstornados.isEmpty()) {
                    enviarEmailContratosEstornados(false);
                    for(ContratoVO contrato : listaContratoEstornados) {
                        contratoFacade.inserirAvisoContratoEstornado(contrato, contrato.getCliente());
                    }
                }

            } catch (Exception ex) {
                logarErro("[DEBUG] Erro ao processar contratos considerando primeira parcela em aberto p/ Estorno Automático: " + ex.getMessage());
            } finally {
                if (errosTentativasEstorno.length() > 0) {
                    UteisValidacao.enfileirarEmail(new MsgTO("ESTORNOAUTOMATICO",
                            "Estorno Automático Contratos considerando primeira parcela em aberto - Erro ao processar", errosTentativasEstorno));
                }
            }

        }
    }

    private void processarCliente(ClienteVO cliente, Date dia) throws Exception {
        // pega o ultimo contrato vigente da pessoa
        ContratoVO contrato = contratoFacade.consultarContratoVigentePorPessoa(cliente.getPessoa().getCodigo(), false,false, Uteis.NIVELMONTARDADOS_MINIMOS);
        // se contrato encontrado
        if (contrato.getCodigo().intValue() != 0) {
            // altera o historico se necessario
            Contrato.gerarHistoricoTemporalUmContrato(contrato.getCodigo());
            // exclui a situacao sintetica do cliente, que ainda é relativa ao contrato estornado
            zwFacade.getSituacaoClienteSinteticoDW().excluir(cliente.getCodigo().intValue());
        }
        // atualiza a situacao do cliente
        cliente.setDeveAtualizarDependentesSintetico(true);
        zwFacade.atualizarSintetico(cliente, dia, SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
    }

    private void enviarEmailContratosEstornados(boolean isDataLancamento) throws Exception {
        StringBuffer textoEmail = montarTextoEmail(listaContratoEstornados, isDataLancamento);
        UteisEmail email = new UteisEmail();
        ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
        email.novo("", config);
        UsuarioVO usuario = new UsuarioVO();
        usuario.setNome("ZillyonWeb");

        email.setRemetente(usuario);
        //enviar email aos responsaveis
        String[] destinos = configSistema.getListaEmailsRecorrencia().toArray(
                new String[configSistema.getListaEmailsRecorrencia().size()]);
        email.enviarEmailN(destinos, textoEmail.toString(),
                "ZillyonWeb: Contratos Estornados Automaticamente", "");
    }

    private StringBuffer montarTextoEmail(List<ContratoVO> listaContratoEstornados, boolean isDatalancamento) {
        StringBuffer sb = new StringBuffer();
        sb.append("<html><body style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;color:#000;\">");
        sb.append("Olá! <br>").
                append("Você está recebendo este e-mail de aviso ").
                append("automático, para informar sobre os ").
                append("<b>CONTRATOS ESTORNADOS AUTOMATICAMENTE</b> de sua Academia: <br>");

        for (ContratoVO contratoVO : listaContratoEstornados) {
            sb.append("<br><br><b>Contrato:</b> ").append(contratoVO.getCodigo()).
                    append(" - <b>ALUNO:</b> ").append(contratoVO.getPessoa().getNome());
            sb.append("<br><b>Data de Lançamento:</b> ").append(contratoVO.getDataLancamento_Apresentar());
            sb.append("<br><b>Empresa:</b> ").append(contratoVO.getEmpresa().getNome());
            if(isDatalancamento){
                sb.append("<br><b>Motivo: </b>").append(" Mais de ").
                        append(configSistema.getQtdDiasEstornoAutomaticoContrato()).
                        append(" dias após seu lançamento, sem nenhum pagamento efetuado.");
            } else {
                 sb.append("<br><b>Motivo: </b>").append(" Aluno estava com a primeira parcela vencida há mais de ").
                        append(configSistema.getQtdDiaPrimeiraParcelaVencidaEstornarContrato()).
                        append(" dias.");
            }
        }
        try {
            sb.append("<br><b>Info: </b>").append(Criptografia.encrypt(getCon().getMetaData().getURL(),
                    SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
            sb.append("<br><b>From: </b>").append(Criptografia.encrypt(InetAddress.getLocalHost().getHostAddress(),
                    SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM));
        } catch (Exception ex) {
            Logger.getLogger(EstornoContratoAutomaticoService.class.getName()).
                    log(Level.SEVERE, null, ex);
        }
        sb.append("</body>");
        sb.append("</html>");
        return sb;
    }

    public static void main(String... args) throws Exception {
        Connection c = new DAO().obterConexaoEspecifica("stella");
        Conexao.guardarConexaoForJ2SE(c);
        Date hoje = Calendario.hoje();
        EstornoContratoAutomaticoService service = new EstornoContratoAutomaticoService(c);

        service.estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(hoje, false);
        service.estornarContratosAutomaticamenteQuandoPrimeiraParcelaNaoPaga(hoje, true);

    }

    private boolean estornarRenovacaoFutura(ContratoVO anterior, ClienteVO clienteVO) {
        try {
            if(movParcelaFacade.existeParcelaContratoEmRemessaGeradaOuAguardando(anterior.getContratoResponsavelRenovacaoMatricula(), new ArrayList<>(TipoRemessaEnum.ITAU_BOLETO.getId()))
                    || movParcelaFacade.existeParcelaEmSituacaoPorContrato(anterior.getContratoResponsavelRenovacaoMatricula(), "PG")){
                throw new Exception("Contrato não pode ser estornado, pois tem remessa aguardando retorno ou alguma parcela paga");
            }
            
            ContratoVO futuro = contratoFacade.consultarPorChavePrimaria(anterior.getContratoResponsavelRenovacaoMatricula(), Uteis.NIVELMONTARDADOS_PARCELA);
            futuro.setEmpresa(anterior.getEmpresa());
            futuro.setUsuarioVO(usuarioVO);
            if(!UteisValidacao.emptyNumber(futuro.getContratoResponsavelRenovacaoMatricula()) && 
                    !estornarRenovacaoFutura(futuro, clienteVO)){ // recursão, para avaliar uma linha de contratos com vários  contratos futuros
                throw new Exception("Problemas no estorno de contratos futuros");
            }
            //////setar 0 no campo contratoResponsavelRenovaçãoMatricula para efetivar o estorno do contrato vigente evitando a validação
            anterior.setContratoResponsavelRenovacaoMatricula(0);
            futuro.setContratoPossuiRemessaBoleto(movParcelaFacade.consultarParcelasRemessasComBoletosItauDeContrato(futuro.getCodigo()));
            contratoFacade.montarListasParaEstorno(futuro);
            contratoFacade.estornoContrato(futuro, clienteVO, null, "Estorno Automático");
            listaContratoEstornados.add(futuro);
        } catch (Exception e) {
            logarErro(
                    String.format("[DEBUG] => Erro ao estornar contrato futuro para estornar contrato vigente com primeira parcela atrasada. Contrato "
                    + "\"%s\" do cliente \"%s\" da empresa \"%s\" mensagem: \"%s\"",
                            anterior.getContratoResponsavelRenovacaoMatricula(), clienteVO.getPessoa().getNome(),
                            clienteVO.getEmpresa().getNome(), e.getMessage()));
            return false;
        }
        return true;
    }
}

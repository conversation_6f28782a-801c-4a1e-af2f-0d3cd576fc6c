/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.MovProduto;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoTipoProdutoVO;

/**
 *
 * <AUTHOR>
 */
public class RelatorioFaturamentoService {

    Connection con;

    public RelatorioFaturamentoService(Connection con) {
        this.con = con;
    }

    public FaturamentoSinteticoProdutoVO gerarFaturamento(Date inicio, Date fim, Integer empresa) throws Exception {
        MovProduto movProdutoDao = new MovProduto(con);
        //cria objeto de total geral
        FaturamentoSinteticoProdutoVO totalGeral = new FaturamentoSinteticoProdutoVO(); // total geral de toda a consulta
        totalGeral.setDescricao("TOTAL GERAL");
        if (inicio.compareTo(fim) > 0) {
            throw new Exception("O Relatório só pode ser gerado com a Data de Início menor que a Data de Término para o período da pesquisa.");
        }
        List<FaturamentoSinteticoTipoProdutoVO> listaTipoProdutoVO = new ArrayList<FaturamentoSinteticoTipoProdutoVO>();
        List<PeriodoMensal> periodos = Uteis.getPeriodosMensaisEntreDatas(inicio, fim);
        movProdutoDao.consultaFaturamentoRecebido(empresa, listaTipoProdutoVO,
                periodos, getMapaTipoProduto(), totalGeral, "nomeDuracao", null, null, false);
        return totalGeral;

    }
    
    public Map<String, Boolean> getMapaTipoProduto() {
        Map<String, Boolean> mapaTipoProduto = new HashMap<String, Boolean>();
        mapaTipoProduto.put("PM", true);
        return mapaTipoProduto;
    }
}

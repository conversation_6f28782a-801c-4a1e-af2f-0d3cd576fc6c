/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.ConfiguracaoBI;
import negocio.facade.jdbc.contrato.MovProduto;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoRelTO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoTipoProdutoVO;

/**
 *
 * <AUTHOR>
 */
public class RelatorioCompetenciaService {

    Connection con;

    public RelatorioCompetenciaService(Connection con) {
        this.con = con;
    }

    private ConfiguracaoBIVO getConfiguracao(ConfiguracaoBIEnum cfg, List<ConfiguracaoBIVO> cfgs){
        for(ConfiguracaoBIVO c : cfgs){
            if(c.getConfiguracao().equals(cfg)){
                return c;
            }
        }
        return null;
    }

    public CompetenciaSinteticoProdutoVO gerarCompetencia(Date inicio, Date fim, Integer empresa, boolean somentePlano) throws Exception {

        ConfiguracaoBI dao = new ConfiguracaoBI(con);
        List<ConfiguracaoBIVO> configuracoes = dao.consultarPorBI(BIEnum.TICKET_MEDIO, empresa);
        Boolean desconsiderarAlunosInadimplentes = getConfiguracao(ConfiguracaoBIEnum.FILTRAR_PARCELAS_VENCIDAS_NAO_PAGAS, configuracoes).getValorAsBoolean();
        dao = null;

        String agrupamento = "nomeDuracao";
        MovProduto movProduto = new MovProduto(con);// cria uma estancia de movProduto - onde fica o sql !
        List<PeriodoMensal> periodos = Uteis.getPeriodosMensaisEntreDatas(inicio, fim);// pega data inicial e final para o relatorios
        List<CompetenciaSinteticoTipoProdutoVO> listaTipoProdutoVO = new ArrayList<CompetenciaSinteticoTipoProdutoVO>();// cria uma lista da propria classe
        CompetenciaSinteticoProdutoVO totalGeral = new CompetenciaSinteticoProdutoVO(); // total geral de toda a consulta
        totalGeral.setDescricao("TOTAL GERAL");
        //TABLE - TIPOPRODUTO
        String tipoProduto = somentePlano ? "PM" : "";
        CompetenciaSinteticoTipoProdutoVO tipoProdutoVO = new CompetenciaSinteticoTipoProdutoVO(); // estancia do competencia, onde tem se lista de mês e lista de produtos
        tipoProdutoVO.setTipoProduto("Mês de Referência Plano"); // substitui as abreviacoes  do tipo de produto para o nome
        List<CompetenciaSinteticoProdutoVO> listaProduto = 
                movProduto.consultarProdutosMovimentadoParaCompetenciaPeriodo(inicio,
                fim, preencherListaTipoProduto(tipoProduto),
                empresa, agrupamento, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, desconsiderarAlunosInadimplentes);
        // traz uma lista de CompetenciaSinteticoProdutoVO, filtrando por data e tipo do Produto: (matricula, produtoEstoque, mês de referencia, renovacao, devolucao e etc.. )
        //SUBTABLE - PRODUTO
        for (CompetenciaSinteticoProdutoVO fsProduto : listaProduto) { //laço que percorre uma lista de competenciaSinteticoProdutos              
            //COLUNA - MES x PRODUTO
            for (PeriodoMensal p : periodos) { // percorre um laço entre o periodo lançado na tela como filtro
                String mesReferencia = Uteis.getMesReferenciaData(p.getDataInicio());
                CompetenciaSinteticoProdutoMesVO produtoMesVO = movProduto.consultarCompetenciaSinteticoResumoPessoa(
                        mesReferencia, preencherListaTipoProduto(fsProduto.getProduto().getTipoProduto()),
                        empresa, fsProduto.getDuracao(),
                        fsProduto.getPlano(), fsProduto.getProduto().getCodigo(), fsProduto.getNomePlano(),
                        fsProduto.getProduto().getTipoProduto().equals("PM") ? null : fsProduto.getDescricao());
                fsProduto.getListaProdutoXMes().add(produtoMesVO);

                for (CompetenciaSinteticoResumoPessoaVO listaPessoas : produtoMesVO.getListaResumoPessoa()) {
                    listaPessoas.setMesLancamentoMovProduto(p.getDataInicio());
                }
            }
            if (!fsProduto.getListaProdutoXMes().isEmpty()) {
                tipoProdutoVO.getListaProduto().add(fsProduto);
            }
        }
        CompetenciaSinteticoProdutoVO totalizador = new CompetenciaSinteticoProdutoVO();
        if (!tipoProdutoVO.getListaProduto().isEmpty()) {
            
            totalizador.setDescricao("TOTALIZADOR");
            int tamanho = 0;
            for (CompetenciaSinteticoProdutoVO fsProduto : listaProduto) {
                tamanho = fsProduto.getListaProdutoXMes().size();
                break;
            }
            for (int i = 0; i < tamanho; i++) {
                CompetenciaSinteticoProdutoMesVO subTotalGeral;
                CompetenciaSinteticoProdutoMesVO totalizadorMes = new CompetenciaSinteticoProdutoMesVO();
                if (totalGeral.getListaProdutoXMes().size() <= i) {
                    subTotalGeral = new CompetenciaSinteticoProdutoMesVO();
                    totalGeral.getListaProdutoXMes().add(subTotalGeral);
                } else {
                    subTotalGeral = totalGeral.getListaProdutoXMes().get(i);
                }
                for (CompetenciaSinteticoProdutoVO fsProduto : listaProduto) {
                    CompetenciaSinteticoProdutoMesVO produtoMes = fsProduto.getListaProdutoXMes().get(i);
                    totalizadorMes.setQtd(totalizadorMes.getQtd().intValue() + produtoMes.getQtd().intValue());
                    totalizadorMes.setValor(totalizadorMes.getValor() + produtoMes.getValor());
                    totalizadorMes.getListaResumoPessoa().addAll(produtoMes.getListaResumoPessoa());
                    subTotalGeral.setQtd(subTotalGeral.getQtd().intValue() + produtoMes.getQtd().intValue());
                    subTotalGeral.setValor(subTotalGeral.getValor() + produtoMes.getValor());
                    subTotalGeral.getListaResumoPessoa().addAll(produtoMes.getListaResumoPessoa());
                }
                totalizador.getListaProdutoXMes().add(totalizadorMes);
            }
            tipoProdutoVO.getListaProduto().add(totalizador);
            tipoProdutoVO.setApresentarResultado(true);
            listaTipoProdutoVO.add(tipoProdutoVO);
        }

        return totalizador;
    }
    
    public List<String> preencherListaTipoProduto(String tipoProduto) {
        //analisa o tipo de produto para consulta
        //por padrão para consultar por Matricula, Rematricula, Renovacao sera adicionado os três para a pesquisa
        List<String> listaTipoProdutos = new ArrayList<String>();
        if (tipoProduto.equalsIgnoreCase("MARERN")) {
            listaTipoProdutos.add("MA");
            listaTipoProdutos.add("RE");
            listaTipoProdutos.add("RN");
        } else {
            if (!tipoProduto.isEmpty()) {
                listaTipoProdutos.add(tipoProduto);
            }
        }
        return listaTipoProdutos;
    }
}

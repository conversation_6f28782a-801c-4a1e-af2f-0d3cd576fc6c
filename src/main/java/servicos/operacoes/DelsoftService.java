package servicos.operacoes;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.contrato.ContratoControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoExcecaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoCondicaoPagamento;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.plano.PlanoExcecao;
import negocio.facade.jdbc.plano.PlanoHorario;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DelsoftService extends SuperEntidade {

    private static Connection con;

    public DelsoftService() throws Exception {
        super(con);
    }

    public static void main(String[] args) {
        // http://200.153.204.124:9099/getAllATC
        try {
//            JSONArray consulta = getConsulta("http://200.153.204.124", 9099, null, "getAllATC");
            con = DriverManager.getConnection("***************************************************************************","postgres", "pactodb");
            Empresa empresaDao = new Empresa(con);
            EmpresaVO empresaVO = empresaDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            Conexao.guardarConexaoForJ2SE("c44fa7a3d50f6830acc7a7647d110e33", con);
            inicializarDelsolftService(empresaVO, con);
        } catch (Exception ex) {

        }
    }

    public static void inicializarDelsolftService(EmpresaVO empresaVO, Connection connection) throws Exception {

        Conexao.guardarConexaoForJ2SE(con);
        JSONArray consulta = getConsulta(empresaVO.getHostIntegracaoDelsoft(), empresaVO.getPortaIntegracaoDelsoft(), empresaVO.getUsuarioAplicacaoDelsoft(), empresaVO.getSenhaAplicacaoDelsoft(), empresaVO.getTokenIntegracaoDelsoft(), empresaVO.getNomeAplicacaoDelsoft());
        con = connection;
        ZillyonWebFacade facade = new ZillyonWebFacade(con);
        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(con);
        Colaborador colaboradorDao = new Colaborador(con);
        Cliente clienteDao = new Cliente(con);
        Cidade cidadeDao = new Cidade(con);
        Pessoa pessoaDao = new Pessoa(con);

        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        ColaboradorVO colaboradorVO = colaboradorDao.consultarPorNomeColaborador("PACTO - M", empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        int contadorErros = 0;

        for (int i = 0; i < consulta.length(); i++) {

            try {
                if(!isCPF(consulta, i)) {
                    continue;
                }
                String cpfConsulta = formataCPFInteiro(consulta.getJSONObject(i).getLong("cpf"));
                PessoaVO pessoaVO = null;
                if (!UteisValidacao.emptyString(cpfConsulta)) {
                    pessoaVO = pessoaDao.consultarPorCPF(Uteis.formatarCpfCnpj(cpfConsulta, false), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                if(pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    pessoaVO = pessoaDao.consultarPorMatriculaExterna(Integer.parseInt(consulta.getJSONObject(i).get("matricula").toString()), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                if(pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    pessoaVO = pessoaDao.consultarPorNomeSemMatriculaExternaOuCpf(consulta.getJSONObject(i).optString("nome", "").replace("  ", " ").trim(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                PessoaVO pessoaVOConsulta = montarDadosPessoaVOJSON(consulta.getJSONObject(i));
                Date dataAtual = new Date(System.currentTimeMillis());
                if (pessoaVO != null && !UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    ClienteVO clienteVO = clienteDao.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    pessoaVO = atualizaDadosPessoa(pessoaVO, pessoaVOConsulta);
                    if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        clienteVO = criaCliente(pessoaVO, empresaVO);
                        criaVinculo(colaboradorVO, clienteVO);
                        clienteDao.gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO);
                        clienteDao.atualizarMatriculaAluno(clienteVO.getCodigoMatricula());
                        clienteDao.incluirClienteSimplificadoImportacao(clienteVO);
                        facade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                        clienteDao.atualizarMatriculaExternaAluno(clienteVO.getCodigo(), Integer.parseInt(consulta.getJSONObject(i).get("matricula").toString()));
                    } else {
                        clienteDao.atualizarMatriculaExternaAluno(clienteVO.getCodigo(), Integer.parseInt(consulta.getJSONObject(i).get("matricula").toString()));
                        pessoaDao.alterarConexaoInicializadaSemPermissao(pessoaVO, con);
                    }

                    criaContratoCasoNaoExista(empresaVO, clienteDao, pessoaDao, colaboradorVO, pessoaVO, dataAtual, clienteVO);
                    System.out.println("Sucesso PESSOA ATUALIZADA: "+pessoaVO.getNome());
                } else {
                    pessoaVOConsulta = atualizaDadosPessoa(new PessoaVO(), pessoaVOConsulta);
                    pessoaVOConsulta.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
                    ClienteVO clienteVO = criaCliente(pessoaVOConsulta, empresaVO);
                    criaVinculo(colaboradorVO, clienteVO);
                    clienteDao.gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO);
                    clienteDao.atualizarMatriculaAluno(clienteVO.getCodigoMatricula());
                    clienteDao.incluirClienteSimplificadoImportacao(clienteVO);
                    facade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
                    criaContrato(empresaVO, clienteDao, colaboradorVO, dataAtual, clienteVO);
                    clienteDao.atualizarMatriculaExternaAluno(clienteVO.getCodigo(), Integer.parseInt(consulta.getJSONObject(i).get("matricula").toString()));
                    System.out.println("Sucesso NOVA PESSOA: "+pessoaVOConsulta.getNome());
                }

            } catch (Exception e) {
                contadorErros++;
                System.err.println("Erro: "+e.getMessage()+" - "+contadorErros + " - " + consulta.getJSONObject(i).toString());
                Uteis.logar(e, MentorWebService.class);
            }
        }

    }

    private static String formataCPFInteiro(Long cpf) {
        if (cpf == null || cpf == 0 || cpf.toString().length() > 11) { //não aceita CNPJ
            return "";
        }
        return StringUtilities.formatarCampoForcandoZerosAEsquerda(cpf, 11);
    }

    private static boolean isCPF(JSONArray consulta, int i) {
        return consulta.getJSONObject(i).get("cpf").toString().split("").length < 13;
    }

    private static void criaContratoCasoNaoExista(EmpresaVO empresaVO, Cliente clienteDao, Pessoa pessoaDao, ColaboradorVO colaboradorVO, PessoaVO pessoaVO, Date dataAtual, ClienteVO clienteVO) throws Exception {
        if (!pessoaDao.existeContratoParaPessoa(pessoaVO.getCodigo())) {
            criaContrato(empresaVO, clienteDao, colaboradorVO, dataAtual, clienteVO);
        }
    }

    private static void criaContrato(EmpresaVO empresaVO, Cliente clienteDao, ColaboradorVO colaboradorVO, Date dataAtual, ClienteVO clienteVO) throws Exception {
        Plano planoDao = new Plano(con);
        PlanoVO planoVO = planoDao.consultarPorChavePrimaria(empresaVO.getPlanoAplicacaoDelsoft().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        PlanoExcecao planoExcecaoDao = new PlanoExcecao(con);
        List<PlanoExcecaoVO> planosExcecaoVO = planoExcecaoDao.consultarPorPlano(planoVO.getCodigo());

        if (planosExcecaoVO.size() == 1) {
            Contrato contratoDao = new Contrato(con);
            ContratoVO contratoVO = new ContratoVO();
            contratoVO.setConsultor(colaboradorVO);
            ContratoControle contratoControle = contratoDao.obterContratoControle(planoVO.getCodigo(), clienteDao.consultarPorCodigoMatricula(clienteVO.getCodigoMatricula(), 1, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo(), false, 1, contratoVO, contratoVO);

            PlanoDuracao planoDuracaoDao = new PlanoDuracao(con);
            PlanoDuracaoVO planoDuracaoVO = planoDuracaoDao.consultarPorPlanoRecorrencia(planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoControle.getContratoVO().setVigenciaDe(dataAtual);

            Usuario usuarioDao = new Usuario(con);
            UsuarioVO usuarioVO = usuarioDao.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);

            contratoControle.getContratoVO().setVigenciaAte(Uteis.somarMeses(dataAtual, planoDuracaoVO.getNumeroMeses()));
            contratoControle.getContratoVO().setVigenciaAteAjustada(Uteis.somarMeses(dataAtual, planosExcecaoVO.get(0).getDuracao()));
            contratoControle.getContratoVO().setDataMatricula(dataAtual);
            contratoControle.setDataInicioContrato(dataAtual);
            contratoControle.getContratoVO().setPlano(planoVO);
            contratoControle.getContratoVO().setDataLancamento(dataAtual);
            contratoControle.getContratoVO().setPlanoDuracao(planoDuracaoVO);
            contratoControle.getContratoVO().getContratoModalidadeVOs().get(0).getModalidade().setModalidadeEscolhida(true);

            PlanoHorario planoHorarioDao = new PlanoHorario(con);
            List<PlanoHorarioVO> planoHorarioVO = planoHorarioDao.consultarPlanoHorarios(planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
            contratoControle.getContratoVO().getPlano().setPlanoHorarioVOs(planoHorarioVO);
            contratoControle.getContratoVO().setPlanoHorario(planoHorarioVO.get(0));

            PlanoCondicaoPagamento planoCondicaoPagamentoDao = new PlanoCondicaoPagamento(con);
            PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = planoCondicaoPagamentoDao.consultarPorPlanoDuracaoCondicao(planoDuracaoVO.getCodigo(), 1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            contratoControle.getContratoVO().setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);
            contratoControle.getContratoVO().setOrigemSistema(OrigemSistemaEnum.IMPORTACAO_API);

            String fecharNegociacao = contratoControle.fecharNegociacao();
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }

            contratoControle.getContratoVO().setConsultor(colaboradorVO);

            String gravarContrato = contratoControle.gravar(usuarioVO, false);
            if (gravarContrato.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            Log logDao = new Log(con);
            LogVO logVO = logDao.novo();
            logVO.setPessoa(clienteVO.getPessoa().getCodigo());
            logVO.setNomePessoa(clienteVO.getPessoa().getNome());
            logVO.setDataAlteracao(dataAtual);
            logVO.setNomeEntidade("CONTRATO");
            logVO.setNomeEntidadeDescricao("CONTRATO");
            logVO.setResponsavelAlteracao("IMPORTACAO API");
            logVO.setNomeCampo("TODOS");
            logVO.setCliente(clienteVO.getCodigo());
            logVO.setOperacao("INCLUSAO");
            logVO.setValorCampoAlterado("*Informados via processo Importação*");
            logDao.incluir(logVO);
        } else {
            throw new Exception("Não foi possível criar o contrato, pois não existe nenhum planoexcessao para o plano configurado em Empresas!");
        }
    }

    private static ClienteVO criaCliente(PessoaVO pessoaVO, EmpresaVO empresaVO) {
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setPessoa(pessoaVO);
        clienteVO.setEmpresa(empresaVO);
        clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
        clienteVO.setMatricula("");

        return clienteVO;
    }

    private static void criaVinculo(ColaboradorVO consultor, ClienteVO clienteVO) {
        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setColaborador(consultor);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
        vinculoVO.setCliente(clienteVO);
        clienteVO.getVinculoVOs().add(vinculoVO);
    }

    private static void registraLogInclusaoPessoa(PessoaVO pessoaVO) throws Exception {
        Log logFacade = new Log(con);
        try {
            if (!UteisValidacao.emptyString(pessoaVO.getCfp())) {
                pessoaVO.setCfp(pessoaVO.getCfp());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "cpf", "", pessoaVO.getCfp());
            }
            if (!UteisValidacao.emptyString(pessoaVO.getRg())) {
                pessoaVO.setRg(pessoaVO.getRg());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "RG", "", pessoaVO.getRg());
            }
            if (pessoaVO.getDataNasc() != null) {
                pessoaVO.setDataNasc(pessoaVO.getDataNasc());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "Data de Nascimento", "", pessoaVO.getDataNasc_Apresentar());
            }
            if (!UteisValidacao.emptyString(pessoaVO.getNome())) {
                pessoaVO.setNome(pessoaVO.getNome());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "nome", "", pessoaVO.getNome());
            }

            if (!UteisValidacao.emptyList(pessoaVO.getEnderecoVOs())) {
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "Endereço", "", pessoaVO.getEnderecoVOs().get(0).getEndereco());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "Número", "", pessoaVO.getEnderecoVOs().get(0).getNumero());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "Bairro", "", pessoaVO.getEnderecoVOs().get(0).getBairro());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "INCLUSÃO", "PESSOA", "Pessoa", "Complemento", "", pessoaVO.getEnderecoVOs().get(0).getComplemento());

            }
        } catch (Exception ex) {
            registrarLog(pessoaVO.getCodigo(), logFacade,
                    "ERRO AO CRIAR LOG", "PESSOA", "Pessoa", "Erro", "", "");
        }
    }

    private static PessoaVO atualizaDadosPessoa(PessoaVO pessoaVOAntesAlteracao, PessoaVO pessoaVOConsulta) throws Exception {
        PessoaVO pessoaVO = pessoaVOAntesAlteracao;
        Log logFacade = new Log(con);
        try {

            if (!pessoaVOAntesAlteracao.getCfp().equals(pessoaVOConsulta.getCfp())) {
                pessoaVO.setCfp(pessoaVOConsulta.getCfp());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "ALTERAÇÃO", "PESSOA", "Pessoa", "cpf", pessoaVOAntesAlteracao.getCfp(), pessoaVO.getCfp());
            }
            if (!pessoaVOAntesAlteracao.getRg().equals(pessoaVOConsulta.getRg())) {
                pessoaVO.setRg(pessoaVOConsulta.getRg());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "ALTERAÇÃO", "PESSOA", "Pessoa", "RG", pessoaVOAntesAlteracao.getRg(), pessoaVO.getRg());
            }
            if (pessoaVOAntesAlteracao.getDataNasc() == null || !pessoaVOAntesAlteracao.getDataNasc().equals(pessoaVOConsulta.getDataNasc())) {
                pessoaVO.setDataNasc(pessoaVOConsulta.getDataNasc());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "ALTERAÇÃO", "PESSOA", "Pessoa", "Data de Nascimento", pessoaVOAntesAlteracao.getDataNasc_Apresentar(), pessoaVO.getDataNasc_Apresentar());
            }
            if (!pessoaVOAntesAlteracao.getNome().equals(pessoaVOConsulta.getNome())) {
                pessoaVO.setNome(pessoaVOConsulta.getNome());
                registrarLog(pessoaVO.getCodigo(), logFacade,
                        "ALTERAÇÃO", "PESSOA", "Pessoa", "nome", pessoaVOAntesAlteracao.getNome(), pessoaVO.getNome());
            }

            if (!UteisValidacao.emptyList(pessoaVOConsulta.getEnderecoVOs())) {
                pessoaVO.setEnderecoVOs(new ArrayList<>());
                pessoaVO.getEnderecoVOs().addAll(pessoaVOConsulta.getEnderecoVOs());
                if (!UteisValidacao.emptyList(pessoaVOAntesAlteracao.getEnderecoVOs())) {
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Endereço", pessoaVOAntesAlteracao.getEnderecoVOs().get(0).getEndereco(), pessoaVO.getEnderecoVOs().get(0).getEndereco());
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Número", pessoaVOAntesAlteracao.getEnderecoVOs().get(0).getNumero(), pessoaVO.getEnderecoVOs().get(0).getNumero());
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Bairro", pessoaVOAntesAlteracao.getEnderecoVOs().get(0).getBairro(), pessoaVO.getEnderecoVOs().get(0).getBairro());
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Complemento", pessoaVOAntesAlteracao.getEnderecoVOs().get(0).getComplemento(), pessoaVO.getEnderecoVOs().get(0).getComplemento());
                } else {
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Endereço", "", pessoaVO.getEnderecoVOs().get(0).getEndereco());
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Número", "", pessoaVO.getEnderecoVOs().get(0).getNumero());
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Bairro", "", pessoaVO.getEnderecoVOs().get(0).getBairro());
                    registrarLog(pessoaVO.getCodigo(), logFacade,
                            "ALTERAÇÃO", "PESSOA", "Pessoa", "Complemento", "", pessoaVO.getEnderecoVOs().get(0).getComplemento());
                }
            }
            return pessoaVO;

        } catch (Exception ex) {
            registrarLog(pessoaVO.getCodigo(), logFacade,
                    "ERRO AO CRIAR LOG", "PESSOA", "Pessoa", "Erro", "", "");
            return pessoaVO;
        }
    }

    private static void registrarLog(Integer codigoPessoa, Log logDAO,
                                     String operacao, String entidade, String entidadeDescricao, String nomeCampo, String valorAnterior, String valorAlterado) throws Exception {
        LogVO log = new LogVO();
        log.setOperacao(operacao);
        if ("Erro".equals(nomeCampo)) {
            log.setChavePrimaria("");
        } else {
            log.setChavePrimaria(codigoPessoa.toString());
        }
        log.setResponsavelAlteracao("DELSOFT SERVICE");
        log.setNomeEntidade(entidade);
        log.setNomeEntidadeDescricao(entidadeDescricao);
        log.setDataAlteracao(Calendario.hoje());
        log.setNomeCampo(nomeCampo);
        log.setValorCampoAnterior(valorAnterior);
        log.setValorCampoAlterado(valorAlterado);
        log.setPessoa(codigoPessoa);
        logDAO.incluir(log);
    }

    private static JSONArray getConsulta(String url, Integer porta, String usuario, String senha, String token, String nomeAplicacao) throws IOException {
        String urlFinal = url;
        if (!UteisValidacao.emptyNumber(porta)) {
            urlFinal = urlFinal + ":" + porta;
        }
        if (!UteisValidacao.emptyString(nomeAplicacao)) {
            urlFinal = urlFinal + "/" + nomeAplicacao;
        }
        Map<String, String> header = new HashMap<>();
        if (!UteisValidacao.emptyString(usuario) && !UteisValidacao.emptyString(senha)) {
            String plainCredentials = usuario + ":" + senha;
            String base64Credentials = new String(Base64.getEncoder().encode(plainCredentials.getBytes()));
            String authorizationHeader = "Basic " + base64Credentials;
            header.put("Authorization", authorizationHeader);
        }
        if (!UteisValidacao.emptyString(token)) {
            header.put("Authorization", token);
        }

        String result = ExecuteRequestHttpService.executeHttpRequest(urlFinal, null, header,
                ExecuteRequestHttpService.METODO_POST, Charsets.UTF_8.name());

        return new JSONArray(result);
    }

    public static PessoaVO montarDadosPessoaVOJSON(JSONObject obj) throws Exception {
        try {
            PessoaVO pessoaVO = new PessoaVO();

            pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());
            pessoaVO.setNome(obj.optString("nome", "").replace("  ", " ").trim());
            pessoaVO.setDataNasc(Uteis.getDate(obj.getString("datanasc")));
            String cpf = formataCPFInteiro(obj.getLong("cpf"));
            pessoaVO.setCfp(Uteis.formatarCpfCnpj(cpf, false));
            pessoaVO.setRg(obj.optString("rg", ""));

            EnderecoVO enderecoVO = new EnderecoVO();
            enderecoVO.setEndereco(obj.optString("endereco", ""));
            Long numeroEnderecoConsulta = obj.optLong("numero", 0);
            enderecoVO.setNumero(String.valueOf(numeroEnderecoConsulta));
            enderecoVO.setBairro(obj.optString("bairro", ""));
            enderecoVO.setComplemento(obj.optString("complemento", ""));
            pessoaVO.getEnderecoVOs().add(enderecoVO);

            Cidade cidadeDao = new Cidade(con);
            String nomeCidadeConsulta = obj.optString("cidade");
            CidadeVO cidadeVO = null;

            if (!UteisValidacao.emptyString(nomeCidadeConsulta)) {
                cidadeVO = cidadeDao.consultarPorNome(nomeCidadeConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (cidadeVO != null && !UteisValidacao.emptyNumber(cidadeVO.getCodigo())) {
                pessoaVO.setCidade(cidadeVO);
                pessoaVO.setEstadoVO(cidadeVO.getEstado());
                pessoaVO.setPais(cidadeVO.getPais());
            }
            return pessoaVO;
        } catch (Exception ex) {
            throw new Exception(ex);
        }
    }
}

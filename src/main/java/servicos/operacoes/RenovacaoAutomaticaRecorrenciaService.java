package servicos.operacoes;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.ClienteGrupoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.impl.apf.RecorrenciaService;

import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import static controle.arquitetura.SuperControle.registrarLogErroObjetoVO;
import static controle.arquitetura.SuperControle.registrarLogObjetoVO;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

public class RenovacaoAutomaticaRecorrenciaService {

    public RenovacaoAutomaticaRecorrenciaService(Connection con) {
        this.con = con;
    }
    private ContratoRecorrenciaVO contratoRecorrenciaVelho;
    private ContratoRecorrenciaVO contratoRecorrenciaNovo;
    private ZillyonWebFacade zwFacade;
    private Connection con;

    public boolean validarRenovacao(Integer codEmpresa) throws Exception{
        EmpresaVO empresa = getFacade().getEmpresa().consultarPorCodigo(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return empresa.isNaoRenovarContratoSemIndiceFinanceiro();
    }
    
    /**
     * Método que renovará automaticamente o contrato de recorrência
     * <AUTHOR>
     * 27/07/2011
     */
    public ContratoRecorrenciaVO renovarAutomatico(ContratoRecorrenciaVO contratoRecorrencia, boolean simulacao, Integer origemSistema) throws Exception {
        boolean planoDiferente = false;
        setContratoRecorrenciaNovo(contratoRecorrencia);
        // obter os dados de recorrencia do plano do contrato
        PlanoRecorrenciaVO planoRecorrencia;
        if (!UteisValidacao.emptyNumber(getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoDiferenteRenovacao())) {// se plano diferente e plano recorrente e nao for plano credito ou turma
            setContratoRecorrenciaVelho((ContratoRecorrenciaVO) getContratoRecorrenciaNovo().getClone(true));
            getContratoRecorrenciaVelho().setContrato((ContratoVO) getContratoRecorrenciaNovo().getContrato().getClone(true));
            getContratoRecorrenciaVelho().getContrato().setPlano((PlanoVO) getContratoRecorrenciaNovo().getContrato().getPlano().getClone(true));
            getContratoRecorrenciaNovo().getContrato().setPlano(getZWFacade().getPlano().consultarPorChavePrimaria(
                    getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoDiferenteRenovacao(), Uteis.NIVELMONTARDADOS_TODOS));
            planoRecorrencia = getZWFacade().getPlanoRecorrencia().consultarPlanoRecorrencia(
                    getContratoRecorrenciaNovo().getContrato().getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!getContratoRecorrenciaNovo().getContrato().getPlano().isVendaCreditoTreino() &&
                    getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoModalidadeVOs().stream().noneMatch(pM -> pM.getModalidade().getUtilizarTurma())) {
                planoDiferente = true;
                preencherContratoRecorrencia(contratoRecorrencia.getContrato().getCodigo(), planoRecorrencia, planoDiferente);
            }
        } else {
            planoRecorrencia = getZWFacade().getPlanoRecorrencia().consultarPlanoRecorrencia(
                    getContratoRecorrenciaNovo().getContrato().getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            planoDiferente = false;
            preencherContratoRecorrencia(contratoRecorrencia.getContrato().getCodigo(), planoRecorrencia, planoDiferente);
        }

        if(validarRenovacao(contratoRecorrencia.getContrato().getEmpresa().getCodigo())) {
            if(getContratoRecorrenciaNovo().getIndiceFinanceiroVO() != null
                    || (origemSistema == 4 || origemSistema == 6 || origemSistema == 7 || origemSistema == 9)) { //libera se  forem renovações antecipadas feitas por canais on line
                return renovarContrato(simulacao, origemSistema, planoDiferente);
            } else {
                return null;
            }
        } else {
            return renovarContrato(simulacao, origemSistema, planoDiferente);
        }
    }

    private ContratoRecorrenciaVO renovarContrato(boolean simulacao, Integer origemSistema, boolean planoDiferente) throws Exception{
        preencherContrato(origemSistema, planoDiferente);
        adicionarPeriodoAcesso();
        if(!simulacao) {
            getZWFacade().incluirContrato(getContratoRecorrenciaNovo().getContrato());
            getZWFacade().getContratoRecorrencia().incluir(getContratoRecorrenciaNovo());
            incluirReajusteContrato();
            getZWFacade().atualizarSintetico(getZWFacade().getCliente().consultarPorCodigoPessoa(
                    getContratoRecorrenciaNovo().getContrato().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), Calendario.hoje(),
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
            getZWFacade().gravarPontuacaoCliente(getContratoRecorrenciaNovo().getContrato());
            try {
                LogVO logVO = new LogVO();
                String logAdicional = null;
                if (getContratoRecorrenciaNovo().getIndiceFinanceiroVO() != null){
                    logAdicional = getContratoRecorrenciaNovo().getIndiceFinanceiroVO().getDescricaoLog();
                }
                getContratoRecorrenciaNovo().getContrato().montarLogDadosContrato(logVO, null, logAdicional);
                logVO.setOperacao("INCLUSÃO DE CONTRATO");
                registrarLogObjetoVO(logVO, getContratoRecorrenciaNovo().getContrato().getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CONTRATO", getContratoRecorrenciaNovo().getContrato().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO CONTRATO", getContratoRecorrenciaNovo().getContrato().getResponsavelContrato().getNome(),"");
                Uteis.logar(e, RenovacaoAutomaticaRecorrenciaService.class);
            }
        }

        return getContratoRecorrenciaNovo();
    }
    
    private void incluirReajusteContrato()throws Exception{
        if (UtilReflection.objetoMaiorQueZero(getContratoRecorrenciaNovo(), "getIndiceFinanceiroVO().getCodigo()")){
            ReajusteContratoVO reajusteContratoVO = new ReajusteContratoVO();
            reajusteContratoVO.setContratoVO(getContratoRecorrenciaNovo().getContrato());
            reajusteContratoVO.setUsuarioVO(getContratoRecorrenciaNovo().getUsuarioVO());
            reajusteContratoVO.setIndiceFinanceiroReajustePrecoVO(getContratoRecorrenciaNovo().getIndiceFinanceiroVO());
            reajusteContratoVO.setPercentualIndice(getContratoRecorrenciaNovo().getIndiceFinanceiroVO().getPercentualAcumulado());
            reajusteContratoVO.setValorMensalAnterior(getContratoRecorrenciaNovo().getIndiceFinanceiroVO().getValorMensalidadeOriginal());
            reajusteContratoVO.setValorMensalNovo(getContratoRecorrenciaNovo().getIndiceFinanceiroVO().getValorMensalidadeReajustada());
            getFacade().getReajusteContrato().incluir(reajusteContratoVO);
        }

    }

    /**
     * Seta valores no novo contrato recorrencia gerado na renovação
     * <AUTHOR>
     * 27/07/2011
     */
    private void preencherContratoRecorrencia(Integer codigoContratoRenovado, PlanoRecorrenciaVO planoRecorrencia, boolean planoDiferente) throws Exception {
        getContratoRecorrenciaNovo().getContrato().setContratoBaseadoRenovacao(codigoContratoRenovado);
        getContratoRecorrenciaNovo().setMesVencimentoAnuidade(planoRecorrencia.getMesAnuidade());
        getContratoRecorrenciaNovo().setDiaVencimentoAnuidade(planoRecorrencia.getDiaAnuidade());
        getContratoRecorrenciaNovo().setFidelidade(planoRecorrencia.getDuracaoPlano());
        getContratoRecorrenciaNovo().setRenovavelAutomaticamente(planoRecorrencia.getRenovavelAutomaticamente());
        getContratoRecorrenciaNovo().setDiasCancelamentoAutomatico(planoRecorrencia.getQtdDiasAposVencimentoCancelamentoAutomatico());
        getContratoRecorrenciaNovo().getContrato().setSituacao("AT");
        getContratoRecorrenciaNovo().getContrato().setSituacaoContrato("RN");

        PlanoEmpresaVO planoEmpresaVO = getContratoRecorrenciaNovo().getContrato().getPlano().obterPlanoEmpresa(getContratoRecorrenciaNovo().getContrato().getEmpresa().getCodigo());
        if(!getContratoRecorrenciaNovo().getContrato().getPlano().getRenovarAutomaticamenteUtilizandoValorBaseContrato()) {
            getContratoRecorrenciaNovo().setValorMensal(calcularValorMensal(planoRecorrencia, planoEmpresaVO));
        } else {
            getContratoRecorrenciaNovo().setValorMensal(getContratoRecorrenciaNovo().getValorMensalNegociado());
        }

        Calendar dataInicioContrato = Calendario.getInstance();
        dataInicioContrato.setTime(montarDataInicioNovoContrato());

        IndiceFinanceiroReajustePrecoVO indiceFinanceiroVO = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo(
                (new DecimalFormat("00")).format(dataInicioContrato.get(Calendar.MONTH) + 1), String.valueOf(dataInicioContrato.get(Calendar.YEAR)), true, TipoPlanoEnum.PLANO_RECORRENCIA,
                false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (indiceFinanceiroVO != null) {
            Double valorMensalidade = getContratoRecorrenciaNovo().getValorMensalNegociado();
            indiceFinanceiroVO.setValorMensalidadeOriginal(valorMensalidade);
            if (UteisValidacao.notEmptyNumber(indiceFinanceiroVO.getPercentualAcumulado())) {
                Double valorReajuste = Uteis.arredondarForcando2CasasDecimais((valorMensalidade * indiceFinanceiroVO.getPercentualAcumulado()) / 100);
                valorMensalidade = Uteis.arredondarForcando2CasasDecimais(valorMensalidade + valorReajuste);
                indiceFinanceiroVO.setValorMensalidadeReajustada(valorMensalidade);
            } else {
                indiceFinanceiroVO.setValorMensalidadeReajustada(valorMensalidade);
            }
            getContratoRecorrenciaNovo().setIndiceFinanceiroVO(indiceFinanceiroVO);
            getContratoRecorrenciaNovo().setValorMensal(valorMensalidade);
        }

        getContratoRecorrenciaNovo().setValorAnuidade(planoRecorrencia.getValorAnuidade());
        if (planoEmpresaVO != null && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorAnuidade())) {
            getContratoRecorrenciaNovo().setValorAnuidade(planoEmpresaVO.getValorAnuidade());
        }

        PlanoDuracaoVO planoduracao;
        PlanoCondicaoPagamentoVO planoCondicao;
        if (!planoDiferente) {
            planoduracao = getZWFacade().getPlanoDuracao().consultarPorPlanoRecorrencia(planoRecorrencia.getPlano(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getContratoRecorrenciaNovo().getContrato().getPlanoHorario().setCodigo(getContratoRecorrenciaNovo().getContrato().getContratoHorario().getCodigo());
            planoCondicao = getZWFacade().getPlanoCondicaoPagamento().consultarPorPlanoDuracaoRecorrencia(planoduracao.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        } else {
            planoduracao = getZWFacade().getPlanoDuracao().consultarPorChavePrimaria(getContratoRecorrenciaVelho().getContrato().getPlano().getDuracaoPlanoDiferenteRenovacao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getContratoRecorrenciaNovo().getContrato().setPlanoHorario(getZWFacade().getPlanoHorario().consultarPorPlanoHorario(
                    getContratoRecorrenciaNovo().getContrato().getPlano().getCodigo(), getContratoRecorrenciaVelho().getContrato().getPlano().getHorarioPlanoDiferenteRenovacao(), Uteis.NIVELMONTARDADOS_TODOS));
            planoCondicao = getZWFacade().getPlanoCondicaoPagamento().consultarPorCodigoCondicaoPagamento(
                    getContratoRecorrenciaVelho().getContrato().getPlano().getCondicaoPagPlanoDiferenteRenovacao(), planoduracao.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        }

        getContratoRecorrenciaNovo().getContrato().setPlanoDuracao(planoduracao);
        getContratoRecorrenciaNovo().getContrato().setPlanoCondicaoPagamento(planoCondicao);
        getContratoRecorrenciaNovo().setPessoa(getContratoRecorrenciaNovo().getContrato().getPessoa());
        getContratoRecorrenciaNovo().setUsuarioVO(getZWFacade().getUsuarioRecorrencia());
        getContratoRecorrenciaNovo().setAnuidadeNaParcela(planoRecorrencia.isAnuidadeNaParcela());
        getContratoRecorrenciaNovo().setParcelaAnuidade(planoRecorrencia.getParcelaAnuidade());

        getContratoRecorrenciaNovo().setCancelamentoProporcional(false);
        if (planoRecorrencia.isCancelamentoProporcional()) {
            if (!planoRecorrencia.isCancelamentoProporcionalSomenteRenovacao() || getContratoRecorrenciaNovo().getContrato().isContratoRenovacao()) {
                getContratoRecorrenciaNovo().setCancelamentoProporcional(true);
            }
        }

        getContratoRecorrenciaNovo().setQtdDiasCobrarProximaParcela(planoRecorrencia.getQtdDiasCobrarProximaParcela());
        getContratoRecorrenciaNovo().setQtdDiasCobrarAnuidadeTotal(planoRecorrencia.getQtdDiasCobrarAnuidadeTotal());
        getContratoRecorrenciaNovo().setGerarParcelasValorDifente(false);
        getContratoRecorrenciaNovo().getContrato().getContratoRecorrenciaVO().setGerarParcelasValorDifente(false);
        getContratoRecorrenciaNovo().setParcelarAnuidade(planoRecorrencia.isParcelarAnuidade());
    }

    private Double calcularValorMensal(PlanoRecorrenciaVO planoRecorrencia, PlanoEmpresaVO planoEmpresaVO) throws Exception {
        Double valorMensalidade = planoRecorrencia.getValorMensal();
        if (planoEmpresaVO != null  && !UteisValidacao.emptyNumber(planoEmpresaVO.getValorMensal())){
            valorMensalidade = planoEmpresaVO.getValorMensal();
        }

        return valorMensalidade;
    }

    /**
     * Seta atributos do contrato que será gerado na renovação
     * <AUTHOR>
     * 27/07/2011
     */
    private void preencherContrato(Integer origemSistema, boolean planoDiferente) throws Exception {
        getContratoRecorrenciaNovo().getContrato().setResponsavelContrato(getZWFacade().getUsuarioRecorrencia());
        getContratoRecorrenciaNovo().getContrato().setUsuarioVO(getContratoRecorrenciaNovo().getContrato().getResponsavelContrato());
        getContratoRecorrenciaNovo().getContrato().setSomaProduto(0.0);
        getContratoRecorrenciaNovo().getContrato().setImportacao(false);
        getContratoRecorrenciaNovo().getContrato().setRenovarContrato(true);
        if(!getContratoRecorrenciaNovo().getContrato().getPlano().getPermitePagarComBoleto()){ // caso desabilitado ,vai desabilidar no novo contrato. Caso contrário, vai propagar o que vem do contrato anterior
            getContratoRecorrenciaNovo().getContrato().setPagarComBoleto(false);
        }
        preencherDatas();
        preencherContratoModalidadePlanoDiferente(planoDiferente);
        preencherModalidadeVezesNaSemana();
        double valor = getContratoRecorrenciaNovo().getValorMensal() * getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses();
        if (getContratoRecorrenciaNovo().getContrato().getPlano().isRenovarAutomaticamenteComDesconto()) {
            valor = verificarAbatendoDesconto(valor);
        } else {
            getContratoRecorrenciaNovo().getContrato().setDesconto(null);
            getContratoRecorrenciaNovo().getContrato().setValorDescontoEspecifico(0.0);
            getContratoRecorrenciaNovo().getContrato().setValorDescontoPorcentagem(0.0);
        }
        if (OrigemSistemaEnum.getOrigemSistema(origemSistema).equals(OrigemSistemaEnum.AUTO_ATENDIMENTO)){
            if (!getContratoRecorrenciaNovo().getContrato().getPlano().isRenovarComDescontoTotem()){
                getContratoRecorrenciaNovo().getContrato().setDesconto(null);
                getContratoRecorrenciaNovo().getContrato().setValorDescontoEspecifico(0.0);
                getContratoRecorrenciaNovo().getContrato().setValorDescontoPorcentagem(0.0);
                getContratoRecorrenciaNovo().getContrato().setConvenioDesconto(new ConvenioDescontoVO());
            }
        }
        getContratoRecorrenciaNovo().getContrato().setNumeroCupomDesconto(null);
        valor = calcularDescontoHorario(valor, getContratoRecorrenciaNovo().getContrato());

        if (getContratoRecorrenciaNovo().getContrato().isDeveGerarParcelasComValorDiferente()) {
            Integer nrMeses = getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses() - getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoRecorrencia().getQuantidadeParcelasValorDiferente();
            valor = ((valor / getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses()) * nrMeses) + getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoRecorrencia().getValorTotalParcelasValorDirefente();
        }

        getContratoRecorrenciaNovo().getContrato().setValorBaseCalculo(valor);
        getZWFacade().inicializarMovProdutoContrato(getContratoRecorrenciaNovo().getContrato());
        adicionarProdutosObrigatorios();
        getContratoRecorrenciaNovo().getContrato().setValorFinal(Uteis.arredondarForcando2CasasDecimais(getContratoRecorrenciaNovo().getContrato().getValorBaseCalculo()+ getContratoRecorrenciaNovo().getContrato().getSomaProduto()));
        getContratoRecorrenciaNovo().setGerarParcelasValorDifente(false);
        getContratoRecorrenciaNovo().getContrato().getContratoRecorrenciaVO().setGerarParcelasValorDifente(false);

        getContratoRecorrenciaNovo().getContrato().setOrigemSistema(OrigemSistemaEnum.getOrigemSistema(origemSistema));

        setarHistorico();
    }

    private void preencherContratoModalidadePlanoDiferente(boolean planoDiferente) {
        if (planoDiferente) {
            getContratoRecorrenciaNovo().getContrato().setContratoModalidadeVOs(new ArrayList<>());
            getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoModalidadeVOs().forEach(pM -> {
                ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
                contratoModalidadeVO.setModalidade(pM.getModalidade());
                contratoModalidadeVO.setNrVezesSemana(pM.getModalidade().getNrVezes());
                getContratoRecorrenciaNovo().getContrato().getContratoModalidadeVOs().add(contratoModalidadeVO);
            });
        }
    }

    private double verificarAbatendoDesconto(double valor) {
        if (getContratoRecorrenciaNovo().getContrato().getConvenioDesconto() != null || getContratoRecorrenciaNovo().getContrato().getConvenioDesconto().getCodigo() != 0) {
            if (getContratoRecorrenciaNovo().getContrato().getConvenioDesconto().getDataFinalVigencia() != null && Calendario.maiorOuIgual(getContratoRecorrenciaNovo().getContrato().getConvenioDesconto().getDataFinalVigencia(), Calendario.hoje())) {
                Iterator l = getContratoRecorrenciaNovo().getContrato().getConvenioDesconto().getConvenioDescontoConfiguracaoVOs().iterator();
                while (l.hasNext()) {
                    ConvenioDescontoConfiguracaoVO convenioDescontoConfiguracaoVO = (ConvenioDescontoConfiguracaoVO) l.next();
                    if (convenioDescontoConfiguracaoVO.getConvenioDescontoConfiguracaoEscolhida()) {
                        if (convenioDescontoConfiguracaoVO.getTipoDesconto().equals("VE")) {
                            if (getContratoRecorrenciaNovo().getContrato().getPlanoDuracao() != null && getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses() != 0) {
                                getContratoRecorrenciaNovo().getContrato().setValorConvenioDesconto(convenioDescontoConfiguracaoVO.getValorDesconto());
                                valor = (valor - convenioDescontoConfiguracaoVO.getValorDesconto());
                            }
                        } else {
                            if (getContratoRecorrenciaNovo().getContrato().getPlanoDuracao() != null && getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses() != 0) {
                                getContratoRecorrenciaNovo().getContrato().setValorConvenioDesconto((getContratoRecorrenciaNovo().getContrato().getValorFinal() * (convenioDescontoConfiguracaoVO.getPorcentagemDesconto() / 100)));
                                valor = ((valor - (valor * (convenioDescontoConfiguracaoVO.getPorcentagemDesconto() / 100))));
                            }
                        }
                    }
                }
            }
        }

        try {
            getContratoRecorrenciaNovo().getContrato().setMensagemSemGrupoDescontoAplicavel("");
            getContratoRecorrenciaNovo().getContrato().setGrupo(null);
            GrupoVO grupoVO = getFacade().getGrupo().consultarMelhorGrupoPorVinculo(getContratoRecorrenciaNovo().getContrato().getPessoa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS, getContratoRecorrenciaNovo().getContrato().getValorFinal());
            if (grupoVO != null && grupoVO.getCodigo() > 0) {
                getContratoRecorrenciaNovo().getContrato().setGrupo(grupoVO);
                if (grupoVO.getTipoDesconto().equals("VA") && grupoVO.getValorDescontoGrupo() > 0.0) {
                    if (getContratoRecorrenciaNovo().getContrato().getPlanoDuracao() != null && !UteisValidacao.emptyNumber(getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses())) {
                        double descontoPorParcela = grupoVO.getValorDescontoGrupo() / getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses();
                        valor = valor - descontoPorParcela;
                    } else {
                        valor = valor - grupoVO.getValorDescontoGrupo();
                    }
                }
                if (grupoVO.getTipoDesconto().equals("PE") && grupoVO.getPercentualDescontoGrupo() > 0.0) {
                    // Calcula o valor do desconto com base no valor final do contrato
                    Double valorDesconto = getContratoRecorrenciaNovo().getContrato().getValorFinal() * (grupoVO.getPercentualDescontoGrupo() / 100);

                    // Atualiza o valor das parcelas com base no desconto concedido
                    if (getContratoRecorrenciaNovo().getContrato().getPlanoDuracao() != null && !UteisValidacao.emptyNumber(getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses())) {
                        valorDesconto = valor * (grupoVO.getPercentualDescontoGrupo() / 100);
                        valor = valor - valorDesconto;
                        getContratoRecorrenciaNovo().getContrato().setValorTemporarioDescontoPorcentagem(valorDesconto * getContratoRecorrenciaNovo().getContrato().getPlanoDuracao().getNumeroMeses());
                    } else {
                        valor = valor - valorDesconto;
                        getContratoRecorrenciaNovo().getContrato().setValorTemporarioDescontoPorcentagem(valorDesconto);
                    }
                }
            } else {
                List<ClienteGrupoVO> gruposCliente = (List<ClienteGrupoVO>) getFacade().getClienteGrupo()//
                        .consultarClienteGruposPorPessoa(getContratoRecorrenciaNovo().getContrato().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (gruposCliente != null && !gruposCliente.isEmpty()) {
                    // Se o cliente possui grupos configurados mas nenhum é eligível para aplicação do desconto
                    // Exibe uma mensagem para o usuário informando o cenário
                    getContratoRecorrenciaNovo().getContrato().setMensagemSemGrupoDescontoAplicavel("Aluno não atingiu o mínimo de participantes do grupo ou não atendeu as regras do grupo de desconto.");
                }
            }
        } catch (Exception ignored){}

        if(getContratoRecorrenciaNovo().getContrato().getDesconto().getCodigo() != 0 && getContratoRecorrenciaNovo().getContrato().getDesconto().getTipoProduto().equals("DE")){
            if(getContratoRecorrenciaNovo().getContrato().getValorDescontoEspecifico() != 0.0){
                valor -= getContratoRecorrenciaNovo().getContrato().getValorDescontoEspecifico();
            }else if(getContratoRecorrenciaNovo().getContrato().getValorDescontoPorcentagem() != 0.0){
                valor = valor - Uteis.arredondarForcando2CasasDecimais((valor * getContratoRecorrenciaNovo().getContrato().getValorDescontoPorcentagem()) / 100);
            }
        }

        return valor;
    }

    private void adicionarProdutosObrigatorios() throws Exception {
        List<PlanoProdutoSugeridoVO> produtoSugeridos = getZWFacade().getPlanoProdutoSugerido().consultarPlanoProdutoSugeridos(
                getContratoRecorrenciaNovo().getContrato().getPlano().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
         getContratoRecorrenciaNovo().getContrato().setContratoPlanoProdutoSugeridoVOs(new ArrayList());
          getContratoRecorrenciaNovo().getContrato().setSomaProduto(0D);
         ContratoPlanoProdutoSugeridoVO conPPSugerigo;
         boolean anuidadeAdicionada = false;
        for (PlanoProdutoSugeridoVO planoProdutoSugerido : produtoSugeridos) {
            if ((!planoProdutoSugerido.getProduto().getTipoProduto().equals("MA")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("RE")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("TA")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("TD")
                    && (planoProdutoSugerido.getProduto().getTipoProduto().equals("RN") || (planoProdutoSugerido.isObrigatorio() && getContratoRecorrenciaNovo().getContrato().getPlano().getRenovarProdutoObrigatorio())))
                    || (planoProdutoSugerido.getProduto().getTipoProduto().equals("TA") && planoProdutoSugerido.isObrigatorio() && (getContratoRecorrenciaNovo().getContrato().getPlano().isRenovarAnuidadeAutomaticamente() || getContratoRecorrenciaNovo().getContrato().getPlano().getPlanoRecorrencia().isAnuidadeNaParcela()) && !anuidadeAdicionada)) {
                conPPSugerigo = new ContratoPlanoProdutoSugeridoVO();
                conPPSugerigo.setPlanoProdutoSugerido(planoProdutoSugerido);
                getContratoRecorrenciaNovo().getContrato().getContratoPlanoProdutoSugeridoVOs().add(conPPSugerigo);
                MovProdutoVO movP = new MovProdutoVO();
                movP.setProduto(planoProdutoSugerido.getProduto());
                movP.setApresentarMovProduto(false);
                movP.setContrato(getContratoRecorrenciaNovo().getContrato());
                movP.setDescricao(planoProdutoSugerido.getProduto().getDescricao());
                movP.setSituacao("EA");
                movP.setEmpresa(getContratoRecorrenciaNovo().getContrato().getEmpresa());
                movP.setPessoa(getContratoRecorrenciaNovo().getContrato().getPessoa());
                movP.setMesReferencia(Uteis.getMesReferenciaData(getContratoRecorrenciaNovo().getContrato().getVigenciaDe()));
                movP.setQuantidade(1);
                movP.setAnoReferencia(Uteis.getAnoData(getContratoRecorrenciaNovo().getContrato().getVigenciaDe()));
                movP.setDataLancamento(getContratoRecorrenciaNovo().getContrato().getDataLancamento());
                if (!planoProdutoSugerido.getProduto().getTipoVigencia().equals("")) {
                        movP.setDataInicioVigencia(planoProdutoSugerido.getDataVenda());
                        movP.setDataFinalVigencia(planoProdutoSugerido.getDataValidade());
                    } else {
                        movP.setDataInicioVigencia(null);
                        movP.setDataFinalVigencia(null);
                    }
                movP.setResponsavelLancamento(getContratoRecorrenciaNovo().getContrato().getResponsavelContrato());
                movP.setPrecoUnitario(planoProdutoSugerido.getValorProduto());
                movP.setTotalFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(planoProdutoSugerido.getValorProduto()));
                getContratoRecorrenciaNovo().getContrato().setSomaProduto(
                        Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movP.getTotalFinal()
                        + getContratoRecorrenciaNovo().getContrato().getSomaProduto()));
                if (planoProdutoSugerido.getProduto().getTipoProduto().equals("TA")) {
                    getContratoRecorrenciaNovo().getContrato().setSomaAnuidade(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movP.getTotalFinal() + getContratoRecorrenciaNovo().getContrato().getSomaAnuidade()));
                }
                getContratoRecorrenciaNovo().getContrato().adicionarObjMovProdutoVOs(movP,true);

                if(planoProdutoSugerido.getProduto().getTipoProduto().equals("TA")) {
                    anuidadeAdicionada = true;
                }
            }
        }
    }

    private void adicionarPeriodoAcesso() {
        PeriodoAcessoClienteVO periodoAcessoClienteVO = new PeriodoAcessoClienteVO();
        periodoAcessoClienteVO.setPessoa(getContratoRecorrenciaNovo().getContrato().getPessoa().getCodigo());
        periodoAcessoClienteVO.setDataInicioAcesso(getContratoRecorrenciaNovo().getContrato().getVigenciaDe());
        periodoAcessoClienteVO.setDataFinalAcesso(getContratoRecorrenciaNovo().getContrato().getVigenciaAteAjustada());
        periodoAcessoClienteVO.setTipoAcesso("CA");
        getContratoRecorrenciaNovo().getContrato().getPeriodoAcessoClienteVOs().add(periodoAcessoClienteVO);
    }

    private Date montarDataInicioNovoContrato(){
        return Uteis.obterDataFutura(getContratoRecorrenciaNovo().getContrato().getVigenciaAteAjustada(), 2);
    }

    /**
     * Responsável por preencher as datas do novo contrato gerado na renovação
     * <AUTHOR>
     * 27/07/2011
     */
    private void preencherDatas() throws Exception {
        getContratoRecorrenciaNovo().getContrato().setDataLancamento(Calendario.hoje());
        getContratoRecorrenciaNovo().getContrato().setDataAlteracaoManual(null);
        getContratoRecorrenciaNovo().getContrato().setVigenciaDe(montarDataInicioNovoContrato());
        getContratoRecorrenciaNovo().getContrato().setVigenciaAte(
                Uteis.obterDataAnterior(Uteis.somarCampoData(
                getContratoRecorrenciaNovo().getContrato().getVigenciaDe(),
                Calendar.MONTH,
                getContratoRecorrenciaNovo().getFidelidade()), 1));
        Calendar cDataPrimeiraParcela = Calendario.getInstance(getContratoRecorrenciaNovo().getContrato().getVigenciaDe());
        if((getContratoRecorrenciaNovo().getContrato().getVigenciaDe().getMonth() == 1 && getContratoRecorrenciaNovo().getDiaVencimentoCartao() > 28) || getContratoRecorrenciaNovo().getDiaVencimentoCartao() == 31){ //problemas com o mês de fevereiro e meses que não tem o dia 31.
            cDataPrimeiraParcela = Calendario.getInstance(Uteis.obterUltimoDiaMes(getContratoRecorrenciaNovo().getContrato().getVigenciaDe()));
        } else {
            cDataPrimeiraParcela.set(Calendar.DAY_OF_MONTH, getContratoRecorrenciaNovo().getDiaVencimentoCartao());
        }

        if(getContratoRecorrenciaNovo().getContrato().getEmpresa().isPermiteContratoPosPagoRenovacaoAuto()){
            //Verificar se existe uma parcela no mesmo mês para este aluno, caso positivo, empurrar a primeira parcela do contrato para um mês após
            boolean existeParcelaNoMesmoMes = zwFacade.existe(String.format("SELECT\n" +
                    "  mp codigo\n" +
                    "FROM movparcela mp\n" +
                    "  INNER JOIN movprodutoparcela mpp\n" +
                    "    ON mpp.movparcela = mp.codigo\n" +
                    "  INNER JOIN movproduto mprod\n" +
                    "    ON mpp.movproduto = mprod.codigo\n" +
                    "  INNER JOIN produto prod\n" +
                    "    ON mprod.produto = prod.codigo\n" +
                    "WHERE mp.pessoa = %s\n" +
                    "      AND date_part('month', mp.datavencimento) = %s\n" +
                    "      AND date_part('year', mp.datavencimento) = %s\n" +
                    "      AND mp.regimerecorrencia IS TRUE\n" +
                    "      AND prod.tipoproduto = 'PM' AND mp.situacao not in ('CA', 'RG') \n"
                    + "   AND mp.contrato  = %s",
                    getContratoRecorrenciaNovo().getContrato().getPessoa().getCodigo(),
                    Uteis.getMesData(getContratoRecorrenciaNovo().getContrato().getVigenciaDe()),
                    Uteis.getAnoData(getContratoRecorrenciaNovo().getContrato().getVigenciaDe()),
                    getContratoRecorrenciaNovo().getContrato().getCodigo())
                    , con);
            if (existeParcelaNoMesmoMes) {
                cDataPrimeiraParcela.add(Calendar.MONTH, 1);
                if((cDataPrimeiraParcela.getTime().getMonth() == 1 && getContratoRecorrenciaNovo().getDiaVencimentoCartao() > 28) || getContratoRecorrenciaNovo().getDiaVencimentoCartao() == 31){ //problemas com o mês de fevereiro e meses que não tem o dia 31.
                    cDataPrimeiraParcela = Calendario.getInstance(Uteis.obterUltimoDiaMes(cDataPrimeiraParcela.getTime()));
                } else {
                    cDataPrimeiraParcela.set(Calendar.DAY_OF_MONTH, getContratoRecorrenciaNovo().getDiaVencimentoCartao());
                }
            }
        }

        getContratoRecorrenciaNovo().getContrato().setVigenciaAteAjustada(getContratoRecorrenciaNovo().getContrato().getVigenciaAte());
        if(zwFacade.isMensalEexisteOperacaoContratoAcrescimoDias(getContratoRecorrenciaNovo(), null)){
            Calendar cDataVencimento = Calendario.getInstance(getContratoRecorrenciaNovo().getContrato().getVigenciaDe());
            getContratoRecorrenciaNovo().setDiaVencimentoCartao(cDataVencimento.get(Calendar.DAY_OF_MONTH));
            getContratoRecorrenciaNovo().getContrato().setDataPrimeiraParcela(getContratoRecorrenciaNovo().getContrato().getVigenciaDe());
        }else{
            getContratoRecorrenciaNovo().getContrato().setDataPrimeiraParcela(cDataPrimeiraParcela.getTime());
        }
        getContratoRecorrenciaNovo().getContrato().setDataPrevistaRenovar(getContratoRecorrenciaNovo().getContrato().getVigenciaAte());
        getContratoRecorrenciaNovo().getContrato().setDataPrevistaRematricula(getContratoRecorrenciaNovo().getContrato().getVigenciaAte());

    }
    
    /**
     * Responsável por preencher as modalidades e vezes na semana para a inclusão
     * <AUTHOR>
     * 27/07/2011
     */
    private void preencherModalidadeVezesNaSemana() {
        Double valorMensal = getContratoRecorrenciaNovo().getValorMensal() / getContratoRecorrenciaNovo().getContrato().getContratoModalidadeVOs().size();
        for (ContratoModalidadeVO modalidade : getContratoRecorrenciaNovo().getContrato().getContratoModalidadeVOs()) {
            modalidade.getPlanoVezesSemanaVO().setNrVezes(modalidade.getNrVezesSemana());
            modalidade.getModalidade().setModalidadeEscolhida(true);
            modalidade.setValorModalidade(valorMensal);
            modalidade.setValorFinalModalidade(valorMensal);
        }
    }

    /**
     * Monta o histórico do contrato que será gerado na renovação
     * <AUTHOR>
     * 27/07/2011
     */
    @SuppressWarnings("unchecked")
    private void setarHistorico() throws Exception {
        HistoricoContratoVO historico = new HistoricoContratoVO();
        historico.setDescricao("Renovado");
        historico.setTipoHistorico("RN");
        getContratoRecorrenciaNovo().getContrato().setContratoBaseadoRematricula(0);
        getContratoRecorrenciaNovo().getContrato().setHistoricoContratoVOs(new ArrayList());
        historico.setDataRegistro(Calendario.hoje());
        historico.setDataInicioSituacao(getContratoRecorrenciaNovo().getContrato().getVigenciaDe());
        historico.setDataFinalSituacao(getContratoRecorrenciaNovo().getContrato().getVigenciaAteAjustada());
        historico.setDataInicioTemporal(null);
        historico.setResponsavelRegistro(getZWFacade().getUsuarioRecorrencia());
        getContratoRecorrenciaNovo().getContrato().getHistoricoContratoVOs().add(historico);

        getContratoRecorrenciaNovo().getContrato().atualizarHistoricosContratoAnteriorRenovado(getZWFacade().getHistoricoContrato());
    }

    //-------------------- GETTERS AND SETTERS ------------------------------------------//
    /**
     * @param contratoRecorrenciaNovo the contratoRecorrenciaNovo to set
     */
    public void setContratoRecorrenciaNovo(ContratoRecorrenciaVO contratoRecorrenciaNovo) {
        this.contratoRecorrenciaNovo = contratoRecorrenciaNovo;
    }

    /**
     * @return the contratoRecorrenciaNovo
     */
    public ContratoRecorrenciaVO getContratoRecorrenciaNovo() {
        return contratoRecorrenciaNovo;
    }

    public ContratoRecorrenciaVO getContratoRecorrenciaVelho() {
        return contratoRecorrenciaVelho;
    }

    public void setContratoRecorrenciaVelho(ContratoRecorrenciaVO contratoRecorrenciaVelho) {
        this.contratoRecorrenciaVelho = contratoRecorrenciaVelho;
    }

    public ZillyonWebFacade getZWFacade() throws Exception {
        if (zwFacade == null) {
            zwFacade = new ZillyonWebFacade(this.con);
        }
        return zwFacade;
    }

    public static void main(String... args) throws Exception {
        String chave = args.length > 0 ? args[0] : "selfit";
        Connection c = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(chave, c);
//        RenovacaoAutomaticaRecorrenciaService rs = new RenovacaoAutomaticaRecorrenciaService(c);
//
//        rs.renovarAutomatico(getFacade().getContratoRecorrencia().consultarPorContrato(63491, Uteis.NIVELMONTARDADOS_TODOS));
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        if (args.length >= 2) {
            Calendario.dia = Calendario.getInstance(sdf.parse(args[1])).getTime();
        }
//        Date hoje = Calendario.getInstance(sdf.parse("17/05/2017")).getTime();
//        Calendario.dia = hoje;
        RecorrenciaService service = new RecorrenciaService(c);
        service.processarRenovacoesAutomaticas(Calendario.hoje());
        service.processarAnuidadeContratoRecorrencia(Calendario.hoje());
    }

    public void ajustarVencimentoParcelasContratoPrePago() throws Exception{
        
        if(Calendario.menor(Calendario.hoje(), getContratoRecorrenciaNovo().getContrato().getDataPrimeiraParcela()) && 
                (getContratoRecorrenciaNovo().getContrato().getContratoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago())){
            Date ultimoVencimento  = zwFacade.getMovParcela().consultarUltimoVencimentoContrato(getContratoRecorrenciaNovo().getContrato().getContratoBaseadoRenovacao());
            Date primeiraParcela = Uteis.somarCampoData(getContratoRecorrenciaNovo().getContrato().getDataPrimeiraParcela(), Calendar.MONTH, -1);
            if(ultimoVencimento.getMonth() < primeiraParcela.getMonth()){       
                List<MovParcelaVO> parcelas = zwFacade.getMovParcela().consultarPorContrato(getContratoRecorrenciaNovo().getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                boolean primeira = true;
                for(MovParcelaVO parcela: parcelas){
                    if(parcela.getDescricao().startsWith("PARCELA")){
                        if(primeira){
                            if(Calendario.menor(primeiraParcela, Calendario.hoje())){
                                parcela.setDataVencimento(Calendario.hoje());
//                                //é preciso verificar se a primeira parcela não está no mesmo mes de hoje,
//                                //se ela não estiver, é necessário incrementar um mes, para que a proxima parcela venha em mês diferente
                                if(Uteis.getMesData(primeiraParcela) < Uteis.getMesData(Calendario.hoje())){
                                    primeiraParcela = Uteis.somarCampoData(primeiraParcela, Calendar.MONTH, 1);
                                }
                            } else {
                                parcela.setDataVencimento(primeiraParcela);
                            }
                            parcela.setDataCobranca(parcela.getDataVencimento());
                            primeira = false;
                        } else {
                            primeiraParcela = Uteis.somarCampoData(primeiraParcela, Calendar.MONTH, 1);
                            parcela.setDataVencimento(primeiraParcela);
                            parcela.setDataCobranca(parcela.getDataVencimento());
                        }
                        zwFacade.getMovParcela().alterarSemCommit(parcela);
                    }
                }
            }
        }
    }

    private double calcularDescontoHorario(double valor, ContratoVO contrato) throws Exception {
           PlanoHorarioVO planoHorarioVO = getZWFacade().getPlanoHorario().consultarPorPlanoHorario(contrato.getPlano().getCodigo(), contrato.getContratoHorario().getHorario().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
           if(planoHorarioVO == null){
               return valor;
           }
            if (planoHorarioVO.getTipoOperacao() == null) {
                planoHorarioVO.setTipoOperacao("");
            }

            if (planoHorarioVO.getTipoOperacao().equals("RE")) {
                if (planoHorarioVO.getTipoValor().equals("PD")) {
                    valor = valor - (valor * (planoHorarioVO.getPercentualDesconto() / 100));
                } else {
                     valor =  valor - planoHorarioVO.getValorEspecifico();
                }

            } else if (planoHorarioVO.getTipoOperacao().equals("AC")) {
                if (planoHorarioVO.getTipoValor().equals("PD")) {
                     valor = valor + (valor * (planoHorarioVO.getPercentualDesconto() / 100));
                } else {
                    valor = valor + planoHorarioVO.getValorEspecifico();
                }
            }
            return valor;
    }
}

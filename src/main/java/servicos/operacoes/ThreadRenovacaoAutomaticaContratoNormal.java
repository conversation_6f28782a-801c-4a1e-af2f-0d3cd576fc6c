/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import java.sql.Connection;
import java.util.Date;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;

/**
 *
 * <AUTHOR>
 */
public class ThreadRenovacaoAutomaticaContratoNormal extends Thread {
    
    private ContratoVO contrato;
    private Connection con;
    private StringBuffer sb = new StringBuffer();
    private Integer carenciaRenovacao;
    
    public ThreadRenovacaoAutomaticaContratoNormal(ContratoVO contrato, Connection con) throws Exception {
        this.contrato = contrato;
        this.con = con;
        this.setName("ThreadRenovacaoAuto_" + contrato.getCodigo());
    }

    private void logar(String mensagem) {
        Uteis.logar(sb, this.getName() + " - " + mensagem);
    }

    public boolean verificarDeveRenovar(Integer nrDiasRenovacaoAutomaticaAntecipada) throws ValidacaoException {
        
        Date dataVencimento = contrato.getVigenciaAteAjustada();
        //data de vencimento do contrato deve menor ou igual a hoje
        //e a diferença entre a data de vencimento e hoje não pode ser maior do que a carencia de renovacao
        Date dataLimite = UteisValidacao.emptyNumber(nrDiasRenovacaoAutomaticaAntecipada) ? Calendario.hoje() : Uteis.somarDias(Calendario.hoje(), nrDiasRenovacaoAutomaticaAntecipada);
        if ((Calendario.menorOuIgual(dataVencimento, dataLimite)
                && (Uteis.nrDiasEntreDatas(dataVencimento, Calendario.hoje()) <= getCarenciaRenovacao() 
                    || (Calendario.maiorOuIgual(dataVencimento, Calendario.hoje()) 
                        && !UteisValidacao.emptyNumber(nrDiasRenovacaoAutomaticaAntecipada))))
                && (contrato.getSituacao().equals("AT")
                || contrato.getSituacao().equals("IN"))) {
            
            //verificar se já foi renovado
            if (contrato.getDataRenovarRealizada() != null) {
                throw new ValidacaoException("Contrato já foi renovado.");
            }
            return true;
        } else {
            return false;
        }

    }

    @Override
    public void run() {
        RenovacaoAutomaticaService renovacaoAutomatica = new RenovacaoAutomaticaService(con);
        try {
            long inicio = System.currentTimeMillis();
            logar("Iniciando Renovação Automática do contrato " + contrato.getCodigo());
            contrato.setOrigemSistema(OrigemSistemaEnum.ZW);
            renovacaoAutomatica.renovarAutomatico(contrato, false);
            logar("Renovado com sucesso!");
            long fim = System.currentTimeMillis();
            System.out.println("Tempo gasto na Renovação Automática do contrato " + contrato.getCodigo() + " em milisegundos ->" + (fim - inicio));
        } catch (Exception ex) {
            logar("Erro ao renovar automaticamente o contrato " + contrato.getCodigo() + ": "
                    + ex.getMessage());
            UteisValidacao.enfileirarEmail(new MsgTO(contrato.getEmpresa().getNome(), "Renovação Automática", sb));
        }
    }

    public Integer getCarenciaRenovacao() {
        if(carenciaRenovacao == null){
            carenciaRenovacao = 0;
        }
        return carenciaRenovacao;
    }

    public void setCarenciaRenovacao(Integer carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }
    
}

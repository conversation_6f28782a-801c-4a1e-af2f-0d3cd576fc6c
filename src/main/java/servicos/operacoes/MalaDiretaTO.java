package servicos.operacoes;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import negocio.comuns.basico.AmostraClienteTO;
import negocio.comuns.crm.MalaDiretaVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@XStreamAlias("MalaDireta")
public class MalaDiretaTO {
    private List<AmostraClienteTO> clientes =  new ArrayList<AmostraClienteTO>();
    protected Integer codigo;
    protected Date dataEnvio;
    protected Date dataCriacao;
    protected String mensagem;
    protected String titulo;
    private Integer empresa;
    private RemetenteTO remetente;

    public MalaDiretaTO(MalaDiretaVO malaDiretaVO) {
        this.codigo = malaDiretaVO.getCodigo();
        this.dataEnvio = malaDiretaVO.getDataEnvio();
        this.dataCriacao = malaDiretaVO.getDataCriacao();
        this.mensagem = malaDiretaVO.getMensagem();
        this.titulo = malaDiretaVO.getTitulo();
        this.empresa = malaDiretaVO.getEmpresa().getCodigo();
        this.remetente = new RemetenteTO(malaDiretaVO.getRemetente());
    }

    public List<AmostraClienteTO> getClientes() {
        return clientes;
    }

    public void setClientes(List<AmostraClienteTO> clientes) {
        this.clientes = clientes;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public RemetenteTO getRemetente() {
        return remetente;
    }

    public void setRemetente(RemetenteTO remetente) {
        this.remetente = remetente;
    }
}

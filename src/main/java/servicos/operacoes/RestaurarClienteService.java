/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;

/**
 * Esta classe tem o objetivo de recuperar contratos que foram estornados acidentalmente.
 * Ele precisa de um banco de dados original com os códigos dos contratos que foram excluídos.
 * Todos os dados relacionados devem ser previstos aqui.
 *
 * <AUTHOR>
 */
public class RestaurarClienteService {

    private Connection origem;
    private Connection destino;

    public RestaurarClienteService(final Connection origem, final Connection destino) {
        this.origem = origem;
        this.destino = destino;
    }

    public static void main(String... argss) throws Exception {
        Conexao cOrigem = new Conexao("*****************************************", "postgres", "pactodb");
        Conexao cDestino = new Conexao("**************************************************", "postgres", "pactodb");
        Connection origem = cOrigem.getConexao();
        Connection destino = cDestino.getConexao();
        int[] codsCliente = {74092};
        new RestaurarClienteService(origem, destino).executar(codsCliente);
    }

    private String splitColumnsInsert(int n) {
        String result = "(";
        for (int i = 1; i <= n; i++) {
            result += "?" + (i + 1 <= n ? "," : "");
        }
        return result += ")";
    }

    private String splitColumnsNames(ResultSetMetaData rsmd) throws SQLException {
        String result = "(";
        for (int i = 1; i <= rsmd.getColumnCount(); i++) {
            result += rsmd.getColumnName(i) + (i + 1 <= rsmd.getColumnCount() ? "," : "");
        }
        return result += ")";
    }

    private void restauraTabela(final String nomeTabela, final String join,
                                final int codigoContrato,
                                final String nomeColunaCodigo)
            throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta(
                String.format("select t.* from %s t %s where %s = %s", new Object[]{
                        nomeTabela,
                        join,
                        nomeColunaCodigo,
                        codigoContrato
                }), origem);

        String columns = splitColumnsNames(rs.getMetaData());
        String params = splitColumnsInsert(rs.getMetaData().getColumnCount());
        int n = rs.getMetaData().getColumnCount();
        try {
            while (rs.next()) {
                PreparedStatement ps = destino.prepareStatement("insert into " + nomeTabela + " " + columns + " values " + params);
                int i = 1;
                for (int j = 1; j <= n; j++) {
                    ps.setObject(i++, rs.getObject(j));
                }
                ps.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void executar(final int[] codsCliente) throws Exception {

        for (int ind = 0; ind < codsCliente.length; ind++) {
            int codCliente = codsCliente[ind];
            try {
                destino.setAutoCommit(false);
                //
                restauraTabela("pessoa", "inner join cliente cli on cli.pessoa = t.codigo", codCliente, "cli.codigo");
                restauraTabela("cliente", "", codCliente, "codigo");
                restauraTabela("telefone", "inner join cliente cli on cli.pessoa = t.pessoa", codCliente, "cli.codigo");
                restauraTabela("email", "inner join cliente cli on cli.pessoa = t.pessoa", codCliente, "cli.codigo");
                restauraTabela("endereco", "inner join cliente cli on cli.pessoa = t.pessoa", codCliente, "cli.codigo");
                restauraTabela("vinculo", "", codCliente, "cliente");
                restauraTabela("historicovinculo", "", codCliente, "cliente");
                restauraTabela("situacaoclientesinteticodw", "", codCliente, "codigocliente");
                //
                destino.setAutoCommit(true);
                Uteis.logar(null, "Restaurado cliente " + codCliente);
            } catch (Exception e) {
                destino.rollback();
                throw e;
            } finally {
                destino.setAutoCommit(true);
            }
        }
    }
}

package servicos.operacoes;

import br.com.pactosolucoes.ce.comuns.ex.ValidacaoException;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class ThreadRenovacaoAutomatica extends Thread {

    private ContratoRecorrenciaVO contratoRecorrencia;
    private Connection con;
    private StringBuffer sb = new StringBuffer();
    private Integer carenciaRenovacao;

    public ThreadRenovacaoAutomatica(ContratoRecorrenciaVO contratoRecorrencia, Connection con) throws Exception {
        this.contratoRecorrencia = contratoRecorrencia;
        this.con = con;
        this.setName("ThreadRenovacaoAuto_" + contratoRecorrencia.getContrato().getCodigo());
    }

    private void logar(String mensagem) {
        Uteis.logar(sb, this.getName() + " - " + mensagem);
    }

    public boolean verificarDeveRenovar(Integer nrDiasRenovacaoAutomaticaAntecipada) throws ValidacaoException {

        Date dataVencimento = contratoRecorrencia.getContrato().getVigenciaAteAjustada();
        //data de vencimento do contrato deve menor ou igual a hoje
        //e a diferença entre a data de vencimento e hoje não pode ser maior do que a carencia de renovacao
        Date dataLimite = UteisValidacao.emptyNumber(nrDiasRenovacaoAutomaticaAntecipada) ? Calendario.hoje() : Uteis.somarDias(Calendario.hoje(), nrDiasRenovacaoAutomaticaAntecipada);
        if ((Calendario.menorOuIgual(dataVencimento, dataLimite)
                && (Uteis.nrDiasEntreDatas(dataVencimento, Calendario.hoje()) <= getCarenciaRenovacao()
                || (Calendario.maiorOuIgual(dataVencimento, Calendario.hoje())
                && !UteisValidacao.emptyNumber(nrDiasRenovacaoAutomaticaAntecipada))))
                && (contratoRecorrencia.getContrato().getSituacao().equals("AT")
                || contratoRecorrencia.getContrato().getSituacao().equals("IN"))) {
            // verificar se é renovavel automaticamente
            if (!contratoRecorrencia.getRenovavelAutomaticamente() && !contratoRecorrencia.getContrato().getPlano().getPlanoRecorrencia().getRenovavelAutomaticamente()) {
                throw new ValidacaoException("Contrato não pode ser renovado automaticamente.");
            }
            //verificar se já foi renovado
            if (contratoRecorrencia.getContrato().getDataRenovarRealizada() != null) {
                throw new ValidacaoException("Contrato já foi renovado.");
            }

            return true;
        } else {
            return false;
        }

    }

    @Override
    public void run() {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            RenovacaoAutomaticaRecorrenciaService renovacaoAutomatica = new RenovacaoAutomaticaRecorrenciaService(c);
            long inicio = System.currentTimeMillis();
            logar(String.format("Iniciando Renovação Automática do contrato %s da pessoa %s",
                    contratoRecorrencia.getContrato().getCodigo(),
                    contratoRecorrencia.getContrato().getPessoa().getCodigo()));
            renovacaoAutomatica.renovarAutomatico(contratoRecorrencia, false, OrigemSistemaEnum.ZW.getCodigo());
            long fim = System.currentTimeMillis();
            logar(String.format("Renovação Concluída do contrato %s da pessoa %s (%s ms)",
                    contratoRecorrencia.getContrato().getCodigo(),
                    contratoRecorrencia.getContrato().getPessoa().getCodigo(),
                    (fim - inicio)));

        } catch (Exception ex) {
            logar(String.format("Erro ao renovar automaticamente o contrato %s deviado ao erro: %s",
                    contratoRecorrencia.getContrato().getCodigo(), ex.getMessage()));
            UteisValidacao.enfileirarEmail(new MsgTO(contratoRecorrencia.getContrato().getEmpresa().getNome(), "Renovação Automática", sb));
        }
    }

    public Integer getCarenciaRenovacao() {
        if (carenciaRenovacao == null) {
            carenciaRenovacao = 0;
        }
        return carenciaRenovacao;
    }

    public void setCarenciaRenovacao(Integer carenciaRenovacao) {
        this.carenciaRenovacao = carenciaRenovacao;
    }
}

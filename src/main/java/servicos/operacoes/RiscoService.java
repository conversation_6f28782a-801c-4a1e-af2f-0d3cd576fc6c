/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.arquitetura.RiscoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteMensagemVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TiposMensagensEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.QuarentenaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;

/**
 *
 * <AUTHOR>
 */
public class RiscoService extends SuperEntidade {

    private boolean processamentoControlado = false;
    private List<EmpresaVO> listaEmpresa;
    private List<ClienteVO> listaClientesControlado;
    private Date dia;
    private ConfiguracaoSistemaVO configSistema;
    private UsuarioVO usuario;
    
    private String sqlClientesProcessarRisco = "SELECT cli.codigo as codigocliente,codigocontrato,sit.nomecliente,cli.matricula,sit.telefonescliente,sit.datavigenciade, sit.datavigenciaateajustada, \n" +
            "sit.situacaocontrato,sit.datarenovacaocontrato, sit.pesorisco,sit.dataultimarematricula, sit.datamatricula, sit.dataultimoacesso, sit.codigousuariomovel, \n" +
            "sit.diasacessosemanapassada, sit.diasacessosemana2, sit.diasacessosemana3, sit.diasacessosemana4,  sit.vezesporsemana, vin.colaborador as consultor, cli.pessoa as codigopessoa  \n" +
            "from cliente cli\n" +
            "inner join situacaoclientesinteticodw sit on sit.codigocliente = cli.codigo\n" +
            "inner join vinculo vin on vin.cliente = cli.codigo and vin.tipovinculo = 'CO'\n" +
            "where cli.pessoa in \n" +
            "(Select contrato.pessoa from contrato  where contrato.situacao = 'AT'  \n" +
            " and contrato.empresa = %s \n" +
            " and contrato.codigo not in \n" +
            " (select contrato.codigo from contrato \n" +
            "left join contratooperacao co on co.contrato = contrato.codigo  where (co.tipooperacao = 'AT' or co.tipooperacao = 'CR')  and ('%s'  between co.datainicioefetivacaooperacao and co.datafimefetivacaooperacao)\n" +
            " and contrato.empresa =  %s \n" +
            "))\n" +
            "order by cli.codigo";

    private String sqlClienteProcessarRiscoQuarentena = "SELECT cli.codigo as codigocliente,codigocontrato,sit.nomecliente,cli.matricula,sit.telefonescliente,sit.datavigenciade, sit.datavigenciaateajustada, \n" +
            "sit.situacaocontrato,sit.datarenovacaocontrato, sit.pesorisco,sit.dataultimarematricula, sit.datamatricula, sit.dataultimoacesso, sit.codigousuariomovel, \n" +
            "sit.diasacessosemanapassada, sit.diasacessosemana2, sit.diasacessosemana3, sit.diasacessosemana4,  sit.vezesporsemana, vin.colaborador as consultor, cli.pessoa as codigopessoa  \n" +
            "from cliente cli\n" +
            "inner join situacaoclientesinteticodw sit on sit.codigocliente = cli.codigo\n" +
            "inner join vinculo vin on vin.cliente = cli.codigo and vin.tipovinculo = 'CO'\n" +
            "where cli.pessoa in \n" +
            "(Select contrato.pessoa from contrato  where contrato.situacao = 'AT'  \n" +
            " and contrato.empresa = %s \n" +
            " and contrato.codigo not in \n" +
            " (select contrato.codigo from contrato \n" +
            "left join contratooperacao co on co.contrato = contrato.codigo  where (co.tipooperacao = 'AT' or co.tipooperacao = 'CR')  and ('%s'  between co.datainicioefetivacaooperacao and co.datafimefetivacaooperacao)\n" +
            " and contrato.empresa = %s \n" +
            "))\n" +
            " and cli.codigo = %s";

    private String sqlClienteProcessarVerificarUltiAcessRiscoQuarentena = "SELECT codigocontrato, sit.dataultimarematricula, sit.datamatricula, sit.datavigenciade, sit.dataultimoacesso \n" +
            "from cliente cli\n" +
            "inner join situacaoclientesinteticodw sit on sit.codigocliente = cli.codigo\n" +
            "inner join vinculo vin on vin.cliente = cli.codigo and vin.tipovinculo = 'CO'\n" +
            "where cli.pessoa in \n" +
            "(Select contrato.pessoa from contrato  where contrato.situacao = 'AT'  \n" +
            " and contrato.empresa = %s \n" +
            " and contrato.codigo not in \n" +
            " (select contrato.codigo from contrato \n" +
            "left join contratooperacao co on co.contrato = contrato.codigo  where (co.tipooperacao = 'AT' or co.tipooperacao = 'CR')  and ('%s'  between co.datainicioefetivacaooperacao and co.datafimefetivacaooperacao)\n" +
            " and contrato.empresa = %s \n" +
            "))\n" +
            " and cli.codigo = %s";

    public RiscoService() throws Exception {
    }

    public RiscoService(boolean processamentoControlado,
            List<EmpresaVO> listaEmpresa,
            List<ClienteVO> listaClientesControlado,
            Date dia, ConfiguracaoSistemaVO configSistema,
            UsuarioVO usuario) throws Exception {
        this.processamentoControlado = processamentoControlado;
        this.listaEmpresa = listaEmpresa;
        this.listaClientesControlado = listaClientesControlado;
        this.dia = dia;
        this.configSistema = configSistema;
        this.usuario = usuario;
    }

    public void processarClientesEmRisco() throws Exception {
        try {
             Uteis.logar(null, "Iniciando processamento (Risco) do dia "
                    + Calendario.hoje().toString() + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            if (this.processamentoControlado) {
                getFacade().getRisco().excluirPorListaCliente(listaClientesControlado);
            } else {
                getFacade().getRisco().excluirTodoRegistro();
            }
            for (EmpresaVO empresa : listaEmpresa) {
                if (!empresa.isAtiva()) {
                    continue;
                }
                QuarentenaVO quarentenaCRM = null;
                QuarentenaVO ultQuarentenaEncerrada = null;
                try {
                    quarentenaCRM = getFacade().getQuarentena().obterAtiva(empresa.getCodigo());
                    ultQuarentenaEncerrada = getFacade().getQuarentena().obterUltimaQuarentenaEncerrada(empresa.getCodigo());
                }catch (Exception ignore){}
                if(processamentoControlado){
                
                    for (ClienteVO clienteVO : listaClientesControlado) {
                        if(!clienteVO.getEmpresa().getCodigo().equals(empresa.getCodigo())){
                            continue;
                        }
                        RiscoVO risco = new RiscoVO();
                        SituacaoClienteSinteticoDWVO scDWVO = getFacade().getSituacaoClienteSinteticoDW().consultarCliente(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        if (scDWVO.getCodigoContrato() == 0) {
                            continue;
                        }
                        if (scDWVO.getCodigo() > 0) {
                            obterPesoClienteEstaEmRiscoPorRenovacao(risco, scDWVO, null, null, null, null);
                            obterPesoClienteEstaEmRiscoPorFalta(quarentenaCRM, ultQuarentenaEncerrada, risco, scDWVO, empresa, null, null, null, null);
                            if (quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva()) {
                                ResultSet rsCliente = SuperFacadeJDBC.criarConsulta(String.format(sqlClienteProcessarRiscoQuarentena, new Object[]{empresa.getCodigo(), Uteis.getDataHoraJDBC(quarentenaCRM.getInicioQuarentena(), "00:00:00"), empresa.getCodigo(), clienteVO.getCodigo()}), con);
                                while (rsCliente.next()) {
                                    if (UteisValidacao.emptyNumber(rsCliente.getInt("codigocontrato"))) {
                                        continue;
                                    }
                                    obterPesoClienteEstaEmRiscoPorFrequencia(risco, null, rsCliente.getInt("diasacessosemanapassada"), rsCliente.getInt("diasacessosemana2"), rsCliente.getInt("diasacessosemana3"), rsCliente.getInt("diasacessosemana4"), rsCliente.getInt("vezesporsemana"));
                                }
                            } else if (ultQuarentenaEncerrada.getCodigo() != null && !ultQuarentenaEncerrada.isAtiva() && quarentenaCRM == null) {
                                Date novaDataPosPeriodoQuarentenaEncerrado = obterNovaDataPosQuarentenaEncerrada(empresa.getCodigo(), clienteVO.getCodigo(), ultQuarentenaEncerrada);
                                ResultSet rsCliente = SuperFacadeJDBC.criarConsulta(String.format(sqlClienteProcessarRiscoQuarentena, new Object[]{empresa.getCodigo(), Uteis.getDataHoraJDBC(novaDataPosPeriodoQuarentenaEncerrado, "00:00:00"), empresa.getCodigo(), clienteVO.getCodigo()}), con);
                                while (rsCliente.next()) {
                                    if (UteisValidacao.emptyNumber(rsCliente.getInt("codigocontrato"))) {
                                        continue;
                                    }
                                    obterPesoClienteEstaEmRiscoPorFrequencia(risco, null, rsCliente.getInt("diasacessosemanapassada"), rsCliente.getInt("diasacessosemana2"), rsCliente.getInt("diasacessosemana3"), rsCliente.getInt("diasacessosemana4"), rsCliente.getInt("vezesporsemana"));
                                }
                            } else {
                                obterPesoClienteEstaEmRiscoPorFrequencia(risco, scDWVO, 0, 0, 0, 0, 0);
                            }
                        }
                        if (risco.getPeso() > 0) {
                            risco.setDia(dia);
                            risco.setClienteVO(clienteVO);
                            risco.setEmpresaVO(empresa);
                            risco.setNomeCliente(clienteVO.getPessoa().getNome());
                            risco.setMatriculaCliente(clienteVO.getMatricula());
                            risco.getClienteVO().setVinculoVOs(getFacade().getVinculo().consultarVinculo(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
                            Iterator e = risco.getClienteVO().getVinculoVOs().iterator();
                            while (e.hasNext()) {
                                VinculoVO vinculo = (VinculoVO) e.next();
                                if (vinculo.getTipoVinculo().equals(TipoColaboradorEnum.CONSULTOR.getSigla())) {
                                    risco.setColaboradorVO(vinculo.getColaborador());
                                    break;
                                }
                            }
                            setarMensagemRiscoCliente(risco, clienteVO);
                            try {
                                String telefones = ClienteVO.obterTelefoneCliente(clienteVO);
                                if (telefones.length() > 100) {
                                    telefones = telefones.substring(0, 100);
                                }
                                risco.setFoneCliente(telefones);
                            } catch (Exception eTel) {
                                Uteis.logar(eTel, RiscoService.class);
                            }
                            getFacade().getRisco().incluirSemCommit(risco, false);
                            getFacade().getSituacaoClienteSinteticoDW().atualizarPesoRisco(risco.getPeso(), clienteVO.getCodigo(),scDWVO.getCodigoUsuarioMovel());

                        }
                    }
                } else {
                   ResultSet rsClientes = SuperFacadeJDBC.criarConsulta(String.format(sqlClientesProcessarRisco, new Object[]{empresa.getCodigo(),
                           Uteis.getDataHoraJDBC(((quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva()) ? quarentenaCRM.getInicioQuarentena() : Calendario.hoje()), "00:00:00"), empresa.getCodigo()}), con);
                   while(rsClientes.next()){
                        if(UteisValidacao.emptyNumber(rsClientes.getInt("codigocontrato"))){
                            continue;
                        }
                        RiscoVO risco = new RiscoVO();
                        ClienteVO clienteVO = new ClienteVO();
                        PessoaVO pessoaVO = new PessoaVO();
                        pessoaVO.setCodigo(rsClientes.getInt("codigopessoa"));
                        clienteVO.setPessoa(pessoaVO);
                        ColaboradorVO colaboradorVO  = new ColaboradorVO();
                        clienteVO.setCodigo(rsClientes.getInt("codigocliente"));
                        
                        obterPesoClienteEstaEmRiscoPorRenovacao(risco, null, rsClientes.getDate("datavigenciade"),rsClientes.getDate("datavigenciaateajustada"),rsClientes.getDate("datarenovacaocontrato"),rsClientes.getString("situacaocontrato"));
                        obterPesoClienteEstaEmRiscoPorFalta(quarentenaCRM, ultQuarentenaEncerrada, risco, null, empresa, rsClientes.getDate("dataultimarematricula"),rsClientes.getDate("datamatricula"),rsClientes.getDate("dataultimoacesso"),rsClientes.getDate("datavigenciade"));

                       if (ultQuarentenaEncerrada.getCodigo() != null && !ultQuarentenaEncerrada.isAtiva() && quarentenaCRM == null) {
                           Date novaDataPosPeriodoQuarentenaEncerrado = obterNovaDataPosQuarentenaEncerrada(empresa.getCodigo(), clienteVO.getCodigo(), ultQuarentenaEncerrada);
                           ResultSet rsCliente = SuperFacadeJDBC.criarConsulta(String.format(sqlClienteProcessarRiscoQuarentena, new Object[]{empresa.getCodigo(), Uteis.getDataHoraJDBC(novaDataPosPeriodoQuarentenaEncerrado, "00:00:00"), empresa.getCodigo(), clienteVO.getCodigo()}), con);
                           while (rsCliente.next()) {
                               if (UteisValidacao.emptyNumber(rsCliente.getInt("codigocontrato"))) {
                                   continue;
                               }
                               obterPesoClienteEstaEmRiscoPorFrequencia(risco, null, rsCliente.getInt("diasacessosemanapassada"), rsCliente.getInt("diasacessosemana2"), rsCliente.getInt("diasacessosemana3"), rsCliente.getInt("diasacessosemana4"), rsCliente.getInt("vezesporsemana"));
                           }
                       }else {
                           obterPesoClienteEstaEmRiscoPorFrequencia(risco, null, rsClientes.getInt("diasacessosemanapassada"), rsClientes.getInt("diasacessosemana2"), rsClientes.getInt("diasacessosemana3"), rsClientes.getInt("diasacessosemana4"), rsClientes.getInt("vezesporsemana"));
                       }
                        if (risco.getPeso() > 0) {
                            risco.setDia(dia);
                            risco.setClienteVO(clienteVO);
                            risco.setEmpresaVO(empresa);
                            risco.setNomeCliente(rsClientes.getString("nomecliente"));
                            risco.setMatriculaCliente(rsClientes.getString("matricula"));
                            colaboradorVO.setCodigo(rsClientes.getInt("consultor"));
                            risco.setColaboradorVO(colaboradorVO);
                            setarMensagemRiscoCliente(risco, clienteVO);
                            try {
                                String telefones = ClienteVO.obterTelefoneCliente(clienteVO);
                                if (telefones.length() > 100) {
                                    telefones = telefones.substring(0, 100);
                                }
                                risco.setFoneCliente(telefones);
                            } catch (Exception eTel) {
                                Uteis.logar(eTel, RiscoService.class);
                            }
                            getFacade().getRisco().incluirSemCommit(risco, false);
                           

                        }
                        if(rsClientes.getInt("pesorisco") != risco.getPeso()){
                             getFacade().getSituacaoClienteSinteticoDW().atualizarPesoRisco(risco.getPeso(), clienteVO.getCodigo(),rsClientes.getInt("codigousuariomovel"));
                        }
                   }
                }
            }
            //EXCLUIR AS MENSAGENS DOS CLIENTES QUE ESTÃO INATIVOS.
            getFacade().getClienteMensagem().excluirClienteMensagemRiscoClientesInativos();

             Uteis.logar(null, "Terminou processamento (Risco) do dia " + Calendario.hoje().toString()
                        + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
        } catch (Exception e) {
            throw e;
        }

    }

    public void obterPesoClienteEstaEmRiscoPorRenovacao(RiscoVO risco, SituacaoClienteSinteticoDWVO scDWVO, Date vigenciade, Date vigenciaAteAjustada, Date dataRenovacao, String situacaoContrato) throws Exception {
        if(scDWVO != null){
            if(scDWVO.getDataRenovacaoContrato() == null){
                ContratoVO contrato = getFacade().getContrato().consultarPorChavePrimaria(scDWVO.getCodigoContrato(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
                if (Uteis.getCompareData(dia, contrato.getVigenciaDe()) >= 0 && Uteis.getCompareData(dia, contrato.getVigenciaAteAjustada()) <= 0) {
                    if (getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoTipoHistoricoDataInicioDataFim(contrato.getCodigo().intValue(), "AV", dia, Uteis.NIVELMONTARDADOS_DADOSBASICOS)) {
                        risco.setPeso(risco.getPeso() + 2);
                    } else {
                        risco.setPeso(risco.getPeso() + 1);
                    }
                }
            }
        } else {
            if(dataRenovacao == null){
                if(Calendario.maiorOuIgual(dia, vigenciade) && Calendario.menorOuIgual(dia, vigenciaAteAjustada)){
                    if(situacaoContrato.equals("AV")){
                        risco.setPeso(risco.getPeso() + 2);
                    }else {
                        risco.setPeso(risco.getPeso() + 1);
                    }
                }
            }
                
        }
    }

    public Date obterNovaDataPosQuarentenaEncerrada(Integer codEmpresa, Integer codCliente, QuarentenaVO ultQuarentenaEncerrada) {
        try {
            ResultSet rsClienteAcesso = SuperFacadeJDBC.criarConsulta(String.format(sqlClienteProcessarVerificarUltiAcessRiscoQuarentena, new Object[]{codEmpresa, Uteis.getDataHoraJDBC(Calendario.hoje(), "00:00:00"), codEmpresa, codCliente}), con);
            while (rsClienteAcesso.next()) {
                if (UteisValidacao.emptyNumber(rsClienteAcesso.getInt("codigocontrato"))) {
                    continue;
                }
                Date dataUltAcessoScDW = rsClienteAcesso.getTimestamp("dataultimoacesso");
                Date dataUltRematriculaScDW = rsClienteAcesso.getTimestamp("dataultimarematricula");
                if (dataUltRematriculaScDW == null) {
                    dataUltRematriculaScDW = rsClienteAcesso.getTimestamp("datamatricula");
                }
                Date vigeciade = rsClienteAcesso.getTimestamp("datavigenciade");
                if (vigeciade != null && dataUltAcessoScDW == null && (dataUltRematriculaScDW == null || Calendario.maior(vigeciade, dataUltRematriculaScDW))) {
                    dataUltRematriculaScDW = vigeciade;
                }
                if (dataUltAcessoScDW == null && dataUltRematriculaScDW != null || (dataUltRematriculaScDW != null && dataUltRematriculaScDW.compareTo(dataUltAcessoScDW) > 0)) {
                    return ((ultQuarentenaEncerrada.getCodigo() != null && Calendario.menor(dataUltRematriculaScDW, ultQuarentenaEncerrada.getFimQuarentena())) ? ultQuarentenaEncerrada.getInicioQuarentena() : Calendario.hoje());
                } else if (dataUltAcessoScDW != null) {
                    return ((ultQuarentenaEncerrada.getCodigo() != null && Calendario.menor(dataUltAcessoScDW, ultQuarentenaEncerrada.getFimQuarentena())) ? ultQuarentenaEncerrada.getInicioQuarentena() : Calendario.hoje());
                }
            }
            return Calendario.hoje();
        } catch (Exception e) {
            Uteis.logar(e.getMessage(), RiscoService.class);
            return Calendario.hoje();
        }
    }

    public void obterPesoClienteEstaEmRiscoPorFalta(QuarentenaVO quarentenaCRM, QuarentenaVO ultQuarentenaEncerrada, RiscoVO risco, SituacaoClienteSinteticoDWVO scDWVO, EmpresaVO empresa ,Date dataUltRematriculaScDW,Date dataMatriculaScDW,Date dataUltAcessoScDW, Date vigeciade) throws Exception {
        boolean quarentenaAtivada = (quarentenaCRM.getCodigo() != null && quarentenaCRM.isAtiva());
        long nrFaltasCliente = 0;
        if (dataUltRematriculaScDW == null) {
            dataUltRematriculaScDW = dataMatriculaScDW;
        }
        
        if(scDWVO != null){
            dataUltAcessoScDW = scDWVO.getDataUltimoAcesso();
            dataUltRematriculaScDW = scDWVO.getDataUltimaRematricula();
            if (dataUltRematriculaScDW == null) {
                dataUltRematriculaScDW = scDWVO.getDataMatricula();
            }
            vigeciade = scDWVO.getDataVigenciaDe();
        }
        if(vigeciade != null && dataUltAcessoScDW == null && (dataUltRematriculaScDW == null ||   Calendario.maior(vigeciade, dataUltRematriculaScDW))){
            dataUltRematriculaScDW = vigeciade;
        }
        // se data da ultima matricula é posterior a data do ultimo acesso
        if (dataUltAcessoScDW == null && dataUltRematriculaScDW != null || (dataUltRematriculaScDW != null && dataUltRematriculaScDW.compareTo(dataUltAcessoScDW) > 0)) {
            nrFaltasCliente = Uteis.nrDiasEntreDatas(dataUltRematriculaScDW, ((quarentenaAtivada && Calendario.menorOuIgual(dataUltRematriculaScDW, quarentenaCRM.getInicioQuarentena())) ?
                    quarentenaCRM.getInicioQuarentena() : ((!quarentenaAtivada && ultQuarentenaEncerrada.getCodigo() != null && Calendario.menor(dataUltRematriculaScDW, ultQuarentenaEncerrada.getFimQuarentena())) ? ultQuarentenaEncerrada.getInicioQuarentena() : Calendario.hoje())));
        } else if (dataUltAcessoScDW != null) {
            nrFaltasCliente = Uteis.nrDiasEntreDatas(dataUltAcessoScDW, ((quarentenaAtivada && Calendario.menorOuIgual(dataUltAcessoScDW, quarentenaCRM.getInicioQuarentena())) ?
                    quarentenaCRM.getInicioQuarentena() : ((!quarentenaAtivada && ultQuarentenaEncerrada.getCodigo() != null && Calendario.menor(dataUltAcessoScDW, ultQuarentenaEncerrada.getFimQuarentena())) ? ultQuarentenaEncerrada.getInicioQuarentena() : Calendario.hoje())));
        }

        if(getFacade().getQuarentena().obterTodas(empresa.getCodigo()).size() == 0) { // QuarentenaCRM sobrepoe config geral empresa.
            if (dataUltAcessoScDW == null && dataUltRematriculaScDW != null || (dataUltRematriculaScDW != null && dataUltRematriculaScDW.compareTo(dataUltAcessoScDW) > 0)) {
                nrFaltasCliente = Uteis.nrDiasEntreDatas(dataUltRematriculaScDW, Calendario.hoje());
                nrFaltasCliente -= verfificarPeriodoDesconsiderar(dataUltRematriculaScDW);
            } else if (dataUltAcessoScDW != null) {
                nrFaltasCliente = Uteis.nrDiasEntreDatas(dataUltAcessoScDW, Calendario.hoje());
                nrFaltasCliente -= verfificarPeriodoDesconsiderar(dataUltAcessoScDW);
            }
            if (nrFaltasCliente < 0) {
                return;
            }
        }
        if (empresa.getQtdFaltaPeso1() != 0 && empresa.getQtdFaltaPeso1().longValue() >= nrFaltasCliente) {
            risco.setPeso(risco.getPeso() + 1);
            return;
        }
        if (empresa.getQtdFaltaInicioPeso2() != 0 && empresa.getQtdFaltaTerminoPeso2() != 0 && empresa.getQtdFaltaInicioPeso2().longValue() <= nrFaltasCliente && empresa.getQtdFaltaTerminoPeso2().longValue() >= nrFaltasCliente) {
            risco.setPeso(risco.getPeso() + 2);
            return;
        }
        if (empresa.getQtdFaltaPeso3() != 0 && empresa.getQtdFaltaPeso3().longValue() <= nrFaltasCliente) {
            risco.setPeso(risco.getPeso() + 3);
            return;
        }
        if (configSistema.getQtdFaltaPeso1() != 0 && configSistema.getQtdFaltaPeso1() >= nrFaltasCliente) {
            risco.setPeso(risco.getPeso() + 1);
            return;
        }
        if (configSistema.getQtdFaltaInicioPeso2() != 0 && configSistema.getQtdFaltaTerminoPeso2() != 0 && configSistema.getQtdFaltaInicioPeso2() <= nrFaltasCliente && configSistema.getQtdFaltaTerminoPeso2() >= nrFaltasCliente) {
            risco.setPeso(risco.getPeso() + 2);
            return;
        }
        if (configSistema.getQtdFaltaPeso3() != 0 && configSistema.getQtdFaltaPeso3() <= nrFaltasCliente) {
            risco.setPeso(risco.getPeso() + 3);
            return;
        }
    }

    private long verfificarPeriodoDesconsiderar(Date dataUltimoAcesso) throws Exception {

        Date dataInicioPeriodoDesconsiderar = configSistema.getDataInicioDesconsiderarAcessoRisco();
        Date dataFinalPeriodoDesconsiderar = configSistema.getDataFimDesconsiderarAcessoRisco();

        if (dataInicioPeriodoDesconsiderar == null || dataFinalPeriodoDesconsiderar == null) {
            return 0;
        }
        long diasPeriodoDesconsiderar = Calendario.diferencaEmDias(dataInicioPeriodoDesconsiderar, dataFinalPeriodoDesconsiderar);

        if (Calendario.menor(dataInicioPeriodoDesconsiderar, dataUltimoAcesso) && Calendario.menor(dataFinalPeriodoDesconsiderar, dataUltimoAcesso)) {
            return 0;
        }
        if (Calendario.maior(dataInicioPeriodoDesconsiderar, Calendario.hoje()) && Calendario.maior(dataFinalPeriodoDesconsiderar, Calendario.hoje())) {
            return 0;
        }

        if (Calendario.entre(dataInicioPeriodoDesconsiderar, dataUltimoAcesso, Calendario.hoje()) && Calendario.maior(dataFinalPeriodoDesconsiderar, Calendario.hoje())) {
            return Calendario.diferencaEmDias(dataInicioPeriodoDesconsiderar, Calendario.hoje());
        }
        if (Calendario.entre(dataFinalPeriodoDesconsiderar, dataUltimoAcesso, Calendario.hoje()) && Calendario.menor(dataInicioPeriodoDesconsiderar, dataUltimoAcesso)) {
            return Calendario.diferencaEmDias(dataUltimoAcesso, dataFinalPeriodoDesconsiderar);
        }

        return diasPeriodoDesconsiderar;
    }

    public void obterPesoClienteEstaEmRiscoPorFrequencia(RiscoVO risco, SituacaoClienteSinteticoDWVO scDWVO, Integer diasAcessoSemanaPassadaScDW, Integer diasAcessoSemana2ScDW, Integer diasAcessoSemana3ScDW ,Integer diasAcessoSemana4ScDW, Integer vezesPorSemanascDW) throws Exception {
        double nrFrequenciaCliente = 0;
        
        if(scDWVO != null){
            diasAcessoSemanaPassadaScDW = scDWVO.getDiasAcessoSemanaPassada();
            diasAcessoSemana2ScDW = scDWVO.getDiasAcessoSemana2();
            diasAcessoSemana3ScDW = scDWVO.getDiasAcessoSemana3();
            diasAcessoSemana4ScDW = scDWVO.getDiasAcessoSemana4();
            vezesPorSemanascDW = scDWVO.getVezesporsemana();
        }
        
        // pega a frequencia da semana passada
        if (diasAcessoSemanaPassadaScDW >= 0) {
            nrFrequenciaCliente = diasAcessoSemanaPassadaScDW;
            // frequencia da semana anterior 2
            if (diasAcessoSemana2ScDW >= 0) {
                nrFrequenciaCliente += diasAcessoSemana2ScDW;
                // frequencia da semana anterior 3
                if (diasAcessoSemana3ScDW >= 0) {
                    nrFrequenciaCliente += diasAcessoSemana3ScDW;
                    // frequencia da semana anterior 4
                    if (diasAcessoSemana4ScDW >= 0) // faz a media
                    {
                        nrFrequenciaCliente += diasAcessoSemana4ScDW;
                        nrFrequenciaCliente = nrFrequenciaCliente / (vezesPorSemanascDW * 4);
                    } else // faz a media
                    {
                        nrFrequenciaCliente = nrFrequenciaCliente / (vezesPorSemanascDW * 3);
                    }
                } else // faz a media
                {
                    nrFrequenciaCliente = nrFrequenciaCliente / (vezesPorSemanascDW * 2);
                }
            } else // faz a media
            {
                nrFrequenciaCliente = nrFrequenciaCliente / vezesPorSemanascDW;
            }
            if (nrFrequenciaCliente <= 0.4) {
                risco.setPeso(risco.getPeso() + 3);
            } else if (nrFrequenciaCliente <= 0.65) {
                risco.setPeso(risco.getPeso() + 2);
            } else {
                risco.setPeso(risco.getPeso() + 1);
            }
        }
        
    }

    /**
     * Método que o robô usa para setar a mensagem de risco para cada cliente de acordo com o risco
     * sendo que somente irá aparecer a mensagem para riscos maiores ou iguais a 6
     * @param risco
     * @param clienteVO
     * @throws Exception
     */
    public void setarMensagemRiscoCliente(RiscoVO risco, ClienteVO clienteVO) throws Exception {
        ClienteMensagemVO msg = new ClienteMensagemVO();
        Boolean existeRisco = new ClienteMensagem().consultarPorCodigoClienteRiscoMaiorQueSeis(clienteVO.getCodigo(), false);
        if (risco.getPeso() >= 6) {
            Boolean pesoRisco = getFacade().getClienteMensagem().consultarPendenciaClienteMensagemRiscoPeso(clienteVO.getCodigo(), risco.getPeso(), false);
            if (!existeRisco) {
                msg.getCliente().setCodigo(clienteVO.getCodigo());
                msg.setTipomensagem(TiposMensagensEnum.RISCO);
                msg.getUsuario().setCodigo(usuario.getCodigo());
                msg.getRisco().setPeso(risco.getPeso());
                msg.setMensagem(TiposMensagensEnum.RISCO.getMensagem().replace("Z", risco.getPeso().toString()));
                getFacade().getClienteMensagem().incluirSemCommit(msg);
            } else if (!pesoRisco) {
                ResultSet consultaCodigo = getFacade().getClienteMensagem().consultarPendenciaClienteMensagemRiscoPorCodigo(clienteVO.getCodigo(), false);
                if (consultaCodigo.next()) {
                    Integer codigoMsg = new Integer(consultaCodigo.getInt("codigo"));
                    msg.setCodigo(codigoMsg);
                    msg.getCliente().setCodigo(clienteVO.getCodigo());
                    msg.setMensagem(TiposMensagensEnum.RISCO.getMensagem().replace("Z", risco.getPeso().toString()));
                    msg.setTipomensagem(TiposMensagensEnum.RISCO);
                    msg.getUsuario().setCodigo(usuario.getCodigo());
                    msg.getRisco().setPeso(risco.getPeso());
                    getFacade().getClienteMensagem().alterar(msg);
                }
            }
        } else if (existeRisco) {
            msg.getCliente().setCodigo(clienteVO.getCodigo());
            getFacade().getClienteMensagem().excluirClienteMensagemRisco(clienteVO.getCodigo());
        }

    }

    public static void main(String... args) {
        try {
//            Connection con = DriverManager.getConnection("**************************************", "postgres", "pactodb");
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "corpo");
            Conexao.guardarConexaoForJ2SE(c);
            RiscoService riscoService = new RiscoService(false,
                    FacadeManager.getFacade().getEmpresa().consultarTodas(true,Uteis.NIVELMONTARDADOS_ROBO),
                    null, Calendario.hoje(),
                    FacadeManager.getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_ROBO),
                    FacadeManager.getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_TODOS));
            riscoService.processarClientesEmRisco();
        } catch (Exception ex) {
            Logger.getLogger(RiscoService.class.getName()).log(Level.SEVERE, null, ex);
        }

    }
}

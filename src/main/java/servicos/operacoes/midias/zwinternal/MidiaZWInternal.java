/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.zwinternal;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.rmi.CORBA.Util;
import java.io.File;

/**
 * <AUTHOR>
 */
public class MidiaZWInternal extends MidiaService {

    @Override
    public String downloadObject(String chave, MidiaEntidadeEnum tipo, String identificador) throws Exception {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public byte[] downloadObjectAsByteArray(String chave, MidiaEntidadeEnum tipo, String identificador, String fotoKey) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
        if (chave!=null && tipo != null && identificador != null) {
            final String key = UteisValidacao.emptyString(fotoKey) ? genKey(chave, tipo, identificador) : fotoKey;
            Uteis.logar(null, "Getting an object of ZW-INTERNAL as a ByteArray " + key);
            path += key;
        }
        return obterArquivo(path);
    }

    public byte[] downloadObjectWithExtensionAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador, final String extensao, String fotoKey) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
        if (chave!=null && tipo != null && identificador != null && extensao != null) {
            final String key = UteisValidacao.emptyString(fotoKey) ? genKey(chave, tipo, identificador) : fotoKey;
            Uteis.logar(null, "Getting an object of ZW-INTERNAL as a ByteArray " + key);
            path += key;
        }
        return obterArquivo(path);
    }

    private byte[] obterArquivo(String path) throws Exception {
        File file = new File(path);
        if (!file.exists()) {
            file = new File(PropsService.getPropertyValue(PropsService.diretorioFotos) + "fotoPadrao.jpg");
        }
        return FileUtilities.obterBytesArquivo(file);
    }

    @Override
    public boolean exists(String key) {
        String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
        path += key;
        File file = new File(path);
        return file.exists();
    }

    @Override
    public void deleteObject(final String key) {
        if (exists(key)) {
            String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
            path += key;
            File file = new File(path);
            file.delete();
            Uteis.logar(null, "Removed object from ZW-INTERNAL " + key);
        }
    }

    @Override
    public String uploadObject(String chave, MidiaEntidadeEnum tipo, String identificador, File file) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
        final String key = genKey(chave, tipo, identificador);
        Uteis.logar(null, "Uploading a new object to ZW-INTERNAL from a ByteArray " + identificador + key);
        path += key;

        file = droparAlphaChannel(tipo, file);
        File fileGerar = new File(path);
        FileUtilities.forceDirectory(fileGerar.getParent());
        FileUtilities.saveToFile(FileUtilities.obterBytesArquivo(file), path);
        return key;

    }

    @Override
    public String uploadObjectWithExtension(String chave, MidiaEntidadeEnum tipo, String identificador, File file, String extensao) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
        final String key = genKey(chave, tipo, identificador, extensao);
        Uteis.logar(null, "Uploading a new object to ZW-INTERNAL from a ByteArray " + identificador + key);
        path += key;

        File fileGerar = new File(path);
        FileUtilities.forceDirectory(fileGerar.getParent());
        FileUtilities.saveToFile(FileUtilities.obterBytesArquivo(file), path);
        return key;

    }

    @Override
    public String uploadObjectFromByteArray(String chave, MidiaEntidadeEnum tipo, String identificador, byte[] bytes) throws Exception {
        return uploadObjectFromByteArray(chave, tipo, identificador, bytes, true);
    }

    @Override
    public String uploadObjectFromByteArray(String chave, MidiaEntidadeEnum tipo, String identificador, byte[] bytes, boolean encriptarIdentificador) throws Exception {
        if (bytes != null && bytes.length > 0) {
            String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
            final String key;
            if(encriptarIdentificador){
                key = genKey(chave, tipo, identificador);
            }else{
                key = chave + "/" +identificador;
            }
            Uteis.logar(null, String.format("Uploading a new object %s %s %s to ZW-INTERNAL from a ByteArray with key %s",
                    chave, tipo, identificador, key));
            path += key;

            File file = new File(path);
            FileUtilities.forceDirectory(file.getParent());
            bytes = droparAlphaChannel(tipo, bytes);
            FileUtilities.saveToFile(bytes, path);
            return key;
        } else {
            return "ERRO: Array de Bytes informado é inválido!";
        }
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier) throws Exception {
        return genKey(chave, tipo, identifier, tipo.getExtensao());
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, final String extensao) throws Exception {
        return String.format("%s/%s/%s%s", chave, Uteis.encriptarZWInternal(tipo.name().toLowerCase()), Uteis.encriptarZWInternal(identifier), extensao);
    }

    
    public void moveObject(final String key, final String keyDestino) {
        
    }

    public void deleteObject(final String chave, MidiaEntidadeEnum tipo, final String identificador) throws Exception {
        final String key = genKey(chave, tipo, identificador);
        Uteis.logar(null, "Deleting an object " + key);
        deleteObject(key);
    }

}

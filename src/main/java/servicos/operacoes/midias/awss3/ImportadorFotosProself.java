package servicos.operacoes.midias.awss3;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ImportadorFotosProself {

    public static void importar(Connection conOrigem, Connection conDestino) {
        try {
            List<PessoaFoto> pessoasImportadasSemFoto = consultarPessoas(conDestino);
            preencherFotokey(pessoasImportadasSemFoto, conOrigem, "and cli.empresa in (111, 119, 203)");

            processarLista(conDestino, pessoasImportadasSemFoto);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void importarColaboradores(Connection conOrigem, Connection conDestino) {
        try {
            List<PessoaFoto> pessoasImportadasSemFoto = consultarPessoasColaboradores(conDestino);
            preencherFotokeyColaborador(pessoasImportadasSemFoto, conOrigem);

            processarLista(conDestino, pessoasImportadasSemFoto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void processarLista(Connection conDestino, List<PessoaFoto> pessoasImportadasSemFoto) {
        for (PessoaFoto pessoaFoto : pessoasImportadasSemFoto) {
            try {
                if (UteisValidacao.emptyString(pessoaFoto.getFotokeyProself())) {
                    continue;
                }
                URL url = new URL("http://image.proselfit.com.br/getfiles/" + pessoaFoto.getFotokeyProself());
                ByteArrayOutputStream output = new ByteArrayOutputStream();

                try (InputStream inputStream = url.openStream()) {
                    int n = 0;
                    byte[] buffer = new byte[1024];
                    while (-1 != (n = inputStream.read(buffer))) {
                        output.write(buffer, 0, n);
                    }
                }

                byte[] foto = output.toByteArray();
                final String ident = MidiaService.getInstance().uploadObjectFromByteArray("146d607f8343d7d03efde0a5d72a76e8", MidiaEntidadeEnum.FOTO_PESSOA, pessoaFoto.getCodPessoaDestino().toString(), foto);
                SuperFacadeJDBC.executarConsulta(String.format("update pessoa set fotokey = '%s' where codigo = %s", ident, pessoaFoto.getCodPessoaDestino()), conDestino);
                Uteis.logar(null, String.format("Enviado %s %s", pessoaFoto.getMatricula(), ident));
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private static void preencherFotokey(List<PessoaFoto> pessoasImportadasSemFoto, Connection conOrigem, String inUnidades) throws SQLException {
        String sql = "SELECT cli.matricula, p.fotokey from pessoa p\n" +
                "inner join cliente cli on p.codigo = cli.pessoa \n" +
                "where p.fotokey is not null\n";
        if (!UteisValidacao.emptyString(inUnidades)) {
            sql += inUnidades;
        }
        PreparedStatement ps = conOrigem.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        Map<String, String> mapaMatriculasFotokey = new HashMap<>();
        while (rs.next()) {
            String matricula = rs.getString("matricula");
            String fotokey = rs.getString("fotokey");
            mapaMatriculasFotokey.put(matricula, fotokey);
        }

        for (PessoaFoto pessoaFoto : pessoasImportadasSemFoto) {
            String fotokeyProself = mapaMatriculasFotokey.get(pessoaFoto.getMatricula());
            if (fotokeyProself != null && !fotokeyProself.equals("")) {
                pessoaFoto.setFotokeyProself(fotokeyProself);
            }
        }
    }

    private static void preencherFotokeyColaborador(List<PessoaFoto> pessoasImportadasSemFoto, Connection conOrigem) throws SQLException {
        String sql = "SELECT col.codigo, p.fotokey from pessoa p\n" +
                "inner join colaborador col on p.codigo = col.pessoa \n" +
                "where p.fotokey is not null\n";

        PreparedStatement ps = conOrigem.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();
        Map<String, String> mapaMatriculasFotokey = new HashMap<>();
        while (rs.next()) {
            String matricula = rs.getString("codigo");
            String fotokey = rs.getString("fotokey");
            mapaMatriculasFotokey.put(matricula, fotokey);
        }

        for (PessoaFoto pessoaFoto : pessoasImportadasSemFoto) {
            String fotokeyProself = mapaMatriculasFotokey.get(pessoaFoto.getMatricula());
            if (fotokeyProself != null && !fotokeyProself.equals("")) {
                pessoaFoto.setFotokeyProself(fotokeyProself);
            }
        }
    }

    private static List<PessoaFoto> consultarPessoas(Connection conDestino) throws SQLException {
        List<PessoaFoto> pessoas = new ArrayList<>();
        String sql = "SELECT cli.matricula, p.codigo from pessoa p\n" +
                "inner join cliente cli on p.codigo = cli.pessoa \n" +
                "where cli.matriculaexterna is not null\n" +
                "and p.fotokey is null";
        PreparedStatement ps = conDestino.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();

        while (rs.next()) {
            PessoaFoto pessoaFoto = new PessoaFoto();
            pessoaFoto.setMatricula(rs.getString("matricula"));
            pessoaFoto.setCodPessoaDestino(rs.getInt("codigo"));
            pessoas.add(pessoaFoto);
        }
        return pessoas;
    }

    private static List<PessoaFoto> consultarPessoasColaboradores(Connection conDestino) throws SQLException {
        List<PessoaFoto> pessoas = new ArrayList<>();
        String sql = "SELECT col.idexterno as matricula, p.codigo from pessoa p\n" +
                "inner join colaborador col on p.codigo = col.pessoa \n" +
                "where col.idexterno is not null\n" +
                "and p.fotokey is null";
        PreparedStatement ps = conDestino.prepareStatement(sql);
        ResultSet rs = ps.executeQuery();

        while (rs.next()) {
            PessoaFoto pessoaFoto = new PessoaFoto();
            pessoaFoto.setMatricula(rs.getString("matricula"));
            pessoaFoto.setCodPessoaDestino(rs.getInt("codigo"));
            pessoas.add(pessoaFoto);
        }
        return pessoas;
    }

    public static void main(String[] args) {
        try {
            Connection conOrigem = DriverManager.getConnection("*********************************************", "postgres", "pactodb");
            Connection conDestino =  DriverManager.getConnection("jdbc:postgresql://*************:5432/bdzillyonselfitoficial", "postgres", "pactodb");;
//            importar(conOrigem, conDestino);
            importarColaboradores(conOrigem, conDestino);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


}

class PessoaFoto {

    private String fotokeyProself;
    private String matricula;
    private Integer codPessoaDestino;

    public String getFotokeyProself() {
        return fotokeyProself;
    }

    public void setFotokeyProself(String fotokeyProself) {
        this.fotokeyProself = fotokeyProself;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getCodPessoaDestino() {
        return codPessoaDestino;
    }

    public void setCodPessoaDestino(Integer codPessoaDestino) {
        this.codPessoaDestino = codPessoaDestino;
    }
}


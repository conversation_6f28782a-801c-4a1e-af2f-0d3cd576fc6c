/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.awss3;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;

/**
 *
 * <AUTHOR>
 */
public class MoverNotaNfse {
    
     private static void moverNotas() throws Exception {
        DAO dao = new DAO();
        try {
            Connection c = null;
            try {
                c = dao.obterConexaoEspecifica("");
                NotaFiscalDeServico notaDao = new NotaFiscalDeServico(c);
                notaDao.moverPDFNotas();
                } catch (Exception e) {
                    Uteis.logar(null, "Erro ao migrar notas -> " + e.getMessage());
            } finally {
                if (c != null) {
                    c.close();
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosZWParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
     }
     
     public static void main(String[] args) throws Exception{
         moverNotas();
     }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.awss3;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;

/**
 * <AUTHOR>
 */
public class MigracaoFotosS3ParaS3Service {


    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            String keyOrigem = "f1ff82d1e388cdc35b16d0ea33d90966";
            String keyDestino = "5e945d79592f112fae3c838960a8063f";
            if (args.length == 2) {
                keyOrigem = args[0];
                keyDestino = args[1];
            }
            DAO oamd = new DAO();
            Connection c = oamd.obterConexaoEspecifica(keyDestino);

            AmazonS3Client s3 = new AmazonS3Client();
            //
            s3.copyAllItems(keyOrigem, keyDestino);
            //
            String atualizarChaves = "UPDATE pessoa SET fotokey = replace(fotokey, '%s', '%s') WHERE fotokey is not null;";
            SuperFacadeJDBC.executarConsulta(String.format(atualizarChaves, keyOrigem, keyDestino), c);
        } catch (Exception ex) {
            Logger.getLogger(MigracaoFotosS3ParaS3Service.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

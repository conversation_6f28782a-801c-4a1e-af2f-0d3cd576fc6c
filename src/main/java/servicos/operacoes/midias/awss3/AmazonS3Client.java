/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.awss3;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ListObjectsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.transfer.TransferManager;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.commons.io.IOUtils;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class AmazonS3Client extends MidiaService {

    private AmazonS3 s3 = null;
    private String bucketName = "prod-zwphotos";

    public String getBucketName() {
        return bucketName;
    }

    public AmazonS3Client() {
        internal();
    }

    public AmazonS3Client(final String bucketName) {
        this.bucketName = bucketName;
        internal();
    }

    private void internal() {
        try {
            s3 = new com.amazonaws.services.s3.AmazonS3Client(new AWSCredentials() {
                @Override
                public String getAWSAccessKeyId() {
                    return "********************";
                }

                @Override
                public String getAWSSecretKey() {
                    return "GBHnWy3xl22G85vM0jdrYQ6LBisocM/wdiSvM5Bs";
                }
            });
            Region region = Region.getRegion(Regions.DEFAULT_REGION);
            s3.setRegion(region);
            s3.setEndpoint("s3-sa-east-1.amazonaws.com");
        } catch (Exception e) {
            throw new AmazonClientException(
                    "AmazonS3ServiceClient.internal(): Nao pude inicializar Amazon S3 Client devido ao erro:"
                    + e.getMessage(),
                    e);
        }
    }

    public void listBuckets() {
        Uteis.logar(null, "Listing buckets");
        for (Bucket bucket : s3.listBuckets()) {
            Uteis.logar(null, " - " + bucket.getName());
        }
    }

    @Override
    public boolean exists(final String key) {
        try {
            s3.getObjectMetadata(bucketName, key);
            return true;
        } catch (Exception e) {
        }
        return false;
    }

    public void listBucketsContents(final String prefix) {
        ObjectListing objectListing = s3.listObjects(new ListObjectsRequest()
                .withBucketName(bucketName)
                .withPrefix(prefix));
        for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {
            Uteis.logar(null, " - " + objectSummary.getKey() + "  "
                    + "(size = " + objectSummary.getSize() + ")");
        }
    }

    public ObjectListing getObjectListBucketContent(final String prefix) {
        return s3.listObjects(new ListObjectsRequest().withBucketName(bucketName).withPrefix(prefix));
    }

    public void createBucket(final String bucketName) {
        s3.createBucket(bucketName);
    }

    @Override
    public String uploadObject(final String chave, MidiaEntidadeEnum tipo,
            final String identificador, File file) throws Exception {
        final String key = genKey(chave, tipo, identificador);
        Uteis.logar(null, "Uploading a new object to S3 from a file " + key);
        file = droparAlphaChannel(tipo, file);
        s3.putObject(new PutObjectRequest(bucketName, key, file));
        return key;
    }

    @Override
    public String uploadObjectWithExtension(final String chave, MidiaEntidadeEnum tipo,
                               final String identificador, File file, String extensao) throws Exception {
        final String key = genKey(chave, tipo, identificador, extensao);
        Uteis.logar(null, "Uploading a new object to S3 from a file " + key);
        s3.putObject(new PutObjectRequest(bucketName, key, file));
        return key;
    }

    public String uploadObject(final String chave, MidiaEntidadeEnum tipo, File file) throws Exception {
        String identificador = file.getAbsolutePath();
        identificador = identificador.substring(identificador.indexOf(chave)).replaceAll("\\\\", "/");
        identificador = identificador.replaceAll("\\+", "*");
        Uteis.logar(null, "Uploading a new object to S3 from a file " + identificador);
        s3.putObject(new PutObjectRequest(bucketName, identificador, file));
        return identificador;
    }

    @Override
    public String uploadObjectFromByteArray(final String chave, MidiaEntidadeEnum tipo,
                                            final String identificador, byte[] bytes) throws Exception {
        return uploadObjectFromByteArray(chave, tipo, identificador, bytes, true);
    }

    @Override
    public String uploadObjectFromByteArray(final String chave, MidiaEntidadeEnum tipo,
            final String identificador, byte[] bytes, boolean encriptarIdentificador) throws Exception {
        if (bytes != null && bytes.length > 0) {
            final String key;
            if(encriptarIdentificador){
                key = genKey(chave, tipo, identificador);
            }else{
                key = chave + "/" +identificador;
            }
            Uteis.logar(null, "Uploading a new object to S3 from a file " + key);
            bytes = droparAlphaChannel(tipo, bytes);
            ObjectMetadata om = new ObjectMetadata();
            om.setContentLength(bytes.length);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            s3.putObject(bucketName, key, bis, om);
            return key;
        } else {
            return "ERRO: Array de Bytes informado é inválido!";
        }
    }

    public S3Object findS3Object(final String key) throws Exception {
        Uteis.logar(null, "Downloading an object");
        return s3.getObject(new GetObjectRequest(bucketName, key));
    }

    @Override
    public String downloadObject(final String chave, MidiaEntidadeEnum tipo, final String identificador) throws Exception {
        Uteis.logar(null, "Downloading an object");
        final String key = genKey(chave, tipo, identificador);
        S3Object object = s3.getObject(new GetObjectRequest(bucketName, key));
        Uteis.logar(null, "Content-Type: " + object.getObjectMetadata().getContentType());
        try {
            FileOutputStream fos = new FileOutputStream(key);
            BufferedReader reader = new BufferedReader(new InputStreamReader(object.getObjectContent()));
            while (true) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                }
                fos.write(line.getBytes());
            }
        } catch (IOException ex) {
            Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
        }finally {
            object.close();
        }
        return key;
    }

    @Override
    public byte[] downloadObjectAsByteArray(final String chave, MidiaEntidadeEnum tipo,
                                            final String identificador, String key) throws Exception {
        return downloadObjectWithExtensionAsByteArray(chave, tipo, identificador, null, key);
    }

    public byte[] downloadObjectWithExtensionAsByteArray(final String chave, MidiaEntidadeEnum tipo, final String identificador, final String extensao, String fotoKey) throws Exception {
        String key;
        key = UteisValidacao.emptyString(fotoKey) ? getKey(chave, tipo, identificador, extensao) : fotoKey;
        Uteis.logar(null, "Downloading an object " + key);
        byte[] foto = null;
        try {
            ObjectMetadata metadata = s3.getObjectMetadata(bucketName, key);
            if (metadata != null) {
                S3Object object = s3.getObject(new GetObjectRequest(bucketName, key));
                Uteis.logar(null, "Object: " + key + "\nContent-Type: " + object.getObjectMetadata().getContentType());
                try {
                    foto = IOUtils.toByteArray(object.getObjectContent());
                } catch (IOException ex) {
                    Logger.getLogger(AmazonS3Client.class.getName()).log(Level.SEVERE, null, ex);
                } finally {
                    object.close();
                }
            } else {
                Uteis.logar(null, "Downloading an object FAILED: object not exists " + key);
            }
        } catch (Exception e) {
            Uteis.logar(null, "Downloading an object FAILED: " + e.getMessage());
        }
        return foto;
    }

    @Override
    public void deleteObject(final String key) {
        s3.deleteObject(bucketName, key);
        Uteis.logar(null, "Removed object from S3 " + key);
    }

    public void uploadDirectory(final String prefixKeyDirectory, final String localDirectory) {
        TransferManager transferManager = new TransferManager(s3);
        transferManager.uploadDirectory(bucketName, prefixKeyDirectory,
                new File(localDirectory), true);
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier) throws Exception {
        return genKey(chave, tipo, identifier, tipo.getExtensao());
    }

    public String genKey(final String chave, MidiaEntidadeEnum tipo, final String identifier, final String extensao) throws Exception {
        return String.format("%s/%s/%s%s", chave, Uteis.encriptarAWS(tipo.name().toLowerCase()), Uteis.encriptarAWS(identifier), extensao);
    }

    public void copy(String keyOrigem, String keyDestino) {
        try {
            CopyObjectRequest copyObjRequest = new CopyObjectRequest(bucketName, keyOrigem, bucketName, keyDestino);
            System.out.println("Copying object.");
            s3.copyObject(copyObjRequest);
        } catch (AmazonServiceException ase) {
            System.out.println("Caught an AmazonServiceException, " +
                    "which means your request made it " +
                    "to Amazon S3, but was rejected with an error " +
                    "response for some reason.");
            System.out.println("Error Message:    " + ase.getMessage());
            System.out.println("HTTP Status Code: " + ase.getStatusCode());
            System.out.println("AWS Error Code:   " + ase.getErrorCode());
            System.out.println("Error Type:       " + ase.getErrorType());
            System.out.println("Request ID:       " + ase.getRequestId());
        } catch (AmazonClientException ace) {
            System.out.println("Caught an AmazonClientException, " +
                    "which means the client encountered " +
                    "an internal error while trying to " +
                    " communicate with S3, " +
                    "such as not being able to access the network.");
            System.out.println("Error Message: " + ace.getMessage());
        }
    }
    
    public void copyAllItems(final String keyOrigem, final String keyDestino) {
        Uteis.logar(String.format(" - Copy objects from bucket %s to %s", keyOrigem, keyDestino));
        ObjectListing result = getObjectListBucketContent(keyOrigem);
        do {
            for (Iterator<S3ObjectSummary> iterator = result.getObjectSummaries().iterator(); iterator.hasNext();) {
                S3ObjectSummary summary = iterator.next();
                final String newKey = summary.getKey().replace(keyOrigem, keyDestino);
                copy(summary.getKey(), newKey);
                Uteis.logar(null, String.format("Copied object %s to %s", summary.getKey(), newKey));
            }
            // load more items!!!
            result = s3.listNextBatchOfObjects(result);
        } while(result != null && result.isTruncated());
    }
    
    public void moveObject(final String key, final String keyDestino) {
        try {
            s3.copyObject(bucketName, key, bucketName, keyDestino);
            s3.deleteObject(bucketName, key);
            Uteis.logar(null, "moved object from S3 " + key);
        }catch(Exception e){
            Uteis.logar(null, "move object "+key+" FAILED: " + e.getMessage());
        }
    }

    private String getKey(String chave, MidiaEntidadeEnum tipo, String identificador, String extensao) throws Exception {
        String key;
        if (identificador == null) {
            if (tipo.name().contains("EMPRESA")) {
                key = "logoPadraoRelatorio.jpg";
            } else {
                key = "fotoPadrao.jpg";
            }
        } else if (tipo.equals(MidiaEntidadeEnum.PDF_NFE) || tipo.equals(MidiaEntidadeEnum.XML_NFE)) {
            key = chave + identificador + tipo.getExtensao();
        } else {
            if (extensao != null) {
                key = genKey(chave, tipo, identificador, extensao);
            } else {
                key = genKey(chave, tipo, identificador);
            }
        }
        return key;
    }


    public void deleteObject(final String chave, MidiaEntidadeEnum tipo, final String identificador) throws Exception {
        String key = getKey(chave, tipo, identificador, null);
        Uteis.logar(null, "Deleting an object " + key);
        deleteObject(key);
    }

    public AmazonS3 getS3() {
        return s3;
    }
}

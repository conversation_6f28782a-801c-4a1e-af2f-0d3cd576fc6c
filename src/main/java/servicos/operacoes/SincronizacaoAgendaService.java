/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.estudio.dao.AgendaEstudio;
import br.com.pactosolucoes.estudio.interfaces.AgendaEstudioInterfaceFacade;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import java.sql.Connection;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class SincronizacaoAgendaService {
    
    private Connection con;
    private String key;
    private AgendaEstudioInterfaceFacade agendaEstudioDao;
    
    public SincronizacaoAgendaService(String key) throws Exception {
        this.key = key;
        this.con = new DAO().obterConexaoEspecifica(key);
        Conexao.guardarConexaoForJ2SE(this.con);
        agendaEstudioDao = new AgendaEstudio(con);
    }
    
    public static void main(String[] args) {
        try {
            if(args.length > 0){
                SincronizacaoAgendaService service = new SincronizacaoAgendaService(args[0]);
                service.sincronizarTudo();
            }else{
               SincronizacaoAgendaService service = new SincronizacaoAgendaService("dhyana");
               service.sincronizarTudo(); 
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    } 
    

    public void sincronizarTudo() throws Exception{
        List<AgendaVO> agendamentosSincronizar = agendaEstudioDao.buscarAgendamentosSincronizar();
        for(AgendaVO agendamento : agendamentosSincronizar){
            try {
                Uteis.logar(null, key + " - Vou sincronizar o agendamento "+agendamento.getCodigo()+
                        " do professor "+agendamento.getColaboradorVO().getPessoa().getNome());
                if(agendamento.isAlterado()){
                    boolean resultado = agendaEstudioDao.deletarAgendamentoGoogleCalendar(agendamento);
                    Uteis.logar(null, key + " - Tentei deletar o agendamento "+agendamento.getCodigo()+
                            " na agenda do google. Resultado: "+(resultado ? "sucesso!" : "erro!"));    
                }
                agendaEstudioDao.salvarAgendamentoGoogleCalendar(agendamento.getCodigo(), agendamento);
                Uteis.logar(null, key + " - Agendamento "+agendamento.getCodigo()+" sincronizado com sucesso! ");
            } catch (Exception e) {
                Uteis.logar(null, key + " - Não foi possível sincronizar o agendamento "+agendamento.getCodigo()+
                        ".Erro: "+e.getMessage());
            }
            
            
        }
    }
}

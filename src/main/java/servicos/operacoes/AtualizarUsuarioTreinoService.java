/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.arquitetura.UsuarioEmailVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.UsuarioEmail;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovel;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.TreinoWSConsumer;
import servicos.integracao.treino.UsuarioZW;

/**
 *
 * <AUTHOR>
 */
public class AtualizarUsuarioTreinoService {

    public static void main(String... args) {
        try {
            String key;
            boolean colaborador = true;
            if (args.length > 0) {
                key = args[0];
                try {
                    colaborador = Boolean.parseBoolean(args[1]);
                } catch (Exception e) {
                }

            } else {
                key = "acadi10megaacademyrs";
            }
            Connection c = new DAO().obterConexaoEspecifica(key);
            Conexao.guardarConexaoForJ2SE(key, c);
            atualizarUsuarios(key, c, colaborador);
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void atualizarUsuarios(String key, Connection c, boolean colaborador) {
        try {
            if (colaborador) {
                SuperFacadeJDBC.executarUpdate("update usuariomovel u set senha = (select senha from usuario where colaborador in (select codigo from colaborador where pessoa = (select pessoa from colaborador   where codigo = u.colaborador)))\n" +
                        " where colaborador > 0 and exists (select codigo from usuario where colaborador in (select codigo from colaborador where pessoa = (select pessoa from colaborador   where codigo = u.colaborador)))", c);
                ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT * FROM usuariomovel where "
                        + (colaborador ? "colaborador is not null" : "cliente is not null"), c);
                List<UsuarioMovelVO> lista = UsuarioMovel.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, c);
                for (UsuarioMovelVO user : lista) {
                    Integer empresa = colaborador ? user.getColaborador().getEmpresa().getCodigo() : user.getCliente().getEmpresa().getCodigo();
                    if (colaborador) {
                        ResultSet rsTpCl = SuperFacadeJDBC.criarConsulta("SELECT * FROM colaborador WHERE codigo = " + user.getColaborador().getCodigo(),c);
                        if (rsTpCl.next()) {
                            user.setColaborador(Colaborador.montarDados(rsTpCl, Uteis.NIVELMONTARDADOS_TODOS, c));
                            empresa = rsTpCl.getInt("empresa");
                        }
                    }
                    ResultSet rsUsuarioEmail = SuperFacadeJDBC.criarConsulta("SELECT * FROM usuarioemail where usuario = " + user.getUsuarioZW(), c);
                    if (rsUsuarioEmail.next()) {
                        user.setUsuarioEmailVO(UsuarioEmail.montarDadosBasico(rsUsuarioEmail));
                        user.getUsuarioEmailVO().setUsuario(user.getUsuarioZW());
                    }
                    SuperFacadeJDBC.executarConsulta("UPDATE usuariomovel SET empresa = " + empresa + " where codigo = " + user.getCodigo(), c);
                    user.setEmpresa(empresa);
                    UsuarioZW usuarioTreino = user.toUsuarioTreino();
                    usuarioTreino.setEmpresaZW(empresa);
                    usuarioTreino.getProfessor().setProfessorTW(
                            user.getColaborador().temTipoColaborador(TipoColaboradorEnum.COORDENADOR.getSigla())
                            || user.getColaborador().temTipoColaborador(TipoColaboradorEnum.PROFESSOR_TREINO.getSigla()));
                    TreinoWSConsumer.sincronizarUsuario(key, usuarioTreino);
                }
            } else {
                SituacaoClienteSinteticoDW sitDao = new SituacaoClienteSinteticoDW(c);
                sitDao.atualizarUsuariosTreino(c);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

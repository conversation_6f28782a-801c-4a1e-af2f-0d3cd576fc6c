package servicos.operacoes;

import negocio.comuns.arquitetura.UsuarioVO;

public class RemetenteTO {

    private Integer usuario;
    private String nome;
    private String email;
    private String foto;

    public RemetenteTO(UsuarioVO remetente) {
        this.usuario = remetente.getCodigo();
        this.nome = remetente.getNome();
        if(remetente.getColaboradorVO() != null &&
                remetente.getColaboradorVO().getPessoa() != null ){

            this.email = remetente.getColaboradorVO().getPessoa().getEmail();
            this.foto = remetente.getColaboradorVO().getPessoa().getUrlFoto();
        }
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

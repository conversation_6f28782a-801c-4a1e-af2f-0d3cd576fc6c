/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.enumeradores.RegraCancelamentoAutomaticoEnum;
import br.com.pactosolucoes.enumeradores.TipoParcelaCancelamento;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.MovParcelaTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class ThreadCancelamentoAutomatico extends Thread {

    private ContratoRecorrenciaVO contratoRecorrencia;
    private Connection con;
    private StringBuffer sb = new StringBuffer();
    private EmpresaVO empresaVO;

    public ThreadCancelamentoAutomatico(ContratoRecorrenciaVO contratoRecorrencia, Connection con) throws Exception {
        this.contratoRecorrencia = contratoRecorrencia;
        this.con = con;
        this.setName("ThreadCancelamentoAuto_" + contratoRecorrencia.getContrato().getCodigo());
    }

    private void logar(String mensagem) {
        Uteis.logar(sb, this.getName() + " - " + mensagem);
    }

    public void obterConfiguracoesEmpresa() throws SQLException{
        if(empresaVO == null || !empresaVO.getCodigo().equals(contratoRecorrencia.getContrato().getEmpresa().getCodigo())) {
            String sqlStr = "select quantidadeParcelasSeguidasCancelamento,tipoParcelaCancelamento,considerarsomenteparcelasplanos from empresa where codigo = " + contratoRecorrencia.getContrato().getEmpresa().getCodigo();
            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlStr);
            while (rs.next()) {
                contratoRecorrencia.getContrato().getEmpresa().setQuantidadeParcelasSeguidasCancelamento(rs.getInt("quantidadeParcelasSeguidasCancelamento"));
                contratoRecorrencia.getContrato().getEmpresa().setTipoParcelaCancelamento(rs.getString("tipoParcelaCancelamento"));
                contratoRecorrencia.getContrato().getEmpresa().setConsiderarSomenteParcelasPlanos(rs.getBoolean("considerarsomenteparcelasplanos"));
            }
            empresaVO = contratoRecorrencia.getContrato().getEmpresa();
        }
    }
    public boolean validarParcelasParaCancelamento() throws SQLException{
        obterConfiguracoesEmpresa();
        int numeroSequenciaVencidas = contratoRecorrencia.getContrato().getEmpresa().getQuantidadeParcelasSeguidasCancelamento();
        if(UteisValidacao.emptyNumber(numeroSequenciaVencidas)){
            boolean retorno = verificarParcelasVencidasAcimaTolerancia();
            if (retorno) {
                Integer tolerancia = contratoRecorrencia.getDiasCancelamentoAutomatico();
                if (contratoRecorrencia.getContrato().getEmpresa().getTipoParcelaCancelamento().equals(TipoParcelaCancelamento.TODAS_PARCELAS.getSigla())) {
                    contratoRecorrencia.getContrato().setDescricaoRegraCancelamento(RegraCancelamentoAutomaticoEnum.PARCELA_VENCIDA_TOLERANCIA_PLANO_TODAS_PARCELAS.getDescricao().replace("XXX", tolerancia.toString()));
                } else {
                    contratoRecorrencia.getContrato().setDescricaoRegraCancelamento(RegraCancelamentoAutomaticoEnum.PARCELA_VENCIDA_TOLERANCIA_PLANO_MAIOR_IGUAL_MES_ATUAL.getDescricao().replace("XXX", tolerancia.toString()));
                }
            }
            return retorno;
        }else{
            boolean retorno = verificarParcelaVencidasSeguidas();
            if (retorno) {
                Integer numeroSequencia = contratoRecorrencia.getContrato().getEmpresa().getQuantidadeParcelasSeguidasCancelamento();
                contratoRecorrencia.getContrato().setDescricaoRegraCancelamento(RegraCancelamentoAutomaticoEnum.PARCELAS_VENCIDAS_SEGUIDAS.getDescricao().replace("XXX", numeroSequencia.toString()));
            }
            return retorno;

        }
    }

    public boolean verificarParcelaVencidasSeguidas() throws SQLException{
        obterConfiguracoesEmpresa();
        int numeroSequenciaVencidas = contratoRecorrencia.getContrato().getEmpresa().getQuantidadeParcelasSeguidasCancelamento();
        int tolerancia = contratoRecorrencia.getDiasCancelamentoAutomatico();
        if(tolerancia > 0 || numeroSequenciaVencidas > 0 ){

            Date hoje = Calendario.hoje();
            Date dataVencimento = Uteis.somarDias(hoje, - (numeroSequenciaVencidas > 0 ? 0 : tolerancia));
            StringBuilder sqlS = new StringBuilder();
            sqlS.append("SELECT mpar.codigo, mpar.dataVencimento FROM movparcela mpar\n");
            sqlS.append("LEFT JOIN movprodutoparcela mpp ON mpar.codigo = mpp.movparcela\n");
            sqlS.append("LEFT JOIN pagamentomovparcela pmp ON mpar.codigo = pmp.movparcela\n");
            sqlS.append("LEFT JOIN movproduto mprod ON mpp.movproduto = mprod.codigo\n");
            sqlS.append("LEFT JOIN produto prod ON prod.codigo = mprod.produto\n");
            sqlS.append("WHERE dataVencimento < '").append(Uteis.getDataFormatoBD(dataVencimento)).append("'\n");
            sqlS.append("AND mpar.situacao = 'EA' AND valorparcela > 0\n");
            sqlS.append("AND mpar.contrato =").append(contratoRecorrencia.getContrato().getCodigo()).append("\n");
            sqlS.append("AND pmp.codigo is null\n");
            sqlS.append("AND prod.tipoproduto IN ('").append(TipoProduto.MES_REFERENCIA_PLANO.getCodigo()).append("','").append(TipoProduto.TAXA_RENEGOCIACAO.getCodigo()).append("')\n");
            sqlS.append("group by mpar.codigo, mpar.dataVencimento order by mpar.dataVencimento asc;");

            Statement stm = con.createStatement();
            ResultSet rs = stm.executeQuery(sqlS.toString());
            List<MovParcelaTO> parcelas = new ArrayList<MovParcelaTO>();
            while (rs.next()) {
                MovParcelaTO movParcelaTO = new MovParcelaTO();
                movParcelaTO.setCodigo(rs.getInt("codigo"));
                movParcelaTO.setDataVencimento(rs.getDate("dataVencimento"));
                parcelas.add(movParcelaTO);
            }
            if (parcelas.size() < numeroSequenciaVencidas) {
                return false;
            }

            Ordenacao.ordenarLista(parcelas, "dataVencimento");

            boolean sequencial = false;
            int count = 0;
            Date parcelaAnterior = null;
            Integer codigoAnterior = null;
            for (MovParcelaTO movParcelaTO : parcelas) {
                if ((parcelaAnterior != null && (Uteis.getDataMesAnoConcatenado(Uteis.somarMeses(movParcelaTO.getDataVencimento(), -1)).equals(Uteis.getDataMesAnoConcatenado(parcelaAnterior)))) ||
                        (codigoAnterior != null && ((movParcelaTO.getCodigo() - 1) == (codigoAnterior)))) {
                    count++;
                    sequencial = true;
                } else {
                    count = 0;
                    sequencial = false;
                }
                parcelaAnterior = movParcelaTO.getDataVencimento();
                codigoAnterior = movParcelaTO.getCodigo();
            }
            return (count > 0 ? count + 1 : 0) > numeroSequenciaVencidas && sequencial;
        }
        return false;
    }
    public boolean verificarParcelasVencidasAcimaTolerancia() throws SQLException {
        int tolerancia = contratoRecorrencia.getDiasCancelamentoAutomatico();
        boolean considerarSomentePlanos = contratoRecorrencia.getContrato().getEmpresa().isConsiderarSomenteParcelasPlanos();
        if (tolerancia > 0) {
            Date hoje = Calendario.hoje();
            Date dataVencimento = Uteis.somarDias(hoje, -tolerancia);

            StringBuilder sb = new StringBuilder();
            sb.append("select exists (select Movparcela.codigo from Movparcela\n");
            if (considerarSomentePlanos) {
                sb.append("LEFT JOIN movprodutoparcela mpp ON mpp.movparcela = Movparcela.codigo\n");
                sb.append("LEFT JOIN movproduto mprod ON mprod.codigo = mpp.movproduto\n");
                sb.append("LEFT JOIN produto prod ON mprod.produto = prod.codigo\n");
            }
            sb.append("where (Movparcela.codigo not in (select movparcela from PagamentoMovParcela)) ");
            sb.append("and (dataVencimento < '").append(Uteis.getDataFormatoBD(dataVencimento)).append("') ");
            sb.append("and (Movparcela.situacao = 'EA') and (valorparcela > 0)");
            sb.append("and (Movparcela.contrato = ").append(contratoRecorrencia.getContrato().getCodigo()).append(") ");
            if (considerarSomentePlanos) {
                sb.append("and prod.tipoproduto = '").append(TipoProduto.MES_REFERENCIA_PLANO.getCodigo()).append("'\n");
            }
            sb.append("order by datavencimento ");
            sb.append("offset 0 limit 1) as existe");

            Statement stm = con.createStatement();
            ResultSet tabelaResultado = stm.executeQuery(sb.toString());

            tabelaResultado.next();
            return tabelaResultado.getBoolean("existe");

        }
        return false;
    }

    @Override
    public void run() {
        boolean deveCancelar = false;
        try {
            deveCancelar = validarParcelasParaCancelamento();
            if (deveCancelar) {
                CancelamentoContratoAutomaticoService cancelamentoAutomatico = new CancelamentoContratoAutomaticoService(con);
                logar("Iniciando cancelamento do contrato " + contratoRecorrencia.getContrato().getCodigo());
                long inicio = System.currentTimeMillis();
                cancelamentoAutomatico.cancelarAutomatico(contratoRecorrencia);
                logar("Cancelamento com sucesso!");
                long fim = System.currentTimeMillis();
                System.out.println("Tempo gasto no cancelamento do contrato " + contratoRecorrencia.getContrato().getCodigo() + " em milisegundos ->" + (fim - inicio));
            }
        } catch (Exception ex) {
            String chave = "";
            try {
                chave = DAO.resolveKeyFromConnection(con);
            } catch (Exception ex1) {
                Logger.getLogger(ThreadCancelamentoAutomatico.class.getName()).log(Level.SEVERE, null, ex1);
            }
            logar("Erro ao cancelar automaticamente o contrato " + contratoRecorrencia.getContrato().getCodigo() + ": " + ex.getMessage());
            UteisValidacao.enfileirarEmail(new MsgTO(contratoRecorrencia.getContrato().getEmpresa().getNome(), "CANCELAMENTO AUTOMÁTICO: Erro ao processar -" + chave, sb));
        }
    }
}

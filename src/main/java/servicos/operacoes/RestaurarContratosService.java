/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 * Esta classe tem o objetivo de recuperar contratos que foram estornados acidentalmente.
 * Ele precisa de um banco de dados original com os códigos dos contratos que foram excluídos.
 * Todos os dados relacionados devem ser previstos aqui.
 * <AUTHOR>
 */
public class RestaurarContratosService {

    private Connection origem;
    private Connection destino;

    public RestaurarContratosService(final Connection origem, final Connection destino) {
        this.origem = origem;
        this.destino = destino;
    }

    private String splitColumnsInsert(int n) {
        String result = "(";
        for (int i = 1; i <= n; i++) {
            result += "?" + (i + 1 <= n ? "," : "");
        }
        return result += ")";
    }

    private String splitColumnsNames(ResultSetMetaData rsmd) throws SQLException {
        String result = "(";
        for (int i = 1; i <= rsmd.getColumnCount(); i++) {
            result += rsmd.getColumnName(i) + (i + 1 <= rsmd.getColumnCount() ? "," : "");
        }
        return result += ")";
    }

    private void restauraTabela(final String nomeTabela, final String join,
            final int codigoContrato,
            final String nomeColunaCodigo)
            throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta(
                String.format("select t.* from %s t %s where %s = %s", new Object[]{
                    nomeTabela,
                    join,
                    nomeColunaCodigo,
                    codigoContrato
                }), origem);

        String columns = splitColumnsNames(rs.getMetaData());
        String params = splitColumnsInsert(rs.getMetaData().getColumnCount());
        int n = rs.getMetaData().getColumnCount();
        try {
            while (rs.next()) {
                PreparedStatement ps = destino.prepareStatement("insert into " + nomeTabela + " " + columns + " values " + params);
                int i = 1;
                for (int j = 1; j <= n; j++) {
                    ps.setObject(i++, rs.getObject(j));
                }
                ps.execute();
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void executar(final int[] contratos) throws Exception {

        for (int ind = 0; ind < contratos.length; ind++) {
            int contrato = contratos[ind];
            try {
                destino.setAutoCommit(false);
                //
                restauraTabela("contrato", "", contrato, "codigo");
                restauraTabela("contratocomposicao", "", contrato, "contrato");
                restauraTabela("contratocondicaopagamento", "", contrato, "contrato");
                restauraTabela("contratoduracao", "", contrato, "contrato");
                restauraTabela("contratohorario", "", contrato, "contrato");
                restauraTabela("contratomodalidade", "", contrato, "contrato");
                restauraTabela("contratomodalidadeturma",
                        "inner join contratomodalidade cm on cm.codigo = t.contratomodalidade "
                                + "inner join contrato c on c.codigo = cm.contrato", contrato, "cm.contrato");
                restauraTabela("contratomodalidadehorarioturma",
                        "inner join contratomodalidadeturma cmt on cmt.codigo = t.contratomodalidadeturma "
                        + "inner join contratomodalidade cm on cm.codigo = cmt.contratomodalidade "
                        + "inner join contrato c on c.codigo = cm.contrato", contrato, "cm.contrato");
                restauraTabela("contratomodalidadeprodutosugerido",
                        "inner join contratomodalidade cm on cm.codigo = t.contratomodalidade "
                        + "inner join contrato c on c.codigo = cm.contrato", contrato, "cm.contrato");
                restauraTabela("contratomodalidadevezessemana",
                        "inner join contratomodalidade cm on cm.codigo = t.contratomodalidade "
                        + "inner join contrato c on c.codigo = cm.contrato", contrato, "cm.contrato");
                restauraTabela("contratooperacao", "", contrato, "contrato");
                restauraTabela("contratoplanoprodutosugerido", "", contrato, "contrato");
                restauraTabela("contratorecorrencia", "", contrato, "contrato");
                restauraTabela("contratotextopadrao", "", contrato, "contrato");
                //
                restauraTabela("historicocontrato", "", contrato, "contrato");
                restauraTabela("trancamentocontrato", "", contrato, "contrato");
                restauraTabela("periodoacessocliente", "", contrato, "contrato");
                restauraTabela("matriculaalunohorarioturma", "", contrato, "contrato");
                //
                restauraTabela("movparcela", "", contrato, "contrato");
                restauraTabela("movproduto", "", contrato, "contrato");
                restauraTabela("movprodutoparcela", "inner join movproduto mp on mp.codigo = t.movproduto", contrato, "mp.contrato");
                restauraTabela("recibopagamento", "", contrato, "contrato");
                restauraTabela("movpagamento", "inner join recibopagamento rp on rp.codigo = t.recibopagamento", contrato, "rp.contrato");
                restauraTabela("pagamentomovparcela", "inner join recibopagamento rp on rp.codigo = t.recibopagamento", contrato, "rp.contrato");
                //
                destino.setAutoCommit(true);
                Uteis.logar(null, "Restaurado contrato N. " + contrato);
            } catch (Exception e) {
                destino.rollback();
                throw e;
            } finally {
                destino.setAutoCommit(true);
            }
        }
    }

    public static void main(String... argss) throws Exception {
        Conexao cOrigem = new Conexao("*****************************************", "postgres", "pactodb");
        Conexao cDestino = new Conexao("**************************************************", "postgres", "pactodb");
        Connection origem = cOrigem.getConexao();
        Connection destino = cDestino.getConexao();
        int[] contratos = {158566,
                174858,
                178419,
                181236,
                184968,
                188326,
                197177,
                207674,
                219292};
        new RestaurarContratosService(origem, destino).executar(contratos);
    }
}

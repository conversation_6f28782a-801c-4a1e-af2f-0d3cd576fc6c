package servicos.operacoes;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoTelefone;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.FamiliarVO;
import negocio.comuns.basico.ParentescoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Categoria;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Familiar;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MentorWebService extends SuperEntidade {

    public MentorWebService() throws Exception {
    }

//    public static void main(String[] args) {
//        try {
//            String chave = null;
//            if (args.length == 0) {
//                args = new String[]{"eclubwellnessdf"};
//            }
//            if (args.length > 0) {
//                chave = args[0];
//                Uteis.logar(null, "Obter conexão para chave: " + chave);
//            }
//            if (UteisValidacao.emptyString(chave)) {
//                throw new Exception("Chave não informada");
//            }
//            Uteis.debug = true;
//
//            Connection con = new DAO().obterConexaoEspecifica(chave);
//            Empresa empresaDAO = new Empresa(con);
//            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            MentorWebService.inicializarMentorWebService(empresaVO, con);
//
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//    }

    public static void inicializarMentorWebService(EmpresaVO empresaVO, Connection connection) throws Exception {

        String token = getToken(empresaVO.getIntegracaoMentorWebUrl(), empresaVO.getIntegracaoMentorWebServico(),
                empresaVO.getIntegracaoMentorWebUser(), empresaVO.getIntegracaoMentorWebPassword());
        if (token.contains("error")){
            Uteis.logar(null, "ERRO AO OBTER TOKEN MENTOR WEB : " + token);
            Logger.getLogger(MentorWebService.class.getName()).log(Level.SEVERE, null, "ERRO AO OBTER TOKEN MENTOR WEB : " + token);

        }
        JSONArray consulta = getConsulta(empresaVO.getIntegracaoMentorWebUrl(), empresaVO.getIntegracaoMentorWebServico(), token);

        ZillyonWebFacade facade = new ZillyonWebFacade(connection);
        ConfiguracaoSistema configuracaoSistema = new ConfiguracaoSistema(connection);
        Colaborador colaboradorDao = new Colaborador(connection);
        Cliente clienteDao = new Cliente(connection);
        Familiar familiarDao = new Familiar(connection);
        Cidade cidadeDao = new Cidade(connection);

        Categoria categoriaDAO = new Categoria(connection);
        Map<String, Integer> categorias = categoriaDAO.categoriasCadastradas();

        ConfiguracaoSistemaVO configuracaoSistemaVO = configuracaoSistema.consultarConfigs(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        ColaboradorVO colaboradorVO = colaboradorDao.consultarPorNomeColaborador("PACTO - M", empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        CidadeVO cidadeVO = cidadeDao.consultarPorNomeCidadeSiglaEstado("BRASILIA", "DF");

        for(int i = 0; i < consulta.length(); i++) {

            try {
                //ALUNO
                PessoaVO alunoVO = criaAluno(consulta.getJSONObject(i));
                adicionaBD(alunoVO, connection, empresaVO, colaboradorVO, categoriaDAO, categorias, consulta.getJSONObject(i),
                        clienteDao, configuracaoSistemaVO, facade, cidadeVO);

                //MAE
                if(!UteisValidacao.emptyString(consulta.getJSONObject(i).optString("MAE_NOME"))) {
                    PessoaVO maeVO = criaMaeOuPai(consulta.getJSONObject(i), "MAE");
                    adicionaBD(maeVO, connection, empresaVO, colaboradorVO, categoriaDAO, categorias, consulta.getJSONObject(i),
                            clienteDao, configuracaoSistemaVO, facade, cidadeVO);
                    adicionarParentesco(alunoVO, maeVO, familiarDao, connection);
                }

                //PAI
                if(!UteisValidacao.emptyString(consulta.getJSONObject(i).optString("PAI_NOME"))) {
                    PessoaVO paiVO = criaMaeOuPai(consulta.getJSONObject(i), "PAI");
                    adicionaBD(paiVO, connection, empresaVO, colaboradorVO, categoriaDAO, categorias, consulta.getJSONObject(i),
                            clienteDao, configuracaoSistemaVO, facade, cidadeVO);
                    adicionarParentesco(alunoVO, paiVO, familiarDao, connection);
                }
            } catch (Exception e) {
                Uteis.logar(e, MentorWebService.class);
            }
        }
    }

    private static void adicionarParentesco(PessoaVO pessoaAlunoVO, PessoaVO pessoaMaePai, Familiar familiarDao, Connection connection) throws Exception {

        ResultSet rsMaePai = SuperFacadeJDBC.criarConsulta("select cliente.codigo, cliente.codacesso " +
                                                                "from pessoa " +
                                                                "inner join cliente on pessoa.codigo = cliente.pessoa " +
                                                                "where pessoa.idexternointegracao = '" + pessoaMaePai.getIdExternoIntegracao() + "'", connection);

        ResultSet rsAluno = SuperFacadeJDBC.criarConsulta("select cliente.codigo " +
                                                                "from pessoa " +
                                                                "inner join cliente on pessoa.codigo = cliente.pessoa " +
                                                                "where pessoa.idexternointegracao = '" + pessoaAlunoVO.getIdExternoIntegracao() + "'", connection);

        Integer codigoClienteResponsavel = 0;
        String codacessoClienteResponsavel = "";
        Integer codigoClienteDependente = 0;

        if(rsMaePai.next()) {
            codigoClienteResponsavel = rsMaePai.getInt("codigo");
            codacessoClienteResponsavel = rsMaePai.getString("codacesso");
        }

        if(rsAluno.next()) {
            codigoClienteDependente = rsAluno.getInt("codigo");
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT COUNT(cliente) " +
                                                            "FROM familiar " +
                                                            "WHERE codacesso = '" + codacessoClienteResponsavel  + "'" + " AND cliente = " + codigoClienteDependente, connection);

        Integer quantidadeParentesco = 0;
        if(rs.next()) {
            quantidadeParentesco = rs.getInt("COUNT");
        }

        if(quantidadeParentesco == 0) {
            FamiliarVO familiarVO = new FamiliarVO();
            familiarVO.setIdentificador((pessoaMaePai.getSexo().equals("M")) ? "PAI" : "MAE");
            familiarVO.setCodAcesso(codacessoClienteResponsavel);
            familiarVO.setNome(pessoaMaePai.getNome());

            ParentescoVO parentescoVO = new ParentescoVO();
            parentescoVO.setCodigo((pessoaMaePai.getSexo().equals("M")) ? 4 : 5);
            parentescoVO.setDescricao((pessoaMaePai.getSexo().equals("M")) ? "PAI" : "MAE");
            familiarVO.setParentesco(parentescoVO);

            familiarVO.setFamiliar(codigoClienteResponsavel);
            familiarVO.setCliente(codigoClienteDependente);

            familiarDao.incluir(familiarVO, true);
        }
    }

    private static String getToken(String url, String servico, String user, String password) throws IOException {
        Map<String, String> header = new HashMap<>();
        header.put("usuario", user);
        header.put("senha", password);

        String result = ExecuteRequestHttpService.executeHttpRequest(url + "rest/servicoexterno/token/" + servico,
                null, header, ExecuteRequestHttpService.METODO_GET, Charsets.UTF_8.name());

        return result;
    }

    private static JSONArray getConsulta(String url, String servico, String token) throws IOException {
        JSONObject body = new JSONObject();
        int anoAtual = Calendar.getInstance().get(Calendar.YEAR);
        body.put("PEL_ANOREF", anoAtual);
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("token", token);

        String result = ExecuteRequestHttpService.executeHttpRequest(url + "rest/servicoexterno/execute/" + servico, body.toString(), header,
                ExecuteRequestHttpService.METODO_POST, Charsets.UTF_8.name());

        return new JSONArray(result).getJSONObject(0).getJSONArray("dados");
    }

    private static PessoaVO criaAluno(JSONObject aluno) throws Exception {

        //Dados pessoais
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setIdExternoIntegracao('A' + aluno.optString("ALUNO_MATRICULA"));
        pessoaVO.setCfp(aluno.optString("ALUNO_CPF"));
        pessoaVO.setNome(aluno.optString("ALUNO_NOME"));
        pessoaVO.setDataNasc(Formatador.formataData(aluno.optString("ALUNO_NASCIMENTO")));
        pessoaVO.setSexo(aluno.optString("ALUNO_SEXO"));

        pessoaVO.setCpfMae(aluno.optString("MAE_CPF"));
        pessoaVO.setRgMae(aluno.optString("MAE_RG"));
        pessoaVO.setNomeMae(aluno.optString("MAE_NOME"));

        pessoaVO.setCpfPai(aluno.optString("PAI_CPF"));
        pessoaVO.setRgPai(aluno.optString("PAI_RG"));
        pessoaVO.setNomePai(aluno.optString("PAI_NOME"));

        //Endereco
        EnderecoVO enderecoVO = new EnderecoVO();
        if(!UteisValidacao.emptyString(aluno.optString("MAE_ENDERECO"))) {
            enderecoVO.setEndereco(aluno.optString("MAE_ENDERECO"));
            enderecoVO.setCep(aluno.optString("MAE_CEP"));
            enderecoVO.setBairro(aluno.optString("MAE_BAIRRO"));
        } else {
            enderecoVO.setEndereco(aluno.optString("PAI_ENDERECO"));
            enderecoVO.setCep(aluno.optString("PAI_CEP"));
            enderecoVO.setBairro(aluno.optString("PAI_BAIRRO"));
        }
        pessoaVO.adicionarObjEnderecoVOs(enderecoVO);

        //Email
        if(!UteisValidacao.emptyString(aluno.optString("ALUNO_EMAIL"))) {
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(aluno.optString("ALUNO_EMAIL"));
            pessoaVO.adicionarObjEmailVOs(emailVO);
        } else if(!UteisValidacao.emptyString(aluno.optString("MAE_EMAIL"))) {
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(aluno.optString("MAE_EMAIL"));
            pessoaVO.adicionarObjEmailVOs(emailVO);
        } else if(!UteisValidacao.emptyString(aluno.optString("PAI_EMAIL"))){
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(aluno.optString("PAI_EMAIL"));
            pessoaVO.adicionarObjEmailVOs(emailVO);
        }

        //Telefone
        if(!UteisValidacao.emptyString(aluno.optString("MAE_CELULAR"))) {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
            telefoneVO.setNumero(Formatador.formataTelefone(aluno.getString("MAE_CELULAR")));
            pessoaVO.adicionarObjTelefoneVOs(telefoneVO);
        } else if(!UteisValidacao.emptyString(aluno.optString("PAI_CELULAR"))){
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
            telefoneVO.setNumero(Formatador.formataTelefone(aluno.getString("PAI_CELULAR")));
            pessoaVO.adicionarObjTelefoneVOs(telefoneVO);
        }

        pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

        return pessoaVO;
    }

    private static PessoaVO criaMaeOuPai(JSONObject pessoa, String tipoPessoa) throws Exception {

        //Dados pessoais
        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setIdExternoIntegracao(tipoPessoa.substring(0, 1) + pessoa.optInt(tipoPessoa + "_ID"));
        pessoaVO.setCfp(pessoa.optString(tipoPessoa + "_CPF"));
        pessoaVO.setRg(pessoa.optString(tipoPessoa + "_RG"));
        pessoaVO.setNome(pessoa.optString(tipoPessoa + "_NOME"));
        pessoaVO.setDataNasc(Formatador.formataData(pessoa.optString(tipoPessoa + "_NASCIMENTO")));

        if(tipoPessoa.substring(0, 1).equals("M")) {
            pessoaVO.setSexo("F");
        } else {
            pessoaVO.setSexo("M");
        }

        //Endereco
        EnderecoVO enderecoVO = new EnderecoVO();
        enderecoVO.setEndereco(pessoa.optString(tipoPessoa + "_ENDERECO"));
        enderecoVO.setCep(pessoa.optString(tipoPessoa + "_CEP"));
        enderecoVO.setBairro(pessoa.optString(tipoPessoa + "_BAIRRO"));
        pessoaVO.adicionarObjEnderecoVOs(enderecoVO);

        //Email
        if(!UteisValidacao.emptyString(pessoa.optString(tipoPessoa + "_EMAIL"))) {
            EmailVO emailVO = new EmailVO();
            emailVO.setEmail(pessoa.optString(tipoPessoa + "_EMAIL"));
            pessoaVO.adicionarObjEmailVOs(emailVO);
        }

        //Telefone
        if(!UteisValidacao.emptyString(pessoa.optString(tipoPessoa + "_CELULAR"))) {
            TelefoneVO telefoneVO = new TelefoneVO();
            telefoneVO.setTipoTelefone(TipoTelefone.CELULAR.getCodigo());
            telefoneVO.setNumero(Formatador.formataTelefone(pessoa.getString(tipoPessoa + "_CELULAR")));
            pessoaVO.adicionarObjTelefoneVOs(telefoneVO);
        }

        pessoaVO.setTipoPessoa(TipoPessoaEnum.ALUNO.getTipo());

        return pessoaVO;
    }

    private static ClienteVO criaCliente(PessoaVO pessoaVO, EmpresaVO empresaVO) {
        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setPessoa(pessoaVO);
        clienteVO.setEmpresa(empresaVO);
        clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
        clienteVO.setMatricula("");

        return clienteVO;
    }

    private static void adicionaBD(PessoaVO pessoaVO, Connection connection, EmpresaVO empresaVO, ColaboradorVO colaboradorVO, Categoria categoriaDAO, Map<String,
            Integer> categorias, JSONObject jsonObject, Cliente clienteDao, ConfiguracaoSistemaVO configuracaoSistemaVO, ZillyonWebFacade facade, CidadeVO cidadeVO) throws Exception {

        ResultSet resultSet = SuperFacadeJDBC.criarConsulta("SELECT pessoa.codigo as pessoacodigo, cliente.categoria clientecategoria, cliente.codigo as clientecodigo " +
                                                                "FROM pessoa " +
                                                                "INNER JOIN cliente ON pessoa.codigo = cliente.pessoa " +
                                                                "WHERE pessoa.idExternoIntegracao = '" + pessoaVO.getIdExternoIntegracao() + "'", connection);

        Integer pessoacodigo = 0;
        Integer clientecategoria = 0;
        Integer clientecodigo = 0;

        if(resultSet.next()) {
            pessoacodigo = resultSet.getInt("pessoacodigo");
            clientecategoria = resultSet.getInt("clientecategoria");
            clientecodigo = resultSet.getInt("clientecodigo");
        }

        pessoaVO.setCidade(cidadeVO);
        pessoaVO.setEstadoVO(cidadeVO.getEstado());
        pessoaVO.setPais(cidadeVO.getPais());

        ClienteVO clienteVO = criaCliente(pessoaVO, empresaVO);
        criaVinculo(colaboradorVO, clienteVO);

        int codigoCategoria = 0;
        String tipoPessoa = pessoaVO.getIdExternoIntegracao().substring(0, 1);
        if(tipoPessoa.equals("M") || tipoPessoa.equals("P")) {
            if(!categorias.containsKey("RESPONSAVEL")) {
                categorias.put("RESPONSAVEL", categoriaDAO.criarOuConsultarCategoriaPorNome("RESPONSAVEL", Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
            }
            codigoCategoria = categorias.get("RESPONSAVEL");
        } else {
            String situacaoMatricula = jsonObject.optString("SITUACAO_MATRICULA");
            if(situacaoMatricula.equals("ATIVO")) {
                if(!categorias.containsKey(jsonObject.optString("TURMA"))) {
                    categorias.put(jsonObject.optString("TURMA"), categoriaDAO.criarOuConsultarCategoriaPorNome(jsonObject.optString("TURMA"),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
                }
                codigoCategoria = categorias.get(jsonObject.optString("TURMA"));
            } else {
                if(!categorias.containsKey(situacaoMatricula)) {
                    categorias.put(situacaoMatricula, categoriaDAO.criarOuConsultarCategoriaPorNome(situacaoMatricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS).getCodigo());
                }
                codigoCategoria = categorias.get(situacaoMatricula);
            }
        }
        clienteVO.getCategoria().setCodigo(codigoCategoria);

        if(UteisValidacao.emptyNumber(pessoacodigo)) {
            clienteDao.gerarNumeroMatricula(clienteVO, empresaVO, configuracaoSistemaVO);
            clienteDao.atualizarMatriculaAluno(clienteVO.getCodigoMatricula());
            clienteDao.incluirClienteSimplificadoImportacao(clienteVO);
            facade.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
        } else {
            clienteVO.setCodigo(clientecodigo);
            clienteVO.getPessoa().setCodigo(pessoacodigo);

            if(clientecategoria != clienteVO.getCategoria().getCodigo() && (!tipoPessoa.equals("M") && !tipoPessoa.equals("P"))) {
                String updateCategoria = "UPDATE cliente SET categoria = " + clienteVO.getCategoria().getCodigo() + " WHERE pessoa = " + pessoacodigo;
                SuperFacadeJDBC.executarUpdate(updateCategoria, connection);
            }
        }
    }

    private static void criaVinculo(ColaboradorVO consultor, ClienteVO clienteVO) {
        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setColaborador(consultor);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
        vinculoVO.setCliente(clienteVO);
        clienteVO.getVinculoVOs().add(vinculoVO);
    }
}

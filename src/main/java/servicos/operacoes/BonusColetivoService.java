/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.RoboControle;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.PeriodoMensal;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.OperacaoColetiva;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class BonusColetivoService extends SuperEntidade {

    public BonusColetivoService() throws Exception {
    }

    public void adicionarBonusColetivo(final Connection con, OperacaoColetivaVO operacao, PrintWriter out) throws Exception {
        Date dataOriginal = Calendario.hoje();
        if (JSFUtilities.isJSFContext()) {
            JSFUtilities.storeOnSession("dataSistema", operacao.getDataInicio());
        } else {
            Calendario.dia = operacao.getDataInicio();
        }
        try {
            int nrDias = (int) Uteis.nrDiasEntreDatas(operacao.getDataInicio(), operacao.getDataFinal()) + 1;
            gravar(con, operacao.getEmpresa().getCodigo(), nrDias, "BC", operacao.getObservacao(), operacao.getPlanoVO().getCodigo(), operacao.getUsuario(), operacao.getTipo(), out,operacao.getDataFinal(), operacao.getIdadeMinima(), operacao.getIdadeMaxima(), dataOriginal);
            operacao.setStatus(StatusOperacaoColetivaEnum.PROCESSADA);
            operacao.setDataprocessamento(dataOriginal);
            operacao.setResultado("Contrato vigentes na data de inicio do recesso, tiveram afastamento");
            if(Calendario.menor(operacao.getDataInicio(), operacao.getDataprocessamento())){
                operacao.setResultado(operacao.getResultado()+", exceto contratos que foram renovados e tiveram operação de contrato na renovação");
            }
        } catch (Exception e) {
            con.rollback();
            operacao.setStatus(StatusOperacaoColetivaEnum.ERRO);
            operacao.setResultado(Uteis.getData(dataOriginal) + " - Erro ao processar operacao:" + e.getMessage());
        } finally {
            con.setAutoCommit(true);
        }
        if (JSFUtilities.isJSFContext()) {
            JSFUtilities.storeOnSession("dataSistema", dataOriginal);
        } else {
            Calendario.dia = dataOriginal;
        }
        OperacaoColetiva operacaoDAO = new OperacaoColetiva(con);
        operacaoDAO.altetarSemCommit(operacao);
        operacaoDAO = null;
    }

    public void gravar(final Connection con,
                       final int empresa, final int nrDias,
                       final String tipo,
                       final String observacao,
                       final Integer codPlano,
                       final String usuarioOAMD,
                       PrintWriter out, Date dataFinal) throws Exception {
        gravar(con, empresa, nrDias, tipo, observacao, codPlano, null, TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO, out, dataFinal, null, null,null);
    }

    public void gravar(final Connection con,
                       final int empresa, final int nrDias,
                       final String tipo,
                       final String observacao,
                       final Integer codPlano,
                       final String usuarioOAMD,
                       final TipoOperacaoColetivaEnum tipoOperacaoColetivaEnum,
                       PrintWriter out, Date datafinal, Integer idadeMinima, Integer idadeMaxima, Date dataOriginalProcessamento) throws Exception {
        try {
            BonusContratoVO bonusContratoVO = new BonusContratoVO();
            bonusContratoVO.setEmpresa(empresa);
            bonusContratoVO.getResponsavelOperacao().setCodigo(1);
            bonusContratoVO.getResponsavelOperacao().setUsername("ADMIN");

            /**
             * Esta é a consulta que elege quais são os contratos são candidatos ao bonus coletivo!
             */
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT c.* FROM contrato c \n");
            if(idadeMinima != null && UteisValidacao.notEmptyNumber(idadeMaxima)){
                sql.append(" inner join pessoa pes on pes.codigo = c.pessoa \n" );
            }
            sql.append("WHERE c.situacao  in ('AT', 'IN') AND empresa = ").append(bonusContratoVO.getEmpresa()).append("\n");
            sql.append("AND vigenciaDe <= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
            sql.append("AND vigenciaAteAjustada >= '").append(Uteis.getDataJDBC(Calendario.hoje())).append("'\n");
            if (!UteisValidacao.emptyNumber(codPlano)) {
                sql.append("AND plano = ").append(codPlano).append("\n");
            }
            if(idadeMinima != null && UteisValidacao.notEmptyNumber(idadeMaxima)){
                sql.append(" AND extract(year from age('").append(Uteis.getDataJDBC(Calendario.hoje())).append("', pes.datanasc)) between ").append(idadeMinima).append(" and ").append(idadeMaxima).append(" \n" );
            }
            sql.append(" AND not exists (select codigo from contratooperacao ope where contrato = c.codigo  and (tipooperacao = 'CA' or (tipooperacao = 'TR' and datainicioefetivacaooperacao::date >= '");
            sql.append(Uteis.getDataFormatoBD(Calendario.hoje())).append("' and not exists (select codigo from contratooperacao where contrato = ope.contrato and tipooperacao ='RT' and datainicioefetivacaooperacao > ope.datainicioefetivacaooperacao ))  or (dataoperacao::date = '").append(Uteis.getDataFormatoBD(Calendario.hoje())).append("' and tipooperacao = 'BC'))) ");
            sql.append(" AND not exists (select codigo from contratooperacao  where contrato = c.contratoresponsavelrenovacaomatricula  and tipooperacao <> 'AC') ");// renovação teve operações, para lançar operações no contrato atual, renovação não pode ter operações.
            sql.append(" ORDER BY codigo");
            ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);
            List<ContratoVO> contratoVOs = zwDAO.getContrato().consultar(sql.toString(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            EmpresaVO empresaVO =  zwDAO.getEmpresa().consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            JustificativaOperacaoVO j = new JustificativaOperacaoVO();
            j.setEmpresa(empresaVO);
            j.setDescricao("AFASTAMENTO COLETIVO");
            j.setTipoOperacao("BO");
            j = zwDAO.getJustificativaOperacao().criarOuConsultarSeExistePorNome(j);
            for (ContratoVO contratoVO : contratoVOs) {

                    try {
                        con.setAutoCommit(false);
                        //validar se o contrato possui um trancamento sem retorno
                        if (contratoVO.getSituacao().equals("IN")) {
                            contratoVO.setSituacao("AT");
                            executarUpdate("update contrato set situacao = 'AT' where codigo = " + contratoVO.getCodigo(), con);
                            executarUpdate("delete from historicoContrato where contrato =  " + contratoVO.getCodigo() + " and tipohistorico in ('AV', 'VE', 'DE')", con);
                            //contratos que tiveram operações após vencimento do contrato e essas operações não alteram a vigencia do contrato
                            // devem ser estornadas para evitar exclusões dela afetem os dados
                            executarUpdate("delete from contratooperacao where contrato = " + contratoVO.getCodigo() + " and dataoperacao > (select vigenciaateajustada from contrato where codigo = " + contratoVO.getCodigo() + " )", con);
                            executarUpdate("delete from periodoacessocliente where contrato = " + contratoVO.getCodigo() + " and datainicioacesso > (select vigenciaateajustada from contrato where codigo = " + contratoVO.getCodigo() + " )", con);
                        }
                        contratoVO.setEmpresa(empresaVO);
                        bonusContratoVO.setTipoJustificativa(j.getCodigo());
                        bonusContratoVO.setAcrescentarDiaContrato(tipo);
                        List<PeriodoMensal> periodos = zwDAO.getContratoOperacao().obterPeriodosParaAfastamentoColetivo(contratoVO.getCodigo(), Calendario.hoje(), datafinal);
                        for(PeriodoMensal periodo: periodos){
                            bonusContratoVO.setPeriodoOperacao(periodo);

                            bonusContratoVO.setContratoVO(contratoVO);
                            int nrDiasPeriodo = (int) Uteis.nrDiasEntreDatas(periodo.getDataInicio(),periodo.getDataTermino()) + 1;
                            bonusContratoVO.setNrDias(nrDiasPeriodo);
                            bonusContratoVO.obterDataInicioTermino();
                            bonusContratoVO.setObservacao(observacao == null ? "Operação de Afastamento devido ao recesso da Academia." : observacao);
                            if(nrDias > nrDiasPeriodo) {
                                bonusContratoVO.setObservacao(bonusContratoVO.getObservacao() + ". *Contrato já tinha dia(s) de afastamento dentro do período dessa operacao. Portanto só recebeu " + (nrDiasPeriodo) + " dia(s)");
                            }
                            if (contratoVO.isVendaCreditoTreino()) {
                                bonusContratoVO.getContratoVO().setContratoDuracao(getFacade().getContratoDuracao().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                                bonusContratoVO.getContratoVO().getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(contratoVO.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                            }
                            zwDAO.getContratoOperacao().incluirOperacaoBonus(bonusContratoVO, false, null, false, dataOriginalProcessamento);

                        }

                        if (TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS.equals(tipoOperacaoColetivaEnum) ||
                                TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS_SOMENTE_PLANOS.equals(tipoOperacaoColetivaEnum)) {
                            adicionarDiasMovParcelas(contratoVO, nrDias, tipoOperacaoColetivaEnum, con, Calendario.hoje());
                        }
                        con.commit();
                        print(out, "Incluído afastamento de " + nrDias + " dias para o contrato " + contratoVO.getCodigo());
                    } catch (Exception e) {
                        con.rollback();
                        throw e;
                    }
            }
        }catch (Exception e) {
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private void removerDiasMovParcelas(ContratoVO contratoVo, int nrDias, TipoOperacaoColetivaEnum operacao, final Connection connection ,Date dataLimiteVencimento) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.alterarDiasVencimentoMovParcelas(contratoVo, nrDias, "-", TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS_SOMENTE_PLANOS.equals(operacao), dataLimiteVencimento);
        movParcelaDAO = null;
    }

    private void adicionarDiasMovParcelas(ContratoVO contratoVo, int nrDias, TipoOperacaoColetivaEnum operacao, final Connection connection, Date dataLimiteVencimento) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.alterarDiasVencimentoMovParcelas(contratoVo, nrDias, "+", TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS_SOMENTE_PLANOS.equals(operacao), dataLimiteVencimento);
        movParcelaDAO = null;
    }


    private void print(PrintWriter printer, final String msg) {
        final String tmp = msg.contains("<td>") ? "" : "</br>";
        if (printer != null) {
            printer.println(msg + tmp);
        } else {
            Uteis.logar(null, msg);
        }
    }

    public static void main(String... args) {
        try {
            /*
             * arg0 - chave da empresa
             * arg1 - codigo da empresa
             * arg2 - número de dias
             * arg3 - data base do lançamento (forçar data de lançamento
             * arg4 - qual o  tipo do bonus: acrescimo ou decrescimo
             * arg5 - Observação (opcional)
             * arg6 - código do plano
             */
            Connection c = new DAO().obterConexaoEspecifica(args[0]);
            Conexao.guardarConexaoForJ2SE(c);
            Calendario.dia = new SimpleDateFormat("yyyyMMdd").parse(args[3]);
            //Conexao,codigoEmpresa,nrDias,tipo(Acrescimo/Retirada)
            Integer codPlano = (args.length > 6) ? Integer.valueOf(args[6]) : null;
            Date datafinal = Uteis.somarDias(Calendario.hoje(), Integer.valueOf(args[2]));
            new BonusColetivoService().gravar(c, Integer.valueOf(args[1]),
                    Integer.valueOf(args[2]), args[4], args[5], codPlano, "", null, datafinal);
//            Calendario.dia = new SimpleDateFormat("yyyyMMdd").parse("20130209");
//            new BonusColetivoService().gravar(FacadeManager.getFacade().getRisco().getCon(), 1,
//                    7, "RE", null);
        } catch (Exception ex) {
            Logger.getLogger(ConfiguracaoSistemaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void excluirBonusColetivo (Connection con, OperacaoColetivaVO operacaoColetivaVO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from contrato c \n");
        if(operacaoColetivaVO.getIdadeMinima() != null && UteisValidacao.notEmptyNumber(operacaoColetivaVO.getIdadeMaxima())){
            sql.append(" inner join pessoa pes on pes.codigo = c.pessoa \n" );
        }
        sql.append(" where c.codigo in (SELECT distinct c.codigo FROM contrato c inner join contratooperacao co on c.codigo = co.contrato \n");
        sql.append(" WHERE situacao  in ('AT', 'IN') AND empresa = ").append(operacaoColetivaVO.getEmpresa().getCodigo()).append("\n");
        if (!UteisValidacao.emptyNumber(operacaoColetivaVO.getPlanoVO().getCodigo())) {
            sql.append(" AND plano = ").append(operacaoColetivaVO.getPlanoVO().getCodigo()).append("\n");
        }
        sql.append(" AND dataoperacao::date = '").append(Uteis.getDataFormatoBD(operacaoColetivaVO.getDataInicio())).append("'\n");
        sql.append(" AND tipooperacao = 'BC') ");
        if(operacaoColetivaVO.getIdadeMinima() != null && UteisValidacao.notEmptyNumber(operacaoColetivaVO.getIdadeMaxima())){
            sql.append(" AND extract(year from age('").append(Uteis.getDataJDBC(Calendario.hoje())).append("', pes.datanasc)) between ").append(operacaoColetivaVO.getIdadeMinima()).append(" and ").append(operacaoColetivaVO.getIdadeMaxima()).append(" \n" );
        }
        sql.append(" AND not exists (select codigo from contratooperacao  where contrato = c.contratoresponsavelrenovacaomatricula  and tipooperacao <> 'AC') ");// renovação teve operações, para lançar remover operações no contrato atual, renovação não pode ter operações.
        sql.append("ORDER BY c.codigo");
        ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);
        List<ContratoVO> contratoVOs = zwDAO.getContrato().consultar(sql.toString(), Uteis.NIVELMONTARDADOS_COM_CLIENTE);
        int nrDias = (int) Uteis.nrDiasEntreDatas(operacaoColetivaVO.getDataInicio(), operacaoColetivaVO.getDataFinal())  + 1;
        BonusContratoVO bonusContratoVO = new BonusContratoVO();
        bonusContratoVO.setEmpresa(operacaoColetivaVO.getEmpresa().getCodigo());
        bonusContratoVO.getResponsavelOperacao().setCodigo(1);
        bonusContratoVO.getResponsavelOperacao().setUsername("ADMIN");
        bonusContratoVO.setNrDias(nrDias);
        EmpresaVO empresaVO =  zwDAO.getEmpresa().consultarPorChavePrimaria(operacaoColetivaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        String observacao = "EXCLUSAO OPERACAO COLETIVA "+ operacaoColetivaVO.getCodigo();
        Date dataAtual = Calendario.hoje();
        JustificativaOperacaoVO j = new JustificativaOperacaoVO();
        j.setEmpresa(empresaVO);
        j.setDescricao("AFASTAMENTO COLETIVO");
        j.setTipoOperacao("BO");
        j = zwDAO.getJustificativaOperacao().criarOuConsultarSeExistePorNome(j);
        try {
            StringBuilder contratosProcessar = new StringBuilder();
            StringBuilder matriculasProcessar = new StringBuilder();
            for (ContratoVO contratoVO : contratoVOs) {
                try {
                    con.setAutoCommit(false);
                    if (JSFUtilities.isJSFContext()) {
                        JSFUtilities.storeOnSession("dataSistema", operacaoColetivaVO.getDataInicio());
                    } else {
                        Calendario.dia = operacaoColetivaVO.getDataInicio();
                    }

                    //validar se o contrato possui um trancamento sem retorno
                    List listaOperacoes = zwDAO.getContratoOperacao().consultarAfastamentoColetivoPorContrato(contratoVO.getCodigo(),operacaoColetivaVO.getDataInicio(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA );
                    Iterator<ContratoOperacaoVO> i = listaOperacoes .iterator();
                    int nrDiasAfastamento = 0;
                    while (i.hasNext()) {
                        ContratoOperacaoVO obj = i.next();
                        nrDiasAfastamento += (int) Uteis.nrDiasEntreDatas(obj.getDataInicioEfetivacaoOperacao(), obj.getDataFimEfetivacaoOperacao()) + 1;
                    }
                    contratoVO.setEmpresa(empresaVO);
                    bonusContratoVO.setTipoJustificativa(j.getCodigo());
                    bonusContratoVO.setContratoVO(contratoVO);
                    bonusContratoVO.setAcrescentarDiaContrato("BX");
                    bonusContratoVO.setObservacao(observacao);
                    bonusContratoVO.setNrDias(nrDiasAfastamento);
                    bonusContratoVO.obterDataInicioTermino();
                    if (contratoVO.getSituacao().equals("IN")) {
                        SuperFacadeJDBC.executarConsulta("delete from historicocontrato where contrato = " + contratoVO.getCodigo() + " and tipohistorico in ('VE', 'DE')", con);
                        SuperFacadeJDBC.executarConsulta("update contrato set situacao = 'AT' where codigo = " + contratoVO.getCodigo(), con);
                    }

                    if (contratoVO.isVendaCreditoTreino()) {
                        contratoVO.setContratoDuracao(getFacade().getContratoDuracao().consultarPorContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                        contratoVO.getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(contratoVO.getContratoDuracao().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    }
                    zwDAO.getContratoOperacao().incluirOperacaoBonus(bonusContratoVO, true, null, false, dataAtual);

                    if (TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS.equals(operacaoColetivaVO.getTipo()) ||
                            TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS_SOMENTE_PLANOS.equals(operacaoColetivaVO.getTipo())) {
                        removerDiasMovParcelas(contratoVO, nrDias, operacaoColetivaVO.getTipo(), con, operacaoColetivaVO.getDataInicio());
                    }

                    if (JSFUtilities.isJSFContext()) {
                        JSFUtilities.storeOnSession("dataSistema", dataAtual);
                    } else {
                        Calendario.dia = dataAtual;
                    }
                    if (Calendario.menor(bonusContratoVO.getContratoVO().getVigenciaAteAjustada(), dataAtual)) {
                        contratosProcessar.append(",").append(contratoVO.getCodigo().toString());
                        matriculasProcessar.append(",").append(contratoVO.getCliente().getMatricula());
                    }

                    SuperFacadeJDBC.executarConsulta("delete from contratooperacao where contrato = " + contratoVO.getCodigo() + " and tipooperacao = 'BC' and  dataoperacao ='" + Uteis.getDataFormatoBD(operacaoColetivaVO.getDataInicio()) + "'", con);
                    SuperFacadeJDBC.executarConsulta("delete from contratooperacao where contrato = " + contratoVO.getCodigo() + " and tipooperacao = 'BX' and  observacao like '" + observacao + "'", con);
//                    zwDAO.atualizarSintetico(zwDAO.getCliente().consultarPorCodigoPessoa(bonusContratoVO.getContratoVO().getPessoa().getCodigo(),
//                            Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW),
//                            Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
                    con.commit();
                } catch (Exception e) {
                    con.rollback();
                    throw e;
                } finally {
                    con.setAutoCommit(true);
                }
            }

            try {
                if (contratosProcessar.length() > 0) {
                    contratosProcessar.deleteCharAt(0);
                    matriculasProcessar.deleteCharAt(0);
                    processarRoboContratos(contratosProcessar.toString(), matriculasProcessar.toString());
                }
            } catch (Exception ex) {
                Uteis.logar(ex, BonusColetivoService.class);
                throw ex;
            }
            operacaoColetivaVO.setStatus(StatusOperacaoColetivaEnum.EXCLUIDA);
            operacaoColetivaVO.setResultado("Bonus Coletivo foi excluído no dia "+ Uteis.getData(Calendario.hoje())+ " pelo usuario "+operacaoColetivaVO.getUsuarioExclusao());
        } catch (Exception e) {
            operacaoColetivaVO.setResultado("Erro na tentativa de excluir: "+ e.getMessage());
        }finally {
            zwDAO = null;
        }
        OperacaoColetiva operacaoDAO = new OperacaoColetiva(con);
        operacaoDAO.altetarSemCommit(operacaoColetivaVO);
        operacaoDAO = null;

    }

    public static void processarRoboContratos(String contratos, String matriculas) throws Exception {
        RoboControle robo = new RoboControle();
        robo.setContratos(contratos);
        robo.setApagarHistorioContratoAntes(true);
        robo.setMatriculas(matriculas);
        robo.getRobo().setProcessarValidador(true); // isso define que o processamento do robo é soliciatado pelo verificador de inconsistencias
        robo.processarLote();
    }

    public void ajustarContratoFuturosOperacaoProcessada(Connection con) throws Exception {
        OperacaoColetiva operacaoDAO = new OperacaoColetiva(con);
        String sql = "select * from operacaocoletiva  where tipo in (2,3) and dataprocessamento  < '2020-04-25' and status = 2 and resultado not like '%parcelasfuturasAjustadas%' ORDER BY codigo";
        List<OperacaoColetivaVO> operacoes = operacaoDAO.consultar(sql, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (OperacaoColetivaVO operacaoColetivaVO : operacoes) {
            int nrDias = (int) Uteis.nrDiasEntreDatas(operacaoColetivaVO.getDataInicio(), operacaoColetivaVO.getDataFinal()) + 1;
            StringBuilder sqlContratos = new StringBuilder();
            sqlContratos.append("select * from contrato where codigo in (select contratoresponsavelrenovacaomatricula  from contrato where codigo in (SELECT distinct c.codigo FROM contrato c inner join contratooperacao co on c.codigo = co.contrato \n");
            sqlContratos.append(" WHERE situacao  in ('AT', 'IN') AND empresa = ").append(operacaoColetivaVO.getEmpresa().getCodigo()).append("\n");
            sqlContratos.append(" AND dataoperacao::date = '").append(Uteis.getDataFormatoBD(operacaoColetivaVO.getDataInicio())).append("'\n");
            sqlContratos.append(" AND tipooperacao = 'BC') and contratoresponsavelrenovacaomatricula  > 0) ORDER BY codigo");
            ZillyonWebFacade zwDAO = new ZillyonWebFacade(con);
            List<ContratoVO> contratoVOs = zwDAO.getContrato().consultar(sqlContratos.toString(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            try {
                con.setAutoCommit(false);
                for (ContratoVO contratoVO : contratoVOs) {
                    if (TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS.equals(operacaoColetivaVO.getTipo()) ||
                            TipoOperacaoColetivaEnum.AFASTAMENTO_COLETIVO_ALTERANDO_PARCELAS_SOMENTE_PLANOS.equals(operacaoColetivaVO.getTipo())) {
                        adicionarDiasMovParcelas(contratoVO, nrDias, operacaoColetivaVO.getTipo(), con, operacaoColetivaVO.getDataInicio());
                    }
                }
                con.commit();
            } catch (Exception e) {
                con.rollback();
                operacaoColetivaVO.setResultado( operacaoColetivaVO.getResultado() + "- Erro ajustando Parcelas: "+ e.getMessage());
            }finally {
                con.setAutoCommit(true);
                operacaoColetivaVO.setResultado( operacaoColetivaVO.getResultado() + "- #parcelasfuturasAjustadas");
                zwDAO = null;
            }
            operacaoDAO.altetarSemCommit(operacaoColetivaVO);
        }
        operacaoDAO = null;


    }


}

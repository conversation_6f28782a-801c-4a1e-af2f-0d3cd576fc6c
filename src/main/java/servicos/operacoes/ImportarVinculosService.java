/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 *
 * <AUTHOR>
 */
public class ImportarVinculosService extends SuperEntidade {

    public ImportarVinculosService() throws Exception {
    }

    public ImportarVinculosService(Connection conexao) throws Exception {
        super(conexao);
    }

    public static void main(String... args) {
        final String key = args.length > 0 ? args[0] : "2affa34296b5bf0267cccfe64f7e5119";
        final String arq = args.length > 1 ? args[1] : "C:\\Users\\<USER>\\Documents\\BrenoConsultor.xlsx";
        final String tipo = args.length > 2 ? args[2] : "CO";
        try {
            Connection c = new DAO().obterConexaoEspecifica(key);
            XSSFWorkbook wb = new XSSFWorkbook(new FileInputStream(arq));
            XSSFSheet ws = wb.getSheetAt(0);

            int rowNum = ws.getLastRowNum() + 1;

            FORPRINCIPAL:
            for (int i = 1; i < rowNum; i++) {
                XSSFRow row = ws.getRow(i);
                String idExterno = "";
                String colaborador = "";

                try {
                    try {
                        idExterno = new BigDecimal(row.getCell(0).getNumericCellValue()).toString();
                    } catch (Exception e) {
                        idExterno = row.getCell(0).getStringCellValue();
                    }
                    try {
                        colaborador = new BigDecimal(row.getCell(1).getNumericCellValue()).toString();
                    } catch (Exception e) {
                        colaborador = row.getCell(1).getStringCellValue();
                    }
                    //
                    if (!UteisValidacao.emptyString(idExterno) && !UteisValidacao.emptyString(colaborador)) {
                        boolean existe;
                        boolean alterarConsultor = false;
                        if (tipo.equals("CO")) {
                            existe = SuperFacadeJDBC.existe(String.format("select c.codigo from vinculo v "
                                    + "inner join cliente c on v.cliente = c.codigo "
                                    + "where c.codigomatricula = %s and v.tipovinculo = '%s'", idExterno, tipo), c);
                            alterarConsultor = true;
                        } else {
                            existe = SuperFacadeJDBC.existe(String.format("select c.codigo from vinculo v "
                                    + "inner join cliente c on v.cliente = c.codigo "
                                    + "where v.colaborador = %s and c.codigomatricula = %s and v.tipovinculo = '%s'", colaborador, idExterno, tipo), c);
                        }
                        //
                        try {
                            if (alterarConsultor) {
                                final String update = String.format("update vinculo set colaborador = %s "
                                        + "where tipovinculo = '%s' and cliente = ( "
                                        + "select codigo from cliente where codigomatricula = %s)", colaborador, tipo, idExterno);
                                SuperFacadeJDBC.executarConsulta(update, c);
                                Uteis.logar(null, String.format("Alterei Vinculo => Matricula: %s, Colaborador: %s, Tipo: %s",
                                        idExterno, colaborador, tipo));
                            } else if (!existe) {
                                final String insert = String.format("insert into vinculo(colaborador,tipovinculo,cliente) "
                                        + "select %s,'%s',codigo from cliente where codigomatricula = %s ", colaborador, tipo, idExterno);
                                SuperFacadeJDBC.executarConsulta(insert, c);
                                Uteis.logar(null, String.format("Inseriu Vinculo => Matricula: %s, Colaborador: %s, Tipo: %s",
                                        idExterno, colaborador, tipo));
                            } else {
                                Uteis.logar(null, String.format("Registro Ignorado. Já existe vinculo \"%s\" para o aluno matricula %s e colaborador %s",
                                        tipo, idExterno, colaborador));
                            }
                        } catch (Exception e) {
                            Uteis.logar(null, String.format("Registro Não Inserido Ignorado na Linha: %s, devido ao erro: %s",
                                    String.valueOf(i + 1), e.getMessage()));
                        }
                    }
                } catch (Exception e) {
                    Uteis.logar(null, String.format("Registro Ignorado na Linha: %s, devido ao erro: %s",
                            String.valueOf(i + 1), e.getMessage()));
                    continue FORPRINCIPAL;
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(ImportarVinculosService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}

package servicos.operacoes;

import org.apache.commons.collections4.ListUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MailingItensController {

    private int nrLimite;
    private List<MailingItem> items;
    private int registroAfetados = 0;
    public MailingItensController(int nrLimite) {
        this.nrLimite = nrLimite;
    }

    public List<MailingItem> getItems() {
        if( null == items){items = new ArrayList<MailingItem>();}
        return items;
    }

    public List<MailingItem> getItemsAguardando() {
        List<MailingItem> mailingItems = new ArrayList<MailingItem>();
        for(MailingItem mi : getItems()){
            if(mi.operacao.equals(MailingItem.OP.AG)) {
                mailingItems.add(mi);
            }
        }
        return mailingItems;
    }


    public void add(int codigo, String value, int bounced) {
        ++registroAfetados;
        getItems().add(new MailingItem(codigo, value, bounced));
    }

    public void addExcluido(int codigo, int erro, int bounced){
        addExcluido(codigo, String.valueOf(erro), bounced);
    }

    public void addExcluido(int codigo, String erro, int bounced){
        getItems().add(new MailingItem(codigo, "",  bounced).setEnviado(false).setErro(erro));
    }

    public int getRegistroAfetados() {
        return registroAfetados;
    }

    public List<List<MailingItem>> fracionarDestinatarios() {
        return ListUtils.partition(getItems(), nrLimite);
    }

    public StringBuffer getEnviados(){
        StringBuffer env = new StringBuffer();
        for(MailingItem mi : getItems()){
            if(mi.operacao.equals(MailingItem.OP.ENV)) {
                env.append(',').append(mi.codigo);
            }
        }
        return env;
    }

    public StringBuffer getEnviadosExcluidos(){
        StringBuffer env = new StringBuffer();
        for(MailingItem mi : getItems()){
            if(mi.operacao.equals(MailingItem.OP.ERR)) {
                env.append(',').append(mi.codigo).append('|').append(mi.getErro() == null ? "0" : mi.getErro());
            }
        }
        return env;
    }

    public String[] toArray() {
        return toArray(getItems());
    }

    public String[] toArray(List<MailingItem> itemsLocal) {
        List<String> emails = new ArrayList<String>();
        if(null != itemsLocal) {
            for (MailingItem mi : itemsLocal) {
                if (mi.operacao.equals(MailingItem.OP.AG)) {
                    emails.add(mi.getValor());
                }
            }
        }
        return emails.toArray(new String[emails.size()]);
    }

    public void marcarTodosEnviado(){
        marcarTodosEnviado(getItems());
    }
    public void marcarTodosEnviado(List<MailingItem> itemsLocal){
        if(null != itemsLocal) {
            for (MailingItem mi : itemsLocal) {
                if (mi.operacao.equals(MailingItem.OP.AG)) {
                    mi.setEnviado(true);
                }
            }
        }
    }

    public void marcarTodosNaoEnviado(){
        marcarTodosNaoEnviado(getItems());
    }
    public void marcarTodosNaoEnviado(List<MailingItem> itemsLocal){
        for (MailingItem mi : getItems()){
            if(mi.operacao.equals(MailingItem.OP.AG)) {
                mi.setEnviado(false);
                if(mi.getErro() == null){
                    mi.setErro("0");
                }
            }
        }
    }


    public int getQtdeNaoEnviados(){
        return getQtde(false);
    }
    public int getQtdeEnviados(){
        return getQtde(true);
    }
    private int getQtde( boolean isEnviado){
        int count = 0;
        for (MailingItem mi : getItems()){
            if(mi.isEnviado() == isEnviado) {
                count++;
            }
        }
        return count;
    }

    public static class MailingItem{
        public int getBounced() {
            return bounced;
        }

        public void setBounced(int bounced) {
            this.bounced = bounced;
        }

        public enum OP{AG, ERR, ENV;}

        private int codigo;
        private String valor;
        private OP operacao;
        private String erro;
        private int bounced;
        private String custom_fields;

        public MailingItem(int codigo, String valor, int bounced) {
            this.codigo = codigo;
            this.valor = valor;
            this.operacao = OP.AG;
            this.bounced = bounced;
        }

        public boolean isEnviado() {
            return operacao.equals(OP.ENV);
        }

        public MailingItem setEnviado(boolean enviado) {
            this.operacao = enviado ? OP.ENV : OP.ERR;
            return  this;
        }

        public String getErro() {
            return erro;
        }

        public MailingItem setErro(String erro) {
            this.erro = erro;
            return this;
        }

        public int getCodigo() {
            return codigo;
        }

        public String getValor() {
            return valor;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof MailingItem)) return false;

            MailingItem that = (MailingItem) o;

            return getValor().equals(that.getValor());
        }

        @Override
        public int hashCode() {
            return getValor().hashCode();
        }

        public String getCustom_fields() {
            return custom_fields;
        }

        public void setCustom_fields(String custom_fields) {
            this.custom_fields = custom_fields;
        }
    }
    
    public void marcarEmailEnviado(final String email){
        for (MailingItem mi : getItems()){
            if(mi.getValor().equals(email) && mi.operacao.equals(MailingItem.OP.AG)) {
                mi.setEnviado(true);
            }
        }
    }

}

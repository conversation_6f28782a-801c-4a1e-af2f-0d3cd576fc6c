/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package servicos;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.json.DemonstrativoFinanceiroJSON;
import org.json.JSONArray;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.DFSinteticoDWVO;
import negocio.comuns.financeiro.DFSinteticoDetalheVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.ConfiguracaoFinanceiro;
import negocio.facade.jdbc.financeiro.DFSinteticoDW;
import negocio.facade.jdbc.financeiro.DFSinteticoDetalhe;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.DFSinteticoDetalheInterfaceFacade;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.DemonstrativoFinanceiro;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import relatorio.negocio.jdbc.financeiro.RelatorioDemonstrativoFinanceiro;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DemonstrativoFinanceiroSintetico {

    public static Double processarDemonstrativo(Connection con, Integer empresa, Integer nrMeses){
        return processarDemonstrativo(con, empresa, nrMeses, false);
    }

    public static Double processarDemonstrativo(Connection con, Integer empresa, Integer nrMeses, boolean ticketmedioreceita){
        return processarDemonstrativo(con,empresa,nrMeses,ticketmedioreceita,Calendario.hoje());
    }

    public static Double processarDemonstrativo(Connection con, Integer empresa, Integer nrMeses, boolean ticketmedioreceita, Date dataBase){
        return processarDemonstrativo(con, empresa, nrMeses, ticketmedioreceita, dataBase, null);
    }

    public static Double processarDemonstrativo(Connection con, Integer empresa, Integer nrMeses, boolean ticketmedioreceita, Date dataBaseFim, Date dataBaseInicio) {
        Double valorRetorno = null;
        try{
            RelatorioDemonstrativoFinanceiro relatorioDF = new RelatorioDemonstrativoFinanceiro();
            Map<Integer, DFSinteticoDWVO> sintetico = new HashMap<Integer,DFSinteticoDWVO>();
            Map<Date,List<DemonstrativoFinanceiroJSON>> detalhesJSON = new HashMap<Date, List<DemonstrativoFinanceiroJSON>>();
            
            ConfiguracaoFinanceiro configuracaoFinanceiro = new ConfiguracaoFinanceiro(con);
            ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = configuracaoFinanceiro.consultar();
            configuracaoFinanceiro = null;

            //calcular datas de consulta
            if (dataBaseInicio == null) {
                dataBaseInicio = Uteis.somarCampoData(dataBaseFim, Calendar.MONTH, nrMeses == null ? -5 : nrMeses);
            }
            
            Calendar dataInicialRel = Calendario.getInstance();
            dataInicialRel.setTime(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataBaseInicio)));
            Calendar dataFinalRel = Calendario.getInstance();
            dataFinalRel.setTime(Uteis.obterUltimoDiaMesUltimaHora(dataBaseFim));

            //receita
            List<DemonstrativoFinanceiro> listaDF = relatorioDF.gerarDemonstrativo(TipoRelatorioDF.RECEITA,
                    TipoVisualizacaoRelatorioDF.PLANOCONTA, dataInicialRel, dataFinalRel, empresa,
                    new ArrayList<Integer>(), true, TipoFonteDadosDF.TODAS, configuracaoFinanceiroVO.getUsarCentralEventos(), false, null, true, con);
            processarLista(listaDF, true, sintetico, false);

            if(ticketmedioreceita){
                int mesData = Uteis.getMesData(dataBaseFim);
                Mes mes = Mes.getMesPeloCodigo(mesData);
                if(sintetico.get(mes.getCodigo()) == null){
                    valorRetorno = 0.0;
                }else{
                    valorRetorno = sintetico.get(mes.getCodigo()).getReceita();
                }
            }

            for (DemonstrativoFinanceiro obj : listaDF) {
                for(TotalizadorMesDF total : obj.getListaTotalizadorMeses()){
                    Date data = Uteis.obterPrimeiroDiaMes(total.getMesProcessar().getDataIni().getTime());
                    List<DemonstrativoFinanceiroJSON> listaJson = detalhesJSON.get(data);
                    if(listaJson == null){
                        listaJson = new ArrayList<DemonstrativoFinanceiroJSON>();
                        detalhesJSON.put(data, listaJson);
                    }
                    DemonstrativoFinanceiroJSON json = new DemonstrativoFinanceiroJSON();
                    json.setMesAno(Uteis.getDataAplicandoFormatacao(data, "MM/yyyy"));
                    json.setCodigo(obj.getCodigoAgrupador());
                    json.setEntrada(obj.getTipoES() == null ? false : obj.getTipoES().equals(TipoES.ENTRADA));
                    json.setNome(obj.getNomeAgrupador());
                    json.setValor(total.getTotalNivel());
                    listaJson.add(json);
                }
            }

            relatorioDF = new RelatorioDemonstrativoFinanceiro();
            //faturamento
            listaDF = relatorioDF.gerarDemonstrativo(TipoRelatorioDF.FATURAMENTO,
                    TipoVisualizacaoRelatorioDF.PLANOCONTA, dataInicialRel, dataFinalRel, empresa,
                    new ArrayList<Integer>(), true, TipoFonteDadosDF.TODAS, configuracaoFinanceiroVO.getUsarCentralEventos(), false, null, true, con);
            processarLista(listaDF, false, sintetico, false);


            relatorioDF = new RelatorioDemonstrativoFinanceiro();
            //competencia
            listaDF = relatorioDF.gerarDemonstrativo(TipoRelatorioDF.COMPETENCIA,
                    TipoVisualizacaoRelatorioDF.PLANOCONTA, dataInicialRel, dataFinalRel, empresa,
                    new ArrayList<Integer>(), true, TipoFonteDadosDF.TODAS, configuracaoFinanceiroVO.getUsarCentralEventos(), false, null, true, con);
            processarLista(listaDF, false, sintetico, true);

            //salvar em banco
            DFSinteticoDW dao = new DFSinteticoDW(con);
            DFSinteticoDetalheInterfaceFacade daoDetalhes = new DFSinteticoDetalhe(con);
            dao.incluir(sintetico.values(), empresa);
            List<DFSinteticoDetalheVO> detalhesDF = new ArrayList<DFSinteticoDetalheVO>();
            for(Date d : detalhesJSON.keySet()){
                if(detalhesJSON.get(d) == null || detalhesJSON.get(d).isEmpty()){
                    continue;
                }
                JSONArray array = new JSONArray(detalhesJSON.get(d));
                detalhesDF.add(new DFSinteticoDetalheVO(empresa, d, array.toString()));
            }
            daoDetalhes.incluir(detalhesDF);
        }catch(Exception e){
            e.printStackTrace();
        }
        return valorRetorno;
    }



    public static void main(String ... args) throws SQLException{
        Connection con1 = DriverManager.getConnection("*******************************************************", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(con1);
        processarDemonstrativo(con1, 5, null);
        //            Set<Integer> keySet = sintetico.keySet();
//            for(Integer cod : keySet){
//                System.out.println(sintetico.get(cod).getMes() + " - "+ sintetico.get(cod).getFaturamento()
//                        +" - "+sintetico.get(cod).getReceita()
//                        +" - "+sintetico.get(cod).getDespesa());
//            }
    }

    private static void processarLista(List<DemonstrativoFinanceiro> listaDF, boolean receita, 
            Map<Integer, DFSinteticoDWVO> sintetico, boolean competencia) {
        List<DemonstrativoFinanceiro> listaTotalizadoresDF = new ArrayList<DemonstrativoFinanceiro>();
        DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Total Entrada(+): ");
        listaTotalizadoresDF.add(df);
        df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Total Saída(-): ");
        listaTotalizadoresDF.add(df);
        
        // Criar lista dos meses que serão processados
        List<MesProcessar> listaMesProcessar = new ArrayList<MesProcessar>();
        if (listaDF.size() > 0) {
            DemonstrativoFinanceiro dfRel = listaDF.get(0);
            for (TotalizadorMesDF obj : dfRel.getListaTotalizadorMeses()) {
                listaMesProcessar.add(obj.getMesProcessar());
            }
        }
        // Criar a lista de meses para cada totalizador.
        for (DemonstrativoFinanceiro demonstrativo : listaTotalizadoresDF) {
            RelatorioDemonstrativoFinanceiro.criarTotalizadoresMeses(demonstrativo, listaMesProcessar);
        }

        for (DemonstrativoFinanceiro obj : listaDF) {
            for (TotalizadorMesDF totalizadorMesDf : obj.getListaTotalizadorMeses()) {
                 if (obj.getCodigoAgrupador().length() == 3) {
                    totalizarListaTotalizadoresDF(totalizadorMesDf, listaTotalizadoresDF);
                }
            }
            
        }

        DemonstrativoFinanceiro dfEntrada = listaTotalizadoresDF.get(0);
        for(TotalizadorMesDF totalizadorMesDf : dfEntrada.getListaTotalizadorMeses()){
                setarMes(sintetico, totalizadorMesDf, false, receita, competencia);
        }

        if(receita){
            DemonstrativoFinanceiro dfSaida = listaTotalizadoresDF.get(1);
            for(TotalizadorMesDF totalizadorMesDf : dfSaida.getListaTotalizadorMeses()){
                    setarMes(sintetico, totalizadorMesDf, true, receita, competencia);
            }
        }

    }

    private static void setarMes(Map<Integer, DFSinteticoDWVO> sintetico, TotalizadorMesDF totalizadorMesDf, boolean saida, boolean receita, boolean competencia){
        String[] dadosMes = totalizadorMesDf.getMesProcessar().getNomeMes().split("/");
        Mes mes = Mes.getMesPelaDescricao(dadosMes[0].trim());
            if(mes != null && !mes.equals(Mes.VAZIO)){
                DFSinteticoDWVO sinteticoMes = sintetico.get(mes.getCodigo());
                if(sinteticoMes == null){
                    sinteticoMes = new DFSinteticoDWVO();
                    sinteticoMes.setAno(Integer.valueOf(dadosMes[1].trim()));
                    sinteticoMes.setMes(mes.getCodigo());
                    sintetico.put(mes.getCodigo(), sinteticoMes);
                }
                if(saida){
                    sinteticoMes.setDespesa(sinteticoMes.getDespesa() + totalizadorMesDf.getTotalNivel());
                }else if (receita) {
                    sinteticoMes.setReceita(sinteticoMes.getReceita() + totalizadorMesDf.getTotalNivel());
                }else {
                    if (!competencia){
                        sinteticoMes.setFaturamento(sinteticoMes.getFaturamento() + totalizadorMesDf.getTotalNivel());
                    }
                }

                if (competencia) {
                    sinteticoMes.setCompetencia(sinteticoMes.getCompetencia() + totalizadorMesDf.getTotalNivel());
                }

            }
    }

    private static void totalizarListaTotalizadoresDF(TotalizadorMesDF totalizadorMesDf, List<DemonstrativoFinanceiro> listaTotalizadoresDF) {
        DemonstrativoFinanceiro dfEntrada = listaTotalizadoresDF.get(0);
        DemonstrativoFinanceiro dfSaida = listaTotalizadoresDF.get(1);

        
        int indice;
        //Somar os valores das Entradas.
        indice = dfEntrada.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        TotalizadorMesDF totMesDF = dfEntrada.getListaTotalizadorMeses().get(indice);
        totMesDF.setTotalNivel(totalizadorMesDf.getTotalEntradaNivel().isNaN() ? 0.0 : totalizadorMesDf.getTotalEntradaNivel());
        
        //dfEntrada.setTotalTodosMeses(dfEntrada.getTotalTodosMeses() + totMesDF.getTotalNivel());

        //Somar os valores das Saídas.
        indice = dfSaida.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        TotalizadorMesDF  totMesDFSaida = dfSaida.getListaTotalizadorMeses().get(indice);

        totMesDFSaida.setTotalNivel(totalizadorMesDf.getTotalSaidaNivel().isNaN() ? 0.0 : totalizadorMesDf.getTotalSaidaNivel());
        
        //dfSaida.setTotalTodosMeses(dfSaida.getTotalTodosMeses() + totMesDF.getTotalNivel());
    }
    
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;
import org.json.JSONObject;
import com.jcraft.jsch.Session;
import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.io.FileUtils;
import servicos.util.ExecuteRequestHttpService;
import servicos.util.SFTP;

/**
 *
 * <AUTHOR>
 */
public class BounceService {
    
    private static final String JSON_BOUNCE = "bounce.json";
    private static final String JSON_DENUNCIA = "denuncias.json";

    private static Map<String, String> preencherEmails(final String jsonName) {
        Map<String, String> mapa = new HashMap();
        try {
            Map<String, String> h = new HashMap();
            h.put("content-type", "application/json");
            String retorno = ExecuteRequestHttpService.executeRequestGET(
                String.format("http://app.pactosolucoes.com.br/bounce_emails/%s", jsonName), h);
            if (!retorno.endsWith("\"\n]\n") &&!retorno.endsWith("\"]\n") && !retorno.endsWith(",]\n")){
                retorno = retorno.replace("]\n","\"]\n");
            }
            JSONArray arr = new JSONArray(retorno);
            for (int i=0; i < arr.length(); i++){
                final String email = arr.getString(i);
                mapa.put(email.toLowerCase(), email.toLowerCase());
            }            
        } catch (Exception ex) {
            Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return mapa;
    }
    
    public static Map<String, String> preencherEmailsBounce() {
        return preencherEmails(JSON_BOUNCE);
    }
    
    public static Map<String, String> preencherEmailsDenunciantes() {
        return preencherEmails(JSON_DENUNCIA);
    }    


    private static void excluirEmailsPessoa(Connection c, Map mapa, StringBuilder sbEmails, boolean singleArray) throws Exception {
        StringBuilder sqlPessoa = new StringBuilder("select e.codigo, e.email, p.codigo as cod_pessoa, p.nome from email e inner join pessoa p on p.codigo = e.pessoa where (e.bloqueadobounce is false or e.bloqueadobounce is null) and lower(e.email) in ").append(sbEmails);
        //Uteis.logar("excluirEmailsPessoa => " + sqlPessoa.toString());
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlPessoa.toString(), c);

        while (rs.next()) {
            final String email = rs.getString("email");
            final String pessoa = rs.getString("nome");
            final Integer codigo = rs.getInt("codigo");
            final Integer cod_pessoa = rs.getInt("cod_pessoa");

            //Setando o e-mail associado a pessoa para correspondencia false
            SuperFacadeJDBC.executarConsultaUpdate(String.format("UPDATE email SET bloqueadoBounce = TRUE, emailCorrespondencia = FALSE WHERE codigo = '%s'", codigo), c);

            //gera Log da exclusão deste e-mail
            Uteis.logar(String.format("Desmarcando correspondência do e-mail %s da pessoa... %s", email, pessoa));
            StringBuilder insertLog = new StringBuilder("insert into log (nomeentidade, nomeentidadedescricao, chaveprimaria,nomecampo,valorcampoalterado,dataalteracao,responsavelalteracao,operacao,pessoa)").
                    append(" values(").
                    append("'EMAIL', 'Email',").append(codigo).
                    append(", 'TODOS', ").
                    append(String.format("'Marcando bloqueio de Bounce do e-mail %s de Pessoa conforme Politicas Anti-Spam'", email)).append(",").
                    append("'").append(Calendario.getData(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss.SSS")).append("', ").
                    append("'ADMINISTRADOR', ").
                    append("'EXCLUSÃO', ").
                    append(cod_pessoa).
                    append(")");
            SuperFacadeJDBC.executarConsultaUpdate(insertLog.toString(), c);
        }
    }
    
    private static void excluirEmailsRecorrencia(Connection c, Map mapa, final StringBuilder sbEmails, 
            boolean singleArray) throws Exception {
        StringBuilder sqlEmailsRecorrencia = new StringBuilder("select email from emailsrecorrencia where lower(email) in ").append(sbEmails);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmailsRecorrencia.toString(), c);
        try {
            while (rs.next()) {
                final String email = rs.getString("email");
                //deleta o e-mail 
                Uteis.logar(String.format("Excluindo e-mail inválido %s de Emails de Recorrência...", email));
                SuperFacadeJDBC.executarConsultaUpdate(String.format("delete from emailsrecorrencia where lower(email) = '%s'", email), c);
            }
        } catch (Exception e) {
            Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, e);
        }
    }
    
    private static void excluirEmailsFechamentoMeta(Connection c, Map mapa, final StringBuilder sbEmails, boolean singleArray) throws Exception {
        StringBuilder sqlEmailsFechamentoMeta = new StringBuilder("select email from configuracaoemailfechamentometa where lower(email) in ").append(sbEmails);
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlEmailsFechamentoMeta.toString(), c);
        try {
            while (rs.next()) {
                final String email = rs.getString("email");
                //deleta o e-mail 
                Uteis.logar(String.format("Excluindo e-mail inválido %s de Emails de Fechamento de Meta...", email));
                SuperFacadeJDBC.executarConsultaUpdate(String.format("delete from configuracaoemailfechamentometa where lower(email) = '%s'", email), c);

                StringBuilder insertLog = new StringBuilder("INSERT INTO log (nomeentidade, nomeentidadedescricao, chaveprimaria, nomecampo, valorcampoalterado, dataalteracao, responsavelalteracao, operacao, pessoa)").
                        append(" VALUES(").
                        append("'CONFIGURACAOSISTEMACRM', 'Configuração Sistema CRM',").append(1).
                        append(", 'MENSAGEM', ").
                        append(String.format("'Excluido e-mail de Fechamento de Meta conforme Politicas Anti-Spam: %s '", email)).append(",").
                        append("'").append(Calendario.getData(Calendario.hoje(), "yyyy-MM-dd HH:mm:ss.SSS")).append("', ").
                        append("'ADMINISTRADOR', ").
                        append("'EXCLUSÃO', ").
                        append(1).
                        append(")");
                SuperFacadeJDBC.executarConsultaUpdate(insertLog.toString(), c);
            }
        } catch (Exception e) {
            Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static void excluirTodos(final String chave){
        Connection c = null;
        try {
            c = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, c);
            excluirEmailsInBounce(c);
            excluirDenuncias(c);
        } catch (Exception e) {
            Uteis.logar("Não foi possível executar Bounce Service pelo erro ocorrido:" + e.getMessage());
        } finally {
            try {
                if (c != null && !c.isClosed())
                    c.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    private static void excluirEmailsInBounce(Connection c) {
        try {
            Map<String, String> mapa = preencherEmailsBounce();
            System.out.println("Emails in bounce: " + mapa.size());
            try {
                JSONArray arr = new JSONArray(mapa.values());
                StringBuilder sbEmails = new StringBuilder("(");
                for (int i = 0; i < arr.length(); i++) {
                    final String o = arr.getString(i);
                    sbEmails.append("'").append(o.toString()).append("',");
                }
                sbEmails.replace(sbEmails.length() - 1, sbEmails.length(), "");
                sbEmails.append(")");

                if (arr.length() > 0) {
                    excluirEmailsPessoa(c, mapa, sbEmails, false);
                    excluirEmailsRecorrencia(c, mapa, sbEmails, false);
                    excluirEmailsFechamentoMeta(c, mapa, sbEmails, false);
                }

            } catch (Exception ex) {
                Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, ex);
            }
        } catch (Exception ex) {
            Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    private static void excluirDenuncias(Connection c) {
        try {
            Map<String, String> mapa = preencherEmailsDenunciantes();
            System.out.println("Emails em DENÚNCIA: " + mapa.size());
            try {
                JSONArray arr = new JSONArray(mapa.values());
                StringBuilder sbEmails = new StringBuilder("(");
                for (int i = 0; i < arr.length(); i++) {
                    final String email = arr.getString(i);
                    sbEmails.append("'").append(email.toLowerCase()).append("',");
                }
                sbEmails.replace(sbEmails.length() - 1, sbEmails.length(), "");
                sbEmails.append(")");

                excluirEmailsPessoa(c, mapa, sbEmails, true);
                excluirEmailsRecorrencia(c, mapa, sbEmails, true);
                excluirEmailsFechamentoMeta(c, mapa, sbEmails, true);

            } catch (Exception ex) {
                Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, ex);
            }
        } catch (Exception ex) {
            Logger.getLogger(BounceService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void main(String... args) {
        Uteis.debug = true;
        if (args.length > 0) {
            excluirTodos(args[0]);
        } else if (args.length == 0) {
            try {
                List<String> chaves = new DAO().buscarListaChaves();
                for (String chave: chaves){
                    Uteis.logar("Executando Bounce Service na chave " + chave);
                    excluirTodos(chave);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}

package servicos;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

public class NotasRunner {


    public static void main(String[] args) {
        System.out.println("Entrou no notas Runner");
        Uteis.debug = true;
        if (args.length == 0) {
            args = new String[]{"banco"};
        }
        if (args.length >= 1) {
            String chave = args[0];
            try {
                Uteis.logar(null, "------------- INICIO processarNotas: chave "+chave);

                LocalDateTime dateTimeStart = LocalDateTime.now();

                Connection con = new DAO().obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(chave, con);
                Empresa empresa = new Empresa(con);

                RoboVO roboVO = new RoboVO();
                roboVO.setDia(Calendario.hoje());
                roboVO.setListaEmpresa(empresa.consultarTodas(null, Uteis.NIVELMONTARDADOS_ROBO));
                AdministrativoRunner.processarNotas(roboVO, chave, con);

                LocalDateTime dateTimeEnd = LocalDateTime.now();
                long dateTimeDiffInSeconds = ChronoUnit.MINUTES.between(dateTimeStart, dateTimeEnd);
                Uteis.logar(null, "------------- FIM processarNotas em "+dateTimeDiffInSeconds+" minutos: chave "+chave);
                empresa = null;
                con = null;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}

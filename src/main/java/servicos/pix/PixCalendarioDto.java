package servicos.pix;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Calendar;
import java.util.Date;

public class PixCalendarioDto {

    private Date criacao;
    private Integer expiracao;

    public PixCalendarioDto(Integer expiracao) throws Exception {
        this.expiracao = calcularExpiracao(expiracao);
    }

    public PixCalendarioDto(Integer expiracao, boolean pjBank) throws Exception {
        if (pjBank) {
            this.expiracao = expiracao;
        } else {
            this.expiracao = calcularExpiracao(expiracao);
        }
    }

    public PixCalendarioDto() {
    }

    public static Integer calcularExpiracao(Integer expiracaoConfiguradaConvenio) throws Exception {
        //fórmula: (expiração configurada no convênio + segundos até o próximo processo remessaService rodar), onde o remessa service pode rodar às 12:30 e as 22:00

        //primeiro obter a data e horário exato que o pix vai expirar
        Date expiraEm = Calendario.somarSegundos(Calendario.hoje(), expiracaoConfiguradaConvenio.intValue());

        //agora obter o próximo processamento do remessa service lá no dia em que o pix vai expirar
        //obs: pode ser que seja 12:30 ou as 22:00
        Date proximoProcessamento = Uteis.obterProximoProcessamentoRemessaServiceAPartirDeUmaData(expiraEm);

        //agora descobrir a diferença de segundos entre data de expiração e a data do próximo remessaService
        Integer segundosEntreExpiracaoEProximoRemessaService = (int) Calendario.diferencaEmSegundos(expiraEm, proximoProcessamento);

        //retornar a soma entre segundos de expiração + diferença entre a expiração e próximo processamento do remessaService
        return expiracaoConfiguradaConvenio + segundosEntreExpiracaoEProximoRemessaService;
    }

    public Date getCriacao() {
        return criacao;
    }

    public void setCriacao(Date criacao) {
        this.criacao = criacao;
    }

    public Integer getExpiracao() {
        return expiracao;
    }

    public void setExpiracao(Integer expiracao) {
        this.expiracao = expiracao;
    }
}

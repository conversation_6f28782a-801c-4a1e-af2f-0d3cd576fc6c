package servicos.pix;

import java.util.List;

public class PixResponseErrorDto {

    int statusCode;
    String error;
    String error_description;
    String error_title;
    String message;
    List<PixErroDto> erros;

    public List<PixErroDto> getErros() {
        return erros;
    }

    public void setErros(List<PixErroDto> erros) {
        this.erros = erros;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorDescription() {
        return error_description;
    }

    public void setErrorDescription(String error_description) {
        this.error_description = error_description;
    }

    public String getError_title() {
        return error_title;
    }

    public void setError_title(String error_title) {
        this.error_title = error_title;
    }
}

package servicos.pix;

import br.com.pactosolucoes.integracao.pactopay.StatusPactoPayEnum;

public enum PixStatusEnum {

    ATIVA(1, "ATIVA", StatusPactoPayEnum.AGUARDANDO),
    CONCLUIDA(2, "CONCLUIDA", StatusPactoPayEnum.CONCLUIDA_COM_SUCESSO),
    REMOVIDA_PELO_USUARIO_RECEBEDOR(3, "REMOVIDA_PELO_USUARIO_RECEBEDOR", StatusPactoPayEnum.CANCELADA),
    REMOVIDA_PELO_PSP(4, "REMOVIDA_PELO_PSP", StatusPactoPayEnum.CANCELADA),
    EXPIRADA(5, "EXPIRADA", StatusPactoPayEnum.EXPIRADO),
    CANCELADA(6, "CANCELADA", StatusPactoPayEnum.CANCELADA);

    private Integer codigo;
    private String descricao;
    private StatusPactoPayEnum statusPactoPayEnum;

    PixStatusEnum(Integer codigo, String descricao, StatusPactoPayEnum statusPactoPayEnum) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.statusPactoPayEnum = statusPactoPayEnum;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public StatusPactoPayEnum getStatusPactoPayEnum() {
        return statusPactoPayEnum;
    }
}

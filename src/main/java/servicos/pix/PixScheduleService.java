package servicos.pix;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.Pix;

import java.sql.Connection;
import java.util.List;

public class PixScheduleService {

    private String chave;
    private Connection connection;
    private Pix pix;

    public static void main(String[] args) {
        try {
            PixScheduleService pixScheduleService = new PixScheduleService(chave(args));
            pixScheduleService.processarCobrancas();
        } catch (Exception ex) {
            System.out.println("Erro ao processar pix");
            ex.printStackTrace();
        }
    }

    public static String chave(String... args) {
        if (args.length == 1) {
            return args[0];
        }
        return "teste";
    }

    public PixScheduleService(String chave) {
        this.chave = chave;
    }

    public void initConnection() throws Exception {
        setChave(chave);
        setConnection(new DAO().obterConexaoEspecifica(chave));
    }

    public void closeConnection() throws Exception {
        if (getConnection() != null) {
            getConnection().close();
        }
    }

    public List<PixVO> processarCobrancas() throws Exception {
        List<PixVO> pixAtivos;
        try {
            initConnection();
            pixAtivos = getPix().consultarAtivos();
            System.out.println("########### INICIO DE PROCESSAMENTO DE PIX |  " + pixAtivos.size() + " PIX ATIVO(S) | " + Calendario.hoje());

            for (PixVO pixVo : pixAtivos) {
                //Evitar que o pool do caixa em aberto duplique o pagamento do pix com o processo aqui;
                //Se o pix tiver sido gerado pelo caixa em aberto, ou vendas online nos últimos 5 minutos, será ignorado aqui no processo automático.
                if ((pixVo.getOrigem().equals(OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO) ||
                        pixVo.getOrigem().equals(OrigemCobrancaEnum.VENDAS_ONLINE_VENDA) ||
                        pixVo.getOrigem().equals(OrigemCobrancaEnum.VENDAS_ONLINE_LINK_PAGAMENTO)) &&
                        Calendario.diferencaEmMinutos(pixVo.getData(), Calendario.hoje()) <= 5) {
                    continue;
                }

                PixPagamentoService pixPagamentoService = null;
                try {
                    pixPagamentoService = new PixPagamentoService(getConnection());
                    pixPagamentoService.processarPixControlandoTransacao(pixVo);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logar("ERRO PixScheduleService | Pix: " + pixVo.getCodigo() + " | " + ex.getMessage());
                } finally {
                    pixPagamentoService = null;
                }
            }

        } catch (Exception e) {
            System.out.println("Erro ao processar cobranças pix: " + e.getMessage());
            e.printStackTrace();
            throw e;
        } finally {
            closeConnection();
        }
        System.out.println("########### FIM DE PROCESSAMENTO DE PIX | " + Calendario.hoje());
        return pixAtivos;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Connection getConnection() throws Exception {
        if (connection == null) {
            setConnection(new DAO().obterConexaoEspecifica(chave));
        }
        return connection;
    }

    public void setConnection(Connection connection) {
        this.connection = connection;
    }

    public void setPix(Pix pix) {
        this.pix = pix;
    }

    public Pix getPix() throws Exception {
        if (pix == null) {
            setPix(new Pix(getConnection()));
        }
        return pix;
    }
}

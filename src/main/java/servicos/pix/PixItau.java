package servicos.pix;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoTokenEnum;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.Token;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.http.MetodoHttpEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

public class PixItau extends SuperServico implements PixServiceInterfaceFacade {

    private ConvenioCobrancaVO convenioCobrancaVO;
    private String URL_API;
    private String URL_API_TOKEN;

    public PixItau(Connection con, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        super(con);
        this.convenioCobrancaVO = convenioCobrancaVO;
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API = PropsService.getPropertyValue(PropsService.urlApiPixItauProducao);
                this.URL_API_TOKEN = PropsService.getPropertyValue(PropsService.urlApiPixItauAuth);
            } else {
                this.URL_API = PropsService.getPropertyValue(PropsService.urlApiPixItauSandbox);
                this.URL_API_TOKEN = PropsService.getPropertyValue(PropsService.urlApiPixItauAuthSandbox);
            }
        }
    }

    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {

        String token = token(this.convenioCobrancaVO);
        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para cancelar a cobrança");
        }

        String url = this.URL_API + "/cob/" + pixVO.getTxid();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("x-itau-apikey", this.convenioCobrancaVO.getPixClientId());
        headers.put("Authorization", "Bearer " + token);

        JSONObject body = new JSONObject();
        body.put("status", "REMOVIDA_PELO_USUARIO_RECEBEDOR");

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body.toString());

        String response = executeRequest(url, headers, pixRequisicaoDto.getEnvio(), MetodoHttpEnum.PATCH);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public HttpClient createConnector() {
        return null;
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {
        String token = token(this.convenioCobrancaVO);
        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para cancelar a cobrança");
        }

        String url = this.URL_API + "/cob/" + pixVO.getTxid();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        String response = executeRequest(url, headers, null, MetodoHttpEnum.GET);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {

        String token = token(this.convenioCobrancaVO);
        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para cancelar a cobrança");
        }

        String url = this.URL_API + "/pix/" + pixVO.getE2eId();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        String response = executeRequest(url, headers, null, MetodoHttpEnum.GET);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                          String nomeDevedor, String telefoneDevedor,
                                          Double valor, String descricao, Integer expiracao) throws Exception {
        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto(expiracao);

        PixDto pixDto = new PixDto();
        pixDto.setCalendario(pixCalendarioDto);
        pixDto.setChave(convenioCobrancaVO.getPixChave());

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setCpf(cpfDevedor.replaceAll("\\.|-", ""));
        pixDevedorDto.setNome(Uteis.removerCaracteresNaoAscii(nomeDevedor).replace("-", " ").replace("_", " "));
        pixDto.setDevedor(pixDevedorDto);

        pixDto.setSolicitacaoPagador(descricao);
        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(Uteis.formatarValorEmRealSemAlterarPontuacao(valor));
        pixDto.setValor(pixValorDto);

        String txId = gerarTxId();
        pixDto.setTxid(txId);

        PixRequisicaoDto retornoPixDto = criarCobranca(convenioCobrancaVO, pixDto);
        retornoPixDto.getPixDto().setTextoImagemQRcode(retornoPixDto.getPixDto().getPixCopiaECola());
        retornoPixDto.getPixDto().getDevedor().setTelefone(UteisTelefone.removerCaracteresEspeciais(telefoneDevedor));
        return retornoPixDto;
    }

    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body(pixDto));

        String token = token(convenioCobrancaVO);
        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para gerar a cobrança");
        }

        String url = this.URL_API + "/cob/" + pixDto.getTxid();

        Map<String, String> headers = new HashMap<>();
        headers.put("Accept", "application/json");
        headers.put("Content-Type", "application/json");
        headers.put("x-itau-apikey", this.convenioCobrancaVO.getPixClientId());
        headers.put("Authorization", "Bearer " + token);

        String response = executeRequest(url, headers, pixRequisicaoDto.getEnvio(), MetodoHttpEnum.PUT);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        //verificar se já existe token gerado para reutilizar
        Token tokenDAO;
        try {
            tokenDAO = new Token(getCon());
            TokenVO tokenReutilizar = tokenDAO.consultarAptoParaReutilizacao(convenioCobrancaVO.getCodigo(), TipoTokenEnum.PIX_ITAU);
            if (tokenReutilizar != null && !UteisValidacao.emptyString(tokenReutilizar.getAccess_token())) {
                String token = tokenReutilizar.getAccess_token();
                tokenDAO.incrementarUtilizacao(tokenReutilizar.getCodigo());
                return token;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            tokenDAO = null;
        }
        return gerarNovoToken();
    }

    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String gerarNovoToken() throws Exception {
        try {
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getPixClientId())) {
                throw new Exception("Não foi encontrado o client_id para obter o token do itaú.");
            }
            if (UteisValidacao.emptyString(this.convenioCobrancaVO.getPixClientSecret())) {
                throw new Exception("Não foi encontrado o client_secret para obter o token do itaú.");
            }
            if(UteisValidacao.emptyString(this.convenioCobrancaVO.getChaveGETNET())) {
                throw new Exception("Não foi encontrado o certificado para obter o token do itaú.");
            }
            if(UteisValidacao.emptyString(this.convenioCobrancaVO.getNossaChave())) {
                throw new Exception("Não foi encontrada a chave privada para obter o token do itaú.");
            }

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");

            StringBuilder body = new StringBuilder();
            body.append("&grant_type=client_credentials");
            body.append("&client_id=").append(this.convenioCobrancaVO.getPixClientId());
            body.append("&client_secret=").append(this.convenioCobrancaVO.getPixClientSecret());

            String ret = executeRequest(URL_API_TOKEN, headers, body.toString(), MetodoHttpEnum.POST);
            if (ret.contains("Falha ao obter") && ret.contains("filter failed")) {
                throw new ConsistirException("Erro ao gerar o token: Verifique se o Client ID e Client Secret configurados no convênio estão corretos.");
            }
            JSONObject retorno = new JSONObject(ret);
            if (retorno.getInt("statusCode") != 200 && retorno.getInt("statusCode") != 201) {
                throw new ConsistirException("Erro ao obter token:" + retorno.getString("response"));
            }
            if (!retorno.isNull("error")) {
                throw new ConsistirException("Erro ao obter token. Erro:" + retorno.getString("error"));
            }
            return responseTokenDto(retorno.getString("response"), convenioCobrancaVO).getAccess_token();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String gerarTxId() {
        String txId = UUID.randomUUID().toString().replaceAll("-", "");
        if (txId.length() > 35) {
            txId = txId.substring(0, 35);
        }
        return txId;
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {

    }

    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, String response) throws IOException, PixRequestException {
        int status = 0;
        String response_error = "";
        try {
            JSONObject jsonObject = new JSONObject(response);
            status = jsonObject.optInt("statusCode");
            response = jsonObject.getString("response");
            response_error = jsonObject.optString("response_error");
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        pixRequisicaoDto.setResposta(response);
        validateResponseError(response, status);
        Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
        pixRequisicaoDto.setPixDto(json.fromJson(response, PixDto.class));

        //setar a data de pagamento/quitação do pix que vem no json de retorno quando o pix está pago
        try {
            JSONObject retornoJSON = null;
            retornoJSON = new JSONObject(response);
            String dataPagamentoString = retornoJSON.getJSONArray("pix").getJSONObject(0).getString("horario");
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(dataPagamentoString);
            pixRequisicaoDto.getPixDto().setDataPagamento(dataPagamento);
        } catch (Exception ignore) {
        }
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorDto.class);
    }

    public PixResponseErrorItauDto responseErrorDtoItau(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorItauDto.class);
    }

    public PixResponseErrorItau2Dto responseErrorDto2Itau(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorItau2Dto.class);
    }



    @Override
    public String translateMessages(String message) {
        return message;
    }

    @Override
    public void validateResponseError(String responseJsonString, int status) throws PixRequestException {
        if (status != 200 && status != 201) {
            if (responseJsonString.contains("Falha ao obter") && responseJsonString.contains("filter failed")) {
                throw new PixRequestException("Erro ao gerar o token: Verifique se o Client ID e Client Secret configurados no convênio estão corretos.");
            }
            String mensagemErro = "";
            try {
                try {
                    String response_error = new JSONObject(responseJsonString).optString("response_error");
                    if (!UteisValidacao.emptyString(response_error)) {
                        responseJsonString = response_error;
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                PixResponseErrorItau2Dto pixResponseErrorItau2Dto = responseErrorDto2Itau(responseJsonString);
//                PixResponseErrorDto pixResponseErrorDto1 = responseErrorDto(responseJsonString);
//                PixResponseErrorItauDto pixResponseErrorDto = responseErrorDtoItau(responseJsonString);
                //mensagem geral
                if (pixResponseErrorItau2Dto != null) {
                    StringBuilder msg = new StringBuilder();
                    if (pixResponseErrorItau2Dto.getViolacoes() != null && !pixResponseErrorItau2Dto.getViolacoes().isEmpty()) {
                        for (PixResponseErrorItauViolacoesDto vio : pixResponseErrorItau2Dto.getViolacoes()) {
                            if (msg.length() > 0) {
                                msg.append(" - ");
                            }
                            String campo = vio.getPropriedade() == null ? "" : "Campo: " + vio.getPropriedade() + " - ";
                            msg.append(campo).append("Motivo: ").append(vio.getRazao());
                        }
                    }
                    if (!UteisValidacao.emptyString(msg.toString())) {
                        mensagemErro = msg.toString();
                    }
                    if (!UteisValidacao.emptyString(mensagemErro)) {
                        if (mensagemErro.toLowerCase().contains("cob.devedor.cpf")) {
                            mensagemErro = "O campo CPF do aluno não é válido.";
                        } else if (mensagemErro.toLowerCase().contains("documento do solicitante divergente do cadastrado na cobran")) {
                            mensagemErro = "Verifique se a chave Pix cadastrada no convênio de cobrança realmente pertence a conta correta do Itaú configurada no convênio.";
                        }
                    }
                }

            } catch (Exception e) {
                mensagemErro = responseJsonString;
            }
            throw new PixRequestException(mensagemErro);
        }
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws Exception {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public TokenVO responseTokenDto(String response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        Token tokenDAO;
        Pix pixDAO;
        try {
            tokenDAO = new Token(getCon());
            pixDAO = new Pix(getCon());

            pixDAO.incluirPixHistoricoGeral(null, "responseTokenDto", response);

            Gson json = new Gson();
            TokenVO tokenVO = json.fromJson(response, TokenVO.class);
            tokenVO.setData_gerado(Calendario.hoje());
            tokenVO.setVezes_utilizado(1);
            tokenVO.setTipoTokenEnum(TipoTokenEnum.PIX_ITAU);
            tokenVO.setConvenioCobranca(convenioCobrancaVO.getCodigo());
            tokenVO.setCodigo(tokenDAO.incluir(tokenVO).getCodigo());
            return tokenVO;
        } finally {
            tokenDAO = null;
            pixDAO = null;
        }
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            String token = token(convenioCobrancaVO);
            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para consultar o webhook do pix!");
            }

            String url = this.URL_API + "/webhook/" + convenioCobrancaVO.getPixChave();
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);

            String response = executeRequest(url, headers, null, MetodoHttpEnum.GET);
            JSONObject jsonObject = new JSONObject(response);
            if (jsonObject.optInt("statusCode") != 404) {
                return new JSONObject(jsonObject.getString("response"));
            } else {
                return jsonObject;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        try {
            String token = token(convenioCobrancaVO);
            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para consultar o webhook do pix!");
            }

            String url = this.URL_API + "/webhook/" + convenioCobrancaVO.getPixChave();
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);

            //URL de callback NOSSA que o banco vai chamar nas requisições
            JSONObject body = new JSONObject();
            body.put("webhookUrl", urlCallback);

            String response = executeRequest(url, headers, body.toString(), MetodoHttpEnum.PUT);

            JSONObject jsonObject = new JSONObject(response);
            if (jsonObject.optInt("statusCode") == 201) {
                return true;
            } else {
                throw new Exception(jsonObject.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {

            String token = token(convenioCobrancaVO);
            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para consultar o webhook do pix!");
            }

            String url = this.URL_API + "/webhook/" + convenioCobrancaVO.getPixChave();

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Bearer " + token);

            String response = executeRequest(url, headers, null, MetodoHttpEnum.DELETE);
            JSONObject jsonObject = new JSONObject(response);
            if (jsonObject.optInt("statusCode") == 204) {
                return true;
            } else {
                throw new Exception(jsonObject.toString());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    @Override
    public String token(PixVO pixVO) {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String gerarTextoQrCode(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO) {
        return "";
    }

    @Override
    public String fixResponseErros(String responseJson) {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) {
        return null;
    }

    private String executeRequest(String url, Map<String, String> headers, String body, MetodoHttpEnum metodoHttpEnum) {
        String caminhoCertificado = Uteis.desencriptar(this.convenioCobrancaVO.getChaveGETNET(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));
        String caminhoChave = Uteis.desencriptar(this.convenioCobrancaVO.getNossaChave(), PropsService.getPropertyValue(PropsService.chaveDesencriptItauOnline));
        return ExecuteRequestHttpService.executeComCertificado(url, body, headers, caminhoCertificado, caminhoChave, "", metodoHttpEnum.name());
    }
}

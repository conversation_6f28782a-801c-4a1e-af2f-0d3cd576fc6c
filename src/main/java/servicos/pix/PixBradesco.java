package servicos.pix;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FilenameUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

public class PixBradesco implements PixServiceInterfaceFacade {


    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/v1/spi/cob/" + pixVO.getTxid();

        HttpPatch patch = new HttpPatch(url);
        patch.addHeader("Content-Type", "application/json");
        patch.addHeader("Authorization", "Bearer " + token(pixVO.getConveniocobranca()));
        String body = "{\"status\": \"REMOVIDA_PELO_USUARIO_RECEBEDOR\"}";

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body);

        StringEntity params = new StringEntity(body);
        patch.setEntity(params);

        HttpClient httpClient = createConnector(pixVO.getConveniocobranca());
        if (httpClient == null) {
            throw new Exception(getMensagemErroCertificado());
        }
        HttpResponse response = httpClient.execute(patch);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    private String getMensagemErroCertificado() {
        return "Não foi possível obter a conexão. Verifique configuração do certificado no convênio. Tente configurar sem certificado para que o sistema use o certificado da Pacto e caso não funcione, então configure o certificado do cliente. Se o problema persistir, pode ser a senha do certificado que foi informada incorretamente ou o certificado não é válido.";
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/v1/spi/cob/" + pixVO.getTxid();

        HttpGet get = new HttpGet(url);
        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token(pixVO.getConveniocobranca()));

        HttpClient httpClient = createConnector(pixVO.getConveniocobranca());
        if (httpClient == null) {
            throw new Exception(getMensagemErroCertificado());
        }
        HttpResponse response = httpClient.execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/v1/spi/pix/" + pixVO.getE2eId();

        HttpGet get = new HttpGet(url);
        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token(pixVO.getConveniocobranca()));

        HttpClient httpClient = createConnector(pixVO.getConveniocobranca());
        if (httpClient == null) {
            throw new Exception(getMensagemErroCertificado());
        }
        HttpResponse response = httpClient.execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                          String nomeDevedor, String telefoneDevedor,
                                          Double valor, String descricao, Integer expiracao) throws Exception {
        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto(expiracao);

        PixDto pixDto = new PixDto();
        pixDto.setCalendario(pixCalendarioDto);
        pixDto.setChave(convenioCobrancaVO.getPixChave());

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setCpf(cpfDevedor.replaceAll("\\.|-", ""));
        pixDevedorDto.setNome(Uteis.removerCaracteresNaoAscii(nomeDevedor));
        pixDto.setDevedor(pixDevedorDto);

        pixDto.setSolicitacaoPagador(descricao);
        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(Uteis.formatarValorEmRealSemAlterarPontuacao(valor));
        pixDto.setValor(pixValorDto);

        String txId = gerarTxId();
        pixDto.setTxid(txId);

        PixRequisicaoDto retornoPixDto = criarCobranca(convenioCobrancaVO, pixDto);
        String textoQrCode = gerarTextoQrCode(retornoPixDto, convenioCobrancaVO);
        retornoPixDto.getPixDto().setTextoImagemQRcode(textoQrCode);
        retornoPixDto.getPixDto().getDevedor().setTelefone(UteisTelefone.removerCaracteresEspeciais(telefoneDevedor));
        return retornoPixDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {
        String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/v1/spi/cob/" + pixDto.getTxid();
        HttpPut post = new HttpPut(url);

        String token = "";
        try {
            token = token(convenioCobrancaVO);
        } catch (Exception ex) {
            if (ex.getMessage().contains("The given client credentials were not valid")) {
                throw new Exception("As credenciais (Client key ou Client secret) estão incorretas. " +
                        "Confira se estão corretas e também verifique com o banco se essas são credenciais de PRODUÇÃO e não de HOMOLOGAÇÃO.");
            }
            if (ex.getMessage().contains("The client certificate is not valid")) {
                throw new Exception("O certificado não é válido! Algumas contas necessitam do certificado privado para funcionar, " +
                        "se este for o caso, configure o certificado A1 lá no convênio de cobrança de pix.");
            }
            if (ex.getMessage().contains("SSL with client authentication is required")) {
                throw new Exception(getMensagemErroCertificado());
            }
            throw ex;
        }

        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para gerar a cobrança");
        }

        post.addHeader("Content-Type", "application/json");
        post.addHeader("Authorization", "Bearer " + token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body(pixDto));
        StringEntity params = new StringEntity(pixRequisicaoDto.getEnvio());
        post.setEntity(params);

        HttpClient httpClient = createConnector(convenioCobrancaVO);
        if (httpClient == null) {
            throw new Exception(getMensagemErroCertificado());
        }
        HttpResponse response = httpClient.execute(post);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {

        HttpPost post = new HttpPost(apiAuthUrl(convenioCobrancaVO.getAmbiente()));
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("grant_type", "client_credentials"));
        params.add(new BasicNameValuePair("scope", "cob.read cob.write pix.read pix.write webhook.read webhook.write"));

        post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        post.addHeader("Content-Type", "application/x-www-form-urlencoded");
        post.addHeader("Authorization", "Basic " + new String(new Base64().encode((convenioCobrancaVO.getPixClientId() + ":" + convenioCobrancaVO.getPixClientSecret()).getBytes())));

        HttpClient httpClient = createConnector(convenioCobrancaVO);
        if (httpClient == null) {
            throw new Exception(getMensagemErroCertificado());
        }

        HttpResponse response = httpClient.execute(post);
        return responseTokenDto(response).getAccess_token();
    }

    @Override
    public HttpClient createConnector() {
        return ExecuteRequestHttpService.createConnector();
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return ExecuteRequestHttpService.createConnector(path, senha);
    }

    private HttpClient createConnector(ConvenioCobrancaVO convenioCobrancaVO) {
        //usado para descrobrir se é pra usar o certificado privado configurado no convênio ou se é pra usar o privado padrão da Pacto...
        if (!UteisValidacao.emptyList(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO())) {
            String[] nomeArq = convenioCobrancaVO.getListaConvenioCobrancaArquivoVO().get(0).getNomeArquivoOriginal().split("[.]");

            //usar certificado privado configurado dentro do convênio
            return ExecuteRequestHttpService.createConnector(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO().get(0).getArquivo(),
                    nomeArq[0],
                    FilenameUtils.getExtension(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO().get(0).getNomeArquivoOriginal()),
                    Uteis.desencriptar(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO().get(0).getSenha(), PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado)));
        } else {
            //usar certificado privado padrão da Pacto
            return ExecuteRequestHttpService.createConnector(uriPrivateCert(), senhaPrivateCert());
        }

    }

    @Override
    public String token(PixVO pixVO) {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String gerarTextoQrCode(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO) {
        PayloadPix payloadPix = new PayloadPix();
        payloadPix.setUrlLocation(pixRequisicaoDto.getPixDto().getLocation());
        String nomeEmpresa = Uteis.retirarAcentuacao(convenioCobrancaVO.getEmpresa().getNome());
        if (nomeEmpresa.length() > 25) {
            payloadPix.setMerchantName(nomeEmpresa.substring(0, 25));
        } else {
            payloadPix.setMerchantName(nomeEmpresa);
        }
        payloadPix.setMerchantCity(convenioCobrancaVO.getEmpresa().getCidade_Apresentar().length() > 15 ?  Uteis.retirarAcentuacao(convenioCobrancaVO.getEmpresa().getCidade_Apresentar()).substring(0,15) : Uteis.retirarAcentuacao(convenioCobrancaVO.getEmpresa().getCidade_Apresentar()));
        String textoQrCode = payloadPix.gerarPayload();
        return textoQrCode;
    }

    public String gerarTxId() {
        //No Bradesco é obrigatório informar o txId para gerar o pix
        String txId = UUID.randomUUID().toString().replaceAll("-", "");
        if (txId.length() > 35) {
            txId = txId.substring(0, 35);
        }
        return txId;
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorDto.class);
    }

    @Override
    public String fixResponseErros(String responseJson) {
        if (responseJson.contains("\"criacao\":\"-03:00\"")) {
            responseJson = responseJson.replace("\"criacao\":\"-03:00\"", "\"criacao\":\"" + Calendario.format("yyyy-MM-dd'T'HH:mm:ss") + "\"");
        }

        return responseJson;
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        int status = response.getStatusLine().getStatusCode();
        responseJsonString = fixResponseErros(responseJsonString);
        validateResponseError(responseJsonString, status);
        Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
        pixRequisicaoDto.setPixDto(json.fromJson(responseJsonString, PixDto.class));

        //setar a data de pagamento/quitação do pix que vem no json de retorno quando o pix está pago
        try {
            JSONObject retornoJSON = null;
            retornoJSON = new JSONObject(responseJsonString);
            String dataPagamentoString = retornoJSON.getJSONArray("pix").getJSONObject(0).getString("horario");
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(dataPagamentoString);
            pixRequisicaoDto.getPixDto().setDataPagamento(dataPagamento);
        } catch (Exception ignore) {
        }
    }

    @Override
    public String translateMessages(String message) {
        if (message.contains("Unauthorized")) {
            return "Requisição não autorizada. Confira as chaves de aplicação no cadastro do convênio.";
        }
        if (message.contains("Unauthorized")) {
            return "Requisição não autorizada. Confira as chaves de aplicação no cadastro do convênio.";
        }
        if (message.contains("Internal Server Error")) {
            return "Não foi possível gerar a cobrança nos servidores do pix. Aguarde alguns segundos, recarregue a página e tente novamente.";
        }
        if (message.contains("chave DICT")) {
            return "A chave pix da conta configurada no convênio está incorreta! Verifique qual chave pix (cnpj, telefone, email ou aleatória) está vinculada para a conta do seu banco e configure ela no convênio.";
        }
        if (message.contains("\"razao\":\"O campo cob.chave corresponde a uma conta que não pertence a este usuário recebedor.")) {
            return   "Retorno Bradesco: A chave ou credenciais informadas não corresponde a uma conta pertencente a este usuário recebedor.";
        }
        return message;
    }

    @Override
    public void validateResponseError(String responseJsonString, int status) throws PixRequestException {
        if (status != 200 && status != 201) {
            String mesagemErro = "";
            try {
                PixResponseErrorDto pixResponseErrorDto = responseErrorDto(responseJsonString);
                if (pixResponseErrorDto != null && pixResponseErrorDto.getErros() != null && pixResponseErrorDto.getErros().size() > 0) {
                    for (PixErroDto pixErroDto : pixResponseErrorDto.getErros()) {
                        mesagemErro += pixErroDto.getMensagem() + " ";
                        if (pixErroDto.getOcorrencia() != null && !pixErroDto.getOcorrencia().trim().isEmpty()) {
                            mesagemErro += "Ocorrencia: " + pixErroDto.getOcorrencia() + " ";
                        }
                    }
                }

                if (pixResponseErrorDto != null && mesagemErro.trim().isEmpty() && !pixResponseErrorDto.getErrorDescription().trim().isEmpty()) {
                    mesagemErro = pixResponseErrorDto.getErrorDescription();
                }
            } catch (Exception e) {
                mesagemErro = translateMessages(responseJsonString);
            }
            throw new PixRequestException("Falha na requisição do pix: " + mesagemErro);
        }
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        Gson json = new Gson();
        return json.fromJson(responseJsonString, TokenVO.class);
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }


    private String apiUrl(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
            return PropsService.getPropertyValue(PropsService.urlApiPixBradescoSandbox);
        } else {
            return PropsService.getPropertyValue(PropsService.urlApiPixBradescoProducao);
        }
    }

    private String apiAuthUrl(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
            return PropsService.getPropertyValue(PropsService.urlApiPixBradescoAuthSandbox);
        } else {
            return PropsService.getPropertyValue(PropsService.urlApiPixBradescoAuthProducao);

        }
    }

    private String uriPrivateCert(){
        return PropsService.getPropertyValue(PropsService.uriCertPrivado);
    }

    private String senhaPrivateCert(){
        return Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaCertPrivado),PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            String url = PropsService.getPropertyValue(PropsService.urlApiWebhookPixBradesco) + "/" + convenioCobrancaVO.getPixChave();
            HttpGet get = new HttpGet(url);
            get.addHeader("Content-Type", "application/json");
            get.addHeader("Authorization", "Bearer " + token(convenioCobrancaVO));

            HttpClient httpClient = createConnector(convenioCobrancaVO);
            if (httpClient == null) {
                throw new Exception(getMensagemErroCertificado());
            }
            HttpResponse response = httpClient.execute(get);
            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            return new JSONObject(responseJsonString);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        boolean sucesso = false;
        try {
            //Endpoint configurar webhook DO BANCO
            String url = PropsService.getPropertyValue(PropsService.urlApiWebhookPixBradesco) + "/" + convenioCobrancaVO.getPixChave();
            HttpPut put = new HttpPut(url);
            put.addHeader("Content-Type", "application/json");
            put.addHeader("Authorization", "Bearer " + token(convenioCobrancaVO));

            //URL de callback NOSSA que o banco vai chamar nas requisições
            JSONObject body = new JSONObject();
            body.put("webhookUrl", urlCallback);

            StringEntity params = new StringEntity(body.toString());
            put.setEntity(params);

            HttpClient httpClient = createConnector(convenioCobrancaVO);

            if (httpClient == null) {
                throw new Exception(getMensagemErroCertificado());
            }

            HttpResponse response = httpClient.execute(put);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                sucesso = true;
            }

            return sucesso;

        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            //Endpoint Excluir webhook DO BANCO
            String url = PropsService.getPropertyValue(PropsService.urlApiWebhookPixBradesco) + "/" + convenioCobrancaVO.getPixChave();
            HttpDelete delete = new HttpDelete(url);
            delete.addHeader("Content-Type", "application/json");
            delete.addHeader("Authorization", "Bearer " + token(convenioCobrancaVO));

            HttpClient httpClient = createConnector(convenioCobrancaVO);

            if (httpClient == null) {
                throw new Exception(getMensagemErroCertificado());
            }

            HttpResponse response = httpClient.execute(delete);
            int statusCode = response.getStatusLine().getStatusCode();

            //quando excluir com sucesso eles retornam (204 No Content)
            if (statusCode == 204) {
                return true;
            }

            //chegou até aqui, não deu certo, então lançar exceção com a response da request em String
            //tentar obter motivo do erro
            StringBuilder sb = new StringBuilder();
            try {
                String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = new JSONObject(responseJsonString);
                sb.append(jsonObject.getString("title")).append(" | ").append(jsonObject.getString("detail"));
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(sb.toString())) {
                throw new Exception("Não foi possível excluir o webhook. Motivo não informado pelo banco.");
            } else {
                throw new Exception(sb.toString());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }
}

package servicos.pix;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
ATENÇÃO
Se tiver testando local vai dar a seguinte mensagem de handshake no momento em que for fazer as requests em "httpClient.execute":
The size of the handshake message (49409) exceeds the maximum allowed size (32768)
O erro é do nosso lado e para contornar então você precisa:
1-Editar as configurações do seu server (tomcat local) e colocar a seguinte variável no VM Options:
 -Djdk.tls.maxHandshakeMessageSize=65536
2-Reiniciar o servidor tomcat.
Feito isso irá funcionar local. Em produção já tem essas enviroments em todos os servers que o Waller colocou.
*/


public class PixBB implements PixServiceInterfaceFacade {

    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws IOException, PixRequestException {
        String url = apiUrl(pixVO.getAmbienteEnum())+"/cob/"+pixVO.getTxid()+"?gw-dev-app-key="+developerApplicationKeyPacto();
        HttpPatch patch = new HttpPatch(url);

        patch.addHeader("Content-Type", "application/json");
        patch.addHeader("Authorization",  "Bearer "+token(pixVO));

        String body = "{\"status\": \"REMOVIDA_PELO_USUARIO_RECEBEDOR\"}";

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body);

        StringEntity params = new StringEntity(body);
        patch.setEntity(params);

        HttpResponse response = createConnector().execute(patch);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public HttpClient createConnector() {
        return ExecuteRequestHttpService.createConnector();
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return ExecuteRequestHttpService.createConnector(path, senha);
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws IOException, PixRequestException {
        String url = apiUrl(pixVO.getAmbienteEnum())+"/cob/"+pixVO.getTxid()+"?gw-dev-app-key="+developerApplicationKeyPacto();
        HttpGet get = new HttpGet(url);

        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization",  "Bearer "+token(pixVO));

        HttpResponse response = createConnector().execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        responseCobrancaDto(pixRequisicaoDto ,response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/pix/" + pixVO.getE2eId() + "?gw-dev-app-key=" + developerApplicationKeyPacto();
        HttpGet get = new HttpGet(url);

        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token(pixVO));

        HttpResponse response = createConnector().execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) throws IOException, PixRequestException {
        String url = apiUrl(convenioCobrancaVO.getAmbiente())+"/cob/"+txId+"?gw-dev-app-key="+developerApplicationKeyPacto();
        HttpGet get = new HttpGet(url);

        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization",  "Bearer "+token(convenioCobrancaVO));

        HttpResponse response = createConnector().execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                          String nomeDevedor, String telefoneDevedor, Double valor,
                                          String descricao, Integer expiracao) throws Exception {
        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto(expiracao);

        PixDto pixDto = new PixDto();
        pixDto.setCalendario(pixCalendarioDto);
        pixDto.setChave(convenioCobrancaVO.getPixChave());

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setCpf(Uteis.formatarCpfCnpj(cpfDevedor, true));
        pixDevedorDto.setNome(tratarCaracteresEspeciaisNomeDevedor(nomeDevedor));
        pixDto.setDevedor(pixDevedorDto);

        pixDto.setSolicitacaoPagador(descricao);
        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(valor.toString());
        pixDto.setValor(pixValorDto);

        PixRequisicaoDto retornoPixDto = criarCobranca(convenioCobrancaVO, pixDto);

        retornoPixDto.getPixDto().getDevedor().setTelefone(UteisTelefone.removerCaracteresEspeciais(telefoneDevedor));
        return retornoPixDto;
    }

    public String tratarCaracteresEspeciaisNomeDevedor(String nomeDevedor) {
        //Retirar caracter do alfabeto francês, espanhol e portugues que não é tratado pela Api do BB e retorna Erro: 500 Ex: D´ornelas
        //Também remove espaços duplicados e em branco NBSP que também já aconteceu esse problema
        return Uteis.retirarAcentuacao(nomeDevedor)
                .replace("\u00A0", " ")  // Remove NBSP
                .replace("´", "")        // Remove acento solto
                .replaceAll("\\s+", " ") // Substitui múltiplos espaços por um único
                .trim();     }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {
        String url = apiUrl(convenioCobrancaVO.getAmbiente())+"/cobqrcode/?gw-dev-app-key="+developerApplicationKeyPacto();
        HttpPut post = new HttpPut(url);

        String token = token(convenioCobrancaVO);
        post.addHeader("Content-Type", "application/json");
        post.addHeader("Authorization",  "Bearer "+token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body(pixDto));

        StringEntity params = new StringEntity(pixRequisicaoDto.getEnvio());
        post.setEntity(params);

        HttpResponse response = createConnector().execute(post);
        if (response != null && response.getStatusLine().getStatusCode() == 500) {
            throw new Exception("Não foi possível criar o conector para gerar o pix. Entre em contato com a Pacto. " +
                    response.getStatusLine().getStatusCode() + " " + response.getStatusLine().getReasonPhrase());
        }
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorDto.class);
    }

    @Override
    public String fixResponseErros(String responseJson) {
        if(responseJson.contains("\"criacao\":\"-03:00\"")){
            responseJson = responseJson.replace("\"criacao\":\"-03:00\"", "\"criacao\":\""+ Calendario.format("yyyy-MM-dd'T'HH:mm:ss")+"\"");
        }
        return responseJson;
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        int status = response.getStatusLine().getStatusCode();
        responseJsonString = fixResponseErros(responseJsonString);
        validateResponseError(responseJsonString, status);
        Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
        pixRequisicaoDto.setPixDto(json.fromJson(responseJsonString, PixDto.class));

        //setar a data de pagamento/quitação do pix que vem no json de retorno quando pix está pago
        try {
            JSONObject retornoJSON = null;
            retornoJSON = new JSONObject(responseJsonString);
            String dataPagamentoString = retornoJSON.getJSONArray("pix").getJSONObject(0).getString("horario");
            Date dataPagamento = Uteis.getDate(dataPagamentoString, "yyyy-MM-dd'T'HH:mm:ss");
            pixRequisicaoDto.getPixDto().setDataPagamento(dataPagamento);
        } catch (Exception ignore) {
        }
    }

    @Override
    public String translateMessages(String message) {
        if (message.contains("Unauthorized")) {
            return "Requisição não autorizada. Confira as chaves de aplicação no cadastro do convênio.";
        }
        if (message.contains("Unauthorized")) {
            return "Requisição não autorizada. Confira as chaves de aplicação no cadastro do convênio.";
        }
        if (message.contains("Internal Server Error")) {
            return "Não foi possível gerar a cobrança nos servidores do pix. Aguarde alguns segundos, recarregue a página e tente novamente.";
        }
        if (message.contains("chave Pix não foi localizada")) {
            return "A chave pix da conta configurada no convênio está incorreta! Verifique qual chave pix (cnpj, telefone, email ou aleatória) está vinculada para a conta do seu banco e configure ela no convênio.";
        }
        return message;
    }

    @Override
    public void validateResponseError(String responseJsonString, int status) throws PixRequestException {
        if(status != 200 && status != 201){
            String mesagemErro = "";
            try{
                PixResponseErrorDto pixResponseErrorDto = responseErrorDto(responseJsonString);
                if(pixResponseErrorDto != null && pixResponseErrorDto.getErros() != null && pixResponseErrorDto.getErros().size() > 0){
                    if (!pixResponseErrorDto.getErros().get(0).getMensagem().contains("chave Pix não foi localizada")){
                        for (PixErroDto pixErroDto: pixResponseErrorDto.getErros()) {
                            mesagemErro += pixErroDto.getMensagem()+" ";
                            if(pixErroDto.getOcorrencia() != null && !pixErroDto.getOcorrencia().trim().isEmpty()){
                                mesagemErro += "Ocorrencia: "+ pixErroDto.getOcorrencia()+" ";
                            }
                        }
                    } else {
                        mesagemErro = translateMessages(responseJsonString);
                    }
                }

                if(pixResponseErrorDto != null && mesagemErro.trim().isEmpty() && !pixResponseErrorDto.getErrorDescription().trim().isEmpty()){
                    mesagemErro = pixResponseErrorDto.getErrorDescription();
                }
            }catch (Exception e){
                e.printStackTrace();
                mesagemErro = translateMessages(responseJsonString);
            }

            Uteis.logarDebug("Erro PIX: " + responseJsonString);

            if (mesagemErro != null && mesagemErro.toUpperCase().contains("CAMPO CPF NÃO É VÁLIDO")) {
                throw new PixRequestException("O campo CPF do aluno não é válido.");
            } else {
                throw new PixRequestException("Falha na requisição do pix: "+mesagemErro);
            }
        }
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        Gson json = new Gson();
        return json.fromJson(responseJsonString, TokenVO.class);
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }


    public String token(PixVO pixVO) throws IOException, PixRequestException {
        return token(pixVO.getBasicAuth(), pixVO.getAmbienteEnum());
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws IOException, PixRequestException {
        return token(convenioCobrancaVO.getPixBasicAuth(), convenioCobrancaVO.getAmbiente());
    }

    public String token(String basicToken, AmbienteEnum ambiente) throws IOException, PixRequestException {
        HttpPost post = new HttpPost(apiAuthUrl(ambiente));
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("grant_type", "client_credentials"));
        params.add(new BasicNameValuePair("scope", "pix.read pix.write cob.write cob.read webhook.read webhook.write"));

        post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        post.addHeader("Content-Type", "application/x-www-form-urlencoded");
        String basicAuthtoken = basicToken.replaceAll(" |Basic", "");
        post.addHeader("Authorization",  "Basic " + basicAuthtoken);

        HttpResponse response = createConnector().execute(post);
        return responseTokenDto(response).getAccess_token();
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            String url = apiUrlV2(convenioCobrancaVO.getAmbiente())+"/webhook/" + convenioCobrancaVO.getPixChave() +
                    "?gw-dev-app-key=" + developerApplicationKeyPacto();

            String token = token(convenioCobrancaVO);
            HttpGet get = new HttpGet(url);
            get.addHeader("Content-Type", "application/json");
            get.addHeader("Authorization",  "Bearer " + token);

            HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());

            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado corretamente.");
            }

            //TODO Quando for testar local, fica dando erro de handshake, favor ler o comentário "ATENÇÃO" no início dessa classe aqui
            HttpResponse response = httpClient.execute(get);

            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            return new JSONObject(responseJsonString);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        try {
            String url = apiUrlV2(convenioCobrancaVO.getAmbiente())+"/webhook/" + convenioCobrancaVO.getPixChave() +
                    "?gw-dev-app-key=" + developerApplicationKeyPacto();

            String token = token(convenioCobrancaVO);
            HttpPut put = new HttpPut(url);
            put.addHeader("Content-Type", "application/json");
            put.addHeader("Authorization",  "Bearer " + token);

            //URL de callback NOSSA que o banco vai chamar nas requisições
            JSONObject body = new JSONObject();
            body.put("webhookUrl", urlCallback);

            StringEntity params = new StringEntity(body.toString());
            put.setEntity(params);

            HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());

            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado corretamente.");
            }

            //TODO Quando for testar local, fica dando erro de handshake, favor ler o comentário "ATENÇÃO" no início dessa classe aqui
            HttpResponse response = httpClient.execute(put);

            int statusCode = response.getStatusLine().getStatusCode();

            //quando excluir com sucesso eles retornam (204 No Content)
            if (statusCode == 200) {
                return true;
            } else {
                String responseString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                throw new Exception(responseString);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private String uriPrivateCert(){
        return PropsService.getPropertyValue(PropsService.uriCertPrivado);
    }

    private String senhaPrivateCert(){
        return Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaCertPrivado),PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            String url = apiUrlV2(convenioCobrancaVO.getAmbiente())+"/webhook/" + convenioCobrancaVO.getPixChave() +
                    "?gw-dev-app-key=" + developerApplicationKeyPacto();

            String token = token(convenioCobrancaVO);
            HttpDelete delete = new HttpDelete(url);
            delete.addHeader("Content-Type", "application/json");
            delete.addHeader("Authorization",  "Bearer " + token);

            HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());

            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado corretamente.");
            }

            //TODO Quando for testar local, fica dando erro de handshake, favor ler o comentário "ATENÇÃO" no início dessa classe aqui
            HttpResponse response = httpClient.execute(delete);

            int statusCode = response.getStatusLine().getStatusCode();

            //quando excluir com sucesso eles retornam (204 No Content)
            if (statusCode == 204) {
                return true;
            }
            return false;

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    private String apiUrl(AmbienteEnum ambienteEnum){
        if(ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)){
            return PropsService.getPropertyValue(PropsService.urlApiPixBBSandbox);
        }else{
            return PropsService.getPropertyValue(PropsService.urlApiPixBBProducao);
        }
    }

    private String apiUrlV2(AmbienteEnum ambienteEnum){
        if(ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)){
            return PropsService.getPropertyValue(PropsService.urlApiPixBBSandboxV2);
        }else{
            return PropsService.getPropertyValue(PropsService.urlApiPixBBProducaoV2);
        }
    }

    private String apiAuthUrl(AmbienteEnum ambienteEnum){
        if(ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)){
            return PropsService.getPropertyValue(PropsService.urlApiPixBBAuthSandbox);
        }else{
            return PropsService.getPropertyValue(PropsService.urlApiPixBBAuthProducao);
        }
    }

    private String developerApplicationKeyPacto(){
        //disponível no cadastro da Pacto no portal developer BB
        return "7091008b06ffbee0136ce181a0050356b9d1a5b8";
    }
}

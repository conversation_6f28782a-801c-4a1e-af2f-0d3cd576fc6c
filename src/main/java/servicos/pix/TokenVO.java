package servicos.pix;

import negocio.comuns.basico.enumerador.TipoTokenEnum;

import java.util.Date;

public class TokenVO {
    private int codigo;
    private Date data_gerado;
    private String access_token;
    private String token_type;
    private long expires_in;
    private String scope;
    private int vezes_utilizado;
    private TipoTokenEnum tipoTokenEnum;
    private int convenioCobranca;

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getToken_type() {
        return token_type;
    }

    public void setToken_type(String token_type) {
        this.token_type = token_type;
    }

    public long getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(long expires_in) {
        this.expires_in = expires_in;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public Date getData_gerado() {
        return data_gerado;
    }

    public void setData_gerado(Date data_gerado) {
        this.data_gerado = data_gerado;
    }

    public int getVezes_utilizado() {
        return vezes_utilizado;
    }

    public void setVezes_utilizado(int vezes_utilizado) {
        this.vezes_utilizado = vezes_utilizado;
    }

    public TipoTokenEnum getTipoTokenEnum() {
        return tipoTokenEnum;
    }

    public void setTipoTokenEnum(TipoTokenEnum tipoTokenEnum) {
        this.tipoTokenEnum = tipoTokenEnum;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(int convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }
}

package servicos.pix;

import org.apache.commons.lang.StringUtils;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 28/07/2021.
 * classe responsável por gerar o payload do pix (texto copiável que resultará no qr code)
 */

public class PayloadPix {

    private final static String ID_PAYLOAD_FORMAT_INDICATOR = "00";
    private final static String ID_POINT_OF_INITIATION_METHOD = "01";
    private final static String ID_MERCHANT_ACCOUNT_INFORMATION = "26";
    private final static String ID_MERCHANT_ACCOUNT_INFORMATION_GUI = "00";
    private final static String ID_MERCHANT_ACCOUNT_INFORMATION_URL = "25";
    private final static String ID_MERCHANT_CATEGORY_CODE = "52";
    private final static String ID_TRANSACTION_CURRENCY = "53";
    private final static String ID_COUNTRY_CODE = "58";
    private final static String ID_MERCHANT_NAME = "59";
    private final static String ID_MERCHANT_CITY = "60";
    private final static String ID_ADDITIONAL_DATA_FIELD_TEMPLATE = "62";
    private final static String ID_ADDITIONAL_DATA_FIELD_TEMPLATE_TXID = "05";
    private final static String ID_CRC16 = "63";
    private String merchantName = "";
    private String merchantCity = "";
    private String payload = "";
    private String urlLocation = "";


    public String gerarPayload() {
        payload = getValue(ID_PAYLOAD_FORMAT_INDICATOR, "01") +
                getPointOfInitiationMethod() +
                getMerchantAccountInformation() +
                getValue(ID_MERCHANT_CATEGORY_CODE, "0000") +
                getValue(ID_TRANSACTION_CURRENCY, "986") +
                getValue(ID_COUNTRY_CODE, "BR") +
                getValue(ID_MERCHANT_NAME, getMerchantName()) +
                getValue(ID_MERCHANT_CITY, getMerchantCity()) +
                getAdditionalDataFieldTemplate();

        //Retorna o Payload + CRC16
        return payload + getCRC16(payload);
    }

    public String getValue(String id, String valor) {
        int size = StringUtils.length(StringUtils.leftPad(valor, 2, "0"));
        String sizeString = Integer.toString(size);
        String sizeFinal = StringUtils.leftPad(sizeString, 2, "0");


        return id + sizeFinal + valor;
    }

    private String getPointOfInitiationMethod() {
        String pagamentoUnico = getValue(ID_POINT_OF_INITIATION_METHOD, "12");
        return pagamentoUnico;
    }


    private String getMerchantAccountInformation() {
        //Domínio do banco
        String gui = getValue(ID_MERCHANT_ACCOUNT_INFORMATION_GUI, "br.gov.bcb.pix");

        //Location URL do QRCode dinâmico
        String urlLocation = getValue(ID_MERCHANT_ACCOUNT_INFORMATION_URL, getUrlLocation());

        //String completa da conta
        return getValue(ID_MERCHANT_ACCOUNT_INFORMATION, gui + urlLocation);
    }

    private String getAdditionalDataFieldTemplate() {

        //Reference Label
        String referenceLabel = getValue(ID_ADDITIONAL_DATA_FIELD_TEMPLATE_TXID, "***");

        //RETORNA O VALOR COMPLETO
        return getValue(ID_ADDITIONAL_DATA_FIELD_TEMPLATE, referenceLabel);

    }

    /**
     * Método responsável por calcular o valor da hash de validação do código pix
     *
     * @return string
     */
    private String getCRC16(String payload) {

        //Adiciona dados gerais no payload e depois calcula
        payload += ID_CRC16 + "04";

        int crc = 0xFFFF;          // initial value
        int polynomial = 0x1021;   // 0001 0000 0010 0001  (0, 5, 12)

        // byte[] testBytes = "*********".getBytes("ASCII");
        byte[] bytes = payload.getBytes();

        for (byte b : bytes) {
            for (int i = 0; i < 8; i++) {
                boolean bit = ((b >> (7 - i) & 1) == 1);
                boolean c15 = ((crc >> 15 & 1) == 1);
                crc <<= 1;
                if (c15 ^ bit) crc ^= polynomial;
            }
        }
        crc &= 0xffff;
        String resultado = ID_CRC16 + "04" + StringUtils.leftPad(Integer.toHexString(crc), 4, "0").toUpperCase();
        return resultado;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantCity() {
        return merchantCity;
    }

    public void setMerchantCity(String merchantCity) {
        this.merchantCity = merchantCity;
    }

    public String getUrlLocation() {
        return urlLocation;
    }

    public void setUrlLocation(String urlLocation) {
        this.urlLocation = urlLocation;
    }
}

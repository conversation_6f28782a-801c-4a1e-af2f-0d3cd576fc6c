package servicos.pix;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import com.sun.xml.ws.util.StringUtils;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.UteisEmail;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;

public class PixEmailService {

    private static final String enderecoModeloEmailPix = "/br/com/pactosolucoes/comuns/util/resources/emailPix.txt";

    public static void enviarEmail(String email, PixVO pixVO, String baseUrl, EmpresaVO empresaVO, String chave,
                            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO, UsuarioVO usuarioVO) throws Exception {
        UteisEmail uteisEmail = new UteisEmail();
        String nomeEmpresa = StringUtils.capitalize(empresaVO.getNome().toLowerCase());
        uteisEmail.novo("Pix - " + nomeEmpresa, configuracaoSistemaCRMVO);
        uteisEmail.setRemetente(usuarioVO);

        String emailHtml = emailHtml(pixVO, baseUrl, empresaVO, chave);
        uteisEmail.enviarEmail(email, pixVO.getDevedorNome(), emailHtml, empresaVO.getNome());
    }

    public static String emailHtml(PixVO pixVO, String baseUrl, EmpresaVO empresaVO,
                                   String chave) throws IOException, URISyntaxException {

        File arq = new File(PixEmailService.class.getResource(enderecoModeloEmailPix).toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        String nomeEmpresa = StringUtils.capitalize(empresaVO.getNome().toLowerCase());
        return texto.toString().replace("{{zwUrl}}", baseUrl)
                .replace("{{valor}}", pixVO.getValorFormatado())
                .replace("{{empresa}}", nomeEmpresa)
                .replace("{{qrCodeUrl}}", pixVO.obterUrlCompartilharQRcode(chave));
    }

    public static String emailHtmlSendy() throws IOException, URISyntaxException {
        File arq = new File(PixEmailService.class.getResource(enderecoModeloEmailPix).toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());
        return texto.toString().replace("{{zwUrl}}", "[ZW_URL,fallback=]")
                .replace("{{valor}}", "[VALOR_PIX,fallback=]")
                .replace("{{empresa}}", "[EMPRESA_PIX,fallback=]")
                .replace("{{qrCodeUrl}}", "[QRCODE_URL_PIX,fallback=]");
    }
}

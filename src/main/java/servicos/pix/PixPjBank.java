package servicos.pix;

import controle.arquitetura.threads.ThreadRobo;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoPJBankEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.impl.pix.PixWebhookService;
import servicos.integracao.pjbank.api.PJBankClient;
import servicos.integracao.pjbank.exceptions.PJBankException;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

public class PixPjBank extends SuperServico implements PixServiceInterfaceFacade {

    public PixPjBank(Connection con) throws Exception {
        super(con);
    }

    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws IOException, PixRequestException, PJBankException {

        PJBankClient client = new PJBankClient("recebimentos/" + pixVO.getConveniocobranca().getCredencialPJBank() + "/transacoes/" + pixVO.getPedidoNumero(), pixVO.getConveniocobranca().getAmbiente());
        HttpDelete httpDelete = client.getHttpDeleteClient();
        httpDelete.addHeader("x-chave", pixVO.getConveniocobranca().getChavePJBank());

        HttpResponse response = client.doRequest(httpDelete);
        String resposta = EntityUtils.toString(response.getEntity());

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setResposta(resposta);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        PJBankClient client = new PJBankClient("recebimentos/" + convenioCobrancaVO.getCredencialPJBank() + "/transacoes/" + idExterno, convenioCobrancaVO.getAmbiente());
        HttpGet httpGet = client.getHttpGetClient();
        httpGet.addHeader("x-chave", convenioCobrancaVO.getChavePJBank());

        HttpResponse response = client.doRequest(httpGet);
        String resposta = EntityUtils.toString(response.getEntity());

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setResposta(resposta);

        if (resposta.equals("[]")) {
            //aqui quer dizer que o boleto está cancelado na pjbank
            pixRequisicaoDto.setPixDto(new PixDto());
            pixRequisicaoDto.getPixDto().setStatus(PixStatusEnum.CANCELADA.getDescricao());
        } else {
            JSONArray jsonArray;
            try {
                 jsonArray = new JSONArray(resposta);
            } catch (Exception ex) {
                JSONObject jsonObject = new JSONObject(resposta);
                if (!UteisValidacao.emptyString(jsonObject.optString("msg"))) {
                    if (jsonObject.optString("msg").equals("Credencial não localizada.")) {
                        throw new Exception("Credencial PJBank não localizada!");
                    } else {
                        throw new Exception("Erro: " + jsonObject.optString("msg"));
                    }
                } else {
                    throw new Exception("Não foi possível converter o retorno da PjBank: " + ex.getMessage());
                }
            }
            JSONObject jsonObject = jsonArray.optJSONObject(0);
            pixRequisicaoDto.setPixDto(preencherObtejoRetorno(jsonObject));
        }
        return pixRequisicaoDto;
    }

    public PixDto preencherObtejoRetorno(JSONObject jsonObject) throws Exception {
        PixDto pixDto = new PixDto();
        pixDto.setTxid(jsonObject.optString("nosso_numero"));
        if (UteisValidacao.emptyString(pixDto.getTxid())) {
            pixDto.setTxid(jsonObject.optString("id_unico"));
        }
        if (!UteisValidacao.emptyString(jsonObject.optString("data_pagamento"))) {
            pixDto.setDataPagamento(Uteis.getDate(jsonObject.optString("data_pagamento"), "MM/dd/yyyy"));
            pixDto.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());
        } else {
            pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());
        }
        if (!UteisValidacao.emptyString(jsonObject.optString("data_credito"))) {
            pixDto.setDataCredito(Uteis.getDate(jsonObject.optString("data_credito"), "MM/dd/yyyy"));
        }

        pixDto.setPedidoNumero(jsonObject.optString("pedido_numero"));
        pixDto.setVencimento(Uteis.getDate(jsonObject.optString("data_vencimento"), "MM/dd/yyyy"));
        pixDto.setUrlQRcode(jsonObject.optString("link"));
        pixDto.setTextoImagemQRcode(jsonObject.optString("qrcode"));
        pixDto.setLinkInfo(jsonObject.optString("link_info"));

        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(jsonObject.optString("valor"));
        pixDto.setValor(pixValorDto);

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setNome(jsonObject.optString("pagador"));
        pixDto.setDevedor(pixDevedorDto);

        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto();
        pixDto.setCalendario(pixCalendarioDto);

        return pixDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                          String nomeDevedor, String telefoneDevedor, Double valor,
                                          String descricao, Integer expiracao, String chave) throws Exception {

        Uteis.logarDebug("Iniciou criarCobranca PixPjbank");

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setNome(nomeDevedor);
        pixDevedorDto.setCpf(cpfDevedor);

        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(String.valueOf(valor));

        PixDto pixDto = new PixDto();
        pixDto.setDevedor(pixDevedorDto);
        pixDto.setVencimento(Uteis.somarDias(Calendario.hoje(), expiracao));
        pixDto.setValor(pixValorDto);
        pixDto.setPedidoNumero(gerarNumeroAleatorio());

        //CRIAR PIX
        PixRequisicaoDto pixRequisicaoDto = criarCobranca(convenioCobrancaVO, pixDto, chave);
        validateResponseError(pixRequisicaoDto.getResposta(), 0);
        JSONObject pixCriado = new JSONObject(pixRequisicaoDto.getResposta());

        Uteis.logarDebug("criarCobranca PixPjbank - criarPix resposta: " + pixRequisicaoDto.getResposta());

        int tentativas = 1;
        boolean conseguiuConsultar = false;

        //CONSULTAR PIX
        while (tentativas <= 7) {
            //aguardar 3 segundos pelo menos antes de consultar o pix a primeira vez.
            ThreadRobo.sleep(3000);
            try {
                PixRequisicaoDto retornoConsultarPix = consultarCobranca(pixCriado.optString("nossonumero"), convenioCobrancaVO);

                Uteis.logarDebug("criarCobranca PixPjbank - Tentativa: " + tentativas + ", consultarPix resposta: " + retornoConsultarPix.getResposta());

                if (!UteisValidacao.emptyString(retornoConsultarPix.getPixDto().getTextoImagemQRcode())) {
                    pixDto = retornoConsultarPix.getPixDto(); //sobrepor resposta, pois aqui está mais completa
                    pixDto.setCalendario(new PixCalendarioDto(expiracao, true));
                    pixDto.setDevedor(pixDevedorDto);
                    pixDto.setValor(pixValorDto);
                    conseguiuConsultar = true;
                    pixRequisicaoDto.setResposta(retornoConsultarPix.getResposta().toString()); //sobrepor resposta, pois aqui está mais completa
                    break;
                }
            } catch (Exception ignore) {
            }
            tentativas++;
        }

        //se não consultar com sucesso, deve cancelar o que foi gerado
        if (!conseguiuConsultar) {
            PixVO pixVO = new PixVO();
            pixVO.setConveniocobranca(convenioCobrancaVO);
            pixVO.setPedidoNumero(pixDto.getPedidoNumero());
            try {
                cancelar(pixVO);
            } catch (Exception ignore) {
                throw new Exception("Não foi possível gerar o pix por instabilidade na PJBank. Gerou mas não conseguiu consultar nem cancelar. Tente novamente");
            }
            //conseguiu cancelar, lança essa exceção aqui
            throw new Exception("Não foi possível gerar o pix por instabilidade na PJBank. Gerou mas não conseguiu consultar. Tente novamente");
        }

        //chegou até aqui, tudo ok!
        pixDto.setStatus(PixStatusEnum.ATIVA.getDescricao());
        pixRequisicaoDto.setPixDto(pixDto);

        Uteis.logarDebug("Fim criarCobranca PixPjbank");

        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        PJBankClient client = new PJBankClient("recebimentos/" + convenioCobrancaVO.getCredencialPJBank() + "/transacoes", convenioCobrancaVO.getAmbiente());
        HttpPost httpPost = client.getHttpPostClient();
        DateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");

        JSONObject params = new JSONObject();

        params.put("nome_cliente", pixDto.getDevedor().getNome());
        params.put("cpf_cliente", pixDto.getDevedor().getCpf());
        params.put("vencimento", dateFormat.format(pixDto.getVencimento()));
        params.put("valor", pixDto.getValor().getOriginal());
        params.put("pedido_numero", pixDto.getPedidoNumero());
        params.put("pix", TipoBoletoPJBankEnum.PIX.getParametro()); // gerar somente o pix

        //Opcional. Caso não queira a atualização do vencimento do boleto de forma automática, utilizar este parâmetro, informando o valor 1. length (0-1).
        params.put("nunca_atualizar_boleto", 1);

        PixWebhookService pixWebhookService = new PixWebhookService(this.getCon());
        String urlCallback = pixWebhookService.montarUrlCallbackWebhook(convenioCobrancaVO, chave);
        if (UteisValidacao.emptyString(urlCallback)) {
            throw new Exception("Não foi possível criar URLCallback do pix");
        }
        params.put("webhook", urlCallback);

        httpPost.setEntity(new StringEntity(params.toString(), StandardCharsets.UTF_8));

        //gerar a cobrança na API da PjBank
        HttpResponse response = client.doRequest(httpPost);

        if (response != null && response.getStatusLine().getStatusCode() != 201
                && response.getStatusLine().getStatusCode() != 200) {
            throw new Exception("Não foi possível gerar o pix. " +
                    response.getStatusLine().getStatusCode() + " " + response.getStatusLine().getReasonPhrase());
        }

        String resposta = EntityUtils.toString(response.getEntity());

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(params.toString());
        pixRequisicaoDto.setResposta(resposta);

        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {
        return null;
    }

    @Override
    public void validateResponseError(String resposta, int status) throws PixRequestException {
        JSONObject json;
        try {
            json = new JSONObject(resposta);
        } catch (Exception ex) {
            throw new PixRequestException("Não foi possível gerar o pix. ");
        }
        if (!UteisValidacao.emptyString(json.optString("status")) && !json.optString("status").equals("200") && !json.optString("status").equals("201")) {
            if (!UteisValidacao.emptyString(json.optString("msg"))) {
                throw new PixRequestException("Não foi possível consultar o pix. " +
                        "Motivo: " + json.getString("msg"));
            } else {
                throw new PixRequestException("Não foi possível consultar o pix");
            }
        }
    }

    public String gerarNumeroAleatorio() {
        SimpleDateFormat sdf = new SimpleDateFormat("ddHHmmss");
        Double vlrRandom = 0.0;
        vlrRandom = Math.random() * Double.valueOf(sdf.format(Calendario.hoje()));
        return String.valueOf(vlrRandom.intValue());
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        return null;
    }

    @Override
    public String fixResponseErros(String responseJson) {
        return null;
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {
    }

    @Override
    public String translateMessages(String message) {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws IOException, PixRequestException {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public String token(PixVO pixVO) throws Exception {
        return null;
    }

    @Override
    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        return false;
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return false;
    }

    @Override
    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {
        return null;
    }

    @Override
    public HttpClient createConnector() {
        return ExecuteRequestHttpService.createConnector();
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws IOException, PixRequestException {
        return null;
    }
}

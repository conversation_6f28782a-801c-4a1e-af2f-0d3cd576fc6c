package servicos.pix;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

public class PixSantander implements PixServiceInterfaceFacade {

    //Santander ainda não possui o método patch para cancelar um pix, foi incluído um tratamento em cada lugar que chama o endpoint cancelar
    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/v1/cob/" + pixVO.getTxid();

        HttpPatch patch = new HttpPatch(url);
        patch.addHeader("Content-Type", "application/json");
        patch.addHeader("Authorization", "Bearer " + token(pixVO.getConveniocobranca()));
        String body = "{\"status\": \"REMOVIDA_PELO_USUARIO_RECEBEDOR\"}";

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body);

        StringEntity params = new StringEntity(body);
        patch.setEntity(params);

        HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());
        HttpResponse response = httpClient.execute(patch);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public HttpClient createConnector() {
        return ExecuteRequestHttpService.createConnector();
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return ExecuteRequestHttpService.createConnector(path, senha);
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/v1/cob/" + pixVO.getTxid();

        HttpGet get = new HttpGet(url);
        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token(pixVO.getConveniocobranca()));

        HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());
        HttpResponse response = httpClient.execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {
        String url = apiUrl(pixVO.getAmbienteEnum()) + "/v1/cob/" + pixVO.getTxid();

        HttpGet get = new HttpGet(url);
        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token(pixVO.getConveniocobranca()));

        HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());
        HttpResponse response = httpClient.execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                          String nomeDevedor, String telefoneDevedor, Double valor,
                                          String descricao, Integer expiracao) throws Exception {

        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto(expiracao);

        PixDto pixDto = new PixDto();
        pixDto.setCalendario(pixCalendarioDto);
        pixDto.setChave(convenioCobrancaVO.getPixChave());

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        pixDevedorDto.setCpf(cpfDevedor.replaceAll("\\.|-", ""));
        pixDevedorDto.setNome(Uteis.removerCaracteresNaoAscii(nomeDevedor));
        pixDto.setDevedor(pixDevedorDto);

        pixDto.setSolicitacaoPagador(descricao);
        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(Uteis.formatarValorEmRealSemAlterarPontuacao(valor));
        pixDto.setValor(pixValorDto);

        String txId = gerarTxId();
        pixDto.setTxid(txId);

        PixRequisicaoDto retornoPixDto = criarCobranca(convenioCobrancaVO, pixDto);
        String textoQrCode = gerarTextoQrCode(retornoPixDto, convenioCobrancaVO);
        retornoPixDto.getPixDto().setTextoImagemQRcode(textoQrCode);
        retornoPixDto.getPixDto().getDevedor().setTelefone(UteisTelefone.removerCaracteresEspeciais(telefoneDevedor));
        return retornoPixDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {
        String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/v1/cob/" + pixDto.getTxid();
        HttpPut post = new HttpPut(url);

        String token = token(convenioCobrancaVO);
        post.addHeader("Content-Type", "application/json");
        post.addHeader("Authorization", "Bearer " + token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body(pixDto));
        StringEntity params = new StringEntity(pixRequisicaoDto.getEnvio());
        post.setEntity(params);

        HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());
        HttpResponse response = httpClient.execute(post);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {

        URIBuilder builder = new URIBuilder(apiAuthUrl(convenioCobrancaVO.getAmbiente()));
        builder.setParameter("grant_type", "client_credentials")
                .setParameter("scope", "cob.read cob.write pix.read pix.write webhook.read webhook.write");

        HttpPost post = new HttpPost(builder.build());
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("client_id", convenioCobrancaVO.getPixClientId()));
        params.add(new BasicNameValuePair("client_secret", convenioCobrancaVO.getPixClientSecret()));

        post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        post.addHeader("Content-Type", "application/x-www-form-urlencoded");

        HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());
        if (httpClient == null) {
            throw new Exception("Não foi possível obter a conexão. Se o problema persistir entre em contato com a Pacto");
        }
        HttpResponse response = httpClient.execute(post);
        return responseTokenDto(response).getAccess_token();
    }

    @Override
    public String token(PixVO pixVO) {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/v1/webhook/" + convenioCobrancaVO.getPixChave();
            HttpGet get = new HttpGet(url);
            get.addHeader("Content-Type", "application/json");
            get.addHeader("Authorization", "Bearer " + token(convenioCobrancaVO));

            HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());
            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio. Se o problema persistir, pode ser a senha do certificado que foi informada incorretamente ou o certificado não é válido.");
            }

            HttpResponse response = httpClient.execute(get);
            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            return new JSONObject(responseJsonString);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        try {
            //Endpoint configurar webhook DO BANCO
            String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/v1/webhook/" + convenioCobrancaVO.getPixChave();
            HttpPut put = new HttpPut(url);
            put.addHeader("Content-Type", "application/json");
            put.addHeader("Authorization", "Bearer " + token(convenioCobrancaVO));

            //URL de callback NOSSA que o banco vai chamar nas requisições
            JSONObject body = new JSONObject();
            body.put("webhookUrl", urlCallback);

            StringEntity params = new StringEntity(body.toString());
            put.setEntity(params);

            HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());

            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio. Se o problema persistir, pode ser a senha do certificado que foi informada incorretamente ou o certificado não é válido.");
            }

            HttpResponse response = httpClient.execute(put);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                return true;
            }

            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            //chegou até aqui, não deu certo, então lançar exceção com a response da request em String
            throw new Exception(responseJsonString);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            //Endpoint Excluir webhook DO BANCO
            String url = apiUrl(convenioCobrancaVO.getAmbiente()) + "/v1/webhook/" + convenioCobrancaVO.getPixChave();
            HttpDelete delete = new HttpDelete(url);
            delete.addHeader("Content-Type", "application/json");
            delete.addHeader("Authorization", "Bearer " + token(convenioCobrancaVO));

            HttpClient httpClient = createConnector(uriPrivateCert(), senhaPrivateCert());

            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio. Se o problema persistir, pode ser a senha do certificado que foi informada incorretamente ou o certificado não é válido.");
            }

            HttpResponse response = httpClient.execute(delete);
            int statusCode = response.getStatusLine().getStatusCode();

            //quando excluir com sucesso eles retornam (204 No Content)
            if (statusCode == 204) {
                return true;
            }

            //chegou até aqui, não deu certo, então lançar exceção com a response da request em String
            //tentar obter motivo do erro
            StringBuilder sb = new StringBuilder();
            try {
                String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = new JSONObject(responseJsonString);
                sb.append(jsonObject.getString("title")).append(" | ").append(jsonObject.getString("detail"));
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(sb.toString())) {
                throw new Exception("Não foi possível excluir o webhook. Motivo não informado pelo banco.");
            } else {
                throw new Exception(sb.toString());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String gerarTextoQrCode(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO) {
        PayloadPix payloadPix = new PayloadPix();
        payloadPix.setUrlLocation(pixRequisicaoDto.getPixDto().getLocation());
        String nomeEmpresa = Uteis.retirarAcentuacao(convenioCobrancaVO.getEmpresa().getNome());
        payloadPix.setMerchantName(nomeEmpresa.replaceAll("-", ""));
        payloadPix.setMerchantName(payloadPix.getMerchantName().replaceAll(" ", ""));
        if (payloadPix.getMerchantName().length() > 25) {
            payloadPix.setMerchantName(payloadPix.getMerchantName().substring(0, 25));
        }
        payloadPix.setMerchantCity(convenioCobrancaVO.getEmpresa().getCidade_Apresentar().length() > 15 ? Uteis.retirarAcentuacao(convenioCobrancaVO.getEmpresa().getCidade_Apresentar()).substring(0, 15) : Uteis.retirarAcentuacao(convenioCobrancaVO.getEmpresa().getCidade_Apresentar()));
        String textoQrCode = payloadPix.gerarPayload();
        return textoQrCode;
    }

    public String gerarTxId() {
        //No Bradesco e no Santander é obrigatório informar o txId para gerar o pix
        String txId = UUID.randomUUID().toString().replaceAll("-", "");
        if (txId.length() > 35) {
            txId = txId.substring(0, 35);
        }
        return txId;
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorDto.class);
    }

    @Override
    public String fixResponseErros(String responseJson) {
        if (responseJson.contains("\"criacao\":\"-03:00\"")) {
            responseJson = responseJson.replace("\"criacao\":\"-03:00\"", "\"criacao\":\"" + Calendario.format("yyyy-MM-dd'T'HH:mm:ss") + "\"");
        }

        return responseJson;
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        int status = response.getStatusLine().getStatusCode();
        responseJsonString = fixResponseErros(responseJsonString);
        validateResponseError(responseJsonString, status);
        Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
        pixRequisicaoDto.setPixDto(json.fromJson(responseJsonString, PixDto.class));

        //setar a data de pagamento/quitação do pix que vem no json de retorno quando o pix está pago
        try {
            JSONObject retornoJSON = null;
            retornoJSON = new JSONObject(responseJsonString);
            String dataPagamentoString = retornoJSON.getJSONArray("pix").getJSONObject(0).getString("horario");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(dataPagamentoString);
            pixRequisicaoDto.getPixDto().setDataPagamento(dataPagamento);
        } catch (Exception ignore) {
        }
    }

    @Override
    public String translateMessages(String message) {
        if (message.contains("Unauthorized")) {
            return "Requisição não autorizada. Confira as chaves de aplicação no cadastro do convênio.";
        }
        if (message.contains("Internal Server Error")) {
            return "Não foi possível gerar a cobrança nos servidores do pix. Aguarde alguns segundos, recarregue a página e tente novamente.";
        }
        if (message.contains("Chave do destinatário inexistente") || message.contains("Dados inválidos")) {
            return "A chave pix da conta configurada no convênio está incorreta! Verifique qual chave pix (cnpj, telefone, email ou aleatória) está vinculada para a conta do seu banco e configure ela no convênio.";
        }
        if (message.contains("O CPF informado invalido")) {
            return "O CPF do aluno é inválido";
        }
        if (message.contains("ClientId is Invalid")) {
            return "O Client Id está incorreto na tela convênio de cobrança.";
        }
        if (message.contains("Client credentials are invalid")) {
            return "O Client Secret está incorreto na tela convênio de cobrança.";
        }
        return message;
    }

    @Override
    public void validateResponseError(String responseJsonString, int status) throws PixRequestException {
        if (status != 200 && status != 201) {
            String mensagemErro = "";
            try {
                PixResponseErrorDto pixResponseErrorDto = responseErrorDto(responseJsonString);
                if (pixResponseErrorDto != null && pixResponseErrorDto.getErros() != null && pixResponseErrorDto.getErros().size() > 0) {
                    for (PixErroDto pixErroDto : pixResponseErrorDto.getErros()) {
                        mensagemErro += pixErroDto.getMensagem() + " ";
                        if (pixErroDto.getOcorrencia() != null && !pixErroDto.getOcorrencia().trim().isEmpty()) {
                            mensagemErro += "Ocorrencia: " + pixErroDto.getOcorrencia() + " ";
                        }
                    }
                }

                if (pixResponseErrorDto != null && mensagemErro.trim().isEmpty() && !pixResponseErrorDto.getErrorDescription().trim().isEmpty()) {
                    mensagemErro = pixResponseErrorDto.getErrorDescription();
                }
            } catch (Exception e) {
                mensagemErro = translateMessages(responseJsonString);
            }
            throw new PixRequestException(mensagemErro);
        }
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        Gson json = new Gson();
        return json.fromJson(responseJsonString, TokenVO.class);
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }


    private String apiUrl(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
            return PropsService.getPropertyValue(PropsService.urlApiPixSantanderSandbox);
        } else {
            return PropsService.getPropertyValue(PropsService.urlApiPixSantanderProducao);
        }
    }

    private String apiAuthUrl(AmbienteEnum ambienteEnum) {
        if (ambienteEnum.equals(AmbienteEnum.HOMOLOGACAO)) {
            return PropsService.getPropertyValue(PropsService.urlApiPixSantanderAuthSandbox);
        } else {
            return PropsService.getPropertyValue(PropsService.urlApiPixSantanderAuthProducao);
        }
    }

    private String uriPrivateCert() {
        return PropsService.getPropertyValue(PropsService.uriCertPrivado);
    }

    private String senhaPrivateCert() {
        return Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaCertPrivado), PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
    }

}

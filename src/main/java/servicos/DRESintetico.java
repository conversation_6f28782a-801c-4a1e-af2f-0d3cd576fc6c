/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package servicos;

import br.com.pactosolucoes.ce.comuns.enumerador.Mes;
import br.com.pactosolucoes.comuns.json.DemonstrativoFinanceiroJSON;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.DRESinteticoDWVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.ConfiguracaoFinanceiro;
import negocio.facade.jdbc.financeiro.DRESinteticoDW;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;
import relatorio.negocio.jdbc.financeiro.*;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class DRESintetico {

    public static Double processarDRE(Connection con, Integer empresa, Integer nrMeses){
        return processarDRE(con, empresa, nrMeses, false);
    }

    public static Double processarDRE(Connection con, Integer empresa, Integer nrMeses, boolean ticketmedioreceita){
        return processarDRE(con,empresa,nrMeses,ticketmedioreceita,Calendario.hoje());
    }

    public static Double processarDRE(Connection con, Integer empresa, Integer nrMeses, boolean ticketmedioreceita, Date dataBase){
        return processarDRE(con, empresa, nrMeses, ticketmedioreceita, dataBase, null);
    }

    public static Double processarDRE(Connection con, Integer empresa, Integer nrMeses, boolean ticketmedioreceita, Date dataBaseFim, Date dataBaseInicio) {
        Double valorReceita = null;
        try{
            RelatorioDRE relatorioDRE = new RelatorioDRE();
            Map<Integer, DRESinteticoDWVO> dreSintetico = new HashMap<Integer,DRESinteticoDWVO>();
            Map<Date,List<DemonstrativoFinanceiroJSON>> detalhesJSON = new HashMap<Date, List<DemonstrativoFinanceiroJSON>>();

            ConfiguracaoFinanceiro configuracaoFinanceiro = new ConfiguracaoFinanceiro(con);
            ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = configuracaoFinanceiro.consultar();
            configuracaoFinanceiro = null;

            //calcular datas de consulta
            if (dataBaseInicio == null) {
                dataBaseInicio = Uteis.somarCampoData(dataBaseFim, Calendar.MONTH, nrMeses == null ? -5 : nrMeses);
            }

            Calendar dataInicialRel = Calendario.getInstance();
            dataInicialRel.setTime(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataBaseInicio)));
            Calendar dataFinalRel = Calendario.getInstance();
            dataFinalRel.setTime(Uteis.obterUltimoDiaMesUltimaHora(dataBaseFim));

            //receita
            List<DemonstrativoFinanceiro> listaDF = relatorioDRE.gerarDREApartirConexao(con, TipoRelatorioDF.RECEITA,
                    dataInicialRel, dataFinalRel, empresa, new ArrayList<Integer>(), true,
                    TipoFonteDadosDF.TODAS, configuracaoFinanceiroVO.getUsarCentralEventos(),
                    false, false,  null, true);
            processarLista(listaDF, true, dreSintetico, false);

            if(ticketmedioreceita){
                int mesData = Uteis.getMesData(dataBaseFim);
                Mes mes = Mes.getMesPeloCodigo(mesData);
                if(dreSintetico.get(mes.getCodigo()) == null){
                    valorReceita = 0.0;
                }else{
                    valorReceita = dreSintetico.get(mes.getCodigo()).getReceita();
                }
            }

            for (DemonstrativoFinanceiro obj : listaDF) {
                for(TotalizadorMesDF total : obj.getListaTotalizadorMeses()){
                    Date data = Uteis.obterPrimeiroDiaMes(total.getMesProcessar().getDataIni().getTime());
                    List<DemonstrativoFinanceiroJSON> listaJson = detalhesJSON.get(data);
                    if(listaJson == null){
                        listaJson = new ArrayList<DemonstrativoFinanceiroJSON>();
                        detalhesJSON.put(data, listaJson);
                    }
                    DemonstrativoFinanceiroJSON json = new DemonstrativoFinanceiroJSON();
                    json.setMesAno(Uteis.getDataAplicandoFormatacao(data, "MM/yyyy"));
                    json.setCodigo(obj.getCodigoAgrupador());
                    json.setEntrada(obj.getTipoES() == null ? false : obj.getTipoES().equals(TipoES.ENTRADA));
                    json.setNome(obj.getNomeAgrupador());
                    json.setValor(total.getTotalNivel());
                    listaJson.add(json);
                }
            }

            relatorioDRE = new RelatorioDRE();
            //faturamento
            listaDF = relatorioDRE.gerarDREApartirConexao(con, TipoRelatorioDF.FATURAMENTO,
                    dataInicialRel, dataFinalRel, empresa, new ArrayList<Integer>(), true,
                    TipoFonteDadosDF.TODAS, configuracaoFinanceiroVO.getUsarCentralEventos(),
                    false, false,  null, true);
            processarLista(listaDF, false, dreSintetico, false);


            relatorioDRE = new RelatorioDRE();
            //competencia
            listaDF = relatorioDRE.gerarDREApartirConexao(con, TipoRelatorioDF.FATURAMENTO,
                    dataInicialRel, dataFinalRel, empresa, new ArrayList<Integer>(), true,
                    TipoFonteDadosDF.TODAS, configuracaoFinanceiroVO.getUsarCentralEventos(),
                    false, false,  null, true);
            processarLista(listaDF, false, dreSintetico, true);

            //salvar em banco
            DRESinteticoDW dao = new DRESinteticoDW(con);
            dao.incluir(dreSintetico.values(), empresa);
        }catch(Exception e){
            e.printStackTrace();
        }
        return valorReceita;
    }



    public static void main(String ... args) throws SQLException{
        Connection con1 = DriverManager.getConnection("jdbc:postgresql://*************:5432/bdzillyontestegame", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(con1);
        processarDRE(con1, 5, null);
    }

    private static void processarLista(List<DemonstrativoFinanceiro> listaDF, boolean receita, 
            Map<Integer, DRESinteticoDWVO> sintetico, boolean competencia) {
        List<DemonstrativoFinanceiro> listaTotalizadoresDF = new ArrayList<DemonstrativoFinanceiro>();
        DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Total Entrada(+): ");
        listaTotalizadoresDF.add(df);
        df = new DemonstrativoFinanceiro();
        df.setNomeAgrupador("Total Saída(-): ");
        listaTotalizadoresDF.add(df);
        
        // Criar lista dos meses que serão processados
        List<MesProcessar> listaMesProcessar = new ArrayList<MesProcessar>();
        if (listaDF.size() > 0) {
            DemonstrativoFinanceiro dfRel = listaDF.get(0);
            for (TotalizadorMesDF obj : dfRel.getListaTotalizadorMeses()) {
                listaMesProcessar.add(obj.getMesProcessar());
            }
        }
        // Criar a lista de meses para cada totalizador.
        for (DemonstrativoFinanceiro demonstrativo : listaTotalizadoresDF) {
            RelatorioDemonstrativoFinanceiro.criarTotalizadoresMeses(demonstrativo, listaMesProcessar);
        }

        for (DemonstrativoFinanceiro obj : listaDF) {
            for (TotalizadorMesDF totalizadorMesDf : obj.getListaTotalizadorMeses()) {
                if(totalizadorMesDf.getTotalEntradaNivel() > 0.0) {
                    Double totalEntradaNivel = 0.0;
                    for (LancamentoDF lancamentoDF : totalizadorMesDf.getListaLancamentos()) {
                        try {
                            if(lancamentoDF.getValorLancamento()  > 0.0) {
                                totalEntradaNivel += lancamentoDF.getValorLancamento();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    totalizadorMesDf.setTotalEntradaNivel(totalEntradaNivel);
                }
                if(totalizadorMesDf.getTotalSaidaNivel() < 0.0) {
                    Double totalSaida = 0.0;
                    for (LancamentoDF lancamentoDF : totalizadorMesDf.getListaLancamentos()) {
                        try {
                            if(lancamentoDF.getValorLancamento()  < 0.0) {
                                totalSaida += lancamentoDF.getValorLancamento();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    totalizadorMesDf.setTotalSaidaNivel(totalSaida);
                }
                if (obj.getCodigoAgrupador().length() == 3) {
                    totalizarListaTotalizadoresDF(totalizadorMesDf, listaTotalizadoresDF);
                }
            }
            
        }

        DemonstrativoFinanceiro dfEntrada = listaTotalizadoresDF.get(0);
        for(TotalizadorMesDF totalizadorMesDf : dfEntrada.getListaTotalizadorMeses()){
                setarMes(sintetico, totalizadorMesDf, false, receita, competencia);
        }

        if(receita){
            DemonstrativoFinanceiro dfSaida = listaTotalizadoresDF.get(1);
            for(TotalizadorMesDF totalizadorMesDf : dfSaida.getListaTotalizadorMeses()){
                    setarMes(sintetico, totalizadorMesDf, true, receita, competencia);
            }
        }

    }

    private static void setarMes(Map<Integer, DRESinteticoDWVO> sintetico, TotalizadorMesDF totalizadorMesDf, boolean saida, boolean receita, boolean competencia){
        String[] dadosMes = totalizadorMesDf.getMesProcessar().getNomeMes().split("/");
        Mes mes = Mes.getMesPelaDescricao(dadosMes[0].trim());
            if(mes != null && !mes.equals(Mes.VAZIO)){
                DRESinteticoDWVO sinteticoMes = sintetico.get(mes.getCodigo());
                if(sinteticoMes == null){
                    sinteticoMes = new DRESinteticoDWVO();
                    sinteticoMes.setAno(Integer.valueOf(dadosMes[1].trim()));
                    sinteticoMes.setMes(mes.getCodigo());
                    sintetico.put(mes.getCodigo(), sinteticoMes);
                }
                if(saida){
                    sinteticoMes.setDespesa(sinteticoMes.getDespesa() + totalizadorMesDf.getTotalNivel());
                }else if (receita) {
                    sinteticoMes.setReceita(sinteticoMes.getReceita() + totalizadorMesDf.getTotalNivel());
                }else {
                    if (!competencia){
                        sinteticoMes.setFaturamento(sinteticoMes.getFaturamento() + totalizadorMesDf.getTotalNivel());
                    }
                }

                if (competencia) {
                    sinteticoMes.setCompetencia(sinteticoMes.getCompetencia() + totalizadorMesDf.getTotalNivel());
                }

            }
    }

    private static void totalizarListaTotalizadoresDF(TotalizadorMesDF totalizadorMesDf, List<DemonstrativoFinanceiro> listaTotalizadoresDF) {
        DemonstrativoFinanceiro dfEntrada = listaTotalizadoresDF.get(0);
        DemonstrativoFinanceiro dfSaida = listaTotalizadoresDF.get(1);

        
        int indice;
        //Somar os valores das Entradas.
        indice = dfEntrada.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        TotalizadorMesDF totMesDF = dfEntrada.getListaTotalizadorMeses().get(indice);
        totMesDF.setTotalNivel(totalizadorMesDf.getTotalEntradaNivel().isNaN() ? 0.0 : totalizadorMesDf.getTotalEntradaNivel());
        
        //dfEntrada.setTotalTodosMeses(dfEntrada.getTotalTodosMeses() + totMesDF.getTotalNivel());

        //Somar os valores das Saídas.
        indice = dfSaida.getListaTotalizadorMeses().indexOf(totalizadorMesDf);
        TotalizadorMesDF  totMesDFSaida = dfSaida.getListaTotalizadorMeses().get(indice);

        totMesDFSaida.setTotalNivel(totalizadorMesDf.getTotalSaidaNivel().isNaN() ? 0.0 : totalizadorMesDf.getTotalSaidaNivel());
        
        //dfSaida.setTotalTodosMeses(dfSaida.getTotalTodosMeses() + totMesDF.getTotalNivel());
    }
    
}

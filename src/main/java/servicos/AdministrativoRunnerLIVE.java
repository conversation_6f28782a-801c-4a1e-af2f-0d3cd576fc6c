package servicos;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.RoboControle;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.arquitetura.RoboVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.acesso.IntegracaoMember;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.oamd.RedeEmpresaVO;
import servicos.acesso.IntegracaoMemberService;
import servicos.impl.microsservice.acessosistema.AcessoSistemaMSService;
import servicos.oamd.RedeEmpresaService;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class AdministrativoRunnerLIVE {

    private static final SimpleDateFormat FORMATADOR_DATA = new SimpleDateFormat("dd/MM/yyyy");

    public static void main(String[] args) {
        Uteis.debug = true;
        Uteis.logar("Entrou no Administrativo Runner");
        if (args.length == 0) {
            args = new String[]{"bdzillyonlivefitnessintegracaoacesso-2025-04-07"};
        }
        if (args.length >= 1) {
            String chave = args[0];
            Connection con = null;
            try {
                DAO dao = new DAO();
                con = dao.obterConexaoEspecifica(chave);
                Conexao.guardarConexaoForJ2SE(con);
                //atualizar banco de dados, se defasado
                LoginControle control = new LoginControle();
                control.setUsername("RECOR");
                control.setSenha(PropsService.getPropertyValue(PropsService.RECORRENCIA_USER_PASSWORD));
                control.setUserOamd("adm");
                control.login();
                control.atualizarBD();

                RoboControle roboControle = new RoboControle();
                roboControle.inicializarRobo();
                RoboVO robo = roboControle.getRobo();
                if (args.length >= 2) {
                    robo.setDia(FORMATADOR_DATA.parse(args[1]));
                    Calendario.dia = FORMATADOR_DATA.parse(args[1]);
                }

                processarIntegracaoEVO(robo, chave, con);

                processarAlunosPlanosVIP(robo, chave, con);

                Logger.getLogger(AdministrativoRunnerLIVE.class.getName()).log(
                        Level.INFO, " Finalizando AdministrativoRunner em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});

            } catch (Exception ex) {
                Uteis.logarDebug("Erro: " + ex.getMessage());
                ex.printStackTrace();
                Logger.getLogger(AdministrativoRunnerLIVE.class.getName()).log(Level.SEVERE,
                        "Erro ao obter conexao especifica com a chave " + chave, ex);
            } finally {
                Uteis.finalizarExecutor(1);
            }
        }
    }

    public static void processarIntegracaoEVO(RoboVO robo, final String chave, Connection con) throws Exception {
        try {
            boolean possuiIntegracaoConfigurada = false;
            Uteis.logar(null, "INICIALIZA Processamento Integracao EVO - em: " + Uteis.getDataComHora(Calendario.hoje()));
            RedeEmpresaService.limparMapaDeRedes();

            IntegracaoMemberService integracaoMemberService = new IntegracaoMemberService(con, chave, null, TipoOperacaoIntegracaoMembersEnum.MEMBERS_FREEPASS);
            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                IntegracaoMember integracaoMemberDAO = new IntegracaoMember(con);
                List<IntegracaoMemberVO> integracoes = integracaoMemberDAO.consultarIntegracoesPorEmpresa(empresa.getCodigo());
                if (!integracoes.isEmpty()) {
                    integracaoMemberService.consultarOuCriarProdutoFreePassIntegracao();
                    possuiIntegracaoConfigurada = true;
                }
                Uteis.logar(null, "possuiIntegracaoConfigurada:  " + possuiIntegracaoConfigurada);

                for (IntegracaoMemberVO integracaoMemberVO : integracoes) {
                    Uteis.logar(null, "Processando integracao " + integracaoMemberVO.getDescricao());
                    integracaoMemberService.sincronizar(integracaoMemberVO);
                }
            }

            if (possuiIntegracaoConfigurada) {
                Uteis.logar(null, "Vou integrar members...");
                integracaoMemberService.integrarMembers();
            }

            Uteis.logar(null, "Processamento Integracao EVO - em: " + Uteis.getDataComHora(Calendario.hoje()));
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarIntegracaoEVO | Erro: " + ex.getMessage());
        }
    }

    public static void processarAlunosPlanosVIP(RoboVO robo, final String chave, Connection con) {
        try {
            Uteis.logar(null, "INICIOU PROCESSO DE ALUNOS VIPS - em: " + Uteis.getDataComHora(Calendario.hoje()));

            RedeEmpresaService.limparMapaDeRedes();
            RedeEmpresaVO redeEmpresa = RedeEmpresaService.obterRedePorChave(chave);
            if (redeEmpresa == null || !redeEmpresa.getGestaoRedes()) {
                return;
            }

            Uteis.logar(null, "REDE DE EMPRESA " + redeEmpresa.getNome() + " - em: " + Uteis.getDataComHora(Calendario.hoje()));

            Cliente clienteDAO = new Cliente(con);
            for (EmpresaVO empresa : robo.getListaEmpresa()) {
                List<ClienteVO> clientesVipsNaoSincronizados = clienteDAO.consultarClientesVipsNaoSincronizados(empresa.getCodigo());

                Uteis.logar(null, "Empresa: " + empresa.getNome() + " - Quantidade: " + clientesVipsNaoSincronizados.size() + " - em: " + Uteis.getDataComHora(Calendario.hoje()));

                for (ClienteVO cliente : clientesVipsNaoSincronizados) {
                    if (cliente.getSincronizadoRedeEmpresa() == null) {
                        AutorizacaoAcessoGrupoEmpresarialVO autorizacao = AcessoSistemaMSService.findByCPF(cliente.getPessoa().getCfp(), null, redeEmpresa);
                        if (UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                            autorizacao = new AutorizacaoAcessoGrupoEmpresarialVO(cliente);
                            autorizacao = AcessoSistemaMSService.insertAccessAuthorization(autorizacao, chave, cliente.getEmpresa().getCodigo(), redeEmpresa);
                        } else {
                            AutorizacaoAcessoGrupoEmpresarialVO autorizacaoModelo = new AutorizacaoAcessoGrupoEmpresarialVO(cliente);
                            autorizacao.setCodigoPessoa(autorizacaoModelo.getCodigoPessoa());
                            autorizacao.setCodAcesso(autorizacaoModelo.getCodAcesso());
                            autorizacao.setCodigoGenerico(autorizacaoModelo.getCodigoGenerico());
                            autorizacao.setAssinaturaBiometriaDigital(autorizacaoModelo.getAssinaturaBiometriaDigital());
                            autorizacao.setAssinaturaBiometriaFacial(autorizacaoModelo.getAssinaturaBiometriaFacial());
                            autorizacao = AcessoSistemaMSService.updateAccessAuthorization(autorizacao, chave, cliente.getEmpresa().getCodigo(), redeEmpresa);
                        }
                        AcessoSistemaMSService.publish(autorizacao, false, redeEmpresa);

                        if (!UteisValidacao.emptyNumber(autorizacao.getCodigo())) {
                            clienteDAO.alterarSincronizadoRedeEmpresa(cliente.getCodigo());
                        }
                    }
                }
            }

            Uteis.logar(null, "FINALIZOU PROCESSO DE ALUNOS VIPS - em: " + Uteis.getDataComHora(Calendario.hoje()));

        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarAlunosPlanosVIP | Erro: " + ex.getMessage());
        }
    }
}

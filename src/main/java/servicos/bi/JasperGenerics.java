package servicos.bi;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ReflectionType;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JRPrintPage;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.design.JRDesignBand;
import net.sf.jasperreports.engine.design.JRDesignExpression;
import net.sf.jasperreports.engine.design.JRDesignField;
import net.sf.jasperreports.engine.design.JRDesignFrame;
import net.sf.jasperreports.engine.design.JRDesignImage;
import net.sf.jasperreports.engine.design.JRDesignLine;
import net.sf.jasperreports.engine.design.JRDesignParameter;
import net.sf.jasperreports.engine.design.JRDesignQuery;
import net.sf.jasperreports.engine.design.JRDesignSection;
import net.sf.jasperreports.engine.design.JRDesignStaticText;
import net.sf.jasperreports.engine.design.JRDesignStyle;
import net.sf.jasperreports.engine.design.JRDesignTextField;
import net.sf.jasperreports.engine.design.JRDesignVariable;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.JRHtmlExporter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRRtfExporter;
import net.sf.jasperreports.engine.export.JRXhtmlExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.export.JRXmlExporter;
import net.sf.jasperreports.engine.export.JRXmlExporterParameter;
import net.sf.jasperreports.engine.export.oasis.JROdsExporter;
import net.sf.jasperreports.engine.export.oasis.JROdtExporter;
import net.sf.jasperreports.engine.export.ooxml.JRDocxExporter;
import net.sf.jasperreports.engine.export.ooxml.JRPptxExporter;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;
import net.sf.jasperreports.engine.type.CalculationEnum;
import net.sf.jasperreports.engine.type.EvaluationTimeEnum;
import net.sf.jasperreports.engine.type.FillEnum;
import net.sf.jasperreports.engine.type.HorizontalAlignEnum;
import net.sf.jasperreports.engine.type.LineDirectionEnum;
import net.sf.jasperreports.engine.type.ModeEnum;
import net.sf.jasperreports.engine.type.PositionTypeEnum;
import net.sf.jasperreports.engine.type.SplitTypeEnum;
import net.sf.jasperreports.engine.type.StretchTypeEnum;
import net.sf.jasperreports.engine.type.VerticalAlignEnum;
import servicos.bi.exportador.formatadores.FormatadorEnum;

import java.awt.*;
import java.io.File;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 17/08/2012
 */
public class JasperGenerics {

    private String sql;
    private String path = this.getClass().getResource("").getPath();
    private String prefixoArquivo = "JasperGenerics";
    private List<Connection> conexoes = new ArrayList();
    private List dados;
    private List<ReflectionType> fields = new LinkedList<ReflectionType>();
    private JasperPrint merged;
    private File destFile;
    private boolean fileNameDefault = true;
    private Map<String, String> labels = new LinkedHashMap<String, String>();
    private Map<String, String> functions = new LinkedHashMap<String, String>();
    private static final Map<String, Class> wrappers = new HashMap<String, Class>();
    private int wTotal = 0;
    private int wFrame = 0;
    private int wPerColumn = 0;
    private JRDesignStyle boldStyle;
    private JRDesignStyle tituloStyle;
    private JRDesignStyle subTituloStyle;
    private JRDesignStyle normalStyle;
    private JRDesignStyle italicStyle;
    private JRDesignStyle styleRotuloZillyon;
    JRDesignFrame frame;
    private String titulo = "";
    private String subTitulo = "";
    private String usuario = "";
    private String informacoesRodape = "";
    private EmpresaVO empresa = new EmpresaVO();
    private String filtro = "";
    private java.io.InputStream logo = null;
    private InputStream imagemGrafico;

    static {
        wrappers.put("int", Integer.class);
        wrappers.put("long", Long.class);
        wrappers.put("double", Double.class);
        wrappers.put("float", Float.class);
        wrappers.put("boolean", Boolean.class);
        wrappers.put("char", Character.class);
        wrappers.put("byte", Byte.class);
        wrappers.put("short", Short.class);

    }

    public void putLabels(final String[][] labels) {
        for (int i = 0; i < labels.length; i++) {
            String[] strings = labels[i];
            putLabel(strings[0], strings[1]);
        }
    }

    public void putLabel(final String atributo, final String label) {
        labels.put(atributo, label);
    }

    public void putFunction(final String atributo, final String tipo) {
        functions.put(atributo, tipo);
    }

    public JasperGenerics() {
    }

    public JasperGenerics(List l) {
        this.dados = l;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public List<Connection> getConexoes() {
        return conexoes;
    }

    public void setConexoes(List<Connection> conexoes) {
        this.conexoes = conexoes;
    }

    public File getDestFile() {
        return destFile;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List getDados() {
        return dados;
    }

    public void setDados(List dados) {
        this.dados = dados;
    }

    public boolean isFileNameDefault() {
        return fileNameDefault;
    }

    public void setFileNameDefault(boolean fileNameDefault) {
        this.fileNameDefault = fileNameDefault;
    }

    public String getPrefixoArquivo() {
        return prefixoArquivo;
    }

    public void setPrefixoArquivo(String prefixoArquivo) {
        this.prefixoArquivo = prefixoArquivo.replace("/", "-");
    }

    private static Connection obterConexao(String nomeHost, String porta, String superUser, String senha, String nomeBanco)
            throws Exception {
        Conexao conex = new Conexao("jdbc:postgresql://" + nomeHost + ":" + porta + "/" + nomeBanco, superUser, senha);
        return conex.getConexao();
    }

    /**
     *
     */
    public void prepare(boolean cabecalhoRodape, boolean summary) throws Exception {
        compile(cabecalhoRodape, summary);
        writeXml();
        fill();
    }

    public void compile(boolean cabecalhoRodape, boolean summary) throws Exception {
        initColumns();
        long start = System.currentTimeMillis();
        JasperDesign jasperDesign = draw(cabecalhoRodape, summary, getEmpresa().getMoeda());
        JasperCompileManager.compileReportToFile(jasperDesign,
                getPathPrefixo() + ".jasper");
        System.err.println("Compile time : " + (System.currentTimeMillis() - start));
    }

    /**
     *
     */
    public void writeXml() throws JRException {
        long start = System.currentTimeMillis();
        JasperCompileManager.writeReportToXmlFile(getPathPrefixo() + ".jasper");
        System.err.println("XML design creation time : " + (System.currentTimeMillis() - start));
    }

    /**
     *
     */
    public void fill() throws JRException, SQLException, Exception {
        long start = System.currentTimeMillis();
        //Preparing parameters
        Map parameters = new HashMap();
        parameters.put("logo", getLogo());
        if (getImagemGrafico() != null) {
            parameters.put("imagemgrafico", getImagemGrafico());
        }
        final String jasper = getPathPrefixo() + ".jasper";
        List<JasperPrint> lista = new ArrayList<JasperPrint>();

        if (dados == null) {
            for (Connection connection : conexoes) {
                try {
                    lista.add(JasperFillManager.fillReport(jasper, parameters, connection));
                } catch (Exception e) {
                    Uteis.logar(e, JasperGenerics.class);
                } finally {
                    connection.close();
                }
            }
        } else {
            lista.add(JasperFillManager.fillReport(jasper, parameters,
                    new JRBeanArrayDataSource(dados.toArray())));
        }

        if (!lista.isEmpty()) {
            List<JasperPrint> tmpLst = new ArrayList(lista);
            for (JasperPrint o : tmpLst) {
                if (o.getPages().isEmpty()) {
                    lista.remove(o);
                }
            }
            if (!lista.isEmpty()) {
                JasperPrint jpUnique = lista.get(0);

                for (JasperPrint jasperPrint : lista) {
                    if (jasperPrint.equals(jpUnique)) {
                        continue;
                    }
                    for (JRPrintPage obj : (List<JRPrintPage>) jasperPrint.getPages()) {
                        if (obj.getElements() != null && !obj.getElements().isEmpty()) {
                            obj.getElements().remove(0);
                        }
                        jpUnique.addPage(obj);
                    }

                }
                jpUnique.setProperty("net.sf.jasperreports.print.keep.full.text", "true");
                jpUnique.setProperty("net.sf.jasperreports.export.xls.detect.cell.type", "true");
                jpUnique.setProperty("net.sf.jasperreports.export.xls.white.page.background", "false");

                merged = jpUnique;
            }
        }

        System.err.println("Filling time : " + (System.currentTimeMillis() - start));

    }

    /**
     *
     */
    public void test() throws JRException {
        try {
            System.out.println(path);
            compile(false, false);
            writeXml();
            fill();
            pdf();
            xlsx();
            xmlEmbed();
            xml();
            html();
            rtf();
            csv();
            odt();
            ods();
            docx();
            pptx();
            xhtml();
        } catch (Exception ex) {
            Logger.getLogger(JasperGenerics.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    private void initColumns() throws Exception {
        if (dados != null) {
            fields = UtilReflection.getFields(labels, dados);
        } else {
            fields = UtilReflection.getFields(SuperFacadeJDBC.criarConsulta(getSql(),
                    conexoes.get(0)));
        }
    }

    /**
     *
     */
    public String getPathPrefixo() {
        return path + prefixoArquivo;
    }

    public File newFile(final String extension) {
        return new File(getPathPrefixo() + "." + extension);
    }

    public void pdf() throws JRException {
        long start = System.currentTimeMillis();
        //
        destFile = newFile("pdf");
        JRPdfExporter exporter = new JRPdfExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE, destFile);
        exporter.exportReport();
        //
        System.err.println("PDF creation time : " + (System.currentTimeMillis() - start));
    }

    public void rtf() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("rtf");
        JRRtfExporter exporter = new JRRtfExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.exportReport();
        System.err.println("RTF creation time : " + (System.currentTimeMillis() - start));
    }

    public void xml() throws JRException {
        long start = System.currentTimeMillis();
        //
        destFile = newFile("xml");
        JRXmlExporter exporter = new JRXmlExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.exportReport();
        //
        System.err.println("XML creation time : " + (System.currentTimeMillis() - start));
    }

    public void xmlEmbed() throws JRException {
        long start = System.currentTimeMillis();
        //
        destFile = newFile("embbed.xml");
        JRXmlExporter exporter = new JRXmlExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.setParameter(JRXmlExporterParameter.IS_EMBEDDING_IMAGES, Boolean.TRUE);
        exporter.exportReport();
        //
        System.err.println("XML creation time : " + (System.currentTimeMillis() - start));
    }

    public void html() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("html");
        JRHtmlExporter exporter = new JRHtmlExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.exportReport();
        System.err.println("HTML creation time : " + (System.currentTimeMillis() - start));
    }

    public void csv() throws JRException {
        long start = System.currentTimeMillis();
        //
        destFile = newFile("csv");
        JRCsvExporter exporter = new JRCsvExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.exportReport();
        //
        System.err.println("CSV creation time : " + (System.currentTimeMillis() - start));
    }

    public void odt() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("odt");

        JROdtExporter exporter = new JROdtExporter();

        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());

        exporter.exportReport();

        System.err.println("ODT creation time : " + (System.currentTimeMillis() - start));
    }

    public void ods() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("ods");

        JROdsExporter exporter = new JROdsExporter();

        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.TRUE);

        exporter.exportReport();

        System.err.println("ODS creation time : " + (System.currentTimeMillis() - start));
    }

    public void docx() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("docx");

        JRDocxExporter exporter = new JRDocxExporter();

        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());

        exporter.exportReport();

        System.err.println("DOCX creation time : " + (System.currentTimeMillis() - start));
    }

    public void xlsx() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("xls");

        JRXlsxExporter exporter = new JRXlsxExporter();
        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());
        exporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
        exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
        exporter.setParameter(JRXlsExporterParameter.IS_COLLAPSE_ROW_SPAN, Boolean.TRUE);

        exporter.exportReport();

        System.err.println("XLSX creation time : " + (System.currentTimeMillis() - start));
    }

    public void pptx() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("pptx");

        JRPptxExporter exporter = new JRPptxExporter();

        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());

        exporter.exportReport();

        System.err.println("PPTX creation time : " + (System.currentTimeMillis() - start));
    }

    public void xhtml() throws JRException {
        long start = System.currentTimeMillis();
        destFile = newFile("xhtml");

        JRXhtmlExporter exporter = new JRXhtmlExporter();

        exporter.setParameter(JRExporterParameter.JASPER_PRINT, merged);
        exporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, destFile.toString());

        exporter.exportReport();

        System.err.println("XHTML creation time : " + (System.currentTimeMillis() - start));
    }

    private int addColumnsTitle(int x, int y, int width, String title,
                                JRDesignStyle style, JRDesignFrame frame) {
        JRDesignStaticText staticText = new JRDesignStaticText();
        staticText.setX(x);
        staticText.setY(y);
        staticText.setWidth(width);
        staticText.setHeight(15);
        staticText.setForecolor(Color.BLACK);
        staticText.setBackcolor(new Color(172, 190, 206));
        staticText.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        staticText.setMode(ModeEnum.OPAQUE);
        staticText.setBold(Boolean.TRUE);
        staticText.setVerticalAlignment(VerticalAlignEnum.MIDDLE);
        staticText.setStyle(style);
        staticText.setHorizontalAlignment(HorizontalAlignEnum.LEFT);
        String label = labels.get(title);
        label = label != null ? label : title;
        staticText.setText(label);
        frame.addElement(staticText);
        return staticText.getWidth();
    }

    private int addColumnsDetail(int x, int y, int width, JRDesignStyle style,
                                 ReflectionType field, JRDesignBand band, String moeda) {
        JRDesignTextField textField = new JRDesignTextField();
        textField.setX(x);
        textField.setY(y);
        textField.setWidth(width);
        textField.setHeight(15);
        textField.setPositionType(PositionTypeEnum.FLOAT);
        textField.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        textField.setStretchWithOverflow(true);
        textField.setStyle(style);
        textField.setBlankWhenNull(true);

        JRDesignExpression expression = new JRDesignExpression();
        expression.setValueClass(defineType(field.getType()));

        if (field.getFormatador() != null) {
            if (field.getFormatador().permiteClasse(field.getType())) {
                textField.setPattern(field.getFormatador().getPattern());
                expression.setText(field.getFormatador().getText(field.getName()));
                expression.setValueClass(field.getFormatador().getClasseFormatada());
            }
        } else {
            if (expression.getValueClass() == Double.class || expression.getValueClass() == double.class) {
                textField.setPattern( moeda + " #,##0.00");
            }
            if(null == field.getFunctionType() || field.getFunctionType().isEmpty()){
                expression.setText("$F{" + field.getName() + "}");
            }else{
                expression.setText(String.format("$F{%s}.%s", field.getName(), field.getFunctionType()));
            }

        }
        textField.setExpression(expression);
        band.addElement(textField);
        return textField.getWidth();
    }

    private int addSummary(int x, int y, int width, JRDesignStyle style,
                           String attName, Class tipo, JRDesignBand band) {
        JRDesignTextField textField = new JRDesignTextField();
        textField.setX(x);
        textField.setY(y);
        textField.setWidth(width);
        textField.setHeight(15);
        textField.setPositionType(PositionTypeEnum.FLOAT);
        textField.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        textField.setStretchWithOverflow(true);
        textField.setStyle(style);

        JRDesignExpression expression = new JRDesignExpression();

        if (tipo == Double.class || tipo == double.class) {
            expression.setValueClass(String.class);
            expression.setText(String.format("$V{%s} != null ? new java.text.DecimalFormat(\"#,##0.00\").format($V{%s}):\"NULO\"",
                    new Object[]{attName, attName}));

        } else {
            expression.setValueClass(defineType(tipo));
            expression.setText(String.format("$V{%s}",
                    new Object[]{attName}));
        }
        textField.setExpression(expression);

        band.addElement(textField);
        return textField.getWidth();
    }

    private int addBlank(int x, int y, int width, JRDesignStyle style, JRDesignBand band) {
        JRDesignStaticText staticText = new JRDesignStaticText();
        staticText.setX(x);
        staticText.setY(y);
        staticText.setWidth(width);
        staticText.setHeight(15);
        staticText.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        staticText.setMode(ModeEnum.OPAQUE);
        staticText.setBold(Boolean.TRUE);
        staticText.setHorizontalAlignment(HorizontalAlignEnum.CENTER);
        staticText.setStyle(style);
        staticText.setHorizontalAlignment(HorizontalAlignEnum.LEFT);
        staticText.setText("");
        band.addElement(staticText);
        return staticText.getWidth();
    }

    private Class defineType(Class f) {
        if (f == Integer.class
                || f == int.class
                || f == Double.class
                || f == double.class
                || f == Date.class
                || f == Timestamp.class
                || f == Boolean.class
                || f == boolean.class
                || f == Long.class
                || f == List.class
                || f == Collection.class
                || f == long.class) {
            if (f.isPrimitive()) {
                return wrappers.get(f.getSimpleName());
            }
            return f;
        } else {
            return String.class;
        }
    }

    private JasperDesign configureReport() throws Exception {
        if(fields == null){
            throw new ConsistirException("Informações insuficientes para criação do arquivo");
        }
        int nAttr = fields.size();
        wTotal = (nAttr * 100) + 200;
        if (wTotal < 600) {
            wTotal = 600;
        }
        wFrame = wTotal - 10;
        wPerColumn = ((wFrame - 20) / nAttr);
        JasperDesign design = new JasperDesign();
        design.setName("JasperGenerics");
        design.setPageWidth(wTotal);
        int pageHeight = 842;
        if(getFiltro() != null && !getFiltro().isEmpty()){
            pageHeight = 910;
        }
        design.setPageHeight(pageHeight);
        design.setColumnWidth(1);
        design.setColumnSpacing(0);
        design.setLeftMargin(20);
        design.setRightMargin(20);
        design.setTopMargin(10);
        design.setBottomMargin(20);

        //Fonts
        normalStyle = new JRDesignStyle();
        normalStyle.setName("Sans_Normal");
        normalStyle.setDefault(true);
        normalStyle.setFontName("SansSerif");
        normalStyle.setFontSize(8);
        normalStyle.setPdfFontName("Helvetica");
        normalStyle.setPdfEncoding("Cp1252");
        normalStyle.setPdfEmbedded(false);
        design.addStyle(normalStyle);

        boldStyle = new JRDesignStyle();
        boldStyle.setName("Sans_Bold");
        boldStyle.setFontName("Arial");
        boldStyle.setFontSize(10);
        boldStyle.setBold(true);
        boldStyle.setPdfFontName("Helvetica-Bold");
        boldStyle.setPdfEncoding("Cp1252");
        boldStyle.setPdfEmbedded(false);
        design.addStyle(boldStyle);

        italicStyle = new JRDesignStyle();
        italicStyle.setName("Sans_Italic");
        italicStyle.setFontName("SansSerif");
        italicStyle.setFontSize(8);
        italicStyle.setItalic(true);
        italicStyle.setPdfFontName("Helvetica-Oblique");
        italicStyle.setPdfEncoding("Cp1252");
        italicStyle.setPdfEmbedded(false);
        design.addStyle(italicStyle);

        tituloStyle = new JRDesignStyle();
        tituloStyle.setName("Sans_Title");
        tituloStyle.setFontName("SansSerif");
        tituloStyle.setFontSize(20);
        tituloStyle.setBold(true);
        tituloStyle.setPdfFontName("Helvetica-Bold");
        tituloStyle.setHorizontalAlignment(HorizontalAlignEnum.CENTER);
        tituloStyle.setVerticalAlignment(VerticalAlignEnum.TOP);
        tituloStyle.setPdfEncoding("Cp1252");
        tituloStyle.setPdfEmbedded(false);
        design.addStyle(tituloStyle);

        subTituloStyle = new JRDesignStyle();
        subTituloStyle.setName("Sans_SubTitle");
        subTituloStyle.setFontName("SansSerif");
        subTituloStyle.setFontSize(12);
        subTituloStyle.setBold(true);
        subTituloStyle.setPdfFontName("Helvetica-Bold");
        subTituloStyle.setHorizontalAlignment(HorizontalAlignEnum.LEFT);
        subTituloStyle.setVerticalAlignment(VerticalAlignEnum.TOP);
        subTituloStyle.setPdfEncoding("Cp1252");
        subTituloStyle.setPdfEmbedded(false);
        design.addStyle(subTituloStyle);


        styleRotuloZillyon = new JRDesignStyle();
        styleRotuloZillyon.setName("Sans_Zillyon");
        styleRotuloZillyon.setFontName("Microsoft Sans Serif");
        styleRotuloZillyon.setFontSize(9);
        styleRotuloZillyon.setBold(true);
        styleRotuloZillyon.setItalic(true);
        styleRotuloZillyon.setPdfFontName("Helvetica-Bold");
        styleRotuloZillyon.setPdfEncoding("Cp1252");
        styleRotuloZillyon.setPdfEmbedded(false);
        design.addStyle(styleRotuloZillyon);

        //Parameters
        JRDesignParameter parameter = new JRDesignParameter();
        parameter.setName("ReportTitle");
        parameter.setValueClass(java.lang.String.class);
        design.addParameter(parameter);

        parameter = new JRDesignParameter();
        parameter.setName("OrderByClause");
        parameter.setValueClass(java.lang.String.class);
        design.addParameter(parameter);

        //Query
        JRDesignQuery query = new JRDesignQuery();
        query.setText(getSql());

        design.setQuery(query);
        return design;

    }

    private void configurePageHeader(JasperDesign design, boolean cabecalhoRodape) throws  Exception{
        int x = 0;
        int y = 0;

        int bandHeight = 110;
        int frameX = 85;

        if(getFiltro() != null && !getFiltro().isEmpty()){
            bandHeight = 150;
            frameX = 128;
        }

        //Page Title
        JRDesignBand band = new JRDesignBand();
        band.setHeight(bandHeight);

        frame = new JRDesignFrame();
        frame.setX(0);
        frame.setY(frameX);
        frame.setWidth(wTotal - 40);
        frame.setHeight(15);
        frame.setForecolor(new Color(172, 190, 206));
        frame.setBackcolor(new Color(172, 190, 206));
        frame.setMode(ModeEnum.OPAQUE);
        band.addElement(frame);

        for (ReflectionType f : fields) {
            int w = wPerColumn;
            if (x + wPerColumn > (wTotal - 40)) {
                w = (wTotal - 40) - (x);
            }
            x += addColumnsTitle(x, y, w, f.getName(), boldStyle, frame);
        }
        if (!cabecalhoRodape) {
            design.setTitle(band);
            return;
        }

        JRDesignImage image = new JRDesignImage(design);
        JRDesignExpression expression = new JRDesignExpression();
        expression.setValueClass(java.io.InputStream.class);
        expression.setText("$P{logo}");

        image.setExpression(expression);
        image.setX(0);
        image.setY(0);
        image.setWidth(82);
        image.setHeight(46);
        image.setPositionType(PositionTypeEnum.FLOAT);
        image.setStretchType(StretchTypeEnum.RELATIVE_TO_BAND_HEIGHT);
        image.setPrintRepeatedValues(true);
        image.setPrintWhenDetailOverflows(true);
        image.setHorizontalAlignment(HorizontalAlignEnum.LEFT);
        image.setVerticalAlignment(VerticalAlignEnum.TOP);
        image.setUsingCache(true);
        band.addElement(image);

        String rotuloZillyon = "ZillyonWeb - Desenvolvido por PACTO Soluções Tecnológicas Ltda.";

        //titulo
        gerarStaticText(band, wTotal-40,0,45, 27, tituloStyle, getTitulo(), VerticalAlignEnum.TOP, HorizontalAlignEnum.CENTER);
        if ((getSubTitulo() != null) && (!getSubTitulo().trim().equals(""))){
            gerarStaticText(band, wTotal-40,0,68, 16, subTituloStyle, getSubTitulo(), VerticalAlignEnum.TOP, HorizontalAlignEnum.LEFT);
        }
        //empresa
        gerarStaticText(band, 200,85, 0, 14, normalStyle, UteisValidacao.emptyString(getEmpresa().getNome()) ? "" : getEmpresa().getNome(), VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.LEFT);
        //Endereço Empresa
        gerarStaticText(band, 200,85, 14,14, normalStyle, UteisValidacao.emptyString(getEmpresa().getNome()) ? "" :  getEmpresa().getEndereco(), VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.LEFT);
        //Cidade Empresa
        gerarStaticText(band, 200,85,28,14, normalStyle, UteisValidacao.emptyString(getEmpresa().getNome()) ? "" :  getEmpresa().getCidade_Apresentar(), VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.LEFT);
        //ZillyonWeb
        gerarStaticText(band, 185,wTotal-185-40,0,23, italicStyle,rotuloZillyon, VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.RIGHT);
        //Contato
        gerarStaticText(band,111,(wTotal-185)+34,23,12, italicStyle,"(0xx62) 3251-5820", VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.RIGHT);
        //Data Impressão
        gerarStaticText(band, 65,(wTotal-185)+35,34,12, normalStyle,"Data impressão:", VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.RIGHT);
        //Data Impressão
        gerarStaticText(band, 60,(wTotal-185)+105, 34, 12, normalStyle, Formatador.formatarDataPadrao(Calendario.hoje()), VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.LEFT);
//        //usuario
//        gerarStaticText(band, 200, wTotal - 200, 12, 12, normalStyle, "Usuário: " + getUsuario(), VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.CENTER);
//        //data e hora
//        gerarStaticText(band, 200, wTotal - 200, 24, 12, normalStyle, Uteis.getDataComHora(Calendario.hoje()), VerticalAlignEnum.MIDDLE, HorizontalAlignEnum.CENTER);



        if(getFiltro() != null && !getFiltro().isEmpty()) {
            gerarStaticText(band, wTotal-40, 0, 85, 12, normalStyle, "Filtros:", VerticalAlignEnum.TOP, HorizontalAlignEnum.CENTER);
            gerarStaticText(band, (wTotal-40)/2, (wTotal-40)/4, 95, 32, normalStyle, getFiltro(), VerticalAlignEnum.TOP, HorizontalAlignEnum.CENTER);
        }
        design.setPageHeader(band);
    }

    private void configurePageFooter(JasperDesign design) {
        //Page Title
        JRDesignBand band = new JRDesignBand();
        band.setHeight(20);

        JRDesignTextField textField = new JRDesignTextField();
        textField.setX(wTotal - 200);
        textField.setY(0);
        textField.setWidth(100);
        textField.setHeight(15);
        textField.setPositionType(PositionTypeEnum.FLOAT);
        textField.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        textField.setStretchWithOverflow(true);
        textField.setStyle(boldStyle);
        textField.setHorizontalAlignment(HorizontalAlignEnum.RIGHT);
        textField.setVerticalAlignment(VerticalAlignEnum.TOP);
        textField.setEvaluationTime(EvaluationTimeEnum.NOW);
        JRDesignExpression expression = new JRDesignExpression();
        expression.setValueClass(String.class);

        expression.setText("\"Página \"+$V{PAGE_NUMBER}+\" de \"");
        textField.setExpression(expression);
        band.addElement(textField);

        JRDesignTextField textField2 = new JRDesignTextField();
        textField2.setX(wTotal - 95);
        textField2.setY(0);
        textField2.setWidth(20);
        textField2.setHeight(15);
        textField2.setPositionType(PositionTypeEnum.FLOAT);
        textField2.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        textField2.setStretchWithOverflow(true);
        textField2.setStyle(boldStyle);
        textField2.setHorizontalAlignment(HorizontalAlignEnum.LEFT);
        textField2.setVerticalAlignment(VerticalAlignEnum.TOP);
        textField2.setEvaluationTime(EvaluationTimeEnum.REPORT);
        JRDesignExpression expression2 = new JRDesignExpression();
        expression2.setValueClass(String.class);

        expression2.setText("\"\"+$V{PAGE_NUMBER}");
        textField2.setExpression(expression2);
        band.addElement(textField2);

        //informacoes
        if (UteisValidacao.emptyString(informacoesRodape)) {
            JRDesignTextField totais = new JRDesignTextField();
            totais.setX(0);
            totais.setY(0);
            totais.setWidth(200);
            totais.setHeight(15);
            totais.setPositionType(PositionTypeEnum.FLOAT);
            totais.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
            totais.setStretchWithOverflow(true);
            totais.setStyle(boldStyle);
            totais.setHorizontalAlignment(HorizontalAlignEnum.LEFT);
            totais.setVerticalAlignment(VerticalAlignEnum.TOP);

            JRDesignExpression totaisEx = new JRDesignExpression();
            totaisEx.setValueClass(String.class);

            totaisEx.setText("\" Total de registros até aqui: \"+$V{REPORT_COUNT}");
            totais.setExpression(totaisEx);
            band.addElement(totais);
        } else {
            gerarStaticText(band, 400, 0, 0, 20,boldStyle, informacoesRodape, VerticalAlignEnum.TOP, HorizontalAlignEnum.LEFT);
        }


        design.setPageFooter(band);
    }

    private void configureImagemGrafico(JasperDesign design) {

        JRDesignBand band = new JRDesignBand();
        band.setHeight(700);

        JRDesignImage imageGrafico = new JRDesignImage(design);
        JRDesignExpression expressionGrafico = new JRDesignExpression();
        expressionGrafico.setValueClass(java.io.InputStream.class);
        expressionGrafico.setText("$P{imagemgrafico}");

        imageGrafico.setExpression(expressionGrafico);
        imageGrafico.setX(130);
        imageGrafico.setY(0);
        imageGrafico.setWidth(380);
        imageGrafico.setHeight(300);
        //  imageGrafico.setHorizontalAlignment(HorizontalAlignEnum.CENTER);
        //   imageGrafico.setPositionType(PositionTypeEnum.FLOAT);
        //   imageGrafico.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        imageGrafico.setUsingCache(true);

        band.addElement(imageGrafico);

        design.setLastPageFooter(band);
    }

    private void gerarStaticText(JRDesignBand band, int width, int x, int y, int height, JRDesignStyle style, String texto, VerticalAlignEnum align, HorizontalAlignEnum alignh) {
        JRDesignStaticText tituloText = new JRDesignStaticText();
        tituloText.setX(x);
        tituloText.setY(y);
        tituloText.setWidth(width);
        tituloText.setHeight(height);
        tituloText.setForecolor(Color.BLACK);
        tituloText.setStretchType(StretchTypeEnum.RELATIVE_TO_TALLEST_OBJECT);
        tituloText.setMode(ModeEnum.OPAQUE);
        tituloText.setBold(Boolean.TRUE);
        tituloText.setVerticalAlignment(align);
        tituloText.setStyle(style);
        tituloText.setHorizontalAlignment(alignh);
        tituloText.setText(texto);
        band.addElement(tituloText);
    }

    private void configureFields(JasperDesign design) throws JRException {
        //Fields
        for (ReflectionType f : fields) {
            JRDesignField field = new JRDesignField();
            field.setName(f.getName());
            field.setValueClass(defineType(f.getType()));
            design.addField(field);
        }

        JRDesignParameter parameter = new JRDesignParameter();
        parameter.setName("logo");
        parameter.setValueClass(java.io.InputStream.class);
        design.addParameter(parameter);

        if (getImagemGrafico() != null) {
            JRDesignParameter parameterGrafico = new JRDesignParameter();
            parameterGrafico.setName("imagemgrafico");
            parameterGrafico.setValueClass(java.io.InputStream.class);
            design.addParameter(parameterGrafico);
        }

    }

    private void configureVariables(JasperDesign design) throws JRException {
        Set<String> atributos = functions.keySet();
        for (String att : atributos) {
            CalculationEnum c = CalculationEnum.valueOf(functions.get(att));
            Class clazz = getType(att);
            JRDesignVariable var = new JRDesignVariable();
            var.setName(c.getName() + "_" + att);
            var.setValueClass(clazz);
            var.setCalculation(c);
            JRDesignExpression exp = new JRDesignExpression();
            exp.setValueClass(clazz);
            exp.setText(String.format("$F{%s}",
                    new Object[]{att}));
            var.setExpression(exp);
            design.addVariable(var);
        }

    }

    private void configureDetails(JasperDesign design, String moeda) {
        //Detail
        JRDesignBand band = new JRDesignBand();
        band.setHeight(20);
        int x = 0;
        int y = 0;

        for (ReflectionType f : fields) {
            x += addColumnsDetail(x, y, wPerColumn, normalStyle, f, band, moeda);
        }

        if (x > wFrame) {
            frame.setWidth(x);
        }

        JRDesignLine line = new JRDesignLine();
        line.setMode(ModeEnum.TRANSPARENT);
        line.setPositionType(PositionTypeEnum.FIX_RELATIVE_TO_TOP);
        line.setDirection(LineDirectionEnum.TOP_DOWN);
        line.setFill(FillEnum.SOLID);
        line.setWidth(wTotal - 40);
        band.addElement(line);

        band.setSplitType(SplitTypeEnum.STRETCH);

        ((JRDesignSection) design.getDetailSection()).addBand(band);

    }

    private void configureSummary(JasperDesign design) {
        //Detail
        JRDesignBand band = new JRDesignBand();
        band.setHeight(20);
        int x = 0;
        int y = 0;

        for (ReflectionType f : fields) {
            if (functions.get(f.getName()) != null) {
                CalculationEnum c = CalculationEnum.valueOf(functions.get(f.getName()));
                Class clazz = getType(f.getName());
                String varName = String.format("%s_%s",
                        new Object[]{c.getName(), f.getName()});

                x += addSummary(x, y, wPerColumn, boldStyle, varName,
                        clazz, band);


            } else {
                x += addBlank(x, y, wPerColumn, boldStyle, band);
            }
        }

        band.setSplitType(SplitTypeEnum.STRETCH);

        design.setSummary(band);

    }

    private Class<?> getType(final String fieldName) {
        for (ReflectionType f : fields) {
            if (f.getName().equals(fieldName)) {
                return defineType(f.getType());
            }
        }
        return null;
    }

    private JasperDesign draw(boolean cabecalhoRodape, boolean summary, String moeda) throws Exception {
        //JasperDesign
        JasperDesign design = configureReport();
        if(!cabecalhoRodape){
            design.setIgnorePagination(Boolean.TRUE);
        } else {
            design.setIgnorePagination(Boolean.FALSE);
        }
        configurePageHeader(design, cabecalhoRodape);
        configureFields(design);
        configureVariables(design);
        configureDetails(design, moeda);
        if (cabecalhoRodape) {
            configurePageFooter(design);
            if (getImagemGrafico() != null) {
                configureImagemGrafico(design);
            }
        }

        if (summary) {
            configureSummary(design);
        }
        return design;
    }

    public static void main1(String[] args) throws Exception {
        try {
            final String host = "app.pactosolucoes.com.br";
            final String sql = "select * from pg_database where datname like('bdzillyon%') order by datname";
            Connection con = obterConexao(host, "5432", "postgres", "pactodb", "postgres");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            JasperGenerics o = new JasperGenerics();
            o.setSql(String.format("SELECT emp.nome, log.entidade, log.acao, count(log.codigo) FROM logcontroleusabilidade "
                    + "log inner join usuario usu on log.usuario = usu.codigo "
                    + "inner join empresa emp on emp.codigo = log.empresa "
                    + "where dataregistro between '%s' and '%s' "
                    + ""
                    + " group by emp.nome, log.entidade, log.acao", new Object[]{
                    "2012-08-01",
                    "2012-10-31"
            }));

            /*o.setSql("select e.email,s.codigocliente,s.situacao,s.situacaocontrato "
             + "from situacaoclientesinteticodw s "
             + "inner join cliente c on c.codigo = s.codigocliente "
             + "inner join pessoa p on p.codigo =  c.pessoa "
             + "inner join email e on e.pessoa = c.pessoa "
             + "where e.email like ('%@%') "
             + "order by s.situacao");*/
            //o.setSql("SELECT e.nome as nomeEmpresa,u.username, u.nome as nomeUsuario,u.serviceusuario,u.servicesenha FROM usuario u inner join colaborador c on u.colaborador = c.codigo inner join empresa e on e.codigo = c.empresa where trim(u.serviceusuario) <> '' order by e.nome");
            //o.setSql("select e.nome, count(l.codigo) from logcontroleusabilidade l inner join empresa e on e.codigo = l.empresa  where l.entidade='WSACESSO' and l.dataregistro between '2012-09-28' and '2012-09-29' group by e.nome");
            //o.setSql("select e.nome, count(ac.codigo) from acessocliente ac inner join cliente c on c.codigo = ac.cliente  inner join empresa e on e.codigo = c.empresa where ac.dthrentrada between '2012-09-28' and '2012-09-29' group by e.nome order by count(ac.codigo)");
            //o.setSql("select dthrentrada from acessocliente order by codigo desc limit 1");
            while (rs.next()) {
                final String nomeBanco = rs.getString("datname");
                Connection c = obterConexao(host, "5432", "postgres", "pactodb", nomeBanco);
                o.conexoes.add(c);
            }
            con.close();
            o.prepare(false, false);
            o.xlsx();
        } catch (JRException ex) {
            Logger.getLogger(JasperGenerics.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void main(String... args) {
        try {
            List l = FacadeManager.getFacade().getContrato().consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JasperGenerics j = new JasperGenerics(l);
            j.putLabels(new String[][]{{"codigo", "Código"},
                    {"situacao", "Situação"},
                    {"vigenciaDe", "Início"},
                    {"pessoa.codigo", "Pessoa"},
                    {"vigenciaAteAjustada", "Término"}});
            j.prepare(false, false);
            j.pdf();
            j.xlsx();
        } catch (Exception ex) {
            Logger.getLogger(JasperGenerics.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public JRDesignStyle getTituloStyle() {
        return tituloStyle;
    }

    public void setTituloStyle(JRDesignStyle tituloStyle) {
        this.tituloStyle = tituloStyle;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public String getInformacoesRodape() {
        return informacoesRodape;
    }

    public void setInformacoesRodape(String informacoesRodape) {
        this.informacoesRodape = informacoesRodape;
    }

    public String getFiltro() {
        return filtro;
    }

    public void setFiltro(String filtro) {
        this.filtro = filtro;
    }

    public InputStream getLogo() {
        return logo;
    }

    public void setLogo(InputStream logo) {
        this.logo = logo;
    }

    public InputStream getImagemGrafico() {
        return imagemGrafico;
    }

    public void setImagemGrafico(InputStream imagemGrafico) {
        this.imagemGrafico = imagemGrafico;
    }

    public JRDesignStyle getSubTituloStyle() {
        return subTituloStyle;
    }

    public void setSubTituloStyle(JRDesignStyle subTituloStyle) {
        this.subTituloStyle = subTituloStyle;
    }

    public String getSubTitulo() {
        return subTitulo;
    }

    public void setSubTitulo(String subTitulo) {
        this.subTitulo = subTitulo;
    }
}

package servicos.bi;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 27/07/2015.
 */
public class CrossTabExcelLinha {

    private String descricao = "";
    private List<CrossTabExcelCelula> colunas = new ArrayList<CrossTabExcelCelula>();
    private Boolean sumarioDemonstrativo = false;
    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<CrossTabExcelCelula> getColunas() {
        return colunas;
    }

    public void addCelula(String valor){
        CrossTabExcelCelula celula = new CrossTabExcelCelula();
        celula.setValorCelula(valor);
        colunas.add(celula);
    }
    public void addCelulaColuna(String valor){
        CrossTabExcelCelula celula = new CrossTabExcelCelula();
        celula.setDescricaoColuna(valor);
        colunas.add(celula);
    }
    public void addCelulaRotulo(String valor){
        CrossTabExcelCelula celula = new CrossTabExcelCelula();
        celula.setDescricaoColuna(valor);
        colunas.add(celula);
    }
    public void addCelula(int index,String valor){
        CrossTabExcelCelula celula = new CrossTabExcelCelula();
        celula.setValorCelula(valor);
        colunas.add(index,celula);
    }

    public Boolean getSumarioDemonstrativo() {
        return sumarioDemonstrativo;
    }

    public void setSumarioDemonstrativo(Boolean sumarioDemonstrativo) {
        this.sumarioDemonstrativo = sumarioDemonstrativo;
    }

    public void setColunas(List<CrossTabExcelCelula> colunas) {
        this.colunas = colunas;
    }
}

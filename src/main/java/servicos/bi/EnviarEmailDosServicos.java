package servicos.bi;

import controle.arquitetura.SuperControle;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;

import java.net.InetAddress;
import servicos.propriedades.PropsService;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 05/03/13
 * Time: 15:38
 */
public class EnviarEmailDosServicos {

    private static String[] emailsPara;

    static {
        String value = PropsService.getPropertyValue(PropsService.emailsNotificarValidacaoBI);
        emailsPara = value.split(",");
    }

    public static void enviarEmail(StringBuilder mensagem, String assunto, String destinatarioSimples) {
        try {
            String[] destino;

            if (UteisValidacao.emptyString(destinatarioSimples)) {
                destino = emailsPara;
            } else {
                destino = new String[]{destinatarioSimples};
            }
            UteisEmail uteis = obterUteisEmail(assunto);
            mensagem.append("<b>From: ").append(InetAddress.getLocalHost().getHostAddress()).append("</b>");
            uteis.enviarEmailN(destino, mensagem.toString(), "RobotRunner - " + assunto, "");
            System.out.println("Email " + assunto + " enviado com SUCESSO!!");
        } catch (Exception ex) {
            System.out.println("EXC.: Não foi possível enviar email " + assunto + ": '"
                    + mensagem.toString()
                    + "' , por causa do seguinte erro de email: " + ex.getMessage());
        }
    }

    public static void enviarEmail(StringBuffer mensagem) {
        try {
            UteisEmail uteis = obterUteisEmail("BI");
            mensagem.append("<b>From: ").append(InetAddress.getLocalHost().getHostAddress()).append("</b>");
            uteis.enviarEmailN(new String[]{"<EMAIL>"}, mensagem.toString(), "RobotRunner - Validação Business Intelligence", "");
            System.out.println("Email BI enviado com SUCESSO!!");
        } catch (Exception ex) {
            System.out.println("EXC.: Não foi possível enviar email BI. MENSAGEM: '"
                    + mensagem.toString()
                    + "' , por causa do seguinte erro de email: " + ex.getMessage());
        }
    }

    private static UteisEmail obterUteisEmail(String assunto) throws Exception {
        System.out.println("Tentando ENVIAR EMAIL - " + assunto);
        UteisEmail uteis = new UteisEmail();
        ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPRobo();
        uteis.novo("RobotRunner - " + assunto, config);
        return uteis;
    }
    
    public static void main(String... args){
        emailsPara = new String[]{"<EMAIL>"};
        enviarEmail(new StringBuilder("Mensagem do corpo do e-mail"), 
                "Assunto do e-mail", "<EMAIL>");
    }
}

package servicos.bi.exportador.reflexao;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Classe que realiza a leitura de uma propriedade de um objeto.
 * Created by johnys on 11/10/2016.
 */
public class LeitorPropriedade {

    /**
     * Nome da propriedade que esta sendo lida. Pode ser usado propriedades compostas como pessoa.cidade.nome.
     * Os nomes das propriedades devem ser da mesma forma que as declaradas na Classe do objeto.
     */
    private String propriedade;

    /**
     * Caso seja uma propriedade composta, concatena os leitores subsequentes para a leitura da propriedade especificada.
     */
    private LeitorPropriedade proximoLeitor;

    /**
     * Armazena o field da propriedade que sera lida.
     */
    private Method metodoLeitor;

    public LeitorPropriedade(String propriedade){
        this.propriedade = propriedade;
        construirLeitores();

    }

    /**
     * Realiza a construição do leitor de propriedades.
     * Caso a propriedade seja composta, inicia o ciclo de concatenação dos leitores.
     */
    private void construirLeitores() {
        if(this.propriedade.contains(".")){
            String proximaPropriedade = this.propriedade.substring(this.propriedade.indexOf(".") + 1);
            this.proximoLeitor = new LeitorPropriedade(proximaPropriedade);
            this.propriedade = this.propriedade.substring(0, this.propriedade.indexOf("."));
        }
    }

    /**
     * Realiza a leitura de uma propriedade de um objeto.
     * Não e possivel utilizar o mesmo leitor para dois objetos de classes diferentes.
     * @param objeto Objeto para qual a propriedade sera lida.
     * @return Valor da propriedade lida.
     * @throws Exception
     */
    public Object ler(Object objeto) throws  Exception{
        if(this.metodoLeitor == null){
            descobrirMetodoLeitor(objeto.getClass());
        }
        Object resultado = metodoLeitor.invoke(objeto);
        return this.proximoLeitor == null || resultado == null ? resultado : this.proximoLeitor.ler(resultado);
    }

    /**
     * Descobre o {@link Field} da propriedade que sera lida.
     * @param clazz
     * @throws Exception
     */
    private void descobrirMetodoLeitor(Class<?> clazz) throws  Exception{
        String metodoGet = "get" + this.propriedade.substring(0, 1).toUpperCase()	+  this.propriedade.substring(1);
        this.metodoLeitor = clazz.getDeclaredMethod(metodoGet);
    }

}

package servicos.bi.exportador.formatadores;

import negocio.comuns.utilitarias.Uteis;
import servicos.bi.exportador.Formatador;

import java.util.Date;

/**
 * Created by johny<PERSON> on 11/10/2016.
 */
public class FormatadorData extends  FormatadorBase<Date>{

    /**
     * Formato de data que será utlizado para formatação;
     */
    public String formato = "dd/MM/yyyy";

    public FormatadorData(){}

    public FormatadorData(String formato){
        this.formato = formato;
    }

    @Override
    public String formatar(Date valor) {
        return Uteis.getDataAplicandoFormatacao(valor, formato);
    }
}

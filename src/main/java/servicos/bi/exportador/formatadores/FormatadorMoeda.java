package servicos.bi.exportador.formatadores;

import servicos.bi.exportador.Formatador;

import java.text.DecimalFormat;

/**
 * Created by johny<PERSON> on 11/10/2016.
 */
public class FormatadorMoeda extends FormatadorBase<Number> {

    private DecimalFormat format = new DecimalFormat("#,###,###,##0.00");

    @Override
    public String formatar(Number valor) {
        return format.format(valor);
    }
}

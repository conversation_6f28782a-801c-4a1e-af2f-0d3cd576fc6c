package servicos.bi.exportador.formatadores;

import servicos.bi.exportador.Formatador;

/**
 * Classe base dos formatadores que implementa o método de <code>formatarObject</code> da interface {@link Formatador}
 * Created by joh<PERSON><PERSON> on 11/10/2016.
 */
public abstract class FormatadorBase<T> implements Formatador<T> {

    public  String formatarObject(Object valor){
       String valorFormatado = null;
        try{
            valorFormatado = formatar((T) valor);
        }catch (Exception e){
            new RuntimeException("Falha ao realizar conversão do valor " + valor + " na formataçao do exportador");
        }
        return valorFormatado;
    }
}

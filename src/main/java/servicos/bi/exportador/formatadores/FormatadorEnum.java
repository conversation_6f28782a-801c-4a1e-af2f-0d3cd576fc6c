package servicos.bi.exportador.formatadores;

import java.util.Date;

/**
 * Created by GlaucoT on 22/02/2017
 */
public enum FormatadorEnum {
    NENHUM("", "", "", String.class),
    MONETARIO_SEM_MOEDA("monetarioSemMoeda", "#,##0.00", "$F{FIELD}", Double.class, Double.class, double.class),
    MONETARIO("monetario", "R$ #,##0.00", "$F{FIELD}", Double.class, Double.class, double.class),
    PORCENTAGEM_SEM_SINAL("porcentagemSemSinal", "#,##0.00", "$F{FIELD}", Double.class, Double.class, double.class),
    PORCENTAGEM("porcentagem", "#,##0.00%", "$F{FIELD}", Double.class, Double.class, double.class),
    PARTE_INTEIRA("inteiro", "#,##0", "$F{FIELD}", Double.class, Double.class, double.class),
    HORA("hora", "HH.mm", "$F{FIELD}", Date.class, Date.class),
    SIM_NAO("sim_nao", "", "$F{FIELD} ? \"SIM\" : \"NÃO\"", String.class, Boolean.class, boolean.class);

    private String identificador;
    private String pattern;
    private String text;
    private Class classeFormatada;
    private Class[] classes;

    FormatadorEnum(String identificador, String pattern, String text, Class classeFormatada, Class... classes) {
        this.identificador = identificador;
        this.pattern = pattern;
        this.text = text;
        this.classeFormatada = classeFormatada;
        this.classes = classes;
    }

    public static FormatadorEnum obterPorIdentificador(String identificador) {
        for (FormatadorEnum formatadorEnum : FormatadorEnum.values()) {
            if (identificador.equals(formatadorEnum.getIdentificador())) {
                return formatadorEnum;
            }
        }
        return FormatadorEnum.NENHUM;
    }

    public String getIdentificador() {
        return identificador;
    }

    public void setIdentificador(String identificador) {
        this.identificador = identificador;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public Class[] getClasses() {
        return classes;
    }

    public void setClasses(Class[] classes) {
        this.classes = classes;
    }

    public String getText(String fieldName) {
        return text.replace("FIELD", fieldName);
    }

    public void setText(String text) {
        this.text = text;
    }

    public Class getClasseFormatada() {
        return classeFormatada;
    }

    public void setClasseFormatada(Class classeFormatada) {
        this.classeFormatada = classeFormatada;
    }

    public boolean permiteClasse(Class valueClass) {
        if (getClasses().length == 0) {
            return false;
        }
        for (Class clazz : getClasses()) {
            if (clazz.equals(valueClass)) {
                return true;
            }
        }
        return false;
    }
}

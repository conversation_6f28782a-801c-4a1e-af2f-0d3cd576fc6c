package servicos.bi.exportador;

import servicos.bi.exportador.formatadores.FormatadorData;
import servicos.bi.exportador.formatadores.FormatadorMoeda;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Classe que realiza a construção do Relatório.
 * Created by johny<PERSON> on 11/10/2016.
 */
public class RelatorioBuilder {

    public static final Formatador<Date> FORMATADOR_DATA = new FormatadorData();

    public static final Formatador<Date> FORMATADOR_DATA_HORA = new FormatadorData("dd/MM/yyyy HH:mm");

    public static final Formatador<Number> FORMATADOR_MOEDA = new FormatadorMoeda();

    private String titulo;

    private String fonte = "Times New Roman";

    private List<Filtro> filtros = new ArrayList<Filtro>();

    private List<Coluna> colunas = new ArrayList<Coluna>();

    private List<Object> dados = new ArrayList<Object>();

    /**
     * Define o título a ser usado no relatório.
     * @param titulo
     * @return
     */
    public RelatorioBuilder titulo(String titulo){
        this.titulo = titulo;
        return this;
    }

    /**
     * Informa a fonte a ser utilizada no relatório.
     * @param fonte
     * @return
     */
    public RelatorioBuilder fonte(String fonte){
        this.fonte = fonte;
        return this;
    }

    /**
     * Adiciona um filtro a ser exibido no relatório.
     * @param nome Nome do filtro
     * @param valor Valor do filtro.
     * @return {@link RelatorioBuilder}
     */
    public RelatorioBuilder addFiltro(String nome, Object valor){
        this.filtros.add(new Filtro(nome, valor));
        return this;
    }

    /**
     * Adiciona um filtro a ser exibido no relatório.
     * @param nome Nome do filtro
     * @param valor Valor do filtro.
     * @param formatador Formatador presente no {@link RelatorioBuilder}. Ele realiza a formatação do valor do filtro que será exibido.
     * @return {@link RelatorioBuilder}
     */
    public RelatorioBuilder addFiltro(String nome, Object valor, Formatador<?> formatador){
        this.filtros.add(new Filtro(nome, valor, formatador));
        return this;
    }

    /**
     * Adiciona uma coluna ao relatório.
     * @param nome Nome da coluna.
     * @param campoObjeto Identificador da propriedade do objeto.
     * @return {@link RelatorioBuilder}
     */
    public RelatorioBuilder addColuna(String nome, String campoObjeto){
        this.colunas.add(new Coluna(nome, campoObjeto));
        return this;
    }

    /**
     * Adiciona uma coluna ao relatório.
     * @param nome Nome da coluna.
     * @param campoObjeto Identificador da propriedade do objeto.
     * @param formatador Formatador presente no {@link RelatorioBuilder}. Ele realiza a formatação do valor da coluna que será exibida.
     * @return {@link RelatorioBuilder}
     */
    public RelatorioBuilder addColuna(String nome, String campoObjeto, Formatador<?> formatador){
        this.colunas.add(new Coluna(nome, campoObjeto, formatador));
        return this;
    }


    /**
     * Adiciona uma lista de objetos que serão exibidos no relatório.
     * @param dados {@link List} de objetos com as propriedades definidas nas colunas.
     * @return {@link RelatorioBuilder}
     */
    public RelatorioBuilder dado(List<? extends Object> dados){
        this.dados.addAll(dados);
        return this;
    }

    public String getTitulo() {
        return titulo;
    }

    public List<Filtro> getFiltros() {
        return filtros;
    }

    public List<Coluna> getColunas() {
        return colunas;
    }

    public List<Object> getDados() {
        return dados;
    }

    public String getFonte() {
        return fonte;
    }
}

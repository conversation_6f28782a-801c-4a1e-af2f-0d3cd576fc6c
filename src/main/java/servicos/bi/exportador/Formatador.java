package servicos.bi.exportador;

/**
 * Classe que repesenta a formatação de um determinado valor para a string de exibição.
 * Created by joh<PERSON><PERSON> on 11/10/2016.
 */
public interface Formatador<T> {

    /**
     * Recebe um valor T e retorna sua representação em string.
     * @param valor Valor a ser formatado.
     * @return String formatada do valor.
     */
    String formatar(T valor);

    /**
     * Realiza a formatação de um valor, realizando a verificação de tipagem de um Object.
     * @param valor
     * @return
     */
    String formatarObject(Object valor);
}

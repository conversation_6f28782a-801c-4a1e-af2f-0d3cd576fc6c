package servicos.bi.exportador;

import servicos.bi.exportador.processador.IProcessador;
import servicos.bi.exportador.processador.ProcessadorExcel;

import java.io.File;

/**
 * Realiza a exportação de um relatório.
 * Created by johny<PERSON> on 11/10/2016.
 */
public class Exportador {

    /**
     * Realiza a exportação de um relatório para excel.
     * @param relatorio
     * @return
     * @throws Exception
     */
    public static void exportarExcel(RelatorioBuilder relatorio, File file) throws  Exception{
        IProcessador processador = new ProcessadorExcel();
        processador.processar(relatorio, file);
    }

}

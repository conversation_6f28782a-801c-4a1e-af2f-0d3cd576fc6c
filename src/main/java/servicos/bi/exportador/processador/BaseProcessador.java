package servicos.bi.exportador.processador;

import org.apache.commons.beanutils.BeanUtils;
import servicos.bi.exportador.Coluna;
import servicos.bi.exportador.Formatador;
import servicos.bi.exportador.RelatorioBuilder;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Classe base dos processadores de relatório.
 * Realiza a leitura das propriedades das linhas que serão exibidas no relatório
 * Created by johny<PERSON> on 11/10/2016.
 */
public abstract class BaseProcessador implements  IProcessador{

    /**
     * Define as linhas que serão exibidas no relatório.
     */
    private List<Object[]> linhas = new ArrayList<Object[]>();

    /**
     * Realiza o processamento das colunas do relatório extraindo os valores das linhas que serão exibidos.
     * @param relatorioBuilder
     */
    public void processar(RelatorioBuilder relatorioBuilder, File arquivo) throws Exception{
        for(Object dado : relatorioBuilder.getDados()){
            Object[] linha = new Object[relatorioBuilder.getColunas().size()];
            for(int i = 0; i < relatorioBuilder.getColunas().size(); i++){
                linha[i] = lerValor(dado, relatorioBuilder.getColunas().get(i));
            }
            this.linhas.add(linha);
        }
    }

    /**
     * Retorna as linhas que o relatório deve apresentar.
     */
    protected List<Object[]> getLinhas(){
        return this.linhas;
    }

    /**
     * Realiza a leitura de um dado do objeto.
     * @param dado Objeto para qual a propriedade da coluna sera lida.
     * @param coluna Coluna para qual o dado sera lido.
     * @return {@link Object} que representa o valor da propriedade lida.
     * @throws Exception
     */
    private Object lerValor(Object dado, Coluna coluna) throws Exception{
        Object valor = coluna.getLeitor() != null ? coluna.getLeitor().ler(dado) : null;
        if(valor != null && coluna.getFormatador() != null){
            valor = coluna.getFormatador().formatarObject(valor);
        }
        return valor;
    }
}

package servicos.bi.exportador.processador;

import servicos.bi.exportador.RelatorioBuilder;

import java.io.File;

/**
 * Interface de padronização dos processadores de relatório.
 * Created by johny<PERSON> on 11/10/2016.
 */
public interface IProcessador {

    /**
     * Realiza o processamento do {@link RelatorioBuilder} para a geração do arquivo experado.
     * @param relatorioBuilder
     */
    void processar(RelatorioBuilder relatorioBuilder, File arquivo) throws Exception;

}

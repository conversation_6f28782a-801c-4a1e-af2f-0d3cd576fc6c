package servicos.bi.exportador.processador;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import servicos.bi.exportador.Coluna;
import servicos.bi.exportador.Filtro;
import servicos.bi.exportador.RelatorioBuilder;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import org.apache.poi.ss.usermodel.FillPatternType;

/**
 * Realiza o processamento do relatório em excel.
 * Created by johnys on 11/10/2016.
 */
public class ProcessadorExcel extends  BaseProcessador{

    /**
     * Objeto que representa a planilha do excel.
     */
    private Workbook wb;

    /**
     * Pasta de trabalho atual do excel.
     */
    private Sheet sheet;

    /**
     * Representa alinha atual para qual se esta trabalhando.
     */
    private Integer linhaCorrente = 0;

    private Font fonteDado;

    private Font fonteCabecalho;

    private Font fonteTitulo;

    private Font fonteFiltros;

    private CellStyle styleDado;

    private CellStyle styleCabecalho;

    private CellStyle styleTitulo;

    private CellStyle styleFitros;

    private CellStyle styleData;

    private RelatorioBuilder relatorio;

    private File arquivo;

    public void processar(RelatorioBuilder relatorio, File arquivo) throws  Exception{
        super.processar(relatorio, arquivo);
        this.arquivo = arquivo;
        this.relatorio = relatorio;
        initExcel();
        escreverTitulo();
        escreverFiltros();
        escreverCabecalho();
        escreverLinhas();
        escreverArquivo();
    }

    /**
     * Realiza a escrita do arquivo excel no arquivo informado.
     * @throws IOException
     */
    private void escreverArquivo() throws  IOException{
        this.wb.write(new FileOutputStream(this.arquivo));
    }

    /**
     * Escreve os dados do relatório no excel.
     */
    private void escreverLinhas() {
        for(int i = 0; i < getLinhas().size(); i++){
            Row linha = gerarNovaLinha();
            Object[] dado = getLinhas().get(i);
            for(int j = 0; j < dado.length; j++){
                Coluna coluna = relatorio.getColunas().get(j);
                CellStyle style = getStylePorTipo(dado[j]);
                criarCelula(j, linha, style, dado[j]);
            }
        }
        for(int i = 0; i < relatorio.getColunas().size(); i++){
            if (relatorio.getColunas().get(i).getCampoObjeto().equals("nome") || relatorio.getColunas().get(i).getCampoObjeto().equals("nomeConsultor") 
                    || relatorio.getColunas().get(i).getCampoObjeto().equals("nomeProfessores") || relatorio.getColunas().get(i).getCampoObjeto().equals("nomeRespContrato")
                    || relatorio.getColunas().get(i).getCampoObjeto().equals("email") || relatorio.getColunas().get(i).getCampoObjeto().equals("telefone")) {
                sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 7);
            }else if(relatorio.getColunas().get(i).getCampoObjeto().equals("consultor")){
                sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 23);
            }else{
                sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 3);
            }
        }
    }

    /**
     * Retorna o {@link CellStyle} que será aplicado no campo de valor.
     * @param valor
     * @return
     */
    private CellStyle getStylePorTipo(Object valor) {
        return valor != null && valor instanceof Date ? styleData : styleDado;
    }

    /**
     * Escreve o cabeçalho do relatório
     */
    private void escreverCabecalho() {

        if (((relatorio.getTitulo() == null) || (relatorio.getTitulo().trim().equals(""))) &&
           ((relatorio.getFiltros() == null) || (relatorio.getFiltros().size() <= 0))){
            linhaCorrente = -1;
        }
        linhaCorrente++;
        Row cabecalho = gerarNovaLinha();
        for(int i = 0; i < this.relatorio.getColunas().size(); i++){
            Coluna coluna = this.relatorio.getColunas().get(i);
            Cell celulaColuna = criarCelula(i, cabecalho, styleCabecalho, coluna.getNome());
            adicionarFundoCelula(celulaColuna);
        }
    }

    /**
     * Adiciona um fundo a uma determinada celula.
     * @param celula
     */
    private void adicionarFundoCelula(Cell celula) {
        celula.getCellStyle().setFillPattern(FillPatternType.SOLID_FOREGROUND);
        celula.getCellStyle().setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
    }

    /**
     * Escreve os filtros do relatório.
     */
    private void escreverFiltros() {
        if ((relatorio.getFiltros() != null) && (relatorio.getFiltros().size() > 0)){
            linhaCorrente++;
            for(Filtro filtro : relatorio.getFiltros()){
                Row linhaFiltro = gerarNovaLinha();
                criarCelula(0 , linhaFiltro, styleFitros, filtro.getNome());
                criarCelula(1, linhaFiltro, styleDado, filtro.getValorFomatado());
            }
        }
    }

    /**
     * Realiza a criação do título do excel.
     */
    private void escreverTitulo() {
        if ((relatorio.getTitulo() != null) && (!relatorio.getTitulo().trim().equals(""))){
            Row linha = gerarNovaLinha();
            criarCelula(0, linha, styleTitulo, relatorio.getTitulo());
            linha.setHeight((short) 500);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, relatorio.getColunas().size()));
        }
    }

    /**
     * Realiza a criação de uma nova célula no excel a partir dos parâmtros informados.
     * @param numeroCelula numero da celula na {@link Row}
     * @param linha {@link Row} que se deseja criar a celula.
     * @param style {@link CellStyle} a ser utilizada
     * @param valor {@link Object} valor a ser utilizado na celula.
     * @return {@link Cell}
     */
    private Cell criarCelula(int numeroCelula, Row linha, CellStyle style, Object valor){
        Cell celula = linha.createCell(numeroCelula);
        celula.setCellStyle(style);
        setValorCelula(celula, valor);
        return celula;
    }

    /**
     * Insere um determinado valor em uma celula, procurando o tipo de dados do objeto valor
     * @param celula {@link Cell} que será inserido o valor.
     * @param valor {@link Object} valor que será inserido na celula.
     */
    private void setValorCelula(Cell celula, Object valor) {
        if(valor instanceof  String){
            celula.setCellValue((String) valor);
        }else if(valor instanceof Number){
            celula.setCellValue(((Number)valor).doubleValue());
        }else if(valor instanceof Date){
            celula.setCellValue((Date) valor);
        }else if(valor instanceof  Boolean){
            celula.setCellValue((Boolean) valor);
        }else {
            celula.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    /**
     * Realiza a criação do objeto {@link CellStyle} a partir do {@link Font} informado.
     * @param fonte
     * @return
     */
    private CellStyle criarCellStyle(Font fonte) {
        CellStyle style = wb.createCellStyle();
        style.setFont(fonte);
        return style;
    }

    /**
     * Realiza a criação de uma nova linha no {@link Sheet} atual com base no <code>this.linhaCorrente</code>
     * @return
     */
    private Row gerarNovaLinha() {
        return sheet.createRow(linhaCorrente++);
    }

    /**
     * Realiza a inicialização dos componentes do excel da biblioteca Apache POI
     */
    private void initExcel() throws  Exception{
        wb = new SXSSFWorkbook(new XSSFWorkbook());
        sheet = wb.createSheet();
        criarFontes();
        criarStyles();
    }

    /**
     * Cria os estilos a serem utilizados.
     */
    private void criarStyles() {
        this.styleCabecalho = criarCellStyle(fonteCabecalho);
        //GTC 27/10/2016: Retirado o style(styleDado) dos dados que ao usar em conjunto com o autoSizeColumn causa um erro de outOfMemory.
        this.styleFitros = criarCellStyle(fonteFiltros);
        this.styleTitulo = criarCellStyle(fonteTitulo);
        this.styleTitulo.setWrapText(true);
        this.styleData = criarCellStyle(fonteDado);
        this.styleData.setDataFormat(wb.createDataFormat().getFormat("dd/MM/yyyy hh:mm"));
    }

    /**
     * Cria as fontes a serem utilizadas na planilha
     */
    private void criarFontes() {
        fonteDado = criarFonte(this.relatorio.getFonte(), false, IndexedColors.AUTOMATIC.index, null);
        fonteTitulo = criarFonte(this.relatorio.getFonte(), true, IndexedColors.AUTOMATIC.index, Short.valueOf("16"));
        fonteCabecalho = criarFonte(this.relatorio.getFonte(), true, IndexedColors.AUTOMATIC.index, Short.valueOf("12"));
        fonteFiltros = criarFonte(this.relatorio.getFonte(), true, IndexedColors.AUTOMATIC.index, Short.valueOf("12"));
    }

    /**
     * Realiza a criação do objeto {@link Font} dados os parâmetros.
     * @param nomeFonte Nome da fonte.
     * @param negrito Valor do negrito. Caso seja informado <code>null</code> não será adicionado ao objeto {@link Font}
     * @param cor Cor da fonte. Caso seja informado <code>null</code> não será adicionado ao objeto {@link Font}
     * @return {@link Font}
     */
    private Font criarFonte(String nomeFonte, boolean negrito, Short cor, Short fontHeight){
        Font font = wb.createFont();
       font.setBold(negrito);
        if(nomeFonte != null)
            font.setFontName(nomeFonte);
        if(cor != null)
            font.setColor(cor);
        if(fontHeight != null)
            font.setFontHeightInPoints(fontHeight);
        return font;
    }

}

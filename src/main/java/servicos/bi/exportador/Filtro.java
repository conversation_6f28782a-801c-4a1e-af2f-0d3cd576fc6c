package servicos.bi.exportador;

/**
 * Classe que representa o filtro a ser adicionado ao exportador.
 * Created by johny<PERSON> on 11/10/2016.
 */
public class Filtro {

    /**
     * Nome do filtro a ser demonstrado no relatório
     */
    private String nome;

    /**
     * Valor do filtro a ser demonstrado no relatório.
     */
    private Object valor;

    /**
     * Realiza a formatação do valor do filtro caso necessário.
     */
    private Formatador<?> formatador;

    public Filtro(String nome, Object valor){
        setNome(nome);
        setValor(valor);
    }

    public Filtro(String nome, Object valor, Formatador<?> formatador) {
        this(nome, valor);
        setFormatador(formatador);
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Object getValor() {
        return valor;
    }

    public void setValor(Object valor){
        this.valor = valor;
    }

    /**
     * Retorna o valor de exibição do campo.
     * @return
     */
    public Object getValorFomatado(){
        return formatador == null ? valor : formatador.formatarObject(valor);
    }

    public void setFormatador(Formatador<?> formatador) {
        this.formatador = formatador;
    }
}

package servicos.bi;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 28/07/2015.
 */
public class CrossTablePOIGenerics extends SuperControle {

        private List<CrossTabExcelLinha> crossTabExcelLinhas;
        private String formatoDados = null;
        private Workbook wb = new HSSFWorkbook();
        private String nomeArquivo;
        private int tipoDados= 0;//  0  -> valor Texto   /  1 -> valor Inteiro / 2 -> valor Double

    public List<CrossTabExcelLinha> getCrossTabExcelLinhas() {
        return crossTabExcelLinhas;
    }

    public void setCrossTabExcelLinhas(List<CrossTabExcelLinha> crossTabExcelLinhas) {
        this.crossTabExcelLinhas = crossTabExcelLinhas;
    }

    public Workbook getWb() {
        return wb;
    }

    public void setWb(Workbook wb) {
        this.wb = wb;
    }

    public String getFormatoDados() {
        if(formatoDados == null){
            formatoDados = "General";
        }
        return formatoDados;
    }

    public void setFormatoDados(String formatoDados) {
        this.formatoDados = formatoDados;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }
    public void gerarNomeArquivo(String nomeArquivo){
        this.nomeArquivo = nomeArquivo;
        this.nomeArquivo = String.format("%s-%s-%s", new Object[]{
                this.nomeArquivo,
                getKey(),
                new Date().getTime()
        });
        this.nomeArquivo +=".xls";
    }

    public int getTipoDados() {
        return tipoDados;
    }

    public void setTipoDados(int tipoDados) {
        this.tipoDados = tipoDados;
    }

    public void gerarXLS(List<CrossTabExcelLinha> map,String nomeArquivo,Boolean demonstrativo) throws Exception {

        Sheet sheet = wb.createSheet("Sheet1");
        Row row = sheet.createRow(0);
        CellStyle titulos = wb.createCellStyle();
        titulos.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titulos.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // nome do arquivo a ser gerado
        gerarNomeArquivo(nomeArquivo);

        Font font = wb.createFont();
        font.setBold(true);
        font.setFontName("SansSerif");
        font.setColor(IndexedColors.AUTOMATIC.index);
        titulos.setFont(font);
        CellStyle styleCelula=wb.createCellStyle();
        DataFormat df = wb.createDataFormat();
        styleCelula.setDataFormat(df.getFormat(getFormatoDados()));

        for (int index = 1;index < map.get(0).getColunas().size() ; index ++){
            Cell cell = row.createCell(index);
            cell.setCellValue(map.get(0).getColunas().get(index).getDescricaoColuna());
        }
        for(int index = 0;index < map.size(); index ++){
            CrossTabExcelLinha linha = map.get(index);

            if(linha.getSumarioDemonstrativo()){
                adicionaSumario(index,linha,sheet,styleCelula);
            }else {
                if(index!=0)
                row = sheet.createRow(index);
                Cell cell = row.createCell(0);
                cell.setCellValue(map.get(index).getDescricao());

                    for (int indexR = 0; indexR < (linha.getColunas().size()); indexR++) {
                        CrossTabExcelCelula celula = linha.getColunas().get(indexR);
                        cell = row.createCell(indexR + 1);
                        String valorCelula =celula.getValorCelula();
                        if(celula.getValorCelula().equals(""))
                        valorCelula = celula.getDescricaoColuna();
                        setarValorCelulaComTipo(cell, valorCelula, styleCelula);
                    }

            }
        }

        for(int num = 1 ; num < sheet.getPhysicalNumberOfRows()+1; num++) {
            if( sheet.getRow(num) !=null )
            sheet.getRow(num).getCell(0).setCellStyle(titulos);
        }
        for(int num = 1 ; num < sheet.getRow(0).getPhysicalNumberOfCells() ; num++) {
            sheet.getRow(0).getCell(num).setCellStyle(titulos);
        }

    }
    public void setarValorCelulaComTipo(Cell celula , String valor,CellStyle styleCelula){

        try {
            if(tipoDados== 0)
            celula.setCellValue(valor);
            else if(tipoDados == 1)
            celula.setCellValue(Integer.parseInt(valor));
            else if(tipoDados == 2)
            celula.setCellValue(Double.parseDouble(valor));

            celula.setCellStyle(styleCelula);
        }catch(Exception erro){

            celula.setCellValue(valor);
        }
    }
    public void exportarXLS() throws Exception{
        FileOutputStream out = new FileOutputStream(this.getServletContext().getRealPath("relatorio")+ File.separator+nomeArquivo);
        wb.write(out);
        out.close();
        out.flush();
        JSFUtilities.setExpressionValue("#{ExportadorListaControle.fileName}", nomeArquivo);
    }
    public void adicionaSumario(int index,CrossTabExcelLinha item ,Sheet sheet,CellStyle styleCelula){
        Row row = sheet.createRow(index+1);
        Cell rotuloSumario = row.createCell(0);
        rotuloSumario.setCellValue(item.getDescricao());
        int contador = 1;
        for(CrossTabExcelCelula celula : item.getColunas()) {
            Cell valorSumario = row.createCell(contador);
            setarValorCelulaComTipo(valorSumario, item.getColunas().get(contador-1).getValorCelula(),styleCelula);

            contador++;
        }
    }

}



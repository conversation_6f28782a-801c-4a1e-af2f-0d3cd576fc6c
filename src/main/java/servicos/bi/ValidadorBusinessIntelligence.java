/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.bi;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.RoboControle;
import controle.crm.AberturaMetaControle;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Predicate;
import relatorio.controle.basico.ClientePorDuracaoRelControle;
import relatorio.controle.sad.RotatividadeAnaliticoDWControle;
import relatorio.negocio.comuns.basico.ClientePorDuracaoVO;
import relatorio.negocio.comuns.sad.RotatividadeAnaliticoDWVO;
import relatorio.negocio.jdbc.basico.ClientePorDuracaoRel;

/**
 * <AUTHOR>
 */
public class ValidadorBusinessIntelligence {

    public static void executar(String chave, boolean enviarEmailSeNaoBater,
                                StringBuffer sb, boolean corrigirHistoricoContratoESintetico, boolean apenasAnalitico, Date dia) {
        try {
            Uteis.logar(null, "Iniciando processamento (ValidadorBusinessIntelligence) "
                    + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            DAO oamd = new DAO();
            Conexao conexao = Conexao.getInstance();
            if (!conexao.getUrlOAMD().isEmpty()) {
                Connection con = Conexao.getConexaoForJ2SE();
                if (con == null || con.isClosed()) {
                    con = oamd.obterConexaoEspecifica(chave);
                    Conexao.guardarConexaoForJ2SE(con);
                }
                Uteis.logar(sb, "Guardada conexao para " + chave + "\r");
            } else {
                Connection c = FacadeManager.getFacade().getZWFacade().getCon();
                Conexao.guardarConexaoForJ2SE(c);
                Uteis.logar(sb, "Usando cfgBD.xml para -> " + conexao.getIpServidor());
            }
//                        Connection c = DriverManager.getConnection("**************************************************", "zillyonweb", "pactodb");
//            Conexao.guardarConexaoForJ2SE(c);
            if (dia == null) {
                dia = Calendario.hoje();
            }
            LoginControle control = new LoginControle();
            control.setUsername("admin");
            control.setSenha("01pzw27rfb");
            control.setUserOamd("adm");
            control.login();
            control.atualizarBD();
            Uteis.logar(sb, control.getMensagemAtualizacaoBD());
            //corrigirHistorico();
            //int[] codigosDOMBOSCO = {8521, 7238,8182, 9769,6524};
            //int[] codigosCORPOLIVRE = {4439};
            /*int[] codigos = {28352};
            corrigirHistorico(codigos);*/
            if (apenasAnalitico) {
                if (!validarMovimentacaoContratosAnalitico(Uteis.getData(dia), sb, corrigirHistoricoContratoESintetico)) {
                    if (enviarEmailSeNaoBater) {
                        EnviarEmailDosServicos.enviarEmail(sb);
                    }
                }
            } else {
                if (!validarMovimentacaoContratosSegundoHistorico(
                        Uteis.getData(dia), sb)
                        || !validarMovimentacaoContratosSegundoContratoPorDuracao(
                        Uteis.getData(dia), sb)) {
                    if (enviarEmailSeNaoBater) {
                        EnviarEmailDosServicos.enviarEmail(sb);
                    }
                }
            }


        } catch (Exception e) {
            Uteis.logar(sb, "FALHOU para chave " + chave);
            Uteis.logar(sb, "            " + e.getMessage() + "\n");
        } finally {
            FacadeManager.limparFactory();
        }
        Uteis.logar(null, "Terminou processamento (ValidadorBusinessIntelligence)  "
                + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());

    }

    private static void rodarRisco() throws Exception {

        FacadeManager.getFacade().getRisco().prepararConexao();

        RoboControle control = new RoboControle();
        control.start("");

    }

    private static boolean validarMovimentacaoContratosSegundoContratoPorDuracao(
            String data, StringBuffer sb) throws Exception {

        boolean valido = true;

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        Date fim = sdf.parse(data);


        int mes = Uteis.getMesData(fim);
        int ano = Uteis.getAnoData(fim);


        RotatividadeAnaliticoDWControle control = new RotatividadeAnaliticoDWControle();
        RotatividadeAnaliticoDWVO rotVO = new RotatividadeAnaliticoDWVO();
        List<EmpresaVO> listaEmpresa = FacadeManager.getFacade().getEmpresa().
                consultarPorCodigo(0, true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (EmpresaVO empresaVO : listaEmpresa) {
            Uteis.logar(sb, "validarMovimentacaoContratosSegundoContratosPorDuracao");
            Uteis.logar(sb, "<b>Empresa => " + empresaVO.getNome() + "</b>");

            rotVO.setEmpresa(empresaVO);

            rotVO.setAno(ano);
            rotVO.setMes(mes);
            rotVO.setDia(fim);
            control.setRotatividadeAnaliticoDWVO(rotVO);
            control.setEmpresaVO(rotVO.getEmpresa());

            control.setListaApresentarContratos(new ArrayList<ContratoVO>());
            control.montarRelatorioRotatividadeTelaInicial();
            control.consultarRotatividadePorSituacaoTodos();
            List<ContratoVO> listaFinalMes = control.getListaApresentarContratos();

            ClientePorDuracaoRel cliPorDurRel = FacadeManager.getFacade().getClientePorDuracaoRel();
            cliPorDurRel.setDataPeriodo(fim);
            cliPorDurRel.setSituacaoAtestado(true);
            cliPorDurRel.setSituacaoAvencer(true);
            cliPorDurRel.setSituacaoCarencia(true);
            cliPorDurRel.setSituacaoNormal(true);
            cliPorDurRel.setSituacaoVencido(true);
            cliPorDurRel.setSituacaoTrancado(false);
            cliPorDurRel.setSemBolsa(false);
            ClientePorDuracaoRelControle cliDurControl = new ClientePorDuracaoRelControle();
            cliDurControl.setFiltroEmpresa(empresaVO.getCodigo());
            cliDurControl.imprimir();
            cliDurControl.imprimirTudo();
            List<ClientePorDuracaoVO> listaCliPorDur = cliDurControl.getListaClientePorDuracao();

            for (ClientePorDuracaoVO cliPorDur : listaCliPorDur) {
                final ContratoVO umContrato = cliPorDur.getContratoVO();
                ContratoVO achado = (ContratoVO) CollectionUtils.find(listaFinalMes, new Predicate() {

                    @Override
                    public boolean evaluate(Object o) {
                        ContratoVO c = (ContratoVO) o;
                        return c.getCodigo().intValue() == umContrato.getCodigo().intValue();
                    }
                });

                if (achado == null) {
                    Uteis.logar(sb, String.format("Contrato por Duração %s não encontrado em Movimentação de Contratos",
                            new Object[]{umContrato.getCodigo()}));
                }

            }

            for (ContratoVO contrato : listaFinalMes) {
                final ContratoVO procurado = contrato;
                ClientePorDuracaoVO achado = (ClientePorDuracaoVO) CollectionUtils.find(listaCliPorDur, new Predicate() {

                    @Override
                    public boolean evaluate(Object o) {
                        ClientePorDuracaoVO c = (ClientePorDuracaoVO) o;
                        return c.getContratoVO().getCodigo().intValue() == procurado.getCodigo().intValue();
                    }
                });

                if (achado == null) {
                    Uteis.logar(sb, String.format("Contrato %s em Movimentação de Contratos não encontrado em Contratos por Duração",
                            new Object[]{contrato.getCodigo()}));
                }

            }
            if (valido) {
                //Contratos por Duração pode ser maior do que Movimentação de Contratos
                //Exemplo: Um contrato de A Vencer no mesmo mês que estava de Férias ou Atestado
                valido = listaCliPorDur.size() >= listaFinalMes.size();
            }
            if (valido) {
                Uteis.logar(sb, "<b>OK</b>");
            } else {
                Uteis.logar(sb, "<b>DIFERENÇA!</b>");
            }
        }//for Empresa...
        return valido;
    }

    private static boolean validarMovimentacaoContratosSegundoHistorico(String data,
                                                                        StringBuffer sb) throws Exception {

        boolean valido = true;

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        Date fim = sdf.parse(data);


        int mes = Uteis.getMesData(fim);
        int ano = Uteis.getAnoData(fim);


        RotatividadeAnaliticoDWControle control = new RotatividadeAnaliticoDWControle();
        RotatividadeAnaliticoDWVO rotVO = new RotatividadeAnaliticoDWVO();
        List<EmpresaVO> listaEmpresa = FacadeManager.getFacade().getEmpresa().
                consultarPorCodigo(0, true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (EmpresaVO empresaVO : listaEmpresa) {
            Uteis.logar(sb, "validarMovimentacaoContratosSegundoHistorico");
            Uteis.logar(sb, "<b>Empresa => " + empresaVO.getNome() + "</b>");

            rotVO.setEmpresa(empresaVO);

            rotVO.setAno(ano);
            rotVO.setMes(mes);
            rotVO.setDia(fim);
            control.setRotatividadeAnaliticoDWVO(rotVO);
            control.setEmpresaVO(rotVO.getEmpresa());
            control.montarRelatorioRotatividadeTelaInicial();

            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoVirgentesMesAnterior();
            List<ContratoVO> listaInicioMes = control.getListaApresentarContratos();

            //cancelados
            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoCancelado();
            List<ContratoVO> negativos = control.getListaApresentarContratos();

            //trancados
            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoTrancado();
            negativos.addAll(control.getListaApresentarContratos());

            //desistentes
            control.consultarRotatividadePorSituacaoDesistente();
            negativos.addAll(control.getListaApresentarContratos());


            //matriculados
            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoMatriculados();
            List<ContratoVO> positivos = control.getListaApresentarContratos();

            //rematriculados
            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoRematricula();
            positivos.addAll(control.getListaApresentarContratos());


            //retorno de trancamento
            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoRetornoTrancamento();
            positivos.addAll(control.getListaApresentarContratos());

            //verificar se os POSITIVOS do mês não estão no início realmente!
            for (ContratoVO positivo : positivos) {
                int procurado = positivo.getCodigo();

                for (ContratoVO ctInicioMes : listaInicioMes) {
                    if (procurado == ctInicioMes.getCodigo().intValue()) {
                        boolean achou = false;
                        for (ContratoVO negativo : negativos) {
                            int negprocurado = negativo.getCodigo();

                            if (procurado == negprocurado) {
                                achou = true;
                                break;
                            }

                        }
                        if (!achou) {
                            Uteis.logar(sb, "Achei contrato " + ctInicioMes.getPessoa().getNome() + " negativo que não está no início do mês");
                        }

                    }

                }
            }

//            //verificar quais positivos no meio de negativos!
//            for (ContratoVO positivo : positivos) {
//                int procurado = positivo.getCodigo();
//                boolean achou = false;
//                for (ContratoVO ctInicioMes : negativos) {
//                    if (procurado == ctInicioMes.getCodigo().intValue()) {
//                        achou = true;
//                    }
//
//                }
//                if (achou) {
//                    Uteis.logar(sb, "Achei " + positivo.getPessoa().getNome() + " POSITIVO no meio dos NEGATIVOS");
//                }
//
//
//            }


            //verificar se os NEGATIVOS do mês estão no início realmente e nem nos Positivos!
            for (ContratoVO negativo : negativos) {
                int procurado = negativo.getCodigo();
                boolean achou = false;
                for (ContratoVO ctInicioMes : listaInicioMes) {
                    if (procurado == ctInicioMes.getCodigo().intValue() || procurado == ctInicioMes.getContratoResponsavelRenovacaoMatricula().intValue()) {
                        achou = true;
                    }

                }
                if (!achou) {
                    for (ContratoVO positivo : positivos) {
                        if (positivo.getCodigo().intValue() == procurado) {
                            achou = true;
                            break;
                        }
                    }
                    if (!achou) {
                        Uteis.logar(sb, "Achei contrato " + negativo.getPessoa().getNome() + " negativo que não está no INÍCIO DO MÊS e não está nos POSITIVOS");
                    }
                }


            }

            listaInicioMes.addAll(positivos);

            Contrato.removeItens(listaInicioMes, negativos);

            //pega lista de vigentes do final do mes
            control.setListaApresentarContratos(new ArrayList());
            control.consultarRotatividadePorSituacaoTodos();
            List<ContratoVO> listaFinalMes = control.getListaApresentarContratos();


            for (ContratoVO negativo : negativos) {
                int procurado = negativo.getCodigo();
                for (ContratoVO ctFinalMes : listaFinalMes) {
                    if (procurado == ctFinalMes.getCodigo().intValue()) {
                        boolean existePositivo = false;
                        for (ContratoVO ctPositivo : positivos) {
                            if (ctPositivo.getCodigo().intValue() == procurado) {
                                existePositivo = true;
                                break;
                            }
                        }
                        if (!existePositivo) {
                            Uteis.logar(sb, "Achei contrato " + procurado + " da pessoa "
                                    + ctFinalMes.getPessoa().getNome() + " negativo que nao existe positivo equivalente");
                        }
                    }

                }


            }

            if (listaInicioMes.size() != listaFinalMes.size()) {
                valido = false;
                List<String> nomesDiferentes = diferencaPorCliente(listaInicioMes, listaFinalMes);
                if (!nomesDiferentes.isEmpty()) {
                    for (String string : nomesDiferentes) {
                        Uteis.logar(sb, "<b>Diferente: " + string + "</b>");

                    }
                } else {
                    Ordenacao.ordenarLista(listaInicioMes, "pessoa");
                    for (ContratoVO c : listaInicioMes) {
                        Uteis.logar(sb, c.getPessoa().getNome());

                    }
                    Uteis.logar(sb, "===========================================================================");
                    Ordenacao.ordenarLista(listaFinalMes, "pessoa");

                    for (ContratoVO c : listaFinalMes) {
                        Uteis.logar(sb, c.getPessoa().getNome());

                    }
                }

            } else {
                Uteis.logar(sb, "<b>OK</b>");
            }
        }
        return valido;
    }

    protected static List<String> diferencaPorCliente(Collection<ContratoVO> c1,
                                                      Collection<ContratoVO> c2) {

        Map<String, String> mapa = new HashMap<String, String>();
        List<String> diferenca = new ArrayList();
        if (c2.size() > c1.size()) {
            Iterator<ContratoVO> it = c1.iterator();
            while (it.hasNext()) {
                ContratoVO ct = it.next();
                mapa.put(ct.getPessoa().getNome(),
                        ct.getPessoa().getNome());
            }

            Iterator<ContratoVO> it2 = c2.iterator();
            while (it2.hasNext()) {
                ContratoVO ct = it2.next();
                String nomeAnterior = mapa.put(ct.getPessoa().getNome(),
                        ct.getPessoa().getNome());
                if (nomeAnterior == null) {
                    diferenca.add(ct.getPessoa().getNome());
                }
            }
        } else if (c1.size() > c2.size()) {

            Iterator<ContratoVO> it = c2.iterator();
            while (it.hasNext()) {
                ContratoVO ct = it.next();
                mapa.put(ct.getPessoa().getNome(),
                        ct.getPessoa().getNome());
            }

            Iterator<ContratoVO> it2 = c1.iterator();
            while (it2.hasNext()) {
                ContratoVO ct = it2.next();
                String nomeAnterior = mapa.put(ct.getPessoa().getNome(),
                        ct.getPessoa().getNome());
                if (nomeAnterior == null) {
                    diferenca.add(ct.getPessoa().getNome());
                }
            }
        }
        return diferenca;

    }

    private static void corrigirHistorico() throws Exception {
//        Contrato.gerarSituacoesTemporaisContratos();
    }

    private static void corrigirHistorico(int[] codigos) throws Exception {
        for (int i = 0; i < codigos.length; i++) {

            Contrato.gerarHistoricoTemporalUmContrato(codigos[i]);

        }

    }

    public static void main(String[] args) {
        try {
            Uteis.debug = true;
            StringBuffer sb = new StringBuffer();
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            boolean tentarCorrigir = args.length > 1 ?  args[1].equals("true") : true;
            if (args.length > 2) {
                Date hoje = Calendario.getInstance(sdf.parse(args[2])).getTime();
                Calendario.dia = hoje;
            }
            executar(args.length == 0 ? "selfit" : args[0], true, sb, tentarCorrigir, true, Calendario.dia);
            
            realizarEnvioDasMetas(args.length == 0 ? "axis" : args[0], Calendario.dia);

        } catch (Exception ex) {
            Logger.getLogger(ValidadorBusinessIntelligence.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    private static boolean validarMovimentacaoContratosAnalitico(String data,
                                                                 StringBuffer sb, boolean corrigirHistoricoContratoESintetico) throws Exception {

        boolean valido = true;
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

        Date fim = sdf.parse(data);


        int mes = Uteis.getMesData(fim);
        int ano = Uteis.getAnoData(fim);


        RotatividadeAnaliticoDWControle control = new RotatividadeAnaliticoDWControle();
        control.setValidadorBusinessInteligence(true);
        RotatividadeAnaliticoDWVO rotVO = new RotatividadeAnaliticoDWVO();
        List<EmpresaVO> listaEmpresa = FacadeManager.getFacade().getEmpresa().
                consultarPorCodigo(0, true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<EmpresaVO> empresasErro = new ArrayList<EmpresaVO>();

        for (EmpresaVO empresaVO : listaEmpresa) { // primeira validação
           validarMovimentacaoContratosAnalitico(sb, mes, ano, control, rotVO, empresaVO, fim, corrigirHistoricoContratoESintetico);
           if(!UteisValidacao.emptyString(empresaVO.getContratoProblematicos())) {
               empresasErro.add(empresaVO);
           }
        }

        if(corrigirHistoricoContratoESintetico) {
            for (EmpresaVO empresaVO : empresasErro) {  // correção dos contrato com problema encontrados
                processarRoboContratosProblematicos(empresaVO.getContratoProblematicos(), empresaVO.getMatriculasProblematicas());
                empresaVO.setContratoProblematicos("");
                empresaVO.setMatriculasProblematicas("");
            }
            for (EmpresaVO empresaVO : empresasErro) {
                validarMovimentacaoContratosAnalitico(sb, mes, ano, control, rotVO, empresaVO, fim, false); // validação após correção
                if(!UteisValidacao.emptyString(empresaVO.getContratoProblematicos())) {
                    valido = false;
                }
            }
        } else {
            if(!empresasErro.isEmpty()) {
                valido = false;
            }
        }

        return valido;
    }

    private static void validarMovimentacaoContratosAnalitico(StringBuffer sb, int mes, int ano, RotatividadeAnaliticoDWControle control, RotatividadeAnaliticoDWVO rotVO, EmpresaVO empresaVO, Date fim, boolean tentarCorrigir) throws Exception {
        int processado = 0; // avalia o numero de verificações feita po
        StringBuffer temporario;

        temporario = new StringBuffer();
        HashMap<Integer, ContratoVO> resultado = new HashMap<Integer, ContratoVO>();
        HashMap<Integer, ContratoVO> fimMes = new HashMap<Integer, ContratoVO>();
        String contratoProblematicos = "";
        String matriculas = "";

        Uteis.logar(temporario, "validarMovimentacaoContratosAnalitico");
        Uteis.logar(temporario, "<b>Empresa => " + empresaVO.getNome() + "</b>");

        rotVO.setEmpresa(empresaVO);

        rotVO.setAno(ano);
        rotVO.setMes(mes);
        rotVO.setDia(fim);
        control.setRotatividadeAnaliticoDWVO(rotVO);
        control.setEmpresaVO(rotVO.getEmpresa());

        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoVirgentesMesAnterior();
        List<ContratoVO> listaInicioMes = control.getListaApresentarContratos();

        //matriculados
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoMatriculados();
        List<ContratoVO> positivos = control.getListaApresentarContratos();

        //rematriculados
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoRematricula();
        positivos.addAll(control.getListaApresentarContratos());

        //contratos transferidos
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoContratoTransferido();
        positivos.addAll(control.getListaApresentarContratos());


        listaInicioMes.addAll(positivos);

        for (ContratoVO positivo : listaInicioMes) {
            resultado.put(positivo.getCodigo(), positivo);
        }

        //trancados

        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoTrancado();
        List<ContratoVO> trancados = control.getListaApresentarContratos();

        //retorno de trancamento
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoRetornoTrancamento();
        List<ContratoVO> retornoTrancados = control.getListaApresentarContratos();

        int tentativas = 0;
        List<ContratoVO> restanteTrancados = new ArrayList<ContratoVO>();
        List<ContratoVO> restanteRetornos = new ArrayList<ContratoVO>();
        boolean processoConcluido = false;
        while (tentativas < 6 && !processoConcluido) {
            if (tentativas > 0) {
                trancados = restanteTrancados;
                retornoTrancados = restanteRetornos;
                restanteRetornos = new ArrayList<ContratoVO>();
                restanteTrancados = new ArrayList<ContratoVO>();
            }
            for (ContratoVO trancado : trancados) {
                if (resultado.containsKey(trancado.getCodigo())) {
                    resultado.remove(trancado.getCodigo());
                } else if (resultado.containsKey(trancado.getContratoBaseadoRenovacao())) {
                    resultado.remove(trancado.getContratoBaseadoRenovacao());
                } else {
                    restanteTrancados.add(trancado);
                }
            }
            for (ContratoVO retorno : retornoTrancados) {
                if (!resultado.containsKey(retorno.getCodigo()) && !resultado.containsKey(retorno.getContratoBaseadoRenovacao())) {
                    resultado.put(retorno.getCodigo(), retorno);
                } else {
                    restanteRetornos.add(retorno);
                }
            }

            if (restanteRetornos.isEmpty() && restanteTrancados.isEmpty()) {
                processoConcluido = true;
            }
            tentativas++;
        }

        if (!processoConcluido) {
            for (ContratoVO trancado : restanteTrancados) {
                Uteis.logar(temporario, "Contrato " + trancado.getCodigo() + " está com problemas no trancamento" + contratoImportado(trancado));
                contratoProblematicos = (contratoProblematicos == "" ? trancado.getCodigo().toString() : contratoProblematicos + "," + trancado.getCodigo().toString());
                matriculas = (matriculas == "" ? trancado.getCliente().getCodigoMatricula().toString() : matriculas + "," + trancado.getCliente().getCodigoMatricula().toString());
            }
            for (ContratoVO retorno : restanteRetornos) {
                Uteis.logar(temporario, "Contrato " + retorno.getCodigo() + " está com problemas no retorno" + contratoImportado(retorno));
                contratoProblematicos = (contratoProblematicos == "" ? retorno.getCodigo().toString() : contratoProblematicos + "," + retorno.getCodigo().toString());
                matriculas = (matriculas == "" ? retorno.getCliente().getCodigoMatricula().toString() : matriculas + "," + retorno.getCliente().getCodigoMatricula().toString());
            }
        }
        //cancelados
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoCancelado();
        List<ContratoVO> negativos = control.getListaApresentarContratos();


        //desistentes
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoDesistente();
        negativos.addAll(control.getListaApresentarContratos());

        for (ContratoVO negativo : negativos) {
            if (resultado.containsKey(negativo.getCodigo())) {
                resultado.remove(negativo.getCodigo());
            } else if (resultado.containsKey(negativo.getContratoBaseadoRenovacao())) {
                resultado.remove(negativo.getContratoBaseadoRenovacao());
            } else {
                Uteis.logar(temporario, "Contrato " + negativo.getCodigo() + " está negativo mas não está no inicio do mes nem no meio" + contratoImportado(negativo));
                contratoProblematicos = (contratoProblematicos == "" ? negativo.getCodigo().toString() : contratoProblematicos + "," + negativo.getCodigo().toString());
                matriculas = (matriculas == "" ? negativo.getCliente().getCodigoMatricula().toString() : matriculas + "," + negativo.getCliente().getCodigoMatricula().toString());
            }
        }

        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoTodos();
        List<ContratoVO> totalFimMes = control.getListaApresentarContratos();
        HashMap<Integer, ContratoVO> possiveisProblemas = new HashMap<Integer, ContratoVO>();
        for (ContratoVO contrato : totalFimMes) {  // valida contrato que estão no fim do mês, mas não estavam no inicio e nem entraram no meio
            fimMes.put(contrato.getCodigo(), contrato);
            if (resultado.containsKey(contrato.getCodigo())) {
                resultado.remove(contrato.getCodigo());
            } else {
                if (resultado.containsKey(contrato.getContratoBaseadoRenovacao())) {
                    resultado.remove(contrato.getContratoBaseadoRenovacao());
                } else {
                    if(UteisValidacao.notEmptyNumber(contrato.getContratoBaseadoRenovacao())){
                        try (ResultSet rs = SuperFacadeJDBC.criarConsulta("select c.codigo, c.contratobaseadorenovacao, c.contratobaseadorematricula, c.contratoresponsavelrenovacaomatricula, c.contratoresponsavelrematriculamatricula from contrato c where codigo = " + contrato.getContratoBaseadoRenovacao() , FacadeManager.getFacade().getZWFacade().getCon())) {
                            if(rs.next()) {
                                Integer codigoContratoBaseadoRenovacao = rs.getInt("contratobaseadorenovacao");
                                if (UteisValidacao.notEmptyNumber(codigoContratoBaseadoRenovacao) && resultado.containsKey(codigoContratoBaseadoRenovacao)) {
                                    resultado.remove(codigoContratoBaseadoRenovacao);
                                    continue;
                                }
                            }
                        }
                    }
                    possiveisProblemas.put(contrato.getCodigo(), contrato);
                }
            }
        }

        Set<Integer> restante = resultado.keySet();
        for (Integer contrato : restante) { // contratos que estão no inicio ou meio do mês, mas não estão no fim do Mês
            if (possiveisProblemas.containsKey(resultado.get(contrato).getContratoResponsavelRenovacaoMatricula())) { // contratos com menos de um mes, começam e terminam no mesmo mes
                possiveisProblemas.remove((resultado.get(contrato).getContratoResponsavelRenovacaoMatricula()));
            } else {
                Uteis.logar(temporario, "Contrato " + contrato.toString() + " está inicio do mês mas não está no fim do mês" + contratoImportado(resultado.get(contrato)));
                contratoProblematicos = (contratoProblematicos == "" ? contrato.toString() : contratoProblematicos + "," + contrato.toString());
                matriculas = (matriculas == "" ? resultado.get(contrato).getCliente().getCodigoMatricula().toString() : matriculas + "," + resultado.get(contrato).getCliente().getCodigoMatricula().toString());
            }
        }
        restante = possiveisProblemas.keySet();
        for (Integer contrato : restante) { // contratos que estão no inicio ou meio do mês, mas não estão no fim do Mês
            Uteis.logar(temporario, "Contrato " + possiveisProblemas.get(contrato).getCodigo() + " está fim do mês mas não está no inicio do mes nem no meio" + contratoImportado(possiveisProblemas.get(contrato)));
            contratoProblematicos = (contratoProblematicos == "" ? possiveisProblemas.get(contrato).getCodigo().toString() : contratoProblematicos + "," + possiveisProblemas.get(contrato).getCodigo().toString());
            contratoProblematicos = (contratoProblematicos + "," + possiveisProblemas.get(contrato).getContratoBaseadoRenovacao().toString());
            matriculas = (matriculas == "" ? possiveisProblemas.get(contrato).getCliente().getCodigoMatricula().toString() : matriculas + "," + possiveisProblemas.get(contrato).getCliente().getCodigoMatricula().toString());
        }


        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoVencidoMes();
        List<ContratoVO> vencidosFim = control.getListaApresentarContratos();


        for (ContratoVO contrato : vencidosFim) {  // valida contrato que estão no fim do mês, mas não estavam no inicio e nem entraram no meio
            if (fimMes.containsKey(contrato.getCodigo())) {
                fimMes.remove(contrato.getCodigo());
            } else {
                Uteis.logar(temporario, "Contrato " + contrato.getCodigo() + " está nos vencidos do mes , mas não na lista final do mes" + contratoImportado(contrato));
                contratoProblematicos = (contratoProblematicos == "" ? contrato.getCodigo().toString() : contratoProblematicos + "," + contrato.getCodigo().toString());
                matriculas = (matriculas == "" ? contrato.getCliente().getCodigoMatricula().toString() : matriculas + "," + contrato.getCliente().getCodigoMatricula().toString());
            }
        }

        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorSituacaoVigenteMes();
        List<ContratoVO> ativosFim = control.getListaApresentarContratos();

        for (ContratoVO contrato : ativosFim) {
            if (fimMes.containsKey(contrato.getCodigo())) {
                fimMes.remove(contrato.getCodigo());
            } else {
                Uteis.logar(temporario, "Contrato " + contrato.getCodigo() + " está nos ativos do mes , mas não na lista final do mes" + contratoImportado(contrato));
                contratoProblematicos = (contratoProblematicos == "" ? contrato.getCodigo().toString() : contratoProblematicos + "," + contrato.getCodigo().toString());
                matriculas = (matriculas == "" ? contrato.getCliente().getCodigoMatricula().toString() : matriculas + "," + contrato.getCliente().getCodigoMatricula().toString());
            }
        }




        //validar dependentes
        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorDependentesInicioMes();
        List<ContratoVO> dependentesInicio = control.getListaApresentarContratos();


        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorDependentesVinculadosMes();
        dependentesInicio.addAll(control.getListaApresentarContratos());

        HashMap<Integer, ContratoVO> dependentes = new HashMap<>();

        for (ContratoVO dependenteInicio : dependentesInicio) {
            dependentes.put(dependenteInicio.getCodigo(), dependenteInicio);
        }

        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorDependentesDesvinculadosMes();
        List<ContratoVO> dependentesDesvinculados = control.getListaApresentarContratos();

        for (ContratoVO desvinculado : dependentesDesvinculados) {
            if (dependentes.containsKey(desvinculado.getCodigo())) {
                dependentes.remove(desvinculado.getCodigo());
            } else {
                Uteis.logar(temporario, "Contrato dependente " + desvinculado.getCodigo() + " está negativo mas não está no inicio do mes nem no meio" + contratoImportado(desvinculado));
            }
        }


        control.setListaApresentarContratos(new ArrayList());
        control.consultarRotatividadePorDependentesFinalMes();
        List<ContratoVO> dependentesFinalMes = control.getListaApresentarContratos();


        for (ContratoVO vigente : dependentesFinalMes) {
            if (dependentes.containsKey(vigente.getCodigo())) {
                resultado.remove(vigente.getCodigo());
            } else {
                Uteis.logar(temporario, "Contrato dependente" + vigente.getCodigo() + " está no final do mês, mas na esta no inicio do mes nem no meio" + contratoImportado(vigente));
            }
        }

        Set<Integer> restanteDependetes = dependentes.keySet();
        for (Integer contrato : restanteDependetes) {
            Uteis.logar(temporario, "Contrato dependente " + contrato + " está no inicio ou meio do mês, não teve saída e não está no fim do mes mas na esta no inicio do mes nem no meio" + contratoImportado(dependentes.get(contrato)));
        }


        empresaVO.setMatriculasProblematicas(matriculas);
        empresaVO.setContratoProblematicos(contratoProblematicos);
        if (!tentarCorrigir && !UteisValidacao.emptyString(contratoProblematicos)) { //Não irá mais tentar corrigir e então envia o resultado
            sb.append(temporario.toString());
        }

    }

    private static String contratoImportado(ContratoVO contrato) {
        if (contrato.getImportacao()) {
            return " (importação)";
        }
        return "";
    }

    public static void processarRoboContratosProblematicos(String contratoProblematicos, String matriculas) throws Exception {
        RoboControle robo = new RoboControle();
        robo.setContratos(contratoProblematicos);
        robo.setApagarHistorioContratoAntes(true);
        robo.setMatriculas(matriculas);
        robo.getRobo().setProcessarValidador(true); // isso define que o processamento do robo é soliciatado pelo verificador de inconsistencias
        robo.processarLote();
        Uteis.finalizarExecutor(3);

    }

    public static void realizarEnvioDasMetas(String chave, Date dia) {
        Uteis.logar(null, "Iniciando Envio de Metas (ValidadorBusinessIntelligence) "
                + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
        try {
            DAO oamd = new DAO();
            Conexao conexao = Conexao.getInstance();
            if (!conexao.getUrlOAMD().isEmpty()) {
                Connection con = Conexao.getConexaoForJ2SE();
                if (con == null || con.isClosed()) {
                    con = oamd.obterConexaoEspecifica(chave);
                    Conexao.guardarConexaoForJ2SE(con);
                }

                Uteis.logar(null, "Guardada conexao para " + chave + "\r");
            } else {
                Connection c = FacadeManager.getFacade().getZWFacade().getCon();
                Conexao.guardarConexaoForJ2SE(c);
                Uteis.logar(null, "Usando cfgBD.xml para -> " + conexao.getIpServidor());
            }
            if (dia == null) {
                dia = Calendario.hoje();
            }
            new AberturaMetaControle().enviarMetasFechadas(Uteis.somarDias(dia, -1));

        } catch (Exception ex) {
            Logger.getLogger(ValidadorBusinessIntelligence.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            ThreadEnviarEmail.finalmente();
        }
    }
}

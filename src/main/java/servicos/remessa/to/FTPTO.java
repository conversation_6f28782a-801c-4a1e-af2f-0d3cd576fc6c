package servicos.remessa.to;

/**
 * Created by John<PERSON> on 05/02/2017.
 */
public class FTPTO {

    private Long codigo;

    private String host;

    private String usuario;

    private String senha;

    private String porta;

    private String diretorioRemotoRemessa;

    private String diretorioRemotoRetorno;

    private String diretorioLocalRemessa;

    private String diretorioLocalRetorno;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getPorta() {
        return porta;
    }

    public void setPorta(String porta) {
        this.porta = porta;
    }

    public String getDiretorioRemotoRemessa() {
        return diretorioRemotoRemessa;
    }

    public void setDiretorioRemotoRemessa(String diretorioRemotoRemessa) {
        this.diretorioRemotoRemessa = diretorioRemotoRemessa;
    }

    public String getDiretorioRemotoRetorno() {
        return diretorioRemotoRetorno;
    }

    public void setDiretorioRemotoRetorno(String diretorioRemotoRetorno) {
        this.diretorioRemotoRetorno = diretorioRemotoRetorno;
    }

    public String getDiretorioLocalRemessa() {
        return diretorioLocalRemessa;
    }

    public void setDiretorioLocalRemessa(String diretorioLocalRemessa) {
        this.diretorioLocalRemessa = diretorioLocalRemessa;
    }

    public String getDiretorioLocalRetorno() {
        return diretorioLocalRetorno;
    }

    public void setDiretorioLocalRetorno(String diretorioLocalRetorno) {
        this.diretorioLocalRetorno = diretorioLocalRetorno;
    }
}

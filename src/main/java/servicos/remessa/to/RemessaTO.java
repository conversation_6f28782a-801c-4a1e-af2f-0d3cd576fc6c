package servicos.remessa.to;

import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Johnys on 05/02/2017.
 */
public class RemessaTO {

    private Long codigo;

    private ConvenioCobrancaTO convenio;

    private List<RemessaItemTO> itens;

    private Long identificador;

    private Date dataGeracao;

    private Map<String, String> parametros = new HashMap<String, String>();

    private SituacaoRemessaEnum situacaoRemessa;

    private String chave;

    private String nomeArquivo;

    private String retorno;

    public ConvenioCobrancaTO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaTO convenio) {
        this.convenio = convenio;
    }

    public List<RemessaItemTO> getItens() {
        return itens;
    }

    public void setItens(List<RemessaItemTO> itens) {
        this.itens = itens;
    }

    public Long getIdentificador() {
        return identificador;
    }

    public void setIdentificador(Long identificador) {
        this.identificador = identificador;
    }

    public Date getDataGeracao() {
        return dataGeracao;
    }

    public void setDataGeracao(Date dataGeracao) {
        this.dataGeracao = dataGeracao;
    }

    public Map<String, String> getParametros() {
        return parametros;
    }

    public void setParametros(Map<String, String> parametros) {
        this.parametros = parametros;
    }

    public SituacaoRemessaEnum getSituacaoRemessa() {
        return situacaoRemessa;
    }

    public void setSituacaoRemessa(SituacaoRemessaEnum situacaoRemessa) {
        this.situacaoRemessa = situacaoRemessa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getNomeArquivo(){
        return this.nomeArquivo;
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }
}

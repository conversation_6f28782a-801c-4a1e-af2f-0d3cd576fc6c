package servicos.remessa.to;

import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Johnys on 05/02/2017.
 */
public class RemessaItemTO {

    private Long codigo;

    private Long identificador;

    private BigDecimal valor;

    private AutorizacaoTO autorizacao;

    private RemessaTO remessa;

    private BigDecimal valorPago;

    private String codigoErro;

    private SituacaoRemessaEnum situacao;

    private Map<String, String> parametros = new HashMap<String, String>();

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public Long getIdentificador() {
        return identificador;
    }

    public void setIdentificador(Long identificador) {
        this.identificador = identificador;
    }

    public AutorizacaoTO getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(AutorizacaoTO autorizacao) {
        this.autorizacao = autorizacao;
    }

    public RemessaTO getRemessa() {
        return remessa;
    }

    public void setRemessa(RemessaTO remessa) {
        this.remessa = remessa;
    }

    public BigDecimal getValorPago() {
        return valorPago;
    }

    public void setValorPago(BigDecimal valorPago) {
        this.valorPago = valorPago;
    }

    public String getCodigoErro() {
        return codigoErro;
    }

    public void setCodigoErro(String codigoErro) {
        this.codigoErro = codigoErro;
    }

    public SituacaoRemessaEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoRemessaEnum situacao) {
        this.situacao = situacao;
    }

    public Map<String, String> getParametros() {
        return parametros;
    }

    public void setParametros(Map<String, String> parametros) {
        this.parametros = parametros;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }
}

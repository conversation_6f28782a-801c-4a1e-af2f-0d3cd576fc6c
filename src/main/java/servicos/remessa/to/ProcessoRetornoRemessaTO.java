package servicos.remessa.to;

import negocio.comuns.crm.GenericoTO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.oamd.EmpresaFinanceiroVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class ProcessoRetornoRemessaTO {

    private String nomeArquivo;
    private List<RemessaItemVO> itensProcessar;
    private RemessaVO remessaRetorno;
    private RemessaVO retornoBoleto;
    private RemessaVO remessaVO;
    private StringBuilder headRetorno;
    private Integer quantidadeAutorizacoes;
    private Integer quantidadeConfirmacoes;
    private Integer quantidadeItensReconhecidos;
    private Integer quantidadeDeItens;
    private String identificadorEmpresaFinaceiro;
    private List<EmpresaFinanceiroVO> empresaFinanceiroVOs;
    private List<GenericoTO> itensOutrasEmpresa;
    private Map<EmpresaFinanceiroVO, String> mapaItemEmpresa;
    private EmpresaFinanceiroVO empresaFinaceiro;
    private Date dataPrevistaCredito;
    private Double valorTotalDoArquivo;
    private Integer quantidadeItensNaoReconhecido;
    private Double valorTotalNaoReconhecido;
    private Double valorTotalASerBaixado;
    private Integer quantidadeItensRegistrados;
    private Integer quantidadeItensBaixaValorMenor;
    private Integer quantidadeItensBaixaValorMaior;
    private Integer quantidadeItensBaixaNormal;
    private Integer quantidadeItensBaixaEmDuplicidade;
    private List<BoletoVO> itensProcessarBoleto;


    public List<RemessaItemVO> getItensProcessar() {
        if (itensProcessar == null) {
            itensProcessar = new ArrayList<RemessaItemVO>();
        }
        return itensProcessar;
    }

    public void setItensProcessar(List<RemessaItemVO> itensProcessar) {
        this.itensProcessar = itensProcessar;
    }

    public RemessaVO getRemessaRetorno() {
        if (remessaRetorno == null) {
            remessaRetorno = new RemessaVO();
        }
        return remessaRetorno;
    }

    public void setRemessaRetorno(RemessaVO remessaRetorno) {
        this.remessaRetorno = remessaRetorno;
    }

    public RemessaVO getRetornoBoleto() {
        if (retornoBoleto == null) {
            retornoBoleto = new RemessaVO();
        }
        return retornoBoleto;
    }

    public void setRetornoBoleto(RemessaVO retornoBoleto) {
        this.retornoBoleto = retornoBoleto;
    }

    public RemessaVO getRemessaVO() {
        if (remessaVO == null) {
            remessaVO = new RemessaVO();
        }
        return remessaVO;
    }

    public void setRemessaVO(RemessaVO remessaVO) {
        this.remessaVO = remessaVO;
    }

    public StringBuilder getHeadRetorno() {
        if (headRetorno == null) {
            headRetorno = new StringBuilder();
        }
        return headRetorno;
    }

    public void setHeadRetorno(StringBuilder headRetorno) {
        this.headRetorno = headRetorno;
    }

    public Integer getQuantidadeAutorizacoes() {
        if (quantidadeAutorizacoes == null) {
            quantidadeAutorizacoes = 0;
        }
        return quantidadeAutorizacoes;
    }

    public void setQuantidadeAutorizacoes(Integer quantidadeAutorizacoes) {
        this.quantidadeAutorizacoes = quantidadeAutorizacoes;
    }

    public Integer getQuantidadeConfirmacoes() {
        if (quantidadeConfirmacoes == null) {
            quantidadeConfirmacoes = 0;
        }
        return quantidadeConfirmacoes;
    }

    public void setQuantidadeConfirmacoes(Integer quantidadeConfirmacoes) {
        this.quantidadeConfirmacoes = quantidadeConfirmacoes;
    }

    public Integer getQuantidadeItensReconhecidos() {
        if (quantidadeItensReconhecidos == null) {
            quantidadeItensReconhecidos = 0;
        }
        return quantidadeItensReconhecidos;
    }

    public void setQuantidadeItensReconhecidos(Integer quantidadeItensReconhecidos) {
        this.quantidadeItensReconhecidos = quantidadeItensReconhecidos;
    }

    public Integer getQuantidadeDeItens() {
        if (quantidadeDeItens == null) {
            quantidadeDeItens = 0;
        }
        return quantidadeDeItens;
    }

    public void setQuantidadeDeItens(Integer quantidadeDeItens) {
        this.quantidadeDeItens = quantidadeDeItens;
    }

    public List<EmpresaFinanceiroVO> getEmpresaFinanceiroVOs() {
        if (empresaFinanceiroVOs == null) {
            empresaFinanceiroVOs = new ArrayList<EmpresaFinanceiroVO>();
        }
        return empresaFinanceiroVOs;
    }

    public void setEmpresaFinanceiroVOs(List<EmpresaFinanceiroVO> empresaFinanceiroVOs) {
        this.empresaFinanceiroVOs = empresaFinanceiroVOs;
    }

    public Map<EmpresaFinanceiroVO, String> getMapaItemEmpresa() {
        return mapaItemEmpresa;
    }

    public void setMapaItemEmpresa(Map<EmpresaFinanceiroVO, String> mapaItemEmpresa) {
        this.mapaItemEmpresa = mapaItemEmpresa;
    }

    public EmpresaFinanceiroVO getEmpresaFinaceiro() {
        if (empresaFinaceiro == null) {
            empresaFinaceiro = new EmpresaFinanceiroVO();
        }
        return empresaFinaceiro;
    }

    public void setEmpresaFinaceiro(EmpresaFinanceiroVO empresaFinaceiro) {
        this.empresaFinaceiro = empresaFinaceiro;
    }

    public Date getDataPrevistaCredito() {
        return dataPrevistaCredito;
    }

    public void setDataPrevistaCredito(Date dataPrevistaCredito) {
        this.dataPrevistaCredito = dataPrevistaCredito;
    }

    public Double getValorTotalDoArquivo() {
        if (valorTotalDoArquivo == null) {
            valorTotalDoArquivo = 0.0;
        }
        return valorTotalDoArquivo;
    }

    public void setValorTotalDoArquivo(Double valorTotalDoArquivo) {
        this.valorTotalDoArquivo = valorTotalDoArquivo;
    }

    public String getIdentificadorEmpresaFinaceiro() {
        if (identificadorEmpresaFinaceiro == null) {
            identificadorEmpresaFinaceiro = "";
        }
        return identificadorEmpresaFinaceiro;
    }

    public void setIdentificadorEmpresaFinaceiro(String identificadorEmpresaFinaceiro) {
        this.identificadorEmpresaFinaceiro = identificadorEmpresaFinaceiro;
    }

    public Integer getQuantidadeItensNaoReconhecido() {
        if (quantidadeItensNaoReconhecido == null) {
            quantidadeItensNaoReconhecido = 0;
        }
        return quantidadeItensNaoReconhecido;
    }

    public void setQuantidadeItensNaoReconhecido(Integer quantidadeItensNaoReconhecido) {
        this.quantidadeItensNaoReconhecido = quantidadeItensNaoReconhecido;
    }

    public Double getValorTotalNaoReconhecido() {
        if (valorTotalNaoReconhecido == null) {
            valorTotalNaoReconhecido = 0.0;
        }
        return valorTotalNaoReconhecido;
    }

    public void setValorTotalNaoReconhecido(Double valorTotalNaoReconhecido) {
        this.valorTotalNaoReconhecido = valorTotalNaoReconhecido;
    }

    public List<GenericoTO> getItensOutrasEmpresa() {
        if (itensOutrasEmpresa == null) {
            itensOutrasEmpresa = new ArrayList<GenericoTO>();
        }
        return itensOutrasEmpresa;
    }

    public void setItensOutrasEmpresa(List<GenericoTO> itensOutrasEmpresa) {
        this.itensOutrasEmpresa = itensOutrasEmpresa;
    }

    public Double getValorTotalASerBaixado() {
        if (valorTotalASerBaixado == null) {
            valorTotalASerBaixado = 0.0;
        }
        return valorTotalASerBaixado;
    }

    public void setValorTotalASerBaixado(Double valorTotalASerBaixado) {
        this.valorTotalASerBaixado = valorTotalASerBaixado;
    }

    public Integer getQuantidadeItensRegistrados() {
        if (quantidadeItensRegistrados == null) {
            quantidadeItensRegistrados = 0;
        }
        return quantidadeItensRegistrados;
    }

    public void setQuantidadeItensRegistrados(Integer quantidadeItensRegistrados) {
        this.quantidadeItensRegistrados = quantidadeItensRegistrados;
    }

    public Integer getQuantidadeItensBaixaValorMenor() {
        if (quantidadeItensBaixaValorMenor == null) {
            quantidadeItensBaixaValorMenor = 0;
        }
        return quantidadeItensBaixaValorMenor;
    }

    public void setQuantidadeItensBaixaValorMenor(Integer quantidadeItensBaixaValorMenor) {
        this.quantidadeItensBaixaValorMenor = quantidadeItensBaixaValorMenor;
    }

    public Integer getQuantidadeItensBaixaValorMaior() {
        if (quantidadeItensBaixaValorMaior == null) {
            quantidadeItensBaixaValorMaior = 0;
        }
        return quantidadeItensBaixaValorMaior;
    }

    public void setQuantidadeItensBaixaValorMaior(Integer quantidadeItensBaixaValorMaior) {
        this.quantidadeItensBaixaValorMaior = quantidadeItensBaixaValorMaior;
    }

    public Integer getQuantidadeItensBaixaNormal() {
        if (quantidadeItensBaixaNormal == null) {
            quantidadeItensBaixaNormal = 0;
        }
        return quantidadeItensBaixaNormal;
    }

    public void setQuantidadeItensBaixaNormal(Integer quantidadeItensBaixaNormal) {
        this.quantidadeItensBaixaNormal = quantidadeItensBaixaNormal;
    }

    public Integer getQuantidadeItensBaixaEmDuplicidade() {
        if (quantidadeItensBaixaEmDuplicidade == null) {
            quantidadeItensBaixaEmDuplicidade = 0;
        }
        return quantidadeItensBaixaEmDuplicidade;
    }

    public void setQuantidadeItensBaixaEmDuplicidade(Integer quantidadeItensBaixaEmDuplicidade) {
        this.quantidadeItensBaixaEmDuplicidade = quantidadeItensBaixaEmDuplicidade;
    }

    public String getNomeArquivo() {
        if (nomeArquivo == null) {
            nomeArquivo = "";
        }
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public List<BoletoVO> getItensProcessarBoleto() {
        if (itensProcessarBoleto == null) {
            itensProcessarBoleto = new ArrayList<>();
        }
        return itensProcessarBoleto;
    }

    public void setItensProcessarBoleto(List<BoletoVO> itensProcessarBoleto) {
        this.itensProcessarBoleto = itensProcessarBoleto;
    }
}

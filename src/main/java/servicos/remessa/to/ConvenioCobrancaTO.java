package servicos.remessa.to;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import servicos.remessa.dao.RemessaCriptografia;

/**
 * Created by Johnys on 05/02/2017.
 */
public class ConvenioCobrancaTO {

    private Long codigo;

    private String numeroContrato;

    private String numeroLogico;

    private TipoConvenioCobrancaEnum tipoConvenio;

    private FTPTO ftp;

    private RemessaCriptografia remessaCriptografia;

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public String getNumeroLogico() {
        return numeroLogico;
    }

    public void setNumeroLogico(String numeroLogico) {
        this.numeroLogico = numeroLogico;
    }

    public TipoConvenioCobrancaEnum getTipoConvenio() {
        return tipoConvenio;
    }

    public void setTipoConvenio(TipoConvenioCobrancaEnum tipoConvenio) {
        this.tipoConvenio = tipoConvenio;
    }

    public FTPTO getFtp() {
        return ftp;
    }

    public void setFtp(FTPTO ftp) {
        this.ftp = ftp;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public RemessaCriptografia getRemessaCriptografia() {
        return remessaCriptografia;
    }

    public void setRemessaCriptografia(RemessaCriptografia remessaCriptografia) {
        this.remessaCriptografia = remessaCriptografia;
    }

    @Override
    public String toString() {
        return String.format("ConvenioCobrancaTO{codigo=%s, tipoConvenio=%s}", 
                codigo, tipoConvenio);
    }
    
    
}

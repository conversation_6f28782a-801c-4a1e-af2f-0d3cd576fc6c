package servicos.remessa.util;

import controle.arquitetura.security.AlgoritmoCriptoEnum;
import negocio.comuns.utilitarias.Criptografia;

/**
 * Created by Johnys on 05/02/2017.
 */
public class UtilCriptografia {

    public static final String CHAVE_CRIPTOGRAFIA_COMUM = "P@cT0zIlLy0nW3b1";

    public static final AlgoritmoCriptoEnum ALGORITMO_CRIPTOGRAFIA = AlgoritmoCriptoEnum.ALGORITMO_AES;

    public static String decifrar(final String numCartao) {
        String nCartao = numCartao.contains("=") ? Criptografia.decrypt(numCartao,CHAVE_CRIPTOGRAFIA_COMUM, ALGORITMO_CRIPTOGRAFIA) : numCartao;
        return nCartao;
    }

    public static String encriptar(final String numCartao) throws Exception {
        String nCartao = Criptografia.encrypt(numCartao, CHAVE_CRIPTOGRAFIA_COMUM, ALGORITMO_CRIPTOGRAFIA);
        return nCartao;
    }

}

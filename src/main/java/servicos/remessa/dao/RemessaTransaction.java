package servicos.remessa.dao;

import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.remessa.to.RemessaTO;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Johnys on 11/02/2017.
 */
public class RemessaTransaction extends SuperEntidadeRemessaTransaction{

    private static final String SQL_INSERT = "INSERT INTO remessa(convenio, identificador, datageracao, parametros, chave, nomearquivo, situacao) VALUES (?, ?, ?, ?, ?, ?, ?);";
    private static final String SQL_UPDATE = "UPDATE remessa SET convenio=?, identificador=?, datageracao=?, parametros=?, chave=?, nomearquivo=?, situacao = ?, retorno=? WHERE codigo = ?;";
    private static final String NOME_TABELA = "remessa";


    /**
     * Realiza a inserção de uma nova {@link RemessaTO}
     * @param remessa
     * @param commit
     * @throws Exception
     */
    public void inserir(RemessaTO remessa, Boolean commit) throws Exception{
        try{
            controlarTransacao(commit);
            new ConvenioCobrancaTransaction().inserir(remessa.getConvenio(), commit);
            executarUpdate(SQL_INSERT, new Object[]{remessa.getConvenio().getCodigo(), remessa.getIdentificador(),
                    Uteis.getDataJDBC(remessa.getDataGeracao()), remessa.getParametros().toString(), remessa.getChave(), remessa.getNomeArquivo(),
                    remessa.getSituacaoRemessa() == null ? null : remessa.getSituacaoRemessa().getId()});
            remessa.setCodigo(obterUltimoSequencial(NOME_TABELA));
            new RemessaItemTransaction().inserir(remessa.getItens(), commit);
            commit();
        }catch (Exception e){
            rollback();
            throw e;
        }
    }

    /**
     * Verifica se existe uma {@link RemessaTO} a partir do seu identificador e sua chave.
     * @param identificador
     * @param chave
     * @return
     * @throws Exception
     */
    public Boolean existePorIdentificador(Long identificador, String chave) throws Exception{
        String sql = "SELECT 1 FROM remessa WHERE identificador = ? AND chave = ?";
        PreparedStatement ps = prepararStatement(sql, new Object[]{identificador, chave});
        return ps.executeQuery().next();
    }

    /**
     * Realiza a alteração de uma {@link RemessaTO}
     * @param remessa
     * @param commit
     * @throws Exception
     */
    public void alterar(RemessaTO remessa, Boolean commit) throws Exception{
        try{
            controlarTransacao(commit);
            executarUpdate(SQL_UPDATE, new Object[]{remessa.getConvenio().getCodigo(), remessa.getIdentificador(), remessa.getDataGeracao(), remessa.getParametros().toString(), remessa.getChave(), remessa.getNomeArquivo(), remessa.getSituacaoRemessa() == null ? null : remessa.getSituacaoRemessa().getId(), remessa.getRetorno(), remessa.getCodigo()});
            new RemessaItemTransaction().alterar(remessa.getItens(), commit);
            commit();
        }catch (Exception e){
            rollback();
            throw e;
        }
    }

    /**
     * Consulta as remessas por sua {@link SituacaoRemessaEnum}
     * @param situacao
     * @return
     * @throws Exception
     */
    public List<RemessaTO> consultarPorSituacao(SituacaoRemessaEnum situacao) throws Exception{
        String sql = "SELECT * FROM remessa WHERE situacao = ? ORDER BY codigo";
        PreparedStatement ps = prepararStatement(sql, new Object[]{situacao.getId()});
        return montarLista(ps.executeQuery());
    }

    /**
     * Monta a lista de {@link RemessaTO} a partir do {@link ResultSet}
     * @param rs
     * @return
     * @throws SQLException
     */
    private List<RemessaTO> montarLista(ResultSet rs) throws Exception{
        List<RemessaTO> list = new ArrayList<RemessaTO>();
        while(rs.next()){
            list.add(montarDados(rs));
        }
        return list;
    }

    /**
     * Monta os dados de uma {@link RemessaTO} a partir do {@link ResultSet}
     * @param rs
     * @return
     * @throws SQLException
     */
    private RemessaTO montarDados(ResultSet rs) throws Exception{
        RemessaTO remessa = new RemessaTO();
        remessa.setCodigo(rs.getLong("codigo"));
        remessa.setIdentificador(rs.getLong("identificador"));
        remessa.setDataGeracao(rs.getDate("datageracao"));
        remessa.setChave(rs.getString("chave"));
        remessa.setNomeArquivo(rs.getString("nomearquivo"));
        remessa.setSituacaoRemessa(SituacaoRemessaEnum.valueOf(rs.getInt("situacao")));
        remessa.setConvenio(new ConvenioCobrancaTransaction().consultarPorCodigo(rs.getLong("convenio")));
        remessa.setParametros(Uteis.obterMapFromString(rs.getString("parametros")));
        remessa.setItens(new RemessaItemTransaction().consultarPorRemessa(remessa));
        remessa.setRetorno(rs.getString("retorno"));
        return remessa;
    }

    /**
     * Consulta uma {@link RemessaTO} a partir do numero do contrato e do identificador.
     * @param numeroContrato
     * @param identificador
     * @return
     * @throws Exception
     */
    public RemessaTO consultarPorNumeroContratoIdentificador(String numeroContrato, Integer identificador) throws Exception{
        String sql = "SELECT * FROM remessa r INNER JOIN convenio c ON C.codigo = r.convenio WHERE r.identificador = ? AND c.numerocontrato = ?";
        ResultSet rs = prepararStatement(sql, new Object[]{identificador, numeroContrato}).executeQuery();
        return rs.next() ? montarDados(rs) : null;
    }

    public RemessaTO consultarPorIdentificadorChave(Long identificador, String chave) throws Exception{
        String sql = "SELECT * FROM remessa r WHERE r.identificador = ? AND r.chave = ?";
        ResultSet rs = prepararStatement(sql, new Object[]{identificador, chave}).executeQuery();
        return rs.next() ? montarDados(rs) : null;
    }
}

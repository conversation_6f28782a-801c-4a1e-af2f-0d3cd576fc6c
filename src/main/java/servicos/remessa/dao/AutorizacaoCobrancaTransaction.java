package servicos.remessa.dao;

import servicos.remessa.to.AutorizacaoCartaoTO;
import servicos.remessa.to.AutorizacaoTO;

import java.sql.ResultSet;

/**
 * Created by Johnys on 19/02/2017.
 */
public class AutorizacaoCobrancaTransaction extends SuperEntidadeRemessaTransaction{

    private static final String SQL_INSERT = "INSERT INTO autorizacao(codigo) VALUES (?);";
    private static final String SQL_INSERT_CARTAO = "INSERT INTO autorizacaocartao(codigo, numerocartao, validade) VALUES (?, ?, ?);";
    private static final String NOME_TABELA = "autorizacao";
    private static final String NOME_TABELA_CARTAO = "autorizacaocartao";

    /**
     * Realiza a insercao de uma {@link AutorizacaoTO}
     * @param autorizacao
     * @param commit
     * @throws Exception
     */
    public void inserir(AutorizacaoTO autorizacao, Boolean commit) throws Exception{
        try{
            controlarTransacao(commit);
            autorizacao.setCodigo(obterProximoSequencial(NOME_TABELA));
            executarUpdate(SQL_INSERT, new Object[]{autorizacao.getCodigo()});
            if(autorizacao instanceof AutorizacaoCartaoTO){
                AutorizacaoCartaoTO cartao = (AutorizacaoCartaoTO) autorizacao;
                executarUpdate(SQL_INSERT_CARTAO, new Object[]{autorizacao.getCodigo(), cartao.getNumeroCartao(), cartao.getValidade()});
            }
            commit();
        }catch (Exception e){
            rollback();
            throw e;
        }
    }

    /**
     * Realiza a consulta de uma {@link AutorizacaoCartaoTO} a partir de seu codigo.
     * @param codigo
     * @return
     */
    public AutorizacaoTO consultarPorCodigo(Long codigo) throws Exception{
        String sql = "SELECT * FROM autorizacaocartao WHERE codigo = ?";
        return montarDados(prepararStatement(sql, new Object[]{codigo}).executeQuery());
    }

    /**
     * Realiza a montagem de uma {@link AutorizacaoCartaoTO} a partir de seu resultset
     * @param resultSet
     * @return
     * @throws Exception
     */
    private AutorizacaoTO montarDados(ResultSet rs) throws Exception{
        rs.next();
        AutorizacaoCartaoTO auto = new AutorizacaoCartaoTO();
        auto.setNumeroCartao(rs.getString("numerocartao"));
        auto.setValidade(rs.getString("validade"));
        auto.setCodigo(rs.getLong("codigo"));
        return auto;
    }
}

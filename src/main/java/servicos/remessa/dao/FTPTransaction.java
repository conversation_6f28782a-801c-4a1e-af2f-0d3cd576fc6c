package servicos.remessa.dao;

import servicos.remessa.to.FTPTO;

import java.sql.ResultSet;

/**
 * Created by Johnys on 19/02/2017.
 */
public class FTPTransaction extends SuperEntidadeRemessaTransaction{

    private static final String SQL_INSERT = "INSERT INTO ftp(host, usuario, senha, porta, diretorioremotoremessa, diretorioremotoretorno, diretoriolocalremessa, diretoriolocalretorno) values (?, ?, ?, ?, ?, ?, ?, ?);";
    private static final String SQL_UPDATE = "UPDATE ftp SET host=?, usuario=?, senha=?, porta=?, diretorioremotoremessa=?, diretorioremotoretorno=?, diretoriolocalremessa=?, diretoriolocalretorno=?  where codigo=?;";
    private static final String NOME_TABELA = "ftp";

    /**
     * Raeliza a inserção de um {@link FTPTO}
     * @param ftp
     * @param commit
     * @throws Exception
     */
    public void inserir(FTPTO ftp, Boolean commit) throws Exception{
        try{
            controlarTransacao(commit);
            executarUpdate(SQL_INSERT, new Object[]{ftp.getHost(), ftp.getUsuario(), ftp.getSenha(), ftp.getPorta(), ftp.getDiretorioRemotoRemessa(), ftp.getDiretorioRemotoRetorno(),ftp.getDiretorioLocalRemessa(), ftp.getDiretorioLocalRetorno()});
            ftp.setCodigo(obterUltimoSequencial(NOME_TABELA));
            commit();
        }catch (Exception e){
            rollback();
            throw e;
        }
    }

    /**
     * Realiza a consulta de um {@link FTPTO} a partir de seu codigo.
     * @param codigo
     * @return
     * @throws Exception
     */
    public FTPTO consultarPorCodigo(Long codigo) throws Exception{
        String sql = "SELECT * FROM ftp WHERE codigo = ?";
        return montarDados(prepararStatement(sql, new Object[]{codigo}).executeQuery());
    }

    /**
     * Monta um {@link FTPTO} a partir de seu {@link ResultSet}
     * @param rs
     * @return
     */
    private FTPTO montarDados(ResultSet rs) throws Exception{
        rs.next();
        FTPTO ftp = new FTPTO();
        ftp.setCodigo(rs.getLong("codigo"));
        ftp.setUsuario(rs.getString("usuario"));
        ftp.setSenha(rs.getString("senha"));
        ftp.setHost(rs.getString("host"));
        ftp.setPorta(rs.getString("porta"));
        ftp.setDiretorioLocalRemessa(rs.getString("diretoriolocalremessa"));
        ftp.setDiretorioLocalRetorno(rs.getString("diretoriolocalretorno"));
        ftp.setDiretorioRemotoRemessa(rs.getString("diretorioremotoremessa"));
        ftp.setDiretorioRemotoRetorno(rs.getString("diretorioremotoretorno"));
        return ftp;
    }
}

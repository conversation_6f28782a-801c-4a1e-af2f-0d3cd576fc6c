package servicos.remessa.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Created by Johnys on 11/02/2017.
 */
public class SuperEntidadeRemessaTransaction {

    private static ThreadLocal<Connection> conexoes = new ThreadLocal<Connection>();
    private static ThreadLocal<Integer> controleTransacao = new ThreadLocal<Integer>();

    /**
     * Obtem uma conexao com o banco de dados.
     * @return
     * @throws Exception
     */
    protected Connection obterConexao() throws Exception{
        if(conexoes.get() == null){
            carregarConexao();
        }
        return conexoes.get();
    }

    /**
     * Realiza o carregamento da conexao para o {@link ThreadLocal}
     * @throws Exception
     */
    private void carregarConexao() throws Exception{
        conexoes.set(ConexaoRemessaFactory.obterNovaConexao());
    }

    /**
     * Inicia uma nova transação no banco de dados.
     * @throws Exception
     */
    protected void iniciarTransacao() throws Exception{
        Connection con = obterConexao();
        con.setAutoCommit(false);
    }

    /**
     * Realiza o commit da transação.
     * @throws Exception
     */
    protected void commit() throws Exception{
        if(controleTransacao.get() == null || controleTransacao.get().equals(System.identityHashCode(this))){
            Connection con = obterConexao();
            con.setAutoCommit(true);
            con.commit();
            conexoes.set(null);
            controleTransacao.set(null);
        }
    }

    /**
     * Realiza o rollback da transação atual.
     * @throws Exception
     */
    protected  void rollback() throws Exception{
        obterConexao().rollback();
    }

    /**
     * Realiza o controle de transação.
     * @param commit
     * @throws Exception
     */
    protected void controlarTransacao(Boolean commit)throws Exception{
        if(commit && controleTransacao.get() == null){
           iniciarTransacao();
            controleTransacao.set(System.identityHashCode(this));
        }
    }

    /**
     * Realiza a execução de uma sql de update
     * @param sql
     * @param params
     * @throws Exception
     */
    protected void executarUpdate(String sql, Object[] params) throws Exception{
        PreparedStatement ps = prepararStatement(sql, params);
        ps.executeUpdate();
    }

    /**
     * Realiza a criação do {@link PreparedStatement} com seus parâmetros
     * @param sql
     * @param params
     * @return
     * @throws Exception
     */
    protected PreparedStatement prepararStatement(String sql, Object[] params) throws Exception{
        PreparedStatement ps = obterConexao().prepareStatement(sql);
        for(int i = 0; i < params.length; i++){
            ps.setObject(i+1, params[i]);
        }
        return ps;
    }

    /**
     * Obtem o ultimo codigo gerado para a tabela.
     * @param nomeTabela
     * @return
     * @throws Exception
     */
    protected Long obterUltimoSequencial(String nomeTabela) throws Exception{
        String csCodigoGerado = "SELECT last_value FROM " + nomeTabela + "_codigo_seq";
        Statement stmt = obterConexao().createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getLong(1));
    }

    /**
     * Obtem o ultimo codigo gerado para a tabela.
     * @param nomeTabela
     * @return
     * @throws Exception
     */
    protected Long obterProximoSequencial(String nomeTabela) throws Exception{
        String csCodigoGerado = "SELECT nextVal('" + nomeTabela + "_codigo_seq')";
        Statement stmt = obterConexao().createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultado = stmt.executeQuery(csCodigoGerado);
        resultado.first();
        return (resultado.getLong(1));
    }


}

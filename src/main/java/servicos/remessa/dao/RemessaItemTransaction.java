package servicos.remessa.dao;

import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Uteis;
import servicos.remessa.to.RemessaItemTO;
import servicos.remessa.to.RemessaTO;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Johnys on 19/02/2017.
 */
public class RemessaItemTransaction extends SuperEntidadeRemessaTransaction {

    private static final String SQL_INSERT = "INSERT INTO remessaitem(remessa, identificador, valor, valorpago, codigoerro, parametros, situacao, autorizacao) VALUES (?, ?, ?, ?, ?, ?, ?, ?);";
    private static final String SQL_UPDATE = "UPDATE remessaitem SET remessa=?, identificador=?, valor=?, valorpago=?, codigoerro=?, parametros=?, situacao=?, autorizacao=? WHERE codigo=?;";
    private static final String NOME_TABELA = "remessaitem";


    /**
     * Realiza a inserção de um {@link RemessaItemTO}
     * @param item
     * @param commit
     * @throws Exception
     */
    public void inserir(RemessaItemTO item, Boolean commit) throws Exception{
        try {
            System.out.println(" [[FACILITE]] Vou tentar inserir o tem da remessa "+item.getRemessa().getCodigo());
            controlarTransacao(commit);
            new AutorizacaoCobrancaTransaction().inserir(item.getAutorizacao(), commit);
            executarUpdate(SQL_INSERT, new Object[]{item.getRemessa().getCodigo(), item.getIdentificador(), item.getValor(), item.getValorPago(), item.getCodigoErro(), item.getParametros().toString(), item.getSituacao().getId(), item.getAutorizacao().getCodigo()});
            item.setCodigo(obterUltimoSequencial(NOME_TABELA));
            commit();
        } catch (Exception e) {
            System.out.println(" [[FACILITE]] ERRO AO INSERIR ITEM "+e.getMessage());
            rollback();
            throw e;
        }
    }

    /**
     * Realiza a alteracao de um {@link RemessaItemTO}
     *
     * @param item
     * @param commit
     * @throws Exception
     */
    public void alterar(RemessaItemTO item, Boolean commit) throws Exception {
        try {
            controlarTransacao(commit);
            executarUpdate(SQL_UPDATE, new Object[]{item.getRemessa().getCodigo(), item.getIdentificador(), item.getValor(), item.getValorPago(), item.getCodigoErro(), item.getParametros().toString(), item.getSituacao().getId(), item.getAutorizacao().getCodigo(), item.getCodigo()});
            commit();
        } catch (Exception e) {
            rollback();
            throw e;
        }
    }

    /**
     * Realiza a alteração de uma lista de {@link RemessaItemTO}
     *
     * @param itens
     * @param commit
     * @throws Exception
     */
    public void alterar(List<RemessaItemTO> itens, Boolean commit) throws Exception {
        try {
            controlarTransacao(commit);
            for (RemessaItemTO item : itens) {
                alterar(item, false);
            }
            commit();
        } catch (Exception e) {
            rollback();
            throw e;
        }
    }


    /**
     * Realiza a inserção de uma lista de {@link RemessaItemTO}
     *
     * @param itens
     * @param commit
     * @throws Exception
     */
    public void inserir(List<RemessaItemTO> itens, Boolean commit) throws Exception {
        try {
            controlarTransacao(commit);
            for (RemessaItemTO item : itens) {
                inserir(item, false);
            }
            commit();
        } catch (Exception e) {
            rollback();
            throw e;
        }
    }

    /**
     * Raliza a consulta da lista de {@link RemessaItemTO} a partir do codigo da remessa.
     * @param codigoRemessa
     * @return
     */
    public List<RemessaItemTO> consultarPorRemessa(RemessaTO remessa) throws Exception{
        String sql = "SELECT * FROM remessaitem WHERE remessa = ?";
        return montarLista(prepararStatement(sql, new Object[]{remessa.getCodigo()}).executeQuery(), remessa);
    }

    /**
     * Monta a lista de {@link RemessaItemTO} a partir do {@link ResultSet}
     * @param rs
     * @param remessa
     * @return
     * @throws Exception
     */
    private List<RemessaItemTO> montarLista(ResultSet rs, RemessaTO remessa) throws Exception{
        List<RemessaItemTO> lista = new ArrayList<RemessaItemTO>();
        while(rs.next()){
            RemessaItemTO item = montarDados(rs);
            item.setRemessa(remessa);
            lista.add(item);
        }
        return lista;
    }

    /**
     * Realiza a montagem de um {@link RemessaItemTO} a partir de seu {@link ResultSet}
     * @param rs
     * @return
     */
    private RemessaItemTO montarDados(ResultSet rs) throws Exception{
        RemessaItemTO item = new RemessaItemTO();
        item.setValor(rs.getBigDecimal("valor"));
        item.setCodigo(rs.getLong("codigo"));
        item.setIdentificador(rs.getLong("identificador"));
        item.setAutorizacao(new AutorizacaoCobrancaTransaction().consultarPorCodigo(rs.getLong("autorizacao")));
        item.setCodigoErro(rs.getString("codigoerro"));
        item.setParametros(Uteis.obterMapFromString(rs.getString("parametros")));
        item.setSituacao(SituacaoRemessaEnum.valueOf(rs.getInt("situacao")));
        item.setValorPago(rs.getBigDecimal("valorpago"));
        return item;
    }
}
package servicos.remessa.dao;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import servicos.remessa.to.ConvenioCobrancaTO;

import java.sql.ResultSet;

/**
 * Created by Johnys on 19/02/2017.
 */
public class ConvenioCobrancaTransaction extends SuperEntidadeRemessaTransaction{

    private static final String SQL_INSERT = "INSERT INTO convenio(numerocontrato, tipoconvenio, ftp, numerologico) VALUES (?, ?, ?, ?);";
    private static final String SQL_UPDATE = "UPDATE convenio SET numerocontrato=?, tipoconvenio=?, ftp=?, numerologico = ?WHERE codigo=?;";
    private static final String NOME_TABELA = "convenio";

    /**
     * Realiza a insercao de um {@link ConvenioCobrancaTO}
     * @param convenio
     * @param commit
     * @throws Exception
     */
    public void inserir(ConvenioCobrancaTO convenio, Boolean commit) throws Exception{
        try{
            controlarTransacao(commit);
            new FTPTransaction().inserir(convenio.getFtp(), commit);
            executarUpdate(SQL_INSERT, new Object[]{convenio.getNumeroContrato(), convenio.getTipoConvenio().getCodigo(), convenio.getFtp().getCodigo(), convenio.getNumeroLogico()});
            convenio.setCodigo(obterUltimoSequencial(NOME_TABELA));
            commit();
        }catch (Exception e){
            rollback();
            throw e;
        }
    }

    /**
     * Consulta um {@link ConvenioCobrancaTO} a partir de seu codigo.
     * @param convenio
     * @return
     * @throws Exception
     */
    public ConvenioCobrancaTO consultarPorCodigo(Long convenio) throws Exception{
        String sql = "SELECT * FROM convenio WHERE codigo = ?";
        return montarDados(prepararStatement(sql, new Object[]{convenio}).executeQuery());
    }

    /**
     * Realiza a montagem de um {@link ConvenioCobrancaTO} a partir do seu {@link ResultSet}
     * @param rs
     * @return
     * @throws Exception
     */
    private ConvenioCobrancaTO montarDados(ResultSet rs) throws Exception{
        rs.next();
        ConvenioCobrancaTO convenio = new ConvenioCobrancaTO();
        convenio.setCodigo(rs.getLong("codigo"));
        convenio.setNumeroLogico(rs.getString("numerologico"));
        convenio.setNumeroContrato(rs.getString("numerocontrato"));
        convenio.setTipoConvenio(TipoConvenioCobrancaEnum.valueOf(rs.getInt("tipoconvenio")));
        convenio.setFtp(new FTPTransaction().consultarPorCodigo(rs.getLong("ftp")));
        convenio.setRemessaCriptografia(new RemessaCriptografia().consultarPorTipoConvenio(convenio.getTipoConvenio()));
        return convenio;
    }
}

package servicos.remessa.dao;

import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * Fábrica de conexões com o banco de remessas
 *
 * <AUTHOR>
 * @since 10/10/2018
 */
public final class ConexaoRemessaFactory {

    private static final String URL_BD_REMESSA_TAG = "urlBdRemessa";
    private static final String USER_BD_REMESSA_TAG = "userBdRemessa";
    private static final String PWD_BD_REMESSA_TAG = "pwdBdRemessa";

    private static final String URL_BANCO;
    private static final String USUARIO_BANCO;
    private static final String SENHA_BANCO;

    static {
        try {
            final String xml = Uteis.getXMLDocumentCFG(Uteis.nomeArqCFG);
            URL_BANCO = Uteis.getValorTAG(xml, URL_BD_REMESSA_TAG);
            USUARIO_BANCO = Uteis.getValorTAG(xml, USER_BD_REMESSA_TAG);
            SENHA_BANCO = Uteis.getValorTAG(xml, PWD_BD_REMESSA_TAG);
            Class.forName("org.postgresql.Driver");
        } catch (Exception e) {
            throw new RuntimeException("N\u00c3O FOI POSS\u00cdVEL CARREGAR AS CONFIGURA\u00c7\u00d5ES DO BANCO DE DADOS DE REMESSA", e);
        }
    }

    /**
     * Obt&eacute;m uma nova conex&atilde;o com o banco centralizado de remessas.
     * <br/><b>ATEN&Ccedil;&Atilde;O</b>: Este n&atilde;o &eacute; o banco do cliente, mas sim o banco de remessas que fica centralizado
     *
     * @return Uma nova conex&atilde;o com o banco de remessas centralizado
     * @throws Exception Caso ocorra algum erro ao obter a conex&atilde;o
     */
    public static Connection obterNovaConexao() throws Exception {
        return DriverManager.getConnection(URL_BANCO, USUARIO_BANCO, SENHA_BANCO);
    }

}

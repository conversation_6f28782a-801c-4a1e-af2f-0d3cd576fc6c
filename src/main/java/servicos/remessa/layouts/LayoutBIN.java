package servicos.remessa.layouts;

import annotations.arquitetura.Lista;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import servicos.impl.apf.APF;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.remessa.dao.RemessaTransaction;
import servicos.remessa.to.AutorizacaoCartaoTO;
import servicos.remessa.to.ConvenioCobrancaTO;
import servicos.remessa.to.RemessaItemTO;
import servicos.remessa.to.RemessaTO;
import servicos.remessa.util.UtilCriptografia;

import java.io.BufferedReader;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by Johnys on 05/02/2017.
 */
public class LayoutBIN extends LayoutArquivoFTP{

    private BigDecimal somaDetail = new BigDecimal(0.0);

    public LayoutBIN(){}

    public LayoutBIN(RemessaTO remessa) {
        super(remessa);
    }
    
        /**
     * Realiza a montagem do {@link StringBuilder} referente ao arquivo.
     * @param remessa
     * @return
     */
    @Override
    public StringBuilder montarArquivo(){
        RegistroRemessa header = getHeader();
        List<RegistroRemessa> detail = getDetail();
        RegistroRemessa trailer = getTrailer();
        StringBuilder arquivo = new StringBuilder();
        arquivo.append(header.toStringBuffer().toString());
        arquivo.append("\n");
        for(RegistroRemessa reg : detail){
            arquivo.append(reg.toStringBuffer().toString());
            arquivo.append("\n");
        }
        arquivo.append(trailer.toStringBuffer());
        arquivo.append("\n");
        return arquivo;
    }
    
    protected List<RegistroRemessa> getDetail(){
        List<RegistroRemessa> details = new ArrayList<RegistroRemessa>();
        Integer linha = 0;
        for(RemessaItemTO item : remessa.getItens()){
            linha++;
            details.add(getDetail(item, linha));
        }
        return details;
    }
    

    @Override
    protected RegistroRemessa getHeader() {
        RegistroRemessa header = super.getHeader();
        header.put(DCCAttEnum.TipoRegistro, "01");
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataGeracao(),"yyyyMMdd"));
        header.put(DCCAttEnum.DataVenda, StringUtilities.formatarCampoData(Calendario.hoje(),"yyyyMMdd"));
        header.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenio().getNumeroContrato()), 10));
        header.put(DCCAttEnum.ReservadoEstabelecimento, StringUtilities.formatarCampo(new BigDecimal(remessa.getIdentificador(), MathContext.UNLIMITED), 20));
        header.put(DCCAttEnum.IndicadorProcesso, "P");
        header.put(DCCAttEnum.CodigoRetorno, StringUtilities.formatarCampoEmBranco(2));
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(149));
        return header;
    }

    @Override
    protected RegistroRemessa getTrailer() {
        RegistroRemessa trailer = super.getTrailer();
        trailer.put(DCCAttEnum.TipoRegistro, "03");
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(remessa.getItens().size()), 8));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(somaDetail.doubleValue(), 14));
        trailer.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(176));
        return trailer;
    }

    @Override
    protected RegistroRemessa getDetail(RemessaItemTO item, Integer linha) {
        RegistroRemessa detail = super.getDetail(item, linha);
        somaDetail = somaDetail.add(item.getValor());
        AutorizacaoCartaoTO cartao = (AutorizacaoCartaoTO) item.getAutorizacao();
        detail.put(DCCAttEnum.TipoRegistro, "02");
        String nCartao = UtilCriptografia.decifrar(cartao.getNumeroCartao());
        detail.put(DCCAttEnum.NumeroCartao, StringUtilities.formatarCampo(new BigDecimal(nCartao.trim(), MathContext.UNLIMITED), 20));
        detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValor().doubleValue(), 12));
        detail.put(DCCAttEnum.NumeroReferencia, StringUtilities.formatarCampoZerado(16));
        detail.put(DCCAttEnum.IndicacaoEspecial, "0");
        detail.put(DCCAttEnum.CodigoAutorizacao, StringUtilities.formatarCampoEmBranco(6));
        detail.put(DCCAttEnum.ValidadeCartao, cartao.getValidade());
        detail.put(DCCAttEnum.StatusVenda, StringUtilities.formatarCampoEmBranco(2));
        detail.put(DCCAttEnum.CodigoCorrente, "986");
        detail.put(DCCAttEnum.CVV2, StringUtilities.formatarCampoZerado(3));
        detail.put(DCCAttEnum.CVV2, StringUtilities.formatarCampoEmBranco(3));
        detail.put(DCCAttEnum.TipoTransacao,"3");
        detail.put(DCCAttEnum.TipoVenda,"00");
        detail.put(DCCAttEnum.TipoEstorno,"0");
        detail.put(DCCAttEnum.DataTransacao,StringUtilities.formatarCampoData(Calendario.hoje(),"yyyyMMdd"));
        detail.put(DCCAttEnum.ValorEstorno,StringUtilities.formatarCampoZerado(12));
        detail.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.formatarCampo(new BigDecimal(item.getIdentificador()), 15));
        detail.put(DCCAttEnum.SolicitacaoEstorno, StringUtilities.formatarCampoZerado(15));
        detail.put(DCCAttEnum.NumeroTerminal, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenio().getNumeroLogico(),8));
        detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(66));
        return detail;
    }

    @Override
    public RemessaTO descobrirRemessa(StringBuilder retorno) throws Exception{
        BufferedReader arquivo = new BufferedReader(new StringReader(retorno.toString()));
        String linha = arquivo.readLine();
        Long numeroContrato = Long.valueOf(StringUtilities.readString(18, 28, linha));
        Long identificador = Long.valueOf(StringUtilities.readString(28, 48, linha));
        return new RemessaTransaction().consultarPorNumeroContratoIdentificador(numeroContrato.toString(), identificador.intValue());
    }

    @Override
    public void processarAtributosRetorno(RemessaTO remessa, StringBuilder retorno) throws Exception{
        super.processarAtributosRetorno(remessa, retorno);
        BufferedReader arquivo = new BufferedReader(new StringReader(retorno.toString()));
        String linha = null;
        Map<Long, RemessaItemTO> itens = mapearItensRemessaPorIdentificador(remessa);
        while((linha = arquivo.readLine()) != null){
            if(linha.startsWith("02")){
                processarLinha(itens, linha);
            }
        }
        remessa.setSituacaoRemessa(SituacaoRemessaEnum.RETORNO_PROCESSADO);
        remessa.setRetorno(retorno.toString());
    }

    private void processarLinha(Map<Long, RemessaItemTO> itens, String linha) throws Exception{
        Long identificador = Long.valueOf(StringUtilities.readString(96, 111, linha));
        RemessaItemTO item = itens.get(identificador);
        item.getParametros().put(DCCAttEnum.CodigoAutorizacao.name(), StringUtilities.readString(51,57, linha));
        String statusVenda = StringUtilities.readString(61, 63, linha);
        if(statusVenda.equals("00")){
            item.setValorPago(item.getValor());
            item.setSituacao(SituacaoRemessaEnum.RETORNO_PROCESSADO);
        }else{
            item.setCodigoErro(statusVenda);
            item.setSituacao(SituacaoRemessaEnum.ERRO_RETORNO);
        }
    }
}

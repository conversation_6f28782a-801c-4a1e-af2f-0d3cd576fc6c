package servicos.remessa.layouts;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.remessa.dao.RemessaTransaction;
import servicos.remessa.to.ConvenioCobrancaTO;
import servicos.remessa.to.RemessaTO;
import servicos.util.SFTP;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.CaixaPostalSFTP;
import servicos.impl.dcc.base.RetornoService;

/**
 * Created by Johnys on 11/02/2017.
 */
public class LayoutArquivoFTP extends LayoutBase{

    protected List<Map<String, File>> arquivos = new ArrayList<Map<String, File>>();

    public LayoutArquivoFTP(){}

    public LayoutArquivoFTP(RemessaTO remessa) {
        super(remessa);
    }

    @Override
    public List<RemessaTO> processarRetorno(ConvenioCobrancaTO convenio) throws Exception{
        receberRetornos(convenio, false);
        List<RemessaTO> lista = super.processarRetorno(convenio);
        for(Map<String, File> arquivo : arquivos){
            for(String fileName : arquivo.keySet()){
                try{
                    File f = arquivo.get(fileName);
                    if(f.length() > 0){
                        StringBuilder sb = FileUtilities.readContentFile(fileName);
                        RemessaTO remessa = descrobrirRemessaTO(sb, convenio);
                        if(remessa != null){
                            processarRetorno(remessa, sb);
                            lista.add(remessa);
                        }
                    }
                }catch (Exception e){
                    System.out.println("Falha no processamento do arquivo: " + fileName);
                    Uteis.logar(e, this.getClass());
                }
            }
        }
        return lista;
    }

    private RemessaTO descrobrirRemessaTO(StringBuilder sb, ConvenioCobrancaTO convenio) throws Exception{
        LayoutBase layout = LayoutFactory.getLayout(convenio);
        return layout.descobrirRemessa(sb);
    }

    public void processarRetorno(RemessaTO remessa, StringBuilder sb) throws Exception{
        LayoutBase layout = LayoutFactory.getLayout(remessa.getConvenio());
        layout.processarAtributosRetorno(remessa, sb);
        new RemessaTransaction().alterar(remessa, true);
    }

    /**
     * Recebe os retornos do {@link ConvenioCobrancaTO}
     * @param convenio
     * @throws Exception
     */
    public void receberRetornos(ConvenioCobrancaTO convenio, 
            boolean receberExtratos) throws Exception {
        Date dataInicio = Uteis.somarDias(Calendario.hoje(), -7);
        String pathDestino = convenio.getFtp().getDiretorioLocalRetorno();

        if (Calendario.hoje().getHours() < 5) {
            pathDestino += Uteis.getDataAplicandoFormatacao(Uteis.obterDataAnterior(Calendario.hoje(), 1), "dd-MM-yyyy");
        } else {
            pathDestino += Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy");
        }
        String PREFIXO_MSG = "";
        String PREFIXO_ARQUIVOS = null;
        if (receberExtratos){
            pathDestino += "_ED";
            PREFIXO_MSG = "EXTRATO - ";
            PREFIXO_ARQUIVOS = convenio.getNumeroContrato();
        }
        
        Uteis.logar(null, String.format(PREFIXO_MSG + "Iniciando Servico Receber Retornos DEFAULT para o CONVENIO: %s ... ", convenio.getTipoConvenio()));

        File pDestino = new File(pathDestino);
        if (!pDestino.exists()) {
            Uteis.logar(null, PREFIXO_MSG + "ReceberRetornos -> Criar diretório  (forçado): " + pathDestino);
            FileUtilities.forceDirectory(pathDestino);
        }
        /**
         * Fluxos Básicos:
         * 
         * 1. Caixas Postais compartilhadas como TIVIT (DCC Cielo) e BIN podemos 
         * ter um servidor central da Pacto que obtém os dados através do método 
         * 'receberRetornosCentralizados' vide caixas postais em 
         * SuperControle.properties.arrayCaixasPostaisSFTP;
         *      a. Neste caso neste servidor centralizado pode haver os arquivos 
         *         de retorno que serão utilizados pelas empresas que rodam nele;
         * 
         *      b. Outras empresas fora deste servidor, usarão a configuração 
         *         SuperControle.properties.serverSFTPSlave para obter os arquivos 
         *         de cada Tipo de Convênio que são compartilhados;
         * 
         * 2. Caixas postais que não se enquadrem no item 1, como GETNET por exemplo, 
         * deve seguir o fluxo de efetuar o download do arquivo da forma tradicional,
         * ou seja, cada empresa em cada convênio deverá fazer o download de seus arquivos.
         */
        CaixaPostalSFTP cpp = null;
        String nomeCaixaLog = PREFIXO_MSG + "CaixaPostalDefault";
        boolean verificarDiferencaArquivos = false;
        if (convenio.getTipoConvenio().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {//Caso 2
            cpp = new CaixaPostalSFTP(convenio.getFtp().getHost(),
                    new Integer(convenio.getFtp().getPorta()), convenio.getFtp().getUsuario(),
                    convenio.getFtp().getSenha(), convenio.getFtp().getDiretorioRemotoRetorno());
        } else if (RetornoService.obterCaixaPostalCentralPacto(pathDestino, convenio.getTipoConvenio()) != null) {//Caso 1.b
            cpp = RetornoService.obterCaixaPostalCentralPacto(pathDestino, convenio.getTipoConvenio());
            nomeCaixaLog = PREFIXO_MSG + "CaixaPostalCentralPacto";
            verificarDiferencaArquivos = true;
        } else if (UteisValidacao.emptyList(RetornoService.obterListaCaixasPostaisGlobais())){//Caso 1.a
            cpp = new CaixaPostalSFTP(convenio.getFtp().getHost(),
                    new Integer(convenio.getFtp().getPorta()), convenio.getFtp().getUsuario(),
                    convenio.getFtp().getSenha(), convenio.getFtp().getDiretorioRemotoRetorno());
        }
        //
        if (cpp != null) {
            SFTP sftp = new SFTP(cpp.getHost(), cpp.getUser(), cpp.getPwd(),
                    Integer.valueOf(cpp.getPorta()));
            try {
                Uteis.logar(null, nomeCaixaLog + " -> Convênio: " + convenio.getTipoConvenio()
                        + " -> Vou copiar arquivos via SFTP de: " + cpp);
                sftp.getFiles(cpp.getDiretorioRemoto(), pathDestino, 
                        PREFIXO_ARQUIVOS, dataInicio,
                        /*deleteArquivosAntigos?*/ convenio.getTipoConvenio().equals(TipoConvenioCobrancaEnum.DCC_GETNET),
                        /*verificarDiferencaArquivos*/ verificarDiferencaArquivos);
                Uteis.logar(null, nomeCaixaLog + " -> Convênio: " + convenio.getTipoConvenio()
                        + " - Arquivos copiados com sucesso! ");
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logar(null, nomeCaixaLog + " | receberRetornos -> Erro ao obter arquivos remotos:  "
                        + e.getMessage() + " - convênio: " + convenio.getTipoConvenio()
                        + " - DEBUG CaixaPostalSFTP: " + cpp);
            }
        } else if (UteisValidacao.emptyList(RetornoService.obterListaCaixasPostaisGlobais())){
            Uteis.logar(null, nomeCaixaLog + " -> ATENÇÃO!!! Não foi possível determinar "
                    + "qual SFTP seria utilizado para Download dos Arquivos de Retorno. "
                    + "Verifique sua configuração! Pode ser que a Aplicação não esteja "
                    + "configurada nem para Obter da Caixa Postal Central da Pacto "
                    + "e nem para obter os arquivos individualmente! "
                    + "Convênio: " + convenio.getTipoConvenio());
        }
            
        this.arquivos = FileUtilities.readListFilesDirectory(pathDestino);
    }
}

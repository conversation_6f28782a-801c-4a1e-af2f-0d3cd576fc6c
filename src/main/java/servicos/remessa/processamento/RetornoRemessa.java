package servicos.remessa.processamento;

import servicos.remessa.layouts.LayoutBase;
import servicos.remessa.layouts.LayoutFactory;
import servicos.remessa.to.ConvenioCobrancaTO;
import servicos.remessa.to.RemessaTO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Johnys on 11/02/2017.
 */
public class RetornoRemessa {

    public List<Long> processar(ConvenioCobrancaTO convenio) throws Exception{
        LayoutBase layout = LayoutFactory.getLayout(convenio);
        List<RemessaTO> list = layout.processarRetorno(convenio);
        List<Long> codigos = new ArrayList<Long>();
        for(RemessaTO rem : list){
            codigos.add(rem.getCodigo());
        }
        return  codigos;
    }
}

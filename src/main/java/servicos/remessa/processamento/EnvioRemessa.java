package servicos.remessa.processamento;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.encrypt.gpg.PgpEncryption;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.remessa.layouts.LayoutBase;
import servicos.remessa.layouts.LayoutFactory;
import servicos.remessa.to.ConvenioCobrancaTO;
import servicos.remessa.to.RemessaItemTO;
import servicos.remessa.to.RemessaTO;
import servicos.util.SFTP;

import java.io.File;
import java.io.IOException;
import static negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum.DCC_BIN;
import servicos.remessa.layouts.LayoutBIN;

/**
 * Created by Johnys on 11/02/2017.
 */
public class EnvioRemessa {

    private RemessaTO remessa;
    
    public EnvioRemessa(RemessaTO remessa){
        this.remessa = remessa;
    }
    
    /**
     * Realiza o envio da {@link RemessaTO}
     */
    public void enviarRemessa() throws Exception {
        StringBuilder arquivo = new StringBuilder();
        switch (remessa.getConvenio().getTipoConvenio()) {
            case DCC_BIN:
                LayoutBIN layoutBin = new LayoutBIN();
                layoutBin.setRemessa(remessa);
                remessa.setNomeArquivo(layoutBin.getNomeArquivo());
                arquivo = layoutBin.montarArquivo();
                break;
            default:
                break;
        }

        enviarArquivoRemotamente(arquivo);
        remessa.setSituacaoRemessa(SituacaoRemessaEnum.REMESSA_ENVIADA);
        for (RemessaItemTO itens : remessa.getItens()) {
            itens.setSituacao(SituacaoRemessaEnum.REMESSA_ENVIADA);
        }
    }

    /**
     * Realiza o envio da remessa remotamente via FTP
     * @param arquivo
     * @throws IOException
     */
    private void enviarArquivoRemotamente(StringBuilder arquivo) throws Exception{
        SFTP sftp = new SFTP(remessa.getConvenio().getFtp().getHost(),
                remessa.getConvenio().getFtp().getUsuario(),
                remessa.getConvenio().getFtp().getSenha(),
                Integer.valueOf(remessa.getConvenio().getFtp().getPorta()));
        String path = remessa.getConvenio().getFtp().getDiretorioLocalRemessa() + Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "dd-MM-yyyy") + File.separator;
        FileUtilities.forceDirectory(path);
        path += remessa.getNomeArquivo();
        if (TipoConvenioCobrancaEnum.DCC_BIN.equals(remessa.getConvenio().getTipoConvenio())) {
            path += ".enc";
            PgpEncryption.salvarCriptografado(arquivo.toString(), path, remessa.getConvenio().getRemessaCriptografia().getChave());
        } else {
            StringUtilities.saveToFile(arquivo, path);
        }
        sftp.putFile(path, remessa.getConvenio().getFtp().getDiretorioRemotoRemessa());
    }
}

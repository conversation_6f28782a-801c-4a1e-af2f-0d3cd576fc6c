package servicos.remessa.rotinas;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.impl.apf.APF;
import servicos.remessa.service.NewRemessaService;

/**
 * Created by Johnys on 11/02/2017.
 */
public class ProcessamentoRemessa {

    public static void main(String[] args) {
        try{
            //new NewRemessaService().enviarRemessas();
            new NewRemessaService().processarRetornos(Calendario.hoje());
        }catch (Exception e){
            Uteis.logar(e, ProcessamentoRemessa.class);
        }
    }
}

package servicos.remessa.service;

import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import servicos.remessa.dao.RemessaTransaction;
import servicos.remessa.processamento.EnvioRemessa;
import servicos.remessa.processamento.RetornoRemessa;
import servicos.remessa.to.RemessaTO;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Johnys on 11/02/2017.
 */
public class NewRemessaService {

    private RemessaTransaction remessaTransaction = new RemessaTransaction();

    /**
     * Realiza a inserção de uma nova remessa.
     * @param remessa
     * @throws Exception
     */
    public void inserir(RemessaTO remessa) throws Exception{
        if(remessaTransaction.existePorIdentificador(remessa.getIdentificador(), remessa.getChave())){
            throw new ConsistirException("erro.remessa_ja_cadastrada");
        }
        remessaTransaction.inserir(remessa, true);
    }

    /**
     * Raliza o envio das remessas.
     * @throws Exception
     */
    public void enviarRemessas() throws Exception{
        try{
            List<RemessaTO> remessasEnviar = remessaTransaction.consultarPorSituacao(SituacaoRemessaEnum.GERADA);
            for(RemessaTO remessa : remessasEnviar){
                System.out.println(new StringBuilder("Enviando remessa : ").append(remessa.getCodigo()).append(" chave: ").append(remessa.getChave()).append(" identificador : ").append(remessa.getIdentificador()));
                new EnvioRemessa(remessa).enviarRemessa();
                new RemessaTransaction().alterar(remessa, true);
            }
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
    }

    /**
     * Realiza o processamento dos retornos da remessa para uma dia especifico.
     * @param dia
     * @throws Exception
     */
    public void processarRetornos(Date dia) throws Exception{
        try{
            List<RemessaTO> remessasProcessar = remessaTransaction.consultarPorSituacao(SituacaoRemessaEnum.REMESSA_ENVIADA);
            RetornoRemessa retorno = new RetornoRemessa();
            if(!remessasProcessar.isEmpty()) {
                RemessaTO remessa = remessasProcessar.remove(0);
                do {
                    List<Long> codigosRemessasProcessadas = retorno.processar(remessa.getConvenio());
                    List<RemessaTO> remessasFaltantes = new ArrayList<RemessaTO>();
                    for(RemessaTO rem : remessasProcessar){
                        if(!codigosRemessasProcessadas.contains(rem.getCodigo())){
                            remessasFaltantes.add(rem);
                        }
                    }
                    if(!remessasFaltantes.isEmpty()){
                        remessasProcessar = remessasFaltantes;
                        remessa = remessasFaltantes.remove(0);
                    }else {
                        remessa = null;
                    }
                } while (remessa != null);
            }
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
    }

    public static void main(String[] args) {
        try{
            Uteis.debug = true;
            new NewRemessaService().enviarRemessas();
            new NewRemessaService().processarRetornos(Calendario.hoje());
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}

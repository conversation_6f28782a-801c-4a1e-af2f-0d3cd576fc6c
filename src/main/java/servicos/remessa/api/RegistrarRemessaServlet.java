package servicos.remessa.api;

import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.remessa.service.NewRemessaService;
import servicos.remessa.to.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by Johnys on 18/02/2017.
 */
public class RegistrarRemessaServlet extends SuperServletRemessa{

    @Override
    public String processar(String corpo) throws Exception {
        JSONObject remessaJSON = new JSONObject(corpo);
        RemessaTO remessa = converterRemessa(remessaJSON);
        try{
            new NewRemessaService().inserir(remessa);
        }catch (ConsistirException c){
            return getJSONErro(c.getMessage()).toString();
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
            throw e;
        }
        return getJSONSucesso().toString();
    }

    private RemessaTO converterRemessa(JSONObject remessaJSON) throws Exception{
        RemessaTO remessa = new RemessaTO();
        remessa.setChave(remessaJSON.getString("chave"));
        remessa.setDataGeracao(Calendario.getDate("dd/MM/yyyy", remessaJSON.getString("datageracao")));
        remessa.setIdentificador(remessaJSON.getLong("identificador"));
        remessa.setConvenio(converterConvenio(remessaJSON.getJSONObject("convenio")));
        remessa.setSituacaoRemessa(SituacaoRemessaEnum.GERADA);
        remessa.setItens(converterItens(remessaJSON.getJSONArray("itens"), remessa));
        if(UteisValidacao.emptyList(remessa.getItens())){
            throw new Exception("Remessa sem itens!");
        }
        return remessa;
    }

    private List<RemessaItemTO> converterItens(JSONArray itensJSON, RemessaTO remessa) {
        List<RemessaItemTO> itens = new ArrayList<RemessaItemTO>();
        for(int i = 0; i < itensJSON.length(); i++){
            itens.add(converterRemessaItem(itensJSON.getJSONObject(i), remessa));
        }
        return itens;
    }

    private RemessaItemTO converterRemessaItem(JSONObject remessaItemJSON, RemessaTO remessa) {
        RemessaItemTO item = new RemessaItemTO();
        item.setRemessa(remessa);
        item.setAutorizacao(converterAutorizacao(remessaItemJSON.getJSONObject("autorizacao")));
        item.setIdentificador(remessaItemJSON.getLong("identificador"));
        item.setValor(new BigDecimal(remessaItemJSON.getDouble("valor")));
        item.setSituacao(SituacaoRemessaEnum.GERADA);
        return item;
    }

    private AutorizacaoTO converterAutorizacao(JSONObject autorizacaoJSON) {
        AutorizacaoCartaoTO auto = new AutorizacaoCartaoTO();
        auto.setNumeroCartao(autorizacaoJSON.getString("numerocartao"));
        auto.setValidade(autorizacaoJSON.getString("validade"));
        return auto;
    }

    private ConvenioCobrancaTO converterConvenio(JSONObject convenioJSON) {
        ConvenioCobrancaTO convenio = new ConvenioCobrancaTO();
        convenio.setFtp(converterFTP(convenioJSON.getJSONObject("ftp")));
        convenio.setNumeroContrato(convenioJSON.getString("numerocontrato"));
        convenio.setNumeroLogico(convenioJSON.getString("numerologico"));
        convenio.setTipoConvenio(TipoConvenioCobrancaEnum.valueOf(convenioJSON.getString("tipo")));
        return convenio;
    }

    private FTPTO converterFTP(JSONObject fTPJSON) {
        FTPTO ftp = new FTPTO();
        ftp.setDiretorioLocalRemessa(fTPJSON.getString("diretorioLocalRemessa"));
        ftp.setDiretorioLocalRetorno(fTPJSON.getString("diretorioLocalRetorno"));
        ftp.setDiretorioRemotoRemessa(fTPJSON.getString("diretorioRemotoRemessa"));
        ftp.setDiretorioRemotoRetorno(fTPJSON.getString("diretorioRemotoRetorno"));
        ftp.setHost(fTPJSON.getString("host"));
        ftp.setPorta(fTPJSON.getString("porta"));
        ftp.setSenha(fTPJSON.getString("senha"));
        ftp.setUsuario(fTPJSON.getString("usuario"));
        return ftp;
    }
}

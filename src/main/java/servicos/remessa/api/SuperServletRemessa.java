package servicos.remessa.api;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * Created by Johnys on 18/02/2017.
 */
public abstract class SuperServletRemessa extends HttpServlet{

    @Override
    public void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        BufferedReader b = new BufferedReader(new InputStreamReader(req.getInputStream()));
        StringBuilder sb = new StringBuilder();
        String linha = null;
        while((linha = b.readLine()) != null){
            sb.append(linha);
        }
        try{
            String retorno = processar(sb.toString());
            if(!UteisValidacao.emptyString(retorno)){
                BufferedWriter w = new BufferedWriter((new OutputStreamWriter(resp.getOutputStream())));
                w.write(retorno);
                w.close();
            }
        }catch (Exception e){
            resp.sendError(500, e.getMessage());
        }
    }

    protected JSONObject getJSONSucesso(){
        JSONObject retorno = new JSONObject();
        retorno.put("erro", false);
        return retorno;
    }

    protected JSONObject getJSONErro(String erro){
        JSONObject retorno = new JSONObject();
        retorno.put("erro", true);
        retorno.put("msg", erro);
        return retorno;
    }

    public abstract String processar(String corpo) throws Exception;
}

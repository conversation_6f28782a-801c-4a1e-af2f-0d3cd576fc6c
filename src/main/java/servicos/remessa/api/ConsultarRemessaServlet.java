package servicos.remessa.api;

import org.json.JSONArray;
import org.json.JSONObject;
import servicos.remessa.dao.RemessaTransaction;
import servicos.remessa.to.RemessaItemTO;
import servicos.remessa.to.RemessaTO;

import java.util.List;

/**
 * Created by Johnys on 19/02/2017.
 */
public class ConsultarRemessaServlet extends SuperServletRemessa{

    @Override
    public String processar(String corpo) throws Exception {
        JSONObject remessa = new JSONObject(corpo);
        Long identificador = remessa.getLong("identificador");
        String chave = remessa.getString("chave");
        RemessaTO rem = new RemessaTransaction().consultarPorIdentificadorChave(identificador, chave);
        return montarRetorno(rem);
    }

    private String montarRetorno(RemessaTO rem) {
        JSONObject obj = new JSONObject();
        if(rem != null){
            obj.put("situacao", rem.getSituacaoRemessa().name());
            obj.put("itens", montarRetornoItens(rem.getItens()));
        }
        return obj.toString();
    }

    private JSONArray montarRetornoItens(List<RemessaItemTO> itens) {
        JSONArray jsonITem = new JSONArray();
        for(RemessaItemTO item : itens){
            JSONObject obj = new JSONObject();
            obj.put("identificador", item.getIdentificador());
            obj.put("valorpago", item.getValorPago());
            obj.put("situacao", item.getSituacao().name());
            JSONObject parametros = new JSONObject();
            for(String key : item.getParametros().keySet()){
                parametros.put(key, item.getParametros().get(key));
            }
            obj.put("parametros", parametros);
            obj.put("codigoRetorno", item.getCodigoErro());
            jsonITem.put(obj);
        }
        return jsonITem;
    }
}

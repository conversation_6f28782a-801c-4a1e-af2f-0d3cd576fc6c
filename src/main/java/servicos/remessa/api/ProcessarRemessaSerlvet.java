package servicos.remessa.api;

import negocio.comuns.utilitarias.Calendario;
import servicos.remessa.service.NewRemessaService;

/**
 * Created by johny<PERSON> on 07/03/2017.
 */
public class ProcessarRemessaSerlvet extends SuperServletRemessa{

    @Override
    public String processar(String corpo) throws Exception {
        new NewRemessaService().enviarRemessas();
        new NewRemessaService().processarRetornos(Calendario.hoje());
        return null;
    }
}

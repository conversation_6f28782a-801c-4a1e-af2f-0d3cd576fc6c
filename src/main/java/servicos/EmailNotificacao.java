package servicos;

import br.com.pactosolucoes.comuns.to.GrupoTransacoesTO;
import java.util.Map;
import java.util.Set;

import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.arquitetura.FacadeFactory;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Transacao;

/**
 * Encapsula a operação de envio de notificação de inconsistências em transação
 * de recorrência em cartão de crédito via email para os responsáveis
 *
 * <AUTHOR> / Waller Maciel
 *
 */
public class EmailNotificacao {

    private final static String ASSUNTO = "Notificação - Transações de Recorrência no Cartão de Crédito";
    private final static String ASSUNTO_REPESCAGEM = "Notificação - Repescagem de Recorrência no Cartão de Crédito";
    private final static String ASSUNTO_RETORNO_REMESSA = "Notificação - Resultado de Remessa";
    private ConfiguracaoSistemaVO configuracaoSistema;
    private FacadeFactory facade;

    /**
     * Responsável por inicializar as Configuracoes do Sistema em VO
     *
     * <AUTHOR> / Waller Maciel 07/07/2011
     */
    public void inicializarConfiguracaoSistema() throws Exception {
        setConfiguracaoSistema(getFacade().getConfiguracaoSistema().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_ROBO));
    }

    /**
     * Responsável por enviar aos responsáveis email com detalhamento de
     * inconsistências em transação de recorrência em cartão de crédito recebe
     * como parametro um mapa com os dados de contratos recorrentes com
     * mensalidades vencidas cujo processo de pagamento não obteve sucesso e
     * outro que não tiveram sua renovação automatica concluida. Ambos os mapas
     * relacionam a transacao com o contrato de recorrencia
     *
     * <AUTHOR> / Waller Maciel 13/07/2011
     */
    public void enviarEmailInconsistencias(Map<TransacaoVO, ContratoRecorrenciaVO> mensalidadesVencidas,
            Map<TransacaoVO, ContratoRecorrenciaVO> renovacoesNaoConcluidas, boolean verificarRepescagem) throws Exception {

        UteisEmail email = new UteisEmail();
        ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPRobo();
        email.novo("", config);
        email.setRemetente(getUsuarioRecorrencia());

        //enviar email aos responsaveis        
        String[] destinos = getConfiguracaoSistema().getListaEmailsRecorrencia().toArray(
                new String[getConfiguracaoSistema().getListaEmailsRecorrencia().size()]);

        //Transações não aprovadas ou Renovações Automática Não Efetuadas, quando houver.
        if ((mensalidadesVencidas != null && mensalidadesVencidas.size() > 0)
                || (renovacoesNaoConcluidas != null)) {

            String textoEmail = montarTextoEmail(mensalidadesVencidas, renovacoesNaoConcluidas);
            if (!textoEmail.isEmpty()) {
                email.enviarEmailN(destinos, textoEmail, ASSUNTO, "");
            }

        }
        //Transações da Repescagem, quando necessário
        if (verificarRepescagem) {
            //Preparar um outro e-mail para repescagem de transações, quando houver.
            String textoRepescagem = montarTextoEmailRepescagem();
            if (!textoRepescagem.isEmpty()) {
                email.enviarEmailN(destinos, textoRepescagem, ASSUNTO_REPESCAGEM, "");
            }
        }
    }

    public void enviarEmailRetorno(RemessaVO remessaVO) throws Exception {
        UteisEmail email = new UteisEmail();
        ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
        email.novo("", config);
        email.setRemetente(getUsuarioRecorrencia());
        //enviar email aos responsaveis
        String[] destinos = getConfiguracaoSistema().getListaEmailsRecorrencia().toArray(
                new String[getConfiguracaoSistema().getListaEmailsRecorrencia().size()]);

        //Transações não aprovadas ou Renovações Automática Não Efetuadas, quando houver.

        EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(remessaVO.getEmpresa(), Uteis.NIVELMONTARDADOS_MINIMOS);

        String textoEmail = montarTextoRetornoRemessa(remessaVO, empresaVO);
        if (!textoEmail.isEmpty()) {
            email.enviarEmailN(destinos, textoEmail, ASSUNTO_RETORNO_REMESSA, "");
        }
    }

    /**
     * Responsável por montar o conteúdo do email que será enviado
     *
     * <AUTHOR> / Waller Maciel 13/07/2011
     */
    private String montarTextoEmail(Map<TransacaoVO, ContratoRecorrenciaVO> mensalidadesVencidas,
            Map<TransacaoVO, ContratoRecorrenciaVO> renovacoesNaoConcluidas) throws Exception {
        StringBuffer texto = new StringBuffer();
        //cabeçalho
        texto.append("<html><body style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;color:#000;\">");
        texto.append("Olá, você está recebendo este e-mail de aviso automático, para informar algumas inconsistências ocorridas ");
        texto.append("durante o processamento de transações de RECORRÊNCIA NO CARTÃO DE CRÉDITO de sua Academia:<br/><br/>");
        boolean naoGerarNada = true;
        if (mensalidadesVencidas != null && mensalidadesVencidas.size() > 0) {
            texto.append("<b>Parcelas Vencidas e Não Aprovadas :</b> <br/><br/>");
            //montagem do detalhamento dos contratos recorrentes com mensalidades vencidas
            Set<TransacaoVO> keys = mensalidadesVencidas.keySet();
            for (TransacaoVO transacao : keys) {
                if (transacao.getRepescagem().isEmpty()) {
                    ContratoRecorrenciaVO contratoRecorrencia = mensalidadesVencidas.get(transacao);
                    texto.append("<b>").append(contratoRecorrencia.getContrato().getPessoa().getNome()).append("</b> - ");
                    texto.append(Formatador.formatarValorMonetario(contratoRecorrencia.getValorMensal())).append(" - Data Transação : ");
                    texto.append(Uteis.getDataComHora(transacao.getDataProcessamento())).append("<br/>");
                    for (MovParcelaVO parcela : transacao.getListaParcelas()) {
                        String empresa = "";
                        if (parcela.getEmpresa().getCodigo().intValue() != 0) {
                            empresa = getFacade().getEmpresa().consultarPorCodigo(
                                    parcela.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).getNome();
                        }
                        texto.append("<b>Empresa:</b> ").append(empresa).append("<br/>");
                        texto.append("<b>").append(parcela.getCodigo()).append(" - ").append(parcela.getDescricao()).append(" - Vencimento: ").append(Uteis.getData(parcela.getDataVencimento())).append(" - Tentativas: ").append(parcela.getNrTentativas()).append("</b><br/>");
                    }
                    texto.append("<b>Id Transação:</b> ").append(transacao.getCodigoExterno()).append("<br/>");
                    texto.append("<b>=========================================================================</b><br/>");
                    naoGerarNada = false;
                }
            }
        }

        //montagem do detalhamento dos contratos recorrentes com renovacoes não concluidas
        if (renovacoesNaoConcluidas != null) {
            texto.append("<br/><b>Renovações Automáticas Não Concluídas :</b> <br/><br/>");
            Set<TransacaoVO> keys = renovacoesNaoConcluidas.keySet();
            for (TransacaoVO transacao : keys) {
                ContratoRecorrenciaVO contratoRecorrencia = renovacoesNaoConcluidas.get(transacao);
                texto.append(contratoRecorrencia.getContrato().getPessoa().getNome()).
                        append(" - Contrato : ").
                        append(contratoRecorrencia.getContrato().getCodigo()).
                        append(" - Id Transação : ").
                        append(transacao.getCodigoExterno()).append("<br/>");
                texto.append("<b>Empresa:</b> ").append(contratoRecorrencia.getContrato().getEmpresa().getNome()).append("<br/>");
                naoGerarNada = false;
            }
        }
        texto.append("<br/>Para efetuar as devidas averiguações, proceda a \"Gestão de Transações Recorrentes\" no software Pacto ZillyonWeb.");
        texto.append("</body>");
        texto.append("</html>");
        return naoGerarNada ? "" : texto.toString();
    }

    private String montarTextoEmailRepescagem() throws Exception {
        StringBuilder texto = new StringBuilder();
        boolean naoGerarNada = true;
        //cabeçalho
        texto.append("<html><body style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;color:#000;\">");
        texto.append("Olá, você está recebendo este e-mail de aviso automático, "
                + "para informar sobre a <b>REPESCAGEM de PARCELAS da RECORRÊNCIA em CARTÃO DE CRÉDITO</b> de sua Academia:<br/><br/>");


        //Acumular transações de repescagem para enviar resumo no email também.
        List<TransacaoVO> listaTransacoesRepescagem = new ArrayList<TransacaoVO>();
        Date dataBase = Calendario.hoje();
        try {
            listaTransacoesRepescagem = getFacade().getTransacao().consultar(dataBase,
                    dataBase, null, /*empresa-todas->*/ 0, null, null, false);
        } catch (Exception ex) {
            texto.append("<b>***Não foi possível anexar ao Email as informações ").
                    append("acerca das transações da Repescagem devido a um erro interno: ").
                    append(ex.getMessage());
        }

        if (!listaTransacoesRepescagem.isEmpty()) {
            listaTransacoesRepescagem = Ordenacao.ordenarLista(listaTransacoesRepescagem, "nomePessoa");
            List<GrupoTransacoesTO> gruposRepescagem = Transacao.consultarGrupoRepescagem(listaTransacoesRepescagem);
            texto.append("<br/><b>").append("==================================R E P E S C A G E M==================================").append("</b><br/><br/>");

            for (TransacaoVO transacao : listaTransacoesRepescagem) {
                if (!transacao.getRepescagem().isEmpty()) {
                    List<MovParcelaVO> listaParc = getFacade().getTransacao().
                            obterParcelasDaTransacao(transacao);

                    transacao.setListaParcelas(listaParc);
                    texto.append("<b>").append(transacao.getNomePessoa()).append("</b> - ").append(Formatador.formatarValorMonetario(transacao.getValor())).append(" - Data Transação : ").append(Uteis.getDataComHora(transacao.getDataProcessamento())).append("<br/>");
                    texto.append("<b>Id Transação:</b> ").append(transacao.getCodigoExterno()).append("<br/>");
                    for (MovParcelaVO parcela : transacao.getListaParcelas()) {
                        String empresa = "";
                        if (parcela.getEmpresa().getCodigo() != 0) {
                            empresa = getFacade().getEmpresa().consultarPorCodigo(
                                    parcela.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA).getNome();
                        }
                        texto.append("<b>Empresa:</b> ").append(empresa).append("<br/>");
                        texto.append("<b>").append(parcela.getCodigo()).append(" - ").append(parcela.getDescricao()).append(" - Vencimento: ").append(Uteis.getData(parcela.getDataVencimento())).append(" - Tentativas: ").append(parcela.getNrTentativas()).append("</b><br/>");
                    }
                    texto.append("<table style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;\">");
                    texto.append("<tr>");
                    texto.append("<td><div style=\"cursor:pointer;").append("vertical-align:middle;width: 8px; height: 8px;background-color:").append(transacao.getSituacao().getImagem()).append(";\"/></td>");
                    texto.append("<td><b><font color=\"").append(transacao.getSituacao().getImagem()).append("\">").append(transacao.getSituacao().getDescricao()).append("</font></td></br>");
                    texto.append("</tr>");
                    texto.append("</table><br/>");


                    texto.append("<b>=====================================================================================</b><br/>");
                    naoGerarNada = false;
                }

            }
            texto.append("<br/>");
            texto.append("<br/><b>").append("=======================================TOTAIS=======================================").append("</b>");
            texto.append("<table style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;\">");
            texto.append("<tbody>");
            int qtd = 0;
            double soma = 0.0;
            for (GrupoTransacoesTO grupoTransacoesTO : gruposRepescagem) {
                texto.append("<tr>");
                texto.append("<td><div style=\"cursor:pointer;").append("vertical-align:middle;width: 8px; height: 8px;background-color:").append(grupoTransacoesTO.getSituacao().getImagem()).append(";\"/></td>");
                texto.append("<td>").append("<b><font color=\"").append(grupoTransacoesTO.getSituacao().getImagem()).append("\">").append(grupoTransacoesTO.getSituacao().getDescricao()).append("</font>");
                texto.append("<td>").append(grupoTransacoesTO.getQuantidade()).append("</td>");
                texto.append("<td>").append(grupoTransacoesTO.getValor_Apresentar()).append("</td>");
                texto.append("</tr>");
                qtd += grupoTransacoesTO.getQuantidade();
                soma += grupoTransacoesTO.getValor();
            }
            texto.append("</tbody>");
            texto.append("<tfooter>");
            texto.append("<tr>");
            texto.append("<td><b></b></td><td></td>");
            texto.append("<td>").append(qtd).append("</td>");
            texto.append("<td>").append(Formatador.formatarValorMonetario(soma)).append("</td>");
            texto.append("</tr>");
            texto.append("</table>");
            texto.append("<b>====================================================================================</b><br/>");
        }

        texto.append("<br/>Para maiores informações, proceda a \"Gestão de Transações Recorrentes\" no software Pacto ZillyonWeb.");
        texto.append("</body>");
        texto.append("</html>");
        return naoGerarNada ? "" : texto.toString();
    }

    private String montarTextoRetornoRemessa(RemessaVO remessaVO, EmpresaVO empresaVO) throws Exception {
        StringBuilder texto = new StringBuilder();
        boolean naoGerarNada = true;
        //cabeçalho
        texto.append("<html><body style=\"font-size:11px;font-family:Arial,Verdana,sans-serif;color:#000;\">");
        texto.append("Olá, você está recebendo este e-mail de aviso automático, ");
        if (UteisValidacao.emptyNumber(remessaVO.getCodigo())) {
            texto.append("para informar sobre os <b>RETORNOS das REMESSAS </b> de sua Academia:<br/>");
        } else {
            texto.append("para informar sobre o <b>RETORNO da REMESSA ").append(remessaVO.getIdentificador()).append(" </b> de sua Academia:<br/>");
        }
        texto.append("<b>").append(empresaVO.getNome()).append("</b><br/><br/>");

        if (!remessaVO.getListaItens().isEmpty()) {
            if (!UteisValidacao.emptyNumber(remessaVO.getCodigo())) {
                texto.append("<b>Código da Remessa:</b> ").append(remessaVO.getCodigo()).append("<br/>");
                texto.append("<b>Data de Envio:</b> ").append(Formatador.formatarDataPadrao(remessaVO.getDataRegistro())).append("<br/>");
                texto.append("<b>Data de Retorno/Caixa:</b> ").append(Formatador.formatarDataPadrao(remessaVO.getDataRetorno())).append("<br/>");
                if (remessaVO.getDataPrevistaCredito() != null) {
                    texto.append("<b>Data do Prevista para depósito:</b> ").append(Formatador.formatarDataPadrao(remessaVO.getDataPrevistaCredito())).append("<br/>");
                }
                Integer qtdEnviada = 0;
                Integer qtdAceita = 0;
                Double valorEnviado = 0.0;
                Double valorAceito = 0.0;

                for (RemessaItemVO item : remessaVO.getListaItens()) {
                    if (item.getCodigoStatus().equals("00")) {
                        qtdAceita++;
                        valorAceito += item.getMovParcela().getValorParcela();
                    }
                    qtdEnviada++;
                    valorEnviado += item.getMovParcela().getValorParcela();
                }

                texto.append("<b>Número de parcelas enviadas: </b>").append(qtdEnviada).append("<br/>");
                texto.append("<b>Valor total enviado: </b>").append(Formatador.formatarValorMonetario(valorEnviado)).append("<br/>");

                texto.append("<b>Número de parcelas Pagas: </b>").append(qtdAceita).append("<br/>");
                texto.append("<b>Valor total Pago: </b>").append(Formatador.formatarValorMonetario(valorAceito)).append("<br/>");

                texto.append("<b>Número de parcelas não Pagas: </b>").append((qtdEnviada - qtdAceita)).append("<br/>");
                texto.append("<b>Valor total Não Pago: </b>").append(Formatador.formatarValorMonetario((valorEnviado - valorAceito))).append("<br/>");
            }

            texto.append("<br/><b>").append("================================= ITENS DA REMESSA NÃO PAGAS =================================").append("</b><br/><br/>");
            texto.append("<b>Para resolver cada um destes casos, acesse o 'Gestão de Remessas'.</b><br/>");
            texto.append("<b>O sistema indicará oq ue deve ser feito para cada uma das parcelas não pagas.</b><br/><br/>");

            for (RemessaItemVO item : remessaVO.getListaItens()) {
                if (!item.getCodigoStatus().equals("00")) {
                    texto.append("<b>").append(item.getPessoa().getNome()).append("</b><br/>");
                    texto.append("<b>Descrição:</b> ").append(item.getMovParcela().getDescricao()).append(" - ").append(item.getMovParcela().getValorParcela_Apresentar()).append("<br/>");
                    texto.append("<b>Vencimento:</b> ").append(item.getMovParcela().getDataVencimento_Apresentar()).append("<br/>");
                    texto.append("<b>Status:</b> ").append(item.getCodigoStatus()).append(" (").append(item.getDescricaoStatus()).append(")<br/>");
                    texto.append("<b>Número de tentativas:</b> ").append(item.getNrTentativaParcela()).append(" <br/>");
                    texto.append("<br/>");

                    naoGerarNada = false;
                }
            }

            texto.append("<br/><b>").append("=================================   ITENS DA REMESSA PAGAS   =================================").append("</b><br/><br/>");
            for (RemessaItemVO item : remessaVO.getListaItens()) {
                if (item.getCodigoStatus().equals("00")) {
                    texto.append("<b>").append(item.getPessoa().getNome()).append("</b><br/>");
                    texto.append("<b>Descrição:</b> ").append(item.getMovParcela().getDescricao()).append(" - ").append(item.getMovParcela().getValorParcela_Apresentar()).append("<br/>");
                    texto.append("<b>Vencimento:</b> ").append(item.getMovParcela().getDataVencimento_Apresentar()).append("<br/>");
                    texto.append("<b>Status:</b> ").append(item.getCodigoStatus()).append(" (").append(item.getDescricaoStatus()).append(")<br/>");
                    texto.append("<b>Autorização:</b> ").append(item.getAutorizacao()).append(" <br/>");
                    texto.append("<br/>");

                    naoGerarNada = false;
                }
            }
        }
        texto.append("<b>=====================================================================================</b><br/>");
        texto.append("<br/>");
        texto.append("</body>");
        texto.append("</html>");
        return naoGerarNada ? "" : texto.toString();
    }

    //-------------------- GETTERS AND SETTERS ------------------------------------------//
    /**
     * @param configuracaoSistema the configuracaoSistema to set
     */
    public void setConfiguracaoSistema(ConfiguracaoSistemaVO configuracaoSistema) {
        this.configuracaoSistema = configuracaoSistema;
    }

    /**
     * @return the configuracaoSistema
     * @throws Exception
     */
    public ConfiguracaoSistemaVO getConfiguracaoSistema() throws Exception {
        if (configuracaoSistema == null) {
            inicializarConfiguracaoSistema();
        }
        return configuracaoSistema;
    }

    public UsuarioVO getUsuarioRecorrencia() throws Exception {
        return getFacade().getZWFacade().getUsuarioRecorrencia();
    }

    /**
     * @return the facade
     */
    public FacadeFactory getFacade() {
        if (facade == null) {
            facade = new FacadeFactory();
        }
        return facade;
    }
}

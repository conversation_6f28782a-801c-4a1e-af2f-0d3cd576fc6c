package servicos.erro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisHash;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;

public class ErroService {

    public static void registrarErroGoogleStackDriver(Exception e, HttpServletRequest servletRequest){
        String url = servletRequest.getRequestURI();
        UsuarioVO usuarioLogado = JSFUtilities.getUsuarioLogado(servletRequest);
        String chave = JSFUtilities.getChave(servletRequest);
        String referer = getReferer(servletRequest);
        String versaoZw = JSFUtilities.getVersaoSistema(servletRequest);
        String versaoDb = JSFUtilities.getVersaoDb(servletRequest);
        String ip = getIp(servletRequest);
        String httpMethod = servletRequest.getMethod();
        String browser = servletRequest.getHeader("user-agent");
        EmpresaVO empresaLogada = JSFUtilities.getEmpresaLogada(servletRequest);

        ErroService.registrarErroGoogleStackDriver(e, url, usuarioLogado, empresaLogada, chave, versaoZw, versaoDb, ip, httpMethod, 500, browser, referer);
    }

    public static void registrarErroGoogleStackDriver(Exception e,
                                                      String urlPagina,
                                                      UsuarioVO usuarioLogado,
                                                      EmpresaVO empresaLogada,
                                                      String chave,
                                                      String versaoZw,
                                                      String versaoDb,
                                                      String ip,
                                                      String httpMethod,
                                                      int responseStatusCode,
                                                      String browser,
                                                      String referer) {

        String stackTrace = stackTraceToString(e);
        String codigoEmpresaLogada = empresaLogada == null ? "" : empresaLogada.getCodigo().toString();
        String nomeUsuario = usuarioLogado == null  ? "" : usuarioLogado.getNome();
        String codigoUsuarioLogado = usuarioLogado == null ? "" : usuarioLogado.getCodigo().toString();

        String user  = chave+":"+codigoEmpresaLogada+":"+codigoUsuarioLogado+":"+nomeUsuario;
        String versao = versaoZw+":"+versaoDb;

        JSONObject payload = new JSONObject();

        JSONObject serviceContext = new JSONObject();
        serviceContext.put("service", "zw");
        serviceContext.put("version", versao);

        JSONObject context = new JSONObject();
        JSONObject httpRequest = new JSONObject();
        httpRequest.put("method", httpMethod);
        httpRequest.put("url", urlPagina);
        httpRequest.put("userAgent", browser);
        httpRequest.put("responseStatusCode", responseStatusCode);
        httpRequest.put("remoteIp", ip);
        httpRequest.put("referrer", referer);
        context.put("httpRequest", httpRequest);
        context.put("user", user);

        payload.put("serviceContext", serviceContext);
        payload.put("message", stackTrace);
        payload.put("context", context);

        String key = PropsService.getPropertyValue(PropsService.GCLOUD_API_KEY);
        if(key != null && key.length() > 0){
            String apiUrl = "https://clouderrorreporting.googleapis.com/v1beta1/projects/zw-producao/events:report?key="+key;

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");

            try{
                ExecuteRequestHttpService.executeHttpRequest(apiUrl, payload.toString(), headers, "POST", "UTF-8");
            }catch (IOException ex){
                Uteis.logar("Não foi possível registrar exceção inesperada na API do google cloud stackdriver. Exeception: "+ ex.getMessage());
            }
        }
    }

    public static void registrarExceptionOAMD(Exception e, String urlPagina, UsuarioVO usuarioLogado, String chave){

        String url = PropsService.getPropertyValue(PropsService.urlOamd)+"/prest/erros/"+chave+"/zw";
        Long horarioRegistro = System.currentTimeMillis();
        Integer codigoUsuarioLogado = usuarioLogado == null ? 0 : usuarioLogado.getCodigo();
        String nomeUsuarioLogado = usuarioLogado == null ? "nao-foi-possivel-identificar" : usuarioLogado.getNome();

        String stackTrace = stackTraceToString(e);
        String stackTraceHash = UteisHash.md5(stackTrace);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");

        JSONObject body = new JSONObject();
        body.put("mensagem", e.getMessage());
        body.put("stacktrace", stackTrace);
        body.put("stacktracehash", stackTraceHash);
        body.put("dataregistro", horarioRegistro);
        body.put("codigousuariologado", codigoUsuarioLogado);
        body.put("nomeusuariologado", codigoUsuarioLogado);
        body.put("nomeusuario", nomeUsuarioLogado);
        body.put("exception", e.getClass().toString());
        body.put("chave", chave);
        body.put("url", urlPagina);

        try{
            String result = ExecuteRequestHttpService.executeHttpRequest(url, body.toString(), headers, "POST", "UTF-8");
            if(result.contains("erro")){
                Uteis.logar("Erro ao registrar exceção na aplicação OAMD: "+result);
            }else{
                Uteis.logar("Exceção registrada no OAMD: "+result.substring(0, 140));
            }
        }catch (IOException ex){
            Uteis.logar("Não foi possível registrar exceção inesperada na API da aplicação OAMD. Exeception: "+ e.getMessage());
        }
    }

    private static String stackTraceToString(Exception e){
        final StringWriter writer = new StringWriter();
        PrintWriter pw = new PrintWriter(writer);
        fillStackTrace(e, pw);

        return writer.toString();
    }

    private static void fillStackTrace(Throwable ex, PrintWriter pw) {
        if (null == ex) {return;}
        ex.printStackTrace(pw);
        if (ex instanceof ServletException) {
            Throwable cause = ((ServletException) ex).getRootCause();

            if (null != cause) {
                fillStackTrace(cause, pw);
            }
        } else {
            Throwable cause = ex.getCause();
            if (null != cause) {
                fillStackTrace(cause, pw);
            }
        }
    }

    private static String getIp(HttpServletRequest request){
        String ip = request.getHeader("X-FORWARDED-FOR");

        if( ip == null){
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if( ip == null){
            ip = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ip == null) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    private static String getReferer(HttpServletRequest request){
        String referer = request.getHeader("referer");
        if(referer == null || referer.length() == 0){
            String port = request.getServerPort() == 0 ? "" : Integer.toString(request.getServerPort());

            referer = request.getScheme() + "://"+ request.getServerName();
            referer += port.length() > 0 ? ":"+ port : "" ;
            referer += request.getRequestURI();
        }

        return referer;
    }
}

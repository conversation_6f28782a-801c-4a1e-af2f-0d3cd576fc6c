package servicos.crm.impl;

import negocio.facade.jdbc.basico.Empresa;
import servicos.crm.intf.MqvServiceInterface;

import java.sql.Connection;

public class MqvServiceImpl implements MqvServiceInterface {

    private Connection con;

    public MqvServiceImpl(Connection con) {
        this.con = con;
    }
    @Override
    public String persistTokenMqv(String ctx, String empresa, String token) throws Exception {
        try {
            Empresa emp = new Empresa(this.con);
            emp.atualizarTokenMqvEmpresa(empresa, token);
        }
        catch (Exception e){
            throw e;
        }
        return  "Ok";
    }
}

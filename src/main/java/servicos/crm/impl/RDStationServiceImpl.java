package servicos.crm.impl;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import servicos.crm.intf.RDStationServiceInterface;
import servicos.integracao.impl.rd.IntegracaoRDServiceImpl;

import java.sql.Connection;

import static controle.arquitetura.SuperControle.notificarRecursoEmpresa;

public class RDStationServiceImpl implements RDStationServiceInterface {

    private Connection con;

    public RDStationServiceImpl(Connection con) {
        this.con = con;
    }

    public String persistirLeadRDOLD(String lead, int empresa, String key) throws Exception {
        new IntegracaoRDServiceImpl(this.con).processarNovaLeadOLD(lead, empresa);
        notificarRecursoEmpresa(this.con, key, RecursoSistema.ENVIOU_LEAD_RDSTATION_PARA_CRM_OLD, String.valueOf(empresa));
        return "sucesso";
    }
}

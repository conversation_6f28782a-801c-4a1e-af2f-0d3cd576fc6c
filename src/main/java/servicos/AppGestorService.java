package servicos;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.commons.lang3.StringUtils;
import servlet.appGestor.Interface.MSFinanceiroService;
import servlet.appGestor.appGestorDados.ContaJSON;
import servlet.appGestor.appGestorDados.DadosEmpresa;
import servlet.appGestor.appGestorDados.LancamentoJSON;
import servlet.appGestor.appGestorDados.LancamentoTotal;
import servlet.appGestor.appGestorDados.TipoContaJSON;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


public class AppGestorService implements MSFinanceiroService, AutoCloseable {

    private Connection con;

    public AppGestorService(Connection con) {
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public List<TipoContaJSON> consultarContas(Date inicio, Date fim) throws Exception {
        String sql = "SELECT cast (coalesce((SELECT sum(cast(movcontarateio.valor as double precision)) from movcontarateio  \n" +
                " INNER JOIN movconta mc ON movconta = mc.codigo AND tipoes = 1 " +
                (inicio == null ? "" : " AND dataquitacao  >= '" + Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00' ") +
                " AND dataquitacao <= '" + Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59 " + "' WHERE mc.conta = conta.codigo),0.0) - \n" +
                " coalesce((SELECT sum(cast(movcontarateio.valor as double precision)) from movcontarateio INNER JOIN movconta mc \n" +
                " ON movconta = mc.codigo AND tipoes = 2 " +
                (inicio == null ? "" : "AND dataquitacao  >= '" + Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00' ") +
                " AND dataquitacao <= '" + Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59 " + "' WHERE mc.conta = conta.codigo),0.0) " +
                " as numeric(12,2)) as valor, conta.codigo, conta.descricao,numero,numerodv,agencia,agenciadv,tc.descricao as tipoconta \n" +
                " FROM conta left join tipoconta tc ON conta.tipoconta = tc.codigo WHERE mostrarnobi and ativa " +
                " ORDER BY tc.descricao, conta.descricao ";

        List<TipoContaJSON> result = new ArrayList<>();
        List<ContaJSON> lista = new ArrayList<>();
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, this.con)) {
            while (rs.next()) {
                ContaJSON contaJSON = new ContaJSON();
                contaJSON.setTipoConta(rs.getString("tipoconta"));
                if (StringUtils.isNotBlank(rs.getString("agencia"))) {
                    contaJSON.setAgencia(rs.getString("agencia") + "-" + rs.getString("agenciadv"));
                }
                contaJSON.setCodigo(rs.getInt("codigo"));
                contaJSON.setDescricao(rs.getString("descricao"));
                if (StringUtils.isNotBlank(rs.getString("numero"))) {
                    contaJSON.setNumero(rs.getString("numero") + "-" + rs.getString("numerodv"));
                }
                contaJSON.setValor(rs.getDouble("valor"));

                lista.add(contaJSON);
            }

            for (ContaJSON contaJSON : lista) {
                TipoContaJSON contaAdicionar = null;
                for (TipoContaJSON tipoContaJSON : result) {
                    if (tipoContaJSON.getTipoConta().equals(contaJSON.getTipoConta())) {
                        contaAdicionar = tipoContaJSON;
                        break;
                    }
                }
                if (contaAdicionar == null) {
                    TipoContaJSON tipo = new TipoContaJSON();
                    tipo.setTipoConta(contaJSON.getTipoConta());
                    result.add(tipo);
                    contaAdicionar = tipo;
                }
                contaAdicionar.getContas().add(contaJSON);
            }
        }
        return result;
    }


    public List<LancamentoJSON> obterDetalhamentoVencidos(Integer empresa, Date inicio, Date fim, Boolean incluirCheques) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct (mc.codigo) as codigo, mc.descricao,mc.tipooperacao as tipoes,mc.valor,dataquitacao,datavencimento,(Select nome from planoconta where planoconta.codigo =  (Select planoconta from movcontarateio where mc.codigo = movcontarateio.movconta limit 1)) as planoconta\n");
        sql.append("        FROM movconta mc\n");
        sql.append("LEFT JOIN conta ON mc.conta = conta.codigo\n");
        sql.append("INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo\n");
        sql.append("INNER JOIN empresa ON empresa.codigo = mc.empresa\n");
        sql.append("LEFT JOIN FormaPagamento fp ON fp.codigo = mcr.formapagamento\n");
        sql.append("WHERE mc.tipooperacao IN (2,1)\n");
        sql.append("AND mc.empresa = ").append(empresa);
        if (incluirCheques != null && incluirCheques) {
            sql.append(" AND (fp.tipoformapagamento not in ('CH')) ");
        } else if (incluirCheques != null) {
            sql.append(" AND (fp.codigo is null OR fp.tipoformapagamento not in ('CH')) ");
        }
        if (inicio != null) {
            sql.append(" AND  mc.datavencimento >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00");
        }
        if (fim != null) {
            sql.append("' AND  mc.datavencimento <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59");
        }
        sql.append("' AND dataquitacao IS NULL  order by datavencimento\n");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {

            List<LancamentoJSON> lista = new ArrayList<>();
            while (rs.next()) {
                Boolean tipoperacao = true;
                LancamentoJSON conta = new LancamentoJSON();
                conta.setCodigo(rs.getInt("codigo"));
                conta.setDescricao(rs.getString("descricao"));
                conta.setValor(rs.getDouble("valor"));
                conta.setQuitacao(Uteis.getDataString(rs.getString("dataquitacao")));
                conta.setVencimento(Uteis.getDataString(rs.getString("datavencimento")));
                conta.setTipo(rs.getInt("tipoes") == (tipoperacao ? 2 : 1) ? "E" : "S");
                conta.setPlanoConta(rs.getString("planoconta"));
                lista.add(conta);

            }

            return lista;
        }
    }

    public List<LancamentoJSON> obterLancamentos(Integer empresa, Date inicio, Date fim, Boolean incluirCheques) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct (mc.codigo) as codigo,mc.dataquitacao,mc.descricao,mc.tipooperacao as tipoes,mc.valor,dataquitacao,datavencimento,(Select nome from planoconta where planoconta.codigo =  (Select planoconta from movcontarateio where mc.codigo = movcontarateio.movconta limit 1)) as planoconta\n");
        sql.append("        FROM movconta mc\n");
        sql.append("LEFT JOIN conta ON mc.conta = conta.codigo\n");
        sql.append("INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo\n");
        sql.append("INNER JOIN empresa ON empresa.codigo = mc.empresa\n");
        sql.append("WHERE mc.tipooperacao IN (2,1)\n");
        sql.append("AND mc.empresa = ").append(empresa);
        sql.append(" and  ");
        if (inicio != null) {
            sql.append("  mc.dataquitacao >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00");
        }
        if (fim != null) {
            sql.append("' AND  mc.dataquitacao <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59");
        }
        sql.append("' order by  mc.dataquitacao\n");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {

            List<LancamentoJSON> lista = new ArrayList<>();
            while (rs.next()) {
                Boolean tipoperacao = true;
                LancamentoJSON conta = new LancamentoJSON();
                conta.setCodigo(rs.getInt("codigo"));
                conta.setDescricao(rs.getString("descricao"));
                conta.setValor(rs.getDouble("valor"));
                conta.setQuitacao(Uteis.getDataString(rs.getString("dataquitacao")));
                conta.setVencimento(Uteis.getDataString(rs.getString("datavencimento")));
                conta.setTipo(rs.getInt("tipoes") == (tipoperacao ? 2 : 1) ? "E" : "S");
                conta.setPlanoConta(rs.getString("planoconta"));
                lista.add(conta);

            }

            return lista;
        }
    }

    public List<LancamentoJSON> obterLancamentosFuturos(Integer empresa, Date inicio, Date fim, Boolean incluirCheques) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct (mc.codigo) as codigo, mc.descricao,mc.tipooperacao as tipoes,mc.valor,dataquitacao,datavencimento,(Select nome from planoconta where planoconta.codigo =  (Select planoconta from movcontarateio where mc.codigo = movcontarateio.movconta limit 1)) as planoconta\n");
        sql.append("        FROM movconta mc\n");
        sql.append("LEFT JOIN conta ON mc.conta = conta.codigo\n");
        sql.append("INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo\n");
        sql.append("INNER JOIN empresa ON empresa.codigo = mc.empresa\n");
        sql.append("WHERE mc.tipooperacao IN (2,1)\n");
        sql.append("AND mc.empresa = ").append(empresa);
        if (inicio != null) {
            sql.append(" AND  mc.datavencimento >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00");
        }
        if (fim != null) {
            sql.append("' AND  mc.datavencimento <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59");
        }
        sql.append("' order by datavencimento\n");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {

            List<LancamentoJSON> lista = new ArrayList<>();
            while (rs.next()) {
                Boolean tipoperacao = true;
                LancamentoJSON conta = new LancamentoJSON();
                conta.setCodigo(rs.getInt("codigo"));
                conta.setDescricao(rs.getString("descricao"));
                conta.setValor(rs.getDouble("valor"));
                conta.setQuitacao(Uteis.getDataString(rs.getString("dataquitacao")));
                conta.setVencimento(Uteis.getDataString(rs.getString("datavencimento")));
                conta.setTipo(rs.getInt("tipoes") == (tipoperacao ? 2 : 1) ? "E" : "S");
                conta.setPlanoConta(rs.getString("planoconta"));
                lista.add(conta);

            }

            return lista;
        }
    }

    public List<LancamentoJSON> obterRecebiveis(Integer empresa, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("SUM(CASE  fp.tipoformapagamento\n");
        sql.append(" WHEN 'CH' THEN ch.valor\n");
        sql.append(" WHEN 'CA' THEN cc.valor\n");
        sql.append(" ELSE mp.valortotal\n");
        sql.append(" END) as valor,\n");
        sql.append("fp.descricao AS formapagamento FROM movpagamento mp\n");
        sql.append(" INNER JOIN empresa e ON e.codigo = mp.empresa\n");
        sql.append(" INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento\n");
        sql.append(" LEFT JOIN cartaocredito cc ON cc.movpagamento = mp.codigo\n");
        sql.append(" LEFT JOIN cheque ch ON ch.movpagamento = mp.codigo\n");
        sql.append(" LEFT JOIN remessaitem ri ON ri.movpagamento = mp.codigo\n");
        sql.append("WHERE ");
        sql.append("  (mp.recibopagamento is not null or mp.credito = 't') AND mp.valor > 0 ");
        sql.append(" AND e.codigo = ").append(empresa).append(" AND fp.tipoformapagamento in ('CH','CA','CD','AV')  AND ((mp.datapagamento::DATE >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00").append("' ");
        if (fim != null) {
            sql.append("  AND mp.datapagamento::DATE <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59").append("' ");
        }
        sql.append(" and cc.codigo is null and ch.codigo is null)\n");
        sql.append(" OR (ch.datacompesancao::DATE >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00").append("' AND ch.situacao <> 'CA'  ");
        if (fim != null) {
            sql.append(" AND ch.datacompesancao::DATE <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59").append("' ");
        }
        sql.append("  and ch.codigo is not null)\n");
        sql.append(" OR (cc.datacompesancao::DATE >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00").append("' AND cc.situacao <> 'CA' ");
        if (fim != null) {
            sql.append(" AND cc.datacompesancao::DATE <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59").append("' ");
        }
        sql.append(" and cc.codigo is not null))\n");
        sql.append(" group by 2  order by 1 desc");

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {

            List<LancamentoJSON> lista = new ArrayList<>();
            while (rs.next()) {
                LancamentoJSON conta = new LancamentoJSON();
                conta.setDescricao(rs.getString("formapagamento"));
                conta.setValor(rs.getDouble("valor"));
                conta.setVencimento("");
                conta.setPlanoConta("Gestão Recebíveis");
                conta.setQuitacao("");
                conta.setTipo("E");
                lista.add(conta);
            }
            return lista;
        }
    }

    @Override
    public LancamentoTotal obterSaldoGeral(Integer empresa, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT SUM (valor) AS soma, COUNT(*) AS total, tipooperacao  AS tipo FROM (");
        sql.append("        SELECT distinct (mc.codigo), mc.*");
        sql.append("                FROM movconta mc");
        sql.append("        LEFT JOIN conta ON mc.conta = conta.codigo");
        sql.append("        INNER JOIN movcontarateio mcr ON mcr.movconta = mc.codigo");
        sql.append("        INNER JOIN empresa ON empresa.codigo = mc.empresa");
        sql.append("        INNER JOIN pessoa ON mc.pessoa = pessoa.codigo");
        sql.append("       WHERE ");
        sql.append(" mc.tipooperacao IN (2,1)");
        sql.append("AND mc.empresa = ").append(empresa);
        sql.append(" AND ((");
        if (inicio != null) {
            sql.append("  mc.datavencimento  >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00");
        }
        sql.append("' AND mc.datavencimento <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59");
        sql.append("' ) OR (");
        if (inicio != null) {
            sql.append("  mc.dataquitacao >= '").append(Uteis.getData(inicio, "yyyy-MM-dd") + " 00:00:00");
        }
        if (fim != null) {
            sql.append("' AND  mc.dataquitacao <= '").append(Uteis.getData(fim, "yyyy-MM-dd") + " 23:59:59");
        }
        sql.append("' ))) AS total GROUP BY tipooperacao");

        List<LancamentoJSON> lista = obterRecebiveis(empresa, inicio, fim);

        Double totalRecebiveis = 0.0;
        for (LancamentoJSON obj : lista) {
            totalRecebiveis += obj.getValor();
        }

        LancamentoTotal lancamentoTotals = new LancamentoTotal();

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {
            while (rs.next()) {
                boolean existeEntrada = false;
                if (rs.getInt("tipo") == 2) {
                    existeEntrada = true;
                    lancamentoTotals.setEntradasHoje(rs.getDouble("soma") + totalRecebiveis);
                } else {
                    lancamentoTotals.setSaidasHoje(rs.getDouble("soma"));
                    existeEntrada = false;
                }

                if (!existeEntrada) {
                    lancamentoTotals.setEntradasHoje(totalRecebiveis);
                }
            }
        }

        return lancamentoTotals;
    }

    @Override
    public DadosEmpresa obterDadosEmpresaPeriodo(String key, Integer empresa, Date mesConsulta) throws Exception {
        DadosEmpresa dadosEmpresa = new DadosEmpresa();
        //List<DadosEmpresa> retorno = new ArrayList<>();
        try {
            Date inicio = Uteis.obterPrimeiroDiaMes(mesConsulta);
            Date fim = Uteis.obterUltimoDiaMes(mesConsulta);

            StringBuilder sql = new StringBuilder();

            sql.append("SELECT * FROM dadosgerencialpmg d ");
            sql.append("WHERE d.empresa = ").append(empresa);
            sql.append(" AND d.datageracao >= '").append(Uteis.getData(inicio, "bd"));
            sql.append("' AND d.datageracao <= '").append(Uteis.getData(fim, "bd"));
            sql.append("' AND d.identificador IN ('MCC', 'CCR', 'CRA', 'RI', 'RP', 'RR')");
            sql.append(" and periodicidade = 'MS' ");
            sql.append(" order by identificador, datageracao desc");

            try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {

                while (rs.next()) {

                    dadosEmpresa.setChave(rs.getString("chave"));
                    dadosEmpresa.setNomeEmpresa(rs.getString("nomeempresa"));
                    dadosEmpresa.setEmpresa(rs.getInt("empresa"));
                    dadosEmpresa.setIdentificador(rs.getString("identificador"));
                    dadosEmpresa.setGeracao(rs.getDate("datageracao"));
                    dadosEmpresa.setValor(rs.getInt("valor"));
                    if (rs.getString("identificador") != null && rs.getString("identificador").equals("MCC")) {
                        dadosEmpresa.setMETA_CONTATOS_CRM(dadosEmpresa.getValor());
                    }
                    if (rs.getString("identificador") != null && rs.getString("identificador").equals("CCR")) {
                        dadosEmpresa.setNUMERO_CONTATOS_CRM(dadosEmpresa.getValor());
                    }
                    if (rs.getString("identificador") != null && rs.getString("identificador").equals("CRA")) {
                        dadosEmpresa.setRESULTADO_CRM_CONVERSAO_AGENDADOS(dadosEmpresa.getValor());
                    }
                    if (rs.getString("identificador") != null && rs.getString("identificador").equals("RI")) {
                        dadosEmpresa.setINDICE_RENOVACAO(dadosEmpresa.getValor());
                    }
                    if (rs.getString("identificador") != null && rs.getString("identificador").equals("RP")) {
                        dadosEmpresa.setPREVISAO_RENOVACAO(dadosEmpresa.getValor());
                    }
                    if (rs.getString("identificador") != null && rs.getString("identificador").equals("RR")) {
                        dadosEmpresa.setRENOVADOS(dadosEmpresa.getValor());
                    }
                }
            }

        } catch (Exception ex) {
            Logger.getLogger(AppGestorService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return dadosEmpresa;
    }

    @Override
    public void close() throws Exception {

    }
}

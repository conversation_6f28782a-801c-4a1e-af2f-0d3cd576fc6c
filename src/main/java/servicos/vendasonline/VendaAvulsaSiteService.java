package servicos.vendasonline;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmailVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.basico.VinculoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.financeiro.AulaAvulsaDiaria;
import negocio.facade.jdbc.financeiro.VendaAvulsa;
import negocio.facade.jdbc.plano.Modalidade;
import negocio.facade.jdbc.plano.Produto;
import org.apache.commons.lang.StringUtils;
import servicos.vendasonline.dto.VendaDTO;
import servicos.vendasonline.dto.VendaProdutoDTO;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static java.util.Objects.isNull;

public class VendaAvulsaSiteService {

    private Connection con;

    public VendaAvulsaSiteService(Connection con) {
        this.con = con;
    }

    public AulaAvulsaDiariaVO incluirDiariaSite(AulaAvulsaDiaria aulaAvulsaDiariaDao,
                                                Double valor,
                                                ProdutoVO produtoVO,
                                                EmpresaVO empresaVO,
                                                ClienteVO clienteVO,
                                                UsuarioVO usuarioVO,
                                                OrigemSistemaEnum origemSistemaEnum,
                                                Integer modalidade,
                                                Date dataLancamento,
                                                Date dataRegistro,
                                                Date dataInicio) throws Exception {
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### INCLUSÃO DE DIÁRIA VIA incluirDiariaSite - VendaAvulsaSiteService.java ");
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### Matrícula " + clienteVO.getMatricula());
        AulaAvulsaDiariaVO avulsaDiariaVO = new AulaAvulsaDiariaVO();
        avulsaDiariaVO.setDataLancamento(isNull(dataLancamento) ? Calendario.hoje() : dataLancamento);
        avulsaDiariaVO.setDataRegistro(isNull(dataRegistro) ? Calendario.hoje() : dataRegistro);
        avulsaDiariaVO.setResponsavel(usuarioVO);
        avulsaDiariaVO.setCliente(clienteVO);
        avulsaDiariaVO.setNomeComprador(avulsaDiariaVO.getCliente().getPessoa().getNome());
        avulsaDiariaVO.setEmpresa(empresaVO);
        avulsaDiariaVO.setProduto(produtoVO);
        avulsaDiariaVO.setModalidade(new ModalidadeVO());
        avulsaDiariaVO.getModalidade().setCodigo(modalidade);
        avulsaDiariaVO.setDataInicio(isNull(dataInicio) ? Calendario.hoje() : dataInicio);
        avulsaDiariaVO.setValor(valor);
        aulaAvulsaDiariaDao.incluirSemCommit(avulsaDiariaVO, origemSistemaEnum);
        Logger.getLogger(getClass().getSimpleName()).log(Level.INFO, "#### FINALIZOU INCLUSÃO DE DIÁRIA VIA incluirDiariaSite - VendaAvulsaSiteService.java ");
        return avulsaDiariaVO;
    }

    public OrigemSistemaEnum origemSistema(VendaDTO  vendaDTO){
        return vendaDTO.isVendaConsultor() ? OrigemSistemaEnum.APP_CONSULTOR : OrigemSistemaEnum.VENDAS_ONLINE_2;
    }

    public VendaAvulsaVO incluirVendaAvulsaSite(VendaDTO vendaDTO, ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception {
        VendaAvulsa vendaDAO = null;
        Empresa empresaDAO = null;
        Produto produtoDAO = null;
        AulaAvulsaDiaria aulaAvulsaDiariaDao = null;
        try {
            con.setAutoCommit(false);

            empresaDAO = new Empresa(con);
            vendaDAO = new VendaAvulsa(con);
            produtoDAO = new Produto(con);
            aulaAvulsaDiariaDao = new AulaAvulsaDiaria(con);
            OrigemSistemaEnum origemSistemaEnum = origemSistema(vendaDTO);

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(vendaDTO.getUnidade(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Date dataRegistro = null;
            if(vendaDTO.isVendaConsultor() && vendaDTO.getDataInicioVendaProdutos() != null){
                dataRegistro = vendaDTO.getDataInicioVendaProdutos();
            }else{
                dataRegistro = Calendario.hoje();
            }

            VendaAvulsaVO vendaAvulsaVO = new VendaAvulsaVO();
            vendaAvulsaVO.setOrigemSistema(origemSistemaEnum);
            vendaAvulsaVO.setTipoComprador("CI");
            vendaAvulsaVO.setCliente(clienteVO);
            vendaAvulsaVO.setDataRegistro(dataRegistro);
            vendaAvulsaVO.setEmpresa(empresaVO);
            vendaAvulsaVO.setNomeComprador(vendaAvulsaVO.getCliente().getPessoa().getNome());
            vendaAvulsaVO.setResponsavel(usuarioVO);
            vendaAvulsaVO.getEventoVO().setCodigo(vendaDTO.getCodigoEvento());

            vendaAvulsaVO.setItemVendaAvulsaVOs(new ArrayList<>());
            for (VendaProdutoDTO vendaProdutoDTO : vendaDTO.getProdutos()) {
                ProdutoVO produtoVO = produtoDAO.consultarPorChavePrimaria(vendaProdutoDTO.getProduto(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if(vendaProdutoDTO.getValorUnitario() != null){
                    produtoVO.setValorFinal(vendaProdutoDTO.getValorUnitario());
                }
                Integer modalidade = vendaDTO.isVendaConsultor() ? vendaProdutoDTO.getModalidade() : produtoVO.getModalidadeVendasOnline();
                if(produtoVO.getTipoProduto().equals(TipoProduto.DIARIA.getCodigo()) && UteisValidacao.emptyList(vendaDTO.getAulasMarcadas())){

                    if(vendaDTO.isPermiteInformarDataUtilizacao() && vendaDTO.getDataUtilizacao() != null) {
                        vendaProdutoDTO.setDataInicio(vendaDTO.getDataUtilizacao());
                    }

                    AulaAvulsaDiariaVO avulsaDiariaVO = incluirDiariaSite(aulaAvulsaDiariaDao,
                            vendaProdutoDTO.getValorUnitario(),
                            produtoVO,
                            empresaVO,
                            clienteVO,
                            usuarioVO,
                            origemSistemaEnum,
                            modalidade,
                            vendaProdutoDTO.getDataLancamento(),
                            vendaProdutoDTO.getDataRegistro(),
                            vendaProdutoDTO.getDataInicio());
                    vendaAvulsaVO.setDiariaVendaOnline(avulsaDiariaVO);
                    continue;
                }
                ItemVendaAvulsaVO item = new ItemVendaAvulsaVO();
                item.setDataVenda(dataRegistro);
                item.setQuantidade(vendaProdutoDTO.getQtd());
                item.setUsuarioVO(usuarioVO);
                item.setProduto(produtoVO);
                vendaAvulsaVO.getItemVendaAvulsaVOs().add(item);
            }
            if(!UteisValidacao.emptyList(vendaAvulsaVO.getItemVendaAvulsaVOs())){
                if(vendaDTO.isVendaConsultor()){
                    vendaAvulsaVO.setNomeComprador(clienteVO.getPessoa().getNome());
                }
                vendaDAO.incluirSemCommit(vendaAvulsaVO, false, dataRegistro);
            }

            con.commit();
            return vendaAvulsaVO;
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
            ex.printStackTrace();
            throw ex;
        } finally {
            con.setAutoCommit(true);
            vendaDAO = null;
            empresaDAO = null;
            produtoDAO = null;
            aulaAvulsaDiariaDao = null;
        }
    }

    private void validarDados(VendaDTO vendaDTO) throws Exception {
        if(UteisValidacao.emptyString(Uteis.getSobrenome(vendaDTO.getNome()))){
            throw new Exception("Informe seu sobrenome");
        }
    }

    private void gravarLogTentativaCadastroClienteVendas(VendaDTO vendaDTO, Exception ex) {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("PERSISTIR CLIENTE");
            log.setChavePrimaria("");
            log.setNomeEntidade("CLIENTE");
            log.setValorCampoAnterior("");
            log.setValorCampoAlterado("EMPRESA: ".concat(""+vendaDTO.getUnidade()).concat(";CLIENTE: ").concat(vendaDTO.getNome()).concat(";EMAIL: ").concat(vendaDTO.getEmail()).concat(";MOTIVO: ").concat(ex.getMessage()));
            log.setOperacao("FALHA_INCLUSAO_CLIENTE_VENDAS_ONLINE");
            log.setResponsavelAlteracao("VENDAS_ONLINE");
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception e) {
            Uteis.logar(null, "FALHA AO INCLUIR LOG DA FALHA DE INCLUSÃO DE CLIENTE ATRÁVES DO VENDAS ONLINE");
        }
    }

    private List consultarEmailExistente(String email) throws Exception {
        Email emailDao = new Email(con);
        List emails = emailDao.consultarEmailExiste(email, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        emailDao = null;
        return emails;
    }

    private void validarInserirEmail(List emails, String email, PessoaVO pessoa) throws Exception {
        if (emails.size() == 0 && StringUtils.isNotBlank(email)) {
            gravarEmail(pessoa, true, email);
        }
    }

    private void gravarEmail(PessoaVO pessoa, boolean emailcorrespondencia, String email) throws Exception {
        con.setAutoCommit(true);
        Email emailDao = new Email(con);
        EmailVO emailVO = new EmailVO();
        emailVO.setPessoa(pessoa.getCodigo());
        emailVO.setEmailCorrespondencia(emailcorrespondencia);
        emailVO.setEmail(email);
        emailDao.incluir(emailVO);
        emailDao = null;
        con.setAutoCommit(false);
    }

    private ClienteVO validarSobreporVisitanteExistente(List pessoas, List emails, String dataNascimento, String email) throws Exception {
        if (!UteisValidacao.emptyList(pessoas)) {
            PessoaVO pessoa = (PessoaVO) pessoas.get(0);
            Cliente clienteDao = new Cliente(con);
            Contrato contratoDao = new Contrato(con);
            ClienteVO clienteVO = clienteDao.consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ContratoVO contrato = contratoDao.consultarContratoVigentePorPessoa(pessoa.getCodigo(), false, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            contratoDao = null;

            if (clienteVO.getSituacao().equals(SituacaoClienteEnum.VISITANTE.getCodigo()) && UteisValidacao.emptyNumber(contrato.getCodigo())) {
                if (!UteisValidacao.emptyString(dataNascimento) && Calendario.getDataComHoraZerada(pessoa.getDataNasc()).equals(Calendario.getDataComHoraZerada(Uteis.getDate(dataNascimento)))) {
                    for (Object o : emails) {
                        EmailVO emailVO = (EmailVO) o;
                        if (emailVO.getEmail().equalsIgnoreCase(email)) {
                            clienteVO = clienteDao.consultarPorChavePrimaria(clienteVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                            clienteDao = null;
                            return clienteVO;
                        }
                    }
                }
            }
        }
        return null;
    }

    private PessoaVO povoarTelefoneEnderecoVisitanteExistente(PessoaVO pessoaExistente, String telResidencial, String telCelular, final String endereco, final String complemento,
                                                              final String numero, final String bairro, final String cep) throws Exception {
        Pessoa pessoaDao = new Pessoa(con);
        pessoaExistente = pessoaDao.consultarPorChavePrimaria(pessoaExistente.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean telefoneComercialExistente = false;
        boolean telefoneResidencialExistente = false;
        for (TelefoneVO telefone : pessoaExistente.getTelefoneVOs()) {
            if (telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo()) && !UteisValidacao.emptyString(telCelular)) {
                telefoneComercialExistente = true;
                telefone.setNumero(telCelular);
            } else if (telefone.getTipoTelefone().equals(TipoTelefoneEnum.RESIDENCIAL.getCodigo()) && !UteisValidacao.emptyString(telResidencial)) {
                telefoneResidencialExistente = true;
                telefone.setNumero(telResidencial);
            }
        }
        if (!telefoneComercialExistente) {
            povoarTelefonePessoa(pessoaExistente.getTelefoneVOs(), telCelular, TipoTelefoneEnum.CELULAR, "");
        }
        if (!telefoneResidencialExistente) {
            povoarTelefonePessoa(pessoaExistente.getTelefoneVOs(), telResidencial, TipoTelefoneEnum.RESIDENCIAL, "");
        }
        boolean enderecoExistente = false;
        for (EnderecoVO enderecoVO : pessoaExistente.getEnderecoVOs()) {
            if (enderecoVO.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo()) && !UteisValidacao.emptyString(endereco)) {
                enderecoExistente = true;
                enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
                enderecoVO.setEndereco(endereco);
                enderecoVO.setComplemento(complemento);
                enderecoVO.setNumero(numero);
                enderecoVO.setBairro(bairro);
                enderecoVO.setCep(cep);
                break;
            }
        }
        if (!enderecoExistente) {
            povoarEndereco(pessoaExistente.getEnderecoVOs(), endereco, complemento, numero, bairro, cep);
        }
        return pessoaExistente;
    }

    private void povoarTelefonePessoa(List<TelefoneVO> telefones, String numero, TipoTelefoneEnum tipo, String descricao) {
        if (UteisValidacao.emptyString(numero)) {
            return;
        }
        TelefoneVO telefoneVO = new TelefoneVO();
        telefoneVO.setTipoTelefone(tipo.getCodigo());
        telefoneVO.setNumero(Formatador.formataTelefoneZW(numero));
        telefoneVO.setDescricao(descricao);
        telefones.add(telefoneVO);
    }

    private void povoarEndereco(List<EnderecoVO> enderecoVOS, final String endereco, final String complemento,
                                final String numero, final String bairro, final String cep) {
        if (UteisValidacao.emptyString(endereco)) {
            return;
        }
        EnderecoVO enderecoVO = new EnderecoVO();
        enderecoVO.setTipoEndereco(TipoEnderecoEnum.RESIDENCIAL.getCodigo());
        enderecoVO.setEndereco(endereco);
        enderecoVO.setComplemento(complemento);
        enderecoVO.setNumero(numero);
        enderecoVO.setBairro(bairro);
        enderecoVO.setCep(cep);
        enderecoVO.setEnderecoCorrespondencia(true);
        enderecoVOS.add(enderecoVO);
    }

    private PessoaVO povoarTelefoneEndereco(PessoaVO pessoa, String telCelular, String telComercial, final String endereco, final String complemento,
                                            final String numero, final String bairro, final String cep) throws Exception {
        povoarTelefonePessoa(pessoa.getTelefoneVOs(), telCelular, TipoTelefoneEnum.CELULAR, "");
        povoarTelefonePessoa(pessoa.getTelefoneVOs(), telComercial, TipoTelefoneEnum.RESIDENCIAL, "");
        povoarEndereco(pessoa.getEnderecoVOs(), endereco, complemento, numero, bairro, cep);
        return pessoa;
    }

    private void adicionarConsultor(ClienteVO cliente, ColaboradorVO consultor) {
        if (cliente != null && !UteisValidacao.emptyList(cliente.getVinculoVOs())) {
            for (VinculoVO vinculo : cliente.getVinculoVOs()) {
                if (vinculo.getTipoColaboradorVinculo().equals(TipoColaboradorEnum.CONSULTOR)) {
                    vinculo.setColaborador(consultor);
                    return;
                }
            }
        }
        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setColaborador(consultor);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());
        cliente.getVinculoVOs().add(vinculoVO);
    }
}

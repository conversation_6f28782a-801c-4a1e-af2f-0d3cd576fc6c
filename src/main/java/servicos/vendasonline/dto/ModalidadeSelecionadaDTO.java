package servicos.vendasonline.dto;

import org.json.JSONObject;

public class ModalidadeSelecionadaDTO {

    private Integer codigo;
    private String modalidade;
    private Integer selectedTimesPerWeek;

    public ModalidadeSelecionadaDTO() {
    }

    public ModalidadeSelecionadaDTO(JSONObject json) {
        this.codigo = json.optInt("codigo");
        this.modalidade = json.getString("modalidade");
        this.selectedTimesPerWeek = json.getInt("selectedTimesPerWeek");
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public Integer getSelectedTimesPerWeek() {
        return selectedTimesPerWeek;
    }

    public void setSelectedTimesPerWeek(Integer selectedTimesPerWeek) {
        this.selectedTimesPerWeek = selectedTimesPerWeek;
    }
}

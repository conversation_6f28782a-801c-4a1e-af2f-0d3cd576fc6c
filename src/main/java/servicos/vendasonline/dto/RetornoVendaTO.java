package servicos.vendasonline.dto;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 25/06/2020
 */
public class RetornoVendaTO {

    private Integer codigoCliente;
    private String matricula;
    private String msgRetorno;
    private boolean erro;
    private Integer contrato;
    private Integer codigoFinanceiro;
    private Integer vendaAvulsa;
    private VendaDTO vendaDTO;
    private ContratoVO contratoVO;
    private VendaAvulsaVO vendaAvulsaVO;
    private AulaAvulsaDiariaVO diariaVO;
    private ClienteVO clienteVO;
    private Double valorPagarContrato;
    private List<ParcelaDTO> parcelasPagarContrato;
    private PixVO pixVO;
    private TransacaoVO transacaoVO;
    private BoletoVO boletoVO;
    private List<BoletoVO> boletosGerados;

    public RetornoVendaTO(VendaDTO vendaDTO) {
        this.vendaDTO = vendaDTO;
        this.codigoFinanceiro = vendaDTO.getCodigoFinanceiro();
    }

    public RetornoVendaTO(String mensagemErro) {
        this.msgRetorno = mensagemErro;
        this.erro = true;
    }

    public RetornoVendaTO() {
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("codigoCliente", this.codigoCliente);
        json.put("matricula", this.matricula);
        json.put("msgRetorno", this.msgRetorno);
        json.put("contrato", this.contrato);
        json.put("vendaAvulsa", this.vendaAvulsa);
        json.put("codigoFinanceiro", this.codigoFinanceiro);
        json.put("valorPagar", this.valorPagarContrato);
        if (!UteisValidacao.emptyList(this.parcelasPagarContrato)) {
            json.put("parcelasPagar", converterParcelarPagarJSON(this.parcelasPagarContrato));
        }
        return json;
    }

    private JSONArray converterParcelarPagarJSON(List<ParcelaDTO> parcelasPagar) {

        JSONArray jsonArray = new JSONArray();
        for(ParcelaDTO parcelaDTO :parcelasPagar){
            jsonArray.put(parcelaDTO.toJSON());
        }

        return jsonArray;
    }


    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(Integer vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public String getMsgRetorno() {
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public VendaDTO getVendaDTO() {
        return vendaDTO;
    }

    public void setVendaDTO(VendaDTO vendaDTO) {
        this.vendaDTO = vendaDTO;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public VendaAvulsaVO getVendaAvulsaVO() {
        return vendaAvulsaVO;
    }

    public void setVendaAvulsaVO(VendaAvulsaVO vendaAvulsaVO) {
        this.vendaAvulsaVO = vendaAvulsaVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Integer getCodigoFinanceiro() {
        return codigoFinanceiro;
    }

    public void setCodigoFinanceiro(Integer codigoFinanceiro) {
        this.codigoFinanceiro = codigoFinanceiro;
    }

    public AulaAvulsaDiariaVO getDiariaVO() {
        return diariaVO;
    }

    public void setDiariaVO(AulaAvulsaDiariaVO diariaVO) {
        this.diariaVO = diariaVO;
    }

    public boolean isErro() {
        return erro;
    }

    public void setErro(boolean erro) {
        this.erro = erro;
    }

    public Double getValorPagarContrato() {
        return valorPagarContrato;
    }

    public void setValorPagarContrato(Double valorPagarContrato) {
        this.valorPagarContrato = valorPagarContrato;
    }

    public List<ParcelaDTO> getParcelasPagarContrato() {
        return parcelasPagarContrato;
    }

    public void setParcelasPagarContrato(List<ParcelaDTO> parcelasPagarContrato) {
        this.parcelasPagarContrato = parcelasPagarContrato;
    }

    public PixVO getPixVO() {
        if (pixVO == null) {
            pixVO = new PixVO();
        }
        return pixVO;
    }

    public void setPixVO(PixVO pixVO) {
        this.pixVO = pixVO;
    }

    public TransacaoVO getTransacaoVO() {
        if (transacaoVO == null) {
            transacaoVO = new TransacaoVO();
        }
        return transacaoVO;
    }

    public void setTransacaoVO(TransacaoVO transacaoVO) {
        this.transacaoVO = transacaoVO;
    }

    public BoletoVO getBoletoVO() {
        if (boletoVO == null) {
            boletoVO = new BoletoVO();
        }
        return boletoVO;
    }

    public void setBoletoVO(BoletoVO boletoVO) {
        this.boletoVO = boletoVO;
    }

    public RetornoVendaTO(JSONObject json) {
        this.setVendaDTO(new VendaDTO(json.optString("vendaDTO")));

        this.setCodigoCliente(json.optInt("cliente"));
        this.setMatricula(json.optString("matricula"));
        this.setMsgRetorno(json.optString("msgRetorno"));

        this.setContrato(json.optInt("contrato"));
        this.setContratoVO(new ContratoVO());
        this.getContratoVO().setCodigo(this.getContrato());

        this.setVendaAvulsa(json.optInt("vendaavulsa"));
        this.setVendaAvulsaVO(new VendaAvulsaVO());
        this.getVendaAvulsaVO().setCodigo(this.getVendaAvulsa());

        this.setDiariaVO(new AulaAvulsaDiariaVO());
        this.getDiariaVO().setCodigo(json.optInt("diaria"));

        this.getPixVO().setCodigo(json.optInt("pix"));
        this.getTransacaoVO().setCodigo(json.optInt("transacao"));
        this.getBoletoVO().setCodigo(json.optInt("boleto"));

        try {
            JSONArray array = json.optJSONArray("boletosGerados");
            if (array != null) {
                for (int e = 0; e < array.length(); e++) {
                    Integer cod = array.getInt(e);
                    BoletoVO boletoVO1 = new BoletoVO();
                    boletoVO1.setCodigo(cod);
                    this.getBoletosGerados().add(boletoVO1);
                }
            }
        } catch (Exception ignored) {
        }
    }

    public JSONObject toJSONVendaVendasOnline() {
        JSONObject json = new JSONObject();
        try {
            json.put("vendaDTO", new JSONObject(this.getVendaDTO()).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        if (!UteisValidacao.emptyNumber(this.getCodigoCliente())) {
            json.put("cliente", this.getCodigoCliente());
        }
        if (!UteisValidacao.emptyString(this.getMatricula())) {
            json.put("matricula", this.getMatricula());
        }
        if (!UteisValidacao.emptyString(this.getMsgRetorno())) {
            json.put("msgRetorno", this.getMsgRetorno());
        }
        if (!UteisValidacao.emptyNumber(this.getContrato())) {
            json.put("contrato", this.getContrato());
        }
        if (!UteisValidacao.emptyNumber(this.getVendaAvulsa())) {
            json.put("vendaavulsa", this.getVendaAvulsa());
        }
        if (this.getDiariaVO() != null &&
                !UteisValidacao.emptyNumber(this.getDiariaVO().getCodigo())) {
            json.put("diaria", this.getDiariaVO().getCodigo());
        }
        if (!UteisValidacao.emptyNumber(this.getPixVO().getCodigo())) {
            json.put("pix", this.getPixVO().getCodigo());
        }
        if (!UteisValidacao.emptyNumber(this.getTransacaoVO().getCodigo())) {
            json.put("transacao", this.getTransacaoVO().getCodigo());
        }
        if (!UteisValidacao.emptyNumber(this.getBoletoVO().getCodigo())) {
            json.put("boleto", this.getBoletoVO().getCodigo());
        }
        if (!UteisValidacao.emptyList(this.getBoletosGerados())) {
            JSONArray arrayBole = new JSONArray();
            for (BoletoVO boletoVO1 : this.getBoletosGerados()) {
                arrayBole.put(boletoVO1.getCodigo());
            }
            json.put("boletosGerados", arrayBole);
        }
        return json;
    }

    public List<BoletoVO> getBoletosGerados() {
        if (boletosGerados == null) {
            boletosGerados = new ArrayList<>();
        }
        return boletosGerados;
    }

    public void setBoletosGerados(List<BoletoVO> boletosGerados) {
        this.boletosGerados = boletosGerados;
    }
}

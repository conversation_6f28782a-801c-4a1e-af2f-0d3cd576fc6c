package servicos.vendasonline.dto;

import negocio.comuns.plano.CategoriaProdutoVO;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 05/06/2020
 */
public class CategoriaProdutoVendasOnlineDTO {

    private Integer codigo;
    private String descricao;

    public CategoriaProdutoVendasOnlineDTO() {

    }

    public CategoriaProdutoVendasOnlineDTO(CategoriaProdutoVO categoriaProdutoVO) {
        this.codigo = categoriaProdutoVO.getCodigo();
        this.descricao = categoriaProdutoVO.getDescricao();
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }


    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}

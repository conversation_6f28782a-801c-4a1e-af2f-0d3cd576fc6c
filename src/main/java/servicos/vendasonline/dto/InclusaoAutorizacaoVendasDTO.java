package servicos.vendasonline.dto;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;

import java.util.ArrayList;
import java.util.List;

public class InclusaoAutorizacaoVendasDTO {

    private AutorizacaoCobrancaClienteVO novaAutorizacao;
    private List<AutorizacaoCobrancaClienteVO> anteriores;

    public AutorizacaoCobrancaClienteVO getNovaAutorizacao() {
        return novaAutorizacao;
    }

    public void setNovaAutorizacao(AutorizacaoCobrancaClienteVO novaAutorizacao) {
        this.novaAutorizacao = novaAutorizacao;
    }

    public List<AutorizacaoCobrancaClienteVO> getAnteriores() {
        if (anteriores == null) {
            anteriores = new ArrayList<>();
        }
        return anteriores;
    }

    public void setAnteriores(List<AutorizacaoCobrancaClienteVO> anteriores) {
        this.anteriores = anteriores;
    }
}

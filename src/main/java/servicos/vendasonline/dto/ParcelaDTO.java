package servicos.vendasonline.dto;

import negocio.comuns.financeiro.MovParcelaVO;
import org.apache.poi.util.ArrayUtil;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ParcelaDTO {

    private Integer codigo;
    private String descricao;
    private String situacao;
    private Double valorParcela;

    public ParcelaDTO(MovParcelaVO movParcelaVO) {
        this.codigo = movParcelaVO.getCodigo();
        this.descricao = movParcelaVO.getDescricao();
        this.situacao = movParcelaVO.getSituacao();
        this.valorParcela = movParcelaVO.getValorParcela();
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }

    public static List<ParcelaDTO> movParcelasToDTOList(List<MovParcelaVO> parcelas){
        List<ParcelaDTO> parcelaDTOS = new ArrayList<>();
        for(MovParcelaVO movParcelaVO: parcelas){
            parcelaDTOS.add(new ParcelaDTO(movParcelaVO));
        }

        return parcelaDTOS;
    }

    public static JSONArray toJSONArray(List<MovParcelaVO> parcelas){
        JSONArray json = new JSONArray();
        for(MovParcelaVO movParcelaVO: parcelas){
            json.put(new ParcelaDTO(movParcelaVO));
        }

        return json;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Double getValorParcela() {
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }
}

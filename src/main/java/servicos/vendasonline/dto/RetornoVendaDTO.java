package servicos.vendasonline.dto;

import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import org.json.JSONObject;

public class RetornoVendaDTO {

    private String msgRetorno;
    private boolean erro;
    private VendaDTO vendaDTO;
    private ContratoDTO contrato;
    private VendaAvulsaDTO vendaAvulsa;
    private AulaAvulsaDiariaDTO aulaAvulsaDiariaVO;
    private ClienteDTO cliente;

    public RetornoVendaDTO(String mensagemErro) {
        this.msgRetorno = mensagemErro;
        this.erro = true;
    }

    public RetornoVendaDTO(RetornoVendaTO retornoVendaTO) {
        this.msgRetorno = retornoVendaTO.getMsgRetorno();
        this.erro = retornoVendaTO.isErro();
        this.vendaDTO = retornoVendaTO.getVendaDTO();
        this.contrato = retornoVendaTO.getContrato() != null ? new ContratoDTO(retornoVendaTO) : null;
        this.vendaAvulsa = retornoVendaTO.getVendaAvulsaVO() != null ? new VendaAvulsaDTO(retornoVendaTO.getVendaAvulsaVO()) : null;
        this.aulaAvulsaDiariaVO = retornoVendaTO.getDiariaVO() != null ? new AulaAvulsaDiariaDTO(retornoVendaTO.getDiariaVO()) : null;
        this.cliente = new ClienteDTO(retornoVendaTO.getClienteVO());
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("contrato", this.contrato != null ? this.contrato.toJSON() : null);
        json.put("vendaAvulsa", this.vendaAvulsa != null ? this.vendaAvulsa.toJSON() : null);
        json.put("aulaAvulsa", this.aulaAvulsaDiariaVO != null ? this.aulaAvulsaDiariaVO.toJSON() : null);
        json.put("cliente", this.cliente.toJSON());
        return json;
    }

    public String getMsgRetorno() {
        if(msgRetorno == null){
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public boolean isErro() {
        return erro;
    }

    public void setErro(boolean erro) {
        this.erro = erro;
    }

    public VendaDTO getVendaDTO() {
        return vendaDTO;
    }

    public void setVendaDTO(VendaDTO vendaDTO) {
        this.vendaDTO = vendaDTO;
    }

    public ContratoDTO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoDTO contrato) {
        this.contrato = contrato;
    }

    public VendaAvulsaDTO getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(VendaAvulsaDTO vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public ClienteDTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteDTO cliente) {
        this.cliente = cliente;
    }

    public AulaAvulsaDiariaDTO getAulaAvulsaDiariaVO() {
        return aulaAvulsaDiariaVO;
    }

    public void setAulaAvulsaDiariaVO(AulaAvulsaDiariaDTO aulaAvulsaDiariaVO) {
        this.aulaAvulsaDiariaVO = aulaAvulsaDiariaVO;
    }
}

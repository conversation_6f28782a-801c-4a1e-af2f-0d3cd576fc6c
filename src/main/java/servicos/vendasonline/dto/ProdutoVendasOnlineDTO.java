package servicos.vendasonline.dto;

import negocio.comuns.plano.ProdutoImagemTO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 04/06/2020
 */
public class ProdutoVendasOnlineDTO {

    private Integer codigo;
    private String descricao;
    private String observacao;
    private Double valor;
    private String posicaoEstoque;
    private List<String> imagens;
    private Integer maxDivisao;
    private boolean renovavelAutomaticamente = false;
    private int categoria;
    private String categoriaDescricao = "";
    private String tipo;
    private String tipoDescricao;
    private String codigoBarras;

    public ProdutoVendasOnlineDTO(){

    }

    public ProdutoVendasOnlineDTO(ProdutoVO produtoVO){
        this.codigo = produtoVO.getCodigo();
        this.maxDivisao = produtoVO.getMaxDivisao();
        this.descricao = produtoVO.getDescricao();
        this.observacao = produtoVO.getObservacao();
        this.valor = produtoVO.getValorFinal();
        this.posicaoEstoque = produtoVO.getPosicaoEstoque();
        this.imagens = new ArrayList<>();
        this.renovavelAutomaticamente = produtoVO.getRenovavelAutomaticamente();
        this.categoria = !UteisValidacao.emptyNumber(produtoVO.getCategoriaProduto().getCodigo()) ? produtoVO.getCategoriaProduto().getCodigo() : 0;
        this.categoriaDescricao = produtoVO.getCategoria_Apresentar();
        this.tipo = produtoVO.getTipoProduto();
        this.tipoDescricao = produtoVO.getTipoProduto_Apresentar();
        this.codigoBarras = produtoVO.getCodigoBarras();

        try {
            JSONArray lista = new JSONArray(produtoVO.getImagens());
            for (int e = 0; e < lista.length(); e++) {
                JSONObject obj = lista.getJSONObject(e);
                try {
                    ProdutoImagemTO produtoImagemTO = new ProdutoImagemTO(obj);
                    String urlImagem = produtoImagemTO.getUrlFoto();
                    if (!UteisValidacao.emptyString(urlImagem)) {
                        this.imagens.add(urlImagem);
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ex) {
        }
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<String> getImagens() {
        if (imagens == null) {
            imagens = new ArrayList<>();
        }
        return imagens;
    }

    public void setImagens(List<String> imagens) {
        this.imagens = imagens;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getPosicaoEstoque() {
        if (posicaoEstoque == null) {
            posicaoEstoque = "";
        }
        return posicaoEstoque;
    }

    public void setPosicaoEstoque(String posicaoEstoque) {
        this.posicaoEstoque = posicaoEstoque;
    }

    public Integer getMaxDivisao() {
        if (maxDivisao == null) {
            maxDivisao = 0;
        }
        return maxDivisao;
    }

    public void setMaxDivisao(Integer maxDivisao) {
        this.maxDivisao = maxDivisao;
    }

    public boolean isRenovavelAutomaticamente() {
        return renovavelAutomaticamente;
    }

    public void setRenovavelAutomaticamente(boolean renovavelAutomaticamente) {
        this.renovavelAutomaticamente = renovavelAutomaticamente;
    }

    public int getCategoria() {
        return categoria;
    }

    public void setCategoria(int categoria) {
        this.categoria = categoria;
    }

    public String getCategoriaDescricao() {
        return categoriaDescricao;
    }

    public void setCategoriaDescricao(String categoriaDescricao) {
        this.categoriaDescricao = categoriaDescricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getTipoDescricao() {
        return tipoDescricao;
    }

    public void setTipoDescricao(String tipoDescricao) {
        this.tipoDescricao = tipoDescricao;
    }

    public String getCodigoBarras() {
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }
}

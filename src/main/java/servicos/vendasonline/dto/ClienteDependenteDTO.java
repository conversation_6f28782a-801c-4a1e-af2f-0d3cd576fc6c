package servicos.vendasonline.dto;

import negocio.comuns.basico.ClienteVO;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ClienteDependenteDTO {
    private int unidade;
    private String nome;
    private String cpf;
    private String responsavelPai;
    private String responsavelMae;
    private String sexo;
    private String email;
    private String dataNascimento;
    private String telefone;
    private String endereco;
    private String numero;
    private String bairro;
    private String complemento;
    private String cep;
    private String cpfPai;
    private String cpfMae;
    private String remetenteConviteMatricula;
    private String rg;
    private int codigoEvento;
    private int usuarioResponsavel;
    private int codigoRegistroAcessoPagina;
    private String freepass;
    private List<String> aulasMarcadas;

    public ClienteDependenteDTO(JSONObject json) {
        this.unidade = json.optInt("unidade");
        this.nome = json.optString("nome");
        this.cpf = json.optString("cpf");
        this.responsavelPai = json.optString("responsavelPai");
        this.responsavelMae = json.optString("responsavelMae");
        this.sexo = json.optString("sexo");
        this.email = json.optString("email");
        this.dataNascimento = json.optString("dataNascimento");
        this.telefone = json.optString("telefone");
        this.endereco = json.optString("endereco");
        this.numero = json.optString("numero");
        this.bairro = json.optString("bairro");
        this.complemento = json.optString("complemento");
        this.cep = json.optString("cep");
        this.cpfPai = json.optString("cpfPai");
        this.cpfMae = json.optString("cpfMae");
        this.remetenteConviteMatricula = json.optString("remetenteConviteMatricula");
        this.rg = json.optString("rg");
        this.codigoEvento = json.optInt("codigoEvento");
        this.usuarioResponsavel = json.optInt("usuarioResponsavel");
        this.codigoRegistroAcessoPagina = json.optInt("codigoRegistroAcessoPagina");
        this.freepass = json.optString("freepass");

        this.aulasMarcadas = new ArrayList<>();
        JSONArray aulasJsonArray = json.optJSONArray("aulasMarcadas");
        if (aulasJsonArray != null) {
            for (int i = 0; i < aulasJsonArray.length(); i++) {
                this.aulasMarcadas.add(aulasJsonArray.optString(i));
            }
        }
    }

    // Getters and setters
    public int getUnidade() {
        return unidade;
    }

    public void setUnidade(int unidade) {
        this.unidade = unidade;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getResponsavelPai() {
        return responsavelPai;
    }

    public void setResponsavelPai(String responsavelPai) {
        this.responsavelPai = responsavelPai;
    }

    public String getResponsavelMae() {
        return responsavelMae;
    }

    public void setResponsavelMae(String responsavelMae) {
        this.responsavelMae = responsavelMae;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(String dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getCpfPai() {
        return cpfPai;
    }

    public void setCpfPai(String cpfPai) {
        this.cpfPai = cpfPai;
    }

    public String getCpfMae() {
        return cpfMae;
    }

    public void setCpfMae(String cpfMae) {
        this.cpfMae = cpfMae;
    }

    public String getRemetenteConviteMatricula() {
        return remetenteConviteMatricula;
    }

    public void setRemetenteConviteMatricula(String remetenteConviteMatricula) {
        this.remetenteConviteMatricula = remetenteConviteMatricula;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public int getCodigoEvento() {
        return codigoEvento;
    }

    public void setCodigoEvento(int codigoEvento) {
        this.codigoEvento = codigoEvento;
    }

    public int getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(int usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public int getCodigoRegistroAcessoPagina() {
        return codigoRegistroAcessoPagina;
    }

    public void setCodigoRegistroAcessoPagina(int codigoRegistroAcessoPagina) {
        this.codigoRegistroAcessoPagina = codigoRegistroAcessoPagina;
    }

    public String getFreepass() {
        return freepass;
    }

    public void setFreepass(String freepass) {
        this.freepass = freepass;
    }

    public List<String> getAulasMarcadas() {
        return aulasMarcadas;
    }

    public void setAulasMarcadas(List<String> aulasMarcadas) {
        this.aulasMarcadas = aulasMarcadas;
    }
}

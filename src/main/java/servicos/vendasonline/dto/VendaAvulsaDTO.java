package servicos.vendasonline.dto;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import org.json.JSONObject;

import java.util.List;

public class VendaAvulsaDTO {
    private Integer codigo;
    private Double valortotal;
    private List<ParcelaDTO> parcelasPagar;

    public VendaAvulsaDTO(VendaAvulsaVO vendaAvulsaVO) {

        this.codigo = vendaAvulsaVO.getCodigo();
        this.valortotal = vendaAvulsaVO.getValorTotal();
        this.parcelasPagar = ParcelaDTO.movParcelasToDTOList(vendaAvulsaVO.getMovParcelaVOs());

    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValortotal() {
        return valortotal;
    }

    public void setValortotal(Double valortotal) {
        this.valortotal = valortotal;
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }

    public List<ParcelaDTO> getParcelasPagar() {
        return parcelasPagar;
    }

    public void setParcelasPagar(List<ParcelaDTO> parcelasPagar) {
        this.parcelasPagar = parcelasPagar;
    }
}

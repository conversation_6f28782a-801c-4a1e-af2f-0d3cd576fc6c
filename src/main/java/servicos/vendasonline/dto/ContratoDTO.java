package servicos.vendasonline.dto;

import org.json.JSONObject;

import java.util.Date;
import java.util.List;

public class ContratoDTO {

    private Integer codigo;
    private Date datalancamento;
    private Date datamatricula;
    private Date dataprevistarenovar;
    private String situacao;
    private Double valorfinal;
    private Date vigenciade;
    private Date vigenciaateajustada;

    private Double valorPagar;
    private List<ParcelaDTO> parcelasPagar;


    public ContratoDTO(RetornoVendaTO retornoVendaTO) {
        this.codigo = retornoVendaTO.getContratoVO().getCodigo();
        this.datalancamento = retornoVendaTO.getContratoVO().getDataLancamento();
        this.dataprevistarenovar =  retornoVendaTO.getContratoVO().getDataPrevistaRenovar();
        this.situacao = retornoVendaTO.getContratoVO().getSituacao();
        this.valorfinal = retornoVendaTO.getContratoVO().getValorFinal();
        this.vigenciade = retornoVendaTO.getContratoVO().getVigenciaDe();
        this.vigenciaateajustada = retornoVendaTO.getContratoVO().getVigenciaAteAjustada();
        this.valorPagar = retornoVendaTO.getValorPagarContrato();
        this.parcelasPagar = retornoVendaTO.getParcelasPagarContrato();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDatalancamento() {
        return datalancamento;
    }

    public void setDatalancamento(Date datalancamento) {
        this.datalancamento = datalancamento;
    }

    public Date getDatamatricula() {
        return datamatricula;
    }

    public void setDatamatricula(Date datamatricula) {
        this.datamatricula = datamatricula;
    }

    public Date getDataprevistarenovar() {
        return dataprevistarenovar;
    }

    public void setDataprevistarenovar(Date dataprevistarenovar) {
        this.dataprevistarenovar = dataprevistarenovar;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Double getValorfinal() {
        return valorfinal;
    }

    public void setValorfinal(Double valorfinal) {
        this.valorfinal = valorfinal;
    }

    public Date getVigenciade() {
        return vigenciade;
    }

    public void setVigenciade(Date vigenciade) {
        this.vigenciade = vigenciade;
    }

    public Date getVigenciaateajustada() {
        return vigenciaateajustada;
    }

    public void setVigenciaateajustada(Date vigenciaateajustada) {
        this.vigenciaateajustada = vigenciaateajustada;
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }

    public List<ParcelaDTO> getParcelasPagar() {
        return parcelasPagar;
    }

    public void setParcelasPagar(List<ParcelaDTO> parcelasPagar) {
        this.parcelasPagar = parcelasPagar;
    }

    public Double getValorPagar() {
        return valorPagar;
    }

    public void setValorPagar(Double valorPagar) {
        this.valorPagar = valorPagar;
    }
}

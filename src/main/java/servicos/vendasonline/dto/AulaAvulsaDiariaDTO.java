package servicos.vendasonline.dto;

import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import org.json.JSONObject;

import java.util.List;

public class AulaAvulsaDiariaDTO {
    private Integer codigo;
    private Double valortotal;
    private List<ParcelaDTO> parcelasPagar;

    public AulaAvulsaDiariaDTO(AulaAvulsaDiariaVO aulaAvulsaDiariaVO) {
        this.codigo = aulaAvulsaDiariaVO.getCodigo();
        this.valortotal = aulaAvulsaDiariaVO.getValor();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValortotal() {
        return valortotal;
    }

    public void setValortotal(Double valortotal) {
        this.valortotal = valortotal;
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }

    public List<ParcelaDTO> getParcelasPagar() {
        return parcelasPagar;
    }

    public void setParcelasPagar(List<ParcelaDTO> parcelasPagar) {
        this.parcelasPagar = parcelasPagar;
    }
}

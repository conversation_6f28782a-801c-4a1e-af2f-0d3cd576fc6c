package servicos.vendasonline.dto;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class VendaProdutoDTO {

    private Integer produto;
    private String descricao;
    private Integer qtd;
    private Double valorUnitario;
    private String observacao;
    private Integer modalidade;
    private Date dataLancamento;
    private Date dataRegistro;
    private Date dataInicio;

    public VendaProdutoDTO() {
    }

    public VendaProdutoDTO(JSONObject json) {
        this.produto = json.optInt("produto");
        this.qtd = json.optInt("qtd");
        this.descricao = json.optString("descricao");
        this.valorUnitario = json.has("valorUnitario") ? json.optDouble("valorUnitario") : null;
        this.observacao = json.optString("observacao");
        this.modalidade = json.optInt("modalidade");
        try {
            this.dataLancamento = new SimpleDateFormat("yyyy-MM-dd").parse(json.optString("dataLancamento"));
        }catch (Exception ignore){
            this.dataLancamento = null;
        }
        try {
            this.dataRegistro = new SimpleDateFormat("yyyy-MM-dd").parse(json.optString("dataRegistro"));
        }catch (Exception ignore){
            this.dataRegistro = null;
        }
        try {
            this.dataInicio = new SimpleDateFormat("yyyy-MM-dd").parse(json.optString("dataInicio"));
        }catch (Exception ignore){
            this.dataInicio = null;
        }
    }

    public Integer getProduto() {
        return produto;
    }

    public void setProduto(Integer produto) {
        this.produto = produto;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Integer getModalidade() {
        return modalidade;
    }

    public void setModalidade(Integer modalidade) {
        this.modalidade = modalidade;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }
}

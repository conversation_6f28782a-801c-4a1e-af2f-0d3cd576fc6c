package servicos.notificacao;

public class RecursoEmpresaDTO {
    private String recurso;
    private String chave;
    private String nomeEmpresa;
    private Number empresa;
    private String usuario;
    private Boolean ce;
    private Boolean crm;
    private String data;
    private Boolean est;
    private Boolean fin;
    private Boolean slc;
    private Boolean tr;
    private Boolean zw;

    public String getRecurso() {
        return recurso;
    }

    public void setRecurso(String recurso) {
        this.recurso = recurso;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Number getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Number empresa) {
        this.empresa = empresa;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Boolean getCe() {
        return ce;
    }

    public void setCe(Boolean ce) {
        this.ce = ce;
    }

    public Boolean getCrm() {
        return crm;
    }

    public void setCrm(Boolean crm) {
        this.crm = crm;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Boolean getEst() {
        return est;
    }

    public void setEst(Boolean est) {
        this.est = est;
    }

    public Boolean getFin() {
        return fin;
    }

    public void setFin(Boolean fin) {
        this.fin = fin;
    }

    public Boolean getSlc() {
        return slc;
    }

    public void setSlc(Boolean slc) {
        this.slc = slc;
    }

    public Boolean getTr() {
        return tr;
    }

    public void setTr(Boolean tr) {
        this.tr = tr;
    }

    public Boolean getZw() {
        return zw;
    }

    public void setZw(Boolean zw) {
        this.zw = zw;
    }
}

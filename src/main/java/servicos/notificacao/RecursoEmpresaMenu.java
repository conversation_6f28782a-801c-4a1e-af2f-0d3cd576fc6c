package servicos.notificacao;

import br.com.pactosolucoes.enumeradores.Modulo;
import controle.arquitetura.FuncionalidadeSistemaEnum;

public class RecursoEmpresaMenu {
    private String identificadorMenu;
    private FuncionalidadeSistemaEnum funcionalidadeSistema;
    private Integer codigoEmpresa;
    private String chave;
    private String nomeUsuario;
    private String nomeEmpresa;
    private Modulo modulo;

    public FuncionalidadeSistemaEnum getFuncionalidadeSistema() {
        return funcionalidadeSistema;
    }

    public void setFuncionalidadeSistema(FuncionalidadeSistemaEnum funcionalidadeSistema) {
        this.funcionalidadeSistema = funcionalidadeSistema;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getNomeUsuario() {
        return nomeUsuario;
    }

    public void setNomeUsuario(String nomeUsuario) {
        this.nomeUsuario = nomeUsuario;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Modulo getModulo() {
        return modulo;
    }

    public void setModulo(Modulo modulo) {
        this.modulo = modulo;
    }

    public String getIdentificadorMenu() {
        return identificadorMenu;
    }

    public void setIdentificadorMenu(String identificadorMenu) {
        this.identificadorMenu = identificadorMenu;
    }
}

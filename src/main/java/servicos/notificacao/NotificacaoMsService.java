package servicos.notificacao;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.Modulo;
import com.google.gson.Gson;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import servicos.discovery.DiscoveryMsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

public class NotificacaoMsService {
    private static String url;

    public static String getUrl() throws Exception {
        if(url == null){
            try {
                url = DiscoveryMsService.urls().getServiceUrls().getNotificacaoMs();
            }catch (Exception e){
                Uteis.logarDebug("Falha ao obter url do serviço de notificação do discovery "+e.getMessage());
                throw e;
            }

        }

        return url;
    }

    public static void notificarAcessoMenu(RecursoEmpresaMenu recursoEmpresaMenu) {
        if (Uteis.isAmbienteDesenvolvimentoTeste()) {
            return;
        }
        RecursoEmpresaDTO recursoEmpresaDTO = new RecursoEmpresaDTO();
        recursoEmpresaDTO.setData(Calendario.getData("dd/MM/yyyy - HH:mm:ss"));
        recursoEmpresaDTO.setRecurso("MENU-"+recursoEmpresaMenu.getIdentificadorMenu().replace("_", "-"));
        recursoEmpresaDTO.setEmpresa(recursoEmpresaMenu.getCodigoEmpresa());
        recursoEmpresaDTO.setChave(recursoEmpresaMenu.getChave());
        recursoEmpresaDTO.setUsuario(recursoEmpresaMenu.getNomeUsuario());
        recursoEmpresaDTO.setNomeEmpresa(recursoEmpresaMenu.getNomeEmpresa());
        recursoEmpresaDTO.setZw(recursoEmpresaMenu.getModulo().equals(Modulo.ZILLYON_WEB));
        recursoEmpresaDTO.setCrm(recursoEmpresaMenu.getModulo().equals(Modulo.CUSTOMER_RELATIONSHIP_MANAGEMENT));
        recursoEmpresaDTO.setFin(recursoEmpresaMenu.getModulo().equals(Modulo.FINANCEIRO));
        recursoEmpresaDTO.setTr(recursoEmpresaMenu.getModulo().equals(Modulo.TREINO) || recursoEmpresaMenu.getModulo().equals(Modulo.NOVO_TREINO));
        recursoEmpresaDTO.setSlc(recursoEmpresaMenu.getModulo().equals(Modulo.AULA_CHEIA));
        recursoEmpresaDTO.setCe(recursoEmpresaMenu.getModulo().equals(Modulo.CENTRAL_DE_EVENTOS));
        recursoEmpresaDTO.setCe(recursoEmpresaMenu.getModulo().equals(Modulo.STUDIO));

        Gson json = new Gson();
        String body = json.toJson(recursoEmpresaDTO);

        try {
            CompletableFuture.runAsync(() -> {
                try {
                    String response = ExecuteRequestHttpService.post(getUrl() + "/prest/recursoEmpresa/notificar", body, new HashMap<>());
                    Uteis.logar("Notificação de acesso ao menu enviada com sucesso: "+response);
                } catch (Exception e) {
                    if(e instanceof java.net.ConnectException){
                        NotificacaoMsService.url = null;
                    }
                    Uteis.logarDebug("Erro ao notificar acesso ao menu:"+e.getMessage()+" body: "+body);
                }
            });
        } catch (Exception e) {
            Uteis.logarDebug("Erro ao montar notificação de acesso ao menu:"+e.getMessage());
        }
    }


    public static void notificarAcessoMenuRecurso(String idRecurso, IdLocalicazaoMenuEnum idLocalicazaoMenuEnum) {
        try{
            RecursoEmpresaMenu recursoEmpresaMenu = new RecursoEmpresaMenu();
            recursoEmpresaMenu.setIdentificadorMenu(idLocalicazaoMenuEnum.name()+"-"+idRecurso);
            recursoEmpresaMenu.setCodigoEmpresa(getCodigoEmpresaLogado());
            recursoEmpresaMenu.setChave(getChave());
            recursoEmpresaMenu.setNomeUsuario(getNomeUsuarioLogado());
            recursoEmpresaMenu.setNomeEmpresa(getNomeEmpresaLogado());
            recursoEmpresaMenu.setModulo(getModulo());

            notificarAcessoMenu(recursoEmpresaMenu);
        }catch (Exception e){
            Uteis.logarDebug("Erro ao criar notificação de acesso ao menu de id "+idRecurso);
            e.printStackTrace();
        }
    }

    public static void notificarAcessoMenuModulo(Modulo modulo, IdLocalicazaoMenuEnum idLocalicazaoMenuEnum) {
        try{
            RecursoEmpresaMenu recursoEmpresaMenu = new RecursoEmpresaMenu();
            recursoEmpresaMenu.setIdentificadorMenu(idLocalicazaoMenuEnum.name()+"-"+modulo.getSiglaModulo());
            recursoEmpresaMenu.setCodigoEmpresa(getCodigoEmpresaLogado());
            recursoEmpresaMenu.setChave(getChave());
            recursoEmpresaMenu.setNomeUsuario(getNomeUsuarioLogado());
            recursoEmpresaMenu.setNomeEmpresa(getNomeEmpresaLogado());
            recursoEmpresaMenu.setModulo(getModulo());

            notificarAcessoMenu(recursoEmpresaMenu);
        }catch (Exception e){
            Uteis.logarDebug("Erro ao criar notificação de acesso ao menu do modulo "+modulo.getSiglaModulo());
            e.printStackTrace();
        }
    }

    public static void notificarAcessoMenuRecurso(FuncionalidadeSistemaEnum funcionalidadeSistemaEnum, IdLocalicazaoMenuEnum idLocalicazaoMenuEnum) {
        try{
            RecursoEmpresaMenu recursoEmpresaMenu = new RecursoEmpresaMenu();
            recursoEmpresaMenu.setIdentificadorMenu(idLocalicazaoMenuEnum.name()+"-"+funcionalidadeSistemaEnum.getName());
            recursoEmpresaMenu.setCodigoEmpresa(getCodigoEmpresaLogado());
            recursoEmpresaMenu.setChave(getChave());
            recursoEmpresaMenu.setNomeUsuario(getNomeUsuarioLogado());
            recursoEmpresaMenu.setNomeEmpresa(getNomeEmpresaLogado());
            recursoEmpresaMenu.setModulo(getModulo());

            notificarAcessoMenu(recursoEmpresaMenu);
        }catch (Exception e){
            Uteis.logarDebug("Erro ao criar notificação de acesso ao menu do recurso "+funcionalidadeSistemaEnum.getName());
            e.printStackTrace();
        }
    }

    private static String getChave() {
        return (String) JSFUtilities.getFromSession("key");
    }

    private static Modulo getModulo() {
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if(loginControle != null){
            try {
                return loginControle.getModulo();
            } catch (Exception e) {
                Uteis.logarDebug("Não foi possível recuperar o modulo atual ao notificar acesso ao menu");
            }
        }

        return null;
    }

    private static String getNomeUsuarioLogado(){
        UsuarioVO usuarioVO = getUsuarioLogado();
        if(Objects.nonNull(usuarioVO)){
            return usuarioVO.getNome();
        }
        return null;
    }

    private static Integer getCodigoUsuarioLogado(){
        UsuarioVO usuarioVO = getUsuarioLogado();
        if(Objects.nonNull(usuarioVO)){
            return usuarioVO.getCodigo();
        }
        return null;
    }

    private static UsuarioVO getUsuarioLogado() {
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if(loginControle != null){
            try {
                return loginControle.getUsuarioLogado();
            } catch (Exception e) {
                Uteis.logarDebug("Não foi possível recuperar o usuário logado ao notificar acesso ao menu");
            }
        }

        return null;
    }

    private static String getNomeEmpresaLogado(){
        EmpresaVO empresaVO = getEmpresaLogado();
        if(Objects.nonNull(empresaVO)){
            return empresaVO.getNome();
        }
        return null;
    }

    private static Integer getCodigoEmpresaLogado(){
        EmpresaVO empresaVO = getEmpresaLogado();
        if(Objects.nonNull(empresaVO)){
            return empresaVO.getCodigo();
        }
        return null;
    }

    private static EmpresaVO getEmpresaLogado() {
        LoginControle loginControle = JSFUtilities.getControlador(LoginControle.class);
        if(loginControle != null){
            try {
                return loginControle.getEmpresaLogado();
            } catch (Exception e) {
                Uteis.logarDebug("Não foi possível recuperar a empresa logada ao notificar acesso ao menu");
            }
        }

        return null;
    }
}

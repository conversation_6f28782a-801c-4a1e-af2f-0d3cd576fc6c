/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.jms.zaw.beans;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;

import javax.ejb.ActivationConfigProperty;
import javax.ejb.MessageDriven;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
//@MessageDriven(mappedName = "JMS_ZAW_Queue", activationConfig = {
//    @ActivationConfigProperty(propertyName = "acknowledgeMode", propertyValue = "Auto-acknowledge"),
//    @ActivationConfigProperty(propertyName = "destinationType", propertyValue = "javax.jms.Queue")
//})
public class MessageBean implements MessageListener {

    public MessageBean() {
    }

    @Override
    public void onMessage(Message message) {
        AcessoControle acessoControle = null;
        try {
            Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
            ObjectMessage objMessage = (ObjectMessage) message;
            RegistroAcessoJMSBean reg = (RegistroAcessoJMSBean) objMessage.getObject();
            Logger.getLogger(MessageBean.class.getSimpleName()).log(Level.INFO, "ZAWQueue (OnMessage) - Nova mensagem: {0}", reg.toString());
            acessoControle = DaoAuxiliar.retornarAcessoControle(reg.getKey());
            acessoControle.registrarAcesso(reg.getDataAcesso(),
                    reg.getTipo(), reg.getCodigo(),
                    reg.getSituacao(), reg.getDirecao(),
                    reg.getLocal(),
                    reg.getTerminal(), reg.getUsuario(),
                        reg.getMeioIdentificacao(), reg.getKey(), reg.getEmpresa(), "", null, null);
            Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
            long dif = d2.getTime() - d1.getTime();
            Logger.getLogger(MessageBean.class.getSimpleName()).log(Level.INFO,
                    "ZAWQueue (Registrado) - Cartao: {0} - Chave: {1} - Empresa: {2} - Local: {3} "
                    + "- Terminal: {4} - Direção: {5} "
                    + "- Meio Ident: {6} - SituacaoAcesso: {7} {8}ms",
                    new Object[]{
                reg.getCodigo(),
                reg.getKey(),
                reg.getEmpresa(),
                reg.getLocal(),
                reg.getTerminal(),
                reg.getDirecao(),
                reg.getMeioIdentificacao(),
                reg.getSituacao(),
                String.valueOf(dif)});
        } catch (Exception e) {
            System.out.println("ZAWQueue - ERRO Recebendo Mensagem:  " + e.getMessage());
        } finally {
//            
        }
    }
}

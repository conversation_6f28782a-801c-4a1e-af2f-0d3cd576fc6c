/*
 * To change this template; choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.jms.zaw.beans;

import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RegistroAcessoJMSBean implements Serializable {

    private static final long serialVersionUID = -6999797435857606870L;
    private String key;
    private Date dataAcesso;
    private String tipo;
    private Integer codigo;
    private Integer empresa;
    private Integer local;
    private Integer terminal;
    private DirecaoAcessoEnum direcao;
    private SituacaoAcessoEnum situacao;
    private Integer usuario;
    private MeioIdentificacaoEnum meioIdentificacao;

    public RegistroAcessoJMSBean() {
    }

    public RegistroAcessoJMSBean(String key,
            Date dataAcesso, String tipo, Integer codigo,
            Integer empresa, Integer local, Integer terminal,
            DirecaoAcessoEnum direcao, SituacaoAcessoEnum situacao, Integer usuario,
            MeioIdentificacaoEnum meioIdentificacao) {
        this.key = key;
        this.dataAcesso = dataAcesso;
        this.tipo = tipo;
        this.codigo = codigo;
        this.empresa = empresa;
        this.local = local;
        this.terminal = terminal;
        this.direcao = direcao;
        this.situacao = situacao;
        this.usuario = usuario;
        this.meioIdentificacao = meioIdentificacao;
    }

    @Override
    public String toString() {
        return String.format("Chave: %s data: %s tipo: %s codigo: %s empresa: %s local: %s terminal: %s direcao: %s situacao: %s usuario: %s meio: %s",
                key, dataAcesso, tipo,
                codigo, empresa, local,
                terminal, direcao, situacao,
                usuario, meioIdentificacao);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }

    public DirecaoAcessoEnum getDirecao() {
        return direcao;
    }

    public void setDirecao(DirecaoAcessoEnum direcao) {
        this.direcao = direcao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getLocal() {
        return local;
    }

    public void setLocal(Integer local) {
        this.local = local;
    }

    public MeioIdentificacaoEnum getMeioIdentificacao() {
        return meioIdentificacao;
    }

    public void setMeioIdentificacao(MeioIdentificacaoEnum meioIdentificacao) {
        this.meioIdentificacao = meioIdentificacao;
    }

    public SituacaoAcessoEnum getSituacao() {
        return situacao;
    }

    public void setSituacao(SituacaoAcessoEnum situacao) {
        this.situacao = situacao;
    }

    public Integer getTerminal() {
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }

}

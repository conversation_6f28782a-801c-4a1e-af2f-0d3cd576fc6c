package servicos;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import servlet.appGestor.Interface.MSAlunoService;
import servlet.appGestor.appGestorDados.AlunoSimplesJSON;
import servlet.appGestor.appGestorDados.VendaDescontoJSON;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;


public class AppGestorAlunoService implements MSAlunoService, AutoCloseable {

    private Connection con;

    public AppGestorAlunoService(Connection con) {
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    @Override
    public List<AlunoSimplesJSON> obterContratosCanceladosSql(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  FROM contratooperacao co, usuario u, contrato c, cliente cli, pessoa p ");
            sql.append("WHERE tipooperacao LIKE 'CA' ");
            sql.append("AND dataoperacao BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND '" + Uteis.getDataJDBC(fim) + " 23:59:59' ");
            sql.append("AND u.codigo = co.responsavel ");
            sql.append("AND c.codigo = co.contrato ");
            sql.append("AND c.pessoa = cli.pessoa ");
            sql.append("AND c.pessoa = p.codigo ");
            sql.append("AND c.empresa = ").append(empresa);

            lista = getListaAlunoBi(sql.toString(), paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> obterClientesComBonusSql(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT co.codigo, cli.matricula, p.nome, p.fotokey, cli.situacao  FROM contratooperacao co ");
            sql.append("left join usuario as us on us.codigo = co.responsavel ");
            sql.append("inner join contrato as cont on cont.codigo = co.contrato ");
            sql.append("inner join cliente as cli on cli.pessoa = cont.pessoa ");
            sql.append("inner join pessoa as p on cli.pessoa = p.codigo ");
            sql.append("inner join justificativaoperacao jo on co.tipojustificativa = jo.codigo ");
            sql.append("where (co.tipooperacao LIKE 'BA' or co.tipooperacao like 'BR') and cont.empresa = " + empresa + "  and ");
            sql.append("(('" + Uteis.getDataJDBC(fim) + "' >= co.datainicioefetivacaooperacao and '" + Uteis.getDataJDBC(fim) + "' <= co.datafimefetivacaooperacao ) ");
            //se for o mês corrente deve ser considerado bonus que iniciam no futuro
            if (Uteis.getMesData(fim) == Uteis.getMesData(Calendario.hoje())) {
                sql.append(" or co.datainicioefetivacaooperacao >= '" + Uteis.getDataJDBC(fim) + "')");
            } else {
                sql.append(" )");
            }

            sql.append(" AND us.colaborador is not null ");
            lista = getListaAlunoBi(sql.toString(), paginadorDTO, filtro, "p.nome", "cli.matricula");

        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> obterClientesComFreePassSql(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  ");
            sql.append(" FROM cliente cli\n");
            sql.append("  inner join pessoa p on p.codigo = cli.pessoa\n");
            sql.append("  inner join periodoacessocliente pa on pa.pessoa = cli.pessoa\n");
            sql.append("  LEFT JOIN usuario us\n");
            sql.append("    ON pa.responsavel = us.codigo\n");
            sql.append(" WHERE 1 = 1 \n");
            sql.append(" AND coalesce(pa.tokengympass, '') = '' \n");
            sql.append(" AND pa.tipoacesso = 'PL'\n");
            sql.append("     AND '").append(Uteis.getDataJDBC(fim)).append("' BETWEEN pa.datainicioacesso  AND pa.datafinalacesso\n");
            sql.append("      AND cli.empresa = ").append(empresa).append("\n");

            lista = getListaAlunoBi(sql.toString(), paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> consultarInativosPeriodoAcesso(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            String sqlStr = "select distinct cli.matricula, p.nome, p.fotokey, cli.situacao  from historicocontrato as ht"
                    + " inner join contrato on contrato.codigo = ht.contrato"
                    + " inner join pessoa p on p.codigo = contrato.pessoa "
                    + " inner join cliente cli on cli.pessoa = p.codigo"
                    + " inner join periodoacessocliente as per on per.contrato = ht.contrato "
                    + " where (ht.tipohistorico = 'VE' or ht.tipohistorico = 'DE' or ht.tipohistorico = 'CA')"
                    + " and ht.datainiciosituacao <= '" + Uteis.getDataJDBC(fim) + "' "
                    + " and (per.datainicioacesso <= '" + Uteis.getDataJDBC(fim) + "' and per.datafinalacesso>='" + Uteis.getDataJDBC(fim) + "')"
                    + " and contrato.empresa = " + empresa;

            lista = getListaAlunoBi(sqlStr, paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> consultarOperacoesContratoRetroativas(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, Boolean distinct, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            String distinctStr = distinct ? "DISTINCT" : "";
            String sqlStr = "SELECT " + distinctStr + "cli.matricula, p.nome, p.fotokey, cli.situacao "
                    + " FROM Cliente cli"
                    + " inner join contrato  on contrato.pessoa = cli.pessoa"
                    + " inner join pessoa p  on contrato.pessoa = p.codigo"
                    + " inner join contratooperacao con on con.contrato = contrato.codigo  ";
                    if(distinct) {
                        sqlStr = sqlStr + " inner join justificativaoperacao jo on con.tipojustificativa = jo.codigo ";
                    } else {
                        sqlStr = sqlStr + " left join justificativaoperacao jo on con.tipojustificativa = jo.codigo ";
                    }
                    sqlStr = sqlStr + " where dataoperacao between '" + Uteis.getDataJDBC(inicio) + "' and '" + Uteis.getDataJDBC(fim) + "'"
                    + " and dataoperacao > datainicioefetivacaooperacao"
                    + " and cli.empresa  = " + empresa;

            lista = getListaAlunoBi(sqlStr, paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade,
                                                                                   java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, String operacao,
                                                                                   String nomeEmpresa, boolean buscarComAdministrador, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT pessoa as matricula, coalesce(p.nome, responsavelalteracao) as nome, p.fotokey FROM Log\n");
            sql.append(" left JOIN pessoa p on p.codigo = pessoa ");
            sql.append("WHERE nomeEntidade like '").append(nomeEntidade.toUpperCase()).append("' ");
            sql.append("AND dataAlteracao between '").append(dataAlteracaoInicial).append(" 00:00:00' AND '").append(dataAlteracaoFim).append(" 23:59:59'\n");
            sql.append("AND operacao LIKE '").append(operacao.toUpperCase()).append("' ");

            if (!buscarComAdministrador) {
                sql.append(" and (responsavelalteracao  = 'ADMINISTRADOR' )");
            }
            if (!nomeEmpresa.isEmpty()) {
                sql.append("AND valorcampoalterado iLIKE '%Empresa = ").append(nomeEmpresa).append("%'\n");
            }

            lista = getListaAlunoBi(sql.toString(), paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(java.sql.Date inicio, java.sql.Date fim,
                                                                                      Integer empresa, boolean buscarComADM, boolean buscarComRecorrencia, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            int qtde = 0;
            String sqlStr = "SELECT s.matricula, s.nomecliente as nome, p.fotokey FROM estornoobservacao e " +
                    " left join situacaoclientesinteticodw s on s.codigopessoa = e.pessoa" +
                    " left join pessoa p on p.codigo = e.pessoa " +
                    " WHERE dataestorno between '" + inicio + " 00:00:00' and '"
                    + fim + " 23:59:59'";
            sqlStr += " and " + ((!buscarComADM) ? "not" : "") + " (usuarioresponsavel ilike 'ADMINISTRADOR' )";
            sqlStr += " and " + ((!buscarComRecorrencia) ? "not" : "") + " (usuarioresponsavel ilike 'RECOR%' )";

            sqlStr += (qtde > 0 ? ")" : "");

            if (empresa != null && empresa > 0) {
                sqlStr += " and empresa = " + empresa + "";
            }

            lista = getListaAlunoBi(sqlStr, paginadorDTO, filtro, "s.nomecliente", "s.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    private String getPaginador(PaginadorDTO paginadorDTO, String sqlStr, String orderByPadrao) throws Exception {
        StringBuilder sqlStringBuilder = new StringBuilder(sqlStr);
        processarPaginador(sqlStringBuilder, orderByPadrao, paginadorDTO);
        sqlStr = sqlStringBuilder.toString();
        return sqlStr;
    }

    private Integer obterCount(StringBuilder sql, String count) throws Exception {
        StringBuilder sqlCount = new StringBuilder();
        sqlCount.append("select ");
        if (count != null && count.length() > 0) {
            sqlCount.append(count).append("\n");
        } else {
            sqlCount.append("count(*) \n");
        }
        sqlCount.append("as total from ( \n");
        sqlCount.append(sql).append(" \n");
        sqlCount.append(") as sql ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlCount.toString())) {
                if (rs.next()) {
                    return rs.getInt("total");
                } else {
                    return 0;
                }
            }
        }
    }

    public void processarPaginador(StringBuilder sql, String orderByDefault, PaginadorDTO paginadorDTO) throws Exception {
        if (paginadorDTO != null) {

            int maxResults = paginadorDTO.getSize().intValue();
            int indiceInicial = paginadorDTO.getPage().intValue() * maxResults;

            paginadorDTO.setQuantidadeTotalElementos(obterCount(sql, null).longValue());
            paginadorDTO.setSize((long) maxResults);

            //adicionar ordenação
            if (!UteisValidacao.emptyString(orderByDefault)
                    && paginadorDTO.getSQLOrderByUse() != null
                    && UteisValidacao.emptyString(paginadorDTO.getSQLOrderByUse().trim())) {
                sql.append(" ORDER BY ").append(orderByDefault).append(" \n");
            } else {
                sql.append(paginadorDTO.getSQLOrderByUse());
            }

            //adicionar limit
            sql.append(paginadorDTO.getSQLLimitByUse());

            //adicionar offset
            sql.append(" OFFSET " + indiceInicial + "\n");
        }
    }

    public List<AlunoSimplesJSON> consultaPorNomeEntidadePorDataAlteracaoPorOperacao(
            Date dataAlteracaoInicial, Date dataAlteracaoFim,
            Integer codEmpresa, boolean buscarComAdministrador, PaginadorDTO paginadorDTO, String filtro) throws Exception {

        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            int qtde = 0;
            String sqlStr = "SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  FROM observacaooperacao\n";
            sqlStr += "INNER JOIN movparcela ON movparcela.codigo = observacaooperacao.movparcela\n";
            sqlStr += "inner join pessoa p on p.codigo = movparcela.pessoa\n";
            sqlStr += "left join cliente cli on p.codigo = cli.pessoa\n";
            sqlStr += "WHERE dataoperacao between '" + Uteis.getDataJDBC(dataAlteracaoInicial) + " 00:00:00' and '" + Uteis.getDataJDBC(dataAlteracaoFim)+ " 23:59:59'";
            sqlStr += " and " + ((!buscarComAdministrador) ? "not" : "") + " (usuarioresponsavel ilike 'ADMINISTRADOR' )";
            sqlStr += "AND tipooperacao  = 'PC'\n";

            if (codEmpresa != null && codEmpresa > 0) {
                sqlStr += " and movparcela.empresa = " + codEmpresa + "";
            }

            lista = getListaAlunoBi(sqlStr, paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> obterClientesComAutorizacaoSemRenovacaoAutomatica(Date dataBase, Integer empresa, PaginadorDTO paginadorDTO, Boolean comAutorizacaoNaoRenovavel, Boolean comAutorizacaoContratoRenovavel, String filtro) throws Exception {
        Date dataInicio = Uteis.obterPrimeiroDiaMes(dataBase);
        Date dataFim = Uteis.obterUltimoDiaMes(dataBase);

        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();

        String sql = "select * from (select\n" +
                "                   distinct\n" +
                "                           p.nome,\n" +
                "                           cli.matricula, cli.situacao, p.fotokey, \n" +
                "                           CASE WHEN exists(select codigo from autorizacaocobrancacliente  where tipoautorizacao  = 1 and cliente = cli.codigo and ativa = true)\n" +
                "                                    THEN true\n" +
                "                                ELSE false END AS temAutorizacaoCobranca,\n" +
                "                           CASE WHEN pl.recorrencia THEN ((plrec.renovavelautomaticamente OR crec.renovavelautomaticamente) AND c.permiterenovacaoautomatica)\n" +
                "                                ELSE  ((pl.renovavelautomaticamente or c.renovavelautomaticamente) AND c.permiterenovacaoautomatica) END   AS contratoRenovavel\n" +
                "               FROM contrato c\n" +
                "                        INNER JOIN pessoa p on p.codigo = c.pessoa\n" +
                "                        INNER JOIN cliente cli on cli.pessoa = p.codigo\n" +
                "                        INNER JOIN situacaoclientesinteticodw  dw on dw.codigocliente = cli.codigo\n" +
                "                        INNER JOIN empresa e on e.codigo = c.empresa\n" +
                "                        INNER JOIN plano pl on pl.codigo = c.plano\n" +
                "                        LEFT JOIN  planorecorrencia plrec on plrec.plano = pl.codigo\n" +
                "                        LEFT JOIN  contratorecorrencia crec on crec.contrato = c.codigo\n" +
                "                        INNER JOIN contratoduracao cdu on cdu.contrato = c.codigo\n" +
                "                        LEFT JOIN vinculo v ON cli.codigo = v.cliente\n" +
                "               WHERE 1 = 1\n" +
                "                 AND c.situacao in('AT','IN')\n" +
                "                 AND cli.empresa = " + empresa + "\n" +
                "                 AND c.vigenciaateajustada::date between ' "+ Uteis.getData(dataInicio) +"' and '"+ Uteis.getData(dataFim) + "'\n" +
                "              ) as foo\n" +
                "WHERE 1 = 1\n";


        if (comAutorizacaoNaoRenovavel) {
            sql = sql + " AND temAutorizacaoCobranca = true \n";
            sql = sql + " AND contratoRenovavel = false \n";
        }

        if (comAutorizacaoContratoRenovavel) {
            sql = sql + " AND temAutorizacaoCobranca = true \n";
            sql = sql + " AND contratoRenovavel = true \n";
        }
        lista = getListaAlunoBi(sql, paginadorDTO, filtro, "nome", "matricula");

        return lista;
    }

    @Override
    public List<VendaDescontoJSON> obterValorDescontos(Date dataInicio, Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<VendaDescontoJSON> lista = new ArrayList<>();

        String sql = "select descricao,\n" +
                "       usuario,\n" +
                "       matricula,\n" +
                "       nome,\n" +
                "       contrato,\n" +
                "       datalancamento,\n" +
                "       sum(desconto) as valor,\n" +
                "       convenio,\n" +
                "       moeda\n" +
                "from (select emp.moeda,\n" +
                "             pro.descricao,\n" +
                "             u.nome                                                                      AS usuario,\n" +
                "             cli.matricula,\n" +
                "             p.nome                                                                    AS nome,\n" +
                "             mp.contrato,\n" +
                "             mp.datalancamento::date,\n" +
                "             CASE when pro.tipoproduto = 'DE' THEN mp.totalfinal else mp.valordesconto END AS desconto,\n" +
                "             c2.descricao                                                                as convenio\n" +
                "      from movproduto mp\n" +
                "               INNER JOIN produto pro ON mp.produto = pro.codigo\n" +
                "               INNER JOIN usuario u ON mp.responsavellancamento = u.codigo\n" +
                "               INNER JOIN cliente cli ON mp.pessoa = cli.pessoa\n" +
                "               INNER JOIN pessoa p ON p.codigo = mp.pessoa\n" +
                "               INNER JOIN empresa emp ON emp.codigo = mp.empresa\n" +
                "               LEFT JOIN contrato con ON mp.contrato = con.codigo\n" +
                "               LEFT JOIN conveniodesconto c2 ON con.conveniodesconto = c2.codigo\n" +
                "               LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato\n" +
                "               LEFT JOIN contratoduracaocreditotreino cdct ON cd.codigo = cdct.contratoduracao\n" +
                "     where mp.datalancamento::date BETWEEN ' " + Uteis.getSQLData(dataInicio) + "' AND '" + Uteis.getSQLData(dataFim) + "'\n" +
                "        and p.codigo is not null\n" +
                "        and ((mp.valordesconto > 0 and (c2.codigo is not null OR mp.contrato is null)) OR\n" +
                "             (pro.tipoproduto = 'DE' and c2.codigo is null))\n" +
                "        and mp.empresa = 1 union all\n" +
                "      select\n" +
                "          '' as moeda,\n" +
                "          pro.descricao,\n" +
                "          u.nome as usuario,\n" +
                "          '' as matricula,\n" +
                "          'CONSUMIDOR' as cliente,\n" +
                "          mp.contrato,\n" +
                "          mp.datalancamento::date,\n" +
                "          mp.valordesconto,\n" +
                "          '' as convenio\n" +
                "      from\n" +
                "          movproduto mp\n" +
                "          inner join produto pro\n" +
                "      on\n" +
                "          mp.produto = pro.codigo\n" +
                "          inner join usuario u on\n" +
                "          mp.responsavellancamento = u.codigo\n" +
                "      where\n" +
                "          mp.datalancamento BETWEEN '" + Uteis.getSQLData(dataInicio) + "'\n" +
                "        AND '" + Uteis.getSQLData(dataFim) + "'\n" +
                "        and mp.valordesconto\n" +
                "          > 0\n" +
                "        and mp.pessoa is null\n" +
                "        and mp.empresa = 1) as query\n";
                if(!UteisValidacao.emptyString(filtro)) {
                    sql = sql + "where (UPPER( nome) like UPPER('%" + filtro + "%') or UPPER(matricula) like '%" + filtro + "%')";
                }
                sql = sql + " group by 1, 2, 3, 4, 5, 6, 8, 9\n";

        lista = getListValorDesconto(sql, paginadorDTO, "nome");

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> obterAlunosExcluidos(Date dataInicio, Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, String nomeEntidade, String valorCampoAlterado, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();

        String sql = "SELECT * \n" +
                "FROM Log \n" +
                "WHERE nomeEntidade ilike '"+ nomeEntidade +"'\n" +
                "  and dataalteracao between '" + Uteis.getSQLData(dataInicio) + " 00:00:00' and '" + Uteis.getSQLData(dataFim) + " 23:59:59'\n" +
                "  and operacao ilike 'EXCLUSÃO'";
        if(!UteisValidacao.emptyString(valorCampoAlterado)) {
            sql = sql + " and valorcampoalterado ilike '%" + valorCampoAlterado + "%'";
        }
        if(!UteisValidacao.emptyString(filtro)) {
            sql = sql + "AND (SUBSTRING(valorcampoanterior FROM 'Nome = (.*)') ILIKE '%" + filtro.toUpperCase() + "%' or (SUBSTRING(valorcampoalterado FROM ' - Matricula : (.*)') ILIKE '%" + filtro.toUpperCase() + "%'))";
        }

        lista = getListaAlunoExcluidosBi(sql, paginadorDTO);

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> obterRenegociacaoParcelas(Date dataInicio, Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, String filtro, String nomeEmpresa) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();

        String sql = "SELECT *\n" +
                "FROM log\n" +
                "         LEFT JOIN cliente cli ON cli.codigo = log.cliente\n" +
                "         LEFT JOIN pessoa p ON p.codigo = cli.pessoa\n" +
                "WHERE nomeentidade = 'PARCELA'\n" +
                "  and operacao = 'RENEGOCIAÇÃO - PARCELA'\n" +
                "  and dataalteracao between '" + Uteis.getSQLData(dataInicio) + " 00:00:00' and '" + Uteis.getSQLData(dataFim) + " 23:59:59'\n";
        if(!UteisValidacao.emptyString(nomeEmpresa)) {
            sql = sql + " and valorcampoalterado ilike '%Empresa = " + nomeEmpresa + "%'";
        }
        lista = getListaAlunoBi(sql, paginadorDTO, filtro, "p.nome", "cli.matricula");

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> contarContratoAlteracaoManual(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            String sql = " SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  FROM contrato " +
                    "inner  JOIN pessoa p on p.codigo = contrato.pessoa  " +
                    "inner  JOIN cliente cli on p.codigo = cli.pessoa  " +
                    "LEFT JOIN usuario usu ON contrato.responsavelcontrato = usu.codigo  ";
            sql += "WHERE dataalteracaomanual BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND  '" + Uteis.getDataJDBC(fim) + " 23:59:59'";
            sql += " AND contrato.empresa = " + empresa;

            lista = getListaAlunoBi(sql, paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> contarPagamentoAlteracaoManual(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            String sql = " SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  FROM movpagamento   " +
                    "inner  JOIN pessoa p on p.codigo = movpagamento.pessoa  " +
                    "inner  JOIN cliente cli on p.codigo = cli.pessoa  " +
                    "WHERE movpagamento.dataalteracaomanual ";
            sql += "BETWEEN '" + Uteis.getDataJDBC(inicio) + " 00:00:00' AND  '" + Uteis.getDataJDBC(fim) + " 23:59:59'";
            sql += " AND movpagamento.empresa = " + empresa;

            lista = getListaAlunoBi(sql, paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade, Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            String sqlStr = "SELECT distinct chaveprimaria, cli.matricula, p.nome, p.fotokey, cli.situacao  FROM Log "
                    + " inner join pessoa p on p.codigo= log.pessoa "
                    + " inner join cliente cli on cli.pessoa= log.pessoa "
                    + " WHERE UPPER(nomeEntidade) like '" + nomeEntidade.toUpperCase() + "' "
                    + "and dataalteracao between '" + inicio + " 00:00:00' and '"
                    + fim + " 23:59:59' ";

            if (empresa > 0) {
                sqlStr += " and cli.empresa =" + empresa;
            }

            lista = getListaAlunoBi(sqlStr, paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public List<AlunoSimplesJSON> obterClientesComGymPassSql(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  ");
            sql.append("FROM cliente cli\n");
            sql.append("  inner join pessoa p on p.codigo = cli.pessoa\n");
            sql.append("  inner join periodoacessocliente pa on pa.pessoa = cli.pessoa\n");
            sql.append("  LEFT JOIN usuario us\n");
            sql.append("    ON pa.responsavel = us.codigo\n");
            sql.append(" WHERE 1 = 1 \n");
            sql.append(" AND coalesce(pa.tokengympass, '') <> '' \n");
            sql.append(" AND pa.tipoacesso = 'PL'\n");
            sql.append("     AND '").append(Uteis.getDataJDBC(fim)).append("' BETWEEN pa.datainicioacesso  AND pa.datafinalacesso\n");
            sql.append("      AND cli.empresa = ").append(empresa).append("\n");

            lista = getListaAlunoBi(sql.toString(), paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    @Override
    public String nomeEmpresa(Integer codigoEmpresa) throws Exception {

        String nomeEmpresa = null;

        StringBuilder sql = new StringBuilder();
        sql.append("select nome from empresa where codigo = " + codigoEmpresa);
        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), this.con)) {

            while (rs.next()) {
                nomeEmpresa = rs.getString("nome");

            }
        }
        return nomeEmpresa;
    }

    @Override
    public List<AlunoSimplesJSON> obterClientesBolsa(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception {
        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();
        try {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT cli.matricula, p.nome, p.fotokey, cli.situacao  FROM contrato c");
            sql.append(" INNER JOIN pessoa p ON p.codigo = c.pessoa");
            sql.append(" INNER JOIN cliente cli ON cli.pessoa = p.codigo");
            sql.append(" INNER JOIN usuario u ON u.codigo = c.responsavelcontrato");
            sql.append(" WHERE c.bolsa");
            sql.append(" AND '").append(Uteis.getDataJDBC(fim)).append(" 00:00:00' BETWEEN c.vigenciade AND c.vigenciaateajustada");
            sql.append(" AND c.empresa = " + empresa + " ");

            lista = getListaAlunoBi(sql.toString(), paginadorDTO, filtro, "p.nome", "cli.matricula");
        } catch (Exception ex) {
            Logger.getLogger(AppGestorAlunoService.class.getName()).log(Level.SEVERE, null, ex);
        }

        return lista;
    }

    public List<AlunoSimplesJSON> getListaAlunoBi(String sqlStr, PaginadorDTO paginadorDTO, String filtro, String colunaNome, String colunaMatricula) throws Exception {

        if(!UteisValidacao.emptyString(filtro)) {
            sqlStr += " and (UPPER( " + colunaNome + ") like '%" + filtro.toUpperCase() + "%' or CAST( " + colunaMatricula + " as text) like '%" + filtro.toUpperCase() + "%') ";
        }

        if(paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            sqlStr = getPaginador(paginadorDTO, sqlStr, " "+ colunaNome +" asc ");
        } else {
            sqlStr += " order by "+ colunaNome +" asc ";
        }

        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlStr, this.con)) {

            while (rs.next()) {
                String nome = UteisValidacao.emptyString(rs.getString("nome")) ? "Aluno excluido" : rs.getString("nome");
                AlunoSimplesJSON alunoSimplesJSON = new AlunoSimplesJSON(
                        rs.getString("matricula"),
                        nome);
                if(Uteis.resultSetContemColuna(rs, "fotokey")) {
                    alunoSimplesJSON.setFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey") != null ? rs.getString("fotokey") : ""));
                }
                if(Uteis.resultSetContemColuna(rs, "situacao")) {
                    alunoSimplesJSON.setSituacaoCliente(rs.getString("situacao") != null ? SituacaoClienteEnum.getSituacaoCliente(rs.getString("situacao")).getDescricao() : "");
                }
                lista.add(alunoSimplesJSON);
            }
        }
        return lista;
    }

    public List<VendaDescontoJSON> getListValorDesconto(String sqlStr, PaginadorDTO paginadorDTO, String colunaNome) throws Exception {

        if(paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            sqlStr = getPaginador(paginadorDTO, sqlStr, " "+ colunaNome +" asc ");
        } else {
            sqlStr += " order by "+ colunaNome +" asc ";
        }

        List<VendaDescontoJSON> lista = new ArrayList<>();

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlStr, this.con)) {

            while (rs.next()) {
                String nome = UteisValidacao.emptyString(rs.getString("nome")) ? "Aluno excluido" : rs.getString("nome");
                VendaDescontoJSON vendaDescontoJSON = new VendaDescontoJSON();
                vendaDescontoJSON.setMatricula(rs.getString("matricula"));
                vendaDescontoJSON.setNome(nome);
                vendaDescontoJSON.setValor(rs.getDouble("valor"));
                lista.add(vendaDescontoJSON);
            }
        }
        return lista;
    }

    public static String extrairMatricula(String string) {
        String matricula = "";

        int startIndex = string.indexOf("Matricula :") + "Matricula :".length();
        int endIndex = string.indexOf("-", startIndex);

        if (startIndex != -1 && endIndex != -1) {
            matricula = string.substring(startIndex, endIndex).trim();
        }

        return matricula;
    }

    public List<AlunoSimplesJSON> getListaAlunoExcluidosBi(String sqlStr, PaginadorDTO paginadorDTO) throws Exception {

        if(paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
            sqlStr = getPaginador(paginadorDTO, sqlStr, "codigo asc ");
        } else {
            sqlStr += " order by codigo asc ";
        }

        List<AlunoSimplesJSON> lista = new ArrayList<AlunoSimplesJSON>();

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(sqlStr, this.con)) {

            while (rs.next()) {
                String nome = rs.getString("valorcampoanterior").substring(8);
                String matricula = "";
                if(rs.getString("valorcampoalterado").contains("Matricula")) {
                    matricula = extrairMatricula(rs.getString("valorcampoalterado"));
                }
                AlunoSimplesJSON alunoSimplesJSON = new AlunoSimplesJSON(matricula, nome);
                lista.add(alunoSimplesJSON);
            }
        }
        return lista;
    }

    @Override
    public void close() throws Exception {

    }
}

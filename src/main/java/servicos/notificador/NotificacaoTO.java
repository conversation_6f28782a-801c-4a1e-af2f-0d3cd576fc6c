/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.notificador;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;

/**
 *
 * <AUTHOR>
 */
public class NotificacaoTO extends SuperTO {

    private static final long serialVersionUID = -7043861797788041927L;
    private Long dataInicio = Calendario.hoje().getTime();
    private Long dataFim = Calendario.hoje().getTime();
    private String tipo;
    private String descricao;
    private String chave;
    private String localAcesso;
    private boolean lida = false;
    private TimeZone timeZone;

    public NotificacaoTO(long dataInicio, long dataFim, final String descricao,
            final String tipo, final String chave, final String localAcesso, final TimeZone timeZone) {
        this.dataInicio = dataInicio;
        this.dataFim = dataFim;
        if (timeZone != null) {
            this.dataInicio = Calendario.getDateInTimeZone(new Date(dataInicio), timeZone.getID()).getTime();
            this.dataFim = Calendario.getDateInTimeZone(new Date(dataFim), timeZone.getID()).getTime();
        }
        this.descricao = descricao;
        this.tipo = tipo;
        this.chave = chave;
        this.localAcesso = localAcesso;
        this.timeZone = timeZone;
    }

    public Long getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Long dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Long getDataFim() {
        return dataFim;
    }

    public void setDataFim(Long dataFim) {
        this.dataFim = dataFim;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(String localAcesso) {
        this.localAcesso = localAcesso;
    }

    public boolean isLida() {
        return lida;
    }

    public void setLida(boolean lida) {
        this.lida = lida;
    }

    public long getTotalMinutosRestantes() {
        long dif = this.getDataFim() - Calendario.hoje().getTime();
        if (timeZone != null) {
            dif = this.getDataFim() - Calendar.getInstance(TimeZone.getTimeZone(timeZone.getID())).getTime().getTime();
        }
        long minutes = (dif / 1000) / 60;
        if (minutes < 0) {
            minutes = 0;
        }
        return minutes;
    }

    public boolean isAtiva() {
        return getTotalMinutosRestantes() > 0L;
    }

    public String getMinutosRestante() {
        return String.valueOf(getTotalMinutosRestantes());
    }

    @Override
    public String toString() {
        return String.format("Inicio: %s Fim: %s Tipo: %s Descricao: %s Chave: %s, LocalAcesso: %s, Lida: %s", new Object[]{
            this.dataInicio,
            this.dataFim,
            this.tipo,
            this.descricao,
            this.chave,
            this.localAcesso,
            this.lida
        });
    }

    public String getDescricaoPlusID() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        if (timeZone == null) {
            timeZone = TimeZone.getTimeZone("Brazil/East");
        }
        sdf.setTimeZone(timeZone);
        return String.format("%s;%s\n", sdf.format(dataInicio), this.getDescricao());
    }

    public TimeZone getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(TimeZone timeZone) {
        this.timeZone = timeZone;
    }
}

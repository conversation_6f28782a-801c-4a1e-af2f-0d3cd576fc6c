
package relatorio.negocio.comuns.sad;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class RenovacaoConsultorTO extends SuperTO {
    private ColaboradorVO colaborador = new ColaboradorVO();
    private int qtdePrevista = 0;
    private int qtdeRenovados = 0;
    private int qtdeNaoRenovados = 0;
    private String tipoColaborador ="";

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public int getQtdePrevista() {
        return qtdePrevista;
    }

    public void setQtdePrevista(int qtdePrevista) {
        this.qtdePrevista = qtdePrevista;
    }

    public int getQtdeRenovados() {
        return qtdeRenovados;
    }

    public void setQtdeRenovados(int qtdeRenovados) {
        this.qtdeRenovados = qtdeRenovados;
    }

    public int getQtdeNaoRenovados() {
        return qtdeNaoRenovados;
    }

    public void setQtdeNaoRenovados(int qtdeNaoRenovados) {
        this.qtdeNaoRenovados = qtdeNaoRenovados;
    }

    public boolean getAlert() {
        return (getPercentualRenovados() < 50.0);
    }

    public double getPercentualRenovados() {
        double total = qtdePrevista;
        double renovados = qtdeRenovados;
        if(total == 0) return 0;
        return Uteis.arredondarForcando2CasasDecimais(renovados*100/total);
    }

    public double getPercentualNaoRenovados() {
        double total = qtdePrevista;
        double naoRenovados = qtdeNaoRenovados;
        if(total == 0) return 0;
        return Uteis.arredondarForcando2CasasDecimais(naoRenovados*100/total);
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof RenovacaoConsultorTO && obj != null) {
            RenovacaoConsultorTO aux = (RenovacaoConsultorTO)obj;
                return this.getColaborador().equals(aux.getColaborador());
        }
        return false;
    }

	/**
	 * @param tipoColaborador the tipoColaborador to set
	 */
	public void setTipoColaborador(String tipoColaborador) {
		this.tipoColaborador = tipoColaborador;
	}

	/**
	 * @return the tipoColaborador
	 */
	public String getTipoColaborador() {
		return tipoColaborador;
	}
}

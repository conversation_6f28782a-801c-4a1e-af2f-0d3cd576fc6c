/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import java.lang.Exception;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import negocio.comuns.acesso.webservice.client.*;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;

import negocio.comuns.utilitarias.UteisValidacao;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

/**
 * 
 * <AUTHOR>
 */
public class RenovacaoSinteticoVO extends SuperVO {

    private DefaultPieDataset dataSetPizza;
    private DefaultCategoryDataset dataSetBarra;
    private Boolean pizza;
    private Boolean barra;
    protected EmpresaVO empresa;
    protected ClienteSimplificadoTO cliente;
    protected ContratoVO contrato;
    protected ColaboradorVO consultor;
    protected String planoRenovado;
    private Date planoRenovadoDate;
    private String planoRenovadoModalidades;
    private String planoRenovadoDuracao;
    protected String situacaoRenovacao;
    protected String foneCliente;
    protected Boolean renovacaoAntecipada;
    protected Boolean renovacaoNoDia;
    protected Boolean renovacaoAtrasada;
    protected Boolean renovacaoAvencer;
    protected Boolean renovacaoVencido;
    protected Boolean renovacaoDesistente;
    protected Boolean renovacaoCancelado;
    protected Boolean renovacaoTrancado;
    private List<HistoricoVinculoVO> vinculos;
    private String telefonesCliente = "";
    private String planoRenovadoResponsavelCadastro;
    private String valorBaseCalculoApresentar;
    private Integer diasSemAcesso = 0;
    private Date ultimoContatoCRM;
    private Double mediaAcessos4semanas = 0.0;
    private Integer checkin4semanas = 0;
    private String professores ="";

    private Integer lifeTime = 0;
    private double LifeTimeValue = 0D;

    public RenovacaoSinteticoVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setCliente(new ClienteSimplificadoTO());
        setContrato(new ContratoVO());
        setEmpresa(new EmpresaVO());
        setConsultor(new ColaboradorVO());
        setFoneCliente("");
        setPlanoRenovado("");
        setRenovacaoAntecipada(false);
        setRenovacaoAtrasada(false);
        setRenovacaoAvencer(false);
        setRenovacaoDesistente(false);
        setRenovacaoNoDia(false);
        setRenovacaoVencido(false);
        setPlanoRenovadoModalidades("");
        setPlanoRenovadoDuracao("");
        setPlanoRenovadoResponsavelCadastro("");
    }

    public String getNomeApresentar() {
        return getContrato().getPessoa().getNome();
    }
    public String getEmailCliente(){
         return getContrato().getPessoa().getEmail();
    }
    public Date getPrevisao() {
        return getContrato().getDataPrevistaRenovar();
    }

    public String getPlanoApresentar() {
        return getContrato().getPlano().getDescricao();
    }

    public Integer getDuracao() {
        return getContrato().getContratoDuracao().getNumeroMeses();
    }

    public String getModalidades() {
        return getContrato().getNomeModalidades();
    }

    public String getModalidadesConcatenado() {
        return getContrato().getNomeModalidadesConcatenado();
    }

    public void obterSituacaoPrevisaRenovacao(Boolean cancelado, Boolean trancado) throws Exception {
        if (getContrato().getDataRenovarRealizada() != null
                && getContrato().getContratoResponsavelRenovacaoMatricula() != 0) {
            if (Uteis.getCompareData(getContrato().getDataRenovarRealizada(), getContrato().getDataPrevistaRenovar()) < 0) {
                setSituacaoRenovacao("AN");
                setRenovacaoAntecipada(true);
            } else if (Uteis.getCompareData(getContrato().getDataRenovarRealizada(), getContrato().getDataPrevistaRenovar()) == 0) {
                setSituacaoRenovacao("ND");
                setRenovacaoNoDia(true);
            } else if (Uteis.getCompareData(getContrato().getDataRenovarRealizada(), getContrato().getDataPrevistaRenovar()) > 0) {
                setSituacaoRenovacao("RT");
                setRenovacaoAtrasada(true);
            }
        } else {
//            if (getContrato().getDataRematriculaRealizada() != null) {
//                setSituacaoRenovacao("RT");
//                setRenovacaoAtrasada(true);
//                return;
//            } else {
            Boolean his = getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoTipoHistorico(getContrato().getCodigo(), "DE");
            if (his) {
                setSituacaoRenovacao("RD");
                setRenovacaoDesistente(true);
                return;
            }
            his = getFacade().getHistoricoContrato().obterHistoricoContratoPorCodigoContratoTipoHistorico(getContrato().getCodigo(), "VE");
            if (his) {
                setSituacaoRenovacao("RV");
                setRenovacaoVencido(true);
            } else if (cancelado && getContrato().getContratoCancelado()) {
                setSituacaoRenovacao("CA");
                setRenovacaoCancelado(true);
            } else if (trancado && getContrato().getContratoTrancado()) {
                setSituacaoRenovacao("TR");
                setRenovacaoCancelado(true);
            } else {
                setSituacaoRenovacao("RA");
                setRenovacaoAvencer(true);
            }
        }
    }

    public ClienteSimplificadoTO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteSimplificadoTO cliente) {
        this.cliente = cliente;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getRenovacaoAntecipada() {
        return renovacaoAntecipada;
    }

    public void setRenovacaoAntecipada(Boolean renovacaoAntecipada) {
        this.renovacaoAntecipada = renovacaoAntecipada;
    }

    public Boolean getRenovacaoAtrasada() {
        return renovacaoAtrasada;
    }

    public void setRenovacaoAtrasada(Boolean renovacaoAtrasada) {
        this.renovacaoAtrasada = renovacaoAtrasada;
    }

    public Boolean getRenovacaoAvencer() {
        return renovacaoAvencer;
    }

    public void setRenovacaoAvencer(Boolean renovacaoAvencer) {
        this.renovacaoAvencer = renovacaoAvencer;
    }

    public Boolean getRenovacaoDesistente() {
        return renovacaoDesistente;
    }

    public void setRenovacaoDesistente(Boolean renovacaoDesistente) {
        this.renovacaoDesistente = renovacaoDesistente;
    }

    public Boolean getRenovacaoNoDia() {
        return renovacaoNoDia;
    }

    public void setRenovacaoNoDia(Boolean renovacaoNoDia) {
        this.renovacaoNoDia = renovacaoNoDia;
    }

    public Boolean getRenovacaoVencido() {
        return renovacaoVencido;
    }

    public void setRenovacaoVencido(Boolean renovacaoVencido) {
        this.renovacaoVencido = renovacaoVencido;
    }

    public String getSituacaoRenovacao_Apresentar() {
        if (situacaoRenovacao == null) {
            return "";
        }
        if (situacaoRenovacao.equals("RA")) {
            return "Não Renovados a Vencer";
        }
        if (situacaoRenovacao.equals("RD")) {
            return "Não Renovados Desistentes";
        }
        if (situacaoRenovacao.equals("RT")) {
            return "Renovação Atrasada";
        }
        if (situacaoRenovacao.equals("ND")) {
            return "Renovação do Dia";
        }
        if (situacaoRenovacao.equals("RV")) {
            return "Não Renovados Vencidos";
        }
        if (situacaoRenovacao.equals("CA")) {
            return "Não Renovados Cancelado";
        }
        if (situacaoRenovacao.equals("TR")) {
            return "Não Renovados Trancado";
        }
        if (situacaoRenovacao.equals("AN")) {
            return "Renovação Antecipada";
        }
        return situacaoRenovacao;
    }

    public String getSituacaoRenovacao() {
        if (situacaoRenovacao == null) {
            return situacaoRenovacao = "";
        }
        return situacaoRenovacao;
    }

    public void setSituacaoRenovacao(String situacaoRenovacao) {
        this.situacaoRenovacao = situacaoRenovacao;
    }

    public Boolean getBarra() {
        return barra;
    }

    public void setBarra(Boolean barra) {
        this.barra = barra;
    }

    public Boolean getPizza() {
        return pizza;
    }

    public void setPizza(Boolean pizza) {
        this.pizza = pizza;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    public void setDataSetPizza(DefaultPieDataset dataSetPizza) {
        this.dataSetPizza = dataSetPizza;
    }

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public String getFoneCliente() {
        if (foneCliente == null) {
            foneCliente = "";
        }
        return foneCliente;
    }

    public void setFoneCliente(String foneCliente) {
        this.foneCliente = foneCliente;
    }

    public String getPlanoRenovado() {
        if (planoRenovado == null) {
            planoRenovado = "";
        }
        return planoRenovado;
    }

    public void setPlanoRenovado(String planoRenovado) {
        this.planoRenovado = planoRenovado;
    }

    public Boolean isRenovacaoCancelado() {
        return renovacaoCancelado;
    }

    public void setRenovacaoCancelado(Boolean renovacaoCancelado) {
        this.renovacaoCancelado = renovacaoCancelado;
    }

    public Boolean isRenovacaoTrancado() {
        return renovacaoTrancado;
    }

    public void setRenovacaoTrancado(Boolean renovacaoTrancado) {
        this.renovacaoTrancado = renovacaoTrancado;
    }

    public String getPlanoRenovadoModalidades() {
        return planoRenovadoModalidades;
    }

    public void setPlanoRenovadoModalidades(String planoRenovadoModalidades) {
        this.planoRenovadoModalidades = planoRenovadoModalidades;
    }

    public String getPlanoRenovadoDuracao() {
        return planoRenovadoDuracao;
    }

    public void setPlanoRenovadoDuracao(String planoRenovadoDuracao) {
        this.planoRenovadoDuracao = planoRenovadoDuracao;
    }

    /**
     * @param vinculos the vinculos to set
     */
    public void setVinculos(List<HistoricoVinculoVO> vinculos) {
        this.vinculos = vinculos;
    }

    /**
     * @return the vinculos
     */
    public List<HistoricoVinculoVO> getVinculos() {
        if (vinculos == null) {
            vinculos = new ArrayList<HistoricoVinculoVO>();
        }
        return vinculos;
    }

    public Date getPlanoRenovadoDate() {
        return planoRenovadoDate;
    }

    public void setPlanoRenovadoDate(Date planoRenovadoDate) {
        this.planoRenovadoDate = planoRenovadoDate;
    }

    public String getTelefonesCliente() {
        return telefonesCliente;
    }

    public void setTelefonesCliente(String telefonesCliente) {
        this.telefonesCliente = telefonesCliente;
    }

    public String getNomeClienteApresentar() throws Exception{
        return getContrato().getPessoa().getNome();
    }

    public Date getDataPrevistaRenovarApresentar() {
        return getContrato().getDataPrevistaRenovar();
    }

    public Integer getDuracaoMesesApresentar() {
        return getContrato().getContratoDuracao().getNumeroMeses();
    }

    public String getNomeModalidadesApresentar() {
        return getContrato().getNomeModalidades();
    }

    public String getPlanoRenovadoDateApresentar() {
        if (getPlanoRenovadoDate() == null){
            return "";
        }
        return getPlanoRenovadoDate().toString();
    }

    public String getPlanoRenovadoResponsavelCadastro() {
        if (planoRenovadoResponsavelCadastro == null) {
            planoRenovadoResponsavelCadastro = "";
        }
        return planoRenovadoResponsavelCadastro;
    }

    public void setPlanoRenovadoResponsavelCadastro(String planoRenovadoResponsavelCadastro) {
        this.planoRenovadoResponsavelCadastro = planoRenovadoResponsavelCadastro;
    }

    public String getValorBaseCalculoApresentar(){
        if (UteisValidacao.emptyString(valorBaseCalculoApresentar)) {
            valorBaseCalculoApresentar = "R$ 0.0";
        }
        return valorBaseCalculoApresentar;
    }

    public void setValorBaseCalculoApresentar(String valorBaseCalculoApresentar) {
        this.valorBaseCalculoApresentar = valorBaseCalculoApresentar;
    }

    public Integer getDiasSemAcesso() {
        return diasSemAcesso;
    }

    public void setDiasSemAcesso(Integer diasSemAcesso) {
        this.diasSemAcesso = diasSemAcesso;
    }

    public Date getUltimoContatoCRM() {
        return ultimoContatoCRM;
    }

    public void setUltimoContatoCRM(Date ultimoContatoCRM) {
        this.ultimoContatoCRM = ultimoContatoCRM;
    }
    public String getUltimoContatoCRM_Apresentar(){
        if(ultimoContatoCRM == null){
            return "";
        }
        return Uteis.getDataComHora(ultimoContatoCRM);
    }

    public Double getMediaAcessos4semanas() {
        return mediaAcessos4semanas;
    }

    public void setMediaAcessos4semanas(Double mediaAcessos4semanas) {
        this.mediaAcessos4semanas = mediaAcessos4semanas;
    }

    public String getMediaAcessos4semanas_Apresentar() {
        return mediaAcessos4semanas.toString().replace(".", ",");
    }

    public Integer getCheckin4semanas() {
        return checkin4semanas;
    }

    public void setCheckin4semanas(Integer checkin4semanas) {
        this.checkin4semanas = checkin4semanas;
    }

    public String getProfessores() {
        return professores;
    }

    public void setProfessores(String professores) {
        this.professores = professores;
    }

    public Integer getLifeTime() {
        return lifeTime;
    }

    public void setLifeTime(Integer lifeTime) {
        this.lifeTime = lifeTime;
    }

    public  String getMatriculaCliente(){
        return getCliente().getMatricula();
    }

    public Integer getCodigoContrato(){
        return getContrato().getCodigo();
    }

    public double getLifeTimeValue() {
        return LifeTimeValue;
    }

    public void setLifeTimeValue(double lifeTimeValue) {
        LifeTimeValue = lifeTimeValue;
    }
}

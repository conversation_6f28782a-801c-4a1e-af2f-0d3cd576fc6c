/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import org.json.JSONObject;
import com.sun.org.apache.xerces.internal.jaxp.datatype.XMLGregorianCalendarImpl;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.integracao.treino.ClienteZW;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;

/**
 *
 * <AUTHOR>
 */
public class SituacaoClienteSinteticoDWVO extends SuperVO {

    private Date dia = null;
    private Integer codigoCliente = 0;
    private Integer codigoPessoa;
    private Integer matricula = 0;
    private String nomeCliente = "";
    private String sexoCliente = "";
    private Date dataNascimento = null;
    private Integer idade = 0;
    private String profissao = "";
    private Integer empresaCliente = 0;
    private String colaboradores = "";
    private Integer codigoContrato = 0;
    private String situacao = "";
    private String situacaoContrato = "";
    private Integer duracaoContratoMeses = 0;
    private String mnemonicoContrato = "";
    private String nomePlano = "";
    private Double valorFaturadoContrato = 0.0;
    private Double valorPagoContrato = 0.0;
    private Double valorParcAbertoContrato = 0.0;
    private Double saldoContaCorrenteCliente = 0.0;
    private String telefonesCliente = "";
    private String codAcessoCliente = "";
    //datas do contrato
    private Date dataVigenciaDe = null;
    private Date dataVigenciaAte = null;
    private Date dataVigenciaAteAjustada = null;
    private Date dataLancamentoContrato = null;
    private Date dataRenovacaoContrato = null;
    private Date dataRematriculaContrato = null;
    //datas do cliente
    private Date dataUltimoBV = null;
    private Date dataMatricula = null;
    private Date dataUltimaRematricula = null;
    private Integer diasAssiduidadeUltRematriculaAteHoje = 0;
    //dados de acesso
    private Integer diasAcessoSemanaPassada = -1;
    private Integer diasAcessoSemana2 = -1;
    private Integer diasAcessoSemana3 = -1;
    private Integer diasAcessoSemana4 = -1;
    private Date dataUltimoAcesso = null;
    //private String periodoAcessoContrato = "";
    private String tipoPeriodoAcesso = "";
    private Date dataInicioPeriodoAcesso = null;
    private Date dataFimPeriodoAcesso = null;
    //dados do CRM
    private String faseAtualCRM = "";
    private Date dataUltimoContatoCRM = null;
    private String responsavelUltimoContatoCRM = "";
    private Integer codigoUltimoContatoCRM = 0;
    private long nrDiasUltimoAcesso;
    private int vezesporsemana = 0;
    //frequencimetro
    private Integer diasAcessoMes2 = 0;
    private Integer diasAcessoMes3 = 0;
    private Integer diasAcessoMes4 = 0;
    private Integer diasAcessoUltimoMes = 0;
    private Integer mediaDiasAcesso4Meses = 0;
    private String telCelColVinculados = "";
    private Integer pesoRisco;
    private Integer SMSRisco;
    private boolean envioSMSMarcadoClassif;
    private Integer codigoUsuarioMovel = 0;
    private String email = "";
    @NaoControlarLogAlteracao
    private boolean atualizarTreino = true;
    private String situacaoMatriculaContrato = "";
    private String cpf = "";
    private String modalidades;
    private String descricoesModalidades;
    private Integer frequenciaSemanal = 0;
    private Integer saldoCreditoTreino = 0;
    private Integer totalCreditoTreino = 0;
    private boolean validarSaldoCreditoTreino = false;
    private Integer quantidadeDiasExtra = 0;
    private String situacaoContratoOperacao;
    private Boolean crossfit;
    @NaoControlarLogAlteracao
    private String key = "";
    @NaoControlarLogAlteracao
    private boolean incrementarVersaoTreino = false;
    private String nomeConsulta;
    private Date dataSaidaAcesso = null;
    @NaoControlarLogAlteracao
    private String nomeEmpresa;
    private Date dataCadastro;
    private Boolean existeParcVencidaContrato = false;
    private Boolean empresausafreepass = false;
    private Date ultimaVisita;
    private String cargo;
    private boolean freePass = false;
    private String endereco;
    private String cidade;
    private String bairro;
    private String estadoCivil;
    private String RG;
    private String UF;
    private String cpfConsulta;
    private String telefonesConsulta;
    private Date freePassInicio;
    private Date freePassFim;

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Integer getCodigoUltimoContatoCRM() {
        return codigoUltimoContatoCRM;
    }

    public void setCodigoUltimoContatoCRM(Integer codigoUltimoContatoCRM) {
        this.codigoUltimoContatoCRM = codigoUltimoContatoCRM;
    }

    public String getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(String colaboradores) {
        this.colaboradores = colaboradores;
    }

    public Date getDataLancamentoContrato() {
        return dataLancamentoContrato;
    }

    public void setDataLancamentoContrato(Date dataLancamentoContrato) {
        this.dataLancamentoContrato = dataLancamentoContrato;
    }

    public Date getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(Date dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Date getDataRematriculaContrato() {
        return dataRematriculaContrato;
    }

    public void setDataRematriculaContrato(Date dataRematriculaContrato) {
        this.dataRematriculaContrato = dataRematriculaContrato;
    }

    public Date getDataRenovacaoContrato() {
        return dataRenovacaoContrato;
    }

    public void setDataRenovacaoContrato(Date dataRenovacaoContrato) {
        this.dataRenovacaoContrato = dataRenovacaoContrato;
    }

    public Date getDataUltimaRematricula() {
        return dataUltimaRematricula;
    }

    public void setDataUltimaRematricula(Date dataUltimaRematricula) {
        this.dataUltimaRematricula = dataUltimaRematricula;
    }

    public Date getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public Date getDataUltimoBV() {
        return dataUltimoBV;
    }

    public void setDataUltimoBV(Date dataUltimoBV) {
        this.dataUltimoBV = dataUltimoBV;
    }

    public Date getDataUltimoContatoCRM() {
        return dataUltimoContatoCRM;
    }

    public void setDataUltimoContatoCRM(Date dataUltimoContatoCRM) {
        this.dataUltimoContatoCRM = dataUltimoContatoCRM;
    }

    public Date getDataVigenciaAte() {
        return dataVigenciaAte;
    }

    public void setDataVigenciaAte(Date dataVigenciaAte) {
        this.dataVigenciaAte = dataVigenciaAte;
    }

    public Date getDataVigenciaAteAjustada() {
        return dataVigenciaAteAjustada;
    }

    public void setDataVigenciaAteAjustada(Date dataVigenciaAteAjustada) {
        this.dataVigenciaAteAjustada = dataVigenciaAteAjustada;
    }
    public String getDataVigenciaAte_Apresentar(){
        return Uteis.getData(getDataVigenciaAte());
    }
    public Date getDataVigenciaDe() {
        return dataVigenciaDe;
    }

    public void setDataVigenciaDe(Date dataVigenciaDe) {
        this.dataVigenciaDe = dataVigenciaDe;
    }

    public Integer getDiasAcessoSemanaPassada() {
        return diasAcessoSemanaPassada;
    }

    public void setDiasAcessoSemanaPassada(Integer diasAcessoSemanaPassada) {
        this.diasAcessoSemanaPassada = diasAcessoSemanaPassada;
    }

    public Integer getDiasAssiduidadeUltRematriculaAteHoje() {
        return diasAssiduidadeUltRematriculaAteHoje;
    }

    public void setDiasAssiduidadeUltRematriculaAteHoje(Integer diasAssiduidadeUltRematriculaAteHoje) {
        this.diasAssiduidadeUltRematriculaAteHoje = diasAssiduidadeUltRematriculaAteHoje;
    }

    public Integer getDuracaoContratoMeses() {
        return duracaoContratoMeses;
    }

    public void setDuracaoContratoMeses(Integer duracaoContratoMeses) {
        this.duracaoContratoMeses = duracaoContratoMeses;
    }

    public String getFaseAtualCRM() {
        return faseAtualCRM;
    }

    public void setFaseAtualCRM(String faseAtualCRM) {
        this.faseAtualCRM = faseAtualCRM;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public Integer getMatricula() {
        return matricula;
    }

    public void setMatricula(Integer matricula) {
        this.matricula = matricula;
    }

    public String getMnemonicoContrato() {
        return mnemonicoContrato;
    }

    public void setMnemonicoContrato(String mnemonicoContrato) {
        this.mnemonicoContrato = mnemonicoContrato;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public Integer getEmpresaCliente() {
        return empresaCliente;
    }

    public void setEmpresaCliente(Integer empresaCliente) {
        this.empresaCliente = empresaCliente;
    }

    public String getResponsavelUltimoContatoCRM() {
        return responsavelUltimoContatoCRM;
    }

    public void setResponsavelUltimoContatoCRM(String responsavelUltimoContatoCRM) {
        this.responsavelUltimoContatoCRM = responsavelUltimoContatoCRM;
    }

    public Double getSaldoContaCorrenteCliente() {
        return saldoContaCorrenteCliente;
    }

    public void setSaldoContaCorrenteCliente(Double saldoContaCorrenteCliente) {
        this.saldoContaCorrenteCliente = saldoContaCorrenteCliente;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Double getValorFaturadoContrato() {
        return valorFaturadoContrato;
    }

    public void setValorFaturadoContrato(Double valorFaturadoContrato) {
        this.valorFaturadoContrato = valorFaturadoContrato;
    }

    public Double getValorPagoContrato() {
        return valorPagoContrato;
    }

    public void setValorPagoContrato(Double valorPagoContrato) {
        this.valorPagoContrato = valorPagoContrato;
    }

    public Double getValorParcAbertoContrato() {
        return valorParcAbertoContrato;
    }

    public void setValorParcAbertoContrato(Double valorParcAbertoContrato) {
        this.valorParcAbertoContrato = valorParcAbertoContrato;
    }

    public void setCodigoCliente(int codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Date getDataFimPeriodoAcesso() {
        return dataFimPeriodoAcesso;
    }

    public void setDataFimPeriodoAcesso(Date dataFimPeriodoAcesso) {
        this.dataFimPeriodoAcesso = dataFimPeriodoAcesso;
    }

    public Date getDataInicioPeriodoAcesso() {
        return dataInicioPeriodoAcesso;
    }

    public void setDataInicioPeriodoAcesso(Date dataInicioPeriodoAcesso) {
        this.dataInicioPeriodoAcesso = dataInicioPeriodoAcesso;
    }

    public String getTipoPeriodoAcesso() {
        return tipoPeriodoAcesso;
    }

    public void setTipoPeriodoAcesso(String tipoPeriodoAcesso) {
        this.tipoPeriodoAcesso = tipoPeriodoAcesso;
    }

    public SituacaoClienteSinteticoDWVO() {
        super();
    }

    public Integer getDataInicioPeriodoAcessoD() {
        if (dataInicioPeriodoAcesso != null) {
            return Uteis.getDiaMesData(dataInicioPeriodoAcesso);
        } else {
            return 0;
        }

    }

    public Integer getDataInicioPeriodoAcessoM() {
        if (dataInicioPeriodoAcesso != null) {
            return Uteis.getMesData(dataInicioPeriodoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataInicioPeriodoAcessoY() {
        if (dataInicioPeriodoAcesso != null) {
            return Uteis.getAnoData(dataInicioPeriodoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataFimPeriodoAcessoD() {
        if (dataFimPeriodoAcesso != null) {
            return Uteis.getDiaMesData(dataFimPeriodoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataFimPeriodoAcessoM() {
        if (dataFimPeriodoAcesso != null) {
            return Uteis.getMesData(dataFimPeriodoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataFimPeriodoAcessoY() {
        if (dataFimPeriodoAcesso != null) {
            return Uteis.getAnoData(dataFimPeriodoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataLancamentoContratoD() {
        if (dataLancamentoContrato != null) {
            return Uteis.getDiaMesData(dataLancamentoContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataLancamentoContratoM() {
        if (dataLancamentoContrato != null) {
            return Uteis.getMesData(dataLancamentoContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataLancamentoContratoY() {
        if (dataLancamentoContrato != null) {
            return Uteis.getAnoData(dataLancamentoContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataMatriculaD() {
        if (dataMatricula != null) {
            return Uteis.getDiaMesData(dataMatricula);
        } else {
            return 0;
        }
    }

    public Integer getDataMatriculaM() {
        if (dataMatricula != null) {
            return Uteis.getMesData(dataMatricula);
        } else {
            return 0;
        }
    }

    public Integer getDataMatriculaY() {
        if (dataMatricula != null) {
            return Uteis.getAnoData(dataMatricula);
        } else {
            return 0;
        }
    }

    public Integer getDataNascimentoD() {
        if (dataNascimento != null) {
            return Uteis.getDiaMesData(dataNascimento);
        } else {
            return 0;
        }
    }

    public Integer getDataNascimentoM() {
        if (dataNascimento != null) {
            return Uteis.getMesData(dataNascimento);
        } else {
            return 0;
        }
    }

    public Integer getDataNascimentoY() {
        if (dataNascimento != null) {
            return Uteis.getAnoData(dataNascimento);
        } else {
            return 0;
        }
    }

    public Integer getDataRematriculaContratoD() {
        if (dataRematriculaContrato != null) {
            return Uteis.getDiaMesData(dataRematriculaContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataRematriculaContratoM() {
        if (dataRematriculaContrato != null) {
            return Uteis.getMesData(dataRematriculaContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataRematriculaContratoY() {
        if (dataRematriculaContrato != null) {
            return Uteis.getAnoData(dataRematriculaContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataRenovacaoContratoD() {
        if (dataRenovacaoContrato != null) {
            return Uteis.getDiaMesData(dataRenovacaoContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataRenovacaoContratoM() {
        if (dataRenovacaoContrato != null) {
            return Uteis.getMesData(dataRenovacaoContrato);
        } else {
            return 0;
        }

    }

    public Integer getDataRenovacaoContratoY() {
        if (dataRenovacaoContrato != null) {
            return Uteis.getAnoData(dataRenovacaoContrato);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimaRematriculaD() {
        if (dataUltimaRematricula != null) {
            return Uteis.getDiaMesData(dataUltimaRematricula);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimaRematriculaM() {
        if (dataUltimaRematricula != null) {
            return Uteis.getMesData(dataUltimaRematricula);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimaRematriculaY() {
        if (dataUltimaRematricula != null) {
            return Uteis.getAnoData(dataUltimaRematricula);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoAcessoD() {
        if (dataUltimoAcesso != null) {
            return Uteis.getDiaMesData(dataUltimoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoAcessoM() {
        if (dataUltimoAcesso != null) {
            return Uteis.getMesData(dataUltimoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoAcessoY() {
        if (dataUltimoAcesso != null) {
            return Uteis.getAnoData(dataUltimoAcesso);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoBVD() {
        if (dataUltimoBV != null) {
            return Uteis.getDiaMesData(dataUltimoBV);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoBVM() {
        if (dataUltimoBV != null) {
            return Uteis.getMesData(dataUltimoBV);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoBVY() {
        if (dataUltimoBV != null) {
            return Uteis.getAnoData(dataUltimoBV);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoContatoCRMD() {
        if (dataUltimoContatoCRM != null) {
            return Uteis.getDiaMesData(dataUltimoContatoCRM);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoContatoCRMM() {
        if (dataUltimoContatoCRM != null) {
            return Uteis.getMesData(dataUltimoContatoCRM);
        } else {
            return 0;
        }
    }

    public Integer getDataUltimoContatoCRMY() {
        if (dataUltimoContatoCRM != null) {
            return Uteis.getAnoData(dataUltimoContatoCRM);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaAteAjustadaD() {
        if (dataVigenciaAteAjustada != null) {
            return Uteis.getDiaMesData(dataVigenciaAteAjustada);
        } else {
            return 0;
        }

    }

    public Integer getDataVigenciaAteAjustadaM() {
        if (dataVigenciaAteAjustada != null) {
            return Uteis.getMesData(dataVigenciaAteAjustada);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaAteAjustadaY() {
        if (dataVigenciaAteAjustada != null) {
            return Uteis.getAnoData(dataVigenciaAteAjustada);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaAteD() {
        if (dataVigenciaAte != null) {
            return Uteis.getDiaMesData(dataVigenciaAte);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaAteM() {
        if (dataVigenciaAte != null) {
            return Uteis.getMesData(dataVigenciaAte);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaAteY() {
        if (dataVigenciaAte != null) {
            return Uteis.getAnoData(dataVigenciaAte);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaDeD() {
        if (dataVigenciaDe != null) {
            return Uteis.getDiaMesData(dataVigenciaDe);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaDeM() {
        if (dataVigenciaDe != null) {
            return Uteis.getMesData(dataVigenciaDe);
        } else {
            return 0;
        }
    }

    public Integer getDataVigenciaDeY() {
        if (dataVigenciaDe != null) {
            return Uteis.getAnoData(dataVigenciaDe);
        } else {
            return 0;
        }
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public Integer getDiasAcessoSemana2() {
        return diasAcessoSemana2;
    }

    public void setDiasAcessoSemana2(Integer diasAcessoSemana2) {
        this.diasAcessoSemana2 = diasAcessoSemana2;
    }

    public Integer getDiasAcessoSemana3() {
        return diasAcessoSemana3;
    }

    public void setDiasAcessoSemana3(Integer diasAcessoSemana3) {
        this.diasAcessoSemana3 = diasAcessoSemana3;
    }

    public Integer getDiasAcessoSemana4() {
        return diasAcessoSemana4;
    }

    public void setDiasAcessoSemana4(Integer diasAcessoSemana4) {
        this.diasAcessoSemana4 = diasAcessoSemana4;
    }

    public String getNomeDiaSemanaDataMatricula() {
        if (dataMatricula != null) {
            return super.retornaNomeDiaSemanaData(dataMatricula);
        } else {
            return "";
        }
    }

    public String getNomeDiaSemanaDataUltimaRematricula() {
        if (dataUltimaRematricula != null) {
            return super.retornaNomeDiaSemanaData(dataUltimaRematricula);
        } else {
            return "";
        }
    }

    public String getNomeDiaSemanaDataUltimoAcesso() {
        if (dataUltimoAcesso != null) {
            return super.retornaNomeDiaSemanaData(dataUltimoAcesso);
        } else {
            return "";
        }
    }

    public String getDataMatriculaAteHojePorExtenso() {
        return getData(dataMatricula);
    }

    public String getDataRematriculaAteHojePorExtenso() {
        return getData(dataUltimaRematricula);
    }

    private String getData(Date data) {
        if (data != null && !Calendario.igual(data, Calendario.hoje())) {
            return " (Há " + Uteis.obterDiferencaEntreDatasPorExtenso(data, negocio.comuns.utilitarias.Calendario.hoje()) + ")";
        } else if (data != null && Calendario.igual(data, Calendario.hoje())) {
            return " (Hoje)";
        } else {
            return "";
        }
    }

    public void setNrDiasUltimoAcesso(long nrDiasUltimoAcesso) {
        this.nrDiasUltimoAcesso = nrDiasUltimoAcesso;
    }

    public long getNrDiasUltimoAcesso() {
        return nrDiasUltimoAcesso;
    }

    public int getVezesporsemana() {
        return vezesporsemana;
    }

    public void setVezesporsemana(int vezesporsemana) {
        this.vezesporsemana = vezesporsemana;
    }

    public void setDiasAcessoUltimoMes(Integer diasAcessoUltimoMes) {
        this.diasAcessoUltimoMes = diasAcessoUltimoMes;
    }

    public Integer getDiasAcessoUltimoMes() {
        return diasAcessoUltimoMes;
    }

    public void setDiasAcessoMes4(Integer diasAcessoMes4) {
        this.diasAcessoMes4 = diasAcessoMes4;
    }

    public Integer getDiasAcessoMes4() {
        return diasAcessoMes4;
    }

    public void setDiasAcessoMes3(Integer diasAcessoMes3) {
        this.diasAcessoMes3 = diasAcessoMes3;
    }

    public Integer getDiasAcessoMes3() {
        return diasAcessoMes3;
    }

    public void setDiasAcessoMes2(Integer diasAcessoMes2) {
        this.diasAcessoMes2 = diasAcessoMes2;
    }

    public Integer getDiasAcessoMes2() {
        return diasAcessoMes2;
    }

    public void setMediaDiasAcesso4Meses(Integer mediaDiasAcesso4Meses) {
        this.mediaDiasAcesso4Meses = mediaDiasAcesso4Meses;
    }

    public Integer getMediaDiasAcesso4Meses() {
        return mediaDiasAcesso4Meses;
    }

    /**
     * @return the telCelColVinculados
     */
    public String getTelCelColVinculados() {
        return telCelColVinculados;
    }

    /**
     * @param telCelColVinculados the telCelColVinculados to set
     */
    public void setTelCelColVinculados(String telCelColVinculados) {
        this.telCelColVinculados = telCelColVinculados;
    }

    /**
     * @return the pesoRisco
     */
    public Integer getPesoRisco() {
        if(pesoRisco == null) {
            pesoRisco = 0;
        }
        return pesoRisco;
    }

    /**
     * @param pesoRisco the pesoRisco to set
     */
    public void setPesoRisco(Integer pesoRisco) {
        this.pesoRisco = pesoRisco;
    }

    /**
     * @return the envioSMSMarcadoClassif
     */
    public boolean isEnvioSMSMarcadoClassif() {
        return envioSMSMarcadoClassif;
    }

    /**
     * @param envioSMSMarcadoClassif the envioSMSMarcadoClassif to set
     */
    public void setEnvioSMSMarcadoClassif(boolean envioSMSMarcadoClassif) {
        this.envioSMSMarcadoClassif = envioSMSMarcadoClassif;
    }

    public Integer getSMSRisco() {
        return SMSRisco;
    }

    public void setSMSRisco(Integer SMSRisco) {
        this.SMSRisco = SMSRisco;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getCodigoUsuarioMovel() {
        return codigoUsuarioMovel;
    }

    public void setCodigoUsuarioMovel(Integer codigoUsuarioMovel) {
        this.codigoUsuarioMovel = codigoUsuarioMovel;
    }

    public String getSexoCliente() {
        return sexoCliente;
    }

    public void setSexoCliente(String sexoCliente) {
        this.sexoCliente = sexoCliente;
    }

    public String getTelefonesCliente() {
        return telefonesCliente;
    }
    public String getPrimeiroTelefone() {
        if(!UteisValidacao.emptyString(telefonesCliente)){
            String[] telefone =  telefonesCliente.split(",")[0].replace("{","").replace("}","").split("-");
            return  telefone.length > 1 ? telefone[1] : telefone[0];
        }
        return telefonesCliente;
    }
    public void setTelefonesCliente(String telefonesCliente) {
        this.telefonesCliente = telefonesCliente;
    }

    public JSONObject toJSON(boolean append) {
        if (this == null) {
            return new JSONObject();
        }
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                if (append) {
                    o.append(field.getName(), field.get(this));
                } else {
                    o.put(field.getName(), field.get(this));
                }
            }
        } catch (Exception ignored) {
            Uteis.logar(ignored, SituacaoClienteSinteticoDWVO.class);
        }
        return o;
    }

    private static XMLGregorianCalendarImpl gCalendar(final Date d) {
        if (d == null) {
            return null;
        }
        GregorianCalendar g = new GregorianCalendar(Calendario.getDefaultLocale());
        g.setTime(d);
        return new XMLGregorianCalendarImpl(g);
    }

    public ClienteZW toSinteticoZW(String telefones, String emails) {
        ClienteZW s = new ClienteZW();
        s.setCodigo(codigo);
        s.setCodigoCliente(codigoCliente);
        s.setCodigoContrato(codigoContrato);
        s.setCodigoPessoa(codigoPessoa);
        s.setCodigoUltimoContatoCRM(codigoUltimoContatoCRM);
        s.setColaboradores(colaboradores);
        s.setDataFimPeriodoAcesso(gCalendar(dataFimPeriodoAcesso));
        s.setDataInicioPeriodoAcesso(gCalendar(dataInicioPeriodoAcesso));
        s.setDataLancamentoContrato(gCalendar(dataLancamentoContrato));
        s.setDataMatricula(gCalendar(dataMatricula));
        s.setDataRematriculaContrato(gCalendar(dataRematriculaContrato));
        s.setDataRenovacaoContrato(gCalendar(dataRenovacaoContrato));
        s.setDataUltimarematricula(gCalendar(dataUltimaRematricula));
        s.setDataUltimoBV(gCalendar(dataUltimoBV));
        s.setDataUltimoContatoCRM(gCalendar(dataUltimoContatoCRM));
        s.setDataUltimoacesso(gCalendar(dataUltimoAcesso));
        s.setDataVigenciaAte(gCalendar(dataVigenciaAte));
        s.setDataVigenciaAteAjustada(gCalendar(dataVigenciaAteAjustada));
        s.setDataVigenciaDe(gCalendar(dataVigenciaDe));
        s.setDataNascimento(gCalendar(dataNascimento));
        s.setDia(gCalendar(dia));
        s.setDiasAcessoMes2(diasAcessoMes2);
        s.setDiasAcessoMes3(diasAcessoMes3);
        s.setDiasAcessoMes4(diasAcessoMes4);
        s.setDiasAcessoSemana2(diasAcessoSemana2);
        s.setDiasAcessoSemana3(diasAcessoSemana3);
        s.setDiasAcessoSemana4(diasAcessoSemana4);
        s.setDiasAcessoSemanaPassada(diasAcessoSemanaPassada);
        s.setDiasAcessoUltimoMes(diasAcessoUltimoMes);
        s.setDiasAssiduidadeUltRematriculaAteHoje(diasAssiduidadeUltRematriculaAteHoje);
        s.setDuracaoContratoMeses(duracaoContratoMeses);
        s.setFaseAtualCRM(faseAtualCRM);
        s.setIdade(idade);
        s.setMatricula(matricula);
        s.setMediaDiasAcesso4Meses(mediaDiasAcesso4Meses);
        s.setMnemonicoDoContrato(mnemonicoContrato);
        s.setNome(nomeCliente);
        s.setNomeplano(nomePlano);
        s.setProfissao(profissao);
        s.setResponsavelUltimoContatoCRM(responsavelUltimoContatoCRM);
        s.setSaldoContaCorrenteCliente(saldoContaCorrenteCliente);
        s.setSexo(sexoCliente);
        s.setSituacao(situacao);
        s.setSituacaoContrato(situacaoContrato);
        s.setTipoPeriodoAcesso(tipoPeriodoAcesso);
        s.setValorPagoContrato(valorPagoContrato);
        s.setValorParcAbertoContrato(valorParcAbertoContrato);
        s.setValorfaturadocontrato(valorFaturadoContrato);
        s.setVezesPorSemana(vezesporsemana);
        s.setEmpresa(empresaCliente);
        s.setTelefones(telefones);
        s.setPesoRisco(pesoRisco);
        s.setSituacaoMatriculaContrato(situacaoMatriculaContrato);
        s.setEmail(emails);
        s.setCodigoAcesso(codAcessoCliente);
        s.setModalidades(modalidades);
        s.setDescricaoDuracao(getDescricaoDuracao());
        s.setTotalCreditoTreino(totalCreditoTreino);
        s.setSaldoCreditoTreino(saldoCreditoTreino);
        s.setDescricoesModalidades(descricoesModalidades);
        s.setSituacaoContratoOperacao(situacaoContratoOperacao);
        s.setCrossfit(crossfit);
        s.setNomeConsulta(nomeConsulta);
        s.setDataCadastro(gCalendar(dataCadastro));
        s.setExisteParcVencidaContrato(existeParcVencidaContrato);
        s.setEmpresausafreepass(empresausafreepass);
        s.setUltimaVisita(gCalendar(ultimaVisita));
        s.setCargo(cargo);
        s.setFreePass(freePass);
        s.setEndereco(endereco);
        s.setCidade(cidade);
        s.setBairro(bairro);
        s.setEstadoCivil(estadoCivil);
        s.setRG(RG);
        s.setCPF(cpf);
        s.setTelefones(telefones);
        s.setFreePassInicio(gCalendar(freePassInicio));
        s.setFreePassFim(gCalendar(freePassFim));
        return s;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isAtualizarTreino() {
        return atualizarTreino;
    }

    public void setAtualizarTreino(boolean atualizarTreino) {
        this.atualizarTreino = atualizarTreino;
    }

    public String getSituacaoMatriculaContrato() {
        return situacaoMatriculaContrato;
    }

    public void setSituacaoMatriculaContrato(String situacaoMatriculaContrato) {
        this.situacaoMatriculaContrato = situacaoMatriculaContrato;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCodAcessoCliente() {
        return codAcessoCliente;
    }

    public void setCodAcessoCliente(String codAcessoCliente) {
        this.codAcessoCliente = codAcessoCliente;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getDescricoesModalidades() {
        return this.descricoesModalidades;
    }

    public void setDescricoesModalidades(String descricoesModalidades) {
        this.descricoesModalidades = descricoesModalidades;
    }

    public Integer getFrequenciaSemanal() {
        return frequenciaSemanal;
    }

    public void setFrequenciaSemanal(Integer frequenciaSemanal) {
        this.frequenciaSemanal = frequenciaSemanal;
    }

    public Integer getSaldoCreditoTreino() {
        return saldoCreditoTreino;
    }

    public void setSaldoCreditoTreino(Integer saldoCreditoTreino) {
        this.saldoCreditoTreino = saldoCreditoTreino;
    }

    public boolean isValidarSaldoCreditoTreino() {
        return validarSaldoCreditoTreino;
    }

    public void setValidarSaldoCreditoTreino(boolean validarSaldoCreditoTreino) {
        this.validarSaldoCreditoTreino = validarSaldoCreditoTreino;
    }
    
    public Integer getTotalDias(){
        return (this.duracaoContratoMeses * 30) + this.quantidadeDiasExtra;
    }

    
    public String getDescricaoDuracao(){
        if (this.quantidadeDiasExtra > 0){
            return getTotalDias() + " Dias";
        }
        if (this.duracaoContratoMeses == 1)
            return "1 Mês";
        else
            return this.duracaoContratoMeses + " Meses";
    }

    public Integer getQuantidadeDiasExtra() {
        return quantidadeDiasExtra;
    }

    public void setQuantidadeDiasExtra(Integer quantidadeDiasExtra) {
        this.quantidadeDiasExtra = quantidadeDiasExtra;
    }
    
    public String getMediaUltimasSemanas(){
        try {
            Double mediaUltimasSemanas = 0.0;
            mediaUltimasSemanas = (double) diasAcessoSemana4 + diasAcessoSemana3 + diasAcessoSemana2 + diasAcessoSemanaPassada;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula (mediaUltimasSemanas / 4);
        } catch (Exception e) {
            return "ERRO!";
        }
    }

    public String getMediaUltimosMeses(){
        try {
            Double mediaUltimosMeses = 0.0;
            mediaUltimosMeses = (double) diasAcessoMes4 + diasAcessoMes3 + diasAcessoMes2 + diasAcessoUltimoMes;
            return Uteis.arrendondarForcando2CadasDecimaisComVirgula (mediaUltimosMeses / 4);
        } catch (Exception e) {
            return "ERRO!";
        }
    }

    public Integer getTotalCreditoTreino() {
        return totalCreditoTreino;
    }

    public void setTotalCreditoTreino(Integer totalCreditoTreino) {
        this.totalCreditoTreino = totalCreditoTreino;
    }

    public String getSituacaoContratoOperacao() {
        return situacaoContratoOperacao;
    }

    public void setSituacaoContratoOperacao(String situacaoContratoOperacao) {
        this.situacaoContratoOperacao = situacaoContratoOperacao;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
    
    public String getDia_Apresentar() {
        return Uteis.getDataComHora(this.dia);
    }

    public Boolean getCrossfit() {
        return crossfit;
    }

    public void setCrossfit(Boolean crossfit) {
        this.crossfit = crossfit;
    }

    public boolean isIncrementarVersaoTreino() {
        return incrementarVersaoTreino;
    }

    public void setIncrementarVersaoTreino(boolean incrementarVersaoTreino) {
        this.incrementarVersaoTreino = incrementarVersaoTreino;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public PessoaTO toPessoaTO(){
        PessoaTO p = new PessoaTO();
        p.setCodigo(getCodigoPessoa());
        p.setCpf(getCpf());
        p.setNomeCompleto(getNomeCliente());
        p.setMatricula(getMatricula().toString());
        return p;
    }

    public Date getDataSaidaAcesso() {
        return dataSaidaAcesso;
    }

    public void setDataSaidaAcesso(Date dataSaidaAcesso) {
        this.dataSaidaAcesso = dataSaidaAcesso;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Boolean getExisteParcVencidaContrato() {
        return existeParcVencidaContrato;
    }

    public void setExisteParcVencidaContrato(Boolean existeParcVencidaContrato) {
        this.existeParcVencidaContrato = existeParcVencidaContrato;
    }

    public Boolean getEmpresausafreepass() {
        return empresausafreepass;
    }

    public void setEmpresausafreepass(Boolean empresausafreepass) {
        this.empresausafreepass = empresausafreepass;
    }

    public Date getUltimaVisita() {
        return ultimaVisita;
    }

    public void setUltimaVisita(Date ultimaVisita) {
        this.ultimaVisita = ultimaVisita;
    }

    public String getCargo() {
        return cargo;
    }

    public void setCargo(String cargo) {
        this.cargo = cargo;
    }

    public boolean isFreePass() {
        return freePass;
    }

    public void setFreePass(boolean freePass) {
        this.freePass = freePass;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getEstadoCivil() {
        return estadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getRG() {
        return RG;
    }

    public void setRG(String RG) {
        this.RG = RG;
    }

    public String getUF() {
        return UF;
    }

    public void setUF(String UF) {
        this.UF = UF;
    }

    public String getCpfConsulta() {
        return cpfConsulta;
    }

    public void setCpfConsulta(String cpfConsulta) {
        this.cpfConsulta = cpfConsulta;
    }

    public String getTelefonesConsulta() {
        return telefonesConsulta;
    }

    public void setTelefonesConsulta(String telefonesConsulta) {
        this.telefonesConsulta = telefonesConsulta;
    }

    public Date getFreePassInicio() {
        return freePassInicio;
    }

    public void setFreePassInicio(Date freePassInicio) {
        this.freePassInicio = freePassInicio;
    }

    public Date getFreePassFim() {
        return freePassFim;
    }

    public void setFreePassFim(Date freePassFim) {
        this.freePassFim = freePassFim;
    }
}

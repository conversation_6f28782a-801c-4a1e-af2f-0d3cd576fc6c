/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import br.com.pactosolucoes.enumeradores.SituacaoRenovacaoEnum;
import java.text.ParseException;
import java.util.Date;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class RenovacaoAnaliticoVO {

    protected EmpresaVO empresa;
    protected ClienteVO cliente;
    protected ContratoVO contrato;
    protected String situacaoRenovacao;
    protected String planoRenovado;
    protected Boolean renovacaoAntecipada;
    protected Boolean renovacaoNoDia;
    protected Boolean renovacaoAtrasada;
    protected Boolean renovacaoAvencer;
    protected Boolean renovacaoVencido;
    protected Boolean renovacaoDesistente;

    public RenovacaoAnaliticoVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setCliente(new ClienteVO());
        setContrato(new ContratoVO());
        setEmpresa(new EmpresaVO());
        setRenovacaoAntecipada(false);
        setRenovacaoAtrasada(false);
        setRenovacaoAvencer(false);
        setRenovacaoDesistente(false);
        setRenovacaoNoDia(false);
        setRenovacaoVencido(false);

    }

    public void obterSituacaoRenovacao(ConfiguracaoSistemaVO cs) throws Exception {
        try {
            if (getContrato().getSituacaoRenovacao().equals("")) {
                if (obterSituacaoNaoRenovacaoAvencer(cs)) {
                    setSituacaoRenovacao(SituacaoRenovacaoEnum.NaoRenovadosAVencer.getSigla());
                    setRenovacaoAvencer(true);
                } else if (obterSituacaoNaoRenovacaoVencido(cs)) {
                    setSituacaoRenovacao(SituacaoRenovacaoEnum.NaoRenovadosVencidos.getSigla());
                    setRenovacaoVencido(true);
                } else if (obterSituacaoNaoRenovacaoDesistente(cs)) {
                    setSituacaoRenovacao(SituacaoRenovacaoEnum.NaoRenovadosDesistentes.getSigla());
                    setRenovacaoDesistente(true);
                } else {
                    setSituacaoRenovacao(SituacaoRenovacaoEnum.NaoRenovadosAVencer.getSigla());
                }
            } else if (getContrato().getSituacaoRenovacao().equals(SituacaoRenovacaoEnum.RenovacaoAntecipada.getSigla())) {
                setSituacaoRenovacao(SituacaoRenovacaoEnum.RenovacaoAntecipada.getSigla());
                setRenovacaoAntecipada(true);
            } else if (getContrato().getSituacaoRenovacao().equals(SituacaoRenovacaoEnum.RenovacaoNoDia.getSigla())) {
                setSituacaoRenovacao(SituacaoRenovacaoEnum.RenovacaoNoDia.getSigla());
                setRenovacaoNoDia(true);
            } else if (getContrato().getSituacaoRenovacao().equals(SituacaoRenovacaoEnum.RenovacaoAtrasada.getSigla())) {
                setSituacaoRenovacao(SituacaoRenovacaoEnum.RenovacaoAtrasada.getSigla());
                setRenovacaoAtrasada(true);
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public Boolean obterSituacaoNaoRenovacaoAvencer(ConfiguracaoSistemaVO cs) throws ParseException {
        Date dataInicioAvencer = negocio.comuns.utilitarias.Calendario.hoje();
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        if (getEmpresa().getCodigo().intValue() != 0 && getEmpresa().getNrDiasAvencer().intValue() != 0) {
            dataInicioAvencer = Uteis.obterDataAnterior(getContrato().getVigenciaAteAjustada(), getEmpresa().getNrDiasAvencer().intValue());
        } else {
            dataInicioAvencer = Uteis.obterDataAnterior(getContrato().getVigenciaAteAjustada(), cs.getNrDiasAvencer());
        }
        if ((Uteis.getCompareData(data, dataInicioAvencer) >= 0) &&
                (Uteis.getCompareData(data, getContrato().getDataPrevistaRenovar()) <= 0)) {
            return true;
        }
        return false;
    }

    public Boolean obterSituacaoNaoRenovacaoVencido(ConfiguracaoSistemaVO cs) throws ParseException {
        Date dataIniciovencido = negocio.comuns.utilitarias.Calendario.hoje();
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        if (getEmpresa().getCodigo().intValue() != 0 && getEmpresa().getCarenciaRenovacao().intValue() != 0) {
            dataIniciovencido = Uteis.obterDataFutura2(getContrato().getVigenciaAteAjustada(), getEmpresa().getCarenciaRenovacao().intValue());
        } else {
            dataIniciovencido = Uteis.obterDataFutura2(getContrato().getVigenciaAteAjustada(), cs.getCarenciaRenovacao());
        }
        if ((Uteis.getCompareData(data, dataIniciovencido) <= 0) &&
                (Uteis.getCompareData(data, getContrato().getVigenciaAteAjustada()) >= 0)) {
            return true;
        }
        return false;
    }

    public Boolean obterSituacaoNaoRenovacaoDesistente(ConfiguracaoSistemaVO cs) throws ParseException {
        Date dataIniciovencido = negocio.comuns.utilitarias.Calendario.hoje();
        Date data = negocio.comuns.utilitarias.Calendario.hoje();
        if (getEmpresa().getCodigo().intValue() != 0 && getEmpresa().getCarenciaRenovacao().intValue() != 0) {
            dataIniciovencido = Uteis.obterDataFutura2(getContrato().getVigenciaAteAjustada(), getEmpresa().getCarenciaRenovacao().intValue());
        } else {
            dataIniciovencido = Uteis.obterDataFutura2(getContrato().getVigenciaAteAjustada(), cs.getCarenciaRenovacao());
        }
        if ((Uteis.getCompareData(data, dataIniciovencido) >= 0)) {
            return true;
        }
        if (getContrato().getSituacao().equals("IN") || getContrato().getSituacao().equals("CA")) {
            return true;
        }
        return false;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getRenovacaoAntecipada() {
        return renovacaoAntecipada;
    }

    public void setRenovacaoAntecipada(Boolean renovacaoAntecipada) {
        this.renovacaoAntecipada = renovacaoAntecipada;
    }

    public Boolean getRenovacaoAtrasada() {
        return renovacaoAtrasada;
    }

    public void setRenovacaoAtrasada(Boolean renovacaoAtrasada) {
        this.renovacaoAtrasada = renovacaoAtrasada;
    }

    public Boolean getRenovacaoAvencer() {
        return renovacaoAvencer;
    }

    public void setRenovacaoAvencer(Boolean renovacaoAvencer) {
        this.renovacaoAvencer = renovacaoAvencer;
    }

    public Boolean getRenovacaoDesistente() {
        return renovacaoDesistente;
    }

    public void setRenovacaoDesistente(Boolean renovacaoDesistente) {
        this.renovacaoDesistente = renovacaoDesistente;
    }

    public Boolean getRenovacaoNoDia() {
        return renovacaoNoDia;
    }

    public void setRenovacaoNoDia(Boolean renovacaoNoDia) {
        this.renovacaoNoDia = renovacaoNoDia;
    }

    public Boolean getRenovacaoVencido() {
        return renovacaoVencido;
    }

    public void setRenovacaoVencido(Boolean renovacaoVencido) {
        this.renovacaoVencido = renovacaoVencido;
    }

    public String getSituacaoRenovacao_Apresentar() {
        if (situacaoRenovacao == null) {
            return situacaoRenovacao = "";
        }
        if (situacaoRenovacao.equals("RA")) {
            return "Não Renovado A Vencer";
        }
        if (situacaoRenovacao.equals("RV")) {
            return "Não Renovado Vencido";
        }
        if (situacaoRenovacao.equals("RD")) {
            return "Não Renovado Desistente";
        }
        if (situacaoRenovacao.equals("RT")) {
            return "Renovado Atrasado";
        }
        if (situacaoRenovacao.equals("ND")) {
            return "Renovado no Dia";
        }
        if (situacaoRenovacao.equals("AN")) {
            return "Renovado Antecipado";
        }
        return situacaoRenovacao;
    }

    public String getSituacaoRenovacao() {
        if (situacaoRenovacao == null) {
            return situacaoRenovacao = "";
        }
        return situacaoRenovacao;
    }

    public void setSituacaoRenovacao(String situacaoRenovacao) {
        this.situacaoRenovacao = situacaoRenovacao;
    }

    public String getPlanoRenovado() {
        if (planoRenovado == null) {
            planoRenovado = "";
        }
        return planoRenovado;
    }

    public void setPlanoRenovado(String planoRenovado) {
        this.planoRenovado = planoRenovado;
    }
}

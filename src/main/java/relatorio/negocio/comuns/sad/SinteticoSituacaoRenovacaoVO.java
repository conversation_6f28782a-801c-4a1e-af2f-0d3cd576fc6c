/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.sad;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class SinteticoSituacaoRenovacaoVO extends SuperVO {

    protected String nome;
    protected Boolean marcado;

    public SinteticoSituacaoRenovacaoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados(){
        setNome("");
        setMarcado(new Boolean(false));
    }

     public void adicionarObjClienteSituacaoVOs(List listaRenovacao, SinteticoSituacaoRenovacaoVO obj) throws Exception {
        int index = 0;
        Iterator i = listaRenovacao.iterator();
        while (i.hasNext()) {
            SinteticoSituacaoRenovacaoVO objExistente = (SinteticoSituacaoRenovacaoVO) i.next();
            if (objExistente.getNome().equals(obj.getNome())) {
                listaRenovacao.set(index, obj);
                return;
            }
            index++;
        }
        listaRenovacao.add(obj);
    }

    public String getNome_Apresentar(){
        if (nome == null) {
            return "";
        }
        if (nome.equals("RA")) {
            return "Não Renovados a Vencer";
        }
        if (nome.equals("RD")) {
            return "Não Renovados Desistentes";
        }
        if (nome.equals("RT")) {
            return  "Renovação Atrasada";
        }
        if (nome.equals("ND")) {
            return "Renovação do Dia";
        }
        if (nome.equals("RV")) {
            return "Não Renovados Vencidos";
        }
        if (nome.equals("CA")) {
        	return "Não Renovados Cancelado";
        }
        if (nome.equals("TR")) {
        	return "Não Renovados Trancado";
        }
        if (nome.equals("AN")) {
            return "Renovação Antecipada";
        }
        return nome;
    }

    public Boolean getMarcado() {
        return marcado;
    }

    public void setMarcado(Boolean marcado) {
        this.marcado = marcado;
    }

    public String getNome() {
        if(nome == null){
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }






}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class SituacaoContratoAnaliticoDWVO extends SuperVO {

    protected Integer codigo;
    protected Date dia;
    protected String situacao;
    protected EmpresaVO empresa;
    protected PlanoVO plano; 
    protected ClienteVO cliente;
    protected ContratoVO contrato;
    protected String foneCliente;
    protected String emailCliente;
    protected String modalidadeCliente;
    protected String enderecoCliente;
    protected String opcao; 
    protected Boolean apresentarSituacao;
    protected Boolean apresentarOpcaoAtivos;
    protected Boolean apresentarOpcaoInativos;
    protected Boolean apresentarOpcaoVisitante;
    protected Boolean apresentarTodasOpcoes;
   
  
  


    public SituacaoContratoAnaliticoDWVO() {
        super();
        inicializarDados();
    }

     public static void validarDados(SituacaoContratoAnaliticoDWVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDia() == null) {
            throw new ConsistirException("O campo DIA (Situação Contrato Sintétivo DW) deve ser informado.");
        }
        if (obj.getSituacao().equals("")) {
            throw new ConsistirException("O campo SITUAÇÃO (Situação Contrato Sintétivo DW) deve ser informado.");
        }
        if (obj.getEmpresa() == null ||obj.getEmpresa().getCodigo().intValue() == 0) {
            throw new ConsistirException("O campo EMPRESA (Situação Contrato Sintétivo DW) deve ser informado.");
        }
    }

    
    public void inicializarDados() {
        setCodigo(new Integer(0));
        setDia(negocio.comuns.utilitarias.Calendario.hoje());
        setSituacao("");
        setEmpresa(new EmpresaVO());
        setPlano(new PlanoVO());
        setCliente(new ClienteVO());
        setContrato(new ContratoVO());
        setFoneCliente("");
        setEmailCliente("");
        setModalidadeCliente("");
        setEnderecoCliente("");
        setApresentarOpcaoAtivos(new Boolean(false));
        setApresentarOpcaoInativos(new Boolean(false));
        setApresentarOpcaoVisitante(new Boolean(false));
        setApresentarTodasOpcoes(new Boolean(false));
        setApresentarSituacao(new Boolean(false));
      
    }

     public void realizarUpperCaseDados() {
        setSituacao(getSituacao().toUpperCase());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }
    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }

    public String getSituacao() {
        if(situacao == null){
            situacao = "";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public String getEmailCliente() {
        if(emailCliente == null){
            emailCliente = "";
        }
        return emailCliente;
    }

    public void setEmailCliente(String emailCliente) {
        this.emailCliente = emailCliente;
    }

    public String getFoneCliente() {
        if(foneCliente == null){
            foneCliente = "";
        }
        return foneCliente;
    }

    public void setFoneCliente(String foneCliente) {
        this.foneCliente = foneCliente;
    }

    public String getModalidadeCliente() {
        if(modalidadeCliente == null){
            modalidadeCliente = "";
        }
        return modalidadeCliente;
    }

    public void setModalidadeCliente(String modalidadeCliente) {
        this.modalidadeCliente = modalidadeCliente;
    }
    public Boolean getApresentarOpcaoAtivos() {
        return apresentarOpcaoAtivos;
    }

    public void setApresentarOpcaoAtivos(Boolean apresentarOpcaoAtivos) {
        this.apresentarOpcaoAtivos = apresentarOpcaoAtivos;
    }

    public Boolean getApresentarOpcaoInativos() {
        return apresentarOpcaoInativos;
    }

    public void setApresentarOpcaoInativos(Boolean apresentarOpcaoInativos) {
        this.apresentarOpcaoInativos = apresentarOpcaoInativos;
    }

    public Boolean getApresentarOpcaoVisitante() {
        return apresentarOpcaoVisitante;
    }

    public void setApresentarOpcaoVisitante(Boolean apresentarOpcaoVisitante) {
        this.apresentarOpcaoVisitante = apresentarOpcaoVisitante;
    }


    public Boolean getApresentarSituacao() {
        return apresentarSituacao;
    }

    public void setApresentarSituacao(Boolean apresentarSituacao) {
        this.apresentarSituacao = apresentarSituacao;
    }

    public Boolean getApresentarTodasOpcoes() {
        return apresentarTodasOpcoes;
    }

    public void setApresentarTodasOpcoes(Boolean apresentarTodasOpcoes) {
        this.apresentarTodasOpcoes = apresentarTodasOpcoes;
    }    

    public String getOpcao() {
        if(opcao == null){
            opcao = "";
        }
        return opcao;
    }

    public void setOpcao(String opcao) {
        this.opcao = opcao;
    }

    public String getEnderecoCliente() {
        if(enderecoCliente == null){
            enderecoCliente = "";
        }
        return enderecoCliente;
    }

    public void setEnderecoCliente(String enderecoCliente) {
        this.enderecoCliente = enderecoCliente;
    }
  
    
}

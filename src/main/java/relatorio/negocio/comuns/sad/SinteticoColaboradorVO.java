/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.sad;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class SinteticoColaboradorVO extends SuperVO{

    protected String nome;
    protected Boolean marcado;
    protected Integer chavePrimaria;
    private String tipoColaborador;

    public SinteticoColaboradorVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados(){
        setNome("");
        setMarcado(new Boolean(false));
        setChavePrimaria(new Integer(0));
    }

     public void adicionarObjClienteSituacaoVOs(List listaColaborador, SinteticoColaboradorVO obj) throws Exception {
        int index = 0;
        Iterator i = listaColaborador.iterator();
        while (i.hasNext()) {
            SinteticoColaboradorVO objExistente = (SinteticoColaboradorVO) i.next();
            if (objExistente.getChavePrimaria().equals(obj.getChavePrimaria()) && objExistente.getTipoColaborador().equals(obj.getTipoColaborador())) {
                listaColaborador.set(index, obj);
                return;
            }
            index++;
        }
        listaColaborador.add(obj);
    }

    public Integer getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(Integer chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public Boolean getMarcado() {
        return marcado;
    }

    public void setMarcado(Boolean marcado) {
        this.marcado = marcado;
    }

    public String getNome() {
        if(nome == null){
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

	/**
	 * @param tipoColaborador the tipoColaborador to set
	 */
	public void setTipoColaborador(String tipoColaborador) {
		this.tipoColaborador = tipoColaborador;
	}

	/**
	 * @return the tipoColaborador
	 */
	public String getTipoColaborador() {
		return tipoColaborador;
	}

}

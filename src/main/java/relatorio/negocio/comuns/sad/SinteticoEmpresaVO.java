/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class SinteticoEmpresaVO extends SuperVO {

    protected String nome;
    protected Boolean marcado;
    protected Integer chavePrimaria;

    public SinteticoEmpresaVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setNome("");
        setMarcado(new Boolean(false));
        setChavePrimaria(new Integer(0));
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>ClienteClassificacaoVO</code>
     * ao List <code>clienteClassificacaoVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>ClienteClassificacao</code> - getClassificacao().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>ClienteClassificacaoVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjClienteSituacaoVOs(List listaEmpresa, SinteticoEmpresaVO obj) throws Exception {
        int index = 0;
        Iterator i = listaEmpresa.iterator();
        while (i.hasNext()) {
            SinteticoEmpresaVO objExistente = (SinteticoEmpresaVO) i.next();
            if (objExistente.getChavePrimaria().equals(obj.getChavePrimaria())) {
                listaEmpresa.set(index, obj);
                return;
            }
            index++;
        }
        listaEmpresa.add(obj);
    }

    public Integer getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(Integer chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public Boolean getMarcado() {
        return marcado;
    }

    public void setMarcado(Boolean marcado) {
        this.marcado = marcado;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}

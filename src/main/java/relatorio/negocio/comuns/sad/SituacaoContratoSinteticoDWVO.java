/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 *
 * <AUTHOR>
 */
public class SituacaoContratoSinteticoDWVO extends SuperVO {

    protected Integer codigo;
    protected Date dia;
    protected String situacao;
    protected String vinculoCarteira;
    protected String plano;
    protected EmpresaVO empresa;
    protected Integer peso;    
    protected Integer qtdClientesAtivos;
    protected Integer qtdClientesAtivosTrancados;
    protected Integer qtdClientesAtivosTrancadosVencidos;
    protected Integer qtdClientesAtivosAvencer;
    protected Integer qtdClientesAtivosAtestado;
    protected Integer qtdClientesAtivosCarencia;
    protected Integer qtdClientesAtivosVencido;
    protected Integer qtdClientesInativos;
    protected Integer qtdClientesInativosCancelados;
    protected Integer qtdClientesInativosDesistencia;
    protected Integer qtdClientesVisitantes;
    protected Integer qtdClientesVisitantesFreePass;
    protected Integer qtdClientesVisitantesAulaAvulsa;
    protected Integer qtdClientesVisitantesDiaria;
    protected Integer qtdClientesAtivosNormal;
    protected Integer qtdClientesTotal;

    public SituacaoContratoSinteticoDWVO() {
        super();
        inicializarDados();
    }

    public static void validarDados(SituacaoContratoSinteticoDWVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if (obj.getDia() == null) {
            throw new ConsistirException("O campo DIA (Situação Contrato Sintétivo DW) deve ser informado.");
        }
        if (obj.getSituacao().equals("")) {
            throw new ConsistirException("O campo SITUÇÃO (Situação Contrato Sintétivo DW) deve ser informado.");
        }
        if ((obj.getEmpresa() == null) ||
                (obj.getEmpresa().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo EMPRESA (Situação Contrato Sintétivo DW) deve ser informado.");
        }
//        if (obj.getPeso().equals(0)) {
//            throw new ConsistirException("O campo PESO (Situação Contrato Sintétivo DW) deve ser informado.");
//        }
    }

    public void inicializarDados() {
        setCodigo(new Integer(0));
        setSituacao("");
        setEmpresa(new EmpresaVO());
        setPlano("");
        setVinculoCarteira("");
        setPeso(new Integer(0));        
    }

    public void realizarUpperCaseDados() {
        setSituacao(getSituacao().toUpperCase());
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Integer getPeso() {
        return peso;
    }

    public void setPeso(Integer peso) {
        this.peso = peso;
    }

    public String getPlano() {
        if (plano == null) {
            plano = "";
        }
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getVinculoCarteira() {
        if (vinculoCarteira == null) {
            vinculoCarteira = "";
        }
        return vinculoCarteira;
    }

    public void setVinculoCarteira(String vinculoCarteira) {
        this.vinculoCarteira = vinculoCarteira;
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            return "Todos";
        }
        if (situacao.equals("VI")) {
            return "Visitantes";
        }
        if (situacao.equals("PL")) {
            return "Visitantes - Free Pass";
        }
        if (situacao.equals("AA")) {
            return "Visitantes - Aula Avulsa";
        }
        if (situacao.equals("DI")) {
            return "Visitantes - Diária";
        }
        if (situacao.equals("IN")) {
            return "Inativos";
        }
        if (situacao.equals("CA")) {
            return "Inativos - Cancelados";
        }
        if (situacao.equals("DE")) {
            return "Inativos - Desistentes";
        }
        if (situacao.equals("AT")) {
            return "Ativos";
        }
        if (situacao.equals("NO")) {
            return "Ativos - Normais";
        }
        if (situacao.equals("AE")) {
            return "Ativos - Atestado";
        }
        if (situacao.equals("CR")) {
            return "Ativos - Férias";
        }
        if (situacao.equals("NO")) {
            return "Ativos - Normais";
        }
        if (situacao.equals("TR")) {
            return "Trancados";
        }
        if (situacao.equals("TV")) {
            return "Trancados Vencidos";
        }
        if (situacao.equals("AV")) {
            return "Ativos - A Vencer";
        }
        if (situacao.equals("VE")) {
            return "Inativos - Vencidos";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getQtdClientesAtivos() {
        return qtdClientesAtivos;
    }

    public void setQtdClientesAtivos(Integer qtdClientesAtivos) {
        this.qtdClientesAtivos = qtdClientesAtivos;
    }

    public Integer getQtdClientesAtivosTrancados() {
        return qtdClientesAtivosTrancados;
    }

    public void setQtdClientesAtivosTrancados(Integer qtdClientesAtivosTrancados) {
        this.qtdClientesAtivosTrancados = qtdClientesAtivosTrancados;
    }

    public Integer getQtdClientesInativos() {
        return qtdClientesInativos;
    }

    public void setQtdClientesInativos(Integer qtdClientesInativos) {
        this.qtdClientesInativos = qtdClientesInativos;
    }

    public Integer getQtdClientesInativosCancelados() {
        return qtdClientesInativosCancelados;
    }

    public void setQtdClientesInativosCancelados(Integer qtdClientesInativosCancelados) {
        this.qtdClientesInativosCancelados = qtdClientesInativosCancelados;
    }

    public Integer getQtdClientesInativosDesistencia() {
        return qtdClientesInativosDesistencia;
    }

    public void setQtdClientesInativosDesistencia(Integer qtdClientesInativosDesistencia) {
        this.qtdClientesInativosDesistencia = qtdClientesInativosDesistencia;
    }

    public Integer getQtdClientesTotal() {
        return qtdClientesTotal;
    }

    public void setQtdClientesTotal(Integer qtdClientesTotal) {
        this.qtdClientesTotal = qtdClientesTotal;
    }

    public Integer getQtdClientesVisitantes() {
        return qtdClientesVisitantes;
    }

    public void setQtdClientesVisitantes(Integer qtdClientesVisitantes) {
        this.qtdClientesVisitantes = qtdClientesVisitantes;
    }

    public Integer getQtdClientesVisitantesAulaAvulsa() {
        return qtdClientesVisitantesAulaAvulsa;
    }

    public void setQtdClientesVisitantesAulaAvulsa(Integer qtdClientesVisitantesAulaAvulsa) {
        this.qtdClientesVisitantesAulaAvulsa = qtdClientesVisitantesAulaAvulsa;
    }

    public Integer getQtdClientesVisitantesDiaria() {
        return qtdClientesVisitantesDiaria;
    }

    public void setQtdClientesVisitantesDiaria(Integer qtdClientesVisitantesDiaria) {
        this.qtdClientesVisitantesDiaria = qtdClientesVisitantesDiaria;
    }

    public Integer getQtdClientesVisitantesFreePass() {
        return qtdClientesVisitantesFreePass;
    }

    public void setQtdClientesVisitantesFreePass(Integer qtdClientesVisitantesFreePass) {
        this.qtdClientesVisitantesFreePass = qtdClientesVisitantesFreePass;
    }

    public Integer getQtdClientesAtivosAvencer() {
        return qtdClientesAtivosAvencer;
    }

    public void setQtdClientesAtivosAvencer(Integer qtdClientesAtivosAvencer) {
        this.qtdClientesAtivosAvencer = qtdClientesAtivosAvencer;
    }

    public Integer getQtdClientesAtivosTrancadosVencidos() {
        return qtdClientesAtivosTrancadosVencidos;
    }

    public void setQtdClientesAtivosTrancadosVencidos(Integer qtdClientesAtivosTrancadosVencidos) {
        this.qtdClientesAtivosTrancadosVencidos = qtdClientesAtivosTrancadosVencidos;
    }

    public Integer getQtdClientesAtivosVencido() {
        return qtdClientesAtivosVencido;
    }

    public void setQtdClientesAtivosVencido(Integer qtdClientesAtivosVencido) {
        this.qtdClientesAtivosVencido = qtdClientesAtivosVencido;
    }

    public Integer getQtdClientesAtivosNormal() {
        return qtdClientesAtivosNormal;
    }

    public void setQtdClientesAtivosNormal(Integer qtdClientesAtivosNormal) {
        this.qtdClientesAtivosNormal = qtdClientesAtivosNormal;
    }

    public Boolean getApresentarQtdTotal() {
        if (qtdClientesTotal != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdVisitante() {
        if (qtdClientesVisitantes != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdVisitanteFreePass() {
        if (qtdClientesVisitantesFreePass != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdVisitanteAulaAvulsa() {
        if (qtdClientesVisitantesAulaAvulsa != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdVisitanteDiaria() {
        if (qtdClientesVisitantesDiaria != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivo() {
        if (qtdClientesAtivosNormal != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivoTrancado() {
        if (qtdClientesAtivosTrancados != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivoTrancadoVencido() {
        if (qtdClientesAtivosTrancadosVencidos != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivoAvencer() {
        if (qtdClientesAtivosAvencer != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivoAtestado() {
        if (qtdClientesAtivosAtestado != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivoCarencia() {
        if (qtdClientesAtivosCarencia != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdAtivoVencido() {
        if (qtdClientesAtivosVencido != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdInativo() {
        if (qtdClientesInativos != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdInativoCancelado() {
        if (qtdClientesInativosCancelados != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Boolean getApresentarQtdInativoDesistente() {
        if (qtdClientesInativosDesistencia != 0) {
            return true;
        } else {
            return false;
        }
    }

    public Integer getQtdClientesAtivosAtestado() {
        return qtdClientesAtivosAtestado;
    }

    public void setQtdClientesAtivosAtestado(Integer qtdClientesAtivosAtestado) {
        this.qtdClientesAtivosAtestado = qtdClientesAtivosAtestado;
    }

    public Integer getQtdClientesAtivosCarencia() {
        return qtdClientesAtivosCarencia;
    }

    public void setQtdClientesAtivosCarencia(Integer qtdClientesAtivosCarencia) {
        this.qtdClientesAtivosCarencia = qtdClientesAtivosCarencia;
    }
}

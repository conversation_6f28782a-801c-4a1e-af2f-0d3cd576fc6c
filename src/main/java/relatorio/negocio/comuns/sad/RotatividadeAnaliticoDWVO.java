package relatorio.negocio.comuns.sad;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;

/**
 * Reponsável por manter os dados da entidade RotatividadeAnaliticoDW. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class RotatividadeAnaliticoDWVO extends SuperVO {

    protected Integer codigo;
    protected Date dia;
    protected Date dataAlteracaoRegistro;
    protected Integer mes;
    protected Integer ano;
    protected EmpresaVO empresa;
    protected ClienteVO cliente;
    protected ContratoVO contrato;
    protected Integer peso;
    protected String situacao;
    protected String foneCliente;
    protected Integer qtdMatriculado;
    protected Integer qtdMatriculadoHoje;
    protected Integer qtdRematriculado;
    protected Integer qtdRematriculadoHoje;        
    protected Integer qtdDesistente;
    protected Integer qtdDesistenteHoje;
    protected Integer qtdCancelado;
    protected Integer qtdCanceladoComFiltro;
    protected Integer qtdCanceladoHoje;
    protected Integer qtdCanceladoHojeComFiltro;

    protected Integer qtdTrancamento;
    protected Integer qtdTrancamentoHoje;
    protected Integer qtdRetornoTrancamento;
    protected Integer qtdRetornoTrancamentoHoje;
    protected Integer qtdVencidoMes;
    protected Integer qtdSaldo;
    protected Integer qtdSaldoDependentes;
    protected Integer qtdContratoTransferido;
    protected Integer qtdContratoTransferidoHoje;
    protected Integer qtdDependentesVinculados;
    protected Integer qtdDependentesVinculadosHoje;
    protected Integer qtdDependentesDesvinculados;
    protected Integer qtdDependentesDesvinculadosHoje;

    protected Integer qtdAgregadoresVinculadosMes;
    protected Integer qtdAgregadoresVinculadosHoje;

    protected Integer qtdAgregadorGympass;
    protected Integer qtdAgregadorGogood;
    protected Integer qtdAgregadorTotalpass;

    /**
     * Construtor padrão da classe <code>RotatividadeAnaliticoDW</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public RotatividadeAnaliticoDWVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>RotatividadeAnaliticoDWVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(RotatividadeAnaliticoDWVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
        setSituacao(getSituacao().toUpperCase());
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setDia(negocio.comuns.utilitarias.Calendario.hoje());
        setDataAlteracaoRegistro(negocio.comuns.utilitarias.Calendario.hoje());
        setMes(0);
        setEmpresa(new EmpresaVO());
        setCliente(new ClienteVO());
        setContrato(new ContratoVO());
        setPeso(0);
        setSituacao("");
        setQtdCancelado(0);
        setQtdCanceladoHoje(0);
        setQtdDesistente(0);
        setQtdDesistenteHoje(0);
        setQtdMatriculado(0);
        setQtdMatriculadoHoje(0);
        setQtdRematriculado(0);
        setQtdRematriculadoHoje(0);
        setQtdRetornoTrancamento(0);
        setQtdRetornoTrancamentoHoje(0);
        setQtdTrancamento(0);
        setQtdTrancamentoHoje(0);
        setQtdVencidoMes(0);
        setQtdSaldo(0);
        setAno(0);
        setQtdContratoTransferido(0);
        setQtdContratoTransferidoHoje(0);
        setQtdSaldoDependentes(0);
        setQtdDependentesVinculados(0);
        setQtdDependentesVinculadosHoje(0);
        setQtdDependentesDesvinculados(0);
        setQtdDependentesDesvinculadosHoje(0);

        setQtdAgregadoresVinculadosHoje(0);
        setQtdAgregadoresVinculadosMes(0);

    }


    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return (situacao);
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getPeso() {
        if (peso == null) {
            peso = 0;
        }
        return (peso);
    }

    public void setPeso(Integer peso) {
        this.peso = peso;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }


    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Integer getMes() {
        if (mes == null) {
            mes = 0;
        }
        return (mes);
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Date getDia() {
        if (dia == null) {
            dia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dia);
    }
    public String getDataBase_Apresentar(){
        DateFormat dfmt = new SimpleDateFormat("MMMM 'de' yyyy");
        Calendar cal = Calendario.getInstance();
        cal.setTime(getDia());
        return dfmt.format(cal.getTime());
    }
    public String getPeriodoDiasDataBase() {
        return " dia 01 até "+Uteis.obterDiaData(Uteis.obterUltimoDiaMes(getDia()));
    }

    public String getMesAnteriorFormatado() {
        Date mesAnterior = Calendario.somarMeses(getDia(), -1);
        return Calendario.getData(mesAnterior, "MMMM/YYYY");
    }

    public String getMesAtualFormatado() {
        return Calendario.getData(getDia(), "MMMM/YYYY");
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato padrão dd/mm/aaaa.
     */
    public String getDia_Apresentar() {
        return (Uteis.getData(dia));
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getQtdDesistente() {
        return qtdDesistente;
    }

    public void setQtdDesistente(Integer qtdDesistente) {
        this.qtdDesistente = qtdDesistente;
    }

    public Integer getQtdCancelado() {
        return qtdCancelado;
    }

    public void setQtdCancelado(Integer qtdCancelado) {
        this.qtdCancelado = qtdCancelado;
    }

    public Integer getQtdCanceladoComFiltro() {
        return qtdCanceladoComFiltro;
    }

    public void setQtdCanceladoComFiltro(Integer qtdCanceladoComFiltro) {
        this.qtdCanceladoComFiltro = qtdCanceladoComFiltro;
    }

    public Integer getQtdCanceladoHojeComFiltro() {
        return qtdCanceladoHojeComFiltro;
    }

    public void setQtdCanceladoHojeComFiltro(Integer qtdCanceladoHojeComFiltro) {
        this.qtdCanceladoHojeComFiltro = qtdCanceladoHojeComFiltro;
    }

    public Integer getQtdMatriculado() {
        return qtdMatriculado;
    }

    public void setQtdMatriculado(Integer qtdMatriculado) {
        this.qtdMatriculado = qtdMatriculado;
    }

    public Integer getQtdRematriculado() {
        return qtdRematriculado;
    }

    public void setQtdRematriculado(Integer qtdRematriculado) {
        this.qtdRematriculado = qtdRematriculado;
    }

    public Integer getQtdRetornoTrancamento() {
        return qtdRetornoTrancamento;
    }

    public void setQtdRetornoTrancamento(Integer qtdRetornoTrancamento) {
        this.qtdRetornoTrancamento = qtdRetornoTrancamento;
    }

    public Integer getQtdTrancamento() {
        return qtdTrancamento;
    }

    public void setQtdTrancamento(Integer qtdTrancamento) {
        this.qtdTrancamento = qtdTrancamento;
    }

    public Integer getQtdSaldo() {
        return qtdSaldo;
    }

    public String getCorSaldoMes() {
        if (qtdSaldo < 0) {
            return "bi-cor-vermelho";
        } else {
            return "bi-bg-box-verde bi-cor-verde";
        }
    }

    public String getCorSaldoDependentesMes() {
        if (qtdSaldoDependentes < 0) {
            return "bi-cor-vermelho";
        } else {
            return "bi-bg-box-verde bi-cor-verde";
        }
    }

    public String getSetaSaldoMes() {
        if (qtdSaldo < 0) {
            return "fa-icon-arrow-down";
        } else if (qtdSaldo == 0) {
            return "";
        } else {
            return "fa-icon-arrow-up";
        }
    }

    public String getSetaSaldoDependentesMes() {
        if (qtdSaldoDependentes < 0) {
            return "fa-icon-arrow-down";
        } else if (qtdSaldoDependentes == 0) {
            return "";
        } else {
            return "fa-icon-arrow-up";
        }
    }

    public void setQtdSaldo(Integer qtdSaldo) {
        this.qtdSaldo = qtdSaldo;
    }

   

    public Integer getQtdCanceladoHoje() {
        return qtdCanceladoHoje;
    }

    public void setQtdCanceladoHoje(Integer qtdCanceladoHoje) {
        this.qtdCanceladoHoje = qtdCanceladoHoje;
    }

    public Integer getQtdDesistenteHoje() {
        return qtdDesistenteHoje;
    }

    public void setQtdDesistenteHoje(Integer qtdDesistenteHoje) {
        this.qtdDesistenteHoje = qtdDesistenteHoje;
    }

    public Integer getQtdMatriculadoHoje() {
        return qtdMatriculadoHoje;
    }

    public void setQtdMatriculadoHoje(Integer qtdMatriculadoHoje) {
        this.qtdMatriculadoHoje = qtdMatriculadoHoje;
    }

    public Integer getQtdRematriculadoHoje() {
        return qtdRematriculadoHoje;
    }

    public void setQtdRematriculadoHoje(Integer qtdRematriculadoHoje) {
        this.qtdRematriculadoHoje = qtdRematriculadoHoje;
    }

    public Integer getQtdRetornoTrancamentoHoje() {
        return qtdRetornoTrancamentoHoje;
    }

    public void setQtdRetornoTrancamentoHoje(Integer qtdRetornoTrancamentoHoje) {
        this.qtdRetornoTrancamentoHoje = qtdRetornoTrancamentoHoje;
    }

    public Integer getQtdTrancamentoHoje() {
        return qtdTrancamentoHoje;
    }

    public void setQtdTrancamentoHoje(Integer qtdTrancamentoHoje) {
        this.qtdTrancamentoHoje = qtdTrancamentoHoje;
    }

    public Integer getQtdVencidoMes() {
        return qtdVencidoMes;
    }

    public void setQtdVencidoMes(Integer qtdVencidoMes) {
        this.qtdVencidoMes = qtdVencidoMes;
    }

    public String getFoneCliente() {
        if (foneCliente == null) {
            foneCliente = "";
        }
        return foneCliente;
    }

    public void setFoneCliente(String foneCliente) {
        this.foneCliente = foneCliente;
    }

    public Date getDataAlteracaoRegistro() {
        return dataAlteracaoRegistro;
    }

    public void setDataAlteracaoRegistro(Date dataAlteracaoRegistro) {
        this.dataAlteracaoRegistro = dataAlteracaoRegistro;
    }



    public Boolean getMatriculado() {
        return situacao.equals("MA");
    }

    public Boolean getRematriculado() {
        return situacao.equals("RE");
    }

    public Boolean getCancelado() {
        return situacao.equals("CA");
    }

    public Boolean getDesistente() {
        return situacao.equals("DE");
    }

    public Boolean getTrancado() {
        return situacao.equals("TR");
    }

    public Boolean getRetornoTrancado() {
        return situacao.equals("RT");
    }

    public Boolean getVencido() {
        return situacao.equals("VE");
    }

    public Boolean getRenovacao() {
        return situacao.equals("RN");
    }

    public Integer getQtdContratoTransferido() {
        return qtdContratoTransferido;
    }

    public void setQtdContratoTransferido(Integer qtdContratoTransferido) {
        this.qtdContratoTransferido = qtdContratoTransferido;
    }

    public Integer getQtdContratoTransferidoHoje() {
        return qtdContratoTransferidoHoje;
    }

    public void setQtdContratoTransferidoHoje(Integer qtdContratoTransferidoHoje) {
        this.qtdContratoTransferidoHoje = qtdContratoTransferidoHoje;
    }

    public Integer getQtdDependentesVinculados() {
        return qtdDependentesVinculados;
    }

    public void setQtdDependentesVinculados(Integer qtdDependentesVinculados) {
        this.qtdDependentesVinculados = qtdDependentesVinculados;
    }

    public Integer getQtdDependentesVinculadosHoje() {
        return qtdDependentesVinculadosHoje;
    }

    public void setQtdDependentesVinculadosHoje(Integer qtdDependentesVinculadosHoje) {
        this.qtdDependentesVinculadosHoje = qtdDependentesVinculadosHoje;
    }

    public Integer getQtdDependentesDesvinculados() {
        return qtdDependentesDesvinculados;
    }

    public void setQtdDependentesDesvinculados(Integer qtdDependentesDesvinculados) {
        this.qtdDependentesDesvinculados = qtdDependentesDesvinculados;
    }

    public Integer getQtdDependentesDesvinculadosHoje() {
        return qtdDependentesDesvinculadosHoje;
    }

    public void setQtdDependentesDesvinculadosHoje(Integer qtdDependentesDesvinculadosHoje) {
        this.qtdDependentesDesvinculadosHoje = qtdDependentesDesvinculadosHoje;
    }

    public Integer getQtdSaldoDependentes() {
        return qtdSaldoDependentes;
    }

    public void setQtdSaldoDependentes(Integer qtdSaldoDependentes) {
        this.qtdSaldoDependentes = qtdSaldoDependentes;
    }

    public Integer getQtdAgregadoresVinculadosHoje() {
        return qtdAgregadoresVinculadosHoje;
    }

    public void setQtdAgregadoresVinculadosHoje(Integer qtdAgregadoresVinculadosHoje) {
        this.qtdAgregadoresVinculadosHoje = qtdAgregadoresVinculadosHoje;
    }

    public Integer getQtdAgregadoresVinculadosMes() {
        return qtdAgregadoresVinculadosMes;
    }

    public void setQtdAgregadoresVinculadosMes(Integer qtdAgregadoresVinculadosMes) {
        this.qtdAgregadoresVinculadosMes = qtdAgregadoresVinculadosMes;
    }

    public Integer getQtdAgregadorTotalpass() {
        return qtdAgregadorTotalpass;
    }

    public void setQtdAgregadorTotalpass(Integer qtdAgregadorTotalpass) {
        this.qtdAgregadorTotalpass = qtdAgregadorTotalpass;
    }

    public Integer getQtdAgregadorGogood() {
        return qtdAgregadorGogood;
    }

    public void setQtdAgregadorGogood(Integer qtdAgregadorGogood) {
        this.qtdAgregadorGogood = qtdAgregadorGogood;
    }

    public Integer getQtdAgregadorGympass() {
        return qtdAgregadorGympass;
    }

    public void setQtdAgregadorGympass(Integer qtdAgregadorGympass) {
        this.qtdAgregadorGympass = qtdAgregadorGympass;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.sad;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ListaPrevisaoRenovacaoTO extends SuperTO {

    protected String mes;
    protected String ano;
    protected List<RenovacaoSinteticoVO> listaRenovacaoSinteticoVOs;
    protected Integer qtdRenovacoesAntecipadas;
    protected Integer qtdRenovacoesDia;
    protected Integer qtdRenovacoesAtrasadas;
    protected Integer qtdNRenovadosAVencer;
    protected Integer qtdNRenovadosVencidos;
    protected Integer qtdNRenovadosDesistentes;
    protected Integer qtdNRenovadosCancelados;
    protected Integer qtdNRenovadosTrancados;
    protected Integer qtdTotalRenovacoes;
    protected Integer qtdTotalNRenovados;
    protected Integer qtdTotalPrevisaoRenovacao;
    private DefaultPieDataset dataSetPizza;
    private DefaultCategoryDataset dataSetBarra;
    private DefaultPieDataset dataSetPizzaRenovados;
    private DefaultCategoryDataset dataSetBarraRenovados;
    private DefaultPieDataset dataSetPizzaNaoRenovados;
    private DefaultCategoryDataset dataSetBarraNaoRenovados;
    private Boolean pizza;
    private Boolean barra;
    private Boolean pizzaRenovados;
    private Boolean barraRenovados;
    private Boolean pizzaNaoRenovados;
    private Boolean barraNaoRenovados;

    public ListaPrevisaoRenovacaoTO() {
        setQtdRenovacoesAntecipadas(0);
        setQtdRenovacoesDia(0);
        setQtdRenovacoesAtrasadas(0);
        setQtdNRenovadosAVencer(0);
        setQtdNRenovadosVencidos(0);
        setQtdNRenovadosDesistentes(0);
        setQtdNRenovadosCancelados(0);
        setQtdNRenovadosTrancados(0);
        setQtdTotalRenovacoes(0);
        setQtdTotalNRenovados(0);
        setQtdTotalPrevisaoRenovacao(0);
        setListaRenovacaoSinteticoVOs(new ArrayList<RenovacaoSinteticoVO>());
        setDataSetBarra(new DefaultCategoryDataset());
        setDataSetPizza(new DefaultPieDataset());
        setDataSetBarraRenovados(new DefaultCategoryDataset());
        setDataSetPizzaRenovados(new DefaultPieDataset());
        setDataSetBarraNaoRenovados(new DefaultCategoryDataset());
        setDataSetPizzaNaoRenovados(new DefaultPieDataset());
        setBarra(false);
        setPizza(true);
        setBarraRenovados(false);
        setPizzaRenovados(true);
        setBarraNaoRenovados(false);
        setPizzaNaoRenovados(true);
    }

    public String getAno() {
        if (ano == null) {
            ano = "";
        }
        return ano;
    }

    public void setAno(String ano) {
        this.ano = ano;
    }

    public List<RenovacaoSinteticoVO> getListaRenovacaoSinteticoVOs() {
        return listaRenovacaoSinteticoVOs;
    }

    public void setListaRenovacaoSinteticoVOs(List<RenovacaoSinteticoVO> listaRenovacaoSinteticoVOs) {
        this.listaRenovacaoSinteticoVOs = listaRenovacaoSinteticoVOs;
    }

    public String getMes() {
        if (mes == null) {
            mes = "";
        }
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public Integer getQtdNRenovadosAVencer() {
        return qtdNRenovadosAVencer;
    }

    public void setQtdNRenovadosAVencer(Integer qtdNRenovadosAVencer) {
        this.qtdNRenovadosAVencer = qtdNRenovadosAVencer;
    }

    public Integer getQtdNRenovadosDesistentes() {
        return qtdNRenovadosDesistentes;
    }

    public void setQtdNRenovadosDesistentes(Integer qtdNRenovadosDesistentes) {
        this.qtdNRenovadosDesistentes = qtdNRenovadosDesistentes;
    }

    public Integer getQtdNRenovadosVencidos() {
        return qtdNRenovadosVencidos;
    }

    public void setQtdNRenovadosVencidos(Integer qtdNRenovadosVencidos) {
        this.qtdNRenovadosVencidos = qtdNRenovadosVencidos;
    }

    public Integer getQtdRenovacoesAntecipadas() {
        return qtdRenovacoesAntecipadas;
    }

    public void setQtdRenovacoesAntecipadas(Integer qtdRenovacoesAntecipadas) {
        this.qtdRenovacoesAntecipadas = qtdRenovacoesAntecipadas;
    }

    public Integer getQtdRenovacoesAtrasadas() {
        return qtdRenovacoesAtrasadas;
    }

    public void setQtdRenovacoesAtrasadas(Integer qtdRenovacoesAtrasadas) {
        this.qtdRenovacoesAtrasadas = qtdRenovacoesAtrasadas;
    }

    public Integer getQtdRenovacoesDia() {
        return qtdRenovacoesDia;
    }

    public void setQtdRenovacoesDia(Integer qtdRenovacoesDia) {
        this.qtdRenovacoesDia = qtdRenovacoesDia;
    }

    public Integer getQtdTotalNRenovados() {
        return qtdTotalNRenovados;
    }

    public void setQtdTotalNRenovados(Integer qtdTotalNRenovados) {
        this.qtdTotalNRenovados = qtdTotalNRenovados;
    }

    public Integer getQtdTotalRenovacoes() {
        return qtdTotalRenovacoes;
    }

    public void setQtdTotalRenovacoes(Integer qtdTotalRenovacoes) {
        this.qtdTotalRenovacoes = qtdTotalRenovacoes;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    public void setDataSetPizza(DefaultPieDataset dataSetPizza) {
        this.dataSetPizza = dataSetPizza;
    }

    public Boolean getBarra() {
        return barra;
    }

    public void setBarra(Boolean barra) {
        this.barra = barra;
    }

    public Boolean getPizza() {
        return pizza;
    }

    public void setPizza(Boolean pizza) {
        this.pizza = pizza;
    }

    public DefaultCategoryDataset getDataSetBarraNaoRenovados() {
        return dataSetBarraNaoRenovados;
    }

    public void setDataSetBarraNaoRenovados(DefaultCategoryDataset dataSetBarraNaoRenovados) {
        this.dataSetBarraNaoRenovados = dataSetBarraNaoRenovados;
    }

    public DefaultCategoryDataset getDataSetBarraRenovados() {
        return dataSetBarraRenovados;
    }

    public void setDataSetBarraRenovados(DefaultCategoryDataset dataSetBarraRenovados) {
        this.dataSetBarraRenovados = dataSetBarraRenovados;
    }

    public DefaultPieDataset getDataSetPizzaNaoRenovados() {
        return dataSetPizzaNaoRenovados;
    }

    public void setDataSetPizzaNaoRenovados(DefaultPieDataset dataSetPizzaNaoRenovados) {
        this.dataSetPizzaNaoRenovados = dataSetPizzaNaoRenovados;
    }

    public DefaultPieDataset getDataSetPizzaRenovados() {
        return dataSetPizzaRenovados;
    }

    public void setDataSetPizzaRenovados(DefaultPieDataset dataSetPizzaRenovados) {
        this.dataSetPizzaRenovados = dataSetPizzaRenovados;
    }

    public Boolean getBarraNaoRenovados() {
        return barraNaoRenovados;
    }

    public void setBarraNaoRenovados(Boolean barraNaoRenovados) {
        this.barraNaoRenovados = barraNaoRenovados;
    }

    public Boolean getBarraRenovados() {
        return barraRenovados;
    }

    public void setBarraRenovados(Boolean barraRenovados) {
        this.barraRenovados = barraRenovados;
    }

    public Boolean getPizzaNaoRenovados() {
        return pizzaNaoRenovados;
    }

    public void setPizzaNaoRenovados(Boolean pizzaNaoRenovados) {
        this.pizzaNaoRenovados = pizzaNaoRenovados;
    }

    public Boolean getPizzaRenovados() {
        return pizzaRenovados;
    }

    public void setPizzaRenovados(Boolean pizzaRenovados) {
        this.pizzaRenovados = pizzaRenovados;
    }

    public Integer getQtdNRenovadosCancelados() {
        return qtdNRenovadosCancelados;
    }

    public void setQtdNRenovadosCancelados(Integer qtdNRenovadosCancelados) {
        this.qtdNRenovadosCancelados = qtdNRenovadosCancelados;
    }

    public Integer getQtdNRenovadosTrancados() {
        return qtdNRenovadosTrancados;
    }

    public void setQtdNRenovadosTrancados(Integer qtdNRenovadosTrancados) {
        this.qtdNRenovadosTrancados = qtdNRenovadosTrancados;
    }

    public Integer getQtdTotalPrevisaoRenovacao() {
        return qtdTotalPrevisaoRenovacao;
    }

    public void setQtdTotalPrevisaoRenovacao(Integer qtdTotalPrevisaoRenovacao) {
        this.qtdTotalPrevisaoRenovacao = qtdTotalPrevisaoRenovacao;
    }

    public double getQtdTotalRenovacoesPercentual() {
        if (qtdTotalPrevisaoRenovacao == 0)
            return 0.0;
        double total = qtdTotalPrevisaoRenovacao;
        double renovados = qtdTotalRenovacoes;
        return Uteis.arredondarForcando2CasasDecimais((renovados * 100) / total);
    }

    public double getQtdTotalNRenovadosPercentual() {
        if (qtdTotalPrevisaoRenovacao == 0)
            return 0.0;
        double total = qtdTotalPrevisaoRenovacao;
        double nRenovados = qtdTotalNRenovados;
        return Uteis.arredondarForcando2CasasDecimais((nRenovados * 100) / total);
    }

    public boolean isAlertRenovacoes() {
        return getQtdTotalRenovacoesPercentual() < 50;
    }
}

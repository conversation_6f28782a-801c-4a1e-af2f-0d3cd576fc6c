package relatorio.negocio.comuns.sad;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Iterator;
import java.util.List;

public class SinteticoDuracaoTO extends SuperTO {

    protected String nome;
    protected Boolean marcado;
    protected Integer chavePrimaria;

    public SinteticoDuracaoTO() {
        super();
        inicializarDados();
    }

    public void inicializarDados(){
        setNome("");
        setMarcado(new Boolean(false));
        setChavePrimaria(new Integer(0));
    }

    public void adicionarObjSituacaoDuracaoTO(List<SinteticoDuracaoTO> lista, SinteticoDuracaoTO obj) {
        int index = 0;
        int indexAdd = 0; //ordenação
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            SinteticoDuracaoTO objExistente = (SinteticoDuracaoTO) i.next();
            if (objExistente.getChavePrimaria().equals(obj.getChavePrimaria())) {
                lista.set(index, obj);
                return;
            }
            if(objExistente.getChavePrimaria() < obj.getChavePrimaria()) {
                indexAdd++;
            }
            index++;
        }
        lista.add(indexAdd, obj);
    }

    public Integer getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(Integer chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public Boolean getMarcado() {
        return marcado;
    }

    public void setMarcado(Boolean marcado) {
        this.marcado = marcado;
    }

    public String getNome() {
        if(nome == null){
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }


}

package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.util.ArrayList;
import java.util.List;

public class AuxiliarReciboPagamentoRelTO extends SuperTO {

    private ReciboPagamentoRelTO recibo = new ReciboPagamentoRelTO();
    private int sequencial = 0;

    public int getSequencial() {
        return sequencial;
    }

    public void setSequencial(int sequencial) {
        this.sequencial = sequencial;
    }

    public JRDataSource getReciboDatasource() {
        List<ReciboPagamentoRelTO> recibos = new ArrayList<ReciboPagamentoRelTO>();
        recibos.add(recibo);
        return new JRBeanCollectionDataSource(recibos);
    }

    public ReciboPagamentoRelTO getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamentoRelTO recibo) {
        this.recibo = recibo;
    }
}

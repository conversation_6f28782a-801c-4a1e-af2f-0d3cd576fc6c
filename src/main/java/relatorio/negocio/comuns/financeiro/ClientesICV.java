package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;

import java.util.Date;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;

/**
 * <AUTHOR>
 */
public class ClientesICV extends SuperTO {
    private ClienteVO cliente;
    private String duracao;
    private TipoContratoEnum tipoContrato;
    private ColaboradorVO consultor;
    private Date dataLancamento;
    private Date inicioContrato;
    private Date dataLancamentoContratoNovo;
    private Date dataFinalContratoAntigo;
    private Integer quantidadeDiasParaRenovar;
    
    public String getMatriculaApresentar() {
        return getCliente().getMatricula();
    }

    public String getNomeApresentar() {
        return getCliente().getPessoa().getNome();
    }

    public String getSituacaoApresentar() {
        return getCliente().getSituacao_Apresentar();
    }

    public String getConsultorApresentar() {
        return getConsultor().getPessoa().getNome();
    }

    public String getContrato() {
        return getTipoContrato().getDescricao();
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public String getDuracao() {
        return duracao;
    }

    public void setDuracao(int duracao) {
        this.duracao = duracao + (duracao == 1 ? " Mês" : " Meses");
    }

    public TipoContratoEnum getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(TipoContratoEnum tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }
    
    
    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public Date getInicioContrato() {
        return inicioContrato;
    }

    public void setInicioContrato(Date inicioContrato) {
        this.inicioContrato = inicioContrato;
    }
    
    public String getDataLancamento_Apresentar() {
        return Uteis.getDataComHora(dataLancamento);
    }
    
      public String getInicioContrato_Apresentar() {
        return Uteis.getDataComHora(inicioContrato);
    }

    public void setDataFinalContratoAntigo(Date dataFinalContratoAntigo) {
        this.dataFinalContratoAntigo = dataFinalContratoAntigo;
    }

    public Date getDataFinalContratoAntigo() {
        return dataFinalContratoAntigo;
    }

    public void setDataLancamentoContratoNovo(Date dataLancamentoContratoNovo) {
        this.dataLancamentoContratoNovo = dataLancamentoContratoNovo;
    }

    public Date getDataLancamentoContratoNovo() {
        return dataLancamentoContratoNovo;
    }

    public void setQuantidadeDiasParaRenovar(Integer quantidadeDiasParaRenovar) {
        this.quantidadeDiasParaRenovar = quantidadeDiasParaRenovar;
    }

    public Integer getQuantidadeDiasParaRenovar() {
        return quantidadeDiasParaRenovar;
    }
    
}
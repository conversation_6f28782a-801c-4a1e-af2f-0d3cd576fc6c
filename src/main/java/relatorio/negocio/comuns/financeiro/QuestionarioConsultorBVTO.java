/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.basico.QuestionarioClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Log;

/**
 *
 * <AUTHOR>
 */
public class QuestionarioConsultorBVTO {
    
    private QuestionarioClienteVO questionarioClienteVO;
    
    private LogVO log;

    public QuestionarioClienteVO getQuestionarioClienteVO() {
        return questionarioClienteVO;
    }

    public void setQuestionarioClienteVO(QuestionarioClienteVO questionarioClienteVO) {
        this.questionarioClienteVO = questionarioClienteVO;
    }

    public LogVO getLog() {
        return log;
    }

    public void setLog(LogVO log) {
        this.log = log;
    }

    public String getDataAlteracao_Apresentar(){
        return Uteis.getData(log.getDataAlteracao());
    }
    
    public String getTipoBV_Apresentar(){
        return questionarioClienteVO.getTipoBV().getDescricao();
    }
    
    public String getNomeCliente(){
        return questionarioClienteVO.getCliente().getPessoa().getNome();
    }
    
    public String getMatriculaCliente(){
        return questionarioClienteVO.getCliente().getMatricula();
    }
    
    public String getValorCampoAnterior(){
        return log.getValorCampoAnterior();
    }
    
    public String getValorCampoAlterado(){
        return log.getValorCampoAlterado();
    }
    
    public String getResponsavelAlteracao(){
        return log.getResponsavelAlteracao();
    }
}

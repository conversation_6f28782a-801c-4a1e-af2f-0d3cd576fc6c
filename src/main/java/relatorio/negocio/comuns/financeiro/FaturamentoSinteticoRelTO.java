package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by glauco on 10/02/14.
 */
public class FaturamentoSinteticoRelTO extends SuperTO {

    private String matriculaCliente;
    private String nomeCliente;
    private Date dataCadastroCliente;
    private String nomeResponsavelLancamento;
    private String nomeResponsavelRecebimento;
    private String descricaoProduto;
    private Integer codContrato;
    private String dataInicio;
    private String dataTermino;
    private Integer duracaoPlano;
    private String modalidades;
    private String nomePlano;
    private String situacaoContrato;
    private Date dataLancamentoProduto;
    private Double valorCompetencia;
    private String FormaPagApresentar;
    private String condicaoPagamento;
    private String nomeEmpresa;
    private String turma;
    private String categoria;

    public String getFormaPagApresentar() {
        return FormaPagApresentar;
    }

    public void setFormaPagApresentar(String formaPagApresentar) {
        FormaPagApresentar = formaPagApresentar;
    }

    private Integer codCliente;
    private Integer codPlano;

    public Integer getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(Integer codCliente) {
        this.codCliente = codCliente;
    }

    public Integer getCodContrato() {
        return codContrato;
    }

    public void setCodContrato(Integer codContrato) {
        this.codContrato = codContrato;
    }

    public Double getValorCompetencia() {
        return valorCompetencia;
    }

    public void setValorCompetencia(Double valorCompetencia) {
        this.valorCompetencia = valorCompetencia;
    }

    public Date getDataLancamentoProduto() {
        return dataLancamentoProduto;
    }

    public void setDataLancamentoProduto(Date dataLancamentoProduto) {
        this.dataLancamentoProduto = dataLancamentoProduto;
    }

    public String getDataLancamentoProdutoApresentar() {
        if (getDataLancamentoProduto() != null) {
            return Uteis.getData(getDataLancamentoProduto());
        } else {
            return "";
        }
    }

    public Integer getCodPlano() {
        return codPlano;
    }

    public void setCodPlano(Integer codPlano) {
        this.codPlano = codPlano;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Integer getDuracaoPlano() {
        return duracaoPlano;
    }

    public void setDuracaoPlano(Integer duracaoPlano) {
        this.duracaoPlano = duracaoPlano;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public Date getDataCadastroCliente() {
        return dataCadastroCliente;
    }

    public void setDataCadastroCliente(Date dataCadastroCliente) {
        this.dataCadastroCliente = dataCadastroCliente;
    }

    public String getDataCadastroClienteApresentar() {
        if (getDataCadastroCliente() != null) {
            return Uteis.getData(getDataCadastroCliente());
        } else {
            return "";
        }
    }

    public String getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(String dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(String dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getNomeResponsavelLancamento() {
        return nomeResponsavelLancamento;
    }

    public void setNomeResponsavelLancamento(String nomeResponsavelLancamento) {
        this.nomeResponsavelLancamento = nomeResponsavelLancamento;
    }

    public String getNomeResponsavelRecebimento() {
        return nomeResponsavelRecebimento;
    }

    public void setNomeResponsavelRecebimento(String nomeResponsavelRecebimento) {
        this.nomeResponsavelRecebimento = nomeResponsavelRecebimento;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getCondicaoPagamento() {
        return condicaoPagamento;
    }

    public void setCondicaoPagamento(String condicaoPagamento) {
        this.condicaoPagamento = condicaoPagamento;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }
}
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created by johny<PERSON> on 29/03/2017.
 */
public class DescontoBoletoTO extends SuperTO{

    private Integer diaMaximoPagamentoDesconto;

    private Double porcentagemDesconto;
    private Double valorDesconto;

    public DescontoBoletoTO(){}

    public DescontoBoletoTO(Integer diaMaximoPagamentoDesconto,Double porcentagemDesconto){
        setDiaMaximoPagamentoDesconto(diaMaximoPagamentoDesconto);
        setPorcentagemDesconto(porcentagemDesconto);
    }

    public Integer getDiaMaximoPagamentoDesconto() {
        return diaMaximoPagamentoDesconto;
    }

    public void setDiaMaximoPagamentoDesconto(Integer diaMaximoPagamentoDesconto) {
        this.diaMaximoPagamentoDesconto = diaMaximoPagamentoDesconto;
    }

    public Double getPorcentagemDesconto() {
        return porcentagemDesconto;
    }

    public void setPorcentagemDesconto(Double porcentagemDesconto) {
        this.porcentagemDesconto = porcentagemDesconto;
    }

    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

}

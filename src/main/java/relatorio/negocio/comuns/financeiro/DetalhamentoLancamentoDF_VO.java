/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.financeiro;

import annotations.arquitetura.Lista;
import br.com.pactosolucoes.comuns.util.Formatador;
import relatorio.negocio.jdbc.financeiro.ContratoModalidadePercentual;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.ProdutoVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

/**
 *
 * <AUTHOR>
 */
public class DetalhamentoLancamentoDF_VO extends SuperVO{

    private int movPagamento;
    private int reciboPagamento;
    private String dataPagamento;
    private String pessoaPagador;
    private double valorPlanoMensal;
    private ContratoVO contratoVO;
    @Lista
    private List<ContratoModalidadePercentual> listaContratoModalidade;
    private List<ProdutoVO> listaProdutos;
    private List<MovParcelaVO> listaMovParcela;
    private List<FormaPagamentoVO> listaFormaPagamento;
    private List<RateioIntegracaoTO> listaRateios = new ArrayList<RateioIntegracaoTO>();
    private List<LancamentoDF> listaLancamentoDF = new ArrayList<LancamentoDF>();
    private double valorPagamento;
    private String nomeCliente;
    private String matriculaCliente;
    private double totalPagoPlano;
    private double valorLancamento;
    private double valorMovProduto;
    private double valorMultaJuros;
    private String produtosPagos = "";
    private Integer empresa = 0;
    private String competencia = "";
    

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public List<ContratoModalidadePercentual> getListaContratoModalidade() {
        return listaContratoModalidade;
    }

    public void setListaContratoModalidade(List<ContratoModalidadePercentual> listaContratoModalidade) {
        this.listaContratoModalidade = listaContratoModalidade;
    }

    public List<MovParcelaVO> getListaMovParcela() {
        return listaMovParcela;
    }

    public void setListaMovParcela(List<MovParcelaVO> listaMovParcela) {
        this.listaMovParcela = listaMovParcela;
    }

    public List<ProdutoVO> getListaProdutos() {
        return listaProdutos;
    }

    public void setListaProdutos(List<ProdutoVO> listaProdutos) {
        this.listaProdutos = listaProdutos;
    }

    public List<RateioIntegracaoTO> getListaRateios() {
        return listaRateios;
    }

    public void setListaRateios(List<RateioIntegracaoTO> listaRateios) {
        this.listaRateios = listaRateios;
    }

    public int getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(int movPagamento) {
        this.movPagamento = movPagamento;
    }


    public String getPessoaPagador() {
        return pessoaPagador;
    }

    public void setPessoaPagador(String pessoaPagador) {
        this.pessoaPagador = pessoaPagador;
    }

    public double getValorPlanoMensal() {
        return valorPlanoMensal;
    }

    public void setValorPlanoMensal(double valorPlanoMensal) {
        this.valorPlanoMensal = valorPlanoMensal;
    }

    public List<FormaPagamentoVO> getListaFormaPagamento() {
        return listaFormaPagamento;
    }

    public void setListaFormaPagamento(List<FormaPagamentoVO> listaFormaPagamento) {
        this.listaFormaPagamento = listaFormaPagamento;
    }

    public double getValorPagamento() {
        return valorPagamento;
    }

    public void setValorPagamento(double valorPagamento) {
        this.valorPagamento = valorPagamento;
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public double getTotalPagoPlano() {
        return totalPagoPlano;
    }

    public void setTotalPagoPlano(double totalPagoPlano) {
        this.totalPagoPlano = totalPagoPlano;
    }

    public double getValorLancamento() {
        return valorLancamento;
    }

    public void setValorLancamento(double valorLancamento) {
        this.valorLancamento = valorLancamento;
    }

    public double getValorMovProduto() {
        return valorMovProduto;
    }

    public void setValorMovProduto(double valorMovProduto) {
        this.valorMovProduto = valorMovProduto;
    }

    public double getValorMultaJuros() {
        return valorMultaJuros;
    }

    public void setValorMultaJuros(double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public List<LancamentoDF> getListaLancamentoDF() {
        return listaLancamentoDF;
    }

    public void setListaLancamentoDF(List<LancamentoDF> listaLancamentoDF) {
        this.listaLancamentoDF = listaLancamentoDF;
    }

    public String getTotalPagoPlano_Apresentar(){
        return Formatador.formatarValorMonetario(totalPagoPlano);
    }
    
	public JRDataSource getDsModalidades() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getListaContratoModalidade().toArray());
        return jr1;
    }

	public void setProdutosPagos(String produtosPagos) {
		this.produtosPagos = produtosPagos;
	}

	public String getProdutosPagos() {
		return produtosPagos;
	}

	public void setReciboPagamento(int recibopagamento) {
		this.reciboPagamento = recibopagamento;
	}

	public int getReciboPagamento() {
		return reciboPagamento;
	}

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
    
    public String getCompetencia() {
        return competencia;
    }

    public void setCompetencia(String competencia) {
        this.competencia = competencia;
    }

    public boolean isApresentarValorMultaJuros() {
        return getValorMultaJuros() > 0.0;
    }
}

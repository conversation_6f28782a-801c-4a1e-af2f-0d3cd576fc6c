/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import relatorio.negocio.jdbc.financeiro.LancamentoDF;
import relatorio.negocio.jdbc.financeiro.MesProcessar;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class TotalizadorMesDF implements Serializable {

    private MesProcessar mesProcessar;
    private Double totalEntradaNivel = 0.0;
    private Double totalSaidaNivel = 0.0;
    private Double totalNivel = 0.0; // (totalEntradaNivel - totalSaidaNivel)
    private Double totalEntradaNaoAtribuido = 0.0;
    private Double totalSaidaNaoAtribuido = 0.0;
    private Double totalNaoAtribuido = 0.0;
    private Set<LancamentoDF> listaLancamentos = new HashSet<LancamentoDF>();
    private Set<LancamentoDF> listaLancamentosNaoAtribuido = new HashSet<LancamentoDF>();
    private Map<TipoAutorizacaoCobrancaEnum, Double> totaisPorTipoAutorizacaoCobranca = new HashMap<TipoAutorizacaoCobrancaEnum, Double>();

    public Double getTotalNivel() {
        return totalNivel;
    }

    public void setTotalNivel(Double valor) {
        this.totalNivel += valor;
        if (valor <0)
          this.totalSaidaNivel += valor;
        else
          this.totalEntradaNivel += valor;
    }

    public double getTotalNaoAtribuido() {
        return totalNaoAtribuido;
    }

    public void setTotalNaoAtribuido(Double valor) {
        this.totalNaoAtribuido += valor;
        if (valor <0)
          this.totalSaidaNaoAtribuido += valor;
        else
          this.totalEntradaNaoAtribuido += valor;

    }

    public Set<LancamentoDF> getListaLancamentos() {
        return listaLancamentos;
    }

    public void setListaLancamentos(Set<LancamentoDF> listaLancamentos) {
        this.listaLancamentos = listaLancamentos;
    }

    public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (!(obj instanceof TotalizadorMesDF))
            return false;
       return ((((TotalizadorMesDF)obj).getMesProcessar().getMesAno().equals(this.getMesProcessar().getMesAno())));
    }

    public MesProcessar getMesProcessar() {
        return mesProcessar;
    }

    public void setMesProcessar(MesProcessar mesProcessar) {
        this.mesProcessar = mesProcessar;
    }

    public String getValorApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalNivel);
        return (this.totalNivel < 0.0 ? "-" : "") + valor;
    }

    public String getValorApresentarTela() {
        return getValorApresentar().contains("-") ? getValorApresentar().replace("-", "(-)") : getValorApresentar();
    }

    public String getCorLinkTotalNivel(){
        if (this.totalNivel >= 0 )
            return "black";
         else
            return "red";
    }


    public String getValorNaoAtribuidoApresentar() {
      String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalNaoAtribuido);
      return valor;
    }


    public Set<LancamentoDF> getListaLancamentosNaoAtribuido() {
        return listaLancamentosNaoAtribuido;
    }

    public void setListaLancamentosNaoAtribuido(Set<LancamentoDF> listaLancamentosNaoAtribuido) {
        this.listaLancamentosNaoAtribuido = listaLancamentosNaoAtribuido;
    }

    public Double getTotalEntradaNaoAtribuido() {
        return totalEntradaNaoAtribuido;
    }

    public void setTotalEntradaNaoAtribuido(Double totalEntradaNaoAtribuido) {
        this.totalEntradaNaoAtribuido = totalEntradaNaoAtribuido;
    }

    public Double getTotalEntradaNivel() {
        return totalEntradaNivel;
    }

    public void setTotalEntradaNivel(Double totalEntradaNivel) {
        this.totalEntradaNivel = totalEntradaNivel;
    }

    public Double getTotalSaidaNaoAtribuido() {
        return totalSaidaNaoAtribuido;
    }

    public void setTotalSaidaNaoAtribuido(Double totalSaidaNaoAtribuido) {
        this.totalSaidaNaoAtribuido = totalSaidaNaoAtribuido;
    }

    public Double getTotalSaidaNivel() {
        return totalSaidaNivel;
    }

    public void setTotalSaidaNivel(Double totalSaidaNivel) {
        this.totalSaidaNivel = totalSaidaNivel;
    }


    public Map<TipoAutorizacaoCobrancaEnum, Double> getTotaisPorTipoAutorizacaoCobranca() {
        if(totaisPorTipoAutorizacaoCobranca.size() == 0){
            for (TipoAutorizacaoCobrancaEnum tipo: TipoAutorizacaoCobrancaEnum.values()) {
                totaisPorTipoAutorizacaoCobranca.put(tipo, 0.0);
            }
        }
        return totaisPorTipoAutorizacaoCobranca;
    }

    public void setTotaisPorTipoAutorizacaoCobranca(Map<TipoAutorizacaoCobrancaEnum, Double> totaisPorTipoAutorizacaoCobranca) {
        this.totaisPorTipoAutorizacaoCobranca = totaisPorTipoAutorizacaoCobranca;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import relatorio.negocio.jdbc.financeiro.MesProcessar;

import java.io.Serializable;

public class TotalizadorMesRelOrcamentario implements Serializable {

    private MesProcessar mesProcessar;
    private Double totalPrevistoMes = 0.0;
    private Double totalRealizadoMes = 0.0;
    private Double totalReceitasPrevistoMes = 0.0;
    private Double totalReceitasRealizadoes = 0.0;
    private Double totalDespesasPrevistoMes = 0.0;
    private Double totalDespesasRealizadoMes = 0.0;
    private Double totalInvestimentosPrevistoMes = 0.0;
    private Double totalInvestimentosRealizadoMes = 0.0;
    private Double totalInvestimentosDespesasPrevistoMes = 0.0;
    private Double totalInvestimentosDespesasRealizadoMes = 0.0;
    private Double totalSemInvestimentoPrevistoMes = 0.0;
    private Double totalSemInvestimentoRealizadoMes = 0.0;
    private Double totalComInvestimentoPrevistoMes = 0.0;
    private Double totalComInvestimentoRealizadoMes = 0.0;

    public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (!(obj instanceof TotalizadorMesRelOrcamentario))
            return false;
       return ((((TotalizadorMesRelOrcamentario)obj).getMesProcessar().getMesAno().equals(this.getMesProcessar().getMesAno())));
    }

    public MesProcessar getMesProcessar() {
        return mesProcessar;
    }

    public void setMesProcessar(MesProcessar mesProcessar) {
        this.mesProcessar = mesProcessar;
    }

    public String getTotalPrevistoApresentar() {
      String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalPrevistoMes);
      return valor;
    }

    public String getTotalRealizadoApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalRealizadoMes);
        return valor;
    }

    public Double getTotalPrevistoMes() {
        return totalPrevistoMes;
    }

    public void setTotalPrevistoMes(Double totalPrevisto) {
        this.totalPrevistoMes = totalPrevisto;
    }

    public Double getTotalRealizadoMes() {
        return totalRealizadoMes;
    }

    public void setTotalRealizadoMes(Double totalRealizado) {
        this.totalRealizadoMes = totalRealizado;
    }

    public String getTotalPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalPrevistoMes());
    }

    public String getTotalRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalRealizadoMes());
    }

    public Double getTotalReceitasPrevistoMes() {
        return totalReceitasPrevistoMes;
    }

    public void setTotalReceitasPrevistoMes(Double totalReceitasPrevistoMes) {
        this.totalReceitasPrevistoMes = totalReceitasPrevistoMes;
    }

    public Double getTotalReceitasRealizadoes() {
        return totalReceitasRealizadoes;
    }

    public void setTotalReceitasRealizadoes(Double totalReceitasRealizadoes) {
        this.totalReceitasRealizadoes = totalReceitasRealizadoes;
    }

    public Double getTotalDespesasPrevistoMes() {
        return totalDespesasPrevistoMes;
    }

    public void setTotalDespesasPrevistoMes(Double totalDespesasPrevistoMes) {
        this.totalDespesasPrevistoMes = totalDespesasPrevistoMes;
    }

    public Double getTotalDespesasRealizadoMes() {
        return totalDespesasRealizadoMes;
    }

    public void setTotalDespesasRealizadoMes(Double totalDespesasRealizadoMes) {
        this.totalDespesasRealizadoMes = totalDespesasRealizadoMes;
    }

    public Double getTotalInvestimentosPrevistoMes() {
        return totalInvestimentosPrevistoMes;
    }

    public void setTotalInvestimentosPrevistoMes(Double totalInvestimentosPrevistoMes) {
        this.totalInvestimentosPrevistoMes = totalInvestimentosPrevistoMes;
    }

    public Double getTotalInvestimentosRealizadoMes() {
        return totalInvestimentosRealizadoMes;
    }

    public void setTotalInvestimentosRealizadoMes(Double totalInvestimentosRealizadoMes) {
        this.totalInvestimentosRealizadoMes = totalInvestimentosRealizadoMes;
    }

    public Double getTotalInvestimentosDespesasPrevistoMes() {
        return totalInvestimentosDespesasPrevistoMes;
    }

    public void setTotalInvestimentosDespesasPrevistoMes(Double totalInvestimentosDespesasPrevistoMes) {
        this.totalInvestimentosDespesasPrevistoMes = totalInvestimentosDespesasPrevistoMes;
    }

    public Double getTotalInvestimentosDespesasRealizadoMes() {
        return totalInvestimentosDespesasRealizadoMes;
    }

    public void setTotalInvestimentosDespesasRealizadoMes(Double totalInvestimentosDespesasRealizadoMes) {
        this.totalInvestimentosDespesasRealizadoMes = totalInvestimentosDespesasRealizadoMes;
    }

    public Double getTotalSemInvestimentoPrevistoMes() {
        return totalSemInvestimentoPrevistoMes;
    }

    public void setTotalSemInvestimentoPrevistoMes(Double totalSemInvestimentoPrevistoMes) {
        this.totalSemInvestimentoPrevistoMes = totalSemInvestimentoPrevistoMes;
    }

    public Double getTotalSemInvestimentoRealizadoMes() {
        return totalSemInvestimentoRealizadoMes;
    }

    public void setTotalSemInvestimentoRealizadoMes(Double totalSemInvestimentoRealizadoMes) {
        this.totalSemInvestimentoRealizadoMes = totalSemInvestimentoRealizadoMes;
    }

    public Double getTotalComInvestimentoPrevistoMes() {
        return totalComInvestimentoPrevistoMes;
    }

    public void setTotalComInvestimentoPrevistoMes(Double totalComInvestimentoPrevistoMes) {
        this.totalComInvestimentoPrevistoMes = totalComInvestimentoPrevistoMes;
    }

    public Double getTotalComInvestimentoRealizadoMes() {
        return totalComInvestimentoRealizadoMes;
    }

    public void setTotalComInvestimentoRealizadoMes(Double totalComInvestimentoRealizadoMes) {
        this.totalComInvestimentoRealizadoMes = totalComInvestimentoRealizadoMes;
    }

    public String getTotalReceitasPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalReceitasPrevistoMes());
    }

    public String getTotalReceitasRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalReceitasRealizadoes());
    }

    public String getTotalDespesasPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalDespesasPrevistoMes());
    }

    public String getTotalDespesasRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalDespesasRealizadoMes());
    }

    public String getTotalInvestimentosPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalInvestimentosPrevistoMes());
    }

    public String getTotalInvestimentosRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalInvestimentosRealizadoMes());
    }

    public String getTotalInvestimentosDespesasPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalInvestimentosDespesasPrevistoMes());
    }

    public String getTotalInvestimentosDespesasRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalInvestimentosDespesasRealizadoMes());
    }

    public String getTotalSemInvestimentosPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalSemInvestimentoPrevistoMes());
    }

    public String getTotalSemInvestimentosRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalSemInvestimentoRealizadoMes());
    }

    public String getTotalComInvestimentosPrevistoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalComInvestimentoPrevistoMes());
    }

    public String getTotalComInvestimentosRealizadoMesString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalComInvestimentoRealizadoMes());
    }

}

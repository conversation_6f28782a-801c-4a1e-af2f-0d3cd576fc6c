/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ColaboradorVO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.enumerador.TipoBVEnum;

/**
 * <AUTHOR>
 */
public class IndiceConversaoVendaVO extends SuperVO {

    private List<ColaboradorVO> listaColaboradoVOs;
    private Integer qtdMatriculaMes;
    private Integer qtdRematriculaMes;
    private Integer qtdQuestionarioMes;
    private Integer qtdMatriculaDia;
    private Integer qtdRematriculaDia;
    private Integer qtdQuestionarioDia;
    private Double totalICV;
    private Integer qtdBVSessaoMes;
    private Integer qtdBVSessaoDia;
    private Integer qtdBVSessaoPrimeiraCompraMes;
    private Integer qtdBVSessaoPrimeiraCompraDia;
    private Integer qtdBVSessaoRetornoCompraMes;
    private Integer qtdBVSessaoRetornoCompraDia;
    private Double totalICVS;
    private Map<Integer,Integer> mapaQuestionarios;
    private Map<Integer,Integer> mapaQuestionariosDia;
    private Map<Integer,Integer> mapaGeralMatriculas;
    private Map<Integer,Integer> mapaGeralMatriculasDia;
    private Map<Integer,Integer> mapaGeralRematriculas;
    private Map<Integer,Integer> mapaGeralRematriculasDia;
    private Map<Integer,Integer> mapaMatriculas;
    private Map<Integer,Integer> mapaMatriculasDia;
    private Map<Integer,Integer> mapaRematriculas;
    private Map<Integer,Integer> mapaRematriculasDia;

    public IndiceConversaoVendaVO() {
        listaColaboradoVOs = new ArrayList<ColaboradorVO>();
        limparCampos();
    }

    public void limparCampos() {
        setQtdMatriculaDia(0);
        setQtdMatriculaMes(0);
        setQtdQuestionarioDia(0);
        setQtdQuestionarioMes(0);
        setQtdRematriculaDia(0);
        setQtdRematriculaMes(0);
        setTotalICV(0.0);
        setQtdBVSessaoDia(0);
        setQtdBVSessaoMes(0);
        setQtdBVSessaoPrimeiraCompraDia(0);
        setQtdBVSessaoPrimeiraCompraMes(0);
        setQtdBVSessaoRetornoCompraDia(0);
        setQtdBVSessaoRetornoCompraMes(0);
        setTotalICVS(0.0);
        mapaQuestionarios = new HashMap<Integer, Integer>();
        mapaQuestionariosDia = new HashMap<Integer, Integer>(); 
        mapaMatriculas = new HashMap<Integer, Integer>(); 
        mapaMatriculasDia = new HashMap<Integer, Integer>(); 
        mapaRematriculas = new HashMap<Integer, Integer>(); 
        mapaRematriculasDia = new HashMap<Integer, Integer>(); 
    }

    public List<ColaboradorVO> getListaColaboradoVOs() {
        return listaColaboradoVOs;
    }

    public void setListaColaboradoVOs(List<ColaboradorVO> listaColaboradoVOs) {
        this.listaColaboradoVOs = listaColaboradoVOs;
    }

    public Integer getQtdMatriculaDia() {
        return qtdMatriculaDia;
    }

    public void setQtdMatriculaDia(Integer qtdMatriculaDia) {
        this.qtdMatriculaDia = qtdMatriculaDia;
    }

    public Integer getQtdMatriculaMes() {
        return qtdMatriculaMes;
    }

    public void setQtdMatriculaMes(Integer qtdMatriculaMes) {
        this.qtdMatriculaMes = qtdMatriculaMes;
    }

    public Integer getQtdQuestionarioDia() {
        return qtdQuestionarioDia;
    }

    public void setQtdQuestionarioDia(Integer qtdQuestionarioDia) {
        this.qtdQuestionarioDia = qtdQuestionarioDia;
    }

    public Integer getQtdQuestionarioMes() {
        return qtdQuestionarioMes;
    }

    public void setQtdQuestionarioMes(Integer qtdQuestionarioMes) {
        this.qtdQuestionarioMes = qtdQuestionarioMes;
    }

    public Integer getQtdRematriculaDia() {
        return qtdRematriculaDia;
    }

    public void setQtdRematriculaDia(Integer qtdRematriculaDia) {
        this.qtdRematriculaDia = qtdRematriculaDia;
    }

    public Integer getQtdRematriculaMes() {
        return qtdRematriculaMes;
    }

    public void setQtdRematriculaMes(Integer qtdRematriculaMes) {
        this.qtdRematriculaMes = qtdRematriculaMes;
    }

    public Double getTotalICV() {
        return totalICV;
    }

    public void setTotalICV(Double totalICV) {
        this.totalICV = totalICV;
    }

    public Double getTotalICVS() {
        return totalICVS;
    }

    public void setTotalICVS(Double totalICVS) {
        this.totalICVS = totalICVS;
    }

    public Integer getQtdBVSessaoMes() {
        return qtdBVSessaoMes;
    }

    public void setQtdBVSessaoMes(Integer qtdBVSessaoMes) {
        this.qtdBVSessaoMes = qtdBVSessaoMes;
    }

    public Integer getQtdBVSessaoDia() {
        return qtdBVSessaoDia;
    }

    public void setQtdBVSessaoDia(Integer qtdBVSessaoDia) {
        this.qtdBVSessaoDia = qtdBVSessaoDia;
    }

    public Integer getQtdBVSessaoPrimeiraCompraMes() {
        return qtdBVSessaoPrimeiraCompraMes;
    }

    public void setQtdBVSessaoPrimeiraCompraMes(Integer qtdBVSessaoPrimeiraCompraMes) {
        this.qtdBVSessaoPrimeiraCompraMes = qtdBVSessaoPrimeiraCompraMes;
    }

    public Integer getQtdBVSessaoPrimeiraCompraDia() {
        return qtdBVSessaoPrimeiraCompraDia;
    }

    public void setQtdBVSessaoPrimeiraCompraDia(Integer qtdBVSessaoPrimeiraCompraDia) {
        this.qtdBVSessaoPrimeiraCompraDia = qtdBVSessaoPrimeiraCompraDia;
    }

    public Integer getQtdBVSessaoRetornoCompraMes() {
        return qtdBVSessaoRetornoCompraMes;
    }

    public void setQtdBVSessaoRetornoCompraMes(Integer qtdBVSessaoRetornoCompraMes) {
        this.qtdBVSessaoRetornoCompraMes = qtdBVSessaoRetornoCompraMes;
    }

    public Integer getQtdBVSessaoRetornoCompraDia() {
        return qtdBVSessaoRetornoCompraDia;
    }

    public void setQtdBVSessaoRetornoCompraDia(Integer qtdBVSessaoRetornoCompraDia) {
        this.qtdBVSessaoRetornoCompraDia = qtdBVSessaoRetornoCompraDia;
    }

    public Map<Integer, Integer> getMapaQuestionarios() {
        if (mapaQuestionarios == null) {
            mapaQuestionarios = new HashMap<Integer, Integer>();
        }
        return mapaQuestionarios;
    }

    public void setMapaQuestionarios(Map<Integer, Integer> mapaQuestionarios) {
        this.mapaQuestionarios = mapaQuestionarios;
    }

    public Map<Integer, Integer> getMapaQuestionariosDia() {
        return mapaQuestionariosDia;
    }

    public void setMapaQuestionariosDia(Map<Integer, Integer> mapaQuestionariosDia) {
        this.mapaQuestionariosDia = mapaQuestionariosDia;
    }

    public Map<Integer, Integer> getMapaMatriculas() {
        return mapaMatriculas;
    }

    public void setMapaMatriculas(Map<Integer, Integer> mapaMatriculas) {
        this.mapaMatriculas = mapaMatriculas;
    }

    public Map<Integer, Integer> getMapaMatriculasDia() {
        return mapaMatriculasDia;
    }

    public void setMapaMatriculasDia(Map<Integer, Integer> mapaMatriculasDia) {
        this.mapaMatriculasDia = mapaMatriculasDia;
    }

    public Map<Integer, Integer> getMapaRematriculas() {
        return mapaRematriculas;
    }

    public void setMapaRematriculas(Map<Integer, Integer> mapaRematriculas) {
        this.mapaRematriculas = mapaRematriculas;
    }

    public Map<Integer, Integer> getMapaRematriculasDia() {
        return mapaRematriculasDia;
    }

    public void setMapaRematriculasDia(Map<Integer, Integer> mapaRematriculasDia) {
        this.mapaRematriculasDia = mapaRematriculasDia;
    }
    
    public void processarIndicadoresMapasMes(){
        mapaMatriculas = new HashMap<Integer, Integer>(); 
        mapaRematriculas = new HashMap<Integer, Integer>(); 
        for (Map.Entry<Integer,Integer> questionario: getMapaQuestionarios().entrySet()) {
            if(questionario.getValue().equals(TipoBVEnum.MA.getCodigo()) || questionario.getValue().equals(TipoBVEnum.RT.getCodigo())){
                 if(mapaGeralMatriculas.containsKey(questionario.getKey())){
                    mapaMatriculas.put(questionario.getKey(), mapaGeralMatriculas.get(questionario.getKey()));
                    continue;
                 }
                 if(mapaGeralRematriculas.containsKey(questionario.getKey())){
                    mapaRematriculas.put(questionario.getKey(), mapaGeralRematriculas.get(questionario.getKey()));
                    continue;
                 }
            } else {
                if(mapaGeralRematriculas.containsKey(questionario.getKey())){
                    mapaRematriculas.put(questionario.getKey(), mapaGeralRematriculas.get(questionario.getKey()));
                    continue;
                }
                if(mapaGeralMatriculas.containsKey(questionario.getKey())){
                    mapaMatriculas.put(questionario.getKey(), mapaGeralMatriculas.get(questionario.getKey()));
                    continue;
                }
            }
        }
        setQtdMatriculaMes(mapaMatriculas.size());
        setQtdQuestionarioMes(mapaQuestionarios.size());
        setQtdRematriculaMes(mapaRematriculas.size());
        processarIndicadoresMapasDia();
    }
    
    public void processarIndicadoresMapasDia(){
        mapaMatriculasDia = new HashMap<Integer, Integer>(); 
        mapaRematriculasDia = new HashMap<Integer, Integer>(); 
        for (Map.Entry<Integer,Integer> matricula: getMapaGeralMatriculasDia().entrySet()) {
            if(mapaMatriculas.containsValue(matricula.getValue())){
                mapaMatriculasDia.put(matricula.getKey(), matricula.getValue());
                continue;
             }
        }
        for (Map.Entry<Integer,Integer> rematricula: getMapaGeralRematriculasDia().entrySet()) {
            if(mapaRematriculas.containsValue(rematricula.getValue())){
                mapaRematriculasDia.put(rematricula.getKey(), rematricula.getValue());
                continue;
             }
        }
        setQtdMatriculaDia(mapaMatriculasDia.size());
        setQtdQuestionarioDia(mapaQuestionariosDia.size());
        setQtdRematriculaDia(mapaRematriculasDia.size());
    }

    public Map<Integer, Integer> getMapaGeralMatriculas() {
        return mapaGeralMatriculas;
    }

    public void setMapaGeralMatriculas(Map<Integer, Integer> mapaGeralMatriculas) {
        this.mapaGeralMatriculas = mapaGeralMatriculas;
    }

    public Map<Integer, Integer> getMapaGeralMatriculasDia() {
        if (mapaGeralMatriculasDia == null) {
            mapaGeralMatriculasDia = new HashMap<Integer, Integer>();
        }
        return mapaGeralMatriculasDia;
    }

    public void setMapaGeralMatriculasDia(Map<Integer, Integer> mapaGeralMatriculasDia) {
        this.mapaGeralMatriculasDia = mapaGeralMatriculasDia;
    }

    public Map<Integer, Integer> getMapaGeralRematriculas() {
        return mapaGeralRematriculas;
    }

    public void setMapaGeralRematriculas(Map<Integer, Integer> mapaGeralRematriculas) {
        this.mapaGeralRematriculas = mapaGeralRematriculas;
    }

    public Map<Integer, Integer> getMapaGeralRematriculasDia() {
        if (mapaGeralRematriculasDia == null) {
            mapaGeralRematriculasDia = new HashMap<Integer, Integer>();
        }
        return mapaGeralRematriculasDia;
    }

    public void setMapaGeralRematriculasDia(Map<Integer, Integer> mapaGeralRematriculasDia) {
        this.mapaGeralRematriculasDia = mapaGeralRematriculasDia;
    }
    
    
}

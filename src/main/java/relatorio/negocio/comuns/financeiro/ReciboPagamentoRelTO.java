package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ReciboPagamentoRelTO extends SuperTO {

    protected ReciboPagamentoVO reciboPagamentoVO = new ReciboPagamentoVO();
    protected List listaMovPagamentoVOs;
    protected List<MovProdutoVO> listaMovProdutoVOs;
    protected Boolean mostrarNumeroContrato;
    protected String nomeOperador;
    protected String matricula;
    protected String numeroContrato;
    protected String consultorResponsavel;
    private ContratoVO contratoVO = new ContratoVO();
    private List<MovParcelaVO> listaMovParcelaVOs;
    private List listaConvenioDescontoConfiguracaoVOs;
    private boolean devolucao = false;
    private String descricaoDevolucao;
    private Integer contrato;
    private Double valorDevolvido;
    private Double valorParcelasAberto = 0.0;
    private Boolean centralEventos = Boolean.FALSE;
    private MovProdutoVO movProduto = new MovProdutoVO();
    private String modalidades = "";
    private ConvenioDescontoConfiguracaoVO descConfiguracaoVO = new ConvenioDescontoConfiguracaoVO();
    private ConvenioDescontoVO convenioDescontoVO = new ConvenioDescontoVO();
    private List<DescontoReciboRelTO> descontosRecibo = new ArrayList<DescontoReciboRelTO>();
    private Integer empresa;

    private Integer sequencial = 3;


    private String mensagemValoresRodape;

    public ReciboPagamentoRelTO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setReciboPagamentoVO(new ReciboPagamentoVO());
        setListaMovPagamentoVOs(new ArrayList());
        setListaMovProdutoVOs(new ArrayList<MovProdutoVO>());
        setListaMovParcelaVOs(new ArrayList<MovParcelaVO>());
        setListaConvenioDescontoConfiguracaoVOs(new ArrayList());
        setMostrarNumeroContrato(false);
        setNomeOperador("");
        setConsultorResponsavel("");
        setMatricula("");
        setNumeroContrato("");
    }

    public List getListaMovPagamentoVOs() {
        return listaMovPagamentoVOs;
    }

    public void setListaMovPagamentoVOs(List listaMovPagamentoVOs) {
        this.listaMovPagamentoVOs = listaMovPagamentoVOs;
    }

    public List<MovProdutoVO> getListaMovProdutoVOs() {
        return listaMovProdutoVOs;
    }

    public void setListaMovProdutoVOs(List<MovProdutoVO> listaMovProdutoVOs) {
        this.listaMovProdutoVOs = listaMovProdutoVOs;
    }

    public ReciboPagamentoVO getReciboPagamentoVO() {
        return reciboPagamentoVO;
    }

    public void setReciboPagamentoVO(ReciboPagamentoVO reciboPagamentoVO) {
        this.reciboPagamentoVO = reciboPagamentoVO;
    }

    public Boolean getMostrarNumeroContrato() {
        return mostrarNumeroContrato;
    }

    public void setMostrarNumeroContrato(Boolean mostrarNumeroContrato) {
        this.mostrarNumeroContrato = mostrarNumeroContrato;
    }

    public String getNomeOperador() {
        return nomeOperador;
    }

    public void setNomeOperador(String nomeOperador) {
        this.nomeOperador = nomeOperador;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public JRDataSource getListaMovProduto() {
        return new JRBeanCollectionDataSource(getListaMovProdutoVOs());

    }

    public JRDataSource getListaConvenioDescontoConfiguracao() {
        return new JRBeanCollectionDataSource(getListaConvenioDescontoConfiguracaoVOs());
    }

    public JRDataSource getListaMovPagamento() {
        return new JRBeanCollectionDataSource(getListaMovPagamentoVOs());
    }

    public JRDataSource getListaMovParcela() {
        return new JRBeanCollectionDataSource(getListaMovParcelaVOs());
    }

    public List<MovParcelaVO> getListaMovParcelaVOs() {
        return listaMovParcelaVOs;
    }

    public void setListaMovParcelaVOs(List<MovParcelaVO> listaMovParcelaVOs) {
        this.listaMovParcelaVOs = listaMovParcelaVOs;
    }

    public String getConsultorResponsavel() {
        return consultorResponsavel;
    }

    public void setConsultorResponsavel(String consultorResponsavel) {
        this.consultorResponsavel = consultorResponsavel;
    }

    public boolean getDevolucao() {
        return devolucao;
    }

    public void setDevolucao(boolean devolucao) {
        this.devolucao = devolucao;
    }

    public String getDescricaoDevolucao() {
        return descricaoDevolucao;
    }

    public void setDescricaoDevolucao(String descricaoDevolucao) {
        this.descricaoDevolucao = descricaoDevolucao;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Double getValorDevolvido() {
        if (valorDevolvido == null) {
            valorDevolvido = 0.0;
        }
        return valorDevolvido;
    }

    public void setValorDevolvido(Double valorDevolvido) {
        this.valorDevolvido = valorDevolvido;
    }

    public String getValorDevolvidoMonetario() {
        return Formatador.formatarValorMonetario(valorDevolvido);
    }

    public Boolean getCentralEventos() {
        return centralEventos;
    }

    public void setCentralEventos(Boolean centralEventos) {
        this.centralEventos = centralEventos;
    }

    public Double getValorParcelasAberto() {
        return valorParcelasAberto;
    }

    public void setValorParcelasAberto(Double valorParcelasAberto) {
        this.valorParcelasAberto = valorParcelasAberto;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public MovProdutoVO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }

    public String getModalidades() {
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public ConvenioDescontoConfiguracaoVO getDescConfiguracaoVO() {
        return descConfiguracaoVO;
    }

    public void setDescConfiguracaoVO(ConvenioDescontoConfiguracaoVO descConfiguracaoVO) {
        this.descConfiguracaoVO = descConfiguracaoVO;
    }

    public ConvenioDescontoVO getConvenioDescontoVO() {
        return convenioDescontoVO;
    }

    public void setConvenioDescontoVO(ConvenioDescontoVO convenioDescontoVO) {
        this.convenioDescontoVO = convenioDescontoVO;
    }

    public List getListaConvenioDescontoConfiguracaoVOs() {
        return listaConvenioDescontoConfiguracaoVOs;
    }

    public void setListaConvenioDescontoConfiguracaoVOs(List listaConvenioDescontoConfiguracaoVOs) {
        this.listaConvenioDescontoConfiguracaoVOs = listaConvenioDescontoConfiguracaoVOs;
    }

    public List<DescontoReciboRelTO> getDescontosRecibo() {
        return descontosRecibo;
    }

    public void setDescontosRecibo(List<DescontoReciboRelTO> descontosRecibo) {
        this.descontosRecibo = descontosRecibo;
    }

    public JRDataSource getListaDescontosRecibo() {
        return new JRBeanCollectionDataSource(getDescontosRecibo());
    }

    public boolean getApresentarDescontos() {
        return !getDescontosRecibo().isEmpty();
    }

    public Integer getSequencial() {
        return sequencial;
    }

    public void setSequencial(Integer sequencial) {
        this.sequencial = sequencial;
    }

    public String getMensagemValoresRodape() {
        if (mensagemValoresRodape == null){
            mensagemValoresRodape = "";
        }
        return mensagemValoresRodape;
    }

    public void setMensagemValoresRodape(String mensagemValoresRodape) {
        this.mensagemValoresRodape = mensagemValoresRodape;
    }


    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }
}

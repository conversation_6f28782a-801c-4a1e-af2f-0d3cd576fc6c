/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
public class CompetenciaSinteticoProdutoMesVO extends SuperVO {

    protected List<CompetenciaSinteticoResumoPessoaVO> listaResumoPessoa;
    protected Integer qtd;
    protected Double valor;

    public CompetenciaSinteticoProdutoMesVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setValor(0.0);
        setQtd(0);
        setListaResumoPessoa(new ArrayList<CompetenciaSinteticoResumoPessoaVO>());
    }

    public List<CompetenciaSinteticoResumoPessoaVO> getListaResumoPessoa() {
        return listaResumoPessoa;
    }

    public void setListaResumoPessoa(List<CompetenciaSinteticoResumoPessoaVO> listaResumoPessoa) {
        this.listaResumoPessoa = listaResumoPessoa;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

}
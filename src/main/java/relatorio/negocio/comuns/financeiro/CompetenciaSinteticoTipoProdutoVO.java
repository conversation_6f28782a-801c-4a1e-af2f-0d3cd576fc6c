/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;

/**
 * <AUTHOR>
 */
public class CompetenciaSinteticoTipoProdutoVO extends SuperVO {

    protected List<CompetenciaSinteticoMesVO> listaMes;
    protected List<CompetenciaSinteticoProdutoVO> listaProduto;
    protected String tipoProduto;
    protected Boolean apresentarResultado;

    public CompetenciaSinteticoTipoProdutoVO() {
        super();
        inicializarDados();
    }

    public void adicionarProdutoMes(ProdutoVO produtoVO, Integer mes) {
        CompetenciaSinteticoProdutoMesVO produtoMesVO = new CompetenciaSinteticoProdutoMesVO();

        for (CompetenciaSinteticoProdutoVO produto : listaProduto) {
            if (produto.getCodigo().equals(produtoVO.getCodigo())) {
//                produtoMesVO.setp
            }
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        listaMes = new ArrayList<CompetenciaSinteticoMesVO>();
        listaProduto = new ArrayList<CompetenciaSinteticoProdutoVO>();
        tipoProduto = "";
    }

    public List<CompetenciaSinteticoMesVO> getListaMes() {
        return listaMes;
    }

    public void setListaMes(List<CompetenciaSinteticoMesVO> listaMes) {
        this.listaMes = listaMes;
    }

    public List<CompetenciaSinteticoProdutoVO> getListaProduto() {
        return listaProduto;
    }

    public void setListaProduto(List<CompetenciaSinteticoProdutoVO> listaProduto) {
        this.listaProduto = listaProduto;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Boolean getApresentarResultado() {
        return apresentarResultado;
    }

    public void setApresentarResultado(Boolean apresentarResultado) {
        this.apresentarResultado = apresentarResultado;
    }
}

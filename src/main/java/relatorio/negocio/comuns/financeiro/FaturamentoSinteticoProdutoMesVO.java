/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class FaturamentoSinteticoProdutoMesVO extends SuperVO {

    protected List<FaturamentoSinteticoResumoPessoaVO> listaResumoPessoa;
    protected Integer qtd;
    protected Double valor;
    private Map<String,FaturamentoSinteticoResumoPessoaVO> mapaResumoPessoa = new HashMap<String, FaturamentoSinteticoResumoPessoaVO>();

    public FaturamentoSinteticoProdutoMesVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setValor(0.0);
        setQtd(0);
        setListaResumoPessoa(new ArrayList<FaturamentoSinteticoResumoPessoaVO>());
    }

    public List<FaturamentoSinteticoResumoPessoaVO> getListaResumoPessoa() {
        return listaResumoPessoa;
    }

    public void setListaResumoPessoa(List<FaturamentoSinteticoResumoPessoaVO> listaResumoPessoa) {
        this.listaResumoPessoa = listaResumoPessoa;
    }

    public Integer getQtd() {
        return qtd;
    }

    public void setQtd(Integer qtd) {
        this.qtd = qtd;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Map<String, FaturamentoSinteticoResumoPessoaVO> getMapaResumoPessoa() {
        return mapaResumoPessoa;
    }

    public void setMapaResumoPessoa(Map<String, FaturamentoSinteticoResumoPessoaVO> mapaResumoPessoa) {
        this.mapaResumoPessoa = mapaResumoPessoa;
    }
    
    

}
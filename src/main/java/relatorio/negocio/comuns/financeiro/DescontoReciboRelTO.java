package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 13/11/13
 * Time: 15:59
 * To change this template use File | Settings | File Templates.
 */
public class DescontoReciboRelTO extends SuperTO {

    private Double valorDesconto = 0.0;
    private Double porcentagemDesconto = 0.0;
    private String descricaoDesconto = "";
    private Integer codigoContrato = 0;

    public Double getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(Double valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public Double getPorcentagemDesconto() {
        return porcentagemDesconto;
    }

    public void setPorcentagemDesconto(Double porcentagemDesconto) {
        this.porcentagemDesconto = porcentagemDesconto;
    }

    public String getDescricaoDesconto() {
        return descricaoDesconto;
    }

    public void setDescricaoDesconto(String descricaoDesconto) {
        this.descricaoDesconto = descricaoDesconto;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }
}

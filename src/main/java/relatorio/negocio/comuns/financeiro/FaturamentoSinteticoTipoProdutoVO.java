/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;
import relatorio.controle.arquitetura.SuperControleRelatorio;

/**
 * <AUTHOR>
 */
public class FaturamentoSinteticoTipoProdutoVO extends SuperControleRelatorio {

    //protected List<FaturamentoSinteticoMesVO> listaMes;
    protected List<FaturamentoSinteticoProdutoVO> listaProduto;
    protected String tipoProduto;
    protected Boolean apresentarResultado;

    public FaturamentoSinteticoTipoProdutoVO() {
        super();
        inicializarDados();
    }

    public void adicionarProdutoMes(ProdutoVO produtoVO, Integer mes) {
        FaturamentoSinteticoProdutoMesVO produtoMesVO = new FaturamentoSinteticoProdutoMesVO();

        for (FaturamentoSinteticoProdutoVO produto : listaProduto) {
            if (produto.getCodigo().equals(produtoVO.getCodigo())) {
//                produtoMesVO.setp
            }
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        // listaMes = new ArrayList<FaturamentoSinteticoMesVO>();
        listaProduto = new ArrayList<FaturamentoSinteticoProdutoVO>();
        tipoProduto = "";
        setApresentarResultado(false);
    }

    public List<FaturamentoSinteticoProdutoVO> getListaProduto() {
        return listaProduto;
    }

    public List<FaturamentoSinteticoProdutoVO> getListaProdutoSemZero() {
        if (getMostrarValoresZeradosRel()) {
            return getListaProduto();
        }

        List<FaturamentoSinteticoProdutoVO> novaLista = new ArrayList<>();
        for(FaturamentoSinteticoProdutoVO p : listaProduto){
            boolean todosZero = true;
            for(FaturamentoSinteticoProdutoMesVO pp : p.listaProdutoXMes){
                todosZero = true;
                if(!(pp.valor==0)){
                    todosZero = false;
                    break;
                }
            }
            if(!todosZero) {
                novaLista.add(p);
            }
        }
        return novaLista;
    }

    public boolean getMostrarValoresZeradosRel() {
        try {
            return getEmpresaLogado().isMostrarValoresZeradosRel();
        }catch (Exception e){
            return true;
        }
    }

    public void setListaProduto(List<FaturamentoSinteticoProdutoVO> listaProduto) {
        this.listaProduto = listaProduto;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Boolean getApresentarResultado() {
        return apresentarResultado;
    }

    public void setApresentarResultado(Boolean apresentarResultado) {
        this.apresentarResultado = apresentarResultado;
    }
}
/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.AulaAvulsaDiariaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class FaturamentoSinteticoResumoPessoaVO extends SuperVO {

    protected ClienteVO cliente;
    protected PessoaVO pessoa;
    protected ContratoVO contrato;
    protected Double valor;
    private String formaPagApresentar;
    private ProdutoVO produtoVO;
    private String produtoQuitado = "";
    private int quantidade;
    private String descricaoProduto="";
    private String nomeModalidadeDiaria;
    private String nomeResponsavelLancamento="";
    private String nomeResponsavelRecebimento="";
    public String getFormaPagApresentar() {
        return formaPagApresentar;
    }

    public void setFormaPagApresentar(String formaPagApresentar) {
        this.formaPagApresentar = formaPagApresentar;
    }

    protected Date dataLancamentoMovProduto;
    private ColaboradorVO colaboradorVO;
    private String consumidor;
    private String nomeEmpresa;

    public FaturamentoSinteticoResumoPessoaVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {
        setCliente(new ClienteVO());
        setContrato(new ContratoVO());
        setValor(0.0);
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public ContratoVO getContrato() {
        return contrato;
    }
    public void setNomeModalidadeDiaria(String nomeModalidade){
        nomeModalidadeDiaria = nomeModalidade;
    }
    public String getNumeroModalidades() throws Exception{
        if(produtoVO.getTipoProduto().equals("DI")){
            return nomeModalidadeDiaria;
        }else{
            return getContrato().getNumeroContratoModalidade();
        }

    }
    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValor());
    }

    public String getDataLancamentoMovProduto_Apresentar() {
        return Uteis.getData(dataLancamentoMovProduto);
    }

    public Date getDataLancamentoMovProduto() {
        return dataLancamentoMovProduto;
    }

    public void setDataLancamentoMovProduto(Date dataLancamentoMovProduto) {
        this.dataLancamentoMovProduto = dataLancamentoMovProduto;
    }
    public String getNomeProdutoApresentar(){
        return produtoVO.getDescricao();
    }
    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public String getMatricula_Apresentar() {
        return getCliente().getMatricula();
    }

    public String getNome_Apresentar() {
        try {
            return (getCliente() == null
                    || getCliente().getCodigo() == null
                    || getCliente().getCodigo() == 0) ? getColaboradorVO().getPessoa().getNome() : getCliente().getPessoa().getNome();
        } catch (Exception e) {
            return "";
        }
        
    }

    public int getContrato_Apresentar() {
        return getContrato().getCodigo();
    }
    public Boolean getProdutoDiaria(){
        return getProdutoVO().getTipoProduto().equals("DI");
    }
    public String getDataInicio_Apresentar() {
        return getContrato().getVigenciaDe_Apresentar();
    }

    public String getDataAte_Apresentar() {
        return getContrato().getVigenciaAteAjustada_Apresentar();
    }

    public Integer getDuracao_Apresentar() {
        return getContrato().getContratoDuracao().getNumeroMeses();
    }

    public String getModalidade_Apresentar() throws Exception{
        if(getProdutoDiaria())
        return getNumeroModalidades();
        else
        return getContrato().getNomeModalidadesConcatenado();
    }

    public String getPlano_Apresentar() {
        return getContrato().getPlano().getDescricao();
    }

    public String getDataCadastro_Apresentar() {
        return getCliente().getPessoa().getDataCadastro_Apresentar();
    }

    public String getSituacaoContrato_Apresentar() {
        return getContrato().getCodigo() > 0 ? getContrato().getSituacaoContrato_Apresentar() : "-";
    }

    public String getConsumidor() {
        return consumidor;
    }

    public void setConsumidor(String consumidor) {
        this.consumidor = consumidor;
    }

    public String getNomeProduto() {
        if(produtoVO.getDescricao().equals("PLANO") || (produtoVO.getDescricao().equals("")  || UteisValidacao.emptyString(produtoVO.getDescricao())))
            return "-";
        return produtoVO.getDescricao();
    }
    public Boolean getApresentarNomeProduto(){
        if(!UteisValidacao.emptyString(produtoVO.getDescricao())) {
            if (produtoVO.getDescricao().equals("PLANO")) {
                return false;
            }else{
                return true;
            }
        }else
            return false;

    }
    public void setNomeProduto(String nomeProduto){
        getProdutoVO().setDescricao(nomeProduto);
    }
    public ProdutoVO getProdutoVO() {
        if(produtoVO== null)
        produtoVO = new ProdutoVO();
        return produtoVO;
    }
    public void setProdutoVO(ProdutoVO produtoVO) {
        this.produtoVO = produtoVO;
    }

    public String getProdutoQuitado() {
        if(produtoVO.getDescricao().equals("") || !UteisValidacao.emptyString(produtoVO.getDescricao()))
            return "-";
        return produtoQuitado;
    }

    public void setProdutoQuitado(String produtoQuitado) {
        this.produtoQuitado = produtoQuitado;
    }
    public Boolean getApresentarColunaPago(){
        if(produtoQuitado.equals("") || produtoQuitado == null)
            return false;
        else
            return true;
    }
    public String getQuitado_Apresentar(){

        if(produtoQuitado.equals("EA"))
            return "Em Aberto";
        else  if(produtoQuitado.equals("PG"))
            return "Pago";
        else if(produtoQuitado.equals(""))
            return "-";
        else
            return "Cancelado";
    }

    public String getNomeResponsavelLancamento() {
        return nomeResponsavelLancamento;
    }

    public void setNomeResponsavelLancamento(String nomeResponsavelLancamento) {
        this.nomeResponsavelLancamento = nomeResponsavelLancamento;
    }

    public String getNomeResponsavelRecebimento() {
        return nomeResponsavelRecebimento;
    }

    public void setNomeResponsavelRecebimento(String nomeResponsavelRecebimento) {
        this.nomeResponsavelRecebimento = nomeResponsavelRecebimento;
    }

    public String getMatriculaApresentar(){
        return cliente.getMatricula();
    }
    public String getNomePessoaApresentar(){
        return cliente.getPessoa().getNome();
    }
    public String getDataCadastroApresentar(){
        return cliente.getPessoa().getDataCadastro_Apresentar();
    }
    public String getNumeroContratoApresentar(){
        return contrato.getCodigo().toString();
    }
    public String getVigenciaDeApresentar(){
        return contrato.getVigenciaDe_Apresentar();
    }
    public String getVigenciaAteApresentar(){
        return contrato.getVigenciaAteAjustada_Apresentar();
    }
    public String getDuracaoMesesApresentar(){
        return contrato.getContratoDuracao().getNumeroMeses().toString();
    }
    public String getPlanoApresentar(){
        return contrato.getPlano().getDescricao();
    }
    public String getSituacaoApresentar(){
        return contrato.getSituacao_Apresentar();
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public int getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(int quantidade) {
        this.quantidade = quantidade;
    }

    public String getNomeEmpresaPlanoOuVendaApresentar() {
        if(!UteisValidacao.emptyString(contrato.getPlano().getNomeEmpresaPlanoApresentar())) {
            return contrato.getPlano().getNomeEmpresaPlanoApresentar();
        } else {
            return nomeEmpresa;
        }
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            return "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }
}


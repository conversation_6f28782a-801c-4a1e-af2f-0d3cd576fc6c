/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.PeriodoMensal;

/**
 *
 * <AUTHOR>
 */
public class FaturamentoSinteticoProdutoVO extends SuperVO {

    protected ProdutoVO produto;
    protected String descricao;
    protected Integer duracao;   
    protected Integer plano;
    private String nomePlano;
    protected List<FaturamentoSinteticoProdutoMesVO> listaProdutoXMes;
    private Map<PeriodoMensal, FaturamentoSinteticoProdutoMesVO> mapaMeses = new HashMap<PeriodoMensal, FaturamentoSinteticoProdutoMesVO>();

    public Map<PeriodoMensal, FaturamentoSinteticoProdutoMesVO> getMapaMeses() {
        return mapaMeses;
    }

    public void setMapaMeses(Map<PeriodoMensal, FaturamentoSinteticoProdutoMesVO> mapaMeses) {
        this.mapaMeses = mapaMeses;
    }

    public FaturamentoSinteticoProdutoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setProduto(new ProdutoVO());
        setListaProdutoXMes(new ArrayList<>());
        setDescricao("");
        setDuracao(0);
        setPlano(0);
        setNomePlano("");
    }

    public List<FaturamentoSinteticoProdutoMesVO> getListaProdutoXMes() {
        return listaProdutoXMes;
    }

    public void setListaProdutoXMes(List<FaturamentoSinteticoProdutoMesVO> listaProdutoXMes) {
        this.listaProdutoXMes = listaProdutoXMes;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    /**
     * @return the nomePlano
     */
    public String getNomePlano() {
        return nomePlano;
    }

    /**
     * @param nomePlano the nomePlano to set
     */
    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }
   


}

package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.utilitarias.UteisValidacao;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by Rafael on 18/11/2016.
 */
public class GestaoFiltroICV implements Serializable {
   private Date inicio;
    private Date fim;
   private List<Integer> colaboradores ;
   private Integer empresa ;
   private boolean filtroMatriculas;
   private boolean filtroRematriculas;
   private boolean filtroRetornos;
   private boolean filtroEspontaneo;
   private boolean filtroAgendado;
   private java.util.List<TipoContratoEnum> tiposContrato;
   private List<TipoBVEnum> tiposBV;

    public GestaoFiltroICV(Date inicio, Date fim, List<Integer> colaboradores, Integer empresa, boolean filtroMatriculas, boolean filtroRematriculas, boolean filtroRetornos, boolean filtroEspontaneo, boolean filtroAgendado, List<TipoContratoEnum> tiposContrato, List<TipoBVEnum> tiposBV) {
        this.inicio = inicio;
        this.fim = fim;
        this.colaboradores = colaboradores;
        this.empresa = empresa;
        this.filtroMatriculas = filtroMatriculas;
        this.filtroRematriculas = filtroRematriculas;
        this.filtroRetornos = filtroRetornos;
        this.filtroEspontaneo = filtroEspontaneo;
        this.filtroAgendado = filtroAgendado;
        this.tiposContrato = tiposContrato;
        this.tiposBV = tiposBV;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public List<Integer> getColaboradores() {
        return colaboradores;
    }

    public void setColaboradores(List<Integer> colaboradores) {
        this.colaboradores = colaboradores;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public boolean isFiltroMatriculas() {
        return filtroMatriculas;
    }

    public void setFiltroMatriculas(boolean filtroMatriculas) {
        this.filtroMatriculas = filtroMatriculas;
    }

    public boolean isFiltroRematriculas() {
        return filtroRematriculas;
    }

    public void setFiltroRematriculas(boolean filtroRematriculas) {
        this.filtroRematriculas = filtroRematriculas;
    }

    public boolean isFiltroRetornos() {
        return filtroRetornos;
    }

    public void setFiltroRetornos(boolean filtroRetornos) {
        this.filtroRetornos = filtroRetornos;
    }

    public boolean isFiltroEspontaneo() {
        return filtroEspontaneo;
    }

    public void setFiltroEspontaneo(boolean filtroEspontaneo) {
        this.filtroEspontaneo = filtroEspontaneo;
    }

    public boolean isFiltroAgendado() {
        return filtroAgendado;
    }

    public void setFiltroAgendado(boolean filtroAgendado) {
        this.filtroAgendado = filtroAgendado;
    }

    public List<TipoContratoEnum> getTiposContrato() {
        return tiposContrato;
    }

    public void setTiposContrato(List<TipoContratoEnum> tiposContrato) {
        this.tiposContrato = tiposContrato;
    }

    public List<TipoBVEnum> getTiposBV() {
        return tiposBV;
    }

    public void setTiposBV(List<TipoBVEnum> tiposBV) {
        this.tiposBV = tiposBV;
    }
    public String getColaboradoresString(){
        String filtroColaborador = "";
        for(Integer codigo : colaboradores){
            if(UteisValidacao.emptyString(filtroColaborador)){
                filtroColaborador += " AND QuestionarioCliente.consultor in ("+codigo;
            } else {
                filtroColaborador += ","+ codigo;
            }
        }
        filtroColaborador += ")";
        return filtroColaborador;
    }
}

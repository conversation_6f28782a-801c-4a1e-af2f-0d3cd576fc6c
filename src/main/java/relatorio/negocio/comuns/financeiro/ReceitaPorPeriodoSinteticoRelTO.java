/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperTO;

public class ReceitaPorPeriodoSinteticoRelTO extends SuperTO {

    private String nomeOperador;
    private String matricula;
    private String dataIni;
    private String dataFim;
    private double valorTotalDinheiro;
    private double valorTotalChequeVista;
    private double valorTotalChequePrazo;
    private double valorTotalCartaoCredito;
    private double valorTotalCartaoDebito;
    private double valorTotalBoleto;
    private double valorTotalOutros;
    private double valorTotalDevolucao;
    private double valorTotalFinal;
    private double valorTotalEspecie;
    private double valorTotalTransferenciaBancaria;
    private double valorTotalPix;
    private int qtdeTotalDinheiro;
    private int qtdeTotalChequeVista;
    private int qtdeTotalChequePrazo;
    private int qtdeTotalCartaoCredito;
    private int qtdeTotalCartaoDebito;
    private int qtdeTotalBoleto;
    private int qtdeTotalOutros;
    private int qtdeTotalDevolucao;
    private int qtdeTotalFinal;
    private int qtdeTotalEspecie;
    private int qtdeTotalTransferenciaBancaria;
    private int qtdeTotalPix;
    private boolean visaoDinheiro;
    private boolean visaoChequeVista;
    private boolean visaoChequePrazo;
    private boolean visaoCartaoDebito;
    private boolean visaoCartaoCredito;
    private boolean visaoOutros;
    private boolean visaoDevolucao;
    private boolean visaoBoleto;
    private boolean visaoTransferenciaBancaria;
    private boolean visaoPix;

    public ReceitaPorPeriodoSinteticoRelTO() {

        setNomeOperador("");
        setMatricula("");
        setDataIni("");
        setDataFim("");

        setValorTotalDinheiro(0.0);
        setValorTotalChequeVista(0.0);
        setValorTotalChequePrazo(0.0);
        setValorTotalCartaoCredito(0.0);
        setValorTotalCartaoDebito(0.0);
        setValorTotalOutros(0.0);
        setValorTotalFinal(0.0);
        setValorTotalBoleto(0.0);
        setValorTotalDevolucao(0.0);
        setValorTotalEspecie(0.0);
        setValorTotalTransferenciaBancaria(0.0);
        setValorTotalPix(0.0);

        setQtdeTotalDinheiro(0);
        setQtdeTotalChequeVista(0);
        setQtdeTotalChequePrazo(0);
        setQtdeTotalCartaoCredito(0);
        setQtdeTotalCartaoDebito(0);
        setQtdeTotalOutros(0);
        setQtdeTotalFinal(0);
        setQtdeTotalBoleto(0);
        setQtdeTotalDevolucao(0);
        setQtdeTotalTransferenciaBancaria(0);
        setQtdeTotalPix(0);

        setVisaoCartaoCredito(false);
        setVisaoCartaoDebito(false);
        setVisaoChequeVista(false);
        setVisaoChequePrazo(false);
        setVisaoDinheiro(false);
        setVisaoOutros(false);
        setVisaoDevolucao(false);
        setVisaoBoleto(false);
        setVisaoTransferenciaBancaria(false);
        setVisaoPix(false);
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomeOperador() {
        return nomeOperador;
    }

    public void setNomeOperador(String nomeOperador) {
        this.nomeOperador = nomeOperador;
    }

    public int getQtdeTotalCartaoCredito() {
        return qtdeTotalCartaoCredito;
    }

    public void setQtdeTotalCartaoCredito(int qtdeTotalCartaoCredito) {
        this.qtdeTotalCartaoCredito = qtdeTotalCartaoCredito;
    }

    public int getQtdeTotalCartaoDebito() {
        return qtdeTotalCartaoDebito;
    }

    public void setQtdeTotalCartaoDebito(int qtdeTotalCartaoDebito) {
        this.qtdeTotalCartaoDebito = qtdeTotalCartaoDebito;
    }

    public int getQtdeTotalChequePrazo() {
        return qtdeTotalChequePrazo;
    }

    public void setQtdeTotalChequePrazo(int qtdeTotalChequePrazo) {
        this.qtdeTotalChequePrazo = qtdeTotalChequePrazo;
    }

    public int getQtdeTotalChequeVista() {
        return qtdeTotalChequeVista;
    }

    public void setQtdeTotalChequeVista(int qtdeTotalChequeVista) {
        this.qtdeTotalChequeVista = qtdeTotalChequeVista;
    }

    public int getQtdeTotalDinheiro() {
        return qtdeTotalDinheiro;
    }

    public void setQtdeTotalDinheiro(int qtdeTotalDinheiro) {
        this.qtdeTotalDinheiro = qtdeTotalDinheiro;
    }

    public int getQtdeTotalFinal() {
        return qtdeTotalFinal;
    }

    public void setQtdeTotalFinal(int qtdeTotalFinal) {
        this.qtdeTotalFinal = qtdeTotalFinal;
    }

    public int getQtdeTotalOutros() {
        return qtdeTotalOutros;
    }

    public void setQtdeTotalOutros(int qtdeTotalOutros) {
        this.qtdeTotalOutros = qtdeTotalOutros;
    }

    public double getValorTotalChequePrazo() {
        return valorTotalChequePrazo;
    }

    public void setValorTotalChequePrazo(double valorTotalChequePrazo) {
        this.valorTotalChequePrazo = valorTotalChequePrazo;
    }

    public double getValorTotalChequeVista() {
        return valorTotalChequeVista;
    }

    public void setValorTotalChequeVista(double valorTotalChequeVista) {
        this.valorTotalChequeVista = valorTotalChequeVista;
    }

    public double getValorTotalDinheiro() {
        return valorTotalDinheiro;
    }

    public void setValorTotalDinheiro(double valorTotalDinheiro) {
        this.valorTotalDinheiro = valorTotalDinheiro;
    }

    public double getValorTotalFinal() {
        return valorTotalFinal;
    }

    public void setValorTotalFinal(double valorTotalFinal) {
        this.valorTotalFinal = valorTotalFinal;
    }

    public double getValorTotalOutros() {
        return valorTotalOutros;
    }

    public void setValorTotalOutros(double valorTotalOutros) {
        this.valorTotalOutros = valorTotalOutros;
    }

    public double getValorTotalCartaoCredito() {
        return valorTotalCartaoCredito;
    }

    public void setValorTotalCartaoCredito(double valorTotalCartaoCredito) {
        this.valorTotalCartaoCredito = valorTotalCartaoCredito;
    }

    public double getValorTotalCartaoDebito() {
        return valorTotalCartaoDebito;
    }

    public void setValorTotalCartaoDebito(double valorTotalCartaoDebito) {
        this.valorTotalCartaoDebito = valorTotalCartaoDebito;
    }

    public String getDataFim() {
        return dataFim;
    }

    public void setDataFim(String dataFim) {
        this.dataFim = dataFim;
    }

    public String getDataIni() {
        return dataIni;
    }

    public void setDataIni(String dataIni) {
        this.dataIni = dataIni;
    }

    public boolean getVisaoCartaoCredito() {
        return visaoCartaoCredito;
    }

    public void setVisaoCartaoCredito(boolean visaoCartaoCredito) {
        this.visaoCartaoCredito = visaoCartaoCredito;
    }

    public boolean getVisaoCartaoDebito() {
        return visaoCartaoDebito;
    }

    public void setVisaoCartaoDebito(boolean visaoCartaoDebito) {
        this.visaoCartaoDebito = visaoCartaoDebito;
    }

    public boolean getVisaoDinheiro() {
        return visaoDinheiro;
    }

    public void setVisaoDinheiro(boolean visaoDinheiro) {
        this.visaoDinheiro = visaoDinheiro;
    }

    public boolean getVisaoOutros() {
        return visaoOutros;
    }

    public void setVisaoOutros(boolean visaoOutros) {
        this.visaoOutros = visaoOutros;
    }

    public boolean getVisaoChequePrazo() {
        return visaoChequePrazo;
    }

    public void setVisaoChequePrazo(boolean visaoChequePrazo) {
        this.visaoChequePrazo = visaoChequePrazo;
    }

    public boolean getVisaoChequeVista() {
        return visaoChequeVista;
    }

    public void setVisaoChequeVista(boolean visaoChequeVista) {
        this.visaoChequeVista = visaoChequeVista;
    }

    public void setValorTotalDevolucao(double valorTotalDevolucao) {
        this.valorTotalDevolucao = valorTotalDevolucao;
    }

    public double getValorTotalDevolucao() {
        return valorTotalDevolucao;
    }

    public void setQtdeTotalDevolucao(int qtdeTotalDevolucao) {
        this.qtdeTotalDevolucao = qtdeTotalDevolucao;
    }

    public int getQtdeTotalDevolucao() {
        return qtdeTotalDevolucao;
    }

    public void setVisaoDevolucao(boolean visaoDevolucao) {
        this.visaoDevolucao = visaoDevolucao;
    }

    public boolean getVisaoDevolucao() {
        return visaoDevolucao;
    }

    public boolean getVisaoBoleto() {
        return visaoBoleto;
    }

    public void setVisaoBoleto(boolean visaoBoleto) {
        this.visaoBoleto = visaoBoleto;
    }

    public double getValorTotalBoleto() {
        return valorTotalBoleto;
    }

    public void setValorTotalBoleto(double valorTotalBoleto) {
        this.valorTotalBoleto = valorTotalBoleto;
    }

    public int getQtdeTotalBoleto() {
        return qtdeTotalBoleto;
    }

    public void setQtdeTotalBoleto(int qtdeTotalBoleto) {
        this.qtdeTotalBoleto = qtdeTotalBoleto;
    }

    public double getValorTotalEspecie() {
        return valorTotalEspecie;
    }

    public void setValorTotalEspecie(double valorTotalEspecie) {
        this.valorTotalEspecie = valorTotalEspecie;
    }

    public int getQtdeTotalEspecie() {
        return qtdeTotalEspecie;
    }

    public void setQtdeTotalEspecie(int qtdeTotalEspecie) {
        this.qtdeTotalEspecie = qtdeTotalEspecie;
    }

    public double getValorTotalTransferenciaBancaria() {
        return valorTotalTransferenciaBancaria;
    }

    public void setValorTotalTransferenciaBancaria(double valorTotalTransferenciaBancaria) {
        this.valorTotalTransferenciaBancaria = valorTotalTransferenciaBancaria;
    }

    public int getQtdeTotalTransferenciaBancaria() {
        return qtdeTotalTransferenciaBancaria;
    }

    public void setQtdeTotalTransferenciaBancaria(int qtdeTotalTransferenciaBancaria) {
        this.qtdeTotalTransferenciaBancaria = qtdeTotalTransferenciaBancaria;
    }

    public boolean getVisaoTransferenciaBancaria() {
        return visaoTransferenciaBancaria;
    }

    public void setVisaoTransferenciaBancaria(boolean visaoTransferenciaBancaria) {
        this.visaoTransferenciaBancaria = visaoTransferenciaBancaria;
    }

    public double getValorTotalPix() {
        return valorTotalPix;
    }

    public void setValorTotalPix(double valorTotalPix) {
        this.valorTotalPix = valorTotalPix;
    }

    public int getQtdeTotalPix() {
        return qtdeTotalPix;
    }

    public void setQtdeTotalPix(int qtdeTotalPix) {
        this.qtdeTotalPix = qtdeTotalPix;
    }

    public boolean isVisaoPix() {
        return visaoPix;
    }

    public void setVisaoPix(boolean visaoPix) {
        this.visaoPix = visaoPix;
    }
}

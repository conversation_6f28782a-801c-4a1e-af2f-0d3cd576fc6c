/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.PeriodoMensal;

import java.util.Date;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * <AUTHOR>
 */
public class CompetenciaSinteticoResumoPessoaVO extends SuperVO {
    
    private String nomeModalidadeDiaria;
    protected ClienteVO cliente;
    protected ContratoVO contrato;
    protected Double valor;
    protected Date mesLancamentoMovProduto;
    protected ColaboradorVO colaboradorVO;

    public CompetenciaSinteticoResumoPessoaVO() {
        super();
        inicializarDados();
    }

    public String getMatricula_Apresentar() {
        return getCliente().getMatricula();
    }

    public String getNome_Apresentar() {
        return getCliente().getPessoa().getNome();
    }

    public String getPessoaCPF() {
        if (!UteisValidacao.emptyNumber(getColaboradorVO().getCodigo())) {
            return getColaboradorVO().getPessoa().getCfp();
        } else if (!UteisValidacao.emptyNumber(getCliente().getCodigo())) {
            return getCliente().getPessoa().getCfp();
        }
        return "";
    }

    public int getContrato_Apresentar() {
        return getContrato().getCodigo();
    }

    public String getDataInicio_Apresentar() {
        return getContrato().getVigenciaDe_Apresentar();
    }

    public String getDataAte_Apresentar() {
        return getContrato().getVigenciaAteAjustada_Apresentar();
    }

    public Integer getDuracao_Apresentar() {
        return getContrato().getContratoDuracao().getNumeroMeses();
    }

    public String getModalidade_Apresentar() {
        return getContrato().getNomeModalidadesConcatenado();
    }

    public String getPlano_Apresentar() {
        return getContrato().getPlano().getDescricao();
    }

    public void inicializarDados() {
        setValor(0.0);
        setCliente(new ClienteVO());
        setContrato(new ContratoVO());
        setColaboradorVO(new ColaboradorVO());
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ContratoVO getContrato() {
        return contrato;
    }

    public void setContrato(ContratoVO contrato) {
        this.contrato = contrato;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getMesLancamentoMovProduto_Apresentar() {
        PeriodoMensal periodo = new PeriodoMensal(mesLancamentoMovProduto, mesLancamentoMovProduto);
        return periodo.getMesAnoApresentar();
    }

    public Date getMesLancamentoMovProduto() {
        return mesLancamentoMovProduto;
    }

    public void setMesLancamentoMovProduto(Date mesLancamentoMovProduto) {
        this.mesLancamentoMovProduto = mesLancamentoMovProduto;
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }
    public void setNomeModalidadeDiaria(String nomeModalidade){
        nomeModalidadeDiaria = nomeModalidade;
    }
    public String getNumeroModalidades() throws Exception{
        if(UteisValidacao.emptyNumber(getContrato().getCodigo())){
            return nomeModalidadeDiaria;
        }else{
            return getContrato().getNumeroContratoModalidade();
        }

    }
    public boolean getApresentarNumeroModalidades(){
     if(UteisValidacao.emptyString(nomeModalidadeDiaria)){
         return true;
     }
     return false;
    }
}

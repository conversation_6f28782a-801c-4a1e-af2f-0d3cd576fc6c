package relatorio.negocio.comuns.contrato;

import java.util.Date;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;
import negocio.comuns.basico.ClienteVO;

/**
 * Reponsável por manter os dados da entidade MatriculaAlunoHorarioTurma. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 */
public class ItemMatriAlunoHorarioTurmaVO extends SuperVO {

    private Integer codigo;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Contrato </code>.*/
    private ContratoVO contrato;
    /** Atributo responsável por manter o objeto relacionado da classe <code>Pessoa </code>.*/
    private PessoaVO pessoa;
    private ClienteVO cliente;
    private Boolean listaChamada1;
    private Boolean listaChamada2;
    private Boolean listaChamada3;
    private Boolean listaChamada4;
    private Boolean listaChamada5;
    private Boolean listaChamada6;
    private Boolean listaChamada7;
    private Boolean listaChamada8;
    private Boolean listaChamada9;
    private Boolean listaChamada10;
    private Boolean listaChamada11;
    private Boolean listaChamada12;
    private Boolean listaChamada13;
    private Boolean listaChamada14;
    private Boolean listaChamada15;
    private Boolean listaChamada16;
    private Boolean listaChamada17;
    private Boolean listaChamada18;
    private Boolean listaChamada19;
    private Boolean listaChamada20;
    private Boolean listaChamada21;
    private Boolean listaChamada22;
    private Boolean listaChamada23;
    private Boolean listaChamada24;
    private Boolean listaChamada25;
    private Boolean listaChamada26;
    private Boolean listaChamada27;
    private Boolean listaChamada28;
    private Boolean listaChamada29;
    private Boolean listaChamada30;
    private Boolean listaChamada31;
    private Boolean presenca1 = false;
    private Boolean presenca2 = false;
    private Boolean presenca3 = false;
    private Boolean presenca4 = false;
    private Boolean presenca5 = false;
    private Boolean presenca6 = false;
    private Boolean presenca7 = false;
    private Boolean presenca8 = false;
    private Boolean presenca9 = false;
    private Boolean presenca10 = false;
    private Boolean presenca11 = false;
    private Boolean presenca12 = false;
    private Boolean presenca13 = false;
    private Boolean presenca14 = false;
    private Boolean presenca15 = false;
    private Boolean presenca16 = false;
    private Boolean presenca17 = false;
    private Boolean presenca18 = false;
    private Boolean presenca19 = false;
    private Boolean presenca20 = false;
    private Boolean presenca21 = false;
    private Boolean presenca22 = false;
    private Boolean presenca23 = false;
    private Boolean presenca24 = false;
    private Boolean presenca25 = false;
    private Boolean presenca26 = false;
    private Boolean presenca27 = false;
    private Boolean presenca28 = false;
    private Boolean presenca29 = false;
    private Boolean presenca30 = false;
    private Boolean presenca31 = false;

    /**
     * Construtor padrão da classe <code>MatriculaAlunoHorarioTurma</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public ItemMatriAlunoHorarioTurmaVO() {
        super();
        inicializarDados();
    }

    public String getMatriculaApresentar() {
        return getCliente().getMatricula();
    }

    public String getNomeApresentar() {
        return getPessoa().getNome();
    }

    public Date getDataNascimento() {
        return getContrato().getPessoa().getDataNasc();
    }

    public Date getDataTermino() {
        return getContrato().getVigenciaAteAjustada();
    }

    public String getLista1() {
        if (getListaChamada1() == true && getPresenca1() == true) {
            return ("P");
        } else {
            return ("F");
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>MatriculaAlunoHorarioTurmaVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(ItemMatriAlunoHorarioTurmaVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
        }
        if ((obj.getContrato() == null)
                || (obj.getContrato().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo CONTRATO (Matrícula Aluno Horário Turma) deve ser informado.");
        }
        if ((obj.getPessoa() == null)
                || (obj.getPessoa().getCodigo().intValue() == 0)) {
            throw new ConsistirException("O campo PESSOA (Matrícula Aluno Horário Turma) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
    public void realizarUpperCaseDados() {
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setCodigo(0);
        setContrato(new ContratoVO());
        setPessoa(new PessoaVO());
        setCliente(new ClienteVO());

        setListaChamada1(false);
        setListaChamada2(false);
        setListaChamada3(false);
        setListaChamada4(false);
        setListaChamada5(false);
        setListaChamada6(false);
        setListaChamada7(false);
        setListaChamada8(false);
        setListaChamada9(false);
        setListaChamada10(false);
        setListaChamada11(false);
        setListaChamada12(false);
        setListaChamada13(false);
        setListaChamada14(false);
        setListaChamada15(false);
        setListaChamada16(false);
        setListaChamada17(false);
        setListaChamada18(false);
        setListaChamada19(false);
        setListaChamada20(false);
        setListaChamada21(false);
        setListaChamada22(false);
        setListaChamada23(false);
        setListaChamada24(false);
        setListaChamada25(false);
        setListaChamada26(false);
        setListaChamada27(false);
        setListaChamada28(false);
        setListaChamada29(false);
        setListaChamada30(false);
        setListaChamada31(false);
    }

    /**
     * Retorna o objeto da classe <code>Pessoa</code> relacionado com (<code>MatriculaAlunoHorarioTurma</code>).
     */
    public PessoaVO getPessoa() {
        if (pessoa == null) {
            pessoa = new PessoaVO();
        }
        return (pessoa);
    }

    /**
     * Define o objeto da classe <code>Pessoa</code> relacionado com (<code>MatriculaAlunoHorarioTurma</code>).
     */
    public void setPessoa(PessoaVO obj) {
        this.pessoa = obj;
    }

    /**
     * Retorna o objeto da classe <code>Contrato</code> relacionado com (<code>MatriculaAlunoHorarioTurma</code>).
     */
    public ContratoVO getContrato() {
        if (contrato == null) {
            contrato = new ContratoVO();
        }
        return (contrato);
    }

    /**
     * Define o objeto da classe <code>Contrato</code> relacionado com (<code>MatriculaAlunoHorarioTurma</code>).
     */
    public void setContrato(ContratoVO obj) {
        this.contrato = obj;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Boolean getListaChamada1() {
        return listaChamada1;
    }

    public void setListaChamada1(Boolean listaChamada1) {
        this.listaChamada1 = listaChamada1;
    }

    public Boolean getListaChamada2() {
        return listaChamada2;
    }

    public void setListaChamada2(Boolean listaChamada2) {
        this.listaChamada2 = listaChamada2;
    }

    public Boolean getListaChamada3() {
        return listaChamada3;
    }

    public void setListaChamada3(Boolean listaChamada3) {
        this.listaChamada3 = listaChamada3;
    }

    public Boolean getListaChamada4() {
        return listaChamada4;
    }

    public void setListaChamada4(Boolean listaChamada4) {
        this.listaChamada4 = listaChamada4;
    }

    public Boolean getListaChamada5() {
        return listaChamada5;
    }

    public void setListaChamada5(Boolean listaChamada5) {
        this.listaChamada5 = listaChamada5;
    }

    public Boolean getListaChamada6() {
        return listaChamada6;
    }

    public void setListaChamada6(Boolean listaChamada6) {
        this.listaChamada6 = listaChamada6;
    }

    public Boolean getListaChamada7() {
        return listaChamada7;
    }

    public void setListaChamada7(Boolean listaChamada7) {
        this.listaChamada7 = listaChamada7;
    }

    public Boolean getListaChamada8() {
        return listaChamada8;
    }

    public void setListaChamada8(Boolean listaChamada8) {
        this.listaChamada8 = listaChamada8;
    }

    public Boolean getListaChamada9() {
        return listaChamada9;
    }

    public void setListaChamada9(Boolean listaChamada9) {
        this.listaChamada9 = listaChamada9;
    }

    public Boolean getListaChamada10() {
        return listaChamada10;
    }

    public void setListaChamada10(Boolean listaChamada10) {
        this.listaChamada10 = listaChamada10;
    }

    public Boolean getListaChamada11() {
        return listaChamada11;
    }

    public void setListaChamada11(Boolean listaChamada11) {
        this.listaChamada11 = listaChamada11;
    }

    public Boolean getListaChamada12() {
        return listaChamada12;
    }

    public void setListaChamada12(Boolean listaChamada12) {
        this.listaChamada12 = listaChamada12;
    }

    public Boolean getListaChamada13() {
        return listaChamada13;
    }

    public void setListaChamada13(Boolean listaChamada13) {
        this.listaChamada13 = listaChamada13;
    }

    public Boolean getListaChamada14() {
        return listaChamada14;
    }

    public void setListaChamada14(Boolean listaChamada14) {
        this.listaChamada14 = listaChamada14;
    }

    public Boolean getListaChamada15() {
        return listaChamada15;
    }

    public void setListaChamada15(Boolean listaChamada15) {
        this.listaChamada15 = listaChamada15;
    }

    public Boolean getListaChamada16() {
        return listaChamada16;
    }

    public void setListaChamada16(Boolean listaChamada16) {
        this.listaChamada16 = listaChamada16;
    }

    public Boolean getListaChamada17() {
        return listaChamada17;
    }

    public void setListaChamada17(Boolean listaChamada17) {
        this.listaChamada17 = listaChamada17;
    }

    public Boolean getListaChamada18() {
        return listaChamada18;
    }

    public void setListaChamada18(Boolean listaChamada18) {
        this.listaChamada18 = listaChamada18;
    }

    public Boolean getListaChamada19() {
        return listaChamada19;
    }

    public void setListaChamada19(Boolean listaChamada19) {
        this.listaChamada19 = listaChamada19;
    }

    public Boolean getListaChamada20() {
        return listaChamada20;
    }

    public void setListaChamada20(Boolean listaChamada20) {
        this.listaChamada20 = listaChamada20;
    }

    public Boolean getListaChamada21() {
        return listaChamada21;
    }

    public void setListaChamada21(Boolean listaChamada21) {
        this.listaChamada21 = listaChamada21;
    }

    public Boolean getListaChamada22() {
        return listaChamada22;
    }

    public void setListaChamada22(Boolean listaChamada22) {
        this.listaChamada22 = listaChamada22;
    }

    public Boolean getListaChamada23() {
        return listaChamada23;
    }

    public void setListaChamada23(Boolean listaChamada23) {
        this.listaChamada23 = listaChamada23;
    }

    public Boolean getListaChamada24() {
        return listaChamada24;
    }

    public void setListaChamada24(Boolean listaChamada24) {
        this.listaChamada24 = listaChamada24;
    }

    public Boolean getListaChamada25() {
        return listaChamada25;
    }

    public void setListaChamada25(Boolean listaChamada25) {
        this.listaChamada25 = listaChamada25;
    }

    public Boolean getListaChamada26() {
        return listaChamada26;
    }

    public void setListaChamada26(Boolean listaChamada26) {
        this.listaChamada26 = listaChamada26;
    }

    public Boolean getListaChamada27() {
        return listaChamada27;
    }

    public void setListaChamada27(Boolean listaChamada27) {
        this.listaChamada27 = listaChamada27;
    }

    public Boolean getListaChamada28() {
        return listaChamada28;
    }

    public void setListaChamada28(Boolean listaChamada28) {
        this.listaChamada28 = listaChamada28;
    }

    public Boolean getListaChamada29() {
        return listaChamada29;
    }

    public void setListaChamada29(Boolean listaChamada29) {
        this.listaChamada29 = listaChamada29;
    }

    public Boolean getListaChamada30() {
        return listaChamada30;
    }

    public void setListaChamada30(Boolean listaChamada30) {
        this.listaChamada30 = listaChamada30;
    }

    public Boolean getListaChamada31() {
        return listaChamada31;
    }

    public void setListaChamada31(Boolean listaChamada31) {
        this.listaChamada31 = listaChamada31;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public Boolean getPresenca1() {
        return presenca1;
    }

    public void setPresenca1(Boolean presenca1) {
        this.presenca1 = presenca1;
    }

    public Boolean getPresenca2() {
        return presenca2;
    }

    public void setPresenca2(Boolean presenca2) {
        this.presenca2 = presenca2;
    }

    public Boolean getPresenca3() {
        return presenca3;
    }

    public void setPresenca3(Boolean presenca3) {
        this.presenca3 = presenca3;
    }

    public Boolean getPresenca4() {
        return presenca4;
    }

    public void setPresenca4(Boolean presenca4) {
        this.presenca4 = presenca4;
    }

    public Boolean getPresenca5() {
        return presenca5;
    }

    public void setPresenca5(Boolean presenca5) {
        this.presenca5 = presenca5;
    }

    public Boolean getPresenca6() {
        return presenca6;
    }

    public void setPresenca6(Boolean presenca6) {
        this.presenca6 = presenca6;
    }

    public Boolean getPresenca7() {
        return presenca7;
    }

    public void setPresenca7(Boolean presenca7) {
        this.presenca7 = presenca7;
    }

    public Boolean getPresenca8() {
        return presenca8;
    }

    public void setPresenca8(Boolean presenca8) {
        this.presenca8 = presenca8;
    }

    public Boolean getPresenca9() {
        return presenca9;
    }

    public void setPresenca9(Boolean presenca9) {
        this.presenca9 = presenca9;
    }

    public Boolean getPresenca10() {
        return presenca10;
    }

    public void setPresenca10(Boolean presenca10) {
        this.presenca10 = presenca10;
    }

    public Boolean getPresenca11() {
        return presenca11;
    }

    public void setPresenca11(Boolean presenca11) {
        this.presenca11 = presenca11;
    }

    public Boolean getPresenca12() {
        return presenca12;
    }

    public void setPresenca12(Boolean presenca12) {
        this.presenca12 = presenca12;
    }

    public Boolean getPresenca13() {
        return presenca13;
    }

    public void setPresenca13(Boolean presenca13) {
        this.presenca13 = presenca13;
    }

    public Boolean getPresenca14() {
        return presenca14;
    }

    public void setPresenca14(Boolean presenca14) {
        this.presenca14 = presenca14;
    }

    public Boolean getPresenca15() {
        return presenca15;
    }

    public void setPresenca15(Boolean presenca15) {
        this.presenca15 = presenca15;
    }

    public Boolean getPresenca16() {
        return presenca16;
    }

    public void setPresenca16(Boolean presenca16) {
        this.presenca16 = presenca16;
    }

    public Boolean getPresenca17() {
        return presenca17;
    }

    public void setPresenca17(Boolean presenca17) {
        this.presenca17 = presenca17;
    }

    public Boolean getPresenca18() {
        return presenca18;
    }

    public void setPresenca18(Boolean presenca18) {
        this.presenca18 = presenca18;
    }

    public Boolean getPresenca19() {
        return presenca19;
    }

    public void setPresenca19(Boolean presenca19) {
        this.presenca19 = presenca19;
    }

    public Boolean getPresenca20() {
        return presenca20;
    }

    public void setPresenca20(Boolean presenca20) {
        this.presenca20 = presenca20;
    }

    public Boolean getPresenca21() {
        return presenca21;
    }

    public void setPresenca21(Boolean presenca21) {
        this.presenca21 = presenca21;
    }

    public Boolean getPresenca22() {
        return presenca22;
    }

    public void setPresenca22(Boolean presenca22) {
        this.presenca22 = presenca22;
    }

    public Boolean getPresenca23() {
        return presenca23;
    }

    public void setPresenca23(Boolean presenca23) {
        this.presenca23 = presenca23;
    }

    public Boolean getPresenca24() {
        return presenca24;
    }

    public void setPresenca24(Boolean presenca24) {
        this.presenca24 = presenca24;
    }

    public Boolean getPresenca25() {
        return presenca25;
    }

    public void setPresenca25(Boolean presenca25) {
        this.presenca25 = presenca25;
    }

    public Boolean getPresenca26() {
        return presenca26;
    }

    public void setPresenca26(Boolean presenca26) {
        this.presenca26 = presenca26;
    }

    public Boolean getPresenca27() {
        return presenca27;
    }

    public void setPresenca27(Boolean presenca27) {
        this.presenca27 = presenca27;
    }

    public Boolean getPresenca28() {
        return presenca28;
    }

    public void setPresenca28(Boolean presenca28) {
        this.presenca28 = presenca28;
    }

    public Boolean getPresenca29() {
        return presenca29;
    }

    public void setPresenca29(Boolean presenca29) {
        this.presenca29 = presenca29;
    }

    public Boolean getPresenca30() {
        return presenca30;
    }

    public void setPresenca30(Boolean presenca30) {
        this.presenca30 = presenca30;
    }

    public Boolean getPresenca31() {
        return presenca31;
    }

    public void setPresenca31(Boolean presenca31) {
        this.presenca31 = presenca31;
    }
}

package relatorio.negocio.comuns.basico;

import java.io.Serializable;
import negocio.comuns.basico.enumerador.TipoBIDCC;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class BiDCCRelVO implements Serializable {

    private TipoBIDCC tipo = null;
    private double valor = 0.0;
    private int qtd = 0;
    private List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs = new ArrayList<PendenciaResumoPessoaRelVO>();
    private boolean exibirMensagem = false;
    private boolean exibirAutorizacaoCobrancaCliente = false;
    private boolean exibirInformacoesPessoa = true;
    private boolean exibirInformacoesFinanceiras = false;

    public BiDCCRelVO(TipoBIDCC tipo,int qtd,double sum){
        this.tipo = tipo;
        this.qtd = qtd;
        this.valor = sum;
    }

    public BiDCCRelVO(JSONObject json){
        this.tipo = TipoBIDCC.obterPorID(json.getInt("tipo"));
        this.qtd = json.getInt("qtd");
        this.valor =  json.getDouble("valor");
    }

    public BiDCCRelVO(){
    }
    public TipoBIDCC getTipo() {
        return tipo;
    }

    public void setTipo(TipoBIDCC tipo) {
        this.tipo = tipo;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public int  getQtd() {
        return qtd;
    }

    public void setQtd(int qtd) {
        this.qtd = qtd;
    }

    public List<PendenciaResumoPessoaRelVO> getListaPendenciaResumoPessoaRelVOs() {
        return listaPendenciaResumoPessoaRelVOs;
    }

    public void setListaPendenciaResumoPessoaRelVOs(List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs) {
        this.listaPendenciaResumoPessoaRelVOs = listaPendenciaResumoPessoaRelVOs;
    }

    public boolean isExibirMensagem() {
        return exibirMensagem;
    }

    public void setExibirMensagem(boolean exibirMensagem) {
        this.exibirMensagem = exibirMensagem;
    }

    public boolean isExibirAutorizacaoCobrancaCliente() {
        return exibirAutorizacaoCobrancaCliente;
    }

    public void setExibirAutorizacaoCobrancaCliente(boolean exibirAutorizacaoCobrancaCliente) {
        this.exibirAutorizacaoCobrancaCliente = exibirAutorizacaoCobrancaCliente;
    }

    public boolean isExibirInformacoesPessoa() {
        return exibirInformacoesPessoa;
    }

    public void setExibirInformacoesPessoa(boolean exibirInformacoesPessoa) {
        this.exibirInformacoesPessoa = exibirInformacoesPessoa;
    }

    public boolean isExibirInformacoesFinanceiras() {
        return exibirInformacoesFinanceiras;
    }

    public void setExibirInformacoesFinanceiras(boolean exibirInformacoesFinanceiras) {
        this.exibirInformacoesFinanceiras = exibirInformacoesFinanceiras;
    }
    public String getAbrirPopUp(){
        if(getTipo() == TipoBIDCC.ContratosAtivosRecorrencia ||
                getTipo() == TipoBIDCC.ContratosCanceladosRecorrencia ||
                getTipo() == TipoBIDCC.ContratosNaoRenovadosRecorrencia ||
                getTipo() == TipoBIDCC.ContratosSemAutorizacaoCobranca ||
                getTipo() == TipoBIDCC.ContratosSemAutorizacaoCobranca ||
                getTipo() == TipoBIDCC.ParcelasCanceladas ||
                getTipo() == TipoBIDCC.ParcelasEmAberto ||
                getTipo() == TipoBIDCC.OperacoesSuspeitas
                ){
            return "abrirPopup('recorrenciaClienteForm.jsp', 'RotatividadeCliente', 850, 650);";
        } else  if( getTipo() == TipoBIDCC.CartoesCreditoVencidos ||
                    getTipo() == TipoBIDCC.CartoesCreditoAVencer ||
                    getTipo() == TipoBIDCC.ClientesMesmoCartao){
            return "abrirPopup('./pendenciaResumoPessoaRes.jsp', 'objResumoPessoaRel', 850, 650);";
        } else if(getTipo() == TipoBIDCC.ParcelasVencidasEmAberto){
                return "abrirPopup('./parcelasVencidasEmAberto.jsp', 'ParcelasVencidasEmAberto', 1024, 650);";
        } else if( getTipo() == TipoBIDCC.AlunosAdimplentes ){
            return "abrirPopup('recorrenciaClienteAdimplentesForm.jsp', 'RotatividadeCliente', 850, 650);";
        }else if( getTipo() == TipoBIDCC.CartoesComProblema){
            return "abrirPopup('pendenciaResumoPessoaRes.jsp', 'RotatividadeCliente', 850, 650);";
        }

        return "";
    }
    public int getOrdem(){
        return getTipo().getId();
    }
}

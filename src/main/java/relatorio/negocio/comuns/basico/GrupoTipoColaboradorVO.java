/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Colaborador;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoMesVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 *
 * <AUTHOR>
 */
public class GrupoTipoColaboradorVO extends SuperVO {

    private TipoColaboradorEnum tipoColaborador;
    private List<UsuarioVO> usuarios;
    private boolean todosUsuariosSelecionados = false;


    public GrupoTipoColaboradorVO() {
    }

    public TipoColaboradorEnum getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(TipoColaboradorEnum tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }

    public List<UsuarioVO> getUsuarios() {
        if(usuarios == null)
            usuarios = new ArrayList<UsuarioVO>();
        return usuarios;
    }

    public void setUsuarios(List<UsuarioVO> usuarios) {
        this.usuarios = usuarios;
    }

    public boolean isTodosUsuariosSelecionados() {
        return todosUsuariosSelecionados;
    }

    public void setTodosUsuariosSelecionados(boolean todosUsuariosSelecionados) {
        this.todosUsuariosSelecionados = todosUsuariosSelecionados;
    }

    public Integer getCodigoTipoColaboradorOrdinal() {
        if (tipoColaborador == null) {
            return  999;
        } else {
            return tipoColaborador.ordinal();
        }
    }

    public String getTipoColaborador_Apresentar() {
        if (tipoColaborador == null) {
            return  "SEM TIPO";
        } else {
            return tipoColaborador.getDescricao();
        }
    }
}

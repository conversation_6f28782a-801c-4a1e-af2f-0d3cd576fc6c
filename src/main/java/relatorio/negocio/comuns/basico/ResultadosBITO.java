package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;
import java.util.HashMap;

/**
 * Created by <PERSON> on 17/06/2016.
 */
public class ResultadosBITO extends SuperTO {
    private String key;
    private int empresa;
    private String vinculoColaborador;
    private Date data;
    private Date dataGeracao;
    private BIEnum bi;
    private HashMap<String,ResultadoBITO> resultadosBI;

    public ResultadosBITO(){
        this.setDataGeracao(Calendario.hoje());
        this.setResultadosBI(new HashMap<String,ResultadoBITO>());
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getVinculoColaborador() {
        return vinculoColaborador;
    }

    public void setVinculoColaborador(String vinculoColaborador) {
        this.vinculoColaborador = vinculoColaborador;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getDataGeracao() {
        return dataGeracao;
    }

    public void setDataGeracao(Date dataGeracao) {
        this.dataGeracao = dataGeracao;
    }

    public HashMap<String,ResultadoBITO> getResultadosBI() {
        return resultadosBI;
    }

    public void setResultadosBI(HashMap<String,ResultadoBITO> resultadosBI) {
        this.resultadosBI = resultadosBI;
    }

    public BIEnum getBi() {
        return bi;
    }

    public void setBi(BIEnum bi) {
        this.bi = bi;
    }
    public String getIdentificador(){
        return getKey()+ Uteis.getData(getData())+getEmpresa()+getVinculoColaborador();
    }
}

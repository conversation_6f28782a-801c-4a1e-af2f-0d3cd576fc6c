package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

public class GruposConsultasTO extends SuperTO {

    private String nomeGrupo = "";
    private String categoriaAberta = "false";
    private boolean somenteUsuarioPactoSolucoes = false;
    private List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

    public GruposConsultasTO() {

    }

    public GruposConsultasTO(String nomeGrupo) {
        this.nomeGrupo = nomeGrupo;
    }

    public String getNomeGrupo() {
        return nomeGrupo;
    }

    public void setNomeGrupo(String nomeGrupo) {
        this.nomeGrupo = nomeGrupo;
    }

    public String getCategoriaAberta() {
        return categoriaAberta;
    }

    public void setCategoriaAberta(String categoriaAberta) {
        this.categoriaAberta = categoriaAberta;
    }

    public List<ConsultaTO> getConsultas() {
        return consultas;
    }

    public void setConsultas(List<ConsultaTO> consultas) {
        this.consultas = consultas;
    }

    public boolean isSomenteUsuarioPactoSolucoes() {
        return somenteUsuarioPactoSolucoes;
    }

    public void setSomenteUsuarioPactoSolucoes(boolean somenteUsuarioPactoSolucoes) {
        this.somenteUsuarioPactoSolucoes = somenteUsuarioPactoSolucoes;
    }
}

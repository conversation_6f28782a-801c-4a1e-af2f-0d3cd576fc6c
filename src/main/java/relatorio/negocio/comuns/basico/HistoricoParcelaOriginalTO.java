package relatorio.negocio.comuns.basico;

import negocio.comuns.utilitarias.Calendario;
import java.util.Date;

public class HistoricoParcelaOriginalTO {

    private String id;
    private String idRenegociada;
    private Double valor;
    private Date vencimento;
    private String situacao;
    private Date dataOperacao;
    private String origem;

    public HistoricoParcelaOriginalTO() {
    }

    public HistoricoParcelaOriginalTO(String id, Double valor, Date vencimento, String situacao, Date dataOperacao, String origem) {
        this.id = id;
        this.valor = valor;
        this.vencimento = vencimento;
        this.situacao = situacao;
        this.dataOperacao = dataOperacao;
        this.origem = origem;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Date getVencimento() {
        return vencimento;
    }

    public void setVencimento(Date vencimento) {
        this.vencimento = vencimento;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getVencimento_Apresentar() {
        if(vencimento != null){
            return Calendario.getData(vencimento, "dd/MM/yyyy");
        }
        return "";
    }

    public String getDataOperacao_Apresentar() {
        if(dataOperacao != null){
            return Calendario.getData(dataOperacao, "dd/MM/yyyy");
        }
        return "";
    }

    public String getIdRenegociada() {
        return idRenegociada;
    }

    public void setIdRenegociada(String idRenegociada) {
        this.idRenegociada = idRenegociada;
    }
}

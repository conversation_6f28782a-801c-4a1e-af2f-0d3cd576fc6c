package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class ResumoModalidadeTO extends SuperTO {

    private static final long serialVersionUID = -7609152868724592405L;
    private String modalidade = "";
    private String clientes = "";
    private String contratos = "";
    private String competencia = "";
    private String faturamento = "";

    public ResumoModalidadeTO(String modalidade, String clientes, String contratos, String competencia, String faturamento) {
        this.modalidade = modalidade;
        this.clientes = clientes;
        this.contratos = contratos;
        this.competencia = competencia;
        this.faturamento = faturamento;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    public String getClientes() {
        return clientes;
    }

    public void setClientes(String clientes) {
        this.clientes = clientes;
    }

    public String getContratos() {
        return contratos;
    }

    public void setContratos(String contratos) {
        this.contratos = contratos;
    }

    public String getCompetencia() {
        return competencia;
    }

    public void setCompetencia(String competencia) {
        this.competencia = competencia;
    }

    public String getFaturamento() {
        return faturamento;
    }

    public void setFaturamento(String faturamento) {
        this.faturamento = faturamento;
    }
}

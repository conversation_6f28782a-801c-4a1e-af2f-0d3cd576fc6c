/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.SituacaoParcelaEnum;
import negocio.comuns.arquitetura.LogAgrupadoChavePrimaria;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/*
 * Created by <PERSON><PERSON> on 28/05/2018.
 */
public class ResumoPessoaRelBIInadimplenciaVO extends SuperVO {

    private String matricula;
    private String nome;
    private Integer contrato;
    private Integer cliente;
    private String situacaoCliente;
    private Integer movParcela;
    private Double valorParcela;
    private Date dataVencimentoParcela;
    private Date dataPagamentoParcela;
    private String situacaoParcela;
    private String situacaoParcelaDiaPagamento;
    private Date dataMatricula;
    private String telefone;
    private String email;
    private String descricaoParcela;

    private Integer nrParcela;
    private Integer qtdParcelas;
    private String prefixo;
    private String empresa;
    private String convenio;
    private String retorno;
    private String ultimaCobranca;

    private Integer nrtentativas;
    private Date dataultimatentativa;
    private String meioultimatentativa;
    private String codigoretorno;
    private String motivoretorno;
    private Date dataCancelamentoParcela;

    public ResumoPessoaRelBIInadimplenciaVO() {
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getContrato() {
        if (contrato == null) {
            contrato = 0;
        }
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getMovParcela() {
        if (movParcela == null) {
            movParcela = 0;
        }
        return movParcela;
    }

    public void setMovParcela(Integer movParcela) {
        this.movParcela = movParcela;
    }

    public Double getValorParcela() {
        if (valorParcela == null) {
            valorParcela = 0.0;
        }
        return valorParcela;
    }

    public void setValorParcela(Double valorParcela) {
        this.valorParcela = valorParcela;
    }

    public Date getDataVencimentoParcela() {
        return dataVencimentoParcela;
    }

    public void setDataVencimentoParcela(Date dataVencimentoParcela) {
        this.dataVencimentoParcela = dataVencimentoParcela;
    }

    public String getSituacaoParcela() {
        if (situacaoParcela == null) {
            situacaoParcela = "";
        }
        return situacaoParcela;
    }

    public void setSituacaoParcela(String situacaoParcela) {
        this.situacaoParcela = situacaoParcela;
    }

    public String getValorParcela_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorParcela());
    }

    public String getDataVencimento_Apresentar() {
        if (getDataVencimentoParcela() == null) {
            return "";
        } else {
            return Uteis.getData(getDataVencimentoParcela());
        }
    }

    public Date getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(Date dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public String getDataMatricula_Apresentar() {
        if (getDataMatricula() == null) {
            return "";
        } else {
            return Uteis.getData(getDataMatricula());
        }
    }

    public String getSituacaoParcelaDiaPagamento() {
        if (situacaoParcelaDiaPagamento == null) {
            situacaoParcelaDiaPagamento = "";
        }
        return situacaoParcelaDiaPagamento;
    }

    public void setSituacaoParcelaDiaPagamento(String situacaoParcelaDiaPagamento) {
        this.situacaoParcelaDiaPagamento = situacaoParcelaDiaPagamento;
    }

    public Integer getCliente() {
        if (cliente == null) {
            cliente = 0;
        }
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getTelefone() {
        if (telefone == null) {
            telefone = "";
        }
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        if (email == null) {
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDescricaoParcela() {
        if (descricaoParcela == null) {
            descricaoParcela = "";
        }
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getSituacaoParcelaDiaPagamento_Apresentar() {
        return obterDescricaoSituacaoParcela(getSituacaoParcelaDiaPagamento());
    }

    public String getSituacaoParcela_Apresentar() {
        if (getSituacaoParcela() == null) {
            return "";
        }

        String situacoes[] = getSituacaoParcela().split(",");
        String situacoesApresentar = "";

        for (String item : situacoes) {
            String situacaoNova = obterDescricaoSituacaoParcela(item.trim());
            if (UteisValidacao.emptyString(situacaoNova)) {
                continue;
            }

            if (!situacoesApresentar.equals("")) {
                situacoesApresentar += ", ";
            }
            situacoesApresentar += situacaoNova;
        }

        return situacoesApresentar;
    }

    public String obterDescricaoSituacaoParcela(String sigla) {
        if (sigla == null) {
            return "";
        }
        if (sigla.equals("EA")) {
            return "Em Aberto";
        }
        if (sigla.equals("PG")) {
            return "Pago";
        }
        if (sigla.equals("CA")) {
            return "Cancelado";
        }
        if (sigla.equals("RG")) {
            return "Renegociado";
        }
        return "";
    }

    public Integer getNrParcela() {
        if(nrParcela == null){
            try{
                // considerar que o número da parcela são as duas últimas letras da descrição da parcela.
                if (this.descricaoParcela != null){
                    String nrParcelastr = descricaoParcela.substring(descricaoParcela.indexOf("PARCELA") + 7,
                            descricaoParcela.length()).trim();
                    return Integer.parseInt(nrParcelastr);
                }
            }catch (Exception e){
                nrParcela = 1;
            }
        }
        return nrParcela;
    }

    public void setNrParcela(Integer nrParcela) {
        this.nrParcela = nrParcela;
    }

    public Integer getQtdParcelas() {
        if (qtdParcelas == null) {
            qtdParcelas = 1;
        }
        return qtdParcelas;
    }

    public void setQtdParcelas(Integer qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public String getPrefixo() {
        return prefixo;
    }

    public void setPrefixo(String prefixo) {
        this.prefixo = prefixo;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getConvenio() {
        return convenio;
    }

    public void setConvenio(String convenio) {
        this.convenio = convenio;
    }

    public String getRetorno() {
        return retorno;
    }

    public void setRetorno(String retorno) {
        this.retorno = retorno;
    }

    public String getUltimaCobranca() {
        return ultimaCobranca;
    }

    public void setUltimaCobranca(String ultimaCobranca) {
        this.ultimaCobranca = ultimaCobranca;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public Date getDataPagamentoParcela() {
        return dataPagamentoParcela;
    }

    public void setDataPagamentoParcela(Date dataPagamentoParcela) {
        this.dataPagamentoParcela = dataPagamentoParcela;
    }

    public String getDataPagamento_Apresentar() {
        if (getDataPagamentoParcela() == null) {
            return "";
        } else {
            return Uteis.getData(getDataPagamentoParcela());
        }
    }

    public Integer getNrtentativas() {
        return nrtentativas;
    }

    public void setNrtentativas(Integer nrtentativas) {
        this.nrtentativas = nrtentativas;
    }

    public Date getDataultimatentativa() {
        return dataultimatentativa;
    }

    public void setDataultimatentativa(Date dataultimatentativa) {
        this.dataultimatentativa = dataultimatentativa;
    }

    public String getMeioultimatentativa() {
        return meioultimatentativa;
    }

    public void setMeioultimatentativa(String meioultimatentativa) {
        this.meioultimatentativa = meioultimatentativa;
    }

    public String getCodigoretorno() {
        return codigoretorno;
    }

    public void setCodigoretorno(String codigoretorno) {
        this.codigoretorno = codigoretorno;
    }

    public String getMotivoretorno() {
        return motivoretorno;
    }

    public void setMotivoretorno(String motivoretorno) {
        this.motivoretorno = motivoretorno;
    }

    public Date getDataCancelamentoParcela() {
        return dataCancelamentoParcela;
    }

    public void setDataCancelamentoParcela(Date dataCancelamentoParcela) {
        this.dataCancelamentoParcela = dataCancelamentoParcela;
    }

    public String getDataCancelamentoParcela_Apresentar() {
        if (dataCancelamentoParcela == null) {
            return getSituacaoParcela_Apresentar();
        }
        return "Parcela cancelada em " + Uteis.getData(dataCancelamentoParcela);
    }

    public boolean isApresentarBotaoCliente(){
        if(UteisValidacao.emptyString(matricula) || matricula.startsWith("cl")){
            return false;
        }
        return true;
    }
}

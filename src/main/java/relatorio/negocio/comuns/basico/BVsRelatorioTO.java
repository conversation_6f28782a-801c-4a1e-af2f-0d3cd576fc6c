/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.basico;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.PerguntaVO;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.category.DefaultCategoryDataset;
import org.json.JSONArray;

/**
 * Contém os dados que serão apresentados na consulta do relatório
 * <AUTHOR>
 */
public class BVsRelatorioTO extends SuperTO {
    private static final long serialVersionUID = 4959555794961891169L;

    private PerguntaVO perguntaVO;
    //lista com as respostas, numero de respostas e percentual de respostas
    private List<BVsRelatorioListasTO> listaBVsRelatorioListasTO;
    private Integer totalGeral;
    //usado no gráfico de pizza
    private DefaultPieDataset dataSetPizza;
    //usado no gráfico de barra
    private DefaultCategoryDataset dataSetBarra;
    private boolean pizza = true;
    private boolean barra = false;
    private Integer totalConversoes = 0;

    private String jsonGrafico;

    public BVsRelatorioTO() {
        perguntaVO = new PerguntaVO();
        listaBVsRelatorioListasTO = new ArrayList<BVsRelatorioListasTO>();
        totalGeral = 0;
        dataSetPizza = new DefaultPieDataset();
        dataSetBarra = new DefaultCategoryDataset();
    }

    /**
     * @return the perguntaVO
     */
    public PerguntaVO getPerguntaVO() {
        return perguntaVO;
    }

    /**
     * @param perguntaVO the perguntaVO to set
     */
    public void setPerguntaVO(PerguntaVO perguntaVO) {
        this.perguntaVO = perguntaVO;
    }

    /**
     * @return the totalGeral
     */
    public Integer getTotalGeral() {
        if (totalGeral == null) {
            totalGeral = 0;
        }
        return totalGeral;
    }

    /**
     * @param totalGeral the totalGeral to set
     */
    public void setTotalGeral(Integer totalGeral) {
        this.totalGeral = totalGeral;
    }

    /**
     * @return the listaBVsRelatorioListasTO
     */
    public List<BVsRelatorioListasTO> getListaBVsRelatorioListasTO() {
        return listaBVsRelatorioListasTO;
    }

    /**
     * @param listaBVsRelatorioListasTO the listaBVsRelatorioListasTO to set
     */
    public void setListaBVsRelatorioListasTO(List<BVsRelatorioListasTO> listaBVsRelatorioListasTO) {
        this.listaBVsRelatorioListasTO = listaBVsRelatorioListasTO;
    }

    /**
     * @return the dataSetPizza
     */
    public DefaultPieDataset getDataSetPizza() {
        return dataSetPizza;
    }

    /**
     * @param dataSetPizza the dataSetPizza to set
     */
    public void setDataSetPizza(DefaultPieDataset dataSetPizza) {
        this.dataSetPizza = dataSetPizza;
    }

    /**
     * @return the dataSetBarra
     */
    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    /**
     * @param dataSetBarra the dataSetBarra to set
     */
    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    /**
     * @return the pizza
     */
    public boolean isPizza() {
        return pizza;
    }

    /**
     * @param pizza the pizza to set
     */
    public void setPizza(boolean pizza) {
        this.pizza = pizza;
    }

    /**
     * @return the barra
     */
    public boolean isBarra() {
        return barra;
    }

    /**
     * @param barra the barra to set
     */
    public void setBarra(boolean barra) {
        this.barra = barra;
    }

    public Integer getTotalConversoes() {
        return totalConversoes;
    }

    public void setTotalConversoes(Integer totalConversoes) {
        this.totalConversoes = totalConversoes;
    }

    public String getJsonGrafico() {
        if (jsonGrafico == null) {
            jsonGrafico = new JSONArray().toString();
        }
        return jsonGrafico;
    }

    public void setJsonGrafico(String jsonGrafico) {
        this.jsonGrafico = jsonGrafico;
    }
}

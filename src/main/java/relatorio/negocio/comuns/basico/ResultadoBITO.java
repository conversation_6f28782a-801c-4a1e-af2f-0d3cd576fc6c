package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.BIEnum;
import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;
import java.util.HashMap;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class ResultadoBITO extends SuperTO {
    private HashMap<String,Object> resultadosBI;
    private Date diaGerado;

    public ResultadoBITO(){
       this.setResultadosBI(new HashMap<String, Object>());
    }
    public HashMap<String, Object> getResultadosBI() {
        return resultadosBI;
    }

    public void setResultadosBI(HashMap<String, Object> resultadosBI) {
        this.resultadosBI = resultadosBI;
    }

    public Date getDiaGerado() {
        return diaGerado;
    }

    public void setDiaGerado(Date diaGerado) {
        this.diaGerado = diaGerado;
    }
}

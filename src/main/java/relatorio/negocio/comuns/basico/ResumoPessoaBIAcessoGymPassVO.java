package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class ResumoPessoaBIAcessoGymPassVO extends SuperVO {

    private String matricula;
    private String nome;
    private Integer cliente;
    private String situacaoCliente;
    private Date dataAcesso;
    private String tokenGymPass;

    public ResumoPessoaBIAcessoGymPassVO() {
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public Date getDataAcesso() {
        return dataAcesso;
    }

    public void setDataAcesso(Date dataAcesso) {
        this.dataAcesso = dataAcesso;
    }

    public String getTokenGymPass() {
        return tokenGymPass;
    }

    public void setTokenGymPass(String tokenGymPass) {
        this.tokenGymPass = tokenGymPass;
    }

    public String getDataAcesso_Apresentar() {
        if (getDataAcesso() == null) {
            return "";
        } else {
            return Uteis.formataComHorarioSeDiferenteZero(getDataAcesso());
        }
    }
}

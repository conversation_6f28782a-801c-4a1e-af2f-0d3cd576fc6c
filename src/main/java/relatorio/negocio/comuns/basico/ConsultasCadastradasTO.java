package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.enumeradores.EImportacaoClinteEstacionamento;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT ON 02/03/14.
 */
public class ConsultasCadastradasTO extends SuperTO {

    private static ConsultasCadastradasTO instance = null;
    private static Integer empresaLogada = null;
    private List<GruposConsultasTO> consultasCadastradas = null;

    public static ConsultasCadastradasTO getInstance(Integer codigoEmpresa) {
        instance = new ConsultasCadastradasTO();
        empresaLogada = codigoEmpresa;
        return instance;
    }

    /**
     * Sempre colocar a empresa, em todas as consultas.
     *
     * @return lista List<GruposConsultasTO>
     */
    private List<GruposConsultasTO> inicializarConsultasCadastradas() {
        consultasCadastradas = new ArrayList<GruposConsultasTO>();

        //Sempre colocar a empresa na consulta;
        consultasCadastradas.add(incluirGrupoConsultas("Remessa", consultasRemessa()));
        consultasCadastradas.add(incluirGrupoConsultas("Recibo", consultasRecibo()));
        consultasCadastradas.add(incluirGrupoConsultas("Acesso", consultasAcesso()));
        consultasCadastradas.add(incluirGrupoConsultas("Clientes", consultasCliente()));
        consultasCadastradas.add(incluirGrupoConsultas("Produtos", consultasProdutos()));
        consultasCadastradas.add(incluirGrupoConsultas("NFSe", consultasNotaFiscalServicoEletronica()));
        consultasCadastradas.add(incluirGrupoConsultas("Recorrência", consultasRecorrencia()));
        consultasCadastradas.add(incluirGrupoConsultas("Familiares", consultasFamilia()));
        consultasCadastradas.add(incluirGrupoConsultas("Alunos", consultasAlunos()));
        consultasCadastradas.add(incluirGrupoConsultas("Colaboradores Aniversariantes", consultaColaboradores()));
        consultasCadastradas.add(incluirGrupoConsultas("Colaboradores", consultaGeralColaboradores()));
        consultasCadastradas.add(incluirGrupoConsultas("Dados Gerenciais", consultaGeralDadosGeraisPMG()));
        consultasCadastradas.add(incluirGrupoConsultas("Contratos", consultasContratos()));
        consultasCadastradas.add(incluirGrupoConsultas("Grupo com desconto", consultasGrupoDesconto()));

        Ordenacao.ordenarLista(consultasCadastradas, "nomeGrupo");


        //Consultar para serem apresentadas somente para os usuários "admin" ou "PACTOBR"
        //Adicionada depois da ordenação para poder ficar por ULTIMO!
        //by Luiz Felipe 24/04/2020
        GruposConsultasTO consultasPactoTO = incluirGrupoConsultas("Consultas - Pacto Soluções (Interno)", consultasInternasPactoSolucoes());
        consultasPactoTO.setSomenteUsuarioPactoSolucoes(true);
        consultasCadastradas.add(consultasPactoTO);

        return consultasCadastradas;
    }

    private GruposConsultasTO incluirGrupoConsultas(String nomeGrupo, List<ConsultaTO> consultasGrupo) {
        GruposConsultasTO grupo = new GruposConsultasTO(nomeGrupo);
        grupo.getConsultas().addAll(consultasGrupo);
        Ordenacao.ordenarLista(grupo.getConsultas(), "nomeConsulta");
        return grupo;
    }

    private List<ConsultaTO> consultasCliente() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consultaTO = new ConsultaTO("TodosOsClientes", "Todos os Clientes", "Cliente com endereço, BV, Idade e Colaborador vinculado.");

        consultaTO.getColunas().add("sc.matricula");
        consultaTO.getColunas().add("pes.codigo AS codigoPessoa");
        consultaTO.getColunas().add("sc.nomecliente");
        consultaTO.getColunas().add("cat.nome as categoria");
        consultaTO.getColunas().add("sc.idade");
        consultaTO.getColunas().add("(SELECT ende.tipoendereco from endereco ende where ende.pessoa = pes.codigo limit 1) AS end_Tipo");
        consultaTO.getColunas().add("(SELECT ende.endereco     from endereco ende where ende.pessoa = pes.codigo limit 1) AS end_endereco");
        consultaTO.getColunas().add("(SELECT ende.numero       from endereco ende where ende.pessoa = pes.codigo limit 1) AS end_numero");
        consultaTO.getColunas().add("(SELECT ende.bairro       from endereco ende where ende.pessoa = pes.codigo limit 1) AS end_bairro");
        consultaTO.getColunas().add("cida.nome as end_cidade");
        consultaTO.getColunas().add("(SELECT ende.cep          from endereco ende where ende.pessoa = pes.codigo limit 1) AS end_cep");
        consultaTO.getColunas().add("uf.sigla as end_estado");
        consultaTO.getColunas().add("(select numero from telefone tel3 where tel3.pessoa = pes.codigo and tel3.tipotelefone = 'RE' limit 1 ) as Tel_Residencial");
        consultaTO.getColunas().add("(select numero from telefone tel2 where tel2.pessoa = pes.codigo and tel2.tipotelefone = 'CE' limit 1 ) as Tel_Celular");
        consultaTO.getColunas().add("(select numero from telefone tel1 where tel1.pessoa = pes.codigo and tel1.tipotelefone = 'CO' limit 1 ) as Tel_Comercial");
        consultaTO.getColunas().add("pes.datanasc");
        consultaTO.getColunas().add("pes.cfp");
        consultaTO.getColunas().add("pes.estadocivil");
        consultaTO.getColunas().add("prof.descricao as nome_profissao");
        consultaTO.getColunas().add("pes.sexo");
        consultaTO.getColunas().add("CASE WHEN sc.situacao = 'IN'THEN 'INATIVO'\n" +
                "     WHEN sc.situacao = 'AT'THEN 'ATIVO'\n" +
                "     WHEN sc.situacao = 'VI'THEN 'VISITANTE'\n" +
                "     ELSE sc.situacao\n" +
                "END as situacaocliente");
        consultaTO.getColunas().add("CASE WHEN sc.situacaocontrato = 'DE' THEN 'DESISTENTE'\n" +
                "     WHEN sc.situacaocontrato = 'NO' THEN 'ATIVO'\n" +
                "     WHEN sc.situacaocontrato = 'VE' THEN 'VENCIDO'\n" +
                "     WHEN sc.situacaocontrato = 'AV' THEN 'A VENCER'\n" +
                "     WHEN sc.situacaocontrato = 'CA' THEN 'CANCELADO'\n" +
                " ELSE sc.situacaocontrato END as situacaocontrato");
        consultaTO.getColunas().add("pes.datacadastro");
        consultaTO.getColunas().add("sc.datamatricula");
        consultaTO.getColunas().add("sc.datarematriculacontrato");
        consultaTO.getColunas().add("sc.nomeplano Plano_Nome");
        consultaTO.getColunas().add("sc.duracaocontratomeses Plano_Mes_Duracao");
        consultaTO.getColunas().add("sc.dataultimobv as Data_Ultimo_BV");
        consultaTO.getColunas().add("sc.colaboradores as Colaboradores_Vinculados");
        consultaTO.getColunas().add("emp.nome as nome_empresa");
        consultaTO.getColunas().add("con.valorfinal as valor_Ultimo_Contrato");

        consultaTO.setTabelaPrincipal("situacaoclientesinteticodw sc");

        consultaTO.getJoins().add("left join cliente cli on sc.codigocliente = cli.codigo");
        consultaTO.getJoins().add("left join pessoa pes on cli.pessoa = pes.codigo");
        consultaTO.getJoins().add("left join cidade cida on pes.cidade = cida.codigo");
        consultaTO.getJoins().add("left join pais on pais.codigo = cida.pais");
        consultaTO.getJoins().add("left join estado uf on uf.codigo = pes.estado");
        consultaTO.getJoins().add("left join profissao prof on prof.codigo = pes.profissao");
        consultaTO.getJoins().add("left join empresa emp on cli.empresa = emp.codigo");
        consultaTO.getJoins().add("left join contrato con on sc.codigocontrato = con.codigo");
        consultaTO.getJoins().add("left join categoria cat on cat.codigo = cli.categoria");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("ClientesCarencia", "Clientes em Férias", "Clientes que estão de férias");

        consultaTO.getColunas().add("cli.codigomatricula");
        consultaTO.getColunas().add("pes.nome");
        consultaTO.getColunas().add("(select nome from categoria where codigo = "
                + "(SELECT categoria FROM cliente WHERE pessoa = pes.codigo LIMIT 1)) as categoria ");
        consultaTO.getColunas().add("con.codigo AS contrato");
        consultaTO.getColunas().add("dataoperacao");
        consultaTO.getColunas().add("datainicioefetivacaooperacao");
        consultaTO.getColunas().add("datafimefetivacaooperacao");
        consultaTO.getColunas().add("emp.nome   AS empresa");

        consultaTO.setTabelaPrincipal("contratooperacao coop");

        consultaTO.getJoins().add("INNER JOIN contrato con ON con.codigo = coop.contrato AND coop.tipooperacao = 'CR'");
        consultaTO.getJoins().add("INNER JOIN pessoa pes ON con.pessoa = pes.codigo");
        consultaTO.getJoins().add("INNER JOIN cliente cli ON cli.pessoa = pes.codigo");
        consultaTO.getJoins().add("INNER JOIN empresa emp ON con.empresa = emp.codigo");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("1 = 1\n" +
                "      AND (NOW() BETWEEN datainicioefetivacaooperacao AND datafimefetivacaooperacao)");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("ClientesFreePass", "Clientes com Free Pass", "Clientes que estão de Free Pass em determinado período");


        consultaTO.getColunas().add("cli.codigomatricula");
        consultaTO.getColunas().add("pes.nome");
        consultaTO.getColunas().add("cli.situacao");
        consultaTO.getColunas().add("cat.nome as categoria");
        consultaTO.getColunas().add("us.nome AS responsavel");
        consultaTO.getColunas().add("pa.datainicioacesso :: DATE");
        consultaTO.getColunas().add("pa.datafinalacesso :: DATE");

        consultaTO.setTabelaPrincipal("cliente cli");

        consultaTO.getJoins().add("INNER JOIN pessoa pes ON cli.pessoa = pes.codigo");
        consultaTO.getJoins().add("INNER JOIN periodoacessocliente pa ON pa.pessoa = cli.pessoa");
        consultaTO.getJoins().add("LEFT JOIN usuario us ON pa.responsavel = us.codigo");
        consultaTO.getJoins().add("LEFT JOIN categoria cat ON cat.codigo = cli.categoria");

        consultaTO.setPermiteWhere(true);

        consultaTO.setWhere("1 = 1 AND cli.empresa = 1\n" +
                "      AND pa.tipoacesso = 'PL'\n" +
                "      AND\n" +
                "      (\n" +
                "        pa.datainicioacesso BETWEEN '2014-10-01' AND '2014-11-30' OR\n" +
                "        pa.datafinalacesso BETWEEN '2014-10-01' AND '2014-11-30'\n" +
                "      )");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("AlunosMesmoCpf", "Alunos com o mesmo CPF", "Mostra todos os alunos que tem o mesmo CPF.");

        consultaTO.getColunas().add("c.matricula");
        consultaTO.getColunas().add("p.nome");
        consultaTO.getColunas().add("p.rg");
        consultaTO.getColunas().add("p.cfp  AS cpf");
        consultaTO.getColunas().add("p.datanasc");
        consultaTO.getColunas().add("e.nome AS empresa");

        consultaTO.setTabelaPrincipal("pessoa p");

        consultaTO.getJoins().add("INNER JOIN cliente c ON p.codigo = c.pessoa");
        consultaTO.getJoins().add("INNER JOIN empresa e ON e.codigo = c.empresa");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("WHERE p.codigo IN\n" +
                "      (SELECT codigo\n" +
                "       FROM pessoa\n" +
                "       WHERE cfp IN (SELECT cfp\n" +
                "                     FROM pessoa\n" +
                "                     WHERE codigo IN (SELECT pessoa\n" +
                "                                      FROM cliente)\n" +
                "                     GROUP BY cfp\n" +
                "                     HAVING count(*) > 1) AND cfp <> '')\n" +
                "ORDER BY p.cfp");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("AlunosComResponsaveis", "Alunos com Responsável", "Mostra todos os alunos que tem algum responsável cadastrado.");

        consultaTO.getColunas().add("c.matricula");
        consultaTO.getColunas().add("p.nome");
        consultaTO.getColunas().add("to_char(p.datanasc, 'DD/MM/YYYY') as datanascimento");
        consultaTO.getColunas().add("p.cfp as cpfaluno");
        consultaTO.getColunas().add("p.nomepai");
        consultaTO.getColunas().add("p.nomemae");
        consultaTO.getColunas().add("pr.nome as responsavel");
        consultaTO.getColunas().add("p.cpfmae as cpfmae,");
        consultaTO.getColunas().add("p.cpfpai as cpfpai,");
        consultaTO.getColunas().add("pr.cfp as cpfresponsavel");
        consultaTO.getColunas().add("e.nome as empresa");

        consultaTO.setTabelaPrincipal("cliente c");

        consultaTO.getJoins().add("inner join empresa e on e.codigo = c.empresa");
        consultaTO.getJoins().add("inner join pessoa p on c.pessoa = p.codigo and (p.nomepai <> '' OR p.nomemae <> '' OR c.pessoaresponsavel is not null)");
        consultaTO.getJoins().add("left join pessoa pr on pr.codigo = c.pessoaresponsavel");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("WHERE c.empresa = 1 \n" +
                "ORDER BY p.nome");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("AlunosMesmoNome", "Alunos com o mesmo NOME", "Mostra todos os alunos que tem nome em comum.");

        consultaTO.getColunas().add("c.matricula");
        consultaTO.getColunas().add("p.nome");
        consultaTO.getColunas().add("p.rg");
        consultaTO.getColunas().add("p.cfp AS cpf");
        consultaTO.getColunas().add("p.datanasc");
        consultaTO.getColunas().add("e.nome AS empresa");

        consultaTO.setTabelaPrincipal("pessoa p");

        consultaTO.getJoins().add("INNER JOIN cliente c ON p.codigo = c.pessoa");
        consultaTO.getJoins().add("INNER JOIN empresa e ON e.codigo = c.empresa");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("WHERE p.nome IN\n" +
                "      (SELECT nome\n" +
                "       FROM pessoa\n" +
                "       WHERE codigo IN (SELECT pessoa\n" +
                "                        FROM cliente)\n" +
                "       GROUP BY nome\n" +
                "       HAVING count(*) > 1)\n" +
                "ORDER BY p.nome");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("ClientesPorIdade", "Quantidade de clientes por idade", "Quantidade de clientes por idade");

        consultaTO.getColunas().add("extract(YEAR FROM age(p.datanasc)) AS Idade");
        consultaTO.getColunas().add("count(*) AS Quantidade");

        consultaTO.setTabelaPrincipal("pessoa p");

        consultaTO.getJoins().add("INNER JOIN cliente c ON p.codigo = c.pessoa");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("c.empresa = 1 \n" +
                "GROUP BY 1\n" +
                "ORDER BY 1");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("ClientesMXM", "Relatório MXM", "Relatório de clientes no formato MXM");

        consultaTO.getColunas().add(" substring(trim(scsdw.nomecliente) FROM '([^ ]+)$')                                                AS \"Sobrenome\"");
        consultaTO.getColunas().add("  split_part(scsdw.nomecliente, ' ', 1)                                                            AS \"Nome\"");
        consultaTO.getColunas().add("  scsdw.idade                                                                                      AS \"Idade\"");
        consultaTO.getColunas().add("  scsdw.sexocliente                                                                                AS \"Sexo\"");
        consultaTO.getColunas().add("  scsdw.codigocliente                                                                              AS \"Codigo Cliente\"");
        consultaTO.getColunas().add("  scsdw.telefonescliente                                                                           AS \"Telefone\"");
        consultaTO.getColunas().add("  array_to_string(array(SELECT email FROM email WHERE email.pessoa = scsdw.codigopessoa), ',', '') AS \"Email\"");
        consultaTO.getColunas().add("  scsdw.situacao                                                                                   AS \"Status Cliente\"");
        consultaTO.getColunas().add("  'EF1'                                                                                            AS \"Frequent Club Id\"");
        consultaTO.getColunas().add("  'EF1'                                                                                            AS \"Home Club Id\"");
        consultaTO.getColunas().add("  to_char(scsdw.datamatricula, 'DD/MM/YYYY')                                                       AS \"Data Matrícula\"");
        consultaTO.getColunas().add("  to_char(scsdw.dataultimoacesso, 'DD/MM/YYYY')                                                    AS \"Data Última visita\"");

        consultaTO.setTabelaPrincipal("situacaoclientesinteticodw scsdw");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("situacao = 'AT' AND scsdw.empresacliente = 3");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("alunosEvento", "Relatório de Alunos Por Evento", "Nessa consulta traz a relação de Alunos Por Evento");

        consultaTO.getColunas().add("nome                                                                                              AS \"Nome\"");
        consultaTO.getColunas().add("cli.matricula                                                                                     AS \"Matricula\"");
        consultaTO.getColunas().add("cli.situacao                                                                                      AS \"Situação\"");
        consultaTO.getColunas().add("evento                                                                                            AS \"Codigo Evento\"");
        consultaTO.getColunas().add("e.descricao                                                                                       AS \"Descrição do Evento\"");
        consultaTO.getColunas().add("qc.data                                                                                           AS \"Data BV\"");
        consultaTO.getColunas().add("co.datalancamento                                                                                 AS \"Data Contrato\"");
        consultaTO.getColunas().add("sw.telefonescliente                                                                               AS \"Telefone\"");

        consultaTO.setTabelaPrincipal("pessoa p");

        consultaTO.getJoins().add("INNER JOIN cliente cli ON cli.pessoa = p.codigo");
        consultaTO.getJoins().add("left JOIN situacaoclientesinteticodw sw ON sw.codigocliente = cli.codigo");
        consultaTO.getJoins().add("INNER JOIN questionariocliente qc ON cli.codigo = qc.cliente");
        consultaTO.getJoins().add("INNER JOIN evento e ON e.codigo = qc.evento");
        consultaTO.getJoins().add("left JOIN contrato co ON co.pessoa = p.codigo");


        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("WHERE 1 = 1\n" +
                "            AND qc.evento IS NOT NULL\n" +
                "            AND p.datacadastro BETWEEN '2017-05-01' AND '2017-05-30'\n" +
                "            AND sw.empresacliente = 1\n "+
                "            ORDER BY nome");

        consultas.add(consultaTO);



        consultaTO = new ConsultaTO("alunosAtivosTransferidos", "Relatório de Alunos Ativos Transferidos de Unidade", "Nessa consulta traz a relação de Alunos que estão ativos e foram Transferidos de unidade.");

        consultaTO.getColunas().add("p.nome                                                                                              AS \"Nome\"");
        consultaTO.getColunas().add("cli.matricula                                                                                     AS \"Matricula\"");
        consultaTO.getColunas().add("cli.situacao                                                                                      AS \"Situação\"");
        consultaTO.getColunas().add("e.nome                                                                                            AS \"Empresa Cliente\"");
        consultaTO.getColunas().add("c.codigo                                                                                          AS \"Código do Contrato\"");
        consultaTO.getColunas().add("ec.nome                                                                                           AS \"Empresa Contrato\"");

        consultaTO.setTabelaPrincipal("cliente cli");

        consultaTO.getJoins().add("inner join pessoa p on p.codigo=cli.pessoa");
        consultaTO.getJoins().add("inner join contrato c on c.pessoa=p.codigo");
        consultaTO.getJoins().add("inner join empresa e on e.codigo=cli.empresa");
        consultaTO.getJoins().add("inner join empresa ec on ec.codigo=c.empresa");


        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("WHERE 1 = 1\n" +
                "            AND cli.empresa <> c.empresa and c.situacao='AT'\n "+
                "            ORDER BY p.nome");

        consultas.add(consultaTO);


        consultaTO = new ConsultaTO("alunosSemCompensacaoFutura", "Relatório de Alunos Ativos que já compensaram todos os pagamentos", "O resultado desta consulta é a relação de quais Alunos que estão ativos e não tem mais pagamentos para serem compensados.");

        consultaTO.getColunas().add("DISTINCT cli.matricula                                                                                     AS \"Matricula\"");
        consultaTO.getColunas().add("pes.nome                                                                                          AS \"Nome\"");
        consultaTO.getColunas().add("cli.situacao                                                                                      AS \"Situação\"");
        consultaTO.getColunas().add("pl.descricao                                                                                      AS \"plano\"");
        consultaTO.getColunas().add("cd.numeromeses                                                                                    AS \"duracao\"");
        consultaTO.getColunas().add("c.vigenciade                                                                                      AS \"dataInicioContrato\"");
        consultaTO.getColunas().add("c.vigenciaateajustada                                                                             AS \"dataFinalContrato\"");
        consultaTO.getColunas().add("c.valorfinal                                                                                      AS \"valorTotal\"");

        consultaTO.setTabelaPrincipal("cliente cli");

        consultaTO.getJoins().add("inner join pessoa pes on pes.codigo=cli.pessoa");
        consultaTO.getJoins().add("inner join situacaoclientesinteticodw st on st.codigopessoa= pes.codigo");
        consultaTO.getJoins().add("inner join contrato c on c.codigo=st.codigocontrato");
        consultaTO.getJoins().add("inner join plano pl on pl.codigo = c.plano");
        consultaTO.getJoins().add("inner join contratoduracao cd on cd.contrato = c.codigo");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("WHERE 1 = 1\n" +
                "            AND cli.situacao = 'AT'\n" +
                "            AND (select count(codigo) from movparcela mpar WHERE mpar.pessoa = pes.codigo AND mpar.situacao = 'EA') = 0\n" +
                "            AND (select count(ch.codigo) from cheque ch INNER JOIN movpagamento mpag ON ch.movpagamento = mpag.codigo WHERE mpag.pessoa = pes.codigo AND datacompesancao > now()) = 0\n" +
                "            AND (select count(cc.codigo) from cartaocredito cc INNER JOIN movpagamento mpag ON cc.movpagamento = mpag.codigo WHERE mpag.pessoa = pes.codigo AND  datacompesancao > now()) = 0\n" +
                "            AND (select count(mpag.codigo) from movpagamento mpag WHERE mpag.pessoa = pes.codigo AND dataquitacao > now()) = 0\n"+
                "            AND c.bolsa IS FALSE\n"+
                "            ORDER BY pes.nome");

        consultas.add(consultaTO);



        // Dados dos alunos por modalidade e valor
        consultaTO = new ConsultaTO("AlunosModalidade",
                "Dados básicos de Alunos com suas modalidades",
                "Todos os alunos ativos que tem tem contrato com modalidade, seus valores e endereço para emissão de nota");
        consultaTO.getColunas().add("pes.nome");
        consultaTO.getColunas().add("pes.cfp as cpf");
        consultaTO.getColunas().add("mpm.valor as valormodalidade");
        consultaTO.getColunas().add("mod.nome as modalidade");
        consultaTO.getColunas().add("ende.cep");
        consultaTO.getColunas().add("ende.bairro");
        consultaTO.getColunas().add("ende.numero");
        consultaTO.getColunas().add("ende.complemento");
        consultaTO.getColunas().add("ende.endereco as endereco");
        consultaTO.getColunas().add("emp.nome as empresa");

        consultaTO.setTabelaPrincipal("contrato c");

        consultaTO.getJoins().add("left join pessoa pes ON c.pessoa = pes.codigo ");
        consultaTO.getJoins().add("left join endereco ende ON pes.codigo = ende.pessoa ");
        consultaTO.getJoins().add("left join movproduto mprod ON mprod.contrato = c.codigo and mprod.mesreferencia = to_char(now(), 'MM/yyyy') ");
        consultaTO.getJoins().add("left join movprodutomodalidade mpm ON mpm.movproduto = mprod.codigo ");
        consultaTO.getJoins().add("left join modalidade mod ON mpm.modalidade = mod.codigo ");
        consultaTO.getJoins().add("left join empresa emp ON c.empresa = emp.codigo ");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("now() between vigenciade and vigenciaateajustada\n" +
                "and c.situacao not in ('CA')\n" +
                "and mpm.codigo is not null\n" +
                "and emp.codigo = 1\n" +
                "order by emp.nome, pes.nome ");

        consultas.add(consultaTO);

        // Dados dos alunos por modalidade e valor
        consultaTO = new ConsultaTO("ClienteMaillingEnviado",
                "Dados básicos do mailling que o cliente já participou",
                "Devolve os mailings onde já foi envido email, ");
        consultaTO.getColunas().add("md.codigo");
        consultaTO.getColunas().add("md.datacriacao");
        consultaTO.getColunas().add("md.dataenvio");
        consultaTO.getColunas().add("md.titulo");
        consultaTO.getColunas().add("md.vigenteate");

        consultaTO.setTabelaPrincipal("email e");

        consultaTO.getJoins().add("inner join pessoa p on e.pessoa = p.codigo ");
        consultaTO.getJoins().add("inner join cliente c on p.codigo = c.pessoa ");
        consultaTO.getJoins().add("inner join (select codigo, cliente::integer from (\n" +
                "\t\t\tselect md.codigo, unnest(string_to_array(mh.clientesenviados, ',')) cliente\n" +
                "\t\t\tfrom maladireta md inner join mailinghistorico mh on md.codigo = mh.maladireta where not mh.clientesenviados is null\n" +
                "\t\t) a group by codigo, cliente \n" +
                "\t     ) m on c.codigo = m.cliente ");

        consultaTO.getJoins().add("inner join maladireta md on m.codigo = md.codigo ");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("e.email = '<EMAIL>'");


        consultaTO = new ConsultaTO("ClientesAtivosComFormaPagamento",
                "Clientes Ativos com a Forma de Pagamento",
                "Devolve os clientes com faturamento, forma de pagamento e data de faturamento");
        consultaTO.getColunas().add("distinct ST.MATRICULA as \"MATRÍCULA\"");
        consultaTO.getColunas().add("ST.NOMECLIENTE as \"NOME CLIENTE\"");
        consultaTO.getColunas().add("ST.situacao as \"SITUACAO\"");
        consultaTO.getColunas().add("(case when C.bolsa is true then 'SIM' else 'NÃO' end) as \"Bolsa\"");
        consultaTO.getColunas().add("CD.numeromeses as \"DURAÇÃO(MESES)\"");
        consultaTO.getColunas().add("C.CODIGO as \"CONTRATO\"");
        consultaTO.getColunas().add("C.VALORFINAL as \"VALOR CONTRATO\"");
        consultaTO.getColunas().add("MP.DESCRICAO as \"DESCRIÇÃO\"");
        consultaTO.getColunas().add("RP.CODIGO as \"RECIBO\"");
        consultaTO.getColunas().add("RP.DATA as \"DT. RECIBO\"");
        consultaTO.getColunas().add("PM.VALORPAGO as \"VALOR\"");
        consultaTO.getColunas().add("FP.DESCRICAO as \"FORMA PAGAMENTO\"");
        consultaTO.getColunas().add("mp.situacao as \"SITUAÇÃO PARCELA\"");


        consultaTO.setTabelaPrincipal("SITUACAOCLIENTESINTETICODW ST");

        consultaTO.getJoins().add("left join CONTRATO C on\n" +
                "\tC.CODIGO = ST.CODIGOCONTRATO\n" +
                "left join MOVPARCELA MP on\n" +
                "\tMP.CONTRATO = C.CODIGO\n" +
                "left join contratoduracao CD on\n" +
                "\tCD.contrato = ST.CODIGOCONTRATO\n" +
                "left join PAGAMENTOMOVPARCELA PM on\n" +
                "\tPM.MOVPARCELA = MP.CODIGO\n" +
                "left join MOVPAGAMENTO MOV on\n" +
                "\tMOV.CODIGO = PM.MOVPAGAMENTO\n" +
                "left join FORMAPAGAMENTO FP on\n" +
                "\tFP.CODIGO = MOV.FORMAPAGAMENTO\n" +
                "left join RECIBOPAGAMENTO RP on\n" +
                "\tRP.CODIGO = PM.RECIBOPAGAMENTO");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("\tST.SITUACAO = 'AT'\n" +
                "order by\n" +
                "\t2\n");

        consultas.add(consultaTO);

        consultaTO = new ConsultaTO("ClientesContratosAtivosNivelTurma",
                "Clientes com Contratos Ativos com Turmas",
                "Devolve os clientes com contrato ativo que encontra-se em turma, trazendo o nivel da turma que o contrato possui");
        consultaTO.getColunas().add("distinct p.nome, c.matricula, e.email, t.numero, h.diasemana, h.horainicial , h.horafinal ,n.descricao");

        consultaTO.setTabelaPrincipal("cliente c");

        consultaTO.getJoins().add("inner join pessoa p on p.codigo = c.pessoa \n" +
                "left join email e on e.pessoa = p.codigo \n" +
                "left join telefone t on t.pessoa = p.codigo \n" +
                "left join contrato con on p.codigo = con.pessoa \n" +
                "left join matriculaalunohorarioturma tu on tu.contrato = con.codigo \n" +
                "left join horarioturma h on tu.horarioturma = h.codigo \n" +
                "left join nivelturma n on h.nivelturma = n.codigo ");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("con.situacao = 'AT' \n" +
                "and n.descricao <> '' \n" +
                "and n.descricao is not null\n" +
                "order by p.nome");

        consultas.add(consultaTO);


        // Clientes com autorização de cobrança
        consultaTO = new ConsultaTO("AlunosAutorizacaoCobranca",
                "Alunos com informações de cobrança (Autorização de Cobrança)",
                "Todos os alunos com as informações de cobrança (Alunos com e sem autorização de cobrança)");

        consultaTO.getColunas().add("cl.matricula as \"Matrícula\"");
        consultaTO.getColunas().add("p.nome as \"Nome\"");
        consultaTO.getColunas().add("CASE \n" +
                "WHEN cl.situacao = 'IN' THEN 'INATIVO'\n" +
                "WHEN cl.situacao = 'AT' THEN 'ATIVO'\n" +
                "WHEN cl.situacao = 'VI' THEN 'VISITANTE'\n" +
                "ELSE cl.situacao\n" +
                "END as \"Situação Cliente\"");

        consultaTO.getColunas().add("cc.descricao as \"Convênio de Cobrança\"");
        consultaTO.getColunas().add("CASE \n" +
                "WHEN au.tipoautorizacao = 1 THEN 'CARTÃO DE CRÉDITO'\n" +
                "WHEN au.tipoautorizacao = 2 THEN 'DÉBITO EM CONTA'\n" +
                "WHEN au.tipoautorizacao = 3 THEN 'BOLETO BANCÁRIO'\n" +
                "ELSE au.tipoautorizacao::text\n" +
                "END as \"Tipo Autorização\"");
        consultaTO.getColunas().add("e.nome as \"Empresa\"");

        consultaTO.setTabelaPrincipal("cliente cl");

        consultaTO.getJoins().add("INNER JOIN pessoa p on p.codigo = cl.pessoa ");
        consultaTO.getJoins().add("INNER JOIN empresa e on e.codigo = cl.empresa ");
        consultaTO.getJoins().add("LEFT JOIN autorizacaocobrancacliente au on cl.codigo = au.cliente and au.ativa ");
        consultaTO.getJoins().add("LEFT JOIN conveniocobranca cc on cc.codigo = au.conveniocobranca ");

        consultaTO.setPermiteWhere(true);
        consultaTO.setWhere("1 = 1 \n" +
                "--CASO DESEJE TODOS OS ALUNOS COM E SEM AUTORIZAÇÃO - REMOVER A LINHA ABAIXO \n" +
                "AND au.codigo is not null \n" +
                "--CASO DESEJE COM TODAS AS SITUAÇÕES - REMOVER A LINHA ABAIXO \n" +
                "AND cl.situacao = 'AT' \n" +
                (UteisValidacao.emptyNumber(empresaLogada) ? "" : "--CASO DESEJE TODAS AS EMPRESAS - REMOVER A LINHA ABAIXO \n AND cl.empresa = " + empresaLogada + " \n") +
                "order by e.nome,p.nome ");

        consultas.add(consultaTO);


        return consultas;
    }

    private List<ConsultaTO> consultasNotaFiscalServicoEletronica() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("ContratosSemNFSe", "Contratos lançados no período e que não tem envio de NFSe",
                "SQL que trará uma relação de alunos e contratos que não tiveram notas emitidas");
        consulta.getColunas().add("cli.matricula");
        consulta.getColunas().add("p.nome");
        consulta.getColunas().add("c.codigo AS contrato");
        consulta.getColunas().add("c.datalancamento");
        consulta.getColunas().add("c.valorfinal");

        consulta.setTabelaPrincipal("contrato c");

        consulta.getJoins().add("LEFT JOIN nfseemitida nfse ON c.codigo = nfse.contrato");
        consulta.getJoins().add("LEFT JOIN pessoa p ON c.pessoa = p.codigo");
        consulta.getJoins().add("LEFT JOIN cliente cli ON cli.pessoa = p.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("1 = 1\n" +
                "      AND c.situacao = 'AT'\n" +
                "      AND nfse.codigo IS NULL\n" +
                "      AND c.bolsa IS FALSE\n" +
                "      AND c.datalancamento BETWEEN '01/05/2014 00:00:00' AND '31/05/2014 00:00:00'");

        consultas.add(consulta);

        // todos os alunos ativos que tem nota emitida para o contrato vigente
        ConsultaTO consultaAtivos = new ConsultaTO("AlunosNotaEmitida",
                "Alunos ativos com nota emitida",
                "Todos os alunos ativos que tem nota emitida para o contrato vigente");
        consultaAtivos.getColunas().add("nomecliente");
        consultaAtivos.getColunas().add("matricula");
        consultaAtivos.getColunas().add("cpf");
        consultaAtivos.getColunas().add("c.codigo as contrato");
        consultaAtivos.getColunas().add("nf.rps as RpsUltimaNotaEmitida");
        consultaAtivos.getColunas().add("e.nome as empresa");

        consultaAtivos.setTabelaPrincipal("situacaoclientesinteticodw sw");

        consultaAtivos.getJoins().add("inner join contrato c on c.pessoa = sw.codigopessoa ");
        consultaAtivos.getJoins().add("inner join nfseemitida nf on nf.contrato = c.codigo ");
        consultaAtivos.getJoins().add("inner join empresa e on e.codigo = sw.empresacliente ");
        consultaAtivos.getJoins().add("left join nfseemitida nf2 on nf2.contrato = c.codigo and nf2.codigo > nf.codigo ");

        consultaAtivos.setPermiteWhere(true);
        consultaAtivos.setWhere("sw.situacao = 'AT' and c.situacao = 'AT' and nf2.codigo is null ORDER BY nomecliente ");

        consultas.add(consultaAtivos);

        return consultas;
    }

    private List<ConsultaTO> consultasRecibo() {
        //Sempre colocar a empresa na consulta;
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("RecibosComESemCupomFiscal", "Recibos com e sem cupom fiscal", "SQL que irá consultar todos os recibos");
        consulta.getColunas().add("r.codigo as cod_recibo");
        consulta.getColunas().add("r.nomepessoapagador");
        consulta.getColunas().add("r.valortotal as valor_recibo");
        consulta.getColunas().add("r.data as data_recibo");
        consulta.getColunas().add("r.pessoapagador");
        consulta.getColunas().add("c.co_cupom");
        consulta.getColunas().add("c.datapagamento");
        consulta.getColunas().add("c.horaemissao");
        consulta.getColunas().add("c.horavenda");
        consulta.getColunas().add("c.statusimpressao");
        consulta.getColunas().add("c.valor as valorcupon");
        consulta.getColunas().add("e.nome");

        consulta.setTabelaPrincipal("recibopagamento r");

        consulta.getJoins().add("LEFT JOIN cupomfiscal c ON r.codigo = c.recibo");
        consulta.getJoins().add("LEFT JOIN empresa e ON r.empresa = e.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("r.data BETWEEN '2013-12-25 00:00:00' AND '2014-03-25 23:59:59'");

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultasRemessa() {
        //Sempre colocar a empresa na consulta;
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("Remessas", "Remessas e suas respectivas parcelas", "Mostra as remessas cadastradas");
        consulta.getColunas().add("re.tipo");
        consulta.getColunas().add("re.codigo cod_remessa");
        consulta.getColunas().add("re.dataregistro as Data_Remessa");
        consulta.getColunas().add("date_part('year', re.dataregistro) as Ano_Remessa");
        consulta.getColunas().add("date_part('month', re.dataregistro) as Mes_Remessa");
        consulta.getColunas().add("date_part('day', re.dataregistro) as Dia_Remessa");
        consulta.getColunas().add("date_part('hour', re.dataregistro) as Hora_Remessa");
        consulta.getColunas().add("split_part(split_part(rei.props, 'CartaoMascarado=', 2), ',', 1) AS Num_Cartao");
        consulta.getColunas().add("split_part(split_part(rei.props, 'CodigoAutorizacao=', 2), ',', 1) AS CodigoAutorizacao");
        consulta.getColunas().add("split_part(split_part(rei.props, 'Bandeira=', 2), ',', 1) AS Bandeira");
        consulta.getColunas().add("split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) AS StatusRetornado");
        consulta.getColunas().add("rei.pessoa");
        consulta.getColunas().add("mpa.nrtentativas");
        consulta.getColunas().add("mpa.codigo as cod_movparcela");
        consulta.getColunas().add("mpa.valorparcela");
        consulta.getColunas().add("mpa.situacao");
        consulta.getColunas().add("CASE WHEN mpa.situacao = 'PG' THEN 'PAGO' WHEN mpa.situacao = 'CA' THEN 'CANCELADA' WHEN mpa.situacao = 'EA' THEN 'EM ABERTO' ELSE '????' END as Situacao_Atual_Parcela");
        consulta.getColunas().add("pes.nome as Nome_Aluno");
        consulta.getColunas().add("CASE WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '00' THEN '00-Autorizado' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '14' THEN '14-Numero do Cartao Invalido' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '42' THEN '42-Cartao em Boletin' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '74' THEN '74-Autorizaçao negada' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '81' THEN '81-Fundos Insuficiente' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '82' THEN '82-Cartão vencido ou data de validade errada' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '87' THEN '87-Cartao Restrito' WHEN split_part(split_part(rei.props, 'StatusVenda=', 2), ',', 1) = '99' THEN '99-Erro LauOut - Outros' ELSE '???? NAO SEI' END as StatusRetonado_Descricao");

        consulta.setTabelaPrincipal("remessa re");

        consulta.getJoins().add("left outer join remessaitem rei on re.codigo = rei.remessa");
        consulta.getJoins().add("left outer join movparcela  mpa on rei.movparcela = mpa.codigo");
        consulta.getJoins().add("left outer join pessoa pes on rei.pessoa = pes.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("re.dataregistro BETWEEN '2014-01-01 00:00:00' AND '2014-01-31 23:59:59'");

        consultas.add(consulta);


        consulta = new ConsultaTO("SaldoDCC", "Saldo DCC", "Mostra o saldo de cada empresa");
        consulta.getColunas().add("emp.nome");
        consulta.getColunas().add("emp.creditodcc");

        consulta.setTabelaPrincipal("empresa emp");

        consulta.setPermiteSelectAll(true);

        consultas.add(consulta);


        consulta = new ConsultaTO("ExtratoDCC", "Extrato DCC", "Trás o Extrato dos créditos de DCC por empresa");
        consulta.getColunas().add("log.data :: TEXT");
        consulta.getColunas().add("log.quantidade");
        consulta.getColunas().add("log.saldo");

        consulta.setTabelaPrincipal("logdcc log");
        consulta.getJoins().add("INNER JOIN empresa emp ON log.empresa = emp.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("emp.nome ILIKE 'life%' ORDER BY log.data");

        consultas.add(consulta);


        consulta = new ConsultaTO("ClientesSemUsarAutorizacaoCobranca", "Clientes com Autorização de Cobrança sem utilizar", "Trás os clientes que tem autorização de cobrança e não estão em remssa \"Sem Utilizar\" a X dias de acordo com o consulta realizada.");

        consulta.getColunas().add("matricula");
        consulta.getColunas().add("nomecliente");
        consulta.getColunas().add("nomeplano");
        consulta.getColunas().add("situacao");
        consulta.getColunas().add("codigocontrato as contrato");
        consulta.getColunas().add("to_char(datavigenciade, 'DD/MM/YYYY') as dataInicioContrato");
        consulta.getColunas().add("to_char(datavigenciaateajustada, 'DD/MM/YYYY') as dataFinalContrato");
        consulta.getColunas().add("duracaocontratomeses");
        consulta.getColunas().add("telefonescliente");
        consulta.getColunas().add("to_char(p.datacadastro, 'DD/MM/YYYY') as datacadastro");
        consulta.getColunas().add("e.nome as empresa");

        consulta.setTabelaPrincipal("situacaoclientesinteticodw s");
        consulta.getJoins().add("inner join pessoa p on p.codigo = s.codigopessoa");
        consulta.getJoins().add("inner join empresa e on e.codigo = s.empresacliente");

        consulta.setPermiteWhere(true);
        consulta.setWhere("and codigocliente in (select cliente from autorizacaocobrancacliente)\n" +
                "and codigopessoa in (select pessoa from movparcela where situacao = 'EA')\n" +
                "and codigopessoa not in (select distinct(pessoa) from remessaitem where remessa in (select codigo from remessa where dataregistro::date >= '01/04/2017'))\n" +
                "and p.datacadastro <= '01/04/2017'\n" +
                "and s.empresacliente = 1\n" +
                "order by 2");

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultasAcesso() {
        //Sempre colocar a empresa na consulta;
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("IntegracaoDeAcesso", "Acessos de alunos nas academias da rede", "SQL que irá consultar os alunos que acessaram a academia pela Integração de Acesso");
        consulta.getColunas().add("c.matricula");
        consulta.getColunas().add("p.nome as cliente");
        consulta.getColunas().add("date_part('year', ac.dthrentrada) as Ano_Entrada");
        consulta.getColunas().add("date_part('month', ac.dthrentrada) as Mes_Entrada");
        consulta.getColunas().add("date_part('day', ac.dthrentrada) as Dia_Entrada");
        consulta.getColunas().add("date_part('hour', ac.dthrentrada) as Hora_Entrada");
        consulta.getColunas().add("la.descricao as localacesso");
        consulta.getColunas().add("e.nome         AS empresa");

        consulta.setTabelaPrincipal("acessocliente ac");

        consulta.getJoins().add("INNER JOIN localacesso la ON ac.localacesso = la.codigo");
        consulta.getJoins().add("INNER JOIN cliente c ON ac.cliente = c.codigo");
        consulta.getJoins().add("INNER JOIN pessoa p ON p.codigo = c.pessoa");
        consulta.getJoins().add("INNER JOIN empresa e ON c.empresa = e.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("la.descricao LIKE '%INTEGR%'");

        consulta.setPermiteSelectAll(false);

        consultas.add(consulta);

        consulta = new ConsultaTO("TodosAcessos", "Todos Acessos", "SQL que irá consultar trazer todos os acesso da academia");
        consulta.getColunas().add("ac.codigo      AS codacesso");
        consulta.getColunas().add("cli.matricula  AS matriculacliente");
        consulta.getColunas().add("p.nome         AS cliente");
        consulta.getColunas().add("date_part('year', ac.dthrentrada) as Ano_Entrada");
        consulta.getColunas().add("date_part('month', ac.dthrentrada) as Mes_Entrada");
        consulta.getColunas().add("date_part('day', ac.dthrentrada) as Dia_Entrada");
        consulta.getColunas().add("date_part('hour', ac.dthrentrada) as Hora_Entrada");
        consulta.getColunas().add("ac.situacao    AS situacaoacesso");
        consulta.getColunas().add("la.descricao   AS localacesso");

        consulta.setTabelaPrincipal("acessocliente ac");

        consulta.getJoins().add("LEFT JOIN cliente cli ON ac.cliente = cli.codigo");
        consulta.getJoins().add("LEFT JOIN pessoa p ON cli.pessoa = p.codigo");
        consulta.getJoins().add("LEFT JOIN localacesso la ON ac.localacesso = la.codigo");

        consulta.setPermiteWhere(true);

        consulta.setWhere(" 1 = 1 limit 1000");

        consulta.setPermiteSelectAll(false);

        consultas.add(consulta);


        consulta = new ConsultaTO("AcessoLiberadosCliente", "Acessos de clientes com liberação forçada", "Consulta para verificar os acessos que tiveram a liberação forçada em determinado período");

        consulta.getColunas().add("cli.matricula");
        consulta.getColunas().add("pes.nome");
        consulta.getColunas().add("loc.descricao AS localacesso");
        consulta.getColunas().add("col.descricao AS coletor");
        consulta.getColunas().add("ace.dthrentrada::text");
        consulta.getColunas().add("ace.dthrsaida::text");
        consulta.getColunas().add("usu.nome AS usuario");

        consulta.setTabelaPrincipal("acessocliente ace");

        consulta.getJoins().add("INNER JOIN cliente cli ON ace.cliente = cli.codigo");
        consulta.getJoins().add("INNER JOIN pessoa pes ON cli.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN usuario usu ON ace.usuario = usu.codigo");
        consulta.getJoins().add("INNER JOIN localacesso loc ON ace.localacesso = loc.codigo");
        consulta.getJoins().add("INNER JOIN coletor col ON ace.coletor = col.codigo");
        consulta.getJoins().add("INNER JOIN empresa emp ON loc.empresa = emp.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("emp.codigo = 1\n" +
                "      AND\n" +
                "      ace.dthrentrada BETWEEN '2015-02-01' AND '2015-02-28'\n" +
                "ORDER BY ace.dthrentrada DESC");

        consultas.add(consulta);

        //Alunos com senha de acesso cadastrada
        consulta = new ConsultaTO("AlunosComSenhaAcessoCadastrada", "Alunos com senha de acesso cadastrada", "SQL que irá consultar todos os alunos de determinada unidade que tem senha de acesso cadastrada");

        consulta.getColunas().add("c.matricula");
        consulta.getColunas().add("p.nome");
        consulta.getColunas().add("e.nome as empresa");

        consulta.setTabelaPrincipal("pessoa p");

        consulta.getJoins().add("inner join cliente c on c.pessoa = p.codigo");
        consulta.getJoins().add("inner join empresa e on e.codigo = c.empresa");

        consulta.setPermiteWhere(true);

        consulta.setWhere("p.senhaacesso is not null \n" +
                "and e.codigo = 1\n" +
                "order by p.nome");

        consulta.setPermiteSelectAll(false);

        consultas.add(consulta);

        //Alunos com mais de uma digital cadastrada
        consulta = new ConsultaTO("AlunosComMaisDeUmaDigitalCadastrada", "Alunos com mais de uma digital cadastrada", "SQL que irá consultar todos os alunos de determinada unidade que tem mais de uma digital cadastrada");

        consulta.getColunas().add("c.matricula");
        consulta.getColunas().add("p.nome");
        consulta.getColunas().add("sc.colaboradores as colaboradores");
        consulta.getColunas().add("sc.datavigenciaateajustada as terminocontrato");
        consulta.getColunas().add("sc.telefonescliente as telefones");
        consulta.getColunas().add("e.nome as empresa");

        consulta.setTabelaPrincipal("pessoa p");

        consulta.getJoins().add("INNER JOIN cliente c ON c.pessoa = p.codigo");
        consulta.getJoins().add("INNER JOIN situacaoclientesinteticodw sc ON c.codigo = sc.codigocliente");
        consulta.getJoins().add("INNER JOIN empresa e ON e.codigo = c.empresa");

        consulta.setPermiteWhere(true);

        consulta.setWhere("c.temmaisdeumadigital IS TRUE\n" +
                "AND e.codigo = 1\n" +
                "ORDER BY p.nome");

        consulta.setPermiteSelectAll(false);

        consultas.add(consulta);


        // Totalizador de Acessos por cliente (Semanal, Mensal, Bimestral e Trimestral)
        consulta = new ConsultaTO("TotalizadordeAcessosPorCliente", "Totalizador de Acessos por cliente (Semanal, Mensal, Bimestral e Trimestral)", "Totalizador de Acessos por cliente (Semanal, Mensal, Bimestral e Trimestral)");

        consulta.getColunas().add("c.matricula");
        consulta.getColunas().add("c.situacao");
        consulta.getColunas().add("p.nome");
        consulta.getColunas().add("(select nome from pessoa where codigo in (select pessoa from colaborador where codigo in (select colaborador from vinculo  where tipovinculo = 'CO' and cliente = c.codigo))) as consultor");
        consulta.getColunas().add("(select\n" +
                "array_to_string(\n" +
                "array(\n" +
                "select \n" +
                "vin.tipovinculo || ' - ' ||pes.nome \n" +
                "from pessoa pes \n" +
                "inner join colaborador col on col.pessoa = pes.codigo\n" +
                "inner join vinculo vin on vin.colaborador = col.codigo\n" +
                "where vin.tipovinculo in ('PI','PE','TW','PT','PR') \n" +
                "and vin.cliente = c.codigo\n" +
                "order by pes.nome)\n" +
                " , ' | ')) as Professores");
        consulta.getColunas().add("s.codigocontrato as Contrato");
        consulta.getColunas().add("cd.numeromeses as Duracao");
        consulta.getColunas().add("(select count(*) from acessocliente  where cliente  = c.codigo and dthrentrada::date >= (CURRENT_DATE - integer '7')) as ultimos7Dias");
        consulta.getColunas().add("(select count(*) from acessocliente  where cliente  = c.codigo and dthrentrada::date >= (CURRENT_DATE - integer '30')) as ultimos30Dias");
        consulta.getColunas().add("(select count(*) from acessocliente  where cliente  = c.codigo and dthrentrada::date >= (CURRENT_DATE - integer '60')) as ultimos60Dias");
        consulta.getColunas().add("(select count(*) from acessocliente  where cliente  = c.codigo and dthrentrada::date >= (CURRENT_DATE - integer '90')) as ultimos90Dias");

        consulta.setTabelaPrincipal("cliente c");

        consulta.getJoins().add("inner join pessoa p on p.codigo = c.pessoa");
        consulta.getJoins().add("inner join situacaoclientesinteticodw s on s.codigocliente = c.codigo");
        consulta.getJoins().add("left join contrato co on co.codigo = s.codigocontrato");
        consulta.getJoins().add("left join contratoduracao cd on co.codigo = cd.contrato");

        consulta.setPermiteWhere(true);

        consulta.setWhere("WHERE c.empresa = 1 \n" +
                "ORDER BY p.nome");

        consulta.setPermiteSelectAll(false);

        consultas.add(consulta);


        consulta = new ConsultaTO("MediaAcessoAluno", "Média de Acesso por Aluno", "Média de Acesso por Aluno");

        consulta.getColunas().add("s.matricula as \"Matrícula\"");
        consulta.getColunas().add("CASE \n" +
                "WHEN s.situacao = 'IN' THEN 'INATIVO'\n" +
                "WHEN s.situacao = 'AT' THEN 'ATIVO'\n" +
                "WHEN s.situacao = 'VI' THEN 'VISITANTE'\n" +
                "ELSE s.situacao\n" +
                "END as \"Situação Cliente\"");
        consulta.getColunas().add("s.nomecliente as \"Nome\"");
        consulta.getColunas().add("s.codigocontrato as \"Contrato\"");
        consulta.getColunas().add("(s.diasacessosemanapassada + s.diasacessosemana2 + s.diasacessosemana3 + s.diasacessosemana4)/4 as \"Média últimas 4 semanas\"");
        consulta.getColunas().add("(s.diasacessoultimomes + s.diasacessomes2 + s.diasacessomes3 + s.diasacessomes4)/4 as \"Média últimos 4 meses\"");

        consulta.setTabelaPrincipal("situacaoclientesinteticodw s");

        consulta.setPermiteWhere(true);

        consulta.setWhere("1 = 1  \n" +
                "--CASO DESEJE TODAS AS SITUAÇÕES BASTA REMOVER A LINHA ABAIXO\n" +
                "AND s.situacao = 'AT' \n" +
                "ORDER BY s.nomecliente");

        consulta.setPermiteSelectAll(false);

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultasRecorrencia() {
        //Sempre colocar a empresa na consulta;
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("Anuidade", "Contratos com plano de recorrência com e sem anuidade", "Retorna informações dos contratos, que são de planos do tipo recorrência, e valores cobrados pela anuidade");
        consulta.getColunas().add("cli.matricula                         AS matricula");
        consulta.getColunas().add("pes.nome                              AS nome");
        consulta.getColunas().add("con.codigo                            AS contrato");
        consulta.getColunas().add("con.datalancamento");
        consulta.getColunas().add("con.vigenciade");
        consulta.getColunas().add("con.vigenciaateajustada");
        consulta.getColunas().add("pl.descricao                          AS plano");
        consulta.getColunas().add("emp.nome                              AS empresa");
        consulta.getColunas().add("coalesce((SELECT sum(totalfinal) FROM movproduto mp INNER JOIN produto p ON mp.produto = p.codigo AND p.tipoproduto = 'TA' WHERE mp.contrato = con.codigo), 0) AS valorAnuidade");

        consulta.setTabelaPrincipal("contrato con");

        consulta.getJoins().add("INNER JOIN pessoa pes ON con.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN cliente cli ON con.pessoa = cli.pessoa");
        consulta.getJoins().add("INNER JOIN plano pl ON con.plano = pl.codigo");
        consulta.getJoins().add("INNER JOIN empresa emp ON con.empresa = emp.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("WHERE con.situacao = 'AT'\n" +
                "      AND con.regimerecorrencia = TRUE\n" +
                "      AND con.plano IN (SELECT\n" +
                "                          codigo\n" +
                "                        FROM plano\n" +
                "                        WHERE recorrencia IS TRUE)\n" +
                "ORDER BY valorAnuidade, plano, pes.nome");

        consultas.add(consulta);

        consulta = new ConsultaTO("NaoRenovaAutomaticamente", "Contratos que não renovam automaticamente", "Retorna informações dos contratos, que não renovam automaticamente");
        consulta.getColunas().add("pes.nome as cliente, con.codigo as contrato, emp.nome as empresa");
        consulta.setTabelaPrincipal("contrato con");
        consulta.getJoins().add("INNER JOIN pessoa pes ON con.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN empresa emp ON con.empresa = emp.codigo");
        consulta.setPermiteWhere(true);
        consulta.setWhere("con.situacao = 'AT' AND con.permiterenovacaoautomatica = 'f'");

        consultas.add(consulta);

        return consultas;
    }

    public List<GruposConsultasTO> getConsultasCadastradas() {
        if (consultasCadastradas == null) {
            consultasCadastradas = inicializarConsultasCadastradas();
        }
        return consultasCadastradas;
    }

    private List<ConsultaTO> consultasProdutos() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("ProdutosFaturados", "Produtos faturados", "SQL que irá consultar todos os produtos");
        consulta.getColunas().add("distinct mp.codigo");
        consulta.getColunas().add("mp.descricao");
        consulta.getColunas().add("date_part('year', mp.datalancamento) as Ano_lancamento");
        consulta.getColunas().add("date_part('month', mp.datalancamento) as Mes_datalancamento");
        consulta.getColunas().add("date_part('day', mp.datalancamento) as Dia_datalancamento");
        consulta.getColunas().add("date_part('hour', mp.datalancamento) as Hora_datalancamento");
        consulta.getColunas().add("pe.nome as comprador");
        consulta.getColunas().add("usr.nome as responsavel");
        consulta.getColunas().add("con.nome as consultor");
        consulta.getColunas().add("emp.nome as empresa");
        consulta.getColunas().add("pr.tipoproduto");
        consulta.getColunas().add("cp.descricao as categoria");
        consulta.getColunas().add("mp.precounitario");
        consulta.getColunas().add("mp.quantidade");
        consulta.getColunas().add("mp.valordesconto");
        consulta.getColunas().add("mp.totalfinal");
        consulta.getColunas().add("mp.situacao");

        consulta.setTabelaPrincipal("movproduto mp");

        consulta.getJoins().add("inner join empresa emp on emp.codigo = mp.empresa");
        consulta.getJoins().add("left join pessoa pe on pe.codigo = mp.pessoa");
        consulta.getJoins().add("inner join usuario usr on usr.codigo = mp.responsavellancamento");
        consulta.getJoins().add("left join movprodutoparcela mpp on mp.codigo = mpp.movproduto");
        consulta.getJoins().add("left join reciboclienteconsultor rc on mpp.recibopagamento = rc.recibo");
        consulta.getJoins().add("left join colaborador col on col.codigo = rc.consultor ");
        consulta.getJoins().add("left join pessoa con on con.codigo = col.pessoa");
        consulta.getJoins().add("inner join produto pr on pr.codigo = mp.produto");
        consulta.getJoins().add("inner join categoriaproduto cp on pr.categoriaproduto = cp.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("mp.datalancamento BETWEEN '2014-03-01 00:00:00' AND '2014-03-25 23:59:59' and pr.tipoproduto in ('PM') and cp.codigo = 8");

        consultas.add(consulta);

        consulta = new ConsultaTO("Desconto", "Descontos", "SQL que trará todos os descontos que foram aplicados em um período");
        consulta.getColunas().add("p.descricao");
        consulta.getColunas().add("u.nome        AS usuario");
        consulta.getColunas().add("cli.matricula");
        consulta.getColunas().add("pes.nome      AS cliente");
        consulta.getColunas().add("mp.contrato");
        consulta.getColunas().add("con.datalancamento");
        consulta.getColunas().add("CASE when p.tipoproduto = 'DE' THEN mp.totalfinal else mp.valordesconto END AS desconto");
        consulta.getColunas().add("c2.descricao as convenio");
        consulta.getColunas().add("cdct.quantidadecreditocompra");
        consulta.setTabelaPrincipal("movproduto mp");

        consulta.getJoins().add("INNER JOIN produto p ON mp.produto = p.codigo");
        consulta.getJoins().add("INNER JOIN usuario u ON mp.responsavellancamento = u.codigo");
        consulta.getJoins().add("INNER JOIN contrato con ON mp.contrato = con.codigo");
        consulta.getJoins().add("INNER JOIN cliente cli ON mp.pessoa = cli.pessoa");
        consulta.getJoins().add("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa");
        consulta.getJoins().add("INNER JOIN empresa emp ON emp.codigo = con.empresa");
        consulta.getJoins().add("LEFT JOIN conveniodesconto c2 ON con.conveniodesconto = c2.codigo");
        consulta.getJoins().add("LEFT JOIN contratoduracao cd ON con.codigo = cd.contrato");
        consulta.getJoins().add("LEFT JOIN contratoduracaocreditotreino cdct ON cd.codigo = cdct.contratoduracao");

        consulta.setPermiteWhere(true);
        consulta.setWhere("con.datalancamento BETWEEN '2014-04-01 00:00:00' AND '2014-04-30 23:59:59' and ((mp.valordesconto >0) OR (p.tipoproduto = 'DE'))");

        consultas.add(consulta);

        consulta = new ConsultaTO("ProdutosVendidos", "Produtos Vendidos", "SQL que trará todos os produtos de determinado tipo vendidos em determinado periodo");
        consulta.getColunas().add("c.matricula");
        consulta.getColunas().add("p.nome");
        consulta.getColunas().add("m.datalancamento");
        consulta.getColunas().add("m.descricao");
        consulta.getColunas().add("m.quantidade");
        consulta.getColunas().add("m.precounitario");
        consulta.getColunas().add("m.valordesconto");
        consulta.getColunas().add("m.totalfinal");
        consulta.getColunas().add("m.situacao");
        consulta.getColunas().add("e.nome as empresa");

        consulta.setTabelaPrincipal("movproduto m");

        consulta.getJoins().add("INNER JOIN pessoa p ON m.pessoa = p.codigo");
        consulta.getJoins().add("INNER JOIN cliente c ON c.pessoa = p.codigo");
        consulta.getJoins().add("INNER JOIN produto pr ON pr.codigo = m.produto");
        consulta.getJoins().add("INNER JOIN empresa e ON e.codigo = c.empresa");

        consulta.setPermiteWhere(true);
        consulta.setWhere("WHERE c.empresa = 1 \n" +
                "AND pr.tipoproduto = 'SE'\n" +
                "AND m.datalancamento BETWEEN '2015-04-01 00:00:00' AND '2015-04-30 23:59:59'");

        consultas.add(consulta);

        consulta = new ConsultaTO("CompetenciaRecebida", "Competência Recebida", "Consulta que trará a competência de determinado mês, porém apenas recebida.");
        consulta.getColunas().add("  DISTINCT mov.mesreferencia");
        consulta.getColunas().add("  mov.datalancamento");
        consulta.getColunas().add("  mov.precounitario");
        consulta.getColunas().add("  mov.valordesconto");
        consulta.getColunas().add("  mov.totalfinal");
        consulta.getColunas().add("  mov.descricao");
        consulta.getColunas().add("  pes.nome");
        consulta.getColunas().add("  mov.contrato");

        consulta.setTabelaPrincipal("movpagamento mpag");

        consulta.getJoins().add("INNER JOIN pagamentomovparcela pmp ON mpag.codigo = pmp.movpagamento");
        consulta.getJoins().add("INNER JOIN movprodutoparcela mpp ON pmp.movparcela = mpp.movparcela");
        consulta.getJoins().add("INNER JOIN movproduto mov ON mpp.movproduto = mov.codigo");
        consulta.getJoins().add("INNER JOIN produto prod ON mov.produto = prod.codigo");
        consulta.getJoins().add("INNER JOIN pessoa pes ON pes.codigo = mov.pessoa");

        consulta.setPermiteWhere(true);
        consulta.setWhere("WHERE 1 = 1\n" +
                "AND mov.TotalFinal > 0\n" +
                "AND mov.movpagamentocc IS NULL\n" +
                "AND mov.situacao != 'CA'" +
                "AND mov.mesReferencia = '08/2015'\n" +
                "AND mov.empresa = 2\n" +
                "ORDER BY nome, descricao");

        consultas.add(consulta);



        consulta = new ConsultaTO("ParcelasAPagarProdutoEstoque", "Parcelas a Pagar Produto Estoque", "Consulta que trará as parcelas a pagar de produtos do tipo estoque.");
        consulta.getColunas().add("  dw.matricula as matricula");
        consulta.getColunas().add("  dw.nomecliente as nome");
        consulta.getColunas().add(" movparcela.datavencimento::DATE as vencimento");
        consulta.getColunas().add("  sum(movparcela.valorparcela) as valor");
        consulta.setTabelaPrincipal("movparcela");
        consulta.getJoins().add("INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa\n");
        consulta.getJoins().add("LEFT JOIN MovprodutoParcela mpp ON mpp.movparcela = movparcela.codigo\n");
        consulta.getJoins().add("LEFT JOIN MovProduto mp ON mp.codigo = mpp.movproduto\n");
        consulta.getJoins().add("LEFT JOIN Produto prod ON prod.codigo = mp.produto\n");
        consulta.setPermiteWhere(true);
        StringBuilder strWhere = new StringBuilder();
        strWhere.append("WHERE movparcela.situacao = 'EA' ");
        strWhere.append(" AND movparcela.empresa = ").append(empresaLogada).append("\n");
        strWhere.append(" AND prod.tipoproduto = '").append(TipoProduto.PRODUTO_ESTOQUE.getCodigo()).append("'\n");
        strWhere.append("AND movparcela.datavencimento::DATE >= '").append(Uteis.getData(Calendario.hoje())).append("'\n");
        strWhere.append("GROUP BY dw.codigocliente,dw.matricula,dw.nomecliente,vencimento");
        consulta.setWhere(strWhere.toString());
        consultas.add(consulta);


        consulta = new ConsultaTO("ParcelasAtrasoProdutoEstoque", "Parcelas Atraso Produto Estoque", "Consulta que trará as parcelas em atraso de produtos do tipo estoque.");
        consulta.getColunas().add("  dw.matricula as matricula");
        consulta.getColunas().add("  dw.nomecliente as nome");
        consulta.getColunas().add(" movparcela.datavencimento::DATE as vencimento");
        consulta.getColunas().add("  sum(movparcela.valorparcela) as valor");
        consulta.setTabelaPrincipal("movparcela");
        consulta.getJoins().add("INNER JOIN Situacaoclientesinteticodw dw on dw.codigopessoa = movparcela.pessoa\n");
        consulta.getJoins().add("LEFT JOIN MovprodutoParcela mpp ON mpp.movparcela = movparcela.codigo\n");
        consulta.getJoins().add("LEFT JOIN MovProduto mp ON mp.codigo = mpp.movproduto\n");
        consulta.getJoins().add("LEFT JOIN Produto prod ON prod.codigo = mp.produto\n");
        consulta.setPermiteWhere(true);
        strWhere = new StringBuilder();
        strWhere.append("WHERE movparcela.situacao = 'EA' ");
        strWhere.append(" AND movparcela.empresa = ").append(empresaLogada).append("\n");
        strWhere.append(" AND prod.tipoproduto = '").append(TipoProduto.PRODUTO_ESTOQUE.getCodigo()).append("'\n");
        strWhere.append("AND movparcela.datavencimento::DATE < '").append(Uteis.getData(Calendario.hoje())).append("'\n");
        strWhere.append("GROUP BY dw.codigocliente,dw.matricula,dw.nomecliente,vencimento");
        consulta.setWhere(strWhere.toString());
        consultas.add(consulta);

        return consultas;
    }


    private List<ConsultaTO> consultasFamilia() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("Familiares",
                "Todos os familiares",
                "Retorna informações dos vínculos de família");

        consulta.getColunas().add("fm.nome as familiar");
        consulta.getColunas().add("clifam.matricula as matriculaFamiliar");
        consulta.getColunas().add("pa.descricao as parentesco");
        consulta.getColunas().add("pes.nome as cliente");
        consulta.getColunas().add("cli.matricula as matriculacliente");
        consulta.getColunas().add("e.nome as empresa");

        consulta.setTabelaPrincipal("familiar fm");

        consulta.getJoins().add("INNER JOIN parentesco pa ON pa.codigo = fm.parentesco");
        consulta.getJoins().add("INNER JOIN cliente cli ON fm.cliente = cli.codigo");
        consulta.getJoins().add("INNER JOIN pessoa pes ON cli.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN empresa e ON e.codigo = cli.empresa");
        consulta.getJoins().add("INNER JOIN cliente clifam ON fm.familiar = clifam.codigo");

        consulta.setPermiteWhere(true);
        consulta.setWhere("WHERE e.codigo > 0");

        consultas.add(consulta);

        consulta = new ConsultaTO("FamiliaresAgrupados",
                "Todos os vínculos de familiares agrupados por pai ou mãe ",
                "Retorna os familiares agrupados por pai ou por mãe que tem filhos sem vínculo com pai");
        consulta.getColunas().add("fm.nome as pai_mae");
        consulta.getColunas().add("ARRAY_TO_STRING(ARRAY(SELECT nome FROM familiar INNER JOIN parentesco ON parentesco.codigo = familiar.parentesco WHERE familiar.cliente = fm.familiar and parentesco.descricao LIKE 'FILH%'), ',') as filhos");
        consulta.getColunas().add("ARRAY_TO_STRING(ARRAY(SELECT nome FROM familiar INNER JOIN parentesco ON parentesco.codigo = familiar.parentesco WHERE familiar.cliente = fm.familiar and parentesco.descricao IN ('CONJUGE','CÔNJUGE')), ',') as conjuge");
        consulta.getColunas().add("ARRAY_TO_STRING(ARRAY(SELECT nome FROM familiar INNER JOIN parentesco ON parentesco.codigo = familiar.parentesco WHERE familiar.cliente = fm.familiar and parentesco.descricao NOT IN ('CONJUGE','CÔNJUGE', 'FILHO', 'FILHA')), ',') as outro_parentesco");
        consulta.getColunas().add("(SELECT COUNT(codigo) FROM familiar WHERE cliente = fm.familiar) as nrfamiliares");

        consulta.setTabelaPrincipal("familiar fm");

        consulta.getJoins().add("INNER JOIN parentesco pa ON pa.codigo = fm.parentesco");
        consulta.setPermiteWhere(true);
        consulta.setWhere("WHERE pa.descricao IN ('MAE','MÃE','PAI')\n" +
                "AND (pa.descricao LIKE 'PAI' \n" +
                "	OR  EXISTS(SELECT familiar.codigo FROM familiar "
                + "INNER JOIN parentesco ON parentesco.codigo = familiar.parentesco AND parentesco.descricao IN ('MAE','MÃE') \n" +
                " WHERE familiar = fm.familiar AND  cliente NOT IN (SELECT cliente FROM familiar "
                + " INNER JOIN parentesco ON parentesco.codigo = familiar.parentesco AND parentesco.descricao LIKE 'PAI') ))\n" +
                " GROUP BY fm.familiar, fm.nome order by fm.nome");
        consultas.add(consulta);

        return consultas;
    }


    private List<ConsultaTO> consultasAlunos() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("AlunoHorariosTurmas", "Aluno Horário Turmas", "Mostra todas os horários que o aluno está na turma.");

        consulta.getColunas().add("cli.matricula as matriculaCliente");
        consulta.getColunas().add("pes.nome as nomeAluno");
        consulta.getColunas().add("con.codigo as contrato");
        consulta.getColunas().add("cod.numeromeses as duracaoContrato");
        consulta.getColunas().add("con.vigenciade as inicioContrato");
        consulta.getColunas().add("con.vigenciaateajustada as fimContrato");
        consulta.getColunas().add("ht.diasemana as diaSemana");
        consulta.getColunas().add("ht.horainicial as horaInicio");
        consulta.getColunas().add("ht.horafinal as horaFim");
        consulta.getColunas().add("pesc.nome as professorHorario");
        consulta.getColunas().add("niv.descricao as nivelHorario");
        consulta.getColunas().add("m.nome as modalidade");
        consulta.getColunas().add("t.descricao as turma");

        consulta.setTabelaPrincipal("contrato con");

        consulta.getJoins().add("INNER JOIN pessoa pes on con.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN cliente cli on cli.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN matriculaalunohorarioturma mah on mah.contrato = con.codigo");
        consulta.getJoins().add("INNER JOIN horarioturma ht on ht.codigo = mah.horarioturma");
        consulta.getJoins().add("INNER JOIN turma t on t.codigo = ht.turma");
        consulta.getJoins().add("INNER JOIN modalidade m on m.codigo = t.modalidade");
        consulta.getJoins().add("INNER JOIN colaborador col on col.codigo = ht.professor");
        consulta.getJoins().add("INNER JOIN pessoa pesc on pesc.codigo = col.pessoa");
        consulta.getJoins().add("INNER JOIN contratoduracao cod on cod.contrato = con.codigo");
        consulta.getJoins().add("INNER JOIN nivelturma niv on niv.codigo = ht.nivelturma");

        consulta.setPermiteWhere(true);
        consulta.setWhere("cli.matricula = 'matriculaCliente' ORDER BY m.nome");

        consultas.add(consulta);



        consultas.add(obterConsultaAlunoEstacionamento(Uteis.somarDias(Calendario.hoje(),-1)));

        return consultas;
    }

    public static ConsultaTO obterConsultaAlunoEstacionamento(Date dia){
        return obterConsultaAlunoEstacionamento(dia, EImportacaoClinteEstacionamento.COM_VALOR_COM_HORA);
    }
    public static ConsultaTO obterConsultaAlunoEstacionamento(Date dia, EImportacaoClinteEstacionamento retornoValor){

        ConsultaTO consulta = new ConsultaTO("AlunoEstacionamento", "Integração Aluno Estacionamento", "Gerar arquivo '.txt' necessário para integração estacionamento.");

        consulta.getColunas().add("cli.codigomatricula as matriculaCliente");
        consulta.getColunas().add("pes.nome as nomeAluno");
        consulta.getColunas().add("cli.pessoaresponsavel as responsavel");
        consulta.getColunas().add("con.vigenciade as validadeInicial");
        consulta.getColunas().add("con.vigenciaateajustada as validadeFinal");
        consulta.getColunas().add("cli.situacao as situacao");
        consulta.getColunas().add("pes.cfp as CPF");
        if(retornoValor.equals(EImportacaoClinteEstacionamento.COM_VALOR_COM_HORA) || retornoValor.equals(EImportacaoClinteEstacionamento.COM_VALOR_SEM_HORA)) {
            consulta.getColunas().add("con.valorfinal as ValorFinalContrato");
        }

        consulta.setTabelaPrincipal("Cliente cli");

        consulta.getJoins().add("INNER JOIN pessoa pes on cli.pessoa = pes.codigo");
        consulta.getJoins().add("INNER JOIN contrato con on con.pessoa = cli.pessoa ");
        consulta.getJoins().add("INNER JOIN plano p on p.codigo = con.plano ");

        consulta.setPermiteWhere(true);

        try {
            consulta.setWhere(" (con.vigenciaDe::DATE <= '" + Uteis.getData(dia) + "' and con.vigenciaateajustada::DATE >= '" + Uteis.getData(dia) + "')  ");
        }catch (Exception ignored){}

        consulta.setFormatoExportar("text/plain");

        return consulta;
    }


    private List<ConsultaTO> consultaColaboradores() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("ColaboradoresAniversariantesdoMes", "Colaboradores Aniversariantes do Mês", "Todos os colaboradores aniversariantes do mês.");

        consulta.getColunas().add("col.codigo as codigoColaborador");
        consulta.getColunas().add("pes.nome");
        consulta.getColunas().add("pes.datanasc as dataNascimento");
        consulta.getColunas().add("col.situacao");
        consulta.getColunas().add("emp.nome as empresa");

        consulta.setTabelaPrincipal("colaborador col");

        consulta.getJoins().add("INNER JOIN pessoa pes ON pes.codigo = col.pessoa");
        consulta.getJoins().add("INNER JOIN empresa emp ON emp.codigo = col.empresa");

        consulta.setPermiteWhere(true);
        consulta.setWhere("DATE_PART('MONTH',pes.datanasc) = 8 " +
                "AND col.empresa = 1 ");

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultaGeralColaboradores() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("Colaboradores", "Colaboradores", "Todos os colaboradores");

        consulta.getColunas().add("CASE WHEN (select exists(select * from tipocolaborador  where colaborador = colab.codigo and descricao = 'FC')) = true  THEN 'Sim'   ELSE 'Não'   END as E_Funcionario");
        consulta.getColunas().add("CASE WHEN colab.situacao = 'NA' THEN 'INATIVO'  WHEN colab.situacao = 'AT' THEN 'ATIVO' ELSE colab.situacao END as Situacao");
        consulta.getColunas().add("pes.nome AS NOME");
        consulta.getColunas().add("pes.cfp");
        consulta.getColunas().add("pes.rg");
        consulta.getColunas().add("pes.estadocivil");
        consulta.getColunas().add("pes.datanasc");
        consulta.getColunas().add("pes.sexo");
        consulta.getColunas().add("pes.datacadastro");
        consulta.getColunas().add("array(SELECT\n" +
                "        CASE WHEN tc.descricao = 'CO' THEN 'Consultor'\n" +
                "             WHEN tc.descricao = 'CR' THEN 'Coordenador'\n" +
                "             WHEN tc.descricao = 'ES' THEN 'Estudio'\n" +
                "             WHEN tc.descricao = 'FO' THEN 'Fornecedor'\n" +
                "             WHEN tc.descricao = 'OR' THEN 'Orientador'\n" +
                "             WHEN tc.descricao = 'FC' THEN 'Funcionário'\n" +
                "             WHEN tc.descricao = 'PE' THEN 'Personal Externo'\n" +
                "             WHEN tc.descricao = 'PI' THEN 'Personal Interno'\n" +
                "             WHEN tc.descricao = 'PR' THEN 'Professor'                                                    \n" +
                "             WHEN tc.descricao = 'PW' THEN 'Professor (TreinoWeb)'                                                                \n" +
                "             WHEN tc.descricao = 'TE' THEN 'Tercerizado'  "
                + "WHEN tc.descricao = 'PT' THEN 'Personal Trainer'                                                               \n" +
                "             ELSE tc.descricao\n" +
                "        END as tc001 FROM tipocolaborador tc\n" +
                "      WHERE tc.colaborador = colab.codigo) as Desc_TipoColaborador");
        consulta.getColunas().add("(SELECT email.email FROM email WHERE email.pessoa = pes.codigo limit 1) as e_mail");
        consulta.getColunas().add("ende.tipoendereco as end_Tipo");
        consulta.getColunas().add("ende.endereco as end_endereco");
        consulta.getColunas().add("ende.numero as end_numero");
        consulta.getColunas().add("ende.bairro as end_bairro");
        consulta.getColunas().add("cida.nome as end_cidade");
        consulta.getColunas().add("ende.cep as end_cep");
        consulta.getColunas().add("uf.sigla as end_estado");
        consulta.getColunas().add("( select numero from telefone tel3 where tel3.pessoa = pes.codigo and tel3.tipotelefone = 'RE' limit 1 ) as Tel_Residencial");
        consulta.getColunas().add("( select numero from telefone tel2 where tel2.pessoa = pes.codigo and tel2.tipotelefone = 'CE' limit 1 ) as Tel_Celular");
        consulta.getColunas().add("( select numero from telefone tel1 where tel1.pessoa = pes.codigo and tel1.tipotelefone = 'CO' limit 1 ) as Tel_Comercial");
        consulta.getColunas().add("prof.descricao as nome_profissao");
        consulta.getColunas().add("dep.nome as departamento");


        consulta.setTabelaPrincipal("colaborador colab");

        consulta.getJoins().add("left join pessoa pes on colab.pessoa = pes.codigo");
        consulta.getJoins().add("left join endereco ende on ende.pessoa = pes.codigo");
        consulta.getJoins().add("left join cidade cida on pes.cidade = cida.codigo");
        consulta.getJoins().add("left join pais on pais.codigo = cida.pais");
        consulta.getJoins().add("left join estado uf on uf.codigo = pes.estado");
        consulta.getJoins().add("left join profissao prof on prof.codigo = pes.profissao");
        consulta.getJoins().add("left join departamento dep on dep.codigo = colab.departamento");

        consulta.setPermiteWhere(false);

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultaGeralDadosGeraisPMG() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("DadosGerenciasPMG", "Dados gerados para análises gerenciais",
                "SQL que permite a consulta de alguns dados para análises do PMG");

        consulta.getColunas().add("chave");
        consulta.getColunas().add("nomeempresa");
        consulta.getColunas().add("estado");
        consulta.getColunas().add("cidade");
        consulta.getColunas().add("indicador");
        consulta.getColunas().add("identificador");
        consulta.getColunas().add("periodicidade");
        consulta.getColunas().add("mes");
        consulta.getColunas().add("ano");
        consulta.getColunas().add("datageracao");
        consulta.getColunas().add("datapesquisainicio");
        consulta.getColunas().add("datapesquisafim");
        consulta.getColunas().add("datainicio");
        consulta.getColunas().add("valor");



        consulta.setTabelaPrincipal("dadosgerenciais");

        consulta.setPermiteWhere(true);
        consulta.setWhere("datainicio  BETWEEN '2014-10-01' and  '2014-12-01'\n");
        consulta.setPermiteSelectAll(true);
//                + "AND (periodicidade = 'MS')\n"
//                + "AND  (indicador = 'FI')\n"
//                + "     \n"
//                + "	-- ## PARAMETROS DE BUSCA ##\n"
//                + "	-- periodicidade = 'MS' para mensal | 'SM' semanal\n"
//                + "	-- indicador = 'MC' para Movimentaçao de Contratos | 'IC'  para Indice Conversão de Vendas\n"
//                + "	-- 'IR' para Indice de Renovação |'QP' para Contratos Faturados por Duração\n"
//                + "     -- 'PE'  para Pendências | 'FI'  para Indicadores Financeiros\n");

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultasContratos() {
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("ContratosFuturos", "Contratos Futuros",
                "Consulta que mostra os contratos que iram iniciar");

        consulta.getColunas().add("cli.matricula");
        consulta.getColunas().add("pes.nome");
        consulta.getColunas().add("con.codigo              AS Contrato");
        consulta.getColunas().add("con.vigenciade          AS Inicio_Contrato");
        consulta.getColunas().add("con.vigenciaateajustada AS Fim_Contrato");
        consulta.getColunas().add("emp.nome                AS empresa");

        consulta.setTabelaPrincipal("contrato con");

        consulta.getJoins().add("INNER JOIN cliente cli ON cli.pessoa = con.pessoa");
        consulta.getJoins().add("INNER JOIN pessoa pes ON pes.codigo = cli.pessoa");
        consulta.getJoins().add("INNER JOIN empresa emp ON emp.codigo = cli.empresa");

        consulta.setPermiteWhere(true);
        consulta.setWhere("emp.codigo = 1\n" +
                "  AND\n" +
                "  con.vigenciade > now()\n" +
                "ORDER BY pes.nome");

        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultasGrupoDesconto(){
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("GruposComDesconto", "Grupos com desconto com clientes relacionados",
                "Consulta que mostra quantos clientes estão em cada grupo com desconto");

        consulta.getColunas().add("DISTINCT clientegrupo.cliente as clienteCodigo");
        consulta.getColunas().add("cliente.situacao as situacao_cliente");
        consulta.getColunas().add("pessoa.nome");
        consulta.getColunas().add("(SELECT COUNT(cliente) FROM clientegrupo WHERE clientegrupo.grupo = grupo.codigo) as quantidade_clientes_no_grupo");
        consulta.getColunas().add("grupo.*");

        consulta.setTabelaPrincipal("grupo");

        consulta.getJoins().add("INNER JOIN clientegrupo on clientegrupo.grupo = grupo.codigo");
        consulta.getJoins().add("INNER JOIN cliente on cliente.codigo = clientegrupo.cliente");
        consulta.getJoins().add("INNER JOIN pessoa on pessoa.codigo = cliente.pessoa");

        consulta.setPermiteSelectAll(true);
        consultas.add(consulta);

        return consultas;
    }

    private List<ConsultaTO> consultasInternasPactoSolucoes(){
        List<ConsultaTO> consultas = new ArrayList<ConsultaTO>();

        ConsultaTO consulta = new ConsultaTO("TransacaoAprovadaRetentativa", "Transações aprovadas em retentativa",
                "Consulta que mostra as transações que foram negadas em uma adquirente e foram aprovadas em outra no mesmo dia.");

        consulta.setMsgAtencao("Não filtrar um período maior que 30 dias.<br/>Obs: Caso a academia tenha um grande fluxo de transações, filtrar um período menor que 30 dias.");

        consulta.getColunas().add("SQL.matricula");
        consulta.getColunas().add("SQL.nomepessoa                AS nome");
        consulta.getColunas().add("SQL.parceladescricao");
        consulta.getColunas().add("SQL.dataprocessamento :: DATE AS data");
        consulta.getColunas().add("CASE \n" +
                "         WHEN SQL.tipotentativa = 2 THEN 'VINDI' \n" +
                "         WHEN SQL.tipotentativa = 3 THEN 'CIELO' \n" +
                "         WHEN SQL.tipotentativa = 6 THEN 'MAXIPAGO' \n" +
                "         WHEN SQL.tipotentativa = 7 THEN 'REDE' \n" +
                "         WHEN SQL.tipotentativa = 10 THEN 'GETNET' \n" +
                "         WHEN SQL.tipotentativa = 11 THEN 'STONE' \n" +
                "         ELSE ( 'OUTRA - ' \n" +
                "                || SQL.tipotentativa ) \n" +
                "       END                           AS tentativa");
        consulta.getColunas().add("CASE \n" +
                "         WHEN SQL.tipoaprovado = 2 THEN 'VINDI' \n" +
                "         WHEN SQL.tipoaprovado = 3 THEN 'CIELO' \n" +
                "         WHEN SQL.tipoaprovado = 6 THEN 'MAXIPAGO' \n" +
                "         WHEN SQL.tipoaprovado = 7 THEN 'REDE' \n" +
                "         WHEN SQL.tipoaprovado = 10 THEN 'GETNET' \n" +
                "         WHEN SQL.tipoaprovado = 11 THEN 'STONE' \n" +
                "         ELSE ( 'OUTRA - ' \n" +
                "                || SQL.tipoaprovado ) \n" +
                "       END                           AS aprovada");
        consulta.getColunas().add("SQL.empresa");
        consulta.getColunas().add("SQL.empresacodigo");
        consulta.getColunas().add("SQL.pessoa");
        consulta.getColunas().add("SQL.parcela");


        consulta.setTabelaPrincipal("(SELECT t.codigo \n" +
                "                    AS \n" +
                "                    transacao, \n" +
                "              cl.matricula, \n" +
                "              p.codigo \n" +
                "                    AS pessoa, \n" +
                "              p.nome \n" +
                "                    AS nomepessoa, \n" +
                "              t.tipo \n" +
                "                    AS tipoTentativa, \n" +
                "              (SELECT t1.tipo \n" +
                "               FROM   transacao t1 \n" +
                "                      inner join transacaomovparcela tm1 \n" +
                "                              ON tm1.transacao = t1.codigo \n" +
                "               WHERE  t1.situacao = 4 \n" +
                "                      AND tm1.movparcela = tm.movparcela \n" +
                "                      AND t1.dataprocessamento :: DATE = \n" +
                "                          t.dataprocessamento :: DATE) \n" +
                "                    AS \n" +
                "              tipoAprovado, \n" +
                "              t.situacao, \n" +
                "              t.dataprocessamento, \n" +
                "              tm.movparcela \n" +
                "                    AS parcela, \n" +
                "              mp.descricao \n" +
                "                    AS parceladescricao, \n" +
                "              e.nome \n" +
                "                    AS empresa, \n" +
                "              e.codigo \n" +
                "                    AS empresacodigo \n" +
                "       FROM   transacao t \n" +
                "              inner join empresa e \n" +
                "                      ON e.codigo = t.empresa \n" +
                "              inner join pessoa p \n" +
                "                      ON p.codigo = t.pessoapagador \n" +
                "              left join cliente cl \n" +
                "                     ON cl.pessoa = p.codigo \n" +
                "              inner join transacaomovparcela tm \n" +
                "                      ON tm.transacao = t.codigo \n" +
                "              inner join movparcela mp \n" +
                "                      ON mp.codigo = tm.movparcela) AS SQL");


        consulta.setPermiteWhere(true);
        consulta.setWhere("SQL.tipoaprovado <> SQL.tipotentativa \n" +
                (UteisValidacao.emptyNumber(empresaLogada) ? "" : "--CASO DESEJE TODAS AS EMPRESAS - REMOVER A LINHA ABAIXO \n AND SQL.empresacodigo = " + empresaLogada + " \n") +
                "  --FILTRO DE DATAS \n" +
                "  AND SQL.dataprocessamento :: DATE BETWEEN '" + Uteis.getDataFormatoBD(Calendario.hoje()) + "' AND '" + Uteis.getDataFormatoBD(Calendario.hoje()) + "' \n" +
                "  --ATENÇÃO NÃO FILTRAR UM PERIODO MAIOR QUE 30 DIAS - CONSULTA DEMORADA\n" +
                "ORDER BY 2");

        consulta.setPermiteSelectAll(true);
        consultas.add(consulta);

        return consultas;
    }
}

package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;

public class ClientePorDuracaoVO extends SuperVO {

    protected ContratoVO contratoVO;
    protected PessoaVO pessoaVO;
    protected String descricao;
    protected ClienteVO clienteVO;
    protected Integer numeroMeses;
    protected String situacao;

    public String getCliente_Apresentar() {
        return getClienteVO().getMatricula();
    }

    public String getPessoa_Apresentar() {
        return getPessoaVO().getNome();
    }

    public int getCodigo_Apresentar() {
        return getContratoVO().getCodigo();
    }

    public String getVigenciaDe_Apresentar() {
        return getContratoVO().getVigenciaDe_Apresentar();
    }

    public String getVigenciaAte_Apresentar() {
        return getContratoVO().getVigenciaAteAjustada_Apresentar();
    }

    public String getModalidade_Apresentar() {
        return getContratoVO().getNomeModalidades();
    }

    public ClientePorDuracaoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados() {

        setContratoVO(new ContratoVO());
        setPessoaVO(new PessoaVO());
        setDescricao("");
        setClienteVO(new ClienteVO());
        setNumeroMeses(new Integer(0));
        setSituacao("");
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getNumeroMeses() {
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            return "";
        }
        if (getSituacao().equals("NO")) {
            return "Normal";
        }
        if (getSituacao().equals("AV")) {
            return "A Vencer";
        }
        if (getSituacao().equals("VE")) {
            return "Vencido";

        }
        if (getSituacao().equals("CR")) {
            return "Férias";

        }
        if (getSituacao().equals("AT")) {
            return "Atestado";

        }
        if (getSituacao().equals("TR")) {
            return "Trancado";
        }
        return "";
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }
}

package relatorio.negocio.comuns.basico;

import java.util.Date;
import negocio.comuns.arquitetura.SuperTO;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class ClienteRelatorioTO extends SuperTO {
    private static final long serialVersionUID = 6202071184994396127L;

    private String matricula;
    private String nome;
    private String empresa;
    private String situacao;
    private String situacaoContrato;
    private String vinculo;
    private String plano;
    private String contrato;
    private String faturamento;
    private String modalidade;
    private String duracao;
    private String horario;
    private String inicio;
    private String vence;
    private String dataLancamento;
    private String valorModalidade;
    private String receita;
    private String categoriaCliente;
    private String nivelTurma;
    private String email;

    public String getCategoriaCliente() {
        return categoriaCliente;
    }

    public void setCategoriaCliente(String categoriaCliente) {
        this.categoriaCliente = categoriaCliente;
    }

    private Double faturamentoOrdem;
    private Double valorModalidadeOrdem;
	private Integer pessoa;


    /**
     * @return the situacaoContrato
     */
    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    /**
     * @param situacaoContrato the situacaoContrato to set
     */
    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    /**
     * @return the vinculo
     */
    public String getVinculo() {
        return vinculo;
    }

    /**
     * @param vinculo the vinculo to set
     */
    public void setVinculo(String vinculo) {
        this.vinculo = vinculo;
    }

    /**
     * @return the plano
     */
    public String getPlano() {
        return plano;
    }

    /**
     * @param plano the plano to set
     */
    public void setPlano(String plano) {
        this.plano = plano;
    }

    /**
     * @return the contrato
     */
    public String getContrato() {
        if (contrato == null) {
            contrato = "";
        }
        return contrato;
    }

    /**
     * @param contrato the contrato to set
     */
    public void setContrato(String contrato) {
        this.contrato = contrato;
    }

    /**
     * @return the valor
     */
    public String getFaturamento() {
        return faturamento;
    }

    /**
     * @param valor the valor to set
     */
    public void setFaturamento(String faturamento) {
        this.faturamento = faturamento;
    }

    /**
     * @return the modalidade
     */
    public String getModalidade() {
        if (modalidade == null) {
            modalidade = "";
        }
        return modalidade;
    }

    /**
     * @param modalidade the modalidade to set
     */
    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }

    /**
     * @return the duracao
     */
    public String getDuracao() {
        return duracao;
    }

    public String getNivelTurma() {
        return nivelTurma;
    }

    public void setNivelTurma(String nivelTurma) {
        this.nivelTurma = nivelTurma;
    }

    /**
     * @param duracao the duracao to set
     */
    public void setDuracao(String duracao) {
        this.duracao = duracao;
    }

    /**
     * @return the horario
     */
    public String getHorario() {
        return horario;
    }

    /**
     * @param horario the horario to set
     */
    public void setHorario(String horario) {
        this.horario = horario;
    }

    /**
     * @return the inicio
     */
    public String getInicio() {
        return inicio;
    }
    
    public Date getInicioOrdem() throws Exception {
    	return extrairMaiorData(getInicio());
    }
    
    public Date getDataLancamentoOrdem() throws Exception {
    	return extrairMaiorData(getDataLancamento());
    }

    /**
     * @param inicio the inicio to set
     */
    public void setInicio(String inicio) {
        this.inicio = inicio;
    }

    /**
     * @return the vence
     */
    public String getVence() {
        return vence;
    }
    public Integer getDuracaoOrdem(){
    	Integer ordem = 0;
    	String[] split = getDuracao().split("\\<br\\/\\>");
    	if(split != null)
	    	for(String d : split){
	    		if(!UteisValidacao.emptyString(d)){
	    			Integer duracao = Integer.valueOf(d);
	    			if(duracao > ordem){
		    				ordem = duracao;
		    		}
	    		}
	    	}
    	return ordem;
    }
    
    public Integer getMatriculaOrdem(){
    	return (UteisValidacao.emptyString(matricula) ? 0 : Integer.valueOf(matricula));
    }
    public Date getMaiorData() throws Exception{
    	return extrairMaiorData(getVence());
    }

	/**
	 * Responsável por 
	 * <AUTHOR> Alcides
	 * 18/02/2013
	 */
	private Date extrairMaiorData(String campo) throws Exception {
		Date dataMaior = null;
		String[] split = campo.split("\\<br\\/\\>");
    	if(split != null)
	    	for(String d : split){
	    		if(!UteisValidacao.emptyString(d)){
	    			Date data = Uteis.getDate(d);
		    		if(dataMaior == null){
		    			dataMaior = data;
		    		}else{
		    			if(Calendario.maior(data, dataMaior)){
		    				dataMaior = data;
		    			}
		    		}	
	    		}
	    	}
		return dataMaior;
	}

    /**
     * @param vence the vence to set
     */
    public void setVence(String vence) {
        this.vence = vence;
    }

    /**
     * @return the matricula
     */
    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    /**
     * @param matricula
     *            the matricula to set
     */
    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    /**
     * @return the empresa
     */
    public String getEmpresa() {
        return empresa;
    }

    /**
     * @param empresa the situacao to set
     */
    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    /**
     * @return the nome
     */
    public String getNome() {
        return nome;
    }

    /**
     * @param situacao the situacao to set
     */
    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    /**
     * @return the situacao
     */
    public String getSituacao() {
        return situacao;
    }

    /**
     * @param nome
     *            the nome to set
     */
    public void setNome(String nome) {
        this.nome = nome;
    }

    /**
     * @param valorModalidade the valorModalidade to set
     */
    public void setValorModalidade(String valorModalidade) {
        this.valorModalidade = valorModalidade;
    }

    /**
     * @return the valorModalidade
     */
    public String getValorModalidade() {
        if (valorModalidade == null) {
            valorModalidade = "";
        }
        return valorModalidade;
    }

    /**
     * @param dataLancamento the dataLancamento to set
     */
    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    /**
     * @return the dataLancamento
     */
    public String getDataLancamento() {
        return dataLancamento;
    }

    /**
     * @param receita the receita to set
     */
    public void setReceita(String receita) {
        this.receita = receita;
    }

    /**
     * @return the receita
     */
    public String getReceita() {
        if (receita == null) {
            receita = "";
        }
        return receita;
    }
    
    public void setPessoa(Integer pessoa) {
		this.pessoa = pessoa;
	}

	public Integer getPessoa() {
		if(pessoa == null){
			pessoa = new Integer(0);
		}
		return pessoa;
	}

	public void setFaturamentoOrdem(Double faturamentoOrdem) {
		this.faturamentoOrdem = faturamentoOrdem;
	}

	public Double getFaturamentoOrdem() {
		return faturamentoOrdem;
	}

	public void setValorModalidadeOrdem(Double valorModalidadeOrdem) {
		this.valorModalidadeOrdem = valorModalidadeOrdem;
	}

	public Double getValorModalidadeOrdem() {
		return valorModalidadeOrdem;
	}

    public String getEmail() {
        if(email == null){
            email = "";
        }
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}

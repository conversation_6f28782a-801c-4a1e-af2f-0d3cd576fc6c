/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.LogAgrupadoChavePrimaria;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoObservacaoOperacaoEnum;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class PendenciaResumoPessoaRelVO extends SuperVO implements Comparable {

    protected ClienteVO clienteVO;
    protected ContratoVO contratoVO;
    private ContratoOperacaoVO contratoOperacaoVO;
    //atributo usado para listar informações de colaboradores
    private ColaboradorVO colaboradorVO;
    private LogVO logVO;
    private EstornoObservacaoVO estornoObservacaoVO;
    private ObservacaoOperacaoVO observacaoOperacaoVO;
    private LogAgrupadoChavePrimaria logAgrupadoChavePrimaria;
    private MovPagamentoVO movPagamentoVO;
    private MovProdutoVO movProdutoVO;
    private String conteudoMensagem;
    private Double valorCC;
    private Integer qtdFreePass = 0;
    private Integer qtdGymPass = 0;
    private Boolean apresentarFreePass = false;
    private Boolean apresentarGymPass = false;
    private String autorizacaoCobrancaCliente = "";
    private PessoaVO pessoaVO;
    private Date dataRegistro;
    private Double valorEmAberto;
    private Integer qtdParcelaEmAtraso;
    private String token;
    private String tipoGymPass;
    private String problemaCartao;
    private String dataNasc_Apresentar;
    private String moeda;
    private Date dataBV;
    private String tipoBV;
    private String nomeEmpresa;
    private String produtoDescricao;

    public PendenciaResumoPessoaRelVO() {
    }

    public String getNomeEstorno() {
        return getEstornoObservacaoVO().getNome();
    }

    public String getCodContratoEstorno() {
        return getEstornoObservacaoVO().getCodContrato().toString();
    }

    public String getDataEstorno_Apresentar() {
        return getEstornoObservacaoVO().getDataEstorno_Apresentar();
    }

    public String getUsuarioResponsavelEstorno() {
        return getEstornoObservacaoVO().getUsuarioResponsavel();
    }

    public String getJustificativaEstorno() {
        return getEstornoObservacaoVO().getJustificativa();
    }

    public String getChavePrimaria() {
        return getLogAgrupadoChavePrimaria().getChavePrimaria();
    }

    public String getNomeEntidade() {
        return getLogAgrupadoChavePrimaria().getNomeEntidade();
    }

    public String getDataLogAlteracao() {
        return getLogAgrupadoChavePrimaria().getDataHoraAlteracao_Apresentar();
    }

    public String getOperacoes() {
        return getLogAgrupadoChavePrimaria().getOperacao();
    }

    public String getResponsavelAlteracao() {
        return getLogAgrupadoChavePrimaria().getResponsavelAlteracao();
    }

    public String getNomeLog_Apresentar() {
        return getLogVO().getNomeExclusaoVisitantes();
    }

    public String getDataExclusao_Apresentar() {
        return getLogVO().getDataHoraAlteracao_Apresentar();
    }

    public String getResponsavel_Apresentar() {
        return getLogVO().getResponsavelAlteracao();
    }

    public String getNomeProd_Apresentar() {
        return getMovProdutoVO().getPessoa().getNome();
    }

    public String getProduto_Apresentar() {
        return getMovProdutoVO().getProduto().getDescricao();
    }

    public String getVencimento_Apresentar() {
        return getMovProdutoVO().getDataFinalVigencia_Apresentar();
    }

    public String getNomePagador() {
        return getMovPagamentoVO().getNomePagador();
    }

    public String getDataLancamento() {
        return getMovPagamentoVO().getDataLancamentoSemHora_Apresentar();
    }

    public String getDataPagamento() {
        return getMovPagamentoVO().getDataPagamentoSemHora_Apresentar();
    }

    public String getDataAlteracao() {
        return getMovPagamentoVO().getDataAlteracaoManualSemHora_Apresentar();
    }

    public double getValor() {
        return getMovPagamentoVO().getValor();
    }

    public String getFormaPagamento() {
        return getMovPagamentoVO().getFormaPagamento().getDescricao();
    }

    public String getResponsavelPagamento() {
        return getMovPagamentoVO().getResponsavelPagamento().getNome();
    }

    public String getDataQuitacao() {
        return getMovPagamentoVO().getDataQuitacao_Apresentar();
    }

    public String getMatricula_Apresentar() {
        return getClienteVO().getMatricula();
    }

    public String getNome_Apresentar() {
        return getClienteVO().getPessoa().getNome();
    }

    public String getSituacao_Apresentar() {
        return getClienteVO().getSituacao_Apresentar();
    }

    public String getNomeContrato_Apresentar() {
        return getContratoVO().getPessoa().getNome();
    }

    public String getMatriculaContrato_Apresentar() {
        return getContratoVO().getCliente().getMatricula();
    }

    public Integer getContrato_Apresentar() {
        return getContratoVO().getCodigo();
    }

    public String getDataDe_Apresentar() {
        return getContratoVO().getVigenciaDe_Apresentar();
    }
    
    public Integer getMatricula(){
        return Integer.parseInt(getClienteVO().getMatricula());
    }

    public String getDataAte_Apresentar() {
        return getContratoVO().getVigenciaAteAjustada_Apresentar();
    }

    public Integer getDuracao_Apresentar() {
        return getContratoVO().getContratoDuracao().getNumeroMeses();
    }

    public String getModalidade_Apresentar() {
        return getContratoVO().getNomeModalidades();
    }

    public String getPlano_Apresentar() {
        return getContratoVO().getPlano().getDescricao();
    }

    public String getUsuario_Apresentar() {
        return getContratoVO().getResponsavelDataBase().getNome();
    }

    public String getDataBase_Apresentar() {
        return getContratoVO().getDataAlteracaoManual_Apresentar();
    }

    public String getDataLancamento_Apresentar() {
        return getContratoVO().getDataLancamento_Apresentar();
    }

    public Date getDataOperacao_Apresentar() {
        return getContratoOperacaoVO().getDataOperacao();
    }

    public Date getDataInicio_Apresentar() {
        return getContratoOperacaoVO().getDataInicioEfetivacaoOperacao();
    }

    public Date getDataFim_Apresentar() {
        return getContratoOperacaoVO().getDataFimEfetivacaoOperacao();
    }

    public ClienteVO getClienteVO() {
        if(clienteVO == null){
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    public String getTelefone_Apresentar(){
        if(getClienteVO() != null && !UteisValidacao.emptyString(getClienteVO().getPessoa().getListaTelefones_Apresentar())){
            return getClienteVO().getPessoa().getListaTelefones_Apresentar();
        }else if(!UteisValidacao.emptyString(getPessoaVO().getListaTelefones_Apresentar())){
            return getPessoaVO().getListaTelefones_Apresentar();
        }else {
            return getClienteVO().getPessoa().getNumeroTelefonesApresentar();
        }

    }
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public ContratoVO getContratoVO() {
        if(contratoVO == null){
            contratoVO = new ContratoVO();
        }
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public ColaboradorVO getColaboradorVO() {
        if (colaboradorVO == null) {
            colaboradorVO = new ColaboradorVO();
        }
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public LogVO getLogVO() {
        return logVO;
    }

    public void setLogVO(LogVO logVO) {
        this.logVO = logVO;
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public LogAgrupadoChavePrimaria getLogAgrupadoChavePrimaria() {
        return logAgrupadoChavePrimaria;
    }

    public void setLogAgrupadoChavePrimaria(LogAgrupadoChavePrimaria logAgrupadoChavePrimaria) {
        this.logAgrupadoChavePrimaria = logAgrupadoChavePrimaria;
    }

    public ContratoOperacaoVO getContratoOperacaoVO() {
        if(contratoOperacaoVO == null){
            contratoOperacaoVO = new ContratoOperacaoVO();
        }
        return contratoOperacaoVO;
    }

    public void setContratoOperacaoVO(ContratoOperacaoVO contratoOperacaoVO) {
        this.contratoOperacaoVO = contratoOperacaoVO;
    }

    public MovProdutoVO getMovProdutoVO() {
        return movProdutoVO;
    }

    public void setMovProdutoVO(MovProdutoVO movProdutoVO) {
        this.movProdutoVO = movProdutoVO;
    }

    public String getConteudoMensagem() {
        return conteudoMensagem;
    }

    public void setConteudoMensagem(String conteudoMensagem) {
        this.conteudoMensagem = conteudoMensagem;
    }

    public EstornoObservacaoVO getEstornoObservacaoVO() {
        return estornoObservacaoVO;
    }

    public void setEstornoObservacaoVO(EstornoObservacaoVO estornoObservacaoVO) {
        this.estornoObservacaoVO = estornoObservacaoVO;
    }

    public Double getValorCC() {
        return valorCC;
    }

    public void setValorCC(Double valorCC) {
        this.valorCC = valorCC;
    }

    public String getValorCC_Apresentar() {
        return (getValorCC() != null ? moeda + " " + Formatador.formatarValorMonetarioSemMoeda(getValorCC()) : "");
    }

    public ObservacaoOperacaoVO getObservacaoOperacaoVO() {
        return observacaoOperacaoVO;
    }

    public void setObservacaoOperacaoVO(ObservacaoOperacaoVO observacaoOperacaoVO) {
        this.observacaoOperacaoVO = observacaoOperacaoVO;
    }

    public String getColuna1_ObservacaoOperacao() {
        if (getObservacaoOperacaoVO().getTipoObservacao().equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            return getObservacaoOperacaoVO().getMovParcela().getCodigo().toString();
        }
        return "";
    }

    public String getColuna2_ObservacaoOperacao() {
        if (getObservacaoOperacaoVO().getTipoObservacao().equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            return getObservacaoOperacaoVO().getValorOperacao_Apresentar();
        }
        return "";
    }

    public String getColuna3_ObservacaoOperacao() {
        if (getObservacaoOperacaoVO().getTipoObservacao().equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            return getObservacaoOperacaoVO().getDataOperacao_Apresentar();
        }
        return "";
    }

    public String getColuna4_ObservacaoOperacao() {
        if (getObservacaoOperacaoVO().getTipoObservacao().equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            return getObservacaoOperacaoVO().getUsuarioResponsavel();
        }
        return "";
    }

    public String getColuna5_ObservacaoOperacao() {
        if (getObservacaoOperacaoVO().getTipoObservacao().equals(TipoObservacaoOperacaoEnum.PARCELA_CANCELADA)) {
            return getObservacaoOperacaoVO().getJustificativa();
        }
        return "";
    }

    public String getJustificativa_Apresentar() {
        return getContratoOperacaoVO().getJustificativaApresentar();
    }

    public Integer getQtdFreePass() {
        return qtdFreePass;
    }

    public void setQtdFreePass(Integer qtdFreePass) {
        this.qtdFreePass = qtdFreePass;
    }

    public Boolean getApresentarFreePass() {
        return apresentarFreePass;
    }

    public void setApresentarFreePass(Boolean apresentarFreePass) {
        this.apresentarFreePass = apresentarFreePass;
    }

    public String getPlanoApresentar() {
        return getContratoVO().getPlano().getDescricao();
    }

    public Integer getDuracaoApresentar() {
        return getContratoVO().getContratoDuracao().getNumeroMeses();
    }

    public String getAutorizacaoCobrancaCliente() {
        return autorizacaoCobrancaCliente;
    }

    public void setAutorizacaoCobrancaCliente(String autorizacaoCobrancaCliente) {
        this.autorizacaoCobrancaCliente = autorizacaoCobrancaCliente;
    }

    public String getDataRegistro_Apresentar(){
        return Uteis.getData(getDataRegistro());
    }

    public PessoaVO getPessoaVO() {
        if (pessoaVO == null) {
            pessoaVO = new PessoaVO();
        }
        return pessoaVO;
    }

    public Date getDataRegistro() {
        if(dataRegistro==null)
            dataRegistro = Calendario.hoje();
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public String getListaTelefones_Apresentar() {
        if(getPessoaVO().getListaTelefones_Apresentar().equals("")){
            return getClienteVO().getPessoa().getListaTelefones_Apresentar();
        }
        return getPessoaVO().getListaTelefones_Apresentar();


    }

    public Double getValorEmAberto() {
        return valorEmAberto;
    }

    public void setValorEmAberto(Double valorEmAberto) {
        this.valorEmAberto = valorEmAberto;
    }

    public String getValorEmAberto_Apresentar() {
        return moeda + Formatador.formatarValorMonetarioSemMoeda(getValorEmAberto());
    }

    public String getResponsavelOperacao_Apresentar(){
        if(colaboradorVO == null || colaboradorVO.getPessoa() == null){
            return "";
        } 
        return colaboradorVO.getPessoa_Apresentar();
    }
    
    public Integer getCodigoContratoOperacao_Apresentar() {
        return getContratoOperacaoVO().getContrato();
    }
    
    public String getDescricaoOperacao_Apresentar() {
        return getContratoOperacaoVO().getTipoOperacao_Apresentar();
    }
    
    public Date getDataInicioOperacao() {
        return getContratoOperacaoVO().getDataInicioEfetivacaoOperacao();
    }
    
    public Date getDataFinalOperacao() {
        return getContratoOperacaoVO().getDataFimEfetivacaoOperacao();
    }

    public Integer getQtdParcelaEmAtraso() {
        return qtdParcelaEmAtraso;
    }

    public void setQtdParcelaEmAtraso(Integer qtdParcelaEmAtraso) {
        this.qtdParcelaEmAtraso = qtdParcelaEmAtraso;
    }

    public Boolean getApresentarGymPass() {
        return apresentarGymPass;
    }

    public void setApresentarGymPass(Boolean apresentarGymPass) {
        this.apresentarGymPass = apresentarGymPass;
    }

    public String getToken() {
        if (token == null) {
            token = "";
        }
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getQtdGymPass() {
        return qtdGymPass;
    }

    public void setQtdGymPass(Integer qtdGymPass) {
        this.qtdGymPass = qtdGymPass;
    }
    
    public String getCpf_Apresentar(){
        return getClienteVO().getPessoa().getCfp();
    }
    
    public String getTipoGymPass() {
        return tipoGymPass;
    }

    public void setTipoGymPass(String tipoGymPass) {
        this.tipoGymPass = tipoGymPass;
    }

    public String getNomePessoaApresentar() {
        return getPessoaVO().getNome();
    }

    public String getProblemaCartao() {
        if (problemaCartao == null) {
            problemaCartao = "";
        }
        return problemaCartao;
    }

    public void setProblemaCartao(String problemaCartao) {
        this.problemaCartao = problemaCartao;
    }

    public String getVigenciaAteAjustada_Apresentar() {
        return Uteis.getData(getContratoVO().getVigenciaAteAjustada());
    }

    @Override
    public int compareTo(Object o) {
        int nome = this.getNome_Apresentar().compareTo(((PendenciaResumoPessoaRelVO)o).getNome_Apresentar());
        int data = this.getDataRegistro().compareTo(((PendenciaResumoPessoaRelVO)o).getDataRegistro());
        return nome == 0 ? data : nome;
    }

    public String getDataNasc_Apresentar() {
//      return getClienteVO().getPessoa().getDataNasc_Apresentar();
        if(getClienteVO() != null && !UteisValidacao.emptyString(getClienteVO().getPessoa().getDataNasc_Apresentar())){
            return getClienteVO().getPessoa().getDataNasc_Apresentar();
        }else if(!UteisValidacao.emptyString(getPessoaVO().getDataNasc_Apresentar())){
            return getPessoaVO().getDataNasc_Apresentar();
        }else {
            return getClienteVO().getPessoa().getDataNasc_Apresentar();
        }
    }

    public void setDataNasc_Apresentar(String dataNasc_Apresentar) {
        this.dataNasc_Apresentar = dataNasc_Apresentar;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public Date getDataBV() {
        return dataBV;
    }

    public String getDataBVApresentar() {
        return Uteis.getData(getDataBV());
    }

    public void setDataBV(Date dataBV) {
        this.dataBV = dataBV;
    }

    public String getTipoBV() {
        return tipoBV;
    }

    public void setTipoBV(String tipoBV) {
        this.tipoBV = tipoBV;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public void setProdutoDescricao(String produtoDescricao) {
        this.produtoDescricao = produtoDescricao;
    }

    public String getProdutoDescricao() {
        if (produtoDescricao == null) {
            return "";
        }
        return produtoDescricao;
    }
}

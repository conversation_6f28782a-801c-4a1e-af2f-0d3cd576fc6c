package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by GlaucoT ON 02/03/14.
 */
public class ConsultaTO extends SuperTO {

    private String nomeArquivo = "";
    private String nomeConsulta = "";
    private String hintConsulta = "";
    private String tabelaPrincipal = "";
    private List<String> colunas = new ArrayList<String>();
    private List<String> joins = new ArrayList<String>();
    private boolean permiteWhere = false;
    private boolean permiteSelectAll = false;
    private String where = "";
    private String formatoExportar = "application/vnd.ms-excel";
    private String msgAtencao;
    public ConsultaTO() {

    }

    public ConsultaTO(String nomeArquivo, String nomeConsulta, String hintConsulta) {
        this.nomeArquivo = nomeArquivo;
        this.nomeConsulta = nomeConsulta;
        this.hintConsulta = hintConsulta;
    }

    public String getSQLMontada() {
        StringBuilder sqlMontada = new StringBuilder("");
        sqlMontada.append("SELECT\n");
        for (String coluna : getColunas()) {
            sqlMontada.append(coluna).append(",\n");
        }
        sqlMontada.deleteCharAt(sqlMontada.length() - 1);
        sqlMontada.deleteCharAt(sqlMontada.length() - 1);
        sqlMontada.append("\nFROM ").append(getTabelaPrincipal()).append("\n");
        for (String join : getJoins()) {
            sqlMontada.append(join).append("\n");
        }
        return sqlMontada.toString();
    }

    public String getSQLMontadaApresentar() {
        String sqlApresentar = getSQLMontada();
        if (!getWhere().isEmpty()) {
            sqlApresentar = sqlApresentar + "\nWHERE " + getWhere();
        }
        sqlApresentar = sqlApresentar.replaceAll("\n", "<br/>");
        return sqlApresentar;
    }

    public String getNomeConsulta() {
        return nomeConsulta;
    }

    public void setNomeConsulta(String nomeConsulta) {
        this.nomeConsulta = nomeConsulta;
    }

    public String getTabelaPrincipal() {
        return tabelaPrincipal;
    }

    public void setTabelaPrincipal(String tabelaPrincipal) {
        this.tabelaPrincipal = tabelaPrincipal;
    }

    public List<String> getJoins() {
        return joins;
    }

    public void setJoins(List<String> joins) {
        this.joins = joins;
    }

    public List<String> getColunas() {
        return colunas;
    }

    public void setColunas(List<String> colunas) {
        this.colunas = colunas;
    }

    public String getHintConsulta() {
        return hintConsulta;
    }

    public void setHintConsulta(String hintConsulta) {
        this.hintConsulta = hintConsulta;
    }

    public boolean isPermiteWhere() {
        return permiteWhere;
    }

    public void setPermiteWhere(boolean permiteWhere) {
        this.permiteWhere = permiteWhere;
    }

    public String getWhere() {
        return where;
    }

    public void setWhere(String where) {
        this.where = where;
    }

    public boolean isPermiteSelectAll() {
        return permiteSelectAll;
    }

    public void setPermiteSelectAll(boolean permiteSelectAll) {
        this.permiteSelectAll = permiteSelectAll;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getFormatoExportar() {
        return formatoExportar;
    }

    public void setFormatoExportar(String formatoExportar) {
        this.formatoExportar = formatoExportar;
    }

    public String getMsgAtencao() {
        if (msgAtencao == null) {
            msgAtencao = "";
        }
        return msgAtencao;
    }

    public void setMsgAtencao(String msgAtencao) {
        this.msgAtencao = msgAtencao;
    }
}

package relatorio.negocio.comuns.basico;

import br.com.pacto.priv.utils.Uteis;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class ProbabilidadeEvasaoVO extends SuperVO {

    private Integer codigo;
    private Integer cliente;
    private Date dataPredicao;
    private Date dataPredicao30Dias;
    private String nome;
    private Float chancesair30dias;
    private String contato;
    private Date dataEnvio;
    private Date dataCriacao;
    private Date vigenteAte;

    private boolean registroSelecionado = false;
    private boolean disabledCheckBox;

    private String apresentarDataEnvio;
    private String apresentarChurnRate;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public Date getDataPredicao() {
        return dataPredicao;
    }

    public void setDataPredicao(Date dataPredicao) {
        this.dataPredicao = dataPredicao;
    }

    public Date getDataPredicao30Dias() {
        return dataPredicao30Dias;
    }

    public void setDataPredicao30Dias(Date dataPredicao30Dias) {
        this.dataPredicao30Dias = dataPredicao30Dias;
    }


    public String getNome() {
        return nome.toLowerCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Float getChancesair30dias() {
        return chancesair30dias;
    }

    public void setChancesair30dias(Float chancesair30dias) {

        this.chancesair30dias = chancesair30dias;

        if (chancesair30dias != 0)
            this.apresentarChurnRate = Uteis.arredondarForcando2CasasDecimais(chancesair30dias) + "%";
    }

    public String getStringChancesair30dias() {
        return String.valueOf(chancesair30dias);
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

    public boolean isRegistroSelecionado() {
        return registroSelecionado;
    }

    public void setRegistroSelecionado(boolean registroSelecionado) {
        this.registroSelecionado = registroSelecionado;
    }

    public Date getDataEnvio() {
        return dataEnvio;
    }

    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;

        if (dataEnvio != null)
            this.apresentarDataEnvio = "Enviado - " + Calendario.getData(this.dataEnvio, "dd/MM/yyyy");
    }

    public Date getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(Date dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public Date getVigenteAte() {
        return vigenteAte;
    }

    public void setVigenteAte(Date vigenteAte) {
        this.vigenteAte = vigenteAte;

        if (vigenteAte != null)
            this.disabledCheckBox = Calendario.maior(vigenteAte, Calendario.hoje());
    }

    public String getApresentarDataEnvio() {
        return apresentarDataEnvio;
    }

    public void setApresentarDataEnvio(String apresentarDataEnvio) {
        this.apresentarDataEnvio = apresentarDataEnvio;
    }

    public String getApresentarChurnRate() {
        return apresentarChurnRate;
    }

    public void setApresentarChurnRate(String apresentarChurnRate) {
        this.apresentarChurnRate = apresentarChurnRate;
    }

    public boolean isDisabledCheckBox() {
        return disabledCheckBox;
    }

    public void setDisabledCheckBox(boolean disabledCheckBox) {
        this.disabledCheckBox = disabledCheckBox;
    }
}
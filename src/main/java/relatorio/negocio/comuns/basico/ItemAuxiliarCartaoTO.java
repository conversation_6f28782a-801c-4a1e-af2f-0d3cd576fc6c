package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;

public class ItemAuxiliarCartaoTO extends SuperVO {

    private static final long serialVersionUID = -3709068880974180015L;

    private String matricula;
    private String nome;
    private String titular;
    private ClienteVO clienteVO;

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getTitular() {
        return titular;
    }

    public void setTitular(String titular) {
        this.titular = titular;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }
}

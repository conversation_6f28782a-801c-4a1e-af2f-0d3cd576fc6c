package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.basico.AgendaProfessor;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.SuperVO;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ControleOperacoesRelVO extends SuperVO {

    //atributos usados nos itens do relatorio de BI - Controle de Operações
    private int qtdClientesInativosComPeriodoAcesso;
    private int qtdOperacoesContratoRetroativas;
    private int qtdExclusaoVisitantes;
    private int qtdAlteracaoConsultorContrato;
    private int qtdEstornoContratoAdmin;
    private int qtdEstornoContratoUsuarioComum;
    private int qtdEstornoContratoRecorrencia;
    private int qtdEstornoRecibo;
    private int qtdAlteracoesDataBaseContrato;
    private int qtdAlteracoesDataBasePagamento;
    private int qtdRenegociacaoParcelaRetroativa;
    private List<LogVO> listaLogRenegociacaoParcelasRetroativas;
    private int qtdAlteracoesRecibo;
    private int qtdContratosCancelamento;
    private int qtdParcelasCanceladas;
    private int qtdClientesComBonus;
    private int qtdClientesComFreePass;
    private int qtdClientesComGymPass;
    private int qtdContratosTipoBolsa;
    private int qtdClientesAutorizacaoNaoRenovavel;
    private int qtdClientesAutorizacaoRenovavel;
    private int qtdContratosTransferidosCancelados;
    private double valorDescontos = 0.0;

    private int qtdClientesExcluidos;

    //atributo usado para apresentar itens do relatorio de BI - Controle de Operações
    private boolean apresentarQtdClientesInativosComPeriodoAcesso = false;
    private boolean apresentarQtdOperacoesContratoRetroativas = false;
    private boolean apresentarQtdExclusaoVisitantes = false;
    private boolean apresentarQtdAlteracaoConsultorContrato = false;
    private boolean apresentarQtdEstornoContratoAdmin = false;
    private boolean apresentarQtdEstornoContratoUsuarioComum = false;
    private boolean apresentarQtdEstornoContratoRecorrencia = false;
    private boolean apresentarQtdEstornoRecibo = false;
    private boolean apresentarQtdAlteracoesDataBaseContrato = false;
    private boolean apresentarQtdAlteracoesDataBasePagamento = false;
    private boolean apresentarQtdRenegociacaoParcelaRetroativa = false;
    private boolean apresentarQtdAlteracoesRecibo = false;
    private boolean apresentarQtdContratosCancelamento = false;
    private boolean apresentarQtdClientesComBonus = false;
    private boolean apresentarQtdClientesComFreePass = false;
    private boolean apresentarQtdClientesComGymPass = false;
    private boolean apresentarQtdProfessores = false;
    private boolean apresentarQtdParcelasCanceladas = false;
    private boolean apresentarQtdContratosTipoBolsa = false;
    private boolean apresentarQtdClientesAutorizacaoNaoRenovavel = false;
    private boolean apresentarValorDescontos = false;
    private boolean apresentarQtdClientesAutorizacaoRenovavel = false;
    private List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs;
    private List<AgendaProfessor> listaAgendaProfessorRel;
    private int qtdClientesExcluidosTreinoWeb;
    private boolean apresentarQtdClientesExcluidosTreinoWeb = false;
    private boolean apresentarQtdContratosTransferidosCancelados = false;

    private boolean apresentarQtdClientesExcluidos = false;
    private boolean apresentarAlunosComAgendamento = false;
    private boolean apresentarAlunosAgendamentoExecutado = false;
    private boolean apresentarAlunosAgendamentoConvertido = false;

    public ControleOperacoesRelVO(JSONObject json) {
        this.qtdClientesInativosComPeriodoAcesso = json.optInt("qtdClientesInativosComPeriodoAcesso");
        this.qtdOperacoesContratoRetroativas = json.optInt("qtdOperacoesContratoRetroativas");
        this.qtdExclusaoVisitantes = json.optInt("qtdExclusaoVisitantes");
        this.qtdAlteracaoConsultorContrato = json.optInt("qtdAlteracaoConsultorContrato");
        this.qtdEstornoContratoAdmin = json.optInt("qtdEstornoContratoAdmin");
        this.qtdEstornoContratoUsuarioComum = json.optInt("qtdEstornoContratoUsuarioComum");
        this.qtdEstornoContratoRecorrencia = json.optInt("qtdEstornoContratoRecorrencia");
        this.qtdEstornoRecibo = json.optInt("qtdEstornoRecibo");
        this.qtdAlteracoesDataBaseContrato = json.optInt("qtdAlteracoesDataBaseContrato");
        this.qtdAlteracoesDataBasePagamento = json.optInt("qtdAlteracoesDataBasePagamento");
        this.qtdRenegociacaoParcelaRetroativa = json.optInt("qtdRenegociacaoParcelaRetroativa");
        this.qtdAlteracoesRecibo = json.optInt("qtdAlteracoesRecibo");
        this.qtdContratosCancelamento = json.optInt("qtdContratosCancelamento");
        this.qtdParcelasCanceladas = json.optInt("qtdParcelasCanceladas");
        this.qtdClientesComBonus = json.optInt("qtdClientesComBonus");
        this.qtdClientesComFreePass = json.optInt("qtdClientesComFreePass");
        this.qtdClientesComGymPass = json.optInt("qtdClientesComGymPass");
        this.qtdContratosTipoBolsa = json.optInt("qtdContratosTipoBolsa");
        this.qtdClientesAutorizacaoNaoRenovavel = json.optInt("qtdClientesAutorizacaoNaoRenovavel");
        this.qtdClientesAutorizacaoRenovavel = json.optInt("qtdClientesAutorizacaoRenovavel");
        this.qtdContratosTransferidosCancelados = json.optInt("qtdContratosTransferidosCancelados");
        this.valorDescontos = json.optDouble("valorDescontos");
        this.qtdClientesExcluidos = json.optInt("qtdClientesExcluidos");
    }
    public ControleOperacoesRelVO() {
        inicializarDados();
    }

    public void inicializarDados() {
        setListaPendenciaResumoPessoaRelVOs(new ArrayList<PendenciaResumoPessoaRelVO>());
    }

    public List<PendenciaResumoPessoaRelVO> getListaPendenciaResumoPessoaRelVOs() {
        return listaPendenciaResumoPessoaRelVOs;
    }

    public void setListaPendenciaResumoPessoaRelVOs(List<PendenciaResumoPessoaRelVO> listaPendenciaResumoPessoaRelVOs) {
        this.listaPendenciaResumoPessoaRelVOs = listaPendenciaResumoPessoaRelVOs;
    }

    public List<AgendaProfessor> getListaAgendaProfessorRel() {
        return listaAgendaProfessorRel;
    }

    public void setListaAgendaProfessorRel(List<AgendaProfessor> listaAgendaProfessorRel) {
        this.listaAgendaProfessorRel = listaAgendaProfessorRel;
    }

    public int getQtdClientesInativosComPeriodoAcesso() {
        return qtdClientesInativosComPeriodoAcesso;
    }

    public void setQtdClientesInativosComPeriodoAcesso(int qtdClientesInativosComPeriodoAcesso) {
        this.qtdClientesInativosComPeriodoAcesso = qtdClientesInativosComPeriodoAcesso;
    }

    public boolean isApresentarQtdClientesInativosComPeriodoAcesso() {
        return apresentarQtdClientesInativosComPeriodoAcesso;
    }

    public void setApresentarQtdClientesInativosComPeriodoAcesso(boolean apresentarQtdClientesInativosComPeriodoAcesso) {
        this.apresentarQtdClientesInativosComPeriodoAcesso = apresentarQtdClientesInativosComPeriodoAcesso;
    }

    public int getQtdOperacoesContratoRetroativas() {
        return qtdOperacoesContratoRetroativas;
    }

    public void setQtdOperacoesContratoRetroativas(int qtdOperacoesContratoRetroativas) {
        this.qtdOperacoesContratoRetroativas = qtdOperacoesContratoRetroativas;
    }

    public boolean isApresentarQtdOperacoesContratoRetroativas() {
        return apresentarQtdOperacoesContratoRetroativas;
    }

    public void setApresentarQtdOperacoesContratoRetroativas(boolean apresentarQtdOperacoesContratoRetroativas) {
        this.apresentarQtdOperacoesContratoRetroativas = apresentarQtdOperacoesContratoRetroativas;
    }

    public int getQtdExclusaoVisitantes() {
        return qtdExclusaoVisitantes;
    }

    public void setQtdExclusaoVisitantes(int qtdExclusaoVisitantes) {
        this.qtdExclusaoVisitantes = qtdExclusaoVisitantes;
    }

    public boolean isApresentarQtdExclusaoVisitantes() {
        return apresentarQtdExclusaoVisitantes;
    }

    public void setApresentarQtdExclusaoVisitantes(boolean apresentarQtdExclusaoVisitantes) {
        this.apresentarQtdExclusaoVisitantes = apresentarQtdExclusaoVisitantes;
    }

    public int getQtdEstornoContratoAdmin() {
        return qtdEstornoContratoAdmin;
    }

    public void setQtdEstornoContratoAdmin(int qtdEstornoContratoAdmin) {
        this.qtdEstornoContratoAdmin = qtdEstornoContratoAdmin;
    }

    public boolean isApresentarQtdEstornoContratoAdmin() {
        return apresentarQtdEstornoContratoAdmin;
    }

    public void setApresentarQtdEstornoContratoAdmin(boolean apresentarQtdEstornoContratoAdmin) {
        this.apresentarQtdEstornoContratoAdmin = apresentarQtdEstornoContratoAdmin;
    }

    public int getQtdEstornoContratoUsuarioComum() {
        return qtdEstornoContratoUsuarioComum;
    }

    public void setQtdEstornoContratoUsuarioComum(int qtdEstornoContratoUsuarioComum) {
        this.qtdEstornoContratoUsuarioComum = qtdEstornoContratoUsuarioComum;
    }

    public boolean isApresentarQtdEstornoContratoUsuarioComum() {
        return apresentarQtdEstornoContratoUsuarioComum;
    }

    public void setApresentarQtdEstornoContratoUsuarioComum(boolean apresentarQtdEstornoContratoUsuarioComum) {
        this.apresentarQtdEstornoContratoUsuarioComum = apresentarQtdEstornoContratoUsuarioComum;
    }

    public int getQtdAlteracoesDataBaseContrato() {
        return qtdAlteracoesDataBaseContrato;
    }

    public void setQtdAlteracoesDataBaseContrato(int qtdAlteracoesDataBaseContrato) {
        this.qtdAlteracoesDataBaseContrato = qtdAlteracoesDataBaseContrato;
    }

    public boolean isApresentarQtdAlteracoesDataBaseContrato() {
        return apresentarQtdAlteracoesDataBaseContrato;
    }

    public void setApresentarQtdAlteracoesDataBaseContrato(boolean apresentarQtdAlteracoesDataBaseContrato) {
        this.apresentarQtdAlteracoesDataBaseContrato = apresentarQtdAlteracoesDataBaseContrato;
    }

    public int getQtdAlteracoesDataBasePagamento() {
        return qtdAlteracoesDataBasePagamento;
    }

    public void setQtdAlteracoesDataBasePagamento(int qtdAlteracoesDataBasePagamento) {
        this.qtdAlteracoesDataBasePagamento = qtdAlteracoesDataBasePagamento;
    }

    public boolean isApresentarQtdAlteracoesDataBasePagamento() {
        return apresentarQtdAlteracoesDataBasePagamento;
    }

    public void setApresentarQtdAlteracoesDataBasePagamento(boolean apresentarQtdAlteracoesDataBasePagamento) {
        this.apresentarQtdAlteracoesDataBasePagamento = apresentarQtdAlteracoesDataBasePagamento;
    }

    public int getQtdAlteracoesRecibo() {
        return qtdAlteracoesRecibo;
    }

    public void setQtdAlteracoesRecibo(int qtdAlteracoesRecibo) {
        this.qtdAlteracoesRecibo = qtdAlteracoesRecibo;
    }

    public boolean isApresentarQtdAlteracoesRecibo() {
        return apresentarQtdAlteracoesRecibo;
    }

    public void setApresentarQtdAlteracoesRecibo(boolean apresentarQtdAlteracoesRecibo) {
        this.apresentarQtdAlteracoesRecibo = apresentarQtdAlteracoesRecibo;
    }

    public int getQtdContratosCancelamento() {
        return qtdContratosCancelamento;
    }

    public void setQtdContratosCancelamento(int qtdContratosCancelamento) {
        this.qtdContratosCancelamento = qtdContratosCancelamento;
    }

    public boolean isApresentarQtdContratosCancelamento() {
        return apresentarQtdContratosCancelamento;
    }

    public void setApresentarQtdContratosCancelamento(boolean apresentarQtdContratosCancelamento) {
        this.apresentarQtdContratosCancelamento = apresentarQtdContratosCancelamento;
    }

    public int getQtdClientesComBonus() {
        return qtdClientesComBonus;
    }

    public void setQtdClientesComBonus(int qtdClientesComBonus) {
        this.qtdClientesComBonus = qtdClientesComBonus;
    }

    public boolean isApresentarQtdClientesComBonus() {
        return apresentarQtdClientesComBonus;
    }

    public void setApresentarQtdClientesComBonus(boolean apresentarQtdClientesComBonus) {
        this.apresentarQtdClientesComBonus = apresentarQtdClientesComBonus;
    }

    public boolean isApresentarQtdClientesComFreePass() {
        return apresentarQtdClientesComFreePass;
    }

    public void setApresentarQtdClientesComFreePass(boolean apresentarQtdClientesComFreePass) {
        this.apresentarQtdClientesComFreePass = apresentarQtdClientesComFreePass;
    }

    public int getQtdClientesComFreePass() {
        return qtdClientesComFreePass;
    }

    public void setQtdClientesComFreePass(int qtdClientesComFreePass) {
        this.qtdClientesComFreePass = qtdClientesComFreePass;
    }

    public boolean isApresentarQtdAlteracaoConsultorContrato() {
        return apresentarQtdAlteracaoConsultorContrato;
    }

    public void setApresentarQtdAlteracaoConsultorContrato(boolean apresentarQtdAlteracaoConsultorContrato) {
        this.apresentarQtdAlteracaoConsultorContrato = apresentarQtdAlteracaoConsultorContrato;
    }

    public int getQtdAlteracaoConsultorContrato() {
        return qtdAlteracaoConsultorContrato;
    }

    public void setQtdAlteracaoConsultorContrato(int qtdAlteracaoConsultorContrato) {
        this.qtdAlteracaoConsultorContrato = qtdAlteracaoConsultorContrato;
    }

    public int getQtdEstornoContratoRecorrencia() {
        return qtdEstornoContratoRecorrencia;
    }

    public void setQtdEstornoContratoRecorrencia(int qtdEstornoContratoRecorrencia) {
        this.qtdEstornoContratoRecorrencia = qtdEstornoContratoRecorrencia;
    }

    public boolean isApresentarQtdEstornoContratoRecorrencia() {
        return apresentarQtdEstornoContratoRecorrencia;
    }

    public void setApresentarQtdEstornoContratoRecorrencia(boolean apresentarQtdEstornoContratoRecorrencia) {
        this.apresentarQtdEstornoContratoRecorrencia = apresentarQtdEstornoContratoRecorrencia;
    }

    public int getQtdEstornoRecibo() {
        return qtdEstornoRecibo;
    }

    public boolean isApresentarQtdEstornoRecibo() {
        return apresentarQtdEstornoRecibo;
    }

    public void setQtdEstornoRecibo(int qtdEstornoRecibo) {
        this.qtdEstornoRecibo = qtdEstornoRecibo;
    }

    public void setApresentarQtdEstornoRecibo(boolean apresentarQtdEstornoRecibo) {
        this.apresentarQtdEstornoRecibo = apresentarQtdEstornoRecibo;
    }

    public boolean isApresentarQtdParcelasCanceladas() {
        return apresentarQtdParcelasCanceladas;
    }

    public void setApresentarQtdParcelasCanceladas(boolean apresentarQtdParcelasCanceladas) {
        this.apresentarQtdParcelasCanceladas = apresentarQtdParcelasCanceladas;
    }

    public int getQtdParcelasCanceladas() {
        return qtdParcelasCanceladas;
    }

    public void setQtdParcelasCanceladas(int qtdParcelasCanceladas) {
        this.qtdParcelasCanceladas = qtdParcelasCanceladas;
    }

    public String getAtributos() {
        if (isApresentarQtdClientesComFreePass()) {
            return "matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,qtdFreePass=Dias de FreePass,produtoDescricao=FreePass,dataRegistro=Data Lançamento,responsavelOperacao_Apresentar=Responsavel";
        } else if(isApresentarQtdOperacoesContratoRetroativas()){
            return "matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,responsavelOperacao_Apresentar=Responsavel,codigoContratoOperacao_Apresentar=Contrato,descricaoOperacao_Apresentar=Operação,dataRegistro=Data Lançamento,dataInicioOperacao=Data Início,dataFinalOperacao=Data Final";
        } else if(isApresentarQtdProfessores()){
            return "nome=Professor Aula Experimental,icv=ICV Aulas Experimentais";
        } else if (isApresentarQtdClientesComGymPass()) {
            return "matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação,dataRegistro=Data Lançamento,token=Token GymPass,responsavelOperacao_Apresentar=Responsavel";
        } else {
            return "matricula_Apresentar=Matrícula,nome_Apresentar=Nome,situacao_Apresentar=Situação";
        }
    }

    public boolean isApresentarQtdClientesComGymPass() {
        return apresentarQtdClientesComGymPass;
    }

    public void setApresentarQtdClientesComGymPass(boolean apresentarQtdClientesComGymPass) {
        this.apresentarQtdClientesComGymPass = apresentarQtdClientesComGymPass;
    }

    public boolean isApresentarQtdProfessores() {
        return apresentarQtdProfessores;
    }

    public void setApresentarQtdProfessores(boolean apresentarQtdProfessores) {
        this.apresentarQtdProfessores = apresentarQtdProfessores;
    }

    public int getQtdClientesComGymPass() {
        return qtdClientesComGymPass;
    }

    public void setQtdClientesComGymPass(int qtdClientesComGymPass) {
        this.qtdClientesComGymPass = qtdClientesComGymPass;
    }

    public int getQtdContratosTipoBolsa() {
        return qtdContratosTipoBolsa;
    }

    public void setQtdContratosTipoBolsa(int qtdContratosTipoBolsa) {
        this.qtdContratosTipoBolsa = qtdContratosTipoBolsa;
    }

    public boolean isApresentarQtdContratosTipoBolsa() {
        return apresentarQtdContratosTipoBolsa;
    }

    public void setApresentarQtdContratosTipoBolsa(boolean apresentarQtdContratosTipoBolsa) {
        this.apresentarQtdContratosTipoBolsa = apresentarQtdContratosTipoBolsa;
    }

    public boolean isApresentarQtdClientesAutorizacaoNaoRenovavel() {
        return apresentarQtdClientesAutorizacaoNaoRenovavel;
    }

    public void setApresentarQtdClientesAutorizacaoNaoRenovavel(boolean apresentarQtdClientesAutorizacaoNaoRenovavel) {
        this.apresentarQtdClientesAutorizacaoNaoRenovavel = apresentarQtdClientesAutorizacaoNaoRenovavel;
    }

    public boolean isApresentarQtdClientesAutorizacaoRenovavel() {
        return apresentarQtdClientesAutorizacaoRenovavel;
    }

    public void setApresentarQtdClientesAutorizacaoRenovavel(boolean apresentarQtdClientesAutorizacaoRenovavel) {
        this.apresentarQtdClientesAutorizacaoRenovavel = apresentarQtdClientesAutorizacaoRenovavel;
    }

    public int getQtdClientesAutorizacaoNaoRenovavel() {
        return qtdClientesAutorizacaoNaoRenovavel;
    }

    public void setQtdClientesAutorizacaoNaoRenovavel(int qtdClientesAutorizacaoNaoRenovavel) {
        this.qtdClientesAutorizacaoNaoRenovavel = qtdClientesAutorizacaoNaoRenovavel;
    }

    public int getQtdClientesAutorizacaoRenovavel() {
        return qtdClientesAutorizacaoRenovavel;
    }

    public void setQtdClientesAutorizacaoRenovavel(int qtdClientesAutorizacaoRenovavel) {
        this.qtdClientesAutorizacaoRenovavel = qtdClientesAutorizacaoRenovavel;
    }

    public double getValorDescontos() {
        return valorDescontos;
    }

    public String getValorDescontosApresentar(){
        return Formatador.formatarValorMonetarioSemMoeda(this.getValorDescontos());
    }

    public void setValorDescontos(double valorDescontos) {
        this.valorDescontos = valorDescontos;
    }

    public boolean isApresentarValorDescontos() {
        return apresentarValorDescontos;
    }

    public void setApresentarValorDescontos(boolean apresentarValorDescontos) {
        this.apresentarValorDescontos = apresentarValorDescontos;
    }

    public int getQtdRenegociacaoParcelaRetroativa() {
        return qtdRenegociacaoParcelaRetroativa;
    }

    public void setQtdRenegociacaoParcelaRetroativa(int qtdRenegociacaoParcelaRetroativa) {
        this.qtdRenegociacaoParcelaRetroativa = qtdRenegociacaoParcelaRetroativa;
    }

    public boolean isApresentarQtdRenegociacaoParcelaRetroativa() {
        return apresentarQtdRenegociacaoParcelaRetroativa;
    }

    public void setApresentarQtdRenegociacaoParcelaRetroativa(boolean apresentarQtdRenegociacaoParcelaRetroativa) {
        this.apresentarQtdRenegociacaoParcelaRetroativa = apresentarQtdRenegociacaoParcelaRetroativa;
    }

    public List<LogVO> getListaLogRenegociacaoParcelasRetroativas() {
        return listaLogRenegociacaoParcelasRetroativas;
    }

    public void setListaLogRenegociacaoParcelasRetroativas(List<LogVO> listaLogRenegociacaoParcelasRetroativas) {
        this.listaLogRenegociacaoParcelasRetroativas = listaLogRenegociacaoParcelasRetroativas;
    }

    public int getQtdClientesExcluidosTreinoWeb() {
        return qtdClientesExcluidosTreinoWeb;
    }

    public void setQtdClientesExcluidosTreinoWeb(int qtdClientesExcluidosTreinoWeb) {
        this.qtdClientesExcluidosTreinoWeb = qtdClientesExcluidosTreinoWeb;
    }

    public boolean isApresentarQtdClientesExcluidosTreinoWeb() {
        return apresentarQtdClientesExcluidosTreinoWeb;
    }

    public void setApresentarQtdClientesExcluidosTreinoWeb(boolean apresentarQtdClientesExcluidosTreinoWeb) {
        this.apresentarQtdClientesExcluidosTreinoWeb = apresentarQtdClientesExcluidosTreinoWeb;
    }

    public int getQtdContratosTransferidosCancelados() {
        return qtdContratosTransferidosCancelados;
    }

    public void setQtdContratosTransferidosCancelados(int qtdContratosTransferidosCancelados) {
        this.qtdContratosTransferidosCancelados = qtdContratosTransferidosCancelados;
    }

    public boolean isApresentarQtdContratosTransferidosCancelados() {
        return apresentarQtdContratosTransferidosCancelados;
    }

    public void setApresentarQtdContratosTransferidosCancelados(boolean apresentarQtdContratosTransferidosCancelados) {
        this.apresentarQtdContratosTransferidosCancelados = apresentarQtdContratosTransferidosCancelados;
    }

    public int getQtdClientesExcluidos() {
        return qtdClientesExcluidos;
    }

    public void setQtdClientesExcluidos(int qtdClientesExcluidos) {
        this.qtdClientesExcluidos = qtdClientesExcluidos;
    }

    public boolean isApresentarQtdClientesExcluidos() {
        return apresentarQtdClientesExcluidos;
    }

    public void setApresentarQtdClientesExcluidos(boolean apresentarQtdClientesExcluidos) {
        this.apresentarQtdClientesExcluidos = apresentarQtdClientesExcluidos;
    }

    public String getItemExportar() {
        if(this.apresentarQtdClientesInativosComPeriodoAcesso){
            return ItemExportacaoEnum.BI_EXCECOES_INATIVOS_COM_PERIODOACESSO.getId();
        }
        if(this.apresentarQtdOperacoesContratoRetroativas){
            return ItemExportacaoEnum.BI_EXCECOES_OPERACOES_RETROATIVAS.getId();
        }
        if(this.apresentarQtdExclusaoVisitantes){
            return ItemExportacaoEnum.BI_EXCECOES_EXCLUSAO_VISITANTES.getId();
        }
        if(this.apresentarQtdAlteracaoConsultorContrato){
            return ItemExportacaoEnum.BI_EXCECOES_ALTERACAO_CONSULTOR.getId();
        }
        if(this.apresentarQtdEstornoContratoAdmin){
            return ItemExportacaoEnum.BI_EXCECOES_ESTORNOCONTRATO_ADMIN.getId();
        }
        if(this.apresentarQtdEstornoContratoUsuarioComum){
            return ItemExportacaoEnum.BI_EXCECOES_ESTORNOCONTRATO_USUARIOCOMUM.getId();
        }
        if(this.apresentarQtdEstornoContratoRecorrencia){
            return ItemExportacaoEnum.BI_EXCECOES_ESTORNOCONTRATO_RECORRENCIA.getId();
        }
        if(this.apresentarQtdEstornoRecibo){
            return ItemExportacaoEnum.BI_EXCECOES_ESTORNO_RECIBO.getId();
        }
        if(this.apresentarQtdAlteracoesDataBaseContrato){
            return ItemExportacaoEnum.BI_EXCECOES_DATABASE_CONTRATO.getId();
        }
        if(this.apresentarQtdAlteracoesDataBasePagamento){
            return ItemExportacaoEnum.BI_EXCECOES_DATABASE_PAGAMENTO.getId();
        }
        if(this.apresentarQtdRenegociacaoParcelaRetroativa){
            return ItemExportacaoEnum.BI_EXCECOES_RENEGOCIACAO_PARCELA_RETROATIVA.getId();
        }
        if(this.apresentarQtdAlteracoesRecibo){
            return ItemExportacaoEnum.BI_EXCECOES_ALTERACOES_RECIBO.getId();
        }
        if(this.apresentarQtdContratosCancelamento){
            return ItemExportacaoEnum.BI_EXCECOES_CONTRATOS_CANCELAMENTO.getId();
        }
        if(this.apresentarQtdClientesComBonus){
            return ItemExportacaoEnum.BI_EXCECOES_CLIENTES_BONUS.getId();
        }
        if(this.apresentarQtdClientesComFreePass){
            return ItemExportacaoEnum.BI_EXCECOES_CLIENTES_FREEPASS.getId();
        }
        if(this.apresentarQtdClientesComGymPass){
            return ItemExportacaoEnum.BI_EXCECOES_CLIENTES_GYMPASS.getId();
        }
        if(this.apresentarQtdParcelasCanceladas){
            return ItemExportacaoEnum.BI_EXCECOES_PARCELAS_CANCELADAS.getId();
        }
        if(this.apresentarQtdContratosTipoBolsa){
            return ItemExportacaoEnum.BI_EXCECOES_CONTRATOS_BOLSA.getId();
        }
        if(this.apresentarQtdClientesAutorizacaoNaoRenovavel){
            return ItemExportacaoEnum.BI_EXCECOES_CLIENTES_AUTORIZACAO_NAORENOVAVEL.getId();
        }
        if(this.apresentarValorDescontos){
            return ItemExportacaoEnum.BI_EXCECOES_VALORDESCONTOS.getId();
        }
        if(this.apresentarQtdClientesAutorizacaoRenovavel){
            return ItemExportacaoEnum.BI_EXCECOES_CLIENTES_AUTORIZACAO_RENOVAVEL.getId();
        }
        if(this.apresentarQtdClientesExcluidosTreinoWeb){
            return ItemExportacaoEnum.BI_EXCECOES_EXCLUIDOS_TREINOWEB.getId();
        }
        if(this.apresentarQtdContratosTransferidosCancelados){
            return ItemExportacaoEnum.BI_EXCECOES_CONTRATOS_TRANSFERIDOSCANCELADOS.getId();
        }
        if(this.apresentarQtdClientesExcluidos){
            return ItemExportacaoEnum.BI_EXCECOES_CLIENTES_EXCLUIDOS.getId();
        }
        if(this.apresentarAlunosAgendamentoExecutado){
            return ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_EXECUTADOS.getId();
        }
        if(this.apresentarAlunosComAgendamento){
            return ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_AGENDADOS.getId();
        }
        if(this.apresentarAlunosAgendamentoConvertido){
            return ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_CONVERTIDOS.getId();
        }
        if(this.apresentarQtdProfessores){
            return ItemExportacaoEnum.BI_AULA_EXPERIMENTAL_ICV.getId();
        }

        return "";
    }

    public boolean isApresentarAlunosComAgendamento() {
        return apresentarAlunosComAgendamento;
    }

    public void setApresentarAlunosComAgendamento(boolean apresentarAlunosComAgendamento) {
        this.apresentarAlunosComAgendamento = apresentarAlunosComAgendamento;
    }

    public boolean isApresentarAlunosAgendamentoExecutado() {
        return apresentarAlunosAgendamentoExecutado;
    }

    public void setApresentarAlunosAgendamentoExecutado(boolean apresentarAlunosAgendamentoExecutado) {
        this.apresentarAlunosAgendamentoExecutado = apresentarAlunosAgendamentoExecutado;
    }

    public boolean isApresentarAlunosAgendamentoConvertido() {
        return apresentarAlunosAgendamentoConvertido;
    }

    public void setApresentarAlunosAgendamentoConvertido(boolean apresentarAlunosAgendamentoConvertido) {
        this.apresentarAlunosAgendamentoConvertido = apresentarAlunosAgendamentoConvertido;
    }
}

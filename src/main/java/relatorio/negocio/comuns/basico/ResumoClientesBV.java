/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.QuestionarioClienteVO;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class ResumoClientesBV extends SuperTO {

    private String situacaoContrato;

    private QuestionarioClienteVO questionarioClienteVO;

    public Date getData_Apresentar() {
        return getQuestionarioClienteVO().getData();
    }

    public String getMatricula_Apresentar() {
        return getQuestionarioClienteVO().getCliente().getMatricula();
    }

    public String getNome_Apresentar() {
        return getQuestionarioClienteVO().getCliente().getPessoa().getNome();
    }

    public String getQuestionario_Apresentar() {
        return getQuestionarioClienteVO().getQuestionario().getTituloPesquisa();
    }

    public QuestionarioClienteVO getQuestionarioClienteVO() {
        return questionarioClienteVO;
    }

    public void setQuestionarioClienteVO(QuestionarioClienteVO questionarioClienteVO) {
        this.questionarioClienteVO = questionarioClienteVO;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }
}

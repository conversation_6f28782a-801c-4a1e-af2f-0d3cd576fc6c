package relatorio.negocio.comuns.basico;

import br.com.pactosolucoes.estudio.enumeradores.DiaDaSemanaEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ItemRelatorioTO extends SuperVO {
    private static final long serialVersionUID = -3709068880974180015L;

    private Integer codigoEmpresa;
    protected ClienteVO clienteVO;
    private Integer codigoMes;
    private String nomeEmpresa = "";
    //dados cadastrais
    private Integer cliCodigo = 0;
    private String cnpj;
    private String matricula = "";
    private String nome = "";
    private String ddd = "";
    private String telefone = "";
    private String email = "";
    private String cpf = "";
    private String cpfResponsavel = "";
    private String rne = "";
    private String passaporte = "";
    private Date dataCadastro;
    private Date dataNascimento;
    private String situacaoCliente = "";
    private String situacaoClienteApr = "";
    private String situacaoContrato = "";
    private String situacao = "";
    private String sexo = "";
    private Integer duracao = null;
    private Integer convenio = null;
    private String categoria = "";
    private String modalidades = "";
    //endereco
    private String logradouro = "";
    private String numero;
    private String bairro = "";
    private String cidade = "";
    private String estado;
    private String pais;
    private String cep = "";
    private String complemento = "";
    // inf. de plano
    private Date dataMatricula;
    private Date inicioPlano;
    private Date vencimentoPlano;
    private Integer ultimoAcesso;
    private String plano = "";
    private String nomeRespContrato = "";
    //
    private List<ColaboradorVO> consultores = new ArrayList<ColaboradorVO>();
    private boolean consultoresAtivos;
    private boolean consultoresInativos;
    private String consultor = "";
    private List<ColaboradorVO> professores = new ArrayList<ColaboradorVO>();
    private boolean professoresAtivos;
    private boolean professoresInativos;
    private String professor = "";
    //RelatorioVisitantes
    private Integer tipoConsulta = 1;
    private String bvCompIncomp;
    //Relatorio A.Cancelados
    private Date dataUltimoAcesso;
    private Date dataOperacao;
    //Relatorio A.Atestado
    private Date dataAtestado;
    //Relatorio Bônus
    private String bonus;

    private String justificativa = "";
    private String observacao = "";

    private String nomeConsultor = "";
    private String nomeProfessores = "";

    private Date dataInicioOperacao;
    private Date dataFimOperacao;
    private String rg;
    private String rgOrgao;
    private String rgUf;
    private String professorTreino;

    //DESEMPENHO MENSAL CONSULTOR
    private Integer mes;
    private String vezesSemana;
    private Integer ano;
    private String mesAno;
    private Integer meta;
    private Integer metaAtingida;
    private Integer repescagem;
    private String porcentagem;
    private Integer codUsuario;
    private Integer codColaborador;
    private String estadoCivil ="";
    private String profissao = "";

    private String tipoCliente = "";

    private Integer grupoRisco;
    private String  grupoDesconto;
    private String pacote = "";

    //Pesquisa NPS
    private String nomeCliente;
    private String quantidade;
    private Date dataLancamento;
    private String produto;
    private String mensagemVendasOnline;
    private String evento;
    private String parq;

    private String sentido;
    private String localAcesso;
    private String coletor;
    private String meioIdentificacaoEntrada;
    private String meioIdentificacaoSaida;

    private String habilitacaoSesc;

    private String validadeCartaoSesc;
    private String valorContrato;
    private String valorPago;
    private String valorUtilizado;
    private String valorDevolvido;
    private String descricaoCancelamento;

    public int getIdade() {
        return idade;
    }

    public void setIdade(int idade) {
        this.idade = idade;
    }

    private int idade;

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getRgOrgao() {
        return rgOrgao;
    }

    public void setRgOrgao(String rgOrgao) {
        this.rgOrgao = rgOrgao;
    }

    public String getRgUf() {
        return rgUf;
    }

    public void setRgUf(String rgUf) {
        this.rgUf = rgUf;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public Integer getTipoConsulta() {
        return tipoConsulta;
    }

    public void setTipoConsulta(Integer tipoConsulta) {
        this.tipoConsulta = tipoConsulta;
    }

    public List<ColaboradorVO> getProfessores() {
        return professores;
    }

    public void setProfessores(List<ColaboradorVO> professores) {
        this.professores = professores;
    }

    public boolean isProfessoresAtivos() {
        return professoresAtivos;
    }

    public void setProfessoresAtivos(boolean professoresAtivos) {
        this.professoresAtivos = professoresAtivos;
    }

    public boolean isProfessoresInativos() {
        return professoresInativos;
    }

    public void setProfessoresInativos(boolean professoresInativos) {
        this.professoresInativos = professoresInativos;
    }

    public List<ColaboradorVO> getConsultores() {
        return consultores;
    }

    public void setConsultores(List<ColaboradorVO> consultores) {
        this.consultores = consultores;
    }

    public boolean isConsultoresAtivos() {
        return consultoresAtivos;
    }

    public void setConsultoresAtivos(boolean consultoresAtivos) {
        this.consultoresAtivos = consultoresAtivos;
    }

    public boolean isConsultoresInativos() {
        return consultoresInativos;
    }

    public void setConsultoresInativos(boolean consultoresInativos) {
        this.consultoresInativos = consultoresInativos;
    }

    public Integer getCodigoMes() {
        return codigoMes;
    }

    public void setCodigoMes(Integer codigoMes) {
        this.codigoMes = codigoMes;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDdd() {
        return ddd;
    }

    public void setDdd(String ddd) {
        this.ddd = ddd;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public String getSituacaoCliente() {
        if (situacaoCliente == null) {
            return "";
        }
        if (situacaoCliente.equals("AT")) {
            return ("Ativo");
        }
        if (situacaoCliente.equals("IN")) {
            return ("Inativo");
        }
        if (situacaoCliente.equals("VI")) {
            return ("Visitante");
        }
        if (situacaoCliente.equals("TR")) {
            return ("Trancado");
        }
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {

        this.situacaoCliente = situacaoCliente;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getSexo() {
        if (sexo == null) {
            return "";
        }
        if (sexo.equals("M")) {
            return "Masculino";
        }
        if (sexo.equals("F")) {
            return "Feminino";
        }
        return (sexo);
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getLogradouro() {
        return logradouro;
    }

    public void setLogradouro(String logradouro) {
        this.logradouro = logradouro;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getPais() {
        return pais;
    }

    public void setPais(String pais) {
        this.pais = pais;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public Date getDataMatricula() {
        return dataMatricula;
    }

    public void setDataMatricula(Date dataMatricula) {
        this.dataMatricula = dataMatricula;
    }

    public Date getInicioPlano() {
        return inicioPlano;
    }

    public void setInicioPlano(Date inicioPlano) {
        this.inicioPlano = inicioPlano;
    }

    public Date getVencimentoPlano() {
        return vencimentoPlano;
    }

    public void setVencimentoPlano(Date vencimentoPlano) {
        this.vencimentoPlano = vencimentoPlano;
    }

    public Integer getUltimoAcesso() {
        return ultimoAcesso;
    }

    public void setUltimoAcesso(Integer ultimoAcesso) {
        this.ultimoAcesso = ultimoAcesso;
    }

    public String getPlano() {
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getDataNascimentoApresentar() {
        if (dataNascimento == null) {
            return "";
        }
        return Uteis.getData(dataNascimento);
    }

    public String getDataOperacaoApresentar() {
        if (dataOperacao == null) {
            return "";
        }
        return Uteis.getData(dataOperacao);
    }

    public String getDataCadastroApresentar() {
        if (dataCadastro == null) {
            return "";
        }
        return Uteis.getData(dataCadastro);
    }

    public String getDataHoraCadastroApresentar(){
        if (dataCadastro == null) {
            return "";
        }

        return Uteis.getDataComHora(dataCadastro);
    }

    public String getDataUltimoAcessoApresentar() {
        if (dataUltimoAcesso == null) {
            return "";
        }
        return Uteis.getData(dataUltimoAcesso);
    }

    public String getDataHoraUltimoAcessoApresentar() {
        if (dataUltimoAcesso == null) {
            return "";
        }
        return Uteis.getDataComHora(dataUltimoAcesso);
    }

    public String getDataInicioPlanoApresentar() {
        if (inicioPlano == null) {
            return "";
        }
        return Uteis.getData(inicioPlano);
    }

    public String getDataVencimentoPlanoApresentar() {
        if (vencimentoPlano == null) {
            return "";
        }
        return Uteis.getData(vencimentoPlano);
    }

    public String getDataMatriculaApresentar() {
        if (dataMatricula == null) {
            return "";
        }
        return Uteis.getData(dataMatricula);
    }

    public String getDataAtestadoApresentar() {
        if (dataAtestado == null) {
            return "";
        }
        return Uteis.getData(dataAtestado);
    }

    public String getDataInicioOperacaoApresentar() {
        if (dataInicioOperacao == null) {
            return "";
        }
        return Uteis.getData(dataInicioOperacao);
    }

    public String getDataHoraSemanaInicioOperacaoApresentar() {
        if (dataInicioOperacao == null) {
            return "";
        }
        return Uteis.getDataComHora(dataInicioOperacao) + " - " + Uteis.retornaDescricaoDiaSemana(dataInicioOperacao);
    }

    public String getDataFimOperacaoApresentar() {
        if (dataFimOperacao == null) {
            return "";
        }
        return Uteis.getData(dataFimOperacao);
    }

    public String getDataHoraSemanaFimOperacaoApresentar() {
        if (dataFimOperacao == null) {
            return "";
        }
        return Uteis.getDataComHora(dataFimOperacao) + " - " + Uteis.retornaDescricaoDiaSemana(dataFimOperacao);
    }

    public String getBvCompIncomp() {
        return bvCompIncomp;
    }

    public void setBvCompIncomp(String bvCompIncomp) {
        this.bvCompIncomp = bvCompIncomp;

    }

    public Date getDataUltimoAcesso() {
        return dataUltimoAcesso;
    }

    public void setDataUltimoAcesso(Date dataUltimoAcesso) {
        this.dataUltimoAcesso = dataUltimoAcesso;
    }

    public Date getDataAtestado() {
        return dataAtestado;
    }

    public void setDataAtestado(Date dataAtestado) {
        this.dataAtestado = dataAtestado;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public String getProfessor() {
        return professor;
    }

    public void setProfessor(String professor) {
        this.professor = professor;
    }

    public Integer getCliCodigo() {
        return cliCodigo;
    }

    public void setCliCodigo(Integer cliCodigo) {
        this.cliCodigo = cliCodigo;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Date getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(Date dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public void setBonus(String bonus) {
        this.bonus = bonus;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public String getDuracaoApresentar(){
        if(duracao > 0)
            return Integer.toString(duracao);
        else
            return "";
    }

    public String getSituacaoApresentar() {
        StringBuilder st = new StringBuilder();
        if (situacaoContrato == null) {
            st.append(" ");
        }else {
            if (situacaoContrato.equals("AV")) {
                st.append("A Vencer");
            }
            if (situacaoContrato.equals("CA")) {
                st.append("Cancelado");
            }
            if (situacaoContrato.equals("VE")) {
                st.append("Vencido");
            }
            if (situacaoContrato.equals("DE")) {
                st.append("Desistente");
            }
            if (situacaoContrato.equals("AE")) {
                st.append("Atestado");
            }
            if (situacaoContrato.equals("CR")) {
                st.append("Férias");
            }
        }
        if (situacaoCliente == null) {
            st.append(" ");
        }else{
            if (situacaoCliente.equals("AT")) {
                st.append("Ativo");
            }
            if (situacaoCliente.equals("IN")) {
                st.append("Inativo");
            }
            if (situacaoCliente.equals("VI")) {
                st.append("Visitante");
            }
            if (situacaoCliente.equals("TR")) {
                st.append("Trancado");
            }
        }
        if (situacao == null) {
            st.append(" ");
        }else{
            if (situacao.equals("AT")) {
                st.append("Atestado");
            }
        }
        if (bonus == null) {
            st.append(" ");
        }else{
            if (bonus.equals("BA")) {
                st.append("Bônus de Acréscimo");
            } else if (bonus.equals("BR")) {
                st.append("Bônus de Redução");
            }
        }
        return st.toString();
    }

    public String getSituacaoClienteApresentar() {
        if (situacaoContrato == null) {
            return (" ");
        }
        if (situacaoContrato.equals("AV")) {
            return ("A Vencer");
        }
        if (situacaoContrato.equals("CA")) {
            return ("Cancelado");
        }
        if (situacaoContrato.equals("VE")) {
            return ("Vencido");
        }
        if (situacaoContrato.equals("DE")) {
            return ("Desistente");
        }
        if (situacaoContrato.equals("AE")) {
            return ("Atestado");
        }
        if (situacaoContrato.equals("CR")) {
            return ("Férias");
        }

        if (situacaoCliente == null) {
            return (" ");
        }
        if (situacaoCliente.equals("AT")) {
            return ("Ativo");
        }
        if (situacaoCliente.equals("IN")) {
            return ("Inativo");
        }
        if (situacaoCliente.equals("VI")) {
            return ("Visitante");
        }
        if (situacaoCliente.equals("TR")) {
            return ("Trancado");
        }
        if (situacao == null) {
            return (" ");
        }
        if (situacao.equals("AT")) {
            return ("Atestado");
        }
        if (bonus == null) {
            return "";
        }
        if (bonus.equals("BA")) {
            return ("Bônus de Acréscimo");
        }
        if (bonus.equals("BR")) {
            return ("Bônus de Redução");
        }
        return situacaoClienteApr;
    }

    public void setSituacaoClienteApr(String situacaoClienteApr) {
        this.situacaoClienteApr = situacaoClienteApr;
    }

    public String getBonus() {
        return bonus;
    }

    public String getSituacao() {
        return situacao;
    }

    public String getSituacaoClienteApr() {
        return situacaoClienteApr;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public Integer getConvenio() {
        return convenio;
    }

    public void setConvenio(Integer convenio) {
        this.convenio = convenio;
    }

    public String getCategoria() {
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public String getModalidades() {
        if (modalidades == null) {
            return (" ");
        }
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getCpfResponsavel() {
        return cpfResponsavel;
    }

    public void setCpfResponsavel(String cpfResponsavel) {
        this.cpfResponsavel = cpfResponsavel;
    }

    public String getRne() {
        return rne;
    }

    public void setRne(String rne) {
        this.rne = rne;
    }

    public String getPassaporte() {
        return passaporte;
    }

    public void setPassaporte(String passaporte) {
        this.passaporte = passaporte;
    }

    public String getDocumento() {
        if (!UteisValidacao.emptyString(getCpf())) {
            return getCpf();
        } else if (!UteisValidacao.emptyString(getRne())) {
            return "RNE: " + getRne();
        } else if (!UteisValidacao.emptyString(getPassaporte())) {
            return "Passaporte: " + getPassaporte();
        } else if (!UteisValidacao.emptyString(getCpfResponsavel())) {
            return getCpfResponsavel();
        }
        return "";
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getJustificativa() {
        if (justificativa != null) {
            justificativa = justificativa.trim();
        }
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public String getNomeRespContrato() {
        return nomeRespContrato;
    }

    public void setNomeRespContrato(String nomeRespContrato) {
        this.nomeRespContrato = nomeRespContrato;
    }

    public String getNomeConsultor() {
        return nomeConsultor;
    }

    public void setNomeConsultor(String nomeConsultor) {
        this.nomeConsultor = nomeConsultor;
    }

    public String getNomeProfessores() {
        return nomeProfessores;
    }

    public void setNomeProfessores(String nomeProfessores) {
        this.nomeProfessores = nomeProfessores;
    }

    public String getProfessorTreino() {
        return professorTreino;
    }

    public void setProfessorTreino(String professorTreino) {
        this.professorTreino = professorTreino;
    }

    public Date getDataInicioOperacao() {
        return dataInicioOperacao;
    }

    public void setDataInicioOperacao(Date dataInicioOperacao) {
        this.dataInicioOperacao = dataInicioOperacao;
    }

    public Date getDataFimOperacao() {
        return dataFimOperacao;
    }

    public void setDataFimOperacao(Date dataFimOperacao) {
        this.dataFimOperacao = dataFimOperacao;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public String getMesAno() {
        return mesAno;
    }

    public void setMesAno(String mesAno) {
        this.mesAno = mesAno;
    }

    public Integer getMeta() {
        return meta;
    }

    public void setMeta(Integer meta) {
        this.meta = meta;
    }

    public Integer getMetaAtingida() {
        return metaAtingida;
    }

    public void setMetaAtingida(Integer metaAtingida) {
        this.metaAtingida = metaAtingida;
    }

    public Integer getRepescagem() {
        return repescagem;
    }

    public void setRepescagem(Integer repescagem) {
        this.repescagem = repescagem;
    }

    public String getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(String porcentagem) {
        this.porcentagem = porcentagem;
    }

    public Integer getCodUsuario() {
        return codUsuario;
    }

    public void setCodUsuario(Integer codUsuario) {
        this.codUsuario = codUsuario;
    }

    public Integer getCodColaborador() {
        return codColaborador;
    }

    public void setCodColaborador(Integer codColaborador) {
        this.codColaborador = codColaborador;
    }

    public String getEstadoCivil() {
        if (estadoCivil == null) {
            estadoCivil = "";
        } else if (estadoCivil.equals("S")) {
            return "Solteiro(a)";
        } else if (estadoCivil.equals("C")) {
            return "Casado(a)";
        } else if (estadoCivil.equals("A")) {
            return "Amasiado(a)";
        } else if (estadoCivil.equals("V")) {
            return "Viúvo(a)";
        } else if (estadoCivil.equals("D")) {
            return "Divorciado(a)";
        } else if (estadoCivil.equals("P")) {
            return "Separado(a)";
        } else if (estadoCivil.equals("U")) {
            return "União estável";
        }
        return (estadoCivil);
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getProfissao() {
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public void setTipoCliente(String tipoCliente) {
        this.tipoCliente = tipoCliente;
    }

    public String getTipoCliente() {
        return tipoCliente;
    }

    public Integer getGrupoRisco() {
        if (grupoRisco == null) {
            grupoRisco = 0;
        }
        return grupoRisco;
    }

    public void setGrupoRisco(Integer grupoRiscoPeso) {
        this.grupoRisco = grupoRiscoPeso;
    }

    public String getGrupoRiscoApresentar(){
        String retorno = "";
        if (!UteisValidacao.emptyString(situacaoCliente) && situacaoCliente.equals("AT")) {
            if (getGrupoRisco() > 0) {
                retorno = getGrupoRisco().toString();
            }
        }
        return retorno;
    }

    public String getGrupoDesconto() {
        return grupoDesconto;
    }

    public void setGrupoDesconto(String grupoDesconto) {
        this.grupoDesconto = grupoDesconto;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getVezesSemana() {
        return vezesSemana;
    }

    public void setVezesSemana(String vezesSemana) {
        this.vezesSemana = vezesSemana;
    }

    public String getPacote() {
        return pacote;
    }

    public void setPacote(String pacote) {
        this.pacote = pacote;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getDataLancamentoApresentar() {
        if (getDataLancamento() == null) {
            return "";
        } else {
            return Uteis.getDataComHora(getDataLancamento());
        }
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getProduto() {
        if (produto == null) {
            produto = "";
        }
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public String getMensagemVendasOnline() {
        if (mensagemVendasOnline == null) {
            mensagemVendasOnline = "";
        }
        return mensagemVendasOnline;
    }

    public void setMensagemVendasOnline(String mensagemVendasOnline) {
        this.mensagemVendasOnline = mensagemVendasOnline;
    }

    public String getEvento() {
        return evento;
    }

    public void setEvento(String evento) {
        this.evento = evento;
    }

    public String getParq() {
        return parq;
    }

    public void setParq(String parq) {
        this.parq = parq;
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getDiaSemanaUltimoAcesso() {
        return Uteis.retornaDescricaoDiaSemana(dataUltimoAcesso);
    }

    public String getSentido() {
        return sentido;
    }

    public void setSentido(String sentido) {
        this.sentido = sentido;
    }

    public String getSentidoApresentar() {
        if ("E".equalsIgnoreCase(sentido)) {
            return "Entrada";
        } else if ("S".equalsIgnoreCase(sentido)) {
            return "Saída";
        }
        return sentido;
    }

    public String getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(String localAcesso) {
        this.localAcesso = localAcesso;
    }

    public String getColetor() {
        return coletor;
    }

    public void setColetor(String coletor) {
        this.coletor = coletor;
    }

    public String getMeioIdentificacaoEntrada() {
        return meioIdentificacaoEntrada;
    }

    public void setMeioIdentificacaoEntrada(String meioIdentificacaoEntrada) {
        this.meioIdentificacaoEntrada = meioIdentificacaoEntrada;
    }

    public String getMeioIdentificacaoSaida() {
        return meioIdentificacaoSaida;
    }

    public void setMeioIdentificacaoSaida(String meioIdentificacaoSaida) {
        this.meioIdentificacaoSaida = meioIdentificacaoSaida;
    }

    public String getHabilitacaoSesc() {
        return habilitacaoSesc;
    }

    public void setHabilitacaoSesc(String habilitacaoSesc) {
        this.habilitacaoSesc = habilitacaoSesc;
    }

    public String getValidadeCartaoSesc() {
        return validadeCartaoSesc;
    }

    public void setValidadeCartaoSesc(String validadeCartaoSesc) {
        this.validadeCartaoSesc = validadeCartaoSesc;
    }

    public String getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(String valorContrato) {
        this.valorContrato = valorContrato;
    }

    public String getValorPago() {
        return valorPago;
    }

    public void setValorPago(String valorPago) {
        this.valorPago = valorPago;
    }

    public String getValorUtilizado() {
        return valorUtilizado;
    }

    public void setValorUtilizado(String valorUtilizado) {
        this.valorUtilizado = valorUtilizado;
    }

    public String getValorDevolvido() {
        return valorDevolvido;
    }

    public void setValorDevolvido(String valorDevolvido) {
        this.valorDevolvido = valorDevolvido;
    }

    public String getDescricaoCancelamento() {
        return descricaoCancelamento;
    }

    public void setDescricaoCancelamento(String descricaoCancelamento) {
        this.descricaoCancelamento = descricaoCancelamento;
    }
}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.basico;

import java.util.Map;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import relatorio.controle.arquitetura.SuperControleRelatorio;

/**
 *
 * <AUTHOR>
 */
public class DemonstrativoFinanceiroRel extends SuperControleRelatorio {

    private String filtros = "";
    private TipoRelatorioDF tipoRelatorioDF;

    public DemonstrativoFinanceiroRel() throws Exception {
        super();
    }

    public void imprimirRelatorio(Map<String, Object> parametros) {
        try {
            apresentarRelatorioObjetos(parametros);
            setMensagemDetalhada("", "");
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getFiltros() {
        return filtros;
    }

    /**
     * @param filtros the filtros to set
     */
    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    /**
     * @return the tipoRelatorioDF
     */
    public TipoRelatorioDF getTipoRelatorioDF() {
        return tipoRelatorioDF;
    }

    /**
     * @param tipoRelatorioDF the tipoRelatorioDF to set
     */
    public void setTipoRelatorioDF(TipoRelatorioDF tipoRelatorioDF) {
        this.tipoRelatorioDF = tipoRelatorioDF;
    }
}

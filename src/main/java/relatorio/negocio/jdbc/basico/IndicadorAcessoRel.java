/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.basico;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class IndicadorAcessoRel {
    
    private Double hora;
    private Integer quantidade;
    private Date data;
    private Integer mes;
    private Integer ano;
    
    public void setHora(Double hora) {
        this.hora = hora;
    }

    public Double getHora() {
        return hora;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getData() {
        return data;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }
}

package relatorio.negocio.jdbc.basico;

/**
 *
 * <AUTHOR>
 */
/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.acesso.AcessoColaboradorVO;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.enumerador.MeioIdentificacaoEnum;
import negocio.comuns.acesso.enumerador.SituacaoAcessoEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.GrupoVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.io.File;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ListaAcessoRel extends SuperRelatorio {


    protected Date dataInicio;
    protected Date dataTermino;
    protected String horarioInicio;
    protected String horarioFim;
    protected String faixaHoraInicial;
    protected String faixaHoraFinal;
    protected EmpresaVO empresaVO;
    protected ClienteVO clienteVO;
    private ColaboradorVO professor;
    private ColaboradorVO professorVinculo;
    private ColaboradorVO professorTreino;
    private String tipoAcessoClienteOuColaborador;
    private ColaboradorVO colaborador;
    private GrupoVO grupo;
    private PlanoVO plano;
    private ModalidadeVO modalidade;


    public ListaAcessoRel() throws Exception {
        super();
        setIdEntidade("ListaAcessoRelCliente");
        inicializarParametros();
    }

    public void inicializarParametros() {
        setEmpresaVO(new EmpresaVO());
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setHorarioInicio("00:00");
        setHorarioFim("23:59");
        setFaixaHoraInicial("");
        setFaixaHoraFinal("");
        setClienteVO(new ClienteVO());
        setProfessor(new ColaboradorVO());
        setProfessorVinculo(new ColaboradorVO());
        setProfessorTreino(new ColaboradorVO());
        setTipoAcessoClienteOuColaborador("CL");
        setColaborador(new ColaboradorVO());
        setGrupo(new GrupoVO());
        setPlano(new PlanoVO());
    }

    public void validarDados() throws ConsistirException, Exception {
        if (getEmpresaVO().getCodigo() == 0) {
            throw new ConsistirException("Uma Empresa deve ser selecionada !");
        }
        if (getDataInicio() == null || getDataTermino() == null) {
            throw new Exception("O período deve ser preenchido.");
        }

        if (Calendario.maior(getDataInicio(), getDataTermino())) {
            throw new Exception("A Data de Início deve ser menor que a Data De Término para pesquisa.");
        }
        if (Calendario.igual(getDataInicio(), getDataTermino())) {
            if (!Calendario.horasMenor(getHorarioInicio(),getHorarioFim())) {
                throw new Exception("O Horário Inicial deve ser menor que o Horário Final para pesquisa");
            }
        }
        if ((getFaixaHoraInicial().equals("") && !getFaixaHoraFinal().equals("")) || (getFaixaHoraFinal().equals("") && !getFaixaHoraInicial().equals(""))) {
            throw new Exception("Informe a Faixa de Horário de início e de fim para pesquisa");
        }
        if (!getFaixaHoraInicial().equals("") || !getFaixaHoraFinal().equals("")) {
            if (Calendario.horasMenor(getFaixaHoraFinal(), getFaixaHoraInicial())) {
                throw new Exception("A Faixa Horária de Início deve ser menor que a Faixa Horária de Fim para pesquisa");
            }
        }
    }

    public List consultarAcessosClientes(boolean somentePrimeiroAcessoDia) throws Exception {
        validarDados();
        return consultarCliente(getEmpresaVO().getCodigo(), getDataInicio(), getDataTermino(),
                getHorarioInicio(), getHorarioFim(), getFaixaHoraInicial(),
                getFaixaHoraFinal(), getClienteVO().getCodigo(), professor.getCodigo(),
                professorVinculo.getCodigo(),professorTreino.getCodigo(), grupo.getCodigo(), plano.getCodigo(), modalidade.getCodigo(),
                somentePrimeiroAcessoDia);

    }
    public List consultarAcessosColaboradores(boolean campoSomentePrimeiroAcesso) throws Exception {
        validarDados();
        return  consultarColaborador(getEmpresaVO().getCodigo(), getDataInicio(), getDataTermino(),
                getHorarioInicio(), getHorarioFim(), getFaixaHoraInicial(),
                getFaixaHoraFinal(), getColaborador().getPessoa().getCodigo(),
                campoSomentePrimeiroAcesso);

    }
    public List consultarCliente(Integer empresa, Date dataInicial, Date dataFinal, String horarioInicio,
                                 String horarioFim, String faixaHoraInicial, String faixaHoraFinal,
                                 Integer cliente, Integer professor, Integer professorVinculo,Integer professorTreino, Integer grupo,
                                 Integer plano, Integer modalidade,
                                 boolean somentePrimeiroAcessoDia) throws Exception {
        inicializar();
        //Dados de saída
        //Matricula, Nome, Data hora Entrada, Data hora Saida, Tempo,  Tipo Acesso(sentido), usuário que Liberou o acesso.
        StringBuffer sql = new StringBuffer();
        sql.append("select distinct ac.codigo, cli.matricula, ac.*, sit.nomecliente as nome, ARRAY_TO_STRING(ARRAY(select email from email where pessoa = pe.codigo), ',  ') as email,\n" +
                "\tsit.codigocliente as codigopessoa , emp.codigo as codigoempresa, emp.nome as nomeempresa,");
        if(somentePrimeiroAcessoDia){
            sql.append("to_char(dthrentrada, 'DD/MM/YYYY') as dataentrada, \n");
        }
        sql.append("us.nome as nomeusuario, loc.descricao as descricaolocal, ct.descricao as descricaocoletor ");
        sql.append("FROM acessocliente ac ");
        sql.append("inner join cliente cli on cli.codigo = ac.cliente ");
        sql.append("inner join empresa emp on emp.codigo = cli.empresa ");
        sql.append("inner join situacaoclientesinteticodw sit on sit.codigocliente = cli.codigo ");
        sql.append("inner join localacesso loc on loc.codigo =ac.localacesso ");
        sql.append("inner join pessoa pe on cli.pessoa = pe.codigo ");
        sql.append("left join email em on pe.codigo = em.pessoa ");
        sql.append("inner join coletor ct on ct.codigo = ac.coletor ");
        sql.append("left join usuario us on us.codigo =  ac.usuario ");
        if (professor != null && professor != 0) {
            sql.append("inner join matriculaalunohorarioturma matr on matr.pessoa = sit.codigocliente ");
            sql.append("inner join horarioturma ht on matr.horarioturma = ht.codigo ");
            sql.append("inner join colaborador col on ht.professor = col.codigo ");
        }

        if (professorVinculo != null && professorVinculo != 0) {
            sql.append("inner join vinculo vinc on vinc.cliente = cli.codigo ");
        }
        if (professorTreino != null && professorTreino != 0){
            sql.append("inner JOIN vinculo vi ON vi.cliente = cli.codigo AND vi.tipovinculo IN ('TW')");
        }
        if (grupo != null && grupo != 0) {
            sql.append("inner join clientegrupo cg on cg.cliente = cli.codigo ");
        }
        if (plano != null && plano != 0) {
            sql.append("inner join plano pla on pla.descricao = sit.nomeplano ");
        }
        if (modalidade != null && modalidade != 0) {
            sql.append("inner join contrato con ON sit.codigocontrato = con.codigo\n");
            sql.append("inner join contratomodalidade cmod ON cmod.contrato = con.codigo\n");
        }
        if (!faixaHoraInicial.equals("") && !faixaHoraFinal.equals("")) {
            sql.append("where cast(ac.dthrentrada as DATE) between '");
            sql.append(Uteis.getDataFormatoBD(dataInicial));
            sql.append("' and '");
            sql.append(Uteis.getDataFormatoBD(dataFinal));
            sql.append("' and ");
            sql.append("cast(ac.dthrentrada as TIME) BETWEEN '");
            sql.append(faixaHoraInicial).append(":00");
            sql.append("' and '");
            sql.append(faixaHoraFinal).append(":59");
            sql.append("'");
        } else {
            sql.append("where ( ac.dthrentrada >= '").append(Uteis.getDataFormatoBD(dataInicial)).
                    append(" ").append(horarioInicio).append(":00").append("' and ac.dthrentrada <= '").
                    append(Uteis.getDataFormatoBD(dataFinal)).append(" ").append(horarioFim).append(":59").append("')");
        }
        if (cliente != null && cliente != 0) {
            sql.append(" and ac.cliente = ").append(cliente.intValue());
        }
        if (professor != null && professor != 0) {
            sql.append(" and ht.professor = ").append(professor.intValue());
        }
        if (professorVinculo != null && professorVinculo != 0) {
            sql.append(" and vinc.colaborador = ").append(professorVinculo.intValue());
        }
        if (professorTreino != null && professorTreino != 0){
            sql.append("and vi.colaborador = ").append(professorTreino.intValue());
        }
        if (grupo != null && grupo != 0) {
            sql.append(" and cg.grupo = ").append(grupo.intValue());
        }
        if (plano != null && plano != 0) {
            sql.append(" and pla.codigo = ").append(plano.intValue());
        }
        if (modalidade != null && modalidade != 0) {
            sql.append(" AND cmod.modalidade = ").append(modalidade).append("\n");
        }

        sql.append(" and emp.codigo = ");
        sql.append(empresa.intValue());
        sql.append(" order by sit.nomecliente,ac.dthrentrada");

        if(somentePrimeiroAcessoDia){
            StringBuffer sqlPai = new StringBuffer();
            sqlPai.append("SELECT distinct  on (nome, dataentrada) * FROM(");
            sqlPai.append(sql);
            sqlPai.append(") as subConsulta ORDER BY nome, dataentrada");
            sql = sqlPai;
        }

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        List<AcessoClienteVO> listaAcesso = new ArrayList<AcessoClienteVO>();
        while (tabelaResultado.next()) {
            AcessoClienteVO acessoCliente = montarDadosAcessoCliente(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            listaAcesso.add(acessoCliente);
        }
        return listaAcesso;
    }

    public List<AcessoColaboradorVO> consultarColaborador(Integer empresa,
                                     Date dataInicial,
                                     Date dataFinal,
                                     String horarioInicio,
                                     String horarioFim,
                                     String faixaHoraInicial,
                                     String faixaHoraFinal,
                                     Integer pessoaColaborador,
                                     boolean somentePrimeiroAcessoDia) throws Exception {
        inicializar();
        //Dados de saída
        //Matricula, Nome, Data hora Entrada, Data hora Saida, Tempo,  Tipo Acesso(sentido), usuário que Liberou o acesso.
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT distinct ac.*,\n");
        sql.append("                pes.nome,\n");
        sql.append("                pes.codigo                                                                     as codigopessoa,\n");
        sql.append("                emp.nome                                                                       as nomeempresa,\n");
        sql.append("                emp.codigo                                                                     as codigoEmpresa,\n");
        sql.append("                array_to_string(array(select email from email where pessoa = pes.codigo), ',') as email,");
        if(somentePrimeiroAcessoDia){
            sql.append("date_trunc('day', dthrentrada) as dataentrada,");
        }
        sql.append("la.descricao as descricaolocal, ct.descricao as descricaocoletor ");
        sql.append("FROM acessocolaborador ac\n");
        sql.append("               INNER JOIN colaborador col ON ac.colaborador = col.codigo\n");
        sql.append("               INNER JOIN pessoa pes ON col.pessoa = pes.codigo\n");
        sql.append("               inner join empresa emp on emp.codigo = col.empresa\n");
        sql.append("               inner join localacesso la on la.codigo = ac.localacesso\n");
        sql.append("               inner join coletor ct on ct.codigo = ac.coletor\n");
        if (!faixaHoraInicial.equals("") && !faixaHoraFinal.equals("")) {
            sql.append("where cast(ac.dthrentrada as DATE) between '");
            sql.append(Uteis.getDataFormatoBD(dataInicial));
            sql.append("' and '");
            sql.append(Uteis.getDataFormatoBD(dataFinal));
            sql.append("' and ");
            sql.append("cast(ac.dthrentrada as TIME) BETWEEN '");
            sql.append(faixaHoraInicial).append(":00");
            sql.append("' and '");
            sql.append(faixaHoraFinal).append(":59");
            sql.append("'");
        } else {
            sql.append("where ( ac.dthrentrada >= '").append(Uteis.getDataFormatoBD(dataInicial)).
                    append(" ").append(horarioInicio).append(":00").append("' and ac.dthrentrada <= '").
                    append(Uteis.getDataFormatoBD(dataFinal)).append(" ").append(horarioFim).append(":59").append("')");
        }

        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append("and col.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyNumber(pessoaColaborador)) {
            sql.append(" and pes.codigo = ").append(pessoaColaborador);
        }
        sql.append(" order by pes.nome,ac.dthrentrada");

        if(somentePrimeiroAcessoDia){
            StringBuffer sqlPai = new StringBuffer();
            sqlPai.append("SELECT distinct  on (nome, dataentrada) * FROM(");
            sqlPai.append(sql);
            sqlPai.append(") as subConsulta ORDER BY nome, dataentrada");
            sql = sqlPai;
        }

        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        List<AcessoColaboradorVO> listaAcesso = new ArrayList<>();
        while (tabelaResultado.next()) {
            AcessoColaboradorVO acessoColaborador = montarDadosAcessoColaborador(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
            listaAcesso.add(acessoColaborador);
        }
        return listaAcesso;
    }
    public static AcessoClienteVO montarDadosAcessoCliente(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AcessoClienteVO obj = new AcessoClienteVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));

        ClienteVO clienteVO = new ClienteVO();
        LocalAcessoVO localVO = new LocalAcessoVO();
        ColetorVO coletorVO = new ColetorVO();
        UsuarioVO usuarioVO = new UsuarioVO();

        clienteVO.setCodigo(dadosSQL.getInt("cliente"));
        clienteVO.setMatricula(dadosSQL.getString("matricula"));
        clienteVO.getPessoa().setCodigo(dadosSQL.getInt("codigopessoa"));
        clienteVO.getPessoa().setNome(dadosSQL.getString("nome"));
        clienteVO.getPessoa().setEmail(dadosSQL.getString("email"));
        localVO.setCodigo(dadosSQL.getInt("localacesso"));
        localVO.setDescricao(dadosSQL.getString("descricaolocal"));
        localVO.getEmpresa().setCodigo(dadosSQL.getInt("codigoempresa"));
        coletorVO.setCodigo(dadosSQL.getInt("coletor"));
        coletorVO.setDescricao(dadosSQL.getString("descricaocoletor"));
        usuarioVO.setCodigo(dadosSQL.getInt("usuario"));
        usuarioVO.setNome(dadosSQL.getString("nomeUsuario"));


        obj.setCliente(clienteVO);
        obj.setLocalAcesso(localVO);
        obj.setColetor(coletorVO);
        obj.setUsuario(usuarioVO);
        obj.setDataHoraEntrada(dadosSQL.getTimestamp("dthrentrada"));
        obj.setDataHoraEntrada(dadosSQL.getTimestamp("dthrentrada"));
        obj.setDataHoraSaida(dadosSQL.getTimestamp("dthrsaida"));
        obj.setSentido(dadosSQL.getString("sentido"));
        obj.setSituacao(SituacaoAcessoEnum.valueOf(dadosSQL.getString("situacao").trim()));
        obj.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioidentificacaoentrada")));
        obj.setMeioIdentificacaoSaida(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioidentificacaosaida")));
        obj.setIntervaloDataHoras(Uteis.getIntervaloHorasEntreDatas(obj.getDataHoraEntrada(), obj.getDataHoraSaida()));
        return obj;
    }

    private AcessoColaboradorVO montarDadosAcessoColaborador(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        AcessoColaboradorVO obj = new AcessoColaboradorVO();
        obj.setNovoObj(false);
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setMeioIdentificacaoEntrada(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioIdentificacaoEntrada")));
        obj.setMeioIdentificacaoSaida(MeioIdentificacaoEnum.getMeioIdentificacao(dadosSQL.getInt("meioIdentificacaoSaida")));
        obj.setSentido(dadosSQL.getString("sentido"));
        obj.setDataHoraEntrada(dadosSQL.getTimestamp("dthrentrada"));
        obj.setDataHoraSaida(dadosSQL.getTimestamp("dthrsaida"));
        obj.setColaborador(new ColaboradorVO());
        obj.getColaborador().setCodigo(dadosSQL.getInt("colaborador"));
        obj.getColaborador().getPessoa().setCodigo(dadosSQL.getInt("codigopessoa"));
        obj.getColaborador().getPessoa().setNome(dadosSQL.getString("nome"));
        obj.getColaborador().getPessoa().setEmail(dadosSQL.getString("email"));
        obj.setIntervaloDataHoras(Uteis.getIntervaloHorasEntreDatas(obj.getDataHoraEntrada(), obj.getDataHoraSaida()));
        obj.setLocalAcesso(new LocalAcessoVO());
        obj.getLocalAcesso().setCodigo(dadosSQL.getInt("localacesso"));
        obj.getLocalAcesso().setDescricao(dadosSQL.getString("descricaolocal"));
        obj.getLocalAcesso().getEmpresa().setCodigo(dadosSQL.getInt("codigoempresa"));
        obj.getLocalAcesso().getEmpresa().setNome(dadosSQL.getString("nomeempresa"));
        obj.setColetor(new ColetorVO());
        obj.getColetor().setCodigo(dadosSQL.getInt("coletor"));
        obj.getColetor().setDescricao(dadosSQL.getString("descricaocoletor"));

        return obj;
    }
    public String getDesignIReportRelatorio() {
        String caminho="";
        if(tipoAcessoClienteOuColaborador.equals("CL")){
            caminho="relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator + "ListaAcessoRelCliente.jrxml";
        }else if(tipoAcessoClienteOuColaborador.equals("CO")){
            caminho="relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator +"ListaAcessoRelColaborador.jrxml";
        }
        return caminho;
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "outros" + File.separator);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the horarioInicio
     */
    public String getHorarioInicio() {
        return horarioInicio;
    }

    /**
     * @param horarioInicio the horarioInicio to set
     */
    public void setHorarioInicio(String horarioInicio) {
        this.horarioInicio = horarioInicio;
    }

    /**
     * @return the horarioFim
     */
    public String getHorarioFim() {
        return horarioFim;
    }

    /**
     * @param horarioFim the horarioFim to set
     */
    public void setHorarioFim(String horarioFim) {
        this.horarioFim = horarioFim;
    }

    /**
     * @return the faixaHoraInicial
     */
    public String getFaixaHoraInicial() {
        return faixaHoraInicial;
    }

    /**
     * @param faixaHoraInicial the faixaHoraInicial to set
     */
    public void setFaixaHoraInicial(String faixaHoraInicial) {
        this.faixaHoraInicial = faixaHoraInicial;
    }

    /**
     * @return the faixaHoraFinal
     */
    public String getFaixaHoraFinal() {
        return faixaHoraFinal;
    }

    /**
     * @param faixaHoraFinal the faixaHoraFinal to set
     */
    public void setFaixaHoraFinal(String faixaHoraFinal) {
        this.faixaHoraFinal = faixaHoraFinal;
    }

    /**
     * @return the colaboradorVO
     */
    public ColaboradorVO getProfessor() {
        return professor;
    }

    /**
     * @param colaboradorVO the colaboradorVO to set
     */
    public void setProfessor(ColaboradorVO colaboradorVO) {
        this.professor = colaboradorVO;
    }

    /**
     * @return the tipoAcessoClienteOuColaborador
     */
    public String getTipoAcessoClienteOuColaborador() {
        if (tipoAcessoClienteOuColaborador == null) {
            tipoAcessoClienteOuColaborador = "";
        }
        return tipoAcessoClienteOuColaborador;
    }

    /**
     * @param tipoAcessoClienteOuColaborador the tipoAcessoClienteOuColaborador to set
     */
    public void setTipoAcessoClienteOuColaborador(String tipoAcessoClienteOuColaborador) {
        this.tipoAcessoClienteOuColaborador = tipoAcessoClienteOuColaborador;
    }

    /**
     * @return the colaborador
     */
    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    /**
     * @param colaborador the colaborador to set
     */
    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    public void setProfessorVinculo(ColaboradorVO professorVinculo) {
        this.professorVinculo = professorVinculo;
    }

    public ColaboradorVO getProfessorVinculo() {
        return professorVinculo;
    }

    public ColaboradorVO getProfessorTreino() { return professorTreino;    }

    public void setProfessorTreino(ColaboradorVO professorTreino) {
        this.professorTreino = professorTreino;
    }

    public void setGrupo(GrupoVO grupo) {
        this.grupo = grupo;
    }

    public GrupoVO getGrupo() {
        return grupo;
    }

    public ModalidadeVO getModalidade() {
        if (modalidade == null) {
            modalidade = new ModalidadeVO();
        }
        return modalidade;
    }

    public void setModalidade(ModalidadeVO modalidade) {
        this.modalidade = modalidade;
    }

    public static enum TipoFiltroProfessor{
        VINCULO(1, "Vínculos"), TURMA(2,"Turma");

        public String descricao;
        public Integer codigo;

        private TipoFiltroProfessor(Integer codigo, String descricao){
            this.descricao = descricao;
            this.codigo = codigo;
        }
    }


    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano){
        this.plano = plano;
    }
}

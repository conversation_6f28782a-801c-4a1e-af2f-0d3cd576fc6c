package relatorio.negocio.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.plano.Turma;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.SGPModalidadeComTurmaTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

import static br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria.*;
import static org.codehaus.groovy.runtime.InvokerHelper.asList;

public class SGPTurmaRel extends SuperRelatorio {
    private Date dataInicio;
    private Date dataFim;
    private EmpresaVO empresa;
    private List<ModalidadeVO> modalidades = new ArrayList<>();
    private TurmaVO turma;
    private Integer tipoRel;
    private Integer modeloRel;
    private Integer vezesSemanaRel;
    private String chave;
    JSONArray listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached = new JSONArray();

    public SGPTurmaRel() throws Exception {
        super();
        setIdEntidade("SGPTurmaRel");
        inicializar();
        inicializarParametros();
    }

    public SGPTurmaRel(Connection con) throws Exception {
        super(con);
        setIdEntidade("SGPTurmaRel");
        inicializar();
        inicializarParametros();
    }

    private void inicializarParametros() {
        setDataInicio(new Date());
        setDataFim(new Date());
        setEmpresa(new EmpresaVO());
        setModalidades(new ArrayList<>());
    }

    public void setarParametrosConsulta(Date dataInicio, Date dataFim, EmpresaVO empresa, List<ModalidadeVO> modalidades, ModalidadeVO modalidade, TurmaVO turma, Integer tipoRel, Integer modeloRel, String chave, Integer vezesSemanaRel) {
        setTurma(null);
        setDataInicio(dataInicio);
        setDataFim(dataFim);
        setEmpresa(empresa);
        if (modalidade != null && !UteisValidacao.emptyNumber(modalidade.getCodigo())) {
            setModalidades(asList(modalidade));
            if (turma != null && !UteisValidacao.emptyNumber(turma.getCodigo())) {
                setTurma(turma);
            }
        } else {
            setModalidades(modalidades);
        }
        setTipoRel(tipoRel);
        setModeloRel(modeloRel);
        setVezesSemanaRel(vezesSemanaRel);
        setChave(chave);
    }

    public void validarDados() throws ConsistirException {
        if (getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Uma empresa Deve Ser Selecionada!");
        }
        if (getDataInicio() == null) {
            throw new ConsistirException("Informe a data inicial da Pesquisa!");
        }
        if (getDataFim() == null) {
            throw new ConsistirException("Informe a data final da Pesquisa!");
        }
    }

    public List<SGPModalidadeComTurmaTO> consultar() throws ConsistirException {
        List<SGPModalidadeComTurmaTO> listaConsultada = new ArrayList<>();
        SGPModalidadeComTurmaTO totalizador = new SGPModalidadeComTurmaTO();
        totalizador.setModalidadeVO(new ModalidadeVO());
        totalizador.getModalidadeVO().setNome("TOTAIS");

        try {
            List<Date> dias = Uteis.getDiasEntreDatas(dataInicio, dataFim);
            String codigosModalidade = modalidades.stream()
                    .map(m -> String.valueOf(m.getCodigo()))
                    .collect(Collectors.joining(","));
            for (Date dia : dias) {
                SGPModalidadeComTurmaTO aux = processarTurmaDia(chave, codigosModalidade, dia, dia, turma);
                aux.setDia(Uteis.getData(dia, "dd/MM/yyyy"));
                listaConsultada.add(aux);
                totalizador.adicionaFrequencias(aux);
            }
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
        Ordenacao.ordenarLista(listaConsultada, "dia");
        listaConsultada.add(totalizador);
        return listaConsultada;
    }

    public Object[][] obterDadosExcelEstatistico(Integer tipoRel) throws ConsistirException {
        if(Calendario.diferencaEmDias(dataInicio, dataFim) > 30){
            throw new ConsistirException("O período de consulta não pode ser maior que 30 dias.");
        }
        setTipoRel(tipoRel);
        List<SGPModalidadeComTurmaTO> listaConsultada = new ArrayList<>();
        SGPModalidadeComTurmaTO totalizador = new SGPModalidadeComTurmaTO();
        totalizador.setModalidadeVO(new ModalidadeVO());
        totalizador.getModalidadeVO().setNome("TOTAIS");

        try {
            List<Date> dias = Uteis.getDiasEntreDatas(dataInicio, dataFim);
            String codigosModalidade = modalidades.stream()
                    .map(m -> String.valueOf(m.getCodigo()))
                    .collect(Collectors.joining(","));
            for (Date dia : dias) {
                SGPModalidadeComTurmaTO aux = processarTurmaDia(chave, codigosModalidade, dia, dia, turma);
                aux.setDia(Uteis.getData(dia, "dd/MM/yyyy"));
                listaConsultada.add(aux);
                totalizador.adicionaFrequencias(aux);
            }
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
        Ordenacao.ordenarLista(listaConsultada, "dia");
        listaConsultada.add(totalizador);

        List<Object[]> dataList = new ArrayList<>();

        for (SGPModalidadeComTurmaTO sgp : listaConsultada) {
                dataList.add(new Object[]{
                        UteisValidacao.emptyString(sgp.getDia()) ? "TOTAIS" : sgp.getDia(),
                        sgp.getQtdeComerciario(),
                        sgp.getQtdeDependente(),
                        sgp.getQtdeUsuario(),
                        sgp.getQtdeSemCategoria(),
                        sgp.getTotalCategorias(),
                        sgp.getContratosCanceladosDesistentes(),
                        sgp.getQtdTurmasCriadasAtivasPeriodo(),
                        sgp.getQtdTurmasCriadasAtivasComAlunoPeriodo(),
                        sgp.getCargaHorariaConvertida()
                });
        }
        return dataList.toArray(new Object[0][]);
    }

    private List<ConsultarAlunosTurmaVO> findAgendamentoAvaliacao(String chave, Date dataInicio, boolean confirmado, boolean umaVezAno) throws Exception {
        List<ConsultarAlunosTurmaVO> listaConsultaAlunos = new ArrayList<>();
        String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
        String url = urlTreino + "/prest/agendamento/" + chave +
                "/agendamentoAvaliacao/" + Calendario.getData(dataInicio, "yyyy-MM-dd") + "/" + (confirmado ? "true" : "false") + "/" + (umaVezAno ? "true" : "false");
        String dados = ExecuteRequestHttpService.executeRequestCupomDesconto(url + "?empresaZw=" + getEmpresa().getCodigo(), "", new HashMap<>());
        JSONArray retorno = new JSONArray(new JSONObject(dados).getString("sucesso"));
        Cliente clienteDAO = new Cliente(this.con);
        for (int i = 0; i < retorno.length(); i++) {
            JSONObject obj = retorno.getJSONObject(i);
            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(obj.getInt("codigoCliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            ConsultarAlunosTurmaVO consultaAluno = new ConsultarAlunosTurmaVO();
            consultaAluno.setClienteVO(clienteVO);
            listaConsultaAlunos.add(consultaAluno);
        }
        clienteDAO = null;
        return listaConsultaAlunos;
    }

    private List<ConsultarAlunosTurmaVO> findAgendamentoAvaliacaoConfirmadoUmaVezAno(String chave, Date dataInicio, boolean confirmado, boolean umaVezAno) throws Exception {
        List<ConsultarAlunosTurmaVO> listaConsultaAlunos = new ArrayList<>();
        if (listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached.length() == 0) {
            String urlTreino = PropsService.getPropertyValue(chave, PropsService.urlTreino);
            String url = urlTreino + "/prest/agendamento/" + chave +
                    "/agendamentoAvaliacao/" + Calendario.getData(dataInicio, "yyyy-MM-dd") + "/" + (confirmado ? "true" : "false") + "/" + (umaVezAno ? "true" : "false");
            String dados = ExecuteRequestHttpService.executeRequestCupomDesconto(url + "?empresaZw=" + getEmpresa().getCodigo(), "", new HashMap<>());
            if (new JSONObject(dados).has("sucesso")) {
                listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached = new JSONArray(new JSONObject(dados).getString("sucesso"));
            }
        }
        Cliente clienteDAO = new Cliente(this.con);
        for (int i = 0; i < listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached.length(); i++) {
            JSONObject obj = listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached.getJSONObject(i);
            ClienteVO clienteVO;
            try {
                clienteVO = clienteDAO.consultarPorChavePrimaria(obj.getInt("codigoCliente"), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception ignore) {
                continue;
            }
            ConsultarAlunosTurmaVO consultaAluno = new ConsultarAlunosTurmaVO();
            consultaAluno.setClienteVO(clienteVO);
            if (Calendario.igual(Uteis.getDate(obj.getString("dataAgendamento"), "dd/MM/yyyy"), dataInicio)) {
                listaConsultaAlunos.add(consultaAluno);
            }
        }
        clienteDAO = null;
        return listaConsultaAlunos;
    }

    private SGPModalidadeComTurmaTO processarTurmaDia(String chave, String modalidades, Date inicio, Date fim, TurmaVO turmaVO) throws Exception {
        SGPModalidadeComTurmaTO retorno = new SGPModalidadeComTurmaTO();
        List<ConsultarAlunosTurmaVO> listaAlunos;
        if (getModeloRel() == 0) {
            MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma = new MatriculaAlunoHorarioTurma(con);
            Turma turma = new Turma(con);

            retorno.setListaCanceladosDesistentes(matriculaAlunoHorarioTurma.contarEvasaoPorVariasModalidadeNoPeriodo(inicio, fim, modalidades, getEmpresa(), turmaVO, getVezesSemanaRel()));
            retorno.setContratosCanceladosDesistentes(retorno.getListaCanceladosDesistentes().size());
            retorno.setQtdTurmasCriadasAtivasPeriodo(turma.contarTurmasAtivasPorDataInicialVigenciaVariasModalidade(inicio, fim, getEmpresa().getCodigo(), modalidades, turmaVO));
            retorno.setQtdTurmasCriadasAtivasComAlunoPeriodo(turma.contarTurmasAtivasComAlunoPorDataInicialVigenciaVariasModalidade(inicio, fim, getEmpresa().getCodigo(), modalidades, turmaVO, getVezesSemanaRel()));
            retorno.setQtdHoraAulaAtivaComAlunosPeriodoEmMinutos(turma.qtdHoraAulaAtivaComAlunosPeriodoEmMinutos(inicio, fim, getEmpresa().getCodigo(), modalidades, turmaVO, getVezesSemanaRel()));

            if (getTipoRel() == 0) {
                listaAlunos = matriculaAlunoHorarioTurma.contarEntradaPorVariasModalidadeNoPeriodo(inicio, fim, modalidades, getEmpresa().getCodigo(), turmaVO, getVezesSemanaRel());
            } else if (getTipoRel() == 1) {
                listaAlunos = matriculaAlunoHorarioTurma.contarPresencaPorVariasModalidadeNoPeriodo(inicio, fim, modalidades, getEmpresa().getCodigo(), turmaVO, getVezesSemanaRel());
            } else {
                listaAlunos = matriculaAlunoHorarioTurma.contarPresencaAnoPorVariasModalidadeNoPeriodo(inicio, fim, modalidades, getEmpresa().getCodigo(), turmaVO, getVezesSemanaRel());
            }

        } else {
            if (getTipoRel() == 0) {
                listaAlunos = findAgendamentoAvaliacao(chave, inicio, false, false);
            } else if (getTipoRel() == 1) {
                listaAlunos = findAgendamentoAvaliacao(chave, inicio, true, false);
            } else {
                listaAlunos = findAgendamentoAvaliacaoConfirmadoUmaVezAno(chave, inicio, true, true);
            }
        }

        for (ConsultarAlunosTurmaVO aluno : listaAlunos) {
            // diferencia as presencas deste aluno pela categoria
            TipoCategoria tipo = TipoCategoria.getTipoCategoria(aluno.getClienteVO().getCategoria().getTipoCategoria());
            if (tipo != null) {
                if (tipo == NAO_SOCIO || tipo == ALUNO || tipo == SOCIO || tipo == EVENTOS) {
                    retorno.addQtdeSemCategoria(1);
                    retorno.getListaSemCategoria().add(aluno.getClienteVO());
                } else {
                    switch (tipo) {
                        case COMERCIARIO:
                            retorno.addQtdeComerciario(1);
                            retorno.getListaComerciario().add(aluno.getClienteVO());
                            break;
                        case DEPENDENTE:
                            retorno.addQtdeDependente(1);
                            retorno.getListaDependentes().add(aluno.getClienteVO());
                            break;
                        case USUARIO:
                            retorno.addQtdeUsuario(1);
                            retorno.getListaUsuarios().add(aluno.getClienteVO());
                            break;
                    }
                }
            } else {
                retorno.addQtdeSemCategoria(1);
                retorno.getListaSemCategoria().add(aluno.getClienteVO());
            }
        }
        retorno.calculaTotalCategorias();
        return retorno;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<ModalidadeVO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeVO> modalidades) {
        this.modalidades = modalidades;
    }

    public TurmaVO getTurma() {
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    public Integer getTipoRel() {
        return tipoRel;
    }

    public void setTipoRel(Integer tipoRel) {
        this.tipoRel = tipoRel;
    }

    public Integer getModeloRel() {
        return modeloRel;
    }

    public void setModeloRel(Integer modeloRel) {
        this.modeloRel = modeloRel;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public JSONArray getListaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached() {
        return listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached;
    }

    public void setListaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached(JSONArray listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached) {
        this.listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached = listaAlunosAvaliacaoTreinoConfirmadoUmaVezAnoCached;
    }

    public Integer getVezesSemanaRel() {
        return vezesSemanaRel;
    }

    public void setVezesSemanaRel(Integer vezesSemanaRel) {
        this.vezesSemanaRel = vezesSemanaRel;
    }
}

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.basico;

/**
 *
 * <AUTHOR>
 */
public class IndicadorAcessoTO {
    
    private Integer mesAtual;
    private Integer anoAtual;
    private Integer quantidadeAtual;
    private Integer mesPassado;
    private Integer anoPassado;
    private Integer quantidadePassado;

    public Integer getMesAtual() {
        return mesAtual;
    }

    public void setMesAtual(Integer mesAtual) {
        this.mesAtual = mesAtual;
    }

    public Integer getAnoAtual() {
        return anoAtual;
    }

    public void setAnoAtual(Integer anoAtual) {
        this.anoAtual = anoAtual;
    }

    public Integer getQuantidadeAtual() {
        return quantidadeAtual;
    }

    public void setQuantidadeAtual(Integer quantidadeAtual) {
        this.quantidadeAtual = quantidadeAtual;
    }

    public Integer getMesPassado() {
        return mesPassado;
    }

    public void setMesPassado(Integer mesPassado) {
        this.mesPassado = mesPassado;
    }

    public Integer getAnoPassado() {
        return anoPassado;
    }

    public void setAnoPassado(Integer anoPassado) {
        this.anoPassado = anoPassado;
    }

    public Integer getQuantidadePassado() {
        return quantidadePassado;
    }

    public void setQuantidadePassado(Integer quantidadePassado) {
        this.quantidadePassado = quantidadePassado;
    }

}

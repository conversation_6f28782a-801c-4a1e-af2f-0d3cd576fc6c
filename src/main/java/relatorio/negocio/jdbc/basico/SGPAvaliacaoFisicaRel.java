package relatorio.negocio.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.UtilizacaoAvaliacaoFisicaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.UtilizacaoAvaliacaoFisica;
import relatorio.negocio.comuns.basico.SGPAvaliacaoFisicaTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SGPAvaliacaoFisicaRel extends SuperRelatorio {
    private Date dataInicio;
    private Date dataFim;
    private EmpresaVO empresa;

    public SGPAvaliacaoFisicaRel() throws Exception {
        super();
        setIdEntidade("SGPAvaliacaoFisicaRel");
        inicializar();
        inicializarParametros();
    }

    public SGPAvaliacaoFisicaRel(Connection con) throws Exception {
        super(con);
        setIdEntidade("SGPAvaliacaoFisicaRel");
        inicializar();
        inicializarParametros();
    }

    private void inicializarParametros() {
        setDataInicio(new Date());
        setDataFim(new Date());
        setEmpresa(new EmpresaVO());
    }

    public void setarParametrosConsulta(Date dataInicio, Date dataFim, EmpresaVO empresa) {
        setDataInicio(dataInicio);
        setDataFim(dataFim);
        setEmpresa(empresa);
    }

    public void validarDados() throws ConsistirException {
        if (getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Uma empresa Deve Ser Selecionada!");
        }
        if (getDataInicio() == null) {
            throw new ConsistirException("Informe a data inicial da Pesquisa!");
        }
        if (getDataFim() == null) {
            throw new ConsistirException("Informe a data final da Pesquisa!");
        }
        if (getDataInicio().after(getDataFim())) {
            throw new ConsistirException("Data inicio não pode ser igual ou posterior à data fim!");
        }
    }

    public List<SGPAvaliacaoFisicaTO> consultar() throws ConsistirException {
        List<SGPAvaliacaoFisicaTO> listaConsultada = new ArrayList<>();
        try {
            SGPAvaliacaoFisicaTO aux = processarTurma();
            listaConsultada.add(aux);
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
        return listaConsultada;
    }

    private SGPAvaliacaoFisicaTO processarTurma() throws Exception {
        // prepara o objeto que sera retornado
        SGPAvaliacaoFisicaTO retorno = new SGPAvaliacaoFisicaTO();
        List<UtilizacaoAvaliacaoFisicaVO> listaUtilizacoes;
        UtilizacaoAvaliacaoFisica utilizacaoAvaliacaoFisica = new UtilizacaoAvaliacaoFisica(con);
        listaUtilizacoes = utilizacaoAvaliacaoFisica.findByDataPrimeiraUtilizacao(dataInicio, dataFim, getEmpresa());
        for (UtilizacaoAvaliacaoFisicaVO utilizacao : listaUtilizacoes) {
            // diferencia as presencas deste aluno pela categoria
            TipoCategoria tipo = TipoCategoria.getTipoCategoria(utilizacao.getClienteVO().getCategoria().getTipoCategoria());
            if (tipo != null) {
                switch (tipo) {
                    case COMERCIARIO:
                        retorno.addQtdeComerciario(1);
                        retorno.getListaComerciario().add(utilizacao.getClienteVO());
                        break;
                    case DEPENDENTE:
                        retorno.addQtdeDependente(1);
                        retorno.getListaDependentes().add(utilizacao.getClienteVO());
                        break;
                    case USUARIO:
                        retorno.addQtdeUsuario(1);
                        retorno.getListaUsuarios().add(utilizacao.getClienteVO());
                        break;
                }
            }
        }
        retorno.calculaTotalCategorias();
        return retorno;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }
}

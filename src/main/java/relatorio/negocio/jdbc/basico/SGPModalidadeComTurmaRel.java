package relatorio.negocio.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Presenca;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Turma;
import relatorio.negocio.comuns.basico.SGPModalidadeComTurmaTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class SGPModalidadeComTurmaRel extends SuperRelatorio {
    private Date dataInicio;
    private Date dataFim;
    private EmpresaVO empresa;
    private List<ModalidadeVO> modalidades = new ArrayList<>();
    private boolean isVisualizacaoTurma;
    private List<Integer> turmasSelecionadas;

    public SGPModalidadeComTurmaRel() throws Exception {
        super();
        setIdEntidade("SGPModalidadeComTurmaRel");
        inicializar();
        inicializarParametros();
    }

    public SGPModalidadeComTurmaRel(Connection con) throws Exception {
        super(con);
        setIdEntidade("SGPModalidadeComTurmaRel");
        inicializar();
        inicializarParametros();
    }

    private void inicializarParametros() {
        setDataInicio(new Date());
        setDataFim(new Date());
        setEmpresa(new EmpresaVO());
        setModalidades(new ArrayList<>());
    }

    public void setarParametrosConsulta(Date dataInicio, Date dataFim, EmpresaVO empresa, List<ModalidadeVO> modalidades, boolean isVisualizarPorTurma,
                                        List<Integer> turmasSelecionadas) {
        setDataInicio(dataInicio);
        setDataFim(dataFim);
        setEmpresa(empresa);
        setModalidades(modalidades.stream().filter(ModalidadeVO::getSelecionado).collect(Collectors.toList()));
        setVisualizacaoTurma(isVisualizarPorTurma);
        setTurmasSelecionadas(turmasSelecionadas);

    }

    public void setarParametrosConsulta(Date dataInicio, Date dataFim, EmpresaVO empresa, List<ModalidadeVO> modalidades) {
        setarParametrosConsulta(dataInicio, dataFim, empresa, modalidades, false, null);

    }


    public void validarDados() throws ConsistirException {
        if (getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Uma empresa Deve Ser Selecionada!");
        }
        if (getDataInicio() == null) {
            throw new ConsistirException("Informe a data inicial da Pesquisa!");
        }
        if (getDataFim() == null) {
            throw new ConsistirException("Informe a data final da Pesquisa!");
        }
        if (getDataInicio().compareTo(getDataFim()) >= 0) {
            throw new ConsistirException("Data inicio não pode ser igual ou posterior à data fim!");
        }
        if (getModalidades().isEmpty()) {
            throw new ConsistirException("Selecione pelo menos uma modalidade!");
        }
    }

    public List<SGPModalidadeComTurmaTO> consultar() throws ConsistirException {
        List<SGPModalidadeComTurmaTO> listaConsultada = new ArrayList<>();
        SGPModalidadeComTurmaTO totalizador = new SGPModalidadeComTurmaTO();
        totalizador.setModalidadeVO(new ModalidadeVO());
        totalizador.getModalidadeVO().setNome("TOTAIS");
        try {
            consultarPorModalidadeComTurma(listaConsultada, totalizador);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ConsistirException(e.getMessage());
        }
        return listaConsultada;
    }

    private void consultarPorModalidade(List<SGPModalidadeComTurmaTO> listaConsultada, SGPModalidadeComTurmaTO totalizador, ModalidadeVO modalidadeVO) throws Exception {
        SGPModalidadeComTurmaTO aux = processarTurma(modalidadeVO, new Turma(con));
        listaConsultada.add(aux);
        totalizador.adicionaFrequencias(aux);
    }

    private void consultarPorModalidadeComTurma(List<SGPModalidadeComTurmaTO> listaConsultada, SGPModalidadeComTurmaTO totalizador) throws Exception {
        Turma turma = new Turma(con);
        for (ModalidadeVO modalidadeVO : modalidades) {
            if (!this.isVisualizacaoTurma()) {
                consultarPorModalidade(listaConsultada, totalizador, modalidadeVO);
            } else {
                List<TurmaVO> listTurmasPorModalidade =
                        turma.consultarPorCodigoModalidade(modalidadeVO.getCodigo(), getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                //uma turma selecionada em especifico
                if (this.getTurmasSelecionadas() != null && this.getTurmasSelecionadas().size() > 0) {
                    listTurmasPorModalidade = listTurmasPorModalidade
                            .stream().filter(tur -> turmasSelecionadas.contains(tur.getCodigo()))
                            .collect(Collectors.toList());
                }
                if (listTurmasPorModalidade.isEmpty()) {
                    consultarPorModalidade(listaConsultada, totalizador, modalidadeVO);
                } else {
                    for (TurmaVO turmaVO : listTurmasPorModalidade) {
                        SGPModalidadeComTurmaTO aux = processarTurma(modalidadeVO, turma, turmaVO);
                        listaConsultada.add(aux);
                        totalizador.adicionaFrequencias(aux);

                    }
                }
            }
        }

        listaConsultada.add(totalizador);
    }

    private SGPModalidadeComTurmaTO processarTurma(ModalidadeVO modalidadeVO, Turma turma) throws Exception {
        return processarTurma(modalidadeVO, turma, null);
    }

    private SGPModalidadeComTurmaTO processarTurma(ModalidadeVO modalidadeVO, Turma turma, TurmaVO turmaVO) throws Exception {
        // prepara o objeto que sera retornado
        SGPModalidadeComTurmaTO retorno = new SGPModalidadeComTurmaTO();
        retorno.setModalidadeVO(modalidadeVO);
        retorno.setTurmaVO(turmaVO);
        MatriculaAlunoHorarioTurma matriculaAlunoHorarioTurma = new MatriculaAlunoHorarioTurma(con);
        HorarioTurma horarioTurma = new HorarioTurma(con);
        Presenca presenca = new Presenca(con);

        retorno.setListaCanceladosDesistentes(matriculaAlunoHorarioTurma.contarEvasaoPorModalidadeNoPeriodo(dataInicio, dataFim, modalidadeVO, getEmpresa(), turmaVO));
        retorno.setContratosCanceladosDesistentes(retorno.getListaCanceladosDesistentes().size());
        retorno.setQtdTurmasCriadasPeriodo(turma.contarTurmasPorDataInicialVigenciaModalidade(dataInicio, dataFim, getEmpresa().getCodigo(), modalidadeVO.getCodigo(), turmaVO));
        retorno.setQtdAulasPeriodo(horarioTurma.contarAulasPorPeriodoModalidade(dataInicio, dataFim, getEmpresa().getCodigo(), modalidadeVO.getCodigo(), turmaVO));
        retorno.setFrequenciaPeriodo(presenca.contarPresencasPorPeriodoModalidade(dataInicio, dataFim, getEmpresa().getCodigo(), modalidadeVO.getCodigo(), turmaVO));
        int totalVagas = horarioTurma.contarVagasTurmasPorPeriodoModalidade(dataInicio, dataFim, getEmpresa().getCodigo(), modalidadeVO.getCodigo(), turmaVO);
        int totalVagasOcupadas = horarioTurma.contarVagasOcupadasTurmasPorPeriodoModalidade(dataInicio, dataFim, getEmpresa().getCodigo(), modalidadeVO.getCodigo(), turmaVO);
        retorno.setFrequenciaPossivel(retorno.getFrequenciaPeriodo() * totalVagas);
        retorno.setTotalVagasDisponiveis(totalVagas - totalVagasOcupadas);
        //retorno.setTurmaVO(turma.consultarPorCodigoModalidade(modalidadeVO.getCodigo(), getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

        List<ConsultarAlunosTurmaVO> listaAlunos = matriculaAlunoHorarioTurma.contarEntradaPorModalidadeNoPeriodo(dataInicio, dataFim, modalidadeVO, getEmpresa().getCodigo(), turmaVO);
        for (ConsultarAlunosTurmaVO aluno : listaAlunos) {
            // diferencia as presencas deste aluno pela categoria
            TipoCategoria tipo = TipoCategoria.getTipoCategoria(aluno.getClienteVO().getCategoria().getTipoCategoria());
            if (tipo != null) {
                switch (tipo) {
                    case NAO_SOCIO:
                        retorno.addQtdeNaoSocio(1);
                        retorno.getListaNaoSocio().add(aluno.getClienteVO());
                        break;
                    case ALUNO:
                        retorno.addQtdeAlunos(1);
                        retorno.getListaAlunos().add(aluno.getClienteVO());
                        break;
                    case SOCIO:
                        retorno.addQtdeSocios(1);
                        retorno.getListaSocios().add(aluno.getClienteVO());
                        break;
                    case COMERCIARIO:
                        retorno.addQtdeComerciario(1);
                        retorno.getListaComerciario().add(aluno.getClienteVO());
                        break;
                    case DEPENDENTE:
                        retorno.addQtdeDependente(1);
                        retorno.getListaDependentes().add(aluno.getClienteVO());
                        break;
                    case USUARIO:
                        retorno.addQtdeUsuario(1);
                        retorno.getListaUsuarios().add(aluno.getClienteVO());
                        break;
                    case EVENTOS:
                        retorno.addQtdeEventos(1);
                        retorno.getListaEventos().add(aluno.getClienteVO());
                        break;
                }
            }
        }
        retorno.calculaTotalCategorias();
        return retorno;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<ModalidadeVO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeVO> modalidades) {
        this.modalidades = modalidades;
    }

    public boolean isVisualizacaoTurma() {
        return isVisualizacaoTurma;
    }

    public void setVisualizacaoTurma(boolean visualizacaoTurma) {
        isVisualizacaoTurma = visualizacaoTurma;
    }


    public List<Integer> getTurmasSelecionadas() {
        return turmasSelecionadas;
    }

    public void setTurmasSelecionadas(List<Integer> turmasSelecionadas) {
        this.turmasSelecionadas = turmasSelecionadas;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.basico;

import br.com.pactosolucoes.comuns.to.FiltrosConsultaRelatorioBVsTO;
import br.com.pactosolucoes.comuns.util.Declaracao;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.basico.enumerador.TipoServicoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import relatorio.negocio.comuns.basico.BVsRelatorioListasTO;
import relatorio.negocio.comuns.basico.BVsRelatorioTO;
import relatorio.negocio.comuns.basico.ResumoClientesBV;

/**
 * Usado para todas as consultas dos relatório
 * <AUTHOR>
 */
public class RelatorioBVs extends SuperEntidade {


    private int i = 0;
    StringBuilder sqlPrincipalRelatorio = new StringBuilder();

    public RelatorioBVs() throws Exception {
        super();
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>QuestionarioPerguntaVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public List<PerguntaClienteVO> consultarPerguntaCliente(Integer codigoQuestionario) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder().append("select distinct rtrim(perguntacliente.descricao) as pergunta,"
                + " perguntacliente.simples, perguntacliente.textual, perguntacliente.multipla from questionarioperguntacliente as qpc ");
        sql.append("inner join perguntacliente on perguntacliente.codigo= qpc.perguntacliente ");
        sql.append("inner join questionariocliente on questionariocliente.codigo = qpc.questionariocliente ");

        if (codigoQuestionario != null && codigoQuestionario != 0) {
            sql.append("where questionariocliente.questionario = ? ");
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (codigoQuestionario != null && codigoQuestionario != 0) {
            sqlConsultar.setInt(1, codigoQuestionario.intValue());
        }
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        List<PerguntaClienteVO> listaPerguntas = new ArrayList<PerguntaClienteVO>();
        while (tabelaResultado.next()) {
            PerguntaClienteVO perguntaVO = new PerguntaClienteVO();
            perguntaVO.setDescricao(tabelaResultado.getString("pergunta"));
            perguntaVO.setTextual(tabelaResultado.getBoolean("textual"));
            perguntaVO.setMultipla(tabelaResultado.getBoolean("multipla"));
            perguntaVO.setSimples(tabelaResultado.getBoolean("simples"));
            listaPerguntas.add(perguntaVO);
        }
        return listaPerguntas;
    }

    public List<PerguntaClienteVO> consultarPerguntasNoQuestionario(Integer codigoQuestionario) throws Exception {
        consultar(getIdEntidade(), false);
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT p.codigo, p.descricao, p.tipopergunta FROM pergunta p ");
        sql.append(" INNER JOIN questionariopergunta qp on qp.pergunta = p.codigo ");
        sql.append(" WHERE qp.questionario = ? ");
        sql.append(" ORDER BY descricao ");

        if(codigoQuestionario == null) {
            codigoQuestionario = 0;
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        sqlConsultar.setInt(1, codigoQuestionario.intValue());

        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        List<PerguntaClienteVO> listaPerguntas = new ArrayList<PerguntaClienteVO>();
        while (tabelaResultado.next()) {
            PerguntaClienteVO perguntaVO = new PerguntaClienteVO();
            perguntaVO.setCodigo(tabelaResultado.getInt("codigo"));
            perguntaVO.setDescricao(tabelaResultado.getString("descricao"));
            perguntaVO.setTipoPergunta(tabelaResultado.getString("tipopergunta"));
            listaPerguntas.add(perguntaVO);
        }
        return listaPerguntas;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>QuestionarioVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public List<QuestionarioVO> consultarQuestionarios(Integer codigoEmpresa, boolean relatorioPesquisa) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = " select distinct (questionario.codigo) as codigo, questionario.nomeinterno as questionario from questionario"
                + " inner join questionariocliente as qc on qc.questionario = questionario.codigo"
                + " inner join cliente as c on c.codigo = qc.cliente "
                + " inner join empresa as emp on emp.codigo = c.empresa ";

        sql += " where 1 = 1 ";
        if (codigoEmpresa != null && codigoEmpresa != 0) {
            sql += " and emp.codigo = " + codigoEmpresa;
        }
        if (relatorioPesquisa) {
            sql += " and questionario.tipoquestionario = '" + TipoServicoEnum.PESQUISA.getTipo() + "' ";
        }
        else {
            sql += " and questionario.tipoquestionario <> '" + TipoServicoEnum.PESQUISA.getTipo() + "' ";
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        List<QuestionarioVO> listaQuestionarios = new ArrayList<QuestionarioVO>();
        while (tabelaResultado.next()) {
            QuestionarioVO questionario = new QuestionarioVO();
            questionario.setCodigo(tabelaResultado.getInt("codigo"));
            questionario.setTituloPesquisa(tabelaResultado.getString("questionario"));
            listaQuestionarios.add(questionario);
        }
        return listaQuestionarios;
    }

    public List<QuestionarioVO> consultarQuestionariosPorTipo(TipoServicoEnum tipoServicoEnum, boolean somenteAtivos) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = " select * from questionario where 1 = 1 ";
        if (tipoServicoEnum != null) {
            sql += " and tipoquestionario = '" + tipoServicoEnum.getTipo() + "' ";
        }
        if (somenteAtivos) {
            sql += " and ativo = true ";
        }
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        List<QuestionarioVO> listaQuestionarios = new ArrayList<QuestionarioVO>();
        while (tabelaResultado.next()) {
            QuestionarioVO questionario = new QuestionarioVO();
            questionario.setCodigo(tabelaResultado.getInt("codigo"));
            questionario.setNomeInterno(tabelaResultado.getString("nomeinterno"));
            listaQuestionarios.add(questionario);
        }
        return listaQuestionarios;
    }

    /**
     * Responsável por montar a sql do relatório de bvs
     * <AUTHOR> 19/06/2012
     * @return
     * @throws Exception
     */
    public BVsRelatorioTO montarRelatorioBVs(FiltrosConsultaRelatorioBVsTO filtros) throws Exception {
        sqlPrincipalRelatorio = new StringBuilder();
        //inicializa o indice dos parametros
        i = 0;
        //selecionar dados de resposta, totalizador de clientes que escolheram cada resposta
        sqlPrincipalRelatorio.append("SELECT distinct (rp.descricaorespota) as Resposta, count(DISTINCT cliente.codigo) as totalClientes \n");

        sqlPrincipalRelatorio.append("FROM questionariocliente as qc  \n");
        sqlPrincipalRelatorio.append("inner join questionario as quest on quest.codigo = qc.questionario \n");
        sqlPrincipalRelatorio.append("inner join cliente on cliente.codigo = qc.cliente \n");
        sqlPrincipalRelatorio.append("inner join pessoa on pessoa.codigo = cliente.pessoa \n");
        sqlPrincipalRelatorio.append("inner join questionarioperguntacliente on questionarioperguntacliente.questionariocliente= qc.codigo \n");
        sqlPrincipalRelatorio.append("inner join perguntacliente on perguntacliente.codigo= questionarioperguntacliente.perguntacliente \n");
        sqlPrincipalRelatorio.append("inner join respostapergcliente as rp on rp.perguntacliente = perguntacliente.codigo \n");
        //adicionando inner join para pesquisa por empresa
        sqlPrincipalRelatorio.append("left join empresa on empresa.codigo = cliente.empresa \n");

        //se escolheu evento adicionar inner join de evento
        if (filtros.getEventoVO().getCodigo() != 0) {
            sqlPrincipalRelatorio.append("left join evento on evento.codigo = qc.evento \n");
        }
        sqlPrincipalRelatorio.append("left join contrato con on cliente.pessoa = con.pessoa \n");

        //sempre considerando a resposta que o cliente marcou
        StringBuilder sqlWhere = new StringBuilder(" WHERE   \n");
        //considerar todos os tipos de respostas
        if (filtros.getRespostaSubjetiva().isEmpty()) {
            sqlWhere.append(" (respostaopcao OR perguntacliente.textual) AND rp.descricaorespota IS NOT NULL AND rp.descricaorespota <> '' \n");

        } else {
            sqlWhere.append(" rp.descricaorespota ilike ? and perguntacliente.textual  \n");
        }
        informarFiltros(filtros, sqlPrincipalRelatorio, sqlWhere);
        sqlWhere.append(" and empresa.codigo = ? \n");
        sqlPrincipalRelatorio.append(sqlWhere);
        BVsRelatorioTO bVsRelatorioTO = new BVsRelatorioTO();
        for (String descricaoPergunta : filtros.getListaDescricoesPergunta()) {
            bVsRelatorioTO = consultarPorCadaPergunta(descricaoPergunta, sqlPrincipalRelatorio, filtros);
        }

        return bVsRelatorioTO;
    }

    /**
     * Responsável por adicionar as condições no sql
     * <AUTHOR> 19/06/2012
     * @param sqlPrincipal
     * @param sqlWhere
     */
    private void informarFiltros(FiltrosConsultaRelatorioBVsTO filtros, StringBuilder sqlPrincipal, StringBuilder sqlWhere) {
        String listaParametros = "";

        //para intervalo de periodo
        if (filtros.getTipoConsulta() == 1) {
            if (filtros.getDataInicial() != null && filtros.getDataFinal() != null) {
                listaParametros += " AND  qc.data between ? and ? ";
                sqlPrincipal.append(" AND (con.datalancamento between ? and ?) ");
            }
        }
        //para mês ou ano
        if (filtros.getTipoConsulta() == 2) {
            if (filtros.getAno() != null) {
                listaParametros += " AND date_part('year', qc.data) = date_part('year', Cast (? as Date))  ";
                sqlPrincipal.append(" AND (date_part('year', con.datalancamento) = date_part('year', Cast (? as Date))) ");
            }
            if (filtros.getCodigoMes() != null && filtros.getCodigoMes() != 0) {
                listaParametros += "  AND date_part('month', qc.data) = date_part('month', Cast (? as Date))  ";
                sqlPrincipal.append(" AND (date_part('month', con.datalancamento) = date_part('month', Cast (? as Date))) ");
            }
        }
        //para evento
        if (filtros.getEventoVO().getCodigo() != null && filtros.getEventoVO().getCodigo() != 0) {
            listaParametros += " and qc.evento = ?";
        }
        //para questionario
        if (filtros.getCodigoQuestionario() != null && filtros.getCodigoQuestionario() != 0) {
            listaParametros += " and qc.questionario = ?";
        }
        if (!UteisValidacao.emptyList(filtros.getConsultores())) {
            StringBuilder filtrosConsultor = new StringBuilder();
            for (ColaboradorVO obj : filtros.getConsultores()) {
                filtrosConsultor.append(", ").append(obj.getCodigo());
            }
            filtrosConsultor = new StringBuilder(filtrosConsultor.toString().replaceFirst(",", ""));
            listaParametros += "and qc.consultor in (" + filtrosConsultor + ")";
        }

        sqlWhere.append(listaParametros);
    }

    /**
     *  Responsável por preencher a lista de parametros de acordo com os filtros informados
     * @param filtros
     * @param dc
     * @throws Exception
     */
    private void preencherFiltros(FiltrosConsultaRelatorioBVsTO filtros, Declaracao dc) throws Exception {
        i = 0;
        //para intervalo de periodo - Intervalo contrato
        if (filtros.getTipoConsulta() == 1) {
            if (filtros.getDataInicial() != null && filtros.getDataFinal() != null) {
                dc.setTimestamp(++i, Uteis.getDataHoraJDBC(filtros.getDataInicial(), "00:00:00"));
                dc.setTimestamp(++i, Uteis.getDataHoraJDBC(filtros.getDataFinal(), "23:59:59"));
            }
        }
        //para mês ou ano - Intervalo contrato
        if (filtros.getTipoConsulta() == 2) {
            if (filtros.getAno() != null) {
                dc.setDate(++i, Uteis.getDataJDBC(filtros.getAno()));
            }
            if (filtros.getCodigoMes() != null && filtros.getCodigoMes().intValue() != 0) {
                Calendar calendar = Calendario.getInstance();
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.MONTH, filtros.getCodigoMes() - 1);
                dc.setDate(++i, Uteis.getDataJDBC(calendar.getTime()));
            }
        }
        //tipo de respostas
        if (!filtros.getRespostaSubjetiva().isEmpty()) {
            dc.setString(++i, "%" + filtros.getRespostaSubjetiva() + "%");
        }
        //para intervalo de periodo
        if (filtros.getTipoConsulta() == 1) {
            if (filtros.getDataInicial() != null && filtros.getDataFinal() != null) {
                dc.setTimestamp(++i, Uteis.getDataHoraJDBC(filtros.getDataInicial(), "00:00:00"));
                dc.setTimestamp(++i, Uteis.getDataHoraJDBC(filtros.getDataFinal(), "23:59:59"));
            }
        }
        //para mês ou ano
        if (filtros.getTipoConsulta() == 2) {
            if (filtros.getAno() != null) {
                dc.setDate(++i, Uteis.getDataJDBC(filtros.getAno()));
            }
            if (filtros.getCodigoMes() != null && filtros.getCodigoMes().intValue() != 0) {
                Calendar calendar = Calendario.getInstance();
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.MONTH, filtros.getCodigoMes() - 1);
                dc.setDate(++i, Uteis.getDataJDBC(calendar.getTime()));
            }
        }
        //para evento
        if (filtros.getEventoVO().getCodigo() != null && filtros.getEventoVO().getCodigo() != 0) {
            dc.setInt(++i, filtros.getEventoVO().getCodigo().intValue());
        }
        //para questionario
        if (filtros.getCodigoQuestionario() != null && filtros.getCodigoQuestionario() != 0) {
            dc.setInt(++i, filtros.getCodigoQuestionario().intValue());
        }
        // empresa
        if (!UteisValidacao.emptyNumber(filtros.getCodigoEmpresa())) {
            dc.setInt(++i, filtros.getCodigoEmpresa());
        }
    }

    public BVsRelatorioTO consultarPorCadaPergunta(String descricaoPergunta, StringBuilder sql, FiltrosConsultaRelatorioBVsTO filtros) throws SQLException, Exception {
        sql.append("and rtrim(perguntacliente.descricao) ilike rtrim(?) \n");
        sql.append(" group by rp.descricaorespota \n");
        Declaracao dc = new Declaracao(sql.toString(), con);
        preencherFiltros(filtros, dc);
        dc.setString(++i, descricaoPergunta);
        ResultSet rs = dc.executeQuery();
        BVsRelatorioTO bVsRelatorioTO = new BVsRelatorioTO();
        bVsRelatorioTO.setListaBVsRelatorioListasTO(new ArrayList<BVsRelatorioListasTO>());

        List<TipoContratoEnum> listaTipoContratos = new ArrayList<TipoContratoEnum>();
        listaTipoContratos.add(TipoContratoEnum.AGENDADO);
        listaTipoContratos.add(TipoContratoEnum.ESPONTANEO);

        List<TipoBVEnum> listaTipoBV = new ArrayList<TipoBVEnum>();
        listaTipoBV.add(TipoBVEnum.MA);
        Date inicioContrato = filtros.getDataInicial();
        Date fimContrato = filtros.getDataFinal();
        Date inicioBV = filtros.getDataInicial();
        Date fimBV = filtros.getDataFinal();

        Integer codigoQuestionrio = filtros.isSomentePerguntasNoQuestionario() ? filtros.getCodigoQuestionario() : 0;
        if(inicioContrato == null && fimContrato == null){
            inicioContrato = Calendario.setMesData(filtros.getAno(),filtros.getCodigoMes() - 1);
            fimContrato = Uteis.obterUltimoDiaMesUltimaHora(inicioContrato);
        }
        if(inicioBV != null && fimBV!= null){
            inicioBV = Calendario.getDataComHoraZerada(filtros.getDataInicial());
            fimBV = Calendario.getDataComUltimaHora(filtros.getDataFinal());
            inicioContrato = Calendario.getDataComHoraZerada(filtros.getDataInicial());
            fimContrato = Calendario.getDataComUltimaHora(filtros.getDataFinal());
        } else if (inicioBV == null && fimBV == null){
            inicioBV = Calendario.setMesData(filtros.getAno(),filtros.getCodigoMes() - 1);
            fimBV = Calendario.setMesData(filtros.getAno(),filtros.getCodigoMes() - 1);
            inicioBV = Uteis.obterPrimeiroDiaMesPrimeiraHora(inicioBV);
            fimBV = Uteis.obterUltimoDiaMesUltimaHora(fimBV);
        }

        String filtroConsultores = "";
        if (!UteisValidacao.emptyList(filtros.getConsultores())) {
            StringBuilder codConsultores = new StringBuilder();
            for (ColaboradorVO obj : filtros.getConsultores()) {
                codConsultores.append(",").append(obj.getCodigo());
            }
            codConsultores = new StringBuilder(codConsultores.toString().replaceFirst(",", ""));
            filtroConsultores = codConsultores.toString();
        }

        HashMap<String,Integer> mapaRespostaConversao = getFacade().getCliente().montarMapaConversaoPorQuestionarioResposta(inicioBV, fimBV, inicioContrato, fimContrato, filtros.getCodigoEmpresa(),codigoQuestionrio,descricaoPergunta, filtroConsultores);
        while (rs.next()) {
            BVsRelatorioListasTO bVsRelatorioListasTO = new BVsRelatorioListasTO();
            bVsRelatorioListasTO.setRespostaVO(new RespostaPerguntaVO());
            bVsRelatorioListasTO.getRespostaVO().setDescricaoRespota(rs.getString("resposta"));
            bVsRelatorioListasTO.setTotalRespondidoPorResposta(rs.getInt("totalClientes"));
            Integer conversoes  = mapaRespostaConversao.get(rs.getString("resposta"));
            conversoes = UteisValidacao.emptyNumber(conversoes) ? 0 : conversoes;
            bVsRelatorioListasTO.setTotalConvertidos(conversoes);
            bVsRelatorioTO.setTotalConversoes(bVsRelatorioTO.getTotalConversoes() + conversoes);
            bVsRelatorioTO.getListaBVsRelatorioListasTO().add(bVsRelatorioListasTO);
            bVsRelatorioTO.setTotalGeral(bVsRelatorioTO.getTotalGeral() + bVsRelatorioListasTO.getTotalRespondidoPorResposta());
        }
        for (BVsRelatorioListasTO bVsRelatorioListasTO : bVsRelatorioTO.getListaBVsRelatorioListasTO()) {
            bVsRelatorioListasTO.setPercentualRespondidoPorResposta(Uteis.arredondarForcando2CasasDecimais(
                    (100.0 * bVsRelatorioListasTO.getTotalRespondidoPorResposta()) / bVsRelatorioTO.getTotalGeral()));
        }
        bVsRelatorioTO.setTotalConversoes(getFacade().getCliente().contarConversoesPorQuestionarioPeriodo(inicioBV, fimBV, inicioContrato, fimContrato,filtros.getCodigoEmpresa(),codigoQuestionrio, descricaoPergunta, filtroConsultores));
        bVsRelatorioTO.setPerguntaVO(getFacade().getPergunta().consultarPorCodigoExato(getFacade().getPergunta().consultarPorDescricao(descricaoPergunta),false,Uteis.NIVELMONTARDADOS_TODOS));
        if (bVsRelatorioTO.getPerguntaVO() == null) {
            bVsRelatorioTO.setPerguntaVO(new PerguntaVO());
        }
        bVsRelatorioTO.getPerguntaVO().setDescricao(descricaoPergunta);
        return bVsRelatorioTO;
    }
    /**
     * Operação responsável por localizar um objeto da classe <code>QuestionarioVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public List<ResumoClientesBV> consultarResumoClientes(Integer codigoEmpresa, String pergunta, String resposta, FiltrosConsultaRelatorioBVsTO filtros) throws Exception {
        String sqlIntermediario = sqlPrincipalRelatorio.substring(sqlPrincipalRelatorio.indexOf("FROM"), sqlPrincipalRelatorio.length() - 1);
        String sqlSemGroupBy = sqlIntermediario.substring(0, sqlIntermediario.indexOf("group by"));
        String sqlInnerJoins = sqlSemGroupBy.substring(0,sqlSemGroupBy.indexOf("WHERE"));
        String sqlWhere      = sqlSemGroupBy.substring(sqlSemGroupBy.indexOf("WHERE"), sqlSemGroupBy.length() -1);
        String sqlResultante = "SELECT cliente.codigo as codigoCliente, pessoa.codigo as codigoPessoa, pessoa.nome as nomePessoa, qc.data, quest.nomeinterno as questionario, cliente.matricula as matricula, contrato.situacaocontrato " +  sqlInnerJoins //sqlSemGroupBy
                + " left join contrato as contrato on contrato.pessoa = cliente.pessoa " + sqlWhere
                + " and rtrim(rp.descricaorespota) ilike ? and (contrato.situacaocontrato = 'MA' or contrato.situacaocontrato is null)"
                + " GROUP BY cliente.codigo, pessoa.codigo, pessoa.nome, qc.data, quest.nomeinterno, cliente.matricula, contrato.situacaocontrato";
        Declaracao dc = new Declaracao(sqlResultante.toString(), con);
        preencherFiltros(filtros, dc);
        dc.setString(++i, paramTrim(pergunta));
        dc.setString(++i, paramTrim(resposta));
        ResultSet rs = dc.executeQuery();
        List<ResumoClientesBV> listaResumo = new ArrayList<ResumoClientesBV>();
        while (rs.next()) {
            ResumoClientesBV resumoCliente = new ResumoClientesBV();
            resumoCliente.setQuestionarioClienteVO(new QuestionarioClienteVO());
            resumoCliente.getQuestionarioClienteVO().setCliente(new ClienteVO());
            resumoCliente.getQuestionarioClienteVO().getCliente().setCodigo(rs.getInt("codigoCliente"));
            resumoCliente.getQuestionarioClienteVO().getCliente().setMatricula(rs.getString("matricula"));
            resumoCliente.getQuestionarioClienteVO().getCliente().setPessoa(new PessoaVO());
            resumoCliente.getQuestionarioClienteVO().getCliente().getPessoa().setCodigo(rs.getInt("codigoPessoa"));
            resumoCliente.getQuestionarioClienteVO().getCliente().getPessoa().setNome(rs.getString("nomePessoa"));
            resumoCliente.getQuestionarioClienteVO().setData(rs.getDate("data"));
            resumoCliente.getQuestionarioClienteVO().setQuestionario(new QuestionarioVO());
            resumoCliente.getQuestionarioClienteVO().getQuestionario().setNomeInterno(rs.getString("questionario"));
            if (rs.getString("situacaocontrato") != null) {
                resumoCliente.setSituacaoContrato("Convertido");
            } else {
                resumoCliente.setSituacaoContrato("Não convertido");
            }
            listaResumo.add(resumoCliente);
        }
        return listaResumo;
    }

    private String paramTrim(String param) {
        if (param != null) {
            param = param.trim();
        }
        return param;
    }
}

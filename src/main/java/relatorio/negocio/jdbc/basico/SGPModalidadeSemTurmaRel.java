package relatorio.negocio.jdbc.basico;

import br.com.pactosolucoes.ce.comuns.enumerador.TipoCategoria;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.ConsultarAlunosTurmaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.acesso.AcessoCliente;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import relatorio.negocio.comuns.basico.SGPModalidadeSemTurmaTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class SGPModalidadeSemTurmaRel extends SuperRelatorio {
    private Date dataInicio;
    private Date dataFim;
    private EmpresaVO empresa;
    private List<ModalidadeVO> modalidades = new ArrayList<>();

    public SGPModalidadeSemTurmaRel() throws Exception {
        super();
        setIdEntidade("SGPModalidadeSemTurmaRel");
        inicializar();
        inicializarParametros();
    }

    public SGPModalidadeSemTurmaRel(Connection con) throws Exception {
        super(con);
        setIdEntidade("SGPModalidadeSemTurmaRel");
        inicializar();
        inicializarParametros();
    }

    private void inicializarParametros() {
        setDataInicio(new Date());
        setDataFim(new Date());
        setEmpresa(new EmpresaVO());
        setModalidades(new ArrayList<>());
    }

    public void setarParametrosConsulta(Date dataInicio, Date dataFim, EmpresaVO empresa, List<ModalidadeVO> modalidades) {
        setDataInicio(dataInicio);
        setDataFim(dataFim);
        setEmpresa(empresa);
        setModalidades(modalidades.stream().filter(ModalidadeVO::getSelecionado).collect(Collectors.toList()));
    }

    public void validarDados() throws ConsistirException {
        if (getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Uma empresa Deve Ser Selecionada!");
        }
        if (getDataInicio() == null) {
            throw new ConsistirException("Informe a data inicial da Pesquisa!");
        }
        if (getDataFim() == null) {
            throw new ConsistirException("Informe a data final da Pesquisa!");
        }
        if (getDataInicio().compareTo(getDataFim()) >= 0) {
            throw new ConsistirException("Data inicio não pode ser igual ou posterior à data fim!");
        }
        if (getModalidades().isEmpty()) {
            throw new ConsistirException("Selecione pelo menos uma modalidade!");
        }
    }

    public List<SGPModalidadeSemTurmaTO> consultar() throws ConsistirException {
        List<SGPModalidadeSemTurmaTO> listaConsultada = new ArrayList<>();
        SGPModalidadeSemTurmaTO totalizador = new SGPModalidadeSemTurmaTO();
        totalizador.setModalidadeVO(new ModalidadeVO());
        totalizador.getModalidadeVO().setNome("TOTAIS");
        try {
            for (ModalidadeVO modalidadeVO : modalidades) {
                SGPModalidadeSemTurmaTO aux = processarTurma(modalidadeVO);
                listaConsultada.add(aux);
                totalizador.adicionaFrequencias(aux);
            }
            listaConsultada.add(totalizador);
        } catch (Exception e) {
            throw new ConsistirException(e.getMessage());
        }
        return listaConsultada;
    }

    private SGPModalidadeSemTurmaTO processarTurma(ModalidadeVO modalidadeVO) throws Exception {
        // prepara o objeto que sera retornado
        SGPModalidadeSemTurmaTO retorno = new SGPModalidadeSemTurmaTO();
        retorno.setModalidadeVO(modalidadeVO);
        ContratoModalidade contratoModalidade = new ContratoModalidade(con);
        retorno.setListaCanceladosDesistentes(contratoModalidade.contarEvasaoPorModalidadeNoPeriodo(dataInicio, dataFim, modalidadeVO, getEmpresa()));
        retorno.setContratosCanceladosDesistentes(retorno.getListaCanceladosDesistentes().size());
        retorno.setQtdDias((int) Uteis.contarDiasUteis(dataInicio, dataFim, false, false));
        AcessoCliente acessoCliente = new AcessoCliente(con);
        retorno.setFrequenciaPeriodo(acessoCliente.contarPresencasPorPeriodoModalidade(dataInicio, dataFim, getEmpresa().getCodigo(), modalidadeVO.getCodigo()));
        retorno.setFrequenciaPossivel(modalidadeVO.getFrequenciasPossiveis());
        retorno.setVagasDisponiveis(modalidadeVO.getVagasDisponiveis());
        Contrato contrato = new Contrato(con);
        List<ConsultarAlunosTurmaVO> listaAlunos = contrato.contarEntradaPorModalidadeNoPeriodo(dataInicio, dataFim, modalidadeVO, getEmpresa());
        for (ConsultarAlunosTurmaVO aluno : listaAlunos) {
            // diferencia as presencas deste aluno pela categoria
            TipoCategoria tipo = TipoCategoria.getTipoCategoria(aluno.getClienteVO().getCategoria().getTipoCategoria());
            if(tipo != null) {
                switch (tipo) {
                    case NAO_SOCIO:
                        retorno.addQtdeNaoSocio(1);
                        retorno.getListaNaoSocio().add(aluno.getClienteVO());
                        break;
                    case ALUNO:
                        retorno.addQtdeAlunos(1);
                        retorno.getListaAlunos().add(aluno.getClienteVO());
                        break;
                    case SOCIO:
                        retorno.addQtdeSocios(1);
                        retorno.getListaSocios().add(aluno.getClienteVO());
                        break;
                    case COMERCIARIO:
                        retorno.addQtdeComerciario(1);
                        retorno.getListaComerciario().add(aluno.getClienteVO());
                        break;
                    case DEPENDENTE:
                        retorno.addQtdeDependente(1);
                        retorno.getListaDependentes().add(aluno.getClienteVO());
                        break;
                    case USUARIO:
                        retorno.addQtdeUsuario(1);
                        retorno.getListaUsuarios().add(aluno.getClienteVO());
                        break;
                    case EVENTOS:
                        retorno.addQtdeEventos(1);
                        retorno.getListaEventos().add(aluno.getClienteVO());
                        break;
                }
            }
        }

        retorno.calculaTotalCategorias();
        return retorno;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<ModalidadeVO> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ModalidadeVO> modalidades) {
        this.modalidades = modalidades;
    }
}

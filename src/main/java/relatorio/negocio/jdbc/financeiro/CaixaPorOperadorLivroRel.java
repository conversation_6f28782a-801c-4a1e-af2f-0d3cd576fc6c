package relatorio.negocio.jdbc.financeiro;

import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.util.Date;

public class CaixaPorOperadorLivroRel extends SuperRelatorio {

    private String matricula;
    private String nomePagador;
    private String formaPagamentoDescricao;
    private double valorTotalPagamento;
    private String nomeUsuarioOperacao;
    private double valorTotalSaida;
    private Date dataLancamento;

    public CaixaPorOperadorLivroRel() throws Exception {
        super();
        setIdEntidade("CaixaPorOperadorRel");
        inicializarParametros();
    }

    private void inicializarParametros() {
        setMatricula("");
        setNomePagador("");
        setFormaPagamentoDescricao("");
        setValorTotalPagamento(0.0);
        setNomeUsuarioOperacao("");
        setValorTotalSaida(0.0);

    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNomePagador() {
        return nomePagador;
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getFormaPagamentoDescricao() {
        return formaPagamentoDescricao;
    }

    public void setFormaPagamentoDescricao(String formaPagamentoDescricao) {
        this.formaPagamentoDescricao = formaPagamentoDescricao;
    }

    public double getValorTotalPagamento() {
        return valorTotalPagamento;
    }

    public void setValorTotalPagamento(double valorTotalPagamento) {
        this.valorTotalPagamento = valorTotalPagamento;
    }

    public String getNomeUsuarioOperacao() {
        return nomeUsuarioOperacao;
    }

    public void setNomeUsuarioOperacao(String nomeUsuarioOperacao) {
        this.nomeUsuarioOperacao = nomeUsuarioOperacao;
    }

    public double getValorTotalSaida() {
        return valorTotalSaida;
    }

    public void setValorTotalSaida(double valorTotalSaida) {
        this.valorTotalSaida = valorTotalSaida;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }
}

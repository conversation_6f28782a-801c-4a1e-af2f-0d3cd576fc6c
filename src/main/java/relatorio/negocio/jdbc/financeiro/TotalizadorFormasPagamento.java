package relatorio.negocio.jdbc.financeiro;

import java.util.List;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.FormaPagamentoVO;

public class TotalizadorFormasPagamento extends SuperTO {

	public static final int ORDEM_TITULO_ESPECIE = 0;
	public static final int ORDEM_ESPECIE = 1;
	public static final int ORDEM_TOTAL_ESPECIE = 2;
        public static final int ORDEM_TITULO_BOLETO = 3;
        public static final int ORDEM_BOLETO = 4;
        public static final int ORDEM_TOTAL_BOLETO = 5;
        public static final int ORDEM_TITULO_ESPECIE_BOLETO = 6;
        public static final int ORDEM_TOTAL_ESPECIE_BOLETO = 7;
        public static final int ORDEM_TITULO_DEVOLUCAO = 8;
        public static final int ORDEM_DEVOLUCAO = 9;
        public static final int ORDEM_TITULO_ESPECIE_BOLETO_DEVOLUCOES = 10;
        public static final int ORDEM_TOTAL_ESPECIE_BOLETO_DEVOLUCOES = 11;
        public static final int ORDEM_TITULO_OUTRASINFORMACOES = 12;
        public static final int ORDEM_DEV_RECEBIVEIS = 13;
	public static final int ORDEM_NAO_ESPECIE = 14;
        public static final int ORDEM_ESPACO_EDICOES = 15;
        public static final int ORDEM_TITULO_EDICOES = 16;
        public static final int ORDEM_EDICOES_ENTRADAS = 17;
        public static final int ORDEM_EDICOES_SAIDAS = 18;
        
	
	
	private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
	private Double valor = 0.0;
	private Integer qtd = 0;
	private boolean chequeAVista = false;
	private boolean chequeAPrazo = false;
	private boolean titulo = false;
	private String descricao = "";
	private int ordem = 0;
	private boolean total = false;
	private boolean devolucao = false;
	
	/**
	 * @param formaPagamento the formaPagamento to set
	 */
	public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
		this.formaPagamento = formaPagamento;
	}
	
	public String getTipo(){
		return formaPagamento.getTipoFormaPagamento();
	}
	/**
	 * @return the formaPagamento
	 */
	public FormaPagamentoVO getFormaPagamento() {
		return formaPagamento;
	}
	/**
	 * @param valor the valor to set
	 */
	public void setValor(Double valor) {
		this.valor = valor;
	}
	/**
	 * @return the valor
	 */
	public Double getValor() {
		return valor;
	}
	/**
	 * @param qtd the qtd to set
	 */
	public void setQtd(Integer qtd) {
		this.qtd = qtd;
	}
	/**
	 * @return the qtd
	 */
	public Integer getQtd() {
		return qtd;
	}
	/**
	 * @param chequeAPrazo the chequeAPrazo to set
	 */
	public void setChequeAPrazo(boolean chequeAPrazo) {
		this.chequeAPrazo = chequeAPrazo;
	}
	/**
	 * @return the chequeAPrazo
	 */
	public boolean getChequeAPrazo() {
		return chequeAPrazo;
	}
	/**
	 * @param chequeAVista the chequeAVista to set
	 */
	public void setChequeAVista(boolean chequeAVista) {
		this.chequeAVista = chequeAVista;
	}
	/**
	 * @return the chequeAVista
	 */
	public boolean getChequeAVista() {
		return chequeAVista;
	}
	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}
	/**
	 * @param ordem the ordem to set
	 */
	public void setOrdem(int ordem) {
		this.ordem = ordem;
	}
	/**
	 * @return the ordem
	 */
	public int getOrdem() {
		return ordem;
	}
	/**
	 * @param total the total to set
	 */
	public void setTotal(boolean total) {
		this.total = total;
	}
	/**
	 * @return the total
	 */
	public boolean getTotal() {
		return total;
	}

	/**
	 * @param titulo the titulo to set
	 */
	public void setTitulo(boolean titulo) {
		this.titulo = titulo;
	}

	/**
	 * @return the titulo
	 */
	public boolean getTitulo() {
		return titulo;
	}
	
	public static TotalizadorFormasPagamento getTituloEspecie(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("Espécie");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_TITULO_ESPECIE);
		return tot;
		
	}
        public static TotalizadorFormasPagamento getTituloDevolucao(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("Devolução");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_TITULO_DEVOLUCAO);
		return tot;
		
	}
        
        public static TotalizadorFormasPagamento getTituloBoleto(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("Boleto");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_TITULO_BOLETO);
		return tot;
		
	}
	public static TotalizadorFormasPagamento getTituloOutrasInformacoes(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("Outras Informacões");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_TITULO_OUTRASINFORMACOES);
		return tot;
		
	}
        
        
        public static TotalizadorFormasPagamento getTituloTotalEspecieBoleto(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("Recebido em Especie + Boleto");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_TITULO_ESPECIE_BOLETO);
		return tot;
		
	}
        
        public static TotalizadorFormasPagamento getEspacoEdicoes(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_ESPACO_EDICOES);
		return tot;
		
	}
        
        public static TotalizadorFormasPagamento getTituloEdicoes(){
		TotalizadorFormasPagamento tot = new TotalizadorFormasPagamento();
		tot.setDescricao("Edições de Pagamento");
		tot.setTitulo(true);
		tot.setOrdem(ORDEM_TITULO_EDICOES);
		return tot;
		
	}
        

	/**
	 * @param devolucao the devolucao to set
	 */
	public void setDevolucao(boolean devolucao) {
		this.devolucao = devolucao;
	}

	/**
	 * @return the devolucao
	 */
	public boolean getDevolucao() {
		return devolucao;
	}
	
	public static void inserirDevolucoes(List<TotalizadorFormasPagamento> lista, double valorDevolucoes, int qtdDV,
                                         double valorDevolucoesRecebiveis, int qtdDR){
            TotalizadorFormasPagamento devolucao = new TotalizadorFormasPagamento();
            boolean existeTituloOutrasInformacoes = false;
            if (qtdDV > 0) {
                devolucao.setDescricao("DEVOLUÇÃO EM DINHEIRO");
                devolucao.setValor(valorDevolucoes * -1);
                devolucao.setQtd(qtdDV);
                devolucao.setDevolucao(true);
                devolucao.setOrdem(TotalizadorFormasPagamento.ORDEM_DEVOLUCAO);
                lista.add(TotalizadorFormasPagamento.getTituloDevolucao());
                lista.add(devolucao);
            }

            if(qtdDR > 0){
                TotalizadorFormasPagamento devRec = new TotalizadorFormasPagamento();
                devRec.setDescricao("DEVOLUÇÃO DE RECEBÍVEIS");
                devRec.setValor(valorDevolucoesRecebiveis * -1);
                devRec.setQtd(qtdDR);
                devRec.setOrdem(TotalizadorFormasPagamento.ORDEM_DEV_RECEBIVEIS);
                devRec.setDevolucao(true);
                lista.add(devRec);
                lista.add(TotalizadorFormasPagamento.getTituloOutrasInformacoes());
                existeTituloOutrasInformacoes = true;
            }
            boolean possuiEspecie = false;
            boolean possuiBoleto = false;
            boolean possuiNaoEspecie = false;
            boolean possuiTotBoletoEspecie = false;
            boolean possuiEdicoes = false;
            double valorTotEspecie = 0.0;
            double valorTotBoleto = 0.0;
            for(TotalizadorFormasPagamento tot : lista){
                if(tot.getOrdem() == ORDEM_TOTAL_ESPECIE){
                        valorTotEspecie = tot.getValor();
                }
                if(tot.getOrdem() == ORDEM_TOTAL_BOLETO){
                        valorTotBoleto = tot.getValor();
                }
                if(tot.getOrdem() == ORDEM_ESPECIE){
                        possuiEspecie = true;
                }
                if(tot.getOrdem() == ORDEM_BOLETO){
                        possuiBoleto = true;
                }
                if(tot.getOrdem() == ORDEM_NAO_ESPECIE){
                        possuiNaoEspecie = true;
                }
                if (tot.getOrdem() == ORDEM_TOTAL_ESPECIE_BOLETO){
                    possuiTotBoletoEspecie = true;
                }
                if (tot.getOrdem() == ORDEM_EDICOES_ENTRADAS || tot.getOrdem() == ORDEM_EDICOES_SAIDAS){
                    possuiEdicoes = true;
                }
            }
            if(possuiEspecie){
                 lista.add(TotalizadorFormasPagamento.getTituloEspecie());
            }
            if(possuiBoleto){
                 lista.add(TotalizadorFormasPagamento.getTituloBoleto());
            }
            if(possuiNaoEspecie && !existeTituloOutrasInformacoes){
                    lista.add(TotalizadorFormasPagamento.getTituloOutrasInformacoes());
            }
            if(possuiTotBoletoEspecie){
                    lista.add(TotalizadorFormasPagamento.getTituloTotalEspecieBoleto());
            }
            if(possuiEdicoes){
                 lista.add(TotalizadorFormasPagamento.getEspacoEdicoes());
                 lista.add(TotalizadorFormasPagamento.getTituloEdicoes());
            }
            if(valorDevolucoes > 0.0 && (valorTotBoleto > 0.0 || valorTotEspecie > 0.0)){
               TotalizadorFormasPagamento totalBoletoEspecieDev = new TotalizadorFormasPagamento();
                totalBoletoEspecieDev.setTotal(true);
                totalBoletoEspecieDev.setOrdem(TotalizadorFormasPagamento.ORDEM_TOTAL_ESPECIE_BOLETO_DEVOLUCOES);
                totalBoletoEspecieDev.setValor(valorTotBoleto + valorTotEspecie - valorDevolucoes);
                lista.add(totalBoletoEspecieDev);
            }
        }
                 
	
	public String getLabelTotal(){
            if(getOrdem() == ORDEM_TOTAL_ESPECIE_BOLETO){
                return  "Total Espécie + Boleto";
            }
            if(getOrdem() == ORDEM_TOTAL_ESPECIE_BOLETO_DEVOLUCOES){
                return  "Espécie + Boleto - Devoluções";
            }
            return "Total";
	}
	
}

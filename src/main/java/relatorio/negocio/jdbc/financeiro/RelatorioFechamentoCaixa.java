/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.jdbc.financeiro;

import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import negocio.comuns.financeiro.CaixaVO;
import negocio.comuns.financeiro.ContaVO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.TipoContaVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.financeiro.TipoConta;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;

/**
 *
 * <AUTHOR>
 */
public class RelatorioFechamentoCaixa extends SuperRelatorioFinan{



    public  List<DemonstrativoFinanceiro> gerarDemonstrativoPorCaixa(int codigoCaixa) throws Exception{
    	 Connection con = Conexao.getFromSession();
         List<DemonstrativoFinanceiro> listaDemonstrativo = montarListaDemonstrativoFinaneiro(codigoCaixa);
         if(!listaDemonstrativo.isEmpty()){
         	MesProcessar mesProcessar = new MesProcessar();
             Calendar data = Calendario.getInstance();
             mesProcessar.setMesAno(data.get(Calendar.MONTH) + "/" + data.get(Calendar.YEAR));
             ThreadDemonstrativoFinanceiro threadDF = new ThreadDemonstrativoFinanceiro(con,
                                                                                       listaDemonstrativo,
                                                                                       mesProcessar,
                                                                                       TipoVisualizacaoRelatorioDF.PLANOCONTA,
                                                                                       listaDemonstrativo.get(listaDemonstrativo.size()-1).getCodigoAgrupador(),
                                                                                       false);
             threadDF.setMontarNivelSuperior(false);
             threadDF.processarRelatorioFechamentoCaixa(codigoCaixa, true);
             adicionarLancamentosNaTreeView(listaDemonstrativo);	
         }
         return listaDemonstrativo;

    }
    
	public List<DemonstrativoFinanceiro> gerarDemonstrativoPorCaixaPorTipoConta(int codigoCaixa, List<Integer> contasMovimentadas, 
			Map<Integer, Double> valoresIniciais, boolean usarCE) throws Exception {

		Connection con = Conexao.getFromSession();
		List<DemonstrativoFinanceiro> listaDemonstrativo = new ArrayList<DemonstrativoFinanceiro>();
		Map<String, String> codigos = montarPorConta(con, listaDemonstrativo, contasMovimentadas, valoresIniciais);

		if (!listaDemonstrativo.isEmpty()) {
			MesProcessar mesProcessar = new MesProcessar();
			Calendar data = Calendario.getInstance();
			mesProcessar.setMesAno(data.get(Calendar.MONTH) + "/" + data.get(Calendar.YEAR));
			ThreadDemonstrativoFinanceiro threadDF = new ThreadDemonstrativoFinanceiro(con, listaDemonstrativo, mesProcessar,
					TipoVisualizacaoRelatorioDF.PLANOCONTA, listaDemonstrativo.get(listaDemonstrativo.size() - 1).getCodigoAgrupador(), usarCE);
			threadDF.setMontarNivelSuperior(false);
			List<LancamentoDF> lancamentos = threadDF.processarRelatorioFechamentoCaixa(codigoCaixa, false);
			
			
			Map<String, Integer> agrupadores = new HashMap<String, Integer>();
			
			for (LancamentoDF lanc : lancamentos) {
				 if(lanc.getTipoES() != null && lanc.getTipoES().equals(TipoES.SAIDA)){
					 lanc.setValorLancamento(lanc.getValorLancamento()*-1);
				 }
				 String key = "T"+lanc.getTipoConta()+"C"+lanc.getConta();
				 DemonstrativoFinanceiro contaPai = listaDemonstrativo.get(listaDemonstrativo.indexOf(new DemonstrativoFinanceiro(codigos.get(key))));
				 contaPai.setTotalTodosMeses(contaPai.getTotalTodosMeses()+lanc.getValorLancamento());
				 contaPai.setSaldoFinal(contaPai.getSaldoFinal() + lanc.getValorLancamento());
				 
				 String codTipoPai = Uteis.obterCodigoPaiEntidadeFinanceiro(contaPai.getCodigoAgrupador());
				 DemonstrativoFinanceiro tipoContaPai = listaDemonstrativo.get(listaDemonstrativo.indexOf(new DemonstrativoFinanceiro(codTipoPai)));
				 tipoContaPai.setTotalTodosMeses(tipoContaPai.getTotalTodosMeses()+lanc.getValorLancamento());
				 
				 DemonstrativoFinanceiro dfLanc = new DemonstrativoFinanceiro();
				 dfLanc.setNomeAgrupador(lanc.getDescricaoLancamento());
				 dfLanc.setTotalTodosMeses(lanc.getValorLancamento());
				 Integer integer = agrupadores.get(contaPai.getCodigoAgrupador());
				 if(integer == null){
					 integer = 1;
				 }
				 dfLanc.setCodigoAgrupador(contaPai.getCodigoAgrupador() +"."
						 			+ (integer.toString().length() == 1 ? "00"+integer.toString() :
						 				integer.toString().length() == 2 ? "0"+integer.toString() : integer.toString()));
				 agrupadores.put(contaPai.getCodigoAgrupador(), integer++);
				 listaDemonstrativo.add(dfLanc);
			}
			
		}
		Ordenacao.ordenarLista(listaDemonstrativo, "codigoAgrupador");
		return listaDemonstrativo;

	}

	/**
	 * <AUTHOR> Alcides
	 * 05/11/2012
	 */
	public Map<String, String> montarPorConta(Connection con, List<DemonstrativoFinanceiro> lista, List<Integer> contasMovimentadas, 
			Map<Integer, Double> valoresIniciais) throws Exception{
		TipoConta tipoConta = new TipoConta(con);
		Map<String, String> codigosAgrupadores = new HashMap<String, String>();
		Integer codAgrupTipConta = 1;
		List<TipoContaVO> todas = tipoConta.consultarTodas(Uteis.NIVELMONTARDADOS_TODOS);
		
		for(TipoContaVO tipo :  todas){
			boolean possuiContaMovimentada = false;
			
			DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
			//criar agrupador
			df.setCodigoAgrupador(codAgrupTipConta.toString().length() == 1 ? "00"+codAgrupTipConta.toString() : 
										codAgrupTipConta.toString().length() == 2 ? "0"+codAgrupTipConta.toString() : codAgrupTipConta.toString());
			df.setNomeAgrupador(tipo.getDescricao());
//			adicionar a lista
			
			codAgrupTipConta++;
			Integer codAgrupConta = 1;
			//iterar na lista de contas deste tipo
			for(ContaVO conta : tipo.getContas()){
				DemonstrativoFinanceiro dfConta = new DemonstrativoFinanceiro();
				if (contasMovimentadas.contains(conta.getCodigo())) {
					possuiContaMovimentada = true;
					// criar agrupador
					String codAg = codAgrupConta.toString().length() == 1 ? "00" + codAgrupConta.toString() : codAgrupConta.toString()
							.length() == 2 ? "0" + codAgrupConta.toString() : codAgrupConta.toString();
					dfConta.setCodigoAgrupador(df.getCodigoAgrupador() + "." + codAg);
					dfConta.setNomeAgrupador(conta.getDescricao());
					dfConta.setSaldoInicial(valoresIniciais.get(conta.getCodigo()));
					dfConta.setSaldoFinal(valoresIniciais.get(conta.getCodigo()));
					dfConta.setDfConta(true);
					// adicionar ao mapa para que os lancamentos o encontrem com facilidade
					codigosAgrupadores.put("T" + tipo.getCodigo() + "C" + conta.getCodigo(), dfConta.getCodigoAgrupador());
					codAgrupConta++;
					lista.add(dfConta);
				}
			}
			if(possuiContaMovimentada){
				lista.add(df);
			}
		}
		return codigosAgrupadores;
		
	}


    private void loopVerificarTerminoDaThread(ThreadDemonstrativoFinanceiro thread)  throws Exception{
        while (!thread.isTerminouExecucao()){
            // Verifica se houve erro na execução da thread
            if (!thread.getMsgErro().equals("")){
              System.out.println("método loopVerificarTerminoDaThread achou erro na excecução da thread 'Demonstrativo por Caixa' .");
              throw new Exception(thread.getMsgErro());
            }
           Thread.sleep(1000);
        }

    }


    private DemonstrativoFinanceiro retornarDemonstrativoPai(DemonstrativoFinanceiro df, List<DemonstrativoFinanceiro> listaDF){
        DemonstrativoFinanceiro objDF = null;
        if (df.getCodigoAgrupador().length() > 3){
            String codigoPai = df.getCodigoAgrupador().substring(0, df.getCodigoAgrupador().length() - 4);
            DemonstrativoFinanceiro dfProcurar = new DemonstrativoFinanceiro();
            dfProcurar.setCodigoAgrupador(codigoPai);
            int indice = listaDF.indexOf(dfProcurar);
            if (indice >= 0){
                return listaDF.get(indice);
            }
        }
        return objDF;
    }

    private List<DemonstrativoFinanceiro> montarListaDemonstrativoFinaneiro(int codigoCaixa) throws Exception{
        List<PlanoContaTO> listaPlanoConta = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarPorCaixa(codigoCaixa);
        List<DemonstrativoFinanceiro> listaDF = new ArrayList<DemonstrativoFinanceiro>();
        for (PlanoContaTO obj: listaPlanoConta){
            DemonstrativoFinanceiro df = new  DemonstrativoFinanceiro();
            df.setCodigoAgrupador(obj.getCodigoPlano());
            df.setNomeAgrupador(obj.getDescricao());
            df.setTipoES(obj.getTipoPadrao());
            df.setDfPai(retornarDemonstrativoPai(df, listaDF));
            criarTotalizadorMes(df);
            listaDF.add(df);
         }
        
        /* Criar um totalizador para os valores que não foram definidos um plano de conta/Centro de Custo.
         * Obs.: Este totalizador deverá ser o último nível da Árvore.
         */
        String ultimoNivel;
        int nivelNaoDefinido = 1;
        NumberFormat nf = new DecimalFormat("000");
        String codigoNode;
        DemonstrativoFinanceiro dfUltimoNivel;
        // Entende-se que a lista já está ordenada no formato de árvore.
        if(!listaDF.isEmpty()){
        	dfUltimoNivel= listaDF.get(listaDF.size()-1);
            ultimoNivel = dfUltimoNivel.getCodigoAgrupador().substring(0, 3);
            nivelNaoDefinido = (Integer.parseInt(ultimoNivel) + 1);
        }
        DemonstrativoFinanceiro df = new  DemonstrativoFinanceiro();
        codigoNode = nf.format(nivelNaoDefinido);
        df.setCodigoAgrupador(codigoNode);
        df.setNomeAgrupador("Não Informado Plano de Conta");
        criarTotalizadorMes(df);
        listaDF.add(df);
      return listaDF;
    }





}

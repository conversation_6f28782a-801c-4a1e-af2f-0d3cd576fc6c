package relatorio.negocio.jdbc.financeiro;

import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.financeiro.CentroCustoTO;
import negocio.comuns.financeiro.PlanoContaTO;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.financeiro.CentroCusto;
import negocio.facade.jdbc.financeiro.PlanoConta;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;

import java.io.Serializable;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SuperRelatorioFinan implements Serializable {
    
    public List<ThreadDemonstrativoFinanceiro> listaThreads;
    private String rateioNaoDefinido;
    public boolean gerarRelatorioUsandoThread = true;
	
    public void loopVerificarTerminoDasThreads()  throws Exception{
        int totalThreads = listaThreads.size();
        int totalThreadsTerminadas = 0;
        while (totalThreadsTerminadas < totalThreads){
            for (ThreadDemonstrativoFinanceiro thread :listaThreads ){
              if (thread.isTerminouExecucao())
                totalThreadsTerminadas ++;

              // Verifica se houve erro na execução da thread
              if (!UteisValidacao.emptyString(thread.getMsgErro())){
                System.out.println("método loopVerificarTerminoDasThreads achou erro nas threads.");
                throw new Exception(thread.getMsgErro());
              }
            }
            if (totalThreadsTerminadas == totalThreads)
              break;
           totalThreadsTerminadas = 0;
           Thread.sleep(1000);
        }
      }
	
	public List<LancamentoDF> retornarListaLancamentos(DemonstrativoFinanceiro df){
        // Para o fechamento de caixa, a lista de totalizadores de meses só tem 1 registro.
        TotalizadorMesDF totalizadorMes = df.getListaTotalizadorMeses().get(0);
        return !totalizadorMes.getListaLancamentosNaoAtribuido().isEmpty()
                ? new ArrayList(totalizadorMes.getListaLancamentosNaoAtribuido())
                : new ArrayList(totalizadorMes.getListaLancamentos());
    }
	

	public void adicionarLancamentosNaTreeView(List<DemonstrativoFinanceiro> listaDemonstrativo){

       //  * Adicionar os lançamentos na treeView.
       //  * Obs.: A lista deverá está ordenada.
        DecimalFormat decimalFormat = new DecimalFormat("000");
        Integer indice = 0;
        DemonstrativoFinanceiro df;
        DemonstrativoFinanceiro dfNew;
        List<LancamentoDF> listaLancamentos;
        while (indice < listaDemonstrativo.size()){
            df = listaDemonstrativo.get(indice);
            listaLancamentos = retornarListaLancamentos(df);
            String codigoPai = "";
            if (df.getDfPai() != null)
              codigoPai = df.getDfPai().getCodigoAgrupador();
            else
              codigoPai = df.getCodigoAgrupador();
            if ((df.getDfPai() != null) && (retornarListaLancamentos(df.getDfPai()).size() > 0)){
                //Se o nó pai teve lançamentos, então alterar o código Agrupador o nó filho.
                df.getDfPai().setNumFilhos(df.getDfPai().getNumFilhos() +1);
                df.setCodigoAgrupador(codigoPai + "." + decimalFormat.format(df.getDfPai().getNumFilhos()));
                
            }
            if ((listaLancamentos != null) && (listaLancamentos.size() > 0)){
                for (LancamentoDF lancamento: listaLancamentos){
                   dfNew = new DemonstrativoFinanceiro();
                   df.setNumFilhos(df.getNumFilhos() +1);
                   dfNew.setCodigoAgrupador(df.getCodigoAgrupador() + "." + decimalFormat.format(df.getNumFilhos()));
                   dfNew.setNomeAgrupador(lancamento.getDescricaoLancamento());
                   dfNew.setTipoES(df.getTipoES());
                   dfNew.setTotalTodosMeses(lancamento.getValorLancamento());
                   dfNew.setTipoconta(lancamento.getTipoConta());
                   dfNew.setConta(lancamento.getConta());
                   //criarTotalizadorMes(dfNew);
                   indice ++;
                   listaDemonstrativo.add(indice, dfNew);
                }
            }
            indice ++;
        }
    }
	

	public void criarTotalizadorMes(DemonstrativoFinanceiro df){
        // Criar totalizador de 1 mês
        MesProcessar mesProcessar = new MesProcessar();
        Calendar data = Calendario.getInstance();
        mesProcessar.setMesAno(data.get(Calendar.MONTH) + "/" + data.get(Calendar.YEAR));
        TotalizadorMesDF totMes = new TotalizadorMesDF();
        totMes.setMesProcessar(mesProcessar);
        df.getListaTotalizadorMeses().add(totMes);
    }


	public void criarNivelNaoInformado(List<DemonstrativoFinanceiro> listaDF){
        /* Criar um totalizador para os valores que não foram definidos um plano de conta/Centro de Custo.
         * Obs.: Este totalizador deverá ser o último nível da Árvore.
         */
        String ultimoNivel;
        int nivelNaoDefinido;
        NumberFormat nf = new DecimalFormat("000");
        String codigoNode;
        // Entende-se que a lista já está ordenada no formato de árvore.
        DemonstrativoFinanceiro dfUltimoNivel = listaDF.get(listaDF.size()-1);
        ultimoNivel = dfUltimoNivel.getCodigoAgrupador().substring(0, 3);
        nivelNaoDefinido = (Integer.parseInt(ultimoNivel) + 1);
        DemonstrativoFinanceiro df = new  DemonstrativoFinanceiro();
        codigoNode = nf.format(nivelNaoDefinido);
        df.setCodigoAgrupador(codigoNode);
        //this.setRateioNaoDefinido(codigoNode);
        df.setNomeAgrupador("Não Informado Plano de Conta");
        criarTotalizadorMes(df);
        listaDF.add(df);
    }
	
    public String getRateioNaoDefinido() {
        return rateioNaoDefinido;
    }

    public void setRateioNaoDefinido(String rateioNaoDefinido) {
        this.rateioNaoDefinido = rateioNaoDefinido;
    }

    public boolean isGerarRelatorioUsandoThread() {
        return gerarRelatorioUsandoThread;
    }

    public void setGerarRelatorioUsandoThread(boolean gerarRelatorioUsandoThread) {
        this.gerarRelatorioUsandoThread = gerarRelatorioUsandoThread;
    }
    

    public static List<MesProcessar> montarListaMeses (Calendar dataInicial, Calendar dataFinal) throws Exception{
      /* Data    : 15/07/2011
       * Author  : Ulisses
       * Objetivo: Montar uma lista de meses que serão processados a partir de um período.
       *
       */
      List<MesProcessar> listaMeses = new ArrayList<MesProcessar>();
      Uteis.retirarHoraDaData(dataInicial);
      Uteis.retirarHoraDaData(dataFinal);
      if (dataInicial.after(dataFinal)){
          throw new Exception("Não foi possível montar lista de Meses para processar. A data inicial deve ser menor que a data final.");
      }

      int anoIni = dataInicial.get(Calendar.YEAR);
      int anoFim = dataFinal.get(Calendar.YEAR);
      int mesIni = dataInicial.get(Calendar.MONTH);
      int mesFim = dataFinal.get(Calendar.MONTH);

      int diaIni;
      int diaFim;
      int anoGerar;
      int nrMesIni = 0;
      int nrMesFim = 0;
      String mesAno;
      NumberFormat nf = new DecimalFormat("00");
      for (int iAno=anoIni; iAno<=anoFim; iAno++){
          anoGerar = iAno;
          int iMes;
          if (anoIni != anoFim){
              if (anoIni == iAno) {
                nrMesIni =mesIni;
                nrMesFim = 11;
              }
              else if (anoFim == iAno) {
                nrMesIni = 0;
                nrMesFim = dataFinal.get(Calendar.MONTH);
              }else{
                nrMesIni = 0;
                nrMesFim = 11;
              }
          }else{
              nrMesIni= dataInicial.get(Calendar.MONTH);
              nrMesFim = dataFinal.get(Calendar.MONTH);
          }

          for (iMes=nrMesIni; iMes<=nrMesFim; iMes++){
             mesAno = nf.format(iMes + 1) + "/" + iAno;
             if ((anoIni == iAno) &&
                (mesIni == iMes)){
                 // Pegar dia inicial e final do 1º Mês do Período
                 diaIni = dataInicial.get(Calendar.DAY_OF_MONTH);
                 if (mesIni == mesFim && anoIni == anoFim)
                   diaFim = dataFinal.get(Calendar.DAY_OF_MONTH);
                 else
                   diaFim = dataInicial.getActualMaximum(Calendar.DAY_OF_MONTH);

             }else if ((anoFim == iAno) && (mesFim == iMes)) {
                 // Pegar dia inicial e final do último Mês do Período
                 diaIni = 1;
                 diaFim = dataFinal.get(Calendar.DAY_OF_MONTH);
             }else{
                 // Pegar dia inicial e final dos Meses do Período
                 Calendar dataPeriodo  = Calendario.getInstance() ;
                 dataPeriodo.set(Calendar.YEAR, iAno);
                 dataPeriodo.set(Calendar.MONTH, iMes);
                 dataPeriodo.set(Calendar.DAY_OF_MONTH, 1);

                 diaIni = dataPeriodo.get(Calendar.DAY_OF_MONTH);
                 diaFim = dataPeriodo.getActualMaximum(Calendar.DAY_OF_MONTH);
             }
             MesProcessar mesProcessar = new  MesProcessar();
             mesProcessar.setMesAno(mesAno);

             mesProcessar.getDataIni().set(Calendar.DAY_OF_MONTH, diaIni);
             mesProcessar.getDataIni().set(Calendar.MONTH, iMes);
             mesProcessar.getDataIni().set(Calendar.YEAR, iAno);

             mesProcessar.getDataFim().set(Calendar.DAY_OF_MONTH, diaFim);
             mesProcessar.getDataFim().set(Calendar.MONTH, iMes);
             mesProcessar.getDataFim().set(Calendar.YEAR, iAno);
             mesProcessar.setNomeMes(Uteis.getMesNomeReferencia(mesProcessar.getDataIni().getTime()) +"/" + iAno );

            listaMeses.add(mesProcessar);
          }

      }
      return listaMeses;
    }
    public void executarThreads(TipoRelatorioDF tipoRelatorio, List<MesProcessar> listaMesProcessar, boolean apresentarDevolucoesRel){
        // iniciar as threads
        for (ThreadDemonstrativoFinanceiro thread: listaThreads ){
            thread.setApresentarDevolucoesRel(apresentarDevolucoesRel);
            if (this.isGerarRelatorioUsandoThread())
              thread.start();
            else
              thread.executarProcessoSemThread(listaMesProcessar);
        }
    }

    public static void criarTotalizadoresMeses(DemonstrativoFinanceiro df, List<MesProcessar> listaMesesProcessar){
        for(MesProcessar mesProcessar: listaMesesProcessar){
           TotalizadorMesDF totMes = new TotalizadorMesDF();
           totMes.setMesProcessar(mesProcessar);
           df.getListaTotalizadorMeses().add(totMes);
        }

    }

    public void criarThreadsDemonstrativoFinanceiro(Connection con,
                                                List<DemonstrativoFinanceiro> listaDemonstrativo,
                                                TipoRelatorioDF tipoRelatorio,
                                                TipoVisualizacaoRelatorioDF tipoVisualizacao,
                                                List<MesProcessar> listaMesesProcessar,
                                                int empresa,
                                                List<Integer> listaFiltroCentroCusto,
                                                TipoFonteDadosDF tipoFonteDados,
                                                boolean dre,
                                                Map<String, String> nc,
                                                boolean usarCE,
                                                boolean agruparValorProdutoMMasModalidades,
                                                String contas) throws Exception {
        criarThreadsDemonstrativoFinanceiro(con,
                listaDemonstrativo,
                tipoRelatorio,
                tipoVisualizacao,
                listaMesesProcessar,
                empresa,
                listaFiltroCentroCusto,
                tipoFonteDados,
                dre,
                nc,
                usarCE,
                agruparValorProdutoMMasModalidades,
                contas,
                false, false, false);
    }

    public void criarThreadsDemonstrativoFinanceiro(Connection con,
                                                    List<DemonstrativoFinanceiro> listaDemonstrativo,
                                                    TipoRelatorioDF tipoRelatorio,
                                                    TipoVisualizacaoRelatorioDF tipoVisualizacao,
                                                    List<MesProcessar> listaMesesProcessar,
                                                    int empresa,
                                                    List<Integer> listaFiltroCentroCusto,
                                                    TipoFonteDadosDF tipoFonteDados,
                                                    boolean dre,
                                                    Map<String, String> nc,
                                                    boolean usarCE,
                                                    boolean agruparValorProdutoMMasModalidades,
                                                    String contas,
                                                    boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao, boolean fluxoCaixa,
                                                    boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) throws Exception {

		if (this.gerarRelatorioUsandoThread) {
			for (MesProcessar mesProcessar : listaMesesProcessar) {
				listaThreads.add(new ThreadDemonstrativoFinanceiro(con,
                        listaDemonstrativo,
                        tipoRelatorio,
                        tipoVisualizacao,
                        mesProcessar,
						this.getRateioNaoDefinido(),
                        empresa,
                        listaFiltroCentroCusto,
                        tipoFonteDados,
                        dre,
                        nc,
                        usarCE,
                        agruparValorProdutoMMasModalidades,
                        contas,
                        incluirParcelasRecorrenciaEmRelatorioReceitaProvisao, fluxoCaixa,
                        incluirParcelasEmAbertoEmRelatorioReceitaProvisao));
			}
		} else {
			listaThreads.add(new ThreadDemonstrativoFinanceiro(con,
                    listaDemonstrativo,
                    tipoRelatorio,
                    tipoVisualizacao,
                    null,
                    this.getRateioNaoDefinido(),
                    empresa,
                    listaFiltroCentroCusto,
                    tipoFonteDados,
                    dre,
                    nc,
                    usarCE,
                    agruparValorProdutoMMasModalidades,
                    contas,
                    incluirParcelasRecorrenciaEmRelatorioReceitaProvisao,fluxoCaixa, incluirParcelasEmAbertoEmRelatorioReceitaProvisao));

		}

	}

    public List<DemonstrativoFinanceiro> montarListaDemonstrativoFinaneiro(TipoVisualizacaoRelatorioDF tipoVisualizacao,
                                                                            List<MesProcessar> listaMesesProcessar, 
                                                                            Map<String, String> novosCodigos,
                                                                            boolean dre, Connection con) throws Exception{
        CentroCusto cc = null;
        if (con == null){
            cc = new CentroCusto();
        } else {
            cc = new CentroCusto(con);
        }

    	List<CentroCustosDRE> centroCustos = cc.obterCentroCustos();
    	
    	centroCustos.add(new CentroCustosDRE(0, "999", "Não Atribuído"));
    	
    	List<DemonstrativoFinanceiro> listaDF = new ArrayList<DemonstrativoFinanceiro>();
    	Map<String, String> nodes = new HashMap<String, String>();
    	List<String> novosNos = new ArrayList<String>();
    	int codigoParaDRE = 1;
    	if(dre){
    		for (TipoEquivalenciaDRE equivalencia: TipoEquivalenciaDRE.values()){
           	 	DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
           	 	df.setDre(true);
                df.setCodigoAgrupador(equivalencia.getCodigoNode());
                df.setNomeAgrupador(equivalencia.getDescricao());
                df.setLink(equivalencia.getTipo());
                df.setListaCentros(CentroCustosDRE.getListaClone(centroCustos));
                criarTotalizadoresMeses(df,listaMesesProcessar);
                listaDF.add(df);
            }	
    	}
         if ((tipoVisualizacao ==  TipoVisualizacaoRelatorioDF.PLANOCONTA) || dre){
             // Consultar todos os Plano de Contas cadastrados.
             List<PlanoContaTO> listaPlanoConta;
             if (con == null){
                 listaPlanoConta = FacadeManager.getFacade().getFinanceiro().getPlanoConta().consultarTodos();
             } else {
                 PlanoConta planoConta = new PlanoConta(con);
                 listaPlanoConta = planoConta.consultarTodos();
             }
             for (PlanoContaTO obj: listaPlanoConta){
                DemonstrativoFinanceiro df = new  DemonstrativoFinanceiro();
                df.setMetaPerc(obj.getMeta());
                if(dre){
                	
                	if(obj.getEquivalenciaDRE() == null){
                		continue;
                	}
                	//se o codigo não tem pai, posso inserir direto o codigo agrupador
                	if(obj.getCodigoPlano().length() < 4){
                		String no = obj.getEquivalenciaDRE().getCodigoNode()+"."+obj.getCodigoPlano();
                		df.setCodigoAgrupador(no);
                		//informo para as proximas iterações que este nó do plano de contas representa ele mesmo no dre
                		nodes.put(no, no);
                		novosCodigos.put(obj.getCodigoPlano(), no);
                	}else{
                		//tenho que verificar se o pai desse plano de contas possui a mesma equivalencia de dre
                                String nodePai = obj.getCodigoPlano().substring(0, obj.getCodigoPlano().length() - 4);
                		//verifico se existe no mapa este nó pai
                		String noPai = novosCodigos.get(nodePai);
                		
                		//VERIFICAR SE O PAI TEM A MESA EQUIVALENCIA NO DRE
                		String eqPai = UteisValidacao.emptyString(noPai) ? "" : noPai.substring(0, 3);
                		
                		if(UteisValidacao.emptyString(noPai) || !eqPai.equals(obj.getEquivalenciaDRE().getCodigoNode())){
                                    String[] codigos = obj.getCodigoPlano().split("\\.");
                                    String novoNo = "";
                                    for(int i = 1; i < codigos.length; i++){
                                        nodePai = obj.getCodigoPlano().substring(0, (i * 4) -1 );
                                        noPai = novosCodigos.get(nodePai);
                                        eqPai = UteisValidacao.emptyString(noPai) ? "" : noPai.substring(0, 3);
                                        if(UteisValidacao.emptyString(noPai) || !eqPai.equals(obj.getEquivalenciaDRE().getCodigoNode())){
                                            novoNo = obj.getEquivalenciaDRE().getCodigoNode()
                                            +"."+nodePai;
                                            if(nodes.containsKey(novoNo)){
                                                noPai = novoNo;
                                                continue;
                                            }
                                            PlanoContaTO planoPai = null;
                                            for(PlanoContaTO planoConta: listaPlanoConta){
                                                if(planoConta.getCodigoPlano().equals(nodePai)){
                                                    planoPai = planoConta;
                                                    break;
                                                }
                                            }
                                            if (planoPai != null) {
                                                DemonstrativoFinanceiro dfPai = new  DemonstrativoFinanceiro();
                                                dfPai.setCodigoAgrupador(novoNo);

                                                dfPai.setNomeAgrupador(planoPai.getDescricao());
                                                dfPai.setTipoES(planoPai.getTipoPadrao());
                                                dfPai.setTipoEquivalencia(obj.getEquivalenciaDRE());
                                                dfPai.setListaCentros(CentroCustosDRE.getListaClone(centroCustos));
                                                criarTotalizadoresMeses(dfPai,listaMesesProcessar);
                                                listaDF.add(dfPai);
                                            }
                                            noPai = novoNo;
                                            nodes.put(novoNo, novoNo);
                                        }
                                    }
                		}
                                //se existe no mapa, apenas substituir o nó pai do plano de contas pelo do dre
                                String noFilho = noPai+"."+obj.getCodigoPlano().substring(obj.getCodigoPlano().length() - 3, obj.getCodigoPlano().length());
                                df.setCodigoAgrupador(noFilho);
                                novosNos.add(noFilho);
                                nodes.put(noFilho, noFilho);
                                novosCodigos.put(obj.getCodigoPlano(), noFilho);
                	}

                	
                }else{
                	df.setCodigoAgrupador(obj.getCodigoPlano());	
                }
                df.setPercPretendido(obj.getPercGastoPretendido());
                df.setNomeAgrupador(obj.getDescricao());
                df.setTipoES(obj.getTipoPadrao());
                df.setInvestimento(obj.getInvestimento());
                df.setTipoEquivalencia(obj.getEquivalenciaDRE());
                df.setListaCentros(CentroCustosDRE.getListaClone(centroCustos));
                criarTotalizadoresMeses(df,listaMesesProcessar);
                listaDF.add(df);
             }
             if (listaDF.size() == 0){
                 throw new Exception("Não é possível processar o relatório. Não há nenhum plano de contas cadastrado.");
             }
         }
         else if(tipoVisualizacao == TipoVisualizacaoRelatorioDF.CENTROCUSTO){
             // Consultar todos os Centro de Custos cadastrados.
             List<CentroCustoTO> listaCentroCusto = FacadeManager.getFacade().getFinanceiro().getCentroCusto().consultarTodos();
             for (CentroCustoTO obj: listaCentroCusto){
                DemonstrativoFinanceiro df = new  DemonstrativoFinanceiro();
                df.setCodigoAgrupador(obj.getCodigoCentro());
                df.setNomeAgrupador(obj.getDescricao());
                criarTotalizadoresMeses(df,listaMesesProcessar);
                listaDF.add(df);
             }
             if (listaDF.size() == 0){
                 throw new Exception("Não é possível processar o relatório. Não há nenhum centro de custos cadastrado.");
             }

         }


        /* Criar um totalizador para os valores que não foram definidos um plano de conta/Centro de Custo.
         * Obs.: Este totalizador deverá ser o último nível da Árvore.
         */
        String ultimoNivel;
        int nivelNaoDefinido;
        NumberFormat nf = new DecimalFormat("000");
        String codigoNode;
        // Entende-se que a lista já está ordenada no formato de árvore.
        DemonstrativoFinanceiro dfUltimoNivel = listaDF.get(listaDF.size()-1);
        ultimoNivel = dfUltimoNivel.getCodigoAgrupador().substring(0, 3);
        nivelNaoDefinido = (Integer.parseInt(ultimoNivel) + 1);
        DemonstrativoFinanceiro df = new  DemonstrativoFinanceiro();
        codigoNode = nf.format(nivelNaoDefinido);
        df.setCodigoAgrupador(codigoNode);
        this.setRateioNaoDefinido(codigoNode);
        if (tipoVisualizacao ==  TipoVisualizacaoRelatorioDF.PLANOCONTA)
          df.setNomeAgrupador("Não Informado Plano de Conta");
        else
          df.setNomeAgrupador("Não Informado Centro de Custos");
         // criar totalizadores para cada item do plano de contas
        criarTotalizadoresMeses(df, listaMesesProcessar);
        
        
        if(dre){
        	novosCodigos.put(df.getCodigoAgrupador(), TipoEquivalenciaDRE.RECEITA_BRUTA.getCodigoNode()+"."+df.getCodigoAgrupador());
        	df.setNomeAgrupador("Não Informado Plano de Conta");
        	df.setCodigoAgrupador(TipoEquivalenciaDRE.RECEITA_BRUTA.getCodigoNode()+"."+df.getCodigoAgrupador());
        	df.setListaCentros(CentroCustosDRE.getListaClone(centroCustos));
        	listaDF.add(df);
        	Ordenacao.ordenarLista(listaDF, "codigoAgrupador");
        }else{
        	listaDF.add(df);
        }
        return listaDF;
    }
}

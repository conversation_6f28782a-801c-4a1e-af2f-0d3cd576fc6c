package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created by <PERSON> on 10/12/2015.
 */
public class ParcelaEmAbertoRelTO {
    private String matricula;
    private String nome;
    private int parcela;
    private String descricaoParcela;
    private Date dataFatura;
    private Date dateVencimento;
    private Date dataPagamento;
    private int contrato;
    private String situacao;
    private String recorrencia;
    private Double valor = 0.0;
    private String email;
    private String telefone;
    private String nomeempresa;
    private Double multas = 0.0;
    private Double juros = 0.0;
    private Integer cliente;
    private Date dataCancelamento;
    private Integer nrTentativas;
    private String motivoRetorno;
    private String nome_plano;
    private String convenioCobranca;
    private String formasPagamento;
    private String modalidades;
    private String cpf;
    private String endereco;
    private Integer codigoPessoa;
    private String nomeRespPgto;
    private String turma;

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome.toUpperCase();
    }

    public String getNomeAbreviado() {
        if (nome != null && nome.length() > 30) {
            return nome.substring(0, 30).concat("...").toUpperCase();
        }
        return nome.toUpperCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public int getParcela() {
        return parcela;
    }

    public void setParcela(int parcela) {
        this.parcela = parcela;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public String getDescricaoParcelaAbreviado() {
        if (descricaoParcela != null && descricaoParcela.length() > 15) {
            return descricaoParcela.substring(0, 15).concat("...");
        }
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public Date getDataFatura() {
        return dataFatura;
    }

    public void setDataFatura(Date dataFatura) {
        this.dataFatura = dataFatura;
    }

    public Date getDateVencimento() {
        return dateVencimento;
    }
    public String getDateVencimento_Apresentar() {
        return Uteis.getData(dateVencimento, "br");
    }

    public String getDataFaturamento_Apresentar() {
        return Uteis.getData(dataFatura, "br");
    }

    public void setDateVencimento(Date dateVencimento) {
        this.dateVencimento = dateVencimento;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }
    public String getDataPagamento_Apresentar() {
        if (dataPagamento == null) {
            return "";
        }
        return Uteis.getData(dataPagamento, "br");
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("EA")) {
            return "Em Aberto";
        }
        if (situacao.equals("PG")) {
            return "Pago";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        if (situacao.equals("RG")) {
            return "Renegociado";
        }
        return situacao;
    }

    public String getRecorrencia_Apresentar() {
        if (getRecorrencia().equals("t")) {
            return "Sim";
        } else if (getRecorrencia().equals("f")) {
            return "Não";
        }
        return "";
    }

    public String getRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(String recorrencia) {
        this.recorrencia = recorrencia;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setEmailValidado(String email) {
        email = Uteis.retirarAcentuacaoRegex(email);
        if (UteisEmail.getValidEmail(email)) {
            this.email = email;
        } else {
            this.email = "";
        }
    }

    public String getTelefone() {
        return telefone;
    }

    public String getPrimeiroTelefone() {
        if (!UteisValidacao.emptyString(telefone)) {
            try {
                return telefone.split(",")[0];
            } catch (Exception e) {
                e.printStackTrace();
                return "";
            }
        }
        return "";
    }

    public String getTelefones() {
        String telefones = "";
        boolean outros = false;
        for (String telefone : telefone.split(",")) {
            if (telefones == "") {
                telefones = "<b>Outros</b><br>";
                continue;
            }
            outros = true;
            telefones += telefone + "</br>";
        }
        return outros ? telefones : "Nenhum outro telefone.";
    }


    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getNomeempresa() {
        return nomeempresa;
    }

    public void setNomeempresa(String nomeempresa) {
        this.nomeempresa = nomeempresa;
    }

    public Double getMultas() {
        return multas;
    }

    public void setMultas(Double multasJuros) {
        this.multas = multasJuros;
    }

    public Double getJuros() {
        return juros;
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getValor_Apresentar() {
        if (UteisValidacao.emptyNumber(getValor())) {
            return "";
        } else {
            return Formatador.formatarValorMonetarioSemMoeda(getValor());
        }
    }

    public double getValorMultasJuros() {
        return getValor() + getMultas() + getJuros();
    }

    public String getValorMultasJuros_Apresentar() {
        if (UteisValidacao.emptyNumber(getValorMultasJuros())) {
            return "";
        } else {
            return Formatador.formatarValorMonetarioSemMoeda(getValorMultasJuros());
        }
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getDataCancelamento_Hint() {
        if (dataCancelamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela cancelada na data " + Calendario.getData(dataCancelamento, "dd/MM/yyyy");
    }

    public String getDataPagamento_Hint() {
        if (dataPagamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela paga na data " + Calendario.getData(dataPagamento, "dd/MM/yyyy");
    }

    public Integer getNrTentativas() {
        return nrTentativas;
    }

    public void setNrTentativas(Integer nrTentativas) {
        this.nrTentativas = nrTentativas;
    }

    public String getMotivoRetorno() {
        return motivoRetorno;
    }

    public String getMotivoRetornoAbreviado() {
        if (motivoRetorno != null && motivoRetorno.length() > 15) {
            return motivoRetorno.substring(0, 15).concat("...");
        }
        return motivoRetorno;
    }

    public void setMotivoRetorno(String motivoRetorno) {
        this.motivoRetorno = motivoRetorno;
    }

    public String getNome_plano() {
        return nome_plano;
    }

    public void setNome_plano(String nome_plano) {
        this.nome_plano = nome_plano;
    }

    public String getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(String convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public String getFormasPagamento() {
        if (formasPagamento == null) {
            return "";
        }
        return formasPagamento;
    }

    public void setFormasPagamento(String formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public String getModalidades() {
        if (modalidades == null) {
            return "";
        }
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getCpf() {
        if (cpf == null) {
            return "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getEndereco() {
        if (endereco == null) {
            return "";
        }
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public boolean isApresentarBotaoCliente(){
        if(UteisValidacao.emptyString(matricula) || matricula.startsWith("cl")){
            return false;
        }
        return true;
    }

    public String getNomeRespPgto() {
        return nomeRespPgto;
    }

    public void setNomeRespPgto(String nomeRespPgto) {
        this.nomeRespPgto = nomeRespPgto;
    }

    public String getTurma() {
        return turma;
    }

    public void setTurma(String turma) {
        this.turma = turma;
    }
}

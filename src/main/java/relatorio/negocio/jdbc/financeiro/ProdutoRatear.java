/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import negocio.comuns.arquitetura.SuperTO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProdutoRatear extends SuperTO {

    private int codigoProduto;
    private String tipoProduto;
    private int codigoCategoriaProduto;
    private Double valorRatear;
    private Double valorMovProdutoParcela;
    private Double valorTotalContrato;
    private int contrato;
    private String descricaoParcela = "";
    private String descricaoProduto = "";
    private int codigoPessoa = 0;
    private int codigoMovProdutoAulaAvulsaEDiaria = 0;
    private int codigoMovParcela = 0;
    private String nomePessoa = "";
    private boolean escolhido = false;
    private int recibo = 0;
    private List<ContratoModalidadePercentual> listaContratoModalidadePercentual;
    private int codigoMovProduto = 0;
    private int codigoConsultor = 0;
    private int empresa = 0;
    private String nomeEmpresa = "";
    private String planoDescricao = "";
    private int vendaAvulsa;
    private int aulaavulsadiaria;
    private int personal;
    private String modalidadeDiaria="";
    private Double multaJuros = 0.0;
    private Integer quantidade = 0;
    private String responsavelLancamento = "";
    private boolean devolucaoCheque = false;

    @Override
    public ProdutoRatear clone() {
        ProdutoRatear clone = new ProdutoRatear();
        clone.setCodigoProduto(this.getCodigoProduto());
        clone.setTipoProduto(this.tipoProduto);
        clone.setCodigoCategoriaProduto(this.codigoCategoriaProduto);
        clone.setValorRatear(this.valorRatear);
        clone.setValorMovProdutoParcela(this.valorMovProdutoParcela);
        clone.setTipoProduto(this.tipoProduto);
        clone.setValorTotalContrato(this.valorTotalContrato);
        clone.setContrato(this.contrato);
        clone.setDescricaoParcela(this.descricaoParcela);
        clone.setDescricaoProduto(this.descricaoProduto);
        clone.setCodigoPessoa(this.codigoPessoa);
        clone.setCodigoMovProdutoAulaAvulsaEDiaria(this.codigoMovProdutoAulaAvulsaEDiaria);
        clone.setCodigoMovParcela(this.codigoMovParcela);
        clone.setCodigoMovProduto(this.codigoMovProduto);
        clone.setNomePessoa(this.nomePessoa);
        clone.setListaContratoModalidadePercentual(new ArrayList(this.getListaContratoModalidadePercentual()));
        clone.setCodigoConsultor(this.codigoConsultor);
        clone.setVendaAvulsa(this.vendaAvulsa);
        clone.setAulaavulsadiaria(this.aulaavulsadiaria);
        clone.setPersonal(this.personal);
        clone.setModalidadeDiaria(this.modalidadeDiaria);
        clone.setMultaJuros(this.multaJuros);
        return clone;
    }

    public int getCodigoCategoriaProduto() {
        return codigoCategoriaProduto;
    }

    public void setCodigoCategoriaProduto(int codigoCategoriaProduto) {
        this.codigoCategoriaProduto = codigoCategoriaProduto;
    }

    public int getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(int codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public Double getValorRatear() {
        return valorRatear;
    }

    public void setValorRatear(Double valorRatear) {
        this.valorRatear = valorRatear;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Double getValorMovProdutoParcela() {
        return valorMovProdutoParcela;
    }

    public void setValorMovProdutoParcela(Double valorMovProdutoParcela) {
        this.valorMovProdutoParcela = valorMovProdutoParcela;
    }

    public Double getValorTotalContrato() {
        return valorTotalContrato;
    }

    public void setValorTotalContrato(Double valorTotalContrato) {
        this.valorTotalContrato = valorTotalContrato;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public int getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(int codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public int getCodigoMovProdutoAulaAvulsaEDiaria() {
        return codigoMovProdutoAulaAvulsaEDiaria;
    }

    public void setCodigoMovProdutoAulaAvulsaEDiaria(int codigoMovProdutoAulaAvulsaEDiaria) {
        this.codigoMovProdutoAulaAvulsaEDiaria = codigoMovProdutoAulaAvulsaEDiaria;
    }

    public int getCodigoMovParcela() {
        return codigoMovParcela;
    }

    public void setCodigoMovParcela(int codigoMovParcela) {
        this.codigoMovParcela = codigoMovParcela;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public boolean getEscolhido() {
        return escolhido;
    }

    public void setEscolhido(boolean escolhido) {
        this.escolhido = escolhido;
    }

    public void setRecibo(int recibo) {
        this.recibo = recibo;
    }

    public int getRecibo() {
        return recibo;
    }

    public List<ContratoModalidadePercentual> getListaContratoModalidadePercentual() {
        if(listaContratoModalidadePercentual == null){
            listaContratoModalidadePercentual = new ArrayList<ContratoModalidadePercentual>();
        }
        return listaContratoModalidadePercentual;
    }

    public void setListaContratoModalidadePercentual(List<ContratoModalidadePercentual> listaContratoModalidadePercentual) {
        this.listaContratoModalidadePercentual = listaContratoModalidadePercentual;
    }

    public void setCodigoMovProduto(int codigoMovProduto) {
        this.codigoMovProduto = codigoMovProduto;
    }

    public int getCodigoMovProduto() {
        return codigoMovProduto;
    }

    public int getCodigoConsultor() {
        return codigoConsultor;
    }

    public void setCodigoConsultor(int codigoConsultor) {
        this.codigoConsultor = codigoConsultor;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getPlanoDescricao() {
        return planoDescricao;
    }

    public void setPlanoDescricao(String planoDescricao) {
        this.planoDescricao = planoDescricao;
    }

    public int getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(int vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public int getAulaavulsadiaria() {
        return aulaavulsadiaria;
    }

    public void setAulaavulsadiaria(int aulaavulsadiaria) {
        this.aulaavulsadiaria = aulaavulsadiaria;
    }

    public int getPersonal() {
        return personal;
    }

    public void setPersonal(int personal) {
        this.personal = personal;
    }

    public String getModalidadeDiaria() {
        return modalidadeDiaria;
    }

    public void setModalidadeDiaria(String modalidadeDiaria) {
        this.modalidadeDiaria = modalidadeDiaria;
    }

    public Double getMultaJuros() {
        return multaJuros;
    }

    public void setMultaJuros(Double multaJuros) {
        this.multaJuros = multaJuros;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public boolean isDevolucaoCheque() {
        return devolucaoCheque;
    }

    public void setDevolucaoCheque(boolean devolucaoCheque) {
        this.devolucaoCheque = devolucaoCheque;
    }
}

package relatorio.negocio.jdbc.financeiro;

import java.util.Date;

public class ParcelaConsolidadaTO {

    private String nome_empresa;
    private Integer movparcela_codigo;
    private String cliente_matricula;
    private String pessoa_nome;
    private String email;
    private String parcela_descricao;
    private Date parcela_dataregistro;
    private Date parcela_datavencimento;
    private String parcela_situacao;
    private Boolean regime_recorrencia;
    private Integer parcela_contrato;
    private Double parcela_valorparcela;
    private Integer total;
    private Date datapagamento;
    private String tipo;
    private Long id_externo_parcela;
    private Long id_externo_contrato;

    public String getNome_empresa() {
        return nome_empresa;
    }

    public void setNome_empresa(String nome_empresa) {
        this.nome_empresa = nome_empresa;
    }

    public Integer getMovparcela_codigo() {
        return movparcela_codigo;
    }

    public void setMovparcela_codigo(Integer movparcela_codigo) {
        this.movparcela_codigo = movparcela_codigo;
    }

    public String getCliente_matricula() {
        return cliente_matricula;
    }

    public void setCliente_matricula(String cliente_matricula) {
        this.cliente_matricula = cliente_matricula;
    }

    public String getPessoa_nome() {
        return pessoa_nome;
    }

    public void setPessoa_nome(String pessoa_nome) {
        this.pessoa_nome = pessoa_nome;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getParcela_descricao() {
        return parcela_descricao;
    }

    public void setParcela_descricao(String parcela_descricao) {
        this.parcela_descricao = parcela_descricao;
    }

    public Date getParcela_dataregistro() {
        return parcela_dataregistro;
    }

    public void setParcela_dataregistro(Date parcela_dataregistro) {
        this.parcela_dataregistro = parcela_dataregistro;
    }

    public Date getParcela_datavencimento() {
        return parcela_datavencimento;
    }

    public void setParcela_datavencimento(Date parcela_datavencimento) {
        this.parcela_datavencimento = parcela_datavencimento;
    }

    public String getParcela_situacao() {
        return parcela_situacao;
    }

    public void setParcela_situacao(String parcela_situacao) {
        this.parcela_situacao = parcela_situacao;
    }

    public Boolean getRegime_recorrencia() {
        return regime_recorrencia;
    }

    public void setRegime_recorrencia(Boolean regime_recorrencia) {
        this.regime_recorrencia = regime_recorrencia;
    }

    public Integer getParcela_contrato() {
        return parcela_contrato;
    }

    public void setParcela_contrato(Integer parcela_contrato) {
        this.parcela_contrato = parcela_contrato;
    }

    public Double getParcela_valorparcela() {
        return parcela_valorparcela;
    }

    public void setParcela_valorparcela(Double parcela_valorparcela) {
        this.parcela_valorparcela = parcela_valorparcela;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Date getDatapagamento() {
        return datapagamento;
    }

    public void setDatapagamento(Date datapagamento) {
        this.datapagamento = datapagamento;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public Long getId_externo_parcela() {
        return id_externo_parcela;
    }

    public void setId_externo_parcela(Long id_externo_parcela) {
        this.id_externo_parcela = id_externo_parcela;
    }

    public Long getId_externo_contrato() {
        return id_externo_contrato;
    }

    public void setId_externo_contrato(Long id_externo_contrato) {
        this.id_externo_contrato = id_externo_contrato;
    }
}

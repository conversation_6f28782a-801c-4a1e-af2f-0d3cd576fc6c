/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import java.io.Serializable;
import java.util.Calendar;
import java.util.List;

import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class MesProcessar implements Serializable {
    private Calendar dataIni;
    private Calendar dataFim;
    private String mesAno;
    private String nomeMes;
    private double entrada = 0;
    private double saida = 0;
    private double total = 0; // Esta variável servirá para mostrar o total de cada mês
    private double totalNaoAtribuido = 0; // Esta variável servirá para mostrar o total do totalizador "não atribuido"
    private double valorPercGasto = 0;
    private double valorPercGastoNA = 0;
    private double metaPretendido = 0;

    public String getCorPretendido() {
         return UteisValidacao.emptyNumber(metaPretendido) ? "" :
                (valorPercGasto*100) > metaPretendido ? "red" : "green";
    }
        public String getCorPretendidoNA() {
         return UteisValidacao.emptyNumber(metaPretendido) ? "" :
                (valorPercGastoNA*100) > metaPretendido ? "red" : "green";
    }

    public double getMetaPretendido() {
        return metaPretendido;
    }

    public void setMetaPretendido(double metaPretendido) {
        this.metaPretendido = metaPretendido;
    }

    public double getValorPercGasto() {
        return valorPercGasto;
    }

    public void setValorPercGasto(double valorPercGasto) {
        this.valorPercGasto = valorPercGasto;
    }

    public MesProcessar(){
      dataIni = Calendario.getInstance();
      dataFim = Calendario.getInstance();
    }

    public String getMesAno() {
        return mesAno;
    }

    public void setMesAno(String mesAno) {
        this.mesAno = mesAno;
    }

    public Calendar getDataFim() {
        return dataFim;
    }

    public void setDataFim(Calendar dataFim) {
        this.dataFim = dataFim;
    }

    public Calendar getDataIni() {
        return dataIni;
    }

    public void setDataIni(Calendar dataIni) {
        this.dataIni = dataIni;
    }

    public String getNomeMes() {
        return nomeMes;
    }

    public void setNomeMes(String nomeMes) {
        this.nomeMes = nomeMes;
    }

    public double getTotal() {
        return total;
    }
	public double getTotalPositivo() {
		return total < 0.0 ? total *-1 : total;
	}
    public void setTotal(double total) {
        this.total = total;
    }

    public String getValorApresentar() {
      String valor = Formatador.formatarValorMonetarioSemMoeda(this.total);
        return (total < 0.0 ? "(-)" : "")+valor;
    }

    public String getValorAcumuladoApresentar() {
        if(UteisValidacao.emptyNumber(valorPercGasto)){
            return "";
        }
        return Formatador.formatarValorPercentual(valorPercGasto, (valorPercGasto % 1 > 0 ? 2 : 0));

    }

    public String getValorAcumuladoNaoAtribuidoApresentar() {
        if(UteisValidacao.emptyNumber(valorPercGastoNA)){
            return "";
        }
        return Formatador.formatarValorPercentual(valorPercGastoNA, (valorPercGastoNA % 1 > 0 ? 2 : 0));
    }

    public double getValorPercGastoNA() {
        return valorPercGastoNA;
    }

    public void setValorPercGastoNA(double valorPercGastoNA) {
        this.valorPercGastoNA = valorPercGastoNA;
    }

    public String getValorNaoAtribuidoApresentar() {
      String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalNaoAtribuido);
      return valor;
    }

    public String getCorLinkTotal(){
        if (this.total >= 0 )
            return "black";
         else
            return "red";
    }

    public String getCorLinkTotalNaoAtribuido(){
        if (this.totalNaoAtribuido >= 0 )
            return "black";
         else
            return "red";
    }

    public double getTotalNaoAtribuido() {
        return totalNaoAtribuido;
    }

    public void setTotalNaoAtribuido(double totalNaoAtribuido) {
        this.totalNaoAtribuido = totalNaoAtribuido;
    }
	public static MesProcessar obter(MesProcessar mes, List<MesProcessar> meses){
		int indexOf = meses.indexOf(mes);
		if(indexOf >= 0){
			return meses.get(indexOf);
		}else{
			return new MesProcessar();			
		}
	}
	
    public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (!(obj instanceof MesProcessar))
            return false;
        return  (((MesProcessar)obj).getMesAno().equals(this.getMesAno()));
    }

    public String getCorLinkTotalTodosMesesDre() {
        if (this.getTotal() >= 0) {
            return "black";
        } else {
            return "red";
        }
    }

    public double getEntrada() {
        return entrada;
    }

    public void setEntrada(double entrada) {
        this.entrada = entrada;
    }

    public double getSaida() {
        return saida;
    }

    public void setSaida(double saida) {
        this.saida = saida;
    }

    public String getEntradaApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.entrada);
        return "+" + valor;
    }

    public String getSaidaApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.saida);
        return "-" + valor;
    }


}

package relatorio.negocio.jdbc.financeiro;

import negocio.comuns.arquitetura.SuperTO;

public class CaixaPorOperadorDetalheSaldoLivroRel extends SuperTO {

    private Integer codFormaPagamento;
    private String formaPagamento;
    private double valorTotal = 0.0;

    public Integer getCodFormaPagamento() {
        return codFormaPagamento;
    }

    public void setCodFormaPagamento(Integer codFormaPagamento) {
        this.codFormaPagamento = codFormaPagamento;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public double getValorTotal() {
        return valorTotal;
    }

    public void setValorTotal(double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public void adicionarValor(Double valorTotal) {
        this.valorTotal += valorTotal;
    }
}

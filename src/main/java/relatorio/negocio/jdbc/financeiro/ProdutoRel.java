package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.financeiro.ClienteProdutoTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProdutoRel extends SuperRelatorio {

    private ColaboradorVO consultorSelecionado = new ColaboradorVO();
    private UsuarioVO responsavelLancamento = new UsuarioVO();
    private String tipoProduto = "";
    private Integer codProduto = 0;
    private Date dataInicioLanc = null;
    private Date dataFinalLanc = null;
    private Date dataInicioVenc = null;
    private Date dataFinalVenc = null;
    private Date dataInicioCadastroCliente = null;
    private boolean semProdutos = false;
    private ArrayList<SituacaoClienteEnum> situacoes = new ArrayList<SituacaoClienteEnum>();

    public ProdutoRel() throws Exception {
    }

    public ProdutoRel(Connection connection) throws Exception {
        super(connection);
    }

    public List<ClienteProdutoTO> consultarProdutos(final String colaboradoresPendencia, Integer empresa) throws Exception {
        StringBuilder sql;
        if (!getSemProdutos()) {
            sql = getSqlProdutosComVigencia(empresa,false, colaboradoresPendencia);
        } else {
            sql = getSqlClientesSemProdutos(empresa,false, colaboradoresPendencia,null);
        }
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();

        List<ClienteProdutoTO> clienteProdutoTOs = new ArrayList<ClienteProdutoTO>();
        while (rs.next()) {
            ClienteProdutoTO clienteProdutoTO = new ClienteProdutoTO();
            clienteProdutoTO.setCodigoCliente(rs.getInt("codigocliente"));
            clienteProdutoTO.setMatriculaCliente(rs.getString("matricula"));
            clienteProdutoTO.setNomeCliente(rs.getString("nome"));
            clienteProdutoTO.setTelefonesCliente(rs.getString("telefones"));
            clienteProdutoTO.setNomeEmpresa(rs.getString("nomeEmpresa"));
            if (!getSemProdutos()) {
                clienteProdutoTO.setDescricaoProduto(rs.getString("descricao"));
                clienteProdutoTO.setValidadeProduto(rs.getDate("datafinalvigencia"));
                clienteProdutoTO.setValorProduto(rs.getDouble("totalfinal"));
                clienteProdutoTO.setResponsavellancamento(rs.getString("responsavellancamento"));
            }
            clienteProdutoTOs.add(clienteProdutoTO);
        }
        return clienteProdutoTOs;
    }

    private StringBuilder getSqlClientesSemProdutos(Integer empresa, boolean contar, final String colaboradores,ConfPaginacao paginacao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (!contar) {
            sql.append("  cli.codigo as codigoCliente,\n");
            sql.append("  cli.matricula,\n");
            sql.append("  pes.nome,\n");
            sql.append("  array(SELECT\n");
            sql.append("  t.numero\n");
            sql.append("        FROM telefone t\n");
            sql.append("        WHERE t.pessoa = pes.codigo) AS telefones,\n");
            sql.append("  emp.nome as nomeEmpresa \n");
        } else {
            sql.append("  count(distinct nome) as qtd\n");
        }
        sql.append("FROM cliente cli\n");
        filtrarSituacoes(sql);
        sql.append("  INNER JOIN pessoa pes ON cli.pessoa = pes.codigo\n");
        sql.append("  left JOIN vinculo vi ON cli.codigo = vi.cliente \n");
        sql.append("  INNER JOIN empresa emp ON emp.codigo = cli.empresa \n");
        sql.append("  WHERE 1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("  AND cli.empresa = ").append(empresa).append("\n");
        }
        if (getDataInicioCadastroCliente() != null) {
            sql.append(" AND pes.datacadastro >= '").append(Uteis.getDataHoraJDBC(getDataInicioCadastroCliente(), "00:00:00")).append("'\n");
        }
        sql.append("        AND NOT EXISTS (\n");
        sql.append("  SELECT\n");
        sql.append("    movproduto.pessoa\n");
        sql.append("  FROM movproduto\n");
        sql.append("    INNER JOIN produto ON movproduto.produto = produto.codigo\n");
        sql.append("            AND movproduto.pessoa IS NOT NULL\n");
        sql.append("            AND movproduto.pessoa = cli.pessoa\n");
        sql.append("            AND produto.tipoproduto IN ('").append(TipoProduto.SERVICO.getCodigo()).append("', '").append(TipoProduto.ATESTADO.getCodigo()).append("')\n");
        sql.append(" and produto.tipovigencia   IN ('ID', 'VV') ");
        if (getDataInicioLanc() != null) {
            sql.append("      AND datalancamento >= '").append(Uteis.getDataHoraJDBC(getDataInicioLanc(), "00:00:00")).append("'\n");
        }

        if (getDataFinalLanc() != null) {
            sql.append("      AND datalancamento <= '").append(Uteis.getDataHoraJDBC(getDataFinalLanc(), "23:59:59")).append("'\n");
        }

        sql.append(")\n");
        if (getConsultorSelecionado().getCodigo() != null && getConsultorSelecionado().getCodigo() > 0) {
           
            sql.append(" AND vi.colaborador = ").append(getConsultorSelecionado().getCodigo()).append(" AND vi.tipovinculo = 'CO' \n");
        }
        if(!UteisValidacao.emptyString(colaboradores)){
             sql.append(" AND (").append(colaboradores.replaceAll("vinculo.colaborador", "vi.colaborador")).append(") \n");
        }
        if (!contar) {
            sql.append("GROUP BY 1, 2, 3, 4, 5\n");
            sql.append("ORDER BY nome;");
        }
        if(paginacao != null && paginacao.isExistePaginacao()){
            sql.append(" limit ").append(paginacao.getItensPorPagina())
                    .append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
        }
        return sql;
    }

    public ResultSet contarQuantidadeEValorProdutosVencidos(Integer codEmpresa, final String colaboradores,Date dataBase) throws Exception {
        setDataFinalVenc(dataBase);
        setSemProdutos(false);
        setSituacoes(new ArrayList<SituacaoClienteEnum>());
        getSituacoes().add(SituacaoClienteEnum.ATIVO);
        StringBuilder sql = getSqlProdutosComVigencia(codEmpresa, true, colaboradores);
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        ResultSet rs = ps.executeQuery();
        return rs;
    }
    public ResultSet contarClientesProdutosVencidosResumido(Integer codEmpresa, final String colaboradores,Date dataBase) throws Exception {
        return contarClientesProdutosVencidosResumido(codEmpresa, colaboradores, dataBase, null);
    }

    public ResultSet contarClientesProdutosVencidosResumido(Integer codEmpresa, final String colaboradores,Date dataBase, Date dataBaseInicio) throws Exception {
        setDataFinalVenc(dataBase);
        setSemProdutos(false);
        setSituacoes(new ArrayList<SituacaoClienteEnum>());
        getSituacoes().add(SituacaoClienteEnum.ATIVO);
        PreparedStatement ps = getCon().prepareStatement(obterSqlProdutosVencidosPendencia(codEmpresa,true,colaboradores, dataBaseInicio).toString());
        ResultSet rs = ps.executeQuery();
        return rs;
    }

    public StringBuilder obterSqlProdutosVencidosPendencia(Integer empresa, boolean contar,String colaboradores) throws Exception{
        return obterSqlProdutosVencidosPendencia(empresa, contar, colaboradores, null);
    }

    public StringBuilder obterSqlProdutosVencidosPendencia(Integer empresa, boolean contar,String colaboradores, Date dataBaseInicio) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (!contar) {
            sql.append("  dw.codigocliente as codigoCliente,\n");
            sql.append("  matricula,\n");
            sql.append("  dw.nomecliente as nomecliente,\n");
            sql.append("  array (select t.numero from telefone t where t.pessoa = dw.codigopessoa) as telefones,\n");
            sql.append("  prod.descricao,\n");
            sql.append("  mp.datafinalvigencia,\n");
            sql.append("  mp.totalfinal,\n");
            sql.append("  mp.responsavellancamento,\n");
            sql.append("  usu.nome as responsaovel\n");
        } else {
            sql.append("  count(distinct(dw.codigocliente)) as qtd,\n");
            sql.append("  sum(mp.totalfinal) as total\n");
        }
        sql.append( "from movproduto mp inner join situacaoclientesinteticodw dw on dw.codigopessoa = mp.pessoa \n");
        sql.append("  inner join produto prod on prod.codigo = mp.produto inner join usuario usu on usu.codigo = mp.responsavellancamento\n");
        sql.append("  join lateral (select mp.produto, mp.pessoa, cl.codigo as cli, max(mp.codigo) codigo \n");
        sql.append("  from movproduto mp\n");
        sql.append("  inner join produto pd on mp.produto = pd.codigo\n");
        sql.append("  inner join cliente cl on mp.pessoa = cl.pessoa\n");
        sql.append("  where pd.tipovigencia   IN ('ID', 'VV')\n");
        sql.append("  and pd.tipoproduto  IN ('SE','AT')\n");
        sql.append("  and cl.situacao = 'AT'\n");
        sql.append("and mp.datafinalvigencia < '").append(Uteis.getDataHoraJDBC(getDataFinalVenc(), "00:00:00")).append("'\n");
        if(!UteisValidacao.emptyNumber(empresa)){
            sql.append("      AND cl.empresa = ").append(empresa).append("\n");
        }

        sql.append("    and not exists (select 1 from movproduto mp1 where mp.pessoa = mp1.pessoa and mp.produto = mp1.produto and mp1.datalancamento > mp.datalancamento)\n");
        sql.append(" group by mp.produto, mp.pessoa, cl.codigo) res on mp.codigo = res.codigo\n");

        if (dataBaseInicio != null){
            sql.append(" AND  mp.datafinalvigencia >= '" + Uteis.getDataJDBC(dataBaseInicio) + " 00:00:00' \n");
        }
        if (getConsultorSelecionado() != null && getConsultorSelecionado().getCodigo() > 0) {
            sql.append(" AND exists (select 1 from vinculo where  cliente = dw.codigocliente= and vinculo.colaborador in (").append(getConsultorSelecionado().getCodigo()).append(")) \n");
        }
        if(!UteisValidacao.emptyString(colaboradores) && !colaboradores.equals("true")){
            sql.append(" AND exists (select 1 from vinculo where  cliente = dw.codigocliente and (").append(colaboradores).append(")) \n");
        }



        if (!contar) {
            sql.append(" ORDER BY nomecliente");
        }
        return sql;
    }
    public ResultSet contarQuantidadeSemProdutos(Integer codEmpresa, final String colaboradores) throws Exception {
        setSemProdutos(true);
        setSituacoes(new ArrayList<SituacaoClienteEnum>());
        getSituacoes().add(SituacaoClienteEnum.ATIVO);
        StringBuilder sql = getSqlClientesSemProdutos(codEmpresa,true, colaboradores,null);
        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        return ps.executeQuery();
    }

    /**
     *
     * @param codEmpresa         0 para consultar de todas as empresas
     * @param colaboradores
     * @param dataBaseInicio
     * @return
     * @throws Exception
     */
    public ResultSet contarClientesSemProdutosSintetico(Integer codEmpresa, final String colaboradores, Date dataBaseInicio) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT count(distinct scsdw.nomecliente) as qtd").append("\n");
        sql.append("FROM situacaoclientesinteticodw scsdw").append("\n");

            sql.append("WHERE scsdw.empresacliente = ").append(codEmpresa).append(" AND scsdw.situacao = 'AT' \n");
            sql.append(" AND not exists(select 1 from movproduto mp inner join produto p on p.codigo = mp.produto where mp.pessoa = scsdw.codigopessoa and p.tipoproduto in ('SE','AT') and p.tipovigencia   IN ('ID', 'VV')) \n");

        if (dataBaseInicio != null) {
            sql.append(" AND scsdw.datacadastro >= '" + Uteis.getData(dataBaseInicio) + " 00:00:00'\n");
        }
        if(!UteisValidacao.emptyString(colaboradores) && !colaboradores.equals("true")) {
            sql.append(" AND exists (select 1 from vinculo where  vinculo.cliente = scsdw.codigocliente  and ").append(colaboradores).append(") \n");;
        }

        PreparedStatement ps = getCon().prepareStatement(sql.toString());
        return ps.executeQuery();
    }
    private StringBuilder getSqlProdutosComVigencia(Integer empresa, boolean contar, final String colaboradores) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT\n");
        if (!contar) {
            sql.append("  cli.codigo as codigoCliente,\n");
            sql.append("  cli.matricula,\n");
            sql.append("  pes.nome,\n");
            sql.append("  array (select t.numero from telefone t where t.pessoa = pes.codigo) as telefones,\n");
            sql.append("  prod.descricao,\n");
            sql.append("  mp.datafinalvigencia,\n");
            sql.append("  mp.totalfinal,\n");
            sql.append("  mp.responsavellancamento,\n");
            sql.append("  usu.nome,\n");
            sql.append("  emp.nome as nomeEmpresa\n");
        } else {
            sql.append("  count(*) as qtd,\n");
            sql.append("  sum(mp.totalfinal) as total\n");
        }
        sql.append("FROM movproduto mp\n");
        sql.append("  INNER JOIN produto prod ON mp.produto = prod.codigo AND prod.tipovigencia IN ('ID', 'VV')\n");
        sql.append("  INNER JOIN pessoa pes ON mp.pessoa = pes.codigo\n");
        sql.append("  INNER JOIN cliente cli ON pes.codigo = cli.pessoa\n");
        sql.append("  INNER JOIN empresa emp ON emp.codigo = cli.empresa\n");
        sql.append("  INNER JOIN usuario usu on usu.codigo = mp.responsavellancamento ");
        filtrarSituacoes(sql);
        sql.append("  LEFT JOIN vinculo vi ON scsdw.codigocliente = vi.cliente AND vi.tipovinculo = 'CO'\n");
        sql.append("  INNER JOIN(SELECT distinct movproduto.pessoa,movproduto.produto,max(movproduto.codigo) as codigo\n");
        sql.append("    FROM movproduto \n");
        sql.append("        INNER JOIN produto ON movproduto.produto = produto.codigo\n");
        if (!UteisValidacao.emptyString(getTipoProduto())) {
            sql.append("      AND produto.tipoproduto IN ('").append(getTipoProduto()).append("')\n");
        } else {
            sql.append("      AND produto.tipoproduto IN ('").append(TipoProduto.SERVICO.getCodigo()).append("','").append(TipoProduto.ATESTADO.getCodigo()).append("')\n");
        }
        sql.append("and not exists (select 1 from movproduto mp1 where\tmovproduto.pessoa = mp1.pessoa and movproduto.produto = mp1.produto and movproduto.codigo != mp1.codigo and mp1.datafinalvigencia > movproduto.datafinalvigencia)");
        sql.append("    GROUP BY movproduto.pessoa, movproduto.produto)  as movAux \n");
        sql.append("        ON  movAux.codigo = mp.codigo AND movAux.produto = mp.produto\n");
        sql.append("WHERE 1 = 1 AND mp.pessoa = movAux.pessoa\n");
        if (getCodProduto() != null && getCodProduto() > 0) {
            sql.append("      AND mp.produto = ").append(getCodProduto()).append("\n");
        }

        if (getConsultorSelecionado() != null && getConsultorSelecionado().getCodigo() > 0) {
            sql.append("      AND vi.colaborador = ").append(getConsultorSelecionado().getCodigo()).append("\n");
        }
        if(!UteisValidacao.emptyString(colaboradores)){
            sql.append(" AND exists (select codigo from vinculo where cli.codigo = vinculo.cliente and (").append(colaboradores).append(")) \n");
        }

        if (getResponsavelLancamento() != null && getResponsavelLancamento().getCodigo() > 0) {
            sql.append("      AND mp.responsavellancamento = ").append(getResponsavelLancamento().getCodigo()).append("\n");
        }

        if (getDataInicioLanc() != null) {
            sql.append("      AND mp.datalancamento >= '").append(Uteis.getDataHoraJDBC(getDataInicioLanc(), "00:00:00")).append("'\n");
        }

        if (getDataFinalLanc() != null) {
            sql.append("      AND mp.datalancamento <= '").append(Uteis.getDataHoraJDBC(getDataFinalLanc(), "23:59:59")).append("'\n");
        }

        if (getDataInicioVenc() != null) {
            sql.append("      AND mp.datafinalvigencia >= '").append(Uteis.getDataHoraJDBC(getDataInicioVenc(), "00:00:00")).append("'\n");
        }

        if (getDataFinalVenc() != null) {
            sql.append("      AND mp.datafinalvigencia <= '").append(Uteis.getDataHoraJDBC(getDataFinalVenc(), "00:00:00")).append("'\n");
        }

        if (getDataInicioCadastroCliente() != null) {
            sql.append("      AND pes.datacadastro >= '").append(Uteis.getDataHoraJDBC(getDataInicioCadastroCliente(), "00:00:00")).append("'\n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("      AND cli.empresa = ").append(empresa).append("\n");
        }

        if (!contar) {
            sql.append(" ORDER BY pes.nome");
        }
        return sql;
    }

    private void filtrarSituacoes(StringBuilder sql) {
        sql.append("  INNER JOIN situacaoclientesinteticodw scsdw ON scsdw.codigopessoa = cli.pessoa\n");

        StringBuilder situacaoContrato = new StringBuilder();
        StringBuilder situacao = new StringBuilder();

        for (SituacaoClienteEnum sit : getSituacoes()) {
            if (sit.isSituacaoCliente()) {
                situacao.append("'").append(sit.getCodigo()).append("',");
            } else {
                situacaoContrato.append("'").append(sit.getCodigo()).append("',");
            }
        }

        if (situacao.length() > 0) {
            situacao.deleteCharAt(situacao.length() - 1);
        }

        if (situacaoContrato.length() > 0) {
            situacaoContrato.deleteCharAt(situacaoContrato.length() - 1);
        }

        if (situacao.length() > 0 || situacaoContrato.length() > 0) {
            sql.append("    AND (");
            if (situacao.length() > 0) {
                sql.append("scsdw.situacao IN (").append(situacao.toString()).append(") ");
            }
            if (situacao.length() > 0 && situacaoContrato.length() > 0) {
                sql.append(" OR ");
            }
            if (situacaoContrato.length() > 0) {
                sql.append("scsdw.situacaocontrato IN (").append(situacaoContrato.toString()).append(")");
            }
            sql.append(")\n");
        }
    }


    public ColaboradorVO getConsultorSelecionado() {
        return consultorSelecionado;
    }

    public void setConsultorSelecionado(ColaboradorVO consultorSelecionado) {
        this.consultorSelecionado = consultorSelecionado;
    }

    public Date getDataInicioLanc() {
        return dataInicioLanc;
    }

    public void setDataInicioLanc(Date dataInicioLanc) {
        this.dataInicioLanc = dataInicioLanc;
    }

    public Date getDataFinalLanc() {
        return dataFinalLanc;
    }

    public void setDataFinalLanc(Date dataFinalLanc) {
        this.dataFinalLanc = dataFinalLanc;
    }

    public Date getDataInicioVenc() {
        return dataInicioVenc;
    }

    public void setDataInicioVenc(Date dataInicioVenc) {
        this.dataInicioVenc = dataInicioVenc;
    }

    public Date getDataFinalVenc() {
        return dataFinalVenc;
    }

    public void setDataFinalVenc(Date dataFinalVenc) {
        this.dataFinalVenc = dataFinalVenc;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public Integer getCodProduto() {
        return codProduto;
    }

    public void setCodProduto(Integer codProduto) {
        this.codProduto = codProduto;
    }

    public boolean getSemProdutos() {
        return semProdutos;
    }

    public void setSemProdutos(boolean semProdutos) {
        this.semProdutos = semProdutos;
    }

    public ArrayList<SituacaoClienteEnum> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(ArrayList<SituacaoClienteEnum> situacoes) {
        this.situacoes = situacoes;
    }

    public UsuarioVO getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(UsuarioVO responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Date getDataInicioCadastroCliente() {
        return dataInicioCadastroCliente;
    }

    public void setDataInicioCadastroCliente(Date dataInicioCadastroCliente) {
        this.dataInicioCadastroCliente = dataInicioCadastroCliente;
    }
}

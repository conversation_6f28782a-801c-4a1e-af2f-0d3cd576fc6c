/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import relatorio.negocio.comuns.financeiro.DetalhamentoLancamentoDF_VO;
import br.com.pactosolucoes.enumeradores.EntidadeRateioEnum;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoCamposPesquisarRateio;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;

import java.io.Serializable;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class DetalhamentoLancamentoDF implements Serializable {

    private static final String styleMarcar = "text-align:right; font-family: Arial,Helvetica,sans-serif;font-size: 11px;line-height: 125%; text-decoration: none; color: blue";
    public static DetalhamentoLancamentoDF_VO consultarDetalhesDoLancamento(LancamentoDF lancamento,
            TipoVisualizacaoRelatorioDF tipoVisualizacao,
            DemonstrativoFinanceiro dfSelecionado,
            List<Integer> listaCentroCustoSelecionado,
            Date dataInicioRelatorio,
            Date dataFimRelatorio, TipoRelatorioDF tipo, Integer contrato, boolean comissao) throws Exception {
        DetalhamentoLancamentoDF_VO objRetornar = null;
        if (lancamento.getMovPagamento() > 0) {
            // Consultar dodos do Pagamento.
            objRetornar = consultarDadosReceita(lancamento, tipoVisualizacao, dfSelecionado, listaCentroCustoSelecionado, dataInicioRelatorio, dataFimRelatorio, contrato, comissao);
        } else {
            //Consultar dados do MovProduto
            objRetornar = consultarDadosCompetencia(lancamento, tipoVisualizacao, dfSelecionado, listaCentroCustoSelecionado);
        }
        objRetornar.setValorLancamento(lancamento.getValorLancamento());
        objRetornar.setValorMultaJuros(lancamento.getValorMultaJuros());
        return objRetornar;
    }

    private static DetalhamentoLancamentoDF_VO consultarDadosCompetencia(LancamentoDF lancamento,
            TipoVisualizacaoRelatorioDF tipoVisualizacao, DemonstrativoFinanceiro dfSelecionado,
            List<Integer> listaCentroCustoSelecionado) throws Exception {
        DetalhamentoLancamentoDF_VO objRetornar = new DetalhamentoLancamentoDF_VO();
        objRetornar.setEmpresa(lancamento.getEmpresa());
        consultarDadosDoContrato(lancamento, objRetornar);

        if (lancamento.getTipoProduto().equals("PM")) {
            // Realizar rateio das modalidade do Plano.
            realizarRateioModalidades(objRetornar, tipoVisualizacao, dfSelecionado, lancamento.isLancamentoEhNaoAtribuido(), listaCentroCustoSelecionado);
        } else {
            // Realizar rateio do produto
            List<ProdutoVO> listaProduto = new ArrayList<ProdutoVO>();
            ProdutoVO produtoVo = new ProdutoVO();
            ResultSet resultDados = SuperFacadeJDBC.criarConsulta("select prod.Codigo, prod.descricao, prod.categoriaProduto, p.nome, cli.matricula, movP.totalFinal "
                    + "from produto prod "
                    + "inner join movProduto movP on movP.produto = prod.codigo "
                    + "left join pessoa p on p.codigo = movP.pessoa "
                    + "left join cliente cli on cli.pessoa = movP.pessoa "
                    + "where movP.codigo = " + lancamento.getMovProduto(), FacadeManager.getFacade().getRisco().getCon());
            if (resultDados.next()) {
                produtoVo.setCodigo(resultDados.getInt("Codigo"));
                produtoVo.setDescricao(resultDados.getString("descricao"));
                produtoVo.getCategoriaProduto().setCodigo(resultDados.getInt("categoriaProduto"));
                produtoVo.setValorBaseCalculo(resultDados.getDouble("totalFinal"));
                if (lancamento.getCodigoPessoa() > 0) {
                    if (resultDados.getString("matricula") == null) {
                        // É um colaborador
                        objRetornar.setNomeCliente("Colaborador - " + resultDados.getString("nome"));
                    } else {
                        // É um Cliente
                        objRetornar.setMatriculaCliente(resultDados.getString("matricula"));
                        objRetornar.setNomeCliente(resultDados.getString("nome"));
                    }
                } else {
                    // É um Consumidor
                    objRetornar.setNomeCliente("Consumidor");
                }
                objRetornar.setValorMovProduto(resultDados.getDouble("totalFinal"));
            }
            listaProduto.add(produtoVo);

            realizarRateioProdutos(objRetornar, listaProduto, tipoVisualizacao, dfSelecionado, lancamento.isLancamentoEhNaoAtribuido(), lancamento, listaCentroCustoSelecionado);
        }
        return objRetornar;
    }

    private static void consultarDadosDoContrato(LancamentoDF lancamento, DetalhamentoLancamentoDF_VO detalhamentoLancamentoDF) throws Exception {
        // Consultar dados do Contrato
        if (lancamento.getContrato() > 0) {
            ContratoVO contratoVo = FacadeManager.getFacade().getContrato().consultarPorCodigo(lancamento.getContrato(),
                    lancamento.isDadosResumidos() ? Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA : Uteis.NIVELMONTARDADOS_ROBO);
            detalhamentoLancamentoDF.setContratoVO(contratoVo);

            // Pesquisar as Modalidades do contrato e calcular o percentual de cada uma em relação ao valor da mensalidade.
            double somaTotalMensal = 0;
            List<ContratoModalidadePercentual> listaContratoModalidade = new ArrayList<ContratoModalidadePercentual>();
            for (LancamentoDF lanc : detalhamentoLancamentoDF.getListaLancamentoDF()) {
                if (lanc.getContrato() == lancamento.getContrato()) {
                    double tudo = 0.0;
                    Map<Integer, ContratoModalidadePercentual> mapa = new HashMap();
                    for (ProdutoRatear prodRatear : lanc.getListaProdutoRatear()) {
                        for (ContratoModalidadePercentual cmPerc : prodRatear.getListaContratoModalidadePercentual()) {
                            if (!UteisValidacao.emptyNumber(cmPerc.getPercentagem())) {
                                ContratoModalidadePercentual cmp = mapa.get(cmPerc.getCodigoModalidade());
                                tudo += cmPerc.getPercentagem();
                                if (cmp == null) {
                                    mapa.put(cmPerc.getCodigoModalidade(), cmPerc);
                                } else {
                                    cmp.setPercentagem(cmp.getPercentagem() + cmPerc.getPercentagem());
                                }
                            }

                        }
                    }
                    listaContratoModalidade.addAll(mapa.values());
                    for (ContratoModalidadePercentual obj : listaContratoModalidade) {
                        obj.setPercentagem(tudo == 0.0 ? 0.0 : (obj.getPercentagem() / tudo) * 1);
                    }
                }
            }
            if (listaContratoModalidade.isEmpty()) {
                listaContratoModalidade = lancamento.getRateioModalidades();
            }

            for (ContratoModalidadePercentual obj : listaContratoModalidade) {
                somaTotalMensal += obj.getValor();
            }

            double diferenca = somaTotalMensal - (detalhamentoLancamentoDF.getContratoVO().getValorBaseCalculo() / detalhamentoLancamentoDF.getContratoVO().getContratoDuracao().getNumeroMeses());
            if (diferenca > 0) {
                int posicaoUltimo = listaContratoModalidade.size() - 1;
                double valorUltimo = listaContratoModalidade.get(posicaoUltimo).getValor();
                listaContratoModalidade.get(posicaoUltimo).setValor(valorUltimo - diferenca);
            }

            detalhamentoLancamentoDF.setValorPlanoMensal(somaTotalMensal);
            for (ContratoModalidadePercentual obj : lancamento.getRateioModalidades()) {
                // Calcular o valor que cada modalidade representa em relação ao valor do mensalidade.
                obj.setValor(obj.getPercentagem() * detalhamentoLancamentoDF.getValorPlanoMensal());
                obj.setValorPago(obj.getPercentagem() * detalhamentoLancamentoDF.getValorPlanoMensal());
            }

            detalhamentoLancamentoDF.setListaContratoModalidade(listaContratoModalidade);
        }
    }

    private static boolean pintarRateio(List<RateioIntegracaoTO> listaRateio, DemonstrativoFinanceiro dfSelecionado, boolean lancamentoEhNaoAtribuido, List<Integer> listaCentroCustoSelecionado) {
        // Pintar os rateios de Centro de Custo, somente para o plano de contas selecionado.
        boolean pintar = false;
        if (listaCentroCustoSelecionado.size() > 0) {
            for (RateioIntegracaoTO rateio : listaRateio) {
                if ((rateio.getCodigoPlano() != null) && (rateioEhIgualAoRateioSelecionado(rateio.getCodigoPlano(), dfSelecionado.getCodigoAgrupador(), lancamentoEhNaoAtribuido))) {
                    pintar = true;
                    break;
                }
            }
        }

        return pintar;
    }

    private static void realizarRateioModalidades(DetalhamentoLancamentoDF_VO detalhamentoLancamentoDF_VO, 
            TipoVisualizacaoRelatorioDF tipoVisualizacao, DemonstrativoFinanceiro dfSelecionado, 
            boolean lancamentoEhNaoAtribuido, List<Integer> listaCentroCustoSelecionado) throws Exception {
        
        Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaPlanoContas = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);
        Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaCentroCusto = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);                
        
        List<RateioIntegracaoTO> listaRateio;
        for (ContratoModalidadePercentual modalidade : detalhamentoLancamentoDF_VO.getListaContratoModalidade()) {
            listaRateio = FacadeManager.getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.MODALIDADE, modalidade.getCodigoModalidade());
            if(listaRateio.isEmpty()){
                listaRateio = FacadeManager.getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.GERAL_MODALIDADES, modalidade.getCodigoModalidade());
            }
            for(RateioIntegracaoTO rateio : listaRateio){
                if(rateio.getCodigoPlanoContas() > 0){
                    ThreadDemonstrativoFinanceiro.preencherMapaRateioMultiEmpresa(rateio, rateioMultiEmpresaPlanoContas);
                } else {
                    ThreadDemonstrativoFinanceiro.preencherMapaRateioMultiEmpresa(rateio, rateioMultiEmpresaCentroCusto);
                }
            }
            
            boolean pintarRateioCentroCusto = pintarRateio(listaRateio, dfSelecionado, lancamentoEhNaoAtribuido, listaCentroCustoSelecionado);
            for (RateioIntegracaoTO rateio : listaRateio) {
                
                if ((!UteisValidacao.emptyNumber(rateio.getEmpresa())
                        && !rateio.getEmpresa().equals(detalhamentoLancamentoDF_VO.getEmpresa()))
                        || (UteisValidacao.emptyNumber(rateio.getEmpresa()) 
                            && ThreadDemonstrativoFinanceiro.temRateioDaEmpresa(detalhamentoLancamentoDF_VO.getEmpresa(), rateio, rateioMultiEmpresaPlanoContas, rateioMultiEmpresaCentroCusto))) {
                    continue;
                }
                
                if ((rateio.getCodigoCentro() != null) && (!rateio.getCodigoCentro().equals(""))) {
                    rateio.setTotalCentroCusto(rateio.getPercent() * modalidade.getValorPago());
                    rateio.setPercentagemCentroCusto(rateio.getPercentagem());
                    rateio.setPercentagem(0.0);
                } else {
                    rateio.setTotalPlanoConta(rateio.getPercent() * modalidade.getValorPago());
                }
                rateio.setDescricao(modalidade.getNomeModalidade());
                if (tipoVisualizacao == TipoVisualizacaoRelatorioDF.PLANOCONTA) {
                    if (pintarRateioCentroCusto) {
                        // Neste caso o usuário está visualizando o relatório por Plano de Contas, e aplicando filtro de Centro de Custos.
                        if (listaCentroCustoSelecionado.contains(rateio.getCodigoCentroCustos())) {
                            rateio.setStyleCentroCusto(styleMarcar);
                        }
                    } else {
                        if ((rateio.getCodigoPlano() != null) && (rateioEhIgualAoRateioSelecionado(rateio.getCodigoPlano(), dfSelecionado.getCodigoAgrupador(), lancamentoEhNaoAtribuido))) {
                            rateio.setStylePlanoConta(styleMarcar);
                        }
                    }
                } else {
                    if ((rateio.getCodigoCentro() != null) && (rateioEhIgualAoRateioSelecionado(rateio.getCodigoCentro(), dfSelecionado.getCodigoAgrupador(), lancamentoEhNaoAtribuido))) {
                        rateio.setStyleCentroCusto(styleMarcar);
                    }
                }
                detalhamentoLancamentoDF_VO.getListaRateios().add(rateio);
            }
            if ((tipoVisualizacao == TipoVisualizacaoRelatorioDF.PLANOCONTA) && (listaCentroCustoSelecionado.size() > 0)) {
                calcularValorParaCentroCustoComFiltro(listaRateio, dfSelecionado, modalidade.getValorPago());
            }
        }
    }

    private static void realizarRateioProdutos(DetalhamentoLancamentoDF_VO detalhamentoLancamentoDF_VO, List<ProdutoVO> listaProdutos, TipoVisualizacaoRelatorioDF tipoVisualizacao, DemonstrativoFinanceiro dfSelecionado, boolean lancamentoEhNaoAtribuido, LancamentoDF lancamento, List<Integer> listaCentroCustoSelecionado) throws Exception {
        List<RateioIntegracaoTO> listaRateio;
        Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaPlanoContas = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);
        Map<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>> rateioMultiEmpresaCentroCusto = new EnumMap<TipoCamposPesquisarRateio,Map<Integer, Set<Integer>>>(TipoCamposPesquisarRateio.class);
                
        
        for (ProdutoVO produto : listaProdutos) {
            // Consultar os rateios do Produto

            if (produto.getTipoProduto().equals("PM")) {
                // Não realizar rateio para os produtos do tipo Plano.
                continue;
            }

            if ((produto.getTipoProduto().equals("DI"))
                    || produto.getTipoProduto().equals("AA")) {
                // Realizar rateio para venda de Diária e Aula Avulsa.
                List<RateioIntegracaoTO> listaRateioLocal;
                List<RateioIntegracaoTO> listaTodosRateio = FacadeManager.getFacade().getFinanceiro().getRateioIntegracao().consultar(null, 0);
                listaRateio = new ArrayList<RateioIntegracaoTO>();

                // Pesquisar os rateios de Plano de contas. Considerar os rateios definido no cadastro do Produto.
                listaRateioLocal = ThreadDemonstrativoFinanceiro.pesquisarRateios(listaTodosRateio, TipoCamposPesquisarRateio.PRODUTO, produto.getCodigo(), TipoVisualizacaoRelatorioDF.PLANOCONTA, null);
                if (listaRateioLocal.size() <= 0) {
                    // Rateio foi definido na categoria do produto
                    listaRateioLocal = ThreadDemonstrativoFinanceiro.pesquisarRateios(listaTodosRateio, TipoCamposPesquisarRateio.CATEGORIAPRODUTO, produto.getCategoriaProduto().getCodigo(), 
                            TipoVisualizacaoRelatorioDF.PLANOCONTA, null);
                }
                for (RateioIntegracaoTO rateio : listaRateioLocal) {
                    listaRateio.add(rateio);
                }

                // Pesquisar os rateios de Centro de custo. Considerar os  definido na modalidade que foi vendida a Aula ou a Diária.
                int codigoModalidade = ThreadDemonstrativoFinanceiro.consultarModalidadeVendaDiariaAulaAvulsa(lancamento.getMovProduto(), Conexao.getFromSession());

                listaRateioLocal = ThreadDemonstrativoFinanceiro.pesquisarRateios(listaTodosRateio, TipoCamposPesquisarRateio.PRODUTO, 
                        produto.getCodigo(), TipoVisualizacaoRelatorioDF.CENTROCUSTO, null);

                if (listaRateioLocal.size() <= 0) {
                    listaRateioLocal = ThreadDemonstrativoFinanceiro.pesquisarRateios(listaTodosRateio, TipoCamposPesquisarRateio.MODALIDADE, 
                            codigoModalidade, TipoVisualizacaoRelatorioDF.CENTROCUSTO, null);
                }

                if (listaRateioLocal.size() <= 0) {
                    // Rateio foi definido na categoria do produto
                    listaRateioLocal = ThreadDemonstrativoFinanceiro.pesquisarRateios(listaTodosRateio, TipoCamposPesquisarRateio.CATEGORIAPRODUTO, 
                            produto.getCategoriaProduto().getCodigo(), TipoVisualizacaoRelatorioDF.CENTROCUSTO, null);
                }

                for (RateioIntegracaoTO rateio : listaRateioLocal) {
                    listaRateio.add(rateio);
                }
            } else {
                listaRateio = FacadeManager.getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.PRODUTO, produto.getCodigo());
                if (listaRateio.isEmpty()) {
                    // Rateio foi definido na categoria do produto
                    listaRateio = FacadeManager.getFacade().getFinanceiro().getRateioIntegracao().consultar(EntidadeRateioEnum.CATEGORIA_PRODUTO, produto.getCategoriaProduto().getCodigo());
                }
            }
            for (RateioIntegracaoTO rateio : listaRateio) {
                if(rateio.getCodigoPlanoContas() > 0){
                    ThreadDemonstrativoFinanceiro.preencherMapaRateioMultiEmpresa(rateio, rateioMultiEmpresaPlanoContas);
                } else {
                    ThreadDemonstrativoFinanceiro.preencherMapaRateioMultiEmpresa(rateio, rateioMultiEmpresaCentroCusto);
                }
            }

            boolean pintarRateioCentroCusto = pintarRateio(listaRateio, dfSelecionado, lancamentoEhNaoAtribuido, listaCentroCustoSelecionado);
            
            for (RateioIntegracaoTO rateio : listaRateio) {
                
                if ((!UteisValidacao.emptyNumber(rateio.getEmpresa())
                        && !rateio.getEmpresa().equals(detalhamentoLancamentoDF_VO.getEmpresa()))
                        || (UteisValidacao.emptyNumber(rateio.getEmpresa()) 
                            && ThreadDemonstrativoFinanceiro.temRateioDaEmpresa(detalhamentoLancamentoDF_VO.getEmpresa(), rateio, rateioMultiEmpresaPlanoContas,rateioMultiEmpresaCentroCusto))) {
                    continue;
                }
                
                if ((rateio.getCodigoCentro() != null) && (!rateio.getCodigoCentro().equals(""))) {
                    rateio.setTotalCentroCusto(rateio.getPercent() * produto.getValorBaseCalculo());
                    //rateio.setTotalCentroCusto(rateio.getPercent() * lancamento.getValorLancamento());
                    rateio.setPercentagemCentroCusto(rateio.getPercentagem());
                    rateio.setPercentagem(0.0);
                } else {
                    rateio.setTotalPlanoConta(rateio.getPercent() * produto.getValorBaseCalculo());
                }
                rateio.setDescricao(produto.getDescricao());
                if (tipoVisualizacao == TipoVisualizacaoRelatorioDF.PLANOCONTA) {
                    if (pintarRateioCentroCusto) {
                        // Neste caso o usuário está visualizando o relatório por Plano de Contas, e aplicando filtro de Centro de Custos.
                        if (listaCentroCustoSelecionado.contains(rateio.getCodigoCentroCustos())) {
                            rateio.setStyleCentroCusto(styleMarcar);
                        }
                    } else {
                        if ((rateio.getCodigoPlano() != null) && (rateioEhIgualAoRateioSelecionado(rateio.getCodigoPlano(), dfSelecionado.getCodigoAgrupador(), lancamentoEhNaoAtribuido))) {
                            rateio.setStylePlanoConta(styleMarcar);
                        }
                    }
                } else {
                    if ((rateio.getCodigoCentro() != null) && (rateioEhIgualAoRateioSelecionado(rateio.getCodigoCentro(), dfSelecionado.getCodigoAgrupador(), lancamentoEhNaoAtribuido))) {
                        rateio.setStyleCentroCusto(styleMarcar);
                    }
                }
                detalhamentoLancamentoDF_VO.getListaRateios().add(rateio);
            }
            if ((tipoVisualizacao == TipoVisualizacaoRelatorioDF.PLANOCONTA) && (listaCentroCustoSelecionado.size() > 0)) {
                calcularValorParaCentroCustoComFiltro(listaRateio, dfSelecionado, produto.getValorBaseCalculo());
            }
        }


    }

    private static void calcularValorParaCentroCustoComFiltro(List<RateioIntegracaoTO> listaRateio, DemonstrativoFinanceiro dfSelecionado, double valorProduto) {
        // O usuário está visualizando o relatório por Plano de Contas, e aplicando filtro de Centro de Custos.

        /*
         * Motivação do Método: O sistema permite ratear o valor de um produto, para mais de um plano de contas.
         *                      Desta forma, ao selecionar o plano de contas, calcular o percentual de cada Centro de Custo,
         *                      em relação ao valor rateado para o Plano de Contas.
         */
        double valorPlanoConta = 0;
        for (RateioIntegracaoTO rateio : listaRateio) {
            // Pegar o valor do plano de contas Selecionado.
            if ((rateio.getCodigoPlano() != null) && (rateio.getCodigoPlano().equals(dfSelecionado.getCodigoAgrupador()))) {
                valorPlanoConta = rateio.getTotalPlanoConta();
                break;
            }
        }
        if (valorPlanoConta == 0) {
            // Se o "valorPlanoConta" for igual a zero, então o usuário selecionou um Plano de Conta Pai. Desta forma, considerar o valor cheio do produto.
            valorPlanoConta = valorProduto;
        }

        // Realizar o rateio do valor do Plano de contas selecionado, para os rateios de Centro de Custo.
        for (RateioIntegracaoTO rateio : listaRateio) {
            if ((rateio.getCodigoCentro() != null)) {
                rateio.setTotalCentroCusto((valorPlanoConta * rateio.getPercentagemCentroCusto()) / 100);
            }
        }
    }

    private static DetalhamentoLancamentoDF_VO consultarDadosReceita(LancamentoDF lancamento,
            TipoVisualizacaoRelatorioDF tipoVisualizacao,
            DemonstrativoFinanceiro dfSelecionado,
            List<Integer> listaCentroCustoSelecionado,
            Date dataInicioRelatorio,
            Date dataFimRelatorio,
            Integer contrato,  boolean comissao) throws Exception {
        DetalhamentoLancamentoDF_VO objRetornar = new DetalhamentoLancamentoDF_VO();
        objRetornar.setEmpresa(lancamento.getEmpresa());
        
        ResultSet resultPagto = SuperFacadeJDBC.criarConsulta("select MovP.nomePagador, MovP.produtospagos,MovP.produtospagoscancelados, MovP.valor, MovP.formaPagamento, MovP.recibopagamento, fp.tipoformapagamento, fp.descricao, MovP.DataPagamento, p.nome "
                + "from MovPagamento MovP "
                + "inner join formapagamento fp on fp.codigo = MovP.formapagamento "
                + "left join pessoa p on p.codigo = movP.pessoa "
                + "left join cliente cli on cli.pessoa = movP.pessoa "
                + "where MovP.codigo =" + lancamento.getMovPagamento(),FacadeManager.getFacade().getRisco().getCon());
        objRetornar.setMovPagamento(lancamento.getMovPagamento());

        TipoFormaPagto tipoFormaPgto = TipoFormaPagto.AVISTA;
        if (resultPagto.next()) {
            objRetornar.setPessoaPagador(resultPagto.getString("nomePagador"));
            objRetornar.setValorPagamento(resultPagto.getDouble("valor"));
            objRetornar.setProdutosPagos(
                    (resultPagto.getString("produtospagos") != null ? resultPagto.getString("produtospagos") : "") +
                            (resultPagto.getString("produtospagoscancelados") != null && !comissao ? resultPagto.getString("produtospagoscancelados") : "")); // comissão não deve considerar produtos cancelados
            objRetornar.setReciboPagamento(resultPagto.getInt("recibopagamento"));
            if (resultPagto.getString("tipoformapagamento").equals("CH")) {
                tipoFormaPgto = TipoFormaPagto.CHEQUE;
            } else if (resultPagto.getString("tipoformapagamento").equals("CA")) {
                tipoFormaPgto = TipoFormaPagto.CARTAOCREDITO;
            }
        }

        // Consutar o nome e a matrícula do cliente, do lançamento selecionado.
        if (lancamento.getCodigoPessoa() > 0) {

            ResultSet resultDados = SuperFacadeJDBC.criarConsulta("select p.nome, cli.codigo codigoCliente, cli.matricula  "
                    + "from pessoa p "
                    + "left join cliente cli on cli.pessoa = p.codigo "
                    + "where p.codigo = " + lancamento.getCodigoPessoa(), FacadeManager.getFacade().getRisco().getCon());
            if (resultDados.next()) {
                if (resultDados.getInt("codigoCliente") > 0) {
                    objRetornar.setNomeCliente(resultDados.getString("nome"));
                    objRetornar.setMatriculaCliente(resultDados.getString("matricula"));
                } else {
                    objRetornar.setNomeCliente("Colaborador - " + resultDados.getString("nome"));
                }
            }
        } else {
            objRetornar.setNomeCliente("CONSUMIDOR");
        }
        double totalPagamento = 0.0;
        // Consultar as formas de pagamento
        objRetornar.setListaFormaPagamento(new ArrayList<FormaPagamentoVO>());
        if (tipoFormaPgto == TipoFormaPagto.CHEQUE) {
            String produtosPagos = "";
            // Consultar os cheques
            ResultSet resultCheque = SuperFacadeJDBC.criarConsulta(
                    "select valor, dataCompesancao, produtospagos, produtospagoscancelados  from cheque where movPagamento = " + lancamento.getMovPagamento()
                    + " and ((datacompesancao >= '" + Uteis.getDataJDBC(dataInicioRelatorio)
                    + "') and (datacompesancao <= '" + Uteis.getDataHoraJDBC(dataFimRelatorio, "23:59:59") + "')) ORDER BY datacompesancao", FacadeManager.getFacade().getRisco().getCon());
            while (resultCheque.next()) {
                FormaPagamentoVO formaPgto = new FormaPagamentoVO();
                formaPgto.setDescricao("Cheque");
                formaPgto.setValor(resultCheque.getDouble("valor"));
                formaPgto.setDataCompensacao(resultCheque.getDate("dataCompesancao"));
                objRetornar.getListaFormaPagamento().add(formaPgto);
                totalPagamento += formaPgto.getValor();
                produtosPagos += resultCheque.getString("produtospagos") + resultCheque.getString("produtospagoscancelados");
            }
            objRetornar.setProdutosPagos(produtosPagos.isEmpty() ? objRetornar.getProdutosPagos() : produtosPagos);
        } else if (tipoFormaPgto == TipoFormaPagto.CARTAOCREDITO) {
            String produtosPagos = "";
            // Consultar os Cartões crédito.
            ResultSet resultCartao = SuperFacadeJDBC.criarConsulta("select valor, dataCompesancao, produtospagos , produtospagoscancelados from cartaoCredito where movPagamento = " + lancamento.getMovPagamento()
                    + " and ((datacompesancao >= '" + Uteis.getDataJDBC(dataInicioRelatorio) + "') and (datacompesancao <= '" + Uteis.getDataHoraJDBC(dataFimRelatorio, "23:59:59") + "'))",FacadeManager.getFacade().getRisco().getCon());
            while (resultCartao.next()) {
                FormaPagamentoVO formaPgto = new FormaPagamentoVO();
                formaPgto.setDescricao("Cartão Crédito");
                formaPgto.setValor(resultCartao.getDouble("valor"));
                formaPgto.setDataCompensacao(resultCartao.getDate("dataCompesancao"));
                objRetornar.getListaFormaPagamento().add(formaPgto);
                totalPagamento += formaPgto.getValor();
                produtosPagos += resultCartao.getString("produtospagos") + resultCartao.getString("produtospagoscancelados");
            }
            objRetornar.setProdutosPagos(produtosPagos.isEmpty() ? objRetornar.getProdutosPagos() : produtosPagos);
        } else {
            // pagamento á vista
            FormaPagamentoVO formaPgto = new FormaPagamentoVO();
            formaPgto.setDescricao(resultPagto.getString("descricao"));
            formaPgto.setValor(resultPagto.getDouble("valor"));
            formaPgto.setDataCompensacao(resultPagto.getDate("DataPagamento"));
            objRetornar.getListaFormaPagamento().add(formaPgto);
            totalPagamento += formaPgto.getValor();
        }

        List<ProdutoRatear> listaProdutoRatear = new ArrayList<ProdutoRatear>();
        listaProdutoRatear = ThreadDemonstrativoFinanceiro.pesquisarProdutosDoPagamento(
                lancamento.getMovPagamento(), objRetornar.getProdutosPagos(), Conexao.getFromSession(), null);

        // Dividir o valor do pagamento por Contrato.
        ThreadDemonstrativoFinanceiro.dividirValorPagamentoPorContrato(objRetornar.getListaLancamentoDF(),
                listaProdutoRatear, totalPagamento, lancamento.getRecibo(), tipoFormaPgto,lancamento.getFormaPagApresentar(),
                lancamento.getMovPagamento(), null, null,null, Conexao.getFromSession(), false, null, null);

        //Dividir o valor do pagamento para os produtos da parcela.
        ThreadDemonstrativoFinanceiro.dividirValorDoPagamentoParaCadaProduto(objRetornar.getListaLancamentoDF());

        {
            /* Pegar o valor pago proporcional para o (Lançamento que o usuário seleciou na tela).
             * Obs.: Um pagamento pode pagar mais de um contrato para pessoas diferente.
             */
            for (LancamentoDF obj : objRetornar.getListaLancamentoDF()) {
                if (obj.getCodigoPessoa() == lancamento.getCodigoPessoa()) {
                    obj.setStyle(styleMarcar);
                    totalPagamento = obj.getValorLancamento();
                }
            }
        }
        // Consultar dados do Contrato
        consultarDadosDoContrato(lancamento, objRetornar);

        String wherePessoa = "";
        if (lancamento.getCodigoPessoa() > 0) {
            wherePessoa = " and movProd.pessoa = " + lancamento.getCodigoPessoa();
        }

        String codsProdutos = "";
        Map<Integer, Double> produtosValores = new HashMap<Integer, Double>();
        if (!UteisValidacao.emptyString(objRetornar.getProdutosPagos())) {
            String[] split = objRetornar.getProdutosPagos().split("\\|");
            for (String str : split) {
                if (!str.isEmpty() && !str.equals("null")) {
                    String[] prodvalor = str.split(",");
                    if (contrato == null || contrato == 0 || contrato.intValue() == Integer.valueOf(prodvalor[2])) {
                        codsProdutos = codsProdutos + ", " + prodvalor[0];
                        produtosValores.put(Integer.valueOf(prodvalor[0]), Double.valueOf(prodvalor[3]));
                    }
                }
            }
        }

        ResultSet resultProdutos;
        if (UteisValidacao.emptyString(codsProdutos)) {
            //Consultar os produtos que foram pagos.
            resultProdutos = SuperFacadeJDBC.criarConsulta("select movProd.produto,movProd.codigo,  prod.descricao, movProd.totalfinal, prod.tipoproduto, prod.categoriaproduto "
                    + "from pagamentomovparcela pagMovParc "
                    + "inner join MovParcela movParc on movParc.codigo = pagMovParc.movparcela "
                    + "inner join MovProdutoParcela movProdParc on movProdParc.movparcela = movParc.codigo "
                    + "inner join MovProduto movProd on movProd.codigo = movProdParc.movproduto "
                    + "inner join Produto prod on prod.codigo = movProd.produto "
                    + "where pagMovParc.movpagamento = " + lancamento.getMovPagamento()
                    + wherePessoa
                    + " and (movProd.totalfinal >0)  "
                    + " group by movProd.produto, movProd.codigo, prod.descricao, movProd.totalfinal, prod.tipoproduto, prod.categoriaproduto ", FacadeManager.getFacade().getRisco().getCon());
        } else {
            //Consultar os produtos que foram pagos.
            resultProdutos = SuperFacadeJDBC.criarConsulta("select movProd.produto, movProd.codigo, prod.descricao, "
                    + "movProd.totalfinal, prod.tipoproduto, prod.categoriaproduto,\n"
                    + "coalesce(mprodmj.totalfinal, 0) as multajuros\n"
                    + "from MovProduto movProd inner join Produto prod on prod.codigo = movProd.produto\n"
                    + "left join movproduto mprodmj ON mprodmj.movprodutooriginal = movProd.codigo\n"
                    + " WHERE movProd.codigo IN (" + codsProdutos.replaceFirst(",", "") + ") "
                    + wherePessoa
                    + " and (movProd.totalfinal >0)\n"
                    + "and coalesce(movProd.movprodutooriginal, 0) = 0\n"
                    + " group by movProd.produto, movProd.codigo, prod.descricao, movProd.totalfinal, prod.tipoproduto, prod.categoriaproduto, mprodmj.totalfinal",FacadeManager.getFacade().getRisco().getCon());

        }


        List<ProdutoVO> listaProdutos = new ArrayList<ProdutoVO>();
        while (resultProdutos.next()) {
            ProdutoVO produtoVo = new ProdutoVO();
            produtoVo.setCodigo(resultProdutos.getInt("produto"));
            if (listaProdutos.contains(produtoVo)) {
                produtoVo = listaProdutos.get(listaProdutos.indexOf(produtoVo));
                if (!resultProdutos.getString("tipoproduto").equals("PM")) {
                    Double valor = produtosValores.get(resultProdutos.getInt("codigo"));
                    produtoVo.setValorFinal(produtoVo.getValorFinal() + (valor == null ? 0.0 : valor));
                }
            } else {
                produtoVo.setDescricao(resultProdutos.getString("descricao"));
                produtoVo.setTipoProduto(resultProdutos.getString("tipoproduto"));
                produtoVo.getCategoriaProduto().setCodigo(resultProdutos.getInt("categoriaproduto"));
                if (resultProdutos.getString("tipoproduto").equals("PM")) {
                    // Setar o valor total do Plano no produto "Plano"
                    produtoVo.setValorFinal(objRetornar.getContratoVO().getValorFinal());
                    try {
                        objRetornar.setValorPlanoMensal(produtosValores.get(resultProdutos.getInt("codigo")));
                    } catch (NullPointerException npe) {
                        if (contrato == null || contrato == 0) {
                            throw new Exception("Não foi possível verificar quais produtos o pagamento " + lancamento.getMovPagamento() + " da pessoa de código " + lancamento.getCodigoPessoa() + " paga.");
                        }
                    }
                } else {
                    produtoVo.setValorFinal(produtosValores.get(resultProdutos.getInt("codigo")));
                }
                listaProdutos.add(produtoVo);
            }
        }
        double valorPagoParaPlano = 0.0;


        // Setar na variável "ValorBaseCalculo"  o valor pago proporcinal para cada produto
        for (ProdutoVO obj : listaProdutos) {
            // Setar na variável "ValorBaseCalculo"  o valor pago proporcinal.
            double valor = retornarValorProduto(listaProdutoRatear, lancamento.getCodigoPessoa(), obj.getCodigo(), contrato);
            obj.setValorBaseCalculo(valor);
            if (obj.getTipoProduto().equals("PM") || obj.getTipoProduto().equals("MM")) {
                valorPagoParaPlano += obj.getValorBaseCalculo();
            }
        }

        objRetornar.setListaProdutos(listaProdutos);
        if (lancamento.getContrato() > 0) {
            double valorDiferenca = Uteis.arredondarForcando2CasasDecimais(valorPagoParaPlano);

            if (!UteisValidacao.emptyList(objRetornar.getListaContratoModalidade())) {
                for (ContratoModalidadePercentual obj : objRetornar.getListaContratoModalidade()) {
                    // Calcular o valor que cada modalidade representa em relação ao Pagamento.
                    obj.setValorPago(obj.getPercentagem() * valorPagoParaPlano);
                    valorDiferenca = valorDiferenca - Uteis.arredondarForcando2CasasDecimais(obj.getValorPago());
                }
                if (valorDiferenca > 0) {
                    objRetornar.getListaContratoModalidade().get(0).setValorPago(objRetornar.getListaContratoModalidade().get(0).getValorPago() + valorDiferenca);
                } else if (valorDiferenca < 0) {
                    int ultimoItem = objRetornar.getListaContratoModalidade().size() - 1;
                    objRetornar.getListaContratoModalidade().get(ultimoItem).setValorPago(objRetornar.getListaContratoModalidade().get(ultimoItem).getValorPago() - valorDiferenca);
                }
            }
            // Realizar Rateio das Modalidades
            realizarRateioModalidades(objRetornar, tipoVisualizacao, dfSelecionado, lancamento.isLancamentoEhNaoAtribuido(), listaCentroCustoSelecionado);
        }
        // Atribuir o total pago para todas as modalidades.
        objRetornar.setTotalPagoPlano(valorPagoParaPlano);

        // Realizar rateio dos Produtos
        realizarRateioProdutos(objRetornar, listaProdutos, tipoVisualizacao, dfSelecionado, lancamento.isLancamentoEhNaoAtribuido(), lancamento, listaCentroCustoSelecionado);

        if (lancamento.getNegociacaoEvento() > 0) {
            for (RateioIntegracaoTO rateio : lancamento.getRateios()) {
                rateio.setDescricao(lancamento.getDescricaoLancamento());
                if (UteisValidacao.emptyNumber(rateio.getCodigoCentroCustos())) {
                    rateio.setTotalPlanoConta(lancamento.getValorLancamento() * (rateio.getPercentagem() / 100));
                    objRetornar.getListaRateios().add(rateio);
                } else {
                    rateio.setPercentagemCentroCusto(rateio.getPercentagem());
                    rateio.setPercentagem(0.0);
                    rateio.setTotalCentroCusto(lancamento.getValorLancamento() * (rateio.getPercentagemCentroCusto() / 100));
                    objRetornar.getListaRateios().add(rateio);
                }

            }
        }
        return objRetornar;

    }

    private static double retornarValorProduto(List<ProdutoRatear> listaProdutoRatear, int codigoPessoa, int codigoProduto, Integer contrato) {
        double valor = 0;
        for (ProdutoRatear obj : listaProdutoRatear) {
            if ((obj.getCodigoPessoa() == codigoPessoa)
                    && (obj.getCodigoProduto() == codigoProduto)
                    && (contrato == null || contrato == 0 || obj.getContrato() == contrato)) {
                valor += obj.getValorRatear();
            }
        }
        return valor;
    }

    private static boolean rateioEhIgualAoRateioSelecionado(String nodeRateio, String nodeSelecionado, boolean lancamentoEhNaoAtribuido) {
        // Verifica se o rateio "", é igual ou é filho do rateio selecionado.
        boolean result = false;
        if (nodeRateio.equals(nodeSelecionado)) {
            return true;
        }

        if ((lancamentoEhNaoAtribuido) && (!nodeRateio.equals(nodeSelecionado))) {
            // Se o lançamento selecionado for "Não Atribuido", então marcar somente os lançamentos cujo Node seja igual ao Node Selecionado.
            return false;
        }

        String nodeVerificar;
        String nodeComparar;
        if (nodeRateio.length() > nodeSelecionado.length()) {
            nodeVerificar = nodeRateio;
            nodeComparar = nodeSelecionado;
        } else {
            nodeVerificar = nodeSelecionado;
            nodeComparar = nodeRateio;

        }
        if (nodeVerificar.length() == 3) {
            return false;
        }

        while (nodeVerificar.length() != 3) {
            nodeVerificar = nodeVerificar.substring(0, nodeVerificar.length() - 4);
            if (nodeVerificar.equals(nodeComparar)) {
                result = true;
                break;
            }

        }

        return result;

    }
}

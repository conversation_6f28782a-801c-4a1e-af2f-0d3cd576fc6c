package relatorio.negocio.jdbc.financeiro;

import annotations.arquitetura.ExportFormatter;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.jfree.data.category.DefaultCategoryDataset;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;
import relatorio.negocio.jdbc.basico.IndicadorAcessoRel;
import servicos.bi.exportador.formatadores.FormatadorEnum;

import javax.faces.model.SelectItem;
import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.TreeMap;

public class TotalizadorFrequenciaRel extends SuperRelatorio {

    public static final Integer TICKET_TOTALIZADOR = 1;
    public static final Integer TICKET_TEMPO_PERMANENCIA = 2;
    private final String SIGNPERCENT = " % ";
    protected Date dataInicio;
    protected Date dataTermino;
    protected Integer frequencia;
    protected Integer quantidade;
    protected Integer quantidadeAtivos;
    protected String diaDaSemana;
    @ExportFormatter(formato = FormatadorEnum.PARTE_INTEIRA)
    protected Double hora;
    protected String porcentagem;
    private String agrupamento = "NENHUM";
    private String porcentagemRelacionada;
    private String generoSelecionado;
    private int idadeInicio = 0;
    private int idadeFim = 0;
    private String porcentagemAtivos;
    private DefaultCategoryDataset dataSetBarra;
    private int totalizador;
    private Boolean somenteAcessoDia = false;
    private EmpresaVO empresaVO;
    private String nome;
    private String nomeEmpresa;
    private String matricula;
    private String plano;
    private String modalidade;


    public TotalizadorFrequenciaRel() throws Exception {
        try {
            inicializarDados();
            inicializar();
        } catch (Exception e) {
            throw e;
        }
    }

    public void inicializarDados() {
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setFrequencia(1);
        setQuantidade(0);
        setQuantidadeAtivos(0);
        setDiaDaSemana("");
        setHora((double) 0);
        setDataSetBarra(new DefaultCategoryDataset());
        setTotalizador(0);
        setNomeEmpresa("");
    }

    public void validarDados() throws Exception {
        if (getDataInicio() == null) {
            throw new Exception("O campo DATA INÍCIO deve ser informado.");
        }
        if (getDataTermino() == null) {
            throw new Exception("O campo DATA TÉRMINO deve ser informado.");
        }
        if (Uteis.getQuantidadeMesesEntreDatas(getDataInicio(), getDataTermino()) > 6) {
            throw new Exception("O o período não pode ser superior a 6 meses.");
        }
    }

    private List gerarRelatorio(List lista) throws Exception {
        totalizador = 0;
        double total = 0;
        for (Object aLista : lista) {
            TotalizadorFrequenciaRel obj = new TotalizadorFrequenciaRel();
            obj = (TotalizadorFrequenciaRel) aLista;
            total += (double) obj.getQuantidade();
            //Setando o total de quantidades de acessos para o relatorio
            totalizador += obj.getQuantidade();
        }
        for (Object aLista : lista) {
            TotalizadorFrequenciaRel obj;
            obj = (TotalizadorFrequenciaRel) aLista;
            double atual = (double) obj.getQuantidade();
            String porcentagem = String.valueOf(atual * 100.0 / total);
            String porcentagemAtivos = String.valueOf(obj.getQuantidadeAtivos() * 100.0 / total);
            BigDecimal big = new BigDecimal(porcentagem).setScale(2, BigDecimal.ROUND_UP);
            porcentagem = Formatador.formatarValorMonetarioSemMoeda(big.doubleValue());
            BigDecimal bigAtivo = new BigDecimal(porcentagemAtivos).setScale(2, BigDecimal.ROUND_UP);
            porcentagemAtivos = Formatador.formatarValorMonetarioSemMoeda(bigAtivo.doubleValue());

            obj.setPorcentagem(porcentagem + this.SIGNPERCENT);
            obj.setPorcentagemAtivos(porcentagemAtivos + this.SIGNPERCENT);
        }
        if (agrupamento.equals("SEXTA_SABADO")) {
            double maiorValor = 0.0;
            for (Object aLista : lista) {
                TotalizadorFrequenciaRel obj = (TotalizadorFrequenciaRel) aLista;
                double atual = (double) obj.getQuantidade();
                if (atual > maiorValor) {
                    maiorValor = atual;
                }
            }

            for (Object aLista : lista) {
                TotalizadorFrequenciaRel obj = (TotalizadorFrequenciaRel) aLista;
                double atual = (double) obj.getQuantidade();
                String porcentagem = String.valueOf(atual * 100.0 / maiorValor);
                BigDecimal big = new BigDecimal(porcentagem).setScale(2, BigDecimal.ROUND_UP);
                porcentagem = Formatador.formatarValorMonetarioSemMoeda(big.doubleValue());
                obj.setPorcentagemRelacionada(porcentagem + this.SIGNPERCENT);
            }
        }
        return lista;

    }

    private String sqlBase(boolean comColunas, Integer empresa, Integer cliente, Integer plano, Integer modalidade, Integer colaborador) throws Exception {
        StringBuilder sql = new StringBuilder();

        if (comColunas) {
            sql.append("SELECT  \n");
            sql.append("*  \n");
        }
        sql.append(" FROM ( \n");
        sql.append(" SELECT  \n");
        sql.append(" ai.dthrentrada as entrada, \n");
        sql.append(" ai.codigo as codAcesso,  \n");
        sql.append(" ci.situacao as situacao, \n");
        sql.append(" pc.codigo as pessoa, \n");
        sql.append(" pc.nome as nome, \n");
        sql.append(" ci.matricula as matricula, \n");
        sql.append(" pc.sexo as sexo, \n");
        sql.append(" EXTRACT ('YEAR' FROM (age(now(), pc.datanasc))) as idade, \n");
        sql.append(" con.plano, \n");
        sql.append(" pl.descricao as nomePlano, \n");
        sql.append(" modalidade.codigo as modalidade, \n");
        sql.append(" modalidade.nome as nomeModalidade, \n");
        sql.append(" ci.codigo as cliente, \n");
        sql.append(" null as colaborador, \n");
        sql.append(" emp.codigo as empresa, \n");
        sql.append(" emp.nome as nomeEmpresa \n");
        sql.append(" FROM acessocliente ai \n");
        sql.append(" INNER JOIN cliente ci ON ai.cliente = ci.codigo  \n");
        sql.append(" INNER JOIN pessoa pc ON ci.pessoa = pc.codigo  \n");
        sql.append(" LEFT JOIN situacaoclientesinteticodw  sw ON sw.codigocliente = ci.codigo \n");
        sql.append(" LEFT JOIN contrato con ON sw.codigocontrato = con.codigo \n");
        sql.append(" left join contratomodalidade conm on sw.codigocontrato = conm.contrato \n");
        sql.append(" left join modalidade on modalidade.codigo = conm.modalidade \n");
        sql.append(" LEFT JOIN plano pl ON pl.codigo = con.plano \n");
        sql.append(" inner join empresa emp on emp.codigo = ci.empresa \n");
        sql.append(" UNION  \n");
        sql.append(" SELECT  \n");
        sql.append(" ac.dthrentrada as entrada, \n");
        sql.append(" ac.codigo as codAcesso,  \n");
        sql.append(" '' as situacao, \n");
        sql.append(" po.codigo as pessoa, \n");
        sql.append(" po.nome as nome, \n");
        sql.append(" 'COLABORADOR' as matricula, \n");
        sql.append(" po.sexo as sexo, \n");
        sql.append(" EXTRACT ('YEAR' FROM (age(now(), po.datanasc))) as idade, \n");
        sql.append(" null as plano, \n");
        sql.append(" '' as nomePlano, \n");
        sql.append(" null as modalidade, \n");
        sql.append(" '' as nomeModalidade, \n");
        sql.append(" null as cliente, \n");
        sql.append(" co.codigo as colaborador, \n");
        sql.append(" emp.codigo as empresa, \n");
        sql.append(" emp.nome as nomeEmpresa \n");
        sql.append(" FROM acessocolaborador ac \n");
        sql.append(" INNER JOIN colaborador co ON ac.colaborador = co.codigo  \n");
        sql.append(" INNER JOIN pessoa po ON co.pessoa = po.codigo  \n");
        sql.append(" inner join empresa emp on emp.codigo = co.empresa  \n");
        sql.append(") as sql \n");
        sql.append("WHERE sql.entrada >= '").append(Uteis.getDataJDBC(getDataInicio())).append(" 00:00:00'  \n");
        sql.append("AND sql.entrada <= '").append(Uteis.getDataJDBC(getDataTermino())).append(" 23:59:59' \n");

        if (!UteisValidacao.emptyNumber(getIdadeInicio())) {
            sql.append("AND sql.idade >= ").append(getIdadeInicio()).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(getIdadeFim())) {
            sql.append("AND sql.idade <= ").append(getIdadeFim()).append(" \n");
        }

        if (!UteisValidacao.emptyString(getGeneroSelecionado())) {
            sql.append("AND sql.sexo = '").append(getGeneroSelecionado()).append("' \n");
        }

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND sql.empresa = ").append(empresa).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(colaborador)) {
            sql.append("AND sql.colaborador = ").append(colaborador).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append("AND sql.cliente = ").append(cliente).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(plano)) {
            sql.append("AND sql.plano = ").append(plano).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(modalidade)) {
            sql.append("AND sql.modalidade = ").append(modalidade).append(" \n");
        }
        return sql.toString();

    }

    public List<TotalizadorFrequenciaRel> consultaParametrizada(Integer empresa, Integer cliente, Integer plano, Integer modalidade, Integer colaborador, boolean agruparPorPessoa) throws Exception {
        List listaDeObjetos = new ArrayList();
        inicializar();
        StringBuilder sql = new StringBuilder();

        String sqlSemColuna = sqlBase(false, empresa, cliente, plano, modalidade, colaborador);
        String sqlComColuna = sqlBase(true, empresa, cliente, plano, modalidade, colaborador);

        if (getFrequencia() == 1) { // geral

            sql.append(" SELECT \n");

            if (somenteAcessoDia) {
                sql.append("sql.pessoa as pessoa, \n");
            }

            if (agruparPorPessoa) {
                sql.append(" sql.matricula, \n");
                sql.append(" sql.nome, \n");
                sql.append(" sql.nomePlano, \n");
                sql.append(" sql.nomeModalidade, \n");
            } else {
                sql.append(" CAST(sql.entrada AS DATE) as data, \n");
            }

            sql.append(" COUNT( distinct(sql.codAcesso)) as quantidade, \n");
            sql.append(" sql.nomeEmpresa as nomeEmpresa, \n");
            sql.append(" COUNT(CASE WHEN sql.situacao = 'AT' THEN 1 ELSE 0 END) as quantidadeAtivos \n");

            sql.append(sqlSemColuna);

            if (somenteAcessoDia) {
                sql.append(" GROUP BY 1,2,nomeEmpresa \n");
                sql.append(" ORDER BY data ");
            } else if (agruparPorPessoa) {
                sql.append(" GROUP BY matricula,nome,nomeplano,nomeModalidade,nomeEmpresa \n");
                sql.append(" ORDER BY nome ");
            } else {
                sql.append(" GROUP BY data,nomeEmpresa \n");
                sql.append(" ORDER BY data ");
            }
            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();

            System.out.println("--SQL GERAL: \n\n" + sql.toString());

            if (!resultadoConsulta.next()) {
                throw new ConsistirException("Nenhum Registro Encontrado!");
            }

            resultadoConsulta.beforeFirst();
            int quantidadeLocal = 0;
            int ativosLocal = 0;
            Date dataAnterior = null;
            String nomeEmpresa = "";

            while (resultadoConsulta.next()) {

                if (agruparPorPessoa) {

                    String nome = resultadoConsulta.getString("nome");
                    String matricula = resultadoConsulta.getString("matricula");
                    String nomeplano = resultadoConsulta.getString("nomeplano");
                    String nomemodalidade = resultadoConsulta.getString("nomemodalidade");
                    int atual = resultadoConsulta.getInt("quantidade");
                    int ativos = resultadoConsulta.getInt("quantidadeAtivos");
                    nomeEmpresa = resultadoConsulta.getString("nomeEmpresa");


                    TotalizadorFrequenciaRel obj = new TotalizadorFrequenciaRel();
                    obj.setQuantidade(atual);
                    obj.setNome(nome);
                    obj.setMatricula(matricula);
                    obj.setPlano(nomeplano);
                    obj.setModalidade(nomemodalidade);
                    obj.setNome(nome);
                    obj.setQuantidadeAtivos(ativos);
                    obj.setNomeEmpresa(nomeEmpresa);
                    listaDeObjetos.add(obj);


                } else {

                    //preenche o objeto (data e quantidade)
                    Date dataAtual = resultadoConsulta.getDate("data");
                    int quantidadeAtual = resultadoConsulta.getInt("quantidade");
                    int quantidadeAtivos = resultadoConsulta.getInt("quantidadeAtivos");
                    nomeEmpresa = resultadoConsulta.getString("nomeEmpresa");

                    if (dataAnterior == null
                            || dataAtual.compareTo(dataAnterior) == 0) {
                        //se a data for igual a dataDeComparacao ou a data do objeto anterior  for nula
                        // adicionar a quantidade
                        quantidadeLocal += (somenteAcessoDia ? 1 : quantidadeAtual);
                        ativosLocal += (somenteAcessoDia && quantidadeAtivos >= 1) ? 1 : quantidadeAtivos;
                        // atualizar a data do objAnterior
                        dataAnterior = dataAtual;

                    } else {
                        adicionarLista(quantidadeLocal, ativosLocal, dataAnterior, listaDeObjetos, nomeEmpresa);

                        //se a data for diferente da do objeto anterior, e a data do objeto anterior nao for nula
                        dataAnterior = dataAtual;
                        quantidadeLocal = somenteAcessoDia ? 1 : quantidadeAtual;
                        ativosLocal += (somenteAcessoDia && quantidadeAtivos >= 1) ? 1 : quantidadeAtivos;

                    }

                }
            }
            adicionarLista(quantidadeLocal, ativosLocal, dataAnterior, listaDeObjetos, nomeEmpresa);


        }
        if (getFrequencia() == 2) { // geral por dia da semana

            sql.append(" SELECT \n");

            if (somenteAcessoDia) {
                sql.append("sql.pessoa as pessoa, \n");
            }

            sql.append("CAST(sql.entrada AS DATE) as data, \n");
            sql.append("COUNT(DISTINCT(sql.codAcesso)) as quantidade \n");

            sql.append(sqlSemColuna);

            if (somenteAcessoDia) {
                sql.append(" GROUP BY 1,2 ");
            } else {
                sql.append(" GROUP BY data ");
            }
            sql.append(" ORDER BY data");

            System.out.println("--SQL GERAL POR DIA DA SEMANA: \n\n" + sql.toString());

            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();
            resultadoConsulta.last();
            if (resultadoConsulta.getRow() == 0) {
                throw new ConsistirException("Nenhum Registro Encontrado !");
            }

            resultadoConsulta.beforeFirst();
            //cria um vetor pra armazenar a quantidade de frequencias por dia da semana
            int[] quantidades = new int[7];
            //instancia uma data de entrada do tipo Calendar
            Calendar dtEntrada = Calendario.getInstance();
            while (resultadoConsulta.next()) {
                //obtemos a data atual e quantidade de frequencias da consulta do banco
                Date dataAtual = resultadoConsulta.getDate("data");
                int quantidadeAtual = resultadoConsulta.getInt("quantidade");
                //setamos o valor da variavel do tipo dtEntrada para a data atual
                dtEntrada.setTime(dataAtual);
                //criamos a variavel que armazena o inteiro correspondente ao valor do dia da semana
                int diaDaSemanaAtual = dtEntrada.get(Calendar.DAY_OF_WEEK);
                //setamos o valor da quantidade de acordo com o dia da semana
                quantidades[diaDaSemanaAtual - 1] += (somenteAcessoDia ? 1 : quantidadeAtual);

            }
            //condicionais que verificam se existem frequências por dia da semana
            //e seta o valor do dia da semana e da frequência do objeto do tipo TotalizadorFrequenciaRel
            if (quantidades[0] > 0) {
                TotalizadorFrequenciaRel objDomingo = new TotalizadorFrequenciaRel();
                objDomingo.setDiaDaSemana("Domingo");
                objDomingo.setQuantidade(quantidades[0]);
                listaDeObjetos.add(objDomingo);
            }
            if (quantidades[1] > 0) {
                TotalizadorFrequenciaRel objSegunda = new TotalizadorFrequenciaRel();
                objSegunda.setDiaDaSemana("Segunda-Feira");
                objSegunda.setQuantidade(quantidades[1]);
                listaDeObjetos.add(objSegunda);
            }
            if (quantidades[2] > 0) {
                TotalizadorFrequenciaRel objTerca = new TotalizadorFrequenciaRel();
                objTerca.setDiaDaSemana("Terça-Feira");
                objTerca.setQuantidade(quantidades[2]);
                listaDeObjetos.add(objTerca);
            }
            if (quantidades[3] > 0) {
                TotalizadorFrequenciaRel objQuarta = new TotalizadorFrequenciaRel();
                objQuarta.setDiaDaSemana("Quarta-Feira");
                objQuarta.setQuantidade(quantidades[3]);
                listaDeObjetos.add(objQuarta);
            }
            if (quantidades[4] > 0) {
                TotalizadorFrequenciaRel objQuinta = new TotalizadorFrequenciaRel();
                objQuinta.setDiaDaSemana("Quinta-Feira");
                objQuinta.setQuantidade(quantidades[4]);
                listaDeObjetos.add(objQuinta);
            }
            if (getAgrupamento().equals("NENHUM")) {
                if (quantidades[5] > 0) {
                    TotalizadorFrequenciaRel objSexta = new TotalizadorFrequenciaRel();
                    objSexta.setDiaDaSemana("Sexta-Feira");
                    objSexta.setQuantidade(quantidades[5]);
                    listaDeObjetos.add(objSexta);
                }
                if (quantidades[6] > 0) {
                    TotalizadorFrequenciaRel objSabado = new TotalizadorFrequenciaRel();
                    objSabado.setDiaDaSemana("Sábado");
                    objSabado.setQuantidade(quantidades[6]);
                    listaDeObjetos.add(objSabado);
                }
            } else {
                if ((quantidades[5] > 0) || (quantidades[6] > 0)) {
                    TotalizadorFrequenciaRel objSexta = new TotalizadorFrequenciaRel();
                    objSexta.setDiaDaSemana("Sexta-Feira e Sábado");
                    objSexta.setQuantidade(quantidades[5] + quantidades[6]);
                    listaDeObjetos.add(objSexta);
                }
            }

        }
        if (getFrequencia() == 3) { // por horário

            sql.append(" SELECT ");
            if (somenteAcessoDia) {
                sql.append("sql.pessoa as pessoa,");
                sql.append("sql.entrada as data, ");
            } else {
                sql.append(" EXTRACT( hour FROM sql.entrada) as data, ");
            }
            sql.append(" COUNT(DISTINCT(sql.codAcesso)) as quantidade, ");
            sql.append(" COUNT(CASE WHEN sql.situacao = 'AT' THEN 1 ELSE null END) as quantidadeAtivos ");

            sql.append(sqlBase(false, empresa, cliente, plano, modalidade, colaborador));

            if (somenteAcessoDia) {
                sql.append(" GROUP BY 1,2 ");
            } else {
                sql.append(" GROUP BY data ");
            }
            sql.append(" ORDER BY data");

            System.out.println("--SQL POR HORÁRIO: \n\n" + sql.toString());


            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();
            resultadoConsulta.last();
            if (resultadoConsulta.getRow() == 0) {
                throw new ConsistirException("Nenhum Registro Encontrado !");
            }

            resultadoConsulta.beforeFirst();
            TreeMap<Double, HashMap<String, Integer>> mapaHora = new TreeMap<Double, HashMap<String, Integer>>();
            HashMap<String, Integer> mapaClienteDia = new HashMap<String, Integer>();
            while (resultadoConsulta.next()) {
                Double hora = 0.0;
                if (!somenteAcessoDia) {
                    hora = resultadoConsulta.getDouble("data");
                    TotalizadorFrequenciaRel obj = new TotalizadorFrequenciaRel();
                    obj.setQuantidadeAtivos(new Integer(resultadoConsulta.getInt("quantidadeAtivos")));
                    obj.setQuantidade(resultadoConsulta.getInt("quantidade"));
                    obj.setHora(new Double(hora));
                    listaDeObjetos.add(obj);
                } else {
                    hora = new Double(Uteis.gethoraHH(resultadoConsulta.getTimestamp("data")));
                    Integer quantidadeAtivos = resultadoConsulta.getInt("quantidadeAtivos");
                    HashMap<String, Integer> mapaDiaCliente = mapaHora.get(hora);
                    String keyCliente = Uteis.getData(resultadoConsulta.getDate("data")) + "|" + resultadoConsulta.getInt("pessoa");
                    if (mapaClienteDia.get(keyCliente) != null) {
                        continue;
                    }
                    if (mapaDiaCliente == null) {
                        mapaDiaCliente = new HashMap<String, Integer>();
                        mapaDiaCliente.put(keyCliente, quantidadeAtivos);
                        mapaHora.put(hora, mapaDiaCliente);
                    } else if (mapaDiaCliente.get(keyCliente) == null) {
                        mapaDiaCliente.put(keyCliente, quantidadeAtivos);
                    }
                    mapaClienteDia.put(keyCliente, quantidadeAtivos);
                }
            }
            for (Double key : mapaHora.keySet()) {
                TotalizadorFrequenciaRel obj = new TotalizadorFrequenciaRel();
                obj.setQuantidade(mapaHora.get(key).size());
                obj.setHora(key);
                Integer ativos = 0;
                for (String k : mapaHora.get(key).keySet()) {
                    ativos += mapaHora.get(key).get(k);
                }
                obj.setQuantidadeAtivos(ativos);
                listaDeObjetos.add(obj);
            }
        }
        return gerarRelatorio(listaDeObjetos);
    }

    private void adicionarLista(int quantidadeLocal, int ativos, Date dataAnterior, List listaDeObjetos, String nomeEmpresas) throws Exception {
        // adicionar o objeto anterior
        TotalizadorFrequenciaRel objAnterior = new TotalizadorFrequenciaRel();
        objAnterior.setQuantidade(quantidadeLocal);
        objAnterior.setDataInicio(dataAnterior);
        objAnterior.setQuantidadeAtivos(ativos);
        objAnterior.setNomeEmpresa(nomeEmpresas);
        listaDeObjetos.add(objAnterior);
    }

    private void adicionarListaObjDiaSemana(int quantidadeLocal, Date dataAnterior, List diaDaSemanaAnterior, List listaDeObjetos) throws Exception {
        // adicionar o objeto anterior
        TotalizadorFrequenciaRel objAnterior = new TotalizadorFrequenciaRel();
        objAnterior.setQuantidade(quantidadeLocal);
        objAnterior.setDataInicio(dataAnterior);
        Iterator i = diaDaSemanaAnterior.iterator();
        while (i.hasNext()) {
            if (diaDaSemanaAnterior.size() > 0) {
                if (diaDaSemanaAnterior.contains(Calendar.SUNDAY)) {
                    objAnterior.setDiaDaSemana("Domingo");
                }
                if (diaDaSemanaAnterior.contains(Calendar.MONDAY)) {
                    objAnterior.setDiaDaSemana("Segunda-Feira");
                }
                if (diaDaSemanaAnterior.contains(Calendar.TUESDAY)) {
                    objAnterior.setDiaDaSemana("Terça-Feira");
                }
                if (diaDaSemanaAnterior.contains(Calendar.WEDNESDAY)) {
                    objAnterior.setDiaDaSemana("Quarta-Feira");
                }
                if (diaDaSemanaAnterior.contains(Calendar.THURSDAY)) {
                    objAnterior.setDiaDaSemana("Quinta-Feira");
                }
                if (diaDaSemanaAnterior.contains(Calendar.FRIDAY)) {
                    objAnterior.setDiaDaSemana("Sexta-Feira");
                }
                if (diaDaSemanaAnterior.contains(Calendar.SATURDAY)) {
                    objAnterior.setDiaDaSemana("Sábado");
                }

            }
            i.next();
        }

        listaDeObjetos.add(objAnterior);
    }

    public List<IndicadorAcessoRel> consultarAcessoDoDiaPorHoraData(Date dataInicial, Date dataFinal, Integer empresa, boolean retornarException) throws Exception {
        List<IndicadorAcessoRel> listaAcessoDiaHora = new ArrayList<IndicadorAcessoRel>();
        if (dataFinal == null) {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT ");
            sql.append(" EXTRACT( hour FROM dthrentrada) as data, ");
            sql.append(" COUNT( acessocliente.codigo) as quantidade ");
            sql.append(" FROM acessocliente ");
            sql.append(" LEFT JOIN cliente ");
            sql.append(" ON acessocliente.cliente = cliente.codigo ");
            sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ");
            sql.append(" WHERE dthrentrada >= ' " + Uteis.getDataJDBC(dataInicial) + " 00:00:00 ' ");
            sql.append(" AND dthrentrada <= ' " + Uteis.getDataJDBC(dataInicial) + " 23:59:59 ' ");
            sql.append(" AND cliente.empresa = " + empresa + " ");
            sql.append(" GROUP BY data ");
            sql.append(" ORDER BY data");

            Statement stm = con.createStatement();
            ResultSet resultadoConsulta = stm.executeQuery(sql.toString());

            while (resultadoConsulta.next()) {
                IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();
                acessoDiaHora.setHora(new Double(resultadoConsulta.getDouble("data")));
                acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
                listaAcessoDiaHora.add(acessoDiaHora);
            }
            if (retornarException) {
                if (listaAcessoDiaHora.isEmpty()) {
                    throw new Exception("Nenhum Registro Encontrado!");
                }
            }

        } else {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT ");
            sql.append(" CAST(dthrentrada AS DATE) as data, ");
            sql.append(" COUNT( acessocliente.codigo) as quantidade ");
            sql.append(" FROM acessocliente ");
            sql.append(" LEFT JOIN cliente ");
            sql.append(" ON acessocliente.cliente = cliente.codigo ");
            sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ");
            sql.append(" WHERE dthrentrada >= ' " + Uteis.getDataJDBC(dataInicial) + " 00:00:00 ' ");
            sql.append(" AND dthrentrada <= ' " + Uteis.getDataJDBC(dataFinal) + " 23:59:59 ' ");
            sql.append(" AND cliente.empresa = " + empresa + " ");
            sql.append(" GROUP BY data ");
            sql.append(" ORDER BY data");

            Statement stm = con.createStatement();
            ResultSet resultadoConsulta = stm.executeQuery(sql.toString());


            while (resultadoConsulta.next()) {
                IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();
                acessoDiaHora.setData(resultadoConsulta.getDate("data"));
                acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
                listaAcessoDiaHora.add(acessoDiaHora);
            }

            if (retornarException) {
                if (listaAcessoDiaHora.isEmpty()) {
                    throw new Exception("Nenhum Registro Encontrado!");
                }
            }
        }

        return listaAcessoDiaHora;
    }

    public List<IndicadorAcessoRel> consultarAcessoDoDiaPorMes(Integer mes, Integer ano, Integer empresa, boolean retornarException) throws Exception {
        List<IndicadorAcessoRel> listaAcessoDiaHora = new ArrayList<IndicadorAcessoRel>();
        StringBuilder sql = new StringBuilder();

        if (mes != null) {
            sql.append(" SELECT ").append("\n");
            sql.append(" EXTRACT(MONTH FROM dthrentrada) as mes, ").append("\n");
            sql.append(" EXTRACT(year FROM dthrentrada) as ano, ").append("\n");
            sql.append(" COUNT( acessocliente.codigo) as quantidade ").append("\n");
            sql.append(" FROM acessocliente ").append("\n");
            sql.append(" LEFT JOIN cliente ").append("\n");
            sql.append(" ON acessocliente.cliente = cliente.codigo ").append("\n");
            sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ").append("\n");
            sql.append(" WHERE EXTRACT(MONTH FROM dthrentrada) = '" + mes + "'").append("\n");
            sql.append(" AND EXTRACT(year FROM dthrentrada) = '" + ano + "'").append("\n");
            sql.append(" AND cliente.empresa = " + empresa + " ").append("\n");
            sql.append(" GROUP BY mes,ano ").append("\n");
            sql.append(" ORDER BY mes,ano ");

            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();
            resultadoConsulta.last();
            resultadoConsulta.beforeFirst();

            while (resultadoConsulta.next()) {
                IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();
                acessoDiaHora.setMes(resultadoConsulta.getInt("mes"));
                acessoDiaHora.setAno(resultadoConsulta.getInt("ano"));
                acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
                listaAcessoDiaHora.add(acessoDiaHora);
            }

            if (retornarException) {
                if (listaAcessoDiaHora.isEmpty()) {
                    throw new Exception("Nenhum Registro Encontrado!");
                }
            }
        } else {
            sql.append(" SELECT ").append("\n");
            sql.append(" EXTRACT(MONTH FROM dthrentrada) as mes, ").append("\n");
            sql.append(" EXTRACT(year FROM dthrentrada) as ano, ").append("\n");
            sql.append(" COUNT( acessocliente.codigo) as quantidade ").append("\n");
            sql.append(" FROM acessocliente ").append("\n");
            sql.append(" LEFT JOIN cliente ").append("\n");
            sql.append(" ON acessocliente.cliente = cliente.codigo ").append("\n");
            sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ").append("\n");
            sql.append(" WHERE 1=1 ").append("\n");
            sql.append(" AND EXTRACT(year FROM dthrentrada) = '" + ano + "'").append("\n");
            sql.append(" AND cliente.empresa = " + empresa + " ");
            sql.append(" GROUP BY mes,ano ").append("\n");
            sql.append(" ORDER BY mes,ano ");

            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();
            resultadoConsulta.last();
            resultadoConsulta.beforeFirst();

            while (resultadoConsulta.next()) {
                IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();
                acessoDiaHora.setMes(resultadoConsulta.getInt("mes"));
                acessoDiaHora.setAno(resultadoConsulta.getInt("ano"));
                acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
                listaAcessoDiaHora.add(acessoDiaHora);
            }

            if (retornarException) {
                if (listaAcessoDiaHora.isEmpty()) {
                    throw new Exception("Nenhum Registro Encontrado!");
                }
            }
        }
        return listaAcessoDiaHora;
    }

    public List<IndicadorAcessoRel> consultarAcessoDoDiaPorData(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        List<IndicadorAcessoRel> listaAcessoDiaHora = new ArrayList<IndicadorAcessoRel>();
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT ").append("\n");
        sql.append(" EXTRACT(MONTH FROM dthrentrada) as mes, ").append("\n");
        sql.append(" EXTRACT(year FROM dthrentrada) as ano, ").append("\n");
        sql.append(" COUNT( acessocliente.codigo) as quantidade ").append("\n");
        sql.append(" FROM acessocliente ").append("\n");
        sql.append(" LEFT JOIN cliente ").append("\n");
        sql.append(" ON acessocliente.cliente = cliente.codigo ").append("\n");
        sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ").append("\n");
        sql.append(" WHERE (dthrentrada BETWEEN '").append(Uteis.getDataHoraJDBC(dataInicial, "00:00:00")).append("' and '").append(Uteis.getDataHoraJDBC(dataFinal, "23:59:59")).append("') ").append("\n");
        sql.append(" AND cliente.empresa = ").append(empresa).append(" ").append("\n");
        sql.append(" GROUP BY mes,ano ").append("\n");
        sql.append(" ORDER BY mes,ano ");

        PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultadoConsulta = sqlPS.executeQuery();

        while (resultadoConsulta.next()) {
            IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();
            acessoDiaHora.setMes(resultadoConsulta.getInt("mes"));
            acessoDiaHora.setAno(resultadoConsulta.getInt("ano"));
            acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
            listaAcessoDiaHora.add(acessoDiaHora);
        }

        return listaAcessoDiaHora;
    }

    public IndicadorAcessoRel consultarDiaComMaiorAcesso(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" CAST(dthrentrada AS DATE) as data, ");
        sql.append(" COUNT( acessocliente.codigo) as quantidade ");
        sql.append(" FROM acessocliente ");
        sql.append(" LEFT JOIN cliente ");
        sql.append(" ON acessocliente.cliente = cliente.codigo ");
        sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ");
        sql.append(" WHERE dthrentrada >= ' " + Uteis.getDataJDBC(dataInicial) + " 00:00:00 ' ");
        sql.append(" AND dthrentrada <= ' " + Uteis.getDataJDBC(dataFinal) + " 23:59:59 ' ");
        sql.append(" AND cliente.empresa = " + empresa + " ");
        sql.append(" GROUP BY data ");
        sql.append(" ORDER BY quantidade DESC LIMIT 1");

        PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultadoConsulta = sqlPS.executeQuery();
        resultadoConsulta.last();
        resultadoConsulta.beforeFirst();

        while (resultadoConsulta.next()) {
            acessoDiaHora.setData(resultadoConsulta.getDate("data"));
            acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
        }
        return acessoDiaHora;
    }

    public IndicadorAcessoRel consultarHoraComMaiorAcesso(Date dataInicial, Date dataFinal, Integer empresa) throws Exception {
        IndicadorAcessoRel acessoDiaHora = new IndicadorAcessoRel();

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT ");
        sql.append(" EXTRACT( hour FROM dthrentrada) as hora, ");
        sql.append(" COUNT( acessocliente.codigo) as quantidade ");
        sql.append(" FROM acessocliente ");
        sql.append(" LEFT JOIN cliente ");
        sql.append(" ON acessocliente.cliente = cliente.codigo ");
        sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ");
        sql.append(" WHERE dthrentrada >= ' " + Uteis.getDataJDBC(dataInicial) + " 00:00:00 ' ");
        sql.append(" AND dthrentrada <= ' " + Uteis.getDataJDBC(dataFinal) + " 23:59:59 ' ");
        sql.append(" AND cliente.empresa = " + empresa + " ");
        sql.append(" GROUP BY hora ");
        sql.append(" ORDER BY quantidade DESC LIMIT 1");

        PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultadoConsulta = sqlPS.executeQuery();
        resultadoConsulta.last();
        resultadoConsulta.beforeFirst();

        while (resultadoConsulta.next()) {
            acessoDiaHora.setHora(new Double(resultadoConsulta.getDouble("hora")));
            acessoDiaHora.setQuantidade(resultadoConsulta.getInt("quantidade"));
        }
        return acessoDiaHora;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public Integer getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(Integer frequencia) {
        this.frequencia = frequencia;
    }

    public String getAgrupamento() {
        return agrupamento;
    }

    public void setAgrupamento(String agrupamento) {
        this.agrupamento = agrupamento;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Integer getQuantidadeAtivos() {
        return quantidadeAtivos;
    }

    public void setQuantidadeAtivos(Integer quantidadeAtivos) {
        this.quantidadeAtivos = quantidadeAtivos;
    }

    public String getDiaDaSemana() {
        return diaDaSemana;
    }

    public void setDiaDaSemana(String diaDaSemana) {
        this.diaDaSemana = diaDaSemana;
    }

    public Double getHora() {
        return hora;
    }

    public void setHora(Double hora) {
        this.hora = hora;
    }

    public String getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(String porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagemRelacionada() {
        return porcentagemRelacionada;
    }

    public void setPorcentagemRelacionada(String porcentagemRelacionada) {
        this.porcentagemRelacionada = porcentagemRelacionada;
    }

    public DefaultCategoryDataset getDataSetBarra() {
        return dataSetBarra;
    }

    public void setDataSetBarra(DefaultCategoryDataset dataSetBarra) {
        this.dataSetBarra = dataSetBarra;
    }

    public List<SelectItem> getListaGeneros() {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(null, "Todos"));
        lista.add(new SelectItem("F", "Feminino"));
        lista.add(new SelectItem("M", "Masculino"));
        return lista;
    }

    public String getGeneroSelecionado() {
        return generoSelecionado;
    }

    public void setGeneroSelecionado(String generoSelecionado) {
        this.generoSelecionado = generoSelecionado;
    }

    public int getIdadeInicio() {
        return idadeInicio;
    }

    public void setIdadeInicio(int idadeInicio) {
        this.idadeInicio = idadeInicio;
    }

    public int getIdadeFim() {
        return idadeFim;
    }

    public void setIdadeFim(int idadeFim) {
        this.idadeFim = idadeFim;
    }

    /**
     * @return the totalizador
     */
    public int getTotalizador() {
        return totalizador;
    }

    /**
     * @param totalizador the totalizador to set
     */
    public void setTotalizador(int totalizador) {
        this.totalizador = totalizador;
    }

    public String getPorcentagemAtivos() {
        return porcentagemAtivos;
    }

    public void setPorcentagemAtivos(String porcentagemAtivos) {
        this.porcentagemAtivos = porcentagemAtivos;
    }

    public Boolean getSomenteAcessoDia() {
        return somenteAcessoDia;
    }

    public void setSomenteAcessoDia(Boolean somenteAcessoDia) {
        this.somenteAcessoDia = somenteAcessoDia;
    }

    public List<TotalizadorFrequenciaRel> consultaTickets(Integer empresa) throws Exception {
        List<TotalizadorFrequenciaRel> listaDeObjetos = new ArrayList<TotalizadorFrequenciaRel>();
        inicializar();
        StringBuilder sql = new StringBuilder();
        if (getFrequencia().equals(TICKET_TOTALIZADOR)) {
            sql.append("SELECT\n");
            if (somenteAcessoDia) {
                sql.append("pessoa.codigo as pessoa,\n");
            }
            sql.append(" CAST(dthrentrada AS DATE) as data,\n");
            sql.append(" COUNT( acessocliente.codigo) as quantidade,\n");
            sql.append(" COUNT(CASE WHEN cliente.situacao like 'AT' THEN 1 ELSE null END) as quantidadeAtivos\n");
            sql.append("FROM acessocliente\n");
            sql.append(" LEFT JOIN cliente ON acessocliente.cliente = cliente.codigo\n");
            sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo\n");
            sql.append("WHERE dthrentrada >= '").append(Uteis.getDataHoraJDBC(getDataInicio(), "00:00:00")).append("'\n");
            sql.append(" AND dthrentrada <= '").append(Uteis.getDataHoraJDBC(getDataTermino(), "23:59:59")).append("'\n");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append(" AND cliente.empresa = ").append(empresa).append("\n");
            }
            sql.append(" AND length(ticket) > 0\n");
            if (somenteAcessoDia) {
                sql.append("GROUP BY 1,2\n");
            } else {
                sql.append("GROUP BY data\n");
            }
            sql.append("ORDER BY data\n");
            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();

            if (!resultadoConsulta.next()) {
                throw new ConsistirException("Nenhum Registro Encontrado!");
            }

            resultadoConsulta.beforeFirst();
            int quantidadeLocal = 0;
            int ativosLocal = 0;
            Date dataAnterior = null;

            while (resultadoConsulta.next()) {
                //preenche o objeto (data e quantidade)
                Date dataAtual = resultadoConsulta.getDate("data");
                int quantidadeAtual = resultadoConsulta.getInt("quantidade");
                int quantidadeAtivos = resultadoConsulta.getInt("quantidadeAtivos");
                String nomeEmpresa = resultadoConsulta.getString("nomeEmpresa");

                if (dataAnterior == null
                        || dataAtual.compareTo(dataAnterior) == 0) {
                    //se a data for igual a dataDeComparacao ou a data do objeto anterior  for nula
                    // adicionar a quantidade
                    quantidadeLocal += (somenteAcessoDia ? 1 : quantidadeAtual);
                    ativosLocal += (somenteAcessoDia && quantidadeAtivos >= 1) ? 1 : quantidadeAtivos;
                    // atualizar a data do objAnterior
                    dataAnterior = dataAtual;

                } else {
                    adicionarLista(quantidadeLocal, ativosLocal, dataAnterior, listaDeObjetos, nomeEmpresa);

                    //se a data for diferente da do objeto anterior, e a data do objeto anterior nao for nula
                    dataAnterior = dataAtual;
                    quantidadeLocal = somenteAcessoDia ? 1 : quantidadeAtual;
                    ativosLocal += (somenteAcessoDia && quantidadeAtivos >= 1) ? 1 : quantidadeAtivos;

                }


            }
            adicionarLista(quantidadeLocal, ativosLocal, dataAnterior, listaDeObjetos, nomeEmpresa);
        } else if (getFrequencia().equals(TICKET_TEMPO_PERMANENCIA)) {
            sql.append("SELECT agrupador, count(codigo) as quantidade FROM (");
            sql.append("SELECT  \n");
            sql.append(" acessocliente.codigo,\n");
            sql.append(" CASE \n");
            sql.append(" WHEN (dthrsaida - dthrentrada)::interval <= '30 minutes' THEN 'Até 00:30'\n");
            sql.append(" WHEN (dthrsaida - dthrentrada)::interval <= '60 minutes' THEN 'Até 01:00'\n");
            sql.append(" WHEN (dthrsaida - dthrentrada)::interval <= '90 minutes' THEN 'Até 01:30'\n");
            sql.append(" WHEN (dthrsaida - dthrentrada)::interval <= '120 minutes' THEN 'Até 02:00'\n");
            sql.append(" ELSE 'Mais que 02:00' END as agrupador\n");
            sql.append("FROM acessocliente\n");
            sql.append(" LEFT JOIN cliente ON acessocliente.cliente = cliente.codigo\n");
            sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo\n");
            sql.append("WHERE dthrentrada >= '").append(Uteis.getDataHoraJDBC(getDataInicio(), "00:00:00")).append("'\n");
            sql.append(" AND dthrentrada <= '").append(Uteis.getDataHoraJDBC(getDataTermino(), "23:59:59")).append("'\n");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append(" AND cliente.empresa = ").append(empresa).append(" \n");
            }
            sql.append(" AND length(ticket) > 0\n");
            sql.append(") as ConsultaInicial\n");
            sql.append("GROUP BY agrupador\n");
            sql.append("Order by agrupador;");
            PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
            ResultSet resultadoConsulta = sqlPS.executeQuery();
            resultadoConsulta.last();
            if (resultadoConsulta.getRow() == 0) {
                throw new ConsistirException("Nenhum Registro Encontrado !");
            }

            resultadoConsulta.beforeFirst();
            //cria um vetor pra armazenar a quantidade de frequencias por dia da semana
            final int ATE_30 = 0;
            final int ATE_60 = 1;
            final int ATE_90 = 2;
            final int ATE_120 = 3;
            final int MAIOR_120 = 4;

            int[] quantidades = new int[5];
            //instancia uma data de entrada do tipo Calendar
            Calendar dtEntrada = Calendario.getInstance();
            while (resultadoConsulta.next()) {
                String agrupador = resultadoConsulta.getString("agrupador");
                int quantidadeAtual = resultadoConsulta.getInt("quantidade");

                //criamos a variavel que armazena o inteiro correspondente ao valor do dia da semana
                Integer posicao = null;
                if (agrupador.equals("Até 00:30")) {
                    posicao = ATE_30;
                } else if (agrupador.equals("Até 01:00")) {
                    posicao = ATE_60;
                } else if (agrupador.equals("Até 01:30")) {
                    posicao = ATE_90;
                } else if (agrupador.equals("Até 02:00")) {
                    posicao = ATE_120;
                } else {
                    posicao = MAIOR_120;
                }

                quantidades[posicao] += (somenteAcessoDia ? 1 : quantidadeAtual);
            }
            //condicionais que verificam se existem frequências por dia da semana
            //e seta o valor do dia da semana e da frequência do objeto do tipo TotalizadorFrequenciaRel
            if (quantidades[ATE_30] > 0) {
                TotalizadorFrequenciaRel objAte30 = new TotalizadorFrequenciaRel();
                objAte30.setDiaDaSemana("Até 00:30");
                objAte30.setQuantidade(quantidades[ATE_30]);
                listaDeObjetos.add(objAte30);
            }
            if (quantidades[ATE_60] > 0) {
                TotalizadorFrequenciaRel objAte60 = new TotalizadorFrequenciaRel();
                objAte60.setDiaDaSemana("Até 01:00");
                objAte60.setQuantidade(quantidades[ATE_60]);
                listaDeObjetos.add(objAte60);
            }
            if (quantidades[ATE_90] > 0) {
                TotalizadorFrequenciaRel objAte90 = new TotalizadorFrequenciaRel();
                objAte90.setDiaDaSemana("Até 01:30");
                objAte90.setQuantidade(quantidades[ATE_90]);
                listaDeObjetos.add(objAte90);
            }
            if (quantidades[ATE_120] > 0) {
                TotalizadorFrequenciaRel objAte120 = new TotalizadorFrequenciaRel();
                objAte120.setDiaDaSemana("Até 02:00");
                objAte120.setQuantidade(quantidades[ATE_120]);
                listaDeObjetos.add(objAte120);
            }
            if (quantidades[MAIOR_120] > 0) {
                TotalizadorFrequenciaRel maior120 = new TotalizadorFrequenciaRel();
                maior120.setDiaDaSemana("Mais que 02:00");
                maior120.setQuantidade(quantidades[MAIOR_120]);
                listaDeObjetos.add(maior120);
            }
        }

        gerarRelatorio(listaDeObjetos);
        return listaDeObjetos;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getMatricula() {
        if (matricula == null) {
            matricula = "";
        }
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getPlano() {
        if (plano == null) {
            plano = "";
        }
        return plano;
    }

    public void setPlano(String plano) {
        this.plano = plano;
    }

    public String getModalidade() {
        return modalidade;
    }

    public void setModalidade(String modalidade) {
        this.modalidade = modalidade;
    }
}

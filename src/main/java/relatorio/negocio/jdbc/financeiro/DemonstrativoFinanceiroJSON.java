package relatorio.negocio.jdbc.financeiro;


import java.io.Serializable;

/**
 * Created by <PERSON> on 25/11/2016.
 */
public class DemonstrativoFinanceiroJSON implements Serializable {
    private String tipoRelatorio;
    private Double pontoEquilibrio = 0.0;
    private Double realizadoPeriodo = 0.0;
    private Double naoRealizado = 0.0;
    private Double resultadoExercicio = 0.0;
    private Double resultadoExercicioPorc = 0.0;

    public DemonstrativoFinanceiroJSON() {
    }

    public DemonstrativoFinanceiroJSON(String tipoRelatorio, Double pontoEquilibrio, Double realizadoPeriodo, Double naoRealizado, Double resultadoExercicio, Double resultadoExercicioPorc) {
        this.tipoRelatorio = tipoRelatorio;
        this.pontoEquilibrio = pontoEquilibrio;
        this.realizadoPeriodo = realizadoPeriodo;
        this.naoRealizado = naoRealizado;
        this.resultadoExercicio = resultadoExercicio;
        this.resultadoExercicioPorc = resultadoExercicioPorc;
    }

    public String getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(String tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public Double getPontoEquilibrio() {
        return pontoEquilibrio;
    }

    public void setPontoEquilibrio(Double pontoEquilibrio) {
        this.pontoEquilibrio = pontoEquilibrio;
    }

    public Double getRealizadoPeriodo() {
        return realizadoPeriodo;
    }

    public void setRealizadoPeriodo(Double realizadoPeriodo) {
        this.realizadoPeriodo = realizadoPeriodo;
    }

    public Double getNaoRealizado() {
        return naoRealizado;
    }

    public void setNaoRealizado(Double naoRealizado) {
        this.naoRealizado = naoRealizado;
    }

    public Double getResultadoExercicio() {
        return resultadoExercicio;
    }

    public void setResultadoExercicio(Double resultadoExercicio) {
        this.resultadoExercicio = resultadoExercicio;
    }

    public Double getResultadoExercicioPorc() {
        return resultadoExercicioPorc;
    }

    public void setResultadoExercicioPorc(Double resultadoExercicioPorc) {
        this.resultadoExercicioPorc = resultadoExercicioPorc;
    }
}

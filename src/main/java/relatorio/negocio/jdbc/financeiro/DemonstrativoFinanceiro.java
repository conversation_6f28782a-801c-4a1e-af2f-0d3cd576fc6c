/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.financeiro.TotalizadorMesDF;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DemonstrativoFinanceiro implements Serializable {
	private boolean dre = false;
    private String codigoAgrupador;
    private String nomeAgrupador;
    private List<TotalizadorMesDF> listaTotalizadorMeses; // Esta lista servirá para armazenar todos os lançamentos de cada mês. Obs.: Esta lista não será enviada ao Browser.
    private List<MesProcessar> listaMeses; // Esta lista servirá para montar o treeView do Demonstrativo Financeiro. Obs.: Esta lista será enviada ao Browser.
    private List<CentroCustosDRE> listaCentros = new ArrayList<CentroCustosDRE>();
    private boolean houveNaoAtribuido = false;
    private double totalEntradaTodosOsMeses = 0;
    private double totalSaidaTodosOsMeses = 0;
    public double totalTodosMeses = 0;
    private TipoES tipoES;
    private DemonstrativoFinanceiro dfPai;
    private int numFilhos = 0;
    private TipoEquivalenciaDRE tipoEquivalencia;
    private Double percentual = 0.0;
    private Double metaPerc = 0.0;
    private boolean link = true;
    private int conta = 0;
    private int tipoconta = 0;
    private boolean dfConta = false;
    private Double saldoInicial = 0.0;
    private Double saldoFinal = 0.0;
    private String codigoNode;
    private Double valorPercGasto = 0.0;
    private Double percPretendido = 0.0;
    private Boolean investimento = false;
    private HashMap<String, Double> totalPorFormaPagamento;
    private String codigoPlanoContaTotalFormaPag;


    public String getPercPretendidoApresentar() {
        if (percPretendido > 0.0) {
            return " - " + Formatador.formatarValorPercentual(percPretendido / 100, 2);
        }
        return "";
    }

    public Double getPercPretendido() {
        return percPretendido;
    }

    public void setPercPretendido(Double percPretendido) {
        this.percPretendido = percPretendido;
    }

    public Double getValorPercGasto() {
        return valorPercGasto;
    }

    public void setValorPercGasto(Double valorPercGasto) {
        this.valorPercGasto = valorPercGasto;
    }

    public String getCorMeta() {
        if (tipoEquivalencia == null && this.totalTodosMeses >= 0) {
    		return "";
    	}

        if (tipoEquivalencia == null && this.totalTodosMeses < 0) {
            return "red";
        }

        boolean positivo = (tipoEquivalencia.equals(TipoEquivalenciaDRE.RECEITA_BRUTA)
    			|| tipoEquivalencia.equals(TipoEquivalenciaDRE.LUCRO_BRUTO)
                || tipoEquivalencia.equals(TipoEquivalenciaDRE.LUCRO_OPERACIONAL));
        boolean negativo = (tipoEquivalencia.equals(TipoEquivalenciaDRE.CUSTOS_ESPECIFICOS)
    						|| tipoEquivalencia.equals(TipoEquivalenciaDRE.DESPESAS_OPERACIONAIS));
        if (positivo && percentual >= metaPerc) {
    		return "blue";
    	}
        if (totalTodosMeses > 0 && negativo) {
            return "red";
        }
        if (positivo && percentual < metaPerc) {
    		return "red";
    	}
        if (negativo && percentual >= metaPerc) {
    		return "red";
    	}
        if (negativo && percentual < metaPerc) {
    		return "blue";
    	}

    	return "";
    }


    public DemonstrativoFinanceiro getClone() {
    	DemonstrativoFinanceiro clone = new DemonstrativoFinanceiro();
    	clone.setLink(link);
    	clone.setCodigoAgrupador(this.codigoAgrupador);
    	clone.setNomeAgrupador(nomeAgrupador);
    	clone.setListaTotalizadorMeses(listaTotalizadorMeses);
    	clone.setListaMeses(listaMeses);
    	clone.setHouveNaoAtribuido(houveNaoAtribuido);
    	clone.setTipoES(tipoES);
    	clone.setTotalTodosMeses(totalTodosMeses);
    	clone.setDfPai(dfPai);
    	clone.setNumFilhos(numFilhos);
    	clone.setTipoEquivalencia(tipoEquivalencia);
    	clone.setListaCentros(listaCentros);
    	clone.setDre(dre);
    	clone.setConta(this.conta);
    	clone.setTipoconta(this.tipoconta);
    	clone.setListaCentros(listaCentros);
    	return clone;
    }

    public String getValorAcumuladoApresentar() {
        if (UteisValidacao.emptyNumber(valorPercGasto)) {
            return "";
        }
        return Formatador.formatarValorPercentual(valorPercGasto, (valorPercGasto % 1 > 0 ? 2 : 0));
    }

    public String getCorPretendido() {
        return UteisValidacao.emptyNumber(percPretendido) ? "" :
                (valorPercGasto * 100) > percPretendido ? "red" : "green";
    }

    public DemonstrativoFinanceiro() {
      listaTotalizadorMeses = new ArrayList<TotalizadorMesDF>();
      listaMeses = new ArrayList<MesProcessar>();
    }

    public DemonstrativoFinanceiro(String codigoAgrupador) {
    	this.codigoAgrupador = codigoAgrupador;
    }

    public String getCodigoAgrupador() {
        return codigoAgrupador;
    }

    public void setCodigoAgrupador(String codigoAgrupador) {
        this.codigoAgrupador = codigoAgrupador;
    }

    public List<TotalizadorMesDF> getListaTotalizadorMeses() {
        return listaTotalizadorMeses;
    }

    public void setListaTotalizadorMeses(List<TotalizadorMesDF> listaTotalizadorMeses) {
        this.listaTotalizadorMeses = listaTotalizadorMeses;
    }

    public String getNomeAgrupador() {
        return nomeAgrupador;
    }

    public void setNomeAgrupador(String nomeAgrupador) {
        this.nomeAgrupador = nomeAgrupador;
    }


    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (!(obj instanceof DemonstrativoFinanceiro))
            return false;
        return (((DemonstrativoFinanceiro) obj).getCodigoAgrupador().equals(this.getCodigoAgrupador()));
    }

    public String getCodigoNode() {
        if (UteisValidacao.emptyString(codigoNode)) {
    		codigoNode = "";
    		String codigo = this.getCodigoAgrupador();
            codigo =  codigo.replace(".", "");
            for (int i = 0; i < codigo.length(); i++) {
                if (!codigo.substring(i, i + 1).equals("0")) {
                    codigoNode += codigo.substring(i, codigo.length());
                    break;
    	}
            }
        }
    	return codigoNode;

    }

    public List<MesProcessar> getListaMeses() {
        return listaMeses;
    }

    public void setListaMeses(List<MesProcessar> listaMeses) {
        this.listaMeses = listaMeses;
    }

    public boolean isHouveNaoAtribuido() {
        return houveNaoAtribuido;
    }

    public void setHouveNaoAtribuido(boolean houveNaoAtribuido) {
        this.houveNaoAtribuido = houveNaoAtribuido;
    }

    public synchronized double getTotalTodosMeses() {
        return totalTodosMeses;
    }

    public synchronized double getTotalTodosMesesPositivo() {
        return totalTodosMeses < 0 ? totalTodosMeses * (-1) : totalTodosMeses;
    }

    public synchronized void setTotalTodosMeses(double totalTodosMeses) {
        this.totalTodosMeses = totalTodosMeses;
    }

    public synchronized String getTotalTodosMesesApresentar() {
      String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalTodosMeses);
        return (totalTodosMeses < 0.0 ? "-" : "") + valor;

    }

    public synchronized String getTotalTodosMesesApresentarTela(){
        return getTotalTodosMesesApresentar().contains("-") ? getTotalTodosMesesApresentar().replace("-", "(-)") : getTotalTodosMesesApresentar();
    }

    public synchronized String getTotalEntradaTodosMesesApresentarTela(){
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalEntradaTodosOsMeses);
        return "+" + valor;
    }

    public synchronized String getTotalSaidaTodosMesesApresentarTela(){
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalSaidaTodosOsMeses);
        return "-" + valor;
    }


    public synchronized String getTotalTodosMesesApresentarDRE() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalTodosMeses);
        return (totalTodosMeses < 0.0 ? "-" : "") + valor;
      }

    public synchronized String getTotalTodosMesesApresentarDRETela() {
        return getTotalTodosMesesApresentarDRE().contains("-") ? getTotalTodosMesesApresentarDRE().replace("-", "(-)") :  getTotalTodosMesesApresentarDRE();
    }

    public synchronized String getSaldoInicialApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.saldoInicial);
        return valor;
      }

    public synchronized String getSaldoFinalApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.saldoFinal);
        return valor;
      }

    public String getCorLinkTotalTodosMeses() {
       if (this.totalTodosMeses >= 0)
           return "black";
       else
           return "red";
    }


    public TipoES getTipoES() {
        return tipoES;
    }

    public void setTipoES(TipoES tipoES) {
        this.tipoES = tipoES;
    }

    public int getNumFilhos() {
        return numFilhos;
    }

    public void setNumFilhos(int numFilhos) {
        this.numFilhos = numFilhos;
    }

    public DemonstrativoFinanceiro getDfPai() {
        return dfPai;
    }

    public void setDfPai(DemonstrativoFinanceiro dfPai) {
        this.dfPai = dfPai;
    }

	public void setTipoEquivalencia(TipoEquivalenciaDRE tipoEquivalencia) {
		this.tipoEquivalencia = tipoEquivalencia;
	}

	public TipoEquivalenciaDRE getTipoEquivalencia() {
		return tipoEquivalencia;
	}

	public void setListaCentros(List<CentroCustosDRE> listaCentros) {
		this.listaCentros = listaCentros;
	}

	public List<CentroCustosDRE> getListaCentros() {
		return listaCentros;
	}

	public void setPercentual(Double percentual) {
		this.percentual = percentual;
	}

	public Double getPercentual() {
		return percentual;
	}

	public String getPercentualApresentar() {
		return Formatador.formatarValorPercentual(percentual, 2);
	}

	public void setDre(boolean dre) {
		this.dre = dre;
	}

	public boolean getDre() {
		return dre;
	}

    public static DemonstrativoFinanceiro obterTituloEquivalencia(TipoEquivalenciaDRE equivalencia, List<DemonstrativoFinanceiro> dfs) {
		int indexOf = dfs.indexOf(new DemonstrativoFinanceiro(equivalencia.getCodigoNode()));
        if (indexOf >= 0) {
			return dfs.get(indexOf);
        } else {
            return new DemonstrativoFinanceiro();
		}
	}

	public void setMetaPerc(Double metaPerc) {
		this.metaPerc = metaPerc;
	}

	public Double getMetaPerc() {
		return metaPerc;
	}

	public void setLink(boolean link) {
		this.link = link;
	}

	public boolean getLink() {
		return link;
	}

	public void setConta(int conta) {
		this.conta = conta;
	}

	public int getConta() {
		return conta;
	}

	public void setTipoconta(int tipoconta) {
		this.tipoconta = tipoconta;
	}

	public int getTipoconta() {
		return tipoconta;
	}

	public void setDfConta(boolean dfConta) {
		this.dfConta = dfConta;
	}

	public boolean getDfConta() {
		return dfConta;
	}

	public void setSaldoInicial(Double saldoInicial) {
		this.saldoInicial = saldoInicial;
	}

	public Double getSaldoInicial() {
		return saldoInicial;
	}

	public void setSaldoFinal(Double saldoFinal) {
		this.saldoFinal = saldoFinal;
	}

	public Double getSaldoFinal() {
		return saldoFinal;
	}


    public synchronized String getMes(int i) {
        try {
            String valor = Formatador.formatarValorMonetarioSemMoeda(getListaMeses().get(getListaMeses().size() - i).getTotal());
            return valor;
        } catch (Exception e) {
        }
        return "";
    }

    public double getTotalEntradaTodosOsMeses() {
        return totalEntradaTodosOsMeses;
    }

    public void setTotalEntradaTodosOsMeses(double totalEntradaTodosOsMeses) {
        this.totalEntradaTodosOsMeses = totalEntradaTodosOsMeses;
    }

    public double getTotalSaidaTodosOsMeses() {
        return totalSaidaTodosOsMeses;
    }

    public void setTotalSaidaTodosOsMeses(double totalSaidaTodosOsMeses) {
        this.totalSaidaTodosOsMeses = totalSaidaTodosOsMeses;
    }

    public synchronized String getMes1() {
        return getMes(1);
    }

    public synchronized String getMes2() {
        return getMes(2);
    }

    public synchronized String getMes3() {
        return getMes(3);
    }

    public synchronized String getMes4() {
        return getMes(4);
    }

    public synchronized String getMes5() {
        return getMes(5);
    }

    public synchronized String getMes6() {
        return getMes(6);
    }

    public synchronized String getMes7() {
        return getMes(7);
    }

    public synchronized String getMes8() {
        return getMes(8);
    }

    public synchronized String getMes9() {
        return getMes(9);
    }

    public synchronized String getMes10() {
        return getMes(10);
    }

    public synchronized String getMes11() {
        return getMes(11);
    }

    public synchronized String getMes12() {
        return getMes(12);
    }

    public synchronized String getMes13() {
        return getMes(13);
    }

    public synchronized String getMes14() {
        return getMes(14);
    }

    public synchronized String getMes15() {
        return getMes(15);
    }

    public synchronized String getMes16() {
        return getMes(16);
    }

    public synchronized String getMes17() {
        return getMes(17);
    }

    public synchronized String getMes18() {
        return getMes(18);
    }

    public synchronized String getMes19() {
        return getMes(19);
    }

    public synchronized String getMes20() {
        return getMes(20);
    }

    public synchronized String getMes21() {
        return getMes(21);
    }

    public synchronized String getMes22() {
        return getMes(22);
    }

    public synchronized String getMes23() {
        return getMes(23);
    }

    public synchronized String getMes24() {
        return getMes(24);
    }

    public Boolean getInvestimento() {
        return investimento;
    }

    public void setInvestimento(Boolean investimento) {
        this.investimento = investimento;
    }

    public HashMap<String, Double> getTotalPorFormaPagamento() {
        return totalPorFormaPagamento;
    }

    public void setTotalPorFormaPagamento(HashMap<String, Double> totalPorFormaPagamento) {
        this.totalPorFormaPagamento = totalPorFormaPagamento;
    }

    public String getTotalPorFormaPagamentoFormatado() {
        String retorno = "";
        if (this.totalPorFormaPagamento != null) {
            for (String key : this.totalPorFormaPagamento.keySet()) {
                retorno += "<br><br>&nbsp;&nbsp;&nbsp;&nbsp;"+key + "<br>&nbsp;&nbsp;&nbsp;&nbsp;Total: " +
                        Formatador.formatarValorMonetario(this.totalPorFormaPagamento.get(key));
            }
        }
        return retorno;
    }

    public String getCodigoPlanoContaTotalFormaPag() {
        return codigoPlanoContaTotalFormaPag;
    }

    public void setCodigoPlanoContaTotalFormaPag(String codigoPlanoContaTotalFormaPag) {
        this.codigoPlanoContaTotalFormaPag = codigoPlanoContaTotalFormaPag;
    }
}

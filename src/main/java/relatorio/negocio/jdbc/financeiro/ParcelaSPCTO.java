package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.microsservice.integracoes.MovParcelaCDLTO;
import servicos.impl.microsservice.integracoes.TelefoneCDLTO;

import java.util.Date;

public class ParcelaSPCTO extends SuperTO {
    private String matricula;
    private String nome;
    private String rg;
    private String ufRg;
    private String cpf;
    private String nomePai;
    private String cpfPai;
    private String nomeMae;
    private String cpfMae;
    private String nomeTerceiro;
    private String cpfTerceiro;

    private String cpfApresentar;
    private String nomeApresentar;

    private int parcela;
    private String descricaoParcela;
    private Date dataFatura;
    private Date dateVencimento;
    private Date dataPagamento;
    private int contrato;
    private int vendaAvulsa;
    private String situacao;
    private String recorrencia;
    private Double valor = 0.0;
    private String email;
    private String telefone;
    private String nomeempresa;
    private Double multas = 0.0;
    private Double juros = 0.0;
    private Integer cliente;
    private Date dataCancelamento;
    private Integer nrTentativas;
    private String motivoRetorno;
    private String convenioCobranca;
    private String formasPagamento;
    private String modalidades;
    private Date dataNascimento;
    private Date dataNascimentoResponsavel;
    private EnderecoDTO endereco;

    private Integer codigoPessoa;
    private boolean selecionada = false;
    private boolean incluidaSpc = false;
    private boolean erroInclusaoSpc = false;
    private String situacaoSpc;
    private String jsonSpc;
    private PessoaVO pessoaResponsavel;
    private String erro;


    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome.toUpperCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getNomeAbreviado() {
        if (nome != null && nome.length() > 30) {
            return nome.substring(0, 30).concat("...").toUpperCase();
        }
        return nome.toUpperCase();
    }

    public String getNomeApresentar() {
        if (nomeApresentar == null) {
            nomeApresentar = "";
        }
        return nomeApresentar;
    }

    public void setNomeApresentar(String nomeApresentar) {
        this.nomeApresentar = nomeApresentar;
    }

    public String getCpfApresentar() {
        if (cpfApresentar == null) {
            cpfApresentar = "";
        }

        return cpfApresentar;
    }

    public void setCpfApresentar(String cpfApresentar) {
        this.cpfApresentar = cpfApresentar;
    }

    public Date getDataNascimentoApresentar() {
        return getDataNascimento();
    }

    public String getCpfPai() {
        return cpfPai;
    }

    public void setCpfPai(String cpfPai) {
        this.cpfPai = cpfPai;
    }

    public String getCpfMae() {
        return cpfMae;
    }

    public void setCpfMae(String cpfMae) {
        this.cpfMae = cpfMae;
    }

    public String getCpfTerceiro() {
        return cpfTerceiro;
    }

    public void setCpfTerceiro(String cpfTerceiro) {
        this.cpfTerceiro = cpfTerceiro;
    }

    public String getRg() {
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getUfRg() {
        return ufRg;
    }

    public void setUfRg(String ufRg) {
        this.ufRg = ufRg;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Date getDataNascimentoResponsavel() {
        return dataNascimentoResponsavel;
    }

    public void setDataNascimentoResponsavel(Date dataNascimentoResponsavel) {
        this.dataNascimentoResponsavel = dataNascimentoResponsavel;
    }

    public int getParcela() {
        return parcela;
    }

    public void setParcela(int parcela) {
        this.parcela = parcela;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public String getDescricaoParcelaAbreviado() {
        if (descricaoParcela != null && descricaoParcela.length() > 15) {
            return descricaoParcela.substring(0, 15).concat("...");
        }
        return descricaoParcela;
    }

    public Date getDataFatura() {
        return dataFatura;
    }

    public void setDataFatura(Date dataFatura) {
        this.dataFatura = dataFatura;
    }

    public Date getDateVencimento() {
        return dateVencimento;
    }

    public void setDateVencimento(Date dateVencimento) {
        this.dateVencimento = dateVencimento;
    }

    public String getDateVencimento_Apresentar() {
        return Uteis.getData(dateVencimento, "br");
    }

    public String getDataFaturamento_Apresentar() {
        return Uteis.getData(dataFatura, "br");
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getDataPagamento_Apresentar() {
        if (dataPagamento == null) {
            return "";
        }
        return Uteis.getData(dataPagamento, "br");
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public int getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(int vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public String getCodigoVendaApresentar() {
        if (contrato > 0) {
            return "C-" + contrato;
        }
        if (vendaAvulsa > 0) {
            return "V-" + vendaAvulsa;
        }
        return "";
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            situacao = "";
        }
        if (situacao.equals("EA")) {
            return "Em Aberto";
        }
        if (situacao.equals("PG")) {
            return "Pago";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        if (situacao.equals("RG")) {
            return "Renegociado";
        }
        return situacao;
    }


    public String getRecorrencia() {
        return recorrencia;
    }

    public void setRecorrencia(String recorrencia) {
        this.recorrencia = recorrencia;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setEmailValidado(String email) {
        email = Uteis.retirarAcentuacaoRegex(email);
        if (UteisEmail.getValidEmail(email)) {
            this.email = email;
        } else {
            this.email = "";
        }
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getPrimeiroTelefone() {
        if (!UteisValidacao.emptyString(telefone)) {
            try {
                return telefone.split(",")[0];
            } catch (Exception e) {
                e.printStackTrace();
                return "";
            }
        }
        return "";
    }

    public String getTelefones() {
        String telefones = "";
        boolean outros = false;
        for (String telefone : telefone.split(",")) {
            if (telefones == "") {
                telefones = "<b>Outros</b><br>";
                continue;
            }
            outros = true;
            telefones += telefone + "</br>";
        }
        return outros ? telefones : "Nenhum outro telefone.";
    }

    public String getNomeempresa() {
        return nomeempresa;
    }

    public void setNomeempresa(String nomeempresa) {
        this.nomeempresa = nomeempresa;
    }

    public Double getMultas() {
        return multas;
    }

    public void setMultas(Double multasJuros) {
        this.multas = multasJuros;
    }

    public Double getJuros() {
        return juros;
    }

    public void setJuros(Double juros) {
        this.juros = juros;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public String getValor_Apresentar() {
        if (UteisValidacao.emptyNumber(getValor())) {
            return "";
        } else {
            return Formatador.formatarValorMonetarioSemMoeda(getValor());
        }
    }

    public double getValorMultasJuros() {
        return getValor() + getMultas() + getJuros();
    }

    public String getValorMultasJuros_Apresentar() {
        if (UteisValidacao.emptyNumber(getValorMultasJuros())) {
            return "";
        } else {
            return Formatador.formatarValorMonetarioSemMoeda(getValorMultasJuros());
        }
    }

    public Date getDataCancelamento() {
        return dataCancelamento;
    }

    public void setDataCancelamento(Date dataCancelamento) {
        this.dataCancelamento = dataCancelamento;
    }

    public String getDataCancelamento_Hint() {
        if (dataCancelamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela cancelada na data " + Calendario.getData(dataCancelamento, "dd/MM/yyyy");
    }

    public String getDataPagamento_Hint() {
        if (dataPagamento == null) {
            return getSituacao_Apresentar();
        }
        return "Parcela paga na data " + Calendario.getData(dataPagamento, "dd/MM/yyyy");
    }

    public Integer getNrTentativas() {
        return nrTentativas;
    }

    public void setNrTentativas(Integer nrTentativas) {
        this.nrTentativas = nrTentativas;
    }

    public String getMotivoRetorno() {
        return motivoRetorno;
    }

    public void setMotivoRetorno(String motivoRetorno) {
        this.motivoRetorno = motivoRetorno;
    }

    public String getMotivoRetornoAbreviado() {
        if (motivoRetorno != null && motivoRetorno.length() > 15) {
            return motivoRetorno.substring(0, 15).concat("...");
        }
        return motivoRetorno;
    }

    public String getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(String convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public String getFormasPagamento() {
        if (formasPagamento == null) {
            return "";
        }
        return formasPagamento;
    }

    public void setFormasPagamento(String formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public String getModalidades() {
        if (modalidades == null) {
            return "";
        }
        return modalidades;
    }

    public void setModalidades(String modalidades) {
        this.modalidades = modalidades;
    }

    public String getCpf() {
        if (cpf == null) {
            return "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public EnderecoDTO getEndereco() {
        if (endereco == null) {
            endereco = new EnderecoDTO();
        }
        return endereco;
    }

    public void setEndereco(EnderecoDTO endereco) {
        this.endereco = endereco;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public boolean isPermiteSelecionar() {
        if (incluidaSpc) {
            return true;
        }

        if ("CA".equals(situacao) || "PG".equals(situacao)) {
            return false;
        }

        return Calendario.menor(getDateVencimento(), Calendario.hoje());
    }

    public boolean isSelecionada() {
        return selecionada;
    }

    public void setSelecionada(boolean selecionada) {
        this.selecionada = selecionada;
    }

    public boolean isIncluidaSpc() {
        return incluidaSpc;
    }

    public void setIncluidaSpc(boolean incluidaSpc) {
        this.incluidaSpc = incluidaSpc;
    }

    public boolean isErroInclusaoSpc() {
        return erroInclusaoSpc;
    }

    public void setErroInclusaoSpc(boolean erroInclusaoSpc) {
        this.erroInclusaoSpc = erroInclusaoSpc;
    }

    public String getSituacaoSpc() {
        if (situacaoSpc == null) {
            situacaoSpc = "";
        }
        return situacaoSpc;
    }

    public void setSituacaoSpc(String situacaoSpc) {
        this.situacaoSpc = situacaoSpc;
    }

    public String getNomePai() {
        if (nomePai == null) {
            nomePai = "";
        }
        return nomePai;
    }

    public void setNomePai(String nomePai) {
        this.nomePai = nomePai;
    }

    public String getNomeMae() {
        if (nomeMae == null) {
            nomeMae = "";
        }
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public String getNomeTerceiro() {
        return nomeTerceiro;
    }

    public void setNomeTerceiro(String nomeTerceiro) {
        this.nomeTerceiro = nomeTerceiro;
    }

    public String getJsonSpc() {
        return jsonSpc;
    }

    public void setJsonSpc(String jsonSpc) {
        this.jsonSpc = jsonSpc;
    }

    public MovParcelaCDLTO toParcelaCDTO() {
        MovParcelaCDLTO movParcelaCDLTO = new MovParcelaCDLTO();
        movParcelaCDLTO.setDataRegistro(getDataFatura().getTime());
        movParcelaCDLTO.setDataVencimento(getDateVencimento().getTime());
        movParcelaCDLTO.setCodigoContrato(getCodigoVendaApresentar());
        movParcelaCDLTO.setValorParcela(getValor());
        movParcelaCDLTO.getPessoa().setNome(getNomeApresentar());
        movParcelaCDLTO.getPessoa().setCpf(getCpfApresentar());
        movParcelaCDLTO.getPessoa().setEndereco(getEndereco().toEnderecoCDLTO());
        movParcelaCDLTO.getPessoa().setNumeroRg(getRg());
        movParcelaCDLTO.getPessoa().setUfRg(getUfRg());
        movParcelaCDLTO.getPessoa().setEmail(getEmail());
        String telefone = getPrimeiroTelefone();
        if (!UteisValidacao.emptyString(telefone)) {
            TelefoneCDLTO telefoneCDLTO = new TelefoneCDLTO();
            telefone = telefone
                    .replace("(", "")
                    .replace(")", "");

            String dddTelefone = telefone.substring(0, 2);
            telefoneCDLTO.setDdd(dddTelefone);

            String numeroTelefone = telefone.substring(2);
            telefoneCDLTO.setNumero(numeroTelefone);

            movParcelaCDLTO.getPessoa().setTelefone(telefoneCDLTO);
        }

        if (getDataNascimentoApresentar() != null) {
            movParcelaCDLTO.getPessoa().setDataNascimento(getDataNascimentoApresentar().getTime());
        }
        return movParcelaCDLTO;
    }

    public PessoaVO getPessoaResponsavel() {
        if(pessoaResponsavel == null) {
            new PessoaVO();
        }
        return pessoaResponsavel;
    }

    public void setPessoaResponsavel(PessoaVO pessoaResponsavel) {
        this.pessoaResponsavel = pessoaResponsavel;
    }

    public String getErro() {
        if(erro == null) {
            erro = "";
        }
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}

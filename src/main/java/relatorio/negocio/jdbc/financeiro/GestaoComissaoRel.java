/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.financeiro.ComissaoVO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Estado;
import negocio.facade.jdbc.basico.Pais;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.Ambiente;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.utilitarias.CacheControl;
import relatorio.negocio.comuns.financeiro.DetalhamentoLancamentoDF_VO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class GestaoComissaoRel extends SuperRelatorio {

    private Date dataInicio = Calendario.hoje();
    private Date dataFim = Calendario.hoje();
    private EmpresaVO empresa = null;
    private List<ComissaoVO> lista = new ArrayList<ComissaoVO>();

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public List<ComissaoVO> getLista() {
        return lista;
    }

    public void setLista(List<ComissaoVO> lista) {
        this.lista = lista;
    }

    public GestaoComissaoRel() throws Exception {
    }

    public List<ComissaoVO> consultarPorCompetencia(EmpresaVO empresa, Date dataInicio, Date dataFim,
            boolean somenteComTurmas) throws Exception {
        DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
        List<Integer> centroCustos = new ArrayList<Integer>();
        setLista(new ArrayList<ComissaoVO>());
        List<String> mesesReferencia = Uteis.getMesesStringEntreDatas(dataInicio, dataFim);
        String[] tiposProdutos = empresa.isUsarManutencaoModalidadeComissao() ? new String[]{"PM", "MM"} : new String[]{"PM"};

        StringBuilder condicaoMesesReferencia = new StringBuilder(" mp.mesreferencia in (");
        for (int i = 0; i < mesesReferencia.size(); i++) {
            String mesReferencia = mesesReferencia.get(i);
            condicaoMesesReferencia.append("'").append(mesReferencia).append("'");
            if (i != mesesReferencia.size() - 1) {
                condicaoMesesReferencia.append(",");
            }
        }
        condicaoMesesReferencia.append(")");

        StringBuilder clienteQueCompraram = new StringBuilder("select c.*, st.situacaocontrato, pes.* from cliente c ");
        clienteQueCompraram.append(" LEFT JOIN situacaoclientesinteticodw st ON st.codigocliente  = c.codigo ");
        clienteQueCompraram.append(" LEFT JOIN pessoa pes ON pes.codigo = c.pessoa ");
        clienteQueCompraram.append("where (");
        clienteQueCompraram.append("pessoa in (");
        clienteQueCompraram.append("select pessoa from movproduto mp ");
        clienteQueCompraram.append("where c.pessoa = mp.pessoa ");
        clienteQueCompraram.append("and mp.situacao not in ('CA') ");
        clienteQueCompraram.append("and mp.totalfinal > 0 ");
        clienteQueCompraram.append("and mp.produto in (select codigo from produto where tipoproduto = 'PM' OR tipoproduto = 'MM') ");
//        clienteQueCompraram.append("and mp.contrato = 4032 ");
        if (somenteComTurmas) {
            clienteQueCompraram.append(" and mp.contrato in (select contrato from matriculaalunohorarioturma) ");
        }
        clienteQueCompraram.append("and %s ");
        clienteQueCompraram.append("%s)) ");

        String condicaoEmpresa = empresa.getCodigo() != 0 ? " and empresa = " + empresa.getCodigo() : "";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(clienteQueCompraram.toString(), condicaoMesesReferencia.toString(), condicaoEmpresa), FacadeManager.getFacade().getRisco().getCon());


//        List<ClienteVO> clientes = Cliente.montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_MINIMOS, Conexao.getFromSession());
        List<ClienteVO> clientes = montarDadosCompetencia(rs);
        //Para cada cliente, criar uma comissao (em cima de CADA produto).
        for (ClienteVO cliente : clientes) {
            for (String tipoProduto : tiposProdutos) {
                try {
                    ComissaoVO comissao = new ComissaoVO();
                    comissao.setCliente(cliente);

                    List<DetalhamentoLancamentoDF_VO> listaDetalheLancamento = new ArrayList<DetalhamentoLancamentoDF_VO>();

                    StringBuilder comprasDoCliente = new StringBuilder("select p.tipoproduto, mp.codigo,mp.pessoa,mp.contrato,mp.empresa,mp.totalfinal,mp.descricaomovprodutomodalidade,mp.mesreferencia from movproduto mp ");
                    comprasDoCliente.append("inner join produto p on p.codigo = mp.produto ");
                    comprasDoCliente.append("where mp.situacao not in ('CA') ");
                    comprasDoCliente.append("and mp.contrato is not null ");
                    comprasDoCliente.append("and (p.tipoproduto = '").append(tipoProduto).append("')");
                    comprasDoCliente.append("and mp.pessoa = ").append(cliente.getPessoa().getCodigo()).append(" ");
                    comprasDoCliente.append("and %s;");

                    ResultSet rsCompras = SuperFacadeJDBC.criarConsulta(String.format(comprasDoCliente.toString(), condicaoMesesReferencia.toString()), FacadeManager.getFacade().getRisco().getCon());

                    while (rsCompras.next()) {

                        LancamentoDF lancamento = new LancamentoDF();
                        lancamento.setDadosResumidos(true);
                        lancamento.setMovPagamento(0);
                        lancamento.setMovProduto(rsCompras.getInt("codigo"));
                        lancamento.setCodigoPessoa(rsCompras.getInt("pessoa"));
                        lancamento.setContrato(rsCompras.getInt("contrato"));
                        lancamento.setDescricaoLancamento("LANÇAMENTO");
                        lancamento.setEmpresa(rsCompras.getInt("empresa"));
                        lancamento.setValorLancamento(rsCompras.getDouble("totalfinal"));
                        lancamento.setLancamentoEhNaoAtribuido(true);
                        lancamento.setTipoProduto(rsCompras.getString("tipoproduto"));
                        lancamento.setRecibo(0);
                        lancamento.setTipoES(TipoES.ENTRADA);
                        String descricaomovprodutomodalidade = rsCompras.getString("descricaomovprodutomodalidade");
                        if (descricaomovprodutomodalidade != null && !descricaomovprodutomodalidade.isEmpty()) {
                            lancamento.setRateioModalidades(ThreadDemonstrativoFinanceiro.obterListaContratoModalidadePercentual(new StringBuilder(descricaomovprodutomodalidade)));
                        }
                        DetalhamentoLancamentoDF_VO detalhe = DetalhamentoLancamentoDF.consultarDetalhesDoLancamento(lancamento,
                                TipoVisualizacaoRelatorioDF.PLANOCONTA, df, centroCustos, dataInicio,
                                dataFim, TipoRelatorioDF.COMPETENCIA, rsCompras.getInt("contrato"), true);
                        detalhe.setCompetencia(rsCompras.getString("mesreferencia"));

                        Ordenacao.ordenarLista(detalhe.getListaContratoModalidade(), "nomeModalidade");
                        listaDetalheLancamento.add(detalhe);
                    }

                    if (listaDetalheLancamento.isEmpty()) {
                        continue;
                    }
                    //Montar a lista de contratos que estes pagamentos pagam
                    Map<Integer, ContratoVO> mapaContratos = new HashMap<Integer, ContratoVO>();
                    processarMapaDeContratos(listaDetalheLancamento, mapaContratos);
                    //Montar lista de ContratosModalidades e MatrículasTurmas envolvidas
                    Set<Integer> codigos = mapaContratos.keySet();
                    
                    //Calcular a proporção do Valor do Produto que Incide Comissão referente a cada modalidade
                    for (DetalhamentoLancamentoDF_VO detalhe : listaDetalheLancamento) {
                        Date inicioComp = Uteis.getDataComHoraZerada(Uteis.getDate("01/"+detalhe.getCompetencia()));
                        Date finalComp = Uteis.obterUltimoDiaMesUltimaHora(inicioComp);
                                List<MatriculaAlunoHorarioTurmaVO> listaMatriculas = getFacade().getMatriculaAlunoHorarioTurma().consultarPorCodigoContrato(detalhe.getContratoVO().getCodigo(), inicioComp, finalComp, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, false);
                                if (listaMatriculas.isEmpty()) {
                                    listaMatriculas= getFacade().getMatriculaAlunoHorarioTurma().consultarPorCodigoContrato(detalhe.getContratoVO().getCodigo(), inicioComp, finalComp, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, true);
                                }
                        if (listaMatriculas.isEmpty() && somenteComTurmas) {
                            continue;
                        }
                        calcularComissaoPorModalidade(detalhe, listaMatriculas, true, inicioComp, finalComp);
                        
                        List<ContratoModalidadePercentual> modalidades = new ArrayList<ContratoModalidadePercentual>(detalhe.getListaContratoModalidade());
                        for (ContratoModalidadePercentual modalidade : modalidades) {
                            if (UteisValidacao.emptyNumber(modalidade.getValorPago())) {
                                detalhe.getListaContratoModalidade().remove(modalidade);
                            }
                        }
                    }
                    comissao.setListaDetalheLancamento(listaDetalheLancamento);
                    getLista().add(comissao);
                    long l2 = new Date().getTime();
//                System.out.println("Cliente -> " + cliente.getCodigo() + " " + new Long(l2 - l1) + "ms");
                } catch (Exception e) {
                    throw new Exception("Erro ao processar cliente: " + cliente.getMatricula() + ". " + e.getMessage());
                }
            }

        }
        Ordenacao.ordenarLista(lista, "nome");
        return lista;
    }

    private void processarMapaDeContratos(List<DetalhamentoLancamentoDF_VO> listaDetalheLancamento, Map<Integer, ContratoVO> mapaContratos) throws Exception {
        for (DetalhamentoLancamentoDF_VO detalhe : listaDetalheLancamento) {
            if (detalhe.getContratoVO() == null) {
                continue;
            }

            if (!mapaContratos.containsKey(detalhe.getContratoVO().getCodigo())) {

                mapaContratos.put(detalhe.getContratoVO().getCodigo(),
                        getFacade().getContrato().consultarPorChavePrimaria(
                        detalhe.getContratoVO().getCodigo(),
                        Uteis.NIVELMONTARDADOS_MINIMOS));
            }
        }
    }

    private void calcularComissaoPorModalidade(DetalhamentoLancamentoDF_VO detalheLancamento,
            List<MatriculaAlunoHorarioTurmaVO> listaMatriculas,
            boolean competencia,
            Date inicio, Date fim) throws Exception {

        //preencher mapa modalidade x (valor e turmas)
        Map<Integer, CalculoModalidadeMatriculas> modalidades = new HashMap<Integer, CalculoModalidadeMatriculas>();
        if (detalheLancamento.getContratoVO() == null) {
            return;
        }
        List<ContratoModalidadePercentual> listaCM = detalheLancamento.getListaContratoModalidade();
        for (ContratoModalidadePercentual contratoModalidadePercentual : listaCM) {
            modalidades.put(contratoModalidadePercentual.getCodigoModalidade(), new CalculoModalidadeMatriculas(contratoModalidadePercentual, new ArrayList<MatriculaAlunoHorarioTurmaVO>()));
        }
//    	necessario obter o inicio de vigencia do contrato para os casos onde a consulta é por competencia
        int inicioVigencia = 0;
        //iterar nas matriculas para posicioná-las de acordo com a modalidade
        for (MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO : listaMatriculas) {
            if (matriculaAlunoHorarioTurmaVO.getContrato().getCodigo().equals(detalheLancamento.getContratoVO().getCodigo())) {
                TurmaVO t = getFacade().getTurma().consultarPorChavePrimaria(matriculaAlunoHorarioTurmaVO.getHorarioTurma().getTurma(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                CalculoModalidadeMatriculas calculoModalidadeMatriculas = modalidades.get(t.getModalidade().getCodigo());
                if (calculoModalidadeMatriculas != null) {
                    calculoModalidadeMatriculas.matriculas.add(matriculaAlunoHorarioTurmaVO);
                }
                //setar inicio de vigencia
                if (inicioVigencia == 0) {
                    inicioVigencia = Uteis.obterDiaData(matriculaAlunoHorarioTurmaVO.getContrato().getVigenciaDe());
                }
            }

        }
        //iterar nas modalidades para calcular as comissões
        Set<Integer> keySet = modalidades.keySet();
        for (Integer key : keySet) {
            CalculoModalidadeMatriculas calculoModalidadeMatriculas = modalidades.get(key);
            //o nr de dias do mes e o valor diário são importantes pois existem os casos onde a modalidade ou a turma não se extendeu por todo o mês
            int nrDiasMes = Uteis.obterNumeroDiasDoMes(inicio);
            Double valorDiario = calculoModalidadeMatriculas.modalidadePercentual.getValorPago() / nrDiasMes;
            //o CalculoModalidadeMatriculasDias representa um periodo de dias dentro do mes que conteve um determinado nr de horarios
            //por modalidade. ou seja, se houve manutenção no 5 dia de um mes, retirando um horario, nos 4 primeiros dias deste mes
            // o calculo tem que considerar este horario. já no resto do mês, deve desconsiderar
            List<CalculoModalidadeMatriculasDias> diasCalculo;
            // se relatorio por competencia, usar o inicio da vigencia do contrato para saber qual o inicio do periodo considerado,
            //se for compensacao, o inicio é o parametro da consulta
            // -> Alterada na atividade 51445/1/1, a pedido da Dhyana, para competência também tratar as comissões 'fracionadas'.
            diasCalculo = montarDiasCalculo(inicio, fim, calculoModalidadeMatriculas.matriculas, nrDiasMes);
            //calcular a comissão por horario, iterando nas faixas de dias do mês criadas anteriormente
            for (CalculoModalidadeMatriculasDias calc : diasCalculo) {
                for (MatriculaAlunoHorarioTurmaVO matricula : calculoModalidadeMatriculas.matriculas) {
                    //se o horario esteve presente nesta faixa de dias
                    if (calc.matriculas.contains(matricula.getCodigo())) {

                        boolean teste = false;
                        if (teste) {
                            Double porcComissao = getFacade().getColaborador().getPorcComissao(matricula.getPessoa().getCodigo(),
                                    calculoModalidadeMatriculas.modalidadePercentual.getCodigoModalidade(), matricula.getHorarioTurma().getTurma(),
                                    matricula.getHorarioTurma().getProfessor().getCodigo());

                            Double valorComissao = getFacade().getColaborador().getValorComissao(matricula.getPessoa().getCodigo(),
                                    calculoModalidadeMatriculas.modalidadePercentual.getCodigoModalidade(), matricula.getHorarioTurma().getTurma(),
                                    matricula.getHorarioTurma().getProfessor().getCodigo());

                            boolean tipoValorFixo = false;
                            if (porcComissao == null &&
                                    !UteisValidacao.emptyNumber(valorComissao)) {
                                porcComissao = valorComissao;
                                tipoValorFixo = true;
                            }

                            if (porcComissao == null) {
                                if (!UteisValidacao.emptyNumber(matricula.getHorarioTurma().getProfessor().getPorcComissao())) {
                                    porcComissao = matricula.getHorarioTurma().getProfessor().getPorcComissao();
                                } else if (!UteisValidacao.emptyNumber(matricula.getHorarioTurma().getProfessor().getValorComissao())) {
                                    porcComissao = matricula.getHorarioTurma().getProfessor().getValorComissao();
                                    tipoValorFixo = true;
                                } else {
                                    porcComissao = matricula.getHorarioTurma().getProfessor().getPorcComissao();
                                }
                            }
                            matricula.setPorcComissao(porcComissao);

                            double valorPeriodo = calc.proporcaoDias * (valorDiario / calc.matriculas.size());

                            matricula.setValor(matricula.getValor() + valorPeriodo);

                            if (tipoValorFixo) {
                                matricula.setValorComissao(matricula.getValorComissao() + Uteis.arredondarForcando2CasasDecimais(porcComissao));
                            } else {
                                matricula.setValorComissao(matricula.getValorComissao() + Uteis.arredondarForcando2CasasDecimais(
                                        (valorPeriodo * (porcComissao / 100))));
                            }

                        } else {

                            Double porcComissao = getFacade().getColaborador().getPorcComissao(matricula.getPessoa().getCodigo(),
                                    calculoModalidadeMatriculas.modalidadePercentual.getCodigoModalidade(), matricula.getHorarioTurma().getTurma(),
                                    matricula.getHorarioTurma().getProfessor().getCodigo());
                            if (porcComissao == null) {
                                porcComissao = matricula.getHorarioTurma().getProfessor().getPorcComissao();
                            }
                            matricula.setPorcComissao(porcComissao);

                            double valorPeriodo = calc.proporcaoDias * (valorDiario / calc.matriculas.size());

                            matricula.setValor(matricula.getValor() + valorPeriodo);
                            matricula.setValorComissao(matricula.getValorComissao() + Uteis.arredondarForcando2CasasDecimais(
                                    (valorPeriodo * (porcComissao / 100))));
                        }
                    }
                }
            }
            Ordenacao.ordenarLista(calculoModalidadeMatriculas.matriculas, "horarioTurma");
            calculoModalidadeMatriculas.modalidadePercentual.getMatriculasDesteContratoModalidade().addAll(
                    calculoModalidadeMatriculas.matriculas);
        }
    }

    private List<ClienteVO> montarDados(ResultSet dadosSQL) throws Exception {
        List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();

        while (dadosSQL.next()) {
            //dados de cliente
            ClienteVO cliente = new ClienteVO();
            cliente.setNovoObj(false);
            cliente.setCodigo(dadosSQL.getInt("codigoCliente"));
            cliente.setCodAcesso(dadosSQL.getString("codAcesso"));
            cliente.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
            cliente.getPessoa().setNome(dadosSQL.getString("nomecliente"));
            cliente.setSituacao(dadosSQL.getString("situacao"));
            cliente.setSituacaoContrato(dadosSQL.getString("situacaocontrato"));
            cliente.setMatricula(dadosSQL.getString("matricula"));
            cliente.setCodigoMatricula(dadosSQL.getInt("codigoMatricula"));
            cliente.getCategoria().setCodigo(dadosSQL.getInt("categoria"));
            cliente.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
            cliente.setUaCliente(new AcessoClienteVO());
            cliente.setCodAcessoAlternativo(dadosSQL.getString("codAcessoAlternativo"));
//            cliente.setPessoa(Pessoa.montarDados(dadosSQL, Uteis.NIVELMONTARDADOS_MINIMOS, Conexao.getFromSession()));
            //dados de pessoa
            listaClientes.add(cliente);
        }

        return listaClientes;
    }

    private List<ClienteVO> montarDadosCompetencia(ResultSet dadosSQL) throws Exception {
        List<ClienteVO> listaClientes = new ArrayList<ClienteVO>();

        while (dadosSQL.next()) {
            //dados de cliente
            ClienteVO cliente = new ClienteVO();
            cliente.setNovoObj(false);
            cliente.setCodigo(dadosSQL.getInt("codigo"));
            cliente.setCodAcesso(dadosSQL.getString("codAcesso"));
            cliente.getPessoa().setCodigo(dadosSQL.getInt("pessoa"));
            cliente.getPessoa().setNome(dadosSQL.getString("nome"));
            cliente.setSituacao(dadosSQL.getString("situacao"));
            cliente.setSituacaoContrato(dadosSQL.getString("situacaocontrato"));
            cliente.setMatricula(dadosSQL.getString("matricula"));
            cliente.setCodigoMatricula(dadosSQL.getInt("codigoMatricula"));
            cliente.getCategoria().setCodigo(dadosSQL.getInt("categoria"));
            cliente.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
            cliente.setUaCliente(new AcessoClienteVO());
            cliente.setCodAcessoAlternativo(dadosSQL.getString("codAcessoAlternativo"));
            listaClientes.add(cliente);
        }
        return listaClientes;
    }

    public List<ComissaoVO> consultar(EmpresaVO empresa, final Date dataInicio,
            final Date dataFim, boolean somenteComTurmas) throws Exception {
        try {
            CacheControl.toggleCache(Empresa.class, true);
            CacheControl.toggleCache(Usuario.class, true);
            CacheControl.toggleCache(Cidade.class, true);
            CacheControl.toggleCache(Estado.class, true);
            CacheControl.toggleCache(Pais.class, true);
            CacheControl.toggleCache(Turma.class, true);
            CacheControl.toggleCache(Ambiente.class, true);
            CacheControl.toggleCache(HorarioTurma.class, true);
            CacheControl.toggleCache(Colaborador.class, true);
            CacheControl.toggleCache(Plano.class, true);
            CacheControl.toggleCache(Contrato.class, true);

            //
            DemonstrativoFinanceiro df = new DemonstrativoFinanceiro();
            List<Integer> centrosCusto = new ArrayList<Integer>();
//        String[] tiposProdutos = new String[]{"PM"};
            String[] tiposProdutos = new String[]{"PM", "MM"};
            this.empresa = empresa;
            this.dataInicio = dataInicio;
            this.dataFim = Calendario.getDataComHora(dataFim, "23:59");
            setLista(new ArrayList<>());

            String condicaoPagamento = String.format(" datapagamento >= '%s' and datapagamento <='%s'",
                    Uteis.getDataJDBCTimestamp(this.dataInicio),
                    Uteis.getDataJDBCTimestamp(this.dataFim));

            String condicaoDataCompesancaoCartao = String.format(" ca.datacompesancao >= '%s' and ca.datacompesancao <='%s'",
                    Uteis.getDataJDBCTimestamp(this.dataInicio),
                    Uteis.getDataJDBCTimestamp(this.dataFim));

            String condicaoDataCompesancaoCheque = String.format(" ch.datacompesancao >= '%s' and ch.datacompesancao <='%s'",
                    Uteis.getDataJDBCTimestamp(this.dataInicio),
                    Uteis.getDataJDBCTimestamp(this.dataFim));


            StringBuilder sql = new StringBuilder();
            sql.append("SELECT distinct cli.*,cli.codigo as codigoCliente, st.situacaocontrato, st.nomecliente FROM cliente cli ");
            sql.append(" INNER JOIN situacaoclientesinteticodw st ON st.codigocliente  = cli.codigo");
            sql.append(" INNER JOIN contrato con ON con.pessoa = cli.pessoa ");
            if (somenteComTurmas) {
                sql.append(" and con.codigo in (select contrato from matriculaalunohorarioturma) ");
            }
//        sql.append(" and cli.codigomatricula = 1394 ");
            sql.append(" WHERE con.codigo IN (");
            sql.append(" SELECT contrato FROM movparcela mp ");
            sql.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movparcela = mp.codigo ");
            sql.append(" INNER JOIN movpagamento mpg ON mpg.codigo = pmp.movpagamento   and mpg.valor > 0 ");
            sql.append(" WHERE (mpg.codigo in ");
            sql.append("(select codigo from movpagamento where formapagamento not in ");
            sql.append("    (select codigo from formapagamento where tipoformapagamento in ('CH', 'CA')) and %s)) ");
            sql.append("or (mpg.codigo in ");
            sql.append("    (select codigo from movpagamento where formapagamento in ");
            sql.append("        (select codigo from formapagamento where tipoformapagamento in ('CH', 'CA')) ");
            sql.append("    and ((codigo in (select movpagamento from cheque ch where %s)) ");
            sql.append("or (codigo in (select movpagamento from cartaocredito ca where %s)))))) ");
            sql.append(" %s ");

            String condicaoEmpresa = empresa.getCodigo() != 0 ? " and cli.empresa =" + empresa.getCodigo() : "";

            ResultSet rs = SuperFacadeJDBC.criarConsulta(String.format(sql.toString(), condicaoPagamento,
                    condicaoDataCompesancaoCheque,
                    condicaoDataCompesancaoCartao,
                    condicaoEmpresa), FacadeManager.getFacade().getRisco().getCon());

            List<ClienteVO> clientes = montarDados(rs);

            for (ClienteVO clienteVO : clientes) {
                try {
                    ComissaoVO com = new ComissaoVO();
                    com.setCliente(clienteVO);

                    StringBuilder sqlPagamentosDaPessoa = new StringBuilder();
                    sqlPagamentosDaPessoa.append(" select mp.codigo, fp.tipoformapagamento, con.codigo as contrato, \n");
                    sqlPagamentosDaPessoa.append(" mp.recibopagamento, mp.empresa, con.situacao from movpagamento mp  \n");
                    sqlPagamentosDaPessoa.append(" inner join recibopagamento rp on rp.codigo = mp.recibopagamento   \n");
                    sqlPagamentosDaPessoa.append(" inner join formapagamento fp on fp.codigo = mp.formapagamento  \n");

                    sqlPagamentosDaPessoa.append(" INNER JOIN pagamentomovparcela pmp ON pmp.movpagamento = mp.codigo  \n");
                    sqlPagamentosDaPessoa.append(" INNER JOIN movparcela mpc ON pmp.movparcela = mpc.codigo and mpc.pessoa = %s \n");
                    sqlPagamentosDaPessoa.append(" INNER JOIN contrato con ON mpc.contrato = con.codigo and con.pessoa = %s \n");
//                sqlPagamentosDaPessoa.append(" INNER JOIN contratomodalidade conm ON conm.contrato = con.codigo and conm.modalidade = 3 \n");

                    if (somenteComTurmas) {
                        sql.append(" and con.codigo in (select contrato from matriculaalunohorarioturma) \n");
                    }

                    sqlPagamentosDaPessoa.append(" where mp.codigo in ( \n");
//
                    movPagamentos(sqlPagamentosDaPessoa);
                    sqlPagamentosDaPessoa.append(" ) \nGROUP BY mp.codigo, fp.tipoformapagamento, con.codigo, mp.recibopagamento, mp.empresa, con.situacao ");
//                //Montar lista dos pagamentos já rateados por Receita, segundo FinanceiroWeb
                    ResultSet rsM = SuperFacadeJDBC.criarConsulta(String.format(sqlPagamentosDaPessoa.toString(),
                            clienteVO.getPessoa().getCodigo(),
                            clienteVO.getPessoa().getCodigo(),
                            condicaoPagamento,
                            condicaoDataCompesancaoCartao,
                            condicaoDataCompesancaoCheque), FacadeManager.getFacade().getRisco().getCon());
//
                    List<DetalhamentoLancamentoDF_VO> listaDetalhamentoLancamentoDF_VOs = new ArrayList<DetalhamentoLancamentoDF_VO>();
//
                    while (rsM.next()) {

                        String situacao = rsM.getString("situacao");
                        if (situacao.equals("CA")) {
                            ResultSet consulta = SuperFacadeJDBC.criarConsulta("select datainicioefetivacaooperacao from contratooperacao where "
                                    + "contrato = " + rsM.getInt("contrato") + " and tipooperacao like 'CA'", FacadeManager.getFacade().getRisco().getCon());
                            if (consulta.next()) {

                                Date dataCancelamento = consulta.getDate("datainicioefetivacaooperacao");
                                StringBuilder sqlPagamentosAntesCancelamento = new StringBuilder();
                                sqlPagamentosAntesCancelamento.append("select exists( ");
                                movPagamentos(sqlPagamentosAntesCancelamento);
                                sqlPagamentosAntesCancelamento.append(" and mpag.codigo = %s ) as existe");

                                String condicaoCancdatapagamento = String.format(" datapagamento >= '%s' and datapagamento <='%s'",
                                        Uteis.getDataJDBCTimestamp(this.dataInicio),
                                        Uteis.getDataJDBCTimestamp(dataCancelamento));

                                String condicaoCancDataCompesancaoCartao = String.format(" ca.datacompesancao >= '%s' and ca.datacompesancao <='%s'",
                                        Uteis.getDataJDBCTimestamp(this.dataInicio),
                                        Uteis.getDataJDBCTimestamp(dataCancelamento));

                                String condicaoCancDataCompesancaoCheque = String.format(" ch.datacompesancao >= '%s' and ch.datacompesancao <='%s'",
                                        Uteis.getDataJDBCTimestamp(this.dataInicio),
                                        Uteis.getDataJDBCTimestamp(dataCancelamento));


                                ResultSet rsC = SuperFacadeJDBC.criarConsulta(String.format(sqlPagamentosAntesCancelamento.toString(),
                                        condicaoCancdatapagamento,
                                        condicaoCancDataCompesancaoCartao,
                                        condicaoCancDataCompesancaoCheque,
                                        rsM.getInt("codigo")), FacadeManager.getFacade().getRisco().getCon());

                                rsC.next();
                                if (!rsC.getBoolean("existe")) {
                                    continue;
                                }

                            }
                        }
                        int idMovPagamento = rsM.getInt("codigo");
//                    long tiDF = new Date().getTime();
                        LancamentoDF lancamento = new LancamentoDF();
                        lancamento.setDadosResumidos(true);
                        lancamento.setMovPagamento(idMovPagamento);
                        lancamento.setCodigoPessoa(clienteVO.getPessoa().getCodigo());
                        lancamento.setContrato(rsM.getInt("contrato"));

                        lancamento.setDescricaoLancamento("LANÇAMENTO");
                        lancamento.setEmpresa(rsM.getInt("empresa"));
                        lancamento.setLancamentoEhNaoAtribuido(true);
                        lancamento.setTipoProduto("PM");
                        lancamento.setRecibo(rsM.getInt("recibopagamento"));
                        lancamento.setDescricaoFormaPagamento("formaPagamento");
                        //
                        String tipo = rsM.getString("tipoformapagamento");
                        if (tipo.equals("CH")) {
                            lancamento.setTipoFormaPagto(TipoFormaPagto.CHEQUE);
                        } else if (tipo.equals("CA")) {
                            lancamento.setTipoFormaPagto(TipoFormaPagto.CARTAOCREDITO);
                        } else {
                            lancamento.setTipoFormaPagto(TipoFormaPagto.AVISTA);
                        }

                        lancamento.setTipoES(TipoES.ENTRADA);
                        DetalhamentoLancamentoDF_VO detalhe = DetalhamentoLancamentoDF.consultarDetalhesDoLancamento(lancamento,
                                TipoVisualizacaoRelatorioDF.PLANOCONTA,
                                df,
                                centrosCusto,
                                this.dataInicio,
                                this.dataFim,
                                TipoRelatorioDF.RECEITA,
                                rsM.getInt("contrato"),true);
                        if (detalhe.getProdutosPagos() != null && !detalhe.getProdutosPagos().isEmpty()) {
                            detalhe.setTotalPagoPlano(0.0);
                            String[] produtosPagos = detalhe.getProdutosPagos().split("\\|");
                            for (String dadosProd : produtosPagos) {
                                if (dadosProd != null && !dadosProd.isEmpty()) {
                                    String[] dados = dadosProd.split(",");
                                    boolean produtoCerto = dados[1].equals("PM") || (empresa.isUsarManutencaoModalidadeComissao() && dados[1].equals("MM"));
                                    if (produtoCerto && Integer.valueOf(dados[2]).intValue() == detalhe.getContratoVO().getCodigo().intValue()) {
                                        detalhe.setTotalPagoPlano(detalhe.getTotalPagoPlano() + Double.valueOf(dados[3]));
                                    }
                                }
                            }
                        }

                        if (detalhe.getTotalPagoPlano() <= 0) {
                            continue;
                        }

                        Ordenacao.ordenarLista(detalhe.getListaContratoModalidade(), "nomeModalidade");
                        listaDetalhamentoLancamentoDF_VOs.add(detalhe);
                    }
                    if (listaDetalhamentoLancamentoDF_VOs.isEmpty()) {
                        continue;
                    }

                    //Montar a lista de contratos que estes pagamentos pagam
                    Map<Integer, ContratoVO> mapaContratos = new HashMap<Integer, ContratoVO>();
                    processarMapaDeContratos(listaDetalhamentoLancamentoDF_VOs, mapaContratos);
                    //Montar lista de ContratosModalidades e MatrículasTurmas envolvidas
                    Set<Integer> codigos = mapaContratos.keySet();
                    List<MatriculaAlunoHorarioTurmaVO> listaMatriculas = new ArrayList<MatriculaAlunoHorarioTurmaVO>();
                    for (Integer contrato : codigos) {
                        List<MatriculaAlunoHorarioTurmaVO> listaM = getFacade().getMatriculaAlunoHorarioTurma().
                                consultarPorCodigoContrato(contrato,
                                        dataInicio, dataFim, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, false);

                        ContratoVO contratoVO = mapaContratos.get(contrato);
                        boolean alterarValidacoes = false;
                        if (listaM.isEmpty() && Calendario.maiorOuIgual(contratoVO.getVigenciaDe(), dataFim)) {
                            alterarValidacoes = true;
                            listaM = getFacade().getMatriculaAlunoHorarioTurma().
                                    consultarPorCodigoContrato(contrato,
                                            contratoVO.getVigenciaDe(), contratoVO.getVigenciaDe(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, false);
                        }

                        if (!listaM.isEmpty()) {
                            for (DetalhamentoLancamentoDF_VO detalhe : listaDetalhamentoLancamentoDF_VOs) {
                                if (detalhe.getContratoVO().getCodigo().intValue() == contrato) {
                                    Double percSaiu = 0.0;
                                    List<ContratoModalidadePercentual> listaCM = new ArrayList<>(detalhe.getListaContratoModalidade());
                                    for (ContratoModalidadePercentual cmp : listaCM) {
                                        if (!getFacade().getMatriculaAlunoHorarioTurma().
                                                validarTemTurmaNoPeriodoOuNaoTemTurmaNenhuma(dataInicio, dataFim, cmp.getContrato(), cmp.getCodigoModalidade())) {
                                            if (!alterarValidacoes) {
                                                detalhe.getListaContratoModalidade().remove(cmp);
                                                percSaiu += cmp.getPercentagem();
                                            }
                                        }
                                    }
                                    if (percSaiu > 0.0) {
                                        reorganizarValoresModalidades(detalhe, percSaiu);
                                    }
                                }
                            }
                        }
                        listaMatriculas.addAll(listaM);
                    }

                    if (listaMatriculas.isEmpty() && somenteComTurmas) {
                        continue;
                    }
                    //Calcular a proporção do Valor do Produto que Incide Comissão referente a cada modalidade
                    for (DetalhamentoLancamentoDF_VO detalhe : listaDetalhamentoLancamentoDF_VOs) {
                        calcularComissaoPorModalidade(detalhe, getCloneLista(listaMatriculas), false, this.dataInicio, this.dataFim);
                        List<ContratoModalidadePercentual> modalidades = new ArrayList<ContratoModalidadePercentual>(detalhe.getListaContratoModalidade());
                        for (ContratoModalidadePercentual modalidade : modalidades) {
                            if (UteisValidacao.emptyNumber(modalidade.getValorPago())) {
                                detalhe.getListaContratoModalidade().remove(modalidade);
                            }
                        }
                    }
                    com.getListaDetalheLancamento().addAll(listaDetalhamentoLancamentoDF_VOs);
                    getLista().add(com);
//                long l2 = new Date().getTime();
//                System.out.println("Cliente -> " + clienteVO.getCodigo() + " " + new Long(l2 - l1) + "ms");
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new Exception("Erro ao processar cliente: " + clienteVO.getMatricula() + ". " + e.getMessage());
                }

            }
            Ordenacao.ordenarLista(lista, "nome");
            return getLista();
        } finally {
            CacheControl.clear();
        }
    }

    public void reorganizarValoresModalidades(DetalhamentoLancamentoDF_VO detalhe, Double percSaiu) {
        Double percRelativo = 1.0 - percSaiu;
        for (ContratoModalidadePercentual cmp : detalhe.getListaContratoModalidade()) {
            Double percNovo = 1.0 * cmp.getPercentagem() / percRelativo;
            cmp.setPercentagem(percNovo);
            cmp.setValorPago(percNovo * detalhe.getTotalPagoPlano());
        }

    }

    private List<MatriculaAlunoHorarioTurmaVO> getCloneLista(List<MatriculaAlunoHorarioTurmaVO> listaMatricula) throws Exception {
        List<MatriculaAlunoHorarioTurmaVO> listaM = new ArrayList<MatriculaAlunoHorarioTurmaVO>();
        for (MatriculaAlunoHorarioTurmaVO obj : listaMatricula) {
            MatriculaAlunoHorarioTurmaVO clone = (MatriculaAlunoHorarioTurmaVO) obj.getClone(false);
            clone.setCodigo(obj.getCodigo());
            listaM.add(clone);
        }
        return listaM;
    }

    /**
     * Responsável por
     *
     * <AUTHOR> Alcides 23/11/2012
     */
    private void movPagamentos(StringBuilder sqlPagamentosDaPessoa) {
//        sqlPagamentosDaPessoa.append("select movpagamento from pagamentomovparcela  \n");
//        sqlPagamentosDaPessoa.append("where movpagamento in((select codigo from movpagamento where %s and formapagamento not in \n");
//        sqlPagamentosDaPessoa.append("(select codigo from formapagamento where tipoformapagamento in ('CH', 'CA')))) \n");
//        sqlPagamentosDaPessoa.append("or movpagamento in((select codigo from movpagamento where formapagamento in \n");
//        sqlPagamentosDaPessoa.append("(select codigo from formapagamento where tipoformapagamento in ('CH', 'CA')) \n");
//        sqlPagamentosDaPessoa.append("and ((codigo in (select movpagamento from cheque where situacao != 'CA' and %s)) \n");
//        sqlPagamentosDaPessoa.append("or (codigo in (select movpagamento from cartaocredito where situacao != 'CA' and %s))))) \n");

        sqlPagamentosDaPessoa.append("select mpag.codigo\n");
        sqlPagamentosDaPessoa.append("from pagamentomovparcela pmp\n");
        sqlPagamentosDaPessoa.append("left join movpagamento mpag on pmp.movpagamento = mpag.codigo\n");
        sqlPagamentosDaPessoa.append("left join formapagamento fp on mpag.formapagamento = fp.codigo \n");
        sqlPagamentosDaPessoa.append("left join cheque ch on ch.movpagamento = mpag.codigo and ch.situacao != 'CA'\n");
        sqlPagamentosDaPessoa.append("left join cartaocredito ca on ca.movpagamento = mpag.codigo and ca.situacao != 'CA'\n");
        sqlPagamentosDaPessoa.append("where ((fp.tipoformapagamento not in ('CH', 'CA') and %s)\n");
        sqlPagamentosDaPessoa.append("or (fp.tipoformapagamento = 'CA'and %s)\n");
        sqlPagamentosDaPessoa.append("or (fp.tipoformapagamento = 'CH'and %s))");
    }

    private class CalculoModalidadeMatriculas {

        private ContratoModalidadePercentual modalidadePercentual;
        private List<MatriculaAlunoHorarioTurmaVO> matriculas;

        private CalculoModalidadeMatriculas(ContratoModalidadePercentual modalidadePercentual, List<MatriculaAlunoHorarioTurmaVO> matriculas) {
            this.matriculas = matriculas;
            this.modalidadePercentual = modalidadePercentual;
        }
    }

    private class CalculoModalidadeMatriculasDias {

        private Date inicio;
        private Date fim;
        private double proporcaoDias = 0.0;
        private List<Integer> matriculas = new ArrayList<Integer>();

        private CalculoModalidadeMatriculasDias() {
        }
    }

    private List<CalculoModalidadeMatriculasDias> montarDiasCalculo(Date inicio, Date fim,
            List<MatriculaAlunoHorarioTurmaVO> matriculas,
            int nrDiasMes) {
        List<CalculoModalidadeMatriculasDias> calculos = new ArrayList<CalculoModalidadeMatriculasDias>();
        Ordenacao.ordenarLista(matriculas, "dataFim");
        CalculoModalidadeMatriculasDias calc = new CalculoModalidadeMatriculasDias();
//    	double nrDiasTotal = Uteis.nrDiasEntreDatasSemHoraZerada(Calendario.getDataComHora(inicio, "00:00:00"),
//                Calendario.getDataComHora(fim, "23:59:59"));
        double nrDiasTotal = 0.0;
        for (MatriculaAlunoHorarioTurmaVO matricula : matriculas) {

            calc = new CalculoModalidadeMatriculasDias();
            calc.inicio = Calendario.entre(matricula.getDataInicio(), inicio, fim)
                    ? matricula.getDataInicio() : inicio;

            calc.fim = Calendario.entre(matricula.getDataFim(), inicio, fim) ? matricula.getDataFim() : fim;

            nrDiasTotal = nrDiasTotal + (Uteis.nrDiasEntreDatas(calc.inicio, calc.fim) > 0
                    ? Uteis.nrDiasEntreDatas(calc.inicio, calc.fim) + 1 : 1);
//    			inicio = Uteis.somarDias(matricula.getDataFim(), 1);
            calculos.add(calc);
            calc.matriculas.add(matricula.getCodigo());
        }
        for (CalculoModalidadeMatriculasDias calculo : calculos) {
            long intervalo = Uteis.nrDiasEntreDatas(Calendario.getDataComHora(calculo.inicio, "00:00:00"),
                    Calendario.getDataComHora(calculo.fim, "23:59:59")) + 1;
            calculo.proporcaoDias = (nrDiasMes * intervalo) / nrDiasTotal;
        }
        return calculos;
    }
}

/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import java.io.File;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoTipoProdutoVO;
import relatorio.negocio.comuns.financeiro.ReciboPagamentoRelTO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

/**
 * <AUTHOR> Araujo
 */
public class FaturamentoSinteticoRel extends SuperRelatorio {

    protected ReciboPagamentoRelTO reciboPagamentoRelTO;
    protected ChequeVO chequeVO;
    protected Date dataInicio;
    protected Date dataTermino;
    protected String horaInicio;
    protected String horaTermino;
    protected List listaReciboPagamentoRelVOs;
    protected Boolean situacaoPagamentoDesbloqueado;
    protected Boolean situacaoPagamentoPagosBanco;
    protected Boolean situacaoPagamentoCancelados;
    protected String tipoVisualizacao;
    protected String ordenacao;
    protected Boolean mostraObservacaoreRecebimento;
    protected Boolean mostraContrato;
    protected Integer qtdPagamentoAV;
    protected Double valorPagamentoAV;
    protected Integer qtdPagamentoCA;
    protected Double valorPagamentoCA;
    protected Integer qtdPagamentoChAvista;
    protected Double valorPagamentoChAvista;
    protected Integer qtdPagamentoChPrazo;
    protected Double valorPagamentoChPrazo;
    protected Integer qtdPagamentoOutros;
    protected Double valorPagamentoOutros;
    private FaturamentoSinteticoTipoProdutoVO tipoProdutoVO;

    private List<FaturamentoSinteticoTipoProdutoVO> listaFaturamentoSinteticoTipoProdutoVO;

    public FaturamentoSinteticoRel() throws Exception {
        setIdEntidade("CaixaPorOperadorRel");
        inicializarParametros();
    }

    /**
     *Metodo que inicializar os Parametros para o Relatório. 
     */
    public void inicializarParametros() {
        setReciboPagamentoRelTO(new ReciboPagamentoRelTO());
        setChequeVO(new ChequeVO());
        setListaReciboPagamentoRelVOs(new ArrayList());
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(negocio.comuns.utilitarias.Calendario.hoje());
        setHoraInicio("");
        setHoraTermino("");
        setSituacaoPagamentoCancelados(false);
        setSituacaoPagamentoDesbloqueado(false);
        setSituacaoPagamentoPagosBanco(false);
        setMostraObservacaoreRecebimento(false);
        setMostraContrato(new Boolean(false));
        setQtdPagamentoAV(new Integer(0));
        setValorPagamentoAV(new Double(0.0));
        setQtdPagamentoCA(new Integer(0));
        setQtdPagamentoChAvista(new Integer(0));
        setQtdPagamentoChPrazo(new Integer(0));
        setQtdPagamentoOutros(new Integer(0));
        setValorPagamentoCA(new Double(0.0));
        setValorPagamentoChAvista(new Double(0.0));
        setValorPagamentoChPrazo(new Double(0.0));
        setValorPagamentoOutros(new Double(0.0));
        setTipoProdutoVO(new FaturamentoSinteticoTipoProdutoVO());
        setTipoVisualizacao("");
        setOrdenacao("NR");
        listaFaturamentoSinteticoTipoProdutoVO = new ArrayList<FaturamentoSinteticoTipoProdutoVO>();
    }

    /**
     * Valida os campos obrigatórios Para a emissão do relatório
     * @throws negocio.comuns.utilitarias.ConsistirException Caso algum campo esteja faltando é mostrado o erro.
     */
    public void validarDados() throws ConsistirException {
        if (getDataInicio() == null) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro a Período De Início da Pesquisa.");
        }
        if (getDataTermino() == null) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro a Período de Termino da Pesquisa");
        }
        if ((getHoraInicio() != null && getHoraTermino() != null) &&
                (getDataInicio() == null && getDataTermino() == null)) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro a Período De e Período Até.");
        }
        if (tipoVisualizacao == null || tipoVisualizacao.equals("")) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro o Tipo de Visualização.");
        }
    }

    /**
     * Metodo que Realiza a consulta dos Dados para Gerar o Relatório do Caixa Por Operador
     * @ retorna a Lista com os pagamento realizado de acordo com os filtros pedidos.
     * @throws java.lang.Exception caso haja um erro na consulta do sql e passado o erro para ser tratado.
     */
    public List consultarRecibosCaixaPorOperador(UsuarioVO usuarioLogado) throws Exception {
        List lista = new ArrayList();
        try {
            validarDados();
            inicializar();
            if (tipoVisualizacao.equals("AN")) {
                String sqlSelect = "SELECT ReciboPagamento.codigo, " +
                        "ReciboPagamento.valortotal," +
                        "ReciboPagamento.pessoapagador," +
                        "ReciboPagamento.responsavellancamento, " +
                        "ReciboPagamento.contrato," +
                        "ReciboPagamento.data," +
                        "ReciboPagamento.nomepessoapagador" +
                        " FROM  ReciboPagamento , Usuario WHERE ";
                sqlSelect = montarFiltro(sqlSelect, usuarioLogado);
                sqlSelect = sqlSelect + " and recibopagamento.responsavelLancamento =  usuario.codigo ";
                sqlSelect = OrdenacaoRelatorio(sqlSelect);
                PreparedStatement sqlConsultar = con.prepareStatement(sqlSelect);
                ResultSet tabelaResultado = sqlConsultar.executeQuery();
                return (ReciboPagamento.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
            } else {
                return lista;
            }
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     *Metodo que Valida se existe algum Filtro para o relatório 
     * @param sqlSelect String já inicilizada com comandos Sql para consulta do relatório
     * @return retorna sqlSelect Para continuar a montar o restante do comando para fazer a consulta.
     */
    public String montarFiltro(String sqlSelect, UsuarioVO usuarioLogado) throws Exception {
        if ((getDataInicio() != null && getDataTermino() != null) && (getHoraInicio().equals("") && getHoraTermino().equals(""))) {
            sqlSelect = sqlSelect + "ReciboPagamento.data >= '" + Uteis.getDataJDBC(getDataInicio()) + " 00:00' and ReciboPagamento.data <= '" + Uteis.getDataJDBC(getDataTermino()) + " 23:59'";
        }
        if ((!getHoraInicio().equals("") && !getHoraTermino().equals("")) &&
                (getDataInicio() != null && getDataTermino() != null)) {
            sqlSelect = sqlSelect + "ReciboPagamento.data >= '" + Uteis.getDataJDBC(getDataInicio()) + " " + getHoraInicio() + "' and ReciboPagamento.data <= '" + Uteis.getDataJDBC(getDataTermino()) + " " + getHoraTermino() + "'";
        }
        if (getReciboPagamentoRelTO().getNomeOperador() != null) {
            sqlSelect = sqlSelect + " and upper( usuario.nome ) like('" + getReciboPagamentoRelTO().getNomeOperador().toUpperCase() + "%')";
        }

        return sqlSelect;
    }

    /**
     *Metodo que Valida qual será a Ordenacão do relatório 
     * @param sqlSelect String já inicilizada com comandos Sql para consulta do relatório
     * @return retorna sqlSelect Para continuar a montar o restante do comando para fazer a consulta.
     */
    public String OrdenacaoRelatorio(String sqlSelect) {
        if (ordenacao.equals("NA")) {
            sqlSelect = sqlSelect + "group by " +
                    "ReciboPagamento.codigo, " +
                    "ReciboPagamento.valortotal," +
                    "ReciboPagamento.pessoapagador," +
                    "ReciboPagamento.responsavellancamento, " +
                    "ReciboPagamento.contrato," +
                    "ReciboPagamento.data," +
                    "usuario.nome," +
                    "ReciboPagamento.nomepessoapagador" +
                    " Order By usuario.nome, ReciboPagamento.nomepessoapagador";
        }
        if (ordenacao.equals("NR")) {
            sqlSelect = sqlSelect + "group by " +
                    "ReciboPagamento.codigo, " +
                    "ReciboPagamento.valortotal," +
                    "ReciboPagamento.pessoapagador," +
                    "ReciboPagamento.responsavellancamento, " +
                    "ReciboPagamento.contrato," +
                    "ReciboPagamento.data," +
                    "usuario.nome," +
                    "ReciboPagamento.nomepessoapagador" +
                    " Order By usuario.nome, reciboPagamento.codigo";
        }
        return sqlSelect;
    }

    /**
     * Metodo responsável por montar os dados do Objeto MovPagamentoRelVO e adicionar o Objeto na listaMovPagamentoRelVOs
     * @throws java.lang.Exception caso haja erro sera tratado.
     */
    public List montarDadosReciboPagamentoRelVO(UsuarioVO usuarioLogado) throws Exception {
        try {
            List resultadoConsulta = consultarRecibosCaixaPorOperador(usuarioLogado);
            Iterator i = resultadoConsulta.iterator();
            while (i.hasNext()) {
                ReciboPagamentoVO reciboPag = (ReciboPagamentoVO) i.next();
                carregarDadosReciboPagamentoRelVO(reciboPag);
                adicionarObjReciboPagamentoRelVOs(getReciboPagamentoRelTO());
                this.setReciboPagamentoRelTO(new ReciboPagamentoRelTO());
            }
            contarMarcadoresRelatorio();
            return getListaReciboPagamentoRelVOs();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Metodo responsável por inicializar os dados do Classe MovPagamentoRelVO
     * @param movPagamento Obejto do tipo MovPagamentoVO  que foi consultado.
     * @throws java.lang.Exception caso haja erro sera tratado.
     */
    public void carregarDadosReciboPagamentoRelVO(ReciboPagamentoVO reciboPag) throws Exception {
        try {
            getReciboPagamentoRelTO().setReciboPagamentoVO(reciboPag);
            getReciboPagamentoRelTO().setNomeOperador(reciboPag.getResponsavelLancamento().getNome());
            ClienteVO clienteVO = getFacade().getCliente().consultarPorCodigoPessoa(reciboPag.getPessoaPagador().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            getReciboPagamentoRelTO().setMatricula(clienteVO.getMatricula());
            if (getMostraContrato()) {
                getReciboPagamentoRelTO().setMostrarNumeroContrato(true);
                getReciboPagamentoRelTO().setNumeroContrato(reciboPag.getContrato().getCodigo().toString());
            }

            montarDadosPagamentoMovParcela();
            montarDadosMovProdutoParcela();
        } catch (Exception e) {
            throw e;
        }

    }

    public void montarDadosPagamentoMovParcela() throws Exception {
        List resultadoConsulta = consultarPagamento();
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            PagamentoMovParcelaVO obj = (PagamentoMovParcelaVO) i.next();
            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO = new MovPagamento().consultarPorChavePrimaria(obj.getMovPagamento().intValue(), Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR);
            adicionarObjMovPagamentoVOs(movPagamentoVO);
        }
    }

    public List consultarPagamento() throws Exception {
        try {
            List objs = new PagamentoMovParcela().consultarPorReciboPagamneto(getReciboPagamentoRelTO().getReciboPagamentoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return objs;
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDadosMovProdutoParcela() throws Exception {
        List resultadoConsulta = consultarMovProduto();
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            MovProdutoParcelaVO obj = (MovProdutoParcelaVO) i.next();
            MovProdutoVO movProdutoVO = new MovProdutoVO();
            movProdutoVO = new MovProduto().consultarPorChavePrimaria(obj.getMovProduto().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            movProdutoVO.setValorPagoMovProdutoParcela(obj.getValorPago());
            getReciboPagamentoRelTO().getListaMovProdutoVOs().add(movProdutoVO);
        }
    }

    public List consultarMovProduto() throws Exception {
        try {
            List objs = new MovProdutoParcela().consultarPorReciboPagamneto(getReciboPagamentoRelTO().getReciboPagamentoVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return objs;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>MovPagamentoRelVO</code>
     * ao List <code>listaMovPagamentoRelVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>MovPagamentoRelVO</code> - getMovPagamentoRelVO().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>MovPagamentoRelVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjReciboPagamentoRelVOs(ReciboPagamentoRelTO obj) throws Exception {
        int index = 0;
        Iterator i = getListaReciboPagamentoRelVOs().iterator();
        while (i.hasNext()) {
            ReciboPagamentoRelTO objExistente = (ReciboPagamentoRelTO) i.next();
            if (objExistente.getReciboPagamentoVO().getCodigo().equals(obj.getReciboPagamentoVO().getCodigo().intValue())) {
                getListaReciboPagamentoRelVOs().set(index, obj);
                return;
            }
            index++;
        }
        getListaReciboPagamentoRelVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>MovPagamentoRelVO</code>
     * ao List <code>listaMovPagamentoRelVOs</code>. Utiliza o atributo padrão de consulta 
     * da classe <code>MovPagamentoRelVO</code> - getMovPagamentoRelVO().getCodigo() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>MovPagamentoRelVO</code> que será adiocionado ao Hashtable correspondente.
     */
    public void adicionarObjMovPagamentoVOs(MovPagamentoVO obj) throws Exception {
        int index = 0;
        Iterator i = getReciboPagamentoRelTO().getListaMovPagamentoVOs().iterator();
        while (i.hasNext()) {
            MovPagamentoVO objExistente = (MovPagamentoVO) i.next();
            if (objExistente.getCodigo().equals(obj.getCodigo().intValue())) {
                getReciboPagamentoRelTO().getListaMovPagamentoVOs().set(index, obj);
                return;
            }
            index++;
        }
        getReciboPagamentoRelTO().getListaMovPagamentoVOs().add(obj);
        //adicionarObjSubordinadoOC
    }

//    /**
//     * Operação responsável por excluir um objeto da classe <code>MovPagamentoRelVO</code>
//     * no List <code>MovPagamentoRelVO</code>. Utiliza o atributo padrão de consulta 
//     * da classe <code>MovPagamentoRelVO</code> - getMovPagamentoRelVO.getCodigo() - como identificador (key) do objeto no List.
//     * @param grupo  Parâmetro para localizar e remover o objeto do List.
//     */
//    public void excluirObjMovPagamentoRelVOs(Integer codigo) throws Exception {
//        int index = 0;
//        Iterator i = getListaMovPagamentoRelVOs().iterator();
//        while (i.hasNext()) {
//            MovPagamentoRelVO objExistente = (MovPagamentoRelVO) i.next();
//            if (objExistente.getCodigo().equals(codigo.intValue())) {
//                getListaMovPagamentoRelVOs().remove(index);
//                return;
//            }
//            index++;
//        }
//    //excluirObjSubordinadoOC
//    }
    public void contarMarcadoresRelatorio() throws Exception {
        setQtdPagamentoAV(new Integer(0));
        setValorPagamentoAV(new Double(0.0));
        setQtdPagamentoCA(new Integer(0));
        setValorPagamentoCA(new Double(0.0));
        setQtdPagamentoChAvista(new Integer(0));
        setQtdPagamentoChPrazo(new Integer(0));
        setQtdPagamentoOutros(new Integer(0));
        setValorPagamentoChAvista(new Double(0.0));
        setValorPagamentoChPrazo(new Double(0.0));
        setValorPagamentoOutros(new Double(0.0));
        Iterator i = getListaReciboPagamentoRelVOs().iterator();
        while (i.hasNext()) {
            ReciboPagamentoRelTO movParRel = (ReciboPagamentoRelTO) i.next();
            somarPagamento(movParRel);
        }
    }

    public void somarPagamento(ReciboPagamentoRelTO movParRel) throws Exception {
        Iterator i = movParRel.getListaMovPagamentoVOs().iterator();
        while (i.hasNext()) {
            MovPagamentoVO movPag = (MovPagamentoVO) i.next();
            if (movPag.getFormaPagamento().getTipoFormaPagamento().equals("AV")) {
                setQtdPagamentoAV(getQtdPagamentoAV() + 1);
                setValorPagamentoAV(Uteis.arredondarForcando2CasasDecimais(getValorPagamentoAV() + movPag.getValor()));
            }
            if (movPag.getFormaPagamento().getTipoFormaPagamento().equals("CA") || movPag.getFormaPagamento().getTipoFormaPagamento().equals("CD")) {
                setQtdPagamentoCA(getQtdPagamentoCA() + 1);
                setValorPagamentoCA(Uteis.arredondarForcando2CasasDecimais(getValorPagamentoCA() + movPag.getValor()));
            }
            if (movPag.getFormaPagamento().getTipoFormaPagamento().equals("CC")) {
                setQtdPagamentoOutros(getQtdPagamentoOutros() + 1);
                setValorPagamentoOutros(Uteis.arredondarForcando2CasasDecimais(getValorPagamentoOutros() + movPag.getValor()));
            }

            if (movPag.getFormaPagamento().getTipoFormaPagamento().equals("CH")) {
                consultarPagamentoCheque(movPag);
            }
        }
    }

    public void consultarPagamentoCheque(MovPagamentoVO movPag) throws Exception {

        movPag.setChequeVOs(new Cheque().consultarPagamentoCheques(movPag.getCodigo().intValue(),null,false,false,false, Uteis.NIVELMONTARDADOS_TODOS));
        Iterator i = movPag.getChequeVOs().iterator();
        while (i.hasNext()) {
            ChequeVO cheque = (ChequeVO) i.next();
            if (cheque.getVistaOuPrazo().equals("AV")) {
                setQtdPagamentoChAvista(getQtdPagamentoChAvista() + 1);
                setValorPagamentoChAvista(Uteis.arredondarForcando2CasasDecimais(getValorPagamentoChAvista() + cheque.getValor()));
            }
            if (cheque.getVistaOuPrazo().equals("PR")) {
                setQtdPagamentoChPrazo(getQtdPagamentoChPrazo() + 1);
                setValorPagamentoChPrazo(Uteis.arredondarForcando2CasasDecimais(getValorPagamentoChPrazo() + cheque.getValor()));
            }
        }
    }

    /**
     * Operação reponsável por retornar o arquivo (caminho e nome) correspondente ao
     * design do relatório criado pelo IReport.
     */
    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + getIdEntidade() + ".jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public String getHoraTermino() {
        return horaTermino;
    }

    public void setHoraTermino(String horaTermino) {
        this.horaTermino = horaTermino;
    }

    public Boolean getMostraObservacaoreRecebimento() {
        return mostraObservacaoreRecebimento;
    }

    public void setMostraObservacaoreRecebimento(Boolean mostraObservacaoreRecebimento) {
        this.mostraObservacaoreRecebimento = mostraObservacaoreRecebimento;
    }

    public Boolean getMostraContrato() {
        return mostraContrato;
    }

    public void setMostraContrato(Boolean mostraContrato) {
        this.mostraContrato = mostraContrato;
    }

    public Boolean getSituacaoPagamentoCancelados() {
        return situacaoPagamentoCancelados;
    }

    public void setSituacaoPagamentoCancelados(Boolean situacaoPagamentoCancelados) {
        this.situacaoPagamentoCancelados = situacaoPagamentoCancelados;
    }

    public Boolean getSituacaoPagamentoDesbloqueado() {
        return situacaoPagamentoDesbloqueado;
    }

    public void setSituacaoPagamentoDesbloqueado(Boolean situacaoPagamentoDesbloqueado) {
        this.situacaoPagamentoDesbloqueado = situacaoPagamentoDesbloqueado;
    }

    public Boolean getSituacaoPagamentoPagosBanco() {
        return situacaoPagamentoPagosBanco;
    }

    public void setSituacaoPagamentoPagosBanco(Boolean situacaoPagamentoPagosBanco) {
        this.situacaoPagamentoPagosBanco = situacaoPagamentoPagosBanco;
    }

    public String getTipoVisualizacao() {
        return tipoVisualizacao;
    }

    public void setTipoVisualizacao(String tipoVisualizacao) {
        this.tipoVisualizacao = tipoVisualizacao;
    }

    public List getListaReciboPagamentoRelVOs() {
        return listaReciboPagamentoRelVOs;
    }

    public void setListaReciboPagamentoRelVOs(List listaReciboPagamentoRelVOs) {
        this.listaReciboPagamentoRelVOs = listaReciboPagamentoRelVOs;
    }

    public Integer getQtdPagamentoAV() {
        return qtdPagamentoAV;
    }

    public void setQtdPagamentoAV(Integer qtdPagamentoAV) {
        this.qtdPagamentoAV = qtdPagamentoAV;
    }

    public Integer getQtdPagamentoCA() {
        return qtdPagamentoCA;
    }

    public void setQtdPagamentoCA(Integer qtdPagamentoCA) {
        this.qtdPagamentoCA = qtdPagamentoCA;
    }

    public Double getValorPagamentoAV() {
        return valorPagamentoAV;
    }

    public void setValorPagamentoAV(Double valorPagamentoAV) {
        this.valorPagamentoAV = valorPagamentoAV;
    }

    public Double getValorPagamentoCA() {
        return valorPagamentoCA;
    }

    public void setValorPagamentoCA(Double valorPagamentoCA) {
        this.valorPagamentoCA = valorPagamentoCA;
    }

    public Integer getQtdPagamentoChAvista() {
        return qtdPagamentoChAvista;
    }

    public void setQtdPagamentoChAvista(Integer qtdPagamentoChAvista) {
        this.qtdPagamentoChAvista = qtdPagamentoChAvista;
    }

    public Integer getQtdPagamentoChPrazo() {
        return qtdPagamentoChPrazo;
    }

    public void setQtdPagamentoChPrazo(Integer qtdPagamentoChPrazo) {
        this.qtdPagamentoChPrazo = qtdPagamentoChPrazo;
    }

    public Double getValorPagamentoChAvista() {
        return valorPagamentoChAvista;
    }

    public void setValorPagamentoChAvista(Double valorPagamentoChAvista) {
        this.valorPagamentoChAvista = valorPagamentoChAvista;
    }

    public Double getValorPagamentoChPrazo() {
        return valorPagamentoChPrazo;
    }

    public void setValorPagamentoChPrazo(Double valorPagamentoChPrazo) {
        this.valorPagamentoChPrazo = valorPagamentoChPrazo;
    }

    public Integer getQtdPagamentoOutros() {
        return qtdPagamentoOutros;
    }

    public void setQtdPagamentoOutros(Integer qtdPagamentoOutros) {
        this.qtdPagamentoOutros = qtdPagamentoOutros;
    }

    public Double getValorPagamentoOutros() {
        return valorPagamentoOutros;
    }

    public void setValorPagamentoOutros(Double valorPagamentoOutros) {
        this.valorPagamentoOutros = valorPagamentoOutros;
    }

    public String getTipoVisualizacao_Apresentar() {
        if (tipoVisualizacao == null) {
            return "";
        }
        if (tipoVisualizacao.equals("SI")) {
            return "Sintético";
        }
        if (tipoVisualizacao.equals("AN")) {
            return "Analítico";
        }
        return (tipoVisualizacao);
    }

    public String getOrdenacao() {
        return ordenacao;
    }

    public String getOrdenacao_Apresentar() {
        if (ordenacao == null) {
            return "";
        }
        if (ordenacao.equals("NR")) {
            return "Número Recibo";
        }
        if (ordenacao.equals("NA")) {
            return "Nome do Aluno";
        }
        return (ordenacao);
    }

    public void setOrdenacao(String ordenacao) {
        this.ordenacao = ordenacao;
    }

    public ReciboPagamentoRelTO getReciboPagamentoRelTO() {
        return reciboPagamentoRelTO;
    }

    public void setReciboPagamentoRelTO(ReciboPagamentoRelTO reciboPagamentoRelTO) {
        this.reciboPagamentoRelTO = reciboPagamentoRelTO;
    }

    public ChequeVO getChequeVO() {
        return chequeVO;
    }

    public void setChequeVO(ChequeVO chequeVO) {
        this.chequeVO = chequeVO;
    }

    public FaturamentoSinteticoTipoProdutoVO getTipoProdutoVO() {
        return tipoProdutoVO;
    }

    public void setTipoProdutoVO(FaturamentoSinteticoTipoProdutoVO tipoProdutoVO) {
        this.tipoProdutoVO = tipoProdutoVO;
    }

    public List<FaturamentoSinteticoTipoProdutoVO> getListaFaturamentoSinteticoTipoProdutoVO() {
        return listaFaturamentoSinteticoTipoProdutoVO;
    }

    public void setListaFaturamentoSinteticoTipoProdutoVO(List<FaturamentoSinteticoTipoProdutoVO> listaFaturamentoSinteticoTipoProdutoVO) {
        this.listaFaturamentoSinteticoTipoProdutoVO = listaFaturamentoSinteticoTipoProdutoVO;
    }
}
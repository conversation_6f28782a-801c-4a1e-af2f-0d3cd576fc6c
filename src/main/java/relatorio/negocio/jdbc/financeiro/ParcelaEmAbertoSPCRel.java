package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.integracao.pactopay.dto.EnderecoDTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.plano.Horario;
import negocio.facade.jdbc.plano.Plano;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

import javax.faces.model.SelectItem;
import java.io.File;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ParcelaEmAbertoSPCRel extends SuperRelatorio {

    private Date dataInicioVencimento;
    private Date dataTerminoVencimento;
    private String situacao;
    private String situacaoSPC;
    private List<SelectItem> situacoesSelecionadas;
    private String periodo;
    private String parcelaCancelada = "";
    private ColaboradorVO colaboradorVO;
    private ClienteVO clienteVO;
    private Integer totalParcelaEmAberto;
    private Double valorTotalParcelaEmAberto;
    private Integer totalParcelaPaga;
    private Double valorTotalParcelaPaga;
    private Integer totalParcelacCancelada;
    private Double valorTotalParcelaCancelada;
    //por padrão escolher parcelas de recorrência e normais
    private int parcelasRecorrencia = 1;
    private String situacaoCliente = "";
    private ConvenioCobrancaVO convenio = new ConvenioCobrancaVO();
    private Integer codigoPlano;
    private List<SelectItem> planosSelecionados;
    private Integer codigoHorario;
    private List<SelectItem> horariosSelecionados;
    private Integer totalMatriculas = 0;
    private String idRelatorio = "ParcelaEmAbertoRel";
    private Double totalJuroeMulta = 0.0d;
    private Date dataInicioFaturamento;
    private Date dataTerminoFaturamento;
    private Date dataInicioPagamento;
    private Date dataTerminoPagamento;
    private Date dataInicioSpc;
    private Date dataTerminoSpc;
    private Integer formaPagamentoSelecionado = 0;
    private boolean somenteParcelasContratosAssinados;

    private boolean apresentarDadosSensiveis = false;

    public ParcelaEmAbertoSPCRel() throws Exception {
        inicializarDados();

    }

    private void inicializarDados() {
        setDataInicioVencimento(Calendario.hoje());
        setDataTerminoVencimento(Calendario.hoje());
        setDataInicioFaturamento(null);
        setDataTerminoFaturamento(null);
        setDataInicioPagamento(null);
        setDataTerminoPagamento(null);
        setDataInicioSpc(null);
        setDataTerminoSpc(null);
        setSituacao("");
        setColaboradorVO(new ColaboradorVO());
        setClienteVO(new ClienteVO());
        setPeriodo("");
        setTotalParcelaEmAberto(0);
        setValorTotalParcelaEmAberto(0.0);
        setTotalParcelaPaga(0);
        setValorTotalParcelaPaga(0.0);
        setTotalParcelacCancelada(0);
        setValorTotalParcelaCancelada(0.0);
    }

    public void validarDados() throws Exception {
        if ((getDataInicioVencimento() == null || getDataTerminoVencimento() == null) &&
                (getDataInicioFaturamento() == null || getDataTerminoFaturamento() == null)
                && (getDataInicioPagamento() == null || getDataTerminoPagamento() == null)) {
            throw new Exception("Ao menos um dos periodos de pesquisa(Vencimento, Faturamento ou Pagamento) deve ter as data de inicio e fim  informadas");
        }

        if (Calendario.menor(getDataTerminoSpc(), getDataInicioSpc())) {
            throw new Exception("A DATA DE TÉRMINO do PERÍODO DE PESQUISA por SPC não deve ser menor que a DATA DE INÍCIO");
        }
        if (Calendario.menor(getDataTerminoVencimento(), getDataInicioVencimento())) {
            throw new Exception("A DATA DE TÉRMINO do PERÍODO DE PESQUISA por vencimento não deve ser menor que a DATA DE INÍCIO");
        }
        if (Calendario.menor(getDataTerminoFaturamento(), getDataInicioFaturamento())) {
            throw new Exception("A DATA DE TÉRMINO do PERÍODO DE PESQUISA por faturamento não deve ser menor que a DATA DE INÍCIO");
        }
        if (Calendario.menor(getDataTerminoPagamento(), getDataInicioPagamento())) {
            throw new Exception("A DATA DE TÉRMINO do PERÍODO DE PESQUISA por pagamento não deve ser menor que a DATA DE INÍCIO");
        }
    }

    public String consultarEmpresaPorNome(Integer codigo) throws Exception {
        EmpresaVO empresa = new Empresa().consultarPorChavePrimaria(codigo, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        return empresa.getNome();
    }

    public String consultarPlanoPorCodigo(Integer codigo) throws Exception {
        Plano planoDao = new Plano(con);
        PlanoVO planoVO = planoDao.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_MINIMOS);
        planoDao = null;
        return planoVO.getDescricao();
    }

    public String consultarHorarioPorCodigo(Integer codigo) throws Exception {
        Horario horarioDao = new Horario(con);
        HorarioVO horarioVO = horarioDao.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_MINIMOS);
        horarioDao = null;
        return horarioVO.getDescricao();
    }

    public String emitirRelatorio(Integer empresa, List<ParcelaSPCTO> pc) throws Exception {
        emitirRelatorio("ParcelaEmAbertoRel", true);
        ResultSet resultadoTotalizador = executarConsultaParametrizada(empresa);
        resultadoTotalizador.beforeFirst();
        converterResultadoConsultaGestaoNegativacaoParaXML(pc, "ParcelaEmAbertoRel", "registros");

        resultadoTotalizador.beforeFirst();
        obterTotalizadorGeral(resultadoTotalizador);

        resultadoTotalizador.beforeFirst();
        obterQuantidades(resultadoTotalizador);
        return getXmlRelatorio();
    }

    public ResultSet executarConsultaParametrizada(Integer empresa) throws Exception {
        inicializar();
        StringBuilder sql = new StringBuilder("SELECT distinct on (movparcela.codigo) movparcela.codigo as movparcela_codigo,\n");
        sql.append("oo.dataoperacao as datacancelamento, movparcela.pessoa as codigopessoa,");
        sql.append("cliente.codigo as codigoCliente, \n");
        sql.append("Case WHEN necp.contrato IS NOT NULL THEN cliev.matricula when ((vendaavulsa.colaborador is not null or controletaxapersonal.codigo is not null) and colaborador.codigo is not null) then 'cl-'||colaborador.codigo ");
        sql.append(" When (cliente.matricula is null or cliente.matricula = '') then  vendaavulsa.nomecomprador || ' sem matrícula' else cliente.matricula end as cliente_matricula, \n");
        sql.append("Case When (movparcela.vendaavulsa > 0 and (pessoa.nome is null or pessoa.nome = '')) THEN vendaavulsa.nomecomprador WHEN necp.contrato IS NOT NULL THEN pesev.nome ELSE pessoa.nome END as pessoa_nome \n");
        sql.append(", movparcela.descricao as parcela_descricao, \n");
        sql.append("movparcela.dataregistro as parcela_dataregistro, \n");
        sql.append("movparcela.datavencimento as parcela_datavencimento, \n");
        sql.append("movparcela.situacao as parcela_situacao, \n");
        sql.append("movparcela.regimerecorrencia as regime_recorrencia, \n");
        sql.append("movparcela.contrato as parcela_contrato, \n");
        sql.append("movparcela.vendaavulsa as parcela_venda_avulsa,\n");
        sql.append("movparcela.valorparcela as parcela_valorparcela, 1 AS total, \n");
        sql.append("rp.data as datapagamento, \n");
        sql.append("array_to_string(array(SELECT email FROM email WHERE email.pessoa = movparcela.pessoa), ',') AS email,  \n");
        sql.append("array_to_string(array_agg(distinct fp.descricao), ',') AS formaspagamento,  \n");
        sql.append("array_to_string(array_agg(distinct mod.nome), ',') AS modalidades,  \n");
        sql.append("array_to_string(array(SELECT numero FROM telefone WHERE telefone.pessoa = movparcela.pessoa), ',') AS telefone, \n");
        sql.append("emp.nome as nomeEmpresa, \n");
        sql.append("(select nrtentativas from movparcelaresultadocobranca mr where mr.movparcela = movparcela.codigo order by mr.codigo desc limit 1) as nrtentativas, \n");
        sql.append("(select motivoretorno from movparcelaresultadocobranca mr where mr.movparcela = movparcela.codigo order by mr.codigo desc limit 1) as motivoretorno, \n");
        sql.append("array_to_string((select array_agg(descricao) from conveniocobranca cc INNER JOIN autorizacaocobrancacliente acc ON acc.conveniocobranca = cc.codigo AND acc.ativa WHERE acc.cliente = cliente.codigo), ',') as convenios  \n");

        if (getSituacaoCliente() != null && !getSituacaoCliente().isEmpty()) {
            sql.append(", st.situacao as situacao_cliente, st.situacaoContrato as situacao_contrato\n");
        }
        sql.append(", pessoa.cfp as cpf\n");
        sql.append(", pessoa.rg as rg\n");
        sql.append(", pessoa.rguf as rguf\n");
        sql.append(", pessoa.cpfpai\n");
        sql.append(", pessoa.nomepai\n");
        sql.append(", pessoa.cpfmae\n");
        sql.append(", pessoa.nomemae\n");
        sql.append(", pessoa.cpfcnpjterceiro\n");
        sql.append(", pessoa.nometerceiro\n");
        sql.append(", pessoa.emitirnometerceiro\n");
        sql.append(", pessoa.datanasc\n");
        sql.append(", endereco.*\n");
        sql.append(", plano.descricao as nomeplano\n");
        sql.append(", movparcela.incluidaspc\n");
        sql.append(", movparcela.situacaospc\n");
        sql.append(", movparcela.jsonspc\n");
        sql.append("FROM movparcela\n");
        sql.append("LEFT JOIN observacaooperacao oo ON oo.movparcela = movparcela.codigo AND oo.tipooperacao = 'PC'\n");
        sql.append("left join empresa emp on emp.codigo = movparcela.empresa \n");
        sql.append("LEFT JOIN pessoa ON pessoa.codigo = movparcela.pessoa \n");
        sql.append("LEFT JOIN cliente ON cliente.pessoa = pessoa.codigo \n");
        sql.append("LEFT JOIN colaborador ON colaborador.pessoa = pessoa.codigo \n");
        sql.append("LEFT JOIN contrato ON movparcela.contrato = contrato.codigo\n");
        sql.append("LEFT JOIN contratoassinaturadigital ON contratoassinaturadigital.contrato = contrato.codigo\n");
        sql.append("LEFT JOIN contratocondicaopagamento ccp on ccp.contrato = contrato.codigo\n");
        sql.append("LEFT JOIN condicaopagamento cp on cp.codigo = ccp.condicaopagamento\n");
        sql.append("LEFT JOIN contratomodalidade cmod on cmod.contrato = contrato.codigo\n");
        sql.append("LEFT JOIN modalidade mod on cmod.modalidade = mod.codigo\n");
        sql.append("LEFT JOIN aulaavulsadiaria ON aulaavulsadiaria.codigo = movparcela.aulaavulsadiaria\n");
        sql.append("LEFT JOIN vendaavulsa ON vendaavulsa.codigo = movparcela.vendaavulsa\n");
        sql.append("LEFT JOIN controletaxapersonal ON controletaxapersonal.codigo = movparcela.personal\n");
        sql.append("LEFT JOIN usuario ON usuario.codigo = movparcela.responsavel\n");
        if (getColaboradorVO().getCodigo() != 0) {
            sql.append("INNER JOIN colaborador as colResponsavel  ON colResponsavel.codigo = usuario.colaborador ");
        }
        sql.append("LEFT JOIN negociacaoeventocontratoparcelas necp ON necp.parcela = movparcela.codigo \n");
        sql.append("LEFT JOIN pessoa pesev ON pesev.codigo = movparcela.pessoa\n");
        sql.append("LEFT JOIN cliente cliev ON cliev.pessoa = pesev.codigo\n");
        sql.append("LEFT JOIN movprodutoparcela mpp ON mpp.movparcela = movparcela.codigo\n");
        sql.append("LEFT JOIN recibopagamento rp ON mpp.recibopagamento = rp.codigo\n");
        sql.append("LEFT JOIN pagamentomovparcela pmp ON movparcela.codigo = pmp.movparcela\n");
        sql.append("LEFT JOIN movpagamento mpag ON pmp.movpagamento = mpag.codigo\n");
        sql.append("LEFT JOIN formapagamento fp ON mpag.formapagamento = fp.codigo\n");

        if (getSituacaoCliente() != null && !getSituacaoCliente().isEmpty()) {
            sql.append("LEFT JOIN situacaoclientesinteticodw st ON st.codigocliente = cliev.codigo \n");
        }

        sql.append("LEFT JOIN remessaitem ri ON ri.movparcela = movparcela.codigo \n");
        sql.append("LEFT JOIN remessa re ON re.codigo = ri.remessa \n");

        sql.append("left JOIN contratohorario chor ON chor.contrato = movparcela.contrato \n");
        sql.append("LEFT JOIN plano on plano.codigo= contrato.plano \n");

        sql.append("LEFT JOIN (\n");
        sql.append("    SELECT pessoa, MAX(codigo) as max_codigo\n");
        sql.append("    FROM endereco\n");
        sql.append("    GROUP BY pessoa\n");
        sql.append(") AS ultimo_endereco ON ultimo_endereco.pessoa = pessoa.codigo\n");
        sql.append("LEFT JOIN endereco on ultimo_endereco.max_codigo = endereco.codigo\n");

        sql.append(" WHERE  1 = 1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" and movparcela.empresa = ").append(empresa).append(" \n");
        }

        if (getColaboradorVO().getPessoa().getCodigo() != 0) {
            sql.append(" and colResponsavel.pessoa = ").append(getColaboradorVO().getPessoa().getCodigo()).append(" \n");
        }

        if (getParcelaCancelada().equals("CANCELADA_APOS_VENCIMENTO")) {
            sql.append(" and (oo.dataoperacao >= movparcela.datavencimento or movparcela.situacao != 'CA')");
        } else if (getParcelaCancelada().equals("CANCELADA_ANTES_VENCIMENTO")) {
            sql.append(" and (oo.dataoperacao < movparcela.datavencimento or movparcela.situacao != 'CA')");
        }

        if (isSomenteParcelasContratosAssinados()) {
            sql.append(" and contratoassinaturadigital.codigo is not null");
        }

        String filtros = "";
        sql.append(montarFiltrosRelatorio(filtros));
        sql.append("\nGROUP BY pessoa.cfp, pessoa.rg, pessoa.rguf, pessoa.cpfpai, pessoa.cpfmae, pessoa.cpfcnpjterceiro, pessoa.datanasc,\n");
        sql.append("pessoa.nomepai, pessoa.nomemae, pessoa.nometerceiro, pessoa.emitirnometerceiro,\n");
        sql.append("rp.data, nrtentativas, motivoretorno, oo.dataoperacao, movparcela_codigo, cliente.codigo, movparcela.pessoa, cliente_matricula, pessoa_nome, parcela_descricao, parcela_dataregistro, ");
        sql.append("parcela_datavencimento, parcela_situacao, parcela_contrato, parcela_venda_avulsa, parcela_valorparcela, regime_recorrencia, datapagamento,emp.nome, plano.descricao,  \n");
        sql.append("endereco.pessoa, endereco.tipoendereco, endereco.cep, endereco.bairro, endereco.numero, endereco.complemento, endereco.endereco, endereco.enderecocorrespondencia, endereco.codigo\n");
        if (getSituacaoCliente() != null && !getSituacaoCliente().isEmpty()) {
            sql.append(", situacao_cliente ,situacao_contrato \n");
        }
        sql.append("ORDER BY movparcela.codigo, pessoa_nome \n");
        PreparedStatement sqlPS = con.prepareStatement(sql.toString(), ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultadoTotalizador = sqlPS.executeQuery();
        if (!resultadoTotalizador.next()) {
            throw new ConsistirException("Nenhum Registro Encontrado!");
        }
        return resultadoTotalizador;
    }

    public double calcularMulta(Double valorParcela) {
        if (getEmpresaLogado().isUtilizarMultaValorAbsoluto()) {
            return Uteis.arredondarForcando2CasasDecimais(getEmpresaLogado().getMultaCobrancaAutomatica());
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela * (getEmpresaLogado().getMultaCobrancaAutomatica() / 100));
        }
    }

    public double calcularJuros(Double valorParcela, Date dataVencimento, Date dataPagamento) {
        if (getEmpresaLogado().isUtilizarJurosValorAbsoluto()) {
            return Uteis.arredondarForcando2CasasDecimais((Uteis.nrDiasEntreDatas(dataVencimento, dataPagamento) * getEmpresaLogado().getJurosCobrancaAutomatica()));
        } else {
            return Uteis.arredondarForcando2CasasDecimais(valorParcela * ((Uteis.nrDiasEntreDatas(dataVencimento, dataPagamento)) * (getEmpresaLogado().getJurosCobrancaAutomatica() / 100)));
        }
    }

    public void obterTotalizadorGeral(ResultSet resultadoTotalizador) throws Exception {
        while (resultadoTotalizador.next()) {
            String si = resultadoTotalizador.getString("parcela_situacao");

            switch (si) {
                case "EA":
                    setValorTotalParcelaEmAberto(getValorTotalParcelaEmAberto() + resultadoTotalizador.getDouble("parcela_valorparcela"));
                    setTotalParcelaEmAberto(getTotalParcelaEmAberto() + 1);
                    break;
                case "PG":
                    setValorTotalParcelaPaga(getValorTotalParcelaPaga() + resultadoTotalizador.getDouble("parcela_valorparcela"));
                    setTotalParcelaPaga(getTotalParcelaPaga() + 1);
                    break;
                case "CA":
                    setValorTotalParcelaCancelada(getValorTotalParcelaCancelada() + resultadoTotalizador.getDouble("parcela_valorparcela"));
                    setTotalParcelacCancelada(getTotalParcelacCancelada() + 1);
                    break;
            }
        }
    }

    private void obterQuantidades(ResultSet resultadoTotalizador) throws SQLException {
        List<String> matriculas = new ArrayList<>();
        while (resultadoTotalizador.next()) {
            String matricula = resultadoTotalizador.getString("cliente_matricula");

            if (!matriculas.contains(matricula)) {
                matriculas.add(matricula);
            }
        }

        setTotalMatriculas(matriculas.size());
    }

    public boolean isApresentarDadosSensiveis() {
        return apresentarDadosSensiveis;
    }

    public void setApresentarDadosSensiveis(boolean apresentarDadosSensiveis) {
        this.apresentarDadosSensiveis = apresentarDadosSensiveis;
    }

    private String montarFiltrosRelatorio(String selectStr) throws Exception {
        String filtros = "";
        setDescricaoFiltros("");
        if (getDataInicioSpc() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.codigo in (SELECT chaveprimaria::integer FROM Log  WHERE operacao ilike 'NEGATIVAR PARCELA' AND dataalteracao >= '" + Uteis.getDataJDBC(getDataInicioSpc()) + " 00:00:00' ))", false);
            adicionarDescricaoFiltro(" Data início Negativação " + Uteis.getData(dataInicioSpc));
        }

        if (getDataTerminoSpc() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.codigo in (SELECT chaveprimaria::integer FROM Log  WHERE operacao ilike 'NEGATIVAR PARCELA' AND dataalteracao <= '" + Uteis.getDataJDBC(getDataTerminoSpc()) + " 23:59:59' ))", false);
            adicionarDescricaoFiltro(" Data fim Negativação " + Uteis.getData(dataTerminoSpc));
        }

        if (getDataInicioFaturamento() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.dataregistro >= '" + Uteis.getDataJDBC(getDataInicioFaturamento()) + " 00:00:00' )", false);
            adicionarDescricaoFiltro(" Data início Faturamento " + Uteis.getData(dataInicioFaturamento));
        }

        if (getDataTerminoFaturamento() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.dataregistro <= '" + Uteis.getDataJDBC(getDataTerminoFaturamento()) + " 23:59:59' )", false);
            adicionarDescricaoFiltro(" Data fim Faturamento " + Uteis.getData(dataTerminoFaturamento));
        }
        if (getDataInicioVencimento() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.datavencimento >= '" + Uteis.getDataJDBC(getDataInicioVencimento()) + " 00:00:00' )", false);
            adicionarDescricaoFiltro(" Data início Vencimento " + Uteis.getData(dataInicioVencimento));
        }

        if (getDataTerminoVencimento() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.datavencimento <= '" + Uteis.getDataJDBC(getDataTerminoVencimento()) + " 23:59:59' )", false);
            adicionarDescricaoFiltro(" Data fim Vencimento " + Uteis.getData(dataTerminoVencimento));
        }
        if (getDataInicioPagamento() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (rp.data >= '" + Uteis.getDataJDBC(getDataInicioPagamento()) + " 00:00:00' )", false);
            adicionarDescricaoFiltro(" Data início Recebimento " + Uteis.getData(dataInicioPagamento));
        }

        if (getDataTerminoPagamento() != null) {
            filtros += adicionarCondicionalWhere(filtros, " and (rp.data <= '" + Uteis.getDataJDBC(getDataTerminoPagamento()) + " 23:59:59' )", false);
            adicionarDescricaoFiltro(" Data fim Recebimento " + Uteis.getData(dataTerminoPagamento));
        }

        if (getColaboradorVO().getCodigo() != 0) {
            adicionarDescricaoFiltro(" Responsável " + getColaboradorVO().getPessoa().getNome());
        }
        if (getClienteVO().getCodigo() != 0) {
            filtros += adicionarCondicionalWhere(filtros, " and (cliente.codigo = " + getClienteVO().getCodigo() + ")", false);
            adicionarDescricaoFiltro(" Cliente " + getClienteVO().getPessoa().getNome());
        }
        if (UteisValidacao.notEmptyNumber(getFormaPagamentoSelecionado())) {
            filtros += adicionarCondicionalWhere(filtros, " and (fp.codigo = " + getFormaPagamentoSelecionado() + ")", false);
            adicionarDescricaoFiltro(" Forma Pagamento: " + getFormaPagamentoSelecionado());
        }
        String situacoesSelecionadas = getCodigoSituacoesSelecionadas();
        boolean selecionadoCombobox = getSituacoesSelecionadas().isEmpty() && !getSituacao().equals("");
        if (situacoesSelecionadas != null && !situacoesSelecionadas.equals("")) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.situacao IN (" + situacoesSelecionadas + "))", false);
            String situacao;
            if (selecionadoCombobox) {
                situacao = getSituacao_Apresentar();
            } else {
                situacao = getSituacoesSelecionadasApresentar().replaceAll("<br/>", ",");
                situacao = situacao.substring(0, situacao.length() - 1);
            }
            adicionarDescricaoFiltro(" Situações: " + situacao);
        }
//        filtros += adicionarCondicionalWhere(filtros, " and movparcela.situacao != 'RG'", false);
        //1-Parcelas normais e recorrencia
        //2-Parcelas recorrencia
        //3-Parcelas normais
        if (getParcelasRecorrencia() == 2) {
            filtros += adicionarCondicionalWhere(filtros, " and (movparcela.regimerecorrencia or movparcela.parceladcc or coalesce(cp.tipoconveniocobranca,0) > 0) ", false);
            adicionarDescricaoFiltro(" Regime Recorrência: Sim");
        } else if (getParcelasRecorrencia() == 3) {
            filtros += adicionarCondicionalWhere(filtros, " and (not (movparcela.regimerecorrencia or movparcela.parceladcc or coalesce(cp.tipoconveniocobranca,0) > 0)) ", false);
            adicionarDescricaoFiltro(" Regime Recorrência: Não");
        }

        int codConvenio = getConvenio().getCodigo();

        if (!UteisValidacao.emptyNumber(getConvenio().getCodigo())) {
            if (codConvenio == -1) {
                String filtro = " and movparcela.pessoa not in (select pessoa from cliente where codigo "
                        + " in (select cliente from autorizacaocobrancacliente where conveniocobranca is not null AND ativa = TRUE)) \n"
                        + " and movparcela.pessoa not in (select pessoa from contratorecorrencia where datainutilizada is null and ultimatransacaoaprovada <> '') ";
                filtros += adicionarCondicionalWhere(filtros, filtro, false);
                adicionarDescricaoFiltro(" Convênio de cobrança: Nenhum ");
            } else if (codConvenio != -2) {
                String filtro = " and movparcela.pessoa in (select pessoa from cliente where codigo "
                        + " in( select cliente from autorizacaocobrancacliente where conveniocobranca = " + codConvenio + " AND ativa = TRUE)) ";
                filtros += adicionarCondicionalWhere(filtros, filtro, false);
                adicionarDescricaoFiltro(" Convênio de cobrança: " + codConvenio);
            } else if (codConvenio == -2) {
                String filtro = " and movparcela.pessoa not in (select pessoa from cliente where codigo "
                        + " in (select cliente from autorizacaocobrancacliente where conveniocobranca is not null AND ativa = TRUE)) \n"
                        + " and movparcela.pessoa in (select pessoa from contratorecorrencia where datainutilizada is null and ultimatransacaoaprovada <> '') ";
                filtros += adicionarCondicionalWhere(filtros, filtro, false);
                adicionarDescricaoFiltro(" Convênio de cobrança: Transação ON-LINE ");
            }
        }

        filtros += adicionarCondicionalWhere(filtros, " and (re.codigo is null or (re.retorno is not null and re.retorno not like '') )", false);
        adicionarDescricaoFiltro(" Ignorar parcelas com remessa e sem retorno");

        String codigosPlanos = getCodigoPlanosSelecionados();
        selecionadoCombobox = getPlanosSelecionados().size() == 0 && getCodigoPlano() > 0;
        if (codigosPlanos != null && codigosPlanos.length() > 0 && !codigosPlanos.equals("9999")) {
            filtros += adicionarCondicionalWhere(filtros, " and (contrato.plano IN (" + codigosPlanos + "))", false);
            String plano;
            if (selecionadoCombobox) {
                plano = consultarPlanoPorCodigo(getCodigoPlano());
            } else {
                plano = getPlanosSelecionadosApresentar().replaceAll("<br/>", ",");
                plano = plano.substring(0, plano.length() - 1);
            }
            adicionarDescricaoFiltro(" Planos: " + plano);
        }

        String codigosHorarios = getCodigoHorariosSelecionados();
        selecionadoCombobox = getHorariosSelecionados().isEmpty() && getCodigoHorario() > 0;
        if (codigosHorarios != null && codigosHorarios.length() > 0) {
            filtros += adicionarCondicionalWhere(filtros, " and (chor.horario IN (" + codigosHorarios + "))", false);
            String horario;
            if (selecionadoCombobox) {
                horario = consultarHorarioPorCodigo(getCodigoHorario());
            } else {
                horario = getHorariosSelecionadosApresentar().replaceAll("<br/>", ",");
                horario = horario.substring(0, horario.length() - 1);
            }
            adicionarDescricaoFiltro(" Horário: " + horario);
        }

        //situação do cliente ou contrato
        if (getSituacaoCliente() != null && !getSituacaoCliente().equals("")) {
            String sit = " and (st.situacao = '" + getSituacaoCliente() + "'  or st.situacaoContrato = '" + getSituacaoCliente() + "'  ) ";
            filtros += adicionarCondicionalWhere(filtros, sit, false);
            adicionarDescricaoFiltro(" Situação Cliente: " + getSituacaoCliente_Apresentar());
        }

        if (!UteisValidacao.emptyString(getSituacaoSPC())) {
            if ("NG".equals(getSituacaoSPC())) {
                filtros = adicionarCondicionalWhere(filtros, "movparcela.incluidaSpc IS TRUE ", true);
            } else {
                filtros = adicionarCondicionalWhere(filtros, "coalesce(movparcela.incluidaSpc, FALSE) IS FALSE ", true);
            }
        }

        selectStr += filtros;

        return selectStr;
    }

    public List<ParcelaSPCTO> consultarTodosParcelasEmAberto(Integer empresa) throws Exception {
        ResultSet rs = executarConsultaParametrizada(empresa);
        rs.beforeFirst();
        List<ParcelaSPCTO> parcelas = new ArrayList<>();
        while (rs.next()) {
            ParcelaSPCTO parcela = new ParcelaSPCTO();
            parcela.setMatricula(rs.getString("cliente_matricula"));
            parcela.setNome(rs.getString("pessoa_nome"));
            parcela.setCodigoPessoa(rs.getInt("codigopessoa"));
            parcela.setParcela(rs.getInt("movparcela_codigo"));
            parcela.setDescricaoParcela(rs.getString("parcela_descricao"));
            parcela.setDataFatura(rs.getDate("parcela_dataregistro"));
            parcela.setDateVencimento(rs.getDate("parcela_datavencimento"));
            parcela.setDataPagamento(rs.getDate("datapagamento"));
            parcela.setSituacao(rs.getString("parcela_situacao"));
            parcela.setRecorrencia(rs.getString("regime_recorrencia"));
            parcela.setVendaAvulsa(rs.getInt("parcela_venda_avulsa"));
            parcela.setContrato(rs.getInt("parcela_contrato"));
            parcela.setValor(rs.getDouble("parcela_valorParcela"));
            parcela.setEmailValidado(rs.getString("email"));
            parcela.setTelefone(rs.getString("telefone"));
            parcela.setNomeempresa(rs.getString("nomeempresa"));
            parcela.setCliente(rs.getInt("codigoCliente"));
            parcela.setDataCancelamento(rs.getDate("dataCancelamento"));
            parcela.setNrTentativas(rs.getInt("nrtentativas"));
            parcela.setMotivoRetorno(rs.getString("motivoretorno"));
            parcela.setConvenioCobranca(rs.getString("convenios"));
            parcela.setFormasPagamento(rs.getString("formaspagamento"));
            parcela.setModalidades(rs.getString("modalidades"));
            parcela.setRg(rs.getString("rg"));
            parcela.setUfRg(rs.getString("rguf"));
            parcela.setCpf(rs.getString("cpf"));
            parcela.setCpfPai(rs.getString("cpfpai"));
            parcela.setNomePai(rs.getString("nomepai"));
            parcela.setCpfMae(rs.getString("cpfmae"));
            parcela.setNomeMae(rs.getString("nomemae"));
            parcela.setCpfTerceiro(rs.getString("cpfcnpjterceiro"));
            parcela.setNomeTerceiro(rs.getString("nometerceiro"));
            parcela.setIncluidaSpc(rs.getBoolean("incluidaspc"));
            parcela.setSituacaoSpc(rs.getString("situacaospc"));
            parcela.setDataNascimento(rs.getDate("datanasc"));
            parcela.setErroInclusaoSpc(!parcela.isIncluidaSpc() && !UteisValidacao.emptyString(parcela.getSituacaoSpc()));
            parcela.setJsonSpc(rs.getString("jsonspc"));

            EnderecoDTO endereco = new EnderecoDTO();
            endereco.setCep(rs.getString("cep"));
            endereco.setEndereco(rs.getString("endereco"));
            endereco.setNumero(rs.getString("numero"));
            endereco.setComplemento(rs.getString("complemento"));
            endereco.setBairro(rs.getString("bairro"));

//            endereco.setCidade(rs.getString("cidade"));
//            endereco.setUf(rs.getString("uf"));
//            endereco.setPais(rs.getString("pais"));
            parcela.setEndereco(endereco);

            //Hibrael - 19/03-2025 - PAY-419
            //Metodo para fazer a verificacao se o aluno vinculado a parcela e menor de idade e possui pessoa responsavel financeiro cadastrado
            verificaPessoaEMenorComResponsavelFinanceiro(parcela);

            parcelas.add(parcela);
        }
        return parcelas;
    }

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + getIdRelatorio() + ".jrxml");
    }

    public String getSituacao() {
        if (situacao == null) {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getSituacao_Apresentar() {
        if (situacao == null) {
            return "";
        }
        if (situacao.equals("PG")) {
            return "Pago";
        }
        if (situacao.equals("EA")) {
            return "Em Aberto";
        }
        if (situacao.equals("CA")) {
            return "Cancelado";
        }
        return situacao;
    }

    public String getSituacaoSPC() {
        if (situacaoSPC == null) {
            situacaoSPC = "";
        }
        return situacaoSPC;
    }

    public void setSituacaoSPC(String situacaoSPC) {
        this.situacaoSPC = situacaoSPC;
    }

    public String getSituacaoCliente_Apresentar() {
        if (situacaoCliente == null) {
            return "";
        }
        if (situacaoCliente.equals("AT")) {
            return "Ativo";
        }
        if (situacaoCliente.equals("IN")) {
            return "Inativo";
        }
        if (situacaoCliente.equals("VI")) {
            return "Visitante";
        }
        if (situacaoCliente.equals("TR")) {
            return "Trancado";
        }
        if (situacaoCliente.equals("DE")) {
            return "Desistentes";
        }
        if (situacaoCliente.equals("VE")) {
            return "Vencidos";
        }
        if (situacaoCliente.equals("AV")) {
            return "A Vencer";
        }
        if (situacaoCliente.equals("CA")) {
            return "Cancelados";
        }
        return situacaoCliente;
    }

    public Date getDataInicioVencimento() {
        return dataInicioVencimento;
    }

    public void setDataInicioVencimento(Date dataInicioVencimento) {
        this.dataInicioVencimento = dataInicioVencimento;
    }

    public Date getDataTerminoVencimento() {
        return dataTerminoVencimento;
    }

    public void setDataTerminoVencimento(Date dataTerminoVencimento) {
        this.dataTerminoVencimento = dataTerminoVencimento;
    }

    public ColaboradorVO getColaboradorVO() {
        return colaboradorVO;
    }

    public void setColaboradorVO(ColaboradorVO colaboradorVO) {
        this.colaboradorVO = colaboradorVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getPeriodo() {
        if (periodo == null) {
            periodo = "";
        }
        return periodo;
    }

    public void setPeriodo(String periodo) {
        this.periodo = periodo;
    }

    public Integer getTotalParcelaEmAberto() {
        return totalParcelaEmAberto;
    }

    public void setTotalParcelaEmAberto(Integer totalParcelaEmAberto) {
        this.totalParcelaEmAberto = totalParcelaEmAberto;
    }

    public Integer getTotalParcelaPaga() {
        return totalParcelaPaga;
    }

    public void setTotalParcelaPaga(Integer totalParcelaPaga) {
        this.totalParcelaPaga = totalParcelaPaga;
    }

    public Integer getTotalParcelacCancelada() {
        return totalParcelacCancelada;
    }

    public void setTotalParcelacCancelada(Integer totalParcelacCancelada) {
        this.totalParcelacCancelada = totalParcelacCancelada;
    }

    public Double getValorTotalParcelaCancelada() {
        return valorTotalParcelaCancelada;
    }

    public void setValorTotalParcelaCancelada(Double valorTotalParcelaCancelada) {
        this.valorTotalParcelaCancelada = valorTotalParcelaCancelada;
    }

    public Double getValorTotalParcelaEmAberto() {
        return valorTotalParcelaEmAberto;
    }

    public void setValorTotalParcelaEmAberto(Double valorTotalParcelaEmAberto) {
        this.valorTotalParcelaEmAberto = valorTotalParcelaEmAberto;
    }

    public Double getValorTotalParcelaPaga() {
        return valorTotalParcelaPaga;
    }

    public void setValorTotalParcelaPaga(Double valorTotalParcelaPaga) {
        this.valorTotalParcelaPaga = valorTotalParcelaPaga;
    }

    public int getParcelasRecorrencia() {
        return parcelasRecorrencia;
    }

    public void setParcelasRecorrencia(int parcelasRecorrencia) {
        this.parcelasRecorrencia = parcelasRecorrencia;
    }

    public String getSituacaoCliente() {
        return situacaoCliente;
    }

    public void setSituacaoCliente(String situacaoCliente) {
        this.situacaoCliente = situacaoCliente;
    }

    public ConvenioCobrancaVO getConvenio() {
        return convenio;
    }

    public void setConvenio(ConvenioCobrancaVO convenio) {
        this.convenio = convenio;
    }

    public Integer getCodigoPlano() {
        if (codigoPlano == null) {
            codigoPlano = 0;
        }
        return codigoPlano;
    }

    public void setCodigoPlano(Integer codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public Integer getCodigoHorario() {
        if (codigoHorario == null) {
            codigoHorario = 0;
        }
        return codigoHorario;
    }

    public void setCodigoHorario(Integer codigoHorario) {
        this.codigoHorario = codigoHorario;
    }

    public List<SelectItem> getPlanosSelecionados() {
        if (planosSelecionados == null) {
            planosSelecionados = new ArrayList<>();
        }
        return planosSelecionados;
    }

    public void setPlanosSelecionados(List<SelectItem> planosSelecionados) {
        this.planosSelecionados = planosSelecionados;
    }

    public String getQtdPlanosSelecionados() {
        return String.valueOf(getPlanosSelecionados().size());
    }

    public String getCodigoPlanosSelecionados() {
        if (getPlanosSelecionados().isEmpty() && getCodigoPlano() > 0) {
            return getCodigoPlano().toString();
        }
        return Uteis.getValuesFromListSelectItems(getPlanosSelecionados(), false);
    }

    public String getPlanosSelecionadosApresentar() {
        StringBuilder sb = new StringBuilder();
        boolean nenhumSelecionado = true;
        sb.append("Clique para abrir filtro avançado de planos<br/><br/>");

        for (SelectItem itemSelecionado : getPlanosSelecionados()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/>");
        }

        return sb.toString();
    }

    public Integer getTotalMatriculas() {
        return totalMatriculas;
    }

    public void setTotalMatriculas(Integer totalMatriculas) {
        this.totalMatriculas = totalMatriculas;
    }

    public List<SelectItem> getHorariosSelecionados() {
        if (horariosSelecionados == null) {
            horariosSelecionados = new ArrayList<>();
        }
        return horariosSelecionados;
    }

    public void setHorariosSelecionados(List<SelectItem> horariosSelecionados) {
        this.horariosSelecionados = horariosSelecionados;
    }

    public String getQtdHorariosSelecionados() {
        return String.valueOf(getHorariosSelecionados().size());
    }

    public String getCodigoHorariosSelecionados() {
        if (getHorariosSelecionados().isEmpty() && getCodigoHorario() > 0) {
            return codigoHorario.toString();
        }
        return Uteis.getValuesFromListSelectItems(getHorariosSelecionados(), false);
    }

    public String getHorariosSelecionadosApresentar() {
        StringBuilder sb = new StringBuilder();
        boolean nenhumSelecionado = true;
        for (SelectItem itemSelecionado : getHorariosSelecionados()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/>");
        }
        if (nenhumSelecionado) {
            sb.append("TODOS OS HORÁRIOS");
        }
        return sb.toString();
    }

    public List<SelectItem> getSituacoesSelecionadas() {
        if (situacoesSelecionadas == null) {
            situacoesSelecionadas = new ArrayList<>();
        }
        return situacoesSelecionadas;
    }

    public void setSituacoesSelecionadas(List<SelectItem> situacoesSelecionadas) {
        this.situacoesSelecionadas = situacoesSelecionadas;
    }

    public String getQtdSituacoesSelecionadas() {
        return String.valueOf(getSituacoesSelecionadas().size());
    }

    public String getCodigoSituacoesSelecionadas() {
        if (getSituacoesSelecionadas().isEmpty() && !getSituacao().equals("")) {
            return "'" + getSituacao() + "'";
        }
        return Uteis.getValuesFromListSelectItems(getSituacoesSelecionadas(), true);
    }

    public String getSituacoesSelecionadasApresentar() {
        StringBuilder sb = new StringBuilder();
        boolean nenhumSelecionado = true;
        for (SelectItem itemSelecionado : getSituacoesSelecionadas()) {
            nenhumSelecionado = false;
            sb.append(itemSelecionado.getLabel()).append("<br/>");
        }
        if (nenhumSelecionado) {
            sb.append("TODAS AS SITUAÇÕES");
        }
        return sb.toString();
    }

    public String getIdRelatorio() {
        if (idRelatorio == null) {
            idRelatorio = "ParcelaEmAbertoRel";
        }
        return idRelatorio;
    }

    public void setIdRelatorio(String idRelatorio) {
        this.idRelatorio = idRelatorio;
    }

    public Double getTotalJuroeMulta() {
        if (UteisValidacao.emptyNumber(totalJuroeMulta)) {
            totalJuroeMulta = 0.0d;
        }
        return totalJuroeMulta;
    }

    public void setTotalJuroeMulta(Double totalJuroeMulta) {
        this.totalJuroeMulta = totalJuroeMulta;
    }

    public String getParcelaCancelada() {
        if (parcelaCancelada == null) {
            return "";
        }
        return parcelaCancelada;
    }

    public void setParcelaCancelada(String parcelaCancelada) {
        this.parcelaCancelada = parcelaCancelada;
    }

    public Date getDataInicioFaturamento() {
        return dataInicioFaturamento;
    }

    public void setDataInicioFaturamento(Date dataInicioFaturamento) {
        this.dataInicioFaturamento = dataInicioFaturamento;
    }

    public Date getDataTerminoFaturamento() {
        return dataTerminoFaturamento;
    }

    public void setDataTerminoFaturamento(Date dataTerminoFaturamento) {
        this.dataTerminoFaturamento = dataTerminoFaturamento;
    }

    public Date getDataInicioPagamento() {
        return dataInicioPagamento;
    }

    public void setDataInicioPagamento(Date dataInicioPagamento) {
        this.dataInicioPagamento = dataInicioPagamento;
    }

    public Date getDataTerminoPagamento() {
        return dataTerminoPagamento;
    }

    public void setDataTerminoPagamento(Date dataTerminoPagamento) {
        this.dataTerminoPagamento = dataTerminoPagamento;
    }

    public Date getDataInicioSpc() {
        return dataInicioSpc;
    }

    public void setDataInicioSpc(Date dataInicioSpc) {
        this.dataInicioSpc = dataInicioSpc;
    }

    public Date getDataTerminoSpc() {
        return dataTerminoSpc;
    }

    public void setDataTerminoSpc(Date dataTerminoSpc) {
        this.dataTerminoSpc = dataTerminoSpc;
    }

    public Integer getFormaPagamentoSelecionado() {
        return formaPagamentoSelecionado;
    }

    public void setFormaPagamentoSelecionado(Integer formaPagamentoSelecionado) {
        this.formaPagamentoSelecionado = formaPagamentoSelecionado;
    }

    public boolean isSomenteParcelasContratosAssinados() {
        return somenteParcelasContratosAssinados;
    }

    public void setSomenteParcelasContratosAssinados(boolean somenteParcelasContratosAssinados) {
        this.somenteParcelasContratosAssinados = somenteParcelasContratosAssinados;
    }

    private void verificaPessoaEMenorComResponsavelFinanceiro(ParcelaSPCTO parcelaSPCTO) throws Exception {

        if(parcelaSPCTO != null && !UteisValidacao.emptyNumber(parcelaSPCTO.getCodigoPessoa())) {

            PessoaVO pessoaVO = getFacade().getPessoa().consultarPorChavePrimaria(parcelaSPCTO.getCodigoPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            if(pessoaVO != null) {

                //Verifica se pessoa e menor de idade
                if (pessoaVO.getDataNasc() != null && pessoaVO.getIdade() < 18) {
                    //Verifica se existe pessoa responsavel vinculada
                    PessoaVO pessoaResponsavel = getFacade().getCliente().obterPessoaResponsavelCliente(null, pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                    if(pessoaResponsavel != null && !UteisValidacao.emptyNumber(pessoaResponsavel.getCodigo())) {

                        //Verifica os dados que sao utilizados no ao enviar a parcela para negativacao
                        if(!UteisValidacao.emptyString(pessoaResponsavel.getCfp()) && !UteisValidacao.emptyString(pessoaResponsavel.getNome())
                            && !UteisValidacao.emptyString(pessoaResponsavel.getEmail()) && pessoaResponsavel.getDataNasc() != null
                            && !UteisValidacao.emptyList(pessoaResponsavel.getEnderecoVOs()) && pessoaResponsavel.getIdade() > 18) {

                            parcelaSPCTO.setPessoaResponsavel(pessoaResponsavel);
                        }else{
                            parcelaSPCTO.setErro("O Aluno é menor de idade e o seu responsável não tem todos os dados necessários para negativação!");
                        }
                    }else {
                        parcelaSPCTO.setErro("O Aluno é menor de idade e não possuí responsável financeiro para negativação!");
                    }
                } else if (pessoaVO.getDataNasc() != null && pessoaVO.getIdade() >= 18){
                    parcelaSPCTO.setCpfApresentar(pessoaVO.getCfp());
                }
            }
        }
    }
}

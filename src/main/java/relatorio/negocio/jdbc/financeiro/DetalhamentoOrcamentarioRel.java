/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.financeiro.enumerador.TipoES;
import relatorio.negocio.comuns.financeiro.TotalizadorMesRelOrcamentario;

import java.io.Serializable;
import java.util.List;

public class DetalhamentoOrcamentarioRel implements Serializable {
    private String codigoAgrupador;
    private String nomeAgrupador;
    private TipoES tipoPlanoContas;
    private Boolean eInvestimento;
    private Double saldoFinal = 0.0;
    private Double percPretendido = 0.0;
    private Double totalPrevisto = 0.0;
    private Double totalRealizado = 0.0;
    private Integer nivelArvore = 0;
    private TotalizadorMesRelOrcamentario mes1 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes2 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes3 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes4 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes5 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes6 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes7 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes8 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes9 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes10 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes11 = new TotalizadorMesRelOrcamentario();
    private TotalizadorMesRelOrcamentario mes12 = new TotalizadorMesRelOrcamentario();

    public String getPercPretendidoApresentar() {
        if (percPretendido > 0.0) {
            return " - " + Formatador.formatarValorPercentual(percPretendido / 100, 2);
        }
        return "";
    }
    public DetalhamentoOrcamentarioRel(String codigoAgrupador) {
    	this.codigoAgrupador = codigoAgrupador;
    }

    public DetalhamentoOrcamentarioRel() {
        this.codigoAgrupador = codigoAgrupador;
    }

    public Double getPercPretendido() {
        return percPretendido;
    }

    public void setPercPretendido(Double percPretendido) {
        this.percPretendido = percPretendido;
    }

    public String getCodigoAgrupador() {
        return codigoAgrupador;
    }

    public void setCodigoAgrupador(String codigoAgrupador) {
        this.codigoAgrupador = codigoAgrupador;
    }

    public String getNomeAgrupador() {
        return nomeAgrupador;
    }

    public void setNomeAgrupador(String nomeAgrupador) {
        this.nomeAgrupador = nomeAgrupador;
    }


    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (!(obj instanceof DetalhamentoOrcamentarioRel))
            return false;
        return (((DetalhamentoOrcamentarioRel) obj).getCodigoAgrupador().equals(this.getCodigoAgrupador()));
    }

    public synchronized String getSaldoFinalApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.saldoFinal);
        return valor;
      }

	public void setSaldoFinal(Double saldoFinal) {
		this.saldoFinal = saldoFinal;
	}

	public Double getSaldoFinal() {
		return saldoFinal;
	}

    public void setTotalPrevisto(Double totalPrevisto) {
        this.totalPrevisto = totalPrevisto;
    }

    public Double getTotalPrevisto() {
        return totalPrevisto;
    }

    public void setTotalRealizado(Double totalRealizado) {
        this.totalRealizado = totalRealizado;
    }

    public Double getTotalRealizado() {
        return totalRealizado;
    }

    public void setMes1(TotalizadorMesRelOrcamentario mes1) {
        this.mes1 = mes1;
    }

    public TotalizadorMesRelOrcamentario getMes1() {
        return mes1;
    }

    public void setMes2(TotalizadorMesRelOrcamentario mes2) {
        this.mes2 = mes2;
    }

    public TotalizadorMesRelOrcamentario getMes2() {
        return mes2;
    }

    public void setMes3(TotalizadorMesRelOrcamentario mes3) {
        this.mes3 = mes3;
    }

    public TotalizadorMesRelOrcamentario getMes3() {
        return mes3;
    }

    public void setMes4(TotalizadorMesRelOrcamentario mes4) {
        this.mes4 = mes4;
    }

    public TotalizadorMesRelOrcamentario getMes4() {
        return mes4;
    }

    public void setMes5(TotalizadorMesRelOrcamentario mes5) {
        this.mes5 = mes5;
    }

    public TotalizadorMesRelOrcamentario getMes5() {
        return mes5;
    }

    public void setMes6(TotalizadorMesRelOrcamentario mes6) {
        this.mes6 = mes6;
    }

    public TotalizadorMesRelOrcamentario getMes6() {
        return mes6;
    }

    public TotalizadorMesRelOrcamentario getMes7() {
        return mes7;
    }

    public void setMes7(TotalizadorMesRelOrcamentario mes7) {
        this.mes7 = mes7;
    }

    public TotalizadorMesRelOrcamentario getMes8() {
        return mes8;
    }

    public void setMes8(TotalizadorMesRelOrcamentario mes8) {
        this.mes8 = mes8;
    }

    public TotalizadorMesRelOrcamentario getMes9() {
        return mes9;
    }

    public void setMes9(TotalizadorMesRelOrcamentario mes9) {
        this.mes9 = mes9;
    }

    public TotalizadorMesRelOrcamentario getMes10() {
        return mes10;
    }

    public void setMes10(TotalizadorMesRelOrcamentario mes10) {
        this.mes10 = mes10;
    }

    public TotalizadorMesRelOrcamentario getMes11() {
        return mes11;
    }

    public void setMes11(TotalizadorMesRelOrcamentario mes11) {
        this.mes11 = mes11;
    }

    public TotalizadorMesRelOrcamentario getMes12() {
        return mes12;
    }

    public void setMes12(TotalizadorMesRelOrcamentario mes12) {
        this.mes12 = mes12;
    }

    public String getTotalPrevistoString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalPrevisto());
    }

    public String getTotalRealizadoString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getTotalRealizado());
    }

    public String getSaldoFinalString() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getSaldoFinal());
    }

    public String getPercPretendidoString() {
        if (getSaldoFinal() >= 0) {
            return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getPercPretendido()) + "%";
        } else {
            return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getPercPretendido()) + "%";
        }
    }

    public Integer getNivelArvore() {
        return nivelArvore;
    }

    public void setNivelArvore(Integer nivelArvore) {
        this.nivelArvore = nivelArvore;
    }

    public TipoES getTipoPlanoContas() {
        return tipoPlanoContas;
    }

    public void setTipoPlanoContas(TipoES tipoPlanoContas) {
        this.tipoPlanoContas = tipoPlanoContas;
    }

    public Boolean geteInvestimento() {
        return eInvestimento;
    }

    public void seteInvestimento(Boolean eInvestimento) {
        this.eInvestimento = eInvestimento;
    }

    public String getPercPretendidoStr() {
        if (percPretendido == null) {
                return "0";
        }
        return percPretendido.toString();
    }

    // RETORNA UM MES ESPECIFICO
    public TotalizadorMesRelOrcamentario getMes(int mes) {
        switch (mes) {
            case 1: return mes1;
            case 2: return mes2;
            case 3: return mes3;
            case 4: return mes4;
            case 5: return mes5;
            case 6: return mes6;
            case 7: return mes7;
            case 8: return mes8;
            case 9: return mes9;
            case 10: return mes10;
            case 11: return mes11;
            case 12: return mes12;
            default: throw new IllegalArgumentException("mês inválido: " + mes);
        }
    }

    // SETA O VALOR DE UM MES ESPECIFICO
    public void setMes(int mes, TotalizadorMesRelOrcamentario totalizador) {
        switch (mes) {
            case 1: this.mes1 = totalizador; break;
            case 2: this.mes2 = totalizador; break;
            case 3: this.mes3 = totalizador; break;
            case 4: this.mes4 = totalizador; break;
            case 5: this.mes5 = totalizador; break;
            case 6: this.mes6 = totalizador; break;
            case 7: this.mes7 = totalizador; break;
            case 8: this.mes8 = totalizador; break;
            case 9: this.mes9 = totalizador; break;
            case 10: this.mes10 = totalizador; break;
            case 11: this.mes11 = totalizador; break;
            case 12: this.mes12 = totalizador; break;
            default: throw new IllegalArgumentException("mês inválido: " + mes);
        }
    }

}

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.financeiro.RateioIntegracaoTO;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LancamentoDF implements Serializable {

    private int codigoPessoa = 0;
    private String descricaoLancamento = "";
    private double valorLancamento;
    private double valorMultaJuros = 0.0;
    private int contrato = 0;
    private int movConta = 0;
    private int movProduto = 0;
    private int movPagamento = 0;
    private int negociacaoEvento = 0;
    private int empresa = 0;
    private String nomeEmpresa;
    private String descricaoFormaPagamento;
    private String mesReferencia;
    private String tipoProduto = "";
    private List<ProdutoRatear> listaProdutoRatear = new ArrayList<ProdutoRatear>();
    private TipoFormaPagto tipoFormaPagto = null;
    private double totalMovProdutoParcela = 0;
    private int qtdePgtosParaPagarParcela = 0; // atributo para saber se a parcela foi paga em mais de uma vez.
    private double totalProdutosDiferenteDePlano = 0; // atributo para saber o total dos produtos que não são planos.
    private double totalPlano = 0; // atributo para saber qual o valor referente ao Plano.
    private int qtdeParcelasPagaPlano = 0; // Atributo para saber a quantidade de parcelas de um Plano que foram pagas.
    private TipoES tipoES;
    private boolean lancamentoEhNaoAtribuido = false; // atributo utilizado quando o cliente clica no lançamento, para visualizar os detalhes do pagamento.
    private String nomePessoa = "";
    private String style; // Atributo utilizado para marcar o lançamento na tela de visualização dos detalhes do lançamento.
    private int recibo = 0;
    private List<RateioIntegracaoTO> rateios = new ArrayList<RateioIntegracaoTO>();
    private List<RateioIntegracaoTO> rateiosDRE = new ArrayList<RateioIntegracaoTO>();
    private List<ContratoModalidadePercentual> rateioModalidades = new ArrayList<ContratoModalidadePercentual>();
    private int conta = 0;
    private int tipoConta = 0;
    private ContratoModalidadePercentual cmRec = null;
    private boolean dadosResumidos = false;
    private Date dataLancamento;
    private Date diaConta;
    private String formaPagApresentar;
    private int codigoConsultor = 0;
    private int vendaAvulsa = 0;
    private int aulaavulsadiaria = 0;
    private int personal = 0;
    private String modalidadeDiaria = "";
    private Integer quantidade = 0;
    private String responsavelLancamento = "";
    private boolean devolucaoCheque = false;
    private boolean fluxocaixa = false;
    private boolean adicionado = false;
    private TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobranca;
    private String empresaApresentar;
    private int codProduto;
    private String responsavelPagamento= "";

    public String getFormaPagApresentar() {
        return formaPagApresentar;
    }

    public void setFormaPagApresentar(String formaPagApresentar) {
        this.formaPagApresentar = formaPagApresentar;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    
    public LancamentoDF clone() {
        LancamentoDF obj = new LancamentoDF();
        obj.setCodigoPessoa(this.codigoPessoa);
        obj.setDescricaoLancamento(this.descricaoLancamento);
        obj.setValorLancamento(this.valorLancamento);
        obj.setValorMultaJuros(this.valorMultaJuros);
        obj.setContrato(this.contrato);
        obj.setMovConta(this.movConta);
        obj.setMovProduto(this.movProduto);
        obj.setMovPagamento(this.movPagamento);
        obj.setEmpresa(this.empresa);
        obj.setTipoFormaPagto(this.tipoFormaPagto);
        obj.setTipoProduto(this.tipoProduto);
        obj.setNomePessoa(this.nomePessoa);
        obj.setRecibo(this.recibo);
        obj.setRateios(this.rateios);
        obj.setRateiosDRE(this.rateiosDRE);
        obj.setDescricaoFormaPagamento(this.descricaoFormaPagamento);
        obj.setRateioModalidades(this.rateioModalidades);
        obj.setTipoConta(this.tipoConta);
        obj.setConta(this.conta);
        obj.setCmRec(this.cmRec);
        obj.setDiaConta(this.diaConta);
        obj.setNegociacaoEvento(this.negociacaoEvento);
        obj.setCodigoConsultor(this.codigoConsultor);
        obj.setVendaAvulsa(this.vendaAvulsa);
        obj.setMesReferencia(this.mesReferencia);
        obj.setAulaavulsadiaria(this.aulaavulsadiaria);
        obj.setPersonal(this.personal);
        obj.setModalidadeDiaria(this.modalidadeDiaria);
        obj.setDevolucaoCheque(this.devolucaoCheque);
        obj.setTipoES(this.tipoES);
        obj.setFluxocaixa(this.fluxocaixa);
        obj.setTipoAutorizacaoCobranca(this.tipoAutorizacaoCobranca);
        obj.setCodProduto(this.codProduto);
        return obj;
    }

    public String getDescricaoLancamento() {
        return descricaoLancamento;
    }

    public void setDescricaoLancamento(String descricaoLancamento) {
        this.descricaoLancamento = descricaoLancamento;
    }

    public int getMovConta() {
        return movConta;
    }

    public void setMovConta(int movConta) {
        this.movConta = movConta;
    }

    public int getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(int codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public String getMesReferencia() {
        return mesReferencia;
    }

    public void setMesReferencia(String mesReferencia) {
        this.mesReferencia = mesReferencia;
    }

    public double getValorLancamento() {
        return valorLancamento;
    }

    public String getStyleValor(){
        if (this.devolucaoCheque){
            return "text-align:right;background-color: green;";
        }
        return "text-align:right;";
    }

    public String getTitleValor(){
        if (this.devolucaoCheque){
            return "Este recebimento é oriundo de uma devolução de cheque.";
        }
        return "";
    }

    public void setValorLancamento(double valorLancamento) {
        this.valorLancamento = valorLancamento;
    }

    public double getValorMultaJuros() {
        return valorMultaJuros;
    }

    public void setValorMultaJuros(double valorMultaJuros) {
        this.valorMultaJuros = valorMultaJuros;
    }

    public void adicionarValorMultaJuros(double valorMultaJuros) {
        this.valorMultaJuros += valorMultaJuros;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public List<ProdutoRatear> getListaProdutoRatear() {
        return listaProdutoRatear;
    }

    public void setListaProdutoRatear(List<ProdutoRatear> listaProdutoRatear) {
        this.listaProdutoRatear = listaProdutoRatear;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getDescricaoFormaPagamento() {
        return descricaoFormaPagamento;
    }

    public void setDescricaoFormaPagamento(String descricaoFormaPagamento) {
        this.descricaoFormaPagamento = descricaoFormaPagamento;
    }

    public String getValorLancamentoApresentar() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.valorLancamento);
        if (this.valorLancamento < 0) {
            valor = "(" + (valor == null ? "0.0" : valor.replaceAll(" ", "")) + ")";
        }
        return valor;
    }

    public String getValorLancamentoApresentarExcel() {
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.valorLancamento);
        if (this.valorLancamento < 0) {
            valor = "-" + valor;
        }
        return valor;
    }

    public int getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(int movProduto) {
        this.movProduto = movProduto;
    }

    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof LancamentoDF)) {
            return false;
        }
        
        if (this.negociacaoEvento > 0){
            return ((LancamentoDF) obj).getNegociacaoEvento() == this.getNegociacaoEvento()
                    && ((((LancamentoDF) obj).getMovProduto() == this.getMovProduto())) && ((LancamentoDF) obj).getTipoFormaPagto() == this.getTipoFormaPagto();
        }else
            if (this.movProduto > 0) {
            // Trata-se de de um relatório por competência
            return ((((LancamentoDF) obj).getMovProduto() == this.getMovProduto()));
        } else if (this.movPagamento > 0) {
            // Trata-se de de um recebimento do ZillyonWeb
            if (((LancamentoDF) obj).getContrato() > 0) {
                return ((((LancamentoDF) obj).getMovPagamento() == this.getMovPagamento())
                        && (((LancamentoDF) obj).getContrato() == this.getContrato()) &&
                        (((LancamentoDF)obj).getDescricaoLancamento().equals(this.getDescricaoLancamento())) 
                        && (((LancamentoDF)obj).isFluxocaixa() ? ((LancamentoDF)obj).getDiaConta() == this.getDiaConta() : true));
            } else {
                return ((((LancamentoDF) obj).getMovPagamento() == this.getMovPagamento())
                        && (((LancamentoDF) obj).getCodigoPessoa() == this.getCodigoPessoa()) &&
                (((LancamentoDF)obj).getDescricaoLancamento().equals(this.getDescricaoLancamento())) 
                        && (((LancamentoDF)obj).isFluxocaixa() ? ((LancamentoDF)obj).getDiaConta() == this.getDiaConta() : true));

            }
        } else  {
            // Trata-se de dados da tabela MovConta
            return ((((LancamentoDF) obj).getMovConta() == this.getMovConta())
                    && (((LancamentoDF) obj).getDescricaoLancamento().equals(this.getDescricaoLancamento())));
        }
    }

    public int hashCode() {
        return this.codigoPessoa ^ this.empresa ^ 13;
    }

    public int getMovPagamento() {
        return movPagamento;
    }

    public void setMovPagamento(int movPagamento) {
        this.movPagamento = movPagamento;
    }

    public TipoFormaPagto getTipoFormaPagto() {
        return tipoFormaPagto;
    }

    public void setTipoFormaPagto(TipoFormaPagto tipoFormaPagto) {
        this.tipoFormaPagto = tipoFormaPagto;
    }

    public String getTipoProduto() {
        return tipoProduto;
    }

    public void setTipoProduto(String tipoProduto) {
        this.tipoProduto = tipoProduto;
    }

    public double getTotalMovProdutoParcela() {
        return totalMovProdutoParcela;
    }

    public void setTotalMovProdutoParcela(double totalMovProdutoParcela) {
        this.totalMovProdutoParcela = totalMovProdutoParcela;
    }

    public int getQtdePgtosParaPagarParcela() {
        return qtdePgtosParaPagarParcela;
    }

    public void setQtdePgtosParaPagarParcela(int qtdePgtosParaPagarParcela) {
        this.qtdePgtosParaPagarParcela = qtdePgtosParaPagarParcela;
    }

    public double getTotalProdutosDiferenteDePlano() {
        return totalProdutosDiferenteDePlano;
    }

    public void setTotalProdutosDiferenteDePlano(double totalProdutosDiferenteDePlano) {
        this.totalProdutosDiferenteDePlano = totalProdutosDiferenteDePlano;
    }

    public double getTotalPlano() {
        return totalPlano;
    }

    public void setTotalPlano(double totalPlano) {
        this.totalPlano = totalPlano;
    }

    public int getQtdeParcelasPagaPlano() {
        return qtdeParcelasPagaPlano;
    }

    public void setQtdeParcelasPagaPlano(int qtdeParcelasPagaPlano) {
        this.qtdeParcelasPagaPlano = qtdeParcelasPagaPlano;
    }

    public TipoES getTipoES() {
        return tipoES;
    }

    public void setTipoES(TipoES tipoES) {
        this.tipoES = tipoES;
    }

    public boolean isLancamentoEhNaoAtribuido() {
        return lancamentoEhNaoAtribuido;
    }

    public void setLancamentoEhNaoAtribuido(boolean lancamentoEhNaoAtribuido) {
        this.lancamentoEhNaoAtribuido = lancamentoEhNaoAtribuido;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public void setRecibo(int recibo) {
        this.recibo = recibo;
    }

    public int getRecibo() {
        return recibo;
    }

    public void setRateios(List<RateioIntegracaoTO> rateios) {
        this.rateios = rateios;
    }

    public List<RateioIntegracaoTO> getRateios() {
        return rateios;
    }

    public void setRateioModalidades(List<ContratoModalidadePercentual> rateioModalidades) {
        this.rateioModalidades = rateioModalidades;
    }

    public List<ContratoModalidadePercentual> getRateioModalidades() {
        return rateioModalidades;
    }

    public void setTipoConta(int tipoConta) {
        this.tipoConta = tipoConta;
    }

    public int getTipoConta() {
        return tipoConta;
    }

    public void setConta(Integer conta) {
        this.conta = conta == null ? 0 : conta;
    }

    public int getConta() {
        return conta;
    }

    public void setCmRec(ContratoModalidadePercentual cmRec) {
        this.cmRec = cmRec;
    }

    public ContratoModalidadePercentual getCmRec() {
        return cmRec;
    }

    public void setRateiosDRE(List<RateioIntegracaoTO> rateiosDRE) {
        this.rateiosDRE = rateiosDRE;
    }

    public List<RateioIntegracaoTO> getRateiosDRE() {
        return rateiosDRE;
    }

    public boolean isDadosResumidos() {
        return dadosResumidos;
    }

    public void setDadosResumidos(boolean dadosResumidos) {
        this.dadosResumidos = dadosResumidos;
    }

	public void setNegociacaoEvento(int negociacaoEvento) {
		this.negociacaoEvento = negociacaoEvento;
	}

	public int getNegociacaoEvento() {
		return negociacaoEvento;
	}

    public int getCodigoConsultor() {
        return codigoConsultor;
    }

    public void setCodigoConsultor(int codigoConsultor) {
        this.codigoConsultor = codigoConsultor;
    }

    public int getVendaAvulsa() {
        return vendaAvulsa;
    }

    public void setVendaAvulsa(int vendaAvulsa) {
        this.vendaAvulsa = vendaAvulsa;
    }

    public int getAulaavulsadiaria() {
        return aulaavulsadiaria;
    }

    public void setAulaavulsadiaria(int aulaavulsadiaria) {
        this.aulaavulsadiaria = aulaavulsadiaria;
    }

    public int getPersonal() {
        return personal;
    }

    public void setPersonal(int personal) {
        this.personal = personal;
    }

    public String getModalidadeDiaria() {
        return modalidadeDiaria;
    }

    public void setModalidadeDiaria(String modalidadeDiaria) {
        this.modalidadeDiaria = modalidadeDiaria;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(String responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public Date getDiaConta() {
        return diaConta;
    }

    public void setDiaConta(Date diaConta) {
        this.diaConta = diaConta;
    }

    public String getDataApresentar() {

        if (getDiaConta() != null) {
            return Uteis.getDataAplicandoFormatacao(getDiaConta(), "dd/MM/yyyy");
        } else return  "";
    }

    public String getDataMesApresentar() {

        if(getMesReferencia() != null){
            return mesReferencia;
        }
        else if (getDiaConta() != null) {
            return Uteis.getDataAplicandoFormatacao(getDiaConta(), "MM/yyyy");
        }
        else return  "";
    }

    public boolean isDevolucaoCheque() {
        return devolucaoCheque;
    }

    public void setDevolucaoCheque(boolean devolucaoCheque) {
        this.devolucaoCheque = devolucaoCheque;
    }

    public boolean isFluxocaixa() {
        return fluxocaixa;
    }

    public void setFluxocaixa(boolean fluxocaixa) {
        this.fluxocaixa = fluxocaixa;
    }

    public void setTipoAutorizacaoCobranca(TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobranca) {
        this.tipoAutorizacaoCobranca = tipoAutorizacaoCobranca;
    }

    public TipoAutorizacaoCobrancaEnum getTipoAutorizacaoCobranca() {
        if(tipoAutorizacaoCobranca == null){
            tipoAutorizacaoCobranca = TipoAutorizacaoCobrancaEnum.NENHUM;
        }
        return tipoAutorizacaoCobranca;
    }

    public boolean isAdicionado() {
        return adicionado;
    }

    public void setAdicionado(boolean adicionado) {
        this.adicionado = adicionado;
    }

    public String getEmpresaApresentar() {
        return empresaApresentar;
    }

    public void setEmpresaApresentar(String empresaApresentar) {
        this.empresaApresentar = empresaApresentar;
    }

    public int getCodProduto() {
        return codProduto;
    }

    public void setCodProduto(int codProduto) {
        this.codProduto = codProduto;
    }

    public String getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(String responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }
}

package relatorio.negocio.jdbc.financeiro;

import java.io.File;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

public class SaldoContaCorrenteRel extends SuperRelatorio {

    private EmpresaVO empresaVO;
    private ClienteVO clienteVO;
    private Double saldoMenor;
    private Double saldoMaior;
    private Integer filtroEmpresa;

    public SaldoContaCorrenteRel() throws Exception {
        inicializarDados();
    }

    public void inicializarDados() {
        setEmpresaVO(new EmpresaVO());
        setClienteVO(new ClienteVO());
        setSaldoMenor(0.0);
        setSaldoMaior(0.0);
    }

    public void validarDados(boolean permiteConsultarInfoTodasEmpresas) throws Exception {
        if(filtroEmpresa == null){
            filtroEmpresa = getEmpresaVO().getCodigo();
        }
        if (getEmpresaVO() != null && (filtroEmpresa == 0 && !permiteConsultarInfoTodasEmpresas)) {
            throw new Exception("O campo Empresa deve ser informado.");
        }
        if ((getSaldoMenor() != null && getSaldoMaior() != null)) {
            if ((getSaldoMenor() != 1) && (getSaldoMaior() != 1)) {
                if (getSaldoMenor() > getSaldoMaior()) {
                    throw new Exception("O valor do primeiro Campo deve ser Menor que o valor do segundo Campo.");
                }
            }
        }
        if ((getSaldoMenor() != null && getSaldoMaior() != null) && (getSaldoMenor() == 1 && getSaldoMaior() != 1)) {
            throw new Exception("Os Dois Campos devem ser Preenchidos com TODOS.");
        }
        if ((getSaldoMenor() != null && getSaldoMaior() != null) && (getSaldoMaior() == 1 && getSaldoMenor() != 1)) {
            throw new Exception("Os Dois Campos devem ser Preenchidos com TODOS.");
        }

    }

    private String montarFiltrosRelatorio(String selectStr) throws Exception {
        String filtros = "";
        if (getClienteVO().getPessoa().getCodigo() != 0) {
            filtros = "" + getClienteVO().getPessoa().getCodigo() + " )";
            adicionarDescricaoFiltro(" Clientes " + getClienteVO().getPessoa().getNome());
        } else {
            filtros = "MOV.pessoa )";
            adicionarDescricaoFiltro(" Clientes " + getClienteVO().getPessoa().getNome());
        }
        if (filtroEmpresa > 0) {
            filtros = adicionarCondicionalWhere(filtros, "(EMP.codigo = " + filtroEmpresa + ")", true);
            String empresa = obterEmpresaNome(filtroEmpresa);
            adicionarDescricaoFiltro(" Empresa " + empresa);
        }else if(filtroEmpresa == 0){
            adicionarDescricaoFiltro(" TODAS AS EMPRESAS ");
        }
        if ((getSaldoMenor() != null && getSaldoMaior() != null) && (getSaldoMenor() == 0 && getSaldoMaior() == 0)) {
            if (getSaldoMenor() != null) {
                adicionarDescricaoFiltro(" Saldo " + " Todos ");
            }
        }
        if (getSaldoMenor() != null && getSaldoMenor() != 0) {
            filtros = adicionarCondicionalWhere(filtros, "(saldoatual >= " + getSaldoMenor().toString() + ")", true);
            adicionarDescricaoFiltro(" Saldo Inicial " + getSaldoMenor().toString());
        }
        if (getSaldoMaior() != null && getSaldoMaior() != 0) {
            filtros = adicionarCondicionalWhere(filtros, "(saldoatual <= " + getSaldoMaior().toString() + ")", true);
            adicionarDescricaoFiltro(" Saldo Final " + getSaldoMaior().toString());
        }
        selectStr += filtros + " AND saldoatual <> 0 ORDER BY saldoatual";

        return selectStr;
    }

    public String emitirRelatorio() throws Exception {
        new SaldoContaCorrenteRel().emitirRelatorio(getIdEntidade(), true);
        ResultSet resultadoConsulta = executarConsultaParametrizada();
        converterResultadoConsultaParaXML(this.getIdEntidade(), "registros", resultadoConsulta);
        return getXmlRelatorio();
    }

    public String obterEmpresaNome(Integer codigo) throws Exception {
        List lista = new Empresa().consultarPorCodigo(codigo,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        EmpresaVO empresa = new EmpresaVO();
        empresa = (EmpresaVO) lista.get(0);
        return empresa.getNome();
    }

    public ResultSet executarConsultaParametrizada() throws Exception {
        inicializar();
        String sql = "SELECT " +
                "MOV.codigo, saldoatual, PES.nome, CLI.matricula, EMP.nome empresa " +
                "from movimentocontacorrentecliente MOV " +
                "LEFT JOIN pessoa PES " +
                "ON PES.codigo = MOV.pessoa " +
                "LEFT JOIN cliente CLI " +
                "ON CLI.pessoa = MOV.pessoa " +
                "LEFT JOIN empresa EMP " +
                "ON CLI.empresa = EMP.codigo " +
                "WHERE MOV.codigo = (select MAX(codigo) " +
                "FROM movimentocontacorrentecliente WHERE pessoa = ";

        sql = montarFiltrosRelatorio(sql);
        PreparedStatement sqlPS = con.prepareStatement(sql, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        ResultSet resultadoConsulta = sqlPS.executeQuery();

        resultadoConsulta.last();
        if (resultadoConsulta.getRow() == 0) {
            throw new ConsistirException("Nenhum Registro Encontrado!");
        }
        resultadoConsulta.beforeFirst();
        return resultadoConsulta;
    }

    public static String getDesignIReportRelatorioTodasEmpresas() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + "SaldoContaCorrenteTodasEmpresasRel" + ".jrxml");
    }

    public static String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "financeiro" + File.separator + getIdEntidadeLocal() + ".jrxml");
    }

    public static String getIdEntidadeLocal() {
        return ("SaldoContaCorrenteRel");
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public Double getSaldoMaior() {
        return saldoMaior;
    }

    public void setSaldoMaior(Double saldoMaior) {
        this.saldoMaior = saldoMaior;
    }

    public Double getSaldoMenor() {
        return saldoMenor;
    }

    public void setSaldoMenor(Double saldoMenor) {
        this.saldoMenor = saldoMenor;
    }

    public Integer getFiltroEmpresa() {
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }
}

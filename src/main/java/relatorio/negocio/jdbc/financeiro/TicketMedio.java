/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import br.com.pactosolucoes.comuns.json.TicketMedioJSON;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.CampoBIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.basico.CampoBIVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.financeiro.DFSinteticoDWVO;
import negocio.comuns.financeiro.DRESinteticoDWVO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.CampoBI;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.ConfiguracaoBI;
import negocio.facade.jdbc.financeiro.DFSinteticoDW;
import negocio.facade.jdbc.financeiro.DRESinteticoDW;
import negocio.interfaces.financeiro.TicketMedioInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.TicketMedioVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoVO;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoResumoPessoaVO;
import relatorio.negocio.comuns.sad.RotatividadeSinteticoDWVO;
import relatorio.negocio.enumeradores.TicketMedioEnum;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;
import servicos.DRESintetico;
import servicos.DemonstrativoFinanceiroSintetico;
import servicos.operacoes.RelatorioCompetenciaService;
import servicos.operacoes.RotatividadeService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class TicketMedio extends SuperRelatorio implements TicketMedioInterfaceFacade {

    public TicketMedio() throws Exception {
        super();
        setIdEntidade("TicketMedio");

    }

    public TicketMedio(Connection con) throws Exception {
        super(con);
        setIdEntidade("TicketMedio");

    }

    @Override
    public Integer numeroContratosAtivosComBolsa(Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(pessoa) FROM (SELECT pessoa FROM contrato \n");
        sql.append("WHERE bolsa IS TRUE \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND empresa = ").append(empresa).append("\n");
        }
        sql.append("AND (vigenciade BETWEEN '");
        sql.append(Uteis.getDataHoraJDBC(inicio, "23:59:59"));
        sql.append("' AND '").append(Uteis.getDataHoraJDBC(fim, "23:59:59")).append("'\n");
        sql.append("OR vigenciaateajustada BETWEEN '");
        sql.append(Uteis.getDataHoraJDBC(inicio, "23:59:59"));
        sql.append("' AND '").append(Uteis.getDataHoraJDBC(fim, "23:59:59")).append("'\n");
        sql.append("OR (vigenciade < '").append(Uteis.getDataHoraJDBC(inicio, "23:59:59"));
        sql.append("' AND vigenciaateajustada > '");
        sql.append(Uteis.getDataHoraJDBC(fim, "23:59:59")).append("'))\n");
        sql.append("group by pessoa) as a");
        
        return contar(sql.toString(), con);
    }

    @Override
    public Double caixaFaturamentoRecebido(Integer empresa, Date inicio, Date fim) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sum(valortotal) as valor FROM movpagamento mp \n");
        sql.append("INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento\n");
        sql.append("WHERE fp.tipoformapagamento NOT IN ('CC') AND mp.empresa = ").append(empresa).append("\n");
        sql.append("AND mp.datalancamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBCTimestamp(fim)).append("' AND not mp.credito\n");

        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getDouble(1);
        } else {
            return 0.0;
        }
    }

    public ResultSet consultaFaturamento(Integer empresa, Date inicio, Date fim) throws Exception {
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaFaturamentoRecebido(empresa,
                inicio, fim, null, null, true, null, false, null, null, null, false, null,null,null, null);
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs;
    }

    @Override
    public String caixaFaturamentoRecebidoJSON(Integer empresa, Date inicio, Date fim) throws Exception {
        ResultSet rs = consultaFaturamento(empresa, inicio, fim);
        return montarJSON(rs, TicketMedioEnum.FATURAMENTO_RECEBIDO);
    }

    public Double caixaReceita(Integer empresa, Date inicio, Date fim) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT sum(valor) FROM (\n");
        sql.append(sqlReceita(true, inicio, fim, empresa));
        sql.append(") as a");
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getDouble(1);
        } else {
            return 0.0;
        }

    }

    public String caixaReceitaJSON(Integer empresa, Date inicio, Date fim) throws Exception {
        ResultSet rs = criarConsulta(sqlReceita(false, inicio, fim, empresa).toString(), con);
        return montarJSON(rs, TicketMedioEnum.RECEITA);

    }

    private String sqlReceita(boolean sum, Date inicio, Date fim, int empresa) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ").append(sum ? "sum(valor)" : "mp.pessoa, mp.nomepagador, mp.datalancamento, mp.valor").append(" as valor FROM movpagamento mp \n");
        sql.append("INNER JOIN formapagamento fp ON fp.codigo = mp.formapagamento\n");
        sql.append("WHERE fp.tipoformapagamento NOT IN ('CH', 'CA', 'CC') AND mp.empresa = ").append(empresa).append("\n");
        sql.append("AND mp.datapagamento BETWEEN '").append(Uteis.getDataJDBCTimestamp(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBCTimestamp(fim)).append("' \n");
        sql.append("UNION ALL \n");
        sql.append("SELECT ").append(sum ? "sum(ch.valor) as valor" : "mp.pessoa, mp.nomepagador, mp.datalancamento, ch.valor").append(" FROM cheque ch\n");
        sql.append("INNER JOIN movpagamento mp ON ch.movpagamento = mp.codigo AND mp.empresa = ").append(empresa).append("\n");
        sql.append("WHERE datacompesancao BETWEEN '").append(Uteis.getDataJDBCTimestamp(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBCTimestamp(fim)).append("' AND situacao NOT LIKE 'CA'\n");
        sql.append("UNION ALL \n");
        sql.append("SELECT ").append(sum ? "sum(cc.valor) as valor" : "mp.pessoa, mp.nomepagador, mp.datalancamento, cc.valor").append(" FROM cartaocredito cc\n");
        sql.append("INNER JOIN movpagamento mp ON cc.movpagamento = mp.codigo AND mp.empresa = ").append(empresa).append(" \n");
        sql.append("WHERE datacompesancao BETWEEN '").append(Uteis.getDataJDBCTimestamp(inicio));
        sql.append("' AND '").append(Uteis.getDataJDBCTimestamp(fim)).append("' AND situacao NOT LIKE 'CA'\n");

        return sql.toString();
    }

    @Override
    public Double caixaCompetencia(Integer empresa, Date dataBase) throws Exception {
        Double valor = 0.0;
        ResultSet rs = consultaCompetencia(empresa, dataBase);
        while (rs.next()) {
            valor += rs.getDouble("valor");
        }
        return valor;
    }

    @Override
    public String povoarJSON(TicketMedioVO ticket, Boolean competencia) throws Exception {
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        Cliente clienteDao = new Cliente(con);
        Colaborador colaboradorDao = new Colaborador(con);

        if (competencia) {
            for (CompetenciaSinteticoResumoPessoaVO pessoa : ticket.getCompetenciaSinteticoProdutoMesVO().getListaResumoPessoa()) {
                dados = povoarJSON(clienteDao, pessoa.getCliente().getPessoa().getCodigo(),
                        UteisValidacao.emptyNumber(pessoa.getContrato().getCodigo()) ? null : pessoa.getContrato().getCodigo(),
                        pessoa.getValor(),
                        colaboradorDao, json);
            }
        } else {
            for (FaturamentoSinteticoResumoPessoaVO pessoa : ticket.getFaturamentoSinteticoProdutoMesVO().getListaResumoPessoa()) {
                dados = povoarJSON(clienteDao, pessoa.getCliente().getPessoa().getCodigo(),
                        UteisValidacao.emptyNumber(pessoa.getContrato().getCodigo()) ? null : pessoa.getContrato().getCodigo(),
                        pessoa.getValor(),
                        colaboradorDao, json);
            }
        }

        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public ResultSet consultaCompetencia(Integer empresa, Date dataBase) throws Exception {
        int anoData = Uteis.getAnoData(dataBase);
        String mesReferenciaData = Uteis.getMesReferenciaData(dataBase);
        StringBuilder sql = ThreadDemonstrativoFinanceiro.consultaCompetenciaFaturamento(anoData, mesReferenciaData, null, null, empresa, true, TipoRelatorioDF.COMPETENCIA, false, null);
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs;
    }

    private String montarJSON(ResultSet rs, TicketMedioEnum ticketMedioEnum) throws Exception {
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        Cliente clienteDao = new Cliente(con);
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            ClienteVO clienteFat = clienteDao.consultarPorCodigoPessoa(rs.getInt("pessoa"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            json.append("[\"").append(clienteFat.getCodigo()).append("\",");
            json.append("\"").append(clienteFat.getMatricula()).append("\",");
            json.append("\"").append(rs.getString("nomepagador")).append("\",");
            json.append("\"").append(Uteis.getData(rs.getDate("datalancamento"))).append("\",");
            json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valor"))).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private DFSinteticoDWVO dfSintetico(Integer empresa, Date dataBase) throws Exception {
        int ano = Uteis.getAnoData(dataBase);
        int mes = Uteis.getMesData(dataBase);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM dfsinteticodw where ano = ").append(ano).append(" and mes = ").append(mes);
        if (empresa != null && empresa >= 0) {
            sql.append(" and empresa = ").append(empresa);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return DFSinteticoDW.montarDados(rs);
        } else {
            return new DFSinteticoDWVO();
        }
    }

    private DRESinteticoDWVO dreSintetico(Integer empresa, Date dataBase) throws Exception {
        int ano = Uteis.getAnoData(dataBase);
        int mes = Uteis.getMesData(dataBase);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM dresinteticodw where ano = ").append(ano).append(" and mes = ").append(mes);
        if (empresa != null && empresa >= 0) {
            sql.append(" and empresa = ").append(empresa);
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return DRESinteticoDW.montarDados(rs);
        } else {
            return new DRESinteticoDWVO();
        }
    }

    @Override
    public TicketMedioVO carregarBITicketMedio(Integer empresa, Date dataBase, 
        RotatividadeSinteticoDWVO sintetico, boolean somenteProdutosNaCompetencia, boolean consultarPeloDRE) throws Exception {
        TicketMedioVO ticket = new TicketMedioVO();
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(dataBase));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(dataBase);

        ticket.setAtivosVencidosInicioMes(sintetico.getQtdVigentesMesAnterior());
        ticket.setAtivosVencidosFimMes(sintetico.getQtdTotal());
        ticket.setAtivos(sintetico.getQtdeFinalMesAtual());
        ticket.setBolsas(numeroContratosAtivosComBolsa(inicio, fim, empresa));
        ticket.setDependentesInicioMes(sintetico.getQtdDependentesMesAtual());
        ticket.setDependentesFimMes(sintetico.getQtdDependentesFinalMesAtual());

        RelatorioCompetenciaService competenciaService = new RelatorioCompetenciaService(con);
        CompetenciaSinteticoProdutoVO compProd = competenciaService.gerarCompetencia(inicio, fim, empresa,somenteProdutosNaCompetencia);
        if (compProd != null && (compProd.getListaProdutoXMes() != null && !compProd.getListaProdutoXMes().isEmpty())) {
            ticket.setCaixaCompetencia(compProd.getListaProdutoXMes().get(0).getValor());
        }
        Double receita;
        if (consultarPeloDRE) {
            receita = DRESintetico.processarDRE(getCon(), empresa, 0, true, dataBase);
        } else {
            receita = DemonstrativoFinanceiroSintetico.processarDemonstrativo(getCon(), empresa, 0, true, dataBase);
        }
        ticket.setCaixaReceita(receita);
        return ticket;
    }

    private boolean povoarJSON(Cliente clienteDao, Integer codigoPessoa,
            Integer codigoContrato, Double valor,
            Colaborador colaboradorDao, StringBuilder json) throws Exception {
        boolean dados;
        dados = true;
        String nome = "";
        Integer codigo = null;
        ClienteVO cliente = clienteDao.consultarPorCodigoPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        if (cliente == null || UteisValidacao.emptyNumber(cliente.getCodigo())) {
            ColaboradorVO colaborador = colaboradorDao.consultarPorCodigoPessoa(codigoPessoa, 0, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            nome = colaborador.getPessoa().getNome();
            codigo = colaborador.getCodigo();
        } else {
            nome = cliente.getPessoa().getNome();
            codigo = cliente.getCodigo();
        }
        json.append("[\"").append(codigo).append("\",");
        json.append("\"").append(cliente == null ? null : cliente.getMatricula()).append("\",");
        json.append("\"").append(nome).append("\",");
        json.append("\"").append(codigoContrato).append("\",");
        json.append("\"").append(Formatador.formatarValorMonetarioSemMoeda(valor)).append("\"],");
        return dados;
    }
    
    @Override
    public void gerarDadosSintetico(Integer empresa) throws Exception {
        if (empresa == null || empresa == 0) {
            ResultSet rs = criarConsulta("SELECT codigo FROM empresa ", getCon());
            while (rs.next()) {
                processarBiTicketMedio(Calendario.hoje(), rs.getInt("codigo"), true, false);
                processarBiTicketMedio(Calendario.hoje(), rs.getInt("codigo"), true, true);
            }
        } else {
            processarBiTicketMedio(Calendario.hoje(), empresa, true, false);
            processarBiTicketMedio(Calendario.hoje(), empresa, true, true);
        }

    }

    private void processarBiTicketMedio(Date data, Integer empresa, boolean somenteProdutosNaCompetencia, boolean consultarPeloDRE) throws Exception {
        RotatividadeService rotatividadeService = new RotatividadeService(con);
        RotatividadeSinteticoDWVO sintetico = rotatividadeService.montarRelatorioRotatividadeTelaInicial(empresa, data);
        int mesData = Uteis.getMesData(data);
        int anoData = Uteis.getAnoData(data);
        List<CampoBIVO> lista = new ArrayList<>();
        TicketMedioVO ticket = carregarBITicketMedio(empresa, data, sintetico, somenteProdutosNaCompetencia, consultarPeloDRE);
        lista.add(new CampoBIVO(CampoBIEnum.ATIVOS, ticket.getAtivos().doubleValue(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.ATIVOS_VENCIDOS_INICIO_MES, ticket.getAtivosVencidosInicioMes().doubleValue(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.ATIVOS_VENCIDOS_FIM_MES, ticket.getAtivosVencidosFimMes().doubleValue(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.BOLSAS_MES, ticket.getBolsas().doubleValue(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.CAIXA_POR_COMPETENCIA, ticket.getCaixaCompetencia(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.CAIXA_POR_RECEITA, ticket.getCaixaReceita(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.DEPENDENTES_INICIO_MES, ticket.getDependentesInicioMes().doubleValue(), Calendario.hoje(), mesData, anoData, empresa));
        lista.add(new CampoBIVO(CampoBIEnum.DEPENDENTES_FINAL_MES, ticket.getDependentesFimMes().doubleValue(), Calendario.hoje(), mesData, anoData, empresa));

        CampoBI dao = new CampoBI(con);
        dao.deletarPorBI(BIEnum.TICKET_MEDIO, mesData, anoData, empresa);
        dao.incluirPorBI(lista);
    }
    
    private ConfiguracaoBIVO getConfiguracao(ConfiguracaoBIEnum cfg, List<ConfiguracaoBIVO> cfgs){
        for(ConfiguracaoBIVO c : cfgs){
            if(c.getConfiguracao().equals(cfg)){
                return c;
            }
        }
        return null;
    }

    public JSONObject gerarTicketMedio(Boolean atualizar, Integer empresa, String database, Integer mes, Integer ano) throws Exception{
        ConfiguracaoBI dao = new ConfiguracaoBI(con);
        List<ConfiguracaoBIVO> configuracoes = dao.consultarPorBI(BIEnum.TICKET_MEDIO, empresa);
        //-------------------------------
        configuracoes.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA));
        TicketMedioVO ticketMedioVO = montarTicketMedio(atualizar, configuracoes, empresa, Uteis.getDate(database));
        JSONObject json = new JSONObject();
        json.put("ativosVencidosInicioMes", ticketMedioVO.getAtivosVencidosInicioMes());
        json.put("ativosVencidosFimMes", ticketMedioVO.getAtivosVencidosFimMes());
        json.put("mediaAtivosVencidos", ticketMedioVO.getMediaAtivosVencidos());
        json.put("bolsas", ticketMedioVO.getBolsas());
        json.put("ativos", ticketMedioVO.getAtivos());
        json.put("pagantes", ticketMedioVO.getPagantes());
        json.put("caixaCompetencia", ticketMedioVO.getCaixaCompetencia());
        json.put("ticketCompetencia", ticketMedioVO.getTicketCompetencia());
        json.put("caixaFaturamentoRecebido", ticketMedioVO.getCaixaFaturamentoRecebido());
        json.put("ticketFaturamentoRecebido", ticketMedioVO.getTicketFaturamentoRecebido());
        json.put("ticketReceita", ticketMedioVO.getTicketReceita());
        json.put("caixaReceita", ticketMedioVO.getCaixaReceita());
        json.put("despesaTotalMes", ticketMedioVO.getDespesaTotalMes());
        json.put("despesaPorAluno", ticketMedioVO.getDespesaPorAluno());
        json.put("ultimaExecucaoDespesa", ticketMedioVO.getUltimaExecucaoDespesa());
        json.put("corDespesa", ticketMedioVO.getCorDespesa());
        json.put("mes", mes);
        json.put("ultimaAtualizacao", ticketMedioVO.getUltimaAtualizacao().getTime());
        json.put("ano", ano);
        json.put("ticketReceitaFormatado", ticketMedioVO.getTicketReceitaFormatado());
        json.put("ticketCompetenciaFormatado", ticketMedioVO.getTicketCompetenciaFormatado());
        json.put("despesaPorAlunoFormatado", ticketMedioVO.getDespesaPorAlunoFormatado());
        json.put("dependentesInicioMes", ticketMedioVO.getDependentesInicioMes());
        json.put("dependentesFimMes", ticketMedioVO.getDependentesFimMes());
        JSONObject content = new JSONObject().put("content", json);

        boolean consultarPeloDRE = getConfiguracao(ConfiguracaoBIEnum.FONTE_RECEITA_DESPESA, configuracoes).getValorAsInteger().equals(2);
        List<TicketMedioJSON> ticketMedioJSONS = consultarParaGrafico(empresa,
                getConfiguracao(ConfiguracaoBIEnum.ALUNOS_BOLSA, configuracoes).getValorAsBoolean(),
                getConfiguracao(ConfiguracaoBIEnum.ATIVOS, configuracoes).getValorAsInteger().equals(1),
                mes, ano, consultarPeloDRE);
        JSONArray grafico = new JSONArray();
        for(TicketMedioJSON tm : ticketMedioJSONS){
            grafico.put(tm.toJSONObject());
        }
        content.put("grafico", grafico);
        return content;
    }

    @Override
    public TicketMedioVO montarTicketMedio(boolean atualizar, List<ConfiguracaoBIVO> cfgs, Integer empresa, Date data) throws Exception {
        TicketMedioVO ticket = new TicketMedioVO();
        CampoBI campoDao = new CampoBI(con);
        int mesData = Uteis.getMesData(data);
        int anoData = Uteis.getAnoData(data);
        boolean consultarPeloDRE = getConfiguracao(ConfiguracaoBIEnum.FONTE_RECEITA_DESPESA, cfgs).getValorAsInteger().equals(2);
        boolean incluirProdutosCompetencia = false;
        for (ConfiguracaoBIVO cvo : cfgs) {
            if (cvo.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_COMPETENCIA)) {
                incluirProdutosCompetencia = cvo.getValorAsBoolean();
            }
        }

        Map<CampoBIEnum, CampoBIVO> mapa = campoDao.obterMapaPorBI(BIEnum.TICKET_MEDIO, mesData, anoData, empresa);
        if (mapa.isEmpty() || atualizar) {
            processarBiTicketMedio(data, empresa, !incluirProdutosCompetencia, consultarPeloDRE);
            mapa = campoDao.obterMapaPorBI(BIEnum.TICKET_MEDIO, mesData, anoData, empresa);
        }
        CampoBIVO campo = mapa.get(CampoBIEnum.ATIVOS_VENCIDOS_INICIO_MES);
        ticket.setUltimaAtualizacao(campo.getData());
        ticket.setAtivosVencidosInicioMes(campo.getValor().intValue());
        ticket.setAtivosVencidosFimMes(mapa.get(CampoBIEnum.ATIVOS_VENCIDOS_FIM_MES).getValor().intValue());
        ticket.setMediaAtivosVencidos((ticket.getAtivosVencidosInicioMes() + ticket.getAtivosVencidosFimMes()) / 2);

        CampoBIVO campoDependentesInicioMes = mapa.get(CampoBIEnum.DEPENDENTES_INICIO_MES);
        ticket.setDependentesInicioMes(campoDependentesInicioMes.getValor().intValue());
        CampoBIVO campoDependentesFinalMes = mapa.get(CampoBIEnum.DEPENDENTES_FINAL_MES);
        ticket.setDependentesFimMes(campoDependentesFinalMes.getValor().intValue());

        ticket.setAtivos(mapa.get(CampoBIEnum.ATIVOS) == null
                ? 0
                : mapa.get(CampoBIEnum.ATIVOS).getValor().intValue());

        ConfiguracaoBIVO configAlunosBolsa = getConfiguracao(ConfiguracaoBIEnum.ALUNOS_BOLSA, cfgs);
        ConfiguracaoBIVO configAtivos = getConfiguracao(ConfiguracaoBIEnum.ATIVOS, cfgs);
        ConfiguracaoBIVO configContarDependentesComoPagantes = getConfiguracao(ConfiguracaoBIEnum.CONTAR_DEPENDENTES_COMO_PAGANTES, cfgs);

        int bolsas = 0;
        if (configAlunosBolsa == null || !configAlunosBolsa.getValorAsBoolean()) {
            ticket.setBolsas(mapa.get(CampoBIEnum.BOLSAS_MES).getValor().intValue());
            bolsas = ticket.getBolsas();
        }

        int dependentes = 0;
        if (configContarDependentesComoPagantes != null && configContarDependentesComoPagantes.getValorAsBoolean()) {
            ticket.setMediaAtivosVencidos((ticket.getAtivosVencidosInicioMes() + ticket.getDependentesInicioMes()
                    + ticket.getAtivosVencidosFimMes() + ticket.getDependentesFimMes()) / 2);
            dependentes = ticket.getDependentesFimMes();
        }

        int pagantes;
        if (configAtivos != null && configAtivos.getValorAsInteger().equals(1)) {
            pagantes = ticket.getMediaAtivosVencidos() - bolsas;
        } else {
            pagantes = ticket.getAtivos() + dependentes - bolsas;
        }
        ticket.setPagantes(pagantes);

        if (ticket.getPagantes() <= 0.0) {
            ticket.setPagantes(0);
            return ticket;
        }
        ticket.setCaixaCompetencia(mapa.get(CampoBIEnum.CAIXA_POR_COMPETENCIA).getValor());
        ticket.setTicketCompetencia(ticket.getCaixaCompetencia() / ticket.getPagantes());
         
        ticket.setCaixaReceita(mapa.get(CampoBIEnum.CAIXA_POR_RECEITA).getValor());
        ticket.setTicketReceita(ticket.getCaixaReceita() / ticket.getPagantes());

        if (consultarPeloDRE) {
            DRESinteticoDWVO dreSinteticoDWVO = dreSintetico(empresa, data);
            ticket.setDespesaTotalMes(dreSinteticoDWVO.getDespesa() < 0 ? dreSinteticoDWVO.getDespesa() * -1 : dreSinteticoDWVO.getDespesa());
            ticket.setDespesaPorAluno(ticket.getDespesaTotalMes() / ticket.getPagantes());
        } else {
            DFSinteticoDWVO dfSintetico = dfSintetico(empresa, data);
            ticket.setDespesaTotalMes(dfSintetico.getDespesa() < 0 ? dfSintetico.getDespesa() * -1 : dfSintetico.getDespesa());
            ticket.setDespesaPorAluno(ticket.getDespesaTotalMes() / ticket.getPagantes());
        }

        if (ticket.getDespesaPorAluno() > ticket.getTicketReceita()) {
            ticket.setCorDespesa("#FF322C");
        } else {
            double percentual = ticket.getDespesaPorAluno() * 100 / ticket.getTicketReceita();
            ticket.setCorDespesa(percentual > 80 ? "#FF6E00" : "#002C74");
        }


        return ticket;
    }
    
    
    
    public static void main(String ... args){
        try {
            
            Connection con = DriverManager.getConnection("*****************************************", "postgres", "pactodb");
            TicketMedio ticketServico = new TicketMedio(con);
            //            ticketServico.processarBiTicketMedio(Calendario.hoje(), 1);
//            List<TicketMedioJSON> consultarParaGrafico = ticketServico.consultarParaGrafico(5, true, true);
//            JSONArray array = new JSONArray(consultarParaGrafico);
//            System.out.println(array.toString());
            
            
        } catch (Exception ex) {
            Logger.getLogger(TicketMedio.class.getName()).log(Level.SEVERE, null, ex);
        }
        
    }
    
    @Override
    public List<TicketMedioJSON> consultarParaGrafico(Integer empresa, boolean incluirBolsas, boolean usarMedia, Integer mes, Integer ano, Boolean consultarPeloDRE) throws Exception{
        
        Map<String, TicketMedioVO> mapa = new HashMap<>();
        StringBuilder sql = new StringBuilder();
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(("SELECT ca.campobi, ca.mes, ca.ano, ca.valor, d.despesa, d.receita \n"));
        } else {
            sql.append("SELECT ca.campobi, ca.mes, ca.ano, sum(coalesce(valor,0)) as valor, sum(coalesce(d.despesa,0)) as despesa, sum(coalesce(d.receita,0)) as receita ");
        }
        sql.append(" FROM campobi ca \n");
        sql.append(" INNER JOIN \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" (SELECT ano, mes, empresa FROM campobi \n");
            sql.append(" WHERE empresa = ").append(empresa).append(" \n");
        } else {
            sql.append(" (SELECT ano, mes FROM campobi \n");
            sql.append(" WHERE 1 = 1 \n ");
        }
        sql.append(" AND campobi = ").append(CampoBIEnum.ATIVOS_VENCIDOS_INICIO_MES.getCodigo()).append("\n");
        sql.append(" AND ((ano < ").append(ano).append(" ) OR (ano = ").append(ano).append(" AND mes <= ").append(mes).append("))\n");
        if (UteisValidacao.emptyNumber(empresa)) {
            sql.append(" GROUP BY ano, mes ");
        }
        sql.append(" ORDER BY ano DESC, mes DESC \n");
        sql.append(" LIMIT 6) AS reg ON reg.ano = ca.ano AND reg.mes = ca.mes \n");
        sql.append(" LEFT JOIN ( \n");
        if(consultarPeloDRE) {
            sql.append(" SELECT mes, ano, empresa, receita, despesa FROM dresinteticodw \n");
        } else {
            sql.append(" SELECT mes, ano, empresa, receita, despesa FROM dfsinteticodw \n");
        }
        sql.append(") AS d on d.mes = ca.mes AND d.ano = ca.ano AND d.empresa = ca.empresa \n");
        sql.append(" WHERE ca.campobi IN (");
        sql.append(CampoBIEnum.ATIVOS_VENCIDOS_INICIO_MES.getCodigo()).append(",");
        sql.append(CampoBIEnum.ATIVOS_VENCIDOS_FIM_MES.getCodigo()).append(",");
        sql.append(CampoBIEnum.BOLSAS_MES.getCodigo()).append(",");
        sql.append(CampoBIEnum.ATIVOS.getCodigo()).append(",");
        sql.append(CampoBIEnum.CAIXA_POR_COMPETENCIA.getCodigo()).append(",");
        sql.append(CampoBIEnum.CAIXA_POR_RECEITA.getCodigo()).append(",");
        sql.append(CampoBIEnum.VENCIDOS_FIM_MES.getCodigo()).append(")");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND ca.empresa =  ").append(empresa);
        } else {
            sql.append(" AND ca.empresa != 0 ");
            sql.append(" GROUP BY ca.campobi, ca.mes, ca.ano ");
        }
        sql.append("ORDER BY ca.ano, ca.mes, ca.campobi ");
        
        ResultSet rs = criarConsulta(sql.toString(), con);
        while(rs.next()){
            String mesAno = (rs.getInt("mes") < 10 ? "0" : "")+rs.getInt("mes")+"/"+rs.getInt("ano");
            TicketMedioVO tm = mapa.get(mesAno);
            if(tm == null){
                tm = new TicketMedioVO();
                tm.setAno(rs.getInt("ano"));
                tm.setMes(rs.getInt("mes"));
                mapa.put(mesAno, tm);
            }
            CampoBIEnum campo = CampoBIEnum.getFromCodigo(rs.getInt("campobi"));
            if(campo != null){
                switch(campo){
                    case ATIVOS: 
                        tm.setAtivos(rs.getInt("valor"));
                        break;
                    case ATIVOS_VENCIDOS_FIM_MES: 
                        tm.setAtivosVencidosFimMes(rs.getInt("valor"));
                        break;
                    case ATIVOS_VENCIDOS_INICIO_MES: 
                        tm.setAtivosVencidosInicioMes(rs.getInt("valor"));
                        break;
                    case BOLSAS_MES:
                        tm.setBolsas(rs.getInt("valor"));
                        break;
                    case CAIXA_POR_COMPETENCIA:
                        tm.setCaixaCompetencia(rs.getDouble("valor"));
                        break;
                    case CAIXA_POR_RECEITA:
                        tm.setCaixaReceita(rs.getDouble("receita"));
                        tm.setDespesaTotalMes(rs.getDouble("despesa"));
                        break;
                }
            }
        }
        List<TicketMedioJSON> lista = new ArrayList<>();
        for (String key : mapa.keySet()) {
            TicketMedioVO ticket = mapa.get(key);
            ticket.setMediaAtivosVencidos((ticket.getAtivosVencidosInicioMes() + ticket.getAtivosVencidosFimMes()) / 2);
            if (incluirBolsas) {
                ticket.setPagantes(usarMedia || ticket.getAtivos() == null || ticket.getAtivos().equals(0) ? ticket.getMediaAtivosVencidos() : ticket.getAtivos());
            } else {
                ticket.setPagantes((usarMedia || ticket.getAtivos() == null || ticket.getAtivos().equals(0) ? ticket.getMediaAtivosVencidos() : ticket.getAtivos()) - ticket.getBolsas());
            }
            ticket.setTicketCompetencia(ticket.getPagantes() == null || ticket.getPagantes() == 0.0 ? 0.0 : (ticket.getCaixaCompetencia() / ticket.getPagantes()));
            ticket.setTicketReceita(ticket.getPagantes() == null || ticket.getPagantes() == 0.0 ? 0.0 : (ticket.getCaixaReceita() / ticket.getPagantes()));
            ticket.setDespesaPorAluno(ticket.getPagantes() == null || ticket.getPagantes() == 0.0 ? 0.0 : (ticket.getDespesaTotalMes() / ticket.getPagantes()));
            lista.add(new TicketMedioJSON(ticket.getMes(), ticket.getAno(), ticket.getTicketReceita(), 
                    ticket.getTicketCompetencia(), ticket.getDespesaPorAluno()));
        }
        return Ordenacao.ordenarLista(lista, "mesAnoOrdem");
        
    }

    public JSONObject gerarTicketMedioApp(Boolean atualizar, Integer empresa, String database, Integer mes, Integer ano, Boolean consultarPeloDRE, Boolean incluirBolsa) throws Exception {
        ConfiguracaoBI dao = new ConfiguracaoBI(con);
        List<ConfiguracaoBIVO> configuracoes = dao.consultarPorBI(BIEnum.TICKET_MEDIO, empresa);
        //-------------------------------
        configuracoes.removeIf(c -> c.getConfiguracao().equals(ConfiguracaoBIEnum.INCLUIR_PRODUTOS_RECEITA));
        TicketMedioVO ticketMedioVO = montarTicketMedio(atualizar, configuracoes, empresa, Uteis.getDate(database));
        JSONObject json = new JSONObject();
        json.put("ativosVencidosInicioMes", ticketMedioVO.getAtivosVencidosInicioMes());
        json.put("ativosVencidosFimMes", ticketMedioVO.getAtivosVencidosFimMes());
        json.put("mediaAtivosVencidos", ticketMedioVO.getMediaAtivosVencidos());
        json.put("bolsas", ticketMedioVO.getBolsas());
        json.put("ativos", ticketMedioVO.getAtivos());
        json.put("pagantes", ticketMedioVO.getPagantes());
        json.put("caixaCompetencia", ticketMedioVO.getCaixaCompetencia());
        json.put("ticketCompetencia", ticketMedioVO.getTicketCompetencia());
        json.put("caixaFaturamentoRecebido", ticketMedioVO.getCaixaFaturamentoRecebido());
        json.put("ticketFaturamentoRecebido", ticketMedioVO.getTicketFaturamentoRecebido());
        json.put("ticketReceita", ticketMedioVO.getTicketReceita());
        json.put("caixaReceita", ticketMedioVO.getCaixaReceita());
        json.put("despesaTotalMes", ticketMedioVO.getDespesaTotalMes());
        json.put("despesaPorAluno", ticketMedioVO.getDespesaPorAluno());
        json.put("ultimaExecucaoDespesa", ticketMedioVO.getUltimaExecucaoDespesa());
        json.put("corDespesa", ticketMedioVO.getCorDespesa());
        json.put("mes", mes);
        json.put("ultimaAtualizacao", ticketMedioVO.getUltimaAtualizacao().getTime());
        json.put("ano", ano);
        json.put("ticketReceitaFormatado", ticketMedioVO.getTicketReceitaFormatado());
        json.put("ticketCompetenciaFormatado", ticketMedioVO.getTicketCompetenciaFormatado());
        json.put("despesaPorAlunoFormatado", ticketMedioVO.getDespesaPorAlunoFormatado());
        json.put("dependentesInicioMes", ticketMedioVO.getDependentesInicioMes());
        json.put("dependentesFimMes", ticketMedioVO.getDependentesFimMes());
        JSONObject content = new JSONObject().put("content", json);

        List<TicketMedioJSON> ticketMedioJSONS = consultarParaGrafico(empresa,
                incluirBolsa,
                getConfiguracao(ConfiguracaoBIEnum.ATIVOS, configuracoes).getValorAsInteger().equals(1),
                mes, ano, consultarPeloDRE);
        JSONArray grafico = new JSONArray();
        for(TicketMedioJSON tm : ticketMedioJSONS){
            grafico.put(tm.toJSONObject());
        }
        content.put("grafico", grafico);
        return content;
    }
}

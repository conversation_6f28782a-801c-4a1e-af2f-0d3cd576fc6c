package relatorio.negocio.jdbc.arquitetura;

import java.io.StringWriter;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.List;
import java.util.Vector;
import java.util.stream.Collectors;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import controle.arquitetura.security.LoginControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import relatorio.negocio.jdbc.financeiro.ParcelaEmAbertoRelTO;
import relatorio.negocio.jdbc.financeiro.ParcelaSPCTO;

/**
 * SuperRelatorio é uma classe padrão para que encapsulam as operações básicas relativas a emissão de relatórios.
 * Verificar a permissão do usuário para realizar a emissão de um determinado relatório.
 */
public class SuperRelatorio extends SuperEntidade {

    protected String xmlRelatorio;
    protected Vector ordenacoesRelatorio;
    protected int ordenarPor;
    private String descricaoFiltros;

    public SuperRelatorio() throws Exception {
        super();
        setXmlRelatorio("");
        setOrdenacoesRelatorio(new Vector());
        setOrdenarPor(0);
        setDescricaoFiltros("");
    }

    public SuperRelatorio(Connection connection) throws Exception {
        super(connection);
        setXmlRelatorio("");
        setOrdenacoesRelatorio(new Vector());
        setOrdenarPor(0);
        setDescricaoFiltros("");
    }

    public void adicionarDescricaoFiltro(String filtro) {
        if (!descricaoFiltros.equals("")) {
            descricaoFiltros = descricaoFiltros + ";" + filtro;
        } else {
            descricaoFiltros = filtro;
        }
    }

    /**
     * Operação padrão para realizar o EMITIR UM RELATÓRIO de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD.
     * Verifica se o usuário logado possui permissão de acesso a operação EMITIRRELATORIO.
     * @param idEntidade  Nome da entidade para a qual se deseja realizar a operação.
     * @exception Exception  Caso haja problemas de conexão ou restrição de acesso a esta operação.
     */
    public void emitirRelatorio(String idEntidade, boolean verificarAcesso) throws Exception {
//        new ControleAcesso().emitirRelatorio(idEntidade, verificarAcesso);
        getFacade().getControleAcesso().emitirRelatorio(idEntidade, verificarAcesso);
    }

    public String adicionarCondicionalWhere(String whereStr, String filtro, boolean operadorAND) {
        if (!operadorAND) {
            return filtro;
        } else {
            return whereStr + " AND " + filtro;
        }
    }

    public String getStringFromDocument(Document doc) {
        try {
            DOMSource domSource = new DOMSource(doc);
            StringWriter writer = new StringWriter();
            StreamResult result = new StreamResult(writer);
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer transformer = tf.newTransformer();
            transformer.transform(domSource, result);
            String xmlConvertido = writer.toString();
            xmlConvertido = xmlConvertido.replaceFirst("UTF-8", "ISO-8859-1");
            return xmlConvertido;
        } catch (TransformerException ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public void converterResultadoConsultaParaXML(String nomeRelatorio, String nomeRegistro, ResultSet resultadoConsulta) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.newDocument();
        // Criando o nó raiz do XML - com o nome padrão do relatório
        Element root = doc.createElement(nomeRelatorio);
        // Obtendo as informações de estrutura do ResultSet - METADados
        ResultSetMetaData metaDadosConsulta = resultadoConsulta.getMetaData();

        int cols = metaDadosConsulta.getColumnCount();
        while (resultadoConsulta.next()) {
            Element linhaXML = doc.createElement(nomeRegistro);

            for (int j = 1; j <= cols; j++) {
                String nomeColuna = metaDadosConsulta.getColumnName(j);
                String nomeClasse = metaDadosConsulta.getColumnClassName(j);
                String valorColuna = " ";
                if (nomeClasse.equals("java.sql.Timestamp")) {
                    valorColuna = Uteis.getData(resultadoConsulta.getDate(j));
                    if ((valorColuna == null) || (valorColuna.equals("")) || (valorColuna.equals("null"))) {
                        valorColuna = " ";
                    }
                } else if (nomeClasse.equals("java.lang.Double")) {
                    valorColuna = String.valueOf(resultadoConsulta.getDouble(j));
                    if (valorColuna == null) {
                        valorColuna = "0.0";
                    }
                } else if (nomeClasse.equals("java.lang.Float")) {
                    valorColuna = String.valueOf(resultadoConsulta.getFloat(j));
                    if (valorColuna == null) {
                        valorColuna = "0.0";
                    }
                } else {
                    valorColuna = resultadoConsulta.getString(j);
                    if ((valorColuna == null) || (valorColuna.equals("")) || (valorColuna.equals("null"))) {
                        valorColuna = " ";
                    }
                }
                Element e = doc.createElement(nomeColuna);
                e.appendChild(doc.createTextNode(valorColuna.replaceAll("\u001F","")));
                linhaXML.appendChild(e);
            }
            root.appendChild(linhaXML);
        }
        doc.appendChild(root);
        this.setXmlRelatorio(getStringFromDocument(doc));
    }

    public void converterResultadoConsultaParaXML(List<ParcelaEmAbertoRelTO> lista, String nomeRelatorio, String nomeRegistro) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.newDocument();
        // Criando o nó raiz do XML - com o nome padrão do relatório
        Element root = doc.createElement(nomeRelatorio);

        lista.forEach(el -> {
            Element linhaXML = doc.createElement(nomeRegistro);

            Element e = doc.createElement("movparcela_codigo");
            e.appendChild(doc.createTextNode(String.valueOf(el.getParcela()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("cliente_matricula");
            e.appendChild(doc.createTextNode(el.getMatricula().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("pessoa_nome");
            e.appendChild(doc.createTextNode(el.getNome().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_descricao");
            e.appendChild(doc.createTextNode(el.getDescricaoParcela().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_dataregistro");
            if ((el.getDataFatura() == null) || (el.getDataFatura().equals("")) || (el.getDataFatura().equals("null"))) {
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            }else{
                e.appendChild(doc.createTextNode(Uteis.getData(el.getDataFatura()).replaceAll("\u001F","")));
            }

            linhaXML.appendChild(e);

            e = doc.createElement("parcela_datavencimento");
            if ((el.getDateVencimento() == null) || (el.getDateVencimento().equals("")) || (el.getDateVencimento().equals("null"))) {
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            }else{
                e.appendChild(doc.createTextNode(Uteis.getData(el.getDateVencimento()).replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_situacao");
            e.appendChild(doc.createTextNode(el.getSituacao().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("regime_recorrencia");
            e.appendChild(doc.createTextNode(el.getRecorrencia().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_contrato");
            e.appendChild(doc.createTextNode(String.valueOf(el.getContrato()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_valorparcela");
            e.appendChild(doc.createTextNode(String.valueOf(el.getValor()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("total");
            e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("datapagamento");
            if ((el.getDataPagamento() == null) || (el.getDataPagamento().equals("")) || (el.getDataPagamento().equals("null"))) {
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            }else{
                e.appendChild(doc.createTextNode(Uteis.getData(el.getDataPagamento()).replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("telefone");
            e.appendChild(doc.createTextNode(el.getTelefone().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("primeiro_telefone");
            e.appendChild(doc.createTextNode(el.getPrimeiroTelefone().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("nomeempresa");
            e.appendChild(doc.createTextNode(el.getNomeempresa().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("multa");
            e.appendChild(doc.createTextNode(String.valueOf(el.getJuros()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("juros");
            e.appendChild(doc.createTextNode(String.valueOf(el.getMultas()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("formas_pagamento");
            if(UteisValidacao.emptyString(el.getFormasPagamento())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getFormasPagamento().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("pessoa_cpf");
            if(UteisValidacao.emptyString(el.getCpf())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getCpf().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("modalidades");
            if(UteisValidacao.emptyString(el.getModalidades())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getModalidades().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("endereco");
            if(UteisValidacao.emptyString(el.getEndereco())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getEndereco().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            root.appendChild(linhaXML);
        });


        doc.appendChild(root);
        this.setXmlRelatorio(getStringFromDocument(doc));
    }

    public void converterResultadoConsultaGestaoNegativacaoParaXML(List<ParcelaSPCTO> lista, String nomeRelatorio, String nomeRegistro) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.newDocument();
        // Criando o nó raiz do XML - com o nome padrão do relatório
        Element root = doc.createElement(nomeRelatorio);

        lista.forEach(el -> {
            Element linhaXML = doc.createElement(nomeRegistro);

            Element e = doc.createElement("movparcela_codigo");
            e.appendChild(doc.createTextNode(String.valueOf(el.getParcela()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("cliente_matricula");
            e.appendChild(doc.createTextNode(el.getMatricula().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("pessoa_nome");
            e.appendChild(doc.createTextNode(el.getNome().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_descricao");
            e.appendChild(doc.createTextNode(el.getDescricaoParcela().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_dataregistro");
            if ((el.getDataFatura() == null) || (el.getDataFatura().equals("")) || (el.getDataFatura().equals("null"))) {
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            }else{
                e.appendChild(doc.createTextNode(Uteis.getData(el.getDataFatura()).replaceAll("\u001F","")));
            }

            linhaXML.appendChild(e);

            e = doc.createElement("parcela_datavencimento");
            if ((el.getDateVencimento() == null) || (el.getDateVencimento().equals("")) || (el.getDateVencimento().equals("null"))) {
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            }else{
                e.appendChild(doc.createTextNode(Uteis.getData(el.getDateVencimento()).replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_situacao");
            e.appendChild(doc.createTextNode(el.getSituacao().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("regime_recorrencia");
            e.appendChild(doc.createTextNode(el.getRecorrencia().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_contrato");
            e.appendChild(doc.createTextNode(String.valueOf(el.getContrato()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("parcela_valorparcela");
            e.appendChild(doc.createTextNode(String.valueOf(el.getValor()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("total");
            e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("datapagamento");
            if ((el.getDataPagamento() == null) || (el.getDataPagamento().equals("")) || (el.getDataPagamento().equals("null"))) {
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            }else{
                e.appendChild(doc.createTextNode(Uteis.getData(el.getDataPagamento()).replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("telefone");
            e.appendChild(doc.createTextNode(el.getTelefone().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("primeiro_telefone");
            e.appendChild(doc.createTextNode(el.getPrimeiroTelefone().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("nomeempresa");
            e.appendChild(doc.createTextNode(el.getNomeempresa().replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("multa");
            e.appendChild(doc.createTextNode(String.valueOf(el.getJuros()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("juros");
            e.appendChild(doc.createTextNode(String.valueOf(el.getMultas()).replaceAll("\u001F","")));
            linhaXML.appendChild(e);

            e = doc.createElement("formas_pagamento");
            if(UteisValidacao.emptyString(el.getFormasPagamento())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getFormasPagamento().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("pessoa_cpf");
            if(UteisValidacao.emptyString(el.getCpf())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getCpf().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

            e = doc.createElement("modalidades");
            if(UteisValidacao.emptyString(el.getModalidades())){
                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
            } else{
                e.appendChild(doc.createTextNode(el.getModalidades().replaceAll("\u001F","")));
            }
            linhaXML.appendChild(e);

//            e = doc.createElement("endereco");
//            if(UteisValidacao.emptyString(el.getEndereco().getEndereco())){
//                e.appendChild(doc.createTextNode(" ".replaceAll("\u001F","")));
//            } else{
//                e.appendChild(doc.createTextNode(el.getEndereco().replaceAll("\u001F","")));
//            }
//            linhaXML.appendChild(e);

            root.appendChild(linhaXML);
        });


        doc.appendChild(root);
        this.setXmlRelatorio(getStringFromDocument(doc));
    }


    public EmpresaVO getEmpresaLogado(){
        if (context() != null) {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getEmpresa();
        } else {
            return new EmpresaVO();
        }
    }

    public String getXmlRelatorio() {
        return xmlRelatorio;
    }

    public void setXmlRelatorio(String xmlRelatorio) {
        this.xmlRelatorio = xmlRelatorio;
    }

    public Vector getOrdenacoesRelatorio() {
        return ordenacoesRelatorio;
    }

    public void setOrdenacoesRelatorio(Vector ordenacoesRelatorio) {
        this.ordenacoesRelatorio = ordenacoesRelatorio;
    }

    public int getOrdenarPor() {
        return ordenarPor;
    }

    public void setOrdenarPor(int ordenarPor) {
        this.ordenarPor = ordenarPor;
    }

    public String getDescricaoFiltros() {
        return descricaoFiltros;
    }

    public void setDescricaoFiltros(String descricaoFiltros) {
        this.descricaoFiltros = descricaoFiltros;
    }
}

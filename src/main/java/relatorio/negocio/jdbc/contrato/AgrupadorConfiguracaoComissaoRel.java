package relatorio.negocio.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.ComissaoConfiguracaoVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.utilitarias.Ordenacao;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by glauco on 24/01/14.
 */
public class AgrupadorConfiguracaoComissaoRel extends SuperTO {

    private ComissaoConfiguracaoVO configuracao;
    private List<ComissaoRel> comissoes = new ArrayList<ComissaoRel>();
    private String tipoValorComissoes = "";
    private ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO;

    public ComissaoConfiguracaoVO getConfiguracao() {
        return configuracao;
    }

    public void setConfiguracao(ComissaoConfiguracaoVO configuracao) {
        this.configuracao = configuracao;
    }

    public List<ComissaoRel> getComissoes() {
        return comissoes;
    }

    public void setComissoes(List<ComissaoRel> comissoes) {
        this.comissoes = comissoes;
    }

    public String getConfiguracao_apresentar() {
        String taxaApresentar = "";
        if (getConfiguracao() instanceof ComissaoGeralConfiguracaoVO) {
            ComissaoGeralConfiguracaoVO configPlano = ((ComissaoGeralConfiguracaoVO) getConfiguracao());
            String valorFixoAgendado_Apresentar = configPlano.getValorFixoAgendado_Apresentar();
            String valorFixoEspontaneo_Apresentar = configPlano.getValorFixoEspontaneo_Apresentar();
            String porcentagemEspontaneo_Apresentar = configPlano.getPorcentagemEspontaneo_Apresentar();
            String porcentagemAgendado_Apresentar = configPlano.getPorcentagemAgendado_Apresentar();
            if (comissaoMetaFinananceiraVO != null) {
                valorFixoAgendado_Apresentar = comissaoMetaFinananceiraVO.getValorAgendado_Apresentar();
                valorFixoEspontaneo_Apresentar = comissaoMetaFinananceiraVO.getValorEspontaneo_Apresentar();
                porcentagemEspontaneo_Apresentar = comissaoMetaFinananceiraVO.getPorcentagemEspontaneo_Apresentar();
                porcentagemAgendado_Apresentar = comissaoMetaFinananceiraVO.getPorcentagemAgendado_Apresentar();
            }
            if (getTipoValorComissoes().equals("FIXO")) {
                if (valorFixoAgendado_Apresentar.equals(valorFixoEspontaneo_Apresentar)) {
                    taxaApresentar = "(" + valorFixoAgendado_Apresentar + ")";
                } else {
                    taxaApresentar = "(" + valorFixoAgendado_Apresentar + "/" + valorFixoEspontaneo_Apresentar + ")";
                }
            } else if (getTipoValorComissoes().equals("PORC")) {
                if (porcentagemAgendado_Apresentar.equals(porcentagemEspontaneo_Apresentar)) {
                    taxaApresentar = "(" + porcentagemAgendado_Apresentar + ")";
                } else {
                    taxaApresentar = "(" + porcentagemAgendado_Apresentar + "/" + porcentagemEspontaneo_Apresentar + ")";
                }
            }
        } else {
            ComissaoProdutoConfiguracaoVO configProduto = ((ComissaoProdutoConfiguracaoVO) getConfiguracao());
            String valorFixo = configProduto.getValorFixo_Apresentar();
            String porcentagem = configProduto.getPorcentagemApresentar();
            if (getTipoValorComissoes().equals("FIXO")) {
                taxaApresentar = "(" + valorFixo + ")";
            } else {
                taxaApresentar = "(" + porcentagem + ")";
            }
        }
        return getConfiguracao().toString() + " " + taxaApresentar;
    }

    public JRDataSource getListaComissoes() {
        Ordenacao.ordenarLista(getComissoes(), "nomePessoa");
        return new JRBeanArrayDataSource(getComissoes().toArray());
    }

    public String getValorTotalConfiguracao() {
        double valor = getValorConfiguracao();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public double getValorConfiguracao() {
        double valor = 0.0;
        for (ComissaoRel comissao : comissoes) {
            valor += comissao.getValor();
        }
        return valor;
    }

    public String getValorComissaoConfiguracao() {
        double valor = getValorComissao();
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(valor);
    }

    public double getValorComissao() {
        double valor = 0.0;
        for (ComissaoRel comissao : comissoes) {
            valor += comissao.getValorDaComissao();
        }
        return valor;
    }

    public int getQtdComissoes() {
        return comissoes.size();
    }

    public int getQtdAlunos() {
        List<String> alunos = new ArrayList<String>();
        for (ComissaoRel comissaoRel : comissoes) {
            if (!alunos.contains(comissaoRel.getMatriculaCliente())) {
                alunos.add(comissaoRel.getMatriculaCliente());
            }
        }
        return alunos.size();
    }

    public String getTipoValorComissoes() {
        return tipoValorComissoes;
    }

    public void setTipoValorComissoes(String tipoValorComissoes) {
        this.tipoValorComissoes = tipoValorComissoes;
    }

    public ComissaoMetaFinananceiraVO getComissaoMetaFinananceiraVO() {
        return comissaoMetaFinananceiraVO;
    }

    public void setComissaoMetaFinananceiraVO(ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO) {
        this.comissaoMetaFinananceiraVO = comissaoMetaFinananceiraVO;
    }
    
    public int getQtdContratos() {
        List<Integer> contratos = new ArrayList<Integer>();
        for (ComissaoRel comissaoRel : comissoes) {
            if (!contratos.contains(comissaoRel.getCodigoContrato())) {
                contratos.add(comissaoRel.getCodigoContrato());
            }
        }
        return contratos.size();
    }

    public boolean verificarListaComissaoMeta() {
        if (getConfiguracao() instanceof ComissaoGeralConfiguracaoVO) {
            ComissaoGeralConfiguracaoVO configuracaoVO = (ComissaoGeralConfiguracaoVO) getConfiguracao();
            return configuracaoVO.getListaComissaoMeta().isEmpty();
        }
        return true;
    }
}

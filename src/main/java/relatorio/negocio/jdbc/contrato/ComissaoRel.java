package relatorio.negocio.jdbc.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.ComissaoConfiguracaoVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.contrato.ComissaoProdutoConfiguracaoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.financeiro.MetaConsultorMesTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.contrato.ComissaoRelTO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created by glauco on 21/01/14
 */
public class ComissaoRel extends SuperTO {

    private String matriculaCliente = "";
    private String nomePessoa = "";
    private Integer codigoCliente = 0;
    private Integer codigoPessoa = 0;
    private ColaboradorVO consultorResponsavel = new ColaboradorVO();
    private ColaboradorVO responsavelRecebimento = new ColaboradorVO();
    private UsuarioVO responsavelLancamento = new UsuarioVO();
    private Double valor = 0.0;
    private String formaPagamento = "";
    private Integer codigoContrato = 0;
    private String situacaoContrato = "";
    private TipoContratoEnum contratoAgendadoEspontaneo;
    private Integer duracaoContrato = 0;
    private String tipoContrato = "";
    private Double valorContrato = 0.0;
    private String nomePlano = "";
    private Date dataPagamento = new Date();
    private Date dataCompensacao = new Date();
    private String produtosPagos = "";
    private Map<Integer, Double> mapaContratoValor = new HashMap<Integer, Double>();
    private Map<Integer, Double> mapaProdutoValor = new HashMap<Integer, Double>();
    private Double valorDaComissao = 0.0;
    private ComissaoGeralConfiguracaoVO configuracaoVO = new ComissaoGeralConfiguracaoVO();
    private ComissaoProdutoConfiguracaoVO configuracaoProdutoVO = new ComissaoProdutoConfiguracaoVO();
    private Integer codigoRecibo = 0;
    private int qtdParcelas = 0;
    private ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO;
    private Boolean devolucaoCheque = false;
    private Boolean retornoCheque = false;


    public String getTipoContratoApresentar() {
        if (getContratoAgendadoEspontaneo() == null) {
            return "";
        }
        if (getContratoAgendadoEspontaneo().equals(TipoContratoEnum.ESPONTANEO)) {
            return "ES";
        }
        if (getContratoAgendadoEspontaneo().equals(TipoContratoEnum.AGENDADO)) {
            return "AG";
        }
        return "";
    }

    public String getValor_apresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getValor());
    }

    public String getValorDaComissao_apresentar() {
        return Formatador.formatarValorMonetarioSemMoedaMantendoSinal(getValorDaComissao());
    }

    public String getValorContrato_apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorContrato());
    }

    public String getResponsavelLancamento_apresentar() {
        return getResponsavelLancamento().getNomeAbreviado();
    }

    public String getResponsavelRecebimento_apresentar() {
        return getResponsavelRecebimento().getPessoa_Apresentar();
    }

    public void processarProdutosPagos(boolean aceitaMatriculaRematricula, boolean aceitaManutencaoModalidade, boolean calcularComissaoProdutos) {
        String[] produtos = new String[0];
        if (getProdutosPagos() != null) {
            produtos = getProdutosPagos().split("\\|");
        }
        for (String infoProdutoTemp : produtos) {
            if (!infoProdutoTemp.isEmpty()) {
                String[] infoProduto = infoProdutoTemp.split(",");
                if (infoProduto[1].equals("PM") ||
                        (infoProduto[1].equals(TipoProduto.MANUTENCAO_MODALIDADE.getCodigo()) && aceitaManutencaoModalidade) ||
                        (infoProduto[1].equals("RE") && aceitaMatriculaRematricula) ||
                        (infoProduto[1].equals("MA") && aceitaMatriculaRematricula)) {
                    Integer codigoContrato = Integer.parseInt(infoProduto[2]);
                    if (codigoContrato > 0) {
                        if (getMapaContratoValor().containsKey(codigoContrato)) {
                            Double valorPagoProduto = Double.parseDouble(infoProduto[3]);
                            Double valorContrato = getMapaContratoValor().get(codigoContrato);
                            valorContrato += valorPagoProduto;
                            getMapaContratoValor().put(codigoContrato, valorContrato);
                        } else {
                            Double valorPagoProduto = Double.parseDouble(infoProduto[3]);
                            getMapaContratoValor().put(codigoContrato, valorPagoProduto);
                        }
                    }
                } else {
                    if (calcularComissaoProdutos || "CH".equals(infoProduto[1])) {
                        Integer codigoProduto = Integer.parseInt(infoProduto[0]);
                        if (getMapaProdutoValor().containsKey(codigoProduto)) {
                            Double valorPagoProduto = Double.parseDouble(infoProduto[3]);
                            Double valorProduto = getMapaProdutoValor().get(codigoProduto);
                            valorProduto += valorPagoProduto;
                            getMapaProdutoValor().put(codigoProduto, valorProduto);
                        } else {
                            Double valorPagoProduto = Double.parseDouble(infoProduto[3]);
                            getMapaProdutoValor().put(codigoProduto, valorPagoProduto);
                        }
                    }
                }
            }
        }
    }

    public void calcularValorComissao(List<ComissaoGeralConfiguracaoVO> comissoesEmpresa,
                                      Map<ComissaoGeralConfiguracaoVO, ArrayList<ComissaoRel>> mapaComissoes, String tipoValorComissoes) throws Exception {
        for (ComissaoGeralConfiguracaoVO comissao : comissoesEmpresa) {
            if (comissao.getSituacao().equals(this.getTipoContrato())
                    && comissao.getDuracao().equals(this.getDuracaoContrato())
                    && validarData(comissao, this)) {
                if (getContratoAgendadoEspontaneo().equals(TipoContratoEnum.AGENDADO)) {
                    if (tipoValorComissoes.equals("PORC")) {
                        setValorDaComissao(getValor() * (comissao.getPorcentagemAgendado() / 100));
                    } else if (tipoValorComissoes.equals("FIXO")) {
                        setValorDaComissao(comissao.getValorFixoAgendado());
                    } else {
                        setValorDaComissao(0.0);
                    }
                }
                if (getContratoAgendadoEspontaneo().equals(TipoContratoEnum.ESPONTANEO)) {
                    if (tipoValorComissoes.equals("PORC")) {
                        setValorDaComissao(getValor() * (comissao.getPorcentagemEspontaneo() / 100));
                    } else if (tipoValorComissoes.equals("FIXO")) {
                        setValorDaComissao(comissao.getValorFixoEspontaneo());
                    } else {
                        setValorDaComissao(0.0);
                    }
                }
                setConfiguracaoVO(comissao);
                ArrayList<ComissaoRel> comissoes = mapaComissoes.get(comissao);
                if (comissoes == null) {
                    comissoes = new ArrayList<ComissaoRel>();
                    comissoes.add(this);
                    mapaComissoes.put(comissao, comissoes);
                } else {
                    comissoes.add(this);
                }
            }
        }
    }

    public void calcularValorComissaoProduto(MovProdutoVO movProdutoVO, Map<ComissaoProdutoConfiguracaoVO, ArrayList<ComissaoRel>> mapaComissoesProdutos, String tipoValorComissoes) throws Exception {
        boolean encontrou = false;
        for (ComissaoProdutoConfiguracaoVO comissaoProduto : movProdutoVO.getProduto().getComissaoProdutos()) {
            if (comissaoProduto.getEmpresa().getCodigo().equals(movProdutoVO.getEmpresa().getCodigo())
                    && validarData(comissaoProduto, this)) {
                calcularValorDaComissaoProduto(tipoValorComissoes, comissaoProduto, movProdutoVO);
                encontrou = true;

                setConfiguracaoProdutoVO(comissaoProduto);
                ArrayList<ComissaoRel> comissoes = mapaComissoesProdutos.get(comissaoProduto);
                organizarMapa(mapaComissoesProdutos, comissaoProduto, comissoes);
            }
        }
        if (!encontrou) {
            for (ComissaoProdutoConfiguracaoVO comissaoProduto : movProdutoVO.getProduto().getCategoriaProduto().getComissaoCategoriaProdutos()) {
                if (comissaoProduto.getEmpresa().getCodigo().equals(movProdutoVO.getEmpresa().getCodigo())
                        && validarData(comissaoProduto, this)) {
                    calcularValorDaComissaoProduto(tipoValorComissoes, comissaoProduto, movProdutoVO);

                    setConfiguracaoProdutoVO(comissaoProduto);
                    ArrayList<ComissaoRel> comissoes = mapaComissoesProdutos.get(comissaoProduto);
                    organizarMapa(mapaComissoesProdutos, comissaoProduto, comissoes);
                }
            }
        }
    }

    public void organizarMapa(Map<ComissaoProdutoConfiguracaoVO, ArrayList<ComissaoRel>> mapaComissoesProdutos, ComissaoProdutoConfiguracaoVO comissaoProduto, ArrayList<ComissaoRel> comissoes) {
        if (comissoes == null) {
            comissoes = new ArrayList<ComissaoRel>();
            comissoes.add(this);
            mapaComissoesProdutos.put(comissaoProduto, comissoes);
        } else {
            comissoes.add(this);
        }
    }

    public void calcularValorDaComissaoProduto(String tipoValorComissoes, ComissaoProdutoConfiguracaoVO comissaoProduto, MovProdutoVO movProdutoVO) {
        if (tipoValorComissoes.equals("PORC")) {
            setValorDaComissao(getValor() * (comissaoProduto.getPorcentagem() / 100));
        } else if (tipoValorComissoes.equals("FIXO")) {
            setValorDaComissao(movProdutoVO.getQuantidade() * comissaoProduto.getValorFixo());
        } else {
            setValorDaComissao(0.0);
        }
    }

    public void calcularValorComissaoSeAtingirMetaFinanceira(MetaConsultorMesTO metaAtingida, String tipoValorComissoes) throws Exception {
        ComissaoGeralConfiguracaoVO cgc = getConfiguracaoVO();

        Double porcentagemEspontaneo = cgc.getPorcentagemEspontaneo();
        Double valorEspontaneo = cgc.getValorFixoEspontaneo();
        Double porcentagemAgendado = cgc.getPorcentagemAgendado();
        Double valorAgendado = cgc.getValorFixoAgendado();

        for (ComissaoMetaFinananceiraVO comissao: cgc.getListaComissaoMeta()){
            if (comissao.getCodigoMeta().equals(metaAtingida.getMetaFinanceiraEmpresaValoresVO().getNumeroMeta())){
                porcentagemEspontaneo = comissao.getPorcentagemEspontaneo();
                valorEspontaneo = comissao.getValorEspontaneo();
                porcentagemAgendado = comissao.getPorcentagemAgendado();
                valorAgendado = comissao.getValorAgendado();

                this.comissaoMetaFinananceiraVO = comissao;
            }
        }
        if (getContratoAgendadoEspontaneo().equals(TipoContratoEnum.ESPONTANEO)) {
            if (tipoValorComissoes.equals("PORC")) {

                setValorDaComissao(getValor() * (porcentagemEspontaneo / 100));
            } else if (tipoValorComissoes.equals("FIXO")) {
                setValorDaComissao(valorEspontaneo);
            } else {
                setValorDaComissao(0.0);
            }
        }else{
            if (tipoValorComissoes.equals("PORC")) {
                setValorDaComissao(getValor() * (porcentagemAgendado / 100));
            } else if (tipoValorComissoes.equals("FIXO")) {
                setValorDaComissao(valorAgendado);
            } else {
                setValorDaComissao(0.0);
            }
        }
    }

    private boolean validarData(ComissaoConfiguracaoVO comissao, ComissaoRel comissaoRel) throws Exception {
        if (comissao.getVigenciaInicio() == null) {
            return true;
        }
        Date dataFim = comissao.getVigenciaFinal();
        if (dataFim == null) {
            dataFim = Calendario.hoje();
        }
        Date inicio = Uteis.obterPrimeiroDiaMes(comissao.getVigenciaInicio());
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(dataFim);
        return Calendario.entre(comissaoRel.dataPagamento, inicio, fim);
    }

    public String getContratos() {
        StringBuilder contratos = new StringBuilder();
        for (Integer contrato : getMapaContratoValor().keySet()) {
            contratos.append(contrato).append(",");
        }
        if (contratos.length() > 1) {
            contratos.deleteCharAt(contratos.length() - 1);
        }
        return contratos.toString();
    }


    public String getMovProdutos() {
        StringBuilder movProdutos = new StringBuilder();
        for (Integer movProduto : getMapaProdutoValor().keySet()) {
            movProdutos.append(movProduto).append(",");
        }
        if (movProdutos.length() > 1) {
            movProdutos.deleteCharAt(movProdutos.length() - 1);
        }
        return movProdutos.toString();
    }

    public String getConfiguracao() {
        return getConfiguracaoVO().toString();
    }

    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getNomePessoa() {
        return nomePessoa;
    }

    public void setNomePessoa(String nomePessoa) {
        this.nomePessoa = nomePessoa;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public ColaboradorVO getConsultorResponsavel() {
        return consultorResponsavel;
    }

    public void setConsultorResponsavel(ColaboradorVO consultorResponsavel) {
        this.consultorResponsavel = consultorResponsavel;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Integer getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(Integer codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public TipoContratoEnum getContratoAgendadoEspontaneo() {
        return contratoAgendadoEspontaneo;
    }

    public void setContratoAgendadoEspontaneo(TipoContratoEnum contratoAgendadoEspontaneo) {
        this.contratoAgendadoEspontaneo = contratoAgendadoEspontaneo;
    }

    public Integer getDuracaoContrato() {
        return duracaoContrato;
    }

    public void setDuracaoContrato(Integer duracaoContrato) {
        this.duracaoContrato = duracaoContrato;
    }

    public String getTipoContrato() {
        return tipoContrato;
    }

    public void setTipoContrato(String tipoContrato) {
        this.tipoContrato = tipoContrato;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public UsuarioVO getResponsavelLancamento() {
        return responsavelLancamento;
    }

    public void setResponsavelLancamento(UsuarioVO responsavelLancamento) {
        this.responsavelLancamento = responsavelLancamento;
    }

    public String getProdutosPagos() {
        return produtosPagos;
    }

    public void setProdutosPagos(String produtosPagos) {
        this.produtosPagos = produtosPagos;
    }

    public Double getValorDaComissao() {
        return valorDaComissao;
    }

    public void setValorDaComissao(Double valorDaComissao) {
        this.valorDaComissao = valorDaComissao;
    }

    public Map<Integer, Double> getMapaContratoValor() {
        return mapaContratoValor;
    }

    public void setMapaContratoValor(Map<Integer, Double> mapaContratoValor) {
        this.mapaContratoValor = mapaContratoValor;
    }

    public Map<Integer, Double> getMapaProdutoValor() {
        return mapaProdutoValor;
    }

    public void setMapaProdutoValor(Map<Integer, Double> mapaProdutoValor) {
        this.mapaProdutoValor = mapaProdutoValor;
    }

    public ComissaoGeralConfiguracaoVO getConfiguracaoVO() {
        return configuracaoVO;
    }

    public void setConfiguracaoVO(ComissaoGeralConfiguracaoVO configuracaoVO) {
        this.configuracaoVO = configuracaoVO;
    }

    public ComissaoProdutoConfiguracaoVO getConfiguracaoProdutoVO() {
        return configuracaoProdutoVO;
    }

    public void setConfiguracaoProdutoVO(ComissaoProdutoConfiguracaoVO configuracaoProdutoVO) {
        this.configuracaoProdutoVO = configuracaoProdutoVO;
    }

    public Integer getCodigoRecibo() {
        return codigoRecibo;
    }

    public void setCodigoRecibo(Integer codigoRecibo) {
        this.codigoRecibo = codigoRecibo;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public Double getValorContrato() {
        return valorContrato;
    }

    public void setValorContrato(Double valorContrato) {
        this.valorContrato = valorContrato;
    }

    public ColaboradorVO getResponsavelRecebimento() {
        return responsavelRecebimento;
    }

    public void setResponsavelRecebimento(ColaboradorVO responsavelRecebimento) {
        this.responsavelRecebimento = responsavelRecebimento;
    }

    public int getQtdParcelas() {
        return qtdParcelas;
    }

    public void setQtdParcelas(int qtdParcelas) {
        this.qtdParcelas = qtdParcelas;
    }

    public ComissaoMetaFinananceiraVO getComissaoMetaFinananceiraVO() {
        return comissaoMetaFinananceiraVO;
    }

    public void setComissaoMetaFinananceiraVO(ComissaoMetaFinananceiraVO comissaoMetaFinananceiraVO) {
        this.comissaoMetaFinananceiraVO = comissaoMetaFinananceiraVO;
    }
    
    public ComissaoRelTO getTO(){
        ComissaoRelTO comissaoTO = new ComissaoRelTO();
        try{
            comissaoTO.setCodigoCliente(this.codigoCliente);
            comissaoTO.setNomePessoa(this.nomePessoa);
            comissaoTO.setCodigoPessoa(this.codigoPessoa);
            comissaoTO.setCodigoRecibo(this.codigoRecibo);
            comissaoTO.setMatriculaCliente(this.matriculaCliente);
            if(!UteisValidacao.emptyNumber(this.configuracaoVO.getCodigo())){
                 comissaoTO.setCodigoContrato(this.codigoContrato);
                 comissaoTO.setSituacaoContrato(this.situacaoContrato);
                 comissaoTO.setValorContrato(this.valorContrato);
                 comissaoTO.setContratoAgendadoEspontaneo(this.contratoAgendadoEspontaneo != null ?  contratoAgendadoEspontaneo.getDescricao(): "");
                 comissaoTO.setNomePlano(this.nomePlano);
                 comissaoTO.setDuracaoContrato(this.duracaoContrato);
                 comissaoTO.setTipoContrato(this.tipoContrato);
            } else {
                comissaoTO.setCodigoProduto(this.codigoContrato);
                comissaoTO.setValorProduto(this.valorContrato);
                comissaoTO.setNomeProduto(this.nomePlano);
            }
            comissaoTO.setCodigoConsultorResponsavel(this.getConsultorResponsavel().getCodigo());
            comissaoTO.setValor(this.valor);
            comissaoTO.setValorDaComissao(this.valorDaComissao);
            comissaoTO.setCodigoResponsavelRecebimento(this.getResponsavelRecebimento().getCodigo());
            comissaoTO.setResponsavelRecebimento(this.getResponsavelRecebimento_apresentar());
            comissaoTO.setConsultorResponsavel(this.getConsultorResponsavel().getPessoa_Apresentar());
            comissaoTO.setDataCompensacao(this.dataCompensacao);
            comissaoTO.setDataPagamento(this.dataPagamento);
            comissaoTO.setFormaPagamento(this.formaPagamento);
            comissaoTO.setProdutosPagos(this.produtosPagos);
            comissaoTO.setCodigoResponsavelLancamento(this.getResponsavelLancamento().getCodigo());
            comissaoTO.setResponsavelLancamento(this.getResponsavelLancamento_apresentar());
        } catch(Exception ignored){
        }
        return comissaoTO;
    }

    public Boolean getDevolucaoCheque() {
        return devolucaoCheque;
    }

    public void setDevolucaoCheque(Boolean devolucaoCheque) {
        this.devolucaoCheque = devolucaoCheque;
    }

    public Boolean getRetornoCheque() {
        return retornoCheque;
    }

    public void setRetornoCheque(Boolean retornoCheque) {
        this.retornoCheque = retornoCheque;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }
}

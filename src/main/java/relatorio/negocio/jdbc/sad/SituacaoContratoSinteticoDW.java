/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.sad;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.negocio.comuns.sad.SituacaoContratoSinteticoDWVO;

/**
 * 
 * <AUTHOR>
 */
public class SituacaoContratoSinteticoDW extends SuperEntidade {	

	public SituacaoContratoSinteticoDW() throws Exception {
		super();		
	}

        public SituacaoContratoSinteticoDWVO novo() throws Exception {
		incluir(getIdEntidade());
		SituacaoContratoSinteticoDWVO obj = new SituacaoContratoSinteticoDWVO();
		return obj;
	}

	public void incluir(SituacaoContratoSinteticoDWVO obj) throws Exception {
		try {
			con.setAutoCommit(false);
			incluirSemCommit(obj);
			con.commit();
		} catch (Exception e) {
			con.rollback();
			con.setAutoCommit(true);
			throw e;
		} finally {
			con.setAutoCommit(true);
		}
	}

	public void incluirSemCommit(SituacaoContratoSinteticoDWVO obj) throws Exception {
		try {
			SituacaoContratoSinteticoDWVO.validarDados(obj);
			obj.realizarUpperCaseDados();
			String sql = "INSERT INTO SituacaoContratoSinteticoDW ( dia, situacao, empresa, plano, peso, vinculoCarteira ) VALUES (  ?, ?, ?, ?, ?, ? )";
			PreparedStatement sqlInserir = con.prepareStatement(sql);
			sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
			sqlInserir.setString(2, obj.getSituacao());
			if (obj.getEmpresa().getCodigo().intValue() != 0) {
				sqlInserir.setInt(3, obj.getEmpresa().getCodigo().intValue());
			} else {
				sqlInserir.setNull(3, 0);
			}
			sqlInserir.setString(4, obj.getPlano());
			sqlInserir.setInt(5, obj.getPeso());
			sqlInserir.setString(6, obj.getVinculoCarteira());
			sqlInserir.execute();
			obj.setCodigo(obterValorChavePrimariaCodigo());
			obj.setNovoObj(new Boolean(false));
		} catch (Exception e) {
			throw e;
		}
	}

	public void alterar(SituacaoContratoSinteticoDWVO obj) throws Exception {
		try {
			con.setAutoCommit(false);
			alterarSemCommit(obj);
			con.commit();
		} catch (Exception e) {
			con.rollback();
			con.setAutoCommit(true);
			throw e;
		} finally {
			con.setAutoCommit(true);
		}
	}

	public void alterarSemCommit(SituacaoContratoSinteticoDWVO obj) throws Exception {
		try {
			SituacaoContratoSinteticoDWVO.validarDados(obj);
			obj.realizarUpperCaseDados();
			String sql = "UPDATE SituacaoContratoSinteticoDW set dia=?, situacao=?, empresa=?, plano=?, peso=?, vinculoCarteira=? WHERE ((codigo = ?))";
			PreparedStatement sqlAlterar = con.prepareStatement(sql);
			sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
			sqlAlterar.setString(2, obj.getSituacao());
			if (obj.getEmpresa().getCodigo().intValue() != 0) {
				sqlAlterar.setInt(3, obj.getEmpresa().getCodigo().intValue());
			} else {
				sqlAlterar.setNull(3, 0);
			}
			sqlAlterar.setString(4, obj.getPlano());
			sqlAlterar.setInt(5, obj.getPeso());
			sqlAlterar.setString(6, obj.getVinculoCarteira());
			sqlAlterar.setInt(7, obj.getCodigo().intValue());
			sqlAlterar.execute();
		} catch (Exception e) {
			throw e;
		}
	}

	public void excluir(SituacaoContratoSinteticoDWVO obj) throws Exception {
		try {
			con.setAutoCommit(false);
			// SituacaoContratoSinteticoDW.excluir(getIdEntidade());
			String sql = "DELETE FROM  SituacaoContratoSinteticoDW WHERE ((codigo = ?))";
			PreparedStatement sqlExcluir = con.prepareStatement(sql);
			sqlExcluir.setInt(1, obj.getCodigo().intValue());
			sqlExcluir.execute();
			con.commit();
		} catch (Exception e) {
			con.rollback();
			con.setAutoCommit(true);
			throw e;
		} finally {
			con.setAutoCommit(true);
		}
	}

	public void excluirSemCommit(SituacaoContratoSinteticoDWVO obj) throws Exception {
		try {
			String sql = "DELETE FROM  SituacaoContratoSinteticoDW WHERE ((codigo = ?))";
			PreparedStatement sqlExcluir = con.prepareStatement(sql);
			sqlExcluir.setInt(1, obj.getCodigo().intValue());
			sqlExcluir.execute();
		} catch (Exception e) {
			throw e;
		}
	}

	public void excluirSemCommitPorDiaEEmpresa(Date dia, Integer empresa) throws Exception {
		try {
			String sql = "DELETE FROM  SituacaoContratoSinteticoDW WHERE dia >= '" + Uteis.getDataJDBC(dia) + " 00:00:00' and  dia <= '" + Uteis.getDataJDBC(dia) + " 23:59:59'  and empresa = "+ empresa;
			PreparedStatement sqlExcluir = con.prepareStatement(sql);
			sqlExcluir.execute();
		} catch (Exception e) {
			throw e;
		}
	}
	public void excluirSemCommitPorDia(Date dia) throws Exception {
		try {
			String sql = "DELETE FROM  SituacaoContratoSinteticoDW WHERE dia >= '" + Uteis.getDataJDBC(dia) + " 00:00:00' and  dia <= '" + Uteis.getDataJDBC(dia) + " 23:59:59' ";
			PreparedStatement sqlExcluir = con.prepareStatement(sql);
			sqlExcluir.execute();
		} catch (Exception e) {
			throw e;
		}
	}

	public SituacaoContratoSinteticoDWVO consultarSituacaoSinteticoPorDiaVinculoCarteira(Date dia, String vinculo, String situacao, int nivelMontarDados) throws Exception {
		String sqlStr = "SELECT * FROM SituacaoContratoSinteticodw  WHERE CAST( dia AS DATE) = CAST( '" + Uteis.getDataJDBC(dia) + "' AS DATE) and vinculoCarteira = '" + vinculo + "' and situacao = '" + situacao.toUpperCase() + "'";
		PreparedStatement stm = con.prepareStatement(sqlStr);
		ResultSet tabelaResultado = stm.executeQuery();
		if (!tabelaResultado.next()) {
			return new SituacaoContratoSinteticoDWVO();
		}
		return (montarDados(tabelaResultado, nivelMontarDados));
	}

	public ResultSet consultarQuantidadeContratosSinteticosDefault(Date dataFim, Integer codigoEmpresa, int nivelMontarDados) throws Exception {
		String sqlStr = "SELECT * FROM SituacaoContratoSinteticodw  WHERE empresa = " + codigoEmpresa + "  AND dia >= '" + Uteis.getDataJDBC(dataFim) + " 00:00:00' AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59:59'";
		PreparedStatement stm = con.prepareStatement(sqlStr);
		ResultSet tabelaResultado = stm.executeQuery();
		return tabelaResultado;
	}

	public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
		List vetResultado = new ArrayList();
		while (tabelaResultado.next()) {
			SituacaoContratoSinteticoDWVO obj = new SituacaoContratoSinteticoDWVO();
			obj = montarDados(tabelaResultado, nivelMontarDados);
			vetResultado.add(obj);
		}
		return vetResultado;
	}

	public static SituacaoContratoSinteticoDWVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
		SituacaoContratoSinteticoDWVO obj = new SituacaoContratoSinteticoDWVO();
		obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
		obj.setDia(dadosSQL.getDate("dia"));
		obj.setSituacao(dadosSQL.getString("situacao"));
		obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("empresa")));
                if (dadosSQL.getString("plano") != null)
                    obj.setPlano(dadosSQL.getString("plano"));
                else
                    obj.setPlano("");
		obj.setVinculoCarteira(new String(dadosSQL.getString("vinculoCarteira")));
		obj.setPeso(dadosSQL.getInt("peso"));
		obj.setNovoObj(new Boolean(false));
		if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
			return obj;
		}
		montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
		return obj;
	}

	public static void montarDadosEmpresa(SituacaoContratoSinteticoDWVO obj, int nivelMontarDados) throws Exception {
		if (obj.getEmpresa().getCodigo().intValue() == 0) {
			obj.setEmpresa(new EmpresaVO());
			return;
		}
		obj.setEmpresa(new Empresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
	}	
}

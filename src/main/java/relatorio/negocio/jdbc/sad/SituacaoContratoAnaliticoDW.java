/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.sad;

import java.io.File;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import relatorio.negocio.comuns.sad.SituacaoContratoAnaliticoDWVO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

/**
 *
 * <AUTHOR>
 */
public class SituacaoContratoAnaliticoDW extends SuperRelatorio {    

    public SituacaoContratoAnaliticoDW() throws Exception {
        super();       
    }

    public SituacaoContratoAnaliticoDWVO novo() throws Exception {
        incluir(getIdEntidade());
        SituacaoContratoAnaliticoDWVO obj = new SituacaoContratoAnaliticoDWVO();
        return obj;
    }

    public void incluir(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {
            SituacaoContratoAnaliticoDWVO.validarDados(obj);
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO SituacaoContratoAnaliticoDW ( dia, situacao, empresa, plano, cliente, contrato, fonecliente, emailcliente, modalidadecliente,enderecoCliente ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?,? )";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setDate(1, Uteis.getDataJDBC(obj.getDia()));
            sqlInserir.setString(2, obj.getSituacao());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlInserir.setInt(3, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlInserir.setNull(3, 0);
            }
            if (obj.getPlano().getCodigo().intValue() != 0) {
                sqlInserir.setInt(4, obj.getPlano().getCodigo().intValue());
            } else {
                sqlInserir.setNull(4, 0);
            }
            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlInserir.setInt(5, obj.getCliente().getCodigo().intValue());
            } else {
                sqlInserir.setNull(5, 0);
            }
            if (obj.getContrato().getCodigo().intValue() != 0) {
                sqlInserir.setInt(6, obj.getContrato().getCodigo().intValue());
            } else {
                sqlInserir.setNull(6, 0);
            }
            sqlInserir.setString(7, obj.getFoneCliente());
            sqlInserir.setString(8, obj.getEmailCliente());
            sqlInserir.setString(9, obj.getModalidadeCliente());
            sqlInserir.setString(10, obj.getEnderecoCliente());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);
        } catch (Exception e) {
            throw e;
        }
    }

    public void alterar(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {

            SituacaoContratoAnaliticoDWVO.validarDados(obj);
            // SituacaoContratoAnaliticoDW.alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE SituacaoContratoAnaliticoDW set dia=?, situacao=?, empresa=?, plano=?, cliente=?, contrato=?, fonecliente=?, emailcliente=?, modalidadecliente=?, enderecoCliente=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setTimestamp(1, Uteis.getDataJDBCTimestamp(obj.getDia()));
            sqlAlterar.setString(2, obj.getSituacao());
            if (obj.getEmpresa().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(3, obj.getEmpresa().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(3, 0);
            }
            if (obj.getPlano().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(4, obj.getPlano().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(4, 0);
            }
            if (obj.getCliente().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(5, obj.getCliente().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(5, 0);
            }
            if (obj.getContrato().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(6, obj.getContrato().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(6, 0);
            }
            sqlAlterar.setString(7, obj.getFoneCliente());
            sqlAlterar.setString(8, obj.getEmailCliente());
            sqlAlterar.setString(9, obj.getModalidadeCliente());
            sqlAlterar.setString(10, obj.getEnderecoCliente());
            sqlAlterar.setInt(11, obj.getCodigo().intValue());
            sqlAlterar.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluir(SituacaoContratoAnaliticoDWVO obj) throws Exception {
        try {

            con.setAutoCommit(false);
            //  SituacaoContratoAnaliticoDW.excluir(getIdEntidade());
            String sql = "DELETE FROM  SituacaoContratoAnaliticoDW WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    public void excluirSituacaoAnaliticoEstornoContrato(Integer codigoContrato) throws Exception {
    	try {
    		String sql = "DELETE FROM  SituacaoContratoAnaliticoDW WHERE ((contrato = ?))";
    		PreparedStatement sqlExcluir = con.prepareStatement(sql);
    		sqlExcluir.setInt(1, codigoContrato.intValue());
    		sqlExcluir.execute();    		
    	} catch (Exception e) {    		
    		throw e;
    	}
    }    

    public ResultSet consultarDadosAnaliticoPorDiaParaGerarSintetico(Date data) throws Exception {
        String sqlStr = "SELECT Count(situacaoContratoAnaliticoDW.situacao) as peso, " +
                "situacaoContratoAnaliticoDW.empresa, " +
                "situacaoContratoAnaliticoDW.situacao, " +
                "situacaoContratoAnaliticoDW.plano, " +
                "ARRAY_TO_STRING( ARRAY( SELECT Vinculo.Colaborador FROM Vinculo WHERE situacaoContratoAnaliticoDW.cliente = Vinculo.cliente ), ':' )  as vinculoCarteira " +
                "FROM situacaoContratoAnaliticoDW " +
                "WHERE dia >= '" + Uteis.getDataJDBC(data) + " 00:00:00' " +
                "AND dia <=' " + Uteis.getDataJDBC(data) + " 23:59:59' " +
                "GROUP BY " +
                "empresa ,  situacao, plano, vinculoCarteira";
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return tabelaResultado;
    }
    public ResultSet consultarDadosAnaliticoPorDiaEEmpresaParaGerarSintetico(Date data, Integer empresa) throws Exception {
    	String sqlStr = "SELECT Count(situacaoContratoAnaliticoDW.situacao) as peso, " +
    	"situacaoContratoAnaliticoDW.empresa, " +
    	"situacaoContratoAnaliticoDW.situacao, " +
    	"situacaoContratoAnaliticoDW.plano, " +
    	"ARRAY_TO_STRING( ARRAY( SELECT Vinculo.Colaborador FROM Vinculo WHERE situacaoContratoAnaliticoDW.cliente = Vinculo.cliente ), ':' )  as vinculoCarteira " +
    	"FROM situacaoContratoAnaliticoDW " +
    	"WHERE dia >= '" + Uteis.getDataJDBC(data) + " 00:00:00' " +
    	"AND dia <=' " + Uteis.getDataJDBC(data) + " 23:59:59' " +
    	"AND empresa = " + empresa + " "+
    	"GROUP BY " +
    	"empresa ,  situacao, plano, vinculoCarteira";
    	PreparedStatement stm = con.prepareStatement(sqlStr);
    	ResultSet tabelaResultado = stm.executeQuery();
    	return tabelaResultado;
    }

    public ResultSet consultarQuantidadeContratosAnaliticoDefault(Date dataFim,
            Integer codigoEmpresa, int nivelMontarDados) throws Exception {

        String complementoSelecaoCampos = " contrato, cliente, fonecliente, emailcliente, empresa, situacao, plano, modalidadecliente, enderecocliente ";
        String complementoGroupBy = ", contrato, empresa, cliente, fonecliente, emailcliente, situacao, plano, modalidadecliente,enderecocliente ";
        String complementoWhere = "";
        if (codigoEmpresa != null && codigoEmpresa.intValue() != 0) {
            complementoSelecaoCampos += " ,empresa ";
            complementoWhere += " AND empresa = " + codigoEmpresa;
        }
        if (dataFim != null) {
            complementoWhere += " AND dia >= '" + Uteis.getDataJDBC(dataFim) + " 00:00' AND dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59'";
        }
        if (!complementoWhere.equals("")) {
            complementoWhere = complementoWhere.replaceFirst("AND", "WHERE ");
        }
        if (!complementoGroupBy.equals("")) {
            complementoGroupBy = complementoGroupBy.replaceFirst(",", " ");
        }
        String sqlStr = "SELECT  " + complementoSelecaoCampos + " FROM SituacaoContratoAnaliticoDW " + complementoWhere + " group by  " + complementoGroupBy;
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return tabelaResultado;

    }

    public List<SituacaoContratoAnaliticoDWVO> consultarQuantidadeContratosAnalitico(
            Date dataFim,
            String situacao,
            Integer codigoEmpresa,            
            String vinculo,
            String plano,
            int nivelMontarDados) throws Exception {

        String complementoWhere = "";
        if (codigoEmpresa != null && codigoEmpresa.intValue() != 0) {
            complementoWhere += " AND SituacaoContratoAnaliticodw.empresa = " + codigoEmpresa;
        }
        if (situacao.equals("VI")) {
            complementoWhere += " AND (SituacaoContratoAnaliticodw.situacao ='VI' or SituacaoContratoAnaliticodw.situacao ='PL' or SituacaoContratoAnaliticodw.situacao ='AA' or SituacaoContratoAnaliticodw.situacao ='DI') ";

        }
        if (situacao.equals("AT")) {
            complementoWhere += " AND (SituacaoContratoAnaliticodw.situacao ='NO' or SituacaoContratoAnaliticodw.situacao ='TR' or SituacaoContratoAnaliticodw.situacao ='TV' or SituacaoContratoAnaliticodw.situacao ='AV' or SituacaoContratoAnaliticodw.situacao ='VE')";

        }
        if (situacao.equals("IN")) {
            complementoWhere += " AND (SituacaoContratoAnaliticodw.situacao ='IN' or SituacaoContratoAnaliticodw.situacao ='CA' or SituacaoContratoAnaliticodw.situacao ='DE')";

        }
        if (!situacao.equals("VI") && !situacao.equals("AT") && !situacao.equals("IN") && !situacao.equals("")) {

            complementoWhere += " AND SituacaoContratoAnaliticodw.situacao ='" + situacao.toUpperCase() + "'";

        }
        if (dataFim != null) {
            complementoWhere += " AND SituacaoContratoAnaliticodw.dia >= '" + Uteis.getDataJDBC(dataFim) + " 00:00' AND SituacaoContratoAnaliticodw.dia <= '" + Uteis.getDataJDBC(dataFim) + " 23:59'";
        }

        if (!complementoWhere.equals("")) {
            complementoWhere = complementoWhere.replaceFirst("AND", "WHERE ");
        }

        String sqlStr = "SELECT SituacaoContratoAnaliticodw.* FROM SituacaoContratoAnaliticodw ";
        if (!vinculo.equals("")) {
        	sqlStr =  sqlStr + " INNER JOIN vinculo ON vinculo.cliente = SituacaoContratoAnaliticoDW.cliente AND (" + vinculo + " )" ;
        } 
        if (!plano.equals("")) {
        	sqlStr = sqlStr + " INNER JOIN plano ON plano.codigo = SituacaoContratoAnaliticoDW.plano AND ("+plano+" ) ";
        } 
        sqlStr = sqlStr + complementoWhere;
        PreparedStatement stm = con.prepareStatement(sqlStr);
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }


    public SituacaoContratoAnaliticoDWVO consultarPorClienteDia(Date dia, Integer cliente, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM SituacaoContratoAnaliticodw WHERE CAST( dia AS DATE) = CAST( '" + Uteis.getDataJDBC(dia) + "' AS DATE) and cliente = " + cliente.intValue();
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new SituacaoContratoAnaliticoDWVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List<SituacaoContratoAnaliticoDWVO> consultarPorClienteDiaLista(Date dia, Integer cliente, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM SituacaoContratoAnaliticodw WHERE CAST(dia AS DATE) = CAST( '" + Uteis.getDataJDBC(dia) + 
                     "' AS DATE) and cliente = " + cliente.intValue();
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    public SituacaoContratoAnaliticoDWVO consultarPorClienteDiaContrato(Date dia, Integer cliente, Integer contrato, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM SituacaoContratoAnaliticodw WHERE CAST( dia AS DATE) = CAST('" + Uteis.getDataJDBC(dia) + "' AS DATE) and cliente = " + cliente.intValue() + " and contrato = " + contrato;
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return new SituacaoContratoAnaliticoDWVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
    
    public Boolean consultarSeExisteRegistroAnaliticoPorClienteDia(Date dia, Integer cliente) throws Exception {
    	String sql = "SELECT * FROM SituacaoContratoAnaliticodw WHERE CAST( dia AS DATE) = CAST('" + Uteis.getDataJDBC(dia) + "' AS DATE) and cliente = " + cliente.intValue();
    	PreparedStatement stm = con.prepareStatement(sql);
    	ResultSet tabelaResultado = stm.executeQuery();
    	if (!tabelaResultado.next()) {
    		return false;
    	}
    	return true;
    }
    
    
    public Boolean consultarSeExisteRegistroAnaliticoPorClienteDiaContrato(Date dia, Integer cliente, Integer contrato) throws Exception {
    	String sql = "SELECT * FROM SituacaoContratoAnaliticodw WHERE CAST( dia AS DATE) = CAST('" + Uteis.getDataJDBC(dia) + "' AS DATE) and cliente = " + cliente.intValue() + " and contrato = " + contrato;
    	PreparedStatement stm = con.prepareStatement(sql);
    	ResultSet tabelaResultado = stm.executeQuery();
    	if (!tabelaResultado.next()) {
    		return false;
    	}
    	return true;
    }

    public Boolean consultarRotinaRobo(Date dia) throws Exception {
        String sql = "";
        sql = "SELECT * FROM SituacaoContratoAnaliticodw where CAST( dia AS DATE) = CAST( '" + Uteis.getDataJDBC(dia) + "' AS DATE)  limit 1 ";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;
    }

    public Boolean validarSeExisteDia(Date dia) throws Exception {
        String sql = "SELECT * FROM SituacaoContratoAnaliticodw where dia > '" + Uteis.getDataJDBC(dia) + " 23:59:59' limit 1";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            return false;
        }
        return true;
    }

    public Date consultaParaObterUltimoDiaprocessado() throws Exception {
        String sql = "SELECT MAX(SituacaoContratoAnaliticodw.dia) as dia  FROM SituacaoContratoAnaliticodw ";
        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet tabelaResultado = stm.executeQuery();
        if (!tabelaResultado.next()) {
            throw new Exception("Nao foi encontrado a ultima data processada.");
        }                
        return tabelaResultado.getDate("dia");
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            SituacaoContratoAnaliticoDWVO obj = new SituacaoContratoAnaliticoDWVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static SituacaoContratoAnaliticoDWVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        SituacaoContratoAnaliticoDWVO obj = new SituacaoContratoAnaliticoDWVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("Codigo")));
        obj.setDia(dadosSQL.getDate("Dia"));
        obj.setSituacao(dadosSQL.getString("Situacao"));
        obj.getEmpresa().setCodigo(new Integer(dadosSQL.getInt("Empresa")));
        obj.getPlano().setCodigo(new Integer(dadosSQL.getInt("Plano")));
        obj.getCliente().setCodigo(new Integer(dadosSQL.getInt("Cliente")));
        obj.getContrato().setCodigo(new Integer(dadosSQL.getInt("Contrato")));
        obj.setFoneCliente(dadosSQL.getString("FoneCliente"));
        obj.setEmailCliente(dadosSQL.getString("EmailCliente"));
        obj.setModalidadeCliente(dadosSQL.getString("ModalidadeCliente"));
        obj.setEnderecoCliente(dadosSQL.getString("EnderecoCliente"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosPlano(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        montarDadosCliente(obj, Uteis.NIVELMONTARDADOS_TODOS);
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_ROBO);

        return obj;
    }

    public static void montarDadosEmpresa(SituacaoContratoAnaliticoDWVO obj, int nivelMontarDados) throws Exception {
        if (obj.getEmpresa().getCodigo().intValue() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        obj.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public static void montarDadosPlano(SituacaoContratoAnaliticoDWVO obj, int nivelMontarDados) throws Exception {
        if (obj.getPlano().getCodigo().intValue() == 0) {
            obj.setPlano(new PlanoVO());
            return;
        }
        obj.setPlano(getFacade().getPlano().consultarPorChavePrimaria(obj.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public static void montarDadosCliente(SituacaoContratoAnaliticoDWVO obj, int nivelMontarDados) throws Exception {
        if (obj.getCliente().getCodigo().intValue() == 0) {
            obj.setCliente(new ClienteVO());
            return;
        }
        obj.setCliente(getFacade().getCliente().consultarPorChavePrimaria(obj.getCliente().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_TODOS));
    }

    public static void montarDadosContrato(SituacaoContratoAnaliticoDWVO obj, int nivelMontarDados) throws Exception {
        if (obj.getContrato().getCodigo().intValue() == 0) {
            obj.setContrato(new ContratoVO());
            obj.getContrato().setSituacaoContrato("");
            return;
        }
        obj.setContrato(getFacade().getContrato().consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
    }    

    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "sad" + File.separator + getIdEntidade() + "Rel" + ".jrxml");
    }

}


package relatorio.negocio.jdbc.sad;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import relatorio.negocio.comuns.sad.MetaCrescimentoVO;

/**
 *
 * <AUTHOR>
 */
public class MetaCrescimento extends SuperEntidade {   

    public MetaCrescimento() throws Exception {
        super();
        inicializar();        
    }

    public void incluir(MetaCrescimentoVO meta) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(meta);
            con.commit();
        } catch (Exception e) {
            meta.setNovoObj(new Boolean(true));
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(MetaCrescimentoVO meta) throws Exception {
        MetaCrescimentoVO.validarDados(meta);
        String sql = "INSERT INTO metacrescimento(totalinicial, metacrescimento, totalfinal, qtdeprevistosrenovar,"+
                     "iranterior, irinformado, totalrenovacoes, totalrenovacoesatrasadas, icvanterior, icvinformado, totalvendas,"+
                     "totalvisitas, icanceladosanterior, icanceladosinformado, totalcancelados, itrancadosanterior, itrancadosinformado,"+
                     "totaltrancados, ano, mes, empresa, dataedicao) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, meta.getTotalInicial());
        sqlInserir.setInt(2, meta.getMetaCrescimento());
        sqlInserir.setInt(3, meta.getTotalFinal());
        sqlInserir.setInt(4, meta.getQtdePrevistosRenovar());
        sqlInserir.setDouble(5, meta.getIRAnterior());
        sqlInserir.setDouble(6, meta.getIRInformado());
        sqlInserir.setInt(7, meta.getTotalRenovacoes());
        sqlInserir.setInt(8, meta.getTotalRenovacoesAtrasadas());
        sqlInserir.setDouble(9, meta.getICVAnterior());
        sqlInserir.setDouble(10, meta.getICVInformado());
        sqlInserir.setInt(11, meta.getTotalVendas());
        sqlInserir.setInt(12, meta.getTotalVisitas());
        sqlInserir.setDouble(13, meta.getICanceladosAnterior());
        sqlInserir.setDouble(14, meta.getICanceladosInformado());
        sqlInserir.setInt(15, meta.getTotalCancelados());
        sqlInserir.setDouble(16, meta.getITrancadosAnterior());
        sqlInserir.setDouble(17, meta.getITrancadosInformado());
        sqlInserir.setInt(18, meta.getTotalTrancados());
        sqlInserir.setInt(19, meta.getAno());
        sqlInserir.setInt(20, meta.getMes());
        sqlInserir.setInt(21, meta.getEmpresa().getCodigo());
        sqlInserir.setDate(22, Uteis.getSQLData(Calendario.hoje()));
        sqlInserir.execute();
        meta.setCodigo(obterValorChavePrimariaCodigo());
        meta.setNovoObj(false);
    }

    public void alterar(MetaCrescimentoVO meta) throws Exception {
        MetaCrescimentoVO.validarDados(meta);
        String sql = "UPDATE metacrescimento SET totalinicial=?, metacrescimento=?, totalfinal=?, qtdeprevistosrenovar=?,"+
                     "iranterior=?, irinformado=?, totalrenovacoes=?, totalrenovacoesatrasadas=?, icvanterior=?, " +
                     "icvinformado=?, totalvendas=?, totalvisitas=?, icanceladosanterior=?, icanceladosinformado=?, totalcancelados=?, " +
                     "itrancadosanterior=?, itrancadosinformado=?, totaltrancados=?, ano=?, mes=?, dataedicao=? WHERE codigo=? AND empresa=?";

        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, meta.getTotalInicial());
        sqlAlterar.setInt(2, meta.getMetaCrescimento());
        sqlAlterar.setInt(3, meta.getTotalFinal());
        sqlAlterar.setInt(4, meta.getQtdePrevistosRenovar());
        sqlAlterar.setDouble(5, meta.getIRAnterior());
        sqlAlterar.setDouble(6, meta.getIRInformado());
        sqlAlterar.setInt(7, meta.getTotalRenovacoes());
        sqlAlterar.setInt(8, meta.getTotalRenovacoesAtrasadas());
        sqlAlterar.setDouble(9, meta.getICVAnterior());
        sqlAlterar.setDouble(10, meta.getICVInformado());
        sqlAlterar.setInt(11, meta.getTotalVendas());
        sqlAlterar.setInt(12, meta.getTotalVisitas());
        sqlAlterar.setDouble(13, meta.getICanceladosAnterior());
        sqlAlterar.setDouble(14, meta.getICanceladosInformado());
        sqlAlterar.setInt(15, meta.getTotalCancelados());
        sqlAlterar.setDouble(16, meta.getITrancadosAnterior());
        sqlAlterar.setDouble(17, meta.getITrancadosInformado());
        sqlAlterar.setInt(18, meta.getTotalTrancados());
        sqlAlterar.setInt(19, meta.getAno());
        sqlAlterar.setInt(20, meta.getMes());
        sqlAlterar.setDate(21, Uteis.getSQLData(Calendario.hoje()));
        sqlAlterar.setInt(22, meta.getCodigo());
        sqlAlterar.setInt(23, meta.getEmpresa().getCodigo());
        sqlAlterar.execute();
        con.commit();
    }

     public void excluir(MetaCrescimentoVO meta) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(meta);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(MetaCrescimentoVO meta) throws Exception {
        String sql = "DELETE FROM metacrescimento WHERE codigo = ? AND empresa = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, meta.getCodigo());
        sqlExcluir.setInt(2, meta.getEmpresa().getCodigo());
        sqlExcluir.execute();
    }

    /**
     * consulta todas as metas de um determinado ano
     * @param ano
     * @return
     * @throws Exception
     */
    public List<MetaCrescimentoVO> consultarPorAno(EmpresaVO empresa, int ano) throws Exception {
        String sqlStr = "SELECT * FROM metacrescimento WHERE empresa = ? AND ano = ? ORDER BY ano, mes";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        sqlConsultar.setInt(1, empresa.getCodigo());
        sqlConsultar.setInt(2, ano);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado);
    }

    /**
     * consulta todas as metas de um determinado mes de todos os anos
     * @param ano
     * @param mes
     * @return
     * @throws Exception
     */
    public List<MetaCrescimentoVO> consultarPorMes(EmpresaVO empresa, int mes) throws Exception {
        String sqlStr = "SELECT * FROM metacrescimento WHERE empresa = ? AND mes = ? ORDER BY ano, mes";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        sqlConsultar.setInt(1, empresa.getCodigo());
        sqlConsultar.setInt(2, mes);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return montarDadosConsulta(tabelaResultado);
    }

    /**
     * consulta a meta de um determinado mes do ano
     * @param ano
     * @param mes
     * @return
     * @throws Exception
     */
    public MetaCrescimentoVO consultarPorAnoMes(EmpresaVO empresa, int ano, int mes) throws Exception {
        String sqlStr = "SELECT * FROM metacrescimento WHERE empresa = ? AND ano = ? AND mes = ? ORDER BY ano, mes";
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr);
        sqlConsultar.setInt(1, empresa.getCodigo());
        sqlConsultar.setInt(2, ano);
        sqlConsultar.setInt(3, mes);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if(tabelaResultado.next())
            return montarDados(tabelaResultado);
        else
            return new MetaCrescimentoVO();
    }

    public List<MetaCrescimentoVO> montarDadosConsulta(ResultSet tabelaResultado) throws Exception {
        List<MetaCrescimentoVO> resultado = new ArrayList<MetaCrescimentoVO>();
        while (tabelaResultado.next()) {
            MetaCrescimentoVO meta = new MetaCrescimentoVO();
            meta = montarDados(tabelaResultado);
            resultado.add(meta);
        }
        return resultado;
    }

    public MetaCrescimentoVO montarDados(ResultSet dadosSQL) throws Exception {
        MetaCrescimentoVO meta = new MetaCrescimentoVO();
        meta.setCodigo(dadosSQL.getInt("codigo"));
        meta.setAno(dadosSQL.getInt("ano"));
        meta.setMes(dadosSQL.getInt("mes"));
        meta.setTotalInicial(dadosSQL.getInt("totalinicial"));
        meta.setMetaCrescimento(dadosSQL.getInt("metacrescimento"));
        meta.setTotalFinal(dadosSQL.getInt("totalfinal"));
        meta.setQtdePrevistosRenovar(dadosSQL.getInt("qtdeprevistosrenovar"));
        meta.setIRAnterior(dadosSQL.getDouble("iranterior"));
        meta.setIRInformado(dadosSQL.getDouble("irinformado"));
        meta.setTotalRenovacoes(dadosSQL.getInt("totalrenovacoes"));
        meta.setTotalRenovacoesAtrasadas(dadosSQL.getInt("totalrenovacoesatrasadas"));
        meta.setICVAnterior(dadosSQL.getDouble("icvanterior"));
        meta.setICVInformado(dadosSQL.getDouble("icvinformado"));
        meta.setTotalVendas(dadosSQL.getInt("totalvendas"));
        meta.setTotalVisitas(dadosSQL.getInt("totalvisitas"));
        meta.setICanceladosAnterior(dadosSQL.getDouble("icanceladosanterior"));
        meta.setICanceladosInformado(dadosSQL.getDouble("icanceladosinformado"));
        meta.setTotalCancelados(dadosSQL.getInt("totalcancelados"));
        meta.setITrancadosAnterior(dadosSQL.getDouble("itrancadosanterior"));
        meta.setITrancadosInformado(dadosSQL.getDouble("itrancadosinformado"));
        meta.setTotalTrancados(dadosSQL.getInt("totaltrancados"));
        meta.setEmpresa(montarDadosEmpresa(dadosSQL.getInt("empresa")));
        meta.setNovoObj(false);
        return meta;
    }   

    public EmpresaVO montarDadosEmpresa(int codigo) throws Exception {
        if (codigo == 0)
            return new EmpresaVO();
        else
            return getFacade().getEmpresa().consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }
}
